ISO-10303-21;
HEADER;
FILE_DESCRIPTION(('ViewDefinition [CoordinationView]'),'2;1');
FILE_NAME('','2020-10-30T00:18:54',(),(),'IfcOpenShell 0.6.0b0','IfcOpenShell 0.6.0b0','');
FILE_SCHEMA(('IFC4'));
ENDSEC;
DATA;
#1=IFCPROJECT('3EasFN11P0zQRYKyp7G85J',#58,'HVAC Product Type Library Example','Demonstrates an air terminal type, which may be instantiated in buildings within referencing files.','ProductLibrary',$,$,(#59),#74);
#2=IFCAIRTERMINALTYPE('1FESQ2M9vC7xYWZpI_LlCh',#58,'Acme Diffuser 1234','Ceiling diffuser',$,(#47,#56),(#78),$,$,.DIFFUSER.);
#3=IFCDISTRIBUTIONPORT('0lCGSOGtb4mu56WSeYuzNg',#58,'Inlet',$,$,#81,$,.SINK.,.DUCT.,.AIRCONDITIONING.);
#4=IFCSIMPLEPROPERTYTEMPLATE('3_bLjRfq13tPEPPfI$6pRX',#58,'PortNumber','The port index for logically ordering the port within the containing element or element type.',.P_SINGLEVALUE.,'IfcInteger',$,$,$,$,$,.READWRITE.);
#5=IFCSIMPLEPROPERTYTEMPLATE('2VDgHzk35At81oM56a3xnn',#58,'ColorCode','Name of a color for identifying the connector, if applicable.',.P_SINGLEVALUE.,'IfcLabel',$,$,$,$,$,.READWRITE.);
#6=IFCPROPERTYSETTEMPLATE('29WBDBI9L3tgEAg5Ay2Kax',#58,'Pset_DistributionPortCommon','Common attributes attached to an instance of IfcDistributionPort.',.PSET_OCCURRENCEDRIVEN.,'IfcDistributionPort',(#4,#5));
#7=IFCPROJECTLIBRARY('1rfo$z7PbCaeLf5SOGGJV5',#58,'IFC4',$,$,$,$,$,$);
#8=IFCPROPERTYSET('3MdHsbDH1BrQYI$kjaLN5a',#58,'Pset_DistributionPortCommon','Common attributes attached to an instance of IfcDistributionPort.',$);
#9=IFCSIMPLEPROPERTYTEMPLATE('3$fSJrLkP9WesgtUW7tvY9',#58,'ConnectionType','The end-style treatment of the duct port:\X2\000A000A\X0\BEADEDSLEEVE: Beaded Sleeve. \X2\000A\X0\COMPRESSION: Compression. \X2\000A\X0\CRIMP: Crimp. \X2\000A\X0\DRAWBAND: Drawband. \X2\000A\X0\DRIVESLIP: Drive slip. \X2\000A\X0\FLANGED: Flanged. \X2\000A\X0\OUTSIDESLEEVE: Outside Sleeve. \X2\000A\X0\SLIPON: Slipon. \X2\000A\X0\SOLDERED: Soldered. \X2\000A\X0\SSLIP: S-Slip. \X2\000A\X0\STANDINGSEAM: Standing seam. \X2\000A\X0\SWEDGE: Swedge. \X2\000A\X0\WELDED: Welded. \X2\000A\X0\OTHER: Another type of end-style has been applied.\X2\000A\X0\NONE: No end-style has been applied.',.P_ENUMERATEDVALUE.,'IfcLabel',$,#84,$,$,$,.READWRITE.);
#10=IFCSIMPLEPROPERTYTEMPLATE('3eYrOWOML0MAWniKzD$bg8',#58,'ConnectionSubType','The physical port connection subtype that further qualifies the ConnectionType.',.P_SINGLEVALUE.,'IfcLabel',$,$,$,$,$,.READWRITE.);
#11=IFCSIMPLEPROPERTYTEMPLATE('3U0yI_myr9Tuk86rnglnUq',#58,'NominalWidth','The nominal width or diameter of the duct connection.',.P_SINGLEVALUE.,'IfcPositiveLengthMeasure',$,$,$,$,$,.READWRITE.);
#12=IFCSIMPLEPROPERTYTEMPLATE('1T7T2i39H9OfTY1TIerMbJ',#58,'NominalHeight','The nominal height of the duct connection.',.P_SINGLEVALUE.,'IfcPositiveLengthMeasure',$,$,$,$,$,.READWRITE.);
#13=IFCSIMPLEPROPERTYTEMPLATE('3ASt$O5yD8NeM$q_lQyHWd',#58,'DryBulbTemperature','Dry bulb temperature of the air.',.P_BOUNDEDVALUE.,'IfcThermodynamicTemperatureMeasure',$,$,$,$,$,.READWRITE.);
#14=IFCSIMPLEPROPERTYTEMPLATE('1tms9JUcX85eqcKjr4hn0X',#58,'WetBulbTemperature','Wet bulb temperature of the air.',.P_BOUNDEDVALUE.,'IfcThermodynamicTemperatureMeasure',$,$,$,$,$,.READWRITE.);
#15=IFCSIMPLEPROPERTYTEMPLATE('0oDnPia3b949zQ9IOqQ3WD',#58,'VolumetricFlowRate','The volumetric flow rate of the fluid.',.P_BOUNDEDVALUE.,'IfcVolumetricFlowRateMeasure',$,$,$,$,$,.READWRITE.);
#16=IFCSIMPLEPROPERTYTEMPLATE('0Y$o5$IVnCBBYMkfqCX9u2',#58,'Velocity','The velocity of the fluid.',.P_BOUNDEDVALUE.,'IfcLinearVelocityMeasure',$,$,$,$,$,.READWRITE.);
#17=IFCSIMPLEPROPERTYTEMPLATE('0r4zhLdSb4$QDqtTZAlksU',#58,'Pressure','The pressure of the fluid.',.P_BOUNDEDVALUE.,'IfcPressureMeasure',$,$,$,$,$,.READWRITE.);
#18=IFCPROPERTYSETTEMPLATE('1A6ROK3ijAv94HqL2QeE5V',#58,'Pset_DistributionPortTypeAirConditioning','Duct port occurrence attributes attached to an instance of IfcDistributionPort.',.PSET_OCCURRENCEDRIVEN.,'IfcDistributionPort/AIRCONDITIONING',(#9,#10,#11,#12,#13,#14,#15,#16,#17));
#19=IFCPROPERTYSET('142LVYnPP62figC7tUlZnT',#58,'Pset_DistributionPortTypeAirConditioning',$,(#85,#87,#88));
#20=IFCSIMPLEPROPERTYTEMPLATE('3fpWm82nz9hQ6TKg7VFU19',#58,'Reference','Reference ID for this specified type in this project (e.g. type ''A-1''), provided, if there is no classification reference to a recognized classification system used.',.P_SINGLEVALUE.,'IfcIdentifier',$,$,$,$,$,.READWRITE.);
#21=IFCSIMPLEPROPERTYTEMPLATE('0Yj8hLD1HER8_kCs5XavrF',#58,'Status','Status of the element, predominately used in renovation or retrofitting projects. The status can be assigned to as "New" - element designed as new addition, "Existing" - element exists and remains, "Demolish" - element existed but is to be demolished,  "Temporary" - element will exists only temporary (like a temporary support structure).',.P_ENUMERATEDVALUE.,'IfcLabel',$,#89,$,$,$,.READWRITE.);
#22=IFCSIMPLEPROPERTYTEMPLATE('0s3HOiBZz5HuKtBnUthHVn',#58,'Shape','Shape of the air terminal. Slot is typically a long narrow supply device with an aspect ratio generally greater than 10 to 1.',.P_ENUMERATEDVALUE.,'IfcLabel',$,#90,$,$,$,.READWRITE.);
#23=IFCSIMPLEPROPERTYTEMPLATE('3ccEnz8CbDehYY5UfGjNhk',#58,'FaceType','Identifies how the terminal face of an AirTerminal is constructed.',.P_ENUMERATEDVALUE.,'IfcLabel',$,#91,$,$,$,.READWRITE.);
#24=IFCSIMPLEPROPERTYTEMPLATE('3s2OInnMzFHQs$K3elh_3h',#58,'SlotWidth','Slot width.',.P_SINGLEVALUE.,'IfcPositiveLengthMeasure',$,$,$,$,$,.READWRITE.);
#25=IFCSIMPLEPROPERTYTEMPLATE('1y1kHB7PrChPwTgQ$ECHb$',#58,'SlotLength','Slot length.',.P_SINGLEVALUE.,'IfcPositiveLengthMeasure',$,$,$,$,$,.READWRITE.);
#26=IFCSIMPLEPROPERTYTEMPLATE('1U0eyN6b902eI0MbwdUd8M',#58,'NumberOfSlots','Number of slots.',.P_SINGLEVALUE.,'IfcInteger',$,$,$,$,$,.READWRITE.);
#27=IFCSIMPLEPROPERTYTEMPLATE('0y7Fb2v4H9ehCvfO24PHM1',#58,'FlowPattern','Flow pattern.',.P_ENUMERATEDVALUE.,'IfcLabel',$,#92,$,$,$,.READWRITE.);
#28=IFCSIMPLEPROPERTYTEMPLATE('0rg225Jsz7aPmR4rqHJHRR',#58,'AirFlowrateRange','Air flowrate range within which the air terminal is designed to operate.',.P_BOUNDEDVALUE.,'IfcVolumetricFlowRateMeasure',$,$,$,$,$,.READWRITE.);
#29=IFCSIMPLEPROPERTYTEMPLATE('1IJN6qN3j68w9NjQIASYJF',#58,'TemperatureRange','Temperature range within which the air terminal is designed to operate.',.P_BOUNDEDVALUE.,'IfcThermodynamicTemperatureMeasure',$,$,$,$,$,.READWRITE.);
#30=IFCSIMPLEPROPERTYTEMPLATE('3YNHGt1l51jg4X9QGpz17j',#58,'DischargeDirection','Discharge direction of the air terminal.\X2\000A000A\X0\Parallel: discharges parallel to mounting surface designed so that flow attaches to the surface.\X2\000A\X0\Perpendicular:  discharges away from mounting surface.\X2\000A\X0\Adjustable: both parallel and perpendicular discharge.',.P_ENUMERATEDVALUE.,'IfcLabel',$,#93,$,$,$,.READWRITE.);
#31=IFCSIMPLEPROPERTYTEMPLATE('15AjYKuPv9lQ9iggUpGAaF',#58,'ThrowLength','The horizontal or vertical axial distance an airstream travels after leaving an AirTerminal before the maximum stream velocity is reduced to a specified terminal velocity under isothermal conditions at the upper value of the AirFlowrateRange.',.P_SINGLEVALUE.,'IfcLengthMeasure',$,$,$,$,$,.READWRITE.);
#32=IFCSIMPLEPROPERTYTEMPLATE('0JBeud9tT5RgwbQACxzdZH',#58,'AirDiffusionPerformanceIndex','The Air Diffusion Performance Index (ADPI) is used for cooling mode conditions. If several measurements of air velocity and air temperature are made throughout the occupied zone of a space, the ADPI is the percentage of locations where measurements were taken that meet the specifications for effective draft temperature and air velocity.',.P_SINGLEVALUE.,'IfcReal',$,$,$,$,$,.READWRITE.);
#33=IFCSIMPLEPROPERTYTEMPLATE('1rGAtIuLj049KjoAX5lv0j',#58,'FinishType','The type of finish for the air terminal.',.P_ENUMERATEDVALUE.,'IfcLabel',$,#94,$,$,$,.READWRITE.);
#34=IFCSIMPLEPROPERTYTEMPLATE('0Lx74WLuDAJBe29ZAokn7n',#58,'FinishColor','The finish color for the air terminal.',.P_SINGLEVALUE.,'IfcLabel',$,$,$,$,$,.READWRITE.);
#35=IFCSIMPLEPROPERTYTEMPLATE('28DA3rBGD45wIHAjVhRVfn',#58,'MountingType','The way the air terminal is mounted to the ceiling, wall, etc.\X2\000A000A\X0\Surface: mounted to the surface of something (e.g., wall, duct, etc.).\X2\000A\X0\Flat flush: mounted flat and flush with a surface.\X2\000A\X0\Lay-in: mounted in a lay-in type ceiling (e.g., a dropped ceiling grid).',.P_ENUMERATEDVALUE.,'IfcLabel',$,#95,$,$,$,.READWRITE.);
#36=IFCSIMPLEPROPERTYTEMPLATE('2BHDN$CwT3oPiLEOzMDEo6',#58,'CoreType','Identifies the way the core of the AirTerminal is constructed.',.P_ENUMERATEDVALUE.,'IfcLabel',$,#96,$,$,$,.READWRITE.);
#37=IFCSIMPLEPROPERTYTEMPLATE('3ivegLDDDF4Bj8eqOSTpHI',#58,'CoreSetHorizontal','Degree of horizontal (in the X-axis of the LocalPlacement) blade set from the centerline.',.P_SINGLEVALUE.,'IfcPlaneAngleMeasure',$,$,$,$,$,.READWRITE.);
#38=IFCSIMPLEPROPERTYTEMPLATE('0lGnkfyAL1Yw4lbl7z2VvE',#58,'CoreSetVertical','Degree of vertical (in the Y-axis of the LocalPlacement) blade set from the centerline.',.P_SINGLEVALUE.,'IfcPlaneAngleMeasure',$,$,$,$,$,.READWRITE.);
#39=IFCSIMPLEPROPERTYTEMPLATE('2z_gncAVT79OxwFBa0hkvU',#58,'HasIntegralControl','If TRUE, a self powered temperature control is included in the AirTerminal.',.P_SINGLEVALUE.,'IfcBoolean',$,$,$,$,$,.READWRITE.);
#40=IFCSIMPLEPROPERTYTEMPLATE('3M$NkRjRbFrvWwcY1izFe4',#58,'FlowControlType','Type of flow control element that may be included as a part of the construction of the air terminal.',.P_ENUMERATEDVALUE.,'IfcLabel',$,#97,$,$,$,.READWRITE.);
#41=IFCSIMPLEPROPERTYTEMPLATE('1hMma$QwL4QOtgcEGSVoYE',#58,'HasSoundAttenuator','If TRUE, the air terminal has sound attenuation.',.P_SINGLEVALUE.,'IfcBoolean',$,$,$,$,$,.READWRITE.);
#42=IFCSIMPLEPROPERTYTEMPLATE('1KWi8MdX54FxZUOwumhTef',#58,'HasThermalInsulation','If TRUE, the air terminal has thermal insulation.',.P_SINGLEVALUE.,'IfcBoolean',$,$,$,$,$,.READWRITE.);
#43=IFCSIMPLEPROPERTYTEMPLATE('2dgLXZEs900vztGBIr8_ao',#58,'NeckArea','Neck area of the air terminal.',.P_SINGLEVALUE.,'IfcAreaMeasure',$,$,$,$,$,.READWRITE.);
#44=IFCSIMPLEPROPERTYTEMPLATE('309SGtOAPE9929AbT6FTuV',#58,'EffectiveArea','Effective discharge area of the air terminal.',.P_SINGLEVALUE.,'IfcAreaMeasure',$,$,$,$,$,.READWRITE.);
#45=IFCSIMPLEPROPERTYTEMPLATE('2aOlIhtEn1yBBCqpuP6ygt',#58,'AirFlowrateVersusFlowControlElement','Air flowrate versus flow control element position at nominal pressure drop.',.P_TABLEVALUE.,'IfcVolumetricFlowRateMeasure','IfcPositiveRatioMeasure',$,$,$,$,.READWRITE.);
#46=IFCPROPERTYSETTEMPLATE('2jTsg4Mlz1weXQWo2pv0Qb',#58,'Pset_AirTerminalTypeCommon','Air terminal type common attributes.\X2\000A\X0\SoundLevel attribute deleted in IFC2x2 Pset Addendum: Use IfcSoundProperties instead.',.PSET_TYPEDRIVENOVERRIDE.,'IfcAirTerminal',(#20,#21,#22,#23,#24,#25,#26,#27,#28,#29,#30,#31,#32,#33,#34,#35,#36,#37,#38,#39,#40,#41,#42,#43,#44,#45));
#47=IFCPROPERTYSET('34RBPXI3v1B9OyUNo6YREP',#58,'Pset_AirTerminalTypeCommon',$,(#98));
#48=IFCSIMPLEPROPERTYTEMPLATE('3eHmy4aE566RLnsXQnc182',#58,'GlobalTradeItemNumber','The Global Trade Item Number (GTIN) is an identifier for trade items developed by GS1 (www.gs1.org).',.P_SINGLEVALUE.,'IfcIdentifier',$,$,$,$,$,.READWRITE.);
#49=IFCSIMPLEPROPERTYTEMPLATE('1sgvdetjb1JxEqbzkc2sqC',#58,'ArticleNumber','Article number or reference that is be applied to a configured product according to a standard scheme for article number definition as defined by the manufacturer. It is often used as the purchasing number.',.P_SINGLEVALUE.,'IfcIdentifier',$,$,$,$,$,.READWRITE.);
#50=IFCSIMPLEPROPERTYTEMPLATE('3wqp__MIv1H9F7bsX0g1Pv',#58,'ModelReference','The model number or designator of the product model (or product line) as assigned by the manufacturer of the manufactured item.',.P_SINGLEVALUE.,'IfcLabel',$,$,$,$,$,.READWRITE.);
#51=IFCSIMPLEPROPERTYTEMPLATE('0ZGAMw3wDEvQK1eN0ABad0',#58,'ModelLabel','The descriptive model name of the product model (or product line) as assigned by the manufacturer of the manufactured item.',.P_SINGLEVALUE.,'IfcLabel',$,$,$,$,$,.READWRITE.);
#52=IFCSIMPLEPROPERTYTEMPLATE('0SjOR_Aiz1F9zib80mPIr0',#58,'Manufacturer','The organization that manufactured and/or assembled the item.',.P_SINGLEVALUE.,'IfcLabel',$,$,$,$,$,.READWRITE.);
#53=IFCSIMPLEPROPERTYTEMPLATE('2zE_DR$F52WAh8dBauU7vt',#58,'ProductionYear','The year of production of the manufactured item.',.P_SINGLEVALUE.,'IfcLabel',$,$,$,$,$,.READWRITE.);
#54=IFCSIMPLEPROPERTYTEMPLATE('0T7c_L8b9E$9V2KslgB9xe',#58,'AssemblyPlace','Enumeration defining where the assembly is intended to take place, either in a factory or on the building site.',.P_ENUMERATEDVALUE.,'IfcLabel',$,#100,$,$,$,.READWRITE.);
#55=IFCPROPERTYSETTEMPLATE('0wwkiIwGz0lf2YIWw4Kdjj',#58,'Pset_ManufacturerTypeInformation','Defines characteristics of types (ranges) of manufactured products that may be given by the manufacturer. Note that the term ''manufactured'' may also be used to refer to products that are supplied and identified by the supplier or that are assembled off site by a third party provider. \X2\000A\X0\HISTORY: This property set replaces the entity IfcManufacturerInformation from previous IFC releases. IFC 2x4: AssemblyPlace property added.',.PSET_TYPEDRIVENOVERRIDE.,'IfcElement',(#48,#49,#50,#51,#52,#53,#54));
#56=IFCPROPERTYSET('2LM02K5dbFiBVTpfUgOnl4',#58,'Pset_ManufacturerTypeInformation',$,(#101,#102,#103,#104,#105));
#57=IFCSHAPEREPRESENTATION(#59,'Body','AdvancedSweptSolid',(#107));
#58=IFCOWNERHISTORY(#116,#119,.READWRITE.,.NOTDEFINED.,$,$,$,**********);
#59=IFCGEOMETRICREPRESENTATIONCONTEXT('3D','Model',3,1.E-05,#121,$);
#60=IFCRELDECLARES('0$b_yyn8PEbAVbeTOu2ljb',#58,$,$,#1,(#2,#7));
#61=IFCRELNESTS('1yPSy1NEzAsQ3rkI$pYuin',#58,$,$,#2,(#3));
#62=IFCRELASSOCIATESLIBRARY('2AvhQDLy558AFVeANPXoWB',#58,$,$,(#7),#123);
#63=IFCRELASSOCIATESLIBRARY('24PRN3YsXAVP3fEgb4zpZM',#58,$,$,(#6),#125);
#64=IFCRELDECLARES('0Xl$PCnZPB2BUMJcj$F_RN',#58,'PROJECT',$,#7,(#6,#18,#46,#55));
#65=IFCRELDEFINESBYTEMPLATE('2DA69vfj12BPHrvbOJli4C',#58,$,$,(#8),#6);
#66=IFCRELDEFINESBYPROPERTIES('0eTnqsHaH63xaP$yFWdQxt',#58,'Pset_DistributionPortCommon',$,(#3),#8);
#67=IFCRELASSOCIATESLIBRARY('2DqAITf693l8NzYzj5r79c',#58,$,$,(#18),#128);
#68=IFCRELDEFINESBYTEMPLATE('2rvy24e5L04OIpKBIyaF5s',#58,$,$,(#19),#18);
#69=IFCRELDEFINESBYPROPERTIES('2Phx4Cdqz3XRbcSidvQspJ',#58,'Pset_DistributionPortTypeAirConditioning',$,(#3),#19);
#70=IFCRELASSOCIATESLIBRARY('259LjdEyzC5gc5NKIzDXj4',#58,$,$,(#46),#131);
#71=IFCRELDEFINESBYTEMPLATE('2MnMFQk5T8PfDt4jNfTD0y',#58,$,$,(#47),#46);
#72=IFCRELASSOCIATESLIBRARY('39XKqfEPrEmwCmJTAn1TLk',#58,$,$,(#55),#134);
#73=IFCRELDEFINESBYTEMPLATE('1gZFqTeWDCiPFdVEDktMyc',#58,$,$,(#56),#55);
#74=IFCUNITASSIGNMENT((#75));
#75=IFCCONVERSIONBASEDUNIT($,.LENGTHUNIT.,'inch',#76);
#76=IFCMEASUREWITHUNIT(IFCLENGTHMEASURE(0.0254),#77);
#77=IFCSIUNIT(*,.LENGTHUNIT.,$,.METRE.);
#78=IFCREPRESENTATIONMAP(#79,#57);
#79=IFCAXIS2PLACEMENT3D(#80,$,$);
#80=IFCCARTESIANPOINT((0.,0.,0.));
#81=IFCLOCALPLACEMENT($,#82);
#82=IFCAXIS2PLACEMENT3D(#83,$,$);
#83=IFCCARTESIANPOINT((12.,12.,4.));
#84=IFCPROPERTYENUMERATION('PEnum_DuctConnectionType',(IFCLABEL('BEADEDSLEEVE'),IFCLABEL('COMPRESSION'),IFCLABEL('CRIMP'),IFCLABEL('DRAWBAND'),IFCLABEL('DRIVESLIP'),IFCLABEL('FLANGED'),IFCLABEL('OUTSIDESLEEVE'),IFCLABEL('SLIPON'),IFCLABEL('SOLDERED'),IFCLABEL('SSLIP'),IFCLABEL('STANDINGSEAM'),IFCLABEL('SWEDGE'),IFCLABEL('WELDED'),IFCLABEL('OTHER'),IFCLABEL('NONE'),IFCLABEL('USERDEFINED'),IFCLABEL('NOTDEFINED')),$);
#85=IFCPROPERTYENUMERATEDVALUE('ConnectionType',$,(IFCLABEL('OUTSIDESLEEVE')),#86);
#86=IFCPROPERTYENUMERATION('PEnum_DuctConnectionType',(IFCLABEL('BEADEDSLEEVE'),IFCLABEL('COMPRESSION'),IFCLABEL('CRIMP'),IFCLABEL('DRAWBAND'),IFCLABEL('DRIVESLIP'),IFCLABEL('FLANGED'),IFCLABEL('OUTSIDESLEEVE'),IFCLABEL('SLIPON'),IFCLABEL('SOLDERED'),IFCLABEL('SSLIP'),IFCLABEL('STANDINGSEAM'),IFCLABEL('SWEDGE'),IFCLABEL('WELDED'),IFCLABEL('OTHER'),IFCLABEL('NONE'),IFCLABEL('USERDEFINED'),IFCLABEL('NOTDEFINED')),$);
#87=IFCPROPERTYSINGLEVALUE('NominalWidth',$,IFCPOSITIVELENGTHMEASURE(12.),$);
#88=IFCPROPERTYSINGLEVALUE('NominalHeight',$,IFCPOSITIVELENGTHMEASURE(12.),$);
#89=IFCPROPERTYENUMERATION('PEnum_Status',(IFCLABEL('NEW'),IFCLABEL('EXISTING'),IFCLABEL('DEMOLISH'),IFCLABEL('TEMPORARY'),IFCLABEL('OTHER'),IFCLABEL('NOTKNOWN'),IFCLABEL('UNSET')),$);
#90=IFCPROPERTYENUMERATION('PEnum_AirTerminalShape',(IFCLABEL('ROUND'),IFCLABEL('RECTANGULAR'),IFCLABEL('SQUARE'),IFCLABEL('SLOT'),IFCLABEL('OTHER'),IFCLABEL('NOTKNOWN'),IFCLABEL('UNSET')),$);
#91=IFCPROPERTYENUMERATION('PEnum_AirTerminalFaceType',(IFCLABEL('FOURWAYPATTERN'),IFCLABEL('SINGLEDEFLECTION'),IFCLABEL('DOUBLEDEFLECTION'),IFCLABEL('SIGHTPROOF'),IFCLABEL('EGGCRATE'),IFCLABEL('PERFORATED'),IFCLABEL('LOUVERED'),IFCLABEL('OTHER'),IFCLABEL('NOTKNOWN'),IFCLABEL('UNSET')),$);
#92=IFCPROPERTYENUMERATION('PEnum_AirTerminalFlowPattern',(IFCLABEL('LINEARSINGLE'),IFCLABEL('LINEARDOUBLE'),IFCLABEL('LINEARFOURWAY'),IFCLABEL('RADIAL'),IFCLABEL('SWIRL'),IFCLABEL('DISPLACMENT'),IFCLABEL('COMPACTJET'),IFCLABEL('OTHER'),IFCLABEL('NOTKNOWN'),IFCLABEL('UNSET')),$);
#93=IFCPROPERTYENUMERATION('PEnum_AirTerminalDischargeDirection',(IFCLABEL('PARALLEL'),IFCLABEL('PERPENDICULAR'),IFCLABEL('ADJUSTABLE'),IFCLABEL('OTHER'),IFCLABEL('NOTKNOWN'),IFCLABEL('UNSET')),$);
#94=IFCPROPERTYENUMERATION('PEnum_AirTerminalFinishType',(IFCLABEL('ANNODIZED'),IFCLABEL('PAINTED'),IFCLABEL('NONE'),IFCLABEL('OTHER'),IFCLABEL('NOTKNOWN'),IFCLABEL('UNSET')),$);
#95=IFCPROPERTYENUMERATION('PEnum_AirTerminalMountingType',(IFCLABEL('SURFACE'),IFCLABEL('FLATFLUSH'),IFCLABEL('LAYIN'),IFCLABEL('OTHER'),IFCLABEL('NOTKNOWN'),IFCLABEL('UNSET')),$);
#96=IFCPROPERTYENUMERATION('PEnum_AirTerminalCoreType',(IFCLABEL('SHUTTERBLADE'),IFCLABEL('CURVEDBLADE'),IFCLABEL('REMOVABLE'),IFCLABEL('REVERSIBLE'),IFCLABEL('NONE'),IFCLABEL('OTHER'),IFCLABEL('NOTKNOWN'),IFCLABEL('UNSET')),$);
#97=IFCPROPERTYENUMERATION('PEnum_AirTerminalFlowControlType',(IFCLABEL('DAMPER'),IFCLABEL('BELLOWS'),IFCLABEL('NONE'),IFCLABEL('OTHER'),IFCLABEL('NOTKNOWN'),IFCLABEL('UNSET')),$);
#98=IFCPROPERTYENUMERATEDVALUE('Shape',$,(IFCLABEL('SQUARE')),#99);
#99=IFCPROPERTYENUMERATION('PEnum_AirTerminalShape',(IFCLABEL('ROUND'),IFCLABEL('RECTANGULAR'),IFCLABEL('SQUARE'),IFCLABEL('SLOT'),IFCLABEL('OTHER'),IFCLABEL('NOTKNOWN'),IFCLABEL('UNSET')),$);
#100=IFCPROPERTYENUMERATION('PEnum_AssemblyPlace',(IFCLABEL('FACTORY'),IFCLABEL('OFFSITE'),IFCLABEL('SITE'),IFCLABEL('OTHER'),IFCLABEL('NOTKNOWN'),IFCLABEL('UNSET')),$);
#101=IFCPROPERTYSINGLEVALUE('ModelReference',$,IFCLABEL('1234'),$);
#102=IFCPROPERTYSINGLEVALUE('ModelLabel',$,IFCLABEL('Ceiling Diffuser'),$);
#103=IFCPROPERTYSINGLEVALUE('Manufacturer',$,IFCLABEL('Acme'),$);
#104=IFCPROPERTYSINGLEVALUE('ProductionYear',$,IFCLABEL('2011'),$);
#105=IFCPROPERTYENUMERATEDVALUE('AssemblyPlace',$,(IFCLABEL('FACTORY')),#106);
#106=IFCPROPERTYENUMERATION('PEnum_AssemblyPlace',(IFCLABEL('FACTORY'),IFCLABEL('OFFSITE'),IFCLABEL('SITE'),IFCLABEL('OTHER'),IFCLABEL('NOTKNOWN'),IFCLABEL('UNSET')),$);
#107=IFCEXTRUDEDAREASOLIDTAPERED(#108,#109,#111,4.,#112);
#108=IFCRECTANGLEHOLLOWPROFILEDEF(.AREA.,$,$,24.,24.,2.,10.,0.);
#109=IFCAXIS2PLACEMENT3D(#110,$,$);
#110=IFCCARTESIANPOINT((12.,12.,0.));
#111=IFCDIRECTION((0.,0.,1.));
#112=IFCDERIVEDPROFILEDEF(.AREA.,$,#113,#114,$);
#113=IFCRECTANGLEHOLLOWPROFILEDEF(.AREA.,$,$,24.,24.,2.,10.,0.);
#114=IFCCARTESIANTRANSFORMATIONOPERATOR2D($,$,#115,0.5);
#115=IFCCARTESIANPOINT((0.,0.));
#116=IFCPERSONANDORGANIZATION(#117,#118,$);
#117=IFCPERSON('Tim',$,$,$,$,$,$,$);
#118=IFCORGANIZATION($,'Tim-PC',$,$,$);
#119=IFCAPPLICATION(#120,'0.9.1','Constructivity','CONSTRUCTIVITY');
#120=IFCORGANIZATION($,'Constructivity.com LLC',$,$,$);
#121=IFCAXIS2PLACEMENT3D(#122,$,$);
#122=IFCCARTESIANPOINT((0.,0.,0.));
#123=IFCLIBRARYINFORMATION('IFC4',$,#124,'2011-09-26T20:52:24','http://buildingsmart-tech.org/ifc/IFC2x4/rc3/html/annex/annex-a/ifc2x4_rc3-templates.ifc',$);
#124=IFCORGANIZATION($,'Tim-PC',$,$,$);
#125=IFCLIBRARYREFERENCE($,'Pset_DistributionPortCommon','Pset_DistributionPortCommon',$,$,#126);
#126=IFCLIBRARYINFORMATION('IFC4',$,#127,'2011-09-26T20:52:24','http://buildingsmart-tech.org/ifc/IFC2x4/rc3/html/annex/annex-a/ifc2x4_rc3-templates.ifc',$);
#127=IFCORGANIZATION($,'Tim-PC',$,$,$);
#128=IFCLIBRARYREFERENCE($,'Pset_DistributionPortTypeAirConditioning','Pset_DistributionPortTypeAirConditioning',$,$,#129);
#129=IFCLIBRARYINFORMATION('IFC4',$,#130,'2011-09-26T20:52:24','http://buildingsmart-tech.org/ifc/IFC2x4/rc3/html/annex/annex-a/ifc2x4_rc3-templates.ifc',$);
#130=IFCORGANIZATION($,'Tim-PC',$,$,$);
#131=IFCLIBRARYREFERENCE($,'Pset_AirTerminalTypeCommon','Pset_AirTerminalTypeCommon',$,$,#132);
#132=IFCLIBRARYINFORMATION('IFC4',$,#133,'2011-09-26T20:52:24','http://buildingsmart-tech.org/ifc/IFC2x4/rc3/html/annex/annex-a/ifc2x4_rc3-templates.ifc',$);
#133=IFCORGANIZATION($,'Tim-PC',$,$,$);
#134=IFCLIBRARYREFERENCE($,'Pset_ManufacturerTypeInformation','Pset_ManufacturerTypeInformation',$,$,#135);
#135=IFCLIBRARYINFORMATION('IFC4',$,#136,'2011-09-26T20:52:24','http://buildingsmart-tech.org/ifc/IFC2x4/rc3/html/annex/annex-a/ifc2x4_rc3-templates.ifc',$);
#136=IFCORGANIZATION($,'Tim-PC',$,$,$);
ENDSEC;
END-ISO-10303-21;

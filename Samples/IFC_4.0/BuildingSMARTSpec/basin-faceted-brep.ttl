# baseURI: http://linkedbuildingdata.net/ifc/resources20200624_184152/
# imports: http://standards.buildingsmart.org/IFC/DEV/IFC4/ADD1/OWL

@prefix ifc:  <http://standards.buildingsmart.org/IFC/DEV/IFC4/ADD1/OWL#> .
@prefix inst:  <http://linkedbuildingdata.net/ifc/resources20200624_184152/> .
@prefix list:  <https://w3id.org/list#> .
@prefix express:  <https://w3id.org/express#> .
@prefix rdf:  <http://www.w3.org/1999/02/22-rdf-syntax-ns#> .
@prefix xsd:  <http://www.w3.org/2001/XMLSchema#> .
@prefix owl:  <http://www.w3.org/2002/07/owl#> .

inst:   rdf:type     owl:Ontology ;
        owl:imports  ifc: .

inst:IfcApplication_1
        rdf:type  ifc:IfcApplication .

inst:IfcOrganization_2
        rdf:type  ifc:IfcOrganization .

inst:IfcApplication_1
        ifc:applicationDeveloper_IfcApplication  inst:IfcOrganization_2 .

inst:IfcLabel_697  rdf:type  ifc:IfcLabel ;
        express:hasString  "1.0.0.0" .

inst:IfcApplication_1
        ifc:version_IfcApplication  inst:IfcLabel_697 .

inst:IfcLabel_698  rdf:type  ifc:IfcLabel ;
        express:hasString  "ggRhinoIFC - Geometry Gym Plug-in for Rhino3d" .

inst:IfcApplication_1
        ifc:applicationFullName_IfcApplication  inst:IfcLabel_698 .

inst:IfcIdentifier_699
        rdf:type           ifc:IfcIdentifier ;
        express:hasString  "ggRhinoIFC" .

inst:IfcApplication_1
        ifc:applicationIdentifier_IfcApplication  inst:IfcIdentifier_699 .

inst:IfcLabel_700  rdf:type  ifc:IfcLabel ;
        express:hasString  "Geometry Gym Pty Ltd" .

inst:IfcOrganization_2
        ifc:name_IfcOrganization  inst:IfcLabel_700 .

inst:IfcPersonAndOrganization_3
        rdf:type  ifc:IfcPersonAndOrganization .

inst:IfcPerson_4  rdf:type  ifc:IfcPerson .

inst:IfcPersonAndOrganization_3
        ifc:thePerson_IfcPersonAndOrganization  inst:IfcPerson_4 ;
        ifc:theOrganization_IfcPersonAndOrganization  inst:IfcOrganization_2 .

inst:IfcIdentifier_701
        rdf:type           ifc:IfcIdentifier ;
        express:hasString  "Jon" .

inst:IfcPerson_4  ifc:identification_IfcPerson  inst:IfcIdentifier_701 ;
        ifc:familyName_IfcPerson      inst:IfcIdentifier_701 .

inst:IfcOwnerHistory_6
        rdf:type                        ifc:IfcOwnerHistory ;
        ifc:owningUser_IfcOwnerHistory  inst:IfcPersonAndOrganization_3 ;
        ifc:owningApplication_IfcOwnerHistory  inst:IfcApplication_1 ;
        ifc:changeAction_IfcOwnerHistory  ifc:ADDED .

inst:IfcTimeStamp_702
        rdf:type            ifc:IfcTimeStamp ;
        express:hasInteger  1418084875 .

inst:IfcOwnerHistory_6
        ifc:lastModifiedDate_IfcOwnerHistory  inst:IfcTimeStamp_702 ;
        ifc:creationDate_IfcOwnerHistory  inst:IfcTimeStamp_702 .

inst:IfcGeometricRepresentationContext_7
        rdf:type  ifc:IfcGeometricRepresentationContext .

inst:IfcLabel_703  rdf:type  ifc:IfcLabel ;
        express:hasString  "Model" .

inst:IfcGeometricRepresentationContext_7
        ifc:contextType_IfcRepresentationContext  inst:IfcLabel_703 .

inst:IfcDimensionCount_704
        rdf:type            ifc:IfcDimensionCount ;
        express:hasInteger  3 .

inst:IfcGeometricRepresentationContext_7
        ifc:coordinateSpaceDimension_IfcGeometricRepresentationContext  inst:IfcDimensionCount_704 .

inst:IfcReal_705  rdf:type  ifc:IfcReal ;
        express:hasDouble  "0.0001"^^xsd:double .

inst:IfcGeometricRepresentationContext_7
        ifc:precision_IfcGeometricRepresentationContext  inst:IfcReal_705 .

inst:IfcAxis2Placement3D_8
        rdf:type  ifc:IfcAxis2Placement3D .

inst:IfcGeometricRepresentationContext_7
        ifc:worldCoordinateSystem_IfcGeometricRepresentationContext  inst:IfcAxis2Placement3D_8 .

inst:IfcDirection_10  rdf:type  ifc:IfcDirection .

inst:IfcGeometricRepresentationContext_7
        ifc:trueNorth_IfcGeometricRepresentationContext  inst:IfcDirection_10 .

inst:IfcCartesianPoint_9
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcAxis2Placement3D_8
        ifc:location_IfcPlacement  inst:IfcCartesianPoint_9 .

inst:IfcLengthMeasure_List_706
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_9
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_706 .

inst:IfcLengthMeasure_List_707
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_708
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_709
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "0.0"^^xsd:double .

inst:IfcLengthMeasure_List_706
        list:hasContents  inst:IfcLengthMeasure_709 ;
        list:hasNext      inst:IfcLengthMeasure_List_707 .

inst:IfcLengthMeasure_List_707
        list:hasContents  inst:IfcLengthMeasure_709 ;
        list:hasNext      inst:IfcLengthMeasure_List_708 .

inst:IfcLengthMeasure_List_708
        list:hasContents  inst:IfcLengthMeasure_709 .

inst:IfcReal_List_710
        rdf:type  ifc:IfcReal_List .

inst:IfcDirection_10  ifc:directionRatios_IfcDirection  inst:IfcReal_List_710 .

inst:IfcReal_List_711
        rdf:type  ifc:IfcReal_List .

inst:IfcReal_List_710
        list:hasContents  inst:IfcLengthMeasure_709 ;
        list:hasNext      inst:IfcReal_List_711 .

inst:IfcReal_712  rdf:type  ifc:IfcReal ;
        express:hasDouble  "1.0"^^xsd:double .

inst:IfcReal_List_711
        list:hasContents  inst:IfcReal_712 .

inst:IfcGeometricRepresentationSubContext_11
        rdf:type  ifc:IfcGeometricRepresentationSubContext .

inst:IfcLabel_713  rdf:type  ifc:IfcLabel ;
        express:hasString  "Axis" .

inst:IfcGeometricRepresentationSubContext_11
        ifc:contextIdentifier_IfcRepresentationContext  inst:IfcLabel_713 ;
        ifc:contextType_IfcRepresentationContext  inst:IfcLabel_703 ;
        ifc:parentContext_IfcGeometricRepresentationSubContext  inst:IfcGeometricRepresentationContext_7 ;
        ifc:targetView_IfcGeometricRepresentationSubContext  ifc:MODEL_VIEW .

inst:IfcGeometricRepresentationSubContext_12
        rdf:type  ifc:IfcGeometricRepresentationSubContext .

inst:IfcLabel_714  rdf:type  ifc:IfcLabel ;
        express:hasString  "Body" .

inst:IfcGeometricRepresentationSubContext_12
        ifc:contextIdentifier_IfcRepresentationContext  inst:IfcLabel_714 ;
        ifc:contextType_IfcRepresentationContext  inst:IfcLabel_703 ;
        ifc:parentContext_IfcGeometricRepresentationSubContext  inst:IfcGeometricRepresentationContext_7 ;
        ifc:targetView_IfcGeometricRepresentationSubContext  ifc:MODEL_VIEW .

inst:IfcGeometricRepresentationContext_13
        rdf:type  ifc:IfcGeometricRepresentationContext ;
        ifc:contextType_IfcRepresentationContext  inst:IfcLabel_703 ;
        ifc:coordinateSpaceDimension_IfcGeometricRepresentationContext  inst:IfcDimensionCount_704 ;
        ifc:precision_IfcGeometricRepresentationContext  inst:IfcReal_705 ;
        ifc:worldCoordinateSystem_IfcGeometricRepresentationContext  inst:IfcAxis2Placement3D_8 ;
        ifc:trueNorth_IfcGeometricRepresentationContext  inst:IfcDirection_10 .

inst:IfcBuilding_50  rdf:type  ifc:IfcBuilding .

inst:IfcGloballyUniqueId_715
        rdf:type           ifc:IfcGloballyUniqueId ;
        express:hasString  "2UrSelLcv7p8sV8DUl$XR_" .

inst:IfcBuilding_50  ifc:globalId_IfcRoot  inst:IfcGloballyUniqueId_715 .

inst:IfcLabel_716  rdf:type  ifc:IfcLabel ;
        express:hasString  "IfcBuilding" .

inst:IfcBuilding_50  ifc:name_IfcRoot  inst:IfcLabel_716 .

inst:IfcLocalPlacement_51
        rdf:type  ifc:IfcLocalPlacement .

inst:IfcBuilding_50  ifc:objectPlacement_IfcProduct  inst:IfcLocalPlacement_51 ;
        ifc:compositionType_IfcSpatialStructureElement  ifc:ELEMENT .

inst:IfcPostalAddress_57
        rdf:type  ifc:IfcPostalAddress .

inst:IfcBuilding_50  ifc:buildingAddress_IfcBuilding  inst:IfcPostalAddress_57 .

inst:IfcAxis2Placement3D_52
        rdf:type  ifc:IfcAxis2Placement3D .

inst:IfcLocalPlacement_51
        ifc:relativePlacement_IfcLocalPlacement  inst:IfcAxis2Placement3D_52 .

inst:IfcAxis2Placement3D_52
        ifc:location_IfcPlacement  inst:IfcCartesianPoint_9 .

inst:IfcRelContainedInSpatialStructure_54
        rdf:type  ifc:IfcRelContainedInSpatialStructure .

inst:IfcGloballyUniqueId_717
        rdf:type           ifc:IfcGloballyUniqueId ;
        express:hasString  "1nLCX25sz2RPQ379yFIRFo" .

inst:IfcRelContainedInSpatialStructure_54
        ifc:globalId_IfcRoot  inst:IfcGloballyUniqueId_717 .

inst:IfcLabel_718  rdf:type  ifc:IfcLabel ;
        express:hasString  "Building" .

inst:IfcRelContainedInSpatialStructure_54
        ifc:name_IfcRoot  inst:IfcLabel_718 .

inst:IfcText_719  rdf:type  ifc:IfcText ;
        express:hasString  "Building Container for Elements" .

inst:IfcRelContainedInSpatialStructure_54
        ifc:description_IfcRoot  inst:IfcText_719 .

inst:IfcSanitaryTerminal_868
        rdf:type  ifc:IfcSanitaryTerminal .

inst:IfcRelContainedInSpatialStructure_54
        ifc:relatedElements_IfcRelContainedInSpatialStructure  inst:IfcSanitaryTerminal_868 ;
        ifc:relatingStructure_IfcRelContainedInSpatialStructure  inst:IfcBuilding_50 .

inst:IfcLocalPlacement_55
        rdf:type  ifc:IfcLocalPlacement ;
        ifc:placementRelTo_IfcLocalPlacement  inst:IfcLocalPlacement_51 ;
        ifc:relativePlacement_IfcLocalPlacement  inst:IfcAxis2Placement3D_52 .

inst:IfcLabel_720  rdf:type  ifc:IfcLabel ;
        express:hasString  "Unknown" .

inst:IfcPostalAddress_57
        ifc:region_IfcPostalAddress  inst:IfcLabel_720 .

inst:IfcProject_100  rdf:type  ifc:IfcProject .

inst:IfcGloballyUniqueId_721
        rdf:type           ifc:IfcGloballyUniqueId ;
        express:hasString  "0HXpBSJeXCieb8ZCRZtdyz" .

inst:IfcProject_100  ifc:globalId_IfcRoot  inst:IfcGloballyUniqueId_721 ;
        ifc:ownerHistory_IfcRoot  inst:IfcOwnerHistory_6 .

inst:IfcLabel_722  rdf:type  ifc:IfcLabel ;
        express:hasString  "IfcProject" .

inst:IfcProject_100  ifc:name_IfcRoot  inst:IfcLabel_722 ;
        ifc:longName_IfcContext  inst:IfcLabel_722 .

inst:IfcLabel_723  rdf:type  ifc:IfcLabel ;
        express:hasString  "" .

inst:IfcProject_100  ifc:phase_IfcContext  inst:IfcLabel_723 ;
        ifc:representationContexts_IfcContext  inst:IfcGeometricRepresentationContext_13 .

inst:IfcUnitAssignment_101
        rdf:type  ifc:IfcUnitAssignment .

inst:IfcProject_100  ifc:unitsInContext_IfcContext  inst:IfcUnitAssignment_101 .

inst:IfcSIUnit_102  rdf:type  ifc:IfcSIUnit .

inst:IfcUnitAssignment_101
        ifc:units_IfcUnitAssignment  inst:IfcSIUnit_102 .

inst:IfcSIUnit_103  rdf:type  ifc:IfcSIUnit .

inst:IfcUnitAssignment_101
        ifc:units_IfcUnitAssignment  inst:IfcSIUnit_103 .

inst:IfcSIUnit_104  rdf:type  ifc:IfcSIUnit .

inst:IfcUnitAssignment_101
        ifc:units_IfcUnitAssignment  inst:IfcSIUnit_104 .

inst:IfcSIUnit_102  ifc:unitType_IfcNamedUnit  ifc:LENGTHUNIT ;
        ifc:prefix_IfcSIUnit       ifc:MILLI ;
        ifc:name_IfcSIUnit         ifc:METRE .

inst:IfcSIUnit_103  ifc:unitType_IfcNamedUnit  ifc:PLANEANGLEUNIT ;
        ifc:name_IfcSIUnit         ifc:RADIAN .

inst:IfcSIUnit_104  ifc:unitType_IfcNamedUnit  ifc:TIMEUNIT ;
        ifc:name_IfcSIUnit         ifc:SECOND .

inst:IfcRelAggregates_105
        rdf:type  ifc:IfcRelAggregates .

inst:IfcGloballyUniqueId_724
        rdf:type           ifc:IfcGloballyUniqueId ;
        express:hasString  "0uZ92lKhT6EwklfgWpFFGq" .

inst:IfcRelAggregates_105
        ifc:globalId_IfcRoot  inst:IfcGloballyUniqueId_724 .

inst:IfcLabel_725  rdf:type  ifc:IfcLabel ;
        express:hasString  "Project Container" .

inst:IfcRelAggregates_105
        ifc:name_IfcRoot  inst:IfcLabel_725 .

inst:IfcText_726  rdf:type  ifc:IfcText ;
        express:hasString  "Project Container for Buildings" .

inst:IfcRelAggregates_105
        ifc:description_IfcRoot  inst:IfcText_726 ;
        ifc:relatingObject_IfcRelAggregates  inst:IfcProject_100 ;
        ifc:relatedObjects_IfcRelAggregates  inst:IfcBuilding_50 .

inst:IfcCartesianPoint_200
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_727
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_200
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_727 .

inst:IfcLengthMeasure_List_728
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_729
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_730
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-300.0"^^xsd:double .

inst:IfcLengthMeasure_List_727
        list:hasContents  inst:IfcLengthMeasure_730 ;
        list:hasNext      inst:IfcLengthMeasure_List_728 .

inst:IfcLengthMeasure_731
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "150.0"^^xsd:double .

inst:IfcLengthMeasure_List_728
        list:hasContents  inst:IfcLengthMeasure_731 ;
        list:hasNext      inst:IfcLengthMeasure_List_729 .

inst:IfcLengthMeasure_List_729
        list:hasContents  inst:IfcLengthMeasure_709 .

inst:IfcCartesianPoint_201
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_732
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_201
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_732 .

inst:IfcLengthMeasure_List_733
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_734
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_732
        list:hasContents  inst:IfcLengthMeasure_709 ;
        list:hasNext      inst:IfcLengthMeasure_List_733 .

inst:IfcLengthMeasure_735
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "253.099263998677"^^xsd:double .

inst:IfcLengthMeasure_List_733
        list:hasContents  inst:IfcLengthMeasure_735 ;
        list:hasNext      inst:IfcLengthMeasure_List_734 .

inst:IfcLengthMeasure_List_734
        list:hasContents  inst:IfcLengthMeasure_709 .

inst:IfcCartesianPoint_202
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_736
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_202
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_736 .

inst:IfcLengthMeasure_List_737
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_738
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_739
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-304.891071307496"^^xsd:double .

inst:IfcLengthMeasure_List_736
        list:hasContents  inst:IfcLengthMeasure_739 ;
        list:hasNext      inst:IfcLengthMeasure_List_737 .

inst:IfcLengthMeasure_740
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "109.822256120206"^^xsd:double .

inst:IfcLengthMeasure_List_737
        list:hasContents  inst:IfcLengthMeasure_740 ;
        list:hasNext      inst:IfcLengthMeasure_List_738 .

inst:IfcLengthMeasure_List_738
        list:hasContents  inst:IfcLengthMeasure_709 .

inst:IfcCartesianPoint_203
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_741
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_203
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_741 .

inst:IfcLengthMeasure_List_742
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_743
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_744
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-296.940075211924"^^xsd:double .

inst:IfcLengthMeasure_List_741
        list:hasContents  inst:IfcLengthMeasure_744 ;
        list:hasNext      inst:IfcLengthMeasure_List_742 .

inst:IfcLengthMeasure_745
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "69.9862810835965"^^xsd:double .

inst:IfcLengthMeasure_List_742
        list:hasContents  inst:IfcLengthMeasure_745 ;
        list:hasNext      inst:IfcLengthMeasure_List_743 .

inst:IfcLengthMeasure_List_743
        list:hasContents  inst:IfcLengthMeasure_709 .

inst:IfcCartesianPoint_204
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_746
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_204
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_746 .

inst:IfcLengthMeasure_List_747
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_748
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_749
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-280.922701731312"^^xsd:double .

inst:IfcLengthMeasure_List_746
        list:hasContents  inst:IfcLengthMeasure_749 ;
        list:hasNext      inst:IfcLengthMeasure_List_747 .

inst:IfcLengthMeasure_750
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "32.5907488402735"^^xsd:double .

inst:IfcLengthMeasure_List_747
        list:hasContents  inst:IfcLengthMeasure_750 ;
        list:hasNext      inst:IfcLengthMeasure_List_748 .

inst:IfcLengthMeasure_List_748
        list:hasContents  inst:IfcLengthMeasure_709 .

inst:IfcCartesianPoint_205
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_751
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_205
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_751 .

inst:IfcLengthMeasure_List_752
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_753
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_754
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-259.608278768049"^^xsd:double .

inst:IfcLengthMeasure_List_751
        list:hasContents  inst:IfcLengthMeasure_754 ;
        list:hasNext      inst:IfcLengthMeasure_List_752 .

inst:IfcLengthMeasure_755
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-2.08529338102262"^^xsd:double .

inst:IfcLengthMeasure_List_752
        list:hasContents  inst:IfcLengthMeasure_755 ;
        list:hasNext      inst:IfcLengthMeasure_List_753 .

inst:IfcLengthMeasure_List_753
        list:hasContents  inst:IfcLengthMeasure_709 .

inst:IfcCartesianPoint_206
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_756
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_206
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_756 .

inst:IfcLengthMeasure_List_757
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_758
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_759
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-234.428168068557"^^xsd:double .

inst:IfcLengthMeasure_List_756
        list:hasContents  inst:IfcLengthMeasure_759 ;
        list:hasNext      inst:IfcLengthMeasure_List_757 .

inst:IfcLengthMeasure_760
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-34.0753473673235"^^xsd:double .

inst:IfcLengthMeasure_List_757
        list:hasContents  inst:IfcLengthMeasure_760 ;
        list:hasNext      inst:IfcLengthMeasure_List_758 .

inst:IfcLengthMeasure_List_758
        list:hasContents  inst:IfcLengthMeasure_709 .

inst:IfcCartesianPoint_207
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_761
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_207
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_761 .

inst:IfcLengthMeasure_List_762
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_763
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_764
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-206.125816386308"^^xsd:double .

inst:IfcLengthMeasure_List_761
        list:hasContents  inst:IfcLengthMeasure_764 ;
        list:hasNext      inst:IfcLengthMeasure_List_762 .

inst:IfcLengthMeasure_765
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-63.3430855287523"^^xsd:double .

inst:IfcLengthMeasure_List_762
        list:hasContents  inst:IfcLengthMeasure_765 ;
        list:hasNext      inst:IfcLengthMeasure_List_763 .

inst:IfcLengthMeasure_List_763
        list:hasContents  inst:IfcLengthMeasure_709 .

inst:IfcCartesianPoint_208
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_766
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_208
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_766 .

inst:IfcLengthMeasure_List_767
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_768
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_769
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-175.071534188435"^^xsd:double .

inst:IfcLengthMeasure_List_766
        list:hasContents  inst:IfcLengthMeasure_769 ;
        list:hasNext      inst:IfcLengthMeasure_List_767 .

inst:IfcLengthMeasure_770
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-89.671802213621"^^xsd:double .

inst:IfcLengthMeasure_List_767
        list:hasContents  inst:IfcLengthMeasure_770 ;
        list:hasNext      inst:IfcLengthMeasure_List_768 .

inst:IfcLengthMeasure_List_768
        list:hasContents  inst:IfcLengthMeasure_709 .

inst:IfcCartesianPoint_209
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_771
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_209
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_771 .

inst:IfcLengthMeasure_List_772
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_773
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_774
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-141.412934526735"^^xsd:double .

inst:IfcLengthMeasure_List_771
        list:hasContents  inst:IfcLengthMeasure_774 ;
        list:hasNext      inst:IfcLengthMeasure_List_772 .

inst:IfcLengthMeasure_775
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-112.569598180375"^^xsd:double .

inst:IfcLengthMeasure_List_772
        list:hasContents  inst:IfcLengthMeasure_775 ;
        list:hasNext      inst:IfcLengthMeasure_List_773 .

inst:IfcLengthMeasure_List_773
        list:hasContents  inst:IfcLengthMeasure_709 .

inst:IfcCartesianPoint_210
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_776
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_210
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_776 .

inst:IfcLengthMeasure_List_777
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_778
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_779
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-105.215114341225"^^xsd:double .

inst:IfcLengthMeasure_List_776
        list:hasContents  inst:IfcLengthMeasure_779 ;
        list:hasNext      inst:IfcLengthMeasure_List_777 .

inst:IfcLengthMeasure_780
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-131.178510124149"^^xsd:double .

inst:IfcLengthMeasure_List_777
        list:hasContents  inst:IfcLengthMeasure_780 ;
        list:hasNext      inst:IfcLengthMeasure_List_778 .

inst:IfcLengthMeasure_List_778
        list:hasContents  inst:IfcLengthMeasure_709 .

inst:IfcCartesianPoint_211
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_781
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_211
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_781 .

inst:IfcLengthMeasure_List_782
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_783
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_784
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-66.7978479682479"^^xsd:double .

inst:IfcLengthMeasure_List_781
        list:hasContents  inst:IfcLengthMeasure_784 ;
        list:hasNext      inst:IfcLengthMeasure_List_782 .

inst:IfcLengthMeasure_785
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-144.600522500239"^^xsd:double .

inst:IfcLengthMeasure_List_782
        list:hasContents  inst:IfcLengthMeasure_785 ;
        list:hasNext      inst:IfcLengthMeasure_List_783 .

inst:IfcLengthMeasure_List_783
        list:hasContents  inst:IfcLengthMeasure_709 .

inst:IfcCartesianPoint_212
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_786
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_212
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_786 .

inst:IfcLengthMeasure_List_787
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_788
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_789
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-26.8019125425913"^^xsd:double .

inst:IfcLengthMeasure_List_786
        list:hasContents  inst:IfcLengthMeasure_789 ;
        list:hasNext      inst:IfcLengthMeasure_List_787 .

inst:IfcLengthMeasure_790
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-152.078206375312"^^xsd:double .

inst:IfcLengthMeasure_List_787
        list:hasContents  inst:IfcLengthMeasure_790 ;
        list:hasNext      inst:IfcLengthMeasure_List_788 .

inst:IfcLengthMeasure_List_788
        list:hasContents  inst:IfcLengthMeasure_709 .

inst:IfcCartesianPoint_213
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_791
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_213
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_791 .

inst:IfcLengthMeasure_List_792
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_793
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_794
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "13.8710862775596"^^xsd:double .

inst:IfcLengthMeasure_List_791
        list:hasContents  inst:IfcLengthMeasure_794 ;
        list:hasNext      inst:IfcLengthMeasure_List_792 .

inst:IfcLengthMeasure_795
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-153.121660363236"^^xsd:double .

inst:IfcLengthMeasure_List_792
        list:hasContents  inst:IfcLengthMeasure_795 ;
        list:hasNext      inst:IfcLengthMeasure_List_793 .

inst:IfcLengthMeasure_List_793
        list:hasContents  inst:IfcLengthMeasure_709 .

inst:IfcCartesianPoint_214
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_796
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_214
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_796 .

inst:IfcLengthMeasure_List_797
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_798
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_799
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "54.1903326227934"^^xsd:double .

inst:IfcLengthMeasure_List_796
        list:hasContents  inst:IfcLengthMeasure_799 ;
        list:hasNext      inst:IfcLengthMeasure_List_797 .

inst:IfcLengthMeasure_800
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-147.658103330519"^^xsd:double .

inst:IfcLengthMeasure_List_797
        list:hasContents  inst:IfcLengthMeasure_800 ;
        list:hasNext      inst:IfcLengthMeasure_List_798 .

inst:IfcLengthMeasure_List_798
        list:hasContents  inst:IfcLengthMeasure_709 .

inst:IfcCartesianPoint_215
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_801
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_215
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_801 .

inst:IfcLengthMeasure_List_802
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_803
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_804
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "93.1938862047157"^^xsd:double .

inst:IfcLengthMeasure_List_801
        list:hasContents  inst:IfcLengthMeasure_804 ;
        list:hasNext      inst:IfcLengthMeasure_List_802 .

inst:IfcLengthMeasure_805
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-136.056391059388"^^xsd:double .

inst:IfcLengthMeasure_List_802
        list:hasContents  inst:IfcLengthMeasure_805 ;
        list:hasNext      inst:IfcLengthMeasure_List_803 .

inst:IfcLengthMeasure_List_803
        list:hasContents  inst:IfcLengthMeasure_709 .

inst:IfcCartesianPoint_216
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_806
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_216
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_806 .

inst:IfcLengthMeasure_List_807
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_808
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_809
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "130.150407237097"^^xsd:double .

inst:IfcLengthMeasure_List_806
        list:hasContents  inst:IfcLengthMeasure_809 ;
        list:hasNext      inst:IfcLengthMeasure_List_807 .

inst:IfcLengthMeasure_810
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-119.008883827061"^^xsd:double .

inst:IfcLengthMeasure_List_807
        list:hasContents  inst:IfcLengthMeasure_810 ;
        list:hasNext      inst:IfcLengthMeasure_List_808 .

inst:IfcLengthMeasure_List_808
        list:hasContents  inst:IfcLengthMeasure_709 .

inst:IfcCartesianPoint_217
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_811
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_217
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_811 .

inst:IfcLengthMeasure_List_812
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_813
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_814
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "164.629900262907"^^xsd:double .

inst:IfcLengthMeasure_List_811
        list:hasContents  inst:IfcLengthMeasure_814 ;
        list:hasNext      inst:IfcLengthMeasure_List_812 .

inst:IfcLengthMeasure_815
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-97.37165169902"^^xsd:double .

inst:IfcLengthMeasure_List_812
        list:hasContents  inst:IfcLengthMeasure_815 ;
        list:hasNext      inst:IfcLengthMeasure_List_813 .

inst:IfcLengthMeasure_List_813
        list:hasContents  inst:IfcLengthMeasure_709 .

inst:IfcCartesianPoint_218
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_816
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_218
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_816 .

inst:IfcLengthMeasure_List_817
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_818
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_819
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "196.521914802755"^^xsd:double .

inst:IfcLengthMeasure_List_816
        list:hasContents  inst:IfcLengthMeasure_819 ;
        list:hasNext      inst:IfcLengthMeasure_List_817 .

inst:IfcLengthMeasure_820
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-72.0656434367093"^^xsd:double .

inst:IfcLengthMeasure_List_817
        list:hasContents  inst:IfcLengthMeasure_820 ;
        list:hasNext      inst:IfcLengthMeasure_List_818 .

inst:IfcLengthMeasure_List_818
        list:hasContents  inst:IfcLengthMeasure_709 .

inst:IfcCartesianPoint_219
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_821
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_219
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_821 .

inst:IfcLengthMeasure_List_822
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_823
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_824
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "225.728273711876"^^xsd:double .

inst:IfcLengthMeasure_List_821
        list:hasContents  inst:IfcLengthMeasure_824 ;
        list:hasNext      inst:IfcLengthMeasure_List_822 .

inst:IfcLengthMeasure_825
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-43.6997637700286"^^xsd:double .

inst:IfcLengthMeasure_List_822
        list:hasContents  inst:IfcLengthMeasure_825 ;
        list:hasNext      inst:IfcLengthMeasure_List_823 .

inst:IfcLengthMeasure_List_823
        list:hasContents  inst:IfcLengthMeasure_709 .

inst:IfcCartesianPoint_220
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_826
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_220
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_826 .

inst:IfcLengthMeasure_List_827
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_828
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_829
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "251.961645534009"^^xsd:double .

inst:IfcLengthMeasure_List_826
        list:hasContents  inst:IfcLengthMeasure_829 ;
        list:hasNext      inst:IfcLengthMeasure_List_827 .

inst:IfcLengthMeasure_830
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-12.5658965535741"^^xsd:double .

inst:IfcLengthMeasure_List_827
        list:hasContents  inst:IfcLengthMeasure_830 ;
        list:hasNext      inst:IfcLengthMeasure_List_828 .

inst:IfcLengthMeasure_List_828
        list:hasContents  inst:IfcLengthMeasure_709 .

inst:IfcCartesianPoint_221
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_831
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_221
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_831 .

inst:IfcLengthMeasure_List_832
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_833
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_834
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "274.62087031373"^^xsd:double .

inst:IfcLengthMeasure_List_831
        list:hasContents  inst:IfcLengthMeasure_834 ;
        list:hasNext      inst:IfcLengthMeasure_List_832 .

inst:IfcLengthMeasure_835
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "21.2509318856294"^^xsd:double .

inst:IfcLengthMeasure_List_832
        list:hasContents  inst:IfcLengthMeasure_835 ;
        list:hasNext      inst:IfcLengthMeasure_List_833 .

inst:IfcLengthMeasure_List_833
        list:hasContents  inst:IfcLengthMeasure_709 .

inst:IfcCartesianPoint_222
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_836
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_222
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_836 .

inst:IfcLengthMeasure_List_837
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_838
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_839
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "292.544561947164"^^xsd:double .

inst:IfcLengthMeasure_List_836
        list:hasContents  inst:IfcLengthMeasure_839 ;
        list:hasNext      inst:IfcLengthMeasure_List_837 .

inst:IfcLengthMeasure_840
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "57.7814080324017"^^xsd:double .

inst:IfcLengthMeasure_List_837
        list:hasContents  inst:IfcLengthMeasure_840 ;
        list:hasNext      inst:IfcLengthMeasure_List_838 .

inst:IfcLengthMeasure_List_838
        list:hasContents  inst:IfcLengthMeasure_709 .

inst:IfcCartesianPoint_223
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_841
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_223
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_841 .

inst:IfcLengthMeasure_List_842
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_843
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_844
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "303.485368351088"^^xsd:double .

inst:IfcLengthMeasure_List_841
        list:hasContents  inst:IfcLengthMeasure_844 ;
        list:hasNext      inst:IfcLengthMeasure_List_842 .

inst:IfcLengthMeasure_845
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "96.9291090994897"^^xsd:double .

inst:IfcLengthMeasure_List_842
        list:hasContents  inst:IfcLengthMeasure_845 ;
        list:hasNext      inst:IfcLengthMeasure_List_843 .

inst:IfcLengthMeasure_List_843
        list:hasContents  inst:IfcLengthMeasure_709 .

inst:IfcCartesianPoint_224
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_846
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_224
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_846 .

inst:IfcLengthMeasure_List_847
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_848
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_849
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "303.288957242437"^^xsd:double .

inst:IfcLengthMeasure_List_846
        list:hasContents  inst:IfcLengthMeasure_849 ;
        list:hasNext      inst:IfcLengthMeasure_List_847 .

inst:IfcLengthMeasure_850
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "137.460278743209"^^xsd:double .

inst:IfcLengthMeasure_List_847
        list:hasContents  inst:IfcLengthMeasure_850 ;
        list:hasNext      inst:IfcLengthMeasure_List_848 .

inst:IfcLengthMeasure_List_848
        list:hasContents  inst:IfcLengthMeasure_709 .

inst:IfcCartesianPoint_225
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_851
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_225
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_851 .

inst:IfcLengthMeasure_List_852
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_853
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_854
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "287.369755242598"^^xsd:double .

inst:IfcLengthMeasure_List_851
        list:hasContents  inst:IfcLengthMeasure_854 ;
        list:hasNext      inst:IfcLengthMeasure_List_852 .

inst:IfcLengthMeasure_855
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "174.609406522514"^^xsd:double .

inst:IfcLengthMeasure_List_852
        list:hasContents  inst:IfcLengthMeasure_855 ;
        list:hasNext      inst:IfcLengthMeasure_List_853 .

inst:IfcLengthMeasure_List_853
        list:hasContents  inst:IfcLengthMeasure_709 .

inst:IfcCartesianPoint_226
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_856
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_226
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_856 .

inst:IfcLengthMeasure_List_857
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_858
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_859
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "258.965081713391"^^xsd:double .

inst:IfcLengthMeasure_List_856
        list:hasContents  inst:IfcLengthMeasure_859 ;
        list:hasNext      inst:IfcLengthMeasure_List_857 .

inst:IfcLengthMeasure_860
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "203.584218912768"^^xsd:double .

inst:IfcLengthMeasure_List_857
        list:hasContents  inst:IfcLengthMeasure_860 ;
        list:hasNext      inst:IfcLengthMeasure_List_858 .

inst:IfcLengthMeasure_List_858
        list:hasContents  inst:IfcLengthMeasure_709 .

inst:IfcCartesianPoint_227
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_861
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_227
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_861 .

inst:IfcLengthMeasure_List_862
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_863
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_864
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "224.363000998313"^^xsd:double .

inst:IfcLengthMeasure_List_861
        list:hasContents  inst:IfcLengthMeasure_864 ;
        list:hasNext      inst:IfcLengthMeasure_List_862 .

inst:IfcLengthMeasure_865
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "224.953681400811"^^xsd:double .

inst:IfcLengthMeasure_List_862
        list:hasContents  inst:IfcLengthMeasure_865 ;
        list:hasNext      inst:IfcLengthMeasure_List_863 .

inst:IfcLengthMeasure_List_863
        list:hasContents  inst:IfcLengthMeasure_709 .

inst:IfcCartesianPoint_228
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_866
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_228
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_866 .

inst:IfcLengthMeasure_List_867
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_868
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_869
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "186.818017180943"^^xsd:double .

inst:IfcLengthMeasure_List_866
        list:hasContents  inst:IfcLengthMeasure_869 ;
        list:hasNext      inst:IfcLengthMeasure_List_867 .

inst:IfcLengthMeasure_870
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "240.67163971588"^^xsd:double .

inst:IfcLengthMeasure_List_867
        list:hasContents  inst:IfcLengthMeasure_870 ;
        list:hasNext      inst:IfcLengthMeasure_List_868 .

inst:IfcLengthMeasure_List_868
        list:hasContents  inst:IfcLengthMeasure_709 .

inst:IfcCartesianPoint_229
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_871
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_229
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_871 .

inst:IfcLengthMeasure_List_872
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_873
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_874
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "147.756154156487"^^xsd:double .

inst:IfcLengthMeasure_List_871
        list:hasContents  inst:IfcLengthMeasure_874 ;
        list:hasNext      inst:IfcLengthMeasure_List_872 .

inst:IfcLengthMeasure_875
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "252.155742473922"^^xsd:double .

inst:IfcLengthMeasure_List_872
        list:hasContents  inst:IfcLengthMeasure_875 ;
        list:hasNext      inst:IfcLengthMeasure_List_873 .

inst:IfcLengthMeasure_List_873
        list:hasContents  inst:IfcLengthMeasure_709 .

inst:IfcCartesianPoint_230
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_876
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_230
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_876 .

inst:IfcLengthMeasure_List_877
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_878
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_879
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "107.854433144861"^^xsd:double .

inst:IfcLengthMeasure_List_876
        list:hasContents  inst:IfcLengthMeasure_879 ;
        list:hasNext      inst:IfcLengthMeasure_List_877 .

inst:IfcLengthMeasure_880
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "260.280582237886"^^xsd:double .

inst:IfcLengthMeasure_List_877
        list:hasContents  inst:IfcLengthMeasure_880 ;
        list:hasNext      inst:IfcLengthMeasure_List_878 .

inst:IfcLengthMeasure_List_878
        list:hasContents  inst:IfcLengthMeasure_709 .

inst:IfcCartesianPoint_231
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_881
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_231
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_881 .

inst:IfcLengthMeasure_List_882
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_883
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_884
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "67.4765332607867"^^xsd:double .

inst:IfcLengthMeasure_List_881
        list:hasContents  inst:IfcLengthMeasure_884 ;
        list:hasNext      inst:IfcLengthMeasure_List_882 .

inst:IfcLengthMeasure_885
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "265.571769764524"^^xsd:double .

inst:IfcLengthMeasure_List_882
        list:hasContents  inst:IfcLengthMeasure_885 ;
        list:hasNext      inst:IfcLengthMeasure_List_883 .

inst:IfcLengthMeasure_List_883
        list:hasContents  inst:IfcLengthMeasure_709 .

inst:IfcCartesianPoint_232
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_886
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_232
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_886 .

inst:IfcLengthMeasure_List_887
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_888
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_889
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "26.8458427708863"^^xsd:double .

inst:IfcLengthMeasure_List_886
        list:hasContents  inst:IfcLengthMeasure_889 ;
        list:hasNext      inst:IfcLengthMeasure_List_887 .

inst:IfcLengthMeasure_890
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "268.331827033896"^^xsd:double .

inst:IfcLengthMeasure_List_887
        list:hasContents  inst:IfcLengthMeasure_890 ;
        list:hasNext      inst:IfcLengthMeasure_List_888 .

inst:IfcLengthMeasure_List_888
        list:hasContents  inst:IfcLengthMeasure_709 .

inst:IfcCartesianPoint_233
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_891
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_233
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_891 .

inst:IfcLengthMeasure_List_892
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_893
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_894
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-13.8771780303431"^^xsd:double .

inst:IfcLengthMeasure_List_891
        list:hasContents  inst:IfcLengthMeasure_894 ;
        list:hasNext      inst:IfcLengthMeasure_List_892 .

inst:IfcLengthMeasure_895
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "268.706812331913"^^xsd:double .

inst:IfcLengthMeasure_List_892
        list:hasContents  inst:IfcLengthMeasure_895 ;
        list:hasNext      inst:IfcLengthMeasure_List_893 .

inst:IfcLengthMeasure_List_893
        list:hasContents  inst:IfcLengthMeasure_709 .

inst:IfcCartesianPoint_234
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_896
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_234
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_896 .

inst:IfcLengthMeasure_List_897
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_898
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_899
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-54.5530293999268"^^xsd:double .

inst:IfcLengthMeasure_List_896
        list:hasContents  inst:IfcLengthMeasure_899 ;
        list:hasNext      inst:IfcLengthMeasure_List_897 .

inst:IfcLengthMeasure_900
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "266.715969920949"^^xsd:double .

inst:IfcLengthMeasure_List_897
        list:hasContents  inst:IfcLengthMeasure_900 ;
        list:hasNext      inst:IfcLengthMeasure_List_898 .

inst:IfcLengthMeasure_List_898
        list:hasContents  inst:IfcLengthMeasure_709 .

inst:IfcCartesianPoint_235
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_901
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_235
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_901 .

inst:IfcLengthMeasure_List_902
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_903
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_904
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-95.0316202590492"^^xsd:double .

inst:IfcLengthMeasure_List_901
        list:hasContents  inst:IfcLengthMeasure_904 ;
        list:hasNext      inst:IfcLengthMeasure_List_902 .

inst:IfcLengthMeasure_905
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "262.255497380798"^^xsd:double .

inst:IfcLengthMeasure_List_902
        list:hasContents  inst:IfcLengthMeasure_905 ;
        list:hasNext      inst:IfcLengthMeasure_List_903 .

inst:IfcLengthMeasure_List_903
        list:hasContents  inst:IfcLengthMeasure_709 .

inst:IfcCartesianPoint_236
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_906
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_236
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_906 .

inst:IfcLengthMeasure_List_907
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_908
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_909
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-135.115612880362"^^xsd:double .

inst:IfcLengthMeasure_List_906
        list:hasContents  inst:IfcLengthMeasure_909 ;
        list:hasNext      inst:IfcLengthMeasure_List_907 .

inst:IfcLengthMeasure_910
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "255.077835581894"^^xsd:double .

inst:IfcLengthMeasure_List_907
        list:hasContents  inst:IfcLengthMeasure_910 ;
        list:hasNext      inst:IfcLengthMeasure_List_908 .

inst:IfcLengthMeasure_List_908
        list:hasContents  inst:IfcLengthMeasure_709 .

inst:IfcCartesianPoint_237
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_911
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_237
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_911 .

inst:IfcLengthMeasure_List_912
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_913
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_914
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-174.498313981775"^^xsd:double .

inst:IfcLengthMeasure_List_911
        list:hasContents  inst:IfcLengthMeasure_914 ;
        list:hasNext      inst:IfcLengthMeasure_List_912 .

inst:IfcLengthMeasure_915
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "244.738726835566"^^xsd:double .

inst:IfcLengthMeasure_List_912
        list:hasContents  inst:IfcLengthMeasure_915 ;
        list:hasNext      inst:IfcLengthMeasure_List_913 .

inst:IfcLengthMeasure_List_913
        list:hasContents  inst:IfcLengthMeasure_709 .

inst:IfcCartesianPoint_238
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_916
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_238
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_916 .

inst:IfcLengthMeasure_List_917
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_918
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_919
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-212.631973130034"^^xsd:double .

inst:IfcLengthMeasure_List_916
        list:hasContents  inst:IfcLengthMeasure_919 ;
        list:hasNext      inst:IfcLengthMeasure_List_917 .

inst:IfcLengthMeasure_920
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "230.493081814022"^^xsd:double .

inst:IfcLengthMeasure_List_917
        list:hasContents  inst:IfcLengthMeasure_920 ;
        list:hasNext      inst:IfcLengthMeasure_List_918 .

inst:IfcLengthMeasure_List_918
        list:hasContents  inst:IfcLengthMeasure_709 .

inst:IfcCartesianPoint_239
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_921
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_239
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_921 .

inst:IfcLengthMeasure_List_922
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_923
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_924
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-248.405687646327"^^xsd:double .

inst:IfcLengthMeasure_List_921
        list:hasContents  inst:IfcLengthMeasure_924 ;
        list:hasNext      inst:IfcLengthMeasure_List_922 .

inst:IfcLengthMeasure_925
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "211.118276332925"^^xsd:double .

inst:IfcLengthMeasure_List_922
        list:hasContents  inst:IfcLengthMeasure_925 ;
        list:hasNext      inst:IfcLengthMeasure_List_923 .

inst:IfcLengthMeasure_List_923
        list:hasContents  inst:IfcLengthMeasure_709 .

inst:IfcCartesianPoint_240
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_926
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_240
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_926 .

inst:IfcLengthMeasure_List_927
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_928
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_929
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-279.319862190373"^^xsd:double .

inst:IfcLengthMeasure_List_926
        list:hasContents  inst:IfcLengthMeasure_929 ;
        list:hasNext      inst:IfcLengthMeasure_List_927 .

inst:IfcLengthMeasure_930
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "184.775801315119"^^xsd:double .

inst:IfcLengthMeasure_List_927
        list:hasContents  inst:IfcLengthMeasure_930 ;
        list:hasNext      inst:IfcLengthMeasure_List_928 .

inst:IfcLengthMeasure_List_928
        list:hasContents  inst:IfcLengthMeasure_709 .

inst:IfcCartesianPoint_241
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_931
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_241
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_931 .

inst:IfcLengthMeasure_List_932
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_933
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_934
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "38.4756983219429"^^xsd:double .

inst:IfcLengthMeasure_List_931
        list:hasContents  inst:IfcLengthMeasure_934 ;
        list:hasNext      inst:IfcLengthMeasure_List_932 .

inst:IfcLengthMeasure_935
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "252.033478287557"^^xsd:double .

inst:IfcLengthMeasure_List_932
        list:hasContents  inst:IfcLengthMeasure_935 ;
        list:hasNext      inst:IfcLengthMeasure_List_933 .

inst:IfcLengthMeasure_List_933
        list:hasContents  inst:IfcLengthMeasure_709 .

inst:IfcCartesianPoint_242
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_936
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_242
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_936 .

inst:IfcLengthMeasure_List_937
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_938
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_939
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "76.82797079471"^^xsd:double .

inst:IfcLengthMeasure_List_936
        list:hasContents  inst:IfcLengthMeasure_939 ;
        list:hasNext      inst:IfcLengthMeasure_List_937 .

inst:IfcLengthMeasure_940
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "248.781505427916"^^xsd:double .

inst:IfcLengthMeasure_List_937
        list:hasContents  inst:IfcLengthMeasure_940 ;
        list:hasNext      inst:IfcLengthMeasure_List_938 .

inst:IfcLengthMeasure_List_938
        list:hasContents  inst:IfcLengthMeasure_709 .

inst:IfcCartesianPoint_243
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_941
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_243
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_941 .

inst:IfcLengthMeasure_List_942
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_943
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_944
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "114.905040261981"^^xsd:double .

inst:IfcLengthMeasure_List_941
        list:hasContents  inst:IfcLengthMeasure_944 ;
        list:hasNext      inst:IfcLengthMeasure_List_942 .

inst:IfcLengthMeasure_945
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "243.168611097888"^^xsd:double .

inst:IfcLengthMeasure_List_942
        list:hasContents  inst:IfcLengthMeasure_945 ;
        list:hasNext      inst:IfcLengthMeasure_List_943 .

inst:IfcLengthMeasure_List_943
        list:hasContents  inst:IfcLengthMeasure_709 .

inst:IfcCartesianPoint_244
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_946
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_244
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_946 .

inst:IfcLengthMeasure_List_947
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_948
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_949
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "152.483825019629"^^xsd:double .

inst:IfcLengthMeasure_List_946
        list:hasContents  inst:IfcLengthMeasure_949 ;
        list:hasNext      inst:IfcLengthMeasure_List_947 .

inst:IfcLengthMeasure_950
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "234.862972697005"^^xsd:double .

inst:IfcLengthMeasure_List_947
        list:hasContents  inst:IfcLengthMeasure_950 ;
        list:hasNext      inst:IfcLengthMeasure_List_948 .

inst:IfcLengthMeasure_List_948
        list:hasContents  inst:IfcLengthMeasure_709 .

inst:IfcCartesianPoint_245
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_951
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_245
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_951 .

inst:IfcLengthMeasure_List_952
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_953
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_954
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "189.184260308893"^^xsd:double .

inst:IfcLengthMeasure_List_951
        list:hasContents  inst:IfcLengthMeasure_954 ;
        list:hasNext      inst:IfcLengthMeasure_List_952 .

inst:IfcLengthMeasure_955
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "223.297735395069"^^xsd:double .

inst:IfcLengthMeasure_List_952
        list:hasContents  inst:IfcLengthMeasure_955 ;
        list:hasNext      inst:IfcLengthMeasure_List_953 .

inst:IfcLengthMeasure_List_953
        list:hasContents  inst:IfcLengthMeasure_709 .

inst:IfcCartesianPoint_246
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_956
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_246
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_956 .

inst:IfcLengthMeasure_List_957
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_958
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_959
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "224.265826794532"^^xsd:double .

inst:IfcLengthMeasure_List_956
        list:hasContents  inst:IfcLengthMeasure_959 ;
        list:hasNext      inst:IfcLengthMeasure_List_957 .

inst:IfcLengthMeasure_960
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "207.523990642274"^^xsd:double .

inst:IfcLengthMeasure_List_957
        list:hasContents  inst:IfcLengthMeasure_960 ;
        list:hasNext      inst:IfcLengthMeasure_List_958 .

inst:IfcLengthMeasure_List_958
        list:hasContents  inst:IfcLengthMeasure_709 .

inst:IfcCartesianPoint_247
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_961
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_247
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_961 .

inst:IfcLengthMeasure_List_962
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_963
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_964
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "256.087669717028"^^xsd:double .

inst:IfcLengthMeasure_List_961
        list:hasContents  inst:IfcLengthMeasure_964 ;
        list:hasNext      inst:IfcLengthMeasure_List_962 .

inst:IfcLengthMeasure_965
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "185.991172830599"^^xsd:double .

inst:IfcLengthMeasure_List_962
        list:hasContents  inst:IfcLengthMeasure_965 ;
        list:hasNext      inst:IfcLengthMeasure_List_963 .

inst:IfcLengthMeasure_List_963
        list:hasContents  inst:IfcLengthMeasure_709 .

inst:IfcCartesianPoint_248
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_966
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_248
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_966 .

inst:IfcLengthMeasure_List_967
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_968
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_969
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "280.765071736094"^^xsd:double .

inst:IfcLengthMeasure_List_966
        list:hasContents  inst:IfcLengthMeasure_969 ;
        list:hasNext      inst:IfcLengthMeasure_List_967 .

inst:IfcLengthMeasure_970
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "156.699063336262"^^xsd:double .

inst:IfcLengthMeasure_List_967
        list:hasContents  inst:IfcLengthMeasure_970 ;
        list:hasNext      inst:IfcLengthMeasure_List_968 .

inst:IfcLengthMeasure_List_968
        list:hasContents  inst:IfcLengthMeasure_709 .

inst:IfcCartesianPoint_249
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_971
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_249
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_971 .

inst:IfcLengthMeasure_List_972
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_973
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_974
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "291.585141614082"^^xsd:double .

inst:IfcLengthMeasure_List_971
        list:hasContents  inst:IfcLengthMeasure_974 ;
        list:hasNext      inst:IfcLengthMeasure_List_972 .

inst:IfcLengthMeasure_975
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "120.093338245881"^^xsd:double .

inst:IfcLengthMeasure_List_972
        list:hasContents  inst:IfcLengthMeasure_975 ;
        list:hasNext      inst:IfcLengthMeasure_List_973 .

inst:IfcLengthMeasure_List_973
        list:hasContents  inst:IfcLengthMeasure_709 .

inst:IfcCartesianPoint_250
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_976
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_250
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_976 .

inst:IfcLengthMeasure_List_977
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_978
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_979
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "287.786485451514"^^xsd:double .

inst:IfcLengthMeasure_List_976
        list:hasContents  inst:IfcLengthMeasure_979 ;
        list:hasNext      inst:IfcLengthMeasure_List_977 .

inst:IfcLengthMeasure_980
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "81.9448662146907"^^xsd:double .

inst:IfcLengthMeasure_List_977
        list:hasContents  inst:IfcLengthMeasure_980 ;
        list:hasNext      inst:IfcLengthMeasure_List_978 .

inst:IfcLengthMeasure_List_978
        list:hasContents  inst:IfcLengthMeasure_709 .

inst:IfcCartesianPoint_251
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_981
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_251
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_981 .

inst:IfcLengthMeasure_List_982
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_983
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_984
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "274.528157897687"^^xsd:double .

inst:IfcLengthMeasure_List_981
        list:hasContents  inst:IfcLengthMeasure_984 ;
        list:hasNext      inst:IfcLengthMeasure_List_982 .

inst:IfcLengthMeasure_985
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "45.8738463140147"^^xsd:double .

inst:IfcLengthMeasure_List_982
        list:hasContents  inst:IfcLengthMeasure_985 ;
        list:hasNext      inst:IfcLengthMeasure_List_983 .

inst:IfcLengthMeasure_List_983
        list:hasContents  inst:IfcLengthMeasure_709 .

inst:IfcCartesianPoint_252
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_986
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_252
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_986 .

inst:IfcLengthMeasure_List_987
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_988
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_989
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "255.376512280737"^^xsd:double .

inst:IfcLengthMeasure_List_986
        list:hasContents  inst:IfcLengthMeasure_989 ;
        list:hasNext      inst:IfcLengthMeasure_List_987 .

inst:IfcLengthMeasure_990
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "12.5180862102588"^^xsd:double .

inst:IfcLengthMeasure_List_987
        list:hasContents  inst:IfcLengthMeasure_990 ;
        list:hasNext      inst:IfcLengthMeasure_List_988 .

inst:IfcLengthMeasure_List_988
        list:hasContents  inst:IfcLengthMeasure_709 .

inst:IfcCartesianPoint_253
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_991
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_253
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_991 .

inst:IfcLengthMeasure_List_992
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_993
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_994
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "232.162082007755"^^xsd:double .

inst:IfcLengthMeasure_List_991
        list:hasContents  inst:IfcLengthMeasure_994 ;
        list:hasNext      inst:IfcLengthMeasure_List_992 .

inst:IfcLengthMeasure_995
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-18.1641105843033"^^xsd:double .

inst:IfcLengthMeasure_List_992
        list:hasContents  inst:IfcLengthMeasure_995 ;
        list:hasNext      inst:IfcLengthMeasure_List_993 .

inst:IfcLengthMeasure_List_993
        list:hasContents  inst:IfcLengthMeasure_709 .

inst:IfcCartesianPoint_254
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_996
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_254
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_996 .

inst:IfcLengthMeasure_List_997
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_998
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_999
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "205.82058249047"^^xsd:double .

inst:IfcLengthMeasure_List_996
        list:hasContents  inst:IfcLengthMeasure_999 ;
        list:hasNext      inst:IfcLengthMeasure_List_997 .

inst:IfcLengthMeasure_1000
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-46.2135089189517"^^xsd:double .

inst:IfcLengthMeasure_List_997
        list:hasContents  inst:IfcLengthMeasure_1000 ;
        list:hasNext      inst:IfcLengthMeasure_List_998 .

inst:IfcLengthMeasure_List_998
        list:hasContents  inst:IfcLengthMeasure_709 .

inst:IfcCartesianPoint_255
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_1001
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_255
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_1001 .

inst:IfcLengthMeasure_List_1002
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1003
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_1004
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "176.831543285532"^^xsd:double .

inst:IfcLengthMeasure_List_1001
        list:hasContents  inst:IfcLengthMeasure_1004 ;
        list:hasNext      inst:IfcLengthMeasure_List_1002 .

inst:IfcLengthMeasure_1005
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-71.5181976728152"^^xsd:double .

inst:IfcLengthMeasure_List_1002
        list:hasContents  inst:IfcLengthMeasure_1005 ;
        list:hasNext      inst:IfcLengthMeasure_List_1003 .

inst:IfcLengthMeasure_List_1003
        list:hasContents  inst:IfcLengthMeasure_709 .

inst:IfcCartesianPoint_256
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_1006
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_256
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_1006 .

inst:IfcLengthMeasure_List_1007
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1008
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_1009
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "145.419108882987"^^xsd:double .

inst:IfcLengthMeasure_List_1006
        list:hasContents  inst:IfcLengthMeasure_1009 ;
        list:hasNext      inst:IfcLengthMeasure_List_1007 .

inst:IfcLengthMeasure_1010
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-93.7387882260769"^^xsd:double .

inst:IfcLengthMeasure_List_1007
        list:hasContents  inst:IfcLengthMeasure_1010 ;
        list:hasNext      inst:IfcLengthMeasure_List_1008 .

inst:IfcLengthMeasure_List_1008
        list:hasContents  inst:IfcLengthMeasure_709 .

inst:IfcCartesianPoint_257
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_1011
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_257
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_1011 .

inst:IfcLengthMeasure_List_1012
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1013
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_1014
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "111.672515835794"^^xsd:double .

inst:IfcLengthMeasure_List_1011
        list:hasContents  inst:IfcLengthMeasure_1014 ;
        list:hasNext      inst:IfcLengthMeasure_List_1012 .

inst:IfcLengthMeasure_1015
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-112.209605260782"^^xsd:double .

inst:IfcLengthMeasure_List_1012
        list:hasContents  inst:IfcLengthMeasure_1015 ;
        list:hasNext      inst:IfcLengthMeasure_List_1013 .

inst:IfcLengthMeasure_List_1013
        list:hasContents  inst:IfcLengthMeasure_709 .

inst:IfcCartesianPoint_258
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_1016
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_258
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_1016 .

inst:IfcLengthMeasure_List_1017
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1018
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_1019
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "75.812198248967"^^xsd:double .

inst:IfcLengthMeasure_List_1016
        list:hasContents  inst:IfcLengthMeasure_1019 ;
        list:hasNext      inst:IfcLengthMeasure_List_1017 .

inst:IfcLengthMeasure_1020
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-126.124204222316"^^xsd:double .

inst:IfcLengthMeasure_List_1017
        list:hasContents  inst:IfcLengthMeasure_1020 ;
        list:hasNext      inst:IfcLengthMeasure_List_1018 .

inst:IfcLengthMeasure_List_1018
        list:hasContents  inst:IfcLengthMeasure_709 .

inst:IfcCartesianPoint_259
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_1021
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_259
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_1021 .

inst:IfcLengthMeasure_List_1022
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1023
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_1024
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "38.3440539351622"^^xsd:double .

inst:IfcLengthMeasure_List_1021
        list:hasContents  inst:IfcLengthMeasure_1024 ;
        list:hasNext      inst:IfcLengthMeasure_List_1022 .

inst:IfcLengthMeasure_1025
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-134.804771445296"^^xsd:double .

inst:IfcLengthMeasure_List_1022
        list:hasContents  inst:IfcLengthMeasure_1025 ;
        list:hasNext      inst:IfcLengthMeasure_List_1023 .

inst:IfcLengthMeasure_List_1023
        list:hasContents  inst:IfcLengthMeasure_709 .

inst:IfcCartesianPoint_260
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_1026
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_260
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_1026 .

inst:IfcLengthMeasure_List_1027
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1028
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_1029
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "0.00000562489231925"^^xsd:double .

inst:IfcLengthMeasure_List_1026
        list:hasContents  inst:IfcLengthMeasure_1029 ;
        list:hasNext      inst:IfcLengthMeasure_List_1027 .

inst:IfcLengthMeasure_1030
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-137.758996454453"^^xsd:double .

inst:IfcLengthMeasure_List_1027
        list:hasContents  inst:IfcLengthMeasure_1030 ;
        list:hasNext      inst:IfcLengthMeasure_List_1028 .

inst:IfcLengthMeasure_List_1028
        list:hasContents  inst:IfcLengthMeasure_709 .

inst:IfcCartesianPoint_261
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_1031
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_261
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_1031 .

inst:IfcLengthMeasure_List_1032
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1033
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_1034
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-38.3440494938959"^^xsd:double .

inst:IfcLengthMeasure_List_1031
        list:hasContents  inst:IfcLengthMeasure_1034 ;
        list:hasNext      inst:IfcLengthMeasure_List_1032 .

inst:IfcLengthMeasure_1035
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-134.804772131387"^^xsd:double .

inst:IfcLengthMeasure_List_1032
        list:hasContents  inst:IfcLengthMeasure_1035 ;
        list:hasNext      inst:IfcLengthMeasure_List_1033 .

inst:IfcLengthMeasure_List_1033
        list:hasContents  inst:IfcLengthMeasure_709 .

inst:IfcCartesianPoint_262
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_1036
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_262
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_1036 .

inst:IfcLengthMeasure_List_1037
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1038
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_1039
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-75.8121974100292"^^xsd:double .

inst:IfcLengthMeasure_List_1036
        list:hasContents  inst:IfcLengthMeasure_1039 ;
        list:hasNext      inst:IfcLengthMeasure_List_1037 .

inst:IfcLengthMeasure_1040
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-126.124204482417"^^xsd:double .

inst:IfcLengthMeasure_List_1037
        list:hasContents  inst:IfcLengthMeasure_1040 ;
        list:hasNext      inst:IfcLengthMeasure_List_1038 .

inst:IfcLengthMeasure_List_1038
        list:hasContents  inst:IfcLengthMeasure_709 .

inst:IfcCartesianPoint_263
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_1041
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_263
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_1041 .

inst:IfcLengthMeasure_List_1042
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1043
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_1044
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-111.672515690142"^^xsd:double .

inst:IfcLengthMeasure_List_1041
        list:hasContents  inst:IfcLengthMeasure_1044 ;
        list:hasNext      inst:IfcLengthMeasure_List_1042 .

inst:IfcLengthMeasure_1045
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-112.209605328934"^^xsd:double .

inst:IfcLengthMeasure_List_1042
        list:hasContents  inst:IfcLengthMeasure_1045 ;
        list:hasNext      inst:IfcLengthMeasure_List_1043 .

inst:IfcLengthMeasure_List_1043
        list:hasContents  inst:IfcLengthMeasure_709 .

inst:IfcCartesianPoint_264
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_1046
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_264
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_1046 .

inst:IfcLengthMeasure_List_1047
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1048
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_1049
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-145.419108622881"^^xsd:double .

inst:IfcLengthMeasure_List_1046
        list:hasContents  inst:IfcLengthMeasure_1049 ;
        list:hasNext      inst:IfcLengthMeasure_List_1047 .

inst:IfcLengthMeasure_1050
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-93.7387883895915"^^xsd:double .

inst:IfcLengthMeasure_List_1047
        list:hasContents  inst:IfcLengthMeasure_1050 ;
        list:hasNext      inst:IfcLengthMeasure_List_1048 .

inst:IfcLengthMeasure_List_1048
        list:hasContents  inst:IfcLengthMeasure_709 .

inst:IfcCartesianPoint_265
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_1051
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_265
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_1051 .

inst:IfcLengthMeasure_List_1052
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1053
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_1054
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-176.831543103321"^^xsd:double .

inst:IfcLengthMeasure_List_1051
        list:hasContents  inst:IfcLengthMeasure_1054 ;
        list:hasNext      inst:IfcLengthMeasure_List_1052 .

inst:IfcLengthMeasure_1055
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-71.5181978165611"^^xsd:double .

inst:IfcLengthMeasure_List_1052
        list:hasContents  inst:IfcLengthMeasure_1055 ;
        list:hasNext      inst:IfcLengthMeasure_List_1053 .

inst:IfcLengthMeasure_List_1053
        list:hasContents  inst:IfcLengthMeasure_709 .

inst:IfcCartesianPoint_266
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_1056
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_266
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_1056 .

inst:IfcLengthMeasure_List_1057
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1058
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_1059
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-205.820584751455"^^xsd:double .

inst:IfcLengthMeasure_List_1056
        list:hasContents  inst:IfcLengthMeasure_1059 ;
        list:hasNext      inst:IfcLengthMeasure_List_1057 .

inst:IfcLengthMeasure_1060
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-46.2135067401479"^^xsd:double .

inst:IfcLengthMeasure_List_1057
        list:hasContents  inst:IfcLengthMeasure_1060 ;
        list:hasNext      inst:IfcLengthMeasure_List_1058 .

inst:IfcLengthMeasure_List_1058
        list:hasContents  inst:IfcLengthMeasure_709 .

inst:IfcCartesianPoint_267
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_1061
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_267
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_1061 .

inst:IfcLengthMeasure_List_1062
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1063
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_1064
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-232.162080231681"^^xsd:double .

inst:IfcLengthMeasure_List_1061
        list:hasContents  inst:IfcLengthMeasure_1064 ;
        list:hasNext      inst:IfcLengthMeasure_List_1062 .

inst:IfcLengthMeasure_1065
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-18.164112680286"^^xsd:double .

inst:IfcLengthMeasure_List_1062
        list:hasContents  inst:IfcLengthMeasure_1065 ;
        list:hasNext      inst:IfcLengthMeasure_List_1063 .

inst:IfcLengthMeasure_List_1063
        list:hasContents  inst:IfcLengthMeasure_709 .

inst:IfcCartesianPoint_268
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_1066
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_268
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_1066 .

inst:IfcLengthMeasure_List_1067
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1068
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_1069
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-255.37651255073"^^xsd:double .

inst:IfcLengthMeasure_List_1066
        list:hasContents  inst:IfcLengthMeasure_1069 ;
        list:hasNext      inst:IfcLengthMeasure_List_1067 .

inst:IfcLengthMeasure_1070
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "12.5180866141667"^^xsd:double .

inst:IfcLengthMeasure_List_1067
        list:hasContents  inst:IfcLengthMeasure_1070 ;
        list:hasNext      inst:IfcLengthMeasure_List_1068 .

inst:IfcLengthMeasure_List_1068
        list:hasContents  inst:IfcLengthMeasure_709 .

inst:IfcCartesianPoint_269
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_1071
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_269
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_1071 .

inst:IfcLengthMeasure_List_1072
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1073
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_1074
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-274.528157992219"^^xsd:double .

inst:IfcLengthMeasure_List_1071
        list:hasContents  inst:IfcLengthMeasure_1074 ;
        list:hasNext      inst:IfcLengthMeasure_List_1072 .

inst:IfcLengthMeasure_1075
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "45.8738465115148"^^xsd:double .

inst:IfcLengthMeasure_List_1072
        list:hasContents  inst:IfcLengthMeasure_1075 ;
        list:hasNext      inst:IfcLengthMeasure_List_1073 .

inst:IfcLengthMeasure_List_1073
        list:hasContents  inst:IfcLengthMeasure_709 .

inst:IfcCartesianPoint_270
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_1076
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_270
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_1076 .

inst:IfcLengthMeasure_List_1077
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1078
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_1079
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-287.786482832935"^^xsd:double .

inst:IfcLengthMeasure_List_1076
        list:hasContents  inst:IfcLengthMeasure_1079 ;
        list:hasNext      inst:IfcLengthMeasure_List_1077 .

inst:IfcLengthMeasure_1080
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "81.9448557189429"^^xsd:double .

inst:IfcLengthMeasure_List_1077
        list:hasContents  inst:IfcLengthMeasure_1080 ;
        list:hasNext      inst:IfcLengthMeasure_List_1078 .

inst:IfcLengthMeasure_List_1078
        list:hasContents  inst:IfcLengthMeasure_709 .

inst:IfcCartesianPoint_271
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_1081
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_271
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_1081 .

inst:IfcLengthMeasure_List_1082
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1083
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_1084
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-291.585141527273"^^xsd:double .

inst:IfcLengthMeasure_List_1081
        list:hasContents  inst:IfcLengthMeasure_1084 ;
        list:hasNext      inst:IfcLengthMeasure_List_1082 .

inst:IfcLengthMeasure_1085
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "120.093339480335"^^xsd:double .

inst:IfcLengthMeasure_List_1082
        list:hasContents  inst:IfcLengthMeasure_1085 ;
        list:hasNext      inst:IfcLengthMeasure_List_1083 .

inst:IfcLengthMeasure_List_1083
        list:hasContents  inst:IfcLengthMeasure_709 .

inst:IfcCartesianPoint_272
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_1086
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_272
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_1086 .

inst:IfcLengthMeasure_List_1087
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1088
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_1089
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-280.765069841138"^^xsd:double .

inst:IfcLengthMeasure_List_1086
        list:hasContents  inst:IfcLengthMeasure_1089 ;
        list:hasNext      inst:IfcLengthMeasure_List_1087 .

inst:IfcLengthMeasure_1090
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "156.69906670217"^^xsd:double .

inst:IfcLengthMeasure_List_1087
        list:hasContents  inst:IfcLengthMeasure_1090 ;
        list:hasNext      inst:IfcLengthMeasure_List_1088 .

inst:IfcLengthMeasure_List_1088
        list:hasContents  inst:IfcLengthMeasure_709 .

inst:IfcCartesianPoint_273
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_1091
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_273
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_1091 .

inst:IfcLengthMeasure_List_1092
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1093
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_1094
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-256.087676107765"^^xsd:double .

inst:IfcLengthMeasure_List_1091
        list:hasContents  inst:IfcLengthMeasure_1094 ;
        list:hasNext      inst:IfcLengthMeasure_List_1092 .

inst:IfcLengthMeasure_1095
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "185.991167334279"^^xsd:double .

inst:IfcLengthMeasure_List_1092
        list:hasContents  inst:IfcLengthMeasure_1095 ;
        list:hasNext      inst:IfcLengthMeasure_List_1093 .

inst:IfcLengthMeasure_List_1093
        list:hasContents  inst:IfcLengthMeasure_709 .

inst:IfcCartesianPoint_274
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_1096
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_274
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_1096 .

inst:IfcLengthMeasure_List_1097
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1098
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_1099
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-224.265825523088"^^xsd:double .

inst:IfcLengthMeasure_List_1096
        list:hasContents  inst:IfcLengthMeasure_1099 ;
        list:hasNext      inst:IfcLengthMeasure_List_1097 .

inst:IfcLengthMeasure_1100
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "207.523991330957"^^xsd:double .

inst:IfcLengthMeasure_List_1097
        list:hasContents  inst:IfcLengthMeasure_1100 ;
        list:hasNext      inst:IfcLengthMeasure_List_1098 .

inst:IfcLengthMeasure_List_1098
        list:hasContents  inst:IfcLengthMeasure_709 .

inst:IfcCartesianPoint_275
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_1101
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_275
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_1101 .

inst:IfcLengthMeasure_List_1102
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1103
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_1104
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-189.184272565133"^^xsd:double .

inst:IfcLengthMeasure_List_1101
        list:hasContents  inst:IfcLengthMeasure_1104 ;
        list:hasNext      inst:IfcLengthMeasure_List_1102 .

inst:IfcLengthMeasure_1105
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "223.297730817421"^^xsd:double .

inst:IfcLengthMeasure_List_1102
        list:hasContents  inst:IfcLengthMeasure_1105 ;
        list:hasNext      inst:IfcLengthMeasure_List_1103 .

inst:IfcLengthMeasure_List_1103
        list:hasContents  inst:IfcLengthMeasure_709 .

inst:IfcCartesianPoint_276
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_1106
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_276
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_1106 .

inst:IfcLengthMeasure_List_1107
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1108
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_1109
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-152.483829695455"^^xsd:double .

inst:IfcLengthMeasure_List_1106
        list:hasContents  inst:IfcLengthMeasure_1109 ;
        list:hasNext      inst:IfcLengthMeasure_List_1107 .

inst:IfcLengthMeasure_1110
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "234.862971464014"^^xsd:double .

inst:IfcLengthMeasure_List_1107
        list:hasContents  inst:IfcLengthMeasure_1110 ;
        list:hasNext      inst:IfcLengthMeasure_List_1108 .

inst:IfcLengthMeasure_List_1108
        list:hasContents  inst:IfcLengthMeasure_709 .

inst:IfcCartesianPoint_277
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_1111
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_277
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_1111 .

inst:IfcLengthMeasure_List_1112
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1113
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_1114
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-114.905033181042"^^xsd:double .

inst:IfcLengthMeasure_List_1111
        list:hasContents  inst:IfcLengthMeasure_1114 ;
        list:hasNext      inst:IfcLengthMeasure_List_1112 .

inst:IfcLengthMeasure_1115
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "243.168612385642"^^xsd:double .

inst:IfcLengthMeasure_List_1112
        list:hasContents  inst:IfcLengthMeasure_1115 ;
        list:hasNext      inst:IfcLengthMeasure_List_1113 .

inst:IfcLengthMeasure_List_1113
        list:hasContents  inst:IfcLengthMeasure_709 .

inst:IfcCartesianPoint_278
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_1116
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_278
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_1116 .

inst:IfcLengthMeasure_List_1117
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1118
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_1119
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-76.8279738129532"^^xsd:double .

inst:IfcLengthMeasure_List_1116
        list:hasContents  inst:IfcLengthMeasure_1119 ;
        list:hasNext      inst:IfcLengthMeasure_List_1117 .

inst:IfcLengthMeasure_1120
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "248.781505081298"^^xsd:double .

inst:IfcLengthMeasure_List_1117
        list:hasContents  inst:IfcLengthMeasure_1120 ;
        list:hasNext      inst:IfcLengthMeasure_List_1118 .

inst:IfcLengthMeasure_List_1118
        list:hasContents  inst:IfcLengthMeasure_709 .

inst:IfcCartesianPoint_279
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_1121
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_279
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_1121 .

inst:IfcLengthMeasure_List_1122
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1123
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_1124
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-38.4757029862493"^^xsd:double .

inst:IfcLengthMeasure_List_1121
        list:hasContents  inst:IfcLengthMeasure_1124 ;
        list:hasNext      inst:IfcLengthMeasure_List_1122 .

inst:IfcLengthMeasure_1125
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "252.0334780278"^^xsd:double .

inst:IfcLengthMeasure_List_1122
        list:hasContents  inst:IfcLengthMeasure_1125 ;
        list:hasNext      inst:IfcLengthMeasure_List_1123 .

inst:IfcLengthMeasure_List_1123
        list:hasContents  inst:IfcLengthMeasure_709 .

inst:IfcCartesianPoint_280
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_1126
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_280
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_1126 .

inst:IfcLengthMeasure_List_1127
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1128
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_1129
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-157.154914340418"^^xsd:double .

inst:IfcLengthMeasure_List_1126
        list:hasContents  inst:IfcLengthMeasure_1129 ;
        list:hasNext      inst:IfcLengthMeasure_List_1127 .

inst:IfcLengthMeasure_1130
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "175.808617178122"^^xsd:double .

inst:IfcLengthMeasure_List_1127
        list:hasContents  inst:IfcLengthMeasure_1130 ;
        list:hasNext      inst:IfcLengthMeasure_List_1128 .

inst:IfcLengthMeasure_1131
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-93.9999999999991"^^xsd:double .

inst:IfcLengthMeasure_List_1128
        list:hasContents  inst:IfcLengthMeasure_1131 .

inst:IfcCartesianPoint_281
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_1132
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_281
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_1132 .

inst:IfcLengthMeasure_List_1133
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1134
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_1135
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-159.799786377107"^^xsd:double .

inst:IfcLengthMeasure_List_1132
        list:hasContents  inst:IfcLengthMeasure_1135 ;
        list:hasNext      inst:IfcLengthMeasure_List_1133 .

inst:IfcLengthMeasure_1136
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "153.46782071584"^^xsd:double .

inst:IfcLengthMeasure_List_1133
        list:hasContents  inst:IfcLengthMeasure_1136 ;
        list:hasNext      inst:IfcLengthMeasure_List_1134 .

inst:IfcLengthMeasure_List_1134
        list:hasContents  inst:IfcLengthMeasure_1131 .

inst:IfcCartesianPoint_282
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_1137
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_282
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_1137 .

inst:IfcLengthMeasure_List_1138
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1139
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_1140
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-156.563435238971"^^xsd:double .

inst:IfcLengthMeasure_List_1137
        list:hasContents  inst:IfcLengthMeasure_1140 ;
        list:hasNext      inst:IfcLengthMeasure_List_1138 .

inst:IfcLengthMeasure_1141
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "131.153861277019"^^xsd:double .

inst:IfcLengthMeasure_List_1138
        list:hasContents  inst:IfcLengthMeasure_1141 ;
        list:hasNext      inst:IfcLengthMeasure_List_1139 .

inst:IfcLengthMeasure_List_1139
        list:hasContents  inst:IfcLengthMeasure_1131 .

inst:IfcCartesianPoint_283
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_1142
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_283
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_1142 .

inst:IfcLengthMeasure_List_1143
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1144
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_1145
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-149.376131896764"^^xsd:double .

inst:IfcLengthMeasure_List_1142
        list:hasContents  inst:IfcLengthMeasure_1145 ;
        list:hasNext      inst:IfcLengthMeasure_List_1143 .

inst:IfcLengthMeasure_1146
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "109.760102145713"^^xsd:double .

inst:IfcLengthMeasure_List_1143
        list:hasContents  inst:IfcLengthMeasure_1146 ;
        list:hasNext      inst:IfcLengthMeasure_List_1144 .

inst:IfcLengthMeasure_List_1144
        list:hasContents  inst:IfcLengthMeasure_1131 .

inst:IfcCartesianPoint_284
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_1147
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_284
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_1147 .

inst:IfcLengthMeasure_List_1148
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1149
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_1150
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-139.405144871723"^^xsd:double .

inst:IfcLengthMeasure_List_1147
        list:hasContents  inst:IfcLengthMeasure_1150 ;
        list:hasNext      inst:IfcLengthMeasure_List_1148 .

inst:IfcLengthMeasure_1151
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "89.5038839334345"^^xsd:double .

inst:IfcLengthMeasure_List_1148
        list:hasContents  inst:IfcLengthMeasure_1151 ;
        list:hasNext      inst:IfcLengthMeasure_List_1149 .

inst:IfcLengthMeasure_List_1149
        list:hasContents  inst:IfcLengthMeasure_1131 .

inst:IfcCartesianPoint_285
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_1152
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_285
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_1152 .

inst:IfcLengthMeasure_List_1153
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1154
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_1155
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-127.294335499358"^^xsd:double .

inst:IfcLengthMeasure_List_1152
        list:hasContents  inst:IfcLengthMeasure_1155 ;
        list:hasNext      inst:IfcLengthMeasure_List_1153 .

inst:IfcLengthMeasure_1156
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "70.445523587566"^^xsd:double .

inst:IfcLengthMeasure_List_1153
        list:hasContents  inst:IfcLengthMeasure_1156 ;
        list:hasNext      inst:IfcLengthMeasure_List_1154 .

inst:IfcLengthMeasure_List_1154
        list:hasContents  inst:IfcLengthMeasure_1131 .

inst:IfcCartesianPoint_286
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_1157
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_286
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_1157 .

inst:IfcLengthMeasure_List_1158
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1159
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_1160
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-113.387459490709"^^xsd:double .

inst:IfcLengthMeasure_List_1157
        list:hasContents  inst:IfcLengthMeasure_1160 ;
        list:hasNext      inst:IfcLengthMeasure_List_1158 .

inst:IfcLengthMeasure_1161
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "52.6538727330131"^^xsd:double .

inst:IfcLengthMeasure_List_1158
        list:hasContents  inst:IfcLengthMeasure_1161 ;
        list:hasNext      inst:IfcLengthMeasure_List_1159 .

inst:IfcLengthMeasure_List_1159
        list:hasContents  inst:IfcLengthMeasure_1131 .

inst:IfcCartesianPoint_287
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_1162
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_287
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_1162 .

inst:IfcLengthMeasure_List_1163
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1164
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_1165
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-97.8447661146846"^^xsd:double .

inst:IfcLengthMeasure_List_1162
        list:hasContents  inst:IfcLengthMeasure_1165 ;
        list:hasNext      inst:IfcLengthMeasure_List_1163 .

inst:IfcLengthMeasure_1166
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "36.2725918775068"^^xsd:double .

inst:IfcLengthMeasure_List_1163
        list:hasContents  inst:IfcLengthMeasure_1166 ;
        list:hasNext      inst:IfcLengthMeasure_List_1164 .

inst:IfcLengthMeasure_List_1164
        list:hasContents  inst:IfcLengthMeasure_1131 .

inst:IfcCartesianPoint_288
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_1167
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_288
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_1167 .

inst:IfcLengthMeasure_List_1168
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1169
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_1170
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-80.698001162248"^^xsd:double .

inst:IfcLengthMeasure_List_1167
        list:hasContents  inst:IfcLengthMeasure_1170 ;
        list:hasNext      inst:IfcLengthMeasure_List_1168 .

inst:IfcLengthMeasure_1171
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "21.582584637739"^^xsd:double .

inst:IfcLengthMeasure_List_1168
        list:hasContents  inst:IfcLengthMeasure_1171 ;
        list:hasNext      inst:IfcLengthMeasure_List_1169 .

inst:IfcLengthMeasure_List_1169
        list:hasContents  inst:IfcLengthMeasure_1131 .

inst:IfcCartesianPoint_289
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_1172
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_289
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_1172 .

inst:IfcLengthMeasure_List_1173
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1174
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_1175
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-61.8871867593298"^^xsd:double .

inst:IfcLengthMeasure_List_1172
        list:hasContents  inst:IfcLengthMeasure_1175 ;
        list:hasNext      inst:IfcLengthMeasure_List_1173 .

inst:IfcLengthMeasure_1176
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "9.10501350136987"^^xsd:double .

inst:IfcLengthMeasure_List_1173
        list:hasContents  inst:IfcLengthMeasure_1176 ;
        list:hasNext      inst:IfcLengthMeasure_List_1174 .

inst:IfcLengthMeasure_List_1174
        list:hasContents  inst:IfcLengthMeasure_1131 .

inst:IfcCartesianPoint_290
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_1177
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_290
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_1177 .

inst:IfcLengthMeasure_List_1178
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1179
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_1180
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-41.4394014166823"^^xsd:double .

inst:IfcLengthMeasure_List_1177
        list:hasContents  inst:IfcLengthMeasure_1180 ;
        list:hasNext      inst:IfcLengthMeasure_List_1178 .

inst:IfcLengthMeasure_1181
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-0.440153063918815"^^xsd:double .

inst:IfcLengthMeasure_List_1178
        list:hasContents  inst:IfcLengthMeasure_1181 ;
        list:hasNext      inst:IfcLengthMeasure_List_1179 .

inst:IfcLengthMeasure_List_1179
        list:hasContents  inst:IfcLengthMeasure_1131 .

inst:IfcCartesianPoint_291
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_1182
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_291
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_1182 .

inst:IfcLengthMeasure_List_1183
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1184
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_1185
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-19.662998563232"^^xsd:double .

inst:IfcLengthMeasure_List_1182
        list:hasContents  inst:IfcLengthMeasure_1185 ;
        list:hasNext      inst:IfcLengthMeasure_List_1183 .

inst:IfcLengthMeasure_1186
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-6.32985285628291"^^xsd:double .

inst:IfcLengthMeasure_List_1183
        list:hasContents  inst:IfcLengthMeasure_1186 ;
        list:hasNext      inst:IfcLengthMeasure_List_1184 .

inst:IfcLengthMeasure_List_1184
        list:hasContents  inst:IfcLengthMeasure_1131 .

inst:IfcCartesianPoint_292
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_1187
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_292
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_1187 .

inst:IfcLengthMeasure_List_1188
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1189
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_1190
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "2.83012817778957"^^xsd:double .

inst:IfcLengthMeasure_List_1187
        list:hasContents  inst:IfcLengthMeasure_1190 ;
        list:hasNext      inst:IfcLengthMeasure_List_1188 .

inst:IfcLengthMeasure_1191
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-7.98927385674093"^^xsd:double .

inst:IfcLengthMeasure_List_1188
        list:hasContents  inst:IfcLengthMeasure_1191 ;
        list:hasNext      inst:IfcLengthMeasure_List_1189 .

inst:IfcLengthMeasure_List_1189
        list:hasContents  inst:IfcLengthMeasure_1131 .

inst:IfcCartesianPoint_293
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_1192
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_293
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_1192 .

inst:IfcLengthMeasure_List_1193
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1194
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_1195
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "25.2161298976784"^^xsd:double .

inst:IfcLengthMeasure_List_1192
        list:hasContents  inst:IfcLengthMeasure_1195 ;
        list:hasNext      inst:IfcLengthMeasure_List_1193 .

inst:IfcLengthMeasure_1196
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-5.23363177905661"^^xsd:double .

inst:IfcLengthMeasure_List_1193
        list:hasContents  inst:IfcLengthMeasure_1196 ;
        list:hasNext      inst:IfcLengthMeasure_List_1194 .

inst:IfcLengthMeasure_List_1194
        list:hasContents  inst:IfcLengthMeasure_1131 .

inst:IfcCartesianPoint_294
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_1197
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_294
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_1197 .

inst:IfcLengthMeasure_List_1198
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1199
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_1200
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "46.7052656338443"^^xsd:double .

inst:IfcLengthMeasure_List_1197
        list:hasContents  inst:IfcLengthMeasure_1200 ;
        list:hasNext      inst:IfcLengthMeasure_List_1198 .

inst:IfcLengthMeasure_1201
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "1.63611866403177"^^xsd:double .

inst:IfcLengthMeasure_List_1198
        list:hasContents  inst:IfcLengthMeasure_1201 ;
        list:hasNext      inst:IfcLengthMeasure_List_1199 .

inst:IfcLengthMeasure_List_1199
        list:hasContents  inst:IfcLengthMeasure_1131 .

inst:IfcCartesianPoint_295
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_1202
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_295
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_1202 .

inst:IfcLengthMeasure_List_1203
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1204
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_1205
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "66.7603568037461"^^xsd:double .

inst:IfcLengthMeasure_List_1202
        list:hasContents  inst:IfcLengthMeasure_1205 ;
        list:hasNext      inst:IfcLengthMeasure_List_1203 .

inst:IfcLengthMeasure_1206
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "11.9848882796374"^^xsd:double .

inst:IfcLengthMeasure_List_1203
        list:hasContents  inst:IfcLengthMeasure_1206 ;
        list:hasNext      inst:IfcLengthMeasure_List_1204 .

inst:IfcLengthMeasure_List_1204
        list:hasContents  inst:IfcLengthMeasure_1131 .

inst:IfcCartesianPoint_296
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_1207
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_296
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_1207 .

inst:IfcLengthMeasure_List_1208
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1209
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_1210
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "85.1475914236449"^^xsd:double .

inst:IfcLengthMeasure_List_1207
        list:hasContents  inst:IfcLengthMeasure_1210 ;
        list:hasNext      inst:IfcLengthMeasure_List_1208 .

inst:IfcLengthMeasure_1211
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "25.0817028555501"^^xsd:double .

inst:IfcLengthMeasure_List_1208
        list:hasContents  inst:IfcLengthMeasure_1211 ;
        list:hasNext      inst:IfcLengthMeasure_List_1209 .

inst:IfcLengthMeasure_List_1209
        list:hasContents  inst:IfcLengthMeasure_1131 .

inst:IfcCartesianPoint_297
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_1212
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_297
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_1212 .

inst:IfcLengthMeasure_List_1213
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1214
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_1215
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "101.889510015946"^^xsd:double .

inst:IfcLengthMeasure_List_1212
        list:hasContents  inst:IfcLengthMeasure_1215 ;
        list:hasNext      inst:IfcLengthMeasure_List_1213 .

inst:IfcLengthMeasure_1216
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "40.2328202653944"^^xsd:double .

inst:IfcLengthMeasure_List_1213
        list:hasContents  inst:IfcLengthMeasure_1216 ;
        list:hasNext      inst:IfcLengthMeasure_List_1214 .

inst:IfcLengthMeasure_List_1214
        list:hasContents  inst:IfcLengthMeasure_1131 .

inst:IfcCartesianPoint_298
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_1217
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_298
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_1217 .

inst:IfcLengthMeasure_List_1218
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1219
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_1220
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "117.029708390029"^^xsd:double .

inst:IfcLengthMeasure_List_1217
        list:hasContents  inst:IfcLengthMeasure_1220 ;
        list:hasNext      inst:IfcLengthMeasure_List_1218 .

inst:IfcLengthMeasure_1221
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "56.9871779332136"^^xsd:double .

inst:IfcLengthMeasure_List_1218
        list:hasContents  inst:IfcLengthMeasure_1221 ;
        list:hasNext      inst:IfcLengthMeasure_List_1219 .

inst:IfcLengthMeasure_List_1219
        list:hasContents  inst:IfcLengthMeasure_1131 .

inst:IfcCartesianPoint_299
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_1222
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_299
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_1222 .

inst:IfcLengthMeasure_List_1223
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1224
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_1225
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "130.507477385555"^^xsd:double .

inst:IfcLengthMeasure_List_1222
        list:hasContents  inst:IfcLengthMeasure_1225 ;
        list:hasNext      inst:IfcLengthMeasure_List_1223 .

inst:IfcLengthMeasure_1226
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "75.1059075118196"^^xsd:double .

inst:IfcLengthMeasure_List_1223
        list:hasContents  inst:IfcLengthMeasure_1226 ;
        list:hasNext      inst:IfcLengthMeasure_List_1224 .

inst:IfcLengthMeasure_List_1224
        list:hasContents  inst:IfcLengthMeasure_1131 .

inst:IfcCartesianPoint_300
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_1227
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_300
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_1227 .

inst:IfcLengthMeasure_List_1228
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1229
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_1230
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "142.123444996071"^^xsd:double .

inst:IfcLengthMeasure_List_1227
        list:hasContents  inst:IfcLengthMeasure_1230 ;
        list:hasNext      inst:IfcLengthMeasure_List_1228 .

inst:IfcLengthMeasure_1231
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "94.4691487187488"^^xsd:double .

inst:IfcLengthMeasure_List_1228
        list:hasContents  inst:IfcLengthMeasure_1231 ;
        list:hasNext      inst:IfcLengthMeasure_List_1229 .

inst:IfcLengthMeasure_List_1229
        list:hasContents  inst:IfcLengthMeasure_1131 .

inst:IfcCartesianPoint_301
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_1232
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_301
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_1232 .

inst:IfcLengthMeasure_List_1233
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1234
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_1235
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "151.473017550452"^^xsd:double .

inst:IfcLengthMeasure_List_1232
        list:hasContents  inst:IfcLengthMeasure_1235 ;
        list:hasNext      inst:IfcLengthMeasure_List_1233 .

inst:IfcLengthMeasure_1236
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "115.017962619083"^^xsd:double .

inst:IfcLengthMeasure_List_1233
        list:hasContents  inst:IfcLengthMeasure_1236 ;
        list:hasNext      inst:IfcLengthMeasure_List_1234 .

inst:IfcLengthMeasure_List_1234
        list:hasContents  inst:IfcLengthMeasure_1131 .

inst:IfcCartesianPoint_302
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_1237
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_302
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_1237 .

inst:IfcLengthMeasure_List_1238
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1239
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_1240
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "157.807019966901"^^xsd:double .

inst:IfcLengthMeasure_List_1237
        list:hasContents  inst:IfcLengthMeasure_1240 ;
        list:hasNext      inst:IfcLengthMeasure_List_1238 .

inst:IfcLengthMeasure_1241
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "136.675894195075"^^xsd:double .

inst:IfcLengthMeasure_List_1238
        list:hasContents  inst:IfcLengthMeasure_1241 ;
        list:hasNext      inst:IfcLengthMeasure_List_1239 .

inst:IfcLengthMeasure_List_1239
        list:hasContents  inst:IfcLengthMeasure_1131 .

inst:IfcCartesianPoint_303
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_1242
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_303
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_1242 .

inst:IfcLengthMeasure_List_1243
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1244
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_1245
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "159.780564770642"^^xsd:double .

inst:IfcLengthMeasure_List_1242
        list:hasContents  inst:IfcLengthMeasure_1245 ;
        list:hasNext      inst:IfcLengthMeasure_List_1243 .

inst:IfcLengthMeasure_1246
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "159.127525554679"^^xsd:double .

inst:IfcLengthMeasure_List_1243
        list:hasContents  inst:IfcLengthMeasure_1246 ;
        list:hasNext      inst:IfcLengthMeasure_List_1244 .

inst:IfcLengthMeasure_List_1244
        list:hasContents  inst:IfcLengthMeasure_1131 .

inst:IfcCartesianPoint_304
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_1247
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_304
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_1247 .

inst:IfcLengthMeasure_List_1248
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1249
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_1250
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "155.311843754385"^^xsd:double .

inst:IfcLengthMeasure_List_1247
        list:hasContents  inst:IfcLengthMeasure_1250 ;
        list:hasNext      inst:IfcLengthMeasure_List_1248 .

inst:IfcLengthMeasure_1251
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "181.158856579337"^^xsd:double .

inst:IfcLengthMeasure_List_1248
        list:hasContents  inst:IfcLengthMeasure_1251 ;
        list:hasNext      inst:IfcLengthMeasure_List_1249 .

inst:IfcLengthMeasure_List_1249
        list:hasContents  inst:IfcLengthMeasure_1131 .

inst:IfcCartesianPoint_305
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_1252
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_305
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_1252 .

inst:IfcLengthMeasure_List_1253
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1254
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_1255
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "143.527109004701"^^xsd:double .

inst:IfcLengthMeasure_List_1252
        list:hasContents  inst:IfcLengthMeasure_1255 ;
        list:hasNext      inst:IfcLengthMeasure_List_1253 .

inst:IfcLengthMeasure_1256
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "200.308104946409"^^xsd:double .

inst:IfcLengthMeasure_List_1253
        list:hasContents  inst:IfcLengthMeasure_1256 ;
        list:hasNext      inst:IfcLengthMeasure_List_1254 .

inst:IfcLengthMeasure_List_1254
        list:hasContents  inst:IfcLengthMeasure_1131 .

inst:IfcCartesianPoint_306
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_1257
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_306
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_1257 .

inst:IfcLengthMeasure_List_1258
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1259
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_1260
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "126.728646947386"^^xsd:double .

inst:IfcLengthMeasure_List_1257
        list:hasContents  inst:IfcLengthMeasure_1260 ;
        list:hasNext      inst:IfcLengthMeasure_List_1258 .

inst:IfcLengthMeasure_1261
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "215.328663366711"^^xsd:double .

inst:IfcLengthMeasure_List_1258
        list:hasContents  inst:IfcLengthMeasure_1261 ;
        list:hasNext      inst:IfcLengthMeasure_List_1259 .

inst:IfcLengthMeasure_List_1259
        list:hasContents  inst:IfcLengthMeasure_1131 .

inst:IfcCartesianPoint_307
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_1262
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_307
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_1262 .

inst:IfcLengthMeasure_List_1263
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1264
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_1265
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "107.202467334812"^^xsd:double .

inst:IfcLengthMeasure_List_1262
        list:hasContents  inst:IfcLengthMeasure_1265 ;
        list:hasNext      inst:IfcLengthMeasure_List_1263 .

inst:IfcLengthMeasure_1266
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "226.634520930962"^^xsd:double .

inst:IfcLengthMeasure_List_1263
        list:hasContents  inst:IfcLengthMeasure_1266 ;
        list:hasNext      inst:IfcLengthMeasure_List_1264 .

inst:IfcLengthMeasure_List_1264
        list:hasContents  inst:IfcLengthMeasure_1131 .

inst:IfcCartesianPoint_308
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_1267
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_308
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_1267 .

inst:IfcLengthMeasure_List_1268
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1269
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_1270
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "86.2268790106761"^^xsd:double .

inst:IfcLengthMeasure_List_1267
        list:hasContents  inst:IfcLengthMeasure_1270 ;
        list:hasNext      inst:IfcLengthMeasure_List_1268 .

inst:IfcLengthMeasure_1271
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "234.98230335346"^^xsd:double .

inst:IfcLengthMeasure_List_1268
        list:hasContents  inst:IfcLengthMeasure_1271 ;
        list:hasNext      inst:IfcLengthMeasure_List_1269 .

inst:IfcLengthMeasure_List_1269
        list:hasContents  inst:IfcLengthMeasure_1131 .

inst:IfcCartesianPoint_309
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_1272
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_309
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_1272 .

inst:IfcLengthMeasure_List_1273
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1274
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_1275
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "64.4507343905464"^^xsd:double .

inst:IfcLengthMeasure_List_1272
        list:hasContents  inst:IfcLengthMeasure_1275 ;
        list:hasNext      inst:IfcLengthMeasure_List_1273 .

inst:IfcLengthMeasure_1276
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "240.958334023702"^^xsd:double .

inst:IfcLengthMeasure_List_1273
        list:hasContents  inst:IfcLengthMeasure_1276 ;
        list:hasNext      inst:IfcLengthMeasure_List_1274 .

inst:IfcLengthMeasure_List_1274
        list:hasContents  inst:IfcLengthMeasure_1131 .

inst:IfcCartesianPoint_310
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_1277
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_310
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_1277 .

inst:IfcLengthMeasure_List_1278
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1279
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_1280
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "42.2218098296953"^^xsd:double .

inst:IfcLengthMeasure_List_1277
        list:hasContents  inst:IfcLengthMeasure_1280 ;
        list:hasNext      inst:IfcLengthMeasure_List_1278 .

inst:IfcLengthMeasure_1281
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "244.946818741202"^^xsd:double .

inst:IfcLengthMeasure_List_1278
        list:hasContents  inst:IfcLengthMeasure_1281 ;
        list:hasNext      inst:IfcLengthMeasure_List_1279 .

inst:IfcLengthMeasure_List_1279
        list:hasContents  inst:IfcLengthMeasure_1131 .

inst:IfcCartesianPoint_311
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_1282
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_311
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_1282 .

inst:IfcLengthMeasure_List_1283
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1284
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_1285
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "19.7473348710258"^^xsd:double .

inst:IfcLengthMeasure_List_1282
        list:hasContents  inst:IfcLengthMeasure_1285 ;
        list:hasNext      inst:IfcLengthMeasure_List_1283 .

inst:IfcLengthMeasure_1286
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "247.180277362528"^^xsd:double .

inst:IfcLengthMeasure_List_1283
        list:hasContents  inst:IfcLengthMeasure_1286 ;
        list:hasNext      inst:IfcLengthMeasure_List_1284 .

inst:IfcLengthMeasure_List_1284
        list:hasContents  inst:IfcLengthMeasure_1131 .

inst:IfcCartesianPoint_312
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_1287
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_312
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_1287 .

inst:IfcLengthMeasure_List_1288
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1289
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_1290
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-2.83037829161053"^^xsd:double .

inst:IfcLengthMeasure_List_1287
        list:hasContents  inst:IfcLengthMeasure_1290 ;
        list:hasNext      inst:IfcLengthMeasure_List_1288 .

inst:IfcLengthMeasure_1291
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "247.779902642456"^^xsd:double .

inst:IfcLengthMeasure_List_1288
        list:hasContents  inst:IfcLengthMeasure_1291 ;
        list:hasNext      inst:IfcLengthMeasure_List_1289 .

inst:IfcLengthMeasure_List_1289
        list:hasContents  inst:IfcLengthMeasure_1131 .

inst:IfcCartesianPoint_313
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_1292
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_313
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_1292 .

inst:IfcLengthMeasure_List_1293
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1294
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_1295
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-25.3937160660832"^^xsd:double .

inst:IfcLengthMeasure_List_1292
        list:hasContents  inst:IfcLengthMeasure_1295 ;
        list:hasNext      inst:IfcLengthMeasure_List_1293 .

inst:IfcLengthMeasure_1296
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "246.777117598331"^^xsd:double .

inst:IfcLengthMeasure_List_1293
        list:hasContents  inst:IfcLengthMeasure_1296 ;
        list:hasNext      inst:IfcLengthMeasure_List_1294 .

inst:IfcLengthMeasure_List_1294
        list:hasContents  inst:IfcLengthMeasure_1131 .

inst:IfcCartesianPoint_314
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_1297
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_314
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_1297 .

inst:IfcLengthMeasure_List_1298
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1299
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_1300
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-47.8217104720131"^^xsd:double .

inst:IfcLengthMeasure_List_1297
        list:hasContents  inst:IfcLengthMeasure_1300 ;
        list:hasNext      inst:IfcLengthMeasure_List_1298 .

inst:IfcLengthMeasure_1301
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "244.119106659353"^^xsd:double .

inst:IfcLengthMeasure_List_1298
        list:hasContents  inst:IfcLengthMeasure_1301 ;
        list:hasNext      inst:IfcLengthMeasure_List_1299 .

inst:IfcLengthMeasure_List_1299
        list:hasContents  inst:IfcLengthMeasure_1131 .

inst:IfcCartesianPoint_315
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_1302
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_315
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_1302 .

inst:IfcLengthMeasure_List_1303
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1304
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_1305
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-69.9604240039862"^^xsd:double .

inst:IfcLengthMeasure_List_1302
        list:hasContents  inst:IfcLengthMeasure_1305 ;
        list:hasNext      inst:IfcLengthMeasure_List_1303 .

inst:IfcLengthMeasure_1306
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "239.659498513825"^^xsd:double .

inst:IfcLengthMeasure_List_1303
        list:hasContents  inst:IfcLengthMeasure_1306 ;
        list:hasNext      inst:IfcLengthMeasure_List_1304 .

inst:IfcLengthMeasure_List_1304
        list:hasContents  inst:IfcLengthMeasure_1131 .

inst:IfcCartesianPoint_316
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_1307
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_316
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_1307 .

inst:IfcLengthMeasure_List_1308
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1309
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_1310
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-91.576683901205"^^xsd:double .

inst:IfcLengthMeasure_List_1307
        list:hasContents  inst:IfcLengthMeasure_1310 ;
        list:hasNext      inst:IfcLengthMeasure_List_1308 .

inst:IfcLengthMeasure_1311
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "233.132310979361"^^xsd:double .

inst:IfcLengthMeasure_List_1308
        list:hasContents  inst:IfcLengthMeasure_1311 ;
        list:hasNext      inst:IfcLengthMeasure_List_1309 .

inst:IfcLengthMeasure_List_1309
        list:hasContents  inst:IfcLengthMeasure_1131 .

inst:IfcCartesianPoint_317
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_1312
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_317
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_1312 .

inst:IfcLengthMeasure_List_1313
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1314
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_1315
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-112.267364103545"^^xsd:double .

inst:IfcLengthMeasure_List_1312
        list:hasContents  inst:IfcLengthMeasure_1315 ;
        list:hasNext      inst:IfcLengthMeasure_List_1313 .

inst:IfcLengthMeasure_1316
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "224.107056891456"^^xsd:double .

inst:IfcLengthMeasure_List_1313
        list:hasContents  inst:IfcLengthMeasure_1316 ;
        list:hasNext      inst:IfcLengthMeasure_List_1314 .

inst:IfcLengthMeasure_List_1314
        list:hasContents  inst:IfcLengthMeasure_1131 .

inst:IfcCartesianPoint_318
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_1317
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_318
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_1317 .

inst:IfcLengthMeasure_List_1318
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1319
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_1320
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-131.264894314891"^^xsd:double .

inst:IfcLengthMeasure_List_1317
        list:hasContents  inst:IfcLengthMeasure_1320 ;
        list:hasNext      inst:IfcLengthMeasure_List_1318 .

inst:IfcLengthMeasure_1321
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "211.943402767825"^^xsd:double .

inst:IfcLengthMeasure_List_1318
        list:hasContents  inst:IfcLengthMeasure_1321 ;
        list:hasNext      inst:IfcLengthMeasure_List_1319 .

inst:IfcLengthMeasure_List_1319
        list:hasContents  inst:IfcLengthMeasure_1131 .

inst:IfcCartesianPoint_319
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_1322
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_319
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_1322 .

inst:IfcLengthMeasure_List_1323
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1324
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_1325
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-147.061746316906"^^xsd:double .

inst:IfcLengthMeasure_List_1322
        list:hasContents  inst:IfcLengthMeasure_1325 ;
        list:hasNext      inst:IfcLengthMeasure_List_1323 .

inst:IfcLengthMeasure_1326
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "195.888143420344"^^xsd:double .

inst:IfcLengthMeasure_List_1323
        list:hasContents  inst:IfcLengthMeasure_1326 ;
        list:hasNext      inst:IfcLengthMeasure_List_1324 .

inst:IfcLengthMeasure_List_1324
        list:hasContents  inst:IfcLengthMeasure_1131 .

inst:IfcCartesianPoint_320
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_1327
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_320
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_1327 .

inst:IfcLengthMeasure_List_1328
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1329
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1327
        list:hasContents  inst:IfcLengthMeasure_709 ;
        list:hasNext      inst:IfcLengthMeasure_List_1328 .

inst:IfcLengthMeasure_1330
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "247.792422124388"^^xsd:double .

inst:IfcLengthMeasure_List_1328
        list:hasContents  inst:IfcLengthMeasure_1330 ;
        list:hasNext      inst:IfcLengthMeasure_List_1329 .

inst:IfcLengthMeasure_1331
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-83.9999999999991"^^xsd:double .

inst:IfcLengthMeasure_List_1329
        list:hasContents  inst:IfcLengthMeasure_1331 .

inst:IfcCartesianPoint_321
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_1332
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_321
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_1332 .

inst:IfcLengthMeasure_List_1333
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1334
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_1335
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-22.5714426203723"^^xsd:double .

inst:IfcLengthMeasure_List_1332
        list:hasContents  inst:IfcLengthMeasure_1335 ;
        list:hasNext      inst:IfcLengthMeasure_List_1333 .

inst:IfcLengthMeasure_1336
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "246.991542511388"^^xsd:double .

inst:IfcLengthMeasure_List_1333
        list:hasContents  inst:IfcLengthMeasure_1336 ;
        list:hasNext      inst:IfcLengthMeasure_List_1334 .

inst:IfcLengthMeasure_List_1334
        list:hasContents  inst:IfcLengthMeasure_1331 .

inst:IfcCartesianPoint_322
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_1337
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_322
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_1337 .

inst:IfcLengthMeasure_List_1338
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1339
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_1340
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-45.0238143941789"^^xsd:double .

inst:IfcLengthMeasure_List_1337
        list:hasContents  inst:IfcLengthMeasure_1340 ;
        list:hasNext      inst:IfcLengthMeasure_List_1338 .

inst:IfcLengthMeasure_1341
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "244.546840992317"^^xsd:double .

inst:IfcLengthMeasure_List_1338
        list:hasContents  inst:IfcLengthMeasure_1341 ;
        list:hasNext      inst:IfcLengthMeasure_List_1339 .

inst:IfcLengthMeasure_List_1339
        list:hasContents  inst:IfcLengthMeasure_1331 .

inst:IfcCartesianPoint_323
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_1342
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_323
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_1342 .

inst:IfcLengthMeasure_List_1343
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1344
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_1345
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-67.2093088278616"^^xsd:double .

inst:IfcLengthMeasure_List_1342
        list:hasContents  inst:IfcLengthMeasure_1345 ;
        list:hasNext      inst:IfcLengthMeasure_List_1343 .

inst:IfcLengthMeasure_1346
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "240.324720105693"^^xsd:double .

inst:IfcLengthMeasure_List_1343
        list:hasContents  inst:IfcLengthMeasure_1346 ;
        list:hasNext      inst:IfcLengthMeasure_List_1344 .

inst:IfcLengthMeasure_List_1344
        list:hasContents  inst:IfcLengthMeasure_1331 .

inst:IfcCartesianPoint_324
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_1347
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_324
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_1347 .

inst:IfcLengthMeasure_List_1348
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1349
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_1350
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-88.9083392880943"^^xsd:double .

inst:IfcLengthMeasure_List_1347
        list:hasContents  inst:IfcLengthMeasure_1350 ;
        list:hasNext      inst:IfcLengthMeasure_List_1348 .

inst:IfcLengthMeasure_1351
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "234.076268171425"^^xsd:double .

inst:IfcLengthMeasure_List_1348
        list:hasContents  inst:IfcLengthMeasure_1351 ;
        list:hasNext      inst:IfcLengthMeasure_List_1349 .

inst:IfcLengthMeasure_List_1349
        list:hasContents  inst:IfcLengthMeasure_1331 .

inst:IfcCartesianPoint_325
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_1352
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_325
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_1352 .

inst:IfcLengthMeasure_List_1353
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1354
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_1355
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-109.746783760019"^^xsd:double .

inst:IfcLengthMeasure_List_1352
        list:hasContents  inst:IfcLengthMeasure_1355 ;
        list:hasNext      inst:IfcLengthMeasure_List_1353 .

inst:IfcLengthMeasure_1356
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "225.394569776618"^^xsd:double .

inst:IfcLengthMeasure_List_1353
        list:hasContents  inst:IfcLengthMeasure_1356 ;
        list:hasNext      inst:IfcLengthMeasure_List_1354 .

inst:IfcLengthMeasure_List_1354
        list:hasContents  inst:IfcLengthMeasure_1331 .

inst:IfcCartesianPoint_326
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_1357
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_326
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_1357 .

inst:IfcLengthMeasure_List_1358
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1359
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_1360
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-129.019214178155"^^xsd:double .

inst:IfcLengthMeasure_List_1357
        list:hasContents  inst:IfcLengthMeasure_1360 ;
        list:hasNext      inst:IfcLengthMeasure_List_1358 .

inst:IfcLengthMeasure_1361
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "213.666092805292"^^xsd:double .

inst:IfcLengthMeasure_List_1358
        list:hasContents  inst:IfcLengthMeasure_1361 ;
        list:hasNext      inst:IfcLengthMeasure_List_1359 .

inst:IfcLengthMeasure_List_1359
        list:hasContents  inst:IfcLengthMeasure_1331 .

inst:IfcCartesianPoint_327
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_1362
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_327
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_1362 .

inst:IfcLengthMeasure_List_1363
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1364
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_1365
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-145.336255413146"^^xsd:double .

inst:IfcLengthMeasure_List_1362
        list:hasContents  inst:IfcLengthMeasure_1365 ;
        list:hasNext      inst:IfcLengthMeasure_List_1363 .

inst:IfcLengthMeasure_1366
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "198.131561931792"^^xsd:double .

inst:IfcLengthMeasure_List_1363
        list:hasContents  inst:IfcLengthMeasure_1366 ;
        list:hasNext      inst:IfcLengthMeasure_List_1364 .

inst:IfcLengthMeasure_List_1364
        list:hasContents  inst:IfcLengthMeasure_1331 .

inst:IfcCartesianPoint_328
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_1367
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_328
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_1367 .

inst:IfcLengthMeasure_List_1368
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1369
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_1370
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-156.295574267743"^^xsd:double .

inst:IfcLengthMeasure_List_1367
        list:hasContents  inst:IfcLengthMeasure_1370 ;
        list:hasNext      inst:IfcLengthMeasure_List_1368 .

inst:IfcLengthMeasure_1371
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "178.505166800292"^^xsd:double .

inst:IfcLengthMeasure_List_1368
        list:hasContents  inst:IfcLengthMeasure_1371 ;
        list:hasNext      inst:IfcLengthMeasure_List_1369 .

inst:IfcLengthMeasure_List_1369
        list:hasContents  inst:IfcLengthMeasure_1331 .

inst:IfcCartesianPoint_329
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_1372
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_329
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_1372 .

inst:IfcLengthMeasure_List_1373
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1374
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_1375
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-159.838406313243"^^xsd:double .

inst:IfcLengthMeasure_List_1372
        list:hasContents  inst:IfcLengthMeasure_1375 ;
        list:hasNext      inst:IfcLengthMeasure_List_1373 .

inst:IfcLengthMeasure_1376
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "156.297849730896"^^xsd:double .

inst:IfcLengthMeasure_List_1373
        list:hasContents  inst:IfcLengthMeasure_1376 ;
        list:hasNext      inst:IfcLengthMeasure_List_1374 .

inst:IfcLengthMeasure_List_1374
        list:hasContents  inst:IfcLengthMeasure_1331 .

inst:IfcCartesianPoint_330
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_1377
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_330
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_1377 .

inst:IfcLengthMeasure_List_1378
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1379
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_1380
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-157.217231378449"^^xsd:double .

inst:IfcLengthMeasure_List_1377
        list:hasContents  inst:IfcLengthMeasure_1380 ;
        list:hasNext      inst:IfcLengthMeasure_List_1378 .

inst:IfcLengthMeasure_1381
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "133.907674702395"^^xsd:double .

inst:IfcLengthMeasure_List_1378
        list:hasContents  inst:IfcLengthMeasure_1381 ;
        list:hasNext      inst:IfcLengthMeasure_List_1379 .

inst:IfcLengthMeasure_List_1379
        list:hasContents  inst:IfcLengthMeasure_1331 .

inst:IfcCartesianPoint_331
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_1382
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_331
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_1382 .

inst:IfcLengthMeasure_List_1383
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1384
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_1385
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-150.446948503868"^^xsd:double .

inst:IfcLengthMeasure_List_1382
        list:hasContents  inst:IfcLengthMeasure_1385 ;
        list:hasNext      inst:IfcLengthMeasure_List_1383 .

inst:IfcLengthMeasure_1386
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "112.380106954109"^^xsd:double .

inst:IfcLengthMeasure_List_1383
        list:hasContents  inst:IfcLengthMeasure_1386 ;
        list:hasNext      inst:IfcLengthMeasure_List_1384 .

inst:IfcLengthMeasure_List_1384
        list:hasContents  inst:IfcLengthMeasure_1331 .

inst:IfcCartesianPoint_332
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_1387
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_332
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_1387 .

inst:IfcLengthMeasure_List_1388
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1389
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_1390
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-140.781370993691"^^xsd:double .

inst:IfcLengthMeasure_List_1387
        list:hasContents  inst:IfcLengthMeasure_1390 ;
        list:hasNext      inst:IfcLengthMeasure_List_1388 .

inst:IfcLengthMeasure_1391
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "91.9771671791993"^^xsd:double .

inst:IfcLengthMeasure_List_1388
        list:hasContents  inst:IfcLengthMeasure_1391 ;
        list:hasNext      inst:IfcLengthMeasure_List_1389 .

inst:IfcLengthMeasure_List_1389
        list:hasContents  inst:IfcLengthMeasure_1331 .

inst:IfcCartesianPoint_333
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_1392
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_333
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_1392 .

inst:IfcLengthMeasure_List_1393
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1394
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_1395
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-128.915130782292"^^xsd:double .

inst:IfcLengthMeasure_List_1392
        list:hasContents  inst:IfcLengthMeasure_1395 ;
        list:hasNext      inst:IfcLengthMeasure_List_1393 .

inst:IfcLengthMeasure_1396
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "72.7659081552236"^^xsd:double .

inst:IfcLengthMeasure_List_1393
        list:hasContents  inst:IfcLengthMeasure_1396 ;
        list:hasNext      inst:IfcLengthMeasure_List_1394 .

inst:IfcLengthMeasure_List_1394
        list:hasContents  inst:IfcLengthMeasure_1331 .

inst:IfcCartesianPoint_334
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_1397
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_334
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_1397 .

inst:IfcLengthMeasure_List_1398
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1399
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_1400
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-115.221432502533"^^xsd:double .

inst:IfcLengthMeasure_List_1397
        list:hasContents  inst:IfcLengthMeasure_1400 ;
        list:hasNext      inst:IfcLengthMeasure_List_1398 .

inst:IfcLengthMeasure_1401
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "54.8097171953257"^^xsd:double .

inst:IfcLengthMeasure_List_1398
        list:hasContents  inst:IfcLengthMeasure_1401 ;
        list:hasNext      inst:IfcLengthMeasure_List_1399 .

inst:IfcLengthMeasure_List_1399
        list:hasContents  inst:IfcLengthMeasure_1331 .

inst:IfcCartesianPoint_335
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_1402
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_335
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_1402 .

inst:IfcLengthMeasure_List_1403
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1404
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_1405
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-99.8796470071603"^^xsd:double .

inst:IfcLengthMeasure_List_1402
        list:hasContents  inst:IfcLengthMeasure_1405 ;
        list:hasNext      inst:IfcLengthMeasure_List_1403 .

inst:IfcLengthMeasure_1406
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "38.239929890392"^^xsd:double .

inst:IfcLengthMeasure_List_1403
        list:hasContents  inst:IfcLengthMeasure_1406 ;
        list:hasNext      inst:IfcLengthMeasure_List_1404 .

inst:IfcLengthMeasure_List_1404
        list:hasContents  inst:IfcLengthMeasure_1331 .

inst:IfcCartesianPoint_336
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_1407
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_336
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_1407 .

inst:IfcLengthMeasure_List_1408
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1409
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_1410
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-82.9357654214438"^^xsd:double .

inst:IfcLengthMeasure_List_1407
        list:hasContents  inst:IfcLengthMeasure_1410 ;
        list:hasNext      inst:IfcLengthMeasure_List_1408 .

inst:IfcLengthMeasure_1411
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "23.3156500424069"^^xsd:double .

inst:IfcLengthMeasure_List_1408
        list:hasContents  inst:IfcLengthMeasure_1411 ;
        list:hasNext      inst:IfcLengthMeasure_List_1409 .

inst:IfcLengthMeasure_List_1409
        list:hasContents  inst:IfcLengthMeasure_1331 .

inst:IfcCartesianPoint_337
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_1412
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_337
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_1412 .

inst:IfcLengthMeasure_List_1413
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1414
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_1415
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-64.3369337417203"^^xsd:double .

inst:IfcLengthMeasure_List_1412
        list:hasContents  inst:IfcLengthMeasure_1415 ;
        list:hasNext      inst:IfcLengthMeasure_List_1413 .

inst:IfcLengthMeasure_1416
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "10.5226649501159"^^xsd:double .

inst:IfcLengthMeasure_List_1413
        list:hasContents  inst:IfcLengthMeasure_1416 ;
        list:hasNext      inst:IfcLengthMeasure_List_1414 .

inst:IfcLengthMeasure_List_1414
        list:hasContents  inst:IfcLengthMeasure_1331 .

inst:IfcCartesianPoint_338
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_1417
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_338
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_1417 .

inst:IfcLengthMeasure_List_1418
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1419
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_1420
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-44.0834019165823"^^xsd:double .

inst:IfcLengthMeasure_List_1417
        list:hasContents  inst:IfcLengthMeasure_1420 ;
        list:hasNext      inst:IfcLengthMeasure_List_1418 .

inst:IfcLengthMeasure_1421
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "0.569910981625322"^^xsd:double .

inst:IfcLengthMeasure_List_1418
        list:hasContents  inst:IfcLengthMeasure_1421 ;
        list:hasNext      inst:IfcLengthMeasure_List_1419 .

inst:IfcLengthMeasure_List_1419
        list:hasContents  inst:IfcLengthMeasure_1331 .

inst:IfcCartesianPoint_339
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_1422
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_339
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_1422 .

inst:IfcLengthMeasure_List_1423
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1424
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_1425
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-22.4460832273862"^^xsd:double .

inst:IfcLengthMeasure_List_1422
        list:hasContents  inst:IfcLengthMeasure_1425 ;
        list:hasNext      inst:IfcLengthMeasure_List_1423 .

inst:IfcLengthMeasure_1426
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-5.81475796938246"^^xsd:double .

inst:IfcLengthMeasure_List_1423
        list:hasContents  inst:IfcLengthMeasure_1426 ;
        list:hasNext      inst:IfcLengthMeasure_List_1424 .

inst:IfcLengthMeasure_List_1424
        list:hasContents  inst:IfcLengthMeasure_1331 .

inst:IfcCartesianPoint_340
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_1427
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_340
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_1427 .

inst:IfcLengthMeasure_List_1428
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1429
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_1430
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "0.00000176219393597"^^xsd:double .

inst:IfcLengthMeasure_List_1427
        list:hasContents  inst:IfcLengthMeasure_1430 ;
        list:hasNext      inst:IfcLengthMeasure_List_1428 .

inst:IfcLengthMeasure_1431
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-8.0243005407274"^^xsd:double .

inst:IfcLengthMeasure_List_1428
        list:hasContents  inst:IfcLengthMeasure_1431 ;
        list:hasNext      inst:IfcLengthMeasure_List_1429 .

inst:IfcLengthMeasure_List_1429
        list:hasContents  inst:IfcLengthMeasure_1331 .

inst:IfcCartesianPoint_341
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_1432
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_341
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_1432 .

inst:IfcLengthMeasure_List_1433
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1434
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_1435
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "22.446084481302"^^xsd:double .

inst:IfcLengthMeasure_List_1432
        list:hasContents  inst:IfcLengthMeasure_1435 ;
        list:hasNext      inst:IfcLengthMeasure_List_1433 .

inst:IfcLengthMeasure_1436
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-5.81475772180151"^^xsd:double .

inst:IfcLengthMeasure_List_1433
        list:hasContents  inst:IfcLengthMeasure_1436 ;
        list:hasNext      inst:IfcLengthMeasure_List_1434 .

inst:IfcLengthMeasure_List_1434
        list:hasContents  inst:IfcLengthMeasure_1331 .

inst:IfcCartesianPoint_342
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_1437
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_342
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_1437 .

inst:IfcLengthMeasure_List_1438
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1439
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_1440
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "44.0834024872804"^^xsd:double .

inst:IfcLengthMeasure_List_1437
        list:hasContents  inst:IfcLengthMeasure_1440 ;
        list:hasNext      inst:IfcLengthMeasure_List_1438 .

inst:IfcLengthMeasure_1441
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "0.569911206685156"^^xsd:double .

inst:IfcLengthMeasure_List_1438
        list:hasContents  inst:IfcLengthMeasure_1441 ;
        list:hasNext      inst:IfcLengthMeasure_List_1439 .

inst:IfcLengthMeasure_List_1439
        list:hasContents  inst:IfcLengthMeasure_1331 .

inst:IfcCartesianPoint_343
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_1442
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_343
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_1442 .

inst:IfcLengthMeasure_List_1443
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1444
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_1445
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "64.3369357795818"^^xsd:double .

inst:IfcLengthMeasure_List_1442
        list:hasContents  inst:IfcLengthMeasure_1445 ;
        list:hasNext      inst:IfcLengthMeasure_List_1443 .

inst:IfcLengthMeasure_1446
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "10.5226661545967"^^xsd:double .

inst:IfcLengthMeasure_List_1443
        list:hasContents  inst:IfcLengthMeasure_1446 ;
        list:hasNext      inst:IfcLengthMeasure_List_1444 .

inst:IfcLengthMeasure_List_1444
        list:hasContents  inst:IfcLengthMeasure_1331 .

inst:IfcCartesianPoint_344
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_1447
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_344
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_1447 .

inst:IfcLengthMeasure_List_1448
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1449
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_1450
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "82.9357630974296"^^xsd:double .

inst:IfcLengthMeasure_List_1447
        list:hasContents  inst:IfcLengthMeasure_1450 ;
        list:hasNext      inst:IfcLengthMeasure_List_1448 .

inst:IfcLengthMeasure_1451
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "23.3156482145766"^^xsd:double .

inst:IfcLengthMeasure_List_1448
        list:hasContents  inst:IfcLengthMeasure_1451 ;
        list:hasNext      inst:IfcLengthMeasure_List_1449 .

inst:IfcLengthMeasure_List_1449
        list:hasContents  inst:IfcLengthMeasure_1331 .

inst:IfcCartesianPoint_345
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_1452
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_345
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_1452 .

inst:IfcLengthMeasure_List_1453
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1454
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_1455
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "99.8796474348905"^^xsd:double .

inst:IfcLengthMeasure_List_1452
        list:hasContents  inst:IfcLengthMeasure_1455 ;
        list:hasNext      inst:IfcLengthMeasure_List_1453 .

inst:IfcLengthMeasure_1456
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "38.2399303092091"^^xsd:double .

inst:IfcLengthMeasure_List_1453
        list:hasContents  inst:IfcLengthMeasure_1456 ;
        list:hasNext      inst:IfcLengthMeasure_List_1454 .

inst:IfcLengthMeasure_List_1454
        list:hasContents  inst:IfcLengthMeasure_1331 .

inst:IfcCartesianPoint_346
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_1457
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_346
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_1457 .

inst:IfcLengthMeasure_List_1458
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1459
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_1460
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "115.221437914664"^^xsd:double .

inst:IfcLengthMeasure_List_1457
        list:hasContents  inst:IfcLengthMeasure_1460 ;
        list:hasNext      inst:IfcLengthMeasure_List_1458 .

inst:IfcLengthMeasure_1461
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "54.8097236343718"^^xsd:double .

inst:IfcLengthMeasure_List_1458
        list:hasContents  inst:IfcLengthMeasure_1461 ;
        list:hasNext      inst:IfcLengthMeasure_List_1459 .

inst:IfcLengthMeasure_List_1459
        list:hasContents  inst:IfcLengthMeasure_1331 .

inst:IfcCartesianPoint_347
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_1462
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_347
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_1462 .

inst:IfcLengthMeasure_List_1463
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1464
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_1465
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "128.915131219959"^^xsd:double .

inst:IfcLengthMeasure_List_1462
        list:hasContents  inst:IfcLengthMeasure_1465 ;
        list:hasNext      inst:IfcLengthMeasure_List_1463 .

inst:IfcLengthMeasure_1466
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "72.7659087899957"^^xsd:double .

inst:IfcLengthMeasure_List_1463
        list:hasContents  inst:IfcLengthMeasure_1466 ;
        list:hasNext      inst:IfcLengthMeasure_List_1464 .

inst:IfcLengthMeasure_List_1464
        list:hasContents  inst:IfcLengthMeasure_1331 .

inst:IfcCartesianPoint_348
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_1467
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_348
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_1467 .

inst:IfcLengthMeasure_List_1468
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1469
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_1470
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "140.781372023645"^^xsd:double .

inst:IfcLengthMeasure_List_1467
        list:hasContents  inst:IfcLengthMeasure_1470 ;
        list:hasNext      inst:IfcLengthMeasure_List_1468 .

inst:IfcLengthMeasure_1471
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "91.9771690603084"^^xsd:double .

inst:IfcLengthMeasure_List_1468
        list:hasContents  inst:IfcLengthMeasure_1471 ;
        list:hasNext      inst:IfcLengthMeasure_List_1469 .

inst:IfcLengthMeasure_List_1469
        list:hasContents  inst:IfcLengthMeasure_1331 .

inst:IfcCartesianPoint_349
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_1472
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_349
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_1472 .

inst:IfcLengthMeasure_List_1473
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1474
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_1475
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "150.44694977305"^^xsd:double .

inst:IfcLengthMeasure_List_1472
        list:hasContents  inst:IfcLengthMeasure_1475 ;
        list:hasNext      inst:IfcLengthMeasure_List_1473 .

inst:IfcLengthMeasure_1476
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "112.380110135675"^^xsd:double .

inst:IfcLengthMeasure_List_1473
        list:hasContents  inst:IfcLengthMeasure_1476 ;
        list:hasNext      inst:IfcLengthMeasure_List_1474 .

inst:IfcLengthMeasure_List_1474
        list:hasContents  inst:IfcLengthMeasure_1331 .

inst:IfcCartesianPoint_350
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_1477
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_350
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_1477 .

inst:IfcLengthMeasure_List_1478
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1479
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_1480
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "157.217230633118"^^xsd:double .

inst:IfcLengthMeasure_List_1477
        list:hasContents  inst:IfcLengthMeasure_1480 ;
        list:hasNext      inst:IfcLengthMeasure_List_1478 .

inst:IfcLengthMeasure_1481
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "133.907671395464"^^xsd:double .

inst:IfcLengthMeasure_List_1478
        list:hasContents  inst:IfcLengthMeasure_1481 ;
        list:hasNext      inst:IfcLengthMeasure_List_1479 .

inst:IfcLengthMeasure_List_1479
        list:hasContents  inst:IfcLengthMeasure_1331 .

inst:IfcCartesianPoint_351
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_1482
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_351
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_1482 .

inst:IfcLengthMeasure_List_1483
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1484
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_1485
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "159.838406320187"^^xsd:double .

inst:IfcLengthMeasure_List_1482
        list:hasContents  inst:IfcLengthMeasure_1485 ;
        list:hasNext      inst:IfcLengthMeasure_List_1483 .

inst:IfcLengthMeasure_1486
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "156.297847499286"^^xsd:double .

inst:IfcLengthMeasure_List_1483
        list:hasContents  inst:IfcLengthMeasure_1486 ;
        list:hasNext      inst:IfcLengthMeasure_List_1484 .

inst:IfcLengthMeasure_List_1484
        list:hasContents  inst:IfcLengthMeasure_1331 .

inst:IfcCartesianPoint_352
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_1487
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_352
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_1487 .

inst:IfcLengthMeasure_List_1488
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1489
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_1490
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "156.295574340587"^^xsd:double .

inst:IfcLengthMeasure_List_1487
        list:hasContents  inst:IfcLengthMeasure_1490 ;
        list:hasNext      inst:IfcLengthMeasure_List_1488 .

inst:IfcLengthMeasure_1491
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "178.505166588871"^^xsd:double .

inst:IfcLengthMeasure_List_1488
        list:hasContents  inst:IfcLengthMeasure_1491 ;
        list:hasNext      inst:IfcLengthMeasure_List_1489 .

inst:IfcLengthMeasure_List_1489
        list:hasContents  inst:IfcLengthMeasure_1331 .

inst:IfcCartesianPoint_353
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_1492
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_353
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_1492 .

inst:IfcLengthMeasure_List_1493
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1494
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_1495
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "145.336254078924"^^xsd:double .

inst:IfcLengthMeasure_List_1492
        list:hasContents  inst:IfcLengthMeasure_1495 ;
        list:hasNext      inst:IfcLengthMeasure_List_1493 .

inst:IfcLengthMeasure_1496
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "198.131563599338"^^xsd:double .

inst:IfcLengthMeasure_List_1493
        list:hasContents  inst:IfcLengthMeasure_1496 ;
        list:hasNext      inst:IfcLengthMeasure_List_1494 .

inst:IfcLengthMeasure_List_1494
        list:hasContents  inst:IfcLengthMeasure_1331 .

inst:IfcCartesianPoint_354
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_1497
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_354
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_1497 .

inst:IfcLengthMeasure_List_1498
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1499
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_1500
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "129.019208284081"^^xsd:double .

inst:IfcLengthMeasure_List_1497
        list:hasContents  inst:IfcLengthMeasure_1500 ;
        list:hasNext      inst:IfcLengthMeasure_List_1498 .

inst:IfcLengthMeasure_1501
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "213.666097201998"^^xsd:double .

inst:IfcLengthMeasure_List_1498
        list:hasContents  inst:IfcLengthMeasure_1501 ;
        list:hasNext      inst:IfcLengthMeasure_List_1499 .

inst:IfcLengthMeasure_List_1499
        list:hasContents  inst:IfcLengthMeasure_1331 .

inst:IfcCartesianPoint_355
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_1502
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_355
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_1502 .

inst:IfcLengthMeasure_List_1503
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1504
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_1505
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "109.746785929842"^^xsd:double .

inst:IfcLengthMeasure_List_1502
        list:hasContents  inst:IfcLengthMeasure_1505 ;
        list:hasNext      inst:IfcLengthMeasure_List_1503 .

inst:IfcLengthMeasure_1506
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "225.394568694178"^^xsd:double .

inst:IfcLengthMeasure_List_1503
        list:hasContents  inst:IfcLengthMeasure_1506 ;
        list:hasNext      inst:IfcLengthMeasure_List_1504 .

inst:IfcLengthMeasure_List_1504
        list:hasContents  inst:IfcLengthMeasure_1331 .

inst:IfcCartesianPoint_356
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_1507
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_356
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_1507 .

inst:IfcLengthMeasure_List_1508
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1509
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_1510
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "88.90833880495"^^xsd:double .

inst:IfcLengthMeasure_List_1507
        list:hasContents  inst:IfcLengthMeasure_1510 ;
        list:hasNext      inst:IfcLengthMeasure_List_1508 .

inst:IfcLengthMeasure_1511
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "234.07626833846"^^xsd:double .

inst:IfcLengthMeasure_List_1508
        list:hasContents  inst:IfcLengthMeasure_1511 ;
        list:hasNext      inst:IfcLengthMeasure_List_1509 .

inst:IfcLengthMeasure_List_1509
        list:hasContents  inst:IfcLengthMeasure_1331 .

inst:IfcCartesianPoint_357
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_1512
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_357
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_1512 .

inst:IfcLengthMeasure_List_1513
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1514
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_1515
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "67.2093069026739"^^xsd:double .

inst:IfcLengthMeasure_List_1512
        list:hasContents  inst:IfcLengthMeasure_1515 ;
        list:hasNext      inst:IfcLengthMeasure_List_1513 .

inst:IfcLengthMeasure_1516
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "240.324720559443"^^xsd:double .

inst:IfcLengthMeasure_List_1513
        list:hasContents  inst:IfcLengthMeasure_1516 ;
        list:hasNext      inst:IfcLengthMeasure_List_1514 .

inst:IfcLengthMeasure_List_1514
        list:hasContents  inst:IfcLengthMeasure_1331 .

inst:IfcCartesianPoint_358
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_1517
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_358
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_1517 .

inst:IfcLengthMeasure_List_1518
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1519
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_1520
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "45.0238122743352"^^xsd:double .

inst:IfcLengthMeasure_List_1517
        list:hasContents  inst:IfcLengthMeasure_1520 ;
        list:hasNext      inst:IfcLengthMeasure_List_1518 .

inst:IfcLengthMeasure_1521
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "244.546841305597"^^xsd:double .

inst:IfcLengthMeasure_List_1518
        list:hasContents  inst:IfcLengthMeasure_1521 ;
        list:hasNext      inst:IfcLengthMeasure_List_1519 .

inst:IfcLengthMeasure_List_1519
        list:hasContents  inst:IfcLengthMeasure_1331 .

inst:IfcCartesianPoint_359
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_1522
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_359
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_1522 .

inst:IfcLengthMeasure_List_1523
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1524
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_1525
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "22.5714426167356"^^xsd:double .

inst:IfcLengthMeasure_List_1522
        list:hasContents  inst:IfcLengthMeasure_1525 ;
        list:hasNext      inst:IfcLengthMeasure_List_1523 .

inst:IfcLengthMeasure_1526
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "246.991542511648"^^xsd:double .

inst:IfcLengthMeasure_List_1523
        list:hasContents  inst:IfcLengthMeasure_1526 ;
        list:hasNext      inst:IfcLengthMeasure_List_1524 .

inst:IfcLengthMeasure_List_1524
        list:hasContents  inst:IfcLengthMeasure_1331 .

inst:IfcPolyLoop_360  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_1527
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_360  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_1527 .

inst:IfcCartesianPoint_List_1528
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1529
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1530
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1531
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1532
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1533
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1534
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1535
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1536
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1537
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1538
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1539
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1540
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1541
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1542
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1543
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1544
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1545
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1546
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1547
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1548
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1549
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1550
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1551
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1552
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1553
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1554
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1555
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1556
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1557
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1558
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1559
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1560
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1561
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1562
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1563
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1564
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1565
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1527
        list:hasContents  inst:IfcCartesianPoint_202 ;
        list:hasNext      inst:IfcCartesianPoint_List_1528 .

inst:IfcCartesianPoint_List_1528
        list:hasContents  inst:IfcCartesianPoint_203 ;
        list:hasNext      inst:IfcCartesianPoint_List_1529 .

inst:IfcCartesianPoint_List_1529
        list:hasContents  inst:IfcCartesianPoint_204 ;
        list:hasNext      inst:IfcCartesianPoint_List_1530 .

inst:IfcCartesianPoint_List_1530
        list:hasContents  inst:IfcCartesianPoint_205 ;
        list:hasNext      inst:IfcCartesianPoint_List_1531 .

inst:IfcCartesianPoint_List_1531
        list:hasContents  inst:IfcCartesianPoint_206 ;
        list:hasNext      inst:IfcCartesianPoint_List_1532 .

inst:IfcCartesianPoint_List_1532
        list:hasContents  inst:IfcCartesianPoint_207 ;
        list:hasNext      inst:IfcCartesianPoint_List_1533 .

inst:IfcCartesianPoint_List_1533
        list:hasContents  inst:IfcCartesianPoint_208 ;
        list:hasNext      inst:IfcCartesianPoint_List_1534 .

inst:IfcCartesianPoint_List_1534
        list:hasContents  inst:IfcCartesianPoint_209 ;
        list:hasNext      inst:IfcCartesianPoint_List_1535 .

inst:IfcCartesianPoint_List_1535
        list:hasContents  inst:IfcCartesianPoint_210 ;
        list:hasNext      inst:IfcCartesianPoint_List_1536 .

inst:IfcCartesianPoint_List_1536
        list:hasContents  inst:IfcCartesianPoint_211 ;
        list:hasNext      inst:IfcCartesianPoint_List_1537 .

inst:IfcCartesianPoint_List_1537
        list:hasContents  inst:IfcCartesianPoint_212 ;
        list:hasNext      inst:IfcCartesianPoint_List_1538 .

inst:IfcCartesianPoint_List_1538
        list:hasContents  inst:IfcCartesianPoint_213 ;
        list:hasNext      inst:IfcCartesianPoint_List_1539 .

inst:IfcCartesianPoint_List_1539
        list:hasContents  inst:IfcCartesianPoint_214 ;
        list:hasNext      inst:IfcCartesianPoint_List_1540 .

inst:IfcCartesianPoint_List_1540
        list:hasContents  inst:IfcCartesianPoint_215 ;
        list:hasNext      inst:IfcCartesianPoint_List_1541 .

inst:IfcCartesianPoint_List_1541
        list:hasContents  inst:IfcCartesianPoint_216 ;
        list:hasNext      inst:IfcCartesianPoint_List_1542 .

inst:IfcCartesianPoint_List_1542
        list:hasContents  inst:IfcCartesianPoint_217 ;
        list:hasNext      inst:IfcCartesianPoint_List_1543 .

inst:IfcCartesianPoint_List_1543
        list:hasContents  inst:IfcCartesianPoint_218 ;
        list:hasNext      inst:IfcCartesianPoint_List_1544 .

inst:IfcCartesianPoint_List_1544
        list:hasContents  inst:IfcCartesianPoint_219 ;
        list:hasNext      inst:IfcCartesianPoint_List_1545 .

inst:IfcCartesianPoint_List_1545
        list:hasContents  inst:IfcCartesianPoint_220 ;
        list:hasNext      inst:IfcCartesianPoint_List_1546 .

inst:IfcCartesianPoint_List_1546
        list:hasContents  inst:IfcCartesianPoint_221 ;
        list:hasNext      inst:IfcCartesianPoint_List_1547 .

inst:IfcCartesianPoint_List_1547
        list:hasContents  inst:IfcCartesianPoint_222 ;
        list:hasNext      inst:IfcCartesianPoint_List_1548 .

inst:IfcCartesianPoint_List_1548
        list:hasContents  inst:IfcCartesianPoint_223 ;
        list:hasNext      inst:IfcCartesianPoint_List_1549 .

inst:IfcCartesianPoint_List_1549
        list:hasContents  inst:IfcCartesianPoint_224 ;
        list:hasNext      inst:IfcCartesianPoint_List_1550 .

inst:IfcCartesianPoint_List_1550
        list:hasContents  inst:IfcCartesianPoint_225 ;
        list:hasNext      inst:IfcCartesianPoint_List_1551 .

inst:IfcCartesianPoint_List_1551
        list:hasContents  inst:IfcCartesianPoint_226 ;
        list:hasNext      inst:IfcCartesianPoint_List_1552 .

inst:IfcCartesianPoint_List_1552
        list:hasContents  inst:IfcCartesianPoint_227 ;
        list:hasNext      inst:IfcCartesianPoint_List_1553 .

inst:IfcCartesianPoint_List_1553
        list:hasContents  inst:IfcCartesianPoint_228 ;
        list:hasNext      inst:IfcCartesianPoint_List_1554 .

inst:IfcCartesianPoint_List_1554
        list:hasContents  inst:IfcCartesianPoint_229 ;
        list:hasNext      inst:IfcCartesianPoint_List_1555 .

inst:IfcCartesianPoint_List_1555
        list:hasContents  inst:IfcCartesianPoint_230 ;
        list:hasNext      inst:IfcCartesianPoint_List_1556 .

inst:IfcCartesianPoint_List_1556
        list:hasContents  inst:IfcCartesianPoint_231 ;
        list:hasNext      inst:IfcCartesianPoint_List_1557 .

inst:IfcCartesianPoint_List_1557
        list:hasContents  inst:IfcCartesianPoint_232 ;
        list:hasNext      inst:IfcCartesianPoint_List_1558 .

inst:IfcCartesianPoint_List_1558
        list:hasContents  inst:IfcCartesianPoint_233 ;
        list:hasNext      inst:IfcCartesianPoint_List_1559 .

inst:IfcCartesianPoint_List_1559
        list:hasContents  inst:IfcCartesianPoint_234 ;
        list:hasNext      inst:IfcCartesianPoint_List_1560 .

inst:IfcCartesianPoint_List_1560
        list:hasContents  inst:IfcCartesianPoint_235 ;
        list:hasNext      inst:IfcCartesianPoint_List_1561 .

inst:IfcCartesianPoint_List_1561
        list:hasContents  inst:IfcCartesianPoint_236 ;
        list:hasNext      inst:IfcCartesianPoint_List_1562 .

inst:IfcCartesianPoint_List_1562
        list:hasContents  inst:IfcCartesianPoint_237 ;
        list:hasNext      inst:IfcCartesianPoint_List_1563 .

inst:IfcCartesianPoint_List_1563
        list:hasContents  inst:IfcCartesianPoint_238 ;
        list:hasNext      inst:IfcCartesianPoint_List_1564 .

inst:IfcCartesianPoint_List_1564
        list:hasContents  inst:IfcCartesianPoint_239 ;
        list:hasNext      inst:IfcCartesianPoint_List_1565 .

inst:IfcCartesianPoint_List_1565
        list:hasContents  inst:IfcCartesianPoint_240 .

inst:IfcFaceOuterBound_361
        rdf:type                ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound  inst:IfcPolyLoop_360 .

inst:IfcBoolean_1566  rdf:type  ifc:IfcBoolean ;
        express:hasBoolean  true .

inst:IfcFaceOuterBound_361
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcPolyLoop_362  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_1567
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_362  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_1567 .

inst:IfcCartesianPoint_List_1568
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1569
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1570
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1571
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1572
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1573
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1574
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1575
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1576
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1577
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1578
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1579
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1580
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1581
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1582
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1583
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1584
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1585
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1586
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1587
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1588
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1589
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1590
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1591
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1592
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1593
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1594
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1595
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1596
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1597
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1598
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1599
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1600
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1601
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1602
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1603
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1604
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1605
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1606
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1567
        list:hasContents  inst:IfcCartesianPoint_241 ;
        list:hasNext      inst:IfcCartesianPoint_List_1568 .

inst:IfcCartesianPoint_List_1568
        list:hasContents  inst:IfcCartesianPoint_242 ;
        list:hasNext      inst:IfcCartesianPoint_List_1569 .

inst:IfcCartesianPoint_List_1569
        list:hasContents  inst:IfcCartesianPoint_243 ;
        list:hasNext      inst:IfcCartesianPoint_List_1570 .

inst:IfcCartesianPoint_List_1570
        list:hasContents  inst:IfcCartesianPoint_244 ;
        list:hasNext      inst:IfcCartesianPoint_List_1571 .

inst:IfcCartesianPoint_List_1571
        list:hasContents  inst:IfcCartesianPoint_245 ;
        list:hasNext      inst:IfcCartesianPoint_List_1572 .

inst:IfcCartesianPoint_List_1572
        list:hasContents  inst:IfcCartesianPoint_246 ;
        list:hasNext      inst:IfcCartesianPoint_List_1573 .

inst:IfcCartesianPoint_List_1573
        list:hasContents  inst:IfcCartesianPoint_247 ;
        list:hasNext      inst:IfcCartesianPoint_List_1574 .

inst:IfcCartesianPoint_List_1574
        list:hasContents  inst:IfcCartesianPoint_248 ;
        list:hasNext      inst:IfcCartesianPoint_List_1575 .

inst:IfcCartesianPoint_List_1575
        list:hasContents  inst:IfcCartesianPoint_249 ;
        list:hasNext      inst:IfcCartesianPoint_List_1576 .

inst:IfcCartesianPoint_List_1576
        list:hasContents  inst:IfcCartesianPoint_250 ;
        list:hasNext      inst:IfcCartesianPoint_List_1577 .

inst:IfcCartesianPoint_List_1577
        list:hasContents  inst:IfcCartesianPoint_251 ;
        list:hasNext      inst:IfcCartesianPoint_List_1578 .

inst:IfcCartesianPoint_List_1578
        list:hasContents  inst:IfcCartesianPoint_252 ;
        list:hasNext      inst:IfcCartesianPoint_List_1579 .

inst:IfcCartesianPoint_List_1579
        list:hasContents  inst:IfcCartesianPoint_253 ;
        list:hasNext      inst:IfcCartesianPoint_List_1580 .

inst:IfcCartesianPoint_List_1580
        list:hasContents  inst:IfcCartesianPoint_254 ;
        list:hasNext      inst:IfcCartesianPoint_List_1581 .

inst:IfcCartesianPoint_List_1581
        list:hasContents  inst:IfcCartesianPoint_255 ;
        list:hasNext      inst:IfcCartesianPoint_List_1582 .

inst:IfcCartesianPoint_List_1582
        list:hasContents  inst:IfcCartesianPoint_256 ;
        list:hasNext      inst:IfcCartesianPoint_List_1583 .

inst:IfcCartesianPoint_List_1583
        list:hasContents  inst:IfcCartesianPoint_257 ;
        list:hasNext      inst:IfcCartesianPoint_List_1584 .

inst:IfcCartesianPoint_List_1584
        list:hasContents  inst:IfcCartesianPoint_258 ;
        list:hasNext      inst:IfcCartesianPoint_List_1585 .

inst:IfcCartesianPoint_List_1585
        list:hasContents  inst:IfcCartesianPoint_259 ;
        list:hasNext      inst:IfcCartesianPoint_List_1586 .

inst:IfcCartesianPoint_List_1586
        list:hasContents  inst:IfcCartesianPoint_260 ;
        list:hasNext      inst:IfcCartesianPoint_List_1587 .

inst:IfcCartesianPoint_List_1587
        list:hasContents  inst:IfcCartesianPoint_261 ;
        list:hasNext      inst:IfcCartesianPoint_List_1588 .

inst:IfcCartesianPoint_List_1588
        list:hasContents  inst:IfcCartesianPoint_262 ;
        list:hasNext      inst:IfcCartesianPoint_List_1589 .

inst:IfcCartesianPoint_List_1589
        list:hasContents  inst:IfcCartesianPoint_263 ;
        list:hasNext      inst:IfcCartesianPoint_List_1590 .

inst:IfcCartesianPoint_List_1590
        list:hasContents  inst:IfcCartesianPoint_264 ;
        list:hasNext      inst:IfcCartesianPoint_List_1591 .

inst:IfcCartesianPoint_List_1591
        list:hasContents  inst:IfcCartesianPoint_265 ;
        list:hasNext      inst:IfcCartesianPoint_List_1592 .

inst:IfcCartesianPoint_List_1592
        list:hasContents  inst:IfcCartesianPoint_266 ;
        list:hasNext      inst:IfcCartesianPoint_List_1593 .

inst:IfcCartesianPoint_List_1593
        list:hasContents  inst:IfcCartesianPoint_267 ;
        list:hasNext      inst:IfcCartesianPoint_List_1594 .

inst:IfcCartesianPoint_List_1594
        list:hasContents  inst:IfcCartesianPoint_268 ;
        list:hasNext      inst:IfcCartesianPoint_List_1595 .

inst:IfcCartesianPoint_List_1595
        list:hasContents  inst:IfcCartesianPoint_269 ;
        list:hasNext      inst:IfcCartesianPoint_List_1596 .

inst:IfcCartesianPoint_List_1596
        list:hasContents  inst:IfcCartesianPoint_270 ;
        list:hasNext      inst:IfcCartesianPoint_List_1597 .

inst:IfcCartesianPoint_List_1597
        list:hasContents  inst:IfcCartesianPoint_271 ;
        list:hasNext      inst:IfcCartesianPoint_List_1598 .

inst:IfcCartesianPoint_List_1598
        list:hasContents  inst:IfcCartesianPoint_272 ;
        list:hasNext      inst:IfcCartesianPoint_List_1599 .

inst:IfcCartesianPoint_List_1599
        list:hasContents  inst:IfcCartesianPoint_273 ;
        list:hasNext      inst:IfcCartesianPoint_List_1600 .

inst:IfcCartesianPoint_List_1600
        list:hasContents  inst:IfcCartesianPoint_274 ;
        list:hasNext      inst:IfcCartesianPoint_List_1601 .

inst:IfcCartesianPoint_List_1601
        list:hasContents  inst:IfcCartesianPoint_275 ;
        list:hasNext      inst:IfcCartesianPoint_List_1602 .

inst:IfcCartesianPoint_List_1602
        list:hasContents  inst:IfcCartesianPoint_276 ;
        list:hasNext      inst:IfcCartesianPoint_List_1603 .

inst:IfcCartesianPoint_List_1603
        list:hasContents  inst:IfcCartesianPoint_277 ;
        list:hasNext      inst:IfcCartesianPoint_List_1604 .

inst:IfcCartesianPoint_List_1604
        list:hasContents  inst:IfcCartesianPoint_278 ;
        list:hasNext      inst:IfcCartesianPoint_List_1605 .

inst:IfcCartesianPoint_List_1605
        list:hasContents  inst:IfcCartesianPoint_279 ;
        list:hasNext      inst:IfcCartesianPoint_List_1606 .

inst:IfcCartesianPoint_List_1606
        list:hasContents  inst:IfcCartesianPoint_201 .

inst:IfcFaceBound_363
        rdf:type                      ifc:IfcFaceBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_362 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_364  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_361 ;
        ifc:bounds_IfcFace  inst:IfcFaceBound_363 .

inst:IfcPolyLoop_365  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_1607
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_365  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_1607 .

inst:IfcCartesianPoint_List_1608
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1609
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1610
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1611
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1612
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1613
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1614
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1615
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1616
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1617
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1618
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1619
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1620
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1621
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1622
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1623
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1624
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1625
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1626
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1627
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1628
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1629
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1630
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1631
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1632
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1633
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1634
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1635
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1636
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1637
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1638
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1639
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1640
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1641
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1642
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1643
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1644
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1645
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1646
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1607
        list:hasContents  inst:IfcCartesianPoint_281 ;
        list:hasNext      inst:IfcCartesianPoint_List_1608 .

inst:IfcCartesianPoint_List_1608
        list:hasContents  inst:IfcCartesianPoint_282 ;
        list:hasNext      inst:IfcCartesianPoint_List_1609 .

inst:IfcCartesianPoint_List_1609
        list:hasContents  inst:IfcCartesianPoint_283 ;
        list:hasNext      inst:IfcCartesianPoint_List_1610 .

inst:IfcCartesianPoint_List_1610
        list:hasContents  inst:IfcCartesianPoint_284 ;
        list:hasNext      inst:IfcCartesianPoint_List_1611 .

inst:IfcCartesianPoint_List_1611
        list:hasContents  inst:IfcCartesianPoint_285 ;
        list:hasNext      inst:IfcCartesianPoint_List_1612 .

inst:IfcCartesianPoint_List_1612
        list:hasContents  inst:IfcCartesianPoint_286 ;
        list:hasNext      inst:IfcCartesianPoint_List_1613 .

inst:IfcCartesianPoint_List_1613
        list:hasContents  inst:IfcCartesianPoint_287 ;
        list:hasNext      inst:IfcCartesianPoint_List_1614 .

inst:IfcCartesianPoint_List_1614
        list:hasContents  inst:IfcCartesianPoint_288 ;
        list:hasNext      inst:IfcCartesianPoint_List_1615 .

inst:IfcCartesianPoint_List_1615
        list:hasContents  inst:IfcCartesianPoint_289 ;
        list:hasNext      inst:IfcCartesianPoint_List_1616 .

inst:IfcCartesianPoint_List_1616
        list:hasContents  inst:IfcCartesianPoint_290 ;
        list:hasNext      inst:IfcCartesianPoint_List_1617 .

inst:IfcCartesianPoint_List_1617
        list:hasContents  inst:IfcCartesianPoint_291 ;
        list:hasNext      inst:IfcCartesianPoint_List_1618 .

inst:IfcCartesianPoint_List_1618
        list:hasContents  inst:IfcCartesianPoint_292 ;
        list:hasNext      inst:IfcCartesianPoint_List_1619 .

inst:IfcCartesianPoint_List_1619
        list:hasContents  inst:IfcCartesianPoint_293 ;
        list:hasNext      inst:IfcCartesianPoint_List_1620 .

inst:IfcCartesianPoint_List_1620
        list:hasContents  inst:IfcCartesianPoint_294 ;
        list:hasNext      inst:IfcCartesianPoint_List_1621 .

inst:IfcCartesianPoint_List_1621
        list:hasContents  inst:IfcCartesianPoint_295 ;
        list:hasNext      inst:IfcCartesianPoint_List_1622 .

inst:IfcCartesianPoint_List_1622
        list:hasContents  inst:IfcCartesianPoint_296 ;
        list:hasNext      inst:IfcCartesianPoint_List_1623 .

inst:IfcCartesianPoint_List_1623
        list:hasContents  inst:IfcCartesianPoint_297 ;
        list:hasNext      inst:IfcCartesianPoint_List_1624 .

inst:IfcCartesianPoint_List_1624
        list:hasContents  inst:IfcCartesianPoint_298 ;
        list:hasNext      inst:IfcCartesianPoint_List_1625 .

inst:IfcCartesianPoint_List_1625
        list:hasContents  inst:IfcCartesianPoint_299 ;
        list:hasNext      inst:IfcCartesianPoint_List_1626 .

inst:IfcCartesianPoint_List_1626
        list:hasContents  inst:IfcCartesianPoint_300 ;
        list:hasNext      inst:IfcCartesianPoint_List_1627 .

inst:IfcCartesianPoint_List_1627
        list:hasContents  inst:IfcCartesianPoint_301 ;
        list:hasNext      inst:IfcCartesianPoint_List_1628 .

inst:IfcCartesianPoint_List_1628
        list:hasContents  inst:IfcCartesianPoint_302 ;
        list:hasNext      inst:IfcCartesianPoint_List_1629 .

inst:IfcCartesianPoint_List_1629
        list:hasContents  inst:IfcCartesianPoint_303 ;
        list:hasNext      inst:IfcCartesianPoint_List_1630 .

inst:IfcCartesianPoint_List_1630
        list:hasContents  inst:IfcCartesianPoint_304 ;
        list:hasNext      inst:IfcCartesianPoint_List_1631 .

inst:IfcCartesianPoint_List_1631
        list:hasContents  inst:IfcCartesianPoint_305 ;
        list:hasNext      inst:IfcCartesianPoint_List_1632 .

inst:IfcCartesianPoint_List_1632
        list:hasContents  inst:IfcCartesianPoint_306 ;
        list:hasNext      inst:IfcCartesianPoint_List_1633 .

inst:IfcCartesianPoint_List_1633
        list:hasContents  inst:IfcCartesianPoint_307 ;
        list:hasNext      inst:IfcCartesianPoint_List_1634 .

inst:IfcCartesianPoint_List_1634
        list:hasContents  inst:IfcCartesianPoint_308 ;
        list:hasNext      inst:IfcCartesianPoint_List_1635 .

inst:IfcCartesianPoint_List_1635
        list:hasContents  inst:IfcCartesianPoint_309 ;
        list:hasNext      inst:IfcCartesianPoint_List_1636 .

inst:IfcCartesianPoint_List_1636
        list:hasContents  inst:IfcCartesianPoint_310 ;
        list:hasNext      inst:IfcCartesianPoint_List_1637 .

inst:IfcCartesianPoint_List_1637
        list:hasContents  inst:IfcCartesianPoint_311 ;
        list:hasNext      inst:IfcCartesianPoint_List_1638 .

inst:IfcCartesianPoint_List_1638
        list:hasContents  inst:IfcCartesianPoint_312 ;
        list:hasNext      inst:IfcCartesianPoint_List_1639 .

inst:IfcCartesianPoint_List_1639
        list:hasContents  inst:IfcCartesianPoint_313 ;
        list:hasNext      inst:IfcCartesianPoint_List_1640 .

inst:IfcCartesianPoint_List_1640
        list:hasContents  inst:IfcCartesianPoint_314 ;
        list:hasNext      inst:IfcCartesianPoint_List_1641 .

inst:IfcCartesianPoint_List_1641
        list:hasContents  inst:IfcCartesianPoint_315 ;
        list:hasNext      inst:IfcCartesianPoint_List_1642 .

inst:IfcCartesianPoint_List_1642
        list:hasContents  inst:IfcCartesianPoint_316 ;
        list:hasNext      inst:IfcCartesianPoint_List_1643 .

inst:IfcCartesianPoint_List_1643
        list:hasContents  inst:IfcCartesianPoint_317 ;
        list:hasNext      inst:IfcCartesianPoint_List_1644 .

inst:IfcCartesianPoint_List_1644
        list:hasContents  inst:IfcCartesianPoint_318 ;
        list:hasNext      inst:IfcCartesianPoint_List_1645 .

inst:IfcCartesianPoint_List_1645
        list:hasContents  inst:IfcCartesianPoint_319 ;
        list:hasNext      inst:IfcCartesianPoint_List_1646 .

inst:IfcCartesianPoint_List_1646
        list:hasContents  inst:IfcCartesianPoint_280 .

inst:IfcFaceOuterBound_366
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_365 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_367  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_366 .

inst:IfcPolyLoop_368  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_1647
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_368  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_1647 .

inst:IfcCartesianPoint_List_1648
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1649
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1650
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1651
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1652
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1653
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1654
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1655
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1656
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1657
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1658
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1659
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1660
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1661
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1662
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1663
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1664
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1665
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1666
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1667
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1668
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1669
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1670
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1671
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1672
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1673
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1674
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1675
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1676
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1677
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1678
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1679
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1680
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1681
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1682
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1683
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1684
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1685
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1686
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1647
        list:hasContents  inst:IfcCartesianPoint_321 ;
        list:hasNext      inst:IfcCartesianPoint_List_1648 .

inst:IfcCartesianPoint_List_1648
        list:hasContents  inst:IfcCartesianPoint_322 ;
        list:hasNext      inst:IfcCartesianPoint_List_1649 .

inst:IfcCartesianPoint_List_1649
        list:hasContents  inst:IfcCartesianPoint_323 ;
        list:hasNext      inst:IfcCartesianPoint_List_1650 .

inst:IfcCartesianPoint_List_1650
        list:hasContents  inst:IfcCartesianPoint_324 ;
        list:hasNext      inst:IfcCartesianPoint_List_1651 .

inst:IfcCartesianPoint_List_1651
        list:hasContents  inst:IfcCartesianPoint_325 ;
        list:hasNext      inst:IfcCartesianPoint_List_1652 .

inst:IfcCartesianPoint_List_1652
        list:hasContents  inst:IfcCartesianPoint_326 ;
        list:hasNext      inst:IfcCartesianPoint_List_1653 .

inst:IfcCartesianPoint_List_1653
        list:hasContents  inst:IfcCartesianPoint_327 ;
        list:hasNext      inst:IfcCartesianPoint_List_1654 .

inst:IfcCartesianPoint_List_1654
        list:hasContents  inst:IfcCartesianPoint_328 ;
        list:hasNext      inst:IfcCartesianPoint_List_1655 .

inst:IfcCartesianPoint_List_1655
        list:hasContents  inst:IfcCartesianPoint_329 ;
        list:hasNext      inst:IfcCartesianPoint_List_1656 .

inst:IfcCartesianPoint_List_1656
        list:hasContents  inst:IfcCartesianPoint_330 ;
        list:hasNext      inst:IfcCartesianPoint_List_1657 .

inst:IfcCartesianPoint_List_1657
        list:hasContents  inst:IfcCartesianPoint_331 ;
        list:hasNext      inst:IfcCartesianPoint_List_1658 .

inst:IfcCartesianPoint_List_1658
        list:hasContents  inst:IfcCartesianPoint_332 ;
        list:hasNext      inst:IfcCartesianPoint_List_1659 .

inst:IfcCartesianPoint_List_1659
        list:hasContents  inst:IfcCartesianPoint_333 ;
        list:hasNext      inst:IfcCartesianPoint_List_1660 .

inst:IfcCartesianPoint_List_1660
        list:hasContents  inst:IfcCartesianPoint_334 ;
        list:hasNext      inst:IfcCartesianPoint_List_1661 .

inst:IfcCartesianPoint_List_1661
        list:hasContents  inst:IfcCartesianPoint_335 ;
        list:hasNext      inst:IfcCartesianPoint_List_1662 .

inst:IfcCartesianPoint_List_1662
        list:hasContents  inst:IfcCartesianPoint_336 ;
        list:hasNext      inst:IfcCartesianPoint_List_1663 .

inst:IfcCartesianPoint_List_1663
        list:hasContents  inst:IfcCartesianPoint_337 ;
        list:hasNext      inst:IfcCartesianPoint_List_1664 .

inst:IfcCartesianPoint_List_1664
        list:hasContents  inst:IfcCartesianPoint_338 ;
        list:hasNext      inst:IfcCartesianPoint_List_1665 .

inst:IfcCartesianPoint_List_1665
        list:hasContents  inst:IfcCartesianPoint_339 ;
        list:hasNext      inst:IfcCartesianPoint_List_1666 .

inst:IfcCartesianPoint_List_1666
        list:hasContents  inst:IfcCartesianPoint_340 ;
        list:hasNext      inst:IfcCartesianPoint_List_1667 .

inst:IfcCartesianPoint_List_1667
        list:hasContents  inst:IfcCartesianPoint_341 ;
        list:hasNext      inst:IfcCartesianPoint_List_1668 .

inst:IfcCartesianPoint_List_1668
        list:hasContents  inst:IfcCartesianPoint_342 ;
        list:hasNext      inst:IfcCartesianPoint_List_1669 .

inst:IfcCartesianPoint_List_1669
        list:hasContents  inst:IfcCartesianPoint_343 ;
        list:hasNext      inst:IfcCartesianPoint_List_1670 .

inst:IfcCartesianPoint_List_1670
        list:hasContents  inst:IfcCartesianPoint_344 ;
        list:hasNext      inst:IfcCartesianPoint_List_1671 .

inst:IfcCartesianPoint_List_1671
        list:hasContents  inst:IfcCartesianPoint_345 ;
        list:hasNext      inst:IfcCartesianPoint_List_1672 .

inst:IfcCartesianPoint_List_1672
        list:hasContents  inst:IfcCartesianPoint_346 ;
        list:hasNext      inst:IfcCartesianPoint_List_1673 .

inst:IfcCartesianPoint_List_1673
        list:hasContents  inst:IfcCartesianPoint_347 ;
        list:hasNext      inst:IfcCartesianPoint_List_1674 .

inst:IfcCartesianPoint_List_1674
        list:hasContents  inst:IfcCartesianPoint_348 ;
        list:hasNext      inst:IfcCartesianPoint_List_1675 .

inst:IfcCartesianPoint_List_1675
        list:hasContents  inst:IfcCartesianPoint_349 ;
        list:hasNext      inst:IfcCartesianPoint_List_1676 .

inst:IfcCartesianPoint_List_1676
        list:hasContents  inst:IfcCartesianPoint_350 ;
        list:hasNext      inst:IfcCartesianPoint_List_1677 .

inst:IfcCartesianPoint_List_1677
        list:hasContents  inst:IfcCartesianPoint_351 ;
        list:hasNext      inst:IfcCartesianPoint_List_1678 .

inst:IfcCartesianPoint_List_1678
        list:hasContents  inst:IfcCartesianPoint_352 ;
        list:hasNext      inst:IfcCartesianPoint_List_1679 .

inst:IfcCartesianPoint_List_1679
        list:hasContents  inst:IfcCartesianPoint_353 ;
        list:hasNext      inst:IfcCartesianPoint_List_1680 .

inst:IfcCartesianPoint_List_1680
        list:hasContents  inst:IfcCartesianPoint_354 ;
        list:hasNext      inst:IfcCartesianPoint_List_1681 .

inst:IfcCartesianPoint_List_1681
        list:hasContents  inst:IfcCartesianPoint_355 ;
        list:hasNext      inst:IfcCartesianPoint_List_1682 .

inst:IfcCartesianPoint_List_1682
        list:hasContents  inst:IfcCartesianPoint_356 ;
        list:hasNext      inst:IfcCartesianPoint_List_1683 .

inst:IfcCartesianPoint_List_1683
        list:hasContents  inst:IfcCartesianPoint_357 ;
        list:hasNext      inst:IfcCartesianPoint_List_1684 .

inst:IfcCartesianPoint_List_1684
        list:hasContents  inst:IfcCartesianPoint_358 ;
        list:hasNext      inst:IfcCartesianPoint_List_1685 .

inst:IfcCartesianPoint_List_1685
        list:hasContents  inst:IfcCartesianPoint_359 ;
        list:hasNext      inst:IfcCartesianPoint_List_1686 .

inst:IfcCartesianPoint_List_1686
        list:hasContents  inst:IfcCartesianPoint_320 .

inst:IfcFaceOuterBound_369
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_368 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_370  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_369 .

inst:IfcPolyLoop_371  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_1687
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_371  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_1687 .

inst:IfcCartesianPoint_List_1688
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1689
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1687
        list:hasContents  inst:IfcCartesianPoint_200 ;
        list:hasNext      inst:IfcCartesianPoint_List_1688 .

inst:IfcCartesianPoint_List_1688
        list:hasContents  inst:IfcCartesianPoint_319 ;
        list:hasNext      inst:IfcCartesianPoint_List_1689 .

inst:IfcCartesianPoint_List_1689
        list:hasContents  inst:IfcCartesianPoint_280 .

inst:IfcFaceOuterBound_372
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_371 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_373  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_372 .

inst:IfcPolyLoop_374  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_1690
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_374  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_1690 .

inst:IfcCartesianPoint_List_1691
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1692
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1690
        list:hasContents  inst:IfcCartesianPoint_240 ;
        list:hasNext      inst:IfcCartesianPoint_List_1691 .

inst:IfcCartesianPoint_List_1691
        list:hasContents  inst:IfcCartesianPoint_318 ;
        list:hasNext      inst:IfcCartesianPoint_List_1692 .

inst:IfcCartesianPoint_List_1692
        list:hasContents  inst:IfcCartesianPoint_319 .

inst:IfcFaceOuterBound_375
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_374 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_376  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_375 .

inst:IfcPolyLoop_377  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_1693
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_377  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_1693 .

inst:IfcCartesianPoint_List_1694
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1695
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1693
        list:hasContents  inst:IfcCartesianPoint_239 ;
        list:hasNext      inst:IfcCartesianPoint_List_1694 .

inst:IfcCartesianPoint_List_1694
        list:hasContents  inst:IfcCartesianPoint_317 ;
        list:hasNext      inst:IfcCartesianPoint_List_1695 .

inst:IfcCartesianPoint_List_1695
        list:hasContents  inst:IfcCartesianPoint_318 .

inst:IfcFaceOuterBound_378
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_377 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_379  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_378 .

inst:IfcPolyLoop_380  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_1696
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_380  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_1696 .

inst:IfcCartesianPoint_List_1697
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1698
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1696
        list:hasContents  inst:IfcCartesianPoint_238 ;
        list:hasNext      inst:IfcCartesianPoint_List_1697 .

inst:IfcCartesianPoint_List_1697
        list:hasContents  inst:IfcCartesianPoint_316 ;
        list:hasNext      inst:IfcCartesianPoint_List_1698 .

inst:IfcCartesianPoint_List_1698
        list:hasContents  inst:IfcCartesianPoint_317 .

inst:IfcFaceOuterBound_381
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_380 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_382  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_381 .

inst:IfcPolyLoop_383  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_1699
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_383  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_1699 .

inst:IfcCartesianPoint_List_1700
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1701
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1699
        list:hasContents  inst:IfcCartesianPoint_237 ;
        list:hasNext      inst:IfcCartesianPoint_List_1700 .

inst:IfcCartesianPoint_List_1700
        list:hasContents  inst:IfcCartesianPoint_315 ;
        list:hasNext      inst:IfcCartesianPoint_List_1701 .

inst:IfcCartesianPoint_List_1701
        list:hasContents  inst:IfcCartesianPoint_316 .

inst:IfcFaceOuterBound_384
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_383 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_385  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_384 .

inst:IfcPolyLoop_386  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_1702
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_386  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_1702 .

inst:IfcCartesianPoint_List_1703
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1704
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1702
        list:hasContents  inst:IfcCartesianPoint_236 ;
        list:hasNext      inst:IfcCartesianPoint_List_1703 .

inst:IfcCartesianPoint_List_1703
        list:hasContents  inst:IfcCartesianPoint_314 ;
        list:hasNext      inst:IfcCartesianPoint_List_1704 .

inst:IfcCartesianPoint_List_1704
        list:hasContents  inst:IfcCartesianPoint_315 .

inst:IfcFaceOuterBound_387
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_386 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_388  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_387 .

inst:IfcPolyLoop_389  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_1705
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_389  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_1705 .

inst:IfcCartesianPoint_List_1706
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1707
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1705
        list:hasContents  inst:IfcCartesianPoint_235 ;
        list:hasNext      inst:IfcCartesianPoint_List_1706 .

inst:IfcCartesianPoint_List_1706
        list:hasContents  inst:IfcCartesianPoint_313 ;
        list:hasNext      inst:IfcCartesianPoint_List_1707 .

inst:IfcCartesianPoint_List_1707
        list:hasContents  inst:IfcCartesianPoint_314 .

inst:IfcFaceOuterBound_390
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_389 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_391  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_390 .

inst:IfcPolyLoop_392  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_1708
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_392  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_1708 .

inst:IfcCartesianPoint_List_1709
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1710
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1708
        list:hasContents  inst:IfcCartesianPoint_234 ;
        list:hasNext      inst:IfcCartesianPoint_List_1709 .

inst:IfcCartesianPoint_List_1709
        list:hasContents  inst:IfcCartesianPoint_312 ;
        list:hasNext      inst:IfcCartesianPoint_List_1710 .

inst:IfcCartesianPoint_List_1710
        list:hasContents  inst:IfcCartesianPoint_313 .

inst:IfcFaceOuterBound_393
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_392 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_394  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_393 .

inst:IfcPolyLoop_395  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_1711
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_395  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_1711 .

inst:IfcCartesianPoint_List_1712
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1713
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1711
        list:hasContents  inst:IfcCartesianPoint_233 ;
        list:hasNext      inst:IfcCartesianPoint_List_1712 .

inst:IfcCartesianPoint_List_1712
        list:hasContents  inst:IfcCartesianPoint_311 ;
        list:hasNext      inst:IfcCartesianPoint_List_1713 .

inst:IfcCartesianPoint_List_1713
        list:hasContents  inst:IfcCartesianPoint_312 .

inst:IfcFaceOuterBound_396
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_395 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_397  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_396 .

inst:IfcPolyLoop_398  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_1714
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_398  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_1714 .

inst:IfcCartesianPoint_List_1715
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1716
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1714
        list:hasContents  inst:IfcCartesianPoint_232 ;
        list:hasNext      inst:IfcCartesianPoint_List_1715 .

inst:IfcCartesianPoint_List_1715
        list:hasContents  inst:IfcCartesianPoint_310 ;
        list:hasNext      inst:IfcCartesianPoint_List_1716 .

inst:IfcCartesianPoint_List_1716
        list:hasContents  inst:IfcCartesianPoint_311 .

inst:IfcFaceOuterBound_399
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_398 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_400  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_399 .

inst:IfcPolyLoop_401  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_1717
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_401  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_1717 .

inst:IfcCartesianPoint_List_1718
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1719
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1717
        list:hasContents  inst:IfcCartesianPoint_231 ;
        list:hasNext      inst:IfcCartesianPoint_List_1718 .

inst:IfcCartesianPoint_List_1718
        list:hasContents  inst:IfcCartesianPoint_309 ;
        list:hasNext      inst:IfcCartesianPoint_List_1719 .

inst:IfcCartesianPoint_List_1719
        list:hasContents  inst:IfcCartesianPoint_310 .

inst:IfcFaceOuterBound_402
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_401 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_403  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_402 .

inst:IfcPolyLoop_404  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_1720
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_404  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_1720 .

inst:IfcCartesianPoint_List_1721
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1722
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1720
        list:hasContents  inst:IfcCartesianPoint_230 ;
        list:hasNext      inst:IfcCartesianPoint_List_1721 .

inst:IfcCartesianPoint_List_1721
        list:hasContents  inst:IfcCartesianPoint_308 ;
        list:hasNext      inst:IfcCartesianPoint_List_1722 .

inst:IfcCartesianPoint_List_1722
        list:hasContents  inst:IfcCartesianPoint_309 .

inst:IfcFaceOuterBound_405
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_404 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_406  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_405 .

inst:IfcPolyLoop_407  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_1723
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_407  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_1723 .

inst:IfcCartesianPoint_List_1724
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1725
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1723
        list:hasContents  inst:IfcCartesianPoint_229 ;
        list:hasNext      inst:IfcCartesianPoint_List_1724 .

inst:IfcCartesianPoint_List_1724
        list:hasContents  inst:IfcCartesianPoint_307 ;
        list:hasNext      inst:IfcCartesianPoint_List_1725 .

inst:IfcCartesianPoint_List_1725
        list:hasContents  inst:IfcCartesianPoint_308 .

inst:IfcFaceOuterBound_408
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_407 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_409  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_408 .

inst:IfcPolyLoop_410  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_1726
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_410  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_1726 .

inst:IfcCartesianPoint_List_1727
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1728
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1726
        list:hasContents  inst:IfcCartesianPoint_228 ;
        list:hasNext      inst:IfcCartesianPoint_List_1727 .

inst:IfcCartesianPoint_List_1727
        list:hasContents  inst:IfcCartesianPoint_306 ;
        list:hasNext      inst:IfcCartesianPoint_List_1728 .

inst:IfcCartesianPoint_List_1728
        list:hasContents  inst:IfcCartesianPoint_307 .

inst:IfcFaceOuterBound_411
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_410 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_412  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_411 .

inst:IfcPolyLoop_413  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_1729
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_413  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_1729 .

inst:IfcCartesianPoint_List_1730
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1731
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1729
        list:hasContents  inst:IfcCartesianPoint_227 ;
        list:hasNext      inst:IfcCartesianPoint_List_1730 .

inst:IfcCartesianPoint_List_1730
        list:hasContents  inst:IfcCartesianPoint_305 ;
        list:hasNext      inst:IfcCartesianPoint_List_1731 .

inst:IfcCartesianPoint_List_1731
        list:hasContents  inst:IfcCartesianPoint_306 .

inst:IfcFaceOuterBound_414
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_413 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_415  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_414 .

inst:IfcPolyLoop_416  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_1732
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_416  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_1732 .

inst:IfcCartesianPoint_List_1733
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1734
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1732
        list:hasContents  inst:IfcCartesianPoint_226 ;
        list:hasNext      inst:IfcCartesianPoint_List_1733 .

inst:IfcCartesianPoint_List_1733
        list:hasContents  inst:IfcCartesianPoint_304 ;
        list:hasNext      inst:IfcCartesianPoint_List_1734 .

inst:IfcCartesianPoint_List_1734
        list:hasContents  inst:IfcCartesianPoint_305 .

inst:IfcFaceOuterBound_417
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_416 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_418  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_417 .

inst:IfcPolyLoop_419  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_1735
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_419  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_1735 .

inst:IfcCartesianPoint_List_1736
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1737
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1735
        list:hasContents  inst:IfcCartesianPoint_225 ;
        list:hasNext      inst:IfcCartesianPoint_List_1736 .

inst:IfcCartesianPoint_List_1736
        list:hasContents  inst:IfcCartesianPoint_303 ;
        list:hasNext      inst:IfcCartesianPoint_List_1737 .

inst:IfcCartesianPoint_List_1737
        list:hasContents  inst:IfcCartesianPoint_304 .

inst:IfcFaceOuterBound_420
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_419 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_421  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_420 .

inst:IfcPolyLoop_422  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_1738
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_422  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_1738 .

inst:IfcCartesianPoint_List_1739
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1740
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1738
        list:hasContents  inst:IfcCartesianPoint_224 ;
        list:hasNext      inst:IfcCartesianPoint_List_1739 .

inst:IfcCartesianPoint_List_1739
        list:hasContents  inst:IfcCartesianPoint_302 ;
        list:hasNext      inst:IfcCartesianPoint_List_1740 .

inst:IfcCartesianPoint_List_1740
        list:hasContents  inst:IfcCartesianPoint_303 .

inst:IfcFaceOuterBound_423
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_422 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_424  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_423 .

inst:IfcPolyLoop_425  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_1741
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_425  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_1741 .

inst:IfcCartesianPoint_List_1742
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1743
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1741
        list:hasContents  inst:IfcCartesianPoint_223 ;
        list:hasNext      inst:IfcCartesianPoint_List_1742 .

inst:IfcCartesianPoint_List_1742
        list:hasContents  inst:IfcCartesianPoint_301 ;
        list:hasNext      inst:IfcCartesianPoint_List_1743 .

inst:IfcCartesianPoint_List_1743
        list:hasContents  inst:IfcCartesianPoint_302 .

inst:IfcFaceOuterBound_426
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_425 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_427  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_426 .

inst:IfcPolyLoop_428  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_1744
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_428  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_1744 .

inst:IfcCartesianPoint_List_1745
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1746
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1744
        list:hasContents  inst:IfcCartesianPoint_222 ;
        list:hasNext      inst:IfcCartesianPoint_List_1745 .

inst:IfcCartesianPoint_List_1745
        list:hasContents  inst:IfcCartesianPoint_300 ;
        list:hasNext      inst:IfcCartesianPoint_List_1746 .

inst:IfcCartesianPoint_List_1746
        list:hasContents  inst:IfcCartesianPoint_301 .

inst:IfcFaceOuterBound_429
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_428 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_430  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_429 .

inst:IfcPolyLoop_431  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_1747
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_431  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_1747 .

inst:IfcCartesianPoint_List_1748
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1749
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1747
        list:hasContents  inst:IfcCartesianPoint_221 ;
        list:hasNext      inst:IfcCartesianPoint_List_1748 .

inst:IfcCartesianPoint_List_1748
        list:hasContents  inst:IfcCartesianPoint_299 ;
        list:hasNext      inst:IfcCartesianPoint_List_1749 .

inst:IfcCartesianPoint_List_1749
        list:hasContents  inst:IfcCartesianPoint_300 .

inst:IfcFaceOuterBound_432
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_431 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_433  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_432 .

inst:IfcPolyLoop_434  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_1750
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_434  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_1750 .

inst:IfcCartesianPoint_List_1751
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1752
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1750
        list:hasContents  inst:IfcCartesianPoint_220 ;
        list:hasNext      inst:IfcCartesianPoint_List_1751 .

inst:IfcCartesianPoint_List_1751
        list:hasContents  inst:IfcCartesianPoint_298 ;
        list:hasNext      inst:IfcCartesianPoint_List_1752 .

inst:IfcCartesianPoint_List_1752
        list:hasContents  inst:IfcCartesianPoint_299 .

inst:IfcFaceOuterBound_435
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_434 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_436  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_435 .

inst:IfcPolyLoop_437  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_1753
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_437  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_1753 .

inst:IfcCartesianPoint_List_1754
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1755
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1753
        list:hasContents  inst:IfcCartesianPoint_219 ;
        list:hasNext      inst:IfcCartesianPoint_List_1754 .

inst:IfcCartesianPoint_List_1754
        list:hasContents  inst:IfcCartesianPoint_297 ;
        list:hasNext      inst:IfcCartesianPoint_List_1755 .

inst:IfcCartesianPoint_List_1755
        list:hasContents  inst:IfcCartesianPoint_298 .

inst:IfcFaceOuterBound_438
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_437 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_439  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_438 .

inst:IfcPolyLoop_440  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_1756
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_440  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_1756 .

inst:IfcCartesianPoint_List_1757
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1758
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1756
        list:hasContents  inst:IfcCartesianPoint_218 ;
        list:hasNext      inst:IfcCartesianPoint_List_1757 .

inst:IfcCartesianPoint_List_1757
        list:hasContents  inst:IfcCartesianPoint_296 ;
        list:hasNext      inst:IfcCartesianPoint_List_1758 .

inst:IfcCartesianPoint_List_1758
        list:hasContents  inst:IfcCartesianPoint_297 .

inst:IfcFaceOuterBound_441
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_440 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_442  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_441 .

inst:IfcPolyLoop_443  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_1759
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_443  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_1759 .

inst:IfcCartesianPoint_List_1760
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1761
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1759
        list:hasContents  inst:IfcCartesianPoint_217 ;
        list:hasNext      inst:IfcCartesianPoint_List_1760 .

inst:IfcCartesianPoint_List_1760
        list:hasContents  inst:IfcCartesianPoint_295 ;
        list:hasNext      inst:IfcCartesianPoint_List_1761 .

inst:IfcCartesianPoint_List_1761
        list:hasContents  inst:IfcCartesianPoint_296 .

inst:IfcFaceOuterBound_444
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_443 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_445  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_444 .

inst:IfcPolyLoop_446  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_1762
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_446  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_1762 .

inst:IfcCartesianPoint_List_1763
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1764
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1762
        list:hasContents  inst:IfcCartesianPoint_216 ;
        list:hasNext      inst:IfcCartesianPoint_List_1763 .

inst:IfcCartesianPoint_List_1763
        list:hasContents  inst:IfcCartesianPoint_294 ;
        list:hasNext      inst:IfcCartesianPoint_List_1764 .

inst:IfcCartesianPoint_List_1764
        list:hasContents  inst:IfcCartesianPoint_295 .

inst:IfcFaceOuterBound_447
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_446 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_448  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_447 .

inst:IfcPolyLoop_449  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_1765
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_449  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_1765 .

inst:IfcCartesianPoint_List_1766
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1767
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1765
        list:hasContents  inst:IfcCartesianPoint_215 ;
        list:hasNext      inst:IfcCartesianPoint_List_1766 .

inst:IfcCartesianPoint_List_1766
        list:hasContents  inst:IfcCartesianPoint_293 ;
        list:hasNext      inst:IfcCartesianPoint_List_1767 .

inst:IfcCartesianPoint_List_1767
        list:hasContents  inst:IfcCartesianPoint_294 .

inst:IfcFaceOuterBound_450
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_449 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_451  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_450 .

inst:IfcPolyLoop_452  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_1768
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_452  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_1768 .

inst:IfcCartesianPoint_List_1769
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1770
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1768
        list:hasContents  inst:IfcCartesianPoint_214 ;
        list:hasNext      inst:IfcCartesianPoint_List_1769 .

inst:IfcCartesianPoint_List_1769
        list:hasContents  inst:IfcCartesianPoint_292 ;
        list:hasNext      inst:IfcCartesianPoint_List_1770 .

inst:IfcCartesianPoint_List_1770
        list:hasContents  inst:IfcCartesianPoint_293 .

inst:IfcFaceOuterBound_453
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_452 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_454  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_453 .

inst:IfcPolyLoop_455  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_1771
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_455  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_1771 .

inst:IfcCartesianPoint_List_1772
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1773
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1771
        list:hasContents  inst:IfcCartesianPoint_213 ;
        list:hasNext      inst:IfcCartesianPoint_List_1772 .

inst:IfcCartesianPoint_List_1772
        list:hasContents  inst:IfcCartesianPoint_291 ;
        list:hasNext      inst:IfcCartesianPoint_List_1773 .

inst:IfcCartesianPoint_List_1773
        list:hasContents  inst:IfcCartesianPoint_292 .

inst:IfcFaceOuterBound_456
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_455 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_457  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_456 .

inst:IfcPolyLoop_458  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_1774
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_458  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_1774 .

inst:IfcCartesianPoint_List_1775
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1776
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1774
        list:hasContents  inst:IfcCartesianPoint_212 ;
        list:hasNext      inst:IfcCartesianPoint_List_1775 .

inst:IfcCartesianPoint_List_1775
        list:hasContents  inst:IfcCartesianPoint_290 ;
        list:hasNext      inst:IfcCartesianPoint_List_1776 .

inst:IfcCartesianPoint_List_1776
        list:hasContents  inst:IfcCartesianPoint_291 .

inst:IfcFaceOuterBound_459
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_458 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_460  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_459 .

inst:IfcPolyLoop_461  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_1777
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_461  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_1777 .

inst:IfcCartesianPoint_List_1778
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1779
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1777
        list:hasContents  inst:IfcCartesianPoint_211 ;
        list:hasNext      inst:IfcCartesianPoint_List_1778 .

inst:IfcCartesianPoint_List_1778
        list:hasContents  inst:IfcCartesianPoint_289 ;
        list:hasNext      inst:IfcCartesianPoint_List_1779 .

inst:IfcCartesianPoint_List_1779
        list:hasContents  inst:IfcCartesianPoint_290 .

inst:IfcFaceOuterBound_462
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_461 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_463  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_462 .

inst:IfcPolyLoop_464  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_1780
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_464  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_1780 .

inst:IfcCartesianPoint_List_1781
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1782
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1780
        list:hasContents  inst:IfcCartesianPoint_210 ;
        list:hasNext      inst:IfcCartesianPoint_List_1781 .

inst:IfcCartesianPoint_List_1781
        list:hasContents  inst:IfcCartesianPoint_288 ;
        list:hasNext      inst:IfcCartesianPoint_List_1782 .

inst:IfcCartesianPoint_List_1782
        list:hasContents  inst:IfcCartesianPoint_289 .

inst:IfcFaceOuterBound_465
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_464 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_466  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_465 .

inst:IfcPolyLoop_467  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_1783
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_467  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_1783 .

inst:IfcCartesianPoint_List_1784
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1785
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1783
        list:hasContents  inst:IfcCartesianPoint_209 ;
        list:hasNext      inst:IfcCartesianPoint_List_1784 .

inst:IfcCartesianPoint_List_1784
        list:hasContents  inst:IfcCartesianPoint_287 ;
        list:hasNext      inst:IfcCartesianPoint_List_1785 .

inst:IfcCartesianPoint_List_1785
        list:hasContents  inst:IfcCartesianPoint_288 .

inst:IfcFaceOuterBound_468
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_467 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_469  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_468 .

inst:IfcPolyLoop_470  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_1786
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_470  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_1786 .

inst:IfcCartesianPoint_List_1787
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1788
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1786
        list:hasContents  inst:IfcCartesianPoint_208 ;
        list:hasNext      inst:IfcCartesianPoint_List_1787 .

inst:IfcCartesianPoint_List_1787
        list:hasContents  inst:IfcCartesianPoint_286 ;
        list:hasNext      inst:IfcCartesianPoint_List_1788 .

inst:IfcCartesianPoint_List_1788
        list:hasContents  inst:IfcCartesianPoint_287 .

inst:IfcFaceOuterBound_471
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_470 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_472  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_471 .

inst:IfcPolyLoop_473  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_1789
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_473  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_1789 .

inst:IfcCartesianPoint_List_1790
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1791
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1789
        list:hasContents  inst:IfcCartesianPoint_207 ;
        list:hasNext      inst:IfcCartesianPoint_List_1790 .

inst:IfcCartesianPoint_List_1790
        list:hasContents  inst:IfcCartesianPoint_285 ;
        list:hasNext      inst:IfcCartesianPoint_List_1791 .

inst:IfcCartesianPoint_List_1791
        list:hasContents  inst:IfcCartesianPoint_286 .

inst:IfcFaceOuterBound_474
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_473 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_475  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_474 .

inst:IfcPolyLoop_476  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_1792
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_476  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_1792 .

inst:IfcCartesianPoint_List_1793
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1794
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1792
        list:hasContents  inst:IfcCartesianPoint_206 ;
        list:hasNext      inst:IfcCartesianPoint_List_1793 .

inst:IfcCartesianPoint_List_1793
        list:hasContents  inst:IfcCartesianPoint_284 ;
        list:hasNext      inst:IfcCartesianPoint_List_1794 .

inst:IfcCartesianPoint_List_1794
        list:hasContents  inst:IfcCartesianPoint_285 .

inst:IfcFaceOuterBound_477
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_476 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_478  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_477 .

inst:IfcPolyLoop_479  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_1795
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_479  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_1795 .

inst:IfcCartesianPoint_List_1796
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1797
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1795
        list:hasContents  inst:IfcCartesianPoint_205 ;
        list:hasNext      inst:IfcCartesianPoint_List_1796 .

inst:IfcCartesianPoint_List_1796
        list:hasContents  inst:IfcCartesianPoint_283 ;
        list:hasNext      inst:IfcCartesianPoint_List_1797 .

inst:IfcCartesianPoint_List_1797
        list:hasContents  inst:IfcCartesianPoint_284 .

inst:IfcFaceOuterBound_480
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_479 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_481  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_480 .

inst:IfcPolyLoop_482  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_1798
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_482  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_1798 .

inst:IfcCartesianPoint_List_1799
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1800
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1798
        list:hasContents  inst:IfcCartesianPoint_204 ;
        list:hasNext      inst:IfcCartesianPoint_List_1799 .

inst:IfcCartesianPoint_List_1799
        list:hasContents  inst:IfcCartesianPoint_282 ;
        list:hasNext      inst:IfcCartesianPoint_List_1800 .

inst:IfcCartesianPoint_List_1800
        list:hasContents  inst:IfcCartesianPoint_283 .

inst:IfcFaceOuterBound_483
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_482 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_484  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_483 .

inst:IfcPolyLoop_485  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_1801
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_485  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_1801 .

inst:IfcCartesianPoint_List_1802
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1803
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1801
        list:hasContents  inst:IfcCartesianPoint_203 ;
        list:hasNext      inst:IfcCartesianPoint_List_1802 .

inst:IfcCartesianPoint_List_1802
        list:hasContents  inst:IfcCartesianPoint_281 ;
        list:hasNext      inst:IfcCartesianPoint_List_1803 .

inst:IfcCartesianPoint_List_1803
        list:hasContents  inst:IfcCartesianPoint_282 .

inst:IfcFaceOuterBound_486
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_485 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_487  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_486 .

inst:IfcPolyLoop_488  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_1804
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_488  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_1804 .

inst:IfcCartesianPoint_List_1805
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1806
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1804
        list:hasContents  inst:IfcCartesianPoint_202 ;
        list:hasNext      inst:IfcCartesianPoint_List_1805 .

inst:IfcCartesianPoint_List_1805
        list:hasContents  inst:IfcCartesianPoint_280 ;
        list:hasNext      inst:IfcCartesianPoint_List_1806 .

inst:IfcCartesianPoint_List_1806
        list:hasContents  inst:IfcCartesianPoint_281 .

inst:IfcFaceOuterBound_489
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_488 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_490  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_489 .

inst:IfcPolyLoop_491  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_1807
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_491  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_1807 .

inst:IfcCartesianPoint_List_1808
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1809
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1807
        list:hasContents  inst:IfcCartesianPoint_200 ;
        list:hasNext      inst:IfcCartesianPoint_List_1808 .

inst:IfcCartesianPoint_List_1808
        list:hasContents  inst:IfcCartesianPoint_240 ;
        list:hasNext      inst:IfcCartesianPoint_List_1809 .

inst:IfcCartesianPoint_List_1809
        list:hasContents  inst:IfcCartesianPoint_319 .

inst:IfcFaceOuterBound_492
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_491 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_493  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_492 .

inst:IfcPolyLoop_494  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_1810
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_494  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_1810 .

inst:IfcCartesianPoint_List_1811
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1812
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1810
        list:hasContents  inst:IfcCartesianPoint_240 ;
        list:hasNext      inst:IfcCartesianPoint_List_1811 .

inst:IfcCartesianPoint_List_1811
        list:hasContents  inst:IfcCartesianPoint_239 ;
        list:hasNext      inst:IfcCartesianPoint_List_1812 .

inst:IfcCartesianPoint_List_1812
        list:hasContents  inst:IfcCartesianPoint_318 .

inst:IfcFaceOuterBound_495
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_494 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_496  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_495 .

inst:IfcPolyLoop_497  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_1813
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_497  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_1813 .

inst:IfcCartesianPoint_List_1814
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1815
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1813
        list:hasContents  inst:IfcCartesianPoint_239 ;
        list:hasNext      inst:IfcCartesianPoint_List_1814 .

inst:IfcCartesianPoint_List_1814
        list:hasContents  inst:IfcCartesianPoint_238 ;
        list:hasNext      inst:IfcCartesianPoint_List_1815 .

inst:IfcCartesianPoint_List_1815
        list:hasContents  inst:IfcCartesianPoint_317 .

inst:IfcFaceOuterBound_498
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_497 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_499  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_498 .

inst:IfcPolyLoop_500  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_1816
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_500  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_1816 .

inst:IfcCartesianPoint_List_1817
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1818
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1816
        list:hasContents  inst:IfcCartesianPoint_238 ;
        list:hasNext      inst:IfcCartesianPoint_List_1817 .

inst:IfcCartesianPoint_List_1817
        list:hasContents  inst:IfcCartesianPoint_237 ;
        list:hasNext      inst:IfcCartesianPoint_List_1818 .

inst:IfcCartesianPoint_List_1818
        list:hasContents  inst:IfcCartesianPoint_316 .

inst:IfcFaceOuterBound_501
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_500 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_502  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_501 .

inst:IfcPolyLoop_503  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_1819
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_503  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_1819 .

inst:IfcCartesianPoint_List_1820
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1821
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1819
        list:hasContents  inst:IfcCartesianPoint_237 ;
        list:hasNext      inst:IfcCartesianPoint_List_1820 .

inst:IfcCartesianPoint_List_1820
        list:hasContents  inst:IfcCartesianPoint_236 ;
        list:hasNext      inst:IfcCartesianPoint_List_1821 .

inst:IfcCartesianPoint_List_1821
        list:hasContents  inst:IfcCartesianPoint_315 .

inst:IfcFaceOuterBound_504
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_503 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_505  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_504 .

inst:IfcPolyLoop_506  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_1822
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_506  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_1822 .

inst:IfcCartesianPoint_List_1823
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1824
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1822
        list:hasContents  inst:IfcCartesianPoint_236 ;
        list:hasNext      inst:IfcCartesianPoint_List_1823 .

inst:IfcCartesianPoint_List_1823
        list:hasContents  inst:IfcCartesianPoint_235 ;
        list:hasNext      inst:IfcCartesianPoint_List_1824 .

inst:IfcCartesianPoint_List_1824
        list:hasContents  inst:IfcCartesianPoint_314 .

inst:IfcFaceOuterBound_507
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_506 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_508  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_507 .

inst:IfcPolyLoop_509  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_1825
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_509  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_1825 .

inst:IfcCartesianPoint_List_1826
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1827
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1825
        list:hasContents  inst:IfcCartesianPoint_235 ;
        list:hasNext      inst:IfcCartesianPoint_List_1826 .

inst:IfcCartesianPoint_List_1826
        list:hasContents  inst:IfcCartesianPoint_234 ;
        list:hasNext      inst:IfcCartesianPoint_List_1827 .

inst:IfcCartesianPoint_List_1827
        list:hasContents  inst:IfcCartesianPoint_313 .

inst:IfcFaceOuterBound_510
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_509 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_511  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_510 .

inst:IfcPolyLoop_512  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_1828
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_512  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_1828 .

inst:IfcCartesianPoint_List_1829
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1830
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1828
        list:hasContents  inst:IfcCartesianPoint_234 ;
        list:hasNext      inst:IfcCartesianPoint_List_1829 .

inst:IfcCartesianPoint_List_1829
        list:hasContents  inst:IfcCartesianPoint_233 ;
        list:hasNext      inst:IfcCartesianPoint_List_1830 .

inst:IfcCartesianPoint_List_1830
        list:hasContents  inst:IfcCartesianPoint_312 .

inst:IfcFaceOuterBound_513
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_512 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_514  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_513 .

inst:IfcPolyLoop_515  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_1831
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_515  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_1831 .

inst:IfcCartesianPoint_List_1832
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1833
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1831
        list:hasContents  inst:IfcCartesianPoint_233 ;
        list:hasNext      inst:IfcCartesianPoint_List_1832 .

inst:IfcCartesianPoint_List_1832
        list:hasContents  inst:IfcCartesianPoint_232 ;
        list:hasNext      inst:IfcCartesianPoint_List_1833 .

inst:IfcCartesianPoint_List_1833
        list:hasContents  inst:IfcCartesianPoint_311 .

inst:IfcFaceOuterBound_516
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_515 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_517  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_516 .

inst:IfcPolyLoop_518  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_1834
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_518  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_1834 .

inst:IfcCartesianPoint_List_1835
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1836
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1834
        list:hasContents  inst:IfcCartesianPoint_232 ;
        list:hasNext      inst:IfcCartesianPoint_List_1835 .

inst:IfcCartesianPoint_List_1835
        list:hasContents  inst:IfcCartesianPoint_231 ;
        list:hasNext      inst:IfcCartesianPoint_List_1836 .

inst:IfcCartesianPoint_List_1836
        list:hasContents  inst:IfcCartesianPoint_310 .

inst:IfcFaceOuterBound_519
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_518 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_520  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_519 .

inst:IfcPolyLoop_521  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_1837
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_521  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_1837 .

inst:IfcCartesianPoint_List_1838
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1839
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1837
        list:hasContents  inst:IfcCartesianPoint_231 ;
        list:hasNext      inst:IfcCartesianPoint_List_1838 .

inst:IfcCartesianPoint_List_1838
        list:hasContents  inst:IfcCartesianPoint_230 ;
        list:hasNext      inst:IfcCartesianPoint_List_1839 .

inst:IfcCartesianPoint_List_1839
        list:hasContents  inst:IfcCartesianPoint_309 .

inst:IfcFaceOuterBound_522
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_521 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_523  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_522 .

inst:IfcPolyLoop_524  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_1840
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_524  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_1840 .

inst:IfcCartesianPoint_List_1841
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1842
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1840
        list:hasContents  inst:IfcCartesianPoint_230 ;
        list:hasNext      inst:IfcCartesianPoint_List_1841 .

inst:IfcCartesianPoint_List_1841
        list:hasContents  inst:IfcCartesianPoint_229 ;
        list:hasNext      inst:IfcCartesianPoint_List_1842 .

inst:IfcCartesianPoint_List_1842
        list:hasContents  inst:IfcCartesianPoint_308 .

inst:IfcFaceOuterBound_525
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_524 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_526  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_525 .

inst:IfcPolyLoop_527  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_1843
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_527  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_1843 .

inst:IfcCartesianPoint_List_1844
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1845
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1843
        list:hasContents  inst:IfcCartesianPoint_229 ;
        list:hasNext      inst:IfcCartesianPoint_List_1844 .

inst:IfcCartesianPoint_List_1844
        list:hasContents  inst:IfcCartesianPoint_228 ;
        list:hasNext      inst:IfcCartesianPoint_List_1845 .

inst:IfcCartesianPoint_List_1845
        list:hasContents  inst:IfcCartesianPoint_307 .

inst:IfcFaceOuterBound_528
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_527 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_529  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_528 .

inst:IfcPolyLoop_530  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_1846
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_530  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_1846 .

inst:IfcCartesianPoint_List_1847
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1848
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1846
        list:hasContents  inst:IfcCartesianPoint_228 ;
        list:hasNext      inst:IfcCartesianPoint_List_1847 .

inst:IfcCartesianPoint_List_1847
        list:hasContents  inst:IfcCartesianPoint_227 ;
        list:hasNext      inst:IfcCartesianPoint_List_1848 .

inst:IfcCartesianPoint_List_1848
        list:hasContents  inst:IfcCartesianPoint_306 .

inst:IfcFaceOuterBound_531
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_530 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_532  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_531 .

inst:IfcPolyLoop_533  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_1849
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_533  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_1849 .

inst:IfcCartesianPoint_List_1850
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1851
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1849
        list:hasContents  inst:IfcCartesianPoint_227 ;
        list:hasNext      inst:IfcCartesianPoint_List_1850 .

inst:IfcCartesianPoint_List_1850
        list:hasContents  inst:IfcCartesianPoint_226 ;
        list:hasNext      inst:IfcCartesianPoint_List_1851 .

inst:IfcCartesianPoint_List_1851
        list:hasContents  inst:IfcCartesianPoint_305 .

inst:IfcFaceOuterBound_534
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_533 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_535  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_534 .

inst:IfcPolyLoop_536  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_1852
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_536  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_1852 .

inst:IfcCartesianPoint_List_1853
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1854
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1852
        list:hasContents  inst:IfcCartesianPoint_226 ;
        list:hasNext      inst:IfcCartesianPoint_List_1853 .

inst:IfcCartesianPoint_List_1853
        list:hasContents  inst:IfcCartesianPoint_225 ;
        list:hasNext      inst:IfcCartesianPoint_List_1854 .

inst:IfcCartesianPoint_List_1854
        list:hasContents  inst:IfcCartesianPoint_304 .

inst:IfcFaceOuterBound_537
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_536 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_538  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_537 .

inst:IfcPolyLoop_539  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_1855
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_539  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_1855 .

inst:IfcCartesianPoint_List_1856
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1857
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1855
        list:hasContents  inst:IfcCartesianPoint_225 ;
        list:hasNext      inst:IfcCartesianPoint_List_1856 .

inst:IfcCartesianPoint_List_1856
        list:hasContents  inst:IfcCartesianPoint_224 ;
        list:hasNext      inst:IfcCartesianPoint_List_1857 .

inst:IfcCartesianPoint_List_1857
        list:hasContents  inst:IfcCartesianPoint_303 .

inst:IfcFaceOuterBound_540
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_539 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_541  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_540 .

inst:IfcPolyLoop_542  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_1858
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_542  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_1858 .

inst:IfcCartesianPoint_List_1859
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1860
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1858
        list:hasContents  inst:IfcCartesianPoint_224 ;
        list:hasNext      inst:IfcCartesianPoint_List_1859 .

inst:IfcCartesianPoint_List_1859
        list:hasContents  inst:IfcCartesianPoint_223 ;
        list:hasNext      inst:IfcCartesianPoint_List_1860 .

inst:IfcCartesianPoint_List_1860
        list:hasContents  inst:IfcCartesianPoint_302 .

inst:IfcFaceOuterBound_543
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_542 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_544  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_543 .

inst:IfcPolyLoop_545  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_1861
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_545  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_1861 .

inst:IfcCartesianPoint_List_1862
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1863
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1861
        list:hasContents  inst:IfcCartesianPoint_223 ;
        list:hasNext      inst:IfcCartesianPoint_List_1862 .

inst:IfcCartesianPoint_List_1862
        list:hasContents  inst:IfcCartesianPoint_222 ;
        list:hasNext      inst:IfcCartesianPoint_List_1863 .

inst:IfcCartesianPoint_List_1863
        list:hasContents  inst:IfcCartesianPoint_301 .

inst:IfcFaceOuterBound_546
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_545 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_547  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_546 .

inst:IfcPolyLoop_548  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_1864
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_548  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_1864 .

inst:IfcCartesianPoint_List_1865
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1866
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1864
        list:hasContents  inst:IfcCartesianPoint_222 ;
        list:hasNext      inst:IfcCartesianPoint_List_1865 .

inst:IfcCartesianPoint_List_1865
        list:hasContents  inst:IfcCartesianPoint_221 ;
        list:hasNext      inst:IfcCartesianPoint_List_1866 .

inst:IfcCartesianPoint_List_1866
        list:hasContents  inst:IfcCartesianPoint_300 .

inst:IfcFaceOuterBound_549
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_548 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_550  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_549 .

inst:IfcPolyLoop_551  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_1867
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_551  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_1867 .

inst:IfcCartesianPoint_List_1868
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1869
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1867
        list:hasContents  inst:IfcCartesianPoint_221 ;
        list:hasNext      inst:IfcCartesianPoint_List_1868 .

inst:IfcCartesianPoint_List_1868
        list:hasContents  inst:IfcCartesianPoint_220 ;
        list:hasNext      inst:IfcCartesianPoint_List_1869 .

inst:IfcCartesianPoint_List_1869
        list:hasContents  inst:IfcCartesianPoint_299 .

inst:IfcFaceOuterBound_552
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_551 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_553  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_552 .

inst:IfcPolyLoop_554  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_1870
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_554  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_1870 .

inst:IfcCartesianPoint_List_1871
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1872
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1870
        list:hasContents  inst:IfcCartesianPoint_220 ;
        list:hasNext      inst:IfcCartesianPoint_List_1871 .

inst:IfcCartesianPoint_List_1871
        list:hasContents  inst:IfcCartesianPoint_219 ;
        list:hasNext      inst:IfcCartesianPoint_List_1872 .

inst:IfcCartesianPoint_List_1872
        list:hasContents  inst:IfcCartesianPoint_298 .

inst:IfcFaceOuterBound_555
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_554 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_556  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_555 .

inst:IfcPolyLoop_557  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_1873
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_557  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_1873 .

inst:IfcCartesianPoint_List_1874
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1875
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1873
        list:hasContents  inst:IfcCartesianPoint_219 ;
        list:hasNext      inst:IfcCartesianPoint_List_1874 .

inst:IfcCartesianPoint_List_1874
        list:hasContents  inst:IfcCartesianPoint_218 ;
        list:hasNext      inst:IfcCartesianPoint_List_1875 .

inst:IfcCartesianPoint_List_1875
        list:hasContents  inst:IfcCartesianPoint_297 .

inst:IfcFaceOuterBound_558
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_557 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_559  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_558 .

inst:IfcPolyLoop_560  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_1876
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_560  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_1876 .

inst:IfcCartesianPoint_List_1877
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1878
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1876
        list:hasContents  inst:IfcCartesianPoint_218 ;
        list:hasNext      inst:IfcCartesianPoint_List_1877 .

inst:IfcCartesianPoint_List_1877
        list:hasContents  inst:IfcCartesianPoint_217 ;
        list:hasNext      inst:IfcCartesianPoint_List_1878 .

inst:IfcCartesianPoint_List_1878
        list:hasContents  inst:IfcCartesianPoint_296 .

inst:IfcFaceOuterBound_561
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_560 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_562  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_561 .

inst:IfcPolyLoop_563  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_1879
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_563  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_1879 .

inst:IfcCartesianPoint_List_1880
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1881
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1879
        list:hasContents  inst:IfcCartesianPoint_217 ;
        list:hasNext      inst:IfcCartesianPoint_List_1880 .

inst:IfcCartesianPoint_List_1880
        list:hasContents  inst:IfcCartesianPoint_216 ;
        list:hasNext      inst:IfcCartesianPoint_List_1881 .

inst:IfcCartesianPoint_List_1881
        list:hasContents  inst:IfcCartesianPoint_295 .

inst:IfcFaceOuterBound_564
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_563 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_565  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_564 .

inst:IfcPolyLoop_566  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_1882
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_566  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_1882 .

inst:IfcCartesianPoint_List_1883
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1884
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1882
        list:hasContents  inst:IfcCartesianPoint_216 ;
        list:hasNext      inst:IfcCartesianPoint_List_1883 .

inst:IfcCartesianPoint_List_1883
        list:hasContents  inst:IfcCartesianPoint_215 ;
        list:hasNext      inst:IfcCartesianPoint_List_1884 .

inst:IfcCartesianPoint_List_1884
        list:hasContents  inst:IfcCartesianPoint_294 .

inst:IfcFaceOuterBound_567
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_566 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_568  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_567 .

inst:IfcPolyLoop_569  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_1885
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_569  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_1885 .

inst:IfcCartesianPoint_List_1886
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1887
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1885
        list:hasContents  inst:IfcCartesianPoint_215 ;
        list:hasNext      inst:IfcCartesianPoint_List_1886 .

inst:IfcCartesianPoint_List_1886
        list:hasContents  inst:IfcCartesianPoint_214 ;
        list:hasNext      inst:IfcCartesianPoint_List_1887 .

inst:IfcCartesianPoint_List_1887
        list:hasContents  inst:IfcCartesianPoint_293 .

inst:IfcFaceOuterBound_570
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_569 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_571  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_570 .

inst:IfcPolyLoop_572  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_1888
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_572  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_1888 .

inst:IfcCartesianPoint_List_1889
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1890
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1888
        list:hasContents  inst:IfcCartesianPoint_214 ;
        list:hasNext      inst:IfcCartesianPoint_List_1889 .

inst:IfcCartesianPoint_List_1889
        list:hasContents  inst:IfcCartesianPoint_213 ;
        list:hasNext      inst:IfcCartesianPoint_List_1890 .

inst:IfcCartesianPoint_List_1890
        list:hasContents  inst:IfcCartesianPoint_292 .

inst:IfcFaceOuterBound_573
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_572 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_574  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_573 .

inst:IfcPolyLoop_575  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_1891
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_575  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_1891 .

inst:IfcCartesianPoint_List_1892
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1893
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1891
        list:hasContents  inst:IfcCartesianPoint_213 ;
        list:hasNext      inst:IfcCartesianPoint_List_1892 .

inst:IfcCartesianPoint_List_1892
        list:hasContents  inst:IfcCartesianPoint_212 ;
        list:hasNext      inst:IfcCartesianPoint_List_1893 .

inst:IfcCartesianPoint_List_1893
        list:hasContents  inst:IfcCartesianPoint_291 .

inst:IfcFaceOuterBound_576
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_575 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_577  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_576 .

inst:IfcPolyLoop_578  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_1894
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_578  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_1894 .

inst:IfcCartesianPoint_List_1895
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1896
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1894
        list:hasContents  inst:IfcCartesianPoint_212 ;
        list:hasNext      inst:IfcCartesianPoint_List_1895 .

inst:IfcCartesianPoint_List_1895
        list:hasContents  inst:IfcCartesianPoint_211 ;
        list:hasNext      inst:IfcCartesianPoint_List_1896 .

inst:IfcCartesianPoint_List_1896
        list:hasContents  inst:IfcCartesianPoint_290 .

inst:IfcFaceOuterBound_579
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_578 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_580  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_579 .

inst:IfcPolyLoop_581  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_1897
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_581  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_1897 .

inst:IfcCartesianPoint_List_1898
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1899
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1897
        list:hasContents  inst:IfcCartesianPoint_211 ;
        list:hasNext      inst:IfcCartesianPoint_List_1898 .

inst:IfcCartesianPoint_List_1898
        list:hasContents  inst:IfcCartesianPoint_210 ;
        list:hasNext      inst:IfcCartesianPoint_List_1899 .

inst:IfcCartesianPoint_List_1899
        list:hasContents  inst:IfcCartesianPoint_289 .

inst:IfcFaceOuterBound_582
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_581 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_583  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_582 .

inst:IfcPolyLoop_584  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_1900
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_584  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_1900 .

inst:IfcCartesianPoint_List_1901
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1902
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1900
        list:hasContents  inst:IfcCartesianPoint_210 ;
        list:hasNext      inst:IfcCartesianPoint_List_1901 .

inst:IfcCartesianPoint_List_1901
        list:hasContents  inst:IfcCartesianPoint_209 ;
        list:hasNext      inst:IfcCartesianPoint_List_1902 .

inst:IfcCartesianPoint_List_1902
        list:hasContents  inst:IfcCartesianPoint_288 .

inst:IfcFaceOuterBound_585
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_584 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_586  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_585 .

inst:IfcPolyLoop_587  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_1903
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_587  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_1903 .

inst:IfcCartesianPoint_List_1904
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1905
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1903
        list:hasContents  inst:IfcCartesianPoint_209 ;
        list:hasNext      inst:IfcCartesianPoint_List_1904 .

inst:IfcCartesianPoint_List_1904
        list:hasContents  inst:IfcCartesianPoint_208 ;
        list:hasNext      inst:IfcCartesianPoint_List_1905 .

inst:IfcCartesianPoint_List_1905
        list:hasContents  inst:IfcCartesianPoint_287 .

inst:IfcFaceOuterBound_588
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_587 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_589  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_588 .

inst:IfcPolyLoop_590  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_1906
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_590  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_1906 .

inst:IfcCartesianPoint_List_1907
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1908
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1906
        list:hasContents  inst:IfcCartesianPoint_208 ;
        list:hasNext      inst:IfcCartesianPoint_List_1907 .

inst:IfcCartesianPoint_List_1907
        list:hasContents  inst:IfcCartesianPoint_207 ;
        list:hasNext      inst:IfcCartesianPoint_List_1908 .

inst:IfcCartesianPoint_List_1908
        list:hasContents  inst:IfcCartesianPoint_286 .

inst:IfcFaceOuterBound_591
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_590 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_592  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_591 .

inst:IfcPolyLoop_593  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_1909
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_593  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_1909 .

inst:IfcCartesianPoint_List_1910
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1911
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1909
        list:hasContents  inst:IfcCartesianPoint_207 ;
        list:hasNext      inst:IfcCartesianPoint_List_1910 .

inst:IfcCartesianPoint_List_1910
        list:hasContents  inst:IfcCartesianPoint_206 ;
        list:hasNext      inst:IfcCartesianPoint_List_1911 .

inst:IfcCartesianPoint_List_1911
        list:hasContents  inst:IfcCartesianPoint_285 .

inst:IfcFaceOuterBound_594
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_593 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_595  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_594 .

inst:IfcPolyLoop_596  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_1912
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_596  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_1912 .

inst:IfcCartesianPoint_List_1913
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1914
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1912
        list:hasContents  inst:IfcCartesianPoint_206 ;
        list:hasNext      inst:IfcCartesianPoint_List_1913 .

inst:IfcCartesianPoint_List_1913
        list:hasContents  inst:IfcCartesianPoint_205 ;
        list:hasNext      inst:IfcCartesianPoint_List_1914 .

inst:IfcCartesianPoint_List_1914
        list:hasContents  inst:IfcCartesianPoint_284 .

inst:IfcFaceOuterBound_597
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_596 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_598  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_597 .

inst:IfcPolyLoop_599  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_1915
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_599  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_1915 .

inst:IfcCartesianPoint_List_1916
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1917
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1915
        list:hasContents  inst:IfcCartesianPoint_205 ;
        list:hasNext      inst:IfcCartesianPoint_List_1916 .

inst:IfcCartesianPoint_List_1916
        list:hasContents  inst:IfcCartesianPoint_204 ;
        list:hasNext      inst:IfcCartesianPoint_List_1917 .

inst:IfcCartesianPoint_List_1917
        list:hasContents  inst:IfcCartesianPoint_283 .

inst:IfcFaceOuterBound_600
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_599 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_601  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_600 .

inst:IfcPolyLoop_602  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_1918
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_602  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_1918 .

inst:IfcCartesianPoint_List_1919
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1920
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1918
        list:hasContents  inst:IfcCartesianPoint_204 ;
        list:hasNext      inst:IfcCartesianPoint_List_1919 .

inst:IfcCartesianPoint_List_1919
        list:hasContents  inst:IfcCartesianPoint_203 ;
        list:hasNext      inst:IfcCartesianPoint_List_1920 .

inst:IfcCartesianPoint_List_1920
        list:hasContents  inst:IfcCartesianPoint_282 .

inst:IfcFaceOuterBound_603
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_602 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_604  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_603 .

inst:IfcPolyLoop_605  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_1921
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_605  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_1921 .

inst:IfcCartesianPoint_List_1922
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1923
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1921
        list:hasContents  inst:IfcCartesianPoint_203 ;
        list:hasNext      inst:IfcCartesianPoint_List_1922 .

inst:IfcCartesianPoint_List_1922
        list:hasContents  inst:IfcCartesianPoint_202 ;
        list:hasNext      inst:IfcCartesianPoint_List_1923 .

inst:IfcCartesianPoint_List_1923
        list:hasContents  inst:IfcCartesianPoint_281 .

inst:IfcFaceOuterBound_606
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_605 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_607  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_606 .

inst:IfcPolyLoop_608  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_1924
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_608  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_1924 .

inst:IfcCartesianPoint_List_1925
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1926
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1924
        list:hasContents  inst:IfcCartesianPoint_202 ;
        list:hasNext      inst:IfcCartesianPoint_List_1925 .

inst:IfcCartesianPoint_List_1925
        list:hasContents  inst:IfcCartesianPoint_200 ;
        list:hasNext      inst:IfcCartesianPoint_List_1926 .

inst:IfcCartesianPoint_List_1926
        list:hasContents  inst:IfcCartesianPoint_280 .

inst:IfcFaceOuterBound_609
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_608 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_610  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_609 .

inst:IfcPolyLoop_611  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_1927
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_611  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_1927 .

inst:IfcCartesianPoint_List_1928
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1929
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1927
        list:hasContents  inst:IfcCartesianPoint_201 ;
        list:hasNext      inst:IfcCartesianPoint_List_1928 .

inst:IfcCartesianPoint_List_1928
        list:hasContents  inst:IfcCartesianPoint_359 ;
        list:hasNext      inst:IfcCartesianPoint_List_1929 .

inst:IfcCartesianPoint_List_1929
        list:hasContents  inst:IfcCartesianPoint_320 .

inst:IfcFaceOuterBound_612
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_611 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_613  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_612 .

inst:IfcPolyLoop_614  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_1930
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_614  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_1930 .

inst:IfcCartesianPoint_List_1931
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1932
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1930
        list:hasContents  inst:IfcCartesianPoint_241 ;
        list:hasNext      inst:IfcCartesianPoint_List_1931 .

inst:IfcCartesianPoint_List_1931
        list:hasContents  inst:IfcCartesianPoint_358 ;
        list:hasNext      inst:IfcCartesianPoint_List_1932 .

inst:IfcCartesianPoint_List_1932
        list:hasContents  inst:IfcCartesianPoint_359 .

inst:IfcFaceOuterBound_615
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_614 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_616  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_615 .

inst:IfcPolyLoop_617  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_1933
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_617  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_1933 .

inst:IfcCartesianPoint_List_1934
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1935
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1933
        list:hasContents  inst:IfcCartesianPoint_242 ;
        list:hasNext      inst:IfcCartesianPoint_List_1934 .

inst:IfcCartesianPoint_List_1934
        list:hasContents  inst:IfcCartesianPoint_357 ;
        list:hasNext      inst:IfcCartesianPoint_List_1935 .

inst:IfcCartesianPoint_List_1935
        list:hasContents  inst:IfcCartesianPoint_358 .

inst:IfcFaceOuterBound_618
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_617 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_619  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_618 .

inst:IfcPolyLoop_620  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_1936
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_620  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_1936 .

inst:IfcCartesianPoint_List_1937
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1938
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1936
        list:hasContents  inst:IfcCartesianPoint_243 ;
        list:hasNext      inst:IfcCartesianPoint_List_1937 .

inst:IfcCartesianPoint_List_1937
        list:hasContents  inst:IfcCartesianPoint_356 ;
        list:hasNext      inst:IfcCartesianPoint_List_1938 .

inst:IfcCartesianPoint_List_1938
        list:hasContents  inst:IfcCartesianPoint_357 .

inst:IfcFaceOuterBound_621
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_620 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_622  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_621 .

inst:IfcPolyLoop_623  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_1939
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_623  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_1939 .

inst:IfcCartesianPoint_List_1940
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1941
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1939
        list:hasContents  inst:IfcCartesianPoint_244 ;
        list:hasNext      inst:IfcCartesianPoint_List_1940 .

inst:IfcCartesianPoint_List_1940
        list:hasContents  inst:IfcCartesianPoint_355 ;
        list:hasNext      inst:IfcCartesianPoint_List_1941 .

inst:IfcCartesianPoint_List_1941
        list:hasContents  inst:IfcCartesianPoint_356 .

inst:IfcFaceOuterBound_624
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_623 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_625  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_624 .

inst:IfcPolyLoop_626  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_1942
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_626  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_1942 .

inst:IfcCartesianPoint_List_1943
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1944
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1942
        list:hasContents  inst:IfcCartesianPoint_245 ;
        list:hasNext      inst:IfcCartesianPoint_List_1943 .

inst:IfcCartesianPoint_List_1943
        list:hasContents  inst:IfcCartesianPoint_354 ;
        list:hasNext      inst:IfcCartesianPoint_List_1944 .

inst:IfcCartesianPoint_List_1944
        list:hasContents  inst:IfcCartesianPoint_355 .

inst:IfcFaceOuterBound_627
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_626 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_628  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_627 .

inst:IfcPolyLoop_629  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_1945
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_629  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_1945 .

inst:IfcCartesianPoint_List_1946
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1947
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1945
        list:hasContents  inst:IfcCartesianPoint_246 ;
        list:hasNext      inst:IfcCartesianPoint_List_1946 .

inst:IfcCartesianPoint_List_1946
        list:hasContents  inst:IfcCartesianPoint_353 ;
        list:hasNext      inst:IfcCartesianPoint_List_1947 .

inst:IfcCartesianPoint_List_1947
        list:hasContents  inst:IfcCartesianPoint_354 .

inst:IfcFaceOuterBound_630
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_629 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_631  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_630 .

inst:IfcPolyLoop_632  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_1948
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_632  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_1948 .

inst:IfcCartesianPoint_List_1949
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1950
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1948
        list:hasContents  inst:IfcCartesianPoint_247 ;
        list:hasNext      inst:IfcCartesianPoint_List_1949 .

inst:IfcCartesianPoint_List_1949
        list:hasContents  inst:IfcCartesianPoint_352 ;
        list:hasNext      inst:IfcCartesianPoint_List_1950 .

inst:IfcCartesianPoint_List_1950
        list:hasContents  inst:IfcCartesianPoint_353 .

inst:IfcFaceOuterBound_633
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_632 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_634  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_633 .

inst:IfcPolyLoop_635  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_1951
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_635  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_1951 .

inst:IfcCartesianPoint_List_1952
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1953
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1951
        list:hasContents  inst:IfcCartesianPoint_248 ;
        list:hasNext      inst:IfcCartesianPoint_List_1952 .

inst:IfcCartesianPoint_List_1952
        list:hasContents  inst:IfcCartesianPoint_351 ;
        list:hasNext      inst:IfcCartesianPoint_List_1953 .

inst:IfcCartesianPoint_List_1953
        list:hasContents  inst:IfcCartesianPoint_352 .

inst:IfcFaceOuterBound_636
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_635 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_637  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_636 .

inst:IfcPolyLoop_638  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_1954
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_638  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_1954 .

inst:IfcCartesianPoint_List_1955
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1956
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1954
        list:hasContents  inst:IfcCartesianPoint_249 ;
        list:hasNext      inst:IfcCartesianPoint_List_1955 .

inst:IfcCartesianPoint_List_1955
        list:hasContents  inst:IfcCartesianPoint_350 ;
        list:hasNext      inst:IfcCartesianPoint_List_1956 .

inst:IfcCartesianPoint_List_1956
        list:hasContents  inst:IfcCartesianPoint_351 .

inst:IfcFaceOuterBound_639
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_638 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_640  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_639 .

inst:IfcPolyLoop_641  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_1957
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_641  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_1957 .

inst:IfcCartesianPoint_List_1958
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1959
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1957
        list:hasContents  inst:IfcCartesianPoint_250 ;
        list:hasNext      inst:IfcCartesianPoint_List_1958 .

inst:IfcCartesianPoint_List_1958
        list:hasContents  inst:IfcCartesianPoint_349 ;
        list:hasNext      inst:IfcCartesianPoint_List_1959 .

inst:IfcCartesianPoint_List_1959
        list:hasContents  inst:IfcCartesianPoint_350 .

inst:IfcFaceOuterBound_642
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_641 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_643  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_642 .

inst:IfcPolyLoop_644  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_1960
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_644  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_1960 .

inst:IfcCartesianPoint_List_1961
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1962
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1960
        list:hasContents  inst:IfcCartesianPoint_251 ;
        list:hasNext      inst:IfcCartesianPoint_List_1961 .

inst:IfcCartesianPoint_List_1961
        list:hasContents  inst:IfcCartesianPoint_348 ;
        list:hasNext      inst:IfcCartesianPoint_List_1962 .

inst:IfcCartesianPoint_List_1962
        list:hasContents  inst:IfcCartesianPoint_349 .

inst:IfcFaceOuterBound_645
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_644 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_646  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_645 .

inst:IfcPolyLoop_647  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_1963
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_647  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_1963 .

inst:IfcCartesianPoint_List_1964
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1965
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1963
        list:hasContents  inst:IfcCartesianPoint_252 ;
        list:hasNext      inst:IfcCartesianPoint_List_1964 .

inst:IfcCartesianPoint_List_1964
        list:hasContents  inst:IfcCartesianPoint_347 ;
        list:hasNext      inst:IfcCartesianPoint_List_1965 .

inst:IfcCartesianPoint_List_1965
        list:hasContents  inst:IfcCartesianPoint_348 .

inst:IfcFaceOuterBound_648
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_647 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_649  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_648 .

inst:IfcPolyLoop_650  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_1966
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_650  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_1966 .

inst:IfcCartesianPoint_List_1967
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1968
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1966
        list:hasContents  inst:IfcCartesianPoint_253 ;
        list:hasNext      inst:IfcCartesianPoint_List_1967 .

inst:IfcCartesianPoint_List_1967
        list:hasContents  inst:IfcCartesianPoint_346 ;
        list:hasNext      inst:IfcCartesianPoint_List_1968 .

inst:IfcCartesianPoint_List_1968
        list:hasContents  inst:IfcCartesianPoint_347 .

inst:IfcFaceOuterBound_651
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_650 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_652  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_651 .

inst:IfcPolyLoop_653  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_1969
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_653  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_1969 .

inst:IfcCartesianPoint_List_1970
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1971
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1969
        list:hasContents  inst:IfcCartesianPoint_254 ;
        list:hasNext      inst:IfcCartesianPoint_List_1970 .

inst:IfcCartesianPoint_List_1970
        list:hasContents  inst:IfcCartesianPoint_345 ;
        list:hasNext      inst:IfcCartesianPoint_List_1971 .

inst:IfcCartesianPoint_List_1971
        list:hasContents  inst:IfcCartesianPoint_346 .

inst:IfcFaceOuterBound_654
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_653 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_655  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_654 .

inst:IfcPolyLoop_656  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_1972
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_656  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_1972 .

inst:IfcCartesianPoint_List_1973
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1974
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1972
        list:hasContents  inst:IfcCartesianPoint_255 ;
        list:hasNext      inst:IfcCartesianPoint_List_1973 .

inst:IfcCartesianPoint_List_1973
        list:hasContents  inst:IfcCartesianPoint_344 ;
        list:hasNext      inst:IfcCartesianPoint_List_1974 .

inst:IfcCartesianPoint_List_1974
        list:hasContents  inst:IfcCartesianPoint_345 .

inst:IfcFaceOuterBound_657
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_656 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_658  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_657 .

inst:IfcPolyLoop_659  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_1975
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_659  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_1975 .

inst:IfcCartesianPoint_List_1976
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1977
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1975
        list:hasContents  inst:IfcCartesianPoint_256 ;
        list:hasNext      inst:IfcCartesianPoint_List_1976 .

inst:IfcCartesianPoint_List_1976
        list:hasContents  inst:IfcCartesianPoint_343 ;
        list:hasNext      inst:IfcCartesianPoint_List_1977 .

inst:IfcCartesianPoint_List_1977
        list:hasContents  inst:IfcCartesianPoint_344 .

inst:IfcFaceOuterBound_660
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_659 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_661  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_660 .

inst:IfcPolyLoop_662  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_1978
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_662  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_1978 .

inst:IfcCartesianPoint_List_1979
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1980
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1978
        list:hasContents  inst:IfcCartesianPoint_257 ;
        list:hasNext      inst:IfcCartesianPoint_List_1979 .

inst:IfcCartesianPoint_List_1979
        list:hasContents  inst:IfcCartesianPoint_342 ;
        list:hasNext      inst:IfcCartesianPoint_List_1980 .

inst:IfcCartesianPoint_List_1980
        list:hasContents  inst:IfcCartesianPoint_343 .

inst:IfcFaceOuterBound_663
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_662 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_664  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_663 .

inst:IfcPolyLoop_665  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_1981
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_665  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_1981 .

inst:IfcCartesianPoint_List_1982
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1983
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1981
        list:hasContents  inst:IfcCartesianPoint_258 ;
        list:hasNext      inst:IfcCartesianPoint_List_1982 .

inst:IfcCartesianPoint_List_1982
        list:hasContents  inst:IfcCartesianPoint_341 ;
        list:hasNext      inst:IfcCartesianPoint_List_1983 .

inst:IfcCartesianPoint_List_1983
        list:hasContents  inst:IfcCartesianPoint_342 .

inst:IfcFaceOuterBound_666
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_665 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_667  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_666 .

inst:IfcPolyLoop_668  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_1984
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_668  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_1984 .

inst:IfcCartesianPoint_List_1985
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1986
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1984
        list:hasContents  inst:IfcCartesianPoint_259 ;
        list:hasNext      inst:IfcCartesianPoint_List_1985 .

inst:IfcCartesianPoint_List_1985
        list:hasContents  inst:IfcCartesianPoint_340 ;
        list:hasNext      inst:IfcCartesianPoint_List_1986 .

inst:IfcCartesianPoint_List_1986
        list:hasContents  inst:IfcCartesianPoint_341 .

inst:IfcFaceOuterBound_669
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_668 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_670  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_669 .

inst:IfcPolyLoop_671  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_1987
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_671  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_1987 .

inst:IfcCartesianPoint_List_1988
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1989
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1987
        list:hasContents  inst:IfcCartesianPoint_260 ;
        list:hasNext      inst:IfcCartesianPoint_List_1988 .

inst:IfcCartesianPoint_List_1988
        list:hasContents  inst:IfcCartesianPoint_339 ;
        list:hasNext      inst:IfcCartesianPoint_List_1989 .

inst:IfcCartesianPoint_List_1989
        list:hasContents  inst:IfcCartesianPoint_340 .

inst:IfcFaceOuterBound_672
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_671 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_673  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_672 .

inst:IfcPolyLoop_674  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_1990
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_674  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_1990 .

inst:IfcCartesianPoint_List_1991
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1992
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1990
        list:hasContents  inst:IfcCartesianPoint_261 ;
        list:hasNext      inst:IfcCartesianPoint_List_1991 .

inst:IfcCartesianPoint_List_1991
        list:hasContents  inst:IfcCartesianPoint_338 ;
        list:hasNext      inst:IfcCartesianPoint_List_1992 .

inst:IfcCartesianPoint_List_1992
        list:hasContents  inst:IfcCartesianPoint_339 .

inst:IfcFaceOuterBound_675
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_674 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_676  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_675 .

inst:IfcPolyLoop_677  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_1993
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_677  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_1993 .

inst:IfcCartesianPoint_List_1994
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1995
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1993
        list:hasContents  inst:IfcCartesianPoint_262 ;
        list:hasNext      inst:IfcCartesianPoint_List_1994 .

inst:IfcCartesianPoint_List_1994
        list:hasContents  inst:IfcCartesianPoint_337 ;
        list:hasNext      inst:IfcCartesianPoint_List_1995 .

inst:IfcCartesianPoint_List_1995
        list:hasContents  inst:IfcCartesianPoint_338 .

inst:IfcFaceOuterBound_678
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_677 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_679  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_678 .

inst:IfcPolyLoop_680  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_1996
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_680  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_1996 .

inst:IfcCartesianPoint_List_1997
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1998
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1996
        list:hasContents  inst:IfcCartesianPoint_263 ;
        list:hasNext      inst:IfcCartesianPoint_List_1997 .

inst:IfcCartesianPoint_List_1997
        list:hasContents  inst:IfcCartesianPoint_336 ;
        list:hasNext      inst:IfcCartesianPoint_List_1998 .

inst:IfcCartesianPoint_List_1998
        list:hasContents  inst:IfcCartesianPoint_337 .

inst:IfcFaceOuterBound_681
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_680 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_682  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_681 .

inst:IfcPolyLoop_683  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_1999
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_683  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_1999 .

inst:IfcCartesianPoint_List_2000
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_2001
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_1999
        list:hasContents  inst:IfcCartesianPoint_264 ;
        list:hasNext      inst:IfcCartesianPoint_List_2000 .

inst:IfcCartesianPoint_List_2000
        list:hasContents  inst:IfcCartesianPoint_335 ;
        list:hasNext      inst:IfcCartesianPoint_List_2001 .

inst:IfcCartesianPoint_List_2001
        list:hasContents  inst:IfcCartesianPoint_336 .

inst:IfcFaceOuterBound_684
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_683 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_685  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_684 .

inst:IfcPolyLoop_686  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_2002
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_686  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_2002 .

inst:IfcCartesianPoint_List_2003
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_2004
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_2002
        list:hasContents  inst:IfcCartesianPoint_265 ;
        list:hasNext      inst:IfcCartesianPoint_List_2003 .

inst:IfcCartesianPoint_List_2003
        list:hasContents  inst:IfcCartesianPoint_334 ;
        list:hasNext      inst:IfcCartesianPoint_List_2004 .

inst:IfcCartesianPoint_List_2004
        list:hasContents  inst:IfcCartesianPoint_335 .

inst:IfcFaceOuterBound_687
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_686 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_688  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_687 .

inst:IfcPolyLoop_689  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_2005
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_689  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_2005 .

inst:IfcCartesianPoint_List_2006
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_2007
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_2005
        list:hasContents  inst:IfcCartesianPoint_266 ;
        list:hasNext      inst:IfcCartesianPoint_List_2006 .

inst:IfcCartesianPoint_List_2006
        list:hasContents  inst:IfcCartesianPoint_333 ;
        list:hasNext      inst:IfcCartesianPoint_List_2007 .

inst:IfcCartesianPoint_List_2007
        list:hasContents  inst:IfcCartesianPoint_334 .

inst:IfcFaceOuterBound_690
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_689 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_691  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_690 .

inst:IfcPolyLoop_692  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_2008
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_692  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_2008 .

inst:IfcCartesianPoint_List_2009
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_2010
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_2008
        list:hasContents  inst:IfcCartesianPoint_267 ;
        list:hasNext      inst:IfcCartesianPoint_List_2009 .

inst:IfcCartesianPoint_List_2009
        list:hasContents  inst:IfcCartesianPoint_332 ;
        list:hasNext      inst:IfcCartesianPoint_List_2010 .

inst:IfcCartesianPoint_List_2010
        list:hasContents  inst:IfcCartesianPoint_333 .

inst:IfcFaceOuterBound_693
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_692 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_694  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_693 .

inst:IfcPolyLoop_695  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_2011
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_695  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_2011 .

inst:IfcCartesianPoint_List_2012
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_2013
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_2011
        list:hasContents  inst:IfcCartesianPoint_268 ;
        list:hasNext      inst:IfcCartesianPoint_List_2012 .

inst:IfcCartesianPoint_List_2012
        list:hasContents  inst:IfcCartesianPoint_331 ;
        list:hasNext      inst:IfcCartesianPoint_List_2013 .

inst:IfcCartesianPoint_List_2013
        list:hasContents  inst:IfcCartesianPoint_332 .

inst:IfcFaceOuterBound_696
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_695 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_697  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_696 .

inst:IfcPolyLoop_698  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_2014
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_698  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_2014 .

inst:IfcCartesianPoint_List_2015
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_2016
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_2014
        list:hasContents  inst:IfcCartesianPoint_269 ;
        list:hasNext      inst:IfcCartesianPoint_List_2015 .

inst:IfcCartesianPoint_List_2015
        list:hasContents  inst:IfcCartesianPoint_330 ;
        list:hasNext      inst:IfcCartesianPoint_List_2016 .

inst:IfcCartesianPoint_List_2016
        list:hasContents  inst:IfcCartesianPoint_331 .

inst:IfcFaceOuterBound_699
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_698 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_700  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_699 .

inst:IfcPolyLoop_701  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_2017
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_701  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_2017 .

inst:IfcCartesianPoint_List_2018
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_2019
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_2017
        list:hasContents  inst:IfcCartesianPoint_270 ;
        list:hasNext      inst:IfcCartesianPoint_List_2018 .

inst:IfcCartesianPoint_List_2018
        list:hasContents  inst:IfcCartesianPoint_329 ;
        list:hasNext      inst:IfcCartesianPoint_List_2019 .

inst:IfcCartesianPoint_List_2019
        list:hasContents  inst:IfcCartesianPoint_330 .

inst:IfcFaceOuterBound_702
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_701 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_703  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_702 .

inst:IfcPolyLoop_704  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_2020
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_704  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_2020 .

inst:IfcCartesianPoint_List_2021
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_2022
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_2020
        list:hasContents  inst:IfcCartesianPoint_271 ;
        list:hasNext      inst:IfcCartesianPoint_List_2021 .

inst:IfcCartesianPoint_List_2021
        list:hasContents  inst:IfcCartesianPoint_328 ;
        list:hasNext      inst:IfcCartesianPoint_List_2022 .

inst:IfcCartesianPoint_List_2022
        list:hasContents  inst:IfcCartesianPoint_329 .

inst:IfcFaceOuterBound_705
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_704 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_706  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_705 .

inst:IfcPolyLoop_707  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_2023
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_707  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_2023 .

inst:IfcCartesianPoint_List_2024
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_2025
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_2023
        list:hasContents  inst:IfcCartesianPoint_272 ;
        list:hasNext      inst:IfcCartesianPoint_List_2024 .

inst:IfcCartesianPoint_List_2024
        list:hasContents  inst:IfcCartesianPoint_327 ;
        list:hasNext      inst:IfcCartesianPoint_List_2025 .

inst:IfcCartesianPoint_List_2025
        list:hasContents  inst:IfcCartesianPoint_328 .

inst:IfcFaceOuterBound_708
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_707 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_709  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_708 .

inst:IfcPolyLoop_710  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_2026
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_710  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_2026 .

inst:IfcCartesianPoint_List_2027
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_2028
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_2026
        list:hasContents  inst:IfcCartesianPoint_273 ;
        list:hasNext      inst:IfcCartesianPoint_List_2027 .

inst:IfcCartesianPoint_List_2027
        list:hasContents  inst:IfcCartesianPoint_326 ;
        list:hasNext      inst:IfcCartesianPoint_List_2028 .

inst:IfcCartesianPoint_List_2028
        list:hasContents  inst:IfcCartesianPoint_327 .

inst:IfcFaceOuterBound_711
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_710 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_712  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_711 .

inst:IfcPolyLoop_713  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_2029
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_713  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_2029 .

inst:IfcCartesianPoint_List_2030
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_2031
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_2029
        list:hasContents  inst:IfcCartesianPoint_274 ;
        list:hasNext      inst:IfcCartesianPoint_List_2030 .

inst:IfcCartesianPoint_List_2030
        list:hasContents  inst:IfcCartesianPoint_325 ;
        list:hasNext      inst:IfcCartesianPoint_List_2031 .

inst:IfcCartesianPoint_List_2031
        list:hasContents  inst:IfcCartesianPoint_326 .

inst:IfcFaceOuterBound_714
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_713 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_715  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_714 .

inst:IfcPolyLoop_716  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_2032
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_716  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_2032 .

inst:IfcCartesianPoint_List_2033
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_2034
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_2032
        list:hasContents  inst:IfcCartesianPoint_275 ;
        list:hasNext      inst:IfcCartesianPoint_List_2033 .

inst:IfcCartesianPoint_List_2033
        list:hasContents  inst:IfcCartesianPoint_324 ;
        list:hasNext      inst:IfcCartesianPoint_List_2034 .

inst:IfcCartesianPoint_List_2034
        list:hasContents  inst:IfcCartesianPoint_325 .

inst:IfcFaceOuterBound_717
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_716 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_718  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_717 .

inst:IfcPolyLoop_719  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_2035
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_719  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_2035 .

inst:IfcCartesianPoint_List_2036
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_2037
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_2035
        list:hasContents  inst:IfcCartesianPoint_276 ;
        list:hasNext      inst:IfcCartesianPoint_List_2036 .

inst:IfcCartesianPoint_List_2036
        list:hasContents  inst:IfcCartesianPoint_323 ;
        list:hasNext      inst:IfcCartesianPoint_List_2037 .

inst:IfcCartesianPoint_List_2037
        list:hasContents  inst:IfcCartesianPoint_324 .

inst:IfcFaceOuterBound_720
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_719 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_721  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_720 .

inst:IfcPolyLoop_722  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_2038
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_722  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_2038 .

inst:IfcCartesianPoint_List_2039
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_2040
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_2038
        list:hasContents  inst:IfcCartesianPoint_277 ;
        list:hasNext      inst:IfcCartesianPoint_List_2039 .

inst:IfcCartesianPoint_List_2039
        list:hasContents  inst:IfcCartesianPoint_322 ;
        list:hasNext      inst:IfcCartesianPoint_List_2040 .

inst:IfcCartesianPoint_List_2040
        list:hasContents  inst:IfcCartesianPoint_323 .

inst:IfcFaceOuterBound_723
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_722 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_724  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_723 .

inst:IfcPolyLoop_725  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_2041
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_725  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_2041 .

inst:IfcCartesianPoint_List_2042
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_2043
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_2041
        list:hasContents  inst:IfcCartesianPoint_278 ;
        list:hasNext      inst:IfcCartesianPoint_List_2042 .

inst:IfcCartesianPoint_List_2042
        list:hasContents  inst:IfcCartesianPoint_321 ;
        list:hasNext      inst:IfcCartesianPoint_List_2043 .

inst:IfcCartesianPoint_List_2043
        list:hasContents  inst:IfcCartesianPoint_322 .

inst:IfcFaceOuterBound_726
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_725 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_727  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_726 .

inst:IfcPolyLoop_728  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_2044
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_728  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_2044 .

inst:IfcCartesianPoint_List_2045
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_2046
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_2044
        list:hasContents  inst:IfcCartesianPoint_279 ;
        list:hasNext      inst:IfcCartesianPoint_List_2045 .

inst:IfcCartesianPoint_List_2045
        list:hasContents  inst:IfcCartesianPoint_320 ;
        list:hasNext      inst:IfcCartesianPoint_List_2046 .

inst:IfcCartesianPoint_List_2046
        list:hasContents  inst:IfcCartesianPoint_321 .

inst:IfcFaceOuterBound_729
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_728 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_730  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_729 .

inst:IfcPolyLoop_731  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_2047
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_731  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_2047 .

inst:IfcCartesianPoint_List_2048
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_2049
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_2047
        list:hasContents  inst:IfcCartesianPoint_201 ;
        list:hasNext      inst:IfcCartesianPoint_List_2048 .

inst:IfcCartesianPoint_List_2048
        list:hasContents  inst:IfcCartesianPoint_241 ;
        list:hasNext      inst:IfcCartesianPoint_List_2049 .

inst:IfcCartesianPoint_List_2049
        list:hasContents  inst:IfcCartesianPoint_359 .

inst:IfcFaceOuterBound_732
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_731 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_733  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_732 .

inst:IfcPolyLoop_734  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_2050
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_734  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_2050 .

inst:IfcCartesianPoint_List_2051
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_2052
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_2050
        list:hasContents  inst:IfcCartesianPoint_241 ;
        list:hasNext      inst:IfcCartesianPoint_List_2051 .

inst:IfcCartesianPoint_List_2051
        list:hasContents  inst:IfcCartesianPoint_242 ;
        list:hasNext      inst:IfcCartesianPoint_List_2052 .

inst:IfcCartesianPoint_List_2052
        list:hasContents  inst:IfcCartesianPoint_358 .

inst:IfcFaceOuterBound_735
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_734 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_736  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_735 .

inst:IfcPolyLoop_737  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_2053
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_737  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_2053 .

inst:IfcCartesianPoint_List_2054
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_2055
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_2053
        list:hasContents  inst:IfcCartesianPoint_242 ;
        list:hasNext      inst:IfcCartesianPoint_List_2054 .

inst:IfcCartesianPoint_List_2054
        list:hasContents  inst:IfcCartesianPoint_243 ;
        list:hasNext      inst:IfcCartesianPoint_List_2055 .

inst:IfcCartesianPoint_List_2055
        list:hasContents  inst:IfcCartesianPoint_357 .

inst:IfcFaceOuterBound_738
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_737 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_739  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_738 .

inst:IfcPolyLoop_740  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_2056
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_740  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_2056 .

inst:IfcCartesianPoint_List_2057
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_2058
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_2056
        list:hasContents  inst:IfcCartesianPoint_243 ;
        list:hasNext      inst:IfcCartesianPoint_List_2057 .

inst:IfcCartesianPoint_List_2057
        list:hasContents  inst:IfcCartesianPoint_244 ;
        list:hasNext      inst:IfcCartesianPoint_List_2058 .

inst:IfcCartesianPoint_List_2058
        list:hasContents  inst:IfcCartesianPoint_356 .

inst:IfcFaceOuterBound_741
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_740 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_742  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_741 .

inst:IfcPolyLoop_743  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_2059
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_743  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_2059 .

inst:IfcCartesianPoint_List_2060
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_2061
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_2059
        list:hasContents  inst:IfcCartesianPoint_244 ;
        list:hasNext      inst:IfcCartesianPoint_List_2060 .

inst:IfcCartesianPoint_List_2060
        list:hasContents  inst:IfcCartesianPoint_245 ;
        list:hasNext      inst:IfcCartesianPoint_List_2061 .

inst:IfcCartesianPoint_List_2061
        list:hasContents  inst:IfcCartesianPoint_355 .

inst:IfcFaceOuterBound_744
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_743 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_745  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_744 .

inst:IfcPolyLoop_746  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_2062
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_746  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_2062 .

inst:IfcCartesianPoint_List_2063
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_2064
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_2062
        list:hasContents  inst:IfcCartesianPoint_245 ;
        list:hasNext      inst:IfcCartesianPoint_List_2063 .

inst:IfcCartesianPoint_List_2063
        list:hasContents  inst:IfcCartesianPoint_246 ;
        list:hasNext      inst:IfcCartesianPoint_List_2064 .

inst:IfcCartesianPoint_List_2064
        list:hasContents  inst:IfcCartesianPoint_354 .

inst:IfcFaceOuterBound_747
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_746 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_748  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_747 .

inst:IfcPolyLoop_749  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_2065
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_749  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_2065 .

inst:IfcCartesianPoint_List_2066
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_2067
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_2065
        list:hasContents  inst:IfcCartesianPoint_246 ;
        list:hasNext      inst:IfcCartesianPoint_List_2066 .

inst:IfcCartesianPoint_List_2066
        list:hasContents  inst:IfcCartesianPoint_247 ;
        list:hasNext      inst:IfcCartesianPoint_List_2067 .

inst:IfcCartesianPoint_List_2067
        list:hasContents  inst:IfcCartesianPoint_353 .

inst:IfcFaceOuterBound_750
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_749 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_751  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_750 .

inst:IfcPolyLoop_752  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_2068
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_752  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_2068 .

inst:IfcCartesianPoint_List_2069
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_2070
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_2068
        list:hasContents  inst:IfcCartesianPoint_247 ;
        list:hasNext      inst:IfcCartesianPoint_List_2069 .

inst:IfcCartesianPoint_List_2069
        list:hasContents  inst:IfcCartesianPoint_248 ;
        list:hasNext      inst:IfcCartesianPoint_List_2070 .

inst:IfcCartesianPoint_List_2070
        list:hasContents  inst:IfcCartesianPoint_352 .

inst:IfcFaceOuterBound_753
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_752 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_754  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_753 .

inst:IfcPolyLoop_755  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_2071
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_755  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_2071 .

inst:IfcCartesianPoint_List_2072
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_2073
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_2071
        list:hasContents  inst:IfcCartesianPoint_248 ;
        list:hasNext      inst:IfcCartesianPoint_List_2072 .

inst:IfcCartesianPoint_List_2072
        list:hasContents  inst:IfcCartesianPoint_249 ;
        list:hasNext      inst:IfcCartesianPoint_List_2073 .

inst:IfcCartesianPoint_List_2073
        list:hasContents  inst:IfcCartesianPoint_351 .

inst:IfcFaceOuterBound_756
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_755 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_757  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_756 .

inst:IfcPolyLoop_758  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_2074
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_758  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_2074 .

inst:IfcCartesianPoint_List_2075
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_2076
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_2074
        list:hasContents  inst:IfcCartesianPoint_249 ;
        list:hasNext      inst:IfcCartesianPoint_List_2075 .

inst:IfcCartesianPoint_List_2075
        list:hasContents  inst:IfcCartesianPoint_250 ;
        list:hasNext      inst:IfcCartesianPoint_List_2076 .

inst:IfcCartesianPoint_List_2076
        list:hasContents  inst:IfcCartesianPoint_350 .

inst:IfcFaceOuterBound_759
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_758 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_760  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_759 .

inst:IfcPolyLoop_761  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_2077
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_761  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_2077 .

inst:IfcCartesianPoint_List_2078
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_2079
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_2077
        list:hasContents  inst:IfcCartesianPoint_250 ;
        list:hasNext      inst:IfcCartesianPoint_List_2078 .

inst:IfcCartesianPoint_List_2078
        list:hasContents  inst:IfcCartesianPoint_251 ;
        list:hasNext      inst:IfcCartesianPoint_List_2079 .

inst:IfcCartesianPoint_List_2079
        list:hasContents  inst:IfcCartesianPoint_349 .

inst:IfcFaceOuterBound_762
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_761 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_763  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_762 .

inst:IfcPolyLoop_764  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_2080
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_764  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_2080 .

inst:IfcCartesianPoint_List_2081
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_2082
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_2080
        list:hasContents  inst:IfcCartesianPoint_251 ;
        list:hasNext      inst:IfcCartesianPoint_List_2081 .

inst:IfcCartesianPoint_List_2081
        list:hasContents  inst:IfcCartesianPoint_252 ;
        list:hasNext      inst:IfcCartesianPoint_List_2082 .

inst:IfcCartesianPoint_List_2082
        list:hasContents  inst:IfcCartesianPoint_348 .

inst:IfcFaceOuterBound_765
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_764 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_766  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_765 .

inst:IfcPolyLoop_767  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_2083
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_767  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_2083 .

inst:IfcCartesianPoint_List_2084
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_2085
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_2083
        list:hasContents  inst:IfcCartesianPoint_252 ;
        list:hasNext      inst:IfcCartesianPoint_List_2084 .

inst:IfcCartesianPoint_List_2084
        list:hasContents  inst:IfcCartesianPoint_253 ;
        list:hasNext      inst:IfcCartesianPoint_List_2085 .

inst:IfcCartesianPoint_List_2085
        list:hasContents  inst:IfcCartesianPoint_347 .

inst:IfcFaceOuterBound_768
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_767 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_769  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_768 .

inst:IfcPolyLoop_770  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_2086
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_770  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_2086 .

inst:IfcCartesianPoint_List_2087
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_2088
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_2086
        list:hasContents  inst:IfcCartesianPoint_253 ;
        list:hasNext      inst:IfcCartesianPoint_List_2087 .

inst:IfcCartesianPoint_List_2087
        list:hasContents  inst:IfcCartesianPoint_254 ;
        list:hasNext      inst:IfcCartesianPoint_List_2088 .

inst:IfcCartesianPoint_List_2088
        list:hasContents  inst:IfcCartesianPoint_346 .

inst:IfcFaceOuterBound_771
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_770 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_772  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_771 .

inst:IfcPolyLoop_773  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_2089
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_773  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_2089 .

inst:IfcCartesianPoint_List_2090
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_2091
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_2089
        list:hasContents  inst:IfcCartesianPoint_254 ;
        list:hasNext      inst:IfcCartesianPoint_List_2090 .

inst:IfcCartesianPoint_List_2090
        list:hasContents  inst:IfcCartesianPoint_255 ;
        list:hasNext      inst:IfcCartesianPoint_List_2091 .

inst:IfcCartesianPoint_List_2091
        list:hasContents  inst:IfcCartesianPoint_345 .

inst:IfcFaceOuterBound_774
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_773 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_775  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_774 .

inst:IfcPolyLoop_776  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_2092
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_776  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_2092 .

inst:IfcCartesianPoint_List_2093
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_2094
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_2092
        list:hasContents  inst:IfcCartesianPoint_255 ;
        list:hasNext      inst:IfcCartesianPoint_List_2093 .

inst:IfcCartesianPoint_List_2093
        list:hasContents  inst:IfcCartesianPoint_256 ;
        list:hasNext      inst:IfcCartesianPoint_List_2094 .

inst:IfcCartesianPoint_List_2094
        list:hasContents  inst:IfcCartesianPoint_344 .

inst:IfcFaceOuterBound_777
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_776 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_778  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_777 .

inst:IfcPolyLoop_779  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_2095
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_779  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_2095 .

inst:IfcCartesianPoint_List_2096
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_2097
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_2095
        list:hasContents  inst:IfcCartesianPoint_256 ;
        list:hasNext      inst:IfcCartesianPoint_List_2096 .

inst:IfcCartesianPoint_List_2096
        list:hasContents  inst:IfcCartesianPoint_257 ;
        list:hasNext      inst:IfcCartesianPoint_List_2097 .

inst:IfcCartesianPoint_List_2097
        list:hasContents  inst:IfcCartesianPoint_343 .

inst:IfcFaceOuterBound_780
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_779 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_781  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_780 .

inst:IfcPolyLoop_782  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_2098
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_782  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_2098 .

inst:IfcCartesianPoint_List_2099
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_2100
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_2098
        list:hasContents  inst:IfcCartesianPoint_257 ;
        list:hasNext      inst:IfcCartesianPoint_List_2099 .

inst:IfcCartesianPoint_List_2099
        list:hasContents  inst:IfcCartesianPoint_258 ;
        list:hasNext      inst:IfcCartesianPoint_List_2100 .

inst:IfcCartesianPoint_List_2100
        list:hasContents  inst:IfcCartesianPoint_342 .

inst:IfcFaceOuterBound_783
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_782 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_784  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_783 .

inst:IfcPolyLoop_785  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_2101
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_785  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_2101 .

inst:IfcCartesianPoint_List_2102
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_2103
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_2101
        list:hasContents  inst:IfcCartesianPoint_258 ;
        list:hasNext      inst:IfcCartesianPoint_List_2102 .

inst:IfcCartesianPoint_List_2102
        list:hasContents  inst:IfcCartesianPoint_259 ;
        list:hasNext      inst:IfcCartesianPoint_List_2103 .

inst:IfcCartesianPoint_List_2103
        list:hasContents  inst:IfcCartesianPoint_341 .

inst:IfcFaceOuterBound_786
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_785 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_787  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_786 .

inst:IfcPolyLoop_788  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_2104
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_788  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_2104 .

inst:IfcCartesianPoint_List_2105
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_2106
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_2104
        list:hasContents  inst:IfcCartesianPoint_259 ;
        list:hasNext      inst:IfcCartesianPoint_List_2105 .

inst:IfcCartesianPoint_List_2105
        list:hasContents  inst:IfcCartesianPoint_260 ;
        list:hasNext      inst:IfcCartesianPoint_List_2106 .

inst:IfcCartesianPoint_List_2106
        list:hasContents  inst:IfcCartesianPoint_340 .

inst:IfcFaceOuterBound_789
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_788 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_790  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_789 .

inst:IfcPolyLoop_791  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_2107
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_791  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_2107 .

inst:IfcCartesianPoint_List_2108
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_2109
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_2107
        list:hasContents  inst:IfcCartesianPoint_260 ;
        list:hasNext      inst:IfcCartesianPoint_List_2108 .

inst:IfcCartesianPoint_List_2108
        list:hasContents  inst:IfcCartesianPoint_261 ;
        list:hasNext      inst:IfcCartesianPoint_List_2109 .

inst:IfcCartesianPoint_List_2109
        list:hasContents  inst:IfcCartesianPoint_339 .

inst:IfcFaceOuterBound_792
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_791 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_793  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_792 .

inst:IfcPolyLoop_794  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_2110
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_794  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_2110 .

inst:IfcCartesianPoint_List_2111
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_2112
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_2110
        list:hasContents  inst:IfcCartesianPoint_261 ;
        list:hasNext      inst:IfcCartesianPoint_List_2111 .

inst:IfcCartesianPoint_List_2111
        list:hasContents  inst:IfcCartesianPoint_262 ;
        list:hasNext      inst:IfcCartesianPoint_List_2112 .

inst:IfcCartesianPoint_List_2112
        list:hasContents  inst:IfcCartesianPoint_338 .

inst:IfcFaceOuterBound_795
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_794 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_796  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_795 .

inst:IfcPolyLoop_797  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_2113
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_797  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_2113 .

inst:IfcCartesianPoint_List_2114
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_2115
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_2113
        list:hasContents  inst:IfcCartesianPoint_262 ;
        list:hasNext      inst:IfcCartesianPoint_List_2114 .

inst:IfcCartesianPoint_List_2114
        list:hasContents  inst:IfcCartesianPoint_263 ;
        list:hasNext      inst:IfcCartesianPoint_List_2115 .

inst:IfcCartesianPoint_List_2115
        list:hasContents  inst:IfcCartesianPoint_337 .

inst:IfcFaceOuterBound_798
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_797 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_799  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_798 .

inst:IfcPolyLoop_800  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_2116
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_800  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_2116 .

inst:IfcCartesianPoint_List_2117
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_2118
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_2116
        list:hasContents  inst:IfcCartesianPoint_263 ;
        list:hasNext      inst:IfcCartesianPoint_List_2117 .

inst:IfcCartesianPoint_List_2117
        list:hasContents  inst:IfcCartesianPoint_264 ;
        list:hasNext      inst:IfcCartesianPoint_List_2118 .

inst:IfcCartesianPoint_List_2118
        list:hasContents  inst:IfcCartesianPoint_336 .

inst:IfcFaceOuterBound_801
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_800 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_802  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_801 .

inst:IfcPolyLoop_803  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_2119
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_803  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_2119 .

inst:IfcCartesianPoint_List_2120
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_2121
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_2119
        list:hasContents  inst:IfcCartesianPoint_264 ;
        list:hasNext      inst:IfcCartesianPoint_List_2120 .

inst:IfcCartesianPoint_List_2120
        list:hasContents  inst:IfcCartesianPoint_265 ;
        list:hasNext      inst:IfcCartesianPoint_List_2121 .

inst:IfcCartesianPoint_List_2121
        list:hasContents  inst:IfcCartesianPoint_335 .

inst:IfcFaceOuterBound_804
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_803 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_805  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_804 .

inst:IfcPolyLoop_806  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_2122
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_806  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_2122 .

inst:IfcCartesianPoint_List_2123
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_2124
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_2122
        list:hasContents  inst:IfcCartesianPoint_265 ;
        list:hasNext      inst:IfcCartesianPoint_List_2123 .

inst:IfcCartesianPoint_List_2123
        list:hasContents  inst:IfcCartesianPoint_266 ;
        list:hasNext      inst:IfcCartesianPoint_List_2124 .

inst:IfcCartesianPoint_List_2124
        list:hasContents  inst:IfcCartesianPoint_334 .

inst:IfcFaceOuterBound_807
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_806 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_808  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_807 .

inst:IfcPolyLoop_809  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_2125
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_809  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_2125 .

inst:IfcCartesianPoint_List_2126
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_2127
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_2125
        list:hasContents  inst:IfcCartesianPoint_266 ;
        list:hasNext      inst:IfcCartesianPoint_List_2126 .

inst:IfcCartesianPoint_List_2126
        list:hasContents  inst:IfcCartesianPoint_267 ;
        list:hasNext      inst:IfcCartesianPoint_List_2127 .

inst:IfcCartesianPoint_List_2127
        list:hasContents  inst:IfcCartesianPoint_333 .

inst:IfcFaceOuterBound_810
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_809 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_811  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_810 .

inst:IfcPolyLoop_812  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_2128
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_812  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_2128 .

inst:IfcCartesianPoint_List_2129
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_2130
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_2128
        list:hasContents  inst:IfcCartesianPoint_267 ;
        list:hasNext      inst:IfcCartesianPoint_List_2129 .

inst:IfcCartesianPoint_List_2129
        list:hasContents  inst:IfcCartesianPoint_268 ;
        list:hasNext      inst:IfcCartesianPoint_List_2130 .

inst:IfcCartesianPoint_List_2130
        list:hasContents  inst:IfcCartesianPoint_332 .

inst:IfcFaceOuterBound_813
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_812 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_814  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_813 .

inst:IfcPolyLoop_815  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_2131
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_815  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_2131 .

inst:IfcCartesianPoint_List_2132
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_2133
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_2131
        list:hasContents  inst:IfcCartesianPoint_268 ;
        list:hasNext      inst:IfcCartesianPoint_List_2132 .

inst:IfcCartesianPoint_List_2132
        list:hasContents  inst:IfcCartesianPoint_269 ;
        list:hasNext      inst:IfcCartesianPoint_List_2133 .

inst:IfcCartesianPoint_List_2133
        list:hasContents  inst:IfcCartesianPoint_331 .

inst:IfcFaceOuterBound_816
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_815 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_817  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_816 .

inst:IfcPolyLoop_818  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_2134
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_818  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_2134 .

inst:IfcCartesianPoint_List_2135
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_2136
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_2134
        list:hasContents  inst:IfcCartesianPoint_269 ;
        list:hasNext      inst:IfcCartesianPoint_List_2135 .

inst:IfcCartesianPoint_List_2135
        list:hasContents  inst:IfcCartesianPoint_270 ;
        list:hasNext      inst:IfcCartesianPoint_List_2136 .

inst:IfcCartesianPoint_List_2136
        list:hasContents  inst:IfcCartesianPoint_330 .

inst:IfcFaceOuterBound_819
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_818 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_820  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_819 .

inst:IfcPolyLoop_821  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_2137
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_821  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_2137 .

inst:IfcCartesianPoint_List_2138
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_2139
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_2137
        list:hasContents  inst:IfcCartesianPoint_270 ;
        list:hasNext      inst:IfcCartesianPoint_List_2138 .

inst:IfcCartesianPoint_List_2138
        list:hasContents  inst:IfcCartesianPoint_271 ;
        list:hasNext      inst:IfcCartesianPoint_List_2139 .

inst:IfcCartesianPoint_List_2139
        list:hasContents  inst:IfcCartesianPoint_329 .

inst:IfcFaceOuterBound_822
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_821 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_823  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_822 .

inst:IfcPolyLoop_824  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_2140
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_824  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_2140 .

inst:IfcCartesianPoint_List_2141
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_2142
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_2140
        list:hasContents  inst:IfcCartesianPoint_271 ;
        list:hasNext      inst:IfcCartesianPoint_List_2141 .

inst:IfcCartesianPoint_List_2141
        list:hasContents  inst:IfcCartesianPoint_272 ;
        list:hasNext      inst:IfcCartesianPoint_List_2142 .

inst:IfcCartesianPoint_List_2142
        list:hasContents  inst:IfcCartesianPoint_328 .

inst:IfcFaceOuterBound_825
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_824 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_826  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_825 .

inst:IfcPolyLoop_827  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_2143
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_827  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_2143 .

inst:IfcCartesianPoint_List_2144
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_2145
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_2143
        list:hasContents  inst:IfcCartesianPoint_272 ;
        list:hasNext      inst:IfcCartesianPoint_List_2144 .

inst:IfcCartesianPoint_List_2144
        list:hasContents  inst:IfcCartesianPoint_273 ;
        list:hasNext      inst:IfcCartesianPoint_List_2145 .

inst:IfcCartesianPoint_List_2145
        list:hasContents  inst:IfcCartesianPoint_327 .

inst:IfcFaceOuterBound_828
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_827 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_829  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_828 .

inst:IfcPolyLoop_830  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_2146
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_830  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_2146 .

inst:IfcCartesianPoint_List_2147
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_2148
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_2146
        list:hasContents  inst:IfcCartesianPoint_273 ;
        list:hasNext      inst:IfcCartesianPoint_List_2147 .

inst:IfcCartesianPoint_List_2147
        list:hasContents  inst:IfcCartesianPoint_274 ;
        list:hasNext      inst:IfcCartesianPoint_List_2148 .

inst:IfcCartesianPoint_List_2148
        list:hasContents  inst:IfcCartesianPoint_326 .

inst:IfcFaceOuterBound_831
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_830 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_832  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_831 .

inst:IfcPolyLoop_833  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_2149
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_833  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_2149 .

inst:IfcCartesianPoint_List_2150
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_2151
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_2149
        list:hasContents  inst:IfcCartesianPoint_274 ;
        list:hasNext      inst:IfcCartesianPoint_List_2150 .

inst:IfcCartesianPoint_List_2150
        list:hasContents  inst:IfcCartesianPoint_275 ;
        list:hasNext      inst:IfcCartesianPoint_List_2151 .

inst:IfcCartesianPoint_List_2151
        list:hasContents  inst:IfcCartesianPoint_325 .

inst:IfcFaceOuterBound_834
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_833 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_835  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_834 .

inst:IfcPolyLoop_836  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_2152
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_836  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_2152 .

inst:IfcCartesianPoint_List_2153
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_2154
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_2152
        list:hasContents  inst:IfcCartesianPoint_275 ;
        list:hasNext      inst:IfcCartesianPoint_List_2153 .

inst:IfcCartesianPoint_List_2153
        list:hasContents  inst:IfcCartesianPoint_276 ;
        list:hasNext      inst:IfcCartesianPoint_List_2154 .

inst:IfcCartesianPoint_List_2154
        list:hasContents  inst:IfcCartesianPoint_324 .

inst:IfcFaceOuterBound_837
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_836 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_838  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_837 .

inst:IfcPolyLoop_839  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_2155
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_839  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_2155 .

inst:IfcCartesianPoint_List_2156
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_2157
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_2155
        list:hasContents  inst:IfcCartesianPoint_276 ;
        list:hasNext      inst:IfcCartesianPoint_List_2156 .

inst:IfcCartesianPoint_List_2156
        list:hasContents  inst:IfcCartesianPoint_277 ;
        list:hasNext      inst:IfcCartesianPoint_List_2157 .

inst:IfcCartesianPoint_List_2157
        list:hasContents  inst:IfcCartesianPoint_323 .

inst:IfcFaceOuterBound_840
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_839 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_841  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_840 .

inst:IfcPolyLoop_842  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_2158
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_842  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_2158 .

inst:IfcCartesianPoint_List_2159
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_2160
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_2158
        list:hasContents  inst:IfcCartesianPoint_277 ;
        list:hasNext      inst:IfcCartesianPoint_List_2159 .

inst:IfcCartesianPoint_List_2159
        list:hasContents  inst:IfcCartesianPoint_278 ;
        list:hasNext      inst:IfcCartesianPoint_List_2160 .

inst:IfcCartesianPoint_List_2160
        list:hasContents  inst:IfcCartesianPoint_322 .

inst:IfcFaceOuterBound_843
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_842 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_844  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_843 .

inst:IfcPolyLoop_845  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_2161
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_845  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_2161 .

inst:IfcCartesianPoint_List_2162
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_2163
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_2161
        list:hasContents  inst:IfcCartesianPoint_278 ;
        list:hasNext      inst:IfcCartesianPoint_List_2162 .

inst:IfcCartesianPoint_List_2162
        list:hasContents  inst:IfcCartesianPoint_279 ;
        list:hasNext      inst:IfcCartesianPoint_List_2163 .

inst:IfcCartesianPoint_List_2163
        list:hasContents  inst:IfcCartesianPoint_321 .

inst:IfcFaceOuterBound_846
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_845 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_847  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_846 .

inst:IfcPolyLoop_848  rdf:type  ifc:IfcPolyLoop .

inst:IfcCartesianPoint_List_2164
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyLoop_848  ifc:polygon_IfcPolyLoop  inst:IfcCartesianPoint_List_2164 .

inst:IfcCartesianPoint_List_2165
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_2166
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_2164
        list:hasContents  inst:IfcCartesianPoint_279 ;
        list:hasNext      inst:IfcCartesianPoint_List_2165 .

inst:IfcCartesianPoint_List_2165
        list:hasContents  inst:IfcCartesianPoint_201 ;
        list:hasNext      inst:IfcCartesianPoint_List_2166 .

inst:IfcCartesianPoint_List_2166
        list:hasContents  inst:IfcCartesianPoint_320 .

inst:IfcFaceOuterBound_849
        rdf:type                      ifc:IfcFaceOuterBound ;
        ifc:bound_IfcFaceBound        inst:IfcPolyLoop_848 ;
        ifc:orientation_IfcFaceBound  inst:IfcBoolean_1566 .

inst:IfcFace_850  rdf:type  ifc:IfcFace ;
        ifc:bounds_IfcFace  inst:IfcFaceOuterBound_849 .

inst:IfcClosedShell_851
        rdf:type  ifc:IfcClosedShell ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_364 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_367 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_370 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_373 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_376 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_379 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_382 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_385 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_388 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_391 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_394 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_397 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_400 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_403 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_406 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_409 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_412 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_415 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_418 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_421 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_424 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_427 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_430 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_433 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_436 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_439 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_442 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_445 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_448 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_451 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_454 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_457 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_460 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_463 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_466 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_469 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_472 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_475 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_478 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_481 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_484 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_487 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_490 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_493 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_496 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_499 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_502 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_505 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_508 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_511 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_514 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_517 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_520 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_523 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_526 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_529 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_532 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_535 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_538 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_541 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_544 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_547 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_550 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_553 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_556 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_559 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_562 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_565 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_568 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_571 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_574 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_577 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_580 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_583 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_586 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_589 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_592 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_595 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_598 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_601 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_604 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_607 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_610 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_613 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_616 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_619 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_622 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_625 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_628 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_631 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_634 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_637 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_640 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_643 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_646 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_649 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_652 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_655 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_658 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_661 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_664 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_667 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_670 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_673 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_676 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_679 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_682 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_685 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_688 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_691 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_694 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_697 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_700 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_703 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_706 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_709 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_712 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_715 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_718 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_721 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_724 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_727 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_730 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_733 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_736 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_739 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_742 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_745 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_748 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_751 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_754 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_757 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_760 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_763 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_766 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_769 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_772 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_775 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_778 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_781 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_784 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_787 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_790 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_793 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_796 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_799 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_802 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_805 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_808 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_811 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_814 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_817 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_820 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_823 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_826 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_829 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_832 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_835 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_838 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_841 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_844 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_847 ;
        ifc:cfsFaces_IfcConnectedFaceSet  inst:IfcFace_850 .

inst:IfcFacetedBrep_852
        rdf:type                        ifc:IfcFacetedBrep ;
        ifc:outer_IfcManifoldSolidBrep  inst:IfcClosedShell_851 .

inst:IfcRepresentationMap_853
        rdf:type  ifc:IfcRepresentationMap .

inst:IfcAxis2Placement3D_854
        rdf:type  ifc:IfcAxis2Placement3D .

inst:IfcRepresentationMap_853
        ifc:mappingOrigin_IfcRepresentationMap  inst:IfcAxis2Placement3D_854 .

inst:IfcShapeRepresentation_856
        rdf:type  ifc:IfcShapeRepresentation .

inst:IfcRepresentationMap_853
        ifc:mappedRepresentation_IfcRepresentationMap  inst:IfcShapeRepresentation_856 .

inst:IfcAxis2Placement3D_854
        ifc:location_IfcPlacement  inst:IfcCartesianPoint_9 .

inst:IfcShapeRepresentation_856
        ifc:contextOfItems_IfcRepresentation  inst:IfcGeometricRepresentationSubContext_12 ;
        ifc:representationIdentifier_IfcRepresentation  inst:IfcLabel_714 .

inst:IfcLabel_2167  rdf:type  ifc:IfcLabel ;
        express:hasString  "Brep" .

inst:IfcShapeRepresentation_856
        ifc:representationType_IfcRepresentation  inst:IfcLabel_2167 ;
        ifc:items_IfcRepresentation  inst:IfcFacetedBrep_852 .

inst:IfcMaterial_857  rdf:type  ifc:IfcMaterial .

inst:IfcLabel_2168  rdf:type  ifc:IfcLabel ;
        express:hasString  "Ceramic" .

inst:IfcMaterial_857  ifc:name_IfcMaterial  inst:IfcLabel_2168 .

inst:IfcRelAssociatesMaterial_858
        rdf:type  ifc:IfcRelAssociatesMaterial .

inst:IfcGloballyUniqueId_2169
        rdf:type           ifc:IfcGloballyUniqueId ;
        express:hasString  "2V4PnJ8zX1nwtfgVsb30Vx" .

inst:IfcRelAssociatesMaterial_858
        ifc:globalId_IfcRoot  inst:IfcGloballyUniqueId_2169 .

inst:IfcLabel_2170  rdf:type  ifc:IfcLabel ;
        express:hasString  "MatAssoc" .

inst:IfcRelAssociatesMaterial_858
        ifc:name_IfcRoot  inst:IfcLabel_2170 .

inst:IfcText_2171  rdf:type  ifc:IfcText ;
        express:hasString  "Material Associates" .

inst:IfcRelAssociatesMaterial_858
        ifc:description_IfcRoot  inst:IfcText_2171 .

inst:IfcSanitaryTerminalType_860
        rdf:type  ifc:IfcSanitaryTerminalType .

inst:IfcRelAssociatesMaterial_858
        ifc:relatedObjects_IfcRelAssociates  inst:IfcSanitaryTerminalType_860 ;
        ifc:relatingMaterial_IfcRelAssociatesMaterial  inst:IfcMaterial_857 .

inst:IfcGloballyUniqueId_2172
        rdf:type           ifc:IfcGloballyUniqueId ;
        express:hasString  "1kNLoccErCohWO1HlSUjUS" .

inst:IfcSanitaryTerminalType_860
        ifc:globalId_IfcRoot  inst:IfcGloballyUniqueId_2172 .

inst:IfcLabel_2173  rdf:type  ifc:IfcLabel ;
        express:hasString  "IFCSANITARYTERMINALTYPE" .

inst:IfcSanitaryTerminalType_860
        ifc:name_IfcRoot  inst:IfcLabel_2173 .

inst:IfcRepresentationMap_List_2174
        rdf:type  ifc:IfcRepresentationMap_List .

inst:IfcSanitaryTerminalType_860
        ifc:representationMaps_IfcTypeProduct  inst:IfcRepresentationMap_List_2174 .

inst:IfcRepresentationMap_List_2174
        list:hasContents  inst:IfcRepresentationMap_853 .

inst:IfcSanitaryTerminalType_860
        ifc:predefinedType_IfcSanitaryTerminalType  ifc:WASHHANDBASIN .

inst:IfcRelDefinesByType_861
        rdf:type  ifc:IfcRelDefinesByType .

inst:IfcGloballyUniqueId_2175
        rdf:type           ifc:IfcGloballyUniqueId ;
        express:hasString  "0eraknYh91k81bLjGOgkw2" .

inst:IfcRelDefinesByType_861
        ifc:globalId_IfcRoot  inst:IfcGloballyUniqueId_2175 ;
        ifc:relatedObjects_IfcRelDefinesByType  inst:IfcSanitaryTerminal_868 ;
        ifc:relatingType_IfcRelDefinesByType  inst:IfcSanitaryTerminalType_860 .

inst:IfcDirection_862
        rdf:type  ifc:IfcDirection .

inst:IfcReal_List_2176
        rdf:type  ifc:IfcReal_List .

inst:IfcDirection_862
        ifc:directionRatios_IfcDirection  inst:IfcReal_List_2176 .

inst:IfcReal_List_2177
        rdf:type  ifc:IfcReal_List .

inst:IfcReal_List_2178
        rdf:type  ifc:IfcReal_List .

inst:IfcReal_List_2176
        list:hasContents  inst:IfcReal_712 ;
        list:hasNext      inst:IfcReal_List_2177 .

inst:IfcReal_List_2177
        list:hasContents  inst:IfcLengthMeasure_709 ;
        list:hasNext      inst:IfcReal_List_2178 .

inst:IfcReal_List_2178
        list:hasContents  inst:IfcLengthMeasure_709 .

inst:IfcDirection_863
        rdf:type  ifc:IfcDirection .

inst:IfcReal_List_2179
        rdf:type  ifc:IfcReal_List .

inst:IfcDirection_863
        ifc:directionRatios_IfcDirection  inst:IfcReal_List_2179 .

inst:IfcReal_List_2180
        rdf:type  ifc:IfcReal_List .

inst:IfcReal_List_2181
        rdf:type  ifc:IfcReal_List .

inst:IfcReal_List_2179
        list:hasContents  inst:IfcLengthMeasure_709 ;
        list:hasNext      inst:IfcReal_List_2180 .

inst:IfcReal_List_2180
        list:hasContents  inst:IfcReal_712 ;
        list:hasNext      inst:IfcReal_List_2181 .

inst:IfcReal_List_2181
        list:hasContents  inst:IfcLengthMeasure_709 .

inst:IfcCartesianTransformationOperator3D_865
        rdf:type  ifc:IfcCartesianTransformationOperator3D ;
        ifc:axis1_IfcCartesianTransformationOperator  inst:IfcDirection_862 ;
        ifc:axis2_IfcCartesianTransformationOperator  inst:IfcDirection_863 ;
        ifc:localOrigin_IfcCartesianTransformationOperator  inst:IfcCartesianPoint_9 ;
        ifc:scale_IfcCartesianTransformationOperator  inst:IfcReal_712 .

inst:IfcDirection_866
        rdf:type  ifc:IfcDirection .

inst:IfcCartesianTransformationOperator3D_865
        ifc:axis3_IfcCartesianTransformationOperator3D  inst:IfcDirection_866 .

inst:IfcReal_List_2182
        rdf:type  ifc:IfcReal_List .

inst:IfcDirection_866
        ifc:directionRatios_IfcDirection  inst:IfcReal_List_2182 .

inst:IfcReal_List_2183
        rdf:type  ifc:IfcReal_List .

inst:IfcReal_List_2184
        rdf:type  ifc:IfcReal_List .

inst:IfcReal_List_2182
        list:hasContents  inst:IfcLengthMeasure_709 ;
        list:hasNext      inst:IfcReal_List_2183 .

inst:IfcReal_List_2183
        list:hasContents  inst:IfcLengthMeasure_709 ;
        list:hasNext      inst:IfcReal_List_2184 .

inst:IfcReal_List_2184
        list:hasContents  inst:IfcReal_712 .

inst:IfcMappedItem_867
        rdf:type  ifc:IfcMappedItem ;
        ifc:mappingSource_IfcMappedItem  inst:IfcRepresentationMap_853 ;
        ifc:mappingTarget_IfcMappedItem  inst:IfcCartesianTransformationOperator3D_865 .

inst:IfcGloballyUniqueId_2185
        rdf:type           ifc:IfcGloballyUniqueId ;
        express:hasString  "3tkl48paX9yw0lahGPGmeU" .

inst:IfcSanitaryTerminal_868
        ifc:globalId_IfcRoot            inst:IfcGloballyUniqueId_2185 ;
        ifc:objectPlacement_IfcProduct  inst:IfcLocalPlacement_55 .

inst:IfcProductDefinitionShape_869
        rdf:type  ifc:IfcProductDefinitionShape .

inst:IfcSanitaryTerminal_868
        ifc:representation_IfcProduct  inst:IfcProductDefinitionShape_869 ;
        ifc:predefinedType_IfcSanitaryTerminal  ifc:NOTDEFINED .

inst:IfcRepresentation_List_2186
        rdf:type  ifc:IfcRepresentation_List .

inst:IfcProductDefinitionShape_869
        ifc:representations_IfcProductRepresentation  inst:IfcRepresentation_List_2186 .

inst:IfcShapeRepresentation_870
        rdf:type  ifc:IfcShapeRepresentation .

inst:IfcRepresentation_List_2186
        list:hasContents  inst:IfcShapeRepresentation_870 .

inst:IfcShapeRepresentation_870
        ifc:contextOfItems_IfcRepresentation  inst:IfcGeometricRepresentationSubContext_12 ;
        ifc:representationIdentifier_IfcRepresentation  inst:IfcLabel_714 .

inst:IfcLabel_2187  rdf:type  ifc:IfcLabel ;
        express:hasString  "MappedRepresentation" .

inst:IfcShapeRepresentation_870
        ifc:representationType_IfcRepresentation  inst:IfcLabel_2187 ;
        ifc:items_IfcRepresentation  inst:IfcMappedItem_867 .

ISO-10303-21;
HEADER;
FILE_DESCRIPTION(('ViewDefinition [CoordinationView]'),'2;1');
FILE_NAME('','2020-10-30T00:18:54',(),(),'IfcOpenShell 0.6.0b0','IfcOpenShell 0.6.0b0','');
FILE_SCHEMA(('IFC4'));
ENDSEC;
DATA;
#1=IFCBUILDING('2UrSelLcv7p8sV8DUl$XR_',$,'IfcBuilding',$,$,#16,$,$,.ELEMENT.,$,$,#19);
#2=IFCPROJECT('0HXpBSJeXCieb8ZCRZtdyz',#7,'IfcProject',$,$,'IfcProject',$,(#11),#20);
#3=IFCSANITARYTERMINALTYPE('1kNLoccErCohWO1HlSUjUS',$,'IFCSANITARYTERMINALTYPE',$,$,$,(#24),$,$,.WASHHANDBASIN.);
#4=IFCSANITARYTERMINAL('3tkl48paX9yw0lahGPGmeU',$,$,$,$,#27,#33,$,.NOTDEFINED.);
#5=IFCSHAPEREPRESENTATION(#10,'Body','Brep',(#34));
#6=IFCSHAPEREPRESENTATION(#10,'Body','MappedRepresentation',(#1166));
#7=IFCOWNERHISTORY(#1175,#1178,$,.ADDED.,1418084875,$,$,1418084875);
#8=IFCGEOMETRICREPRESENTATIONCONTEXT($,'Model',3,0.0001,#1180,#1182);
#9=IFCGEOMETRICREPRESENTATIONSUBCONTEXT('Axis','Model',*,*,*,*,#8,$,.MODEL_VIEW.,$);
#10=IFCGEOMETRICREPRESENTATIONSUBCONTEXT('Body','Model',*,*,*,*,#8,$,.MODEL_VIEW.,$);
#11=IFCGEOMETRICREPRESENTATIONCONTEXT($,'Model',3,0.0001,#1183,#1185);
#12=IFCRELCONTAINEDINSPATIALSTRUCTURE('1nLCX25sz2RPQ379yFIRFo',$,'Building','Building Container for Elements',(#4),#1);
#13=IFCRELAGGREGATES('0uZ92lKhT6EwklfgWpFFGq',$,'Project Container','Project Container for Buildings',#2,(#1));
#14=IFCRELASSOCIATESMATERIAL('2V4PnJ8zX1nwtfgVsb30Vx',$,'MatAssoc','Material Associates',(#3),#1186);
#15=IFCRELDEFINESBYTYPE('0eraknYh91k81bLjGOgkw2',$,$,$,(#4),#3);
#16=IFCLOCALPLACEMENT($,#17);
#17=IFCAXIS2PLACEMENT3D(#18,$,$);
#18=IFCCARTESIANPOINT((0.,0.,0.));
#19=IFCPOSTALADDRESS($,$,$,$,$,$,$,'Unknown',$,$);
#20=IFCUNITASSIGNMENT((#21,#22,#23));
#21=IFCSIUNIT(*,.LENGTHUNIT.,.MILLI.,.METRE.);
#22=IFCSIUNIT(*,.PLANEANGLEUNIT.,$,.RADIAN.);
#23=IFCSIUNIT(*,.TIMEUNIT.,$,.SECOND.);
#24=IFCREPRESENTATIONMAP(#25,#5);
#25=IFCAXIS2PLACEMENT3D(#26,$,$);
#26=IFCCARTESIANPOINT((0.,0.,0.));
#27=IFCLOCALPLACEMENT(#28,#31);
#28=IFCLOCALPLACEMENT($,#29);
#29=IFCAXIS2PLACEMENT3D(#30,$,$);
#30=IFCCARTESIANPOINT((0.,0.,0.));
#31=IFCAXIS2PLACEMENT3D(#32,$,$);
#32=IFCCARTESIANPOINT((0.,0.,0.));
#33=IFCPRODUCTDEFINITIONSHAPE($,$,(#6));
#34=IFCFACETEDBREP(#35);
#35=IFCCLOSEDSHELL((#36,#120,#163,#206,#212,#218,#224,#230,#236,#242,#248,#254,#260,#266,#272,#278,#284,#290,#296,#302,#308,#314,#320,#326,#332,#338,#344,#350,#356,#362,#368,#374,#380,#386,#392,#398,#404,#410,#416,#422,#428,#434,#440,#446,#452,#458,#464,#470,#476,#482,#488,#494,#500,#506,#512,#518,#524,#530,#536,#542,#548,#554,#560,#566,#572,#578,#584,#590,#596,#602,#608,#614,#620,#626,#632,#638,#644,#650,#656,#662,#668,#674,#680,#686,#692,#698,#704,#710,#716,#722,#728,#734,#740,#746,#752,#758,#764,#770,#776,#782,#788,#794,#800,#806,#812,#818,#824,#830,#836,#842,#848,#854,#860,#866,#872,#878,#884,#890,#896,#902,#908,#914,#920,#926,#932,#938,#944,#950,#956,#962,#968,#974,#980,#986,#992,#998,#1004,#1010,#1016,#1022,#1028,#1034,#1040,#1046,#1052,#1058,#1064,#1070,#1076,#1082,#1088,#1094,#1100,#1106,#1112,#1118,#1124,#1130,#1136,#1142,#1148,#1154,#1160));
#36=IFCFACE((#37,#78));
#37=IFCFACEOUTERBOUND(#38,.T.);
#38=IFCPOLYLOOP((#39,#40,#41,#42,#43,#44,#45,#46,#47,#48,#49,#50,#51,#52,#53,#54,#55,#56,#57,#58,#59,#60,#61,#62,#63,#64,#65,#66,#67,#68,#69,#70,#71,#72,#73,#74,#75,#76,#77));
#39=IFCCARTESIANPOINT((-304.891071307496,109.822256120206,0.));
#40=IFCCARTESIANPOINT((-296.940075211924,69.9862810835965,0.));
#41=IFCCARTESIANPOINT((-280.922701731312,32.5907488402735,0.));
#42=IFCCARTESIANPOINT((-259.608278768049,-2.08529338102262,0.));
#43=IFCCARTESIANPOINT((-234.428168068557,-34.0753473673235,0.));
#44=IFCCARTESIANPOINT((-206.125816386308,-63.3430855287523,0.));
#45=IFCCARTESIANPOINT((-175.071534188435,-89.671802213621,0.));
#46=IFCCARTESIANPOINT((-141.412934526735,-112.569598180375,0.));
#47=IFCCARTESIANPOINT((-105.215114341225,-131.178510124149,0.));
#48=IFCCARTESIANPOINT((-66.7978479682479,-144.600522500239,0.));
#49=IFCCARTESIANPOINT((-26.8019125425913,-152.078206375312,0.));
#50=IFCCARTESIANPOINT((13.8710862775596,-153.121660363236,0.));
#51=IFCCARTESIANPOINT((54.1903326227934,-147.658103330519,0.));
#52=IFCCARTESIANPOINT((93.1938862047157,-136.056391059388,0.));
#53=IFCCARTESIANPOINT((130.150407237097,-119.008883827061,0.));
#54=IFCCARTESIANPOINT((164.629900262907,-97.37165169902,0.));
#55=IFCCARTESIANPOINT((196.521914802755,-72.0656434367093,0.));
#56=IFCCARTESIANPOINT((225.728273711876,-43.6997637700286,0.));
#57=IFCCARTESIANPOINT((251.961645534009,-12.5658965535741,0.));
#58=IFCCARTESIANPOINT((274.62087031373,21.2509318856294,0.));
#59=IFCCARTESIANPOINT((292.544561947164,57.7814080324017,0.));
#60=IFCCARTESIANPOINT((303.485368351088,96.9291090994897,0.));
#61=IFCCARTESIANPOINT((303.288957242437,137.460278743209,0.));
#62=IFCCARTESIANPOINT((287.369755242598,174.609406522514,0.));
#63=IFCCARTESIANPOINT((258.965081713391,203.584218912768,0.));
#64=IFCCARTESIANPOINT((224.363000998313,224.953681400811,0.));
#65=IFCCARTESIANPOINT((186.818017180943,240.67163971588,0.));
#66=IFCCARTESIANPOINT((147.756154156487,252.155742473922,0.));
#67=IFCCARTESIANPOINT((107.854433144861,260.280582237886,0.));
#68=IFCCARTESIANPOINT((67.4765332607867,265.571769764524,0.));
#69=IFCCARTESIANPOINT((26.8458427708863,268.331827033896,0.));
#70=IFCCARTESIANPOINT((-13.8771780303431,268.706812331913,0.));
#71=IFCCARTESIANPOINT((-54.5530293999268,266.715969920949,0.));
#72=IFCCARTESIANPOINT((-95.0316202590492,262.255497380798,0.));
#73=IFCCARTESIANPOINT((-135.115612880362,255.077835581894,0.));
#74=IFCCARTESIANPOINT((-174.498313981775,244.738726835566,0.));
#75=IFCCARTESIANPOINT((-212.631973130034,230.493081814022,0.));
#76=IFCCARTESIANPOINT((-248.405687646327,211.118276332925,0.));
#77=IFCCARTESIANPOINT((-279.319862190373,184.775801315119,0.));
#78=IFCFACEBOUND(#79,.T.);
#79=IFCPOLYLOOP((#80,#81,#82,#83,#84,#85,#86,#87,#88,#89,#90,#91,#92,#93,#94,#95,#96,#97,#98,#99,#100,#101,#102,#103,#104,#105,#106,#107,#108,#109,#110,#111,#112,#113,#114,#115,#116,#117,#118,#119));
#80=IFCCARTESIANPOINT((38.4756983219429,252.033478287557,0.));
#81=IFCCARTESIANPOINT((76.82797079471,248.781505427916,0.));
#82=IFCCARTESIANPOINT((114.905040261981,243.168611097888,0.));
#83=IFCCARTESIANPOINT((152.483825019629,234.862972697005,0.));
#84=IFCCARTESIANPOINT((189.184260308893,223.297735395069,0.));
#85=IFCCARTESIANPOINT((224.265826794532,207.523990642274,0.));
#86=IFCCARTESIANPOINT((256.087669717028,185.991172830599,0.));
#87=IFCCARTESIANPOINT((280.765071736094,156.699063336262,0.));
#88=IFCCARTESIANPOINT((291.585141614082,120.093338245881,0.));
#89=IFCCARTESIANPOINT((287.786485451514,81.9448662146907,0.));
#90=IFCCARTESIANPOINT((274.528157897687,45.8738463140147,0.));
#91=IFCCARTESIANPOINT((255.376512280737,12.5180862102588,0.));
#92=IFCCARTESIANPOINT((232.162082007755,-18.1641105843033,0.));
#93=IFCCARTESIANPOINT((205.82058249047,-46.2135089189517,0.));
#94=IFCCARTESIANPOINT((176.831543285532,-71.5181976728152,0.));
#95=IFCCARTESIANPOINT((145.419108882987,-93.7387882260769,0.));
#96=IFCCARTESIANPOINT((111.672515835794,-112.209605260782,0.));
#97=IFCCARTESIANPOINT((75.812198248967,-126.124204222316,0.));
#98=IFCCARTESIANPOINT((38.3440539351622,-134.804771445296,0.));
#99=IFCCARTESIANPOINT((5.62489231925E-06,-137.758996454453,0.));
#100=IFCCARTESIANPOINT((-38.3440494938959,-134.804772131387,0.));
#101=IFCCARTESIANPOINT((-75.8121974100292,-126.124204482417,0.));
#102=IFCCARTESIANPOINT((-111.672515690142,-112.209605328934,0.));
#103=IFCCARTESIANPOINT((-145.419108622881,-93.7387883895915,0.));
#104=IFCCARTESIANPOINT((-176.831543103321,-71.5181978165611,0.));
#105=IFCCARTESIANPOINT((-205.820584751455,-46.2135067401479,0.));
#106=IFCCARTESIANPOINT((-232.162080231681,-18.164112680286,0.));
#107=IFCCARTESIANPOINT((-255.37651255073,12.5180866141667,0.));
#108=IFCCARTESIANPOINT((-274.528157992219,45.8738465115148,0.));
#109=IFCCARTESIANPOINT((-287.786482832935,81.9448557189429,0.));
#110=IFCCARTESIANPOINT((-291.585141527273,120.093339480335,0.));
#111=IFCCARTESIANPOINT((-280.765069841138,156.69906670217,0.));
#112=IFCCARTESIANPOINT((-256.087676107765,185.991167334279,0.));
#113=IFCCARTESIANPOINT((-224.265825523088,207.523991330957,0.));
#114=IFCCARTESIANPOINT((-189.184272565133,223.297730817421,0.));
#115=IFCCARTESIANPOINT((-152.483829695455,234.862971464014,0.));
#116=IFCCARTESIANPOINT((-114.905033181042,243.168612385642,0.));
#117=IFCCARTESIANPOINT((-76.8279738129532,248.781505081298,0.));
#118=IFCCARTESIANPOINT((-38.4757029862493,252.0334780278,0.));
#119=IFCCARTESIANPOINT((0.,253.099263998677,0.));
#120=IFCFACE((#121));
#121=IFCFACEOUTERBOUND(#122,.T.);
#122=IFCPOLYLOOP((#123,#124,#125,#126,#127,#128,#129,#130,#131,#132,#133,#134,#135,#136,#137,#138,#139,#140,#141,#142,#143,#144,#145,#146,#147,#148,#149,#150,#151,#152,#153,#154,#155,#156,#157,#158,#159,#160,#161,#162));
#123=IFCCARTESIANPOINT((-159.799786377107,153.46782071584,-93.9999999999991));
#124=IFCCARTESIANPOINT((-156.563435238971,131.153861277019,-93.9999999999991));
#125=IFCCARTESIANPOINT((-149.376131896764,109.760102145713,-93.9999999999991));
#126=IFCCARTESIANPOINT((-139.405144871723,89.5038839334345,-93.9999999999991));
#127=IFCCARTESIANPOINT((-127.294335499358,70.445523587566,-93.9999999999991));
#128=IFCCARTESIANPOINT((-113.387459490709,52.6538727330131,-93.9999999999991));
#129=IFCCARTESIANPOINT((-97.8447661146846,36.2725918775068,-93.9999999999991));
#130=IFCCARTESIANPOINT((-80.698001162248,21.582584637739,-93.9999999999991));
#131=IFCCARTESIANPOINT((-61.8871867593298,9.10501350136987,-93.9999999999991));
#132=IFCCARTESIANPOINT((-41.4394014166823,-0.440153063918815,-93.9999999999991));
#133=IFCCARTESIANPOINT((-19.662998563232,-6.32985285628291,-93.9999999999991));
#134=IFCCARTESIANPOINT((2.83012817778957,-7.98927385674093,-93.9999999999991));
#135=IFCCARTESIANPOINT((25.2161298976784,-5.23363177905661,-93.9999999999991));
#136=IFCCARTESIANPOINT((46.7052656338443,1.63611866403177,-93.9999999999991));
#137=IFCCARTESIANPOINT((66.7603568037461,11.9848882796374,-93.9999999999991));
#138=IFCCARTESIANPOINT((85.1475914236449,25.0817028555501,-93.9999999999991));
#139=IFCCARTESIANPOINT((101.889510015946,40.2328202653944,-93.9999999999991));
#140=IFCCARTESIANPOINT((117.029708390029,56.9871779332136,-93.9999999999991));
#141=IFCCARTESIANPOINT((130.507477385555,75.1059075118196,-93.9999999999991));
#142=IFCCARTESIANPOINT((142.123444996071,94.4691487187488,-93.9999999999991));
#143=IFCCARTESIANPOINT((151.473017550452,115.017962619083,-93.9999999999991));
#144=IFCCARTESIANPOINT((157.807019966901,136.675894195075,-93.9999999999991));
#145=IFCCARTESIANPOINT((159.780564770642,159.127525554679,-93.9999999999991));
#146=IFCCARTESIANPOINT((155.311843754385,181.158856579337,-93.9999999999991));
#147=IFCCARTESIANPOINT((143.527109004701,200.308104946409,-93.9999999999991));
#148=IFCCARTESIANPOINT((126.728646947386,215.328663366711,-93.9999999999991));
#149=IFCCARTESIANPOINT((107.202467334812,226.634520930962,-93.9999999999991));
#150=IFCCARTESIANPOINT((86.2268790106761,234.98230335346,-93.9999999999991));
#151=IFCCARTESIANPOINT((64.4507343905464,240.958334023702,-93.9999999999991));
#152=IFCCARTESIANPOINT((42.2218098296953,244.946818741202,-93.9999999999991));
#153=IFCCARTESIANPOINT((19.7473348710258,247.180277362528,-93.9999999999991));
#154=IFCCARTESIANPOINT((-2.83037829161053,247.779902642456,-93.9999999999991));
#155=IFCCARTESIANPOINT((-25.3937160660832,246.777117598331,-93.9999999999991));
#156=IFCCARTESIANPOINT((-47.8217104720131,244.119106659353,-93.9999999999991));
#157=IFCCARTESIANPOINT((-69.9604240039862,239.659498513825,-93.9999999999991));
#158=IFCCARTESIANPOINT((-91.576683901205,233.132310979361,-93.9999999999991));
#159=IFCCARTESIANPOINT((-112.267364103545,224.107056891456,-93.9999999999991));
#160=IFCCARTESIANPOINT((-131.264894314891,211.943402767825,-93.9999999999991));
#161=IFCCARTESIANPOINT((-147.061746316906,195.888143420344,-93.9999999999991));
#162=IFCCARTESIANPOINT((-157.154914340418,175.808617178122,-93.9999999999991));
#163=IFCFACE((#164));
#164=IFCFACEOUTERBOUND(#165,.T.);
#165=IFCPOLYLOOP((#166,#167,#168,#169,#170,#171,#172,#173,#174,#175,#176,#177,#178,#179,#180,#181,#182,#183,#184,#185,#186,#187,#188,#189,#190,#191,#192,#193,#194,#195,#196,#197,#198,#199,#200,#201,#202,#203,#204,#205));
#166=IFCCARTESIANPOINT((-22.5714426203723,246.991542511388,-83.9999999999991));
#167=IFCCARTESIANPOINT((-45.0238143941789,244.546840992317,-83.9999999999991));
#168=IFCCARTESIANPOINT((-67.2093088278616,240.324720105693,-83.9999999999991));
#169=IFCCARTESIANPOINT((-88.9083392880943,234.076268171425,-83.9999999999991));
#170=IFCCARTESIANPOINT((-109.746783760019,225.394569776618,-83.9999999999991));
#171=IFCCARTESIANPOINT((-129.019214178155,213.666092805292,-83.9999999999991));
#172=IFCCARTESIANPOINT((-145.336255413146,198.131561931792,-83.9999999999991));
#173=IFCCARTESIANPOINT((-156.295574267743,178.505166800292,-83.9999999999991));
#174=IFCCARTESIANPOINT((-159.838406313243,156.297849730896,-83.9999999999991));
#175=IFCCARTESIANPOINT((-157.217231378449,133.907674702395,-83.9999999999991));
#176=IFCCARTESIANPOINT((-150.446948503868,112.380106954109,-83.9999999999991));
#177=IFCCARTESIANPOINT((-140.781370993691,91.9771671791993,-83.9999999999991));
#178=IFCCARTESIANPOINT((-128.915130782292,72.7659081552236,-83.9999999999991));
#179=IFCCARTESIANPOINT((-115.221432502533,54.8097171953257,-83.9999999999991));
#180=IFCCARTESIANPOINT((-99.8796470071603,38.239929890392,-83.9999999999991));
#181=IFCCARTESIANPOINT((-82.9357654214438,23.3156500424069,-83.9999999999991));
#182=IFCCARTESIANPOINT((-64.3369337417203,10.5226649501159,-83.9999999999991));
#183=IFCCARTESIANPOINT((-44.0834019165823,0.569910981625322,-83.9999999999991));
#184=IFCCARTESIANPOINT((-22.4460832273862,-5.81475796938246,-83.9999999999991));
#185=IFCCARTESIANPOINT((1.76219393597E-06,-8.0243005407274,-83.9999999999991));
#186=IFCCARTESIANPOINT((22.446084481302,-5.81475772180151,-83.9999999999991));
#187=IFCCARTESIANPOINT((44.0834024872804,0.569911206685156,-83.9999999999991));
#188=IFCCARTESIANPOINT((64.3369357795818,10.5226661545967,-83.9999999999991));
#189=IFCCARTESIANPOINT((82.9357630974296,23.3156482145766,-83.9999999999991));
#190=IFCCARTESIANPOINT((99.8796474348905,38.2399303092091,-83.9999999999991));
#191=IFCCARTESIANPOINT((115.221437914664,54.8097236343718,-83.9999999999991));
#192=IFCCARTESIANPOINT((128.915131219959,72.7659087899957,-83.9999999999991));
#193=IFCCARTESIANPOINT((140.781372023645,91.9771690603084,-83.9999999999991));
#194=IFCCARTESIANPOINT((150.44694977305,112.380110135675,-83.9999999999991));
#195=IFCCARTESIANPOINT((157.217230633118,133.907671395464,-83.9999999999991));
#196=IFCCARTESIANPOINT((159.838406320187,156.297847499286,-83.9999999999991));
#197=IFCCARTESIANPOINT((156.295574340587,178.505166588871,-83.9999999999991));
#198=IFCCARTESIANPOINT((145.336254078924,198.131563599338,-83.9999999999991));
#199=IFCCARTESIANPOINT((129.019208284081,213.666097201998,-83.9999999999991));
#200=IFCCARTESIANPOINT((109.746785929842,225.394568694178,-83.9999999999991));
#201=IFCCARTESIANPOINT((88.90833880495,234.07626833846,-83.9999999999991));
#202=IFCCARTESIANPOINT((67.2093069026739,240.324720559443,-83.9999999999991));
#203=IFCCARTESIANPOINT((45.0238122743352,244.546841305597,-83.9999999999991));
#204=IFCCARTESIANPOINT((22.5714426167356,246.991542511648,-83.9999999999991));
#205=IFCCARTESIANPOINT((0.,247.792422124388,-83.9999999999991));
#206=IFCFACE((#207));
#207=IFCFACEOUTERBOUND(#208,.T.);
#208=IFCPOLYLOOP((#209,#210,#211));
#209=IFCCARTESIANPOINT((-300.,150.,0.));
#210=IFCCARTESIANPOINT((-147.061746316906,195.888143420344,-93.9999999999991));
#211=IFCCARTESIANPOINT((-157.154914340418,175.808617178122,-93.9999999999991));
#212=IFCFACE((#213));
#213=IFCFACEOUTERBOUND(#214,.T.);
#214=IFCPOLYLOOP((#215,#216,#217));
#215=IFCCARTESIANPOINT((-279.319862190373,184.775801315119,0.));
#216=IFCCARTESIANPOINT((-131.264894314891,211.943402767825,-93.9999999999991));
#217=IFCCARTESIANPOINT((-147.061746316906,195.888143420344,-93.9999999999991));
#218=IFCFACE((#219));
#219=IFCFACEOUTERBOUND(#220,.T.);
#220=IFCPOLYLOOP((#221,#222,#223));
#221=IFCCARTESIANPOINT((-248.405687646327,211.118276332925,0.));
#222=IFCCARTESIANPOINT((-112.267364103545,224.107056891456,-93.9999999999991));
#223=IFCCARTESIANPOINT((-131.264894314891,211.943402767825,-93.9999999999991));
#224=IFCFACE((#225));
#225=IFCFACEOUTERBOUND(#226,.T.);
#226=IFCPOLYLOOP((#227,#228,#229));
#227=IFCCARTESIANPOINT((-212.631973130034,230.493081814022,0.));
#228=IFCCARTESIANPOINT((-91.576683901205,233.132310979361,-93.9999999999991));
#229=IFCCARTESIANPOINT((-112.267364103545,224.107056891456,-93.9999999999991));
#230=IFCFACE((#231));
#231=IFCFACEOUTERBOUND(#232,.T.);
#232=IFCPOLYLOOP((#233,#234,#235));
#233=IFCCARTESIANPOINT((-174.498313981775,244.738726835566,0.));
#234=IFCCARTESIANPOINT((-69.9604240039862,239.659498513825,-93.9999999999991));
#235=IFCCARTESIANPOINT((-91.576683901205,233.132310979361,-93.9999999999991));
#236=IFCFACE((#237));
#237=IFCFACEOUTERBOUND(#238,.T.);
#238=IFCPOLYLOOP((#239,#240,#241));
#239=IFCCARTESIANPOINT((-135.115612880362,255.077835581894,0.));
#240=IFCCARTESIANPOINT((-47.8217104720131,244.119106659353,-93.9999999999991));
#241=IFCCARTESIANPOINT((-69.9604240039862,239.659498513825,-93.9999999999991));
#242=IFCFACE((#243));
#243=IFCFACEOUTERBOUND(#244,.T.);
#244=IFCPOLYLOOP((#245,#246,#247));
#245=IFCCARTESIANPOINT((-95.0316202590492,262.255497380798,0.));
#246=IFCCARTESIANPOINT((-25.3937160660832,246.777117598331,-93.9999999999991));
#247=IFCCARTESIANPOINT((-47.8217104720131,244.119106659353,-93.9999999999991));
#248=IFCFACE((#249));
#249=IFCFACEOUTERBOUND(#250,.T.);
#250=IFCPOLYLOOP((#251,#252,#253));
#251=IFCCARTESIANPOINT((-54.5530293999268,266.715969920949,0.));
#252=IFCCARTESIANPOINT((-2.83037829161053,247.779902642456,-93.9999999999991));
#253=IFCCARTESIANPOINT((-25.3937160660832,246.777117598331,-93.9999999999991));
#254=IFCFACE((#255));
#255=IFCFACEOUTERBOUND(#256,.T.);
#256=IFCPOLYLOOP((#257,#258,#259));
#257=IFCCARTESIANPOINT((-13.8771780303431,268.706812331913,0.));
#258=IFCCARTESIANPOINT((19.7473348710258,247.180277362528,-93.9999999999991));
#259=IFCCARTESIANPOINT((-2.83037829161053,247.779902642456,-93.9999999999991));
#260=IFCFACE((#261));
#261=IFCFACEOUTERBOUND(#262,.T.);
#262=IFCPOLYLOOP((#263,#264,#265));
#263=IFCCARTESIANPOINT((26.8458427708863,268.331827033896,0.));
#264=IFCCARTESIANPOINT((42.2218098296953,244.946818741202,-93.9999999999991));
#265=IFCCARTESIANPOINT((19.7473348710258,247.180277362528,-93.9999999999991));
#266=IFCFACE((#267));
#267=IFCFACEOUTERBOUND(#268,.T.);
#268=IFCPOLYLOOP((#269,#270,#271));
#269=IFCCARTESIANPOINT((67.4765332607867,265.571769764524,0.));
#270=IFCCARTESIANPOINT((64.4507343905464,240.958334023702,-93.9999999999991));
#271=IFCCARTESIANPOINT((42.2218098296953,244.946818741202,-93.9999999999991));
#272=IFCFACE((#273));
#273=IFCFACEOUTERBOUND(#274,.T.);
#274=IFCPOLYLOOP((#275,#276,#277));
#275=IFCCARTESIANPOINT((107.854433144861,260.280582237886,0.));
#276=IFCCARTESIANPOINT((86.2268790106761,234.98230335346,-93.9999999999991));
#277=IFCCARTESIANPOINT((64.4507343905464,240.958334023702,-93.9999999999991));
#278=IFCFACE((#279));
#279=IFCFACEOUTERBOUND(#280,.T.);
#280=IFCPOLYLOOP((#281,#282,#283));
#281=IFCCARTESIANPOINT((147.756154156487,252.155742473922,0.));
#282=IFCCARTESIANPOINT((107.202467334812,226.634520930962,-93.9999999999991));
#283=IFCCARTESIANPOINT((86.2268790106761,234.98230335346,-93.9999999999991));
#284=IFCFACE((#285));
#285=IFCFACEOUTERBOUND(#286,.T.);
#286=IFCPOLYLOOP((#287,#288,#289));
#287=IFCCARTESIANPOINT((186.818017180943,240.67163971588,0.));
#288=IFCCARTESIANPOINT((126.728646947386,215.328663366711,-93.9999999999991));
#289=IFCCARTESIANPOINT((107.202467334812,226.634520930962,-93.9999999999991));
#290=IFCFACE((#291));
#291=IFCFACEOUTERBOUND(#292,.T.);
#292=IFCPOLYLOOP((#293,#294,#295));
#293=IFCCARTESIANPOINT((224.363000998313,224.953681400811,0.));
#294=IFCCARTESIANPOINT((143.527109004701,200.308104946409,-93.9999999999991));
#295=IFCCARTESIANPOINT((126.728646947386,215.328663366711,-93.9999999999991));
#296=IFCFACE((#297));
#297=IFCFACEOUTERBOUND(#298,.T.);
#298=IFCPOLYLOOP((#299,#300,#301));
#299=IFCCARTESIANPOINT((258.965081713391,203.584218912768,0.));
#300=IFCCARTESIANPOINT((155.311843754385,181.158856579337,-93.9999999999991));
#301=IFCCARTESIANPOINT((143.527109004701,200.308104946409,-93.9999999999991));
#302=IFCFACE((#303));
#303=IFCFACEOUTERBOUND(#304,.T.);
#304=IFCPOLYLOOP((#305,#306,#307));
#305=IFCCARTESIANPOINT((287.369755242598,174.609406522514,0.));
#306=IFCCARTESIANPOINT((159.780564770642,159.127525554679,-93.9999999999991));
#307=IFCCARTESIANPOINT((155.311843754385,181.158856579337,-93.9999999999991));
#308=IFCFACE((#309));
#309=IFCFACEOUTERBOUND(#310,.T.);
#310=IFCPOLYLOOP((#311,#312,#313));
#311=IFCCARTESIANPOINT((303.288957242437,137.460278743209,0.));
#312=IFCCARTESIANPOINT((157.807019966901,136.675894195075,-93.9999999999991));
#313=IFCCARTESIANPOINT((159.780564770642,159.127525554679,-93.9999999999991));
#314=IFCFACE((#315));
#315=IFCFACEOUTERBOUND(#316,.T.);
#316=IFCPOLYLOOP((#317,#318,#319));
#317=IFCCARTESIANPOINT((303.485368351088,96.9291090994897,0.));
#318=IFCCARTESIANPOINT((151.473017550452,115.017962619083,-93.9999999999991));
#319=IFCCARTESIANPOINT((157.807019966901,136.675894195075,-93.9999999999991));
#320=IFCFACE((#321));
#321=IFCFACEOUTERBOUND(#322,.T.);
#322=IFCPOLYLOOP((#323,#324,#325));
#323=IFCCARTESIANPOINT((292.544561947164,57.7814080324017,0.));
#324=IFCCARTESIANPOINT((142.123444996071,94.4691487187488,-93.9999999999991));
#325=IFCCARTESIANPOINT((151.473017550452,115.017962619083,-93.9999999999991));
#326=IFCFACE((#327));
#327=IFCFACEOUTERBOUND(#328,.T.);
#328=IFCPOLYLOOP((#329,#330,#331));
#329=IFCCARTESIANPOINT((274.62087031373,21.2509318856294,0.));
#330=IFCCARTESIANPOINT((130.507477385555,75.1059075118196,-93.9999999999991));
#331=IFCCARTESIANPOINT((142.123444996071,94.4691487187488,-93.9999999999991));
#332=IFCFACE((#333));
#333=IFCFACEOUTERBOUND(#334,.T.);
#334=IFCPOLYLOOP((#335,#336,#337));
#335=IFCCARTESIANPOINT((251.961645534009,-12.5658965535741,0.));
#336=IFCCARTESIANPOINT((117.029708390029,56.9871779332136,-93.9999999999991));
#337=IFCCARTESIANPOINT((130.507477385555,75.1059075118196,-93.9999999999991));
#338=IFCFACE((#339));
#339=IFCFACEOUTERBOUND(#340,.T.);
#340=IFCPOLYLOOP((#341,#342,#343));
#341=IFCCARTESIANPOINT((225.728273711876,-43.6997637700286,0.));
#342=IFCCARTESIANPOINT((101.889510015946,40.2328202653944,-93.9999999999991));
#343=IFCCARTESIANPOINT((117.029708390029,56.9871779332136,-93.9999999999991));
#344=IFCFACE((#345));
#345=IFCFACEOUTERBOUND(#346,.T.);
#346=IFCPOLYLOOP((#347,#348,#349));
#347=IFCCARTESIANPOINT((196.521914802755,-72.0656434367093,0.));
#348=IFCCARTESIANPOINT((85.1475914236449,25.0817028555501,-93.9999999999991));
#349=IFCCARTESIANPOINT((101.889510015946,40.2328202653944,-93.9999999999991));
#350=IFCFACE((#351));
#351=IFCFACEOUTERBOUND(#352,.T.);
#352=IFCPOLYLOOP((#353,#354,#355));
#353=IFCCARTESIANPOINT((164.629900262907,-97.37165169902,0.));
#354=IFCCARTESIANPOINT((66.7603568037461,11.9848882796374,-93.9999999999991));
#355=IFCCARTESIANPOINT((85.1475914236449,25.0817028555501,-93.9999999999991));
#356=IFCFACE((#357));
#357=IFCFACEOUTERBOUND(#358,.T.);
#358=IFCPOLYLOOP((#359,#360,#361));
#359=IFCCARTESIANPOINT((130.150407237097,-119.008883827061,0.));
#360=IFCCARTESIANPOINT((46.7052656338443,1.63611866403177,-93.9999999999991));
#361=IFCCARTESIANPOINT((66.7603568037461,11.9848882796374,-93.9999999999991));
#362=IFCFACE((#363));
#363=IFCFACEOUTERBOUND(#364,.T.);
#364=IFCPOLYLOOP((#365,#366,#367));
#365=IFCCARTESIANPOINT((93.1938862047157,-136.056391059388,0.));
#366=IFCCARTESIANPOINT((25.2161298976784,-5.23363177905661,-93.9999999999991));
#367=IFCCARTESIANPOINT((46.7052656338443,1.63611866403177,-93.9999999999991));
#368=IFCFACE((#369));
#369=IFCFACEOUTERBOUND(#370,.T.);
#370=IFCPOLYLOOP((#371,#372,#373));
#371=IFCCARTESIANPOINT((54.1903326227934,-147.658103330519,0.));
#372=IFCCARTESIANPOINT((2.83012817778957,-7.98927385674093,-93.9999999999991));
#373=IFCCARTESIANPOINT((25.2161298976784,-5.23363177905661,-93.9999999999991));
#374=IFCFACE((#375));
#375=IFCFACEOUTERBOUND(#376,.T.);
#376=IFCPOLYLOOP((#377,#378,#379));
#377=IFCCARTESIANPOINT((13.8710862775596,-153.121660363236,0.));
#378=IFCCARTESIANPOINT((-19.662998563232,-6.32985285628291,-93.9999999999991));
#379=IFCCARTESIANPOINT((2.83012817778957,-7.98927385674093,-93.9999999999991));
#380=IFCFACE((#381));
#381=IFCFACEOUTERBOUND(#382,.T.);
#382=IFCPOLYLOOP((#383,#384,#385));
#383=IFCCARTESIANPOINT((-26.8019125425913,-152.078206375312,0.));
#384=IFCCARTESIANPOINT((-41.4394014166823,-0.440153063918815,-93.9999999999991));
#385=IFCCARTESIANPOINT((-19.662998563232,-6.32985285628291,-93.9999999999991));
#386=IFCFACE((#387));
#387=IFCFACEOUTERBOUND(#388,.T.);
#388=IFCPOLYLOOP((#389,#390,#391));
#389=IFCCARTESIANPOINT((-66.7978479682479,-144.600522500239,0.));
#390=IFCCARTESIANPOINT((-61.8871867593298,9.10501350136987,-93.9999999999991));
#391=IFCCARTESIANPOINT((-41.4394014166823,-0.440153063918815,-93.9999999999991));
#392=IFCFACE((#393));
#393=IFCFACEOUTERBOUND(#394,.T.);
#394=IFCPOLYLOOP((#395,#396,#397));
#395=IFCCARTESIANPOINT((-105.215114341225,-131.178510124149,0.));
#396=IFCCARTESIANPOINT((-80.698001162248,21.582584637739,-93.9999999999991));
#397=IFCCARTESIANPOINT((-61.8871867593298,9.10501350136987,-93.9999999999991));
#398=IFCFACE((#399));
#399=IFCFACEOUTERBOUND(#400,.T.);
#400=IFCPOLYLOOP((#401,#402,#403));
#401=IFCCARTESIANPOINT((-141.412934526735,-112.569598180375,0.));
#402=IFCCARTESIANPOINT((-97.8447661146846,36.2725918775068,-93.9999999999991));
#403=IFCCARTESIANPOINT((-80.698001162248,21.582584637739,-93.9999999999991));
#404=IFCFACE((#405));
#405=IFCFACEOUTERBOUND(#406,.T.);
#406=IFCPOLYLOOP((#407,#408,#409));
#407=IFCCARTESIANPOINT((-175.071534188435,-89.671802213621,0.));
#408=IFCCARTESIANPOINT((-113.387459490709,52.6538727330131,-93.9999999999991));
#409=IFCCARTESIANPOINT((-97.8447661146846,36.2725918775068,-93.9999999999991));
#410=IFCFACE((#411));
#411=IFCFACEOUTERBOUND(#412,.T.);
#412=IFCPOLYLOOP((#413,#414,#415));
#413=IFCCARTESIANPOINT((-206.125816386308,-63.3430855287523,0.));
#414=IFCCARTESIANPOINT((-127.294335499358,70.445523587566,-93.9999999999991));
#415=IFCCARTESIANPOINT((-113.387459490709,52.6538727330131,-93.9999999999991));
#416=IFCFACE((#417));
#417=IFCFACEOUTERBOUND(#418,.T.);
#418=IFCPOLYLOOP((#419,#420,#421));
#419=IFCCARTESIANPOINT((-234.428168068557,-34.0753473673235,0.));
#420=IFCCARTESIANPOINT((-139.405144871723,89.5038839334345,-93.9999999999991));
#421=IFCCARTESIANPOINT((-127.294335499358,70.445523587566,-93.9999999999991));
#422=IFCFACE((#423));
#423=IFCFACEOUTERBOUND(#424,.T.);
#424=IFCPOLYLOOP((#425,#426,#427));
#425=IFCCARTESIANPOINT((-259.608278768049,-2.08529338102262,0.));
#426=IFCCARTESIANPOINT((-149.376131896764,109.760102145713,-93.9999999999991));
#427=IFCCARTESIANPOINT((-139.405144871723,89.5038839334345,-93.9999999999991));
#428=IFCFACE((#429));
#429=IFCFACEOUTERBOUND(#430,.T.);
#430=IFCPOLYLOOP((#431,#432,#433));
#431=IFCCARTESIANPOINT((-280.922701731312,32.5907488402735,0.));
#432=IFCCARTESIANPOINT((-156.563435238971,131.153861277019,-93.9999999999991));
#433=IFCCARTESIANPOINT((-149.376131896764,109.760102145713,-93.9999999999991));
#434=IFCFACE((#435));
#435=IFCFACEOUTERBOUND(#436,.T.);
#436=IFCPOLYLOOP((#437,#438,#439));
#437=IFCCARTESIANPOINT((-296.940075211924,69.9862810835965,0.));
#438=IFCCARTESIANPOINT((-159.799786377107,153.46782071584,-93.9999999999991));
#439=IFCCARTESIANPOINT((-156.563435238971,131.153861277019,-93.9999999999991));
#440=IFCFACE((#441));
#441=IFCFACEOUTERBOUND(#442,.T.);
#442=IFCPOLYLOOP((#443,#444,#445));
#443=IFCCARTESIANPOINT((-304.891071307496,109.822256120206,0.));
#444=IFCCARTESIANPOINT((-157.154914340418,175.808617178122,-93.9999999999991));
#445=IFCCARTESIANPOINT((-159.799786377107,153.46782071584,-93.9999999999991));
#446=IFCFACE((#447));
#447=IFCFACEOUTERBOUND(#448,.T.);
#448=IFCPOLYLOOP((#449,#450,#451));
#449=IFCCARTESIANPOINT((-300.,150.,0.));
#450=IFCCARTESIANPOINT((-279.319862190373,184.775801315119,0.));
#451=IFCCARTESIANPOINT((-147.061746316906,195.888143420344,-93.9999999999991));
#452=IFCFACE((#453));
#453=IFCFACEOUTERBOUND(#454,.T.);
#454=IFCPOLYLOOP((#455,#456,#457));
#455=IFCCARTESIANPOINT((-279.319862190373,184.775801315119,0.));
#456=IFCCARTESIANPOINT((-248.405687646327,211.118276332925,0.));
#457=IFCCARTESIANPOINT((-131.264894314891,211.943402767825,-93.9999999999991));
#458=IFCFACE((#459));
#459=IFCFACEOUTERBOUND(#460,.T.);
#460=IFCPOLYLOOP((#461,#462,#463));
#461=IFCCARTESIANPOINT((-248.405687646327,211.118276332925,0.));
#462=IFCCARTESIANPOINT((-212.631973130034,230.493081814022,0.));
#463=IFCCARTESIANPOINT((-112.267364103545,224.107056891456,-93.9999999999991));
#464=IFCFACE((#465));
#465=IFCFACEOUTERBOUND(#466,.T.);
#466=IFCPOLYLOOP((#467,#468,#469));
#467=IFCCARTESIANPOINT((-212.631973130034,230.493081814022,0.));
#468=IFCCARTESIANPOINT((-174.498313981775,244.738726835566,0.));
#469=IFCCARTESIANPOINT((-91.576683901205,233.132310979361,-93.9999999999991));
#470=IFCFACE((#471));
#471=IFCFACEOUTERBOUND(#472,.T.);
#472=IFCPOLYLOOP((#473,#474,#475));
#473=IFCCARTESIANPOINT((-174.498313981775,244.738726835566,0.));
#474=IFCCARTESIANPOINT((-135.115612880362,255.077835581894,0.));
#475=IFCCARTESIANPOINT((-69.9604240039862,239.659498513825,-93.9999999999991));
#476=IFCFACE((#477));
#477=IFCFACEOUTERBOUND(#478,.T.);
#478=IFCPOLYLOOP((#479,#480,#481));
#479=IFCCARTESIANPOINT((-135.115612880362,255.077835581894,0.));
#480=IFCCARTESIANPOINT((-95.0316202590492,262.255497380798,0.));
#481=IFCCARTESIANPOINT((-47.8217104720131,244.119106659353,-93.9999999999991));
#482=IFCFACE((#483));
#483=IFCFACEOUTERBOUND(#484,.T.);
#484=IFCPOLYLOOP((#485,#486,#487));
#485=IFCCARTESIANPOINT((-95.0316202590492,262.255497380798,0.));
#486=IFCCARTESIANPOINT((-54.5530293999268,266.715969920949,0.));
#487=IFCCARTESIANPOINT((-25.3937160660832,246.777117598331,-93.9999999999991));
#488=IFCFACE((#489));
#489=IFCFACEOUTERBOUND(#490,.T.);
#490=IFCPOLYLOOP((#491,#492,#493));
#491=IFCCARTESIANPOINT((-54.5530293999268,266.715969920949,0.));
#492=IFCCARTESIANPOINT((-13.8771780303431,268.706812331913,0.));
#493=IFCCARTESIANPOINT((-2.83037829161053,247.779902642456,-93.9999999999991));
#494=IFCFACE((#495));
#495=IFCFACEOUTERBOUND(#496,.T.);
#496=IFCPOLYLOOP((#497,#498,#499));
#497=IFCCARTESIANPOINT((-13.8771780303431,268.706812331913,0.));
#498=IFCCARTESIANPOINT((26.8458427708863,268.331827033896,0.));
#499=IFCCARTESIANPOINT((19.7473348710258,247.180277362528,-93.9999999999991));
#500=IFCFACE((#501));
#501=IFCFACEOUTERBOUND(#502,.T.);
#502=IFCPOLYLOOP((#503,#504,#505));
#503=IFCCARTESIANPOINT((26.8458427708863,268.331827033896,0.));
#504=IFCCARTESIANPOINT((67.4765332607867,265.571769764524,0.));
#505=IFCCARTESIANPOINT((42.2218098296953,244.946818741202,-93.9999999999991));
#506=IFCFACE((#507));
#507=IFCFACEOUTERBOUND(#508,.T.);
#508=IFCPOLYLOOP((#509,#510,#511));
#509=IFCCARTESIANPOINT((67.4765332607867,265.571769764524,0.));
#510=IFCCARTESIANPOINT((107.854433144861,260.280582237886,0.));
#511=IFCCARTESIANPOINT((64.4507343905464,240.958334023702,-93.9999999999991));
#512=IFCFACE((#513));
#513=IFCFACEOUTERBOUND(#514,.T.);
#514=IFCPOLYLOOP((#515,#516,#517));
#515=IFCCARTESIANPOINT((107.854433144861,260.280582237886,0.));
#516=IFCCARTESIANPOINT((147.756154156487,252.155742473922,0.));
#517=IFCCARTESIANPOINT((86.2268790106761,234.98230335346,-93.9999999999991));
#518=IFCFACE((#519));
#519=IFCFACEOUTERBOUND(#520,.T.);
#520=IFCPOLYLOOP((#521,#522,#523));
#521=IFCCARTESIANPOINT((147.756154156487,252.155742473922,0.));
#522=IFCCARTESIANPOINT((186.818017180943,240.67163971588,0.));
#523=IFCCARTESIANPOINT((107.202467334812,226.634520930962,-93.9999999999991));
#524=IFCFACE((#525));
#525=IFCFACEOUTERBOUND(#526,.T.);
#526=IFCPOLYLOOP((#527,#528,#529));
#527=IFCCARTESIANPOINT((186.818017180943,240.67163971588,0.));
#528=IFCCARTESIANPOINT((224.363000998313,224.953681400811,0.));
#529=IFCCARTESIANPOINT((126.728646947386,215.328663366711,-93.9999999999991));
#530=IFCFACE((#531));
#531=IFCFACEOUTERBOUND(#532,.T.);
#532=IFCPOLYLOOP((#533,#534,#535));
#533=IFCCARTESIANPOINT((224.363000998313,224.953681400811,0.));
#534=IFCCARTESIANPOINT((258.965081713391,203.584218912768,0.));
#535=IFCCARTESIANPOINT((143.527109004701,200.308104946409,-93.9999999999991));
#536=IFCFACE((#537));
#537=IFCFACEOUTERBOUND(#538,.T.);
#538=IFCPOLYLOOP((#539,#540,#541));
#539=IFCCARTESIANPOINT((258.965081713391,203.584218912768,0.));
#540=IFCCARTESIANPOINT((287.369755242598,174.609406522514,0.));
#541=IFCCARTESIANPOINT((155.311843754385,181.158856579337,-93.9999999999991));
#542=IFCFACE((#543));
#543=IFCFACEOUTERBOUND(#544,.T.);
#544=IFCPOLYLOOP((#545,#546,#547));
#545=IFCCARTESIANPOINT((287.369755242598,174.609406522514,0.));
#546=IFCCARTESIANPOINT((303.288957242437,137.460278743209,0.));
#547=IFCCARTESIANPOINT((159.780564770642,159.127525554679,-93.9999999999991));
#548=IFCFACE((#549));
#549=IFCFACEOUTERBOUND(#550,.T.);
#550=IFCPOLYLOOP((#551,#552,#553));
#551=IFCCARTESIANPOINT((303.288957242437,137.460278743209,0.));
#552=IFCCARTESIANPOINT((303.485368351088,96.9291090994897,0.));
#553=IFCCARTESIANPOINT((157.807019966901,136.675894195075,-93.9999999999991));
#554=IFCFACE((#555));
#555=IFCFACEOUTERBOUND(#556,.T.);
#556=IFCPOLYLOOP((#557,#558,#559));
#557=IFCCARTESIANPOINT((303.485368351088,96.9291090994897,0.));
#558=IFCCARTESIANPOINT((292.544561947164,57.7814080324017,0.));
#559=IFCCARTESIANPOINT((151.473017550452,115.017962619083,-93.9999999999991));
#560=IFCFACE((#561));
#561=IFCFACEOUTERBOUND(#562,.T.);
#562=IFCPOLYLOOP((#563,#564,#565));
#563=IFCCARTESIANPOINT((292.544561947164,57.7814080324017,0.));
#564=IFCCARTESIANPOINT((274.62087031373,21.2509318856294,0.));
#565=IFCCARTESIANPOINT((142.123444996071,94.4691487187488,-93.9999999999991));
#566=IFCFACE((#567));
#567=IFCFACEOUTERBOUND(#568,.T.);
#568=IFCPOLYLOOP((#569,#570,#571));
#569=IFCCARTESIANPOINT((274.62087031373,21.2509318856294,0.));
#570=IFCCARTESIANPOINT((251.961645534009,-12.5658965535741,0.));
#571=IFCCARTESIANPOINT((130.507477385555,75.1059075118196,-93.9999999999991));
#572=IFCFACE((#573));
#573=IFCFACEOUTERBOUND(#574,.T.);
#574=IFCPOLYLOOP((#575,#576,#577));
#575=IFCCARTESIANPOINT((251.961645534009,-12.5658965535741,0.));
#576=IFCCARTESIANPOINT((225.728273711876,-43.6997637700286,0.));
#577=IFCCARTESIANPOINT((117.029708390029,56.9871779332136,-93.9999999999991));
#578=IFCFACE((#579));
#579=IFCFACEOUTERBOUND(#580,.T.);
#580=IFCPOLYLOOP((#581,#582,#583));
#581=IFCCARTESIANPOINT((225.728273711876,-43.6997637700286,0.));
#582=IFCCARTESIANPOINT((196.521914802755,-72.0656434367093,0.));
#583=IFCCARTESIANPOINT((101.889510015946,40.2328202653944,-93.9999999999991));
#584=IFCFACE((#585));
#585=IFCFACEOUTERBOUND(#586,.T.);
#586=IFCPOLYLOOP((#587,#588,#589));
#587=IFCCARTESIANPOINT((196.521914802755,-72.0656434367093,0.));
#588=IFCCARTESIANPOINT((164.629900262907,-97.37165169902,0.));
#589=IFCCARTESIANPOINT((85.1475914236449,25.0817028555501,-93.9999999999991));
#590=IFCFACE((#591));
#591=IFCFACEOUTERBOUND(#592,.T.);
#592=IFCPOLYLOOP((#593,#594,#595));
#593=IFCCARTESIANPOINT((164.629900262907,-97.37165169902,0.));
#594=IFCCARTESIANPOINT((130.150407237097,-119.008883827061,0.));
#595=IFCCARTESIANPOINT((66.7603568037461,11.9848882796374,-93.9999999999991));
#596=IFCFACE((#597));
#597=IFCFACEOUTERBOUND(#598,.T.);
#598=IFCPOLYLOOP((#599,#600,#601));
#599=IFCCARTESIANPOINT((130.150407237097,-119.008883827061,0.));
#600=IFCCARTESIANPOINT((93.1938862047157,-136.056391059388,0.));
#601=IFCCARTESIANPOINT((46.7052656338443,1.63611866403177,-93.9999999999991));
#602=IFCFACE((#603));
#603=IFCFACEOUTERBOUND(#604,.T.);
#604=IFCPOLYLOOP((#605,#606,#607));
#605=IFCCARTESIANPOINT((93.1938862047157,-136.056391059388,0.));
#606=IFCCARTESIANPOINT((54.1903326227934,-147.658103330519,0.));
#607=IFCCARTESIANPOINT((25.2161298976784,-5.23363177905661,-93.9999999999991));
#608=IFCFACE((#609));
#609=IFCFACEOUTERBOUND(#610,.T.);
#610=IFCPOLYLOOP((#611,#612,#613));
#611=IFCCARTESIANPOINT((54.1903326227934,-147.658103330519,0.));
#612=IFCCARTESIANPOINT((13.8710862775596,-153.121660363236,0.));
#613=IFCCARTESIANPOINT((2.83012817778957,-7.98927385674093,-93.9999999999991));
#614=IFCFACE((#615));
#615=IFCFACEOUTERBOUND(#616,.T.);
#616=IFCPOLYLOOP((#617,#618,#619));
#617=IFCCARTESIANPOINT((13.8710862775596,-153.121660363236,0.));
#618=IFCCARTESIANPOINT((-26.8019125425913,-152.078206375312,0.));
#619=IFCCARTESIANPOINT((-19.662998563232,-6.32985285628291,-93.9999999999991));
#620=IFCFACE((#621));
#621=IFCFACEOUTERBOUND(#622,.T.);
#622=IFCPOLYLOOP((#623,#624,#625));
#623=IFCCARTESIANPOINT((-26.8019125425913,-152.078206375312,0.));
#624=IFCCARTESIANPOINT((-66.7978479682479,-144.600522500239,0.));
#625=IFCCARTESIANPOINT((-41.4394014166823,-0.440153063918815,-93.9999999999991));
#626=IFCFACE((#627));
#627=IFCFACEOUTERBOUND(#628,.T.);
#628=IFCPOLYLOOP((#629,#630,#631));
#629=IFCCARTESIANPOINT((-66.7978479682479,-144.600522500239,0.));
#630=IFCCARTESIANPOINT((-105.215114341225,-131.178510124149,0.));
#631=IFCCARTESIANPOINT((-61.8871867593298,9.10501350136987,-93.9999999999991));
#632=IFCFACE((#633));
#633=IFCFACEOUTERBOUND(#634,.T.);
#634=IFCPOLYLOOP((#635,#636,#637));
#635=IFCCARTESIANPOINT((-105.215114341225,-131.178510124149,0.));
#636=IFCCARTESIANPOINT((-141.412934526735,-112.569598180375,0.));
#637=IFCCARTESIANPOINT((-80.698001162248,21.582584637739,-93.9999999999991));
#638=IFCFACE((#639));
#639=IFCFACEOUTERBOUND(#640,.T.);
#640=IFCPOLYLOOP((#641,#642,#643));
#641=IFCCARTESIANPOINT((-141.412934526735,-112.569598180375,0.));
#642=IFCCARTESIANPOINT((-175.071534188435,-89.671802213621,0.));
#643=IFCCARTESIANPOINT((-97.8447661146846,36.2725918775068,-93.9999999999991));
#644=IFCFACE((#645));
#645=IFCFACEOUTERBOUND(#646,.T.);
#646=IFCPOLYLOOP((#647,#648,#649));
#647=IFCCARTESIANPOINT((-175.071534188435,-89.671802213621,0.));
#648=IFCCARTESIANPOINT((-206.125816386308,-63.3430855287523,0.));
#649=IFCCARTESIANPOINT((-113.387459490709,52.6538727330131,-93.9999999999991));
#650=IFCFACE((#651));
#651=IFCFACEOUTERBOUND(#652,.T.);
#652=IFCPOLYLOOP((#653,#654,#655));
#653=IFCCARTESIANPOINT((-206.125816386308,-63.3430855287523,0.));
#654=IFCCARTESIANPOINT((-234.428168068557,-34.0753473673235,0.));
#655=IFCCARTESIANPOINT((-127.294335499358,70.445523587566,-93.9999999999991));
#656=IFCFACE((#657));
#657=IFCFACEOUTERBOUND(#658,.T.);
#658=IFCPOLYLOOP((#659,#660,#661));
#659=IFCCARTESIANPOINT((-234.428168068557,-34.0753473673235,0.));
#660=IFCCARTESIANPOINT((-259.608278768049,-2.08529338102262,0.));
#661=IFCCARTESIANPOINT((-139.405144871723,89.5038839334345,-93.9999999999991));
#662=IFCFACE((#663));
#663=IFCFACEOUTERBOUND(#664,.T.);
#664=IFCPOLYLOOP((#665,#666,#667));
#665=IFCCARTESIANPOINT((-259.608278768049,-2.08529338102262,0.));
#666=IFCCARTESIANPOINT((-280.922701731312,32.5907488402735,0.));
#667=IFCCARTESIANPOINT((-149.376131896764,109.760102145713,-93.9999999999991));
#668=IFCFACE((#669));
#669=IFCFACEOUTERBOUND(#670,.T.);
#670=IFCPOLYLOOP((#671,#672,#673));
#671=IFCCARTESIANPOINT((-280.922701731312,32.5907488402735,0.));
#672=IFCCARTESIANPOINT((-296.940075211924,69.9862810835965,0.));
#673=IFCCARTESIANPOINT((-156.563435238971,131.153861277019,-93.9999999999991));
#674=IFCFACE((#675));
#675=IFCFACEOUTERBOUND(#676,.T.);
#676=IFCPOLYLOOP((#677,#678,#679));
#677=IFCCARTESIANPOINT((-296.940075211924,69.9862810835965,0.));
#678=IFCCARTESIANPOINT((-304.891071307496,109.822256120206,0.));
#679=IFCCARTESIANPOINT((-159.799786377107,153.46782071584,-93.9999999999991));
#680=IFCFACE((#681));
#681=IFCFACEOUTERBOUND(#682,.T.);
#682=IFCPOLYLOOP((#683,#684,#685));
#683=IFCCARTESIANPOINT((-304.891071307496,109.822256120206,0.));
#684=IFCCARTESIANPOINT((-300.,150.,0.));
#685=IFCCARTESIANPOINT((-157.154914340418,175.808617178122,-93.9999999999991));
#686=IFCFACE((#687));
#687=IFCFACEOUTERBOUND(#688,.T.);
#688=IFCPOLYLOOP((#689,#690,#691));
#689=IFCCARTESIANPOINT((0.,253.099263998677,0.));
#690=IFCCARTESIANPOINT((22.5714426167356,246.991542511648,-83.9999999999991));
#691=IFCCARTESIANPOINT((0.,247.792422124388,-83.9999999999991));
#692=IFCFACE((#693));
#693=IFCFACEOUTERBOUND(#694,.T.);
#694=IFCPOLYLOOP((#695,#696,#697));
#695=IFCCARTESIANPOINT((38.4756983219429,252.033478287557,0.));
#696=IFCCARTESIANPOINT((45.0238122743352,244.546841305597,-83.9999999999991));
#697=IFCCARTESIANPOINT((22.5714426167356,246.991542511648,-83.9999999999991));
#698=IFCFACE((#699));
#699=IFCFACEOUTERBOUND(#700,.T.);
#700=IFCPOLYLOOP((#701,#702,#703));
#701=IFCCARTESIANPOINT((76.82797079471,248.781505427916,0.));
#702=IFCCARTESIANPOINT((67.2093069026739,240.324720559443,-83.9999999999991));
#703=IFCCARTESIANPOINT((45.0238122743352,244.546841305597,-83.9999999999991));
#704=IFCFACE((#705));
#705=IFCFACEOUTERBOUND(#706,.T.);
#706=IFCPOLYLOOP((#707,#708,#709));
#707=IFCCARTESIANPOINT((114.905040261981,243.168611097888,0.));
#708=IFCCARTESIANPOINT((88.90833880495,234.07626833846,-83.9999999999991));
#709=IFCCARTESIANPOINT((67.2093069026739,240.324720559443,-83.9999999999991));
#710=IFCFACE((#711));
#711=IFCFACEOUTERBOUND(#712,.T.);
#712=IFCPOLYLOOP((#713,#714,#715));
#713=IFCCARTESIANPOINT((152.483825019629,234.862972697005,0.));
#714=IFCCARTESIANPOINT((109.746785929842,225.394568694178,-83.9999999999991));
#715=IFCCARTESIANPOINT((88.90833880495,234.07626833846,-83.9999999999991));
#716=IFCFACE((#717));
#717=IFCFACEOUTERBOUND(#718,.T.);
#718=IFCPOLYLOOP((#719,#720,#721));
#719=IFCCARTESIANPOINT((189.184260308893,223.297735395069,0.));
#720=IFCCARTESIANPOINT((129.019208284081,213.666097201998,-83.9999999999991));
#721=IFCCARTESIANPOINT((109.746785929842,225.394568694178,-83.9999999999991));
#722=IFCFACE((#723));
#723=IFCFACEOUTERBOUND(#724,.T.);
#724=IFCPOLYLOOP((#725,#726,#727));
#725=IFCCARTESIANPOINT((224.265826794532,207.523990642274,0.));
#726=IFCCARTESIANPOINT((145.336254078924,198.131563599338,-83.9999999999991));
#727=IFCCARTESIANPOINT((129.019208284081,213.666097201998,-83.9999999999991));
#728=IFCFACE((#729));
#729=IFCFACEOUTERBOUND(#730,.T.);
#730=IFCPOLYLOOP((#731,#732,#733));
#731=IFCCARTESIANPOINT((256.087669717028,185.991172830599,0.));
#732=IFCCARTESIANPOINT((156.295574340587,178.505166588871,-83.9999999999991));
#733=IFCCARTESIANPOINT((145.336254078924,198.131563599338,-83.9999999999991));
#734=IFCFACE((#735));
#735=IFCFACEOUTERBOUND(#736,.T.);
#736=IFCPOLYLOOP((#737,#738,#739));
#737=IFCCARTESIANPOINT((280.765071736094,156.699063336262,0.));
#738=IFCCARTESIANPOINT((159.838406320187,156.297847499286,-83.9999999999991));
#739=IFCCARTESIANPOINT((156.295574340587,178.505166588871,-83.9999999999991));
#740=IFCFACE((#741));
#741=IFCFACEOUTERBOUND(#742,.T.);
#742=IFCPOLYLOOP((#743,#744,#745));
#743=IFCCARTESIANPOINT((291.585141614082,120.093338245881,0.));
#744=IFCCARTESIANPOINT((157.217230633118,133.907671395464,-83.9999999999991));
#745=IFCCARTESIANPOINT((159.838406320187,156.297847499286,-83.9999999999991));
#746=IFCFACE((#747));
#747=IFCFACEOUTERBOUND(#748,.T.);
#748=IFCPOLYLOOP((#749,#750,#751));
#749=IFCCARTESIANPOINT((287.786485451514,81.9448662146907,0.));
#750=IFCCARTESIANPOINT((150.44694977305,112.380110135675,-83.9999999999991));
#751=IFCCARTESIANPOINT((157.217230633118,133.907671395464,-83.9999999999991));
#752=IFCFACE((#753));
#753=IFCFACEOUTERBOUND(#754,.T.);
#754=IFCPOLYLOOP((#755,#756,#757));
#755=IFCCARTESIANPOINT((274.528157897687,45.8738463140147,0.));
#756=IFCCARTESIANPOINT((140.781372023645,91.9771690603084,-83.9999999999991));
#757=IFCCARTESIANPOINT((150.44694977305,112.380110135675,-83.9999999999991));
#758=IFCFACE((#759));
#759=IFCFACEOUTERBOUND(#760,.T.);
#760=IFCPOLYLOOP((#761,#762,#763));
#761=IFCCARTESIANPOINT((255.376512280737,12.5180862102588,0.));
#762=IFCCARTESIANPOINT((128.915131219959,72.7659087899957,-83.9999999999991));
#763=IFCCARTESIANPOINT((140.781372023645,91.9771690603084,-83.9999999999991));
#764=IFCFACE((#765));
#765=IFCFACEOUTERBOUND(#766,.T.);
#766=IFCPOLYLOOP((#767,#768,#769));
#767=IFCCARTESIANPOINT((232.162082007755,-18.1641105843033,0.));
#768=IFCCARTESIANPOINT((115.221437914664,54.8097236343718,-83.9999999999991));
#769=IFCCARTESIANPOINT((128.915131219959,72.7659087899957,-83.9999999999991));
#770=IFCFACE((#771));
#771=IFCFACEOUTERBOUND(#772,.T.);
#772=IFCPOLYLOOP((#773,#774,#775));
#773=IFCCARTESIANPOINT((205.82058249047,-46.2135089189517,0.));
#774=IFCCARTESIANPOINT((99.8796474348905,38.2399303092091,-83.9999999999991));
#775=IFCCARTESIANPOINT((115.221437914664,54.8097236343718,-83.9999999999991));
#776=IFCFACE((#777));
#777=IFCFACEOUTERBOUND(#778,.T.);
#778=IFCPOLYLOOP((#779,#780,#781));
#779=IFCCARTESIANPOINT((176.831543285532,-71.5181976728152,0.));
#780=IFCCARTESIANPOINT((82.9357630974296,23.3156482145766,-83.9999999999991));
#781=IFCCARTESIANPOINT((99.8796474348905,38.2399303092091,-83.9999999999991));
#782=IFCFACE((#783));
#783=IFCFACEOUTERBOUND(#784,.T.);
#784=IFCPOLYLOOP((#785,#786,#787));
#785=IFCCARTESIANPOINT((145.419108882987,-93.7387882260769,0.));
#786=IFCCARTESIANPOINT((64.3369357795818,10.5226661545967,-83.9999999999991));
#787=IFCCARTESIANPOINT((82.9357630974296,23.3156482145766,-83.9999999999991));
#788=IFCFACE((#789));
#789=IFCFACEOUTERBOUND(#790,.T.);
#790=IFCPOLYLOOP((#791,#792,#793));
#791=IFCCARTESIANPOINT((111.672515835794,-112.209605260782,0.));
#792=IFCCARTESIANPOINT((44.0834024872804,0.569911206685156,-83.9999999999991));
#793=IFCCARTESIANPOINT((64.3369357795818,10.5226661545967,-83.9999999999991));
#794=IFCFACE((#795));
#795=IFCFACEOUTERBOUND(#796,.T.);
#796=IFCPOLYLOOP((#797,#798,#799));
#797=IFCCARTESIANPOINT((75.812198248967,-126.124204222316,0.));
#798=IFCCARTESIANPOINT((22.446084481302,-5.81475772180151,-83.9999999999991));
#799=IFCCARTESIANPOINT((44.0834024872804,0.569911206685156,-83.9999999999991));
#800=IFCFACE((#801));
#801=IFCFACEOUTERBOUND(#802,.T.);
#802=IFCPOLYLOOP((#803,#804,#805));
#803=IFCCARTESIANPOINT((38.3440539351622,-134.804771445296,0.));
#804=IFCCARTESIANPOINT((1.76219393597E-06,-8.0243005407274,-83.9999999999991));
#805=IFCCARTESIANPOINT((22.446084481302,-5.81475772180151,-83.9999999999991));
#806=IFCFACE((#807));
#807=IFCFACEOUTERBOUND(#808,.T.);
#808=IFCPOLYLOOP((#809,#810,#811));
#809=IFCCARTESIANPOINT((5.62489231925E-06,-137.758996454453,0.));
#810=IFCCARTESIANPOINT((-22.4460832273862,-5.81475796938246,-83.9999999999991));
#811=IFCCARTESIANPOINT((1.76219393597E-06,-8.0243005407274,-83.9999999999991));
#812=IFCFACE((#813));
#813=IFCFACEOUTERBOUND(#814,.T.);
#814=IFCPOLYLOOP((#815,#816,#817));
#815=IFCCARTESIANPOINT((-38.3440494938959,-134.804772131387,0.));
#816=IFCCARTESIANPOINT((-44.0834019165823,0.569910981625322,-83.9999999999991));
#817=IFCCARTESIANPOINT((-22.4460832273862,-5.81475796938246,-83.9999999999991));
#818=IFCFACE((#819));
#819=IFCFACEOUTERBOUND(#820,.T.);
#820=IFCPOLYLOOP((#821,#822,#823));
#821=IFCCARTESIANPOINT((-75.8121974100292,-126.124204482417,0.));
#822=IFCCARTESIANPOINT((-64.3369337417203,10.5226649501159,-83.9999999999991));
#823=IFCCARTESIANPOINT((-44.0834019165823,0.569910981625322,-83.9999999999991));
#824=IFCFACE((#825));
#825=IFCFACEOUTERBOUND(#826,.T.);
#826=IFCPOLYLOOP((#827,#828,#829));
#827=IFCCARTESIANPOINT((-111.672515690142,-112.209605328934,0.));
#828=IFCCARTESIANPOINT((-82.9357654214438,23.3156500424069,-83.9999999999991));
#829=IFCCARTESIANPOINT((-64.3369337417203,10.5226649501159,-83.9999999999991));
#830=IFCFACE((#831));
#831=IFCFACEOUTERBOUND(#832,.T.);
#832=IFCPOLYLOOP((#833,#834,#835));
#833=IFCCARTESIANPOINT((-145.419108622881,-93.7387883895915,0.));
#834=IFCCARTESIANPOINT((-99.8796470071603,38.239929890392,-83.9999999999991));
#835=IFCCARTESIANPOINT((-82.9357654214438,23.3156500424069,-83.9999999999991));
#836=IFCFACE((#837));
#837=IFCFACEOUTERBOUND(#838,.T.);
#838=IFCPOLYLOOP((#839,#840,#841));
#839=IFCCARTESIANPOINT((-176.831543103321,-71.5181978165611,0.));
#840=IFCCARTESIANPOINT((-115.221432502533,54.8097171953257,-83.9999999999991));
#841=IFCCARTESIANPOINT((-99.8796470071603,38.239929890392,-83.9999999999991));
#842=IFCFACE((#843));
#843=IFCFACEOUTERBOUND(#844,.T.);
#844=IFCPOLYLOOP((#845,#846,#847));
#845=IFCCARTESIANPOINT((-205.820584751455,-46.2135067401479,0.));
#846=IFCCARTESIANPOINT((-128.915130782292,72.7659081552236,-83.9999999999991));
#847=IFCCARTESIANPOINT((-115.221432502533,54.8097171953257,-83.9999999999991));
#848=IFCFACE((#849));
#849=IFCFACEOUTERBOUND(#850,.T.);
#850=IFCPOLYLOOP((#851,#852,#853));
#851=IFCCARTESIANPOINT((-232.162080231681,-18.164112680286,0.));
#852=IFCCARTESIANPOINT((-140.781370993691,91.9771671791993,-83.9999999999991));
#853=IFCCARTESIANPOINT((-128.915130782292,72.7659081552236,-83.9999999999991));
#854=IFCFACE((#855));
#855=IFCFACEOUTERBOUND(#856,.T.);
#856=IFCPOLYLOOP((#857,#858,#859));
#857=IFCCARTESIANPOINT((-255.37651255073,12.5180866141667,0.));
#858=IFCCARTESIANPOINT((-150.446948503868,112.380106954109,-83.9999999999991));
#859=IFCCARTESIANPOINT((-140.781370993691,91.9771671791993,-83.9999999999991));
#860=IFCFACE((#861));
#861=IFCFACEOUTERBOUND(#862,.T.);
#862=IFCPOLYLOOP((#863,#864,#865));
#863=IFCCARTESIANPOINT((-274.528157992219,45.8738465115148,0.));
#864=IFCCARTESIANPOINT((-157.217231378449,133.907674702395,-83.9999999999991));
#865=IFCCARTESIANPOINT((-150.446948503868,112.380106954109,-83.9999999999991));
#866=IFCFACE((#867));
#867=IFCFACEOUTERBOUND(#868,.T.);
#868=IFCPOLYLOOP((#869,#870,#871));
#869=IFCCARTESIANPOINT((-287.786482832935,81.9448557189429,0.));
#870=IFCCARTESIANPOINT((-159.838406313243,156.297849730896,-83.9999999999991));
#871=IFCCARTESIANPOINT((-157.217231378449,133.907674702395,-83.9999999999991));
#872=IFCFACE((#873));
#873=IFCFACEOUTERBOUND(#874,.T.);
#874=IFCPOLYLOOP((#875,#876,#877));
#875=IFCCARTESIANPOINT((-291.585141527273,120.093339480335,0.));
#876=IFCCARTESIANPOINT((-156.295574267743,178.505166800292,-83.9999999999991));
#877=IFCCARTESIANPOINT((-159.838406313243,156.297849730896,-83.9999999999991));
#878=IFCFACE((#879));
#879=IFCFACEOUTERBOUND(#880,.T.);
#880=IFCPOLYLOOP((#881,#882,#883));
#881=IFCCARTESIANPOINT((-280.765069841138,156.69906670217,0.));
#882=IFCCARTESIANPOINT((-145.336255413146,198.131561931792,-83.9999999999991));
#883=IFCCARTESIANPOINT((-156.295574267743,178.505166800292,-83.9999999999991));
#884=IFCFACE((#885));
#885=IFCFACEOUTERBOUND(#886,.T.);
#886=IFCPOLYLOOP((#887,#888,#889));
#887=IFCCARTESIANPOINT((-256.087676107765,185.991167334279,0.));
#888=IFCCARTESIANPOINT((-129.019214178155,213.666092805292,-83.9999999999991));
#889=IFCCARTESIANPOINT((-145.336255413146,198.131561931792,-83.9999999999991));
#890=IFCFACE((#891));
#891=IFCFACEOUTERBOUND(#892,.T.);
#892=IFCPOLYLOOP((#893,#894,#895));
#893=IFCCARTESIANPOINT((-224.265825523088,207.523991330957,0.));
#894=IFCCARTESIANPOINT((-109.746783760019,225.394569776618,-83.9999999999991));
#895=IFCCARTESIANPOINT((-129.019214178155,213.666092805292,-83.9999999999991));
#896=IFCFACE((#897));
#897=IFCFACEOUTERBOUND(#898,.T.);
#898=IFCPOLYLOOP((#899,#900,#901));
#899=IFCCARTESIANPOINT((-189.184272565133,223.297730817421,0.));
#900=IFCCARTESIANPOINT((-88.9083392880943,234.076268171425,-83.9999999999991));
#901=IFCCARTESIANPOINT((-109.746783760019,225.394569776618,-83.9999999999991));
#902=IFCFACE((#903));
#903=IFCFACEOUTERBOUND(#904,.T.);
#904=IFCPOLYLOOP((#905,#906,#907));
#905=IFCCARTESIANPOINT((-152.483829695455,234.862971464014,0.));
#906=IFCCARTESIANPOINT((-67.2093088278616,240.324720105693,-83.9999999999991));
#907=IFCCARTESIANPOINT((-88.9083392880943,234.076268171425,-83.9999999999991));
#908=IFCFACE((#909));
#909=IFCFACEOUTERBOUND(#910,.T.);
#910=IFCPOLYLOOP((#911,#912,#913));
#911=IFCCARTESIANPOINT((-114.905033181042,243.168612385642,0.));
#912=IFCCARTESIANPOINT((-45.0238143941789,244.546840992317,-83.9999999999991));
#913=IFCCARTESIANPOINT((-67.2093088278616,240.324720105693,-83.9999999999991));
#914=IFCFACE((#915));
#915=IFCFACEOUTERBOUND(#916,.T.);
#916=IFCPOLYLOOP((#917,#918,#919));
#917=IFCCARTESIANPOINT((-76.8279738129532,248.781505081298,0.));
#918=IFCCARTESIANPOINT((-22.5714426203723,246.991542511388,-83.9999999999991));
#919=IFCCARTESIANPOINT((-45.0238143941789,244.546840992317,-83.9999999999991));
#920=IFCFACE((#921));
#921=IFCFACEOUTERBOUND(#922,.T.);
#922=IFCPOLYLOOP((#923,#924,#925));
#923=IFCCARTESIANPOINT((-38.4757029862493,252.0334780278,0.));
#924=IFCCARTESIANPOINT((0.,247.792422124388,-83.9999999999991));
#925=IFCCARTESIANPOINT((-22.5714426203723,246.991542511388,-83.9999999999991));
#926=IFCFACE((#927));
#927=IFCFACEOUTERBOUND(#928,.T.);
#928=IFCPOLYLOOP((#929,#930,#931));
#929=IFCCARTESIANPOINT((0.,253.099263998677,0.));
#930=IFCCARTESIANPOINT((38.4756983219429,252.033478287557,0.));
#931=IFCCARTESIANPOINT((22.5714426167356,246.991542511648,-83.9999999999991));
#932=IFCFACE((#933));
#933=IFCFACEOUTERBOUND(#934,.T.);
#934=IFCPOLYLOOP((#935,#936,#937));
#935=IFCCARTESIANPOINT((38.4756983219429,252.033478287557,0.));
#936=IFCCARTESIANPOINT((76.82797079471,248.781505427916,0.));
#937=IFCCARTESIANPOINT((45.0238122743352,244.546841305597,-83.9999999999991));
#938=IFCFACE((#939));
#939=IFCFACEOUTERBOUND(#940,.T.);
#940=IFCPOLYLOOP((#941,#942,#943));
#941=IFCCARTESIANPOINT((76.82797079471,248.781505427916,0.));
#942=IFCCARTESIANPOINT((114.905040261981,243.168611097888,0.));
#943=IFCCARTESIANPOINT((67.2093069026739,240.324720559443,-83.9999999999991));
#944=IFCFACE((#945));
#945=IFCFACEOUTERBOUND(#946,.T.);
#946=IFCPOLYLOOP((#947,#948,#949));
#947=IFCCARTESIANPOINT((114.905040261981,243.168611097888,0.));
#948=IFCCARTESIANPOINT((152.483825019629,234.862972697005,0.));
#949=IFCCARTESIANPOINT((88.90833880495,234.07626833846,-83.9999999999991));
#950=IFCFACE((#951));
#951=IFCFACEOUTERBOUND(#952,.T.);
#952=IFCPOLYLOOP((#953,#954,#955));
#953=IFCCARTESIANPOINT((152.483825019629,234.862972697005,0.));
#954=IFCCARTESIANPOINT((189.184260308893,223.297735395069,0.));
#955=IFCCARTESIANPOINT((109.746785929842,225.394568694178,-83.9999999999991));
#956=IFCFACE((#957));
#957=IFCFACEOUTERBOUND(#958,.T.);
#958=IFCPOLYLOOP((#959,#960,#961));
#959=IFCCARTESIANPOINT((189.184260308893,223.297735395069,0.));
#960=IFCCARTESIANPOINT((224.265826794532,207.523990642274,0.));
#961=IFCCARTESIANPOINT((129.019208284081,213.666097201998,-83.9999999999991));
#962=IFCFACE((#963));
#963=IFCFACEOUTERBOUND(#964,.T.);
#964=IFCPOLYLOOP((#965,#966,#967));
#965=IFCCARTESIANPOINT((224.265826794532,207.523990642274,0.));
#966=IFCCARTESIANPOINT((256.087669717028,185.991172830599,0.));
#967=IFCCARTESIANPOINT((145.336254078924,198.131563599338,-83.9999999999991));
#968=IFCFACE((#969));
#969=IFCFACEOUTERBOUND(#970,.T.);
#970=IFCPOLYLOOP((#971,#972,#973));
#971=IFCCARTESIANPOINT((256.087669717028,185.991172830599,0.));
#972=IFCCARTESIANPOINT((280.765071736094,156.699063336262,0.));
#973=IFCCARTESIANPOINT((156.295574340587,178.505166588871,-83.9999999999991));
#974=IFCFACE((#975));
#975=IFCFACEOUTERBOUND(#976,.T.);
#976=IFCPOLYLOOP((#977,#978,#979));
#977=IFCCARTESIANPOINT((280.765071736094,156.699063336262,0.));
#978=IFCCARTESIANPOINT((291.585141614082,120.093338245881,0.));
#979=IFCCARTESIANPOINT((159.838406320187,156.297847499286,-83.9999999999991));
#980=IFCFACE((#981));
#981=IFCFACEOUTERBOUND(#982,.T.);
#982=IFCPOLYLOOP((#983,#984,#985));
#983=IFCCARTESIANPOINT((291.585141614082,120.093338245881,0.));
#984=IFCCARTESIANPOINT((287.786485451514,81.9448662146907,0.));
#985=IFCCARTESIANPOINT((157.217230633118,133.907671395464,-83.9999999999991));
#986=IFCFACE((#987));
#987=IFCFACEOUTERBOUND(#988,.T.);
#988=IFCPOLYLOOP((#989,#990,#991));
#989=IFCCARTESIANPOINT((287.786485451514,81.9448662146907,0.));
#990=IFCCARTESIANPOINT((274.528157897687,45.8738463140147,0.));
#991=IFCCARTESIANPOINT((150.44694977305,112.380110135675,-83.9999999999991));
#992=IFCFACE((#993));
#993=IFCFACEOUTERBOUND(#994,.T.);
#994=IFCPOLYLOOP((#995,#996,#997));
#995=IFCCARTESIANPOINT((274.528157897687,45.8738463140147,0.));
#996=IFCCARTESIANPOINT((255.376512280737,12.5180862102588,0.));
#997=IFCCARTESIANPOINT((140.781372023645,91.9771690603084,-83.9999999999991));
#998=IFCFACE((#999));
#999=IFCFACEOUTERBOUND(#1000,.T.);
#1000=IFCPOLYLOOP((#1001,#1002,#1003));
#1001=IFCCARTESIANPOINT((255.376512280737,12.5180862102588,0.));
#1002=IFCCARTESIANPOINT((232.162082007755,-18.1641105843033,0.));
#1003=IFCCARTESIANPOINT((128.915131219959,72.7659087899957,-83.9999999999991));
#1004=IFCFACE((#1005));
#1005=IFCFACEOUTERBOUND(#1006,.T.);
#1006=IFCPOLYLOOP((#1007,#1008,#1009));
#1007=IFCCARTESIANPOINT((232.162082007755,-18.1641105843033,0.));
#1008=IFCCARTESIANPOINT((205.82058249047,-46.2135089189517,0.));
#1009=IFCCARTESIANPOINT((115.221437914664,54.8097236343718,-83.9999999999991));
#1010=IFCFACE((#1011));
#1011=IFCFACEOUTERBOUND(#1012,.T.);
#1012=IFCPOLYLOOP((#1013,#1014,#1015));
#1013=IFCCARTESIANPOINT((205.82058249047,-46.2135089189517,0.));
#1014=IFCCARTESIANPOINT((176.831543285532,-71.5181976728152,0.));
#1015=IFCCARTESIANPOINT((99.8796474348905,38.2399303092091,-83.9999999999991));
#1016=IFCFACE((#1017));
#1017=IFCFACEOUTERBOUND(#1018,.T.);
#1018=IFCPOLYLOOP((#1019,#1020,#1021));
#1019=IFCCARTESIANPOINT((176.831543285532,-71.5181976728152,0.));
#1020=IFCCARTESIANPOINT((145.419108882987,-93.7387882260769,0.));
#1021=IFCCARTESIANPOINT((82.9357630974296,23.3156482145766,-83.9999999999991));
#1022=IFCFACE((#1023));
#1023=IFCFACEOUTERBOUND(#1024,.T.);
#1024=IFCPOLYLOOP((#1025,#1026,#1027));
#1025=IFCCARTESIANPOINT((145.419108882987,-93.7387882260769,0.));
#1026=IFCCARTESIANPOINT((111.672515835794,-112.209605260782,0.));
#1027=IFCCARTESIANPOINT((64.3369357795818,10.5226661545967,-83.9999999999991));
#1028=IFCFACE((#1029));
#1029=IFCFACEOUTERBOUND(#1030,.T.);
#1030=IFCPOLYLOOP((#1031,#1032,#1033));
#1031=IFCCARTESIANPOINT((111.672515835794,-112.209605260782,0.));
#1032=IFCCARTESIANPOINT((75.812198248967,-126.124204222316,0.));
#1033=IFCCARTESIANPOINT((44.0834024872804,0.569911206685156,-83.9999999999991));
#1034=IFCFACE((#1035));
#1035=IFCFACEOUTERBOUND(#1036,.T.);
#1036=IFCPOLYLOOP((#1037,#1038,#1039));
#1037=IFCCARTESIANPOINT((75.812198248967,-126.124204222316,0.));
#1038=IFCCARTESIANPOINT((38.3440539351622,-134.804771445296,0.));
#1039=IFCCARTESIANPOINT((22.446084481302,-5.81475772180151,-83.9999999999991));
#1040=IFCFACE((#1041));
#1041=IFCFACEOUTERBOUND(#1042,.T.);
#1042=IFCPOLYLOOP((#1043,#1044,#1045));
#1043=IFCCARTESIANPOINT((38.3440539351622,-134.804771445296,0.));
#1044=IFCCARTESIANPOINT((5.62489231925E-06,-137.758996454453,0.));
#1045=IFCCARTESIANPOINT((1.76219393597E-06,-8.0243005407274,-83.9999999999991));
#1046=IFCFACE((#1047));
#1047=IFCFACEOUTERBOUND(#1048,.T.);
#1048=IFCPOLYLOOP((#1049,#1050,#1051));
#1049=IFCCARTESIANPOINT((5.62489231925E-06,-137.758996454453,0.));
#1050=IFCCARTESIANPOINT((-38.3440494938959,-134.804772131387,0.));
#1051=IFCCARTESIANPOINT((-22.4460832273862,-5.81475796938246,-83.9999999999991));
#1052=IFCFACE((#1053));
#1053=IFCFACEOUTERBOUND(#1054,.T.);
#1054=IFCPOLYLOOP((#1055,#1056,#1057));
#1055=IFCCARTESIANPOINT((-38.3440494938959,-134.804772131387,0.));
#1056=IFCCARTESIANPOINT((-75.8121974100292,-126.124204482417,0.));
#1057=IFCCARTESIANPOINT((-44.0834019165823,0.569910981625322,-83.9999999999991));
#1058=IFCFACE((#1059));
#1059=IFCFACEOUTERBOUND(#1060,.T.);
#1060=IFCPOLYLOOP((#1061,#1062,#1063));
#1061=IFCCARTESIANPOINT((-75.8121974100292,-126.124204482417,0.));
#1062=IFCCARTESIANPOINT((-111.672515690142,-112.209605328934,0.));
#1063=IFCCARTESIANPOINT((-64.3369337417203,10.5226649501159,-83.9999999999991));
#1064=IFCFACE((#1065));
#1065=IFCFACEOUTERBOUND(#1066,.T.);
#1066=IFCPOLYLOOP((#1067,#1068,#1069));
#1067=IFCCARTESIANPOINT((-111.672515690142,-112.209605328934,0.));
#1068=IFCCARTESIANPOINT((-145.419108622881,-93.7387883895915,0.));
#1069=IFCCARTESIANPOINT((-82.9357654214438,23.3156500424069,-83.9999999999991));
#1070=IFCFACE((#1071));
#1071=IFCFACEOUTERBOUND(#1072,.T.);
#1072=IFCPOLYLOOP((#1073,#1074,#1075));
#1073=IFCCARTESIANPOINT((-145.419108622881,-93.7387883895915,0.));
#1074=IFCCARTESIANPOINT((-176.831543103321,-71.5181978165611,0.));
#1075=IFCCARTESIANPOINT((-99.8796470071603,38.239929890392,-83.9999999999991));
#1076=IFCFACE((#1077));
#1077=IFCFACEOUTERBOUND(#1078,.T.);
#1078=IFCPOLYLOOP((#1079,#1080,#1081));
#1079=IFCCARTESIANPOINT((-176.831543103321,-71.5181978165611,0.));
#1080=IFCCARTESIANPOINT((-205.820584751455,-46.2135067401479,0.));
#1081=IFCCARTESIANPOINT((-115.221432502533,54.8097171953257,-83.9999999999991));
#1082=IFCFACE((#1083));
#1083=IFCFACEOUTERBOUND(#1084,.T.);
#1084=IFCPOLYLOOP((#1085,#1086,#1087));
#1085=IFCCARTESIANPOINT((-205.820584751455,-46.2135067401479,0.));
#1086=IFCCARTESIANPOINT((-232.162080231681,-18.164112680286,0.));
#1087=IFCCARTESIANPOINT((-128.915130782292,72.7659081552236,-83.9999999999991));
#1088=IFCFACE((#1089));
#1089=IFCFACEOUTERBOUND(#1090,.T.);
#1090=IFCPOLYLOOP((#1091,#1092,#1093));
#1091=IFCCARTESIANPOINT((-232.162080231681,-18.164112680286,0.));
#1092=IFCCARTESIANPOINT((-255.37651255073,12.5180866141667,0.));
#1093=IFCCARTESIANPOINT((-140.781370993691,91.9771671791993,-83.9999999999991));
#1094=IFCFACE((#1095));
#1095=IFCFACEOUTERBOUND(#1096,.T.);
#1096=IFCPOLYLOOP((#1097,#1098,#1099));
#1097=IFCCARTESIANPOINT((-255.37651255073,12.5180866141667,0.));
#1098=IFCCARTESIANPOINT((-274.528157992219,45.8738465115148,0.));
#1099=IFCCARTESIANPOINT((-150.446948503868,112.380106954109,-83.9999999999991));
#1100=IFCFACE((#1101));
#1101=IFCFACEOUTERBOUND(#1102,.T.);
#1102=IFCPOLYLOOP((#1103,#1104,#1105));
#1103=IFCCARTESIANPOINT((-274.528157992219,45.8738465115148,0.));
#1104=IFCCARTESIANPOINT((-287.786482832935,81.9448557189429,0.));
#1105=IFCCARTESIANPOINT((-157.217231378449,133.907674702395,-83.9999999999991));
#1106=IFCFACE((#1107));
#1107=IFCFACEOUTERBOUND(#1108,.T.);
#1108=IFCPOLYLOOP((#1109,#1110,#1111));
#1109=IFCCARTESIANPOINT((-287.786482832935,81.9448557189429,0.));
#1110=IFCCARTESIANPOINT((-291.585141527273,120.093339480335,0.));
#1111=IFCCARTESIANPOINT((-159.838406313243,156.297849730896,-83.9999999999991));
#1112=IFCFACE((#1113));
#1113=IFCFACEOUTERBOUND(#1114,.T.);
#1114=IFCPOLYLOOP((#1115,#1116,#1117));
#1115=IFCCARTESIANPOINT((-291.585141527273,120.093339480335,0.));
#1116=IFCCARTESIANPOINT((-280.765069841138,156.69906670217,0.));
#1117=IFCCARTESIANPOINT((-156.295574267743,178.505166800292,-83.9999999999991));
#1118=IFCFACE((#1119));
#1119=IFCFACEOUTERBOUND(#1120,.T.);
#1120=IFCPOLYLOOP((#1121,#1122,#1123));
#1121=IFCCARTESIANPOINT((-280.765069841138,156.69906670217,0.));
#1122=IFCCARTESIANPOINT((-256.087676107765,185.991167334279,0.));
#1123=IFCCARTESIANPOINT((-145.336255413146,198.131561931792,-83.9999999999991));
#1124=IFCFACE((#1125));
#1125=IFCFACEOUTERBOUND(#1126,.T.);
#1126=IFCPOLYLOOP((#1127,#1128,#1129));
#1127=IFCCARTESIANPOINT((-256.087676107765,185.991167334279,0.));
#1128=IFCCARTESIANPOINT((-224.265825523088,207.523991330957,0.));
#1129=IFCCARTESIANPOINT((-129.019214178155,213.666092805292,-83.9999999999991));
#1130=IFCFACE((#1131));
#1131=IFCFACEOUTERBOUND(#1132,.T.);
#1132=IFCPOLYLOOP((#1133,#1134,#1135));
#1133=IFCCARTESIANPOINT((-224.265825523088,207.523991330957,0.));
#1134=IFCCARTESIANPOINT((-189.184272565133,223.297730817421,0.));
#1135=IFCCARTESIANPOINT((-109.746783760019,225.394569776618,-83.9999999999991));
#1136=IFCFACE((#1137));
#1137=IFCFACEOUTERBOUND(#1138,.T.);
#1138=IFCPOLYLOOP((#1139,#1140,#1141));
#1139=IFCCARTESIANPOINT((-189.184272565133,223.297730817421,0.));
#1140=IFCCARTESIANPOINT((-152.483829695455,234.862971464014,0.));
#1141=IFCCARTESIANPOINT((-88.9083392880943,234.076268171425,-83.9999999999991));
#1142=IFCFACE((#1143));
#1143=IFCFACEOUTERBOUND(#1144,.T.);
#1144=IFCPOLYLOOP((#1145,#1146,#1147));
#1145=IFCCARTESIANPOINT((-152.483829695455,234.862971464014,0.));
#1146=IFCCARTESIANPOINT((-114.905033181042,243.168612385642,0.));
#1147=IFCCARTESIANPOINT((-67.2093088278616,240.324720105693,-83.9999999999991));
#1148=IFCFACE((#1149));
#1149=IFCFACEOUTERBOUND(#1150,.T.);
#1150=IFCPOLYLOOP((#1151,#1152,#1153));
#1151=IFCCARTESIANPOINT((-114.905033181042,243.168612385642,0.));
#1152=IFCCARTESIANPOINT((-76.8279738129532,248.781505081298,0.));
#1153=IFCCARTESIANPOINT((-45.0238143941789,244.546840992317,-83.9999999999991));
#1154=IFCFACE((#1155));
#1155=IFCFACEOUTERBOUND(#1156,.T.);
#1156=IFCPOLYLOOP((#1157,#1158,#1159));
#1157=IFCCARTESIANPOINT((-76.8279738129532,248.781505081298,0.));
#1158=IFCCARTESIANPOINT((-38.4757029862493,252.0334780278,0.));
#1159=IFCCARTESIANPOINT((-22.5714426203723,246.991542511388,-83.9999999999991));
#1160=IFCFACE((#1161));
#1161=IFCFACEOUTERBOUND(#1162,.T.);
#1162=IFCPOLYLOOP((#1163,#1164,#1165));
#1163=IFCCARTESIANPOINT((-38.4757029862493,252.0334780278,0.));
#1164=IFCCARTESIANPOINT((0.,253.099263998677,0.));
#1165=IFCCARTESIANPOINT((0.,247.792422124388,-83.9999999999991));
#1166=IFCMAPPEDITEM(#1167,#1170);
#1167=IFCREPRESENTATIONMAP(#1168,#5);
#1168=IFCAXIS2PLACEMENT3D(#1169,$,$);
#1169=IFCCARTESIANPOINT((0.,0.,0.));
#1170=IFCCARTESIANTRANSFORMATIONOPERATOR3D(#1171,#1172,#1173,1.,#1174);
#1171=IFCDIRECTION((1.,0.,0.));
#1172=IFCDIRECTION((0.,1.,0.));
#1173=IFCCARTESIANPOINT((0.,0.,0.));
#1174=IFCDIRECTION((0.,0.,1.));
#1175=IFCPERSONANDORGANIZATION(#1176,#1177,$);
#1176=IFCPERSON('Jon','Jon',$,$,$,$,$,$);
#1177=IFCORGANIZATION($,'Geometry Gym Pty Ltd',$,$,$);
#1178=IFCAPPLICATION(#1179,'1.0.0.0','ggRhinoIFC - Geometry Gym Plug-in for Rhino3d','ggRhinoIFC');
#1179=IFCORGANIZATION($,'Geometry Gym Pty Ltd',$,$,$);
#1180=IFCAXIS2PLACEMENT3D(#1181,$,$);
#1181=IFCCARTESIANPOINT((0.,0.,0.));
#1182=IFCDIRECTION((0.,1.));
#1183=IFCAXIS2PLACEMENT3D(#1184,$,$);
#1184=IFCCARTESIANPOINT((0.,0.,0.));
#1185=IFCDIRECTION((0.,1.));
#1186=IFCMATERIAL('Ceramic',$,$);
ENDSEC;
END-ISO-10303-21;

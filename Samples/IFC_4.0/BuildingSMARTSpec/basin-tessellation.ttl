# baseURI: http://linkedbuildingdata.net/ifc/resources20200624_184152/
# imports: http://standards.buildingsmart.org/IFC/DEV/IFC4/ADD1/OWL

@prefix ifc:  <http://standards.buildingsmart.org/IFC/DEV/IFC4/ADD1/OWL#> .
@prefix inst:  <http://linkedbuildingdata.net/ifc/resources20200624_184152/> .
@prefix list:  <https://w3id.org/list#> .
@prefix express:  <https://w3id.org/express#> .
@prefix rdf:  <http://www.w3.org/1999/02/22-rdf-syntax-ns#> .
@prefix xsd:  <http://www.w3.org/2001/XMLSchema#> .
@prefix owl:  <http://www.w3.org/2002/07/owl#> .

inst:   rdf:type     owl:Ontology ;
        owl:imports  ifc: .

inst:IfcApplication_1
        rdf:type  ifc:IfcApplication .

inst:IfcOrganization_2
        rdf:type  ifc:IfcOrganization .

inst:IfcApplication_1
        ifc:applicationDeveloper_IfcApplication  inst:IfcOrganization_2 .

inst:IfcLabel_46  rdf:type  ifc:IfcLabel ;
        express:hasString  "1.0.0.0" .

inst:IfcApplication_1
        ifc:version_IfcApplication  inst:IfcLabel_46 .

inst:IfcLabel_47  rdf:type  ifc:IfcLabel ;
        express:hasString  "ggRhinoIFC - Geometry Gym Plug-in for Rhino3d" .

inst:IfcApplication_1
        ifc:applicationFullName_IfcApplication  inst:IfcLabel_47 .

inst:IfcIdentifier_48
        rdf:type           ifc:IfcIdentifier ;
        express:hasString  "ggRhinoIFC" .

inst:IfcApplication_1
        ifc:applicationIdentifier_IfcApplication  inst:IfcIdentifier_48 .

inst:IfcLabel_49  rdf:type  ifc:IfcLabel ;
        express:hasString  "Geometry Gym Pty Ltd" .

inst:IfcOrganization_2
        ifc:name_IfcOrganization  inst:IfcLabel_49 .

inst:IfcPersonAndOrganization_3
        rdf:type  ifc:IfcPersonAndOrganization .

inst:IfcPerson_4  rdf:type  ifc:IfcPerson .

inst:IfcPersonAndOrganization_3
        ifc:thePerson_IfcPersonAndOrganization  inst:IfcPerson_4 ;
        ifc:theOrganization_IfcPersonAndOrganization  inst:IfcOrganization_2 .

inst:IfcIdentifier_50
        rdf:type           ifc:IfcIdentifier ;
        express:hasString  "Jon" .

inst:IfcPerson_4  ifc:identification_IfcPerson  inst:IfcIdentifier_50 ;
        ifc:familyName_IfcPerson      inst:IfcIdentifier_50 .

inst:IfcOwnerHistory_6
        rdf:type                        ifc:IfcOwnerHistory ;
        ifc:owningUser_IfcOwnerHistory  inst:IfcPersonAndOrganization_3 ;
        ifc:owningApplication_IfcOwnerHistory  inst:IfcApplication_1 ;
        ifc:changeAction_IfcOwnerHistory  ifc:ADDED .

inst:IfcTimeStamp_51  rdf:type  ifc:IfcTimeStamp ;
        express:hasInteger  1418084875 .

inst:IfcOwnerHistory_6
        ifc:lastModifiedDate_IfcOwnerHistory  inst:IfcTimeStamp_51 ;
        ifc:creationDate_IfcOwnerHistory  inst:IfcTimeStamp_51 .

inst:IfcGeometricRepresentationContext_7
        rdf:type  ifc:IfcGeometricRepresentationContext .

inst:IfcLabel_52  rdf:type  ifc:IfcLabel ;
        express:hasString  "Model" .

inst:IfcGeometricRepresentationContext_7
        ifc:contextType_IfcRepresentationContext  inst:IfcLabel_52 .

inst:IfcDimensionCount_53
        rdf:type            ifc:IfcDimensionCount ;
        express:hasInteger  3 .

inst:IfcGeometricRepresentationContext_7
        ifc:coordinateSpaceDimension_IfcGeometricRepresentationContext  inst:IfcDimensionCount_53 .

inst:IfcReal_54  rdf:type  ifc:IfcReal ;
        express:hasDouble  "0.0001"^^xsd:double .

inst:IfcGeometricRepresentationContext_7
        ifc:precision_IfcGeometricRepresentationContext  inst:IfcReal_54 .

inst:IfcAxis2Placement3D_8
        rdf:type  ifc:IfcAxis2Placement3D .

inst:IfcGeometricRepresentationContext_7
        ifc:worldCoordinateSystem_IfcGeometricRepresentationContext  inst:IfcAxis2Placement3D_8 .

inst:IfcDirection_10  rdf:type  ifc:IfcDirection .

inst:IfcGeometricRepresentationContext_7
        ifc:trueNorth_IfcGeometricRepresentationContext  inst:IfcDirection_10 .

inst:IfcCartesianPoint_9
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcAxis2Placement3D_8
        ifc:location_IfcPlacement  inst:IfcCartesianPoint_9 .

inst:IfcCartesianPointList3D_200
        rdf:type  ifc:IfcCartesianPointList3D .

inst:IfcLengthMeasure_List_55
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_56
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_57
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_58
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-300.0"^^xsd:double .

inst:IfcLengthMeasure_List_55
        list:hasContents  inst:IfcLengthMeasure_58 ;
        list:hasNext      inst:IfcLengthMeasure_List_56 .

inst:IfcLengthMeasure_59
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "150.0"^^xsd:double .

inst:IfcLengthMeasure_List_56
        list:hasContents  inst:IfcLengthMeasure_59 ;
        list:hasNext      inst:IfcLengthMeasure_List_57 .

inst:IfcLengthMeasure_60
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "0.0"^^xsd:double .

inst:IfcLengthMeasure_List_57
        list:hasContents  inst:IfcLengthMeasure_60 .

inst:IfcLengthMeasure_List_61
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_62
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_63
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_64
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-260.012578"^^xsd:double .

inst:IfcLengthMeasure_List_61
        list:hasContents  inst:IfcLengthMeasure_64 ;
        list:hasNext      inst:IfcLengthMeasure_List_62 .

inst:IfcLengthMeasure_65
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "202.771984"^^xsd:double .

inst:IfcLengthMeasure_List_62
        list:hasContents  inst:IfcLengthMeasure_65 ;
        list:hasNext      inst:IfcLengthMeasure_List_63 .

inst:IfcLengthMeasure_List_63
        list:hasContents  inst:IfcLengthMeasure_60 .

inst:IfcLengthMeasure_List_66
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_67
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_68
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_69
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-200.897703"^^xsd:double .

inst:IfcLengthMeasure_List_66
        list:hasContents  inst:IfcLengthMeasure_69 ;
        list:hasNext      inst:IfcLengthMeasure_List_67 .

inst:IfcLengthMeasure_70
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "235.427328"^^xsd:double .

inst:IfcLengthMeasure_List_67
        list:hasContents  inst:IfcLengthMeasure_70 ;
        list:hasNext      inst:IfcLengthMeasure_List_68 .

inst:IfcLengthMeasure_List_68
        list:hasContents  inst:IfcLengthMeasure_60 .

inst:IfcLengthMeasure_List_71
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_72
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_73
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_74
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-135.653172"^^xsd:double .

inst:IfcLengthMeasure_List_71
        list:hasContents  inst:IfcLengthMeasure_74 ;
        list:hasNext      inst:IfcLengthMeasure_List_72 .

inst:IfcLengthMeasure_75
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "254.960516"^^xsd:double .

inst:IfcLengthMeasure_List_72
        list:hasContents  inst:IfcLengthMeasure_75 ;
        list:hasNext      inst:IfcLengthMeasure_List_73 .

inst:IfcLengthMeasure_List_73
        list:hasContents  inst:IfcLengthMeasure_60 .

inst:IfcLengthMeasure_List_76
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_77
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_78
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_79
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-68.351281"^^xsd:double .

inst:IfcLengthMeasure_List_76
        list:hasContents  inst:IfcLengthMeasure_79 ;
        list:hasNext      inst:IfcLengthMeasure_List_77 .

inst:IfcLengthMeasure_80
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "265.485063"^^xsd:double .

inst:IfcLengthMeasure_List_77
        list:hasContents  inst:IfcLengthMeasure_80 ;
        list:hasNext      inst:IfcLengthMeasure_List_78 .

inst:IfcLengthMeasure_List_78
        list:hasContents  inst:IfcLengthMeasure_60 .

inst:IfcLengthMeasure_List_81
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_82
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_83
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_84
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "2.288734"^^xsd:double .

inst:IfcLengthMeasure_List_81
        list:hasContents  inst:IfcLengthMeasure_84 ;
        list:hasNext      inst:IfcLengthMeasure_List_82 .

inst:IfcLengthMeasure_85
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "268.839531"^^xsd:double .

inst:IfcLengthMeasure_List_82
        list:hasContents  inst:IfcLengthMeasure_85 ;
        list:hasNext      inst:IfcLengthMeasure_List_83 .

inst:IfcLengthMeasure_List_83
        list:hasContents  inst:IfcLengthMeasure_60 .

inst:IfcLengthMeasure_List_86
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_87
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_88
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_89
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "72.81782"^^xsd:double .

inst:IfcLengthMeasure_List_86
        list:hasContents  inst:IfcLengthMeasure_89 ;
        list:hasNext      inst:IfcLengthMeasure_List_87 .

inst:IfcLengthMeasure_90
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "265.023844"^^xsd:double .

inst:IfcLengthMeasure_List_87
        list:hasContents  inst:IfcLengthMeasure_90 ;
        list:hasNext      inst:IfcLengthMeasure_List_88 .

inst:IfcLengthMeasure_List_88
        list:hasContents  inst:IfcLengthMeasure_60 .

inst:IfcLengthMeasure_List_91
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_92
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_93
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_94
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "139.786906"^^xsd:double .

inst:IfcLengthMeasure_List_91
        list:hasContents  inst:IfcLengthMeasure_94 ;
        list:hasNext      inst:IfcLengthMeasure_List_92 .

inst:IfcLengthMeasure_95
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "254.038063"^^xsd:double .

inst:IfcLengthMeasure_List_92
        list:hasContents  inst:IfcLengthMeasure_95 ;
        list:hasNext      inst:IfcLengthMeasure_List_93 .

inst:IfcLengthMeasure_List_93
        list:hasContents  inst:IfcLengthMeasure_60 .

inst:IfcLengthMeasure_List_96
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_97
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_98
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_99
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "201.174906"^^xsd:double .

inst:IfcLengthMeasure_List_96
        list:hasContents  inst:IfcLengthMeasure_99 ;
        list:hasNext      inst:IfcLengthMeasure_List_97 .

inst:IfcLengthMeasure_100
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "235.317031"^^xsd:double .

inst:IfcLengthMeasure_List_97
        list:hasContents  inst:IfcLengthMeasure_100 ;
        list:hasNext      inst:IfcLengthMeasure_List_98 .

inst:IfcLengthMeasure_List_98
        list:hasContents  inst:IfcLengthMeasure_60 .

inst:IfcLengthMeasure_List_101
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_102
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_103
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_104
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "259.220938"^^xsd:double .

inst:IfcLengthMeasure_List_101
        list:hasContents  inst:IfcLengthMeasure_104 ;
        list:hasNext      inst:IfcLengthMeasure_List_102 .

inst:IfcLengthMeasure_105
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "203.387031"^^xsd:double .

inst:IfcLengthMeasure_List_102
        list:hasContents  inst:IfcLengthMeasure_105 ;
        list:hasNext      inst:IfcLengthMeasure_List_103 .

inst:IfcLengthMeasure_List_103
        list:hasContents  inst:IfcLengthMeasure_60 .

inst:IfcLengthMeasure_List_106
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_107
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_108
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_109
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "300.0"^^xsd:double .

inst:IfcLengthMeasure_List_106
        list:hasContents  inst:IfcLengthMeasure_109 ;
        list:hasNext      inst:IfcLengthMeasure_List_107 .

inst:IfcLengthMeasure_List_107
        list:hasContents  inst:IfcLengthMeasure_59 ;
        list:hasNext      inst:IfcLengthMeasure_List_108 .

inst:IfcLengthMeasure_List_108
        list:hasContents  inst:IfcLengthMeasure_60 .

inst:IfcLengthMeasure_List_110
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_111
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_112
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_113
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "301.12175"^^xsd:double .

inst:IfcLengthMeasure_List_110
        list:hasContents  inst:IfcLengthMeasure_113 ;
        list:hasNext      inst:IfcLengthMeasure_List_111 .

inst:IfcLengthMeasure_114
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "84.866148"^^xsd:double .

inst:IfcLengthMeasure_List_111
        list:hasContents  inst:IfcLengthMeasure_114 ;
        list:hasNext      inst:IfcLengthMeasure_List_112 .

inst:IfcLengthMeasure_List_112
        list:hasContents  inst:IfcLengthMeasure_60 .

inst:IfcLengthMeasure_List_115
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_116
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_117
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_118
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "274.727594"^^xsd:double .

inst:IfcLengthMeasure_List_115
        list:hasContents  inst:IfcLengthMeasure_118 ;
        list:hasNext      inst:IfcLengthMeasure_List_116 .

inst:IfcLengthMeasure_119
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "21.433672"^^xsd:double .

inst:IfcLengthMeasure_List_116
        list:hasContents  inst:IfcLengthMeasure_119 ;
        list:hasNext      inst:IfcLengthMeasure_List_117 .

inst:IfcLengthMeasure_List_117
        list:hasContents  inst:IfcLengthMeasure_60 .

inst:IfcLengthMeasure_List_120
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_121
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_122
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_123
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "235.605922"^^xsd:double .

inst:IfcLengthMeasure_List_120
        list:hasContents  inst:IfcLengthMeasure_123 ;
        list:hasNext      inst:IfcLengthMeasure_List_121 .

inst:IfcLengthMeasure_124
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-32.723826"^^xsd:double .

inst:IfcLengthMeasure_List_121
        list:hasContents  inst:IfcLengthMeasure_124 ;
        list:hasNext      inst:IfcLengthMeasure_List_122 .

inst:IfcLengthMeasure_List_122
        list:hasContents  inst:IfcLengthMeasure_60 .

inst:IfcLengthMeasure_List_125
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_126
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_127
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_128
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "186.088641"^^xsd:double .

inst:IfcLengthMeasure_List_125
        list:hasContents  inst:IfcLengthMeasure_128 ;
        list:hasNext      inst:IfcLengthMeasure_List_126 .

inst:IfcLengthMeasure_129
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-80.939688"^^xsd:double .

inst:IfcLengthMeasure_List_126
        list:hasContents  inst:IfcLengthMeasure_129 ;
        list:hasNext      inst:IfcLengthMeasure_List_127 .

inst:IfcLengthMeasure_List_127
        list:hasContents  inst:IfcLengthMeasure_60 .

inst:IfcLengthMeasure_List_130
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_131
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_132
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_133
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "130.136258"^^xsd:double .

inst:IfcLengthMeasure_List_130
        list:hasContents  inst:IfcLengthMeasure_133 ;
        list:hasNext      inst:IfcLengthMeasure_List_131 .

inst:IfcLengthMeasure_134
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-119.016594"^^xsd:double .

inst:IfcLengthMeasure_List_131
        list:hasContents  inst:IfcLengthMeasure_134 ;
        list:hasNext      inst:IfcLengthMeasure_List_132 .

inst:IfcLengthMeasure_List_132
        list:hasContents  inst:IfcLengthMeasure_60 .

inst:IfcLengthMeasure_List_135
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_136
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_137
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_138
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "67.084977"^^xsd:double .

inst:IfcLengthMeasure_List_135
        list:hasContents  inst:IfcLengthMeasure_138 ;
        list:hasNext      inst:IfcLengthMeasure_List_136 .

inst:IfcLengthMeasure_139
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-144.523266"^^xsd:double .

inst:IfcLengthMeasure_List_136
        list:hasContents  inst:IfcLengthMeasure_139 ;
        list:hasNext      inst:IfcLengthMeasure_List_137 .

inst:IfcLengthMeasure_List_137
        list:hasContents  inst:IfcLengthMeasure_60 .

inst:IfcLengthMeasure_List_140
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_141
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_142
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_143
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "1.477218"^^xsd:double .

inst:IfcLengthMeasure_List_140
        list:hasContents  inst:IfcLengthMeasure_143 ;
        list:hasNext      inst:IfcLengthMeasure_List_141 .

inst:IfcLengthMeasure_144
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-153.498641"^^xsd:double .

inst:IfcLengthMeasure_List_141
        list:hasContents  inst:IfcLengthMeasure_144 ;
        list:hasNext      inst:IfcLengthMeasure_List_142 .

inst:IfcLengthMeasure_List_142
        list:hasContents  inst:IfcLengthMeasure_60 .

inst:IfcLengthMeasure_List_145
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_146
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_147
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_148
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-64.392137"^^xsd:double .

inst:IfcLengthMeasure_List_145
        list:hasContents  inst:IfcLengthMeasure_148 ;
        list:hasNext      inst:IfcLengthMeasure_List_146 .

inst:IfcLengthMeasure_149
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-145.234375"^^xsd:double .

inst:IfcLengthMeasure_List_146
        list:hasContents  inst:IfcLengthMeasure_149 ;
        list:hasNext      inst:IfcLengthMeasure_List_147 .

inst:IfcLengthMeasure_List_147
        list:hasContents  inst:IfcLengthMeasure_60 .

inst:IfcLengthMeasure_List_150
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_151
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_152
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_153
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-128.935"^^xsd:double .

inst:IfcLengthMeasure_List_150
        list:hasContents  inst:IfcLengthMeasure_153 ;
        list:hasNext      inst:IfcLengthMeasure_List_151 .

inst:IfcLengthMeasure_154
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-119.668008"^^xsd:double .

inst:IfcLengthMeasure_List_151
        list:hasContents  inst:IfcLengthMeasure_154 ;
        list:hasNext      inst:IfcLengthMeasure_List_152 .

inst:IfcLengthMeasure_List_152
        list:hasContents  inst:IfcLengthMeasure_60 .

inst:IfcLengthMeasure_List_155
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_156
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_157
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_158
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-185.4365"^^xsd:double .

inst:IfcLengthMeasure_List_155
        list:hasContents  inst:IfcLengthMeasure_158 ;
        list:hasNext      inst:IfcLengthMeasure_List_156 .

inst:IfcLengthMeasure_159
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-81.474469"^^xsd:double .

inst:IfcLengthMeasure_List_156
        list:hasContents  inst:IfcLengthMeasure_159 ;
        list:hasNext      inst:IfcLengthMeasure_List_157 .

inst:IfcLengthMeasure_List_157
        list:hasContents  inst:IfcLengthMeasure_60 .

inst:IfcLengthMeasure_List_160
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_161
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_162
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_163
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-235.751609"^^xsd:double .

inst:IfcLengthMeasure_List_160
        list:hasContents  inst:IfcLengthMeasure_163 ;
        list:hasNext      inst:IfcLengthMeasure_List_161 .

inst:IfcLengthMeasure_164
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-32.555805"^^xsd:double .

inst:IfcLengthMeasure_List_161
        list:hasContents  inst:IfcLengthMeasure_164 ;
        list:hasNext      inst:IfcLengthMeasure_List_162 .

inst:IfcLengthMeasure_List_162
        list:hasContents  inst:IfcLengthMeasure_60 .

inst:IfcLengthMeasure_List_165
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_166
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_167
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_168
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-275.439625"^^xsd:double .

inst:IfcLengthMeasure_List_165
        list:hasContents  inst:IfcLengthMeasure_168 ;
        list:hasNext      inst:IfcLengthMeasure_List_166 .

inst:IfcLengthMeasure_169
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "22.660475"^^xsd:double .

inst:IfcLengthMeasure_List_166
        list:hasContents  inst:IfcLengthMeasure_169 ;
        list:hasNext      inst:IfcLengthMeasure_List_167 .

inst:IfcLengthMeasure_List_167
        list:hasContents  inst:IfcLengthMeasure_60 .

inst:IfcLengthMeasure_List_170
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_171
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_172
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_173
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-301.2465"^^xsd:double .

inst:IfcLengthMeasure_List_170
        list:hasContents  inst:IfcLengthMeasure_173 ;
        list:hasNext      inst:IfcLengthMeasure_List_171 .

inst:IfcLengthMeasure_174
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "85.400219"^^xsd:double .

inst:IfcLengthMeasure_List_171
        list:hasContents  inst:IfcLengthMeasure_174 ;
        list:hasNext      inst:IfcLengthMeasure_List_172 .

inst:IfcLengthMeasure_List_172
        list:hasContents  inst:IfcLengthMeasure_60 .

inst:IfcLengthMeasure_List_175
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_176
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_177
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_175
        list:hasContents  inst:IfcLengthMeasure_60 ;
        list:hasNext      inst:IfcLengthMeasure_List_176 .

inst:IfcLengthMeasure_178
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "253.099266"^^xsd:double .

inst:IfcLengthMeasure_List_176
        list:hasContents  inst:IfcLengthMeasure_178 ;
        list:hasNext      inst:IfcLengthMeasure_List_177 .

inst:IfcLengthMeasure_List_177
        list:hasContents  inst:IfcLengthMeasure_60 .

inst:IfcLengthMeasure_List_179
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_180
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_181
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_182
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-65.777992"^^xsd:double .

inst:IfcLengthMeasure_List_179
        list:hasContents  inst:IfcLengthMeasure_182 ;
        list:hasNext      inst:IfcLengthMeasure_List_180 .

inst:IfcLengthMeasure_183
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "249.952375"^^xsd:double .

inst:IfcLengthMeasure_List_180
        list:hasContents  inst:IfcLengthMeasure_183 ;
        list:hasNext      inst:IfcLengthMeasure_List_181 .

inst:IfcLengthMeasure_List_181
        list:hasContents  inst:IfcLengthMeasure_60 .

inst:IfcLengthMeasure_List_184
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_185
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_186
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_187
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-128.508695"^^xsd:double .

inst:IfcLengthMeasure_List_184
        list:hasContents  inst:IfcLengthMeasure_187 ;
        list:hasNext      inst:IfcLengthMeasure_List_185 .

inst:IfcLengthMeasure_188
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "240.511688"^^xsd:double .

inst:IfcLengthMeasure_List_185
        list:hasContents  inst:IfcLengthMeasure_188 ;
        list:hasNext      inst:IfcLengthMeasure_List_186 .

inst:IfcLengthMeasure_List_186
        list:hasContents  inst:IfcLengthMeasure_60 .

inst:IfcLengthMeasure_List_189
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_190
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_191
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_192
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-189.983266"^^xsd:double .

inst:IfcLengthMeasure_List_189
        list:hasContents  inst:IfcLengthMeasure_192 ;
        list:hasNext      inst:IfcLengthMeasure_List_190 .

inst:IfcLengthMeasure_193
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "222.998141"^^xsd:double .

inst:IfcLengthMeasure_List_190
        list:hasContents  inst:IfcLengthMeasure_193 ;
        list:hasNext      inst:IfcLengthMeasure_List_191 .

inst:IfcLengthMeasure_List_191
        list:hasContents  inst:IfcLengthMeasure_60 .

inst:IfcLengthMeasure_List_194
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_195
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_196
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_197
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-246.840234"^^xsd:double .

inst:IfcLengthMeasure_List_194
        list:hasContents  inst:IfcLengthMeasure_197 ;
        list:hasNext      inst:IfcLengthMeasure_List_195 .

inst:IfcLengthMeasure_198
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "193.330969"^^xsd:double .

inst:IfcLengthMeasure_List_195
        list:hasContents  inst:IfcLengthMeasure_198 ;
        list:hasNext      inst:IfcLengthMeasure_List_196 .

inst:IfcLengthMeasure_List_196
        list:hasContents  inst:IfcLengthMeasure_60 .

inst:IfcLengthMeasure_List_199
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_200
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_201
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_202
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-286.93375"^^xsd:double .

inst:IfcLengthMeasure_List_199
        list:hasContents  inst:IfcLengthMeasure_202 ;
        list:hasNext      inst:IfcLengthMeasure_List_200 .

inst:IfcLengthMeasure_203
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "143.116359"^^xsd:double .

inst:IfcLengthMeasure_List_200
        list:hasContents  inst:IfcLengthMeasure_203 ;
        list:hasNext      inst:IfcLengthMeasure_List_201 .

inst:IfcLengthMeasure_List_201
        list:hasContents  inst:IfcLengthMeasure_60 .

inst:IfcLengthMeasure_List_204
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_205
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_206
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_207
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-288.338563"^^xsd:double .

inst:IfcLengthMeasure_List_204
        list:hasContents  inst:IfcLengthMeasure_207 ;
        list:hasNext      inst:IfcLengthMeasure_List_205 .

inst:IfcLengthMeasure_208
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "84.231891"^^xsd:double .

inst:IfcLengthMeasure_List_205
        list:hasContents  inst:IfcLengthMeasure_208 ;
        list:hasNext      inst:IfcLengthMeasure_List_206 .

inst:IfcLengthMeasure_List_206
        list:hasContents  inst:IfcLengthMeasure_60 .

inst:IfcLengthMeasure_List_209
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_210
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_211
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_212
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-263.388344"^^xsd:double .

inst:IfcLengthMeasure_List_209
        list:hasContents  inst:IfcLengthMeasure_212 ;
        list:hasNext      inst:IfcLengthMeasure_List_210 .

inst:IfcLengthMeasure_213
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "25.178932"^^xsd:double .

inst:IfcLengthMeasure_List_210
        list:hasContents  inst:IfcLengthMeasure_213 ;
        list:hasNext      inst:IfcLengthMeasure_List_211 .

inst:IfcLengthMeasure_List_211
        list:hasContents  inst:IfcLengthMeasure_60 .

inst:IfcLengthMeasure_List_214
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_215
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_216
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_217
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-224.986906"^^xsd:double .

inst:IfcLengthMeasure_List_214
        list:hasContents  inst:IfcLengthMeasure_217 ;
        list:hasNext      inst:IfcLengthMeasure_List_215 .

inst:IfcLengthMeasure_218
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-26.382564"^^xsd:double .

inst:IfcLengthMeasure_List_215
        list:hasContents  inst:IfcLengthMeasure_218 ;
        list:hasNext      inst:IfcLengthMeasure_List_216 .

inst:IfcLengthMeasure_List_216
        list:hasContents  inst:IfcLengthMeasure_60 .

inst:IfcLengthMeasure_List_219
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_220
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_221
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_222
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-176.642109"^^xsd:double .

inst:IfcLengthMeasure_List_219
        list:hasContents  inst:IfcLengthMeasure_222 ;
        list:hasNext      inst:IfcLengthMeasure_List_220 .

inst:IfcLengthMeasure_223
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-71.667547"^^xsd:double .

inst:IfcLengthMeasure_List_220
        list:hasContents  inst:IfcLengthMeasure_223 ;
        list:hasNext      inst:IfcLengthMeasure_List_221 .

inst:IfcLengthMeasure_List_221
        list:hasContents  inst:IfcLengthMeasure_60 .

inst:IfcLengthMeasure_List_224
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_225
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_226
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_227
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-122.550633"^^xsd:double .

inst:IfcLengthMeasure_List_224
        list:hasContents  inst:IfcLengthMeasure_227 ;
        list:hasNext      inst:IfcLengthMeasure_List_225 .

inst:IfcLengthMeasure_228
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-106.846461"^^xsd:double .

inst:IfcLengthMeasure_List_225
        list:hasContents  inst:IfcLengthMeasure_228 ;
        list:hasNext      inst:IfcLengthMeasure_List_226 .

inst:IfcLengthMeasure_List_226
        list:hasContents  inst:IfcLengthMeasure_60 .

inst:IfcLengthMeasure_List_229
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_230
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_231
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_232
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-61.391031"^^xsd:double .

inst:IfcLengthMeasure_List_229
        list:hasContents  inst:IfcLengthMeasure_232 ;
        list:hasNext      inst:IfcLengthMeasure_List_230 .

inst:IfcLengthMeasure_233
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-130.155953"^^xsd:double .

inst:IfcLengthMeasure_List_230
        list:hasContents  inst:IfcLengthMeasure_233 ;
        list:hasNext      inst:IfcLengthMeasure_List_231 .

inst:IfcLengthMeasure_List_231
        list:hasContents  inst:IfcLengthMeasure_60 .

inst:IfcLengthMeasure_List_234
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_235
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_236
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_237
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "1.00923"^^xsd:double .

inst:IfcLengthMeasure_List_234
        list:hasContents  inst:IfcLengthMeasure_237 ;
        list:hasNext      inst:IfcLengthMeasure_List_235 .

inst:IfcLengthMeasure_238
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-137.756953"^^xsd:double .

inst:IfcLengthMeasure_List_235
        list:hasContents  inst:IfcLengthMeasure_238 ;
        list:hasNext      inst:IfcLengthMeasure_List_236 .

inst:IfcLengthMeasure_List_236
        list:hasContents  inst:IfcLengthMeasure_60 .

inst:IfcLengthMeasure_List_239
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_240
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_241
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_242
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "63.202145"^^xsd:double .

inst:IfcLengthMeasure_List_239
        list:hasContents  inst:IfcLengthMeasure_242 ;
        list:hasNext      inst:IfcLengthMeasure_List_240 .

inst:IfcLengthMeasure_243
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-129.69757"^^xsd:double .

inst:IfcLengthMeasure_List_240
        list:hasContents  inst:IfcLengthMeasure_243 ;
        list:hasNext      inst:IfcLengthMeasure_List_241 .

inst:IfcLengthMeasure_List_241
        list:hasContents  inst:IfcLengthMeasure_60 .

inst:IfcLengthMeasure_List_244
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_245
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_246
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_247
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "123.138398"^^xsd:double .

inst:IfcLengthMeasure_List_244
        list:hasContents  inst:IfcLengthMeasure_247 ;
        list:hasNext      inst:IfcLengthMeasure_List_245 .

inst:IfcLengthMeasure_248
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-106.540977"^^xsd:double .

inst:IfcLengthMeasure_List_245
        list:hasContents  inst:IfcLengthMeasure_248 ;
        list:hasNext      inst:IfcLengthMeasure_List_246 .

inst:IfcLengthMeasure_List_246
        list:hasContents  inst:IfcLengthMeasure_60 .

inst:IfcLengthMeasure_List_249
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_250
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_251
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_252
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "176.955734"^^xsd:double .

inst:IfcLengthMeasure_List_249
        list:hasContents  inst:IfcLengthMeasure_252 ;
        list:hasNext      inst:IfcLengthMeasure_List_250 .

inst:IfcLengthMeasure_253
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-71.42018"^^xsd:double .

inst:IfcLengthMeasure_List_250
        list:hasContents  inst:IfcLengthMeasure_253 ;
        list:hasNext      inst:IfcLengthMeasure_List_251 .

inst:IfcLengthMeasure_List_251
        list:hasContents  inst:IfcLengthMeasure_60 .

inst:IfcLengthMeasure_List_254
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_255
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_256
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_257
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "224.650078"^^xsd:double .

inst:IfcLengthMeasure_List_254
        list:hasContents  inst:IfcLengthMeasure_257 ;
        list:hasNext      inst:IfcLengthMeasure_List_255 .

inst:IfcLengthMeasure_258
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-26.756678"^^xsd:double .

inst:IfcLengthMeasure_List_255
        list:hasContents  inst:IfcLengthMeasure_258 ;
        list:hasNext      inst:IfcLengthMeasure_List_256 .

inst:IfcLengthMeasure_List_256
        list:hasContents  inst:IfcLengthMeasure_60 .

inst:IfcLengthMeasure_List_259
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_260
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_261
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_262
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "262.387781"^^xsd:double .

inst:IfcLengthMeasure_List_259
        list:hasContents  inst:IfcLengthMeasure_262 ;
        list:hasNext      inst:IfcLengthMeasure_List_260 .

inst:IfcLengthMeasure_263
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "23.516443"^^xsd:double .

inst:IfcLengthMeasure_List_260
        list:hasContents  inst:IfcLengthMeasure_263 ;
        list:hasNext      inst:IfcLengthMeasure_List_261 .

inst:IfcLengthMeasure_List_261
        list:hasContents  inst:IfcLengthMeasure_60 .

inst:IfcLengthMeasure_List_264
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_265
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_266
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_267
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "288.070906"^^xsd:double .

inst:IfcLengthMeasure_List_264
        list:hasContents  inst:IfcLengthMeasure_267 ;
        list:hasNext      inst:IfcLengthMeasure_List_265 .

inst:IfcLengthMeasure_268
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "83.103938"^^xsd:double .

inst:IfcLengthMeasure_List_265
        list:hasContents  inst:IfcLengthMeasure_268 ;
        list:hasNext      inst:IfcLengthMeasure_List_266 .

inst:IfcLengthMeasure_List_266
        list:hasContents  inst:IfcLengthMeasure_60 .

inst:IfcLengthMeasure_List_269
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_270
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_271
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_272
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "286.93375"^^xsd:double .

inst:IfcLengthMeasure_List_269
        list:hasContents  inst:IfcLengthMeasure_272 ;
        list:hasNext      inst:IfcLengthMeasure_List_270 .

inst:IfcLengthMeasure_List_270
        list:hasContents  inst:IfcLengthMeasure_203 ;
        list:hasNext      inst:IfcLengthMeasure_List_271 .

inst:IfcLengthMeasure_List_271
        list:hasContents  inst:IfcLengthMeasure_60 .

inst:IfcLengthMeasure_List_273
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_274
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_275
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_276
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "248.344641"^^xsd:double .

inst:IfcLengthMeasure_List_273
        list:hasContents  inst:IfcLengthMeasure_276 ;
        list:hasNext      inst:IfcLengthMeasure_List_274 .

inst:IfcLengthMeasure_277
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "192.212875"^^xsd:double .

inst:IfcLengthMeasure_List_274
        list:hasContents  inst:IfcLengthMeasure_277 ;
        list:hasNext      inst:IfcLengthMeasure_List_275 .

inst:IfcLengthMeasure_List_275
        list:hasContents  inst:IfcLengthMeasure_60 .

inst:IfcLengthMeasure_List_278
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_279
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_280
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_281
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "191.622094"^^xsd:double .

inst:IfcLengthMeasure_List_278
        list:hasContents  inst:IfcLengthMeasure_281 ;
        list:hasNext      inst:IfcLengthMeasure_List_279 .

inst:IfcLengthMeasure_282
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "222.376281"^^xsd:double .

inst:IfcLengthMeasure_List_279
        list:hasContents  inst:IfcLengthMeasure_282 ;
        list:hasNext      inst:IfcLengthMeasure_List_280 .

inst:IfcLengthMeasure_List_280
        list:hasContents  inst:IfcLengthMeasure_60 .

inst:IfcLengthMeasure_List_283
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_284
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_285
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_286
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "129.659992"^^xsd:double .

inst:IfcLengthMeasure_List_283
        list:hasContents  inst:IfcLengthMeasure_286 ;
        list:hasNext      inst:IfcLengthMeasure_List_284 .

inst:IfcLengthMeasure_287
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "240.269531"^^xsd:double .

inst:IfcLengthMeasure_List_284
        list:hasContents  inst:IfcLengthMeasure_287 ;
        list:hasNext      inst:IfcLengthMeasure_List_285 .

inst:IfcLengthMeasure_List_285
        list:hasContents  inst:IfcLengthMeasure_60 .

inst:IfcLengthMeasure_List_288
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_289
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_290
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_291
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "64.742059"^^xsd:double .

inst:IfcLengthMeasure_List_288
        list:hasContents  inst:IfcLengthMeasure_291 ;
        list:hasNext      inst:IfcLengthMeasure_List_289 .

inst:IfcLengthMeasure_292
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "250.052203"^^xsd:double .

inst:IfcLengthMeasure_List_289
        list:hasContents  inst:IfcLengthMeasure_292 ;
        list:hasNext      inst:IfcLengthMeasure_List_290 .

inst:IfcLengthMeasure_List_290
        list:hasContents  inst:IfcLengthMeasure_60 .

inst:IfcLengthMeasure_List_293
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_294
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_295
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_296
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-157.154922"^^xsd:double .

inst:IfcLengthMeasure_List_293
        list:hasContents  inst:IfcLengthMeasure_296 ;
        list:hasNext      inst:IfcLengthMeasure_List_294 .

inst:IfcLengthMeasure_297
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "175.808609"^^xsd:double .

inst:IfcLengthMeasure_List_294
        list:hasContents  inst:IfcLengthMeasure_297 ;
        list:hasNext      inst:IfcLengthMeasure_List_295 .

inst:IfcLengthMeasure_298
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-94.0"^^xsd:double .

inst:IfcLengthMeasure_List_295
        list:hasContents  inst:IfcLengthMeasure_298 .

inst:IfcLengthMeasure_List_299
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_300
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_301
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_302
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-136.207516"^^xsd:double .

inst:IfcLengthMeasure_List_299
        list:hasContents  inst:IfcLengthMeasure_302 ;
        list:hasNext      inst:IfcLengthMeasure_List_300 .

inst:IfcLengthMeasure_303
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "207.772813"^^xsd:double .

inst:IfcLengthMeasure_List_300
        list:hasContents  inst:IfcLengthMeasure_303 ;
        list:hasNext      inst:IfcLengthMeasure_List_301 .

inst:IfcLengthMeasure_List_301
        list:hasContents  inst:IfcLengthMeasure_298 .

inst:IfcLengthMeasure_List_304
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_305
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_306
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_307
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-105.240203"^^xsd:double .

inst:IfcLengthMeasure_List_304
        list:hasContents  inst:IfcLengthMeasure_307 ;
        list:hasNext      inst:IfcLengthMeasure_List_305 .

inst:IfcLengthMeasure_308
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "227.552281"^^xsd:double .

inst:IfcLengthMeasure_List_305
        list:hasContents  inst:IfcLengthMeasure_308 ;
        list:hasNext      inst:IfcLengthMeasure_List_306 .

inst:IfcLengthMeasure_List_306
        list:hasContents  inst:IfcLengthMeasure_298 .

inst:IfcLengthMeasure_List_309
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_310
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_311
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_312
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-71.061875"^^xsd:double .

inst:IfcLengthMeasure_List_309
        list:hasContents  inst:IfcLengthMeasure_312 ;
        list:hasNext      inst:IfcLengthMeasure_List_310 .

inst:IfcLengthMeasure_313
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "239.383609"^^xsd:double .

inst:IfcLengthMeasure_List_310
        list:hasContents  inst:IfcLengthMeasure_313 ;
        list:hasNext      inst:IfcLengthMeasure_List_311 .

inst:IfcLengthMeasure_List_311
        list:hasContents  inst:IfcLengthMeasure_298 .

inst:IfcLengthMeasure_List_314
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_315
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_316
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_317
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-35.805801"^^xsd:double .

inst:IfcLengthMeasure_List_314
        list:hasContents  inst:IfcLengthMeasure_317 ;
        list:hasNext      inst:IfcLengthMeasure_List_315 .

inst:IfcLengthMeasure_318
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "245.758375"^^xsd:double .

inst:IfcLengthMeasure_List_315
        list:hasContents  inst:IfcLengthMeasure_318 ;
        list:hasNext      inst:IfcLengthMeasure_List_316 .

inst:IfcLengthMeasure_List_316
        list:hasContents  inst:IfcLengthMeasure_298 .

inst:IfcLengthMeasure_List_319
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_320
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_321
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_322
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "1.198953"^^xsd:double .

inst:IfcLengthMeasure_List_319
        list:hasContents  inst:IfcLengthMeasure_322 ;
        list:hasNext      inst:IfcLengthMeasure_List_320 .

inst:IfcLengthMeasure_323
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "247.790172"^^xsd:double .

inst:IfcLengthMeasure_List_320
        list:hasContents  inst:IfcLengthMeasure_323 ;
        list:hasNext      inst:IfcLengthMeasure_List_321 .

inst:IfcLengthMeasure_List_321
        list:hasContents  inst:IfcLengthMeasure_298 .

inst:IfcLengthMeasure_List_324
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_325
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_326
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_327
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "38.145594"^^xsd:double .

inst:IfcLengthMeasure_List_324
        list:hasContents  inst:IfcLengthMeasure_327 ;
        list:hasNext      inst:IfcLengthMeasure_List_325 .

inst:IfcLengthMeasure_328
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "245.479016"^^xsd:double .

inst:IfcLengthMeasure_List_325
        list:hasContents  inst:IfcLengthMeasure_328 ;
        list:hasNext      inst:IfcLengthMeasure_List_326 .

inst:IfcLengthMeasure_List_326
        list:hasContents  inst:IfcLengthMeasure_298 .

inst:IfcLengthMeasure_List_329
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_330
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_331
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_332
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "73.227336"^^xsd:double .

inst:IfcLengthMeasure_List_329
        list:hasContents  inst:IfcLengthMeasure_332 ;
        list:hasNext      inst:IfcLengthMeasure_List_330 .

inst:IfcLengthMeasure_333
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "238.824875"^^xsd:double .

inst:IfcLengthMeasure_List_330
        list:hasContents  inst:IfcLengthMeasure_333 ;
        list:hasNext      inst:IfcLengthMeasure_List_331 .

inst:IfcLengthMeasure_List_331
        list:hasContents  inst:IfcLengthMeasure_298 .

inst:IfcLengthMeasure_List_334
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_335
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_336
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_337
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "105.385414"^^xsd:double .

inst:IfcLengthMeasure_List_334
        list:hasContents  inst:IfcLengthMeasure_337 ;
        list:hasNext      inst:IfcLengthMeasure_List_335 .

inst:IfcLengthMeasure_338
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "227.485469"^^xsd:double .

inst:IfcLengthMeasure_List_335
        list:hasContents  inst:IfcLengthMeasure_338 ;
        list:hasNext      inst:IfcLengthMeasure_List_336 .

inst:IfcLengthMeasure_List_336
        list:hasContents  inst:IfcLengthMeasure_298 .

inst:IfcLengthMeasure_List_339
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_340
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_341
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_342
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "135.792813"^^xsd:double .

inst:IfcLengthMeasure_List_339
        list:hasContents  inst:IfcLengthMeasure_342 ;
        list:hasNext      inst:IfcLengthMeasure_List_340 .

inst:IfcLengthMeasure_343
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "208.145344"^^xsd:double .

inst:IfcLengthMeasure_List_340
        list:hasContents  inst:IfcLengthMeasure_343 ;
        list:hasNext      inst:IfcLengthMeasure_List_341 .

inst:IfcLengthMeasure_List_341
        list:hasContents  inst:IfcLengthMeasure_298 .

inst:IfcLengthMeasure_List_344
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_345
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_346
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_347
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "157.154922"^^xsd:double .

inst:IfcLengthMeasure_List_344
        list:hasContents  inst:IfcLengthMeasure_347 ;
        list:hasNext      inst:IfcLengthMeasure_List_345 .

inst:IfcLengthMeasure_List_345
        list:hasContents  inst:IfcLengthMeasure_297 ;
        list:hasNext      inst:IfcLengthMeasure_List_346 .

inst:IfcLengthMeasure_List_346
        list:hasContents  inst:IfcLengthMeasure_298 .

inst:IfcLengthMeasure_List_348
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_349
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_350
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_351
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "157.742547"^^xsd:double .

inst:IfcLengthMeasure_List_348
        list:hasContents  inst:IfcLengthMeasure_351 ;
        list:hasNext      inst:IfcLengthMeasure_List_349 .

inst:IfcLengthMeasure_352
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "136.356797"^^xsd:double .

inst:IfcLengthMeasure_List_349
        list:hasContents  inst:IfcLengthMeasure_352 ;
        list:hasNext      inst:IfcLengthMeasure_List_350 .

inst:IfcLengthMeasure_List_350
        list:hasContents  inst:IfcLengthMeasure_298 .

inst:IfcLengthMeasure_List_353
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_354
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_355
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_356
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "143.915969"^^xsd:double .

inst:IfcLengthMeasure_List_353
        list:hasContents  inst:IfcLengthMeasure_356 ;
        list:hasNext      inst:IfcLengthMeasure_List_354 .

inst:IfcLengthMeasure_357
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "97.9355"^^xsd:double .

inst:IfcLengthMeasure_List_354
        list:hasContents  inst:IfcLengthMeasure_357 ;
        list:hasNext      inst:IfcLengthMeasure_List_355 .

inst:IfcLengthMeasure_List_355
        list:hasContents  inst:IfcLengthMeasure_298 .

inst:IfcLengthMeasure_List_358
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_359
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_360
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_361
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "123.422102"^^xsd:double .

inst:IfcLengthMeasure_List_358
        list:hasContents  inst:IfcLengthMeasure_361 ;
        list:hasNext      inst:IfcLengthMeasure_List_359 .

inst:IfcLengthMeasure_362
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "65.13209"^^xsd:double .

inst:IfcLengthMeasure_List_359
        list:hasContents  inst:IfcLengthMeasure_362 ;
        list:hasNext      inst:IfcLengthMeasure_List_360 .

inst:IfcLengthMeasure_List_360
        list:hasContents  inst:IfcLengthMeasure_298 .

inst:IfcLengthMeasure_List_363
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_364
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_365
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_366
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "97.482477"^^xsd:double .

inst:IfcLengthMeasure_List_363
        list:hasContents  inst:IfcLengthMeasure_366 ;
        list:hasNext      inst:IfcLengthMeasure_List_364 .

inst:IfcLengthMeasure_367
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "35.927559"^^xsd:double .

inst:IfcLengthMeasure_List_364
        list:hasContents  inst:IfcLengthMeasure_367 ;
        list:hasNext      inst:IfcLengthMeasure_List_365 .

inst:IfcLengthMeasure_List_365
        list:hasContents  inst:IfcLengthMeasure_298 .

inst:IfcLengthMeasure_List_368
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_369
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_370
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_371
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "68.171844"^^xsd:double .

inst:IfcLengthMeasure_List_368
        list:hasContents  inst:IfcLengthMeasure_371 ;
        list:hasNext      inst:IfcLengthMeasure_List_369 .

inst:IfcLengthMeasure_372
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "12.864227"^^xsd:double .

inst:IfcLengthMeasure_List_369
        list:hasContents  inst:IfcLengthMeasure_372 ;
        list:hasNext      inst:IfcLengthMeasure_List_370 .

inst:IfcLengthMeasure_List_370
        list:hasContents  inst:IfcLengthMeasure_298 .

inst:IfcLengthMeasure_List_373
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_374
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_375
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_376
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "35.142449"^^xsd:double .

inst:IfcLengthMeasure_List_373
        list:hasContents  inst:IfcLengthMeasure_376 ;
        list:hasNext      inst:IfcLengthMeasure_List_374 .

inst:IfcLengthMeasure_377
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-2.585266"^^xsd:double .

inst:IfcLengthMeasure_List_374
        list:hasContents  inst:IfcLengthMeasure_377 ;
        list:hasNext      inst:IfcLengthMeasure_List_375 .

inst:IfcLengthMeasure_List_375
        list:hasContents  inst:IfcLengthMeasure_298 .

inst:IfcLengthMeasure_List_378
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_379
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_380
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_381
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "0.77384"^^xsd:double .

inst:IfcLengthMeasure_List_378
        list:hasContents  inst:IfcLengthMeasure_381 ;
        list:hasNext      inst:IfcLengthMeasure_List_379 .

inst:IfcLengthMeasure_382
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-8.021682"^^xsd:double .

inst:IfcLengthMeasure_List_379
        list:hasContents  inst:IfcLengthMeasure_382 ;
        list:hasNext      inst:IfcLengthMeasure_List_380 .

inst:IfcLengthMeasure_List_380
        list:hasContents  inst:IfcLengthMeasure_298 .

inst:IfcLengthMeasure_List_383
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_384
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_385
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_386
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-33.731801"^^xsd:double .

inst:IfcLengthMeasure_List_383
        list:hasContents  inst:IfcLengthMeasure_386 ;
        list:hasNext      inst:IfcLengthMeasure_List_384 .

inst:IfcLengthMeasure_387
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-3.015985"^^xsd:double .

inst:IfcLengthMeasure_List_384
        list:hasContents  inst:IfcLengthMeasure_387 ;
        list:hasNext      inst:IfcLengthMeasure_List_385 .

inst:IfcLengthMeasure_List_385
        list:hasContents  inst:IfcLengthMeasure_298 .

inst:IfcLengthMeasure_List_388
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_389
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_390
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_391
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-67.542563"^^xsd:double .

inst:IfcLengthMeasure_List_388
        list:hasContents  inst:IfcLengthMeasure_391 ;
        list:hasNext      inst:IfcLengthMeasure_List_389 .

inst:IfcLengthMeasure_392
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "12.469661"^^xsd:double .

inst:IfcLengthMeasure_List_389
        list:hasContents  inst:IfcLengthMeasure_392 ;
        list:hasNext      inst:IfcLengthMeasure_List_390 .

inst:IfcLengthMeasure_List_390
        list:hasContents  inst:IfcLengthMeasure_298 .

inst:IfcLengthMeasure_List_393
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_394
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_395
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_396
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-97.140859"^^xsd:double .

inst:IfcLengthMeasure_List_393
        list:hasContents  inst:IfcLengthMeasure_396 ;
        list:hasNext      inst:IfcLengthMeasure_List_394 .

inst:IfcLengthMeasure_397
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "35.603637"^^xsd:double .

inst:IfcLengthMeasure_List_394
        list:hasContents  inst:IfcLengthMeasure_397 ;
        list:hasNext      inst:IfcLengthMeasure_List_395 .

inst:IfcLengthMeasure_List_395
        list:hasContents  inst:IfcLengthMeasure_298 .

inst:IfcLengthMeasure_List_398
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_399
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_400
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_401
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-123.498414"^^xsd:double .

inst:IfcLengthMeasure_List_398
        list:hasContents  inst:IfcLengthMeasure_401 ;
        list:hasNext      inst:IfcLengthMeasure_List_399 .

inst:IfcLengthMeasure_402
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "65.233859"^^xsd:double .

inst:IfcLengthMeasure_List_399
        list:hasContents  inst:IfcLengthMeasure_402 ;
        list:hasNext      inst:IfcLengthMeasure_List_400 .

inst:IfcLengthMeasure_List_400
        list:hasContents  inst:IfcLengthMeasure_298 .

inst:IfcLengthMeasure_List_403
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_404
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_405
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_406
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-144.288969"^^xsd:double .

inst:IfcLengthMeasure_List_403
        list:hasContents  inst:IfcLengthMeasure_406 ;
        list:hasNext      inst:IfcLengthMeasure_List_404 .

inst:IfcLengthMeasure_407
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "98.678578"^^xsd:double .

inst:IfcLengthMeasure_List_404
        list:hasContents  inst:IfcLengthMeasure_407 ;
        list:hasNext      inst:IfcLengthMeasure_List_405 .

inst:IfcLengthMeasure_List_405
        list:hasContents  inst:IfcLengthMeasure_298 .

inst:IfcLengthMeasure_List_408
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_409
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_410
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_411
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-157.807906"^^xsd:double .

inst:IfcLengthMeasure_List_408
        list:hasContents  inst:IfcLengthMeasure_411 ;
        list:hasNext      inst:IfcLengthMeasure_List_409 .

inst:IfcLengthMeasure_412
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "136.680281"^^xsd:double .

inst:IfcLengthMeasure_List_409
        list:hasContents  inst:IfcLengthMeasure_412 ;
        list:hasNext      inst:IfcLengthMeasure_List_410 .

inst:IfcLengthMeasure_List_410
        list:hasContents  inst:IfcLengthMeasure_298 .

inst:IfcLengthMeasure_List_413
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_414
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_415
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_413
        list:hasContents  inst:IfcLengthMeasure_58 ;
        list:hasNext      inst:IfcLengthMeasure_List_414 .

inst:IfcLengthMeasure_List_414
        list:hasContents  inst:IfcLengthMeasure_59 ;
        list:hasNext      inst:IfcLengthMeasure_List_415 .

inst:IfcLengthMeasure_List_415
        list:hasContents  inst:IfcLengthMeasure_60 .

inst:IfcLengthMeasure_List_416
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_417
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_418
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_419
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-228.577453"^^xsd:double .

inst:IfcLengthMeasure_List_416
        list:hasContents  inst:IfcLengthMeasure_419 ;
        list:hasNext      inst:IfcLengthMeasure_List_417 .

inst:IfcLengthMeasure_420
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "162.904313"^^xsd:double .

inst:IfcLengthMeasure_List_417
        list:hasContents  inst:IfcLengthMeasure_420 ;
        list:hasNext      inst:IfcLengthMeasure_List_418 .

inst:IfcLengthMeasure_421
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-47.0"^^xsd:double .

inst:IfcLengthMeasure_List_418
        list:hasContents  inst:IfcLengthMeasure_421 .

inst:IfcLengthMeasure_List_422
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_423
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_424
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_422
        list:hasContents  inst:IfcLengthMeasure_296 ;
        list:hasNext      inst:IfcLengthMeasure_List_423 .

inst:IfcLengthMeasure_List_423
        list:hasContents  inst:IfcLengthMeasure_297 ;
        list:hasNext      inst:IfcLengthMeasure_List_424 .

inst:IfcLengthMeasure_List_424
        list:hasContents  inst:IfcLengthMeasure_298 .

inst:IfcLengthMeasure_List_425
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_426
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_427
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_425
        list:hasContents  inst:IfcLengthMeasure_64 ;
        list:hasNext      inst:IfcLengthMeasure_List_426 .

inst:IfcLengthMeasure_List_426
        list:hasContents  inst:IfcLengthMeasure_65 ;
        list:hasNext      inst:IfcLengthMeasure_List_427 .

inst:IfcLengthMeasure_List_427
        list:hasContents  inst:IfcLengthMeasure_60 .

inst:IfcLengthMeasure_List_428
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_429
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_430
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_428
        list:hasContents  inst:IfcLengthMeasure_302 ;
        list:hasNext      inst:IfcLengthMeasure_List_429 .

inst:IfcLengthMeasure_List_429
        list:hasContents  inst:IfcLengthMeasure_303 ;
        list:hasNext      inst:IfcLengthMeasure_List_430 .

inst:IfcLengthMeasure_List_430
        list:hasContents  inst:IfcLengthMeasure_298 .

inst:IfcLengthMeasure_List_431
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_432
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_433
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_431
        list:hasContents  inst:IfcLengthMeasure_69 ;
        list:hasNext      inst:IfcLengthMeasure_List_432 .

inst:IfcLengthMeasure_List_432
        list:hasContents  inst:IfcLengthMeasure_70 ;
        list:hasNext      inst:IfcLengthMeasure_List_433 .

inst:IfcLengthMeasure_List_433
        list:hasContents  inst:IfcLengthMeasure_60 .

inst:IfcLengthMeasure_List_434
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_435
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_436
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_434
        list:hasContents  inst:IfcLengthMeasure_307 ;
        list:hasNext      inst:IfcLengthMeasure_List_435 .

inst:IfcLengthMeasure_List_435
        list:hasContents  inst:IfcLengthMeasure_308 ;
        list:hasNext      inst:IfcLengthMeasure_List_436 .

inst:IfcLengthMeasure_List_436
        list:hasContents  inst:IfcLengthMeasure_298 .

inst:IfcLengthMeasure_List_437
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_438
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_439
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_437
        list:hasContents  inst:IfcLengthMeasure_74 ;
        list:hasNext      inst:IfcLengthMeasure_List_438 .

inst:IfcLengthMeasure_List_438
        list:hasContents  inst:IfcLengthMeasure_75 ;
        list:hasNext      inst:IfcLengthMeasure_List_439 .

inst:IfcLengthMeasure_List_439
        list:hasContents  inst:IfcLengthMeasure_60 .

inst:IfcLengthMeasure_List_440
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_441
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_442
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_440
        list:hasContents  inst:IfcLengthMeasure_312 ;
        list:hasNext      inst:IfcLengthMeasure_List_441 .

inst:IfcLengthMeasure_List_441
        list:hasContents  inst:IfcLengthMeasure_313 ;
        list:hasNext      inst:IfcLengthMeasure_List_442 .

inst:IfcLengthMeasure_List_442
        list:hasContents  inst:IfcLengthMeasure_298 .

inst:IfcLengthMeasure_List_443
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_444
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_445
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_443
        list:hasContents  inst:IfcLengthMeasure_79 ;
        list:hasNext      inst:IfcLengthMeasure_List_444 .

inst:IfcLengthMeasure_List_444
        list:hasContents  inst:IfcLengthMeasure_80 ;
        list:hasNext      inst:IfcLengthMeasure_List_445 .

inst:IfcLengthMeasure_List_445
        list:hasContents  inst:IfcLengthMeasure_60 .

inst:IfcLengthMeasure_List_446
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_447
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_448
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_446
        list:hasContents  inst:IfcLengthMeasure_317 ;
        list:hasNext      inst:IfcLengthMeasure_List_447 .

inst:IfcLengthMeasure_List_447
        list:hasContents  inst:IfcLengthMeasure_318 ;
        list:hasNext      inst:IfcLengthMeasure_List_448 .

inst:IfcLengthMeasure_List_448
        list:hasContents  inst:IfcLengthMeasure_298 .

inst:IfcLengthMeasure_List_449
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_450
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_451
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_449
        list:hasContents  inst:IfcLengthMeasure_84 ;
        list:hasNext      inst:IfcLengthMeasure_List_450 .

inst:IfcLengthMeasure_List_450
        list:hasContents  inst:IfcLengthMeasure_85 ;
        list:hasNext      inst:IfcLengthMeasure_List_451 .

inst:IfcLengthMeasure_List_451
        list:hasContents  inst:IfcLengthMeasure_60 .

inst:IfcLengthMeasure_List_452
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_453
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_454
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_452
        list:hasContents  inst:IfcLengthMeasure_322 ;
        list:hasNext      inst:IfcLengthMeasure_List_453 .

inst:IfcLengthMeasure_List_453
        list:hasContents  inst:IfcLengthMeasure_323 ;
        list:hasNext      inst:IfcLengthMeasure_List_454 .

inst:IfcLengthMeasure_List_454
        list:hasContents  inst:IfcLengthMeasure_298 .

inst:IfcLengthMeasure_List_455
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_456
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_457
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_455
        list:hasContents  inst:IfcLengthMeasure_89 ;
        list:hasNext      inst:IfcLengthMeasure_List_456 .

inst:IfcLengthMeasure_List_456
        list:hasContents  inst:IfcLengthMeasure_90 ;
        list:hasNext      inst:IfcLengthMeasure_List_457 .

inst:IfcLengthMeasure_List_457
        list:hasContents  inst:IfcLengthMeasure_60 .

inst:IfcLengthMeasure_List_458
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_459
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_460
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_458
        list:hasContents  inst:IfcLengthMeasure_327 ;
        list:hasNext      inst:IfcLengthMeasure_List_459 .

inst:IfcLengthMeasure_List_459
        list:hasContents  inst:IfcLengthMeasure_328 ;
        list:hasNext      inst:IfcLengthMeasure_List_460 .

inst:IfcLengthMeasure_List_460
        list:hasContents  inst:IfcLengthMeasure_298 .

inst:IfcLengthMeasure_List_461
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_462
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_463
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_461
        list:hasContents  inst:IfcLengthMeasure_94 ;
        list:hasNext      inst:IfcLengthMeasure_List_462 .

inst:IfcLengthMeasure_List_462
        list:hasContents  inst:IfcLengthMeasure_95 ;
        list:hasNext      inst:IfcLengthMeasure_List_463 .

inst:IfcLengthMeasure_List_463
        list:hasContents  inst:IfcLengthMeasure_60 .

inst:IfcLengthMeasure_List_464
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_465
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_466
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_464
        list:hasContents  inst:IfcLengthMeasure_332 ;
        list:hasNext      inst:IfcLengthMeasure_List_465 .

inst:IfcLengthMeasure_List_465
        list:hasContents  inst:IfcLengthMeasure_333 ;
        list:hasNext      inst:IfcLengthMeasure_List_466 .

inst:IfcLengthMeasure_List_466
        list:hasContents  inst:IfcLengthMeasure_298 .

inst:IfcLengthMeasure_List_467
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_468
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_469
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_467
        list:hasContents  inst:IfcLengthMeasure_99 ;
        list:hasNext      inst:IfcLengthMeasure_List_468 .

inst:IfcLengthMeasure_List_468
        list:hasContents  inst:IfcLengthMeasure_100 ;
        list:hasNext      inst:IfcLengthMeasure_List_469 .

inst:IfcLengthMeasure_List_469
        list:hasContents  inst:IfcLengthMeasure_60 .

inst:IfcLengthMeasure_List_470
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_471
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_472
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_470
        list:hasContents  inst:IfcLengthMeasure_337 ;
        list:hasNext      inst:IfcLengthMeasure_List_471 .

inst:IfcLengthMeasure_List_471
        list:hasContents  inst:IfcLengthMeasure_338 ;
        list:hasNext      inst:IfcLengthMeasure_List_472 .

inst:IfcLengthMeasure_List_472
        list:hasContents  inst:IfcLengthMeasure_298 .

inst:IfcLengthMeasure_List_473
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_474
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_475
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_473
        list:hasContents  inst:IfcLengthMeasure_104 ;
        list:hasNext      inst:IfcLengthMeasure_List_474 .

inst:IfcLengthMeasure_List_474
        list:hasContents  inst:IfcLengthMeasure_105 ;
        list:hasNext      inst:IfcLengthMeasure_List_475 .

inst:IfcLengthMeasure_List_475
        list:hasContents  inst:IfcLengthMeasure_60 .

inst:IfcLengthMeasure_List_476
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_477
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_478
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_476
        list:hasContents  inst:IfcLengthMeasure_342 ;
        list:hasNext      inst:IfcLengthMeasure_List_477 .

inst:IfcLengthMeasure_List_477
        list:hasContents  inst:IfcLengthMeasure_343 ;
        list:hasNext      inst:IfcLengthMeasure_List_478 .

inst:IfcLengthMeasure_List_478
        list:hasContents  inst:IfcLengthMeasure_298 .

inst:IfcLengthMeasure_List_479
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_480
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_481
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_479
        list:hasContents  inst:IfcLengthMeasure_109 ;
        list:hasNext      inst:IfcLengthMeasure_List_480 .

inst:IfcLengthMeasure_List_480
        list:hasContents  inst:IfcLengthMeasure_59 ;
        list:hasNext      inst:IfcLengthMeasure_List_481 .

inst:IfcLengthMeasure_List_481
        list:hasContents  inst:IfcLengthMeasure_60 .

inst:IfcLengthMeasure_List_482
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_483
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_484
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_482
        list:hasContents  inst:IfcLengthMeasure_347 ;
        list:hasNext      inst:IfcLengthMeasure_List_483 .

inst:IfcLengthMeasure_List_483
        list:hasContents  inst:IfcLengthMeasure_297 ;
        list:hasNext      inst:IfcLengthMeasure_List_484 .

inst:IfcLengthMeasure_List_484
        list:hasContents  inst:IfcLengthMeasure_298 .

inst:IfcLengthMeasure_List_485
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_486
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_487
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_485
        list:hasContents  inst:IfcLengthMeasure_113 ;
        list:hasNext      inst:IfcLengthMeasure_List_486 .

inst:IfcLengthMeasure_List_486
        list:hasContents  inst:IfcLengthMeasure_114 ;
        list:hasNext      inst:IfcLengthMeasure_List_487 .

inst:IfcLengthMeasure_List_487
        list:hasContents  inst:IfcLengthMeasure_60 .

inst:IfcLengthMeasure_List_488
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_489
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_490
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_488
        list:hasContents  inst:IfcLengthMeasure_351 ;
        list:hasNext      inst:IfcLengthMeasure_List_489 .

inst:IfcLengthMeasure_List_489
        list:hasContents  inst:IfcLengthMeasure_352 ;
        list:hasNext      inst:IfcLengthMeasure_List_490 .

inst:IfcLengthMeasure_List_490
        list:hasContents  inst:IfcLengthMeasure_298 .

inst:IfcLengthMeasure_List_491
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_492
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_493
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_491
        list:hasContents  inst:IfcLengthMeasure_118 ;
        list:hasNext      inst:IfcLengthMeasure_List_492 .

inst:IfcLengthMeasure_List_492
        list:hasContents  inst:IfcLengthMeasure_119 ;
        list:hasNext      inst:IfcLengthMeasure_List_493 .

inst:IfcLengthMeasure_List_493
        list:hasContents  inst:IfcLengthMeasure_60 .

inst:IfcLengthMeasure_List_494
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_495
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_496
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_494
        list:hasContents  inst:IfcLengthMeasure_356 ;
        list:hasNext      inst:IfcLengthMeasure_List_495 .

inst:IfcLengthMeasure_List_495
        list:hasContents  inst:IfcLengthMeasure_357 ;
        list:hasNext      inst:IfcLengthMeasure_List_496 .

inst:IfcLengthMeasure_List_496
        list:hasContents  inst:IfcLengthMeasure_298 .

inst:IfcLengthMeasure_List_497
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_498
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_499
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_497
        list:hasContents  inst:IfcLengthMeasure_123 ;
        list:hasNext      inst:IfcLengthMeasure_List_498 .

inst:IfcLengthMeasure_List_498
        list:hasContents  inst:IfcLengthMeasure_124 ;
        list:hasNext      inst:IfcLengthMeasure_List_499 .

inst:IfcLengthMeasure_List_499
        list:hasContents  inst:IfcLengthMeasure_60 .

inst:IfcLengthMeasure_List_500
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_501
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_502
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_500
        list:hasContents  inst:IfcLengthMeasure_361 ;
        list:hasNext      inst:IfcLengthMeasure_List_501 .

inst:IfcLengthMeasure_List_501
        list:hasContents  inst:IfcLengthMeasure_362 ;
        list:hasNext      inst:IfcLengthMeasure_List_502 .

inst:IfcLengthMeasure_List_502
        list:hasContents  inst:IfcLengthMeasure_298 .

inst:IfcLengthMeasure_List_503
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_504
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_505
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_503
        list:hasContents  inst:IfcLengthMeasure_128 ;
        list:hasNext      inst:IfcLengthMeasure_List_504 .

inst:IfcLengthMeasure_List_504
        list:hasContents  inst:IfcLengthMeasure_129 ;
        list:hasNext      inst:IfcLengthMeasure_List_505 .

inst:IfcLengthMeasure_List_505
        list:hasContents  inst:IfcLengthMeasure_60 .

inst:IfcLengthMeasure_List_506
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_507
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_508
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_506
        list:hasContents  inst:IfcLengthMeasure_366 ;
        list:hasNext      inst:IfcLengthMeasure_List_507 .

inst:IfcLengthMeasure_List_507
        list:hasContents  inst:IfcLengthMeasure_367 ;
        list:hasNext      inst:IfcLengthMeasure_List_508 .

inst:IfcLengthMeasure_List_508
        list:hasContents  inst:IfcLengthMeasure_298 .

inst:IfcLengthMeasure_List_509
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_510
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_511
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_509
        list:hasContents  inst:IfcLengthMeasure_133 ;
        list:hasNext      inst:IfcLengthMeasure_List_510 .

inst:IfcLengthMeasure_List_510
        list:hasContents  inst:IfcLengthMeasure_134 ;
        list:hasNext      inst:IfcLengthMeasure_List_511 .

inst:IfcLengthMeasure_List_511
        list:hasContents  inst:IfcLengthMeasure_60 .

inst:IfcLengthMeasure_List_512
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_513
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_514
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_512
        list:hasContents  inst:IfcLengthMeasure_371 ;
        list:hasNext      inst:IfcLengthMeasure_List_513 .

inst:IfcLengthMeasure_List_513
        list:hasContents  inst:IfcLengthMeasure_372 ;
        list:hasNext      inst:IfcLengthMeasure_List_514 .

inst:IfcLengthMeasure_List_514
        list:hasContents  inst:IfcLengthMeasure_298 .

inst:IfcLengthMeasure_List_515
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_516
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_517
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_515
        list:hasContents  inst:IfcLengthMeasure_138 ;
        list:hasNext      inst:IfcLengthMeasure_List_516 .

inst:IfcLengthMeasure_List_516
        list:hasContents  inst:IfcLengthMeasure_139 ;
        list:hasNext      inst:IfcLengthMeasure_List_517 .

inst:IfcLengthMeasure_List_517
        list:hasContents  inst:IfcLengthMeasure_60 .

inst:IfcLengthMeasure_List_518
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_519
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_520
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_518
        list:hasContents  inst:IfcLengthMeasure_376 ;
        list:hasNext      inst:IfcLengthMeasure_List_519 .

inst:IfcLengthMeasure_List_519
        list:hasContents  inst:IfcLengthMeasure_377 ;
        list:hasNext      inst:IfcLengthMeasure_List_520 .

inst:IfcLengthMeasure_List_520
        list:hasContents  inst:IfcLengthMeasure_298 .

inst:IfcLengthMeasure_List_521
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_522
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_523
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_521
        list:hasContents  inst:IfcLengthMeasure_143 ;
        list:hasNext      inst:IfcLengthMeasure_List_522 .

inst:IfcLengthMeasure_List_522
        list:hasContents  inst:IfcLengthMeasure_144 ;
        list:hasNext      inst:IfcLengthMeasure_List_523 .

inst:IfcLengthMeasure_List_523
        list:hasContents  inst:IfcLengthMeasure_60 .

inst:IfcLengthMeasure_List_524
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_525
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_526
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_524
        list:hasContents  inst:IfcLengthMeasure_381 ;
        list:hasNext      inst:IfcLengthMeasure_List_525 .

inst:IfcLengthMeasure_List_525
        list:hasContents  inst:IfcLengthMeasure_382 ;
        list:hasNext      inst:IfcLengthMeasure_List_526 .

inst:IfcLengthMeasure_List_526
        list:hasContents  inst:IfcLengthMeasure_298 .

inst:IfcLengthMeasure_List_527
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_528
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_529
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_527
        list:hasContents  inst:IfcLengthMeasure_148 ;
        list:hasNext      inst:IfcLengthMeasure_List_528 .

inst:IfcLengthMeasure_List_528
        list:hasContents  inst:IfcLengthMeasure_149 ;
        list:hasNext      inst:IfcLengthMeasure_List_529 .

inst:IfcLengthMeasure_List_529
        list:hasContents  inst:IfcLengthMeasure_60 .

inst:IfcLengthMeasure_List_530
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_531
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_532
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_530
        list:hasContents  inst:IfcLengthMeasure_386 ;
        list:hasNext      inst:IfcLengthMeasure_List_531 .

inst:IfcLengthMeasure_List_531
        list:hasContents  inst:IfcLengthMeasure_387 ;
        list:hasNext      inst:IfcLengthMeasure_List_532 .

inst:IfcLengthMeasure_List_532
        list:hasContents  inst:IfcLengthMeasure_298 .

inst:IfcLengthMeasure_List_533
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_534
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_535
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_533
        list:hasContents  inst:IfcLengthMeasure_153 ;
        list:hasNext      inst:IfcLengthMeasure_List_534 .

inst:IfcLengthMeasure_List_534
        list:hasContents  inst:IfcLengthMeasure_154 ;
        list:hasNext      inst:IfcLengthMeasure_List_535 .

inst:IfcLengthMeasure_List_535
        list:hasContents  inst:IfcLengthMeasure_60 .

inst:IfcLengthMeasure_List_536
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_537
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_538
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_536
        list:hasContents  inst:IfcLengthMeasure_391 ;
        list:hasNext      inst:IfcLengthMeasure_List_537 .

inst:IfcLengthMeasure_List_537
        list:hasContents  inst:IfcLengthMeasure_392 ;
        list:hasNext      inst:IfcLengthMeasure_List_538 .

inst:IfcLengthMeasure_List_538
        list:hasContents  inst:IfcLengthMeasure_298 .

inst:IfcLengthMeasure_List_539
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_540
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_541
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_539
        list:hasContents  inst:IfcLengthMeasure_158 ;
        list:hasNext      inst:IfcLengthMeasure_List_540 .

inst:IfcLengthMeasure_List_540
        list:hasContents  inst:IfcLengthMeasure_159 ;
        list:hasNext      inst:IfcLengthMeasure_List_541 .

inst:IfcLengthMeasure_List_541
        list:hasContents  inst:IfcLengthMeasure_60 .

inst:IfcLengthMeasure_List_542
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_543
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_544
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_542
        list:hasContents  inst:IfcLengthMeasure_396 ;
        list:hasNext      inst:IfcLengthMeasure_List_543 .

inst:IfcLengthMeasure_List_543
        list:hasContents  inst:IfcLengthMeasure_397 ;
        list:hasNext      inst:IfcLengthMeasure_List_544 .

inst:IfcLengthMeasure_List_544
        list:hasContents  inst:IfcLengthMeasure_298 .

inst:IfcLengthMeasure_List_545
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_546
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_547
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_545
        list:hasContents  inst:IfcLengthMeasure_163 ;
        list:hasNext      inst:IfcLengthMeasure_List_546 .

inst:IfcLengthMeasure_List_546
        list:hasContents  inst:IfcLengthMeasure_164 ;
        list:hasNext      inst:IfcLengthMeasure_List_547 .

inst:IfcLengthMeasure_List_547
        list:hasContents  inst:IfcLengthMeasure_60 .

inst:IfcLengthMeasure_List_548
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_549
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_550
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_548
        list:hasContents  inst:IfcLengthMeasure_401 ;
        list:hasNext      inst:IfcLengthMeasure_List_549 .

inst:IfcLengthMeasure_List_549
        list:hasContents  inst:IfcLengthMeasure_402 ;
        list:hasNext      inst:IfcLengthMeasure_List_550 .

inst:IfcLengthMeasure_List_550
        list:hasContents  inst:IfcLengthMeasure_298 .

inst:IfcLengthMeasure_List_551
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_552
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_553
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_551
        list:hasContents  inst:IfcLengthMeasure_168 ;
        list:hasNext      inst:IfcLengthMeasure_List_552 .

inst:IfcLengthMeasure_List_552
        list:hasContents  inst:IfcLengthMeasure_169 ;
        list:hasNext      inst:IfcLengthMeasure_List_553 .

inst:IfcLengthMeasure_List_553
        list:hasContents  inst:IfcLengthMeasure_60 .

inst:IfcLengthMeasure_List_554
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_555
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_556
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_554
        list:hasContents  inst:IfcLengthMeasure_406 ;
        list:hasNext      inst:IfcLengthMeasure_List_555 .

inst:IfcLengthMeasure_List_555
        list:hasContents  inst:IfcLengthMeasure_407 ;
        list:hasNext      inst:IfcLengthMeasure_List_556 .

inst:IfcLengthMeasure_List_556
        list:hasContents  inst:IfcLengthMeasure_298 .

inst:IfcLengthMeasure_List_557
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_558
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_559
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_557
        list:hasContents  inst:IfcLengthMeasure_173 ;
        list:hasNext      inst:IfcLengthMeasure_List_558 .

inst:IfcLengthMeasure_List_558
        list:hasContents  inst:IfcLengthMeasure_174 ;
        list:hasNext      inst:IfcLengthMeasure_List_559 .

inst:IfcLengthMeasure_List_559
        list:hasContents  inst:IfcLengthMeasure_60 .

inst:IfcLengthMeasure_List_560
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_561
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_562
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_560
        list:hasContents  inst:IfcLengthMeasure_411 ;
        list:hasNext      inst:IfcLengthMeasure_List_561 .

inst:IfcLengthMeasure_List_561
        list:hasContents  inst:IfcLengthMeasure_412 ;
        list:hasNext      inst:IfcLengthMeasure_List_562 .

inst:IfcLengthMeasure_List_562
        list:hasContents  inst:IfcLengthMeasure_298 .

inst:IfcLengthMeasure_List_563
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_564
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_565
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_563
        list:hasContents  inst:IfcLengthMeasure_58 ;
        list:hasNext      inst:IfcLengthMeasure_List_564 .

inst:IfcLengthMeasure_List_564
        list:hasContents  inst:IfcLengthMeasure_59 ;
        list:hasNext      inst:IfcLengthMeasure_List_565 .

inst:IfcLengthMeasure_List_565
        list:hasContents  inst:IfcLengthMeasure_60 .

inst:IfcLengthMeasure_List_566
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_567
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_568
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_566
        list:hasContents  inst:IfcLengthMeasure_419 ;
        list:hasNext      inst:IfcLengthMeasure_List_567 .

inst:IfcLengthMeasure_List_567
        list:hasContents  inst:IfcLengthMeasure_420 ;
        list:hasNext      inst:IfcLengthMeasure_List_568 .

inst:IfcLengthMeasure_List_568
        list:hasContents  inst:IfcLengthMeasure_421 .

inst:IfcLengthMeasure_List_569
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_570
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_571
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_569
        list:hasContents  inst:IfcLengthMeasure_296 ;
        list:hasNext      inst:IfcLengthMeasure_List_570 .

inst:IfcLengthMeasure_List_570
        list:hasContents  inst:IfcLengthMeasure_297 ;
        list:hasNext      inst:IfcLengthMeasure_List_571 .

inst:IfcLengthMeasure_List_571
        list:hasContents  inst:IfcLengthMeasure_298 .

inst:IfcLengthMeasure_List_572
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_573
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_574
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_575
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-103.357523"^^xsd:double .

inst:IfcLengthMeasure_List_572
        list:hasContents  inst:IfcLengthMeasure_575 ;
        list:hasNext      inst:IfcLengthMeasure_List_573 .

inst:IfcLengthMeasure_576
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "247.172063"^^xsd:double .

inst:IfcLengthMeasure_List_573
        list:hasContents  inst:IfcLengthMeasure_576 ;
        list:hasNext      inst:IfcLengthMeasure_List_574 .

inst:IfcLengthMeasure_List_574
        list:hasContents  inst:IfcLengthMeasure_421 .

inst:IfcLengthMeasure_List_577
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_578
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_579
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_580
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-153.068953"^^xsd:double .

inst:IfcLengthMeasure_List_577
        list:hasContents  inst:IfcLengthMeasure_580 ;
        list:hasNext      inst:IfcLengthMeasure_List_578 .

inst:IfcLengthMeasure_581
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "231.489813"^^xsd:double .

inst:IfcLengthMeasure_List_578
        list:hasContents  inst:IfcLengthMeasure_581 ;
        list:hasNext      inst:IfcLengthMeasure_List_579 .

inst:IfcLengthMeasure_List_579
        list:hasContents  inst:IfcLengthMeasure_421 .

inst:IfcLengthMeasure_List_582
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_583
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_584
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_585
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-52.078543"^^xsd:double .

inst:IfcLengthMeasure_List_582
        list:hasContents  inst:IfcLengthMeasure_585 ;
        list:hasNext      inst:IfcLengthMeasure_List_583 .

inst:IfcLengthMeasure_586
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "255.621719"^^xsd:double .

inst:IfcLengthMeasure_List_583
        list:hasContents  inst:IfcLengthMeasure_586 ;
        list:hasNext      inst:IfcLengthMeasure_List_584 .

inst:IfcLengthMeasure_List_584
        list:hasContents  inst:IfcLengthMeasure_421 .

inst:IfcLengthMeasure_List_587
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_588
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_589
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_590
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "1.743843"^^xsd:double .

inst:IfcLengthMeasure_List_587
        list:hasContents  inst:IfcLengthMeasure_590 ;
        list:hasNext      inst:IfcLengthMeasure_List_588 .

inst:IfcLengthMeasure_591
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "258.314844"^^xsd:double .

inst:IfcLengthMeasure_List_588
        list:hasContents  inst:IfcLengthMeasure_591 ;
        list:hasNext      inst:IfcLengthMeasure_List_589 .

inst:IfcLengthMeasure_List_589
        list:hasContents  inst:IfcLengthMeasure_421 .

inst:IfcLengthMeasure_List_592
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_593
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_594
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_595
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "55.481707"^^xsd:double .

inst:IfcLengthMeasure_List_592
        list:hasContents  inst:IfcLengthMeasure_595 ;
        list:hasNext      inst:IfcLengthMeasure_List_593 .

inst:IfcLengthMeasure_596
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "255.251438"^^xsd:double .

inst:IfcLengthMeasure_List_593
        list:hasContents  inst:IfcLengthMeasure_596 ;
        list:hasNext      inst:IfcLengthMeasure_List_594 .

inst:IfcLengthMeasure_List_594
        list:hasContents  inst:IfcLengthMeasure_421 .

inst:IfcLengthMeasure_List_597
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_598
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_599
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_600
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "106.507117"^^xsd:double .

inst:IfcLengthMeasure_List_597
        list:hasContents  inst:IfcLengthMeasure_600 ;
        list:hasNext      inst:IfcLengthMeasure_List_598 .

inst:IfcLengthMeasure_601
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "246.431469"^^xsd:double .

inst:IfcLengthMeasure_List_598
        list:hasContents  inst:IfcLengthMeasure_601 ;
        list:hasNext      inst:IfcLengthMeasure_List_599 .

inst:IfcLengthMeasure_List_599
        list:hasContents  inst:IfcLengthMeasure_421 .

inst:IfcLengthMeasure_List_602
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_603
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_604
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_605
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "197.506875"^^xsd:double .

inst:IfcLengthMeasure_List_602
        list:hasContents  inst:IfcLengthMeasure_605 ;
        list:hasNext      inst:IfcLengthMeasure_List_603 .

inst:IfcLengthMeasure_606
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "205.766188"^^xsd:double .

inst:IfcLengthMeasure_List_603
        list:hasContents  inst:IfcLengthMeasure_606 ;
        list:hasNext      inst:IfcLengthMeasure_List_604 .

inst:IfcLengthMeasure_List_604
        list:hasContents  inst:IfcLengthMeasure_421 .

inst:IfcLengthMeasure_List_607
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_608
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_609
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_610
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "153.280156"^^xsd:double .

inst:IfcLengthMeasure_List_607
        list:hasContents  inst:IfcLengthMeasure_610 ;
        list:hasNext      inst:IfcLengthMeasure_List_608 .

inst:IfcLengthMeasure_611
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "231.40125"^^xsd:double .

inst:IfcLengthMeasure_List_608
        list:hasContents  inst:IfcLengthMeasure_611 ;
        list:hasNext      inst:IfcLengthMeasure_List_609 .

inst:IfcLengthMeasure_List_609
        list:hasContents  inst:IfcLengthMeasure_421 .

inst:IfcLengthMeasure_List_612
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_613
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_614
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_615
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "228.577453"^^xsd:double .

inst:IfcLengthMeasure_List_612
        list:hasContents  inst:IfcLengthMeasure_615 ;
        list:hasNext      inst:IfcLengthMeasure_List_613 .

inst:IfcLengthMeasure_List_613
        list:hasContents  inst:IfcLengthMeasure_420 ;
        list:hasNext      inst:IfcLengthMeasure_List_614 .

inst:IfcLengthMeasure_List_614
        list:hasContents  inst:IfcLengthMeasure_421 .

inst:IfcLengthMeasure_List_616
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_617
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_618
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_619
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "229.432141"^^xsd:double .

inst:IfcLengthMeasure_List_616
        list:hasContents  inst:IfcLengthMeasure_619 ;
        list:hasNext      inst:IfcLengthMeasure_List_617 .

inst:IfcLengthMeasure_620
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "110.611469"^^xsd:double .

inst:IfcLengthMeasure_List_617
        list:hasContents  inst:IfcLengthMeasure_620 ;
        list:hasNext      inst:IfcLengthMeasure_List_618 .

inst:IfcLengthMeasure_List_618
        list:hasContents  inst:IfcLengthMeasure_421 .

inst:IfcLengthMeasure_List_621
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_622
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_623
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_624
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "209.321781"^^xsd:double .

inst:IfcLengthMeasure_List_621
        list:hasContents  inst:IfcLengthMeasure_624 ;
        list:hasNext      inst:IfcLengthMeasure_List_622 .

inst:IfcLengthMeasure_625
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "59.684586"^^xsd:double .

inst:IfcLengthMeasure_List_622
        list:hasContents  inst:IfcLengthMeasure_625 ;
        list:hasNext      inst:IfcLengthMeasure_List_623 .

inst:IfcLengthMeasure_List_623
        list:hasContents  inst:IfcLengthMeasure_421 .

inst:IfcLengthMeasure_List_626
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_627
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_628
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_629
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "179.514016"^^xsd:double .

inst:IfcLengthMeasure_List_626
        list:hasContents  inst:IfcLengthMeasure_629 ;
        list:hasNext      inst:IfcLengthMeasure_List_627 .

inst:IfcLengthMeasure_630
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "16.204132"^^xsd:double .

inst:IfcLengthMeasure_List_627
        list:hasContents  inst:IfcLengthMeasure_630 ;
        list:hasNext      inst:IfcLengthMeasure_List_628 .

inst:IfcLengthMeasure_List_628
        list:hasContents  inst:IfcLengthMeasure_421 .

inst:IfcLengthMeasure_List_631
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_632
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_633
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_634
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "141.785563"^^xsd:double .

inst:IfcLengthMeasure_List_631
        list:hasContents  inst:IfcLengthMeasure_634 ;
        list:hasNext      inst:IfcLengthMeasure_List_632 .

inst:IfcLengthMeasure_635
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-22.506064"^^xsd:double .

inst:IfcLengthMeasure_List_632
        list:hasContents  inst:IfcLengthMeasure_635 ;
        list:hasNext      inst:IfcLengthMeasure_List_633 .

inst:IfcLengthMeasure_List_633
        list:hasContents  inst:IfcLengthMeasure_421 .

inst:IfcLengthMeasure_List_636
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_637
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_638
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_639
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "51.113715"^^xsd:double .

inst:IfcLengthMeasure_List_636
        list:hasContents  inst:IfcLengthMeasure_639 ;
        list:hasNext      inst:IfcLengthMeasure_List_637 .

inst:IfcLengthMeasure_640
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-73.554266"^^xsd:double .

inst:IfcLengthMeasure_List_637
        list:hasContents  inst:IfcLengthMeasure_640 ;
        list:hasNext      inst:IfcLengthMeasure_List_638 .

inst:IfcLengthMeasure_List_638
        list:hasContents  inst:IfcLengthMeasure_421 .

inst:IfcLengthMeasure_List_641
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_642
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_643
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_644
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "99.154047"^^xsd:double .

inst:IfcLengthMeasure_List_641
        list:hasContents  inst:IfcLengthMeasure_644 ;
        list:hasNext      inst:IfcLengthMeasure_List_642 .

inst:IfcLengthMeasure_645
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-53.076184"^^xsd:double .

inst:IfcLengthMeasure_List_642
        list:hasContents  inst:IfcLengthMeasure_645 ;
        list:hasNext      inst:IfcLengthMeasure_List_643 .

inst:IfcLengthMeasure_List_643
        list:hasContents  inst:IfcLengthMeasure_421 .

inst:IfcLengthMeasure_List_646
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_647
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_648
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_649
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "1.125529"^^xsd:double .

inst:IfcLengthMeasure_List_646
        list:hasContents  inst:IfcLengthMeasure_649 ;
        list:hasNext      inst:IfcLengthMeasure_List_647 .

inst:IfcLengthMeasure_650
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-80.760164"^^xsd:double .

inst:IfcLengthMeasure_List_647
        list:hasContents  inst:IfcLengthMeasure_650 ;
        list:hasNext      inst:IfcLengthMeasure_List_648 .

inst:IfcLengthMeasure_List_648
        list:hasContents  inst:IfcLengthMeasure_421 .

inst:IfcLengthMeasure_List_651
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_652
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_653
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_654
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-49.061969"^^xsd:double .

inst:IfcLengthMeasure_List_651
        list:hasContents  inst:IfcLengthMeasure_654 ;
        list:hasNext      inst:IfcLengthMeasure_List_652 .

inst:IfcLengthMeasure_655
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-74.12518"^^xsd:double .

inst:IfcLengthMeasure_List_652
        list:hasContents  inst:IfcLengthMeasure_655 ;
        list:hasNext      inst:IfcLengthMeasure_List_653 .

inst:IfcLengthMeasure_List_653
        list:hasContents  inst:IfcLengthMeasure_421 .

inst:IfcLengthMeasure_List_656
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_657
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_658
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_659
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-98.238781"^^xsd:double .

inst:IfcLengthMeasure_List_656
        list:hasContents  inst:IfcLengthMeasure_659 ;
        list:hasNext      inst:IfcLengthMeasure_List_657 .

inst:IfcLengthMeasure_660
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-53.599176"^^xsd:double .

inst:IfcLengthMeasure_List_657
        list:hasContents  inst:IfcLengthMeasure_660 ;
        list:hasNext      inst:IfcLengthMeasure_List_658 .

inst:IfcLengthMeasure_List_658
        list:hasContents  inst:IfcLengthMeasure_421 .

inst:IfcLengthMeasure_List_661
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_662
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_663
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_664
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-141.288688"^^xsd:double .

inst:IfcLengthMeasure_List_661
        list:hasContents  inst:IfcLengthMeasure_664 ;
        list:hasNext      inst:IfcLengthMeasure_List_662 .

inst:IfcLengthMeasure_665
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-22.935416"^^xsd:double .

inst:IfcLengthMeasure_List_662
        list:hasContents  inst:IfcLengthMeasure_665 ;
        list:hasNext      inst:IfcLengthMeasure_List_663 .

inst:IfcLengthMeasure_List_663
        list:hasContents  inst:IfcLengthMeasure_421 .

inst:IfcLengthMeasure_List_666
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_667
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_668
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_669
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-209.864297"^^xsd:double .

inst:IfcLengthMeasure_List_666
        list:hasContents  inst:IfcLengthMeasure_669 ;
        list:hasNext      inst:IfcLengthMeasure_List_667 .

inst:IfcLengthMeasure_670
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "60.669523"^^xsd:double .

inst:IfcLengthMeasure_List_667
        list:hasContents  inst:IfcLengthMeasure_670 ;
        list:hasNext      inst:IfcLengthMeasure_List_668 .

inst:IfcLengthMeasure_List_668
        list:hasContents  inst:IfcLengthMeasure_421 .

inst:IfcLengthMeasure_List_671
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_672
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_673
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_674
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-179.625016"^^xsd:double .

inst:IfcLengthMeasure_List_671
        list:hasContents  inst:IfcLengthMeasure_674 ;
        list:hasNext      inst:IfcLengthMeasure_List_672 .

inst:IfcLengthMeasure_675
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "16.339027"^^xsd:double .

inst:IfcLengthMeasure_List_672
        list:hasContents  inst:IfcLengthMeasure_675 ;
        list:hasNext      inst:IfcLengthMeasure_List_673 .

inst:IfcLengthMeasure_List_673
        list:hasContents  inst:IfcLengthMeasure_421 .

inst:IfcLengthMeasure_List_676
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_677
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_678
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_679
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-229.527203"^^xsd:double .

inst:IfcLengthMeasure_List_676
        list:hasContents  inst:IfcLengthMeasure_679 ;
        list:hasNext      inst:IfcLengthMeasure_List_677 .

inst:IfcLengthMeasure_680
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "111.04025"^^xsd:double .

inst:IfcLengthMeasure_List_677
        list:hasContents  inst:IfcLengthMeasure_680 ;
        list:hasNext      inst:IfcLengthMeasure_List_678 .

inst:IfcLengthMeasure_List_678
        list:hasContents  inst:IfcLengthMeasure_421 .

inst:IfcLengthMeasure_List_681
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_682
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_683
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_681
        list:hasContents  inst:IfcLengthMeasure_60 ;
        list:hasNext      inst:IfcLengthMeasure_List_682 .

inst:IfcLengthMeasure_684
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "247.792422"^^xsd:double .

inst:IfcLengthMeasure_List_682
        list:hasContents  inst:IfcLengthMeasure_684 ;
        list:hasNext      inst:IfcLengthMeasure_List_683 .

inst:IfcLengthMeasure_685
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-84.0"^^xsd:double .

inst:IfcLengthMeasure_List_683
        list:hasContents  inst:IfcLengthMeasure_685 .

inst:IfcLengthMeasure_List_686
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_687
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_688
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_689
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "35.45952"^^xsd:double .

inst:IfcLengthMeasure_List_686
        list:hasContents  inst:IfcLengthMeasure_689 ;
        list:hasNext      inst:IfcLengthMeasure_List_687 .

inst:IfcLengthMeasure_690
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "245.798125"^^xsd:double .

inst:IfcLengthMeasure_List_687
        list:hasContents  inst:IfcLengthMeasure_690 ;
        list:hasNext      inst:IfcLengthMeasure_List_688 .

inst:IfcLengthMeasure_List_688
        list:hasContents  inst:IfcLengthMeasure_685 .

inst:IfcLengthMeasure_List_691
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_692
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_693
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_694
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "71.015367"^^xsd:double .

inst:IfcLengthMeasure_List_691
        list:hasContents  inst:IfcLengthMeasure_694 ;
        list:hasNext      inst:IfcLengthMeasure_List_692 .

inst:IfcLengthMeasure_695
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "239.395359"^^xsd:double .

inst:IfcLengthMeasure_List_692
        list:hasContents  inst:IfcLengthMeasure_695 ;
        list:hasNext      inst:IfcLengthMeasure_List_693 .

inst:IfcLengthMeasure_List_693
        list:hasContents  inst:IfcLengthMeasure_685 .

inst:IfcLengthMeasure_List_696
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_697
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_698
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_699
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "104.952289"^^xsd:double .

inst:IfcLengthMeasure_List_696
        list:hasContents  inst:IfcLengthMeasure_699 ;
        list:hasNext      inst:IfcLengthMeasure_List_697 .

inst:IfcLengthMeasure_700
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "227.684234"^^xsd:double .

inst:IfcLengthMeasure_List_697
        list:hasContents  inst:IfcLengthMeasure_700 ;
        list:hasNext      inst:IfcLengthMeasure_List_698 .

inst:IfcLengthMeasure_List_698
        list:hasContents  inst:IfcLengthMeasure_685 .

inst:IfcLengthMeasure_List_701
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_702
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_703
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_704
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "136.019484"^^xsd:double .

inst:IfcLengthMeasure_List_701
        list:hasContents  inst:IfcLengthMeasure_704 ;
        list:hasNext      inst:IfcLengthMeasure_List_702 .

inst:IfcLengthMeasure_705
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "207.942281"^^xsd:double .

inst:IfcLengthMeasure_List_702
        list:hasContents  inst:IfcLengthMeasure_705 ;
        list:hasNext      inst:IfcLengthMeasure_List_703 .

inst:IfcLengthMeasure_List_703
        list:hasContents  inst:IfcLengthMeasure_685 .

inst:IfcLengthMeasure_List_706
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_707
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_708
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_706
        list:hasContents  inst:IfcLengthMeasure_347 ;
        list:hasNext      inst:IfcLengthMeasure_List_707 .

inst:IfcLengthMeasure_List_707
        list:hasContents  inst:IfcLengthMeasure_297 ;
        list:hasNext      inst:IfcLengthMeasure_List_708 .

inst:IfcLengthMeasure_List_708
        list:hasContents  inst:IfcLengthMeasure_685 .

inst:IfcLengthMeasure_List_709
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_710
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_711
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_712
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "157.77775"^^xsd:double .

inst:IfcLengthMeasure_List_709
        list:hasContents  inst:IfcLengthMeasure_712 ;
        list:hasNext      inst:IfcLengthMeasure_List_710 .

inst:IfcLengthMeasure_713
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "136.530484"^^xsd:double .

inst:IfcLengthMeasure_List_710
        list:hasContents  inst:IfcLengthMeasure_713 ;
        list:hasNext      inst:IfcLengthMeasure_List_711 .

inst:IfcLengthMeasure_List_711
        list:hasContents  inst:IfcLengthMeasure_685 .

inst:IfcLengthMeasure_List_714
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_715
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_716
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_717
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "143.710984"^^xsd:double .

inst:IfcLengthMeasure_List_714
        list:hasContents  inst:IfcLengthMeasure_717 ;
        list:hasNext      inst:IfcLengthMeasure_List_715 .

inst:IfcLengthMeasure_718
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "97.530469"^^xsd:double .

inst:IfcLengthMeasure_List_715
        list:hasContents  inst:IfcLengthMeasure_718 ;
        list:hasNext      inst:IfcLengthMeasure_List_716 .

inst:IfcLengthMeasure_List_716
        list:hasContents  inst:IfcLengthMeasure_685 .

inst:IfcLengthMeasure_List_719
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_720
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_721
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_722
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "123.041867"^^xsd:double .

inst:IfcLengthMeasure_List_719
        list:hasContents  inst:IfcLengthMeasure_722 ;
        list:hasNext      inst:IfcLengthMeasure_List_720 .

inst:IfcLengthMeasure_723
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "64.626715"^^xsd:double .

inst:IfcLengthMeasure_List_720
        list:hasContents  inst:IfcLengthMeasure_723 ;
        list:hasNext      inst:IfcLengthMeasure_List_721 .

inst:IfcLengthMeasure_List_721
        list:hasContents  inst:IfcLengthMeasure_685 .

inst:IfcLengthMeasure_List_724
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_725
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_726
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_727
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "96.919461"^^xsd:double .

inst:IfcLengthMeasure_List_724
        list:hasContents  inst:IfcLengthMeasure_727 ;
        list:hasNext      inst:IfcLengthMeasure_List_725 .

inst:IfcLengthMeasure_728
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "35.394453"^^xsd:double .

inst:IfcLengthMeasure_List_725
        list:hasContents  inst:IfcLengthMeasure_728 ;
        list:hasNext      inst:IfcLengthMeasure_List_726 .

inst:IfcLengthMeasure_List_726
        list:hasContents  inst:IfcLengthMeasure_685 .

inst:IfcLengthMeasure_List_729
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_730
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_731
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_732
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "67.443461"^^xsd:double .

inst:IfcLengthMeasure_List_729
        list:hasContents  inst:IfcLengthMeasure_732 ;
        list:hasNext      inst:IfcLengthMeasure_List_730 .

inst:IfcLengthMeasure_733
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "12.407895"^^xsd:double .

inst:IfcLengthMeasure_List_730
        list:hasContents  inst:IfcLengthMeasure_733 ;
        list:hasNext      inst:IfcLengthMeasure_List_731 .

inst:IfcLengthMeasure_List_731
        list:hasContents  inst:IfcLengthMeasure_685 .

inst:IfcLengthMeasure_List_734
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_735
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_736
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_737
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "34.616102"^^xsd:double .

inst:IfcLengthMeasure_List_734
        list:hasContents  inst:IfcLengthMeasure_737 ;
        list:hasNext      inst:IfcLengthMeasure_List_735 .

inst:IfcLengthMeasure_738
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-2.748099"^^xsd:double .

inst:IfcLengthMeasure_List_735
        list:hasContents  inst:IfcLengthMeasure_738 ;
        list:hasNext      inst:IfcLengthMeasure_List_736 .

inst:IfcLengthMeasure_List_736
        list:hasContents  inst:IfcLengthMeasure_685 .

inst:IfcLengthMeasure_List_739
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_740
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_741
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_742
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "0.55276"^^xsd:double .

inst:IfcLengthMeasure_List_739
        list:hasContents  inst:IfcLengthMeasure_742 ;
        list:hasNext      inst:IfcLengthMeasure_List_740 .

inst:IfcLengthMeasure_743
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-8.022964"^^xsd:double .

inst:IfcLengthMeasure_List_740
        list:hasContents  inst:IfcLengthMeasure_743 ;
        list:hasNext      inst:IfcLengthMeasure_List_741 .

inst:IfcLengthMeasure_List_741
        list:hasContents  inst:IfcLengthMeasure_685 .

inst:IfcLengthMeasure_List_744
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_745
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_746
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_747
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-33.624148"^^xsd:double .

inst:IfcLengthMeasure_List_744
        list:hasContents  inst:IfcLengthMeasure_747 ;
        list:hasNext      inst:IfcLengthMeasure_List_745 .

inst:IfcLengthMeasure_748
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-3.048111"^^xsd:double .

inst:IfcLengthMeasure_List_745
        list:hasContents  inst:IfcLengthMeasure_748 ;
        list:hasNext      inst:IfcLengthMeasure_List_746 .

inst:IfcLengthMeasure_List_746
        list:hasContents  inst:IfcLengthMeasure_685 .

inst:IfcLengthMeasure_List_749
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_750
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_751
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_752
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-67.121539"^^xsd:double .

inst:IfcLengthMeasure_List_749
        list:hasContents  inst:IfcLengthMeasure_752 ;
        list:hasNext      inst:IfcLengthMeasure_List_750 .

inst:IfcLengthMeasure_753
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "12.207951"^^xsd:double .

inst:IfcLengthMeasure_List_750
        list:hasContents  inst:IfcLengthMeasure_753 ;
        list:hasNext      inst:IfcLengthMeasure_List_751 .

inst:IfcLengthMeasure_List_751
        list:hasContents  inst:IfcLengthMeasure_685 .

inst:IfcLengthMeasure_List_754
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_755
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_756
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_757
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-96.747688"^^xsd:double .

inst:IfcLengthMeasure_List_754
        list:hasContents  inst:IfcLengthMeasure_757 ;
        list:hasNext      inst:IfcLengthMeasure_List_755 .

inst:IfcLengthMeasure_758
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "35.232555"^^xsd:double .

inst:IfcLengthMeasure_List_755
        list:hasContents  inst:IfcLengthMeasure_758 ;
        list:hasNext      inst:IfcLengthMeasure_List_756 .

inst:IfcLengthMeasure_List_756
        list:hasContents  inst:IfcLengthMeasure_685 .

inst:IfcLengthMeasure_List_759
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_760
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_761
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_762
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-123.226352"^^xsd:double .

inst:IfcLengthMeasure_List_759
        list:hasContents  inst:IfcLengthMeasure_762 ;
        list:hasNext      inst:IfcLengthMeasure_List_760 .

inst:IfcLengthMeasure_763
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "64.87157"^^xsd:double .

inst:IfcLengthMeasure_List_760
        list:hasContents  inst:IfcLengthMeasure_763 ;
        list:hasNext      inst:IfcLengthMeasure_List_761 .

inst:IfcLengthMeasure_List_761
        list:hasContents  inst:IfcLengthMeasure_685 .

inst:IfcLengthMeasure_List_764
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_765
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_766
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_767
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-144.259"^^xsd:double .

inst:IfcLengthMeasure_List_764
        list:hasContents  inst:IfcLengthMeasure_767 ;
        list:hasNext      inst:IfcLengthMeasure_List_765 .

inst:IfcLengthMeasure_768
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "98.61857"^^xsd:double .

inst:IfcLengthMeasure_List_765
        list:hasContents  inst:IfcLengthMeasure_768 ;
        list:hasNext      inst:IfcLengthMeasure_List_766 .

inst:IfcLengthMeasure_List_766
        list:hasContents  inst:IfcLengthMeasure_685 .

inst:IfcLengthMeasure_List_769
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_770
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_771
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_772
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-157.924344"^^xsd:double .

inst:IfcLengthMeasure_List_769
        list:hasContents  inst:IfcLengthMeasure_772 ;
        list:hasNext      inst:IfcLengthMeasure_List_770 .

inst:IfcLengthMeasure_773
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "137.268734"^^xsd:double .

inst:IfcLengthMeasure_List_770
        list:hasContents  inst:IfcLengthMeasure_773 ;
        list:hasNext      inst:IfcLengthMeasure_List_771 .

inst:IfcLengthMeasure_List_771
        list:hasContents  inst:IfcLengthMeasure_685 .

inst:IfcLengthMeasure_List_774
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_775
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_776
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_774
        list:hasContents  inst:IfcLengthMeasure_296 ;
        list:hasNext      inst:IfcLengthMeasure_List_775 .

inst:IfcLengthMeasure_List_775
        list:hasContents  inst:IfcLengthMeasure_297 ;
        list:hasNext      inst:IfcLengthMeasure_List_776 .

inst:IfcLengthMeasure_List_776
        list:hasContents  inst:IfcLengthMeasure_685 .

inst:IfcLengthMeasure_List_777
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_778
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_779
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_780
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-135.195516"^^xsd:double .

inst:IfcLengthMeasure_List_777
        list:hasContents  inst:IfcLengthMeasure_780 ;
        list:hasNext      inst:IfcLengthMeasure_List_778 .

inst:IfcLengthMeasure_781
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "208.674078"^^xsd:double .

inst:IfcLengthMeasure_List_778
        list:hasContents  inst:IfcLengthMeasure_781 ;
        list:hasNext      inst:IfcLengthMeasure_List_779 .

inst:IfcLengthMeasure_List_779
        list:hasContents  inst:IfcLengthMeasure_685 .

inst:IfcLengthMeasure_List_782
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_783
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_784
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_785
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-104.054703"^^xsd:double .

inst:IfcLengthMeasure_List_782
        list:hasContents  inst:IfcLengthMeasure_785 ;
        list:hasNext      inst:IfcLengthMeasure_List_783 .

inst:IfcLengthMeasure_786
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "228.091234"^^xsd:double .

inst:IfcLengthMeasure_List_783
        list:hasContents  inst:IfcLengthMeasure_786 ;
        list:hasNext      inst:IfcLengthMeasure_List_784 .

inst:IfcLengthMeasure_List_784
        list:hasContents  inst:IfcLengthMeasure_685 .

inst:IfcLengthMeasure_List_787
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_788
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_789
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_790
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-70.384797"^^xsd:double .

inst:IfcLengthMeasure_List_787
        list:hasContents  inst:IfcLengthMeasure_790 ;
        list:hasNext      inst:IfcLengthMeasure_List_788 .

inst:IfcLengthMeasure_791
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "239.553859"^^xsd:double .

inst:IfcLengthMeasure_List_788
        list:hasContents  inst:IfcLengthMeasure_791 ;
        list:hasNext      inst:IfcLengthMeasure_List_789 .

inst:IfcLengthMeasure_List_789
        list:hasContents  inst:IfcLengthMeasure_685 .

inst:IfcLengthMeasure_List_792
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_793
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_794
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_795
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-36.026906"^^xsd:double .

inst:IfcLengthMeasure_List_792
        list:hasContents  inst:IfcLengthMeasure_795 ;
        list:hasNext      inst:IfcLengthMeasure_List_793 .

inst:IfcLengthMeasure_796
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "245.732781"^^xsd:double .

inst:IfcLengthMeasure_List_793
        list:hasContents  inst:IfcLengthMeasure_796 ;
        list:hasNext      inst:IfcLengthMeasure_List_794 .

inst:IfcLengthMeasure_List_794
        list:hasContents  inst:IfcLengthMeasure_685 .

inst:IfcLengthMeasure_List_797
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_798
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_799
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_797
        list:hasContents  inst:IfcLengthMeasure_60 ;
        list:hasNext      inst:IfcLengthMeasure_List_798 .

inst:IfcLengthMeasure_List_798
        list:hasContents  inst:IfcLengthMeasure_684 ;
        list:hasNext      inst:IfcLengthMeasure_List_799 .

inst:IfcLengthMeasure_List_799
        list:hasContents  inst:IfcLengthMeasure_685 .

inst:IfcLengthMeasure_List_800
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_801
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_802
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_800
        list:hasContents  inst:IfcLengthMeasure_60 ;
        list:hasNext      inst:IfcLengthMeasure_List_801 .

inst:IfcLengthMeasure_List_801
        list:hasContents  inst:IfcLengthMeasure_178 ;
        list:hasNext      inst:IfcLengthMeasure_List_802 .

inst:IfcLengthMeasure_List_802
        list:hasContents  inst:IfcLengthMeasure_60 .

inst:IfcLengthMeasure_List_803
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_804
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_805
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_803
        list:hasContents  inst:IfcLengthMeasure_291 ;
        list:hasNext      inst:IfcLengthMeasure_List_804 .

inst:IfcLengthMeasure_List_804
        list:hasContents  inst:IfcLengthMeasure_292 ;
        list:hasNext      inst:IfcLengthMeasure_List_805 .

inst:IfcLengthMeasure_List_805
        list:hasContents  inst:IfcLengthMeasure_60 .

inst:IfcLengthMeasure_List_806
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_807
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_808
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_806
        list:hasContents  inst:IfcLengthMeasure_286 ;
        list:hasNext      inst:IfcLengthMeasure_List_807 .

inst:IfcLengthMeasure_List_807
        list:hasContents  inst:IfcLengthMeasure_287 ;
        list:hasNext      inst:IfcLengthMeasure_List_808 .

inst:IfcLengthMeasure_List_808
        list:hasContents  inst:IfcLengthMeasure_60 .

inst:IfcLengthMeasure_List_809
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_810
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_811
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_809
        list:hasContents  inst:IfcLengthMeasure_281 ;
        list:hasNext      inst:IfcLengthMeasure_List_810 .

inst:IfcLengthMeasure_List_810
        list:hasContents  inst:IfcLengthMeasure_282 ;
        list:hasNext      inst:IfcLengthMeasure_List_811 .

inst:IfcLengthMeasure_List_811
        list:hasContents  inst:IfcLengthMeasure_60 .

inst:IfcLengthMeasure_List_812
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_813
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_814
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_812
        list:hasContents  inst:IfcLengthMeasure_276 ;
        list:hasNext      inst:IfcLengthMeasure_List_813 .

inst:IfcLengthMeasure_List_813
        list:hasContents  inst:IfcLengthMeasure_277 ;
        list:hasNext      inst:IfcLengthMeasure_List_814 .

inst:IfcLengthMeasure_List_814
        list:hasContents  inst:IfcLengthMeasure_60 .

inst:IfcLengthMeasure_List_815
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_816
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_817
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_815
        list:hasContents  inst:IfcLengthMeasure_272 ;
        list:hasNext      inst:IfcLengthMeasure_List_816 .

inst:IfcLengthMeasure_List_816
        list:hasContents  inst:IfcLengthMeasure_203 ;
        list:hasNext      inst:IfcLengthMeasure_List_817 .

inst:IfcLengthMeasure_List_817
        list:hasContents  inst:IfcLengthMeasure_60 .

inst:IfcLengthMeasure_List_818
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_819
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_820
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_818
        list:hasContents  inst:IfcLengthMeasure_267 ;
        list:hasNext      inst:IfcLengthMeasure_List_819 .

inst:IfcLengthMeasure_List_819
        list:hasContents  inst:IfcLengthMeasure_268 ;
        list:hasNext      inst:IfcLengthMeasure_List_820 .

inst:IfcLengthMeasure_List_820
        list:hasContents  inst:IfcLengthMeasure_60 .

inst:IfcLengthMeasure_List_821
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_822
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_823
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_821
        list:hasContents  inst:IfcLengthMeasure_262 ;
        list:hasNext      inst:IfcLengthMeasure_List_822 .

inst:IfcLengthMeasure_List_822
        list:hasContents  inst:IfcLengthMeasure_263 ;
        list:hasNext      inst:IfcLengthMeasure_List_823 .

inst:IfcLengthMeasure_List_823
        list:hasContents  inst:IfcLengthMeasure_60 .

inst:IfcLengthMeasure_List_824
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_825
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_826
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_824
        list:hasContents  inst:IfcLengthMeasure_257 ;
        list:hasNext      inst:IfcLengthMeasure_List_825 .

inst:IfcLengthMeasure_List_825
        list:hasContents  inst:IfcLengthMeasure_258 ;
        list:hasNext      inst:IfcLengthMeasure_List_826 .

inst:IfcLengthMeasure_List_826
        list:hasContents  inst:IfcLengthMeasure_60 .

inst:IfcLengthMeasure_List_827
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_828
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_829
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_827
        list:hasContents  inst:IfcLengthMeasure_252 ;
        list:hasNext      inst:IfcLengthMeasure_List_828 .

inst:IfcLengthMeasure_List_828
        list:hasContents  inst:IfcLengthMeasure_253 ;
        list:hasNext      inst:IfcLengthMeasure_List_829 .

inst:IfcLengthMeasure_List_829
        list:hasContents  inst:IfcLengthMeasure_60 .

inst:IfcLengthMeasure_List_830
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_831
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_832
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_830
        list:hasContents  inst:IfcLengthMeasure_247 ;
        list:hasNext      inst:IfcLengthMeasure_List_831 .

inst:IfcLengthMeasure_List_831
        list:hasContents  inst:IfcLengthMeasure_248 ;
        list:hasNext      inst:IfcLengthMeasure_List_832 .

inst:IfcLengthMeasure_List_832
        list:hasContents  inst:IfcLengthMeasure_60 .

inst:IfcLengthMeasure_List_833
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_834
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_835
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_833
        list:hasContents  inst:IfcLengthMeasure_242 ;
        list:hasNext      inst:IfcLengthMeasure_List_834 .

inst:IfcLengthMeasure_List_834
        list:hasContents  inst:IfcLengthMeasure_243 ;
        list:hasNext      inst:IfcLengthMeasure_List_835 .

inst:IfcLengthMeasure_List_835
        list:hasContents  inst:IfcLengthMeasure_60 .

inst:IfcLengthMeasure_List_836
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_837
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_838
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_836
        list:hasContents  inst:IfcLengthMeasure_237 ;
        list:hasNext      inst:IfcLengthMeasure_List_837 .

inst:IfcLengthMeasure_List_837
        list:hasContents  inst:IfcLengthMeasure_238 ;
        list:hasNext      inst:IfcLengthMeasure_List_838 .

inst:IfcLengthMeasure_List_838
        list:hasContents  inst:IfcLengthMeasure_60 .

inst:IfcLengthMeasure_List_839
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_840
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_841
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_839
        list:hasContents  inst:IfcLengthMeasure_232 ;
        list:hasNext      inst:IfcLengthMeasure_List_840 .

inst:IfcLengthMeasure_List_840
        list:hasContents  inst:IfcLengthMeasure_233 ;
        list:hasNext      inst:IfcLengthMeasure_List_841 .

inst:IfcLengthMeasure_List_841
        list:hasContents  inst:IfcLengthMeasure_60 .

inst:IfcLengthMeasure_List_842
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_843
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_844
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_842
        list:hasContents  inst:IfcLengthMeasure_227 ;
        list:hasNext      inst:IfcLengthMeasure_List_843 .

inst:IfcLengthMeasure_List_843
        list:hasContents  inst:IfcLengthMeasure_228 ;
        list:hasNext      inst:IfcLengthMeasure_List_844 .

inst:IfcLengthMeasure_List_844
        list:hasContents  inst:IfcLengthMeasure_60 .

inst:IfcLengthMeasure_List_845
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_846
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_847
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_845
        list:hasContents  inst:IfcLengthMeasure_222 ;
        list:hasNext      inst:IfcLengthMeasure_List_846 .

inst:IfcLengthMeasure_List_846
        list:hasContents  inst:IfcLengthMeasure_223 ;
        list:hasNext      inst:IfcLengthMeasure_List_847 .

inst:IfcLengthMeasure_List_847
        list:hasContents  inst:IfcLengthMeasure_60 .

inst:IfcLengthMeasure_List_848
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_849
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_850
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_848
        list:hasContents  inst:IfcLengthMeasure_217 ;
        list:hasNext      inst:IfcLengthMeasure_List_849 .

inst:IfcLengthMeasure_List_849
        list:hasContents  inst:IfcLengthMeasure_218 ;
        list:hasNext      inst:IfcLengthMeasure_List_850 .

inst:IfcLengthMeasure_List_850
        list:hasContents  inst:IfcLengthMeasure_60 .

inst:IfcLengthMeasure_List_851
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_852
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_853
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_851
        list:hasContents  inst:IfcLengthMeasure_212 ;
        list:hasNext      inst:IfcLengthMeasure_List_852 .

inst:IfcLengthMeasure_List_852
        list:hasContents  inst:IfcLengthMeasure_213 ;
        list:hasNext      inst:IfcLengthMeasure_List_853 .

inst:IfcLengthMeasure_List_853
        list:hasContents  inst:IfcLengthMeasure_60 .

inst:IfcLengthMeasure_List_854
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_855
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_856
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_854
        list:hasContents  inst:IfcLengthMeasure_207 ;
        list:hasNext      inst:IfcLengthMeasure_List_855 .

inst:IfcLengthMeasure_List_855
        list:hasContents  inst:IfcLengthMeasure_208 ;
        list:hasNext      inst:IfcLengthMeasure_List_856 .

inst:IfcLengthMeasure_List_856
        list:hasContents  inst:IfcLengthMeasure_60 .

inst:IfcLengthMeasure_List_857
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_858
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_859
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_857
        list:hasContents  inst:IfcLengthMeasure_202 ;
        list:hasNext      inst:IfcLengthMeasure_List_858 .

inst:IfcLengthMeasure_List_858
        list:hasContents  inst:IfcLengthMeasure_203 ;
        list:hasNext      inst:IfcLengthMeasure_List_859 .

inst:IfcLengthMeasure_List_859
        list:hasContents  inst:IfcLengthMeasure_60 .

inst:IfcLengthMeasure_List_860
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_861
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_862
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_860
        list:hasContents  inst:IfcLengthMeasure_197 ;
        list:hasNext      inst:IfcLengthMeasure_List_861 .

inst:IfcLengthMeasure_List_861
        list:hasContents  inst:IfcLengthMeasure_198 ;
        list:hasNext      inst:IfcLengthMeasure_List_862 .

inst:IfcLengthMeasure_List_862
        list:hasContents  inst:IfcLengthMeasure_60 .

inst:IfcLengthMeasure_List_863
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_864
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_865
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_863
        list:hasContents  inst:IfcLengthMeasure_192 ;
        list:hasNext      inst:IfcLengthMeasure_List_864 .

inst:IfcLengthMeasure_List_864
        list:hasContents  inst:IfcLengthMeasure_193 ;
        list:hasNext      inst:IfcLengthMeasure_List_865 .

inst:IfcLengthMeasure_List_865
        list:hasContents  inst:IfcLengthMeasure_60 .

inst:IfcLengthMeasure_List_866
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_867
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_868
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_866
        list:hasContents  inst:IfcLengthMeasure_187 ;
        list:hasNext      inst:IfcLengthMeasure_List_867 .

inst:IfcLengthMeasure_List_867
        list:hasContents  inst:IfcLengthMeasure_188 ;
        list:hasNext      inst:IfcLengthMeasure_List_868 .

inst:IfcLengthMeasure_List_868
        list:hasContents  inst:IfcLengthMeasure_60 .

inst:IfcLengthMeasure_List_869
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_870
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_871
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_869
        list:hasContents  inst:IfcLengthMeasure_182 ;
        list:hasNext      inst:IfcLengthMeasure_List_870 .

inst:IfcLengthMeasure_List_870
        list:hasContents  inst:IfcLengthMeasure_183 ;
        list:hasNext      inst:IfcLengthMeasure_List_871 .

inst:IfcLengthMeasure_List_871
        list:hasContents  inst:IfcLengthMeasure_60 .

inst:IfcLengthMeasure_List_872
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_873
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_874
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_872
        list:hasContents  inst:IfcLengthMeasure_60 ;
        list:hasNext      inst:IfcLengthMeasure_List_873 .

inst:IfcLengthMeasure_List_873
        list:hasContents  inst:IfcLengthMeasure_178 ;
        list:hasNext      inst:IfcLengthMeasure_List_874 .

inst:IfcLengthMeasure_List_874
        list:hasContents  inst:IfcLengthMeasure_60 .

inst:IfcLengthMeasure_List_875
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_876
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_877
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_875
        list:hasContents  inst:IfcLengthMeasure_60 ;
        list:hasNext      inst:IfcLengthMeasure_List_876 .

inst:IfcLengthMeasure_List_876
        list:hasContents  inst:IfcLengthMeasure_684 ;
        list:hasNext      inst:IfcLengthMeasure_List_877 .

inst:IfcLengthMeasure_List_877
        list:hasContents  inst:IfcLengthMeasure_685 .

inst:IfcLengthMeasure_List_878
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_879
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_880
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_878
        list:hasContents  inst:IfcLengthMeasure_689 ;
        list:hasNext      inst:IfcLengthMeasure_List_879 .

inst:IfcLengthMeasure_List_879
        list:hasContents  inst:IfcLengthMeasure_690 ;
        list:hasNext      inst:IfcLengthMeasure_List_880 .

inst:IfcLengthMeasure_List_880
        list:hasContents  inst:IfcLengthMeasure_685 .

inst:IfcLengthMeasure_List_881
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_882
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_883
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_881
        list:hasContents  inst:IfcLengthMeasure_694 ;
        list:hasNext      inst:IfcLengthMeasure_List_882 .

inst:IfcLengthMeasure_List_882
        list:hasContents  inst:IfcLengthMeasure_695 ;
        list:hasNext      inst:IfcLengthMeasure_List_883 .

inst:IfcLengthMeasure_List_883
        list:hasContents  inst:IfcLengthMeasure_685 .

inst:IfcLengthMeasure_List_884
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_885
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_886
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_884
        list:hasContents  inst:IfcLengthMeasure_699 ;
        list:hasNext      inst:IfcLengthMeasure_List_885 .

inst:IfcLengthMeasure_List_885
        list:hasContents  inst:IfcLengthMeasure_700 ;
        list:hasNext      inst:IfcLengthMeasure_List_886 .

inst:IfcLengthMeasure_List_886
        list:hasContents  inst:IfcLengthMeasure_685 .

inst:IfcLengthMeasure_List_887
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_888
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_889
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_887
        list:hasContents  inst:IfcLengthMeasure_704 ;
        list:hasNext      inst:IfcLengthMeasure_List_888 .

inst:IfcLengthMeasure_List_888
        list:hasContents  inst:IfcLengthMeasure_705 ;
        list:hasNext      inst:IfcLengthMeasure_List_889 .

inst:IfcLengthMeasure_List_889
        list:hasContents  inst:IfcLengthMeasure_685 .

inst:IfcLengthMeasure_List_890
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_891
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_892
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_890
        list:hasContents  inst:IfcLengthMeasure_347 ;
        list:hasNext      inst:IfcLengthMeasure_List_891 .

inst:IfcLengthMeasure_List_891
        list:hasContents  inst:IfcLengthMeasure_297 ;
        list:hasNext      inst:IfcLengthMeasure_List_892 .

inst:IfcLengthMeasure_List_892
        list:hasContents  inst:IfcLengthMeasure_685 .

inst:IfcLengthMeasure_List_893
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_894
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_895
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_893
        list:hasContents  inst:IfcLengthMeasure_712 ;
        list:hasNext      inst:IfcLengthMeasure_List_894 .

inst:IfcLengthMeasure_List_894
        list:hasContents  inst:IfcLengthMeasure_713 ;
        list:hasNext      inst:IfcLengthMeasure_List_895 .

inst:IfcLengthMeasure_List_895
        list:hasContents  inst:IfcLengthMeasure_685 .

inst:IfcLengthMeasure_List_896
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_897
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_898
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_896
        list:hasContents  inst:IfcLengthMeasure_717 ;
        list:hasNext      inst:IfcLengthMeasure_List_897 .

inst:IfcLengthMeasure_List_897
        list:hasContents  inst:IfcLengthMeasure_718 ;
        list:hasNext      inst:IfcLengthMeasure_List_898 .

inst:IfcLengthMeasure_List_898
        list:hasContents  inst:IfcLengthMeasure_685 .

inst:IfcLengthMeasure_List_899
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_900
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_901
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_899
        list:hasContents  inst:IfcLengthMeasure_722 ;
        list:hasNext      inst:IfcLengthMeasure_List_900 .

inst:IfcLengthMeasure_List_900
        list:hasContents  inst:IfcLengthMeasure_723 ;
        list:hasNext      inst:IfcLengthMeasure_List_901 .

inst:IfcLengthMeasure_List_901
        list:hasContents  inst:IfcLengthMeasure_685 .

inst:IfcLengthMeasure_List_902
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_903
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_904
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_902
        list:hasContents  inst:IfcLengthMeasure_727 ;
        list:hasNext      inst:IfcLengthMeasure_List_903 .

inst:IfcLengthMeasure_List_903
        list:hasContents  inst:IfcLengthMeasure_728 ;
        list:hasNext      inst:IfcLengthMeasure_List_904 .

inst:IfcLengthMeasure_List_904
        list:hasContents  inst:IfcLengthMeasure_685 .

inst:IfcLengthMeasure_List_905
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_906
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_907
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_905
        list:hasContents  inst:IfcLengthMeasure_732 ;
        list:hasNext      inst:IfcLengthMeasure_List_906 .

inst:IfcLengthMeasure_List_906
        list:hasContents  inst:IfcLengthMeasure_733 ;
        list:hasNext      inst:IfcLengthMeasure_List_907 .

inst:IfcLengthMeasure_List_907
        list:hasContents  inst:IfcLengthMeasure_685 .

inst:IfcLengthMeasure_List_908
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_909
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_910
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_908
        list:hasContents  inst:IfcLengthMeasure_737 ;
        list:hasNext      inst:IfcLengthMeasure_List_909 .

inst:IfcLengthMeasure_List_909
        list:hasContents  inst:IfcLengthMeasure_738 ;
        list:hasNext      inst:IfcLengthMeasure_List_910 .

inst:IfcLengthMeasure_List_910
        list:hasContents  inst:IfcLengthMeasure_685 .

inst:IfcLengthMeasure_List_911
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_912
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_913
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_911
        list:hasContents  inst:IfcLengthMeasure_742 ;
        list:hasNext      inst:IfcLengthMeasure_List_912 .

inst:IfcLengthMeasure_List_912
        list:hasContents  inst:IfcLengthMeasure_743 ;
        list:hasNext      inst:IfcLengthMeasure_List_913 .

inst:IfcLengthMeasure_List_913
        list:hasContents  inst:IfcLengthMeasure_685 .

inst:IfcLengthMeasure_List_914
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_915
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_916
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_914
        list:hasContents  inst:IfcLengthMeasure_747 ;
        list:hasNext      inst:IfcLengthMeasure_List_915 .

inst:IfcLengthMeasure_List_915
        list:hasContents  inst:IfcLengthMeasure_748 ;
        list:hasNext      inst:IfcLengthMeasure_List_916 .

inst:IfcLengthMeasure_List_916
        list:hasContents  inst:IfcLengthMeasure_685 .

inst:IfcLengthMeasure_List_917
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_918
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_919
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_917
        list:hasContents  inst:IfcLengthMeasure_752 ;
        list:hasNext      inst:IfcLengthMeasure_List_918 .

inst:IfcLengthMeasure_List_918
        list:hasContents  inst:IfcLengthMeasure_753 ;
        list:hasNext      inst:IfcLengthMeasure_List_919 .

inst:IfcLengthMeasure_List_919
        list:hasContents  inst:IfcLengthMeasure_685 .

inst:IfcLengthMeasure_List_920
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_921
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_922
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_920
        list:hasContents  inst:IfcLengthMeasure_757 ;
        list:hasNext      inst:IfcLengthMeasure_List_921 .

inst:IfcLengthMeasure_List_921
        list:hasContents  inst:IfcLengthMeasure_758 ;
        list:hasNext      inst:IfcLengthMeasure_List_922 .

inst:IfcLengthMeasure_List_922
        list:hasContents  inst:IfcLengthMeasure_685 .

inst:IfcLengthMeasure_List_923
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_924
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_925
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_923
        list:hasContents  inst:IfcLengthMeasure_762 ;
        list:hasNext      inst:IfcLengthMeasure_List_924 .

inst:IfcLengthMeasure_List_924
        list:hasContents  inst:IfcLengthMeasure_763 ;
        list:hasNext      inst:IfcLengthMeasure_List_925 .

inst:IfcLengthMeasure_List_925
        list:hasContents  inst:IfcLengthMeasure_685 .

inst:IfcLengthMeasure_List_926
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_927
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_928
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_926
        list:hasContents  inst:IfcLengthMeasure_767 ;
        list:hasNext      inst:IfcLengthMeasure_List_927 .

inst:IfcLengthMeasure_List_927
        list:hasContents  inst:IfcLengthMeasure_768 ;
        list:hasNext      inst:IfcLengthMeasure_List_928 .

inst:IfcLengthMeasure_List_928
        list:hasContents  inst:IfcLengthMeasure_685 .

inst:IfcLengthMeasure_List_929
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_930
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_931
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_929
        list:hasContents  inst:IfcLengthMeasure_772 ;
        list:hasNext      inst:IfcLengthMeasure_List_930 .

inst:IfcLengthMeasure_List_930
        list:hasContents  inst:IfcLengthMeasure_773 ;
        list:hasNext      inst:IfcLengthMeasure_List_931 .

inst:IfcLengthMeasure_List_931
        list:hasContents  inst:IfcLengthMeasure_685 .

inst:IfcLengthMeasure_List_932
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_933
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_934
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_932
        list:hasContents  inst:IfcLengthMeasure_296 ;
        list:hasNext      inst:IfcLengthMeasure_List_933 .

inst:IfcLengthMeasure_List_933
        list:hasContents  inst:IfcLengthMeasure_297 ;
        list:hasNext      inst:IfcLengthMeasure_List_934 .

inst:IfcLengthMeasure_List_934
        list:hasContents  inst:IfcLengthMeasure_685 .

inst:IfcLengthMeasure_List_935
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_936
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_937
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_935
        list:hasContents  inst:IfcLengthMeasure_780 ;
        list:hasNext      inst:IfcLengthMeasure_List_936 .

inst:IfcLengthMeasure_List_936
        list:hasContents  inst:IfcLengthMeasure_781 ;
        list:hasNext      inst:IfcLengthMeasure_List_937 .

inst:IfcLengthMeasure_List_937
        list:hasContents  inst:IfcLengthMeasure_685 .

inst:IfcLengthMeasure_List_938
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_939
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_940
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_938
        list:hasContents  inst:IfcLengthMeasure_785 ;
        list:hasNext      inst:IfcLengthMeasure_List_939 .

inst:IfcLengthMeasure_List_939
        list:hasContents  inst:IfcLengthMeasure_786 ;
        list:hasNext      inst:IfcLengthMeasure_List_940 .

inst:IfcLengthMeasure_List_940
        list:hasContents  inst:IfcLengthMeasure_685 .

inst:IfcLengthMeasure_List_941
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_942
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_943
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_941
        list:hasContents  inst:IfcLengthMeasure_790 ;
        list:hasNext      inst:IfcLengthMeasure_List_942 .

inst:IfcLengthMeasure_List_942
        list:hasContents  inst:IfcLengthMeasure_791 ;
        list:hasNext      inst:IfcLengthMeasure_List_943 .

inst:IfcLengthMeasure_List_943
        list:hasContents  inst:IfcLengthMeasure_685 .

inst:IfcLengthMeasure_List_944
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_945
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_946
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_944
        list:hasContents  inst:IfcLengthMeasure_795 ;
        list:hasNext      inst:IfcLengthMeasure_List_945 .

inst:IfcLengthMeasure_List_945
        list:hasContents  inst:IfcLengthMeasure_796 ;
        list:hasNext      inst:IfcLengthMeasure_List_946 .

inst:IfcLengthMeasure_List_946
        list:hasContents  inst:IfcLengthMeasure_685 .

inst:IfcLengthMeasure_List_List_947
        rdf:type  ifc:IfcLengthMeasure_List_List .

inst:IfcCartesianPointList3D_200
        ifc:coordList_IfcCartesianPointList3D  inst:IfcLengthMeasure_List_List_947 .

inst:IfcLengthMeasure_List_List_947
        list:hasContents  inst:IfcLengthMeasure_List_55 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_948 .

inst:IfcLengthMeasure_List_List_948
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_61 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_949 .

inst:IfcLengthMeasure_List_List_949
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_66 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_950 .

inst:IfcLengthMeasure_List_List_950
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_71 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_951 .

inst:IfcLengthMeasure_List_List_951
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_76 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_952 .

inst:IfcLengthMeasure_List_List_952
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_81 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_953 .

inst:IfcLengthMeasure_List_List_953
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_86 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_954 .

inst:IfcLengthMeasure_List_List_954
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_91 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_955 .

inst:IfcLengthMeasure_List_List_955
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_96 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_956 .

inst:IfcLengthMeasure_List_List_956
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_101 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_957 .

inst:IfcLengthMeasure_List_List_957
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_106 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_958 .

inst:IfcLengthMeasure_List_List_958
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_110 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_959 .

inst:IfcLengthMeasure_List_List_959
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_115 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_960 .

inst:IfcLengthMeasure_List_List_960
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_120 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_961 .

inst:IfcLengthMeasure_List_List_961
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_125 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_962 .

inst:IfcLengthMeasure_List_List_962
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_130 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_963 .

inst:IfcLengthMeasure_List_List_963
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_135 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_964 .

inst:IfcLengthMeasure_List_List_964
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_140 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_965 .

inst:IfcLengthMeasure_List_List_965
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_145 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_966 .

inst:IfcLengthMeasure_List_List_966
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_150 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_967 .

inst:IfcLengthMeasure_List_List_967
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_155 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_968 .

inst:IfcLengthMeasure_List_List_968
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_160 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_969 .

inst:IfcLengthMeasure_List_List_969
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_165 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_970 .

inst:IfcLengthMeasure_List_List_970
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_170 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_971 .

inst:IfcLengthMeasure_List_List_971
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_175 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_972 .

inst:IfcLengthMeasure_List_List_972
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_179 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_973 .

inst:IfcLengthMeasure_List_List_973
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_184 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_974 .

inst:IfcLengthMeasure_List_List_974
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_189 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_975 .

inst:IfcLengthMeasure_List_List_975
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_194 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_976 .

inst:IfcLengthMeasure_List_List_976
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_199 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_977 .

inst:IfcLengthMeasure_List_List_977
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_204 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_978 .

inst:IfcLengthMeasure_List_List_978
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_209 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_979 .

inst:IfcLengthMeasure_List_List_979
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_214 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_980 .

inst:IfcLengthMeasure_List_List_980
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_219 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_981 .

inst:IfcLengthMeasure_List_List_981
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_224 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_982 .

inst:IfcLengthMeasure_List_List_982
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_229 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_983 .

inst:IfcLengthMeasure_List_List_983
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_234 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_984 .

inst:IfcLengthMeasure_List_List_984
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_239 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_985 .

inst:IfcLengthMeasure_List_List_985
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_244 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_986 .

inst:IfcLengthMeasure_List_List_986
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_249 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_987 .

inst:IfcLengthMeasure_List_List_987
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_254 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_988 .

inst:IfcLengthMeasure_List_List_988
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_259 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_989 .

inst:IfcLengthMeasure_List_List_989
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_264 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_990 .

inst:IfcLengthMeasure_List_List_990
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_269 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_991 .

inst:IfcLengthMeasure_List_List_991
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_273 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_992 .

inst:IfcLengthMeasure_List_List_992
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_278 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_993 .

inst:IfcLengthMeasure_List_List_993
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_283 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_994 .

inst:IfcLengthMeasure_List_List_994
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_288 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_995 .

inst:IfcLengthMeasure_List_List_995
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_293 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_996 .

inst:IfcLengthMeasure_List_List_996
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_299 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_997 .

inst:IfcLengthMeasure_List_List_997
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_304 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_998 .

inst:IfcLengthMeasure_List_List_998
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_309 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_999 .

inst:IfcLengthMeasure_List_List_999
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_314 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1000 .

inst:IfcLengthMeasure_List_List_1000
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_319 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1001 .

inst:IfcLengthMeasure_List_List_1001
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_324 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1002 .

inst:IfcLengthMeasure_List_List_1002
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_329 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1003 .

inst:IfcLengthMeasure_List_List_1003
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_334 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1004 .

inst:IfcLengthMeasure_List_List_1004
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_339 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1005 .

inst:IfcLengthMeasure_List_List_1005
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_344 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1006 .

inst:IfcLengthMeasure_List_List_1006
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_348 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1007 .

inst:IfcLengthMeasure_List_List_1007
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_353 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1008 .

inst:IfcLengthMeasure_List_List_1008
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_358 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1009 .

inst:IfcLengthMeasure_List_List_1009
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_363 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1010 .

inst:IfcLengthMeasure_List_List_1010
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_368 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1011 .

inst:IfcLengthMeasure_List_List_1011
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_373 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1012 .

inst:IfcLengthMeasure_List_List_1012
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_378 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1013 .

inst:IfcLengthMeasure_List_List_1013
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_383 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1014 .

inst:IfcLengthMeasure_List_List_1014
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_388 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1015 .

inst:IfcLengthMeasure_List_List_1015
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_393 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1016 .

inst:IfcLengthMeasure_List_List_1016
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_398 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1017 .

inst:IfcLengthMeasure_List_List_1017
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_403 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1018 .

inst:IfcLengthMeasure_List_List_1018
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_408 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1019 .

inst:IfcLengthMeasure_List_List_1019
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_413 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1020 .

inst:IfcLengthMeasure_List_List_1020
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_416 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1021 .

inst:IfcLengthMeasure_List_List_1021
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_422 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1022 .

inst:IfcLengthMeasure_List_List_1022
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_425 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1023 .

inst:IfcLengthMeasure_List_List_1023
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_428 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1024 .

inst:IfcLengthMeasure_List_List_1024
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_431 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1025 .

inst:IfcLengthMeasure_List_List_1025
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_434 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1026 .

inst:IfcLengthMeasure_List_List_1026
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_437 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1027 .

inst:IfcLengthMeasure_List_List_1027
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_440 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1028 .

inst:IfcLengthMeasure_List_List_1028
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_443 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1029 .

inst:IfcLengthMeasure_List_List_1029
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_446 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1030 .

inst:IfcLengthMeasure_List_List_1030
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_449 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1031 .

inst:IfcLengthMeasure_List_List_1031
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_452 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1032 .

inst:IfcLengthMeasure_List_List_1032
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_455 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1033 .

inst:IfcLengthMeasure_List_List_1033
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_458 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1034 .

inst:IfcLengthMeasure_List_List_1034
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_461 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1035 .

inst:IfcLengthMeasure_List_List_1035
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_464 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1036 .

inst:IfcLengthMeasure_List_List_1036
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_467 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1037 .

inst:IfcLengthMeasure_List_List_1037
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_470 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1038 .

inst:IfcLengthMeasure_List_List_1038
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_473 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1039 .

inst:IfcLengthMeasure_List_List_1039
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_476 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1040 .

inst:IfcLengthMeasure_List_List_1040
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_479 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1041 .

inst:IfcLengthMeasure_List_List_1041
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_482 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1042 .

inst:IfcLengthMeasure_List_List_1042
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_485 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1043 .

inst:IfcLengthMeasure_List_List_1043
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_488 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1044 .

inst:IfcLengthMeasure_List_List_1044
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_491 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1045 .

inst:IfcLengthMeasure_List_List_1045
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_494 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1046 .

inst:IfcLengthMeasure_List_List_1046
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_497 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1047 .

inst:IfcLengthMeasure_List_List_1047
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_500 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1048 .

inst:IfcLengthMeasure_List_List_1048
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_503 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1049 .

inst:IfcLengthMeasure_List_List_1049
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_506 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1050 .

inst:IfcLengthMeasure_List_List_1050
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_509 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1051 .

inst:IfcLengthMeasure_List_List_1051
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_512 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1052 .

inst:IfcLengthMeasure_List_List_1052
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_515 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1053 .

inst:IfcLengthMeasure_List_List_1053
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_518 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1054 .

inst:IfcLengthMeasure_List_List_1054
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_521 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1055 .

inst:IfcLengthMeasure_List_List_1055
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_524 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1056 .

inst:IfcLengthMeasure_List_List_1056
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_527 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1057 .

inst:IfcLengthMeasure_List_List_1057
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_530 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1058 .

inst:IfcLengthMeasure_List_List_1058
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_533 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1059 .

inst:IfcLengthMeasure_List_List_1059
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_536 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1060 .

inst:IfcLengthMeasure_List_List_1060
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_539 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1061 .

inst:IfcLengthMeasure_List_List_1061
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_542 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1062 .

inst:IfcLengthMeasure_List_List_1062
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_545 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1063 .

inst:IfcLengthMeasure_List_List_1063
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_548 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1064 .

inst:IfcLengthMeasure_List_List_1064
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_551 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1065 .

inst:IfcLengthMeasure_List_List_1065
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_554 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1066 .

inst:IfcLengthMeasure_List_List_1066
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_557 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1067 .

inst:IfcLengthMeasure_List_List_1067
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_560 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1068 .

inst:IfcLengthMeasure_List_List_1068
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_563 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1069 .

inst:IfcLengthMeasure_List_List_1069
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_566 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1070 .

inst:IfcLengthMeasure_List_List_1070
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_569 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1071 .

inst:IfcLengthMeasure_List_List_1071
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_572 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1072 .

inst:IfcLengthMeasure_List_List_1072
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_577 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1073 .

inst:IfcLengthMeasure_List_List_1073
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_582 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1074 .

inst:IfcLengthMeasure_List_List_1074
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_587 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1075 .

inst:IfcLengthMeasure_List_List_1075
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_592 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1076 .

inst:IfcLengthMeasure_List_List_1076
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_597 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1077 .

inst:IfcLengthMeasure_List_List_1077
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_602 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1078 .

inst:IfcLengthMeasure_List_List_1078
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_607 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1079 .

inst:IfcLengthMeasure_List_List_1079
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_612 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1080 .

inst:IfcLengthMeasure_List_List_1080
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_616 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1081 .

inst:IfcLengthMeasure_List_List_1081
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_621 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1082 .

inst:IfcLengthMeasure_List_List_1082
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_626 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1083 .

inst:IfcLengthMeasure_List_List_1083
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_631 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1084 .

inst:IfcLengthMeasure_List_List_1084
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_636 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1085 .

inst:IfcLengthMeasure_List_List_1085
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_641 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1086 .

inst:IfcLengthMeasure_List_List_1086
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_646 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1087 .

inst:IfcLengthMeasure_List_List_1087
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_651 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1088 .

inst:IfcLengthMeasure_List_List_1088
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_656 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1089 .

inst:IfcLengthMeasure_List_List_1089
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_661 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1090 .

inst:IfcLengthMeasure_List_List_1090
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_666 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1091 .

inst:IfcLengthMeasure_List_List_1091
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_671 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1092 .

inst:IfcLengthMeasure_List_List_1092
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_676 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1093 .

inst:IfcLengthMeasure_List_List_1093
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_681 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1094 .

inst:IfcLengthMeasure_List_List_1094
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_686 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1095 .

inst:IfcLengthMeasure_List_List_1095
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_691 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1096 .

inst:IfcLengthMeasure_List_List_1096
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_696 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1097 .

inst:IfcLengthMeasure_List_List_1097
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_701 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1098 .

inst:IfcLengthMeasure_List_List_1098
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_706 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1099 .

inst:IfcLengthMeasure_List_List_1099
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_709 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1100 .

inst:IfcLengthMeasure_List_List_1100
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_714 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1101 .

inst:IfcLengthMeasure_List_List_1101
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_719 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1102 .

inst:IfcLengthMeasure_List_List_1102
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_724 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1103 .

inst:IfcLengthMeasure_List_List_1103
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_729 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1104 .

inst:IfcLengthMeasure_List_List_1104
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_734 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1105 .

inst:IfcLengthMeasure_List_List_1105
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_739 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1106 .

inst:IfcLengthMeasure_List_List_1106
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_744 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1107 .

inst:IfcLengthMeasure_List_List_1107
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_749 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1108 .

inst:IfcLengthMeasure_List_List_1108
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_754 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1109 .

inst:IfcLengthMeasure_List_List_1109
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_759 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1110 .

inst:IfcLengthMeasure_List_List_1110
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_764 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1111 .

inst:IfcLengthMeasure_List_List_1111
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_769 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1112 .

inst:IfcLengthMeasure_List_List_1112
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_774 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1113 .

inst:IfcLengthMeasure_List_List_1113
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_777 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1114 .

inst:IfcLengthMeasure_List_List_1114
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_782 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1115 .

inst:IfcLengthMeasure_List_List_1115
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_787 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1116 .

inst:IfcLengthMeasure_List_List_1116
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_792 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1117 .

inst:IfcLengthMeasure_List_List_1117
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_797 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1118 .

inst:IfcLengthMeasure_List_List_1118
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_800 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1119 .

inst:IfcLengthMeasure_List_List_1119
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_803 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1120 .

inst:IfcLengthMeasure_List_List_1120
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_806 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1121 .

inst:IfcLengthMeasure_List_List_1121
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_809 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1122 .

inst:IfcLengthMeasure_List_List_1122
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_812 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1123 .

inst:IfcLengthMeasure_List_List_1123
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_815 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1124 .

inst:IfcLengthMeasure_List_List_1124
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_818 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1125 .

inst:IfcLengthMeasure_List_List_1125
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_821 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1126 .

inst:IfcLengthMeasure_List_List_1126
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_824 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1127 .

inst:IfcLengthMeasure_List_List_1127
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_827 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1128 .

inst:IfcLengthMeasure_List_List_1128
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_830 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1129 .

inst:IfcLengthMeasure_List_List_1129
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_833 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1130 .

inst:IfcLengthMeasure_List_List_1130
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_836 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1131 .

inst:IfcLengthMeasure_List_List_1131
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_839 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1132 .

inst:IfcLengthMeasure_List_List_1132
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_842 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1133 .

inst:IfcLengthMeasure_List_List_1133
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_845 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1134 .

inst:IfcLengthMeasure_List_List_1134
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_848 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1135 .

inst:IfcLengthMeasure_List_List_1135
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_851 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1136 .

inst:IfcLengthMeasure_List_List_1136
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_854 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1137 .

inst:IfcLengthMeasure_List_List_1137
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_857 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1138 .

inst:IfcLengthMeasure_List_List_1138
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_860 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1139 .

inst:IfcLengthMeasure_List_List_1139
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_863 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1140 .

inst:IfcLengthMeasure_List_List_1140
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_866 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1141 .

inst:IfcLengthMeasure_List_List_1141
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_869 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1142 .

inst:IfcLengthMeasure_List_List_1142
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_872 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1143 .

inst:IfcLengthMeasure_List_List_1143
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_875 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1144 .

inst:IfcLengthMeasure_List_List_1144
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_878 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1145 .

inst:IfcLengthMeasure_List_List_1145
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_881 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1146 .

inst:IfcLengthMeasure_List_List_1146
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_884 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1147 .

inst:IfcLengthMeasure_List_List_1147
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_887 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1148 .

inst:IfcLengthMeasure_List_List_1148
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_890 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1149 .

inst:IfcLengthMeasure_List_List_1149
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_893 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1150 .

inst:IfcLengthMeasure_List_List_1150
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_896 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1151 .

inst:IfcLengthMeasure_List_List_1151
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_899 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1152 .

inst:IfcLengthMeasure_List_List_1152
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_902 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1153 .

inst:IfcLengthMeasure_List_List_1153
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_905 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1154 .

inst:IfcLengthMeasure_List_List_1154
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_908 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1155 .

inst:IfcLengthMeasure_List_List_1155
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_911 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1156 .

inst:IfcLengthMeasure_List_List_1156
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_914 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1157 .

inst:IfcLengthMeasure_List_List_1157
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_917 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1158 .

inst:IfcLengthMeasure_List_List_1158
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_920 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1159 .

inst:IfcLengthMeasure_List_List_1159
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_923 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1160 .

inst:IfcLengthMeasure_List_List_1160
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_926 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1161 .

inst:IfcLengthMeasure_List_List_1161
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_929 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1162 .

inst:IfcLengthMeasure_List_List_1162
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_932 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1163 .

inst:IfcLengthMeasure_List_List_1163
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_935 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1164 .

inst:IfcLengthMeasure_List_List_1164
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_938 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1165 .

inst:IfcLengthMeasure_List_List_1165
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_941 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1166 .

inst:IfcLengthMeasure_List_List_1166
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_944 .

inst:IfcLengthMeasure_List_1167
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_9
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_1167 .

inst:IfcLengthMeasure_List_1168
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1169
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1167
        list:hasContents  inst:IfcLengthMeasure_60 ;
        list:hasNext      inst:IfcLengthMeasure_List_1168 .

inst:IfcLengthMeasure_List_1168
        list:hasContents  inst:IfcLengthMeasure_60 ;
        list:hasNext      inst:IfcLengthMeasure_List_1169 .

inst:IfcLengthMeasure_List_1169
        list:hasContents  inst:IfcLengthMeasure_60 .

inst:IfcTriangulatedFaceSet_201
        rdf:type  ifc:IfcTriangulatedFaceSet ;
        ifc:coordinates_IfcTessellatedFaceSet  inst:IfcCartesianPointList3D_200 .

inst:IfcBoolean_1170  rdf:type  ifc:IfcBoolean ;
        express:hasBoolean  true .

inst:IfcTriangulatedFaceSet_201
        ifc:closed_IfcTessellatedFaceSet  inst:IfcBoolean_1170 .

inst:IfcPositiveInteger_List_1171
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1172
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1173
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_1174
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  28 .

inst:IfcPositiveInteger_List_1171
        list:hasContents  inst:IfcPositiveInteger_1174 ;
        list:hasNext      inst:IfcPositiveInteger_List_1172 .

inst:IfcPositiveInteger_1175
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  2 .

inst:IfcPositiveInteger_List_1172
        list:hasContents  inst:IfcPositiveInteger_1175 ;
        list:hasNext      inst:IfcPositiveInteger_List_1173 .

inst:IfcPositiveInteger_1176
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  29 .

inst:IfcPositiveInteger_List_1173
        list:hasContents  inst:IfcPositiveInteger_1176 .

inst:IfcPositiveInteger_List_1177
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1178
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1179
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_1180
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  1 .

inst:IfcPositiveInteger_List_1177
        list:hasContents  inst:IfcPositiveInteger_1180 ;
        list:hasNext      inst:IfcPositiveInteger_List_1178 .

inst:IfcPositiveInteger_List_1178
        list:hasContents  inst:IfcPositiveInteger_1176 ;
        list:hasNext      inst:IfcPositiveInteger_List_1179 .

inst:IfcPositiveInteger_List_1179
        list:hasContents  inst:IfcPositiveInteger_1175 .

inst:IfcPositiveInteger_List_1181
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1182
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1183
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_1184
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  30 .

inst:IfcPositiveInteger_List_1181
        list:hasContents  inst:IfcPositiveInteger_1184 ;
        list:hasNext      inst:IfcPositiveInteger_List_1182 .

inst:IfcPositiveInteger_List_1182
        list:hasContents  inst:IfcPositiveInteger_1180 ;
        list:hasNext      inst:IfcPositiveInteger_List_1183 .

inst:IfcPositiveInteger_1185
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  24 .

inst:IfcPositiveInteger_List_1183
        list:hasContents  inst:IfcPositiveInteger_1185 .

inst:IfcPositiveInteger_List_1186
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1187
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1188
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1186
        list:hasContents  inst:IfcPositiveInteger_1176 ;
        list:hasNext      inst:IfcPositiveInteger_List_1187 .

inst:IfcPositiveInteger_List_1187
        list:hasContents  inst:IfcPositiveInteger_1180 ;
        list:hasNext      inst:IfcPositiveInteger_List_1188 .

inst:IfcPositiveInteger_List_1188
        list:hasContents  inst:IfcPositiveInteger_1184 .

inst:IfcPositiveInteger_List_1189
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1190
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1191
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1189
        list:hasContents  inst:IfcPositiveInteger_1185 ;
        list:hasNext      inst:IfcPositiveInteger_List_1190 .

inst:IfcPositiveInteger_1192
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  31 .

inst:IfcPositiveInteger_List_1190
        list:hasContents  inst:IfcPositiveInteger_1192 ;
        list:hasNext      inst:IfcPositiveInteger_List_1191 .

inst:IfcPositiveInteger_List_1191
        list:hasContents  inst:IfcPositiveInteger_1184 .

inst:IfcPositiveInteger_List_1193
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1194
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1195
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1193
        list:hasContents  inst:IfcDimensionCount_53 ;
        list:hasNext      inst:IfcPositiveInteger_List_1194 .

inst:IfcPositiveInteger_List_1194
        list:hasContents  inst:IfcPositiveInteger_1175 ;
        list:hasNext      inst:IfcPositiveInteger_List_1195 .

inst:IfcPositiveInteger_List_1195
        list:hasContents  inst:IfcPositiveInteger_1174 .

inst:IfcPositiveInteger_List_1196
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1197
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1198
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_1199
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  5 .

inst:IfcPositiveInteger_List_1196
        list:hasContents  inst:IfcPositiveInteger_1199 ;
        list:hasNext      inst:IfcPositiveInteger_List_1197 .

inst:IfcPositiveInteger_1200
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  4 .

inst:IfcPositiveInteger_List_1197
        list:hasContents  inst:IfcPositiveInteger_1200 ;
        list:hasNext      inst:IfcPositiveInteger_List_1198 .

inst:IfcPositiveInteger_1201
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  27 .

inst:IfcPositiveInteger_List_1198
        list:hasContents  inst:IfcPositiveInteger_1201 .

inst:IfcPositiveInteger_List_1202
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1203
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1204
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_1205
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  6 .

inst:IfcPositiveInteger_List_1202
        list:hasContents  inst:IfcPositiveInteger_1205 ;
        list:hasNext      inst:IfcPositiveInteger_List_1203 .

inst:IfcPositiveInteger_List_1203
        list:hasContents  inst:IfcPositiveInteger_1199 ;
        list:hasNext      inst:IfcPositiveInteger_List_1204 .

inst:IfcPositiveInteger_1206
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  25 .

inst:IfcPositiveInteger_List_1204
        list:hasContents  inst:IfcPositiveInteger_1206 .

inst:IfcPositiveInteger_List_1207
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1208
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1209
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1207
        list:hasContents  inst:IfcPositiveInteger_1206 ;
        list:hasNext      inst:IfcPositiveInteger_List_1208 .

inst:IfcPositiveInteger_List_1208
        list:hasContents  inst:IfcPositiveInteger_1199 ;
        list:hasNext      inst:IfcPositiveInteger_List_1209 .

inst:IfcPositiveInteger_1210
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  26 .

inst:IfcPositiveInteger_List_1209
        list:hasContents  inst:IfcPositiveInteger_1210 .

inst:IfcPositiveInteger_List_1211
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1212
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1213
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1211
        list:hasContents  inst:IfcPositiveInteger_1200 ;
        list:hasNext      inst:IfcPositiveInteger_List_1212 .

inst:IfcPositiveInteger_List_1212
        list:hasContents  inst:IfcPositiveInteger_1174 ;
        list:hasNext      inst:IfcPositiveInteger_List_1213 .

inst:IfcPositiveInteger_List_1213
        list:hasContents  inst:IfcPositiveInteger_1201 .

inst:IfcPositiveInteger_List_1214
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1215
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1216
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1214
        list:hasContents  inst:IfcPositiveInteger_1199 ;
        list:hasNext      inst:IfcPositiveInteger_List_1215 .

inst:IfcPositiveInteger_List_1215
        list:hasContents  inst:IfcPositiveInteger_1201 ;
        list:hasNext      inst:IfcPositiveInteger_List_1216 .

inst:IfcPositiveInteger_List_1216
        list:hasContents  inst:IfcPositiveInteger_1210 .

inst:IfcPositiveInteger_List_1217
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1218
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1219
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1217
        list:hasContents  inst:IfcDimensionCount_53 ;
        list:hasNext      inst:IfcPositiveInteger_List_1218 .

inst:IfcPositiveInteger_List_1218
        list:hasContents  inst:IfcPositiveInteger_1174 ;
        list:hasNext      inst:IfcPositiveInteger_List_1219 .

inst:IfcPositiveInteger_List_1219
        list:hasContents  inst:IfcPositiveInteger_1200 .

inst:IfcPositiveInteger_List_1220
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1221
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1222
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_1223
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  23 .

inst:IfcPositiveInteger_List_1220
        list:hasContents  inst:IfcPositiveInteger_1223 ;
        list:hasNext      inst:IfcPositiveInteger_List_1221 .

inst:IfcPositiveInteger_1224
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  32 .

inst:IfcPositiveInteger_List_1221
        list:hasContents  inst:IfcPositiveInteger_1224 ;
        list:hasNext      inst:IfcPositiveInteger_List_1222 .

inst:IfcPositiveInteger_List_1222
        list:hasContents  inst:IfcPositiveInteger_1192 .

inst:IfcPositiveInteger_List_1225
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1226
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1227
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_1228
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  33 .

inst:IfcPositiveInteger_List_1225
        list:hasContents  inst:IfcPositiveInteger_1228 ;
        list:hasNext      inst:IfcPositiveInteger_List_1226 .

inst:IfcPositiveInteger_List_1226
        list:hasContents  inst:IfcPositiveInteger_1224 ;
        list:hasNext      inst:IfcPositiveInteger_List_1227 .

inst:IfcPositiveInteger_List_1227
        list:hasContents  inst:IfcPositiveInteger_1223 .

inst:IfcPositiveInteger_List_1229
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1230
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1231
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1229
        list:hasContents  inst:IfcPositiveInteger_1185 ;
        list:hasNext      inst:IfcPositiveInteger_List_1230 .

inst:IfcPositiveInteger_List_1230
        list:hasContents  inst:IfcPositiveInteger_1223 ;
        list:hasNext      inst:IfcPositiveInteger_List_1231 .

inst:IfcPositiveInteger_List_1231
        list:hasContents  inst:IfcPositiveInteger_1192 .

inst:IfcPositiveInteger_List_1232
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1233
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1234
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_1235
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  34 .

inst:IfcPositiveInteger_List_1232
        list:hasContents  inst:IfcPositiveInteger_1235 ;
        list:hasNext      inst:IfcPositiveInteger_List_1233 .

inst:IfcPositiveInteger_1236
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  22 .

inst:IfcPositiveInteger_List_1233
        list:hasContents  inst:IfcPositiveInteger_1236 ;
        list:hasNext      inst:IfcPositiveInteger_List_1234 .

inst:IfcPositiveInteger_1237
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  21 .

inst:IfcPositiveInteger_List_1234
        list:hasContents  inst:IfcPositiveInteger_1237 .

inst:IfcPositiveInteger_List_1238
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1239
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1240
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1238
        list:hasContents  inst:IfcPositiveInteger_1223 ;
        list:hasNext      inst:IfcPositiveInteger_List_1239 .

inst:IfcPositiveInteger_List_1239
        list:hasContents  inst:IfcPositiveInteger_1236 ;
        list:hasNext      inst:IfcPositiveInteger_List_1240 .

inst:IfcPositiveInteger_List_1240
        list:hasContents  inst:IfcPositiveInteger_1228 .

inst:IfcPositiveInteger_List_1241
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1242
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1243
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1241
        list:hasContents  inst:IfcPositiveInteger_1236 ;
        list:hasNext      inst:IfcPositiveInteger_List_1242 .

inst:IfcPositiveInteger_List_1242
        list:hasContents  inst:IfcPositiveInteger_1235 ;
        list:hasNext      inst:IfcPositiveInteger_List_1243 .

inst:IfcPositiveInteger_List_1243
        list:hasContents  inst:IfcPositiveInteger_1228 .

inst:IfcPositiveInteger_List_1244
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1245
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1246
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1244
        list:hasContents  inst:IfcPositiveInteger_1237 ;
        list:hasNext      inst:IfcPositiveInteger_List_1245 .

inst:IfcPositiveInteger_1247
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  20 .

inst:IfcPositiveInteger_List_1245
        list:hasContents  inst:IfcPositiveInteger_1247 ;
        list:hasNext      inst:IfcPositiveInteger_List_1246 .

inst:IfcPositiveInteger_1248
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  35 .

inst:IfcPositiveInteger_List_1246
        list:hasContents  inst:IfcPositiveInteger_1248 .

inst:IfcPositiveInteger_List_1249
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1250
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1251
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_1252
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  36 .

inst:IfcPositiveInteger_List_1249
        list:hasContents  inst:IfcPositiveInteger_1252 ;
        list:hasNext      inst:IfcPositiveInteger_List_1250 .

inst:IfcPositiveInteger_List_1250
        list:hasContents  inst:IfcPositiveInteger_1248 ;
        list:hasNext      inst:IfcPositiveInteger_List_1251 .

inst:IfcPositiveInteger_List_1251
        list:hasContents  inst:IfcPositiveInteger_1247 .

inst:IfcPositiveInteger_List_1253
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1254
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1255
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1253
        list:hasContents  inst:IfcPositiveInteger_1235 ;
        list:hasNext      inst:IfcPositiveInteger_List_1254 .

inst:IfcPositiveInteger_List_1254
        list:hasContents  inst:IfcPositiveInteger_1237 ;
        list:hasNext      inst:IfcPositiveInteger_List_1255 .

inst:IfcPositiveInteger_List_1255
        list:hasContents  inst:IfcPositiveInteger_1248 .

inst:IfcPositiveInteger_List_1256
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1257
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1258
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_1259
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  37 .

inst:IfcPositiveInteger_List_1256
        list:hasContents  inst:IfcPositiveInteger_1259 ;
        list:hasNext      inst:IfcPositiveInteger_List_1257 .

inst:IfcPositiveInteger_List_1257
        list:hasContents  inst:IfcPositiveInteger_1252 ;
        list:hasNext      inst:IfcPositiveInteger_List_1258 .

inst:IfcPositiveInteger_1260
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  19 .

inst:IfcPositiveInteger_List_1258
        list:hasContents  inst:IfcPositiveInteger_1260 .

inst:IfcPositiveInteger_List_1261
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1262
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1263
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1261
        list:hasContents  inst:IfcPositiveInteger_1247 ;
        list:hasNext      inst:IfcPositiveInteger_List_1262 .

inst:IfcPositiveInteger_List_1262
        list:hasContents  inst:IfcPositiveInteger_1260 ;
        list:hasNext      inst:IfcPositiveInteger_List_1263 .

inst:IfcPositiveInteger_List_1263
        list:hasContents  inst:IfcPositiveInteger_1252 .

inst:IfcPositiveInteger_List_1264
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1265
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1266
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_1267
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  18 .

inst:IfcPositiveInteger_List_1264
        list:hasContents  inst:IfcPositiveInteger_1267 ;
        list:hasNext      inst:IfcPositiveInteger_List_1265 .

inst:IfcPositiveInteger_List_1265
        list:hasContents  inst:IfcPositiveInteger_1259 ;
        list:hasNext      inst:IfcPositiveInteger_List_1266 .

inst:IfcPositiveInteger_List_1266
        list:hasContents  inst:IfcPositiveInteger_1260 .

inst:IfcPositiveInteger_List_1268
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1269
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1270
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_1271
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  7 .

inst:IfcPositiveInteger_List_1268
        list:hasContents  inst:IfcPositiveInteger_1271 ;
        list:hasNext      inst:IfcPositiveInteger_List_1269 .

inst:IfcPositiveInteger_List_1269
        list:hasContents  inst:IfcPositiveInteger_1205 ;
        list:hasNext      inst:IfcPositiveInteger_List_1270 .

inst:IfcPositiveInteger_1272
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  48 .

inst:IfcPositiveInteger_List_1270
        list:hasContents  inst:IfcPositiveInteger_1272 .

inst:IfcPositiveInteger_List_1273
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1274
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1275
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_1276
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  8 .

inst:IfcPositiveInteger_List_1273
        list:hasContents  inst:IfcPositiveInteger_1276 ;
        list:hasNext      inst:IfcPositiveInteger_List_1274 .

inst:IfcPositiveInteger_List_1274
        list:hasContents  inst:IfcPositiveInteger_1271 ;
        list:hasNext      inst:IfcPositiveInteger_List_1275 .

inst:IfcPositiveInteger_1277
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  47 .

inst:IfcPositiveInteger_List_1275
        list:hasContents  inst:IfcPositiveInteger_1277 .

inst:IfcPositiveInteger_List_1278
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1279
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1280
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1278
        list:hasContents  inst:IfcPositiveInteger_1271 ;
        list:hasNext      inst:IfcPositiveInteger_List_1279 .

inst:IfcPositiveInteger_List_1279
        list:hasContents  inst:IfcPositiveInteger_1272 ;
        list:hasNext      inst:IfcPositiveInteger_List_1280 .

inst:IfcPositiveInteger_List_1280
        list:hasContents  inst:IfcPositiveInteger_1277 .

inst:IfcPositiveInteger_List_1281
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1282
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1283
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1281
        list:hasContents  inst:IfcPositiveInteger_1276 ;
        list:hasNext      inst:IfcPositiveInteger_List_1282 .

inst:IfcPositiveInteger_List_1282
        list:hasContents  inst:IfcPositiveInteger_1277 ;
        list:hasNext      inst:IfcPositiveInteger_List_1283 .

inst:IfcPositiveInteger_1284
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  46 .

inst:IfcPositiveInteger_List_1283
        list:hasContents  inst:IfcPositiveInteger_1284 .

inst:IfcPositiveInteger_List_1285
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1286
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1287
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1285
        list:hasContents  inst:IfcPositiveInteger_1284 ;
        list:hasNext      inst:IfcPositiveInteger_List_1286 .

inst:IfcPositiveInteger_1288
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  9 .

inst:IfcPositiveInteger_List_1286
        list:hasContents  inst:IfcPositiveInteger_1288 ;
        list:hasNext      inst:IfcPositiveInteger_List_1287 .

inst:IfcPositiveInteger_List_1287
        list:hasContents  inst:IfcPositiveInteger_1276 .

inst:IfcPositiveInteger_List_1289
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1290
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1291
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1289
        list:hasContents  inst:IfcPositiveInteger_1284 ;
        list:hasNext      inst:IfcPositiveInteger_List_1290 .

inst:IfcPositiveInteger_1292
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  45 .

inst:IfcPositiveInteger_List_1290
        list:hasContents  inst:IfcPositiveInteger_1292 ;
        list:hasNext      inst:IfcPositiveInteger_List_1291 .

inst:IfcPositiveInteger_1293
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  10 .

inst:IfcPositiveInteger_List_1291
        list:hasContents  inst:IfcPositiveInteger_1293 .

inst:IfcPositiveInteger_List_1294
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1295
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1296
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_1297
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  11 .

inst:IfcPositiveInteger_List_1294
        list:hasContents  inst:IfcPositiveInteger_1297 ;
        list:hasNext      inst:IfcPositiveInteger_List_1295 .

inst:IfcPositiveInteger_List_1295
        list:hasContents  inst:IfcPositiveInteger_1293 ;
        list:hasNext      inst:IfcPositiveInteger_List_1296 .

inst:IfcPositiveInteger_List_1296
        list:hasContents  inst:IfcPositiveInteger_1292 .

inst:IfcPositiveInteger_List_1298
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1299
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1300
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_1301
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  12 .

inst:IfcPositiveInteger_List_1298
        list:hasContents  inst:IfcPositiveInteger_1301 ;
        list:hasNext      inst:IfcPositiveInteger_List_1299 .

inst:IfcPositiveInteger_List_1299
        list:hasContents  inst:IfcPositiveInteger_1297 ;
        list:hasNext      inst:IfcPositiveInteger_List_1300 .

inst:IfcPositiveInteger_1302
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  44 .

inst:IfcPositiveInteger_List_1300
        list:hasContents  inst:IfcPositiveInteger_1302 .

inst:IfcPositiveInteger_List_1303
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1304
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1305
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1303
        list:hasContents  inst:IfcPositiveInteger_1292 ;
        list:hasNext      inst:IfcPositiveInteger_List_1304 .

inst:IfcPositiveInteger_List_1304
        list:hasContents  inst:IfcPositiveInteger_1302 ;
        list:hasNext      inst:IfcPositiveInteger_List_1305 .

inst:IfcPositiveInteger_List_1305
        list:hasContents  inst:IfcPositiveInteger_1297 .

inst:IfcPositiveInteger_List_1306
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1307
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1308
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1306
        list:hasContents  inst:IfcPositiveInteger_1293 ;
        list:hasNext      inst:IfcPositiveInteger_List_1307 .

inst:IfcPositiveInteger_List_1307
        list:hasContents  inst:IfcPositiveInteger_1288 ;
        list:hasNext      inst:IfcPositiveInteger_List_1308 .

inst:IfcPositiveInteger_List_1308
        list:hasContents  inst:IfcPositiveInteger_1284 .

inst:IfcPositiveInteger_List_1309
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1310
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1311
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1309
        list:hasContents  inst:IfcPositiveInteger_1301 ;
        list:hasNext      inst:IfcPositiveInteger_List_1310 .

inst:IfcPositiveInteger_List_1310
        list:hasContents  inst:IfcPositiveInteger_1302 ;
        list:hasNext      inst:IfcPositiveInteger_List_1311 .

inst:IfcPositiveInteger_1312
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  43 .

inst:IfcPositiveInteger_List_1311
        list:hasContents  inst:IfcPositiveInteger_1312 .

inst:IfcPositiveInteger_List_1313
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1314
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1315
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_1316
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  15 .

inst:IfcPositiveInteger_List_1313
        list:hasContents  inst:IfcPositiveInteger_1316 ;
        list:hasNext      inst:IfcPositiveInteger_List_1314 .

inst:IfcPositiveInteger_1317
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  39 .

inst:IfcPositiveInteger_List_1314
        list:hasContents  inst:IfcPositiveInteger_1317 ;
        list:hasNext      inst:IfcPositiveInteger_List_1315 .

inst:IfcPositiveInteger_1318
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  16 .

inst:IfcPositiveInteger_List_1315
        list:hasContents  inst:IfcPositiveInteger_1318 .

inst:IfcPositiveInteger_List_1319
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1320
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1321
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_1322
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  40 .

inst:IfcPositiveInteger_List_1319
        list:hasContents  inst:IfcPositiveInteger_1322 ;
        list:hasNext      inst:IfcPositiveInteger_List_1320 .

inst:IfcPositiveInteger_List_1320
        list:hasContents  inst:IfcPositiveInteger_1317 ;
        list:hasNext      inst:IfcPositiveInteger_List_1321 .

inst:IfcPositiveInteger_List_1321
        list:hasContents  inst:IfcPositiveInteger_1316 .

inst:IfcPositiveInteger_List_1323
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1324
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1325
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_1326
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  38 .

inst:IfcPositiveInteger_List_1323
        list:hasContents  inst:IfcPositiveInteger_1326 ;
        list:hasNext      inst:IfcPositiveInteger_List_1324 .

inst:IfcPositiveInteger_List_1324
        list:hasContents  inst:IfcPositiveInteger_1318 ;
        list:hasNext      inst:IfcPositiveInteger_List_1325 .

inst:IfcPositiveInteger_List_1325
        list:hasContents  inst:IfcPositiveInteger_1317 .

inst:IfcPositiveInteger_List_1327
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1328
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1329
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1327
        list:hasContents  inst:IfcPositiveInteger_1267 ;
        list:hasNext      inst:IfcPositiveInteger_List_1328 .

inst:IfcPositiveInteger_1330
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  17 .

inst:IfcPositiveInteger_List_1328
        list:hasContents  inst:IfcPositiveInteger_1330 ;
        list:hasNext      inst:IfcPositiveInteger_List_1329 .

inst:IfcPositiveInteger_List_1329
        list:hasContents  inst:IfcPositiveInteger_1259 .

inst:IfcPositiveInteger_List_1331
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1332
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1333
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1331
        list:hasContents  inst:IfcPositiveInteger_1318 ;
        list:hasNext      inst:IfcPositiveInteger_List_1332 .

inst:IfcPositiveInteger_List_1332
        list:hasContents  inst:IfcPositiveInteger_1326 ;
        list:hasNext      inst:IfcPositiveInteger_List_1333 .

inst:IfcPositiveInteger_List_1333
        list:hasContents  inst:IfcPositiveInteger_1330 .

inst:IfcPositiveInteger_List_1334
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1335
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1336
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1334
        list:hasContents  inst:IfcPositiveInteger_1330 ;
        list:hasNext      inst:IfcPositiveInteger_List_1335 .

inst:IfcPositiveInteger_List_1335
        list:hasContents  inst:IfcPositiveInteger_1326 ;
        list:hasNext      inst:IfcPositiveInteger_List_1336 .

inst:IfcPositiveInteger_List_1336
        list:hasContents  inst:IfcPositiveInteger_1259 .

inst:IfcPositiveInteger_List_1337
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1338
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1339
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_1340
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  13 .

inst:IfcPositiveInteger_List_1337
        list:hasContents  inst:IfcPositiveInteger_1340 ;
        list:hasNext      inst:IfcPositiveInteger_List_1338 .

inst:IfcPositiveInteger_List_1338
        list:hasContents  inst:IfcPositiveInteger_1312 ;
        list:hasNext      inst:IfcPositiveInteger_List_1339 .

inst:IfcPositiveInteger_1341
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  42 .

inst:IfcPositiveInteger_List_1339
        list:hasContents  inst:IfcPositiveInteger_1341 .

inst:IfcPositiveInteger_List_1342
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1343
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1344
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1342
        list:hasContents  inst:IfcPositiveInteger_1301 ;
        list:hasNext      inst:IfcPositiveInteger_List_1343 .

inst:IfcPositiveInteger_List_1343
        list:hasContents  inst:IfcPositiveInteger_1312 ;
        list:hasNext      inst:IfcPositiveInteger_List_1344 .

inst:IfcPositiveInteger_List_1344
        list:hasContents  inst:IfcPositiveInteger_1340 .

inst:IfcPositiveInteger_List_1345
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1346
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1347
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_1348
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  14 .

inst:IfcPositiveInteger_List_1345
        list:hasContents  inst:IfcPositiveInteger_1348 ;
        list:hasNext      inst:IfcPositiveInteger_List_1346 .

inst:IfcPositiveInteger_List_1346
        list:hasContents  inst:IfcPositiveInteger_1340 ;
        list:hasNext      inst:IfcPositiveInteger_List_1347 .

inst:IfcPositiveInteger_List_1347
        list:hasContents  inst:IfcPositiveInteger_1341 .

inst:IfcPositiveInteger_List_1349
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1350
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1351
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1349
        list:hasContents  inst:IfcPositiveInteger_1316 ;
        list:hasNext      inst:IfcPositiveInteger_List_1350 .

inst:IfcPositiveInteger_List_1350
        list:hasContents  inst:IfcPositiveInteger_1348 ;
        list:hasNext      inst:IfcPositiveInteger_List_1351 .

inst:IfcPositiveInteger_List_1351
        list:hasContents  inst:IfcPositiveInteger_1322 .

inst:IfcPositiveInteger_List_1352
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1353
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1354
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1352
        list:hasContents  inst:IfcPositiveInteger_1348 ;
        list:hasNext      inst:IfcPositiveInteger_List_1353 .

inst:IfcPositiveInteger_1355
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  41 .

inst:IfcPositiveInteger_List_1353
        list:hasContents  inst:IfcPositiveInteger_1355 ;
        list:hasNext      inst:IfcPositiveInteger_List_1354 .

inst:IfcPositiveInteger_List_1354
        list:hasContents  inst:IfcPositiveInteger_1322 .

inst:IfcPositiveInteger_List_1356
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1357
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1358
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1356
        list:hasContents  inst:IfcPositiveInteger_1341 ;
        list:hasNext      inst:IfcPositiveInteger_List_1357 .

inst:IfcPositiveInteger_List_1357
        list:hasContents  inst:IfcPositiveInteger_1355 ;
        list:hasNext      inst:IfcPositiveInteger_List_1358 .

inst:IfcPositiveInteger_List_1358
        list:hasContents  inst:IfcPositiveInteger_1348 .

inst:IfcPositiveInteger_List_1359
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1360
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1361
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1359
        list:hasContents  inst:IfcPositiveInteger_1272 ;
        list:hasNext      inst:IfcPositiveInteger_List_1360 .

inst:IfcPositiveInteger_List_1360
        list:hasContents  inst:IfcPositiveInteger_1205 ;
        list:hasNext      inst:IfcPositiveInteger_List_1361 .

inst:IfcPositiveInteger_List_1361
        list:hasContents  inst:IfcPositiveInteger_1206 .

inst:IfcPositiveInteger_List_1362
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1363
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1364
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_1365
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  50 .

inst:IfcPositiveInteger_List_1362
        list:hasContents  inst:IfcPositiveInteger_1365 ;
        list:hasNext      inst:IfcPositiveInteger_List_1363 .

inst:IfcPositiveInteger_1366
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  72 .

inst:IfcPositiveInteger_List_1363
        list:hasContents  inst:IfcPositiveInteger_1366 ;
        list:hasNext      inst:IfcPositiveInteger_List_1364 .

inst:IfcPositiveInteger_1367
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  49 .

inst:IfcPositiveInteger_List_1364
        list:hasContents  inst:IfcPositiveInteger_1367 .

inst:IfcPositiveInteger_List_1368
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1369
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1370
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_1371
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  51 .

inst:IfcPositiveInteger_List_1368
        list:hasContents  inst:IfcPositiveInteger_1371 ;
        list:hasNext      inst:IfcPositiveInteger_List_1369 .

inst:IfcPositiveInteger_List_1369
        list:hasContents  inst:IfcPositiveInteger_1366 ;
        list:hasNext      inst:IfcPositiveInteger_List_1370 .

inst:IfcPositiveInteger_List_1370
        list:hasContents  inst:IfcPositiveInteger_1365 .

inst:IfcPositiveInteger_List_1372
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1373
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1374
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_1375
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  71 .

inst:IfcPositiveInteger_List_1372
        list:hasContents  inst:IfcPositiveInteger_1375 ;
        list:hasNext      inst:IfcPositiveInteger_List_1373 .

inst:IfcPositiveInteger_List_1373
        list:hasContents  inst:IfcPositiveInteger_1366 ;
        list:hasNext      inst:IfcPositiveInteger_List_1374 .

inst:IfcPositiveInteger_1376
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  52 .

inst:IfcPositiveInteger_List_1374
        list:hasContents  inst:IfcPositiveInteger_1376 .

inst:IfcPositiveInteger_List_1377
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1378
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1379
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1377
        list:hasContents  inst:IfcPositiveInteger_1371 ;
        list:hasNext      inst:IfcPositiveInteger_List_1378 .

inst:IfcPositiveInteger_List_1378
        list:hasContents  inst:IfcPositiveInteger_1376 ;
        list:hasNext      inst:IfcPositiveInteger_List_1379 .

inst:IfcPositiveInteger_List_1379
        list:hasContents  inst:IfcPositiveInteger_1366 .

inst:IfcPositiveInteger_List_1380
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1381
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1382
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_1383
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  53 .

inst:IfcPositiveInteger_List_1380
        list:hasContents  inst:IfcPositiveInteger_1383 ;
        list:hasNext      inst:IfcPositiveInteger_List_1381 .

inst:IfcPositiveInteger_List_1381
        list:hasContents  inst:IfcPositiveInteger_1375 ;
        list:hasNext      inst:IfcPositiveInteger_List_1382 .

inst:IfcPositiveInteger_List_1382
        list:hasContents  inst:IfcPositiveInteger_1376 .

inst:IfcPositiveInteger_List_1384
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1385
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1386
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_1387
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  69 .

inst:IfcPositiveInteger_List_1384
        list:hasContents  inst:IfcPositiveInteger_1387 ;
        list:hasNext      inst:IfcPositiveInteger_List_1385 .

inst:IfcPositiveInteger_1388
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  70 .

inst:IfcPositiveInteger_List_1385
        list:hasContents  inst:IfcPositiveInteger_1388 ;
        list:hasNext      inst:IfcPositiveInteger_List_1386 .

inst:IfcPositiveInteger_1389
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  63 .

inst:IfcPositiveInteger_List_1386
        list:hasContents  inst:IfcPositiveInteger_1389 .

inst:IfcPositiveInteger_List_1390
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1391
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1392
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1390
        list:hasContents  inst:IfcPositiveInteger_1375 ;
        list:hasNext      inst:IfcPositiveInteger_List_1391 .

inst:IfcPositiveInteger_1393
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  54 .

inst:IfcPositiveInteger_List_1391
        list:hasContents  inst:IfcPositiveInteger_1393 ;
        list:hasNext      inst:IfcPositiveInteger_List_1392 .

inst:IfcPositiveInteger_List_1392
        list:hasContents  inst:IfcPositiveInteger_1388 .

inst:IfcPositiveInteger_List_1394
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1395
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1396
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_1397
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  66 .

inst:IfcPositiveInteger_List_1394
        list:hasContents  inst:IfcPositiveInteger_1397 ;
        list:hasNext      inst:IfcPositiveInteger_List_1395 .

inst:IfcPositiveInteger_1398
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  67 .

inst:IfcPositiveInteger_List_1395
        list:hasContents  inst:IfcPositiveInteger_1398 ;
        list:hasNext      inst:IfcPositiveInteger_List_1396 .

inst:IfcPositiveInteger_1399
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  65 .

inst:IfcPositiveInteger_List_1396
        list:hasContents  inst:IfcPositiveInteger_1399 .

inst:IfcPositiveInteger_List_1400
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1401
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1402
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1400
        list:hasContents  inst:IfcPositiveInteger_1398 ;
        list:hasNext      inst:IfcPositiveInteger_List_1401 .

inst:IfcPositiveInteger_1403
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  68 .

inst:IfcPositiveInteger_List_1401
        list:hasContents  inst:IfcPositiveInteger_1403 ;
        list:hasNext      inst:IfcPositiveInteger_List_1402 .

inst:IfcPositiveInteger_List_1402
        list:hasContents  inst:IfcPositiveInteger_1399 .

inst:IfcPositiveInteger_List_1404
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1405
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1406
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1404
        list:hasContents  inst:IfcPositiveInteger_1403 ;
        list:hasNext      inst:IfcPositiveInteger_List_1405 .

inst:IfcPositiveInteger_List_1405
        list:hasContents  inst:IfcPositiveInteger_1387 ;
        list:hasNext      inst:IfcPositiveInteger_List_1406 .

inst:IfcPositiveInteger_1407
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  64 .

inst:IfcPositiveInteger_List_1406
        list:hasContents  inst:IfcPositiveInteger_1407 .

inst:IfcPositiveInteger_List_1408
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1409
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1410
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1408
        list:hasContents  inst:IfcPositiveInteger_1375 ;
        list:hasNext      inst:IfcPositiveInteger_List_1409 .

inst:IfcPositiveInteger_List_1409
        list:hasContents  inst:IfcPositiveInteger_1383 ;
        list:hasNext      inst:IfcPositiveInteger_List_1410 .

inst:IfcPositiveInteger_List_1410
        list:hasContents  inst:IfcPositiveInteger_1393 .

inst:IfcPositiveInteger_List_1411
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1412
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1413
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1411
        list:hasContents  inst:IfcPositiveInteger_1393 ;
        list:hasNext      inst:IfcPositiveInteger_List_1412 .

inst:IfcPositiveInteger_1414
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  55 .

inst:IfcPositiveInteger_List_1412
        list:hasContents  inst:IfcPositiveInteger_1414 ;
        list:hasNext      inst:IfcPositiveInteger_List_1413 .

inst:IfcPositiveInteger_1415
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  61 .

inst:IfcPositiveInteger_List_1413
        list:hasContents  inst:IfcPositiveInteger_1415 .

inst:IfcPositiveInteger_List_1416
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1417
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1418
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1416
        list:hasContents  inst:IfcPositiveInteger_1414 ;
        list:hasNext      inst:IfcPositiveInteger_List_1417 .

inst:IfcPositiveInteger_1419
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  56 .

inst:IfcPositiveInteger_List_1417
        list:hasContents  inst:IfcPositiveInteger_1419 ;
        list:hasNext      inst:IfcPositiveInteger_List_1418 .

inst:IfcPositiveInteger_List_1418
        list:hasContents  inst:IfcPositiveInteger_1415 .

inst:IfcPositiveInteger_List_1420
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1421
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1422
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_1423
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  58 .

inst:IfcPositiveInteger_List_1420
        list:hasContents  inst:IfcPositiveInteger_1423 ;
        list:hasNext      inst:IfcPositiveInteger_List_1421 .

inst:IfcPositiveInteger_1424
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  60 .

inst:IfcPositiveInteger_List_1421
        list:hasContents  inst:IfcPositiveInteger_1424 ;
        list:hasNext      inst:IfcPositiveInteger_List_1422 .

inst:IfcPositiveInteger_1425
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  57 .

inst:IfcPositiveInteger_List_1422
        list:hasContents  inst:IfcPositiveInteger_1425 .

inst:IfcPositiveInteger_List_1426
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1427
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1428
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1426
        list:hasContents  inst:IfcPositiveInteger_1424 ;
        list:hasNext      inst:IfcPositiveInteger_List_1427 .

inst:IfcPositiveInteger_List_1427
        list:hasContents  inst:IfcPositiveInteger_1419 ;
        list:hasNext      inst:IfcPositiveInteger_List_1428 .

inst:IfcPositiveInteger_List_1428
        list:hasContents  inst:IfcPositiveInteger_1425 .

inst:IfcPositiveInteger_List_1429
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1430
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1431
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_1432
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  59 .

inst:IfcPositiveInteger_List_1429
        list:hasContents  inst:IfcPositiveInteger_1432 ;
        list:hasNext      inst:IfcPositiveInteger_List_1430 .

inst:IfcPositiveInteger_List_1430
        list:hasContents  inst:IfcPositiveInteger_1424 ;
        list:hasNext      inst:IfcPositiveInteger_List_1431 .

inst:IfcPositiveInteger_List_1431
        list:hasContents  inst:IfcPositiveInteger_1423 .

inst:IfcPositiveInteger_List_1433
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1434
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1435
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1433
        list:hasContents  inst:IfcPositiveInteger_1399 ;
        list:hasNext      inst:IfcPositiveInteger_List_1434 .

inst:IfcPositiveInteger_List_1434
        list:hasContents  inst:IfcPositiveInteger_1403 ;
        list:hasNext      inst:IfcPositiveInteger_List_1435 .

inst:IfcPositiveInteger_List_1435
        list:hasContents  inst:IfcPositiveInteger_1407 .

inst:IfcPositiveInteger_List_1436
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1437
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1438
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1436
        list:hasContents  inst:IfcPositiveInteger_1387 ;
        list:hasNext      inst:IfcPositiveInteger_List_1437 .

inst:IfcPositiveInteger_List_1437
        list:hasContents  inst:IfcPositiveInteger_1389 ;
        list:hasNext      inst:IfcPositiveInteger_List_1438 .

inst:IfcPositiveInteger_List_1438
        list:hasContents  inst:IfcPositiveInteger_1407 .

inst:IfcPositiveInteger_List_1439
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1440
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1441
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_1442
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  62 .

inst:IfcPositiveInteger_List_1439
        list:hasContents  inst:IfcPositiveInteger_1442 ;
        list:hasNext      inst:IfcPositiveInteger_List_1440 .

inst:IfcPositiveInteger_List_1440
        list:hasContents  inst:IfcPositiveInteger_1389 ;
        list:hasNext      inst:IfcPositiveInteger_List_1441 .

inst:IfcPositiveInteger_List_1441
        list:hasContents  inst:IfcPositiveInteger_1388 .

inst:IfcPositiveInteger_List_1443
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1444
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1445
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1443
        list:hasContents  inst:IfcPositiveInteger_1442 ;
        list:hasNext      inst:IfcPositiveInteger_List_1444 .

inst:IfcPositiveInteger_List_1444
        list:hasContents  inst:IfcPositiveInteger_1393 ;
        list:hasNext      inst:IfcPositiveInteger_List_1445 .

inst:IfcPositiveInteger_List_1445
        list:hasContents  inst:IfcPositiveInteger_1415 .

inst:IfcPositiveInteger_List_1446
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1447
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1448
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1446
        list:hasContents  inst:IfcPositiveInteger_1415 ;
        list:hasNext      inst:IfcPositiveInteger_List_1447 .

inst:IfcPositiveInteger_List_1447
        list:hasContents  inst:IfcPositiveInteger_1419 ;
        list:hasNext      inst:IfcPositiveInteger_List_1448 .

inst:IfcPositiveInteger_List_1448
        list:hasContents  inst:IfcPositiveInteger_1424 .

inst:IfcPositiveInteger_List_1449
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1450
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1451
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1449
        list:hasContents  inst:IfcPositiveInteger_1442 ;
        list:hasNext      inst:IfcPositiveInteger_List_1450 .

inst:IfcPositiveInteger_List_1450
        list:hasContents  inst:IfcPositiveInteger_1388 ;
        list:hasNext      inst:IfcPositiveInteger_List_1451 .

inst:IfcPositiveInteger_List_1451
        list:hasContents  inst:IfcPositiveInteger_1393 .

inst:IfcPositiveInteger_List_1452
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1453
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1454
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_1455
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  74 .

inst:IfcPositiveInteger_List_1452
        list:hasContents  inst:IfcPositiveInteger_1455 ;
        list:hasNext      inst:IfcPositiveInteger_List_1453 .

inst:IfcPositiveInteger_1456
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  73 .

inst:IfcPositiveInteger_List_1453
        list:hasContents  inst:IfcPositiveInteger_1456 ;
        list:hasNext      inst:IfcPositiveInteger_List_1454 .

inst:IfcPositiveInteger_1457
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  76 .

inst:IfcPositiveInteger_List_1454
        list:hasContents  inst:IfcPositiveInteger_1457 .

inst:IfcPositiveInteger_List_1458
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1459
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1460
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_1461
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  80 .

inst:IfcPositiveInteger_List_1458
        list:hasContents  inst:IfcPositiveInteger_1461 ;
        list:hasNext      inst:IfcPositiveInteger_List_1459 .

inst:IfcPositiveInteger_1462
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  125 .

inst:IfcPositiveInteger_List_1459
        list:hasContents  inst:IfcPositiveInteger_1462 ;
        list:hasNext      inst:IfcPositiveInteger_List_1460 .

inst:IfcPositiveInteger_1463
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  126 .

inst:IfcPositiveInteger_List_1460
        list:hasContents  inst:IfcPositiveInteger_1463 .

inst:IfcPositiveInteger_List_1464
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1465
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1466
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1464
        list:hasContents  inst:IfcPositiveInteger_1463 ;
        list:hasNext      inst:IfcPositiveInteger_List_1465 .

inst:IfcPositiveInteger_List_1465
        list:hasContents  inst:IfcPositiveInteger_1457 ;
        list:hasNext      inst:IfcPositiveInteger_List_1466 .

inst:IfcPositiveInteger_1467
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  78 .

inst:IfcPositiveInteger_List_1466
        list:hasContents  inst:IfcPositiveInteger_1467 .

inst:IfcPositiveInteger_List_1468
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1469
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1470
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1468
        list:hasContents  inst:IfcPositiveInteger_1463 ;
        list:hasNext      inst:IfcPositiveInteger_List_1469 .

inst:IfcPositiveInteger_1471
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  77 .

inst:IfcPositiveInteger_List_1469
        list:hasContents  inst:IfcPositiveInteger_1471 ;
        list:hasNext      inst:IfcPositiveInteger_List_1470 .

inst:IfcPositiveInteger_List_1470
        list:hasContents  inst:IfcPositiveInteger_1457 .

inst:IfcPositiveInteger_List_1472
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1473
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1474
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1472
        list:hasContents  inst:IfcPositiveInteger_1457 ;
        list:hasNext      inst:IfcPositiveInteger_List_1473 .

inst:IfcPositiveInteger_List_1473
        list:hasContents  inst:IfcPositiveInteger_1471 ;
        list:hasNext      inst:IfcPositiveInteger_List_1474 .

inst:IfcPositiveInteger_List_1474
        list:hasContents  inst:IfcPositiveInteger_1455 .

inst:IfcPositiveInteger_List_1475
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1476
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1477
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_1478
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  82 .

inst:IfcPositiveInteger_List_1475
        list:hasContents  inst:IfcPositiveInteger_1478 ;
        list:hasNext      inst:IfcPositiveInteger_List_1476 .

inst:IfcPositiveInteger_1479
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  127 .

inst:IfcPositiveInteger_List_1476
        list:hasContents  inst:IfcPositiveInteger_1479 ;
        list:hasNext      inst:IfcPositiveInteger_List_1477 .

inst:IfcPositiveInteger_List_1477
        list:hasContents  inst:IfcPositiveInteger_1462 .

inst:IfcPositiveInteger_List_1480
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1481
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1482
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1480
        list:hasContents  inst:IfcPositiveInteger_1479 ;
        list:hasNext      inst:IfcPositiveInteger_List_1481 .

inst:IfcPositiveInteger_List_1481
        list:hasContents  inst:IfcPositiveInteger_1478 ;
        list:hasNext      inst:IfcPositiveInteger_List_1482 .

inst:IfcPositiveInteger_1483
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  84 .

inst:IfcPositiveInteger_List_1482
        list:hasContents  inst:IfcPositiveInteger_1483 .

inst:IfcPositiveInteger_List_1484
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1485
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1486
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1484
        list:hasContents  inst:IfcPositiveInteger_1479 ;
        list:hasNext      inst:IfcPositiveInteger_List_1485 .

inst:IfcPositiveInteger_1487
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  83 .

inst:IfcPositiveInteger_List_1485
        list:hasContents  inst:IfcPositiveInteger_1487 ;
        list:hasNext      inst:IfcPositiveInteger_List_1486 .

inst:IfcPositiveInteger_1488
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  81 .

inst:IfcPositiveInteger_List_1486
        list:hasContents  inst:IfcPositiveInteger_1488 .

inst:IfcPositiveInteger_List_1489
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1490
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1491
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1489
        list:hasContents  inst:IfcPositiveInteger_1462 ;
        list:hasNext      inst:IfcPositiveInteger_List_1490 .

inst:IfcPositiveInteger_List_1490
        list:hasContents  inst:IfcPositiveInteger_1488 ;
        list:hasNext      inst:IfcPositiveInteger_List_1491 .

inst:IfcPositiveInteger_1492
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  79 .

inst:IfcPositiveInteger_List_1491
        list:hasContents  inst:IfcPositiveInteger_1492 .

inst:IfcPositiveInteger_List_1493
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1494
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1495
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_1496
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  128 .

inst:IfcPositiveInteger_List_1493
        list:hasContents  inst:IfcPositiveInteger_1496 ;
        list:hasNext      inst:IfcPositiveInteger_List_1494 .

inst:IfcPositiveInteger_List_1494
        list:hasContents  inst:IfcPositiveInteger_1483 ;
        list:hasNext      inst:IfcPositiveInteger_List_1495 .

inst:IfcPositiveInteger_1497
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  129 .

inst:IfcPositiveInteger_List_1495
        list:hasContents  inst:IfcPositiveInteger_1497 .

inst:IfcPositiveInteger_List_1498
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1499
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1500
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_1501
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  88 .

inst:IfcPositiveInteger_List_1498
        list:hasContents  inst:IfcPositiveInteger_1501 ;
        list:hasNext      inst:IfcPositiveInteger_List_1499 .

inst:IfcPositiveInteger_1502
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  130 .

inst:IfcPositiveInteger_List_1499
        list:hasContents  inst:IfcPositiveInteger_1502 ;
        list:hasNext      inst:IfcPositiveInteger_List_1500 .

inst:IfcPositiveInteger_1503
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  86 .

inst:IfcPositiveInteger_List_1500
        list:hasContents  inst:IfcPositiveInteger_1503 .

inst:IfcPositiveInteger_List_1504
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1505
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1506
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_1507
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  92 .

inst:IfcPositiveInteger_List_1504
        list:hasContents  inst:IfcPositiveInteger_1507 ;
        list:hasNext      inst:IfcPositiveInteger_List_1505 .

inst:IfcPositiveInteger_1508
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  131 .

inst:IfcPositiveInteger_List_1505
        list:hasContents  inst:IfcPositiveInteger_1508 ;
        list:hasNext      inst:IfcPositiveInteger_List_1506 .

inst:IfcPositiveInteger_1509
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  90 .

inst:IfcPositiveInteger_List_1506
        list:hasContents  inst:IfcPositiveInteger_1509 .

inst:IfcPositiveInteger_List_1510
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1511
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1512
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1510
        list:hasContents  inst:IfcPositiveInteger_1509 ;
        list:hasNext      inst:IfcPositiveInteger_List_1511 .

inst:IfcPositiveInteger_1513
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  132 .

inst:IfcPositiveInteger_List_1511
        list:hasContents  inst:IfcPositiveInteger_1513 ;
        list:hasNext      inst:IfcPositiveInteger_List_1512 .

inst:IfcPositiveInteger_List_1512
        list:hasContents  inst:IfcPositiveInteger_1501 .

inst:IfcPositiveInteger_List_1514
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1515
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1516
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_1517
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  94 .

inst:IfcPositiveInteger_List_1514
        list:hasContents  inst:IfcPositiveInteger_1517 ;
        list:hasNext      inst:IfcPositiveInteger_List_1515 .

inst:IfcPositiveInteger_1518
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  133 .

inst:IfcPositiveInteger_List_1515
        list:hasContents  inst:IfcPositiveInteger_1518 ;
        list:hasNext      inst:IfcPositiveInteger_List_1516 .

inst:IfcPositiveInteger_List_1516
        list:hasContents  inst:IfcPositiveInteger_1507 .

inst:IfcPositiveInteger_List_1519
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1520
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1521
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_1522
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  96 .

inst:IfcPositiveInteger_List_1519
        list:hasContents  inst:IfcPositiveInteger_1522 ;
        list:hasNext      inst:IfcPositiveInteger_List_1520 .

inst:IfcPositiveInteger_1523
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  134 .

inst:IfcPositiveInteger_List_1520
        list:hasContents  inst:IfcPositiveInteger_1523 ;
        list:hasNext      inst:IfcPositiveInteger_List_1521 .

inst:IfcPositiveInteger_List_1521
        list:hasContents  inst:IfcPositiveInteger_1517 .

inst:IfcPositiveInteger_List_1524
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1525
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1526
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_1527
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  98 .

inst:IfcPositiveInteger_List_1524
        list:hasContents  inst:IfcPositiveInteger_1527 ;
        list:hasNext      inst:IfcPositiveInteger_List_1525 .

inst:IfcPositiveInteger_1528
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  135 .

inst:IfcPositiveInteger_List_1525
        list:hasContents  inst:IfcPositiveInteger_1528 ;
        list:hasNext      inst:IfcPositiveInteger_List_1526 .

inst:IfcPositiveInteger_List_1526
        list:hasContents  inst:IfcPositiveInteger_1522 .

inst:IfcPositiveInteger_List_1529
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1530
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1531
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1529
        list:hasContents  inst:IfcPositiveInteger_1496 ;
        list:hasNext      inst:IfcPositiveInteger_List_1530 .

inst:IfcPositiveInteger_1532
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  85 .

inst:IfcPositiveInteger_List_1530
        list:hasContents  inst:IfcPositiveInteger_1532 ;
        list:hasNext      inst:IfcPositiveInteger_List_1531 .

inst:IfcPositiveInteger_List_1531
        list:hasContents  inst:IfcPositiveInteger_1487 .

inst:IfcPositiveInteger_List_1533
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1534
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1535
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1533
        list:hasContents  inst:IfcPositiveInteger_1471 ;
        list:hasNext      inst:IfcPositiveInteger_List_1534 .

inst:IfcPositiveInteger_1536
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  75 .

inst:IfcPositiveInteger_List_1534
        list:hasContents  inst:IfcPositiveInteger_1536 ;
        list:hasNext      inst:IfcPositiveInteger_List_1535 .

inst:IfcPositiveInteger_List_1535
        list:hasContents  inst:IfcPositiveInteger_1455 .

inst:IfcPositiveInteger_List_1537
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1538
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1539
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1537
        list:hasContents  inst:IfcPositiveInteger_1471 ;
        list:hasNext      inst:IfcPositiveInteger_List_1538 .

inst:IfcPositiveInteger_List_1538
        list:hasContents  inst:IfcPositiveInteger_1463 ;
        list:hasNext      inst:IfcPositiveInteger_List_1539 .

inst:IfcPositiveInteger_List_1539
        list:hasContents  inst:IfcPositiveInteger_1492 .

inst:IfcPositiveInteger_List_1540
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1541
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1542
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1540
        list:hasContents  inst:IfcPositiveInteger_1532 ;
        list:hasNext      inst:IfcPositiveInteger_List_1541 .

inst:IfcPositiveInteger_List_1541
        list:hasContents  inst:IfcPositiveInteger_1496 ;
        list:hasNext      inst:IfcPositiveInteger_List_1542 .

inst:IfcPositiveInteger_1543
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  87 .

inst:IfcPositiveInteger_List_1542
        list:hasContents  inst:IfcPositiveInteger_1543 .

inst:IfcPositiveInteger_List_1544
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1545
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1546
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1544
        list:hasContents  inst:IfcPositiveInteger_1543 ;
        list:hasNext      inst:IfcPositiveInteger_List_1545 .

inst:IfcPositiveInteger_List_1545
        list:hasContents  inst:IfcPositiveInteger_1497 ;
        list:hasNext      inst:IfcPositiveInteger_List_1546 .

inst:IfcPositiveInteger_1547
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  89 .

inst:IfcPositiveInteger_List_1546
        list:hasContents  inst:IfcPositiveInteger_1547 .

inst:IfcPositiveInteger_List_1548
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1549
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1550
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1548
        list:hasContents  inst:IfcPositiveInteger_1508 ;
        list:hasNext      inst:IfcPositiveInteger_List_1549 .

inst:IfcPositiveInteger_1551
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  93 .

inst:IfcPositiveInteger_List_1549
        list:hasContents  inst:IfcPositiveInteger_1551 ;
        list:hasNext      inst:IfcPositiveInteger_List_1550 .

inst:IfcPositiveInteger_List_1550
        list:hasContents  inst:IfcPositiveInteger_1513 .

inst:IfcPositiveInteger_List_1552
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1553
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1554
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1552
        list:hasContents  inst:IfcPositiveInteger_1523 ;
        list:hasNext      inst:IfcPositiveInteger_List_1553 .

inst:IfcPositiveInteger_1555
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  97 .

inst:IfcPositiveInteger_List_1553
        list:hasContents  inst:IfcPositiveInteger_1555 ;
        list:hasNext      inst:IfcPositiveInteger_List_1554 .

inst:IfcPositiveInteger_List_1554
        list:hasContents  inst:IfcPositiveInteger_1518 .

inst:IfcPositiveInteger_List_1556
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1557
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1558
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1556
        list:hasContents  inst:IfcPositiveInteger_1555 ;
        list:hasNext      inst:IfcPositiveInteger_List_1557 .

inst:IfcPositiveInteger_List_1557
        list:hasContents  inst:IfcPositiveInteger_1523 ;
        list:hasNext      inst:IfcPositiveInteger_List_1558 .

inst:IfcPositiveInteger_1559
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  99 .

inst:IfcPositiveInteger_List_1558
        list:hasContents  inst:IfcPositiveInteger_1559 .

inst:IfcPositiveInteger_List_1560
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1561
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1562
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1560
        list:hasContents  inst:IfcPositiveInteger_1518 ;
        list:hasNext      inst:IfcPositiveInteger_List_1561 .

inst:IfcPositiveInteger_1563
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  95 .

inst:IfcPositiveInteger_List_1561
        list:hasContents  inst:IfcPositiveInteger_1563 ;
        list:hasNext      inst:IfcPositiveInteger_List_1562 .

inst:IfcPositiveInteger_List_1562
        list:hasContents  inst:IfcPositiveInteger_1508 .

inst:IfcPositiveInteger_List_1564
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1565
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1566
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1564
        list:hasContents  inst:IfcPositiveInteger_1513 ;
        list:hasNext      inst:IfcPositiveInteger_List_1565 .

inst:IfcPositiveInteger_1567
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  91 .

inst:IfcPositiveInteger_List_1565
        list:hasContents  inst:IfcPositiveInteger_1567 ;
        list:hasNext      inst:IfcPositiveInteger_List_1566 .

inst:IfcPositiveInteger_List_1566
        list:hasContents  inst:IfcPositiveInteger_1502 .

inst:IfcPositiveInteger_List_1568
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1569
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1570
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1568
        list:hasContents  inst:IfcPositiveInteger_1528 ;
        list:hasNext      inst:IfcPositiveInteger_List_1569 .

inst:IfcPositiveInteger_List_1569
        list:hasContents  inst:IfcPositiveInteger_1527 ;
        list:hasNext      inst:IfcPositiveInteger_List_1570 .

inst:IfcPositiveInteger_1571
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  136 .

inst:IfcPositiveInteger_List_1570
        list:hasContents  inst:IfcPositiveInteger_1571 .

inst:IfcPositiveInteger_List_1572
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1573
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1574
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_1575
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  102 .

inst:IfcPositiveInteger_List_1572
        list:hasContents  inst:IfcPositiveInteger_1575 ;
        list:hasNext      inst:IfcPositiveInteger_List_1573 .

inst:IfcPositiveInteger_1576
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  137 .

inst:IfcPositiveInteger_List_1573
        list:hasContents  inst:IfcPositiveInteger_1576 ;
        list:hasNext      inst:IfcPositiveInteger_List_1574 .

inst:IfcPositiveInteger_1577
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  100 .

inst:IfcPositiveInteger_List_1574
        list:hasContents  inst:IfcPositiveInteger_1577 .

inst:IfcPositiveInteger_List_1578
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1579
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1580
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_1581
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  106 .

inst:IfcPositiveInteger_List_1578
        list:hasContents  inst:IfcPositiveInteger_1581 ;
        list:hasNext      inst:IfcPositiveInteger_List_1579 .

inst:IfcPositiveInteger_1582
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  138 .

inst:IfcPositiveInteger_List_1579
        list:hasContents  inst:IfcPositiveInteger_1582 ;
        list:hasNext      inst:IfcPositiveInteger_List_1580 .

inst:IfcPositiveInteger_1583
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  104 .

inst:IfcPositiveInteger_List_1580
        list:hasContents  inst:IfcPositiveInteger_1583 .

inst:IfcPositiveInteger_List_1584
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1585
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1586
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1584
        list:hasContents  inst:IfcPositiveInteger_1583 ;
        list:hasNext      inst:IfcPositiveInteger_List_1585 .

inst:IfcPositiveInteger_1587
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  139 .

inst:IfcPositiveInteger_List_1585
        list:hasContents  inst:IfcPositiveInteger_1587 ;
        list:hasNext      inst:IfcPositiveInteger_List_1586 .

inst:IfcPositiveInteger_List_1586
        list:hasContents  inst:IfcPositiveInteger_1575 .

inst:IfcPositiveInteger_List_1588
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1589
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1590
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1588
        list:hasContents  inst:IfcPositiveInteger_1576 ;
        list:hasNext      inst:IfcPositiveInteger_List_1589 .

inst:IfcPositiveInteger_1591
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  103 .

inst:IfcPositiveInteger_List_1589
        list:hasContents  inst:IfcPositiveInteger_1591 ;
        list:hasNext      inst:IfcPositiveInteger_List_1590 .

inst:IfcPositiveInteger_List_1590
        list:hasContents  inst:IfcPositiveInteger_1571 .

inst:IfcPositiveInteger_List_1592
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1593
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1594
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_1595
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  108 .

inst:IfcPositiveInteger_List_1592
        list:hasContents  inst:IfcPositiveInteger_1595 ;
        list:hasNext      inst:IfcPositiveInteger_List_1593 .

inst:IfcPositiveInteger_1596
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  140 .

inst:IfcPositiveInteger_List_1593
        list:hasContents  inst:IfcPositiveInteger_1596 ;
        list:hasNext      inst:IfcPositiveInteger_List_1594 .

inst:IfcPositiveInteger_List_1594
        list:hasContents  inst:IfcPositiveInteger_1581 .

inst:IfcPositiveInteger_List_1597
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1598
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1599
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1597
        list:hasContents  inst:IfcPositiveInteger_1582 ;
        list:hasNext      inst:IfcPositiveInteger_List_1598 .

inst:IfcPositiveInteger_1600
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  107 .

inst:IfcPositiveInteger_List_1598
        list:hasContents  inst:IfcPositiveInteger_1600 ;
        list:hasNext      inst:IfcPositiveInteger_List_1599 .

inst:IfcPositiveInteger_List_1599
        list:hasContents  inst:IfcPositiveInteger_1587 .

inst:IfcPositiveInteger_List_1601
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1602
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1603
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1601
        list:hasContents  inst:IfcPositiveInteger_1587 ;
        list:hasNext      inst:IfcPositiveInteger_List_1602 .

inst:IfcPositiveInteger_1604
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  105 .

inst:IfcPositiveInteger_List_1602
        list:hasContents  inst:IfcPositiveInteger_1604 ;
        list:hasNext      inst:IfcPositiveInteger_List_1603 .

inst:IfcPositiveInteger_List_1603
        list:hasContents  inst:IfcPositiveInteger_1576 .

inst:IfcPositiveInteger_List_1605
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1606
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1607
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1605
        list:hasContents  inst:IfcPositiveInteger_1559 ;
        list:hasNext      inst:IfcPositiveInteger_List_1606 .

inst:IfcPositiveInteger_List_1606
        list:hasContents  inst:IfcPositiveInteger_1528 ;
        list:hasNext      inst:IfcPositiveInteger_List_1607 .

inst:IfcPositiveInteger_1608
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  101 .

inst:IfcPositiveInteger_List_1607
        list:hasContents  inst:IfcPositiveInteger_1608 .

inst:IfcPositiveInteger_List_1609
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1610
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1611
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_1612
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  141 .

inst:IfcPositiveInteger_List_1609
        list:hasContents  inst:IfcPositiveInteger_1612 ;
        list:hasNext      inst:IfcPositiveInteger_List_1610 .

inst:IfcPositiveInteger_1613
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  110 .

inst:IfcPositiveInteger_List_1610
        list:hasContents  inst:IfcPositiveInteger_1613 ;
        list:hasNext      inst:IfcPositiveInteger_List_1611 .

inst:IfcPositiveInteger_1614
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  112 .

inst:IfcPositiveInteger_List_1611
        list:hasContents  inst:IfcPositiveInteger_1614 .

inst:IfcPositiveInteger_List_1615
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1616
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1617
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_1618
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  114 .

inst:IfcPositiveInteger_List_1615
        list:hasContents  inst:IfcPositiveInteger_1618 ;
        list:hasNext      inst:IfcPositiveInteger_List_1616 .

inst:IfcPositiveInteger_1619
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  143 .

inst:IfcPositiveInteger_List_1616
        list:hasContents  inst:IfcPositiveInteger_1619 ;
        list:hasNext      inst:IfcPositiveInteger_List_1617 .

inst:IfcPositiveInteger_1620
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  142 .

inst:IfcPositiveInteger_List_1617
        list:hasContents  inst:IfcPositiveInteger_1620 .

inst:IfcPositiveInteger_List_1621
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1622
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1623
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1621
        list:hasContents  inst:IfcPositiveInteger_1612 ;
        list:hasNext      inst:IfcPositiveInteger_List_1622 .

inst:IfcPositiveInteger_1624
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  111 .

inst:IfcPositiveInteger_List_1622
        list:hasContents  inst:IfcPositiveInteger_1624 ;
        list:hasNext      inst:IfcPositiveInteger_List_1623 .

inst:IfcPositiveInteger_1625
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  109 .

inst:IfcPositiveInteger_List_1623
        list:hasContents  inst:IfcPositiveInteger_1625 .

inst:IfcPositiveInteger_List_1626
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1627
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1628
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1626
        list:hasContents  inst:IfcPositiveInteger_1613 ;
        list:hasNext      inst:IfcPositiveInteger_List_1627 .

inst:IfcPositiveInteger_List_1627
        list:hasContents  inst:IfcPositiveInteger_1612 ;
        list:hasNext      inst:IfcPositiveInteger_List_1628 .

inst:IfcPositiveInteger_List_1628
        list:hasContents  inst:IfcPositiveInteger_1596 .

inst:IfcPositiveInteger_List_1629
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1630
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1631
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_1632
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  118 .

inst:IfcPositiveInteger_List_1629
        list:hasContents  inst:IfcPositiveInteger_1632 ;
        list:hasNext      inst:IfcPositiveInteger_List_1630 .

inst:IfcPositiveInteger_1633
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  144 .

inst:IfcPositiveInteger_List_1630
        list:hasContents  inst:IfcPositiveInteger_1633 ;
        list:hasNext      inst:IfcPositiveInteger_List_1631 .

inst:IfcPositiveInteger_1634
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  145 .

inst:IfcPositiveInteger_List_1631
        list:hasContents  inst:IfcPositiveInteger_1634 .

inst:IfcPositiveInteger_List_1635
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1636
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1637
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_1638
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  120 .

inst:IfcPositiveInteger_List_1635
        list:hasContents  inst:IfcPositiveInteger_1638 ;
        list:hasNext      inst:IfcPositiveInteger_List_1636 .

inst:IfcPositiveInteger_1639
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  146 .

inst:IfcPositiveInteger_List_1636
        list:hasContents  inst:IfcPositiveInteger_1639 ;
        list:hasNext      inst:IfcPositiveInteger_List_1637 .

inst:IfcPositiveInteger_List_1637
        list:hasContents  inst:IfcPositiveInteger_1633 .

inst:IfcPositiveInteger_List_1640
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1641
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1642
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_1643
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  116 .

inst:IfcPositiveInteger_List_1640
        list:hasContents  inst:IfcPositiveInteger_1643 ;
        list:hasNext      inst:IfcPositiveInteger_List_1641 .

inst:IfcPositiveInteger_List_1641
        list:hasContents  inst:IfcPositiveInteger_1634 ;
        list:hasNext      inst:IfcPositiveInteger_List_1642 .

inst:IfcPositiveInteger_List_1642
        list:hasContents  inst:IfcPositiveInteger_1619 .

inst:IfcPositiveInteger_List_1644
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1645
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1646
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_1647
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  122 .

inst:IfcPositiveInteger_List_1644
        list:hasContents  inst:IfcPositiveInteger_1647 ;
        list:hasNext      inst:IfcPositiveInteger_List_1645 .

inst:IfcPositiveInteger_1648
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  123 .

inst:IfcPositiveInteger_List_1645
        list:hasContents  inst:IfcPositiveInteger_1648 ;
        list:hasNext      inst:IfcPositiveInteger_List_1646 .

inst:IfcPositiveInteger_List_1646
        list:hasContents  inst:IfcPositiveInteger_1639 .

inst:IfcPositiveInteger_List_1649
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1650
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1651
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1649
        list:hasContents  inst:IfcPositiveInteger_1596 ;
        list:hasNext      inst:IfcPositiveInteger_List_1650 .

inst:IfcPositiveInteger_List_1650
        list:hasContents  inst:IfcPositiveInteger_1625 ;
        list:hasNext      inst:IfcPositiveInteger_List_1651 .

inst:IfcPositiveInteger_List_1651
        list:hasContents  inst:IfcPositiveInteger_1582 .

inst:IfcPositiveInteger_List_1652
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1653
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1654
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1652
        list:hasContents  inst:IfcPositiveInteger_1624 ;
        list:hasNext      inst:IfcPositiveInteger_List_1653 .

inst:IfcPositiveInteger_List_1653
        list:hasContents  inst:IfcPositiveInteger_1612 ;
        list:hasNext      inst:IfcPositiveInteger_List_1654 .

inst:IfcPositiveInteger_List_1654
        list:hasContents  inst:IfcPositiveInteger_1620 .

inst:IfcPositiveInteger_List_1655
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1656
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1657
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_1658
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  113 .

inst:IfcPositiveInteger_List_1655
        list:hasContents  inst:IfcPositiveInteger_1658 ;
        list:hasNext      inst:IfcPositiveInteger_List_1656 .

inst:IfcPositiveInteger_List_1656
        list:hasContents  inst:IfcPositiveInteger_1620 ;
        list:hasNext      inst:IfcPositiveInteger_List_1657 .

inst:IfcPositiveInteger_List_1657
        list:hasContents  inst:IfcPositiveInteger_1619 .

inst:IfcPositiveInteger_List_1659
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1660
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1661
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1659
        list:hasContents  inst:IfcPositiveInteger_1634 ;
        list:hasNext      inst:IfcPositiveInteger_List_1660 .

inst:IfcPositiveInteger_1662
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  117 .

inst:IfcPositiveInteger_List_1660
        list:hasContents  inst:IfcPositiveInteger_1662 ;
        list:hasNext      inst:IfcPositiveInteger_List_1661 .

inst:IfcPositiveInteger_1663
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  115 .

inst:IfcPositiveInteger_List_1661
        list:hasContents  inst:IfcPositiveInteger_1663 .

inst:IfcPositiveInteger_List_1664
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1665
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1666
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1664
        list:hasContents  inst:IfcPositiveInteger_1639 ;
        list:hasNext      inst:IfcPositiveInteger_List_1665 .

inst:IfcPositiveInteger_1667
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  121 .

inst:IfcPositiveInteger_List_1665
        list:hasContents  inst:IfcPositiveInteger_1667 ;
        list:hasNext      inst:IfcPositiveInteger_List_1666 .

inst:IfcPositiveInteger_1668
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  119 .

inst:IfcPositiveInteger_List_1666
        list:hasContents  inst:IfcPositiveInteger_1668 .

inst:IfcPositiveInteger_List_1669
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1670
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1671
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1669
        list:hasContents  inst:IfcPositiveInteger_1648 ;
        list:hasNext      inst:IfcPositiveInteger_List_1670 .

inst:IfcPositiveInteger_1672
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  124 .

inst:IfcPositiveInteger_List_1670
        list:hasContents  inst:IfcPositiveInteger_1672 ;
        list:hasNext      inst:IfcPositiveInteger_List_1671 .

inst:IfcPositiveInteger_List_1671
        list:hasContents  inst:IfcPositiveInteger_1667 .

inst:IfcPositiveInteger_List_1673
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1674
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1675
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1673
        list:hasContents  inst:IfcPositiveInteger_1633 ;
        list:hasNext      inst:IfcPositiveInteger_List_1674 .

inst:IfcPositiveInteger_List_1674
        list:hasContents  inst:IfcPositiveInteger_1668 ;
        list:hasNext      inst:IfcPositiveInteger_List_1675 .

inst:IfcPositiveInteger_List_1675
        list:hasContents  inst:IfcPositiveInteger_1662 .

inst:IfcPositiveInteger_List_1676
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1677
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1678
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_1679
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  148 .

inst:IfcPositiveInteger_List_1676
        list:hasContents  inst:IfcPositiveInteger_1679 ;
        list:hasNext      inst:IfcPositiveInteger_List_1677 .

inst:IfcPositiveInteger_1680
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  173 .

inst:IfcPositiveInteger_List_1677
        list:hasContents  inst:IfcPositiveInteger_1680 ;
        list:hasNext      inst:IfcPositiveInteger_List_1678 .

inst:IfcPositiveInteger_1681
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  172 .

inst:IfcPositiveInteger_List_1678
        list:hasContents  inst:IfcPositiveInteger_1681 .

inst:IfcPositiveInteger_List_1682
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1683
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1684
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_1685
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  149 .

inst:IfcPositiveInteger_List_1682
        list:hasContents  inst:IfcPositiveInteger_1685 ;
        list:hasNext      inst:IfcPositiveInteger_List_1683 .

inst:IfcPositiveInteger_1686
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  174 .

inst:IfcPositiveInteger_List_1683
        list:hasContents  inst:IfcPositiveInteger_1686 ;
        list:hasNext      inst:IfcPositiveInteger_List_1684 .

inst:IfcPositiveInteger_List_1684
        list:hasContents  inst:IfcPositiveInteger_1680 .

inst:IfcPositiveInteger_List_1687
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1688
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1689
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_1690
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  151 .

inst:IfcPositiveInteger_List_1687
        list:hasContents  inst:IfcPositiveInteger_1690 ;
        list:hasNext      inst:IfcPositiveInteger_List_1688 .

inst:IfcPositiveInteger_1691
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  176 .

inst:IfcPositiveInteger_List_1688
        list:hasContents  inst:IfcPositiveInteger_1691 ;
        list:hasNext      inst:IfcPositiveInteger_List_1689 .

inst:IfcPositiveInteger_1692
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  175 .

inst:IfcPositiveInteger_List_1689
        list:hasContents  inst:IfcPositiveInteger_1692 .

inst:IfcPositiveInteger_List_1693
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1694
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1695
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_1696
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  152 .

inst:IfcPositiveInteger_List_1693
        list:hasContents  inst:IfcPositiveInteger_1696 ;
        list:hasNext      inst:IfcPositiveInteger_List_1694 .

inst:IfcPositiveInteger_1697
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  177 .

inst:IfcPositiveInteger_List_1694
        list:hasContents  inst:IfcPositiveInteger_1697 ;
        list:hasNext      inst:IfcPositiveInteger_List_1695 .

inst:IfcPositiveInteger_List_1695
        list:hasContents  inst:IfcPositiveInteger_1691 .

inst:IfcPositiveInteger_List_1698
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1699
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1700
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_1701
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  150 .

inst:IfcPositiveInteger_List_1698
        list:hasContents  inst:IfcPositiveInteger_1701 ;
        list:hasNext      inst:IfcPositiveInteger_List_1699 .

inst:IfcPositiveInteger_List_1699
        list:hasContents  inst:IfcPositiveInteger_1692 ;
        list:hasNext      inst:IfcPositiveInteger_List_1700 .

inst:IfcPositiveInteger_List_1700
        list:hasContents  inst:IfcPositiveInteger_1686 .

inst:IfcPositiveInteger_List_1702
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1703
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1704
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_1705
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  154 .

inst:IfcPositiveInteger_List_1702
        list:hasContents  inst:IfcPositiveInteger_1705 ;
        list:hasNext      inst:IfcPositiveInteger_List_1703 .

inst:IfcPositiveInteger_1706
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  179 .

inst:IfcPositiveInteger_List_1703
        list:hasContents  inst:IfcPositiveInteger_1706 ;
        list:hasNext      inst:IfcPositiveInteger_List_1704 .

inst:IfcPositiveInteger_1707
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  178 .

inst:IfcPositiveInteger_List_1704
        list:hasContents  inst:IfcPositiveInteger_1707 .

inst:IfcPositiveInteger_List_1708
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1709
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1710
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_1711
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  155 .

inst:IfcPositiveInteger_List_1708
        list:hasContents  inst:IfcPositiveInteger_1711 ;
        list:hasNext      inst:IfcPositiveInteger_List_1709 .

inst:IfcPositiveInteger_1712
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  180 .

inst:IfcPositiveInteger_List_1709
        list:hasContents  inst:IfcPositiveInteger_1712 ;
        list:hasNext      inst:IfcPositiveInteger_List_1710 .

inst:IfcPositiveInteger_List_1710
        list:hasContents  inst:IfcPositiveInteger_1706 .

inst:IfcPositiveInteger_List_1713
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1714
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1715
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_1716
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  157 .

inst:IfcPositiveInteger_List_1713
        list:hasContents  inst:IfcPositiveInteger_1716 ;
        list:hasNext      inst:IfcPositiveInteger_List_1714 .

inst:IfcPositiveInteger_1717
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  182 .

inst:IfcPositiveInteger_List_1714
        list:hasContents  inst:IfcPositiveInteger_1717 ;
        list:hasNext      inst:IfcPositiveInteger_List_1715 .

inst:IfcPositiveInteger_1718
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  181 .

inst:IfcPositiveInteger_List_1715
        list:hasContents  inst:IfcPositiveInteger_1718 .

inst:IfcPositiveInteger_List_1719
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1720
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1721
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_1722
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  158 .

inst:IfcPositiveInteger_List_1719
        list:hasContents  inst:IfcPositiveInteger_1722 ;
        list:hasNext      inst:IfcPositiveInteger_List_1720 .

inst:IfcPositiveInteger_1723
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  183 .

inst:IfcPositiveInteger_List_1720
        list:hasContents  inst:IfcPositiveInteger_1723 ;
        list:hasNext      inst:IfcPositiveInteger_List_1721 .

inst:IfcPositiveInteger_List_1721
        list:hasContents  inst:IfcPositiveInteger_1717 .

inst:IfcPositiveInteger_List_1724
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1725
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1726
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_1727
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  156 .

inst:IfcPositiveInteger_List_1724
        list:hasContents  inst:IfcPositiveInteger_1727 ;
        list:hasNext      inst:IfcPositiveInteger_List_1725 .

inst:IfcPositiveInteger_List_1725
        list:hasContents  inst:IfcPositiveInteger_1718 ;
        list:hasNext      inst:IfcPositiveInteger_List_1726 .

inst:IfcPositiveInteger_List_1726
        list:hasContents  inst:IfcPositiveInteger_1712 .

inst:IfcPositiveInteger_List_1728
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1729
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1730
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_1731
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  153 .

inst:IfcPositiveInteger_List_1728
        list:hasContents  inst:IfcPositiveInteger_1731 ;
        list:hasNext      inst:IfcPositiveInteger_List_1729 .

inst:IfcPositiveInteger_List_1729
        list:hasContents  inst:IfcPositiveInteger_1707 ;
        list:hasNext      inst:IfcPositiveInteger_List_1730 .

inst:IfcPositiveInteger_List_1730
        list:hasContents  inst:IfcPositiveInteger_1697 .

inst:IfcPositiveInteger_List_1732
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1733
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1734
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_1735
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  160 .

inst:IfcPositiveInteger_List_1732
        list:hasContents  inst:IfcPositiveInteger_1735 ;
        list:hasNext      inst:IfcPositiveInteger_List_1733 .

inst:IfcPositiveInteger_1736
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  185 .

inst:IfcPositiveInteger_List_1733
        list:hasContents  inst:IfcPositiveInteger_1736 ;
        list:hasNext      inst:IfcPositiveInteger_List_1734 .

inst:IfcPositiveInteger_1737
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  159 .

inst:IfcPositiveInteger_List_1734
        list:hasContents  inst:IfcPositiveInteger_1737 .

inst:IfcPositiveInteger_List_1738
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1739
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1740
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_1741
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  161 .

inst:IfcPositiveInteger_List_1738
        list:hasContents  inst:IfcPositiveInteger_1741 ;
        list:hasNext      inst:IfcPositiveInteger_List_1739 .

inst:IfcPositiveInteger_1742
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  186 .

inst:IfcPositiveInteger_List_1739
        list:hasContents  inst:IfcPositiveInteger_1742 ;
        list:hasNext      inst:IfcPositiveInteger_List_1740 .

inst:IfcPositiveInteger_List_1740
        list:hasContents  inst:IfcPositiveInteger_1735 .

inst:IfcPositiveInteger_List_1743
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1744
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1745
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_1746
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  163 .

inst:IfcPositiveInteger_List_1743
        list:hasContents  inst:IfcPositiveInteger_1746 ;
        list:hasNext      inst:IfcPositiveInteger_List_1744 .

inst:IfcPositiveInteger_1747
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  188 .

inst:IfcPositiveInteger_List_1744
        list:hasContents  inst:IfcPositiveInteger_1747 ;
        list:hasNext      inst:IfcPositiveInteger_List_1745 .

inst:IfcPositiveInteger_1748
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  162 .

inst:IfcPositiveInteger_List_1745
        list:hasContents  inst:IfcPositiveInteger_1748 .

inst:IfcPositiveInteger_List_1749
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1750
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1751
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_1752
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  164 .

inst:IfcPositiveInteger_List_1749
        list:hasContents  inst:IfcPositiveInteger_1752 ;
        list:hasNext      inst:IfcPositiveInteger_List_1750 .

inst:IfcPositiveInteger_1753
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  189 .

inst:IfcPositiveInteger_List_1750
        list:hasContents  inst:IfcPositiveInteger_1753 ;
        list:hasNext      inst:IfcPositiveInteger_List_1751 .

inst:IfcPositiveInteger_List_1751
        list:hasContents  inst:IfcPositiveInteger_1746 .

inst:IfcPositiveInteger_List_1754
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1755
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1756
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1754
        list:hasContents  inst:IfcPositiveInteger_1748 ;
        list:hasNext      inst:IfcPositiveInteger_List_1755 .

inst:IfcPositiveInteger_1757
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  187 .

inst:IfcPositiveInteger_List_1755
        list:hasContents  inst:IfcPositiveInteger_1757 ;
        list:hasNext      inst:IfcPositiveInteger_List_1756 .

inst:IfcPositiveInteger_List_1756
        list:hasContents  inst:IfcPositiveInteger_1741 .

inst:IfcPositiveInteger_List_1758
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1759
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1760
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_1761
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  166 .

inst:IfcPositiveInteger_List_1758
        list:hasContents  inst:IfcPositiveInteger_1761 ;
        list:hasNext      inst:IfcPositiveInteger_List_1759 .

inst:IfcPositiveInteger_1762
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  191 .

inst:IfcPositiveInteger_List_1759
        list:hasContents  inst:IfcPositiveInteger_1762 ;
        list:hasNext      inst:IfcPositiveInteger_List_1760 .

inst:IfcPositiveInteger_1763
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  165 .

inst:IfcPositiveInteger_List_1760
        list:hasContents  inst:IfcPositiveInteger_1763 .

inst:IfcPositiveInteger_List_1764
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1765
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1766
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_1767
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  167 .

inst:IfcPositiveInteger_List_1764
        list:hasContents  inst:IfcPositiveInteger_1767 ;
        list:hasNext      inst:IfcPositiveInteger_List_1765 .

inst:IfcPositiveInteger_1768
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  192 .

inst:IfcPositiveInteger_List_1765
        list:hasContents  inst:IfcPositiveInteger_1768 ;
        list:hasNext      inst:IfcPositiveInteger_List_1766 .

inst:IfcPositiveInteger_List_1766
        list:hasContents  inst:IfcPositiveInteger_1761 .

inst:IfcPositiveInteger_List_1769
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1770
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1771
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_1772
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  169 .

inst:IfcPositiveInteger_List_1769
        list:hasContents  inst:IfcPositiveInteger_1772 ;
        list:hasNext      inst:IfcPositiveInteger_List_1770 .

inst:IfcPositiveInteger_1773
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  194 .

inst:IfcPositiveInteger_List_1770
        list:hasContents  inst:IfcPositiveInteger_1773 ;
        list:hasNext      inst:IfcPositiveInteger_List_1771 .

inst:IfcPositiveInteger_1774
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  168 .

inst:IfcPositiveInteger_List_1771
        list:hasContents  inst:IfcPositiveInteger_1774 .

inst:IfcPositiveInteger_List_1775
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1776
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1777
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_1778
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  171 .

inst:IfcPositiveInteger_List_1775
        list:hasContents  inst:IfcPositiveInteger_1778 ;
        list:hasNext      inst:IfcPositiveInteger_List_1776 .

inst:IfcPositiveInteger_1779
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  196 .

inst:IfcPositiveInteger_List_1776
        list:hasContents  inst:IfcPositiveInteger_1779 ;
        list:hasNext      inst:IfcPositiveInteger_List_1777 .

inst:IfcPositiveInteger_1780
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  170 .

inst:IfcPositiveInteger_List_1777
        list:hasContents  inst:IfcPositiveInteger_1780 .

inst:IfcPositiveInteger_List_1781
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1782
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1783
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1781
        list:hasContents  inst:IfcPositiveInteger_1780 ;
        list:hasNext      inst:IfcPositiveInteger_List_1782 .

inst:IfcPositiveInteger_1784
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  195 .

inst:IfcPositiveInteger_List_1782
        list:hasContents  inst:IfcPositiveInteger_1784 ;
        list:hasNext      inst:IfcPositiveInteger_List_1783 .

inst:IfcPositiveInteger_List_1783
        list:hasContents  inst:IfcPositiveInteger_1772 .

inst:IfcPositiveInteger_List_1785
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1786
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1787
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1785
        list:hasContents  inst:IfcPositiveInteger_1774 ;
        list:hasNext      inst:IfcPositiveInteger_List_1786 .

inst:IfcPositiveInteger_1788
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  193 .

inst:IfcPositiveInteger_List_1786
        list:hasContents  inst:IfcPositiveInteger_1788 ;
        list:hasNext      inst:IfcPositiveInteger_List_1787 .

inst:IfcPositiveInteger_List_1787
        list:hasContents  inst:IfcPositiveInteger_1767 .

inst:IfcPositiveInteger_List_1789
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1790
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1791
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1789
        list:hasContents  inst:IfcPositiveInteger_1763 ;
        list:hasNext      inst:IfcPositiveInteger_List_1790 .

inst:IfcPositiveInteger_1792
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  190 .

inst:IfcPositiveInteger_List_1790
        list:hasContents  inst:IfcPositiveInteger_1792 ;
        list:hasNext      inst:IfcPositiveInteger_List_1791 .

inst:IfcPositiveInteger_List_1791
        list:hasContents  inst:IfcPositiveInteger_1752 .

inst:IfcPositiveInteger_List_1793
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1794
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1795
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1793
        list:hasContents  inst:IfcPositiveInteger_1737 ;
        list:hasNext      inst:IfcPositiveInteger_List_1794 .

inst:IfcPositiveInteger_1796
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  184 .

inst:IfcPositiveInteger_List_1794
        list:hasContents  inst:IfcPositiveInteger_1796 ;
        list:hasNext      inst:IfcPositiveInteger_List_1795 .

inst:IfcPositiveInteger_List_1795
        list:hasContents  inst:IfcPositiveInteger_1723 .

inst:IfcPositiveInteger_List_1797
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1798
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1799
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_1800
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  217 .

inst:IfcPositiveInteger_List_1797
        list:hasContents  inst:IfcPositiveInteger_1800 ;
        list:hasNext      inst:IfcPositiveInteger_List_1798 .

inst:IfcPositiveInteger_1801
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  216 .

inst:IfcPositiveInteger_List_1798
        list:hasContents  inst:IfcPositiveInteger_1801 ;
        list:hasNext      inst:IfcPositiveInteger_List_1799 .

inst:IfcPositiveInteger_1802
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  215 .

inst:IfcPositiveInteger_List_1799
        list:hasContents  inst:IfcPositiveInteger_1802 .

inst:IfcPositiveInteger_List_1803
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1804
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1805
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1803
        list:hasContents  inst:IfcPositiveInteger_1800 ;
        list:hasNext      inst:IfcPositiveInteger_List_1804 .

inst:IfcPositiveInteger_List_1804
        list:hasContents  inst:IfcPositiveInteger_1802 ;
        list:hasNext      inst:IfcPositiveInteger_List_1805 .

inst:IfcPositiveInteger_1806
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  218 .

inst:IfcPositiveInteger_List_1805
        list:hasContents  inst:IfcPositiveInteger_1806 .

inst:IfcPositiveInteger_List_1807
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1808
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1809
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_1810
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  220 .

inst:IfcPositiveInteger_List_1807
        list:hasContents  inst:IfcPositiveInteger_1810 ;
        list:hasNext      inst:IfcPositiveInteger_List_1808 .

inst:IfcPositiveInteger_1811
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  219 .

inst:IfcPositiveInteger_List_1808
        list:hasContents  inst:IfcPositiveInteger_1811 ;
        list:hasNext      inst:IfcPositiveInteger_List_1809 .

inst:IfcPositiveInteger_1812
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  214 .

inst:IfcPositiveInteger_List_1809
        list:hasContents  inst:IfcPositiveInteger_1812 .

inst:IfcPositiveInteger_List_1813
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1814
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1815
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1813
        list:hasContents  inst:IfcPositiveInteger_1802 ;
        list:hasNext      inst:IfcPositiveInteger_List_1814 .

inst:IfcPositiveInteger_List_1814
        list:hasContents  inst:IfcPositiveInteger_1811 ;
        list:hasNext      inst:IfcPositiveInteger_List_1815 .

inst:IfcPositiveInteger_List_1815
        list:hasContents  inst:IfcPositiveInteger_1806 .

inst:IfcPositiveInteger_List_1816
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1817
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1818
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_1819
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  197 .

inst:IfcPositiveInteger_List_1816
        list:hasContents  inst:IfcPositiveInteger_1819 ;
        list:hasNext      inst:IfcPositiveInteger_List_1817 .

inst:IfcPositiveInteger_List_1817
        list:hasContents  inst:IfcPositiveInteger_1810 ;
        list:hasNext      inst:IfcPositiveInteger_List_1818 .

inst:IfcPositiveInteger_List_1818
        list:hasContents  inst:IfcPositiveInteger_1812 .

inst:IfcPositiveInteger_List_1820
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1821
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1822
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1820
        list:hasContents  inst:IfcPositiveInteger_1812 ;
        list:hasNext      inst:IfcPositiveInteger_List_1821 .

inst:IfcPositiveInteger_1823
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  213 .

inst:IfcPositiveInteger_List_1821
        list:hasContents  inst:IfcPositiveInteger_1823 ;
        list:hasNext      inst:IfcPositiveInteger_List_1822 .

inst:IfcPositiveInteger_List_1822
        list:hasContents  inst:IfcPositiveInteger_1819 .

inst:IfcPositiveInteger_List_1824
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1825
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1826
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1824
        list:hasContents  inst:IfcPositiveInteger_1811 ;
        list:hasNext      inst:IfcPositiveInteger_List_1825 .

inst:IfcPositiveInteger_List_1825
        list:hasContents  inst:IfcPositiveInteger_1802 ;
        list:hasNext      inst:IfcPositiveInteger_List_1826 .

inst:IfcPositiveInteger_List_1826
        list:hasContents  inst:IfcPositiveInteger_1812 .

inst:IfcPositiveInteger_List_1827
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1828
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1829
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_1830
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  210 .

inst:IfcPositiveInteger_List_1827
        list:hasContents  inst:IfcPositiveInteger_1830 ;
        list:hasNext      inst:IfcPositiveInteger_List_1828 .

inst:IfcPositiveInteger_1831
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  208 .

inst:IfcPositiveInteger_List_1828
        list:hasContents  inst:IfcPositiveInteger_1831 ;
        list:hasNext      inst:IfcPositiveInteger_List_1829 .

inst:IfcPositiveInteger_1832
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  211 .

inst:IfcPositiveInteger_List_1829
        list:hasContents  inst:IfcPositiveInteger_1832 .

inst:IfcPositiveInteger_List_1833
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1834
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1835
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1833
        list:hasContents  inst:IfcPositiveInteger_1823 ;
        list:hasNext      inst:IfcPositiveInteger_List_1834 .

inst:IfcPositiveInteger_1836
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  212 .

inst:IfcPositiveInteger_List_1834
        list:hasContents  inst:IfcPositiveInteger_1836 ;
        list:hasNext      inst:IfcPositiveInteger_List_1835 .

inst:IfcPositiveInteger_1837
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  205 .

inst:IfcPositiveInteger_List_1835
        list:hasContents  inst:IfcPositiveInteger_1837 .

inst:IfcPositiveInteger_List_1838
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1839
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1840
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1838
        list:hasContents  inst:IfcPositiveInteger_1836 ;
        list:hasNext      inst:IfcPositiveInteger_List_1839 .

inst:IfcPositiveInteger_List_1839
        list:hasContents  inst:IfcPositiveInteger_1832 ;
        list:hasNext      inst:IfcPositiveInteger_List_1840 .

inst:IfcPositiveInteger_1841
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  207 .

inst:IfcPositiveInteger_List_1840
        list:hasContents  inst:IfcPositiveInteger_1841 .

inst:IfcPositiveInteger_List_1842
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1843
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1844
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1842
        list:hasContents  inst:IfcPositiveInteger_1819 ;
        list:hasNext      inst:IfcPositiveInteger_List_1843 .

inst:IfcPositiveInteger_List_1843
        list:hasContents  inst:IfcPositiveInteger_1823 ;
        list:hasNext      inst:IfcPositiveInteger_List_1844 .

inst:IfcPositiveInteger_List_1844
        list:hasContents  inst:IfcPositiveInteger_1837 .

inst:IfcPositiveInteger_List_1845
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1846
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1847
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_1848
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  198 .

inst:IfcPositiveInteger_List_1845
        list:hasContents  inst:IfcPositiveInteger_1848 ;
        list:hasNext      inst:IfcPositiveInteger_List_1846 .

inst:IfcPositiveInteger_1849
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  204 .

inst:IfcPositiveInteger_List_1846
        list:hasContents  inst:IfcPositiveInteger_1849 ;
        list:hasNext      inst:IfcPositiveInteger_List_1847 .

inst:IfcPositiveInteger_1850
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  199 .

inst:IfcPositiveInteger_List_1847
        list:hasContents  inst:IfcPositiveInteger_1850 .

inst:IfcPositiveInteger_List_1851
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1852
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1853
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_1854
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  200 .

inst:IfcPositiveInteger_List_1851
        list:hasContents  inst:IfcPositiveInteger_1854 ;
        list:hasNext      inst:IfcPositiveInteger_List_1852 .

inst:IfcPositiveInteger_List_1852
        list:hasContents  inst:IfcPositiveInteger_1850 ;
        list:hasNext      inst:IfcPositiveInteger_List_1853 .

inst:IfcPositiveInteger_1855
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  203 .

inst:IfcPositiveInteger_List_1853
        list:hasContents  inst:IfcPositiveInteger_1855 .

inst:IfcPositiveInteger_List_1856
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1857
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1858
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1856
        list:hasContents  inst:IfcPositiveInteger_1855 ;
        list:hasNext      inst:IfcPositiveInteger_List_1857 .

inst:IfcPositiveInteger_1859
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  202 .

inst:IfcPositiveInteger_List_1857
        list:hasContents  inst:IfcPositiveInteger_1859 ;
        list:hasNext      inst:IfcPositiveInteger_List_1858 .

inst:IfcPositiveInteger_1860
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  201 .

inst:IfcPositiveInteger_List_1858
        list:hasContents  inst:IfcPositiveInteger_1860 .

inst:IfcPositiveInteger_List_1861
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1862
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1863
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1861
        list:hasContents  inst:IfcPositiveInteger_1854 ;
        list:hasNext      inst:IfcPositiveInteger_List_1862 .

inst:IfcPositiveInteger_List_1862
        list:hasContents  inst:IfcPositiveInteger_1855 ;
        list:hasNext      inst:IfcPositiveInteger_List_1863 .

inst:IfcPositiveInteger_List_1863
        list:hasContents  inst:IfcPositiveInteger_1860 .

inst:IfcPositiveInteger_List_1864
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1865
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1866
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1864
        list:hasContents  inst:IfcPositiveInteger_1855 ;
        list:hasNext      inst:IfcPositiveInteger_List_1865 .

inst:IfcPositiveInteger_List_1865
        list:hasContents  inst:IfcPositiveInteger_1850 ;
        list:hasNext      inst:IfcPositiveInteger_List_1866 .

inst:IfcPositiveInteger_List_1866
        list:hasContents  inst:IfcPositiveInteger_1849 .

inst:IfcPositiveInteger_List_1867
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1868
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1869
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_1870
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  209 .

inst:IfcPositiveInteger_List_1867
        list:hasContents  inst:IfcPositiveInteger_1870 ;
        list:hasNext      inst:IfcPositiveInteger_List_1868 .

inst:IfcPositiveInteger_List_1868
        list:hasContents  inst:IfcPositiveInteger_1831 ;
        list:hasNext      inst:IfcPositiveInteger_List_1869 .

inst:IfcPositiveInteger_List_1869
        list:hasContents  inst:IfcPositiveInteger_1830 .

inst:IfcPositiveInteger_List_1871
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1872
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1873
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1871
        list:hasContents  inst:IfcPositiveInteger_1831 ;
        list:hasNext      inst:IfcPositiveInteger_List_1872 .

inst:IfcPositiveInteger_List_1872
        list:hasContents  inst:IfcPositiveInteger_1841 ;
        list:hasNext      inst:IfcPositiveInteger_List_1873 .

inst:IfcPositiveInteger_List_1873
        list:hasContents  inst:IfcPositiveInteger_1832 .

inst:IfcPositiveInteger_List_1874
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1875
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1876
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_1877
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  206 .

inst:IfcPositiveInteger_List_1874
        list:hasContents  inst:IfcPositiveInteger_1877 ;
        list:hasNext      inst:IfcPositiveInteger_List_1875 .

inst:IfcPositiveInteger_List_1875
        list:hasContents  inst:IfcPositiveInteger_1836 ;
        list:hasNext      inst:IfcPositiveInteger_List_1876 .

inst:IfcPositiveInteger_List_1876
        list:hasContents  inst:IfcPositiveInteger_1841 .

inst:IfcPositiveInteger_List_1878
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1879
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1880
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1878
        list:hasContents  inst:IfcPositiveInteger_1836 ;
        list:hasNext      inst:IfcPositiveInteger_List_1879 .

inst:IfcPositiveInteger_List_1879
        list:hasContents  inst:IfcPositiveInteger_1877 ;
        list:hasNext      inst:IfcPositiveInteger_List_1880 .

inst:IfcPositiveInteger_List_1880
        list:hasContents  inst:IfcPositiveInteger_1837 .

inst:IfcPositiveInteger_List_1881
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1882
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1883
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1881
        list:hasContents  inst:IfcPositiveInteger_1819 ;
        list:hasNext      inst:IfcPositiveInteger_List_1882 .

inst:IfcPositiveInteger_List_1882
        list:hasContents  inst:IfcPositiveInteger_1837 ;
        list:hasNext      inst:IfcPositiveInteger_List_1883 .

inst:IfcPositiveInteger_List_1883
        list:hasContents  inst:IfcPositiveInteger_1849 .

inst:IfcPositiveInteger_List_1884
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1885
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1886
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1884
        list:hasContents  inst:IfcPositiveInteger_1819 ;
        list:hasNext      inst:IfcPositiveInteger_List_1885 .

inst:IfcPositiveInteger_List_1885
        list:hasContents  inst:IfcPositiveInteger_1849 ;
        list:hasNext      inst:IfcPositiveInteger_List_1886 .

inst:IfcPositiveInteger_List_1886
        list:hasContents  inst:IfcPositiveInteger_1848 .

inst:IfcPositiveInteger_List_1887
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1888
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1889
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1887
        list:hasContents  inst:IfcPositiveInteger_1461 ;
        list:hasNext      inst:IfcPositiveInteger_List_1888 .

inst:IfcPositiveInteger_List_1888
        list:hasContents  inst:IfcPositiveInteger_1463 ;
        list:hasNext      inst:IfcPositiveInteger_List_1889 .

inst:IfcPositiveInteger_List_1889
        list:hasContents  inst:IfcPositiveInteger_1467 .

inst:IfcPositiveInteger_List_1890
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1891
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1892
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1890
        list:hasContents  inst:IfcPositiveInteger_1478 ;
        list:hasNext      inst:IfcPositiveInteger_List_1891 .

inst:IfcPositiveInteger_List_1891
        list:hasContents  inst:IfcPositiveInteger_1462 ;
        list:hasNext      inst:IfcPositiveInteger_List_1892 .

inst:IfcPositiveInteger_List_1892
        list:hasContents  inst:IfcPositiveInteger_1461 .

inst:IfcPositiveInteger_List_1893
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1894
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1895
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1893
        list:hasContents  inst:IfcPositiveInteger_1479 ;
        list:hasNext      inst:IfcPositiveInteger_List_1894 .

inst:IfcPositiveInteger_List_1894
        list:hasContents  inst:IfcPositiveInteger_1483 ;
        list:hasNext      inst:IfcPositiveInteger_List_1895 .

inst:IfcPositiveInteger_List_1895
        list:hasContents  inst:IfcPositiveInteger_1496 .

inst:IfcPositiveInteger_List_1896
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1897
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1898
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1896
        list:hasContents  inst:IfcPositiveInteger_1479 ;
        list:hasNext      inst:IfcPositiveInteger_List_1897 .

inst:IfcPositiveInteger_List_1897
        list:hasContents  inst:IfcPositiveInteger_1488 ;
        list:hasNext      inst:IfcPositiveInteger_List_1898 .

inst:IfcPositiveInteger_List_1898
        list:hasContents  inst:IfcPositiveInteger_1462 .

inst:IfcPositiveInteger_List_1899
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1900
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1901
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1899
        list:hasContents  inst:IfcPositiveInteger_1462 ;
        list:hasNext      inst:IfcPositiveInteger_List_1900 .

inst:IfcPositiveInteger_List_1900
        list:hasContents  inst:IfcPositiveInteger_1492 ;
        list:hasNext      inst:IfcPositiveInteger_List_1901 .

inst:IfcPositiveInteger_List_1901
        list:hasContents  inst:IfcPositiveInteger_1463 .

inst:IfcPositiveInteger_List_1902
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1903
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1904
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1902
        list:hasContents  inst:IfcPositiveInteger_1483 ;
        list:hasNext      inst:IfcPositiveInteger_List_1903 .

inst:IfcPositiveInteger_List_1903
        list:hasContents  inst:IfcPositiveInteger_1503 ;
        list:hasNext      inst:IfcPositiveInteger_List_1904 .

inst:IfcPositiveInteger_List_1904
        list:hasContents  inst:IfcPositiveInteger_1497 .

inst:IfcPositiveInteger_List_1905
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1906
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1907
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1905
        list:hasContents  inst:IfcPositiveInteger_1502 ;
        list:hasNext      inst:IfcPositiveInteger_List_1906 .

inst:IfcPositiveInteger_List_1906
        list:hasContents  inst:IfcPositiveInteger_1497 ;
        list:hasNext      inst:IfcPositiveInteger_List_1907 .

inst:IfcPositiveInteger_List_1907
        list:hasContents  inst:IfcPositiveInteger_1503 .

inst:IfcPositiveInteger_List_1908
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1909
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1910
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1908
        list:hasContents  inst:IfcPositiveInteger_1508 ;
        list:hasNext      inst:IfcPositiveInteger_List_1909 .

inst:IfcPositiveInteger_List_1909
        list:hasContents  inst:IfcPositiveInteger_1513 ;
        list:hasNext      inst:IfcPositiveInteger_List_1910 .

inst:IfcPositiveInteger_List_1910
        list:hasContents  inst:IfcPositiveInteger_1509 .

inst:IfcPositiveInteger_List_1911
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1912
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1913
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1911
        list:hasContents  inst:IfcPositiveInteger_1513 ;
        list:hasNext      inst:IfcPositiveInteger_List_1912 .

inst:IfcPositiveInteger_List_1912
        list:hasContents  inst:IfcPositiveInteger_1502 ;
        list:hasNext      inst:IfcPositiveInteger_List_1913 .

inst:IfcPositiveInteger_List_1913
        list:hasContents  inst:IfcPositiveInteger_1501 .

inst:IfcPositiveInteger_List_1914
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1915
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1916
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1914
        list:hasContents  inst:IfcPositiveInteger_1518 ;
        list:hasNext      inst:IfcPositiveInteger_List_1915 .

inst:IfcPositiveInteger_List_1915
        list:hasContents  inst:IfcPositiveInteger_1508 ;
        list:hasNext      inst:IfcPositiveInteger_List_1916 .

inst:IfcPositiveInteger_List_1916
        list:hasContents  inst:IfcPositiveInteger_1507 .

inst:IfcPositiveInteger_List_1917
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1918
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1919
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1917
        list:hasContents  inst:IfcPositiveInteger_1523 ;
        list:hasNext      inst:IfcPositiveInteger_List_1918 .

inst:IfcPositiveInteger_List_1918
        list:hasContents  inst:IfcPositiveInteger_1518 ;
        list:hasNext      inst:IfcPositiveInteger_List_1919 .

inst:IfcPositiveInteger_List_1919
        list:hasContents  inst:IfcPositiveInteger_1517 .

inst:IfcPositiveInteger_List_1920
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1921
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1922
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1920
        list:hasContents  inst:IfcPositiveInteger_1528 ;
        list:hasNext      inst:IfcPositiveInteger_List_1921 .

inst:IfcPositiveInteger_List_1921
        list:hasContents  inst:IfcPositiveInteger_1523 ;
        list:hasNext      inst:IfcPositiveInteger_List_1922 .

inst:IfcPositiveInteger_List_1922
        list:hasContents  inst:IfcPositiveInteger_1522 .

inst:IfcPositiveInteger_List_1923
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1924
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1925
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1923
        list:hasContents  inst:IfcPositiveInteger_1496 ;
        list:hasNext      inst:IfcPositiveInteger_List_1924 .

inst:IfcPositiveInteger_List_1924
        list:hasContents  inst:IfcPositiveInteger_1487 ;
        list:hasNext      inst:IfcPositiveInteger_List_1925 .

inst:IfcPositiveInteger_List_1925
        list:hasContents  inst:IfcPositiveInteger_1479 .

inst:IfcPositiveInteger_List_1926
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1927
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1928
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1926
        list:hasContents  inst:IfcPositiveInteger_1496 ;
        list:hasNext      inst:IfcPositiveInteger_List_1927 .

inst:IfcPositiveInteger_List_1927
        list:hasContents  inst:IfcPositiveInteger_1497 ;
        list:hasNext      inst:IfcPositiveInteger_List_1928 .

inst:IfcPositiveInteger_List_1928
        list:hasContents  inst:IfcPositiveInteger_1543 .

inst:IfcPositiveInteger_List_1929
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1930
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1931
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1929
        list:hasContents  inst:IfcPositiveInteger_1497 ;
        list:hasNext      inst:IfcPositiveInteger_List_1930 .

inst:IfcPositiveInteger_List_1930
        list:hasContents  inst:IfcPositiveInteger_1502 ;
        list:hasNext      inst:IfcPositiveInteger_List_1931 .

inst:IfcPositiveInteger_List_1931
        list:hasContents  inst:IfcPositiveInteger_1547 .

inst:IfcPositiveInteger_List_1932
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1933
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1934
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1932
        list:hasContents  inst:IfcPositiveInteger_1551 ;
        list:hasNext      inst:IfcPositiveInteger_List_1933 .

inst:IfcPositiveInteger_List_1933
        list:hasContents  inst:IfcPositiveInteger_1567 ;
        list:hasNext      inst:IfcPositiveInteger_List_1934 .

inst:IfcPositiveInteger_List_1934
        list:hasContents  inst:IfcPositiveInteger_1513 .

inst:IfcPositiveInteger_List_1935
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1936
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1937
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1935
        list:hasContents  inst:IfcPositiveInteger_1555 ;
        list:hasNext      inst:IfcPositiveInteger_List_1936 .

inst:IfcPositiveInteger_List_1936
        list:hasContents  inst:IfcPositiveInteger_1563 ;
        list:hasNext      inst:IfcPositiveInteger_List_1937 .

inst:IfcPositiveInteger_List_1937
        list:hasContents  inst:IfcPositiveInteger_1518 .

inst:IfcPositiveInteger_List_1938
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1939
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1940
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1938
        list:hasContents  inst:IfcPositiveInteger_1523 ;
        list:hasNext      inst:IfcPositiveInteger_List_1939 .

inst:IfcPositiveInteger_List_1939
        list:hasContents  inst:IfcPositiveInteger_1528 ;
        list:hasNext      inst:IfcPositiveInteger_List_1940 .

inst:IfcPositiveInteger_List_1940
        list:hasContents  inst:IfcPositiveInteger_1559 .

inst:IfcPositiveInteger_List_1941
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1942
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1943
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1941
        list:hasContents  inst:IfcPositiveInteger_1563 ;
        list:hasNext      inst:IfcPositiveInteger_List_1942 .

inst:IfcPositiveInteger_List_1942
        list:hasContents  inst:IfcPositiveInteger_1551 ;
        list:hasNext      inst:IfcPositiveInteger_List_1943 .

inst:IfcPositiveInteger_List_1943
        list:hasContents  inst:IfcPositiveInteger_1508 .

inst:IfcPositiveInteger_List_1944
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1945
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1946
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1944
        list:hasContents  inst:IfcPositiveInteger_1567 ;
        list:hasNext      inst:IfcPositiveInteger_List_1945 .

inst:IfcPositiveInteger_List_1945
        list:hasContents  inst:IfcPositiveInteger_1547 ;
        list:hasNext      inst:IfcPositiveInteger_List_1946 .

inst:IfcPositiveInteger_List_1946
        list:hasContents  inst:IfcPositiveInteger_1502 .

inst:IfcPositiveInteger_List_1947
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1948
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1949
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1947
        list:hasContents  inst:IfcPositiveInteger_1527 ;
        list:hasNext      inst:IfcPositiveInteger_List_1948 .

inst:IfcPositiveInteger_List_1948
        list:hasContents  inst:IfcPositiveInteger_1577 ;
        list:hasNext      inst:IfcPositiveInteger_List_1949 .

inst:IfcPositiveInteger_List_1949
        list:hasContents  inst:IfcPositiveInteger_1571 .

inst:IfcPositiveInteger_List_1950
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1951
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1952
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1950
        list:hasContents  inst:IfcPositiveInteger_1576 ;
        list:hasNext      inst:IfcPositiveInteger_List_1951 .

inst:IfcPositiveInteger_List_1951
        list:hasContents  inst:IfcPositiveInteger_1571 ;
        list:hasNext      inst:IfcPositiveInteger_List_1952 .

inst:IfcPositiveInteger_List_1952
        list:hasContents  inst:IfcPositiveInteger_1577 .

inst:IfcPositiveInteger_List_1953
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1954
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1955
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1953
        list:hasContents  inst:IfcPositiveInteger_1582 ;
        list:hasNext      inst:IfcPositiveInteger_List_1954 .

inst:IfcPositiveInteger_List_1954
        list:hasContents  inst:IfcPositiveInteger_1587 ;
        list:hasNext      inst:IfcPositiveInteger_List_1955 .

inst:IfcPositiveInteger_List_1955
        list:hasContents  inst:IfcPositiveInteger_1583 .

inst:IfcPositiveInteger_List_1956
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1957
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1958
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1956
        list:hasContents  inst:IfcPositiveInteger_1587 ;
        list:hasNext      inst:IfcPositiveInteger_List_1957 .

inst:IfcPositiveInteger_List_1957
        list:hasContents  inst:IfcPositiveInteger_1576 ;
        list:hasNext      inst:IfcPositiveInteger_List_1958 .

inst:IfcPositiveInteger_List_1958
        list:hasContents  inst:IfcPositiveInteger_1575 .

inst:IfcPositiveInteger_List_1959
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1960
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1961
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1959
        list:hasContents  inst:IfcPositiveInteger_1591 ;
        list:hasNext      inst:IfcPositiveInteger_List_1960 .

inst:IfcPositiveInteger_List_1960
        list:hasContents  inst:IfcPositiveInteger_1608 ;
        list:hasNext      inst:IfcPositiveInteger_List_1961 .

inst:IfcPositiveInteger_List_1961
        list:hasContents  inst:IfcPositiveInteger_1571 .

inst:IfcPositiveInteger_List_1962
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1963
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1964
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1962
        list:hasContents  inst:IfcPositiveInteger_1596 ;
        list:hasNext      inst:IfcPositiveInteger_List_1963 .

inst:IfcPositiveInteger_List_1963
        list:hasContents  inst:IfcPositiveInteger_1582 ;
        list:hasNext      inst:IfcPositiveInteger_List_1964 .

inst:IfcPositiveInteger_List_1964
        list:hasContents  inst:IfcPositiveInteger_1581 .

inst:IfcPositiveInteger_List_1965
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1966
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1967
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1965
        list:hasContents  inst:IfcPositiveInteger_1600 ;
        list:hasNext      inst:IfcPositiveInteger_List_1966 .

inst:IfcPositiveInteger_List_1966
        list:hasContents  inst:IfcPositiveInteger_1604 ;
        list:hasNext      inst:IfcPositiveInteger_List_1967 .

inst:IfcPositiveInteger_List_1967
        list:hasContents  inst:IfcPositiveInteger_1587 .

inst:IfcPositiveInteger_List_1968
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1969
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1970
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1968
        list:hasContents  inst:IfcPositiveInteger_1604 ;
        list:hasNext      inst:IfcPositiveInteger_List_1969 .

inst:IfcPositiveInteger_List_1969
        list:hasContents  inst:IfcPositiveInteger_1591 ;
        list:hasNext      inst:IfcPositiveInteger_List_1970 .

inst:IfcPositiveInteger_List_1970
        list:hasContents  inst:IfcPositiveInteger_1576 .

inst:IfcPositiveInteger_List_1971
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1972
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1973
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1971
        list:hasContents  inst:IfcPositiveInteger_1528 ;
        list:hasNext      inst:IfcPositiveInteger_List_1972 .

inst:IfcPositiveInteger_List_1972
        list:hasContents  inst:IfcPositiveInteger_1571 ;
        list:hasNext      inst:IfcPositiveInteger_List_1973 .

inst:IfcPositiveInteger_List_1973
        list:hasContents  inst:IfcPositiveInteger_1608 .

inst:IfcPositiveInteger_List_1974
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1975
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1976
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1974
        list:hasContents  inst:IfcPositiveInteger_1612 ;
        list:hasNext      inst:IfcPositiveInteger_List_1975 .

inst:IfcPositiveInteger_List_1975
        list:hasContents  inst:IfcPositiveInteger_1614 ;
        list:hasNext      inst:IfcPositiveInteger_List_1976 .

inst:IfcPositiveInteger_List_1976
        list:hasContents  inst:IfcPositiveInteger_1620 .

inst:IfcPositiveInteger_List_1977
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1978
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1979
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1977
        list:hasContents  inst:IfcPositiveInteger_1618 ;
        list:hasNext      inst:IfcPositiveInteger_List_1978 .

inst:IfcPositiveInteger_List_1978
        list:hasContents  inst:IfcPositiveInteger_1620 ;
        list:hasNext      inst:IfcPositiveInteger_List_1979 .

inst:IfcPositiveInteger_List_1979
        list:hasContents  inst:IfcPositiveInteger_1614 .

inst:IfcPositiveInteger_List_1980
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1981
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1982
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1980
        list:hasContents  inst:IfcPositiveInteger_1612 ;
        list:hasNext      inst:IfcPositiveInteger_List_1981 .

inst:IfcPositiveInteger_List_1981
        list:hasContents  inst:IfcPositiveInteger_1625 ;
        list:hasNext      inst:IfcPositiveInteger_List_1982 .

inst:IfcPositiveInteger_List_1982
        list:hasContents  inst:IfcPositiveInteger_1596 .

inst:IfcPositiveInteger_List_1983
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1984
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1985
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1983
        list:hasContents  inst:IfcPositiveInteger_1613 ;
        list:hasNext      inst:IfcPositiveInteger_List_1984 .

inst:IfcPositiveInteger_List_1984
        list:hasContents  inst:IfcPositiveInteger_1596 ;
        list:hasNext      inst:IfcPositiveInteger_List_1985 .

inst:IfcPositiveInteger_List_1985
        list:hasContents  inst:IfcPositiveInteger_1595 .

inst:IfcPositiveInteger_List_1986
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1987
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1988
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1986
        list:hasContents  inst:IfcPositiveInteger_1632 ;
        list:hasNext      inst:IfcPositiveInteger_List_1987 .

inst:IfcPositiveInteger_List_1987
        list:hasContents  inst:IfcPositiveInteger_1634 ;
        list:hasNext      inst:IfcPositiveInteger_List_1988 .

inst:IfcPositiveInteger_List_1988
        list:hasContents  inst:IfcPositiveInteger_1643 .

inst:IfcPositiveInteger_List_1989
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1990
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1991
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1989
        list:hasContents  inst:IfcPositiveInteger_1638 ;
        list:hasNext      inst:IfcPositiveInteger_List_1990 .

inst:IfcPositiveInteger_List_1990
        list:hasContents  inst:IfcPositiveInteger_1633 ;
        list:hasNext      inst:IfcPositiveInteger_List_1991 .

inst:IfcPositiveInteger_List_1991
        list:hasContents  inst:IfcPositiveInteger_1632 .

inst:IfcPositiveInteger_List_1992
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1993
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1994
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1992
        list:hasContents  inst:IfcPositiveInteger_1643 ;
        list:hasNext      inst:IfcPositiveInteger_List_1993 .

inst:IfcPositiveInteger_List_1993
        list:hasContents  inst:IfcPositiveInteger_1619 ;
        list:hasNext      inst:IfcPositiveInteger_List_1994 .

inst:IfcPositiveInteger_List_1994
        list:hasContents  inst:IfcPositiveInteger_1618 .

inst:IfcPositiveInteger_List_1995
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1996
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1997
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1995
        list:hasContents  inst:IfcPositiveInteger_1647 ;
        list:hasNext      inst:IfcPositiveInteger_List_1996 .

inst:IfcPositiveInteger_List_1996
        list:hasContents  inst:IfcPositiveInteger_1639 ;
        list:hasNext      inst:IfcPositiveInteger_List_1997 .

inst:IfcPositiveInteger_List_1997
        list:hasContents  inst:IfcPositiveInteger_1638 .

inst:IfcPositiveInteger_List_1998
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1999
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_2000
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1998
        list:hasContents  inst:IfcPositiveInteger_1625 ;
        list:hasNext      inst:IfcPositiveInteger_List_1999 .

inst:IfcPositiveInteger_List_1999
        list:hasContents  inst:IfcPositiveInteger_1600 ;
        list:hasNext      inst:IfcPositiveInteger_List_2000 .

inst:IfcPositiveInteger_List_2000
        list:hasContents  inst:IfcPositiveInteger_1582 .

inst:IfcPositiveInteger_List_2001
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_2002
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_2003
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_2001
        list:hasContents  inst:IfcPositiveInteger_1624 ;
        list:hasNext      inst:IfcPositiveInteger_List_2002 .

inst:IfcPositiveInteger_List_2002
        list:hasContents  inst:IfcPositiveInteger_1620 ;
        list:hasNext      inst:IfcPositiveInteger_List_2003 .

inst:IfcPositiveInteger_List_2003
        list:hasContents  inst:IfcPositiveInteger_1658 .

inst:IfcPositiveInteger_List_2004
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_2005
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_2006
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_2004
        list:hasContents  inst:IfcPositiveInteger_1658 ;
        list:hasNext      inst:IfcPositiveInteger_List_2005 .

inst:IfcPositiveInteger_List_2005
        list:hasContents  inst:IfcPositiveInteger_1619 ;
        list:hasNext      inst:IfcPositiveInteger_List_2006 .

inst:IfcPositiveInteger_List_2006
        list:hasContents  inst:IfcPositiveInteger_1663 .

inst:IfcPositiveInteger_List_2007
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_2008
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_2009
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_2007
        list:hasContents  inst:IfcPositiveInteger_1634 ;
        list:hasNext      inst:IfcPositiveInteger_List_2008 .

inst:IfcPositiveInteger_List_2008
        list:hasContents  inst:IfcPositiveInteger_1663 ;
        list:hasNext      inst:IfcPositiveInteger_List_2009 .

inst:IfcPositiveInteger_List_2009
        list:hasContents  inst:IfcPositiveInteger_1619 .

inst:IfcPositiveInteger_List_2010
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_2011
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_2012
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_2010
        list:hasContents  inst:IfcPositiveInteger_1639 ;
        list:hasNext      inst:IfcPositiveInteger_List_2011 .

inst:IfcPositiveInteger_List_2011
        list:hasContents  inst:IfcPositiveInteger_1668 ;
        list:hasNext      inst:IfcPositiveInteger_List_2012 .

inst:IfcPositiveInteger_List_2012
        list:hasContents  inst:IfcPositiveInteger_1633 .

inst:IfcPositiveInteger_List_2013
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_2014
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_2015
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_2013
        list:hasContents  inst:IfcPositiveInteger_1648 ;
        list:hasNext      inst:IfcPositiveInteger_List_2014 .

inst:IfcPositiveInteger_List_2014
        list:hasContents  inst:IfcPositiveInteger_1667 ;
        list:hasNext      inst:IfcPositiveInteger_List_2015 .

inst:IfcPositiveInteger_List_2015
        list:hasContents  inst:IfcPositiveInteger_1639 .

inst:IfcPositiveInteger_List_2016
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_2017
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_2018
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_2016
        list:hasContents  inst:IfcPositiveInteger_1633 ;
        list:hasNext      inst:IfcPositiveInteger_List_2017 .

inst:IfcPositiveInteger_List_2017
        list:hasContents  inst:IfcPositiveInteger_1662 ;
        list:hasNext      inst:IfcPositiveInteger_List_2018 .

inst:IfcPositiveInteger_List_2018
        list:hasContents  inst:IfcPositiveInteger_1634 .

inst:IfcPositiveInteger_List_2019
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_2020
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_2021
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_2019
        list:hasContents  inst:IfcPositiveInteger_1679 ;
        list:hasNext      inst:IfcPositiveInteger_List_2020 .

inst:IfcPositiveInteger_List_2020
        list:hasContents  inst:IfcPositiveInteger_1681 ;
        list:hasNext      inst:IfcPositiveInteger_List_2021 .

inst:IfcPositiveInteger_2022
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  147 .

inst:IfcPositiveInteger_List_2021
        list:hasContents  inst:IfcPositiveInteger_2022 .

inst:IfcPositiveInteger_List_2023
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_2024
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_2025
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_2023
        list:hasContents  inst:IfcPositiveInteger_1685 ;
        list:hasNext      inst:IfcPositiveInteger_List_2024 .

inst:IfcPositiveInteger_List_2024
        list:hasContents  inst:IfcPositiveInteger_1680 ;
        list:hasNext      inst:IfcPositiveInteger_List_2025 .

inst:IfcPositiveInteger_List_2025
        list:hasContents  inst:IfcPositiveInteger_1679 .

inst:IfcPositiveInteger_List_2026
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_2027
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_2028
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_2026
        list:hasContents  inst:IfcPositiveInteger_1690 ;
        list:hasNext      inst:IfcPositiveInteger_List_2027 .

inst:IfcPositiveInteger_List_2027
        list:hasContents  inst:IfcPositiveInteger_1692 ;
        list:hasNext      inst:IfcPositiveInteger_List_2028 .

inst:IfcPositiveInteger_List_2028
        list:hasContents  inst:IfcPositiveInteger_1701 .

inst:IfcPositiveInteger_List_2029
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_2030
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_2031
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_2029
        list:hasContents  inst:IfcPositiveInteger_1696 ;
        list:hasNext      inst:IfcPositiveInteger_List_2030 .

inst:IfcPositiveInteger_List_2030
        list:hasContents  inst:IfcPositiveInteger_1691 ;
        list:hasNext      inst:IfcPositiveInteger_List_2031 .

inst:IfcPositiveInteger_List_2031
        list:hasContents  inst:IfcPositiveInteger_1690 .

inst:IfcPositiveInteger_List_2032
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_2033
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_2034
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_2032
        list:hasContents  inst:IfcPositiveInteger_1701 ;
        list:hasNext      inst:IfcPositiveInteger_List_2033 .

inst:IfcPositiveInteger_List_2033
        list:hasContents  inst:IfcPositiveInteger_1686 ;
        list:hasNext      inst:IfcPositiveInteger_List_2034 .

inst:IfcPositiveInteger_List_2034
        list:hasContents  inst:IfcPositiveInteger_1685 .

inst:IfcPositiveInteger_List_2035
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_2036
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_2037
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_2035
        list:hasContents  inst:IfcPositiveInteger_1705 ;
        list:hasNext      inst:IfcPositiveInteger_List_2036 .

inst:IfcPositiveInteger_List_2036
        list:hasContents  inst:IfcPositiveInteger_1707 ;
        list:hasNext      inst:IfcPositiveInteger_List_2037 .

inst:IfcPositiveInteger_List_2037
        list:hasContents  inst:IfcPositiveInteger_1731 .

inst:IfcPositiveInteger_List_2038
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_2039
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_2040
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_2038
        list:hasContents  inst:IfcPositiveInteger_1711 ;
        list:hasNext      inst:IfcPositiveInteger_List_2039 .

inst:IfcPositiveInteger_List_2039
        list:hasContents  inst:IfcPositiveInteger_1706 ;
        list:hasNext      inst:IfcPositiveInteger_List_2040 .

inst:IfcPositiveInteger_List_2040
        list:hasContents  inst:IfcPositiveInteger_1705 .

inst:IfcPositiveInteger_List_2041
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_2042
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_2043
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_2041
        list:hasContents  inst:IfcPositiveInteger_1716 ;
        list:hasNext      inst:IfcPositiveInteger_List_2042 .

inst:IfcPositiveInteger_List_2042
        list:hasContents  inst:IfcPositiveInteger_1718 ;
        list:hasNext      inst:IfcPositiveInteger_List_2043 .

inst:IfcPositiveInteger_List_2043
        list:hasContents  inst:IfcPositiveInteger_1727 .

inst:IfcPositiveInteger_List_2044
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_2045
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_2046
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_2044
        list:hasContents  inst:IfcPositiveInteger_1722 ;
        list:hasNext      inst:IfcPositiveInteger_List_2045 .

inst:IfcPositiveInteger_List_2045
        list:hasContents  inst:IfcPositiveInteger_1717 ;
        list:hasNext      inst:IfcPositiveInteger_List_2046 .

inst:IfcPositiveInteger_List_2046
        list:hasContents  inst:IfcPositiveInteger_1716 .

inst:IfcPositiveInteger_List_2047
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_2048
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_2049
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_2047
        list:hasContents  inst:IfcPositiveInteger_1727 ;
        list:hasNext      inst:IfcPositiveInteger_List_2048 .

inst:IfcPositiveInteger_List_2048
        list:hasContents  inst:IfcPositiveInteger_1712 ;
        list:hasNext      inst:IfcPositiveInteger_List_2049 .

inst:IfcPositiveInteger_List_2049
        list:hasContents  inst:IfcPositiveInteger_1711 .

inst:IfcPositiveInteger_List_2050
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_2051
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_2052
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_2050
        list:hasContents  inst:IfcPositiveInteger_1731 ;
        list:hasNext      inst:IfcPositiveInteger_List_2051 .

inst:IfcPositiveInteger_List_2051
        list:hasContents  inst:IfcPositiveInteger_1697 ;
        list:hasNext      inst:IfcPositiveInteger_List_2052 .

inst:IfcPositiveInteger_List_2052
        list:hasContents  inst:IfcPositiveInteger_1696 .

inst:IfcPositiveInteger_List_2053
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_2054
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_2055
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_2053
        list:hasContents  inst:IfcPositiveInteger_1736 ;
        list:hasNext      inst:IfcPositiveInteger_List_2054 .

inst:IfcPositiveInteger_List_2054
        list:hasContents  inst:IfcPositiveInteger_1796 ;
        list:hasNext      inst:IfcPositiveInteger_List_2055 .

inst:IfcPositiveInteger_List_2055
        list:hasContents  inst:IfcPositiveInteger_1737 .

inst:IfcPositiveInteger_List_2056
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_2057
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_2058
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_2056
        list:hasContents  inst:IfcPositiveInteger_1742 ;
        list:hasNext      inst:IfcPositiveInteger_List_2057 .

inst:IfcPositiveInteger_List_2057
        list:hasContents  inst:IfcPositiveInteger_1736 ;
        list:hasNext      inst:IfcPositiveInteger_List_2058 .

inst:IfcPositiveInteger_List_2058
        list:hasContents  inst:IfcPositiveInteger_1735 .

inst:IfcPositiveInteger_List_2059
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_2060
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_2061
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_2059
        list:hasContents  inst:IfcPositiveInteger_1747 ;
        list:hasNext      inst:IfcPositiveInteger_List_2060 .

inst:IfcPositiveInteger_List_2060
        list:hasContents  inst:IfcPositiveInteger_1757 ;
        list:hasNext      inst:IfcPositiveInteger_List_2061 .

inst:IfcPositiveInteger_List_2061
        list:hasContents  inst:IfcPositiveInteger_1748 .

inst:IfcPositiveInteger_List_2062
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_2063
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_2064
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_2062
        list:hasContents  inst:IfcPositiveInteger_1753 ;
        list:hasNext      inst:IfcPositiveInteger_List_2063 .

inst:IfcPositiveInteger_List_2063
        list:hasContents  inst:IfcPositiveInteger_1747 ;
        list:hasNext      inst:IfcPositiveInteger_List_2064 .

inst:IfcPositiveInteger_List_2064
        list:hasContents  inst:IfcPositiveInteger_1746 .

inst:IfcPositiveInteger_List_2065
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_2066
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_2067
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_2065
        list:hasContents  inst:IfcPositiveInteger_1757 ;
        list:hasNext      inst:IfcPositiveInteger_List_2066 .

inst:IfcPositiveInteger_List_2066
        list:hasContents  inst:IfcPositiveInteger_1742 ;
        list:hasNext      inst:IfcPositiveInteger_List_2067 .

inst:IfcPositiveInteger_List_2067
        list:hasContents  inst:IfcPositiveInteger_1741 .

inst:IfcPositiveInteger_List_2068
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_2069
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_2070
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_2068
        list:hasContents  inst:IfcPositiveInteger_1762 ;
        list:hasNext      inst:IfcPositiveInteger_List_2069 .

inst:IfcPositiveInteger_List_2069
        list:hasContents  inst:IfcPositiveInteger_1792 ;
        list:hasNext      inst:IfcPositiveInteger_List_2070 .

inst:IfcPositiveInteger_List_2070
        list:hasContents  inst:IfcPositiveInteger_1763 .

inst:IfcPositiveInteger_List_2071
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_2072
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_2073
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_2071
        list:hasContents  inst:IfcPositiveInteger_1768 ;
        list:hasNext      inst:IfcPositiveInteger_List_2072 .

inst:IfcPositiveInteger_List_2072
        list:hasContents  inst:IfcPositiveInteger_1762 ;
        list:hasNext      inst:IfcPositiveInteger_List_2073 .

inst:IfcPositiveInteger_List_2073
        list:hasContents  inst:IfcPositiveInteger_1761 .

inst:IfcPositiveInteger_List_2074
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_2075
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_2076
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_2074
        list:hasContents  inst:IfcPositiveInteger_1773 ;
        list:hasNext      inst:IfcPositiveInteger_List_2075 .

inst:IfcPositiveInteger_List_2075
        list:hasContents  inst:IfcPositiveInteger_1788 ;
        list:hasNext      inst:IfcPositiveInteger_List_2076 .

inst:IfcPositiveInteger_List_2076
        list:hasContents  inst:IfcPositiveInteger_1774 .

inst:IfcPositiveInteger_List_2077
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_2078
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_2079
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_2077
        list:hasContents  inst:IfcPositiveInteger_1779 ;
        list:hasNext      inst:IfcPositiveInteger_List_2078 .

inst:IfcPositiveInteger_List_2078
        list:hasContents  inst:IfcPositiveInteger_1784 ;
        list:hasNext      inst:IfcPositiveInteger_List_2079 .

inst:IfcPositiveInteger_List_2079
        list:hasContents  inst:IfcPositiveInteger_1780 .

inst:IfcPositiveInteger_List_2080
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_2081
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_2082
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_2080
        list:hasContents  inst:IfcPositiveInteger_1784 ;
        list:hasNext      inst:IfcPositiveInteger_List_2081 .

inst:IfcPositiveInteger_List_2081
        list:hasContents  inst:IfcPositiveInteger_1773 ;
        list:hasNext      inst:IfcPositiveInteger_List_2082 .

inst:IfcPositiveInteger_List_2082
        list:hasContents  inst:IfcPositiveInteger_1772 .

inst:IfcPositiveInteger_List_2083
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_2084
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_2085
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_2083
        list:hasContents  inst:IfcPositiveInteger_1788 ;
        list:hasNext      inst:IfcPositiveInteger_List_2084 .

inst:IfcPositiveInteger_List_2084
        list:hasContents  inst:IfcPositiveInteger_1768 ;
        list:hasNext      inst:IfcPositiveInteger_List_2085 .

inst:IfcPositiveInteger_List_2085
        list:hasContents  inst:IfcPositiveInteger_1767 .

inst:IfcPositiveInteger_List_2086
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_2087
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_2088
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_2086
        list:hasContents  inst:IfcPositiveInteger_1792 ;
        list:hasNext      inst:IfcPositiveInteger_List_2087 .

inst:IfcPositiveInteger_List_2087
        list:hasContents  inst:IfcPositiveInteger_1753 ;
        list:hasNext      inst:IfcPositiveInteger_List_2088 .

inst:IfcPositiveInteger_List_2088
        list:hasContents  inst:IfcPositiveInteger_1752 .

inst:IfcPositiveInteger_List_2089
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_2090
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_2091
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_2089
        list:hasContents  inst:IfcPositiveInteger_1737 ;
        list:hasNext      inst:IfcPositiveInteger_List_2090 .

inst:IfcPositiveInteger_List_2090
        list:hasContents  inst:IfcPositiveInteger_1723 ;
        list:hasNext      inst:IfcPositiveInteger_List_2091 .

inst:IfcPositiveInteger_List_2091
        list:hasContents  inst:IfcPositiveInteger_1722 .

inst:IfcPositiveInteger_List_List_2092
        rdf:type  ifc:IfcPositiveInteger_List_List .

inst:IfcTriangulatedFaceSet_201
        ifc:coordIndex_IfcTriangulatedFaceSet  inst:IfcPositiveInteger_List_List_2092 .

inst:IfcPositiveInteger_List_List_2092
        list:hasContents  inst:IfcPositiveInteger_List_1171 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2093 .

inst:IfcPositiveInteger_List_List_2093
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1177 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2094 .

inst:IfcPositiveInteger_List_List_2094
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1181 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2095 .

inst:IfcPositiveInteger_List_List_2095
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1186 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2096 .

inst:IfcPositiveInteger_List_List_2096
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1189 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2097 .

inst:IfcPositiveInteger_List_List_2097
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1193 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2098 .

inst:IfcPositiveInteger_List_List_2098
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1196 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2099 .

inst:IfcPositiveInteger_List_List_2099
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1202 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2100 .

inst:IfcPositiveInteger_List_List_2100
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1207 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2101 .

inst:IfcPositiveInteger_List_List_2101
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1211 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2102 .

inst:IfcPositiveInteger_List_List_2102
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1214 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2103 .

inst:IfcPositiveInteger_List_List_2103
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1217 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2104 .

inst:IfcPositiveInteger_List_List_2104
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1220 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2105 .

inst:IfcPositiveInteger_List_List_2105
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1225 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2106 .

inst:IfcPositiveInteger_List_List_2106
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1229 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2107 .

inst:IfcPositiveInteger_List_List_2107
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1232 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2108 .

inst:IfcPositiveInteger_List_List_2108
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1238 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2109 .

inst:IfcPositiveInteger_List_List_2109
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1241 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2110 .

inst:IfcPositiveInteger_List_List_2110
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1244 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2111 .

inst:IfcPositiveInteger_List_List_2111
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1249 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2112 .

inst:IfcPositiveInteger_List_List_2112
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1253 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2113 .

inst:IfcPositiveInteger_List_List_2113
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1256 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2114 .

inst:IfcPositiveInteger_List_List_2114
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1261 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2115 .

inst:IfcPositiveInteger_List_List_2115
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1264 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2116 .

inst:IfcPositiveInteger_List_List_2116
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1268 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2117 .

inst:IfcPositiveInteger_List_List_2117
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1273 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2118 .

inst:IfcPositiveInteger_List_List_2118
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1278 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2119 .

inst:IfcPositiveInteger_List_List_2119
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1281 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2120 .

inst:IfcPositiveInteger_List_List_2120
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1285 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2121 .

inst:IfcPositiveInteger_List_List_2121
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1289 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2122 .

inst:IfcPositiveInteger_List_List_2122
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1294 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2123 .

inst:IfcPositiveInteger_List_List_2123
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1298 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2124 .

inst:IfcPositiveInteger_List_List_2124
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1303 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2125 .

inst:IfcPositiveInteger_List_List_2125
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1306 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2126 .

inst:IfcPositiveInteger_List_List_2126
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1309 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2127 .

inst:IfcPositiveInteger_List_List_2127
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1313 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2128 .

inst:IfcPositiveInteger_List_List_2128
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1319 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2129 .

inst:IfcPositiveInteger_List_List_2129
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1323 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2130 .

inst:IfcPositiveInteger_List_List_2130
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1327 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2131 .

inst:IfcPositiveInteger_List_List_2131
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1331 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2132 .

inst:IfcPositiveInteger_List_List_2132
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1334 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2133 .

inst:IfcPositiveInteger_List_List_2133
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1337 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2134 .

inst:IfcPositiveInteger_List_List_2134
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1342 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2135 .

inst:IfcPositiveInteger_List_List_2135
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1345 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2136 .

inst:IfcPositiveInteger_List_List_2136
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1349 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2137 .

inst:IfcPositiveInteger_List_List_2137
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1352 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2138 .

inst:IfcPositiveInteger_List_List_2138
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1356 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2139 .

inst:IfcPositiveInteger_List_List_2139
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1359 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2140 .

inst:IfcPositiveInteger_List_List_2140
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1362 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2141 .

inst:IfcPositiveInteger_List_List_2141
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1368 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2142 .

inst:IfcPositiveInteger_List_List_2142
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1372 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2143 .

inst:IfcPositiveInteger_List_List_2143
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1377 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2144 .

inst:IfcPositiveInteger_List_List_2144
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1380 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2145 .

inst:IfcPositiveInteger_List_List_2145
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1384 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2146 .

inst:IfcPositiveInteger_List_List_2146
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1390 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2147 .

inst:IfcPositiveInteger_List_List_2147
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1394 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2148 .

inst:IfcPositiveInteger_List_List_2148
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1400 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2149 .

inst:IfcPositiveInteger_List_List_2149
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1404 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2150 .

inst:IfcPositiveInteger_List_List_2150
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1408 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2151 .

inst:IfcPositiveInteger_List_List_2151
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1411 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2152 .

inst:IfcPositiveInteger_List_List_2152
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1416 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2153 .

inst:IfcPositiveInteger_List_List_2153
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1420 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2154 .

inst:IfcPositiveInteger_List_List_2154
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1426 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2155 .

inst:IfcPositiveInteger_List_List_2155
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1429 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2156 .

inst:IfcPositiveInteger_List_List_2156
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1433 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2157 .

inst:IfcPositiveInteger_List_List_2157
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1436 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2158 .

inst:IfcPositiveInteger_List_List_2158
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1439 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2159 .

inst:IfcPositiveInteger_List_List_2159
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1443 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2160 .

inst:IfcPositiveInteger_List_List_2160
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1446 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2161 .

inst:IfcPositiveInteger_List_List_2161
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1449 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2162 .

inst:IfcPositiveInteger_List_List_2162
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1452 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2163 .

inst:IfcPositiveInteger_List_List_2163
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1458 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2164 .

inst:IfcPositiveInteger_List_List_2164
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1464 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2165 .

inst:IfcPositiveInteger_List_List_2165
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1468 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2166 .

inst:IfcPositiveInteger_List_List_2166
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1472 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2167 .

inst:IfcPositiveInteger_List_List_2167
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1475 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2168 .

inst:IfcPositiveInteger_List_List_2168
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1480 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2169 .

inst:IfcPositiveInteger_List_List_2169
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1484 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2170 .

inst:IfcPositiveInteger_List_List_2170
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1489 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2171 .

inst:IfcPositiveInteger_List_List_2171
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1493 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2172 .

inst:IfcPositiveInteger_List_List_2172
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1498 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2173 .

inst:IfcPositiveInteger_List_List_2173
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1504 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2174 .

inst:IfcPositiveInteger_List_List_2174
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1510 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2175 .

inst:IfcPositiveInteger_List_List_2175
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1514 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2176 .

inst:IfcPositiveInteger_List_List_2176
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1519 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2177 .

inst:IfcPositiveInteger_List_List_2177
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1524 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2178 .

inst:IfcPositiveInteger_List_List_2178
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1529 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2179 .

inst:IfcPositiveInteger_List_List_2179
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1533 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2180 .

inst:IfcPositiveInteger_List_List_2180
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1537 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2181 .

inst:IfcPositiveInteger_List_List_2181
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1540 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2182 .

inst:IfcPositiveInteger_List_List_2182
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1544 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2183 .

inst:IfcPositiveInteger_List_List_2183
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1548 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2184 .

inst:IfcPositiveInteger_List_List_2184
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1552 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2185 .

inst:IfcPositiveInteger_List_List_2185
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1556 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2186 .

inst:IfcPositiveInteger_List_List_2186
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1560 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2187 .

inst:IfcPositiveInteger_List_List_2187
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1564 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2188 .

inst:IfcPositiveInteger_List_List_2188
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1568 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2189 .

inst:IfcPositiveInteger_List_List_2189
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1572 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2190 .

inst:IfcPositiveInteger_List_List_2190
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1578 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2191 .

inst:IfcPositiveInteger_List_List_2191
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1584 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2192 .

inst:IfcPositiveInteger_List_List_2192
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1588 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2193 .

inst:IfcPositiveInteger_List_List_2193
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1592 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2194 .

inst:IfcPositiveInteger_List_List_2194
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1597 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2195 .

inst:IfcPositiveInteger_List_List_2195
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1601 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2196 .

inst:IfcPositiveInteger_List_List_2196
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1605 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2197 .

inst:IfcPositiveInteger_List_List_2197
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1609 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2198 .

inst:IfcPositiveInteger_List_List_2198
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1615 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2199 .

inst:IfcPositiveInteger_List_List_2199
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1621 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2200 .

inst:IfcPositiveInteger_List_List_2200
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1626 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2201 .

inst:IfcPositiveInteger_List_List_2201
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1629 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2202 .

inst:IfcPositiveInteger_List_List_2202
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1635 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2203 .

inst:IfcPositiveInteger_List_List_2203
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1640 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2204 .

inst:IfcPositiveInteger_List_List_2204
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1644 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2205 .

inst:IfcPositiveInteger_List_List_2205
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1649 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2206 .

inst:IfcPositiveInteger_List_List_2206
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1652 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2207 .

inst:IfcPositiveInteger_List_List_2207
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1655 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2208 .

inst:IfcPositiveInteger_List_List_2208
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1659 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2209 .

inst:IfcPositiveInteger_List_List_2209
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1664 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2210 .

inst:IfcPositiveInteger_List_List_2210
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1669 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2211 .

inst:IfcPositiveInteger_List_List_2211
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1673 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2212 .

inst:IfcPositiveInteger_List_List_2212
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1676 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2213 .

inst:IfcPositiveInteger_List_List_2213
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1682 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2214 .

inst:IfcPositiveInteger_List_List_2214
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1687 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2215 .

inst:IfcPositiveInteger_List_List_2215
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1693 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2216 .

inst:IfcPositiveInteger_List_List_2216
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1698 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2217 .

inst:IfcPositiveInteger_List_List_2217
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1702 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2218 .

inst:IfcPositiveInteger_List_List_2218
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1708 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2219 .

inst:IfcPositiveInteger_List_List_2219
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1713 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2220 .

inst:IfcPositiveInteger_List_List_2220
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1719 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2221 .

inst:IfcPositiveInteger_List_List_2221
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1724 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2222 .

inst:IfcPositiveInteger_List_List_2222
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1728 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2223 .

inst:IfcPositiveInteger_List_List_2223
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1732 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2224 .

inst:IfcPositiveInteger_List_List_2224
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1738 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2225 .

inst:IfcPositiveInteger_List_List_2225
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1743 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2226 .

inst:IfcPositiveInteger_List_List_2226
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1749 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2227 .

inst:IfcPositiveInteger_List_List_2227
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1754 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2228 .

inst:IfcPositiveInteger_List_List_2228
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1758 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2229 .

inst:IfcPositiveInteger_List_List_2229
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1764 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2230 .

inst:IfcPositiveInteger_List_List_2230
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1769 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2231 .

inst:IfcPositiveInteger_List_List_2231
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1775 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2232 .

inst:IfcPositiveInteger_List_List_2232
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1781 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2233 .

inst:IfcPositiveInteger_List_List_2233
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1785 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2234 .

inst:IfcPositiveInteger_List_List_2234
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1789 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2235 .

inst:IfcPositiveInteger_List_List_2235
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1793 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2236 .

inst:IfcPositiveInteger_List_List_2236
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1797 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2237 .

inst:IfcPositiveInteger_List_List_2237
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1803 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2238 .

inst:IfcPositiveInteger_List_List_2238
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1807 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2239 .

inst:IfcPositiveInteger_List_List_2239
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1813 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2240 .

inst:IfcPositiveInteger_List_List_2240
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1816 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2241 .

inst:IfcPositiveInteger_List_List_2241
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1820 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2242 .

inst:IfcPositiveInteger_List_List_2242
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1824 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2243 .

inst:IfcPositiveInteger_List_List_2243
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1827 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2244 .

inst:IfcPositiveInteger_List_List_2244
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1833 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2245 .

inst:IfcPositiveInteger_List_List_2245
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1838 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2246 .

inst:IfcPositiveInteger_List_List_2246
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1842 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2247 .

inst:IfcPositiveInteger_List_List_2247
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1845 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2248 .

inst:IfcPositiveInteger_List_List_2248
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1851 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2249 .

inst:IfcPositiveInteger_List_List_2249
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1856 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2250 .

inst:IfcPositiveInteger_List_List_2250
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1861 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2251 .

inst:IfcPositiveInteger_List_List_2251
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1864 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2252 .

inst:IfcPositiveInteger_List_List_2252
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1867 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2253 .

inst:IfcPositiveInteger_List_List_2253
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1871 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2254 .

inst:IfcPositiveInteger_List_List_2254
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1874 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2255 .

inst:IfcPositiveInteger_List_List_2255
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1878 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2256 .

inst:IfcPositiveInteger_List_List_2256
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1881 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2257 .

inst:IfcPositiveInteger_List_List_2257
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1884 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2258 .

inst:IfcPositiveInteger_List_List_2258
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1887 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2259 .

inst:IfcPositiveInteger_List_List_2259
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1890 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2260 .

inst:IfcPositiveInteger_List_List_2260
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1893 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2261 .

inst:IfcPositiveInteger_List_List_2261
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1896 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2262 .

inst:IfcPositiveInteger_List_List_2262
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1899 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2263 .

inst:IfcPositiveInteger_List_List_2263
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1902 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2264 .

inst:IfcPositiveInteger_List_List_2264
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1905 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2265 .

inst:IfcPositiveInteger_List_List_2265
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1908 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2266 .

inst:IfcPositiveInteger_List_List_2266
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1911 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2267 .

inst:IfcPositiveInteger_List_List_2267
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1914 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2268 .

inst:IfcPositiveInteger_List_List_2268
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1917 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2269 .

inst:IfcPositiveInteger_List_List_2269
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1920 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2270 .

inst:IfcPositiveInteger_List_List_2270
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1923 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2271 .

inst:IfcPositiveInteger_List_List_2271
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1926 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2272 .

inst:IfcPositiveInteger_List_List_2272
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1929 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2273 .

inst:IfcPositiveInteger_List_List_2273
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1932 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2274 .

inst:IfcPositiveInteger_List_List_2274
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1935 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2275 .

inst:IfcPositiveInteger_List_List_2275
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1938 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2276 .

inst:IfcPositiveInteger_List_List_2276
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1941 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2277 .

inst:IfcPositiveInteger_List_List_2277
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1944 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2278 .

inst:IfcPositiveInteger_List_List_2278
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1947 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2279 .

inst:IfcPositiveInteger_List_List_2279
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1950 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2280 .

inst:IfcPositiveInteger_List_List_2280
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1953 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2281 .

inst:IfcPositiveInteger_List_List_2281
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1956 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2282 .

inst:IfcPositiveInteger_List_List_2282
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1959 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2283 .

inst:IfcPositiveInteger_List_List_2283
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1962 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2284 .

inst:IfcPositiveInteger_List_List_2284
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1965 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2285 .

inst:IfcPositiveInteger_List_List_2285
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1968 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2286 .

inst:IfcPositiveInteger_List_List_2286
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1971 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2287 .

inst:IfcPositiveInteger_List_List_2287
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1974 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2288 .

inst:IfcPositiveInteger_List_List_2288
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1977 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2289 .

inst:IfcPositiveInteger_List_List_2289
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1980 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2290 .

inst:IfcPositiveInteger_List_List_2290
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1983 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2291 .

inst:IfcPositiveInteger_List_List_2291
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1986 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2292 .

inst:IfcPositiveInteger_List_List_2292
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1989 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2293 .

inst:IfcPositiveInteger_List_List_2293
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1992 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2294 .

inst:IfcPositiveInteger_List_List_2294
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1995 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2295 .

inst:IfcPositiveInteger_List_List_2295
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1998 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2296 .

inst:IfcPositiveInteger_List_List_2296
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_2001 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2297 .

inst:IfcPositiveInteger_List_List_2297
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_2004 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2298 .

inst:IfcPositiveInteger_List_List_2298
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_2007 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2299 .

inst:IfcPositiveInteger_List_List_2299
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_2010 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2300 .

inst:IfcPositiveInteger_List_List_2300
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_2013 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2301 .

inst:IfcPositiveInteger_List_List_2301
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_2016 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2302 .

inst:IfcPositiveInteger_List_List_2302
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_2019 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2303 .

inst:IfcPositiveInteger_List_List_2303
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_2023 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2304 .

inst:IfcPositiveInteger_List_List_2304
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_2026 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2305 .

inst:IfcPositiveInteger_List_List_2305
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_2029 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2306 .

inst:IfcPositiveInteger_List_List_2306
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_2032 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2307 .

inst:IfcPositiveInteger_List_List_2307
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_2035 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2308 .

inst:IfcPositiveInteger_List_List_2308
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_2038 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2309 .

inst:IfcPositiveInteger_List_List_2309
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_2041 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2310 .

inst:IfcPositiveInteger_List_List_2310
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_2044 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2311 .

inst:IfcPositiveInteger_List_List_2311
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_2047 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2312 .

inst:IfcPositiveInteger_List_List_2312
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_2050 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2313 .

inst:IfcPositiveInteger_List_List_2313
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_2053 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2314 .

inst:IfcPositiveInteger_List_List_2314
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_2056 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2315 .

inst:IfcPositiveInteger_List_List_2315
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_2059 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2316 .

inst:IfcPositiveInteger_List_List_2316
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_2062 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2317 .

inst:IfcPositiveInteger_List_List_2317
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_2065 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2318 .

inst:IfcPositiveInteger_List_List_2318
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_2068 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2319 .

inst:IfcPositiveInteger_List_List_2319
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_2071 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2320 .

inst:IfcPositiveInteger_List_List_2320
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_2074 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2321 .

inst:IfcPositiveInteger_List_List_2321
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_2077 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2322 .

inst:IfcPositiveInteger_List_List_2322
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_2080 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2323 .

inst:IfcPositiveInteger_List_List_2323
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_2083 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2324 .

inst:IfcPositiveInteger_List_List_2324
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_2086 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_2325 .

inst:IfcPositiveInteger_List_List_2325
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_2089 .

inst:IfcReal_List_2326
        rdf:type  ifc:IfcReal_List .

inst:IfcDirection_10  ifc:directionRatios_IfcDirection  inst:IfcReal_List_2326 .

inst:IfcReal_List_2327
        rdf:type  ifc:IfcReal_List .

inst:IfcReal_List_2326
        list:hasContents  inst:IfcLengthMeasure_60 ;
        list:hasNext      inst:IfcReal_List_2327 .

inst:IfcReal_2328  rdf:type  ifc:IfcReal ;
        express:hasDouble  "1.0"^^xsd:double .

inst:IfcReal_List_2327
        list:hasContents  inst:IfcReal_2328 .

inst:IfcRepresentationMap_202
        rdf:type  ifc:IfcRepresentationMap .

inst:IfcAxis2Placement3D_203
        rdf:type  ifc:IfcAxis2Placement3D .

inst:IfcRepresentationMap_202
        ifc:mappingOrigin_IfcRepresentationMap  inst:IfcAxis2Placement3D_203 .

inst:IfcShapeRepresentation_205
        rdf:type  ifc:IfcShapeRepresentation .

inst:IfcRepresentationMap_202
        ifc:mappedRepresentation_IfcRepresentationMap  inst:IfcShapeRepresentation_205 .

inst:IfcGeometricRepresentationSubContext_11
        rdf:type  ifc:IfcGeometricRepresentationSubContext .

inst:IfcLabel_2329  rdf:type  ifc:IfcLabel ;
        express:hasString  "Axis" .

inst:IfcGeometricRepresentationSubContext_11
        ifc:contextIdentifier_IfcRepresentationContext  inst:IfcLabel_2329 ;
        ifc:contextType_IfcRepresentationContext  inst:IfcLabel_52 ;
        ifc:parentContext_IfcGeometricRepresentationSubContext  inst:IfcGeometricRepresentationContext_7 ;
        ifc:targetView_IfcGeometricRepresentationSubContext  ifc:MODEL_VIEW .

inst:IfcAxis2Placement3D_203
        ifc:location_IfcPlacement  inst:IfcCartesianPoint_9 .

inst:IfcGeometricRepresentationSubContext_12
        rdf:type  ifc:IfcGeometricRepresentationSubContext .

inst:IfcLabel_2330  rdf:type  ifc:IfcLabel ;
        express:hasString  "Body" .

inst:IfcGeometricRepresentationSubContext_12
        ifc:contextIdentifier_IfcRepresentationContext  inst:IfcLabel_2330 ;
        ifc:contextType_IfcRepresentationContext  inst:IfcLabel_52 ;
        ifc:parentContext_IfcGeometricRepresentationSubContext  inst:IfcGeometricRepresentationContext_7 ;
        ifc:targetView_IfcGeometricRepresentationSubContext  ifc:MODEL_VIEW .

inst:IfcGeometricRepresentationContext_13
        rdf:type  ifc:IfcGeometricRepresentationContext ;
        ifc:contextType_IfcRepresentationContext  inst:IfcLabel_52 ;
        ifc:coordinateSpaceDimension_IfcGeometricRepresentationContext  inst:IfcDimensionCount_53 ;
        ifc:precision_IfcGeometricRepresentationContext  inst:IfcReal_54 ;
        ifc:worldCoordinateSystem_IfcGeometricRepresentationContext  inst:IfcAxis2Placement3D_8 ;
        ifc:trueNorth_IfcGeometricRepresentationContext  inst:IfcDirection_10 .

inst:IfcShapeRepresentation_205
        ifc:contextOfItems_IfcRepresentation  inst:IfcGeometricRepresentationSubContext_12 ;
        ifc:representationIdentifier_IfcRepresentation  inst:IfcLabel_2330 .

inst:IfcLabel_2331  rdf:type  ifc:IfcLabel ;
        express:hasString  "Tessellation" .

inst:IfcShapeRepresentation_205
        ifc:representationType_IfcRepresentation  inst:IfcLabel_2331 ;
        ifc:items_IfcRepresentation  inst:IfcTriangulatedFaceSet_201 .

inst:IfcMaterial_206  rdf:type  ifc:IfcMaterial .

inst:IfcLabel_2332  rdf:type  ifc:IfcLabel ;
        express:hasString  "Ceramic" .

inst:IfcMaterial_206  ifc:name_IfcMaterial  inst:IfcLabel_2332 .

inst:IfcRelAssociatesMaterial_207
        rdf:type  ifc:IfcRelAssociatesMaterial .

inst:IfcGloballyUniqueId_2333
        rdf:type           ifc:IfcGloballyUniqueId ;
        express:hasString  "1Dd05NJPHCnwujFD$zyHB4" .

inst:IfcRelAssociatesMaterial_207
        ifc:globalId_IfcRoot  inst:IfcGloballyUniqueId_2333 .

inst:IfcLabel_2334  rdf:type  ifc:IfcLabel ;
        express:hasString  "MatAssoc" .

inst:IfcRelAssociatesMaterial_207
        ifc:name_IfcRoot  inst:IfcLabel_2334 .

inst:IfcText_2335  rdf:type  ifc:IfcText ;
        express:hasString  "Material Associates" .

inst:IfcRelAssociatesMaterial_207
        ifc:description_IfcRoot  inst:IfcText_2335 .

inst:IfcSanitaryTerminalType_209
        rdf:type  ifc:IfcSanitaryTerminalType .

inst:IfcRelAssociatesMaterial_207
        ifc:relatedObjects_IfcRelAssociates  inst:IfcSanitaryTerminalType_209 ;
        ifc:relatingMaterial_IfcRelAssociatesMaterial  inst:IfcMaterial_206 .

inst:IfcGloballyUniqueId_2336
        rdf:type           ifc:IfcGloballyUniqueId ;
        express:hasString  "14smeVOBv8HRjwdm9$muyM" .

inst:IfcSanitaryTerminalType_209
        ifc:globalId_IfcRoot  inst:IfcGloballyUniqueId_2336 .

inst:IfcLabel_2337  rdf:type  ifc:IfcLabel ;
        express:hasString  "IFCSANITARYTERMINALTYPE" .

inst:IfcSanitaryTerminalType_209
        ifc:name_IfcRoot  inst:IfcLabel_2337 .

inst:IfcRepresentationMap_List_2338
        rdf:type  ifc:IfcRepresentationMap_List .

inst:IfcSanitaryTerminalType_209
        ifc:representationMaps_IfcTypeProduct  inst:IfcRepresentationMap_List_2338 .

inst:IfcRepresentationMap_List_2338
        list:hasContents  inst:IfcRepresentationMap_202 .

inst:IfcSanitaryTerminalType_209
        ifc:predefinedType_IfcSanitaryTerminalType  ifc:WASHHANDBASIN .

inst:IfcRelDefinesByType_210
        rdf:type  ifc:IfcRelDefinesByType .

inst:IfcGloballyUniqueId_2339
        rdf:type           ifc:IfcGloballyUniqueId ;
        express:hasString  "38Tc2o9wrEpw2I0HFj2zW7" .

inst:IfcRelDefinesByType_210
        ifc:globalId_IfcRoot  inst:IfcGloballyUniqueId_2339 .

inst:IfcSanitaryTerminal_217
        rdf:type  ifc:IfcSanitaryTerminal .

inst:IfcRelDefinesByType_210
        ifc:relatedObjects_IfcRelDefinesByType  inst:IfcSanitaryTerminal_217 ;
        ifc:relatingType_IfcRelDefinesByType  inst:IfcSanitaryTerminalType_209 .

inst:IfcDirection_211
        rdf:type  ifc:IfcDirection .

inst:IfcReal_List_2340
        rdf:type  ifc:IfcReal_List .

inst:IfcDirection_211
        ifc:directionRatios_IfcDirection  inst:IfcReal_List_2340 .

inst:IfcReal_List_2341
        rdf:type  ifc:IfcReal_List .

inst:IfcReal_List_2342
        rdf:type  ifc:IfcReal_List .

inst:IfcReal_List_2340
        list:hasContents  inst:IfcReal_2328 ;
        list:hasNext      inst:IfcReal_List_2341 .

inst:IfcReal_List_2341
        list:hasContents  inst:IfcLengthMeasure_60 ;
        list:hasNext      inst:IfcReal_List_2342 .

inst:IfcReal_List_2342
        list:hasContents  inst:IfcLengthMeasure_60 .

inst:IfcDirection_212
        rdf:type  ifc:IfcDirection .

inst:IfcReal_List_2343
        rdf:type  ifc:IfcReal_List .

inst:IfcDirection_212
        ifc:directionRatios_IfcDirection  inst:IfcReal_List_2343 .

inst:IfcReal_List_2344
        rdf:type  ifc:IfcReal_List .

inst:IfcReal_List_2345
        rdf:type  ifc:IfcReal_List .

inst:IfcReal_List_2343
        list:hasContents  inst:IfcLengthMeasure_60 ;
        list:hasNext      inst:IfcReal_List_2344 .

inst:IfcReal_List_2344
        list:hasContents  inst:IfcReal_2328 ;
        list:hasNext      inst:IfcReal_List_2345 .

inst:IfcReal_List_2345
        list:hasContents  inst:IfcLengthMeasure_60 .

inst:IfcCartesianTransformationOperator3D_214
        rdf:type  ifc:IfcCartesianTransformationOperator3D ;
        ifc:axis1_IfcCartesianTransformationOperator  inst:IfcDirection_211 ;
        ifc:axis2_IfcCartesianTransformationOperator  inst:IfcDirection_212 ;
        ifc:localOrigin_IfcCartesianTransformationOperator  inst:IfcCartesianPoint_9 ;
        ifc:scale_IfcCartesianTransformationOperator  inst:IfcReal_2328 .

inst:IfcDirection_215
        rdf:type  ifc:IfcDirection .

inst:IfcCartesianTransformationOperator3D_214
        ifc:axis3_IfcCartesianTransformationOperator3D  inst:IfcDirection_215 .

inst:IfcReal_List_2346
        rdf:type  ifc:IfcReal_List .

inst:IfcDirection_215
        ifc:directionRatios_IfcDirection  inst:IfcReal_List_2346 .

inst:IfcReal_List_2347
        rdf:type  ifc:IfcReal_List .

inst:IfcReal_List_2348
        rdf:type  ifc:IfcReal_List .

inst:IfcReal_List_2346
        list:hasContents  inst:IfcLengthMeasure_60 ;
        list:hasNext      inst:IfcReal_List_2347 .

inst:IfcReal_List_2347
        list:hasContents  inst:IfcLengthMeasure_60 ;
        list:hasNext      inst:IfcReal_List_2348 .

inst:IfcReal_List_2348
        list:hasContents  inst:IfcReal_2328 .

inst:IfcMappedItem_216
        rdf:type  ifc:IfcMappedItem ;
        ifc:mappingSource_IfcMappedItem  inst:IfcRepresentationMap_202 ;
        ifc:mappingTarget_IfcMappedItem  inst:IfcCartesianTransformationOperator3D_214 .

inst:IfcGloballyUniqueId_2349
        rdf:type           ifc:IfcGloballyUniqueId ;
        express:hasString  "0Zk2_ch2P32wrl1QuECi58" .

inst:IfcSanitaryTerminal_217
        ifc:globalId_IfcRoot  inst:IfcGloballyUniqueId_2349 .

inst:IfcLocalPlacement_55
        rdf:type  ifc:IfcLocalPlacement .

inst:IfcSanitaryTerminal_217
        ifc:objectPlacement_IfcProduct  inst:IfcLocalPlacement_55 .

inst:IfcProductDefinitionShape_218
        rdf:type  ifc:IfcProductDefinitionShape .

inst:IfcSanitaryTerminal_217
        ifc:representation_IfcProduct  inst:IfcProductDefinitionShape_218 ;
        ifc:predefinedType_IfcSanitaryTerminal  ifc:NOTDEFINED .

inst:IfcRepresentation_List_2350
        rdf:type  ifc:IfcRepresentation_List .

inst:IfcProductDefinitionShape_218
        ifc:representations_IfcProductRepresentation  inst:IfcRepresentation_List_2350 .

inst:IfcShapeRepresentation_219
        rdf:type  ifc:IfcShapeRepresentation .

inst:IfcRepresentation_List_2350
        list:hasContents  inst:IfcShapeRepresentation_219 .

inst:IfcShapeRepresentation_219
        ifc:contextOfItems_IfcRepresentation  inst:IfcGeometricRepresentationSubContext_12 ;
        ifc:representationIdentifier_IfcRepresentation  inst:IfcLabel_2330 .

inst:IfcLabel_2351  rdf:type  ifc:IfcLabel ;
        express:hasString  "MappedRepresentation" .

inst:IfcShapeRepresentation_219
        ifc:representationType_IfcRepresentation  inst:IfcLabel_2351 ;
        ifc:items_IfcRepresentation  inst:IfcMappedItem_216 .

inst:IfcProject_100  rdf:type  ifc:IfcProject .

inst:IfcGloballyUniqueId_2352
        rdf:type           ifc:IfcGloballyUniqueId ;
        express:hasString  "3SXUMunn9EXfAFTjVxyt84" .

inst:IfcProject_100  ifc:globalId_IfcRoot  inst:IfcGloballyUniqueId_2352 ;
        ifc:ownerHistory_IfcRoot  inst:IfcOwnerHistory_6 .

inst:IfcLabel_2353  rdf:type  ifc:IfcLabel ;
        express:hasString  "IfcProject" .

inst:IfcProject_100  ifc:name_IfcRoot  inst:IfcLabel_2353 ;
        ifc:longName_IfcContext  inst:IfcLabel_2353 .

inst:IfcLabel_2354  rdf:type  ifc:IfcLabel ;
        express:hasString  "" .

inst:IfcProject_100  ifc:phase_IfcContext  inst:IfcLabel_2354 ;
        ifc:representationContexts_IfcContext  inst:IfcGeometricRepresentationContext_13 .

inst:IfcUnitAssignment_101
        rdf:type  ifc:IfcUnitAssignment .

inst:IfcProject_100  ifc:unitsInContext_IfcContext  inst:IfcUnitAssignment_101 .

inst:IfcSIUnit_102  rdf:type  ifc:IfcSIUnit .

inst:IfcUnitAssignment_101
        ifc:units_IfcUnitAssignment  inst:IfcSIUnit_102 .

inst:IfcSIUnit_103  rdf:type  ifc:IfcSIUnit .

inst:IfcUnitAssignment_101
        ifc:units_IfcUnitAssignment  inst:IfcSIUnit_103 .

inst:IfcSIUnit_104  rdf:type  ifc:IfcSIUnit .

inst:IfcUnitAssignment_101
        ifc:units_IfcUnitAssignment  inst:IfcSIUnit_104 .

inst:IfcSIUnit_102  ifc:unitType_IfcNamedUnit  ifc:LENGTHUNIT ;
        ifc:prefix_IfcSIUnit       ifc:MILLI ;
        ifc:name_IfcSIUnit         ifc:METRE .

inst:IfcSIUnit_103  ifc:unitType_IfcNamedUnit  ifc:PLANEANGLEUNIT ;
        ifc:name_IfcSIUnit         ifc:RADIAN .

inst:IfcSIUnit_104  ifc:unitType_IfcNamedUnit  ifc:TIMEUNIT ;
        ifc:name_IfcSIUnit         ifc:SECOND .

inst:IfcRelAggregates_105
        rdf:type  ifc:IfcRelAggregates .

inst:IfcGloballyUniqueId_2355
        rdf:type           ifc:IfcGloballyUniqueId ;
        express:hasString  "2nzrR4fRv81gWFsNf1sIl$" .

inst:IfcRelAggregates_105
        ifc:globalId_IfcRoot  inst:IfcGloballyUniqueId_2355 .

inst:IfcLabel_2356  rdf:type  ifc:IfcLabel ;
        express:hasString  "Project Container" .

inst:IfcRelAggregates_105
        ifc:name_IfcRoot  inst:IfcLabel_2356 .

inst:IfcText_2357  rdf:type  ifc:IfcText ;
        express:hasString  "Project Container for Buildings" .

inst:IfcRelAggregates_105
        ifc:description_IfcRoot  inst:IfcText_2357 ;
        ifc:relatingObject_IfcRelAggregates  inst:IfcProject_100 .

inst:IfcBuilding_50  rdf:type  ifc:IfcBuilding .

inst:IfcRelAggregates_105
        ifc:relatedObjects_IfcRelAggregates  inst:IfcBuilding_50 .

inst:IfcGloballyUniqueId_2358
        rdf:type           ifc:IfcGloballyUniqueId ;
        express:hasString  "0MRYiPpfn0RRBz4hjR$d0R" .

inst:IfcBuilding_50  ifc:globalId_IfcRoot  inst:IfcGloballyUniqueId_2358 .

inst:IfcLabel_2359  rdf:type  ifc:IfcLabel ;
        express:hasString  "IfcBuilding" .

inst:IfcBuilding_50  ifc:name_IfcRoot  inst:IfcLabel_2359 .

inst:IfcLocalPlacement_51
        rdf:type  ifc:IfcLocalPlacement .

inst:IfcBuilding_50  ifc:objectPlacement_IfcProduct  inst:IfcLocalPlacement_51 ;
        ifc:compositionType_IfcSpatialStructureElement  ifc:ELEMENT .

inst:IfcPostalAddress_57
        rdf:type  ifc:IfcPostalAddress .

inst:IfcBuilding_50  ifc:buildingAddress_IfcBuilding  inst:IfcPostalAddress_57 .

inst:IfcAxis2Placement3D_52
        rdf:type  ifc:IfcAxis2Placement3D .

inst:IfcLocalPlacement_51
        ifc:relativePlacement_IfcLocalPlacement  inst:IfcAxis2Placement3D_52 .

inst:IfcAxis2Placement3D_52
        ifc:location_IfcPlacement  inst:IfcCartesianPoint_9 .

inst:IfcRelContainedInSpatialStructure_54
        rdf:type  ifc:IfcRelContainedInSpatialStructure .

inst:IfcGloballyUniqueId_2360
        rdf:type           ifc:IfcGloballyUniqueId ;
        express:hasString  "2YVMFu6$54ggBelxInCL1O" .

inst:IfcRelContainedInSpatialStructure_54
        ifc:globalId_IfcRoot  inst:IfcGloballyUniqueId_2360 .

inst:IfcLabel_2361  rdf:type  ifc:IfcLabel ;
        express:hasString  "Building" .

inst:IfcRelContainedInSpatialStructure_54
        ifc:name_IfcRoot  inst:IfcLabel_2361 .

inst:IfcText_2362  rdf:type  ifc:IfcText ;
        express:hasString  "Building Container for Elements" .

inst:IfcRelContainedInSpatialStructure_54
        ifc:description_IfcRoot  inst:IfcText_2362 ;
        ifc:relatedElements_IfcRelContainedInSpatialStructure  inst:IfcSanitaryTerminal_217 ;
        ifc:relatingStructure_IfcRelContainedInSpatialStructure  inst:IfcBuilding_50 .

inst:IfcLocalPlacement_55
        ifc:placementRelTo_IfcLocalPlacement  inst:IfcLocalPlacement_51 ;
        ifc:relativePlacement_IfcLocalPlacement  inst:IfcAxis2Placement3D_52 .

inst:IfcLabel_2363  rdf:type  ifc:IfcLabel ;
        express:hasString  "Unknown" .

inst:IfcPostalAddress_57
        ifc:region_IfcPostalAddress  inst:IfcLabel_2363 .

ISO-10303-21;
HEADER;
FILE_DESCRIPTION(('ViewDefinition [notYetAssigned]'),'2;1');
FILE_NAME(
/* name */ '',
/* time_stamp */ '2014-12-09T00:27:54',
/* author */ ('Jon'),
/* organization */ ('Unknown'),
/* preprocessor_version */ 'ggIFC - Exporter by Geometry Gym Pty Ltd',
/* originating_system */ 'ggIFC - Exporter by Geometry Gym Pty Ltd',
/* authorization */ 'None');

FILE_SCHEMA (('IFC4'));
ENDSEC;

DATA;
#1= IFCAPPLICATION(#2,'*******','ggRhinoIFC - Geometry Gym Plug-in for Rhino3d','ggRhinoIFC');
#2= IFCORGANIZATION($,'Geometry Gym Pty Ltd',$,$,$);
#3= IFCPERSONANDORGANIZATION(#4,#5,$);
#4= IFCPERSON('Jon','Jon',$,$,$,$,$,$);
#5= IFCORGANIZATION($,'Geometry Gym Pty Ltd',$,$,$);
#6= IFCOWNERHISTORY(#3,#1,$,.ADDED.,1418084874,$,$,1418084874);
#7= IFCGEOMETRICREPRESENTATIONCONTEXT($,'Model',3,0.0001,#8,#10);
#8= IFCAXIS2PLACEMENT3D(#9,$,$);
#9= IFCCARTESIANPOINT((0.0,0.0,0.0));
#10= IFCDIRECTION((0.0,1.0));
#11= IFCGEOMETRICREPRESENTATIONSUBCONTEXT('Axis','Model',*,*,*,*,#7,$,.MODEL_VIEW.,$);
#12= IFCGEOMETRICREPRESENTATIONSUBCONTEXT('Body','Model',*,*,*,*,#7,$,.MODEL_VIEW.,$);
#13= IFCGEOMETRICREPRESENTATIONCONTEXT($,'Model',3,0.0001,#8,#14);
#14= IFCDIRECTION((0.0,1.0));
#50= IFCBUILDING('0x9l_UUnnDiwi3RBxSvXhq',$,'IfcBuilding',$,$,#51,$,$,.ELEMENT.,$,$,#57);
#51= IFCLOCALPLACEMENT($,#52);
#52= IFCAXIS2PLACEMENT3D(#53,$,$);
#53= IFCCARTESIANPOINT((0.0,0.0,0.0));
#54= IFCRELCONTAINEDINSPATIALSTRUCTURE('2TNzCy3Wb2mPn9JP_Wlh0z',$,'Building','Building Container for Elements',(#210,#230,#250,#270),#50);
#57= IFCPOSTALADDRESS($,$,$,$,$,$,$,'Unknown',$,$);
#100= IFCPROJECT('01S2tl1M58lxKZa_6Xi$RG',#6,'IfcProject',$,$,'IfcProject','',(#13),#101);
#101= IFCUNITASSIGNMENT((#102,#103,#104));
#102= IFCSIUNIT(*,.LENGTHUNIT.,.MILLI.,.METRE.);
#103= IFCSIUNIT(*,.PLANEANGLEUNIT.,$,.RADIAN.);
#104= IFCSIUNIT(*,.TIMEUNIT.,$,.SECOND.);
#105= IFCRELAGGREGATES('0JxzMHjzjElv1Rcedc$qQy',$,'Project Container','Project Container for Buildings',#100,(#50));
#200= IFCMATERIAL('S355JR',$,'Steel');
#203= IFCISHAPEPROFILEDEF(.AREA.,'IPE200',$,100.0,200.0,5.6,8.5,12.0,$,$);
#204= IFCMATERIALPROFILE('IPE200',$,#200,#203,0,$);
#206= IFCMATERIALPROFILESET('IPE200',$,(#204),$);
#207= IFCRELASSOCIATESMATERIAL('3tuOfWWPXCowjxj5EzL5yC',$,'MatAssoc','Material Associates',(#208),#206);
#208= IFCBEAMTYPE('0CIULE5oD6pubBJ1yIg7CJ',$,'IPE200',$,$,$,$,$,$,.JOIST.);
#209= IFCRELDEFINESBYTYPE('08Z_7XC_5CNR$ILx3RG0wV',$,'IPE200',$,(#210,#230,#250,#270),#208);
#210= IFCBEAMSTANDARDCASE('06uVDzM0j8rugiazVexGkx',$,'TopMid',$,$,#211,#229,$,$);
#211= IFCLOCALPLACEMENT($,#212);
#212= IFCAXIS2PLACEMENT3D(#213,#214,#215);
#213= IFCCARTESIANPOINT((0.0,0.0,0.0));
#214= IFCDIRECTION((0.0,1.0,0.0));
#215= IFCDIRECTION((-1.0,0.0,0.0));
#216= IFCMATERIALPROFILESETUSAGE(#206,8,$);
#217= IFCRELASSOCIATESMATERIAL('2B1DG40M5EQ8GNtf0Kj3CU',$,'MatAssoc','Material Associates',(#210),#216);
#218= IFCCARTESIANPOINT((0.0,0.0,0.0));
#219= IFCCARTESIANPOINT((0.0,0.0,1000.0));
#220= IFCPOLYLINE((#218,#219));
#221= IFCSHAPEREPRESENTATION(#11,'Axis','Curve3D',(#220));
#222= IFCDIRECTION((0.0,0.0,1.0));
#223= IFCEXTRUDEDAREASOLID(#203,#224,#222,1000.0);
#224= IFCAXIS2PLACEMENT3D(#225,#226,#227);
#225= IFCCARTESIANPOINT((0.0,-100.0,0.0));
#226= IFCDIRECTION((0.0,0.0,1.0));
#227= IFCDIRECTION((1.0,0.0,0.0));
#228= IFCSHAPEREPRESENTATION(#12,'Body','SweptSolid',(#223));
#229= IFCPRODUCTDEFINITIONSHAPE($,$,(#221,#228));
#230= IFCBEAMSTANDARDCASE('3Cyaczln1DyfCVyfoF1Fyl',$,'BotMid',$,$,#231,#249,$,$);
#231= IFCLOCALPLACEMENT($,#232);
#232= IFCAXIS2PLACEMENT3D(#233,#234,#235);
#233= IFCCARTESIANPOINT((0.0,0.0,0.0));
#234= IFCDIRECTION((0.0,1.0,0.0));
#235= IFCDIRECTION((-1.0,0.0,0.0));
#236= IFCMATERIALPROFILESETUSAGE(#206,2,$);
#237= IFCRELASSOCIATESMATERIAL('1x_9tO_r59EfAtzZhnrMq1',$,'MatAssoc','Material Associates',(#230),#236);
#238= IFCCARTESIANPOINT((0.0,0.0,0.0));
#239= IFCCARTESIANPOINT((0.0,0.0,1000.0));
#240= IFCPOLYLINE((#238,#239));
#241= IFCSHAPEREPRESENTATION(#11,'Axis','Curve3D',(#240));
#242= IFCDIRECTION((0.0,0.0,1.0));
#243= IFCEXTRUDEDAREASOLID(#203,#244,#242,1000.0);
#244= IFCAXIS2PLACEMENT3D(#245,#246,#247);
#245= IFCCARTESIANPOINT((0.0,100.0,0.0));
#246= IFCDIRECTION((0.0,0.0,1.0));
#247= IFCDIRECTION((1.0,0.0,0.0));
#248= IFCSHAPEREPRESENTATION(#12,'Body','SweptSolid',(#243));
#249= IFCPRODUCTDEFINITIONSHAPE($,$,(#241,#248));
#250= IFCBEAMSTANDARDCASE('3_uWum$4vBjQ8_adQlTox1',$,'BotLeft',$,$,#251,#269,$,$);
#251= IFCLOCALPLACEMENT($,#252);
#252= IFCAXIS2PLACEMENT3D(#253,#254,#255);
#253= IFCCARTESIANPOINT((500.0,0.0,0.0));
#254= IFCDIRECTION((0.0,1.0,0.0));
#255= IFCDIRECTION((-1.0,0.0,0.0));
#256= IFCMATERIALPROFILESETUSAGE(#206,1,$);
#257= IFCRELASSOCIATESMATERIAL('0L7uiI$4LBd9kW2io0EpWt',$,'MatAssoc','Material Associates',(#250),#256);
#258= IFCCARTESIANPOINT((0.0,0.0,0.0));
#259= IFCCARTESIANPOINT((0.0,0.0,1000.0));
#260= IFCPOLYLINE((#258,#259));
#261= IFCSHAPEREPRESENTATION(#11,'Axis','Curve3D',(#260));
#262= IFCDIRECTION((0.0,0.0,1.0));
#263= IFCEXTRUDEDAREASOLID(#203,#264,#262,1000.0);
#264= IFCAXIS2PLACEMENT3D(#265,#266,#267);
#265= IFCCARTESIANPOINT((-50.0,100.0,0.0));
#266= IFCDIRECTION((0.0,0.0,1.0));
#267= IFCDIRECTION((1.0,0.0,0.0));
#268= IFCSHAPEREPRESENTATION(#12,'Body','SweptSolid',(#263));
#269= IFCPRODUCTDEFINITIONSHAPE($,$,(#261,#268));
#270= IFCBEAMSTANDARDCASE('3QKYtgM_1EtekuZgGn_XdM',$,'TopRight',$,$,#271,#289,$,$);
#271= IFCLOCALPLACEMENT($,#272);
#272= IFCAXIS2PLACEMENT3D(#273,#274,#275);
#273= IFCCARTESIANPOINT((500.0,0.0,0.0));
#274= IFCDIRECTION((0.0,1.0,0.0));
#275= IFCDIRECTION((-1.0,0.0,0.0));
#276= IFCMATERIALPROFILESETUSAGE(#206,9,$);
#277= IFCRELASSOCIATESMATERIAL('3g$0sYwCT7XOlrNjVbpWgs',$,'MatAssoc','Material Associates',(#270),#276);
#278= IFCCARTESIANPOINT((0.0,0.0,0.0));
#279= IFCCARTESIANPOINT((0.0,0.0,1000.0));
#280= IFCPOLYLINE((#278,#279));
#281= IFCSHAPEREPRESENTATION(#11,'Axis','Curve3D',(#280));
#282= IFCDIRECTION((0.0,0.0,1.0));
#283= IFCEXTRUDEDAREASOLID(#203,#284,#282,1000.0);
#284= IFCAXIS2PLACEMENT3D(#285,#286,#287);
#285= IFCCARTESIANPOINT((50.0,-100.0,0.0));
#286= IFCDIRECTION((0.0,0.0,1.0));
#287= IFCDIRECTION((1.0,0.0,0.0));
#288= IFCSHAPEREPRESENTATION(#12,'Body','SweptSolid',(#283));
#289= IFCPRODUCTDEFINITIONSHAPE($,$,(#281,#288));
ENDSEC;

END-ISO-10303-21;


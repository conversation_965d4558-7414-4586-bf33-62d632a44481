ISO-10303-21;
HEADER;
FILE_DESCRIPTION(('ViewDefinition [CoordinationView]'),'2;1');
FILE_NAME('','2020-10-30T00:18:56',(),(),'IfcOpenShell 0.6.0b0','IfcOpenShell 0.6.0b0','');
FILE_SCHEMA(('IFC4'));
ENDSEC;
DATA;
#1=IFCBUILDING('0x9l_UUnnDiwi3RBxSvXhq',$,'IfcBuilding',$,$,#29,$,$,.ELEMENT.,$,$,#32);
#2=IFCPROJECT('01S2tl1M58lxKZa_6Xi$RG',#16,'IfcProject',$,$,'IfcProject',$,(#20),#33);
#3=IFCBEAMTYPE('0CIULE5oD6pubBJ1yIg7CJ',$,'IPE200',$,$,$,$,$,$,.JOIST.);
#4=IFCBEAMSTANDARDCASE('06uVDzM0j8rugiazVexGkx',$,'TopMid',$,$,#37,#42,$,$);
#5=IFCBEAMSTANDARDCASE('3Cyaczln1DyfCVyfoF1Fyl',$,'BotMid',$,$,#43,#48,$,$);
#6=IFCBEAMSTANDARDCASE('3_uWum$4vBjQ8_adQlTox1',$,'BotLeft',$,$,#49,#54,$,$);
#7=IFCBEAMSTANDARDCASE('3QKYtgM_1EtekuZgGn_XdM',$,'TopRight',$,$,#55,#60,$,$);
#8=IFCSHAPEREPRESENTATION(#18,'Axis','Curve3D',(#61));
#9=IFCSHAPEREPRESENTATION(#19,'Body','SweptSolid',(#64));
#10=IFCSHAPEREPRESENTATION(#18,'Axis','Curve3D',(#71));
#11=IFCSHAPEREPRESENTATION(#19,'Body','SweptSolid',(#74));
#12=IFCSHAPEREPRESENTATION(#18,'Axis','Curve3D',(#81));
#13=IFCSHAPEREPRESENTATION(#19,'Body','SweptSolid',(#84));
#14=IFCSHAPEREPRESENTATION(#18,'Axis','Curve3D',(#91));
#15=IFCSHAPEREPRESENTATION(#19,'Body','SweptSolid',(#94));
#16=IFCOWNERHISTORY(#101,#104,$,.ADDED.,1418084874,$,$,1418084874);
#17=IFCGEOMETRICREPRESENTATIONCONTEXT($,'Model',3,0.0001,#106,#108);
#18=IFCGEOMETRICREPRESENTATIONSUBCONTEXT('Axis','Model',*,*,*,*,#17,$,.MODEL_VIEW.,$);
#19=IFCGEOMETRICREPRESENTATIONSUBCONTEXT('Body','Model',*,*,*,*,#17,$,.MODEL_VIEW.,$);
#20=IFCGEOMETRICREPRESENTATIONCONTEXT($,'Model',3,0.0001,#109,#111);
#21=IFCRELCONTAINEDINSPATIALSTRUCTURE('2TNzCy3Wb2mPn9JP_Wlh0z',$,'Building','Building Container for Elements',(#4,#5,#6,#7),#1);
#22=IFCRELAGGREGATES('0JxzMHjzjElv1Rcedc$qQy',$,'Project Container','Project Container for Buildings',#2,(#1));
#23=IFCRELASSOCIATESMATERIAL('3tuOfWWPXCowjxj5EzL5yC',$,'MatAssoc','Material Associates',(#3),#112);
#24=IFCRELDEFINESBYTYPE('08Z_7XC_5CNR$ILx3RG0wV',$,'IPE200',$,(#4,#5,#6,#7),#3);
#25=IFCRELASSOCIATESMATERIAL('2B1DG40M5EQ8GNtf0Kj3CU',$,'MatAssoc','Material Associates',(#4),#116);
#26=IFCRELASSOCIATESMATERIAL('1x_9tO_r59EfAtzZhnrMq1',$,'MatAssoc','Material Associates',(#5),#121);
#27=IFCRELASSOCIATESMATERIAL('0L7uiI$4LBd9kW2io0EpWt',$,'MatAssoc','Material Associates',(#6),#126);
#28=IFCRELASSOCIATESMATERIAL('3g$0sYwCT7XOlrNjVbpWgs',$,'MatAssoc','Material Associates',(#7),#131);
#29=IFCLOCALPLACEMENT($,#30);
#30=IFCAXIS2PLACEMENT3D(#31,$,$);
#31=IFCCARTESIANPOINT((0.,0.,0.));
#32=IFCPOSTALADDRESS($,$,$,$,$,$,$,'Unknown',$,$);
#33=IFCUNITASSIGNMENT((#34,#35,#36));
#34=IFCSIUNIT(*,.LENGTHUNIT.,.MILLI.,.METRE.);
#35=IFCSIUNIT(*,.PLANEANGLEUNIT.,$,.RADIAN.);
#36=IFCSIUNIT(*,.TIMEUNIT.,$,.SECOND.);
#37=IFCLOCALPLACEMENT($,#38);
#38=IFCAXIS2PLACEMENT3D(#39,#40,#41);
#39=IFCCARTESIANPOINT((0.,0.,0.));
#40=IFCDIRECTION((0.,1.,0.));
#41=IFCDIRECTION((-1.,0.,0.));
#42=IFCPRODUCTDEFINITIONSHAPE($,$,(#8,#9));
#43=IFCLOCALPLACEMENT($,#44);
#44=IFCAXIS2PLACEMENT3D(#45,#46,#47);
#45=IFCCARTESIANPOINT((0.,0.,0.));
#46=IFCDIRECTION((0.,1.,0.));
#47=IFCDIRECTION((-1.,0.,0.));
#48=IFCPRODUCTDEFINITIONSHAPE($,$,(#10,#11));
#49=IFCLOCALPLACEMENT($,#50);
#50=IFCAXIS2PLACEMENT3D(#51,#52,#53);
#51=IFCCARTESIANPOINT((500.,0.,0.));
#52=IFCDIRECTION((0.,1.,0.));
#53=IFCDIRECTION((-1.,0.,0.));
#54=IFCPRODUCTDEFINITIONSHAPE($,$,(#12,#13));
#55=IFCLOCALPLACEMENT($,#56);
#56=IFCAXIS2PLACEMENT3D(#57,#58,#59);
#57=IFCCARTESIANPOINT((500.,0.,0.));
#58=IFCDIRECTION((0.,1.,0.));
#59=IFCDIRECTION((-1.,0.,0.));
#60=IFCPRODUCTDEFINITIONSHAPE($,$,(#14,#15));
#61=IFCPOLYLINE((#62,#63));
#62=IFCCARTESIANPOINT((0.,0.,0.));
#63=IFCCARTESIANPOINT((0.,0.,1000.));
#64=IFCEXTRUDEDAREASOLID(#65,#66,#70,1000.);
#65=IFCISHAPEPROFILEDEF(.AREA.,'IPE200',$,100.,200.,5.6,8.5,12.,$,$);
#66=IFCAXIS2PLACEMENT3D(#67,#68,#69);
#67=IFCCARTESIANPOINT((0.,-100.,0.));
#68=IFCDIRECTION((0.,0.,1.));
#69=IFCDIRECTION((1.,0.,0.));
#70=IFCDIRECTION((0.,0.,1.));
#71=IFCPOLYLINE((#72,#73));
#72=IFCCARTESIANPOINT((0.,0.,0.));
#73=IFCCARTESIANPOINT((0.,0.,1000.));
#74=IFCEXTRUDEDAREASOLID(#75,#76,#80,1000.);
#75=IFCISHAPEPROFILEDEF(.AREA.,'IPE200',$,100.,200.,5.6,8.5,12.,$,$);
#76=IFCAXIS2PLACEMENT3D(#77,#78,#79);
#77=IFCCARTESIANPOINT((0.,100.,0.));
#78=IFCDIRECTION((0.,0.,1.));
#79=IFCDIRECTION((1.,0.,0.));
#80=IFCDIRECTION((0.,0.,1.));
#81=IFCPOLYLINE((#82,#83));
#82=IFCCARTESIANPOINT((0.,0.,0.));
#83=IFCCARTESIANPOINT((0.,0.,1000.));
#84=IFCEXTRUDEDAREASOLID(#85,#86,#90,1000.);
#85=IFCISHAPEPROFILEDEF(.AREA.,'IPE200',$,100.,200.,5.6,8.5,12.,$,$);
#86=IFCAXIS2PLACEMENT3D(#87,#88,#89);
#87=IFCCARTESIANPOINT((-50.,100.,0.));
#88=IFCDIRECTION((0.,0.,1.));
#89=IFCDIRECTION((1.,0.,0.));
#90=IFCDIRECTION((0.,0.,1.));
#91=IFCPOLYLINE((#92,#93));
#92=IFCCARTESIANPOINT((0.,0.,0.));
#93=IFCCARTESIANPOINT((0.,0.,1000.));
#94=IFCEXTRUDEDAREASOLID(#95,#96,#100,1000.);
#95=IFCISHAPEPROFILEDEF(.AREA.,'IPE200',$,100.,200.,5.6,8.5,12.,$,$);
#96=IFCAXIS2PLACEMENT3D(#97,#98,#99);
#97=IFCCARTESIANPOINT((50.,-100.,0.));
#98=IFCDIRECTION((0.,0.,1.));
#99=IFCDIRECTION((1.,0.,0.));
#100=IFCDIRECTION((0.,0.,1.));
#101=IFCPERSONANDORGANIZATION(#102,#103,$);
#102=IFCPERSON('Jon','Jon',$,$,$,$,$,$);
#103=IFCORGANIZATION($,'Geometry Gym Pty Ltd',$,$,$);
#104=IFCAPPLICATION(#105,'*******','ggRhinoIFC - Geometry Gym Plug-in for Rhino3d','ggRhinoIFC');
#105=IFCORGANIZATION($,'Geometry Gym Pty Ltd',$,$,$);
#106=IFCAXIS2PLACEMENT3D(#107,$,$);
#107=IFCCARTESIANPOINT((0.,0.,0.));
#108=IFCDIRECTION((0.,1.));
#109=IFCAXIS2PLACEMENT3D(#110,$,$);
#110=IFCCARTESIANPOINT((0.,0.,0.));
#111=IFCDIRECTION((0.,1.));
#112=IFCMATERIALPROFILESET('IPE200',$,(#113),$);
#113=IFCMATERIALPROFILE('IPE200',$,#114,#115,0,$);
#114=IFCMATERIAL('S355JR',$,'Steel');
#115=IFCISHAPEPROFILEDEF(.AREA.,'IPE200',$,100.,200.,5.6,8.5,12.,$,$);
#116=IFCMATERIALPROFILESETUSAGE(#117,8,$);
#117=IFCMATERIALPROFILESET('IPE200',$,(#118),$);
#118=IFCMATERIALPROFILE('IPE200',$,#119,#120,0,$);
#119=IFCMATERIAL('S355JR',$,'Steel');
#120=IFCISHAPEPROFILEDEF(.AREA.,'IPE200',$,100.,200.,5.6,8.5,12.,$,$);
#121=IFCMATERIALPROFILESETUSAGE(#122,2,$);
#122=IFCMATERIALPROFILESET('IPE200',$,(#123),$);
#123=IFCMATERIALPROFILE('IPE200',$,#124,#125,0,$);
#124=IFCMATERIAL('S355JR',$,'Steel');
#125=IFCISHAPEPROFILEDEF(.AREA.,'IPE200',$,100.,200.,5.6,8.5,12.,$,$);
#126=IFCMATERIALPROFILESETUSAGE(#127,1,$);
#127=IFCMATERIALPROFILESET('IPE200',$,(#128),$);
#128=IFCMATERIALPROFILE('IPE200',$,#129,#130,0,$);
#129=IFCMATERIAL('S355JR',$,'Steel');
#130=IFCISHAPEPROFILEDEF(.AREA.,'IPE200',$,100.,200.,5.6,8.5,12.,$,$);
#131=IFCMATERIALPROFILESETUSAGE(#132,9,$);
#132=IFCMATERIALPROFILESET('IPE200',$,(#133),$);
#133=IFCMATERIALPROFILE('IPE200',$,#134,#135,0,$);
#134=IFCMATERIAL('S355JR',$,'Steel');
#135=IFCISHAPEPROFILEDEF(.AREA.,'IPE200',$,100.,200.,5.6,8.5,12.,$,$);
ENDSEC;
END-ISO-10303-21;

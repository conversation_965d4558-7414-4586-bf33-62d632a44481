ISO-10303-21;
HEADER;
FILE_DESCRIPTION(('ViewDefinition [notYetAssigned]'),'2;1');
FILE_NAME(
/* name */ '',
/* time_stamp */ '2014-12-09T00:27:54',
/* author */ ('Jon'),
/* organization */ ('Unknown'),
/* preprocessor_version */ 'ggIFC - Exporter by Geometry Gym Pty Ltd',
/* originating_system */ 'ggIFC - Exporter by Geometry Gym Pty Ltd',
/* authorization */ 'None');

FILE_SCHEMA (('IFC4'));
ENDSEC;

DATA;
#1= IFCAPPLICATION(#2,'*******','ggRhinoIFC - Geometry Gym Plug-in for Rhino3d','ggRhinoIFC');
#2= IFCORGANIZATION($,'Geometry Gym Pty Ltd',$,$,$);
#3= IFCPERSONANDORGANIZATION(#4,#5,$);
#4= IFCPERSON('Jon','Jon',$,$,$,$,$,$);
#5= IFCORGANIZATION($,'Geometry Gym Pty Ltd',$,$,$);
#6= IFCOWNERHISTORY(#3,#1,$,.ADDED.,1418084874,$,$,1418084874);
#7= IFCGEOMETRICREPRESENTATIONCONTEXT($,'Model',3,0.0001,#8,#10);
#8= IFCAXIS2PLACEMENT3D(#9,$,$);
#9= IFCCARTESIANPOINT((0.0,0.0,0.0));
#10= IFCDIRECTION((0.0,1.0));
#11= IFCGEOMETRICREPRESENTATIONSUBCONTEXT('Axis','Model',*,*,*,*,#7,$,.MODEL_VIEW.,$);
#12= IFCGEOMETRICREPRESENTATIONSUBCONTEXT('Body','Model',*,*,*,*,#7,$,.MODEL_VIEW.,$);
#13= IFCGEOMETRICREPRESENTATIONCONTEXT($,'Model',3,0.0001,#8,#14);
#14= IFCDIRECTION((0.0,1.0));
#50= IFCBUILDING('2fr36l6aD6rf9i5KQVhFP$',$,'IfcBuilding',$,$,#51,$,$,.ELEMENT.,$,$,#57);
#51= IFCLOCALPLACEMENT($,#52);
#52= IFCAXIS2PLACEMENT3D(#53,$,$);
#53= IFCCARTESIANPOINT((0.0,0.0,0.0));
#54= IFCRELCONTAINEDINSPATIALSTRUCTURE('2i4NqXGiL3fQ9ioBHEX1Rx',$,'Building','Building Container for Elements',(#210,#230),#50);
#57= IFCPOSTALADDRESS($,$,$,$,$,$,$,'Unknown',$,$);
#100= IFCPROJECT('0lTuVttILD_gRqdvE4G9g0',#6,'IfcProject',$,$,'IfcProject','',(#13),#101);
#101= IFCUNITASSIGNMENT((#102,#103,#104));
#102= IFCSIUNIT(*,.LENGTHUNIT.,.MILLI.,.METRE.);
#103= IFCSIUNIT(*,.PLANEANGLEUNIT.,$,.RADIAN.);
#104= IFCSIUNIT(*,.TIMEUNIT.,$,.SECOND.);
#105= IFCRELAGGREGATES('051hHDrYz458hyqR7V$$rq',$,'Project Container','Project Container for Buildings',#100,(#50));
#200= IFCMATERIAL('S355JR',$,'Steel');
#203= IFCISHAPEPROFILEDEF(.AREA.,'IPE200',$,100.0,200.0,5.6,8.5,12.0,$,$);
#204= IFCMATERIALPROFILE('IPE200',$,#200,#203,0,$);
#206= IFCMATERIALPROFILESET('IPE200',$,(#204),$);
#207= IFCRELASSOCIATESMATERIAL('0tEvU5UX92ZuHXO6nbEM8T',$,'MatAssoc','Material Associates',(#208),#206);
#208= IFCBEAMTYPE('1HK8qWFyv34QkkYiOCeN0I',$,'IPE200',$,$,$,$,$,$,.JOIST.);
#209= IFCRELDEFINESBYTYPE('1oHwFqo1XCjwoGt$KB$Lzm',$,'IPE200',$,(#210,#230),#208);
#210= IFCBEAMSTANDARDCASE('00CM__ZyPDwgWTGJSh5i9o',$,'Extrusion',$,$,#211,#229,$,$);
#211= IFCLOCALPLACEMENT($,#212);
#212= IFCAXIS2PLACEMENT3D(#213,#214,#215);
#213= IFCCARTESIANPOINT((0.0,0.0,0.0));
#214= IFCDIRECTION((0.0,1.0,0.0));
#215= IFCDIRECTION((-1.0,0.0,0.0));
#216= IFCMATERIALPROFILESETUSAGE(#206,8,$);
#217= IFCRELASSOCIATESMATERIAL('3O4J6OQF1C9gIjgzWdmsw5',$,'MatAssoc','Material Associates',(#210,#230),#216);
#218= IFCCARTESIANPOINT((0.0,0.0,0.0));
#219= IFCCARTESIANPOINT((0.0,0.0,1000.0));
#220= IFCPOLYLINE((#218,#219));
#221= IFCSHAPEREPRESENTATION(#11,'Axis','Curve3D',(#220));
#222= IFCDIRECTION((0.0,0.0,1.0));
#223= IFCEXTRUDEDAREASOLID(#203,#224,#222,1000.0);
#224= IFCAXIS2PLACEMENT3D(#225,#226,#227);
#225= IFCCARTESIANPOINT((0.0,-100.0,0.0));
#226= IFCDIRECTION((0.0,0.0,1.0));
#227= IFCDIRECTION((1.0,0.0,0.0));
#228= IFCSHAPEREPRESENTATION(#12,'Body','SweptSolid',(#223));
#229= IFCPRODUCTDEFINITIONSHAPE($,$,(#221,#228));
#230= IFCBEAMSTANDARDCASE('3HvTzTCuD1eO$PIsKnJzHR',$,'Revolution',$,$,#231,#254,$,$);
#231= IFCLOCALPLACEMENT($,#232);
#232= IFCAXIS2PLACEMENT3D(#233,#234,#235);
#233= IFCCARTESIANPOINT((0.0,0.0,0.0));
#234= IFCDIRECTION((-0.38461538,0.92307692,0.0));
#235= IFCDIRECTION((-0.92307692,-0.38461538,0.0));
#236= IFCTRIMMEDCURVE(#243,(IFCPARAMETERVALUE(0.0),#237),(IFCPARAMETERVALUE(0.789582239399523),#238),.T.,.PARAMETER.);
#237= IFCCARTESIANPOINT((0.0,0.0,0.0));
#238= IFCCARTESIANPOINT((-384.615384615385,0.0,923.076923076923));
#239= IFCAXIS2PLACEMENT3D(#240,#241,#242);
#240= IFCCARTESIANPOINT((-1300.0,0.0,0.0));
#241= IFCDIRECTION((0.0,-1.0,0.0));
#242= IFCDIRECTION((1.0,0.0,0.0));
#243= IFCCIRCLE(#239,1300.0);
#244= IFCSHAPEREPRESENTATION(#11,'Axis','Curve3D',(#236));
#245= IFCREVOLVEDAREASOLID(#203,#246,#250,0.789582239399523);
#246= IFCAXIS2PLACEMENT3D(#247,#248,#249);
#247= IFCCARTESIANPOINT((0.0,-100.0,0.0));
#248= IFCDIRECTION((0.0,0.0,1.0));
#249= IFCDIRECTION((1.0,0.0,0.0));
#250= IFCAXIS1PLACEMENT(#251,#252);
#251= IFCCARTESIANPOINT((-1300.0,100.0,0.0));
#252= IFCDIRECTION((0.0,-1.0,0.0));
#253= IFCSHAPEREPRESENTATION(#12,'Body','SweptSolid',(#245));
#254= IFCPRODUCTDEFINITIONSHAPE($,$,(#244,#253));
ENDSEC;

END-ISO-10303-21;


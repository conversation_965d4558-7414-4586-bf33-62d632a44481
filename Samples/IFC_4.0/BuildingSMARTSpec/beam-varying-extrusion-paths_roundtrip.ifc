ISO-10303-21;
HEADER;
FILE_DESCRIPTION(('ViewDefinition [CoordinationView]'),'2;1');
FILE_NAME('','2020-10-30T00:18:56',(),(),'IfcOpenShell 0.6.0b0','IfcOpenShell 0.6.0b0','');
FILE_SCHEMA(('IFC4'));
ENDSEC;
DATA;
#1=IFCBUILDING('2fr36l6aD6rf9i5KQVhFP$',$,'IfcBuilding',$,$,#20,$,$,.ELEMENT.,$,$,#23);
#2=IFCPROJECT('0lTuVttILD_gRqdvE4G9g0',#10,'IfcProject',$,$,'IfcProject',$,(#14),#24);
#3=IFCBEAMTYPE('1HK8qWFyv34QkkYiOCeN0I',$,'IPE200',$,$,$,$,$,$,.JOIST.);
#4=IFCBEAMSTANDARDCASE('00CM__ZyPDwgWTGJSh5i9o',$,'Extrusion',$,$,#28,#33,$,$);
#5=IFCBEAMSTANDARDCASE('3HvTzTCuD1eO$PIsKnJzHR',$,'Revolution',$,$,#34,#39,$,$);
#6=IFCSHAPEREPRESENTATION(#12,'Axis','Curve3D',(#40));
#7=IFCSHAPEREPRESENTATION(#13,'Body','SweptSolid',(#43));
#8=IFCSHAPEREPRESENTATION(#12,'Axis','Curve3D',(#50));
#9=IFCSHAPEREPRESENTATION(#13,'Body','SweptSolid',(#58));
#10=IFCOWNERHISTORY(#67,#70,$,.ADDED.,1418084874,$,$,1418084874);
#11=IFCGEOMETRICREPRESENTATIONCONTEXT($,'Model',3,0.0001,#72,#74);
#12=IFCGEOMETRICREPRESENTATIONSUBCONTEXT('Axis','Model',*,*,*,*,#11,$,.MODEL_VIEW.,$);
#13=IFCGEOMETRICREPRESENTATIONSUBCONTEXT('Body','Model',*,*,*,*,#11,$,.MODEL_VIEW.,$);
#14=IFCGEOMETRICREPRESENTATIONCONTEXT($,'Model',3,0.0001,#75,#77);
#15=IFCRELCONTAINEDINSPATIALSTRUCTURE('2i4NqXGiL3fQ9ioBHEX1Rx',$,'Building','Building Container for Elements',(#4,#5),#1);
#16=IFCRELAGGREGATES('051hHDrYz458hyqR7V$$rq',$,'Project Container','Project Container for Buildings',#2,(#1));
#17=IFCRELASSOCIATESMATERIAL('0tEvU5UX92ZuHXO6nbEM8T',$,'MatAssoc','Material Associates',(#3),#78);
#18=IFCRELDEFINESBYTYPE('1oHwFqo1XCjwoGt$KB$Lzm',$,'IPE200',$,(#4,#5),#3);
#19=IFCRELASSOCIATESMATERIAL('3O4J6OQF1C9gIjgzWdmsw5',$,'MatAssoc','Material Associates',(#4,#5),#82);
#20=IFCLOCALPLACEMENT($,#21);
#21=IFCAXIS2PLACEMENT3D(#22,$,$);
#22=IFCCARTESIANPOINT((0.,0.,0.));
#23=IFCPOSTALADDRESS($,$,$,$,$,$,$,'Unknown',$,$);
#24=IFCUNITASSIGNMENT((#25,#26,#27));
#25=IFCSIUNIT(*,.LENGTHUNIT.,.MILLI.,.METRE.);
#26=IFCSIUNIT(*,.PLANEANGLEUNIT.,$,.RADIAN.);
#27=IFCSIUNIT(*,.TIMEUNIT.,$,.SECOND.);
#28=IFCLOCALPLACEMENT($,#29);
#29=IFCAXIS2PLACEMENT3D(#30,#31,#32);
#30=IFCCARTESIANPOINT((0.,0.,0.));
#31=IFCDIRECTION((0.,1.,0.));
#32=IFCDIRECTION((-1.,0.,0.));
#33=IFCPRODUCTDEFINITIONSHAPE($,$,(#6,#7));
#34=IFCLOCALPLACEMENT($,#35);
#35=IFCAXIS2PLACEMENT3D(#36,#37,#38);
#36=IFCCARTESIANPOINT((0.,0.,0.));
#37=IFCDIRECTION((-0.38461538,0.92307692,0.));
#38=IFCDIRECTION((-0.92307692,-0.38461538,0.));
#39=IFCPRODUCTDEFINITIONSHAPE($,$,(#8,#9));
#40=IFCPOLYLINE((#41,#42));
#41=IFCCARTESIANPOINT((0.,0.,0.));
#42=IFCCARTESIANPOINT((0.,0.,1000.));
#43=IFCEXTRUDEDAREASOLID(#44,#45,#49,1000.);
#44=IFCISHAPEPROFILEDEF(.AREA.,'IPE200',$,100.,200.,5.6,8.5,12.,$,$);
#45=IFCAXIS2PLACEMENT3D(#46,#47,#48);
#46=IFCCARTESIANPOINT((0.,-100.,0.));
#47=IFCDIRECTION((0.,0.,1.));
#48=IFCDIRECTION((1.,0.,0.));
#49=IFCDIRECTION((0.,0.,1.));
#50=IFCTRIMMEDCURVE(#51,(IFCPARAMETERVALUE(0.),#56),(IFCPARAMETERVALUE(0.789582239399523),#57),.T.,.PARAMETER.);
#51=IFCCIRCLE(#52,1300.);
#52=IFCAXIS2PLACEMENT3D(#53,#54,#55);
#53=IFCCARTESIANPOINT((-1300.,0.,0.));
#54=IFCDIRECTION((0.,-1.,0.));
#55=IFCDIRECTION((1.,0.,0.));
#56=IFCCARTESIANPOINT((0.,0.,0.));
#57=IFCCARTESIANPOINT((-384.615384615385,0.,923.076923076923));
#58=IFCREVOLVEDAREASOLID(#59,#60,#64,0.789582239399523);
#59=IFCISHAPEPROFILEDEF(.AREA.,'IPE200',$,100.,200.,5.6,8.5,12.,$,$);
#60=IFCAXIS2PLACEMENT3D(#61,#62,#63);
#61=IFCCARTESIANPOINT((0.,-100.,0.));
#62=IFCDIRECTION((0.,0.,1.));
#63=IFCDIRECTION((1.,0.,0.));
#64=IFCAXIS1PLACEMENT(#65,#66);
#65=IFCCARTESIANPOINT((-1300.,100.,0.));
#66=IFCDIRECTION((0.,-1.,0.));
#67=IFCPERSONANDORGANIZATION(#68,#69,$);
#68=IFCPERSON('Jon','Jon',$,$,$,$,$,$);
#69=IFCORGANIZATION($,'Geometry Gym Pty Ltd',$,$,$);
#70=IFCAPPLICATION(#71,'*******','ggRhinoIFC - Geometry Gym Plug-in for Rhino3d','ggRhinoIFC');
#71=IFCORGANIZATION($,'Geometry Gym Pty Ltd',$,$,$);
#72=IFCAXIS2PLACEMENT3D(#73,$,$);
#73=IFCCARTESIANPOINT((0.,0.,0.));
#74=IFCDIRECTION((0.,1.));
#75=IFCAXIS2PLACEMENT3D(#76,$,$);
#76=IFCCARTESIANPOINT((0.,0.,0.));
#77=IFCDIRECTION((0.,1.));
#78=IFCMATERIALPROFILESET('IPE200',$,(#79),$);
#79=IFCMATERIALPROFILE('IPE200',$,#80,#81,0,$);
#80=IFCMATERIAL('S355JR',$,'Steel');
#81=IFCISHAPEPROFILEDEF(.AREA.,'IPE200',$,100.,200.,5.6,8.5,12.,$,$);
#82=IFCMATERIALPROFILESETUSAGE(#83,8,$);
#83=IFCMATERIALPROFILESET('IPE200',$,(#84),$);
#84=IFCMATERIALPROFILE('IPE200',$,#85,#86,0,$);
#85=IFCMATERIAL('S355JR',$,'Steel');
#86=IFCISHAPEPROFILEDEF(.AREA.,'IPE200',$,100.,200.,5.6,8.5,12.,$,$);
ENDSEC;
END-ISO-10303-21;

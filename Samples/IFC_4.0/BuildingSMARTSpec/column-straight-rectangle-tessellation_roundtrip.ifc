ISO-10303-21;
HEADER;
FILE_DESCRIPTION(('ViewDefinition [CoordinationView]'),'2;1');
FILE_NAME('','2020-10-30T00:18:56',(),(),'IfcOpenShell 0.6.0b0','IfcOpenShell 0.6.0b0','');
FILE_SCHEMA(('IFC4'));
ENDSEC;
DATA;
#1=IFCPROJECT('0CxDbxzA1B4eLeOw9eIjQx',$,'Project',$,$,$,$,(#5),#9);
#2=IFCSITE('0M0akNk9f3hOH9u2awmtre',$,'Site #1',$,$,#13,$,$,.ELEMENT.,$,$,$,$,$);
#3=IFCCOLUMN('2WUGYBphrFv8aLIFJCmiIk',$,'Column #1',$,$,#16,#24,$,.COLUMN.);
#4=IFCSHAPEREPRESENTATION(#6,'Body','Tessellation',(#25));
#5=IFCGEOMETRICREPRESENTATIONCONTEXT('3D','Model',3,1.E-05,#27,$);
#6=IFCGEOMETRICREPRESENTATIONSUBCONTEXT('Body','Model',*,*,*,*,#5,$,.MODEL_VIEW.,$);
#7=IFCRELAGGREGATES('2y_abP04108BvIhy2WfsHm',$,$,$,#1,(#2));
#8=IFCRELCONTAINEDINSPATIALSTRUCTURE('0F1oUoJsr5ZwxKAxiD1Vep',$,$,$,(#3),#2);
#9=IFCUNITASSIGNMENT((#10));
#10=IFCCONVERSIONBASEDUNIT($,.LENGTHUNIT.,'inch',#11);
#11=IFCMEASUREWITHUNIT(IFCLENGTHMEASURE(0.0254),#12);
#12=IFCSIUNIT(*,.LENGTHUNIT.,$,.METRE.);
#13=IFCLOCALPLACEMENT($,#14);
#14=IFCAXIS2PLACEMENT3D(#15,$,$);
#15=IFCCARTESIANPOINT((0.,0.,0.));
#16=IFCLOCALPLACEMENT(#17,#20);
#17=IFCLOCALPLACEMENT($,#18);
#18=IFCAXIS2PLACEMENT3D(#19,$,$);
#19=IFCCARTESIANPOINT((0.,0.,0.));
#20=IFCAXIS2PLACEMENT3D(#21,#22,#23);
#21=IFCCARTESIANPOINT((432.,288.,48.));
#22=IFCDIRECTION((0.,0.,1.));
#23=IFCDIRECTION((1.,0.,0.));
#24=IFCPRODUCTDEFINITIONSHAPE($,$,(#4));
#25=IFCTRIANGULATEDFACESET(#26,((0.,0.,-1.),(0.,0.,-1.),(0.,0.,-1.),(0.,0.,-1.),(0.,0.,1.),(0.,0.,1.),(0.,0.,1.),(0.,0.,1.),(-1.,0.,0.),(-1.,0.,0.),(-1.,0.,0.),(-1.,0.,0.),(0.,-1.,0.),(0.,-1.,0.),(0.,-1.,0.),(0.,-1.,0.),(1.,0.,0.),(1.,0.,0.),(1.,0.,0.),(1.,0.,0.),(0.,1.,0.),(0.,1.,0.),(0.,1.,0.),(0.,1.,0.)),.T.,((1,3,2),(1,4,3),(5,6,7),(5,7,8),(9,10,11),(9,11,12),(13,14,15),(13,15,16),(17,18,19),(17,19,20),(21,22,23),(21,23,24)),$);
#26=IFCCARTESIANPOINTLIST3D(((-4.,4.,0.),(-4.,-4.,0.),(4.,-4.,0.),(4.,4.,0.),(-4.,4.,120.),(-4.,-4.,120.),(4.,-4.,120.),(4.,4.,120.),(-4.,4.,0.),(-4.,-4.,0.),(-4.,-4.,120.),(-4.,4.,120.),(-4.,-4.,0.),(4.,-4.,0.),(4.,-4.,120.),(-4.,-4.,120.),(4.,-4.,0.),(4.,4.,0.),(4.,4.,120.),(4.,-4.,120.),(4.,4.,0.),(-4.,4.,0.),(-4.,4.,120.),(4.,4.,120.)));
#27=IFCAXIS2PLACEMENT3D(#28,$,$);
#28=IFCCARTESIANPOINT((0.,0.,0.));
ENDSEC;
END-ISO-10303-21;

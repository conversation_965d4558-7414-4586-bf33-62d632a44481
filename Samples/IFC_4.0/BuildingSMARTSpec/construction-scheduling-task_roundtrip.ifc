ISO-10303-21;
HEADER;
FILE_DESCRIPTION(('ViewDefinition [CoordinationView]'),'2;1');
FILE_NAME('','2020-10-30T00:18:56',(),(),'IfcOpenShell 0.6.0b0','IfcOpenShell 0.6.0b0','');
FILE_SCHEMA(('IFC4'));
ENDSEC;
DATA;
#1=IFCPROJECT('2ww9A9wXX54eT3Wns4rLsr',#35,'example project',$,$,$,$,(#36,#37),#73);
#2=IFCSITE('2uTUkDuu1Caw4X2OaltWYD',#35,'Site #1',$,$,#77,#80,$,.ELEMENT.,$,$,$,$,$);
#3=IFCBUILDING('39A9p5Pi1EIRKAduawfVgP',#35,'Building #1',$,$,#81,#87,$,.ELEMENT.,$,$,$);
#4=IFCBUILDINGSTOREY('3nYYuCp$XC4vhvJ2cMbpd2',#35,'Ground Level',$,$,#88,#97,$,.ELEMENT.,0.);
#5=IFCSLABSTANDARDCASE('20WrgxqpbDtferON_k1P2X',#35,'Slab #1',$,$,#98,#110,$,.BASESLAB.);
#6=IFCWALLSTANDARDCASE('26tmERtwL8G8UQn5BoglEh',#35,'Wall #1',$,$,#111,#125,$,.NOTDEFINED.);
#7=IFCWALLSTANDARDCASE('2qqyx9mIf27vQuEGt7ts8$',#35,'Wall #2',$,$,#126,#140,$,.NOTDEFINED.);
#8=IFCWALLSTANDARDCASE('0edwXkNgP03AySQjQQlat2',#35,'Wall #3',$,$,#141,#155,$,.NOTDEFINED.);
#9=IFCWALLSTANDARDCASE('27D5wuD5z86hr07xvbZLKW',#35,'Wall #4',$,$,#156,#170,$,.NOTDEFINED.);
#10=IFCTASK('0HSpq15hDC2R2RlJos8Ql5',#35,'Ground Level',$,$,$,$,$,$,.F.,$,#171,$);
#11=IFCTASK('3OFhQ6xenBvBxNI4NhGlTz',#35,'Slab (Standard)',$,$,'IfcSlabStandardCase',$,$,$,.F.,$,#172,$);
#12=IFCTASK('3vesIabXX7guLU5lIRuXh7',#35,'Slab #1',$,$,$,$,$,$,.F.,$,#173,$);
#13=IFCTASK('0njFKjg5HAEuwb1SdMaVRR',#35,'Wall (Standard)',$,$,'IfcWallStandardCase',$,$,$,.F.,$,#174,$);
#14=IFCTASK('0Kq8RwbZH3afCy87RX8dso',#35,'Wall #1',$,$,$,$,$,$,.F.,$,#175,$);
#15=IFCTASK('0hLKmeFATD$PWRDGqXzqOk',#35,'Wall #2',$,$,$,$,$,$,.F.,$,#176,$);
#16=IFCTASK('0Yz5hS48v9jfiCNjUF0okF',#35,'Wall #3',$,$,$,$,$,$,.F.,$,#177,$);
#17=IFCTASK('0kKoBDlp13eBAIY8350FFo',#35,'Wall #4',$,$,$,$,$,$,.F.,$,#178,$);
#18=IFCWORKPLAN('0eVhmaYyb3sBLb0LoNiW62',#35,'Work Plan #1',$,$,$,'2010-09-23T16:26:16',$,$,$,$,'2010-09-23T00:00:00',$,$);
#19=IFCWORKCALENDAR('0LHFCz8r5EQw4GeNNMS$Xp',#35,$,$,$,$,(#179),$,.FIRSTSHIFT.);
#20=IFCWORKSCHEDULE('2LoWUCZHr7YvZks8lmqjcw',#35,$,$,$,$,'2010-09-23T16:26:42',$,$,$,$,'2010-09-23T00:00:00',$,.PLANNED.);
#21=IFCSHAPEREPRESENTATION(#36,'FootPrint','GeometricCurveSet',(#182));
#22=IFCSHAPEREPRESENTATION(#36,'Body','SweptSolid',(#189));
#23=IFCSHAPEREPRESENTATION(#36,'FootPrint','GeometricCurveSet',(#200));
#24=IFCSHAPEREPRESENTATION(#36,'FootPrint','GeometricCurveSet',(#207));
#25=IFCSHAPEREPRESENTATION(#36,'FootPrint','GeometricCurveSet',(#214));
#26=IFCSHAPEREPRESENTATION(#36,'Body','SweptSolid',(#221));
#27=IFCSHAPEREPRESENTATION(#36,'Axis','Curve2D',(#232));
#28=IFCSHAPEREPRESENTATION(#36,'Body','SweptSolid',(#235));
#29=IFCSHAPEREPRESENTATION(#36,'Axis','Curve2D',(#246));
#30=IFCSHAPEREPRESENTATION(#36,'Body','SweptSolid',(#249));
#31=IFCSHAPEREPRESENTATION(#36,'Axis','Curve2D',(#260));
#32=IFCSHAPEREPRESENTATION(#36,'Body','SweptSolid',(#263));
#33=IFCSHAPEREPRESENTATION(#36,'Axis','Curve2D',(#274));
#34=IFCSHAPEREPRESENTATION(#36,'Body','SweptSolid',(#277));
#35=IFCOWNERHISTORY(#288,#291,.READWRITE.,.ADDED.,1285284288,$,$,1285284288);
#36=IFCGEOMETRICREPRESENTATIONCONTEXT('3D','Model',3,1.E-05,#293,$);
#37=IFCGEOMETRICREPRESENTATIONCONTEXT('2D','Plan',2,1.E-05,#295,$);
#38=IFCRELAGGREGATES('3lhFWDvkb6Cwv__4__szPn',#35,$,$,#1,(#2));
#39=IFCRELAGGREGATES('2Ugw7SsXLDQBYCez8hipzK',#35,$,$,#2,(#3));
#40=IFCRELAGGREGATES('2RHRlut4v6yPP4JCrvm4IE',#35,$,$,#3,(#4));
#41=IFCRELASSOCIATESMATERIAL('2kASSjs2f6Phd71DH$IPe5',#35,'IfcSlabStandardCase',$,(#5),#297);
#42=IFCRELCONTAINEDINSPATIALSTRUCTURE('19iKmngSP0bR$GMfGwTIoD',#35,$,$,(#5,#6,#7,#8,#9),#4);
#43=IFCRELCONNECTSELEMENTS('2ZHJEGFvL98QhEDnQsGNrK',#35,$,$,$,#5,#6);
#44=IFCRELASSOCIATESMATERIAL('0cuSbpct575BKLAXM$olZ_',#35,'IfcWallStandardCase',$,(#6,#7,#8,#9),#301);
#45=IFCRELCONNECTSELEMENTS('04lBS8dRv8082xho3Ve2L0',#35,$,$,$,#5,#7);
#46=IFCRELCONNECTSPATHELEMENTS('2aU84ph_f2iwRK7mEx6APn',#35,$,$,$,#6,#7,$,$,.ATSTART.,.ATEND.);
#47=IFCRELCONNECTSELEMENTS('1_c3omXFD7UPfF9thiXwlM',#35,$,$,$,#5,#8);
#48=IFCRELCONNECTSPATHELEMENTS('3cHo0l0vT7YecRcCZH80A_',#35,$,$,$,#7,#8,$,$,.ATSTART.,.ATEND.);
#49=IFCRELCONNECTSELEMENTS('2GwDyB_Xb1mBzqynA5hpmk',#35,$,$,$,#5,#9);
#50=IFCRELCONNECTSPATHELEMENTS('0N2CJ8VSz1yx6iStYDzqLN',#35,$,$,$,#8,#9,$,$,.ATSTART.,.ATEND.);
#51=IFCRELCONNECTSPATHELEMENTS('2FxBB642r3GfFLy0QsWZoT',#35,$,$,$,#9,#6,$,$,.ATSTART.,.ATEND.);
#52=IFCRELDECLARES('0CWmoqi1D5fvyIWw6QZMVg',#35,'PROCESS',$,#1,(#10));
#53=IFCRELASSIGNSTOPRODUCT('3fzXeaVoT7R8p4ro97HwNg',#35,$,$,(#10),$,#4);
#54=IFCRELNESTS('084eMXNPf4iQUJ1DL9YVyg',#35,$,$,#10,(#11,#13));
#55=IFCRELNESTS('25bUvqUPT8sO$Hp1WU0jhy',#35,$,$,#11,(#12));
#56=IFCRELASSIGNSTOPRODUCT('3vXaSpK9P0fg8T$uh5yD3o',#35,$,$,(#12),$,#5);
#57=IFCRELNESTS('3i7xt4aCT70BYgowuH355l',#35,$,$,#13,(#14,#15,#16,#17));
#58=IFCRELASSIGNSTOPRODUCT('3Q5arGx_f1TBv9qlup9fDC',#35,$,$,(#14),$,#6);
#59=IFCRELASSIGNSTOPRODUCT('0$ba11SzL0hQrmYcN5xQqx',#35,$,$,(#15),$,#7);
#60=IFCRELASSIGNSTOPRODUCT('3gkG6$UaT7GwCw3Fu$NJuW',#35,$,$,(#16),$,#8);
#61=IFCRELASSIGNSTOPRODUCT('1ayXYhiTj4fuJRkoMMiKB1',#35,$,$,(#17),$,#9);
#62=IFCRELSEQUENCE('0ytgSSysP98eeo2xw37OMq',#35,$,$,#12,#14,$,.FINISH_START.,$);
#63=IFCRELSEQUENCE('0wK7QBCOv15RnMy6Ia9ykQ',#35,$,$,#12,#15,$,.FINISH_START.,$);
#64=IFCRELSEQUENCE('2Y5tFvjSfCngQp$$PYkXhS',#35,$,$,#12,#16,$,.FINISH_START.,$);
#65=IFCRELSEQUENCE('2hWL_Juob0BPVRlnbe1v3c',#35,$,$,#12,#17,$,.FINISH_START.,$);
#66=IFCRELSEQUENCE('0GVHMGhZ58UhwQX6IyHGzD',#35,$,$,#14,#15,$,.FINISH_START.,$);
#67=IFCRELSEQUENCE('2cOAlYj8X46PLPMFHuoV8X',#35,$,$,#15,#16,$,.FINISH_START.,$);
#68=IFCRELSEQUENCE('3OZBIrkNX0IAUQIs8NADJm',#35,$,$,#16,#17,$,.FINISH_START.,$);
#69=IFCRELDECLARES('3n6EjscYnB4hf4mKVrfRVJ',#35,'CONTROL',$,#1,(#18));
#70=IFCRELNESTS('0if6u97Ln58xH$wewg0rH3',#35,$,$,#18,(#19,#20));
#71=IFCRELASSIGNSTOCONTROL('3DnVao$j10NByLyVF3hP9m',#35,$,$,(#10),$,#19);
#72=IFCRELASSIGNSTOCONTROL('0_6CMgJyT5jviMnquaXCct',#35,$,$,(#10),$,#20);
#73=IFCUNITASSIGNMENT((#74));
#74=IFCCONVERSIONBASEDUNIT($,.LENGTHUNIT.,'inch',#75);
#75=IFCMEASUREWITHUNIT(IFCLENGTHMEASURE(0.0254),#76);
#76=IFCSIUNIT(*,.LENGTHUNIT.,$,.METRE.);
#77=IFCLOCALPLACEMENT($,#78);
#78=IFCAXIS2PLACEMENT3D(#79,$,$);
#79=IFCCARTESIANPOINT((0.,0.,0.));
#80=IFCPRODUCTDEFINITIONSHAPE($,$,(#21,#22));
#81=IFCLOCALPLACEMENT(#82,#85);
#82=IFCLOCALPLACEMENT($,#83);
#83=IFCAXIS2PLACEMENT3D(#84,$,$);
#84=IFCCARTESIANPOINT((0.,0.,0.));
#85=IFCAXIS2PLACEMENT3D(#86,$,$);
#86=IFCCARTESIANPOINT((576.,240.,48.));
#87=IFCPRODUCTDEFINITIONSHAPE($,$,(#23));
#88=IFCLOCALPLACEMENT(#89,#95);
#89=IFCLOCALPLACEMENT(#90,#93);
#90=IFCLOCALPLACEMENT($,#91);
#91=IFCAXIS2PLACEMENT3D(#92,$,$);
#92=IFCCARTESIANPOINT((0.,0.,0.));
#93=IFCAXIS2PLACEMENT3D(#94,$,$);
#94=IFCCARTESIANPOINT((576.,240.,48.));
#95=IFCAXIS2PLACEMENT3D(#96,$,$);
#96=IFCCARTESIANPOINT((0.,0.,0.));
#97=IFCPRODUCTDEFINITIONSHAPE($,$,(#24));
#98=IFCLOCALPLACEMENT(#99,#108);
#99=IFCLOCALPLACEMENT(#100,#106);
#100=IFCLOCALPLACEMENT(#101,#104);
#101=IFCLOCALPLACEMENT($,#102);
#102=IFCAXIS2PLACEMENT3D(#103,$,$);
#103=IFCCARTESIANPOINT((0.,0.,0.));
#104=IFCAXIS2PLACEMENT3D(#105,$,$);
#105=IFCCARTESIANPOINT((576.,240.,48.));
#106=IFCAXIS2PLACEMENT3D(#107,$,$);
#107=IFCCARTESIANPOINT((0.,0.,0.));
#108=IFCAXIS2PLACEMENT3D(#109,$,$);
#109=IFCCARTESIANPOINT((0.,0.,0.));
#110=IFCPRODUCTDEFINITIONSHAPE($,$,(#25,#26));
#111=IFCLOCALPLACEMENT(#112,#121);
#112=IFCLOCALPLACEMENT(#113,#119);
#113=IFCLOCALPLACEMENT(#114,#117);
#114=IFCLOCALPLACEMENT($,#115);
#115=IFCAXIS2PLACEMENT3D(#116,$,$);
#116=IFCCARTESIANPOINT((0.,0.,0.));
#117=IFCAXIS2PLACEMENT3D(#118,$,$);
#118=IFCCARTESIANPOINT((576.,240.,48.));
#119=IFCAXIS2PLACEMENT3D(#120,$,$);
#120=IFCCARTESIANPOINT((0.,0.,0.));
#121=IFCAXIS2PLACEMENT3D(#122,#123,#124);
#122=IFCCARTESIANPOINT((0.,0.,6.));
#123=IFCDIRECTION((0.,0.,1.));
#124=IFCDIRECTION((0.,1.,0.));
#125=IFCPRODUCTDEFINITIONSHAPE($,$,(#27,#28));
#126=IFCLOCALPLACEMENT(#127,#136);
#127=IFCLOCALPLACEMENT(#128,#134);
#128=IFCLOCALPLACEMENT(#129,#132);
#129=IFCLOCALPLACEMENT($,#130);
#130=IFCAXIS2PLACEMENT3D(#131,$,$);
#131=IFCCARTESIANPOINT((0.,0.,0.));
#132=IFCAXIS2PLACEMENT3D(#133,$,$);
#133=IFCCARTESIANPOINT((576.,240.,48.));
#134=IFCAXIS2PLACEMENT3D(#135,$,$);
#135=IFCCARTESIANPOINT((0.,0.,0.));
#136=IFCAXIS2PLACEMENT3D(#137,#138,#139);
#137=IFCCARTESIANPOINT((0.,192.,6.));
#138=IFCDIRECTION((0.,0.,1.));
#139=IFCDIRECTION((1.,0.,0.));
#140=IFCPRODUCTDEFINITIONSHAPE($,$,(#29,#30));
#141=IFCLOCALPLACEMENT(#142,#151);
#142=IFCLOCALPLACEMENT(#143,#149);
#143=IFCLOCALPLACEMENT(#144,#147);
#144=IFCLOCALPLACEMENT($,#145);
#145=IFCAXIS2PLACEMENT3D(#146,$,$);
#146=IFCCARTESIANPOINT((0.,0.,0.));
#147=IFCAXIS2PLACEMENT3D(#148,$,$);
#148=IFCCARTESIANPOINT((576.,240.,48.));
#149=IFCAXIS2PLACEMENT3D(#150,$,$);
#150=IFCCARTESIANPOINT((0.,0.,0.));
#151=IFCAXIS2PLACEMENT3D(#152,#153,#154);
#152=IFCCARTESIANPOINT((288.,192.,6.));
#153=IFCDIRECTION((0.,0.,1.));
#154=IFCDIRECTION((0.,-1.,0.));
#155=IFCPRODUCTDEFINITIONSHAPE($,$,(#31,#32));
#156=IFCLOCALPLACEMENT(#157,#166);
#157=IFCLOCALPLACEMENT(#158,#164);
#158=IFCLOCALPLACEMENT(#159,#162);
#159=IFCLOCALPLACEMENT($,#160);
#160=IFCAXIS2PLACEMENT3D(#161,$,$);
#161=IFCCARTESIANPOINT((0.,0.,0.));
#162=IFCAXIS2PLACEMENT3D(#163,$,$);
#163=IFCCARTESIANPOINT((576.,240.,48.));
#164=IFCAXIS2PLACEMENT3D(#165,$,$);
#165=IFCCARTESIANPOINT((0.,0.,0.));
#166=IFCAXIS2PLACEMENT3D(#167,#168,#169);
#167=IFCCARTESIANPOINT((288.,0.,6.));
#168=IFCDIRECTION((0.,0.,1.));
#169=IFCDIRECTION((-1.,0.,0.));
#170=IFCPRODUCTDEFINITIONSHAPE($,$,(#33,#34));
#171=IFCTASKTIME($,.PREDICTED.,$,.WORKTIME.,'P0Y0M1DT16H0M0S','2010-09-20T08:00:00',$,'2010-09-20T08:00:00','2010-09-24T16:00:00','2010-09-20T08:00:00','2010-09-20T16:00:00','P0Y0M0DT0H0M0S','P0Y0M0DT0H0M0S',.T.,$,$,$,$,$,$);
#172=IFCTASKTIME($,.PREDICTED.,$,.WORKTIME.,'P0Y0M0DT8H0M0S',$,$,'2010-09-20T08:00:00','2010-09-20T16:00:00',$,$,$,$,$,$,$,$,$,$,$);
#173=IFCTASKTIME($,.PREDICTED.,$,.WORKTIME.,'P0Y0M0DT8H0M0S',$,$,'2010-09-20T08:00:00','2010-09-20T16:00:00','2010-09-20T08:00:00','2010-09-20T16:00:00','P0Y0M0DT16H0M0S','P0Y0M0DT1H0M0S',.T.,$,$,$,$,$,$);
#174=IFCTASKTIME($,.PREDICTED.,$,.WORKTIME.,'P0Y0M1DT8H0M0S',$,$,'2010-09-21T08:00:00','2010-09-24T16:00:00',$,$,$,$,$,$,$,$,$,$,$);
#175=IFCTASKTIME($,.PREDICTED.,$,.WORKTIME.,'P0Y0M0DT8H0M0S',$,$,'2010-09-21T08:00:00','2010-09-21T16:00:00','2010-09-21T08:00:00','2010-09-21T16:00:00','P0Y0M0DT0H0M0S','P0Y0M0DT0H0M0S',.T.,$,$,$,$,$,$);
#176=IFCTASKTIME($,.PREDICTED.,$,.WORKTIME.,'P0Y0M0DT8H0M0S',$,$,'2010-09-22T08:00:00','2010-09-22T16:00:00','2010-09-22T08:00:00','2010-09-22T16:00:00','P0Y0M0DT0H0M0S','P0Y0M0DT0H0M0S',.T.,$,$,$,$,$,$);
#177=IFCTASKTIME($,.PREDICTED.,$,.WORKTIME.,'P0Y0M0DT8H0M0S',$,$,'2010-09-23T08:00:00','2010-09-23T16:00:00','2010-09-23T08:00:00','2010-09-23T16:00:00','P0Y0M0DT0H0M0S','P0Y0M0DT0H0M0S',.T.,$,$,$,$,$,$);
#178=IFCTASKTIME($,.PREDICTED.,$,.WORKTIME.,'P0Y0M0DT8H0M0S',$,$,'2010-09-24T08:00:00','2010-09-24T16:00:00','2010-09-24T08:00:00','2010-09-24T16:00:00','P0Y0M0DT0H0M0S','P0Y0M0DT0H0M0S',.T.,$,$,$,$,$,$);
#179=IFCWORKTIME('Standard',$,$,#180,$,$);
#180=IFCRECURRENCEPATTERN(.WEEKLY.,$,(1,2,3,4,5),$,$,$,$,(#181));
#181=IFCTIMEPERIOD('08:00:00','16:00:00');
#182=IFCGEOMETRICCURVESET((#183));
#183=IFCPOLYLINE((#184,#185,#186,#187,#188));
#184=IFCCARTESIANPOINT((0.,0.));
#185=IFCCARTESIANPOINT((1536.,0.));
#186=IFCCARTESIANPOINT((1536.,768.));
#187=IFCCARTESIANPOINT((0.,768.));
#188=IFCCARTESIANPOINT((0.,0.));
#189=IFCEXTRUDEDAREASOLID(#190,#197,#199,48.);
#190=IFCARBITRARYCLOSEDPROFILEDEF(.AREA.,$,#191);
#191=IFCPOLYLINE((#192,#193,#194,#195,#196));
#192=IFCCARTESIANPOINT((0.,0.));
#193=IFCCARTESIANPOINT((1536.,0.));
#194=IFCCARTESIANPOINT((1536.,768.));
#195=IFCCARTESIANPOINT((0.,768.));
#196=IFCCARTESIANPOINT((0.,0.));
#197=IFCAXIS2PLACEMENT3D(#198,$,$);
#198=IFCCARTESIANPOINT((0.,0.,0.));
#199=IFCDIRECTION((0.,0.,1.));
#200=IFCGEOMETRICCURVESET((#201));
#201=IFCPOLYLINE((#202,#203,#204,#205,#206));
#202=IFCCARTESIANPOINT((0.,0.));
#203=IFCCARTESIANPOINT((288.,0.));
#204=IFCCARTESIANPOINT((288.,192.));
#205=IFCCARTESIANPOINT((0.,192.));
#206=IFCCARTESIANPOINT((0.,0.));
#207=IFCGEOMETRICCURVESET((#208));
#208=IFCPOLYLINE((#209,#210,#211,#212,#213));
#209=IFCCARTESIANPOINT((0.,0.));
#210=IFCCARTESIANPOINT((288.,0.));
#211=IFCCARTESIANPOINT((288.,192.));
#212=IFCCARTESIANPOINT((0.,192.));
#213=IFCCARTESIANPOINT((0.,0.));
#214=IFCGEOMETRICCURVESET((#215));
#215=IFCPOLYLINE((#216,#217,#218,#219,#220));
#216=IFCCARTESIANPOINT((0.,0.));
#217=IFCCARTESIANPOINT((288.,0.));
#218=IFCCARTESIANPOINT((288.,192.));
#219=IFCCARTESIANPOINT((0.,192.));
#220=IFCCARTESIANPOINT((0.,0.));
#221=IFCEXTRUDEDAREASOLID(#222,#229,#231,6.);
#222=IFCARBITRARYCLOSEDPROFILEDEF(.AREA.,$,#223);
#223=IFCPOLYLINE((#224,#225,#226,#227,#228));
#224=IFCCARTESIANPOINT((0.,0.));
#225=IFCCARTESIANPOINT((288.,0.));
#226=IFCCARTESIANPOINT((288.,192.));
#227=IFCCARTESIANPOINT((0.,192.));
#228=IFCCARTESIANPOINT((0.,0.));
#229=IFCAXIS2PLACEMENT3D(#230,$,$);
#230=IFCCARTESIANPOINT((0.,0.,0.));
#231=IFCDIRECTION((0.,0.,1.));
#232=IFCPOLYLINE((#233,#234));
#233=IFCCARTESIANPOINT((0.,0.));
#234=IFCCARTESIANPOINT((192.,0.));
#235=IFCEXTRUDEDAREASOLID(#236,#243,#245,120.);
#236=IFCARBITRARYCLOSEDPROFILEDEF(.AREA.,$,#237);
#237=IFCPOLYLINE((#238,#239,#240,#241,#242));
#238=IFCCARTESIANPOINT((3.,-3.));
#239=IFCCARTESIANPOINT((189.,-3.));
#240=IFCCARTESIANPOINT((195.,3.));
#241=IFCCARTESIANPOINT((-3.,3.));
#242=IFCCARTESIANPOINT((3.,-3.));
#243=IFCAXIS2PLACEMENT3D(#244,$,$);
#244=IFCCARTESIANPOINT((0.,0.,0.));
#245=IFCDIRECTION((0.,0.,1.));
#246=IFCPOLYLINE((#247,#248));
#247=IFCCARTESIANPOINT((0.,0.));
#248=IFCCARTESIANPOINT((288.,0.));
#249=IFCEXTRUDEDAREASOLID(#250,#257,#259,120.);
#250=IFCARBITRARYCLOSEDPROFILEDEF(.AREA.,$,#251);
#251=IFCPOLYLINE((#252,#253,#254,#255,#256));
#252=IFCCARTESIANPOINT((3.,-3.));
#253=IFCCARTESIANPOINT((285.,-3.));
#254=IFCCARTESIANPOINT((291.,3.));
#255=IFCCARTESIANPOINT((-3.,3.));
#256=IFCCARTESIANPOINT((3.,-3.));
#257=IFCAXIS2PLACEMENT3D(#258,$,$);
#258=IFCCARTESIANPOINT((0.,0.,0.));
#259=IFCDIRECTION((0.,0.,1.));
#260=IFCPOLYLINE((#261,#262));
#261=IFCCARTESIANPOINT((0.,0.));
#262=IFCCARTESIANPOINT((192.,0.));
#263=IFCEXTRUDEDAREASOLID(#264,#271,#273,120.);
#264=IFCARBITRARYCLOSEDPROFILEDEF(.AREA.,$,#265);
#265=IFCPOLYLINE((#266,#267,#268,#269,#270));
#266=IFCCARTESIANPOINT((3.,-3.));
#267=IFCCARTESIANPOINT((189.,-3.));
#268=IFCCARTESIANPOINT((195.,3.));
#269=IFCCARTESIANPOINT((-3.,3.));
#270=IFCCARTESIANPOINT((3.,-3.));
#271=IFCAXIS2PLACEMENT3D(#272,$,$);
#272=IFCCARTESIANPOINT((0.,0.,0.));
#273=IFCDIRECTION((0.,0.,1.));
#274=IFCPOLYLINE((#275,#276));
#275=IFCCARTESIANPOINT((0.,0.));
#276=IFCCARTESIANPOINT((288.,0.));
#277=IFCEXTRUDEDAREASOLID(#278,#285,#287,120.);
#278=IFCARBITRARYCLOSEDPROFILEDEF(.AREA.,$,#279);
#279=IFCPOLYLINE((#280,#281,#282,#283,#284));
#280=IFCCARTESIANPOINT((3.,-3.));
#281=IFCCARTESIANPOINT((285.,-3.));
#282=IFCCARTESIANPOINT((291.,3.));
#283=IFCCARTESIANPOINT((-3.,3.));
#284=IFCCARTESIANPOINT((3.,-3.));
#285=IFCAXIS2PLACEMENT3D(#286,$,$);
#286=IFCCARTESIANPOINT((0.,0.,0.));
#287=IFCDIRECTION((0.,0.,1.));
#288=IFCPERSONANDORGANIZATION(#289,#290,$);
#289=IFCPERSON($,'Chipman','Tim',$,$,$,$,$);
#290=IFCORGANIZATION($,'buildingSMART',$,$,$);
#291=IFCAPPLICATION(#292,'0.7','Constructivity','CONSTRUCTIVITY');
#292=IFCORGANIZATION($,'Constructivity.com LLC',$,$,$);
#293=IFCAXIS2PLACEMENT3D(#294,$,$);
#294=IFCCARTESIANPOINT((0.,0.,0.));
#295=IFCAXIS2PLACEMENT3D(#296,$,$);
#296=IFCCARTESIANPOINT((0.,0.,0.));
#297=IFCMATERIALLAYERSETUSAGE(#298,.AXIS3.,.POSITIVE.,0.,$);
#298=IFCMATERIALLAYERSET((#299),$,$);
#299=IFCMATERIALLAYER(#300,6.,$,'Core','Material from which the slab is constructed.',$,$);
#300=IFCMATERIAL('Concrete (Nominal)',$,'Concrete');
#301=IFCMATERIALLAYERSETUSAGE(#302,.AXIS2.,.POSITIVE.,-3.,$);
#302=IFCMATERIALLAYERSET((#303),$,$);
#303=IFCMATERIALLAYERWITHOFFSETS(#304,6.,$,'Block','Structural core of the wall, such as concrete masonry units.',$,$,.AXIS1.,(0.,0.));
#304=IFCMATERIAL('Block (Nominal)',$,'Block');
ENDSEC;
END-ISO-10303-21;

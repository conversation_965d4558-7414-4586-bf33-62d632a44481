ISO-10303-21;
HEADER;
FILE_DESCRIPTION(('ViewDefinition [CoordinationView]'),'2;1');
FILE_NAME('','2020-10-30T00:18:57',(),(),'IfcOpenShell 0.6.0b0','IfcOpenShell 0.6.0b0','');
FILE_SCHEMA(('IFC4'));
ENDSEC;
DATA;
#1=IFCBUILDINGELEMENTPROXY('0rBru4syGxGOZb$M8kVJuS',#7,'Geographic Position',$,$,#13,#33,$,$);
#2=IFCBUILDINGSTOREY('3_fv_WeK63IPclwapZa9MD',#7,'Storey 1',$,$,#34,$,$,.ELEMENT.,-1.6);
#3=IFCPROJECT('01JwSt5ycUHvKFlMZUleKS',#7,'Notch',$,$,$,$,(#8),#49);
#4=IFCSITE('1BPCQAtTW7GhKrUF$Sytrr',#7,'Site',$,$,#54,$,$,.ELEMENT.,(49,5,44,124),(8,26,1,320000),113.7,$,$);
#5=IFCBUILDING('0cVTYHAI3nGeTXA2X4Gxyo',#7,'Building',$,$,#59,$,$,.ELEMENT.,$,$,$);
#6=IFCSHAPEREPRESENTATION(#8,'Body','CSG',(#69,#75));
#7=IFCOWNERHISTORY(#81,#84,$,.NOTDEFINED.,$,$,$,1122650864);
#8=IFCGEOMETRICREPRESENTATIONCONTEXT($,'Model',3,1.E-05,#86,$);
#9=IFCRELCONTAINEDINSPATIALSTRUCTURE('2lLFgu3KhSGw4jMC3$Ak50',#7,'Storey 1',$,(#1),#2);
#10=IFCRELAGGREGATES('3INy2PAPGPJ8Dpwhrq610o',#7,'All stories',$,#5,(#2));
#11=IFCRELAGGREGATES('2vOmTZNmbvJAJRbSGMMNrC',#7,$,$,#3,(#4));
#12=IFCRELAGGREGATES('1F_GRhxz1GJgGgcJgzjvKX',#7,$,$,#4,(#5));
#13=IFCLOCALPLACEMENT(#14,#29);
#14=IFCLOCALPLACEMENT(#15,#25);
#15=IFCLOCALPLACEMENT(#16,#21);
#16=IFCLOCALPLACEMENT($,#17);
#17=IFCAXIS2PLACEMENT3D(#18,#19,#20);
#18=IFCCARTESIANPOINT((0.,0.,0.));
#19=IFCDIRECTION((0.,0.,1.));
#20=IFCDIRECTION((1.,0.,0.));
#21=IFCAXIS2PLACEMENT3D(#22,#23,#24);
#22=IFCCARTESIANPOINT((0.,0.,0.));
#23=IFCDIRECTION((0.,0.,1.));
#24=IFCDIRECTION((1.,0.,0.));
#25=IFCAXIS2PLACEMENT3D(#26,#27,#28);
#26=IFCCARTESIANPOINT((0.,0.,0.));
#27=IFCDIRECTION((0.,0.,1.));
#28=IFCDIRECTION((1.,0.,0.));
#29=IFCAXIS2PLACEMENT3D(#30,#31,#32);
#30=IFCCARTESIANPOINT((0.,0.,0.));
#31=IFCDIRECTION((0.,0.,1.));
#32=IFCDIRECTION((1.,0.,0.));
#33=IFCPRODUCTDEFINITIONSHAPE($,$,(#6));
#34=IFCLOCALPLACEMENT(#35,#45);
#35=IFCLOCALPLACEMENT(#36,#41);
#36=IFCLOCALPLACEMENT($,#37);
#37=IFCAXIS2PLACEMENT3D(#38,#39,#40);
#38=IFCCARTESIANPOINT((0.,0.,0.));
#39=IFCDIRECTION((0.,0.,1.));
#40=IFCDIRECTION((1.,0.,0.));
#41=IFCAXIS2PLACEMENT3D(#42,#43,#44);
#42=IFCCARTESIANPOINT((0.,0.,0.));
#43=IFCDIRECTION((0.,0.,1.));
#44=IFCDIRECTION((1.,0.,0.));
#45=IFCAXIS2PLACEMENT3D(#46,#47,#48);
#46=IFCCARTESIANPOINT((0.,0.,0.));
#47=IFCDIRECTION((0.,0.,1.));
#48=IFCDIRECTION((1.,0.,0.));
#49=IFCUNITASSIGNMENT((#50,#51,#52,#53));
#50=IFCSIUNIT(*,.LENGTHUNIT.,.MILLI.,.METRE.);
#51=IFCSIUNIT(*,.AREAUNIT.,$,.SQUARE_METRE.);
#52=IFCSIUNIT(*,.VOLUMEUNIT.,$,.CUBIC_METRE.);
#53=IFCSIUNIT(*,.PLANEANGLEUNIT.,$,.RADIAN.);
#54=IFCLOCALPLACEMENT($,#55);
#55=IFCAXIS2PLACEMENT3D(#56,#57,#58);
#56=IFCCARTESIANPOINT((0.,0.,0.));
#57=IFCDIRECTION((0.,0.,1.));
#58=IFCDIRECTION((1.,0.,0.));
#59=IFCLOCALPLACEMENT(#60,#65);
#60=IFCLOCALPLACEMENT($,#61);
#61=IFCAXIS2PLACEMENT3D(#62,#63,#64);
#62=IFCCARTESIANPOINT((0.,0.,0.));
#63=IFCDIRECTION((0.,0.,1.));
#64=IFCDIRECTION((1.,0.,0.));
#65=IFCAXIS2PLACEMENT3D(#66,#67,#68);
#66=IFCCARTESIANPOINT((0.,0.,0.));
#67=IFCDIRECTION((0.,0.,1.));
#68=IFCDIRECTION((1.,0.,0.));
#69=IFCCSGSOLID(#70);
#70=IFCRIGHTCIRCULARCONE(#71,500.,150.);
#71=IFCAXIS2PLACEMENT3D(#72,#73,#74);
#72=IFCCARTESIANPOINT((0.,0.,500.));
#73=IFCDIRECTION((0.,0.,-1.));
#74=IFCDIRECTION((-1.,0.,0.));
#75=IFCCSGSOLID(#76);
#76=IFCRIGHTCIRCULARCYLINDER(#77,500.,50.);
#77=IFCAXIS2PLACEMENT3D(#78,#79,#80);
#78=IFCCARTESIANPOINT((0.,0.,500.));
#79=IFCDIRECTION((0.,0.,1.));
#80=IFCDIRECTION((1.,0.,0.));
#81=IFCPERSONANDORGANIZATION(#82,#83,$);
#82=IFCPERSON($,'AGeiger',$,$,$,$,$,$);
#83=IFCORGANIZATION($,'KIT',$,$,$);
#84=IFCAPPLICATION(#85,'Unknown','IFCExplorer','Unknown');
#85=IFCORGANIZATION($,'KIT',$,$,$);
#86=IFCAXIS2PLACEMENT3D(#87,#88,#89);
#87=IFCCARTESIANPOINT((0.,0.,0.));
#88=IFCDIRECTION((0.,0.,1.));
#89=IFCDIRECTION((1.,0.,0.));
ENDSEC;
END-ISO-10303-21;

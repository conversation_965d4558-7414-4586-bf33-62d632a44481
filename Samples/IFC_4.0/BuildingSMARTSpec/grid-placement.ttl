# baseURI: http://linkedbuildingdata.net/ifc/resources20200624_184152/
# imports: http://standards.buildingsmart.org/IFC/DEV/IFC4/ADD1/OWL

@prefix ifc:  <http://standards.buildingsmart.org/IFC/DEV/IFC4/ADD1/OWL#> .
@prefix inst:  <http://linkedbuildingdata.net/ifc/resources20200624_184152/> .
@prefix list:  <https://w3id.org/list#> .
@prefix express:  <https://w3id.org/express#> .
@prefix rdf:  <http://www.w3.org/1999/02/22-rdf-syntax-ns#> .
@prefix xsd:  <http://www.w3.org/2001/XMLSchema#> .
@prefix owl:  <http://www.w3.org/2002/07/owl#> .

inst:   rdf:type     owl:Ontology ;
        owl:imports  ifc: .

inst:IfcOrganization_1
        rdf:type  ifc:IfcOrganization .

inst:Ifc<PERSON>abel_525  rdf:type  ifc:Ifc<PERSON>abel ;
        express:hasString  "Name" .

inst:IfcOrganization_1
        ifc:name_IfcOrganization  inst:IfcLabel_525 .

inst:IfcDirection_2050
        rdf:type  ifc:IfcDirection .

inst:IfcReal_List_526
        rdf:type  ifc:IfcReal_List .

inst:IfcDirection_2050
        ifc:directionRatios_IfcDirection  inst:IfcReal_List_526 .

inst:IfcReal_List_527
        rdf:type  ifc:IfcReal_List .

inst:IfcReal_528  rdf:type  ifc:IfcReal ;
        express:hasDouble  "1."^^xsd:double .

inst:IfcReal_List_526
        list:hasContents  inst:IfcReal_528 ;
        list:hasNext      inst:IfcReal_List_527 .

inst:IfcReal_529  rdf:type  ifc:IfcReal ;
        express:hasDouble  "0."^^xsd:double .

inst:IfcReal_List_527
        list:hasContents  inst:IfcReal_529 .

inst:IfcApplication_5
        rdf:type  ifc:IfcApplication ;
        ifc:applicationDeveloper_IfcApplication  inst:IfcOrganization_1 .

inst:IfcLabel_530  rdf:type  ifc:IfcLabel ;
        express:hasString  "Version" .

inst:IfcApplication_5
        ifc:version_IfcApplication  inst:IfcLabel_530 ;
        ifc:applicationFullName_IfcApplication  inst:IfcLabel_525 .

inst:IfcIdentifier_531
        rdf:type           ifc:IfcIdentifier ;
        express:hasString  "Identifier" .

inst:IfcApplication_5
        ifc:applicationIdentifier_IfcApplication  inst:IfcIdentifier_531 .

inst:IfcPerson_6  rdf:type  ifc:IfcPerson .

inst:IfcLabel_532  rdf:type  ifc:IfcLabel ;
        express:hasString  "Family name" .

inst:IfcPerson_6  ifc:familyName_IfcPerson  inst:IfcLabel_532 .

inst:IfcLabel_533  rdf:type  ifc:IfcLabel ;
        express:hasString  "Given name" .

inst:IfcPerson_6  ifc:givenName_IfcPerson  inst:IfcLabel_533 .

inst:IfcCartesianPoint_2054
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_534
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_2054
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_534 .

inst:IfcLengthMeasure_List_535
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_536
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-100."^^xsd:double .

inst:IfcLengthMeasure_List_534
        list:hasContents  inst:IfcLengthMeasure_536 ;
        list:hasNext      inst:IfcLengthMeasure_List_535 .

inst:IfcLengthMeasure_List_535
        list:hasContents  inst:IfcReal_529 .

inst:IfcAxis2Placement2D_2058
        rdf:type                   ifc:IfcAxis2Placement2D ;
        ifc:location_IfcPlacement  inst:IfcCartesianPoint_2054 ;
        ifc:refDirection_IfcAxis2Placement2D  inst:IfcDirection_2050 .

inst:IfcPersonAndOrganization_12
        rdf:type  ifc:IfcPersonAndOrganization ;
        ifc:thePerson_IfcPersonAndOrganization  inst:IfcPerson_6 ;
        ifc:theOrganization_IfcPersonAndOrganization  inst:IfcOrganization_1 .

inst:IfcOwnerHistory_13
        rdf:type                        ifc:IfcOwnerHistory ;
        ifc:owningUser_IfcOwnerHistory  inst:IfcPersonAndOrganization_12 ;
        ifc:owningApplication_IfcOwnerHistory  inst:IfcApplication_5 ;
        ifc:changeAction_IfcOwnerHistory  ifc:NOTDEFINED .

inst:IfcTimeStamp_537
        rdf:type            ifc:IfcTimeStamp ;
        express:hasInteger  1247473054 .

inst:IfcOwnerHistory_13
        ifc:creationDate_IfcOwnerHistory  inst:IfcTimeStamp_537 .

inst:IfcRectangleProfileDef_2061
        rdf:type                       ifc:IfcRectangleProfileDef ;
        ifc:profileType_IfcProfileDef  ifc:AREA .

inst:IfcLabel_538  rdf:type  ifc:IfcLabel ;
        express:hasString  "" .

inst:IfcRectangleProfileDef_2061
        ifc:profileName_IfcProfileDef  inst:IfcLabel_538 ;
        ifc:position_IfcParameterizedProfileDef  inst:IfcAxis2Placement2D_2058 .

inst:IfcPositiveLengthMeasure_539
        rdf:type           ifc:IfcPositiveLengthMeasure ;
        express:hasDouble  "200."^^xsd:double .

inst:IfcRectangleProfileDef_2061
        ifc:xDim_IfcRectangleProfileDef  inst:IfcPositiveLengthMeasure_539 ;
        ifc:yDim_IfcRectangleProfileDef  inst:IfcPositiveLengthMeasure_539 .

inst:IfcSIUnit_14  rdf:type        ifc:IfcSIUnit ;
        ifc:unitType_IfcNamedUnit  ifc:LENGTHUNIT ;
        ifc:prefix_IfcSIUnit       ifc:MILLI ;
        ifc:name_IfcSIUnit         ifc:METRE .

inst:IfcAxis2Placement3D_2062
        rdf:type  ifc:IfcAxis2Placement3D .

inst:IfcCartesianPoint_40
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcAxis2Placement3D_2062
        ifc:location_IfcPlacement  inst:IfcCartesianPoint_40 .

inst:IfcDirection_32  rdf:type  ifc:IfcDirection .

inst:IfcAxis2Placement3D_2062
        ifc:axis_IfcAxis2Placement3D  inst:IfcDirection_32 .

inst:IfcDirection_36  rdf:type  ifc:IfcDirection .

inst:IfcAxis2Placement3D_2062
        ifc:refDirection_IfcAxis2Placement3D  inst:IfcDirection_36 .

inst:IfcSIUnit_15  rdf:type        ifc:IfcSIUnit ;
        ifc:unitType_IfcNamedUnit  ifc:AREAUNIT ;
        ifc:name_IfcSIUnit         ifc:SQUARE_METRE .

inst:IfcSIUnit_16  rdf:type        ifc:IfcSIUnit ;
        ifc:unitType_IfcNamedUnit  ifc:VOLUMEUNIT ;
        ifc:name_IfcSIUnit         ifc:CUBIC_METRE .

inst:IfcSIUnit_17  rdf:type        ifc:IfcSIUnit ;
        ifc:unitType_IfcNamedUnit  ifc:PLANEANGLEUNIT ;
        ifc:name_IfcSIUnit         ifc:RADIAN .

inst:IfcExtrudedAreaSolid_2065
        rdf:type                        ifc:IfcExtrudedAreaSolid ;
        ifc:sweptArea_IfcSweptAreaSolid  inst:IfcRectangleProfileDef_2061 ;
        ifc:position_IfcSweptAreaSolid  inst:IfcAxis2Placement3D_2062 ;
        ifc:extrudedDirection_IfcExtrudedAreaSolid  inst:IfcDirection_36 .

inst:IfcPositiveLengthMeasure_540
        rdf:type           ifc:IfcPositiveLengthMeasure ;
        express:hasDouble  "12000."^^xsd:double .

inst:IfcExtrudedAreaSolid_2065
        ifc:depth_IfcExtrudedAreaSolid  inst:IfcPositiveLengthMeasure_540 .

inst:IfcMeasureWithUnit_18
        rdf:type  ifc:IfcMeasureWithUnit .

inst:IfcPlaneAngleMeasure_541
        rdf:type           ifc:IfcPlaneAngleMeasure ;
        express:hasDouble  "0.017453293"^^xsd:double .

inst:IfcMeasureWithUnit_18
        ifc:valueComponent_IfcMeasureWithUnit  inst:IfcPlaneAngleMeasure_541 ;
        ifc:unitComponent_IfcMeasureWithUnit  inst:IfcSIUnit_17 .

inst:IfcDimensionalExponents_19
        rdf:type  ifc:IfcDimensionalExponents .

inst:INTEGER_542  rdf:type  express:INTEGER ;
        express:hasInteger  0 .

inst:IfcDimensionalExponents_19
        ifc:lengthExponent_IfcDimensionalExponents  inst:INTEGER_542 ;
        ifc:massExponent_IfcDimensionalExponents  inst:INTEGER_542 ;
        ifc:timeExponent_IfcDimensionalExponents  inst:INTEGER_542 ;
        ifc:electricCurrentExponent_IfcDimensionalExponents  inst:INTEGER_542 ;
        ifc:thermodynamicTemperatureExponent_IfcDimensionalExponents  inst:INTEGER_542 ;
        ifc:amountOfSubstanceExponent_IfcDimensionalExponents  inst:INTEGER_542 ;
        ifc:luminousIntensityExponent_IfcDimensionalExponents  inst:INTEGER_542 .

inst:IfcConversionBasedUnit_20
        rdf:type                     ifc:IfcConversionBasedUnit ;
        ifc:dimensions_IfcNamedUnit  inst:IfcDimensionalExponents_19 ;
        ifc:unitType_IfcNamedUnit    ifc:PLANEANGLEUNIT .

inst:IfcLabel_543  rdf:type  ifc:IfcLabel ;
        express:hasString  "DEGREE" .

inst:IfcConversionBasedUnit_20
        ifc:name_IfcConversionBasedUnit  inst:IfcLabel_543 ;
        ifc:conversionFactor_IfcConversionBasedUnit  inst:IfcMeasureWithUnit_18 .

inst:IfcShapeRepresentation_2068
        rdf:type  ifc:IfcShapeRepresentation .

inst:IfcGeometricRepresentationSubContext_53
        rdf:type  ifc:IfcGeometricRepresentationSubContext .

inst:IfcShapeRepresentation_2068
        ifc:contextOfItems_IfcRepresentation  inst:IfcGeometricRepresentationSubContext_53 .

inst:IfcLabel_544  rdf:type  ifc:IfcLabel ;
        express:hasString  "Body" .

inst:IfcShapeRepresentation_2068
        ifc:representationIdentifier_IfcRepresentation  inst:IfcLabel_544 .

inst:IfcLabel_545  rdf:type  ifc:IfcLabel ;
        express:hasString  "SweptSolid" .

inst:IfcShapeRepresentation_2068
        ifc:representationType_IfcRepresentation  inst:IfcLabel_545 ;
        ifc:items_IfcRepresentation  inst:IfcExtrudedAreaSolid_2065 .

inst:IfcSIUnit_21  rdf:type        ifc:IfcSIUnit ;
        ifc:unitType_IfcNamedUnit  ifc:SOLIDANGLEUNIT ;
        ifc:name_IfcSIUnit         ifc:STERADIAN .

inst:IfcSIUnit_22  rdf:type        ifc:IfcSIUnit ;
        ifc:unitType_IfcNamedUnit  ifc:MASSUNIT ;
        ifc:name_IfcSIUnit         ifc:GRAM .

inst:IfcCartesianPoint_1046
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_546
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_1046
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_546 .

inst:IfcLengthMeasure_List_547
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_546
        list:hasContents  inst:IfcReal_529 ;
        list:hasNext      inst:IfcLengthMeasure_List_547 .

inst:IfcLengthMeasure_List_547
        list:hasContents  inst:IfcReal_529 .

inst:IfcSIUnit_23  rdf:type        ifc:IfcSIUnit ;
        ifc:unitType_IfcNamedUnit  ifc:TIMEUNIT ;
        ifc:name_IfcSIUnit         ifc:SECOND .

inst:IfcSIUnit_24  rdf:type        ifc:IfcSIUnit ;
        ifc:unitType_IfcNamedUnit  ifc:THERMODYNAMICTEMPERATUREUNIT ;
        ifc:name_IfcSIUnit         ifc:DEGREE_CELSIUS .

inst:IfcSIUnit_25  rdf:type        ifc:IfcSIUnit ;
        ifc:unitType_IfcNamedUnit  ifc:LUMINOUSINTENSITYUNIT ;
        ifc:name_IfcSIUnit         ifc:LUMEN .

inst:IfcUnitAssignment_26
        rdf:type                     ifc:IfcUnitAssignment ;
        ifc:units_IfcUnitAssignment  inst:IfcSIUnit_14 ;
        ifc:units_IfcUnitAssignment  inst:IfcSIUnit_15 ;
        ifc:units_IfcUnitAssignment  inst:IfcSIUnit_16 ;
        ifc:units_IfcUnitAssignment  inst:IfcConversionBasedUnit_20 ;
        ifc:units_IfcUnitAssignment  inst:IfcSIUnit_21 ;
        ifc:units_IfcUnitAssignment  inst:IfcSIUnit_22 ;
        ifc:units_IfcUnitAssignment  inst:IfcSIUnit_23 ;
        ifc:units_IfcUnitAssignment  inst:IfcSIUnit_24 ;
        ifc:units_IfcUnitAssignment  inst:IfcSIUnit_25 .

inst:IfcAxis2Placement2D_1050
        rdf:type                   ifc:IfcAxis2Placement2D ;
        ifc:location_IfcPlacement  inst:IfcCartesianPoint_1046 ;
        ifc:refDirection_IfcAxis2Placement2D  inst:IfcDirection_2050 .

inst:IfcProductDefinitionShape_2074
        rdf:type  ifc:IfcProductDefinitionShape .

inst:IfcRepresentation_List_548
        rdf:type  ifc:IfcRepresentation_List .

inst:IfcProductDefinitionShape_2074
        ifc:representations_IfcProductRepresentation  inst:IfcRepresentation_List_548 .

inst:IfcRepresentation_List_548
        list:hasContents  inst:IfcShapeRepresentation_2068 .

inst:IfcDirection_28  rdf:type  ifc:IfcDirection .

inst:IfcReal_List_549
        rdf:type  ifc:IfcReal_List .

inst:IfcDirection_28  ifc:directionRatios_IfcDirection  inst:IfcReal_List_549 .

inst:IfcReal_List_550
        rdf:type  ifc:IfcReal_List .

inst:IfcReal_List_551
        rdf:type  ifc:IfcReal_List .

inst:IfcReal_List_549
        list:hasContents  inst:IfcReal_528 ;
        list:hasNext      inst:IfcReal_List_550 .

inst:IfcReal_List_550
        list:hasContents  inst:IfcReal_529 ;
        list:hasNext      inst:IfcReal_List_551 .

inst:IfcReal_List_551
        list:hasContents  inst:IfcReal_529 .

inst:IfcRectangleProfileDef_1053
        rdf:type                       ifc:IfcRectangleProfileDef ;
        ifc:profileType_IfcProfileDef  ifc:AREA ;
        ifc:profileName_IfcProfileDef  inst:IfcLabel_538 ;
        ifc:position_IfcParameterizedProfileDef  inst:IfcAxis2Placement2D_1050 .

inst:IfcPositiveLengthMeasure_552
        rdf:type           ifc:IfcPositiveLengthMeasure ;
        express:hasDouble  "300."^^xsd:double .

inst:IfcRectangleProfileDef_1053
        ifc:xDim_IfcRectangleProfileDef  inst:IfcPositiveLengthMeasure_552 ;
        ifc:yDim_IfcRectangleProfileDef  inst:IfcPositiveLengthMeasure_552 .

inst:IfcAxis2Placement3D_1054
        rdf:type                      ifc:IfcAxis2Placement3D ;
        ifc:location_IfcPlacement     inst:IfcCartesianPoint_40 ;
        ifc:axis_IfcAxis2Placement3D  inst:IfcDirection_36 ;
        ifc:refDirection_IfcAxis2Placement3D  inst:IfcDirection_28 .

inst:IfcCartesianPoint_2078
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_553
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_2078
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_553 .

inst:IfcLengthMeasure_List_554
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_555
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_556
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-14000."^^xsd:double .

inst:IfcLengthMeasure_List_553
        list:hasContents  inst:IfcLengthMeasure_556 ;
        list:hasNext      inst:IfcLengthMeasure_List_554 .

inst:IfcLengthMeasure_557
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "4000."^^xsd:double .

inst:IfcLengthMeasure_List_554
        list:hasContents  inst:IfcLengthMeasure_557 ;
        list:hasNext      inst:IfcLengthMeasure_List_555 .

inst:IfcLengthMeasure_558
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "2800."^^xsd:double .

inst:IfcLengthMeasure_List_555
        list:hasContents  inst:IfcLengthMeasure_558 .

inst:IfcReal_List_559
        rdf:type  ifc:IfcReal_List .

inst:IfcDirection_32  ifc:directionRatios_IfcDirection  inst:IfcReal_List_559 .

inst:IfcReal_List_560
        rdf:type  ifc:IfcReal_List .

inst:IfcReal_List_561
        rdf:type  ifc:IfcReal_List .

inst:IfcReal_List_559
        list:hasContents  inst:IfcReal_529 ;
        list:hasNext      inst:IfcReal_List_560 .

inst:IfcReal_List_560
        list:hasContents  inst:IfcReal_528 ;
        list:hasNext      inst:IfcReal_List_561 .

inst:IfcReal_List_561
        list:hasContents  inst:IfcReal_529 .

inst:IfcExtrudedAreaSolid_1057
        rdf:type                        ifc:IfcExtrudedAreaSolid ;
        ifc:sweptArea_IfcSweptAreaSolid  inst:IfcRectangleProfileDef_1053 ;
        ifc:position_IfcSweptAreaSolid  inst:IfcAxis2Placement3D_1054 ;
        ifc:extrudedDirection_IfcExtrudedAreaSolid  inst:IfcDirection_36 ;
        ifc:depth_IfcExtrudedAreaSolid  inst:IfcLengthMeasure_558 .

inst:IfcAxis2Placement3D_2082
        rdf:type                      ifc:IfcAxis2Placement3D ;
        ifc:location_IfcPlacement     inst:IfcCartesianPoint_2078 ;
        ifc:axis_IfcAxis2Placement3D  inst:IfcDirection_36 ;
        ifc:refDirection_IfcAxis2Placement3D  inst:IfcDirection_28 .

inst:IfcReal_List_562
        rdf:type  ifc:IfcReal_List .

inst:IfcDirection_36  ifc:directionRatios_IfcDirection  inst:IfcReal_List_562 .

inst:IfcReal_List_563
        rdf:type  ifc:IfcReal_List .

inst:IfcReal_List_564
        rdf:type  ifc:IfcReal_List .

inst:IfcReal_List_562
        list:hasContents  inst:IfcReal_529 ;
        list:hasNext      inst:IfcReal_List_563 .

inst:IfcReal_List_563
        list:hasContents  inst:IfcReal_529 ;
        list:hasNext      inst:IfcReal_List_564 .

inst:IfcReal_List_564
        list:hasContents  inst:IfcReal_528 .

inst:IfcShapeRepresentation_1060
        rdf:type                     ifc:IfcShapeRepresentation ;
        ifc:contextOfItems_IfcRepresentation  inst:IfcGeometricRepresentationSubContext_53 ;
        ifc:representationIdentifier_IfcRepresentation  inst:IfcLabel_544 ;
        ifc:representationType_IfcRepresentation  inst:IfcLabel_545 ;
        ifc:items_IfcRepresentation  inst:IfcExtrudedAreaSolid_1057 .

inst:IfcLocalPlacement_2085
        rdf:type  ifc:IfcLocalPlacement .

inst:IfcLocalPlacement_90
        rdf:type  ifc:IfcLocalPlacement .

inst:IfcLocalPlacement_2085
        ifc:placementRelTo_IfcLocalPlacement  inst:IfcLocalPlacement_90 ;
        ifc:relativePlacement_IfcLocalPlacement  inst:IfcAxis2Placement3D_2082 .

inst:IfcLengthMeasure_List_565
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_40
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_565 .

inst:IfcLengthMeasure_List_566
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_567
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_565
        list:hasContents  inst:IfcReal_529 ;
        list:hasNext      inst:IfcLengthMeasure_List_566 .

inst:IfcLengthMeasure_List_566
        list:hasContents  inst:IfcReal_529 ;
        list:hasNext      inst:IfcLengthMeasure_List_567 .

inst:IfcLengthMeasure_List_567
        list:hasContents  inst:IfcReal_529 .

inst:IfcRelAssociatesMaterial_2088
        rdf:type  ifc:IfcRelAssociatesMaterial .

inst:IfcGloballyUniqueId_568
        rdf:type           ifc:IfcGloballyUniqueId ;
        express:hasString  "395DGAbFz7IRhGOTHDhBEA" .

inst:IfcRelAssociatesMaterial_2088
        ifc:globalId_IfcRoot      inst:IfcGloballyUniqueId_568 ;
        ifc:ownerHistory_IfcRoot  inst:IfcOwnerHistory_13 .

inst:IfcBeam_2031  rdf:type  ifc:IfcBeam .

inst:IfcRelAssociatesMaterial_2088
        ifc:relatedObjects_IfcRelAssociates  inst:IfcBeam_2031 .

inst:IfcMaterial_354  rdf:type  ifc:IfcMaterial .

inst:IfcRelAssociatesMaterial_2088
        ifc:relatingMaterial_IfcRelAssociatesMaterial  inst:IfcMaterial_354 .

inst:IfcProductDefinitionShape_1066
        rdf:type  ifc:IfcProductDefinitionShape .

inst:IfcRepresentation_List_569
        rdf:type  ifc:IfcRepresentation_List .

inst:IfcProductDefinitionShape_1066
        ifc:representations_IfcProductRepresentation  inst:IfcRepresentation_List_569 .

inst:IfcRepresentation_List_569
        list:hasContents  inst:IfcShapeRepresentation_1060 .

inst:IfcVirtualGridIntersection_1070
        rdf:type  ifc:IfcVirtualGridIntersection .

inst:IfcGridAxis_List_570
        rdf:type  ifc:IfcGridAxis_List .

inst:IfcVirtualGridIntersection_1070
        ifc:intersectingAxes_IfcVirtualGridIntersection  inst:IfcGridAxis_List_570 .

inst:IfcGridAxis_List_571
        rdf:type  ifc:IfcGridAxis_List .

inst:IfcGridAxis_251  rdf:type  ifc:IfcGridAxis .

inst:IfcGridAxis_List_570
        list:hasContents  inst:IfcGridAxis_251 ;
        list:hasNext      inst:IfcGridAxis_List_571 .

inst:IfcGridAxis_115  rdf:type  ifc:IfcGridAxis .

inst:IfcGridAxis_List_571
        list:hasContents  inst:IfcGridAxis_115 .

inst:IfcLengthMeasure_List_572
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcVirtualGridIntersection_1070
        ifc:offsetDistances_IfcVirtualGridIntersection  inst:IfcLengthMeasure_List_572 .

inst:IfcLengthMeasure_List_573
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_574
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_572
        list:hasContents  inst:IfcReal_529 ;
        list:hasNext      inst:IfcLengthMeasure_List_573 .

inst:IfcLengthMeasure_List_573
        list:hasContents  inst:IfcReal_529 ;
        list:hasNext      inst:IfcLengthMeasure_List_574 .

inst:IfcLengthMeasure_List_574
        list:hasContents  inst:IfcReal_529 .

inst:IfcDirection_47  rdf:type  ifc:IfcDirection .

inst:IfcReal_List_575
        rdf:type  ifc:IfcReal_List .

inst:IfcDirection_47  ifc:directionRatios_IfcDirection  inst:IfcReal_List_575 .

inst:IfcReal_List_576
        rdf:type  ifc:IfcReal_List .

inst:IfcReal_List_575
        list:hasContents  inst:IfcReal_529 ;
        list:hasNext      inst:IfcReal_List_576 .

inst:IfcReal_List_576
        list:hasContents  inst:IfcReal_528 .

inst:IfcGridPlacement_1073
        rdf:type  ifc:IfcGridPlacement ;
        ifc:placementLocation_IfcGridPlacement  inst:IfcVirtualGridIntersection_1070 .

inst:IfcGeometricRepresentationContext_51
        rdf:type  ifc:IfcGeometricRepresentationContext .

inst:IfcLabel_577  rdf:type  ifc:IfcLabel ;
        express:hasString  "Model" .

inst:IfcGeometricRepresentationContext_51
        ifc:contextType_IfcRepresentationContext  inst:IfcLabel_577 .

inst:IfcDimensionCount_578
        rdf:type            ifc:IfcDimensionCount ;
        express:hasInteger  3 .

inst:IfcGeometricRepresentationContext_51
        ifc:coordinateSpaceDimension_IfcGeometricRepresentationContext  inst:IfcDimensionCount_578 .

inst:IfcReal_579  rdf:type  ifc:IfcReal ;
        express:hasDouble  1.0000000E-5 .

inst:IfcGeometricRepresentationContext_51
        ifc:precision_IfcGeometricRepresentationContext  inst:IfcReal_579 ;
        ifc:worldCoordinateSystem_IfcGeometricRepresentationContext  inst:IfcAxis2Placement3D_1054 ;
        ifc:trueNorth_IfcGeometricRepresentationContext  inst:IfcDirection_47 .

inst:IfcGeometricRepresentationSubContext_52
        rdf:type  ifc:IfcGeometricRepresentationSubContext .

inst:IfcLabel_580  rdf:type  ifc:IfcLabel ;
        express:hasString  "FootPrint" .

inst:IfcGeometricRepresentationSubContext_52
        ifc:contextIdentifier_IfcRepresentationContext  inst:IfcLabel_580 ;
        ifc:contextType_IfcRepresentationContext  inst:IfcLabel_577 ;
        ifc:parentContext_IfcGeometricRepresentationSubContext  inst:IfcGeometricRepresentationContext_51 ;
        ifc:targetView_IfcGeometricRepresentationSubContext  ifc:MODEL_VIEW .

inst:IfcRelAssociatesMaterial_1076
        rdf:type  ifc:IfcRelAssociatesMaterial .

inst:IfcGloballyUniqueId_581
        rdf:type           ifc:IfcGloballyUniqueId ;
        express:hasString  "0L2mkv1m9AjAgDU16VxxRY" .

inst:IfcRelAssociatesMaterial_1076
        ifc:globalId_IfcRoot      inst:IfcGloballyUniqueId_581 ;
        ifc:ownerHistory_IfcRoot  inst:IfcOwnerHistory_13 .

inst:IfcColumn_1023  rdf:type  ifc:IfcColumn .

inst:IfcRelAssociatesMaterial_1076
        ifc:relatedObjects_IfcRelAssociates  inst:IfcColumn_1023 ;
        ifc:relatingMaterial_IfcRelAssociatesMaterial  inst:IfcMaterial_354 .

inst:IfcBeam_2100  rdf:type  ifc:IfcBeam .

inst:IfcGloballyUniqueId_582
        rdf:type           ifc:IfcGloballyUniqueId ;
        express:hasString  "3R6gApVrPDBBjeleun$ig8" .

inst:IfcBeam_2100  ifc:globalId_IfcRoot  inst:IfcGloballyUniqueId_582 ;
        ifc:ownerHistory_IfcRoot  inst:IfcOwnerHistory_13 .

inst:IfcLabel_583  rdf:type  ifc:IfcLabel ;
        express:hasString  "BMR - 001" .

inst:IfcBeam_2100  ifc:name_IfcRoot  inst:IfcLabel_583 .

inst:IfcLocalPlacement_2154
        rdf:type  ifc:IfcLocalPlacement .

inst:IfcBeam_2100  ifc:objectPlacement_IfcProduct  inst:IfcLocalPlacement_2154 .

inst:IfcProductDefinitionShape_2143
        rdf:type  ifc:IfcProductDefinitionShape .

inst:IfcBeam_2100  ifc:representation_IfcProduct  inst:IfcProductDefinitionShape_2143 .

inst:IfcGeometricRepresentationSubContext_53
        ifc:contextIdentifier_IfcRepresentationContext  inst:IfcLabel_544 ;
        ifc:contextType_IfcRepresentationContext  inst:IfcLabel_577 ;
        ifc:parentContext_IfcGeometricRepresentationSubContext  inst:IfcGeometricRepresentationContext_51 ;
        ifc:targetView_IfcGeometricRepresentationSubContext  ifc:MODEL_VIEW .

inst:IfcProject_54  rdf:type  ifc:IfcProject .

inst:IfcGloballyUniqueId_584
        rdf:type           ifc:IfcGloballyUniqueId ;
        express:hasString  "2btxazyiP8WAIfB0dw8meA" .

inst:IfcProject_54  ifc:globalId_IfcRoot  inst:IfcGloballyUniqueId_584 ;
        ifc:ownerHistory_IfcRoot  inst:IfcOwnerHistory_13 .

inst:IfcLabel_585  rdf:type  ifc:IfcLabel ;
        express:hasString  "Default Project" .

inst:IfcProject_54  ifc:name_IfcRoot   inst:IfcLabel_585 ;
        ifc:representationContexts_IfcContext  inst:IfcGeometricRepresentationContext_51 ;
        ifc:unitsInContext_IfcContext  inst:IfcUnitAssignment_26 .

inst:IfcLocalPlacement_61
        rdf:type  ifc:IfcLocalPlacement ;
        ifc:relativePlacement_IfcLocalPlacement  inst:IfcAxis2Placement3D_1054 .

inst:IfcColumn_1086  rdf:type  ifc:IfcColumn .

inst:IfcGloballyUniqueId_586
        rdf:type           ifc:IfcGloballyUniqueId ;
        express:hasString  "3sNRA7UHP0x9bDzO0SwHVt" .

inst:IfcColumn_1086  ifc:globalId_IfcRoot  inst:IfcGloballyUniqueId_586 ;
        ifc:ownerHistory_IfcRoot  inst:IfcOwnerHistory_13 .

inst:IfcLabel_587  rdf:type  ifc:IfcLabel ;
        express:hasString  "CRE - 001" .

inst:IfcColumn_1086  ifc:name_IfcRoot  inst:IfcLabel_587 .

inst:IfcGridPlacement_1136
        rdf:type  ifc:IfcGridPlacement .

inst:IfcColumn_1086  ifc:objectPlacement_IfcProduct  inst:IfcGridPlacement_1136 .

inst:IfcProductDefinitionShape_1129
        rdf:type  ifc:IfcProductDefinitionShape .

inst:IfcColumn_1086  ifc:representation_IfcProduct  inst:IfcProductDefinitionShape_1129 .

inst:IfcSite_64  rdf:type  ifc:IfcSite .

inst:IfcGloballyUniqueId_588
        rdf:type           ifc:IfcGloballyUniqueId ;
        express:hasString  "0AU$p8piP1M9KEzK5w32XM" .

inst:IfcSite_64  ifc:globalId_IfcRoot  inst:IfcGloballyUniqueId_588 ;
        ifc:ownerHistory_IfcRoot  inst:IfcOwnerHistory_13 .

inst:IfcLabel_589  rdf:type  ifc:IfcLabel ;
        express:hasString  "Default Site" .

inst:IfcSite_64  ifc:name_IfcRoot       inst:IfcLabel_589 ;
        ifc:objectPlacement_IfcProduct  inst:IfcLocalPlacement_61 ;
        ifc:compositionType_IfcSpatialStructureElement  ifc:ELEMENT .

inst:IfcCompoundPlaneAngleMeasure_590
        rdf:type  ifc:IfcCompoundPlaneAngleMeasure .

inst:IfcSite_64  ifc:refLatitude_IfcSite  inst:IfcCompoundPlaneAngleMeasure_590 .

inst:IfcCompoundPlaneAngleMeasure_591
        rdf:type  ifc:IfcCompoundPlaneAngleMeasure .

inst:IfcCompoundPlaneAngleMeasure_592
        rdf:type  ifc:IfcCompoundPlaneAngleMeasure .

inst:INTEGER_593  rdf:type  express:INTEGER ;
        express:hasInteger  24 .

inst:IfcCompoundPlaneAngleMeasure_590
        list:hasContents  inst:INTEGER_593 ;
        list:hasNext      inst:IfcCompoundPlaneAngleMeasure_591 .

inst:INTEGER_594  rdf:type  express:INTEGER ;
        express:hasInteger  28 .

inst:IfcCompoundPlaneAngleMeasure_591
        list:hasContents  inst:INTEGER_594 ;
        list:hasNext      inst:IfcCompoundPlaneAngleMeasure_592 .

inst:IfcCompoundPlaneAngleMeasure_592
        list:hasContents  inst:INTEGER_542 .

inst:IfcCompoundPlaneAngleMeasure_595
        rdf:type  ifc:IfcCompoundPlaneAngleMeasure .

inst:IfcSite_64  ifc:refLongitude_IfcSite  inst:IfcCompoundPlaneAngleMeasure_595 .

inst:IfcCompoundPlaneAngleMeasure_596
        rdf:type  ifc:IfcCompoundPlaneAngleMeasure .

inst:IfcCompoundPlaneAngleMeasure_597
        rdf:type  ifc:IfcCompoundPlaneAngleMeasure .

inst:INTEGER_598  rdf:type  express:INTEGER ;
        express:hasInteger  54 .

inst:IfcCompoundPlaneAngleMeasure_595
        list:hasContents  inst:INTEGER_598 ;
        list:hasNext      inst:IfcCompoundPlaneAngleMeasure_596 .

inst:INTEGER_599  rdf:type  express:INTEGER ;
        express:hasInteger  25 .

inst:IfcCompoundPlaneAngleMeasure_596
        list:hasContents  inst:INTEGER_599 ;
        list:hasNext      inst:IfcCompoundPlaneAngleMeasure_597 .

inst:IfcCompoundPlaneAngleMeasure_597
        list:hasContents  inst:INTEGER_542 .

inst:IfcLocalPlacement_74
        rdf:type  ifc:IfcLocalPlacement ;
        ifc:placementRelTo_IfcLocalPlacement  inst:IfcLocalPlacement_61 ;
        ifc:relativePlacement_IfcLocalPlacement  inst:IfcAxis2Placement3D_1054 .

inst:IfcBuilding_77  rdf:type  ifc:IfcBuilding .

inst:IfcGloballyUniqueId_600
        rdf:type           ifc:IfcGloballyUniqueId ;
        express:hasString  "0IuzxQ0mr3BOdtlAS8ZLt_" .

inst:IfcBuilding_77  ifc:globalId_IfcRoot  inst:IfcGloballyUniqueId_600 ;
        ifc:ownerHistory_IfcRoot  inst:IfcOwnerHistory_13 .

inst:IfcLabel_601  rdf:type  ifc:IfcLabel ;
        express:hasString  "Default Building" .

inst:IfcBuilding_77  ifc:name_IfcRoot   inst:IfcLabel_601 ;
        ifc:objectPlacement_IfcProduct  inst:IfcLocalPlacement_74 ;
        ifc:compositionType_IfcSpatialStructureElement  ifc:ELEMENT .

inst:IfcAxis2Placement2D_2127
        rdf:type                   ifc:IfcAxis2Placement2D ;
        ifc:location_IfcPlacement  inst:IfcCartesianPoint_2054 ;
        ifc:refDirection_IfcAxis2Placement2D  inst:IfcDirection_2050 .

inst:IfcRectangleProfileDef_2130
        rdf:type                       ifc:IfcRectangleProfileDef ;
        ifc:profileType_IfcProfileDef  ifc:AREA ;
        ifc:profileName_IfcProfileDef  inst:IfcLabel_538 ;
        ifc:position_IfcParameterizedProfileDef  inst:IfcAxis2Placement2D_2127 ;
        ifc:xDim_IfcRectangleProfileDef  inst:IfcPositiveLengthMeasure_539 ;
        ifc:yDim_IfcRectangleProfileDef  inst:IfcPositiveLengthMeasure_539 .

inst:IfcExtrudedAreaSolid_2134
        rdf:type                        ifc:IfcExtrudedAreaSolid ;
        ifc:sweptArea_IfcSweptAreaSolid  inst:IfcRectangleProfileDef_2130 ;
        ifc:position_IfcSweptAreaSolid  inst:IfcAxis2Placement3D_2062 ;
        ifc:extrudedDirection_IfcExtrudedAreaSolid  inst:IfcDirection_36 ;
        ifc:depth_IfcExtrudedAreaSolid  inst:IfcPositiveLengthMeasure_540 .

inst:IfcAxis2Placement2D_1113
        rdf:type                   ifc:IfcAxis2Placement2D ;
        ifc:location_IfcPlacement  inst:IfcCartesianPoint_1046 ;
        ifc:refDirection_IfcAxis2Placement2D  inst:IfcDirection_2050 .

inst:IfcShapeRepresentation_2137
        rdf:type                     ifc:IfcShapeRepresentation ;
        ifc:contextOfItems_IfcRepresentation  inst:IfcGeometricRepresentationSubContext_53 ;
        ifc:representationIdentifier_IfcRepresentation  inst:IfcLabel_544 ;
        ifc:representationType_IfcRepresentation  inst:IfcLabel_545 ;
        ifc:items_IfcRepresentation  inst:IfcExtrudedAreaSolid_2134 .

inst:IfcLocalPlacement_90
        ifc:placementRelTo_IfcLocalPlacement  inst:IfcLocalPlacement_74 ;
        ifc:relativePlacement_IfcLocalPlacement  inst:IfcAxis2Placement3D_1054 .

inst:IfcRectangleProfileDef_1116
        rdf:type                       ifc:IfcRectangleProfileDef ;
        ifc:profileType_IfcProfileDef  ifc:AREA ;
        ifc:profileName_IfcProfileDef  inst:IfcLabel_538 ;
        ifc:position_IfcParameterizedProfileDef  inst:IfcAxis2Placement2D_1113 ;
        ifc:xDim_IfcRectangleProfileDef  inst:IfcPositiveLengthMeasure_552 ;
        ifc:yDim_IfcRectangleProfileDef  inst:IfcPositiveLengthMeasure_552 .

inst:IfcBuildingStorey_93
        rdf:type  ifc:IfcBuildingStorey .

inst:IfcGloballyUniqueId_602
        rdf:type           ifc:IfcGloballyUniqueId ;
        express:hasString  "3AG_0oB1zBfhm3BFIxGOjF" .

inst:IfcBuildingStorey_93
        ifc:globalId_IfcRoot      inst:IfcGloballyUniqueId_602 ;
        ifc:ownerHistory_IfcRoot  inst:IfcOwnerHistory_13 .

inst:IfcLabel_603  rdf:type  ifc:IfcLabel ;
        express:hasString  "Ground Floor" .

inst:IfcBuildingStorey_93
        ifc:name_IfcRoot                inst:IfcLabel_603 ;
        ifc:objectPlacement_IfcProduct  inst:IfcLocalPlacement_90 ;
        ifc:compositionType_IfcSpatialStructureElement  ifc:ELEMENT ;
        ifc:elevation_IfcBuildingStorey  inst:IfcReal_529 .

inst:IfcRepresentation_List_604
        rdf:type  ifc:IfcRepresentation_List .

inst:IfcProductDefinitionShape_2143
        ifc:representations_IfcProductRepresentation  inst:IfcRepresentation_List_604 .

inst:IfcRepresentation_List_604
        list:hasContents  inst:IfcShapeRepresentation_2137 .

inst:IfcExtrudedAreaSolid_1120
        rdf:type                        ifc:IfcExtrudedAreaSolid ;
        ifc:sweptArea_IfcSweptAreaSolid  inst:IfcRectangleProfileDef_1116 ;
        ifc:position_IfcSweptAreaSolid  inst:IfcAxis2Placement3D_1054 ;
        ifc:extrudedDirection_IfcExtrudedAreaSolid  inst:IfcDirection_36 ;
        ifc:depth_IfcExtrudedAreaSolid  inst:IfcLengthMeasure_558 .

inst:IfcShapeRepresentation_1123
        rdf:type                     ifc:IfcShapeRepresentation ;
        ifc:contextOfItems_IfcRepresentation  inst:IfcGeometricRepresentationSubContext_53 ;
        ifc:representationIdentifier_IfcRepresentation  inst:IfcLabel_544 ;
        ifc:representationType_IfcRepresentation  inst:IfcLabel_545 ;
        ifc:items_IfcRepresentation  inst:IfcExtrudedAreaSolid_1120 .

inst:IfcCartesianPoint_2147
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_605
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_2147
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_605 .

inst:IfcLengthMeasure_List_606
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_607
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_608
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-10000."^^xsd:double .

inst:IfcLengthMeasure_List_605
        list:hasContents  inst:IfcLengthMeasure_608 ;
        list:hasNext      inst:IfcLengthMeasure_List_606 .

inst:IfcLengthMeasure_List_606
        list:hasContents  inst:IfcLengthMeasure_557 ;
        list:hasNext      inst:IfcLengthMeasure_List_607 .

inst:IfcLengthMeasure_List_607
        list:hasContents  inst:IfcLengthMeasure_558 .

inst:IfcAxis2Placement3D_2151
        rdf:type                      ifc:IfcAxis2Placement3D ;
        ifc:location_IfcPlacement     inst:IfcCartesianPoint_2147 ;
        ifc:axis_IfcAxis2Placement3D  inst:IfcDirection_36 ;
        ifc:refDirection_IfcAxis2Placement3D  inst:IfcDirection_28 .

inst:IfcRepresentation_List_609
        rdf:type  ifc:IfcRepresentation_List .

inst:IfcProductDefinitionShape_1129
        ifc:representations_IfcProductRepresentation  inst:IfcRepresentation_List_609 .

inst:IfcRepresentation_List_609
        list:hasContents  inst:IfcShapeRepresentation_1123 .

inst:IfcLocalPlacement_2154
        ifc:placementRelTo_IfcLocalPlacement  inst:IfcLocalPlacement_90 ;
        ifc:relativePlacement_IfcLocalPlacement  inst:IfcAxis2Placement3D_2151 .

inst:IfcCartesianPoint_107
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_610
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_107
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_610 .

inst:IfcLengthMeasure_List_611
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_612
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "23000."^^xsd:double .

inst:IfcLengthMeasure_List_610
        list:hasContents  inst:IfcLengthMeasure_612 ;
        list:hasNext      inst:IfcLengthMeasure_List_611 .

inst:IfcLengthMeasure_List_611
        list:hasContents  inst:IfcReal_529 .

inst:IfcVirtualGridIntersection_1133
        rdf:type  ifc:IfcVirtualGridIntersection .

inst:IfcGridAxis_List_613
        rdf:type  ifc:IfcGridAxis_List .

inst:IfcVirtualGridIntersection_1133
        ifc:intersectingAxes_IfcVirtualGridIntersection  inst:IfcGridAxis_List_613 .

inst:IfcGridAxis_List_614
        rdf:type  ifc:IfcGridAxis_List .

inst:IfcGridAxis_234  rdf:type  ifc:IfcGridAxis .

inst:IfcGridAxis_List_613
        list:hasContents  inst:IfcGridAxis_234 ;
        list:hasNext      inst:IfcGridAxis_List_614 .

inst:IfcGridAxis_183  rdf:type  ifc:IfcGridAxis .

inst:IfcGridAxis_List_614
        list:hasContents  inst:IfcGridAxis_183 .

inst:IfcLengthMeasure_List_615
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcVirtualGridIntersection_1133
        ifc:offsetDistances_IfcVirtualGridIntersection  inst:IfcLengthMeasure_List_615 .

inst:IfcLengthMeasure_List_616
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_617
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_615
        list:hasContents  inst:IfcReal_529 ;
        list:hasNext      inst:IfcLengthMeasure_List_616 .

inst:IfcLengthMeasure_List_616
        list:hasContents  inst:IfcReal_529 ;
        list:hasNext      inst:IfcLengthMeasure_List_617 .

inst:IfcLengthMeasure_List_617
        list:hasContents  inst:IfcReal_529 .

inst:IfcRelAssociatesMaterial_2157
        rdf:type  ifc:IfcRelAssociatesMaterial .

inst:IfcGloballyUniqueId_618
        rdf:type           ifc:IfcGloballyUniqueId ;
        express:hasString  "0bQ8chmun9UOk$Z_NNlmg_" .

inst:IfcRelAssociatesMaterial_2157
        ifc:globalId_IfcRoot      inst:IfcGloballyUniqueId_618 ;
        ifc:ownerHistory_IfcRoot  inst:IfcOwnerHistory_13 ;
        ifc:relatedObjects_IfcRelAssociates  inst:IfcBeam_2100 ;
        ifc:relatingMaterial_IfcRelAssociatesMaterial  inst:IfcMaterial_354 .

inst:IfcPolyline_111  rdf:type  ifc:IfcPolyline .

inst:IfcCartesianPoint_List_619
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyline_111  ifc:points_IfcPolyline  inst:IfcCartesianPoint_List_619 .

inst:IfcCartesianPoint_List_620
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_619
        list:hasContents  inst:IfcCartesianPoint_1046 ;
        list:hasNext      inst:IfcCartesianPoint_List_620 .

inst:IfcCartesianPoint_List_620
        list:hasContents  inst:IfcCartesianPoint_107 .

inst:IfcGridPlacement_1136
        ifc:placementLocation_IfcGridPlacement  inst:IfcVirtualGridIntersection_1133 .

inst:IfcLabel_621  rdf:type  ifc:IfcLabel ;
        express:hasString  "5" .

inst:IfcGridAxis_115  ifc:axisTag_IfcGridAxis  inst:IfcLabel_621 ;
        ifc:axisCurve_IfcGridAxis  inst:IfcPolyline_111 .

inst:IfcBoolean_622  rdf:type  ifc:IfcBoolean ;
        express:hasBoolean  true .

inst:IfcGridAxis_115  ifc:sameSense_IfcGridAxis  inst:IfcBoolean_622 .

inst:IfcRelAssociatesMaterial_1139
        rdf:type  ifc:IfcRelAssociatesMaterial .

inst:IfcGloballyUniqueId_623
        rdf:type           ifc:IfcGloballyUniqueId ;
        express:hasString  "2DFTpVzLb0GOz94dnHBIIq" .

inst:IfcRelAssociatesMaterial_1139
        ifc:globalId_IfcRoot      inst:IfcGloballyUniqueId_623 ;
        ifc:ownerHistory_IfcRoot  inst:IfcOwnerHistory_13 ;
        ifc:relatedObjects_IfcRelAssociates  inst:IfcColumn_1086 ;
        ifc:relatingMaterial_IfcRelAssociatesMaterial  inst:IfcMaterial_354 .

inst:IfcBeam_2167  rdf:type  ifc:IfcBeam .

inst:IfcGloballyUniqueId_624
        rdf:type           ifc:IfcGloballyUniqueId ;
        express:hasString  "0QMS1qce10WvjrKIO5PpTf" .

inst:IfcBeam_2167  ifc:globalId_IfcRoot  inst:IfcGloballyUniqueId_624 ;
        ifc:ownerHistory_IfcRoot  inst:IfcOwnerHistory_13 ;
        ifc:name_IfcRoot          inst:IfcLabel_583 .

inst:IfcLocalPlacement_2221
        rdf:type  ifc:IfcLocalPlacement .

inst:IfcBeam_2167  ifc:objectPlacement_IfcProduct  inst:IfcLocalPlacement_2221 .

inst:IfcProductDefinitionShape_2210
        rdf:type  ifc:IfcProductDefinitionShape .

inst:IfcBeam_2167  ifc:representation_IfcProduct  inst:IfcProductDefinitionShape_2210 .

inst:IfcCartesianPoint_120
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_625
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_120
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_625 .

inst:IfcLengthMeasure_List_626
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_625
        list:hasContents  inst:IfcReal_529 ;
        list:hasNext      inst:IfcLengthMeasure_List_626 .

inst:IfcLengthMeasure_627
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-2000."^^xsd:double .

inst:IfcLengthMeasure_List_626
        list:hasContents  inst:IfcLengthMeasure_627 .

inst:IfcCartesianPoint_124
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_628
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_124
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_628 .

inst:IfcLengthMeasure_List_629
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_628
        list:hasContents  inst:IfcLengthMeasure_612 ;
        list:hasNext      inst:IfcLengthMeasure_List_629 .

inst:IfcLengthMeasure_List_629
        list:hasContents  inst:IfcLengthMeasure_627 .

inst:IfcColumn_1149  rdf:type  ifc:IfcColumn .

inst:IfcGloballyUniqueId_630
        rdf:type           ifc:IfcGloballyUniqueId ;
        express:hasString  "1vABltQ550OwXnYj4H1WTw" .

inst:IfcColumn_1149  ifc:globalId_IfcRoot  inst:IfcGloballyUniqueId_630 ;
        ifc:ownerHistory_IfcRoot  inst:IfcOwnerHistory_13 ;
        ifc:name_IfcRoot          inst:IfcLabel_587 .

inst:IfcGridPlacement_1199
        rdf:type  ifc:IfcGridPlacement .

inst:IfcColumn_1149  ifc:objectPlacement_IfcProduct  inst:IfcGridPlacement_1199 .

inst:IfcProductDefinitionShape_1192
        rdf:type  ifc:IfcProductDefinitionShape .

inst:IfcColumn_1149  ifc:representation_IfcProduct  inst:IfcProductDefinitionShape_1192 .

inst:IfcPolyline_128  rdf:type  ifc:IfcPolyline .

inst:IfcCartesianPoint_List_631
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyline_128  ifc:points_IfcPolyline  inst:IfcCartesianPoint_List_631 .

inst:IfcCartesianPoint_List_632
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_631
        list:hasContents  inst:IfcCartesianPoint_120 ;
        list:hasNext      inst:IfcCartesianPoint_List_632 .

inst:IfcCartesianPoint_List_632
        list:hasContents  inst:IfcCartesianPoint_124 .

inst:IfcGridAxis_132  rdf:type  ifc:IfcGridAxis .

inst:IfcLabel_633  rdf:type  ifc:IfcLabel ;
        express:hasString  "4" .

inst:IfcGridAxis_132  ifc:axisTag_IfcGridAxis  inst:IfcLabel_633 ;
        ifc:axisCurve_IfcGridAxis  inst:IfcPolyline_128 ;
        ifc:sameSense_IfcGridAxis  inst:IfcBoolean_622 .

inst:IfcCartesianPoint_137
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_634
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_137
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_634 .

inst:IfcLengthMeasure_List_635
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_634
        list:hasContents  inst:IfcReal_529 ;
        list:hasNext      inst:IfcLengthMeasure_List_635 .

inst:IfcLengthMeasure_636
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-6000."^^xsd:double .

inst:IfcLengthMeasure_List_635
        list:hasContents  inst:IfcLengthMeasure_636 .

inst:IfcCartesianPoint_141
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_637
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_141
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_637 .

inst:IfcLengthMeasure_List_638
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_637
        list:hasContents  inst:IfcLengthMeasure_612 ;
        list:hasNext      inst:IfcLengthMeasure_List_638 .

inst:IfcLengthMeasure_List_638
        list:hasContents  inst:IfcLengthMeasure_636 .

inst:IfcPolyline_145  rdf:type  ifc:IfcPolyline .

inst:IfcCartesianPoint_List_639
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyline_145  ifc:points_IfcPolyline  inst:IfcCartesianPoint_List_639 .

inst:IfcCartesianPoint_List_640
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_639
        list:hasContents  inst:IfcCartesianPoint_137 ;
        list:hasNext      inst:IfcCartesianPoint_List_640 .

inst:IfcCartesianPoint_List_640
        list:hasContents  inst:IfcCartesianPoint_141 .

inst:IfcAxis2Placement2D_2194
        rdf:type                   ifc:IfcAxis2Placement2D ;
        ifc:location_IfcPlacement  inst:IfcCartesianPoint_2054 ;
        ifc:refDirection_IfcAxis2Placement2D  inst:IfcDirection_2050 .

inst:IfcGridAxis_149  rdf:type  ifc:IfcGridAxis .

inst:IfcLabel_641  rdf:type  ifc:IfcLabel ;
        express:hasString  "3" .

inst:IfcGridAxis_149  ifc:axisTag_IfcGridAxis  inst:IfcLabel_641 ;
        ifc:axisCurve_IfcGridAxis  inst:IfcPolyline_145 ;
        ifc:sameSense_IfcGridAxis  inst:IfcBoolean_622 .

inst:IfcRectangleProfileDef_2197
        rdf:type                       ifc:IfcRectangleProfileDef ;
        ifc:profileType_IfcProfileDef  ifc:AREA ;
        ifc:profileName_IfcProfileDef  inst:IfcLabel_538 ;
        ifc:position_IfcParameterizedProfileDef  inst:IfcAxis2Placement2D_2194 ;
        ifc:xDim_IfcRectangleProfileDef  inst:IfcPositiveLengthMeasure_539 ;
        ifc:yDim_IfcRectangleProfileDef  inst:IfcPositiveLengthMeasure_539 .

inst:IfcAxis2Placement2D_1176
        rdf:type                   ifc:IfcAxis2Placement2D ;
        ifc:location_IfcPlacement  inst:IfcCartesianPoint_1046 ;
        ifc:refDirection_IfcAxis2Placement2D  inst:IfcDirection_2050 .

inst:IfcExtrudedAreaSolid_2201
        rdf:type                        ifc:IfcExtrudedAreaSolid ;
        ifc:sweptArea_IfcSweptAreaSolid  inst:IfcRectangleProfileDef_2197 ;
        ifc:position_IfcSweptAreaSolid  inst:IfcAxis2Placement3D_2062 ;
        ifc:extrudedDirection_IfcExtrudedAreaSolid  inst:IfcDirection_36 ;
        ifc:depth_IfcExtrudedAreaSolid  inst:IfcPositiveLengthMeasure_540 .

inst:IfcCartesianPoint_154
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_642
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_154
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_642 .

inst:IfcLengthMeasure_List_643
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_642
        list:hasContents  inst:IfcReal_529 ;
        list:hasNext      inst:IfcLengthMeasure_List_643 .

inst:IfcLengthMeasure_644
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-8000."^^xsd:double .

inst:IfcLengthMeasure_List_643
        list:hasContents  inst:IfcLengthMeasure_644 .

inst:IfcRectangleProfileDef_1179
        rdf:type                       ifc:IfcRectangleProfileDef ;
        ifc:profileType_IfcProfileDef  ifc:AREA ;
        ifc:profileName_IfcProfileDef  inst:IfcLabel_538 ;
        ifc:position_IfcParameterizedProfileDef  inst:IfcAxis2Placement2D_1176 ;
        ifc:xDim_IfcRectangleProfileDef  inst:IfcPositiveLengthMeasure_552 ;
        ifc:yDim_IfcRectangleProfileDef  inst:IfcPositiveLengthMeasure_552 .

inst:IfcShapeRepresentation_2204
        rdf:type                     ifc:IfcShapeRepresentation ;
        ifc:contextOfItems_IfcRepresentation  inst:IfcGeometricRepresentationSubContext_53 ;
        ifc:representationIdentifier_IfcRepresentation  inst:IfcLabel_544 ;
        ifc:representationType_IfcRepresentation  inst:IfcLabel_545 ;
        ifc:items_IfcRepresentation  inst:IfcExtrudedAreaSolid_2201 .

inst:IfcCartesianPoint_158
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_645
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_158
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_645 .

inst:IfcLengthMeasure_List_646
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_645
        list:hasContents  inst:IfcLengthMeasure_612 ;
        list:hasNext      inst:IfcLengthMeasure_List_646 .

inst:IfcLengthMeasure_List_646
        list:hasContents  inst:IfcLengthMeasure_644 .

inst:IfcExtrudedAreaSolid_1183
        rdf:type                        ifc:IfcExtrudedAreaSolid ;
        ifc:sweptArea_IfcSweptAreaSolid  inst:IfcRectangleProfileDef_1179 ;
        ifc:position_IfcSweptAreaSolid  inst:IfcAxis2Placement3D_1054 ;
        ifc:extrudedDirection_IfcExtrudedAreaSolid  inst:IfcDirection_36 ;
        ifc:depth_IfcExtrudedAreaSolid  inst:IfcLengthMeasure_558 .

inst:IfcPolyline_162  rdf:type  ifc:IfcPolyline .

inst:IfcCartesianPoint_List_647
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyline_162  ifc:points_IfcPolyline  inst:IfcCartesianPoint_List_647 .

inst:IfcCartesianPoint_List_648
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_647
        list:hasContents  inst:IfcCartesianPoint_154 ;
        list:hasNext      inst:IfcCartesianPoint_List_648 .

inst:IfcCartesianPoint_List_648
        list:hasContents  inst:IfcCartesianPoint_158 .

inst:IfcShapeRepresentation_1186
        rdf:type                     ifc:IfcShapeRepresentation ;
        ifc:contextOfItems_IfcRepresentation  inst:IfcGeometricRepresentationSubContext_53 ;
        ifc:representationIdentifier_IfcRepresentation  inst:IfcLabel_544 ;
        ifc:representationType_IfcRepresentation  inst:IfcLabel_545 ;
        ifc:items_IfcRepresentation  inst:IfcExtrudedAreaSolid_1183 .

inst:IfcRepresentation_List_649
        rdf:type  ifc:IfcRepresentation_List .

inst:IfcProductDefinitionShape_2210
        ifc:representations_IfcProductRepresentation  inst:IfcRepresentation_List_649 .

inst:IfcRepresentation_List_649
        list:hasContents  inst:IfcShapeRepresentation_2204 .

inst:IfcGridAxis_166  rdf:type  ifc:IfcGridAxis .

inst:IfcLabel_650  rdf:type  ifc:IfcLabel ;
        express:hasString  "2" .

inst:IfcGridAxis_166  ifc:axisTag_IfcGridAxis  inst:IfcLabel_650 ;
        ifc:axisCurve_IfcGridAxis  inst:IfcPolyline_162 ;
        ifc:sameSense_IfcGridAxis  inst:IfcBoolean_622 .

inst:IfcCartesianPoint_2214
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_651
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_2214
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_651 .

inst:IfcLengthMeasure_List_652
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_653
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_651
        list:hasContents  inst:IfcLengthMeasure_636 ;
        list:hasNext      inst:IfcLengthMeasure_List_652 .

inst:IfcLengthMeasure_List_652
        list:hasContents  inst:IfcLengthMeasure_557 ;
        list:hasNext      inst:IfcLengthMeasure_List_653 .

inst:IfcLengthMeasure_List_653
        list:hasContents  inst:IfcLengthMeasure_558 .

inst:IfcRepresentation_List_654
        rdf:type  ifc:IfcRepresentation_List .

inst:IfcProductDefinitionShape_1192
        ifc:representations_IfcProductRepresentation  inst:IfcRepresentation_List_654 .

inst:IfcRepresentation_List_654
        list:hasContents  inst:IfcShapeRepresentation_1186 .

inst:IfcAxis2Placement3D_2218
        rdf:type                      ifc:IfcAxis2Placement3D ;
        ifc:location_IfcPlacement     inst:IfcCartesianPoint_2214 ;
        ifc:axis_IfcAxis2Placement3D  inst:IfcDirection_36 ;
        ifc:refDirection_IfcAxis2Placement3D  inst:IfcDirection_28 .

inst:IfcCartesianPoint_171
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_655
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_171
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_655 .

inst:IfcLengthMeasure_List_656
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_655
        list:hasContents  inst:IfcReal_529 ;
        list:hasNext      inst:IfcLengthMeasure_List_656 .

inst:IfcLengthMeasure_657
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-12000."^^xsd:double .

inst:IfcLengthMeasure_List_656
        list:hasContents  inst:IfcLengthMeasure_657 .

inst:IfcVirtualGridIntersection_1196
        rdf:type  ifc:IfcVirtualGridIntersection .

inst:IfcGridAxis_List_658
        rdf:type  ifc:IfcGridAxis_List .

inst:IfcVirtualGridIntersection_1196
        ifc:intersectingAxes_IfcVirtualGridIntersection  inst:IfcGridAxis_List_658 .

inst:IfcGridAxis_List_659
        rdf:type  ifc:IfcGridAxis_List .

inst:IfcGridAxis_List_658
        list:hasContents  inst:IfcGridAxis_234 ;
        list:hasNext      inst:IfcGridAxis_List_659 .

inst:IfcGridAxis_List_659
        list:hasContents  inst:IfcGridAxis_166 .

inst:IfcLengthMeasure_List_660
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcVirtualGridIntersection_1196
        ifc:offsetDistances_IfcVirtualGridIntersection  inst:IfcLengthMeasure_List_660 .

inst:IfcLengthMeasure_List_661
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_662
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_660
        list:hasContents  inst:IfcReal_529 ;
        list:hasNext      inst:IfcLengthMeasure_List_661 .

inst:IfcLengthMeasure_List_661
        list:hasContents  inst:IfcReal_529 ;
        list:hasNext      inst:IfcLengthMeasure_List_662 .

inst:IfcLengthMeasure_List_662
        list:hasContents  inst:IfcReal_529 .

inst:IfcLocalPlacement_2221
        ifc:placementRelTo_IfcLocalPlacement  inst:IfcLocalPlacement_90 ;
        ifc:relativePlacement_IfcLocalPlacement  inst:IfcAxis2Placement3D_2218 .

inst:IfcCartesianPoint_175
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_663
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_175
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_663 .

inst:IfcLengthMeasure_List_664
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_663
        list:hasContents  inst:IfcLengthMeasure_612 ;
        list:hasNext      inst:IfcLengthMeasure_List_664 .

inst:IfcLengthMeasure_List_664
        list:hasContents  inst:IfcLengthMeasure_657 .

inst:IfcGridPlacement_1199
        ifc:placementLocation_IfcGridPlacement  inst:IfcVirtualGridIntersection_1196 .

inst:IfcRelAssociatesMaterial_2224
        rdf:type  ifc:IfcRelAssociatesMaterial .

inst:IfcGloballyUniqueId_665
        rdf:type           ifc:IfcGloballyUniqueId ;
        express:hasString  "2gdBfewsLA5Q6MxOyuBaJS" .

inst:IfcRelAssociatesMaterial_2224
        ifc:globalId_IfcRoot      inst:IfcGloballyUniqueId_665 ;
        ifc:ownerHistory_IfcRoot  inst:IfcOwnerHistory_13 ;
        ifc:relatedObjects_IfcRelAssociates  inst:IfcBeam_2167 ;
        ifc:relatingMaterial_IfcRelAssociatesMaterial  inst:IfcMaterial_354 .

inst:IfcRelAssociatesMaterial_1202
        rdf:type  ifc:IfcRelAssociatesMaterial .

inst:IfcGloballyUniqueId_666
        rdf:type           ifc:IfcGloballyUniqueId ;
        express:hasString  "2pkwqk6HjC78QH3P0jN49W" .

inst:IfcRelAssociatesMaterial_1202
        ifc:globalId_IfcRoot      inst:IfcGloballyUniqueId_666 ;
        ifc:ownerHistory_IfcRoot  inst:IfcOwnerHistory_13 ;
        ifc:relatedObjects_IfcRelAssociates  inst:IfcColumn_1149 ;
        ifc:relatingMaterial_IfcRelAssociatesMaterial  inst:IfcMaterial_354 .

inst:IfcPolyline_179  rdf:type  ifc:IfcPolyline .

inst:IfcCartesianPoint_List_667
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyline_179  ifc:points_IfcPolyline  inst:IfcCartesianPoint_List_667 .

inst:IfcCartesianPoint_List_668
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_667
        list:hasContents  inst:IfcCartesianPoint_171 ;
        list:hasNext      inst:IfcCartesianPoint_List_668 .

inst:IfcCartesianPoint_List_668
        list:hasContents  inst:IfcCartesianPoint_175 .

inst:IfcLabel_669  rdf:type  ifc:IfcLabel ;
        express:hasString  "1" .

inst:IfcGridAxis_183  ifc:axisTag_IfcGridAxis  inst:IfcLabel_669 ;
        ifc:axisCurve_IfcGridAxis  inst:IfcPolyline_179 ;
        ifc:sameSense_IfcGridAxis  inst:IfcBoolean_622 .

inst:IfcBeam_2234  rdf:type  ifc:IfcBeam .

inst:IfcGloballyUniqueId_670
        rdf:type           ifc:IfcGloballyUniqueId ;
        express:hasString  "2Ib4Kw7NbENgEHiTJh_Leu" .

inst:IfcBeam_2234  ifc:globalId_IfcRoot  inst:IfcGloballyUniqueId_670 ;
        ifc:ownerHistory_IfcRoot  inst:IfcOwnerHistory_13 ;
        ifc:name_IfcRoot          inst:IfcLabel_583 .

inst:IfcLocalPlacement_2288
        rdf:type  ifc:IfcLocalPlacement .

inst:IfcBeam_2234  ifc:objectPlacement_IfcProduct  inst:IfcLocalPlacement_2288 .

inst:IfcProductDefinitionShape_2277
        rdf:type  ifc:IfcProductDefinitionShape .

inst:IfcBeam_2234  ifc:representation_IfcProduct  inst:IfcProductDefinitionShape_2277 .

inst:IfcCartesianPoint_188
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_671
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_188
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_671 .

inst:IfcLengthMeasure_List_672
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_673
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "19000."^^xsd:double .

inst:IfcLengthMeasure_List_671
        list:hasContents  inst:IfcLengthMeasure_673 ;
        list:hasNext      inst:IfcLengthMeasure_List_672 .

inst:IfcLengthMeasure_674
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-16000."^^xsd:double .

inst:IfcLengthMeasure_List_672
        list:hasContents  inst:IfcLengthMeasure_674 .

inst:IfcColumn_1212  rdf:type  ifc:IfcColumn .

inst:IfcGloballyUniqueId_675
        rdf:type           ifc:IfcGloballyUniqueId ;
        express:hasString  "3kEtX5Q8T598aWzoK0Y3VK" .

inst:IfcColumn_1212  ifc:globalId_IfcRoot  inst:IfcGloballyUniqueId_675 ;
        ifc:ownerHistory_IfcRoot  inst:IfcOwnerHistory_13 ;
        ifc:name_IfcRoot          inst:IfcLabel_587 .

inst:IfcGridPlacement_1262
        rdf:type  ifc:IfcGridPlacement .

inst:IfcColumn_1212  ifc:objectPlacement_IfcProduct  inst:IfcGridPlacement_1262 .

inst:IfcProductDefinitionShape_1255
        rdf:type  ifc:IfcProductDefinitionShape .

inst:IfcColumn_1212  ifc:representation_IfcProduct  inst:IfcProductDefinitionShape_1255 .

inst:IfcCartesianPoint_192
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_676
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_192
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_676 .

inst:IfcLengthMeasure_List_677
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_676
        list:hasContents  inst:IfcLengthMeasure_673 ;
        list:hasNext      inst:IfcLengthMeasure_List_677 .

inst:IfcLengthMeasure_678
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "3000."^^xsd:double .

inst:IfcLengthMeasure_List_677
        list:hasContents  inst:IfcLengthMeasure_678 .

inst:IfcPolyline_196  rdf:type  ifc:IfcPolyline .

inst:IfcCartesianPoint_List_679
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyline_196  ifc:points_IfcPolyline  inst:IfcCartesianPoint_List_679 .

inst:IfcCartesianPoint_List_680
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_679
        list:hasContents  inst:IfcCartesianPoint_188 ;
        list:hasNext      inst:IfcCartesianPoint_List_680 .

inst:IfcCartesianPoint_List_680
        list:hasContents  inst:IfcCartesianPoint_192 .

inst:IfcGridAxis_200  rdf:type  ifc:IfcGridAxis .

inst:IfcLabel_681  rdf:type  ifc:IfcLabel ;
        express:hasString  "A" .

inst:IfcGridAxis_200  ifc:axisTag_IfcGridAxis  inst:IfcLabel_681 ;
        ifc:axisCurve_IfcGridAxis  inst:IfcPolyline_196 ;
        ifc:sameSense_IfcGridAxis  inst:IfcBoolean_622 .

inst:IfcCartesianPoint_205
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_682
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_205
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_682 .

inst:IfcLengthMeasure_List_683
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_684
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "15000."^^xsd:double .

inst:IfcLengthMeasure_List_682
        list:hasContents  inst:IfcLengthMeasure_684 ;
        list:hasNext      inst:IfcLengthMeasure_List_683 .

inst:IfcLengthMeasure_List_683
        list:hasContents  inst:IfcLengthMeasure_674 .

inst:IfcCartesianPoint_209
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_685
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_209
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_685 .

inst:IfcLengthMeasure_List_686
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_685
        list:hasContents  inst:IfcLengthMeasure_684 ;
        list:hasNext      inst:IfcLengthMeasure_List_686 .

inst:IfcLengthMeasure_List_686
        list:hasContents  inst:IfcLengthMeasure_678 .

inst:IfcPolyline_213  rdf:type  ifc:IfcPolyline .

inst:IfcCartesianPoint_List_687
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyline_213  ifc:points_IfcPolyline  inst:IfcCartesianPoint_List_687 .

inst:IfcCartesianPoint_List_688
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_687
        list:hasContents  inst:IfcCartesianPoint_205 ;
        list:hasNext      inst:IfcCartesianPoint_List_688 .

inst:IfcCartesianPoint_List_688
        list:hasContents  inst:IfcCartesianPoint_209 .

inst:IfcAxis2Placement2D_2261
        rdf:type                   ifc:IfcAxis2Placement2D ;
        ifc:location_IfcPlacement  inst:IfcCartesianPoint_2054 ;
        ifc:refDirection_IfcAxis2Placement2D  inst:IfcDirection_2050 .

inst:IfcAxis2Placement2D_1239
        rdf:type                   ifc:IfcAxis2Placement2D ;
        ifc:location_IfcPlacement  inst:IfcCartesianPoint_1046 ;
        ifc:refDirection_IfcAxis2Placement2D  inst:IfcDirection_2050 .

inst:IfcRectangleProfileDef_2264
        rdf:type                       ifc:IfcRectangleProfileDef ;
        ifc:profileType_IfcProfileDef  ifc:AREA ;
        ifc:profileName_IfcProfileDef  inst:IfcLabel_538 ;
        ifc:position_IfcParameterizedProfileDef  inst:IfcAxis2Placement2D_2261 ;
        ifc:xDim_IfcRectangleProfileDef  inst:IfcPositiveLengthMeasure_539 ;
        ifc:yDim_IfcRectangleProfileDef  inst:IfcPositiveLengthMeasure_539 .

inst:IfcGridAxis_217  rdf:type  ifc:IfcGridAxis .

inst:IfcLabel_689  rdf:type  ifc:IfcLabel ;
        express:hasString  "B" .

inst:IfcGridAxis_217  ifc:axisTag_IfcGridAxis  inst:IfcLabel_689 ;
        ifc:axisCurve_IfcGridAxis  inst:IfcPolyline_213 ;
        ifc:sameSense_IfcGridAxis  inst:IfcBoolean_622 .

inst:IfcRectangleProfileDef_1242
        rdf:type                       ifc:IfcRectangleProfileDef ;
        ifc:profileType_IfcProfileDef  ifc:AREA ;
        ifc:profileName_IfcProfileDef  inst:IfcLabel_538 ;
        ifc:position_IfcParameterizedProfileDef  inst:IfcAxis2Placement2D_1239 ;
        ifc:xDim_IfcRectangleProfileDef  inst:IfcPositiveLengthMeasure_552 ;
        ifc:yDim_IfcRectangleProfileDef  inst:IfcPositiveLengthMeasure_552 .

inst:IfcExtrudedAreaSolid_2268
        rdf:type                        ifc:IfcExtrudedAreaSolid ;
        ifc:sweptArea_IfcSweptAreaSolid  inst:IfcRectangleProfileDef_2264 ;
        ifc:position_IfcSweptAreaSolid  inst:IfcAxis2Placement3D_2062 ;
        ifc:extrudedDirection_IfcExtrudedAreaSolid  inst:IfcDirection_36 ;
        ifc:depth_IfcExtrudedAreaSolid  inst:IfcPositiveLengthMeasure_540 .

inst:IfcCartesianPoint_222
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_690
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_222
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_690 .

inst:IfcLengthMeasure_List_691
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_692
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "11000."^^xsd:double .

inst:IfcLengthMeasure_List_690
        list:hasContents  inst:IfcLengthMeasure_692 ;
        list:hasNext      inst:IfcLengthMeasure_List_691 .

inst:IfcLengthMeasure_List_691
        list:hasContents  inst:IfcLengthMeasure_674 .

inst:IfcExtrudedAreaSolid_1246
        rdf:type                        ifc:IfcExtrudedAreaSolid ;
        ifc:sweptArea_IfcSweptAreaSolid  inst:IfcRectangleProfileDef_1242 ;
        ifc:position_IfcSweptAreaSolid  inst:IfcAxis2Placement3D_1054 ;
        ifc:extrudedDirection_IfcExtrudedAreaSolid  inst:IfcDirection_36 ;
        ifc:depth_IfcExtrudedAreaSolid  inst:IfcLengthMeasure_558 .

inst:IfcShapeRepresentation_2271
        rdf:type                     ifc:IfcShapeRepresentation ;
        ifc:contextOfItems_IfcRepresentation  inst:IfcGeometricRepresentationSubContext_53 ;
        ifc:representationIdentifier_IfcRepresentation  inst:IfcLabel_544 ;
        ifc:representationType_IfcRepresentation  inst:IfcLabel_545 ;
        ifc:items_IfcRepresentation  inst:IfcExtrudedAreaSolid_2268 .

inst:IfcShapeRepresentation_1249
        rdf:type                     ifc:IfcShapeRepresentation ;
        ifc:contextOfItems_IfcRepresentation  inst:IfcGeometricRepresentationSubContext_53 ;
        ifc:representationIdentifier_IfcRepresentation  inst:IfcLabel_544 ;
        ifc:representationType_IfcRepresentation  inst:IfcLabel_545 ;
        ifc:items_IfcRepresentation  inst:IfcExtrudedAreaSolid_1246 .

inst:IfcCartesianPoint_226
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_693
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_226
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_693 .

inst:IfcLengthMeasure_List_694
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_693
        list:hasContents  inst:IfcLengthMeasure_692 ;
        list:hasNext      inst:IfcLengthMeasure_List_694 .

inst:IfcLengthMeasure_List_694
        list:hasContents  inst:IfcLengthMeasure_678 .

inst:IfcRepresentation_List_695
        rdf:type  ifc:IfcRepresentation_List .

inst:IfcProductDefinitionShape_2277
        ifc:representations_IfcProductRepresentation  inst:IfcRepresentation_List_695 .

inst:IfcRepresentation_List_695
        list:hasContents  inst:IfcShapeRepresentation_2271 .

inst:IfcPolyline_230  rdf:type  ifc:IfcPolyline .

inst:IfcCartesianPoint_List_696
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyline_230  ifc:points_IfcPolyline  inst:IfcCartesianPoint_List_696 .

inst:IfcCartesianPoint_List_697
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_696
        list:hasContents  inst:IfcCartesianPoint_222 ;
        list:hasNext      inst:IfcCartesianPoint_List_697 .

inst:IfcCartesianPoint_List_697
        list:hasContents  inst:IfcCartesianPoint_226 .

inst:IfcRepresentation_List_698
        rdf:type  ifc:IfcRepresentation_List .

inst:IfcProductDefinitionShape_1255
        ifc:representations_IfcProductRepresentation  inst:IfcRepresentation_List_698 .

inst:IfcRepresentation_List_698
        list:hasContents  inst:IfcShapeRepresentation_1249 .

inst:IfcCartesianPoint_2281
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_699
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_2281
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_699 .

inst:IfcLengthMeasure_List_700
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_701
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_699
        list:hasContents  inst:IfcLengthMeasure_627 ;
        list:hasNext      inst:IfcLengthMeasure_List_700 .

inst:IfcLengthMeasure_List_700
        list:hasContents  inst:IfcLengthMeasure_557 ;
        list:hasNext      inst:IfcLengthMeasure_List_701 .

inst:IfcLengthMeasure_List_701
        list:hasContents  inst:IfcLengthMeasure_558 .

inst:IfcLabel_702  rdf:type  ifc:IfcLabel ;
        express:hasString  "C" .

inst:IfcGridAxis_234  ifc:axisTag_IfcGridAxis  inst:IfcLabel_702 ;
        ifc:axisCurve_IfcGridAxis  inst:IfcPolyline_230 ;
        ifc:sameSense_IfcGridAxis  inst:IfcBoolean_622 .

inst:IfcVirtualGridIntersection_1259
        rdf:type  ifc:IfcVirtualGridIntersection .

inst:IfcGridAxis_List_703
        rdf:type  ifc:IfcGridAxis_List .

inst:IfcVirtualGridIntersection_1259
        ifc:intersectingAxes_IfcVirtualGridIntersection  inst:IfcGridAxis_List_703 .

inst:IfcGridAxis_List_704
        rdf:type  ifc:IfcGridAxis_List .

inst:IfcGridAxis_List_703
        list:hasContents  inst:IfcGridAxis_234 ;
        list:hasNext      inst:IfcGridAxis_List_704 .

inst:IfcGridAxis_List_704
        list:hasContents  inst:IfcGridAxis_149 .

inst:IfcLengthMeasure_List_705
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcVirtualGridIntersection_1259
        ifc:offsetDistances_IfcVirtualGridIntersection  inst:IfcLengthMeasure_List_705 .

inst:IfcLengthMeasure_List_706
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_707
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_705
        list:hasContents  inst:IfcReal_529 ;
        list:hasNext      inst:IfcLengthMeasure_List_706 .

inst:IfcLengthMeasure_List_706
        list:hasContents  inst:IfcReal_529 ;
        list:hasNext      inst:IfcLengthMeasure_List_707 .

inst:IfcLengthMeasure_List_707
        list:hasContents  inst:IfcReal_529 .

inst:IfcAxis2Placement3D_2285
        rdf:type                      ifc:IfcAxis2Placement3D ;
        ifc:location_IfcPlacement     inst:IfcCartesianPoint_2281 ;
        ifc:axis_IfcAxis2Placement3D  inst:IfcDirection_36 ;
        ifc:refDirection_IfcAxis2Placement3D  inst:IfcDirection_28 .

inst:IfcGridPlacement_1262
        ifc:placementLocation_IfcGridPlacement  inst:IfcVirtualGridIntersection_1259 .

inst:IfcCartesianPoint_239
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_708
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_239
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_708 .

inst:IfcLengthMeasure_List_709
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_710
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "7000."^^xsd:double .

inst:IfcLengthMeasure_List_708
        list:hasContents  inst:IfcLengthMeasure_710 ;
        list:hasNext      inst:IfcLengthMeasure_List_709 .

inst:IfcLengthMeasure_List_709
        list:hasContents  inst:IfcLengthMeasure_674 .

inst:IfcLocalPlacement_2288
        ifc:placementRelTo_IfcLocalPlacement  inst:IfcLocalPlacement_90 ;
        ifc:relativePlacement_IfcLocalPlacement  inst:IfcAxis2Placement3D_2285 .

inst:IfcRelAssociatesMaterial_1265
        rdf:type  ifc:IfcRelAssociatesMaterial .

inst:IfcGloballyUniqueId_711
        rdf:type           ifc:IfcGloballyUniqueId ;
        express:hasString  "0jQTVYfXPFTe1hDM_MkTM2" .

inst:IfcRelAssociatesMaterial_1265
        ifc:globalId_IfcRoot      inst:IfcGloballyUniqueId_711 ;
        ifc:ownerHistory_IfcRoot  inst:IfcOwnerHistory_13 ;
        ifc:relatedObjects_IfcRelAssociates  inst:IfcColumn_1212 ;
        ifc:relatingMaterial_IfcRelAssociatesMaterial  inst:IfcMaterial_354 .

inst:IfcCartesianPoint_243
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_712
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_243
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_712 .

inst:IfcLengthMeasure_List_713
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_712
        list:hasContents  inst:IfcLengthMeasure_710 ;
        list:hasNext      inst:IfcLengthMeasure_List_713 .

inst:IfcLengthMeasure_List_713
        list:hasContents  inst:IfcLengthMeasure_678 .

inst:IfcRelAssociatesMaterial_2291
        rdf:type  ifc:IfcRelAssociatesMaterial .

inst:IfcGloballyUniqueId_714
        rdf:type           ifc:IfcGloballyUniqueId ;
        express:hasString  "1BKlPhJQj4buntvqU28dfk" .

inst:IfcRelAssociatesMaterial_2291
        ifc:globalId_IfcRoot      inst:IfcGloballyUniqueId_714 ;
        ifc:ownerHistory_IfcRoot  inst:IfcOwnerHistory_13 ;
        ifc:relatedObjects_IfcRelAssociates  inst:IfcBeam_2234 ;
        ifc:relatingMaterial_IfcRelAssociatesMaterial  inst:IfcMaterial_354 .

inst:IfcPolyline_247  rdf:type  ifc:IfcPolyline .

inst:IfcCartesianPoint_List_715
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyline_247  ifc:points_IfcPolyline  inst:IfcCartesianPoint_List_715 .

inst:IfcCartesianPoint_List_716
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_715
        list:hasContents  inst:IfcCartesianPoint_239 ;
        list:hasNext      inst:IfcCartesianPoint_List_716 .

inst:IfcCartesianPoint_List_716
        list:hasContents  inst:IfcCartesianPoint_243 .

inst:IfcLabel_717  rdf:type  ifc:IfcLabel ;
        express:hasString  "D" .

inst:IfcGridAxis_251  ifc:axisTag_IfcGridAxis  inst:IfcLabel_717 ;
        ifc:axisCurve_IfcGridAxis  inst:IfcPolyline_247 ;
        ifc:sameSense_IfcGridAxis  inst:IfcBoolean_622 .

inst:IfcColumn_1275  rdf:type  ifc:IfcColumn .

inst:IfcGloballyUniqueId_718
        rdf:type           ifc:IfcGloballyUniqueId ;
        express:hasString  "23XrrMv196EgYSv3SrHeh9" .

inst:IfcColumn_1275  ifc:globalId_IfcRoot  inst:IfcGloballyUniqueId_718 ;
        ifc:ownerHistory_IfcRoot  inst:IfcOwnerHistory_13 ;
        ifc:name_IfcRoot          inst:IfcLabel_587 .

inst:IfcGridPlacement_1325
        rdf:type  ifc:IfcGridPlacement .

inst:IfcColumn_1275  ifc:objectPlacement_IfcProduct  inst:IfcGridPlacement_1325 .

inst:IfcProductDefinitionShape_1318
        rdf:type  ifc:IfcProductDefinitionShape .

inst:IfcColumn_1275  ifc:representation_IfcProduct  inst:IfcProductDefinitionShape_1318 .

inst:IfcBeam_2301  rdf:type  ifc:IfcBeam .

inst:IfcGloballyUniqueId_719
        rdf:type           ifc:IfcGloballyUniqueId ;
        express:hasString  "3Hx5aT6pD1iPGYA3CCHPRS" .

inst:IfcBeam_2301  ifc:globalId_IfcRoot  inst:IfcGloballyUniqueId_719 ;
        ifc:ownerHistory_IfcRoot  inst:IfcOwnerHistory_13 ;
        ifc:name_IfcRoot          inst:IfcLabel_583 .

inst:IfcLocalPlacement_2359
        rdf:type  ifc:IfcLocalPlacement .

inst:IfcBeam_2301  ifc:objectPlacement_IfcProduct  inst:IfcLocalPlacement_2359 .

inst:IfcProductDefinitionShape_2344
        rdf:type  ifc:IfcProductDefinitionShape .

inst:IfcBeam_2301  ifc:representation_IfcProduct  inst:IfcProductDefinitionShape_2344 .

inst:IfcCartesianPoint_256
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_720
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_256
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_720 .

inst:IfcLengthMeasure_List_721
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_720
        list:hasContents  inst:IfcLengthMeasure_678 ;
        list:hasNext      inst:IfcLengthMeasure_List_721 .

inst:IfcLengthMeasure_List_721
        list:hasContents  inst:IfcLengthMeasure_674 .

inst:IfcCartesianPoint_260
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_722
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_260
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_722 .

inst:IfcLengthMeasure_List_723
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_722
        list:hasContents  inst:IfcLengthMeasure_678 ;
        list:hasNext      inst:IfcLengthMeasure_List_723 .

inst:IfcLengthMeasure_List_723
        list:hasContents  inst:IfcLengthMeasure_678 .

inst:IfcPolyline_264  rdf:type  ifc:IfcPolyline .

inst:IfcCartesianPoint_List_724
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyline_264  ifc:points_IfcPolyline  inst:IfcCartesianPoint_List_724 .

inst:IfcCartesianPoint_List_725
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_724
        list:hasContents  inst:IfcCartesianPoint_256 ;
        list:hasNext      inst:IfcCartesianPoint_List_725 .

inst:IfcCartesianPoint_List_725
        list:hasContents  inst:IfcCartesianPoint_260 .

inst:IfcGridAxis_268  rdf:type  ifc:IfcGridAxis .

inst:IfcLabel_726  rdf:type  ifc:IfcLabel ;
        express:hasString  "E" .

inst:IfcGridAxis_268  ifc:axisTag_IfcGridAxis  inst:IfcLabel_726 ;
        ifc:axisCurve_IfcGridAxis  inst:IfcPolyline_264 ;
        ifc:sameSense_IfcGridAxis  inst:IfcBoolean_622 .

inst:IfcCartesianPoint_273
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_727
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_273
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_727 .

inst:IfcLengthMeasure_List_728
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_729
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_730
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-17000."^^xsd:double .

inst:IfcLengthMeasure_List_727
        list:hasContents  inst:IfcLengthMeasure_730 ;
        list:hasNext      inst:IfcLengthMeasure_List_728 .

inst:IfcLengthMeasure_731
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "16000."^^xsd:double .

inst:IfcLengthMeasure_List_728
        list:hasContents  inst:IfcLengthMeasure_731 ;
        list:hasNext      inst:IfcLengthMeasure_List_729 .

inst:IfcLengthMeasure_List_729
        list:hasContents  inst:IfcReal_529 .

inst:IfcAxis2Placement3D_277
        rdf:type                      ifc:IfcAxis2Placement3D ;
        ifc:location_IfcPlacement     inst:IfcCartesianPoint_273 ;
        ifc:axis_IfcAxis2Placement3D  inst:IfcDirection_36 ;
        ifc:refDirection_IfcAxis2Placement3D  inst:IfcDirection_28 .

inst:IfcAxis2Placement2D_1302
        rdf:type                   ifc:IfcAxis2Placement2D ;
        ifc:location_IfcPlacement  inst:IfcCartesianPoint_1046 ;
        ifc:refDirection_IfcAxis2Placement2D  inst:IfcDirection_2050 .

inst:IfcLocalPlacement_280
        rdf:type  ifc:IfcLocalPlacement ;
        ifc:placementRelTo_IfcLocalPlacement  inst:IfcLocalPlacement_90 ;
        ifc:relativePlacement_IfcLocalPlacement  inst:IfcAxis2Placement3D_277 .

inst:IfcAxis2Placement2D_2328
        rdf:type                   ifc:IfcAxis2Placement2D ;
        ifc:location_IfcPlacement  inst:IfcCartesianPoint_2054 ;
        ifc:refDirection_IfcAxis2Placement2D  inst:IfcDirection_2050 .

inst:IfcRectangleProfileDef_1305
        rdf:type                       ifc:IfcRectangleProfileDef ;
        ifc:profileType_IfcProfileDef  ifc:AREA ;
        ifc:profileName_IfcProfileDef  inst:IfcLabel_538 ;
        ifc:position_IfcParameterizedProfileDef  inst:IfcAxis2Placement2D_1302 ;
        ifc:xDim_IfcRectangleProfileDef  inst:IfcPositiveLengthMeasure_552 ;
        ifc:yDim_IfcRectangleProfileDef  inst:IfcPositiveLengthMeasure_552 .

inst:IfcGrid_283  rdf:type  ifc:IfcGrid .

inst:IfcGloballyUniqueId_732
        rdf:type           ifc:IfcGloballyUniqueId ;
        express:hasString  "0fuUMCx0jFggWzjspLeC2b" .

inst:IfcGrid_283  ifc:globalId_IfcRoot  inst:IfcGloballyUniqueId_732 ;
        ifc:ownerHistory_IfcRoot        inst:IfcOwnerHistory_13 ;
        ifc:objectPlacement_IfcProduct  inst:IfcLocalPlacement_280 .

inst:IfcProductDefinitionShape_286
        rdf:type  ifc:IfcProductDefinitionShape .

inst:IfcGrid_283  ifc:representation_IfcProduct  inst:IfcProductDefinitionShape_286 .

inst:IfcGridAxis_List_733
        rdf:type  ifc:IfcGridAxis_List .

inst:IfcGrid_283  ifc:uAxes_IfcGrid  inst:IfcGridAxis_List_733 .

inst:IfcGridAxis_List_734
        rdf:type  ifc:IfcGridAxis_List .

inst:IfcGridAxis_List_735
        rdf:type  ifc:IfcGridAxis_List .

inst:IfcGridAxis_List_736
        rdf:type  ifc:IfcGridAxis_List .

inst:IfcGridAxis_List_737
        rdf:type  ifc:IfcGridAxis_List .

inst:IfcGridAxis_List_733
        list:hasContents  inst:IfcGridAxis_115 ;
        list:hasNext      inst:IfcGridAxis_List_734 .

inst:IfcGridAxis_List_734
        list:hasContents  inst:IfcGridAxis_132 ;
        list:hasNext      inst:IfcGridAxis_List_735 .

inst:IfcGridAxis_List_735
        list:hasContents  inst:IfcGridAxis_149 ;
        list:hasNext      inst:IfcGridAxis_List_736 .

inst:IfcGridAxis_List_736
        list:hasContents  inst:IfcGridAxis_166 ;
        list:hasNext      inst:IfcGridAxis_List_737 .

inst:IfcGridAxis_List_737
        list:hasContents  inst:IfcGridAxis_183 .

inst:IfcGridAxis_List_738
        rdf:type  ifc:IfcGridAxis_List .

inst:IfcGrid_283  ifc:vAxes_IfcGrid  inst:IfcGridAxis_List_738 .

inst:IfcGridAxis_List_739
        rdf:type  ifc:IfcGridAxis_List .

inst:IfcGridAxis_List_740
        rdf:type  ifc:IfcGridAxis_List .

inst:IfcGridAxis_List_741
        rdf:type  ifc:IfcGridAxis_List .

inst:IfcGridAxis_List_742
        rdf:type  ifc:IfcGridAxis_List .

inst:IfcGridAxis_List_738
        list:hasContents  inst:IfcGridAxis_200 ;
        list:hasNext      inst:IfcGridAxis_List_739 .

inst:IfcGridAxis_List_739
        list:hasContents  inst:IfcGridAxis_217 ;
        list:hasNext      inst:IfcGridAxis_List_740 .

inst:IfcGridAxis_List_740
        list:hasContents  inst:IfcGridAxis_234 ;
        list:hasNext      inst:IfcGridAxis_List_741 .

inst:IfcGridAxis_List_741
        list:hasContents  inst:IfcGridAxis_251 ;
        list:hasNext      inst:IfcGridAxis_List_742 .

inst:IfcGridAxis_List_742
        list:hasContents  inst:IfcGridAxis_268 .

inst:IfcRectangleProfileDef_2331
        rdf:type                       ifc:IfcRectangleProfileDef ;
        ifc:profileType_IfcProfileDef  ifc:AREA ;
        ifc:profileName_IfcProfileDef  inst:IfcLabel_538 ;
        ifc:position_IfcParameterizedProfileDef  inst:IfcAxis2Placement2D_2328 ;
        ifc:xDim_IfcRectangleProfileDef  inst:IfcPositiveLengthMeasure_539 ;
        ifc:yDim_IfcRectangleProfileDef  inst:IfcPositiveLengthMeasure_539 .

inst:IfcGeometricCurveSet_284
        rdf:type                      ifc:IfcGeometricCurveSet ;
        ifc:elements_IfcGeometricSet  inst:IfcPolyline_111 ;
        ifc:elements_IfcGeometricSet  inst:IfcPolyline_128 ;
        ifc:elements_IfcGeometricSet  inst:IfcPolyline_145 ;
        ifc:elements_IfcGeometricSet  inst:IfcPolyline_162 ;
        ifc:elements_IfcGeometricSet  inst:IfcPolyline_179 ;
        ifc:elements_IfcGeometricSet  inst:IfcPolyline_196 ;
        ifc:elements_IfcGeometricSet  inst:IfcPolyline_213 ;
        ifc:elements_IfcGeometricSet  inst:IfcPolyline_230 ;
        ifc:elements_IfcGeometricSet  inst:IfcPolyline_247 ;
        ifc:elements_IfcGeometricSet  inst:IfcPolyline_264 .

inst:IfcShapeRepresentation_285
        rdf:type  ifc:IfcShapeRepresentation ;
        ifc:contextOfItems_IfcRepresentation  inst:IfcGeometricRepresentationSubContext_52 ;
        ifc:representationIdentifier_IfcRepresentation  inst:IfcLabel_580 .

inst:IfcLabel_743  rdf:type  ifc:IfcLabel ;
        express:hasString  "GeometricCurveSet" .

inst:IfcShapeRepresentation_285
        ifc:representationType_IfcRepresentation  inst:IfcLabel_743 ;
        ifc:items_IfcRepresentation  inst:IfcGeometricCurveSet_284 .

inst:IfcExtrudedAreaSolid_1309
        rdf:type                        ifc:IfcExtrudedAreaSolid ;
        ifc:sweptArea_IfcSweptAreaSolid  inst:IfcRectangleProfileDef_1305 ;
        ifc:position_IfcSweptAreaSolid  inst:IfcAxis2Placement3D_1054 ;
        ifc:extrudedDirection_IfcExtrudedAreaSolid  inst:IfcDirection_36 ;
        ifc:depth_IfcExtrudedAreaSolid  inst:IfcLengthMeasure_558 .

inst:IfcRepresentation_List_744
        rdf:type  ifc:IfcRepresentation_List .

inst:IfcProductDefinitionShape_286
        ifc:representations_IfcProductRepresentation  inst:IfcRepresentation_List_744 .

inst:IfcRepresentation_List_744
        list:hasContents  inst:IfcShapeRepresentation_285 .

inst:IfcExtrudedAreaSolid_2335
        rdf:type                        ifc:IfcExtrudedAreaSolid ;
        ifc:sweptArea_IfcSweptAreaSolid  inst:IfcRectangleProfileDef_2331 ;
        ifc:position_IfcSweptAreaSolid  inst:IfcAxis2Placement3D_2062 ;
        ifc:extrudedDirection_IfcExtrudedAreaSolid  inst:IfcDirection_36 ;
        ifc:depth_IfcExtrudedAreaSolid  inst:IfcLengthMeasure_731 .

inst:IfcShapeRepresentation_1312
        rdf:type                     ifc:IfcShapeRepresentation ;
        ifc:contextOfItems_IfcRepresentation  inst:IfcGeometricRepresentationSubContext_53 ;
        ifc:representationIdentifier_IfcRepresentation  inst:IfcLabel_544 ;
        ifc:representationType_IfcRepresentation  inst:IfcLabel_545 ;
        ifc:items_IfcRepresentation  inst:IfcExtrudedAreaSolid_1309 .

inst:IfcShapeRepresentation_2338
        rdf:type                     ifc:IfcShapeRepresentation ;
        ifc:contextOfItems_IfcRepresentation  inst:IfcGeometricRepresentationSubContext_53 ;
        ifc:representationIdentifier_IfcRepresentation  inst:IfcLabel_544 ;
        ifc:representationType_IfcRepresentation  inst:IfcLabel_545 ;
        ifc:items_IfcRepresentation  inst:IfcExtrudedAreaSolid_2335 .

inst:IfcColumn_293  rdf:type  ifc:IfcColumn .

inst:IfcGloballyUniqueId_745
        rdf:type           ifc:IfcGloballyUniqueId ;
        express:hasString  "2E6Q5P3bD23h5JOtEANY6k" .

inst:IfcColumn_293  ifc:globalId_IfcRoot  inst:IfcGloballyUniqueId_745 ;
        ifc:ownerHistory_IfcRoot  inst:IfcOwnerHistory_13 ;
        ifc:name_IfcRoot          inst:IfcLabel_587 .

inst:IfcGridPlacement_351
        rdf:type  ifc:IfcGridPlacement .

inst:IfcColumn_293  ifc:objectPlacement_IfcProduct  inst:IfcGridPlacement_351 .

inst:IfcProductDefinitionShape_344
        rdf:type  ifc:IfcProductDefinitionShape .

inst:IfcColumn_293  ifc:representation_IfcProduct  inst:IfcProductDefinitionShape_344 .

inst:IfcRepresentation_List_746
        rdf:type  ifc:IfcRepresentation_List .

inst:IfcProductDefinitionShape_1318
        ifc:representations_IfcProductRepresentation  inst:IfcRepresentation_List_746 .

inst:IfcRepresentation_List_746
        list:hasContents  inst:IfcShapeRepresentation_1312 .

inst:IfcRepresentation_List_747
        rdf:type  ifc:IfcRepresentation_List .

inst:IfcProductDefinitionShape_2344
        ifc:representations_IfcProductRepresentation  inst:IfcRepresentation_List_747 .

inst:IfcRepresentation_List_747
        list:hasContents  inst:IfcShapeRepresentation_2338 .

inst:IfcVirtualGridIntersection_1322
        rdf:type  ifc:IfcVirtualGridIntersection .

inst:IfcGridAxis_List_748
        rdf:type  ifc:IfcGridAxis_List .

inst:IfcVirtualGridIntersection_1322
        ifc:intersectingAxes_IfcVirtualGridIntersection  inst:IfcGridAxis_List_748 .

inst:IfcGridAxis_List_749
        rdf:type  ifc:IfcGridAxis_List .

inst:IfcGridAxis_List_748
        list:hasContents  inst:IfcGridAxis_234 ;
        list:hasNext      inst:IfcGridAxis_List_749 .

inst:IfcGridAxis_List_749
        list:hasContents  inst:IfcGridAxis_132 .

inst:IfcLengthMeasure_List_750
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcVirtualGridIntersection_1322
        ifc:offsetDistances_IfcVirtualGridIntersection  inst:IfcLengthMeasure_List_750 .

inst:IfcLengthMeasure_List_751
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_752
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_750
        list:hasContents  inst:IfcReal_529 ;
        list:hasNext      inst:IfcLengthMeasure_List_751 .

inst:IfcLengthMeasure_List_751
        list:hasContents  inst:IfcReal_529 ;
        list:hasNext      inst:IfcLengthMeasure_List_752 .

inst:IfcLengthMeasure_List_752
        list:hasContents  inst:IfcReal_529 .

inst:IfcDirection_2348
        rdf:type  ifc:IfcDirection .

inst:IfcReal_List_753
        rdf:type  ifc:IfcReal_List .

inst:IfcDirection_2348
        ifc:directionRatios_IfcDirection  inst:IfcReal_List_753 .

inst:IfcReal_List_754
        rdf:type  ifc:IfcReal_List .

inst:IfcReal_List_755
        rdf:type  ifc:IfcReal_List .

inst:IfcReal_756  rdf:type  ifc:IfcReal ;
        express:hasDouble  1.9428903E-16 .

inst:IfcReal_List_753
        list:hasContents  inst:IfcReal_756 ;
        list:hasNext      inst:IfcReal_List_754 .

inst:IfcReal_757  rdf:type  ifc:IfcReal ;
        express:hasDouble  "-1."^^xsd:double .

inst:IfcReal_List_754
        list:hasContents  inst:IfcReal_757 ;
        list:hasNext      inst:IfcReal_List_755 .

inst:IfcReal_List_755
        list:hasContents  inst:IfcReal_529 .

inst:IfcGridPlacement_1325
        ifc:placementLocation_IfcGridPlacement  inst:IfcVirtualGridIntersection_1322 .

inst:IfcRelAssociatesMaterial_1328
        rdf:type  ifc:IfcRelAssociatesMaterial .

inst:IfcGloballyUniqueId_758
        rdf:type           ifc:IfcGloballyUniqueId ;
        express:hasString  "1LPl3a4iPBlOYvfIxAwQj2" .

inst:IfcRelAssociatesMaterial_1328
        ifc:globalId_IfcRoot      inst:IfcGloballyUniqueId_758 ;
        ifc:ownerHistory_IfcRoot  inst:IfcOwnerHistory_13 ;
        ifc:relatedObjects_IfcRelAssociates  inst:IfcColumn_1275 ;
        ifc:relatingMaterial_IfcRelAssociatesMaterial  inst:IfcMaterial_354 .

inst:IfcAxis2Placement3D_2356
        rdf:type                      ifc:IfcAxis2Placement3D ;
        ifc:location_IfcPlacement     inst:IfcCartesianPoint_2078 ;
        ifc:axis_IfcAxis2Placement3D  inst:IfcDirection_36 ;
        ifc:refDirection_IfcAxis2Placement3D  inst:IfcDirection_2348 .

inst:IfcLocalPlacement_2359
        ifc:placementRelTo_IfcLocalPlacement  inst:IfcLocalPlacement_90 ;
        ifc:relativePlacement_IfcLocalPlacement  inst:IfcAxis2Placement3D_2356 .

inst:IfcColumn_1338  rdf:type  ifc:IfcColumn .

inst:IfcGloballyUniqueId_759
        rdf:type           ifc:IfcGloballyUniqueId ;
        express:hasString  "0eJs1Ppf94_RzyPXHgJ3IY" .

inst:IfcColumn_1338  ifc:globalId_IfcRoot  inst:IfcGloballyUniqueId_759 ;
        ifc:ownerHistory_IfcRoot  inst:IfcOwnerHistory_13 ;
        ifc:name_IfcRoot          inst:IfcLabel_587 .

inst:IfcGridPlacement_1388
        rdf:type  ifc:IfcGridPlacement .

inst:IfcColumn_1338  ifc:objectPlacement_IfcProduct  inst:IfcGridPlacement_1388 .

inst:IfcProductDefinitionShape_1381
        rdf:type  ifc:IfcProductDefinitionShape .

inst:IfcColumn_1338  ifc:representation_IfcProduct  inst:IfcProductDefinitionShape_1381 .

inst:IfcRelAssociatesMaterial_2362
        rdf:type  ifc:IfcRelAssociatesMaterial .

inst:IfcGloballyUniqueId_760
        rdf:type           ifc:IfcGloballyUniqueId ;
        express:hasString  "3aQPpPgfX3hR4JMj79gyHg" .

inst:IfcRelAssociatesMaterial_2362
        ifc:globalId_IfcRoot      inst:IfcGloballyUniqueId_760 ;
        ifc:ownerHistory_IfcRoot  inst:IfcOwnerHistory_13 ;
        ifc:relatedObjects_IfcRelAssociates  inst:IfcBeam_2301 ;
        ifc:relatingMaterial_IfcRelAssociatesMaterial  inst:IfcMaterial_354 .

inst:IfcAxis2Placement2D_320
        rdf:type                   ifc:IfcAxis2Placement2D ;
        ifc:location_IfcPlacement  inst:IfcCartesianPoint_1046 ;
        ifc:refDirection_IfcAxis2Placement2D  inst:IfcDirection_2050 .

inst:IfcRectangleProfileDef_323
        rdf:type                       ifc:IfcRectangleProfileDef ;
        ifc:profileType_IfcProfileDef  ifc:AREA ;
        ifc:profileName_IfcProfileDef  inst:IfcLabel_538 ;
        ifc:position_IfcParameterizedProfileDef  inst:IfcAxis2Placement2D_320 ;
        ifc:xDim_IfcRectangleProfileDef  inst:IfcPositiveLengthMeasure_552 ;
        ifc:yDim_IfcRectangleProfileDef  inst:IfcPositiveLengthMeasure_552 .

inst:IfcBeam_2372  rdf:type  ifc:IfcBeam .

inst:IfcGloballyUniqueId_761
        rdf:type           ifc:IfcGloballyUniqueId ;
        express:hasString  "1Eouahjef86QHNj1gS81gC" .

inst:IfcBeam_2372  ifc:globalId_IfcRoot  inst:IfcGloballyUniqueId_761 ;
        ifc:ownerHistory_IfcRoot  inst:IfcOwnerHistory_13 ;
        ifc:name_IfcRoot          inst:IfcLabel_583 .

inst:IfcLocalPlacement_2426
        rdf:type  ifc:IfcLocalPlacement .

inst:IfcBeam_2372  ifc:objectPlacement_IfcProduct  inst:IfcLocalPlacement_2426 .

inst:IfcProductDefinitionShape_2415
        rdf:type  ifc:IfcProductDefinitionShape .

inst:IfcBeam_2372  ifc:representation_IfcProduct  inst:IfcProductDefinitionShape_2415 .

inst:IfcExtrudedAreaSolid_327
        rdf:type                        ifc:IfcExtrudedAreaSolid ;
        ifc:sweptArea_IfcSweptAreaSolid  inst:IfcRectangleProfileDef_323 ;
        ifc:position_IfcSweptAreaSolid  inst:IfcAxis2Placement3D_1054 ;
        ifc:extrudedDirection_IfcExtrudedAreaSolid  inst:IfcDirection_36 ;
        ifc:depth_IfcExtrudedAreaSolid  inst:IfcLengthMeasure_558 .

inst:IfcDirection_330
        rdf:type  ifc:IfcDirection .

inst:IfcReal_List_762
        rdf:type  ifc:IfcReal_List .

inst:IfcDirection_330
        ifc:directionRatios_IfcDirection  inst:IfcReal_List_762 .

inst:IfcReal_List_763
        rdf:type  ifc:IfcReal_List .

inst:IfcReal_764  rdf:type  ifc:IfcReal ;
        express:hasDouble  6.1232340E-17 .

inst:IfcReal_List_762
        list:hasContents  inst:IfcReal_764 ;
        list:hasNext      inst:IfcReal_List_763 .

inst:IfcReal_List_763
        list:hasContents  inst:IfcReal_528 .

inst:IfcShapeRepresentation_338
        rdf:type                     ifc:IfcShapeRepresentation ;
        ifc:contextOfItems_IfcRepresentation  inst:IfcGeometricRepresentationSubContext_53 ;
        ifc:representationIdentifier_IfcRepresentation  inst:IfcLabel_544 ;
        ifc:representationType_IfcRepresentation  inst:IfcLabel_545 ;
        ifc:items_IfcRepresentation  inst:IfcExtrudedAreaSolid_327 .

inst:IfcAxis2Placement2D_1365
        rdf:type                   ifc:IfcAxis2Placement2D ;
        ifc:location_IfcPlacement  inst:IfcCartesianPoint_1046 ;
        ifc:refDirection_IfcAxis2Placement2D  inst:IfcDirection_2050 .

inst:IfcRepresentation_List_765
        rdf:type  ifc:IfcRepresentation_List .

inst:IfcProductDefinitionShape_344
        ifc:representations_IfcProductRepresentation  inst:IfcRepresentation_List_765 .

inst:IfcRepresentation_List_765
        list:hasContents  inst:IfcShapeRepresentation_338 .

inst:IfcRectangleProfileDef_1368
        rdf:type                       ifc:IfcRectangleProfileDef ;
        ifc:profileType_IfcProfileDef  ifc:AREA ;
        ifc:profileName_IfcProfileDef  inst:IfcLabel_538 ;
        ifc:position_IfcParameterizedProfileDef  inst:IfcAxis2Placement2D_1365 ;
        ifc:xDim_IfcRectangleProfileDef  inst:IfcPositiveLengthMeasure_552 ;
        ifc:yDim_IfcRectangleProfileDef  inst:IfcPositiveLengthMeasure_552 .

inst:IfcVirtualGridIntersection_348
        rdf:type  ifc:IfcVirtualGridIntersection .

inst:IfcGridAxis_List_766
        rdf:type  ifc:IfcGridAxis_List .

inst:IfcVirtualGridIntersection_348
        ifc:intersectingAxes_IfcVirtualGridIntersection  inst:IfcGridAxis_List_766 .

inst:IfcGridAxis_List_767
        rdf:type  ifc:IfcGridAxis_List .

inst:IfcGridAxis_List_766
        list:hasContents  inst:IfcGridAxis_268 ;
        list:hasNext      inst:IfcGridAxis_List_767 .

inst:IfcGridAxis_List_767
        list:hasContents  inst:IfcGridAxis_183 .

inst:IfcLengthMeasure_List_768
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcVirtualGridIntersection_348
        ifc:offsetDistances_IfcVirtualGridIntersection  inst:IfcLengthMeasure_List_768 .

inst:IfcLengthMeasure_List_769
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_770
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_768
        list:hasContents  inst:IfcReal_529 ;
        list:hasNext      inst:IfcLengthMeasure_List_769 .

inst:IfcLengthMeasure_List_769
        list:hasContents  inst:IfcReal_529 ;
        list:hasNext      inst:IfcLengthMeasure_List_770 .

inst:IfcLengthMeasure_List_770
        list:hasContents  inst:IfcReal_529 .

inst:IfcExtrudedAreaSolid_1372
        rdf:type                        ifc:IfcExtrudedAreaSolid ;
        ifc:sweptArea_IfcSweptAreaSolid  inst:IfcRectangleProfileDef_1368 ;
        ifc:position_IfcSweptAreaSolid  inst:IfcAxis2Placement3D_1054 ;
        ifc:extrudedDirection_IfcExtrudedAreaSolid  inst:IfcDirection_36 ;
        ifc:depth_IfcExtrudedAreaSolid  inst:IfcLengthMeasure_558 .

inst:IfcGridPlacement_351
        ifc:placementLocation_IfcGridPlacement  inst:IfcVirtualGridIntersection_348 .

inst:IfcShapeRepresentation_1375
        rdf:type                     ifc:IfcShapeRepresentation ;
        ifc:contextOfItems_IfcRepresentation  inst:IfcGeometricRepresentationSubContext_53 ;
        ifc:representationIdentifier_IfcRepresentation  inst:IfcLabel_544 ;
        ifc:representationType_IfcRepresentation  inst:IfcLabel_545 ;
        ifc:items_IfcRepresentation  inst:IfcExtrudedAreaSolid_1372 .

inst:IfcAxis2Placement2D_2399
        rdf:type                   ifc:IfcAxis2Placement2D ;
        ifc:location_IfcPlacement  inst:IfcCartesianPoint_2054 ;
        ifc:refDirection_IfcAxis2Placement2D  inst:IfcDirection_2050 .

inst:IfcLabel_771  rdf:type  ifc:IfcLabel ;
        express:hasString  "Structural Concrete" .

inst:IfcMaterial_354  ifc:name_IfcMaterial  inst:IfcLabel_771 .

inst:IfcRectangleProfileDef_2402
        rdf:type                       ifc:IfcRectangleProfileDef ;
        ifc:profileType_IfcProfileDef  ifc:AREA ;
        ifc:profileName_IfcProfileDef  inst:IfcLabel_538 ;
        ifc:position_IfcParameterizedProfileDef  inst:IfcAxis2Placement2D_2399 ;
        ifc:xDim_IfcRectangleProfileDef  inst:IfcPositiveLengthMeasure_539 ;
        ifc:yDim_IfcRectangleProfileDef  inst:IfcPositiveLengthMeasure_539 .

inst:IfcRepresentation_List_772
        rdf:type  ifc:IfcRepresentation_List .

inst:IfcProductDefinitionShape_1381
        ifc:representations_IfcProductRepresentation  inst:IfcRepresentation_List_772 .

inst:IfcRepresentation_List_772
        list:hasContents  inst:IfcShapeRepresentation_1375 .

inst:IfcExtrudedAreaSolid_2406
        rdf:type                        ifc:IfcExtrudedAreaSolid ;
        ifc:sweptArea_IfcSweptAreaSolid  inst:IfcRectangleProfileDef_2402 ;
        ifc:position_IfcSweptAreaSolid  inst:IfcAxis2Placement3D_2062 ;
        ifc:extrudedDirection_IfcExtrudedAreaSolid  inst:IfcDirection_36 ;
        ifc:depth_IfcExtrudedAreaSolid  inst:IfcPositiveLengthMeasure_540 .

inst:IfcVirtualGridIntersection_1385
        rdf:type  ifc:IfcVirtualGridIntersection .

inst:IfcGridAxis_List_773
        rdf:type  ifc:IfcGridAxis_List .

inst:IfcVirtualGridIntersection_1385
        ifc:intersectingAxes_IfcVirtualGridIntersection  inst:IfcGridAxis_List_773 .

inst:IfcGridAxis_List_774
        rdf:type  ifc:IfcGridAxis_List .

inst:IfcGridAxis_List_773
        list:hasContents  inst:IfcGridAxis_234 ;
        list:hasNext      inst:IfcGridAxis_List_774 .

inst:IfcGridAxis_List_774
        list:hasContents  inst:IfcGridAxis_115 .

inst:IfcLengthMeasure_List_775
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcVirtualGridIntersection_1385
        ifc:offsetDistances_IfcVirtualGridIntersection  inst:IfcLengthMeasure_List_775 .

inst:IfcLengthMeasure_List_776
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_777
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_775
        list:hasContents  inst:IfcReal_529 ;
        list:hasNext      inst:IfcLengthMeasure_List_776 .

inst:IfcLengthMeasure_List_776
        list:hasContents  inst:IfcReal_529 ;
        list:hasNext      inst:IfcLengthMeasure_List_777 .

inst:IfcLengthMeasure_List_777
        list:hasContents  inst:IfcReal_529 .

inst:IfcShapeRepresentation_2409
        rdf:type                     ifc:IfcShapeRepresentation ;
        ifc:contextOfItems_IfcRepresentation  inst:IfcGeometricRepresentationSubContext_53 ;
        ifc:representationIdentifier_IfcRepresentation  inst:IfcLabel_544 ;
        ifc:representationType_IfcRepresentation  inst:IfcLabel_545 ;
        ifc:items_IfcRepresentation  inst:IfcExtrudedAreaSolid_2406 .

inst:IfcGridPlacement_1388
        ifc:placementLocation_IfcGridPlacement  inst:IfcVirtualGridIntersection_1385 .

inst:IfcRelAssociatesMaterial_1391
        rdf:type  ifc:IfcRelAssociatesMaterial .

inst:IfcGloballyUniqueId_778
        rdf:type           ifc:IfcGloballyUniqueId ;
        express:hasString  "3sjfDIOc9F1ec0f1lD90H2" .

inst:IfcRelAssociatesMaterial_1391
        ifc:globalId_IfcRoot      inst:IfcGloballyUniqueId_778 ;
        ifc:ownerHistory_IfcRoot  inst:IfcOwnerHistory_13 ;
        ifc:relatedObjects_IfcRelAssociates  inst:IfcColumn_1338 ;
        ifc:relatingMaterial_IfcRelAssociatesMaterial  inst:IfcMaterial_354 .

inst:IfcRepresentation_List_779
        rdf:type  ifc:IfcRepresentation_List .

inst:IfcProductDefinitionShape_2415
        ifc:representations_IfcProductRepresentation  inst:IfcRepresentation_List_779 .

inst:IfcRepresentation_List_779
        list:hasContents  inst:IfcShapeRepresentation_2409 .

inst:IfcCartesianPoint_2419
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_780
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_2419
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_780 .

inst:IfcLengthMeasure_List_781
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_782
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_783
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "2000."^^xsd:double .

inst:IfcLengthMeasure_List_780
        list:hasContents  inst:IfcLengthMeasure_783 ;
        list:hasNext      inst:IfcLengthMeasure_List_781 .

inst:IfcLengthMeasure_List_781
        list:hasContents  inst:IfcLengthMeasure_557 ;
        list:hasNext      inst:IfcLengthMeasure_List_782 .

inst:IfcLengthMeasure_List_782
        list:hasContents  inst:IfcLengthMeasure_558 .

inst:IfcAxis2Placement3D_2423
        rdf:type                      ifc:IfcAxis2Placement3D ;
        ifc:location_IfcPlacement     inst:IfcCartesianPoint_2419 ;
        ifc:axis_IfcAxis2Placement3D  inst:IfcDirection_36 ;
        ifc:refDirection_IfcAxis2Placement3D  inst:IfcDirection_28 .

inst:IfcColumn_1401  rdf:type  ifc:IfcColumn .

inst:IfcGloballyUniqueId_784
        rdf:type           ifc:IfcGloballyUniqueId ;
        express:hasString  "1oPiECsTf5rBXznvK9MdP_" .

inst:IfcColumn_1401  ifc:globalId_IfcRoot  inst:IfcGloballyUniqueId_784 ;
        ifc:ownerHistory_IfcRoot  inst:IfcOwnerHistory_13 ;
        ifc:name_IfcRoot          inst:IfcLabel_587 .

inst:IfcGridPlacement_1451
        rdf:type  ifc:IfcGridPlacement .

inst:IfcColumn_1401  ifc:objectPlacement_IfcProduct  inst:IfcGridPlacement_1451 .

inst:IfcProductDefinitionShape_1444
        rdf:type  ifc:IfcProductDefinitionShape .

inst:IfcColumn_1401  ifc:representation_IfcProduct  inst:IfcProductDefinitionShape_1444 .

inst:IfcLocalPlacement_2426
        ifc:placementRelTo_IfcLocalPlacement  inst:IfcLocalPlacement_90 ;
        ifc:relativePlacement_IfcLocalPlacement  inst:IfcAxis2Placement3D_2423 .

inst:IfcRelAssociatesMaterial_2429
        rdf:type  ifc:IfcRelAssociatesMaterial .

inst:IfcGloballyUniqueId_785
        rdf:type           ifc:IfcGloballyUniqueId ;
        express:hasString  "12hCI2$Af87h_yq7FqKlS_" .

inst:IfcRelAssociatesMaterial_2429
        ifc:globalId_IfcRoot      inst:IfcGloballyUniqueId_785 ;
        ifc:ownerHistory_IfcRoot  inst:IfcOwnerHistory_13 ;
        ifc:relatedObjects_IfcRelAssociates  inst:IfcBeam_2372 ;
        ifc:relatingMaterial_IfcRelAssociatesMaterial  inst:IfcMaterial_354 .

inst:IfcBeam_2439  rdf:type  ifc:IfcBeam .

inst:IfcGloballyUniqueId_786
        rdf:type           ifc:IfcGloballyUniqueId ;
        express:hasString  "2inLgxVS94Te6b5ADXjH94" .

inst:IfcBeam_2439  ifc:globalId_IfcRoot  inst:IfcGloballyUniqueId_786 ;
        ifc:ownerHistory_IfcRoot  inst:IfcOwnerHistory_13 ;
        ifc:name_IfcRoot          inst:IfcLabel_583 .

inst:IfcLocalPlacement_2497
        rdf:type  ifc:IfcLocalPlacement .

inst:IfcBeam_2439  ifc:objectPlacement_IfcProduct  inst:IfcLocalPlacement_2497 .

inst:IfcProductDefinitionShape_2482
        rdf:type  ifc:IfcProductDefinitionShape .

inst:IfcBeam_2439  ifc:representation_IfcProduct  inst:IfcProductDefinitionShape_2482 .

inst:IfcAxis2Placement2D_1428
        rdf:type                   ifc:IfcAxis2Placement2D ;
        ifc:location_IfcPlacement  inst:IfcCartesianPoint_1046 ;
        ifc:refDirection_IfcAxis2Placement2D  inst:IfcDirection_2050 .

inst:IfcRectangleProfileDef_1431
        rdf:type                       ifc:IfcRectangleProfileDef ;
        ifc:profileType_IfcProfileDef  ifc:AREA ;
        ifc:profileName_IfcProfileDef  inst:IfcLabel_538 ;
        ifc:position_IfcParameterizedProfileDef  inst:IfcAxis2Placement2D_1428 ;
        ifc:xDim_IfcRectangleProfileDef  inst:IfcPositiveLengthMeasure_552 ;
        ifc:yDim_IfcRectangleProfileDef  inst:IfcPositiveLengthMeasure_552 .

inst:IfcExtrudedAreaSolid_1435
        rdf:type                        ifc:IfcExtrudedAreaSolid ;
        ifc:sweptArea_IfcSweptAreaSolid  inst:IfcRectangleProfileDef_1431 ;
        ifc:position_IfcSweptAreaSolid  inst:IfcAxis2Placement3D_1054 ;
        ifc:extrudedDirection_IfcExtrudedAreaSolid  inst:IfcDirection_36 ;
        ifc:depth_IfcExtrudedAreaSolid  inst:IfcLengthMeasure_558 .

inst:IfcShapeRepresentation_1438
        rdf:type                     ifc:IfcShapeRepresentation ;
        ifc:contextOfItems_IfcRepresentation  inst:IfcGeometricRepresentationSubContext_53 ;
        ifc:representationIdentifier_IfcRepresentation  inst:IfcLabel_544 ;
        ifc:representationType_IfcRepresentation  inst:IfcLabel_545 ;
        ifc:items_IfcRepresentation  inst:IfcExtrudedAreaSolid_1435 .

inst:IfcAxis2Placement2D_2466
        rdf:type                   ifc:IfcAxis2Placement2D ;
        ifc:location_IfcPlacement  inst:IfcCartesianPoint_2054 ;
        ifc:refDirection_IfcAxis2Placement2D  inst:IfcDirection_2050 .

inst:IfcRepresentation_List_787
        rdf:type  ifc:IfcRepresentation_List .

inst:IfcProductDefinitionShape_1444
        ifc:representations_IfcProductRepresentation  inst:IfcRepresentation_List_787 .

inst:IfcRepresentation_List_787
        list:hasContents  inst:IfcShapeRepresentation_1438 .

inst:IfcRectangleProfileDef_2469
        rdf:type                       ifc:IfcRectangleProfileDef ;
        ifc:profileType_IfcProfileDef  ifc:AREA ;
        ifc:profileName_IfcProfileDef  inst:IfcLabel_538 ;
        ifc:position_IfcParameterizedProfileDef  inst:IfcAxis2Placement2D_2466 ;
        ifc:xDim_IfcRectangleProfileDef  inst:IfcPositiveLengthMeasure_539 ;
        ifc:yDim_IfcRectangleProfileDef  inst:IfcPositiveLengthMeasure_539 .

inst:IfcVirtualGridIntersection_1448
        rdf:type  ifc:IfcVirtualGridIntersection .

inst:IfcGridAxis_List_788
        rdf:type  ifc:IfcGridAxis_List .

inst:IfcVirtualGridIntersection_1448
        ifc:intersectingAxes_IfcVirtualGridIntersection  inst:IfcGridAxis_List_788 .

inst:IfcGridAxis_List_789
        rdf:type  ifc:IfcGridAxis_List .

inst:IfcGridAxis_List_788
        list:hasContents  inst:IfcGridAxis_217 ;
        list:hasNext      inst:IfcGridAxis_List_789 .

inst:IfcGridAxis_List_789
        list:hasContents  inst:IfcGridAxis_183 .

inst:IfcLengthMeasure_List_790
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcVirtualGridIntersection_1448
        ifc:offsetDistances_IfcVirtualGridIntersection  inst:IfcLengthMeasure_List_790 .

inst:IfcLengthMeasure_List_791
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_792
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_790
        list:hasContents  inst:IfcReal_529 ;
        list:hasNext      inst:IfcLengthMeasure_List_791 .

inst:IfcLengthMeasure_List_791
        list:hasContents  inst:IfcReal_529 ;
        list:hasNext      inst:IfcLengthMeasure_List_792 .

inst:IfcLengthMeasure_List_792
        list:hasContents  inst:IfcReal_529 .

inst:IfcExtrudedAreaSolid_2473
        rdf:type                        ifc:IfcExtrudedAreaSolid ;
        ifc:sweptArea_IfcSweptAreaSolid  inst:IfcRectangleProfileDef_2469 ;
        ifc:position_IfcSweptAreaSolid  inst:IfcAxis2Placement3D_2062 ;
        ifc:extrudedDirection_IfcExtrudedAreaSolid  inst:IfcDirection_36 ;
        ifc:depth_IfcExtrudedAreaSolid  inst:IfcLengthMeasure_731 .

inst:IfcGridPlacement_1451
        ifc:placementLocation_IfcGridPlacement  inst:IfcVirtualGridIntersection_1448 .

inst:IfcShapeRepresentation_2476
        rdf:type                     ifc:IfcShapeRepresentation ;
        ifc:contextOfItems_IfcRepresentation  inst:IfcGeometricRepresentationSubContext_53 ;
        ifc:representationIdentifier_IfcRepresentation  inst:IfcLabel_544 ;
        ifc:representationType_IfcRepresentation  inst:IfcLabel_545 ;
        ifc:items_IfcRepresentation  inst:IfcExtrudedAreaSolid_2473 .

inst:IfcRelAssociatesMaterial_1454
        rdf:type  ifc:IfcRelAssociatesMaterial .

inst:IfcGloballyUniqueId_793
        rdf:type           ifc:IfcGloballyUniqueId ;
        express:hasString  "1nQn_EHJ1CAgQ3A_5kVjgu" .

inst:IfcRelAssociatesMaterial_1454
        ifc:globalId_IfcRoot      inst:IfcGloballyUniqueId_793 ;
        ifc:ownerHistory_IfcRoot  inst:IfcOwnerHistory_13 ;
        ifc:relatedObjects_IfcRelAssociates  inst:IfcColumn_1401 ;
        ifc:relatingMaterial_IfcRelAssociatesMaterial  inst:IfcMaterial_354 .

inst:IfcRepresentation_List_794
        rdf:type  ifc:IfcRepresentation_List .

inst:IfcProductDefinitionShape_2482
        ifc:representations_IfcProductRepresentation  inst:IfcRepresentation_List_794 .

inst:IfcRepresentation_List_794
        list:hasContents  inst:IfcShapeRepresentation_2476 .

inst:IfcDirection_2486
        rdf:type  ifc:IfcDirection .

inst:IfcReal_List_795
        rdf:type  ifc:IfcReal_List .

inst:IfcDirection_2486
        ifc:directionRatios_IfcDirection  inst:IfcReal_List_795 .

inst:IfcReal_List_796
        rdf:type  ifc:IfcReal_List .

inst:IfcReal_List_797
        rdf:type  ifc:IfcReal_List .

inst:IfcReal_798  rdf:type  ifc:IfcReal ;
        express:hasDouble  1.6653345E-16 .

inst:IfcReal_List_795
        list:hasContents  inst:IfcReal_798 ;
        list:hasNext      inst:IfcReal_List_796 .

inst:IfcReal_List_796
        list:hasContents  inst:IfcReal_757 ;
        list:hasNext      inst:IfcReal_List_797 .

inst:IfcReal_List_797
        list:hasContents  inst:IfcReal_529 .

inst:IfcColumn_1464  rdf:type  ifc:IfcColumn .

inst:IfcGloballyUniqueId_799
        rdf:type           ifc:IfcGloballyUniqueId ;
        express:hasString  "1W7_sZW7157AYdrmDlC7VM" .

inst:IfcColumn_1464  ifc:globalId_IfcRoot  inst:IfcGloballyUniqueId_799 ;
        ifc:ownerHistory_IfcRoot  inst:IfcOwnerHistory_13 ;
        ifc:name_IfcRoot          inst:IfcLabel_587 .

inst:IfcGridPlacement_1514
        rdf:type  ifc:IfcGridPlacement .

inst:IfcColumn_1464  ifc:objectPlacement_IfcProduct  inst:IfcGridPlacement_1514 .

inst:IfcProductDefinitionShape_1507
        rdf:type  ifc:IfcProductDefinitionShape .

inst:IfcColumn_1464  ifc:representation_IfcProduct  inst:IfcProductDefinitionShape_1507 .

inst:IfcCartesianPoint_2490
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_800
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_2490
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_800 .

inst:IfcLengthMeasure_List_801
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_802
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_800
        list:hasContents  inst:IfcLengthMeasure_556 ;
        list:hasNext      inst:IfcLengthMeasure_List_801 .

inst:IfcLengthMeasure_803
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "8000."^^xsd:double .

inst:IfcLengthMeasure_List_801
        list:hasContents  inst:IfcLengthMeasure_803 ;
        list:hasNext      inst:IfcLengthMeasure_List_802 .

inst:IfcLengthMeasure_List_802
        list:hasContents  inst:IfcLengthMeasure_558 .

inst:IfcAxis2Placement3D_2494
        rdf:type                      ifc:IfcAxis2Placement3D ;
        ifc:location_IfcPlacement     inst:IfcCartesianPoint_2490 ;
        ifc:axis_IfcAxis2Placement3D  inst:IfcDirection_36 ;
        ifc:refDirection_IfcAxis2Placement3D  inst:IfcDirection_2486 .

inst:IfcLocalPlacement_2497
        ifc:placementRelTo_IfcLocalPlacement  inst:IfcLocalPlacement_90 ;
        ifc:relativePlacement_IfcLocalPlacement  inst:IfcAxis2Placement3D_2494 .

inst:IfcRelAssociatesMaterial_2500
        rdf:type  ifc:IfcRelAssociatesMaterial .

inst:IfcGloballyUniqueId_804
        rdf:type           ifc:IfcGloballyUniqueId ;
        express:hasString  "3SSTTenEP0ZxzQoK27AEY3" .

inst:IfcRelAssociatesMaterial_2500
        ifc:globalId_IfcRoot      inst:IfcGloballyUniqueId_804 ;
        ifc:ownerHistory_IfcRoot  inst:IfcOwnerHistory_13 ;
        ifc:relatedObjects_IfcRelAssociates  inst:IfcBeam_2439 ;
        ifc:relatingMaterial_IfcRelAssociatesMaterial  inst:IfcMaterial_354 .

inst:IfcBeam_2510  rdf:type  ifc:IfcBeam .

inst:IfcGloballyUniqueId_805
        rdf:type           ifc:IfcGloballyUniqueId ;
        express:hasString  "1v5rDYwSnB7gVTP_r2LzCU" .

inst:IfcBeam_2510  ifc:globalId_IfcRoot  inst:IfcGloballyUniqueId_805 ;
        ifc:ownerHistory_IfcRoot  inst:IfcOwnerHistory_13 ;
        ifc:name_IfcRoot          inst:IfcLabel_583 .

inst:IfcLocalPlacement_2568
        rdf:type  ifc:IfcLocalPlacement .

inst:IfcBeam_2510  ifc:objectPlacement_IfcProduct  inst:IfcLocalPlacement_2568 .

inst:IfcProductDefinitionShape_2553
        rdf:type  ifc:IfcProductDefinitionShape .

inst:IfcBeam_2510  ifc:representation_IfcProduct  inst:IfcProductDefinitionShape_2553 .

inst:IfcAxis2Placement2D_1491
        rdf:type                   ifc:IfcAxis2Placement2D ;
        ifc:location_IfcPlacement  inst:IfcCartesianPoint_1046 ;
        ifc:refDirection_IfcAxis2Placement2D  inst:IfcDirection_2050 .

inst:IfcRectangleProfileDef_1494
        rdf:type                       ifc:IfcRectangleProfileDef ;
        ifc:profileType_IfcProfileDef  ifc:AREA ;
        ifc:profileName_IfcProfileDef  inst:IfcLabel_538 ;
        ifc:position_IfcParameterizedProfileDef  inst:IfcAxis2Placement2D_1491 ;
        ifc:xDim_IfcRectangleProfileDef  inst:IfcPositiveLengthMeasure_552 ;
        ifc:yDim_IfcRectangleProfileDef  inst:IfcPositiveLengthMeasure_552 .

inst:IfcExtrudedAreaSolid_1498
        rdf:type                        ifc:IfcExtrudedAreaSolid ;
        ifc:sweptArea_IfcSweptAreaSolid  inst:IfcRectangleProfileDef_1494 ;
        ifc:position_IfcSweptAreaSolid  inst:IfcAxis2Placement3D_1054 ;
        ifc:extrudedDirection_IfcExtrudedAreaSolid  inst:IfcDirection_36 ;
        ifc:depth_IfcExtrudedAreaSolid  inst:IfcLengthMeasure_558 .

inst:IfcShapeRepresentation_1501
        rdf:type                     ifc:IfcShapeRepresentation ;
        ifc:contextOfItems_IfcRepresentation  inst:IfcGeometricRepresentationSubContext_53 ;
        ifc:representationIdentifier_IfcRepresentation  inst:IfcLabel_544 ;
        ifc:representationType_IfcRepresentation  inst:IfcLabel_545 ;
        ifc:items_IfcRepresentation  inst:IfcExtrudedAreaSolid_1498 .

inst:IfcRepresentation_List_806
        rdf:type  ifc:IfcRepresentation_List .

inst:IfcProductDefinitionShape_1507
        ifc:representations_IfcProductRepresentation  inst:IfcRepresentation_List_806 .

inst:IfcRepresentation_List_806
        list:hasContents  inst:IfcShapeRepresentation_1501 .

inst:IfcVirtualGridIntersection_1511
        rdf:type  ifc:IfcVirtualGridIntersection .

inst:IfcGridAxis_List_807
        rdf:type  ifc:IfcGridAxis_List .

inst:IfcVirtualGridIntersection_1511
        ifc:intersectingAxes_IfcVirtualGridIntersection  inst:IfcGridAxis_List_807 .

inst:IfcGridAxis_List_808
        rdf:type  ifc:IfcGridAxis_List .

inst:IfcGridAxis_List_807
        list:hasContents  inst:IfcGridAxis_217 ;
        list:hasNext      inst:IfcGridAxis_List_808 .

inst:IfcGridAxis_List_808
        list:hasContents  inst:IfcGridAxis_166 .

inst:IfcLengthMeasure_List_809
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcVirtualGridIntersection_1511
        ifc:offsetDistances_IfcVirtualGridIntersection  inst:IfcLengthMeasure_List_809 .

inst:IfcLengthMeasure_List_810
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_811
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_809
        list:hasContents  inst:IfcReal_529 ;
        list:hasNext      inst:IfcLengthMeasure_List_810 .

inst:IfcLengthMeasure_List_810
        list:hasContents  inst:IfcReal_529 ;
        list:hasNext      inst:IfcLengthMeasure_List_811 .

inst:IfcLengthMeasure_List_811
        list:hasContents  inst:IfcReal_529 .

inst:IfcAxis2Placement2D_2537
        rdf:type                   ifc:IfcAxis2Placement2D ;
        ifc:location_IfcPlacement  inst:IfcCartesianPoint_2054 ;
        ifc:refDirection_IfcAxis2Placement2D  inst:IfcDirection_2050 .

inst:IfcGridPlacement_1514
        ifc:placementLocation_IfcGridPlacement  inst:IfcVirtualGridIntersection_1511 .

inst:IfcRectangleProfileDef_2540
        rdf:type                       ifc:IfcRectangleProfileDef ;
        ifc:profileType_IfcProfileDef  ifc:AREA ;
        ifc:profileName_IfcProfileDef  inst:IfcLabel_538 ;
        ifc:position_IfcParameterizedProfileDef  inst:IfcAxis2Placement2D_2537 ;
        ifc:xDim_IfcRectangleProfileDef  inst:IfcPositiveLengthMeasure_539 ;
        ifc:yDim_IfcRectangleProfileDef  inst:IfcPositiveLengthMeasure_539 .

inst:IfcRelAssociatesMaterial_1517
        rdf:type  ifc:IfcRelAssociatesMaterial .

inst:IfcGloballyUniqueId_812
        rdf:type           ifc:IfcGloballyUniqueId ;
        express:hasString  "3hdVqzwW58b8CHoK91M_e3" .

inst:IfcRelAssociatesMaterial_1517
        ifc:globalId_IfcRoot      inst:IfcGloballyUniqueId_812 ;
        ifc:ownerHistory_IfcRoot  inst:IfcOwnerHistory_13 ;
        ifc:relatedObjects_IfcRelAssociates  inst:IfcColumn_1464 ;
        ifc:relatingMaterial_IfcRelAssociatesMaterial  inst:IfcMaterial_354 .

inst:IfcExtrudedAreaSolid_2544
        rdf:type                        ifc:IfcExtrudedAreaSolid ;
        ifc:sweptArea_IfcSweptAreaSolid  inst:IfcRectangleProfileDef_2540 ;
        ifc:position_IfcSweptAreaSolid  inst:IfcAxis2Placement3D_2062 ;
        ifc:extrudedDirection_IfcExtrudedAreaSolid  inst:IfcDirection_36 ;
        ifc:depth_IfcExtrudedAreaSolid  inst:IfcLengthMeasure_731 .

inst:IfcShapeRepresentation_2547
        rdf:type                     ifc:IfcShapeRepresentation ;
        ifc:contextOfItems_IfcRepresentation  inst:IfcGeometricRepresentationSubContext_53 ;
        ifc:representationIdentifier_IfcRepresentation  inst:IfcLabel_544 ;
        ifc:representationType_IfcRepresentation  inst:IfcLabel_545 ;
        ifc:items_IfcRepresentation  inst:IfcExtrudedAreaSolid_2544 .

inst:IfcColumn_1527  rdf:type  ifc:IfcColumn .

inst:IfcGloballyUniqueId_813
        rdf:type           ifc:IfcGloballyUniqueId ;
        express:hasString  "1FN9fX7IT6q8a1vB_hVsGg" .

inst:IfcColumn_1527  ifc:globalId_IfcRoot  inst:IfcGloballyUniqueId_813 ;
        ifc:ownerHistory_IfcRoot  inst:IfcOwnerHistory_13 ;
        ifc:name_IfcRoot          inst:IfcLabel_587 .

inst:IfcGridPlacement_1577
        rdf:type  ifc:IfcGridPlacement .

inst:IfcColumn_1527  ifc:objectPlacement_IfcProduct  inst:IfcGridPlacement_1577 .

inst:IfcProductDefinitionShape_1570
        rdf:type  ifc:IfcProductDefinitionShape .

inst:IfcColumn_1527  ifc:representation_IfcProduct  inst:IfcProductDefinitionShape_1570 .

inst:IfcRelAssociatesMaterial_505
        rdf:type  ifc:IfcRelAssociatesMaterial .

inst:IfcGloballyUniqueId_814
        rdf:type           ifc:IfcGloballyUniqueId ;
        express:hasString  "2ATJkq8SP8Nwm_CddbSUNm" .

inst:IfcRelAssociatesMaterial_505
        ifc:globalId_IfcRoot      inst:IfcGloballyUniqueId_814 ;
        ifc:ownerHistory_IfcRoot  inst:IfcOwnerHistory_13 ;
        ifc:relatedObjects_IfcRelAssociates  inst:IfcColumn_293 ;
        ifc:relatingMaterial_IfcRelAssociatesMaterial  inst:IfcMaterial_354 .

inst:IfcRepresentation_List_815
        rdf:type  ifc:IfcRepresentation_List .

inst:IfcProductDefinitionShape_2553
        ifc:representations_IfcProductRepresentation  inst:IfcRepresentation_List_815 .

inst:IfcRepresentation_List_815
        list:hasContents  inst:IfcShapeRepresentation_2547 .

inst:IfcDirection_2557
        rdf:type  ifc:IfcDirection .

inst:IfcReal_List_816
        rdf:type  ifc:IfcReal_List .

inst:IfcDirection_2557
        ifc:directionRatios_IfcDirection  inst:IfcReal_List_816 .

inst:IfcReal_List_817
        rdf:type  ifc:IfcReal_List .

inst:IfcReal_List_818
        rdf:type  ifc:IfcReal_List .

inst:IfcReal_819  rdf:type  ifc:IfcReal ;
        express:hasDouble  2.2204460E-16 .

inst:IfcReal_List_816
        list:hasContents  inst:IfcReal_819 ;
        list:hasNext      inst:IfcReal_List_817 .

inst:IfcReal_List_817
        list:hasContents  inst:IfcReal_757 ;
        list:hasNext      inst:IfcReal_List_818 .

inst:IfcReal_List_818
        list:hasContents  inst:IfcReal_529 .

inst:IfcCartesianPoint_2561
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_820
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_2561
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_820 .

inst:IfcLengthMeasure_List_821
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_822
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_820
        list:hasContents  inst:IfcLengthMeasure_556 ;
        list:hasNext      inst:IfcLengthMeasure_List_821 .

inst:IfcLengthMeasure_823
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "10000."^^xsd:double .

inst:IfcLengthMeasure_List_821
        list:hasContents  inst:IfcLengthMeasure_823 ;
        list:hasNext      inst:IfcLengthMeasure_List_822 .

inst:IfcLengthMeasure_List_822
        list:hasContents  inst:IfcLengthMeasure_558 .

inst:IfcAxis2Placement3D_2565
        rdf:type                      ifc:IfcAxis2Placement3D ;
        ifc:location_IfcPlacement     inst:IfcCartesianPoint_2561 ;
        ifc:axis_IfcAxis2Placement3D  inst:IfcDirection_36 ;
        ifc:refDirection_IfcAxis2Placement3D  inst:IfcDirection_2557 .

inst:IfcColumn_519  rdf:type  ifc:IfcColumn .

inst:IfcGloballyUniqueId_824
        rdf:type           ifc:IfcGloballyUniqueId ;
        express:hasString  "3SVzKRDSn40RvQ9U19U6kS" .

inst:IfcColumn_519  ifc:globalId_IfcRoot  inst:IfcGloballyUniqueId_824 ;
        ifc:ownerHistory_IfcRoot  inst:IfcOwnerHistory_13 ;
        ifc:name_IfcRoot          inst:IfcLabel_587 .

inst:IfcGridPlacement_569
        rdf:type  ifc:IfcGridPlacement .

inst:IfcColumn_519  ifc:objectPlacement_IfcProduct  inst:IfcGridPlacement_569 .

inst:IfcProductDefinitionShape_562
        rdf:type  ifc:IfcProductDefinitionShape .

inst:IfcColumn_519  ifc:representation_IfcProduct  inst:IfcProductDefinitionShape_562 .

inst:IfcLocalPlacement_2568
        ifc:placementRelTo_IfcLocalPlacement  inst:IfcLocalPlacement_90 ;
        ifc:relativePlacement_IfcLocalPlacement  inst:IfcAxis2Placement3D_2565 .

inst:IfcRelAssociatesMaterial_2571
        rdf:type  ifc:IfcRelAssociatesMaterial .

inst:IfcGloballyUniqueId_825
        rdf:type           ifc:IfcGloballyUniqueId ;
        express:hasString  "1X$dpCdIr83vpygtBICfMM" .

inst:IfcRelAssociatesMaterial_2571
        ifc:globalId_IfcRoot      inst:IfcGloballyUniqueId_825 ;
        ifc:ownerHistory_IfcRoot  inst:IfcOwnerHistory_13 ;
        ifc:relatedObjects_IfcRelAssociates  inst:IfcBeam_2510 ;
        ifc:relatingMaterial_IfcRelAssociatesMaterial  inst:IfcMaterial_354 .

inst:IfcAxis2Placement2D_1554
        rdf:type                   ifc:IfcAxis2Placement2D ;
        ifc:location_IfcPlacement  inst:IfcCartesianPoint_1046 ;
        ifc:refDirection_IfcAxis2Placement2D  inst:IfcDirection_2050 .

inst:IfcRectangleProfileDef_1557
        rdf:type                       ifc:IfcRectangleProfileDef ;
        ifc:profileType_IfcProfileDef  ifc:AREA ;
        ifc:profileName_IfcProfileDef  inst:IfcLabel_538 ;
        ifc:position_IfcParameterizedProfileDef  inst:IfcAxis2Placement2D_1554 ;
        ifc:xDim_IfcRectangleProfileDef  inst:IfcPositiveLengthMeasure_552 ;
        ifc:yDim_IfcRectangleProfileDef  inst:IfcPositiveLengthMeasure_552 .

inst:IfcBeam_2581  rdf:type  ifc:IfcBeam .

inst:IfcGloballyUniqueId_826
        rdf:type           ifc:IfcGloballyUniqueId ;
        express:hasString  "0z$eamUiL5uvdZJOfKdMkc" .

inst:IfcBeam_2581  ifc:globalId_IfcRoot  inst:IfcGloballyUniqueId_826 ;
        ifc:ownerHistory_IfcRoot  inst:IfcOwnerHistory_13 ;
        ifc:name_IfcRoot          inst:IfcLabel_583 .

inst:IfcLocalPlacement_2639
        rdf:type  ifc:IfcLocalPlacement .

inst:IfcBeam_2581  ifc:objectPlacement_IfcProduct  inst:IfcLocalPlacement_2639 .

inst:IfcProductDefinitionShape_2624
        rdf:type  ifc:IfcProductDefinitionShape .

inst:IfcBeam_2581  ifc:representation_IfcProduct  inst:IfcProductDefinitionShape_2624 .

inst:IfcExtrudedAreaSolid_1561
        rdf:type                        ifc:IfcExtrudedAreaSolid ;
        ifc:sweptArea_IfcSweptAreaSolid  inst:IfcRectangleProfileDef_1557 ;
        ifc:position_IfcSweptAreaSolid  inst:IfcAxis2Placement3D_1054 ;
        ifc:extrudedDirection_IfcExtrudedAreaSolid  inst:IfcDirection_36 ;
        ifc:depth_IfcExtrudedAreaSolid  inst:IfcLengthMeasure_558 .

inst:IfcShapeRepresentation_1564
        rdf:type                     ifc:IfcShapeRepresentation ;
        ifc:contextOfItems_IfcRepresentation  inst:IfcGeometricRepresentationSubContext_53 ;
        ifc:representationIdentifier_IfcRepresentation  inst:IfcLabel_544 ;
        ifc:representationType_IfcRepresentation  inst:IfcLabel_545 ;
        ifc:items_IfcRepresentation  inst:IfcExtrudedAreaSolid_1561 .

inst:IfcAxis2Placement2D_546
        rdf:type                   ifc:IfcAxis2Placement2D ;
        ifc:location_IfcPlacement  inst:IfcCartesianPoint_1046 ;
        ifc:refDirection_IfcAxis2Placement2D  inst:IfcDirection_2050 .

inst:IfcRepresentation_List_827
        rdf:type  ifc:IfcRepresentation_List .

inst:IfcProductDefinitionShape_1570
        ifc:representations_IfcProductRepresentation  inst:IfcRepresentation_List_827 .

inst:IfcRepresentation_List_827
        list:hasContents  inst:IfcShapeRepresentation_1564 .

inst:IfcRectangleProfileDef_549
        rdf:type                       ifc:IfcRectangleProfileDef ;
        ifc:profileType_IfcProfileDef  ifc:AREA ;
        ifc:profileName_IfcProfileDef  inst:IfcLabel_538 ;
        ifc:position_IfcParameterizedProfileDef  inst:IfcAxis2Placement2D_546 ;
        ifc:xDim_IfcRectangleProfileDef  inst:IfcPositiveLengthMeasure_552 ;
        ifc:yDim_IfcRectangleProfileDef  inst:IfcPositiveLengthMeasure_552 .

inst:IfcVirtualGridIntersection_1574
        rdf:type  ifc:IfcVirtualGridIntersection .

inst:IfcGridAxis_List_828
        rdf:type  ifc:IfcGridAxis_List .

inst:IfcVirtualGridIntersection_1574
        ifc:intersectingAxes_IfcVirtualGridIntersection  inst:IfcGridAxis_List_828 .

inst:IfcGridAxis_List_829
        rdf:type  ifc:IfcGridAxis_List .

inst:IfcGridAxis_List_828
        list:hasContents  inst:IfcGridAxis_217 ;
        list:hasNext      inst:IfcGridAxis_List_829 .

inst:IfcGridAxis_List_829
        list:hasContents  inst:IfcGridAxis_149 .

inst:IfcLengthMeasure_List_830
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcVirtualGridIntersection_1574
        ifc:offsetDistances_IfcVirtualGridIntersection  inst:IfcLengthMeasure_List_830 .

inst:IfcLengthMeasure_List_831
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_832
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_830
        list:hasContents  inst:IfcReal_529 ;
        list:hasNext      inst:IfcLengthMeasure_List_831 .

inst:IfcLengthMeasure_List_831
        list:hasContents  inst:IfcReal_529 ;
        list:hasNext      inst:IfcLengthMeasure_List_832 .

inst:IfcLengthMeasure_List_832
        list:hasContents  inst:IfcReal_529 .

inst:IfcExtrudedAreaSolid_553
        rdf:type                        ifc:IfcExtrudedAreaSolid ;
        ifc:sweptArea_IfcSweptAreaSolid  inst:IfcRectangleProfileDef_549 ;
        ifc:position_IfcSweptAreaSolid  inst:IfcAxis2Placement3D_1054 ;
        ifc:extrudedDirection_IfcExtrudedAreaSolid  inst:IfcDirection_36 ;
        ifc:depth_IfcExtrudedAreaSolid  inst:IfcLengthMeasure_558 .

inst:IfcGridPlacement_1577
        ifc:placementLocation_IfcGridPlacement  inst:IfcVirtualGridIntersection_1574 .

inst:IfcShapeRepresentation_556
        rdf:type                     ifc:IfcShapeRepresentation ;
        ifc:contextOfItems_IfcRepresentation  inst:IfcGeometricRepresentationSubContext_53 ;
        ifc:representationIdentifier_IfcRepresentation  inst:IfcLabel_544 ;
        ifc:representationType_IfcRepresentation  inst:IfcLabel_545 ;
        ifc:items_IfcRepresentation  inst:IfcExtrudedAreaSolid_553 .

inst:IfcRelAssociatesMaterial_1580
        rdf:type  ifc:IfcRelAssociatesMaterial .

inst:IfcGloballyUniqueId_833
        rdf:type           ifc:IfcGloballyUniqueId ;
        express:hasString  "2qMelZARf5XeVVPFD8KNr_" .

inst:IfcRelAssociatesMaterial_1580
        ifc:globalId_IfcRoot      inst:IfcGloballyUniqueId_833 ;
        ifc:ownerHistory_IfcRoot  inst:IfcOwnerHistory_13 ;
        ifc:relatedObjects_IfcRelAssociates  inst:IfcColumn_1527 ;
        ifc:relatingMaterial_IfcRelAssociatesMaterial  inst:IfcMaterial_354 .

inst:IfcAxis2Placement2D_2608
        rdf:type                   ifc:IfcAxis2Placement2D ;
        ifc:location_IfcPlacement  inst:IfcCartesianPoint_2054 ;
        ifc:refDirection_IfcAxis2Placement2D  inst:IfcDirection_2050 .

inst:IfcRepresentation_List_834
        rdf:type  ifc:IfcRepresentation_List .

inst:IfcProductDefinitionShape_562
        ifc:representations_IfcProductRepresentation  inst:IfcRepresentation_List_834 .

inst:IfcRepresentation_List_834
        list:hasContents  inst:IfcShapeRepresentation_556 .

inst:IfcRectangleProfileDef_2611
        rdf:type                       ifc:IfcRectangleProfileDef ;
        ifc:profileType_IfcProfileDef  ifc:AREA ;
        ifc:profileName_IfcProfileDef  inst:IfcLabel_538 ;
        ifc:position_IfcParameterizedProfileDef  inst:IfcAxis2Placement2D_2608 ;
        ifc:xDim_IfcRectangleProfileDef  inst:IfcPositiveLengthMeasure_539 ;
        ifc:yDim_IfcRectangleProfileDef  inst:IfcPositiveLengthMeasure_539 .

inst:IfcVirtualGridIntersection_566
        rdf:type  ifc:IfcVirtualGridIntersection .

inst:IfcGridAxis_List_835
        rdf:type  ifc:IfcGridAxis_List .

inst:IfcVirtualGridIntersection_566
        ifc:intersectingAxes_IfcVirtualGridIntersection  inst:IfcGridAxis_List_835 .

inst:IfcGridAxis_List_836
        rdf:type  ifc:IfcGridAxis_List .

inst:IfcGridAxis_List_835
        list:hasContents  inst:IfcGridAxis_268 ;
        list:hasNext      inst:IfcGridAxis_List_836 .

inst:IfcGridAxis_List_836
        list:hasContents  inst:IfcGridAxis_166 .

inst:IfcLengthMeasure_List_837
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcVirtualGridIntersection_566
        ifc:offsetDistances_IfcVirtualGridIntersection  inst:IfcLengthMeasure_List_837 .

inst:IfcLengthMeasure_List_838
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_839
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_837
        list:hasContents  inst:IfcReal_529 ;
        list:hasNext      inst:IfcLengthMeasure_List_838 .

inst:IfcLengthMeasure_List_838
        list:hasContents  inst:IfcReal_529 ;
        list:hasNext      inst:IfcLengthMeasure_List_839 .

inst:IfcLengthMeasure_List_839
        list:hasContents  inst:IfcReal_529 .

inst:IfcColumn_1590  rdf:type  ifc:IfcColumn .

inst:IfcGloballyUniqueId_840
        rdf:type           ifc:IfcGloballyUniqueId ;
        express:hasString  "0$iPJRpPTEEPdl0kHj5EAo" .

inst:IfcColumn_1590  ifc:globalId_IfcRoot  inst:IfcGloballyUniqueId_840 ;
        ifc:ownerHistory_IfcRoot  inst:IfcOwnerHistory_13 ;
        ifc:name_IfcRoot          inst:IfcLabel_587 .

inst:IfcGridPlacement_1640
        rdf:type  ifc:IfcGridPlacement .

inst:IfcColumn_1590  ifc:objectPlacement_IfcProduct  inst:IfcGridPlacement_1640 .

inst:IfcProductDefinitionShape_1633
        rdf:type  ifc:IfcProductDefinitionShape .

inst:IfcColumn_1590  ifc:representation_IfcProduct  inst:IfcProductDefinitionShape_1633 .

inst:IfcExtrudedAreaSolid_2615
        rdf:type                        ifc:IfcExtrudedAreaSolid ;
        ifc:sweptArea_IfcSweptAreaSolid  inst:IfcRectangleProfileDef_2611 ;
        ifc:position_IfcSweptAreaSolid  inst:IfcAxis2Placement3D_2062 ;
        ifc:extrudedDirection_IfcExtrudedAreaSolid  inst:IfcDirection_36 ;
        ifc:depth_IfcExtrudedAreaSolid  inst:IfcLengthMeasure_731 .

inst:IfcGridPlacement_569
        ifc:placementLocation_IfcGridPlacement  inst:IfcVirtualGridIntersection_566 .

inst:IfcShapeRepresentation_2618
        rdf:type                     ifc:IfcShapeRepresentation ;
        ifc:contextOfItems_IfcRepresentation  inst:IfcGeometricRepresentationSubContext_53 ;
        ifc:representationIdentifier_IfcRepresentation  inst:IfcLabel_544 ;
        ifc:representationType_IfcRepresentation  inst:IfcLabel_545 ;
        ifc:items_IfcRepresentation  inst:IfcExtrudedAreaSolid_2615 .

inst:IfcRelAssociatesMaterial_572
        rdf:type  ifc:IfcRelAssociatesMaterial .

inst:IfcGloballyUniqueId_841
        rdf:type           ifc:IfcGloballyUniqueId ;
        express:hasString  "3nfekYZyrDse$wWaYIT6nT" .

inst:IfcRelAssociatesMaterial_572
        ifc:globalId_IfcRoot      inst:IfcGloballyUniqueId_841 ;
        ifc:ownerHistory_IfcRoot  inst:IfcOwnerHistory_13 ;
        ifc:relatedObjects_IfcRelAssociates  inst:IfcColumn_519 ;
        ifc:relatingMaterial_IfcRelAssociatesMaterial  inst:IfcMaterial_354 .

inst:IfcRepresentation_List_842
        rdf:type  ifc:IfcRepresentation_List .

inst:IfcProductDefinitionShape_2624
        ifc:representations_IfcProductRepresentation  inst:IfcRepresentation_List_842 .

inst:IfcRepresentation_List_842
        list:hasContents  inst:IfcShapeRepresentation_2618 .

inst:IfcColumn_582  rdf:type  ifc:IfcColumn .

inst:IfcGloballyUniqueId_843
        rdf:type           ifc:IfcGloballyUniqueId ;
        express:hasString  "002c3Y6Or2aOf9mUU7hAvu" .

inst:IfcColumn_582  ifc:globalId_IfcRoot  inst:IfcGloballyUniqueId_843 ;
        ifc:ownerHistory_IfcRoot  inst:IfcOwnerHistory_13 ;
        ifc:name_IfcRoot          inst:IfcLabel_587 .

inst:IfcGridPlacement_632
        rdf:type  ifc:IfcGridPlacement .

inst:IfcColumn_582  ifc:objectPlacement_IfcProduct  inst:IfcGridPlacement_632 .

inst:IfcProductDefinitionShape_625
        rdf:type  ifc:IfcProductDefinitionShape .

inst:IfcColumn_582  ifc:representation_IfcProduct  inst:IfcProductDefinitionShape_625 .

inst:IfcCartesianPoint_2632
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_844
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_2632
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_844 .

inst:IfcLengthMeasure_List_845
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_846
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_844
        list:hasContents  inst:IfcLengthMeasure_556 ;
        list:hasNext      inst:IfcLengthMeasure_List_845 .

inst:IfcLengthMeasure_847
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "14000."^^xsd:double .

inst:IfcLengthMeasure_List_845
        list:hasContents  inst:IfcLengthMeasure_847 ;
        list:hasNext      inst:IfcLengthMeasure_List_846 .

inst:IfcLengthMeasure_List_846
        list:hasContents  inst:IfcLengthMeasure_558 .

inst:IfcAxis2Placement3D_2636
        rdf:type                      ifc:IfcAxis2Placement3D ;
        ifc:location_IfcPlacement     inst:IfcCartesianPoint_2632 ;
        ifc:axis_IfcAxis2Placement3D  inst:IfcDirection_36 ;
        ifc:refDirection_IfcAxis2Placement3D  inst:IfcDirection_2557 .

inst:IfcLocalPlacement_2639
        ifc:placementRelTo_IfcLocalPlacement  inst:IfcLocalPlacement_90 ;
        ifc:relativePlacement_IfcLocalPlacement  inst:IfcAxis2Placement3D_2636 .

inst:IfcAxis2Placement2D_1617
        rdf:type                   ifc:IfcAxis2Placement2D ;
        ifc:location_IfcPlacement  inst:IfcCartesianPoint_1046 ;
        ifc:refDirection_IfcAxis2Placement2D  inst:IfcDirection_2050 .

inst:IfcRelAssociatesMaterial_2642
        rdf:type  ifc:IfcRelAssociatesMaterial .

inst:IfcGloballyUniqueId_848
        rdf:type           ifc:IfcGloballyUniqueId ;
        express:hasString  "1lkTw9iNT77A3UD5oHSTav" .

inst:IfcRelAssociatesMaterial_2642
        ifc:globalId_IfcRoot      inst:IfcGloballyUniqueId_848 ;
        ifc:ownerHistory_IfcRoot  inst:IfcOwnerHistory_13 ;
        ifc:relatedObjects_IfcRelAssociates  inst:IfcBeam_2581 ;
        ifc:relatingMaterial_IfcRelAssociatesMaterial  inst:IfcMaterial_354 .

inst:IfcRectangleProfileDef_1620
        rdf:type                       ifc:IfcRectangleProfileDef ;
        ifc:profileType_IfcProfileDef  ifc:AREA ;
        ifc:profileName_IfcProfileDef  inst:IfcLabel_538 ;
        ifc:position_IfcParameterizedProfileDef  inst:IfcAxis2Placement2D_1617 ;
        ifc:xDim_IfcRectangleProfileDef  inst:IfcPositiveLengthMeasure_552 ;
        ifc:yDim_IfcRectangleProfileDef  inst:IfcPositiveLengthMeasure_552 .

inst:IfcExtrudedAreaSolid_1624
        rdf:type                        ifc:IfcExtrudedAreaSolid ;
        ifc:sweptArea_IfcSweptAreaSolid  inst:IfcRectangleProfileDef_1620 ;
        ifc:position_IfcSweptAreaSolid  inst:IfcAxis2Placement3D_1054 ;
        ifc:extrudedDirection_IfcExtrudedAreaSolid  inst:IfcDirection_36 ;
        ifc:depth_IfcExtrudedAreaSolid  inst:IfcLengthMeasure_558 .

inst:IfcShapeRepresentation_1627
        rdf:type                     ifc:IfcShapeRepresentation ;
        ifc:contextOfItems_IfcRepresentation  inst:IfcGeometricRepresentationSubContext_53 ;
        ifc:representationIdentifier_IfcRepresentation  inst:IfcLabel_544 ;
        ifc:representationType_IfcRepresentation  inst:IfcLabel_545 ;
        ifc:items_IfcRepresentation  inst:IfcExtrudedAreaSolid_1624 .

inst:IfcBeam_2652  rdf:type  ifc:IfcBeam .

inst:IfcGloballyUniqueId_849
        rdf:type           ifc:IfcGloballyUniqueId ;
        express:hasString  "0HgX8eMdT5IAlhYvOpq09J" .

inst:IfcBeam_2652  ifc:globalId_IfcRoot  inst:IfcGloballyUniqueId_849 ;
        ifc:ownerHistory_IfcRoot  inst:IfcOwnerHistory_13 ;
        ifc:name_IfcRoot          inst:IfcLabel_583 .

inst:IfcLocalPlacement_2710
        rdf:type  ifc:IfcLocalPlacement .

inst:IfcBeam_2652  ifc:objectPlacement_IfcProduct  inst:IfcLocalPlacement_2710 .

inst:IfcProductDefinitionShape_2695
        rdf:type  ifc:IfcProductDefinitionShape .

inst:IfcBeam_2652  ifc:representation_IfcProduct  inst:IfcProductDefinitionShape_2695 .

inst:IfcAxis2Placement2D_609
        rdf:type                   ifc:IfcAxis2Placement2D ;
        ifc:location_IfcPlacement  inst:IfcCartesianPoint_1046 ;
        ifc:refDirection_IfcAxis2Placement2D  inst:IfcDirection_2050 .

inst:IfcRepresentation_List_850
        rdf:type  ifc:IfcRepresentation_List .

inst:IfcProductDefinitionShape_1633
        ifc:representations_IfcProductRepresentation  inst:IfcRepresentation_List_850 .

inst:IfcRepresentation_List_850
        list:hasContents  inst:IfcShapeRepresentation_1627 .

inst:IfcRectangleProfileDef_612
        rdf:type                       ifc:IfcRectangleProfileDef ;
        ifc:profileType_IfcProfileDef  ifc:AREA ;
        ifc:profileName_IfcProfileDef  inst:IfcLabel_538 ;
        ifc:position_IfcParameterizedProfileDef  inst:IfcAxis2Placement2D_609 ;
        ifc:xDim_IfcRectangleProfileDef  inst:IfcPositiveLengthMeasure_552 ;
        ifc:yDim_IfcRectangleProfileDef  inst:IfcPositiveLengthMeasure_552 .

inst:IfcVirtualGridIntersection_1637
        rdf:type  ifc:IfcVirtualGridIntersection .

inst:IfcGridAxis_List_851
        rdf:type  ifc:IfcGridAxis_List .

inst:IfcVirtualGridIntersection_1637
        ifc:intersectingAxes_IfcVirtualGridIntersection  inst:IfcGridAxis_List_851 .

inst:IfcGridAxis_List_852
        rdf:type  ifc:IfcGridAxis_List .

inst:IfcGridAxis_List_851
        list:hasContents  inst:IfcGridAxis_217 ;
        list:hasNext      inst:IfcGridAxis_List_852 .

inst:IfcGridAxis_List_852
        list:hasContents  inst:IfcGridAxis_132 .

inst:IfcLengthMeasure_List_853
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcVirtualGridIntersection_1637
        ifc:offsetDistances_IfcVirtualGridIntersection  inst:IfcLengthMeasure_List_853 .

inst:IfcLengthMeasure_List_854
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_855
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_853
        list:hasContents  inst:IfcReal_529 ;
        list:hasNext      inst:IfcLengthMeasure_List_854 .

inst:IfcLengthMeasure_List_854
        list:hasContents  inst:IfcReal_529 ;
        list:hasNext      inst:IfcLengthMeasure_List_855 .

inst:IfcLengthMeasure_List_855
        list:hasContents  inst:IfcReal_529 .

inst:IfcExtrudedAreaSolid_616
        rdf:type                        ifc:IfcExtrudedAreaSolid ;
        ifc:sweptArea_IfcSweptAreaSolid  inst:IfcRectangleProfileDef_612 ;
        ifc:position_IfcSweptAreaSolid  inst:IfcAxis2Placement3D_1054 ;
        ifc:extrudedDirection_IfcExtrudedAreaSolid  inst:IfcDirection_36 ;
        ifc:depth_IfcExtrudedAreaSolid  inst:IfcLengthMeasure_558 .

inst:IfcGridPlacement_1640
        ifc:placementLocation_IfcGridPlacement  inst:IfcVirtualGridIntersection_1637 .

inst:IfcShapeRepresentation_619
        rdf:type                     ifc:IfcShapeRepresentation ;
        ifc:contextOfItems_IfcRepresentation  inst:IfcGeometricRepresentationSubContext_53 ;
        ifc:representationIdentifier_IfcRepresentation  inst:IfcLabel_544 ;
        ifc:representationType_IfcRepresentation  inst:IfcLabel_545 ;
        ifc:items_IfcRepresentation  inst:IfcExtrudedAreaSolid_616 .

inst:IfcRelAssociatesMaterial_1643
        rdf:type  ifc:IfcRelAssociatesMaterial .

inst:IfcGloballyUniqueId_856
        rdf:type           ifc:IfcGloballyUniqueId ;
        express:hasString  "1o9JnTwfr2Evj4BwWdpHfT" .

inst:IfcRelAssociatesMaterial_1643
        ifc:globalId_IfcRoot      inst:IfcGloballyUniqueId_856 ;
        ifc:ownerHistory_IfcRoot  inst:IfcOwnerHistory_13 ;
        ifc:relatedObjects_IfcRelAssociates  inst:IfcColumn_1590 ;
        ifc:relatingMaterial_IfcRelAssociatesMaterial  inst:IfcMaterial_354 .

inst:IfcRepresentation_List_857
        rdf:type  ifc:IfcRepresentation_List .

inst:IfcProductDefinitionShape_625
        ifc:representations_IfcProductRepresentation  inst:IfcRepresentation_List_857 .

inst:IfcRepresentation_List_857
        list:hasContents  inst:IfcShapeRepresentation_619 .

inst:IfcVirtualGridIntersection_629
        rdf:type  ifc:IfcVirtualGridIntersection .

inst:IfcGridAxis_List_858
        rdf:type  ifc:IfcGridAxis_List .

inst:IfcVirtualGridIntersection_629
        ifc:intersectingAxes_IfcVirtualGridIntersection  inst:IfcGridAxis_List_858 .

inst:IfcGridAxis_List_859
        rdf:type  ifc:IfcGridAxis_List .

inst:IfcGridAxis_List_858
        list:hasContents  inst:IfcGridAxis_268 ;
        list:hasNext      inst:IfcGridAxis_List_859 .

inst:IfcGridAxis_List_859
        list:hasContents  inst:IfcGridAxis_149 .

inst:IfcLengthMeasure_List_860
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcVirtualGridIntersection_629
        ifc:offsetDistances_IfcVirtualGridIntersection  inst:IfcLengthMeasure_List_860 .

inst:IfcLengthMeasure_List_861
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_862
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_860
        list:hasContents  inst:IfcReal_529 ;
        list:hasNext      inst:IfcLengthMeasure_List_861 .

inst:IfcLengthMeasure_List_861
        list:hasContents  inst:IfcReal_529 ;
        list:hasNext      inst:IfcLengthMeasure_List_862 .

inst:IfcLengthMeasure_List_862
        list:hasContents  inst:IfcReal_529 .

inst:IfcColumn_1653  rdf:type  ifc:IfcColumn .

inst:IfcGloballyUniqueId_863
        rdf:type           ifc:IfcGloballyUniqueId ;
        express:hasString  "3n$Eued0XDYv5tkLaUxJAx" .

inst:IfcColumn_1653  ifc:globalId_IfcRoot  inst:IfcGloballyUniqueId_863 ;
        ifc:ownerHistory_IfcRoot  inst:IfcOwnerHistory_13 ;
        ifc:name_IfcRoot          inst:IfcLabel_587 .

inst:IfcGridPlacement_1703
        rdf:type  ifc:IfcGridPlacement .

inst:IfcColumn_1653  ifc:objectPlacement_IfcProduct  inst:IfcGridPlacement_1703 .

inst:IfcProductDefinitionShape_1696
        rdf:type  ifc:IfcProductDefinitionShape .

inst:IfcColumn_1653  ifc:representation_IfcProduct  inst:IfcProductDefinitionShape_1696 .

inst:IfcAxis2Placement2D_2679
        rdf:type                   ifc:IfcAxis2Placement2D ;
        ifc:location_IfcPlacement  inst:IfcCartesianPoint_2054 ;
        ifc:refDirection_IfcAxis2Placement2D  inst:IfcDirection_2050 .

inst:IfcGridPlacement_632
        ifc:placementLocation_IfcGridPlacement  inst:IfcVirtualGridIntersection_629 .

inst:IfcRectangleProfileDef_2682
        rdf:type                       ifc:IfcRectangleProfileDef ;
        ifc:profileType_IfcProfileDef  ifc:AREA ;
        ifc:profileName_IfcProfileDef  inst:IfcLabel_538 ;
        ifc:position_IfcParameterizedProfileDef  inst:IfcAxis2Placement2D_2679 ;
        ifc:xDim_IfcRectangleProfileDef  inst:IfcPositiveLengthMeasure_539 ;
        ifc:yDim_IfcRectangleProfileDef  inst:IfcPositiveLengthMeasure_539 .

inst:IfcRelAssociatesMaterial_635
        rdf:type  ifc:IfcRelAssociatesMaterial .

inst:IfcGloballyUniqueId_864
        rdf:type           ifc:IfcGloballyUniqueId ;
        express:hasString  "0snYjDQCP0eQo4c1JRlcY5" .

inst:IfcRelAssociatesMaterial_635
        ifc:globalId_IfcRoot      inst:IfcGloballyUniqueId_864 ;
        ifc:ownerHistory_IfcRoot  inst:IfcOwnerHistory_13 ;
        ifc:relatedObjects_IfcRelAssociates  inst:IfcColumn_582 ;
        ifc:relatingMaterial_IfcRelAssociatesMaterial  inst:IfcMaterial_354 .

inst:IfcExtrudedAreaSolid_2686
        rdf:type                        ifc:IfcExtrudedAreaSolid ;
        ifc:sweptArea_IfcSweptAreaSolid  inst:IfcRectangleProfileDef_2682 ;
        ifc:position_IfcSweptAreaSolid  inst:IfcAxis2Placement3D_2062 ;
        ifc:extrudedDirection_IfcExtrudedAreaSolid  inst:IfcDirection_36 ;
        ifc:depth_IfcExtrudedAreaSolid  inst:IfcLengthMeasure_731 .

inst:IfcShapeRepresentation_2689
        rdf:type                     ifc:IfcShapeRepresentation ;
        ifc:contextOfItems_IfcRepresentation  inst:IfcGeometricRepresentationSubContext_53 ;
        ifc:representationIdentifier_IfcRepresentation  inst:IfcLabel_544 ;
        ifc:representationType_IfcRepresentation  inst:IfcLabel_545 ;
        ifc:items_IfcRepresentation  inst:IfcExtrudedAreaSolid_2686 .

inst:IfcColumn_645  rdf:type  ifc:IfcColumn .

inst:IfcGloballyUniqueId_865
        rdf:type           ifc:IfcGloballyUniqueId ;
        express:hasString  "1y22xNyUvFPB4zIQ9TNnTI" .

inst:IfcColumn_645  ifc:globalId_IfcRoot  inst:IfcGloballyUniqueId_865 ;
        ifc:ownerHistory_IfcRoot  inst:IfcOwnerHistory_13 ;
        ifc:name_IfcRoot          inst:IfcLabel_587 .

inst:IfcGridPlacement_695
        rdf:type  ifc:IfcGridPlacement .

inst:IfcColumn_645  ifc:objectPlacement_IfcProduct  inst:IfcGridPlacement_695 .

inst:IfcProductDefinitionShape_688
        rdf:type  ifc:IfcProductDefinitionShape .

inst:IfcColumn_645  ifc:representation_IfcProduct  inst:IfcProductDefinitionShape_688 .

inst:IfcRepresentation_List_866
        rdf:type  ifc:IfcRepresentation_List .

inst:IfcProductDefinitionShape_2695
        ifc:representations_IfcProductRepresentation  inst:IfcRepresentation_List_866 .

inst:IfcRepresentation_List_866
        list:hasContents  inst:IfcShapeRepresentation_2689 .

inst:IfcCartesianPoint_2703
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcLengthMeasure_List_867
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_2703
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_867 .

inst:IfcLengthMeasure_List_868
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_869
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_867
        list:hasContents  inst:IfcLengthMeasure_556 ;
        list:hasNext      inst:IfcLengthMeasure_List_868 .

inst:IfcLengthMeasure_List_868
        list:hasContents  inst:IfcLengthMeasure_731 ;
        list:hasNext      inst:IfcLengthMeasure_List_869 .

inst:IfcLengthMeasure_List_869
        list:hasContents  inst:IfcLengthMeasure_558 .

inst:IfcAxis2Placement2D_1680
        rdf:type                   ifc:IfcAxis2Placement2D ;
        ifc:location_IfcPlacement  inst:IfcCartesianPoint_1046 ;
        ifc:refDirection_IfcAxis2Placement2D  inst:IfcDirection_2050 .

inst:IfcRectangleProfileDef_1683
        rdf:type                       ifc:IfcRectangleProfileDef ;
        ifc:profileType_IfcProfileDef  ifc:AREA ;
        ifc:profileName_IfcProfileDef  inst:IfcLabel_538 ;
        ifc:position_IfcParameterizedProfileDef  inst:IfcAxis2Placement2D_1680 ;
        ifc:xDim_IfcRectangleProfileDef  inst:IfcPositiveLengthMeasure_552 ;
        ifc:yDim_IfcRectangleProfileDef  inst:IfcPositiveLengthMeasure_552 .

inst:IfcAxis2Placement3D_2707
        rdf:type                      ifc:IfcAxis2Placement3D ;
        ifc:location_IfcPlacement     inst:IfcCartesianPoint_2703 ;
        ifc:axis_IfcAxis2Placement3D  inst:IfcDirection_36 ;
        ifc:refDirection_IfcAxis2Placement3D  inst:IfcDirection_2557 .

inst:IfcLocalPlacement_2710
        ifc:placementRelTo_IfcLocalPlacement  inst:IfcLocalPlacement_90 ;
        ifc:relativePlacement_IfcLocalPlacement  inst:IfcAxis2Placement3D_2707 .

inst:IfcExtrudedAreaSolid_1687
        rdf:type                        ifc:IfcExtrudedAreaSolid ;
        ifc:sweptArea_IfcSweptAreaSolid  inst:IfcRectangleProfileDef_1683 ;
        ifc:position_IfcSweptAreaSolid  inst:IfcAxis2Placement3D_1054 ;
        ifc:extrudedDirection_IfcExtrudedAreaSolid  inst:IfcDirection_36 ;
        ifc:depth_IfcExtrudedAreaSolid  inst:IfcLengthMeasure_558 .

inst:IfcRelAssociatesMaterial_2713
        rdf:type  ifc:IfcRelAssociatesMaterial .

inst:IfcGloballyUniqueId_870
        rdf:type           ifc:IfcGloballyUniqueId ;
        express:hasString  "0e9JZKFZHE1xGhwKv0kdNS" .

inst:IfcRelAssociatesMaterial_2713
        ifc:globalId_IfcRoot      inst:IfcGloballyUniqueId_870 ;
        ifc:ownerHistory_IfcRoot  inst:IfcOwnerHistory_13 ;
        ifc:relatedObjects_IfcRelAssociates  inst:IfcBeam_2652 ;
        ifc:relatingMaterial_IfcRelAssociatesMaterial  inst:IfcMaterial_354 .

inst:IfcShapeRepresentation_1690
        rdf:type                     ifc:IfcShapeRepresentation ;
        ifc:contextOfItems_IfcRepresentation  inst:IfcGeometricRepresentationSubContext_53 ;
        ifc:representationIdentifier_IfcRepresentation  inst:IfcLabel_544 ;
        ifc:representationType_IfcRepresentation  inst:IfcLabel_545 ;
        ifc:items_IfcRepresentation  inst:IfcExtrudedAreaSolid_1687 .

inst:IfcAxis2Placement2D_672
        rdf:type                   ifc:IfcAxis2Placement2D ;
        ifc:location_IfcPlacement  inst:IfcCartesianPoint_1046 ;
        ifc:refDirection_IfcAxis2Placement2D  inst:IfcDirection_2050 .

inst:IfcRepresentation_List_871
        rdf:type  ifc:IfcRepresentation_List .

inst:IfcProductDefinitionShape_1696
        ifc:representations_IfcProductRepresentation  inst:IfcRepresentation_List_871 .

inst:IfcRepresentation_List_871
        list:hasContents  inst:IfcShapeRepresentation_1690 .

inst:IfcRectangleProfileDef_675
        rdf:type                       ifc:IfcRectangleProfileDef ;
        ifc:profileType_IfcProfileDef  ifc:AREA ;
        ifc:profileName_IfcProfileDef  inst:IfcLabel_538 ;
        ifc:position_IfcParameterizedProfileDef  inst:IfcAxis2Placement2D_672 ;
        ifc:xDim_IfcRectangleProfileDef  inst:IfcPositiveLengthMeasure_552 ;
        ifc:yDim_IfcRectangleProfileDef  inst:IfcPositiveLengthMeasure_552 .

inst:IfcRelContainedInSpatialStructure_2723
        rdf:type  ifc:IfcRelContainedInSpatialStructure .

inst:IfcGloballyUniqueId_872
        rdf:type           ifc:IfcGloballyUniqueId ;
        express:hasString  "16oImQoYj9_A1f$3cMD$tG" .

inst:IfcRelContainedInSpatialStructure_2723
        ifc:globalId_IfcRoot      inst:IfcGloballyUniqueId_872 ;
        ifc:ownerHistory_IfcRoot  inst:IfcOwnerHistory_13 .

inst:IfcLabel_873  rdf:type  ifc:IfcLabel ;
        express:hasString  "BuildingStoreyContainer" .

inst:IfcRelContainedInSpatialStructure_2723
        ifc:name_IfcRoot  inst:IfcLabel_873 .

inst:IfcText_874  rdf:type  ifc:IfcText ;
        express:hasString  "BuildingStoreyContainer for Elements" .

inst:IfcRelContainedInSpatialStructure_2723
        ifc:description_IfcRoot  inst:IfcText_874 ;
        ifc:relatedElements_IfcRelContainedInSpatialStructure  inst:IfcGrid_283 ;
        ifc:relatedElements_IfcRelContainedInSpatialStructure  inst:IfcColumn_293 ;
        ifc:relatedElements_IfcRelContainedInSpatialStructure  inst:IfcColumn_519 ;
        ifc:relatedElements_IfcRelContainedInSpatialStructure  inst:IfcColumn_582 ;
        ifc:relatedElements_IfcRelContainedInSpatialStructure  inst:IfcColumn_645 .

inst:IfcColumn_708  rdf:type  ifc:IfcColumn .

inst:IfcRelContainedInSpatialStructure_2723
        ifc:relatedElements_IfcRelContainedInSpatialStructure  inst:IfcColumn_708 .

inst:IfcColumn_771  rdf:type  ifc:IfcColumn .

inst:IfcRelContainedInSpatialStructure_2723
        ifc:relatedElements_IfcRelContainedInSpatialStructure  inst:IfcColumn_771 .

inst:IfcColumn_834  rdf:type  ifc:IfcColumn .

inst:IfcRelContainedInSpatialStructure_2723
        ifc:relatedElements_IfcRelContainedInSpatialStructure  inst:IfcColumn_834 .

inst:IfcColumn_897  rdf:type  ifc:IfcColumn .

inst:IfcRelContainedInSpatialStructure_2723
        ifc:relatedElements_IfcRelContainedInSpatialStructure  inst:IfcColumn_897 .

inst:IfcColumn_960  rdf:type  ifc:IfcColumn .

inst:IfcRelContainedInSpatialStructure_2723
        ifc:relatedElements_IfcRelContainedInSpatialStructure  inst:IfcColumn_960 ;
        ifc:relatedElements_IfcRelContainedInSpatialStructure  inst:IfcColumn_1023 ;
        ifc:relatedElements_IfcRelContainedInSpatialStructure  inst:IfcColumn_1086 ;
        ifc:relatedElements_IfcRelContainedInSpatialStructure  inst:IfcColumn_1149 ;
        ifc:relatedElements_IfcRelContainedInSpatialStructure  inst:IfcColumn_1212 ;
        ifc:relatedElements_IfcRelContainedInSpatialStructure  inst:IfcColumn_1275 ;
        ifc:relatedElements_IfcRelContainedInSpatialStructure  inst:IfcColumn_1338 ;
        ifc:relatedElements_IfcRelContainedInSpatialStructure  inst:IfcColumn_1401 ;
        ifc:relatedElements_IfcRelContainedInSpatialStructure  inst:IfcColumn_1464 ;
        ifc:relatedElements_IfcRelContainedInSpatialStructure  inst:IfcColumn_1527 ;
        ifc:relatedElements_IfcRelContainedInSpatialStructure  inst:IfcColumn_1590 ;
        ifc:relatedElements_IfcRelContainedInSpatialStructure  inst:IfcColumn_1653 .

inst:IfcColumn_1716  rdf:type  ifc:IfcColumn .

inst:IfcRelContainedInSpatialStructure_2723
        ifc:relatedElements_IfcRelContainedInSpatialStructure  inst:IfcColumn_1716 .

inst:IfcColumn_1779  rdf:type  ifc:IfcColumn .

inst:IfcRelContainedInSpatialStructure_2723
        ifc:relatedElements_IfcRelContainedInSpatialStructure  inst:IfcColumn_1779 .

inst:IfcColumn_1842  rdf:type  ifc:IfcColumn .

inst:IfcRelContainedInSpatialStructure_2723
        ifc:relatedElements_IfcRelContainedInSpatialStructure  inst:IfcColumn_1842 .

inst:IfcColumn_1905  rdf:type  ifc:IfcColumn .

inst:IfcRelContainedInSpatialStructure_2723
        ifc:relatedElements_IfcRelContainedInSpatialStructure  inst:IfcColumn_1905 .

inst:IfcColumn_1968  rdf:type  ifc:IfcColumn .

inst:IfcRelContainedInSpatialStructure_2723
        ifc:relatedElements_IfcRelContainedInSpatialStructure  inst:IfcColumn_1968 ;
        ifc:relatedElements_IfcRelContainedInSpatialStructure  inst:IfcBeam_2031 ;
        ifc:relatedElements_IfcRelContainedInSpatialStructure  inst:IfcBeam_2100 ;
        ifc:relatedElements_IfcRelContainedInSpatialStructure  inst:IfcBeam_2167 ;
        ifc:relatedElements_IfcRelContainedInSpatialStructure  inst:IfcBeam_2234 ;
        ifc:relatedElements_IfcRelContainedInSpatialStructure  inst:IfcBeam_2301 ;
        ifc:relatedElements_IfcRelContainedInSpatialStructure  inst:IfcBeam_2372 ;
        ifc:relatedElements_IfcRelContainedInSpatialStructure  inst:IfcBeam_2439 ;
        ifc:relatedElements_IfcRelContainedInSpatialStructure  inst:IfcBeam_2510 ;
        ifc:relatedElements_IfcRelContainedInSpatialStructure  inst:IfcBeam_2581 ;
        ifc:relatedElements_IfcRelContainedInSpatialStructure  inst:IfcBeam_2652 ;
        ifc:relatingStructure_IfcRelContainedInSpatialStructure  inst:IfcBuildingStorey_93 .

inst:IfcVirtualGridIntersection_1700
        rdf:type  ifc:IfcVirtualGridIntersection .

inst:IfcGridAxis_List_875
        rdf:type  ifc:IfcGridAxis_List .

inst:IfcVirtualGridIntersection_1700
        ifc:intersectingAxes_IfcVirtualGridIntersection  inst:IfcGridAxis_List_875 .

inst:IfcGridAxis_List_876
        rdf:type  ifc:IfcGridAxis_List .

inst:IfcGridAxis_List_875
        list:hasContents  inst:IfcGridAxis_217 ;
        list:hasNext      inst:IfcGridAxis_List_876 .

inst:IfcGridAxis_List_876
        list:hasContents  inst:IfcGridAxis_115 .

inst:IfcLengthMeasure_List_877
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcVirtualGridIntersection_1700
        ifc:offsetDistances_IfcVirtualGridIntersection  inst:IfcLengthMeasure_List_877 .

inst:IfcLengthMeasure_List_878
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_879
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_877
        list:hasContents  inst:IfcReal_529 ;
        list:hasNext      inst:IfcLengthMeasure_List_878 .

inst:IfcLengthMeasure_List_878
        list:hasContents  inst:IfcReal_529 ;
        list:hasNext      inst:IfcLengthMeasure_List_879 .

inst:IfcLengthMeasure_List_879
        list:hasContents  inst:IfcReal_529 .

inst:IfcExtrudedAreaSolid_679
        rdf:type                        ifc:IfcExtrudedAreaSolid ;
        ifc:sweptArea_IfcSweptAreaSolid  inst:IfcRectangleProfileDef_675 ;
        ifc:position_IfcSweptAreaSolid  inst:IfcAxis2Placement3D_1054 ;
        ifc:extrudedDirection_IfcExtrudedAreaSolid  inst:IfcDirection_36 ;
        ifc:depth_IfcExtrudedAreaSolid  inst:IfcLengthMeasure_558 .

inst:IfcGridPlacement_1703
        ifc:placementLocation_IfcGridPlacement  inst:IfcVirtualGridIntersection_1700 .

inst:IfcShapeRepresentation_682
        rdf:type                     ifc:IfcShapeRepresentation ;
        ifc:contextOfItems_IfcRepresentation  inst:IfcGeometricRepresentationSubContext_53 ;
        ifc:representationIdentifier_IfcRepresentation  inst:IfcLabel_544 ;
        ifc:representationType_IfcRepresentation  inst:IfcLabel_545 ;
        ifc:items_IfcRepresentation  inst:IfcExtrudedAreaSolid_679 .

inst:IfcRelAssociatesMaterial_1706
        rdf:type  ifc:IfcRelAssociatesMaterial .

inst:IfcGloballyUniqueId_880
        rdf:type           ifc:IfcGloballyUniqueId ;
        express:hasString  "3diy35i5v7ihfG8n$1pm4M" .

inst:IfcRelAssociatesMaterial_1706
        ifc:globalId_IfcRoot      inst:IfcGloballyUniqueId_880 ;
        ifc:ownerHistory_IfcRoot  inst:IfcOwnerHistory_13 ;
        ifc:relatedObjects_IfcRelAssociates  inst:IfcColumn_1653 ;
        ifc:relatingMaterial_IfcRelAssociatesMaterial  inst:IfcMaterial_354 .

inst:IfcRepresentation_List_881
        rdf:type  ifc:IfcRepresentation_List .

inst:IfcProductDefinitionShape_688
        ifc:representations_IfcProductRepresentation  inst:IfcRepresentation_List_881 .

inst:IfcRepresentation_List_881
        list:hasContents  inst:IfcShapeRepresentation_682 .

inst:IfcVirtualGridIntersection_692
        rdf:type  ifc:IfcVirtualGridIntersection .

inst:IfcGridAxis_List_882
        rdf:type  ifc:IfcGridAxis_List .

inst:IfcVirtualGridIntersection_692
        ifc:intersectingAxes_IfcVirtualGridIntersection  inst:IfcGridAxis_List_882 .

inst:IfcGridAxis_List_883
        rdf:type  ifc:IfcGridAxis_List .

inst:IfcGridAxis_List_882
        list:hasContents  inst:IfcGridAxis_268 ;
        list:hasNext      inst:IfcGridAxis_List_883 .

inst:IfcGridAxis_List_883
        list:hasContents  inst:IfcGridAxis_132 .

inst:IfcLengthMeasure_List_884
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcVirtualGridIntersection_692
        ifc:offsetDistances_IfcVirtualGridIntersection  inst:IfcLengthMeasure_List_884 .

inst:IfcLengthMeasure_List_885
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_886
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_884
        list:hasContents  inst:IfcReal_529 ;
        list:hasNext      inst:IfcLengthMeasure_List_885 .

inst:IfcLengthMeasure_List_885
        list:hasContents  inst:IfcReal_529 ;
        list:hasNext      inst:IfcLengthMeasure_List_886 .

inst:IfcLengthMeasure_List_886
        list:hasContents  inst:IfcReal_529 .

inst:IfcGloballyUniqueId_887
        rdf:type           ifc:IfcGloballyUniqueId ;
        express:hasString  "3$2$CWw7T5mPXSiQPW1aw9" .

inst:IfcColumn_1716  ifc:globalId_IfcRoot  inst:IfcGloballyUniqueId_887 ;
        ifc:ownerHistory_IfcRoot  inst:IfcOwnerHistory_13 ;
        ifc:name_IfcRoot          inst:IfcLabel_587 .

inst:IfcGridPlacement_1766
        rdf:type  ifc:IfcGridPlacement .

inst:IfcColumn_1716  ifc:objectPlacement_IfcProduct  inst:IfcGridPlacement_1766 .

inst:IfcProductDefinitionShape_1759
        rdf:type  ifc:IfcProductDefinitionShape .

inst:IfcColumn_1716  ifc:representation_IfcProduct  inst:IfcProductDefinitionShape_1759 .

inst:IfcGridPlacement_695
        ifc:placementLocation_IfcGridPlacement  inst:IfcVirtualGridIntersection_692 .

inst:IfcRelAssociatesMaterial_698
        rdf:type  ifc:IfcRelAssociatesMaterial .

inst:IfcGloballyUniqueId_888
        rdf:type           ifc:IfcGloballyUniqueId ;
        express:hasString  "1ebZRfr2H3thQgmzsIxFDn" .

inst:IfcRelAssociatesMaterial_698
        ifc:globalId_IfcRoot      inst:IfcGloballyUniqueId_888 ;
        ifc:ownerHistory_IfcRoot  inst:IfcOwnerHistory_13 ;
        ifc:relatedObjects_IfcRelAssociates  inst:IfcColumn_645 ;
        ifc:relatingMaterial_IfcRelAssociatesMaterial  inst:IfcMaterial_354 .

inst:IfcGloballyUniqueId_889
        rdf:type           ifc:IfcGloballyUniqueId ;
        express:hasString  "1fu9dbbH5BwwDx0ER7ETo_" .

inst:IfcColumn_708  ifc:globalId_IfcRoot  inst:IfcGloballyUniqueId_889 ;
        ifc:ownerHistory_IfcRoot  inst:IfcOwnerHistory_13 ;
        ifc:name_IfcRoot          inst:IfcLabel_587 .

inst:IfcGridPlacement_758
        rdf:type  ifc:IfcGridPlacement .

inst:IfcColumn_708  ifc:objectPlacement_IfcProduct  inst:IfcGridPlacement_758 .

inst:IfcProductDefinitionShape_751
        rdf:type  ifc:IfcProductDefinitionShape .

inst:IfcColumn_708  ifc:representation_IfcProduct  inst:IfcProductDefinitionShape_751 .

inst:IfcAxis2Placement2D_1743
        rdf:type                   ifc:IfcAxis2Placement2D ;
        ifc:location_IfcPlacement  inst:IfcCartesianPoint_1046 ;
        ifc:refDirection_IfcAxis2Placement2D  inst:IfcDirection_2050 .

inst:IfcRelAggregates_2767
        rdf:type  ifc:IfcRelAggregates .

inst:IfcGloballyUniqueId_890
        rdf:type           ifc:IfcGloballyUniqueId ;
        express:hasString  "0_hC48GjH8oB6PdJvbX9vr" .

inst:IfcRelAggregates_2767
        ifc:globalId_IfcRoot      inst:IfcGloballyUniqueId_890 ;
        ifc:ownerHistory_IfcRoot  inst:IfcOwnerHistory_13 .

inst:IfcLabel_891  rdf:type  ifc:IfcLabel ;
        express:hasString  "BuildingContainer" .

inst:IfcRelAggregates_2767
        ifc:name_IfcRoot  inst:IfcLabel_891 .

inst:IfcText_892  rdf:type  ifc:IfcText ;
        express:hasString  "BuildingContainer for BuildingStories" .

inst:IfcRelAggregates_2767
        ifc:description_IfcRoot  inst:IfcText_892 ;
        ifc:relatingObject_IfcRelAggregates  inst:IfcBuilding_77 ;
        ifc:relatedObjects_IfcRelAggregates  inst:IfcBuildingStorey_93 .

inst:IfcRectangleProfileDef_1746
        rdf:type                       ifc:IfcRectangleProfileDef ;
        ifc:profileType_IfcProfileDef  ifc:AREA ;
        ifc:profileName_IfcProfileDef  inst:IfcLabel_538 ;
        ifc:position_IfcParameterizedProfileDef  inst:IfcAxis2Placement2D_1743 ;
        ifc:xDim_IfcRectangleProfileDef  inst:IfcPositiveLengthMeasure_552 ;
        ifc:yDim_IfcRectangleProfileDef  inst:IfcPositiveLengthMeasure_552 .

inst:IfcRelAggregates_2771
        rdf:type  ifc:IfcRelAggregates .

inst:IfcGloballyUniqueId_893
        rdf:type           ifc:IfcGloballyUniqueId ;
        express:hasString  "2a9WYlGob2OvXZ5E5NGAwK" .

inst:IfcRelAggregates_2771
        ifc:globalId_IfcRoot      inst:IfcGloballyUniqueId_893 ;
        ifc:ownerHistory_IfcRoot  inst:IfcOwnerHistory_13 .

inst:IfcLabel_894  rdf:type  ifc:IfcLabel ;
        express:hasString  "SiteContainer" .

inst:IfcRelAggregates_2771
        ifc:name_IfcRoot  inst:IfcLabel_894 .

inst:IfcText_895  rdf:type  ifc:IfcText ;
        express:hasString  "SiteContainer For Buildings" .

inst:IfcRelAggregates_2771
        ifc:description_IfcRoot  inst:IfcText_895 ;
        ifc:relatingObject_IfcRelAggregates  inst:IfcSite_64 ;
        ifc:relatedObjects_IfcRelAggregates  inst:IfcBuilding_77 .

inst:IfcRelAggregates_2773
        rdf:type  ifc:IfcRelAggregates .

inst:IfcGloballyUniqueId_896
        rdf:type           ifc:IfcGloballyUniqueId ;
        express:hasString  "3RXI8OG4DF5fPc9Gd_kmYm" .

inst:IfcRelAggregates_2773
        ifc:globalId_IfcRoot      inst:IfcGloballyUniqueId_896 ;
        ifc:ownerHistory_IfcRoot  inst:IfcOwnerHistory_13 .

inst:IfcLabel_897  rdf:type  ifc:IfcLabel ;
        express:hasString  "ProjectContainer" .

inst:IfcRelAggregates_2773
        ifc:name_IfcRoot  inst:IfcLabel_897 .

inst:IfcText_898  rdf:type  ifc:IfcText ;
        express:hasString  "ProjectContainer for Sites" .

inst:IfcRelAggregates_2773
        ifc:description_IfcRoot  inst:IfcText_898 ;
        ifc:relatingObject_IfcRelAggregates  inst:IfcProject_54 ;
        ifc:relatedObjects_IfcRelAggregates  inst:IfcSite_64 .

inst:IfcExtrudedAreaSolid_1750
        rdf:type                        ifc:IfcExtrudedAreaSolid ;
        ifc:sweptArea_IfcSweptAreaSolid  inst:IfcRectangleProfileDef_1746 ;
        ifc:position_IfcSweptAreaSolid  inst:IfcAxis2Placement3D_1054 ;
        ifc:extrudedDirection_IfcExtrudedAreaSolid  inst:IfcDirection_36 ;
        ifc:depth_IfcExtrudedAreaSolid  inst:IfcLengthMeasure_558 .

inst:IfcShapeRepresentation_1753
        rdf:type                     ifc:IfcShapeRepresentation ;
        ifc:contextOfItems_IfcRepresentation  inst:IfcGeometricRepresentationSubContext_53 ;
        ifc:representationIdentifier_IfcRepresentation  inst:IfcLabel_544 ;
        ifc:representationType_IfcRepresentation  inst:IfcLabel_545 ;
        ifc:items_IfcRepresentation  inst:IfcExtrudedAreaSolid_1750 .

inst:IfcAxis2Placement2D_735
        rdf:type                   ifc:IfcAxis2Placement2D ;
        ifc:location_IfcPlacement  inst:IfcCartesianPoint_1046 ;
        ifc:refDirection_IfcAxis2Placement2D  inst:IfcDirection_2050 .

inst:IfcRepresentation_List_899
        rdf:type  ifc:IfcRepresentation_List .

inst:IfcProductDefinitionShape_1759
        ifc:representations_IfcProductRepresentation  inst:IfcRepresentation_List_899 .

inst:IfcRepresentation_List_899
        list:hasContents  inst:IfcShapeRepresentation_1753 .

inst:IfcRectangleProfileDef_738
        rdf:type                       ifc:IfcRectangleProfileDef ;
        ifc:profileType_IfcProfileDef  ifc:AREA ;
        ifc:profileName_IfcProfileDef  inst:IfcLabel_538 ;
        ifc:position_IfcParameterizedProfileDef  inst:IfcAxis2Placement2D_735 ;
        ifc:xDim_IfcRectangleProfileDef  inst:IfcPositiveLengthMeasure_552 ;
        ifc:yDim_IfcRectangleProfileDef  inst:IfcPositiveLengthMeasure_552 .

inst:IfcVirtualGridIntersection_1763
        rdf:type  ifc:IfcVirtualGridIntersection .

inst:IfcGridAxis_List_900
        rdf:type  ifc:IfcGridAxis_List .

inst:IfcVirtualGridIntersection_1763
        ifc:intersectingAxes_IfcVirtualGridIntersection  inst:IfcGridAxis_List_900 .

inst:IfcGridAxis_List_901
        rdf:type  ifc:IfcGridAxis_List .

inst:IfcGridAxis_List_900
        list:hasContents  inst:IfcGridAxis_200 ;
        list:hasNext      inst:IfcGridAxis_List_901 .

inst:IfcGridAxis_List_901
        list:hasContents  inst:IfcGridAxis_183 .

inst:IfcLengthMeasure_List_902
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcVirtualGridIntersection_1763
        ifc:offsetDistances_IfcVirtualGridIntersection  inst:IfcLengthMeasure_List_902 .

inst:IfcLengthMeasure_List_903
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_904
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_902
        list:hasContents  inst:IfcReal_529 ;
        list:hasNext      inst:IfcLengthMeasure_List_903 .

inst:IfcLengthMeasure_List_903
        list:hasContents  inst:IfcReal_529 ;
        list:hasNext      inst:IfcLengthMeasure_List_904 .

inst:IfcLengthMeasure_List_904
        list:hasContents  inst:IfcReal_529 .

inst:IfcExtrudedAreaSolid_742
        rdf:type                        ifc:IfcExtrudedAreaSolid ;
        ifc:sweptArea_IfcSweptAreaSolid  inst:IfcRectangleProfileDef_738 ;
        ifc:position_IfcSweptAreaSolid  inst:IfcAxis2Placement3D_1054 ;
        ifc:extrudedDirection_IfcExtrudedAreaSolid  inst:IfcDirection_36 ;
        ifc:depth_IfcExtrudedAreaSolid  inst:IfcLengthMeasure_558 .

inst:IfcGridPlacement_1766
        ifc:placementLocation_IfcGridPlacement  inst:IfcVirtualGridIntersection_1763 .

inst:IfcShapeRepresentation_745
        rdf:type                     ifc:IfcShapeRepresentation ;
        ifc:contextOfItems_IfcRepresentation  inst:IfcGeometricRepresentationSubContext_53 ;
        ifc:representationIdentifier_IfcRepresentation  inst:IfcLabel_544 ;
        ifc:representationType_IfcRepresentation  inst:IfcLabel_545 ;
        ifc:items_IfcRepresentation  inst:IfcExtrudedAreaSolid_742 .

inst:IfcRelAssociatesMaterial_1769
        rdf:type  ifc:IfcRelAssociatesMaterial .

inst:IfcGloballyUniqueId_905
        rdf:type           ifc:IfcGloballyUniqueId ;
        express:hasString  "0AJItPgPX0NuEgejqR7P8f" .

inst:IfcRelAssociatesMaterial_1769
        ifc:globalId_IfcRoot      inst:IfcGloballyUniqueId_905 ;
        ifc:ownerHistory_IfcRoot  inst:IfcOwnerHistory_13 ;
        ifc:relatedObjects_IfcRelAssociates  inst:IfcColumn_1716 ;
        ifc:relatingMaterial_IfcRelAssociatesMaterial  inst:IfcMaterial_354 .

inst:IfcRepresentation_List_906
        rdf:type  ifc:IfcRepresentation_List .

inst:IfcProductDefinitionShape_751
        ifc:representations_IfcProductRepresentation  inst:IfcRepresentation_List_906 .

inst:IfcRepresentation_List_906
        list:hasContents  inst:IfcShapeRepresentation_745 .

inst:IfcVirtualGridIntersection_755
        rdf:type  ifc:IfcVirtualGridIntersection .

inst:IfcGridAxis_List_907
        rdf:type  ifc:IfcGridAxis_List .

inst:IfcVirtualGridIntersection_755
        ifc:intersectingAxes_IfcVirtualGridIntersection  inst:IfcGridAxis_List_907 .

inst:IfcGridAxis_List_908
        rdf:type  ifc:IfcGridAxis_List .

inst:IfcGridAxis_List_907
        list:hasContents  inst:IfcGridAxis_268 ;
        list:hasNext      inst:IfcGridAxis_List_908 .

inst:IfcGridAxis_List_908
        list:hasContents  inst:IfcGridAxis_115 .

inst:IfcLengthMeasure_List_909
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcVirtualGridIntersection_755
        ifc:offsetDistances_IfcVirtualGridIntersection  inst:IfcLengthMeasure_List_909 .

inst:IfcLengthMeasure_List_910
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_911
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_909
        list:hasContents  inst:IfcReal_529 ;
        list:hasNext      inst:IfcLengthMeasure_List_910 .

inst:IfcLengthMeasure_List_910
        list:hasContents  inst:IfcReal_529 ;
        list:hasNext      inst:IfcLengthMeasure_List_911 .

inst:IfcLengthMeasure_List_911
        list:hasContents  inst:IfcReal_529 .

inst:IfcGloballyUniqueId_912
        rdf:type           ifc:IfcGloballyUniqueId ;
        express:hasString  "09ERL8h6nBRBE$$PvUJIuO" .

inst:IfcColumn_1779  ifc:globalId_IfcRoot  inst:IfcGloballyUniqueId_912 ;
        ifc:ownerHistory_IfcRoot  inst:IfcOwnerHistory_13 ;
        ifc:name_IfcRoot          inst:IfcLabel_587 .

inst:IfcGridPlacement_1829
        rdf:type  ifc:IfcGridPlacement .

inst:IfcColumn_1779  ifc:objectPlacement_IfcProduct  inst:IfcGridPlacement_1829 .

inst:IfcProductDefinitionShape_1822
        rdf:type  ifc:IfcProductDefinitionShape .

inst:IfcColumn_1779  ifc:representation_IfcProduct  inst:IfcProductDefinitionShape_1822 .

inst:IfcGridPlacement_758
        ifc:placementLocation_IfcGridPlacement  inst:IfcVirtualGridIntersection_755 .

inst:IfcRelAssociatesMaterial_761
        rdf:type  ifc:IfcRelAssociatesMaterial .

inst:IfcGloballyUniqueId_913
        rdf:type           ifc:IfcGloballyUniqueId ;
        express:hasString  "2b6fbnDeD7FAJbwuuFP7fd" .

inst:IfcRelAssociatesMaterial_761
        ifc:globalId_IfcRoot      inst:IfcGloballyUniqueId_913 ;
        ifc:ownerHistory_IfcRoot  inst:IfcOwnerHistory_13 ;
        ifc:relatedObjects_IfcRelAssociates  inst:IfcColumn_708 ;
        ifc:relatingMaterial_IfcRelAssociatesMaterial  inst:IfcMaterial_354 .

inst:IfcGloballyUniqueId_914
        rdf:type           ifc:IfcGloballyUniqueId ;
        express:hasString  "3rLFU7pKTC0w1RlhdUf_T2" .

inst:IfcColumn_771  ifc:globalId_IfcRoot  inst:IfcGloballyUniqueId_914 ;
        ifc:ownerHistory_IfcRoot  inst:IfcOwnerHistory_13 ;
        ifc:name_IfcRoot          inst:IfcLabel_587 .

inst:IfcGridPlacement_821
        rdf:type  ifc:IfcGridPlacement .

inst:IfcColumn_771  ifc:objectPlacement_IfcProduct  inst:IfcGridPlacement_821 .

inst:IfcProductDefinitionShape_814
        rdf:type  ifc:IfcProductDefinitionShape .

inst:IfcColumn_771  ifc:representation_IfcProduct  inst:IfcProductDefinitionShape_814 .

inst:IfcAxis2Placement2D_1806
        rdf:type                   ifc:IfcAxis2Placement2D ;
        ifc:location_IfcPlacement  inst:IfcCartesianPoint_1046 ;
        ifc:refDirection_IfcAxis2Placement2D  inst:IfcDirection_2050 .

inst:IfcRectangleProfileDef_1809
        rdf:type                       ifc:IfcRectangleProfileDef ;
        ifc:profileType_IfcProfileDef  ifc:AREA ;
        ifc:profileName_IfcProfileDef  inst:IfcLabel_538 ;
        ifc:position_IfcParameterizedProfileDef  inst:IfcAxis2Placement2D_1806 ;
        ifc:xDim_IfcRectangleProfileDef  inst:IfcPositiveLengthMeasure_552 ;
        ifc:yDim_IfcRectangleProfileDef  inst:IfcPositiveLengthMeasure_552 .

inst:IfcExtrudedAreaSolid_1813
        rdf:type                        ifc:IfcExtrudedAreaSolid ;
        ifc:sweptArea_IfcSweptAreaSolid  inst:IfcRectangleProfileDef_1809 ;
        ifc:position_IfcSweptAreaSolid  inst:IfcAxis2Placement3D_1054 ;
        ifc:extrudedDirection_IfcExtrudedAreaSolid  inst:IfcDirection_36 ;
        ifc:depth_IfcExtrudedAreaSolid  inst:IfcLengthMeasure_558 .

inst:IfcShapeRepresentation_1816
        rdf:type                     ifc:IfcShapeRepresentation ;
        ifc:contextOfItems_IfcRepresentation  inst:IfcGeometricRepresentationSubContext_53 ;
        ifc:representationIdentifier_IfcRepresentation  inst:IfcLabel_544 ;
        ifc:representationType_IfcRepresentation  inst:IfcLabel_545 ;
        ifc:items_IfcRepresentation  inst:IfcExtrudedAreaSolid_1813 .

inst:IfcAxis2Placement2D_798
        rdf:type                   ifc:IfcAxis2Placement2D ;
        ifc:location_IfcPlacement  inst:IfcCartesianPoint_1046 ;
        ifc:refDirection_IfcAxis2Placement2D  inst:IfcDirection_2050 .

inst:IfcRepresentation_List_915
        rdf:type  ifc:IfcRepresentation_List .

inst:IfcProductDefinitionShape_1822
        ifc:representations_IfcProductRepresentation  inst:IfcRepresentation_List_915 .

inst:IfcRepresentation_List_915
        list:hasContents  inst:IfcShapeRepresentation_1816 .

inst:IfcRectangleProfileDef_801
        rdf:type                       ifc:IfcRectangleProfileDef ;
        ifc:profileType_IfcProfileDef  ifc:AREA ;
        ifc:profileName_IfcProfileDef  inst:IfcLabel_538 ;
        ifc:position_IfcParameterizedProfileDef  inst:IfcAxis2Placement2D_798 ;
        ifc:xDim_IfcRectangleProfileDef  inst:IfcPositiveLengthMeasure_552 ;
        ifc:yDim_IfcRectangleProfileDef  inst:IfcPositiveLengthMeasure_552 .

inst:IfcVirtualGridIntersection_1826
        rdf:type  ifc:IfcVirtualGridIntersection .

inst:IfcGridAxis_List_916
        rdf:type  ifc:IfcGridAxis_List .

inst:IfcVirtualGridIntersection_1826
        ifc:intersectingAxes_IfcVirtualGridIntersection  inst:IfcGridAxis_List_916 .

inst:IfcGridAxis_List_917
        rdf:type  ifc:IfcGridAxis_List .

inst:IfcGridAxis_List_916
        list:hasContents  inst:IfcGridAxis_200 ;
        list:hasNext      inst:IfcGridAxis_List_917 .

inst:IfcGridAxis_List_917
        list:hasContents  inst:IfcGridAxis_166 .

inst:IfcLengthMeasure_List_918
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcVirtualGridIntersection_1826
        ifc:offsetDistances_IfcVirtualGridIntersection  inst:IfcLengthMeasure_List_918 .

inst:IfcLengthMeasure_List_919
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_920
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_918
        list:hasContents  inst:IfcReal_529 ;
        list:hasNext      inst:IfcLengthMeasure_List_919 .

inst:IfcLengthMeasure_List_919
        list:hasContents  inst:IfcReal_529 ;
        list:hasNext      inst:IfcLengthMeasure_List_920 .

inst:IfcLengthMeasure_List_920
        list:hasContents  inst:IfcReal_529 .

inst:IfcExtrudedAreaSolid_805
        rdf:type                        ifc:IfcExtrudedAreaSolid ;
        ifc:sweptArea_IfcSweptAreaSolid  inst:IfcRectangleProfileDef_801 ;
        ifc:position_IfcSweptAreaSolid  inst:IfcAxis2Placement3D_1054 ;
        ifc:extrudedDirection_IfcExtrudedAreaSolid  inst:IfcDirection_36 ;
        ifc:depth_IfcExtrudedAreaSolid  inst:IfcLengthMeasure_558 .

inst:IfcGridPlacement_1829
        ifc:placementLocation_IfcGridPlacement  inst:IfcVirtualGridIntersection_1826 .

inst:IfcShapeRepresentation_808
        rdf:type                     ifc:IfcShapeRepresentation ;
        ifc:contextOfItems_IfcRepresentation  inst:IfcGeometricRepresentationSubContext_53 ;
        ifc:representationIdentifier_IfcRepresentation  inst:IfcLabel_544 ;
        ifc:representationType_IfcRepresentation  inst:IfcLabel_545 ;
        ifc:items_IfcRepresentation  inst:IfcExtrudedAreaSolid_805 .

inst:IfcRelAssociatesMaterial_1832
        rdf:type  ifc:IfcRelAssociatesMaterial .

inst:IfcGloballyUniqueId_921
        rdf:type           ifc:IfcGloballyUniqueId ;
        express:hasString  "0o7iyvBu15aeZ8UMrdLbAj" .

inst:IfcRelAssociatesMaterial_1832
        ifc:globalId_IfcRoot      inst:IfcGloballyUniqueId_921 ;
        ifc:ownerHistory_IfcRoot  inst:IfcOwnerHistory_13 ;
        ifc:relatedObjects_IfcRelAssociates  inst:IfcColumn_1779 ;
        ifc:relatingMaterial_IfcRelAssociatesMaterial  inst:IfcMaterial_354 .

inst:IfcRepresentation_List_922
        rdf:type  ifc:IfcRepresentation_List .

inst:IfcProductDefinitionShape_814
        ifc:representations_IfcProductRepresentation  inst:IfcRepresentation_List_922 .

inst:IfcRepresentation_List_922
        list:hasContents  inst:IfcShapeRepresentation_808 .

inst:IfcVirtualGridIntersection_818
        rdf:type  ifc:IfcVirtualGridIntersection .

inst:IfcGridAxis_List_923
        rdf:type  ifc:IfcGridAxis_List .

inst:IfcVirtualGridIntersection_818
        ifc:intersectingAxes_IfcVirtualGridIntersection  inst:IfcGridAxis_List_923 .

inst:IfcGridAxis_List_924
        rdf:type  ifc:IfcGridAxis_List .

inst:IfcGridAxis_List_923
        list:hasContents  inst:IfcGridAxis_251 ;
        list:hasNext      inst:IfcGridAxis_List_924 .

inst:IfcGridAxis_List_924
        list:hasContents  inst:IfcGridAxis_183 .

inst:IfcLengthMeasure_List_925
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcVirtualGridIntersection_818
        ifc:offsetDistances_IfcVirtualGridIntersection  inst:IfcLengthMeasure_List_925 .

inst:IfcLengthMeasure_List_926
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_927
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_925
        list:hasContents  inst:IfcReal_529 ;
        list:hasNext      inst:IfcLengthMeasure_List_926 .

inst:IfcLengthMeasure_List_926
        list:hasContents  inst:IfcReal_529 ;
        list:hasNext      inst:IfcLengthMeasure_List_927 .

inst:IfcLengthMeasure_List_927
        list:hasContents  inst:IfcReal_529 .

inst:IfcGloballyUniqueId_928
        rdf:type           ifc:IfcGloballyUniqueId ;
        express:hasString  "1ZW6qv_7PEevosrWrjUrOw" .

inst:IfcColumn_1842  ifc:globalId_IfcRoot  inst:IfcGloballyUniqueId_928 ;
        ifc:ownerHistory_IfcRoot  inst:IfcOwnerHistory_13 ;
        ifc:name_IfcRoot          inst:IfcLabel_587 .

inst:IfcGridPlacement_1892
        rdf:type  ifc:IfcGridPlacement .

inst:IfcColumn_1842  ifc:objectPlacement_IfcProduct  inst:IfcGridPlacement_1892 .

inst:IfcProductDefinitionShape_1885
        rdf:type  ifc:IfcProductDefinitionShape .

inst:IfcColumn_1842  ifc:representation_IfcProduct  inst:IfcProductDefinitionShape_1885 .

inst:IfcGridPlacement_821
        ifc:placementLocation_IfcGridPlacement  inst:IfcVirtualGridIntersection_818 .

inst:IfcRelAssociatesMaterial_824
        rdf:type  ifc:IfcRelAssociatesMaterial .

inst:IfcGloballyUniqueId_929
        rdf:type           ifc:IfcGloballyUniqueId ;
        express:hasString  "1zEryVYB50Y99wJfI_CxFW" .

inst:IfcRelAssociatesMaterial_824
        ifc:globalId_IfcRoot      inst:IfcGloballyUniqueId_929 ;
        ifc:ownerHistory_IfcRoot  inst:IfcOwnerHistory_13 ;
        ifc:relatedObjects_IfcRelAssociates  inst:IfcColumn_771 ;
        ifc:relatingMaterial_IfcRelAssociatesMaterial  inst:IfcMaterial_354 .

inst:IfcGloballyUniqueId_930
        rdf:type           ifc:IfcGloballyUniqueId ;
        express:hasString  "2N_A2Icv9AJvaoWqRYJchR" .

inst:IfcColumn_834  ifc:globalId_IfcRoot  inst:IfcGloballyUniqueId_930 ;
        ifc:ownerHistory_IfcRoot  inst:IfcOwnerHistory_13 ;
        ifc:name_IfcRoot          inst:IfcLabel_587 .

inst:IfcGridPlacement_884
        rdf:type  ifc:IfcGridPlacement .

inst:IfcColumn_834  ifc:objectPlacement_IfcProduct  inst:IfcGridPlacement_884 .

inst:IfcProductDefinitionShape_877
        rdf:type  ifc:IfcProductDefinitionShape .

inst:IfcColumn_834  ifc:representation_IfcProduct  inst:IfcProductDefinitionShape_877 .

inst:IfcAxis2Placement2D_1869
        rdf:type                   ifc:IfcAxis2Placement2D ;
        ifc:location_IfcPlacement  inst:IfcCartesianPoint_1046 ;
        ifc:refDirection_IfcAxis2Placement2D  inst:IfcDirection_2050 .

inst:IfcRectangleProfileDef_1872
        rdf:type                       ifc:IfcRectangleProfileDef ;
        ifc:profileType_IfcProfileDef  ifc:AREA ;
        ifc:profileName_IfcProfileDef  inst:IfcLabel_538 ;
        ifc:position_IfcParameterizedProfileDef  inst:IfcAxis2Placement2D_1869 ;
        ifc:xDim_IfcRectangleProfileDef  inst:IfcPositiveLengthMeasure_552 ;
        ifc:yDim_IfcRectangleProfileDef  inst:IfcPositiveLengthMeasure_552 .

inst:IfcExtrudedAreaSolid_1876
        rdf:type                        ifc:IfcExtrudedAreaSolid ;
        ifc:sweptArea_IfcSweptAreaSolid  inst:IfcRectangleProfileDef_1872 ;
        ifc:position_IfcSweptAreaSolid  inst:IfcAxis2Placement3D_1054 ;
        ifc:extrudedDirection_IfcExtrudedAreaSolid  inst:IfcDirection_36 ;
        ifc:depth_IfcExtrudedAreaSolid  inst:IfcLengthMeasure_558 .

inst:IfcShapeRepresentation_1879
        rdf:type                     ifc:IfcShapeRepresentation ;
        ifc:contextOfItems_IfcRepresentation  inst:IfcGeometricRepresentationSubContext_53 ;
        ifc:representationIdentifier_IfcRepresentation  inst:IfcLabel_544 ;
        ifc:representationType_IfcRepresentation  inst:IfcLabel_545 ;
        ifc:items_IfcRepresentation  inst:IfcExtrudedAreaSolid_1876 .

inst:IfcAxis2Placement2D_861
        rdf:type                   ifc:IfcAxis2Placement2D ;
        ifc:location_IfcPlacement  inst:IfcCartesianPoint_1046 ;
        ifc:refDirection_IfcAxis2Placement2D  inst:IfcDirection_2050 .

inst:IfcRepresentation_List_931
        rdf:type  ifc:IfcRepresentation_List .

inst:IfcProductDefinitionShape_1885
        ifc:representations_IfcProductRepresentation  inst:IfcRepresentation_List_931 .

inst:IfcRepresentation_List_931
        list:hasContents  inst:IfcShapeRepresentation_1879 .

inst:IfcRectangleProfileDef_864
        rdf:type                       ifc:IfcRectangleProfileDef ;
        ifc:profileType_IfcProfileDef  ifc:AREA ;
        ifc:profileName_IfcProfileDef  inst:IfcLabel_538 ;
        ifc:position_IfcParameterizedProfileDef  inst:IfcAxis2Placement2D_861 ;
        ifc:xDim_IfcRectangleProfileDef  inst:IfcPositiveLengthMeasure_552 ;
        ifc:yDim_IfcRectangleProfileDef  inst:IfcPositiveLengthMeasure_552 .

inst:IfcVirtualGridIntersection_1889
        rdf:type  ifc:IfcVirtualGridIntersection .

inst:IfcGridAxis_List_932
        rdf:type  ifc:IfcGridAxis_List .

inst:IfcVirtualGridIntersection_1889
        ifc:intersectingAxes_IfcVirtualGridIntersection  inst:IfcGridAxis_List_932 .

inst:IfcGridAxis_List_933
        rdf:type  ifc:IfcGridAxis_List .

inst:IfcGridAxis_List_932
        list:hasContents  inst:IfcGridAxis_200 ;
        list:hasNext      inst:IfcGridAxis_List_933 .

inst:IfcGridAxis_List_933
        list:hasContents  inst:IfcGridAxis_149 .

inst:IfcLengthMeasure_List_934
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcVirtualGridIntersection_1889
        ifc:offsetDistances_IfcVirtualGridIntersection  inst:IfcLengthMeasure_List_934 .

inst:IfcLengthMeasure_List_935
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_936
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_934
        list:hasContents  inst:IfcReal_529 ;
        list:hasNext      inst:IfcLengthMeasure_List_935 .

inst:IfcLengthMeasure_List_935
        list:hasContents  inst:IfcReal_529 ;
        list:hasNext      inst:IfcLengthMeasure_List_936 .

inst:IfcLengthMeasure_List_936
        list:hasContents  inst:IfcReal_529 .

inst:IfcExtrudedAreaSolid_868
        rdf:type                        ifc:IfcExtrudedAreaSolid ;
        ifc:sweptArea_IfcSweptAreaSolid  inst:IfcRectangleProfileDef_864 ;
        ifc:position_IfcSweptAreaSolid  inst:IfcAxis2Placement3D_1054 ;
        ifc:extrudedDirection_IfcExtrudedAreaSolid  inst:IfcDirection_36 ;
        ifc:depth_IfcExtrudedAreaSolid  inst:IfcLengthMeasure_558 .

inst:IfcGridPlacement_1892
        ifc:placementLocation_IfcGridPlacement  inst:IfcVirtualGridIntersection_1889 .

inst:IfcShapeRepresentation_871
        rdf:type                     ifc:IfcShapeRepresentation ;
        ifc:contextOfItems_IfcRepresentation  inst:IfcGeometricRepresentationSubContext_53 ;
        ifc:representationIdentifier_IfcRepresentation  inst:IfcLabel_544 ;
        ifc:representationType_IfcRepresentation  inst:IfcLabel_545 ;
        ifc:items_IfcRepresentation  inst:IfcExtrudedAreaSolid_868 .

inst:IfcRelAssociatesMaterial_1895
        rdf:type  ifc:IfcRelAssociatesMaterial .

inst:IfcGloballyUniqueId_937
        rdf:type           ifc:IfcGloballyUniqueId ;
        express:hasString  "2g0UVZDhT2OA5mAph2RRwW" .

inst:IfcRelAssociatesMaterial_1895
        ifc:globalId_IfcRoot      inst:IfcGloballyUniqueId_937 ;
        ifc:ownerHistory_IfcRoot  inst:IfcOwnerHistory_13 ;
        ifc:relatedObjects_IfcRelAssociates  inst:IfcColumn_1842 ;
        ifc:relatingMaterial_IfcRelAssociatesMaterial  inst:IfcMaterial_354 .

inst:IfcRepresentation_List_938
        rdf:type  ifc:IfcRepresentation_List .

inst:IfcProductDefinitionShape_877
        ifc:representations_IfcProductRepresentation  inst:IfcRepresentation_List_938 .

inst:IfcRepresentation_List_938
        list:hasContents  inst:IfcShapeRepresentation_871 .

inst:IfcVirtualGridIntersection_881
        rdf:type  ifc:IfcVirtualGridIntersection .

inst:IfcGridAxis_List_939
        rdf:type  ifc:IfcGridAxis_List .

inst:IfcVirtualGridIntersection_881
        ifc:intersectingAxes_IfcVirtualGridIntersection  inst:IfcGridAxis_List_939 .

inst:IfcGridAxis_List_940
        rdf:type  ifc:IfcGridAxis_List .

inst:IfcGridAxis_List_939
        list:hasContents  inst:IfcGridAxis_251 ;
        list:hasNext      inst:IfcGridAxis_List_940 .

inst:IfcGridAxis_List_940
        list:hasContents  inst:IfcGridAxis_166 .

inst:IfcLengthMeasure_List_941
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcVirtualGridIntersection_881
        ifc:offsetDistances_IfcVirtualGridIntersection  inst:IfcLengthMeasure_List_941 .

inst:IfcLengthMeasure_List_942
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_943
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_941
        list:hasContents  inst:IfcReal_529 ;
        list:hasNext      inst:IfcLengthMeasure_List_942 .

inst:IfcLengthMeasure_List_942
        list:hasContents  inst:IfcReal_529 ;
        list:hasNext      inst:IfcLengthMeasure_List_943 .

inst:IfcLengthMeasure_List_943
        list:hasContents  inst:IfcReal_529 .

inst:IfcGloballyUniqueId_944
        rdf:type           ifc:IfcGloballyUniqueId ;
        express:hasString  "2$cnEmlKb7RxirtiLyffW9" .

inst:IfcColumn_1905  ifc:globalId_IfcRoot  inst:IfcGloballyUniqueId_944 ;
        ifc:ownerHistory_IfcRoot  inst:IfcOwnerHistory_13 ;
        ifc:name_IfcRoot          inst:IfcLabel_587 .

inst:IfcGridPlacement_1955
        rdf:type  ifc:IfcGridPlacement .

inst:IfcColumn_1905  ifc:objectPlacement_IfcProduct  inst:IfcGridPlacement_1955 .

inst:IfcProductDefinitionShape_1948
        rdf:type  ifc:IfcProductDefinitionShape .

inst:IfcColumn_1905  ifc:representation_IfcProduct  inst:IfcProductDefinitionShape_1948 .

inst:IfcGridPlacement_884
        ifc:placementLocation_IfcGridPlacement  inst:IfcVirtualGridIntersection_881 .

inst:IfcRelAssociatesMaterial_887
        rdf:type  ifc:IfcRelAssociatesMaterial .

inst:IfcGloballyUniqueId_945
        rdf:type           ifc:IfcGloballyUniqueId ;
        express:hasString  "04YhPfxCb18voKDnykCPKa" .

inst:IfcRelAssociatesMaterial_887
        ifc:globalId_IfcRoot      inst:IfcGloballyUniqueId_945 ;
        ifc:ownerHistory_IfcRoot  inst:IfcOwnerHistory_13 ;
        ifc:relatedObjects_IfcRelAssociates  inst:IfcColumn_834 ;
        ifc:relatingMaterial_IfcRelAssociatesMaterial  inst:IfcMaterial_354 .

inst:IfcGloballyUniqueId_946
        rdf:type           ifc:IfcGloballyUniqueId ;
        express:hasString  "0slTgBVK10Ketv8nvGZqUC" .

inst:IfcColumn_897  ifc:globalId_IfcRoot  inst:IfcGloballyUniqueId_946 ;
        ifc:ownerHistory_IfcRoot  inst:IfcOwnerHistory_13 ;
        ifc:name_IfcRoot          inst:IfcLabel_587 .

inst:IfcGridPlacement_947
        rdf:type  ifc:IfcGridPlacement .

inst:IfcColumn_897  ifc:objectPlacement_IfcProduct  inst:IfcGridPlacement_947 .

inst:IfcProductDefinitionShape_940
        rdf:type  ifc:IfcProductDefinitionShape .

inst:IfcColumn_897  ifc:representation_IfcProduct  inst:IfcProductDefinitionShape_940 .

inst:IfcAxis2Placement2D_1932
        rdf:type                   ifc:IfcAxis2Placement2D ;
        ifc:location_IfcPlacement  inst:IfcCartesianPoint_1046 ;
        ifc:refDirection_IfcAxis2Placement2D  inst:IfcDirection_2050 .

inst:IfcRectangleProfileDef_1935
        rdf:type                       ifc:IfcRectangleProfileDef ;
        ifc:profileType_IfcProfileDef  ifc:AREA ;
        ifc:profileName_IfcProfileDef  inst:IfcLabel_538 ;
        ifc:position_IfcParameterizedProfileDef  inst:IfcAxis2Placement2D_1932 ;
        ifc:xDim_IfcRectangleProfileDef  inst:IfcPositiveLengthMeasure_552 ;
        ifc:yDim_IfcRectangleProfileDef  inst:IfcPositiveLengthMeasure_552 .

inst:IfcExtrudedAreaSolid_1939
        rdf:type                        ifc:IfcExtrudedAreaSolid ;
        ifc:sweptArea_IfcSweptAreaSolid  inst:IfcRectangleProfileDef_1935 ;
        ifc:position_IfcSweptAreaSolid  inst:IfcAxis2Placement3D_1054 ;
        ifc:extrudedDirection_IfcExtrudedAreaSolid  inst:IfcDirection_36 ;
        ifc:depth_IfcExtrudedAreaSolid  inst:IfcLengthMeasure_558 .

inst:IfcShapeRepresentation_1942
        rdf:type                     ifc:IfcShapeRepresentation ;
        ifc:contextOfItems_IfcRepresentation  inst:IfcGeometricRepresentationSubContext_53 ;
        ifc:representationIdentifier_IfcRepresentation  inst:IfcLabel_544 ;
        ifc:representationType_IfcRepresentation  inst:IfcLabel_545 ;
        ifc:items_IfcRepresentation  inst:IfcExtrudedAreaSolid_1939 .

inst:IfcAxis2Placement2D_924
        rdf:type                   ifc:IfcAxis2Placement2D ;
        ifc:location_IfcPlacement  inst:IfcCartesianPoint_1046 ;
        ifc:refDirection_IfcAxis2Placement2D  inst:IfcDirection_2050 .

inst:IfcRepresentation_List_947
        rdf:type  ifc:IfcRepresentation_List .

inst:IfcProductDefinitionShape_1948
        ifc:representations_IfcProductRepresentation  inst:IfcRepresentation_List_947 .

inst:IfcRepresentation_List_947
        list:hasContents  inst:IfcShapeRepresentation_1942 .

inst:IfcRectangleProfileDef_927
        rdf:type                       ifc:IfcRectangleProfileDef ;
        ifc:profileType_IfcProfileDef  ifc:AREA ;
        ifc:profileName_IfcProfileDef  inst:IfcLabel_538 ;
        ifc:position_IfcParameterizedProfileDef  inst:IfcAxis2Placement2D_924 ;
        ifc:xDim_IfcRectangleProfileDef  inst:IfcPositiveLengthMeasure_552 ;
        ifc:yDim_IfcRectangleProfileDef  inst:IfcPositiveLengthMeasure_552 .

inst:IfcVirtualGridIntersection_1952
        rdf:type  ifc:IfcVirtualGridIntersection .

inst:IfcGridAxis_List_948
        rdf:type  ifc:IfcGridAxis_List .

inst:IfcVirtualGridIntersection_1952
        ifc:intersectingAxes_IfcVirtualGridIntersection  inst:IfcGridAxis_List_948 .

inst:IfcGridAxis_List_949
        rdf:type  ifc:IfcGridAxis_List .

inst:IfcGridAxis_List_948
        list:hasContents  inst:IfcGridAxis_200 ;
        list:hasNext      inst:IfcGridAxis_List_949 .

inst:IfcGridAxis_List_949
        list:hasContents  inst:IfcGridAxis_132 .

inst:IfcLengthMeasure_List_950
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcVirtualGridIntersection_1952
        ifc:offsetDistances_IfcVirtualGridIntersection  inst:IfcLengthMeasure_List_950 .

inst:IfcLengthMeasure_List_951
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_952
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_950
        list:hasContents  inst:IfcReal_529 ;
        list:hasNext      inst:IfcLengthMeasure_List_951 .

inst:IfcLengthMeasure_List_951
        list:hasContents  inst:IfcReal_529 ;
        list:hasNext      inst:IfcLengthMeasure_List_952 .

inst:IfcLengthMeasure_List_952
        list:hasContents  inst:IfcReal_529 .

inst:IfcExtrudedAreaSolid_931
        rdf:type                        ifc:IfcExtrudedAreaSolid ;
        ifc:sweptArea_IfcSweptAreaSolid  inst:IfcRectangleProfileDef_927 ;
        ifc:position_IfcSweptAreaSolid  inst:IfcAxis2Placement3D_1054 ;
        ifc:extrudedDirection_IfcExtrudedAreaSolid  inst:IfcDirection_36 ;
        ifc:depth_IfcExtrudedAreaSolid  inst:IfcLengthMeasure_558 .

inst:IfcGridPlacement_1955
        ifc:placementLocation_IfcGridPlacement  inst:IfcVirtualGridIntersection_1952 .

inst:IfcShapeRepresentation_934
        rdf:type                     ifc:IfcShapeRepresentation ;
        ifc:contextOfItems_IfcRepresentation  inst:IfcGeometricRepresentationSubContext_53 ;
        ifc:representationIdentifier_IfcRepresentation  inst:IfcLabel_544 ;
        ifc:representationType_IfcRepresentation  inst:IfcLabel_545 ;
        ifc:items_IfcRepresentation  inst:IfcExtrudedAreaSolid_931 .

inst:IfcRelAssociatesMaterial_1958
        rdf:type  ifc:IfcRelAssociatesMaterial .

inst:IfcGloballyUniqueId_953
        rdf:type           ifc:IfcGloballyUniqueId ;
        express:hasString  "3fel0v29z0yfIc72Hd$FLH" .

inst:IfcRelAssociatesMaterial_1958
        ifc:globalId_IfcRoot      inst:IfcGloballyUniqueId_953 ;
        ifc:ownerHistory_IfcRoot  inst:IfcOwnerHistory_13 ;
        ifc:relatedObjects_IfcRelAssociates  inst:IfcColumn_1905 ;
        ifc:relatingMaterial_IfcRelAssociatesMaterial  inst:IfcMaterial_354 .

inst:IfcRepresentation_List_954
        rdf:type  ifc:IfcRepresentation_List .

inst:IfcProductDefinitionShape_940
        ifc:representations_IfcProductRepresentation  inst:IfcRepresentation_List_954 .

inst:IfcRepresentation_List_954
        list:hasContents  inst:IfcShapeRepresentation_934 .

inst:IfcVirtualGridIntersection_944
        rdf:type  ifc:IfcVirtualGridIntersection .

inst:IfcGridAxis_List_955
        rdf:type  ifc:IfcGridAxis_List .

inst:IfcVirtualGridIntersection_944
        ifc:intersectingAxes_IfcVirtualGridIntersection  inst:IfcGridAxis_List_955 .

inst:IfcGridAxis_List_956
        rdf:type  ifc:IfcGridAxis_List .

inst:IfcGridAxis_List_955
        list:hasContents  inst:IfcGridAxis_251 ;
        list:hasNext      inst:IfcGridAxis_List_956 .

inst:IfcGridAxis_List_956
        list:hasContents  inst:IfcGridAxis_149 .

inst:IfcLengthMeasure_List_957
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcVirtualGridIntersection_944
        ifc:offsetDistances_IfcVirtualGridIntersection  inst:IfcLengthMeasure_List_957 .

inst:IfcLengthMeasure_List_958
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_959
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_957
        list:hasContents  inst:IfcReal_529 ;
        list:hasNext      inst:IfcLengthMeasure_List_958 .

inst:IfcLengthMeasure_List_958
        list:hasContents  inst:IfcReal_529 ;
        list:hasNext      inst:IfcLengthMeasure_List_959 .

inst:IfcLengthMeasure_List_959
        list:hasContents  inst:IfcReal_529 .

inst:IfcGloballyUniqueId_960
        rdf:type           ifc:IfcGloballyUniqueId ;
        express:hasString  "2haSWJcSfEAgcrJRiuVsNA" .

inst:IfcColumn_1968  ifc:globalId_IfcRoot  inst:IfcGloballyUniqueId_960 ;
        ifc:ownerHistory_IfcRoot  inst:IfcOwnerHistory_13 ;
        ifc:name_IfcRoot          inst:IfcLabel_587 .

inst:IfcGridPlacement_2018
        rdf:type  ifc:IfcGridPlacement .

inst:IfcColumn_1968  ifc:objectPlacement_IfcProduct  inst:IfcGridPlacement_2018 .

inst:IfcProductDefinitionShape_2011
        rdf:type  ifc:IfcProductDefinitionShape .

inst:IfcColumn_1968  ifc:representation_IfcProduct  inst:IfcProductDefinitionShape_2011 .

inst:IfcGridPlacement_947
        ifc:placementLocation_IfcGridPlacement  inst:IfcVirtualGridIntersection_944 .

inst:IfcRelAssociatesMaterial_950
        rdf:type  ifc:IfcRelAssociatesMaterial .

inst:IfcGloballyUniqueId_961
        rdf:type           ifc:IfcGloballyUniqueId ;
        express:hasString  "3ADJGROcj1reAZrek3Ih43" .

inst:IfcRelAssociatesMaterial_950
        ifc:globalId_IfcRoot      inst:IfcGloballyUniqueId_961 ;
        ifc:ownerHistory_IfcRoot  inst:IfcOwnerHistory_13 ;
        ifc:relatedObjects_IfcRelAssociates  inst:IfcColumn_897 ;
        ifc:relatingMaterial_IfcRelAssociatesMaterial  inst:IfcMaterial_354 .

inst:IfcGloballyUniqueId_962
        rdf:type           ifc:IfcGloballyUniqueId ;
        express:hasString  "2DpWx6JKf7qRzGJNETj8dw" .

inst:IfcColumn_960  ifc:globalId_IfcRoot  inst:IfcGloballyUniqueId_962 ;
        ifc:ownerHistory_IfcRoot  inst:IfcOwnerHistory_13 ;
        ifc:name_IfcRoot          inst:IfcLabel_587 .

inst:IfcGridPlacement_1010
        rdf:type  ifc:IfcGridPlacement .

inst:IfcColumn_960  ifc:objectPlacement_IfcProduct  inst:IfcGridPlacement_1010 .

inst:IfcProductDefinitionShape_1003
        rdf:type  ifc:IfcProductDefinitionShape .

inst:IfcColumn_960  ifc:representation_IfcProduct  inst:IfcProductDefinitionShape_1003 .

inst:IfcAxis2Placement2D_1995
        rdf:type                   ifc:IfcAxis2Placement2D ;
        ifc:location_IfcPlacement  inst:IfcCartesianPoint_1046 ;
        ifc:refDirection_IfcAxis2Placement2D  inst:IfcDirection_2050 .

inst:IfcRectangleProfileDef_1998
        rdf:type                       ifc:IfcRectangleProfileDef ;
        ifc:profileType_IfcProfileDef  ifc:AREA ;
        ifc:profileName_IfcProfileDef  inst:IfcLabel_538 ;
        ifc:position_IfcParameterizedProfileDef  inst:IfcAxis2Placement2D_1995 ;
        ifc:xDim_IfcRectangleProfileDef  inst:IfcPositiveLengthMeasure_552 ;
        ifc:yDim_IfcRectangleProfileDef  inst:IfcPositiveLengthMeasure_552 .

inst:IfcExtrudedAreaSolid_2002
        rdf:type                        ifc:IfcExtrudedAreaSolid ;
        ifc:sweptArea_IfcSweptAreaSolid  inst:IfcRectangleProfileDef_1998 ;
        ifc:position_IfcSweptAreaSolid  inst:IfcAxis2Placement3D_1054 ;
        ifc:extrudedDirection_IfcExtrudedAreaSolid  inst:IfcDirection_36 ;
        ifc:depth_IfcExtrudedAreaSolid  inst:IfcLengthMeasure_558 .

inst:IfcShapeRepresentation_2005
        rdf:type                     ifc:IfcShapeRepresentation ;
        ifc:contextOfItems_IfcRepresentation  inst:IfcGeometricRepresentationSubContext_53 ;
        ifc:representationIdentifier_IfcRepresentation  inst:IfcLabel_544 ;
        ifc:representationType_IfcRepresentation  inst:IfcLabel_545 ;
        ifc:items_IfcRepresentation  inst:IfcExtrudedAreaSolid_2002 .

inst:IfcAxis2Placement2D_987
        rdf:type                   ifc:IfcAxis2Placement2D ;
        ifc:location_IfcPlacement  inst:IfcCartesianPoint_1046 ;
        ifc:refDirection_IfcAxis2Placement2D  inst:IfcDirection_2050 .

inst:IfcRepresentation_List_963
        rdf:type  ifc:IfcRepresentation_List .

inst:IfcProductDefinitionShape_2011
        ifc:representations_IfcProductRepresentation  inst:IfcRepresentation_List_963 .

inst:IfcRepresentation_List_963
        list:hasContents  inst:IfcShapeRepresentation_2005 .

inst:IfcRectangleProfileDef_990
        rdf:type                       ifc:IfcRectangleProfileDef ;
        ifc:profileType_IfcProfileDef  ifc:AREA ;
        ifc:profileName_IfcProfileDef  inst:IfcLabel_538 ;
        ifc:position_IfcParameterizedProfileDef  inst:IfcAxis2Placement2D_987 ;
        ifc:xDim_IfcRectangleProfileDef  inst:IfcPositiveLengthMeasure_552 ;
        ifc:yDim_IfcRectangleProfileDef  inst:IfcPositiveLengthMeasure_552 .

inst:IfcVirtualGridIntersection_2015
        rdf:type  ifc:IfcVirtualGridIntersection .

inst:IfcGridAxis_List_964
        rdf:type  ifc:IfcGridAxis_List .

inst:IfcVirtualGridIntersection_2015
        ifc:intersectingAxes_IfcVirtualGridIntersection  inst:IfcGridAxis_List_964 .

inst:IfcGridAxis_List_965
        rdf:type  ifc:IfcGridAxis_List .

inst:IfcGridAxis_List_964
        list:hasContents  inst:IfcGridAxis_200 ;
        list:hasNext      inst:IfcGridAxis_List_965 .

inst:IfcGridAxis_List_965
        list:hasContents  inst:IfcGridAxis_115 .

inst:IfcLengthMeasure_List_966
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcVirtualGridIntersection_2015
        ifc:offsetDistances_IfcVirtualGridIntersection  inst:IfcLengthMeasure_List_966 .

inst:IfcLengthMeasure_List_967
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_968
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_966
        list:hasContents  inst:IfcReal_529 ;
        list:hasNext      inst:IfcLengthMeasure_List_967 .

inst:IfcLengthMeasure_List_967
        list:hasContents  inst:IfcReal_529 ;
        list:hasNext      inst:IfcLengthMeasure_List_968 .

inst:IfcLengthMeasure_List_968
        list:hasContents  inst:IfcReal_529 .

inst:IfcExtrudedAreaSolid_994
        rdf:type                        ifc:IfcExtrudedAreaSolid ;
        ifc:sweptArea_IfcSweptAreaSolid  inst:IfcRectangleProfileDef_990 ;
        ifc:position_IfcSweptAreaSolid  inst:IfcAxis2Placement3D_1054 ;
        ifc:extrudedDirection_IfcExtrudedAreaSolid  inst:IfcDirection_36 ;
        ifc:depth_IfcExtrudedAreaSolid  inst:IfcLengthMeasure_558 .

inst:IfcGridPlacement_2018
        ifc:placementLocation_IfcGridPlacement  inst:IfcVirtualGridIntersection_2015 .

inst:IfcShapeRepresentation_997
        rdf:type                     ifc:IfcShapeRepresentation ;
        ifc:contextOfItems_IfcRepresentation  inst:IfcGeometricRepresentationSubContext_53 ;
        ifc:representationIdentifier_IfcRepresentation  inst:IfcLabel_544 ;
        ifc:representationType_IfcRepresentation  inst:IfcLabel_545 ;
        ifc:items_IfcRepresentation  inst:IfcExtrudedAreaSolid_994 .

inst:IfcRelAssociatesMaterial_2021
        rdf:type  ifc:IfcRelAssociatesMaterial .

inst:IfcGloballyUniqueId_969
        rdf:type           ifc:IfcGloballyUniqueId ;
        express:hasString  "2mtSsiV6DBBBqw1_FHq9yw" .

inst:IfcRelAssociatesMaterial_2021
        ifc:globalId_IfcRoot      inst:IfcGloballyUniqueId_969 ;
        ifc:ownerHistory_IfcRoot  inst:IfcOwnerHistory_13 ;
        ifc:relatedObjects_IfcRelAssociates  inst:IfcColumn_1968 ;
        ifc:relatingMaterial_IfcRelAssociatesMaterial  inst:IfcMaterial_354 .

inst:IfcRepresentation_List_970
        rdf:type  ifc:IfcRepresentation_List .

inst:IfcProductDefinitionShape_1003
        ifc:representations_IfcProductRepresentation  inst:IfcRepresentation_List_970 .

inst:IfcRepresentation_List_970
        list:hasContents  inst:IfcShapeRepresentation_997 .

inst:IfcVirtualGridIntersection_1007
        rdf:type  ifc:IfcVirtualGridIntersection .

inst:IfcGridAxis_List_971
        rdf:type  ifc:IfcGridAxis_List .

inst:IfcVirtualGridIntersection_1007
        ifc:intersectingAxes_IfcVirtualGridIntersection  inst:IfcGridAxis_List_971 .

inst:IfcGridAxis_List_972
        rdf:type  ifc:IfcGridAxis_List .

inst:IfcGridAxis_List_971
        list:hasContents  inst:IfcGridAxis_251 ;
        list:hasNext      inst:IfcGridAxis_List_972 .

inst:IfcGridAxis_List_972
        list:hasContents  inst:IfcGridAxis_132 .

inst:IfcLengthMeasure_List_973
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcVirtualGridIntersection_1007
        ifc:offsetDistances_IfcVirtualGridIntersection  inst:IfcLengthMeasure_List_973 .

inst:IfcLengthMeasure_List_974
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_975
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_973
        list:hasContents  inst:IfcReal_529 ;
        list:hasNext      inst:IfcLengthMeasure_List_974 .

inst:IfcLengthMeasure_List_974
        list:hasContents  inst:IfcReal_529 ;
        list:hasNext      inst:IfcLengthMeasure_List_975 .

inst:IfcLengthMeasure_List_975
        list:hasContents  inst:IfcReal_529 .

inst:IfcGloballyUniqueId_976
        rdf:type           ifc:IfcGloballyUniqueId ;
        express:hasString  "0sj55LkZn33RYYV9cNp2vs" .

inst:IfcBeam_2031  ifc:globalId_IfcRoot  inst:IfcGloballyUniqueId_976 ;
        ifc:ownerHistory_IfcRoot        inst:IfcOwnerHistory_13 ;
        ifc:name_IfcRoot                inst:IfcLabel_583 ;
        ifc:objectPlacement_IfcProduct  inst:IfcLocalPlacement_2085 ;
        ifc:representation_IfcProduct   inst:IfcProductDefinitionShape_2074 .

inst:IfcGridPlacement_1010
        ifc:placementLocation_IfcGridPlacement  inst:IfcVirtualGridIntersection_1007 .

inst:IfcRelAssociatesMaterial_1013
        rdf:type  ifc:IfcRelAssociatesMaterial .

inst:IfcGloballyUniqueId_977
        rdf:type           ifc:IfcGloballyUniqueId ;
        express:hasString  "1ej_tV7eL8vuX6xJj3Xoxn" .

inst:IfcRelAssociatesMaterial_1013
        ifc:globalId_IfcRoot      inst:IfcGloballyUniqueId_977 ;
        ifc:ownerHistory_IfcRoot  inst:IfcOwnerHistory_13 ;
        ifc:relatedObjects_IfcRelAssociates  inst:IfcColumn_960 ;
        ifc:relatingMaterial_IfcRelAssociatesMaterial  inst:IfcMaterial_354 .

inst:IfcGloballyUniqueId_978
        rdf:type           ifc:IfcGloballyUniqueId ;
        express:hasString  "3bJspXGYDEoQwjnHqkbpKe" .

inst:IfcColumn_1023  ifc:globalId_IfcRoot  inst:IfcGloballyUniqueId_978 ;
        ifc:ownerHistory_IfcRoot        inst:IfcOwnerHistory_13 ;
        ifc:name_IfcRoot                inst:IfcLabel_587 ;
        ifc:objectPlacement_IfcProduct  inst:IfcGridPlacement_1073 ;
        ifc:representation_IfcProduct   inst:IfcProductDefinitionShape_1066 .

ISO-10303-21;
HEADER;
FILE_DESCRIPTION(('ViewDefinition [CoordinationView]'),'2;1');
FILE_NAME('','2020-10-30T00:18:57',(),(),'IfcOpenShell 0.6.0b0','IfcOpenShell 0.6.0b0','');
FILE_SCHEMA(('IFC4'));
ENDSEC;
DATA;
#1=IFCPROJECT('2btxazyiP8WAIfB0dw8meA',#77,'Default Project',$,$,$,$,(#78),#120);
#2=IFCSITE('0AU$p8piP1M9KEzK5w32XM',#77,'Default Site',$,$,#132,$,$,.ELEMENT.,(24,28,0),(54,25,0),$,$,$);
#3=IFCBUILDING('0IuzxQ0mr3BOdtlAS8ZLt_',#77,'Default Building',$,$,#137,$,$,.ELEMENT.,$,$,$);
#4=IFCBUILDINGSTOREY('3AG_0oB1zBfhm3BFIxGOjF',#77,'Ground Floor',$,$,#147,$,$,.ELEMENT.,0.);
#5=IFCGRID('0fuUMCx0jFggWzjspLeC2b',#77,$,$,$,#162,#182,(#183,#187,#191,#195,#199),(#203,#207,#211,#215,#219),$,$);
#6=IFCCOLUMN('2E6Q5P3bD23h5JOtEANY6k',#77,'CRE - 001',$,$,#223,#233,$,$);
#7=IFCCOLUMN('3SVzKRDSn40RvQ9U19U6kS',#77,'CRE - 001',$,$,#234,#244,$,$);
#8=IFCCOLUMN('002c3Y6Or2aOf9mUU7hAvu',#77,'CRE - 001',$,$,#245,#255,$,$);
#9=IFCCOLUMN('1y22xNyUvFPB4zIQ9TNnTI',#77,'CRE - 001',$,$,#256,#266,$,$);
#10=IFCCOLUMN('1fu9dbbH5BwwDx0ER7ETo_',#77,'CRE - 001',$,$,#267,#277,$,$);
#11=IFCCOLUMN('3rLFU7pKTC0w1RlhdUf_T2',#77,'CRE - 001',$,$,#278,#288,$,$);
#12=IFCCOLUMN('2N_A2Icv9AJvaoWqRYJchR',#77,'CRE - 001',$,$,#289,#299,$,$);
#13=IFCCOLUMN('0slTgBVK10Ketv8nvGZqUC',#77,'CRE - 001',$,$,#300,#310,$,$);
#14=IFCCOLUMN('2DpWx6JKf7qRzGJNETj8dw',#77,'CRE - 001',$,$,#311,#321,$,$);
#15=IFCCOLUMN('3bJspXGYDEoQwjnHqkbpKe',#77,'CRE - 001',$,$,#322,#332,$,$);
#16=IFCCOLUMN('3sNRA7UHP0x9bDzO0SwHVt',#77,'CRE - 001',$,$,#333,#343,$,$);
#17=IFCCOLUMN('1vABltQ550OwXnYj4H1WTw',#77,'CRE - 001',$,$,#344,#354,$,$);
#18=IFCCOLUMN('3kEtX5Q8T598aWzoK0Y3VK',#77,'CRE - 001',$,$,#355,#365,$,$);
#19=IFCCOLUMN('23XrrMv196EgYSv3SrHeh9',#77,'CRE - 001',$,$,#366,#376,$,$);
#20=IFCCOLUMN('0eJs1Ppf94_RzyPXHgJ3IY',#77,'CRE - 001',$,$,#377,#387,$,$);
#21=IFCCOLUMN('1oPiECsTf5rBXznvK9MdP_',#77,'CRE - 001',$,$,#388,#398,$,$);
#22=IFCCOLUMN('1W7_sZW7157AYdrmDlC7VM',#77,'CRE - 001',$,$,#399,#409,$,$);
#23=IFCCOLUMN('1FN9fX7IT6q8a1vB_hVsGg',#77,'CRE - 001',$,$,#410,#420,$,$);
#24=IFCCOLUMN('0$iPJRpPTEEPdl0kHj5EAo',#77,'CRE - 001',$,$,#421,#431,$,$);
#25=IFCCOLUMN('3n$Eued0XDYv5tkLaUxJAx',#77,'CRE - 001',$,$,#432,#442,$,$);
#26=IFCCOLUMN('3$2$CWw7T5mPXSiQPW1aw9',#77,'CRE - 001',$,$,#443,#453,$,$);
#27=IFCCOLUMN('09ERL8h6nBRBE$$PvUJIuO',#77,'CRE - 001',$,$,#454,#464,$,$);
#28=IFCCOLUMN('1ZW6qv_7PEevosrWrjUrOw',#77,'CRE - 001',$,$,#465,#475,$,$);
#29=IFCCOLUMN('2$cnEmlKb7RxirtiLyffW9',#77,'CRE - 001',$,$,#476,#486,$,$);
#30=IFCCOLUMN('2haSWJcSfEAgcrJRiuVsNA',#77,'CRE - 001',$,$,#487,#497,$,$);
#31=IFCBEAM('0sj55LkZn33RYYV9cNp2vs',#77,'BMR - 001',$,$,#498,#518,$,$);
#32=IFCBEAM('3R6gApVrPDBBjeleun$ig8',#77,'BMR - 001',$,$,#519,#539,$,$);
#33=IFCBEAM('0QMS1qce10WvjrKIO5PpTf',#77,'BMR - 001',$,$,#540,#560,$,$);
#34=IFCBEAM('2Ib4Kw7NbENgEHiTJh_Leu',#77,'BMR - 001',$,$,#561,#581,$,$);
#35=IFCBEAM('3Hx5aT6pD1iPGYA3CCHPRS',#77,'BMR - 001',$,$,#582,#602,$,$);
#36=IFCBEAM('1Eouahjef86QHNj1gS81gC',#77,'BMR - 001',$,$,#603,#623,$,$);
#37=IFCBEAM('2inLgxVS94Te6b5ADXjH94',#77,'BMR - 001',$,$,#624,#644,$,$);
#38=IFCBEAM('1v5rDYwSnB7gVTP_r2LzCU',#77,'BMR - 001',$,$,#645,#665,$,$);
#39=IFCBEAM('0z$eamUiL5uvdZJOfKdMkc',#77,'BMR - 001',$,$,#666,#686,$,$);
#40=IFCBEAM('0HgX8eMdT5IAlhYvOpq09J',#77,'BMR - 001',$,$,#687,#707,$,$);
#41=IFCSHAPEREPRESENTATION(#79,'FootPrint','GeometricCurveSet',(#708));
#42=IFCSHAPEREPRESENTATION(#80,'Body','SweptSolid',(#739));
#43=IFCSHAPEREPRESENTATION(#80,'Body','SweptSolid',(#749));
#44=IFCSHAPEREPRESENTATION(#80,'Body','SweptSolid',(#759));
#45=IFCSHAPEREPRESENTATION(#80,'Body','SweptSolid',(#769));
#46=IFCSHAPEREPRESENTATION(#80,'Body','SweptSolid',(#779));
#47=IFCSHAPEREPRESENTATION(#80,'Body','SweptSolid',(#789));
#48=IFCSHAPEREPRESENTATION(#80,'Body','SweptSolid',(#799));
#49=IFCSHAPEREPRESENTATION(#80,'Body','SweptSolid',(#809));
#50=IFCSHAPEREPRESENTATION(#80,'Body','SweptSolid',(#819));
#51=IFCSHAPEREPRESENTATION(#80,'Body','SweptSolid',(#829));
#52=IFCSHAPEREPRESENTATION(#80,'Body','SweptSolid',(#839));
#53=IFCSHAPEREPRESENTATION(#80,'Body','SweptSolid',(#849));
#54=IFCSHAPEREPRESENTATION(#80,'Body','SweptSolid',(#859));
#55=IFCSHAPEREPRESENTATION(#80,'Body','SweptSolid',(#869));
#56=IFCSHAPEREPRESENTATION(#80,'Body','SweptSolid',(#879));
#57=IFCSHAPEREPRESENTATION(#80,'Body','SweptSolid',(#889));
#58=IFCSHAPEREPRESENTATION(#80,'Body','SweptSolid',(#899));
#59=IFCSHAPEREPRESENTATION(#80,'Body','SweptSolid',(#909));
#60=IFCSHAPEREPRESENTATION(#80,'Body','SweptSolid',(#919));
#61=IFCSHAPEREPRESENTATION(#80,'Body','SweptSolid',(#929));
#62=IFCSHAPEREPRESENTATION(#80,'Body','SweptSolid',(#939));
#63=IFCSHAPEREPRESENTATION(#80,'Body','SweptSolid',(#949));
#64=IFCSHAPEREPRESENTATION(#80,'Body','SweptSolid',(#959));
#65=IFCSHAPEREPRESENTATION(#80,'Body','SweptSolid',(#969));
#66=IFCSHAPEREPRESENTATION(#80,'Body','SweptSolid',(#979));
#67=IFCSHAPEREPRESENTATION(#80,'Body','SweptSolid',(#989));
#68=IFCSHAPEREPRESENTATION(#80,'Body','SweptSolid',(#999));
#69=IFCSHAPEREPRESENTATION(#80,'Body','SweptSolid',(#1009));
#70=IFCSHAPEREPRESENTATION(#80,'Body','SweptSolid',(#1019));
#71=IFCSHAPEREPRESENTATION(#80,'Body','SweptSolid',(#1029));
#72=IFCSHAPEREPRESENTATION(#80,'Body','SweptSolid',(#1039));
#73=IFCSHAPEREPRESENTATION(#80,'Body','SweptSolid',(#1049));
#74=IFCSHAPEREPRESENTATION(#80,'Body','SweptSolid',(#1059));
#75=IFCSHAPEREPRESENTATION(#80,'Body','SweptSolid',(#1069));
#76=IFCSHAPEREPRESENTATION(#80,'Body','SweptSolid',(#1079));
#77=IFCOWNERHISTORY(#1089,#1092,$,.NOTDEFINED.,$,$,$,1247473054);
#78=IFCGEOMETRICREPRESENTATIONCONTEXT($,'Model',3,1.E-05,#1094,#1098);
#79=IFCGEOMETRICREPRESENTATIONSUBCONTEXT('FootPrint','Model',*,*,*,*,#78,$,.MODEL_VIEW.,$);
#80=IFCGEOMETRICREPRESENTATIONSUBCONTEXT('Body','Model',*,*,*,*,#78,$,.MODEL_VIEW.,$);
#81=IFCRELASSOCIATESMATERIAL('2ATJkq8SP8Nwm_CddbSUNm',#77,$,$,(#6),#1099);
#82=IFCRELASSOCIATESMATERIAL('3nfekYZyrDse$wWaYIT6nT',#77,$,$,(#7),#1100);
#83=IFCRELASSOCIATESMATERIAL('0snYjDQCP0eQo4c1JRlcY5',#77,$,$,(#8),#1101);
#84=IFCRELASSOCIATESMATERIAL('1ebZRfr2H3thQgmzsIxFDn',#77,$,$,(#9),#1102);
#85=IFCRELASSOCIATESMATERIAL('2b6fbnDeD7FAJbwuuFP7fd',#77,$,$,(#10),#1103);
#86=IFCRELASSOCIATESMATERIAL('1zEryVYB50Y99wJfI_CxFW',#77,$,$,(#11),#1104);
#87=IFCRELASSOCIATESMATERIAL('04YhPfxCb18voKDnykCPKa',#77,$,$,(#12),#1105);
#88=IFCRELASSOCIATESMATERIAL('3ADJGROcj1reAZrek3Ih43',#77,$,$,(#13),#1106);
#89=IFCRELASSOCIATESMATERIAL('1ej_tV7eL8vuX6xJj3Xoxn',#77,$,$,(#14),#1107);
#90=IFCRELASSOCIATESMATERIAL('0L2mkv1m9AjAgDU16VxxRY',#77,$,$,(#15),#1108);
#91=IFCRELASSOCIATESMATERIAL('2DFTpVzLb0GOz94dnHBIIq',#77,$,$,(#16),#1109);
#92=IFCRELASSOCIATESMATERIAL('2pkwqk6HjC78QH3P0jN49W',#77,$,$,(#17),#1110);
#93=IFCRELASSOCIATESMATERIAL('0jQTVYfXPFTe1hDM_MkTM2',#77,$,$,(#18),#1111);
#94=IFCRELASSOCIATESMATERIAL('1LPl3a4iPBlOYvfIxAwQj2',#77,$,$,(#19),#1112);
#95=IFCRELASSOCIATESMATERIAL('3sjfDIOc9F1ec0f1lD90H2',#77,$,$,(#20),#1113);
#96=IFCRELASSOCIATESMATERIAL('1nQn_EHJ1CAgQ3A_5kVjgu',#77,$,$,(#21),#1114);
#97=IFCRELASSOCIATESMATERIAL('3hdVqzwW58b8CHoK91M_e3',#77,$,$,(#22),#1115);
#98=IFCRELASSOCIATESMATERIAL('2qMelZARf5XeVVPFD8KNr_',#77,$,$,(#23),#1116);
#99=IFCRELASSOCIATESMATERIAL('1o9JnTwfr2Evj4BwWdpHfT',#77,$,$,(#24),#1117);
#100=IFCRELASSOCIATESMATERIAL('3diy35i5v7ihfG8n$1pm4M',#77,$,$,(#25),#1118);
#101=IFCRELASSOCIATESMATERIAL('0AJItPgPX0NuEgejqR7P8f',#77,$,$,(#26),#1119);
#102=IFCRELASSOCIATESMATERIAL('0o7iyvBu15aeZ8UMrdLbAj',#77,$,$,(#27),#1120);
#103=IFCRELASSOCIATESMATERIAL('2g0UVZDhT2OA5mAph2RRwW',#77,$,$,(#28),#1121);
#104=IFCRELASSOCIATESMATERIAL('3fel0v29z0yfIc72Hd$FLH',#77,$,$,(#29),#1122);
#105=IFCRELASSOCIATESMATERIAL('2mtSsiV6DBBBqw1_FHq9yw',#77,$,$,(#30),#1123);
#106=IFCRELASSOCIATESMATERIAL('395DGAbFz7IRhGOTHDhBEA',#77,$,$,(#31),#1124);
#107=IFCRELASSOCIATESMATERIAL('0bQ8chmun9UOk$Z_NNlmg_',#77,$,$,(#32),#1125);
#108=IFCRELASSOCIATESMATERIAL('2gdBfewsLA5Q6MxOyuBaJS',#77,$,$,(#33),#1126);
#109=IFCRELASSOCIATESMATERIAL('1BKlPhJQj4buntvqU28dfk',#77,$,$,(#34),#1127);
#110=IFCRELASSOCIATESMATERIAL('3aQPpPgfX3hR4JMj79gyHg',#77,$,$,(#35),#1128);
#111=IFCRELASSOCIATESMATERIAL('12hCI2$Af87h_yq7FqKlS_',#77,$,$,(#36),#1129);
#112=IFCRELASSOCIATESMATERIAL('3SSTTenEP0ZxzQoK27AEY3',#77,$,$,(#37),#1130);
#113=IFCRELASSOCIATESMATERIAL('1X$dpCdIr83vpygtBICfMM',#77,$,$,(#38),#1131);
#114=IFCRELASSOCIATESMATERIAL('1lkTw9iNT77A3UD5oHSTav',#77,$,$,(#39),#1132);
#115=IFCRELASSOCIATESMATERIAL('0e9JZKFZHE1xGhwKv0kdNS',#77,$,$,(#40),#1133);
#116=IFCRELCONTAINEDINSPATIALSTRUCTURE('16oImQoYj9_A1f$3cMD$tG',#77,'BuildingStoreyContainer','BuildingStoreyContainer for Elements',(#5,#6,#7,#8,#9,#10,#11,#12,#13,#14,#15,#16,#17,#18,#19,#20,#21,#22,#23,#24,#25,#26,#27,#28,#29,#30,#31,#32,#33,#34,#35,#36,#37,#38,#39,#40),#4);
#117=IFCRELAGGREGATES('0_hC48GjH8oB6PdJvbX9vr',#77,'BuildingContainer','BuildingContainer for BuildingStories',#3,(#4));
#118=IFCRELAGGREGATES('2a9WYlGob2OvXZ5E5NGAwK',#77,'SiteContainer','SiteContainer For Buildings',#2,(#3));
#119=IFCRELAGGREGATES('3RXI8OG4DF5fPc9Gd_kmYm',#77,'ProjectContainer','ProjectContainer for Sites',#1,(#2));
#120=IFCUNITASSIGNMENT((#121,#122,#123,#124,#127,#128,#129,#130,#131));
#121=IFCSIUNIT(*,.LENGTHUNIT.,.MILLI.,.METRE.);
#122=IFCSIUNIT(*,.AREAUNIT.,$,.SQUARE_METRE.);
#123=IFCSIUNIT(*,.VOLUMEUNIT.,$,.CUBIC_METRE.);
#124=IFCCONVERSIONBASEDUNIT($,.PLANEANGLEUNIT.,'DEGREE',#125);
#125=IFCMEASUREWITHUNIT(IFCPLANEANGLEMEASURE(0.017453293),#126);
#126=IFCSIUNIT(*,.PLANEANGLEUNIT.,$,.RADIAN.);
#127=IFCSIUNIT(*,.SOLIDANGLEUNIT.,$,.STERADIAN.);
#128=IFCSIUNIT(*,.MASSUNIT.,$,.GRAM.);
#129=IFCSIUNIT(*,.TIMEUNIT.,$,.SECOND.);
#130=IFCSIUNIT(*,.THERMODYNAMICTEMPERATUREUNIT.,$,.DEGREE_CELSIUS.);
#131=IFCSIUNIT(*,.LUMINOUSINTENSITYUNIT.,$,.LUMEN.);
#132=IFCLOCALPLACEMENT($,#133);
#133=IFCAXIS2PLACEMENT3D(#134,#135,#136);
#134=IFCCARTESIANPOINT((0.,0.,0.));
#135=IFCDIRECTION((0.,0.,1.));
#136=IFCDIRECTION((1.,0.,0.));
#137=IFCLOCALPLACEMENT(#138,#143);
#138=IFCLOCALPLACEMENT($,#139);
#139=IFCAXIS2PLACEMENT3D(#140,#141,#142);
#140=IFCCARTESIANPOINT((0.,0.,0.));
#141=IFCDIRECTION((0.,0.,1.));
#142=IFCDIRECTION((1.,0.,0.));
#143=IFCAXIS2PLACEMENT3D(#144,#145,#146);
#144=IFCCARTESIANPOINT((0.,0.,0.));
#145=IFCDIRECTION((0.,0.,1.));
#146=IFCDIRECTION((1.,0.,0.));
#147=IFCLOCALPLACEMENT(#148,#158);
#148=IFCLOCALPLACEMENT(#149,#154);
#149=IFCLOCALPLACEMENT($,#150);
#150=IFCAXIS2PLACEMENT3D(#151,#152,#153);
#151=IFCCARTESIANPOINT((0.,0.,0.));
#152=IFCDIRECTION((0.,0.,1.));
#153=IFCDIRECTION((1.,0.,0.));
#154=IFCAXIS2PLACEMENT3D(#155,#156,#157);
#155=IFCCARTESIANPOINT((0.,0.,0.));
#156=IFCDIRECTION((0.,0.,1.));
#157=IFCDIRECTION((1.,0.,0.));
#158=IFCAXIS2PLACEMENT3D(#159,#160,#161);
#159=IFCCARTESIANPOINT((0.,0.,0.));
#160=IFCDIRECTION((0.,0.,1.));
#161=IFCDIRECTION((1.,0.,0.));
#162=IFCLOCALPLACEMENT(#163,#178);
#163=IFCLOCALPLACEMENT(#164,#174);
#164=IFCLOCALPLACEMENT(#165,#170);
#165=IFCLOCALPLACEMENT($,#166);
#166=IFCAXIS2PLACEMENT3D(#167,#168,#169);
#167=IFCCARTESIANPOINT((0.,0.,0.));
#168=IFCDIRECTION((0.,0.,1.));
#169=IFCDIRECTION((1.,0.,0.));
#170=IFCAXIS2PLACEMENT3D(#171,#172,#173);
#171=IFCCARTESIANPOINT((0.,0.,0.));
#172=IFCDIRECTION((0.,0.,1.));
#173=IFCDIRECTION((1.,0.,0.));
#174=IFCAXIS2PLACEMENT3D(#175,#176,#177);
#175=IFCCARTESIANPOINT((0.,0.,0.));
#176=IFCDIRECTION((0.,0.,1.));
#177=IFCDIRECTION((1.,0.,0.));
#178=IFCAXIS2PLACEMENT3D(#179,#180,#181);
#179=IFCCARTESIANPOINT((-17000.,16000.,0.));
#180=IFCDIRECTION((0.,0.,1.));
#181=IFCDIRECTION((1.,0.,0.));
#182=IFCPRODUCTDEFINITIONSHAPE($,$,(#41));
#183=IFCGRIDAXIS('5',#184,.T.);
#184=IFCPOLYLINE((#185,#186));
#185=IFCCARTESIANPOINT((0.,0.));
#186=IFCCARTESIANPOINT((23000.,0.));
#187=IFCGRIDAXIS('4',#188,.T.);
#188=IFCPOLYLINE((#189,#190));
#189=IFCCARTESIANPOINT((0.,-2000.));
#190=IFCCARTESIANPOINT((23000.,-2000.));
#191=IFCGRIDAXIS('3',#192,.T.);
#192=IFCPOLYLINE((#193,#194));
#193=IFCCARTESIANPOINT((0.,-6000.));
#194=IFCCARTESIANPOINT((23000.,-6000.));
#195=IFCGRIDAXIS('2',#196,.T.);
#196=IFCPOLYLINE((#197,#198));
#197=IFCCARTESIANPOINT((0.,-8000.));
#198=IFCCARTESIANPOINT((23000.,-8000.));
#199=IFCGRIDAXIS('1',#200,.T.);
#200=IFCPOLYLINE((#201,#202));
#201=IFCCARTESIANPOINT((0.,-12000.));
#202=IFCCARTESIANPOINT((23000.,-12000.));
#203=IFCGRIDAXIS('A',#204,.T.);
#204=IFCPOLYLINE((#205,#206));
#205=IFCCARTESIANPOINT((19000.,-16000.));
#206=IFCCARTESIANPOINT((19000.,3000.));
#207=IFCGRIDAXIS('B',#208,.T.);
#208=IFCPOLYLINE((#209,#210));
#209=IFCCARTESIANPOINT((15000.,-16000.));
#210=IFCCARTESIANPOINT((15000.,3000.));
#211=IFCGRIDAXIS('C',#212,.T.);
#212=IFCPOLYLINE((#213,#214));
#213=IFCCARTESIANPOINT((11000.,-16000.));
#214=IFCCARTESIANPOINT((11000.,3000.));
#215=IFCGRIDAXIS('D',#216,.T.);
#216=IFCPOLYLINE((#217,#218));
#217=IFCCARTESIANPOINT((7000.,-16000.));
#218=IFCCARTESIANPOINT((7000.,3000.));
#219=IFCGRIDAXIS('E',#220,.T.);
#220=IFCPOLYLINE((#221,#222));
#221=IFCCARTESIANPOINT((3000.,-16000.));
#222=IFCCARTESIANPOINT((3000.,3000.));
#223=IFCGRIDPLACEMENT(#224,$);
#224=IFCVIRTUALGRIDINTERSECTION((#225,#229),(0.,0.,0.));
#225=IFCGRIDAXIS('E',#226,.T.);
#226=IFCPOLYLINE((#227,#228));
#227=IFCCARTESIANPOINT((3000.,-16000.));
#228=IFCCARTESIANPOINT((3000.,3000.));
#229=IFCGRIDAXIS('1',#230,.T.);
#230=IFCPOLYLINE((#231,#232));
#231=IFCCARTESIANPOINT((0.,-12000.));
#232=IFCCARTESIANPOINT((23000.,-12000.));
#233=IFCPRODUCTDEFINITIONSHAPE($,$,(#42));
#234=IFCGRIDPLACEMENT(#235,$);
#235=IFCVIRTUALGRIDINTERSECTION((#236,#240),(0.,0.,0.));
#236=IFCGRIDAXIS('E',#237,.T.);
#237=IFCPOLYLINE((#238,#239));
#238=IFCCARTESIANPOINT((3000.,-16000.));
#239=IFCCARTESIANPOINT((3000.,3000.));
#240=IFCGRIDAXIS('2',#241,.T.);
#241=IFCPOLYLINE((#242,#243));
#242=IFCCARTESIANPOINT((0.,-8000.));
#243=IFCCARTESIANPOINT((23000.,-8000.));
#244=IFCPRODUCTDEFINITIONSHAPE($,$,(#43));
#245=IFCGRIDPLACEMENT(#246,$);
#246=IFCVIRTUALGRIDINTERSECTION((#247,#251),(0.,0.,0.));
#247=IFCGRIDAXIS('E',#248,.T.);
#248=IFCPOLYLINE((#249,#250));
#249=IFCCARTESIANPOINT((3000.,-16000.));
#250=IFCCARTESIANPOINT((3000.,3000.));
#251=IFCGRIDAXIS('3',#252,.T.);
#252=IFCPOLYLINE((#253,#254));
#253=IFCCARTESIANPOINT((0.,-6000.));
#254=IFCCARTESIANPOINT((23000.,-6000.));
#255=IFCPRODUCTDEFINITIONSHAPE($,$,(#44));
#256=IFCGRIDPLACEMENT(#257,$);
#257=IFCVIRTUALGRIDINTERSECTION((#258,#262),(0.,0.,0.));
#258=IFCGRIDAXIS('E',#259,.T.);
#259=IFCPOLYLINE((#260,#261));
#260=IFCCARTESIANPOINT((3000.,-16000.));
#261=IFCCARTESIANPOINT((3000.,3000.));
#262=IFCGRIDAXIS('4',#263,.T.);
#263=IFCPOLYLINE((#264,#265));
#264=IFCCARTESIANPOINT((0.,-2000.));
#265=IFCCARTESIANPOINT((23000.,-2000.));
#266=IFCPRODUCTDEFINITIONSHAPE($,$,(#45));
#267=IFCGRIDPLACEMENT(#268,$);
#268=IFCVIRTUALGRIDINTERSECTION((#269,#273),(0.,0.,0.));
#269=IFCGRIDAXIS('E',#270,.T.);
#270=IFCPOLYLINE((#271,#272));
#271=IFCCARTESIANPOINT((3000.,-16000.));
#272=IFCCARTESIANPOINT((3000.,3000.));
#273=IFCGRIDAXIS('5',#274,.T.);
#274=IFCPOLYLINE((#275,#276));
#275=IFCCARTESIANPOINT((0.,0.));
#276=IFCCARTESIANPOINT((23000.,0.));
#277=IFCPRODUCTDEFINITIONSHAPE($,$,(#46));
#278=IFCGRIDPLACEMENT(#279,$);
#279=IFCVIRTUALGRIDINTERSECTION((#280,#284),(0.,0.,0.));
#280=IFCGRIDAXIS('D',#281,.T.);
#281=IFCPOLYLINE((#282,#283));
#282=IFCCARTESIANPOINT((7000.,-16000.));
#283=IFCCARTESIANPOINT((7000.,3000.));
#284=IFCGRIDAXIS('1',#285,.T.);
#285=IFCPOLYLINE((#286,#287));
#286=IFCCARTESIANPOINT((0.,-12000.));
#287=IFCCARTESIANPOINT((23000.,-12000.));
#288=IFCPRODUCTDEFINITIONSHAPE($,$,(#47));
#289=IFCGRIDPLACEMENT(#290,$);
#290=IFCVIRTUALGRIDINTERSECTION((#291,#295),(0.,0.,0.));
#291=IFCGRIDAXIS('D',#292,.T.);
#292=IFCPOLYLINE((#293,#294));
#293=IFCCARTESIANPOINT((7000.,-16000.));
#294=IFCCARTESIANPOINT((7000.,3000.));
#295=IFCGRIDAXIS('2',#296,.T.);
#296=IFCPOLYLINE((#297,#298));
#297=IFCCARTESIANPOINT((0.,-8000.));
#298=IFCCARTESIANPOINT((23000.,-8000.));
#299=IFCPRODUCTDEFINITIONSHAPE($,$,(#48));
#300=IFCGRIDPLACEMENT(#301,$);
#301=IFCVIRTUALGRIDINTERSECTION((#302,#306),(0.,0.,0.));
#302=IFCGRIDAXIS('D',#303,.T.);
#303=IFCPOLYLINE((#304,#305));
#304=IFCCARTESIANPOINT((7000.,-16000.));
#305=IFCCARTESIANPOINT((7000.,3000.));
#306=IFCGRIDAXIS('3',#307,.T.);
#307=IFCPOLYLINE((#308,#309));
#308=IFCCARTESIANPOINT((0.,-6000.));
#309=IFCCARTESIANPOINT((23000.,-6000.));
#310=IFCPRODUCTDEFINITIONSHAPE($,$,(#49));
#311=IFCGRIDPLACEMENT(#312,$);
#312=IFCVIRTUALGRIDINTERSECTION((#313,#317),(0.,0.,0.));
#313=IFCGRIDAXIS('D',#314,.T.);
#314=IFCPOLYLINE((#315,#316));
#315=IFCCARTESIANPOINT((7000.,-16000.));
#316=IFCCARTESIANPOINT((7000.,3000.));
#317=IFCGRIDAXIS('4',#318,.T.);
#318=IFCPOLYLINE((#319,#320));
#319=IFCCARTESIANPOINT((0.,-2000.));
#320=IFCCARTESIANPOINT((23000.,-2000.));
#321=IFCPRODUCTDEFINITIONSHAPE($,$,(#50));
#322=IFCGRIDPLACEMENT(#323,$);
#323=IFCVIRTUALGRIDINTERSECTION((#324,#328),(0.,0.,0.));
#324=IFCGRIDAXIS('D',#325,.T.);
#325=IFCPOLYLINE((#326,#327));
#326=IFCCARTESIANPOINT((7000.,-16000.));
#327=IFCCARTESIANPOINT((7000.,3000.));
#328=IFCGRIDAXIS('5',#329,.T.);
#329=IFCPOLYLINE((#330,#331));
#330=IFCCARTESIANPOINT((0.,0.));
#331=IFCCARTESIANPOINT((23000.,0.));
#332=IFCPRODUCTDEFINITIONSHAPE($,$,(#51));
#333=IFCGRIDPLACEMENT(#334,$);
#334=IFCVIRTUALGRIDINTERSECTION((#335,#339),(0.,0.,0.));
#335=IFCGRIDAXIS('C',#336,.T.);
#336=IFCPOLYLINE((#337,#338));
#337=IFCCARTESIANPOINT((11000.,-16000.));
#338=IFCCARTESIANPOINT((11000.,3000.));
#339=IFCGRIDAXIS('1',#340,.T.);
#340=IFCPOLYLINE((#341,#342));
#341=IFCCARTESIANPOINT((0.,-12000.));
#342=IFCCARTESIANPOINT((23000.,-12000.));
#343=IFCPRODUCTDEFINITIONSHAPE($,$,(#52));
#344=IFCGRIDPLACEMENT(#345,$);
#345=IFCVIRTUALGRIDINTERSECTION((#346,#350),(0.,0.,0.));
#346=IFCGRIDAXIS('C',#347,.T.);
#347=IFCPOLYLINE((#348,#349));
#348=IFCCARTESIANPOINT((11000.,-16000.));
#349=IFCCARTESIANPOINT((11000.,3000.));
#350=IFCGRIDAXIS('2',#351,.T.);
#351=IFCPOLYLINE((#352,#353));
#352=IFCCARTESIANPOINT((0.,-8000.));
#353=IFCCARTESIANPOINT((23000.,-8000.));
#354=IFCPRODUCTDEFINITIONSHAPE($,$,(#53));
#355=IFCGRIDPLACEMENT(#356,$);
#356=IFCVIRTUALGRIDINTERSECTION((#357,#361),(0.,0.,0.));
#357=IFCGRIDAXIS('C',#358,.T.);
#358=IFCPOLYLINE((#359,#360));
#359=IFCCARTESIANPOINT((11000.,-16000.));
#360=IFCCARTESIANPOINT((11000.,3000.));
#361=IFCGRIDAXIS('3',#362,.T.);
#362=IFCPOLYLINE((#363,#364));
#363=IFCCARTESIANPOINT((0.,-6000.));
#364=IFCCARTESIANPOINT((23000.,-6000.));
#365=IFCPRODUCTDEFINITIONSHAPE($,$,(#54));
#366=IFCGRIDPLACEMENT(#367,$);
#367=IFCVIRTUALGRIDINTERSECTION((#368,#372),(0.,0.,0.));
#368=IFCGRIDAXIS('C',#369,.T.);
#369=IFCPOLYLINE((#370,#371));
#370=IFCCARTESIANPOINT((11000.,-16000.));
#371=IFCCARTESIANPOINT((11000.,3000.));
#372=IFCGRIDAXIS('4',#373,.T.);
#373=IFCPOLYLINE((#374,#375));
#374=IFCCARTESIANPOINT((0.,-2000.));
#375=IFCCARTESIANPOINT((23000.,-2000.));
#376=IFCPRODUCTDEFINITIONSHAPE($,$,(#55));
#377=IFCGRIDPLACEMENT(#378,$);
#378=IFCVIRTUALGRIDINTERSECTION((#379,#383),(0.,0.,0.));
#379=IFCGRIDAXIS('C',#380,.T.);
#380=IFCPOLYLINE((#381,#382));
#381=IFCCARTESIANPOINT((11000.,-16000.));
#382=IFCCARTESIANPOINT((11000.,3000.));
#383=IFCGRIDAXIS('5',#384,.T.);
#384=IFCPOLYLINE((#385,#386));
#385=IFCCARTESIANPOINT((0.,0.));
#386=IFCCARTESIANPOINT((23000.,0.));
#387=IFCPRODUCTDEFINITIONSHAPE($,$,(#56));
#388=IFCGRIDPLACEMENT(#389,$);
#389=IFCVIRTUALGRIDINTERSECTION((#390,#394),(0.,0.,0.));
#390=IFCGRIDAXIS('B',#391,.T.);
#391=IFCPOLYLINE((#392,#393));
#392=IFCCARTESIANPOINT((15000.,-16000.));
#393=IFCCARTESIANPOINT((15000.,3000.));
#394=IFCGRIDAXIS('1',#395,.T.);
#395=IFCPOLYLINE((#396,#397));
#396=IFCCARTESIANPOINT((0.,-12000.));
#397=IFCCARTESIANPOINT((23000.,-12000.));
#398=IFCPRODUCTDEFINITIONSHAPE($,$,(#57));
#399=IFCGRIDPLACEMENT(#400,$);
#400=IFCVIRTUALGRIDINTERSECTION((#401,#405),(0.,0.,0.));
#401=IFCGRIDAXIS('B',#402,.T.);
#402=IFCPOLYLINE((#403,#404));
#403=IFCCARTESIANPOINT((15000.,-16000.));
#404=IFCCARTESIANPOINT((15000.,3000.));
#405=IFCGRIDAXIS('2',#406,.T.);
#406=IFCPOLYLINE((#407,#408));
#407=IFCCARTESIANPOINT((0.,-8000.));
#408=IFCCARTESIANPOINT((23000.,-8000.));
#409=IFCPRODUCTDEFINITIONSHAPE($,$,(#58));
#410=IFCGRIDPLACEMENT(#411,$);
#411=IFCVIRTUALGRIDINTERSECTION((#412,#416),(0.,0.,0.));
#412=IFCGRIDAXIS('B',#413,.T.);
#413=IFCPOLYLINE((#414,#415));
#414=IFCCARTESIANPOINT((15000.,-16000.));
#415=IFCCARTESIANPOINT((15000.,3000.));
#416=IFCGRIDAXIS('3',#417,.T.);
#417=IFCPOLYLINE((#418,#419));
#418=IFCCARTESIANPOINT((0.,-6000.));
#419=IFCCARTESIANPOINT((23000.,-6000.));
#420=IFCPRODUCTDEFINITIONSHAPE($,$,(#59));
#421=IFCGRIDPLACEMENT(#422,$);
#422=IFCVIRTUALGRIDINTERSECTION((#423,#427),(0.,0.,0.));
#423=IFCGRIDAXIS('B',#424,.T.);
#424=IFCPOLYLINE((#425,#426));
#425=IFCCARTESIANPOINT((15000.,-16000.));
#426=IFCCARTESIANPOINT((15000.,3000.));
#427=IFCGRIDAXIS('4',#428,.T.);
#428=IFCPOLYLINE((#429,#430));
#429=IFCCARTESIANPOINT((0.,-2000.));
#430=IFCCARTESIANPOINT((23000.,-2000.));
#431=IFCPRODUCTDEFINITIONSHAPE($,$,(#60));
#432=IFCGRIDPLACEMENT(#433,$);
#433=IFCVIRTUALGRIDINTERSECTION((#434,#438),(0.,0.,0.));
#434=IFCGRIDAXIS('B',#435,.T.);
#435=IFCPOLYLINE((#436,#437));
#436=IFCCARTESIANPOINT((15000.,-16000.));
#437=IFCCARTESIANPOINT((15000.,3000.));
#438=IFCGRIDAXIS('5',#439,.T.);
#439=IFCPOLYLINE((#440,#441));
#440=IFCCARTESIANPOINT((0.,0.));
#441=IFCCARTESIANPOINT((23000.,0.));
#442=IFCPRODUCTDEFINITIONSHAPE($,$,(#61));
#443=IFCGRIDPLACEMENT(#444,$);
#444=IFCVIRTUALGRIDINTERSECTION((#445,#449),(0.,0.,0.));
#445=IFCGRIDAXIS('A',#446,.T.);
#446=IFCPOLYLINE((#447,#448));
#447=IFCCARTESIANPOINT((19000.,-16000.));
#448=IFCCARTESIANPOINT((19000.,3000.));
#449=IFCGRIDAXIS('1',#450,.T.);
#450=IFCPOLYLINE((#451,#452));
#451=IFCCARTESIANPOINT((0.,-12000.));
#452=IFCCARTESIANPOINT((23000.,-12000.));
#453=IFCPRODUCTDEFINITIONSHAPE($,$,(#62));
#454=IFCGRIDPLACEMENT(#455,$);
#455=IFCVIRTUALGRIDINTERSECTION((#456,#460),(0.,0.,0.));
#456=IFCGRIDAXIS('A',#457,.T.);
#457=IFCPOLYLINE((#458,#459));
#458=IFCCARTESIANPOINT((19000.,-16000.));
#459=IFCCARTESIANPOINT((19000.,3000.));
#460=IFCGRIDAXIS('2',#461,.T.);
#461=IFCPOLYLINE((#462,#463));
#462=IFCCARTESIANPOINT((0.,-8000.));
#463=IFCCARTESIANPOINT((23000.,-8000.));
#464=IFCPRODUCTDEFINITIONSHAPE($,$,(#63));
#465=IFCGRIDPLACEMENT(#466,$);
#466=IFCVIRTUALGRIDINTERSECTION((#467,#471),(0.,0.,0.));
#467=IFCGRIDAXIS('A',#468,.T.);
#468=IFCPOLYLINE((#469,#470));
#469=IFCCARTESIANPOINT((19000.,-16000.));
#470=IFCCARTESIANPOINT((19000.,3000.));
#471=IFCGRIDAXIS('3',#472,.T.);
#472=IFCPOLYLINE((#473,#474));
#473=IFCCARTESIANPOINT((0.,-6000.));
#474=IFCCARTESIANPOINT((23000.,-6000.));
#475=IFCPRODUCTDEFINITIONSHAPE($,$,(#64));
#476=IFCGRIDPLACEMENT(#477,$);
#477=IFCVIRTUALGRIDINTERSECTION((#478,#482),(0.,0.,0.));
#478=IFCGRIDAXIS('A',#479,.T.);
#479=IFCPOLYLINE((#480,#481));
#480=IFCCARTESIANPOINT((19000.,-16000.));
#481=IFCCARTESIANPOINT((19000.,3000.));
#482=IFCGRIDAXIS('4',#483,.T.);
#483=IFCPOLYLINE((#484,#485));
#484=IFCCARTESIANPOINT((0.,-2000.));
#485=IFCCARTESIANPOINT((23000.,-2000.));
#486=IFCPRODUCTDEFINITIONSHAPE($,$,(#65));
#487=IFCGRIDPLACEMENT(#488,$);
#488=IFCVIRTUALGRIDINTERSECTION((#489,#493),(0.,0.,0.));
#489=IFCGRIDAXIS('A',#490,.T.);
#490=IFCPOLYLINE((#491,#492));
#491=IFCCARTESIANPOINT((19000.,-16000.));
#492=IFCCARTESIANPOINT((19000.,3000.));
#493=IFCGRIDAXIS('5',#494,.T.);
#494=IFCPOLYLINE((#495,#496));
#495=IFCCARTESIANPOINT((0.,0.));
#496=IFCCARTESIANPOINT((23000.,0.));
#497=IFCPRODUCTDEFINITIONSHAPE($,$,(#66));
#498=IFCLOCALPLACEMENT(#499,#514);
#499=IFCLOCALPLACEMENT(#500,#510);
#500=IFCLOCALPLACEMENT(#501,#506);
#501=IFCLOCALPLACEMENT($,#502);
#502=IFCAXIS2PLACEMENT3D(#503,#504,#505);
#503=IFCCARTESIANPOINT((0.,0.,0.));
#504=IFCDIRECTION((0.,0.,1.));
#505=IFCDIRECTION((1.,0.,0.));
#506=IFCAXIS2PLACEMENT3D(#507,#508,#509);
#507=IFCCARTESIANPOINT((0.,0.,0.));
#508=IFCDIRECTION((0.,0.,1.));
#509=IFCDIRECTION((1.,0.,0.));
#510=IFCAXIS2PLACEMENT3D(#511,#512,#513);
#511=IFCCARTESIANPOINT((0.,0.,0.));
#512=IFCDIRECTION((0.,0.,1.));
#513=IFCDIRECTION((1.,0.,0.));
#514=IFCAXIS2PLACEMENT3D(#515,#516,#517);
#515=IFCCARTESIANPOINT((-14000.,4000.,2800.));
#516=IFCDIRECTION((0.,0.,1.));
#517=IFCDIRECTION((1.,0.,0.));
#518=IFCPRODUCTDEFINITIONSHAPE($,$,(#67));
#519=IFCLOCALPLACEMENT(#520,#535);
#520=IFCLOCALPLACEMENT(#521,#531);
#521=IFCLOCALPLACEMENT(#522,#527);
#522=IFCLOCALPLACEMENT($,#523);
#523=IFCAXIS2PLACEMENT3D(#524,#525,#526);
#524=IFCCARTESIANPOINT((0.,0.,0.));
#525=IFCDIRECTION((0.,0.,1.));
#526=IFCDIRECTION((1.,0.,0.));
#527=IFCAXIS2PLACEMENT3D(#528,#529,#530);
#528=IFCCARTESIANPOINT((0.,0.,0.));
#529=IFCDIRECTION((0.,0.,1.));
#530=IFCDIRECTION((1.,0.,0.));
#531=IFCAXIS2PLACEMENT3D(#532,#533,#534);
#532=IFCCARTESIANPOINT((0.,0.,0.));
#533=IFCDIRECTION((0.,0.,1.));
#534=IFCDIRECTION((1.,0.,0.));
#535=IFCAXIS2PLACEMENT3D(#536,#537,#538);
#536=IFCCARTESIANPOINT((-10000.,4000.,2800.));
#537=IFCDIRECTION((0.,0.,1.));
#538=IFCDIRECTION((1.,0.,0.));
#539=IFCPRODUCTDEFINITIONSHAPE($,$,(#68));
#540=IFCLOCALPLACEMENT(#541,#556);
#541=IFCLOCALPLACEMENT(#542,#552);
#542=IFCLOCALPLACEMENT(#543,#548);
#543=IFCLOCALPLACEMENT($,#544);
#544=IFCAXIS2PLACEMENT3D(#545,#546,#547);
#545=IFCCARTESIANPOINT((0.,0.,0.));
#546=IFCDIRECTION((0.,0.,1.));
#547=IFCDIRECTION((1.,0.,0.));
#548=IFCAXIS2PLACEMENT3D(#549,#550,#551);
#549=IFCCARTESIANPOINT((0.,0.,0.));
#550=IFCDIRECTION((0.,0.,1.));
#551=IFCDIRECTION((1.,0.,0.));
#552=IFCAXIS2PLACEMENT3D(#553,#554,#555);
#553=IFCCARTESIANPOINT((0.,0.,0.));
#554=IFCDIRECTION((0.,0.,1.));
#555=IFCDIRECTION((1.,0.,0.));
#556=IFCAXIS2PLACEMENT3D(#557,#558,#559);
#557=IFCCARTESIANPOINT((-6000.,4000.,2800.));
#558=IFCDIRECTION((0.,0.,1.));
#559=IFCDIRECTION((1.,0.,0.));
#560=IFCPRODUCTDEFINITIONSHAPE($,$,(#69));
#561=IFCLOCALPLACEMENT(#562,#577);
#562=IFCLOCALPLACEMENT(#563,#573);
#563=IFCLOCALPLACEMENT(#564,#569);
#564=IFCLOCALPLACEMENT($,#565);
#565=IFCAXIS2PLACEMENT3D(#566,#567,#568);
#566=IFCCARTESIANPOINT((0.,0.,0.));
#567=IFCDIRECTION((0.,0.,1.));
#568=IFCDIRECTION((1.,0.,0.));
#569=IFCAXIS2PLACEMENT3D(#570,#571,#572);
#570=IFCCARTESIANPOINT((0.,0.,0.));
#571=IFCDIRECTION((0.,0.,1.));
#572=IFCDIRECTION((1.,0.,0.));
#573=IFCAXIS2PLACEMENT3D(#574,#575,#576);
#574=IFCCARTESIANPOINT((0.,0.,0.));
#575=IFCDIRECTION((0.,0.,1.));
#576=IFCDIRECTION((1.,0.,0.));
#577=IFCAXIS2PLACEMENT3D(#578,#579,#580);
#578=IFCCARTESIANPOINT((-2000.,4000.,2800.));
#579=IFCDIRECTION((0.,0.,1.));
#580=IFCDIRECTION((1.,0.,0.));
#581=IFCPRODUCTDEFINITIONSHAPE($,$,(#70));
#582=IFCLOCALPLACEMENT(#583,#598);
#583=IFCLOCALPLACEMENT(#584,#594);
#584=IFCLOCALPLACEMENT(#585,#590);
#585=IFCLOCALPLACEMENT($,#586);
#586=IFCAXIS2PLACEMENT3D(#587,#588,#589);
#587=IFCCARTESIANPOINT((0.,0.,0.));
#588=IFCDIRECTION((0.,0.,1.));
#589=IFCDIRECTION((1.,0.,0.));
#590=IFCAXIS2PLACEMENT3D(#591,#592,#593);
#591=IFCCARTESIANPOINT((0.,0.,0.));
#592=IFCDIRECTION((0.,0.,1.));
#593=IFCDIRECTION((1.,0.,0.));
#594=IFCAXIS2PLACEMENT3D(#595,#596,#597);
#595=IFCCARTESIANPOINT((0.,0.,0.));
#596=IFCDIRECTION((0.,0.,1.));
#597=IFCDIRECTION((1.,0.,0.));
#598=IFCAXIS2PLACEMENT3D(#599,#600,#601);
#599=IFCCARTESIANPOINT((-14000.,4000.,2800.));
#600=IFCDIRECTION((0.,0.,1.));
#601=IFCDIRECTION((1.9428903E-16,-1.,0.));
#602=IFCPRODUCTDEFINITIONSHAPE($,$,(#71));
#603=IFCLOCALPLACEMENT(#604,#619);
#604=IFCLOCALPLACEMENT(#605,#615);
#605=IFCLOCALPLACEMENT(#606,#611);
#606=IFCLOCALPLACEMENT($,#607);
#607=IFCAXIS2PLACEMENT3D(#608,#609,#610);
#608=IFCCARTESIANPOINT((0.,0.,0.));
#609=IFCDIRECTION((0.,0.,1.));
#610=IFCDIRECTION((1.,0.,0.));
#611=IFCAXIS2PLACEMENT3D(#612,#613,#614);
#612=IFCCARTESIANPOINT((0.,0.,0.));
#613=IFCDIRECTION((0.,0.,1.));
#614=IFCDIRECTION((1.,0.,0.));
#615=IFCAXIS2PLACEMENT3D(#616,#617,#618);
#616=IFCCARTESIANPOINT((0.,0.,0.));
#617=IFCDIRECTION((0.,0.,1.));
#618=IFCDIRECTION((1.,0.,0.));
#619=IFCAXIS2PLACEMENT3D(#620,#621,#622);
#620=IFCCARTESIANPOINT((2000.,4000.,2800.));
#621=IFCDIRECTION((0.,0.,1.));
#622=IFCDIRECTION((1.,0.,0.));
#623=IFCPRODUCTDEFINITIONSHAPE($,$,(#72));
#624=IFCLOCALPLACEMENT(#625,#640);
#625=IFCLOCALPLACEMENT(#626,#636);
#626=IFCLOCALPLACEMENT(#627,#632);
#627=IFCLOCALPLACEMENT($,#628);
#628=IFCAXIS2PLACEMENT3D(#629,#630,#631);
#629=IFCCARTESIANPOINT((0.,0.,0.));
#630=IFCDIRECTION((0.,0.,1.));
#631=IFCDIRECTION((1.,0.,0.));
#632=IFCAXIS2PLACEMENT3D(#633,#634,#635);
#633=IFCCARTESIANPOINT((0.,0.,0.));
#634=IFCDIRECTION((0.,0.,1.));
#635=IFCDIRECTION((1.,0.,0.));
#636=IFCAXIS2PLACEMENT3D(#637,#638,#639);
#637=IFCCARTESIANPOINT((0.,0.,0.));
#638=IFCDIRECTION((0.,0.,1.));
#639=IFCDIRECTION((1.,0.,0.));
#640=IFCAXIS2PLACEMENT3D(#641,#642,#643);
#641=IFCCARTESIANPOINT((-14000.,8000.,2800.));
#642=IFCDIRECTION((0.,0.,1.));
#643=IFCDIRECTION((1.6653345E-16,-1.,0.));
#644=IFCPRODUCTDEFINITIONSHAPE($,$,(#73));
#645=IFCLOCALPLACEMENT(#646,#661);
#646=IFCLOCALPLACEMENT(#647,#657);
#647=IFCLOCALPLACEMENT(#648,#653);
#648=IFCLOCALPLACEMENT($,#649);
#649=IFCAXIS2PLACEMENT3D(#650,#651,#652);
#650=IFCCARTESIANPOINT((0.,0.,0.));
#651=IFCDIRECTION((0.,0.,1.));
#652=IFCDIRECTION((1.,0.,0.));
#653=IFCAXIS2PLACEMENT3D(#654,#655,#656);
#654=IFCCARTESIANPOINT((0.,0.,0.));
#655=IFCDIRECTION((0.,0.,1.));
#656=IFCDIRECTION((1.,0.,0.));
#657=IFCAXIS2PLACEMENT3D(#658,#659,#660);
#658=IFCCARTESIANPOINT((0.,0.,0.));
#659=IFCDIRECTION((0.,0.,1.));
#660=IFCDIRECTION((1.,0.,0.));
#661=IFCAXIS2PLACEMENT3D(#662,#663,#664);
#662=IFCCARTESIANPOINT((-14000.,10000.,2800.));
#663=IFCDIRECTION((0.,0.,1.));
#664=IFCDIRECTION((2.220446E-16,-1.,0.));
#665=IFCPRODUCTDEFINITIONSHAPE($,$,(#74));
#666=IFCLOCALPLACEMENT(#667,#682);
#667=IFCLOCALPLACEMENT(#668,#678);
#668=IFCLOCALPLACEMENT(#669,#674);
#669=IFCLOCALPLACEMENT($,#670);
#670=IFCAXIS2PLACEMENT3D(#671,#672,#673);
#671=IFCCARTESIANPOINT((0.,0.,0.));
#672=IFCDIRECTION((0.,0.,1.));
#673=IFCDIRECTION((1.,0.,0.));
#674=IFCAXIS2PLACEMENT3D(#675,#676,#677);
#675=IFCCARTESIANPOINT((0.,0.,0.));
#676=IFCDIRECTION((0.,0.,1.));
#677=IFCDIRECTION((1.,0.,0.));
#678=IFCAXIS2PLACEMENT3D(#679,#680,#681);
#679=IFCCARTESIANPOINT((0.,0.,0.));
#680=IFCDIRECTION((0.,0.,1.));
#681=IFCDIRECTION((1.,0.,0.));
#682=IFCAXIS2PLACEMENT3D(#683,#684,#685);
#683=IFCCARTESIANPOINT((-14000.,14000.,2800.));
#684=IFCDIRECTION((0.,0.,1.));
#685=IFCDIRECTION((2.220446E-16,-1.,0.));
#686=IFCPRODUCTDEFINITIONSHAPE($,$,(#75));
#687=IFCLOCALPLACEMENT(#688,#703);
#688=IFCLOCALPLACEMENT(#689,#699);
#689=IFCLOCALPLACEMENT(#690,#695);
#690=IFCLOCALPLACEMENT($,#691);
#691=IFCAXIS2PLACEMENT3D(#692,#693,#694);
#692=IFCCARTESIANPOINT((0.,0.,0.));
#693=IFCDIRECTION((0.,0.,1.));
#694=IFCDIRECTION((1.,0.,0.));
#695=IFCAXIS2PLACEMENT3D(#696,#697,#698);
#696=IFCCARTESIANPOINT((0.,0.,0.));
#697=IFCDIRECTION((0.,0.,1.));
#698=IFCDIRECTION((1.,0.,0.));
#699=IFCAXIS2PLACEMENT3D(#700,#701,#702);
#700=IFCCARTESIANPOINT((0.,0.,0.));
#701=IFCDIRECTION((0.,0.,1.));
#702=IFCDIRECTION((1.,0.,0.));
#703=IFCAXIS2PLACEMENT3D(#704,#705,#706);
#704=IFCCARTESIANPOINT((-14000.,16000.,2800.));
#705=IFCDIRECTION((0.,0.,1.));
#706=IFCDIRECTION((2.220446E-16,-1.,0.));
#707=IFCPRODUCTDEFINITIONSHAPE($,$,(#76));
#708=IFCGEOMETRICCURVESET((#709,#712,#715,#718,#721,#724,#727,#730,#733,#736));
#709=IFCPOLYLINE((#710,#711));
#710=IFCCARTESIANPOINT((0.,0.));
#711=IFCCARTESIANPOINT((23000.,0.));
#712=IFCPOLYLINE((#713,#714));
#713=IFCCARTESIANPOINT((0.,-2000.));
#714=IFCCARTESIANPOINT((23000.,-2000.));
#715=IFCPOLYLINE((#716,#717));
#716=IFCCARTESIANPOINT((0.,-6000.));
#717=IFCCARTESIANPOINT((23000.,-6000.));
#718=IFCPOLYLINE((#719,#720));
#719=IFCCARTESIANPOINT((0.,-8000.));
#720=IFCCARTESIANPOINT((23000.,-8000.));
#721=IFCPOLYLINE((#722,#723));
#722=IFCCARTESIANPOINT((0.,-12000.));
#723=IFCCARTESIANPOINT((23000.,-12000.));
#724=IFCPOLYLINE((#725,#726));
#725=IFCCARTESIANPOINT((19000.,-16000.));
#726=IFCCARTESIANPOINT((19000.,3000.));
#727=IFCPOLYLINE((#728,#729));
#728=IFCCARTESIANPOINT((15000.,-16000.));
#729=IFCCARTESIANPOINT((15000.,3000.));
#730=IFCPOLYLINE((#731,#732));
#731=IFCCARTESIANPOINT((11000.,-16000.));
#732=IFCCARTESIANPOINT((11000.,3000.));
#733=IFCPOLYLINE((#734,#735));
#734=IFCCARTESIANPOINT((7000.,-16000.));
#735=IFCCARTESIANPOINT((7000.,3000.));
#736=IFCPOLYLINE((#737,#738));
#737=IFCCARTESIANPOINT((3000.,-16000.));
#738=IFCCARTESIANPOINT((3000.,3000.));
#739=IFCEXTRUDEDAREASOLID(#740,#744,#748,2800.);
#740=IFCRECTANGLEPROFILEDEF(.AREA.,$,#741,300.,300.);
#741=IFCAXIS2PLACEMENT2D(#742,#743);
#742=IFCCARTESIANPOINT((0.,0.));
#743=IFCDIRECTION((1.,0.));
#744=IFCAXIS2PLACEMENT3D(#745,#746,#747);
#745=IFCCARTESIANPOINT((0.,0.,0.));
#746=IFCDIRECTION((0.,0.,1.));
#747=IFCDIRECTION((1.,0.,0.));
#748=IFCDIRECTION((0.,0.,1.));
#749=IFCEXTRUDEDAREASOLID(#750,#754,#758,2800.);
#750=IFCRECTANGLEPROFILEDEF(.AREA.,$,#751,300.,300.);
#751=IFCAXIS2PLACEMENT2D(#752,#753);
#752=IFCCARTESIANPOINT((0.,0.));
#753=IFCDIRECTION((1.,0.));
#754=IFCAXIS2PLACEMENT3D(#755,#756,#757);
#755=IFCCARTESIANPOINT((0.,0.,0.));
#756=IFCDIRECTION((0.,0.,1.));
#757=IFCDIRECTION((1.,0.,0.));
#758=IFCDIRECTION((0.,0.,1.));
#759=IFCEXTRUDEDAREASOLID(#760,#764,#768,2800.);
#760=IFCRECTANGLEPROFILEDEF(.AREA.,$,#761,300.,300.);
#761=IFCAXIS2PLACEMENT2D(#762,#763);
#762=IFCCARTESIANPOINT((0.,0.));
#763=IFCDIRECTION((1.,0.));
#764=IFCAXIS2PLACEMENT3D(#765,#766,#767);
#765=IFCCARTESIANPOINT((0.,0.,0.));
#766=IFCDIRECTION((0.,0.,1.));
#767=IFCDIRECTION((1.,0.,0.));
#768=IFCDIRECTION((0.,0.,1.));
#769=IFCEXTRUDEDAREASOLID(#770,#774,#778,2800.);
#770=IFCRECTANGLEPROFILEDEF(.AREA.,$,#771,300.,300.);
#771=IFCAXIS2PLACEMENT2D(#772,#773);
#772=IFCCARTESIANPOINT((0.,0.));
#773=IFCDIRECTION((1.,0.));
#774=IFCAXIS2PLACEMENT3D(#775,#776,#777);
#775=IFCCARTESIANPOINT((0.,0.,0.));
#776=IFCDIRECTION((0.,0.,1.));
#777=IFCDIRECTION((1.,0.,0.));
#778=IFCDIRECTION((0.,0.,1.));
#779=IFCEXTRUDEDAREASOLID(#780,#784,#788,2800.);
#780=IFCRECTANGLEPROFILEDEF(.AREA.,$,#781,300.,300.);
#781=IFCAXIS2PLACEMENT2D(#782,#783);
#782=IFCCARTESIANPOINT((0.,0.));
#783=IFCDIRECTION((1.,0.));
#784=IFCAXIS2PLACEMENT3D(#785,#786,#787);
#785=IFCCARTESIANPOINT((0.,0.,0.));
#786=IFCDIRECTION((0.,0.,1.));
#787=IFCDIRECTION((1.,0.,0.));
#788=IFCDIRECTION((0.,0.,1.));
#789=IFCEXTRUDEDAREASOLID(#790,#794,#798,2800.);
#790=IFCRECTANGLEPROFILEDEF(.AREA.,$,#791,300.,300.);
#791=IFCAXIS2PLACEMENT2D(#792,#793);
#792=IFCCARTESIANPOINT((0.,0.));
#793=IFCDIRECTION((1.,0.));
#794=IFCAXIS2PLACEMENT3D(#795,#796,#797);
#795=IFCCARTESIANPOINT((0.,0.,0.));
#796=IFCDIRECTION((0.,0.,1.));
#797=IFCDIRECTION((1.,0.,0.));
#798=IFCDIRECTION((0.,0.,1.));
#799=IFCEXTRUDEDAREASOLID(#800,#804,#808,2800.);
#800=IFCRECTANGLEPROFILEDEF(.AREA.,$,#801,300.,300.);
#801=IFCAXIS2PLACEMENT2D(#802,#803);
#802=IFCCARTESIANPOINT((0.,0.));
#803=IFCDIRECTION((1.,0.));
#804=IFCAXIS2PLACEMENT3D(#805,#806,#807);
#805=IFCCARTESIANPOINT((0.,0.,0.));
#806=IFCDIRECTION((0.,0.,1.));
#807=IFCDIRECTION((1.,0.,0.));
#808=IFCDIRECTION((0.,0.,1.));
#809=IFCEXTRUDEDAREASOLID(#810,#814,#818,2800.);
#810=IFCRECTANGLEPROFILEDEF(.AREA.,$,#811,300.,300.);
#811=IFCAXIS2PLACEMENT2D(#812,#813);
#812=IFCCARTESIANPOINT((0.,0.));
#813=IFCDIRECTION((1.,0.));
#814=IFCAXIS2PLACEMENT3D(#815,#816,#817);
#815=IFCCARTESIANPOINT((0.,0.,0.));
#816=IFCDIRECTION((0.,0.,1.));
#817=IFCDIRECTION((1.,0.,0.));
#818=IFCDIRECTION((0.,0.,1.));
#819=IFCEXTRUDEDAREASOLID(#820,#824,#828,2800.);
#820=IFCRECTANGLEPROFILEDEF(.AREA.,$,#821,300.,300.);
#821=IFCAXIS2PLACEMENT2D(#822,#823);
#822=IFCCARTESIANPOINT((0.,0.));
#823=IFCDIRECTION((1.,0.));
#824=IFCAXIS2PLACEMENT3D(#825,#826,#827);
#825=IFCCARTESIANPOINT((0.,0.,0.));
#826=IFCDIRECTION((0.,0.,1.));
#827=IFCDIRECTION((1.,0.,0.));
#828=IFCDIRECTION((0.,0.,1.));
#829=IFCEXTRUDEDAREASOLID(#830,#834,#838,2800.);
#830=IFCRECTANGLEPROFILEDEF(.AREA.,$,#831,300.,300.);
#831=IFCAXIS2PLACEMENT2D(#832,#833);
#832=IFCCARTESIANPOINT((0.,0.));
#833=IFCDIRECTION((1.,0.));
#834=IFCAXIS2PLACEMENT3D(#835,#836,#837);
#835=IFCCARTESIANPOINT((0.,0.,0.));
#836=IFCDIRECTION((0.,0.,1.));
#837=IFCDIRECTION((1.,0.,0.));
#838=IFCDIRECTION((0.,0.,1.));
#839=IFCEXTRUDEDAREASOLID(#840,#844,#848,2800.);
#840=IFCRECTANGLEPROFILEDEF(.AREA.,$,#841,300.,300.);
#841=IFCAXIS2PLACEMENT2D(#842,#843);
#842=IFCCARTESIANPOINT((0.,0.));
#843=IFCDIRECTION((1.,0.));
#844=IFCAXIS2PLACEMENT3D(#845,#846,#847);
#845=IFCCARTESIANPOINT((0.,0.,0.));
#846=IFCDIRECTION((0.,0.,1.));
#847=IFCDIRECTION((1.,0.,0.));
#848=IFCDIRECTION((0.,0.,1.));
#849=IFCEXTRUDEDAREASOLID(#850,#854,#858,2800.);
#850=IFCRECTANGLEPROFILEDEF(.AREA.,$,#851,300.,300.);
#851=IFCAXIS2PLACEMENT2D(#852,#853);
#852=IFCCARTESIANPOINT((0.,0.));
#853=IFCDIRECTION((1.,0.));
#854=IFCAXIS2PLACEMENT3D(#855,#856,#857);
#855=IFCCARTESIANPOINT((0.,0.,0.));
#856=IFCDIRECTION((0.,0.,1.));
#857=IFCDIRECTION((1.,0.,0.));
#858=IFCDIRECTION((0.,0.,1.));
#859=IFCEXTRUDEDAREASOLID(#860,#864,#868,2800.);
#860=IFCRECTANGLEPROFILEDEF(.AREA.,$,#861,300.,300.);
#861=IFCAXIS2PLACEMENT2D(#862,#863);
#862=IFCCARTESIANPOINT((0.,0.));
#863=IFCDIRECTION((1.,0.));
#864=IFCAXIS2PLACEMENT3D(#865,#866,#867);
#865=IFCCARTESIANPOINT((0.,0.,0.));
#866=IFCDIRECTION((0.,0.,1.));
#867=IFCDIRECTION((1.,0.,0.));
#868=IFCDIRECTION((0.,0.,1.));
#869=IFCEXTRUDEDAREASOLID(#870,#874,#878,2800.);
#870=IFCRECTANGLEPROFILEDEF(.AREA.,$,#871,300.,300.);
#871=IFCAXIS2PLACEMENT2D(#872,#873);
#872=IFCCARTESIANPOINT((0.,0.));
#873=IFCDIRECTION((1.,0.));
#874=IFCAXIS2PLACEMENT3D(#875,#876,#877);
#875=IFCCARTESIANPOINT((0.,0.,0.));
#876=IFCDIRECTION((0.,0.,1.));
#877=IFCDIRECTION((1.,0.,0.));
#878=IFCDIRECTION((0.,0.,1.));
#879=IFCEXTRUDEDAREASOLID(#880,#884,#888,2800.);
#880=IFCRECTANGLEPROFILEDEF(.AREA.,$,#881,300.,300.);
#881=IFCAXIS2PLACEMENT2D(#882,#883);
#882=IFCCARTESIANPOINT((0.,0.));
#883=IFCDIRECTION((1.,0.));
#884=IFCAXIS2PLACEMENT3D(#885,#886,#887);
#885=IFCCARTESIANPOINT((0.,0.,0.));
#886=IFCDIRECTION((0.,0.,1.));
#887=IFCDIRECTION((1.,0.,0.));
#888=IFCDIRECTION((0.,0.,1.));
#889=IFCEXTRUDEDAREASOLID(#890,#894,#898,2800.);
#890=IFCRECTANGLEPROFILEDEF(.AREA.,$,#891,300.,300.);
#891=IFCAXIS2PLACEMENT2D(#892,#893);
#892=IFCCARTESIANPOINT((0.,0.));
#893=IFCDIRECTION((1.,0.));
#894=IFCAXIS2PLACEMENT3D(#895,#896,#897);
#895=IFCCARTESIANPOINT((0.,0.,0.));
#896=IFCDIRECTION((0.,0.,1.));
#897=IFCDIRECTION((1.,0.,0.));
#898=IFCDIRECTION((0.,0.,1.));
#899=IFCEXTRUDEDAREASOLID(#900,#904,#908,2800.);
#900=IFCRECTANGLEPROFILEDEF(.AREA.,$,#901,300.,300.);
#901=IFCAXIS2PLACEMENT2D(#902,#903);
#902=IFCCARTESIANPOINT((0.,0.));
#903=IFCDIRECTION((1.,0.));
#904=IFCAXIS2PLACEMENT3D(#905,#906,#907);
#905=IFCCARTESIANPOINT((0.,0.,0.));
#906=IFCDIRECTION((0.,0.,1.));
#907=IFCDIRECTION((1.,0.,0.));
#908=IFCDIRECTION((0.,0.,1.));
#909=IFCEXTRUDEDAREASOLID(#910,#914,#918,2800.);
#910=IFCRECTANGLEPROFILEDEF(.AREA.,$,#911,300.,300.);
#911=IFCAXIS2PLACEMENT2D(#912,#913);
#912=IFCCARTESIANPOINT((0.,0.));
#913=IFCDIRECTION((1.,0.));
#914=IFCAXIS2PLACEMENT3D(#915,#916,#917);
#915=IFCCARTESIANPOINT((0.,0.,0.));
#916=IFCDIRECTION((0.,0.,1.));
#917=IFCDIRECTION((1.,0.,0.));
#918=IFCDIRECTION((0.,0.,1.));
#919=IFCEXTRUDEDAREASOLID(#920,#924,#928,2800.);
#920=IFCRECTANGLEPROFILEDEF(.AREA.,$,#921,300.,300.);
#921=IFCAXIS2PLACEMENT2D(#922,#923);
#922=IFCCARTESIANPOINT((0.,0.));
#923=IFCDIRECTION((1.,0.));
#924=IFCAXIS2PLACEMENT3D(#925,#926,#927);
#925=IFCCARTESIANPOINT((0.,0.,0.));
#926=IFCDIRECTION((0.,0.,1.));
#927=IFCDIRECTION((1.,0.,0.));
#928=IFCDIRECTION((0.,0.,1.));
#929=IFCEXTRUDEDAREASOLID(#930,#934,#938,2800.);
#930=IFCRECTANGLEPROFILEDEF(.AREA.,$,#931,300.,300.);
#931=IFCAXIS2PLACEMENT2D(#932,#933);
#932=IFCCARTESIANPOINT((0.,0.));
#933=IFCDIRECTION((1.,0.));
#934=IFCAXIS2PLACEMENT3D(#935,#936,#937);
#935=IFCCARTESIANPOINT((0.,0.,0.));
#936=IFCDIRECTION((0.,0.,1.));
#937=IFCDIRECTION((1.,0.,0.));
#938=IFCDIRECTION((0.,0.,1.));
#939=IFCEXTRUDEDAREASOLID(#940,#944,#948,2800.);
#940=IFCRECTANGLEPROFILEDEF(.AREA.,$,#941,300.,300.);
#941=IFCAXIS2PLACEMENT2D(#942,#943);
#942=IFCCARTESIANPOINT((0.,0.));
#943=IFCDIRECTION((1.,0.));
#944=IFCAXIS2PLACEMENT3D(#945,#946,#947);
#945=IFCCARTESIANPOINT((0.,0.,0.));
#946=IFCDIRECTION((0.,0.,1.));
#947=IFCDIRECTION((1.,0.,0.));
#948=IFCDIRECTION((0.,0.,1.));
#949=IFCEXTRUDEDAREASOLID(#950,#954,#958,2800.);
#950=IFCRECTANGLEPROFILEDEF(.AREA.,$,#951,300.,300.);
#951=IFCAXIS2PLACEMENT2D(#952,#953);
#952=IFCCARTESIANPOINT((0.,0.));
#953=IFCDIRECTION((1.,0.));
#954=IFCAXIS2PLACEMENT3D(#955,#956,#957);
#955=IFCCARTESIANPOINT((0.,0.,0.));
#956=IFCDIRECTION((0.,0.,1.));
#957=IFCDIRECTION((1.,0.,0.));
#958=IFCDIRECTION((0.,0.,1.));
#959=IFCEXTRUDEDAREASOLID(#960,#964,#968,2800.);
#960=IFCRECTANGLEPROFILEDEF(.AREA.,$,#961,300.,300.);
#961=IFCAXIS2PLACEMENT2D(#962,#963);
#962=IFCCARTESIANPOINT((0.,0.));
#963=IFCDIRECTION((1.,0.));
#964=IFCAXIS2PLACEMENT3D(#965,#966,#967);
#965=IFCCARTESIANPOINT((0.,0.,0.));
#966=IFCDIRECTION((0.,0.,1.));
#967=IFCDIRECTION((1.,0.,0.));
#968=IFCDIRECTION((0.,0.,1.));
#969=IFCEXTRUDEDAREASOLID(#970,#974,#978,2800.);
#970=IFCRECTANGLEPROFILEDEF(.AREA.,$,#971,300.,300.);
#971=IFCAXIS2PLACEMENT2D(#972,#973);
#972=IFCCARTESIANPOINT((0.,0.));
#973=IFCDIRECTION((1.,0.));
#974=IFCAXIS2PLACEMENT3D(#975,#976,#977);
#975=IFCCARTESIANPOINT((0.,0.,0.));
#976=IFCDIRECTION((0.,0.,1.));
#977=IFCDIRECTION((1.,0.,0.));
#978=IFCDIRECTION((0.,0.,1.));
#979=IFCEXTRUDEDAREASOLID(#980,#984,#988,2800.);
#980=IFCRECTANGLEPROFILEDEF(.AREA.,$,#981,300.,300.);
#981=IFCAXIS2PLACEMENT2D(#982,#983);
#982=IFCCARTESIANPOINT((0.,0.));
#983=IFCDIRECTION((1.,0.));
#984=IFCAXIS2PLACEMENT3D(#985,#986,#987);
#985=IFCCARTESIANPOINT((0.,0.,0.));
#986=IFCDIRECTION((0.,0.,1.));
#987=IFCDIRECTION((1.,0.,0.));
#988=IFCDIRECTION((0.,0.,1.));
#989=IFCEXTRUDEDAREASOLID(#990,#994,#998,12000.);
#990=IFCRECTANGLEPROFILEDEF(.AREA.,$,#991,200.,200.);
#991=IFCAXIS2PLACEMENT2D(#992,#993);
#992=IFCCARTESIANPOINT((-100.,0.));
#993=IFCDIRECTION((1.,0.));
#994=IFCAXIS2PLACEMENT3D(#995,#996,#997);
#995=IFCCARTESIANPOINT((0.,0.,0.));
#996=IFCDIRECTION((0.,1.,0.));
#997=IFCDIRECTION((0.,0.,1.));
#998=IFCDIRECTION((0.,0.,1.));
#999=IFCEXTRUDEDAREASOLID(#1000,#1004,#1008,12000.);
#1000=IFCRECTANGLEPROFILEDEF(.AREA.,$,#1001,200.,200.);
#1001=IFCAXIS2PLACEMENT2D(#1002,#1003);
#1002=IFCCARTESIANPOINT((-100.,0.));
#1003=IFCDIRECTION((1.,0.));
#1004=IFCAXIS2PLACEMENT3D(#1005,#1006,#1007);
#1005=IFCCARTESIANPOINT((0.,0.,0.));
#1006=IFCDIRECTION((0.,1.,0.));
#1007=IFCDIRECTION((0.,0.,1.));
#1008=IFCDIRECTION((0.,0.,1.));
#1009=IFCEXTRUDEDAREASOLID(#1010,#1014,#1018,12000.);
#1010=IFCRECTANGLEPROFILEDEF(.AREA.,$,#1011,200.,200.);
#1011=IFCAXIS2PLACEMENT2D(#1012,#1013);
#1012=IFCCARTESIANPOINT((-100.,0.));
#1013=IFCDIRECTION((1.,0.));
#1014=IFCAXIS2PLACEMENT3D(#1015,#1016,#1017);
#1015=IFCCARTESIANPOINT((0.,0.,0.));
#1016=IFCDIRECTION((0.,1.,0.));
#1017=IFCDIRECTION((0.,0.,1.));
#1018=IFCDIRECTION((0.,0.,1.));
#1019=IFCEXTRUDEDAREASOLID(#1020,#1024,#1028,12000.);
#1020=IFCRECTANGLEPROFILEDEF(.AREA.,$,#1021,200.,200.);
#1021=IFCAXIS2PLACEMENT2D(#1022,#1023);
#1022=IFCCARTESIANPOINT((-100.,0.));
#1023=IFCDIRECTION((1.,0.));
#1024=IFCAXIS2PLACEMENT3D(#1025,#1026,#1027);
#1025=IFCCARTESIANPOINT((0.,0.,0.));
#1026=IFCDIRECTION((0.,1.,0.));
#1027=IFCDIRECTION((0.,0.,1.));
#1028=IFCDIRECTION((0.,0.,1.));
#1029=IFCEXTRUDEDAREASOLID(#1030,#1034,#1038,16000.);
#1030=IFCRECTANGLEPROFILEDEF(.AREA.,$,#1031,200.,200.);
#1031=IFCAXIS2PLACEMENT2D(#1032,#1033);
#1032=IFCCARTESIANPOINT((-100.,0.));
#1033=IFCDIRECTION((1.,0.));
#1034=IFCAXIS2PLACEMENT3D(#1035,#1036,#1037);
#1035=IFCCARTESIANPOINT((0.,0.,0.));
#1036=IFCDIRECTION((0.,1.,0.));
#1037=IFCDIRECTION((0.,0.,1.));
#1038=IFCDIRECTION((0.,0.,1.));
#1039=IFCEXTRUDEDAREASOLID(#1040,#1044,#1048,12000.);
#1040=IFCRECTANGLEPROFILEDEF(.AREA.,$,#1041,200.,200.);
#1041=IFCAXIS2PLACEMENT2D(#1042,#1043);
#1042=IFCCARTESIANPOINT((-100.,0.));
#1043=IFCDIRECTION((1.,0.));
#1044=IFCAXIS2PLACEMENT3D(#1045,#1046,#1047);
#1045=IFCCARTESIANPOINT((0.,0.,0.));
#1046=IFCDIRECTION((0.,1.,0.));
#1047=IFCDIRECTION((0.,0.,1.));
#1048=IFCDIRECTION((0.,0.,1.));
#1049=IFCEXTRUDEDAREASOLID(#1050,#1054,#1058,16000.);
#1050=IFCRECTANGLEPROFILEDEF(.AREA.,$,#1051,200.,200.);
#1051=IFCAXIS2PLACEMENT2D(#1052,#1053);
#1052=IFCCARTESIANPOINT((-100.,0.));
#1053=IFCDIRECTION((1.,0.));
#1054=IFCAXIS2PLACEMENT3D(#1055,#1056,#1057);
#1055=IFCCARTESIANPOINT((0.,0.,0.));
#1056=IFCDIRECTION((0.,1.,0.));
#1057=IFCDIRECTION((0.,0.,1.));
#1058=IFCDIRECTION((0.,0.,1.));
#1059=IFCEXTRUDEDAREASOLID(#1060,#1064,#1068,16000.);
#1060=IFCRECTANGLEPROFILEDEF(.AREA.,$,#1061,200.,200.);
#1061=IFCAXIS2PLACEMENT2D(#1062,#1063);
#1062=IFCCARTESIANPOINT((-100.,0.));
#1063=IFCDIRECTION((1.,0.));
#1064=IFCAXIS2PLACEMENT3D(#1065,#1066,#1067);
#1065=IFCCARTESIANPOINT((0.,0.,0.));
#1066=IFCDIRECTION((0.,1.,0.));
#1067=IFCDIRECTION((0.,0.,1.));
#1068=IFCDIRECTION((0.,0.,1.));
#1069=IFCEXTRUDEDAREASOLID(#1070,#1074,#1078,16000.);
#1070=IFCRECTANGLEPROFILEDEF(.AREA.,$,#1071,200.,200.);
#1071=IFCAXIS2PLACEMENT2D(#1072,#1073);
#1072=IFCCARTESIANPOINT((-100.,0.));
#1073=IFCDIRECTION((1.,0.));
#1074=IFCAXIS2PLACEMENT3D(#1075,#1076,#1077);
#1075=IFCCARTESIANPOINT((0.,0.,0.));
#1076=IFCDIRECTION((0.,1.,0.));
#1077=IFCDIRECTION((0.,0.,1.));
#1078=IFCDIRECTION((0.,0.,1.));
#1079=IFCEXTRUDEDAREASOLID(#1080,#1084,#1088,16000.);
#1080=IFCRECTANGLEPROFILEDEF(.AREA.,$,#1081,200.,200.);
#1081=IFCAXIS2PLACEMENT2D(#1082,#1083);
#1082=IFCCARTESIANPOINT((-100.,0.));
#1083=IFCDIRECTION((1.,0.));
#1084=IFCAXIS2PLACEMENT3D(#1085,#1086,#1087);
#1085=IFCCARTESIANPOINT((0.,0.,0.));
#1086=IFCDIRECTION((0.,1.,0.));
#1087=IFCDIRECTION((0.,0.,1.));
#1088=IFCDIRECTION((0.,0.,1.));
#1089=IFCPERSONANDORGANIZATION(#1090,#1091,$);
#1090=IFCPERSON($,'Family name','Given name',$,$,$,$,$);
#1091=IFCORGANIZATION($,'Name',$,$,$);
#1092=IFCAPPLICATION(#1093,'Version','Name','Identifier');
#1093=IFCORGANIZATION($,'Name',$,$,$);
#1094=IFCAXIS2PLACEMENT3D(#1095,#1096,#1097);
#1095=IFCCARTESIANPOINT((0.,0.,0.));
#1096=IFCDIRECTION((0.,0.,1.));
#1097=IFCDIRECTION((1.,0.,0.));
#1098=IFCDIRECTION((0.,1.));
#1099=IFCMATERIAL('Structural Concrete',$,$);
#1100=IFCMATERIAL('Structural Concrete',$,$);
#1101=IFCMATERIAL('Structural Concrete',$,$);
#1102=IFCMATERIAL('Structural Concrete',$,$);
#1103=IFCMATERIAL('Structural Concrete',$,$);
#1104=IFCMATERIAL('Structural Concrete',$,$);
#1105=IFCMATERIAL('Structural Concrete',$,$);
#1106=IFCMATERIAL('Structural Concrete',$,$);
#1107=IFCMATERIAL('Structural Concrete',$,$);
#1108=IFCMATERIAL('Structural Concrete',$,$);
#1109=IFCMATERIAL('Structural Concrete',$,$);
#1110=IFCMATERIAL('Structural Concrete',$,$);
#1111=IFCMATERIAL('Structural Concrete',$,$);
#1112=IFCMATERIAL('Structural Concrete',$,$);
#1113=IFCMATERIAL('Structural Concrete',$,$);
#1114=IFCMATERIAL('Structural Concrete',$,$);
#1115=IFCMATERIAL('Structural Concrete',$,$);
#1116=IFCMATERIAL('Structural Concrete',$,$);
#1117=IFCMATERIAL('Structural Concrete',$,$);
#1118=IFCMATERIAL('Structural Concrete',$,$);
#1119=IFCMATERIAL('Structural Concrete',$,$);
#1120=IFCMATERIAL('Structural Concrete',$,$);
#1121=IFCMATERIAL('Structural Concrete',$,$);
#1122=IFCMATERIAL('Structural Concrete',$,$);
#1123=IFCMATERIAL('Structural Concrete',$,$);
#1124=IFCMATERIAL('Structural Concrete',$,$);
#1125=IFCMATERIAL('Structural Concrete',$,$);
#1126=IFCMATERIAL('Structural Concrete',$,$);
#1127=IFCMATERIAL('Structural Concrete',$,$);
#1128=IFCMATERIAL('Structural Concrete',$,$);
#1129=IFCMATERIAL('Structural Concrete',$,$);
#1130=IFCMATERIAL('Structural Concrete',$,$);
#1131=IFCMATERIAL('Structural Concrete',$,$);
#1132=IFCMATERIAL('Structural Concrete',$,$);
#1133=IFCMATERIAL('Structural Concrete',$,$);
ENDSEC;
END-ISO-10303-21;

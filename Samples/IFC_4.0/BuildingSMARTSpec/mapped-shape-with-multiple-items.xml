<IfcProject type="IfcProject" globalId="3b7266e8-127d-4e31-a10d-59cd1eb671a7">
  <ownerHistory type="IfcOwnerHistory" globalId="bab693aa-c80a-4ea0-ab1b-d634fb5bc141" changeAction="ADDED">
    <owningUser type="IfcPersonAndOrganization" globalId="c78733ad-0c11-4647-9af1-981c20b89e28">
      <thePerson type="IfcPerson" globalId="c7202b55-7f2c-4153-b3ef-f09faaf0dfee">
        <familyName type="IfcLabel" value="Liebich"/>
        <givenName type="IfcLabel" value="Thomas"/>
      </thePerson>
      <theOrganization type="IfcOrganization" globalId="e9e83d22-426a-47e8-a2dc-20e63dbb86c3">
        <name type="IfcLabel" value="buildingSMART International"/>
      </theOrganization>
    </owningUser>
    <owningApplication>
      <applicationDeveloper>e9e83d22-426a-47e8-a2dc-20e63dbb86c3</applicationDeveloper>
      <version type="IfcLabel" value="1.0"/>
      <applicationFullName type="IfcLabel" value="IFC text editor"/>
      <applicationIdentifier type="IfcIdentifier" value="ifcTE"/>
    </owningApplication>
    <lastModifiedDate type="IfcTimeStamp" value="1320688800"/>
    <creationDate type="IfcTimeStamp" value="1320688800"/>
  </ownerHistory>
  <name type="IfcLabel" value="proxy with multiple transformed representation"/>
  <declares>
    <IfcRelDeclares type="IfcRelDeclares" globalId="531d62ec-ff37-49ca-aefa-347a6e676d73">
      <relatedDefinitions>
        <IfcDefinitionSelect type="IfcBuildingElementProxyType" globalId="84077810-acbd-43d7-94d3-f5a418622fbb" predefinedType="NOTDEFINED">
          <name type="IfcLabel" value="Type-P"/>
          <hasContext>
            <IfcRelDeclares>531d62ec-ff37-49ca-aefa-347a6e676d73</IfcRelDeclares>
          </hasContext>
          <representationMaps>
            <IfcRepresentationMap type="IfcRepresentationMap">
              <mappingOrigin type="IfcAxis2Placement3D">
                <location type="IfcCartesianPoint">
                  <coordinates>
                    <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                    <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                    <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                  </coordinates>
                </location>
              </mappingOrigin>
              <mappedRepresentation type="IfcShapeRepresentation">
                <contextOfItems type="IfcGeometricRepresentationSubContext" globalId="274f584e-9f82-442f-85aa-2aa118893773" targetView="MODEL_VIEW">
                  <contextIdentifier type="IfcLabel" value="Body"/>
                  <contextType type="IfcLabel" value="Model"/>
                </contextOfItems>
                <representationIdentifier type="IfcLabel" value="Body"/>
                <representationType type="IfcLabel" value="SweptSolid"/>
                <items>
                  <IfcRepresentationItem type="IfcExtrudedAreaSolid">
                    <sweptArea type="IfcRectangleProfileDef" profileType="AREA">
                      <profileName type="IfcLabel" value="1m x 1m rectangle"/>
                      <xDim type="IfcPositiveLengthMeasure" value="1000.0"/>
                      <yDim type="IfcPositiveLengthMeasure" value="1000.0"/>
                    </sweptArea>
                    <extrudedDirection type="IfcDirection">
                      <directionRatios>
                        <IfcReal type="IfcReal" value="0.0"/>
                        <IfcReal type="IfcReal" value="0.0"/>
                        <IfcReal type="IfcReal" value="1.0"/>
                      </directionRatios>
                    </extrudedDirection>
                    <depth type="IfcPositiveLengthMeasure" value="2000.0"/>
                  </IfcRepresentationItem>
                </items>
              </mappedRepresentation>
            </IfcRepresentationMap>
          </representationMaps>
        </IfcDefinitionSelect>
      </relatedDefinitions>
    </IfcRelDeclares>
  </declares>
  <isDecomposedBy type="IfcRelAggregates" globalId="a22f491f-f885-4116-8813-24d281d90c1d">
    <relatedObjects>
      <IfcObjectDefinition type="IfcBuilding" globalId="8f323372-d7b4-4d74-85d2-001ba786c219" compositionType="ELEMENT">
        <name type="IfcLabel" value="Test Building"/>
        <objectPlacement type="IfcLocalPlacement" globalId="3f17c5d7-8313-4a7b-a1b4-f6c20505f3fc">
          <relativePlacement type="IfcAxis2Placement3D">
            <location type="IfcCartesianPoint">
              <coordinates>
                <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
              </coordinates>
            </location>
          </relativePlacement>
        </objectPlacement>
        <containsElements>
          <IfcRelContainedInSpatialStructure type="IfcRelContainedInSpatialStructure" globalId="9dc7b8ee-7617-4022-8378-5b8ade78f5fc">
            <name type="IfcLabel" value="Physical model"/>
            <relatedElements>
              <IfcProduct type="IfcBuildingElementProxy" globalId="6e779871-965f-4c83-a22f-9969c19db132">
                <description type="IfcText" value="sample proxy"/>
                <name type="IfcLabel" value="P-1"/>
                <isTypedBy>
                  <IfcRelDefinesByType type="IfcRelDefinesByType" globalId="0d6c6fb3-bfba-4022-8689-8807c0c7fd9f">
                    <relatingType>84077810-acbd-43d7-94d3-f5a418622fbb</relatingType>
                  </IfcRelDefinesByType>
                </isTypedBy>
                <objectPlacement type="IfcLocalPlacement" globalId="ac458468-1fe6-4f07-b329-d6d6412d4d4b">
                  <relativePlacement type="IfcAxis2Placement3D">
                    <location type="IfcCartesianPoint">
                      <coordinates>
                        <IfcLengthMeasure type="IfcLengthMeasure" value="1000.0"/>
                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                      </coordinates>
                    </location>
                  </relativePlacement>
                  <placementRelTo>3f17c5d7-8313-4a7b-a1b4-f6c20505f3fc</placementRelTo>
                </objectPlacement>
                <representation type="IfcProductDefinitionShape">
                  <representations>
                    <IfcRepresentation type="IfcShapeRepresentation">
                      <contextOfItems>274f584e-9f82-442f-85aa-2aa118893773</contextOfItems>
                      <representationIdentifier type="IfcLabel" value="Body"/>
                      <representationType type="IfcLabel" value="MappedRepresentation"/>
                      <items>
                        <IfcRepresentationItem type="IfcMappedItem">
                          <mappingSource type="IfcRepresentationMap">
                            <mappingOrigin type="IfcAxis2Placement3D">
                              <location type="IfcCartesianPoint">
                                <coordinates>
                                  <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                  <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                  <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                </coordinates>
                              </location>
                            </mappingOrigin>
                            <mappedRepresentation type="IfcShapeRepresentation">
                              <contextOfItems>274f584e-9f82-442f-85aa-2aa118893773</contextOfItems>
                              <representationIdentifier type="IfcLabel" value="Body"/>
                              <representationType type="IfcLabel" value="SweptSolid"/>
                              <items>
                                <IfcRepresentationItem type="IfcExtrudedAreaSolid">
                                  <sweptArea type="IfcRectangleProfileDef" profileType="AREA">
                                    <profileName type="IfcLabel" value="1m x 1m rectangle"/>
                                    <xDim type="IfcPositiveLengthMeasure" value="1000.0"/>
                                    <yDim type="IfcPositiveLengthMeasure" value="1000.0"/>
                                  </sweptArea>
                                  <extrudedDirection type="IfcDirection">
                                    <directionRatios>
                                      <IfcReal type="IfcReal" value="0.0"/>
                                      <IfcReal type="IfcReal" value="0.0"/>
                                      <IfcReal type="IfcReal" value="1.0"/>
                                    </directionRatios>
                                  </extrudedDirection>
                                  <depth type="IfcPositiveLengthMeasure" value="2000.0"/>
                                </IfcRepresentationItem>
                              </items>
                            </mappedRepresentation>
                          </mappingSource>
                          <mappingTarget type="IfcCartesianTransformationOperator3DnonUniform">
                            <scl2 type="IfcReal" value="0.5"/>
                            <scl3 type="IfcReal" value="1.0"/>
                            <axis1 type="IfcDirection">
                              <directionRatios>
                                <IfcReal type="IfcReal" value="1.0"/>
                                <IfcReal type="IfcReal" value="1.0"/>
                                <IfcReal type="IfcReal" value="0.0"/>
                              </directionRatios>
                            </axis1>
                            <axis2 type="IfcDirection">
                              <directionRatios>
                                <IfcReal type="IfcReal" value="-1.0"/>
                                <IfcReal type="IfcReal" value="1.0"/>
                                <IfcReal type="IfcReal" value="0.0"/>
                              </directionRatios>
                            </axis2>
                            <localOrigin type="IfcCartesianPoint">
                              <coordinates>
                                <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                <IfcLengthMeasure type="IfcLengthMeasure" value="1000.0"/>
                                <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                              </coordinates>
                            </localOrigin>
                            <scale type="IfcReal" value="0.5"/>
                            <axis3 type="IfcDirection">
                              <directionRatios>
                                <IfcReal type="IfcReal" value="0.0"/>
                                <IfcReal type="IfcReal" value="0.0"/>
                                <IfcReal type="IfcReal" value="1.0"/>
                              </directionRatios>
                            </axis3>
                            <scale2 type="IfcReal" value="0.5"/>
                            <scale3 type="IfcReal" value="1.0"/>
                          </mappingTarget>
                        </IfcRepresentationItem>
                        <IfcRepresentationItem type="IfcMappedItem">
                          <mappingSource type="IfcRepresentationMap">
                            <mappingOrigin type="IfcAxis2Placement3D">
                              <location type="IfcCartesianPoint">
                                <coordinates>
                                  <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                  <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                  <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                </coordinates>
                              </location>
                            </mappingOrigin>
                            <mappedRepresentation type="IfcShapeRepresentation">
                              <contextOfItems>274f584e-9f82-442f-85aa-2aa118893773</contextOfItems>
                              <representationIdentifier type="IfcLabel" value="Body"/>
                              <representationType type="IfcLabel" value="SweptSolid"/>
                              <items>
                                <IfcRepresentationItem type="IfcExtrudedAreaSolid">
                                  <sweptArea type="IfcRectangleProfileDef" profileType="AREA">
                                    <profileName type="IfcLabel" value="1m x 1m rectangle"/>
                                    <xDim type="IfcPositiveLengthMeasure" value="1000.0"/>
                                    <yDim type="IfcPositiveLengthMeasure" value="1000.0"/>
                                  </sweptArea>
                                  <extrudedDirection type="IfcDirection">
                                    <directionRatios>
                                      <IfcReal type="IfcReal" value="0.0"/>
                                      <IfcReal type="IfcReal" value="0.0"/>
                                      <IfcReal type="IfcReal" value="1.0"/>
                                    </directionRatios>
                                  </extrudedDirection>
                                  <depth type="IfcPositiveLengthMeasure" value="2000.0"/>
                                </IfcRepresentationItem>
                              </items>
                            </mappedRepresentation>
                          </mappingSource>
                          <mappingTarget type="IfcCartesianTransformationOperator3DnonUniform">
                            <scl2 type="IfcReal" value="0.5"/>
                            <scl3 type="IfcReal" value="1.0"/>
                            <axis1 type="IfcDirection">
                              <directionRatios>
                                <IfcReal type="IfcReal" value="1.0"/>
                                <IfcReal type="IfcReal" value="1.0"/>
                                <IfcReal type="IfcReal" value="0.0"/>
                              </directionRatios>
                            </axis1>
                            <axis2 type="IfcDirection">
                              <directionRatios>
                                <IfcReal type="IfcReal" value="-1.0"/>
                                <IfcReal type="IfcReal" value="1.0"/>
                                <IfcReal type="IfcReal" value="0.0"/>
                              </directionRatios>
                            </axis2>
                            <localOrigin type="IfcCartesianPoint">
                              <coordinates>
                                <IfcLengthMeasure type="IfcLengthMeasure" value="1000.0"/>
                                <IfcLengthMeasure type="IfcLengthMeasure" value="1000.0"/>
                                <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                              </coordinates>
                            </localOrigin>
                            <scale type="IfcReal" value="0.5"/>
                            <axis3 type="IfcDirection">
                              <directionRatios>
                                <IfcReal type="IfcReal" value="0.0"/>
                                <IfcReal type="IfcReal" value="0.0"/>
                                <IfcReal type="IfcReal" value="1.0"/>
                              </directionRatios>
                            </axis3>
                            <scale2 type="IfcReal" value="0.5"/>
                            <scale3 type="IfcReal" value="1.0"/>
                          </mappingTarget>
                        </IfcRepresentationItem>
                        <IfcRepresentationItem type="IfcMappedItem">
                          <mappingSource type="IfcRepresentationMap">
                            <mappingOrigin type="IfcAxis2Placement3D">
                              <location type="IfcCartesianPoint">
                                <coordinates>
                                  <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                  <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                  <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                </coordinates>
                              </location>
                            </mappingOrigin>
                            <mappedRepresentation type="IfcShapeRepresentation">
                              <contextOfItems>274f584e-9f82-442f-85aa-2aa118893773</contextOfItems>
                              <representationIdentifier type="IfcLabel" value="Body"/>
                              <representationType type="IfcLabel" value="SweptSolid"/>
                              <items>
                                <IfcRepresentationItem type="IfcExtrudedAreaSolid">
                                  <sweptArea type="IfcRectangleProfileDef" profileType="AREA">
                                    <profileName type="IfcLabel" value="1m x 1m rectangle"/>
                                    <xDim type="IfcPositiveLengthMeasure" value="1000.0"/>
                                    <yDim type="IfcPositiveLengthMeasure" value="1000.0"/>
                                  </sweptArea>
                                  <extrudedDirection type="IfcDirection">
                                    <directionRatios>
                                      <IfcReal type="IfcReal" value="0.0"/>
                                      <IfcReal type="IfcReal" value="0.0"/>
                                      <IfcReal type="IfcReal" value="1.0"/>
                                    </directionRatios>
                                  </extrudedDirection>
                                  <depth type="IfcPositiveLengthMeasure" value="2000.0"/>
                                </IfcRepresentationItem>
                              </items>
                            </mappedRepresentation>
                          </mappingSource>
                          <mappingTarget type="IfcCartesianTransformationOperator3DnonUniform">
                            <scl2 type="IfcReal" value="0.5"/>
                            <scl3 type="IfcReal" value="1.0"/>
                            <axis1 type="IfcDirection">
                              <directionRatios>
                                <IfcReal type="IfcReal" value="1.0"/>
                                <IfcReal type="IfcReal" value="1.0"/>
                                <IfcReal type="IfcReal" value="0.0"/>
                              </directionRatios>
                            </axis1>
                            <axis2 type="IfcDirection">
                              <directionRatios>
                                <IfcReal type="IfcReal" value="-1.0"/>
                                <IfcReal type="IfcReal" value="1.0"/>
                                <IfcReal type="IfcReal" value="0.0"/>
                              </directionRatios>
                            </axis2>
                            <localOrigin type="IfcCartesianPoint">
                              <coordinates>
                                <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                              </coordinates>
                            </localOrigin>
                            <scale type="IfcReal" value="0.5"/>
                            <axis3 type="IfcDirection">
                              <directionRatios>
                                <IfcReal type="IfcReal" value="0.0"/>
                                <IfcReal type="IfcReal" value="0.0"/>
                                <IfcReal type="IfcReal" value="1.0"/>
                              </directionRatios>
                            </axis3>
                            <scale2 type="IfcReal" value="0.5"/>
                            <scale3 type="IfcReal" value="1.0"/>
                          </mappingTarget>
                        </IfcRepresentationItem>
                        <IfcRepresentationItem type="IfcMappedItem">
                          <mappingSource type="IfcRepresentationMap">
                            <mappingOrigin type="IfcAxis2Placement3D">
                              <location type="IfcCartesianPoint">
                                <coordinates>
                                  <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                  <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                  <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                </coordinates>
                              </location>
                            </mappingOrigin>
                            <mappedRepresentation type="IfcShapeRepresentation">
                              <contextOfItems>274f584e-9f82-442f-85aa-2aa118893773</contextOfItems>
                              <representationIdentifier type="IfcLabel" value="Body"/>
                              <representationType type="IfcLabel" value="SweptSolid"/>
                              <items>
                                <IfcRepresentationItem type="IfcExtrudedAreaSolid">
                                  <sweptArea type="IfcRectangleProfileDef" profileType="AREA">
                                    <profileName type="IfcLabel" value="1m x 1m rectangle"/>
                                    <xDim type="IfcPositiveLengthMeasure" value="1000.0"/>
                                    <yDim type="IfcPositiveLengthMeasure" value="1000.0"/>
                                  </sweptArea>
                                  <extrudedDirection type="IfcDirection">
                                    <directionRatios>
                                      <IfcReal type="IfcReal" value="0.0"/>
                                      <IfcReal type="IfcReal" value="0.0"/>
                                      <IfcReal type="IfcReal" value="1.0"/>
                                    </directionRatios>
                                  </extrudedDirection>
                                  <depth type="IfcPositiveLengthMeasure" value="2000.0"/>
                                </IfcRepresentationItem>
                              </items>
                            </mappedRepresentation>
                          </mappingSource>
                          <mappingTarget type="IfcCartesianTransformationOperator3DnonUniform">
                            <scl2 type="IfcReal" value="0.5"/>
                            <scl3 type="IfcReal" value="1.0"/>
                            <axis1 type="IfcDirection">
                              <directionRatios>
                                <IfcReal type="IfcReal" value="1.0"/>
                                <IfcReal type="IfcReal" value="1.0"/>
                                <IfcReal type="IfcReal" value="0.0"/>
                              </directionRatios>
                            </axis1>
                            <axis2 type="IfcDirection">
                              <directionRatios>
                                <IfcReal type="IfcReal" value="-1.0"/>
                                <IfcReal type="IfcReal" value="1.0"/>
                                <IfcReal type="IfcReal" value="0.0"/>
                              </directionRatios>
                            </axis2>
                            <localOrigin type="IfcCartesianPoint">
                              <coordinates>
                                <IfcLengthMeasure type="IfcLengthMeasure" value="1000.0"/>
                                <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                              </coordinates>
                            </localOrigin>
                            <scale type="IfcReal" value="0.5"/>
                            <axis3 type="IfcDirection">
                              <directionRatios>
                                <IfcReal type="IfcReal" value="0.0"/>
                                <IfcReal type="IfcReal" value="0.0"/>
                                <IfcReal type="IfcReal" value="1.0"/>
                              </directionRatios>
                            </axis3>
                            <scale2 type="IfcReal" value="0.5"/>
                            <scale3 type="IfcReal" value="1.0"/>
                          </mappingTarget>
                        </IfcRepresentationItem>
                      </items>
                    </IfcRepresentation>
                  </representations>
                </representation>
              </IfcProduct>
            </relatedElements>
          </IfcRelContainedInSpatialStructure>
        </containsElements>
      </IfcObjectDefinition>
    </relatedObjects>
  </isDecomposedBy>
  <representationContexts>
    <IfcRepresentationContext type="IfcGeometricRepresentationContext" globalId="7ce5d6cb-38d5-469d-b2fa-84a4f1b6a1dc">
      <worldCoordinateSystem type="IfcAxis2Placement3D">
        <location type="IfcCartesianPoint">
          <coordinates>
            <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
            <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
            <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
          </coordinates>
        </location>
      </worldCoordinateSystem>
      <contextType type="IfcLabel" value="Model"/>
      <coordinateSpaceDimension type="IfcDimensionCount" value="3"/>
      <precision type="IfcReal" value="1.0E-5"/>
      <hasSubContexts>
        <IfcGeometricRepresentationSubContext>274f584e-9f82-442f-85aa-2aa118893773</IfcGeometricRepresentationSubContext>
      </hasSubContexts>
    </IfcRepresentationContext>
  </representationContexts>
  <unitsInContext>
    <units>
      <IfcUnit type="IfcSIUnit" globalId="36a6340a-416a-444b-8667-328af787736f">
        <dimensions lengthExponent="1" massExponent="0" timeExponent="0" electricCurrentExponent="0" thermodynamicTemperatureExponent="0" amountOfSubstanceExponent="0" luminousIntensityExponent="0"/>
        <unitType>LENGTHUNIT</unitType>
        <prefix>MILLI</prefix>
        <name>METRE</name>
      </IfcUnit>
      <IfcUnit type="IfcConversionBasedUnit" globalId="f73a6d5f-ddcf-46d0-a114-a47ac1178ba2">
        <dimensions lengthExponent="0" massExponent="0" timeExponent="0" electricCurrentExponent="0" thermodynamicTemperatureExponent="0" amountOfSubstanceExponent="0" luminousIntensityExponent="0"/>
        <unitType>PLANEANGLEUNIT</unitType>
        <name>degree</name>
        <conversionFactor type="IfcMeasureWithUnit">
          <valueComponent type="IfcPlaneAngleMeasure" value="0.017453293"/>
          <unitComponent type="IfcSIUnit" globalId="0d77dbd4-9dc6-46f9-9175-4e65a9730ad0">
            <dimensions lengthExponent="0" massExponent="0" timeExponent="0" electricCurrentExponent="0" thermodynamicTemperatureExponent="0" amountOfSubstanceExponent="0" luminousIntensityExponent="0"/>
            <unitType>PLANEANGLEUNIT</unitType>
            <name>RADIAN</name>
          </unitComponent>
        </conversionFactor>
      </IfcUnit>
    </units>
  </unitsInContext>
</IfcProject>

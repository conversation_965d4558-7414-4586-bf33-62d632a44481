ISO-10303-21;
HEADER;
FILE_DESCRIPTION(('ViewDefinition [CoordinationView]'),'2;1');
FILE_NAME('','2020-10-30T00:18:58',(),(),'IfcOpenShell 0.6.0b0','IfcOpenShell 0.6.0b0','');
FILE_SCHEMA(('IFC4'));
ENDSEC;
DATA;
#1=IFCPROJECT('0xScRe4drECQ4DMSqUjd6d',#7,'proxy with multiple transformed representation',$,$,$,$,(#8),#14);
#2=IFCBUILDING('2FCZDorxHDT8NI01kdXi8P',$,'Test Building',$,$,#19,$,$,.ELEMENT.,$,$,$);
#3=IFCBUILDINGELEMENTPROXY('1kTvXnbbzCWw8lcMd1dR4o',$,'P-1','sample proxy',$,#22,#28,$,$);
#4=IFCBUILDINGELEMENTPROXYTYPE('241tWGhBr3rvJJzQGOOY_x',$,'Type-P',$,$,$,(#29),$,$,.NOTDEFINED.);
#5=IFCSHAPEREPRESENTATION(#9,'Body','MappedRepresentation',(#32,#41,#50,#59));
#6=IFCSHAPEREPRESENTATION(#9,'Body','SweptSolid',(#68));
#7=IFCOWNERHISTORY(#71,#74,$,.ADDED.,1320688800,$,$,1320688800);
#8=IFCGEOMETRICREPRESENTATIONCONTEXT($,'Model',3,1.E-05,#76,$);
#9=IFCGEOMETRICREPRESENTATIONSUBCONTEXT('Body','Model',*,*,*,*,#8,$,.MODEL_VIEW.,$);
#10=IFCRELAGGREGATES('2YBqaV_8L15eWJ9DA1sGmT',$,$,$,#1,(#2));
#11=IFCRELCONTAINEDINSPATIALSTRUCTURE('2TnxZkTXT08eDuMuhUUFNy',$,'Physical model',$,(#3),#2);
#12=IFCRELDEFINESBYTYPE('0DR6_plxf08eQ9Y0V0n$sV',$,$,$,(#3),#4);
#13=IFCRELDECLARES('1J7MBi$pT9ogxwD7fkPsrp',$,$,$,#1,(#4));
#14=IFCUNITASSIGNMENT((#15,#16));
#15=IFCSIUNIT(*,.LENGTHUNIT.,.MILLI.,.METRE.);
#16=IFCCONVERSIONBASEDUNIT($,.PLANEANGLEUNIT.,'degree',#17);
#17=IFCMEASUREWITHUNIT(IFCPLANEANGLEMEASURE(0.017453293),#18);
#18=IFCSIUNIT(*,.PLANEANGLEUNIT.,$,.RADIAN.);
#19=IFCLOCALPLACEMENT($,#20);
#20=IFCAXIS2PLACEMENT3D(#21,$,$);
#21=IFCCARTESIANPOINT((0.,0.,0.));
#22=IFCLOCALPLACEMENT(#23,#26);
#23=IFCLOCALPLACEMENT($,#24);
#24=IFCAXIS2PLACEMENT3D(#25,$,$);
#25=IFCCARTESIANPOINT((0.,0.,0.));
#26=IFCAXIS2PLACEMENT3D(#27,$,$);
#27=IFCCARTESIANPOINT((1000.,0.,0.));
#28=IFCPRODUCTDEFINITIONSHAPE($,$,(#5));
#29=IFCREPRESENTATIONMAP(#30,#6);
#30=IFCAXIS2PLACEMENT3D(#31,$,$);
#31=IFCCARTESIANPOINT((0.,0.,0.));
#32=IFCMAPPEDITEM(#33,#36);
#33=IFCREPRESENTATIONMAP(#34,#6);
#34=IFCAXIS2PLACEMENT3D(#35,$,$);
#35=IFCCARTESIANPOINT((0.,0.,0.));
#36=IFCCARTESIANTRANSFORMATIONOPERATOR3DNONUNIFORM(#37,#38,#39,0.5,#40,0.5,1.);
#37=IFCDIRECTION((1.,1.,0.));
#38=IFCDIRECTION((-1.,1.,0.));
#39=IFCCARTESIANPOINT((0.,0.,0.));
#40=IFCDIRECTION((0.,0.,1.));
#41=IFCMAPPEDITEM(#42,#45);
#42=IFCREPRESENTATIONMAP(#43,#6);
#43=IFCAXIS2PLACEMENT3D(#44,$,$);
#44=IFCCARTESIANPOINT((0.,0.,0.));
#45=IFCCARTESIANTRANSFORMATIONOPERATOR3DNONUNIFORM(#46,#47,#48,0.5,#49,0.5,1.);
#46=IFCDIRECTION((1.,1.,0.));
#47=IFCDIRECTION((-1.,1.,0.));
#48=IFCCARTESIANPOINT((1000.,0.,0.));
#49=IFCDIRECTION((0.,0.,1.));
#50=IFCMAPPEDITEM(#51,#54);
#51=IFCREPRESENTATIONMAP(#52,#6);
#52=IFCAXIS2PLACEMENT3D(#53,$,$);
#53=IFCCARTESIANPOINT((0.,0.,0.));
#54=IFCCARTESIANTRANSFORMATIONOPERATOR3DNONUNIFORM(#55,#56,#57,0.5,#58,0.5,1.);
#55=IFCDIRECTION((1.,1.,0.));
#56=IFCDIRECTION((-1.,1.,0.));
#57=IFCCARTESIANPOINT((0.,1000.,0.));
#58=IFCDIRECTION((0.,0.,1.));
#59=IFCMAPPEDITEM(#60,#63);
#60=IFCREPRESENTATIONMAP(#61,#6);
#61=IFCAXIS2PLACEMENT3D(#62,$,$);
#62=IFCCARTESIANPOINT((0.,0.,0.));
#63=IFCCARTESIANTRANSFORMATIONOPERATOR3DNONUNIFORM(#64,#65,#66,0.5,#67,0.5,1.);
#64=IFCDIRECTION((1.,1.,0.));
#65=IFCDIRECTION((-1.,1.,0.));
#66=IFCCARTESIANPOINT((1000.,1000.,0.));
#67=IFCDIRECTION((0.,0.,1.));
#68=IFCEXTRUDEDAREASOLID(#69,$,#70,2000.);
#69=IFCRECTANGLEPROFILEDEF(.AREA.,'1m x 1m rectangle',$,1000.,1000.);
#70=IFCDIRECTION((0.,0.,1.));
#71=IFCPERSONANDORGANIZATION(#72,#73,$);
#72=IFCPERSON($,'Liebich','Thomas',$,$,$,$,$);
#73=IFCORGANIZATION($,'buildingSMART International',$,$,$);
#74=IFCAPPLICATION(#75,'1.0','IFC text editor','ifcTE');
#75=IFCORGANIZATION($,'buildingSMART International',$,$,$);
#76=IFCAXIS2PLACEMENT3D(#77,$,$);
#77=IFCCARTESIANPOINT((0.,0.,0.));
ENDSEC;
END-ISO-10303-21;

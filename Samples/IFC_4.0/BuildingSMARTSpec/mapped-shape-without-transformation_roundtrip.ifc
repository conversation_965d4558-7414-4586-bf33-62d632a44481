ISO-10303-21;
HEADER;
FILE_DESCRIPTION(('ViewDefinition [CoordinationView]'),'2;1');
FILE_NAME('','2020-10-30T00:18:58',(),(),'IfcOpenShell 0.6.0b0','IfcOpenShell 0.6.0b0','');
FILE_SCHEMA(('IFC4'));
ENDSEC;
DATA;
#1=IFCPROJECT('0xScRe4drECQ4DMSqUjd6d',#7,'proxy with mapped representation',$,$,$,$,(#8),#14);
#2=IFCBUILDING('2FCZDorxHDT8NI01kdXi8P',$,'Test Building',$,$,#19,$,$,.ELEMENT.,$,$,$);
#3=IFCBUILDINGELEMENTPROXY('1kTvXnbbzCWw8lcMd1dR4o',$,'P-1','sample proxy',$,#22,#28,$,$);
#4=IFCBUILDINGELEMENTPROXYTYPE('241tWGhBr3rvJJzQGOOY_x',$,'Type-P',$,$,$,(#29),$,$,.NOTDEFINED.);
#5=IFCSHAPEREPRESENTATION(#9,'Body','MappedRepresentation',(#32));
#6=IFCSHAPEREPRESENTATION(#9,'Body','SweptSolid',(#38));
#7=IFCOWNERHISTORY(#41,#44,$,.ADDED.,1320688800,$,$,1320688800);
#8=IFCGEOMETRICREPRESENTATIONCONTEXT($,'Model',3,1.E-05,#46,$);
#9=IFCGEOMETRICREPRESENTATIONSUBCONTEXT('Body','Model',*,*,*,*,#8,$,.MODEL_VIEW.,$);
#10=IFCRELAGGREGATES('2YBqaV_8L15eWJ9DA1sGmT',$,$,$,#1,(#2));
#11=IFCRELCONTAINEDINSPATIALSTRUCTURE('2TnxZkTXT08eDuMuhUUFNy',$,'Physical model',$,(#3),#2);
#12=IFCRELDEFINESBYTYPE('0DR6_plxf08eQ9Y0V0n$sV',$,$,$,(#3),#4);
#13=IFCRELDECLARES('1J7MBi$pT9ogxwD7fkPsrp',$,$,$,#1,(#4));
#14=IFCUNITASSIGNMENT((#15,#16));
#15=IFCSIUNIT(*,.LENGTHUNIT.,.MILLI.,.METRE.);
#16=IFCCONVERSIONBASEDUNIT($,.PLANEANGLEUNIT.,'degree',#17);
#17=IFCMEASUREWITHUNIT(IFCPLANEANGLEMEASURE(0.017453293),#18);
#18=IFCSIUNIT(*,.PLANEANGLEUNIT.,$,.RADIAN.);
#19=IFCLOCALPLACEMENT($,#20);
#20=IFCAXIS2PLACEMENT3D(#21,$,$);
#21=IFCCARTESIANPOINT((0.,0.,0.));
#22=IFCLOCALPLACEMENT(#23,#26);
#23=IFCLOCALPLACEMENT($,#24);
#24=IFCAXIS2PLACEMENT3D(#25,$,$);
#25=IFCCARTESIANPOINT((0.,0.,0.));
#26=IFCAXIS2PLACEMENT3D(#27,$,$);
#27=IFCCARTESIANPOINT((1000.,0.,0.));
#28=IFCPRODUCTDEFINITIONSHAPE($,$,(#5));
#29=IFCREPRESENTATIONMAP(#30,#6);
#30=IFCAXIS2PLACEMENT3D(#31,$,$);
#31=IFCCARTESIANPOINT((0.,0.,0.));
#32=IFCMAPPEDITEM(#33,#36);
#33=IFCREPRESENTATIONMAP(#34,#6);
#34=IFCAXIS2PLACEMENT3D(#35,$,$);
#35=IFCCARTESIANPOINT((0.,0.,0.));
#36=IFCCARTESIANTRANSFORMATIONOPERATOR3D($,$,#37,$,$);
#37=IFCCARTESIANPOINT((0.,0.,0.));
#38=IFCEXTRUDEDAREASOLID(#39,$,#40,2000.);
#39=IFCRECTANGLEPROFILEDEF(.AREA.,'1m x 1m rectangle',$,1000.,1000.);
#40=IFCDIRECTION((0.,0.,1.));
#41=IFCPERSONANDORGANIZATION(#42,#43,$);
#42=IFCPERSON($,'Liebich','Thomas',$,$,$,$,$);
#43=IFCORGANIZATION($,'buildingSMART International',$,$,$);
#44=IFCAPPLICATION(#45,'1.0','IFC text editor','ifcTE');
#45=IFCORGANIZATION($,'buildingSMART International',$,$,$);
#46=IFCAXIS2PLACEMENT3D(#47,$,$);
#47=IFCCARTESIANPOINT((0.,0.,0.));
ENDSEC;
END-ISO-10303-21;

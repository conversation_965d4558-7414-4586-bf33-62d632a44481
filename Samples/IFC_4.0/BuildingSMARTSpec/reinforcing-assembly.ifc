ISO-10303-21;
HEADER;
FILE_DESCRIPTION(('ViewDefinition [notYetAssigned]'),'2;1');
FILE_NAME(
/* name */ '',
/* time_stamp */ '2014-12-09T00:27:55',
/* author */ ('Jon'),
/* organization */ ('Unknown'),
/* preprocessor_version */ 'ggIFC - Exporter by Geometry Gym Pty Ltd',
/* originating_system */ 'ggIFC - Exporter by Geometry Gym Pty Ltd',
/* authorization */ 'None');

FILE_SCHEMA (('IFC4'));
ENDSEC;

DATA;
#1= IFCAPPLICATION(#2,'*******','ggRhinoIFC - Geometry Gym Plug-in for Rhino3d','ggRhinoIFC');
#2= IFCORGANIZATION($,'Geometry Gym Pty Ltd',$,$,$);
#3= IFCPERSONANDORGANIZATION(#4,#5,$);
#4= IFCPERSON('Jon','Jon',$,$,$,$,$,$);
#5= IFCORGANIZATION($,'Geometry Gym Pty Ltd',$,$,$);
#6= IFCOWNERHISTORY(#3,#1,$,.ADDED.,1418084875,$,$,1418084875);
#7= IFCGEOMETRICREPRESENTATIONCONTEXT($,'Model',3,0.0001,#8,#10);
#8= IFCAXIS2PLACEMENT3D(#9,$,$);
#9= IFCCARTESIANPOINT((0.0,0.0,0.0));
#10= IFCDIRECTION((0.0,1.0));
#11= IFCGEOMETRICREPRESENTATIONSUBCONTEXT('Axis','Model',*,*,*,*,#7,$,.MODEL_VIEW.,$);
#12= IFCGEOMETRICREPRESENTATIONSUBCONTEXT('Body','Model',*,*,*,*,#7,$,.MODEL_VIEW.,$);
#13= IFCGEOMETRICREPRESENTATIONCONTEXT($,'Model',3,0.0001,#8,#14);
#14= IFCDIRECTION((0.0,1.0));
#50= IFCBUILDING('3Tfo7nYPr8Hu0x5EhZN5QK',$,'IfcBuilding',$,$,#51,$,$,.ELEMENT.,$,$,#57);
#51= IFCLOCALPLACEMENT($,#52);
#52= IFCAXIS2PLACEMENT3D(#53,$,$);
#53= IFCCARTESIANPOINT((0.0,0.0,0.0));
#54= IFCRELCONTAINEDINSPATIALSTRUCTURE('0zSh6tHRPD_vCWSRMUDLrz',$,'Building','Building Container for Elements',(#224,#244),#50);
#57= IFCPOSTALADDRESS($,$,$,$,$,$,$,'Unknown',$,$);
#100= IFCPROJECT('27mLgo7jv40PPW7Qsrp_wX',#6,'IfcProject',$,$,'IfcProject','',(#13),#101);
#101= IFCUNITASSIGNMENT((#102,#103,#104));
#102= IFCSIUNIT(*,.LENGTHUNIT.,.MILLI.,.METRE.);
#103= IFCSIUNIT(*,.PLANEANGLEUNIT.,$,.RADIAN.);
#104= IFCSIUNIT(*,.TIMEUNIT.,$,.SECOND.);
#105= IFCRELAGGREGATES('2y86Kdr8zCpQhBH0bDSXAn',$,'Project Container','Project Container for Buildings',#100,(#50));
#200= IFCDOCUMENTREFERENCE($,'MyReinforcementCode','MyCodeISO3766',$,$);
#201= IFCRELASSOCIATESDOCUMENT('3sv6qBdlPEUOXgKyCQuE05',$,$,$,(#100),#200);
#202= IFCMATERIAL('ReinforcingSteel',$,$);
#203= IFCRELASSOCIATESMATERIAL('1AngNNHCD2VRy9BF2MBz0P',$,'MatAssoc','Material Associates',(#212),#202);
#205= IFCINDEXEDPOLYCURVE(#206,(IFCLINEINDEX((1,2)),IFCARCINDEX((2,3,4)),IFCLINEINDEX((4,5)),IFCARCINDEX((5,6,7)),IFCLINEINDEX((7,8)),IFCARCINDEX((8,9,10)),IFCLINEINDEX((10,11)),IFCARCINDEX((11,12,13)),IFCLINEINDEX((13,14)),IFCARCINDEX((14,15,16)),IFCLINEINDEX((16,17)),IFCARCINDEX((17,18,19)),IFCLINEINDEX((19,20))),$);
#206= IFCCARTESIANPOINTLIST3D(((-69.0,0.0,-122.0),(-69.0,0.0,-79.0),(-54.9411254969544,0.0,-45.0588745030457),(-21.0000000000001,0.0,-31.0),(21.0,0.0,-31.0),(54.9411254969543,0.0,-45.0588745030456),(69.0,0.0,-78.9999999999999),(69.0,0.00000000000000089,-321.0),(54.993978595716,1.21791490472038,-354.941125496954),(21.1804517666064,4.1582215855126,-369.0),(-20.6616529376114,7.79666547283599,-369.0),(-54.4751797667207,10.7369721536282,-354.941125496954),(-68.4812011710042,11.9548870583485,-320.999999999999),(-69.0,12.0,-79.0),(-54.9411254969544,12.0,-45.0588745030457),(-21.0000000000001,12.0,-31.0),(21.0,12.0,-31.0),(54.9411254969543,12.0,-45.0588745030456),(69.0,12.0,-78.9999999999999),(-69.0,0.0,-122.0)));
#207= IFCSWEPTDISKSOLID(#205,6.0,$,$,$);
#208= IFCREPRESENTATIONMAP(#209,#211);
#209= IFCAXIS2PLACEMENT3D(#210,$,$);
#210= IFCCARTESIANPOINT((0.0,0.0,0.0));
#211= IFCSHAPEREPRESENTATION(#12,'Body','AdvancedSweptSolid',(#207));
#212= IFCREINFORCINGBARTYPE('0z4ategPj8V86Xh5LOag$2',$,'12 Diameter Ligature',$,$,$,(#208),$,$,.LIGATURE.,12.0,113.097335529233,1150.0,.TEXTURED.,$,$);
#213= IFCRELDEFINESBYTYPE('2_3xPha51ELO8K5I5isuE$',$,'12 Diameter Ligature',$,(#251,#262,#272,#282,#292,#302,#312,#322,#332,#342,#352,#362,#372,#382,#392,#402,#412,#422,#432,#442,#452,#462,#472,#482,#492,#502,#512,#522,#532,#542,#552,#562,#572,#582),#212);
#214= IFCMATERIAL('Concrete',$,'Concrete');
#217= IFCRECTANGLEPROFILEDEF(.AREA.,'400x200RC',$,200.0,400.0);
#218= IFCMATERIALPROFILE('400x200RC',$,#214,#217,0,$);
#220= IFCMATERIALPROFILESET('400x200RC',$,(#218),$);
#221= IFCRELASSOCIATESMATERIAL('2g73lKulD6XveAQGz9xgMf',$,'MatAssoc','Material Associates',(#222),#220);
#222= IFCBEAMTYPE('0bCP748IrDLe12xwZtPGGS',$,'400x200RC',$,$,$,$,$,$,.BEAM.);
#223= IFCRELDEFINESBYTYPE('0jTbx0gcj8$uGSK$7M7ebk',$,'400x200RC',$,(#224),#222);
#224= IFCBEAMSTANDARDCASE('1VqLDnx414bxsjqKU7ADfc',$,$,$,$,#225,#243,$,$);
#225= IFCLOCALPLACEMENT($,#226);
#226= IFCAXIS2PLACEMENT3D(#227,#228,#229);
#227= IFCCARTESIANPOINT((0.0,0.0,0.0));
#228= IFCDIRECTION((0.0,1.0,0.0));
#229= IFCDIRECTION((-1.0,0.0,0.0));
#230= IFCMATERIALPROFILESETUSAGE(#220,8,$);
#231= IFCRELASSOCIATESMATERIAL('0OGCB9U6X4985aj9EedTZh',$,'MatAssoc','Material Associates',(#224),#230);
#232= IFCCARTESIANPOINT((0.0,0.0,0.0));
#233= IFCCARTESIANPOINT((0.0,0.0,5000.0));
#234= IFCPOLYLINE((#232,#233));
#235= IFCSHAPEREPRESENTATION(#11,'Axis','Curve3D',(#234));
#236= IFCDIRECTION((0.0,0.0,1.0));
#237= IFCEXTRUDEDAREASOLID(#217,#238,#236,5000.0);
#238= IFCAXIS2PLACEMENT3D(#239,#240,#241);
#239= IFCCARTESIANPOINT((0.0,-200.0,0.0));
#240= IFCDIRECTION((0.0,0.0,1.0));
#241= IFCDIRECTION((1.0,0.0,0.0));
#242= IFCSHAPEREPRESENTATION(#12,'Body','SweptSolid',(#237));
#243= IFCPRODUCTDEFINITIONSHAPE($,$,(#235,#242));
#244= IFCELEMENTASSEMBLY('0sJPc6Imb3Ph5Rsahz4WYS',$,$,$,$,$,$,$,.FACTORY.,.REINFORCEMENT_UNIT.);
#245= IFCDIRECTION((1.0,0.0,0.0));
#246= IFCDIRECTION((0.0,1.0,0.0));
#247= IFCCARTESIANPOINT((0.0,25.0,0.0));
#248= IFCCARTESIANTRANSFORMATIONOPERATOR3D(#245,#246,#247,1.0,#249);
#249= IFCDIRECTION((0.0,0.0,1.0));
#250= IFCMAPPEDITEM(#208,#248);
#251= IFCREINFORCINGBAR('0X9wv9XdjEYO2nsFBpTcnd',$,$,$,$,#254,#252,$,$,$,$,$,$,$);
#252= IFCPRODUCTDEFINITIONSHAPE($,$,(#253));
#253= IFCSHAPEREPRESENTATION(#12,'Body','MappedRepresentation',(#250));
#254= IFCLOCALPLACEMENT($,#52);
#255= IFCRELAGGREGATES('3ZoxgyKNP5lhL7yVmS2p3f',$,'ELEMENTASSEMBLY Container','ELEMENTASSEMBLY Container for Elements',#244,(#251,#262,#272,#282,#292,#302,#312,#322,#332,#342,#352,#362,#372,#382,#392,#402,#412,#422,#432,#442,#452,#462,#472,#482,#492,#502,#512,#522,#532,#542,#552,#562,#572,#582));
#256= IFCDIRECTION((1.0,0.0,0.0));
#257= IFCDIRECTION((0.0,1.0,0.0));
#258= IFCCARTESIANPOINT((0.0,175.0,0.0));
#259= IFCCARTESIANTRANSFORMATIONOPERATOR3D(#256,#257,#258,1.0,#260);
#260= IFCDIRECTION((0.0,0.0,1.0));
#261= IFCMAPPEDITEM(#208,#259);
#262= IFCREINFORCINGBAR('1_WZkvvIP6helVP3gsEFVw',$,$,$,$,#265,#263,$,$,$,$,$,$,$);
#263= IFCPRODUCTDEFINITIONSHAPE($,$,(#264));
#264= IFCSHAPEREPRESENTATION(#12,'Body','MappedRepresentation',(#261));
#265= IFCLOCALPLACEMENT($,#52);
#266= IFCDIRECTION((1.0,0.0,0.0));
#267= IFCDIRECTION((0.0,1.0,0.0));
#268= IFCCARTESIANPOINT((0.0,325.0,0.0));
#269= IFCCARTESIANTRANSFORMATIONOPERATOR3D(#266,#267,#268,1.0,#270);
#270= IFCDIRECTION((0.0,0.0,1.0));
#271= IFCMAPPEDITEM(#208,#269);
#272= IFCREINFORCINGBAR('2vXadaelP9EwjUulhz9QyC',$,$,$,$,#275,#273,$,$,$,$,$,$,$);
#273= IFCPRODUCTDEFINITIONSHAPE($,$,(#274));
#274= IFCSHAPEREPRESENTATION(#12,'Body','MappedRepresentation',(#271));
#275= IFCLOCALPLACEMENT($,#52);
#276= IFCDIRECTION((1.0,0.0,0.0));
#277= IFCDIRECTION((0.0,1.0,0.0));
#278= IFCCARTESIANPOINT((0.0,475.0,0.0));
#279= IFCCARTESIANTRANSFORMATIONOPERATOR3D(#276,#277,#278,1.0,#280);
#280= IFCDIRECTION((0.0,0.0,1.0));
#281= IFCMAPPEDITEM(#208,#279);
#282= IFCREINFORCINGBAR('0B2Lrr6Lv4HRBEl5eA9AsB',$,$,$,$,#285,#283,$,$,$,$,$,$,$);
#283= IFCPRODUCTDEFINITIONSHAPE($,$,(#284));
#284= IFCSHAPEREPRESENTATION(#12,'Body','MappedRepresentation',(#281));
#285= IFCLOCALPLACEMENT($,#52);
#286= IFCDIRECTION((1.0,0.0,0.0));
#287= IFCDIRECTION((0.0,1.0,0.0));
#288= IFCCARTESIANPOINT((0.0,625.0,0.0));
#289= IFCCARTESIANTRANSFORMATIONOPERATOR3D(#286,#287,#288,1.0,#290);
#290= IFCDIRECTION((0.0,0.0,1.0));
#291= IFCMAPPEDITEM(#208,#289);
#292= IFCREINFORCINGBAR('1pk2f8DX97LuZkc8nzjiTw',$,$,$,$,#295,#293,$,$,$,$,$,$,$);
#293= IFCPRODUCTDEFINITIONSHAPE($,$,(#294));
#294= IFCSHAPEREPRESENTATION(#12,'Body','MappedRepresentation',(#291));
#295= IFCLOCALPLACEMENT($,#52);
#296= IFCDIRECTION((1.0,0.0,0.0));
#297= IFCDIRECTION((0.0,1.0,0.0));
#298= IFCCARTESIANPOINT((0.0,775.0,0.0));
#299= IFCCARTESIANTRANSFORMATIONOPERATOR3D(#296,#297,#298,1.0,#300);
#300= IFCDIRECTION((0.0,0.0,1.0));
#301= IFCMAPPEDITEM(#208,#299);
#302= IFCREINFORCINGBAR('3Fi4$7GBP2_wc47Tuj8EOb',$,$,$,$,#305,#303,$,$,$,$,$,$,$);
#303= IFCPRODUCTDEFINITIONSHAPE($,$,(#304));
#304= IFCSHAPEREPRESENTATION(#12,'Body','MappedRepresentation',(#301));
#305= IFCLOCALPLACEMENT($,#52);
#306= IFCDIRECTION((1.0,0.0,0.0));
#307= IFCDIRECTION((0.0,1.0,0.0));
#308= IFCCARTESIANPOINT((0.0,925.0,0.0));
#309= IFCCARTESIANTRANSFORMATIONOPERATOR3D(#306,#307,#308,1.0,#310);
#310= IFCDIRECTION((0.0,0.0,1.0));
#311= IFCMAPPEDITEM(#208,#309);
#312= IFCREINFORCINGBAR('3co82UYKv6m8fCRbncwjFM',$,$,$,$,#315,#313,$,$,$,$,$,$,$);
#313= IFCPRODUCTDEFINITIONSHAPE($,$,(#314));
#314= IFCSHAPEREPRESENTATION(#12,'Body','MappedRepresentation',(#311));
#315= IFCLOCALPLACEMENT($,#52);
#316= IFCDIRECTION((1.0,0.0,0.0));
#317= IFCDIRECTION((0.0,1.0,0.0));
#318= IFCCARTESIANPOINT((0.0,1075.0,0.0));
#319= IFCCARTESIANTRANSFORMATIONOPERATOR3D(#316,#317,#318,1.0,#320);
#320= IFCDIRECTION((0.0,0.0,1.0));
#321= IFCMAPPEDITEM(#208,#319);
#322= IFCREINFORCINGBAR('2JNMKKGY16BPkJlV1rwYCG',$,$,$,$,#325,#323,$,$,$,$,$,$,$);
#323= IFCPRODUCTDEFINITIONSHAPE($,$,(#324));
#324= IFCSHAPEREPRESENTATION(#12,'Body','MappedRepresentation',(#321));
#325= IFCLOCALPLACEMENT($,#52);
#326= IFCDIRECTION((1.0,0.0,0.0));
#327= IFCDIRECTION((0.0,1.0,0.0));
#328= IFCCARTESIANPOINT((0.0,1225.0,0.0));
#329= IFCCARTESIANTRANSFORMATIONOPERATOR3D(#326,#327,#328,1.0,#330);
#330= IFCDIRECTION((0.0,0.0,1.0));
#331= IFCMAPPEDITEM(#208,#329);
#332= IFCREINFORCINGBAR('2KpH4fyAfFAPKTLsZlJvAj',$,$,$,$,#335,#333,$,$,$,$,$,$,$);
#333= IFCPRODUCTDEFINITIONSHAPE($,$,(#334));
#334= IFCSHAPEREPRESENTATION(#12,'Body','MappedRepresentation',(#331));
#335= IFCLOCALPLACEMENT($,#52);
#336= IFCDIRECTION((1.0,0.0,0.0));
#337= IFCDIRECTION((0.0,1.0,0.0));
#338= IFCCARTESIANPOINT((0.0,1375.0,0.0));
#339= IFCCARTESIANTRANSFORMATIONOPERATOR3D(#336,#337,#338,1.0,#340);
#340= IFCDIRECTION((0.0,0.0,1.0));
#341= IFCMAPPEDITEM(#208,#339);
#342= IFCREINFORCINGBAR('3xlhbdNRLD2QkXU5m_IqqU',$,$,$,$,#345,#343,$,$,$,$,$,$,$);
#343= IFCPRODUCTDEFINITIONSHAPE($,$,(#344));
#344= IFCSHAPEREPRESENTATION(#12,'Body','MappedRepresentation',(#341));
#345= IFCLOCALPLACEMENT($,#52);
#346= IFCDIRECTION((1.0,0.0,0.0));
#347= IFCDIRECTION((0.0,1.0,0.0));
#348= IFCCARTESIANPOINT((0.0,1525.0,0.0));
#349= IFCCARTESIANTRANSFORMATIONOPERATOR3D(#346,#347,#348,1.0,#350);
#350= IFCDIRECTION((0.0,0.0,1.0));
#351= IFCMAPPEDITEM(#208,#349);
#352= IFCREINFORCINGBAR('0til_kQmr2e94OnrA1_Ftc',$,$,$,$,#355,#353,$,$,$,$,$,$,$);
#353= IFCPRODUCTDEFINITIONSHAPE($,$,(#354));
#354= IFCSHAPEREPRESENTATION(#12,'Body','MappedRepresentation',(#351));
#355= IFCLOCALPLACEMENT($,#52);
#356= IFCDIRECTION((1.0,0.0,0.0));
#357= IFCDIRECTION((0.0,1.0,0.0));
#358= IFCCARTESIANPOINT((0.0,1675.0,0.0));
#359= IFCCARTESIANTRANSFORMATIONOPERATOR3D(#356,#357,#358,1.0,#360);
#360= IFCDIRECTION((0.0,0.0,1.0));
#361= IFCMAPPEDITEM(#208,#359);
#362= IFCREINFORCINGBAR('3VX1168jPE$wQRfsCn9XCO',$,$,$,$,#365,#363,$,$,$,$,$,$,$);
#363= IFCPRODUCTDEFINITIONSHAPE($,$,(#364));
#364= IFCSHAPEREPRESENTATION(#12,'Body','MappedRepresentation',(#361));
#365= IFCLOCALPLACEMENT($,#52);
#366= IFCDIRECTION((1.0,0.0,0.0));
#367= IFCDIRECTION((0.0,1.0,0.0));
#368= IFCCARTESIANPOINT((0.0,1825.0,0.0));
#369= IFCCARTESIANTRANSFORMATIONOPERATOR3D(#366,#367,#368,1.0,#370);
#370= IFCDIRECTION((0.0,0.0,1.0));
#371= IFCMAPPEDITEM(#208,#369);
#372= IFCREINFORCINGBAR('3z5f38IIf8fhDAc8ENt10l',$,$,$,$,#375,#373,$,$,$,$,$,$,$);
#373= IFCPRODUCTDEFINITIONSHAPE($,$,(#374));
#374= IFCSHAPEREPRESENTATION(#12,'Body','MappedRepresentation',(#371));
#375= IFCLOCALPLACEMENT($,#52);
#376= IFCDIRECTION((1.0,0.0,0.0));
#377= IFCDIRECTION((0.0,1.0,0.0));
#378= IFCCARTESIANPOINT((0.0,1975.0,0.0));
#379= IFCCARTESIANTRANSFORMATIONOPERATOR3D(#376,#377,#378,1.0,#380);
#380= IFCDIRECTION((0.0,0.0,1.0));
#381= IFCMAPPEDITEM(#208,#379);
#382= IFCREINFORCINGBAR('0xyptLYzb8MQGiGry4bmPS',$,$,$,$,#385,#383,$,$,$,$,$,$,$);
#383= IFCPRODUCTDEFINITIONSHAPE($,$,(#384));
#384= IFCSHAPEREPRESENTATION(#12,'Body','MappedRepresentation',(#381));
#385= IFCLOCALPLACEMENT($,#52);
#386= IFCDIRECTION((1.0,0.0,0.0));
#387= IFCDIRECTION((0.0,1.0,0.0));
#388= IFCCARTESIANPOINT((0.0,2125.0,0.0));
#389= IFCCARTESIANTRANSFORMATIONOPERATOR3D(#386,#387,#388,1.0,#390);
#390= IFCDIRECTION((0.0,0.0,1.0));
#391= IFCMAPPEDITEM(#208,#389);
#392= IFCREINFORCINGBAR('1LNoA5DlL5Qe3oUfd403CQ',$,$,$,$,#395,#393,$,$,$,$,$,$,$);
#393= IFCPRODUCTDEFINITIONSHAPE($,$,(#394));
#394= IFCSHAPEREPRESENTATION(#12,'Body','MappedRepresentation',(#391));
#395= IFCLOCALPLACEMENT($,#52);
#396= IFCDIRECTION((1.0,0.0,0.0));
#397= IFCDIRECTION((0.0,1.0,0.0));
#398= IFCCARTESIANPOINT((0.0,2275.0,0.0));
#399= IFCCARTESIANTRANSFORMATIONOPERATOR3D(#396,#397,#398,1.0,#400);
#400= IFCDIRECTION((0.0,0.0,1.0));
#401= IFCMAPPEDITEM(#208,#399);
#402= IFCREINFORCINGBAR('3eghWwAEX01BtJ2E4ptTLx',$,$,$,$,#405,#403,$,$,$,$,$,$,$);
#403= IFCPRODUCTDEFINITIONSHAPE($,$,(#404));
#404= IFCSHAPEREPRESENTATION(#12,'Body','MappedRepresentation',(#401));
#405= IFCLOCALPLACEMENT($,#52);
#406= IFCDIRECTION((1.0,0.0,0.0));
#407= IFCDIRECTION((0.0,1.0,0.0));
#408= IFCCARTESIANPOINT((0.0,2425.0,0.0));
#409= IFCCARTESIANTRANSFORMATIONOPERATOR3D(#406,#407,#408,1.0,#410);
#410= IFCDIRECTION((0.0,0.0,1.0));
#411= IFCMAPPEDITEM(#208,#409);
#412= IFCREINFORCINGBAR('3qAHaxKPX6MASh_ABF$6Lp',$,$,$,$,#415,#413,$,$,$,$,$,$,$);
#413= IFCPRODUCTDEFINITIONSHAPE($,$,(#414));
#414= IFCSHAPEREPRESENTATION(#12,'Body','MappedRepresentation',(#411));
#415= IFCLOCALPLACEMENT($,#52);
#416= IFCDIRECTION((1.0,0.0,0.0));
#417= IFCDIRECTION((0.0,1.0,0.0));
#418= IFCCARTESIANPOINT((0.0,2575.0,0.0));
#419= IFCCARTESIANTRANSFORMATIONOPERATOR3D(#416,#417,#418,1.0,#420);
#420= IFCDIRECTION((0.0,0.0,1.0));
#421= IFCMAPPEDITEM(#208,#419);
#422= IFCREINFORCINGBAR('1F$nFBpTj8bAMaCu8WL98_',$,$,$,$,#425,#423,$,$,$,$,$,$,$);
#423= IFCPRODUCTDEFINITIONSHAPE($,$,(#424));
#424= IFCSHAPEREPRESENTATION(#12,'Body','MappedRepresentation',(#421));
#425= IFCLOCALPLACEMENT($,#52);
#426= IFCDIRECTION((1.0,0.0,0.0));
#427= IFCDIRECTION((0.0,1.0,0.0));
#428= IFCCARTESIANPOINT((0.0,2725.0,0.0));
#429= IFCCARTESIANTRANSFORMATIONOPERATOR3D(#426,#427,#428,1.0,#430);
#430= IFCDIRECTION((0.0,0.0,1.0));
#431= IFCMAPPEDITEM(#208,#429);
#432= IFCREINFORCINGBAR('3LENYE4Uz5l8$owyO5P$3e',$,$,$,$,#435,#433,$,$,$,$,$,$,$);
#433= IFCPRODUCTDEFINITIONSHAPE($,$,(#434));
#434= IFCSHAPEREPRESENTATION(#12,'Body','MappedRepresentation',(#431));
#435= IFCLOCALPLACEMENT($,#52);
#436= IFCDIRECTION((1.0,0.0,0.0));
#437= IFCDIRECTION((0.0,1.0,0.0));
#438= IFCCARTESIANPOINT((0.0,2875.0,0.0));
#439= IFCCARTESIANTRANSFORMATIONOPERATOR3D(#436,#437,#438,1.0,#440);
#440= IFCDIRECTION((0.0,0.0,1.0));
#441= IFCMAPPEDITEM(#208,#439);
#442= IFCREINFORCINGBAR('1bDonuwqb38eEcK_2WGVix',$,$,$,$,#445,#443,$,$,$,$,$,$,$);
#443= IFCPRODUCTDEFINITIONSHAPE($,$,(#444));
#444= IFCSHAPEREPRESENTATION(#12,'Body','MappedRepresentation',(#441));
#445= IFCLOCALPLACEMENT($,#52);
#446= IFCDIRECTION((1.0,0.0,0.0));
#447= IFCDIRECTION((0.0,1.0,0.0));
#448= IFCCARTESIANPOINT((0.0,3025.0,0.0));
#449= IFCCARTESIANTRANSFORMATIONOPERATOR3D(#446,#447,#448,1.0,#450);
#450= IFCDIRECTION((0.0,0.0,1.0));
#451= IFCMAPPEDITEM(#208,#449);
#452= IFCREINFORCINGBAR('0E6T2zEmH3l8R4$Abx1JgJ',$,$,$,$,#455,#453,$,$,$,$,$,$,$);
#453= IFCPRODUCTDEFINITIONSHAPE($,$,(#454));
#454= IFCSHAPEREPRESENTATION(#12,'Body','MappedRepresentation',(#451));
#455= IFCLOCALPLACEMENT($,#52);
#456= IFCDIRECTION((1.0,0.0,0.0));
#457= IFCDIRECTION((0.0,1.0,0.0));
#458= IFCCARTESIANPOINT((0.0,3175.0,0.0));
#459= IFCCARTESIANTRANSFORMATIONOPERATOR3D(#456,#457,#458,1.0,#460);
#460= IFCDIRECTION((0.0,0.0,1.0));
#461= IFCMAPPEDITEM(#208,#459);
#462= IFCREINFORCINGBAR('3LVHcxsRf6b8kujh2jKKPy',$,$,$,$,#465,#463,$,$,$,$,$,$,$);
#463= IFCPRODUCTDEFINITIONSHAPE($,$,(#464));
#464= IFCSHAPEREPRESENTATION(#12,'Body','MappedRepresentation',(#461));
#465= IFCLOCALPLACEMENT($,#52);
#466= IFCDIRECTION((1.0,0.0,0.0));
#467= IFCDIRECTION((0.0,1.0,0.0));
#468= IFCCARTESIANPOINT((0.0,3325.0,0.0));
#469= IFCCARTESIANTRANSFORMATIONOPERATOR3D(#466,#467,#468,1.0,#470);
#470= IFCDIRECTION((0.0,0.0,1.0));
#471= IFCMAPPEDITEM(#208,#469);
#472= IFCREINFORCINGBAR('3U4RnQfBD8ZOnjZg73GjG8',$,$,$,$,#475,#473,$,$,$,$,$,$,$);
#473= IFCPRODUCTDEFINITIONSHAPE($,$,(#474));
#474= IFCSHAPEREPRESENTATION(#12,'Body','MappedRepresentation',(#471));
#475= IFCLOCALPLACEMENT($,#52);
#476= IFCDIRECTION((1.0,0.0,0.0));
#477= IFCDIRECTION((0.0,1.0,0.0));
#478= IFCCARTESIANPOINT((0.0,3475.0,0.0));
#479= IFCCARTESIANTRANSFORMATIONOPERATOR3D(#476,#477,#478,1.0,#480);
#480= IFCDIRECTION((0.0,0.0,1.0));
#481= IFCMAPPEDITEM(#208,#479);
#482= IFCREINFORCINGBAR('3U6Myoj$98W9IkbmCaLf2M',$,$,$,$,#485,#483,$,$,$,$,$,$,$);
#483= IFCPRODUCTDEFINITIONSHAPE($,$,(#484));
#484= IFCSHAPEREPRESENTATION(#12,'Body','MappedRepresentation',(#481));
#485= IFCLOCALPLACEMENT($,#52);
#486= IFCDIRECTION((1.0,0.0,0.0));
#487= IFCDIRECTION((0.0,1.0,0.0));
#488= IFCCARTESIANPOINT((0.0,3625.0,0.0));
#489= IFCCARTESIANTRANSFORMATIONOPERATOR3D(#486,#487,#488,1.0,#490);
#490= IFCDIRECTION((0.0,0.0,1.0));
#491= IFCMAPPEDITEM(#208,#489);
#492= IFCREINFORCINGBAR('0usMsRTxP0kP29733fsf2K',$,$,$,$,#495,#493,$,$,$,$,$,$,$);
#493= IFCPRODUCTDEFINITIONSHAPE($,$,(#494));
#494= IFCSHAPEREPRESENTATION(#12,'Body','MappedRepresentation',(#491));
#495= IFCLOCALPLACEMENT($,#52);
#496= IFCDIRECTION((1.0,0.0,0.0));
#497= IFCDIRECTION((0.0,1.0,0.0));
#498= IFCCARTESIANPOINT((0.0,3775.0,0.0));
#499= IFCCARTESIANTRANSFORMATIONOPERATOR3D(#496,#497,#498,1.0,#500);
#500= IFCDIRECTION((0.0,0.0,1.0));
#501= IFCMAPPEDITEM(#208,#499);
#502= IFCREINFORCINGBAR('25gmOlxuL91eYqABfnEqRQ',$,$,$,$,#505,#503,$,$,$,$,$,$,$);
#503= IFCPRODUCTDEFINITIONSHAPE($,$,(#504));
#504= IFCSHAPEREPRESENTATION(#12,'Body','MappedRepresentation',(#501));
#505= IFCLOCALPLACEMENT($,#52);
#506= IFCDIRECTION((1.0,0.0,0.0));
#507= IFCDIRECTION((0.0,1.0,0.0));
#508= IFCCARTESIANPOINT((0.0,3925.0,0.0));
#509= IFCCARTESIANTRANSFORMATIONOPERATOR3D(#506,#507,#508,1.0,#510);
#510= IFCDIRECTION((0.0,0.0,1.0));
#511= IFCMAPPEDITEM(#208,#509);
#512= IFCREINFORCINGBAR('1lPvw96JPBKuCj6rfrnRvE',$,$,$,$,#515,#513,$,$,$,$,$,$,$);
#513= IFCPRODUCTDEFINITIONSHAPE($,$,(#514));
#514= IFCSHAPEREPRESENTATION(#12,'Body','MappedRepresentation',(#511));
#515= IFCLOCALPLACEMENT($,#52);
#516= IFCDIRECTION((1.0,0.0,0.0));
#517= IFCDIRECTION((0.0,1.0,0.0));
#518= IFCCARTESIANPOINT((0.0,4075.0,0.0));
#519= IFCCARTESIANTRANSFORMATIONOPERATOR3D(#516,#517,#518,1.0,#520);
#520= IFCDIRECTION((0.0,0.0,1.0));
#521= IFCMAPPEDITEM(#208,#519);
#522= IFCREINFORCINGBAR('1gmjP8SKT91e2HMuoACRv8',$,$,$,$,#525,#523,$,$,$,$,$,$,$);
#523= IFCPRODUCTDEFINITIONSHAPE($,$,(#524));
#524= IFCSHAPEREPRESENTATION(#12,'Body','MappedRepresentation',(#521));
#525= IFCLOCALPLACEMENT($,#52);
#526= IFCDIRECTION((1.0,0.0,0.0));
#527= IFCDIRECTION((0.0,1.0,0.0));
#528= IFCCARTESIANPOINT((0.0,4225.0,0.0));
#529= IFCCARTESIANTRANSFORMATIONOPERATOR3D(#526,#527,#528,1.0,#530);
#530= IFCDIRECTION((0.0,0.0,1.0));
#531= IFCMAPPEDITEM(#208,#529);
#532= IFCREINFORCINGBAR('23usaHG6T8AR7l7h2CYijI',$,$,$,$,#535,#533,$,$,$,$,$,$,$);
#533= IFCPRODUCTDEFINITIONSHAPE($,$,(#534));
#534= IFCSHAPEREPRESENTATION(#12,'Body','MappedRepresentation',(#531));
#535= IFCLOCALPLACEMENT($,#52);
#536= IFCDIRECTION((1.0,0.0,0.0));
#537= IFCDIRECTION((0.0,1.0,0.0));
#538= IFCCARTESIANPOINT((0.0,4375.0,0.0));
#539= IFCCARTESIANTRANSFORMATIONOPERATOR3D(#536,#537,#538,1.0,#540);
#540= IFCDIRECTION((0.0,0.0,1.0));
#541= IFCMAPPEDITEM(#208,#539);
#542= IFCREINFORCINGBAR('31ZuXJpGD0oAwU6NDk4uyD',$,$,$,$,#545,#543,$,$,$,$,$,$,$);
#543= IFCPRODUCTDEFINITIONSHAPE($,$,(#544));
#544= IFCSHAPEREPRESENTATION(#12,'Body','MappedRepresentation',(#541));
#545= IFCLOCALPLACEMENT($,#52);
#546= IFCDIRECTION((1.0,0.0,0.0));
#547= IFCDIRECTION((0.0,1.0,0.0));
#548= IFCCARTESIANPOINT((0.0,4525.0,0.0));
#549= IFCCARTESIANTRANSFORMATIONOPERATOR3D(#546,#547,#548,1.0,#550);
#550= IFCDIRECTION((0.0,0.0,1.0));
#551= IFCMAPPEDITEM(#208,#549);
#552= IFCREINFORCINGBAR('1ZLktWyV96ABNT0HxnVfRf',$,$,$,$,#555,#553,$,$,$,$,$,$,$);
#553= IFCPRODUCTDEFINITIONSHAPE($,$,(#554));
#554= IFCSHAPEREPRESENTATION(#12,'Body','MappedRepresentation',(#551));
#555= IFCLOCALPLACEMENT($,#52);
#556= IFCDIRECTION((1.0,0.0,0.0));
#557= IFCDIRECTION((0.0,1.0,0.0));
#558= IFCCARTESIANPOINT((0.0,4675.0,0.0));
#559= IFCCARTESIANTRANSFORMATIONOPERATOR3D(#556,#557,#558,1.0,#560);
#560= IFCDIRECTION((0.0,0.0,1.0));
#561= IFCMAPPEDITEM(#208,#559);
#562= IFCREINFORCINGBAR('2sq2rbiav96g6JcZKQqvI8',$,$,$,$,#565,#563,$,$,$,$,$,$,$);
#563= IFCPRODUCTDEFINITIONSHAPE($,$,(#564));
#564= IFCSHAPEREPRESENTATION(#12,'Body','MappedRepresentation',(#561));
#565= IFCLOCALPLACEMENT($,#52);
#566= IFCDIRECTION((1.0,0.0,0.0));
#567= IFCDIRECTION((0.0,1.0,0.0));
#568= IFCCARTESIANPOINT((0.0,4825.0,0.0));
#569= IFCCARTESIANTRANSFORMATIONOPERATOR3D(#566,#567,#568,1.0,#570);
#570= IFCDIRECTION((0.0,0.0,1.0));
#571= IFCMAPPEDITEM(#208,#569);
#572= IFCREINFORCINGBAR('1UhvLsV5T0EhRNnXBrLWEz',$,$,$,$,#575,#573,$,$,$,$,$,$,$);
#573= IFCPRODUCTDEFINITIONSHAPE($,$,(#574));
#574= IFCSHAPEREPRESENTATION(#12,'Body','MappedRepresentation',(#571));
#575= IFCLOCALPLACEMENT($,#52);
#576= IFCDIRECTION((1.0,0.0,0.0));
#577= IFCDIRECTION((0.0,1.0,0.0));
#578= IFCCARTESIANPOINT((0.0,4975.0,0.0));
#579= IFCCARTESIANTRANSFORMATIONOPERATOR3D(#576,#577,#578,1.0,#580);
#580= IFCDIRECTION((0.0,0.0,1.0));
#581= IFCMAPPEDITEM(#208,#579);
#582= IFCREINFORCINGBAR('1ltX7blVz6MAo8eXQDPbyM',$,$,$,$,#585,#583,$,$,$,$,$,$,$);
#583= IFCPRODUCTDEFINITIONSHAPE($,$,(#584));
#584= IFCSHAPEREPRESENTATION(#12,'Body','MappedRepresentation',(#581));
#585= IFCLOCALPLACEMENT($,#52);
ENDSEC;

END-ISO-10303-21;


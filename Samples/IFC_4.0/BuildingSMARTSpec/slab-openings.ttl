# baseURI: http://linkedbuildingdata.net/ifc/resources20200624_184152/
# imports: http://standards.buildingsmart.org/IFC/DEV/IFC4/ADD1/OWL

@prefix ifc:  <http://standards.buildingsmart.org/IFC/DEV/IFC4/ADD1/OWL#> .
@prefix inst:  <http://linkedbuildingdata.net/ifc/resources20200624_184152/> .
@prefix list:  <https://w3id.org/list#> .
@prefix express:  <https://w3id.org/express#> .
@prefix rdf:  <http://www.w3.org/1999/02/22-rdf-syntax-ns#> .
@prefix xsd:  <http://www.w3.org/2001/XMLSchema#> .
@prefix owl:  <http://www.w3.org/2002/07/owl#> .

inst:   rdf:type     owl:Ontology ;
        owl:imports  ifc: .

inst:IfcApplication_1
        rdf:type  ifc:IfcApplication .

inst:IfcOrganization_2
        rdf:type  ifc:IfcOrganization .

inst:IfcApplication_1
        ifc:applicationDeveloper_IfcApplication  inst:IfcOrganization_2 .

inst:IfcLabel_75  rdf:type  ifc:IfcLabel ;
        express:hasString  "1.0.0.0" .

inst:IfcApplication_1
        ifc:version_IfcApplication  inst:IfcLabel_75 .

inst:IfcLabel_76  rdf:type  ifc:IfcLabel ;
        express:hasString  "ggRhinoIFC - Geometry Gym Plug-in for Rhino3d" .

inst:IfcApplication_1
        ifc:applicationFullName_IfcApplication  inst:IfcLabel_76 .

inst:IfcIdentifier_77
        rdf:type           ifc:IfcIdentifier ;
        express:hasString  "ggRhinoIFC" .

inst:IfcApplication_1
        ifc:applicationIdentifier_IfcApplication  inst:IfcIdentifier_77 .

inst:IfcLabel_78  rdf:type  ifc:IfcLabel ;
        express:hasString  "Geometry Gym Pty Ltd" .

inst:IfcOrganization_2
        ifc:name_IfcOrganization  inst:IfcLabel_78 .

inst:IfcPersonAndOrganization_3
        rdf:type  ifc:IfcPersonAndOrganization .

inst:IfcPerson_4  rdf:type  ifc:IfcPerson .

inst:IfcPersonAndOrganization_3
        ifc:thePerson_IfcPersonAndOrganization  inst:IfcPerson_4 ;
        ifc:theOrganization_IfcPersonAndOrganization  inst:IfcOrganization_2 .

inst:IfcIdentifier_79
        rdf:type           ifc:IfcIdentifier ;
        express:hasString  "Jon" .

inst:IfcPerson_4  ifc:identification_IfcPerson  inst:IfcIdentifier_79 ;
        ifc:familyName_IfcPerson      inst:IfcIdentifier_79 .

inst:IfcOwnerHistory_6
        rdf:type                        ifc:IfcOwnerHistory ;
        ifc:owningUser_IfcOwnerHistory  inst:IfcPersonAndOrganization_3 ;
        ifc:owningApplication_IfcOwnerHistory  inst:IfcApplication_1 ;
        ifc:changeAction_IfcOwnerHistory  ifc:ADDED .

inst:IfcTimeStamp_80  rdf:type  ifc:IfcTimeStamp ;
        express:hasInteger  1418084874 .

inst:IfcOwnerHistory_6
        ifc:lastModifiedDate_IfcOwnerHistory  inst:IfcTimeStamp_80 ;
        ifc:creationDate_IfcOwnerHistory  inst:IfcTimeStamp_80 .

inst:IfcGeometricRepresentationContext_7
        rdf:type  ifc:IfcGeometricRepresentationContext .

inst:IfcLabel_81  rdf:type  ifc:IfcLabel ;
        express:hasString  "Model" .

inst:IfcGeometricRepresentationContext_7
        ifc:contextType_IfcRepresentationContext  inst:IfcLabel_81 .

inst:IfcDimensionCount_82
        rdf:type            ifc:IfcDimensionCount ;
        express:hasInteger  3 .

inst:IfcGeometricRepresentationContext_7
        ifc:coordinateSpaceDimension_IfcGeometricRepresentationContext  inst:IfcDimensionCount_82 .

inst:IfcReal_83  rdf:type  ifc:IfcReal ;
        express:hasDouble  "0.0001"^^xsd:double .

inst:IfcGeometricRepresentationContext_7
        ifc:precision_IfcGeometricRepresentationContext  inst:IfcReal_83 .

inst:IfcAxis2Placement3D_8
        rdf:type  ifc:IfcAxis2Placement3D .

inst:IfcGeometricRepresentationContext_7
        ifc:worldCoordinateSystem_IfcGeometricRepresentationContext  inst:IfcAxis2Placement3D_8 .

inst:IfcDirection_10  rdf:type  ifc:IfcDirection .

inst:IfcGeometricRepresentationContext_7
        ifc:trueNorth_IfcGeometricRepresentationContext  inst:IfcDirection_10 .

inst:IfcCartesianPoint_9
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcAxis2Placement3D_8
        ifc:location_IfcPlacement  inst:IfcCartesianPoint_9 .

inst:IfcLengthMeasure_List_84
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_9
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_84 .

inst:IfcLengthMeasure_List_85
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_86
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_87
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "0.0"^^xsd:double .

inst:IfcLengthMeasure_List_84
        list:hasContents  inst:IfcLengthMeasure_87 ;
        list:hasNext      inst:IfcLengthMeasure_List_85 .

inst:IfcLengthMeasure_List_85
        list:hasContents  inst:IfcLengthMeasure_87 ;
        list:hasNext      inst:IfcLengthMeasure_List_86 .

inst:IfcLengthMeasure_List_86
        list:hasContents  inst:IfcLengthMeasure_87 .

inst:IfcReal_List_88  rdf:type  ifc:IfcReal_List .

inst:IfcDirection_10  ifc:directionRatios_IfcDirection  inst:IfcReal_List_88 .

inst:IfcReal_List_89  rdf:type  ifc:IfcReal_List .

inst:IfcReal_List_88  list:hasContents  inst:IfcLengthMeasure_87 ;
        list:hasNext      inst:IfcReal_List_89 .

inst:IfcReal_90  rdf:type  ifc:IfcReal ;
        express:hasDouble  "1.0"^^xsd:double .

inst:IfcReal_List_89  list:hasContents  inst:IfcReal_90 .

inst:IfcGeometricRepresentationSubContext_11
        rdf:type  ifc:IfcGeometricRepresentationSubContext .

inst:IfcLabel_91  rdf:type  ifc:IfcLabel ;
        express:hasString  "Axis" .

inst:IfcGeometricRepresentationSubContext_11
        ifc:contextIdentifier_IfcRepresentationContext  inst:IfcLabel_91 ;
        ifc:contextType_IfcRepresentationContext  inst:IfcLabel_81 ;
        ifc:parentContext_IfcGeometricRepresentationSubContext  inst:IfcGeometricRepresentationContext_7 ;
        ifc:targetView_IfcGeometricRepresentationSubContext  ifc:MODEL_VIEW .

inst:IfcGeometricRepresentationSubContext_12
        rdf:type  ifc:IfcGeometricRepresentationSubContext .

inst:IfcLabel_92  rdf:type  ifc:IfcLabel ;
        express:hasString  "Body" .

inst:IfcGeometricRepresentationSubContext_12
        ifc:contextIdentifier_IfcRepresentationContext  inst:IfcLabel_92 ;
        ifc:contextType_IfcRepresentationContext  inst:IfcLabel_81 ;
        ifc:parentContext_IfcGeometricRepresentationSubContext  inst:IfcGeometricRepresentationContext_7 ;
        ifc:targetView_IfcGeometricRepresentationSubContext  ifc:MODEL_VIEW .

inst:IfcGeometricRepresentationContext_13
        rdf:type  ifc:IfcGeometricRepresentationContext ;
        ifc:contextType_IfcRepresentationContext  inst:IfcLabel_81 ;
        ifc:coordinateSpaceDimension_IfcGeometricRepresentationContext  inst:IfcDimensionCount_82 ;
        ifc:precision_IfcGeometricRepresentationContext  inst:IfcReal_83 ;
        ifc:worldCoordinateSystem_IfcGeometricRepresentationContext  inst:IfcAxis2Placement3D_8 ;
        ifc:trueNorth_IfcGeometricRepresentationContext  inst:IfcDirection_10 .

inst:IfcSlabType_300  rdf:type  ifc:IfcSlabType .

inst:IfcGloballyUniqueId_93
        rdf:type           ifc:IfcGloballyUniqueId ;
        express:hasString  "1EDjpMzcT3JAl5OwRhbtOd" .

inst:IfcSlabType_300  ifc:globalId_IfcRoot  inst:IfcGloballyUniqueId_93 .

inst:IfcLabel_94  rdf:type  ifc:IfcLabel ;
        express:hasString  "200mm Concrete" .

inst:IfcSlabType_300  ifc:name_IfcRoot  inst:IfcLabel_94 ;
        ifc:predefinedType_IfcSlabType  ifc:FLOOR .

inst:IfcRelDefinesByType_301
        rdf:type  ifc:IfcRelDefinesByType .

inst:IfcGloballyUniqueId_95
        rdf:type           ifc:IfcGloballyUniqueId ;
        express:hasString  "2tnpQwgGjCVwF6buy_NKcq" .

inst:IfcRelDefinesByType_301
        ifc:globalId_IfcRoot  inst:IfcGloballyUniqueId_95 ;
        ifc:name_IfcRoot      inst:IfcLabel_94 .

inst:IfcSlabStandardCase_302
        rdf:type  ifc:IfcSlabStandardCase .

inst:IfcRelDefinesByType_301
        ifc:relatedObjects_IfcRelDefinesByType  inst:IfcSlabStandardCase_302 ;
        ifc:relatingType_IfcRelDefinesByType  inst:IfcSlabType_300 .

inst:IfcGloballyUniqueId_96
        rdf:type           ifc:IfcGloballyUniqueId ;
        express:hasString  "21hyH1VvT7FO4OaH6TIJak" .

inst:IfcSlabStandardCase_302
        ifc:globalId_IfcRoot  inst:IfcGloballyUniqueId_96 .

inst:IfcLocalPlacement_307
        rdf:type  ifc:IfcLocalPlacement .

inst:IfcSlabStandardCase_302
        ifc:objectPlacement_IfcProduct  inst:IfcLocalPlacement_307 .

inst:IfcProductDefinitionShape_316
        rdf:type  ifc:IfcProductDefinitionShape .

inst:IfcSlabStandardCase_302
        ifc:representation_IfcProduct  inst:IfcProductDefinitionShape_316 .

inst:IfcAxis2Placement3D_303
        rdf:type  ifc:IfcAxis2Placement3D .

inst:IfcCartesianPoint_304
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcAxis2Placement3D_303
        ifc:location_IfcPlacement  inst:IfcCartesianPoint_304 .

inst:IfcDirection_305
        rdf:type  ifc:IfcDirection .

inst:IfcAxis2Placement3D_303
        ifc:axis_IfcAxis2Placement3D  inst:IfcDirection_305 .

inst:IfcDirection_306
        rdf:type  ifc:IfcDirection .

inst:IfcAxis2Placement3D_303
        ifc:refDirection_IfcAxis2Placement3D  inst:IfcDirection_306 .

inst:IfcLengthMeasure_List_97
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_304
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_97 .

inst:IfcLengthMeasure_List_98
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_99
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_97
        list:hasContents  inst:IfcLengthMeasure_87 ;
        list:hasNext      inst:IfcLengthMeasure_List_98 .

inst:IfcLengthMeasure_List_98
        list:hasContents  inst:IfcLengthMeasure_87 ;
        list:hasNext      inst:IfcLengthMeasure_List_99 .

inst:IfcLengthMeasure_100
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-200.0"^^xsd:double .

inst:IfcLengthMeasure_List_99
        list:hasContents  inst:IfcLengthMeasure_100 .

inst:IfcReal_List_101
        rdf:type  ifc:IfcReal_List .

inst:IfcDirection_305
        ifc:directionRatios_IfcDirection  inst:IfcReal_List_101 .

inst:IfcReal_List_102
        rdf:type  ifc:IfcReal_List .

inst:IfcReal_List_103
        rdf:type  ifc:IfcReal_List .

inst:IfcReal_List_101
        list:hasContents  inst:IfcLengthMeasure_87 ;
        list:hasNext      inst:IfcReal_List_102 .

inst:IfcReal_List_102
        list:hasContents  inst:IfcLengthMeasure_87 ;
        list:hasNext      inst:IfcReal_List_103 .

inst:IfcReal_List_103
        list:hasContents  inst:IfcReal_90 .

inst:IfcBuilding_50  rdf:type  ifc:IfcBuilding .

inst:IfcGloballyUniqueId_104
        rdf:type           ifc:IfcGloballyUniqueId ;
        express:hasString  "0VCRxBtkXADAyUqmmi8wgL" .

inst:IfcBuilding_50  ifc:globalId_IfcRoot  inst:IfcGloballyUniqueId_104 .

inst:IfcLabel_105  rdf:type  ifc:IfcLabel ;
        express:hasString  "IfcBuilding" .

inst:IfcBuilding_50  ifc:name_IfcRoot  inst:IfcLabel_105 .

inst:IfcLocalPlacement_51
        rdf:type  ifc:IfcLocalPlacement .

inst:IfcBuilding_50  ifc:objectPlacement_IfcProduct  inst:IfcLocalPlacement_51 ;
        ifc:compositionType_IfcSpatialStructureElement  ifc:ELEMENT .

inst:IfcPostalAddress_57
        rdf:type  ifc:IfcPostalAddress .

inst:IfcBuilding_50  ifc:buildingAddress_IfcBuilding  inst:IfcPostalAddress_57 .

inst:IfcReal_List_106
        rdf:type  ifc:IfcReal_List .

inst:IfcDirection_306
        ifc:directionRatios_IfcDirection  inst:IfcReal_List_106 .

inst:IfcReal_List_107
        rdf:type  ifc:IfcReal_List .

inst:IfcReal_List_108
        rdf:type  ifc:IfcReal_List .

inst:IfcReal_List_106
        list:hasContents  inst:IfcReal_90 ;
        list:hasNext      inst:IfcReal_List_107 .

inst:IfcReal_List_107
        list:hasContents  inst:IfcLengthMeasure_87 ;
        list:hasNext      inst:IfcReal_List_108 .

inst:IfcReal_List_108
        list:hasContents  inst:IfcLengthMeasure_87 .

inst:IfcAxis2Placement3D_52
        rdf:type  ifc:IfcAxis2Placement3D .

inst:IfcLocalPlacement_51
        ifc:relativePlacement_IfcLocalPlacement  inst:IfcAxis2Placement3D_52 .

inst:IfcAxis2Placement3D_317
        rdf:type  ifc:IfcAxis2Placement3D .

inst:IfcLocalPlacement_307
        ifc:relativePlacement_IfcLocalPlacement  inst:IfcAxis2Placement3D_317 .

inst:IfcAxis2Placement3D_52
        ifc:location_IfcPlacement  inst:IfcCartesianPoint_9 .

inst:IfcIndexedPolyCurve_308
        rdf:type  ifc:IfcIndexedPolyCurve .

inst:IfcCartesianPointList2D_309
        rdf:type  ifc:IfcCartesianPointList2D .

inst:IfcIndexedPolyCurve_308
        ifc:points_IfcIndexedPolyCurve  inst:IfcCartesianPointList2D_309 .

inst:IfcLineIndex_109
        rdf:type  ifc:IfcLineIndex .

inst:IfcLineIndex_110
        rdf:type  ifc:IfcLineIndex .

inst:IfcPositiveInteger_111
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  1 .

inst:IfcLineIndex_109
        list:hasContents  inst:IfcPositiveInteger_111 ;
        list:hasNext      inst:IfcLineIndex_110 .

inst:IfcPositiveInteger_112
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  2 .

inst:IfcLineIndex_110
        list:hasContents  inst:IfcPositiveInteger_112 .

inst:IfcArcIndex_113  rdf:type  ifc:IfcArcIndex .

inst:IfcArcIndex_114  rdf:type  ifc:IfcArcIndex .

inst:IfcArcIndex_115  rdf:type  ifc:IfcArcIndex .

inst:IfcArcIndex_113  list:hasContents  inst:IfcPositiveInteger_112 ;
        list:hasNext      inst:IfcArcIndex_114 .

inst:IfcArcIndex_114  list:hasContents  inst:IfcDimensionCount_82 ;
        list:hasNext      inst:IfcArcIndex_115 .

inst:IfcPositiveInteger_116
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  4 .

inst:IfcArcIndex_115  list:hasContents  inst:IfcPositiveInteger_116 .

inst:IfcLineIndex_117
        rdf:type  ifc:IfcLineIndex .

inst:IfcLineIndex_118
        rdf:type  ifc:IfcLineIndex .

inst:IfcLineIndex_117
        list:hasContents  inst:IfcPositiveInteger_116 ;
        list:hasNext      inst:IfcLineIndex_118 .

inst:IfcPositiveInteger_119
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  5 .

inst:IfcLineIndex_118
        list:hasContents  inst:IfcPositiveInteger_119 .

inst:IfcArcIndex_120  rdf:type  ifc:IfcArcIndex .

inst:IfcArcIndex_121  rdf:type  ifc:IfcArcIndex .

inst:IfcArcIndex_122  rdf:type  ifc:IfcArcIndex .

inst:IfcArcIndex_120  list:hasContents  inst:IfcPositiveInteger_119 ;
        list:hasNext      inst:IfcArcIndex_121 .

inst:IfcPositiveInteger_123
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  6 .

inst:IfcArcIndex_121  list:hasContents  inst:IfcPositiveInteger_123 ;
        list:hasNext      inst:IfcArcIndex_122 .

inst:IfcPositiveInteger_124
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  7 .

inst:IfcArcIndex_122  list:hasContents  inst:IfcPositiveInteger_124 .

inst:IfcSegmentIndexSelect_List_125
        rdf:type  ifc:IfcSegmentIndexSelect_List .

inst:IfcIndexedPolyCurve_308
        ifc:segments_IfcIndexedPolyCurve  inst:IfcSegmentIndexSelect_List_125 .

inst:IfcSegmentIndexSelect_List_125
        list:hasContents  inst:IfcLineIndex_109 ;
        list:hasNext      inst:IfcSegmentIndexSelect_List_126 .

inst:IfcSegmentIndexSelect_List_126
        rdf:type          ifc:IfcSegmentIndexSelect_List ;
        list:hasContents  inst:IfcArcIndex_113 ;
        list:hasNext      inst:IfcSegmentIndexSelect_List_127 .

inst:IfcSegmentIndexSelect_List_127
        rdf:type          ifc:IfcSegmentIndexSelect_List ;
        list:hasContents  inst:IfcLineIndex_117 ;
        list:hasNext      inst:IfcSegmentIndexSelect_List_128 .

inst:IfcSegmentIndexSelect_List_128
        rdf:type          ifc:IfcSegmentIndexSelect_List ;
        list:hasContents  inst:IfcArcIndex_120 .

inst:IfcBoolean_129  rdf:type  ifc:IfcBoolean ;
        express:hasBoolean  false .

inst:IfcIndexedPolyCurve_308
        ifc:selfIntersect_IfcIndexedPolyCurve  inst:IfcBoolean_129 .

inst:IfcLengthMeasure_List_130
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_131
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_130
        list:hasContents  inst:IfcLengthMeasure_87 ;
        list:hasNext      inst:IfcLengthMeasure_List_131 .

inst:IfcLengthMeasure_List_131
        list:hasContents  inst:IfcLengthMeasure_87 .

inst:IfcLengthMeasure_List_132
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_133
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_134
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "1000.0"^^xsd:double .

inst:IfcLengthMeasure_List_132
        list:hasContents  inst:IfcLengthMeasure_134 ;
        list:hasNext      inst:IfcLengthMeasure_List_133 .

inst:IfcLengthMeasure_List_133
        list:hasContents  inst:IfcLengthMeasure_87 .

inst:IfcLengthMeasure_List_135
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_136
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_137
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "1400.0"^^xsd:double .

inst:IfcLengthMeasure_List_135
        list:hasContents  inst:IfcLengthMeasure_137 ;
        list:hasNext      inst:IfcLengthMeasure_List_136 .

inst:IfcLengthMeasure_138
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "2000.0"^^xsd:double .

inst:IfcLengthMeasure_List_136
        list:hasContents  inst:IfcLengthMeasure_138 .

inst:IfcLengthMeasure_List_139
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_140
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_139
        list:hasContents  inst:IfcLengthMeasure_134 ;
        list:hasNext      inst:IfcLengthMeasure_List_140 .

inst:IfcLengthMeasure_141
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "4000.0"^^xsd:double .

inst:IfcLengthMeasure_List_140
        list:hasContents  inst:IfcLengthMeasure_141 .

inst:IfcLengthMeasure_List_142
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_143
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_142
        list:hasContents  inst:IfcLengthMeasure_87 ;
        list:hasNext      inst:IfcLengthMeasure_List_143 .

inst:IfcLengthMeasure_List_143
        list:hasContents  inst:IfcLengthMeasure_141 .

inst:IfcLengthMeasure_List_144
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_145
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_146
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-400.0"^^xsd:double .

inst:IfcLengthMeasure_List_144
        list:hasContents  inst:IfcLengthMeasure_146 ;
        list:hasNext      inst:IfcLengthMeasure_List_145 .

inst:IfcLengthMeasure_List_145
        list:hasContents  inst:IfcLengthMeasure_138 .

inst:IfcLengthMeasure_List_147
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_148
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_147
        list:hasContents  inst:IfcLengthMeasure_87 ;
        list:hasNext      inst:IfcLengthMeasure_List_148 .

inst:IfcLengthMeasure_List_148
        list:hasContents  inst:IfcLengthMeasure_87 .

inst:IfcLengthMeasure_List_List_149
        rdf:type  ifc:IfcLengthMeasure_List_List .

inst:IfcCartesianPointList2D_309
        ifc:coordList_IfcCartesianPointList2D  inst:IfcLengthMeasure_List_List_149 .

inst:IfcLengthMeasure_List_List_149
        list:hasContents  inst:IfcLengthMeasure_List_130 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_150 .

inst:IfcLengthMeasure_List_List_150
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_132 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_151 .

inst:IfcLengthMeasure_List_List_151
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_135 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_152 .

inst:IfcLengthMeasure_List_List_152
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_139 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_153 .

inst:IfcLengthMeasure_List_List_153
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_142 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_154 .

inst:IfcLengthMeasure_List_List_154
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_144 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_155 .

inst:IfcLengthMeasure_List_List_155
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_147 .

inst:IfcRelContainedInSpatialStructure_54
        rdf:type  ifc:IfcRelContainedInSpatialStructure .

inst:IfcGloballyUniqueId_156
        rdf:type           ifc:IfcGloballyUniqueId ;
        express:hasString  "1LjW27S1r6k9t60_o5PKpn" .

inst:IfcRelContainedInSpatialStructure_54
        ifc:globalId_IfcRoot  inst:IfcGloballyUniqueId_156 .

inst:IfcLabel_157  rdf:type  ifc:IfcLabel ;
        express:hasString  "Building" .

inst:IfcRelContainedInSpatialStructure_54
        ifc:name_IfcRoot  inst:IfcLabel_157 .

inst:IfcText_158  rdf:type  ifc:IfcText ;
        express:hasString  "Building Container for Elements" .

inst:IfcRelContainedInSpatialStructure_54
        ifc:description_IfcRoot  inst:IfcText_158 ;
        ifc:relatedElements_IfcRelContainedInSpatialStructure  inst:IfcSlabStandardCase_302 ;
        ifc:relatingStructure_IfcRelContainedInSpatialStructure  inst:IfcBuilding_50 .

inst:IfcArbitraryClosedProfileDef_310
        rdf:type                       ifc:IfcArbitraryClosedProfileDef ;
        ifc:profileType_IfcProfileDef  ifc:AREA .

inst:IfcLabel_159  rdf:type  ifc:IfcLabel ;
        express:hasString  "Slab Perimeter" .

inst:IfcArbitraryClosedProfileDef_310
        ifc:profileName_IfcProfileDef  inst:IfcLabel_159 ;
        ifc:outerCurve_IfcArbitraryClosedProfileDef  inst:IfcIndexedPolyCurve_308 .

inst:IfcMaterialLayerSetUsage_311
        rdf:type  ifc:IfcMaterialLayerSetUsage .

inst:IfcMaterialLayerSet_205
        rdf:type  ifc:IfcMaterialLayerSet .

inst:IfcMaterialLayerSetUsage_311
        ifc:forLayerSet_IfcMaterialLayerSetUsage  inst:IfcMaterialLayerSet_205 ;
        ifc:layerSetDirection_IfcMaterialLayerSetUsage  ifc:AXIS3 ;
        ifc:directionSense_IfcMaterialLayerSetUsage  ifc:POSITIVE ;
        ifc:offsetFromReferenceLine_IfcMaterialLayerSetUsage  inst:IfcLengthMeasure_100 .

inst:IfcRelAssociatesMaterial_312
        rdf:type  ifc:IfcRelAssociatesMaterial .

inst:IfcGloballyUniqueId_160
        rdf:type           ifc:IfcGloballyUniqueId ;
        express:hasString  "0ChRP8kzb8SAliQ69Ds9or" .

inst:IfcRelAssociatesMaterial_312
        ifc:globalId_IfcRoot  inst:IfcGloballyUniqueId_160 .

inst:IfcLabel_161  rdf:type  ifc:IfcLabel ;
        express:hasString  "MatAssoc" .

inst:IfcRelAssociatesMaterial_312
        ifc:name_IfcRoot  inst:IfcLabel_161 .

inst:IfcText_162  rdf:type  ifc:IfcText ;
        express:hasString  "Material Associates" .

inst:IfcRelAssociatesMaterial_312
        ifc:description_IfcRoot  inst:IfcText_162 ;
        ifc:relatedObjects_IfcRelAssociates  inst:IfcSlabStandardCase_302 ;
        ifc:relatingMaterial_IfcRelAssociatesMaterial  inst:IfcMaterialLayerSetUsage_311 .

inst:IfcLabel_163  rdf:type  ifc:IfcLabel ;
        express:hasString  "Unknown" .

inst:IfcPostalAddress_57
        ifc:region_IfcPostalAddress  inst:IfcLabel_163 .

inst:IfcExtrudedAreaSolid_314
        rdf:type  ifc:IfcExtrudedAreaSolid ;
        ifc:sweptArea_IfcSweptAreaSolid  inst:IfcArbitraryClosedProfileDef_310 ;
        ifc:extrudedDirection_IfcExtrudedAreaSolid  inst:IfcDirection_305 .

inst:IfcPositiveLengthMeasure_164
        rdf:type           ifc:IfcPositiveLengthMeasure ;
        express:hasDouble  "200.0"^^xsd:double .

inst:IfcExtrudedAreaSolid_314
        ifc:depth_IfcExtrudedAreaSolid  inst:IfcPositiveLengthMeasure_164 .

inst:IfcShapeRepresentation_315
        rdf:type  ifc:IfcShapeRepresentation ;
        ifc:contextOfItems_IfcRepresentation  inst:IfcGeometricRepresentationSubContext_12 ;
        ifc:representationIdentifier_IfcRepresentation  inst:IfcLabel_92 .

inst:IfcLabel_165  rdf:type  ifc:IfcLabel ;
        express:hasString  "SweptSolid" .

inst:IfcShapeRepresentation_315
        ifc:representationType_IfcRepresentation  inst:IfcLabel_165 ;
        ifc:items_IfcRepresentation  inst:IfcExtrudedAreaSolid_314 .

inst:IfcRepresentation_List_166
        rdf:type  ifc:IfcRepresentation_List .

inst:IfcProductDefinitionShape_316
        ifc:representations_IfcProductRepresentation  inst:IfcRepresentation_List_166 .

inst:IfcRepresentation_List_166
        list:hasContents  inst:IfcShapeRepresentation_315 .

inst:IfcAxis2Placement3D_317
        ifc:location_IfcPlacement     inst:IfcCartesianPoint_304 ;
        ifc:axis_IfcAxis2Placement3D  inst:IfcDirection_305 ;
        ifc:refDirection_IfcAxis2Placement3D  inst:IfcDirection_306 .

inst:IfcCircleProfileDef_321
        rdf:type                       ifc:IfcCircleProfileDef ;
        ifc:profileType_IfcProfileDef  ifc:AREA .

inst:IfcLabel_167  rdf:type  ifc:IfcLabel ;
        express:hasString  "100DIA" .

inst:IfcCircleProfileDef_321
        ifc:profileName_IfcProfileDef  inst:IfcLabel_167 .

inst:IfcPositiveLengthMeasure_168
        rdf:type           ifc:IfcPositiveLengthMeasure ;
        express:hasDouble  "50.0"^^xsd:double .

inst:IfcCircleProfileDef_321
        ifc:radius_IfcCircleProfileDef  inst:IfcPositiveLengthMeasure_168 .

inst:IfcAxis2Placement3D_322
        rdf:type  ifc:IfcAxis2Placement3D .

inst:IfcCartesianPoint_323
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcAxis2Placement3D_322
        ifc:location_IfcPlacement     inst:IfcCartesianPoint_323 ;
        ifc:axis_IfcAxis2Placement3D  inst:IfcDirection_305 ;
        ifc:refDirection_IfcAxis2Placement3D  inst:IfcDirection_306 .

inst:IfcLengthMeasure_List_169
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_323
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_169 .

inst:IfcLengthMeasure_List_170
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_171
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_172
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "100.0"^^xsd:double .

inst:IfcLengthMeasure_List_169
        list:hasContents  inst:IfcLengthMeasure_172 ;
        list:hasNext      inst:IfcLengthMeasure_List_170 .

inst:IfcLengthMeasure_173
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "300.0"^^xsd:double .

inst:IfcLengthMeasure_List_170
        list:hasContents  inst:IfcLengthMeasure_173 ;
        list:hasNext      inst:IfcLengthMeasure_List_171 .

inst:IfcLengthMeasure_List_171
        list:hasContents  inst:IfcLengthMeasure_100 .

inst:IfcExtrudedAreaSolid_327
        rdf:type                        ifc:IfcExtrudedAreaSolid ;
        ifc:sweptArea_IfcSweptAreaSolid  inst:IfcCircleProfileDef_321 ;
        ifc:position_IfcSweptAreaSolid  inst:IfcAxis2Placement3D_322 ;
        ifc:extrudedDirection_IfcExtrudedAreaSolid  inst:IfcDirection_305 ;
        ifc:depth_IfcExtrudedAreaSolid  inst:IfcPositiveLengthMeasure_164 .

inst:IfcMaterial_200  rdf:type  ifc:IfcMaterial .

inst:IfcLabel_174  rdf:type  ifc:IfcLabel ;
        express:hasString  "Concrete" .

inst:IfcMaterial_200  ifc:name_IfcMaterial  inst:IfcLabel_174 .

inst:IfcOpeningStandardCase_328
        rdf:type  ifc:IfcOpeningStandardCase .

inst:IfcGloballyUniqueId_175
        rdf:type           ifc:IfcGloballyUniqueId ;
        express:hasString  "1WQ6dDOJ5AMB88HOUHiWD1" .

inst:IfcOpeningStandardCase_328
        ifc:globalId_IfcRoot  inst:IfcGloballyUniqueId_175 .

inst:IfcLabel_176  rdf:type  ifc:IfcLabel ;
        express:hasString  "Opening" .

inst:IfcOpeningStandardCase_328
        ifc:name_IfcRoot                inst:IfcLabel_176 ;
        ifc:objectPlacement_IfcProduct  inst:IfcLocalPlacement_51 .

inst:IfcProductDefinitionShape_329
        rdf:type  ifc:IfcProductDefinitionShape .

inst:IfcOpeningStandardCase_328
        ifc:representation_IfcProduct  inst:IfcProductDefinitionShape_329 ;
        ifc:predefinedType_IfcOpeningElement  ifc:OPENING .

inst:IfcRepresentation_List_177
        rdf:type  ifc:IfcRepresentation_List .

inst:IfcProductDefinitionShape_329
        ifc:representations_IfcProductRepresentation  inst:IfcRepresentation_List_177 .

inst:IfcShapeRepresentation_330
        rdf:type  ifc:IfcShapeRepresentation .

inst:IfcRepresentation_List_177
        list:hasContents  inst:IfcShapeRepresentation_330 .

inst:IfcShapeRepresentation_330
        ifc:contextOfItems_IfcRepresentation  inst:IfcGeometricRepresentationSubContext_12 ;
        ifc:representationIdentifier_IfcRepresentation  inst:IfcLabel_92 ;
        ifc:representationType_IfcRepresentation  inst:IfcLabel_165 ;
        ifc:items_IfcRepresentation  inst:IfcExtrudedAreaSolid_327 .

inst:IfcMaterialLayer_203
        rdf:type                       ifc:IfcMaterialLayer ;
        ifc:material_IfcMaterialLayer  inst:IfcMaterial_200 ;
        ifc:layerThickness_IfcMaterialLayer  inst:IfcPositiveLengthMeasure_164 .

inst:IfcLogical_178  rdf:type  ifc:IfcLogical ;
        express:hasLogical  express:FALSE .

inst:IfcMaterialLayer_203
        ifc:isVentilated_IfcMaterialLayer  inst:IfcLogical_178 .

inst:IfcLabel_179  rdf:type  ifc:IfcLabel ;
        express:hasString  "Core" .

inst:IfcMaterialLayer_203
        ifc:name_IfcMaterialLayer  inst:IfcLabel_179 .

inst:IfcRelVoidsElement_332
        rdf:type  ifc:IfcRelVoidsElement .

inst:IfcGloballyUniqueId_180
        rdf:type           ifc:IfcGloballyUniqueId ;
        express:hasString  "23zFgTv0j0rh6X$0Qe8oaV" .

inst:IfcRelVoidsElement_332
        ifc:globalId_IfcRoot  inst:IfcGloballyUniqueId_180 ;
        ifc:relatingBuildingElement_IfcRelVoidsElement  inst:IfcSlabStandardCase_302 ;
        ifc:relatedOpeningElement_IfcRelVoidsElement  inst:IfcOpeningStandardCase_328 .

inst:IfcMaterialLayer_List_181
        rdf:type  ifc:IfcMaterialLayer_List .

inst:IfcMaterialLayerSet_205
        ifc:materialLayers_IfcMaterialLayerSet  inst:IfcMaterialLayer_List_181 .

inst:IfcMaterialLayer_List_181
        list:hasContents  inst:IfcMaterialLayer_203 .

inst:IfcMaterialLayerSet_205
        ifc:layerSetName_IfcMaterialLayerSet  inst:IfcLabel_94 .

inst:IfcRectangleProfileDef_333
        rdf:type                       ifc:IfcRectangleProfileDef ;
        ifc:profileType_IfcProfileDef  ifc:AREA .

inst:IfcLabel_182  rdf:type  ifc:IfcLabel ;
        express:hasString  "RecessRectangle" .

inst:IfcRectangleProfileDef_333
        ifc:profileName_IfcProfileDef  inst:IfcLabel_182 ;
        ifc:xDim_IfcRectangleProfileDef  inst:IfcLengthMeasure_134 .

inst:IfcPositiveLengthMeasure_183
        rdf:type           ifc:IfcPositiveLengthMeasure ;
        express:hasDouble  "500.0"^^xsd:double .

inst:IfcRectangleProfileDef_333
        ifc:yDim_IfcRectangleProfileDef  inst:IfcPositiveLengthMeasure_183 .

inst:IfcRelAssociatesMaterial_206
        rdf:type  ifc:IfcRelAssociatesMaterial .

inst:IfcGloballyUniqueId_184
        rdf:type           ifc:IfcGloballyUniqueId ;
        express:hasString  "2mG3GPofn9w8XVcm9bJ52u" .

inst:IfcRelAssociatesMaterial_206
        ifc:globalId_IfcRoot     inst:IfcGloballyUniqueId_184 ;
        ifc:name_IfcRoot         inst:IfcLabel_161 ;
        ifc:description_IfcRoot  inst:IfcText_162 ;
        ifc:relatedObjects_IfcRelAssociates  inst:IfcSlabType_300 ;
        ifc:relatingMaterial_IfcRelAssociatesMaterial  inst:IfcMaterialLayerSet_205 .

inst:IfcAxis2Placement3D_334
        rdf:type  ifc:IfcAxis2Placement3D .

inst:IfcCartesianPoint_335
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcAxis2Placement3D_334
        ifc:location_IfcPlacement     inst:IfcCartesianPoint_335 ;
        ifc:axis_IfcAxis2Placement3D  inst:IfcDirection_305 ;
        ifc:refDirection_IfcAxis2Placement3D  inst:IfcDirection_306 .

inst:IfcLengthMeasure_List_185
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_335
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_185 .

inst:IfcLengthMeasure_List_186
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_187
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_185
        list:hasContents  inst:IfcPositiveLengthMeasure_183 ;
        list:hasNext      inst:IfcLengthMeasure_List_186 .

inst:IfcLengthMeasure_List_186
        list:hasContents  inst:IfcLengthMeasure_134 ;
        list:hasNext      inst:IfcLengthMeasure_List_187 .

inst:IfcLengthMeasure_188
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-50.0"^^xsd:double .

inst:IfcLengthMeasure_List_187
        list:hasContents  inst:IfcLengthMeasure_188 .

inst:IfcExtrudedAreaSolid_339
        rdf:type                        ifc:IfcExtrudedAreaSolid ;
        ifc:sweptArea_IfcSweptAreaSolid  inst:IfcRectangleProfileDef_333 ;
        ifc:position_IfcSweptAreaSolid  inst:IfcAxis2Placement3D_334 ;
        ifc:extrudedDirection_IfcExtrudedAreaSolid  inst:IfcDirection_305 ;
        ifc:depth_IfcExtrudedAreaSolid  inst:IfcPositiveLengthMeasure_168 .

inst:IfcOpeningStandardCase_340
        rdf:type  ifc:IfcOpeningStandardCase .

inst:IfcGloballyUniqueId_189
        rdf:type           ifc:IfcGloballyUniqueId ;
        express:hasString  "29xhFZFR94UAIjYUaRULkc" .

inst:IfcOpeningStandardCase_340
        ifc:globalId_IfcRoot  inst:IfcGloballyUniqueId_189 .

inst:IfcLabel_190  rdf:type  ifc:IfcLabel ;
        express:hasString  "Recess" .

inst:IfcOpeningStandardCase_340
        ifc:name_IfcRoot                inst:IfcLabel_190 ;
        ifc:objectPlacement_IfcProduct  inst:IfcLocalPlacement_51 .

inst:IfcProductDefinitionShape_341
        rdf:type  ifc:IfcProductDefinitionShape .

inst:IfcOpeningStandardCase_340
        ifc:representation_IfcProduct  inst:IfcProductDefinitionShape_341 ;
        ifc:predefinedType_IfcOpeningElement  ifc:RECESS .

inst:IfcRepresentation_List_191
        rdf:type  ifc:IfcRepresentation_List .

inst:IfcProductDefinitionShape_341
        ifc:representations_IfcProductRepresentation  inst:IfcRepresentation_List_191 .

inst:IfcShapeRepresentation_342
        rdf:type  ifc:IfcShapeRepresentation .

inst:IfcRepresentation_List_191
        list:hasContents  inst:IfcShapeRepresentation_342 .

inst:IfcShapeRepresentation_342
        ifc:contextOfItems_IfcRepresentation  inst:IfcGeometricRepresentationSubContext_12 ;
        ifc:representationIdentifier_IfcRepresentation  inst:IfcLabel_92 ;
        ifc:representationType_IfcRepresentation  inst:IfcLabel_165 ;
        ifc:items_IfcRepresentation  inst:IfcExtrudedAreaSolid_339 .

inst:IfcRelVoidsElement_344
        rdf:type  ifc:IfcRelVoidsElement .

inst:IfcGloballyUniqueId_192
        rdf:type           ifc:IfcGloballyUniqueId ;
        express:hasString  "0HzTSTYerFFPCOvIO24epW" .

inst:IfcRelVoidsElement_344
        ifc:globalId_IfcRoot  inst:IfcGloballyUniqueId_192 ;
        ifc:relatingBuildingElement_IfcRelVoidsElement  inst:IfcSlabStandardCase_302 ;
        ifc:relatedOpeningElement_IfcRelVoidsElement  inst:IfcOpeningStandardCase_340 .

inst:IfcProject_100  rdf:type  ifc:IfcProject .

inst:IfcGloballyUniqueId_193
        rdf:type           ifc:IfcGloballyUniqueId ;
        express:hasString  "25v_KTp7PE7xsqL0jcdJ0K" .

inst:IfcProject_100  ifc:globalId_IfcRoot  inst:IfcGloballyUniqueId_193 ;
        ifc:ownerHistory_IfcRoot  inst:IfcOwnerHistory_6 .

inst:IfcLabel_194  rdf:type  ifc:IfcLabel ;
        express:hasString  "IfcProject" .

inst:IfcProject_100  ifc:name_IfcRoot  inst:IfcLabel_194 ;
        ifc:longName_IfcContext  inst:IfcLabel_194 .

inst:IfcLabel_195  rdf:type  ifc:IfcLabel ;
        express:hasString  "" .

inst:IfcProject_100  ifc:phase_IfcContext  inst:IfcLabel_195 ;
        ifc:representationContexts_IfcContext  inst:IfcGeometricRepresentationContext_13 .

inst:IfcUnitAssignment_101
        rdf:type  ifc:IfcUnitAssignment .

inst:IfcProject_100  ifc:unitsInContext_IfcContext  inst:IfcUnitAssignment_101 .

inst:IfcSIUnit_102  rdf:type  ifc:IfcSIUnit .

inst:IfcUnitAssignment_101
        ifc:units_IfcUnitAssignment  inst:IfcSIUnit_102 .

inst:IfcSIUnit_103  rdf:type  ifc:IfcSIUnit .

inst:IfcUnitAssignment_101
        ifc:units_IfcUnitAssignment  inst:IfcSIUnit_103 .

inst:IfcSIUnit_104  rdf:type  ifc:IfcSIUnit .

inst:IfcUnitAssignment_101
        ifc:units_IfcUnitAssignment  inst:IfcSIUnit_104 .

inst:IfcSIUnit_102  ifc:unitType_IfcNamedUnit  ifc:LENGTHUNIT ;
        ifc:prefix_IfcSIUnit       ifc:MILLI ;
        ifc:name_IfcSIUnit         ifc:METRE .

inst:IfcSIUnit_103  ifc:unitType_IfcNamedUnit  ifc:PLANEANGLEUNIT ;
        ifc:name_IfcSIUnit         ifc:RADIAN .

inst:IfcSIUnit_104  ifc:unitType_IfcNamedUnit  ifc:TIMEUNIT ;
        ifc:name_IfcSIUnit         ifc:SECOND .

inst:IfcRelAggregates_105
        rdf:type  ifc:IfcRelAggregates .

inst:IfcGloballyUniqueId_196
        rdf:type           ifc:IfcGloballyUniqueId ;
        express:hasString  "0xtt_FAs1E0f27x$DsDUKo" .

inst:IfcRelAggregates_105
        ifc:globalId_IfcRoot  inst:IfcGloballyUniqueId_196 .

inst:IfcLabel_197  rdf:type  ifc:IfcLabel ;
        express:hasString  "Project Container" .

inst:IfcRelAggregates_105
        ifc:name_IfcRoot  inst:IfcLabel_197 .

inst:IfcText_198  rdf:type  ifc:IfcText ;
        express:hasString  "Project Container for Buildings" .

inst:IfcRelAggregates_105
        ifc:description_IfcRoot  inst:IfcText_198 ;
        ifc:relatingObject_IfcRelAggregates  inst:IfcProject_100 ;
        ifc:relatedObjects_IfcRelAggregates  inst:IfcBuilding_50 .

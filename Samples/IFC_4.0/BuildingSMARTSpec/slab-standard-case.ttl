# baseURI: http://linkedbuildingdata.net/ifc/resources20200624_184152/
# imports: http://standards.buildingsmart.org/IFC/DEV/IFC4/ADD1/OWL

@prefix ifc:  <http://standards.buildingsmart.org/IFC/DEV/IFC4/ADD1/OWL#> .
@prefix inst:  <http://linkedbuildingdata.net/ifc/resources20200624_184152/> .
@prefix list:  <https://w3id.org/list#> .
@prefix express:  <https://w3id.org/express#> .
@prefix rdf:  <http://www.w3.org/1999/02/22-rdf-syntax-ns#> .
@prefix xsd:  <http://www.w3.org/2001/XMLSchema#> .
@prefix owl:  <http://www.w3.org/2002/07/owl#> .

inst:   rdf:type     owl:Ontology ;
        owl:imports  ifc: .

inst:IfcApplication_1
        rdf:type  ifc:IfcApplication .

inst:IfcOrganization_2
        rdf:type  ifc:IfcOrganization .

inst:IfcApplication_1
        ifc:applicationDeveloper_IfcApplication  inst:IfcOrganization_2 .

inst:IfcLabel_51  rdf:type  ifc:IfcLabel ;
        express:hasString  "1.0.0.0" .

inst:IfcApplication_1
        ifc:version_IfcApplication  inst:IfcLabel_51 .

inst:IfcLabel_52  rdf:type  ifc:IfcLabel ;
        express:hasString  "ggRhinoIFC - Geometry Gym Plug-in for Rhino3d" .

inst:IfcApplication_1
        ifc:applicationFullName_IfcApplication  inst:IfcLabel_52 .

inst:IfcIdentifier_53
        rdf:type           ifc:IfcIdentifier ;
        express:hasString  "ggRhinoIFC" .

inst:IfcApplication_1
        ifc:applicationIdentifier_IfcApplication  inst:IfcIdentifier_53 .

inst:IfcLabel_54  rdf:type  ifc:IfcLabel ;
        express:hasString  "Geometry Gym Pty Ltd" .

inst:IfcOrganization_2
        ifc:name_IfcOrganization  inst:IfcLabel_54 .

inst:IfcPersonAndOrganization_3
        rdf:type  ifc:IfcPersonAndOrganization .

inst:IfcPerson_4  rdf:type  ifc:IfcPerson .

inst:IfcPersonAndOrganization_3
        ifc:thePerson_IfcPersonAndOrganization  inst:IfcPerson_4 ;
        ifc:theOrganization_IfcPersonAndOrganization  inst:IfcOrganization_2 .

inst:IfcIdentifier_55
        rdf:type           ifc:IfcIdentifier ;
        express:hasString  "Jon" .

inst:IfcPerson_4  ifc:identification_IfcPerson  inst:IfcIdentifier_55 ;
        ifc:familyName_IfcPerson      inst:IfcIdentifier_55 .

inst:IfcOwnerHistory_6
        rdf:type                        ifc:IfcOwnerHistory ;
        ifc:owningUser_IfcOwnerHistory  inst:IfcPersonAndOrganization_3 ;
        ifc:owningApplication_IfcOwnerHistory  inst:IfcApplication_1 ;
        ifc:changeAction_IfcOwnerHistory  ifc:ADDED .

inst:IfcTimeStamp_56  rdf:type  ifc:IfcTimeStamp ;
        express:hasInteger  1418084874 .

inst:IfcOwnerHistory_6
        ifc:lastModifiedDate_IfcOwnerHistory  inst:IfcTimeStamp_56 ;
        ifc:creationDate_IfcOwnerHistory  inst:IfcTimeStamp_56 .

inst:IfcGeometricRepresentationContext_7
        rdf:type  ifc:IfcGeometricRepresentationContext .

inst:IfcLabel_57  rdf:type  ifc:IfcLabel ;
        express:hasString  "Model" .

inst:IfcGeometricRepresentationContext_7
        ifc:contextType_IfcRepresentationContext  inst:IfcLabel_57 .

inst:IfcDimensionCount_58
        rdf:type            ifc:IfcDimensionCount ;
        express:hasInteger  3 .

inst:IfcGeometricRepresentationContext_7
        ifc:coordinateSpaceDimension_IfcGeometricRepresentationContext  inst:IfcDimensionCount_58 .

inst:IfcReal_59  rdf:type  ifc:IfcReal ;
        express:hasDouble  "0.0001"^^xsd:double .

inst:IfcGeometricRepresentationContext_7
        ifc:precision_IfcGeometricRepresentationContext  inst:IfcReal_59 .

inst:IfcAxis2Placement3D_8
        rdf:type  ifc:IfcAxis2Placement3D .

inst:IfcGeometricRepresentationContext_7
        ifc:worldCoordinateSystem_IfcGeometricRepresentationContext  inst:IfcAxis2Placement3D_8 .

inst:IfcDirection_10  rdf:type  ifc:IfcDirection .

inst:IfcGeometricRepresentationContext_7
        ifc:trueNorth_IfcGeometricRepresentationContext  inst:IfcDirection_10 .

inst:IfcCartesianPoint_9
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcAxis2Placement3D_8
        ifc:location_IfcPlacement  inst:IfcCartesianPoint_9 .

inst:IfcLengthMeasure_List_60
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_9
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_60 .

inst:IfcLengthMeasure_List_61
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_62
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_63
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "0.0"^^xsd:double .

inst:IfcLengthMeasure_List_60
        list:hasContents  inst:IfcLengthMeasure_63 ;
        list:hasNext      inst:IfcLengthMeasure_List_61 .

inst:IfcLengthMeasure_List_61
        list:hasContents  inst:IfcLengthMeasure_63 ;
        list:hasNext      inst:IfcLengthMeasure_List_62 .

inst:IfcLengthMeasure_List_62
        list:hasContents  inst:IfcLengthMeasure_63 .

inst:IfcReal_List_64  rdf:type  ifc:IfcReal_List .

inst:IfcDirection_10  ifc:directionRatios_IfcDirection  inst:IfcReal_List_64 .

inst:IfcReal_List_65  rdf:type  ifc:IfcReal_List .

inst:IfcReal_List_64  list:hasContents  inst:IfcLengthMeasure_63 ;
        list:hasNext      inst:IfcReal_List_65 .

inst:IfcReal_66  rdf:type  ifc:IfcReal ;
        express:hasDouble  "1.0"^^xsd:double .

inst:IfcReal_List_65  list:hasContents  inst:IfcReal_66 .

inst:IfcGeometricRepresentationSubContext_11
        rdf:type  ifc:IfcGeometricRepresentationSubContext .

inst:IfcLabel_67  rdf:type  ifc:IfcLabel ;
        express:hasString  "Axis" .

inst:IfcGeometricRepresentationSubContext_11
        ifc:contextIdentifier_IfcRepresentationContext  inst:IfcLabel_67 ;
        ifc:contextType_IfcRepresentationContext  inst:IfcLabel_57 ;
        ifc:parentContext_IfcGeometricRepresentationSubContext  inst:IfcGeometricRepresentationContext_7 ;
        ifc:targetView_IfcGeometricRepresentationSubContext  ifc:MODEL_VIEW .

inst:IfcGeometricRepresentationSubContext_12
        rdf:type  ifc:IfcGeometricRepresentationSubContext .

inst:IfcLabel_68  rdf:type  ifc:IfcLabel ;
        express:hasString  "Body" .

inst:IfcGeometricRepresentationSubContext_12
        ifc:contextIdentifier_IfcRepresentationContext  inst:IfcLabel_68 ;
        ifc:contextType_IfcRepresentationContext  inst:IfcLabel_57 ;
        ifc:parentContext_IfcGeometricRepresentationSubContext  inst:IfcGeometricRepresentationContext_7 ;
        ifc:targetView_IfcGeometricRepresentationSubContext  ifc:MODEL_VIEW .

inst:IfcGeometricRepresentationContext_13
        rdf:type  ifc:IfcGeometricRepresentationContext ;
        ifc:contextType_IfcRepresentationContext  inst:IfcLabel_57 ;
        ifc:coordinateSpaceDimension_IfcGeometricRepresentationContext  inst:IfcDimensionCount_58 ;
        ifc:precision_IfcGeometricRepresentationContext  inst:IfcReal_59 ;
        ifc:worldCoordinateSystem_IfcGeometricRepresentationContext  inst:IfcAxis2Placement3D_8 ;
        ifc:trueNorth_IfcGeometricRepresentationContext  inst:IfcDirection_10 .

inst:IfcSlabType_300  rdf:type  ifc:IfcSlabType .

inst:IfcGloballyUniqueId_69
        rdf:type           ifc:IfcGloballyUniqueId ;
        express:hasString  "1rr$WxzZr8s9JAhFh8EyGd" .

inst:IfcSlabType_300  ifc:globalId_IfcRoot  inst:IfcGloballyUniqueId_69 .

inst:IfcLabel_70  rdf:type  ifc:IfcLabel ;
        express:hasString  "200mm Concrete" .

inst:IfcSlabType_300  ifc:name_IfcRoot  inst:IfcLabel_70 ;
        ifc:predefinedType_IfcSlabType  ifc:FLOOR .

inst:IfcRelDefinesByType_301
        rdf:type  ifc:IfcRelDefinesByType .

inst:IfcGloballyUniqueId_71
        rdf:type           ifc:IfcGloballyUniqueId ;
        express:hasString  "3JEKUx4VTBnwf0YrnD0Z5E" .

inst:IfcRelDefinesByType_301
        ifc:globalId_IfcRoot  inst:IfcGloballyUniqueId_71 ;
        ifc:name_IfcRoot      inst:IfcLabel_70 .

inst:IfcSlabStandardCase_302
        rdf:type  ifc:IfcSlabStandardCase .

inst:IfcRelDefinesByType_301
        ifc:relatedObjects_IfcRelDefinesByType  inst:IfcSlabStandardCase_302 ;
        ifc:relatingType_IfcRelDefinesByType  inst:IfcSlabType_300 .

inst:IfcGloballyUniqueId_72
        rdf:type           ifc:IfcGloballyUniqueId ;
        express:hasString  "1uKP3mPKPDSxR9_M8BpQJz" .

inst:IfcSlabStandardCase_302
        ifc:globalId_IfcRoot  inst:IfcGloballyUniqueId_72 .

inst:IfcLocalPlacement_307
        rdf:type  ifc:IfcLocalPlacement .

inst:IfcSlabStandardCase_302
        ifc:objectPlacement_IfcProduct  inst:IfcLocalPlacement_307 .

inst:IfcProductDefinitionShape_316
        rdf:type  ifc:IfcProductDefinitionShape .

inst:IfcSlabStandardCase_302
        ifc:representation_IfcProduct  inst:IfcProductDefinitionShape_316 .

inst:IfcAxis2Placement3D_303
        rdf:type  ifc:IfcAxis2Placement3D .

inst:IfcCartesianPoint_304
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcAxis2Placement3D_303
        ifc:location_IfcPlacement  inst:IfcCartesianPoint_304 .

inst:IfcDirection_305
        rdf:type  ifc:IfcDirection .

inst:IfcAxis2Placement3D_303
        ifc:axis_IfcAxis2Placement3D  inst:IfcDirection_305 .

inst:IfcDirection_306
        rdf:type  ifc:IfcDirection .

inst:IfcAxis2Placement3D_303
        ifc:refDirection_IfcAxis2Placement3D  inst:IfcDirection_306 .

inst:IfcLengthMeasure_List_73
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_304
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_73 .

inst:IfcLengthMeasure_List_74
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_75
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_73
        list:hasContents  inst:IfcLengthMeasure_63 ;
        list:hasNext      inst:IfcLengthMeasure_List_74 .

inst:IfcLengthMeasure_List_74
        list:hasContents  inst:IfcLengthMeasure_63 ;
        list:hasNext      inst:IfcLengthMeasure_List_75 .

inst:IfcLengthMeasure_76
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-200.0"^^xsd:double .

inst:IfcLengthMeasure_List_75
        list:hasContents  inst:IfcLengthMeasure_76 .

inst:IfcReal_List_77  rdf:type  ifc:IfcReal_List .

inst:IfcDirection_305
        ifc:directionRatios_IfcDirection  inst:IfcReal_List_77 .

inst:IfcReal_List_78  rdf:type  ifc:IfcReal_List .

inst:IfcReal_List_79  rdf:type  ifc:IfcReal_List .

inst:IfcReal_List_77  list:hasContents  inst:IfcLengthMeasure_63 ;
        list:hasNext      inst:IfcReal_List_78 .

inst:IfcReal_List_78  list:hasContents  inst:IfcLengthMeasure_63 ;
        list:hasNext      inst:IfcReal_List_79 .

inst:IfcReal_List_79  list:hasContents  inst:IfcReal_66 .

inst:IfcBuilding_50  rdf:type  ifc:IfcBuilding .

inst:IfcGloballyUniqueId_80
        rdf:type           ifc:IfcGloballyUniqueId ;
        express:hasString  "385$tKiSf6rQEPOC3cePN3" .

inst:IfcBuilding_50  ifc:globalId_IfcRoot  inst:IfcGloballyUniqueId_80 .

inst:IfcLabel_81  rdf:type  ifc:IfcLabel ;
        express:hasString  "IfcBuilding" .

inst:IfcBuilding_50  ifc:name_IfcRoot  inst:IfcLabel_81 .

inst:IfcLocalPlacement_51
        rdf:type  ifc:IfcLocalPlacement .

inst:IfcBuilding_50  ifc:objectPlacement_IfcProduct  inst:IfcLocalPlacement_51 ;
        ifc:compositionType_IfcSpatialStructureElement  ifc:ELEMENT .

inst:IfcPostalAddress_57
        rdf:type  ifc:IfcPostalAddress .

inst:IfcBuilding_50  ifc:buildingAddress_IfcBuilding  inst:IfcPostalAddress_57 .

inst:IfcReal_List_82  rdf:type  ifc:IfcReal_List .

inst:IfcDirection_306
        ifc:directionRatios_IfcDirection  inst:IfcReal_List_82 .

inst:IfcReal_List_83  rdf:type  ifc:IfcReal_List .

inst:IfcReal_List_84  rdf:type  ifc:IfcReal_List .

inst:IfcReal_List_82  list:hasContents  inst:IfcReal_66 ;
        list:hasNext      inst:IfcReal_List_83 .

inst:IfcReal_List_83  list:hasContents  inst:IfcLengthMeasure_63 ;
        list:hasNext      inst:IfcReal_List_84 .

inst:IfcReal_List_84  list:hasContents  inst:IfcLengthMeasure_63 .

inst:IfcAxis2Placement3D_52
        rdf:type  ifc:IfcAxis2Placement3D .

inst:IfcLocalPlacement_51
        ifc:relativePlacement_IfcLocalPlacement  inst:IfcAxis2Placement3D_52 .

inst:IfcAxis2Placement3D_317
        rdf:type  ifc:IfcAxis2Placement3D .

inst:IfcLocalPlacement_307
        ifc:relativePlacement_IfcLocalPlacement  inst:IfcAxis2Placement3D_317 .

inst:IfcAxis2Placement3D_52
        ifc:location_IfcPlacement  inst:IfcCartesianPoint_9 .

inst:IfcIndexedPolyCurve_308
        rdf:type  ifc:IfcIndexedPolyCurve .

inst:IfcCartesianPointList2D_309
        rdf:type  ifc:IfcCartesianPointList2D .

inst:IfcIndexedPolyCurve_308
        ifc:points_IfcIndexedPolyCurve  inst:IfcCartesianPointList2D_309 .

inst:IfcLineIndex_85  rdf:type  ifc:IfcLineIndex .

inst:IfcLineIndex_86  rdf:type  ifc:IfcLineIndex .

inst:IfcPositiveInteger_87
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  1 .

inst:IfcLineIndex_85  list:hasContents  inst:IfcPositiveInteger_87 ;
        list:hasNext      inst:IfcLineIndex_86 .

inst:IfcPositiveInteger_88
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  2 .

inst:IfcLineIndex_86  list:hasContents  inst:IfcPositiveInteger_88 .

inst:IfcArcIndex_89  rdf:type  ifc:IfcArcIndex .

inst:IfcArcIndex_90  rdf:type  ifc:IfcArcIndex .

inst:IfcArcIndex_91  rdf:type  ifc:IfcArcIndex .

inst:IfcArcIndex_89  list:hasContents  inst:IfcPositiveInteger_88 ;
        list:hasNext      inst:IfcArcIndex_90 .

inst:IfcArcIndex_90  list:hasContents  inst:IfcDimensionCount_58 ;
        list:hasNext      inst:IfcArcIndex_91 .

inst:IfcPositiveInteger_92
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  4 .

inst:IfcArcIndex_91  list:hasContents  inst:IfcPositiveInteger_92 .

inst:IfcLineIndex_93  rdf:type  ifc:IfcLineIndex .

inst:IfcLineIndex_94  rdf:type  ifc:IfcLineIndex .

inst:IfcLineIndex_93  list:hasContents  inst:IfcPositiveInteger_92 ;
        list:hasNext      inst:IfcLineIndex_94 .

inst:IfcPositiveInteger_95
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  5 .

inst:IfcLineIndex_94  list:hasContents  inst:IfcPositiveInteger_95 .

inst:IfcArcIndex_96  rdf:type  ifc:IfcArcIndex .

inst:IfcArcIndex_97  rdf:type  ifc:IfcArcIndex .

inst:IfcArcIndex_98  rdf:type  ifc:IfcArcIndex .

inst:IfcArcIndex_96  list:hasContents  inst:IfcPositiveInteger_95 ;
        list:hasNext      inst:IfcArcIndex_97 .

inst:IfcPositiveInteger_99
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  6 .

inst:IfcArcIndex_97  list:hasContents  inst:IfcPositiveInteger_99 ;
        list:hasNext      inst:IfcArcIndex_98 .

inst:IfcPositiveInteger_100
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  7 .

inst:IfcArcIndex_98  list:hasContents  inst:IfcPositiveInteger_100 .

inst:IfcSegmentIndexSelect_List_101
        rdf:type  ifc:IfcSegmentIndexSelect_List .

inst:IfcIndexedPolyCurve_308
        ifc:segments_IfcIndexedPolyCurve  inst:IfcSegmentIndexSelect_List_101 .

inst:IfcSegmentIndexSelect_List_101
        list:hasContents  inst:IfcLineIndex_85 ;
        list:hasNext      inst:IfcSegmentIndexSelect_List_102 .

inst:IfcSegmentIndexSelect_List_102
        rdf:type          ifc:IfcSegmentIndexSelect_List ;
        list:hasContents  inst:IfcArcIndex_89 ;
        list:hasNext      inst:IfcSegmentIndexSelect_List_103 .

inst:IfcSegmentIndexSelect_List_103
        rdf:type          ifc:IfcSegmentIndexSelect_List ;
        list:hasContents  inst:IfcLineIndex_93 ;
        list:hasNext      inst:IfcSegmentIndexSelect_List_104 .

inst:IfcSegmentIndexSelect_List_104
        rdf:type          ifc:IfcSegmentIndexSelect_List ;
        list:hasContents  inst:IfcArcIndex_96 .

inst:IfcBoolean_105  rdf:type  ifc:IfcBoolean ;
        express:hasBoolean  false .

inst:IfcIndexedPolyCurve_308
        ifc:selfIntersect_IfcIndexedPolyCurve  inst:IfcBoolean_105 .

inst:IfcLengthMeasure_List_106
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_107
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_106
        list:hasContents  inst:IfcLengthMeasure_63 ;
        list:hasNext      inst:IfcLengthMeasure_List_107 .

inst:IfcLengthMeasure_List_107
        list:hasContents  inst:IfcLengthMeasure_63 .

inst:IfcLengthMeasure_List_108
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_109
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_110
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "1000.0"^^xsd:double .

inst:IfcLengthMeasure_List_108
        list:hasContents  inst:IfcLengthMeasure_110 ;
        list:hasNext      inst:IfcLengthMeasure_List_109 .

inst:IfcLengthMeasure_List_109
        list:hasContents  inst:IfcLengthMeasure_63 .

inst:IfcLengthMeasure_List_111
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_112
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_113
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "1399.99999999987"^^xsd:double .

inst:IfcLengthMeasure_List_111
        list:hasContents  inst:IfcLengthMeasure_113 ;
        list:hasNext      inst:IfcLengthMeasure_List_112 .

inst:IfcLengthMeasure_114
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "2000.0"^^xsd:double .

inst:IfcLengthMeasure_List_112
        list:hasContents  inst:IfcLengthMeasure_114 .

inst:IfcLengthMeasure_List_115
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_116
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_115
        list:hasContents  inst:IfcLengthMeasure_110 ;
        list:hasNext      inst:IfcLengthMeasure_List_116 .

inst:IfcLengthMeasure_117
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "4000.0"^^xsd:double .

inst:IfcLengthMeasure_List_116
        list:hasContents  inst:IfcLengthMeasure_117 .

inst:IfcLengthMeasure_List_118
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_119
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_118
        list:hasContents  inst:IfcLengthMeasure_63 ;
        list:hasNext      inst:IfcLengthMeasure_List_119 .

inst:IfcLengthMeasure_List_119
        list:hasContents  inst:IfcLengthMeasure_117 .

inst:IfcLengthMeasure_List_120
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_121
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_122
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-400.0"^^xsd:double .

inst:IfcLengthMeasure_List_120
        list:hasContents  inst:IfcLengthMeasure_122 ;
        list:hasNext      inst:IfcLengthMeasure_List_121 .

inst:IfcLengthMeasure_List_121
        list:hasContents  inst:IfcLengthMeasure_114 .

inst:IfcLengthMeasure_List_123
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_124
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_123
        list:hasContents  inst:IfcLengthMeasure_63 ;
        list:hasNext      inst:IfcLengthMeasure_List_124 .

inst:IfcLengthMeasure_List_124
        list:hasContents  inst:IfcLengthMeasure_63 .

inst:IfcLengthMeasure_List_List_125
        rdf:type  ifc:IfcLengthMeasure_List_List .

inst:IfcCartesianPointList2D_309
        ifc:coordList_IfcCartesianPointList2D  inst:IfcLengthMeasure_List_List_125 .

inst:IfcLengthMeasure_List_List_125
        list:hasContents  inst:IfcLengthMeasure_List_106 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_126 .

inst:IfcLengthMeasure_List_List_126
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_108 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_127 .

inst:IfcLengthMeasure_List_List_127
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_111 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_128 .

inst:IfcLengthMeasure_List_List_128
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_115 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_129 .

inst:IfcLengthMeasure_List_List_129
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_118 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_130 .

inst:IfcLengthMeasure_List_List_130
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_120 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_131 .

inst:IfcLengthMeasure_List_List_131
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_123 .

inst:IfcRelContainedInSpatialStructure_54
        rdf:type  ifc:IfcRelContainedInSpatialStructure .

inst:IfcGloballyUniqueId_132
        rdf:type           ifc:IfcGloballyUniqueId ;
        express:hasString  "3BarjS4FL9geN9Y48GlNbV" .

inst:IfcRelContainedInSpatialStructure_54
        ifc:globalId_IfcRoot  inst:IfcGloballyUniqueId_132 .

inst:IfcLabel_133  rdf:type  ifc:IfcLabel ;
        express:hasString  "Building" .

inst:IfcRelContainedInSpatialStructure_54
        ifc:name_IfcRoot  inst:IfcLabel_133 .

inst:IfcText_134  rdf:type  ifc:IfcText ;
        express:hasString  "Building Container for Elements" .

inst:IfcRelContainedInSpatialStructure_54
        ifc:description_IfcRoot  inst:IfcText_134 ;
        ifc:relatedElements_IfcRelContainedInSpatialStructure  inst:IfcSlabStandardCase_302 ;
        ifc:relatingStructure_IfcRelContainedInSpatialStructure  inst:IfcBuilding_50 .

inst:IfcArbitraryClosedProfileDef_310
        rdf:type                       ifc:IfcArbitraryClosedProfileDef ;
        ifc:profileType_IfcProfileDef  ifc:AREA .

inst:IfcLabel_135  rdf:type  ifc:IfcLabel ;
        express:hasString  "Slab Perim" .

inst:IfcArbitraryClosedProfileDef_310
        ifc:profileName_IfcProfileDef  inst:IfcLabel_135 ;
        ifc:outerCurve_IfcArbitraryClosedProfileDef  inst:IfcIndexedPolyCurve_308 .

inst:IfcMaterialLayerSetUsage_311
        rdf:type  ifc:IfcMaterialLayerSetUsage .

inst:IfcMaterialLayerSet_205
        rdf:type  ifc:IfcMaterialLayerSet .

inst:IfcMaterialLayerSetUsage_311
        ifc:forLayerSet_IfcMaterialLayerSetUsage  inst:IfcMaterialLayerSet_205 ;
        ifc:layerSetDirection_IfcMaterialLayerSetUsage  ifc:AXIS3 ;
        ifc:directionSense_IfcMaterialLayerSetUsage  ifc:POSITIVE ;
        ifc:offsetFromReferenceLine_IfcMaterialLayerSetUsage  inst:IfcLengthMeasure_76 .

inst:IfcRelAssociatesMaterial_312
        rdf:type  ifc:IfcRelAssociatesMaterial .

inst:IfcGloballyUniqueId_136
        rdf:type           ifc:IfcGloballyUniqueId ;
        express:hasString  "33b_pm1lv2HhvpkPXAXPdW" .

inst:IfcRelAssociatesMaterial_312
        ifc:globalId_IfcRoot  inst:IfcGloballyUniqueId_136 .

inst:IfcLabel_137  rdf:type  ifc:IfcLabel ;
        express:hasString  "MatAssoc" .

inst:IfcRelAssociatesMaterial_312
        ifc:name_IfcRoot  inst:IfcLabel_137 .

inst:IfcText_138  rdf:type  ifc:IfcText ;
        express:hasString  "Material Associates" .

inst:IfcRelAssociatesMaterial_312
        ifc:description_IfcRoot  inst:IfcText_138 ;
        ifc:relatedObjects_IfcRelAssociates  inst:IfcSlabStandardCase_302 ;
        ifc:relatingMaterial_IfcRelAssociatesMaterial  inst:IfcMaterialLayerSetUsage_311 .

inst:IfcLabel_139  rdf:type  ifc:IfcLabel ;
        express:hasString  "Unknown" .

inst:IfcPostalAddress_57
        ifc:region_IfcPostalAddress  inst:IfcLabel_139 .

inst:IfcExtrudedAreaSolid_314
        rdf:type  ifc:IfcExtrudedAreaSolid ;
        ifc:sweptArea_IfcSweptAreaSolid  inst:IfcArbitraryClosedProfileDef_310 ;
        ifc:extrudedDirection_IfcExtrudedAreaSolid  inst:IfcDirection_305 .

inst:IfcPositiveLengthMeasure_140
        rdf:type           ifc:IfcPositiveLengthMeasure ;
        express:hasDouble  "200.0"^^xsd:double .

inst:IfcExtrudedAreaSolid_314
        ifc:depth_IfcExtrudedAreaSolid  inst:IfcPositiveLengthMeasure_140 .

inst:IfcShapeRepresentation_315
        rdf:type  ifc:IfcShapeRepresentation ;
        ifc:contextOfItems_IfcRepresentation  inst:IfcGeometricRepresentationSubContext_12 ;
        ifc:representationIdentifier_IfcRepresentation  inst:IfcLabel_68 .

inst:IfcLabel_141  rdf:type  ifc:IfcLabel ;
        express:hasString  "SweptSolid" .

inst:IfcShapeRepresentation_315
        ifc:representationType_IfcRepresentation  inst:IfcLabel_141 ;
        ifc:items_IfcRepresentation  inst:IfcExtrudedAreaSolid_314 .

inst:IfcRepresentation_List_142
        rdf:type  ifc:IfcRepresentation_List .

inst:IfcProductDefinitionShape_316
        ifc:representations_IfcProductRepresentation  inst:IfcRepresentation_List_142 .

inst:IfcRepresentation_List_142
        list:hasContents  inst:IfcShapeRepresentation_315 .

inst:IfcAxis2Placement3D_317
        ifc:location_IfcPlacement     inst:IfcCartesianPoint_304 ;
        ifc:axis_IfcAxis2Placement3D  inst:IfcDirection_305 ;
        ifc:refDirection_IfcAxis2Placement3D  inst:IfcDirection_306 .

inst:IfcMaterial_200  rdf:type  ifc:IfcMaterial .

inst:IfcLabel_143  rdf:type  ifc:IfcLabel ;
        express:hasString  "Concrete" .

inst:IfcMaterial_200  ifc:name_IfcMaterial  inst:IfcLabel_143 .

inst:IfcMaterialLayer_203
        rdf:type                       ifc:IfcMaterialLayer ;
        ifc:material_IfcMaterialLayer  inst:IfcMaterial_200 ;
        ifc:layerThickness_IfcMaterialLayer  inst:IfcPositiveLengthMeasure_140 .

inst:IfcLogical_144  rdf:type  ifc:IfcLogical ;
        express:hasLogical  express:FALSE .

inst:IfcMaterialLayer_203
        ifc:isVentilated_IfcMaterialLayer  inst:IfcLogical_144 .

inst:IfcLabel_145  rdf:type  ifc:IfcLabel ;
        express:hasString  "Core" .

inst:IfcMaterialLayer_203
        ifc:name_IfcMaterialLayer  inst:IfcLabel_145 .

inst:IfcMaterialLayer_List_146
        rdf:type  ifc:IfcMaterialLayer_List .

inst:IfcMaterialLayerSet_205
        ifc:materialLayers_IfcMaterialLayerSet  inst:IfcMaterialLayer_List_146 .

inst:IfcMaterialLayer_List_146
        list:hasContents  inst:IfcMaterialLayer_203 .

inst:IfcMaterialLayerSet_205
        ifc:layerSetName_IfcMaterialLayerSet  inst:IfcLabel_70 .

inst:IfcRelAssociatesMaterial_206
        rdf:type  ifc:IfcRelAssociatesMaterial .

inst:IfcGloballyUniqueId_147
        rdf:type           ifc:IfcGloballyUniqueId ;
        express:hasString  "3T98a4jwDANuzfZGb2gESH" .

inst:IfcRelAssociatesMaterial_206
        ifc:globalId_IfcRoot     inst:IfcGloballyUniqueId_147 ;
        ifc:name_IfcRoot         inst:IfcLabel_137 ;
        ifc:description_IfcRoot  inst:IfcText_138 ;
        ifc:relatedObjects_IfcRelAssociates  inst:IfcSlabType_300 ;
        ifc:relatingMaterial_IfcRelAssociatesMaterial  inst:IfcMaterialLayerSet_205 .

inst:IfcProject_100  rdf:type  ifc:IfcProject .

inst:IfcGloballyUniqueId_148
        rdf:type           ifc:IfcGloballyUniqueId ;
        express:hasString  "3IU0sKDyX2mAWXvsGInP94" .

inst:IfcProject_100  ifc:globalId_IfcRoot  inst:IfcGloballyUniqueId_148 ;
        ifc:ownerHistory_IfcRoot  inst:IfcOwnerHistory_6 .

inst:IfcLabel_149  rdf:type  ifc:IfcLabel ;
        express:hasString  "IfcProject" .

inst:IfcProject_100  ifc:name_IfcRoot  inst:IfcLabel_149 ;
        ifc:longName_IfcContext  inst:IfcLabel_149 .

inst:IfcLabel_150  rdf:type  ifc:IfcLabel ;
        express:hasString  "" .

inst:IfcProject_100  ifc:phase_IfcContext  inst:IfcLabel_150 ;
        ifc:representationContexts_IfcContext  inst:IfcGeometricRepresentationContext_13 .

inst:IfcUnitAssignment_101
        rdf:type  ifc:IfcUnitAssignment .

inst:IfcProject_100  ifc:unitsInContext_IfcContext  inst:IfcUnitAssignment_101 .

inst:IfcSIUnit_102  rdf:type  ifc:IfcSIUnit .

inst:IfcUnitAssignment_101
        ifc:units_IfcUnitAssignment  inst:IfcSIUnit_102 .

inst:IfcSIUnit_103  rdf:type  ifc:IfcSIUnit .

inst:IfcUnitAssignment_101
        ifc:units_IfcUnitAssignment  inst:IfcSIUnit_103 .

inst:IfcSIUnit_104  rdf:type  ifc:IfcSIUnit .

inst:IfcUnitAssignment_101
        ifc:units_IfcUnitAssignment  inst:IfcSIUnit_104 .

inst:IfcSIUnit_102  ifc:unitType_IfcNamedUnit  ifc:LENGTHUNIT ;
        ifc:prefix_IfcSIUnit       ifc:MILLI ;
        ifc:name_IfcSIUnit         ifc:METRE .

inst:IfcSIUnit_103  ifc:unitType_IfcNamedUnit  ifc:PLANEANGLEUNIT ;
        ifc:name_IfcSIUnit         ifc:RADIAN .

inst:IfcSIUnit_104  ifc:unitType_IfcNamedUnit  ifc:TIMEUNIT ;
        ifc:name_IfcSIUnit         ifc:SECOND .

inst:IfcRelAggregates_105
        rdf:type  ifc:IfcRelAggregates .

inst:IfcGloballyUniqueId_151
        rdf:type           ifc:IfcGloballyUniqueId ;
        express:hasString  "3lG7RzYM5Axu8hxBvgUmsp" .

inst:IfcRelAggregates_105
        ifc:globalId_IfcRoot  inst:IfcGloballyUniqueId_151 .

inst:IfcLabel_152  rdf:type  ifc:IfcLabel ;
        express:hasString  "Project Container" .

inst:IfcRelAggregates_105
        ifc:name_IfcRoot  inst:IfcLabel_152 .

inst:IfcText_153  rdf:type  ifc:IfcText ;
        express:hasString  "Project Container for Buildings" .

inst:IfcRelAggregates_105
        ifc:description_IfcRoot  inst:IfcText_153 ;
        ifc:relatingObject_IfcRelAggregates  inst:IfcProject_100 ;
        ifc:relatedObjects_IfcRelAggregates  inst:IfcBuilding_50 .

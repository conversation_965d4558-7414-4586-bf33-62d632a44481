ISO-10303-21;
HEADER;
FILE_DESCRIPTION(('ViewDefinition [CoordinationView]'),'2;1');
FILE_NAME('','2020-10-30T00:18:59',(),(),'IfcOpenShell 0.6.0b0','IfcOpenShell 0.6.0b0','');
FILE_SCHEMA(('IFC4'));
ENDSEC;
DATA;
#1=IFCPROJECT('1j1i_xK_X5Tf3O1Ox2mOxp',$,'P1','project used for the unit test case',$,'Default project',$,(#5),#10);
#2=IFCBUILDING('3uvY$5FxrCov51rMJmsbC8',$,'Grasshopper Building','GH Building',$,#16,$,'GH Building',.ELEMENT.,$,$,$);
#3=IFCSLAB('35DHdKriP6OQIQhodN2chQ',$,'Slab 1','slab 1 used for the unit test case',$,#21,#31,$,.FLOOR.);
#4=IFCSHAPEREPRESENTATION(#7,'Body','Tessellation',(#32));
#5=IFCGEOMETRICREPRESENTATIONCONTEXT($,'Model',3,1.E-08,#34,#38);
#6=IFCGEOMETRICREPRESENTATIONSUBCONTEXT('Axis','Model',*,*,*,*,#5,$,.MODEL_VIEW.,$);
#7=IFCGEOMETRICREPRESENTATIONSUBCONTEXT('Body','Model',*,*,*,*,#5,$,.MODEL_VIEW.,$);
#8=IFCRELCONTAINEDINSPATIALSTRUCTURE('3T9M5M_z521OJsm4kWHgkR',$,'Building','Building Container for Elements',(#3),#2);
#9=IFCRELAGGREGATES('2uV5ZjLCz2ZO1ngyJeKRdY',$,'Project Container','Project Container for Buildings',#1,(#2));
#10=IFCUNITASSIGNMENT((#11,#12,#13,#14,#15));
#11=IFCSIUNIT(*,.LENGTHUNIT.,$,.METRE.);
#12=IFCSIUNIT(*,.AREAUNIT.,$,.SQUARE_METRE.);
#13=IFCSIUNIT(*,.VOLUMEUNIT.,$,.CUBIC_METRE.);
#14=IFCSIUNIT(*,.PLANEANGLEUNIT.,$,.RADIAN.);
#15=IFCSIUNIT(*,.TIMEUNIT.,$,.SECOND.);
#16=IFCLOCALPLACEMENT($,#17);
#17=IFCAXIS2PLACEMENT3D(#18,#19,#20);
#18=IFCCARTESIANPOINT((0.,0.,0.));
#19=IFCDIRECTION((0.,0.,1.));
#20=IFCDIRECTION((1.,0.,0.));
#21=IFCLOCALPLACEMENT(#22,#27);
#22=IFCLOCALPLACEMENT($,#23);
#23=IFCAXIS2PLACEMENT3D(#24,#25,#26);
#24=IFCCARTESIANPOINT((0.,0.,0.));
#25=IFCDIRECTION((0.,0.,1.));
#26=IFCDIRECTION((1.,0.,0.));
#27=IFCAXIS2PLACEMENT3D(#28,#29,#30);
#28=IFCCARTESIANPOINT((0.,0.,0.));
#29=IFCDIRECTION((0.,0.,1.));
#30=IFCDIRECTION((1.,0.,0.));
#31=IFCPRODUCTDEFINITIONSHAPE($,$,(#4));
#32=IFCTRIANGULATEDFACESET(#33,$,.T.,((5,4,6),(1,5,6),(4,5,3),(2,5,1),(3,5,7),(5,2,9),(2,1,10),(1,6,11),(6,4,12),(4,3,8),(7,11,12),(10,11,7),(12,8,7),(9,10,7),(3,7,8),(5,9,7),(2,10,9),(1,11,10),(6,12,11),(4,8,12)),$);
#33=IFCCARTESIANPOINTLIST3D(((-5.,-8.66025352478027,0.),(5.,-8.66025352478027,0.),(5.,8.66025352478027,0.),(-5.,8.66025352478027,0.),(10.,0.,0.),(-10.,1.22E-15,0.),(10.,0.,-0.300000011920929),(5.,8.66025352478027,-0.300000011920929),(5.,-8.66025352478027,-0.300000011920929),(-5.,-8.66025352478027,-0.300000011920929),(-10.,1.22E-15,-0.300000011920929),(-5.,8.66025352478027,-0.300000011920929)));
#34=IFCAXIS2PLACEMENT3D(#35,#36,#37);
#35=IFCCARTESIANPOINT((0.,0.,0.));
#36=IFCDIRECTION((0.,0.,1.));
#37=IFCDIRECTION((1.,0.,0.));
#38=IFCDIRECTION((0.,1.));
ENDSEC;
END-ISO-10303-21;

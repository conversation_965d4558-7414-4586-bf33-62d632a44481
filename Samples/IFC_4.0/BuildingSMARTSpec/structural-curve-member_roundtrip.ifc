ISO-10303-21;
HEADER;
FILE_DESCRIPTION(('ViewDefinition [CoordinationView]'),'2;1');
FILE_NAME('','2020-10-30T00:18:59',(),(),'IfcOpenShell 0.6.0b0','IfcOpenShell 0.6.0b0','');
FILE_SCHEMA(('IFC4'));
ENDSEC;
DATA;
#1=IFCPROJECT('0QjBRF7yLCTh4HRF3GOF1t',#22,'Project',$,$,$,$,(#23,#24),#46);
#2=IFCSTRUCTURALANALYSISMODEL('0VYesmxUHFNez26MoJx5F3',#22,'Structural Analysis #1',$,$,.NOTDEFINED.,#173,(#10),(#12),#175);
#3=IFCSTRUCTURALCURVEMEMBER('3eXlZ8csrAvfIIXVwC_gVP',#22,'Curve Member #1',$,$,$,#178,.RIGID_JOINED_MEMBER.,#185);
#4=IFCSTRUCTURALPOINTCONNECTION('3539fAVu96i8mFr0cgUqeI',#22,'Point Connection #1',$,$,$,#186,#190,$);
#5=IFCSTRUCTURALPOINTCONNECTION('2mc6ibF258HPIpTmqg6DSl',#22,'Point Connection #2',$,$,$,#191,$,$);
#6=IFCSTRUCTURALCURVEMEMBER('3jULd7ui93JOXl5trkpgTT',#22,'Curve Member #2',$,$,$,#195,.RIGID_JOINED_MEMBER.,#202);
#7=IFCSTRUCTURALPOINTCONNECTION('1dqi3aUQP3yeww5muaF15h',#22,'Point Connection #3',$,$,$,#203,#207,$);
#8=IFCSTRUCTURALPOINTCONNECTION('0IHrRf6abAZwDys7n7fbS2',#22,'Point Connection #4',$,$,$,#208,$,$);
#9=IFCSTRUCTURALCURVEMEMBER('25vEW7EzrBTvz5cbNWzhP$',#22,'Curve Member #3',$,$,$,#212,.RIGID_JOINED_MEMBER.,#219);
#10=IFCSTRUCTURALLOADCASE('2fv4DZfY55exwX8QDy8dmw',#22,'Structural Load Case #1',$,$,.LOAD_CASE.,.NOTDEFINED.,.NOTDEFINED.,1.,$,(0.,0.,0.));
#11=IFCSTRUCTURALCURVEACTION('2WSwGyLsrFNA9TLOq_ifyd',#22,'Structural Curve Action #1',$,$,$,$,#220,.GLOBAL_COORDS.,.F.,$,.LINEAR.);
#12=IFCSTRUCTURALRESULTGROUP('3nK7dm3u9EYhoBHOTo765A',#22,$,$,$,.FIRST_ORDER_THEORY.,#10,.T.);
#13=IFCSTRUCTURALPOINTREACTION('0Ci9_J7iLDhuBtYHGEpcTw',#22,$,$,$,$,$,#223,.GLOBAL_COORDS.);
#14=IFCSTRUCTURALPOINTREACTION('1VIuS9lz50U9$sbX2t8CSb',#22,$,$,$,$,$,#224,.GLOBAL_COORDS.);
#15=IFCSTRUCTURALPOINTREACTION('2Xxob9ZATFeQmLnunjg4E$',#22,$,$,$,$,$,#225,.GLOBAL_COORDS.);
#16=IFCSTRUCTURALPOINTREACTION('2YqhTmxQv6_AQPAcaE66Xu',#22,$,$,$,$,$,#226,.GLOBAL_COORDS.);
#17=IFCSTRUCTURALPOINTREACTION('1b1zM4uHz8exw0fvIGvpGQ',#22,$,$,$,$,$,#227,.GLOBAL_COORDS.);
#18=IFCSTRUCTURALPOINTREACTION('33628eH254VAe0O7d63nzI',#22,$,$,$,$,$,#228,.GLOBAL_COORDS.);
#19=IFCSTRUCTURALCURVEREACTION('0SH7YcIWrB8Q4VcWjfXpnn',#22,$,$,$,$,$,#229,.GLOBAL_COORDS.,.DISCRETE.);
#20=IFCSTRUCTURALCURVEREACTION('1L3TtLyyPEzgk1VyvDrWiH',#22,$,$,$,$,$,#232,.GLOBAL_COORDS.,.DISCRETE.);
#21=IFCSTRUCTURALCURVEREACTION('2ssucs0rX8KgBecASOwgSd',#22,$,$,$,$,$,#235,.GLOBAL_COORDS.,.DISCRETE.);
#22=IFCOWNERHISTORY(#238,#241,.READWRITE.,.NOCHANGE.,$,$,$,1320677205);
#23=IFCGEOMETRICREPRESENTATIONCONTEXT('3D','Model',3,1.E-05,#243,$);
#24=IFCGEOMETRICREPRESENTATIONCONTEXT('2D','Plan',2,1.E-05,#245,$);
#25=IFCRELDECLARES('1Frhk31Z12CR9mBbodz9XE',#22,'GROUP',$,#1,(#2));
#26=IFCRELASSIGNSTOGROUP('3mDDAcu390$A5orhhUv4qv',#22,$,$,(#4,#5,#3,#7,#8,#6,#9),.PRODUCT.,#2);
#27=IFCRELCONNECTSSTRUCTURALMEMBER('2Z9w70JuDEUx6TnggLE2wU',#22,$,$,#3,#4,$,$,$,$);
#28=IFCRELCONNECTSSTRUCTURALMEMBER('1GClK7cwT80xzpZuaAlGXp',#22,$,$,#3,#5,$,$,$,$);
#29=IFCRELCONNECTSSTRUCTURALMEMBER('0r1xJykBf1OOYUn7dRt3DM',#22,$,$,#6,#7,$,$,$,$);
#30=IFCRELCONNECTSSTRUCTURALMEMBER('1$D3QsVBj2kf4iUp5hUEu2',#22,$,$,#6,#8,$,$,$,$);
#31=IFCRELCONNECTSSTRUCTURALMEMBER('3ZUyJTZMHEev9njAeNDQUT',#22,$,$,#9,#5,$,$,$,$);
#32=IFCRELCONNECTSSTRUCTURALMEMBER('3Y3WZZzV16XQ$1wEZLWjJX',#22,$,$,#9,#8,$,$,$,$);
#33=IFCRELCONNECTSSTRUCTURALACTIVITY('0XvroPpOb4FPsGBZQ$pgtA',#22,$,$,#9,#11);
#34=IFCRELASSIGNSTOGROUP('2OygXKIkL35eDtUalQjese',#22,$,$,(#11),.PRODUCT.,#10);
#35=IFCRELASSOCIATESMATERIAL('01kVXT9hb7C80NqRflZZF9',#22,$,$,(#3,#6,#9),#247);
#36=IFCRELCONNECTSSTRUCTURALACTIVITY('2nVw9fFML1G8QuiGnXOdEh',#22,$,$,#4,#13);
#37=IFCRELASSIGNSTOGROUP('2dN5hErLP9zB0e5nA$yT0F',#22,$,$,(#13,#14,#15,#16,#17,#18,#19,#20,#21),.PRODUCT.,#12);
#38=IFCRELCONNECTSSTRUCTURALACTIVITY('2l0cjBJHH8nxFBsGxDKSfi',#22,$,$,#4,#14);
#39=IFCRELCONNECTSSTRUCTURALACTIVITY('2iaDWtrbn6W9TfDxfsikIH',#22,$,$,#5,#15);
#40=IFCRELCONNECTSSTRUCTURALACTIVITY('0yGyLgS9T3Y9D6VtgcdAkS',#22,$,$,#7,#16);
#41=IFCRELCONNECTSSTRUCTURALACTIVITY('0Whra2O$v9wePeIU6IImNQ',#22,$,$,#7,#17);
#42=IFCRELCONNECTSSTRUCTURALACTIVITY('2ukIn71Pr2VwtcAQ5GPi8T',#22,$,$,#8,#18);
#43=IFCRELCONNECTSSTRUCTURALACTIVITY('2vTn7XCur6sByitir9a9jj',#22,$,$,#3,#19);
#44=IFCRELCONNECTSSTRUCTURALACTIVITY('23GMRW_cf7tA667rAWkQOC',#22,$,$,#6,#20);
#45=IFCRELCONNECTSSTRUCTURALACTIVITY('1tmGmLIfTFnBfWx$tia32R',#22,$,$,#9,#21);
#46=IFCUNITASSIGNMENT((#47,#50,#53,#56,#59,#62,#65,#68,#77,#90,#99,#108,#117,#122,#127,#136,#145,#158,#163,#168));
#47=IFCCONVERSIONBASEDUNIT($,.AREAUNIT.,'square inch',#48);
#48=IFCMEASUREWITHUNIT(IFCAREAMEASURE(0.0006452),#49);
#49=IFCSIUNIT(*,.AREAUNIT.,$,.SQUARE_METRE.);
#50=IFCCONVERSIONBASEDUNIT($,.FORCEUNIT.,'pound-force',#51);
#51=IFCMEASUREWITHUNIT(IFCMASSMEASURE(4.44822162),#52);
#52=IFCSIUNIT(*,.FORCEUNIT.,$,.NEWTON.);
#53=IFCCONVERSIONBASEDUNIT($,.LENGTHUNIT.,'inch',#54);
#54=IFCMEASUREWITHUNIT(IFCLENGTHMEASURE(0.0254),#55);
#55=IFCSIUNIT(*,.LENGTHUNIT.,$,.METRE.);
#56=IFCCONVERSIONBASEDUNIT($,.MASSUNIT.,'pound',#57);
#57=IFCMEASUREWITHUNIT(IFCMASSMEASURE(0.45359237),#58);
#58=IFCSIUNIT(*,.MASSUNIT.,.KILO.,.GRAM.);
#59=IFCCONVERSIONBASEDUNIT($,.PLANEANGLEUNIT.,'degree',#60);
#60=IFCMEASUREWITHUNIT(IFCPLANEANGLEMEASURE(0.0174532925199433),#61);
#61=IFCSIUNIT(*,.PLANEANGLEUNIT.,$,.RADIAN.);
#62=IFCCONVERSIONBASEDUNIT($,.PRESSUREUNIT.,'pound-force per square inch',#63);
#63=IFCMEASUREWITHUNIT(IFCPRESSUREMEASURE(6894.7572932),#64);
#64=IFCSIUNIT(*,.PRESSUREUNIT.,$,.PASCAL.);
#65=IFCCONVERSIONBASEDUNIT($,.VOLUMEUNIT.,'cubic inch',#66);
#66=IFCMEASUREWITHUNIT(IFCVOLUMEMEASURE(1.639E-05),#67);
#67=IFCSIUNIT(*,.VOLUMEUNIT.,$,.CUBIC_METRE.);
#68=IFCDERIVEDUNIT((#69,#73),.LINEARFORCEUNIT.,$);
#69=IFCDERIVEDUNITELEMENT(#70,1);
#70=IFCCONVERSIONBASEDUNIT($,.FORCEUNIT.,'pound-force',#71);
#71=IFCMEASUREWITHUNIT(IFCMASSMEASURE(4.44822162),#72);
#72=IFCSIUNIT(*,.FORCEUNIT.,$,.NEWTON.);
#73=IFCDERIVEDUNITELEMENT(#74,-1);
#74=IFCCONVERSIONBASEDUNIT($,.LENGTHUNIT.,'inch',#75);
#75=IFCMEASUREWITHUNIT(IFCLENGTHMEASURE(0.0254),#76);
#76=IFCSIUNIT(*,.LENGTHUNIT.,$,.METRE.);
#77=IFCDERIVEDUNIT((#78,#82,#86),.LINEARMOMENTUNIT.,$);
#78=IFCDERIVEDUNITELEMENT(#79,1);
#79=IFCCONVERSIONBASEDUNIT($,.FORCEUNIT.,'pound-force',#80);
#80=IFCMEASUREWITHUNIT(IFCMASSMEASURE(4.44822162),#81);
#81=IFCSIUNIT(*,.FORCEUNIT.,$,.NEWTON.);
#82=IFCDERIVEDUNITELEMENT(#83,1);
#83=IFCCONVERSIONBASEDUNIT($,.LENGTHUNIT.,'inch',#84);
#84=IFCMEASUREWITHUNIT(IFCLENGTHMEASURE(0.0254),#85);
#85=IFCSIUNIT(*,.LENGTHUNIT.,$,.METRE.);
#86=IFCDERIVEDUNITELEMENT(#87,-1);
#87=IFCCONVERSIONBASEDUNIT($,.LENGTHUNIT.,'inch',#88);
#88=IFCMEASUREWITHUNIT(IFCLENGTHMEASURE(0.0254),#89);
#89=IFCSIUNIT(*,.LENGTHUNIT.,$,.METRE.);
#90=IFCDERIVEDUNIT((#91,#95),.LINEARSTIFFNESSUNIT.,$);
#91=IFCDERIVEDUNITELEMENT(#92,1);
#92=IFCCONVERSIONBASEDUNIT($,.FORCEUNIT.,'pound-force',#93);
#93=IFCMEASUREWITHUNIT(IFCMASSMEASURE(4.44822162),#94);
#94=IFCSIUNIT(*,.FORCEUNIT.,$,.NEWTON.);
#95=IFCDERIVEDUNITELEMENT(#96,-1);
#96=IFCCONVERSIONBASEDUNIT($,.LENGTHUNIT.,'inch',#97);
#97=IFCMEASUREWITHUNIT(IFCLENGTHMEASURE(0.0254),#98);
#98=IFCSIUNIT(*,.LENGTHUNIT.,$,.METRE.);
#99=IFCDERIVEDUNIT((#100,#104),.MASSDENSITYUNIT.,$);
#100=IFCDERIVEDUNITELEMENT(#101,1);
#101=IFCCONVERSIONBASEDUNIT($,.MASSUNIT.,'pound',#102);
#102=IFCMEASUREWITHUNIT(IFCMASSMEASURE(0.45359237),#103);
#103=IFCSIUNIT(*,.MASSUNIT.,.KILO.,.GRAM.);
#104=IFCDERIVEDUNITELEMENT(#105,-1);
#105=IFCCONVERSIONBASEDUNIT($,.VOLUMEUNIT.,'cubic inch',#106);
#106=IFCMEASUREWITHUNIT(IFCVOLUMEMEASURE(1.639E-05),#107);
#107=IFCSIUNIT(*,.VOLUMEUNIT.,$,.CUBIC_METRE.);
#108=IFCDERIVEDUNIT((#109,#113),.MASSPERLENGTHUNIT.,$);
#109=IFCDERIVEDUNITELEMENT(#110,1);
#110=IFCCONVERSIONBASEDUNIT($,.MASSUNIT.,'pound',#111);
#111=IFCMEASUREWITHUNIT(IFCMASSMEASURE(0.45359237),#112);
#112=IFCSIUNIT(*,.MASSUNIT.,.KILO.,.GRAM.);
#113=IFCDERIVEDUNITELEMENT(#114,-1);
#114=IFCCONVERSIONBASEDUNIT($,.LENGTHUNIT.,'inch',#115);
#115=IFCMEASUREWITHUNIT(IFCLENGTHMEASURE(0.0254),#116);
#116=IFCSIUNIT(*,.LENGTHUNIT.,$,.METRE.);
#117=IFCDERIVEDUNIT((#118),.MODULUSOFELASTICITYUNIT.,$);
#118=IFCDERIVEDUNITELEMENT(#119,1);
#119=IFCCONVERSIONBASEDUNIT($,.PRESSUREUNIT.,'pound-force per square inch',#120);
#120=IFCMEASUREWITHUNIT(IFCPRESSUREMEASURE(6894.7572932),#121);
#121=IFCSIUNIT(*,.PRESSUREUNIT.,$,.PASCAL.);
#122=IFCDERIVEDUNIT((#123),.MOMENTOFINERTIAUNIT.,$);
#123=IFCDERIVEDUNITELEMENT(#124,4);
#124=IFCCONVERSIONBASEDUNIT($,.LENGTHUNIT.,'inch',#125);
#125=IFCMEASUREWITHUNIT(IFCLENGTHMEASURE(0.0254),#126);
#126=IFCSIUNIT(*,.LENGTHUNIT.,$,.METRE.);
#127=IFCDERIVEDUNIT((#128,#132),.PLANARFORCEUNIT.,$);
#128=IFCDERIVEDUNITELEMENT(#129,1);
#129=IFCCONVERSIONBASEDUNIT($,.FORCEUNIT.,'pound-force',#130);
#130=IFCMEASUREWITHUNIT(IFCMASSMEASURE(4.44822162),#131);
#131=IFCSIUNIT(*,.FORCEUNIT.,$,.NEWTON.);
#132=IFCDERIVEDUNITELEMENT(#133,-1);
#133=IFCCONVERSIONBASEDUNIT($,.AREAUNIT.,'square inch',#134);
#134=IFCMEASUREWITHUNIT(IFCAREAMEASURE(0.0006452),#135);
#135=IFCSIUNIT(*,.AREAUNIT.,$,.SQUARE_METRE.);
#136=IFCDERIVEDUNIT((#137,#141),.ROTATIONALMASSUNIT.,$);
#137=IFCDERIVEDUNITELEMENT(#138,1);
#138=IFCCONVERSIONBASEDUNIT($,.MASSUNIT.,'pound',#139);
#139=IFCMEASUREWITHUNIT(IFCMASSMEASURE(0.45359237),#140);
#140=IFCSIUNIT(*,.MASSUNIT.,.KILO.,.GRAM.);
#141=IFCDERIVEDUNITELEMENT(#142,-1);
#142=IFCCONVERSIONBASEDUNIT($,.AREAUNIT.,'square inch',#143);
#143=IFCMEASUREWITHUNIT(IFCAREAMEASURE(0.0006452),#144);
#144=IFCSIUNIT(*,.AREAUNIT.,$,.SQUARE_METRE.);
#145=IFCDERIVEDUNIT((#146,#150,#154),.ROTATIONALSTIFFNESSUNIT.,$);
#146=IFCDERIVEDUNITELEMENT(#147,1);
#147=IFCCONVERSIONBASEDUNIT($,.FORCEUNIT.,'pound-force',#148);
#148=IFCMEASUREWITHUNIT(IFCMASSMEASURE(4.44822162),#149);
#149=IFCSIUNIT(*,.FORCEUNIT.,$,.NEWTON.);
#150=IFCDERIVEDUNITELEMENT(#151,1);
#151=IFCCONVERSIONBASEDUNIT($,.LENGTHUNIT.,'inch',#152);
#152=IFCMEASUREWITHUNIT(IFCLENGTHMEASURE(0.0254),#153);
#153=IFCSIUNIT(*,.LENGTHUNIT.,$,.METRE.);
#154=IFCDERIVEDUNITELEMENT(#155,-1);
#155=IFCCONVERSIONBASEDUNIT($,.PLANEANGLEUNIT.,'degree',#156);
#156=IFCMEASUREWITHUNIT(IFCPLANEANGLEMEASURE(0.0174532925199433),#157);
#157=IFCSIUNIT(*,.PLANEANGLEUNIT.,$,.RADIAN.);
#158=IFCDERIVEDUNIT((#159),.SECTIONAREAINTEGRALUNIT.,$);
#159=IFCDERIVEDUNITELEMENT(#160,5);
#160=IFCCONVERSIONBASEDUNIT($,.LENGTHUNIT.,'inch',#161);
#161=IFCMEASUREWITHUNIT(IFCLENGTHMEASURE(0.0254),#162);
#162=IFCSIUNIT(*,.LENGTHUNIT.,$,.METRE.);
#163=IFCDERIVEDUNIT((#164),.SECTIONMODULUSUNIT.,$);
#164=IFCDERIVEDUNITELEMENT(#165,3);
#165=IFCCONVERSIONBASEDUNIT($,.LENGTHUNIT.,'inch',#166);
#166=IFCMEASUREWITHUNIT(IFCLENGTHMEASURE(0.0254),#167);
#167=IFCSIUNIT(*,.LENGTHUNIT.,$,.METRE.);
#168=IFCDERIVEDUNIT((#169),.SHEARMODULUSUNIT.,$);
#169=IFCDERIVEDUNITELEMENT(#170,1);
#170=IFCCONVERSIONBASEDUNIT($,.PRESSUREUNIT.,'pound-force per square inch',#171);
#171=IFCMEASUREWITHUNIT(IFCPRESSUREMEASURE(6894.7572932),#172);
#172=IFCSIUNIT(*,.PRESSUREUNIT.,$,.PASCAL.);
#173=IFCAXIS2PLACEMENT3D(#174,$,$);
#174=IFCCARTESIANPOINT((0.,0.,0.));
#175=IFCLOCALPLACEMENT($,#176);
#176=IFCAXIS2PLACEMENT3D(#177,$,$);
#177=IFCCARTESIANPOINT((0.,0.,0.));
#178=IFCPRODUCTDEFINITIONSHAPE($,$,(#179));
#179=IFCTOPOLOGYREPRESENTATION(#23,'Reference','Edge',(#180));
#180=IFCEDGE(#181,#183);
#181=IFCVERTEXPOINT(#182);
#182=IFCCARTESIANPOINT((0.,0.,0.));
#183=IFCVERTEXPOINT(#184);
#184=IFCCARTESIANPOINT((0.,0.,120.));
#185=IFCDIRECTION((1.,0.,0.));
#186=IFCPRODUCTDEFINITIONSHAPE($,$,(#187));
#187=IFCTOPOLOGYREPRESENTATION(#23,'Reference','Vertex',(#188));
#188=IFCVERTEXPOINT(#189);
#189=IFCCARTESIANPOINT((0.,0.,0.));
#190=IFCBOUNDARYNODECONDITION('Fixed',IFCBOOLEAN(.T.),IFCBOOLEAN(.T.),IFCBOOLEAN(.T.),IFCBOOLEAN(.T.),IFCBOOLEAN(.T.),IFCBOOLEAN(.T.));
#191=IFCPRODUCTDEFINITIONSHAPE($,$,(#192));
#192=IFCTOPOLOGYREPRESENTATION(#23,'Reference','Vertex',(#193));
#193=IFCVERTEXPOINT(#194);
#194=IFCCARTESIANPOINT((0.,0.,120.));
#195=IFCPRODUCTDEFINITIONSHAPE($,$,(#196));
#196=IFCTOPOLOGYREPRESENTATION(#23,'Reference','Edge',(#197));
#197=IFCEDGE(#198,#200);
#198=IFCVERTEXPOINT(#199);
#199=IFCCARTESIANPOINT((192.,0.,0.));
#200=IFCVERTEXPOINT(#201);
#201=IFCCARTESIANPOINT((192.,0.,120.));
#202=IFCDIRECTION((1.,0.,0.));
#203=IFCPRODUCTDEFINITIONSHAPE($,$,(#204));
#204=IFCTOPOLOGYREPRESENTATION(#23,'Reference','Vertex',(#205));
#205=IFCVERTEXPOINT(#206);
#206=IFCCARTESIANPOINT((192.,0.,0.));
#207=IFCBOUNDARYNODECONDITION('Fixed',IFCBOOLEAN(.T.),IFCBOOLEAN(.T.),IFCBOOLEAN(.T.),IFCBOOLEAN(.T.),IFCBOOLEAN(.T.),IFCBOOLEAN(.T.));
#208=IFCPRODUCTDEFINITIONSHAPE($,$,(#209));
#209=IFCTOPOLOGYREPRESENTATION(#23,'Reference','Vertex',(#210));
#210=IFCVERTEXPOINT(#211);
#211=IFCCARTESIANPOINT((192.,0.,120.));
#212=IFCPRODUCTDEFINITIONSHAPE($,$,(#213));
#213=IFCTOPOLOGYREPRESENTATION(#23,'Reference','Edge',(#214));
#214=IFCEDGE(#215,#217);
#215=IFCVERTEXPOINT(#216);
#216=IFCCARTESIANPOINT((0.,0.,120.));
#217=IFCVERTEXPOINT(#218);
#218=IFCCARTESIANPOINT((192.,0.,120.));
#219=IFCDIRECTION((0.,0.,1.));
#220=IFCSTRUCTURALLOADCONFIGURATION($,(#221,#222),((96.),(192.)));
#221=IFCSTRUCTURALLOADLINEARFORCE('Nominal',$,$,-100.,$,$,$);
#222=IFCSTRUCTURALLOADLINEARFORCE('Nominal',$,$,-100.,$,$,$);
#223=IFCSTRUCTURALLOADSINGLEDISPLACEMENT($,0.,0.,0.,0.,0.,0.);
#224=IFCSTRUCTURALLOADSINGLEFORCE($,1422.66326629449,0.,2278.52897011915,0.,66694.8548930371,0.);
#225=IFCSTRUCTURALLOADSINGLEDISPLACEMENT($,-0.00112040278567376,0.,-7.54271659073925E-05,0.,3.08969735441016E-05,0.);
#226=IFCSTRUCTURALLOADSINGLEDISPLACEMENT($,0.,0.,0.,0.,0.,0.);
#227=IFCSTRUCTURALLOADSINGLEFORCE($,-1422.73493120008,0.,7321.47102988085,0.,-43375.4476654014,0.);
#228=IFCSTRUCTURALLOADSINGLEDISPLACEMENT($,-0.00119575654821984,0.,-0.000242365937540883,0.,-6.94996291089951E-05,0.);
#229=IFCSTRUCTURALLOADCONFIGURATION('Member End Reactions',(#230,#231),((0.),(120.)));
#230=IFCSTRUCTURALLOADSINGLEFORCE('Head',2278.52897011915,0.,-1422.66326629449,0.,66694.8548930371,0.);
#231=IFCSTRUCTURALLOADSINGLEFORCE('Tail',-2278.52897011915,0.,1422.66326629449,0.,104027.289932507,0.);
#232=IFCSTRUCTURALLOADCONFIGURATION('Member End Reactions',(#233,#234),((0.),(120.)));
#233=IFCSTRUCTURALLOADSINGLEFORCE('Head',7321.47102988085,0.,1422.73493120008,0.,-43375.4476654014,0.);
#234=IFCSTRUCTURALLOADSINGLEFORCE('Tail',-7321.47102988085,0.,-1422.73493120008,0.,-127343.989381682,0.);
#235=IFCSTRUCTURALLOADCONFIGURATION('Member End Reactions',(#236,#237),((0.),(192.)));
#236=IFCSTRUCTURALLOADSINGLEFORCE('Head',1422.69473557039,0.,2278.52222225513,0.,-104030.36194645,0.);
#237=IFCSTRUCTURALLOADSINGLEFORCE('Tail',-1422.69473557039,0.,7321.47777774487,0.,127353.857770554,0.);
#238=IFCPERSONANDORGANIZATION(#239,#240,$);
#239=IFCPERSON('Tim',$,$,$,$,$,$,$);
#240=IFCORGANIZATION($,'Tim-PC',$,$,$);
#241=IFCAPPLICATION(#242,'0.8','Constructivity','CONSTRUCTIVITY');
#242=IFCORGANIZATION($,'Constructivity.com LLC',$,$,$);
#243=IFCAXIS2PLACEMENT3D(#244,$,$);
#244=IFCCARTESIANPOINT((0.,0.,0.));
#245=IFCAXIS2PLACEMENT3D(#246,$,$);
#246=IFCCARTESIANPOINT((0.,0.,0.));
#247=IFCMATERIALPROFILESETUSAGE(#248,$,$);
#248=IFCMATERIALPROFILESET($,$,(#249),$);
#249=IFCMATERIALPROFILE($,$,#250,#251,$,$);
#250=IFCMATERIAL('ASTM A36',$,'Steel');
#251=IFCISHAPEPROFILEDEF(.AREA.,'W10X30',$,5.81,10.5,0.3,0.51,0.125,$,$);
ENDSEC;
END-ISO-10303-21;

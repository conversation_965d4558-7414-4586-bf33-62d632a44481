ISO-10303-21;
HEADER;
FILE_DESCRIPTION($,'2;1');
FILE_NAME('triangulatedfaceset-texture-blob.ifc','2016-01-20T18:38:46',(''),(''),'Constructivity *******','Constructivity *******','');
FILE_SCHEMA(('IFC4'));
ENDSEC;

DATA;

#1= IFCPROJECT('2BkIlLW5T7aQj3Uy9BMFxr',$,'Project',$,$,$,$,(#2,#3),#4);
#2= IFCGEOMETRICREPRESENTATIONCONTEXT('3D','Model',3,1.0E-05,#7,$);
#3= IFCGEOMETRICREPRESENTATIONCONTEXT('2D','Plan',2,1.0E-05,#8,$);
#4= IFCUNITASSIGNMENT((#9,#10,#11,#12,#13,#14,#15,#16,#17,#18,#19,#20,#21,#22,#23,#24,#25,#26,#27,#28,#29,#30,#31,#32,#33,#34,#35,#36,#37,#38,#39,#40,#41,#42,#43,#44,#45,#46,#47,#48,#49,#50,#51,#52,#53,#54,#55,#56,#57,#58,#59,#60,#61,#62,#63,#64,#65,#66,#67,#68,#69,#70,#71,#72,#73,#74,#75,#76,#77,#78,#79,#80,#81,#82,#83,#84,#85,#86));
#7= IFCAXIS2PLACEMENT3D(#87,$,$);
#8= IFCAXIS2PLACEMENT3D(#88,$,$);
#9= IFCSIUNIT(*,.ABSORBEDDOSEUNIT.,$,.GRAY.);
#10= IFCSIUNIT(*,.AMOUNTOFSUBSTANCEUNIT.,$,.MOLE.);
#11= IFCSIUNIT(*,.AREAUNIT.,$,.SQUARE_METRE.);
#12= IFCSIUNIT(*,.DOSEEQUIVALENTUNIT.,$,.SIEVERT.);
#13= IFCSIUNIT(*,.ELECTRICCAPACITANCEUNIT.,$,.FARAD.);
#14= IFCSIUNIT(*,.ELECTRICCHARGEUNIT.,$,.COULOMB.);
#15= IFCSIUNIT(*,.ELECTRICCONDUCTANCEUNIT.,$,.SIEMENS.);
#16= IFCSIUNIT(*,.ELECTRICCURRENTUNIT.,$,.AMPERE.);
#17= IFCSIUNIT(*,.ELECTRICRESISTANCEUNIT.,$,.OHM.);
#18= IFCSIUNIT(*,.ELECTRICVOLTAGEUNIT.,$,.VOLT.);
#19= IFCSIUNIT(*,.ENERGYUNIT.,$,.JOULE.);
#20= IFCSIUNIT(*,.FORCEUNIT.,$,.NEWTON.);
#21= IFCSIUNIT(*,.FREQUENCYUNIT.,$,.HERTZ.);
#22= IFCSIUNIT(*,.ILLUMINANCEUNIT.,$,.LUX.);
#23= IFCSIUNIT(*,.INDUCTANCEUNIT.,$,.HENRY.);
#24= IFCSIUNIT(*,.LENGTHUNIT.,$,.METRE.);
#25= IFCSIUNIT(*,.LUMINOUSFLUXUNIT.,$,.LUMEN.);
#26= IFCSIUNIT(*,.LUMINOUSINTENSITYUNIT.,$,.CANDELA.);
#27= IFCSIUNIT(*,.MAGNETICFLUXDENSITYUNIT.,$,.TESLA.);
#28= IFCSIUNIT(*,.MAGNETICFLUXUNIT.,$,.WEBER.);
#29= IFCSIUNIT(*,.MASSUNIT.,.KILO.,.GRAM.);
#30= IFCCONVERSIONBASEDUNIT(#89,.PLANEANGLEUNIT.,'degree',#90);
#31= IFCSIUNIT(*,.POWERUNIT.,$,.WATT.);
#32= IFCSIUNIT(*,.PRESSUREUNIT.,$,.PASCAL.);
#33= IFCSIUNIT(*,.RADIOACTIVITYUNIT.,$,.BECQUEREL.);
#34= IFCSIUNIT(*,.SOLIDANGLEUNIT.,$,.STERADIAN.);
#35= IFCSIUNIT(*,.THERMODYNAMICTEMPERATUREUNIT.,$,.DEGREE_CELSIUS.);
#36= IFCSIUNIT(*,.TIMEUNIT.,$,.SECOND.);
#37= IFCSIUNIT(*,.VOLUMEUNIT.,$,.CUBIC_METRE.);
#38= IFCDERIVEDUNIT((#91,#92,#93),.ACCELERATIONUNIT.,$);
#39= IFCDERIVEDUNIT((#94,#95),.ANGULARVELOCITYUNIT.,$);
#40= IFCDERIVEDUNIT((#96),.COMPOUNDPLANEANGLEUNIT.,$);
#41= IFCDERIVEDUNIT((#97,#98),.CURVATUREUNIT.,$);
#42= IFCDERIVEDUNIT((#99,#100),.DYNAMICVISCOSITYUNIT.,$);
#43= IFCDERIVEDUNIT((#101,#102),.HEATFLUXDENSITYUNIT.,$);
#44= IFCDERIVEDUNIT((#103,#104),.HEATINGVALUEUNIT.,$);
#45= IFCDERIVEDUNIT((#105,#106),.IONCONCENTRATIONUNIT.,$);
#46= IFCDERIVEDUNIT((#107),.INTEGERCOUNTRATEUNIT.,$);
#47= IFCDERIVEDUNIT((#108,#109),.ISOTHERMALMOISTURECAPACITYUNIT.,$);
#48= IFCDERIVEDUNIT((#110,#111),.KINEMATICVISCOSITYUNIT.,$);
#49= IFCDERIVEDUNIT((#112,#113),.LINEARFORCEUNIT.,$);
#50= IFCDERIVEDUNIT((#114,#115,#116),.LINEARMOMENTUNIT.,$);
#51= IFCDERIVEDUNIT((#117,#118),.LINEARSTIFFNESSUNIT.,$);
#52= IFCDERIVEDUNIT((#119,#120),.LINEARVELOCITYUNIT.,$);
#53= IFCDERIVEDUNIT((#121,#122),.LUMINOUSINTENSITYDISTRIBUTIONUNIT.,$);
#54= IFCDERIVEDUNIT((#123,#124),.MASSDENSITYUNIT.,$);
#55= IFCDERIVEDUNIT((#125,#126),.MASSFLOWRATEUNIT.,$);
#56= IFCDERIVEDUNIT((#127,#128),.MASSPERLENGTHUNIT.,$);
#57= IFCDERIVEDUNIT((#129),.MODULUSOFELASTICITYUNIT.,$);
#58= IFCDERIVEDUNIT((#130,#131),.MODULUSOFLINEARSUBGRADEREACTIONUNIT.,$);
#59= IFCDERIVEDUNIT((#132,#133,#134,#135),.MODULUSOFROTATIONALSUBGRADEREACTIONUNIT.,$);
#60= IFCDERIVEDUNIT((#136,#137),.MODULUSOFSUBGRADEREACTIONUNIT.,$);
#61= IFCDERIVEDUNIT((#138,#139),.MOISTUREDIFFUSIVITYUNIT.,$);
#62= IFCDERIVEDUNIT((#140,#141),.MOLECULARWEIGHTUNIT.,$);
#63= IFCDERIVEDUNIT((#142),.MOMENTOFINERTIAUNIT.,$);
#64= IFCDERIVEDUNIT((#143,#144),.PLANARFORCEUNIT.,$);
#65= IFCDERIVEDUNIT((#145),.ROTATIONALFREQUENCYUNIT.,$);
#66= IFCDERIVEDUNIT((#146,#147),.ROTATIONALMASSUNIT.,$);
#67= IFCDERIVEDUNIT((#148,#149,#150),.ROTATIONALSTIFFNESSUNIT.,$);
#68= IFCDERIVEDUNIT((#151),.SECTIONAREAINTEGRALUNIT.,$);
#69= IFCDERIVEDUNIT((#152),.SECTIONMODULUSUNIT.,$);
#70= IFCDERIVEDUNIT((#153),.SHEARMODULUSUNIT.,$);
#71= IFCDERIVEDUNIT((#154),.SOUNDPOWERUNIT.,$);
#72= IFCDERIVEDUNIT((#155),.SOUNDPRESSUREUNIT.,$);
#73= IFCDERIVEDUNIT((#156,#157,#158),.SPECIFICHEATCAPACITYUNIT.,$);
#74= IFCDERIVEDUNIT((#159,#160),.TEMPERATUREGRADIENTUNIT.,$);
#75= IFCDERIVEDUNIT((#161,#162),.TEMPERATURERATEOFCHANGEUNIT.,$);
#76= IFCDERIVEDUNIT((#163,#164,#165),.THERMALADMITTANCEUNIT.,$);
#77= IFCDERIVEDUNIT((#166,#167,#168),.THERMALCONDUCTANCEUNIT.,$);
#78= IFCDERIVEDUNIT((#169),.THERMALEXPANSIONCOEFFICIENTUNIT.,$);
#79= IFCDERIVEDUNIT((#170,#171,#172),.THERMALRESISTANCEUNIT.,$);
#80= IFCDERIVEDUNIT((#173,#174,#175),.THERMALTRANSMITTANCEUNIT.,$);
#81= IFCDERIVEDUNIT((#176,#177),.TORQUEUNIT.,$);
#82= IFCDERIVEDUNIT((#178,#179,#180),.VAPORPERMEABILITYUNIT.,$);
#83= IFCDERIVEDUNIT((#181,#182),.VOLUMETRICFLOWRATEUNIT.,$);
#84= IFCDERIVEDUNIT((#183),.WARPINGCONSTANTUNIT.,$);
#85= IFCDERIVEDUNIT((#184,#185),.WARPINGMOMENTUNIT.,$);
#86= IFCMONETARYUNIT('USD');
#87= IFCCARTESIANPOINT((0.,0.,0.));
#88= IFCCARTESIANPOINT((0.,0.,0.));
#89= IFCDIMENSIONALEXPONENTS(0,0,0,0,0,0,0);
#90= IFCMEASUREWITHUNIT(IFCPLANEANGLEMEASURE(0.0174532925199433),#186);
#91= IFCDERIVEDUNITELEMENT(#24,1);
#92= IFCDERIVEDUNITELEMENT(#36,-1);
#93= IFCDERIVEDUNITELEMENT(#36,-1);
#94= IFCDERIVEDUNITELEMENT(#30,1);
#95= IFCDERIVEDUNITELEMENT(#36,-1);
#96= IFCDERIVEDUNITELEMENT(#30,1);
#97= IFCDERIVEDUNITELEMENT(#30,1);
#98= IFCDERIVEDUNITELEMENT(#24,-1);
#99= IFCDERIVEDUNITELEMENT(#32,1);
#100= IFCDERIVEDUNITELEMENT(#36,1);
#101= IFCDERIVEDUNITELEMENT(#31,1);
#102= IFCDERIVEDUNITELEMENT(#11,-1);
#103= IFCDERIVEDUNITELEMENT(#19,1);
#104= IFCDERIVEDUNITELEMENT(#29,-1);
#105= IFCDERIVEDUNITELEMENT(#187,1);
#106= IFCDERIVEDUNITELEMENT(#37,-1);
#107= IFCDERIVEDUNITELEMENT(#36,-1);
#108= IFCDERIVEDUNITELEMENT(#37,1);
#109= IFCDERIVEDUNITELEMENT(#9,-1);
#110= IFCDERIVEDUNITELEMENT(#11,1);
#111= IFCDERIVEDUNITELEMENT(#36,-1);
#112= IFCDERIVEDUNITELEMENT(#20,1);
#113= IFCDERIVEDUNITELEMENT(#24,-1);
#114= IFCDERIVEDUNITELEMENT(#20,1);
#115= IFCDERIVEDUNITELEMENT(#24,1);
#116= IFCDERIVEDUNITELEMENT(#24,-1);
#117= IFCDERIVEDUNITELEMENT(#20,1);
#118= IFCDERIVEDUNITELEMENT(#24,-1);
#119= IFCDERIVEDUNITELEMENT(#24,1);
#120= IFCDERIVEDUNITELEMENT(#36,-1);
#121= IFCDERIVEDUNITELEMENT(#26,1);
#122= IFCDERIVEDUNITELEMENT(#25,-1);
#123= IFCDERIVEDUNITELEMENT(#29,1);
#124= IFCDERIVEDUNITELEMENT(#37,-1);
#125= IFCDERIVEDUNITELEMENT(#29,1);
#126= IFCDERIVEDUNITELEMENT(#36,-1);
#127= IFCDERIVEDUNITELEMENT(#29,1);
#128= IFCDERIVEDUNITELEMENT(#24,-1);
#129= IFCDERIVEDUNITELEMENT(#32,1);
#130= IFCDERIVEDUNITELEMENT(#20,1);
#131= IFCDERIVEDUNITELEMENT(#11,-1);
#132= IFCDERIVEDUNITELEMENT(#20,1);
#133= IFCDERIVEDUNITELEMENT(#24,1);
#134= IFCDERIVEDUNITELEMENT(#24,-1);
#135= IFCDERIVEDUNITELEMENT(#30,-1);
#136= IFCDERIVEDUNITELEMENT(#20,1);
#137= IFCDERIVEDUNITELEMENT(#37,-1);
#138= IFCDERIVEDUNITELEMENT(#37,1);
#139= IFCDERIVEDUNITELEMENT(#36,-1);
#140= IFCDERIVEDUNITELEMENT(#187,1);
#141= IFCDERIVEDUNITELEMENT(#10,-1);
#142= IFCDERIVEDUNITELEMENT(#24,4);
#143= IFCDERIVEDUNITELEMENT(#20,1);
#144= IFCDERIVEDUNITELEMENT(#11,-1);
#145= IFCDERIVEDUNITELEMENT(#36,-1);
#146= IFCDERIVEDUNITELEMENT(#29,1);
#147= IFCDERIVEDUNITELEMENT(#11,-1);
#148= IFCDERIVEDUNITELEMENT(#20,1);
#149= IFCDERIVEDUNITELEMENT(#24,1);
#150= IFCDERIVEDUNITELEMENT(#30,-1);
#151= IFCDERIVEDUNITELEMENT(#24,5);
#152= IFCDERIVEDUNITELEMENT(#24,3);
#153= IFCDERIVEDUNITELEMENT(#32,1);
#154= IFCDERIVEDUNITELEMENT(#188,0);
#155= IFCDERIVEDUNITELEMENT(#189,0);
#156= IFCDERIVEDUNITELEMENT(#19,1);
#157= IFCDERIVEDUNITELEMENT(#29,-1);
#158= IFCDERIVEDUNITELEMENT(#190,-1);
#159= IFCDERIVEDUNITELEMENT(#190,1);
#160= IFCDERIVEDUNITELEMENT(#24,-1);
#161= IFCDERIVEDUNITELEMENT(#190,1);
#162= IFCDERIVEDUNITELEMENT(#36,-1);
#163= IFCDERIVEDUNITELEMENT(#31,1);
#164= IFCDERIVEDUNITELEMENT(#11,-1);
#165= IFCDERIVEDUNITELEMENT(#190,-1);
#166= IFCDERIVEDUNITELEMENT(#31,1);
#167= IFCDERIVEDUNITELEMENT(#190,-1);
#168= IFCDERIVEDUNITELEMENT(#24,-1);
#169= IFCDERIVEDUNITELEMENT(#190,-1);
#170= IFCDERIVEDUNITELEMENT(#11,1);
#171= IFCDERIVEDUNITELEMENT(#190,1);
#172= IFCDERIVEDUNITELEMENT(#31,-1);
#173= IFCDERIVEDUNITELEMENT(#31,1);
#174= IFCDERIVEDUNITELEMENT(#11,-1);
#175= IFCDERIVEDUNITELEMENT(#190,1);
#176= IFCDERIVEDUNITELEMENT(#20,1);
#177= IFCDERIVEDUNITELEMENT(#24,1);
#178= IFCDERIVEDUNITELEMENT(#29,1);
#179= IFCDERIVEDUNITELEMENT(#36,-1);
#180= IFCDERIVEDUNITELEMENT(#24,-1);
#181= IFCDERIVEDUNITELEMENT(#37,1);
#182= IFCDERIVEDUNITELEMENT(#36,-1);
#183= IFCDERIVEDUNITELEMENT(#24,6);
#184= IFCDERIVEDUNITELEMENT(#20,1);
#185= IFCDERIVEDUNITELEMENT(#24,2);
#186= IFCSIUNIT(*,.PLANEANGLEUNIT.,$,.RADIAN.);
#187= IFCSIUNIT(*,.MASSUNIT.,$,.GRAM.);
#188= IFCSIUNIT(*,.POWERUNIT.,.PICO.,.WATT.);
#189= IFCSIUNIT(*,.PRESSUREUNIT.,.MICRO.,.PASCAL.);
#190= IFCSIUNIT(*,.THERMODYNAMICTEMPERATUREUNIT.,$,.KELVIN.);

#5= IFCRELAGGREGATES('2itvH51TLF$vSczv308eND',$,$,$,#1,(#191));

#6= IFCRELDECLARES('1HmT$jWs52sPjBfKd4uec0',$,$,$,#1,(#192));

#191= IFCSITE('12Jhas3DnFkfm06Be8GeKW',$,'Site #1',$,$,#193,#194,$,.ELEMENT.,$,$,$,$,$);
#193= IFCLOCALPLACEMENT($,#196);
#194= IFCPRODUCTDEFINITIONSHAPE($,$,(#197));
#196= IFCAXIS2PLACEMENT3D(#198,$,$);
#197= IFCSHAPEREPRESENTATION(#2,'FootPrint','GeometricCurveSet',(#199));
#198= IFCCARTESIANPOINT((0.,0.,0.));
#199= IFCGEOMETRICCURVESET((#200));
#200= IFCPOLYLINE((#201,#202,#203,#204,#201));
#201= IFCCARTESIANPOINT((0.,0.));
#202= IFCCARTESIANPOINT((40.,0.));
#203= IFCCARTESIANPOINT((40.,20.));
#204= IFCCARTESIANPOINT((0.,20.));

#192= IFCMEMBERTYPE('1AgtYeFKDEuRnJ8pGQA3pA',$,$,$,$,$,(#205),$,$,.POST.);
#205= IFCREPRESENTATIONMAP(#207,#208);
#207= IFCAXIS2PLACEMENT3D(#209,$,$);
#208= IFCSHAPEREPRESENTATION(#2,'Body','Tessellation',(#210));
#209= IFCCARTESIANPOINT((0.,0.,0.));
#210= IFCTRIANGULATEDFACESET(#211,((0.,0.,-1.),(0.,0.,1.),(0.,0.,-1.),(0.,0.,-1.),(0.,0.,-1.),(0.,0.,-1.),(0.,0.,-1.),(0.,0.,-1.),(0.,0.,-1.),(0.,0.,-1.),(0.,0.,-1.),(0.,0.,-1.),(0.,0.,-1.),(0.,0.,-1.),(0.,0.,-1.),(0.,0.,-1.),(0.,0.,-1.),(0.,0.,-1.),(0.,0.,-1.),(0.,0.,1.),(0.,0.,1.),(0.,0.,1.),(0.,0.,1.),(0.,0.,1.),(0.,0.,1.),(0.,0.,1.),(0.,0.,1.),(0.,0.,1.),(0.,0.,1.),(0.,0.,1.),(0.,0.,1.),(0.,0.,1.),(0.,0.,1.),(0.,0.,1.),(0.,0.,1.),(0.,0.,1.),(1.,0.,0.),(0.923879532511287,0.38268343236509,0.),(0.707106781186548,0.707106781186547,0.),(0.38268343236509,0.923879532511287,0.),(6.12303176911189E-17,1.,0.),(-0.38268343236509,0.923879532511287,0.),(-0.707106781186547,0.707106781186548,0.),(-0.923879532511287,0.38268343236509,0.),(-1.,1.22460635382238E-16,0.),(-0.923879532511287,-0.38268343236509,0.),(-0.707106781186548,-0.707106781186547,0.),(-0.38268343236509,-0.923879532511287,0.),(-1.83690953073357E-16,-1.,0.),(0.38268343236509,-0.923879532511287,0.),(0.707106781186547,-0.707106781186548,0.),(0.923879532511287,-0.38268343236509,0.),(1.,-2.44921270764475E-16,0.),(1.,0.,0.),(0.923879532511287,0.38268343236509,0.),(0.707106781186548,0.707106781186547,0.),(0.38268343236509,0.923879532511287,0.),(6.12303176911189E-17,1.,0.),(-0.38268343236509,0.923879532511287,0.),(-0.707106781186547,0.707106781186548,0.),(-0.923879532511287,0.38268343236509,0.),(-1.,1.22460635382238E-16,0.),(-0.923879532511287,-0.38268343236509,0.),(-0.707106781186548,-0.707106781186547,0.),(-0.38268343236509,-0.923879532511287,0.),(-1.83690953073357E-16,-1.,0.),(0.38268343236509,-0.923879532511287,0.),(0.707106781186547,-0.707106781186548,0.),(0.923879532511287,-0.38268343236509,0.),(1.,-2.44921270764475E-16,0.)),.T.,((1,4,3),(38,54,37),(38,55,54),(2,20,21),(1,5,4),(39,55,38),(39,56,55),(2,21,22),(1,6,5),(40,56,39),(40,57,56),(2,22,23),(1,7,6),(41,57,40),(41,58,57),(2,23,24),(1,8,7),(42,58,41),(42,59,58),(2,24,25),(1,9,8),(43,59,42),(43,60,59),(2,25,26),(1,10,9),(44,60,43),(44,61,60),(2,26,27),(1,11,10),(45,61,44),(45,62,61),(2,27,28),(1,12,11),(46,62,45),(46,63,62),(2,28,29),(1,13,12),(47,63,46),(47,64,63),(2,29,30),(1,14,13),(48,64,47),(48,65,64),(2,30,31),(1,15,14),(49,65,48),(49,66,65),(2,31,32),(1,16,15),(50,66,49),(50,67,66),(2,32,33),(1,17,16),(51,67,50),(51,68,67),(2,33,34),(1,18,17),(52,68,51),(52,69,68),(2,34,35),(1,19,18),(53,69,52),(53,70,69),(2,35,36)),((1,4,3),(38,54,37),(38,55,54),(2,20,21),(1,5,4),(39,55,38),(39,56,55),(2,21,22),(1,6,5),(40,56,39),(40,57,56),(2,22,23),(1,7,6),(41,57,40),(41,58,57),(2,23,24),(1,8,7),(42,58,41),(42,59,58),(2,24,25),(1,9,8),(43,59,42),(43,60,59),(2,25,26),(1,10,9),(44,60,43),(44,61,60),(2,26,27),(1,11,10),(45,61,44),(45,62,61),(2,27,28),(1,12,11),(46,62,45),(46,63,62),(2,28,29),(1,13,12),(47,63,46),(47,64,63),(2,29,30),(1,14,13),(48,64,47),(48,65,64),(2,30,31),(1,15,14),(49,65,48),(49,66,65),(2,31,32),(1,16,15),(50,66,49),(50,67,66),(2,32,33),(1,17,16),(51,67,50),(51,68,67),(2,33,34),(1,18,17),(52,68,51),(52,69,68),(2,34,35),(1,19,18),(53,69,52),(53,70,69),(2,35,36)));
#211= IFCCARTESIANPOINTLIST3D(((0.,0.,0.),(0.,0.,4.),(2.,0.,0.),(1.84775906502257,0.76536686473018,0.),(1.4142135623731,1.41421356237309,0.),(0.76536686473018,1.84775906502257,0.),(1.22460635382238E-16,2.,0.),(-0.765366864730179,1.84775906502257,0.),(-1.41421356237309,1.4142135623731,0.),(-1.84775906502257,0.76536686473018,0.),(-2.,2.44921270764475E-16,0.),(-1.84775906502257,-0.765366864730179,0.),(-1.4142135623731,-1.41421356237309,0.),(-0.765366864730181,-1.84775906502257,0.),(-3.67381906146713E-16,-2.,0.),(0.76536686473018,-1.84775906502257,0.),(1.41421356237309,-1.4142135623731,0.),(1.84775906502257,-0.765366864730181,0.),(2.,-4.89842541528951E-16,0.),(2.,0.,4.),(1.84775906502257,0.76536686473018,4.),(1.4142135623731,1.41421356237309,4.),(0.76536686473018,1.84775906502257,4.),(1.22460635382238E-16,2.,4.),(-0.765366864730179,1.84775906502257,4.),(-1.41421356237309,1.4142135623731,4.),(-1.84775906502257,0.76536686473018,4.),(-2.,2.44921270764475E-16,4.),(-1.84775906502257,-0.765366864730179,4.),(-1.4142135623731,-1.41421356237309,4.),(-0.765366864730181,-1.84775906502257,4.),(-3.67381906146713E-16,-2.,4.),(0.76536686473018,-1.84775906502257,4.),(1.41421356237309,-1.4142135623731,4.),(1.84775906502257,-0.765366864730181,4.),(2.,-4.89842541528951E-16,4.),(2.,0.,0.),(1.84775906502257,0.76536686473018,0.),(1.4142135623731,1.41421356237309,0.),(0.76536686473018,1.84775906502257,0.),(1.22460635382238E-16,2.,0.),(-0.765366864730179,1.84775906502257,0.),(-1.41421356237309,1.4142135623731,0.),(-1.84775906502257,0.76536686473018,0.),(-2.,2.44921270764475E-16,0.),(-1.84775906502257,-0.765366864730179,0.),(-1.4142135623731,-1.41421356237309,0.),(-0.765366864730181,-1.84775906502257,0.),(-3.67381906146713E-16,-2.,0.),(0.76536686473018,-1.84775906502257,0.),(1.41421356237309,-1.4142135623731,0.),(1.84775906502257,-0.765366864730181,0.),(2.,-4.89842541528951E-16,0.),(2.,0.,4.),(1.84775906502257,0.76536686473018,4.),(1.4142135623731,1.41421356237309,4.),(0.76536686473018,1.84775906502257,4.),(1.22460635382238E-16,2.,4.),(-0.765366864730179,1.84775906502257,4.),(-1.41421356237309,1.4142135623731,4.),(-1.84775906502257,0.76536686473018,4.),(-2.,2.44921270764475E-16,4.),(-1.84775906502257,-0.765366864730179,4.),(-1.4142135623731,-1.41421356237309,4.),(-0.765366864730181,-1.84775906502257,4.),(-3.67381906146713E-16,-2.,4.),(0.76536686473018,-1.84775906502257,4.),(1.41421356237309,-1.4142135623731,4.),(1.84775906502257,-0.765366864730181,4.),(2.,-4.89842541528951E-16,4.)));
#212= IFCSTYLEDITEM(#210,(#214),$);
#213= IFCINDEXEDTRIANGLETEXTUREMAP((#219),#210,#215,((1,4,3),(38,54,37),(38,55,54),(2,20,21),(1,5,4),(39,55,38),(39,56,55),(2,21,22),(1,6,5),(40,56,39),(40,57,56),(2,22,23),(1,7,6),(41,57,40),(41,58,57),(2,23,24),(1,8,7),(42,58,41),(42,59,58),(2,24,25),(1,9,8),(43,59,42),(43,60,59),(2,25,26),(1,10,9),(44,60,43),(44,61,60),(2,26,27),(1,11,10),(45,61,44),(45,62,61),(2,27,28),(1,12,11),(46,62,45),(46,63,62),(2,28,29),(1,13,12),(47,63,46),(47,64,63),(2,29,30),(1,14,13),(48,64,47),(48,65,64),(2,30,31),(1,15,14),(49,65,48),(49,66,65),(2,31,32),(1,16,15),(50,66,49),(50,67,66),(2,32,33),(1,17,16),(51,67,50),(51,68,67),(2,33,34),(1,18,17),(52,68,51),(52,69,68),(2,34,35),(1,19,18),(53,69,52),(53,70,69),(2,35,36)));
#214= IFCSURFACESTYLE($,.POSITIVE.,(#216,#217));
#215= IFCTEXTUREVERTEXLIST(((0.5,0.5),(0.5,0.5),(1.,0.5),(0.961939766255643,0.308658283817455),(0.853553390593274,0.146446609406726),(0.691341716182545,0.0380602337443566),(0.5,0.),(0.308658283817455,0.0380602337443566),(0.146446609406726,0.146446609406726),(0.0380602337443566,0.308658283817455),(0.,0.5),(0.0380602337443566,0.691341716182545),(0.146446609406726,0.853553390593274),(0.308658283817455,0.961939766255643),(0.5,1.),(0.691341716182545,0.961939766255643),(0.853553390593274,0.853553390593274),(0.961939766255643,0.691341716182545),(1.,0.5),(1.,0.5),(0.961939766255643,0.691341716182545),(0.853553390593274,0.853553390593274),(0.691341716182545,0.961939766255643),(0.5,1.),(0.308658283817455,0.961939766255643),(0.146446609406726,0.853553390593274),(0.0380602337443566,0.691341716182545),(0.,0.5),(0.0380602337443566,0.308658283817455),(0.146446609406726,0.146446609406726),(0.308658283817455,0.0380602337443567),(0.5,0.),(0.691341716182545,0.0380602337443567),(0.853553390593274,0.146446609406726),(0.961939766255643,0.308658283817455),(1.,0.5),(-0.25,0.),(-0.1875,0.),(-0.125,0.),(-0.0625,0.),(0.,0.),(0.0625,0.),(0.125,0.),(0.1875,0.),(0.25,0.),(0.3125,0.),(0.375,0.),(0.4375,0.),(0.5,0.),(0.5625,0.),(0.625,0.),(0.6875,0.),(0.75,0.),(-0.25,1.),(-0.1875,1.),(-0.125,1.),(-0.0625,1.),(0.,1.),(0.0625,1.),(0.125,1.),(0.1875,1.),(0.25,1.),(0.3125,1.),(0.375,1.),(0.4375,1.),(0.5,1.),(0.5625,1.),(0.625,1.),(0.6875,1.),(0.75,1.)));
#216= IFCSURFACESTYLERENDERING(#218,$,$,$,$,$,$,$,.NOTDEFINED.);
#217= IFCSURFACESTYLEWITHTEXTURES((#219));
#218= IFCCOLOURRGB($,1.,1.,1.);
#219= IFCBLOBTEXTURE(.T.,.T.,'TEXTURE',$,$,'PNG',"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");

#195= IFCRELCONTAINEDINSPATIALSTRUCTURE('2H$O_OUzH91QU2yMIvZZ$h',$,$,$,(#220),#191);

#206= IFCRELDEFINESBYTYPE('208LEfhXv418VHoTjAT$dC',$,$,$,(#220),#192);

#220= IFCMEMBER('3K_lgFFuT4$xmawQPom25e',$,$,$,$,#221,#222,$,$);
#221= IFCLOCALPLACEMENT(#193,#223);
#222= IFCPRODUCTDEFINITIONSHAPE($,$,(#224));
#223= IFCAXIS2PLACEMENT3D(#225,#226,#227);
#224= IFCSHAPEREPRESENTATION(#2,'Body','MappedRepresentation',(#228));
#225= IFCCARTESIANPOINT((1.92970621585846,1.9296875,0.));
#226= IFCDIRECTION((0.,0.,1.));
#227= IFCDIRECTION((1.,0.,0.));
#228= IFCMAPPEDITEM(#205,#229);
#229= IFCCARTESIANTRANSFORMATIONOPERATOR3D($,$,#230,1.,$);
#230= IFCCARTESIANPOINT((0.,0.,0.));
ENDSEC;

END-ISO-10303-21;

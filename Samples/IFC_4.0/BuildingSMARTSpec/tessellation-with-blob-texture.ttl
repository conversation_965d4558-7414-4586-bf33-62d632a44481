# baseURI: http://linkedbuildingdata.net/ifc/resources20200624_184152/
# imports: http://standards.buildingsmart.org/IFC/DEV/IFC4/ADD1/OWL

@prefix ifc:  <http://standards.buildingsmart.org/IFC/DEV/IFC4/ADD1/OWL#> .
@prefix inst:  <http://linkedbuildingdata.net/ifc/resources20200624_184152/> .
@prefix list:  <https://w3id.org/list#> .
@prefix express:  <https://w3id.org/express#> .
@prefix rdf:  <http://www.w3.org/1999/02/22-rdf-syntax-ns#> .
@prefix xsd:  <http://www.w3.org/2001/XMLSchema#> .
@prefix owl:  <http://www.w3.org/2002/07/owl#> .

inst:   rdf:type     owl:Ontology ;
        owl:imports  ifc: .

inst:IfcProject_1  rdf:type  ifc:IfcProject .

inst:IfcGloballyUniqueId_230
        rdf:type           ifc:IfcGloballyUniqueId ;
        express:hasString  "2BkIlLW5T7aQj3Uy9BMFxr" .

inst:IfcProject_1  ifc:globalId_IfcRoot  inst:IfcGloballyUniqueId_230 .

inst:IfcLabel_231  rdf:type  ifc:IfcLabel ;
        express:hasString  "Project" .

inst:IfcProject_1  ifc:name_IfcRoot  inst:IfcLabel_231 .

inst:IfcGeometricRepresentationContext_2
        rdf:type  ifc:IfcGeometricRepresentationContext .

inst:IfcProject_1  ifc:representationContexts_IfcContext  inst:IfcGeometricRepresentationContext_2 .

inst:IfcGeometricRepresentationContext_3
        rdf:type  ifc:IfcGeometricRepresentationContext .

inst:IfcProject_1  ifc:representationContexts_IfcContext  inst:IfcGeometricRepresentationContext_3 .

inst:IfcUnitAssignment_4
        rdf:type  ifc:IfcUnitAssignment .

inst:IfcProject_1  ifc:unitsInContext_IfcContext  inst:IfcUnitAssignment_4 .

inst:IfcLabel_232  rdf:type  ifc:IfcLabel ;
        express:hasString  "3D" .

inst:IfcGeometricRepresentationContext_2
        ifc:contextIdentifier_IfcRepresentationContext  inst:IfcLabel_232 .

inst:IfcLabel_233  rdf:type  ifc:IfcLabel ;
        express:hasString  "Model" .

inst:IfcGeometricRepresentationContext_2
        ifc:contextType_IfcRepresentationContext  inst:IfcLabel_233 .

inst:IfcDimensionCount_234
        rdf:type            ifc:IfcDimensionCount ;
        express:hasInteger  3 .

inst:IfcGeometricRepresentationContext_2
        ifc:coordinateSpaceDimension_IfcGeometricRepresentationContext  inst:IfcDimensionCount_234 .

inst:IfcReal_235  rdf:type  ifc:IfcReal ;
        express:hasDouble  1.0E-05 .

inst:IfcGeometricRepresentationContext_2
        ifc:precision_IfcGeometricRepresentationContext  inst:IfcReal_235 .

inst:IfcAxis2Placement3D_7
        rdf:type  ifc:IfcAxis2Placement3D .

inst:IfcGeometricRepresentationContext_2
        ifc:worldCoordinateSystem_IfcGeometricRepresentationContext  inst:IfcAxis2Placement3D_7 .

inst:IfcLabel_236  rdf:type  ifc:IfcLabel ;
        express:hasString  "2D" .

inst:IfcGeometricRepresentationContext_3
        ifc:contextIdentifier_IfcRepresentationContext  inst:IfcLabel_236 .

inst:IfcLabel_237  rdf:type  ifc:IfcLabel ;
        express:hasString  "Plan" .

inst:IfcGeometricRepresentationContext_3
        ifc:contextType_IfcRepresentationContext  inst:IfcLabel_237 .

inst:IfcDimensionCount_238
        rdf:type            ifc:IfcDimensionCount ;
        express:hasInteger  2 .

inst:IfcGeometricRepresentationContext_3
        ifc:coordinateSpaceDimension_IfcGeometricRepresentationContext  inst:IfcDimensionCount_238 ;
        ifc:precision_IfcGeometricRepresentationContext  inst:IfcReal_235 .

inst:IfcAxis2Placement3D_8
        rdf:type  ifc:IfcAxis2Placement3D .

inst:IfcGeometricRepresentationContext_3
        ifc:worldCoordinateSystem_IfcGeometricRepresentationContext  inst:IfcAxis2Placement3D_8 .

inst:IfcSIUnit_9  rdf:type  ifc:IfcSIUnit .

inst:IfcUnitAssignment_4
        ifc:units_IfcUnitAssignment  inst:IfcSIUnit_9 .

inst:IfcSIUnit_10  rdf:type  ifc:IfcSIUnit .

inst:IfcUnitAssignment_4
        ifc:units_IfcUnitAssignment  inst:IfcSIUnit_10 .

inst:IfcSIUnit_11  rdf:type  ifc:IfcSIUnit .

inst:IfcUnitAssignment_4
        ifc:units_IfcUnitAssignment  inst:IfcSIUnit_11 .

inst:IfcSIUnit_12  rdf:type  ifc:IfcSIUnit .

inst:IfcUnitAssignment_4
        ifc:units_IfcUnitAssignment  inst:IfcSIUnit_12 .

inst:IfcSIUnit_13  rdf:type  ifc:IfcSIUnit .

inst:IfcUnitAssignment_4
        ifc:units_IfcUnitAssignment  inst:IfcSIUnit_13 .

inst:IfcSIUnit_14  rdf:type  ifc:IfcSIUnit .

inst:IfcUnitAssignment_4
        ifc:units_IfcUnitAssignment  inst:IfcSIUnit_14 .

inst:IfcSIUnit_15  rdf:type  ifc:IfcSIUnit .

inst:IfcUnitAssignment_4
        ifc:units_IfcUnitAssignment  inst:IfcSIUnit_15 .

inst:IfcSIUnit_16  rdf:type  ifc:IfcSIUnit .

inst:IfcUnitAssignment_4
        ifc:units_IfcUnitAssignment  inst:IfcSIUnit_16 .

inst:IfcSIUnit_17  rdf:type  ifc:IfcSIUnit .

inst:IfcUnitAssignment_4
        ifc:units_IfcUnitAssignment  inst:IfcSIUnit_17 .

inst:IfcSIUnit_18  rdf:type  ifc:IfcSIUnit .

inst:IfcUnitAssignment_4
        ifc:units_IfcUnitAssignment  inst:IfcSIUnit_18 .

inst:IfcSIUnit_19  rdf:type  ifc:IfcSIUnit .

inst:IfcUnitAssignment_4
        ifc:units_IfcUnitAssignment  inst:IfcSIUnit_19 .

inst:IfcSIUnit_20  rdf:type  ifc:IfcSIUnit .

inst:IfcUnitAssignment_4
        ifc:units_IfcUnitAssignment  inst:IfcSIUnit_20 .

inst:IfcSIUnit_21  rdf:type  ifc:IfcSIUnit .

inst:IfcUnitAssignment_4
        ifc:units_IfcUnitAssignment  inst:IfcSIUnit_21 .

inst:IfcSIUnit_22  rdf:type  ifc:IfcSIUnit .

inst:IfcUnitAssignment_4
        ifc:units_IfcUnitAssignment  inst:IfcSIUnit_22 .

inst:IfcSIUnit_23  rdf:type  ifc:IfcSIUnit .

inst:IfcUnitAssignment_4
        ifc:units_IfcUnitAssignment  inst:IfcSIUnit_23 .

inst:IfcSIUnit_24  rdf:type  ifc:IfcSIUnit .

inst:IfcUnitAssignment_4
        ifc:units_IfcUnitAssignment  inst:IfcSIUnit_24 .

inst:IfcSIUnit_25  rdf:type  ifc:IfcSIUnit .

inst:IfcUnitAssignment_4
        ifc:units_IfcUnitAssignment  inst:IfcSIUnit_25 .

inst:IfcSIUnit_26  rdf:type  ifc:IfcSIUnit .

inst:IfcUnitAssignment_4
        ifc:units_IfcUnitAssignment  inst:IfcSIUnit_26 .

inst:IfcSIUnit_27  rdf:type  ifc:IfcSIUnit .

inst:IfcUnitAssignment_4
        ifc:units_IfcUnitAssignment  inst:IfcSIUnit_27 .

inst:IfcSIUnit_28  rdf:type  ifc:IfcSIUnit .

inst:IfcUnitAssignment_4
        ifc:units_IfcUnitAssignment  inst:IfcSIUnit_28 .

inst:IfcSIUnit_29  rdf:type  ifc:IfcSIUnit .

inst:IfcUnitAssignment_4
        ifc:units_IfcUnitAssignment  inst:IfcSIUnit_29 .

inst:IfcConversionBasedUnit_30
        rdf:type  ifc:IfcConversionBasedUnit .

inst:IfcUnitAssignment_4
        ifc:units_IfcUnitAssignment  inst:IfcConversionBasedUnit_30 .

inst:IfcSIUnit_31  rdf:type  ifc:IfcSIUnit .

inst:IfcUnitAssignment_4
        ifc:units_IfcUnitAssignment  inst:IfcSIUnit_31 .

inst:IfcSIUnit_32  rdf:type  ifc:IfcSIUnit .

inst:IfcUnitAssignment_4
        ifc:units_IfcUnitAssignment  inst:IfcSIUnit_32 .

inst:IfcSIUnit_33  rdf:type  ifc:IfcSIUnit .

inst:IfcUnitAssignment_4
        ifc:units_IfcUnitAssignment  inst:IfcSIUnit_33 .

inst:IfcSIUnit_34  rdf:type  ifc:IfcSIUnit .

inst:IfcUnitAssignment_4
        ifc:units_IfcUnitAssignment  inst:IfcSIUnit_34 .

inst:IfcSIUnit_35  rdf:type  ifc:IfcSIUnit .

inst:IfcUnitAssignment_4
        ifc:units_IfcUnitAssignment  inst:IfcSIUnit_35 .

inst:IfcSIUnit_36  rdf:type  ifc:IfcSIUnit .

inst:IfcUnitAssignment_4
        ifc:units_IfcUnitAssignment  inst:IfcSIUnit_36 .

inst:IfcSIUnit_37  rdf:type  ifc:IfcSIUnit .

inst:IfcUnitAssignment_4
        ifc:units_IfcUnitAssignment  inst:IfcSIUnit_37 .

inst:IfcDerivedUnit_38
        rdf:type  ifc:IfcDerivedUnit .

inst:IfcUnitAssignment_4
        ifc:units_IfcUnitAssignment  inst:IfcDerivedUnit_38 .

inst:IfcDerivedUnit_39
        rdf:type  ifc:IfcDerivedUnit .

inst:IfcUnitAssignment_4
        ifc:units_IfcUnitAssignment  inst:IfcDerivedUnit_39 .

inst:IfcDerivedUnit_40
        rdf:type  ifc:IfcDerivedUnit .

inst:IfcUnitAssignment_4
        ifc:units_IfcUnitAssignment  inst:IfcDerivedUnit_40 .

inst:IfcDerivedUnit_41
        rdf:type  ifc:IfcDerivedUnit .

inst:IfcUnitAssignment_4
        ifc:units_IfcUnitAssignment  inst:IfcDerivedUnit_41 .

inst:IfcDerivedUnit_42
        rdf:type  ifc:IfcDerivedUnit .

inst:IfcUnitAssignment_4
        ifc:units_IfcUnitAssignment  inst:IfcDerivedUnit_42 .

inst:IfcDerivedUnit_43
        rdf:type  ifc:IfcDerivedUnit .

inst:IfcUnitAssignment_4
        ifc:units_IfcUnitAssignment  inst:IfcDerivedUnit_43 .

inst:IfcDerivedUnit_44
        rdf:type  ifc:IfcDerivedUnit .

inst:IfcUnitAssignment_4
        ifc:units_IfcUnitAssignment  inst:IfcDerivedUnit_44 .

inst:IfcDerivedUnit_45
        rdf:type  ifc:IfcDerivedUnit .

inst:IfcUnitAssignment_4
        ifc:units_IfcUnitAssignment  inst:IfcDerivedUnit_45 .

inst:IfcDerivedUnit_46
        rdf:type  ifc:IfcDerivedUnit .

inst:IfcUnitAssignment_4
        ifc:units_IfcUnitAssignment  inst:IfcDerivedUnit_46 .

inst:IfcDerivedUnit_47
        rdf:type  ifc:IfcDerivedUnit .

inst:IfcUnitAssignment_4
        ifc:units_IfcUnitAssignment  inst:IfcDerivedUnit_47 .

inst:IfcDerivedUnit_48
        rdf:type  ifc:IfcDerivedUnit .

inst:IfcUnitAssignment_4
        ifc:units_IfcUnitAssignment  inst:IfcDerivedUnit_48 .

inst:IfcDerivedUnit_49
        rdf:type  ifc:IfcDerivedUnit .

inst:IfcUnitAssignment_4
        ifc:units_IfcUnitAssignment  inst:IfcDerivedUnit_49 .

inst:IfcDerivedUnit_50
        rdf:type  ifc:IfcDerivedUnit .

inst:IfcUnitAssignment_4
        ifc:units_IfcUnitAssignment  inst:IfcDerivedUnit_50 .

inst:IfcDerivedUnit_51
        rdf:type  ifc:IfcDerivedUnit .

inst:IfcUnitAssignment_4
        ifc:units_IfcUnitAssignment  inst:IfcDerivedUnit_51 .

inst:IfcDerivedUnit_52
        rdf:type  ifc:IfcDerivedUnit .

inst:IfcUnitAssignment_4
        ifc:units_IfcUnitAssignment  inst:IfcDerivedUnit_52 .

inst:IfcDerivedUnit_53
        rdf:type  ifc:IfcDerivedUnit .

inst:IfcUnitAssignment_4
        ifc:units_IfcUnitAssignment  inst:IfcDerivedUnit_53 .

inst:IfcDerivedUnit_54
        rdf:type  ifc:IfcDerivedUnit .

inst:IfcUnitAssignment_4
        ifc:units_IfcUnitAssignment  inst:IfcDerivedUnit_54 .

inst:IfcDerivedUnit_55
        rdf:type  ifc:IfcDerivedUnit .

inst:IfcUnitAssignment_4
        ifc:units_IfcUnitAssignment  inst:IfcDerivedUnit_55 .

inst:IfcDerivedUnit_56
        rdf:type  ifc:IfcDerivedUnit .

inst:IfcUnitAssignment_4
        ifc:units_IfcUnitAssignment  inst:IfcDerivedUnit_56 .

inst:IfcDerivedUnit_57
        rdf:type  ifc:IfcDerivedUnit .

inst:IfcUnitAssignment_4
        ifc:units_IfcUnitAssignment  inst:IfcDerivedUnit_57 .

inst:IfcDerivedUnit_58
        rdf:type  ifc:IfcDerivedUnit .

inst:IfcUnitAssignment_4
        ifc:units_IfcUnitAssignment  inst:IfcDerivedUnit_58 .

inst:IfcDerivedUnit_59
        rdf:type  ifc:IfcDerivedUnit .

inst:IfcUnitAssignment_4
        ifc:units_IfcUnitAssignment  inst:IfcDerivedUnit_59 .

inst:IfcDerivedUnit_60
        rdf:type  ifc:IfcDerivedUnit .

inst:IfcUnitAssignment_4
        ifc:units_IfcUnitAssignment  inst:IfcDerivedUnit_60 .

inst:IfcDerivedUnit_61
        rdf:type  ifc:IfcDerivedUnit .

inst:IfcUnitAssignment_4
        ifc:units_IfcUnitAssignment  inst:IfcDerivedUnit_61 .

inst:IfcDerivedUnit_62
        rdf:type  ifc:IfcDerivedUnit .

inst:IfcUnitAssignment_4
        ifc:units_IfcUnitAssignment  inst:IfcDerivedUnit_62 .

inst:IfcDerivedUnit_63
        rdf:type  ifc:IfcDerivedUnit .

inst:IfcUnitAssignment_4
        ifc:units_IfcUnitAssignment  inst:IfcDerivedUnit_63 .

inst:IfcDerivedUnit_64
        rdf:type  ifc:IfcDerivedUnit .

inst:IfcUnitAssignment_4
        ifc:units_IfcUnitAssignment  inst:IfcDerivedUnit_64 .

inst:IfcDerivedUnit_65
        rdf:type  ifc:IfcDerivedUnit .

inst:IfcUnitAssignment_4
        ifc:units_IfcUnitAssignment  inst:IfcDerivedUnit_65 .

inst:IfcDerivedUnit_66
        rdf:type  ifc:IfcDerivedUnit .

inst:IfcUnitAssignment_4
        ifc:units_IfcUnitAssignment  inst:IfcDerivedUnit_66 .

inst:IfcDerivedUnit_67
        rdf:type  ifc:IfcDerivedUnit .

inst:IfcUnitAssignment_4
        ifc:units_IfcUnitAssignment  inst:IfcDerivedUnit_67 .

inst:IfcDerivedUnit_68
        rdf:type  ifc:IfcDerivedUnit .

inst:IfcUnitAssignment_4
        ifc:units_IfcUnitAssignment  inst:IfcDerivedUnit_68 .

inst:IfcDerivedUnit_69
        rdf:type  ifc:IfcDerivedUnit .

inst:IfcUnitAssignment_4
        ifc:units_IfcUnitAssignment  inst:IfcDerivedUnit_69 .

inst:IfcDerivedUnit_70
        rdf:type  ifc:IfcDerivedUnit .

inst:IfcUnitAssignment_4
        ifc:units_IfcUnitAssignment  inst:IfcDerivedUnit_70 .

inst:IfcDerivedUnit_71
        rdf:type  ifc:IfcDerivedUnit .

inst:IfcUnitAssignment_4
        ifc:units_IfcUnitAssignment  inst:IfcDerivedUnit_71 .

inst:IfcDerivedUnit_72
        rdf:type  ifc:IfcDerivedUnit .

inst:IfcUnitAssignment_4
        ifc:units_IfcUnitAssignment  inst:IfcDerivedUnit_72 .

inst:IfcDerivedUnit_73
        rdf:type  ifc:IfcDerivedUnit .

inst:IfcUnitAssignment_4
        ifc:units_IfcUnitAssignment  inst:IfcDerivedUnit_73 .

inst:IfcDerivedUnit_74
        rdf:type  ifc:IfcDerivedUnit .

inst:IfcUnitAssignment_4
        ifc:units_IfcUnitAssignment  inst:IfcDerivedUnit_74 .

inst:IfcDerivedUnit_75
        rdf:type  ifc:IfcDerivedUnit .

inst:IfcUnitAssignment_4
        ifc:units_IfcUnitAssignment  inst:IfcDerivedUnit_75 .

inst:IfcDerivedUnit_76
        rdf:type  ifc:IfcDerivedUnit .

inst:IfcUnitAssignment_4
        ifc:units_IfcUnitAssignment  inst:IfcDerivedUnit_76 .

inst:IfcDerivedUnit_77
        rdf:type  ifc:IfcDerivedUnit .

inst:IfcUnitAssignment_4
        ifc:units_IfcUnitAssignment  inst:IfcDerivedUnit_77 .

inst:IfcDerivedUnit_78
        rdf:type  ifc:IfcDerivedUnit .

inst:IfcUnitAssignment_4
        ifc:units_IfcUnitAssignment  inst:IfcDerivedUnit_78 .

inst:IfcDerivedUnit_79
        rdf:type  ifc:IfcDerivedUnit .

inst:IfcUnitAssignment_4
        ifc:units_IfcUnitAssignment  inst:IfcDerivedUnit_79 .

inst:IfcDerivedUnit_80
        rdf:type  ifc:IfcDerivedUnit .

inst:IfcUnitAssignment_4
        ifc:units_IfcUnitAssignment  inst:IfcDerivedUnit_80 .

inst:IfcDerivedUnit_81
        rdf:type  ifc:IfcDerivedUnit .

inst:IfcUnitAssignment_4
        ifc:units_IfcUnitAssignment  inst:IfcDerivedUnit_81 .

inst:IfcDerivedUnit_82
        rdf:type  ifc:IfcDerivedUnit .

inst:IfcUnitAssignment_4
        ifc:units_IfcUnitAssignment  inst:IfcDerivedUnit_82 .

inst:IfcDerivedUnit_83
        rdf:type  ifc:IfcDerivedUnit .

inst:IfcUnitAssignment_4
        ifc:units_IfcUnitAssignment  inst:IfcDerivedUnit_83 .

inst:IfcDerivedUnit_84
        rdf:type  ifc:IfcDerivedUnit .

inst:IfcUnitAssignment_4
        ifc:units_IfcUnitAssignment  inst:IfcDerivedUnit_84 .

inst:IfcDerivedUnit_85
        rdf:type  ifc:IfcDerivedUnit .

inst:IfcUnitAssignment_4
        ifc:units_IfcUnitAssignment  inst:IfcDerivedUnit_85 .

inst:IfcMonetaryUnit_86
        rdf:type  ifc:IfcMonetaryUnit .

inst:IfcUnitAssignment_4
        ifc:units_IfcUnitAssignment  inst:IfcMonetaryUnit_86 .

inst:IfcRelAggregates_5
        rdf:type  ifc:IfcRelAggregates .

inst:IfcGloballyUniqueId_239
        rdf:type           ifc:IfcGloballyUniqueId ;
        express:hasString  "2itvH51TLF$vSczv308eND" .

inst:IfcRelAggregates_5
        ifc:globalId_IfcRoot  inst:IfcGloballyUniqueId_239 ;
        ifc:relatingObject_IfcRelAggregates  inst:IfcProject_1 .

inst:IfcSite_191  rdf:type  ifc:IfcSite .

inst:IfcRelAggregates_5
        ifc:relatedObjects_IfcRelAggregates  inst:IfcSite_191 .

inst:IfcRelDeclares_6
        rdf:type  ifc:IfcRelDeclares .

inst:IfcGloballyUniqueId_240
        rdf:type           ifc:IfcGloballyUniqueId ;
        express:hasString  "1HmT$jWs52sPjBfKd4uec0" .

inst:IfcRelDeclares_6
        ifc:globalId_IfcRoot  inst:IfcGloballyUniqueId_240 ;
        ifc:relatingContext_IfcRelDeclares  inst:IfcProject_1 .

inst:IfcMemberType_192
        rdf:type  ifc:IfcMemberType .

inst:IfcRelDeclares_6
        ifc:relatedDefinitions_IfcRelDeclares  inst:IfcMemberType_192 .

inst:IfcCartesianPoint_87
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcAxis2Placement3D_7
        ifc:location_IfcPlacement  inst:IfcCartesianPoint_87 .

inst:IfcAxis2Placement3D_8
        ifc:location_IfcPlacement  inst:IfcCartesianPoint_87 .

inst:IfcSIUnit_9  ifc:unitType_IfcNamedUnit  ifc:ABSORBEDDOSEUNIT ;
        ifc:name_IfcSIUnit         ifc:GRAY .

inst:IfcSIUnit_10  ifc:unitType_IfcNamedUnit  ifc:AMOUNTOFSUBSTANCEUNIT ;
        ifc:name_IfcSIUnit         ifc:MOLE .

inst:IfcSIUnit_11  ifc:unitType_IfcNamedUnit  ifc:AREAUNIT ;
        ifc:name_IfcSIUnit         ifc:SQUARE_METRE .

inst:IfcSIUnit_12  ifc:unitType_IfcNamedUnit  ifc:DOSEEQUIVALENTUNIT ;
        ifc:name_IfcSIUnit         ifc:SIEVERT .

inst:IfcSIUnit_13  ifc:unitType_IfcNamedUnit  ifc:ELECTRICCAPACITANCEUNIT ;
        ifc:name_IfcSIUnit         ifc:FARAD .

inst:IfcSIUnit_14  ifc:unitType_IfcNamedUnit  ifc:ELECTRICCHARGEUNIT ;
        ifc:name_IfcSIUnit         ifc:COULOMB .

inst:IfcSIUnit_15  ifc:unitType_IfcNamedUnit  ifc:ELECTRICCONDUCTANCEUNIT ;
        ifc:name_IfcSIUnit         ifc:SIEMENS .

inst:IfcSIUnit_16  ifc:unitType_IfcNamedUnit  ifc:ELECTRICCURRENTUNIT ;
        ifc:name_IfcSIUnit         ifc:AMPERE .

inst:IfcSIUnit_17  ifc:unitType_IfcNamedUnit  ifc:ELECTRICRESISTANCEUNIT ;
        ifc:name_IfcSIUnit         ifc:OHM .

inst:IfcSIUnit_18  ifc:unitType_IfcNamedUnit  ifc:ELECTRICVOLTAGEUNIT ;
        ifc:name_IfcSIUnit         ifc:VOLT .

inst:IfcSIUnit_19  ifc:unitType_IfcNamedUnit  ifc:ENERGYUNIT ;
        ifc:name_IfcSIUnit         ifc:JOULE .

inst:IfcSIUnit_20  ifc:unitType_IfcNamedUnit  ifc:FORCEUNIT ;
        ifc:name_IfcSIUnit         ifc:NEWTON .

inst:IfcSIUnit_21  ifc:unitType_IfcNamedUnit  ifc:FREQUENCYUNIT ;
        ifc:name_IfcSIUnit         ifc:HERTZ .

inst:IfcSIUnit_22  ifc:unitType_IfcNamedUnit  ifc:ILLUMINANCEUNIT ;
        ifc:name_IfcSIUnit         ifc:LUX .

inst:IfcSIUnit_23  ifc:unitType_IfcNamedUnit  ifc:INDUCTANCEUNIT ;
        ifc:name_IfcSIUnit         ifc:HENRY .

inst:IfcSIUnit_24  ifc:unitType_IfcNamedUnit  ifc:LENGTHUNIT ;
        ifc:name_IfcSIUnit         ifc:METRE .

inst:IfcSIUnit_25  ifc:unitType_IfcNamedUnit  ifc:LUMINOUSFLUXUNIT ;
        ifc:name_IfcSIUnit         ifc:LUMEN .

inst:IfcSIUnit_26  ifc:unitType_IfcNamedUnit  ifc:LUMINOUSINTENSITYUNIT ;
        ifc:name_IfcSIUnit         ifc:CANDELA .

inst:IfcSIUnit_27  ifc:unitType_IfcNamedUnit  ifc:MAGNETICFLUXDENSITYUNIT ;
        ifc:name_IfcSIUnit         ifc:TESLA .

inst:IfcSIUnit_28  ifc:unitType_IfcNamedUnit  ifc:MAGNETICFLUXUNIT ;
        ifc:name_IfcSIUnit         ifc:WEBER .

inst:IfcSIUnit_29  ifc:unitType_IfcNamedUnit  ifc:MASSUNIT ;
        ifc:prefix_IfcSIUnit       ifc:KILO ;
        ifc:name_IfcSIUnit         ifc:GRAM .

inst:IfcDimensionalExponents_89
        rdf:type  ifc:IfcDimensionalExponents .

inst:IfcConversionBasedUnit_30
        ifc:dimensions_IfcNamedUnit  inst:IfcDimensionalExponents_89 ;
        ifc:unitType_IfcNamedUnit    ifc:PLANEANGLEUNIT .

inst:IfcLabel_241  rdf:type  ifc:IfcLabel ;
        express:hasString  "degree" .

inst:IfcConversionBasedUnit_30
        ifc:name_IfcConversionBasedUnit  inst:IfcLabel_241 .

inst:IfcMeasureWithUnit_90
        rdf:type  ifc:IfcMeasureWithUnit .

inst:IfcConversionBasedUnit_30
        ifc:conversionFactor_IfcConversionBasedUnit  inst:IfcMeasureWithUnit_90 .

inst:IfcSIUnit_31  ifc:unitType_IfcNamedUnit  ifc:POWERUNIT ;
        ifc:name_IfcSIUnit         ifc:WATT .

inst:IfcSIUnit_32  ifc:unitType_IfcNamedUnit  ifc:PRESSUREUNIT ;
        ifc:name_IfcSIUnit         ifc:PASCAL .

inst:IfcSIUnit_33  ifc:unitType_IfcNamedUnit  ifc:RADIOACTIVITYUNIT ;
        ifc:name_IfcSIUnit         ifc:BECQUEREL .

inst:IfcSIUnit_34  ifc:unitType_IfcNamedUnit  ifc:SOLIDANGLEUNIT ;
        ifc:name_IfcSIUnit         ifc:STERADIAN .

inst:IfcSIUnit_35  ifc:unitType_IfcNamedUnit  ifc:THERMODYNAMICTEMPERATUREUNIT ;
        ifc:name_IfcSIUnit         ifc:DEGREE_CELSIUS .

inst:IfcSIUnit_36  ifc:unitType_IfcNamedUnit  ifc:TIMEUNIT ;
        ifc:name_IfcSIUnit         ifc:SECOND .

inst:IfcSIUnit_37  ifc:unitType_IfcNamedUnit  ifc:VOLUMEUNIT ;
        ifc:name_IfcSIUnit         ifc:CUBIC_METRE .

inst:IfcDerivedUnitElement_91
        rdf:type  ifc:IfcDerivedUnitElement .

inst:IfcDerivedUnit_38
        ifc:elements_IfcDerivedUnit  inst:IfcDerivedUnitElement_91 .

inst:IfcDerivedUnitElement_92
        rdf:type  ifc:IfcDerivedUnitElement .

inst:IfcDerivedUnit_38
        ifc:elements_IfcDerivedUnit  inst:IfcDerivedUnitElement_92 ;
        ifc:elements_IfcDerivedUnit  inst:IfcDerivedUnitElement_92 ;
        ifc:unitType_IfcDerivedUnit  ifc:ACCELERATIONUNIT .

inst:IfcDerivedUnitElement_94
        rdf:type  ifc:IfcDerivedUnitElement .

inst:IfcDerivedUnit_39
        ifc:elements_IfcDerivedUnit  inst:IfcDerivedUnitElement_94 ;
        ifc:elements_IfcDerivedUnit  inst:IfcDerivedUnitElement_92 ;
        ifc:unitType_IfcDerivedUnit  ifc:ANGULARVELOCITYUNIT .

inst:IfcDerivedUnit_40
        ifc:elements_IfcDerivedUnit  inst:IfcDerivedUnitElement_94 ;
        ifc:unitType_IfcDerivedUnit  ifc:COMPOUNDPLANEANGLEUNIT .

inst:IfcDerivedUnit_41
        ifc:elements_IfcDerivedUnit  inst:IfcDerivedUnitElement_94 .

inst:IfcDerivedUnitElement_98
        rdf:type  ifc:IfcDerivedUnitElement .

inst:IfcDerivedUnit_41
        ifc:elements_IfcDerivedUnit  inst:IfcDerivedUnitElement_98 ;
        ifc:unitType_IfcDerivedUnit  ifc:CURVATUREUNIT .

inst:IfcDerivedUnitElement_99
        rdf:type  ifc:IfcDerivedUnitElement .

inst:IfcDerivedUnit_42
        ifc:elements_IfcDerivedUnit  inst:IfcDerivedUnitElement_99 .

inst:IfcDerivedUnitElement_100
        rdf:type  ifc:IfcDerivedUnitElement .

inst:IfcDerivedUnit_42
        ifc:elements_IfcDerivedUnit  inst:IfcDerivedUnitElement_100 ;
        ifc:unitType_IfcDerivedUnit  ifc:DYNAMICVISCOSITYUNIT .

inst:IfcDerivedUnitElement_101
        rdf:type  ifc:IfcDerivedUnitElement .

inst:IfcDerivedUnit_43
        ifc:elements_IfcDerivedUnit  inst:IfcDerivedUnitElement_101 .

inst:IfcDerivedUnitElement_102
        rdf:type  ifc:IfcDerivedUnitElement .

inst:IfcDerivedUnit_43
        ifc:elements_IfcDerivedUnit  inst:IfcDerivedUnitElement_102 ;
        ifc:unitType_IfcDerivedUnit  ifc:HEATFLUXDENSITYUNIT .

inst:IfcDerivedUnitElement_103
        rdf:type  ifc:IfcDerivedUnitElement .

inst:IfcDerivedUnit_44
        ifc:elements_IfcDerivedUnit  inst:IfcDerivedUnitElement_103 .

inst:IfcDerivedUnitElement_104
        rdf:type  ifc:IfcDerivedUnitElement .

inst:IfcDerivedUnit_44
        ifc:elements_IfcDerivedUnit  inst:IfcDerivedUnitElement_104 ;
        ifc:unitType_IfcDerivedUnit  ifc:HEATINGVALUEUNIT .

inst:IfcDerivedUnitElement_105
        rdf:type  ifc:IfcDerivedUnitElement .

inst:IfcDerivedUnit_45
        ifc:elements_IfcDerivedUnit  inst:IfcDerivedUnitElement_105 .

inst:IfcDerivedUnitElement_106
        rdf:type  ifc:IfcDerivedUnitElement .

inst:IfcDerivedUnit_45
        ifc:elements_IfcDerivedUnit  inst:IfcDerivedUnitElement_106 ;
        ifc:unitType_IfcDerivedUnit  ifc:IONCONCENTRATIONUNIT .

inst:IfcDerivedUnit_46
        ifc:elements_IfcDerivedUnit  inst:IfcDerivedUnitElement_92 ;
        ifc:unitType_IfcDerivedUnit  ifc:INTEGERCOUNTRATEUNIT .

inst:IfcDerivedUnitElement_108
        rdf:type  ifc:IfcDerivedUnitElement .

inst:IfcDerivedUnit_47
        ifc:elements_IfcDerivedUnit  inst:IfcDerivedUnitElement_108 .

inst:IfcDerivedUnitElement_109
        rdf:type  ifc:IfcDerivedUnitElement .

inst:IfcDerivedUnit_47
        ifc:elements_IfcDerivedUnit  inst:IfcDerivedUnitElement_109 ;
        ifc:unitType_IfcDerivedUnit  ifc:ISOTHERMALMOISTURECAPACITYUNIT .

inst:IfcDerivedUnitElement_110
        rdf:type  ifc:IfcDerivedUnitElement .

inst:IfcDerivedUnit_48
        ifc:elements_IfcDerivedUnit  inst:IfcDerivedUnitElement_110 ;
        ifc:elements_IfcDerivedUnit  inst:IfcDerivedUnitElement_92 ;
        ifc:unitType_IfcDerivedUnit  ifc:KINEMATICVISCOSITYUNIT .

inst:IfcDerivedUnitElement_112
        rdf:type  ifc:IfcDerivedUnitElement .

inst:IfcDerivedUnit_49
        ifc:elements_IfcDerivedUnit  inst:IfcDerivedUnitElement_112 ;
        ifc:elements_IfcDerivedUnit  inst:IfcDerivedUnitElement_98 ;
        ifc:unitType_IfcDerivedUnit  ifc:LINEARFORCEUNIT .

inst:IfcDerivedUnit_50
        ifc:elements_IfcDerivedUnit  inst:IfcDerivedUnitElement_112 ;
        ifc:elements_IfcDerivedUnit  inst:IfcDerivedUnitElement_91 ;
        ifc:elements_IfcDerivedUnit  inst:IfcDerivedUnitElement_98 ;
        ifc:unitType_IfcDerivedUnit  ifc:LINEARMOMENTUNIT .

inst:IfcDerivedUnit_51
        ifc:elements_IfcDerivedUnit  inst:IfcDerivedUnitElement_112 ;
        ifc:elements_IfcDerivedUnit  inst:IfcDerivedUnitElement_98 ;
        ifc:unitType_IfcDerivedUnit  ifc:LINEARSTIFFNESSUNIT .

inst:IfcDerivedUnit_52
        ifc:elements_IfcDerivedUnit  inst:IfcDerivedUnitElement_91 ;
        ifc:elements_IfcDerivedUnit  inst:IfcDerivedUnitElement_92 ;
        ifc:unitType_IfcDerivedUnit  ifc:LINEARVELOCITYUNIT .

inst:IfcDerivedUnitElement_121
        rdf:type  ifc:IfcDerivedUnitElement .

inst:IfcDerivedUnit_53
        ifc:elements_IfcDerivedUnit  inst:IfcDerivedUnitElement_121 .

inst:IfcDerivedUnitElement_122
        rdf:type  ifc:IfcDerivedUnitElement .

inst:IfcDerivedUnit_53
        ifc:elements_IfcDerivedUnit  inst:IfcDerivedUnitElement_122 ;
        ifc:unitType_IfcDerivedUnit  ifc:LUMINOUSINTENSITYDISTRIBUTIONUNIT .

inst:IfcDerivedUnitElement_123
        rdf:type  ifc:IfcDerivedUnitElement .

inst:IfcDerivedUnit_54
        ifc:elements_IfcDerivedUnit  inst:IfcDerivedUnitElement_123 ;
        ifc:elements_IfcDerivedUnit  inst:IfcDerivedUnitElement_106 ;
        ifc:unitType_IfcDerivedUnit  ifc:MASSDENSITYUNIT .

inst:IfcDerivedUnit_55
        ifc:elements_IfcDerivedUnit  inst:IfcDerivedUnitElement_123 ;
        ifc:elements_IfcDerivedUnit  inst:IfcDerivedUnitElement_92 ;
        ifc:unitType_IfcDerivedUnit  ifc:MASSFLOWRATEUNIT .

inst:IfcDerivedUnit_56
        ifc:elements_IfcDerivedUnit  inst:IfcDerivedUnitElement_123 ;
        ifc:elements_IfcDerivedUnit  inst:IfcDerivedUnitElement_98 ;
        ifc:unitType_IfcDerivedUnit  ifc:MASSPERLENGTHUNIT .

inst:IfcDerivedUnit_57
        ifc:elements_IfcDerivedUnit  inst:IfcDerivedUnitElement_99 ;
        ifc:unitType_IfcDerivedUnit  ifc:MODULUSOFELASTICITYUNIT .

inst:IfcDerivedUnit_58
        ifc:elements_IfcDerivedUnit  inst:IfcDerivedUnitElement_112 ;
        ifc:elements_IfcDerivedUnit  inst:IfcDerivedUnitElement_102 ;
        ifc:unitType_IfcDerivedUnit  ifc:MODULUSOFLINEARSUBGRADEREACTIONUNIT .

inst:IfcDerivedUnit_59
        ifc:elements_IfcDerivedUnit  inst:IfcDerivedUnitElement_112 ;
        ifc:elements_IfcDerivedUnit  inst:IfcDerivedUnitElement_91 ;
        ifc:elements_IfcDerivedUnit  inst:IfcDerivedUnitElement_98 .

inst:IfcDerivedUnitElement_135
        rdf:type  ifc:IfcDerivedUnitElement .

inst:IfcDerivedUnit_59
        ifc:elements_IfcDerivedUnit  inst:IfcDerivedUnitElement_135 ;
        ifc:unitType_IfcDerivedUnit  ifc:MODULUSOFROTATIONALSUBGRADEREACTIONUNIT .

inst:IfcDerivedUnit_60
        ifc:elements_IfcDerivedUnit  inst:IfcDerivedUnitElement_112 ;
        ifc:elements_IfcDerivedUnit  inst:IfcDerivedUnitElement_106 ;
        ifc:unitType_IfcDerivedUnit  ifc:MODULUSOFSUBGRADEREACTIONUNIT .

inst:IfcDerivedUnit_61
        ifc:elements_IfcDerivedUnit  inst:IfcDerivedUnitElement_108 ;
        ifc:elements_IfcDerivedUnit  inst:IfcDerivedUnitElement_92 ;
        ifc:unitType_IfcDerivedUnit  ifc:MOISTUREDIFFUSIVITYUNIT .

inst:IfcDerivedUnit_62
        ifc:elements_IfcDerivedUnit  inst:IfcDerivedUnitElement_105 .

inst:IfcDerivedUnitElement_141
        rdf:type  ifc:IfcDerivedUnitElement .

inst:IfcDerivedUnit_62
        ifc:elements_IfcDerivedUnit  inst:IfcDerivedUnitElement_141 ;
        ifc:unitType_IfcDerivedUnit  ifc:MOLECULARWEIGHTUNIT .

inst:IfcDerivedUnitElement_142
        rdf:type  ifc:IfcDerivedUnitElement .

inst:IfcDerivedUnit_63
        ifc:elements_IfcDerivedUnit  inst:IfcDerivedUnitElement_142 ;
        ifc:unitType_IfcDerivedUnit  ifc:MOMENTOFINERTIAUNIT .

inst:IfcDerivedUnit_64
        ifc:elements_IfcDerivedUnit  inst:IfcDerivedUnitElement_112 ;
        ifc:elements_IfcDerivedUnit  inst:IfcDerivedUnitElement_102 ;
        ifc:unitType_IfcDerivedUnit  ifc:PLANARFORCEUNIT .

inst:IfcDerivedUnit_65
        ifc:elements_IfcDerivedUnit  inst:IfcDerivedUnitElement_92 ;
        ifc:unitType_IfcDerivedUnit  ifc:ROTATIONALFREQUENCYUNIT .

inst:IfcDerivedUnit_66
        ifc:elements_IfcDerivedUnit  inst:IfcDerivedUnitElement_123 ;
        ifc:elements_IfcDerivedUnit  inst:IfcDerivedUnitElement_102 ;
        ifc:unitType_IfcDerivedUnit  ifc:ROTATIONALMASSUNIT .

inst:IfcDerivedUnit_67
        ifc:elements_IfcDerivedUnit  inst:IfcDerivedUnitElement_112 ;
        ifc:elements_IfcDerivedUnit  inst:IfcDerivedUnitElement_91 ;
        ifc:elements_IfcDerivedUnit  inst:IfcDerivedUnitElement_135 ;
        ifc:unitType_IfcDerivedUnit  ifc:ROTATIONALSTIFFNESSUNIT .

inst:IfcDerivedUnitElement_151
        rdf:type  ifc:IfcDerivedUnitElement .

inst:IfcDerivedUnit_68
        ifc:elements_IfcDerivedUnit  inst:IfcDerivedUnitElement_151 ;
        ifc:unitType_IfcDerivedUnit  ifc:SECTIONAREAINTEGRALUNIT .

inst:IfcDerivedUnitElement_152
        rdf:type  ifc:IfcDerivedUnitElement .

inst:IfcDerivedUnit_69
        ifc:elements_IfcDerivedUnit  inst:IfcDerivedUnitElement_152 ;
        ifc:unitType_IfcDerivedUnit  ifc:SECTIONMODULUSUNIT .

inst:IfcDerivedUnit_70
        ifc:elements_IfcDerivedUnit  inst:IfcDerivedUnitElement_99 ;
        ifc:unitType_IfcDerivedUnit  ifc:SHEARMODULUSUNIT .

inst:IfcDerivedUnitElement_154
        rdf:type  ifc:IfcDerivedUnitElement .

inst:IfcDerivedUnit_71
        ifc:elements_IfcDerivedUnit  inst:IfcDerivedUnitElement_154 ;
        ifc:unitType_IfcDerivedUnit  ifc:SOUNDPOWERUNIT .

inst:IfcDerivedUnitElement_155
        rdf:type  ifc:IfcDerivedUnitElement .

inst:IfcDerivedUnit_72
        ifc:elements_IfcDerivedUnit  inst:IfcDerivedUnitElement_155 ;
        ifc:unitType_IfcDerivedUnit  ifc:SOUNDPRESSUREUNIT .

inst:IfcDerivedUnit_73
        ifc:elements_IfcDerivedUnit  inst:IfcDerivedUnitElement_103 ;
        ifc:elements_IfcDerivedUnit  inst:IfcDerivedUnitElement_104 .

inst:IfcDerivedUnitElement_158
        rdf:type  ifc:IfcDerivedUnitElement .

inst:IfcDerivedUnit_73
        ifc:elements_IfcDerivedUnit  inst:IfcDerivedUnitElement_158 ;
        ifc:unitType_IfcDerivedUnit  ifc:SPECIFICHEATCAPACITYUNIT .

inst:IfcDerivedUnitElement_159
        rdf:type  ifc:IfcDerivedUnitElement .

inst:IfcDerivedUnit_74
        ifc:elements_IfcDerivedUnit  inst:IfcDerivedUnitElement_159 ;
        ifc:elements_IfcDerivedUnit  inst:IfcDerivedUnitElement_98 ;
        ifc:unitType_IfcDerivedUnit  ifc:TEMPERATUREGRADIENTUNIT .

inst:IfcDerivedUnit_75
        ifc:elements_IfcDerivedUnit  inst:IfcDerivedUnitElement_159 ;
        ifc:elements_IfcDerivedUnit  inst:IfcDerivedUnitElement_92 ;
        ifc:unitType_IfcDerivedUnit  ifc:TEMPERATURERATEOFCHANGEUNIT .

inst:IfcDerivedUnit_76
        ifc:elements_IfcDerivedUnit  inst:IfcDerivedUnitElement_101 ;
        ifc:elements_IfcDerivedUnit  inst:IfcDerivedUnitElement_102 ;
        ifc:elements_IfcDerivedUnit  inst:IfcDerivedUnitElement_158 ;
        ifc:unitType_IfcDerivedUnit  ifc:THERMALADMITTANCEUNIT .

inst:IfcDerivedUnit_77
        ifc:elements_IfcDerivedUnit  inst:IfcDerivedUnitElement_101 ;
        ifc:elements_IfcDerivedUnit  inst:IfcDerivedUnitElement_158 ;
        ifc:elements_IfcDerivedUnit  inst:IfcDerivedUnitElement_98 ;
        ifc:unitType_IfcDerivedUnit  ifc:THERMALCONDUCTANCEUNIT .

inst:IfcDerivedUnit_78
        ifc:elements_IfcDerivedUnit  inst:IfcDerivedUnitElement_158 ;
        ifc:unitType_IfcDerivedUnit  ifc:THERMALEXPANSIONCOEFFICIENTUNIT .

inst:IfcDerivedUnit_79
        ifc:elements_IfcDerivedUnit  inst:IfcDerivedUnitElement_110 ;
        ifc:elements_IfcDerivedUnit  inst:IfcDerivedUnitElement_159 .

inst:IfcDerivedUnitElement_172
        rdf:type  ifc:IfcDerivedUnitElement .

inst:IfcDerivedUnit_79
        ifc:elements_IfcDerivedUnit  inst:IfcDerivedUnitElement_172 ;
        ifc:unitType_IfcDerivedUnit  ifc:THERMALRESISTANCEUNIT .

inst:IfcDerivedUnit_80
        ifc:elements_IfcDerivedUnit  inst:IfcDerivedUnitElement_101 ;
        ifc:elements_IfcDerivedUnit  inst:IfcDerivedUnitElement_102 ;
        ifc:elements_IfcDerivedUnit  inst:IfcDerivedUnitElement_159 ;
        ifc:unitType_IfcDerivedUnit  ifc:THERMALTRANSMITTANCEUNIT .

inst:IfcDerivedUnit_81
        ifc:elements_IfcDerivedUnit  inst:IfcDerivedUnitElement_112 ;
        ifc:elements_IfcDerivedUnit  inst:IfcDerivedUnitElement_91 ;
        ifc:unitType_IfcDerivedUnit  ifc:TORQUEUNIT .

inst:IfcDerivedUnit_82
        ifc:elements_IfcDerivedUnit  inst:IfcDerivedUnitElement_123 ;
        ifc:elements_IfcDerivedUnit  inst:IfcDerivedUnitElement_92 ;
        ifc:elements_IfcDerivedUnit  inst:IfcDerivedUnitElement_98 ;
        ifc:unitType_IfcDerivedUnit  ifc:VAPORPERMEABILITYUNIT .

inst:IfcDerivedUnit_83
        ifc:elements_IfcDerivedUnit  inst:IfcDerivedUnitElement_108 ;
        ifc:elements_IfcDerivedUnit  inst:IfcDerivedUnitElement_92 ;
        ifc:unitType_IfcDerivedUnit  ifc:VOLUMETRICFLOWRATEUNIT .

inst:IfcDerivedUnitElement_183
        rdf:type  ifc:IfcDerivedUnitElement .

inst:IfcDerivedUnit_84
        ifc:elements_IfcDerivedUnit  inst:IfcDerivedUnitElement_183 ;
        ifc:unitType_IfcDerivedUnit  ifc:WARPINGCONSTANTUNIT .

inst:IfcDerivedUnit_85
        ifc:elements_IfcDerivedUnit  inst:IfcDerivedUnitElement_112 .

inst:IfcDerivedUnitElement_185
        rdf:type  ifc:IfcDerivedUnitElement .

inst:IfcDerivedUnit_85
        ifc:elements_IfcDerivedUnit  inst:IfcDerivedUnitElement_185 ;
        ifc:unitType_IfcDerivedUnit  ifc:WARPINGMOMENTUNIT .

inst:IfcLabel_242  rdf:type  ifc:IfcLabel ;
        express:hasString  "USD" .

inst:IfcMonetaryUnit_86
        ifc:currency_IfcMonetaryUnit  inst:IfcLabel_242 .

inst:IfcLengthMeasure_List_243
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_87
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_243 .

inst:IfcLengthMeasure_List_244
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_245
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_246
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "0."^^xsd:double .

inst:IfcLengthMeasure_List_243
        list:hasContents  inst:IfcLengthMeasure_246 ;
        list:hasNext      inst:IfcLengthMeasure_List_244 .

inst:IfcLengthMeasure_List_244
        list:hasContents  inst:IfcLengthMeasure_246 ;
        list:hasNext      inst:IfcLengthMeasure_List_245 .

inst:IfcLengthMeasure_List_245
        list:hasContents  inst:IfcLengthMeasure_246 .

inst:INTEGER_247  rdf:type  express:INTEGER ;
        express:hasInteger  0 .

inst:IfcDimensionalExponents_89
        ifc:lengthExponent_IfcDimensionalExponents  inst:INTEGER_247 ;
        ifc:massExponent_IfcDimensionalExponents  inst:INTEGER_247 ;
        ifc:timeExponent_IfcDimensionalExponents  inst:INTEGER_247 ;
        ifc:electricCurrentExponent_IfcDimensionalExponents  inst:INTEGER_247 ;
        ifc:thermodynamicTemperatureExponent_IfcDimensionalExponents  inst:INTEGER_247 ;
        ifc:amountOfSubstanceExponent_IfcDimensionalExponents  inst:INTEGER_247 ;
        ifc:luminousIntensityExponent_IfcDimensionalExponents  inst:INTEGER_247 .

inst:IfcPlaneAngleMeasure_248
        rdf:type           ifc:IfcPlaneAngleMeasure ;
        express:hasDouble  "0.0174532925199433"^^xsd:double .

inst:IfcMeasureWithUnit_90
        ifc:valueComponent_IfcMeasureWithUnit  inst:IfcPlaneAngleMeasure_248 .

inst:IfcSIUnit_186  rdf:type  ifc:IfcSIUnit .

inst:IfcMeasureWithUnit_90
        ifc:unitComponent_IfcMeasureWithUnit  inst:IfcSIUnit_186 .

inst:IfcDerivedUnitElement_91
        ifc:unit_IfcDerivedUnitElement  inst:IfcSIUnit_24 .

inst:INTEGER_249  rdf:type  express:INTEGER ;
        express:hasInteger  1 .

inst:IfcDerivedUnitElement_91
        ifc:exponent_IfcDerivedUnitElement  inst:INTEGER_249 .

inst:IfcDerivedUnitElement_92
        ifc:unit_IfcDerivedUnitElement  inst:IfcSIUnit_36 .

inst:INTEGER_250  rdf:type  express:INTEGER ;
        express:hasInteger  -1 .

inst:IfcDerivedUnitElement_92
        ifc:exponent_IfcDerivedUnitElement  inst:INTEGER_250 .

inst:IfcDerivedUnitElement_94
        ifc:unit_IfcDerivedUnitElement  inst:IfcConversionBasedUnit_30 ;
        ifc:exponent_IfcDerivedUnitElement  inst:INTEGER_249 .

inst:IfcDerivedUnitElement_98
        ifc:unit_IfcDerivedUnitElement  inst:IfcSIUnit_24 ;
        ifc:exponent_IfcDerivedUnitElement  inst:INTEGER_250 .

inst:IfcDerivedUnitElement_99
        ifc:unit_IfcDerivedUnitElement  inst:IfcSIUnit_32 ;
        ifc:exponent_IfcDerivedUnitElement  inst:INTEGER_249 .

inst:IfcDerivedUnitElement_100
        ifc:unit_IfcDerivedUnitElement  inst:IfcSIUnit_36 ;
        ifc:exponent_IfcDerivedUnitElement  inst:INTEGER_249 .

inst:IfcDerivedUnitElement_101
        ifc:unit_IfcDerivedUnitElement  inst:IfcSIUnit_31 ;
        ifc:exponent_IfcDerivedUnitElement  inst:INTEGER_249 .

inst:IfcDerivedUnitElement_102
        ifc:unit_IfcDerivedUnitElement  inst:IfcSIUnit_11 ;
        ifc:exponent_IfcDerivedUnitElement  inst:INTEGER_250 .

inst:IfcDerivedUnitElement_103
        ifc:unit_IfcDerivedUnitElement  inst:IfcSIUnit_19 ;
        ifc:exponent_IfcDerivedUnitElement  inst:INTEGER_249 .

inst:IfcDerivedUnitElement_104
        ifc:unit_IfcDerivedUnitElement  inst:IfcSIUnit_29 ;
        ifc:exponent_IfcDerivedUnitElement  inst:INTEGER_250 .

inst:IfcSIUnit_187  rdf:type  ifc:IfcSIUnit .

inst:IfcDerivedUnitElement_105
        ifc:unit_IfcDerivedUnitElement  inst:IfcSIUnit_187 ;
        ifc:exponent_IfcDerivedUnitElement  inst:INTEGER_249 .

inst:IfcDerivedUnitElement_106
        ifc:unit_IfcDerivedUnitElement  inst:IfcSIUnit_37 ;
        ifc:exponent_IfcDerivedUnitElement  inst:INTEGER_250 .

inst:IfcDerivedUnitElement_108
        ifc:unit_IfcDerivedUnitElement  inst:IfcSIUnit_37 ;
        ifc:exponent_IfcDerivedUnitElement  inst:INTEGER_249 .

inst:IfcDerivedUnitElement_109
        ifc:unit_IfcDerivedUnitElement  inst:IfcSIUnit_9 ;
        ifc:exponent_IfcDerivedUnitElement  inst:INTEGER_250 .

inst:IfcDerivedUnitElement_110
        ifc:unit_IfcDerivedUnitElement  inst:IfcSIUnit_11 ;
        ifc:exponent_IfcDerivedUnitElement  inst:INTEGER_249 .

inst:IfcDerivedUnitElement_112
        ifc:unit_IfcDerivedUnitElement  inst:IfcSIUnit_20 ;
        ifc:exponent_IfcDerivedUnitElement  inst:INTEGER_249 .

inst:IfcDerivedUnitElement_121
        ifc:unit_IfcDerivedUnitElement  inst:IfcSIUnit_26 ;
        ifc:exponent_IfcDerivedUnitElement  inst:INTEGER_249 .

inst:IfcDerivedUnitElement_122
        ifc:unit_IfcDerivedUnitElement  inst:IfcSIUnit_25 ;
        ifc:exponent_IfcDerivedUnitElement  inst:INTEGER_250 .

inst:IfcDerivedUnitElement_123
        ifc:unit_IfcDerivedUnitElement  inst:IfcSIUnit_29 ;
        ifc:exponent_IfcDerivedUnitElement  inst:INTEGER_249 .

inst:IfcDerivedUnitElement_135
        ifc:unit_IfcDerivedUnitElement  inst:IfcConversionBasedUnit_30 ;
        ifc:exponent_IfcDerivedUnitElement  inst:INTEGER_250 .

inst:IfcDerivedUnitElement_141
        ifc:unit_IfcDerivedUnitElement  inst:IfcSIUnit_10 ;
        ifc:exponent_IfcDerivedUnitElement  inst:INTEGER_250 .

inst:IfcDerivedUnitElement_142
        ifc:unit_IfcDerivedUnitElement  inst:IfcSIUnit_24 .

inst:INTEGER_251  rdf:type  express:INTEGER ;
        express:hasInteger  4 .

inst:IfcDerivedUnitElement_142
        ifc:exponent_IfcDerivedUnitElement  inst:INTEGER_251 .

inst:IfcDerivedUnitElement_151
        ifc:unit_IfcDerivedUnitElement  inst:IfcSIUnit_24 .

inst:INTEGER_252  rdf:type  express:INTEGER ;
        express:hasInteger  5 .

inst:IfcDerivedUnitElement_151
        ifc:exponent_IfcDerivedUnitElement  inst:INTEGER_252 .

inst:IfcDerivedUnitElement_152
        ifc:unit_IfcDerivedUnitElement  inst:IfcSIUnit_24 ;
        ifc:exponent_IfcDerivedUnitElement  inst:IfcDimensionCount_234 .

inst:IfcSIUnit_188  rdf:type  ifc:IfcSIUnit .

inst:IfcDerivedUnitElement_154
        ifc:unit_IfcDerivedUnitElement  inst:IfcSIUnit_188 ;
        ifc:exponent_IfcDerivedUnitElement  inst:INTEGER_247 .

inst:IfcSIUnit_189  rdf:type  ifc:IfcSIUnit .

inst:IfcDerivedUnitElement_155
        ifc:unit_IfcDerivedUnitElement  inst:IfcSIUnit_189 ;
        ifc:exponent_IfcDerivedUnitElement  inst:INTEGER_247 .

inst:IfcSIUnit_190  rdf:type  ifc:IfcSIUnit .

inst:IfcDerivedUnitElement_158
        ifc:unit_IfcDerivedUnitElement  inst:IfcSIUnit_190 ;
        ifc:exponent_IfcDerivedUnitElement  inst:INTEGER_250 .

inst:IfcDerivedUnitElement_159
        ifc:unit_IfcDerivedUnitElement  inst:IfcSIUnit_190 ;
        ifc:exponent_IfcDerivedUnitElement  inst:INTEGER_249 .

inst:IfcDerivedUnitElement_172
        ifc:unit_IfcDerivedUnitElement  inst:IfcSIUnit_31 ;
        ifc:exponent_IfcDerivedUnitElement  inst:INTEGER_250 .

inst:IfcDerivedUnitElement_183
        ifc:unit_IfcDerivedUnitElement  inst:IfcSIUnit_24 .

inst:INTEGER_253  rdf:type  express:INTEGER ;
        express:hasInteger  6 .

inst:IfcDerivedUnitElement_183
        ifc:exponent_IfcDerivedUnitElement  inst:INTEGER_253 .

inst:IfcDerivedUnitElement_185
        ifc:unit_IfcDerivedUnitElement  inst:IfcSIUnit_24 ;
        ifc:exponent_IfcDerivedUnitElement  inst:IfcDimensionCount_238 .

inst:IfcSIUnit_186  ifc:unitType_IfcNamedUnit  ifc:PLANEANGLEUNIT ;
        ifc:name_IfcSIUnit         ifc:RADIAN .

inst:IfcSIUnit_187  ifc:unitType_IfcNamedUnit  ifc:MASSUNIT ;
        ifc:name_IfcSIUnit         ifc:GRAM .

inst:IfcSIUnit_188  ifc:unitType_IfcNamedUnit  ifc:POWERUNIT ;
        ifc:prefix_IfcSIUnit       ifc:PICO ;
        ifc:name_IfcSIUnit         ifc:WATT .

inst:IfcSIUnit_189  ifc:unitType_IfcNamedUnit  ifc:PRESSUREUNIT ;
        ifc:prefix_IfcSIUnit       ifc:MICRO ;
        ifc:name_IfcSIUnit         ifc:PASCAL .

inst:IfcSIUnit_190  ifc:unitType_IfcNamedUnit  ifc:THERMODYNAMICTEMPERATUREUNIT ;
        ifc:name_IfcSIUnit         ifc:KELVIN .

inst:IfcGloballyUniqueId_254
        rdf:type           ifc:IfcGloballyUniqueId ;
        express:hasString  "12Jhas3DnFkfm06Be8GeKW" .

inst:IfcSite_191  ifc:globalId_IfcRoot  inst:IfcGloballyUniqueId_254 .

inst:IfcLabel_255  rdf:type  ifc:IfcLabel ;
        express:hasString  "Site #1" .

inst:IfcSite_191  ifc:name_IfcRoot  inst:IfcLabel_255 .

inst:IfcLocalPlacement_193
        rdf:type  ifc:IfcLocalPlacement .

inst:IfcSite_191  ifc:objectPlacement_IfcProduct  inst:IfcLocalPlacement_193 .

inst:IfcProductDefinitionShape_194
        rdf:type  ifc:IfcProductDefinitionShape .

inst:IfcSite_191  ifc:representation_IfcProduct  inst:IfcProductDefinitionShape_194 ;
        ifc:compositionType_IfcSpatialStructureElement  ifc:ELEMENT .

inst:IfcGloballyUniqueId_256
        rdf:type           ifc:IfcGloballyUniqueId ;
        express:hasString  "1AgtYeFKDEuRnJ8pGQA3pA" .

inst:IfcMemberType_192
        ifc:globalId_IfcRoot  inst:IfcGloballyUniqueId_256 .

inst:IfcRepresentationMap_List_257
        rdf:type  ifc:IfcRepresentationMap_List .

inst:IfcMemberType_192
        ifc:representationMaps_IfcTypeProduct  inst:IfcRepresentationMap_List_257 .

inst:IfcRepresentationMap_205
        rdf:type  ifc:IfcRepresentationMap .

inst:IfcRepresentationMap_List_257
        list:hasContents  inst:IfcRepresentationMap_205 .

inst:IfcMemberType_192
        ifc:predefinedType_IfcMemberType  ifc:POST .

inst:IfcAxis2Placement3D_196
        rdf:type  ifc:IfcAxis2Placement3D .

inst:IfcLocalPlacement_193
        ifc:relativePlacement_IfcLocalPlacement  inst:IfcAxis2Placement3D_196 .

inst:IfcRepresentation_List_258
        rdf:type  ifc:IfcRepresentation_List .

inst:IfcProductDefinitionShape_194
        ifc:representations_IfcProductRepresentation  inst:IfcRepresentation_List_258 .

inst:IfcShapeRepresentation_197
        rdf:type  ifc:IfcShapeRepresentation .

inst:IfcRepresentation_List_258
        list:hasContents  inst:IfcShapeRepresentation_197 .

inst:IfcRelContainedInSpatialStructure_195
        rdf:type  ifc:IfcRelContainedInSpatialStructure .

inst:IfcGloballyUniqueId_259
        rdf:type           ifc:IfcGloballyUniqueId ;
        express:hasString  "2H$O_OUzH91QU2yMIvZZ$h" .

inst:IfcRelContainedInSpatialStructure_195
        ifc:globalId_IfcRoot  inst:IfcGloballyUniqueId_259 .

inst:IfcMember_220  rdf:type  ifc:IfcMember .

inst:IfcRelContainedInSpatialStructure_195
        ifc:relatedElements_IfcRelContainedInSpatialStructure  inst:IfcMember_220 ;
        ifc:relatingStructure_IfcRelContainedInSpatialStructure  inst:IfcSite_191 .

inst:IfcAxis2Placement3D_196
        ifc:location_IfcPlacement  inst:IfcCartesianPoint_87 .

inst:IfcShapeRepresentation_197
        ifc:contextOfItems_IfcRepresentation  inst:IfcGeometricRepresentationContext_2 .

inst:IfcLabel_260  rdf:type  ifc:IfcLabel ;
        express:hasString  "FootPrint" .

inst:IfcShapeRepresentation_197
        ifc:representationIdentifier_IfcRepresentation  inst:IfcLabel_260 .

inst:IfcLabel_261  rdf:type  ifc:IfcLabel ;
        express:hasString  "GeometricCurveSet" .

inst:IfcShapeRepresentation_197
        ifc:representationType_IfcRepresentation  inst:IfcLabel_261 .

inst:IfcGeometricCurveSet_199
        rdf:type  ifc:IfcGeometricCurveSet .

inst:IfcShapeRepresentation_197
        ifc:items_IfcRepresentation  inst:IfcGeometricCurveSet_199 .

inst:IfcPolyline_200  rdf:type  ifc:IfcPolyline .

inst:IfcGeometricCurveSet_199
        ifc:elements_IfcGeometricSet  inst:IfcPolyline_200 .

inst:IfcCartesianPoint_List_262
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcPolyline_200  ifc:points_IfcPolyline  inst:IfcCartesianPoint_List_262 .

inst:IfcCartesianPoint_List_263
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_264
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_265
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_List_266
        rdf:type  ifc:IfcCartesianPoint_List .

inst:IfcCartesianPoint_201
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcCartesianPoint_List_262
        list:hasContents  inst:IfcCartesianPoint_201 ;
        list:hasNext      inst:IfcCartesianPoint_List_263 .

inst:IfcCartesianPoint_202
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcCartesianPoint_List_263
        list:hasContents  inst:IfcCartesianPoint_202 ;
        list:hasNext      inst:IfcCartesianPoint_List_264 .

inst:IfcCartesianPoint_203
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcCartesianPoint_List_264
        list:hasContents  inst:IfcCartesianPoint_203 ;
        list:hasNext      inst:IfcCartesianPoint_List_265 .

inst:IfcCartesianPoint_204
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcCartesianPoint_List_265
        list:hasContents  inst:IfcCartesianPoint_204 ;
        list:hasNext      inst:IfcCartesianPoint_List_266 .

inst:IfcCartesianPoint_List_266
        list:hasContents  inst:IfcCartesianPoint_201 .

inst:IfcLengthMeasure_List_267
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_201
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_267 .

inst:IfcLengthMeasure_List_268
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_267
        list:hasContents  inst:IfcLengthMeasure_246 ;
        list:hasNext      inst:IfcLengthMeasure_List_268 .

inst:IfcLengthMeasure_List_268
        list:hasContents  inst:IfcLengthMeasure_246 .

inst:IfcLengthMeasure_List_269
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_202
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_269 .

inst:IfcLengthMeasure_List_270
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_271
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "40."^^xsd:double .

inst:IfcLengthMeasure_List_269
        list:hasContents  inst:IfcLengthMeasure_271 ;
        list:hasNext      inst:IfcLengthMeasure_List_270 .

inst:IfcLengthMeasure_List_270
        list:hasContents  inst:IfcLengthMeasure_246 .

inst:IfcLengthMeasure_List_272
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_203
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_272 .

inst:IfcLengthMeasure_List_273
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_272
        list:hasContents  inst:IfcLengthMeasure_271 ;
        list:hasNext      inst:IfcLengthMeasure_List_273 .

inst:IfcLengthMeasure_274
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "20."^^xsd:double .

inst:IfcLengthMeasure_List_273
        list:hasContents  inst:IfcLengthMeasure_274 .

inst:IfcLengthMeasure_List_275
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_204
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_275 .

inst:IfcLengthMeasure_List_276
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_275
        list:hasContents  inst:IfcLengthMeasure_246 ;
        list:hasNext      inst:IfcLengthMeasure_List_276 .

inst:IfcLengthMeasure_List_276
        list:hasContents  inst:IfcLengthMeasure_274 .

inst:IfcAxis2Placement3D_207
        rdf:type  ifc:IfcAxis2Placement3D .

inst:IfcRepresentationMap_205
        ifc:mappingOrigin_IfcRepresentationMap  inst:IfcAxis2Placement3D_207 .

inst:IfcShapeRepresentation_208
        rdf:type  ifc:IfcShapeRepresentation .

inst:IfcRepresentationMap_205
        ifc:mappedRepresentation_IfcRepresentationMap  inst:IfcShapeRepresentation_208 .

inst:IfcRelDefinesByType_206
        rdf:type  ifc:IfcRelDefinesByType .

inst:IfcGloballyUniqueId_277
        rdf:type           ifc:IfcGloballyUniqueId ;
        express:hasString  "208LEfhXv418VHoTjAT$dC" .

inst:IfcRelDefinesByType_206
        ifc:globalId_IfcRoot  inst:IfcGloballyUniqueId_277 ;
        ifc:relatedObjects_IfcRelDefinesByType  inst:IfcMember_220 ;
        ifc:relatingType_IfcRelDefinesByType  inst:IfcMemberType_192 .

inst:IfcAxis2Placement3D_207
        ifc:location_IfcPlacement  inst:IfcCartesianPoint_87 .

inst:IfcShapeRepresentation_208
        ifc:contextOfItems_IfcRepresentation  inst:IfcGeometricRepresentationContext_2 .

inst:IfcLabel_278  rdf:type  ifc:IfcLabel ;
        express:hasString  "Body" .

inst:IfcShapeRepresentation_208
        ifc:representationIdentifier_IfcRepresentation  inst:IfcLabel_278 .

inst:IfcLabel_279  rdf:type  ifc:IfcLabel ;
        express:hasString  "Tessellation" .

inst:IfcShapeRepresentation_208
        ifc:representationType_IfcRepresentation  inst:IfcLabel_279 .

inst:IfcTriangulatedFaceSet_210
        rdf:type  ifc:IfcTriangulatedFaceSet .

inst:IfcShapeRepresentation_208
        ifc:items_IfcRepresentation  inst:IfcTriangulatedFaceSet_210 .

inst:IfcCartesianPointList3D_211
        rdf:type  ifc:IfcCartesianPointList3D .

inst:IfcTriangulatedFaceSet_210
        ifc:coordinates_IfcTessellatedFaceSet  inst:IfcCartesianPointList3D_211 .

inst:IfcParameterValue_List_280
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_281
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_282
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_280
        list:hasContents  inst:IfcLengthMeasure_246 ;
        list:hasNext      inst:IfcParameterValue_List_281 .

inst:IfcParameterValue_List_281
        list:hasContents  inst:IfcLengthMeasure_246 ;
        list:hasNext      inst:IfcParameterValue_List_282 .

inst:IfcParameterValue_283
        rdf:type           ifc:IfcParameterValue ;
        express:hasDouble  "-1."^^xsd:double .

inst:IfcParameterValue_List_282
        list:hasContents  inst:IfcParameterValue_283 .

inst:IfcParameterValue_List_284
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_285
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_286
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_284
        list:hasContents  inst:IfcLengthMeasure_246 ;
        list:hasNext      inst:IfcParameterValue_List_285 .

inst:IfcParameterValue_List_285
        list:hasContents  inst:IfcLengthMeasure_246 ;
        list:hasNext      inst:IfcParameterValue_List_286 .

inst:IfcParameterValue_287
        rdf:type           ifc:IfcParameterValue ;
        express:hasDouble  "1."^^xsd:double .

inst:IfcParameterValue_List_286
        list:hasContents  inst:IfcParameterValue_287 .

inst:IfcParameterValue_List_288
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_289
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_290
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_288
        list:hasContents  inst:IfcLengthMeasure_246 ;
        list:hasNext      inst:IfcParameterValue_List_289 .

inst:IfcParameterValue_List_289
        list:hasContents  inst:IfcLengthMeasure_246 ;
        list:hasNext      inst:IfcParameterValue_List_290 .

inst:IfcParameterValue_List_290
        list:hasContents  inst:IfcParameterValue_283 .

inst:IfcParameterValue_List_291
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_292
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_293
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_291
        list:hasContents  inst:IfcLengthMeasure_246 ;
        list:hasNext      inst:IfcParameterValue_List_292 .

inst:IfcParameterValue_List_292
        list:hasContents  inst:IfcLengthMeasure_246 ;
        list:hasNext      inst:IfcParameterValue_List_293 .

inst:IfcParameterValue_List_293
        list:hasContents  inst:IfcParameterValue_283 .

inst:IfcParameterValue_List_294
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_295
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_296
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_294
        list:hasContents  inst:IfcLengthMeasure_246 ;
        list:hasNext      inst:IfcParameterValue_List_295 .

inst:IfcParameterValue_List_295
        list:hasContents  inst:IfcLengthMeasure_246 ;
        list:hasNext      inst:IfcParameterValue_List_296 .

inst:IfcParameterValue_List_296
        list:hasContents  inst:IfcParameterValue_283 .

inst:IfcParameterValue_List_297
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_298
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_299
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_297
        list:hasContents  inst:IfcLengthMeasure_246 ;
        list:hasNext      inst:IfcParameterValue_List_298 .

inst:IfcParameterValue_List_298
        list:hasContents  inst:IfcLengthMeasure_246 ;
        list:hasNext      inst:IfcParameterValue_List_299 .

inst:IfcParameterValue_List_299
        list:hasContents  inst:IfcParameterValue_283 .

inst:IfcParameterValue_List_300
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_301
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_302
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_300
        list:hasContents  inst:IfcLengthMeasure_246 ;
        list:hasNext      inst:IfcParameterValue_List_301 .

inst:IfcParameterValue_List_301
        list:hasContents  inst:IfcLengthMeasure_246 ;
        list:hasNext      inst:IfcParameterValue_List_302 .

inst:IfcParameterValue_List_302
        list:hasContents  inst:IfcParameterValue_283 .

inst:IfcParameterValue_List_303
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_304
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_305
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_303
        list:hasContents  inst:IfcLengthMeasure_246 ;
        list:hasNext      inst:IfcParameterValue_List_304 .

inst:IfcParameterValue_List_304
        list:hasContents  inst:IfcLengthMeasure_246 ;
        list:hasNext      inst:IfcParameterValue_List_305 .

inst:IfcParameterValue_List_305
        list:hasContents  inst:IfcParameterValue_283 .

inst:IfcParameterValue_List_306
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_307
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_308
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_306
        list:hasContents  inst:IfcLengthMeasure_246 ;
        list:hasNext      inst:IfcParameterValue_List_307 .

inst:IfcParameterValue_List_307
        list:hasContents  inst:IfcLengthMeasure_246 ;
        list:hasNext      inst:IfcParameterValue_List_308 .

inst:IfcParameterValue_List_308
        list:hasContents  inst:IfcParameterValue_283 .

inst:IfcParameterValue_List_309
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_310
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_311
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_309
        list:hasContents  inst:IfcLengthMeasure_246 ;
        list:hasNext      inst:IfcParameterValue_List_310 .

inst:IfcParameterValue_List_310
        list:hasContents  inst:IfcLengthMeasure_246 ;
        list:hasNext      inst:IfcParameterValue_List_311 .

inst:IfcParameterValue_List_311
        list:hasContents  inst:IfcParameterValue_283 .

inst:IfcParameterValue_List_312
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_313
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_314
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_312
        list:hasContents  inst:IfcLengthMeasure_246 ;
        list:hasNext      inst:IfcParameterValue_List_313 .

inst:IfcParameterValue_List_313
        list:hasContents  inst:IfcLengthMeasure_246 ;
        list:hasNext      inst:IfcParameterValue_List_314 .

inst:IfcParameterValue_List_314
        list:hasContents  inst:IfcParameterValue_283 .

inst:IfcParameterValue_List_315
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_316
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_317
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_315
        list:hasContents  inst:IfcLengthMeasure_246 ;
        list:hasNext      inst:IfcParameterValue_List_316 .

inst:IfcParameterValue_List_316
        list:hasContents  inst:IfcLengthMeasure_246 ;
        list:hasNext      inst:IfcParameterValue_List_317 .

inst:IfcParameterValue_List_317
        list:hasContents  inst:IfcParameterValue_283 .

inst:IfcParameterValue_List_318
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_319
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_320
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_318
        list:hasContents  inst:IfcLengthMeasure_246 ;
        list:hasNext      inst:IfcParameterValue_List_319 .

inst:IfcParameterValue_List_319
        list:hasContents  inst:IfcLengthMeasure_246 ;
        list:hasNext      inst:IfcParameterValue_List_320 .

inst:IfcParameterValue_List_320
        list:hasContents  inst:IfcParameterValue_283 .

inst:IfcParameterValue_List_321
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_322
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_323
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_321
        list:hasContents  inst:IfcLengthMeasure_246 ;
        list:hasNext      inst:IfcParameterValue_List_322 .

inst:IfcParameterValue_List_322
        list:hasContents  inst:IfcLengthMeasure_246 ;
        list:hasNext      inst:IfcParameterValue_List_323 .

inst:IfcParameterValue_List_323
        list:hasContents  inst:IfcParameterValue_283 .

inst:IfcParameterValue_List_324
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_325
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_326
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_324
        list:hasContents  inst:IfcLengthMeasure_246 ;
        list:hasNext      inst:IfcParameterValue_List_325 .

inst:IfcParameterValue_List_325
        list:hasContents  inst:IfcLengthMeasure_246 ;
        list:hasNext      inst:IfcParameterValue_List_326 .

inst:IfcParameterValue_List_326
        list:hasContents  inst:IfcParameterValue_283 .

inst:IfcParameterValue_List_327
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_328
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_329
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_327
        list:hasContents  inst:IfcLengthMeasure_246 ;
        list:hasNext      inst:IfcParameterValue_List_328 .

inst:IfcParameterValue_List_328
        list:hasContents  inst:IfcLengthMeasure_246 ;
        list:hasNext      inst:IfcParameterValue_List_329 .

inst:IfcParameterValue_List_329
        list:hasContents  inst:IfcParameterValue_283 .

inst:IfcParameterValue_List_330
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_331
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_332
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_330
        list:hasContents  inst:IfcLengthMeasure_246 ;
        list:hasNext      inst:IfcParameterValue_List_331 .

inst:IfcParameterValue_List_331
        list:hasContents  inst:IfcLengthMeasure_246 ;
        list:hasNext      inst:IfcParameterValue_List_332 .

inst:IfcParameterValue_List_332
        list:hasContents  inst:IfcParameterValue_283 .

inst:IfcParameterValue_List_333
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_334
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_335
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_333
        list:hasContents  inst:IfcLengthMeasure_246 ;
        list:hasNext      inst:IfcParameterValue_List_334 .

inst:IfcParameterValue_List_334
        list:hasContents  inst:IfcLengthMeasure_246 ;
        list:hasNext      inst:IfcParameterValue_List_335 .

inst:IfcParameterValue_List_335
        list:hasContents  inst:IfcParameterValue_283 .

inst:IfcParameterValue_List_336
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_337
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_338
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_336
        list:hasContents  inst:IfcLengthMeasure_246 ;
        list:hasNext      inst:IfcParameterValue_List_337 .

inst:IfcParameterValue_List_337
        list:hasContents  inst:IfcLengthMeasure_246 ;
        list:hasNext      inst:IfcParameterValue_List_338 .

inst:IfcParameterValue_List_338
        list:hasContents  inst:IfcParameterValue_283 .

inst:IfcParameterValue_List_339
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_340
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_341
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_339
        list:hasContents  inst:IfcLengthMeasure_246 ;
        list:hasNext      inst:IfcParameterValue_List_340 .

inst:IfcParameterValue_List_340
        list:hasContents  inst:IfcLengthMeasure_246 ;
        list:hasNext      inst:IfcParameterValue_List_341 .

inst:IfcParameterValue_List_341
        list:hasContents  inst:IfcParameterValue_287 .

inst:IfcParameterValue_List_342
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_343
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_344
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_342
        list:hasContents  inst:IfcLengthMeasure_246 ;
        list:hasNext      inst:IfcParameterValue_List_343 .

inst:IfcParameterValue_List_343
        list:hasContents  inst:IfcLengthMeasure_246 ;
        list:hasNext      inst:IfcParameterValue_List_344 .

inst:IfcParameterValue_List_344
        list:hasContents  inst:IfcParameterValue_287 .

inst:IfcParameterValue_List_345
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_346
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_347
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_345
        list:hasContents  inst:IfcLengthMeasure_246 ;
        list:hasNext      inst:IfcParameterValue_List_346 .

inst:IfcParameterValue_List_346
        list:hasContents  inst:IfcLengthMeasure_246 ;
        list:hasNext      inst:IfcParameterValue_List_347 .

inst:IfcParameterValue_List_347
        list:hasContents  inst:IfcParameterValue_287 .

inst:IfcParameterValue_List_348
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_349
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_350
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_348
        list:hasContents  inst:IfcLengthMeasure_246 ;
        list:hasNext      inst:IfcParameterValue_List_349 .

inst:IfcParameterValue_List_349
        list:hasContents  inst:IfcLengthMeasure_246 ;
        list:hasNext      inst:IfcParameterValue_List_350 .

inst:IfcParameterValue_List_350
        list:hasContents  inst:IfcParameterValue_287 .

inst:IfcParameterValue_List_351
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_352
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_353
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_351
        list:hasContents  inst:IfcLengthMeasure_246 ;
        list:hasNext      inst:IfcParameterValue_List_352 .

inst:IfcParameterValue_List_352
        list:hasContents  inst:IfcLengthMeasure_246 ;
        list:hasNext      inst:IfcParameterValue_List_353 .

inst:IfcParameterValue_List_353
        list:hasContents  inst:IfcParameterValue_287 .

inst:IfcParameterValue_List_354
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_355
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_356
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_354
        list:hasContents  inst:IfcLengthMeasure_246 ;
        list:hasNext      inst:IfcParameterValue_List_355 .

inst:IfcParameterValue_List_355
        list:hasContents  inst:IfcLengthMeasure_246 ;
        list:hasNext      inst:IfcParameterValue_List_356 .

inst:IfcParameterValue_List_356
        list:hasContents  inst:IfcParameterValue_287 .

inst:IfcParameterValue_List_357
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_358
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_359
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_357
        list:hasContents  inst:IfcLengthMeasure_246 ;
        list:hasNext      inst:IfcParameterValue_List_358 .

inst:IfcParameterValue_List_358
        list:hasContents  inst:IfcLengthMeasure_246 ;
        list:hasNext      inst:IfcParameterValue_List_359 .

inst:IfcParameterValue_List_359
        list:hasContents  inst:IfcParameterValue_287 .

inst:IfcParameterValue_List_360
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_361
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_362
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_360
        list:hasContents  inst:IfcLengthMeasure_246 ;
        list:hasNext      inst:IfcParameterValue_List_361 .

inst:IfcParameterValue_List_361
        list:hasContents  inst:IfcLengthMeasure_246 ;
        list:hasNext      inst:IfcParameterValue_List_362 .

inst:IfcParameterValue_List_362
        list:hasContents  inst:IfcParameterValue_287 .

inst:IfcParameterValue_List_363
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_364
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_365
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_363
        list:hasContents  inst:IfcLengthMeasure_246 ;
        list:hasNext      inst:IfcParameterValue_List_364 .

inst:IfcParameterValue_List_364
        list:hasContents  inst:IfcLengthMeasure_246 ;
        list:hasNext      inst:IfcParameterValue_List_365 .

inst:IfcParameterValue_List_365
        list:hasContents  inst:IfcParameterValue_287 .

inst:IfcParameterValue_List_366
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_367
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_368
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_366
        list:hasContents  inst:IfcLengthMeasure_246 ;
        list:hasNext      inst:IfcParameterValue_List_367 .

inst:IfcParameterValue_List_367
        list:hasContents  inst:IfcLengthMeasure_246 ;
        list:hasNext      inst:IfcParameterValue_List_368 .

inst:IfcParameterValue_List_368
        list:hasContents  inst:IfcParameterValue_287 .

inst:IfcParameterValue_List_369
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_370
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_371
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_369
        list:hasContents  inst:IfcLengthMeasure_246 ;
        list:hasNext      inst:IfcParameterValue_List_370 .

inst:IfcParameterValue_List_370
        list:hasContents  inst:IfcLengthMeasure_246 ;
        list:hasNext      inst:IfcParameterValue_List_371 .

inst:IfcParameterValue_List_371
        list:hasContents  inst:IfcParameterValue_287 .

inst:IfcParameterValue_List_372
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_373
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_374
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_372
        list:hasContents  inst:IfcLengthMeasure_246 ;
        list:hasNext      inst:IfcParameterValue_List_373 .

inst:IfcParameterValue_List_373
        list:hasContents  inst:IfcLengthMeasure_246 ;
        list:hasNext      inst:IfcParameterValue_List_374 .

inst:IfcParameterValue_List_374
        list:hasContents  inst:IfcParameterValue_287 .

inst:IfcParameterValue_List_375
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_376
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_377
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_375
        list:hasContents  inst:IfcLengthMeasure_246 ;
        list:hasNext      inst:IfcParameterValue_List_376 .

inst:IfcParameterValue_List_376
        list:hasContents  inst:IfcLengthMeasure_246 ;
        list:hasNext      inst:IfcParameterValue_List_377 .

inst:IfcParameterValue_List_377
        list:hasContents  inst:IfcParameterValue_287 .

inst:IfcParameterValue_List_378
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_379
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_380
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_378
        list:hasContents  inst:IfcLengthMeasure_246 ;
        list:hasNext      inst:IfcParameterValue_List_379 .

inst:IfcParameterValue_List_379
        list:hasContents  inst:IfcLengthMeasure_246 ;
        list:hasNext      inst:IfcParameterValue_List_380 .

inst:IfcParameterValue_List_380
        list:hasContents  inst:IfcParameterValue_287 .

inst:IfcParameterValue_List_381
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_382
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_383
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_381
        list:hasContents  inst:IfcLengthMeasure_246 ;
        list:hasNext      inst:IfcParameterValue_List_382 .

inst:IfcParameterValue_List_382
        list:hasContents  inst:IfcLengthMeasure_246 ;
        list:hasNext      inst:IfcParameterValue_List_383 .

inst:IfcParameterValue_List_383
        list:hasContents  inst:IfcParameterValue_287 .

inst:IfcParameterValue_List_384
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_385
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_386
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_384
        list:hasContents  inst:IfcLengthMeasure_246 ;
        list:hasNext      inst:IfcParameterValue_List_385 .

inst:IfcParameterValue_List_385
        list:hasContents  inst:IfcLengthMeasure_246 ;
        list:hasNext      inst:IfcParameterValue_List_386 .

inst:IfcParameterValue_List_386
        list:hasContents  inst:IfcParameterValue_287 .

inst:IfcParameterValue_List_387
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_388
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_389
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_387
        list:hasContents  inst:IfcLengthMeasure_246 ;
        list:hasNext      inst:IfcParameterValue_List_388 .

inst:IfcParameterValue_List_388
        list:hasContents  inst:IfcLengthMeasure_246 ;
        list:hasNext      inst:IfcParameterValue_List_389 .

inst:IfcParameterValue_List_389
        list:hasContents  inst:IfcParameterValue_287 .

inst:IfcParameterValue_List_390
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_391
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_392
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_390
        list:hasContents  inst:IfcParameterValue_287 ;
        list:hasNext      inst:IfcParameterValue_List_391 .

inst:IfcParameterValue_List_391
        list:hasContents  inst:IfcLengthMeasure_246 ;
        list:hasNext      inst:IfcParameterValue_List_392 .

inst:IfcParameterValue_List_392
        list:hasContents  inst:IfcLengthMeasure_246 .

inst:IfcParameterValue_List_393
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_394
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_395
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_396
        rdf:type           ifc:IfcParameterValue ;
        express:hasDouble  "0.923879532511287"^^xsd:double .

inst:IfcParameterValue_List_393
        list:hasContents  inst:IfcParameterValue_396 ;
        list:hasNext      inst:IfcParameterValue_List_394 .

inst:IfcParameterValue_397
        rdf:type           ifc:IfcParameterValue ;
        express:hasDouble  "0.38268343236509"^^xsd:double .

inst:IfcParameterValue_List_394
        list:hasContents  inst:IfcParameterValue_397 ;
        list:hasNext      inst:IfcParameterValue_List_395 .

inst:IfcParameterValue_List_395
        list:hasContents  inst:IfcLengthMeasure_246 .

inst:IfcParameterValue_List_398
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_399
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_400
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_401
        rdf:type           ifc:IfcParameterValue ;
        express:hasDouble  "0.707106781186548"^^xsd:double .

inst:IfcParameterValue_List_398
        list:hasContents  inst:IfcParameterValue_401 ;
        list:hasNext      inst:IfcParameterValue_List_399 .

inst:IfcParameterValue_402
        rdf:type           ifc:IfcParameterValue ;
        express:hasDouble  "0.707106781186547"^^xsd:double .

inst:IfcParameterValue_List_399
        list:hasContents  inst:IfcParameterValue_402 ;
        list:hasNext      inst:IfcParameterValue_List_400 .

inst:IfcParameterValue_List_400
        list:hasContents  inst:IfcLengthMeasure_246 .

inst:IfcParameterValue_List_403
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_404
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_405
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_403
        list:hasContents  inst:IfcParameterValue_397 ;
        list:hasNext      inst:IfcParameterValue_List_404 .

inst:IfcParameterValue_List_404
        list:hasContents  inst:IfcParameterValue_396 ;
        list:hasNext      inst:IfcParameterValue_List_405 .

inst:IfcParameterValue_List_405
        list:hasContents  inst:IfcLengthMeasure_246 .

inst:IfcParameterValue_List_406
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_407
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_408
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_409
        rdf:type           ifc:IfcParameterValue ;
        express:hasDouble  6.12303176911189E-17 .

inst:IfcParameterValue_List_406
        list:hasContents  inst:IfcParameterValue_409 ;
        list:hasNext      inst:IfcParameterValue_List_407 .

inst:IfcParameterValue_List_407
        list:hasContents  inst:IfcParameterValue_287 ;
        list:hasNext      inst:IfcParameterValue_List_408 .

inst:IfcParameterValue_List_408
        list:hasContents  inst:IfcLengthMeasure_246 .

inst:IfcParameterValue_List_410
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_411
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_412
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_413
        rdf:type           ifc:IfcParameterValue ;
        express:hasDouble  "-0.38268343236509"^^xsd:double .

inst:IfcParameterValue_List_410
        list:hasContents  inst:IfcParameterValue_413 ;
        list:hasNext      inst:IfcParameterValue_List_411 .

inst:IfcParameterValue_List_411
        list:hasContents  inst:IfcParameterValue_396 ;
        list:hasNext      inst:IfcParameterValue_List_412 .

inst:IfcParameterValue_List_412
        list:hasContents  inst:IfcLengthMeasure_246 .

inst:IfcParameterValue_List_414
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_415
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_416
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_417
        rdf:type           ifc:IfcParameterValue ;
        express:hasDouble  "-0.707106781186547"^^xsd:double .

inst:IfcParameterValue_List_414
        list:hasContents  inst:IfcParameterValue_417 ;
        list:hasNext      inst:IfcParameterValue_List_415 .

inst:IfcParameterValue_List_415
        list:hasContents  inst:IfcParameterValue_401 ;
        list:hasNext      inst:IfcParameterValue_List_416 .

inst:IfcParameterValue_List_416
        list:hasContents  inst:IfcLengthMeasure_246 .

inst:IfcParameterValue_List_418
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_419
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_420
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_421
        rdf:type           ifc:IfcParameterValue ;
        express:hasDouble  "-0.923879532511287"^^xsd:double .

inst:IfcParameterValue_List_418
        list:hasContents  inst:IfcParameterValue_421 ;
        list:hasNext      inst:IfcParameterValue_List_419 .

inst:IfcParameterValue_List_419
        list:hasContents  inst:IfcParameterValue_397 ;
        list:hasNext      inst:IfcParameterValue_List_420 .

inst:IfcParameterValue_List_420
        list:hasContents  inst:IfcLengthMeasure_246 .

inst:IfcParameterValue_List_422
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_423
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_424
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_422
        list:hasContents  inst:IfcParameterValue_283 ;
        list:hasNext      inst:IfcParameterValue_List_423 .

inst:IfcParameterValue_425
        rdf:type           ifc:IfcParameterValue ;
        express:hasDouble  1.22460635382238E-16 .

inst:IfcParameterValue_List_423
        list:hasContents  inst:IfcParameterValue_425 ;
        list:hasNext      inst:IfcParameterValue_List_424 .

inst:IfcParameterValue_List_424
        list:hasContents  inst:IfcLengthMeasure_246 .

inst:IfcParameterValue_List_426
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_427
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_428
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_426
        list:hasContents  inst:IfcParameterValue_421 ;
        list:hasNext      inst:IfcParameterValue_List_427 .

inst:IfcParameterValue_List_427
        list:hasContents  inst:IfcParameterValue_413 ;
        list:hasNext      inst:IfcParameterValue_List_428 .

inst:IfcParameterValue_List_428
        list:hasContents  inst:IfcLengthMeasure_246 .

inst:IfcParameterValue_List_429
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_430
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_431
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_432
        rdf:type           ifc:IfcParameterValue ;
        express:hasDouble  "-0.707106781186548"^^xsd:double .

inst:IfcParameterValue_List_429
        list:hasContents  inst:IfcParameterValue_432 ;
        list:hasNext      inst:IfcParameterValue_List_430 .

inst:IfcParameterValue_List_430
        list:hasContents  inst:IfcParameterValue_417 ;
        list:hasNext      inst:IfcParameterValue_List_431 .

inst:IfcParameterValue_List_431
        list:hasContents  inst:IfcLengthMeasure_246 .

inst:IfcParameterValue_List_433
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_434
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_435
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_433
        list:hasContents  inst:IfcParameterValue_413 ;
        list:hasNext      inst:IfcParameterValue_List_434 .

inst:IfcParameterValue_List_434
        list:hasContents  inst:IfcParameterValue_421 ;
        list:hasNext      inst:IfcParameterValue_List_435 .

inst:IfcParameterValue_List_435
        list:hasContents  inst:IfcLengthMeasure_246 .

inst:IfcParameterValue_List_436
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_437
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_438
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_439
        rdf:type           ifc:IfcParameterValue ;
        express:hasDouble  -1.83690953073357E-16 .

inst:IfcParameterValue_List_436
        list:hasContents  inst:IfcParameterValue_439 ;
        list:hasNext      inst:IfcParameterValue_List_437 .

inst:IfcParameterValue_List_437
        list:hasContents  inst:IfcParameterValue_283 ;
        list:hasNext      inst:IfcParameterValue_List_438 .

inst:IfcParameterValue_List_438
        list:hasContents  inst:IfcLengthMeasure_246 .

inst:IfcParameterValue_List_440
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_441
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_442
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_440
        list:hasContents  inst:IfcParameterValue_397 ;
        list:hasNext      inst:IfcParameterValue_List_441 .

inst:IfcParameterValue_List_441
        list:hasContents  inst:IfcParameterValue_421 ;
        list:hasNext      inst:IfcParameterValue_List_442 .

inst:IfcParameterValue_List_442
        list:hasContents  inst:IfcLengthMeasure_246 .

inst:IfcParameterValue_List_443
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_444
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_445
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_443
        list:hasContents  inst:IfcParameterValue_402 ;
        list:hasNext      inst:IfcParameterValue_List_444 .

inst:IfcParameterValue_List_444
        list:hasContents  inst:IfcParameterValue_432 ;
        list:hasNext      inst:IfcParameterValue_List_445 .

inst:IfcParameterValue_List_445
        list:hasContents  inst:IfcLengthMeasure_246 .

inst:IfcParameterValue_List_446
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_447
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_448
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_446
        list:hasContents  inst:IfcParameterValue_396 ;
        list:hasNext      inst:IfcParameterValue_List_447 .

inst:IfcParameterValue_List_447
        list:hasContents  inst:IfcParameterValue_413 ;
        list:hasNext      inst:IfcParameterValue_List_448 .

inst:IfcParameterValue_List_448
        list:hasContents  inst:IfcLengthMeasure_246 .

inst:IfcParameterValue_List_449
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_450
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_451
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_449
        list:hasContents  inst:IfcParameterValue_287 ;
        list:hasNext      inst:IfcParameterValue_List_450 .

inst:IfcParameterValue_452
        rdf:type           ifc:IfcParameterValue ;
        express:hasDouble  -2.44921270764475E-16 .

inst:IfcParameterValue_List_450
        list:hasContents  inst:IfcParameterValue_452 ;
        list:hasNext      inst:IfcParameterValue_List_451 .

inst:IfcParameterValue_List_451
        list:hasContents  inst:IfcLengthMeasure_246 .

inst:IfcParameterValue_List_453
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_454
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_455
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_453
        list:hasContents  inst:IfcParameterValue_287 ;
        list:hasNext      inst:IfcParameterValue_List_454 .

inst:IfcParameterValue_List_454
        list:hasContents  inst:IfcLengthMeasure_246 ;
        list:hasNext      inst:IfcParameterValue_List_455 .

inst:IfcParameterValue_List_455
        list:hasContents  inst:IfcLengthMeasure_246 .

inst:IfcParameterValue_List_456
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_457
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_458
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_456
        list:hasContents  inst:IfcParameterValue_396 ;
        list:hasNext      inst:IfcParameterValue_List_457 .

inst:IfcParameterValue_List_457
        list:hasContents  inst:IfcParameterValue_397 ;
        list:hasNext      inst:IfcParameterValue_List_458 .

inst:IfcParameterValue_List_458
        list:hasContents  inst:IfcLengthMeasure_246 .

inst:IfcParameterValue_List_459
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_460
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_461
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_459
        list:hasContents  inst:IfcParameterValue_401 ;
        list:hasNext      inst:IfcParameterValue_List_460 .

inst:IfcParameterValue_List_460
        list:hasContents  inst:IfcParameterValue_402 ;
        list:hasNext      inst:IfcParameterValue_List_461 .

inst:IfcParameterValue_List_461
        list:hasContents  inst:IfcLengthMeasure_246 .

inst:IfcParameterValue_List_462
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_463
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_464
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_462
        list:hasContents  inst:IfcParameterValue_397 ;
        list:hasNext      inst:IfcParameterValue_List_463 .

inst:IfcParameterValue_List_463
        list:hasContents  inst:IfcParameterValue_396 ;
        list:hasNext      inst:IfcParameterValue_List_464 .

inst:IfcParameterValue_List_464
        list:hasContents  inst:IfcLengthMeasure_246 .

inst:IfcParameterValue_List_465
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_466
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_467
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_465
        list:hasContents  inst:IfcParameterValue_409 ;
        list:hasNext      inst:IfcParameterValue_List_466 .

inst:IfcParameterValue_List_466
        list:hasContents  inst:IfcParameterValue_287 ;
        list:hasNext      inst:IfcParameterValue_List_467 .

inst:IfcParameterValue_List_467
        list:hasContents  inst:IfcLengthMeasure_246 .

inst:IfcParameterValue_List_468
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_469
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_470
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_468
        list:hasContents  inst:IfcParameterValue_413 ;
        list:hasNext      inst:IfcParameterValue_List_469 .

inst:IfcParameterValue_List_469
        list:hasContents  inst:IfcParameterValue_396 ;
        list:hasNext      inst:IfcParameterValue_List_470 .

inst:IfcParameterValue_List_470
        list:hasContents  inst:IfcLengthMeasure_246 .

inst:IfcParameterValue_List_471
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_472
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_473
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_471
        list:hasContents  inst:IfcParameterValue_417 ;
        list:hasNext      inst:IfcParameterValue_List_472 .

inst:IfcParameterValue_List_472
        list:hasContents  inst:IfcParameterValue_401 ;
        list:hasNext      inst:IfcParameterValue_List_473 .

inst:IfcParameterValue_List_473
        list:hasContents  inst:IfcLengthMeasure_246 .

inst:IfcParameterValue_List_474
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_475
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_476
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_474
        list:hasContents  inst:IfcParameterValue_421 ;
        list:hasNext      inst:IfcParameterValue_List_475 .

inst:IfcParameterValue_List_475
        list:hasContents  inst:IfcParameterValue_397 ;
        list:hasNext      inst:IfcParameterValue_List_476 .

inst:IfcParameterValue_List_476
        list:hasContents  inst:IfcLengthMeasure_246 .

inst:IfcParameterValue_List_477
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_478
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_479
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_477
        list:hasContents  inst:IfcParameterValue_283 ;
        list:hasNext      inst:IfcParameterValue_List_478 .

inst:IfcParameterValue_List_478
        list:hasContents  inst:IfcParameterValue_425 ;
        list:hasNext      inst:IfcParameterValue_List_479 .

inst:IfcParameterValue_List_479
        list:hasContents  inst:IfcLengthMeasure_246 .

inst:IfcParameterValue_List_480
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_481
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_482
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_480
        list:hasContents  inst:IfcParameterValue_421 ;
        list:hasNext      inst:IfcParameterValue_List_481 .

inst:IfcParameterValue_List_481
        list:hasContents  inst:IfcParameterValue_413 ;
        list:hasNext      inst:IfcParameterValue_List_482 .

inst:IfcParameterValue_List_482
        list:hasContents  inst:IfcLengthMeasure_246 .

inst:IfcParameterValue_List_483
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_484
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_485
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_483
        list:hasContents  inst:IfcParameterValue_432 ;
        list:hasNext      inst:IfcParameterValue_List_484 .

inst:IfcParameterValue_List_484
        list:hasContents  inst:IfcParameterValue_417 ;
        list:hasNext      inst:IfcParameterValue_List_485 .

inst:IfcParameterValue_List_485
        list:hasContents  inst:IfcLengthMeasure_246 .

inst:IfcParameterValue_List_486
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_487
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_488
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_486
        list:hasContents  inst:IfcParameterValue_413 ;
        list:hasNext      inst:IfcParameterValue_List_487 .

inst:IfcParameterValue_List_487
        list:hasContents  inst:IfcParameterValue_421 ;
        list:hasNext      inst:IfcParameterValue_List_488 .

inst:IfcParameterValue_List_488
        list:hasContents  inst:IfcLengthMeasure_246 .

inst:IfcParameterValue_List_489
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_490
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_491
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_489
        list:hasContents  inst:IfcParameterValue_439 ;
        list:hasNext      inst:IfcParameterValue_List_490 .

inst:IfcParameterValue_List_490
        list:hasContents  inst:IfcParameterValue_283 ;
        list:hasNext      inst:IfcParameterValue_List_491 .

inst:IfcParameterValue_List_491
        list:hasContents  inst:IfcLengthMeasure_246 .

inst:IfcParameterValue_List_492
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_493
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_494
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_492
        list:hasContents  inst:IfcParameterValue_397 ;
        list:hasNext      inst:IfcParameterValue_List_493 .

inst:IfcParameterValue_List_493
        list:hasContents  inst:IfcParameterValue_421 ;
        list:hasNext      inst:IfcParameterValue_List_494 .

inst:IfcParameterValue_List_494
        list:hasContents  inst:IfcLengthMeasure_246 .

inst:IfcParameterValue_List_495
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_496
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_497
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_495
        list:hasContents  inst:IfcParameterValue_402 ;
        list:hasNext      inst:IfcParameterValue_List_496 .

inst:IfcParameterValue_List_496
        list:hasContents  inst:IfcParameterValue_432 ;
        list:hasNext      inst:IfcParameterValue_List_497 .

inst:IfcParameterValue_List_497
        list:hasContents  inst:IfcLengthMeasure_246 .

inst:IfcParameterValue_List_498
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_499
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_500
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_498
        list:hasContents  inst:IfcParameterValue_396 ;
        list:hasNext      inst:IfcParameterValue_List_499 .

inst:IfcParameterValue_List_499
        list:hasContents  inst:IfcParameterValue_413 ;
        list:hasNext      inst:IfcParameterValue_List_500 .

inst:IfcParameterValue_List_500
        list:hasContents  inst:IfcLengthMeasure_246 .

inst:IfcParameterValue_List_501
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_502
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_503
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_501
        list:hasContents  inst:IfcParameterValue_287 ;
        list:hasNext      inst:IfcParameterValue_List_502 .

inst:IfcParameterValue_List_502
        list:hasContents  inst:IfcParameterValue_452 ;
        list:hasNext      inst:IfcParameterValue_List_503 .

inst:IfcParameterValue_List_503
        list:hasContents  inst:IfcLengthMeasure_246 .

inst:IfcParameterValue_List_List_504
        rdf:type  ifc:IfcParameterValue_List_List .

inst:IfcTriangulatedFaceSet_210
        ifc:normals_IfcTessellatedFaceSet  inst:IfcParameterValue_List_List_504 .

inst:IfcParameterValue_List_List_504
        list:hasContents  inst:IfcParameterValue_List_280 ;
        list:hasNext      inst:IfcParameterValue_List_List_505 .

inst:IfcParameterValue_List_List_505
        rdf:type          ifc:IfcParameterValue_List_List ;
        list:hasContents  inst:IfcParameterValue_List_284 ;
        list:hasNext      inst:IfcParameterValue_List_List_506 .

inst:IfcParameterValue_List_List_506
        rdf:type          ifc:IfcParameterValue_List_List ;
        list:hasContents  inst:IfcParameterValue_List_288 ;
        list:hasNext      inst:IfcParameterValue_List_List_507 .

inst:IfcParameterValue_List_List_507
        rdf:type          ifc:IfcParameterValue_List_List ;
        list:hasContents  inst:IfcParameterValue_List_291 ;
        list:hasNext      inst:IfcParameterValue_List_List_508 .

inst:IfcParameterValue_List_List_508
        rdf:type          ifc:IfcParameterValue_List_List ;
        list:hasContents  inst:IfcParameterValue_List_294 ;
        list:hasNext      inst:IfcParameterValue_List_List_509 .

inst:IfcParameterValue_List_List_509
        rdf:type          ifc:IfcParameterValue_List_List ;
        list:hasContents  inst:IfcParameterValue_List_297 ;
        list:hasNext      inst:IfcParameterValue_List_List_510 .

inst:IfcParameterValue_List_List_510
        rdf:type          ifc:IfcParameterValue_List_List ;
        list:hasContents  inst:IfcParameterValue_List_300 ;
        list:hasNext      inst:IfcParameterValue_List_List_511 .

inst:IfcParameterValue_List_List_511
        rdf:type          ifc:IfcParameterValue_List_List ;
        list:hasContents  inst:IfcParameterValue_List_303 ;
        list:hasNext      inst:IfcParameterValue_List_List_512 .

inst:IfcParameterValue_List_List_512
        rdf:type          ifc:IfcParameterValue_List_List ;
        list:hasContents  inst:IfcParameterValue_List_306 ;
        list:hasNext      inst:IfcParameterValue_List_List_513 .

inst:IfcParameterValue_List_List_513
        rdf:type          ifc:IfcParameterValue_List_List ;
        list:hasContents  inst:IfcParameterValue_List_309 ;
        list:hasNext      inst:IfcParameterValue_List_List_514 .

inst:IfcParameterValue_List_List_514
        rdf:type          ifc:IfcParameterValue_List_List ;
        list:hasContents  inst:IfcParameterValue_List_312 ;
        list:hasNext      inst:IfcParameterValue_List_List_515 .

inst:IfcParameterValue_List_List_515
        rdf:type          ifc:IfcParameterValue_List_List ;
        list:hasContents  inst:IfcParameterValue_List_315 ;
        list:hasNext      inst:IfcParameterValue_List_List_516 .

inst:IfcParameterValue_List_List_516
        rdf:type          ifc:IfcParameterValue_List_List ;
        list:hasContents  inst:IfcParameterValue_List_318 ;
        list:hasNext      inst:IfcParameterValue_List_List_517 .

inst:IfcParameterValue_List_List_517
        rdf:type          ifc:IfcParameterValue_List_List ;
        list:hasContents  inst:IfcParameterValue_List_321 ;
        list:hasNext      inst:IfcParameterValue_List_List_518 .

inst:IfcParameterValue_List_List_518
        rdf:type          ifc:IfcParameterValue_List_List ;
        list:hasContents  inst:IfcParameterValue_List_324 ;
        list:hasNext      inst:IfcParameterValue_List_List_519 .

inst:IfcParameterValue_List_List_519
        rdf:type          ifc:IfcParameterValue_List_List ;
        list:hasContents  inst:IfcParameterValue_List_327 ;
        list:hasNext      inst:IfcParameterValue_List_List_520 .

inst:IfcParameterValue_List_List_520
        rdf:type          ifc:IfcParameterValue_List_List ;
        list:hasContents  inst:IfcParameterValue_List_330 ;
        list:hasNext      inst:IfcParameterValue_List_List_521 .

inst:IfcParameterValue_List_List_521
        rdf:type          ifc:IfcParameterValue_List_List ;
        list:hasContents  inst:IfcParameterValue_List_333 ;
        list:hasNext      inst:IfcParameterValue_List_List_522 .

inst:IfcParameterValue_List_List_522
        rdf:type          ifc:IfcParameterValue_List_List ;
        list:hasContents  inst:IfcParameterValue_List_336 ;
        list:hasNext      inst:IfcParameterValue_List_List_523 .

inst:IfcParameterValue_List_List_523
        rdf:type          ifc:IfcParameterValue_List_List ;
        list:hasContents  inst:IfcParameterValue_List_339 ;
        list:hasNext      inst:IfcParameterValue_List_List_524 .

inst:IfcParameterValue_List_List_524
        rdf:type          ifc:IfcParameterValue_List_List ;
        list:hasContents  inst:IfcParameterValue_List_342 ;
        list:hasNext      inst:IfcParameterValue_List_List_525 .

inst:IfcParameterValue_List_List_525
        rdf:type          ifc:IfcParameterValue_List_List ;
        list:hasContents  inst:IfcParameterValue_List_345 ;
        list:hasNext      inst:IfcParameterValue_List_List_526 .

inst:IfcParameterValue_List_List_526
        rdf:type          ifc:IfcParameterValue_List_List ;
        list:hasContents  inst:IfcParameterValue_List_348 ;
        list:hasNext      inst:IfcParameterValue_List_List_527 .

inst:IfcParameterValue_List_List_527
        rdf:type          ifc:IfcParameterValue_List_List ;
        list:hasContents  inst:IfcParameterValue_List_351 ;
        list:hasNext      inst:IfcParameterValue_List_List_528 .

inst:IfcParameterValue_List_List_528
        rdf:type          ifc:IfcParameterValue_List_List ;
        list:hasContents  inst:IfcParameterValue_List_354 ;
        list:hasNext      inst:IfcParameterValue_List_List_529 .

inst:IfcParameterValue_List_List_529
        rdf:type          ifc:IfcParameterValue_List_List ;
        list:hasContents  inst:IfcParameterValue_List_357 ;
        list:hasNext      inst:IfcParameterValue_List_List_530 .

inst:IfcParameterValue_List_List_530
        rdf:type          ifc:IfcParameterValue_List_List ;
        list:hasContents  inst:IfcParameterValue_List_360 ;
        list:hasNext      inst:IfcParameterValue_List_List_531 .

inst:IfcParameterValue_List_List_531
        rdf:type          ifc:IfcParameterValue_List_List ;
        list:hasContents  inst:IfcParameterValue_List_363 ;
        list:hasNext      inst:IfcParameterValue_List_List_532 .

inst:IfcParameterValue_List_List_532
        rdf:type          ifc:IfcParameterValue_List_List ;
        list:hasContents  inst:IfcParameterValue_List_366 ;
        list:hasNext      inst:IfcParameterValue_List_List_533 .

inst:IfcParameterValue_List_List_533
        rdf:type          ifc:IfcParameterValue_List_List ;
        list:hasContents  inst:IfcParameterValue_List_369 ;
        list:hasNext      inst:IfcParameterValue_List_List_534 .

inst:IfcParameterValue_List_List_534
        rdf:type          ifc:IfcParameterValue_List_List ;
        list:hasContents  inst:IfcParameterValue_List_372 ;
        list:hasNext      inst:IfcParameterValue_List_List_535 .

inst:IfcParameterValue_List_List_535
        rdf:type          ifc:IfcParameterValue_List_List ;
        list:hasContents  inst:IfcParameterValue_List_375 ;
        list:hasNext      inst:IfcParameterValue_List_List_536 .

inst:IfcParameterValue_List_List_536
        rdf:type          ifc:IfcParameterValue_List_List ;
        list:hasContents  inst:IfcParameterValue_List_378 ;
        list:hasNext      inst:IfcParameterValue_List_List_537 .

inst:IfcParameterValue_List_List_537
        rdf:type          ifc:IfcParameterValue_List_List ;
        list:hasContents  inst:IfcParameterValue_List_381 ;
        list:hasNext      inst:IfcParameterValue_List_List_538 .

inst:IfcParameterValue_List_List_538
        rdf:type          ifc:IfcParameterValue_List_List ;
        list:hasContents  inst:IfcParameterValue_List_384 ;
        list:hasNext      inst:IfcParameterValue_List_List_539 .

inst:IfcParameterValue_List_List_539
        rdf:type          ifc:IfcParameterValue_List_List ;
        list:hasContents  inst:IfcParameterValue_List_387 ;
        list:hasNext      inst:IfcParameterValue_List_List_540 .

inst:IfcParameterValue_List_List_540
        rdf:type          ifc:IfcParameterValue_List_List ;
        list:hasContents  inst:IfcParameterValue_List_390 ;
        list:hasNext      inst:IfcParameterValue_List_List_541 .

inst:IfcParameterValue_List_List_541
        rdf:type          ifc:IfcParameterValue_List_List ;
        list:hasContents  inst:IfcParameterValue_List_393 ;
        list:hasNext      inst:IfcParameterValue_List_List_542 .

inst:IfcParameterValue_List_List_542
        rdf:type          ifc:IfcParameterValue_List_List ;
        list:hasContents  inst:IfcParameterValue_List_398 ;
        list:hasNext      inst:IfcParameterValue_List_List_543 .

inst:IfcParameterValue_List_List_543
        rdf:type          ifc:IfcParameterValue_List_List ;
        list:hasContents  inst:IfcParameterValue_List_403 ;
        list:hasNext      inst:IfcParameterValue_List_List_544 .

inst:IfcParameterValue_List_List_544
        rdf:type          ifc:IfcParameterValue_List_List ;
        list:hasContents  inst:IfcParameterValue_List_406 ;
        list:hasNext      inst:IfcParameterValue_List_List_545 .

inst:IfcParameterValue_List_List_545
        rdf:type          ifc:IfcParameterValue_List_List ;
        list:hasContents  inst:IfcParameterValue_List_410 ;
        list:hasNext      inst:IfcParameterValue_List_List_546 .

inst:IfcParameterValue_List_List_546
        rdf:type          ifc:IfcParameterValue_List_List ;
        list:hasContents  inst:IfcParameterValue_List_414 ;
        list:hasNext      inst:IfcParameterValue_List_List_547 .

inst:IfcParameterValue_List_List_547
        rdf:type          ifc:IfcParameterValue_List_List ;
        list:hasContents  inst:IfcParameterValue_List_418 ;
        list:hasNext      inst:IfcParameterValue_List_List_548 .

inst:IfcParameterValue_List_List_548
        rdf:type          ifc:IfcParameterValue_List_List ;
        list:hasContents  inst:IfcParameterValue_List_422 ;
        list:hasNext      inst:IfcParameterValue_List_List_549 .

inst:IfcParameterValue_List_List_549
        rdf:type          ifc:IfcParameterValue_List_List ;
        list:hasContents  inst:IfcParameterValue_List_426 ;
        list:hasNext      inst:IfcParameterValue_List_List_550 .

inst:IfcParameterValue_List_List_550
        rdf:type          ifc:IfcParameterValue_List_List ;
        list:hasContents  inst:IfcParameterValue_List_429 ;
        list:hasNext      inst:IfcParameterValue_List_List_551 .

inst:IfcParameterValue_List_List_551
        rdf:type          ifc:IfcParameterValue_List_List ;
        list:hasContents  inst:IfcParameterValue_List_433 ;
        list:hasNext      inst:IfcParameterValue_List_List_552 .

inst:IfcParameterValue_List_List_552
        rdf:type          ifc:IfcParameterValue_List_List ;
        list:hasContents  inst:IfcParameterValue_List_436 ;
        list:hasNext      inst:IfcParameterValue_List_List_553 .

inst:IfcParameterValue_List_List_553
        rdf:type          ifc:IfcParameterValue_List_List ;
        list:hasContents  inst:IfcParameterValue_List_440 ;
        list:hasNext      inst:IfcParameterValue_List_List_554 .

inst:IfcParameterValue_List_List_554
        rdf:type          ifc:IfcParameterValue_List_List ;
        list:hasContents  inst:IfcParameterValue_List_443 ;
        list:hasNext      inst:IfcParameterValue_List_List_555 .

inst:IfcParameterValue_List_List_555
        rdf:type          ifc:IfcParameterValue_List_List ;
        list:hasContents  inst:IfcParameterValue_List_446 ;
        list:hasNext      inst:IfcParameterValue_List_List_556 .

inst:IfcParameterValue_List_List_556
        rdf:type          ifc:IfcParameterValue_List_List ;
        list:hasContents  inst:IfcParameterValue_List_449 ;
        list:hasNext      inst:IfcParameterValue_List_List_557 .

inst:IfcParameterValue_List_List_557
        rdf:type          ifc:IfcParameterValue_List_List ;
        list:hasContents  inst:IfcParameterValue_List_453 ;
        list:hasNext      inst:IfcParameterValue_List_List_558 .

inst:IfcParameterValue_List_List_558
        rdf:type          ifc:IfcParameterValue_List_List ;
        list:hasContents  inst:IfcParameterValue_List_456 ;
        list:hasNext      inst:IfcParameterValue_List_List_559 .

inst:IfcParameterValue_List_List_559
        rdf:type          ifc:IfcParameterValue_List_List ;
        list:hasContents  inst:IfcParameterValue_List_459 ;
        list:hasNext      inst:IfcParameterValue_List_List_560 .

inst:IfcParameterValue_List_List_560
        rdf:type          ifc:IfcParameterValue_List_List ;
        list:hasContents  inst:IfcParameterValue_List_462 ;
        list:hasNext      inst:IfcParameterValue_List_List_561 .

inst:IfcParameterValue_List_List_561
        rdf:type          ifc:IfcParameterValue_List_List ;
        list:hasContents  inst:IfcParameterValue_List_465 ;
        list:hasNext      inst:IfcParameterValue_List_List_562 .

inst:IfcParameterValue_List_List_562
        rdf:type          ifc:IfcParameterValue_List_List ;
        list:hasContents  inst:IfcParameterValue_List_468 ;
        list:hasNext      inst:IfcParameterValue_List_List_563 .

inst:IfcParameterValue_List_List_563
        rdf:type          ifc:IfcParameterValue_List_List ;
        list:hasContents  inst:IfcParameterValue_List_471 ;
        list:hasNext      inst:IfcParameterValue_List_List_564 .

inst:IfcParameterValue_List_List_564
        rdf:type          ifc:IfcParameterValue_List_List ;
        list:hasContents  inst:IfcParameterValue_List_474 ;
        list:hasNext      inst:IfcParameterValue_List_List_565 .

inst:IfcParameterValue_List_List_565
        rdf:type          ifc:IfcParameterValue_List_List ;
        list:hasContents  inst:IfcParameterValue_List_477 ;
        list:hasNext      inst:IfcParameterValue_List_List_566 .

inst:IfcParameterValue_List_List_566
        rdf:type          ifc:IfcParameterValue_List_List ;
        list:hasContents  inst:IfcParameterValue_List_480 ;
        list:hasNext      inst:IfcParameterValue_List_List_567 .

inst:IfcParameterValue_List_List_567
        rdf:type          ifc:IfcParameterValue_List_List ;
        list:hasContents  inst:IfcParameterValue_List_483 ;
        list:hasNext      inst:IfcParameterValue_List_List_568 .

inst:IfcParameterValue_List_List_568
        rdf:type          ifc:IfcParameterValue_List_List ;
        list:hasContents  inst:IfcParameterValue_List_486 ;
        list:hasNext      inst:IfcParameterValue_List_List_569 .

inst:IfcParameterValue_List_List_569
        rdf:type          ifc:IfcParameterValue_List_List ;
        list:hasContents  inst:IfcParameterValue_List_489 ;
        list:hasNext      inst:IfcParameterValue_List_List_570 .

inst:IfcParameterValue_List_List_570
        rdf:type          ifc:IfcParameterValue_List_List ;
        list:hasContents  inst:IfcParameterValue_List_492 ;
        list:hasNext      inst:IfcParameterValue_List_List_571 .

inst:IfcParameterValue_List_List_571
        rdf:type          ifc:IfcParameterValue_List_List ;
        list:hasContents  inst:IfcParameterValue_List_495 ;
        list:hasNext      inst:IfcParameterValue_List_List_572 .

inst:IfcParameterValue_List_List_572
        rdf:type          ifc:IfcParameterValue_List_List ;
        list:hasContents  inst:IfcParameterValue_List_498 ;
        list:hasNext      inst:IfcParameterValue_List_List_573 .

inst:IfcParameterValue_List_List_573
        rdf:type          ifc:IfcParameterValue_List_List ;
        list:hasContents  inst:IfcParameterValue_List_501 .

inst:IfcBoolean_574  rdf:type  ifc:IfcBoolean ;
        express:hasBoolean  true .

inst:IfcTriangulatedFaceSet_210
        ifc:closed_IfcTessellatedFaceSet  inst:IfcBoolean_574 .

inst:IfcPositiveInteger_List_575
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_576
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_577
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_575
        list:hasContents  inst:INTEGER_249 ;
        list:hasNext      inst:IfcPositiveInteger_List_576 .

inst:IfcPositiveInteger_List_576
        list:hasContents  inst:INTEGER_251 ;
        list:hasNext      inst:IfcPositiveInteger_List_577 .

inst:IfcPositiveInteger_List_577
        list:hasContents  inst:IfcDimensionCount_234 .

inst:IfcPositiveInteger_List_578
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_579
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_580
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_581
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  38 .

inst:IfcPositiveInteger_List_578
        list:hasContents  inst:IfcPositiveInteger_581 ;
        list:hasNext      inst:IfcPositiveInteger_List_579 .

inst:IfcPositiveInteger_582
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  54 .

inst:IfcPositiveInteger_List_579
        list:hasContents  inst:IfcPositiveInteger_582 ;
        list:hasNext      inst:IfcPositiveInteger_List_580 .

inst:IfcPositiveInteger_583
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  37 .

inst:IfcPositiveInteger_List_580
        list:hasContents  inst:IfcPositiveInteger_583 .

inst:IfcPositiveInteger_List_584
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_585
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_586
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_584
        list:hasContents  inst:IfcPositiveInteger_581 ;
        list:hasNext      inst:IfcPositiveInteger_List_585 .

inst:IfcPositiveInteger_587
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  55 .

inst:IfcPositiveInteger_List_585
        list:hasContents  inst:IfcPositiveInteger_587 ;
        list:hasNext      inst:IfcPositiveInteger_List_586 .

inst:IfcPositiveInteger_List_586
        list:hasContents  inst:IfcPositiveInteger_582 .

inst:IfcPositiveInteger_List_588
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_589
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_590
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_588
        list:hasContents  inst:IfcDimensionCount_238 ;
        list:hasNext      inst:IfcPositiveInteger_List_589 .

inst:IfcPositiveInteger_591
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  20 .

inst:IfcPositiveInteger_List_589
        list:hasContents  inst:IfcPositiveInteger_591 ;
        list:hasNext      inst:IfcPositiveInteger_List_590 .

inst:IfcPositiveInteger_592
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  21 .

inst:IfcPositiveInteger_List_590
        list:hasContents  inst:IfcPositiveInteger_592 .

inst:IfcPositiveInteger_List_593
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_594
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_595
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_593
        list:hasContents  inst:INTEGER_249 ;
        list:hasNext      inst:IfcPositiveInteger_List_594 .

inst:IfcPositiveInteger_List_594
        list:hasContents  inst:INTEGER_252 ;
        list:hasNext      inst:IfcPositiveInteger_List_595 .

inst:IfcPositiveInteger_List_595
        list:hasContents  inst:INTEGER_251 .

inst:IfcPositiveInteger_List_596
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_597
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_598
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_599
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  39 .

inst:IfcPositiveInteger_List_596
        list:hasContents  inst:IfcPositiveInteger_599 ;
        list:hasNext      inst:IfcPositiveInteger_List_597 .

inst:IfcPositiveInteger_List_597
        list:hasContents  inst:IfcPositiveInteger_587 ;
        list:hasNext      inst:IfcPositiveInteger_List_598 .

inst:IfcPositiveInteger_List_598
        list:hasContents  inst:IfcPositiveInteger_581 .

inst:IfcPositiveInteger_List_600
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_601
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_602
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_600
        list:hasContents  inst:IfcPositiveInteger_599 ;
        list:hasNext      inst:IfcPositiveInteger_List_601 .

inst:IfcPositiveInteger_603
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  56 .

inst:IfcPositiveInteger_List_601
        list:hasContents  inst:IfcPositiveInteger_603 ;
        list:hasNext      inst:IfcPositiveInteger_List_602 .

inst:IfcPositiveInteger_List_602
        list:hasContents  inst:IfcPositiveInteger_587 .

inst:IfcPositiveInteger_List_604
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_605
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_606
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_604
        list:hasContents  inst:IfcDimensionCount_238 ;
        list:hasNext      inst:IfcPositiveInteger_List_605 .

inst:IfcPositiveInteger_List_605
        list:hasContents  inst:IfcPositiveInteger_592 ;
        list:hasNext      inst:IfcPositiveInteger_List_606 .

inst:IfcPositiveInteger_607
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  22 .

inst:IfcPositiveInteger_List_606
        list:hasContents  inst:IfcPositiveInteger_607 .

inst:IfcPositiveInteger_List_608
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_609
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_610
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_608
        list:hasContents  inst:INTEGER_249 ;
        list:hasNext      inst:IfcPositiveInteger_List_609 .

inst:IfcPositiveInteger_List_609
        list:hasContents  inst:INTEGER_253 ;
        list:hasNext      inst:IfcPositiveInteger_List_610 .

inst:IfcPositiveInteger_List_610
        list:hasContents  inst:INTEGER_252 .

inst:IfcPositiveInteger_List_611
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_612
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_613
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_614
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  40 .

inst:IfcPositiveInteger_List_611
        list:hasContents  inst:IfcPositiveInteger_614 ;
        list:hasNext      inst:IfcPositiveInteger_List_612 .

inst:IfcPositiveInteger_List_612
        list:hasContents  inst:IfcPositiveInteger_603 ;
        list:hasNext      inst:IfcPositiveInteger_List_613 .

inst:IfcPositiveInteger_List_613
        list:hasContents  inst:IfcPositiveInteger_599 .

inst:IfcPositiveInteger_List_615
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_616
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_617
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_615
        list:hasContents  inst:IfcPositiveInteger_614 ;
        list:hasNext      inst:IfcPositiveInteger_List_616 .

inst:IfcPositiveInteger_618
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  57 .

inst:IfcPositiveInteger_List_616
        list:hasContents  inst:IfcPositiveInteger_618 ;
        list:hasNext      inst:IfcPositiveInteger_List_617 .

inst:IfcPositiveInteger_List_617
        list:hasContents  inst:IfcPositiveInteger_603 .

inst:IfcPositiveInteger_List_619
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_620
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_621
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_619
        list:hasContents  inst:IfcDimensionCount_238 ;
        list:hasNext      inst:IfcPositiveInteger_List_620 .

inst:IfcPositiveInteger_List_620
        list:hasContents  inst:IfcPositiveInteger_607 ;
        list:hasNext      inst:IfcPositiveInteger_List_621 .

inst:IfcPositiveInteger_622
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  23 .

inst:IfcPositiveInteger_List_621
        list:hasContents  inst:IfcPositiveInteger_622 .

inst:IfcPositiveInteger_List_623
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_624
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_625
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_623
        list:hasContents  inst:INTEGER_249 ;
        list:hasNext      inst:IfcPositiveInteger_List_624 .

inst:IfcPositiveInteger_626
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  7 .

inst:IfcPositiveInteger_List_624
        list:hasContents  inst:IfcPositiveInteger_626 ;
        list:hasNext      inst:IfcPositiveInteger_List_625 .

inst:IfcPositiveInteger_List_625
        list:hasContents  inst:INTEGER_253 .

inst:IfcPositiveInteger_List_627
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_628
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_629
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_630
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  41 .

inst:IfcPositiveInteger_List_627
        list:hasContents  inst:IfcPositiveInteger_630 ;
        list:hasNext      inst:IfcPositiveInteger_List_628 .

inst:IfcPositiveInteger_List_628
        list:hasContents  inst:IfcPositiveInteger_618 ;
        list:hasNext      inst:IfcPositiveInteger_List_629 .

inst:IfcPositiveInteger_List_629
        list:hasContents  inst:IfcPositiveInteger_614 .

inst:IfcPositiveInteger_List_631
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_632
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_633
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_631
        list:hasContents  inst:IfcPositiveInteger_630 ;
        list:hasNext      inst:IfcPositiveInteger_List_632 .

inst:IfcPositiveInteger_634
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  58 .

inst:IfcPositiveInteger_List_632
        list:hasContents  inst:IfcPositiveInteger_634 ;
        list:hasNext      inst:IfcPositiveInteger_List_633 .

inst:IfcPositiveInteger_List_633
        list:hasContents  inst:IfcPositiveInteger_618 .

inst:IfcPositiveInteger_List_635
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_636
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_637
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_635
        list:hasContents  inst:IfcDimensionCount_238 ;
        list:hasNext      inst:IfcPositiveInteger_List_636 .

inst:IfcPositiveInteger_List_636
        list:hasContents  inst:IfcPositiveInteger_622 ;
        list:hasNext      inst:IfcPositiveInteger_List_637 .

inst:IfcPositiveInteger_638
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  24 .

inst:IfcPositiveInteger_List_637
        list:hasContents  inst:IfcPositiveInteger_638 .

inst:IfcPositiveInteger_List_639
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_640
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_641
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_639
        list:hasContents  inst:INTEGER_249 ;
        list:hasNext      inst:IfcPositiveInteger_List_640 .

inst:IfcPositiveInteger_642
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  8 .

inst:IfcPositiveInteger_List_640
        list:hasContents  inst:IfcPositiveInteger_642 ;
        list:hasNext      inst:IfcPositiveInteger_List_641 .

inst:IfcPositiveInteger_List_641
        list:hasContents  inst:IfcPositiveInteger_626 .

inst:IfcPositiveInteger_List_643
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_644
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_645
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_646
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  42 .

inst:IfcPositiveInteger_List_643
        list:hasContents  inst:IfcPositiveInteger_646 ;
        list:hasNext      inst:IfcPositiveInteger_List_644 .

inst:IfcPositiveInteger_List_644
        list:hasContents  inst:IfcPositiveInteger_634 ;
        list:hasNext      inst:IfcPositiveInteger_List_645 .

inst:IfcPositiveInteger_List_645
        list:hasContents  inst:IfcPositiveInteger_630 .

inst:IfcPositiveInteger_List_647
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_648
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_649
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_647
        list:hasContents  inst:IfcPositiveInteger_646 ;
        list:hasNext      inst:IfcPositiveInteger_List_648 .

inst:IfcPositiveInteger_650
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  59 .

inst:IfcPositiveInteger_List_648
        list:hasContents  inst:IfcPositiveInteger_650 ;
        list:hasNext      inst:IfcPositiveInteger_List_649 .

inst:IfcPositiveInteger_List_649
        list:hasContents  inst:IfcPositiveInteger_634 .

inst:IfcPositiveInteger_List_651
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_652
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_653
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_651
        list:hasContents  inst:IfcDimensionCount_238 ;
        list:hasNext      inst:IfcPositiveInteger_List_652 .

inst:IfcPositiveInteger_List_652
        list:hasContents  inst:IfcPositiveInteger_638 ;
        list:hasNext      inst:IfcPositiveInteger_List_653 .

inst:IfcPositiveInteger_654
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  25 .

inst:IfcPositiveInteger_List_653
        list:hasContents  inst:IfcPositiveInteger_654 .

inst:IfcPositiveInteger_List_655
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_656
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_657
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_655
        list:hasContents  inst:INTEGER_249 ;
        list:hasNext      inst:IfcPositiveInteger_List_656 .

inst:IfcPositiveInteger_658
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  9 .

inst:IfcPositiveInteger_List_656
        list:hasContents  inst:IfcPositiveInteger_658 ;
        list:hasNext      inst:IfcPositiveInteger_List_657 .

inst:IfcPositiveInteger_List_657
        list:hasContents  inst:IfcPositiveInteger_642 .

inst:IfcPositiveInteger_List_659
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_660
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_661
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_662
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  43 .

inst:IfcPositiveInteger_List_659
        list:hasContents  inst:IfcPositiveInteger_662 ;
        list:hasNext      inst:IfcPositiveInteger_List_660 .

inst:IfcPositiveInteger_List_660
        list:hasContents  inst:IfcPositiveInteger_650 ;
        list:hasNext      inst:IfcPositiveInteger_List_661 .

inst:IfcPositiveInteger_List_661
        list:hasContents  inst:IfcPositiveInteger_646 .

inst:IfcPositiveInteger_List_663
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_664
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_665
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_663
        list:hasContents  inst:IfcPositiveInteger_662 ;
        list:hasNext      inst:IfcPositiveInteger_List_664 .

inst:IfcPositiveInteger_666
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  60 .

inst:IfcPositiveInteger_List_664
        list:hasContents  inst:IfcPositiveInteger_666 ;
        list:hasNext      inst:IfcPositiveInteger_List_665 .

inst:IfcPositiveInteger_List_665
        list:hasContents  inst:IfcPositiveInteger_650 .

inst:IfcPositiveInteger_List_667
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_668
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_669
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_667
        list:hasContents  inst:IfcDimensionCount_238 ;
        list:hasNext      inst:IfcPositiveInteger_List_668 .

inst:IfcPositiveInteger_List_668
        list:hasContents  inst:IfcPositiveInteger_654 ;
        list:hasNext      inst:IfcPositiveInteger_List_669 .

inst:IfcPositiveInteger_670
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  26 .

inst:IfcPositiveInteger_List_669
        list:hasContents  inst:IfcPositiveInteger_670 .

inst:IfcPositiveInteger_List_671
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_672
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_673
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_671
        list:hasContents  inst:INTEGER_249 ;
        list:hasNext      inst:IfcPositiveInteger_List_672 .

inst:IfcPositiveInteger_674
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  10 .

inst:IfcPositiveInteger_List_672
        list:hasContents  inst:IfcPositiveInteger_674 ;
        list:hasNext      inst:IfcPositiveInteger_List_673 .

inst:IfcPositiveInteger_List_673
        list:hasContents  inst:IfcPositiveInteger_658 .

inst:IfcPositiveInteger_List_675
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_676
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_677
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_678
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  44 .

inst:IfcPositiveInteger_List_675
        list:hasContents  inst:IfcPositiveInteger_678 ;
        list:hasNext      inst:IfcPositiveInteger_List_676 .

inst:IfcPositiveInteger_List_676
        list:hasContents  inst:IfcPositiveInteger_666 ;
        list:hasNext      inst:IfcPositiveInteger_List_677 .

inst:IfcPositiveInteger_List_677
        list:hasContents  inst:IfcPositiveInteger_662 .

inst:IfcPositiveInteger_List_679
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_680
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_681
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_679
        list:hasContents  inst:IfcPositiveInteger_678 ;
        list:hasNext      inst:IfcPositiveInteger_List_680 .

inst:IfcPositiveInteger_682
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  61 .

inst:IfcPositiveInteger_List_680
        list:hasContents  inst:IfcPositiveInteger_682 ;
        list:hasNext      inst:IfcPositiveInteger_List_681 .

inst:IfcPositiveInteger_List_681
        list:hasContents  inst:IfcPositiveInteger_666 .

inst:IfcPositiveInteger_List_683
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_684
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_685
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_683
        list:hasContents  inst:IfcDimensionCount_238 ;
        list:hasNext      inst:IfcPositiveInteger_List_684 .

inst:IfcPositiveInteger_List_684
        list:hasContents  inst:IfcPositiveInteger_670 ;
        list:hasNext      inst:IfcPositiveInteger_List_685 .

inst:IfcPositiveInteger_686
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  27 .

inst:IfcPositiveInteger_List_685
        list:hasContents  inst:IfcPositiveInteger_686 .

inst:IfcPositiveInteger_List_687
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_688
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_689
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_687
        list:hasContents  inst:INTEGER_249 ;
        list:hasNext      inst:IfcPositiveInteger_List_688 .

inst:IfcPositiveInteger_690
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  11 .

inst:IfcPositiveInteger_List_688
        list:hasContents  inst:IfcPositiveInteger_690 ;
        list:hasNext      inst:IfcPositiveInteger_List_689 .

inst:IfcPositiveInteger_List_689
        list:hasContents  inst:IfcPositiveInteger_674 .

inst:IfcPositiveInteger_List_691
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_692
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_693
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_694
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  45 .

inst:IfcPositiveInteger_List_691
        list:hasContents  inst:IfcPositiveInteger_694 ;
        list:hasNext      inst:IfcPositiveInteger_List_692 .

inst:IfcPositiveInteger_List_692
        list:hasContents  inst:IfcPositiveInteger_682 ;
        list:hasNext      inst:IfcPositiveInteger_List_693 .

inst:IfcPositiveInteger_List_693
        list:hasContents  inst:IfcPositiveInteger_678 .

inst:IfcPositiveInteger_List_695
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_696
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_697
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_695
        list:hasContents  inst:IfcPositiveInteger_694 ;
        list:hasNext      inst:IfcPositiveInteger_List_696 .

inst:IfcPositiveInteger_698
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  62 .

inst:IfcPositiveInteger_List_696
        list:hasContents  inst:IfcPositiveInteger_698 ;
        list:hasNext      inst:IfcPositiveInteger_List_697 .

inst:IfcPositiveInteger_List_697
        list:hasContents  inst:IfcPositiveInteger_682 .

inst:IfcPositiveInteger_List_699
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_700
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_701
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_699
        list:hasContents  inst:IfcDimensionCount_238 ;
        list:hasNext      inst:IfcPositiveInteger_List_700 .

inst:IfcPositiveInteger_List_700
        list:hasContents  inst:IfcPositiveInteger_686 ;
        list:hasNext      inst:IfcPositiveInteger_List_701 .

inst:IfcPositiveInteger_702
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  28 .

inst:IfcPositiveInteger_List_701
        list:hasContents  inst:IfcPositiveInteger_702 .

inst:IfcPositiveInteger_List_703
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_704
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_705
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_703
        list:hasContents  inst:INTEGER_249 ;
        list:hasNext      inst:IfcPositiveInteger_List_704 .

inst:IfcPositiveInteger_706
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  12 .

inst:IfcPositiveInteger_List_704
        list:hasContents  inst:IfcPositiveInteger_706 ;
        list:hasNext      inst:IfcPositiveInteger_List_705 .

inst:IfcPositiveInteger_List_705
        list:hasContents  inst:IfcPositiveInteger_690 .

inst:IfcPositiveInteger_List_707
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_708
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_709
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_710
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  46 .

inst:IfcPositiveInteger_List_707
        list:hasContents  inst:IfcPositiveInteger_710 ;
        list:hasNext      inst:IfcPositiveInteger_List_708 .

inst:IfcPositiveInteger_List_708
        list:hasContents  inst:IfcPositiveInteger_698 ;
        list:hasNext      inst:IfcPositiveInteger_List_709 .

inst:IfcPositiveInteger_List_709
        list:hasContents  inst:IfcPositiveInteger_694 .

inst:IfcPositiveInteger_List_711
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_712
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_713
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_711
        list:hasContents  inst:IfcPositiveInteger_710 ;
        list:hasNext      inst:IfcPositiveInteger_List_712 .

inst:IfcPositiveInteger_714
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  63 .

inst:IfcPositiveInteger_List_712
        list:hasContents  inst:IfcPositiveInteger_714 ;
        list:hasNext      inst:IfcPositiveInteger_List_713 .

inst:IfcPositiveInteger_List_713
        list:hasContents  inst:IfcPositiveInteger_698 .

inst:IfcPositiveInteger_List_715
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_716
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_717
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_715
        list:hasContents  inst:IfcDimensionCount_238 ;
        list:hasNext      inst:IfcPositiveInteger_List_716 .

inst:IfcPositiveInteger_List_716
        list:hasContents  inst:IfcPositiveInteger_702 ;
        list:hasNext      inst:IfcPositiveInteger_List_717 .

inst:IfcPositiveInteger_718
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  29 .

inst:IfcPositiveInteger_List_717
        list:hasContents  inst:IfcPositiveInteger_718 .

inst:IfcPositiveInteger_List_719
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_720
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_721
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_719
        list:hasContents  inst:INTEGER_249 ;
        list:hasNext      inst:IfcPositiveInteger_List_720 .

inst:IfcPositiveInteger_722
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  13 .

inst:IfcPositiveInteger_List_720
        list:hasContents  inst:IfcPositiveInteger_722 ;
        list:hasNext      inst:IfcPositiveInteger_List_721 .

inst:IfcPositiveInteger_List_721
        list:hasContents  inst:IfcPositiveInteger_706 .

inst:IfcPositiveInteger_List_723
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_724
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_725
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_726
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  47 .

inst:IfcPositiveInteger_List_723
        list:hasContents  inst:IfcPositiveInteger_726 ;
        list:hasNext      inst:IfcPositiveInteger_List_724 .

inst:IfcPositiveInteger_List_724
        list:hasContents  inst:IfcPositiveInteger_714 ;
        list:hasNext      inst:IfcPositiveInteger_List_725 .

inst:IfcPositiveInteger_List_725
        list:hasContents  inst:IfcPositiveInteger_710 .

inst:IfcPositiveInteger_List_727
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_728
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_729
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_727
        list:hasContents  inst:IfcPositiveInteger_726 ;
        list:hasNext      inst:IfcPositiveInteger_List_728 .

inst:IfcPositiveInteger_730
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  64 .

inst:IfcPositiveInteger_List_728
        list:hasContents  inst:IfcPositiveInteger_730 ;
        list:hasNext      inst:IfcPositiveInteger_List_729 .

inst:IfcPositiveInteger_List_729
        list:hasContents  inst:IfcPositiveInteger_714 .

inst:IfcPositiveInteger_List_731
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_732
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_733
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_731
        list:hasContents  inst:IfcDimensionCount_238 ;
        list:hasNext      inst:IfcPositiveInteger_List_732 .

inst:IfcPositiveInteger_List_732
        list:hasContents  inst:IfcPositiveInteger_718 ;
        list:hasNext      inst:IfcPositiveInteger_List_733 .

inst:IfcPositiveInteger_734
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  30 .

inst:IfcPositiveInteger_List_733
        list:hasContents  inst:IfcPositiveInteger_734 .

inst:IfcPositiveInteger_List_735
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_736
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_737
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_735
        list:hasContents  inst:INTEGER_249 ;
        list:hasNext      inst:IfcPositiveInteger_List_736 .

inst:IfcPositiveInteger_738
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  14 .

inst:IfcPositiveInteger_List_736
        list:hasContents  inst:IfcPositiveInteger_738 ;
        list:hasNext      inst:IfcPositiveInteger_List_737 .

inst:IfcPositiveInteger_List_737
        list:hasContents  inst:IfcPositiveInteger_722 .

inst:IfcPositiveInteger_List_739
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_740
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_741
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_742
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  48 .

inst:IfcPositiveInteger_List_739
        list:hasContents  inst:IfcPositiveInteger_742 ;
        list:hasNext      inst:IfcPositiveInteger_List_740 .

inst:IfcPositiveInteger_List_740
        list:hasContents  inst:IfcPositiveInteger_730 ;
        list:hasNext      inst:IfcPositiveInteger_List_741 .

inst:IfcPositiveInteger_List_741
        list:hasContents  inst:IfcPositiveInteger_726 .

inst:IfcPositiveInteger_List_743
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_744
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_745
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_743
        list:hasContents  inst:IfcPositiveInteger_742 ;
        list:hasNext      inst:IfcPositiveInteger_List_744 .

inst:IfcPositiveInteger_746
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  65 .

inst:IfcPositiveInteger_List_744
        list:hasContents  inst:IfcPositiveInteger_746 ;
        list:hasNext      inst:IfcPositiveInteger_List_745 .

inst:IfcPositiveInteger_List_745
        list:hasContents  inst:IfcPositiveInteger_730 .

inst:IfcPositiveInteger_List_747
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_748
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_749
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_747
        list:hasContents  inst:IfcDimensionCount_238 ;
        list:hasNext      inst:IfcPositiveInteger_List_748 .

inst:IfcPositiveInteger_List_748
        list:hasContents  inst:IfcPositiveInteger_734 ;
        list:hasNext      inst:IfcPositiveInteger_List_749 .

inst:IfcPositiveInteger_750
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  31 .

inst:IfcPositiveInteger_List_749
        list:hasContents  inst:IfcPositiveInteger_750 .

inst:IfcPositiveInteger_List_751
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_752
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_753
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_751
        list:hasContents  inst:INTEGER_249 ;
        list:hasNext      inst:IfcPositiveInteger_List_752 .

inst:IfcPositiveInteger_754
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  15 .

inst:IfcPositiveInteger_List_752
        list:hasContents  inst:IfcPositiveInteger_754 ;
        list:hasNext      inst:IfcPositiveInteger_List_753 .

inst:IfcPositiveInteger_List_753
        list:hasContents  inst:IfcPositiveInteger_738 .

inst:IfcPositiveInteger_List_755
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_756
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_757
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_758
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  49 .

inst:IfcPositiveInteger_List_755
        list:hasContents  inst:IfcPositiveInteger_758 ;
        list:hasNext      inst:IfcPositiveInteger_List_756 .

inst:IfcPositiveInteger_List_756
        list:hasContents  inst:IfcPositiveInteger_746 ;
        list:hasNext      inst:IfcPositiveInteger_List_757 .

inst:IfcPositiveInteger_List_757
        list:hasContents  inst:IfcPositiveInteger_742 .

inst:IfcPositiveInteger_List_759
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_760
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_761
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_759
        list:hasContents  inst:IfcPositiveInteger_758 ;
        list:hasNext      inst:IfcPositiveInteger_List_760 .

inst:IfcPositiveInteger_762
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  66 .

inst:IfcPositiveInteger_List_760
        list:hasContents  inst:IfcPositiveInteger_762 ;
        list:hasNext      inst:IfcPositiveInteger_List_761 .

inst:IfcPositiveInteger_List_761
        list:hasContents  inst:IfcPositiveInteger_746 .

inst:IfcPositiveInteger_List_763
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_764
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_765
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_763
        list:hasContents  inst:IfcDimensionCount_238 ;
        list:hasNext      inst:IfcPositiveInteger_List_764 .

inst:IfcPositiveInteger_List_764
        list:hasContents  inst:IfcPositiveInteger_750 ;
        list:hasNext      inst:IfcPositiveInteger_List_765 .

inst:IfcPositiveInteger_766
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  32 .

inst:IfcPositiveInteger_List_765
        list:hasContents  inst:IfcPositiveInteger_766 .

inst:IfcPositiveInteger_List_767
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_768
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_769
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_767
        list:hasContents  inst:INTEGER_249 ;
        list:hasNext      inst:IfcPositiveInteger_List_768 .

inst:IfcPositiveInteger_770
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  16 .

inst:IfcPositiveInteger_List_768
        list:hasContents  inst:IfcPositiveInteger_770 ;
        list:hasNext      inst:IfcPositiveInteger_List_769 .

inst:IfcPositiveInteger_List_769
        list:hasContents  inst:IfcPositiveInteger_754 .

inst:IfcPositiveInteger_List_771
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_772
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_773
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_774
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  50 .

inst:IfcPositiveInteger_List_771
        list:hasContents  inst:IfcPositiveInteger_774 ;
        list:hasNext      inst:IfcPositiveInteger_List_772 .

inst:IfcPositiveInteger_List_772
        list:hasContents  inst:IfcPositiveInteger_762 ;
        list:hasNext      inst:IfcPositiveInteger_List_773 .

inst:IfcPositiveInteger_List_773
        list:hasContents  inst:IfcPositiveInteger_758 .

inst:IfcPositiveInteger_List_775
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_776
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_777
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_775
        list:hasContents  inst:IfcPositiveInteger_774 ;
        list:hasNext      inst:IfcPositiveInteger_List_776 .

inst:IfcPositiveInteger_778
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  67 .

inst:IfcPositiveInteger_List_776
        list:hasContents  inst:IfcPositiveInteger_778 ;
        list:hasNext      inst:IfcPositiveInteger_List_777 .

inst:IfcPositiveInteger_List_777
        list:hasContents  inst:IfcPositiveInteger_762 .

inst:IfcPositiveInteger_List_779
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_780
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_781
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_779
        list:hasContents  inst:IfcDimensionCount_238 ;
        list:hasNext      inst:IfcPositiveInteger_List_780 .

inst:IfcPositiveInteger_List_780
        list:hasContents  inst:IfcPositiveInteger_766 ;
        list:hasNext      inst:IfcPositiveInteger_List_781 .

inst:IfcPositiveInteger_782
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  33 .

inst:IfcPositiveInteger_List_781
        list:hasContents  inst:IfcPositiveInteger_782 .

inst:IfcPositiveInteger_List_783
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_784
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_785
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_783
        list:hasContents  inst:INTEGER_249 ;
        list:hasNext      inst:IfcPositiveInteger_List_784 .

inst:IfcPositiveInteger_786
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  17 .

inst:IfcPositiveInteger_List_784
        list:hasContents  inst:IfcPositiveInteger_786 ;
        list:hasNext      inst:IfcPositiveInteger_List_785 .

inst:IfcPositiveInteger_List_785
        list:hasContents  inst:IfcPositiveInteger_770 .

inst:IfcPositiveInteger_List_787
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_788
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_789
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_790
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  51 .

inst:IfcPositiveInteger_List_787
        list:hasContents  inst:IfcPositiveInteger_790 ;
        list:hasNext      inst:IfcPositiveInteger_List_788 .

inst:IfcPositiveInteger_List_788
        list:hasContents  inst:IfcPositiveInteger_778 ;
        list:hasNext      inst:IfcPositiveInteger_List_789 .

inst:IfcPositiveInteger_List_789
        list:hasContents  inst:IfcPositiveInteger_774 .

inst:IfcPositiveInteger_List_791
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_792
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_793
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_791
        list:hasContents  inst:IfcPositiveInteger_790 ;
        list:hasNext      inst:IfcPositiveInteger_List_792 .

inst:IfcPositiveInteger_794
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  68 .

inst:IfcPositiveInteger_List_792
        list:hasContents  inst:IfcPositiveInteger_794 ;
        list:hasNext      inst:IfcPositiveInteger_List_793 .

inst:IfcPositiveInteger_List_793
        list:hasContents  inst:IfcPositiveInteger_778 .

inst:IfcPositiveInteger_List_795
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_796
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_797
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_795
        list:hasContents  inst:IfcDimensionCount_238 ;
        list:hasNext      inst:IfcPositiveInteger_List_796 .

inst:IfcPositiveInteger_List_796
        list:hasContents  inst:IfcPositiveInteger_782 ;
        list:hasNext      inst:IfcPositiveInteger_List_797 .

inst:IfcPositiveInteger_798
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  34 .

inst:IfcPositiveInteger_List_797
        list:hasContents  inst:IfcPositiveInteger_798 .

inst:IfcPositiveInteger_List_799
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_800
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_801
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_799
        list:hasContents  inst:INTEGER_249 ;
        list:hasNext      inst:IfcPositiveInteger_List_800 .

inst:IfcPositiveInteger_802
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  18 .

inst:IfcPositiveInteger_List_800
        list:hasContents  inst:IfcPositiveInteger_802 ;
        list:hasNext      inst:IfcPositiveInteger_List_801 .

inst:IfcPositiveInteger_List_801
        list:hasContents  inst:IfcPositiveInteger_786 .

inst:IfcPositiveInteger_List_803
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_804
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_805
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_806
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  52 .

inst:IfcPositiveInteger_List_803
        list:hasContents  inst:IfcPositiveInteger_806 ;
        list:hasNext      inst:IfcPositiveInteger_List_804 .

inst:IfcPositiveInteger_List_804
        list:hasContents  inst:IfcPositiveInteger_794 ;
        list:hasNext      inst:IfcPositiveInteger_List_805 .

inst:IfcPositiveInteger_List_805
        list:hasContents  inst:IfcPositiveInteger_790 .

inst:IfcPositiveInteger_List_807
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_808
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_809
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_807
        list:hasContents  inst:IfcPositiveInteger_806 ;
        list:hasNext      inst:IfcPositiveInteger_List_808 .

inst:IfcPositiveInteger_810
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  69 .

inst:IfcPositiveInteger_List_808
        list:hasContents  inst:IfcPositiveInteger_810 ;
        list:hasNext      inst:IfcPositiveInteger_List_809 .

inst:IfcPositiveInteger_List_809
        list:hasContents  inst:IfcPositiveInteger_794 .

inst:IfcPositiveInteger_List_811
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_812
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_813
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_811
        list:hasContents  inst:IfcDimensionCount_238 ;
        list:hasNext      inst:IfcPositiveInteger_List_812 .

inst:IfcPositiveInteger_List_812
        list:hasContents  inst:IfcPositiveInteger_798 ;
        list:hasNext      inst:IfcPositiveInteger_List_813 .

inst:IfcPositiveInteger_814
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  35 .

inst:IfcPositiveInteger_List_813
        list:hasContents  inst:IfcPositiveInteger_814 .

inst:IfcPositiveInteger_List_815
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_816
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_817
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_815
        list:hasContents  inst:INTEGER_249 ;
        list:hasNext      inst:IfcPositiveInteger_List_816 .

inst:IfcPositiveInteger_818
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  19 .

inst:IfcPositiveInteger_List_816
        list:hasContents  inst:IfcPositiveInteger_818 ;
        list:hasNext      inst:IfcPositiveInteger_List_817 .

inst:IfcPositiveInteger_List_817
        list:hasContents  inst:IfcPositiveInteger_802 .

inst:IfcPositiveInteger_List_819
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_820
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_821
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_822
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  53 .

inst:IfcPositiveInteger_List_819
        list:hasContents  inst:IfcPositiveInteger_822 ;
        list:hasNext      inst:IfcPositiveInteger_List_820 .

inst:IfcPositiveInteger_List_820
        list:hasContents  inst:IfcPositiveInteger_810 ;
        list:hasNext      inst:IfcPositiveInteger_List_821 .

inst:IfcPositiveInteger_List_821
        list:hasContents  inst:IfcPositiveInteger_806 .

inst:IfcPositiveInteger_List_823
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_824
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_825
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_823
        list:hasContents  inst:IfcPositiveInteger_822 ;
        list:hasNext      inst:IfcPositiveInteger_List_824 .

inst:IfcPositiveInteger_826
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  70 .

inst:IfcPositiveInteger_List_824
        list:hasContents  inst:IfcPositiveInteger_826 ;
        list:hasNext      inst:IfcPositiveInteger_List_825 .

inst:IfcPositiveInteger_List_825
        list:hasContents  inst:IfcPositiveInteger_810 .

inst:IfcPositiveInteger_List_827
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_828
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_829
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_827
        list:hasContents  inst:IfcDimensionCount_238 ;
        list:hasNext      inst:IfcPositiveInteger_List_828 .

inst:IfcPositiveInteger_List_828
        list:hasContents  inst:IfcPositiveInteger_814 ;
        list:hasNext      inst:IfcPositiveInteger_List_829 .

inst:IfcPositiveInteger_830
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  36 .

inst:IfcPositiveInteger_List_829
        list:hasContents  inst:IfcPositiveInteger_830 .

inst:IfcPositiveInteger_List_List_831
        rdf:type  ifc:IfcPositiveInteger_List_List .

inst:IfcTriangulatedFaceSet_210
        ifc:coordIndex_IfcTriangulatedFaceSet  inst:IfcPositiveInteger_List_List_831 .

inst:IfcPositiveInteger_List_List_831
        list:hasContents  inst:IfcPositiveInteger_List_575 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_832 .

inst:IfcPositiveInteger_List_List_832
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_578 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_833 .

inst:IfcPositiveInteger_List_List_833
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_584 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_834 .

inst:IfcPositiveInteger_List_List_834
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_588 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_835 .

inst:IfcPositiveInteger_List_List_835
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_593 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_836 .

inst:IfcPositiveInteger_List_List_836
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_596 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_837 .

inst:IfcPositiveInteger_List_List_837
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_600 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_838 .

inst:IfcPositiveInteger_List_List_838
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_604 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_839 .

inst:IfcPositiveInteger_List_List_839
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_608 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_840 .

inst:IfcPositiveInteger_List_List_840
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_611 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_841 .

inst:IfcPositiveInteger_List_List_841
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_615 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_842 .

inst:IfcPositiveInteger_List_List_842
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_619 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_843 .

inst:IfcPositiveInteger_List_List_843
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_623 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_844 .

inst:IfcPositiveInteger_List_List_844
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_627 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_845 .

inst:IfcPositiveInteger_List_List_845
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_631 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_846 .

inst:IfcPositiveInteger_List_List_846
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_635 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_847 .

inst:IfcPositiveInteger_List_List_847
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_639 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_848 .

inst:IfcPositiveInteger_List_List_848
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_643 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_849 .

inst:IfcPositiveInteger_List_List_849
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_647 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_850 .

inst:IfcPositiveInteger_List_List_850
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_651 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_851 .

inst:IfcPositiveInteger_List_List_851
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_655 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_852 .

inst:IfcPositiveInteger_List_List_852
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_659 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_853 .

inst:IfcPositiveInteger_List_List_853
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_663 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_854 .

inst:IfcPositiveInteger_List_List_854
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_667 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_855 .

inst:IfcPositiveInteger_List_List_855
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_671 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_856 .

inst:IfcPositiveInteger_List_List_856
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_675 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_857 .

inst:IfcPositiveInteger_List_List_857
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_679 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_858 .

inst:IfcPositiveInteger_List_List_858
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_683 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_859 .

inst:IfcPositiveInteger_List_List_859
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_687 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_860 .

inst:IfcPositiveInteger_List_List_860
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_691 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_861 .

inst:IfcPositiveInteger_List_List_861
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_695 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_862 .

inst:IfcPositiveInteger_List_List_862
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_699 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_863 .

inst:IfcPositiveInteger_List_List_863
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_703 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_864 .

inst:IfcPositiveInteger_List_List_864
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_707 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_865 .

inst:IfcPositiveInteger_List_List_865
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_711 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_866 .

inst:IfcPositiveInteger_List_List_866
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_715 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_867 .

inst:IfcPositiveInteger_List_List_867
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_719 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_868 .

inst:IfcPositiveInteger_List_List_868
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_723 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_869 .

inst:IfcPositiveInteger_List_List_869
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_727 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_870 .

inst:IfcPositiveInteger_List_List_870
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_731 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_871 .

inst:IfcPositiveInteger_List_List_871
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_735 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_872 .

inst:IfcPositiveInteger_List_List_872
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_739 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_873 .

inst:IfcPositiveInteger_List_List_873
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_743 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_874 .

inst:IfcPositiveInteger_List_List_874
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_747 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_875 .

inst:IfcPositiveInteger_List_List_875
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_751 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_876 .

inst:IfcPositiveInteger_List_List_876
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_755 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_877 .

inst:IfcPositiveInteger_List_List_877
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_759 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_878 .

inst:IfcPositiveInteger_List_List_878
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_763 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_879 .

inst:IfcPositiveInteger_List_List_879
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_767 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_880 .

inst:IfcPositiveInteger_List_List_880
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_771 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_881 .

inst:IfcPositiveInteger_List_List_881
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_775 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_882 .

inst:IfcPositiveInteger_List_List_882
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_779 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_883 .

inst:IfcPositiveInteger_List_List_883
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_783 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_884 .

inst:IfcPositiveInteger_List_List_884
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_787 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_885 .

inst:IfcPositiveInteger_List_List_885
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_791 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_886 .

inst:IfcPositiveInteger_List_List_886
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_795 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_887 .

inst:IfcPositiveInteger_List_List_887
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_799 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_888 .

inst:IfcPositiveInteger_List_List_888
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_803 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_889 .

inst:IfcPositiveInteger_List_List_889
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_807 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_890 .

inst:IfcPositiveInteger_List_List_890
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_811 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_891 .

inst:IfcPositiveInteger_List_List_891
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_815 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_892 .

inst:IfcPositiveInteger_List_List_892
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_819 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_893 .

inst:IfcPositiveInteger_List_List_893
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_823 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_894 .

inst:IfcPositiveInteger_List_List_894
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_827 .

inst:IfcPositiveInteger_List_895
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_896
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_897
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_895
        list:hasContents  inst:INTEGER_249 ;
        list:hasNext      inst:IfcPositiveInteger_List_896 .

inst:IfcPositiveInteger_List_896
        list:hasContents  inst:INTEGER_251 ;
        list:hasNext      inst:IfcPositiveInteger_List_897 .

inst:IfcPositiveInteger_List_897
        list:hasContents  inst:IfcDimensionCount_234 .

inst:IfcPositiveInteger_List_898
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_899
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_900
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_898
        list:hasContents  inst:IfcPositiveInteger_581 ;
        list:hasNext      inst:IfcPositiveInteger_List_899 .

inst:IfcPositiveInteger_List_899
        list:hasContents  inst:IfcPositiveInteger_582 ;
        list:hasNext      inst:IfcPositiveInteger_List_900 .

inst:IfcPositiveInteger_List_900
        list:hasContents  inst:IfcPositiveInteger_583 .

inst:IfcPositiveInteger_List_901
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_902
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_903
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_901
        list:hasContents  inst:IfcPositiveInteger_581 ;
        list:hasNext      inst:IfcPositiveInteger_List_902 .

inst:IfcPositiveInteger_List_902
        list:hasContents  inst:IfcPositiveInteger_587 ;
        list:hasNext      inst:IfcPositiveInteger_List_903 .

inst:IfcPositiveInteger_List_903
        list:hasContents  inst:IfcPositiveInteger_582 .

inst:IfcPositiveInteger_List_904
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_905
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_906
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_904
        list:hasContents  inst:IfcDimensionCount_238 ;
        list:hasNext      inst:IfcPositiveInteger_List_905 .

inst:IfcPositiveInteger_List_905
        list:hasContents  inst:IfcPositiveInteger_591 ;
        list:hasNext      inst:IfcPositiveInteger_List_906 .

inst:IfcPositiveInteger_List_906
        list:hasContents  inst:IfcPositiveInteger_592 .

inst:IfcPositiveInteger_List_907
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_908
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_909
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_907
        list:hasContents  inst:INTEGER_249 ;
        list:hasNext      inst:IfcPositiveInteger_List_908 .

inst:IfcPositiveInteger_List_908
        list:hasContents  inst:INTEGER_252 ;
        list:hasNext      inst:IfcPositiveInteger_List_909 .

inst:IfcPositiveInteger_List_909
        list:hasContents  inst:INTEGER_251 .

inst:IfcPositiveInteger_List_910
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_911
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_912
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_910
        list:hasContents  inst:IfcPositiveInteger_599 ;
        list:hasNext      inst:IfcPositiveInteger_List_911 .

inst:IfcPositiveInteger_List_911
        list:hasContents  inst:IfcPositiveInteger_587 ;
        list:hasNext      inst:IfcPositiveInteger_List_912 .

inst:IfcPositiveInteger_List_912
        list:hasContents  inst:IfcPositiveInteger_581 .

inst:IfcPositiveInteger_List_913
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_914
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_915
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_913
        list:hasContents  inst:IfcPositiveInteger_599 ;
        list:hasNext      inst:IfcPositiveInteger_List_914 .

inst:IfcPositiveInteger_List_914
        list:hasContents  inst:IfcPositiveInteger_603 ;
        list:hasNext      inst:IfcPositiveInteger_List_915 .

inst:IfcPositiveInteger_List_915
        list:hasContents  inst:IfcPositiveInteger_587 .

inst:IfcPositiveInteger_List_916
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_917
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_918
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_916
        list:hasContents  inst:IfcDimensionCount_238 ;
        list:hasNext      inst:IfcPositiveInteger_List_917 .

inst:IfcPositiveInteger_List_917
        list:hasContents  inst:IfcPositiveInteger_592 ;
        list:hasNext      inst:IfcPositiveInteger_List_918 .

inst:IfcPositiveInteger_List_918
        list:hasContents  inst:IfcPositiveInteger_607 .

inst:IfcPositiveInteger_List_919
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_920
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_921
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_919
        list:hasContents  inst:INTEGER_249 ;
        list:hasNext      inst:IfcPositiveInteger_List_920 .

inst:IfcPositiveInteger_List_920
        list:hasContents  inst:INTEGER_253 ;
        list:hasNext      inst:IfcPositiveInteger_List_921 .

inst:IfcPositiveInteger_List_921
        list:hasContents  inst:INTEGER_252 .

inst:IfcPositiveInteger_List_922
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_923
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_924
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_922
        list:hasContents  inst:IfcPositiveInteger_614 ;
        list:hasNext      inst:IfcPositiveInteger_List_923 .

inst:IfcPositiveInteger_List_923
        list:hasContents  inst:IfcPositiveInteger_603 ;
        list:hasNext      inst:IfcPositiveInteger_List_924 .

inst:IfcPositiveInteger_List_924
        list:hasContents  inst:IfcPositiveInteger_599 .

inst:IfcPositiveInteger_List_925
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_926
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_927
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_925
        list:hasContents  inst:IfcPositiveInteger_614 ;
        list:hasNext      inst:IfcPositiveInteger_List_926 .

inst:IfcPositiveInteger_List_926
        list:hasContents  inst:IfcPositiveInteger_618 ;
        list:hasNext      inst:IfcPositiveInteger_List_927 .

inst:IfcPositiveInteger_List_927
        list:hasContents  inst:IfcPositiveInteger_603 .

inst:IfcPositiveInteger_List_928
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_929
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_930
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_928
        list:hasContents  inst:IfcDimensionCount_238 ;
        list:hasNext      inst:IfcPositiveInteger_List_929 .

inst:IfcPositiveInteger_List_929
        list:hasContents  inst:IfcPositiveInteger_607 ;
        list:hasNext      inst:IfcPositiveInteger_List_930 .

inst:IfcPositiveInteger_List_930
        list:hasContents  inst:IfcPositiveInteger_622 .

inst:IfcPositiveInteger_List_931
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_932
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_933
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_931
        list:hasContents  inst:INTEGER_249 ;
        list:hasNext      inst:IfcPositiveInteger_List_932 .

inst:IfcPositiveInteger_List_932
        list:hasContents  inst:IfcPositiveInteger_626 ;
        list:hasNext      inst:IfcPositiveInteger_List_933 .

inst:IfcPositiveInteger_List_933
        list:hasContents  inst:INTEGER_253 .

inst:IfcPositiveInteger_List_934
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_935
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_936
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_934
        list:hasContents  inst:IfcPositiveInteger_630 ;
        list:hasNext      inst:IfcPositiveInteger_List_935 .

inst:IfcPositiveInteger_List_935
        list:hasContents  inst:IfcPositiveInteger_618 ;
        list:hasNext      inst:IfcPositiveInteger_List_936 .

inst:IfcPositiveInteger_List_936
        list:hasContents  inst:IfcPositiveInteger_614 .

inst:IfcPositiveInteger_List_937
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_938
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_939
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_937
        list:hasContents  inst:IfcPositiveInteger_630 ;
        list:hasNext      inst:IfcPositiveInteger_List_938 .

inst:IfcPositiveInteger_List_938
        list:hasContents  inst:IfcPositiveInteger_634 ;
        list:hasNext      inst:IfcPositiveInteger_List_939 .

inst:IfcPositiveInteger_List_939
        list:hasContents  inst:IfcPositiveInteger_618 .

inst:IfcPositiveInteger_List_940
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_941
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_942
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_940
        list:hasContents  inst:IfcDimensionCount_238 ;
        list:hasNext      inst:IfcPositiveInteger_List_941 .

inst:IfcPositiveInteger_List_941
        list:hasContents  inst:IfcPositiveInteger_622 ;
        list:hasNext      inst:IfcPositiveInteger_List_942 .

inst:IfcPositiveInteger_List_942
        list:hasContents  inst:IfcPositiveInteger_638 .

inst:IfcPositiveInteger_List_943
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_944
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_945
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_943
        list:hasContents  inst:INTEGER_249 ;
        list:hasNext      inst:IfcPositiveInteger_List_944 .

inst:IfcPositiveInteger_List_944
        list:hasContents  inst:IfcPositiveInteger_642 ;
        list:hasNext      inst:IfcPositiveInteger_List_945 .

inst:IfcPositiveInteger_List_945
        list:hasContents  inst:IfcPositiveInteger_626 .

inst:IfcPositiveInteger_List_946
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_947
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_948
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_946
        list:hasContents  inst:IfcPositiveInteger_646 ;
        list:hasNext      inst:IfcPositiveInteger_List_947 .

inst:IfcPositiveInteger_List_947
        list:hasContents  inst:IfcPositiveInteger_634 ;
        list:hasNext      inst:IfcPositiveInteger_List_948 .

inst:IfcPositiveInteger_List_948
        list:hasContents  inst:IfcPositiveInteger_630 .

inst:IfcPositiveInteger_List_949
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_950
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_951
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_949
        list:hasContents  inst:IfcPositiveInteger_646 ;
        list:hasNext      inst:IfcPositiveInteger_List_950 .

inst:IfcPositiveInteger_List_950
        list:hasContents  inst:IfcPositiveInteger_650 ;
        list:hasNext      inst:IfcPositiveInteger_List_951 .

inst:IfcPositiveInteger_List_951
        list:hasContents  inst:IfcPositiveInteger_634 .

inst:IfcPositiveInteger_List_952
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_953
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_954
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_952
        list:hasContents  inst:IfcDimensionCount_238 ;
        list:hasNext      inst:IfcPositiveInteger_List_953 .

inst:IfcPositiveInteger_List_953
        list:hasContents  inst:IfcPositiveInteger_638 ;
        list:hasNext      inst:IfcPositiveInteger_List_954 .

inst:IfcPositiveInteger_List_954
        list:hasContents  inst:IfcPositiveInteger_654 .

inst:IfcPositiveInteger_List_955
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_956
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_957
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_955
        list:hasContents  inst:INTEGER_249 ;
        list:hasNext      inst:IfcPositiveInteger_List_956 .

inst:IfcPositiveInteger_List_956
        list:hasContents  inst:IfcPositiveInteger_658 ;
        list:hasNext      inst:IfcPositiveInteger_List_957 .

inst:IfcPositiveInteger_List_957
        list:hasContents  inst:IfcPositiveInteger_642 .

inst:IfcPositiveInteger_List_958
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_959
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_960
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_958
        list:hasContents  inst:IfcPositiveInteger_662 ;
        list:hasNext      inst:IfcPositiveInteger_List_959 .

inst:IfcPositiveInteger_List_959
        list:hasContents  inst:IfcPositiveInteger_650 ;
        list:hasNext      inst:IfcPositiveInteger_List_960 .

inst:IfcPositiveInteger_List_960
        list:hasContents  inst:IfcPositiveInteger_646 .

inst:IfcPositiveInteger_List_961
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_962
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_963
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_961
        list:hasContents  inst:IfcPositiveInteger_662 ;
        list:hasNext      inst:IfcPositiveInteger_List_962 .

inst:IfcPositiveInteger_List_962
        list:hasContents  inst:IfcPositiveInteger_666 ;
        list:hasNext      inst:IfcPositiveInteger_List_963 .

inst:IfcPositiveInteger_List_963
        list:hasContents  inst:IfcPositiveInteger_650 .

inst:IfcPositiveInteger_List_964
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_965
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_966
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_964
        list:hasContents  inst:IfcDimensionCount_238 ;
        list:hasNext      inst:IfcPositiveInteger_List_965 .

inst:IfcPositiveInteger_List_965
        list:hasContents  inst:IfcPositiveInteger_654 ;
        list:hasNext      inst:IfcPositiveInteger_List_966 .

inst:IfcPositiveInteger_List_966
        list:hasContents  inst:IfcPositiveInteger_670 .

inst:IfcPositiveInteger_List_967
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_968
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_969
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_967
        list:hasContents  inst:INTEGER_249 ;
        list:hasNext      inst:IfcPositiveInteger_List_968 .

inst:IfcPositiveInteger_List_968
        list:hasContents  inst:IfcPositiveInteger_674 ;
        list:hasNext      inst:IfcPositiveInteger_List_969 .

inst:IfcPositiveInteger_List_969
        list:hasContents  inst:IfcPositiveInteger_658 .

inst:IfcPositiveInteger_List_970
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_971
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_972
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_970
        list:hasContents  inst:IfcPositiveInteger_678 ;
        list:hasNext      inst:IfcPositiveInteger_List_971 .

inst:IfcPositiveInteger_List_971
        list:hasContents  inst:IfcPositiveInteger_666 ;
        list:hasNext      inst:IfcPositiveInteger_List_972 .

inst:IfcPositiveInteger_List_972
        list:hasContents  inst:IfcPositiveInteger_662 .

inst:IfcPositiveInteger_List_973
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_974
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_975
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_973
        list:hasContents  inst:IfcPositiveInteger_678 ;
        list:hasNext      inst:IfcPositiveInteger_List_974 .

inst:IfcPositiveInteger_List_974
        list:hasContents  inst:IfcPositiveInteger_682 ;
        list:hasNext      inst:IfcPositiveInteger_List_975 .

inst:IfcPositiveInteger_List_975
        list:hasContents  inst:IfcPositiveInteger_666 .

inst:IfcPositiveInteger_List_976
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_977
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_978
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_976
        list:hasContents  inst:IfcDimensionCount_238 ;
        list:hasNext      inst:IfcPositiveInteger_List_977 .

inst:IfcPositiveInteger_List_977
        list:hasContents  inst:IfcPositiveInteger_670 ;
        list:hasNext      inst:IfcPositiveInteger_List_978 .

inst:IfcPositiveInteger_List_978
        list:hasContents  inst:IfcPositiveInteger_686 .

inst:IfcPositiveInteger_List_979
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_980
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_981
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_979
        list:hasContents  inst:INTEGER_249 ;
        list:hasNext      inst:IfcPositiveInteger_List_980 .

inst:IfcPositiveInteger_List_980
        list:hasContents  inst:IfcPositiveInteger_690 ;
        list:hasNext      inst:IfcPositiveInteger_List_981 .

inst:IfcPositiveInteger_List_981
        list:hasContents  inst:IfcPositiveInteger_674 .

inst:IfcPositiveInteger_List_982
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_983
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_984
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_982
        list:hasContents  inst:IfcPositiveInteger_694 ;
        list:hasNext      inst:IfcPositiveInteger_List_983 .

inst:IfcPositiveInteger_List_983
        list:hasContents  inst:IfcPositiveInteger_682 ;
        list:hasNext      inst:IfcPositiveInteger_List_984 .

inst:IfcPositiveInteger_List_984
        list:hasContents  inst:IfcPositiveInteger_678 .

inst:IfcPositiveInteger_List_985
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_986
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_987
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_985
        list:hasContents  inst:IfcPositiveInteger_694 ;
        list:hasNext      inst:IfcPositiveInteger_List_986 .

inst:IfcPositiveInteger_List_986
        list:hasContents  inst:IfcPositiveInteger_698 ;
        list:hasNext      inst:IfcPositiveInteger_List_987 .

inst:IfcPositiveInteger_List_987
        list:hasContents  inst:IfcPositiveInteger_682 .

inst:IfcPositiveInteger_List_988
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_989
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_990
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_988
        list:hasContents  inst:IfcDimensionCount_238 ;
        list:hasNext      inst:IfcPositiveInteger_List_989 .

inst:IfcPositiveInteger_List_989
        list:hasContents  inst:IfcPositiveInteger_686 ;
        list:hasNext      inst:IfcPositiveInteger_List_990 .

inst:IfcPositiveInteger_List_990
        list:hasContents  inst:IfcPositiveInteger_702 .

inst:IfcPositiveInteger_List_991
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_992
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_993
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_991
        list:hasContents  inst:INTEGER_249 ;
        list:hasNext      inst:IfcPositiveInteger_List_992 .

inst:IfcPositiveInteger_List_992
        list:hasContents  inst:IfcPositiveInteger_706 ;
        list:hasNext      inst:IfcPositiveInteger_List_993 .

inst:IfcPositiveInteger_List_993
        list:hasContents  inst:IfcPositiveInteger_690 .

inst:IfcPositiveInteger_List_994
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_995
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_996
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_994
        list:hasContents  inst:IfcPositiveInteger_710 ;
        list:hasNext      inst:IfcPositiveInteger_List_995 .

inst:IfcPositiveInteger_List_995
        list:hasContents  inst:IfcPositiveInteger_698 ;
        list:hasNext      inst:IfcPositiveInteger_List_996 .

inst:IfcPositiveInteger_List_996
        list:hasContents  inst:IfcPositiveInteger_694 .

inst:IfcPositiveInteger_List_997
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_998
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_999
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_997
        list:hasContents  inst:IfcPositiveInteger_710 ;
        list:hasNext      inst:IfcPositiveInteger_List_998 .

inst:IfcPositiveInteger_List_998
        list:hasContents  inst:IfcPositiveInteger_714 ;
        list:hasNext      inst:IfcPositiveInteger_List_999 .

inst:IfcPositiveInteger_List_999
        list:hasContents  inst:IfcPositiveInteger_698 .

inst:IfcPositiveInteger_List_1000
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1001
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1002
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1000
        list:hasContents  inst:IfcDimensionCount_238 ;
        list:hasNext      inst:IfcPositiveInteger_List_1001 .

inst:IfcPositiveInteger_List_1001
        list:hasContents  inst:IfcPositiveInteger_702 ;
        list:hasNext      inst:IfcPositiveInteger_List_1002 .

inst:IfcPositiveInteger_List_1002
        list:hasContents  inst:IfcPositiveInteger_718 .

inst:IfcPositiveInteger_List_1003
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1004
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1005
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1003
        list:hasContents  inst:INTEGER_249 ;
        list:hasNext      inst:IfcPositiveInteger_List_1004 .

inst:IfcPositiveInteger_List_1004
        list:hasContents  inst:IfcPositiveInteger_722 ;
        list:hasNext      inst:IfcPositiveInteger_List_1005 .

inst:IfcPositiveInteger_List_1005
        list:hasContents  inst:IfcPositiveInteger_706 .

inst:IfcPositiveInteger_List_1006
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1007
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1008
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1006
        list:hasContents  inst:IfcPositiveInteger_726 ;
        list:hasNext      inst:IfcPositiveInteger_List_1007 .

inst:IfcPositiveInteger_List_1007
        list:hasContents  inst:IfcPositiveInteger_714 ;
        list:hasNext      inst:IfcPositiveInteger_List_1008 .

inst:IfcPositiveInteger_List_1008
        list:hasContents  inst:IfcPositiveInteger_710 .

inst:IfcPositiveInteger_List_1009
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1010
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1011
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1009
        list:hasContents  inst:IfcPositiveInteger_726 ;
        list:hasNext      inst:IfcPositiveInteger_List_1010 .

inst:IfcPositiveInteger_List_1010
        list:hasContents  inst:IfcPositiveInteger_730 ;
        list:hasNext      inst:IfcPositiveInteger_List_1011 .

inst:IfcPositiveInteger_List_1011
        list:hasContents  inst:IfcPositiveInteger_714 .

inst:IfcPositiveInteger_List_1012
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1013
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1014
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1012
        list:hasContents  inst:IfcDimensionCount_238 ;
        list:hasNext      inst:IfcPositiveInteger_List_1013 .

inst:IfcPositiveInteger_List_1013
        list:hasContents  inst:IfcPositiveInteger_718 ;
        list:hasNext      inst:IfcPositiveInteger_List_1014 .

inst:IfcPositiveInteger_List_1014
        list:hasContents  inst:IfcPositiveInteger_734 .

inst:IfcPositiveInteger_List_1015
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1016
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1017
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1015
        list:hasContents  inst:INTEGER_249 ;
        list:hasNext      inst:IfcPositiveInteger_List_1016 .

inst:IfcPositiveInteger_List_1016
        list:hasContents  inst:IfcPositiveInteger_738 ;
        list:hasNext      inst:IfcPositiveInteger_List_1017 .

inst:IfcPositiveInteger_List_1017
        list:hasContents  inst:IfcPositiveInteger_722 .

inst:IfcPositiveInteger_List_1018
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1019
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1020
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1018
        list:hasContents  inst:IfcPositiveInteger_742 ;
        list:hasNext      inst:IfcPositiveInteger_List_1019 .

inst:IfcPositiveInteger_List_1019
        list:hasContents  inst:IfcPositiveInteger_730 ;
        list:hasNext      inst:IfcPositiveInteger_List_1020 .

inst:IfcPositiveInteger_List_1020
        list:hasContents  inst:IfcPositiveInteger_726 .

inst:IfcPositiveInteger_List_1021
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1022
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1023
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1021
        list:hasContents  inst:IfcPositiveInteger_742 ;
        list:hasNext      inst:IfcPositiveInteger_List_1022 .

inst:IfcPositiveInteger_List_1022
        list:hasContents  inst:IfcPositiveInteger_746 ;
        list:hasNext      inst:IfcPositiveInteger_List_1023 .

inst:IfcPositiveInteger_List_1023
        list:hasContents  inst:IfcPositiveInteger_730 .

inst:IfcPositiveInteger_List_1024
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1025
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1026
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1024
        list:hasContents  inst:IfcDimensionCount_238 ;
        list:hasNext      inst:IfcPositiveInteger_List_1025 .

inst:IfcPositiveInteger_List_1025
        list:hasContents  inst:IfcPositiveInteger_734 ;
        list:hasNext      inst:IfcPositiveInteger_List_1026 .

inst:IfcPositiveInteger_List_1026
        list:hasContents  inst:IfcPositiveInteger_750 .

inst:IfcPositiveInteger_List_1027
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1028
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1029
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1027
        list:hasContents  inst:INTEGER_249 ;
        list:hasNext      inst:IfcPositiveInteger_List_1028 .

inst:IfcPositiveInteger_List_1028
        list:hasContents  inst:IfcPositiveInteger_754 ;
        list:hasNext      inst:IfcPositiveInteger_List_1029 .

inst:IfcPositiveInteger_List_1029
        list:hasContents  inst:IfcPositiveInteger_738 .

inst:IfcPositiveInteger_List_1030
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1031
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1032
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1030
        list:hasContents  inst:IfcPositiveInteger_758 ;
        list:hasNext      inst:IfcPositiveInteger_List_1031 .

inst:IfcPositiveInteger_List_1031
        list:hasContents  inst:IfcPositiveInteger_746 ;
        list:hasNext      inst:IfcPositiveInteger_List_1032 .

inst:IfcPositiveInteger_List_1032
        list:hasContents  inst:IfcPositiveInteger_742 .

inst:IfcPositiveInteger_List_1033
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1034
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1035
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1033
        list:hasContents  inst:IfcPositiveInteger_758 ;
        list:hasNext      inst:IfcPositiveInteger_List_1034 .

inst:IfcPositiveInteger_List_1034
        list:hasContents  inst:IfcPositiveInteger_762 ;
        list:hasNext      inst:IfcPositiveInteger_List_1035 .

inst:IfcPositiveInteger_List_1035
        list:hasContents  inst:IfcPositiveInteger_746 .

inst:IfcPositiveInteger_List_1036
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1037
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1038
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1036
        list:hasContents  inst:IfcDimensionCount_238 ;
        list:hasNext      inst:IfcPositiveInteger_List_1037 .

inst:IfcPositiveInteger_List_1037
        list:hasContents  inst:IfcPositiveInteger_750 ;
        list:hasNext      inst:IfcPositiveInteger_List_1038 .

inst:IfcPositiveInteger_List_1038
        list:hasContents  inst:IfcPositiveInteger_766 .

inst:IfcPositiveInteger_List_1039
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1040
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1041
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1039
        list:hasContents  inst:INTEGER_249 ;
        list:hasNext      inst:IfcPositiveInteger_List_1040 .

inst:IfcPositiveInteger_List_1040
        list:hasContents  inst:IfcPositiveInteger_770 ;
        list:hasNext      inst:IfcPositiveInteger_List_1041 .

inst:IfcPositiveInteger_List_1041
        list:hasContents  inst:IfcPositiveInteger_754 .

inst:IfcPositiveInteger_List_1042
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1043
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1044
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1042
        list:hasContents  inst:IfcPositiveInteger_774 ;
        list:hasNext      inst:IfcPositiveInteger_List_1043 .

inst:IfcPositiveInteger_List_1043
        list:hasContents  inst:IfcPositiveInteger_762 ;
        list:hasNext      inst:IfcPositiveInteger_List_1044 .

inst:IfcPositiveInteger_List_1044
        list:hasContents  inst:IfcPositiveInteger_758 .

inst:IfcPositiveInteger_List_1045
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1046
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1047
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1045
        list:hasContents  inst:IfcPositiveInteger_774 ;
        list:hasNext      inst:IfcPositiveInteger_List_1046 .

inst:IfcPositiveInteger_List_1046
        list:hasContents  inst:IfcPositiveInteger_778 ;
        list:hasNext      inst:IfcPositiveInteger_List_1047 .

inst:IfcPositiveInteger_List_1047
        list:hasContents  inst:IfcPositiveInteger_762 .

inst:IfcPositiveInteger_List_1048
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1049
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1050
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1048
        list:hasContents  inst:IfcDimensionCount_238 ;
        list:hasNext      inst:IfcPositiveInteger_List_1049 .

inst:IfcPositiveInteger_List_1049
        list:hasContents  inst:IfcPositiveInteger_766 ;
        list:hasNext      inst:IfcPositiveInteger_List_1050 .

inst:IfcPositiveInteger_List_1050
        list:hasContents  inst:IfcPositiveInteger_782 .

inst:IfcPositiveInteger_List_1051
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1052
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1053
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1051
        list:hasContents  inst:INTEGER_249 ;
        list:hasNext      inst:IfcPositiveInteger_List_1052 .

inst:IfcPositiveInteger_List_1052
        list:hasContents  inst:IfcPositiveInteger_786 ;
        list:hasNext      inst:IfcPositiveInteger_List_1053 .

inst:IfcPositiveInteger_List_1053
        list:hasContents  inst:IfcPositiveInteger_770 .

inst:IfcPositiveInteger_List_1054
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1055
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1056
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1054
        list:hasContents  inst:IfcPositiveInteger_790 ;
        list:hasNext      inst:IfcPositiveInteger_List_1055 .

inst:IfcPositiveInteger_List_1055
        list:hasContents  inst:IfcPositiveInteger_778 ;
        list:hasNext      inst:IfcPositiveInteger_List_1056 .

inst:IfcPositiveInteger_List_1056
        list:hasContents  inst:IfcPositiveInteger_774 .

inst:IfcPositiveInteger_List_1057
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1058
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1059
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1057
        list:hasContents  inst:IfcPositiveInteger_790 ;
        list:hasNext      inst:IfcPositiveInteger_List_1058 .

inst:IfcPositiveInteger_List_1058
        list:hasContents  inst:IfcPositiveInteger_794 ;
        list:hasNext      inst:IfcPositiveInteger_List_1059 .

inst:IfcPositiveInteger_List_1059
        list:hasContents  inst:IfcPositiveInteger_778 .

inst:IfcPositiveInteger_List_1060
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1061
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1062
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1060
        list:hasContents  inst:IfcDimensionCount_238 ;
        list:hasNext      inst:IfcPositiveInteger_List_1061 .

inst:IfcPositiveInteger_List_1061
        list:hasContents  inst:IfcPositiveInteger_782 ;
        list:hasNext      inst:IfcPositiveInteger_List_1062 .

inst:IfcPositiveInteger_List_1062
        list:hasContents  inst:IfcPositiveInteger_798 .

inst:IfcPositiveInteger_List_1063
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1064
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1065
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1063
        list:hasContents  inst:INTEGER_249 ;
        list:hasNext      inst:IfcPositiveInteger_List_1064 .

inst:IfcPositiveInteger_List_1064
        list:hasContents  inst:IfcPositiveInteger_802 ;
        list:hasNext      inst:IfcPositiveInteger_List_1065 .

inst:IfcPositiveInteger_List_1065
        list:hasContents  inst:IfcPositiveInteger_786 .

inst:IfcPositiveInteger_List_1066
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1067
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1068
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1066
        list:hasContents  inst:IfcPositiveInteger_806 ;
        list:hasNext      inst:IfcPositiveInteger_List_1067 .

inst:IfcPositiveInteger_List_1067
        list:hasContents  inst:IfcPositiveInteger_794 ;
        list:hasNext      inst:IfcPositiveInteger_List_1068 .

inst:IfcPositiveInteger_List_1068
        list:hasContents  inst:IfcPositiveInteger_790 .

inst:IfcPositiveInteger_List_1069
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1070
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1071
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1069
        list:hasContents  inst:IfcPositiveInteger_806 ;
        list:hasNext      inst:IfcPositiveInteger_List_1070 .

inst:IfcPositiveInteger_List_1070
        list:hasContents  inst:IfcPositiveInteger_810 ;
        list:hasNext      inst:IfcPositiveInteger_List_1071 .

inst:IfcPositiveInteger_List_1071
        list:hasContents  inst:IfcPositiveInteger_794 .

inst:IfcPositiveInteger_List_1072
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1073
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1074
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1072
        list:hasContents  inst:IfcDimensionCount_238 ;
        list:hasNext      inst:IfcPositiveInteger_List_1073 .

inst:IfcPositiveInteger_List_1073
        list:hasContents  inst:IfcPositiveInteger_798 ;
        list:hasNext      inst:IfcPositiveInteger_List_1074 .

inst:IfcPositiveInteger_List_1074
        list:hasContents  inst:IfcPositiveInteger_814 .

inst:IfcPositiveInteger_List_1075
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1076
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1077
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1075
        list:hasContents  inst:INTEGER_249 ;
        list:hasNext      inst:IfcPositiveInteger_List_1076 .

inst:IfcPositiveInteger_List_1076
        list:hasContents  inst:IfcPositiveInteger_818 ;
        list:hasNext      inst:IfcPositiveInteger_List_1077 .

inst:IfcPositiveInteger_List_1077
        list:hasContents  inst:IfcPositiveInteger_802 .

inst:IfcPositiveInteger_List_1078
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1079
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1080
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1078
        list:hasContents  inst:IfcPositiveInteger_822 ;
        list:hasNext      inst:IfcPositiveInteger_List_1079 .

inst:IfcPositiveInteger_List_1079
        list:hasContents  inst:IfcPositiveInteger_810 ;
        list:hasNext      inst:IfcPositiveInteger_List_1080 .

inst:IfcPositiveInteger_List_1080
        list:hasContents  inst:IfcPositiveInteger_806 .

inst:IfcPositiveInteger_List_1081
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1082
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1083
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1081
        list:hasContents  inst:IfcPositiveInteger_822 ;
        list:hasNext      inst:IfcPositiveInteger_List_1082 .

inst:IfcPositiveInteger_List_1082
        list:hasContents  inst:IfcPositiveInteger_826 ;
        list:hasNext      inst:IfcPositiveInteger_List_1083 .

inst:IfcPositiveInteger_List_1083
        list:hasContents  inst:IfcPositiveInteger_810 .

inst:IfcPositiveInteger_List_1084
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1085
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1086
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1084
        list:hasContents  inst:IfcDimensionCount_238 ;
        list:hasNext      inst:IfcPositiveInteger_List_1085 .

inst:IfcPositiveInteger_List_1085
        list:hasContents  inst:IfcPositiveInteger_814 ;
        list:hasNext      inst:IfcPositiveInteger_List_1086 .

inst:IfcPositiveInteger_List_1086
        list:hasContents  inst:IfcPositiveInteger_830 .

inst:IfcPositiveInteger_List_List_1087
        rdf:type  ifc:IfcPositiveInteger_List_List .

inst:IfcTriangulatedFaceSet_210
        ifc:normalIndex_IfcTriangulatedFaceSet  inst:IfcPositiveInteger_List_List_1087 .

inst:IfcPositiveInteger_List_List_1087
        list:hasContents  inst:IfcPositiveInteger_List_895 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_1088 .

inst:IfcPositiveInteger_List_List_1088
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_898 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_1089 .

inst:IfcPositiveInteger_List_List_1089
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_901 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_1090 .

inst:IfcPositiveInteger_List_List_1090
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_904 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_1091 .

inst:IfcPositiveInteger_List_List_1091
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_907 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_1092 .

inst:IfcPositiveInteger_List_List_1092
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_910 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_1093 .

inst:IfcPositiveInteger_List_List_1093
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_913 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_1094 .

inst:IfcPositiveInteger_List_List_1094
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_916 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_1095 .

inst:IfcPositiveInteger_List_List_1095
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_919 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_1096 .

inst:IfcPositiveInteger_List_List_1096
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_922 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_1097 .

inst:IfcPositiveInteger_List_List_1097
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_925 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_1098 .

inst:IfcPositiveInteger_List_List_1098
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_928 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_1099 .

inst:IfcPositiveInteger_List_List_1099
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_931 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_1100 .

inst:IfcPositiveInteger_List_List_1100
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_934 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_1101 .

inst:IfcPositiveInteger_List_List_1101
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_937 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_1102 .

inst:IfcPositiveInteger_List_List_1102
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_940 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_1103 .

inst:IfcPositiveInteger_List_List_1103
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_943 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_1104 .

inst:IfcPositiveInteger_List_List_1104
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_946 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_1105 .

inst:IfcPositiveInteger_List_List_1105
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_949 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_1106 .

inst:IfcPositiveInteger_List_List_1106
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_952 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_1107 .

inst:IfcPositiveInteger_List_List_1107
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_955 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_1108 .

inst:IfcPositiveInteger_List_List_1108
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_958 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_1109 .

inst:IfcPositiveInteger_List_List_1109
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_961 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_1110 .

inst:IfcPositiveInteger_List_List_1110
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_964 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_1111 .

inst:IfcPositiveInteger_List_List_1111
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_967 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_1112 .

inst:IfcPositiveInteger_List_List_1112
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_970 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_1113 .

inst:IfcPositiveInteger_List_List_1113
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_973 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_1114 .

inst:IfcPositiveInteger_List_List_1114
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_976 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_1115 .

inst:IfcPositiveInteger_List_List_1115
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_979 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_1116 .

inst:IfcPositiveInteger_List_List_1116
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_982 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_1117 .

inst:IfcPositiveInteger_List_List_1117
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_985 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_1118 .

inst:IfcPositiveInteger_List_List_1118
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_988 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_1119 .

inst:IfcPositiveInteger_List_List_1119
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_991 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_1120 .

inst:IfcPositiveInteger_List_List_1120
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_994 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_1121 .

inst:IfcPositiveInteger_List_List_1121
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_997 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_1122 .

inst:IfcPositiveInteger_List_List_1122
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1000 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_1123 .

inst:IfcPositiveInteger_List_List_1123
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1003 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_1124 .

inst:IfcPositiveInteger_List_List_1124
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1006 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_1125 .

inst:IfcPositiveInteger_List_List_1125
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1009 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_1126 .

inst:IfcPositiveInteger_List_List_1126
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1012 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_1127 .

inst:IfcPositiveInteger_List_List_1127
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1015 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_1128 .

inst:IfcPositiveInteger_List_List_1128
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1018 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_1129 .

inst:IfcPositiveInteger_List_List_1129
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1021 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_1130 .

inst:IfcPositiveInteger_List_List_1130
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1024 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_1131 .

inst:IfcPositiveInteger_List_List_1131
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1027 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_1132 .

inst:IfcPositiveInteger_List_List_1132
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1030 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_1133 .

inst:IfcPositiveInteger_List_List_1133
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1033 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_1134 .

inst:IfcPositiveInteger_List_List_1134
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1036 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_1135 .

inst:IfcPositiveInteger_List_List_1135
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1039 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_1136 .

inst:IfcPositiveInteger_List_List_1136
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1042 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_1137 .

inst:IfcPositiveInteger_List_List_1137
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1045 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_1138 .

inst:IfcPositiveInteger_List_List_1138
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1048 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_1139 .

inst:IfcPositiveInteger_List_List_1139
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1051 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_1140 .

inst:IfcPositiveInteger_List_List_1140
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1054 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_1141 .

inst:IfcPositiveInteger_List_List_1141
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1057 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_1142 .

inst:IfcPositiveInteger_List_List_1142
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1060 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_1143 .

inst:IfcPositiveInteger_List_List_1143
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1063 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_1144 .

inst:IfcPositiveInteger_List_List_1144
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1066 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_1145 .

inst:IfcPositiveInteger_List_List_1145
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1069 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_1146 .

inst:IfcPositiveInteger_List_List_1146
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1072 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_1147 .

inst:IfcPositiveInteger_List_List_1147
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1075 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_1148 .

inst:IfcPositiveInteger_List_List_1148
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1078 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_1149 .

inst:IfcPositiveInteger_List_List_1149
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1081 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_1150 .

inst:IfcPositiveInteger_List_List_1150
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1084 .

inst:IfcLengthMeasure_List_1151
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1152
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1153
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1151
        list:hasContents  inst:IfcLengthMeasure_246 ;
        list:hasNext      inst:IfcLengthMeasure_List_1152 .

inst:IfcLengthMeasure_List_1152
        list:hasContents  inst:IfcLengthMeasure_246 ;
        list:hasNext      inst:IfcLengthMeasure_List_1153 .

inst:IfcLengthMeasure_List_1153
        list:hasContents  inst:IfcLengthMeasure_246 .

inst:IfcLengthMeasure_List_1154
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1155
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1156
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1154
        list:hasContents  inst:IfcLengthMeasure_246 ;
        list:hasNext      inst:IfcLengthMeasure_List_1155 .

inst:IfcLengthMeasure_List_1155
        list:hasContents  inst:IfcLengthMeasure_246 ;
        list:hasNext      inst:IfcLengthMeasure_List_1156 .

inst:IfcLengthMeasure_1157
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "4."^^xsd:double .

inst:IfcLengthMeasure_List_1156
        list:hasContents  inst:IfcLengthMeasure_1157 .

inst:IfcLengthMeasure_List_1158
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1159
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1160
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_1161
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "2."^^xsd:double .

inst:IfcLengthMeasure_List_1158
        list:hasContents  inst:IfcLengthMeasure_1161 ;
        list:hasNext      inst:IfcLengthMeasure_List_1159 .

inst:IfcLengthMeasure_List_1159
        list:hasContents  inst:IfcLengthMeasure_246 ;
        list:hasNext      inst:IfcLengthMeasure_List_1160 .

inst:IfcLengthMeasure_List_1160
        list:hasContents  inst:IfcLengthMeasure_246 .

inst:IfcLengthMeasure_List_1162
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1163
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1164
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_1165
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "1.84775906502257"^^xsd:double .

inst:IfcLengthMeasure_List_1162
        list:hasContents  inst:IfcLengthMeasure_1165 ;
        list:hasNext      inst:IfcLengthMeasure_List_1163 .

inst:IfcLengthMeasure_1166
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "0.76536686473018"^^xsd:double .

inst:IfcLengthMeasure_List_1163
        list:hasContents  inst:IfcLengthMeasure_1166 ;
        list:hasNext      inst:IfcLengthMeasure_List_1164 .

inst:IfcLengthMeasure_List_1164
        list:hasContents  inst:IfcLengthMeasure_246 .

inst:IfcLengthMeasure_List_1167
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1168
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1169
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_1170
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "1.4142135623731"^^xsd:double .

inst:IfcLengthMeasure_List_1167
        list:hasContents  inst:IfcLengthMeasure_1170 ;
        list:hasNext      inst:IfcLengthMeasure_List_1168 .

inst:IfcLengthMeasure_1171
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "1.41421356237309"^^xsd:double .

inst:IfcLengthMeasure_List_1168
        list:hasContents  inst:IfcLengthMeasure_1171 ;
        list:hasNext      inst:IfcLengthMeasure_List_1169 .

inst:IfcLengthMeasure_List_1169
        list:hasContents  inst:IfcLengthMeasure_246 .

inst:IfcLengthMeasure_List_1172
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1173
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1174
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1172
        list:hasContents  inst:IfcLengthMeasure_1166 ;
        list:hasNext      inst:IfcLengthMeasure_List_1173 .

inst:IfcLengthMeasure_List_1173
        list:hasContents  inst:IfcLengthMeasure_1165 ;
        list:hasNext      inst:IfcLengthMeasure_List_1174 .

inst:IfcLengthMeasure_List_1174
        list:hasContents  inst:IfcLengthMeasure_246 .

inst:IfcLengthMeasure_List_1175
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1176
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1177
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1175
        list:hasContents  inst:IfcParameterValue_425 ;
        list:hasNext      inst:IfcLengthMeasure_List_1176 .

inst:IfcLengthMeasure_List_1176
        list:hasContents  inst:IfcLengthMeasure_1161 ;
        list:hasNext      inst:IfcLengthMeasure_List_1177 .

inst:IfcLengthMeasure_List_1177
        list:hasContents  inst:IfcLengthMeasure_246 .

inst:IfcLengthMeasure_List_1178
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1179
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1180
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_1181
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-0.765366864730179"^^xsd:double .

inst:IfcLengthMeasure_List_1178
        list:hasContents  inst:IfcLengthMeasure_1181 ;
        list:hasNext      inst:IfcLengthMeasure_List_1179 .

inst:IfcLengthMeasure_List_1179
        list:hasContents  inst:IfcLengthMeasure_1165 ;
        list:hasNext      inst:IfcLengthMeasure_List_1180 .

inst:IfcLengthMeasure_List_1180
        list:hasContents  inst:IfcLengthMeasure_246 .

inst:IfcLengthMeasure_List_1182
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1183
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1184
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_1185
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-1.41421356237309"^^xsd:double .

inst:IfcLengthMeasure_List_1182
        list:hasContents  inst:IfcLengthMeasure_1185 ;
        list:hasNext      inst:IfcLengthMeasure_List_1183 .

inst:IfcLengthMeasure_List_1183
        list:hasContents  inst:IfcLengthMeasure_1170 ;
        list:hasNext      inst:IfcLengthMeasure_List_1184 .

inst:IfcLengthMeasure_List_1184
        list:hasContents  inst:IfcLengthMeasure_246 .

inst:IfcLengthMeasure_List_1186
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1187
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1188
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_1189
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-1.84775906502257"^^xsd:double .

inst:IfcLengthMeasure_List_1186
        list:hasContents  inst:IfcLengthMeasure_1189 ;
        list:hasNext      inst:IfcLengthMeasure_List_1187 .

inst:IfcLengthMeasure_List_1187
        list:hasContents  inst:IfcLengthMeasure_1166 ;
        list:hasNext      inst:IfcLengthMeasure_List_1188 .

inst:IfcLengthMeasure_List_1188
        list:hasContents  inst:IfcLengthMeasure_246 .

inst:IfcLengthMeasure_List_1190
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1191
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1192
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_1193
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-2."^^xsd:double .

inst:IfcLengthMeasure_List_1190
        list:hasContents  inst:IfcLengthMeasure_1193 ;
        list:hasNext      inst:IfcLengthMeasure_List_1191 .

inst:IfcLengthMeasure_1194
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  2.44921270764475E-16 .

inst:IfcLengthMeasure_List_1191
        list:hasContents  inst:IfcLengthMeasure_1194 ;
        list:hasNext      inst:IfcLengthMeasure_List_1192 .

inst:IfcLengthMeasure_List_1192
        list:hasContents  inst:IfcLengthMeasure_246 .

inst:IfcLengthMeasure_List_1195
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1196
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1197
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1195
        list:hasContents  inst:IfcLengthMeasure_1189 ;
        list:hasNext      inst:IfcLengthMeasure_List_1196 .

inst:IfcLengthMeasure_List_1196
        list:hasContents  inst:IfcLengthMeasure_1181 ;
        list:hasNext      inst:IfcLengthMeasure_List_1197 .

inst:IfcLengthMeasure_List_1197
        list:hasContents  inst:IfcLengthMeasure_246 .

inst:IfcLengthMeasure_List_1198
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1199
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1200
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_1201
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-1.4142135623731"^^xsd:double .

inst:IfcLengthMeasure_List_1198
        list:hasContents  inst:IfcLengthMeasure_1201 ;
        list:hasNext      inst:IfcLengthMeasure_List_1199 .

inst:IfcLengthMeasure_List_1199
        list:hasContents  inst:IfcLengthMeasure_1185 ;
        list:hasNext      inst:IfcLengthMeasure_List_1200 .

inst:IfcLengthMeasure_List_1200
        list:hasContents  inst:IfcLengthMeasure_246 .

inst:IfcLengthMeasure_List_1202
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1203
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1204
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_1205
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "-0.765366864730181"^^xsd:double .

inst:IfcLengthMeasure_List_1202
        list:hasContents  inst:IfcLengthMeasure_1205 ;
        list:hasNext      inst:IfcLengthMeasure_List_1203 .

inst:IfcLengthMeasure_List_1203
        list:hasContents  inst:IfcLengthMeasure_1189 ;
        list:hasNext      inst:IfcLengthMeasure_List_1204 .

inst:IfcLengthMeasure_List_1204
        list:hasContents  inst:IfcLengthMeasure_246 .

inst:IfcLengthMeasure_List_1206
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1207
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1208
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_1209
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  -3.67381906146713E-16 .

inst:IfcLengthMeasure_List_1206
        list:hasContents  inst:IfcLengthMeasure_1209 ;
        list:hasNext      inst:IfcLengthMeasure_List_1207 .

inst:IfcLengthMeasure_List_1207
        list:hasContents  inst:IfcLengthMeasure_1193 ;
        list:hasNext      inst:IfcLengthMeasure_List_1208 .

inst:IfcLengthMeasure_List_1208
        list:hasContents  inst:IfcLengthMeasure_246 .

inst:IfcLengthMeasure_List_1210
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1211
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1212
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1210
        list:hasContents  inst:IfcLengthMeasure_1166 ;
        list:hasNext      inst:IfcLengthMeasure_List_1211 .

inst:IfcLengthMeasure_List_1211
        list:hasContents  inst:IfcLengthMeasure_1189 ;
        list:hasNext      inst:IfcLengthMeasure_List_1212 .

inst:IfcLengthMeasure_List_1212
        list:hasContents  inst:IfcLengthMeasure_246 .

inst:IfcLengthMeasure_List_1213
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1214
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1215
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1213
        list:hasContents  inst:IfcLengthMeasure_1171 ;
        list:hasNext      inst:IfcLengthMeasure_List_1214 .

inst:IfcLengthMeasure_List_1214
        list:hasContents  inst:IfcLengthMeasure_1201 ;
        list:hasNext      inst:IfcLengthMeasure_List_1215 .

inst:IfcLengthMeasure_List_1215
        list:hasContents  inst:IfcLengthMeasure_246 .

inst:IfcLengthMeasure_List_1216
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1217
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1218
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1216
        list:hasContents  inst:IfcLengthMeasure_1165 ;
        list:hasNext      inst:IfcLengthMeasure_List_1217 .

inst:IfcLengthMeasure_List_1217
        list:hasContents  inst:IfcLengthMeasure_1205 ;
        list:hasNext      inst:IfcLengthMeasure_List_1218 .

inst:IfcLengthMeasure_List_1218
        list:hasContents  inst:IfcLengthMeasure_246 .

inst:IfcLengthMeasure_List_1219
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1220
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1221
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1219
        list:hasContents  inst:IfcLengthMeasure_1161 ;
        list:hasNext      inst:IfcLengthMeasure_List_1220 .

inst:IfcLengthMeasure_1222
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  -4.89842541528951E-16 .

inst:IfcLengthMeasure_List_1220
        list:hasContents  inst:IfcLengthMeasure_1222 ;
        list:hasNext      inst:IfcLengthMeasure_List_1221 .

inst:IfcLengthMeasure_List_1221
        list:hasContents  inst:IfcLengthMeasure_246 .

inst:IfcLengthMeasure_List_1223
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1224
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1225
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1223
        list:hasContents  inst:IfcLengthMeasure_1161 ;
        list:hasNext      inst:IfcLengthMeasure_List_1224 .

inst:IfcLengthMeasure_List_1224
        list:hasContents  inst:IfcLengthMeasure_246 ;
        list:hasNext      inst:IfcLengthMeasure_List_1225 .

inst:IfcLengthMeasure_List_1225
        list:hasContents  inst:IfcLengthMeasure_1157 .

inst:IfcLengthMeasure_List_1226
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1227
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1228
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1226
        list:hasContents  inst:IfcLengthMeasure_1165 ;
        list:hasNext      inst:IfcLengthMeasure_List_1227 .

inst:IfcLengthMeasure_List_1227
        list:hasContents  inst:IfcLengthMeasure_1166 ;
        list:hasNext      inst:IfcLengthMeasure_List_1228 .

inst:IfcLengthMeasure_List_1228
        list:hasContents  inst:IfcLengthMeasure_1157 .

inst:IfcLengthMeasure_List_1229
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1230
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1231
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1229
        list:hasContents  inst:IfcLengthMeasure_1170 ;
        list:hasNext      inst:IfcLengthMeasure_List_1230 .

inst:IfcLengthMeasure_List_1230
        list:hasContents  inst:IfcLengthMeasure_1171 ;
        list:hasNext      inst:IfcLengthMeasure_List_1231 .

inst:IfcLengthMeasure_List_1231
        list:hasContents  inst:IfcLengthMeasure_1157 .

inst:IfcLengthMeasure_List_1232
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1233
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1234
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1232
        list:hasContents  inst:IfcLengthMeasure_1166 ;
        list:hasNext      inst:IfcLengthMeasure_List_1233 .

inst:IfcLengthMeasure_List_1233
        list:hasContents  inst:IfcLengthMeasure_1165 ;
        list:hasNext      inst:IfcLengthMeasure_List_1234 .

inst:IfcLengthMeasure_List_1234
        list:hasContents  inst:IfcLengthMeasure_1157 .

inst:IfcLengthMeasure_List_1235
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1236
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1237
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1235
        list:hasContents  inst:IfcParameterValue_425 ;
        list:hasNext      inst:IfcLengthMeasure_List_1236 .

inst:IfcLengthMeasure_List_1236
        list:hasContents  inst:IfcLengthMeasure_1161 ;
        list:hasNext      inst:IfcLengthMeasure_List_1237 .

inst:IfcLengthMeasure_List_1237
        list:hasContents  inst:IfcLengthMeasure_1157 .

inst:IfcLengthMeasure_List_1238
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1239
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1240
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1238
        list:hasContents  inst:IfcLengthMeasure_1181 ;
        list:hasNext      inst:IfcLengthMeasure_List_1239 .

inst:IfcLengthMeasure_List_1239
        list:hasContents  inst:IfcLengthMeasure_1165 ;
        list:hasNext      inst:IfcLengthMeasure_List_1240 .

inst:IfcLengthMeasure_List_1240
        list:hasContents  inst:IfcLengthMeasure_1157 .

inst:IfcLengthMeasure_List_1241
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1242
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1243
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1241
        list:hasContents  inst:IfcLengthMeasure_1185 ;
        list:hasNext      inst:IfcLengthMeasure_List_1242 .

inst:IfcLengthMeasure_List_1242
        list:hasContents  inst:IfcLengthMeasure_1170 ;
        list:hasNext      inst:IfcLengthMeasure_List_1243 .

inst:IfcLengthMeasure_List_1243
        list:hasContents  inst:IfcLengthMeasure_1157 .

inst:IfcLengthMeasure_List_1244
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1245
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1246
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1244
        list:hasContents  inst:IfcLengthMeasure_1189 ;
        list:hasNext      inst:IfcLengthMeasure_List_1245 .

inst:IfcLengthMeasure_List_1245
        list:hasContents  inst:IfcLengthMeasure_1166 ;
        list:hasNext      inst:IfcLengthMeasure_List_1246 .

inst:IfcLengthMeasure_List_1246
        list:hasContents  inst:IfcLengthMeasure_1157 .

inst:IfcLengthMeasure_List_1247
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1248
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1249
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1247
        list:hasContents  inst:IfcLengthMeasure_1193 ;
        list:hasNext      inst:IfcLengthMeasure_List_1248 .

inst:IfcLengthMeasure_List_1248
        list:hasContents  inst:IfcLengthMeasure_1194 ;
        list:hasNext      inst:IfcLengthMeasure_List_1249 .

inst:IfcLengthMeasure_List_1249
        list:hasContents  inst:IfcLengthMeasure_1157 .

inst:IfcLengthMeasure_List_1250
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1251
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1252
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1250
        list:hasContents  inst:IfcLengthMeasure_1189 ;
        list:hasNext      inst:IfcLengthMeasure_List_1251 .

inst:IfcLengthMeasure_List_1251
        list:hasContents  inst:IfcLengthMeasure_1181 ;
        list:hasNext      inst:IfcLengthMeasure_List_1252 .

inst:IfcLengthMeasure_List_1252
        list:hasContents  inst:IfcLengthMeasure_1157 .

inst:IfcLengthMeasure_List_1253
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1254
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1255
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1253
        list:hasContents  inst:IfcLengthMeasure_1201 ;
        list:hasNext      inst:IfcLengthMeasure_List_1254 .

inst:IfcLengthMeasure_List_1254
        list:hasContents  inst:IfcLengthMeasure_1185 ;
        list:hasNext      inst:IfcLengthMeasure_List_1255 .

inst:IfcLengthMeasure_List_1255
        list:hasContents  inst:IfcLengthMeasure_1157 .

inst:IfcLengthMeasure_List_1256
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1257
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1258
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1256
        list:hasContents  inst:IfcLengthMeasure_1205 ;
        list:hasNext      inst:IfcLengthMeasure_List_1257 .

inst:IfcLengthMeasure_List_1257
        list:hasContents  inst:IfcLengthMeasure_1189 ;
        list:hasNext      inst:IfcLengthMeasure_List_1258 .

inst:IfcLengthMeasure_List_1258
        list:hasContents  inst:IfcLengthMeasure_1157 .

inst:IfcLengthMeasure_List_1259
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1260
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1261
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1259
        list:hasContents  inst:IfcLengthMeasure_1209 ;
        list:hasNext      inst:IfcLengthMeasure_List_1260 .

inst:IfcLengthMeasure_List_1260
        list:hasContents  inst:IfcLengthMeasure_1193 ;
        list:hasNext      inst:IfcLengthMeasure_List_1261 .

inst:IfcLengthMeasure_List_1261
        list:hasContents  inst:IfcLengthMeasure_1157 .

inst:IfcLengthMeasure_List_1262
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1263
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1264
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1262
        list:hasContents  inst:IfcLengthMeasure_1166 ;
        list:hasNext      inst:IfcLengthMeasure_List_1263 .

inst:IfcLengthMeasure_List_1263
        list:hasContents  inst:IfcLengthMeasure_1189 ;
        list:hasNext      inst:IfcLengthMeasure_List_1264 .

inst:IfcLengthMeasure_List_1264
        list:hasContents  inst:IfcLengthMeasure_1157 .

inst:IfcLengthMeasure_List_1265
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1266
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1267
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1265
        list:hasContents  inst:IfcLengthMeasure_1171 ;
        list:hasNext      inst:IfcLengthMeasure_List_1266 .

inst:IfcLengthMeasure_List_1266
        list:hasContents  inst:IfcLengthMeasure_1201 ;
        list:hasNext      inst:IfcLengthMeasure_List_1267 .

inst:IfcLengthMeasure_List_1267
        list:hasContents  inst:IfcLengthMeasure_1157 .

inst:IfcLengthMeasure_List_1268
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1269
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1270
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1268
        list:hasContents  inst:IfcLengthMeasure_1165 ;
        list:hasNext      inst:IfcLengthMeasure_List_1269 .

inst:IfcLengthMeasure_List_1269
        list:hasContents  inst:IfcLengthMeasure_1205 ;
        list:hasNext      inst:IfcLengthMeasure_List_1270 .

inst:IfcLengthMeasure_List_1270
        list:hasContents  inst:IfcLengthMeasure_1157 .

inst:IfcLengthMeasure_List_1271
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1272
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1273
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1271
        list:hasContents  inst:IfcLengthMeasure_1161 ;
        list:hasNext      inst:IfcLengthMeasure_List_1272 .

inst:IfcLengthMeasure_List_1272
        list:hasContents  inst:IfcLengthMeasure_1222 ;
        list:hasNext      inst:IfcLengthMeasure_List_1273 .

inst:IfcLengthMeasure_List_1273
        list:hasContents  inst:IfcLengthMeasure_1157 .

inst:IfcLengthMeasure_List_1274
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1275
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1276
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1274
        list:hasContents  inst:IfcLengthMeasure_1161 ;
        list:hasNext      inst:IfcLengthMeasure_List_1275 .

inst:IfcLengthMeasure_List_1275
        list:hasContents  inst:IfcLengthMeasure_246 ;
        list:hasNext      inst:IfcLengthMeasure_List_1276 .

inst:IfcLengthMeasure_List_1276
        list:hasContents  inst:IfcLengthMeasure_246 .

inst:IfcLengthMeasure_List_1277
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1278
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1279
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1277
        list:hasContents  inst:IfcLengthMeasure_1165 ;
        list:hasNext      inst:IfcLengthMeasure_List_1278 .

inst:IfcLengthMeasure_List_1278
        list:hasContents  inst:IfcLengthMeasure_1166 ;
        list:hasNext      inst:IfcLengthMeasure_List_1279 .

inst:IfcLengthMeasure_List_1279
        list:hasContents  inst:IfcLengthMeasure_246 .

inst:IfcLengthMeasure_List_1280
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1281
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1282
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1280
        list:hasContents  inst:IfcLengthMeasure_1170 ;
        list:hasNext      inst:IfcLengthMeasure_List_1281 .

inst:IfcLengthMeasure_List_1281
        list:hasContents  inst:IfcLengthMeasure_1171 ;
        list:hasNext      inst:IfcLengthMeasure_List_1282 .

inst:IfcLengthMeasure_List_1282
        list:hasContents  inst:IfcLengthMeasure_246 .

inst:IfcLengthMeasure_List_1283
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1284
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1285
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1283
        list:hasContents  inst:IfcLengthMeasure_1166 ;
        list:hasNext      inst:IfcLengthMeasure_List_1284 .

inst:IfcLengthMeasure_List_1284
        list:hasContents  inst:IfcLengthMeasure_1165 ;
        list:hasNext      inst:IfcLengthMeasure_List_1285 .

inst:IfcLengthMeasure_List_1285
        list:hasContents  inst:IfcLengthMeasure_246 .

inst:IfcLengthMeasure_List_1286
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1287
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1288
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1286
        list:hasContents  inst:IfcParameterValue_425 ;
        list:hasNext      inst:IfcLengthMeasure_List_1287 .

inst:IfcLengthMeasure_List_1287
        list:hasContents  inst:IfcLengthMeasure_1161 ;
        list:hasNext      inst:IfcLengthMeasure_List_1288 .

inst:IfcLengthMeasure_List_1288
        list:hasContents  inst:IfcLengthMeasure_246 .

inst:IfcLengthMeasure_List_1289
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1290
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1291
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1289
        list:hasContents  inst:IfcLengthMeasure_1181 ;
        list:hasNext      inst:IfcLengthMeasure_List_1290 .

inst:IfcLengthMeasure_List_1290
        list:hasContents  inst:IfcLengthMeasure_1165 ;
        list:hasNext      inst:IfcLengthMeasure_List_1291 .

inst:IfcLengthMeasure_List_1291
        list:hasContents  inst:IfcLengthMeasure_246 .

inst:IfcLengthMeasure_List_1292
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1293
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1294
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1292
        list:hasContents  inst:IfcLengthMeasure_1185 ;
        list:hasNext      inst:IfcLengthMeasure_List_1293 .

inst:IfcLengthMeasure_List_1293
        list:hasContents  inst:IfcLengthMeasure_1170 ;
        list:hasNext      inst:IfcLengthMeasure_List_1294 .

inst:IfcLengthMeasure_List_1294
        list:hasContents  inst:IfcLengthMeasure_246 .

inst:IfcLengthMeasure_List_1295
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1296
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1297
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1295
        list:hasContents  inst:IfcLengthMeasure_1189 ;
        list:hasNext      inst:IfcLengthMeasure_List_1296 .

inst:IfcLengthMeasure_List_1296
        list:hasContents  inst:IfcLengthMeasure_1166 ;
        list:hasNext      inst:IfcLengthMeasure_List_1297 .

inst:IfcLengthMeasure_List_1297
        list:hasContents  inst:IfcLengthMeasure_246 .

inst:IfcLengthMeasure_List_1298
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1299
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1300
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1298
        list:hasContents  inst:IfcLengthMeasure_1193 ;
        list:hasNext      inst:IfcLengthMeasure_List_1299 .

inst:IfcLengthMeasure_List_1299
        list:hasContents  inst:IfcLengthMeasure_1194 ;
        list:hasNext      inst:IfcLengthMeasure_List_1300 .

inst:IfcLengthMeasure_List_1300
        list:hasContents  inst:IfcLengthMeasure_246 .

inst:IfcLengthMeasure_List_1301
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1302
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1303
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1301
        list:hasContents  inst:IfcLengthMeasure_1189 ;
        list:hasNext      inst:IfcLengthMeasure_List_1302 .

inst:IfcLengthMeasure_List_1302
        list:hasContents  inst:IfcLengthMeasure_1181 ;
        list:hasNext      inst:IfcLengthMeasure_List_1303 .

inst:IfcLengthMeasure_List_1303
        list:hasContents  inst:IfcLengthMeasure_246 .

inst:IfcLengthMeasure_List_1304
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1305
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1306
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1304
        list:hasContents  inst:IfcLengthMeasure_1201 ;
        list:hasNext      inst:IfcLengthMeasure_List_1305 .

inst:IfcLengthMeasure_List_1305
        list:hasContents  inst:IfcLengthMeasure_1185 ;
        list:hasNext      inst:IfcLengthMeasure_List_1306 .

inst:IfcLengthMeasure_List_1306
        list:hasContents  inst:IfcLengthMeasure_246 .

inst:IfcLengthMeasure_List_1307
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1308
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1309
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1307
        list:hasContents  inst:IfcLengthMeasure_1205 ;
        list:hasNext      inst:IfcLengthMeasure_List_1308 .

inst:IfcLengthMeasure_List_1308
        list:hasContents  inst:IfcLengthMeasure_1189 ;
        list:hasNext      inst:IfcLengthMeasure_List_1309 .

inst:IfcLengthMeasure_List_1309
        list:hasContents  inst:IfcLengthMeasure_246 .

inst:IfcLengthMeasure_List_1310
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1311
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1312
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1310
        list:hasContents  inst:IfcLengthMeasure_1209 ;
        list:hasNext      inst:IfcLengthMeasure_List_1311 .

inst:IfcLengthMeasure_List_1311
        list:hasContents  inst:IfcLengthMeasure_1193 ;
        list:hasNext      inst:IfcLengthMeasure_List_1312 .

inst:IfcLengthMeasure_List_1312
        list:hasContents  inst:IfcLengthMeasure_246 .

inst:IfcLengthMeasure_List_1313
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1314
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1315
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1313
        list:hasContents  inst:IfcLengthMeasure_1166 ;
        list:hasNext      inst:IfcLengthMeasure_List_1314 .

inst:IfcLengthMeasure_List_1314
        list:hasContents  inst:IfcLengthMeasure_1189 ;
        list:hasNext      inst:IfcLengthMeasure_List_1315 .

inst:IfcLengthMeasure_List_1315
        list:hasContents  inst:IfcLengthMeasure_246 .

inst:IfcLengthMeasure_List_1316
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1317
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1318
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1316
        list:hasContents  inst:IfcLengthMeasure_1171 ;
        list:hasNext      inst:IfcLengthMeasure_List_1317 .

inst:IfcLengthMeasure_List_1317
        list:hasContents  inst:IfcLengthMeasure_1201 ;
        list:hasNext      inst:IfcLengthMeasure_List_1318 .

inst:IfcLengthMeasure_List_1318
        list:hasContents  inst:IfcLengthMeasure_246 .

inst:IfcLengthMeasure_List_1319
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1320
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1321
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1319
        list:hasContents  inst:IfcLengthMeasure_1165 ;
        list:hasNext      inst:IfcLengthMeasure_List_1320 .

inst:IfcLengthMeasure_List_1320
        list:hasContents  inst:IfcLengthMeasure_1205 ;
        list:hasNext      inst:IfcLengthMeasure_List_1321 .

inst:IfcLengthMeasure_List_1321
        list:hasContents  inst:IfcLengthMeasure_246 .

inst:IfcLengthMeasure_List_1322
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1323
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1324
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1322
        list:hasContents  inst:IfcLengthMeasure_1161 ;
        list:hasNext      inst:IfcLengthMeasure_List_1323 .

inst:IfcLengthMeasure_List_1323
        list:hasContents  inst:IfcLengthMeasure_1222 ;
        list:hasNext      inst:IfcLengthMeasure_List_1324 .

inst:IfcLengthMeasure_List_1324
        list:hasContents  inst:IfcLengthMeasure_246 .

inst:IfcLengthMeasure_List_1325
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1326
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1327
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1325
        list:hasContents  inst:IfcLengthMeasure_1161 ;
        list:hasNext      inst:IfcLengthMeasure_List_1326 .

inst:IfcLengthMeasure_List_1326
        list:hasContents  inst:IfcLengthMeasure_246 ;
        list:hasNext      inst:IfcLengthMeasure_List_1327 .

inst:IfcLengthMeasure_List_1327
        list:hasContents  inst:IfcLengthMeasure_1157 .

inst:IfcLengthMeasure_List_1328
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1329
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1330
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1328
        list:hasContents  inst:IfcLengthMeasure_1165 ;
        list:hasNext      inst:IfcLengthMeasure_List_1329 .

inst:IfcLengthMeasure_List_1329
        list:hasContents  inst:IfcLengthMeasure_1166 ;
        list:hasNext      inst:IfcLengthMeasure_List_1330 .

inst:IfcLengthMeasure_List_1330
        list:hasContents  inst:IfcLengthMeasure_1157 .

inst:IfcLengthMeasure_List_1331
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1332
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1333
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1331
        list:hasContents  inst:IfcLengthMeasure_1170 ;
        list:hasNext      inst:IfcLengthMeasure_List_1332 .

inst:IfcLengthMeasure_List_1332
        list:hasContents  inst:IfcLengthMeasure_1171 ;
        list:hasNext      inst:IfcLengthMeasure_List_1333 .

inst:IfcLengthMeasure_List_1333
        list:hasContents  inst:IfcLengthMeasure_1157 .

inst:IfcLengthMeasure_List_1334
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1335
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1336
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1334
        list:hasContents  inst:IfcLengthMeasure_1166 ;
        list:hasNext      inst:IfcLengthMeasure_List_1335 .

inst:IfcLengthMeasure_List_1335
        list:hasContents  inst:IfcLengthMeasure_1165 ;
        list:hasNext      inst:IfcLengthMeasure_List_1336 .

inst:IfcLengthMeasure_List_1336
        list:hasContents  inst:IfcLengthMeasure_1157 .

inst:IfcLengthMeasure_List_1337
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1338
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1339
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1337
        list:hasContents  inst:IfcParameterValue_425 ;
        list:hasNext      inst:IfcLengthMeasure_List_1338 .

inst:IfcLengthMeasure_List_1338
        list:hasContents  inst:IfcLengthMeasure_1161 ;
        list:hasNext      inst:IfcLengthMeasure_List_1339 .

inst:IfcLengthMeasure_List_1339
        list:hasContents  inst:IfcLengthMeasure_1157 .

inst:IfcLengthMeasure_List_1340
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1341
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1342
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1340
        list:hasContents  inst:IfcLengthMeasure_1181 ;
        list:hasNext      inst:IfcLengthMeasure_List_1341 .

inst:IfcLengthMeasure_List_1341
        list:hasContents  inst:IfcLengthMeasure_1165 ;
        list:hasNext      inst:IfcLengthMeasure_List_1342 .

inst:IfcLengthMeasure_List_1342
        list:hasContents  inst:IfcLengthMeasure_1157 .

inst:IfcLengthMeasure_List_1343
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1344
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1345
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1343
        list:hasContents  inst:IfcLengthMeasure_1185 ;
        list:hasNext      inst:IfcLengthMeasure_List_1344 .

inst:IfcLengthMeasure_List_1344
        list:hasContents  inst:IfcLengthMeasure_1170 ;
        list:hasNext      inst:IfcLengthMeasure_List_1345 .

inst:IfcLengthMeasure_List_1345
        list:hasContents  inst:IfcLengthMeasure_1157 .

inst:IfcLengthMeasure_List_1346
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1347
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1348
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1346
        list:hasContents  inst:IfcLengthMeasure_1189 ;
        list:hasNext      inst:IfcLengthMeasure_List_1347 .

inst:IfcLengthMeasure_List_1347
        list:hasContents  inst:IfcLengthMeasure_1166 ;
        list:hasNext      inst:IfcLengthMeasure_List_1348 .

inst:IfcLengthMeasure_List_1348
        list:hasContents  inst:IfcLengthMeasure_1157 .

inst:IfcLengthMeasure_List_1349
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1350
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1351
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1349
        list:hasContents  inst:IfcLengthMeasure_1193 ;
        list:hasNext      inst:IfcLengthMeasure_List_1350 .

inst:IfcLengthMeasure_List_1350
        list:hasContents  inst:IfcLengthMeasure_1194 ;
        list:hasNext      inst:IfcLengthMeasure_List_1351 .

inst:IfcLengthMeasure_List_1351
        list:hasContents  inst:IfcLengthMeasure_1157 .

inst:IfcLengthMeasure_List_1352
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1353
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1354
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1352
        list:hasContents  inst:IfcLengthMeasure_1189 ;
        list:hasNext      inst:IfcLengthMeasure_List_1353 .

inst:IfcLengthMeasure_List_1353
        list:hasContents  inst:IfcLengthMeasure_1181 ;
        list:hasNext      inst:IfcLengthMeasure_List_1354 .

inst:IfcLengthMeasure_List_1354
        list:hasContents  inst:IfcLengthMeasure_1157 .

inst:IfcLengthMeasure_List_1355
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1356
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1357
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1355
        list:hasContents  inst:IfcLengthMeasure_1201 ;
        list:hasNext      inst:IfcLengthMeasure_List_1356 .

inst:IfcLengthMeasure_List_1356
        list:hasContents  inst:IfcLengthMeasure_1185 ;
        list:hasNext      inst:IfcLengthMeasure_List_1357 .

inst:IfcLengthMeasure_List_1357
        list:hasContents  inst:IfcLengthMeasure_1157 .

inst:IfcLengthMeasure_List_1358
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1359
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1360
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1358
        list:hasContents  inst:IfcLengthMeasure_1205 ;
        list:hasNext      inst:IfcLengthMeasure_List_1359 .

inst:IfcLengthMeasure_List_1359
        list:hasContents  inst:IfcLengthMeasure_1189 ;
        list:hasNext      inst:IfcLengthMeasure_List_1360 .

inst:IfcLengthMeasure_List_1360
        list:hasContents  inst:IfcLengthMeasure_1157 .

inst:IfcLengthMeasure_List_1361
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1362
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1363
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1361
        list:hasContents  inst:IfcLengthMeasure_1209 ;
        list:hasNext      inst:IfcLengthMeasure_List_1362 .

inst:IfcLengthMeasure_List_1362
        list:hasContents  inst:IfcLengthMeasure_1193 ;
        list:hasNext      inst:IfcLengthMeasure_List_1363 .

inst:IfcLengthMeasure_List_1363
        list:hasContents  inst:IfcLengthMeasure_1157 .

inst:IfcLengthMeasure_List_1364
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1365
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1366
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1364
        list:hasContents  inst:IfcLengthMeasure_1166 ;
        list:hasNext      inst:IfcLengthMeasure_List_1365 .

inst:IfcLengthMeasure_List_1365
        list:hasContents  inst:IfcLengthMeasure_1189 ;
        list:hasNext      inst:IfcLengthMeasure_List_1366 .

inst:IfcLengthMeasure_List_1366
        list:hasContents  inst:IfcLengthMeasure_1157 .

inst:IfcLengthMeasure_List_1367
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1368
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1369
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1367
        list:hasContents  inst:IfcLengthMeasure_1171 ;
        list:hasNext      inst:IfcLengthMeasure_List_1368 .

inst:IfcLengthMeasure_List_1368
        list:hasContents  inst:IfcLengthMeasure_1201 ;
        list:hasNext      inst:IfcLengthMeasure_List_1369 .

inst:IfcLengthMeasure_List_1369
        list:hasContents  inst:IfcLengthMeasure_1157 .

inst:IfcLengthMeasure_List_1370
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1371
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1372
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1370
        list:hasContents  inst:IfcLengthMeasure_1165 ;
        list:hasNext      inst:IfcLengthMeasure_List_1371 .

inst:IfcLengthMeasure_List_1371
        list:hasContents  inst:IfcLengthMeasure_1205 ;
        list:hasNext      inst:IfcLengthMeasure_List_1372 .

inst:IfcLengthMeasure_List_1372
        list:hasContents  inst:IfcLengthMeasure_1157 .

inst:IfcLengthMeasure_List_1373
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1374
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1375
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1373
        list:hasContents  inst:IfcLengthMeasure_1161 ;
        list:hasNext      inst:IfcLengthMeasure_List_1374 .

inst:IfcLengthMeasure_List_1374
        list:hasContents  inst:IfcLengthMeasure_1222 ;
        list:hasNext      inst:IfcLengthMeasure_List_1375 .

inst:IfcLengthMeasure_List_1375
        list:hasContents  inst:IfcLengthMeasure_1157 .

inst:IfcLengthMeasure_List_List_1376
        rdf:type  ifc:IfcLengthMeasure_List_List .

inst:IfcCartesianPointList3D_211
        ifc:coordList_IfcCartesianPointList3D  inst:IfcLengthMeasure_List_List_1376 .

inst:IfcLengthMeasure_List_List_1376
        list:hasContents  inst:IfcLengthMeasure_List_1151 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1377 .

inst:IfcLengthMeasure_List_List_1377
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_1154 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1378 .

inst:IfcLengthMeasure_List_List_1378
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_1158 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1379 .

inst:IfcLengthMeasure_List_List_1379
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_1162 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1380 .

inst:IfcLengthMeasure_List_List_1380
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_1167 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1381 .

inst:IfcLengthMeasure_List_List_1381
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_1172 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1382 .

inst:IfcLengthMeasure_List_List_1382
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_1175 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1383 .

inst:IfcLengthMeasure_List_List_1383
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_1178 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1384 .

inst:IfcLengthMeasure_List_List_1384
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_1182 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1385 .

inst:IfcLengthMeasure_List_List_1385
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_1186 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1386 .

inst:IfcLengthMeasure_List_List_1386
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_1190 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1387 .

inst:IfcLengthMeasure_List_List_1387
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_1195 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1388 .

inst:IfcLengthMeasure_List_List_1388
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_1198 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1389 .

inst:IfcLengthMeasure_List_List_1389
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_1202 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1390 .

inst:IfcLengthMeasure_List_List_1390
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_1206 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1391 .

inst:IfcLengthMeasure_List_List_1391
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_1210 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1392 .

inst:IfcLengthMeasure_List_List_1392
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_1213 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1393 .

inst:IfcLengthMeasure_List_List_1393
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_1216 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1394 .

inst:IfcLengthMeasure_List_List_1394
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_1219 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1395 .

inst:IfcLengthMeasure_List_List_1395
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_1223 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1396 .

inst:IfcLengthMeasure_List_List_1396
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_1226 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1397 .

inst:IfcLengthMeasure_List_List_1397
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_1229 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1398 .

inst:IfcLengthMeasure_List_List_1398
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_1232 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1399 .

inst:IfcLengthMeasure_List_List_1399
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_1235 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1400 .

inst:IfcLengthMeasure_List_List_1400
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_1238 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1401 .

inst:IfcLengthMeasure_List_List_1401
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_1241 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1402 .

inst:IfcLengthMeasure_List_List_1402
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_1244 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1403 .

inst:IfcLengthMeasure_List_List_1403
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_1247 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1404 .

inst:IfcLengthMeasure_List_List_1404
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_1250 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1405 .

inst:IfcLengthMeasure_List_List_1405
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_1253 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1406 .

inst:IfcLengthMeasure_List_List_1406
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_1256 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1407 .

inst:IfcLengthMeasure_List_List_1407
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_1259 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1408 .

inst:IfcLengthMeasure_List_List_1408
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_1262 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1409 .

inst:IfcLengthMeasure_List_List_1409
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_1265 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1410 .

inst:IfcLengthMeasure_List_List_1410
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_1268 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1411 .

inst:IfcLengthMeasure_List_List_1411
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_1271 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1412 .

inst:IfcLengthMeasure_List_List_1412
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_1274 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1413 .

inst:IfcLengthMeasure_List_List_1413
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_1277 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1414 .

inst:IfcLengthMeasure_List_List_1414
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_1280 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1415 .

inst:IfcLengthMeasure_List_List_1415
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_1283 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1416 .

inst:IfcLengthMeasure_List_List_1416
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_1286 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1417 .

inst:IfcLengthMeasure_List_List_1417
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_1289 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1418 .

inst:IfcLengthMeasure_List_List_1418
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_1292 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1419 .

inst:IfcLengthMeasure_List_List_1419
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_1295 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1420 .

inst:IfcLengthMeasure_List_List_1420
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_1298 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1421 .

inst:IfcLengthMeasure_List_List_1421
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_1301 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1422 .

inst:IfcLengthMeasure_List_List_1422
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_1304 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1423 .

inst:IfcLengthMeasure_List_List_1423
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_1307 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1424 .

inst:IfcLengthMeasure_List_List_1424
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_1310 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1425 .

inst:IfcLengthMeasure_List_List_1425
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_1313 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1426 .

inst:IfcLengthMeasure_List_List_1426
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_1316 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1427 .

inst:IfcLengthMeasure_List_List_1427
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_1319 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1428 .

inst:IfcLengthMeasure_List_List_1428
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_1322 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1429 .

inst:IfcLengthMeasure_List_List_1429
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_1325 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1430 .

inst:IfcLengthMeasure_List_List_1430
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_1328 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1431 .

inst:IfcLengthMeasure_List_List_1431
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_1331 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1432 .

inst:IfcLengthMeasure_List_List_1432
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_1334 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1433 .

inst:IfcLengthMeasure_List_List_1433
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_1337 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1434 .

inst:IfcLengthMeasure_List_List_1434
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_1340 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1435 .

inst:IfcLengthMeasure_List_List_1435
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_1343 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1436 .

inst:IfcLengthMeasure_List_List_1436
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_1346 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1437 .

inst:IfcLengthMeasure_List_List_1437
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_1349 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1438 .

inst:IfcLengthMeasure_List_List_1438
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_1352 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1439 .

inst:IfcLengthMeasure_List_List_1439
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_1355 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1440 .

inst:IfcLengthMeasure_List_List_1440
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_1358 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1441 .

inst:IfcLengthMeasure_List_List_1441
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_1361 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1442 .

inst:IfcLengthMeasure_List_List_1442
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_1364 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1443 .

inst:IfcLengthMeasure_List_List_1443
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_1367 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1444 .

inst:IfcLengthMeasure_List_List_1444
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_1370 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_1445 .

inst:IfcLengthMeasure_List_List_1445
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_1373 .

inst:IfcStyledItem_212
        rdf:type                ifc:IfcStyledItem ;
        ifc:item_IfcStyledItem  inst:IfcTriangulatedFaceSet_210 .

inst:IfcSurfaceStyle_214
        rdf:type  ifc:IfcSurfaceStyle .

inst:IfcStyledItem_212
        ifc:styles_IfcStyledItem  inst:IfcSurfaceStyle_214 .

inst:IfcIndexedTriangleTextureMap_213
        rdf:type  ifc:IfcIndexedTriangleTextureMap .

inst:IfcSurfaceTexture_List_1446
        rdf:type  ifc:IfcSurfaceTexture_List .

inst:IfcIndexedTriangleTextureMap_213
        ifc:maps_IfcTextureCoordinate  inst:IfcSurfaceTexture_List_1446 .

inst:IfcBlobTexture_219
        rdf:type  ifc:IfcBlobTexture .

inst:IfcSurfaceTexture_List_1446
        list:hasContents  inst:IfcBlobTexture_219 .

inst:IfcIndexedTriangleTextureMap_213
        ifc:mappedTo_IfcIndexedTextureMap  inst:IfcTriangulatedFaceSet_210 .

inst:IfcTextureVertexList_215
        rdf:type  ifc:IfcTextureVertexList .

inst:IfcIndexedTriangleTextureMap_213
        ifc:texCoords_IfcIndexedTextureMap  inst:IfcTextureVertexList_215 .

inst:IfcPositiveInteger_List_1447
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1448
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1449
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1447
        list:hasContents  inst:INTEGER_249 ;
        list:hasNext      inst:IfcPositiveInteger_List_1448 .

inst:IfcPositiveInteger_List_1448
        list:hasContents  inst:INTEGER_251 ;
        list:hasNext      inst:IfcPositiveInteger_List_1449 .

inst:IfcPositiveInteger_List_1449
        list:hasContents  inst:IfcDimensionCount_234 .

inst:IfcPositiveInteger_List_1450
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1451
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1452
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1450
        list:hasContents  inst:IfcPositiveInteger_581 ;
        list:hasNext      inst:IfcPositiveInteger_List_1451 .

inst:IfcPositiveInteger_List_1451
        list:hasContents  inst:IfcPositiveInteger_582 ;
        list:hasNext      inst:IfcPositiveInteger_List_1452 .

inst:IfcPositiveInteger_List_1452
        list:hasContents  inst:IfcPositiveInteger_583 .

inst:IfcPositiveInteger_List_1453
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1454
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1455
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1453
        list:hasContents  inst:IfcPositiveInteger_581 ;
        list:hasNext      inst:IfcPositiveInteger_List_1454 .

inst:IfcPositiveInteger_List_1454
        list:hasContents  inst:IfcPositiveInteger_587 ;
        list:hasNext      inst:IfcPositiveInteger_List_1455 .

inst:IfcPositiveInteger_List_1455
        list:hasContents  inst:IfcPositiveInteger_582 .

inst:IfcPositiveInteger_List_1456
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1457
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1458
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1456
        list:hasContents  inst:IfcDimensionCount_238 ;
        list:hasNext      inst:IfcPositiveInteger_List_1457 .

inst:IfcPositiveInteger_List_1457
        list:hasContents  inst:IfcPositiveInteger_591 ;
        list:hasNext      inst:IfcPositiveInteger_List_1458 .

inst:IfcPositiveInteger_List_1458
        list:hasContents  inst:IfcPositiveInteger_592 .

inst:IfcPositiveInteger_List_1459
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1460
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1461
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1459
        list:hasContents  inst:INTEGER_249 ;
        list:hasNext      inst:IfcPositiveInteger_List_1460 .

inst:IfcPositiveInteger_List_1460
        list:hasContents  inst:INTEGER_252 ;
        list:hasNext      inst:IfcPositiveInteger_List_1461 .

inst:IfcPositiveInteger_List_1461
        list:hasContents  inst:INTEGER_251 .

inst:IfcPositiveInteger_List_1462
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1463
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1464
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1462
        list:hasContents  inst:IfcPositiveInteger_599 ;
        list:hasNext      inst:IfcPositiveInteger_List_1463 .

inst:IfcPositiveInteger_List_1463
        list:hasContents  inst:IfcPositiveInteger_587 ;
        list:hasNext      inst:IfcPositiveInteger_List_1464 .

inst:IfcPositiveInteger_List_1464
        list:hasContents  inst:IfcPositiveInteger_581 .

inst:IfcPositiveInteger_List_1465
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1466
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1467
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1465
        list:hasContents  inst:IfcPositiveInteger_599 ;
        list:hasNext      inst:IfcPositiveInteger_List_1466 .

inst:IfcPositiveInteger_List_1466
        list:hasContents  inst:IfcPositiveInteger_603 ;
        list:hasNext      inst:IfcPositiveInteger_List_1467 .

inst:IfcPositiveInteger_List_1467
        list:hasContents  inst:IfcPositiveInteger_587 .

inst:IfcPositiveInteger_List_1468
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1469
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1470
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1468
        list:hasContents  inst:IfcDimensionCount_238 ;
        list:hasNext      inst:IfcPositiveInteger_List_1469 .

inst:IfcPositiveInteger_List_1469
        list:hasContents  inst:IfcPositiveInteger_592 ;
        list:hasNext      inst:IfcPositiveInteger_List_1470 .

inst:IfcPositiveInteger_List_1470
        list:hasContents  inst:IfcPositiveInteger_607 .

inst:IfcPositiveInteger_List_1471
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1472
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1473
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1471
        list:hasContents  inst:INTEGER_249 ;
        list:hasNext      inst:IfcPositiveInteger_List_1472 .

inst:IfcPositiveInteger_List_1472
        list:hasContents  inst:INTEGER_253 ;
        list:hasNext      inst:IfcPositiveInteger_List_1473 .

inst:IfcPositiveInteger_List_1473
        list:hasContents  inst:INTEGER_252 .

inst:IfcPositiveInteger_List_1474
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1475
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1476
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1474
        list:hasContents  inst:IfcPositiveInteger_614 ;
        list:hasNext      inst:IfcPositiveInteger_List_1475 .

inst:IfcPositiveInteger_List_1475
        list:hasContents  inst:IfcPositiveInteger_603 ;
        list:hasNext      inst:IfcPositiveInteger_List_1476 .

inst:IfcPositiveInteger_List_1476
        list:hasContents  inst:IfcPositiveInteger_599 .

inst:IfcPositiveInteger_List_1477
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1478
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1479
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1477
        list:hasContents  inst:IfcPositiveInteger_614 ;
        list:hasNext      inst:IfcPositiveInteger_List_1478 .

inst:IfcPositiveInteger_List_1478
        list:hasContents  inst:IfcPositiveInteger_618 ;
        list:hasNext      inst:IfcPositiveInteger_List_1479 .

inst:IfcPositiveInteger_List_1479
        list:hasContents  inst:IfcPositiveInteger_603 .

inst:IfcPositiveInteger_List_1480
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1481
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1482
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1480
        list:hasContents  inst:IfcDimensionCount_238 ;
        list:hasNext      inst:IfcPositiveInteger_List_1481 .

inst:IfcPositiveInteger_List_1481
        list:hasContents  inst:IfcPositiveInteger_607 ;
        list:hasNext      inst:IfcPositiveInteger_List_1482 .

inst:IfcPositiveInteger_List_1482
        list:hasContents  inst:IfcPositiveInteger_622 .

inst:IfcPositiveInteger_List_1483
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1484
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1485
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1483
        list:hasContents  inst:INTEGER_249 ;
        list:hasNext      inst:IfcPositiveInteger_List_1484 .

inst:IfcPositiveInteger_List_1484
        list:hasContents  inst:IfcPositiveInteger_626 ;
        list:hasNext      inst:IfcPositiveInteger_List_1485 .

inst:IfcPositiveInteger_List_1485
        list:hasContents  inst:INTEGER_253 .

inst:IfcPositiveInteger_List_1486
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1487
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1488
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1486
        list:hasContents  inst:IfcPositiveInteger_630 ;
        list:hasNext      inst:IfcPositiveInteger_List_1487 .

inst:IfcPositiveInteger_List_1487
        list:hasContents  inst:IfcPositiveInteger_618 ;
        list:hasNext      inst:IfcPositiveInteger_List_1488 .

inst:IfcPositiveInteger_List_1488
        list:hasContents  inst:IfcPositiveInteger_614 .

inst:IfcPositiveInteger_List_1489
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1490
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1491
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1489
        list:hasContents  inst:IfcPositiveInteger_630 ;
        list:hasNext      inst:IfcPositiveInteger_List_1490 .

inst:IfcPositiveInteger_List_1490
        list:hasContents  inst:IfcPositiveInteger_634 ;
        list:hasNext      inst:IfcPositiveInteger_List_1491 .

inst:IfcPositiveInteger_List_1491
        list:hasContents  inst:IfcPositiveInteger_618 .

inst:IfcPositiveInteger_List_1492
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1493
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1494
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1492
        list:hasContents  inst:IfcDimensionCount_238 ;
        list:hasNext      inst:IfcPositiveInteger_List_1493 .

inst:IfcPositiveInteger_List_1493
        list:hasContents  inst:IfcPositiveInteger_622 ;
        list:hasNext      inst:IfcPositiveInteger_List_1494 .

inst:IfcPositiveInteger_List_1494
        list:hasContents  inst:IfcPositiveInteger_638 .

inst:IfcPositiveInteger_List_1495
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1496
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1497
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1495
        list:hasContents  inst:INTEGER_249 ;
        list:hasNext      inst:IfcPositiveInteger_List_1496 .

inst:IfcPositiveInteger_List_1496
        list:hasContents  inst:IfcPositiveInteger_642 ;
        list:hasNext      inst:IfcPositiveInteger_List_1497 .

inst:IfcPositiveInteger_List_1497
        list:hasContents  inst:IfcPositiveInteger_626 .

inst:IfcPositiveInteger_List_1498
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1499
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1500
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1498
        list:hasContents  inst:IfcPositiveInteger_646 ;
        list:hasNext      inst:IfcPositiveInteger_List_1499 .

inst:IfcPositiveInteger_List_1499
        list:hasContents  inst:IfcPositiveInteger_634 ;
        list:hasNext      inst:IfcPositiveInteger_List_1500 .

inst:IfcPositiveInteger_List_1500
        list:hasContents  inst:IfcPositiveInteger_630 .

inst:IfcPositiveInteger_List_1501
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1502
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1503
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1501
        list:hasContents  inst:IfcPositiveInteger_646 ;
        list:hasNext      inst:IfcPositiveInteger_List_1502 .

inst:IfcPositiveInteger_List_1502
        list:hasContents  inst:IfcPositiveInteger_650 ;
        list:hasNext      inst:IfcPositiveInteger_List_1503 .

inst:IfcPositiveInteger_List_1503
        list:hasContents  inst:IfcPositiveInteger_634 .

inst:IfcPositiveInteger_List_1504
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1505
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1506
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1504
        list:hasContents  inst:IfcDimensionCount_238 ;
        list:hasNext      inst:IfcPositiveInteger_List_1505 .

inst:IfcPositiveInteger_List_1505
        list:hasContents  inst:IfcPositiveInteger_638 ;
        list:hasNext      inst:IfcPositiveInteger_List_1506 .

inst:IfcPositiveInteger_List_1506
        list:hasContents  inst:IfcPositiveInteger_654 .

inst:IfcPositiveInteger_List_1507
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1508
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1509
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1507
        list:hasContents  inst:INTEGER_249 ;
        list:hasNext      inst:IfcPositiveInteger_List_1508 .

inst:IfcPositiveInteger_List_1508
        list:hasContents  inst:IfcPositiveInteger_658 ;
        list:hasNext      inst:IfcPositiveInteger_List_1509 .

inst:IfcPositiveInteger_List_1509
        list:hasContents  inst:IfcPositiveInteger_642 .

inst:IfcPositiveInteger_List_1510
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1511
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1512
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1510
        list:hasContents  inst:IfcPositiveInteger_662 ;
        list:hasNext      inst:IfcPositiveInteger_List_1511 .

inst:IfcPositiveInteger_List_1511
        list:hasContents  inst:IfcPositiveInteger_650 ;
        list:hasNext      inst:IfcPositiveInteger_List_1512 .

inst:IfcPositiveInteger_List_1512
        list:hasContents  inst:IfcPositiveInteger_646 .

inst:IfcPositiveInteger_List_1513
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1514
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1515
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1513
        list:hasContents  inst:IfcPositiveInteger_662 ;
        list:hasNext      inst:IfcPositiveInteger_List_1514 .

inst:IfcPositiveInteger_List_1514
        list:hasContents  inst:IfcPositiveInteger_666 ;
        list:hasNext      inst:IfcPositiveInteger_List_1515 .

inst:IfcPositiveInteger_List_1515
        list:hasContents  inst:IfcPositiveInteger_650 .

inst:IfcPositiveInteger_List_1516
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1517
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1518
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1516
        list:hasContents  inst:IfcDimensionCount_238 ;
        list:hasNext      inst:IfcPositiveInteger_List_1517 .

inst:IfcPositiveInteger_List_1517
        list:hasContents  inst:IfcPositiveInteger_654 ;
        list:hasNext      inst:IfcPositiveInteger_List_1518 .

inst:IfcPositiveInteger_List_1518
        list:hasContents  inst:IfcPositiveInteger_670 .

inst:IfcPositiveInteger_List_1519
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1520
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1521
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1519
        list:hasContents  inst:INTEGER_249 ;
        list:hasNext      inst:IfcPositiveInteger_List_1520 .

inst:IfcPositiveInteger_List_1520
        list:hasContents  inst:IfcPositiveInteger_674 ;
        list:hasNext      inst:IfcPositiveInteger_List_1521 .

inst:IfcPositiveInteger_List_1521
        list:hasContents  inst:IfcPositiveInteger_658 .

inst:IfcPositiveInteger_List_1522
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1523
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1524
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1522
        list:hasContents  inst:IfcPositiveInteger_678 ;
        list:hasNext      inst:IfcPositiveInteger_List_1523 .

inst:IfcPositiveInteger_List_1523
        list:hasContents  inst:IfcPositiveInteger_666 ;
        list:hasNext      inst:IfcPositiveInteger_List_1524 .

inst:IfcPositiveInteger_List_1524
        list:hasContents  inst:IfcPositiveInteger_662 .

inst:IfcPositiveInteger_List_1525
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1526
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1527
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1525
        list:hasContents  inst:IfcPositiveInteger_678 ;
        list:hasNext      inst:IfcPositiveInteger_List_1526 .

inst:IfcPositiveInteger_List_1526
        list:hasContents  inst:IfcPositiveInteger_682 ;
        list:hasNext      inst:IfcPositiveInteger_List_1527 .

inst:IfcPositiveInteger_List_1527
        list:hasContents  inst:IfcPositiveInteger_666 .

inst:IfcPositiveInteger_List_1528
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1529
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1530
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1528
        list:hasContents  inst:IfcDimensionCount_238 ;
        list:hasNext      inst:IfcPositiveInteger_List_1529 .

inst:IfcPositiveInteger_List_1529
        list:hasContents  inst:IfcPositiveInteger_670 ;
        list:hasNext      inst:IfcPositiveInteger_List_1530 .

inst:IfcPositiveInteger_List_1530
        list:hasContents  inst:IfcPositiveInteger_686 .

inst:IfcPositiveInteger_List_1531
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1532
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1533
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1531
        list:hasContents  inst:INTEGER_249 ;
        list:hasNext      inst:IfcPositiveInteger_List_1532 .

inst:IfcPositiveInteger_List_1532
        list:hasContents  inst:IfcPositiveInteger_690 ;
        list:hasNext      inst:IfcPositiveInteger_List_1533 .

inst:IfcPositiveInteger_List_1533
        list:hasContents  inst:IfcPositiveInteger_674 .

inst:IfcPositiveInteger_List_1534
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1535
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1536
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1534
        list:hasContents  inst:IfcPositiveInteger_694 ;
        list:hasNext      inst:IfcPositiveInteger_List_1535 .

inst:IfcPositiveInteger_List_1535
        list:hasContents  inst:IfcPositiveInteger_682 ;
        list:hasNext      inst:IfcPositiveInteger_List_1536 .

inst:IfcPositiveInteger_List_1536
        list:hasContents  inst:IfcPositiveInteger_678 .

inst:IfcPositiveInteger_List_1537
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1538
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1539
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1537
        list:hasContents  inst:IfcPositiveInteger_694 ;
        list:hasNext      inst:IfcPositiveInteger_List_1538 .

inst:IfcPositiveInteger_List_1538
        list:hasContents  inst:IfcPositiveInteger_698 ;
        list:hasNext      inst:IfcPositiveInteger_List_1539 .

inst:IfcPositiveInteger_List_1539
        list:hasContents  inst:IfcPositiveInteger_682 .

inst:IfcPositiveInteger_List_1540
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1541
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1542
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1540
        list:hasContents  inst:IfcDimensionCount_238 ;
        list:hasNext      inst:IfcPositiveInteger_List_1541 .

inst:IfcPositiveInteger_List_1541
        list:hasContents  inst:IfcPositiveInteger_686 ;
        list:hasNext      inst:IfcPositiveInteger_List_1542 .

inst:IfcPositiveInteger_List_1542
        list:hasContents  inst:IfcPositiveInteger_702 .

inst:IfcPositiveInteger_List_1543
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1544
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1545
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1543
        list:hasContents  inst:INTEGER_249 ;
        list:hasNext      inst:IfcPositiveInteger_List_1544 .

inst:IfcPositiveInteger_List_1544
        list:hasContents  inst:IfcPositiveInteger_706 ;
        list:hasNext      inst:IfcPositiveInteger_List_1545 .

inst:IfcPositiveInteger_List_1545
        list:hasContents  inst:IfcPositiveInteger_690 .

inst:IfcPositiveInteger_List_1546
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1547
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1548
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1546
        list:hasContents  inst:IfcPositiveInteger_710 ;
        list:hasNext      inst:IfcPositiveInteger_List_1547 .

inst:IfcPositiveInteger_List_1547
        list:hasContents  inst:IfcPositiveInteger_698 ;
        list:hasNext      inst:IfcPositiveInteger_List_1548 .

inst:IfcPositiveInteger_List_1548
        list:hasContents  inst:IfcPositiveInteger_694 .

inst:IfcPositiveInteger_List_1549
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1550
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1551
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1549
        list:hasContents  inst:IfcPositiveInteger_710 ;
        list:hasNext      inst:IfcPositiveInteger_List_1550 .

inst:IfcPositiveInteger_List_1550
        list:hasContents  inst:IfcPositiveInteger_714 ;
        list:hasNext      inst:IfcPositiveInteger_List_1551 .

inst:IfcPositiveInteger_List_1551
        list:hasContents  inst:IfcPositiveInteger_698 .

inst:IfcPositiveInteger_List_1552
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1553
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1554
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1552
        list:hasContents  inst:IfcDimensionCount_238 ;
        list:hasNext      inst:IfcPositiveInteger_List_1553 .

inst:IfcPositiveInteger_List_1553
        list:hasContents  inst:IfcPositiveInteger_702 ;
        list:hasNext      inst:IfcPositiveInteger_List_1554 .

inst:IfcPositiveInteger_List_1554
        list:hasContents  inst:IfcPositiveInteger_718 .

inst:IfcPositiveInteger_List_1555
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1556
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1557
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1555
        list:hasContents  inst:INTEGER_249 ;
        list:hasNext      inst:IfcPositiveInteger_List_1556 .

inst:IfcPositiveInteger_List_1556
        list:hasContents  inst:IfcPositiveInteger_722 ;
        list:hasNext      inst:IfcPositiveInteger_List_1557 .

inst:IfcPositiveInteger_List_1557
        list:hasContents  inst:IfcPositiveInteger_706 .

inst:IfcPositiveInteger_List_1558
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1559
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1560
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1558
        list:hasContents  inst:IfcPositiveInteger_726 ;
        list:hasNext      inst:IfcPositiveInteger_List_1559 .

inst:IfcPositiveInteger_List_1559
        list:hasContents  inst:IfcPositiveInteger_714 ;
        list:hasNext      inst:IfcPositiveInteger_List_1560 .

inst:IfcPositiveInteger_List_1560
        list:hasContents  inst:IfcPositiveInteger_710 .

inst:IfcPositiveInteger_List_1561
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1562
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1563
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1561
        list:hasContents  inst:IfcPositiveInteger_726 ;
        list:hasNext      inst:IfcPositiveInteger_List_1562 .

inst:IfcPositiveInteger_List_1562
        list:hasContents  inst:IfcPositiveInteger_730 ;
        list:hasNext      inst:IfcPositiveInteger_List_1563 .

inst:IfcPositiveInteger_List_1563
        list:hasContents  inst:IfcPositiveInteger_714 .

inst:IfcPositiveInteger_List_1564
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1565
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1566
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1564
        list:hasContents  inst:IfcDimensionCount_238 ;
        list:hasNext      inst:IfcPositiveInteger_List_1565 .

inst:IfcPositiveInteger_List_1565
        list:hasContents  inst:IfcPositiveInteger_718 ;
        list:hasNext      inst:IfcPositiveInteger_List_1566 .

inst:IfcPositiveInteger_List_1566
        list:hasContents  inst:IfcPositiveInteger_734 .

inst:IfcPositiveInteger_List_1567
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1568
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1569
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1567
        list:hasContents  inst:INTEGER_249 ;
        list:hasNext      inst:IfcPositiveInteger_List_1568 .

inst:IfcPositiveInteger_List_1568
        list:hasContents  inst:IfcPositiveInteger_738 ;
        list:hasNext      inst:IfcPositiveInteger_List_1569 .

inst:IfcPositiveInteger_List_1569
        list:hasContents  inst:IfcPositiveInteger_722 .

inst:IfcPositiveInteger_List_1570
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1571
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1572
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1570
        list:hasContents  inst:IfcPositiveInteger_742 ;
        list:hasNext      inst:IfcPositiveInteger_List_1571 .

inst:IfcPositiveInteger_List_1571
        list:hasContents  inst:IfcPositiveInteger_730 ;
        list:hasNext      inst:IfcPositiveInteger_List_1572 .

inst:IfcPositiveInteger_List_1572
        list:hasContents  inst:IfcPositiveInteger_726 .

inst:IfcPositiveInteger_List_1573
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1574
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1575
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1573
        list:hasContents  inst:IfcPositiveInteger_742 ;
        list:hasNext      inst:IfcPositiveInteger_List_1574 .

inst:IfcPositiveInteger_List_1574
        list:hasContents  inst:IfcPositiveInteger_746 ;
        list:hasNext      inst:IfcPositiveInteger_List_1575 .

inst:IfcPositiveInteger_List_1575
        list:hasContents  inst:IfcPositiveInteger_730 .

inst:IfcPositiveInteger_List_1576
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1577
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1578
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1576
        list:hasContents  inst:IfcDimensionCount_238 ;
        list:hasNext      inst:IfcPositiveInteger_List_1577 .

inst:IfcPositiveInteger_List_1577
        list:hasContents  inst:IfcPositiveInteger_734 ;
        list:hasNext      inst:IfcPositiveInteger_List_1578 .

inst:IfcPositiveInteger_List_1578
        list:hasContents  inst:IfcPositiveInteger_750 .

inst:IfcPositiveInteger_List_1579
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1580
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1581
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1579
        list:hasContents  inst:INTEGER_249 ;
        list:hasNext      inst:IfcPositiveInteger_List_1580 .

inst:IfcPositiveInteger_List_1580
        list:hasContents  inst:IfcPositiveInteger_754 ;
        list:hasNext      inst:IfcPositiveInteger_List_1581 .

inst:IfcPositiveInteger_List_1581
        list:hasContents  inst:IfcPositiveInteger_738 .

inst:IfcPositiveInteger_List_1582
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1583
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1584
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1582
        list:hasContents  inst:IfcPositiveInteger_758 ;
        list:hasNext      inst:IfcPositiveInteger_List_1583 .

inst:IfcPositiveInteger_List_1583
        list:hasContents  inst:IfcPositiveInteger_746 ;
        list:hasNext      inst:IfcPositiveInteger_List_1584 .

inst:IfcPositiveInteger_List_1584
        list:hasContents  inst:IfcPositiveInteger_742 .

inst:IfcPositiveInteger_List_1585
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1586
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1587
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1585
        list:hasContents  inst:IfcPositiveInteger_758 ;
        list:hasNext      inst:IfcPositiveInteger_List_1586 .

inst:IfcPositiveInteger_List_1586
        list:hasContents  inst:IfcPositiveInteger_762 ;
        list:hasNext      inst:IfcPositiveInteger_List_1587 .

inst:IfcPositiveInteger_List_1587
        list:hasContents  inst:IfcPositiveInteger_746 .

inst:IfcPositiveInteger_List_1588
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1589
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1590
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1588
        list:hasContents  inst:IfcDimensionCount_238 ;
        list:hasNext      inst:IfcPositiveInteger_List_1589 .

inst:IfcPositiveInteger_List_1589
        list:hasContents  inst:IfcPositiveInteger_750 ;
        list:hasNext      inst:IfcPositiveInteger_List_1590 .

inst:IfcPositiveInteger_List_1590
        list:hasContents  inst:IfcPositiveInteger_766 .

inst:IfcPositiveInteger_List_1591
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1592
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1593
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1591
        list:hasContents  inst:INTEGER_249 ;
        list:hasNext      inst:IfcPositiveInteger_List_1592 .

inst:IfcPositiveInteger_List_1592
        list:hasContents  inst:IfcPositiveInteger_770 ;
        list:hasNext      inst:IfcPositiveInteger_List_1593 .

inst:IfcPositiveInteger_List_1593
        list:hasContents  inst:IfcPositiveInteger_754 .

inst:IfcPositiveInteger_List_1594
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1595
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1596
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1594
        list:hasContents  inst:IfcPositiveInteger_774 ;
        list:hasNext      inst:IfcPositiveInteger_List_1595 .

inst:IfcPositiveInteger_List_1595
        list:hasContents  inst:IfcPositiveInteger_762 ;
        list:hasNext      inst:IfcPositiveInteger_List_1596 .

inst:IfcPositiveInteger_List_1596
        list:hasContents  inst:IfcPositiveInteger_758 .

inst:IfcPositiveInteger_List_1597
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1598
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1599
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1597
        list:hasContents  inst:IfcPositiveInteger_774 ;
        list:hasNext      inst:IfcPositiveInteger_List_1598 .

inst:IfcPositiveInteger_List_1598
        list:hasContents  inst:IfcPositiveInteger_778 ;
        list:hasNext      inst:IfcPositiveInteger_List_1599 .

inst:IfcPositiveInteger_List_1599
        list:hasContents  inst:IfcPositiveInteger_762 .

inst:IfcPositiveInteger_List_1600
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1601
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1602
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1600
        list:hasContents  inst:IfcDimensionCount_238 ;
        list:hasNext      inst:IfcPositiveInteger_List_1601 .

inst:IfcPositiveInteger_List_1601
        list:hasContents  inst:IfcPositiveInteger_766 ;
        list:hasNext      inst:IfcPositiveInteger_List_1602 .

inst:IfcPositiveInteger_List_1602
        list:hasContents  inst:IfcPositiveInteger_782 .

inst:IfcPositiveInteger_List_1603
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1604
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1605
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1603
        list:hasContents  inst:INTEGER_249 ;
        list:hasNext      inst:IfcPositiveInteger_List_1604 .

inst:IfcPositiveInteger_List_1604
        list:hasContents  inst:IfcPositiveInteger_786 ;
        list:hasNext      inst:IfcPositiveInteger_List_1605 .

inst:IfcPositiveInteger_List_1605
        list:hasContents  inst:IfcPositiveInteger_770 .

inst:IfcPositiveInteger_List_1606
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1607
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1608
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1606
        list:hasContents  inst:IfcPositiveInteger_790 ;
        list:hasNext      inst:IfcPositiveInteger_List_1607 .

inst:IfcPositiveInteger_List_1607
        list:hasContents  inst:IfcPositiveInteger_778 ;
        list:hasNext      inst:IfcPositiveInteger_List_1608 .

inst:IfcPositiveInteger_List_1608
        list:hasContents  inst:IfcPositiveInteger_774 .

inst:IfcPositiveInteger_List_1609
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1610
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1611
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1609
        list:hasContents  inst:IfcPositiveInteger_790 ;
        list:hasNext      inst:IfcPositiveInteger_List_1610 .

inst:IfcPositiveInteger_List_1610
        list:hasContents  inst:IfcPositiveInteger_794 ;
        list:hasNext      inst:IfcPositiveInteger_List_1611 .

inst:IfcPositiveInteger_List_1611
        list:hasContents  inst:IfcPositiveInteger_778 .

inst:IfcPositiveInteger_List_1612
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1613
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1614
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1612
        list:hasContents  inst:IfcDimensionCount_238 ;
        list:hasNext      inst:IfcPositiveInteger_List_1613 .

inst:IfcPositiveInteger_List_1613
        list:hasContents  inst:IfcPositiveInteger_782 ;
        list:hasNext      inst:IfcPositiveInteger_List_1614 .

inst:IfcPositiveInteger_List_1614
        list:hasContents  inst:IfcPositiveInteger_798 .

inst:IfcPositiveInteger_List_1615
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1616
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1617
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1615
        list:hasContents  inst:INTEGER_249 ;
        list:hasNext      inst:IfcPositiveInteger_List_1616 .

inst:IfcPositiveInteger_List_1616
        list:hasContents  inst:IfcPositiveInteger_802 ;
        list:hasNext      inst:IfcPositiveInteger_List_1617 .

inst:IfcPositiveInteger_List_1617
        list:hasContents  inst:IfcPositiveInteger_786 .

inst:IfcPositiveInteger_List_1618
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1619
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1620
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1618
        list:hasContents  inst:IfcPositiveInteger_806 ;
        list:hasNext      inst:IfcPositiveInteger_List_1619 .

inst:IfcPositiveInteger_List_1619
        list:hasContents  inst:IfcPositiveInteger_794 ;
        list:hasNext      inst:IfcPositiveInteger_List_1620 .

inst:IfcPositiveInteger_List_1620
        list:hasContents  inst:IfcPositiveInteger_790 .

inst:IfcPositiveInteger_List_1621
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1622
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1623
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1621
        list:hasContents  inst:IfcPositiveInteger_806 ;
        list:hasNext      inst:IfcPositiveInteger_List_1622 .

inst:IfcPositiveInteger_List_1622
        list:hasContents  inst:IfcPositiveInteger_810 ;
        list:hasNext      inst:IfcPositiveInteger_List_1623 .

inst:IfcPositiveInteger_List_1623
        list:hasContents  inst:IfcPositiveInteger_794 .

inst:IfcPositiveInteger_List_1624
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1625
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1626
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1624
        list:hasContents  inst:IfcDimensionCount_238 ;
        list:hasNext      inst:IfcPositiveInteger_List_1625 .

inst:IfcPositiveInteger_List_1625
        list:hasContents  inst:IfcPositiveInteger_798 ;
        list:hasNext      inst:IfcPositiveInteger_List_1626 .

inst:IfcPositiveInteger_List_1626
        list:hasContents  inst:IfcPositiveInteger_814 .

inst:IfcPositiveInteger_List_1627
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1628
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1629
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1627
        list:hasContents  inst:INTEGER_249 ;
        list:hasNext      inst:IfcPositiveInteger_List_1628 .

inst:IfcPositiveInteger_List_1628
        list:hasContents  inst:IfcPositiveInteger_818 ;
        list:hasNext      inst:IfcPositiveInteger_List_1629 .

inst:IfcPositiveInteger_List_1629
        list:hasContents  inst:IfcPositiveInteger_802 .

inst:IfcPositiveInteger_List_1630
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1631
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1632
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1630
        list:hasContents  inst:IfcPositiveInteger_822 ;
        list:hasNext      inst:IfcPositiveInteger_List_1631 .

inst:IfcPositiveInteger_List_1631
        list:hasContents  inst:IfcPositiveInteger_810 ;
        list:hasNext      inst:IfcPositiveInteger_List_1632 .

inst:IfcPositiveInteger_List_1632
        list:hasContents  inst:IfcPositiveInteger_806 .

inst:IfcPositiveInteger_List_1633
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1634
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1635
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1633
        list:hasContents  inst:IfcPositiveInteger_822 ;
        list:hasNext      inst:IfcPositiveInteger_List_1634 .

inst:IfcPositiveInteger_List_1634
        list:hasContents  inst:IfcPositiveInteger_826 ;
        list:hasNext      inst:IfcPositiveInteger_List_1635 .

inst:IfcPositiveInteger_List_1635
        list:hasContents  inst:IfcPositiveInteger_810 .

inst:IfcPositiveInteger_List_1636
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1637
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1638
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_1636
        list:hasContents  inst:IfcDimensionCount_238 ;
        list:hasNext      inst:IfcPositiveInteger_List_1637 .

inst:IfcPositiveInteger_List_1637
        list:hasContents  inst:IfcPositiveInteger_814 ;
        list:hasNext      inst:IfcPositiveInteger_List_1638 .

inst:IfcPositiveInteger_List_1638
        list:hasContents  inst:IfcPositiveInteger_830 .

inst:IfcPositiveInteger_List_List_1639
        rdf:type  ifc:IfcPositiveInteger_List_List .

inst:IfcIndexedTriangleTextureMap_213
        ifc:texCoordIndex_IfcIndexedTriangleTextureMap  inst:IfcPositiveInteger_List_List_1639 .

inst:IfcPositiveInteger_List_List_1639
        list:hasContents  inst:IfcPositiveInteger_List_1447 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_1640 .

inst:IfcPositiveInteger_List_List_1640
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1450 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_1641 .

inst:IfcPositiveInteger_List_List_1641
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1453 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_1642 .

inst:IfcPositiveInteger_List_List_1642
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1456 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_1643 .

inst:IfcPositiveInteger_List_List_1643
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1459 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_1644 .

inst:IfcPositiveInteger_List_List_1644
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1462 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_1645 .

inst:IfcPositiveInteger_List_List_1645
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1465 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_1646 .

inst:IfcPositiveInteger_List_List_1646
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1468 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_1647 .

inst:IfcPositiveInteger_List_List_1647
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1471 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_1648 .

inst:IfcPositiveInteger_List_List_1648
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1474 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_1649 .

inst:IfcPositiveInteger_List_List_1649
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1477 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_1650 .

inst:IfcPositiveInteger_List_List_1650
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1480 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_1651 .

inst:IfcPositiveInteger_List_List_1651
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1483 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_1652 .

inst:IfcPositiveInteger_List_List_1652
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1486 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_1653 .

inst:IfcPositiveInteger_List_List_1653
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1489 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_1654 .

inst:IfcPositiveInteger_List_List_1654
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1492 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_1655 .

inst:IfcPositiveInteger_List_List_1655
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1495 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_1656 .

inst:IfcPositiveInteger_List_List_1656
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1498 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_1657 .

inst:IfcPositiveInteger_List_List_1657
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1501 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_1658 .

inst:IfcPositiveInteger_List_List_1658
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1504 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_1659 .

inst:IfcPositiveInteger_List_List_1659
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1507 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_1660 .

inst:IfcPositiveInteger_List_List_1660
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1510 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_1661 .

inst:IfcPositiveInteger_List_List_1661
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1513 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_1662 .

inst:IfcPositiveInteger_List_List_1662
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1516 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_1663 .

inst:IfcPositiveInteger_List_List_1663
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1519 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_1664 .

inst:IfcPositiveInteger_List_List_1664
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1522 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_1665 .

inst:IfcPositiveInteger_List_List_1665
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1525 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_1666 .

inst:IfcPositiveInteger_List_List_1666
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1528 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_1667 .

inst:IfcPositiveInteger_List_List_1667
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1531 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_1668 .

inst:IfcPositiveInteger_List_List_1668
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1534 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_1669 .

inst:IfcPositiveInteger_List_List_1669
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1537 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_1670 .

inst:IfcPositiveInteger_List_List_1670
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1540 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_1671 .

inst:IfcPositiveInteger_List_List_1671
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1543 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_1672 .

inst:IfcPositiveInteger_List_List_1672
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1546 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_1673 .

inst:IfcPositiveInteger_List_List_1673
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1549 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_1674 .

inst:IfcPositiveInteger_List_List_1674
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1552 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_1675 .

inst:IfcPositiveInteger_List_List_1675
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1555 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_1676 .

inst:IfcPositiveInteger_List_List_1676
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1558 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_1677 .

inst:IfcPositiveInteger_List_List_1677
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1561 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_1678 .

inst:IfcPositiveInteger_List_List_1678
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1564 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_1679 .

inst:IfcPositiveInteger_List_List_1679
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1567 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_1680 .

inst:IfcPositiveInteger_List_List_1680
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1570 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_1681 .

inst:IfcPositiveInteger_List_List_1681
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1573 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_1682 .

inst:IfcPositiveInteger_List_List_1682
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1576 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_1683 .

inst:IfcPositiveInteger_List_List_1683
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1579 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_1684 .

inst:IfcPositiveInteger_List_List_1684
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1582 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_1685 .

inst:IfcPositiveInteger_List_List_1685
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1585 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_1686 .

inst:IfcPositiveInteger_List_List_1686
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1588 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_1687 .

inst:IfcPositiveInteger_List_List_1687
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1591 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_1688 .

inst:IfcPositiveInteger_List_List_1688
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1594 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_1689 .

inst:IfcPositiveInteger_List_List_1689
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1597 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_1690 .

inst:IfcPositiveInteger_List_List_1690
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1600 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_1691 .

inst:IfcPositiveInteger_List_List_1691
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1603 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_1692 .

inst:IfcPositiveInteger_List_List_1692
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1606 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_1693 .

inst:IfcPositiveInteger_List_List_1693
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1609 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_1694 .

inst:IfcPositiveInteger_List_List_1694
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1612 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_1695 .

inst:IfcPositiveInteger_List_List_1695
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1615 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_1696 .

inst:IfcPositiveInteger_List_List_1696
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1618 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_1697 .

inst:IfcPositiveInteger_List_List_1697
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1621 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_1698 .

inst:IfcPositiveInteger_List_List_1698
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1624 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_1699 .

inst:IfcPositiveInteger_List_List_1699
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1627 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_1700 .

inst:IfcPositiveInteger_List_List_1700
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1630 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_1701 .

inst:IfcPositiveInteger_List_List_1701
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1633 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_1702 .

inst:IfcPositiveInteger_List_List_1702
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_1636 .

inst:IfcSurfaceStyle_214
        ifc:side_IfcSurfaceStyle  ifc:POSITIVE .

inst:IfcSurfaceStyleRendering_216
        rdf:type  ifc:IfcSurfaceStyleRendering .

inst:IfcSurfaceStyle_214
        ifc:styles_IfcSurfaceStyle  inst:IfcSurfaceStyleRendering_216 .

inst:IfcSurfaceStyleWithTextures_217
        rdf:type  ifc:IfcSurfaceStyleWithTextures .

inst:IfcSurfaceStyle_214
        ifc:styles_IfcSurfaceStyle  inst:IfcSurfaceStyleWithTextures_217 .

inst:IfcParameterValue_List_1703
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_1704
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_1705
        rdf:type           ifc:IfcParameterValue ;
        express:hasDouble  "0.5"^^xsd:double .

inst:IfcParameterValue_List_1703
        list:hasContents  inst:IfcParameterValue_1705 ;
        list:hasNext      inst:IfcParameterValue_List_1704 .

inst:IfcParameterValue_List_1704
        list:hasContents  inst:IfcParameterValue_1705 .

inst:IfcParameterValue_List_1706
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_1707
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_1706
        list:hasContents  inst:IfcParameterValue_1705 ;
        list:hasNext      inst:IfcParameterValue_List_1707 .

inst:IfcParameterValue_List_1707
        list:hasContents  inst:IfcParameterValue_1705 .

inst:IfcParameterValue_List_1708
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_1709
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_1708
        list:hasContents  inst:IfcParameterValue_287 ;
        list:hasNext      inst:IfcParameterValue_List_1709 .

inst:IfcParameterValue_List_1709
        list:hasContents  inst:IfcParameterValue_1705 .

inst:IfcParameterValue_List_1710
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_1711
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_1712
        rdf:type           ifc:IfcParameterValue ;
        express:hasDouble  "0.961939766255643"^^xsd:double .

inst:IfcParameterValue_List_1710
        list:hasContents  inst:IfcParameterValue_1712 ;
        list:hasNext      inst:IfcParameterValue_List_1711 .

inst:IfcParameterValue_1713
        rdf:type           ifc:IfcParameterValue ;
        express:hasDouble  "0.308658283817455"^^xsd:double .

inst:IfcParameterValue_List_1711
        list:hasContents  inst:IfcParameterValue_1713 .

inst:IfcParameterValue_List_1714
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_1715
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_1716
        rdf:type           ifc:IfcParameterValue ;
        express:hasDouble  "0.853553390593274"^^xsd:double .

inst:IfcParameterValue_List_1714
        list:hasContents  inst:IfcParameterValue_1716 ;
        list:hasNext      inst:IfcParameterValue_List_1715 .

inst:IfcParameterValue_1717
        rdf:type           ifc:IfcParameterValue ;
        express:hasDouble  "0.146446609406726"^^xsd:double .

inst:IfcParameterValue_List_1715
        list:hasContents  inst:IfcParameterValue_1717 .

inst:IfcParameterValue_List_1718
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_1719
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_1720
        rdf:type           ifc:IfcParameterValue ;
        express:hasDouble  "0.691341716182545"^^xsd:double .

inst:IfcParameterValue_List_1718
        list:hasContents  inst:IfcParameterValue_1720 ;
        list:hasNext      inst:IfcParameterValue_List_1719 .

inst:IfcParameterValue_1721
        rdf:type           ifc:IfcParameterValue ;
        express:hasDouble  "0.0380602337443566"^^xsd:double .

inst:IfcParameterValue_List_1719
        list:hasContents  inst:IfcParameterValue_1721 .

inst:IfcParameterValue_List_1722
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_1723
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_1722
        list:hasContents  inst:IfcParameterValue_1705 ;
        list:hasNext      inst:IfcParameterValue_List_1723 .

inst:IfcParameterValue_List_1723
        list:hasContents  inst:IfcLengthMeasure_246 .

inst:IfcParameterValue_List_1724
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_1725
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_1724
        list:hasContents  inst:IfcParameterValue_1713 ;
        list:hasNext      inst:IfcParameterValue_List_1725 .

inst:IfcParameterValue_List_1725
        list:hasContents  inst:IfcParameterValue_1721 .

inst:IfcParameterValue_List_1726
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_1727
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_1726
        list:hasContents  inst:IfcParameterValue_1717 ;
        list:hasNext      inst:IfcParameterValue_List_1727 .

inst:IfcParameterValue_List_1727
        list:hasContents  inst:IfcParameterValue_1717 .

inst:IfcParameterValue_List_1728
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_1729
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_1728
        list:hasContents  inst:IfcParameterValue_1721 ;
        list:hasNext      inst:IfcParameterValue_List_1729 .

inst:IfcParameterValue_List_1729
        list:hasContents  inst:IfcParameterValue_1713 .

inst:IfcParameterValue_List_1730
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_1731
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_1730
        list:hasContents  inst:IfcLengthMeasure_246 ;
        list:hasNext      inst:IfcParameterValue_List_1731 .

inst:IfcParameterValue_List_1731
        list:hasContents  inst:IfcParameterValue_1705 .

inst:IfcParameterValue_List_1732
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_1733
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_1732
        list:hasContents  inst:IfcParameterValue_1721 ;
        list:hasNext      inst:IfcParameterValue_List_1733 .

inst:IfcParameterValue_List_1733
        list:hasContents  inst:IfcParameterValue_1720 .

inst:IfcParameterValue_List_1734
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_1735
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_1734
        list:hasContents  inst:IfcParameterValue_1717 ;
        list:hasNext      inst:IfcParameterValue_List_1735 .

inst:IfcParameterValue_List_1735
        list:hasContents  inst:IfcParameterValue_1716 .

inst:IfcParameterValue_List_1736
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_1737
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_1736
        list:hasContents  inst:IfcParameterValue_1713 ;
        list:hasNext      inst:IfcParameterValue_List_1737 .

inst:IfcParameterValue_List_1737
        list:hasContents  inst:IfcParameterValue_1712 .

inst:IfcParameterValue_List_1738
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_1739
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_1738
        list:hasContents  inst:IfcParameterValue_1705 ;
        list:hasNext      inst:IfcParameterValue_List_1739 .

inst:IfcParameterValue_List_1739
        list:hasContents  inst:IfcParameterValue_287 .

inst:IfcParameterValue_List_1740
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_1741
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_1740
        list:hasContents  inst:IfcParameterValue_1720 ;
        list:hasNext      inst:IfcParameterValue_List_1741 .

inst:IfcParameterValue_List_1741
        list:hasContents  inst:IfcParameterValue_1712 .

inst:IfcParameterValue_List_1742
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_1743
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_1742
        list:hasContents  inst:IfcParameterValue_1716 ;
        list:hasNext      inst:IfcParameterValue_List_1743 .

inst:IfcParameterValue_List_1743
        list:hasContents  inst:IfcParameterValue_1716 .

inst:IfcParameterValue_List_1744
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_1745
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_1744
        list:hasContents  inst:IfcParameterValue_1712 ;
        list:hasNext      inst:IfcParameterValue_List_1745 .

inst:IfcParameterValue_List_1745
        list:hasContents  inst:IfcParameterValue_1720 .

inst:IfcParameterValue_List_1746
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_1747
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_1746
        list:hasContents  inst:IfcParameterValue_287 ;
        list:hasNext      inst:IfcParameterValue_List_1747 .

inst:IfcParameterValue_List_1747
        list:hasContents  inst:IfcParameterValue_1705 .

inst:IfcParameterValue_List_1748
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_1749
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_1748
        list:hasContents  inst:IfcParameterValue_287 ;
        list:hasNext      inst:IfcParameterValue_List_1749 .

inst:IfcParameterValue_List_1749
        list:hasContents  inst:IfcParameterValue_1705 .

inst:IfcParameterValue_List_1750
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_1751
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_1750
        list:hasContents  inst:IfcParameterValue_1712 ;
        list:hasNext      inst:IfcParameterValue_List_1751 .

inst:IfcParameterValue_List_1751
        list:hasContents  inst:IfcParameterValue_1720 .

inst:IfcParameterValue_List_1752
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_1753
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_1752
        list:hasContents  inst:IfcParameterValue_1716 ;
        list:hasNext      inst:IfcParameterValue_List_1753 .

inst:IfcParameterValue_List_1753
        list:hasContents  inst:IfcParameterValue_1716 .

inst:IfcParameterValue_List_1754
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_1755
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_1754
        list:hasContents  inst:IfcParameterValue_1720 ;
        list:hasNext      inst:IfcParameterValue_List_1755 .

inst:IfcParameterValue_List_1755
        list:hasContents  inst:IfcParameterValue_1712 .

inst:IfcParameterValue_List_1756
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_1757
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_1756
        list:hasContents  inst:IfcParameterValue_1705 ;
        list:hasNext      inst:IfcParameterValue_List_1757 .

inst:IfcParameterValue_List_1757
        list:hasContents  inst:IfcParameterValue_287 .

inst:IfcParameterValue_List_1758
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_1759
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_1758
        list:hasContents  inst:IfcParameterValue_1713 ;
        list:hasNext      inst:IfcParameterValue_List_1759 .

inst:IfcParameterValue_List_1759
        list:hasContents  inst:IfcParameterValue_1712 .

inst:IfcParameterValue_List_1760
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_1761
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_1760
        list:hasContents  inst:IfcParameterValue_1717 ;
        list:hasNext      inst:IfcParameterValue_List_1761 .

inst:IfcParameterValue_List_1761
        list:hasContents  inst:IfcParameterValue_1716 .

inst:IfcParameterValue_List_1762
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_1763
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_1762
        list:hasContents  inst:IfcParameterValue_1721 ;
        list:hasNext      inst:IfcParameterValue_List_1763 .

inst:IfcParameterValue_List_1763
        list:hasContents  inst:IfcParameterValue_1720 .

inst:IfcParameterValue_List_1764
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_1765
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_1764
        list:hasContents  inst:IfcLengthMeasure_246 ;
        list:hasNext      inst:IfcParameterValue_List_1765 .

inst:IfcParameterValue_List_1765
        list:hasContents  inst:IfcParameterValue_1705 .

inst:IfcParameterValue_List_1766
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_1767
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_1766
        list:hasContents  inst:IfcParameterValue_1721 ;
        list:hasNext      inst:IfcParameterValue_List_1767 .

inst:IfcParameterValue_List_1767
        list:hasContents  inst:IfcParameterValue_1713 .

inst:IfcParameterValue_List_1768
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_1769
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_1768
        list:hasContents  inst:IfcParameterValue_1717 ;
        list:hasNext      inst:IfcParameterValue_List_1769 .

inst:IfcParameterValue_List_1769
        list:hasContents  inst:IfcParameterValue_1717 .

inst:IfcParameterValue_List_1770
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_1771
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_1770
        list:hasContents  inst:IfcParameterValue_1713 ;
        list:hasNext      inst:IfcParameterValue_List_1771 .

inst:IfcParameterValue_1772
        rdf:type           ifc:IfcParameterValue ;
        express:hasDouble  "0.0380602337443567"^^xsd:double .

inst:IfcParameterValue_List_1771
        list:hasContents  inst:IfcParameterValue_1772 .

inst:IfcParameterValue_List_1773
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_1774
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_1773
        list:hasContents  inst:IfcParameterValue_1705 ;
        list:hasNext      inst:IfcParameterValue_List_1774 .

inst:IfcParameterValue_List_1774
        list:hasContents  inst:IfcLengthMeasure_246 .

inst:IfcParameterValue_List_1775
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_1776
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_1775
        list:hasContents  inst:IfcParameterValue_1720 ;
        list:hasNext      inst:IfcParameterValue_List_1776 .

inst:IfcParameterValue_List_1776
        list:hasContents  inst:IfcParameterValue_1772 .

inst:IfcParameterValue_List_1777
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_1778
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_1777
        list:hasContents  inst:IfcParameterValue_1716 ;
        list:hasNext      inst:IfcParameterValue_List_1778 .

inst:IfcParameterValue_List_1778
        list:hasContents  inst:IfcParameterValue_1717 .

inst:IfcParameterValue_List_1779
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_1780
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_1779
        list:hasContents  inst:IfcParameterValue_1712 ;
        list:hasNext      inst:IfcParameterValue_List_1780 .

inst:IfcParameterValue_List_1780
        list:hasContents  inst:IfcParameterValue_1713 .

inst:IfcParameterValue_List_1781
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_1782
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_1781
        list:hasContents  inst:IfcParameterValue_287 ;
        list:hasNext      inst:IfcParameterValue_List_1782 .

inst:IfcParameterValue_List_1782
        list:hasContents  inst:IfcParameterValue_1705 .

inst:IfcParameterValue_List_1783
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_1784
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_1785
        rdf:type           ifc:IfcParameterValue ;
        express:hasDouble  "-0.25"^^xsd:double .

inst:IfcParameterValue_List_1783
        list:hasContents  inst:IfcParameterValue_1785 ;
        list:hasNext      inst:IfcParameterValue_List_1784 .

inst:IfcParameterValue_List_1784
        list:hasContents  inst:IfcLengthMeasure_246 .

inst:IfcParameterValue_List_1786
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_1787
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_1788
        rdf:type           ifc:IfcParameterValue ;
        express:hasDouble  "-0.1875"^^xsd:double .

inst:IfcParameterValue_List_1786
        list:hasContents  inst:IfcParameterValue_1788 ;
        list:hasNext      inst:IfcParameterValue_List_1787 .

inst:IfcParameterValue_List_1787
        list:hasContents  inst:IfcLengthMeasure_246 .

inst:IfcParameterValue_List_1789
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_1790
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_1791
        rdf:type           ifc:IfcParameterValue ;
        express:hasDouble  "-0.125"^^xsd:double .

inst:IfcParameterValue_List_1789
        list:hasContents  inst:IfcParameterValue_1791 ;
        list:hasNext      inst:IfcParameterValue_List_1790 .

inst:IfcParameterValue_List_1790
        list:hasContents  inst:IfcLengthMeasure_246 .

inst:IfcParameterValue_List_1792
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_1793
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_1794
        rdf:type           ifc:IfcParameterValue ;
        express:hasDouble  "-0.0625"^^xsd:double .

inst:IfcParameterValue_List_1792
        list:hasContents  inst:IfcParameterValue_1794 ;
        list:hasNext      inst:IfcParameterValue_List_1793 .

inst:IfcParameterValue_List_1793
        list:hasContents  inst:IfcLengthMeasure_246 .

inst:IfcParameterValue_List_1795
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_1796
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_1795
        list:hasContents  inst:IfcLengthMeasure_246 ;
        list:hasNext      inst:IfcParameterValue_List_1796 .

inst:IfcParameterValue_List_1796
        list:hasContents  inst:IfcLengthMeasure_246 .

inst:IfcParameterValue_List_1797
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_1798
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_1799
        rdf:type           ifc:IfcParameterValue ;
        express:hasDouble  "0.0625"^^xsd:double .

inst:IfcParameterValue_List_1797
        list:hasContents  inst:IfcParameterValue_1799 ;
        list:hasNext      inst:IfcParameterValue_List_1798 .

inst:IfcParameterValue_List_1798
        list:hasContents  inst:IfcLengthMeasure_246 .

inst:IfcParameterValue_List_1800
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_1801
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_1802
        rdf:type           ifc:IfcParameterValue ;
        express:hasDouble  "0.125"^^xsd:double .

inst:IfcParameterValue_List_1800
        list:hasContents  inst:IfcParameterValue_1802 ;
        list:hasNext      inst:IfcParameterValue_List_1801 .

inst:IfcParameterValue_List_1801
        list:hasContents  inst:IfcLengthMeasure_246 .

inst:IfcParameterValue_List_1803
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_1804
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_1805
        rdf:type           ifc:IfcParameterValue ;
        express:hasDouble  "0.1875"^^xsd:double .

inst:IfcParameterValue_List_1803
        list:hasContents  inst:IfcParameterValue_1805 ;
        list:hasNext      inst:IfcParameterValue_List_1804 .

inst:IfcParameterValue_List_1804
        list:hasContents  inst:IfcLengthMeasure_246 .

inst:IfcParameterValue_List_1806
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_1807
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_1808
        rdf:type           ifc:IfcParameterValue ;
        express:hasDouble  "0.25"^^xsd:double .

inst:IfcParameterValue_List_1806
        list:hasContents  inst:IfcParameterValue_1808 ;
        list:hasNext      inst:IfcParameterValue_List_1807 .

inst:IfcParameterValue_List_1807
        list:hasContents  inst:IfcLengthMeasure_246 .

inst:IfcParameterValue_List_1809
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_1810
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_1811
        rdf:type           ifc:IfcParameterValue ;
        express:hasDouble  "0.3125"^^xsd:double .

inst:IfcParameterValue_List_1809
        list:hasContents  inst:IfcParameterValue_1811 ;
        list:hasNext      inst:IfcParameterValue_List_1810 .

inst:IfcParameterValue_List_1810
        list:hasContents  inst:IfcLengthMeasure_246 .

inst:IfcParameterValue_List_1812
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_1813
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_1814
        rdf:type           ifc:IfcParameterValue ;
        express:hasDouble  "0.375"^^xsd:double .

inst:IfcParameterValue_List_1812
        list:hasContents  inst:IfcParameterValue_1814 ;
        list:hasNext      inst:IfcParameterValue_List_1813 .

inst:IfcParameterValue_List_1813
        list:hasContents  inst:IfcLengthMeasure_246 .

inst:IfcParameterValue_List_1815
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_1816
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_1817
        rdf:type           ifc:IfcParameterValue ;
        express:hasDouble  "0.4375"^^xsd:double .

inst:IfcParameterValue_List_1815
        list:hasContents  inst:IfcParameterValue_1817 ;
        list:hasNext      inst:IfcParameterValue_List_1816 .

inst:IfcParameterValue_List_1816
        list:hasContents  inst:IfcLengthMeasure_246 .

inst:IfcParameterValue_List_1818
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_1819
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_1818
        list:hasContents  inst:IfcParameterValue_1705 ;
        list:hasNext      inst:IfcParameterValue_List_1819 .

inst:IfcParameterValue_List_1819
        list:hasContents  inst:IfcLengthMeasure_246 .

inst:IfcParameterValue_List_1820
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_1821
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_1822
        rdf:type           ifc:IfcParameterValue ;
        express:hasDouble  "0.5625"^^xsd:double .

inst:IfcParameterValue_List_1820
        list:hasContents  inst:IfcParameterValue_1822 ;
        list:hasNext      inst:IfcParameterValue_List_1821 .

inst:IfcParameterValue_List_1821
        list:hasContents  inst:IfcLengthMeasure_246 .

inst:IfcParameterValue_List_1823
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_1824
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_1825
        rdf:type           ifc:IfcParameterValue ;
        express:hasDouble  "0.625"^^xsd:double .

inst:IfcParameterValue_List_1823
        list:hasContents  inst:IfcParameterValue_1825 ;
        list:hasNext      inst:IfcParameterValue_List_1824 .

inst:IfcParameterValue_List_1824
        list:hasContents  inst:IfcLengthMeasure_246 .

inst:IfcParameterValue_List_1826
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_1827
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_1828
        rdf:type           ifc:IfcParameterValue ;
        express:hasDouble  "0.6875"^^xsd:double .

inst:IfcParameterValue_List_1826
        list:hasContents  inst:IfcParameterValue_1828 ;
        list:hasNext      inst:IfcParameterValue_List_1827 .

inst:IfcParameterValue_List_1827
        list:hasContents  inst:IfcLengthMeasure_246 .

inst:IfcParameterValue_List_1829
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_1830
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_1831
        rdf:type           ifc:IfcParameterValue ;
        express:hasDouble  "0.75"^^xsd:double .

inst:IfcParameterValue_List_1829
        list:hasContents  inst:IfcParameterValue_1831 ;
        list:hasNext      inst:IfcParameterValue_List_1830 .

inst:IfcParameterValue_List_1830
        list:hasContents  inst:IfcLengthMeasure_246 .

inst:IfcParameterValue_List_1832
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_1833
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_1832
        list:hasContents  inst:IfcParameterValue_1785 ;
        list:hasNext      inst:IfcParameterValue_List_1833 .

inst:IfcParameterValue_List_1833
        list:hasContents  inst:IfcParameterValue_287 .

inst:IfcParameterValue_List_1834
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_1835
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_1834
        list:hasContents  inst:IfcParameterValue_1788 ;
        list:hasNext      inst:IfcParameterValue_List_1835 .

inst:IfcParameterValue_List_1835
        list:hasContents  inst:IfcParameterValue_287 .

inst:IfcParameterValue_List_1836
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_1837
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_1836
        list:hasContents  inst:IfcParameterValue_1791 ;
        list:hasNext      inst:IfcParameterValue_List_1837 .

inst:IfcParameterValue_List_1837
        list:hasContents  inst:IfcParameterValue_287 .

inst:IfcParameterValue_List_1838
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_1839
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_1838
        list:hasContents  inst:IfcParameterValue_1794 ;
        list:hasNext      inst:IfcParameterValue_List_1839 .

inst:IfcParameterValue_List_1839
        list:hasContents  inst:IfcParameterValue_287 .

inst:IfcParameterValue_List_1840
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_1841
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_1840
        list:hasContents  inst:IfcLengthMeasure_246 ;
        list:hasNext      inst:IfcParameterValue_List_1841 .

inst:IfcParameterValue_List_1841
        list:hasContents  inst:IfcParameterValue_287 .

inst:IfcParameterValue_List_1842
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_1843
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_1842
        list:hasContents  inst:IfcParameterValue_1799 ;
        list:hasNext      inst:IfcParameterValue_List_1843 .

inst:IfcParameterValue_List_1843
        list:hasContents  inst:IfcParameterValue_287 .

inst:IfcParameterValue_List_1844
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_1845
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_1844
        list:hasContents  inst:IfcParameterValue_1802 ;
        list:hasNext      inst:IfcParameterValue_List_1845 .

inst:IfcParameterValue_List_1845
        list:hasContents  inst:IfcParameterValue_287 .

inst:IfcParameterValue_List_1846
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_1847
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_1846
        list:hasContents  inst:IfcParameterValue_1805 ;
        list:hasNext      inst:IfcParameterValue_List_1847 .

inst:IfcParameterValue_List_1847
        list:hasContents  inst:IfcParameterValue_287 .

inst:IfcParameterValue_List_1848
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_1849
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_1848
        list:hasContents  inst:IfcParameterValue_1808 ;
        list:hasNext      inst:IfcParameterValue_List_1849 .

inst:IfcParameterValue_List_1849
        list:hasContents  inst:IfcParameterValue_287 .

inst:IfcParameterValue_List_1850
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_1851
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_1850
        list:hasContents  inst:IfcParameterValue_1811 ;
        list:hasNext      inst:IfcParameterValue_List_1851 .

inst:IfcParameterValue_List_1851
        list:hasContents  inst:IfcParameterValue_287 .

inst:IfcParameterValue_List_1852
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_1853
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_1852
        list:hasContents  inst:IfcParameterValue_1814 ;
        list:hasNext      inst:IfcParameterValue_List_1853 .

inst:IfcParameterValue_List_1853
        list:hasContents  inst:IfcParameterValue_287 .

inst:IfcParameterValue_List_1854
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_1855
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_1854
        list:hasContents  inst:IfcParameterValue_1817 ;
        list:hasNext      inst:IfcParameterValue_List_1855 .

inst:IfcParameterValue_List_1855
        list:hasContents  inst:IfcParameterValue_287 .

inst:IfcParameterValue_List_1856
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_1857
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_1856
        list:hasContents  inst:IfcParameterValue_1705 ;
        list:hasNext      inst:IfcParameterValue_List_1857 .

inst:IfcParameterValue_List_1857
        list:hasContents  inst:IfcParameterValue_287 .

inst:IfcParameterValue_List_1858
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_1859
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_1858
        list:hasContents  inst:IfcParameterValue_1822 ;
        list:hasNext      inst:IfcParameterValue_List_1859 .

inst:IfcParameterValue_List_1859
        list:hasContents  inst:IfcParameterValue_287 .

inst:IfcParameterValue_List_1860
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_1861
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_1860
        list:hasContents  inst:IfcParameterValue_1825 ;
        list:hasNext      inst:IfcParameterValue_List_1861 .

inst:IfcParameterValue_List_1861
        list:hasContents  inst:IfcParameterValue_287 .

inst:IfcParameterValue_List_1862
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_1863
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_1862
        list:hasContents  inst:IfcParameterValue_1828 ;
        list:hasNext      inst:IfcParameterValue_List_1863 .

inst:IfcParameterValue_List_1863
        list:hasContents  inst:IfcParameterValue_287 .

inst:IfcParameterValue_List_1864
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_1865
        rdf:type  ifc:IfcParameterValue_List .

inst:IfcParameterValue_List_1864
        list:hasContents  inst:IfcParameterValue_1831 ;
        list:hasNext      inst:IfcParameterValue_List_1865 .

inst:IfcParameterValue_List_1865
        list:hasContents  inst:IfcParameterValue_287 .

inst:IfcParameterValue_List_List_1866
        rdf:type  ifc:IfcParameterValue_List_List .

inst:IfcTextureVertexList_215
        ifc:texCoordsList_IfcTextureVertexList  inst:IfcParameterValue_List_List_1866 .

inst:IfcParameterValue_List_List_1866
        list:hasContents  inst:IfcParameterValue_List_1703 ;
        list:hasNext      inst:IfcParameterValue_List_List_1867 .

inst:IfcParameterValue_List_List_1867
        rdf:type          ifc:IfcParameterValue_List_List ;
        list:hasContents  inst:IfcParameterValue_List_1706 ;
        list:hasNext      inst:IfcParameterValue_List_List_1868 .

inst:IfcParameterValue_List_List_1868
        rdf:type          ifc:IfcParameterValue_List_List ;
        list:hasContents  inst:IfcParameterValue_List_1708 ;
        list:hasNext      inst:IfcParameterValue_List_List_1869 .

inst:IfcParameterValue_List_List_1869
        rdf:type          ifc:IfcParameterValue_List_List ;
        list:hasContents  inst:IfcParameterValue_List_1710 ;
        list:hasNext      inst:IfcParameterValue_List_List_1870 .

inst:IfcParameterValue_List_List_1870
        rdf:type          ifc:IfcParameterValue_List_List ;
        list:hasContents  inst:IfcParameterValue_List_1714 ;
        list:hasNext      inst:IfcParameterValue_List_List_1871 .

inst:IfcParameterValue_List_List_1871
        rdf:type          ifc:IfcParameterValue_List_List ;
        list:hasContents  inst:IfcParameterValue_List_1718 ;
        list:hasNext      inst:IfcParameterValue_List_List_1872 .

inst:IfcParameterValue_List_List_1872
        rdf:type          ifc:IfcParameterValue_List_List ;
        list:hasContents  inst:IfcParameterValue_List_1722 ;
        list:hasNext      inst:IfcParameterValue_List_List_1873 .

inst:IfcParameterValue_List_List_1873
        rdf:type          ifc:IfcParameterValue_List_List ;
        list:hasContents  inst:IfcParameterValue_List_1724 ;
        list:hasNext      inst:IfcParameterValue_List_List_1874 .

inst:IfcParameterValue_List_List_1874
        rdf:type          ifc:IfcParameterValue_List_List ;
        list:hasContents  inst:IfcParameterValue_List_1726 ;
        list:hasNext      inst:IfcParameterValue_List_List_1875 .

inst:IfcParameterValue_List_List_1875
        rdf:type          ifc:IfcParameterValue_List_List ;
        list:hasContents  inst:IfcParameterValue_List_1728 ;
        list:hasNext      inst:IfcParameterValue_List_List_1876 .

inst:IfcParameterValue_List_List_1876
        rdf:type          ifc:IfcParameterValue_List_List ;
        list:hasContents  inst:IfcParameterValue_List_1730 ;
        list:hasNext      inst:IfcParameterValue_List_List_1877 .

inst:IfcParameterValue_List_List_1877
        rdf:type          ifc:IfcParameterValue_List_List ;
        list:hasContents  inst:IfcParameterValue_List_1732 ;
        list:hasNext      inst:IfcParameterValue_List_List_1878 .

inst:IfcParameterValue_List_List_1878
        rdf:type          ifc:IfcParameterValue_List_List ;
        list:hasContents  inst:IfcParameterValue_List_1734 ;
        list:hasNext      inst:IfcParameterValue_List_List_1879 .

inst:IfcParameterValue_List_List_1879
        rdf:type          ifc:IfcParameterValue_List_List ;
        list:hasContents  inst:IfcParameterValue_List_1736 ;
        list:hasNext      inst:IfcParameterValue_List_List_1880 .

inst:IfcParameterValue_List_List_1880
        rdf:type          ifc:IfcParameterValue_List_List ;
        list:hasContents  inst:IfcParameterValue_List_1738 ;
        list:hasNext      inst:IfcParameterValue_List_List_1881 .

inst:IfcParameterValue_List_List_1881
        rdf:type          ifc:IfcParameterValue_List_List ;
        list:hasContents  inst:IfcParameterValue_List_1740 ;
        list:hasNext      inst:IfcParameterValue_List_List_1882 .

inst:IfcParameterValue_List_List_1882
        rdf:type          ifc:IfcParameterValue_List_List ;
        list:hasContents  inst:IfcParameterValue_List_1742 ;
        list:hasNext      inst:IfcParameterValue_List_List_1883 .

inst:IfcParameterValue_List_List_1883
        rdf:type          ifc:IfcParameterValue_List_List ;
        list:hasContents  inst:IfcParameterValue_List_1744 ;
        list:hasNext      inst:IfcParameterValue_List_List_1884 .

inst:IfcParameterValue_List_List_1884
        rdf:type          ifc:IfcParameterValue_List_List ;
        list:hasContents  inst:IfcParameterValue_List_1746 ;
        list:hasNext      inst:IfcParameterValue_List_List_1885 .

inst:IfcParameterValue_List_List_1885
        rdf:type          ifc:IfcParameterValue_List_List ;
        list:hasContents  inst:IfcParameterValue_List_1748 ;
        list:hasNext      inst:IfcParameterValue_List_List_1886 .

inst:IfcParameterValue_List_List_1886
        rdf:type          ifc:IfcParameterValue_List_List ;
        list:hasContents  inst:IfcParameterValue_List_1750 ;
        list:hasNext      inst:IfcParameterValue_List_List_1887 .

inst:IfcParameterValue_List_List_1887
        rdf:type          ifc:IfcParameterValue_List_List ;
        list:hasContents  inst:IfcParameterValue_List_1752 ;
        list:hasNext      inst:IfcParameterValue_List_List_1888 .

inst:IfcParameterValue_List_List_1888
        rdf:type          ifc:IfcParameterValue_List_List ;
        list:hasContents  inst:IfcParameterValue_List_1754 ;
        list:hasNext      inst:IfcParameterValue_List_List_1889 .

inst:IfcParameterValue_List_List_1889
        rdf:type          ifc:IfcParameterValue_List_List ;
        list:hasContents  inst:IfcParameterValue_List_1756 ;
        list:hasNext      inst:IfcParameterValue_List_List_1890 .

inst:IfcParameterValue_List_List_1890
        rdf:type          ifc:IfcParameterValue_List_List ;
        list:hasContents  inst:IfcParameterValue_List_1758 ;
        list:hasNext      inst:IfcParameterValue_List_List_1891 .

inst:IfcParameterValue_List_List_1891
        rdf:type          ifc:IfcParameterValue_List_List ;
        list:hasContents  inst:IfcParameterValue_List_1760 ;
        list:hasNext      inst:IfcParameterValue_List_List_1892 .

inst:IfcParameterValue_List_List_1892
        rdf:type          ifc:IfcParameterValue_List_List ;
        list:hasContents  inst:IfcParameterValue_List_1762 ;
        list:hasNext      inst:IfcParameterValue_List_List_1893 .

inst:IfcParameterValue_List_List_1893
        rdf:type          ifc:IfcParameterValue_List_List ;
        list:hasContents  inst:IfcParameterValue_List_1764 ;
        list:hasNext      inst:IfcParameterValue_List_List_1894 .

inst:IfcParameterValue_List_List_1894
        rdf:type          ifc:IfcParameterValue_List_List ;
        list:hasContents  inst:IfcParameterValue_List_1766 ;
        list:hasNext      inst:IfcParameterValue_List_List_1895 .

inst:IfcParameterValue_List_List_1895
        rdf:type          ifc:IfcParameterValue_List_List ;
        list:hasContents  inst:IfcParameterValue_List_1768 ;
        list:hasNext      inst:IfcParameterValue_List_List_1896 .

inst:IfcParameterValue_List_List_1896
        rdf:type          ifc:IfcParameterValue_List_List ;
        list:hasContents  inst:IfcParameterValue_List_1770 ;
        list:hasNext      inst:IfcParameterValue_List_List_1897 .

inst:IfcParameterValue_List_List_1897
        rdf:type          ifc:IfcParameterValue_List_List ;
        list:hasContents  inst:IfcParameterValue_List_1773 ;
        list:hasNext      inst:IfcParameterValue_List_List_1898 .

inst:IfcParameterValue_List_List_1898
        rdf:type          ifc:IfcParameterValue_List_List ;
        list:hasContents  inst:IfcParameterValue_List_1775 ;
        list:hasNext      inst:IfcParameterValue_List_List_1899 .

inst:IfcParameterValue_List_List_1899
        rdf:type          ifc:IfcParameterValue_List_List ;
        list:hasContents  inst:IfcParameterValue_List_1777 ;
        list:hasNext      inst:IfcParameterValue_List_List_1900 .

inst:IfcParameterValue_List_List_1900
        rdf:type          ifc:IfcParameterValue_List_List ;
        list:hasContents  inst:IfcParameterValue_List_1779 ;
        list:hasNext      inst:IfcParameterValue_List_List_1901 .

inst:IfcParameterValue_List_List_1901
        rdf:type          ifc:IfcParameterValue_List_List ;
        list:hasContents  inst:IfcParameterValue_List_1781 ;
        list:hasNext      inst:IfcParameterValue_List_List_1902 .

inst:IfcParameterValue_List_List_1902
        rdf:type          ifc:IfcParameterValue_List_List ;
        list:hasContents  inst:IfcParameterValue_List_1783 ;
        list:hasNext      inst:IfcParameterValue_List_List_1903 .

inst:IfcParameterValue_List_List_1903
        rdf:type          ifc:IfcParameterValue_List_List ;
        list:hasContents  inst:IfcParameterValue_List_1786 ;
        list:hasNext      inst:IfcParameterValue_List_List_1904 .

inst:IfcParameterValue_List_List_1904
        rdf:type          ifc:IfcParameterValue_List_List ;
        list:hasContents  inst:IfcParameterValue_List_1789 ;
        list:hasNext      inst:IfcParameterValue_List_List_1905 .

inst:IfcParameterValue_List_List_1905
        rdf:type          ifc:IfcParameterValue_List_List ;
        list:hasContents  inst:IfcParameterValue_List_1792 ;
        list:hasNext      inst:IfcParameterValue_List_List_1906 .

inst:IfcParameterValue_List_List_1906
        rdf:type          ifc:IfcParameterValue_List_List ;
        list:hasContents  inst:IfcParameterValue_List_1795 ;
        list:hasNext      inst:IfcParameterValue_List_List_1907 .

inst:IfcParameterValue_List_List_1907
        rdf:type          ifc:IfcParameterValue_List_List ;
        list:hasContents  inst:IfcParameterValue_List_1797 ;
        list:hasNext      inst:IfcParameterValue_List_List_1908 .

inst:IfcParameterValue_List_List_1908
        rdf:type          ifc:IfcParameterValue_List_List ;
        list:hasContents  inst:IfcParameterValue_List_1800 ;
        list:hasNext      inst:IfcParameterValue_List_List_1909 .

inst:IfcParameterValue_List_List_1909
        rdf:type          ifc:IfcParameterValue_List_List ;
        list:hasContents  inst:IfcParameterValue_List_1803 ;
        list:hasNext      inst:IfcParameterValue_List_List_1910 .

inst:IfcParameterValue_List_List_1910
        rdf:type          ifc:IfcParameterValue_List_List ;
        list:hasContents  inst:IfcParameterValue_List_1806 ;
        list:hasNext      inst:IfcParameterValue_List_List_1911 .

inst:IfcParameterValue_List_List_1911
        rdf:type          ifc:IfcParameterValue_List_List ;
        list:hasContents  inst:IfcParameterValue_List_1809 ;
        list:hasNext      inst:IfcParameterValue_List_List_1912 .

inst:IfcParameterValue_List_List_1912
        rdf:type          ifc:IfcParameterValue_List_List ;
        list:hasContents  inst:IfcParameterValue_List_1812 ;
        list:hasNext      inst:IfcParameterValue_List_List_1913 .

inst:IfcParameterValue_List_List_1913
        rdf:type          ifc:IfcParameterValue_List_List ;
        list:hasContents  inst:IfcParameterValue_List_1815 ;
        list:hasNext      inst:IfcParameterValue_List_List_1914 .

inst:IfcParameterValue_List_List_1914
        rdf:type          ifc:IfcParameterValue_List_List ;
        list:hasContents  inst:IfcParameterValue_List_1818 ;
        list:hasNext      inst:IfcParameterValue_List_List_1915 .

inst:IfcParameterValue_List_List_1915
        rdf:type          ifc:IfcParameterValue_List_List ;
        list:hasContents  inst:IfcParameterValue_List_1820 ;
        list:hasNext      inst:IfcParameterValue_List_List_1916 .

inst:IfcParameterValue_List_List_1916
        rdf:type          ifc:IfcParameterValue_List_List ;
        list:hasContents  inst:IfcParameterValue_List_1823 ;
        list:hasNext      inst:IfcParameterValue_List_List_1917 .

inst:IfcParameterValue_List_List_1917
        rdf:type          ifc:IfcParameterValue_List_List ;
        list:hasContents  inst:IfcParameterValue_List_1826 ;
        list:hasNext      inst:IfcParameterValue_List_List_1918 .

inst:IfcParameterValue_List_List_1918
        rdf:type          ifc:IfcParameterValue_List_List ;
        list:hasContents  inst:IfcParameterValue_List_1829 ;
        list:hasNext      inst:IfcParameterValue_List_List_1919 .

inst:IfcParameterValue_List_List_1919
        rdf:type          ifc:IfcParameterValue_List_List ;
        list:hasContents  inst:IfcParameterValue_List_1832 ;
        list:hasNext      inst:IfcParameterValue_List_List_1920 .

inst:IfcParameterValue_List_List_1920
        rdf:type          ifc:IfcParameterValue_List_List ;
        list:hasContents  inst:IfcParameterValue_List_1834 ;
        list:hasNext      inst:IfcParameterValue_List_List_1921 .

inst:IfcParameterValue_List_List_1921
        rdf:type          ifc:IfcParameterValue_List_List ;
        list:hasContents  inst:IfcParameterValue_List_1836 ;
        list:hasNext      inst:IfcParameterValue_List_List_1922 .

inst:IfcParameterValue_List_List_1922
        rdf:type          ifc:IfcParameterValue_List_List ;
        list:hasContents  inst:IfcParameterValue_List_1838 ;
        list:hasNext      inst:IfcParameterValue_List_List_1923 .

inst:IfcParameterValue_List_List_1923
        rdf:type          ifc:IfcParameterValue_List_List ;
        list:hasContents  inst:IfcParameterValue_List_1840 ;
        list:hasNext      inst:IfcParameterValue_List_List_1924 .

inst:IfcParameterValue_List_List_1924
        rdf:type          ifc:IfcParameterValue_List_List ;
        list:hasContents  inst:IfcParameterValue_List_1842 ;
        list:hasNext      inst:IfcParameterValue_List_List_1925 .

inst:IfcParameterValue_List_List_1925
        rdf:type          ifc:IfcParameterValue_List_List ;
        list:hasContents  inst:IfcParameterValue_List_1844 ;
        list:hasNext      inst:IfcParameterValue_List_List_1926 .

inst:IfcParameterValue_List_List_1926
        rdf:type          ifc:IfcParameterValue_List_List ;
        list:hasContents  inst:IfcParameterValue_List_1846 ;
        list:hasNext      inst:IfcParameterValue_List_List_1927 .

inst:IfcParameterValue_List_List_1927
        rdf:type          ifc:IfcParameterValue_List_List ;
        list:hasContents  inst:IfcParameterValue_List_1848 ;
        list:hasNext      inst:IfcParameterValue_List_List_1928 .

inst:IfcParameterValue_List_List_1928
        rdf:type          ifc:IfcParameterValue_List_List ;
        list:hasContents  inst:IfcParameterValue_List_1850 ;
        list:hasNext      inst:IfcParameterValue_List_List_1929 .

inst:IfcParameterValue_List_List_1929
        rdf:type          ifc:IfcParameterValue_List_List ;
        list:hasContents  inst:IfcParameterValue_List_1852 ;
        list:hasNext      inst:IfcParameterValue_List_List_1930 .

inst:IfcParameterValue_List_List_1930
        rdf:type          ifc:IfcParameterValue_List_List ;
        list:hasContents  inst:IfcParameterValue_List_1854 ;
        list:hasNext      inst:IfcParameterValue_List_List_1931 .

inst:IfcParameterValue_List_List_1931
        rdf:type          ifc:IfcParameterValue_List_List ;
        list:hasContents  inst:IfcParameterValue_List_1856 ;
        list:hasNext      inst:IfcParameterValue_List_List_1932 .

inst:IfcParameterValue_List_List_1932
        rdf:type          ifc:IfcParameterValue_List_List ;
        list:hasContents  inst:IfcParameterValue_List_1858 ;
        list:hasNext      inst:IfcParameterValue_List_List_1933 .

inst:IfcParameterValue_List_List_1933
        rdf:type          ifc:IfcParameterValue_List_List ;
        list:hasContents  inst:IfcParameterValue_List_1860 ;
        list:hasNext      inst:IfcParameterValue_List_List_1934 .

inst:IfcParameterValue_List_List_1934
        rdf:type          ifc:IfcParameterValue_List_List ;
        list:hasContents  inst:IfcParameterValue_List_1862 ;
        list:hasNext      inst:IfcParameterValue_List_List_1935 .

inst:IfcParameterValue_List_List_1935
        rdf:type          ifc:IfcParameterValue_List_List ;
        list:hasContents  inst:IfcParameterValue_List_1864 .

inst:IfcColourRgb_218
        rdf:type  ifc:IfcColourRgb .

inst:IfcSurfaceStyleRendering_216
        ifc:surfaceColour_IfcSurfaceStyleShading  inst:IfcColourRgb_218 ;
        ifc:reflectanceMethod_IfcSurfaceStyleRendering  ifc:NOTDEFINED .

inst:IfcSurfaceTexture_List_1936
        rdf:type  ifc:IfcSurfaceTexture_List .

inst:IfcSurfaceStyleWithTextures_217
        ifc:textures_IfcSurfaceStyleWithTextures  inst:IfcSurfaceTexture_List_1936 .

inst:IfcSurfaceTexture_List_1936
        list:hasContents  inst:IfcBlobTexture_219 .

inst:IfcColourRgb_218
        ifc:red_IfcColourRgb    inst:IfcParameterValue_287 ;
        ifc:green_IfcColourRgb  inst:IfcParameterValue_287 ;
        ifc:blue_IfcColourRgb   inst:IfcParameterValue_287 .

inst:IfcBlobTexture_219
        ifc:repeatS_IfcSurfaceTexture  inst:IfcBoolean_574 ;
        ifc:repeatT_IfcSurfaceTexture  inst:IfcBoolean_574 .

inst:IfcIdentifier_1937
        rdf:type           ifc:IfcIdentifier ;
        express:hasString  "TEXTURE" .

inst:IfcBlobTexture_219
        ifc:mode_IfcSurfaceTexture  inst:IfcIdentifier_1937 .

inst:IfcIdentifier_1938
        rdf:type           ifc:IfcIdentifier ;
        express:hasString  "PNG" .

inst:IfcBlobTexture_219
        ifc:rasterFormat_IfcBlobTexture  inst:IfcIdentifier_1938 .

inst:IfcBinary_1939  rdf:type  ifc:IfcBinary ;
        express:hasHexBinary  "\"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\""^^xsd:hexBinary .

inst:IfcBlobTexture_219
        ifc:rasterCode_IfcBlobTexture  inst:IfcBinary_1939 .

inst:IfcGloballyUniqueId_1940
        rdf:type           ifc:IfcGloballyUniqueId ;
        express:hasString  "3K_lgFFuT4$xmawQPom25e" .

inst:IfcMember_220  ifc:globalId_IfcRoot  inst:IfcGloballyUniqueId_1940 .

inst:IfcLocalPlacement_221
        rdf:type  ifc:IfcLocalPlacement .

inst:IfcMember_220  ifc:objectPlacement_IfcProduct  inst:IfcLocalPlacement_221 .

inst:IfcProductDefinitionShape_222
        rdf:type  ifc:IfcProductDefinitionShape .

inst:IfcMember_220  ifc:representation_IfcProduct  inst:IfcProductDefinitionShape_222 .

inst:IfcLocalPlacement_221
        ifc:placementRelTo_IfcLocalPlacement  inst:IfcLocalPlacement_193 .

inst:IfcAxis2Placement3D_223
        rdf:type  ifc:IfcAxis2Placement3D .

inst:IfcLocalPlacement_221
        ifc:relativePlacement_IfcLocalPlacement  inst:IfcAxis2Placement3D_223 .

inst:IfcRepresentation_List_1941
        rdf:type  ifc:IfcRepresentation_List .

inst:IfcProductDefinitionShape_222
        ifc:representations_IfcProductRepresentation  inst:IfcRepresentation_List_1941 .

inst:IfcShapeRepresentation_224
        rdf:type  ifc:IfcShapeRepresentation .

inst:IfcRepresentation_List_1941
        list:hasContents  inst:IfcShapeRepresentation_224 .

inst:IfcCartesianPoint_225
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcAxis2Placement3D_223
        ifc:location_IfcPlacement  inst:IfcCartesianPoint_225 .

inst:IfcDirection_226
        rdf:type  ifc:IfcDirection .

inst:IfcAxis2Placement3D_223
        ifc:axis_IfcAxis2Placement3D  inst:IfcDirection_226 .

inst:IfcDirection_227
        rdf:type  ifc:IfcDirection .

inst:IfcAxis2Placement3D_223
        ifc:refDirection_IfcAxis2Placement3D  inst:IfcDirection_227 .

inst:IfcShapeRepresentation_224
        ifc:contextOfItems_IfcRepresentation  inst:IfcGeometricRepresentationContext_2 ;
        ifc:representationIdentifier_IfcRepresentation  inst:IfcLabel_278 .

inst:IfcLabel_1942  rdf:type  ifc:IfcLabel ;
        express:hasString  "MappedRepresentation" .

inst:IfcShapeRepresentation_224
        ifc:representationType_IfcRepresentation  inst:IfcLabel_1942 .

inst:IfcMappedItem_228
        rdf:type  ifc:IfcMappedItem .

inst:IfcShapeRepresentation_224
        ifc:items_IfcRepresentation  inst:IfcMappedItem_228 .

inst:IfcLengthMeasure_List_1943
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_225
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_1943 .

inst:IfcLengthMeasure_List_1944
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_1945
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_1946
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "1.92970621585846"^^xsd:double .

inst:IfcLengthMeasure_List_1943
        list:hasContents  inst:IfcLengthMeasure_1946 ;
        list:hasNext      inst:IfcLengthMeasure_List_1944 .

inst:IfcLengthMeasure_1947
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "1.9296875"^^xsd:double .

inst:IfcLengthMeasure_List_1944
        list:hasContents  inst:IfcLengthMeasure_1947 ;
        list:hasNext      inst:IfcLengthMeasure_List_1945 .

inst:IfcLengthMeasure_List_1945
        list:hasContents  inst:IfcLengthMeasure_246 .

inst:IfcReal_List_1948
        rdf:type  ifc:IfcReal_List .

inst:IfcDirection_226
        ifc:directionRatios_IfcDirection  inst:IfcReal_List_1948 .

inst:IfcReal_List_1949
        rdf:type  ifc:IfcReal_List .

inst:IfcReal_List_1950
        rdf:type  ifc:IfcReal_List .

inst:IfcReal_List_1948
        list:hasContents  inst:IfcLengthMeasure_246 ;
        list:hasNext      inst:IfcReal_List_1949 .

inst:IfcReal_List_1949
        list:hasContents  inst:IfcLengthMeasure_246 ;
        list:hasNext      inst:IfcReal_List_1950 .

inst:IfcReal_List_1950
        list:hasContents  inst:IfcParameterValue_287 .

inst:IfcReal_List_1951
        rdf:type  ifc:IfcReal_List .

inst:IfcDirection_227
        ifc:directionRatios_IfcDirection  inst:IfcReal_List_1951 .

inst:IfcReal_List_1952
        rdf:type  ifc:IfcReal_List .

inst:IfcReal_List_1953
        rdf:type  ifc:IfcReal_List .

inst:IfcReal_List_1951
        list:hasContents  inst:IfcParameterValue_287 ;
        list:hasNext      inst:IfcReal_List_1952 .

inst:IfcReal_List_1952
        list:hasContents  inst:IfcLengthMeasure_246 ;
        list:hasNext      inst:IfcReal_List_1953 .

inst:IfcReal_List_1953
        list:hasContents  inst:IfcLengthMeasure_246 .

inst:IfcMappedItem_228
        ifc:mappingSource_IfcMappedItem  inst:IfcRepresentationMap_205 .

inst:IfcCartesianTransformationOperator3D_229
        rdf:type  ifc:IfcCartesianTransformationOperator3D .

inst:IfcMappedItem_228
        ifc:mappingTarget_IfcMappedItem  inst:IfcCartesianTransformationOperator3D_229 .

inst:IfcCartesianTransformationOperator3D_229
        ifc:localOrigin_IfcCartesianTransformationOperator  inst:IfcCartesianPoint_87 ;
        ifc:scale_IfcCartesianTransformationOperator  inst:IfcParameterValue_287 .

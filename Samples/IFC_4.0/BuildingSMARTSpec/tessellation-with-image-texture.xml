<IfcProject type="IfcProject" globalId="8bb92bd5-8057-4791-ab43-7bc24b58fef5">
  <name type="IfcLabel" value="Project"/>
  <declares>
    <IfcRelDeclares type="IfcRelDeclares" globalId="51c1dfed-8361-42d9-9b4b-a549c4e28980">
      <relatedDefinitions>
        <IfcDefinitionSelect type="IfcMemberType" globalId="4aab78a8-3d43-4ee1-bc53-23341a283cca" predefinedType="POST">
          <hasContext>
            <IfcRelDeclares>51c1dfed-8361-42d9-9b4b-a549c4e28980</IfcRelDeclares>
          </hasContext>
          <representationMaps>
            <IfcRepresentationMap type="IfcRepresentationMap" globalId="39661ec5-1ada-48fa-9877-d5bcd70d4e6d">
              <mappingOrigin type="IfcAxis2Placement3D">
                <location type="IfcCartesianPoint">
                  <coordinates>
                    <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                    <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                    <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                  </coordinates>
                </location>
              </mappingOrigin>
              <mappedRepresentation type="IfcShapeRepresentation" globalId="0acdb08d-b232-4b2c-8202-7495d459bb9b">
                <contextOfItems type="IfcGeometricRepresentationContext" globalId="a77a0761-be9c-44f6-8e42-dddf74415b84">
                  <worldCoordinateSystem type="IfcAxis2Placement3D">
                    <location type="IfcCartesianPoint">
                      <coordinates>
                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                      </coordinates>
                    </location>
                  </worldCoordinateSystem>
                  <contextIdentifier type="IfcLabel" value="3D"/>
                  <contextType type="IfcLabel" value="Model"/>
                  <coordinateSpaceDimension type="IfcDimensionCount" value="3"/>
                  <precision type="IfcReal" value="1.0E-5"/>
                </contextOfItems>
                <representationIdentifier type="IfcLabel" value="Body"/>
                <representationType type="IfcLabel" value="Tessellation"/>
                <items>
                  <IfcRepresentationItem type="IfcTriangulatedFaceSet">
                    <coordinates type="IfcCartesianPointList3D" globalId="701dc396-5021-410b-8498-88e8096f8705">
                      <coordList>
                        <IfcCartesianPoint type="IfcCartesianPoint">
                          <coordinates>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                          </coordinates>
                        </IfcCartesianPoint>
                        <IfcCartesianPoint type="IfcCartesianPoint">
                          <coordinates>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="4.0"/>
                          </coordinates>
                        </IfcCartesianPoint>
                        <IfcCartesianPoint type="IfcCartesianPoint">
                          <coordinates>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="2.0"/>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                          </coordinates>
                        </IfcCartesianPoint>
                        <IfcCartesianPoint type="IfcCartesianPoint">
                          <coordinates>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="1.84775906502257"/>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="0.76536686473018"/>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                          </coordinates>
                        </IfcCartesianPoint>
                        <IfcCartesianPoint type="IfcCartesianPoint">
                          <coordinates>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="1.4142135623731"/>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="1.41421356237309"/>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                          </coordinates>
                        </IfcCartesianPoint>
                        <IfcCartesianPoint type="IfcCartesianPoint">
                          <coordinates>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="0.76536686473018"/>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="1.84775906502257"/>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                          </coordinates>
                        </IfcCartesianPoint>
                        <IfcCartesianPoint type="IfcCartesianPoint">
                          <coordinates>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="1.22460635382238E-16"/>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="2.0"/>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                          </coordinates>
                        </IfcCartesianPoint>
                        <IfcCartesianPoint type="IfcCartesianPoint">
                          <coordinates>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="-0.765366864730179"/>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="1.84775906502257"/>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                          </coordinates>
                        </IfcCartesianPoint>
                        <IfcCartesianPoint type="IfcCartesianPoint">
                          <coordinates>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="-1.41421356237309"/>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="1.4142135623731"/>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                          </coordinates>
                        </IfcCartesianPoint>
                        <IfcCartesianPoint type="IfcCartesianPoint">
                          <coordinates>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="-1.84775906502257"/>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="0.76536686473018"/>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                          </coordinates>
                        </IfcCartesianPoint>
                        <IfcCartesianPoint type="IfcCartesianPoint">
                          <coordinates>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="-2.0"/>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="2.44921270764475E-16"/>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                          </coordinates>
                        </IfcCartesianPoint>
                        <IfcCartesianPoint type="IfcCartesianPoint">
                          <coordinates>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="-1.84775906502257"/>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="-0.765366864730179"/>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                          </coordinates>
                        </IfcCartesianPoint>
                        <IfcCartesianPoint type="IfcCartesianPoint">
                          <coordinates>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="-1.4142135623731"/>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="-1.41421356237309"/>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                          </coordinates>
                        </IfcCartesianPoint>
                        <IfcCartesianPoint type="IfcCartesianPoint">
                          <coordinates>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="-0.765366864730181"/>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="-1.84775906502257"/>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                          </coordinates>
                        </IfcCartesianPoint>
                        <IfcCartesianPoint type="IfcCartesianPoint">
                          <coordinates>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="-3.67381906146713E-16"/>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="-2.0"/>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                          </coordinates>
                        </IfcCartesianPoint>
                        <IfcCartesianPoint type="IfcCartesianPoint">
                          <coordinates>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="0.76536686473018"/>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="-1.84775906502257"/>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                          </coordinates>
                        </IfcCartesianPoint>
                        <IfcCartesianPoint type="IfcCartesianPoint">
                          <coordinates>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="1.41421356237309"/>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="-1.4142135623731"/>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                          </coordinates>
                        </IfcCartesianPoint>
                        <IfcCartesianPoint type="IfcCartesianPoint">
                          <coordinates>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="1.84775906502257"/>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="-0.765366864730181"/>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                          </coordinates>
                        </IfcCartesianPoint>
                        <IfcCartesianPoint type="IfcCartesianPoint">
                          <coordinates>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="2.0"/>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="-4.89842541528951E-16"/>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                          </coordinates>
                        </IfcCartesianPoint>
                        <IfcCartesianPoint type="IfcCartesianPoint">
                          <coordinates>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="2.0"/>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="4.0"/>
                          </coordinates>
                        </IfcCartesianPoint>
                        <IfcCartesianPoint type="IfcCartesianPoint">
                          <coordinates>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="1.84775906502257"/>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="0.76536686473018"/>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="4.0"/>
                          </coordinates>
                        </IfcCartesianPoint>
                        <IfcCartesianPoint type="IfcCartesianPoint">
                          <coordinates>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="1.4142135623731"/>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="1.41421356237309"/>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="4.0"/>
                          </coordinates>
                        </IfcCartesianPoint>
                        <IfcCartesianPoint type="IfcCartesianPoint">
                          <coordinates>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="0.76536686473018"/>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="1.84775906502257"/>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="4.0"/>
                          </coordinates>
                        </IfcCartesianPoint>
                        <IfcCartesianPoint type="IfcCartesianPoint">
                          <coordinates>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="1.22460635382238E-16"/>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="2.0"/>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="4.0"/>
                          </coordinates>
                        </IfcCartesianPoint>
                        <IfcCartesianPoint type="IfcCartesianPoint">
                          <coordinates>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="-0.765366864730179"/>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="1.84775906502257"/>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="4.0"/>
                          </coordinates>
                        </IfcCartesianPoint>
                        <IfcCartesianPoint type="IfcCartesianPoint">
                          <coordinates>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="-1.41421356237309"/>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="1.4142135623731"/>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="4.0"/>
                          </coordinates>
                        </IfcCartesianPoint>
                        <IfcCartesianPoint type="IfcCartesianPoint">
                          <coordinates>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="-1.84775906502257"/>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="0.76536686473018"/>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="4.0"/>
                          </coordinates>
                        </IfcCartesianPoint>
                        <IfcCartesianPoint type="IfcCartesianPoint">
                          <coordinates>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="-2.0"/>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="2.44921270764475E-16"/>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="4.0"/>
                          </coordinates>
                        </IfcCartesianPoint>
                        <IfcCartesianPoint type="IfcCartesianPoint">
                          <coordinates>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="-1.84775906502257"/>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="-0.765366864730179"/>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="4.0"/>
                          </coordinates>
                        </IfcCartesianPoint>
                        <IfcCartesianPoint type="IfcCartesianPoint">
                          <coordinates>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="-1.4142135623731"/>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="-1.41421356237309"/>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="4.0"/>
                          </coordinates>
                        </IfcCartesianPoint>
                        <IfcCartesianPoint type="IfcCartesianPoint">
                          <coordinates>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="-0.765366864730181"/>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="-1.84775906502257"/>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="4.0"/>
                          </coordinates>
                        </IfcCartesianPoint>
                        <IfcCartesianPoint type="IfcCartesianPoint">
                          <coordinates>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="-3.67381906146713E-16"/>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="-2.0"/>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="4.0"/>
                          </coordinates>
                        </IfcCartesianPoint>
                        <IfcCartesianPoint type="IfcCartesianPoint">
                          <coordinates>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="0.76536686473018"/>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="-1.84775906502257"/>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="4.0"/>
                          </coordinates>
                        </IfcCartesianPoint>
                        <IfcCartesianPoint type="IfcCartesianPoint">
                          <coordinates>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="1.41421356237309"/>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="-1.4142135623731"/>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="4.0"/>
                          </coordinates>
                        </IfcCartesianPoint>
                        <IfcCartesianPoint type="IfcCartesianPoint">
                          <coordinates>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="1.84775906502257"/>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="-0.765366864730181"/>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="4.0"/>
                          </coordinates>
                        </IfcCartesianPoint>
                        <IfcCartesianPoint type="IfcCartesianPoint">
                          <coordinates>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="2.0"/>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="-4.89842541528951E-16"/>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="4.0"/>
                          </coordinates>
                        </IfcCartesianPoint>
                        <IfcCartesianPoint type="IfcCartesianPoint">
                          <coordinates>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="2.0"/>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                          </coordinates>
                        </IfcCartesianPoint>
                        <IfcCartesianPoint type="IfcCartesianPoint">
                          <coordinates>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="1.84775906502257"/>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="0.76536686473018"/>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                          </coordinates>
                        </IfcCartesianPoint>
                        <IfcCartesianPoint type="IfcCartesianPoint">
                          <coordinates>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="1.4142135623731"/>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="1.41421356237309"/>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                          </coordinates>
                        </IfcCartesianPoint>
                        <IfcCartesianPoint type="IfcCartesianPoint">
                          <coordinates>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="0.76536686473018"/>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="1.84775906502257"/>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                          </coordinates>
                        </IfcCartesianPoint>
                        <IfcCartesianPoint type="IfcCartesianPoint">
                          <coordinates>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="1.22460635382238E-16"/>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="2.0"/>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                          </coordinates>
                        </IfcCartesianPoint>
                        <IfcCartesianPoint type="IfcCartesianPoint">
                          <coordinates>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="-0.765366864730179"/>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="1.84775906502257"/>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                          </coordinates>
                        </IfcCartesianPoint>
                        <IfcCartesianPoint type="IfcCartesianPoint">
                          <coordinates>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="-1.41421356237309"/>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="1.4142135623731"/>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                          </coordinates>
                        </IfcCartesianPoint>
                        <IfcCartesianPoint type="IfcCartesianPoint">
                          <coordinates>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="-1.84775906502257"/>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="0.76536686473018"/>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                          </coordinates>
                        </IfcCartesianPoint>
                        <IfcCartesianPoint type="IfcCartesianPoint">
                          <coordinates>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="-2.0"/>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="2.44921270764475E-16"/>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                          </coordinates>
                        </IfcCartesianPoint>
                        <IfcCartesianPoint type="IfcCartesianPoint">
                          <coordinates>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="-1.84775906502257"/>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="-0.765366864730179"/>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                          </coordinates>
                        </IfcCartesianPoint>
                        <IfcCartesianPoint type="IfcCartesianPoint">
                          <coordinates>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="-1.4142135623731"/>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="-1.41421356237309"/>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                          </coordinates>
                        </IfcCartesianPoint>
                        <IfcCartesianPoint type="IfcCartesianPoint">
                          <coordinates>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="-0.765366864730181"/>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="-1.84775906502257"/>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                          </coordinates>
                        </IfcCartesianPoint>
                        <IfcCartesianPoint type="IfcCartesianPoint">
                          <coordinates>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="-3.67381906146713E-16"/>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="-2.0"/>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                          </coordinates>
                        </IfcCartesianPoint>
                        <IfcCartesianPoint type="IfcCartesianPoint">
                          <coordinates>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="0.76536686473018"/>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="-1.84775906502257"/>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                          </coordinates>
                        </IfcCartesianPoint>
                        <IfcCartesianPoint type="IfcCartesianPoint">
                          <coordinates>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="1.41421356237309"/>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="-1.4142135623731"/>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                          </coordinates>
                        </IfcCartesianPoint>
                        <IfcCartesianPoint type="IfcCartesianPoint">
                          <coordinates>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="1.84775906502257"/>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="-0.765366864730181"/>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                          </coordinates>
                        </IfcCartesianPoint>
                        <IfcCartesianPoint type="IfcCartesianPoint">
                          <coordinates>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="2.0"/>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="-4.89842541528951E-16"/>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                          </coordinates>
                        </IfcCartesianPoint>
                        <IfcCartesianPoint type="IfcCartesianPoint">
                          <coordinates>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="2.0"/>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="4.0"/>
                          </coordinates>
                        </IfcCartesianPoint>
                        <IfcCartesianPoint type="IfcCartesianPoint">
                          <coordinates>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="1.84775906502257"/>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="0.76536686473018"/>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="4.0"/>
                          </coordinates>
                        </IfcCartesianPoint>
                        <IfcCartesianPoint type="IfcCartesianPoint">
                          <coordinates>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="1.4142135623731"/>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="1.41421356237309"/>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="4.0"/>
                          </coordinates>
                        </IfcCartesianPoint>
                        <IfcCartesianPoint type="IfcCartesianPoint">
                          <coordinates>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="0.76536686473018"/>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="1.84775906502257"/>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="4.0"/>
                          </coordinates>
                        </IfcCartesianPoint>
                        <IfcCartesianPoint type="IfcCartesianPoint">
                          <coordinates>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="1.22460635382238E-16"/>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="2.0"/>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="4.0"/>
                          </coordinates>
                        </IfcCartesianPoint>
                        <IfcCartesianPoint type="IfcCartesianPoint">
                          <coordinates>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="-0.765366864730179"/>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="1.84775906502257"/>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="4.0"/>
                          </coordinates>
                        </IfcCartesianPoint>
                        <IfcCartesianPoint type="IfcCartesianPoint">
                          <coordinates>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="-1.41421356237309"/>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="1.4142135623731"/>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="4.0"/>
                          </coordinates>
                        </IfcCartesianPoint>
                        <IfcCartesianPoint type="IfcCartesianPoint">
                          <coordinates>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="-1.84775906502257"/>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="0.76536686473018"/>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="4.0"/>
                          </coordinates>
                        </IfcCartesianPoint>
                        <IfcCartesianPoint type="IfcCartesianPoint">
                          <coordinates>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="-2.0"/>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="2.44921270764475E-16"/>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="4.0"/>
                          </coordinates>
                        </IfcCartesianPoint>
                        <IfcCartesianPoint type="IfcCartesianPoint">
                          <coordinates>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="-1.84775906502257"/>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="-0.765366864730179"/>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="4.0"/>
                          </coordinates>
                        </IfcCartesianPoint>
                        <IfcCartesianPoint type="IfcCartesianPoint">
                          <coordinates>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="-1.4142135623731"/>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="-1.41421356237309"/>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="4.0"/>
                          </coordinates>
                        </IfcCartesianPoint>
                        <IfcCartesianPoint type="IfcCartesianPoint">
                          <coordinates>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="-0.765366864730181"/>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="-1.84775906502257"/>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="4.0"/>
                          </coordinates>
                        </IfcCartesianPoint>
                        <IfcCartesianPoint type="IfcCartesianPoint">
                          <coordinates>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="-3.67381906146713E-16"/>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="-2.0"/>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="4.0"/>
                          </coordinates>
                        </IfcCartesianPoint>
                        <IfcCartesianPoint type="IfcCartesianPoint">
                          <coordinates>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="0.76536686473018"/>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="-1.84775906502257"/>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="4.0"/>
                          </coordinates>
                        </IfcCartesianPoint>
                        <IfcCartesianPoint type="IfcCartesianPoint">
                          <coordinates>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="1.41421356237309"/>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="-1.4142135623731"/>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="4.0"/>
                          </coordinates>
                        </IfcCartesianPoint>
                        <IfcCartesianPoint type="IfcCartesianPoint">
                          <coordinates>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="1.84775906502257"/>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="-0.765366864730181"/>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="4.0"/>
                          </coordinates>
                        </IfcCartesianPoint>
                        <IfcCartesianPoint type="IfcCartesianPoint">
                          <coordinates>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="2.0"/>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="-4.89842541528951E-16"/>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="4.0"/>
                          </coordinates>
                        </IfcCartesianPoint>
                      </coordList>
                    </coordinates>
                    <normals>
                      <parameterValues>
                        <IfcParameterValue type="IfcParameterValue" value="0.0"/>
                        <IfcParameterValue type="IfcParameterValue" value="0.0"/>
                        <IfcParameterValue type="IfcParameterValue" value="-1.0"/>
                      </parameterValues>
                      <parameterValues>
                        <IfcParameterValue type="IfcParameterValue" value="0.0"/>
                        <IfcParameterValue type="IfcParameterValue" value="0.0"/>
                        <IfcParameterValue type="IfcParameterValue" value="1.0"/>
                      </parameterValues>
                      <parameterValues>
                        <IfcParameterValue type="IfcParameterValue" value="0.0"/>
                        <IfcParameterValue type="IfcParameterValue" value="0.0"/>
                        <IfcParameterValue type="IfcParameterValue" value="-1.0"/>
                      </parameterValues>
                      <parameterValues>
                        <IfcParameterValue type="IfcParameterValue" value="0.0"/>
                        <IfcParameterValue type="IfcParameterValue" value="0.0"/>
                        <IfcParameterValue type="IfcParameterValue" value="-1.0"/>
                      </parameterValues>
                      <parameterValues>
                        <IfcParameterValue type="IfcParameterValue" value="0.0"/>
                        <IfcParameterValue type="IfcParameterValue" value="0.0"/>
                        <IfcParameterValue type="IfcParameterValue" value="-1.0"/>
                      </parameterValues>
                      <parameterValues>
                        <IfcParameterValue type="IfcParameterValue" value="0.0"/>
                        <IfcParameterValue type="IfcParameterValue" value="0.0"/>
                        <IfcParameterValue type="IfcParameterValue" value="-1.0"/>
                      </parameterValues>
                      <parameterValues>
                        <IfcParameterValue type="IfcParameterValue" value="0.0"/>
                        <IfcParameterValue type="IfcParameterValue" value="0.0"/>
                        <IfcParameterValue type="IfcParameterValue" value="-1.0"/>
                      </parameterValues>
                      <parameterValues>
                        <IfcParameterValue type="IfcParameterValue" value="0.0"/>
                        <IfcParameterValue type="IfcParameterValue" value="0.0"/>
                        <IfcParameterValue type="IfcParameterValue" value="-1.0"/>
                      </parameterValues>
                      <parameterValues>
                        <IfcParameterValue type="IfcParameterValue" value="0.0"/>
                        <IfcParameterValue type="IfcParameterValue" value="0.0"/>
                        <IfcParameterValue type="IfcParameterValue" value="-1.0"/>
                      </parameterValues>
                      <parameterValues>
                        <IfcParameterValue type="IfcParameterValue" value="0.0"/>
                        <IfcParameterValue type="IfcParameterValue" value="0.0"/>
                        <IfcParameterValue type="IfcParameterValue" value="-1.0"/>
                      </parameterValues>
                      <parameterValues>
                        <IfcParameterValue type="IfcParameterValue" value="0.0"/>
                        <IfcParameterValue type="IfcParameterValue" value="0.0"/>
                        <IfcParameterValue type="IfcParameterValue" value="-1.0"/>
                      </parameterValues>
                      <parameterValues>
                        <IfcParameterValue type="IfcParameterValue" value="0.0"/>
                        <IfcParameterValue type="IfcParameterValue" value="0.0"/>
                        <IfcParameterValue type="IfcParameterValue" value="-1.0"/>
                      </parameterValues>
                      <parameterValues>
                        <IfcParameterValue type="IfcParameterValue" value="0.0"/>
                        <IfcParameterValue type="IfcParameterValue" value="0.0"/>
                        <IfcParameterValue type="IfcParameterValue" value="-1.0"/>
                      </parameterValues>
                      <parameterValues>
                        <IfcParameterValue type="IfcParameterValue" value="0.0"/>
                        <IfcParameterValue type="IfcParameterValue" value="0.0"/>
                        <IfcParameterValue type="IfcParameterValue" value="-1.0"/>
                      </parameterValues>
                      <parameterValues>
                        <IfcParameterValue type="IfcParameterValue" value="0.0"/>
                        <IfcParameterValue type="IfcParameterValue" value="0.0"/>
                        <IfcParameterValue type="IfcParameterValue" value="-1.0"/>
                      </parameterValues>
                      <parameterValues>
                        <IfcParameterValue type="IfcParameterValue" value="0.0"/>
                        <IfcParameterValue type="IfcParameterValue" value="0.0"/>
                        <IfcParameterValue type="IfcParameterValue" value="-1.0"/>
                      </parameterValues>
                      <parameterValues>
                        <IfcParameterValue type="IfcParameterValue" value="0.0"/>
                        <IfcParameterValue type="IfcParameterValue" value="0.0"/>
                        <IfcParameterValue type="IfcParameterValue" value="-1.0"/>
                      </parameterValues>
                      <parameterValues>
                        <IfcParameterValue type="IfcParameterValue" value="0.0"/>
                        <IfcParameterValue type="IfcParameterValue" value="0.0"/>
                        <IfcParameterValue type="IfcParameterValue" value="-1.0"/>
                      </parameterValues>
                      <parameterValues>
                        <IfcParameterValue type="IfcParameterValue" value="0.0"/>
                        <IfcParameterValue type="IfcParameterValue" value="0.0"/>
                        <IfcParameterValue type="IfcParameterValue" value="-1.0"/>
                      </parameterValues>
                      <parameterValues>
                        <IfcParameterValue type="IfcParameterValue" value="0.0"/>
                        <IfcParameterValue type="IfcParameterValue" value="0.0"/>
                        <IfcParameterValue type="IfcParameterValue" value="1.0"/>
                      </parameterValues>
                      <parameterValues>
                        <IfcParameterValue type="IfcParameterValue" value="0.0"/>
                        <IfcParameterValue type="IfcParameterValue" value="0.0"/>
                        <IfcParameterValue type="IfcParameterValue" value="1.0"/>
                      </parameterValues>
                      <parameterValues>
                        <IfcParameterValue type="IfcParameterValue" value="0.0"/>
                        <IfcParameterValue type="IfcParameterValue" value="0.0"/>
                        <IfcParameterValue type="IfcParameterValue" value="1.0"/>
                      </parameterValues>
                      <parameterValues>
                        <IfcParameterValue type="IfcParameterValue" value="0.0"/>
                        <IfcParameterValue type="IfcParameterValue" value="0.0"/>
                        <IfcParameterValue type="IfcParameterValue" value="1.0"/>
                      </parameterValues>
                      <parameterValues>
                        <IfcParameterValue type="IfcParameterValue" value="0.0"/>
                        <IfcParameterValue type="IfcParameterValue" value="0.0"/>
                        <IfcParameterValue type="IfcParameterValue" value="1.0"/>
                      </parameterValues>
                      <parameterValues>
                        <IfcParameterValue type="IfcParameterValue" value="0.0"/>
                        <IfcParameterValue type="IfcParameterValue" value="0.0"/>
                        <IfcParameterValue type="IfcParameterValue" value="1.0"/>
                      </parameterValues>
                      <parameterValues>
                        <IfcParameterValue type="IfcParameterValue" value="0.0"/>
                        <IfcParameterValue type="IfcParameterValue" value="0.0"/>
                        <IfcParameterValue type="IfcParameterValue" value="1.0"/>
                      </parameterValues>
                      <parameterValues>
                        <IfcParameterValue type="IfcParameterValue" value="0.0"/>
                        <IfcParameterValue type="IfcParameterValue" value="0.0"/>
                        <IfcParameterValue type="IfcParameterValue" value="1.0"/>
                      </parameterValues>
                      <parameterValues>
                        <IfcParameterValue type="IfcParameterValue" value="0.0"/>
                        <IfcParameterValue type="IfcParameterValue" value="0.0"/>
                        <IfcParameterValue type="IfcParameterValue" value="1.0"/>
                      </parameterValues>
                      <parameterValues>
                        <IfcParameterValue type="IfcParameterValue" value="0.0"/>
                        <IfcParameterValue type="IfcParameterValue" value="0.0"/>
                        <IfcParameterValue type="IfcParameterValue" value="1.0"/>
                      </parameterValues>
                      <parameterValues>
                        <IfcParameterValue type="IfcParameterValue" value="0.0"/>
                        <IfcParameterValue type="IfcParameterValue" value="0.0"/>
                        <IfcParameterValue type="IfcParameterValue" value="1.0"/>
                      </parameterValues>
                      <parameterValues>
                        <IfcParameterValue type="IfcParameterValue" value="0.0"/>
                        <IfcParameterValue type="IfcParameterValue" value="0.0"/>
                        <IfcParameterValue type="IfcParameterValue" value="1.0"/>
                      </parameterValues>
                      <parameterValues>
                        <IfcParameterValue type="IfcParameterValue" value="0.0"/>
                        <IfcParameterValue type="IfcParameterValue" value="0.0"/>
                        <IfcParameterValue type="IfcParameterValue" value="1.0"/>
                      </parameterValues>
                      <parameterValues>
                        <IfcParameterValue type="IfcParameterValue" value="0.0"/>
                        <IfcParameterValue type="IfcParameterValue" value="0.0"/>
                        <IfcParameterValue type="IfcParameterValue" value="1.0"/>
                      </parameterValues>
                      <parameterValues>
                        <IfcParameterValue type="IfcParameterValue" value="0.0"/>
                        <IfcParameterValue type="IfcParameterValue" value="0.0"/>
                        <IfcParameterValue type="IfcParameterValue" value="1.0"/>
                      </parameterValues>
                      <parameterValues>
                        <IfcParameterValue type="IfcParameterValue" value="0.0"/>
                        <IfcParameterValue type="IfcParameterValue" value="0.0"/>
                        <IfcParameterValue type="IfcParameterValue" value="1.0"/>
                      </parameterValues>
                      <parameterValues>
                        <IfcParameterValue type="IfcParameterValue" value="0.0"/>
                        <IfcParameterValue type="IfcParameterValue" value="0.0"/>
                        <IfcParameterValue type="IfcParameterValue" value="1.0"/>
                      </parameterValues>
                      <parameterValues>
                        <IfcParameterValue type="IfcParameterValue" value="1.0"/>
                        <IfcParameterValue type="IfcParameterValue" value="0.0"/>
                        <IfcParameterValue type="IfcParameterValue" value="0.0"/>
                      </parameterValues>
                      <parameterValues>
                        <IfcParameterValue type="IfcParameterValue" value="0.923879532511287"/>
                        <IfcParameterValue type="IfcParameterValue" value="0.38268343236509"/>
                        <IfcParameterValue type="IfcParameterValue" value="0.0"/>
                      </parameterValues>
                      <parameterValues>
                        <IfcParameterValue type="IfcParameterValue" value="0.707106781186548"/>
                        <IfcParameterValue type="IfcParameterValue" value="0.707106781186547"/>
                        <IfcParameterValue type="IfcParameterValue" value="0.0"/>
                      </parameterValues>
                      <parameterValues>
                        <IfcParameterValue type="IfcParameterValue" value="0.38268343236509"/>
                        <IfcParameterValue type="IfcParameterValue" value="0.923879532511287"/>
                        <IfcParameterValue type="IfcParameterValue" value="0.0"/>
                      </parameterValues>
                      <parameterValues>
                        <IfcParameterValue type="IfcParameterValue" value="6.12303176911189E-17"/>
                        <IfcParameterValue type="IfcParameterValue" value="1.0"/>
                        <IfcParameterValue type="IfcParameterValue" value="0.0"/>
                      </parameterValues>
                      <parameterValues>
                        <IfcParameterValue type="IfcParameterValue" value="-0.38268343236509"/>
                        <IfcParameterValue type="IfcParameterValue" value="0.923879532511287"/>
                        <IfcParameterValue type="IfcParameterValue" value="0.0"/>
                      </parameterValues>
                      <parameterValues>
                        <IfcParameterValue type="IfcParameterValue" value="-0.707106781186547"/>
                        <IfcParameterValue type="IfcParameterValue" value="0.707106781186548"/>
                        <IfcParameterValue type="IfcParameterValue" value="0.0"/>
                      </parameterValues>
                      <parameterValues>
                        <IfcParameterValue type="IfcParameterValue" value="-0.923879532511287"/>
                        <IfcParameterValue type="IfcParameterValue" value="0.38268343236509"/>
                        <IfcParameterValue type="IfcParameterValue" value="0.0"/>
                      </parameterValues>
                      <parameterValues>
                        <IfcParameterValue type="IfcParameterValue" value="-1.0"/>
                        <IfcParameterValue type="IfcParameterValue" value="1.22460635382238E-16"/>
                        <IfcParameterValue type="IfcParameterValue" value="0.0"/>
                      </parameterValues>
                      <parameterValues>
                        <IfcParameterValue type="IfcParameterValue" value="-0.923879532511287"/>
                        <IfcParameterValue type="IfcParameterValue" value="-0.38268343236509"/>
                        <IfcParameterValue type="IfcParameterValue" value="0.0"/>
                      </parameterValues>
                      <parameterValues>
                        <IfcParameterValue type="IfcParameterValue" value="-0.707106781186548"/>
                        <IfcParameterValue type="IfcParameterValue" value="-0.707106781186547"/>
                        <IfcParameterValue type="IfcParameterValue" value="0.0"/>
                      </parameterValues>
                      <parameterValues>
                        <IfcParameterValue type="IfcParameterValue" value="-0.38268343236509"/>
                        <IfcParameterValue type="IfcParameterValue" value="-0.923879532511287"/>
                        <IfcParameterValue type="IfcParameterValue" value="0.0"/>
                      </parameterValues>
                      <parameterValues>
                        <IfcParameterValue type="IfcParameterValue" value="-1.83690953073357E-16"/>
                        <IfcParameterValue type="IfcParameterValue" value="-1.0"/>
                        <IfcParameterValue type="IfcParameterValue" value="0.0"/>
                      </parameterValues>
                      <parameterValues>
                        <IfcParameterValue type="IfcParameterValue" value="0.38268343236509"/>
                        <IfcParameterValue type="IfcParameterValue" value="-0.923879532511287"/>
                        <IfcParameterValue type="IfcParameterValue" value="0.0"/>
                      </parameterValues>
                      <parameterValues>
                        <IfcParameterValue type="IfcParameterValue" value="0.707106781186547"/>
                        <IfcParameterValue type="IfcParameterValue" value="-0.707106781186548"/>
                        <IfcParameterValue type="IfcParameterValue" value="0.0"/>
                      </parameterValues>
                      <parameterValues>
                        <IfcParameterValue type="IfcParameterValue" value="0.923879532511287"/>
                        <IfcParameterValue type="IfcParameterValue" value="-0.38268343236509"/>
                        <IfcParameterValue type="IfcParameterValue" value="0.0"/>
                      </parameterValues>
                      <parameterValues>
                        <IfcParameterValue type="IfcParameterValue" value="1.0"/>
                        <IfcParameterValue type="IfcParameterValue" value="-2.44921270764475E-16"/>
                        <IfcParameterValue type="IfcParameterValue" value="0.0"/>
                      </parameterValues>
                      <parameterValues>
                        <IfcParameterValue type="IfcParameterValue" value="1.0"/>
                        <IfcParameterValue type="IfcParameterValue" value="0.0"/>
                        <IfcParameterValue type="IfcParameterValue" value="0.0"/>
                      </parameterValues>
                      <parameterValues>
                        <IfcParameterValue type="IfcParameterValue" value="0.923879532511287"/>
                        <IfcParameterValue type="IfcParameterValue" value="0.38268343236509"/>
                        <IfcParameterValue type="IfcParameterValue" value="0.0"/>
                      </parameterValues>
                      <parameterValues>
                        <IfcParameterValue type="IfcParameterValue" value="0.707106781186548"/>
                        <IfcParameterValue type="IfcParameterValue" value="0.707106781186547"/>
                        <IfcParameterValue type="IfcParameterValue" value="0.0"/>
                      </parameterValues>
                      <parameterValues>
                        <IfcParameterValue type="IfcParameterValue" value="0.38268343236509"/>
                        <IfcParameterValue type="IfcParameterValue" value="0.923879532511287"/>
                        <IfcParameterValue type="IfcParameterValue" value="0.0"/>
                      </parameterValues>
                      <parameterValues>
                        <IfcParameterValue type="IfcParameterValue" value="6.12303176911189E-17"/>
                        <IfcParameterValue type="IfcParameterValue" value="1.0"/>
                        <IfcParameterValue type="IfcParameterValue" value="0.0"/>
                      </parameterValues>
                      <parameterValues>
                        <IfcParameterValue type="IfcParameterValue" value="-0.38268343236509"/>
                        <IfcParameterValue type="IfcParameterValue" value="0.923879532511287"/>
                        <IfcParameterValue type="IfcParameterValue" value="0.0"/>
                      </parameterValues>
                      <parameterValues>
                        <IfcParameterValue type="IfcParameterValue" value="-0.707106781186547"/>
                        <IfcParameterValue type="IfcParameterValue" value="0.707106781186548"/>
                        <IfcParameterValue type="IfcParameterValue" value="0.0"/>
                      </parameterValues>
                      <parameterValues>
                        <IfcParameterValue type="IfcParameterValue" value="-0.923879532511287"/>
                        <IfcParameterValue type="IfcParameterValue" value="0.38268343236509"/>
                        <IfcParameterValue type="IfcParameterValue" value="0.0"/>
                      </parameterValues>
                      <parameterValues>
                        <IfcParameterValue type="IfcParameterValue" value="-1.0"/>
                        <IfcParameterValue type="IfcParameterValue" value="1.22460635382238E-16"/>
                        <IfcParameterValue type="IfcParameterValue" value="0.0"/>
                      </parameterValues>
                      <parameterValues>
                        <IfcParameterValue type="IfcParameterValue" value="-0.923879532511287"/>
                        <IfcParameterValue type="IfcParameterValue" value="-0.38268343236509"/>
                        <IfcParameterValue type="IfcParameterValue" value="0.0"/>
                      </parameterValues>
                      <parameterValues>
                        <IfcParameterValue type="IfcParameterValue" value="-0.707106781186548"/>
                        <IfcParameterValue type="IfcParameterValue" value="-0.707106781186547"/>
                        <IfcParameterValue type="IfcParameterValue" value="0.0"/>
                      </parameterValues>
                      <parameterValues>
                        <IfcParameterValue type="IfcParameterValue" value="-0.38268343236509"/>
                        <IfcParameterValue type="IfcParameterValue" value="-0.923879532511287"/>
                        <IfcParameterValue type="IfcParameterValue" value="0.0"/>
                      </parameterValues>
                      <parameterValues>
                        <IfcParameterValue type="IfcParameterValue" value="-1.83690953073357E-16"/>
                        <IfcParameterValue type="IfcParameterValue" value="-1.0"/>
                        <IfcParameterValue type="IfcParameterValue" value="0.0"/>
                      </parameterValues>
                      <parameterValues>
                        <IfcParameterValue type="IfcParameterValue" value="0.38268343236509"/>
                        <IfcParameterValue type="IfcParameterValue" value="-0.923879532511287"/>
                        <IfcParameterValue type="IfcParameterValue" value="0.0"/>
                      </parameterValues>
                      <parameterValues>
                        <IfcParameterValue type="IfcParameterValue" value="0.707106781186547"/>
                        <IfcParameterValue type="IfcParameterValue" value="-0.707106781186548"/>
                        <IfcParameterValue type="IfcParameterValue" value="0.0"/>
                      </parameterValues>
                      <parameterValues>
                        <IfcParameterValue type="IfcParameterValue" value="0.923879532511287"/>
                        <IfcParameterValue type="IfcParameterValue" value="-0.38268343236509"/>
                        <IfcParameterValue type="IfcParameterValue" value="0.0"/>
                      </parameterValues>
                      <parameterValues>
                        <IfcParameterValue type="IfcParameterValue" value="1.0"/>
                        <IfcParameterValue type="IfcParameterValue" value="-2.44921270764475E-16"/>
                        <IfcParameterValue type="IfcParameterValue" value="0.0"/>
                      </parameterValues>
                    </normals>
                    <closed type="IfcBoolean" value="false"/>
                    <coordIndex>
                      <positiveIntegers>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="1"/>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="4"/>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="3"/>
                      </positiveIntegers>
                      <positiveIntegers>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="38"/>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="54"/>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="37"/>
                      </positiveIntegers>
                      <positiveIntegers>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="38"/>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="55"/>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="54"/>
                      </positiveIntegers>
                      <positiveIntegers>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="2"/>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="20"/>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="21"/>
                      </positiveIntegers>
                      <positiveIntegers>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="1"/>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="5"/>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="4"/>
                      </positiveIntegers>
                      <positiveIntegers>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="39"/>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="55"/>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="38"/>
                      </positiveIntegers>
                      <positiveIntegers>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="39"/>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="56"/>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="55"/>
                      </positiveIntegers>
                      <positiveIntegers>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="2"/>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="21"/>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="22"/>
                      </positiveIntegers>
                      <positiveIntegers>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="1"/>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="6"/>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="5"/>
                      </positiveIntegers>
                      <positiveIntegers>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="40"/>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="56"/>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="39"/>
                      </positiveIntegers>
                      <positiveIntegers>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="40"/>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="57"/>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="56"/>
                      </positiveIntegers>
                      <positiveIntegers>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="2"/>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="22"/>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="23"/>
                      </positiveIntegers>
                      <positiveIntegers>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="1"/>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="7"/>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="6"/>
                      </positiveIntegers>
                      <positiveIntegers>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="41"/>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="57"/>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="40"/>
                      </positiveIntegers>
                      <positiveIntegers>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="41"/>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="58"/>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="57"/>
                      </positiveIntegers>
                      <positiveIntegers>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="2"/>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="23"/>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="24"/>
                      </positiveIntegers>
                      <positiveIntegers>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="1"/>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="8"/>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="7"/>
                      </positiveIntegers>
                      <positiveIntegers>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="42"/>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="58"/>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="41"/>
                      </positiveIntegers>
                      <positiveIntegers>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="42"/>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="59"/>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="58"/>
                      </positiveIntegers>
                      <positiveIntegers>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="2"/>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="24"/>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="25"/>
                      </positiveIntegers>
                      <positiveIntegers>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="1"/>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="9"/>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="8"/>
                      </positiveIntegers>
                      <positiveIntegers>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="43"/>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="59"/>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="42"/>
                      </positiveIntegers>
                      <positiveIntegers>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="43"/>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="60"/>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="59"/>
                      </positiveIntegers>
                      <positiveIntegers>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="2"/>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="25"/>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="26"/>
                      </positiveIntegers>
                      <positiveIntegers>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="1"/>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="10"/>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="9"/>
                      </positiveIntegers>
                      <positiveIntegers>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="44"/>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="60"/>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="43"/>
                      </positiveIntegers>
                      <positiveIntegers>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="44"/>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="61"/>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="60"/>
                      </positiveIntegers>
                      <positiveIntegers>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="2"/>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="26"/>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="27"/>
                      </positiveIntegers>
                      <positiveIntegers>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="1"/>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="11"/>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="10"/>
                      </positiveIntegers>
                      <positiveIntegers>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="45"/>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="61"/>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="44"/>
                      </positiveIntegers>
                      <positiveIntegers>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="45"/>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="62"/>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="61"/>
                      </positiveIntegers>
                      <positiveIntegers>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="2"/>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="27"/>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="28"/>
                      </positiveIntegers>
                      <positiveIntegers>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="1"/>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="12"/>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="11"/>
                      </positiveIntegers>
                      <positiveIntegers>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="46"/>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="62"/>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="45"/>
                      </positiveIntegers>
                      <positiveIntegers>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="46"/>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="63"/>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="62"/>
                      </positiveIntegers>
                      <positiveIntegers>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="2"/>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="28"/>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="29"/>
                      </positiveIntegers>
                      <positiveIntegers>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="1"/>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="13"/>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="12"/>
                      </positiveIntegers>
                      <positiveIntegers>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="47"/>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="63"/>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="46"/>
                      </positiveIntegers>
                      <positiveIntegers>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="47"/>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="64"/>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="63"/>
                      </positiveIntegers>
                      <positiveIntegers>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="2"/>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="29"/>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="30"/>
                      </positiveIntegers>
                      <positiveIntegers>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="1"/>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="14"/>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="13"/>
                      </positiveIntegers>
                      <positiveIntegers>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="48"/>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="64"/>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="47"/>
                      </positiveIntegers>
                      <positiveIntegers>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="48"/>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="65"/>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="64"/>
                      </positiveIntegers>
                      <positiveIntegers>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="2"/>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="30"/>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="31"/>
                      </positiveIntegers>
                      <positiveIntegers>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="1"/>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="15"/>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="14"/>
                      </positiveIntegers>
                      <positiveIntegers>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="49"/>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="65"/>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="48"/>
                      </positiveIntegers>
                      <positiveIntegers>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="49"/>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="66"/>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="65"/>
                      </positiveIntegers>
                      <positiveIntegers>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="2"/>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="31"/>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="32"/>
                      </positiveIntegers>
                      <positiveIntegers>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="1"/>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="16"/>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="15"/>
                      </positiveIntegers>
                      <positiveIntegers>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="50"/>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="66"/>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="49"/>
                      </positiveIntegers>
                      <positiveIntegers>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="50"/>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="67"/>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="66"/>
                      </positiveIntegers>
                      <positiveIntegers>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="2"/>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="32"/>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="33"/>
                      </positiveIntegers>
                      <positiveIntegers>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="1"/>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="17"/>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="16"/>
                      </positiveIntegers>
                      <positiveIntegers>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="51"/>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="67"/>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="50"/>
                      </positiveIntegers>
                      <positiveIntegers>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="51"/>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="68"/>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="67"/>
                      </positiveIntegers>
                      <positiveIntegers>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="2"/>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="33"/>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="34"/>
                      </positiveIntegers>
                      <positiveIntegers>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="1"/>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="18"/>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="17"/>
                      </positiveIntegers>
                      <positiveIntegers>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="52"/>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="68"/>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="51"/>
                      </positiveIntegers>
                      <positiveIntegers>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="52"/>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="69"/>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="68"/>
                      </positiveIntegers>
                      <positiveIntegers>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="2"/>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="34"/>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="35"/>
                      </positiveIntegers>
                      <positiveIntegers>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="1"/>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="19"/>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="18"/>
                      </positiveIntegers>
                      <positiveIntegers>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="53"/>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="69"/>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="52"/>
                      </positiveIntegers>
                      <positiveIntegers>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="53"/>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="70"/>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="69"/>
                      </positiveIntegers>
                      <positiveIntegers>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="2"/>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="35"/>
                        <IfcPositiveInteger type="IfcPositiveInteger" value="36"/>
                      </positiveIntegers>
                    </coordIndex>
                  </IfcRepresentationItem>
                </items>
              </mappedRepresentation>
            </IfcRepresentationMap>
          </representationMaps>
        </IfcDefinitionSelect>
      </relatedDefinitions>
    </IfcRelDeclares>
  </declares>
  <isDecomposedBy type="IfcRelAggregates" globalId="acdf9445-05d5-4fff-9726-f790c02285cd">
    <relatedObjects>
      <IfcObjectDefinition type="IfcSite" globalId="424eb936-0cdc-4fba-9c00-18ba08428520" compositionType="ELEMENT">
        <name type="IfcLabel" value="Site #1"/>
        <objectPlacement type="IfcLocalPlacement" globalId="d0b85c25-3e1f-4365-97c3-a8bb3a8dd376">
          <relativePlacement type="IfcAxis2Placement3D">
            <location type="IfcCartesianPoint">
              <coordinates>
                <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
              </coordinates>
            </location>
          </relativePlacement>
        </objectPlacement>
        <representation type="IfcProductDefinitionShape" globalId="759ca4eb-178f-42fa-98b5-98520ee64e0c">
          <representations>
            <IfcRepresentation type="IfcShapeRepresentation" globalId="da527b0c-93d9-43a7-8d51-df0b65fe7338">
              <contextOfItems>a77a0761-be9c-44f6-8e42-dddf74415b84</contextOfItems>
              <representationIdentifier type="IfcLabel" value="FootPrint"/>
              <representationType type="IfcLabel" value="GeometricCurveSet"/>
              <items>
                <IfcRepresentationItem type="IfcGeometricCurveSet">
                  <elements>
                    <IfcGeometricSetSelect type="IfcPolyline">
                      <points>
                        <IfcCartesianPoint type="IfcCartesianPoint">
                          <coordinates>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                          </coordinates>
                        </IfcCartesianPoint>
                        <IfcCartesianPoint type="IfcCartesianPoint">
                          <coordinates>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="40.0"/>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                          </coordinates>
                        </IfcCartesianPoint>
                        <IfcCartesianPoint type="IfcCartesianPoint">
                          <coordinates>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="40.0"/>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="20.0"/>
                          </coordinates>
                        </IfcCartesianPoint>
                        <IfcCartesianPoint type="IfcCartesianPoint">
                          <coordinates>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="20.0"/>
                          </coordinates>
                        </IfcCartesianPoint>
                        <IfcCartesianPoint type="IfcCartesianPoint">
                          <coordinates>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                            <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                          </coordinates>
                        </IfcCartesianPoint>
                      </points>
                    </IfcGeometricSetSelect>
                  </elements>
                </IfcRepresentationItem>
              </items>
            </IfcRepresentation>
          </representations>
        </representation>
        <containsElements>
          <IfcRelContainedInSpatialStructure type="IfcRelContainedInSpatialStructure" globalId="91fd8f98-7bd4-4905-a782-f164b98e3feb">
            <relatedElements>
              <IfcProduct type="IfcMember" globalId="d4fafa8f-3f87-44ff-bc24-e9a672c02168">
                <isTypedBy>
                  <IfcRelDefinesByType type="IfcRelDefinesByType" globalId="802153a9-ae1e-4404-87d1-c9db4a77f9cc">
                    <relatingType>4aab78a8-3d43-4ee1-bc53-23341a283cca</relatingType>
                  </IfcRelDefinesByType>
                </isTypedBy>
                <objectPlacement type="IfcLocalPlacement" globalId="709ac0a3-fcf4-47c2-806f-b8bf28ab64e6">
                  <relativePlacement type="IfcAxis2Placement3D">
                    <location type="IfcCartesianPoint">
                      <coordinates>
                        <IfcLengthMeasure type="IfcLengthMeasure" value="1.92970621585846"/>
                        <IfcLengthMeasure type="IfcLengthMeasure" value="1.9296875"/>
                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                      </coordinates>
                    </location>
                    <axis type="IfcDirection">
                      <directionRatios>
                        <IfcReal type="IfcReal" value="0.0"/>
                        <IfcReal type="IfcReal" value="0.0"/>
                        <IfcReal type="IfcReal" value="1.0"/>
                      </directionRatios>
                    </axis>
                    <refDirection type="IfcDirection">
                      <directionRatios>
                        <IfcReal type="IfcReal" value="1.0"/>
                        <IfcReal type="IfcReal" value="0.0"/>
                        <IfcReal type="IfcReal" value="0.0"/>
                      </directionRatios>
                    </refDirection>
                  </relativePlacement>
                  <placementRelTo>d0b85c25-3e1f-4365-97c3-a8bb3a8dd376</placementRelTo>
                </objectPlacement>
                <representation type="IfcProductDefinitionShape" globalId="fc98845d-7d1d-453b-bf8e-42025e638e00">
                  <representations>
                    <IfcRepresentation type="IfcShapeRepresentation" globalId="16f671c4-6e50-4748-a7a9-e0b898017043">
                      <contextOfItems>a77a0761-be9c-44f6-8e42-dddf74415b84</contextOfItems>
                      <representationIdentifier type="IfcLabel" value="Body"/>
                      <representationType type="IfcLabel" value="MappedRepresentation"/>
                      <items>
                        <IfcRepresentationItem type="IfcMappedItem">
                          <mappingSource>39661ec5-1ada-48fa-9877-d5bcd70d4e6d</mappingSource>
                          <mappingTarget type="IfcCartesianTransformationOperator3D">
                            <localOrigin type="IfcCartesianPoint">
                              <coordinates>
                                <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                              </coordinates>
                            </localOrigin>
                            <scale type="IfcReal" value="1.0"/>
                          </mappingTarget>
                        </IfcRepresentationItem>
                      </items>
                    </IfcRepresentation>
                  </representations>
                </representation>
              </IfcProduct>
            </relatedElements>
          </IfcRelContainedInSpatialStructure>
        </containsElements>
      </IfcObjectDefinition>
    </relatedObjects>
  </isDecomposedBy>
  <representationContexts>
    <IfcRepresentationContext>a77a0761-be9c-44f6-8e42-dddf74415b84</IfcRepresentationContext>
    <IfcRepresentationContext type="IfcGeometricRepresentationContext" globalId="fe29f3d0-20cf-4bf9-8373-2a88600f24a5">
      <worldCoordinateSystem type="IfcAxis2Placement3D">
        <location type="IfcCartesianPoint">
          <coordinates>
            <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
            <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
            <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
          </coordinates>
        </location>
      </worldCoordinateSystem>
      <contextIdentifier type="IfcLabel" value="2D"/>
      <contextType type="IfcLabel" value="Plan"/>
      <coordinateSpaceDimension type="IfcDimensionCount" value="2"/>
      <precision type="IfcReal" value="1.0E-5"/>
    </IfcRepresentationContext>
  </representationContexts>
  <unitsInContext>
    <units>
      <IfcUnit type="IfcSIUnit" globalId="40815249-73c7-4d58-a122-4b5faf3aab6b">
        <dimensions lengthExponent="0" massExponent="0" timeExponent="0" electricCurrentExponent="1" thermodynamicTemperatureExponent="0" amountOfSubstanceExponent="0" luminousIntensityExponent="0"/>
        <unitType>ELECTRICCURRENTUNIT</unitType>
        <name>AMPERE</name>
      </IfcUnit>
      <IfcUnit type="IfcSIUnit" globalId="641838af-2644-4e4d-b314-32cc4bcd7068">
        <dimensions lengthExponent="1" massExponent="1" timeExponent="-2" electricCurrentExponent="0" thermodynamicTemperatureExponent="0" amountOfSubstanceExponent="0" luminousIntensityExponent="0"/>
        <unitType>FORCEUNIT</unitType>
        <name>NEWTON</name>
      </IfcUnit>
      <IfcUnit type="IfcSIUnit" globalId="b8d3ae6f-4beb-46c6-bd2d-8e8f876833a2" unitType="MAGNETICFLUXUNIT" name="WEBER"/>
      <IfcUnit type="IfcDerivedUnit" unitType="LINEARFORCEUNIT">
        <elements>
          <IfcDerivedUnitElement exponent="1">
            <unit>641838af-2644-4e4d-b314-32cc4bcd7068</unit>
          </IfcDerivedUnitElement>
          <IfcDerivedUnitElement exponent="-1">
            <unit type="IfcSIUnit" globalId="3b7dca74-4ba7-4763-a7a3-39e6b6c07227">
              <dimensions lengthExponent="1" massExponent="0" timeExponent="0" electricCurrentExponent="0" thermodynamicTemperatureExponent="0" amountOfSubstanceExponent="0" luminousIntensityExponent="0"/>
              <unitType>LENGTHUNIT</unitType>
              <name>METRE</name>
            </unit>
          </IfcDerivedUnitElement>
        </elements>
      </IfcUnit>
      <IfcUnit type="IfcDerivedUnit" unitType="LINEARVELOCITYUNIT">
        <elements>
          <IfcDerivedUnitElement exponent="1">
            <unit>3b7dca74-4ba7-4763-a7a3-39e6b6c07227</unit>
          </IfcDerivedUnitElement>
          <IfcDerivedUnitElement exponent="-1">
            <unit type="IfcSIUnit" globalId="d0a685c7-bada-4d4d-9de9-56f8d7273082">
              <dimensions lengthExponent="0" massExponent="0" timeExponent="1" electricCurrentExponent="0" thermodynamicTemperatureExponent="0" amountOfSubstanceExponent="0" luminousIntensityExponent="0"/>
              <unitType>TIMEUNIT</unitType>
              <name>SECOND</name>
            </unit>
          </IfcDerivedUnitElement>
        </elements>
      </IfcUnit>
      <IfcUnit type="IfcDerivedUnit" unitType="IONCONCENTRATIONUNIT">
        <elements>
          <IfcDerivedUnitElement exponent="1">
            <unit type="IfcSIUnit" globalId="33280358-a15a-4690-a3e6-0b27eda119e6">
              <dimensions lengthExponent="0" massExponent="1" timeExponent="0" electricCurrentExponent="0" thermodynamicTemperatureExponent="0" amountOfSubstanceExponent="0" luminousIntensityExponent="0"/>
              <unitType>MASSUNIT</unitType>
              <name>GRAM</name>
            </unit>
          </IfcDerivedUnitElement>
          <IfcDerivedUnitElement exponent="-1">
            <unit type="IfcSIUnit" globalId="e78ce898-aea8-4b5e-9057-5bf2a62704d5">
              <dimensions lengthExponent="3" massExponent="0" timeExponent="0" electricCurrentExponent="0" thermodynamicTemperatureExponent="0" amountOfSubstanceExponent="0" luminousIntensityExponent="0"/>
              <unitType>VOLUMEUNIT</unitType>
              <name>CUBIC_METRE</name>
            </unit>
          </IfcDerivedUnitElement>
        </elements>
      </IfcUnit>
      <IfcUnit type="IfcDerivedUnit" unitType="MASSFLOWRATEUNIT">
        <elements>
          <IfcDerivedUnitElement exponent="1">
            <unit type="IfcSIUnit" globalId="ea122394-a483-46e2-8555-193e281cbff2">
              <dimensions lengthExponent="0" massExponent="1" timeExponent="0" electricCurrentExponent="0" thermodynamicTemperatureExponent="0" amountOfSubstanceExponent="0" luminousIntensityExponent="0"/>
              <unitType>MASSUNIT</unitType>
              <prefix>KILO</prefix>
              <name>GRAM</name>
            </unit>
          </IfcDerivedUnitElement>
          <IfcDerivedUnitElement exponent="-1">
            <unit>d0a685c7-bada-4d4d-9de9-56f8d7273082</unit>
          </IfcDerivedUnitElement>
        </elements>
      </IfcUnit>
      <IfcUnit type="IfcSIUnit" globalId="0908d247-e97e-4e8c-a5d7-eaf8181fece0">
        <dimensions lengthExponent="0" massExponent="0" timeExponent="1" electricCurrentExponent="1" thermodynamicTemperatureExponent="0" amountOfSubstanceExponent="0" luminousIntensityExponent="0"/>
        <unitType>ELECTRICCHARGEUNIT</unitType>
        <name>COULOMB</name>
      </IfcUnit>
      <IfcUnit type="IfcDerivedUnit" unitType="LINEARMOMENTUNIT">
        <elements>
          <IfcDerivedUnitElement exponent="1">
            <unit>641838af-2644-4e4d-b314-32cc4bcd7068</unit>
          </IfcDerivedUnitElement>
          <IfcDerivedUnitElement exponent="1">
            <unit>3b7dca74-4ba7-4763-a7a3-39e6b6c07227</unit>
          </IfcDerivedUnitElement>
          <IfcDerivedUnitElement exponent="-1">
            <unit>3b7dca74-4ba7-4763-a7a3-39e6b6c07227</unit>
          </IfcDerivedUnitElement>
        </elements>
      </IfcUnit>
      <IfcUnit type="IfcSIUnit" globalId="63087ada-c043-4b89-83fd-c8cb32722a73">
        <dimensions lengthExponent="0" massExponent="1" timeExponent="-2" electricCurrentExponent="-1" thermodynamicTemperatureExponent="0" amountOfSubstanceExponent="0" luminousIntensityExponent="0"/>
        <unitType>MAGNETICFLUXDENSITYUNIT</unitType>
        <name>TESLA</name>
      </IfcUnit>
      <IfcUnit type="IfcDerivedUnit" unitType="ACCELERATIONUNIT">
        <elements>
          <IfcDerivedUnitElement exponent="1">
            <unit>3b7dca74-4ba7-4763-a7a3-39e6b6c07227</unit>
          </IfcDerivedUnitElement>
          <IfcDerivedUnitElement exponent="-1">
            <unit>d0a685c7-bada-4d4d-9de9-56f8d7273082</unit>
          </IfcDerivedUnitElement>
          <IfcDerivedUnitElement exponent="-1">
            <unit>d0a685c7-bada-4d4d-9de9-56f8d7273082</unit>
          </IfcDerivedUnitElement>
        </elements>
      </IfcUnit>
      <IfcUnit type="IfcDerivedUnit" unitType="WARPINGCONSTANTUNIT">
        <elements>
          <IfcDerivedUnitElement exponent="6">
            <unit>3b7dca74-4ba7-4763-a7a3-39e6b6c07227</unit>
          </IfcDerivedUnitElement>
        </elements>
      </IfcUnit>
      <IfcUnit type="IfcDerivedUnit" unitType="ISOTHERMALMOISTURECAPACITYUNIT">
        <elements>
          <IfcDerivedUnitElement exponent="-1">
            <unit type="IfcSIUnit" globalId="a2dfa130-e203-454d-b8ce-3e3e71e1ef56">
              <dimensions lengthExponent="2" massExponent="0" timeExponent="-2" electricCurrentExponent="0" thermodynamicTemperatureExponent="0" amountOfSubstanceExponent="0" luminousIntensityExponent="0"/>
              <unitType>ABSORBEDDOSEUNIT</unitType>
              <name>GRAY</name>
            </unit>
          </IfcDerivedUnitElement>
          <IfcDerivedUnitElement exponent="1">
            <unit>e78ce898-aea8-4b5e-9057-5bf2a62704d5</unit>
          </IfcDerivedUnitElement>
        </elements>
      </IfcUnit>
      <IfcUnit type="IfcDerivedUnit" unitType="SOUNDPOWERUNIT">
        <elements>
          <IfcDerivedUnitElement exponent="0">
            <unit type="IfcSIUnit" globalId="def52445-c114-4b77-b194-b63158a89858">
              <dimensions lengthExponent="2" massExponent="1" timeExponent="-3" electricCurrentExponent="0" thermodynamicTemperatureExponent="0" amountOfSubstanceExponent="0" luminousIntensityExponent="0"/>
              <unitType>POWERUNIT</unitType>
              <prefix>PICO</prefix>
              <name>WATT</name>
            </unit>
          </IfcDerivedUnitElement>
        </elements>
      </IfcUnit>
      <IfcUnit type="IfcSIUnit" globalId="7aa0082e-a18c-4a65-855f-675d6b94fd38">
        <dimensions lengthExponent="0" massExponent="0" timeExponent="-1" electricCurrentExponent="0" thermodynamicTemperatureExponent="0" amountOfSubstanceExponent="0" luminousIntensityExponent="0"/>
        <unitType>RADIOACTIVITYUNIT</unitType>
        <name>BECQUEREL</name>
      </IfcUnit>
      <IfcUnit type="IfcSIUnit" globalId="afe0f673-79b0-484d-b3d3-2db7ca6a7d7d">
        <dimensions lengthExponent="-2" massExponent="-1" timeExponent="3" electricCurrentExponent="2" thermodynamicTemperatureExponent="0" amountOfSubstanceExponent="0" luminousIntensityExponent="0"/>
        <unitType>ELECTRICCONDUCTANCEUNIT</unitType>
        <name>SIEMENS</name>
      </IfcUnit>
      <IfcUnit type="IfcSIUnit" globalId="194c5daa-64fd-42b2-b463-b244eaf38df5">
        <dimensions lengthExponent="2" massExponent="1" timeExponent="-2" electricCurrentExponent="0" thermodynamicTemperatureExponent="0" amountOfSubstanceExponent="0" luminousIntensityExponent="0"/>
        <unitType>ENERGYUNIT</unitType>
        <name>JOULE</name>
      </IfcUnit>
      <IfcUnit type="IfcDerivedUnit" unitType="MOISTUREDIFFUSIVITYUNIT">
        <elements>
          <IfcDerivedUnitElement exponent="-1">
            <unit>d0a685c7-bada-4d4d-9de9-56f8d7273082</unit>
          </IfcDerivedUnitElement>
          <IfcDerivedUnitElement exponent="1">
            <unit>e78ce898-aea8-4b5e-9057-5bf2a62704d5</unit>
          </IfcDerivedUnitElement>
        </elements>
      </IfcUnit>
      <IfcUnit type="IfcDerivedUnit" unitType="MODULUSOFSUBGRADEREACTIONUNIT">
        <elements>
          <IfcDerivedUnitElement exponent="1">
            <unit>641838af-2644-4e4d-b314-32cc4bcd7068</unit>
          </IfcDerivedUnitElement>
          <IfcDerivedUnitElement exponent="-1">
            <unit>e78ce898-aea8-4b5e-9057-5bf2a62704d5</unit>
          </IfcDerivedUnitElement>
        </elements>
      </IfcUnit>
      <IfcUnit type="IfcSIUnit" globalId="bec7e40d-0642-497c-a1c7-e442c3bb5182">
        <dimensions lengthExponent="2" massExponent="1" timeExponent="-3" electricCurrentExponent="-1" thermodynamicTemperatureExponent="0" amountOfSubstanceExponent="0" luminousIntensityExponent="0"/>
        <unitType>ELECTRICVOLTAGEUNIT</unitType>
        <name>VOLT</name>
      </IfcUnit>
      <IfcUnit type="IfcSIUnit" globalId="d01a8e5b-efc8-49aa-994a-0f4978eeb2ee">
        <dimensions lengthExponent="0" massExponent="0" timeExponent="-1" electricCurrentExponent="0" thermodynamicTemperatureExponent="0" amountOfSubstanceExponent="0" luminousIntensityExponent="0"/>
        <unitType>FREQUENCYUNIT</unitType>
        <name>HERTZ</name>
      </IfcUnit>
      <IfcUnit type="IfcDerivedUnit" unitType="MOLECULARWEIGHTUNIT">
        <elements>
          <IfcDerivedUnitElement exponent="-1">
            <unit type="IfcSIUnit" globalId="e6b83859-3ab5-4674-b9b7-91ef989512a9">
              <dimensions lengthExponent="0" massExponent="0" timeExponent="0" electricCurrentExponent="0" thermodynamicTemperatureExponent="0" amountOfSubstanceExponent="1" luminousIntensityExponent="0"/>
              <unitType>AMOUNTOFSUBSTANCEUNIT</unitType>
              <name>MOLE</name>
            </unit>
          </IfcDerivedUnitElement>
          <IfcDerivedUnitElement exponent="1">
            <unit>33280358-a15a-4690-a3e6-0b27eda119e6</unit>
          </IfcDerivedUnitElement>
        </elements>
      </IfcUnit>
      <IfcUnit type="IfcDerivedUnit" unitType="WARPINGMOMENTUNIT">
        <elements>
          <IfcDerivedUnitElement exponent="2">
            <unit>3b7dca74-4ba7-4763-a7a3-39e6b6c07227</unit>
          </IfcDerivedUnitElement>
          <IfcDerivedUnitElement exponent="1">
            <unit>641838af-2644-4e4d-b314-32cc4bcd7068</unit>
          </IfcDerivedUnitElement>
        </elements>
      </IfcUnit>
      <IfcUnit type="IfcDerivedUnit" unitType="MODULUSOFROTATIONALSUBGRADEREACTIONUNIT">
        <elements>
          <IfcDerivedUnitElement exponent="1">
            <unit>641838af-2644-4e4d-b314-32cc4bcd7068</unit>
          </IfcDerivedUnitElement>
          <IfcDerivedUnitElement exponent="-1">
            <unit type="IfcConversionBasedUnit" globalId="f2028f76-e8d1-470b-ae23-b43328550563">
              <dimensions lengthExponent="0" massExponent="0" timeExponent="0" electricCurrentExponent="0" thermodynamicTemperatureExponent="0" amountOfSubstanceExponent="0" luminousIntensityExponent="0"/>
              <unitType>PLANEANGLEUNIT</unitType>
              <name>degree</name>
              <conversionFactor type="IfcMeasureWithUnit">
                <valueComponent type="IfcPlaneAngleMeasure" value="0.0174532925199433"/>
                <unitComponent type="IfcSIUnit" globalId="6cd886f9-d6d2-4165-830e-4a089bc71d99">
                  <dimensions lengthExponent="0" massExponent="0" timeExponent="0" electricCurrentExponent="0" thermodynamicTemperatureExponent="0" amountOfSubstanceExponent="0" luminousIntensityExponent="0"/>
                  <unitType>PLANEANGLEUNIT</unitType>
                  <name>RADIAN</name>
                </unitComponent>
              </conversionFactor>
            </unit>
          </IfcDerivedUnitElement>
          <IfcDerivedUnitElement exponent="-1">
            <unit>3b7dca74-4ba7-4763-a7a3-39e6b6c07227</unit>
          </IfcDerivedUnitElement>
          <IfcDerivedUnitElement exponent="1">
            <unit>3b7dca74-4ba7-4763-a7a3-39e6b6c07227</unit>
          </IfcDerivedUnitElement>
        </elements>
      </IfcUnit>
      <IfcUnit type="IfcSIUnit" globalId="18e44a96-50ae-4482-9623-6b23d15b16c6">
        <dimensions lengthExponent="-2" massExponent="0" timeExponent="0" electricCurrentExponent="0" thermodynamicTemperatureExponent="0" amountOfSubstanceExponent="0" luminousIntensityExponent="1"/>
        <unitType>ILLUMINANCEUNIT</unitType>
        <name>LUX</name>
      </IfcUnit>
      <IfcUnit type="IfcDerivedUnit" unitType="MASSDENSITYUNIT">
        <elements>
          <IfcDerivedUnitElement exponent="-1">
            <unit>e78ce898-aea8-4b5e-9057-5bf2a62704d5</unit>
          </IfcDerivedUnitElement>
          <IfcDerivedUnitElement exponent="1">
            <unit>ea122394-a483-46e2-8555-193e281cbff2</unit>
          </IfcDerivedUnitElement>
        </elements>
      </IfcUnit>
      <IfcUnit>e78ce898-aea8-4b5e-9057-5bf2a62704d5</IfcUnit>
      <IfcUnit type="IfcDerivedUnit" unitType="ROTATIONALSTIFFNESSUNIT">
        <elements>
          <IfcDerivedUnitElement exponent="1">
            <unit>3b7dca74-4ba7-4763-a7a3-39e6b6c07227</unit>
          </IfcDerivedUnitElement>
          <IfcDerivedUnitElement exponent="1">
            <unit>641838af-2644-4e4d-b314-32cc4bcd7068</unit>
          </IfcDerivedUnitElement>
          <IfcDerivedUnitElement exponent="-1">
            <unit>f2028f76-e8d1-470b-ae23-b43328550563</unit>
          </IfcDerivedUnitElement>
        </elements>
      </IfcUnit>
      <IfcUnit type="IfcDerivedUnit" unitType="ANGULARVELOCITYUNIT">
        <elements>
          <IfcDerivedUnitElement exponent="1">
            <unit>f2028f76-e8d1-470b-ae23-b43328550563</unit>
          </IfcDerivedUnitElement>
          <IfcDerivedUnitElement exponent="-1">
            <unit>d0a685c7-bada-4d4d-9de9-56f8d7273082</unit>
          </IfcDerivedUnitElement>
        </elements>
      </IfcUnit>
      <IfcUnit type="IfcDerivedUnit" unitType="SECTIONMODULUSUNIT">
        <elements>
          <IfcDerivedUnitElement exponent="3">
            <unit>3b7dca74-4ba7-4763-a7a3-39e6b6c07227</unit>
          </IfcDerivedUnitElement>
        </elements>
      </IfcUnit>
      <IfcUnit type="IfcSIUnit" globalId="f7d57e26-b491-40eb-b387-34d0d8496724">
        <dimensions lengthExponent="2" massExponent="1" timeExponent="-3" electricCurrentExponent="-2" thermodynamicTemperatureExponent="0" amountOfSubstanceExponent="0" luminousIntensityExponent="0"/>
        <unitType>ELECTRICRESISTANCEUNIT</unitType>
        <name>OHM</name>
      </IfcUnit>
      <IfcUnit type="IfcDerivedUnit" unitType="HEATFLUXDENSITYUNIT">
        <elements>
          <IfcDerivedUnitElement exponent="1">
            <unit type="IfcSIUnit" globalId="e0c58107-1e30-45ec-a096-cf2112202530">
              <dimensions lengthExponent="2" massExponent="1" timeExponent="-3" electricCurrentExponent="0" thermodynamicTemperatureExponent="0" amountOfSubstanceExponent="0" luminousIntensityExponent="0"/>
              <unitType>POWERUNIT</unitType>
              <name>WATT</name>
            </unit>
          </IfcDerivedUnitElement>
          <IfcDerivedUnitElement exponent="-1">
            <unit type="IfcSIUnit" globalId="e42a44d3-0030-4763-ba0b-48feccea9491">
              <dimensions lengthExponent="2" massExponent="0" timeExponent="0" electricCurrentExponent="0" thermodynamicTemperatureExponent="0" amountOfSubstanceExponent="0" luminousIntensityExponent="0"/>
              <unitType>AREAUNIT</unitType>
              <name>SQUARE_METRE</name>
            </unit>
          </IfcDerivedUnitElement>
        </elements>
      </IfcUnit>
      <IfcUnit type="IfcDerivedUnit" unitType="TEMPERATUREGRADIENTUNIT">
        <elements>
          <IfcDerivedUnitElement exponent="-1">
            <unit>3b7dca74-4ba7-4763-a7a3-39e6b6c07227</unit>
          </IfcDerivedUnitElement>
          <IfcDerivedUnitElement exponent="1">
            <unit type="IfcSIUnit" globalId="56f9088f-2803-4fed-a76d-8649c3d575b6">
              <dimensions lengthExponent="0" massExponent="0" timeExponent="0" electricCurrentExponent="0" thermodynamicTemperatureExponent="1" amountOfSubstanceExponent="0" luminousIntensityExponent="0"/>
              <unitType>THERMODYNAMICTEMPERATUREUNIT</unitType>
              <name>KELVIN</name>
            </unit>
          </IfcDerivedUnitElement>
        </elements>
      </IfcUnit>
      <IfcUnit type="IfcDerivedUnit" unitType="ROTATIONALFREQUENCYUNIT">
        <elements>
          <IfcDerivedUnitElement exponent="-1">
            <unit>d0a685c7-bada-4d4d-9de9-56f8d7273082</unit>
          </IfcDerivedUnitElement>
        </elements>
      </IfcUnit>
      <IfcUnit type="IfcDerivedUnit" unitType="VAPORPERMEABILITYUNIT">
        <elements>
          <IfcDerivedUnitElement exponent="-1">
            <unit>d0a685c7-bada-4d4d-9de9-56f8d7273082</unit>
          </IfcDerivedUnitElement>
          <IfcDerivedUnitElement exponent="1">
            <unit>ea122394-a483-46e2-8555-193e281cbff2</unit>
          </IfcDerivedUnitElement>
          <IfcDerivedUnitElement exponent="-1">
            <unit>3b7dca74-4ba7-4763-a7a3-39e6b6c07227</unit>
          </IfcDerivedUnitElement>
        </elements>
      </IfcUnit>
      <IfcUnit type="IfcDerivedUnit" unitType="INTEGERCOUNTRATEUNIT">
        <elements>
          <IfcDerivedUnitElement exponent="-1">
            <unit>d0a685c7-bada-4d4d-9de9-56f8d7273082</unit>
          </IfcDerivedUnitElement>
        </elements>
      </IfcUnit>
      <IfcUnit type="IfcDerivedUnit" unitType="MASSPERLENGTHUNIT">
        <elements>
          <IfcDerivedUnitElement exponent="-1">
            <unit>3b7dca74-4ba7-4763-a7a3-39e6b6c07227</unit>
          </IfcDerivedUnitElement>
          <IfcDerivedUnitElement exponent="1">
            <unit>ea122394-a483-46e2-8555-193e281cbff2</unit>
          </IfcDerivedUnitElement>
        </elements>
      </IfcUnit>
      <IfcUnit type="IfcDerivedUnit" unitType="TEMPERATURERATEOFCHANGEUNIT">
        <elements>
          <IfcDerivedUnitElement exponent="1">
            <unit>56f9088f-2803-4fed-a76d-8649c3d575b6</unit>
          </IfcDerivedUnitElement>
          <IfcDerivedUnitElement exponent="-1">
            <unit>d0a685c7-bada-4d4d-9de9-56f8d7273082</unit>
          </IfcDerivedUnitElement>
        </elements>
      </IfcUnit>
      <IfcUnit type="IfcDerivedUnit" unitType="VOLUMETRICFLOWRATEUNIT">
        <elements>
          <IfcDerivedUnitElement exponent="-1">
            <unit>d0a685c7-bada-4d4d-9de9-56f8d7273082</unit>
          </IfcDerivedUnitElement>
          <IfcDerivedUnitElement exponent="1">
            <unit>e78ce898-aea8-4b5e-9057-5bf2a62704d5</unit>
          </IfcDerivedUnitElement>
        </elements>
      </IfcUnit>
      <IfcUnit type="IfcDerivedUnit" unitType="HEATINGVALUEUNIT">
        <elements>
          <IfcDerivedUnitElement exponent="1">
            <unit>194c5daa-64fd-42b2-b463-b244eaf38df5</unit>
          </IfcDerivedUnitElement>
          <IfcDerivedUnitElement exponent="-1">
            <unit>ea122394-a483-46e2-8555-193e281cbff2</unit>
          </IfcDerivedUnitElement>
        </elements>
      </IfcUnit>
      <IfcUnit type="IfcMonetaryUnit" currency="USD"/>
      <IfcUnit type="IfcDerivedUnit" unitType="THERMALTRANSMITTANCEUNIT">
        <elements>
          <IfcDerivedUnitElement exponent="1">
            <unit>e0c58107-1e30-45ec-a096-cf2112202530</unit>
          </IfcDerivedUnitElement>
          <IfcDerivedUnitElement exponent="-1">
            <unit>e42a44d3-0030-4763-ba0b-48feccea9491</unit>
          </IfcDerivedUnitElement>
          <IfcDerivedUnitElement exponent="1">
            <unit>56f9088f-2803-4fed-a76d-8649c3d575b6</unit>
          </IfcDerivedUnitElement>
        </elements>
      </IfcUnit>
      <IfcUnit>ea122394-a483-46e2-8555-193e281cbff2</IfcUnit>
      <IfcUnit type="IfcDerivedUnit" unitType="COMPOUNDPLANEANGLEUNIT">
        <elements>
          <IfcDerivedUnitElement exponent="1">
            <unit>f2028f76-e8d1-470b-ae23-b43328550563</unit>
          </IfcDerivedUnitElement>
        </elements>
      </IfcUnit>
      <IfcUnit type="IfcDerivedUnit" unitType="LINEARSTIFFNESSUNIT">
        <elements>
          <IfcDerivedUnitElement exponent="-1">
            <unit>3b7dca74-4ba7-4763-a7a3-39e6b6c07227</unit>
          </IfcDerivedUnitElement>
          <IfcDerivedUnitElement exponent="1">
            <unit>641838af-2644-4e4d-b314-32cc4bcd7068</unit>
          </IfcDerivedUnitElement>
        </elements>
      </IfcUnit>
      <IfcUnit>d0a685c7-bada-4d4d-9de9-56f8d7273082</IfcUnit>
      <IfcUnit type="IfcSIUnit" globalId="90bc3d39-ed04-4424-b975-37e0df81c2fa">
        <dimensions lengthExponent="-1" massExponent="1" timeExponent="-2" electricCurrentExponent="0" thermodynamicTemperatureExponent="0" amountOfSubstanceExponent="0" luminousIntensityExponent="0"/>
        <unitType>PRESSUREUNIT</unitType>
        <name>PASCAL</name>
      </IfcUnit>
      <IfcUnit type="IfcDerivedUnit" unitType="MOMENTOFINERTIAUNIT">
        <elements>
          <IfcDerivedUnitElement exponent="4">
            <unit>3b7dca74-4ba7-4763-a7a3-39e6b6c07227</unit>
          </IfcDerivedUnitElement>
        </elements>
      </IfcUnit>
      <IfcUnit>3b7dca74-4ba7-4763-a7a3-39e6b6c07227</IfcUnit>
      <IfcUnit type="IfcDerivedUnit" unitType="SECTIONAREAINTEGRALUNIT">
        <elements>
          <IfcDerivedUnitElement exponent="5">
            <unit>3b7dca74-4ba7-4763-a7a3-39e6b6c07227</unit>
          </IfcDerivedUnitElement>
        </elements>
      </IfcUnit>
      <IfcUnit type="IfcDerivedUnit" unitType="THERMALEXPANSIONCOEFFICIENTUNIT">
        <elements>
          <IfcDerivedUnitElement exponent="-1">
            <unit>56f9088f-2803-4fed-a76d-8649c3d575b6</unit>
          </IfcDerivedUnitElement>
        </elements>
      </IfcUnit>
      <IfcUnit type="IfcDerivedUnit" unitType="THERMALRESISTANCEUNIT">
        <elements>
          <IfcDerivedUnitElement exponent="1">
            <unit>e42a44d3-0030-4763-ba0b-48feccea9491</unit>
          </IfcDerivedUnitElement>
          <IfcDerivedUnitElement exponent="1">
            <unit>56f9088f-2803-4fed-a76d-8649c3d575b6</unit>
          </IfcDerivedUnitElement>
          <IfcDerivedUnitElement exponent="-1">
            <unit>e0c58107-1e30-45ec-a096-cf2112202530</unit>
          </IfcDerivedUnitElement>
        </elements>
      </IfcUnit>
      <IfcUnit type="IfcDerivedUnit" unitType="CURVATUREUNIT">
        <elements>
          <IfcDerivedUnitElement exponent="1">
            <unit>f2028f76-e8d1-470b-ae23-b43328550563</unit>
          </IfcDerivedUnitElement>
          <IfcDerivedUnitElement exponent="-1">
            <unit>3b7dca74-4ba7-4763-a7a3-39e6b6c07227</unit>
          </IfcDerivedUnitElement>
        </elements>
      </IfcUnit>
      <IfcUnit type="IfcDerivedUnit" unitType="DYNAMICVISCOSITYUNIT">
        <elements>
          <IfcDerivedUnitElement exponent="1">
            <unit>90bc3d39-ed04-4424-b975-37e0df81c2fa</unit>
          </IfcDerivedUnitElement>
          <IfcDerivedUnitElement exponent="1">
            <unit>d0a685c7-bada-4d4d-9de9-56f8d7273082</unit>
          </IfcDerivedUnitElement>
        </elements>
      </IfcUnit>
      <IfcUnit type="IfcDerivedUnit" unitType="PLANARFORCEUNIT">
        <elements>
          <IfcDerivedUnitElement exponent="-1">
            <unit>e42a44d3-0030-4763-ba0b-48feccea9491</unit>
          </IfcDerivedUnitElement>
          <IfcDerivedUnitElement exponent="1">
            <unit>641838af-2644-4e4d-b314-32cc4bcd7068</unit>
          </IfcDerivedUnitElement>
        </elements>
      </IfcUnit>
      <IfcUnit type="IfcDerivedUnit" unitType="THERMALADMITTANCEUNIT">
        <elements>
          <IfcDerivedUnitElement exponent="-1">
            <unit>56f9088f-2803-4fed-a76d-8649c3d575b6</unit>
          </IfcDerivedUnitElement>
          <IfcDerivedUnitElement exponent="-1">
            <unit>e42a44d3-0030-4763-ba0b-48feccea9491</unit>
          </IfcDerivedUnitElement>
          <IfcDerivedUnitElement exponent="1">
            <unit>e0c58107-1e30-45ec-a096-cf2112202530</unit>
          </IfcDerivedUnitElement>
        </elements>
      </IfcUnit>
      <IfcUnit type="IfcSIUnit" globalId="f0d3d5c6-e96f-4763-b455-efa4547d9ccc">
        <dimensions lengthExponent="0" massExponent="0" timeExponent="0" electricCurrentExponent="0" thermodynamicTemperatureExponent="0" amountOfSubstanceExponent="0" luminousIntensityExponent="1"/>
        <unitType>LUMINOUSFLUXUNIT</unitType>
        <name>LUMEN</name>
      </IfcUnit>
      <IfcUnit type="IfcSIUnit" globalId="6d31dfeb-0a7b-42c4-b1ff-0db30bba2137">
        <dimensions lengthExponent="0" massExponent="0" timeExponent="0" electricCurrentExponent="0" thermodynamicTemperatureExponent="0" amountOfSubstanceExponent="0" luminousIntensityExponent="1"/>
        <unitType>LUMINOUSINTENSITYUNIT</unitType>
        <name>CANDELA</name>
      </IfcUnit>
      <IfcUnit type="IfcSIUnit" globalId="9f7d8036-aa5c-449a-9e3f-096dfbe94148">
        <dimensions lengthExponent="0" massExponent="0" timeExponent="0" electricCurrentExponent="0" thermodynamicTemperatureExponent="1" amountOfSubstanceExponent="0" luminousIntensityExponent="0"/>
        <unitType>THERMODYNAMICTEMPERATUREUNIT</unitType>
        <name>DEGREE_CELSIUS</name>
      </IfcUnit>
      <IfcUnit type="IfcDerivedUnit" unitType="LUMINOUSINTENSITYDISTRIBUTIONUNIT">
        <elements>
          <IfcDerivedUnitElement exponent="-1">
            <unit>f0d3d5c6-e96f-4763-b455-efa4547d9ccc</unit>
          </IfcDerivedUnitElement>
          <IfcDerivedUnitElement exponent="1">
            <unit>6d31dfeb-0a7b-42c4-b1ff-0db30bba2137</unit>
          </IfcDerivedUnitElement>
        </elements>
      </IfcUnit>
      <IfcUnit>e42a44d3-0030-4763-ba0b-48feccea9491</IfcUnit>
      <IfcUnit type="IfcDerivedUnit" unitType="SPECIFICHEATCAPACITYUNIT">
        <elements>
          <IfcDerivedUnitElement exponent="-1">
            <unit>56f9088f-2803-4fed-a76d-8649c3d575b6</unit>
          </IfcDerivedUnitElement>
          <IfcDerivedUnitElement exponent="-1">
            <unit>ea122394-a483-46e2-8555-193e281cbff2</unit>
          </IfcDerivedUnitElement>
          <IfcDerivedUnitElement exponent="1">
            <unit>194c5daa-64fd-42b2-b463-b244eaf38df5</unit>
          </IfcDerivedUnitElement>
        </elements>
      </IfcUnit>
      <IfcUnit type="IfcDerivedUnit" unitType="MODULUSOFLINEARSUBGRADEREACTIONUNIT">
        <elements>
          <IfcDerivedUnitElement exponent="-1">
            <unit>e42a44d3-0030-4763-ba0b-48feccea9491</unit>
          </IfcDerivedUnitElement>
          <IfcDerivedUnitElement exponent="1">
            <unit>641838af-2644-4e4d-b314-32cc4bcd7068</unit>
          </IfcDerivedUnitElement>
        </elements>
      </IfcUnit>
      <IfcUnit type="IfcDerivedUnit" unitType="SOUNDPRESSUREUNIT">
        <elements>
          <IfcDerivedUnitElement exponent="0">
            <unit type="IfcSIUnit" globalId="668352ff-5912-4134-9901-4546a6288175">
              <dimensions lengthExponent="-1" massExponent="1" timeExponent="-2" electricCurrentExponent="0" thermodynamicTemperatureExponent="0" amountOfSubstanceExponent="0" luminousIntensityExponent="0"/>
              <unitType>PRESSUREUNIT</unitType>
              <prefix>MICRO</prefix>
              <name>PASCAL</name>
            </unit>
          </IfcDerivedUnitElement>
        </elements>
      </IfcUnit>
      <IfcUnit type="IfcDerivedUnit" unitType="KINEMATICVISCOSITYUNIT">
        <elements>
          <IfcDerivedUnitElement exponent="-1">
            <unit>d0a685c7-bada-4d4d-9de9-56f8d7273082</unit>
          </IfcDerivedUnitElement>
          <IfcDerivedUnitElement exponent="1">
            <unit>e42a44d3-0030-4763-ba0b-48feccea9491</unit>
          </IfcDerivedUnitElement>
        </elements>
      </IfcUnit>
      <IfcUnit>f2028f76-e8d1-470b-ae23-b43328550563</IfcUnit>
      <IfcUnit type="IfcDerivedUnit" unitType="ROTATIONALMASSUNIT">
        <elements>
          <IfcDerivedUnitElement exponent="1">
            <unit>ea122394-a483-46e2-8555-193e281cbff2</unit>
          </IfcDerivedUnitElement>
          <IfcDerivedUnitElement exponent="-1">
            <unit>e42a44d3-0030-4763-ba0b-48feccea9491</unit>
          </IfcDerivedUnitElement>
        </elements>
      </IfcUnit>
      <IfcUnit type="IfcDerivedUnit" unitType="TORQUEUNIT">
        <elements>
          <IfcDerivedUnitElement exponent="1">
            <unit>3b7dca74-4ba7-4763-a7a3-39e6b6c07227</unit>
          </IfcDerivedUnitElement>
          <IfcDerivedUnitElement exponent="1">
            <unit>641838af-2644-4e4d-b314-32cc4bcd7068</unit>
          </IfcDerivedUnitElement>
        </elements>
      </IfcUnit>
      <IfcUnit>a2dfa130-e203-454d-b8ce-3e3e71e1ef56</IfcUnit>
      <IfcUnit type="IfcSIUnit" globalId="4aa71ef6-0dc5-464e-801a-d1a040cff31e">
        <dimensions lengthExponent="2" massExponent="1" timeExponent="-2" electricCurrentExponent="-2" thermodynamicTemperatureExponent="0" amountOfSubstanceExponent="0" luminousIntensityExponent="0"/>
        <unitType>INDUCTANCEUNIT</unitType>
        <name>HENRY</name>
      </IfcUnit>
      <IfcUnit type="IfcSIUnit" globalId="6bf7e9b2-a20d-496c-88db-5b1ce5031932">
        <dimensions lengthExponent="-2" massExponent="-1" timeExponent="4" electricCurrentExponent="2" thermodynamicTemperatureExponent="0" amountOfSubstanceExponent="0" luminousIntensityExponent="0"/>
        <unitType>ELECTRICCAPACITANCEUNIT</unitType>
        <name>FARAD</name>
      </IfcUnit>
      <IfcUnit>e6b83859-3ab5-4674-b9b7-91ef989512a9</IfcUnit>
      <IfcUnit type="IfcDerivedUnit" unitType="MODULUSOFELASTICITYUNIT">
        <elements>
          <IfcDerivedUnitElement exponent="1">
            <unit>90bc3d39-ed04-4424-b975-37e0df81c2fa</unit>
          </IfcDerivedUnitElement>
        </elements>
      </IfcUnit>
      <IfcUnit type="IfcSIUnit" globalId="f74742ac-0c65-447b-aca5-3787942cf410">
        <dimensions lengthExponent="2" massExponent="0" timeExponent="-2" electricCurrentExponent="0" thermodynamicTemperatureExponent="0" amountOfSubstanceExponent="0" luminousIntensityExponent="0"/>
        <unitType>DOSEEQUIVALENTUNIT</unitType>
        <name>SIEVERT</name>
      </IfcUnit>
      <IfcUnit type="IfcSIUnit" globalId="07e5394c-5c6d-4d14-ab5d-fd88538906d9">
        <dimensions lengthExponent="0" massExponent="0" timeExponent="0" electricCurrentExponent="0" thermodynamicTemperatureExponent="0" amountOfSubstanceExponent="0" luminousIntensityExponent="0"/>
        <unitType>SOLIDANGLEUNIT</unitType>
        <name>STERADIAN</name>
      </IfcUnit>
      <IfcUnit type="IfcDerivedUnit" unitType="SHEARMODULUSUNIT">
        <elements>
          <IfcDerivedUnitElement exponent="1">
            <unit>90bc3d39-ed04-4424-b975-37e0df81c2fa</unit>
          </IfcDerivedUnitElement>
        </elements>
      </IfcUnit>
      <IfcUnit type="IfcDerivedUnit" unitType="THERMALCONDUCTANCEUNIT">
        <elements>
          <IfcDerivedUnitElement exponent="-1">
            <unit>56f9088f-2803-4fed-a76d-8649c3d575b6</unit>
          </IfcDerivedUnitElement>
          <IfcDerivedUnitElement exponent="1">
            <unit>e0c58107-1e30-45ec-a096-cf2112202530</unit>
          </IfcDerivedUnitElement>
          <IfcDerivedUnitElement exponent="-1">
            <unit>3b7dca74-4ba7-4763-a7a3-39e6b6c07227</unit>
          </IfcDerivedUnitElement>
        </elements>
      </IfcUnit>
      <IfcUnit>e0c58107-1e30-45ec-a096-cf2112202530</IfcUnit>
    </units>
  </unitsInContext>
</IfcProject>

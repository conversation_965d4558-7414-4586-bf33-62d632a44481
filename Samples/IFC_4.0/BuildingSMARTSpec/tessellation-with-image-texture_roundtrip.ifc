ISO-10303-21;
HEADER;
FILE_DESCRIPTION(('ViewDefinition [CoordinationView]'),'2;1');
FILE_NAME('','2020-10-30T00:18:59',(),(),'IfcOpenShell 0.6.0b0','IfcOpenShell 0.6.0b0','');
FILE_SCHEMA(('IFC4'));
ENDSEC;
DATA;
#1=IFCPROJECT('2BkIlLW5T7aQj3Uy9BMFxr',$,'Project',$,$,$,$,(#8,#9),#14);
#2=IFCSITE('12Jhas3DnFkfm06Be8GeKW',$,'Site #1',$,$,#295,#298,$,.ELEMENT.,$,$,$,$,$);
#3=IFCMEMBERTYPE('1AgtYeFKDEuRnJ8pGQA3pA',$,$,$,$,$,(#299),$,$,.POST.);
#4=IFCMEMBER('3K_lgFFuT4$xmawQPom25e',$,$,$,$,#302,#310,$,$);
#5=IFCSHAPEREPRESENTATION(#8,'FootPrint','GeometricCurveSet',(#311));
#6=IFCSHAPEREPRESENTATION(#8,'Body','Tessellation',(#318));
#7=IFCSHAPEREPRESENTATION(#8,'Body','MappedRepresentation',(#320));
#8=IFCGEOMETRICREPRESENTATIONCONTEXT('3D','Model',3,1.E-05,#326,$);
#9=IFCGEOMETRICREPRESENTATIONCONTEXT('2D','Plan',2,1.E-05,#328,$);
#10=IFCRELAGGREGATES('2itvH51TLF$vSczv308eND',$,$,$,#1,(#2));
#11=IFCRELDECLARES('1HmT$jWs52sPjBfKd4uec0',$,$,$,#1,(#3));
#12=IFCRELCONTAINEDINSPATIALSTRUCTURE('2H$O_OUzH91QU2yMIvZZ$h',$,$,$,(#4),#2);
#13=IFCRELDEFINESBYTYPE('208LEfhXv418VHoTjAT$dC',$,$,$,(#4),#3);
#14=IFCUNITASSIGNMENT((#15,#16,#17,#18,#19,#20,#21,#22,#23,#24,#25,#26,#27,#28,#29,#30,#31,#32,#33,#34,#35,#36,#39,#40,#41,#42,#43,#44,#45,#46,#53,#60,#65,#72,#77,#82,#87,#92,#95,#100,#105,#110,#117,#122,#127,#132,#137,#142,#147,#150,#155,#166,#171,#176,#181,#184,#189,#192,#197,#206,#209,#212,#215,#218,#221,#228,#233,#238,#245,#252,#255,#262,#269,#274,#281,#286,#289,#294));
#15=IFCSIUNIT(*,.ABSORBEDDOSEUNIT.,$,.GRAY.);
#16=IFCSIUNIT(*,.AMOUNTOFSUBSTANCEUNIT.,$,.MOLE.);
#17=IFCSIUNIT(*,.AREAUNIT.,$,.SQUARE_METRE.);
#18=IFCSIUNIT(*,.DOSEEQUIVALENTUNIT.,$,.SIEVERT.);
#19=IFCSIUNIT(*,.ELECTRICCAPACITANCEUNIT.,$,.FARAD.);
#20=IFCSIUNIT(*,.ELECTRICCHARGEUNIT.,$,.COULOMB.);
#21=IFCSIUNIT(*,.ELECTRICCONDUCTANCEUNIT.,$,.SIEMENS.);
#22=IFCSIUNIT(*,.ELECTRICCURRENTUNIT.,$,.AMPERE.);
#23=IFCSIUNIT(*,.ELECTRICRESISTANCEUNIT.,$,.OHM.);
#24=IFCSIUNIT(*,.ELECTRICVOLTAGEUNIT.,$,.VOLT.);
#25=IFCSIUNIT(*,.ENERGYUNIT.,$,.JOULE.);
#26=IFCSIUNIT(*,.FORCEUNIT.,$,.NEWTON.);
#27=IFCSIUNIT(*,.FREQUENCYUNIT.,$,.HERTZ.);
#28=IFCSIUNIT(*,.ILLUMINANCEUNIT.,$,.LUX.);
#29=IFCSIUNIT(*,.INDUCTANCEUNIT.,$,.HENRY.);
#30=IFCSIUNIT(*,.LENGTHUNIT.,$,.METRE.);
#31=IFCSIUNIT(*,.LUMINOUSFLUXUNIT.,$,.LUMEN.);
#32=IFCSIUNIT(*,.LUMINOUSINTENSITYUNIT.,$,.CANDELA.);
#33=IFCSIUNIT(*,.MAGNETICFLUXDENSITYUNIT.,$,.TESLA.);
#34=IFCSIUNIT(*,.MAGNETICFLUXUNIT.,$,.WEBER.);
#35=IFCSIUNIT(*,.MASSUNIT.,.KILO.,.GRAM.);
#36=IFCCONVERSIONBASEDUNIT($,.PLANEANGLEUNIT.,'degree',#37);
#37=IFCMEASUREWITHUNIT(IFCPLANEANGLEMEASURE(0.0174532925199433),#38);
#38=IFCSIUNIT(*,.PLANEANGLEUNIT.,$,.RADIAN.);
#39=IFCSIUNIT(*,.POWERUNIT.,$,.WATT.);
#40=IFCSIUNIT(*,.PRESSUREUNIT.,$,.PASCAL.);
#41=IFCSIUNIT(*,.RADIOACTIVITYUNIT.,$,.BECQUEREL.);
#42=IFCSIUNIT(*,.SOLIDANGLEUNIT.,$,.STERADIAN.);
#43=IFCSIUNIT(*,.THERMODYNAMICTEMPERATUREUNIT.,$,.DEGREE_CELSIUS.);
#44=IFCSIUNIT(*,.TIMEUNIT.,$,.SECOND.);
#45=IFCSIUNIT(*,.VOLUMEUNIT.,$,.CUBIC_METRE.);
#46=IFCDERIVEDUNIT((#47,#49,#51),.ACCELERATIONUNIT.,$);
#47=IFCDERIVEDUNITELEMENT(#48,1);
#48=IFCSIUNIT(*,.LENGTHUNIT.,$,.METRE.);
#49=IFCDERIVEDUNITELEMENT(#50,-1);
#50=IFCSIUNIT(*,.TIMEUNIT.,$,.SECOND.);
#51=IFCDERIVEDUNITELEMENT(#52,-1);
#52=IFCSIUNIT(*,.TIMEUNIT.,$,.SECOND.);
#53=IFCDERIVEDUNIT((#54,#58),.ANGULARVELOCITYUNIT.,$);
#54=IFCDERIVEDUNITELEMENT(#55,1);
#55=IFCCONVERSIONBASEDUNIT($,.PLANEANGLEUNIT.,'degree',#56);
#56=IFCMEASUREWITHUNIT(IFCPLANEANGLEMEASURE(0.0174532925199433),#57);
#57=IFCSIUNIT(*,.PLANEANGLEUNIT.,$,.RADIAN.);
#58=IFCDERIVEDUNITELEMENT(#59,-1);
#59=IFCSIUNIT(*,.TIMEUNIT.,$,.SECOND.);
#60=IFCDERIVEDUNIT((#61),.COMPOUNDPLANEANGLEUNIT.,$);
#61=IFCDERIVEDUNITELEMENT(#62,1);
#62=IFCCONVERSIONBASEDUNIT($,.PLANEANGLEUNIT.,'degree',#63);
#63=IFCMEASUREWITHUNIT(IFCPLANEANGLEMEASURE(0.0174532925199433),#64);
#64=IFCSIUNIT(*,.PLANEANGLEUNIT.,$,.RADIAN.);
#65=IFCDERIVEDUNIT((#66,#70),.CURVATUREUNIT.,$);
#66=IFCDERIVEDUNITELEMENT(#67,1);
#67=IFCCONVERSIONBASEDUNIT($,.PLANEANGLEUNIT.,'degree',#68);
#68=IFCMEASUREWITHUNIT(IFCPLANEANGLEMEASURE(0.0174532925199433),#69);
#69=IFCSIUNIT(*,.PLANEANGLEUNIT.,$,.RADIAN.);
#70=IFCDERIVEDUNITELEMENT(#71,-1);
#71=IFCSIUNIT(*,.LENGTHUNIT.,$,.METRE.);
#72=IFCDERIVEDUNIT((#73,#75),.DYNAMICVISCOSITYUNIT.,$);
#73=IFCDERIVEDUNITELEMENT(#74,1);
#74=IFCSIUNIT(*,.PRESSUREUNIT.,$,.PASCAL.);
#75=IFCDERIVEDUNITELEMENT(#76,1);
#76=IFCSIUNIT(*,.TIMEUNIT.,$,.SECOND.);
#77=IFCDERIVEDUNIT((#78,#80),.HEATFLUXDENSITYUNIT.,$);
#78=IFCDERIVEDUNITELEMENT(#79,1);
#79=IFCSIUNIT(*,.POWERUNIT.,$,.WATT.);
#80=IFCDERIVEDUNITELEMENT(#81,-1);
#81=IFCSIUNIT(*,.AREAUNIT.,$,.SQUARE_METRE.);
#82=IFCDERIVEDUNIT((#83,#85),.HEATINGVALUEUNIT.,$);
#83=IFCDERIVEDUNITELEMENT(#84,1);
#84=IFCSIUNIT(*,.ENERGYUNIT.,$,.JOULE.);
#85=IFCDERIVEDUNITELEMENT(#86,-1);
#86=IFCSIUNIT(*,.MASSUNIT.,.KILO.,.GRAM.);
#87=IFCDERIVEDUNIT((#88,#90),.IONCONCENTRATIONUNIT.,$);
#88=IFCDERIVEDUNITELEMENT(#89,1);
#89=IFCSIUNIT(*,.MASSUNIT.,$,.GRAM.);
#90=IFCDERIVEDUNITELEMENT(#91,-1);
#91=IFCSIUNIT(*,.VOLUMEUNIT.,$,.CUBIC_METRE.);
#92=IFCDERIVEDUNIT((#93),.INTEGERCOUNTRATEUNIT.,$);
#93=IFCDERIVEDUNITELEMENT(#94,-1);
#94=IFCSIUNIT(*,.TIMEUNIT.,$,.SECOND.);
#95=IFCDERIVEDUNIT((#96,#98),.ISOTHERMALMOISTURECAPACITYUNIT.,$);
#96=IFCDERIVEDUNITELEMENT(#97,1);
#97=IFCSIUNIT(*,.VOLUMEUNIT.,$,.CUBIC_METRE.);
#98=IFCDERIVEDUNITELEMENT(#99,-1);
#99=IFCSIUNIT(*,.ABSORBEDDOSEUNIT.,$,.GRAY.);
#100=IFCDERIVEDUNIT((#101,#103),.KINEMATICVISCOSITYUNIT.,$);
#101=IFCDERIVEDUNITELEMENT(#102,1);
#102=IFCSIUNIT(*,.AREAUNIT.,$,.SQUARE_METRE.);
#103=IFCDERIVEDUNITELEMENT(#104,-1);
#104=IFCSIUNIT(*,.TIMEUNIT.,$,.SECOND.);
#105=IFCDERIVEDUNIT((#106,#108),.LINEARFORCEUNIT.,$);
#106=IFCDERIVEDUNITELEMENT(#107,1);
#107=IFCSIUNIT(*,.FORCEUNIT.,$,.NEWTON.);
#108=IFCDERIVEDUNITELEMENT(#109,-1);
#109=IFCSIUNIT(*,.LENGTHUNIT.,$,.METRE.);
#110=IFCDERIVEDUNIT((#111,#113,#115),.LINEARMOMENTUNIT.,$);
#111=IFCDERIVEDUNITELEMENT(#112,1);
#112=IFCSIUNIT(*,.FORCEUNIT.,$,.NEWTON.);
#113=IFCDERIVEDUNITELEMENT(#114,1);
#114=IFCSIUNIT(*,.LENGTHUNIT.,$,.METRE.);
#115=IFCDERIVEDUNITELEMENT(#116,-1);
#116=IFCSIUNIT(*,.LENGTHUNIT.,$,.METRE.);
#117=IFCDERIVEDUNIT((#118,#120),.LINEARSTIFFNESSUNIT.,$);
#118=IFCDERIVEDUNITELEMENT(#119,1);
#119=IFCSIUNIT(*,.FORCEUNIT.,$,.NEWTON.);
#120=IFCDERIVEDUNITELEMENT(#121,-1);
#121=IFCSIUNIT(*,.LENGTHUNIT.,$,.METRE.);
#122=IFCDERIVEDUNIT((#123,#125),.LINEARVELOCITYUNIT.,$);
#123=IFCDERIVEDUNITELEMENT(#124,1);
#124=IFCSIUNIT(*,.LENGTHUNIT.,$,.METRE.);
#125=IFCDERIVEDUNITELEMENT(#126,-1);
#126=IFCSIUNIT(*,.TIMEUNIT.,$,.SECOND.);
#127=IFCDERIVEDUNIT((#128,#130),.LUMINOUSINTENSITYDISTRIBUTIONUNIT.,$);
#128=IFCDERIVEDUNITELEMENT(#129,1);
#129=IFCSIUNIT(*,.LUMINOUSINTENSITYUNIT.,$,.CANDELA.);
#130=IFCDERIVEDUNITELEMENT(#131,-1);
#131=IFCSIUNIT(*,.LUMINOUSFLUXUNIT.,$,.LUMEN.);
#132=IFCDERIVEDUNIT((#133,#135),.MASSDENSITYUNIT.,$);
#133=IFCDERIVEDUNITELEMENT(#134,1);
#134=IFCSIUNIT(*,.MASSUNIT.,.KILO.,.GRAM.);
#135=IFCDERIVEDUNITELEMENT(#136,-1);
#136=IFCSIUNIT(*,.VOLUMEUNIT.,$,.CUBIC_METRE.);
#137=IFCDERIVEDUNIT((#138,#140),.MASSFLOWRATEUNIT.,$);
#138=IFCDERIVEDUNITELEMENT(#139,1);
#139=IFCSIUNIT(*,.MASSUNIT.,.KILO.,.GRAM.);
#140=IFCDERIVEDUNITELEMENT(#141,-1);
#141=IFCSIUNIT(*,.TIMEUNIT.,$,.SECOND.);
#142=IFCDERIVEDUNIT((#143,#145),.MASSPERLENGTHUNIT.,$);
#143=IFCDERIVEDUNITELEMENT(#144,1);
#144=IFCSIUNIT(*,.MASSUNIT.,.KILO.,.GRAM.);
#145=IFCDERIVEDUNITELEMENT(#146,-1);
#146=IFCSIUNIT(*,.LENGTHUNIT.,$,.METRE.);
#147=IFCDERIVEDUNIT((#148),.MODULUSOFELASTICITYUNIT.,$);
#148=IFCDERIVEDUNITELEMENT(#149,1);
#149=IFCSIUNIT(*,.PRESSUREUNIT.,$,.PASCAL.);
#150=IFCDERIVEDUNIT((#151,#153),.MODULUSOFLINEARSUBGRADEREACTIONUNIT.,$);
#151=IFCDERIVEDUNITELEMENT(#152,1);
#152=IFCSIUNIT(*,.FORCEUNIT.,$,.NEWTON.);
#153=IFCDERIVEDUNITELEMENT(#154,-1);
#154=IFCSIUNIT(*,.AREAUNIT.,$,.SQUARE_METRE.);
#155=IFCDERIVEDUNIT((#156,#158,#160,#162),.MODULUSOFROTATIONALSUBGRADEREACTIONUNIT.,$);
#156=IFCDERIVEDUNITELEMENT(#157,1);
#157=IFCSIUNIT(*,.FORCEUNIT.,$,.NEWTON.);
#158=IFCDERIVEDUNITELEMENT(#159,1);
#159=IFCSIUNIT(*,.LENGTHUNIT.,$,.METRE.);
#160=IFCDERIVEDUNITELEMENT(#161,-1);
#161=IFCSIUNIT(*,.LENGTHUNIT.,$,.METRE.);
#162=IFCDERIVEDUNITELEMENT(#163,-1);
#163=IFCCONVERSIONBASEDUNIT($,.PLANEANGLEUNIT.,'degree',#164);
#164=IFCMEASUREWITHUNIT(IFCPLANEANGLEMEASURE(0.0174532925199433),#165);
#165=IFCSIUNIT(*,.PLANEANGLEUNIT.,$,.RADIAN.);
#166=IFCDERIVEDUNIT((#167,#169),.MODULUSOFSUBGRADEREACTIONUNIT.,$);
#167=IFCDERIVEDUNITELEMENT(#168,1);
#168=IFCSIUNIT(*,.FORCEUNIT.,$,.NEWTON.);
#169=IFCDERIVEDUNITELEMENT(#170,-1);
#170=IFCSIUNIT(*,.VOLUMEUNIT.,$,.CUBIC_METRE.);
#171=IFCDERIVEDUNIT((#172,#174),.MOISTUREDIFFUSIVITYUNIT.,$);
#172=IFCDERIVEDUNITELEMENT(#173,1);
#173=IFCSIUNIT(*,.VOLUMEUNIT.,$,.CUBIC_METRE.);
#174=IFCDERIVEDUNITELEMENT(#175,-1);
#175=IFCSIUNIT(*,.TIMEUNIT.,$,.SECOND.);
#176=IFCDERIVEDUNIT((#177,#179),.MOLECULARWEIGHTUNIT.,$);
#177=IFCDERIVEDUNITELEMENT(#178,1);
#178=IFCSIUNIT(*,.MASSUNIT.,$,.GRAM.);
#179=IFCDERIVEDUNITELEMENT(#180,-1);
#180=IFCSIUNIT(*,.AMOUNTOFSUBSTANCEUNIT.,$,.MOLE.);
#181=IFCDERIVEDUNIT((#182),.MOMENTOFINERTIAUNIT.,$);
#182=IFCDERIVEDUNITELEMENT(#183,4);
#183=IFCSIUNIT(*,.LENGTHUNIT.,$,.METRE.);
#184=IFCDERIVEDUNIT((#185,#187),.PLANARFORCEUNIT.,$);
#185=IFCDERIVEDUNITELEMENT(#186,1);
#186=IFCSIUNIT(*,.FORCEUNIT.,$,.NEWTON.);
#187=IFCDERIVEDUNITELEMENT(#188,-1);
#188=IFCSIUNIT(*,.AREAUNIT.,$,.SQUARE_METRE.);
#189=IFCDERIVEDUNIT((#190),.ROTATIONALFREQUENCYUNIT.,$);
#190=IFCDERIVEDUNITELEMENT(#191,-1);
#191=IFCSIUNIT(*,.TIMEUNIT.,$,.SECOND.);
#192=IFCDERIVEDUNIT((#193,#195),.ROTATIONALMASSUNIT.,$);
#193=IFCDERIVEDUNITELEMENT(#194,1);
#194=IFCSIUNIT(*,.MASSUNIT.,.KILO.,.GRAM.);
#195=IFCDERIVEDUNITELEMENT(#196,-1);
#196=IFCSIUNIT(*,.AREAUNIT.,$,.SQUARE_METRE.);
#197=IFCDERIVEDUNIT((#198,#200,#202),.ROTATIONALSTIFFNESSUNIT.,$);
#198=IFCDERIVEDUNITELEMENT(#199,1);
#199=IFCSIUNIT(*,.FORCEUNIT.,$,.NEWTON.);
#200=IFCDERIVEDUNITELEMENT(#201,1);
#201=IFCSIUNIT(*,.LENGTHUNIT.,$,.METRE.);
#202=IFCDERIVEDUNITELEMENT(#203,-1);
#203=IFCCONVERSIONBASEDUNIT($,.PLANEANGLEUNIT.,'degree',#204);
#204=IFCMEASUREWITHUNIT(IFCPLANEANGLEMEASURE(0.0174532925199433),#205);
#205=IFCSIUNIT(*,.PLANEANGLEUNIT.,$,.RADIAN.);
#206=IFCDERIVEDUNIT((#207),.SECTIONAREAINTEGRALUNIT.,$);
#207=IFCDERIVEDUNITELEMENT(#208,5);
#208=IFCSIUNIT(*,.LENGTHUNIT.,$,.METRE.);
#209=IFCDERIVEDUNIT((#210),.SECTIONMODULUSUNIT.,$);
#210=IFCDERIVEDUNITELEMENT(#211,3);
#211=IFCSIUNIT(*,.LENGTHUNIT.,$,.METRE.);
#212=IFCDERIVEDUNIT((#213),.SHEARMODULUSUNIT.,$);
#213=IFCDERIVEDUNITELEMENT(#214,1);
#214=IFCSIUNIT(*,.PRESSUREUNIT.,$,.PASCAL.);
#215=IFCDERIVEDUNIT((#216),.SOUNDPOWERUNIT.,$);
#216=IFCDERIVEDUNITELEMENT(#217,0);
#217=IFCSIUNIT(*,.POWERUNIT.,.PICO.,.WATT.);
#218=IFCDERIVEDUNIT((#219),.SOUNDPRESSUREUNIT.,$);
#219=IFCDERIVEDUNITELEMENT(#220,0);
#220=IFCSIUNIT(*,.PRESSUREUNIT.,.MICRO.,.PASCAL.);
#221=IFCDERIVEDUNIT((#222,#224,#226),.SPECIFICHEATCAPACITYUNIT.,$);
#222=IFCDERIVEDUNITELEMENT(#223,1);
#223=IFCSIUNIT(*,.ENERGYUNIT.,$,.JOULE.);
#224=IFCDERIVEDUNITELEMENT(#225,-1);
#225=IFCSIUNIT(*,.MASSUNIT.,.KILO.,.GRAM.);
#226=IFCDERIVEDUNITELEMENT(#227,-1);
#227=IFCSIUNIT(*,.THERMODYNAMICTEMPERATUREUNIT.,$,.KELVIN.);
#228=IFCDERIVEDUNIT((#229,#231),.TEMPERATUREGRADIENTUNIT.,$);
#229=IFCDERIVEDUNITELEMENT(#230,1);
#230=IFCSIUNIT(*,.THERMODYNAMICTEMPERATUREUNIT.,$,.KELVIN.);
#231=IFCDERIVEDUNITELEMENT(#232,-1);
#232=IFCSIUNIT(*,.LENGTHUNIT.,$,.METRE.);
#233=IFCDERIVEDUNIT((#234,#236),.TEMPERATURERATEOFCHANGEUNIT.,$);
#234=IFCDERIVEDUNITELEMENT(#235,1);
#235=IFCSIUNIT(*,.THERMODYNAMICTEMPERATUREUNIT.,$,.KELVIN.);
#236=IFCDERIVEDUNITELEMENT(#237,-1);
#237=IFCSIUNIT(*,.TIMEUNIT.,$,.SECOND.);
#238=IFCDERIVEDUNIT((#239,#241,#243),.THERMALADMITTANCEUNIT.,$);
#239=IFCDERIVEDUNITELEMENT(#240,1);
#240=IFCSIUNIT(*,.POWERUNIT.,$,.WATT.);
#241=IFCDERIVEDUNITELEMENT(#242,-1);
#242=IFCSIUNIT(*,.AREAUNIT.,$,.SQUARE_METRE.);
#243=IFCDERIVEDUNITELEMENT(#244,-1);
#244=IFCSIUNIT(*,.THERMODYNAMICTEMPERATUREUNIT.,$,.KELVIN.);
#245=IFCDERIVEDUNIT((#246,#248,#250),.THERMALCONDUCTANCEUNIT.,$);
#246=IFCDERIVEDUNITELEMENT(#247,1);
#247=IFCSIUNIT(*,.POWERUNIT.,$,.WATT.);
#248=IFCDERIVEDUNITELEMENT(#249,-1);
#249=IFCSIUNIT(*,.THERMODYNAMICTEMPERATUREUNIT.,$,.KELVIN.);
#250=IFCDERIVEDUNITELEMENT(#251,-1);
#251=IFCSIUNIT(*,.LENGTHUNIT.,$,.METRE.);
#252=IFCDERIVEDUNIT((#253),.THERMALEXPANSIONCOEFFICIENTUNIT.,$);
#253=IFCDERIVEDUNITELEMENT(#254,-1);
#254=IFCSIUNIT(*,.THERMODYNAMICTEMPERATUREUNIT.,$,.KELVIN.);
#255=IFCDERIVEDUNIT((#256,#258,#260),.THERMALRESISTANCEUNIT.,$);
#256=IFCDERIVEDUNITELEMENT(#257,1);
#257=IFCSIUNIT(*,.AREAUNIT.,$,.SQUARE_METRE.);
#258=IFCDERIVEDUNITELEMENT(#259,1);
#259=IFCSIUNIT(*,.THERMODYNAMICTEMPERATUREUNIT.,$,.KELVIN.);
#260=IFCDERIVEDUNITELEMENT(#261,-1);
#261=IFCSIUNIT(*,.POWERUNIT.,$,.WATT.);
#262=IFCDERIVEDUNIT((#263,#265,#267),.THERMALTRANSMITTANCEUNIT.,$);
#263=IFCDERIVEDUNITELEMENT(#264,1);
#264=IFCSIUNIT(*,.POWERUNIT.,$,.WATT.);
#265=IFCDERIVEDUNITELEMENT(#266,-1);
#266=IFCSIUNIT(*,.AREAUNIT.,$,.SQUARE_METRE.);
#267=IFCDERIVEDUNITELEMENT(#268,1);
#268=IFCSIUNIT(*,.THERMODYNAMICTEMPERATUREUNIT.,$,.KELVIN.);
#269=IFCDERIVEDUNIT((#270,#272),.TORQUEUNIT.,$);
#270=IFCDERIVEDUNITELEMENT(#271,1);
#271=IFCSIUNIT(*,.FORCEUNIT.,$,.NEWTON.);
#272=IFCDERIVEDUNITELEMENT(#273,1);
#273=IFCSIUNIT(*,.LENGTHUNIT.,$,.METRE.);
#274=IFCDERIVEDUNIT((#275,#277,#279),.VAPORPERMEABILITYUNIT.,$);
#275=IFCDERIVEDUNITELEMENT(#276,1);
#276=IFCSIUNIT(*,.MASSUNIT.,.KILO.,.GRAM.);
#277=IFCDERIVEDUNITELEMENT(#278,-1);
#278=IFCSIUNIT(*,.TIMEUNIT.,$,.SECOND.);
#279=IFCDERIVEDUNITELEMENT(#280,-1);
#280=IFCSIUNIT(*,.LENGTHUNIT.,$,.METRE.);
#281=IFCDERIVEDUNIT((#282,#284),.VOLUMETRICFLOWRATEUNIT.,$);
#282=IFCDERIVEDUNITELEMENT(#283,1);
#283=IFCSIUNIT(*,.VOLUMEUNIT.,$,.CUBIC_METRE.);
#284=IFCDERIVEDUNITELEMENT(#285,-1);
#285=IFCSIUNIT(*,.TIMEUNIT.,$,.SECOND.);
#286=IFCDERIVEDUNIT((#287),.WARPINGCONSTANTUNIT.,$);
#287=IFCDERIVEDUNITELEMENT(#288,6);
#288=IFCSIUNIT(*,.LENGTHUNIT.,$,.METRE.);
#289=IFCDERIVEDUNIT((#290,#292),.WARPINGMOMENTUNIT.,$);
#290=IFCDERIVEDUNITELEMENT(#291,1);
#291=IFCSIUNIT(*,.FORCEUNIT.,$,.NEWTON.);
#292=IFCDERIVEDUNITELEMENT(#293,2);
#293=IFCSIUNIT(*,.LENGTHUNIT.,$,.METRE.);
#294=IFCMONETARYUNIT('USD');
#295=IFCLOCALPLACEMENT($,#296);
#296=IFCAXIS2PLACEMENT3D(#297,$,$);
#297=IFCCARTESIANPOINT((0.,0.,0.));
#298=IFCPRODUCTDEFINITIONSHAPE($,$,(#5));
#299=IFCREPRESENTATIONMAP(#300,#6);
#300=IFCAXIS2PLACEMENT3D(#301,$,$);
#301=IFCCARTESIANPOINT((0.,0.,0.));
#302=IFCLOCALPLACEMENT(#303,#306);
#303=IFCLOCALPLACEMENT($,#304);
#304=IFCAXIS2PLACEMENT3D(#305,$,$);
#305=IFCCARTESIANPOINT((0.,0.,0.));
#306=IFCAXIS2PLACEMENT3D(#307,#308,#309);
#307=IFCCARTESIANPOINT((1.92970621585846,1.9296875,0.));
#308=IFCDIRECTION((0.,0.,1.));
#309=IFCDIRECTION((1.,0.,0.));
#310=IFCPRODUCTDEFINITIONSHAPE($,$,(#7));
#311=IFCGEOMETRICCURVESET((#312));
#312=IFCPOLYLINE((#313,#314,#315,#316,#317));
#313=IFCCARTESIANPOINT((0.,0.));
#314=IFCCARTESIANPOINT((40.,0.));
#315=IFCCARTESIANPOINT((40.,20.));
#316=IFCCARTESIANPOINT((0.,20.));
#317=IFCCARTESIANPOINT((0.,0.));
#318=IFCTRIANGULATEDFACESET(#319,((0.,0.,-1.),(0.,0.,1.),(0.,0.,-1.),(0.,0.,-1.),(0.,0.,-1.),(0.,0.,-1.),(0.,0.,-1.),(0.,0.,-1.),(0.,0.,-1.),(0.,0.,-1.),(0.,0.,-1.),(0.,0.,-1.),(0.,0.,-1.),(0.,0.,-1.),(0.,0.,-1.),(0.,0.,-1.),(0.,0.,-1.),(0.,0.,-1.),(0.,0.,-1.),(0.,0.,1.),(0.,0.,1.),(0.,0.,1.),(0.,0.,1.),(0.,0.,1.),(0.,0.,1.),(0.,0.,1.),(0.,0.,1.),(0.,0.,1.),(0.,0.,1.),(0.,0.,1.),(0.,0.,1.),(0.,0.,1.),(0.,0.,1.),(0.,0.,1.),(0.,0.,1.),(0.,0.,1.),(1.,0.,0.),(0.923879532511287,0.38268343236509,0.),(0.707106781186548,0.707106781186547,0.),(0.38268343236509,0.923879532511287,0.),(6.12303176911189E-17,1.,0.),(-0.38268343236509,0.923879532511287,0.),(-0.707106781186547,0.707106781186548,0.),(-0.923879532511287,0.38268343236509,0.),(-1.,1.22460635382238E-16,0.),(-0.923879532511287,-0.38268343236509,0.),(-0.707106781186548,-0.707106781186547,0.),(-0.38268343236509,-0.923879532511287,0.),(-1.83690953073357E-16,-1.,0.),(0.38268343236509,-0.923879532511287,0.),(0.707106781186547,-0.707106781186548,0.),(0.923879532511287,-0.38268343236509,0.),(1.,-2.44921270764475E-16,0.),(1.,0.,0.),(0.923879532511287,0.38268343236509,0.),(0.707106781186548,0.707106781186547,0.),(0.38268343236509,0.923879532511287,0.),(6.12303176911189E-17,1.,0.),(-0.38268343236509,0.923879532511287,0.),(-0.707106781186547,0.707106781186548,0.),(-0.923879532511287,0.38268343236509,0.),(-1.,1.22460635382238E-16,0.),(-0.923879532511287,-0.38268343236509,0.),(-0.707106781186548,-0.707106781186547,0.),(-0.38268343236509,-0.923879532511287,0.),(-1.83690953073357E-16,-1.,0.),(0.38268343236509,-0.923879532511287,0.),(0.707106781186547,-0.707106781186548,0.),(0.923879532511287,-0.38268343236509,0.),(1.,-2.44921270764475E-16,0.)),.T.,((1,4,3),(38,54,37),(38,55,54),(2,20,21),(1,5,4),(39,55,38),(39,56,55),(2,21,22),(1,6,5),(40,56,39),(40,57,56),(2,22,23),(1,7,6),(41,57,40),(41,58,57),(2,23,24),(1,8,7),(42,58,41),(42,59,58),(2,24,25),(1,9,8),(43,59,42),(43,60,59),(2,25,26),(1,10,9),(44,60,43),(44,61,60),(2,26,27),(1,11,10),(45,61,44),(45,62,61),(2,27,28),(1,12,11),(46,62,45),(46,63,62),(2,28,29),(1,13,12),(47,63,46),(47,64,63),(2,29,30),(1,14,13),(48,64,47),(48,65,64),(2,30,31),(1,15,14),(49,65,48),(49,66,65),(2,31,32),(1,16,15),(50,66,49),(50,67,66),(2,32,33),(1,17,16),(51,67,50),(51,68,67),(2,33,34),(1,18,17),(52,68,51),(52,69,68),(2,34,35),(1,19,18),(53,69,52),(53,70,69),(2,35,36)),$);
#319=IFCCARTESIANPOINTLIST3D(((0.,0.,0.),(0.,0.,4.),(2.,0.,0.),(1.84775906502257,0.76536686473018,0.),(1.4142135623731,1.41421356237309,0.),(0.76536686473018,1.84775906502257,0.),(1.22460635382238E-16,2.,0.),(-0.765366864730179,1.84775906502257,0.),(-1.41421356237309,1.4142135623731,0.),(-1.84775906502257,0.76536686473018,0.),(-2.,2.44921270764475E-16,0.),(-1.84775906502257,-0.765366864730179,0.),(-1.4142135623731,-1.41421356237309,0.),(-0.765366864730181,-1.84775906502257,0.),(-3.67381906146713E-16,-2.,0.),(0.76536686473018,-1.84775906502257,0.),(1.41421356237309,-1.4142135623731,0.),(1.84775906502257,-0.765366864730181,0.),(2.,-4.89842541528951E-16,0.),(2.,0.,4.),(1.84775906502257,0.76536686473018,4.),(1.4142135623731,1.41421356237309,4.),(0.76536686473018,1.84775906502257,4.),(1.22460635382238E-16,2.,4.),(-0.765366864730179,1.84775906502257,4.),(-1.41421356237309,1.4142135623731,4.),(-1.84775906502257,0.76536686473018,4.),(-2.,2.44921270764475E-16,4.),(-1.84775906502257,-0.765366864730179,4.),(-1.4142135623731,-1.41421356237309,4.),(-0.765366864730181,-1.84775906502257,4.),(-3.67381906146713E-16,-2.,4.),(0.76536686473018,-1.84775906502257,4.),(1.41421356237309,-1.4142135623731,4.),(1.84775906502257,-0.765366864730181,4.),(2.,-4.89842541528951E-16,4.),(2.,0.,0.),(1.84775906502257,0.76536686473018,0.),(1.4142135623731,1.41421356237309,0.),(0.76536686473018,1.84775906502257,0.),(1.22460635382238E-16,2.,0.),(-0.765366864730179,1.84775906502257,0.),(-1.41421356237309,1.4142135623731,0.),(-1.84775906502257,0.76536686473018,0.),(-2.,2.44921270764475E-16,0.),(-1.84775906502257,-0.765366864730179,0.),(-1.4142135623731,-1.41421356237309,0.),(-0.765366864730181,-1.84775906502257,0.),(-3.67381906146713E-16,-2.,0.),(0.76536686473018,-1.84775906502257,0.),(1.41421356237309,-1.4142135623731,0.),(1.84775906502257,-0.765366864730181,0.),(2.,-4.89842541528951E-16,0.),(2.,0.,4.),(1.84775906502257,0.76536686473018,4.),(1.4142135623731,1.41421356237309,4.),(0.76536686473018,1.84775906502257,4.),(1.22460635382238E-16,2.,4.),(-0.765366864730179,1.84775906502257,4.),(-1.41421356237309,1.4142135623731,4.),(-1.84775906502257,0.76536686473018,4.),(-2.,2.44921270764475E-16,4.),(-1.84775906502257,-0.765366864730179,4.),(-1.4142135623731,-1.41421356237309,4.),(-0.765366864730181,-1.84775906502257,4.),(-3.67381906146713E-16,-2.,4.),(0.76536686473018,-1.84775906502257,4.),(1.41421356237309,-1.4142135623731,4.),(1.84775906502257,-0.765366864730181,4.),(2.,-4.89842541528951E-16,4.)));
#320=IFCMAPPEDITEM(#321,#324);
#321=IFCREPRESENTATIONMAP(#322,#6);
#322=IFCAXIS2PLACEMENT3D(#323,$,$);
#323=IFCCARTESIANPOINT((0.,0.,0.));
#324=IFCCARTESIANTRANSFORMATIONOPERATOR3D($,$,#325,1.,$);
#325=IFCCARTESIANPOINT((0.,0.,0.));
#326=IFCAXIS2PLACEMENT3D(#327,$,$);
#327=IFCCARTESIANPOINT((0.,0.,0.));
#328=IFCAXIS2PLACEMENT3D(#329,$,$);
#329=IFCCARTESIANPOINT((0.,0.,0.));
ENDSEC;
END-ISO-10303-21;

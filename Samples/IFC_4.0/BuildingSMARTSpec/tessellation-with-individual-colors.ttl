# baseURI: http://linkedbuildingdata.net/ifc/resources20200624_184152/
# imports: http://standards.buildingsmart.org/IFC/DEV/IFC4/ADD1/OWL

@prefix ifc:  <http://standards.buildingsmart.org/IFC/DEV/IFC4/ADD1/OWL#> .
@prefix inst:  <http://linkedbuildingdata.net/ifc/resources20200624_184152/> .
@prefix list:  <https://w3id.org/list#> .
@prefix express:  <https://w3id.org/express#> .
@prefix rdf:  <http://www.w3.org/1999/02/22-rdf-syntax-ns#> .
@prefix xsd:  <http://www.w3.org/2001/XMLSchema#> .
@prefix owl:  <http://www.w3.org/2002/07/owl#> .

inst:   rdf:type     owl:Ontology ;
        owl:imports  ifc: .

inst:IfcApplication_1
        rdf:type  ifc:IfcApplication .

inst:IfcOrganization_2
        rdf:type  ifc:IfcOrganization .

inst:IfcApplication_1
        ifc:applicationDeveloper_IfcApplication  inst:IfcOrganization_2 .

inst:IfcLabel_34  rdf:type  ifc:IfcLabel ;
        express:hasString  "1.0.0.0" .

inst:IfcApplication_1
        ifc:version_IfcApplication  inst:IfcLabel_34 .

inst:IfcLabel_35  rdf:type  ifc:IfcLabel ;
        express:hasString  "ggRhinoIFC - Geometry Gym Plug-in for Rhino3d" .

inst:IfcApplication_1
        ifc:applicationFullName_IfcApplication  inst:IfcLabel_35 .

inst:IfcIdentifier_36
        rdf:type           ifc:IfcIdentifier ;
        express:hasString  "ggRhinoIFC" .

inst:IfcApplication_1
        ifc:applicationIdentifier_IfcApplication  inst:IfcIdentifier_36 .

inst:IfcLabel_37  rdf:type  ifc:IfcLabel ;
        express:hasString  "Geometry Gym Pty Ltd" .

inst:IfcOrganization_2
        ifc:name_IfcOrganization  inst:IfcLabel_37 .

inst:IfcPersonAndOrganization_3
        rdf:type  ifc:IfcPersonAndOrganization .

inst:IfcPerson_4  rdf:type  ifc:IfcPerson .

inst:IfcPersonAndOrganization_3
        ifc:thePerson_IfcPersonAndOrganization  inst:IfcPerson_4 ;
        ifc:theOrganization_IfcPersonAndOrganization  inst:IfcOrganization_2 .

inst:IfcIdentifier_38
        rdf:type           ifc:IfcIdentifier ;
        express:hasString  "Jon" .

inst:IfcPerson_4  ifc:identification_IfcPerson  inst:IfcIdentifier_38 ;
        ifc:familyName_IfcPerson      inst:IfcIdentifier_38 .

inst:IfcOwnerHistory_6
        rdf:type                        ifc:IfcOwnerHistory ;
        ifc:owningUser_IfcOwnerHistory  inst:IfcPersonAndOrganization_3 ;
        ifc:owningApplication_IfcOwnerHistory  inst:IfcApplication_1 ;
        ifc:changeAction_IfcOwnerHistory  ifc:ADDED .

inst:IfcTimeStamp_39  rdf:type  ifc:IfcTimeStamp ;
        express:hasInteger  1418084874 .

inst:IfcOwnerHistory_6
        ifc:lastModifiedDate_IfcOwnerHistory  inst:IfcTimeStamp_39 ;
        ifc:creationDate_IfcOwnerHistory  inst:IfcTimeStamp_39 .

inst:IfcGeometricRepresentationContext_7
        rdf:type  ifc:IfcGeometricRepresentationContext .

inst:IfcLabel_40  rdf:type  ifc:IfcLabel ;
        express:hasString  "Model" .

inst:IfcGeometricRepresentationContext_7
        ifc:contextType_IfcRepresentationContext  inst:IfcLabel_40 .

inst:IfcDimensionCount_41
        rdf:type            ifc:IfcDimensionCount ;
        express:hasInteger  3 .

inst:IfcGeometricRepresentationContext_7
        ifc:coordinateSpaceDimension_IfcGeometricRepresentationContext  inst:IfcDimensionCount_41 .

inst:IfcReal_42  rdf:type  ifc:IfcReal ;
        express:hasDouble  "0.0001"^^xsd:double .

inst:IfcGeometricRepresentationContext_7
        ifc:precision_IfcGeometricRepresentationContext  inst:IfcReal_42 .

inst:IfcAxis2Placement3D_8
        rdf:type  ifc:IfcAxis2Placement3D .

inst:IfcGeometricRepresentationContext_7
        ifc:worldCoordinateSystem_IfcGeometricRepresentationContext  inst:IfcAxis2Placement3D_8 .

inst:IfcDirection_10  rdf:type  ifc:IfcDirection .

inst:IfcGeometricRepresentationContext_7
        ifc:trueNorth_IfcGeometricRepresentationContext  inst:IfcDirection_10 .

inst:IfcCartesianPoint_9
        rdf:type  ifc:IfcCartesianPoint .

inst:IfcAxis2Placement3D_8
        ifc:location_IfcPlacement  inst:IfcCartesianPoint_9 .

inst:IfcCartesianPointList3D_200
        rdf:type  ifc:IfcCartesianPointList3D .

inst:IfcLengthMeasure_List_43
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_44
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_45
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_46
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "0.0"^^xsd:double .

inst:IfcLengthMeasure_List_43
        list:hasContents  inst:IfcLengthMeasure_46 ;
        list:hasNext      inst:IfcLengthMeasure_List_44 .

inst:IfcLengthMeasure_List_44
        list:hasContents  inst:IfcLengthMeasure_46 ;
        list:hasNext      inst:IfcLengthMeasure_List_45 .

inst:IfcLengthMeasure_List_45
        list:hasContents  inst:IfcLengthMeasure_46 .

inst:IfcLengthMeasure_List_47
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_48
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_49
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_50
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "1000.0"^^xsd:double .

inst:IfcLengthMeasure_List_47
        list:hasContents  inst:IfcLengthMeasure_50 ;
        list:hasNext      inst:IfcLengthMeasure_List_48 .

inst:IfcLengthMeasure_List_48
        list:hasContents  inst:IfcLengthMeasure_46 ;
        list:hasNext      inst:IfcLengthMeasure_List_49 .

inst:IfcLengthMeasure_List_49
        list:hasContents  inst:IfcLengthMeasure_46 .

inst:IfcLengthMeasure_List_51
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_52
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_53
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_51
        list:hasContents  inst:IfcLengthMeasure_50 ;
        list:hasNext      inst:IfcLengthMeasure_List_52 .

inst:IfcLengthMeasure_List_52
        list:hasContents  inst:IfcLengthMeasure_50 ;
        list:hasNext      inst:IfcLengthMeasure_List_53 .

inst:IfcLengthMeasure_List_53
        list:hasContents  inst:IfcLengthMeasure_46 .

inst:IfcLengthMeasure_List_54
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_55
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_56
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_54
        list:hasContents  inst:IfcLengthMeasure_46 ;
        list:hasNext      inst:IfcLengthMeasure_List_55 .

inst:IfcLengthMeasure_List_55
        list:hasContents  inst:IfcLengthMeasure_50 ;
        list:hasNext      inst:IfcLengthMeasure_List_56 .

inst:IfcLengthMeasure_List_56
        list:hasContents  inst:IfcLengthMeasure_46 .

inst:IfcLengthMeasure_List_57
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_58
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_59
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_57
        list:hasContents  inst:IfcLengthMeasure_46 ;
        list:hasNext      inst:IfcLengthMeasure_List_58 .

inst:IfcLengthMeasure_List_58
        list:hasContents  inst:IfcLengthMeasure_46 ;
        list:hasNext      inst:IfcLengthMeasure_List_59 .

inst:IfcLengthMeasure_60
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "2000.0"^^xsd:double .

inst:IfcLengthMeasure_List_59
        list:hasContents  inst:IfcLengthMeasure_60 .

inst:IfcLengthMeasure_List_61
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_62
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_63
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_61
        list:hasContents  inst:IfcLengthMeasure_50 ;
        list:hasNext      inst:IfcLengthMeasure_List_62 .

inst:IfcLengthMeasure_List_62
        list:hasContents  inst:IfcLengthMeasure_46 ;
        list:hasNext      inst:IfcLengthMeasure_List_63 .

inst:IfcLengthMeasure_List_63
        list:hasContents  inst:IfcLengthMeasure_60 .

inst:IfcLengthMeasure_List_64
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_65
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_66
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_64
        list:hasContents  inst:IfcLengthMeasure_50 ;
        list:hasNext      inst:IfcLengthMeasure_List_65 .

inst:IfcLengthMeasure_67
        rdf:type           ifc:IfcLengthMeasure ;
        express:hasDouble  "1.0"^^xsd:double .

inst:IfcLengthMeasure_List_65
        list:hasContents  inst:IfcLengthMeasure_67 ;
        list:hasNext      inst:IfcLengthMeasure_List_66 .

inst:IfcLengthMeasure_List_66
        list:hasContents  inst:IfcLengthMeasure_60 .

inst:IfcLengthMeasure_List_68
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_69
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_70
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_68
        list:hasContents  inst:IfcLengthMeasure_46 ;
        list:hasNext      inst:IfcLengthMeasure_List_69 .

inst:IfcLengthMeasure_List_69
        list:hasContents  inst:IfcLengthMeasure_50 ;
        list:hasNext      inst:IfcLengthMeasure_List_70 .

inst:IfcLengthMeasure_List_70
        list:hasContents  inst:IfcLengthMeasure_60 .

inst:IfcLengthMeasure_List_List_71
        rdf:type  ifc:IfcLengthMeasure_List_List .

inst:IfcCartesianPointList3D_200
        ifc:coordList_IfcCartesianPointList3D  inst:IfcLengthMeasure_List_List_71 .

inst:IfcLengthMeasure_List_List_71
        list:hasContents  inst:IfcLengthMeasure_List_43 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_72 .

inst:IfcLengthMeasure_List_List_72
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_47 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_73 .

inst:IfcLengthMeasure_List_List_73
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_51 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_74 .

inst:IfcLengthMeasure_List_List_74
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_54 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_75 .

inst:IfcLengthMeasure_List_List_75
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_57 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_76 .

inst:IfcLengthMeasure_List_List_76
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_61 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_77 .

inst:IfcLengthMeasure_List_List_77
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_64 ;
        list:hasNext      inst:IfcLengthMeasure_List_List_78 .

inst:IfcLengthMeasure_List_List_78
        rdf:type          ifc:IfcLengthMeasure_List_List ;
        list:hasContents  inst:IfcLengthMeasure_List_68 .

inst:IfcLengthMeasure_List_79
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcCartesianPoint_9
        ifc:coordinates_IfcCartesianPoint  inst:IfcLengthMeasure_List_79 .

inst:IfcLengthMeasure_List_80
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_81
        rdf:type  ifc:IfcLengthMeasure_List .

inst:IfcLengthMeasure_List_79
        list:hasContents  inst:IfcLengthMeasure_46 ;
        list:hasNext      inst:IfcLengthMeasure_List_80 .

inst:IfcLengthMeasure_List_80
        list:hasContents  inst:IfcLengthMeasure_46 ;
        list:hasNext      inst:IfcLengthMeasure_List_81 .

inst:IfcLengthMeasure_List_81
        list:hasContents  inst:IfcLengthMeasure_46 .

inst:IfcTriangulatedFaceSet_201
        rdf:type  ifc:IfcTriangulatedFaceSet ;
        ifc:coordinates_IfcTessellatedFaceSet  inst:IfcCartesianPointList3D_200 .

inst:IfcBoolean_82  rdf:type  ifc:IfcBoolean ;
        express:hasBoolean  true .

inst:IfcTriangulatedFaceSet_201
        ifc:closed_IfcTessellatedFaceSet  inst:IfcBoolean_82 .

inst:IfcPositiveInteger_List_83
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_84
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_85
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_86
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  1 .

inst:IfcPositiveInteger_List_83
        list:hasContents  inst:IfcPositiveInteger_86 ;
        list:hasNext      inst:IfcPositiveInteger_List_84 .

inst:IfcPositiveInteger_87
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  6 .

inst:IfcPositiveInteger_List_84
        list:hasContents  inst:IfcPositiveInteger_87 ;
        list:hasNext      inst:IfcPositiveInteger_List_85 .

inst:IfcPositiveInteger_88
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  5 .

inst:IfcPositiveInteger_List_85
        list:hasContents  inst:IfcPositiveInteger_88 .

inst:IfcPositiveInteger_List_89
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_90
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_91
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_89
        list:hasContents  inst:IfcPositiveInteger_86 ;
        list:hasNext      inst:IfcPositiveInteger_List_90 .

inst:IfcPositiveInteger_92
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  2 .

inst:IfcPositiveInteger_List_90
        list:hasContents  inst:IfcPositiveInteger_92 ;
        list:hasNext      inst:IfcPositiveInteger_List_91 .

inst:IfcPositiveInteger_List_91
        list:hasContents  inst:IfcPositiveInteger_87 .

inst:IfcPositiveInteger_List_93
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_94
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_95
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_93
        list:hasContents  inst:IfcPositiveInteger_87 ;
        list:hasNext      inst:IfcPositiveInteger_List_94 .

inst:IfcPositiveInteger_List_94
        list:hasContents  inst:IfcPositiveInteger_92 ;
        list:hasNext      inst:IfcPositiveInteger_List_95 .

inst:IfcPositiveInteger_96
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  7 .

inst:IfcPositiveInteger_List_95
        list:hasContents  inst:IfcPositiveInteger_96 .

inst:IfcPositiveInteger_List_97
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_98
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_99
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_97
        list:hasContents  inst:IfcPositiveInteger_96 ;
        list:hasNext      inst:IfcPositiveInteger_List_98 .

inst:IfcPositiveInteger_List_98
        list:hasContents  inst:IfcPositiveInteger_92 ;
        list:hasNext      inst:IfcPositiveInteger_List_99 .

inst:IfcPositiveInteger_List_99
        list:hasContents  inst:IfcDimensionCount_41 .

inst:IfcPositiveInteger_List_100
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_101
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_102
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_100
        list:hasContents  inst:IfcPositiveInteger_96 ;
        list:hasNext      inst:IfcPositiveInteger_List_101 .

inst:IfcPositiveInteger_103
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  8 .

inst:IfcPositiveInteger_List_101
        list:hasContents  inst:IfcPositiveInteger_103 ;
        list:hasNext      inst:IfcPositiveInteger_List_102 .

inst:IfcPositiveInteger_List_102
        list:hasContents  inst:IfcPositiveInteger_87 .

inst:IfcPositiveInteger_List_104
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_105
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_106
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_104
        list:hasContents  inst:IfcPositiveInteger_87 ;
        list:hasNext      inst:IfcPositiveInteger_List_105 .

inst:IfcPositiveInteger_List_105
        list:hasContents  inst:IfcPositiveInteger_103 ;
        list:hasNext      inst:IfcPositiveInteger_List_106 .

inst:IfcPositiveInteger_List_106
        list:hasContents  inst:IfcPositiveInteger_88 .

inst:IfcPositiveInteger_List_107
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_108
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_109
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_107
        list:hasContents  inst:IfcPositiveInteger_88 ;
        list:hasNext      inst:IfcPositiveInteger_List_108 .

inst:IfcPositiveInteger_List_108
        list:hasContents  inst:IfcPositiveInteger_103 ;
        list:hasNext      inst:IfcPositiveInteger_List_109 .

inst:IfcPositiveInteger_List_109
        list:hasContents  inst:IfcPositiveInteger_86 .

inst:IfcPositiveInteger_List_110
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_111
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_112
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_110
        list:hasContents  inst:IfcPositiveInteger_86 ;
        list:hasNext      inst:IfcPositiveInteger_List_111 .

inst:IfcPositiveInteger_List_111
        list:hasContents  inst:IfcPositiveInteger_103 ;
        list:hasNext      inst:IfcPositiveInteger_List_112 .

inst:IfcPositiveInteger_113
        rdf:type            ifc:IfcPositiveInteger ;
        express:hasInteger  4 .

inst:IfcPositiveInteger_List_112
        list:hasContents  inst:IfcPositiveInteger_113 .

inst:IfcPositiveInteger_List_114
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_115
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_116
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_114
        list:hasContents  inst:IfcPositiveInteger_113 ;
        list:hasNext      inst:IfcPositiveInteger_List_115 .

inst:IfcPositiveInteger_List_115
        list:hasContents  inst:IfcPositiveInteger_92 ;
        list:hasNext      inst:IfcPositiveInteger_List_116 .

inst:IfcPositiveInteger_List_116
        list:hasContents  inst:IfcPositiveInteger_86 .

inst:IfcPositiveInteger_List_117
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_118
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_119
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_117
        list:hasContents  inst:IfcPositiveInteger_92 ;
        list:hasNext      inst:IfcPositiveInteger_List_118 .

inst:IfcPositiveInteger_List_118
        list:hasContents  inst:IfcPositiveInteger_113 ;
        list:hasNext      inst:IfcPositiveInteger_List_119 .

inst:IfcPositiveInteger_List_119
        list:hasContents  inst:IfcDimensionCount_41 .

inst:IfcPositiveInteger_List_120
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_121
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_122
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_120
        list:hasContents  inst:IfcPositiveInteger_113 ;
        list:hasNext      inst:IfcPositiveInteger_List_121 .

inst:IfcPositiveInteger_List_121
        list:hasContents  inst:IfcPositiveInteger_103 ;
        list:hasNext      inst:IfcPositiveInteger_List_122 .

inst:IfcPositiveInteger_List_122
        list:hasContents  inst:IfcPositiveInteger_96 .

inst:IfcPositiveInteger_List_123
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_124
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_125
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_123
        list:hasContents  inst:IfcPositiveInteger_96 ;
        list:hasNext      inst:IfcPositiveInteger_List_124 .

inst:IfcPositiveInteger_List_124
        list:hasContents  inst:IfcDimensionCount_41 ;
        list:hasNext      inst:IfcPositiveInteger_List_125 .

inst:IfcPositiveInteger_List_125
        list:hasContents  inst:IfcPositiveInteger_113 .

inst:IfcPositiveInteger_List_List_126
        rdf:type  ifc:IfcPositiveInteger_List_List .

inst:IfcTriangulatedFaceSet_201
        ifc:coordIndex_IfcTriangulatedFaceSet  inst:IfcPositiveInteger_List_List_126 .

inst:IfcPositiveInteger_List_List_126
        list:hasContents  inst:IfcPositiveInteger_List_83 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_127 .

inst:IfcPositiveInteger_List_List_127
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_89 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_128 .

inst:IfcPositiveInteger_List_List_128
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_93 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_129 .

inst:IfcPositiveInteger_List_List_129
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_97 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_130 .

inst:IfcPositiveInteger_List_List_130
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_100 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_131 .

inst:IfcPositiveInteger_List_List_131
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_104 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_132 .

inst:IfcPositiveInteger_List_List_132
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_107 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_133 .

inst:IfcPositiveInteger_List_List_133
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_110 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_134 .

inst:IfcPositiveInteger_List_List_134
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_114 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_135 .

inst:IfcPositiveInteger_List_List_135
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_117 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_136 .

inst:IfcPositiveInteger_List_List_136
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_120 ;
        list:hasNext      inst:IfcPositiveInteger_List_List_137 .

inst:IfcPositiveInteger_List_List_137
        rdf:type          ifc:IfcPositiveInteger_List_List ;
        list:hasContents  inst:IfcPositiveInteger_List_123 .

inst:IfcReal_List_138
        rdf:type  ifc:IfcReal_List .

inst:IfcDirection_10  ifc:directionRatios_IfcDirection  inst:IfcReal_List_138 .

inst:IfcReal_List_139
        rdf:type  ifc:IfcReal_List .

inst:IfcReal_List_138
        list:hasContents  inst:IfcLengthMeasure_46 ;
        list:hasNext      inst:IfcReal_List_139 .

inst:IfcReal_List_139
        list:hasContents  inst:IfcLengthMeasure_67 .

inst:IfcColourRgbList_202
        rdf:type  ifc:IfcColourRgbList .

inst:IfcNormalisedRatioMeasure_List_140
        rdf:type  ifc:IfcNormalisedRatioMeasure_List .

inst:IfcNormalisedRatioMeasure_List_141
        rdf:type  ifc:IfcNormalisedRatioMeasure_List .

inst:IfcNormalisedRatioMeasure_List_142
        rdf:type  ifc:IfcNormalisedRatioMeasure_List .

inst:IfcNormalisedRatioMeasure_143
        rdf:type           ifc:IfcNormalisedRatioMeasure ;
        express:hasDouble  "255.0"^^xsd:double .

inst:IfcNormalisedRatioMeasure_List_140
        list:hasContents  inst:IfcNormalisedRatioMeasure_143 ;
        list:hasNext      inst:IfcNormalisedRatioMeasure_List_141 .

inst:IfcNormalisedRatioMeasure_List_141
        list:hasContents  inst:IfcLengthMeasure_46 ;
        list:hasNext      inst:IfcNormalisedRatioMeasure_List_142 .

inst:IfcNormalisedRatioMeasure_List_142
        list:hasContents  inst:IfcLengthMeasure_46 .

inst:IfcNormalisedRatioMeasure_List_144
        rdf:type  ifc:IfcNormalisedRatioMeasure_List .

inst:IfcNormalisedRatioMeasure_List_145
        rdf:type  ifc:IfcNormalisedRatioMeasure_List .

inst:IfcNormalisedRatioMeasure_List_146
        rdf:type  ifc:IfcNormalisedRatioMeasure_List .

inst:IfcNormalisedRatioMeasure_List_144
        list:hasContents  inst:IfcLengthMeasure_46 ;
        list:hasNext      inst:IfcNormalisedRatioMeasure_List_145 .

inst:IfcNormalisedRatioMeasure_147
        rdf:type           ifc:IfcNormalisedRatioMeasure ;
        express:hasDouble  "128.0"^^xsd:double .

inst:IfcNormalisedRatioMeasure_List_145
        list:hasContents  inst:IfcNormalisedRatioMeasure_147 ;
        list:hasNext      inst:IfcNormalisedRatioMeasure_List_146 .

inst:IfcNormalisedRatioMeasure_List_146
        list:hasContents  inst:IfcLengthMeasure_46 .

inst:IfcNormalisedRatioMeasure_List_148
        rdf:type  ifc:IfcNormalisedRatioMeasure_List .

inst:IfcNormalisedRatioMeasure_List_149
        rdf:type  ifc:IfcNormalisedRatioMeasure_List .

inst:IfcNormalisedRatioMeasure_List_150
        rdf:type  ifc:IfcNormalisedRatioMeasure_List .

inst:IfcNormalisedRatioMeasure_List_148
        list:hasContents  inst:IfcNormalisedRatioMeasure_143 ;
        list:hasNext      inst:IfcNormalisedRatioMeasure_List_149 .

inst:IfcNormalisedRatioMeasure_List_149
        list:hasContents  inst:IfcNormalisedRatioMeasure_143 ;
        list:hasNext      inst:IfcNormalisedRatioMeasure_List_150 .

inst:IfcNormalisedRatioMeasure_List_150
        list:hasContents  inst:IfcLengthMeasure_46 .

inst:IfcNormalisedRatioMeasure_List_List_151
        rdf:type  ifc:IfcNormalisedRatioMeasure_List_List .

inst:IfcColourRgbList_202
        ifc:colourList_IfcColourRgbList  inst:IfcNormalisedRatioMeasure_List_List_151 .

inst:IfcNormalisedRatioMeasure_List_List_151
        list:hasContents  inst:IfcNormalisedRatioMeasure_List_140 ;
        list:hasNext      inst:IfcNormalisedRatioMeasure_List_List_152 .

inst:IfcNormalisedRatioMeasure_List_List_152
        rdf:type          ifc:IfcNormalisedRatioMeasure_List_List ;
        list:hasContents  inst:IfcNormalisedRatioMeasure_List_144 ;
        list:hasNext      inst:IfcNormalisedRatioMeasure_List_List_153 .

inst:IfcNormalisedRatioMeasure_List_List_153
        rdf:type          ifc:IfcNormalisedRatioMeasure_List_List ;
        list:hasContents  inst:IfcNormalisedRatioMeasure_List_148 .

inst:IfcGeometricRepresentationSubContext_11
        rdf:type  ifc:IfcGeometricRepresentationSubContext .

inst:IfcLabel_154  rdf:type  ifc:IfcLabel ;
        express:hasString  "Axis" .

inst:IfcGeometricRepresentationSubContext_11
        ifc:contextIdentifier_IfcRepresentationContext  inst:IfcLabel_154 ;
        ifc:contextType_IfcRepresentationContext  inst:IfcLabel_40 ;
        ifc:parentContext_IfcGeometricRepresentationSubContext  inst:IfcGeometricRepresentationContext_7 ;
        ifc:targetView_IfcGeometricRepresentationSubContext  ifc:MODEL_VIEW .

inst:IfcIndexedColourMap_203
        rdf:type  ifc:IfcIndexedColourMap ;
        ifc:mappedTo_IfcIndexedColourMap  inst:IfcTriangulatedFaceSet_201 ;
        ifc:colours_IfcIndexedColourMap  inst:IfcColourRgbList_202 .

inst:IfcPositiveInteger_List_155
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcIndexedColourMap_203
        ifc:colourIndex_IfcIndexedColourMap  inst:IfcPositiveInteger_List_155 .

inst:IfcPositiveInteger_List_156
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_157
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_158
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_159
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_160
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_161
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_162
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_163
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_164
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_165
        rdf:type  ifc:IfcPositiveInteger_List .

inst:IfcPositiveInteger_List_155
        list:hasContents  inst:IfcPositiveInteger_86 ;
        list:hasNext      inst:IfcPositiveInteger_List_156 .

inst:IfcPositiveInteger_List_156
        list:hasContents  inst:IfcPositiveInteger_86 ;
        list:hasNext      inst:IfcPositiveInteger_List_157 .

inst:IfcPositiveInteger_List_157
        list:hasContents  inst:IfcPositiveInteger_92 ;
        list:hasNext      inst:IfcPositiveInteger_List_158 .

inst:IfcPositiveInteger_List_158
        list:hasContents  inst:IfcPositiveInteger_92 ;
        list:hasNext      inst:IfcPositiveInteger_List_159 .

inst:IfcPositiveInteger_List_159
        list:hasContents  inst:IfcDimensionCount_41 ;
        list:hasNext      inst:IfcPositiveInteger_List_160 .

inst:IfcPositiveInteger_List_160
        list:hasContents  inst:IfcDimensionCount_41 ;
        list:hasNext      inst:IfcPositiveInteger_List_161 .

inst:IfcPositiveInteger_List_161
        list:hasContents  inst:IfcPositiveInteger_86 ;
        list:hasNext      inst:IfcPositiveInteger_List_162 .

inst:IfcPositiveInteger_List_162
        list:hasContents  inst:IfcPositiveInteger_86 ;
        list:hasNext      inst:IfcPositiveInteger_List_163 .

inst:IfcPositiveInteger_List_163
        list:hasContents  inst:IfcPositiveInteger_86 ;
        list:hasNext      inst:IfcPositiveInteger_List_164 .

inst:IfcPositiveInteger_List_164
        list:hasContents  inst:IfcPositiveInteger_86 ;
        list:hasNext      inst:IfcPositiveInteger_List_165 .

inst:IfcPositiveInteger_List_165
        list:hasContents  inst:IfcPositiveInteger_86 .

inst:IfcGeometricRepresentationSubContext_12
        rdf:type  ifc:IfcGeometricRepresentationSubContext .

inst:IfcLabel_166  rdf:type  ifc:IfcLabel ;
        express:hasString  "Body" .

inst:IfcGeometricRepresentationSubContext_12
        ifc:contextIdentifier_IfcRepresentationContext  inst:IfcLabel_166 ;
        ifc:contextType_IfcRepresentationContext  inst:IfcLabel_40 ;
        ifc:parentContext_IfcGeometricRepresentationSubContext  inst:IfcGeometricRepresentationContext_7 ;
        ifc:targetView_IfcGeometricRepresentationSubContext  ifc:MODEL_VIEW .

inst:IfcGeometricRepresentationContext_13
        rdf:type  ifc:IfcGeometricRepresentationContext ;
        ifc:contextType_IfcRepresentationContext  inst:IfcLabel_40 ;
        ifc:coordinateSpaceDimension_IfcGeometricRepresentationContext  inst:IfcDimensionCount_41 ;
        ifc:precision_IfcGeometricRepresentationContext  inst:IfcReal_42 ;
        ifc:worldCoordinateSystem_IfcGeometricRepresentationContext  inst:IfcAxis2Placement3D_8 ;
        ifc:trueNorth_IfcGeometricRepresentationContext  inst:IfcDirection_10 .

inst:IfcProject_100  rdf:type  ifc:IfcProject .

inst:IfcGloballyUniqueId_167
        rdf:type           ifc:IfcGloballyUniqueId ;
        express:hasString  "2yXUajt9D3DwMqV1WYGofM" .

inst:IfcProject_100  ifc:globalId_IfcRoot  inst:IfcGloballyUniqueId_167 ;
        ifc:ownerHistory_IfcRoot  inst:IfcOwnerHistory_6 .

inst:IfcLabel_168  rdf:type  ifc:IfcLabel ;
        express:hasString  "IfcProject" .

inst:IfcProject_100  ifc:name_IfcRoot  inst:IfcLabel_168 ;
        ifc:longName_IfcContext  inst:IfcLabel_168 .

inst:IfcLabel_169  rdf:type  ifc:IfcLabel ;
        express:hasString  "" .

inst:IfcProject_100  ifc:phase_IfcContext  inst:IfcLabel_169 ;
        ifc:representationContexts_IfcContext  inst:IfcGeometricRepresentationContext_13 .

inst:IfcUnitAssignment_101
        rdf:type  ifc:IfcUnitAssignment .

inst:IfcProject_100  ifc:unitsInContext_IfcContext  inst:IfcUnitAssignment_101 .

inst:IfcSIUnit_102  rdf:type  ifc:IfcSIUnit .

inst:IfcUnitAssignment_101
        ifc:units_IfcUnitAssignment  inst:IfcSIUnit_102 .

inst:IfcSIUnit_103  rdf:type  ifc:IfcSIUnit .

inst:IfcUnitAssignment_101
        ifc:units_IfcUnitAssignment  inst:IfcSIUnit_103 .

inst:IfcSIUnit_104  rdf:type  ifc:IfcSIUnit .

inst:IfcUnitAssignment_101
        ifc:units_IfcUnitAssignment  inst:IfcSIUnit_104 .

inst:IfcSIUnit_102  ifc:unitType_IfcNamedUnit  ifc:LENGTHUNIT ;
        ifc:prefix_IfcSIUnit       ifc:MILLI ;
        ifc:name_IfcSIUnit         ifc:METRE .

inst:IfcSIUnit_103  ifc:unitType_IfcNamedUnit  ifc:PLANEANGLEUNIT ;
        ifc:name_IfcSIUnit         ifc:RADIAN .

inst:IfcSIUnit_104  ifc:unitType_IfcNamedUnit  ifc:TIMEUNIT ;
        ifc:name_IfcSIUnit         ifc:SECOND .

inst:IfcRelAggregates_105
        rdf:type  ifc:IfcRelAggregates .

inst:IfcGloballyUniqueId_170
        rdf:type           ifc:IfcGloballyUniqueId ;
        express:hasString  "0ZHOqoKDzA4Ble9lcTcCu7" .

inst:IfcRelAggregates_105
        ifc:globalId_IfcRoot  inst:IfcGloballyUniqueId_170 .

inst:IfcLabel_171  rdf:type  ifc:IfcLabel ;
        express:hasString  "Project Container" .

inst:IfcRelAggregates_105
        ifc:name_IfcRoot  inst:IfcLabel_171 .

inst:IfcText_172  rdf:type  ifc:IfcText ;
        express:hasString  "Project Container for Buildings" .

inst:IfcRelAggregates_105
        ifc:description_IfcRoot  inst:IfcText_172 ;
        ifc:relatingObject_IfcRelAggregates  inst:IfcProject_100 .

inst:IfcBuilding_50  rdf:type  ifc:IfcBuilding .

inst:IfcRelAggregates_105
        ifc:relatedObjects_IfcRelAggregates  inst:IfcBuilding_50 .

inst:IfcBuildingElementProxy_300
        rdf:type  ifc:IfcBuildingElementProxy .

inst:IfcGloballyUniqueId_173
        rdf:type           ifc:IfcGloballyUniqueId ;
        express:hasString  "3VRU6T9_18bPNzxvGkzDIj" .

inst:IfcBuildingElementProxy_300
        ifc:globalId_IfcRoot  inst:IfcGloballyUniqueId_173 .

inst:IfcLabel_174  rdf:type  ifc:IfcLabel ;
        express:hasString  "BuildingElementProxy" .

inst:IfcBuildingElementProxy_300
        ifc:name_IfcRoot  inst:IfcLabel_174 .

inst:IfcLocalPlacement_55
        rdf:type  ifc:IfcLocalPlacement .

inst:IfcBuildingElementProxy_300
        ifc:objectPlacement_IfcProduct  inst:IfcLocalPlacement_55 .

inst:IfcProductDefinitionShape_301
        rdf:type  ifc:IfcProductDefinitionShape .

inst:IfcBuildingElementProxy_300
        ifc:representation_IfcProduct  inst:IfcProductDefinitionShape_301 ;
        ifc:predefinedType_IfcBuildingElementProxy  ifc:NOTDEFINED .

inst:IfcRepresentation_List_175
        rdf:type  ifc:IfcRepresentation_List .

inst:IfcProductDefinitionShape_301
        ifc:representations_IfcProductRepresentation  inst:IfcRepresentation_List_175 .

inst:IfcShapeRepresentation_302
        rdf:type  ifc:IfcShapeRepresentation .

inst:IfcRepresentation_List_175
        list:hasContents  inst:IfcShapeRepresentation_302 .

inst:IfcShapeRepresentation_302
        ifc:contextOfItems_IfcRepresentation  inst:IfcGeometricRepresentationSubContext_12 ;
        ifc:representationIdentifier_IfcRepresentation  inst:IfcLabel_166 .

inst:IfcLabel_176  rdf:type  ifc:IfcLabel ;
        express:hasString  "Tessellation" .

inst:IfcShapeRepresentation_302
        ifc:representationType_IfcRepresentation  inst:IfcLabel_176 ;
        ifc:items_IfcRepresentation  inst:IfcTriangulatedFaceSet_201 .

inst:IfcGloballyUniqueId_177
        rdf:type           ifc:IfcGloballyUniqueId ;
        express:hasString  "0AcaWYX890yeVBaPRCfpNi" .

inst:IfcBuilding_50  ifc:globalId_IfcRoot  inst:IfcGloballyUniqueId_177 .

inst:IfcLabel_178  rdf:type  ifc:IfcLabel ;
        express:hasString  "IfcBuilding" .

inst:IfcBuilding_50  ifc:name_IfcRoot  inst:IfcLabel_178 .

inst:IfcLocalPlacement_51
        rdf:type  ifc:IfcLocalPlacement .

inst:IfcBuilding_50  ifc:objectPlacement_IfcProduct  inst:IfcLocalPlacement_51 ;
        ifc:compositionType_IfcSpatialStructureElement  ifc:ELEMENT .

inst:IfcPostalAddress_57
        rdf:type  ifc:IfcPostalAddress .

inst:IfcBuilding_50  ifc:buildingAddress_IfcBuilding  inst:IfcPostalAddress_57 .

inst:IfcAxis2Placement3D_52
        rdf:type  ifc:IfcAxis2Placement3D .

inst:IfcLocalPlacement_51
        ifc:relativePlacement_IfcLocalPlacement  inst:IfcAxis2Placement3D_52 .

inst:IfcAxis2Placement3D_52
        ifc:location_IfcPlacement  inst:IfcCartesianPoint_9 .

inst:IfcRelContainedInSpatialStructure_54
        rdf:type  ifc:IfcRelContainedInSpatialStructure .

inst:IfcGloballyUniqueId_179
        rdf:type           ifc:IfcGloballyUniqueId ;
        express:hasString  "16VvjHwgv3ThcRsOElhbPR" .

inst:IfcRelContainedInSpatialStructure_54
        ifc:globalId_IfcRoot  inst:IfcGloballyUniqueId_179 .

inst:IfcLabel_180  rdf:type  ifc:IfcLabel ;
        express:hasString  "Building" .

inst:IfcRelContainedInSpatialStructure_54
        ifc:name_IfcRoot  inst:IfcLabel_180 .

inst:IfcText_181  rdf:type  ifc:IfcText ;
        express:hasString  "Building Container for Elements" .

inst:IfcRelContainedInSpatialStructure_54
        ifc:description_IfcRoot  inst:IfcText_181 ;
        ifc:relatedElements_IfcRelContainedInSpatialStructure  inst:IfcBuildingElementProxy_300 ;
        ifc:relatingStructure_IfcRelContainedInSpatialStructure  inst:IfcBuilding_50 .

inst:IfcLocalPlacement_55
        ifc:placementRelTo_IfcLocalPlacement  inst:IfcLocalPlacement_51 ;
        ifc:relativePlacement_IfcLocalPlacement  inst:IfcAxis2Placement3D_52 .

inst:IfcLabel_182  rdf:type  ifc:IfcLabel ;
        express:hasString  "Unknown" .

inst:IfcPostalAddress_57
        ifc:region_IfcPostalAddress  inst:IfcLabel_182 .

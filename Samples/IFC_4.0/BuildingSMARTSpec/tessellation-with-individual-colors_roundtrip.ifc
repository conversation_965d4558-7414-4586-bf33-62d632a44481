ISO-10303-21;
HEADER;
FILE_DESCRIPTION(('ViewDefinition [CoordinationView]'),'2;1');
FILE_NAME('','2020-10-30T00:18:59',(),(),'IfcOpenShell 0.6.0b0','IfcOpenShell 0.6.0b0','');
FILE_SCHEMA(('IFC4'));
ENDSEC;
DATA;
#1=IFCBUILDING('0AcaWYX890yeVBaPRCfpNi',$,'IfcBuilding',$,$,#12,$,$,.ELEMENT.,$,$,#15);
#2=IFCPROJECT('2yXUajt9D3DwMqV1WYGofM',#5,'IfcProject',$,$,'IfcProject',$,(#9),#16);
#3=IFCBUILDINGELEMENTPROXY('3VRU6T9_18bPNzxvGkzDIj',$,'BuildingElementProxy',$,$,#20,#26,$,.NOTDEFINED.);
#4=IFCSHAPEREPRESENTATION(#8,'Body','Tessellation',(#27));
#5=IFCOWNERHISTORY(#29,#32,$,.ADDED.,1418084874,$,$,1418084874);
#6=IFCGEOMETRICREPRESENTATIONCONTEXT($,'Model',3,0.0001,#34,#36);
#7=IFCGEOMETRICREPRESENTATIONSUBCONTEXT('Axis','Model',*,*,*,*,#6,$,.MODEL_VIEW.,$);
#8=IFCGEOMETRICREPRESENTATIONSUBCONTEXT('Body','Model',*,*,*,*,#6,$,.MODEL_VIEW.,$);
#9=IFCGEOMETRICREPRESENTATIONCONTEXT($,'Model',3,0.0001,#37,#39);
#10=IFCRELCONTAINEDINSPATIALSTRUCTURE('16VvjHwgv3ThcRsOElhbPR',$,'Building','Building Container for Elements',(#3),#1);
#11=IFCRELAGGREGATES('0ZHOqoKDzA4Ble9lcTcCu7',$,'Project Container','Project Container for Buildings',#2,(#1));
#12=IFCLOCALPLACEMENT($,#13);
#13=IFCAXIS2PLACEMENT3D(#14,$,$);
#14=IFCCARTESIANPOINT((0.,0.,0.));
#15=IFCPOSTALADDRESS($,$,$,$,$,$,$,'Unknown',$,$);
#16=IFCUNITASSIGNMENT((#17,#18,#19));
#17=IFCSIUNIT(*,.LENGTHUNIT.,.MILLI.,.METRE.);
#18=IFCSIUNIT(*,.PLANEANGLEUNIT.,$,.RADIAN.);
#19=IFCSIUNIT(*,.TIMEUNIT.,$,.SECOND.);
#20=IFCLOCALPLACEMENT(#21,#24);
#21=IFCLOCALPLACEMENT($,#22);
#22=IFCAXIS2PLACEMENT3D(#23,$,$);
#23=IFCCARTESIANPOINT((0.,0.,0.));
#24=IFCAXIS2PLACEMENT3D(#25,$,$);
#25=IFCCARTESIANPOINT((0.,0.,0.));
#26=IFCPRODUCTDEFINITIONSHAPE($,$,(#4));
#27=IFCTRIANGULATEDFACESET(#28,$,.T.,((1,6,5),(1,2,6),(6,2,7),(7,2,3),(7,8,6),(6,8,5),(5,8,1),(1,8,4),(4,2,1),(2,4,3),(4,8,7),(7,3,4)),$);
#28=IFCCARTESIANPOINTLIST3D(((0.,0.,0.),(1000.,0.,0.),(1000.,1000.,0.),(0.,1000.,0.),(0.,0.,2000.),(1000.,0.,2000.),(1000.,1.,2000.),(0.,1000.,2000.)));
#29=IFCPERSONANDORGANIZATION(#30,#31,$);
#30=IFCPERSON('Jon','Jon',$,$,$,$,$,$);
#31=IFCORGANIZATION($,'Geometry Gym Pty Ltd',$,$,$);
#32=IFCAPPLICATION(#33,'*******','ggRhinoIFC - Geometry Gym Plug-in for Rhino3d','ggRhinoIFC');
#33=IFCORGANIZATION($,'Geometry Gym Pty Ltd',$,$,$);
#34=IFCAXIS2PLACEMENT3D(#35,$,$);
#35=IFCCARTESIANPOINT((0.,0.,0.));
#36=IFCDIRECTION((0.,1.));
#37=IFCAXIS2PLACEMENT3D(#38,$,$);
#38=IFCCARTESIANPOINT((0.,0.,0.));
#39=IFCDIRECTION((0.,1.));
ENDSEC;
END-ISO-10303-21;

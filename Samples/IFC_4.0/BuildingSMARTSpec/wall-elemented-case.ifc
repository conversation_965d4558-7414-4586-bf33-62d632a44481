ISO-10303-21;
HEADER;
FILE_DESCRIPTION('ViewDefinition [notYetAssigned]','2;1');
FILE_NAME('ifcwallelementedcase.ifc','2015-03-13T19:19:38',(''),(''),'Constructivity *******','Constructivity *******','');
FILE_SCHEMA(('IFC4'));
ENDSEC;

DATA;

/* Project context */
#1= IFCPROJECT('3jLKJe5mnDk9v_4zehUGXb',$,'Project',$,$,$,$,(#2,#3),#4);
#2= IFCGEOMETRICREPRESENTATIONCONTEXT('3D','Model',3,1.0E-05,#7,$);
#3= IFCGEOMETRICREPRESENTATIONCONTEXT('2D','Plan',2,1.0E-05,#8,$);
#4= IFCUNITASSIGNMENT((#9,#10,#11,#12,#13,#14,#15,#16,#17));
#7= IFCAXIS2PLACEMENT3D(#18,$,$);
#8= IFCAXIS2PLACEMENT3D(#19,$,$);
#9= IFCCONVERSIONBASEDUNIT(#20,.AREAUNIT.,'square inch',#21);
#10= IFCCONVERSIONBASEDUNIT(#22,.FORCEUNIT.,'pound-force',#23);
#11= IFCCONVERSIONBASEDUNIT(#24,.LENGTHUNIT.,'inch',#25);
#12= IFCCONVERSIONBASEDUNIT(#26,.MASSUNIT.,'pound',#27);
#13= IFCCONVERSIONBASEDUNIT(#28,.PLANEANGLEUNIT.,'degree',#29);
#14= IFCCONVERSIONBASEDUNIT(#30,.PRESSUREUNIT.,'pound-force per square inch',#31);
#15= IFCCONVERSIONBASEDUNITWITHOFFSET(#32,.THERMODYNAMICTEMPERATUREUNIT.,'Fahrenheit',#33,-459.67);
#16= IFCCONVERSIONBASEDUNIT(#34,.VOLUMEUNIT.,'cubic inch',#35);
#17= IFCMONETARYUNIT('USD');
#18= IFCCARTESIANPOINT((0.,0.,0.));
#19= IFCCARTESIANPOINT((0.,0.,0.));
#20= IFCDIMENSIONALEXPONENTS(2,0,0,0,0,0,0);
#21= IFCMEASUREWITHUNIT(IFCAREAMEASURE(0.0006452),#36);
#22= IFCDIMENSIONALEXPONENTS(1,1,-2,0,0,0,0);
#23= IFCMEASUREWITHUNIT(IFCMASSMEASURE(4.44822162),#37);
#24= IFCDIMENSIONALEXPONENTS(1,0,0,0,0,0,0);
#25= IFCMEASUREWITHUNIT(IFCLENGTHMEASURE(0.0254),#38);
#26= IFCDIMENSIONALEXPONENTS(0,1,0,0,0,0,0);
#27= IFCMEASUREWITHUNIT(IFCMASSMEASURE(0.45359237),#39);
#28= IFCDIMENSIONALEXPONENTS(0,0,0,0,0,0,0);
#29= IFCMEASUREWITHUNIT(IFCPLANEANGLEMEASURE(0.0174532925199433),#40);
#30= IFCDIMENSIONALEXPONENTS(-1,1,-2,0,0,0,0);
#31= IFCMEASUREWITHUNIT(IFCPRESSUREMEASURE(6894.7572932),#41);
#32= IFCDIMENSIONALEXPONENTS(0,0,0,0,1,0,0);
#33= IFCMEASUREWITHUNIT(IFCTHERMODYNAMICTEMPERATUREMEASURE(1.8),#42);
#34= IFCDIMENSIONALEXPONENTS(3,0,0,0,0,0,0);
#35= IFCMEASUREWITHUNIT(IFCVOLUMEMEASURE(1.639E-05),#43);
#36= IFCSIUNIT(*,.AREAUNIT.,$,.SQUARE_METRE.);
#37= IFCSIUNIT(*,.FORCEUNIT.,$,.NEWTON.);
#38= IFCSIUNIT(*,.LENGTHUNIT.,$,.METRE.);
#39= IFCSIUNIT(*,.MASSUNIT.,.KILO.,.GRAM.);
#40= IFCSIUNIT(*,.PLANEANGLEUNIT.,$,.RADIAN.);
#41= IFCSIUNIT(*,.PRESSUREUNIT.,$,.PASCAL.);
#42= IFCSIUNIT(*,.THERMODYNAMICTEMPERATUREUNIT.,$,.KELVIN.);
#43= IFCSIUNIT(*,.VOLUMEUNIT.,$,.CUBIC_METRE.);

/* The project aggregates a site which contains the elemented wall */
#5= IFCRELAGGREGATES('0LpO_kYrr9RvWBWZZ1$J9y',$,$,$,#1,(#44));

/* Types used for elements are declared on project */
#6= IFCRELDECLARES('3Zu$B51Gv7hvX_I1v6LiPo',$,$,$,#1,(#45,#46,#47,#48,#49));

/* The site contains the wall for this example; typically walls would be included in a building storey */
#44= IFCSITE('0$2MxjB$PAzwRPEqmXLEy9',$,'Site #1',$,$,#50,#51,$,.ELEMENT.,$,$,$,$,$);
#50= IFCLOCALPLACEMENT($,#53);
#51= IFCPRODUCTDEFINITIONSHAPE($,$,(#54));
#53= IFCAXIS2PLACEMENT3D(#55,$,$);
#54= IFCSHAPEREPRESENTATION(#2,'FootPrint','GeometricCurveSet',(#56));
#55= IFCCARTESIANPOINT((0.,0.,0.));
#56= IFCGEOMETRICCURVESET((#57));
#57= IFCPOLYLINE((#58,#59,#60,#61,#58));
#58= IFCCARTESIANPOINT((0.,0.));
#59= IFCCARTESIANPOINT((1536.,0.));
#60= IFCCARTESIANPOINT((1536.,768.));
#61= IFCCARTESIANPOINT((0.,768.));

/* Define the component for track members (base plate and top plate) */
#45= IFCMEMBERTYPE('0fLvCK5n50nxLpZkG$JUuI',$,'Wood Track',$,$,$,(#62),'2x4',$,.PLATE.);
#62= IFCREPRESENTATIONMAP(#65,#66);
#65= IFCAXIS2PLACEMENT3D(#67,$,$);
#66= IFCSHAPEREPRESENTATION(#2,'Body','SweptSolid',(#68));
#67= IFCCARTESIANPOINT((0.,0.,0.));
#68= IFCEXTRUDEDAREASOLID(#69,#70,#71,96.);
#69= IFCRECTANGLEPROFILEDEF(.AREA.,$,$,1.5,3.5);
#70= IFCAXIS2PLACEMENT3D(#73,#74,#75);
#71= IFCDIRECTION((0.,0.,1.));
#72= IFCSTYLEDITEM(#68,(#76),$);
#73= IFCCARTESIANPOINT((0.,0.,0.));
#74= IFCDIRECTION((1.,0.,0.));
#75= IFCDIRECTION((0.,1.,0.));
#76= IFCSURFACESTYLE($,.POSITIVE.,(#77));
#77= IFCSURFACESTYLERENDERING(#78,$,$,$,$,$,$,$,.NOTDEFINED.);
#78= IFCCOLOURRGB($,1.,0.752941176470588,0.);

/* Define the component for stud members */
#46= IFCMEMBERTYPE('0ZAqUxV0P3hQFszDlG2CZT',$,'Wood Stud',$,$,$,(#79),'2x4',$,.STUD.);
#79= IFCREPRESENTATIONMAP(#81,#82);
#81= IFCAXIS2PLACEMENT3D(#83,$,$);
#82= IFCSHAPEREPRESENTATION(#2,'Body','SweptSolid',(#84));
#83= IFCCARTESIANPOINT((0.,0.,0.));
#84= IFCEXTRUDEDAREASOLID(#85,#86,#87,92.5);
#85= IFCRECTANGLEPROFILEDEF(.AREA.,$,$,1.5,3.5);
#86= IFCAXIS2PLACEMENT3D(#89,#90,#91);
#87= IFCDIRECTION((0.,0.,1.));
#88= IFCSTYLEDITEM(#84,(#92),$);
#89= IFCCARTESIANPOINT((0.,0.,0.));
#90= IFCDIRECTION((1.,0.,0.));
#91= IFCDIRECTION((0.,1.,0.));
#92= IFCSURFACESTYLE($,.POSITIVE.,(#93));
#93= IFCSURFACESTYLERENDERING(#94,$,$,$,$,$,$,$,.NOTDEFINED.);
#94= IFCCOLOURRGB($,1.,0.752941176470588,0.);

/* Define the component for gypsym panels */
#47= IFCBUILDINGELEMENTPARTTYPE('3VCzyu65H0uBtwFTYyM$je',$,'Drywall',$,$,$,(#95),'X',$,.PRECASTPANEL.);
#95= IFCREPRESENTATIONMAP(#98,#99);
#98= IFCAXIS2PLACEMENT3D(#100,$,$);
#99= IFCSHAPEREPRESENTATION(#2,'Body','SweptSolid',(#101));
#100= IFCCARTESIANPOINT((0.,0.,0.));
#101= IFCEXTRUDEDAREASOLID(#102,#103,#104,0.5);
#102= IFCRECTANGLEPROFILEDEF(.AREA.,$,$,96.,48.);
#103= IFCAXIS2PLACEMENT3D(#106,#107,#108);
#104= IFCDIRECTION((0.,0.,1.));
#105= IFCSTYLEDITEM(#101,(#109),$);
#106= IFCCARTESIANPOINT((48.,24.,0.));
#107= IFCDIRECTION((0.,0.,1.));
#108= IFCDIRECTION((1.,0.,0.));
#109= IFCSURFACESTYLE($,.POSITIVE.,(#110));
#110= IFCSURFACESTYLERENDERING(#111,$,$,$,$,$,$,$,.NOTDEFINED.);
#111= IFCCOLOURRGB($,0.607843137254902,0.733333333333333,0.349019607843137);

/* Define the component for wall framing, which includes studs and tracks */
#48= IFCELEMENTASSEMBLYTYPE('3LWWZQqmn4RO3Pvh2yNsE3',$,'Wood Stud Frame',$,$,$,$,$,$,.NOTDEFINED.);

/* Define the component for wall, which includes wall framing and drywall */
#49= IFCWALLTYPE('18dyITGxr8vvBzWasHOIhB',$,'Wood Framed Wall',$,$,$,$,$,$,.NOTDEFINED.);

/* The site contains the wall occurrence */
#52= IFCRELCONTAINEDINSPATIALSTRUCTURE('3GkTYDE011eOE5MPsYgvmO',$,$,$,(#123),#44);

/* The material of the studs and tracks is defined a profile of 2x4 wood (2x4 refers to nominal dimensions for 1.5 inches by 3.5 inches) */
#63= IFCRELASSOCIATESMATERIAL('3jKgd0d$X8numOzmr$PX4I',$,$,$,(#46,#45),#124);
#124= IFCMATERIALPROFILESET('2x4 Lumber',$,(#125),$);
#125= IFCMATERIALPROFILE($,$,#126,#127,$,$);
#126= IFCMATERIAL('Southern Pine',$,'Wood');
#127= IFCRECTANGLEPROFILEDEF(.AREA.,$,$,1.5,3.5);

#64= IFCRELDEFINESBYTYPE('0Rs3H_63TAwQ71vL6XMwkm',$,$,$,(#128,#129,#130),#45);

#80= IFCRELDEFINESBYTYPE('23JxxxCaP1D9_Yke1YqK_4',$,$,$,(#131,#132,#133),#46);

/* The material of the paneling is defined by a layer of Type X gypsum */
#96= IFCRELASSOCIATESMATERIAL('18fTMjClX5afvrQY7ovGJV',$,$,$,(#47),#134);
#134= IFCMATERIALLAYERSET((#135),$,$);
#135= IFCMATERIALLAYERWITHOFFSETS(#136,0.5,$,$,$,$,$,.AXIS3.,(0.,0.));
#136= IFCMATERIAL('X',$,'Gypsum');
#137= IFCMATERIALDEFINITIONREPRESENTATION($,$,(#138),#136);
#138= IFCSTYLEDREPRESENTATION(#3,'Undefined','Undefined',(#139));
#139= IFCSTYLEDITEM($,(#140),$);
#140= IFCSURFACESTYLE($,.POSITIVE.,(#141));
#141= IFCSURFACESTYLESHADING(#142,$);
#142= IFCCOLOURRGB($,0.752941176470588,0.752941176470588,0.752941176470588);

#97= IFCRELDEFINESBYTYPE('3HMQ8jSxH4eP7NCIB0AfQK',$,$,$,(#143,#144,#145,#146),#47);

#113= IFCRELAGGREGATES('2__AVJ3MH9OvTHd22TysYH',$,$,$,#48,(#128,#131));

#114= IFCRELDEFINESBYTYPE('1wrfQZgun13R0AuyIGNDE5',$,$,$,(#147,#148),#48);

#120= IFCRELASSOCIATESMATERIAL('2xhXrFIHbEPOyoie$hOuw2',$,$,$,(#49),#149);
#149= IFCMATERIALLAYERSET((#150,#151,#152),'Wall',$);
#150= IFCMATERIALLAYERWITHOFFSETS($,0.5,$,'Panel Forward',$,$,$,.AXIS3.,(0.,0.));
#151= IFCMATERIALLAYERWITHOFFSETS($,3.5,$,'Frame',$,$,$,.AXIS1.,(0.,0.));
#152= IFCMATERIALLAYERWITHOFFSETS($,0.5,$,'Panel Reverse',$,$,$,.AXIS1.,(0.,0.));

#121= IFCRELAGGREGATES('3E6JijosnAOwuYCniGEvpq',$,$,$,#49,(#143,#147,#144));

#122= IFCRELDEFINESBYTYPE('0b7wBKEN98nvtgUGavkVDu',$,$,$,(#123),#49);

/* The wall occurrence is an instance of the wall type, and aggregates instantiations of all components */
#123= IFCWALLELEMENTEDCASE('2ucZRLBGP4uxZW$9i1VAZ8',$,'Wall #1',$,$,#153,#154,$,.MOVABLE.);
#153= IFCLOCALPLACEMENT(#50,#159);
#154= IFCPRODUCTDEFINITIONSHAPE($,$,(#160));
#159= IFCAXIS2PLACEMENT3D(#162,#163,#164);
#160= IFCSHAPEREPRESENTATION(#2,'Axis','Curve2D',(#165));
#162= IFCCARTESIANPOINT((48.,48.,0.));
#163= IFCDIRECTION((0.,0.,1.));
#164= IFCDIRECTION((1.,0.,0.));
#165= IFCPOLYLINE((#167,#168));
#167= IFCCARTESIANPOINT((0.,0.));
#168= IFCCARTESIANPOINT((144.,0.));

#128= IFCMEMBER('0a_4mEHnHCmQlwrbwchCWn',$,'Track',$,$,$,$,$,.PLATE.);

#129= IFCMEMBER('30lp6UJyv4KwjZ$VV5ySkA',$,'Track',$,$,$,$,$,.PLATE.);

/* The tracks are instantiated as a single object with multiple placements - upper and lower, and five segments to accomodate openings */
#130= IFCMEMBER('3E4igxyD1F5w4vj3LTAREz',$,'Track',$,$,#182,#183,$,.PLATE.);
#182= IFCLOCALPLACEMENT(#186,#187);
#183= IFCPRODUCTDEFINITIONSHAPE($,$,(#188));
#186= IFCLOCALPLACEMENT(#153,#189);
#187= IFCAXIS2PLACEMENT3D(#190,$,$);
#188= IFCSHAPEREPRESENTATION(#2,'Body','MappedRepresentation',(#191,#192,#193,#194,#195,#196,#197,#198,#199,#200,#201,#202));
#189= IFCAXIS2PLACEMENT3D(#203,#204,#205);
#190= IFCCARTESIANPOINT((0.,0.,0.));
#191= IFCMAPPEDITEM(#62,#206);
#192= IFCMAPPEDITEM(#62,#207);
#193= IFCMAPPEDITEM(#62,#208);
#194= IFCMAPPEDITEM(#62,#209);
#195= IFCMAPPEDITEM(#62,#210);
#196= IFCMAPPEDITEM(#62,#211);
#197= IFCMAPPEDITEM(#62,#212);
#198= IFCMAPPEDITEM(#62,#213);
#199= IFCMAPPEDITEM(#62,#214);
#200= IFCMAPPEDITEM(#62,#215);
#201= IFCMAPPEDITEM(#62,#216);
#202= IFCMAPPEDITEM(#62,#217);
#203= IFCCARTESIANPOINT((0.,0.5,0.));
#204= IFCDIRECTION((0.,-1.,0.));
#205= IFCDIRECTION((1.,0.,0.));
#206= IFCCARTESIANTRANSFORMATIONOPERATOR3DNONUNIFORM($,$,#218,0.25,$,1.,1.);
#207= IFCCARTESIANTRANSFORMATIONOPERATOR3DNONUNIFORM($,$,#219,0.25,$,1.,1.);
#208= IFCCARTESIANTRANSFORMATIONOPERATOR3DNONUNIFORM($,$,#220,0.333333333333333,$,1.,1.);
#209= IFCCARTESIANTRANSFORMATIONOPERATOR3DNONUNIFORM($,$,#221,0.333333333333333,$,1.,1.);
#210= IFCCARTESIANTRANSFORMATIONOPERATOR3DNONUNIFORM($,$,#222,0.291666666666667,$,1.,1.);
#211= IFCCARTESIANTRANSFORMATIONOPERATOR3DNONUNIFORM($,$,#223,0.291666666666667,$,1.,1.);
#212= IFCCARTESIANTRANSFORMATIONOPERATOR3DNONUNIFORM($,$,#224,0.375,$,1.,1.);
#213= IFCCARTESIANTRANSFORMATIONOPERATOR3DNONUNIFORM($,$,#225,0.375,$,1.,1.);
#214= IFCCARTESIANTRANSFORMATIONOPERATOR3DNONUNIFORM($,$,#226,0.375,$,1.,1.);
#215= IFCCARTESIANTRANSFORMATIONOPERATOR3DNONUNIFORM($,$,#227,0.375,$,1.,1.);
#216= IFCCARTESIANTRANSFORMATIONOPERATOR3DNONUNIFORM($,$,#228,0.25,$,1.,1.);
#217= IFCCARTESIANTRANSFORMATIONOPERATOR3DNONUNIFORM($,$,#229,0.25,$,1.,1.);
#218= IFCCARTESIANPOINT((0.,0.75,-1.75));
#219= IFCCARTESIANPOINT((0.,119.25,-1.75));
#220= IFCCARTESIANPOINT((24.,80.75,-1.75));
#221= IFCCARTESIANPOINT((24.,119.25,-1.75));
#222= IFCCARTESIANPOINT((56.,0.75,-1.75));
#223= IFCCARTESIANPOINT((56.,119.25,-1.75));
#224= IFCCARTESIANPOINT((84.,0.75,-1.75));
#225= IFCCARTESIANPOINT((84.,31.25,-1.75));
#226= IFCCARTESIANPOINT((84.,80.75,-1.75));
#227= IFCCARTESIANPOINT((84.,119.25,-1.75));
#228= IFCCARTESIANPOINT((120.,0.75,-1.75));
#229= IFCCARTESIANPOINT((120.,119.25,-1.75));

#131= IFCMEMBER('0EbognDS18svu9I3VRH00k',$,'Studs',$,$,$,$,$,.STUD.);

#132= IFCMEMBER('2Gwic0O6f3MQF17H5mnvsI',$,'Studs',$,$,$,$,$,.STUD.);

/* The studs are instanted as a single object with multiple placements - five sections to accomodate openings, with starting, ending, and intermediate */
#133= IFCMEMBER('0y81T5OQ921PKefugNkz2z',$,'Studs',$,$,#232,#233,$,.STUD.);
#232= IFCLOCALPLACEMENT(#186,#235);
#233= IFCPRODUCTDEFINITIONSHAPE($,$,(#236));
#235= IFCAXIS2PLACEMENT3D(#237,$,$);
#236= IFCSHAPEREPRESENTATION(#2,'Body','MappedRepresentation',(#238,#239,#240,#241,#242,#243,#244,#245,#246,#247,#248,#249,#250,#251,#252,#253,#254,#255,#256,#257));
#237= IFCCARTESIANPOINT((0.,0.,0.));
#238= IFCMAPPEDITEM(#79,#258);
#239= IFCMAPPEDITEM(#79,#259);
#240= IFCMAPPEDITEM(#79,#260);
#241= IFCMAPPEDITEM(#79,#261);
#242= IFCMAPPEDITEM(#79,#262);
#243= IFCMAPPEDITEM(#79,#263);
#244= IFCMAPPEDITEM(#79,#264);
#245= IFCMAPPEDITEM(#79,#265);
#246= IFCMAPPEDITEM(#79,#266);
#247= IFCMAPPEDITEM(#79,#267);
#248= IFCMAPPEDITEM(#79,#268);
#249= IFCMAPPEDITEM(#79,#269);
#250= IFCMAPPEDITEM(#79,#270);
#251= IFCMAPPEDITEM(#79,#271);
#252= IFCMAPPEDITEM(#79,#272);
#253= IFCMAPPEDITEM(#79,#273);
#254= IFCMAPPEDITEM(#79,#274);
#255= IFCMAPPEDITEM(#79,#275);
#256= IFCMAPPEDITEM(#79,#276);
#257= IFCMAPPEDITEM(#79,#277);
#258= IFCCARTESIANTRANSFORMATIONOPERATOR3DNONUNIFORM(#278,#279,#280,1.26486486486486,#281,1.,1.);
#259= IFCCARTESIANTRANSFORMATIONOPERATOR3DNONUNIFORM(#282,#283,#284,1.26486486486486,#285,1.,1.);
#260= IFCCARTESIANTRANSFORMATIONOPERATOR3DNONUNIFORM(#286,#287,#288,1.26486486486486,#289,1.,1.);
#261= IFCCARTESIANTRANSFORMATIONOPERATOR3DNONUNIFORM(#290,#291,#292,0.4,#293,1.,1.);
#262= IFCCARTESIANTRANSFORMATIONOPERATOR3DNONUNIFORM(#294,#295,#296,0.4,#297,1.,1.);
#263= IFCCARTESIANTRANSFORMATIONOPERATOR3DNONUNIFORM(#298,#299,#300,0.4,#301,1.,1.);
#264= IFCCARTESIANTRANSFORMATIONOPERATOR3DNONUNIFORM(#302,#303,#304,1.26486486486486,#305,1.,1.);
#265= IFCCARTESIANTRANSFORMATIONOPERATOR3DNONUNIFORM(#306,#307,#308,1.26486486486486,#309,1.,1.);
#266= IFCCARTESIANTRANSFORMATIONOPERATOR3DNONUNIFORM(#310,#311,#312,1.26486486486486,#313,1.,1.);
#267= IFCCARTESIANTRANSFORMATIONOPERATOR3DNONUNIFORM(#314,#315,#316,0.313513513513514,#317,1.,1.);
#268= IFCCARTESIANTRANSFORMATIONOPERATOR3DNONUNIFORM(#318,#319,#320,0.313513513513514,#321,1.,1.);
#269= IFCCARTESIANTRANSFORMATIONOPERATOR3DNONUNIFORM(#322,#323,#324,0.313513513513514,#325,1.,1.);
#270= IFCCARTESIANTRANSFORMATIONOPERATOR3DNONUNIFORM(#326,#327,#328,0.313513513513514,#329,1.,1.);
#271= IFCCARTESIANTRANSFORMATIONOPERATOR3DNONUNIFORM(#330,#331,#332,0.4,#333,1.,1.);
#272= IFCCARTESIANTRANSFORMATIONOPERATOR3DNONUNIFORM(#334,#335,#336,0.4,#337,1.,1.);
#273= IFCCARTESIANTRANSFORMATIONOPERATOR3DNONUNIFORM(#338,#339,#340,0.4,#341,1.,1.);
#274= IFCCARTESIANTRANSFORMATIONOPERATOR3DNONUNIFORM(#342,#343,#344,0.4,#345,1.,1.);
#275= IFCCARTESIANTRANSFORMATIONOPERATOR3DNONUNIFORM(#346,#347,#348,1.26486486486486,#349,1.,1.);
#276= IFCCARTESIANTRANSFORMATIONOPERATOR3DNONUNIFORM(#350,#351,#352,1.26486486486486,#353,1.,1.);
#277= IFCCARTESIANTRANSFORMATIONOPERATOR3DNONUNIFORM(#354,#355,#356,1.26486486486486,#357,1.,1.);
#278= IFCDIRECTION((0.,1.,0.));
#279= IFCDIRECTION((1.,0.,0.));
#280= IFCCARTESIANPOINT((0.75,1.5,-1.75));
#281= IFCDIRECTION((0.,0.,1.));
#282= IFCDIRECTION((0.,1.,0.));
#283= IFCDIRECTION((1.,0.,0.));
#284= IFCCARTESIANPOINT((16.75,1.5,-1.75));
#285= IFCDIRECTION((0.,0.,1.));
#286= IFCDIRECTION((0.,1.,0.));
#287= IFCDIRECTION((1.,0.,0.));
#288= IFCCARTESIANPOINT((23.25,1.5,-1.75));
#289= IFCDIRECTION((0.,0.,1.));
#290= IFCDIRECTION((0.,1.,0.));
#291= IFCDIRECTION((1.,0.,0.));
#292= IFCCARTESIANPOINT((24.75,81.5,-1.75));
#293= IFCDIRECTION((0.,0.,1.));
#294= IFCDIRECTION((0.,1.,0.));
#295= IFCDIRECTION((1.,0.,0.));
#296= IFCCARTESIANPOINT((40.75,81.5,-1.75));
#297= IFCDIRECTION((0.,0.,1.));
#298= IFCDIRECTION((0.,1.,0.));
#299= IFCDIRECTION((1.,0.,0.));
#300= IFCCARTESIANPOINT((55.25,81.5,-1.75));
#301= IFCDIRECTION((0.,0.,1.));
#302= IFCDIRECTION((0.,1.,0.));
#303= IFCDIRECTION((1.,0.,0.));
#304= IFCCARTESIANPOINT((56.75,1.5,-1.75));
#305= IFCDIRECTION((0.,0.,1.));
#306= IFCDIRECTION((0.,1.,0.));
#307= IFCDIRECTION((1.,0.,0.));
#308= IFCCARTESIANPOINT((72.75,1.5,-1.75));
#309= IFCDIRECTION((0.,0.,1.));
#310= IFCDIRECTION((0.,1.,0.));
#311= IFCDIRECTION((1.,0.,0.));
#312= IFCCARTESIANPOINT((83.25,1.5,-1.75));
#313= IFCDIRECTION((0.,0.,1.));
#314= IFCDIRECTION((0.,1.,0.));
#315= IFCDIRECTION((1.,0.,0.));
#316= IFCCARTESIANPOINT((84.75,1.5,-1.75));
#317= IFCDIRECTION((0.,0.,1.));
#318= IFCDIRECTION((0.,1.,0.));
#319= IFCDIRECTION((1.,0.,0.));
#320= IFCCARTESIANPOINT((100.75,1.5,-1.75));
#321= IFCDIRECTION((0.,0.,1.));
#322= IFCDIRECTION((0.,1.,0.));
#323= IFCDIRECTION((1.,0.,0.));
#324= IFCCARTESIANPOINT((116.75,1.5,-1.75));
#325= IFCDIRECTION((0.,0.,1.));
#326= IFCDIRECTION((0.,1.,0.));
#327= IFCDIRECTION((1.,0.,0.));
#328= IFCCARTESIANPOINT((119.25,1.5,-1.75));
#329= IFCDIRECTION((0.,0.,1.));
#330= IFCDIRECTION((0.,1.,0.));
#331= IFCDIRECTION((1.,0.,0.));
#332= IFCCARTESIANPOINT((84.75,81.5,-1.75));
#333= IFCDIRECTION((0.,0.,1.));
#334= IFCDIRECTION((0.,1.,0.));
#335= IFCDIRECTION((1.,0.,0.));
#336= IFCCARTESIANPOINT((100.75,81.5,-1.75));
#337= IFCDIRECTION((0.,0.,1.));
#338= IFCDIRECTION((0.,1.,0.));
#339= IFCDIRECTION((1.,0.,0.));
#340= IFCCARTESIANPOINT((116.75,81.5,-1.75));
#341= IFCDIRECTION((0.,0.,1.));
#342= IFCDIRECTION((0.,1.,0.));
#343= IFCDIRECTION((1.,0.,0.));
#344= IFCCARTESIANPOINT((119.25,81.5,-1.75));
#345= IFCDIRECTION((0.,0.,1.));
#346= IFCDIRECTION((0.,1.,0.));
#347= IFCDIRECTION((1.,0.,0.));
#348= IFCCARTESIANPOINT((120.75,1.5,-1.75));
#349= IFCDIRECTION((0.,0.,1.));
#350= IFCDIRECTION((0.,1.,0.));
#351= IFCDIRECTION((1.,0.,0.));
#352= IFCCARTESIANPOINT((136.75,1.5,-1.75));
#353= IFCDIRECTION((0.,0.,1.));
#354= IFCDIRECTION((0.,1.,0.));
#355= IFCDIRECTION((1.,0.,0.));
#356= IFCCARTESIANPOINT((143.25,1.5,-1.75));
#357= IFCDIRECTION((0.,0.,1.));

#143= IFCBUILDINGELEMENTPART('0Pechpapf3Igz9XLIvG5k1',$,'Panel Forward',$,$,$,$,$,.PRECASTPANEL.);

#144= IFCBUILDINGELEMENTPART('0F5ybxF0HADQhVNDyXC1ic',$,'Panel Reverse',$,$,$,$,$,.PRECASTPANEL.);

/* The forward facing panels are instantiated as a single object with multiple placements to accomodate openings. */
#145= IFCBUILDINGELEMENTPART('1dhqqYR5v9EPzs7Ts1dOEX',$,'Panel Forward',$,$,#358,#359,$,.PRECASTPANEL.);
#358= IFCLOCALPLACEMENT(#153,#360);
#359= IFCPRODUCTDEFINITIONSHAPE($,$,(#361));
#360= IFCAXIS2PLACEMENT3D(#362,#363,#364);
#361= IFCSHAPEREPRESENTATION(#2,'Body','MappedRepresentation',(#365,#366,#367,#368,#369,#370));
#362= IFCCARTESIANPOINT((0.,0.,0.));
#363= IFCDIRECTION((0.,-1.,0.));
#364= IFCDIRECTION((1.,0.,0.));
#365= IFCMAPPEDITEM(#95,#371);
#366= IFCMAPPEDITEM(#95,#372);
#367= IFCMAPPEDITEM(#95,#373);
#368= IFCMAPPEDITEM(#95,#374);
#369= IFCMAPPEDITEM(#95,#375);
#370= IFCMAPPEDITEM(#95,#376);
#371= IFCCARTESIANTRANSFORMATIONOPERATOR3DNONUNIFORM($,$,#377,0.25,$,2.5,1.);
#372= IFCCARTESIANTRANSFORMATIONOPERATOR3DNONUNIFORM($,$,#378,0.333333333333333,$,0.833333333333333,1.);
#373= IFCCARTESIANTRANSFORMATIONOPERATOR3DNONUNIFORM($,$,#379,0.291666666666667,$,2.5,1.);
#374= IFCCARTESIANTRANSFORMATIONOPERATOR3DNONUNIFORM($,$,#380,0.375,$,0.666666666666667,1.);
#375= IFCCARTESIANTRANSFORMATIONOPERATOR3DNONUNIFORM($,$,#381,0.375,$,0.833333333333333,1.);
#376= IFCCARTESIANTRANSFORMATIONOPERATOR3DNONUNIFORM($,$,#382,0.25,$,2.5,1.);
#377= IFCCARTESIANPOINT((0.,0.,-0.5));
#378= IFCCARTESIANPOINT((24.,80.,-0.5));
#379= IFCCARTESIANPOINT((56.,0.,-0.5));
#380= IFCCARTESIANPOINT((84.,0.,-0.5));
#381= IFCCARTESIANPOINT((84.,80.,-0.5));
#382= IFCCARTESIANPOINT((120.,0.,-0.5));

/* The reverse facing panels are instantiated as a single object with multiple placements to accomodate openings. */
#146= IFCBUILDINGELEMENTPART('3dca$PAJT1XA6b06dQW26g',$,'Panel Reverse',$,$,#383,#384,$,.PRECASTPANEL.);
#383= IFCLOCALPLACEMENT(#153,#385);
#384= IFCPRODUCTDEFINITIONSHAPE($,$,(#386));
#385= IFCAXIS2PLACEMENT3D(#387,#388,#389);
#386= IFCSHAPEREPRESENTATION(#2,'Body','MappedRepresentation',(#390,#391,#392,#393,#394,#395));
#387= IFCCARTESIANPOINT((0.,4.,0.));
#388= IFCDIRECTION((0.,-1.,0.));
#389= IFCDIRECTION((1.,0.,0.));
#390= IFCMAPPEDITEM(#95,#396);
#391= IFCMAPPEDITEM(#95,#397);
#392= IFCMAPPEDITEM(#95,#398);
#393= IFCMAPPEDITEM(#95,#399);
#394= IFCMAPPEDITEM(#95,#400);
#395= IFCMAPPEDITEM(#95,#401);
#396= IFCCARTESIANTRANSFORMATIONOPERATOR3DNONUNIFORM($,$,#402,0.25,$,2.5,1.);
#397= IFCCARTESIANTRANSFORMATIONOPERATOR3DNONUNIFORM($,$,#403,0.333333333333333,$,0.833333333333333,1.);
#398= IFCCARTESIANTRANSFORMATIONOPERATOR3DNONUNIFORM($,$,#404,0.291666666666667,$,2.5,1.);
#399= IFCCARTESIANTRANSFORMATIONOPERATOR3DNONUNIFORM($,$,#405,0.375,$,0.666666666666667,1.);
#400= IFCCARTESIANTRANSFORMATIONOPERATOR3DNONUNIFORM($,$,#406,0.375,$,0.833333333333333,1.);
#401= IFCCARTESIANTRANSFORMATIONOPERATOR3DNONUNIFORM($,$,#407,0.25,$,2.5,1.);
#402= IFCCARTESIANPOINT((0.,0.,-0.5));
#403= IFCCARTESIANPOINT((24.,80.,-0.5));
#404= IFCCARTESIANPOINT((56.,0.,-0.5));
#405= IFCCARTESIANPOINT((84.,0.,-0.5));
#406= IFCCARTESIANPOINT((84.,80.,-0.5));
#407= IFCCARTESIANPOINT((120.,0.,-0.5));

#147= IFCELEMENTASSEMBLY('154dNyacTC9ODBiATxKNJ5',$,'Frame',$,$,$,$,$,$,.BRACED_FRAME.);

#148= IFCELEMENTASSEMBLY('0tQt_zoibF6gdoecLxBvHT',$,'Frame',$,$,#186,$,$,$,.BRACED_FRAME.);

#155= IFCRELASSOCIATESMATERIAL('0I3e_5YYL0ZxVzuxShgDpU',$,$,$,(#123),#408);
#408= IFCMATERIALLAYERSETUSAGE(#149,.AXIS2.,.POSITIVE.,0.,$);

#156= IFCRELAGGREGATES('1fzLvTCX14ZPMFC2C3x$Zm',$,$,$,#123,(#145,#148,#146));

#157= IFCRELVOIDSELEMENT('0PPCo0_gPA7wruqoSJRqNB',$,$,$,#123,#409);

#158= IFCRELVOIDSELEMENT('3MZmErcrnCphKQbTBDCShY',$,$,$,#123,#410);

#179= IFCRELASSOCIATESMATERIAL('3vPcKoYsT1oflDajkC6Szx',$,$,$,(#128),#411);
#411= IFCMATERIALPROFILESETUSAGE(#412,2,$);
#412= IFCMATERIALPROFILESET($,$,(#413),$);
#413= IFCMATERIALPROFILE($,$,#414,#415,$,$);
#414= IFCMATERIAL('Southern Pine',$,$);
#415= IFCRECTANGLEPROFILEDEF(.AREA.,'2x10',$,1.5,9.25);
#418= IFCMATERIALDEFINITIONREPRESENTATION($,$,(#427),#414);
#427= IFCSTYLEDREPRESENTATION(#3,'Undefined','Undefined',(#428));
#428= IFCSTYLEDITEM($,(#429),$);
#429= IFCSURFACESTYLE($,.POSITIVE.,(#430));
#430= IFCSURFACESTYLESHADING(#431,$);
#431= IFCCOLOURRGB($,1.,0.501960784313725,0.250980392156863);

#180= IFCRELASSOCIATESMATERIAL('04xnFkSZ568fB3nbe7UdDe',$,$,$,(#129),#432);
#432= IFCMATERIALPROFILESETUSAGE(#433,$,$);
#433= IFCMATERIALPROFILESET($,$,(#434),$);
#434= IFCMATERIALPROFILE($,$,#435,#436,$,$);
#435= IFCMATERIAL('Southern Pine',$,$);
#436= IFCRECTANGLEPROFILEDEF(.AREA.,$,$,3.5,1.5);
#437= IFCMATERIALDEFINITIONREPRESENTATION($,$,(#438),#435);
#438= IFCSTYLEDREPRESENTATION(#439,'Undefined','Undefined',(#440));
#439= IFCGEOMETRICREPRESENTATIONCONTEXT('2D','Plan',2,1.0E-05,#441,$);
#440= IFCSTYLEDITEM($,(#442),$);
#441= IFCAXIS2PLACEMENT3D(#443,$,$);
#442= IFCSURFACESTYLE($,.POSITIVE.,(#444));
#443= IFCCARTESIANPOINT((0.,0.,0.));
#444= IFCSURFACESTYLESHADING(#445,$);
#445= IFCCOLOURRGB($,1.,0.501960784313725,0.250980392156863);

#181= IFCRELAGGREGATES('2k3vYiLT158Q8SOcrMsXJR',$,$,$,#147,(#129,#132));

#184= IFCRELASSOCIATESMATERIAL('04MHfV7HXC4ge78OwKZa5s',$,$,$,(#130),#446);
#446= IFCMATERIALPROFILESETUSAGE(#447,$,$);
#447= IFCMATERIALPROFILESET($,$,(#448),$);
#448= IFCMATERIALPROFILE($,$,#449,#450,$,$);
#449= IFCMATERIAL('Southern Pine',$,$);
#450= IFCRECTANGLEPROFILEDEF(.AREA.,$,$,3.5,1.5);
#451= IFCMATERIALDEFINITIONREPRESENTATION($,$,(#452),#449);
#452= IFCSTYLEDREPRESENTATION(#453,'Undefined','Undefined',(#454));
#453= IFCGEOMETRICREPRESENTATIONCONTEXT('2D','Plan',2,1.0E-05,#455,$);
#454= IFCSTYLEDITEM($,(#456),$);
#455= IFCAXIS2PLACEMENT3D(#457,$,$);
#456= IFCSURFACESTYLE($,.POSITIVE.,(#458));
#457= IFCCARTESIANPOINT((0.,0.,0.));
#458= IFCSURFACESTYLESHADING(#459,$);
#459= IFCCOLOURRGB($,1.,0.501960784313725,0.250980392156863);

#185= IFCRELAGGREGATES('0BLnMzOcT8GOmdBBx7N4h2',$,$,$,#148,(#130,#133));

#230= IFCRELASSOCIATESMATERIAL('3mjksFMH99he9ZkubOmiJq',$,$,$,(#131),#460);
#460= IFCMATERIALPROFILESETUSAGE(#124,5,$);

#231= IFCRELASSOCIATESMATERIAL('2ESe9E$0X7he09QXubBhQG',$,$,$,(#132),#461);
#461= IFCMATERIALPROFILESETUSAGE(#462,$,$);
#462= IFCMATERIALPROFILESET($,$,(#463),$);
#463= IFCMATERIALPROFILE($,$,#464,#465,$,$);
#464= IFCMATERIAL('Southern Pine',$,$);
#465= IFCRECTANGLEPROFILEDEF(.AREA.,$,$,1.5,3.5);

#234= IFCRELASSOCIATESMATERIAL('3KZdhyB3H19wi3lQokeHYU',$,$,$,(#133),#466);
#466= IFCMATERIALPROFILESETUSAGE(#467,$,$);
#467= IFCMATERIALPROFILESET($,$,(#468),$);
#468= IFCMATERIALPROFILE($,$,#469,#470,$,$);
#469= IFCMATERIAL('Southern Pine',$,$);
#470= IFCRECTANGLEPROFILEDEF(.AREA.,$,$,1.5,3.5);

/* Opening element for a door to demonstrate framing. */
#409= IFCOPENINGELEMENT('04TbpZcR19gve3AHcfQNmN',$,$,$,$,#471,#472,$,$);
#471= IFCLOCALPLACEMENT(#153,#473);
#472= IFCPRODUCTDEFINITIONSHAPE($,$,(#474));
#473= IFCAXIS2PLACEMENT3D(#475,$,$);
#474= IFCSHAPEREPRESENTATION(#2,'Body','SweptSolid',(#476));
#475= IFCCARTESIANPOINT((24.,-2.25,0.));
#476= IFCEXTRUDEDAREASOLID(#477,#478,#479,1.5);
#477= IFCRECTANGLEPROFILEDEF(.AREA.,$,#480,32.,80.);
#478= IFCAXIS2PLACEMENT3D(#481,#482,#483);
#479= IFCDIRECTION((0.,0.,1.));
#480= IFCAXIS2PLACEMENT2D(#484,$);
#481= IFCCARTESIANPOINT((0.,0.,0.));
#482= IFCDIRECTION((0.,1.,0.));
#483= IFCDIRECTION((-1.,0.,0.));
#484= IFCCARTESIANPOINT((-16.,40.));

/* Opening element for a window to demonstrate framing. */
#410= IFCOPENINGELEMENT('01DJiSemH67OQX$HRpbAps',$,$,$,$,#485,#486,$,.OPENING.);
#485= IFCLOCALPLACEMENT(#153,#487);
#486= IFCPRODUCTDEFINITIONSHAPE($,$,(#488));
#487= IFCAXIS2PLACEMENT3D(#489,#490,#491);
#488= IFCSHAPEREPRESENTATION(#2,'Body','SweptSolid',(#492));
#489= IFCCARTESIANPOINT((84.,0.,32.));
#490= IFCDIRECTION((0.,-1.,0.));
#491= IFCDIRECTION((1.,0.,0.));
#492= IFCEXTRUDEDAREASOLID(#493,#494,#495,1.5);
#493= IFCRECTANGLEPROFILEDEF(.AREA.,$,$,36.,48.);
#494= IFCAXIS2PLACEMENT3D(#496,$,$);
#495= IFCDIRECTION((0.,0.,1.));
#496= IFCCARTESIANPOINT((18.,24.,-1.5));
ENDSEC;

END-ISO-10303-21;

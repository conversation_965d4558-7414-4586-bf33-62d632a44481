ISO-10303-21;
HEADER;
FILE_DESCRIPTION(('ViewDefinition [CoordinationView]'),'2;1');
FILE_NAME('','2020-10-30T00:19:00',(),(),'IfcOpenShell 0.6.0b0','IfcOpenShell 0.6.0b0','');
FILE_SCHEMA(('IFC4'));
ENDSEC;
DATA;
#1=IFCPROJECT('28hypXUBvBefc20SI8kfA$',#16,'Default Project','Description of Default Project',$,$,$,(#17),#31);
#2=IFCSITE('1cwlDi_hLEvPsClAelBNnz',#16,'Default Site','Description of Default Site',$,#43,$,$,.ELEMENT.,(24,28,0),(54,25,0),10.,$,$);
#3=IFCBUILDING('0AqAhXVxvCy9m0OX1nxY1A',#16,'Default Building','Description of Default Building',$,#46,$,$,.ELEMENT.,$,$,#52);
#4=IFCBUILDINGSTOREY('2GNgSHJ5j9BRUjqT$7tE8w',#16,'Default Building Storey','Description of Default Building Storey',$,#53,$,$,.ELEMENT.,0.);
#5=IFCWALLSTANDARDCASE('3ZYW59sxj8lei475l7EhLU',#16,'Wall for Test Example','Description of Wall',$,#62,#74,$,$);
#6=IFCPROPERTYSET('3nMqHLyZHAegWs5Yyxh1ry',#16,'Pset_WallCommon',$,(#75,#76,#77,#78,#79,#80));
#7=IFCOPENINGELEMENT('2bJiss68D6hvLKV8O1xmqJ',#16,'Opening Element for Test Example','Description of Opening',$,#81,#96,$,.OPENING.);
#8=IFCWINDOW('0tA4DSHd50le6Ov9Yu0I9X',#16,'Window for Test Example','Description of Window',$,#97,#115,$,1000.,1000.,.WINDOW.,.SINGLE_PANEL.,$);
#9=IFCWINDOWTYPE('0Ps4H3X0nAxfqkHNemLE6f',#16,'Window for Test Example','Description of Window Type',$,$,$,$,$,.WINDOW.,.SINGLE_PANEL.,$,$);
#10=IFCPROJECTLIBRARY('1SutvPaeH8EBtxrmG2k_Kh',#16,$,$,$,$,$,$,$);
#11=IFCPROPERTYSET('1lZ4zPst55PhFcUG69u5jM',#16,'Pset_WindowCommon',$,(#116,#117,#118,#119,#120));
#12=IFCSHAPEREPRESENTATION(#17,'Axis','Curve2D',(#121));
#13=IFCSHAPEREPRESENTATION(#17,'Body','SweptSolid',(#124));
#14=IFCSHAPEREPRESENTATION(#17,'Body','SweptSolid',(#135));
#15=IFCSHAPEREPRESENTATION(#17,'Body','SweptSolid',(#146));
#16=IFCOWNERHISTORY(#157,#160,$,.NOTDEFINED.,$,$,$,1323724715);
#17=IFCGEOMETRICREPRESENTATIONCONTEXT($,'Model',3,1.E-05,#162,#164);
#18=IFCRELAGGREGATES('1Lm3qeFdPFmvCQm$QtrkO_',#16,'BuildingContainer','BuildingContainer for BuildigStories',#3,(#4));
#19=IFCRELAGGREGATES('16zMrDm_P2fv4w8_JewkSy',#16,'SiteContainer','SiteContainer For Buildings',#2,(#3));
#20=IFCRELAGGREGATES('3IdcKtxyTFSPDjAagDGuOq',#16,'ProjectContainer','ProjectContainer for Sites',#1,(#2));
#21=IFCRELCONTAINEDINSPATIALSTRUCTURE('0w_L$jTK98v8wOzKFGjTuo',#16,'Default Building','Contents of Building Storey',(#5,#8),#4);
#22=IFCRELDEFINESBYPROPERTIES('29JB4VSyHEhx7go0x$VxZ2',#16,$,$,(#5),#6);
#23=IFCRELASSOCIATESMATERIAL('3DQ2_rihzBm8nLF98euhbs',#16,$,$,(#5),#165);
#24=IFCRELVOIDSELEMENT('1nwVYC$VTDeuSc8zbOa89u',#16,$,$,#5,#7);
#25=IFCRELASSOCIATESMATERIAL('2umeFbHwL6GAUKTaYomo7u',#16,$,$,(#8),#169);
#26=IFCRELDEFINESBYTYPE('33ZWGsuJ9CIw2bpc0Zk0Dl',#16,$,$,(#8),#9);
#27=IFCRELDECLARES('1PDtajAqHD6xd3QiWD$I1W',#16,$,$,#10,(#9));
#28=IFCRELDECLARES('3za3M6A5r4wQ3Luv68n$31',#16,$,$,#10,(#1));
#29=IFCRELFILLSELEMENT('0YVioT$0bDzPFxfmI$Sb2G',#16,$,$,#7,#8);
#30=IFCRELDEFINESBYPROPERTIES('17cvqmufjDQ9MHuuJCJrDN',#16,$,$,(#8),#11);
#31=IFCUNITASSIGNMENT((#32,#33,#34,#35,#38,#39,#40,#41,#42));
#32=IFCSIUNIT(*,.LENGTHUNIT.,.MILLI.,.METRE.);
#33=IFCSIUNIT(*,.AREAUNIT.,$,.SQUARE_METRE.);
#34=IFCSIUNIT(*,.VOLUMEUNIT.,$,.CUBIC_METRE.);
#35=IFCCONVERSIONBASEDUNIT($,.PLANEANGLEUNIT.,'DEGREE',#36);
#36=IFCMEASUREWITHUNIT(IFCPLANEANGLEMEASURE(0.01745),#37);
#37=IFCSIUNIT(*,.PLANEANGLEUNIT.,$,.RADIAN.);
#38=IFCSIUNIT(*,.SOLIDANGLEUNIT.,$,.STERADIAN.);
#39=IFCSIUNIT(*,.MASSUNIT.,$,.GRAM.);
#40=IFCSIUNIT(*,.TIMEUNIT.,$,.SECOND.);
#41=IFCSIUNIT(*,.THERMODYNAMICTEMPERATUREUNIT.,$,.DEGREE_CELSIUS.);
#42=IFCSIUNIT(*,.LUMINOUSINTENSITYUNIT.,$,.LUMEN.);
#43=IFCLOCALPLACEMENT($,#44);
#44=IFCAXIS2PLACEMENT3D(#45,$,$);
#45=IFCCARTESIANPOINT((0.,0.,0.));
#46=IFCLOCALPLACEMENT(#47,#50);
#47=IFCLOCALPLACEMENT($,#48);
#48=IFCAXIS2PLACEMENT3D(#49,$,$);
#49=IFCCARTESIANPOINT((0.,0.,0.));
#50=IFCAXIS2PLACEMENT3D(#51,$,$);
#51=IFCCARTESIANPOINT((0.,0.,0.));
#52=IFCPOSTALADDRESS($,$,$,$,('RDF Ltd.','Main Office'),'32','Bankya','Sofia','1320','Bulgaria');
#53=IFCLOCALPLACEMENT(#54,#60);
#54=IFCLOCALPLACEMENT(#55,#58);
#55=IFCLOCALPLACEMENT($,#56);
#56=IFCAXIS2PLACEMENT3D(#57,$,$);
#57=IFCCARTESIANPOINT((0.,0.,0.));
#58=IFCAXIS2PLACEMENT3D(#59,$,$);
#59=IFCCARTESIANPOINT((0.,0.,0.));
#60=IFCAXIS2PLACEMENT3D(#61,$,$);
#61=IFCCARTESIANPOINT((0.,0.,0.));
#62=IFCLOCALPLACEMENT(#63,#72);
#63=IFCLOCALPLACEMENT(#64,#70);
#64=IFCLOCALPLACEMENT(#65,#68);
#65=IFCLOCALPLACEMENT($,#66);
#66=IFCAXIS2PLACEMENT3D(#67,$,$);
#67=IFCCARTESIANPOINT((0.,0.,0.));
#68=IFCAXIS2PLACEMENT3D(#69,$,$);
#69=IFCCARTESIANPOINT((0.,0.,0.));
#70=IFCAXIS2PLACEMENT3D(#71,$,$);
#71=IFCCARTESIANPOINT((0.,0.,0.));
#72=IFCAXIS2PLACEMENT3D(#73,$,$);
#73=IFCCARTESIANPOINT((0.,0.,0.));
#74=IFCPRODUCTDEFINITIONSHAPE($,$,(#12,#13));
#75=IFCPROPERTYSINGLEVALUE('Combustible','Combustible',IFCBOOLEAN(.F.),$);
#76=IFCPROPERTYSINGLEVALUE('ThermalTransmittance','ThermalTransmittance',IFCTHERMALTRANSMITTANCEMEASURE(0.24),$);
#77=IFCPROPERTYSINGLEVALUE('IsExternal','IsExternal',IFCBOOLEAN(.T.),$);
#78=IFCPROPERTYSINGLEVALUE('ExtendToStructure','ExtendToStructure',IFCBOOLEAN(.F.),$);
#79=IFCPROPERTYSINGLEVALUE('LoadBearing','LoadBearing',IFCBOOLEAN(.F.),$);
#80=IFCPROPERTYSINGLEVALUE('Compartmentation','Compartmentation',IFCBOOLEAN(.F.),$);
#81=IFCLOCALPLACEMENT(#82,#94);
#82=IFCLOCALPLACEMENT(#83,#92);
#83=IFCLOCALPLACEMENT(#84,#90);
#84=IFCLOCALPLACEMENT(#85,#88);
#85=IFCLOCALPLACEMENT($,#86);
#86=IFCAXIS2PLACEMENT3D(#87,$,$);
#87=IFCCARTESIANPOINT((0.,0.,0.));
#88=IFCAXIS2PLACEMENT3D(#89,$,$);
#89=IFCCARTESIANPOINT((0.,0.,0.));
#90=IFCAXIS2PLACEMENT3D(#91,$,$);
#91=IFCCARTESIANPOINT((0.,0.,0.));
#92=IFCAXIS2PLACEMENT3D(#93,$,$);
#93=IFCCARTESIANPOINT((0.,0.,0.));
#94=IFCAXIS2PLACEMENT3D(#95,$,$);
#95=IFCCARTESIANPOINT((1000.,0.,500.));
#96=IFCPRODUCTDEFINITIONSHAPE($,$,(#14));
#97=IFCLOCALPLACEMENT(#98,#113);
#98=IFCLOCALPLACEMENT(#99,#111);
#99=IFCLOCALPLACEMENT(#100,#109);
#100=IFCLOCALPLACEMENT(#101,#107);
#101=IFCLOCALPLACEMENT(#102,#105);
#102=IFCLOCALPLACEMENT($,#103);
#103=IFCAXIS2PLACEMENT3D(#104,$,$);
#104=IFCCARTESIANPOINT((0.,0.,0.));
#105=IFCAXIS2PLACEMENT3D(#106,$,$);
#106=IFCCARTESIANPOINT((0.,0.,0.));
#107=IFCAXIS2PLACEMENT3D(#108,$,$);
#108=IFCCARTESIANPOINT((0.,0.,0.));
#109=IFCAXIS2PLACEMENT3D(#110,$,$);
#110=IFCCARTESIANPOINT((0.,0.,0.));
#111=IFCAXIS2PLACEMENT3D(#112,$,$);
#112=IFCCARTESIANPOINT((1000.,0.,500.));
#113=IFCAXIS2PLACEMENT3D(#114,$,$);
#114=IFCCARTESIANPOINT((0.,50.,0.));
#115=IFCPRODUCTDEFINITIONSHAPE($,$,(#15));
#116=IFCPROPERTYSINGLEVALUE('IsExternal','IsExternal',IFCBOOLEAN(.T.),$);
#117=IFCPROPERTYSINGLEVALUE('Infiltration','Infiltration',IFCVOLUMETRICFLOWRATEMEASURE(0.3),$);
#118=IFCPROPERTYSINGLEVALUE('ThermalTransmittance','ThermalTransmittance',IFCTHERMALTRANSMITTANCEMEASURE(0.24),$);
#119=IFCPROPERTYSINGLEVALUE('GlazingAreaFraction','GlazingAreaFraction',IFCPOSITIVERATIOMEASURE(0.7),$);
#120=IFCPROPERTYSINGLEVALUE('SmokeStop','SmokeStop',IFCBOOLEAN(.F.),$);
#121=IFCPOLYLINE((#122,#123));
#122=IFCCARTESIANPOINT((0.,150.));
#123=IFCCARTESIANPOINT((3000.,150.));
#124=IFCEXTRUDEDAREASOLID(#125,#132,#134,2000.);
#125=IFCARBITRARYCLOSEDPROFILEDEF(.AREA.,$,#126);
#126=IFCPOLYLINE((#127,#128,#129,#130,#131));
#127=IFCCARTESIANPOINT((0.,0.));
#128=IFCCARTESIANPOINT((0.,300.));
#129=IFCCARTESIANPOINT((3000.,300.));
#130=IFCCARTESIANPOINT((3000.,0.));
#131=IFCCARTESIANPOINT((0.,0.));
#132=IFCAXIS2PLACEMENT3D(#133,$,$);
#133=IFCCARTESIANPOINT((0.,0.,0.));
#134=IFCDIRECTION((0.,0.,1.));
#135=IFCEXTRUDEDAREASOLID(#136,#143,#145,1000.);
#136=IFCARBITRARYCLOSEDPROFILEDEF(.AREA.,$,#137);
#137=IFCPOLYLINE((#138,#139,#140,#141,#142));
#138=IFCCARTESIANPOINT((0.,0.));
#139=IFCCARTESIANPOINT((0.,300.));
#140=IFCCARTESIANPOINT((1000.,300.));
#141=IFCCARTESIANPOINT((1000.,0.));
#142=IFCCARTESIANPOINT((0.,0.));
#143=IFCAXIS2PLACEMENT3D(#144,$,$);
#144=IFCCARTESIANPOINT((0.,0.,0.));
#145=IFCDIRECTION((0.,0.,1.));
#146=IFCEXTRUDEDAREASOLID(#147,#154,#156,1000.);
#147=IFCARBITRARYCLOSEDPROFILEDEF(.AREA.,$,#148);
#148=IFCPOLYLINE((#149,#150,#151,#152,#153));
#149=IFCCARTESIANPOINT((0.,0.));
#150=IFCCARTESIANPOINT((0.,200.));
#151=IFCCARTESIANPOINT((1000.,200.));
#152=IFCCARTESIANPOINT((1000.,0.));
#153=IFCCARTESIANPOINT((0.,0.));
#154=IFCAXIS2PLACEMENT3D(#155,$,$);
#155=IFCCARTESIANPOINT((0.,0.,0.));
#156=IFCDIRECTION((0.,0.,1.));
#157=IFCPERSONANDORGANIZATION(#158,#159,$);
#158=IFCPERSON($,'Bonsma','Peter',$,$,$,$,$);
#159=IFCORGANIZATION($,'RDF','RDF Ltd.',$,$);
#160=IFCAPPLICATION(#161,'0.10','Test Application','TA 1001');
#161=IFCORGANIZATION($,'RDF','RDF Ltd.',$,$);
#162=IFCAXIS2PLACEMENT3D(#163,$,$);
#163=IFCCARTESIANPOINT((0.,0.,0.));
#164=IFCDIRECTION((0.,1.));
#165=IFCMATERIALLAYERSETUSAGE(#166,.AXIS2.,.POSITIVE.,-150.,$);
#166=IFCMATERIALLAYERSET((#167),$,$);
#167=IFCMATERIALLAYER(#168,300.,$,$,$,$,$);
#168=IFCMATERIAL('Name of the material used for the wall',$,$);
#169=IFCMATERIALCONSTITUENTSET('Constituent Set for Window',$,(#170,#172));
#170=IFCMATERIALCONSTITUENT('Framing',$,#171,$,$);
#171=IFCMATERIAL('Glass',$,$);
#172=IFCMATERIALCONSTITUENT('Framing',$,#173,$,$);
#173=IFCMATERIAL('Wood',$,$);
ENDSEC;
END-ISO-10303-21;

ISO-10303-21;
HEADER;
/* Generated by software containing ST-Developer
 * from STEP Tools, Inc. (www.steptools.com) 
 */

FILE_DESCRIPTION(
/* description */ ('ViewDefinition [ReferenceView_V1.2]',
'ExchangeRequirement [Architecture, Structural, BuildingService]',
'Comment [Comments]',
'Comment [System: OpenBuildings Designer*********** debug build Feb  5
 2020 00:34:15]'),
/* implementation_level */ '2;1');

FILE_NAME(
/* name */ '1.SimpleRectangle.facet',
/* time_stamp */ '2020-03-05T12:22:37+03:00',
/* author */ ('First Name Last Name'),
/* organization */ ('Organization Name'),
/* preprocessor_version */ 'ST-DEVELOPER v16.13',
/* originating_system */ 'OpenBuildings Designer***********',
/* authorisation */ 'Administrator');

FILE_SCHEMA (('IFC4'));
ENDSEC;

DATA;
#10=IFCBOUNDINGBOX(#1051,15.,15.0104204182341,3.11913433609218);
#11=IFCPRESENTATIONLAYERASSIGNMENT('Default',$,(#15),$);
#12=IFCGEOMETRICREPRESENTATIONSUBCONTEXT('Body','Model',*,*,*,*,#14,1.,
 .MODEL_VIEW.,$);
#13=IFCGEOMETRICREPRESENTATIONSUBCONTEXT('Box','Model',*,*,*,*,#14,1.,
 .SKETCH_VIEW.,$);
#14=IFCGEOMETRICREPRESENTATIONCONTEXT($,'Model',3,1.E-6,#1047,#1054);
#15=IFCSHAPEREPRESENTATION(#12,'Body','Tessellation',(#1035));
#16=IFCSHAPEREPRESENTATION(#13,'Box','BoundingBox',(#10));
#17=IFCPRODUCTDEFINITIONSHAPE($,$,(#15,#16));
#18=IFCRELCONTAINEDINSPATIALSTRUCTURE('2aJi0CW_wcTJthPGyCEMkK',#1117,$,
$,(#21),#19);
#19=IFCBUILDINGSTOREY('0rm7DUqMJhOUcs5v8t1F$L',#1117,'Floor 3',
'Administrative and employee lounge',$,#1041,$,'Floor 3',.ELEMENT.,9.144);
#20=IFCBUILDING('3_IkJInE13hR6Xwo4VKOgh',#1117,'Bldg 1',
'3 Story Building',$,#1040,$,'Bldg 1',.ELEMENT.,0.,0.,#1109);
#21=IFCBUILDINGELEMENTPROXY('1SkVtlXVM5QZmO_SLYA8R9',#1117,
'Element type: 24','1157*bspline surface!Default','',#1042,#17,
'1157*bspline surface!Default',.NOTDEFINED.);
#22=IFCSTYLEDITEM(#1035,(#23),$);
#23=IFCSURFACESTYLE($,.BOTH.,(#24));
#24=IFCSURFACESTYLESHADING(#25,0.);
#25=IFCCOLOURRGB($,1.,1.,1.);
#26=IFCINDEXEDPOLYGONALFACE((1,2,3));
#27=IFCINDEXEDPOLYGONALFACE((4,5,6));
#28=IFCINDEXEDPOLYGONALFACE((7,8,9));
#29=IFCINDEXEDPOLYGONALFACE((10,11,12));
#30=IFCINDEXEDPOLYGONALFACE((13,14,15));
#31=IFCINDEXEDPOLYGONALFACE((3,2,16));
#32=IFCINDEXEDPOLYGONALFACE((14,13,17));
#33=IFCINDEXEDPOLYGONALFACE((18,19,20));
#34=IFCINDEXEDPOLYGONALFACE((21,18,16));
#35=IFCINDEXEDPOLYGONALFACE((22,23,24));
#36=IFCINDEXEDPOLYGONALFACE((25,24,23));
#37=IFCINDEXEDPOLYGONALFACE((18,21,26));
#38=IFCINDEXEDPOLYGONALFACE((24,27,26));
#39=IFCINDEXEDPOLYGONALFACE((25,28,27));
#40=IFCINDEXEDPOLYGONALFACE((29,30,20));
#41=IFCINDEXEDPOLYGONALFACE((31,15,32));
#42=IFCINDEXEDPOLYGONALFACE((30,33,15));
#43=IFCINDEXEDPOLYGONALFACE((34,35,32));
#44=IFCINDEXEDPOLYGONALFACE((36,37,38));
#45=IFCINDEXEDPOLYGONALFACE((29,19,26));
#46=IFCINDEXEDPOLYGONALFACE((36,33,30));
#47=IFCINDEXEDPOLYGONALFACE((29,39,40));
#48=IFCINDEXEDPOLYGONALFACE((39,26,27));
#49=IFCINDEXEDPOLYGONALFACE((28,41,42));
#50=IFCINDEXEDPOLYGONALFACE((39,42,43));
#51=IFCINDEXEDPOLYGONALFACE((41,44,43));
#52=IFCINDEXEDPOLYGONALFACE((45,40,46));
#53=IFCINDEXEDPOLYGONALFACE((47,34,38));
#54=IFCINDEXEDPOLYGONALFACE((37,36,45));
#55=IFCINDEXEDPOLYGONALFACE((37,48,49));
#56=IFCINDEXEDPOLYGONALFACE((34,47,50));
#57=IFCINDEXEDPOLYGONALFACE((47,49,51));
#58=IFCINDEXEDPOLYGONALFACE((49,48,46));
#59=IFCINDEXEDPOLYGONALFACE((40,43,52));
#60=IFCINDEXEDPOLYGONALFACE((43,44,53));
#61=IFCINDEXEDPOLYGONALFACE((54,46,52));
#62=IFCINDEXEDPOLYGONALFACE((54,55,51));
#63=IFCINDEXEDPOLYGONALFACE((56,57,55));
#64=IFCINDEXEDPOLYGONALFACE((58,59,52));
#65=IFCINDEXEDPOLYGONALFACE((60,56,59));
#66=IFCINDEXEDPOLYGONALFACE((60,61,62));
#67=IFCINDEXEDPOLYGONALFACE((58,63,61));
#68=IFCINDEXEDPOLYGONALFACE((64,65,66));
#69=IFCINDEXEDPOLYGONALFACE((67,68,69));
#70=IFCINDEXEDPOLYGONALFACE((70,71,72));
#71=IFCINDEXEDPOLYGONALFACE((6,67,73));
#72=IFCINDEXEDPOLYGONALFACE((70,74,75));
#73=IFCINDEXEDPOLYGONALFACE((76,75,74));
#74=IFCINDEXEDPOLYGONALFACE((6,5,76));
#75=IFCINDEXEDPOLYGONALFACE((77,78,74));
#76=IFCINDEXEDPOLYGONALFACE((79,74,78));
#77=IFCINDEXEDPOLYGONALFACE((79,68,67));
#78=IFCINDEXEDPOLYGONALFACE((65,80,81));
#79=IFCINDEXEDPOLYGONALFACE((82,83,77));
#80=IFCINDEXEDPOLYGONALFACE((69,84,85));
#81=IFCINDEXEDPOLYGONALFACE((83,86,84));
#82=IFCINDEXEDPOLYGONALFACE((84,69,68));
#83=IFCINDEXEDPOLYGONALFACE((87,85,88));
#84=IFCINDEXEDPOLYGONALFACE((89,90,91));
#85=IFCINDEXEDPOLYGONALFACE((92,93,94));
#86=IFCINDEXEDPOLYGONALFACE((95,96,97));
#87=IFCINDEXEDPOLYGONALFACE((89,98,94));
#88=IFCINDEXEDPOLYGONALFACE((99,97,90));
#89=IFCINDEXEDPOLYGONALFACE((100,101,102));
#90=IFCINDEXEDPOLYGONALFACE((103,104,105));
#91=IFCINDEXEDPOLYGONALFACE((95,99,102));
#92=IFCINDEXEDPOLYGONALFACE((106,105,96));
#93=IFCINDEXEDPOLYGONALFACE((106,101,107));
#94=IFCINDEXEDPOLYGONALFACE((104,103,108));
#95=IFCINDEXEDPOLYGONALFACE((103,107,109));
#96=IFCINDEXEDPOLYGONALFACE((101,100,110));
#97=IFCINDEXEDPOLYGONALFACE((110,100,111));
#98=IFCINDEXEDPOLYGONALFACE((107,110,112));
#99=IFCINDEXEDPOLYGONALFACE((111,93,92));
#100=IFCINDEXEDPOLYGONALFACE((113,114,92));
#101=IFCINDEXEDPOLYGONALFACE((115,116,117));
#102=IFCINDEXEDPOLYGONALFACE((82,81,80));
#103=IFCINDEXEDPOLYGONALFACE((83,82,115));
#104=IFCINDEXEDPOLYGONALFACE((118,116,115));
#105=IFCINDEXEDPOLYGONALFACE((88,117,119));
#106=IFCINDEXEDPOLYGONALFACE((118,80,120));
#107=IFCINDEXEDPOLYGONALFACE((121,122,17));
#108=IFCINDEXEDPOLYGONALFACE((64,120,80));
#109=IFCINDEXEDPOLYGONALFACE((120,64,17));
#110=IFCINDEXEDPOLYGONALFACE((13,31,123));
#111=IFCINDEXEDPOLYGONALFACE((123,124,117));
#112=IFCINDEXEDPOLYGONALFACE((124,125,119));
#113=IFCINDEXEDPOLYGONALFACE((124,123,31));
#114=IFCINDEXEDPOLYGONALFACE((126,127,128));
#115=IFCINDEXEDPOLYGONALFACE((129,130,131));
#116=IFCINDEXEDPOLYGONALFACE((132,133,134));
#117=IFCINDEXEDPOLYGONALFACE((135,136,137));
#118=IFCINDEXEDPOLYGONALFACE((138,139,140));
#119=IFCINDEXEDPOLYGONALFACE((141,142,143));
#120=IFCINDEXEDPOLYGONALFACE((144,145,146));
#121=IFCINDEXEDPOLYGONALFACE((147,148,149));
#122=IFCINDEXEDPOLYGONALFACE((150,151,152));
#123=IFCINDEXEDPOLYGONALFACE((153,154,155));
#124=IFCINDEXEDPOLYGONALFACE((156,10,157));
#125=IFCINDEXEDPOLYGONALFACE((158,152,11));
#126=IFCINDEXEDPOLYGONALFACE((159,158,10));
#127=IFCINDEXEDPOLYGONALFACE((154,156,160));
#128=IFCINDEXEDPOLYGONALFACE((161,162,156));
#129=IFCINDEXEDPOLYGONALFACE((163,159,162));
#130=IFCINDEXEDPOLYGONALFACE((164,165,166));
#131=IFCINDEXEDPOLYGONALFACE((167,168,169));
#132=IFCINDEXEDPOLYGONALFACE((161,154,153));
#133=IFCINDEXEDPOLYGONALFACE((170,171,153));
#134=IFCINDEXEDPOLYGONALFACE((172,170,168));
#135=IFCINDEXEDPOLYGONALFACE((173,172,167));
#136=IFCINDEXEDPOLYGONALFACE((174,175,176));
#137=IFCINDEXEDPOLYGONALFACE((177,173,165));
#138=IFCINDEXEDPOLYGONALFACE((178,177,164));
#139=IFCINDEXEDPOLYGONALFACE((179,178,175));
#140=IFCINDEXEDPOLYGONALFACE((180,181,179));
#141=IFCINDEXEDPOLYGONALFACE((171,170,182));
#142=IFCINDEXEDPOLYGONALFACE((146,145,151));
#143=IFCINDEXEDPOLYGONALFACE((183,184,158));
#144=IFCINDEXEDPOLYGONALFACE((185,150,184));
#145=IFCINDEXEDPOLYGONALFACE((186,183,159));
#146=IFCINDEXEDPOLYGONALFACE((187,185,183));
#147=IFCINDEXEDPOLYGONALFACE((188,189,150));
#148=IFCINDEXEDPOLYGONALFACE((188,190,191));
#149=IFCINDEXEDPOLYGONALFACE((190,188,185));
#150=IFCINDEXEDPOLYGONALFACE((192,190,187));
#151=IFCINDEXEDPOLYGONALFACE((177,178,193));
#152=IFCINDEXEDPOLYGONALFACE((194,195,186));
#153=IFCINDEXEDPOLYGONALFACE((172,173,196));
#154=IFCINDEXEDPOLYGONALFACE((197,186,198));
#155=IFCINDEXEDPOLYGONALFACE((199,197,182));
#156=IFCINDEXEDPOLYGONALFACE((200,194,197));
#157=IFCINDEXEDPOLYGONALFACE((201,187,195));
#158=IFCINDEXEDPOLYGONALFACE((202,201,194));
#159=IFCINDEXEDPOLYGONALFACE((199,203,204));
#160=IFCINDEXEDPOLYGONALFACE((179,181,205));
#161=IFCINDEXEDPOLYGONALFACE((196,193,203));
#162=IFCINDEXEDPOLYGONALFACE((206,204,203));
#163=IFCINDEXEDPOLYGONALFACE((200,204,207));
#164=IFCINDEXEDPOLYGONALFACE((202,207,208));
#165=IFCINDEXEDPOLYGONALFACE((209,191,190));
#166=IFCINDEXEDPOLYGONALFACE((210,211,201));
#167=IFCINDEXEDPOLYGONALFACE((212,192,211));
#168=IFCINDEXEDPOLYGONALFACE((213,209,192));
#169=IFCINDEXEDPOLYGONALFACE((212,214,215));
#170=IFCINDEXEDPOLYGONALFACE((207,206,216));
#171=IFCINDEXEDPOLYGONALFACE((210,208,214));
#172=IFCINDEXEDPOLYGONALFACE((208,216,217));
#173=IFCINDEXEDPOLYGONALFACE((214,217,218));
#174=IFCINDEXEDPOLYGONALFACE((219,205,181));
#175=IFCINDEXEDPOLYGONALFACE((220,221,222));
#176=IFCINDEXEDPOLYGONALFACE((223,180,148));
#177=IFCINDEXEDPOLYGONALFACE((221,220,147));
#178=IFCINDEXEDPOLYGONALFACE((223,147,220));
#179=IFCINDEXEDPOLYGONALFACE((224,181,180));
#180=IFCINDEXEDPOLYGONALFACE((225,226,227));
#181=IFCINDEXEDPOLYGONALFACE((228,225,229));
#182=IFCINDEXEDPOLYGONALFACE((230,226,225));
#183=IFCINDEXEDPOLYGONALFACE((231,232,227));
#184=IFCINDEXEDPOLYGONALFACE((206,233,234));
#185=IFCINDEXEDPOLYGONALFACE((235,236,205));
#186=IFCINDEXEDPOLYGONALFACE((232,219,224));
#187=IFCINDEXEDPOLYGONALFACE((219,232,237));
#188=IFCINDEXEDPOLYGONALFACE((236,235,233));
#189=IFCINDEXEDPOLYGONALFACE((217,238,239));
#190=IFCINDEXEDPOLYGONALFACE((233,237,240));
#191=IFCINDEXEDPOLYGONALFACE((216,234,238));
#192=IFCINDEXEDPOLYGONALFACE((238,240,241));
#193=IFCINDEXEDPOLYGONALFACE((242,240,237));
#194=IFCINDEXEDPOLYGONALFACE((243,237,232));
#195=IFCINDEXEDPOLYGONALFACE((244,231,226));
#196=IFCINDEXEDPOLYGONALFACE((245,246,231));
#197=IFCINDEXEDPOLYGONALFACE((247,243,246));
#198=IFCINDEXEDPOLYGONALFACE((240,242,248));
#199=IFCINDEXEDPOLYGONALFACE((243,247,249));
#200=IFCINDEXEDPOLYGONALFACE((242,249,250));
#201=IFCINDEXEDPOLYGONALFACE((239,241,251));
#202=IFCINDEXEDPOLYGONALFACE((252,253,140));
#203=IFCINDEXEDPOLYGONALFACE((254,255,191));
#204=IFCINDEXEDPOLYGONALFACE((256,144,257));
#205=IFCINDEXEDPOLYGONALFACE((258,257,146));
#206=IFCINDEXEDPOLYGONALFACE((253,252,257));
#207=IFCINDEXEDPOLYGONALFACE((259,253,258));
#208=IFCINDEXEDPOLYGONALFACE((213,215,260));
#209=IFCINDEXEDPOLYGONALFACE((254,261,262));
#210=IFCINDEXEDPOLYGONALFACE((261,254,209));
#211=IFCINDEXEDPOLYGONALFACE((260,263,264));
#212=IFCINDEXEDPOLYGONALFACE((215,218,263));
#213=IFCINDEXEDPOLYGONALFACE((262,264,265));
#214=IFCINDEXEDPOLYGONALFACE((259,265,266));
#215=IFCINDEXEDPOLYGONALFACE((265,267,268));
#216=IFCINDEXEDPOLYGONALFACE((269,270,267));
#217=IFCINDEXEDPOLYGONALFACE((248,250,271));
#218=IFCINDEXEDPOLYGONALFACE((263,251,269));
#219=IFCINDEXEDPOLYGONALFACE((251,271,270));
#220=IFCINDEXEDPOLYGONALFACE((250,272,273));
#221=IFCINDEXEDPOLYGONALFACE((274,275,276));
#222=IFCINDEXEDPOLYGONALFACE((277,278,230));
#223=IFCINDEXEDPOLYGONALFACE((279,280,281));
#224=IFCINDEXEDPOLYGONALFACE((282,228,143));
#225=IFCINDEXEDPOLYGONALFACE((141,283,281));
#226=IFCINDEXEDPOLYGONALFACE((284,142,141));
#227=IFCINDEXEDPOLYGONALFACE((285,230,228));
#228=IFCINDEXEDPOLYGONALFACE((286,282,142));
#229=IFCINDEXEDPOLYGONALFACE((287,288,282));
#230=IFCINDEXEDPOLYGONALFACE((289,285,288));
#231=IFCINDEXEDPOLYGONALFACE((290,286,284));
#232=IFCINDEXEDPOLYGONALFACE((291,279,292));
#233=IFCINDEXEDPOLYGONALFACE((293,284,280));
#234=IFCINDEXEDPOLYGONALFACE((294,295,279));
#235=IFCINDEXEDPOLYGONALFACE((296,297,294));
#236=IFCINDEXEDPOLYGONALFACE((298,299,295));
#237=IFCINDEXEDPOLYGONALFACE((287,286,290));
#238=IFCINDEXEDPOLYGONALFACE((299,290,293));
#239=IFCINDEXEDPOLYGONALFACE((300,301,290));
#240=IFCINDEXEDPOLYGONALFACE((302,300,299));
#241=IFCINDEXEDPOLYGONALFACE((303,298,294));
#242=IFCINDEXEDPOLYGONALFACE((304,302,298));
#243=IFCINDEXEDPOLYGONALFACE((301,300,305));
#244=IFCINDEXEDPOLYGONALFACE((247,306,307));
#245=IFCINDEXEDPOLYGONALFACE((308,245,244));
#246=IFCINDEXEDPOLYGONALFACE((309,277,285));
#247=IFCINDEXEDPOLYGONALFACE((310,244,278));
#248=IFCINDEXEDPOLYGONALFACE((311,310,277));
#249=IFCINDEXEDPOLYGONALFACE((310,311,312));
#250=IFCINDEXEDPOLYGONALFACE((245,308,306));
#251=IFCINDEXEDPOLYGONALFACE((307,313,272));
#252=IFCINDEXEDPOLYGONALFACE((306,312,313));
#253=IFCINDEXEDPOLYGONALFACE((313,312,314));
#254=IFCINDEXEDPOLYGONALFACE((311,315,314));
#255=IFCINDEXEDPOLYGONALFACE((302,304,316));
#256=IFCINDEXEDPOLYGONALFACE((309,305,315));
#257=IFCINDEXEDPOLYGONALFACE((315,316,317));
#258=IFCINDEXEDPOLYGONALFACE((314,317,318));
#259=IFCINDEXEDPOLYGONALFACE((316,304,319));
#260=IFCINDEXEDPOLYGONALFACE((320,321,322));
#261=IFCINDEXEDPOLYGONALFACE((323,303,297));
#262=IFCINDEXEDPOLYGONALFACE((324,296,325));
#263=IFCINDEXEDPOLYGONALFACE((274,326,322));
#264=IFCINDEXEDPOLYGONALFACE((327,325,275));
#265=IFCINDEXEDPOLYGONALFACE((324,327,321));
#266=IFCINDEXEDPOLYGONALFACE((328,297,296));
#267=IFCINDEXEDPOLYGONALFACE((303,323,319));
#268=IFCINDEXEDPOLYGONALFACE((328,329,330));
#269=IFCINDEXEDPOLYGONALFACE((323,330,331));
#270=IFCINDEXEDPOLYGONALFACE((330,329,332));
#271=IFCINDEXEDPOLYGONALFACE((136,320,333));
#272=IFCINDEXEDPOLYGONALFACE((332,329,321));
#273=IFCINDEXEDPOLYGONALFACE((320,136,334));
#274=IFCINDEXEDPOLYGONALFACE((332,334,335));
#275=IFCINDEXEDPOLYGONALFACE((336,335,337));
#276=IFCINDEXEDPOLYGONALFACE((338,339,340));
#277=IFCINDEXEDPOLYGONALFACE((319,331,339));
#278=IFCINDEXEDPOLYGONALFACE((340,337,341));
#279=IFCINDEXEDPOLYGONALFACE((267,342,343));
#280=IFCINDEXEDPOLYGONALFACE((273,344,342));
#281=IFCINDEXEDPOLYGONALFACE((345,318,344));
#282=IFCINDEXEDPOLYGONALFACE((343,341,346));
#283=IFCINDEXEDPOLYGONALFACE((347,348,349));
#284=IFCINDEXEDPOLYGONALFACE((266,268,348));
#285=IFCINDEXEDPOLYGONALFACE((347,350,140));
#286=IFCINDEXEDPOLYGONALFACE((351,352,353));
#287=IFCINDEXEDPOLYGONALFACE((354,355,130));
#288=IFCINDEXEDPOLYGONALFACE((356,357,351));
#289=IFCINDEXEDPOLYGONALFACE((358,359,355));
#290=IFCINDEXEDPOLYGONALFACE((335,360,356));
#291=IFCINDEXEDPOLYGONALFACE((361,358,360));
#292=IFCINDEXEDPOLYGONALFACE((135,362,359));
#293=IFCINDEXEDPOLYGONALFACE((136,135,361));
#294=IFCINDEXEDPOLYGONALFACE((360,354,357));
#295=IFCINDEXEDPOLYGONALFACE((363,346,353));
#296=IFCINDEXEDPOLYGONALFACE((127,126,364));
#297=IFCINDEXEDPOLYGONALFACE((365,349,346));
#298=IFCINDEXEDPOLYGONALFACE((133,349,365));
#299=IFCINDEXEDPOLYGONALFACE((366,365,363));
#300=IFCINDEXEDPOLYGONALFACE((366,367,134));
#301=IFCINDEXEDPOLYGONALFACE((368,369,370));
#302=IFCINDEXEDPOLYGONALFACE((371,372,8));
#303=IFCINDEXEDPOLYGONALFACE((373,374,375));
#304=IFCINDEXEDPOLYGONALFACE((376,131,372));
#305=IFCINDEXEDPOLYGONALFACE((376,373,352));
#306=IFCINDEXEDPOLYGONALFACE((373,376,371));
#307=IFCINDEXEDPOLYGONALFACE((375,374,377));
#308=IFCINDEXEDPOLYGONALFACE((374,371,7));
#309=IFCINDEXEDPOLYGONALFACE((378,379,368));
#310=IFCINDEXEDPOLYGONALFACE((380,381,369));
#311=IFCINDEXEDPOLYGONALFACE((382,383,384));
#312=IFCINDEXEDPOLYGONALFACE((4,385,386));
#313=IFCINDEXEDPOLYGONALFACE((387,388,389));
#314=IFCINDEXEDPOLYGONALFACE((385,390,128));
#315=IFCINDEXEDPOLYGONALFACE((391,366,388));
#316=IFCINDEXEDPOLYGONALFACE((367,366,391));
#317=IFCINDEXEDPOLYGONALFACE((392,128,127));
#318=IFCINDEXEDPOLYGONALFACE((387,393,394));
#319=IFCINDEXEDPOLYGONALFACE((395,392,391));
#320=IFCINDEXEDPOLYGONALFACE((395,386,128));
#321=IFCINDEXEDPOLYGONALFACE((384,396,389));
#322=IFCINDEXEDPOLYGONALFACE((396,72,71));
#323=IFCINDEXEDPOLYGONALFACE((393,71,75));
#324=IFCINDEXEDPOLYGONALFACE((76,395,394));
#325=IFCINDEXEDPOLYGONALFACE((386,395,76));
#326=IFCINDEXEDPOLYGONALFACE((396,384,66));
#327=IFCINDEXEDPOLYGONALFACE((121,66,384));
#328=IFCINDEXEDPOLYGONALFACE((397,398,382));
#329=IFCINDEXEDPOLYGONALFACE((399,383,382));
#330=IFCINDEXEDPOLYGONALFACE((383,399,122));
#331=IFCINDEXEDPOLYGONALFACE((400,401,402));
#332=IFCINDEXEDPOLYGONALFACE((403,397,381));
#333=IFCINDEXEDPOLYGONALFACE((402,404,405));
#334=IFCINDEXEDPOLYGONALFACE((406,379,378));
#335=IFCINDEXEDPOLYGONALFACE((378,377,407));
#336=IFCINDEXEDPOLYGONALFACE((380,379,406));
#337=IFCINDEXEDPOLYGONALFACE((381,380,408));
#338=IFCINDEXEDPOLYGONALFACE((408,406,409));
#339=IFCINDEXEDPOLYGONALFACE((410,411,408));
#340=IFCINDEXEDPOLYGONALFACE((412,409,413));
#341=IFCINDEXEDPOLYGONALFACE((414,413,415));
#342=IFCINDEXEDPOLYGONALFACE((404,416,407));
#343=IFCINDEXEDPOLYGONALFACE((417,418,414));
#344=IFCINDEXEDPOLYGONALFACE((419,420,409));
#345=IFCINDEXEDPOLYGONALFACE((419,421,422));
#346=IFCINDEXEDPOLYGONALFACE((412,418,421));
#347=IFCINDEXEDPOLYGONALFACE((417,423,424));
#348=IFCINDEXEDPOLYGONALFACE((423,417,425));
#349=IFCINDEXEDPOLYGONALFACE((422,421,424));
#350=IFCINDEXEDPOLYGONALFACE((426,410,422));
#351=IFCINDEXEDPOLYGONALFACE((427,1,122));
#352=IFCINDEXEDPOLYGONALFACE((428,426,427));
#353=IFCINDEXEDPOLYGONALFACE((403,411,410));
#354=IFCINDEXEDPOLYGONALFACE((397,403,428));
#355=IFCINDEXEDPOLYGONALFACE((429,430,2));
#356=IFCINDEXEDPOLYGONALFACE((431,422,432));
#357=IFCINDEXEDPOLYGONALFACE((431,429,427));
#358=IFCINDEXEDPOLYGONALFACE((433,434,430));
#359=IFCINDEXEDPOLYGONALFACE((435,436,437));
#360=IFCINDEXEDPOLYGONALFACE((438,439,423));
#361=IFCINDEXEDPOLYGONALFACE((440,435,441));
#362=IFCINDEXEDPOLYGONALFACE((442,415,416));
#363=IFCINDEXEDPOLYGONALFACE((443,444,416));
#364=IFCINDEXEDPOLYGONALFACE((404,402,401));
#365=IFCINDEXEDPOLYGONALFACE((442,445,446));
#366=IFCINDEXEDPOLYGONALFACE((447,445,442));
#367=IFCINDEXEDPOLYGONALFACE((448,444,449));
#368=IFCINDEXEDPOLYGONALFACE((450,451,443));
#369=IFCINDEXEDPOLYGONALFACE((452,401,453));
#370=IFCINDEXEDPOLYGONALFACE((401,400,441));
#371=IFCINDEXEDPOLYGONALFACE((451,449,444));
#372=IFCINDEXEDPOLYGONALFACE((454,455,456));
#373=IFCINDEXEDPOLYGONALFACE((455,454,448));
#374=IFCINDEXEDPOLYGONALFACE((455,457,458));
#375=IFCINDEXEDPOLYGONALFACE((459,447,456));
#376=IFCINDEXEDPOLYGONALFACE((439,460,433));
#377=IFCINDEXEDPOLYGONALFACE((445,447,459));
#378=IFCINDEXEDPOLYGONALFACE((459,460,439));
#379=IFCINDEXEDPOLYGONALFACE((461,462,463));
#380=IFCINDEXEDPOLYGONALFACE((456,458,464));
#381=IFCINDEXEDPOLYGONALFACE((465,461,460));
#382=IFCINDEXEDPOLYGONALFACE((461,465,464));
#383=IFCINDEXEDPOLYGONALFACE((466,467,468));
#384=IFCINDEXEDPOLYGONALFACE((469,470,471));
#385=IFCINDEXEDPOLYGONALFACE((472,473,474));
#386=IFCINDEXEDPOLYGONALFACE((469,437,436));
#387=IFCINDEXEDPOLYGONALFACE((475,451,450));
#388=IFCINDEXEDPOLYGONALFACE((450,453,476));
#389=IFCINDEXEDPOLYGONALFACE((441,437,476));
#390=IFCINDEXEDPOLYGONALFACE((477,478,449));
#391=IFCINDEXEDPOLYGONALFACE((477,475,479));
#392=IFCINDEXEDPOLYGONALFACE((478,477,480));
#393=IFCINDEXEDPOLYGONALFACE((481,479,482));
#394=IFCINDEXEDPOLYGONALFACE((483,482,476));
#395=IFCINDEXEDPOLYGONALFACE((484,476,437));
#396=IFCINDEXEDPOLYGONALFACE((485,486,483));
#397=IFCINDEXEDPOLYGONALFACE((480,479,481));
#398=IFCINDEXEDPOLYGONALFACE((487,488,481));
#399=IFCINDEXEDPOLYGONALFACE((489,487,486));
#400=IFCINDEXEDPOLYGONALFACE((490,489,485));
#401=IFCINDEXEDPOLYGONALFACE((491,492,488));
#402=IFCINDEXEDPOLYGONALFACE((457,493,466));
#403=IFCINDEXEDPOLYGONALFACE((473,494,493));
#404=IFCINDEXEDPOLYGONALFACE((472,478,492));
#405=IFCINDEXEDPOLYGONALFACE((494,473,472));
#406=IFCINDEXEDPOLYGONALFACE((495,496,467));
#407=IFCINDEXEDPOLYGONALFACE((489,490,497));
#408=IFCINDEXEDPOLYGONALFACE((498,499,495));
#409=IFCINDEXEDPOLYGONALFACE((487,499,498));
#410=IFCINDEXEDPOLYGONALFACE((497,496,495));
#411=IFCINDEXEDPOLYGONALFACE((497,490,500));
#412=IFCINDEXEDPOLYGONALFACE((501,502,471));
#413=IFCINDEXEDPOLYGONALFACE((503,504,505));
#414=IFCINDEXEDPOLYGONALFACE((504,503,484));
#415=IFCINDEXEDPOLYGONALFACE((506,505,500));
#416=IFCINDEXEDPOLYGONALFACE((505,504,507));
#417=IFCINDEXEDPOLYGONALFACE((508,509,501));
#418=IFCINDEXEDPOLYGONALFACE((507,504,502));
#419=IFCINDEXEDPOLYGONALFACE((510,507,501));
#420=IFCINDEXEDPOLYGONALFACE((509,508,511));
#421=IFCINDEXEDPOLYGONALFACE((512,513,507));
#422=IFCINDEXEDPOLYGONALFACE((512,510,514));
#423=IFCINDEXEDPOLYGONALFACE((515,500,513));
#424=IFCINDEXEDPOLYGONALFACE((516,517,467));
#425=IFCINDEXEDPOLYGONALFACE((496,497,515));
#426=IFCINDEXEDPOLYGONALFACE((516,518,519));
#427=IFCINDEXEDPOLYGONALFACE((520,521,519));
#428=IFCINDEXEDPOLYGONALFACE((520,512,522));
#429=IFCINDEXEDPOLYGONALFACE((523,524,521));
#430=IFCINDEXEDPOLYGONALFACE((525,519,521));
#431=IFCINDEXEDPOLYGONALFACE((517,516,525));
#432=IFCINDEXEDPOLYGONALFACE((524,526,527));
#433=IFCINDEXEDPOLYGONALFACE((517,528,468));
#434=IFCINDEXEDPOLYGONALFACE((529,530,463));
#435=IFCINDEXEDPOLYGONALFACE((531,530,529));
#436=IFCINDEXEDPOLYGONALFACE((528,517,532));
#437=IFCINDEXEDPOLYGONALFACE((528,533,529));
#438=IFCINDEXEDPOLYGONALFACE((527,526,534));
#439=IFCINDEXEDPOLYGONALFACE((533,535,536));
#440=IFCINDEXEDPOLYGONALFACE((537,536,535));
#441=IFCINDEXEDPOLYGONALFACE((530,538,539));
#442=IFCINDEXEDPOLYGONALFACE((434,539,16));
#443=IFCINDEXEDPOLYGONALFACE((463,539,434));
#444=IFCINDEXEDPOLYGONALFACE((22,16,539));
#445=IFCINDEXEDPOLYGONALFACE((530,531,540));
#446=IFCINDEXEDPOLYGONALFACE((536,537,541));
#447=IFCINDEXEDPOLYGONALFACE((538,540,23));
#448=IFCINDEXEDPOLYGONALFACE((542,23,540));
#449=IFCINDEXEDPOLYGONALFACE((540,541,542));
#450=IFCINDEXEDPOLYGONALFACE((23,22,538));
#451=IFCINDEXEDPOLYGONALFACE((540,531,536));
#452=IFCINDEXEDPOLYGONALFACE((541,540,536));
#453=IFCINDEXEDPOLYGONALFACE((540,538,530));
#454=IFCINDEXEDPOLYGONALFACE((539,538,22));
#455=IFCINDEXEDPOLYGONALFACE((434,433,463));
#456=IFCINDEXEDPOLYGONALFACE((2,430,434));
#457=IFCINDEXEDPOLYGONALFACE((16,2,434));
#458=IFCINDEXEDPOLYGONALFACE((539,463,530));
#459=IFCINDEXEDPOLYGONALFACE((535,534,537));
#460=IFCINDEXEDPOLYGONALFACE((536,531,533));
#461=IFCINDEXEDPOLYGONALFACE((535,532,527));
#462=IFCINDEXEDPOLYGONALFACE((534,535,527));
#463=IFCINDEXEDPOLYGONALFACE((529,468,528));
#464=IFCINDEXEDPOLYGONALFACE((535,533,528));
#465=IFCINDEXEDPOLYGONALFACE((532,535,528));
#466=IFCINDEXEDPOLYGONALFACE((529,533,531));
#467=IFCINDEXEDPOLYGONALFACE((463,462,529));
#468=IFCINDEXEDPOLYGONALFACE((468,467,517));
#469=IFCINDEXEDPOLYGONALFACE((527,521,524));
#470=IFCINDEXEDPOLYGONALFACE((525,532,517));
#471=IFCINDEXEDPOLYGONALFACE((527,532,525));
#472=IFCINDEXEDPOLYGONALFACE((521,527,525));
#473=IFCINDEXEDPOLYGONALFACE((521,520,523));
#474=IFCINDEXEDPOLYGONALFACE((522,523,520));
#475=IFCINDEXEDPOLYGONALFACE((520,519,513));
#476=IFCINDEXEDPOLYGONALFACE((513,512,520));
#477=IFCINDEXEDPOLYGONALFACE((519,525,516));
#478=IFCINDEXEDPOLYGONALFACE((515,518,496));
#479=IFCINDEXEDPOLYGONALFACE((516,467,496));
#480=IFCINDEXEDPOLYGONALFACE((496,518,516));
#481=IFCINDEXEDPOLYGONALFACE((515,513,519));
#482=IFCINDEXEDPOLYGONALFACE((519,518,515));
#483=IFCINDEXEDPOLYGONALFACE((514,522,512));
#484=IFCINDEXEDPOLYGONALFACE((507,510,512));
#485=IFCINDEXEDPOLYGONALFACE((511,543,509));
#486=IFCINDEXEDPOLYGONALFACE((543,514,510));
#487=IFCINDEXEDPOLYGONALFACE((510,509,543));
#488=IFCINDEXEDPOLYGONALFACE((501,509,510));
#489=IFCINDEXEDPOLYGONALFACE((502,501,507));
#490=IFCINDEXEDPOLYGONALFACE((501,544,508));
#491=IFCINDEXEDPOLYGONALFACE((513,500,505));
#492=IFCINDEXEDPOLYGONALFACE((507,513,505));
#493=IFCINDEXEDPOLYGONALFACE((500,490,506));
#494=IFCINDEXEDPOLYGONALFACE((484,502,504));
#495=IFCINDEXEDPOLYGONALFACE((505,506,503));
#496=IFCINDEXEDPOLYGONALFACE((471,544,501));
#497=IFCINDEXEDPOLYGONALFACE((500,515,497));
#498=IFCINDEXEDPOLYGONALFACE((495,499,497));
#499=IFCINDEXEDPOLYGONALFACE((498,488,487));
#500=IFCINDEXEDPOLYGONALFACE((493,494,498));
#501=IFCINDEXEDPOLYGONALFACE((495,493,498));
#502=IFCINDEXEDPOLYGONALFACE((499,487,489));
#503=IFCINDEXEDPOLYGONALFACE((497,499,489));
#504=IFCINDEXEDPOLYGONALFACE((466,493,495));
#505=IFCINDEXEDPOLYGONALFACE((467,466,495));
#506=IFCINDEXEDPOLYGONALFACE((472,491,494));
#507=IFCINDEXEDPOLYGONALFACE((492,491,472));
#508=IFCINDEXEDPOLYGONALFACE((473,493,457));
#509=IFCINDEXEDPOLYGONALFACE((457,474,473));
#510=IFCINDEXEDPOLYGONALFACE((466,458,457));
#511=IFCINDEXEDPOLYGONALFACE((498,494,491));
#512=IFCINDEXEDPOLYGONALFACE((488,498,491));
#513=IFCINDEXEDPOLYGONALFACE((485,506,490));
#514=IFCINDEXEDPOLYGONALFACE((486,485,489));
#515=IFCINDEXEDPOLYGONALFACE((481,486,487));
#516=IFCINDEXEDPOLYGONALFACE((488,492,480));
#517=IFCINDEXEDPOLYGONALFACE((481,488,480));
#518=IFCINDEXEDPOLYGONALFACE((506,485,483));
#519=IFCINDEXEDPOLYGONALFACE((483,503,506));
#520=IFCINDEXEDPOLYGONALFACE((437,469,484));
#521=IFCINDEXEDPOLYGONALFACE((483,476,484));
#522=IFCINDEXEDPOLYGONALFACE((484,503,483));
#523=IFCINDEXEDPOLYGONALFACE((481,482,483));
#524=IFCINDEXEDPOLYGONALFACE((483,486,481));
#525=IFCINDEXEDPOLYGONALFACE((480,492,478));
#526=IFCINDEXEDPOLYGONALFACE((479,480,477));
#527=IFCINDEXEDPOLYGONALFACE((477,449,451));
#528=IFCINDEXEDPOLYGONALFACE((451,475,477));
#529=IFCINDEXEDPOLYGONALFACE((476,453,441));
#530=IFCINDEXEDPOLYGONALFACE((476,482,450));
#531=IFCINDEXEDPOLYGONALFACE((482,479,475));
#532=IFCINDEXEDPOLYGONALFACE((450,482,475));
#533=IFCINDEXEDPOLYGONALFACE((436,470,469));
#534=IFCINDEXEDPOLYGONALFACE((449,478,472));
#535=IFCINDEXEDPOLYGONALFACE((474,449,472));
#536=IFCINDEXEDPOLYGONALFACE((502,484,469));
#537=IFCINDEXEDPOLYGONALFACE((471,502,469));
#538=IFCINDEXEDPOLYGONALFACE((462,464,468));
#539=IFCINDEXEDPOLYGONALFACE((468,529,462));
#540=IFCINDEXEDPOLYGONALFACE((464,458,466));
#541=IFCINDEXEDPOLYGONALFACE((468,464,466));
#542=IFCINDEXEDPOLYGONALFACE((464,462,461));
#543=IFCINDEXEDPOLYGONALFACE((460,459,465));
#544=IFCINDEXEDPOLYGONALFACE((464,465,456));
#545=IFCINDEXEDPOLYGONALFACE((433,460,461));
#546=IFCINDEXEDPOLYGONALFACE((463,433,461));
#547=IFCINDEXEDPOLYGONALFACE((439,438,459));
#548=IFCINDEXEDPOLYGONALFACE((459,438,445));
#549=IFCINDEXEDPOLYGONALFACE((424,423,439));
#550=IFCINDEXEDPOLYGONALFACE((439,432,424));
#551=IFCINDEXEDPOLYGONALFACE((433,432,439));
#552=IFCINDEXEDPOLYGONALFACE((456,465,459));
#553=IFCINDEXEDPOLYGONALFACE((458,456,455));
#554=IFCINDEXEDPOLYGONALFACE((455,448,474));
#555=IFCINDEXEDPOLYGONALFACE((474,457,455));
#556=IFCINDEXEDPOLYGONALFACE((456,447,454));
#557=IFCINDEXEDPOLYGONALFACE((444,443,451));
#558=IFCINDEXEDPOLYGONALFACE((441,453,401));
#559=IFCINDEXEDPOLYGONALFACE((453,450,452));
#560=IFCINDEXEDPOLYGONALFACE((443,452,450));
#561=IFCINDEXEDPOLYGONALFACE((449,474,448));
#562=IFCINDEXEDPOLYGONALFACE((442,454,447));
#563=IFCINDEXEDPOLYGONALFACE((446,415,442));
#564=IFCINDEXEDPOLYGONALFACE((401,452,404));
#565=IFCINDEXEDPOLYGONALFACE((443,416,404));
#566=IFCINDEXEDPOLYGONALFACE((404,452,443));
#567=IFCINDEXEDPOLYGONALFACE((442,416,448));
#568=IFCINDEXEDPOLYGONALFACE((416,444,448));
#569=IFCINDEXEDPOLYGONALFACE((448,454,442));
#570=IFCINDEXEDPOLYGONALFACE((441,400,440));
#571=IFCINDEXEDPOLYGONALFACE((438,423,446));
#572=IFCINDEXEDPOLYGONALFACE((446,445,438));
#573=IFCINDEXEDPOLYGONALFACE((437,441,435));
#574=IFCINDEXEDPOLYGONALFACE((430,432,433));
#575=IFCINDEXEDPOLYGONALFACE((427,426,431));
#576=IFCINDEXEDPOLYGONALFACE((431,432,430));
#577=IFCINDEXEDPOLYGONALFACE((430,429,431));
#578=IFCINDEXEDPOLYGONALFACE((1,427,429));
#579=IFCINDEXEDPOLYGONALFACE((2,1,429));
#580=IFCINDEXEDPOLYGONALFACE((428,398,397));
#581=IFCINDEXEDPOLYGONALFACE((426,428,403));
#582=IFCINDEXEDPOLYGONALFACE((410,426,403));
#583=IFCINDEXEDPOLYGONALFACE((428,427,399));
#584=IFCINDEXEDPOLYGONALFACE((399,398,428));
#585=IFCINDEXEDPOLYGONALFACE((122,399,427));
#586=IFCINDEXEDPOLYGONALFACE((422,431,426));
#587=IFCINDEXEDPOLYGONALFACE((424,432,422));
#588=IFCINDEXEDPOLYGONALFACE((425,446,423));
#589=IFCINDEXEDPOLYGONALFACE((421,418,417));
#590=IFCINDEXEDPOLYGONALFACE((424,421,417));
#591=IFCINDEXEDPOLYGONALFACE((421,419,412));
#592=IFCINDEXEDPOLYGONALFACE((410,420,419));
#593=IFCINDEXEDPOLYGONALFACE((422,410,419));
#594=IFCINDEXEDPOLYGONALFACE((409,412,419));
#595=IFCINDEXEDPOLYGONALFACE((414,425,417));
#596=IFCINDEXEDPOLYGONALFACE((407,405,404));
#597=IFCINDEXEDPOLYGONALFACE((415,413,407));
#598=IFCINDEXEDPOLYGONALFACE((407,416,415));
#599=IFCINDEXEDPOLYGONALFACE((415,446,425));
#600=IFCINDEXEDPOLYGONALFACE((415,425,414));
#601=IFCINDEXEDPOLYGONALFACE((412,413,414));
#602=IFCINDEXEDPOLYGONALFACE((414,418,412));
#603=IFCINDEXEDPOLYGONALFACE((408,420,410));
#604=IFCINDEXEDPOLYGONALFACE((409,420,408));
#605=IFCINDEXEDPOLYGONALFACE((408,411,381));
#606=IFCINDEXEDPOLYGONALFACE((406,408,380));
#607=IFCINDEXEDPOLYGONALFACE((407,413,378));
#608=IFCINDEXEDPOLYGONALFACE((413,409,406));
#609=IFCINDEXEDPOLYGONALFACE((378,413,406));
#610=IFCINDEXEDPOLYGONALFACE((402,405,9));
#611=IFCINDEXEDPOLYGONALFACE((9,545,402));
#612=IFCINDEXEDPOLYGONALFACE((381,411,403));
#613=IFCINDEXEDPOLYGONALFACE((400,402,545));
#614=IFCINDEXEDPOLYGONALFACE((545,440,400));
#615=IFCINDEXEDPOLYGONALFACE((122,121,383));
#616=IFCINDEXEDPOLYGONALFACE((382,398,399));
#617=IFCINDEXEDPOLYGONALFACE((369,381,397));
#618=IFCINDEXEDPOLYGONALFACE((382,369,397));
#619=IFCINDEXEDPOLYGONALFACE((384,383,121));
#620=IFCINDEXEDPOLYGONALFACE((66,72,396));
#621=IFCINDEXEDPOLYGONALFACE((76,5,386));
#622=IFCINDEXEDPOLYGONALFACE((394,75,76));
#623=IFCINDEXEDPOLYGONALFACE((75,394,393));
#624=IFCINDEXEDPOLYGONALFACE((71,393,396));
#625=IFCINDEXEDPOLYGONALFACE((389,370,384));
#626=IFCINDEXEDPOLYGONALFACE((128,392,395));
#627=IFCINDEXEDPOLYGONALFACE((391,394,395));
#628=IFCINDEXEDPOLYGONALFACE((394,391,387));
#629=IFCINDEXEDPOLYGONALFACE((127,367,392));
#630=IFCINDEXEDPOLYGONALFACE((391,392,367));
#631=IFCINDEXEDPOLYGONALFACE((388,387,391));
#632=IFCINDEXEDPOLYGONALFACE((128,386,385));
#633=IFCINDEXEDPOLYGONALFACE((396,393,387));
#634=IFCINDEXEDPOLYGONALFACE((389,396,387));
#635=IFCINDEXEDPOLYGONALFACE((386,5,4));
#636=IFCINDEXEDPOLYGONALFACE((370,369,382));
#637=IFCINDEXEDPOLYGONALFACE((384,370,382));
#638=IFCINDEXEDPOLYGONALFACE((380,369,368));
#639=IFCINDEXEDPOLYGONALFACE((368,379,380));
#640=IFCINDEXEDPOLYGONALFACE((368,375,378));
#641=IFCINDEXEDPOLYGONALFACE((7,377,374));
#642=IFCINDEXEDPOLYGONALFACE((377,378,375));
#643=IFCINDEXEDPOLYGONALFACE((371,374,373));
#644=IFCINDEXEDPOLYGONALFACE((352,129,376));
#645=IFCINDEXEDPOLYGONALFACE((372,371,376));
#646=IFCINDEXEDPOLYGONALFACE((373,375,353));
#647=IFCINDEXEDPOLYGONALFACE((353,352,373));
#648=IFCINDEXEDPOLYGONALFACE((8,7,371));
#649=IFCINDEXEDPOLYGONALFACE((368,370,353));
#650=IFCINDEXEDPOLYGONALFACE((353,375,368));
#651=IFCINDEXEDPOLYGONALFACE((134,365,366));
#652=IFCINDEXEDPOLYGONALFACE((363,388,366));
#653=IFCINDEXEDPOLYGONALFACE((365,134,133));
#654=IFCINDEXEDPOLYGONALFACE((346,363,365));
#655=IFCINDEXEDPOLYGONALFACE((134,367,127));
#656=IFCINDEXEDPOLYGONALFACE((364,134,127));
#657=IFCINDEXEDPOLYGONALFACE((389,388,363));
#658=IFCINDEXEDPOLYGONALFACE((363,370,389));
#659=IFCINDEXEDPOLYGONALFACE((353,370,363));
#660=IFCINDEXEDPOLYGONALFACE((357,356,360));
#661=IFCINDEXEDPOLYGONALFACE((361,334,136));
#662=IFCINDEXEDPOLYGONALFACE((358,361,135));
#663=IFCINDEXEDPOLYGONALFACE((359,358,135));
#664=IFCINDEXEDPOLYGONALFACE((361,360,335));
#665=IFCINDEXEDPOLYGONALFACE((335,334,361));
#666=IFCINDEXEDPOLYGONALFACE((356,337,335));
#667=IFCINDEXEDPOLYGONALFACE((354,360,358));
#668=IFCINDEXEDPOLYGONALFACE((355,354,358));
#669=IFCINDEXEDPOLYGONALFACE((356,351,341));
#670=IFCINDEXEDPOLYGONALFACE((341,337,356));
#671=IFCINDEXEDPOLYGONALFACE((129,352,351));
#672=IFCINDEXEDPOLYGONALFACE((351,357,129));
#673=IFCINDEXEDPOLYGONALFACE((129,357,354));
#674=IFCINDEXEDPOLYGONALFACE((130,129,354));
#675=IFCINDEXEDPOLYGONALFACE((351,353,346));
#676=IFCINDEXEDPOLYGONALFACE((346,341,351));
#677=IFCINDEXEDPOLYGONALFACE((140,266,347));
#678=IFCINDEXEDPOLYGONALFACE((348,347,266));
#679=IFCINDEXEDPOLYGONALFACE((350,133,132));
#680=IFCINDEXEDPOLYGONALFACE((132,138,350));
#681=IFCINDEXEDPOLYGONALFACE((133,350,347));
#682=IFCINDEXEDPOLYGONALFACE((349,133,347));
#683=IFCINDEXEDPOLYGONALFACE((343,346,348));
#684=IFCINDEXEDPOLYGONALFACE((346,349,348));
#685=IFCINDEXEDPOLYGONALFACE((348,268,343));
#686=IFCINDEXEDPOLYGONALFACE((345,344,273));
#687=IFCINDEXEDPOLYGONALFACE((273,272,345));
#688=IFCINDEXEDPOLYGONALFACE((273,342,267));
#689=IFCINDEXEDPOLYGONALFACE((267,270,273));
#690=IFCINDEXEDPOLYGONALFACE((343,268,267));
#691=IFCINDEXEDPOLYGONALFACE((344,318,340));
#692=IFCINDEXEDPOLYGONALFACE((341,343,342));
#693=IFCINDEXEDPOLYGONALFACE((341,342,344));
#694=IFCINDEXEDPOLYGONALFACE((340,341,344));
#695=IFCINDEXEDPOLYGONALFACE((339,338,319));
#696=IFCINDEXEDPOLYGONALFACE((317,338,340));
#697=IFCINDEXEDPOLYGONALFACE((340,318,317));
#698=IFCINDEXEDPOLYGONALFACE((339,331,336));
#699=IFCINDEXEDPOLYGONALFACE((337,340,339));
#700=IFCINDEXEDPOLYGONALFACE((336,337,339));
#701=IFCINDEXEDPOLYGONALFACE((335,336,332));
#702=IFCINDEXEDPOLYGONALFACE((334,332,320));
#703=IFCINDEXEDPOLYGONALFACE((321,320,332));
#704=IFCINDEXEDPOLYGONALFACE((333,137,136));
#705=IFCINDEXEDPOLYGONALFACE((336,331,330));
#706=IFCINDEXEDPOLYGONALFACE((332,336,330));
#707=IFCINDEXEDPOLYGONALFACE((331,319,323));
#708=IFCINDEXEDPOLYGONALFACE((330,323,328));
#709=IFCINDEXEDPOLYGONALFACE((319,304,303));
#710=IFCINDEXEDPOLYGONALFACE((296,324,328));
#711=IFCINDEXEDPOLYGONALFACE((329,328,324));
#712=IFCINDEXEDPOLYGONALFACE((321,329,324));
#713=IFCINDEXEDPOLYGONALFACE((275,274,327));
#714=IFCINDEXEDPOLYGONALFACE((321,327,274));
#715=IFCINDEXEDPOLYGONALFACE((322,321,274));
#716=IFCINDEXEDPOLYGONALFACE((325,327,324));
#717=IFCINDEXEDPOLYGONALFACE((297,328,323));
#718=IFCINDEXEDPOLYGONALFACE((322,333,320));
#719=IFCINDEXEDPOLYGONALFACE((338,317,316));
#720=IFCINDEXEDPOLYGONALFACE((319,338,316));
#721=IFCINDEXEDPOLYGONALFACE((318,345,314));
#722=IFCINDEXEDPOLYGONALFACE((317,314,315));
#723=IFCINDEXEDPOLYGONALFACE((315,311,309));
#724=IFCINDEXEDPOLYGONALFACE((316,315,305));
#725=IFCINDEXEDPOLYGONALFACE((300,302,316));
#726=IFCINDEXEDPOLYGONALFACE((316,305,300));
#727=IFCINDEXEDPOLYGONALFACE((314,312,311));
#728=IFCINDEXEDPOLYGONALFACE((345,272,313));
#729=IFCINDEXEDPOLYGONALFACE((314,345,313));
#730=IFCINDEXEDPOLYGONALFACE((313,307,306));
#731=IFCINDEXEDPOLYGONALFACE((307,272,250));
#732=IFCINDEXEDPOLYGONALFACE((250,249,307));
#733=IFCINDEXEDPOLYGONALFACE((306,247,245));
#734=IFCINDEXEDPOLYGONALFACE((312,306,308));
#735=IFCINDEXEDPOLYGONALFACE((312,308,310));
#736=IFCINDEXEDPOLYGONALFACE((277,309,311));
#737=IFCINDEXEDPOLYGONALFACE((278,277,310));
#738=IFCINDEXEDPOLYGONALFACE((285,289,309));
#739=IFCINDEXEDPOLYGONALFACE((244,310,308));
#740=IFCINDEXEDPOLYGONALFACE((307,249,247));
#741=IFCINDEXEDPOLYGONALFACE((301,305,309));
#742=IFCINDEXEDPOLYGONALFACE((309,289,301));
#743=IFCINDEXEDPOLYGONALFACE((298,303,304));
#744=IFCINDEXEDPOLYGONALFACE((294,297,303));
#745=IFCINDEXEDPOLYGONALFACE((299,298,302));
#746=IFCINDEXEDPOLYGONALFACE((290,299,300));
#747=IFCINDEXEDPOLYGONALFACE((293,295,299));
#748=IFCINDEXEDPOLYGONALFACE((301,289,287));
#749=IFCINDEXEDPOLYGONALFACE((290,301,287));
#750=IFCINDEXEDPOLYGONALFACE((295,294,298));
#751=IFCINDEXEDPOLYGONALFACE((291,325,296));
#752=IFCINDEXEDPOLYGONALFACE((294,291,296));
#753=IFCINDEXEDPOLYGONALFACE((279,291,294));
#754=IFCINDEXEDPOLYGONALFACE((295,293,280));
#755=IFCINDEXEDPOLYGONALFACE((280,279,295));
#756=IFCINDEXEDPOLYGONALFACE((275,325,291));
#757=IFCINDEXEDPOLYGONALFACE((292,276,275));
#758=IFCINDEXEDPOLYGONALFACE((291,292,275));
#759=IFCINDEXEDPOLYGONALFACE((284,293,290));
#760=IFCINDEXEDPOLYGONALFACE((288,287,289));
#761=IFCINDEXEDPOLYGONALFACE((282,286,287));
#762=IFCINDEXEDPOLYGONALFACE((142,284,286));
#763=IFCINDEXEDPOLYGONALFACE((228,282,288));
#764=IFCINDEXEDPOLYGONALFACE((228,288,285));
#765=IFCINDEXEDPOLYGONALFACE((141,280,284));
#766=IFCINDEXEDPOLYGONALFACE((281,280,141));
#767=IFCINDEXEDPOLYGONALFACE((143,142,282));
#768=IFCINDEXEDPOLYGONALFACE((281,292,279));
#769=IFCINDEXEDPOLYGONALFACE((230,285,277));
#770=IFCINDEXEDPOLYGONALFACE((276,326,274));
#771=IFCINDEXEDPOLYGONALFACE((273,270,271));
#772=IFCINDEXEDPOLYGONALFACE((273,271,250));
#773=IFCINDEXEDPOLYGONALFACE((270,269,251));
#774=IFCINDEXEDPOLYGONALFACE((269,264,263));
#775=IFCINDEXEDPOLYGONALFACE((248,271,251));
#776=IFCINDEXEDPOLYGONALFACE((251,241,248));
#777=IFCINDEXEDPOLYGONALFACE((269,267,265));
#778=IFCINDEXEDPOLYGONALFACE((265,264,269));
#779=IFCINDEXEDPOLYGONALFACE((268,266,265));
#780=IFCINDEXEDPOLYGONALFACE((259,266,140));
#781=IFCINDEXEDPOLYGONALFACE((140,253,259));
#782=IFCINDEXEDPOLYGONALFACE((265,259,262));
#783=IFCINDEXEDPOLYGONALFACE((263,260,215));
#784=IFCINDEXEDPOLYGONALFACE((260,264,262));
#785=IFCINDEXEDPOLYGONALFACE((262,261,260));
#786=IFCINDEXEDPOLYGONALFACE((209,213,261));
#787=IFCINDEXEDPOLYGONALFACE((254,262,258));
#788=IFCINDEXEDPOLYGONALFACE((258,255,254));
#789=IFCINDEXEDPOLYGONALFACE((260,261,213));
#790=IFCINDEXEDPOLYGONALFACE((258,262,259));
#791=IFCINDEXEDPOLYGONALFACE((257,258,253));
#792=IFCINDEXEDPOLYGONALFACE((146,191,255));
#793=IFCINDEXEDPOLYGONALFACE((146,255,258));
#794=IFCINDEXEDPOLYGONALFACE((257,252,256));
#795=IFCINDEXEDPOLYGONALFACE((191,209,254));
#796=IFCINDEXEDPOLYGONALFACE((139,256,252));
#797=IFCINDEXEDPOLYGONALFACE((140,139,252));
#798=IFCINDEXEDPOLYGONALFACE((239,251,263));
#799=IFCINDEXEDPOLYGONALFACE((263,218,239));
#800=IFCINDEXEDPOLYGONALFACE((250,248,242));
#801=IFCINDEXEDPOLYGONALFACE((249,242,243));
#802=IFCINDEXEDPOLYGONALFACE((248,241,240));
#803=IFCINDEXEDPOLYGONALFACE((246,245,247));
#804=IFCINDEXEDPOLYGONALFACE((231,244,245));
#805=IFCINDEXEDPOLYGONALFACE((226,230,278));
#806=IFCINDEXEDPOLYGONALFACE((226,278,244));
#807=IFCINDEXEDPOLYGONALFACE((232,231,246));
#808=IFCINDEXEDPOLYGONALFACE((232,246,243));
#809=IFCINDEXEDPOLYGONALFACE((237,243,242));
#810=IFCINDEXEDPOLYGONALFACE((241,239,238));
#811=IFCINDEXEDPOLYGONALFACE((238,217,216));
#812=IFCINDEXEDPOLYGONALFACE((240,238,234));
#813=IFCINDEXEDPOLYGONALFACE((240,234,233));
#814=IFCINDEXEDPOLYGONALFACE((239,218,217));
#815=IFCINDEXEDPOLYGONALFACE((233,206,236));
#816=IFCINDEXEDPOLYGONALFACE((237,233,235));
#817=IFCINDEXEDPOLYGONALFACE((237,235,219));
#818=IFCINDEXEDPOLYGONALFACE((224,227,232));
#819=IFCINDEXEDPOLYGONALFACE((205,219,235));
#820=IFCINDEXEDPOLYGONALFACE((234,216,206));
#821=IFCINDEXEDPOLYGONALFACE((227,226,231));
#822=IFCINDEXEDPOLYGONALFACE((225,228,230));
#823=IFCINDEXEDPOLYGONALFACE((229,143,228));
#824=IFCINDEXEDPOLYGONALFACE((220,229,225));
#825=IFCINDEXEDPOLYGONALFACE((227,220,225));
#826=IFCINDEXEDPOLYGONALFACE((180,223,224));
#827=IFCINDEXEDPOLYGONALFACE((227,224,223));
#828=IFCINDEXEDPOLYGONALFACE((220,227,223));
#829=IFCINDEXEDPOLYGONALFACE((147,546,221));
#830=IFCINDEXEDPOLYGONALFACE((148,147,223));
#831=IFCINDEXEDPOLYGONALFACE((222,143,229));
#832=IFCINDEXEDPOLYGONALFACE((222,229,220));
#833=IFCINDEXEDPOLYGONALFACE((181,224,219));
#834=IFCINDEXEDPOLYGONALFACE((218,215,214));
#835=IFCINDEXEDPOLYGONALFACE((217,214,208));
#836=IFCINDEXEDPOLYGONALFACE((214,212,210));
#837=IFCINDEXEDPOLYGONALFACE((216,208,207));
#838=IFCINDEXEDPOLYGONALFACE((215,213,212));
#839=IFCINDEXEDPOLYGONALFACE((192,212,213));
#840=IFCINDEXEDPOLYGONALFACE((211,210,212));
#841=IFCINDEXEDPOLYGONALFACE((201,202,210));
#842=IFCINDEXEDPOLYGONALFACE((190,192,209));
#843=IFCINDEXEDPOLYGONALFACE((208,210,202));
#844=IFCINDEXEDPOLYGONALFACE((207,202,200));
#845=IFCINDEXEDPOLYGONALFACE((206,207,204));
#846=IFCINDEXEDPOLYGONALFACE((203,205,236));
#847=IFCINDEXEDPOLYGONALFACE((203,236,206));
#848=IFCINDEXEDPOLYGONALFACE((203,199,196));
#849=IFCINDEXEDPOLYGONALFACE((205,203,193));
#850=IFCINDEXEDPOLYGONALFACE((178,179,205));
#851=IFCINDEXEDPOLYGONALFACE((205,193,178));
#852=IFCINDEXEDPOLYGONALFACE((204,200,199));
#853=IFCINDEXEDPOLYGONALFACE((194,200,202));
#854=IFCINDEXEDPOLYGONALFACE((195,194,201));
#855=IFCINDEXEDPOLYGONALFACE((197,199,200));
#856=IFCINDEXEDPOLYGONALFACE((182,196,199));
#857=IFCINDEXEDPOLYGONALFACE((198,182,197));
#858=IFCINDEXEDPOLYGONALFACE((170,172,196));
#859=IFCINDEXEDPOLYGONALFACE((196,182,170));
#860=IFCINDEXEDPOLYGONALFACE((186,197,194));
#861=IFCINDEXEDPOLYGONALFACE((173,177,193));
#862=IFCINDEXEDPOLYGONALFACE((193,196,173));
#863=IFCINDEXEDPOLYGONALFACE((187,201,211));
#864=IFCINDEXEDPOLYGONALFACE((187,211,192));
#865=IFCINDEXEDPOLYGONALFACE((185,187,190));
#866=IFCINDEXEDPOLYGONALFACE((188,191,146));
#867=IFCINDEXEDPOLYGONALFACE((146,189,188));
#868=IFCINDEXEDPOLYGONALFACE((150,185,188));
#869=IFCINDEXEDPOLYGONALFACE((183,186,195));
#870=IFCINDEXEDPOLYGONALFACE((183,195,187));
#871=IFCINDEXEDPOLYGONALFACE((159,163,198));
#872=IFCINDEXEDPOLYGONALFACE((159,198,186));
#873=IFCINDEXEDPOLYGONALFACE((184,183,185));
#874=IFCINDEXEDPOLYGONALFACE((158,159,183));
#875=IFCINDEXEDPOLYGONALFACE((151,150,189));
#876=IFCINDEXEDPOLYGONALFACE((151,189,146));
#877=IFCINDEXEDPOLYGONALFACE((163,171,182));
#878=IFCINDEXEDPOLYGONALFACE((182,198,163));
#879=IFCINDEXEDPOLYGONALFACE((174,148,180));
#880=IFCINDEXEDPOLYGONALFACE((179,174,180));
#881=IFCINDEXEDPOLYGONALFACE((175,174,179));
#882=IFCINDEXEDPOLYGONALFACE((164,175,178));
#883=IFCINDEXEDPOLYGONALFACE((165,164,177));
#884=IFCINDEXEDPOLYGONALFACE((148,174,176));
#885=IFCINDEXEDPOLYGONALFACE((176,149,148));
#886=IFCINDEXEDPOLYGONALFACE((167,165,173));
#887=IFCINDEXEDPOLYGONALFACE((168,167,172));
#888=IFCINDEXEDPOLYGONALFACE((153,168,170));
#889=IFCINDEXEDPOLYGONALFACE((171,163,161));
#890=IFCINDEXEDPOLYGONALFACE((153,171,161));
#891=IFCINDEXEDPOLYGONALFACE((165,167,169));
#892=IFCINDEXEDPOLYGONALFACE((169,166,165));
#893=IFCINDEXEDPOLYGONALFACE((175,164,166));
#894=IFCINDEXEDPOLYGONALFACE((166,176,175));
#895=IFCINDEXEDPOLYGONALFACE((162,161,163));
#896=IFCINDEXEDPOLYGONALFACE((156,154,161));
#897=IFCINDEXEDPOLYGONALFACE((160,155,154));
#898=IFCINDEXEDPOLYGONALFACE((10,156,162));
#899=IFCINDEXEDPOLYGONALFACE((10,162,159));
#900=IFCINDEXEDPOLYGONALFACE((11,10,158));
#901=IFCINDEXEDPOLYGONALFACE((157,160,156));
#902=IFCINDEXEDPOLYGONALFACE((168,153,155));
#903=IFCINDEXEDPOLYGONALFACE((155,169,168));
#904=IFCINDEXEDPOLYGONALFACE((152,158,184));
#905=IFCINDEXEDPOLYGONALFACE((152,184,150));
#906=IFCINDEXEDPOLYGONALFACE((149,546,147));
#907=IFCINDEXEDPOLYGONALFACE((146,257,144));
#908=IFCINDEXEDPOLYGONALFACE((141,143,222));
#909=IFCINDEXEDPOLYGONALFACE((222,283,141));
#910=IFCINDEXEDPOLYGONALFACE((140,350,138));
#911=IFCINDEXEDPOLYGONALFACE((137,362,135));
#912=IFCINDEXEDPOLYGONALFACE((134,364,132));
#913=IFCINDEXEDPOLYGONALFACE((131,376,129));
#914=IFCINDEXEDPOLYGONALFACE((128,390,126));
#915=IFCINDEXEDPOLYGONALFACE((124,31,547));
#916=IFCINDEXEDPOLYGONALFACE((547,125,124));
#917=IFCINDEXEDPOLYGONALFACE((119,117,124));
#918=IFCINDEXEDPOLYGONALFACE((117,116,123));
#919=IFCINDEXEDPOLYGONALFACE((123,120,13));
#920=IFCINDEXEDPOLYGONALFACE((17,13,120));
#921=IFCINDEXEDPOLYGONALFACE((80,65,64));
#922=IFCINDEXEDPOLYGONALFACE((17,64,121));
#923=IFCINDEXEDPOLYGONALFACE((118,120,123));
#924=IFCINDEXEDPOLYGONALFACE((123,116,118));
#925=IFCINDEXEDPOLYGONALFACE((119,548,88));
#926=IFCINDEXEDPOLYGONALFACE((115,82,118));
#927=IFCINDEXEDPOLYGONALFACE((115,86,83));
#928=IFCINDEXEDPOLYGONALFACE((80,118,82));
#929=IFCINDEXEDPOLYGONALFACE((115,117,88));
#930=IFCINDEXEDPOLYGONALFACE((88,86,115));
#931=IFCINDEXEDPOLYGONALFACE((92,87,113));
#932=IFCINDEXEDPOLYGONALFACE((111,92,114));
#933=IFCINDEXEDPOLYGONALFACE((114,549,111));
#934=IFCINDEXEDPOLYGONALFACE((112,109,107));
#935=IFCINDEXEDPOLYGONALFACE((549,112,110));
#936=IFCINDEXEDPOLYGONALFACE((111,549,110));
#937=IFCINDEXEDPOLYGONALFACE((110,107,101));
#938=IFCINDEXEDPOLYGONALFACE((109,108,103));
#939=IFCINDEXEDPOLYGONALFACE((108,550,104));
#940=IFCINDEXEDPOLYGONALFACE((107,103,106));
#941=IFCINDEXEDPOLYGONALFACE((96,95,106));
#942=IFCINDEXEDPOLYGONALFACE((101,106,95));
#943=IFCINDEXEDPOLYGONALFACE((102,101,95));
#944=IFCINDEXEDPOLYGONALFACE((105,106,103));
#945=IFCINDEXEDPOLYGONALFACE((100,102,93));
#946=IFCINDEXEDPOLYGONALFACE((93,111,100));
#947=IFCINDEXEDPOLYGONALFACE((89,102,99));
#948=IFCINDEXEDPOLYGONALFACE((90,89,99));
#949=IFCINDEXEDPOLYGONALFACE((89,94,93));
#950=IFCINDEXEDPOLYGONALFACE((93,102,89));
#951=IFCINDEXEDPOLYGONALFACE((97,99,95));
#952=IFCINDEXEDPOLYGONALFACE((85,87,92));
#953=IFCINDEXEDPOLYGONALFACE((94,85,92));
#954=IFCINDEXEDPOLYGONALFACE((91,98,89));
#955=IFCINDEXEDPOLYGONALFACE((84,86,88));
#956=IFCINDEXEDPOLYGONALFACE((88,85,84));
#957=IFCINDEXEDPOLYGONALFACE((87,88,548));
#958=IFCINDEXEDPOLYGONALFACE((548,113,87));
#959=IFCINDEXEDPOLYGONALFACE((68,78,84));
#960=IFCINDEXEDPOLYGONALFACE((78,77,83));
#961=IFCINDEXEDPOLYGONALFACE((84,78,83));
#962=IFCINDEXEDPOLYGONALFACE((98,91,551));
#963=IFCINDEXEDPOLYGONALFACE((69,94,98));
#964=IFCINDEXEDPOLYGONALFACE((551,69,98));
#965=IFCINDEXEDPOLYGONALFACE((85,94,69));
#966=IFCINDEXEDPOLYGONALFACE((77,81,82));
#967=IFCINDEXEDPOLYGONALFACE((72,66,65));
#968=IFCINDEXEDPOLYGONALFACE((81,72,65));
#969=IFCINDEXEDPOLYGONALFACE((67,6,79));
#970=IFCINDEXEDPOLYGONALFACE((78,68,79));
#971=IFCINDEXEDPOLYGONALFACE((74,70,77));
#972=IFCINDEXEDPOLYGONALFACE((76,79,6));
#973=IFCINDEXEDPOLYGONALFACE((74,79,76));
#974=IFCINDEXEDPOLYGONALFACE((75,71,70));
#975=IFCINDEXEDPOLYGONALFACE((73,552,6));
#976=IFCINDEXEDPOLYGONALFACE((70,72,81));
#977=IFCINDEXEDPOLYGONALFACE((81,77,70));
#978=IFCINDEXEDPOLYGONALFACE((67,69,551));
#979=IFCINDEXEDPOLYGONALFACE((551,73,67));
#980=IFCINDEXEDPOLYGONALFACE((66,121,64));
#981=IFCINDEXEDPOLYGONALFACE((61,60,58));
#982=IFCINDEXEDPOLYGONALFACE((57,56,60));
#983=IFCINDEXEDPOLYGONALFACE((62,57,60));
#984=IFCINDEXEDPOLYGONALFACE((59,58,60));
#985=IFCINDEXEDPOLYGONALFACE((58,52,53));
#986=IFCINDEXEDPOLYGONALFACE((53,63,58));
#987=IFCINDEXEDPOLYGONALFACE((54,59,56));
#988=IFCINDEXEDPOLYGONALFACE((55,54,56));
#989=IFCINDEXEDPOLYGONALFACE((51,49,54));
#990=IFCINDEXEDPOLYGONALFACE((52,59,54));
#991=IFCINDEXEDPOLYGONALFACE((53,52,43));
#992=IFCINDEXEDPOLYGONALFACE((52,46,40));
#993=IFCINDEXEDPOLYGONALFACE((46,54,49));
#994=IFCINDEXEDPOLYGONALFACE((51,50,47));
#995=IFCINDEXEDPOLYGONALFACE((50,35,34));
#996=IFCINDEXEDPOLYGONALFACE((49,47,37));
#997=IFCINDEXEDPOLYGONALFACE((45,48,37));
#998=IFCINDEXEDPOLYGONALFACE((38,37,47));
#999=IFCINDEXEDPOLYGONALFACE((46,48,45));
#1000=IFCINDEXEDPOLYGONALFACE((43,42,41));
#1001=IFCINDEXEDPOLYGONALFACE((43,40,39));
#1002=IFCINDEXEDPOLYGONALFACE((42,27,28));
#1003=IFCINDEXEDPOLYGONALFACE((27,42,39));
#1004=IFCINDEXEDPOLYGONALFACE((40,45,29));
#1005=IFCINDEXEDPOLYGONALFACE((36,30,29));
#1006=IFCINDEXEDPOLYGONALFACE((29,45,36));
#1007=IFCINDEXEDPOLYGONALFACE((26,39,29));
#1008=IFCINDEXEDPOLYGONALFACE((38,33,36));
#1009=IFCINDEXEDPOLYGONALFACE((34,32,15));
#1010=IFCINDEXEDPOLYGONALFACE((15,33,38));
#1011=IFCINDEXEDPOLYGONALFACE((15,38,34));
#1012=IFCINDEXEDPOLYGONALFACE((14,20,30));
#1013=IFCINDEXEDPOLYGONALFACE((15,14,30));
#1014=IFCINDEXEDPOLYGONALFACE((32,547,31));
#1015=IFCINDEXEDPOLYGONALFACE((20,19,29));
#1016=IFCINDEXEDPOLYGONALFACE((27,24,25));
#1017=IFCINDEXEDPOLYGONALFACE((26,21,24));
#1018=IFCINDEXEDPOLYGONALFACE((26,19,18));
#1019=IFCINDEXEDPOLYGONALFACE((23,542,25));
#1020=IFCINDEXEDPOLYGONALFACE((24,21,22));
#1021=IFCINDEXEDPOLYGONALFACE((16,22,21));
#1022=IFCINDEXEDPOLYGONALFACE((20,3,18));
#1023=IFCINDEXEDPOLYGONALFACE((14,17,3));
#1024=IFCINDEXEDPOLYGONALFACE((3,20,14));
#1025=IFCINDEXEDPOLYGONALFACE((16,18,3));
#1026=IFCINDEXEDPOLYGONALFACE((15,31,13));
#1027=IFCINDEXEDPOLYGONALFACE((12,157,10));
#1028=IFCINDEXEDPOLYGONALFACE((377,7,405));
#1029=IFCINDEXEDPOLYGONALFACE((405,407,377));
#1030=IFCINDEXEDPOLYGONALFACE((9,405,7));
#1031=IFCINDEXEDPOLYGONALFACE((6,552,4));
#1032=IFCINDEXEDPOLYGONALFACE((17,122,1));
#1033=IFCINDEXEDPOLYGONALFACE((3,17,1));
#1034=IFCCARTESIANPOINTLIST3D(((6.27551233417533,7.5000105325918,1.84400901157875),
(4.95474580622064,7.50001053259181,1.86980415827464),(4.95474580622063,
6.27548724303618,1.77466593443737),(15.000035623731,7.5000105325918,1.49858058983303),
(14.2383303254824,7.5000105325918,1.68967402830169),(14.2383303254824,6.27548724303617,
1.70723468259626),(7.50605669432379,13.6777170452482,1.93560510185292),
(7.53905347888618,15.0032455169685,1.88859972957176),(6.27955071286552,
15.00034577065,1.93356436024935),(13.9102084432052,13.9124049547169,2.30583308896575),
(15.000035623731,14.5867573426454,1.5128198689705),(15.000035623731,15.0000105325918,
1.93875932375063),(7.50003562373096,4.95472071508149,1.84125677848346),
(6.27551233417533,4.9547207150815,1.83647148432418),(6.27551233417534,3.30075088376784,
1.92962527191859),(3.30077597490696,7.50001053259181,1.86808736472627),
(7.50003562373096,6.27548724303617,1.73271890630208),(3.30077597490695,
6.27548724303618,1.69587934731789),(3.30077597490694,4.95472071508149,1.65562029183978),
(4.95474580622063,4.9547207150815,1.81486246565565),(2.14168670185173,6.27548724303618,
1.81926313830533),(2.14168670185173,7.50001053259181,1.90800149058855),
(1.32282832019869,7.50001053259181,1.97777400212673),(1.32282832019869,
6.27548724303618,1.93856172162374),(3.56237309950465E-5,6.27548724303618,
2.1262541256213),(2.14168670185173,4.9547207150815,1.8008202057411),(1.32282832019869,
4.9547207150815,1.97189359604043),(3.56237309749177E-5,4.95472071508149,
2.37650155559882),(3.30077597490695,4.19492904440796,1.72452666122253),
(4.95474580622064,4.19492904440797,1.86191364251208),(7.50003562373097,
3.30075088376783,1.93060175605675),(6.27551233417534,1.05325917604205E-5,
1.93875932375063),(4.95474580622064,3.30075088376784,1.86366918809022),
(4.95474580622064,1.32280322905956,1.87469445468997),(4.95474580622064,
1.05325917598748E-5,1.93875932375063),(4.19495413554709,3.30075088376784,
1.7628603235939),(4.19495413554708,2.14166161071261,1.68512316141586),(4.95474580622063,
2.14166161071261,1.83324004528767),(2.14168670185173,4.19492904440797,1.83714247236951),
(2.14168670185173,3.30075088376784,1.81959904917086),(3.56237309657571E-5,
4.19492904440795,2.49038797015704),(1.32282832019869,4.19492904440797,1.99772997122474),
(1.32282832019869,3.30075088376784,2.00159413953116),(3.56237309614627E-5,
3.30075088376783,2.54377711160453),(3.30077597490695,3.30075088376784,1.68948286631111),
(2.14168670185172,2.14166161071261,1.76226366974761),(4.19495413554709,
1.32280322905956,1.7846954049841),(3.30077597490694,2.14166161071261,1.57790945066671),
(3.30077597490695,1.32280322905956,1.71955627943336),(4.1949541355471,1.0532591759511E-5,
1.93875932375063),(3.30077597490697,1.05325917596929E-5,1.93875932375063),
(1.32282832019869,2.14166161071261,1.98817396249951),(3.56237309614991E-5,
2.14166161071261,2.54549772948631),(2.14168670185173,1.32280322905956,1.83120410746694),
(2.14168670185174,1.05325917596929E-5,1.93875932375063),(1.3228283201987,
0.761715830840386,1.94862039622665),(1.3228283201987,1.05325917597838E-5,
1.93875932375063),(0.761740921979545,1.32280322905956,2.07653408335157),
(1.32282832019869,1.32280322905956,1.96517416590292),(0.761740921979552,
0.761715830840386,1.99077832581209),(3.56237309994325E-5,0.761715830840386,
2.06915429698748),(3.56237310098692E-5,1.053259175942E-5,1.93875932375063),
(3.5623730982296E-5,1.32280322905956,2.28398552235805),(8.72455891328657,
6.27548724303617,1.68304371356943),(10.0453254412413,6.27548724303617,1.60937334434211),
(10.0453254412413,7.5000105325918,1.61553526464893),(14.2383303254824,4.95472071508148,
1.84999872314221),(13.6772429272632,4.95472071508148,1.88673586287481),
(13.6772429272632,3.30075088376783,1.93484707784769),(11.699295272555,6.27548724303617,
1.73154806807455),(11.699295272555,7.5000105325918,1.63410014279513),(10.8051171119148,
7.5000105325918,1.6194873081429),(15.000035623731,4.95472071508148,1.74137822270637),
(12.8583845456102,6.27548724303617,1.74409212678875),(12.8583845456101,
7.5000105325918,1.62238548436982),(13.6772429272632,7.5000105325918,1.69233473574082),
(11.699295272555,4.95472071508147,1.74431315163044),(12.8583845456102,4.95472071508148,
1.87625362888279),(13.6772429272632,6.27548724303617,1.75997685900662),
(10.0453254412413,4.95472071508147,1.61109042080252),(10.8051171119148,
4.95472071508147,1.61911359968447),(10.8051171119148,4.19492904440794,1.63199026874048),
(11.699295272555,4.19492904440794,1.75144827528914),(12.8583845456102,3.30075088376783,
1.91870937359671),(12.8583845456102,2.14166161071261,1.93509045682401),
(11.699295272555,3.30075088376782,1.82710554051313),(12.8583845456102,1.32280322905956,
1.93540714157257),(11.699295272555,2.14166161071261,1.91943493083555),(14.2383303254824,
1.76726774475585,1.91038926521151),(15.000035623731,1.76726774475585,1.8699792220814),
(15.000035623731,2.14166161071261,1.91894518857262),(13.2327784115669,1.32280322905956,
1.92801822312755),(13.6772429272632,1.32280322905956,1.90675557586643),
(13.6772429272632,2.14166161071261,1.93536076738232),(14.5867824337846,
1.06096680002671,1.71596464212405),(15.000035623731,1.06096680002671,1.59555247777661),
(15.000035623731,1.32280322905956,1.73158686957491),(14.2383303254824,2.14166161071261,
1.93056187113554),(14.5867824337846,1.32280322905956,1.80427199538384),
(13.9390793562961,0.761715830840386,1.7972484675014),(14.2383303254824,
0.761715830840387,1.71052812116378),(14.2383303254824,1.32280322905956,
1.85333576407027),(14.5867824337846,0.413263722538179,1.37303129227203),
(15.000035623731,0.41326372253818,1.06727626649712),(15.000035623731,0.761715830840387,
1.38522768482735),(14.5867824337846,0.761715830840386,1.57943119469673),
(14.2383303254824,0.413263722538179,1.57943125611753),(14.5867824337846,
1.05325917604368E-5,1.06727626807292),(14.2383303254824,1.05325917598748E-5,
1.38522778039806),(13.9390793562961,0.413263722538179,1.71596531135116),
(13.6772429272632,0.761715830840386,1.85333802024453),(13.9390793562961,
1.05325917596019E-5,1.59555350852774),(12.8583845456102,1.05325917596929E-5,
1.9192235618118),(13.2327784115669,1.05325917597245E-5,1.87003492998141),
(10.8051171119148,3.30075088376782,1.75805600667111),(10.0453254412413,
3.30075088376782,1.77575449644466),(10.0453254412413,2.14166161071261,1.91107027576262),
(10.0453254412413,4.19492904440794,1.65069927144681),(10.0453254412413,
1.05325917596929E-5,1.93875932375063),(8.72455891328658,4.95472071508148,
1.77673335110717),(8.72455891328658,7.5000105325918,1.78279445731793),(7.50003562373096,
7.5000105325918,1.87966798668729),(8.72455891328659,3.30075088376783,1.89211839406824),
(8.72455891328659,2.14166161071261,1.93143775636662),(8.72455891328659,
1.05325917596941E-5,1.93875932375063),(15.000035623731,10.0453003501022,
1.4711341273609),(14.2383303254824,10.0453003501022,1.73869955645438),(14.2383303254824,
9.36305314677204,1.69901450977995),(10.0621874144079,13.6767214025219,1.91806045101115),
(10.1545947176102,14.9967935342049,1.8041032875793),(9.52027554933442,15.0097469844875,
1.73718721226682),(15.000035623731,11.6992701814158,1.81619415062506),(13.6772363896751,
11.6992636697952,1.91946127902194),(13.6772429272632,10.8050920207757,1.8914627550737),
(11.3279791681684,14.6988958688214,2.18774781452213),(11.5160793484747,
14.655647571912,2.27387769793196),(11.4724849443013,14.8409536273032,2.35361693767088),
(15.000035623731,12.2304773857726,1.77393100657198),(15.000035623731,12.858359454471,
1.55804777956385),(14.2156494372533,12.8357366162691,1.80388604985107),
(12.0210545480774,14.1511820852406,2.8132894748948),(12.1522391077941,14.0781083408717,
2.63989760104514),(12.2105479055112,13.9611987863215,2.73622344226053),
(15.000035623731,13.4439694631903,1.30630492091155),(15.000035623731,13.6772178361241,
1.24579196877086),(14.4259487994007,13.5169123160831,1.6419048618389),(12.4198488340077,
13.5698369487958,3.00158173294963),(12.5647270311895,13.4786603873022,3.02523029147542),
(11.9634534345626,13.2930282542596,3.71368093097424),(14.3203353393946,
13.6734820781377,1.73343936921321),(15.000035623731,13.9390542651569,1.23203909221498),
(15.000035623731,14.2383052343432,1.30284756061822),(13.101942454981,13.5319120109975,
2.81993421852295),(13.2265443587367,13.5775609659648,2.74911570430918),
(12.6797115544694,13.4453809290682,3.45762316784546),(13.5273021589626,
13.7110219820313,2.56291713824657),(13.5445452844714,13.961195679799,2.93016230502276),
(14.1570494140113,13.8099832886419,1.93472688034462),(13.8477151120512,
13.6820727952028,2.14563034582227),(13.0526515685725,13.6509726764106,3.24068460682652),
(13.4296575398245,13.5989037135133,2.49898790635751),(13.7036042947654,
13.7055728067667,2.32757538020562),(13.5956839262593,13.5973036402326,2.3028162604706),
(12.736546597039,13.4560328393514,2.98797616245936),(12.8108579623539,13.4615293067516,
2.96074602155555),(12.2010961263917,13.2666384597858,3.68083461645311),
(12.8956207075211,13.4752199802521,2.92444270368959),(12.9921640878836,
13.4982593233104,2.87792208531308),(12.402407816377,13.323219688573,3.59921008531574),
(13.3902791484814,13.5469343756061,2.41627712522252),(13.4877533483981,
13.5683092308394,2.3644498326195),(13.3021833031669,13.532300696801,2.45931017872347),
(13.222477782274,13.5236049228753,2.49447779397357),(12.6145352488121,13.4655105108278,
3.01930721411527),(12.6714738382428,13.4576787212248,3.00717864541307),
(12.0593332573989,13.2607067755376,3.71540632176694),(13.1502584731726,
13.5201137843348,2.52263109886759),(13.0846994302564,13.5211591833562,2.54454881867201),
(13.0250474364255,13.526133626127,2.56094226192908),(12.7880614616808,13.5198571472642,
2.77439452410493),(12.9706168638579,13.5344859071867,2.57246003350001),
(13.4668918997297,13.5444500615532,2.33138776281529),(13.9649002603127,
13.643964663285,2.00746907905359),(14.2473269620383,13.7450039478961,1.81708563045069),
(14.0596690894603,13.5941738152148,1.90492119911296),(13.7306899381334,
13.5770036615329,2.15107669848406),(13.8398706821357,13.5416144492912,2.03566225665743),
(14.1358674383861,13.5351729327287,1.83107768749277),(14.3790373183295,
13.597008488771,1.67695604569019),(13.9276568508822,13.4941931218321,1.94970585528459),
(13.9978108604776,13.4373139625956,1.88742112571277),(13.7483101823531,
13.4698431924254,2.03922922696183),(13.1642200327749,13.5215240033681,2.45793769795693),
(13.5979226843381,13.5257129305751,2.19069959489739),(13.7882148114509,
13.560992031907,2.08928513990351),(13.3016893720719,13.5236993961247,2.40784940440031),
(13.5358824143655,13.5372158823804,2.25651221004857),(13.6667206708126,
13.589251616489,2.22193019914187),(13.3730201656569,13.5187874395614,2.33107057548511),
(13.4371649501209,13.5093727649402,2.26321954804927),(13.6536332661809,
13.5103840257753,2.13307424307798),(13.4947653323605,13.4959190798489,2.20344701256072),
(13.2358290952677,13.516855496648,2.38090324324563),(13.3002241152642,13.5076597462379,
2.31255951930879),(13.1190636097232,13.5273825660528,2.41116652491496),
(13.239114012911,13.5026656097185,2.2834585228774),(13.3580492026369,13.4944026495107,
2.25208988062405),(13.4098985294028,13.4775125506272,2.19874244291222),
(13.8239709086434,13.4184625559819,1.96929435733676),(13.546413175473,13.4788527084374,
2.15097136206159),(13.7035866046632,13.4916362236244,2.08283073546156),
(13.5926538551581,13.4585650740867,2.10507419577025),(13.6708811256458,
13.4097311797593,2.03043243994733),(13.4563195988297,13.457382734319,2.15182579344809),
(13.5348520421051,13.4088158273021,2.07479742332883),(13.290042888014,13.4848150098167,
2.23028669255297),(13.3356398705487,13.4638252501449,2.18333629647007),
(13.4127781719419,13.4138035413698,2.10572186908828),(12.930152944292,13.573128313192,
2.4294171195737),(12.3303229882944,13.6956613655564,2.92979290035981),(11.8425792894748,
13.6273102162075,3.49899902114083),(11.8604162193091,14.0363607684128,3.17677138439079),
(12.6254432007897,13.5927302710207,2.76905067364144),(12.7934977288383,
13.5923976502462,2.58079421702792),(12.4215767237618,13.8015284674579,2.64992542479507),
(12.5482886311358,13.7583280629971,2.4984266688136),(12.6595744462156,13.6723163395967,
2.54952056555428),(12.3405992850722,13.9055273227233,2.57304836190265),
(12.2665600475509,13.8310786880799,2.83595162712383),(12.4469043534274,
13.8409751991997,2.43996010140829),(12.651325682287,13.7042003601274,2.37576032666016),
(12.779992115164,13.6362578265097,2.41186903243457),(13.0406674161136,13.5385725914007,
2.30859708625678),(13.0875508715875,13.5165481008345,2.25793078011039),
(12.9883805287382,13.5575558308994,2.36554441493521),(13.182315461487,13.516993907535,
2.34351242185072),(12.8773751168859,13.5881245261721,2.30145501724411),
(13.1295259612804,13.4918214272933,2.21295239479532),(13.2005369567399,
13.4354778267544,2.13792289106664),(12.9556753767011,13.5304563857028,2.21352668188738),
(13.0182487553647,13.4653885217022,2.14405447327964),(12.8016515054281,
13.5720368893786,2.19815021988653),(12.7346528004524,13.6414543210191,2.27705265775394),
(12.5333473799175,13.7694851330382,2.33201259920487),(12.5701799926211,
13.7316618577271,2.28611816381673),(12.6952288628357,13.6737750349891,2.32368701822834),
(12.6032547404489,13.6926979729788,2.2449719171807),(12.8551932860851,13.4975796204486,
2.13552905586697),(12.6594633640878,13.6119908562893,2.17522874805965),
(12.7043822889983,13.5285114750543,2.11971973691218),(13.0679319432845,
13.3947045242583,2.08963755456336),(14.533428398913,13.1795744792039,1.63215333628101),
(14.1585417665015,13.1531689742611,1.78953309053348),(13.8840454349469,
13.3586462008699,1.91849995156291),(14.0535130603027,13.3731317869537,1.84395987643656),
(15.000035623731,13.2327533204278,1.38826308655121),(14.4925692883778,13.350065676666,
1.61781602913878),(14.0974388047724,13.3034385437283,1.81528486496535),
(13.8501088010673,13.1440188188042,1.88720496614555),(13.5972067082648,
13.3512335699941,2.01653402875193),(13.7329934849766,13.3519369348458,1.97488264038663),
(13.7819741649667,13.2872810979302,1.93454781119327),(13.4740258911122,
13.3550661732035,2.04633894000483),(13.5223247315543,13.2896666063969,2.00160560741919),
(13.5895108938999,13.1453697608768,1.94466972935746),(13.6523040078462,
12.8335195930604,1.90583285096427),(12.8072967131363,13.1834223182122,1.99412051384754),
(12.8438621951252,12.8443365047929,1.95121229328192),(13.1071112041369,
13.3198840622884,2.04742017805106),(12.7681730854841,13.3568602333825,2.04146472775149),
(12.740047861725,13.4432087549174,2.07585693341496),(12.4237798985043,13.5801652441942,
2.08246397784229),(12.4673024777329,13.3871179843897,2.02217309359872),
(11.7461925764,14.607617029771,2.50223060800459),(11.8153057175885,14.5331467301085,
2.58475943801155),(11.7688353492531,14.603664553905,2.65714833184057),(12.3857795572026,
13.8720364361249,2.33181249825477),(12.4923884848816,13.8059882477724,2.38312399698475),
(11.9240796315519,14.3798670383732,2.60436003650895),(11.978837091907,14.2760935422296,
2.70240827047488),(11.8479667380208,14.3710884968873,2.8851365413869),(12.2600030703034,
13.9989676137091,2.4976159360014),(11.8621108674589,14.216455602915,3.02357048236165),
(12.0868512620062,14.1784891363736,2.55166969493561),(12.3480902204701,
13.9152315209277,2.38145868649049),(12.1755814830183,14.079197247439,2.42791480007045),
(12.2136959307614,14.029164957004,2.37478186738116),(12.3062935435019,13.9575913414104,
2.43655499053734),(12.248110296182,13.9789894291223,2.32682424727633),(12.1314033764891,
14.1141835009879,2.39582789088013),(11.8921899676642,14.4242363087772,2.56012874564545),
(11.8164646355547,14.49955386278,2.76317970523666),(12.0509513252901,14.2222628369328,
2.51122505581584),(11.9727991669186,14.2975389746538,2.43788695580198),
(12.0128999345289,14.2618831367799,2.47331121001068),(11.8959672594804,
14.3960864008824,2.45911987207996),(11.9308087161538,14.3294671658109,2.40485538716231),
(12.039017072022,14.1743481662285,2.33748927863294),(12.0858630013772,14.1458501373112,
2.36569013147524),(12.1455036129177,14.0319579974158,2.2777492581996),(12.1971655454941,
14.0067927511386,2.30154641188353),(12.0931441328199,14.0546051743623,2.25544225408308),
(11.9909713939338,14.199866863001,2.31116336677145),(12.0401488620655,14.074884697656,
2.23459052741996),(12.1710216945792,13.9758602200858,2.24013335186282),
(12.4776462259671,13.7387390735089,2.2109918279605),(12.524222156426,13.6478884657837,
2.14988465037431),(12.4502396279214,13.7836716759803,2.2470022353999),(12.2791427777297,
13.9287466828947,2.28359613514099),(12.4197191651019,13.8281418188465,2.28714035887972),
(12.30708782392,13.8785055791533,2.24468502573111),(12.3547835544171,13.7782708492887,
2.17831780575781),(12.3931330407133,13.6787108447716,2.12501142633752),
(12.2332212809541,13.811112917425,2.1484843417196),(12.1940009709746,13.9203466048843,
2.20626692406418),(12.0801380107128,13.9542553403318,2.17233560642426),
(12.1124776446352,13.8377287324182,2.12199972841929),(12.1592599558925,
13.6160553988921,2.04921018923719),(11.9326264121149,14.1090268011795,2.1969699106937),
(11.6083523435194,14.6241122791749,2.32397433893474),(11.6974049671818,
14.5833299275111,2.38043840871095),(11.635973309771,14.7514287197525,2.48555194025822),
(11.8918497896572,14.2428295093303,2.26373054991083),(11.8130749386999,
14.4560891280963,2.3927131420975),(11.8575707411185,14.4639669849143,2.51892500034024),
(11.7076606076534,14.6864698469836,2.56532748164996),(11.781237587319,14.5308565679723,
2.44484499954633),(11.8419645293727,14.3832206782589,2.34540930122912),
(11.7481077374884,14.4253890885883,2.29368422014272),(11.7897584580476,
14.2765327301384,2.22241983315769),(11.8238040999396,14.1358105063799,2.16416907329028),
(11.650966199778,14.4581624226053,2.24832002898602),(11.5567212432305,14.8019148257734,
2.41563786541225),(11.5520600977271,14.4835046758111,2.2080648076311),(11.6057773171257,
14.1727232271479,2.10981012029962),(11.7145862720005,14.1566268309671,2.13537348169738),
(11.6642948607649,13.6526728463698,2.00277454009263),(11.9925994764413,
13.8588978394256,2.09879113120188),(11.8738774348103,13.8755045233416,2.078498862641),
(11.9062649056675,13.6388577407974,2.023090400726),(11.6959947478853,12.8560448718688,
1.94470719518674),(12.2068688882948,13.2124636414598,1.97520119698613),
(12.2237843360063,12.8525918613881,1.94893485242747),(12.1887700245075,
13.4081417234614,2.00332316314824),(12.2899571349674,13.5999894768101,2.06496251959521),
(11.6992944073422,11.6992695746619,1.93491247773865),(13.6762365519101,
12.2294750077673,1.91429965155857),(12.857798516386,12.2299115091723,1.93685498967748),
(12.8583807386631,11.6992665053837,1.93580365244039),(14.2374150698324,
12.2295644726505,1.87166875248382),(10.8047895866783,12.8578219400876,1.93975016257589),
(10.0469155184881,12.8583126409832,1.93775230633148),(10.0453258580705,
11.699270169144,1.9440614342805),(10.7958369236004,14.2230751569525,1.96638317674362),
(10.7826097490432,14.9630728254268,2.00575411573049),(11.2132738678282,
13.6659811255007,1.9723412805636),(10.8016438707495,13.6715177651824,1.94910646012619),
(11.1590576001555,14.5394882115552,2.08002463283062),(11.1206179251257,
14.927194188594,2.15637327722914),(11.1850063086191,14.208281753204,2.02848560716361),
(11.3535755867932,14.5182596818971,2.13885035132236),(11.2969664560346,
14.8944924256782,2.24699216227574),(11.699295272555,10.8050920207757,1.91213522891102),
(15.000035623731,10.8050920207757,1.65991427644428),(12.8583845456102,10.8050920207757,
1.92430314138332),(12.8583845456102,10.0453003501022,1.88949283013031),
(13.6772429272632,10.0453003501022,1.84668656229681),(8.72455891328659,
10.8050920207757,2.14805762267117),(8.72455891328659,10.0453003501022,2.20636448310843),
(10.0453254412413,10.0453003501022,1.88186820671964),(8.74506523593222,
13.6788258710492,1.91346859387673),(8.85744437184725,15.0104309508258,1.76795732981976),
(9.36536575986113,12.8585011384936,1.94002504453861),(8.72649265121465,
12.8585110915138,1.94836033978817),(8.72455942020439,11.6992702211666,2.03142080806639),
(9.38733626299522,13.678720324253,1.90800675069672),(7.50060340825157,12.8584065296894,
1.98599855020476),(7.50003577257228,11.6992701937563,2.25388271474995),
(7.50003562373098,10.8050920207757,2.55632790746336),(7.50003562373098,
10.4127421564049,2.63616884884636),(7.50003562373098,10.0453003501021,2.65323158978436),
(8.72455891328659,9.36305314677202,2.16369291232617),(8.72455891328659,
8.72453382214744,2.04647741234648),(10.0453254412413,8.72453382214744,1.70575074250666),
(15.000035623731,8.72453382214744,1.45380716274449),(14.2383303254824,8.72453382214744,
1.69807642675316),(11.699295272555,9.36305314677203,1.76617088135234),(11.699295272555,
10.0453003501022,1.8539517581471),(10.8051171119148,10.0453003501022,1.83215692995024),
(15.000035623731,9.36305314677204,1.40744635914911),(12.8583845456102,9.36305314677203,
1.82196961618472),(13.6772429272632,9.36305314677203,1.80097979296644),
(11.699295272555,8.72453382214744,1.67971094267248),(12.8583845456102,8.72453382214744,
1.73100863037589),(13.6772429272632,8.72453382214744,1.75519357217684),
(10.8051171119148,8.72453382214744,1.64686856073339),(7.50003562373098,
9.69693841665379,2.61388390036061),(7.50003562373098,9.36305314677201,2.53189690652448),
(7.50003562373097,8.72453382214743,2.30228804043007),(4.19493855297138,
14.5867475595859,1.96666797753253),(4.19494423811177,14.238299020531,2.05139891948743),
(4.95479190162863,14.2383090258343,1.98170341520394),(6.89207594143154,
9.69693841665378,2.7247021394335),(4.95476305807623,13.677219255143,2.05805199812614),
(6.27613551971635,13.6772695686451,1.95196123811898),(6.89207594143154,
10.8050920207757,2.74823858865061),(6.2755711003197,12.8583643328268,2.03456512649748),
(6.89207594143154,10.4127421564049,2.81831284591867),(6.27551233417536,
10.8050920207757,2.84635522542801),(6.27551233417536,10.0453003501021,2.84331829639834),
(6.89207594143154,10.0453003501021,2.80458140229646),(5.96012527991924,
10.8050920207757,2.84061626212418),(6.27551234958054,11.6992701826946,2.46601202596288),
(5.6369930126946,11.6992701816768,2.47214801831906),(4.95474587186964,12.2304773911724,
2.30128927425112),(4.95474743306365,12.8583595882839,2.18899055166305),
(5.303107739669,10.8050920207757,2.70546887146747),(5.63699300955078,10.8050920207757,
2.79291446238769),(5.96012527991924,10.4127421564049,2.87286796062249),
(6.27551233417536,10.4127421564049,2.89242073415194),(5.63699300955078,
10.4127421564049,2.8115492261006),(5.63699300955078,10.0453003501021,2.74200302063899),
(4.95474580622066,10.8050920207757,2.58733127891833),(4.95474580622066,
10.4127421564049,2.57918528672864),(5.30310774089833,11.6992701815179,2.4493954342546),
(6.27551233417535,9.363053146772,2.57604479231308),(6.27551233417534,8.72453382214743,
2.26539560343584),(6.89207594143153,9.363053146772,2.60260094725354),(5.63699300955077,
8.72453382214743,2.18248372175319),(4.95474580622064,8.72453382214744,2.09108316328377),
(5.63699300955077,9.36305314677201,2.4646298514956),(4.95474580622066,10.0453003501021,
2.51265586093137),(4.19495413554711,10.0453003501022,2.2349037276524),(4.1949541355471,
8.72453382214744,2.00716478253043),(3.29997301489355,14.9996198822169,1.87788781644224),
(2.76677498171254,14.9986516189921,1.8225796377953),(2.76775516712611,14.5858751963915,
1.94026814482162),(4.58730399991791,11.2301561202153,2.44066592926588),
(4.58730399991791,10.8050920207757,2.45437934636001),(4.19493013116691,
14.9999954621514,1.92604399095266),(3.30025472898812,14.5865037498012,1.96741189980975),
(4.58730401753221,12.2304773863583,2.36825967268655),(4.19495290671879,
13.2327525489442,2.39756643684222),(4.19495378623742,12.8583592351672,2.47512429602994),
(4.58730400003234,11.6992701814196,2.4058809388772),(4.95474580664712,11.6992701814509,
2.42213785946051),(4.19495413545555,11.6992701813583,2.41598718201081),
(4.19495405464108,12.5288259546746,2.4942538531322),(3.30076429030096,12.8583537697599,
2.89447139722872),(3.30065206550949,13.6771575526083,2.51055562715467),
(3.30073486990841,13.2327333223172,2.77993677791185),(4.19495043129256,
13.6772155105173,2.24790699184106),(3.30044490013042,14.2381441622059,2.13553799115554),
(4.19495412145122,12.2304773769228,2.47853014259128),(3.76988992779578,
12.230477332703,2.62882800203533),(3.76989003540394,11.6992701810711,2.45910794164967),
(3.300775503391,12.2304771563739,2.78652181977866),(3.30077597184394,11.6992701799256,
2.52104210211781),(4.19495413554711,11.2301561202153,2.36464824184457),
(4.19495413554711,10.8050920207757,2.32686067359109),(3.76989003610754,
10.8050920207757,2.22393408549402),(3.30077597490697,10.8050920207757,2.15571221509097),
(3.30077597490697,10.0453003501022,2.0338975271717),(3.30077597490697,11.2301561202153,
2.2967108302355),(3.76989003610755,11.2301561202153,2.31625801185991),(2.76956875989281,
11.699270176232,2.55364011839959),(2.14168667241697,11.6992701670987,2.47706008971196),
(2.14168670185164,11.2301561202152,2.23291636418528),(2.13667771814722,
14.5843209583749,1.87843859114071),(2.13397054815702,14.9962573729492,1.74104878620035),
(1.30858440873506,14.993082252866,1.68322356301754),(3.04447394259517,12.6890229486301,
2.97689646201333),(3.04447984877054,12.5288234863927,2.95299455547085),
(3.30077326855424,12.5288246887936,2.88062318729346),(3.04440637010482,
13.2327150598463,2.84945247513237),(2.76841684143832,14.2377449299584,2.14120403929022),
(3.04444215934266,13.0387126233211,2.93120431404288),(3.0444626682149,12.8583485784258,
2.97186417237722),(2.76942575136586,13.2326837551435,2.88274696607814),
(2.76949082525401,13.0386955622929,2.96765730150351),(2.62352484966811,
13.2326614034743,2.88023666941411),(2.76913764485804,13.6770081343175,2.57602712188558),
(2.47047874109866,13.6768572120769,2.55723636810324),(2.13850519366002,
14.2367577394876,2.07867808451582),(2.31070307814832,13.2326002311307,2.82554873052903),
(2.47097419979957,13.232633689058,2.86187258893387),(2.47115023621386,12.8583254477693,
2.98683862012589),(2.62366010443052,12.8583333259361,3.00689489251454),
(2.31092834766835,12.8583159369382,2.94687334218668),(2.14157441703732,
12.8583048388101,2.88526770850109),(2.76954861524742,12.6890185369439,3.01365496426516),
(2.76952811557196,12.8583396796755,3.0093637165552),(2.76956712997526,12.2304765877882,
2.87289959231811),(2.76955935416835,12.5288214252913,2.98757054985363),
(2.47121732954586,12.2304760134795,2.8508688271129),(2.1416821707545,12.2304751818336,
2.76401069183021),(2.14166069478594,12.5288133555564,2.86652165047505),
(2.62370138027385,12.5288199536576,2.98459168802064),(2.47120395743838,
12.5288181289406,2.96477990834091),(1.32262104400658,12.8582586347318,2.44124315037782),
(0.756349679342826,14.2356829183347,1.89652776085301),(1.31695530071299,
14.235448580682,1.94411569262859),(2.14049597760728,13.6766386643817,2.48809672371908),
(1.32063025994913,13.6761486932624,2.19430971316014),(1.32209914819651,
13.2323986490573,2.36267028121065),(2.14129169776506,13.2325611892889,2.76964661902818),
(0.759723173500355,13.676236397429,2.03244363778357),(0.404530643479107,
14.9957505332811,1.80867143143908),(0.409677663442069,14.2365487604263,
1.90324476319084),(0.411937289847635,13.6765604510251,1.98853561254384),
(3.56237310100823E-5,15.0000105325919,1.93875932375063),(0.413161365527088,
12.8582974633222,2.15449777256329),(0.761550649114403,12.8582669051992,
2.2062696231256),(3.56237310051037E-5,13.6772178361241,2.00103320073166),
(1.32278031152005,12.528802653909,2.44869550815785),(1.32282736561586,11.9555606644331,
2.34824765106328),(1.32282826586249,11.6992701549866,2.27888839679454),
(1.32281995585671,12.2304733173324,2.41025517006299),(0.761733243783124,
12.2304736510756,2.26308223432949),(0.413259294525986,12.5288116472685,
2.22166068878197),(0.41328367068479,12.2304748842061,2.26941926869125),
(3.56237309890404E-5,12.8583594544711,2.20001415123069),(3.56237309808271E-5,
12.5288260054691,2.3030341781359),(3.56237309741178E-5,12.2304773857726,
2.38426107184719),(0.76174004570325,11.9555607025216,2.25990210518771),
(3.5623730968287E-5,11.6992701814159,2.45681587818134),(0.413288780267586,
11.6992701651653,2.30187252931774),(1.32282832019849,11.2301561202152,2.15388047300453),
(2.14168670185174,10.8050920207757,2.08052576733846),(2.14168670185174,
10.0453003501022,1.96655734966022),(1.32282832019869,10.0453003501022,1.99896147936414),
(0.761740872100653,11.6992701571546,2.24323197845462),(1.3228283201987,
10.8050920207757,2.06808509548503),(3.56237309762301E-5,10.8050920207757,
2.35869657813387),(0.761740921979543,10.8050920207757,2.13438295226053),
(0.761740921979545,10.0453003501022,2.07802535735097),(3.56237309835803E-5,
10.0453003501022,2.26880603971376),(2.14168670185174,8.72453382214744,1.94249982085887),
(3.30077597490697,8.72453382214744,1.95117125580154),(1.32282832019869,
8.72453382214744,2.00328105613453),(3.56237309760984E-5,8.72453382214744,
2.36206282478682),(3.56237309854124E-5,7.50001053259181,2.24602824640092),
(3.56237310092759E-5,14.2383052343433,1.94935008860201),(0.748665470249253,
14.9936505950595,1.73330177847375),(4.95485760202037,15.000019728145,1.93687970171871),
(11.8663360411557,13.4334823669781,3.63531004811174),(7.50003562373097,
1.05325917586015E-5,1.93875932375063),(11.699295272555,1.05325917596929E-5,
1.93875420256786),(13.6772429272632,1.05325917596929E-5,1.73159234851917),
(15.000035623731,1.05325917596929E-5,0.596271985674758),(15.000035623731,
3.30075088376783,1.92804784099292),(15.000035623731,6.27548724303617,1.4757441040531)));
#1035=IFCPOLYGONALFACESET(#1034,.F.,(#26,#27,#28,#29,#30,#31,#32,#33,#34,
#35,#36,#37,#38,#39,#40,#41,#42,#43,#44,#45,#46,#47,#48,#49,#50,#51,#52,
#53,#54,#55,#56,#57,#58,#59,#60,#61,#62,#63,#64,#65,#66,#67,#68,#69,#70,
#71,#72,#73,#74,#75,#76,#77,#78,#79,#80,#81,#82,#83,#84,#85,#86,#87,#88,
#89,#90,#91,#92,#93,#94,#95,#96,#97,#98,#99,#100,#101,#102,#103,#104,#105,
#106,#107,#108,#109,#110,#111,#112,#113,#114,#115,#116,#117,#118,#119,#120,
#121,#122,#123,#124,#125,#126,#127,#128,#129,#130,#131,#132,#133,#134,#135,
#136,#137,#138,#139,#140,#141,#142,#143,#144,#145,#146,#147,#148,#149,#150,
#151,#152,#153,#154,#155,#156,#157,#158,#159,#160,#161,#162,#163,#164,#165,
#166,#167,#168,#169,#170,#171,#172,#173,#174,#175,#176,#177,#178,#179,#180,
#181,#182,#183,#184,#185,#186,#187,#188,#189,#190,#191,#192,#193,#194,#195,
#196,#197,#198,#199,#200,#201,#202,#203,#204,#205,#206,#207,#208,#209,#210,
#211,#212,#213,#214,#215,#216,#217,#218,#219,#220,#221,#222,#223,#224,#225,
#226,#227,#228,#229,#230,#231,#232,#233,#234,#235,#236,#237,#238,#239,#240,
#241,#242,#243,#244,#245,#246,#247,#248,#249,#250,#251,#252,#253,#254,#255,
#256,#257,#258,#259,#260,#261,#262,#263,#264,#265,#266,#267,#268,#269,#270,
#271,#272,#273,#274,#275,#276,#277,#278,#279,#280,#281,#282,#283,#284,#285,
#286,#287,#288,#289,#290,#291,#292,#293,#294,#295,#296,#297,#298,#299,#300,
#301,#302,#303,#304,#305,#306,#307,#308,#309,#310,#311,#312,#313,#314,#315,
#316,#317,#318,#319,#320,#321,#322,#323,#324,#325,#326,#327,#328,#329,#330,
#331,#332,#333,#334,#335,#336,#337,#338,#339,#340,#341,#342,#343,#344,#345,
#346,#347,#348,#349,#350,#351,#352,#353,#354,#355,#356,#357,#358,#359,#360,
#361,#362,#363,#364,#365,#366,#367,#368,#369,#370,#371,#372,#373,#374,#375,
#376,#377,#378,#379,#380,#381,#382,#383,#384,#385,#386,#387,#388,#389,#390,
#391,#392,#393,#394,#395,#396,#397,#398,#399,#400,#401,#402,#403,#404,#405,
#406,#407,#408,#409,#410,#411,#412,#413,#414,#415,#416,#417,#418,#419,#420,
#421,#422,#423,#424,#425,#426,#427,#428,#429,#430,#431,#432,#433,#434,#435,
#436,#437,#438,#439,#440,#441,#442,#443,#444,#445,#446,#447,#448,#449,#450,
#451,#452,#453,#454,#455,#456,#457,#458,#459,#460,#461,#462,#463,#464,#465,
#466,#467,#468,#469,#470,#471,#472,#473,#474,#475,#476,#477,#478,#479,#480,
#481,#482,#483,#484,#485,#486,#487,#488,#489,#490,#491,#492,#493,#494,#495,
#496,#497,#498,#499,#500,#501,#502,#503,#504,#505,#506,#507,#508,#509,#510,
#511,#512,#513,#514,#515,#516,#517,#518,#519,#520,#521,#522,#523,#524,#525,
#526,#527,#528,#529,#530,#531,#532,#533,#534,#535,#536,#537,#538,#539,#540,
#541,#542,#543,#544,#545,#546,#547,#548,#549,#550,#551,#552,#553,#554,#555,
#556,#557,#558,#559,#560,#561,#562,#563,#564,#565,#566,#567,#568,#569,#570,
#571,#572,#573,#574,#575,#576,#577,#578,#579,#580,#581,#582,#583,#584,#585,
#586,#587,#588,#589,#590,#591,#592,#593,#594,#595,#596,#597,#598,#599,#600,
#601,#602,#603,#604,#605,#606,#607,#608,#609,#610,#611,#612,#613,#614,#615,
#616,#617,#618,#619,#620,#621,#622,#623,#624,#625,#626,#627,#628,#629,#630,
#631,#632,#633,#634,#635,#636,#637,#638,#639,#640,#641,#642,#643,#644,#645,
#646,#647,#648,#649,#650,#651,#652,#653,#654,#655,#656,#657,#658,#659,#660,
#661,#662,#663,#664,#665,#666,#667,#668,#669,#670,#671,#672,#673,#674,#675,
#676,#677,#678,#679,#680,#681,#682,#683,#684,#685,#686,#687,#688,#689,#690,
#691,#692,#693,#694,#695,#696,#697,#698,#699,#700,#701,#702,#703,#704,#705,
#706,#707,#708,#709,#710,#711,#712,#713,#714,#715,#716,#717,#718,#719,#720,
#721,#722,#723,#724,#725,#726,#727,#728,#729,#730,#731,#732,#733,#734,#735,
#736,#737,#738,#739,#740,#741,#742,#743,#744,#745,#746,#747,#748,#749,#750,
#751,#752,#753,#754,#755,#756,#757,#758,#759,#760,#761,#762,#763,#764,#765,
#766,#767,#768,#769,#770,#771,#772,#773,#774,#775,#776,#777,#778,#779,#780,
#781,#782,#783,#784,#785,#786,#787,#788,#789,#790,#791,#792,#793,#794,#795,
#796,#797,#798,#799,#800,#801,#802,#803,#804,#805,#806,#807,#808,#809,#810,
#811,#812,#813,#814,#815,#816,#817,#818,#819,#820,#821,#822,#823,#824,#825,
#826,#827,#828,#829,#830,#831,#832,#833,#834,#835,#836,#837,#838,#839,#840,
#841,#842,#843,#844,#845,#846,#847,#848,#849,#850,#851,#852,#853,#854,#855,
#856,#857,#858,#859,#860,#861,#862,#863,#864,#865,#866,#867,#868,#869,#870,
#871,#872,#873,#874,#875,#876,#877,#878,#879,#880,#881,#882,#883,#884,#885,
#886,#887,#888,#889,#890,#891,#892,#893,#894,#895,#896,#897,#898,#899,#900,
#901,#902,#903,#904,#905,#906,#907,#908,#909,#910,#911,#912,#913,#914,#915,
#916,#917,#918,#919,#920,#921,#922,#923,#924,#925,#926,#927,#928,#929,#930,
#931,#932,#933,#934,#935,#936,#937,#938,#939,#940,#941,#942,#943,#944,#945,
#946,#947,#948,#949,#950,#951,#952,#953,#954,#955,#956,#957,#958,#959,#960,
#961,#962,#963,#964,#965,#966,#967,#968,#969,#970,#971,#972,#973,#974,#975,
#976,#977,#978,#979,#980,#981,#982,#983,#984,#985,#986,#987,#988,#989,#990,
#991,#992,#993,#994,#995,#996,#997,#998,#999,#1000,#1001,#1002,#1003,#1004,
#1005,#1006,#1007,#1008,#1009,#1010,#1011,#1012,#1013,#1014,#1015,#1016,
#1017,#1018,#1019,#1020,#1021,#1022,#1023,#1024,#1025,#1026,#1027,#1028,
#1029,#1030,#1031,#1032,#1033),$);
#1036=IFCRELAGGREGATES('1kB3AQvfGpkFm_7uHNRdoP',#1117,$,$,#1106,(#1055));
#1037=IFCRELAGGREGATES('0Cp_wDOOr3amQ16IWrGpUy',#1117,$,$,#1055,(#20));
#1038=IFCRELAGGREGATES('1NfacxJUhl1S_ka3CQXVRt',#1117,$,$,#20,(#19));
#1039=IFCLOCALPLACEMENT($,#1043);
#1040=IFCLOCALPLACEMENT(#1039,#1044);
#1041=IFCLOCALPLACEMENT(#1040,#1045);
#1042=IFCLOCALPLACEMENT(#1041,#1046);
#1043=IFCAXIS2PLACEMENT3D(#1048,#1052,#1053);
#1044=IFCAXIS2PLACEMENT3D(#1048,#1052,#1053);
#1045=IFCAXIS2PLACEMENT3D(#1049,#1052,#1053);
#1046=IFCAXIS2PLACEMENT3D(#1050,#1052,#1053);
#1047=IFCAXIS2PLACEMENT3D(#1048,#1052,#1053);
#1048=IFCCARTESIANPOINT((0.,0.,0.));
#1049=IFCCARTESIANPOINT((0.,0.,9.144));
#1050=IFCCARTESIANPOINT((89.1421,-68.3287,38.6153));
#1051=IFCCARTESIANPOINT((3.5623730961E-5,1.053259176E-5,0.59627198567));
#1052=IFCDIRECTION((0.,0.,1.));
#1053=IFCDIRECTION((1.,0.,0.));
#1054=IFCDIRECTION((0.,1.));
#1055=IFCSITE('006_i1V1D0tAKEh_$vljQt',#1117,'Site 1','Site 1',$,#1039,
$,'Site 1',.ELEMENT.,$,$,0.,$,#1108);
#1056=IFCRELDEFINESBYPROPERTIES('1RL1EewHTsROrPcNSlBI56',#1117,
'Project',$,(#1106),#1066);
#1057=IFCRELDEFINESBYPROPERTIES('3bjMWmv_Cp6LPxmaCnPGwW',#1117,'Site',$,
(#1055),#1067);
#1058=IFCRELDEFINESBYPROPERTIES('2zOmgYUoQmhp9sZMdCZF7s',#1117,
'Pset_BuildingCommon',$,(#20),#1068);
#1059=IFCRELDEFINESBYPROPERTIES('3k0v4EdaEvPD8NSrRFB2MT',#1117,
'ArchBuilding',$,(#20),#1069);
#1060=IFCRELDEFINESBYPROPERTIES('3uIgchz4n8nDCCMgq1BBql',#1117,
'ObjectIdentity',$,(#20),#1070);
#1061=IFCRELDEFINESBYPROPERTIES('1CCmZ_1eEDpfMO62t1jkZj',#1117,
'ObjectPostalAddress',$,(#20),#1071);
#1062=IFCRELDEFINESBYPROPERTIES('24I$F7L8gfSwdAGDvfkVEu',#1117,
'Pset_BuildingStoreyCommon',$,(#19),#1072);
#1063=IFCRELDEFINESBYPROPERTIES('1K1DvjfvPzx3hE5w5ZHK0q',#1117,
'ArchFloor',$,(#19),#1073);
#1064=IFCRELDEFINESBYPROPERTIES('0lrK04aZ0v6OfwsyTXPzZq',#1117,'Floor',
$,(#19),#1074);
#1065=IFCRELDEFINESBYPROPERTIES('1fbDyx4oFQKKacC_b9xURq',#1117,
'StructuralFloorCommon',$,(#19),#1075);
#1066=IFCPROPERTYSET('2rcLYSOKrF02Xv$qFtEF4e',#1117,'Project',$,(#1076));
#1067=IFCPROPERTYSET('2Qswsgonw96KTM1$UoUklF',#1117,'Site',$,(#1077,#1078));
#1068=IFCPROPERTYSET('2Ct1rxyLMs3vgB_jB90hMr',#1117,
'Pset_BuildingCommon',$,(#1079));
#1069=IFCPROPERTYSET('3OmKLTEF7bgCV0$1Gr7ajZ',#1117,'ArchBuilding',$,(#1080,
#1081));
#1070=IFCPROPERTYSET('1HjNcqn7ak9HvLTK8j8w1d',#1117,'ObjectIdentity',$,
(#1082));
#1071=IFCPROPERTYSET('306EzywKpRfIeis4axlC1d',#1117,
'ObjectPostalAddress',$,(#1083,#1084,#1085,#1086));
#1072=IFCPROPERTYSET('381uKge8G33qXIbHr84hB4',#1117,
'Pset_BuildingStoreyCommon',$,(#1087,#1088,#1089,#1090));
#1073=IFCPROPERTYSET('0GZmVQOAmJ6zMOk6j3uleN',#1117,'ArchFloor',$,(#1091,
#1092,#1093,#1094,#1095,#1096,#1097,#1098,#1099,#1100));
#1074=IFCPROPERTYSET('0A3C_vQUNRQZfA6f5qtBlE',#1117,'Floor',$,(#1101,#1102,
#1103));
#1075=IFCPROPERTYSET('0izo3etGuCpkjROydkcZbT',#1117,
'StructuralFloorCommon',$,(#1104));
#1076=IFCPROPERTYSINGLEVALUE('ProjectName',$,IFCTEXT(
'BuildingTemplate_US'),$);
#1077=IFCPROPERTYSINGLEVALUE('SiteName',$,IFCTEXT('Site 1'),$);
#1078=IFCPROPERTYSINGLEVALUE('BuildingHeightLimit',$,
IFCLENGTHMEASURE(0.),#1253);
#1079=IFCPROPERTYSINGLEVALUE('YearOfConstruction',$,IFCLABEL('2006'),$);
#1080=IFCPROPERTYSINGLEVALUE('BuildingName',$,IFCTEXT('Bldg 1'),$);
#1081=IFCPROPERTYSINGLEVALUE('YearConstructed',$,IFCTEXT('2006'),$);
#1082=IFCPROPERTYSINGLEVALUE('Description',$,IFCTEXT('3 Story Building'),
$);
#1083=IFCPROPERTYSINGLEVALUE('Address1',$,IFCTEXT(
'203 Rickenhouse Drive'),$);
#1084=IFCPROPERTYSINGLEVALUE('City',$,IFCTEXT('Madison'),$);
#1085=IFCPROPERTYSINGLEVALUE('PostalCode',$,IFCTEXT('35789'),$);
#1086=IFCPROPERTYSINGLEVALUE('Country',$,IFCTEXT('US'),$);
#1087=IFCPROPERTYSINGLEVALUE('GrossAreaPlanned',$,IFCAREAMEASURE(22296729.6),
#1254);
#1088=IFCPROPERTYSINGLEVALUE('GrossPlannedArea',$,IFCAREAMEASURE(22296729.6),
$);
#1089=IFCPROPERTYSINGLEVALUE('NetAreaPlanned',$,IFCAREAMEASURE(22296729.6),
#1254);
#1090=IFCPROPERTYSINGLEVALUE('NetPlannedArea',$,IFCAREAMEASURE(22296729.6),
$);
#1091=IFCPROPERTYSINGLEVALUE('FloorName',$,IFCTEXT('Floor 3'),$);
#1092=IFCPROPERTYSINGLEVALUE('FinishedFloorElevation',$,IFCREAL(9.144),
$);
#1093=IFCPROPERTYSINGLEVALUE('Description',$,IFCTEXT(
'Administrative and employee lounge'),$);
#1094=IFCPROPERTYSINGLEVALUE('ApproxLength',$,IFCLENGTHMEASURE(6096.),#1253);
#1095=IFCPROPERTYSINGLEVALUE('ApproxWidth',$,IFCLENGTHMEASURE(3657.6),#1253);
#1096=IFCPROPERTYSINGLEVALUE('TypicalFloor',$,IFCBOOLEAN(.F.),$);
#1097=IFCPROPERTYSINGLEVALUE('TypicalFloorHeight',$,IFCLENGTHMEASURE(0.),
#1253);
#1098=IFCPROPERTYSINGLEVALUE('TypicalFloorBaseElevation',$,
IFCLENGTHMEASURE(0.),#1253);
#1099=IFCPROPERTYSINGLEVALUE('ACSRotation',$,IFCTEXT('0.00000000'),$);
#1100=IFCPROPERTYSINGLEVALUE('Annotation',$,IFCBOOLEAN(.T.),$);
#1101=IFCPROPERTYSINGLEVALUE('GrossArea',$,IFCAREAMEASURE(22296729.6),#1254);
#1102=IFCPROPERTYSINGLEVALUE('NetArea',$,IFCAREAMEASURE(22296729.6),#1254);
#1103=IFCPROPERTYSINGLEVALUE('OccupancyType',$,IFCTEXT('Commercial'),$);
#1104=IFCPROPERTYSINGLEVALUE('issplicefloor',$,IFCBOOLEAN(.F.),$);
#1105=IFCUNITASSIGNMENT((#1253,#1254,#1255,#1256,#1257,#1258,#1259,#1260,
#1261,#1262,#1263,#1264,#1265,#1266,#1267,#1268,#1269,#1270,#1271,#1272,
#1273,#1274,#1250,#1276,#1277,#1278,#1279,#1280,#1281,#1119,#1120,#1121,
#1122,#1123,#1124,#1125,#1126,#1127,#1128,#1129,#1130,#1131,#1132,#1133,
#1134,#1135,#1136,#1137,#1138,#1139,#1140,#1141,#1142,#1143,#1144,#1145,
#1146,#1147,#1148,#1149,#1150,#1151,#1152,#1153,#1154,#1155,#1156,#1157,
#1158,#1159,#1160,#1161,#1162,#1118));
#1106=IFCPROJECT('1JmBUThP19Z8wED88xGwCF',#1117,'BuildingTemplate_US',
'BuildingTemplate_US',$,'BuildingTemplate_US',$,(#14),#1105);
#1107=IFCPOSTALADDRESS($,$,$,$,('Address 1','Address 2'),$,'City/Town',
'State/Region','Postal Code','Country');
#1108=IFCPOSTALADDRESS($,$,$,'Site 1',$,$,$,$,$,$);
#1109=IFCPOSTALADDRESS($,$,$,$,('203 Rickenhouse Drive'),$,'Madison',$,
'35789','US');
#1110=IFCTELECOMADDRESS($,$,$,('Phone'),$,$,('E-Mail'),$,$);
#1111=IFCACTORROLE(.SUPPLIER.,$,$);
#1112=IFCPERSON($,'Last Name','First Name',$,$,$,(#1111),(#1110));
#1113=IFCPERSONANDORGANIZATION(#1112,#1115,$);
#1114=IFCORGANIZATION($,'Bentley Systems Inc.',$,$,$);
#1115=IFCORGANIZATION($,'Organization Name',$,$,(#1107));
#1116=IFCAPPLICATION(#1114,'***********','OpenBuildings Designer','ABD');
#1117=IFCOWNERHISTORY(#1113,#1116,$,$,0,$,$,1583400155);
#1118=IFCMONETARYUNIT('USD');
#1119=IFCDERIVEDUNIT((#1163,#1164),.ACCELERATIONUNIT.,
'(METRE)/(SECOND^2)');
#1120=IFCDERIVEDUNIT((#1165,#1166),.ANGULARVELOCITYUNIT.,
'(DEGREE)/(SECOND)');
#1121=IFCDERIVEDUNIT((#1167,#1168),.CURVATUREUNIT.,'(DEGREE)/(METRE)');
#1122=IFCDERIVEDUNIT((#1169,#1170),.DYNAMICVISCOSITYUNIT.,
'(PASCAL)(SECOND)');
#1123=IFCDERIVEDUNIT((#1171,#1172),.HEATFLUXDENSITYUNIT.,
'(WATT)/(METRE^2)');
#1124=IFCDERIVEDUNIT((#1173,#1174),.HEATINGVALUEUNIT.,'(JOULE)/(GRAM)');
#1125=IFCDERIVEDUNIT((#1175),.INTEGERCOUNTRATEUNIT.,'1/(SECOND)');
#1126=IFCDERIVEDUNIT((#1176,#1177),.IONCONCENTRATIONUNIT.,
'(GRAM)/(CUBIC_METRE)');
#1127=IFCDERIVEDUNIT((#1178,#1179),.ISOTHERMALMOISTURECAPACITYUNIT.,
'(CUBIC_METRE)/(GRAM)');
#1128=IFCDERIVEDUNIT((#1180,#1181),.KINEMATICVISCOSITYUNIT.,
'(SQUARE_METRE)/(SECOND)');
#1129=IFCDERIVEDUNIT((#1182,#1183),.LINEARFORCEUNIT.,'(NEWTON)/(METRE)');
#1130=IFCDERIVEDUNIT((#1184,#1185),.LINEARMOMENTUNIT.,
'(NEWTON)/(METRE)');
#1131=IFCDERIVEDUNIT((#1186,#1187),.LINEARSTIFFNESSUNIT.,
'(NEWTON)/(METRE)');
#1132=IFCDERIVEDUNIT((#1188,#1189),.LINEARVELOCITYUNIT.,
'(METRE)/(SECOND)');
#1133=IFCDERIVEDUNIT((#1190,#1191),.LUMINOUSINTENSITYDISTRIBUTIONUNIT.,
'(CANDELA)/(LUMEN)');
#1134=IFCDERIVEDUNIT((#1192,#1193),.MASSDENSITYUNIT.,
'(GRAM)/(CUBIC_METRE)');
#1135=IFCDERIVEDUNIT((#1194,#1195),.MASSFLOWRATEUNIT.,'(GRAM)/(SECOND)');
#1136=IFCDERIVEDUNIT((#1196,#1197),.MASSPERLENGTHUNIT.,'(GRAM)/(METRE)');
#1137=IFCDERIVEDUNIT((#1198,#1199),.MODULUSOFELASTICITYUNIT.,
'(NEWTON)/(SQUARE_METRE)');
#1138=IFCDERIVEDUNIT((#1200,#1201),
 .MODULUSOFLINEARSUBGRADEREACTIONUNIT.,'(NEWTON)/(METRE^2)');
#1139=IFCDERIVEDUNIT((#1202,#1203),
 .MODULUSOFROTATIONALSUBGRADEREACTIONUNIT.,'(NEWTON)(METRE)');
#1140=IFCDERIVEDUNIT((#1204,#1205),.MODULUSOFSUBGRADEREACTIONUNIT.,
'(NEWTON)/(CUBIC_METRE)');
#1141=IFCDERIVEDUNIT((#1206,#1207),.MOISTUREDIFFUSIVITYUNIT.,
'(CUBIC_METRE)/(SECOND)');
#1142=IFCDERIVEDUNIT((#1208,#1209),.MOLECULARWEIGHTUNIT.,
'(GRAM)/(MOLE)');
#1143=IFCDERIVEDUNIT((#1210,#1211),.PLANARFORCEUNIT.,
'(NEWTON)/(METRE^2)');
#1144=IFCDERIVEDUNIT((#1212),.ROTATIONALFREQUENCYUNIT.,'1/(SECOND)');
#1145=IFCDERIVEDUNIT((#1213,#1214),.ROTATIONALMASSUNIT.,
'(GRAM)(METRE^2)');
#1146=IFCDERIVEDUNIT((#1215,#1216,#1217),.ROTATIONALSTIFFNESSUNIT.,
'(NEWTON)(METRE)/(DEGREE)');
#1147=IFCDERIVEDUNIT((#1218),.SECTIONAREAINTEGRALUNIT.,'(METRE^5)');
#1148=IFCDERIVEDUNIT((#1219),.SECTIONMODULUSUNIT.,'(METRE^3)');
#1149=IFCDERIVEDUNIT((#1220,#1221),.SHEARMODULUSUNIT.,
'(NEWTON)/(SQUARE_METRE)');
#1150=IFCDERIVEDUNIT((#1222,#1223),.SOUNDPOWERUNIT.,'(JOULE)/(SECOND)');
#1151=IFCDERIVEDUNIT((#1224,#1225,#1226),.SPECIFICHEATCAPACITYUNIT.,
'(NEWTON)/(GRAM)(KELVIN)');
#1152=IFCDERIVEDUNIT((#1227,#1228),.TEMPERATUREGRADIENTUNIT.,
'(KELVIN)/(METRE)');
#1153=IFCDERIVEDUNIT((#1229,#1230,#1231),.THERMALADMITTANCEUNIT.,
'(WATT)/(SQUARE_METRE)(KELVIN)');
#1154=IFCDERIVEDUNIT((#1232,#1233,#1234),.THERMALCONDUCTANCEUNIT.,
'(WATT)/(METRE)(KELVIN)');
#1155=IFCDERIVEDUNIT((#1235),.THERMALEXPANSIONCOEFFICIENTUNIT.,
'1/(KELVIN)');
#1156=IFCDERIVEDUNIT((#1236,#1237),.THERMALRESISTANCEUNIT.,
'(SQUARE_METRE)/(WATT)');
#1157=IFCDERIVEDUNIT((#1238,#1239,#1240),.THERMALTRANSMITTANCEUNIT.,
'(WATT)/(SQUARE_METRE)(KELVIN)');
#1158=IFCDERIVEDUNIT((#1241,#1242),.TORQUEUNIT.,'(NEWTON)(METRE)');
#1159=IFCDERIVEDUNIT((#1243,#1244),.VAPORPERMEABILITYUNIT.,
'(GRAM)/(SECOND)');
#1160=IFCDERIVEDUNIT((#1245,#1246),.VOLUMETRICFLOWRATEUNIT.,
'(CUBIC_METRE)/(SECOND)');
#1161=IFCDERIVEDUNIT((#1247),.WARPINGCONSTANTUNIT.,'(METRE^6)');
#1162=IFCDERIVEDUNIT((#1248,#1249),.WARPINGMOMENTUNIT.,
'(NEWTON)(METRE^2)');
#1163=IFCDERIVEDUNITELEMENT(#1253,1);
#1164=IFCDERIVEDUNITELEMENT(#1281,-2);
#1165=IFCDERIVEDUNITELEMENT(#1250,1);
#1166=IFCDERIVEDUNITELEMENT(#1281,-1);
#1167=IFCDERIVEDUNITELEMENT(#1250,1);
#1168=IFCDERIVEDUNITELEMENT(#1253,-1);
#1169=IFCDERIVEDUNITELEMENT(#1277,1);
#1170=IFCDERIVEDUNITELEMENT(#1281,1);
#1171=IFCDERIVEDUNITELEMENT(#1276,1);
#1172=IFCDERIVEDUNITELEMENT(#1253,-2);
#1173=IFCDERIVEDUNITELEMENT(#1265,1);
#1174=IFCDERIVEDUNITELEMENT(#1274,-1);
#1175=IFCDERIVEDUNITELEMENT(#1281,-1);
#1176=IFCDERIVEDUNITELEMENT(#1274,1);
#1177=IFCDERIVEDUNITELEMENT(#1255,-1);
#1178=IFCDERIVEDUNITELEMENT(#1255,1);
#1179=IFCDERIVEDUNITELEMENT(#1274,-1);
#1180=IFCDERIVEDUNITELEMENT(#1254,1);
#1181=IFCDERIVEDUNITELEMENT(#1281,-1);
#1182=IFCDERIVEDUNITELEMENT(#1266,1);
#1183=IFCDERIVEDUNITELEMENT(#1253,-1);
#1184=IFCDERIVEDUNITELEMENT(#1266,1);
#1185=IFCDERIVEDUNITELEMENT(#1253,-1);
#1186=IFCDERIVEDUNITELEMENT(#1266,1);
#1187=IFCDERIVEDUNITELEMENT(#1253,-1);
#1188=IFCDERIVEDUNITELEMENT(#1253,1);
#1189=IFCDERIVEDUNITELEMENT(#1281,-1);
#1190=IFCDERIVEDUNITELEMENT(#1271,1);
#1191=IFCDERIVEDUNITELEMENT(#1270,-1);
#1192=IFCDERIVEDUNITELEMENT(#1274,1);
#1193=IFCDERIVEDUNITELEMENT(#1255,-1);
#1194=IFCDERIVEDUNITELEMENT(#1274,1);
#1195=IFCDERIVEDUNITELEMENT(#1281,-1);
#1196=IFCDERIVEDUNITELEMENT(#1274,1);
#1197=IFCDERIVEDUNITELEMENT(#1253,-1);
#1198=IFCDERIVEDUNITELEMENT(#1266,1);
#1199=IFCDERIVEDUNITELEMENT(#1254,-1);
#1200=IFCDERIVEDUNITELEMENT(#1266,1);
#1201=IFCDERIVEDUNITELEMENT(#1253,-2);
#1202=IFCDERIVEDUNITELEMENT(#1266,1);
#1203=IFCDERIVEDUNITELEMENT(#1253,1);
#1204=IFCDERIVEDUNITELEMENT(#1266,1);
#1205=IFCDERIVEDUNITELEMENT(#1255,-1);
#1206=IFCDERIVEDUNITELEMENT(#1255,1);
#1207=IFCDERIVEDUNITELEMENT(#1281,-1);
#1208=IFCDERIVEDUNITELEMENT(#1274,1);
#1209=IFCDERIVEDUNITELEMENT(#1257,-1);
#1210=IFCDERIVEDUNITELEMENT(#1266,1);
#1211=IFCDERIVEDUNITELEMENT(#1253,-2);
#1212=IFCDERIVEDUNITELEMENT(#1281,-1);
#1213=IFCDERIVEDUNITELEMENT(#1274,1);
#1214=IFCDERIVEDUNITELEMENT(#1253,2);
#1215=IFCDERIVEDUNITELEMENT(#1266,1);
#1216=IFCDERIVEDUNITELEMENT(#1253,1);
#1217=IFCDERIVEDUNITELEMENT(#1250,-1);
#1218=IFCDERIVEDUNITELEMENT(#1253,5);
#1219=IFCDERIVEDUNITELEMENT(#1253,3);
#1220=IFCDERIVEDUNITELEMENT(#1266,1);
#1221=IFCDERIVEDUNITELEMENT(#1254,-1);
#1222=IFCDERIVEDUNITELEMENT(#1265,1);
#1223=IFCDERIVEDUNITELEMENT(#1281,-1);
#1224=IFCDERIVEDUNITELEMENT(#1266,1);
#1225=IFCDERIVEDUNITELEMENT(#1274,-1);
#1226=IFCDERIVEDUNITELEMENT(#1280,-1);
#1227=IFCDERIVEDUNITELEMENT(#1280,1);
#1228=IFCDERIVEDUNITELEMENT(#1253,-1);
#1229=IFCDERIVEDUNITELEMENT(#1276,1);
#1230=IFCDERIVEDUNITELEMENT(#1254,-1);
#1231=IFCDERIVEDUNITELEMENT(#1280,-1);
#1232=IFCDERIVEDUNITELEMENT(#1276,1);
#1233=IFCDERIVEDUNITELEMENT(#1253,-1);
#1234=IFCDERIVEDUNITELEMENT(#1280,-1);
#1235=IFCDERIVEDUNITELEMENT(#1280,-1);
#1236=IFCDERIVEDUNITELEMENT(#1254,1);
#1237=IFCDERIVEDUNITELEMENT(#1276,-1);
#1238=IFCDERIVEDUNITELEMENT(#1276,1);
#1239=IFCDERIVEDUNITELEMENT(#1254,-1);
#1240=IFCDERIVEDUNITELEMENT(#1280,-1);
#1241=IFCDERIVEDUNITELEMENT(#1266,1);
#1242=IFCDERIVEDUNITELEMENT(#1253,1);
#1243=IFCDERIVEDUNITELEMENT(#1274,1);
#1244=IFCDERIVEDUNITELEMENT(#1281,-1);
#1245=IFCDERIVEDUNITELEMENT(#1255,1);
#1246=IFCDERIVEDUNITELEMENT(#1281,-1);
#1247=IFCDERIVEDUNITELEMENT(#1253,6);
#1248=IFCDERIVEDUNITELEMENT(#1266,1);
#1249=IFCDERIVEDUNITELEMENT(#1253,2);
#1250=IFCCONVERSIONBASEDUNIT(#1251,.PLANEANGLEUNIT.,'degree',#1252);
#1251=IFCDIMENSIONALEXPONENTS(0,0,0,0,0,0,0);
#1252=IFCMEASUREWITHUNIT(IFCREAL(0.0174532925199433),#1275);
#1253=IFCSIUNIT(*,.LENGTHUNIT.,$,.METRE.);
#1254=IFCSIUNIT(*,.AREAUNIT.,$,.SQUARE_METRE.);
#1255=IFCSIUNIT(*,.VOLUMEUNIT.,$,.CUBIC_METRE.);
#1256=IFCSIUNIT(*,.ABSORBEDDOSEUNIT.,$,.GRAY.);
#1257=IFCSIUNIT(*,.AMOUNTOFSUBSTANCEUNIT.,$,.MOLE.);
#1258=IFCSIUNIT(*,.DOSEEQUIVALENTUNIT.,$,.SIEVERT.);
#1259=IFCSIUNIT(*,.ELECTRICCAPACITANCEUNIT.,$,.FARAD.);
#1260=IFCSIUNIT(*,.ELECTRICCHARGEUNIT.,$,.COULOMB.);
#1261=IFCSIUNIT(*,.ELECTRICCONDUCTANCEUNIT.,$,.SIEMENS.);
#1262=IFCSIUNIT(*,.ELECTRICCURRENTUNIT.,$,.AMPERE.);
#1263=IFCSIUNIT(*,.ELECTRICRESISTANCEUNIT.,$,.OHM.);
#1264=IFCSIUNIT(*,.ELECTRICVOLTAGEUNIT.,$,.VOLT.);
#1265=IFCSIUNIT(*,.ENERGYUNIT.,$,.JOULE.);
#1266=IFCSIUNIT(*,.FORCEUNIT.,$,.NEWTON.);
#1267=IFCSIUNIT(*,.FREQUENCYUNIT.,$,.HERTZ.);
#1268=IFCSIUNIT(*,.ILLUMINANCEUNIT.,$,.LUX.);
#1269=IFCSIUNIT(*,.INDUCTANCEUNIT.,$,.HENRY.);
#1270=IFCSIUNIT(*,.LUMINOUSFLUXUNIT.,$,.LUMEN.);
#1271=IFCSIUNIT(*,.LUMINOUSINTENSITYUNIT.,$,.CANDELA.);
#1272=IFCSIUNIT(*,.MAGNETICFLUXDENSITYUNIT.,$,.TESLA.);
#1273=IFCSIUNIT(*,.MAGNETICFLUXUNIT.,$,.WEBER.);
#1274=IFCSIUNIT(*,.MASSUNIT.,$,.GRAM.);
#1275=IFCSIUNIT(*,.PLANEANGLEUNIT.,$,.RADIAN.);
#1276=IFCSIUNIT(*,.POWERUNIT.,$,.WATT.);
#1277=IFCSIUNIT(*,.PRESSUREUNIT.,$,.PASCAL.);
#1278=IFCSIUNIT(*,.RADIOACTIVITYUNIT.,$,.BECQUEREL.);
#1279=IFCSIUNIT(*,.SOLIDANGLEUNIT.,$,.STERADIAN.);
#1280=IFCSIUNIT(*,.THERMODYNAMICTEMPERATUREUNIT.,$,.KELVIN.);
#1281=IFCSIUNIT(*,.TIMEUNIT.,$,.SECOND.);
ENDSEC;
END-ISO-10303-21;

ISO-10303-21;
HEADER;
/* Generated by software containing ST-Developer
 * from STEP Tools, Inc. (www.steptools.com) 
 */

FILE_DESCRIPTION(
/* description */ ('ViewDefinition [DesignTransferView]',
'Comment [Comments]',
'Comment [System: OpenBuildings Designer*********** debug build Feb  5
 2020 00:34:15]'),
/* implementation_level */ '2;1');

FILE_NAME(
/* name */ '1.SimpleRectangle',
/* time_stamp */ '2020-03-05T12:22:11+03:00',
/* author */ ('First Name Last Name'),
/* organization */ ('Organization Name'),
/* preprocessor_version */ 'ST-DEVELOPER v16.13',
/* originating_system */ 'OpenBuildings Designer***********',
/* authorisation */ 'Administrator');

FILE_SCHEMA (('IFC4'));
ENDSEC;

DATA;
#10=IFCBOUNDINGBOX(#303,15.0000000000001,15.0114148312504,3.33239964536385);
#11=IFCGEOMETRICREPRESENTATIONSUBCONTEXT('Body','Model',*,*,*,*,#13,1.,
 .MODEL_VIEW.,$);
#12=IFCGEOMETRICREPRESENTATIONSUBCONTEXT('Box','Model',*,*,*,*,#13,1.,
 .SKETCH_VIEW.,$);
#13=IFCGEOMETRICREPRESENTATIONCONTEXT($,'Model',3,1.E-6,#70,#306);
#14=IFCSHAPEREPRESENTATION(#11,'Body','SurfaceModel',(#25));
#15=IFCSHAPEREPRESENTATION(#12,'Box','BoundingBox',(#10));
#16=IFCPRODUCTDEFINITIONSHAPE($,$,(#14,#15));
#17=IFCRELCONTAINEDINSPATIALSTRUCTURE('2aJi0CW_wcTJthPGyCEMkK',#369,$,$,
(#20),#18);
#18=IFCBUILDINGSTOREY('0rm7DUqMJhOUcs5v8t1F$L',#369,'Floor 3',
'Administrative and employee lounge',$,#64,$,'Floor 3',.ELEMENT.,9.144);
#19=IFCBUILDING('3_IkJInE13hR6Xwo4VKOgh',#369,'Bldg 1',
'3 Story Building',$,#63,$,'Bldg 1',.ELEMENT.,0.,0.,#361);
#20=IFCBUILDINGELEMENTPROXY('1SkVtlXVM5QZmO_SLYA8R9',#369,
'Element type: 24','1157*bspline surface!Default','',#65,#16,
'1157*bspline surface!Default',.NOTDEFINED.);
#21=IFCSURFACESTYLE($,.BOTH.,(#22));
#22=IFCSURFACESTYLESHADING(#23,0.);
#23=IFCCOLOURRGB($,1.,1.,1.);
#24=IFCPRESENTATIONLAYERWITHSTYLE('Default',$,(#25),$,.U.,.U.,.U.,(#21));
#25=IFCSHELLBASEDSURFACEMODEL((#26));
#26=IFCOPENSHELL((#57));
#27=IFCORIENTEDEDGE(*,*,#32,.T.);
#28=IFCORIENTEDEDGE(*,*,#33,.T.);
#29=IFCORIENTEDEDGE(*,*,#34,.F.);
#30=IFCORIENTEDEDGE(*,*,#35,.F.);
#31=IFCEDGELOOP((#27,#28,#29,#30));
#32=IFCEDGECURVE(#36,#37,#40,.T.);
#33=IFCEDGECURVE(#37,#38,#41,.T.);
#34=IFCEDGECURVE(#38,#39,#42,.T.);
#35=IFCEDGECURVE(#39,#36,#43,.T.);
#36=IFCVERTEXPOINT(#72);
#37=IFCVERTEXPOINT(#282);
#38=IFCVERTEXPOINT(#296);
#39=IFCVERTEXPOINT(#86);
#40=IFCSURFACECURVE(#44,(#48),.PCURVE_S1.);
#41=IFCSURFACECURVE(#45,(#49),.PCURVE_S1.);
#42=IFCSURFACECURVE(#46,(#50),.PCURVE_S1.);
#43=IFCSURFACECURVE(#47,(#51),.PCURVE_S1.);
#44=IFCBSPLINECURVEWITHKNOTS(9,(#72,#87,#102,#117,#132,#147,#162,#177,#192,
#207,#222,#237,#252,#267,#282),.UNSPECIFIED.,.F.,.U.,(10,1,1,1,1,1,10),
(0.,0.166666666666667,0.333333333333333,0.5,0.666666666666667,0.833333333333333,
1.),.UNSPECIFIED.);
#45=IFCBSPLINECURVEWITHKNOTS(9,(#282,#283,#284,#285,#286,#287,#288,#289,
#290,#291,#292,#293,#294,#295,#296),.UNSPECIFIED.,.F.,.U.,(10,1,1,1,1,1,
10),(0.,0.166666666666667,0.333333333333333,0.5,0.666666666666667,0.833333333333333,
1.),.UNSPECIFIED.);
#46=IFCBSPLINECURVEWITHKNOTS(9,(#296,#281,#266,#251,#236,#221,#206,#191,
#176,#161,#146,#131,#116,#101,#86),.UNSPECIFIED.,.F.,.U.,(10,1,1,1,1,1,
10),(0.,0.166666666666667,0.333333333333333,0.5,0.666666666666667,0.833333333333333,
1.),.UNSPECIFIED.);
#47=IFCBSPLINECURVEWITHKNOTS(9,(#86,#85,#84,#83,#82,#81,#80,#79,#78,#77,
#76,#75,#74,#73,#72),.UNSPECIFIED.,.F.,.U.,(10,1,1,1,1,1,10),(0.,0.166666666666667,
0.333333333333333,0.5,0.666666666666667,0.833333333333333,1.),
 .UNSPECIFIED.);
#48=IFCPCURVE(#58,#52);
#49=IFCPCURVE(#58,#53);
#50=IFCPCURVE(#58,#54);
#51=IFCPCURVE(#58,#55);
#52=IFCPOLYLINE((#297,#298));
#53=IFCPOLYLINE((#298,#299));
#54=IFCPOLYLINE((#299,#300));
#55=IFCPOLYLINE((#300,#297));
#56=IFCFACEOUTERBOUND(#31,.T.);
#57=IFCADVANCEDFACE((#56),#58,.T.);
#58=IFCBSPLINESURFACEWITHKNOTS(9,9,((#72,#73,#74,#75,#76,#77,#78,#79,#80,
#81,#82,#83,#84,#85,#86),(#87,#88,#89,#90,#91,#92,#93,#94,#95,#96,#97,#98,
#99,#100,#101),(#102,#103,#104,#105,#106,#107,#108,#109,#110,#111,#112,
#113,#114,#115,#116),(#117,#118,#119,#120,#121,#122,#123,#124,#125,#126,
#127,#128,#129,#130,#131),(#132,#133,#134,#135,#136,#137,#138,#139,#140,
#141,#142,#143,#144,#145,#146),(#147,#148,#149,#150,#151,#152,#153,#154,
#155,#156,#157,#158,#159,#160,#161),(#162,#163,#164,#165,#166,#167,#168,
#169,#170,#171,#172,#173,#174,#175,#176),(#177,#178,#179,#180,#181,#182,
#183,#184,#185,#186,#187,#188,#189,#190,#191),(#192,#193,#194,#195,#196,
#197,#198,#199,#200,#201,#202,#203,#204,#205,#206),(#207,#208,#209,#210,
#211,#212,#213,#214,#215,#216,#217,#218,#219,#220,#221),(#222,#223,#224,
#225,#226,#227,#228,#229,#230,#231,#232,#233,#234,#235,#236),(#237,#238,
#239,#240,#241,#242,#243,#244,#245,#246,#247,#248,#249,#250,#251),(#252,
#253,#254,#255,#256,#257,#258,#259,#260,#261,#262,#263,#264,#265,#266),
(#267,#268,#269,#270,#271,#272,#273,#274,#275,#276,#277,#278,#279,#280,
#281),(#282,#283,#284,#285,#286,#287,#288,#289,#290,#291,#292,#293,#294,
#295,#296)),.UNSPECIFIED.,.F.,.F.,.U.,(10,1,1,1,1,1,10),(10,1,1,1,1,1,10),
(0.,0.166666666666667,0.333333333333333,0.5,0.666666666666667,0.833333333333333,
1.),(0.,0.166666666666667,0.333333333333333,0.5,0.666666666666667,0.833333333333333,
1.),.UNSPECIFIED.);
#59=IFCRELAGGREGATES('1kB3AQvfGpkFm_7uHNRdoP',#369,$,$,#358,(#307));
#60=IFCRELAGGREGATES('0Cp_wDOOr3amQ16IWrGpUy',#369,$,$,#307,(#19));
#61=IFCRELAGGREGATES('1NfacxJUhl1S_ka3CQXVRt',#369,$,$,#19,(#18));
#62=IFCLOCALPLACEMENT($,#66);
#63=IFCLOCALPLACEMENT(#62,#67);
#64=IFCLOCALPLACEMENT(#63,#68);
#65=IFCLOCALPLACEMENT(#64,#69);
#66=IFCAXIS2PLACEMENT3D(#71,#304,#305);
#67=IFCAXIS2PLACEMENT3D(#71,#304,#305);
#68=IFCAXIS2PLACEMENT3D(#301,#304,#305);
#69=IFCAXIS2PLACEMENT3D(#302,#304,#305);
#70=IFCAXIS2PLACEMENT3D(#71,#304,#305);
#71=IFCCARTESIANPOINT((0.,0.,0.));
#72=IFCCARTESIANPOINT((3.5623730998E-5,1.0532591771E-5,1.93875932375));
#73=IFCCARTESIANPOINT((3.5623730998E-5,1.07143910402,1.93875932375));
#74=IFCCARTESIANPOINT((3.5623730905E-5,2.14286767545,3.1754724911));
#75=IFCCARTESIANPOINT((3.5623730998E-5,3.2142962469,1.93875932375));
#76=IFCCARTESIANPOINT((3.5623730905E-5,4.2857248183,3.1754724911));
#77=IFCCARTESIANPOINT((3.5623730998E-5,5.3571533897,1.93875932375));
#78=IFCCARTESIANPOINT((3.5623730998E-5,6.4285819612,1.93875932375));
#79=IFCCARTESIANPOINT((3.5623730998E-5,7.5000105326,1.93875932375));
#80=IFCCARTESIANPOINT((3.5623730905E-5,8.571439104,3.1754724911));
#81=IFCCARTESIANPOINT((3.5623730998E-5,9.6428676754,1.93875932375));
#82=IFCCARTESIANPOINT((3.5623730998E-5,10.7142962469,1.93875932375));
#83=IFCCARTESIANPOINT((3.5623730905E-5,11.7857248183,3.1754724911));
#84=IFCCARTESIANPOINT((3.5623730998E-5,12.8571533897,1.93875932375));
#85=IFCCARTESIANPOINT((3.5623730998E-5,13.9285819612,1.93875932375));
#86=IFCCARTESIANPOINT((3.5623730998E-5,15.0000105326,1.93875932375));
#87=IFCCARTESIANPOINT((1.07146419516,1.0532591771E-5,1.93875932375));
#88=IFCCARTESIANPOINT((1.07146419516,1.07143910402,1.93875932375));
#89=IFCCARTESIANPOINT((1.07146419516,2.14286767545,1.93875932375));
#90=IFCCARTESIANPOINT((1.07146419516,3.2142962469,1.93875932375));
#91=IFCCARTESIANPOINT((1.07146419516,4.2857248183,1.93875932375));
#92=IFCCARTESIANPOINT((1.07146419516,5.3571533897,1.93875932375));
#93=IFCCARTESIANPOINT((1.07146419516,6.4285819612,1.93875932375));
#94=IFCCARTESIANPOINT((1.07146419516,7.5000105326,1.93875932375));
#95=IFCCARTESIANPOINT((1.07146419516,8.571439104,1.93875932375));
#96=IFCCARTESIANPOINT((1.07146419516,9.6428676754,1.93875932375));
#97=IFCCARTESIANPOINT((1.07146419516,10.7142962469,1.93875932375));
#98=IFCCARTESIANPOINT((1.07146419516,11.7857248183,1.93875932375));
#99=IFCCARTESIANPOINT((1.07146419516,12.8571533897,1.93875932375));
#100=IFCCARTESIANPOINT((1.07146419516,13.9285819612,1.93875932375));
#101=IFCCARTESIANPOINT((1.04385994151,14.9865837438,1.54886729649));
#102=IFCCARTESIANPOINT((2.14289276659,1.0532591771E-5,1.93875932375));
#103=IFCCARTESIANPOINT((2.14289276659,1.07143910402,1.93875932375));
#104=IFCCARTESIANPOINT((2.14289276659,2.14286767545,1.93875932375));
#105=IFCCARTESIANPOINT((2.14289276659,3.2142962469,1.93875932375));
#106=IFCCARTESIANPOINT((2.14289276659,4.2857248183,1.93875932375));
#107=IFCCARTESIANPOINT((2.14289276659,5.3571533897,1.93875932375));
#108=IFCCARTESIANPOINT((2.14289276659,6.4285819612,1.93875932375));
#109=IFCCARTESIANPOINT((2.14289276659,7.5000105326,1.93875932375));
#110=IFCCARTESIANPOINT((2.14289276659,8.571439104,1.93875932375));
#111=IFCCARTESIANPOINT((2.14289276659,9.6428676754,1.93875932375));
#112=IFCCARTESIANPOINT((2.14289276659,10.7142962469,1.93875932375));
#113=IFCCARTESIANPOINT((2.14289276659,11.7857248183,1.93875932375));
#114=IFCCARTESIANPOINT((2.14289276659,12.8571533897,5.1287882986));
#115=IFCCARTESIANPOINT((2.14289276659,13.9285819612,1.93875932375));
#116=IFCCARTESIANPOINT((2.14289276659,15.0000105326,1.74110678043));
#117=IFCCARTESIANPOINT((3.214321338,1.0532591771E-5,1.93875932375));
#118=IFCCARTESIANPOINT((3.214321338,1.07143910402,1.93875932375));
#119=IFCCARTESIANPOINT((3.214321338,2.14286767545,5.18065991E-5));
#120=IFCCARTESIANPOINT((3.214321338,3.2142962469,1.93875932375));
#121=IFCCARTESIANPOINT((3.214321338,4.2857248183,1.93875932375));
#122=IFCCARTESIANPOINT((3.214321338,5.3571533897,5.18065991E-5));
#123=IFCCARTESIANPOINT((3.214321338,6.4285819612,1.93875932375));
#124=IFCCARTESIANPOINT((3.214321338,7.5000105326,1.93875932375));
#125=IFCCARTESIANPOINT((3.214321338,8.571439104,1.93875932375));
#126=IFCCARTESIANPOINT((3.214321338,9.6428676754,1.93875932375));
#127=IFCCARTESIANPOINT((3.214321338,10.7142962469,1.93875932375));
#128=IFCCARTESIANPOINT((3.214321338,11.7857248183,1.93875932375));
#129=IFCCARTESIANPOINT((3.214321338,12.8571533897,5.1287882986));
#130=IFCCARTESIANPOINT((3.214321338,13.9285819612,1.93875932375));
#131=IFCCARTESIANPOINT((3.214321338,15.0000105326,1.93875932375));
#132=IFCCARTESIANPOINT((4.2857499094,1.0532591771E-5,1.93875932375));
#133=IFCCARTESIANPOINT((4.2857499094,1.07143910402,1.93875932375));
#134=IFCCARTESIANPOINT((4.2857499094,2.14286767545,1.93875932375));
#135=IFCCARTESIANPOINT((4.2857499094,3.2142962469,1.93875932375));
#136=IFCCARTESIANPOINT((4.2857499094,4.2857248183,1.93875932375));
#137=IFCCARTESIANPOINT((4.2857499094,5.3571533897,1.93875932375));
#138=IFCCARTESIANPOINT((4.2857499094,6.4285819612,1.93875932375));
#139=IFCCARTESIANPOINT((4.2857499094,7.5000105326,1.93875932375));
#140=IFCCARTESIANPOINT((4.2857499094,8.571439104,1.93875932375));
#141=IFCCARTESIANPOINT((4.2857499094,9.6428676754,1.93875932375));
#142=IFCCARTESIANPOINT((4.2857499094,10.7142962469,1.93875932375));
#143=IFCCARTESIANPOINT((4.2857499094,11.7857248183,1.93875932375));
#144=IFCCARTESIANPOINT((4.2857499094,12.8571533897,1.93875932375));
#145=IFCCARTESIANPOINT((4.2857499094,13.9285819612,1.93875932375));
#146=IFCCARTESIANPOINT((4.2857499094,15.0000105326,1.93875932375));
#147=IFCCARTESIANPOINT((5.3571784809,1.0532591771E-5,1.93875932375));
#148=IFCCARTESIANPOINT((5.3571784809,1.07143910402,1.93875932375));
#149=IFCCARTESIANPOINT((5.3571784809,2.14286767545,1.93875932375));
#150=IFCCARTESIANPOINT((5.3571784809,3.2142962469,1.93875932375));
#151=IFCCARTESIANPOINT((5.3571784809,4.2857248183,1.93875932375));
#152=IFCCARTESIANPOINT((5.3571784809,5.3571533897,1.93875932375));
#153=IFCCARTESIANPOINT((5.3571784809,6.4285819612,1.93875932375));
#154=IFCCARTESIANPOINT((5.3571784809,7.5000105326,1.93875932375));
#155=IFCCARTESIANPOINT((5.3571784809,8.571439104,1.93875932375));
#156=IFCCARTESIANPOINT((5.3571784809,9.6428676754,1.93875932375));
#157=IFCCARTESIANPOINT((5.3571784809,10.7142962469,5.1287882986));
#158=IFCCARTESIANPOINT((5.3571784809,11.7857248183,1.93875932375));
#159=IFCCARTESIANPOINT((5.3571784809,12.8571533897,1.93875932375));
#160=IFCCARTESIANPOINT((5.3571784809,13.9285819612,1.93875932375));
#161=IFCCARTESIANPOINT((5.3571784809,15.0000105326,1.93875932375));
#162=IFCCARTESIANPOINT((6.4286070523,1.0532591771E-5,1.93875932375));
#163=IFCCARTESIANPOINT((6.4286070523,1.07143910402,1.93875932375));
#164=IFCCARTESIANPOINT((6.4286070523,2.14286767545,1.93875932375));
#165=IFCCARTESIANPOINT((6.4286070523,3.2142962469,1.93875932375));
#166=IFCCARTESIANPOINT((6.4286070523,4.2857248183,1.93875932375));
#167=IFCCARTESIANPOINT((6.4286070523,5.3571533897,1.93875932375));
#168=IFCCARTESIANPOINT((6.4286070523,6.4285819612,5.18065991E-5));
#169=IFCCARTESIANPOINT((6.4286070523,7.5000105326,1.93875932375));
#170=IFCCARTESIANPOINT((6.4286070523,8.571439104,1.93875932375));
#171=IFCCARTESIANPOINT((6.4286070523,9.6428676754,1.93875932375));
#172=IFCCARTESIANPOINT((6.4286070523,10.7142962469,5.1287882986));
#173=IFCCARTESIANPOINT((6.4286070523,11.7857248183,1.93875932375));
#174=IFCCARTESIANPOINT((6.4286070523,12.8571533897,1.93875932375));
#175=IFCCARTESIANPOINT((6.4286070523,13.9285819612,1.93875932375));
#176=IFCCARTESIANPOINT((6.4286070523,15.0000105326,1.93875932375));
#177=IFCCARTESIANPOINT((7.5000356237,1.0532591771E-5,1.93875932375));
#178=IFCCARTESIANPOINT((7.5000356237,1.07143910402,1.93875932375));
#179=IFCCARTESIANPOINT((7.5000356237,2.14286767545,1.93875932375));
#180=IFCCARTESIANPOINT((7.5000356237,3.2142962469,1.93875932375));
#181=IFCCARTESIANPOINT((7.5000356237,4.2857248183,1.93875932375));
#182=IFCCARTESIANPOINT((7.5000356237,5.3571533897,1.93875932375));
#183=IFCCARTESIANPOINT((7.5000356237,6.4285819612,1.93875932375));
#184=IFCCARTESIANPOINT((7.5000356237,7.5000105326,1.93875932375));
#185=IFCCARTESIANPOINT((7.5000356237,8.571439104,1.93875932375));
#186=IFCCARTESIANPOINT((7.5000356237,9.6428676754,5.1287882986));
#187=IFCCARTESIANPOINT((7.5000356237,10.7142962469,1.93875932375));
#188=IFCCARTESIANPOINT((7.5000356237,11.7857248183,1.93875932375));
#189=IFCCARTESIANPOINT((7.5000356237,12.8571533897,1.93875932375));
#190=IFCCARTESIANPOINT((7.5000356237,13.9285819612,1.93875932375));
#191=IFCCARTESIANPOINT((7.5000356237,15.0000105326,1.93875932375));
#192=IFCCARTESIANPOINT((8.5714641952,1.0532591771E-5,1.93875932375));
#193=IFCCARTESIANPOINT((8.5714641952,1.07143910402,1.93875932375));
#194=IFCCARTESIANPOINT((8.5714641952,2.14286767545,1.93875932375));
#195=IFCCARTESIANPOINT((8.5714641952,3.2142962469,1.93875932375));
#196=IFCCARTESIANPOINT((8.5714641952,4.2857248183,1.93875932375));
#197=IFCCARTESIANPOINT((8.5714641952,5.3571533897,1.93875932375));
#198=IFCCARTESIANPOINT((8.5714641952,6.4285819612,1.93875932375));
#199=IFCCARTESIANPOINT((8.5714641952,7.5000105326,1.93875932375));
#200=IFCCARTESIANPOINT((8.5714641952,8.571439104,1.93875932375));
#201=IFCCARTESIANPOINT((8.5714641952,9.6428676754,1.93875932375));
#202=IFCCARTESIANPOINT((8.5714641952,10.7142962469,1.93875932375));
#203=IFCCARTESIANPOINT((8.5714641952,11.7857248183,1.93875932375));
#204=IFCCARTESIANPOINT((8.5714641952,12.8571533897,1.93875932375));
#205=IFCCARTESIANPOINT((8.5714641952,13.9285819612,1.93875932375));
#206=IFCCARTESIANPOINT((8.5714641952,15.0000105326,1.93875932375));
#207=IFCCARTESIANPOINT((9.6428927666,1.0532591771E-5,1.93875932375));
#208=IFCCARTESIANPOINT((9.6428927666,1.07143910402,1.93875932375));
#209=IFCCARTESIANPOINT((9.6428927666,2.14286767545,1.93875932375));
#210=IFCCARTESIANPOINT((9.6428927666,3.2142962469,1.93875932375));
#211=IFCCARTESIANPOINT((9.6428927666,4.2857248183,1.93875932375));
#212=IFCCARTESIANPOINT((9.6428927666,5.3571533897,1.93875932375));
#213=IFCCARTESIANPOINT((9.6428927666,6.4285819612,5.18065991E-5));
#214=IFCCARTESIANPOINT((9.6428927666,7.5000105326,1.93875932375));
#215=IFCCARTESIANPOINT((9.6428927666,8.571439104,1.93875932375));
#216=IFCCARTESIANPOINT((9.6428927666,9.6428676754,1.93875932375));
#217=IFCCARTESIANPOINT((9.6428927666,10.7142962469,1.93875932375));
#218=IFCCARTESIANPOINT((9.6428927666,11.7857248183,1.93875932375));
#219=IFCCARTESIANPOINT((9.6428927666,12.8571533897,1.93875932375));
#220=IFCCARTESIANPOINT((9.6428927666,13.9285819612,1.93875932375));
#221=IFCCARTESIANPOINT((10.0962936606,15.0376487171,1.35588611674));
#222=IFCCARTESIANPOINT((10.714321338,1.0532591771E-5,1.93875932375));
#223=IFCCARTESIANPOINT((10.714321338,1.07143910402,1.93875932375));
#224=IFCCARTESIANPOINT((10.714321338,2.14286767545,1.93875932375));
#225=IFCCARTESIANPOINT((10.714321338,3.2142962469,1.93875932375));
#226=IFCCARTESIANPOINT((10.714321338,4.2857248183,5.18065991E-5));
#227=IFCCARTESIANPOINT((10.714321338,5.3571533897,1.93875932375));
#228=IFCCARTESIANPOINT((10.714321338,6.4285819612,1.93875932375));
#229=IFCCARTESIANPOINT((10.714321338,7.5000105326,1.93875932375));
#230=IFCCARTESIANPOINT((10.714321338,8.571439104,5.18065991E-5));
#231=IFCCARTESIANPOINT((10.714321338,9.6428676754,1.93875932375));
#232=IFCCARTESIANPOINT((10.714321338,10.7142962469,1.93875932375));
#233=IFCCARTESIANPOINT((10.714321338,11.7857248183,1.93875932375));
#234=IFCCARTESIANPOINT((10.714321338,12.8571533897,1.93875932375));
#235=IFCCARTESIANPOINT((10.714321338,13.9285819612,1.93875932375));
#236=IFCCARTESIANPOINT((10.714321338,15.0000105326,1.93875932375));
#237=IFCCARTESIANPOINT((11.7857499094,1.0532591771E-5,1.93875932375));
#238=IFCCARTESIANPOINT((11.7857499094,1.07143910402,1.93875932375));
#239=IFCCARTESIANPOINT((11.7857499094,2.14286767545,1.93875932375));
#240=IFCCARTESIANPOINT((11.7857499094,3.2142962469,1.93875932375));
#241=IFCCARTESIANPOINT((11.7857499094,4.2857248183,1.93875932375));
#242=IFCCARTESIANPOINT((11.7857499094,5.3571533897,1.93875932375));
#243=IFCCARTESIANPOINT((11.7857499094,6.4285819612,1.93875932375));
#244=IFCCARTESIANPOINT((11.7857499094,7.5000105326,1.93875932375));
#245=IFCCARTESIANPOINT((11.7857499094,8.571439104,1.93875932375));
#246=IFCCARTESIANPOINT((11.7857499094,9.6428676754,1.93875932375));
#247=IFCCARTESIANPOINT((11.7857499094,10.7142962469,1.93875932375));
#248=IFCCARTESIANPOINT((11.7857499094,11.7857248183,1.93875932375));
#249=IFCCARTESIANPOINT((11.7857499094,12.8571533897,1.93875932375));
#250=IFCCARTESIANPOINT((11.7857499094,13.9285819612,1.93875932375));
#251=IFCCARTESIANPOINT((11.3978907176,14.8693563406,2.44049268426));
#252=IFCCARTESIANPOINT((12.8571784809,1.0532591771E-5,1.93875932375));
#253=IFCCARTESIANPOINT((12.8571784809,1.07143910402,1.93875932375));
#254=IFCCARTESIANPOINT((12.8571784809,2.14286767545,1.93875932375));
#255=IFCCARTESIANPOINT((12.8571784809,3.2142962469,1.93875932375));
#256=IFCCARTESIANPOINT((12.8571784809,4.2857248183,1.93875932375));
#257=IFCCARTESIANPOINT((12.8571784809,5.3571533897,1.93875932375));
#258=IFCCARTESIANPOINT((12.8571784809,6.4285819612,1.93875932375));
#259=IFCCARTESIANPOINT((12.8571784809,7.5000105326,5.18065991E-5));
#260=IFCCARTESIANPOINT((12.8571784809,8.571439104,1.93875932375));
#261=IFCCARTESIANPOINT((12.8571784809,9.6428676754,1.93875932375));
#262=IFCCARTESIANPOINT((12.8571784809,10.7142962469,1.93875932375));
#263=IFCCARTESIANPOINT((12.8571784809,11.7857248183,1.93875932375));
#264=IFCCARTESIANPOINT((12.8571784809,12.8571533897,1.93875932375));
#265=IFCCARTESIANPOINT((12.8571784809,13.9285819612,1.93875932375));
#266=IFCCARTESIANPOINT((12.8568370215,14.9497010766,2.56095173322));
#267=IFCCARTESIANPOINT((13.9286070523,1.0532591771E-5,1.93875932375));
#268=IFCCARTESIANPOINT((13.9286070523,1.07143910402,1.93875932375));
#269=IFCCARTESIANPOINT((13.9286070523,2.14286767545,1.93875932375));
#270=IFCCARTESIANPOINT((13.9286070523,3.2142962469,1.93875932375));
#271=IFCCARTESIANPOINT((13.9286070523,4.2857248183,1.93875932375));
#272=IFCCARTESIANPOINT((13.9286070523,5.3571533897,1.93875932375));
#273=IFCCARTESIANPOINT((13.9286070523,6.4285819612,1.93875932375));
#274=IFCCARTESIANPOINT((13.9286070523,7.5000105326,1.93875932375));
#275=IFCCARTESIANPOINT((13.9286070523,8.571439104,1.93875932375));
#276=IFCCARTESIANPOINT((13.9286070523,9.6428676754,1.93875932375));
#277=IFCCARTESIANPOINT((13.9286070523,10.7142962469,1.93875932375));
#278=IFCCARTESIANPOINT((13.9286070523,11.7857248183,1.93875932375));
#279=IFCCARTESIANPOINT((13.9286070523,12.8571533897,1.93875932375));
#280=IFCCARTESIANPOINT((13.9286070523,13.9285819612,1.93875932375));
#281=IFCCARTESIANPOINT((10.6452090386,11.7314780592,4.9981805335));
#282=IFCCARTESIANPOINT((15.0000356237,1.0532591771E-5,0.59627198567));
#283=IFCCARTESIANPOINT((15.0000356237,1.07143910402,1.93875932375));
#284=IFCCARTESIANPOINT((15.0000356237,2.14286767545,1.93875932375));
#285=IFCCARTESIANPOINT((15.0000356237,3.2142962469,1.93875932375));
#286=IFCCARTESIANPOINT((15.0000356237,4.2857248183,1.93875932375));
#287=IFCCARTESIANPOINT((15.0000356237,5.3571533897,1.93875932375));
#288=IFCCARTESIANPOINT((15.0000356237,6.4285819612,0.59627198567));
#289=IFCCARTESIANPOINT((15.0000356237,7.5000105326,1.93875932375));
#290=IFCCARTESIANPOINT((15.0000356237,8.571439104,1.93875932375));
#291=IFCCARTESIANPOINT((15.0000356237,9.6428676754,0.59627198567));
#292=IFCCARTESIANPOINT((15.0000356237,10.7142962469,1.93875932375));
#293=IFCCARTESIANPOINT((15.0000356237,11.7857248183,1.93875932375));
#294=IFCCARTESIANPOINT((15.0000356237,12.8571533897,1.93875932375));
#295=IFCCARTESIANPOINT((15.0000356237,13.9285819612,0.59627198567));
#296=IFCCARTESIANPOINT((15.0000356237,15.0000105326,1.93875932375));
#297=IFCCARTESIANPOINT((0.,0.));
#298=IFCCARTESIANPOINT((0.0001,0.));
#299=IFCCARTESIANPOINT((0.0001,0.0001));
#300=IFCCARTESIANPOINT((0.,0.0001));
#301=IFCCARTESIANPOINT((0.,0.,9.144));
#302=IFCCARTESIANPOINT((89.1421,-68.3287,38.6153));
#303=IFCCARTESIANPOINT((3.5623730952E-5,1.053259176E-5,0.59627198567));
#304=IFCDIRECTION((0.,0.,1.));
#305=IFCDIRECTION((1.,0.,0.));
#306=IFCDIRECTION((0.,1.));
#307=IFCSITE('006_i1V1D0tAKEh_$vljQt',#369,'Site 1','Site 1',$,#62,$,
'Site 1',.ELEMENT.,$,$,0.,$,#360);
#308=IFCRELDEFINESBYPROPERTIES('1RL1EewHTsROrPcNSlBI56',#369,'Project',
$,(#358),#318);
#309=IFCRELDEFINESBYPROPERTIES('3bjMWmv_Cp6LPxmaCnPGwW',#369,'Site',$,(#307),
#319);
#310=IFCRELDEFINESBYPROPERTIES('2zOmgYUoQmhp9sZMdCZF7s',#369,
'Pset_BuildingCommon',$,(#19),#320);
#311=IFCRELDEFINESBYPROPERTIES('3k0v4EdaEvPD8NSrRFB2MT',#369,
'ArchBuilding',$,(#19),#321);
#312=IFCRELDEFINESBYPROPERTIES('3uIgchz4n8nDCCMgq1BBql',#369,
'ObjectIdentity',$,(#19),#322);
#313=IFCRELDEFINESBYPROPERTIES('1CCmZ_1eEDpfMO62t1jkZj',#369,
'ObjectPostalAddress',$,(#19),#323);
#314=IFCRELDEFINESBYPROPERTIES('24I$F7L8gfSwdAGDvfkVEu',#369,
'Pset_BuildingStoreyCommon',$,(#18),#324);
#315=IFCRELDEFINESBYPROPERTIES('1K1DvjfvPzx3hE5w5ZHK0q',#369,
'ArchFloor',$,(#18),#325);
#316=IFCRELDEFINESBYPROPERTIES('0lrK04aZ0v6OfwsyTXPzZq',#369,'Floor',$,
(#18),#326);
#317=IFCRELDEFINESBYPROPERTIES('1fbDyx4oFQKKacC_b9xURq',#369,
'StructuralFloorCommon',$,(#18),#327);
#318=IFCPROPERTYSET('2rcLYSOKrF02Xv$qFtEF4e',#369,'Project',$,(#328));
#319=IFCPROPERTYSET('2Qswsgonw96KTM1$UoUklF',#369,'Site',$,(#329,#330));
#320=IFCPROPERTYSET('2Ct1rxyLMs3vgB_jB90hMr',#369,'Pset_BuildingCommon',
$,(#331));
#321=IFCPROPERTYSET('3OmKLTEF7bgCV0$1Gr7ajZ',#369,'ArchBuilding',$,(#332,
#333));
#322=IFCPROPERTYSET('1HjNcqn7ak9HvLTK8j8w1d',#369,'ObjectIdentity',$,(#334));
#323=IFCPROPERTYSET('306EzywKpRfIeis4axlC1d',#369,'ObjectPostalAddress',
$,(#335,#336,#337,#338));
#324=IFCPROPERTYSET('381uKge8G33qXIbHr84hB4',#369,
'Pset_BuildingStoreyCommon',$,(#339,#340,#341,#342));
#325=IFCPROPERTYSET('0GZmVQOAmJ6zMOk6j3uleN',#369,'ArchFloor',$,(#343,#344,
#345,#346,#347,#348,#349,#350,#351,#352));
#326=IFCPROPERTYSET('0A3C_vQUNRQZfA6f5qtBlE',#369,'Floor',$,(#353,#354,
#355));
#327=IFCPROPERTYSET('0izo3etGuCpkjROydkcZbT',#369,
'StructuralFloorCommon',$,(#356));
#328=IFCPROPERTYSINGLEVALUE('ProjectName',$,IFCTEXT(
'BuildingTemplate_US'),$);
#329=IFCPROPERTYSINGLEVALUE('SiteName',$,IFCTEXT('Site 1'),$);
#330=IFCPROPERTYSINGLEVALUE('BuildingHeightLimit',$,IFCLENGTHMEASURE(0.),
#505);
#331=IFCPROPERTYSINGLEVALUE('YearOfConstruction',$,IFCLABEL('2006'),$);
#332=IFCPROPERTYSINGLEVALUE('BuildingName',$,IFCTEXT('Bldg 1'),$);
#333=IFCPROPERTYSINGLEVALUE('YearConstructed',$,IFCTEXT('2006'),$);
#334=IFCPROPERTYSINGLEVALUE('Description',$,IFCTEXT('3 Story Building'),
$);
#335=IFCPROPERTYSINGLEVALUE('Address1',$,IFCTEXT(
'203 Rickenhouse Drive'),$);
#336=IFCPROPERTYSINGLEVALUE('City',$,IFCTEXT('Madison'),$);
#337=IFCPROPERTYSINGLEVALUE('PostalCode',$,IFCTEXT('35789'),$);
#338=IFCPROPERTYSINGLEVALUE('Country',$,IFCTEXT('US'),$);
#339=IFCPROPERTYSINGLEVALUE('GrossAreaPlanned',$,IFCAREAMEASURE(22296729.6),
#506);
#340=IFCPROPERTYSINGLEVALUE('GrossPlannedArea',$,IFCAREAMEASURE(22296729.6),
$);
#341=IFCPROPERTYSINGLEVALUE('NetAreaPlanned',$,IFCAREAMEASURE(22296729.6),
#506);
#342=IFCPROPERTYSINGLEVALUE('NetPlannedArea',$,IFCAREAMEASURE(22296729.6),
$);
#343=IFCPROPERTYSINGLEVALUE('FloorName',$,IFCTEXT('Floor 3'),$);
#344=IFCPROPERTYSINGLEVALUE('FinishedFloorElevation',$,IFCREAL(9.144),$);
#345=IFCPROPERTYSINGLEVALUE('Description',$,IFCTEXT(
'Administrative and employee lounge'),$);
#346=IFCPROPERTYSINGLEVALUE('ApproxLength',$,IFCLENGTHMEASURE(6096.),#505);
#347=IFCPROPERTYSINGLEVALUE('ApproxWidth',$,IFCLENGTHMEASURE(3657.6),#505);
#348=IFCPROPERTYSINGLEVALUE('TypicalFloor',$,IFCBOOLEAN(.F.),$);
#349=IFCPROPERTYSINGLEVALUE('TypicalFloorHeight',$,IFCLENGTHMEASURE(0.),
#505);
#350=IFCPROPERTYSINGLEVALUE('TypicalFloorBaseElevation',$,
IFCLENGTHMEASURE(0.),#505);
#351=IFCPROPERTYSINGLEVALUE('ACSRotation',$,IFCTEXT('0.00000000'),$);
#352=IFCPROPERTYSINGLEVALUE('Annotation',$,IFCBOOLEAN(.T.),$);
#353=IFCPROPERTYSINGLEVALUE('GrossArea',$,IFCAREAMEASURE(22296729.6),#506);
#354=IFCPROPERTYSINGLEVALUE('NetArea',$,IFCAREAMEASURE(22296729.6),#506);
#355=IFCPROPERTYSINGLEVALUE('OccupancyType',$,IFCTEXT('Commercial'),$);
#356=IFCPROPERTYSINGLEVALUE('issplicefloor',$,IFCBOOLEAN(.F.),$);
#357=IFCUNITASSIGNMENT((#505,#506,#507,#508,#509,#510,#511,#512,#513,#514,
#515,#516,#517,#518,#519,#520,#521,#522,#523,#524,#525,#526,#502,#528,#529,
#530,#531,#532,#533,#371,#372,#373,#374,#375,#376,#377,#378,#379,#380,#381,
#382,#383,#384,#385,#386,#387,#388,#389,#390,#391,#392,#393,#394,#395,#396,
#397,#398,#399,#400,#401,#402,#403,#404,#405,#406,#407,#408,#409,#410,#411,
#412,#413,#414,#370));
#358=IFCPROJECT('1JmBUThP19Z8wED88xGwCF',#369,'BuildingTemplate_US',
'BuildingTemplate_US',$,'BuildingTemplate_US',$,(#13),#357);
#359=IFCPOSTALADDRESS($,$,$,$,('Address 1','Address 2'),$,'City/Town',
'State/Region','Postal Code','Country');
#360=IFCPOSTALADDRESS($,$,$,'Site 1',$,$,$,$,$,$);
#361=IFCPOSTALADDRESS($,$,$,$,('203 Rickenhouse Drive'),$,'Madison',$,
'35789','US');
#362=IFCTELECOMADDRESS($,$,$,('Phone'),$,$,('E-Mail'),$,$);
#363=IFCACTORROLE(.SUPPLIER.,$,$);
#364=IFCPERSON($,'Last Name','First Name',$,$,$,(#363),(#362));
#365=IFCPERSONANDORGANIZATION(#364,#367,$);
#366=IFCORGANIZATION($,'Bentley Systems Inc.',$,$,$);
#367=IFCORGANIZATION($,'Organization Name',$,$,(#359));
#368=IFCAPPLICATION(#366,'***********','OpenBuildings Designer','ABD');
#369=IFCOWNERHISTORY(#365,#368,$,$,0,$,$,1583400127);
#370=IFCMONETARYUNIT('USD');
#371=IFCDERIVEDUNIT((#415,#416),.ACCELERATIONUNIT.,'(METRE)/(SECOND^2)');
#372=IFCDERIVEDUNIT((#417,#418),.ANGULARVELOCITYUNIT.,
'(DEGREE)/(SECOND)');
#373=IFCDERIVEDUNIT((#419,#420),.CURVATUREUNIT.,'(DEGREE)/(METRE)');
#374=IFCDERIVEDUNIT((#421,#422),.DYNAMICVISCOSITYUNIT.,
'(PASCAL)(SECOND)');
#375=IFCDERIVEDUNIT((#423,#424),.HEATFLUXDENSITYUNIT.,
'(WATT)/(METRE^2)');
#376=IFCDERIVEDUNIT((#425,#426),.HEATINGVALUEUNIT.,'(JOULE)/(GRAM)');
#377=IFCDERIVEDUNIT((#427),.INTEGERCOUNTRATEUNIT.,'1/(SECOND)');
#378=IFCDERIVEDUNIT((#428,#429),.IONCONCENTRATIONUNIT.,
'(GRAM)/(CUBIC_METRE)');
#379=IFCDERIVEDUNIT((#430,#431),.ISOTHERMALMOISTURECAPACITYUNIT.,
'(CUBIC_METRE)/(GRAM)');
#380=IFCDERIVEDUNIT((#432,#433),.KINEMATICVISCOSITYUNIT.,
'(SQUARE_METRE)/(SECOND)');
#381=IFCDERIVEDUNIT((#434,#435),.LINEARFORCEUNIT.,'(NEWTON)/(METRE)');
#382=IFCDERIVEDUNIT((#436,#437),.LINEARMOMENTUNIT.,'(NEWTON)/(METRE)');
#383=IFCDERIVEDUNIT((#438,#439),.LINEARSTIFFNESSUNIT.,
'(NEWTON)/(METRE)');
#384=IFCDERIVEDUNIT((#440,#441),.LINEARVELOCITYUNIT.,'(METRE)/(SECOND)');
#385=IFCDERIVEDUNIT((#442,#443),.LUMINOUSINTENSITYDISTRIBUTIONUNIT.,
'(CANDELA)/(LUMEN)');
#386=IFCDERIVEDUNIT((#444,#445),.MASSDENSITYUNIT.,
'(GRAM)/(CUBIC_METRE)');
#387=IFCDERIVEDUNIT((#446,#447),.MASSFLOWRATEUNIT.,'(GRAM)/(SECOND)');
#388=IFCDERIVEDUNIT((#448,#449),.MASSPERLENGTHUNIT.,'(GRAM)/(METRE)');
#389=IFCDERIVEDUNIT((#450,#451),.MODULUSOFELASTICITYUNIT.,
'(NEWTON)/(SQUARE_METRE)');
#390=IFCDERIVEDUNIT((#452,#453),.MODULUSOFLINEARSUBGRADEREACTIONUNIT.,
'(NEWTON)/(METRE^2)');
#391=IFCDERIVEDUNIT((#454,#455),
 .MODULUSOFROTATIONALSUBGRADEREACTIONUNIT.,'(NEWTON)(METRE)');
#392=IFCDERIVEDUNIT((#456,#457),.MODULUSOFSUBGRADEREACTIONUNIT.,
'(NEWTON)/(CUBIC_METRE)');
#393=IFCDERIVEDUNIT((#458,#459),.MOISTUREDIFFUSIVITYUNIT.,
'(CUBIC_METRE)/(SECOND)');
#394=IFCDERIVEDUNIT((#460,#461),.MOLECULARWEIGHTUNIT.,'(GRAM)/(MOLE)');
#395=IFCDERIVEDUNIT((#462,#463),.PLANARFORCEUNIT.,'(NEWTON)/(METRE^2)');
#396=IFCDERIVEDUNIT((#464),.ROTATIONALFREQUENCYUNIT.,'1/(SECOND)');
#397=IFCDERIVEDUNIT((#465,#466),.ROTATIONALMASSUNIT.,'(GRAM)(METRE^2)');
#398=IFCDERIVEDUNIT((#467,#468,#469),.ROTATIONALSTIFFNESSUNIT.,
'(NEWTON)(METRE)/(DEGREE)');
#399=IFCDERIVEDUNIT((#470),.SECTIONAREAINTEGRALUNIT.,'(METRE^5)');
#400=IFCDERIVEDUNIT((#471),.SECTIONMODULUSUNIT.,'(METRE^3)');
#401=IFCDERIVEDUNIT((#472,#473),.SHEARMODULUSUNIT.,
'(NEWTON)/(SQUARE_METRE)');
#402=IFCDERIVEDUNIT((#474,#475),.SOUNDPOWERUNIT.,'(JOULE)/(SECOND)');
#403=IFCDERIVEDUNIT((#476,#477,#478),.SPECIFICHEATCAPACITYUNIT.,
'(NEWTON)/(GRAM)(KELVIN)');
#404=IFCDERIVEDUNIT((#479,#480),.TEMPERATUREGRADIENTUNIT.,
'(KELVIN)/(METRE)');
#405=IFCDERIVEDUNIT((#481,#482,#483),.THERMALADMITTANCEUNIT.,
'(WATT)/(SQUARE_METRE)(KELVIN)');
#406=IFCDERIVEDUNIT((#484,#485,#486),.THERMALCONDUCTANCEUNIT.,
'(WATT)/(METRE)(KELVIN)');
#407=IFCDERIVEDUNIT((#487),.THERMALEXPANSIONCOEFFICIENTUNIT.,
'1/(KELVIN)');
#408=IFCDERIVEDUNIT((#488,#489),.THERMALRESISTANCEUNIT.,
'(SQUARE_METRE)/(WATT)');
#409=IFCDERIVEDUNIT((#490,#491,#492),.THERMALTRANSMITTANCEUNIT.,
'(WATT)/(SQUARE_METRE)(KELVIN)');
#410=IFCDERIVEDUNIT((#493,#494),.TORQUEUNIT.,'(NEWTON)(METRE)');
#411=IFCDERIVEDUNIT((#495,#496),.VAPORPERMEABILITYUNIT.,
'(GRAM)/(SECOND)');
#412=IFCDERIVEDUNIT((#497,#498),.VOLUMETRICFLOWRATEUNIT.,
'(CUBIC_METRE)/(SECOND)');
#413=IFCDERIVEDUNIT((#499),.WARPINGCONSTANTUNIT.,'(METRE^6)');
#414=IFCDERIVEDUNIT((#500,#501),.WARPINGMOMENTUNIT.,'(NEWTON)(METRE^2)');
#415=IFCDERIVEDUNITELEMENT(#505,1);
#416=IFCDERIVEDUNITELEMENT(#533,-2);
#417=IFCDERIVEDUNITELEMENT(#502,1);
#418=IFCDERIVEDUNITELEMENT(#533,-1);
#419=IFCDERIVEDUNITELEMENT(#502,1);
#420=IFCDERIVEDUNITELEMENT(#505,-1);
#421=IFCDERIVEDUNITELEMENT(#529,1);
#422=IFCDERIVEDUNITELEMENT(#533,1);
#423=IFCDERIVEDUNITELEMENT(#528,1);
#424=IFCDERIVEDUNITELEMENT(#505,-2);
#425=IFCDERIVEDUNITELEMENT(#517,1);
#426=IFCDERIVEDUNITELEMENT(#526,-1);
#427=IFCDERIVEDUNITELEMENT(#533,-1);
#428=IFCDERIVEDUNITELEMENT(#526,1);
#429=IFCDERIVEDUNITELEMENT(#507,-1);
#430=IFCDERIVEDUNITELEMENT(#507,1);
#431=IFCDERIVEDUNITELEMENT(#526,-1);
#432=IFCDERIVEDUNITELEMENT(#506,1);
#433=IFCDERIVEDUNITELEMENT(#533,-1);
#434=IFCDERIVEDUNITELEMENT(#518,1);
#435=IFCDERIVEDUNITELEMENT(#505,-1);
#436=IFCDERIVEDUNITELEMENT(#518,1);
#437=IFCDERIVEDUNITELEMENT(#505,-1);
#438=IFCDERIVEDUNITELEMENT(#518,1);
#439=IFCDERIVEDUNITELEMENT(#505,-1);
#440=IFCDERIVEDUNITELEMENT(#505,1);
#441=IFCDERIVEDUNITELEMENT(#533,-1);
#442=IFCDERIVEDUNITELEMENT(#523,1);
#443=IFCDERIVEDUNITELEMENT(#522,-1);
#444=IFCDERIVEDUNITELEMENT(#526,1);
#445=IFCDERIVEDUNITELEMENT(#507,-1);
#446=IFCDERIVEDUNITELEMENT(#526,1);
#447=IFCDERIVEDUNITELEMENT(#533,-1);
#448=IFCDERIVEDUNITELEMENT(#526,1);
#449=IFCDERIVEDUNITELEMENT(#505,-1);
#450=IFCDERIVEDUNITELEMENT(#518,1);
#451=IFCDERIVEDUNITELEMENT(#506,-1);
#452=IFCDERIVEDUNITELEMENT(#518,1);
#453=IFCDERIVEDUNITELEMENT(#505,-2);
#454=IFCDERIVEDUNITELEMENT(#518,1);
#455=IFCDERIVEDUNITELEMENT(#505,1);
#456=IFCDERIVEDUNITELEMENT(#518,1);
#457=IFCDERIVEDUNITELEMENT(#507,-1);
#458=IFCDERIVEDUNITELEMENT(#507,1);
#459=IFCDERIVEDUNITELEMENT(#533,-1);
#460=IFCDERIVEDUNITELEMENT(#526,1);
#461=IFCDERIVEDUNITELEMENT(#509,-1);
#462=IFCDERIVEDUNITELEMENT(#518,1);
#463=IFCDERIVEDUNITELEMENT(#505,-2);
#464=IFCDERIVEDUNITELEMENT(#533,-1);
#465=IFCDERIVEDUNITELEMENT(#526,1);
#466=IFCDERIVEDUNITELEMENT(#505,2);
#467=IFCDERIVEDUNITELEMENT(#518,1);
#468=IFCDERIVEDUNITELEMENT(#505,1);
#469=IFCDERIVEDUNITELEMENT(#502,-1);
#470=IFCDERIVEDUNITELEMENT(#505,5);
#471=IFCDERIVEDUNITELEMENT(#505,3);
#472=IFCDERIVEDUNITELEMENT(#518,1);
#473=IFCDERIVEDUNITELEMENT(#506,-1);
#474=IFCDERIVEDUNITELEMENT(#517,1);
#475=IFCDERIVEDUNITELEMENT(#533,-1);
#476=IFCDERIVEDUNITELEMENT(#518,1);
#477=IFCDERIVEDUNITELEMENT(#526,-1);
#478=IFCDERIVEDUNITELEMENT(#532,-1);
#479=IFCDERIVEDUNITELEMENT(#532,1);
#480=IFCDERIVEDUNITELEMENT(#505,-1);
#481=IFCDERIVEDUNITELEMENT(#528,1);
#482=IFCDERIVEDUNITELEMENT(#506,-1);
#483=IFCDERIVEDUNITELEMENT(#532,-1);
#484=IFCDERIVEDUNITELEMENT(#528,1);
#485=IFCDERIVEDUNITELEMENT(#505,-1);
#486=IFCDERIVEDUNITELEMENT(#532,-1);
#487=IFCDERIVEDUNITELEMENT(#532,-1);
#488=IFCDERIVEDUNITELEMENT(#506,1);
#489=IFCDERIVEDUNITELEMENT(#528,-1);
#490=IFCDERIVEDUNITELEMENT(#528,1);
#491=IFCDERIVEDUNITELEMENT(#506,-1);
#492=IFCDERIVEDUNITELEMENT(#532,-1);
#493=IFCDERIVEDUNITELEMENT(#518,1);
#494=IFCDERIVEDUNITELEMENT(#505,1);
#495=IFCDERIVEDUNITELEMENT(#526,1);
#496=IFCDERIVEDUNITELEMENT(#533,-1);
#497=IFCDERIVEDUNITELEMENT(#507,1);
#498=IFCDERIVEDUNITELEMENT(#533,-1);
#499=IFCDERIVEDUNITELEMENT(#505,6);
#500=IFCDERIVEDUNITELEMENT(#518,1);
#501=IFCDERIVEDUNITELEMENT(#505,2);
#502=IFCCONVERSIONBASEDUNIT(#503,.PLANEANGLEUNIT.,'degree',#504);
#503=IFCDIMENSIONALEXPONENTS(0,0,0,0,0,0,0);
#504=IFCMEASUREWITHUNIT(IFCREAL(0.0174532925199433),#527);
#505=IFCSIUNIT(*,.LENGTHUNIT.,$,.METRE.);
#506=IFCSIUNIT(*,.AREAUNIT.,$,.SQUARE_METRE.);
#507=IFCSIUNIT(*,.VOLUMEUNIT.,$,.CUBIC_METRE.);
#508=IFCSIUNIT(*,.ABSORBEDDOSEUNIT.,$,.GRAY.);
#509=IFCSIUNIT(*,.AMOUNTOFSUBSTANCEUNIT.,$,.MOLE.);
#510=IFCSIUNIT(*,.DOSEEQUIVALENTUNIT.,$,.SIEVERT.);
#511=IFCSIUNIT(*,.ELECTRICCAPACITANCEUNIT.,$,.FARAD.);
#512=IFCSIUNIT(*,.ELECTRICCHARGEUNIT.,$,.COULOMB.);
#513=IFCSIUNIT(*,.ELECTRICCONDUCTANCEUNIT.,$,.SIEMENS.);
#514=IFCSIUNIT(*,.ELECTRICCURRENTUNIT.,$,.AMPERE.);
#515=IFCSIUNIT(*,.ELECTRICRESISTANCEUNIT.,$,.OHM.);
#516=IFCSIUNIT(*,.ELECTRICVOLTAGEUNIT.,$,.VOLT.);
#517=IFCSIUNIT(*,.ENERGYUNIT.,$,.JOULE.);
#518=IFCSIUNIT(*,.FORCEUNIT.,$,.NEWTON.);
#519=IFCSIUNIT(*,.FREQUENCYUNIT.,$,.HERTZ.);
#520=IFCSIUNIT(*,.ILLUMINANCEUNIT.,$,.LUX.);
#521=IFCSIUNIT(*,.INDUCTANCEUNIT.,$,.HENRY.);
#522=IFCSIUNIT(*,.LUMINOUSFLUXUNIT.,$,.LUMEN.);
#523=IFCSIUNIT(*,.LUMINOUSINTENSITYUNIT.,$,.CANDELA.);
#524=IFCSIUNIT(*,.MAGNETICFLUXDENSITYUNIT.,$,.TESLA.);
#525=IFCSIUNIT(*,.MAGNETICFLUXUNIT.,$,.WEBER.);
#526=IFCSIUNIT(*,.MASSUNIT.,$,.GRAM.);
#527=IFCSIUNIT(*,.PLANEANGLEUNIT.,$,.RADIAN.);
#528=IFCSIUNIT(*,.POWERUNIT.,$,.WATT.);
#529=IFCSIUNIT(*,.PRESSUREUNIT.,$,.PASCAL.);
#530=IFCSIUNIT(*,.RADIOACTIVITYUNIT.,$,.BECQUEREL.);
#531=IFCSIUNIT(*,.SOLIDANGLEUNIT.,$,.STERADIAN.);
#532=IFCSIUNIT(*,.THERMODYNAMICTEMPERATUREUNIT.,$,.KELVIN.);
#533=IFCSIUNIT(*,.TIMEUNIT.,$,.SECOND.);
ENDSEC;
END-ISO-10303-21;

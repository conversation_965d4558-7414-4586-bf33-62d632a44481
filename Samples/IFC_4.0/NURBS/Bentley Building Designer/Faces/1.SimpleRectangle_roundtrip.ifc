ISO-10303-21;
HEADER;
FILE_DESCRIPTION(('ViewDefinition [CoordinationView]'),'2;1');
FILE_NAME('','2020-10-30T00:19:01',(),(),'IfcOpenShell 0.6.0b0','IfcOpenShell 0.6.0b0','');
FILE_SCHEMA(('IFC4'));
ENDSEC;
DATA;
#1=IFCBUILDINGSTOREY('0rm7DUqMJhOUcs5v8t1F$L',#18,'Floor 3','Administrative and employee lounge',$,#36,$,'Floor 3',.ELEMENT.,9.144);
#2=IFCBUILDING('3_IkJInE13hR6Xwo4VKOgh',#18,'Bldg 1','3 Story Building',$,#51,$,'Bldg 1',.ELEMENT.,0.,0.,#61);
#3=IFCBUILDINGELEMENTPROXY('1SkVtlXVM5QZmO_SLYA8R9',#18,'Element type: 24','1157*bspline surface!Default',$,#62,#82,'1157*bspline surface!Default',.NOTDEFINED.);
#4=IFCSITE('006_i1V1D0tAKEh_$vljQt',#18,'Site 1','Site 1',$,#83,$,'Site 1',.ELEMENT.,$,$,0.,$,#88);
#5=IFCPROPERTYSET('2rcLYSOKrF02Xv$qFtEF4e',#18,'Project',$,(#89));
#6=IFCPROPERTYSET('2Qswsgonw96KTM1$UoUklF',#18,'Site',$,(#90));
#7=IFCPROPERTYSET('2Ct1rxyLMs3vgB_jB90hMr',#18,'Pset_BuildingCommon',$,(#91));
#8=IFCPROPERTYSET('3OmKLTEF7bgCV0$1Gr7ajZ',#18,'ArchBuilding',$,(#92,#93));
#9=IFCPROPERTYSET('1HjNcqn7ak9HvLTK8j8w1d',#18,'ObjectIdentity',$,(#94));
#10=IFCPROPERTYSET('306EzywKpRfIeis4axlC1d',#18,'ObjectPostalAddress',$,(#95,#96,#97,#98));
#11=IFCPROPERTYSET('381uKge8G33qXIbHr84hB4',#18,'Pset_BuildingStoreyCommon',$,(#99,#101,#102,#104));
#12=IFCPROPERTYSET('0GZmVQOAmJ6zMOk6j3uleN',#18,'ArchFloor',$,(#105,#106,#107,#108,#110,#112,#113,#114));
#13=IFCPROPERTYSET('0A3C_vQUNRQZfA6f5qtBlE',#18,'Floor',$,(#115,#117,#119));
#14=IFCPROPERTYSET('0izo3etGuCpkjROydkcZbT',#18,'StructuralFloorCommon',$,(#120));
#15=IFCPROJECT('1JmBUThP19Z8wED88xGwCF',#18,'BuildingTemplate_US','BuildingTemplate_US',$,'BuildingTemplate_US',$,(#21),#121);
#16=IFCSHAPEREPRESENTATION(#19,'Body','SurfaceModel',(#378));
#17=IFCSHAPEREPRESENTATION(#20,'Box','BoundingBox',(#1621));
#18=IFCOWNERHISTORY(#1623,#1629,$,$,0,$,$,1583400127);
#19=IFCGEOMETRICREPRESENTATIONSUBCONTEXT('Body','Model',*,*,*,*,#21,1.,.MODEL_VIEW.,$);
#20=IFCGEOMETRICREPRESENTATIONSUBCONTEXT('Box','Model',*,*,*,*,#21,1.,.SKETCH_VIEW.,$);
#21=IFCGEOMETRICREPRESENTATIONCONTEXT($,'Model',3,1.E-06,#1631,#1635);
#22=IFCRELCONTAINEDINSPATIALSTRUCTURE('2aJi0CW_wcTJthPGyCEMkK',#18,$,$,(#3),#1);
#23=IFCRELAGGREGATES('1kB3AQvfGpkFm_7uHNRdoP',#18,$,$,#15,(#4));
#24=IFCRELAGGREGATES('0Cp_wDOOr3amQ16IWrGpUy',#18,$,$,#4,(#2));
#25=IFCRELAGGREGATES('1NfacxJUhl1S_ka3CQXVRt',#18,$,$,#2,(#1));
#26=IFCRELDEFINESBYPROPERTIES('1RL1EewHTsROrPcNSlBI56',#18,'Project',$,(#15),#5);
#27=IFCRELDEFINESBYPROPERTIES('3bjMWmv_Cp6LPxmaCnPGwW',#18,'Site',$,(#4),#6);
#28=IFCRELDEFINESBYPROPERTIES('2zOmgYUoQmhp9sZMdCZF7s',#18,'Pset_BuildingCommon',$,(#2),#7);
#29=IFCRELDEFINESBYPROPERTIES('3k0v4EdaEvPD8NSrRFB2MT',#18,'ArchBuilding',$,(#2),#8);
#30=IFCRELDEFINESBYPROPERTIES('3uIgchz4n8nDCCMgq1BBql',#18,'ObjectIdentity',$,(#2),#9);
#31=IFCRELDEFINESBYPROPERTIES('1CCmZ_1eEDpfMO62t1jkZj',#18,'ObjectPostalAddress',$,(#2),#10);
#32=IFCRELDEFINESBYPROPERTIES('24I$F7L8gfSwdAGDvfkVEu',#18,'Pset_BuildingStoreyCommon',$,(#1),#11);
#33=IFCRELDEFINESBYPROPERTIES('1K1DvjfvPzx3hE5w5ZHK0q',#18,'ArchFloor',$,(#1),#12);
#34=IFCRELDEFINESBYPROPERTIES('0lrK04aZ0v6OfwsyTXPzZq',#18,'Floor',$,(#1),#13);
#35=IFCRELDEFINESBYPROPERTIES('1fbDyx4oFQKKacC_b9xURq',#18,'StructuralFloorCommon',$,(#1),#14);
#36=IFCLOCALPLACEMENT(#37,#47);
#37=IFCLOCALPLACEMENT(#38,#43);
#38=IFCLOCALPLACEMENT($,#39);
#39=IFCAXIS2PLACEMENT3D(#40,#41,#42);
#40=IFCCARTESIANPOINT((0.,0.,0.));
#41=IFCDIRECTION((0.,0.,1.));
#42=IFCDIRECTION((1.,0.,0.));
#43=IFCAXIS2PLACEMENT3D(#44,#45,#46);
#44=IFCCARTESIANPOINT((0.,0.,0.));
#45=IFCDIRECTION((0.,0.,1.));
#46=IFCDIRECTION((1.,0.,0.));
#47=IFCAXIS2PLACEMENT3D(#48,#49,#50);
#48=IFCCARTESIANPOINT((0.,0.,9.144));
#49=IFCDIRECTION((0.,0.,1.));
#50=IFCDIRECTION((1.,0.,0.));
#51=IFCLOCALPLACEMENT(#52,#57);
#52=IFCLOCALPLACEMENT($,#53);
#53=IFCAXIS2PLACEMENT3D(#54,#55,#56);
#54=IFCCARTESIANPOINT((0.,0.,0.));
#55=IFCDIRECTION((0.,0.,1.));
#56=IFCDIRECTION((1.,0.,0.));
#57=IFCAXIS2PLACEMENT3D(#58,#59,#60);
#58=IFCCARTESIANPOINT((0.,0.,0.));
#59=IFCDIRECTION((0.,0.,1.));
#60=IFCDIRECTION((1.,0.,0.));
#61=IFCPOSTALADDRESS($,$,$,$,('203 Rickenhouse Drive'),$,'Madison',$,'35789','US');
#62=IFCLOCALPLACEMENT(#63,#78);
#63=IFCLOCALPLACEMENT(#64,#74);
#64=IFCLOCALPLACEMENT(#65,#70);
#65=IFCLOCALPLACEMENT($,#66);
#66=IFCAXIS2PLACEMENT3D(#67,#68,#69);
#67=IFCCARTESIANPOINT((0.,0.,0.));
#68=IFCDIRECTION((0.,0.,1.));
#69=IFCDIRECTION((1.,0.,0.));
#70=IFCAXIS2PLACEMENT3D(#71,#72,#73);
#71=IFCCARTESIANPOINT((0.,0.,0.));
#72=IFCDIRECTION((0.,0.,1.));
#73=IFCDIRECTION((1.,0.,0.));
#74=IFCAXIS2PLACEMENT3D(#75,#76,#77);
#75=IFCCARTESIANPOINT((0.,0.,9.144));
#76=IFCDIRECTION((0.,0.,1.));
#77=IFCDIRECTION((1.,0.,0.));
#78=IFCAXIS2PLACEMENT3D(#79,#80,#81);
#79=IFCCARTESIANPOINT((89.1421,-68.3287,38.6153));
#80=IFCDIRECTION((0.,0.,1.));
#81=IFCDIRECTION((1.,0.,0.));
#82=IFCPRODUCTDEFINITIONSHAPE($,$,(#16,#17));
#83=IFCLOCALPLACEMENT($,#84);
#84=IFCAXIS2PLACEMENT3D(#85,#86,#87);
#85=IFCCARTESIANPOINT((0.,0.,0.));
#86=IFCDIRECTION((0.,0.,1.));
#87=IFCDIRECTION((1.,0.,0.));
#88=IFCPOSTALADDRESS($,$,$,'Site 1',$,$,$,$,$,$);
#89=IFCPROPERTYSINGLEVALUE('ProjectName',$,IFCTEXT('BuildingTemplate_US'),$);
#90=IFCPROPERTYSINGLEVALUE('SiteName',$,IFCTEXT('Site 1'),$);
#91=IFCPROPERTYSINGLEVALUE('YearOfConstruction',$,IFCLABEL('2006'),$);
#92=IFCPROPERTYSINGLEVALUE('BuildingName',$,IFCTEXT('Bldg 1'),$);
#93=IFCPROPERTYSINGLEVALUE('YearConstructed',$,IFCTEXT('2006'),$);
#94=IFCPROPERTYSINGLEVALUE('Description',$,IFCTEXT('3 Story Building'),$);
#95=IFCPROPERTYSINGLEVALUE('Address1',$,IFCTEXT('203 Rickenhouse Drive'),$);
#96=IFCPROPERTYSINGLEVALUE('City',$,IFCTEXT('Madison'),$);
#97=IFCPROPERTYSINGLEVALUE('PostalCode',$,IFCTEXT('35789'),$);
#98=IFCPROPERTYSINGLEVALUE('Country',$,IFCTEXT('US'),$);
#99=IFCPROPERTYSINGLEVALUE('GrossAreaPlanned',$,IFCAREAMEASURE(22296729.6),#100);
#100=IFCSIUNIT(*,.AREAUNIT.,$,.SQUARE_METRE.);
#101=IFCPROPERTYSINGLEVALUE('GrossPlannedArea',$,IFCAREAMEASURE(22296729.6),$);
#102=IFCPROPERTYSINGLEVALUE('NetAreaPlanned',$,IFCAREAMEASURE(22296729.6),#103);
#103=IFCSIUNIT(*,.AREAUNIT.,$,.SQUARE_METRE.);
#104=IFCPROPERTYSINGLEVALUE('NetPlannedArea',$,IFCAREAMEASURE(22296729.6),$);
#105=IFCPROPERTYSINGLEVALUE('FloorName',$,IFCTEXT('Floor 3'),$);
#106=IFCPROPERTYSINGLEVALUE('FinishedFloorElevation',$,IFCREAL(9.144),$);
#107=IFCPROPERTYSINGLEVALUE('Description',$,IFCTEXT('Administrative and employee lounge'),$);
#108=IFCPROPERTYSINGLEVALUE('ApproxLength',$,IFCLENGTHMEASURE(6096.),#109);
#109=IFCSIUNIT(*,.LENGTHUNIT.,$,.METRE.);
#110=IFCPROPERTYSINGLEVALUE('ApproxWidth',$,IFCLENGTHMEASURE(3657.6),#111);
#111=IFCSIUNIT(*,.LENGTHUNIT.,$,.METRE.);
#112=IFCPROPERTYSINGLEVALUE('TypicalFloor',$,IFCBOOLEAN(.F.),$);
#113=IFCPROPERTYSINGLEVALUE('ACSRotation',$,IFCTEXT('0.00000000'),$);
#114=IFCPROPERTYSINGLEVALUE('Annotation',$,IFCBOOLEAN(.T.),$);
#115=IFCPROPERTYSINGLEVALUE('GrossArea',$,IFCAREAMEASURE(22296729.6),#116);
#116=IFCSIUNIT(*,.AREAUNIT.,$,.SQUARE_METRE.);
#117=IFCPROPERTYSINGLEVALUE('NetArea',$,IFCAREAMEASURE(22296729.6),#118);
#118=IFCSIUNIT(*,.AREAUNIT.,$,.SQUARE_METRE.);
#119=IFCPROPERTYSINGLEVALUE('OccupancyType',$,IFCTEXT('Commercial'),$);
#120=IFCPROPERTYSINGLEVALUE('issplicefloor',$,IFCBOOLEAN(.F.),$);
#121=IFCUNITASSIGNMENT((#122,#123,#124,#125,#126,#127,#128,#129,#130,#131,#132,#133,#134,#135,#136,#137,#138,#139,#140,#141,#142,#143,#144,#147,#148,#149,#150,#151,#152,#153,#158,#165,#172,#177,#182,#187,#190,#195,#200,#205,#210,#215,#220,#225,#230,#235,#240,#245,#250,#255,#260,#265,#270,#275,#280,#283,#288,#297,#300,#303,#308,#313,#320,#325,#332,#339,#342,#347,#354,#359,#364,#369,#372,#377));
#122=IFCSIUNIT(*,.LENGTHUNIT.,$,.METRE.);
#123=IFCSIUNIT(*,.AREAUNIT.,$,.SQUARE_METRE.);
#124=IFCSIUNIT(*,.VOLUMEUNIT.,$,.CUBIC_METRE.);
#125=IFCSIUNIT(*,.ABSORBEDDOSEUNIT.,$,.GRAY.);
#126=IFCSIUNIT(*,.AMOUNTOFSUBSTANCEUNIT.,$,.MOLE.);
#127=IFCSIUNIT(*,.DOSEEQUIVALENTUNIT.,$,.SIEVERT.);
#128=IFCSIUNIT(*,.ELECTRICCAPACITANCEUNIT.,$,.FARAD.);
#129=IFCSIUNIT(*,.ELECTRICCHARGEUNIT.,$,.COULOMB.);
#130=IFCSIUNIT(*,.ELECTRICCONDUCTANCEUNIT.,$,.SIEMENS.);
#131=IFCSIUNIT(*,.ELECTRICCURRENTUNIT.,$,.AMPERE.);
#132=IFCSIUNIT(*,.ELECTRICRESISTANCEUNIT.,$,.OHM.);
#133=IFCSIUNIT(*,.ELECTRICVOLTAGEUNIT.,$,.VOLT.);
#134=IFCSIUNIT(*,.ENERGYUNIT.,$,.JOULE.);
#135=IFCSIUNIT(*,.FORCEUNIT.,$,.NEWTON.);
#136=IFCSIUNIT(*,.FREQUENCYUNIT.,$,.HERTZ.);
#137=IFCSIUNIT(*,.ILLUMINANCEUNIT.,$,.LUX.);
#138=IFCSIUNIT(*,.INDUCTANCEUNIT.,$,.HENRY.);
#139=IFCSIUNIT(*,.LUMINOUSFLUXUNIT.,$,.LUMEN.);
#140=IFCSIUNIT(*,.LUMINOUSINTENSITYUNIT.,$,.CANDELA.);
#141=IFCSIUNIT(*,.MAGNETICFLUXDENSITYUNIT.,$,.TESLA.);
#142=IFCSIUNIT(*,.MAGNETICFLUXUNIT.,$,.WEBER.);
#143=IFCSIUNIT(*,.MASSUNIT.,$,.GRAM.);
#144=IFCCONVERSIONBASEDUNIT($,.PLANEANGLEUNIT.,'degree',#145);
#145=IFCMEASUREWITHUNIT(IFCREAL(0.0174532925199433),#146);
#146=IFCSIUNIT(*,.PLANEANGLEUNIT.,$,.RADIAN.);
#147=IFCSIUNIT(*,.POWERUNIT.,$,.WATT.);
#148=IFCSIUNIT(*,.PRESSUREUNIT.,$,.PASCAL.);
#149=IFCSIUNIT(*,.RADIOACTIVITYUNIT.,$,.BECQUEREL.);
#150=IFCSIUNIT(*,.SOLIDANGLEUNIT.,$,.STERADIAN.);
#151=IFCSIUNIT(*,.THERMODYNAMICTEMPERATUREUNIT.,$,.KELVIN.);
#152=IFCSIUNIT(*,.TIMEUNIT.,$,.SECOND.);
#153=IFCDERIVEDUNIT((#154,#156),.ACCELERATIONUNIT.,'(METRE)/(SECOND^2)');
#154=IFCDERIVEDUNITELEMENT(#155,1);
#155=IFCSIUNIT(*,.LENGTHUNIT.,$,.METRE.);
#156=IFCDERIVEDUNITELEMENT(#157,-2);
#157=IFCSIUNIT(*,.TIMEUNIT.,$,.SECOND.);
#158=IFCDERIVEDUNIT((#159,#163),.ANGULARVELOCITYUNIT.,'(DEGREE)/(SECOND)');
#159=IFCDERIVEDUNITELEMENT(#160,1);
#160=IFCCONVERSIONBASEDUNIT($,.PLANEANGLEUNIT.,'degree',#161);
#161=IFCMEASUREWITHUNIT(IFCREAL(0.0174532925199433),#162);
#162=IFCSIUNIT(*,.PLANEANGLEUNIT.,$,.RADIAN.);
#163=IFCDERIVEDUNITELEMENT(#164,-1);
#164=IFCSIUNIT(*,.TIMEUNIT.,$,.SECOND.);
#165=IFCDERIVEDUNIT((#166,#170),.CURVATUREUNIT.,'(DEGREE)/(METRE)');
#166=IFCDERIVEDUNITELEMENT(#167,1);
#167=IFCCONVERSIONBASEDUNIT($,.PLANEANGLEUNIT.,'degree',#168);
#168=IFCMEASUREWITHUNIT(IFCREAL(0.0174532925199433),#169);
#169=IFCSIUNIT(*,.PLANEANGLEUNIT.,$,.RADIAN.);
#170=IFCDERIVEDUNITELEMENT(#171,-1);
#171=IFCSIUNIT(*,.LENGTHUNIT.,$,.METRE.);
#172=IFCDERIVEDUNIT((#173,#175),.DYNAMICVISCOSITYUNIT.,'(PASCAL)(SECOND)');
#173=IFCDERIVEDUNITELEMENT(#174,1);
#174=IFCSIUNIT(*,.PRESSUREUNIT.,$,.PASCAL.);
#175=IFCDERIVEDUNITELEMENT(#176,1);
#176=IFCSIUNIT(*,.TIMEUNIT.,$,.SECOND.);
#177=IFCDERIVEDUNIT((#178,#180),.HEATFLUXDENSITYUNIT.,'(WATT)/(METRE^2)');
#178=IFCDERIVEDUNITELEMENT(#179,1);
#179=IFCSIUNIT(*,.POWERUNIT.,$,.WATT.);
#180=IFCDERIVEDUNITELEMENT(#181,-2);
#181=IFCSIUNIT(*,.LENGTHUNIT.,$,.METRE.);
#182=IFCDERIVEDUNIT((#183,#185),.HEATINGVALUEUNIT.,'(JOULE)/(GRAM)');
#183=IFCDERIVEDUNITELEMENT(#184,1);
#184=IFCSIUNIT(*,.ENERGYUNIT.,$,.JOULE.);
#185=IFCDERIVEDUNITELEMENT(#186,-1);
#186=IFCSIUNIT(*,.MASSUNIT.,$,.GRAM.);
#187=IFCDERIVEDUNIT((#188),.INTEGERCOUNTRATEUNIT.,'1/(SECOND)');
#188=IFCDERIVEDUNITELEMENT(#189,-1);
#189=IFCSIUNIT(*,.TIMEUNIT.,$,.SECOND.);
#190=IFCDERIVEDUNIT((#191,#193),.IONCONCENTRATIONUNIT.,'(GRAM)/(CUBIC_METRE)');
#191=IFCDERIVEDUNITELEMENT(#192,1);
#192=IFCSIUNIT(*,.MASSUNIT.,$,.GRAM.);
#193=IFCDERIVEDUNITELEMENT(#194,-1);
#194=IFCSIUNIT(*,.VOLUMEUNIT.,$,.CUBIC_METRE.);
#195=IFCDERIVEDUNIT((#196,#198),.ISOTHERMALMOISTURECAPACITYUNIT.,'(CUBIC_METRE)/(GRAM)');
#196=IFCDERIVEDUNITELEMENT(#197,1);
#197=IFCSIUNIT(*,.VOLUMEUNIT.,$,.CUBIC_METRE.);
#198=IFCDERIVEDUNITELEMENT(#199,-1);
#199=IFCSIUNIT(*,.MASSUNIT.,$,.GRAM.);
#200=IFCDERIVEDUNIT((#201,#203),.KINEMATICVISCOSITYUNIT.,'(SQUARE_METRE)/(SECOND)');
#201=IFCDERIVEDUNITELEMENT(#202,1);
#202=IFCSIUNIT(*,.AREAUNIT.,$,.SQUARE_METRE.);
#203=IFCDERIVEDUNITELEMENT(#204,-1);
#204=IFCSIUNIT(*,.TIMEUNIT.,$,.SECOND.);
#205=IFCDERIVEDUNIT((#206,#208),.LINEARFORCEUNIT.,'(NEWTON)/(METRE)');
#206=IFCDERIVEDUNITELEMENT(#207,1);
#207=IFCSIUNIT(*,.FORCEUNIT.,$,.NEWTON.);
#208=IFCDERIVEDUNITELEMENT(#209,-1);
#209=IFCSIUNIT(*,.LENGTHUNIT.,$,.METRE.);
#210=IFCDERIVEDUNIT((#211,#213),.LINEARMOMENTUNIT.,'(NEWTON)/(METRE)');
#211=IFCDERIVEDUNITELEMENT(#212,1);
#212=IFCSIUNIT(*,.FORCEUNIT.,$,.NEWTON.);
#213=IFCDERIVEDUNITELEMENT(#214,-1);
#214=IFCSIUNIT(*,.LENGTHUNIT.,$,.METRE.);
#215=IFCDERIVEDUNIT((#216,#218),.LINEARSTIFFNESSUNIT.,'(NEWTON)/(METRE)');
#216=IFCDERIVEDUNITELEMENT(#217,1);
#217=IFCSIUNIT(*,.FORCEUNIT.,$,.NEWTON.);
#218=IFCDERIVEDUNITELEMENT(#219,-1);
#219=IFCSIUNIT(*,.LENGTHUNIT.,$,.METRE.);
#220=IFCDERIVEDUNIT((#221,#223),.LINEARVELOCITYUNIT.,'(METRE)/(SECOND)');
#221=IFCDERIVEDUNITELEMENT(#222,1);
#222=IFCSIUNIT(*,.LENGTHUNIT.,$,.METRE.);
#223=IFCDERIVEDUNITELEMENT(#224,-1);
#224=IFCSIUNIT(*,.TIMEUNIT.,$,.SECOND.);
#225=IFCDERIVEDUNIT((#226,#228),.LUMINOUSINTENSITYDISTRIBUTIONUNIT.,'(CANDELA)/(LUMEN)');
#226=IFCDERIVEDUNITELEMENT(#227,1);
#227=IFCSIUNIT(*,.LUMINOUSINTENSITYUNIT.,$,.CANDELA.);
#228=IFCDERIVEDUNITELEMENT(#229,-1);
#229=IFCSIUNIT(*,.LUMINOUSFLUXUNIT.,$,.LUMEN.);
#230=IFCDERIVEDUNIT((#231,#233),.MASSDENSITYUNIT.,'(GRAM)/(CUBIC_METRE)');
#231=IFCDERIVEDUNITELEMENT(#232,1);
#232=IFCSIUNIT(*,.MASSUNIT.,$,.GRAM.);
#233=IFCDERIVEDUNITELEMENT(#234,-1);
#234=IFCSIUNIT(*,.VOLUMEUNIT.,$,.CUBIC_METRE.);
#235=IFCDERIVEDUNIT((#236,#238),.MASSFLOWRATEUNIT.,'(GRAM)/(SECOND)');
#236=IFCDERIVEDUNITELEMENT(#237,1);
#237=IFCSIUNIT(*,.MASSUNIT.,$,.GRAM.);
#238=IFCDERIVEDUNITELEMENT(#239,-1);
#239=IFCSIUNIT(*,.TIMEUNIT.,$,.SECOND.);
#240=IFCDERIVEDUNIT((#241,#243),.MASSPERLENGTHUNIT.,'(GRAM)/(METRE)');
#241=IFCDERIVEDUNITELEMENT(#242,1);
#242=IFCSIUNIT(*,.MASSUNIT.,$,.GRAM.);
#243=IFCDERIVEDUNITELEMENT(#244,-1);
#244=IFCSIUNIT(*,.LENGTHUNIT.,$,.METRE.);
#245=IFCDERIVEDUNIT((#246,#248),.MODULUSOFELASTICITYUNIT.,'(NEWTON)/(SQUARE_METRE)');
#246=IFCDERIVEDUNITELEMENT(#247,1);
#247=IFCSIUNIT(*,.FORCEUNIT.,$,.NEWTON.);
#248=IFCDERIVEDUNITELEMENT(#249,-1);
#249=IFCSIUNIT(*,.AREAUNIT.,$,.SQUARE_METRE.);
#250=IFCDERIVEDUNIT((#251,#253),.MODULUSOFLINEARSUBGRADEREACTIONUNIT.,'(NEWTON)/(METRE^2)');
#251=IFCDERIVEDUNITELEMENT(#252,1);
#252=IFCSIUNIT(*,.FORCEUNIT.,$,.NEWTON.);
#253=IFCDERIVEDUNITELEMENT(#254,-2);
#254=IFCSIUNIT(*,.LENGTHUNIT.,$,.METRE.);
#255=IFCDERIVEDUNIT((#256,#258),.MODULUSOFROTATIONALSUBGRADEREACTIONUNIT.,'(NEWTON)(METRE)');
#256=IFCDERIVEDUNITELEMENT(#257,1);
#257=IFCSIUNIT(*,.FORCEUNIT.,$,.NEWTON.);
#258=IFCDERIVEDUNITELEMENT(#259,1);
#259=IFCSIUNIT(*,.LENGTHUNIT.,$,.METRE.);
#260=IFCDERIVEDUNIT((#261,#263),.MODULUSOFSUBGRADEREACTIONUNIT.,'(NEWTON)/(CUBIC_METRE)');
#261=IFCDERIVEDUNITELEMENT(#262,1);
#262=IFCSIUNIT(*,.FORCEUNIT.,$,.NEWTON.);
#263=IFCDERIVEDUNITELEMENT(#264,-1);
#264=IFCSIUNIT(*,.VOLUMEUNIT.,$,.CUBIC_METRE.);
#265=IFCDERIVEDUNIT((#266,#268),.MOISTUREDIFFUSIVITYUNIT.,'(CUBIC_METRE)/(SECOND)');
#266=IFCDERIVEDUNITELEMENT(#267,1);
#267=IFCSIUNIT(*,.VOLUMEUNIT.,$,.CUBIC_METRE.);
#268=IFCDERIVEDUNITELEMENT(#269,-1);
#269=IFCSIUNIT(*,.TIMEUNIT.,$,.SECOND.);
#270=IFCDERIVEDUNIT((#271,#273),.MOLECULARWEIGHTUNIT.,'(GRAM)/(MOLE)');
#271=IFCDERIVEDUNITELEMENT(#272,1);
#272=IFCSIUNIT(*,.MASSUNIT.,$,.GRAM.);
#273=IFCDERIVEDUNITELEMENT(#274,-1);
#274=IFCSIUNIT(*,.AMOUNTOFSUBSTANCEUNIT.,$,.MOLE.);
#275=IFCDERIVEDUNIT((#276,#278),.PLANARFORCEUNIT.,'(NEWTON)/(METRE^2)');
#276=IFCDERIVEDUNITELEMENT(#277,1);
#277=IFCSIUNIT(*,.FORCEUNIT.,$,.NEWTON.);
#278=IFCDERIVEDUNITELEMENT(#279,-2);
#279=IFCSIUNIT(*,.LENGTHUNIT.,$,.METRE.);
#280=IFCDERIVEDUNIT((#281),.ROTATIONALFREQUENCYUNIT.,'1/(SECOND)');
#281=IFCDERIVEDUNITELEMENT(#282,-1);
#282=IFCSIUNIT(*,.TIMEUNIT.,$,.SECOND.);
#283=IFCDERIVEDUNIT((#284,#286),.ROTATIONALMASSUNIT.,'(GRAM)(METRE^2)');
#284=IFCDERIVEDUNITELEMENT(#285,1);
#285=IFCSIUNIT(*,.MASSUNIT.,$,.GRAM.);
#286=IFCDERIVEDUNITELEMENT(#287,2);
#287=IFCSIUNIT(*,.LENGTHUNIT.,$,.METRE.);
#288=IFCDERIVEDUNIT((#289,#291,#293),.ROTATIONALSTIFFNESSUNIT.,'(NEWTON)(METRE)/(DEGREE)');
#289=IFCDERIVEDUNITELEMENT(#290,1);
#290=IFCSIUNIT(*,.FORCEUNIT.,$,.NEWTON.);
#291=IFCDERIVEDUNITELEMENT(#292,1);
#292=IFCSIUNIT(*,.LENGTHUNIT.,$,.METRE.);
#293=IFCDERIVEDUNITELEMENT(#294,-1);
#294=IFCCONVERSIONBASEDUNIT($,.PLANEANGLEUNIT.,'degree',#295);
#295=IFCMEASUREWITHUNIT(IFCREAL(0.0174532925199433),#296);
#296=IFCSIUNIT(*,.PLANEANGLEUNIT.,$,.RADIAN.);
#297=IFCDERIVEDUNIT((#298),.SECTIONAREAINTEGRALUNIT.,'(METRE^5)');
#298=IFCDERIVEDUNITELEMENT(#299,5);
#299=IFCSIUNIT(*,.LENGTHUNIT.,$,.METRE.);
#300=IFCDERIVEDUNIT((#301),.SECTIONMODULUSUNIT.,'(METRE^3)');
#301=IFCDERIVEDUNITELEMENT(#302,3);
#302=IFCSIUNIT(*,.LENGTHUNIT.,$,.METRE.);
#303=IFCDERIVEDUNIT((#304,#306),.SHEARMODULUSUNIT.,'(NEWTON)/(SQUARE_METRE)');
#304=IFCDERIVEDUNITELEMENT(#305,1);
#305=IFCSIUNIT(*,.FORCEUNIT.,$,.NEWTON.);
#306=IFCDERIVEDUNITELEMENT(#307,-1);
#307=IFCSIUNIT(*,.AREAUNIT.,$,.SQUARE_METRE.);
#308=IFCDERIVEDUNIT((#309,#311),.SOUNDPOWERUNIT.,'(JOULE)/(SECOND)');
#309=IFCDERIVEDUNITELEMENT(#310,1);
#310=IFCSIUNIT(*,.ENERGYUNIT.,$,.JOULE.);
#311=IFCDERIVEDUNITELEMENT(#312,-1);
#312=IFCSIUNIT(*,.TIMEUNIT.,$,.SECOND.);
#313=IFCDERIVEDUNIT((#314,#316,#318),.SPECIFICHEATCAPACITYUNIT.,'(NEWTON)/(GRAM)(KELVIN)');
#314=IFCDERIVEDUNITELEMENT(#315,1);
#315=IFCSIUNIT(*,.FORCEUNIT.,$,.NEWTON.);
#316=IFCDERIVEDUNITELEMENT(#317,-1);
#317=IFCSIUNIT(*,.MASSUNIT.,$,.GRAM.);
#318=IFCDERIVEDUNITELEMENT(#319,-1);
#319=IFCSIUNIT(*,.THERMODYNAMICTEMPERATUREUNIT.,$,.KELVIN.);
#320=IFCDERIVEDUNIT((#321,#323),.TEMPERATUREGRADIENTUNIT.,'(KELVIN)/(METRE)');
#321=IFCDERIVEDUNITELEMENT(#322,1);
#322=IFCSIUNIT(*,.THERMODYNAMICTEMPERATUREUNIT.,$,.KELVIN.);
#323=IFCDERIVEDUNITELEMENT(#324,-1);
#324=IFCSIUNIT(*,.LENGTHUNIT.,$,.METRE.);
#325=IFCDERIVEDUNIT((#326,#328,#330),.THERMALADMITTANCEUNIT.,'(WATT)/(SQUARE_METRE)(KELVIN)');
#326=IFCDERIVEDUNITELEMENT(#327,1);
#327=IFCSIUNIT(*,.POWERUNIT.,$,.WATT.);
#328=IFCDERIVEDUNITELEMENT(#329,-1);
#329=IFCSIUNIT(*,.AREAUNIT.,$,.SQUARE_METRE.);
#330=IFCDERIVEDUNITELEMENT(#331,-1);
#331=IFCSIUNIT(*,.THERMODYNAMICTEMPERATUREUNIT.,$,.KELVIN.);
#332=IFCDERIVEDUNIT((#333,#335,#337),.THERMALCONDUCTANCEUNIT.,'(WATT)/(METRE)(KELVIN)');
#333=IFCDERIVEDUNITELEMENT(#334,1);
#334=IFCSIUNIT(*,.POWERUNIT.,$,.WATT.);
#335=IFCDERIVEDUNITELEMENT(#336,-1);
#336=IFCSIUNIT(*,.LENGTHUNIT.,$,.METRE.);
#337=IFCDERIVEDUNITELEMENT(#338,-1);
#338=IFCSIUNIT(*,.THERMODYNAMICTEMPERATUREUNIT.,$,.KELVIN.);
#339=IFCDERIVEDUNIT((#340),.THERMALEXPANSIONCOEFFICIENTUNIT.,'1/(KELVIN)');
#340=IFCDERIVEDUNITELEMENT(#341,-1);
#341=IFCSIUNIT(*,.THERMODYNAMICTEMPERATUREUNIT.,$,.KELVIN.);
#342=IFCDERIVEDUNIT((#343,#345),.THERMALRESISTANCEUNIT.,'(SQUARE_METRE)/(WATT)');
#343=IFCDERIVEDUNITELEMENT(#344,1);
#344=IFCSIUNIT(*,.AREAUNIT.,$,.SQUARE_METRE.);
#345=IFCDERIVEDUNITELEMENT(#346,-1);
#346=IFCSIUNIT(*,.POWERUNIT.,$,.WATT.);
#347=IFCDERIVEDUNIT((#348,#350,#352),.THERMALTRANSMITTANCEUNIT.,'(WATT)/(SQUARE_METRE)(KELVIN)');
#348=IFCDERIVEDUNITELEMENT(#349,1);
#349=IFCSIUNIT(*,.POWERUNIT.,$,.WATT.);
#350=IFCDERIVEDUNITELEMENT(#351,-1);
#351=IFCSIUNIT(*,.AREAUNIT.,$,.SQUARE_METRE.);
#352=IFCDERIVEDUNITELEMENT(#353,-1);
#353=IFCSIUNIT(*,.THERMODYNAMICTEMPERATUREUNIT.,$,.KELVIN.);
#354=IFCDERIVEDUNIT((#355,#357),.TORQUEUNIT.,'(NEWTON)(METRE)');
#355=IFCDERIVEDUNITELEMENT(#356,1);
#356=IFCSIUNIT(*,.FORCEUNIT.,$,.NEWTON.);
#357=IFCDERIVEDUNITELEMENT(#358,1);
#358=IFCSIUNIT(*,.LENGTHUNIT.,$,.METRE.);
#359=IFCDERIVEDUNIT((#360,#362),.VAPORPERMEABILITYUNIT.,'(GRAM)/(SECOND)');
#360=IFCDERIVEDUNITELEMENT(#361,1);
#361=IFCSIUNIT(*,.MASSUNIT.,$,.GRAM.);
#362=IFCDERIVEDUNITELEMENT(#363,-1);
#363=IFCSIUNIT(*,.TIMEUNIT.,$,.SECOND.);
#364=IFCDERIVEDUNIT((#365,#367),.VOLUMETRICFLOWRATEUNIT.,'(CUBIC_METRE)/(SECOND)');
#365=IFCDERIVEDUNITELEMENT(#366,1);
#366=IFCSIUNIT(*,.VOLUMEUNIT.,$,.CUBIC_METRE.);
#367=IFCDERIVEDUNITELEMENT(#368,-1);
#368=IFCSIUNIT(*,.TIMEUNIT.,$,.SECOND.);
#369=IFCDERIVEDUNIT((#370),.WARPINGCONSTANTUNIT.,'(METRE^6)');
#370=IFCDERIVEDUNITELEMENT(#371,6);
#371=IFCSIUNIT(*,.LENGTHUNIT.,$,.METRE.);
#372=IFCDERIVEDUNIT((#373,#375),.WARPINGMOMENTUNIT.,'(NEWTON)(METRE^2)');
#373=IFCDERIVEDUNITELEMENT(#374,1);
#374=IFCSIUNIT(*,.FORCEUNIT.,$,.NEWTON.);
#375=IFCDERIVEDUNITELEMENT(#376,2);
#376=IFCSIUNIT(*,.LENGTHUNIT.,$,.METRE.);
#377=IFCMONETARYUNIT('USD');
#378=IFCSHELLBASEDSURFACEMODEL((#379));
#379=IFCOPENSHELL((#380));
#380=IFCADVANCEDFACE((#381),#1395,.T.);
#381=IFCFACEOUTERBOUND(#382,.T.);
#382=IFCEDGELOOP((#383,#636,#889,#1142));
#383=IFCORIENTEDEDGE(*,*,#384,.T.);
#384=IFCEDGECURVE(#385,#387,#389,.T.);
#385=IFCVERTEXPOINT(#386);
#386=IFCCARTESIANPOINT((3.5623730998E-05,1.0532591771E-05,1.93875932375));
#387=IFCVERTEXPOINT(#388);
#388=IFCCARTESIANPOINT((15.0000356237,1.0532591771E-05,0.59627198567));
#389=IFCSURFACECURVE(#390,(#406),.PCURVE_S1.);
#390=IFCBSPLINECURVEWITHKNOTS(9,(#391,#392,#393,#394,#395,#396,#397,#398,#399,#400,#401,#402,#403,#404,#405),.UNSPECIFIED.,.F.,.F.,(10,1,1,1,1,1,10),(0.,0.166666666666667,0.333333333333333,0.5,0.666666666666667,0.833333333333333,1.),.UNSPECIFIED.);
#391=IFCCARTESIANPOINT((3.5623730998E-05,1.0532591771E-05,1.93875932375));
#392=IFCCARTESIANPOINT((1.07146419516,1.0532591771E-05,1.93875932375));
#393=IFCCARTESIANPOINT((2.14289276659,1.0532591771E-05,1.93875932375));
#394=IFCCARTESIANPOINT((3.214321338,1.0532591771E-05,1.93875932375));
#395=IFCCARTESIANPOINT((4.2857499094,1.0532591771E-05,1.93875932375));
#396=IFCCARTESIANPOINT((5.3571784809,1.0532591771E-05,1.93875932375));
#397=IFCCARTESIANPOINT((6.4286070523,1.0532591771E-05,1.93875932375));
#398=IFCCARTESIANPOINT((7.5000356237,1.0532591771E-05,1.93875932375));
#399=IFCCARTESIANPOINT((8.5714641952,1.0532591771E-05,1.93875932375));
#400=IFCCARTESIANPOINT((9.6428927666,1.0532591771E-05,1.93875932375));
#401=IFCCARTESIANPOINT((10.714321338,1.0532591771E-05,1.93875932375));
#402=IFCCARTESIANPOINT((11.7857499094,1.0532591771E-05,1.93875932375));
#403=IFCCARTESIANPOINT((12.8571784809,1.0532591771E-05,1.93875932375));
#404=IFCCARTESIANPOINT((13.9286070523,1.0532591771E-05,1.93875932375));
#405=IFCCARTESIANPOINT((15.0000356237,1.0532591771E-05,0.59627198567));
#406=IFCPCURVE(#407,#633);
#407=IFCBSPLINESURFACEWITHKNOTS(9,9,((#408,#409,#410,#411,#412,#413,#414,#415,#416,#417,#418,#419,#420,#421,#422),(#423,#424,#425,#426,#427,#428,#429,#430,#431,#432,#433,#434,#435,#436,#437),(#438,#439,#440,#441,#442,#443,#444,#445,#446,#447,#448,#449,#450,#451,#452),(#453,#454,#455,#456,#457,#458,#459,#460,#461,#462,#463,#464,#465,#466,#467),(#468,#469,#470,#471,#472,#473,#474,#475,#476,#477,#478,#479,#480,#481,#482),(#483,#484,#485,#486,#487,#488,#489,#490,#491,#492,#493,#494,#495,#496,#497),(#498,#499,#500,#501,#502,#503,#504,#505,#506,#507,#508,#509,#510,#511,#512),(#513,#514,#515,#516,#517,#518,#519,#520,#521,#522,#523,#524,#525,#526,#527),(#528,#529,#530,#531,#532,#533,#534,#535,#536,#537,#538,#539,#540,#541,#542),(#543,#544,#545,#546,#547,#548,#549,#550,#551,#552,#553,#554,#555,#556,#557),(#558,#559,#560,#561,#562,#563,#564,#565,#566,#567,#568,#569,#570,#571,#572),(#573,#574,#575,#576,#577,#578,#579,#580,#581,#582,#583,#584,#585,#586,#587),(#588,#589,#590,#591,#592,#593,#594,#595,#596,#597,#598,#599,#600,#601,#602),(#603,#604,#605,#606,#607,#608,#609,#610,#611,#612,#613,#614,#615,#616,#617),(#618,#619,#620,#621,#622,#623,#624,#625,#626,#627,#628,#629,#630,#631,#632)),.UNSPECIFIED.,.F.,.F.,.F.,(10,1,1,1,1,1,10),(10,1,1,1,1,1,10),(0.,0.166666666666667,0.333333333333333,0.5,0.666666666666667,0.833333333333333,1.),(0.,0.166666666666667,0.333333333333333,0.5,0.666666666666667,0.833333333333333,1.),.UNSPECIFIED.);
#408=IFCCARTESIANPOINT((3.5623730998E-05,1.0532591771E-05,1.93875932375));
#409=IFCCARTESIANPOINT((3.5623730998E-05,1.07143910402,1.93875932375));
#410=IFCCARTESIANPOINT((3.5623730905E-05,2.14286767545,3.1754724911));
#411=IFCCARTESIANPOINT((3.5623730998E-05,3.2142962469,1.93875932375));
#412=IFCCARTESIANPOINT((3.5623730905E-05,4.2857248183,3.1754724911));
#413=IFCCARTESIANPOINT((3.5623730998E-05,5.3571533897,1.93875932375));
#414=IFCCARTESIANPOINT((3.5623730998E-05,6.4285819612,1.93875932375));
#415=IFCCARTESIANPOINT((3.5623730998E-05,7.5000105326,1.93875932375));
#416=IFCCARTESIANPOINT((3.5623730905E-05,8.571439104,3.1754724911));
#417=IFCCARTESIANPOINT((3.5623730998E-05,9.6428676754,1.93875932375));
#418=IFCCARTESIANPOINT((3.5623730998E-05,10.7142962469,1.93875932375));
#419=IFCCARTESIANPOINT((3.5623730905E-05,11.7857248183,3.1754724911));
#420=IFCCARTESIANPOINT((3.5623730998E-05,12.8571533897,1.93875932375));
#421=IFCCARTESIANPOINT((3.5623730998E-05,13.9285819612,1.93875932375));
#422=IFCCARTESIANPOINT((3.5623730998E-05,15.0000105326,1.93875932375));
#423=IFCCARTESIANPOINT((1.07146419516,1.0532591771E-05,1.93875932375));
#424=IFCCARTESIANPOINT((1.07146419516,1.07143910402,1.93875932375));
#425=IFCCARTESIANPOINT((1.07146419516,2.14286767545,1.93875932375));
#426=IFCCARTESIANPOINT((1.07146419516,3.2142962469,1.93875932375));
#427=IFCCARTESIANPOINT((1.07146419516,4.2857248183,1.93875932375));
#428=IFCCARTESIANPOINT((1.07146419516,5.3571533897,1.93875932375));
#429=IFCCARTESIANPOINT((1.07146419516,6.4285819612,1.93875932375));
#430=IFCCARTESIANPOINT((1.07146419516,7.5000105326,1.93875932375));
#431=IFCCARTESIANPOINT((1.07146419516,8.571439104,1.93875932375));
#432=IFCCARTESIANPOINT((1.07146419516,9.6428676754,1.93875932375));
#433=IFCCARTESIANPOINT((1.07146419516,10.7142962469,1.93875932375));
#434=IFCCARTESIANPOINT((1.07146419516,11.7857248183,1.93875932375));
#435=IFCCARTESIANPOINT((1.07146419516,12.8571533897,1.93875932375));
#436=IFCCARTESIANPOINT((1.07146419516,13.9285819612,1.93875932375));
#437=IFCCARTESIANPOINT((1.04385994151,14.9865837438,1.54886729649));
#438=IFCCARTESIANPOINT((2.14289276659,1.0532591771E-05,1.93875932375));
#439=IFCCARTESIANPOINT((2.14289276659,1.07143910402,1.93875932375));
#440=IFCCARTESIANPOINT((2.14289276659,2.14286767545,1.93875932375));
#441=IFCCARTESIANPOINT((2.14289276659,3.2142962469,1.93875932375));
#442=IFCCARTESIANPOINT((2.14289276659,4.2857248183,1.93875932375));
#443=IFCCARTESIANPOINT((2.14289276659,5.3571533897,1.93875932375));
#444=IFCCARTESIANPOINT((2.14289276659,6.4285819612,1.93875932375));
#445=IFCCARTESIANPOINT((2.14289276659,7.5000105326,1.93875932375));
#446=IFCCARTESIANPOINT((2.14289276659,8.571439104,1.93875932375));
#447=IFCCARTESIANPOINT((2.14289276659,9.6428676754,1.93875932375));
#448=IFCCARTESIANPOINT((2.14289276659,10.7142962469,1.93875932375));
#449=IFCCARTESIANPOINT((2.14289276659,11.7857248183,1.93875932375));
#450=IFCCARTESIANPOINT((2.14289276659,12.8571533897,5.1287882986));
#451=IFCCARTESIANPOINT((2.14289276659,13.9285819612,1.93875932375));
#452=IFCCARTESIANPOINT((2.14289276659,15.0000105326,1.74110678043));
#453=IFCCARTESIANPOINT((3.214321338,1.0532591771E-05,1.93875932375));
#454=IFCCARTESIANPOINT((3.214321338,1.07143910402,1.93875932375));
#455=IFCCARTESIANPOINT((3.214321338,2.14286767545,5.18065991E-05));
#456=IFCCARTESIANPOINT((3.214321338,3.2142962469,1.93875932375));
#457=IFCCARTESIANPOINT((3.214321338,4.2857248183,1.93875932375));
#458=IFCCARTESIANPOINT((3.214321338,5.3571533897,5.18065991E-05));
#459=IFCCARTESIANPOINT((3.214321338,6.4285819612,1.93875932375));
#460=IFCCARTESIANPOINT((3.214321338,7.5000105326,1.93875932375));
#461=IFCCARTESIANPOINT((3.214321338,8.571439104,1.93875932375));
#462=IFCCARTESIANPOINT((3.214321338,9.6428676754,1.93875932375));
#463=IFCCARTESIANPOINT((3.214321338,10.7142962469,1.93875932375));
#464=IFCCARTESIANPOINT((3.214321338,11.7857248183,1.93875932375));
#465=IFCCARTESIANPOINT((3.214321338,12.8571533897,5.1287882986));
#466=IFCCARTESIANPOINT((3.214321338,13.9285819612,1.93875932375));
#467=IFCCARTESIANPOINT((3.214321338,15.0000105326,1.93875932375));
#468=IFCCARTESIANPOINT((4.2857499094,1.0532591771E-05,1.93875932375));
#469=IFCCARTESIANPOINT((4.2857499094,1.07143910402,1.93875932375));
#470=IFCCARTESIANPOINT((4.2857499094,2.14286767545,1.93875932375));
#471=IFCCARTESIANPOINT((4.2857499094,3.2142962469,1.93875932375));
#472=IFCCARTESIANPOINT((4.2857499094,4.2857248183,1.93875932375));
#473=IFCCARTESIANPOINT((4.2857499094,5.3571533897,1.93875932375));
#474=IFCCARTESIANPOINT((4.2857499094,6.4285819612,1.93875932375));
#475=IFCCARTESIANPOINT((4.2857499094,7.5000105326,1.93875932375));
#476=IFCCARTESIANPOINT((4.2857499094,8.571439104,1.93875932375));
#477=IFCCARTESIANPOINT((4.2857499094,9.6428676754,1.93875932375));
#478=IFCCARTESIANPOINT((4.2857499094,10.7142962469,1.93875932375));
#479=IFCCARTESIANPOINT((4.2857499094,11.7857248183,1.93875932375));
#480=IFCCARTESIANPOINT((4.2857499094,12.8571533897,1.93875932375));
#481=IFCCARTESIANPOINT((4.2857499094,13.9285819612,1.93875932375));
#482=IFCCARTESIANPOINT((4.2857499094,15.0000105326,1.93875932375));
#483=IFCCARTESIANPOINT((5.3571784809,1.0532591771E-05,1.93875932375));
#484=IFCCARTESIANPOINT((5.3571784809,1.07143910402,1.93875932375));
#485=IFCCARTESIANPOINT((5.3571784809,2.14286767545,1.93875932375));
#486=IFCCARTESIANPOINT((5.3571784809,3.2142962469,1.93875932375));
#487=IFCCARTESIANPOINT((5.3571784809,4.2857248183,1.93875932375));
#488=IFCCARTESIANPOINT((5.3571784809,5.3571533897,1.93875932375));
#489=IFCCARTESIANPOINT((5.3571784809,6.4285819612,1.93875932375));
#490=IFCCARTESIANPOINT((5.3571784809,7.5000105326,1.93875932375));
#491=IFCCARTESIANPOINT((5.3571784809,8.571439104,1.93875932375));
#492=IFCCARTESIANPOINT((5.3571784809,9.6428676754,1.93875932375));
#493=IFCCARTESIANPOINT((5.3571784809,10.7142962469,5.1287882986));
#494=IFCCARTESIANPOINT((5.3571784809,11.7857248183,1.93875932375));
#495=IFCCARTESIANPOINT((5.3571784809,12.8571533897,1.93875932375));
#496=IFCCARTESIANPOINT((5.3571784809,13.9285819612,1.93875932375));
#497=IFCCARTESIANPOINT((5.3571784809,15.0000105326,1.93875932375));
#498=IFCCARTESIANPOINT((6.4286070523,1.0532591771E-05,1.93875932375));
#499=IFCCARTESIANPOINT((6.4286070523,1.07143910402,1.93875932375));
#500=IFCCARTESIANPOINT((6.4286070523,2.14286767545,1.93875932375));
#501=IFCCARTESIANPOINT((6.4286070523,3.2142962469,1.93875932375));
#502=IFCCARTESIANPOINT((6.4286070523,4.2857248183,1.93875932375));
#503=IFCCARTESIANPOINT((6.4286070523,5.3571533897,1.93875932375));
#504=IFCCARTESIANPOINT((6.4286070523,6.4285819612,5.18065991E-05));
#505=IFCCARTESIANPOINT((6.4286070523,7.5000105326,1.93875932375));
#506=IFCCARTESIANPOINT((6.4286070523,8.571439104,1.93875932375));
#507=IFCCARTESIANPOINT((6.4286070523,9.6428676754,1.93875932375));
#508=IFCCARTESIANPOINT((6.4286070523,10.7142962469,5.1287882986));
#509=IFCCARTESIANPOINT((6.4286070523,11.7857248183,1.93875932375));
#510=IFCCARTESIANPOINT((6.4286070523,12.8571533897,1.93875932375));
#511=IFCCARTESIANPOINT((6.4286070523,13.9285819612,1.93875932375));
#512=IFCCARTESIANPOINT((6.4286070523,15.0000105326,1.93875932375));
#513=IFCCARTESIANPOINT((7.5000356237,1.0532591771E-05,1.93875932375));
#514=IFCCARTESIANPOINT((7.5000356237,1.07143910402,1.93875932375));
#515=IFCCARTESIANPOINT((7.5000356237,2.14286767545,1.93875932375));
#516=IFCCARTESIANPOINT((7.5000356237,3.2142962469,1.93875932375));
#517=IFCCARTESIANPOINT((7.5000356237,4.2857248183,1.93875932375));
#518=IFCCARTESIANPOINT((7.5000356237,5.3571533897,1.93875932375));
#519=IFCCARTESIANPOINT((7.5000356237,6.4285819612,1.93875932375));
#520=IFCCARTESIANPOINT((7.5000356237,7.5000105326,1.93875932375));
#521=IFCCARTESIANPOINT((7.5000356237,8.571439104,1.93875932375));
#522=IFCCARTESIANPOINT((7.5000356237,9.6428676754,5.1287882986));
#523=IFCCARTESIANPOINT((7.5000356237,10.7142962469,1.93875932375));
#524=IFCCARTESIANPOINT((7.5000356237,11.7857248183,1.93875932375));
#525=IFCCARTESIANPOINT((7.5000356237,12.8571533897,1.93875932375));
#526=IFCCARTESIANPOINT((7.5000356237,13.9285819612,1.93875932375));
#527=IFCCARTESIANPOINT((7.5000356237,15.0000105326,1.93875932375));
#528=IFCCARTESIANPOINT((8.5714641952,1.0532591771E-05,1.93875932375));
#529=IFCCARTESIANPOINT((8.5714641952,1.07143910402,1.93875932375));
#530=IFCCARTESIANPOINT((8.5714641952,2.14286767545,1.93875932375));
#531=IFCCARTESIANPOINT((8.5714641952,3.2142962469,1.93875932375));
#532=IFCCARTESIANPOINT((8.5714641952,4.2857248183,1.93875932375));
#533=IFCCARTESIANPOINT((8.5714641952,5.3571533897,1.93875932375));
#534=IFCCARTESIANPOINT((8.5714641952,6.4285819612,1.93875932375));
#535=IFCCARTESIANPOINT((8.5714641952,7.5000105326,1.93875932375));
#536=IFCCARTESIANPOINT((8.5714641952,8.571439104,1.93875932375));
#537=IFCCARTESIANPOINT((8.5714641952,9.6428676754,1.93875932375));
#538=IFCCARTESIANPOINT((8.5714641952,10.7142962469,1.93875932375));
#539=IFCCARTESIANPOINT((8.5714641952,11.7857248183,1.93875932375));
#540=IFCCARTESIANPOINT((8.5714641952,12.8571533897,1.93875932375));
#541=IFCCARTESIANPOINT((8.5714641952,13.9285819612,1.93875932375));
#542=IFCCARTESIANPOINT((8.5714641952,15.0000105326,1.93875932375));
#543=IFCCARTESIANPOINT((9.6428927666,1.0532591771E-05,1.93875932375));
#544=IFCCARTESIANPOINT((9.6428927666,1.07143910402,1.93875932375));
#545=IFCCARTESIANPOINT((9.6428927666,2.14286767545,1.93875932375));
#546=IFCCARTESIANPOINT((9.6428927666,3.2142962469,1.93875932375));
#547=IFCCARTESIANPOINT((9.6428927666,4.2857248183,1.93875932375));
#548=IFCCARTESIANPOINT((9.6428927666,5.3571533897,1.93875932375));
#549=IFCCARTESIANPOINT((9.6428927666,6.4285819612,5.18065991E-05));
#550=IFCCARTESIANPOINT((9.6428927666,7.5000105326,1.93875932375));
#551=IFCCARTESIANPOINT((9.6428927666,8.571439104,1.93875932375));
#552=IFCCARTESIANPOINT((9.6428927666,9.6428676754,1.93875932375));
#553=IFCCARTESIANPOINT((9.6428927666,10.7142962469,1.93875932375));
#554=IFCCARTESIANPOINT((9.6428927666,11.7857248183,1.93875932375));
#555=IFCCARTESIANPOINT((9.6428927666,12.8571533897,1.93875932375));
#556=IFCCARTESIANPOINT((9.6428927666,13.9285819612,1.93875932375));
#557=IFCCARTESIANPOINT((10.0962936606,15.0376487171,1.35588611674));
#558=IFCCARTESIANPOINT((10.714321338,1.0532591771E-05,1.93875932375));
#559=IFCCARTESIANPOINT((10.714321338,1.07143910402,1.93875932375));
#560=IFCCARTESIANPOINT((10.714321338,2.14286767545,1.93875932375));
#561=IFCCARTESIANPOINT((10.714321338,3.2142962469,1.93875932375));
#562=IFCCARTESIANPOINT((10.714321338,4.2857248183,5.18065991E-05));
#563=IFCCARTESIANPOINT((10.714321338,5.3571533897,1.93875932375));
#564=IFCCARTESIANPOINT((10.714321338,6.4285819612,1.93875932375));
#565=IFCCARTESIANPOINT((10.714321338,7.5000105326,1.93875932375));
#566=IFCCARTESIANPOINT((10.714321338,8.571439104,5.18065991E-05));
#567=IFCCARTESIANPOINT((10.714321338,9.6428676754,1.93875932375));
#568=IFCCARTESIANPOINT((10.714321338,10.7142962469,1.93875932375));
#569=IFCCARTESIANPOINT((10.714321338,11.7857248183,1.93875932375));
#570=IFCCARTESIANPOINT((10.714321338,12.8571533897,1.93875932375));
#571=IFCCARTESIANPOINT((10.714321338,13.9285819612,1.93875932375));
#572=IFCCARTESIANPOINT((10.714321338,15.0000105326,1.93875932375));
#573=IFCCARTESIANPOINT((11.7857499094,1.0532591771E-05,1.93875932375));
#574=IFCCARTESIANPOINT((11.7857499094,1.07143910402,1.93875932375));
#575=IFCCARTESIANPOINT((11.7857499094,2.14286767545,1.93875932375));
#576=IFCCARTESIANPOINT((11.7857499094,3.2142962469,1.93875932375));
#577=IFCCARTESIANPOINT((11.7857499094,4.2857248183,1.93875932375));
#578=IFCCARTESIANPOINT((11.7857499094,5.3571533897,1.93875932375));
#579=IFCCARTESIANPOINT((11.7857499094,6.4285819612,1.93875932375));
#580=IFCCARTESIANPOINT((11.7857499094,7.5000105326,1.93875932375));
#581=IFCCARTESIANPOINT((11.7857499094,8.571439104,1.93875932375));
#582=IFCCARTESIANPOINT((11.7857499094,9.6428676754,1.93875932375));
#583=IFCCARTESIANPOINT((11.7857499094,10.7142962469,1.93875932375));
#584=IFCCARTESIANPOINT((11.7857499094,11.7857248183,1.93875932375));
#585=IFCCARTESIANPOINT((11.7857499094,12.8571533897,1.93875932375));
#586=IFCCARTESIANPOINT((11.7857499094,13.9285819612,1.93875932375));
#587=IFCCARTESIANPOINT((11.3978907176,14.8693563406,2.44049268426));
#588=IFCCARTESIANPOINT((12.8571784809,1.0532591771E-05,1.93875932375));
#589=IFCCARTESIANPOINT((12.8571784809,1.07143910402,1.93875932375));
#590=IFCCARTESIANPOINT((12.8571784809,2.14286767545,1.93875932375));
#591=IFCCARTESIANPOINT((12.8571784809,3.2142962469,1.93875932375));
#592=IFCCARTESIANPOINT((12.8571784809,4.2857248183,1.93875932375));
#593=IFCCARTESIANPOINT((12.8571784809,5.3571533897,1.93875932375));
#594=IFCCARTESIANPOINT((12.8571784809,6.4285819612,1.93875932375));
#595=IFCCARTESIANPOINT((12.8571784809,7.5000105326,5.18065991E-05));
#596=IFCCARTESIANPOINT((12.8571784809,8.571439104,1.93875932375));
#597=IFCCARTESIANPOINT((12.8571784809,9.6428676754,1.93875932375));
#598=IFCCARTESIANPOINT((12.8571784809,10.7142962469,1.93875932375));
#599=IFCCARTESIANPOINT((12.8571784809,11.7857248183,1.93875932375));
#600=IFCCARTESIANPOINT((12.8571784809,12.8571533897,1.93875932375));
#601=IFCCARTESIANPOINT((12.8571784809,13.9285819612,1.93875932375));
#602=IFCCARTESIANPOINT((12.8568370215,14.9497010766,2.56095173322));
#603=IFCCARTESIANPOINT((13.9286070523,1.0532591771E-05,1.93875932375));
#604=IFCCARTESIANPOINT((13.9286070523,1.07143910402,1.93875932375));
#605=IFCCARTESIANPOINT((13.9286070523,2.14286767545,1.93875932375));
#606=IFCCARTESIANPOINT((13.9286070523,3.2142962469,1.93875932375));
#607=IFCCARTESIANPOINT((13.9286070523,4.2857248183,1.93875932375));
#608=IFCCARTESIANPOINT((13.9286070523,5.3571533897,1.93875932375));
#609=IFCCARTESIANPOINT((13.9286070523,6.4285819612,1.93875932375));
#610=IFCCARTESIANPOINT((13.9286070523,7.5000105326,1.93875932375));
#611=IFCCARTESIANPOINT((13.9286070523,8.571439104,1.93875932375));
#612=IFCCARTESIANPOINT((13.9286070523,9.6428676754,1.93875932375));
#613=IFCCARTESIANPOINT((13.9286070523,10.7142962469,1.93875932375));
#614=IFCCARTESIANPOINT((13.9286070523,11.7857248183,1.93875932375));
#615=IFCCARTESIANPOINT((13.9286070523,12.8571533897,1.93875932375));
#616=IFCCARTESIANPOINT((13.9286070523,13.9285819612,1.93875932375));
#617=IFCCARTESIANPOINT((10.6452090386,11.7314780592,4.9981805335));
#618=IFCCARTESIANPOINT((15.0000356237,1.0532591771E-05,0.59627198567));
#619=IFCCARTESIANPOINT((15.0000356237,1.07143910402,1.93875932375));
#620=IFCCARTESIANPOINT((15.0000356237,2.14286767545,1.93875932375));
#621=IFCCARTESIANPOINT((15.0000356237,3.2142962469,1.93875932375));
#622=IFCCARTESIANPOINT((15.0000356237,4.2857248183,1.93875932375));
#623=IFCCARTESIANPOINT((15.0000356237,5.3571533897,1.93875932375));
#624=IFCCARTESIANPOINT((15.0000356237,6.4285819612,0.59627198567));
#625=IFCCARTESIANPOINT((15.0000356237,7.5000105326,1.93875932375));
#626=IFCCARTESIANPOINT((15.0000356237,8.571439104,1.93875932375));
#627=IFCCARTESIANPOINT((15.0000356237,9.6428676754,0.59627198567));
#628=IFCCARTESIANPOINT((15.0000356237,10.7142962469,1.93875932375));
#629=IFCCARTESIANPOINT((15.0000356237,11.7857248183,1.93875932375));
#630=IFCCARTESIANPOINT((15.0000356237,12.8571533897,1.93875932375));
#631=IFCCARTESIANPOINT((15.0000356237,13.9285819612,0.59627198567));
#632=IFCCARTESIANPOINT((15.0000356237,15.0000105326,1.93875932375));
#633=IFCPOLYLINE((#634,#635));
#634=IFCCARTESIANPOINT((0.,0.));
#635=IFCCARTESIANPOINT((0.0001,0.));
#636=IFCORIENTEDEDGE(*,*,#637,.T.);
#637=IFCEDGECURVE(#638,#640,#642,.T.);
#638=IFCVERTEXPOINT(#639);
#639=IFCCARTESIANPOINT((15.0000356237,1.0532591771E-05,0.59627198567));
#640=IFCVERTEXPOINT(#641);
#641=IFCCARTESIANPOINT((15.0000356237,15.0000105326,1.93875932375));
#642=IFCSURFACECURVE(#643,(#659),.PCURVE_S1.);
#643=IFCBSPLINECURVEWITHKNOTS(9,(#644,#645,#646,#647,#648,#649,#650,#651,#652,#653,#654,#655,#656,#657,#658),.UNSPECIFIED.,.F.,.F.,(10,1,1,1,1,1,10),(0.,0.166666666666667,0.333333333333333,0.5,0.666666666666667,0.833333333333333,1.),.UNSPECIFIED.);
#644=IFCCARTESIANPOINT((15.0000356237,1.0532591771E-05,0.59627198567));
#645=IFCCARTESIANPOINT((15.0000356237,1.07143910402,1.93875932375));
#646=IFCCARTESIANPOINT((15.0000356237,2.14286767545,1.93875932375));
#647=IFCCARTESIANPOINT((15.0000356237,3.2142962469,1.93875932375));
#648=IFCCARTESIANPOINT((15.0000356237,4.2857248183,1.93875932375));
#649=IFCCARTESIANPOINT((15.0000356237,5.3571533897,1.93875932375));
#650=IFCCARTESIANPOINT((15.0000356237,6.4285819612,0.59627198567));
#651=IFCCARTESIANPOINT((15.0000356237,7.5000105326,1.93875932375));
#652=IFCCARTESIANPOINT((15.0000356237,8.571439104,1.93875932375));
#653=IFCCARTESIANPOINT((15.0000356237,9.6428676754,0.59627198567));
#654=IFCCARTESIANPOINT((15.0000356237,10.7142962469,1.93875932375));
#655=IFCCARTESIANPOINT((15.0000356237,11.7857248183,1.93875932375));
#656=IFCCARTESIANPOINT((15.0000356237,12.8571533897,1.93875932375));
#657=IFCCARTESIANPOINT((15.0000356237,13.9285819612,0.59627198567));
#658=IFCCARTESIANPOINT((15.0000356237,15.0000105326,1.93875932375));
#659=IFCPCURVE(#660,#886);
#660=IFCBSPLINESURFACEWITHKNOTS(9,9,((#661,#662,#663,#664,#665,#666,#667,#668,#669,#670,#671,#672,#673,#674,#675),(#676,#677,#678,#679,#680,#681,#682,#683,#684,#685,#686,#687,#688,#689,#690),(#691,#692,#693,#694,#695,#696,#697,#698,#699,#700,#701,#702,#703,#704,#705),(#706,#707,#708,#709,#710,#711,#712,#713,#714,#715,#716,#717,#718,#719,#720),(#721,#722,#723,#724,#725,#726,#727,#728,#729,#730,#731,#732,#733,#734,#735),(#736,#737,#738,#739,#740,#741,#742,#743,#744,#745,#746,#747,#748,#749,#750),(#751,#752,#753,#754,#755,#756,#757,#758,#759,#760,#761,#762,#763,#764,#765),(#766,#767,#768,#769,#770,#771,#772,#773,#774,#775,#776,#777,#778,#779,#780),(#781,#782,#783,#784,#785,#786,#787,#788,#789,#790,#791,#792,#793,#794,#795),(#796,#797,#798,#799,#800,#801,#802,#803,#804,#805,#806,#807,#808,#809,#810),(#811,#812,#813,#814,#815,#816,#817,#818,#819,#820,#821,#822,#823,#824,#825),(#826,#827,#828,#829,#830,#831,#832,#833,#834,#835,#836,#837,#838,#839,#840),(#841,#842,#843,#844,#845,#846,#847,#848,#849,#850,#851,#852,#853,#854,#855),(#856,#857,#858,#859,#860,#861,#862,#863,#864,#865,#866,#867,#868,#869,#870),(#871,#872,#873,#874,#875,#876,#877,#878,#879,#880,#881,#882,#883,#884,#885)),.UNSPECIFIED.,.F.,.F.,.F.,(10,1,1,1,1,1,10),(10,1,1,1,1,1,10),(0.,0.166666666666667,0.333333333333333,0.5,0.666666666666667,0.833333333333333,1.),(0.,0.166666666666667,0.333333333333333,0.5,0.666666666666667,0.833333333333333,1.),.UNSPECIFIED.);
#661=IFCCARTESIANPOINT((3.5623730998E-05,1.0532591771E-05,1.93875932375));
#662=IFCCARTESIANPOINT((3.5623730998E-05,1.07143910402,1.93875932375));
#663=IFCCARTESIANPOINT((3.5623730905E-05,2.14286767545,3.1754724911));
#664=IFCCARTESIANPOINT((3.5623730998E-05,3.2142962469,1.93875932375));
#665=IFCCARTESIANPOINT((3.5623730905E-05,4.2857248183,3.1754724911));
#666=IFCCARTESIANPOINT((3.5623730998E-05,5.3571533897,1.93875932375));
#667=IFCCARTESIANPOINT((3.5623730998E-05,6.4285819612,1.93875932375));
#668=IFCCARTESIANPOINT((3.5623730998E-05,7.5000105326,1.93875932375));
#669=IFCCARTESIANPOINT((3.5623730905E-05,8.571439104,3.1754724911));
#670=IFCCARTESIANPOINT((3.5623730998E-05,9.6428676754,1.93875932375));
#671=IFCCARTESIANPOINT((3.5623730998E-05,10.7142962469,1.93875932375));
#672=IFCCARTESIANPOINT((3.5623730905E-05,11.7857248183,3.1754724911));
#673=IFCCARTESIANPOINT((3.5623730998E-05,12.8571533897,1.93875932375));
#674=IFCCARTESIANPOINT((3.5623730998E-05,13.9285819612,1.93875932375));
#675=IFCCARTESIANPOINT((3.5623730998E-05,15.0000105326,1.93875932375));
#676=IFCCARTESIANPOINT((1.07146419516,1.0532591771E-05,1.93875932375));
#677=IFCCARTESIANPOINT((1.07146419516,1.07143910402,1.93875932375));
#678=IFCCARTESIANPOINT((1.07146419516,2.14286767545,1.93875932375));
#679=IFCCARTESIANPOINT((1.07146419516,3.2142962469,1.93875932375));
#680=IFCCARTESIANPOINT((1.07146419516,4.2857248183,1.93875932375));
#681=IFCCARTESIANPOINT((1.07146419516,5.3571533897,1.93875932375));
#682=IFCCARTESIANPOINT((1.07146419516,6.4285819612,1.93875932375));
#683=IFCCARTESIANPOINT((1.07146419516,7.5000105326,1.93875932375));
#684=IFCCARTESIANPOINT((1.07146419516,8.571439104,1.93875932375));
#685=IFCCARTESIANPOINT((1.07146419516,9.6428676754,1.93875932375));
#686=IFCCARTESIANPOINT((1.07146419516,10.7142962469,1.93875932375));
#687=IFCCARTESIANPOINT((1.07146419516,11.7857248183,1.93875932375));
#688=IFCCARTESIANPOINT((1.07146419516,12.8571533897,1.93875932375));
#689=IFCCARTESIANPOINT((1.07146419516,13.9285819612,1.93875932375));
#690=IFCCARTESIANPOINT((1.04385994151,14.9865837438,1.54886729649));
#691=IFCCARTESIANPOINT((2.14289276659,1.0532591771E-05,1.93875932375));
#692=IFCCARTESIANPOINT((2.14289276659,1.07143910402,1.93875932375));
#693=IFCCARTESIANPOINT((2.14289276659,2.14286767545,1.93875932375));
#694=IFCCARTESIANPOINT((2.14289276659,3.2142962469,1.93875932375));
#695=IFCCARTESIANPOINT((2.14289276659,4.2857248183,1.93875932375));
#696=IFCCARTESIANPOINT((2.14289276659,5.3571533897,1.93875932375));
#697=IFCCARTESIANPOINT((2.14289276659,6.4285819612,1.93875932375));
#698=IFCCARTESIANPOINT((2.14289276659,7.5000105326,1.93875932375));
#699=IFCCARTESIANPOINT((2.14289276659,8.571439104,1.93875932375));
#700=IFCCARTESIANPOINT((2.14289276659,9.6428676754,1.93875932375));
#701=IFCCARTESIANPOINT((2.14289276659,10.7142962469,1.93875932375));
#702=IFCCARTESIANPOINT((2.14289276659,11.7857248183,1.93875932375));
#703=IFCCARTESIANPOINT((2.14289276659,12.8571533897,5.1287882986));
#704=IFCCARTESIANPOINT((2.14289276659,13.9285819612,1.93875932375));
#705=IFCCARTESIANPOINT((2.14289276659,15.0000105326,1.74110678043));
#706=IFCCARTESIANPOINT((3.214321338,1.0532591771E-05,1.93875932375));
#707=IFCCARTESIANPOINT((3.214321338,1.07143910402,1.93875932375));
#708=IFCCARTESIANPOINT((3.214321338,2.14286767545,5.18065991E-05));
#709=IFCCARTESIANPOINT((3.214321338,3.2142962469,1.93875932375));
#710=IFCCARTESIANPOINT((3.214321338,4.2857248183,1.93875932375));
#711=IFCCARTESIANPOINT((3.214321338,5.3571533897,5.18065991E-05));
#712=IFCCARTESIANPOINT((3.214321338,6.4285819612,1.93875932375));
#713=IFCCARTESIANPOINT((3.214321338,7.5000105326,1.93875932375));
#714=IFCCARTESIANPOINT((3.214321338,8.571439104,1.93875932375));
#715=IFCCARTESIANPOINT((3.214321338,9.6428676754,1.93875932375));
#716=IFCCARTESIANPOINT((3.214321338,10.7142962469,1.93875932375));
#717=IFCCARTESIANPOINT((3.214321338,11.7857248183,1.93875932375));
#718=IFCCARTESIANPOINT((3.214321338,12.8571533897,5.1287882986));
#719=IFCCARTESIANPOINT((3.214321338,13.9285819612,1.93875932375));
#720=IFCCARTESIANPOINT((3.214321338,15.0000105326,1.93875932375));
#721=IFCCARTESIANPOINT((4.2857499094,1.0532591771E-05,1.93875932375));
#722=IFCCARTESIANPOINT((4.2857499094,1.07143910402,1.93875932375));
#723=IFCCARTESIANPOINT((4.2857499094,2.14286767545,1.93875932375));
#724=IFCCARTESIANPOINT((4.2857499094,3.2142962469,1.93875932375));
#725=IFCCARTESIANPOINT((4.2857499094,4.2857248183,1.93875932375));
#726=IFCCARTESIANPOINT((4.2857499094,5.3571533897,1.93875932375));
#727=IFCCARTESIANPOINT((4.2857499094,6.4285819612,1.93875932375));
#728=IFCCARTESIANPOINT((4.2857499094,7.5000105326,1.93875932375));
#729=IFCCARTESIANPOINT((4.2857499094,8.571439104,1.93875932375));
#730=IFCCARTESIANPOINT((4.2857499094,9.6428676754,1.93875932375));
#731=IFCCARTESIANPOINT((4.2857499094,10.7142962469,1.93875932375));
#732=IFCCARTESIANPOINT((4.2857499094,11.7857248183,1.93875932375));
#733=IFCCARTESIANPOINT((4.2857499094,12.8571533897,1.93875932375));
#734=IFCCARTESIANPOINT((4.2857499094,13.9285819612,1.93875932375));
#735=IFCCARTESIANPOINT((4.2857499094,15.0000105326,1.93875932375));
#736=IFCCARTESIANPOINT((5.3571784809,1.0532591771E-05,1.93875932375));
#737=IFCCARTESIANPOINT((5.3571784809,1.07143910402,1.93875932375));
#738=IFCCARTESIANPOINT((5.3571784809,2.14286767545,1.93875932375));
#739=IFCCARTESIANPOINT((5.3571784809,3.2142962469,1.93875932375));
#740=IFCCARTESIANPOINT((5.3571784809,4.2857248183,1.93875932375));
#741=IFCCARTESIANPOINT((5.3571784809,5.3571533897,1.93875932375));
#742=IFCCARTESIANPOINT((5.3571784809,6.4285819612,1.93875932375));
#743=IFCCARTESIANPOINT((5.3571784809,7.5000105326,1.93875932375));
#744=IFCCARTESIANPOINT((5.3571784809,8.571439104,1.93875932375));
#745=IFCCARTESIANPOINT((5.3571784809,9.6428676754,1.93875932375));
#746=IFCCARTESIANPOINT((5.3571784809,10.7142962469,5.1287882986));
#747=IFCCARTESIANPOINT((5.3571784809,11.7857248183,1.93875932375));
#748=IFCCARTESIANPOINT((5.3571784809,12.8571533897,1.93875932375));
#749=IFCCARTESIANPOINT((5.3571784809,13.9285819612,1.93875932375));
#750=IFCCARTESIANPOINT((5.3571784809,15.0000105326,1.93875932375));
#751=IFCCARTESIANPOINT((6.4286070523,1.0532591771E-05,1.93875932375));
#752=IFCCARTESIANPOINT((6.4286070523,1.07143910402,1.93875932375));
#753=IFCCARTESIANPOINT((6.4286070523,2.14286767545,1.93875932375));
#754=IFCCARTESIANPOINT((6.4286070523,3.2142962469,1.93875932375));
#755=IFCCARTESIANPOINT((6.4286070523,4.2857248183,1.93875932375));
#756=IFCCARTESIANPOINT((6.4286070523,5.3571533897,1.93875932375));
#757=IFCCARTESIANPOINT((6.4286070523,6.4285819612,5.18065991E-05));
#758=IFCCARTESIANPOINT((6.4286070523,7.5000105326,1.93875932375));
#759=IFCCARTESIANPOINT((6.4286070523,8.571439104,1.93875932375));
#760=IFCCARTESIANPOINT((6.4286070523,9.6428676754,1.93875932375));
#761=IFCCARTESIANPOINT((6.4286070523,10.7142962469,5.1287882986));
#762=IFCCARTESIANPOINT((6.4286070523,11.7857248183,1.93875932375));
#763=IFCCARTESIANPOINT((6.4286070523,12.8571533897,1.93875932375));
#764=IFCCARTESIANPOINT((6.4286070523,13.9285819612,1.93875932375));
#765=IFCCARTESIANPOINT((6.4286070523,15.0000105326,1.93875932375));
#766=IFCCARTESIANPOINT((7.5000356237,1.0532591771E-05,1.93875932375));
#767=IFCCARTESIANPOINT((7.5000356237,1.07143910402,1.93875932375));
#768=IFCCARTESIANPOINT((7.5000356237,2.14286767545,1.93875932375));
#769=IFCCARTESIANPOINT((7.5000356237,3.2142962469,1.93875932375));
#770=IFCCARTESIANPOINT((7.5000356237,4.2857248183,1.93875932375));
#771=IFCCARTESIANPOINT((7.5000356237,5.3571533897,1.93875932375));
#772=IFCCARTESIANPOINT((7.5000356237,6.4285819612,1.93875932375));
#773=IFCCARTESIANPOINT((7.5000356237,7.5000105326,1.93875932375));
#774=IFCCARTESIANPOINT((7.5000356237,8.571439104,1.93875932375));
#775=IFCCARTESIANPOINT((7.5000356237,9.6428676754,5.1287882986));
#776=IFCCARTESIANPOINT((7.5000356237,10.7142962469,1.93875932375));
#777=IFCCARTESIANPOINT((7.5000356237,11.7857248183,1.93875932375));
#778=IFCCARTESIANPOINT((7.5000356237,12.8571533897,1.93875932375));
#779=IFCCARTESIANPOINT((7.5000356237,13.9285819612,1.93875932375));
#780=IFCCARTESIANPOINT((7.5000356237,15.0000105326,1.93875932375));
#781=IFCCARTESIANPOINT((8.5714641952,1.0532591771E-05,1.93875932375));
#782=IFCCARTESIANPOINT((8.5714641952,1.07143910402,1.93875932375));
#783=IFCCARTESIANPOINT((8.5714641952,2.14286767545,1.93875932375));
#784=IFCCARTESIANPOINT((8.5714641952,3.2142962469,1.93875932375));
#785=IFCCARTESIANPOINT((8.5714641952,4.2857248183,1.93875932375));
#786=IFCCARTESIANPOINT((8.5714641952,5.3571533897,1.93875932375));
#787=IFCCARTESIANPOINT((8.5714641952,6.4285819612,1.93875932375));
#788=IFCCARTESIANPOINT((8.5714641952,7.5000105326,1.93875932375));
#789=IFCCARTESIANPOINT((8.5714641952,8.571439104,1.93875932375));
#790=IFCCARTESIANPOINT((8.5714641952,9.6428676754,1.93875932375));
#791=IFCCARTESIANPOINT((8.5714641952,10.7142962469,1.93875932375));
#792=IFCCARTESIANPOINT((8.5714641952,11.7857248183,1.93875932375));
#793=IFCCARTESIANPOINT((8.5714641952,12.8571533897,1.93875932375));
#794=IFCCARTESIANPOINT((8.5714641952,13.9285819612,1.93875932375));
#795=IFCCARTESIANPOINT((8.5714641952,15.0000105326,1.93875932375));
#796=IFCCARTESIANPOINT((9.6428927666,1.0532591771E-05,1.93875932375));
#797=IFCCARTESIANPOINT((9.6428927666,1.07143910402,1.93875932375));
#798=IFCCARTESIANPOINT((9.6428927666,2.14286767545,1.93875932375));
#799=IFCCARTESIANPOINT((9.6428927666,3.2142962469,1.93875932375));
#800=IFCCARTESIANPOINT((9.6428927666,4.2857248183,1.93875932375));
#801=IFCCARTESIANPOINT((9.6428927666,5.3571533897,1.93875932375));
#802=IFCCARTESIANPOINT((9.6428927666,6.4285819612,5.18065991E-05));
#803=IFCCARTESIANPOINT((9.6428927666,7.5000105326,1.93875932375));
#804=IFCCARTESIANPOINT((9.6428927666,8.571439104,1.93875932375));
#805=IFCCARTESIANPOINT((9.6428927666,9.6428676754,1.93875932375));
#806=IFCCARTESIANPOINT((9.6428927666,10.7142962469,1.93875932375));
#807=IFCCARTESIANPOINT((9.6428927666,11.7857248183,1.93875932375));
#808=IFCCARTESIANPOINT((9.6428927666,12.8571533897,1.93875932375));
#809=IFCCARTESIANPOINT((9.6428927666,13.9285819612,1.93875932375));
#810=IFCCARTESIANPOINT((10.0962936606,15.0376487171,1.35588611674));
#811=IFCCARTESIANPOINT((10.714321338,1.0532591771E-05,1.93875932375));
#812=IFCCARTESIANPOINT((10.714321338,1.07143910402,1.93875932375));
#813=IFCCARTESIANPOINT((10.714321338,2.14286767545,1.93875932375));
#814=IFCCARTESIANPOINT((10.714321338,3.2142962469,1.93875932375));
#815=IFCCARTESIANPOINT((10.714321338,4.2857248183,5.18065991E-05));
#816=IFCCARTESIANPOINT((10.714321338,5.3571533897,1.93875932375));
#817=IFCCARTESIANPOINT((10.714321338,6.4285819612,1.93875932375));
#818=IFCCARTESIANPOINT((10.714321338,7.5000105326,1.93875932375));
#819=IFCCARTESIANPOINT((10.714321338,8.571439104,5.18065991E-05));
#820=IFCCARTESIANPOINT((10.714321338,9.6428676754,1.93875932375));
#821=IFCCARTESIANPOINT((10.714321338,10.7142962469,1.93875932375));
#822=IFCCARTESIANPOINT((10.714321338,11.7857248183,1.93875932375));
#823=IFCCARTESIANPOINT((10.714321338,12.8571533897,1.93875932375));
#824=IFCCARTESIANPOINT((10.714321338,13.9285819612,1.93875932375));
#825=IFCCARTESIANPOINT((10.714321338,15.0000105326,1.93875932375));
#826=IFCCARTESIANPOINT((11.7857499094,1.0532591771E-05,1.93875932375));
#827=IFCCARTESIANPOINT((11.7857499094,1.07143910402,1.93875932375));
#828=IFCCARTESIANPOINT((11.7857499094,2.14286767545,1.93875932375));
#829=IFCCARTESIANPOINT((11.7857499094,3.2142962469,1.93875932375));
#830=IFCCARTESIANPOINT((11.7857499094,4.2857248183,1.93875932375));
#831=IFCCARTESIANPOINT((11.7857499094,5.3571533897,1.93875932375));
#832=IFCCARTESIANPOINT((11.7857499094,6.4285819612,1.93875932375));
#833=IFCCARTESIANPOINT((11.7857499094,7.5000105326,1.93875932375));
#834=IFCCARTESIANPOINT((11.7857499094,8.571439104,1.93875932375));
#835=IFCCARTESIANPOINT((11.7857499094,9.6428676754,1.93875932375));
#836=IFCCARTESIANPOINT((11.7857499094,10.7142962469,1.93875932375));
#837=IFCCARTESIANPOINT((11.7857499094,11.7857248183,1.93875932375));
#838=IFCCARTESIANPOINT((11.7857499094,12.8571533897,1.93875932375));
#839=IFCCARTESIANPOINT((11.7857499094,13.9285819612,1.93875932375));
#840=IFCCARTESIANPOINT((11.3978907176,14.8693563406,2.44049268426));
#841=IFCCARTESIANPOINT((12.8571784809,1.0532591771E-05,1.93875932375));
#842=IFCCARTESIANPOINT((12.8571784809,1.07143910402,1.93875932375));
#843=IFCCARTESIANPOINT((12.8571784809,2.14286767545,1.93875932375));
#844=IFCCARTESIANPOINT((12.8571784809,3.2142962469,1.93875932375));
#845=IFCCARTESIANPOINT((12.8571784809,4.2857248183,1.93875932375));
#846=IFCCARTESIANPOINT((12.8571784809,5.3571533897,1.93875932375));
#847=IFCCARTESIANPOINT((12.8571784809,6.4285819612,1.93875932375));
#848=IFCCARTESIANPOINT((12.8571784809,7.5000105326,5.18065991E-05));
#849=IFCCARTESIANPOINT((12.8571784809,8.571439104,1.93875932375));
#850=IFCCARTESIANPOINT((12.8571784809,9.6428676754,1.93875932375));
#851=IFCCARTESIANPOINT((12.8571784809,10.7142962469,1.93875932375));
#852=IFCCARTESIANPOINT((12.8571784809,11.7857248183,1.93875932375));
#853=IFCCARTESIANPOINT((12.8571784809,12.8571533897,1.93875932375));
#854=IFCCARTESIANPOINT((12.8571784809,13.9285819612,1.93875932375));
#855=IFCCARTESIANPOINT((12.8568370215,14.9497010766,2.56095173322));
#856=IFCCARTESIANPOINT((13.9286070523,1.0532591771E-05,1.93875932375));
#857=IFCCARTESIANPOINT((13.9286070523,1.07143910402,1.93875932375));
#858=IFCCARTESIANPOINT((13.9286070523,2.14286767545,1.93875932375));
#859=IFCCARTESIANPOINT((13.9286070523,3.2142962469,1.93875932375));
#860=IFCCARTESIANPOINT((13.9286070523,4.2857248183,1.93875932375));
#861=IFCCARTESIANPOINT((13.9286070523,5.3571533897,1.93875932375));
#862=IFCCARTESIANPOINT((13.9286070523,6.4285819612,1.93875932375));
#863=IFCCARTESIANPOINT((13.9286070523,7.5000105326,1.93875932375));
#864=IFCCARTESIANPOINT((13.9286070523,8.571439104,1.93875932375));
#865=IFCCARTESIANPOINT((13.9286070523,9.6428676754,1.93875932375));
#866=IFCCARTESIANPOINT((13.9286070523,10.7142962469,1.93875932375));
#867=IFCCARTESIANPOINT((13.9286070523,11.7857248183,1.93875932375));
#868=IFCCARTESIANPOINT((13.9286070523,12.8571533897,1.93875932375));
#869=IFCCARTESIANPOINT((13.9286070523,13.9285819612,1.93875932375));
#870=IFCCARTESIANPOINT((10.6452090386,11.7314780592,4.9981805335));
#871=IFCCARTESIANPOINT((15.0000356237,1.0532591771E-05,0.59627198567));
#872=IFCCARTESIANPOINT((15.0000356237,1.07143910402,1.93875932375));
#873=IFCCARTESIANPOINT((15.0000356237,2.14286767545,1.93875932375));
#874=IFCCARTESIANPOINT((15.0000356237,3.2142962469,1.93875932375));
#875=IFCCARTESIANPOINT((15.0000356237,4.2857248183,1.93875932375));
#876=IFCCARTESIANPOINT((15.0000356237,5.3571533897,1.93875932375));
#877=IFCCARTESIANPOINT((15.0000356237,6.4285819612,0.59627198567));
#878=IFCCARTESIANPOINT((15.0000356237,7.5000105326,1.93875932375));
#879=IFCCARTESIANPOINT((15.0000356237,8.571439104,1.93875932375));
#880=IFCCARTESIANPOINT((15.0000356237,9.6428676754,0.59627198567));
#881=IFCCARTESIANPOINT((15.0000356237,10.7142962469,1.93875932375));
#882=IFCCARTESIANPOINT((15.0000356237,11.7857248183,1.93875932375));
#883=IFCCARTESIANPOINT((15.0000356237,12.8571533897,1.93875932375));
#884=IFCCARTESIANPOINT((15.0000356237,13.9285819612,0.59627198567));
#885=IFCCARTESIANPOINT((15.0000356237,15.0000105326,1.93875932375));
#886=IFCPOLYLINE((#887,#888));
#887=IFCCARTESIANPOINT((0.0001,0.));
#888=IFCCARTESIANPOINT((0.0001,0.0001));
#889=IFCORIENTEDEDGE(*,*,#890,.F.);
#890=IFCEDGECURVE(#891,#893,#895,.T.);
#891=IFCVERTEXPOINT(#892);
#892=IFCCARTESIANPOINT((15.0000356237,15.0000105326,1.93875932375));
#893=IFCVERTEXPOINT(#894);
#894=IFCCARTESIANPOINT((3.5623730998E-05,15.0000105326,1.93875932375));
#895=IFCSURFACECURVE(#896,(#912),.PCURVE_S1.);
#896=IFCBSPLINECURVEWITHKNOTS(9,(#897,#898,#899,#900,#901,#902,#903,#904,#905,#906,#907,#908,#909,#910,#911),.UNSPECIFIED.,.F.,.F.,(10,1,1,1,1,1,10),(0.,0.166666666666667,0.333333333333333,0.5,0.666666666666667,0.833333333333333,1.),.UNSPECIFIED.);
#897=IFCCARTESIANPOINT((15.0000356237,15.0000105326,1.93875932375));
#898=IFCCARTESIANPOINT((10.6452090386,11.7314780592,4.9981805335));
#899=IFCCARTESIANPOINT((12.8568370215,14.9497010766,2.56095173322));
#900=IFCCARTESIANPOINT((11.3978907176,14.8693563406,2.44049268426));
#901=IFCCARTESIANPOINT((10.714321338,15.0000105326,1.93875932375));
#902=IFCCARTESIANPOINT((10.0962936606,15.0376487171,1.35588611674));
#903=IFCCARTESIANPOINT((8.5714641952,15.0000105326,1.93875932375));
#904=IFCCARTESIANPOINT((7.5000356237,15.0000105326,1.93875932375));
#905=IFCCARTESIANPOINT((6.4286070523,15.0000105326,1.93875932375));
#906=IFCCARTESIANPOINT((5.3571784809,15.0000105326,1.93875932375));
#907=IFCCARTESIANPOINT((4.2857499094,15.0000105326,1.93875932375));
#908=IFCCARTESIANPOINT((3.214321338,15.0000105326,1.93875932375));
#909=IFCCARTESIANPOINT((2.14289276659,15.0000105326,1.74110678043));
#910=IFCCARTESIANPOINT((1.04385994151,14.9865837438,1.54886729649));
#911=IFCCARTESIANPOINT((3.5623730998E-05,15.0000105326,1.93875932375));
#912=IFCPCURVE(#913,#1139);
#913=IFCBSPLINESURFACEWITHKNOTS(9,9,((#914,#915,#916,#917,#918,#919,#920,#921,#922,#923,#924,#925,#926,#927,#928),(#929,#930,#931,#932,#933,#934,#935,#936,#937,#938,#939,#940,#941,#942,#943),(#944,#945,#946,#947,#948,#949,#950,#951,#952,#953,#954,#955,#956,#957,#958),(#959,#960,#961,#962,#963,#964,#965,#966,#967,#968,#969,#970,#971,#972,#973),(#974,#975,#976,#977,#978,#979,#980,#981,#982,#983,#984,#985,#986,#987,#988),(#989,#990,#991,#992,#993,#994,#995,#996,#997,#998,#999,#1000,#1001,#1002,#1003),(#1004,#1005,#1006,#1007,#1008,#1009,#1010,#1011,#1012,#1013,#1014,#1015,#1016,#1017,#1018),(#1019,#1020,#1021,#1022,#1023,#1024,#1025,#1026,#1027,#1028,#1029,#1030,#1031,#1032,#1033),(#1034,#1035,#1036,#1037,#1038,#1039,#1040,#1041,#1042,#1043,#1044,#1045,#1046,#1047,#1048),(#1049,#1050,#1051,#1052,#1053,#1054,#1055,#1056,#1057,#1058,#1059,#1060,#1061,#1062,#1063),(#1064,#1065,#1066,#1067,#1068,#1069,#1070,#1071,#1072,#1073,#1074,#1075,#1076,#1077,#1078),(#1079,#1080,#1081,#1082,#1083,#1084,#1085,#1086,#1087,#1088,#1089,#1090,#1091,#1092,#1093),(#1094,#1095,#1096,#1097,#1098,#1099,#1100,#1101,#1102,#1103,#1104,#1105,#1106,#1107,#1108),(#1109,#1110,#1111,#1112,#1113,#1114,#1115,#1116,#1117,#1118,#1119,#1120,#1121,#1122,#1123),(#1124,#1125,#1126,#1127,#1128,#1129,#1130,#1131,#1132,#1133,#1134,#1135,#1136,#1137,#1138)),.UNSPECIFIED.,.F.,.F.,.F.,(10,1,1,1,1,1,10),(10,1,1,1,1,1,10),(0.,0.166666666666667,0.333333333333333,0.5,0.666666666666667,0.833333333333333,1.),(0.,0.166666666666667,0.333333333333333,0.5,0.666666666666667,0.833333333333333,1.),.UNSPECIFIED.);
#914=IFCCARTESIANPOINT((3.5623730998E-05,1.0532591771E-05,1.93875932375));
#915=IFCCARTESIANPOINT((3.5623730998E-05,1.07143910402,1.93875932375));
#916=IFCCARTESIANPOINT((3.5623730905E-05,2.14286767545,3.1754724911));
#917=IFCCARTESIANPOINT((3.5623730998E-05,3.2142962469,1.93875932375));
#918=IFCCARTESIANPOINT((3.5623730905E-05,4.2857248183,3.1754724911));
#919=IFCCARTESIANPOINT((3.5623730998E-05,5.3571533897,1.93875932375));
#920=IFCCARTESIANPOINT((3.5623730998E-05,6.4285819612,1.93875932375));
#921=IFCCARTESIANPOINT((3.5623730998E-05,7.5000105326,1.93875932375));
#922=IFCCARTESIANPOINT((3.5623730905E-05,8.571439104,3.1754724911));
#923=IFCCARTESIANPOINT((3.5623730998E-05,9.6428676754,1.93875932375));
#924=IFCCARTESIANPOINT((3.5623730998E-05,10.7142962469,1.93875932375));
#925=IFCCARTESIANPOINT((3.5623730905E-05,11.7857248183,3.1754724911));
#926=IFCCARTESIANPOINT((3.5623730998E-05,12.8571533897,1.93875932375));
#927=IFCCARTESIANPOINT((3.5623730998E-05,13.9285819612,1.93875932375));
#928=IFCCARTESIANPOINT((3.5623730998E-05,15.0000105326,1.93875932375));
#929=IFCCARTESIANPOINT((1.07146419516,1.0532591771E-05,1.93875932375));
#930=IFCCARTESIANPOINT((1.07146419516,1.07143910402,1.93875932375));
#931=IFCCARTESIANPOINT((1.07146419516,2.14286767545,1.93875932375));
#932=IFCCARTESIANPOINT((1.07146419516,3.2142962469,1.93875932375));
#933=IFCCARTESIANPOINT((1.07146419516,4.2857248183,1.93875932375));
#934=IFCCARTESIANPOINT((1.07146419516,5.3571533897,1.93875932375));
#935=IFCCARTESIANPOINT((1.07146419516,6.4285819612,1.93875932375));
#936=IFCCARTESIANPOINT((1.07146419516,7.5000105326,1.93875932375));
#937=IFCCARTESIANPOINT((1.07146419516,8.571439104,1.93875932375));
#938=IFCCARTESIANPOINT((1.07146419516,9.6428676754,1.93875932375));
#939=IFCCARTESIANPOINT((1.07146419516,10.7142962469,1.93875932375));
#940=IFCCARTESIANPOINT((1.07146419516,11.7857248183,1.93875932375));
#941=IFCCARTESIANPOINT((1.07146419516,12.8571533897,1.93875932375));
#942=IFCCARTESIANPOINT((1.07146419516,13.9285819612,1.93875932375));
#943=IFCCARTESIANPOINT((1.04385994151,14.9865837438,1.54886729649));
#944=IFCCARTESIANPOINT((2.14289276659,1.0532591771E-05,1.93875932375));
#945=IFCCARTESIANPOINT((2.14289276659,1.07143910402,1.93875932375));
#946=IFCCARTESIANPOINT((2.14289276659,2.14286767545,1.93875932375));
#947=IFCCARTESIANPOINT((2.14289276659,3.2142962469,1.93875932375));
#948=IFCCARTESIANPOINT((2.14289276659,4.2857248183,1.93875932375));
#949=IFCCARTESIANPOINT((2.14289276659,5.3571533897,1.93875932375));
#950=IFCCARTESIANPOINT((2.14289276659,6.4285819612,1.93875932375));
#951=IFCCARTESIANPOINT((2.14289276659,7.5000105326,1.93875932375));
#952=IFCCARTESIANPOINT((2.14289276659,8.571439104,1.93875932375));
#953=IFCCARTESIANPOINT((2.14289276659,9.6428676754,1.93875932375));
#954=IFCCARTESIANPOINT((2.14289276659,10.7142962469,1.93875932375));
#955=IFCCARTESIANPOINT((2.14289276659,11.7857248183,1.93875932375));
#956=IFCCARTESIANPOINT((2.14289276659,12.8571533897,5.1287882986));
#957=IFCCARTESIANPOINT((2.14289276659,13.9285819612,1.93875932375));
#958=IFCCARTESIANPOINT((2.14289276659,15.0000105326,1.74110678043));
#959=IFCCARTESIANPOINT((3.214321338,1.0532591771E-05,1.93875932375));
#960=IFCCARTESIANPOINT((3.214321338,1.07143910402,1.93875932375));
#961=IFCCARTESIANPOINT((3.214321338,2.14286767545,5.18065991E-05));
#962=IFCCARTESIANPOINT((3.214321338,3.2142962469,1.93875932375));
#963=IFCCARTESIANPOINT((3.214321338,4.2857248183,1.93875932375));
#964=IFCCARTESIANPOINT((3.214321338,5.3571533897,5.18065991E-05));
#965=IFCCARTESIANPOINT((3.214321338,6.4285819612,1.93875932375));
#966=IFCCARTESIANPOINT((3.214321338,7.5000105326,1.93875932375));
#967=IFCCARTESIANPOINT((3.214321338,8.571439104,1.93875932375));
#968=IFCCARTESIANPOINT((3.214321338,9.6428676754,1.93875932375));
#969=IFCCARTESIANPOINT((3.214321338,10.7142962469,1.93875932375));
#970=IFCCARTESIANPOINT((3.214321338,11.7857248183,1.93875932375));
#971=IFCCARTESIANPOINT((3.214321338,12.8571533897,5.1287882986));
#972=IFCCARTESIANPOINT((3.214321338,13.9285819612,1.93875932375));
#973=IFCCARTESIANPOINT((3.214321338,15.0000105326,1.93875932375));
#974=IFCCARTESIANPOINT((4.2857499094,1.0532591771E-05,1.93875932375));
#975=IFCCARTESIANPOINT((4.2857499094,1.07143910402,1.93875932375));
#976=IFCCARTESIANPOINT((4.2857499094,2.14286767545,1.93875932375));
#977=IFCCARTESIANPOINT((4.2857499094,3.2142962469,1.93875932375));
#978=IFCCARTESIANPOINT((4.2857499094,4.2857248183,1.93875932375));
#979=IFCCARTESIANPOINT((4.2857499094,5.3571533897,1.93875932375));
#980=IFCCARTESIANPOINT((4.2857499094,6.4285819612,1.93875932375));
#981=IFCCARTESIANPOINT((4.2857499094,7.5000105326,1.93875932375));
#982=IFCCARTESIANPOINT((4.2857499094,8.571439104,1.93875932375));
#983=IFCCARTESIANPOINT((4.2857499094,9.6428676754,1.93875932375));
#984=IFCCARTESIANPOINT((4.2857499094,10.7142962469,1.93875932375));
#985=IFCCARTESIANPOINT((4.2857499094,11.7857248183,1.93875932375));
#986=IFCCARTESIANPOINT((4.2857499094,12.8571533897,1.93875932375));
#987=IFCCARTESIANPOINT((4.2857499094,13.9285819612,1.93875932375));
#988=IFCCARTESIANPOINT((4.2857499094,15.0000105326,1.93875932375));
#989=IFCCARTESIANPOINT((5.3571784809,1.0532591771E-05,1.93875932375));
#990=IFCCARTESIANPOINT((5.3571784809,1.07143910402,1.93875932375));
#991=IFCCARTESIANPOINT((5.3571784809,2.14286767545,1.93875932375));
#992=IFCCARTESIANPOINT((5.3571784809,3.2142962469,1.93875932375));
#993=IFCCARTESIANPOINT((5.3571784809,4.2857248183,1.93875932375));
#994=IFCCARTESIANPOINT((5.3571784809,5.3571533897,1.93875932375));
#995=IFCCARTESIANPOINT((5.3571784809,6.4285819612,1.93875932375));
#996=IFCCARTESIANPOINT((5.3571784809,7.5000105326,1.93875932375));
#997=IFCCARTESIANPOINT((5.3571784809,8.571439104,1.93875932375));
#998=IFCCARTESIANPOINT((5.3571784809,9.6428676754,1.93875932375));
#999=IFCCARTESIANPOINT((5.3571784809,10.7142962469,5.1287882986));
#1000=IFCCARTESIANPOINT((5.3571784809,11.7857248183,1.93875932375));
#1001=IFCCARTESIANPOINT((5.3571784809,12.8571533897,1.93875932375));
#1002=IFCCARTESIANPOINT((5.3571784809,13.9285819612,1.93875932375));
#1003=IFCCARTESIANPOINT((5.3571784809,15.0000105326,1.93875932375));
#1004=IFCCARTESIANPOINT((6.4286070523,1.0532591771E-05,1.93875932375));
#1005=IFCCARTESIANPOINT((6.4286070523,1.07143910402,1.93875932375));
#1006=IFCCARTESIANPOINT((6.4286070523,2.14286767545,1.93875932375));
#1007=IFCCARTESIANPOINT((6.4286070523,3.2142962469,1.93875932375));
#1008=IFCCARTESIANPOINT((6.4286070523,4.2857248183,1.93875932375));
#1009=IFCCARTESIANPOINT((6.4286070523,5.3571533897,1.93875932375));
#1010=IFCCARTESIANPOINT((6.4286070523,6.4285819612,5.18065991E-05));
#1011=IFCCARTESIANPOINT((6.4286070523,7.5000105326,1.93875932375));
#1012=IFCCARTESIANPOINT((6.4286070523,8.571439104,1.93875932375));
#1013=IFCCARTESIANPOINT((6.4286070523,9.6428676754,1.93875932375));
#1014=IFCCARTESIANPOINT((6.4286070523,10.7142962469,5.1287882986));
#1015=IFCCARTESIANPOINT((6.4286070523,11.7857248183,1.93875932375));
#1016=IFCCARTESIANPOINT((6.4286070523,12.8571533897,1.93875932375));
#1017=IFCCARTESIANPOINT((6.4286070523,13.9285819612,1.93875932375));
#1018=IFCCARTESIANPOINT((6.4286070523,15.0000105326,1.93875932375));
#1019=IFCCARTESIANPOINT((7.5000356237,1.0532591771E-05,1.93875932375));
#1020=IFCCARTESIANPOINT((7.5000356237,1.07143910402,1.93875932375));
#1021=IFCCARTESIANPOINT((7.5000356237,2.14286767545,1.93875932375));
#1022=IFCCARTESIANPOINT((7.5000356237,3.2142962469,1.93875932375));
#1023=IFCCARTESIANPOINT((7.5000356237,4.2857248183,1.93875932375));
#1024=IFCCARTESIANPOINT((7.5000356237,5.3571533897,1.93875932375));
#1025=IFCCARTESIANPOINT((7.5000356237,6.4285819612,1.93875932375));
#1026=IFCCARTESIANPOINT((7.5000356237,7.5000105326,1.93875932375));
#1027=IFCCARTESIANPOINT((7.5000356237,8.571439104,1.93875932375));
#1028=IFCCARTESIANPOINT((7.5000356237,9.6428676754,5.1287882986));
#1029=IFCCARTESIANPOINT((7.5000356237,10.7142962469,1.93875932375));
#1030=IFCCARTESIANPOINT((7.5000356237,11.7857248183,1.93875932375));
#1031=IFCCARTESIANPOINT((7.5000356237,12.8571533897,1.93875932375));
#1032=IFCCARTESIANPOINT((7.5000356237,13.9285819612,1.93875932375));
#1033=IFCCARTESIANPOINT((7.5000356237,15.0000105326,1.93875932375));
#1034=IFCCARTESIANPOINT((8.5714641952,1.0532591771E-05,1.93875932375));
#1035=IFCCARTESIANPOINT((8.5714641952,1.07143910402,1.93875932375));
#1036=IFCCARTESIANPOINT((8.5714641952,2.14286767545,1.93875932375));
#1037=IFCCARTESIANPOINT((8.5714641952,3.2142962469,1.93875932375));
#1038=IFCCARTESIANPOINT((8.5714641952,4.2857248183,1.93875932375));
#1039=IFCCARTESIANPOINT((8.5714641952,5.3571533897,1.93875932375));
#1040=IFCCARTESIANPOINT((8.5714641952,6.4285819612,1.93875932375));
#1041=IFCCARTESIANPOINT((8.5714641952,7.5000105326,1.93875932375));
#1042=IFCCARTESIANPOINT((8.5714641952,8.571439104,1.93875932375));
#1043=IFCCARTESIANPOINT((8.5714641952,9.6428676754,1.93875932375));
#1044=IFCCARTESIANPOINT((8.5714641952,10.7142962469,1.93875932375));
#1045=IFCCARTESIANPOINT((8.5714641952,11.7857248183,1.93875932375));
#1046=IFCCARTESIANPOINT((8.5714641952,12.8571533897,1.93875932375));
#1047=IFCCARTESIANPOINT((8.5714641952,13.9285819612,1.93875932375));
#1048=IFCCARTESIANPOINT((8.5714641952,15.0000105326,1.93875932375));
#1049=IFCCARTESIANPOINT((9.6428927666,1.0532591771E-05,1.93875932375));
#1050=IFCCARTESIANPOINT((9.6428927666,1.07143910402,1.93875932375));
#1051=IFCCARTESIANPOINT((9.6428927666,2.14286767545,1.93875932375));
#1052=IFCCARTESIANPOINT((9.6428927666,3.2142962469,1.93875932375));
#1053=IFCCARTESIANPOINT((9.6428927666,4.2857248183,1.93875932375));
#1054=IFCCARTESIANPOINT((9.6428927666,5.3571533897,1.93875932375));
#1055=IFCCARTESIANPOINT((9.6428927666,6.4285819612,5.18065991E-05));
#1056=IFCCARTESIANPOINT((9.6428927666,7.5000105326,1.93875932375));
#1057=IFCCARTESIANPOINT((9.6428927666,8.571439104,1.93875932375));
#1058=IFCCARTESIANPOINT((9.6428927666,9.6428676754,1.93875932375));
#1059=IFCCARTESIANPOINT((9.6428927666,10.7142962469,1.93875932375));
#1060=IFCCARTESIANPOINT((9.6428927666,11.7857248183,1.93875932375));
#1061=IFCCARTESIANPOINT((9.6428927666,12.8571533897,1.93875932375));
#1062=IFCCARTESIANPOINT((9.6428927666,13.9285819612,1.93875932375));
#1063=IFCCARTESIANPOINT((10.0962936606,15.0376487171,1.35588611674));
#1064=IFCCARTESIANPOINT((10.714321338,1.0532591771E-05,1.93875932375));
#1065=IFCCARTESIANPOINT((10.714321338,1.07143910402,1.93875932375));
#1066=IFCCARTESIANPOINT((10.714321338,2.14286767545,1.93875932375));
#1067=IFCCARTESIANPOINT((10.714321338,3.2142962469,1.93875932375));
#1068=IFCCARTESIANPOINT((10.714321338,4.2857248183,5.18065991E-05));
#1069=IFCCARTESIANPOINT((10.714321338,5.3571533897,1.93875932375));
#1070=IFCCARTESIANPOINT((10.714321338,6.4285819612,1.93875932375));
#1071=IFCCARTESIANPOINT((10.714321338,7.5000105326,1.93875932375));
#1072=IFCCARTESIANPOINT((10.714321338,8.571439104,5.18065991E-05));
#1073=IFCCARTESIANPOINT((10.714321338,9.6428676754,1.93875932375));
#1074=IFCCARTESIANPOINT((10.714321338,10.7142962469,1.93875932375));
#1075=IFCCARTESIANPOINT((10.714321338,11.7857248183,1.93875932375));
#1076=IFCCARTESIANPOINT((10.714321338,12.8571533897,1.93875932375));
#1077=IFCCARTESIANPOINT((10.714321338,13.9285819612,1.93875932375));
#1078=IFCCARTESIANPOINT((10.714321338,15.0000105326,1.93875932375));
#1079=IFCCARTESIANPOINT((11.7857499094,1.0532591771E-05,1.93875932375));
#1080=IFCCARTESIANPOINT((11.7857499094,1.07143910402,1.93875932375));
#1081=IFCCARTESIANPOINT((11.7857499094,2.14286767545,1.93875932375));
#1082=IFCCARTESIANPOINT((11.7857499094,3.2142962469,1.93875932375));
#1083=IFCCARTESIANPOINT((11.7857499094,4.2857248183,1.93875932375));
#1084=IFCCARTESIANPOINT((11.7857499094,5.3571533897,1.93875932375));
#1085=IFCCARTESIANPOINT((11.7857499094,6.4285819612,1.93875932375));
#1086=IFCCARTESIANPOINT((11.7857499094,7.5000105326,1.93875932375));
#1087=IFCCARTESIANPOINT((11.7857499094,8.571439104,1.93875932375));
#1088=IFCCARTESIANPOINT((11.7857499094,9.6428676754,1.93875932375));
#1089=IFCCARTESIANPOINT((11.7857499094,10.7142962469,1.93875932375));
#1090=IFCCARTESIANPOINT((11.7857499094,11.7857248183,1.93875932375));
#1091=IFCCARTESIANPOINT((11.7857499094,12.8571533897,1.93875932375));
#1092=IFCCARTESIANPOINT((11.7857499094,13.9285819612,1.93875932375));
#1093=IFCCARTESIANPOINT((11.3978907176,14.8693563406,2.44049268426));
#1094=IFCCARTESIANPOINT((12.8571784809,1.0532591771E-05,1.93875932375));
#1095=IFCCARTESIANPOINT((12.8571784809,1.07143910402,1.93875932375));
#1096=IFCCARTESIANPOINT((12.8571784809,2.14286767545,1.93875932375));
#1097=IFCCARTESIANPOINT((12.8571784809,3.2142962469,1.93875932375));
#1098=IFCCARTESIANPOINT((12.8571784809,4.2857248183,1.93875932375));
#1099=IFCCARTESIANPOINT((12.8571784809,5.3571533897,1.93875932375));
#1100=IFCCARTESIANPOINT((12.8571784809,6.4285819612,1.93875932375));
#1101=IFCCARTESIANPOINT((12.8571784809,7.5000105326,5.18065991E-05));
#1102=IFCCARTESIANPOINT((12.8571784809,8.571439104,1.93875932375));
#1103=IFCCARTESIANPOINT((12.8571784809,9.6428676754,1.93875932375));
#1104=IFCCARTESIANPOINT((12.8571784809,10.7142962469,1.93875932375));
#1105=IFCCARTESIANPOINT((12.8571784809,11.7857248183,1.93875932375));
#1106=IFCCARTESIANPOINT((12.8571784809,12.8571533897,1.93875932375));
#1107=IFCCARTESIANPOINT((12.8571784809,13.9285819612,1.93875932375));
#1108=IFCCARTESIANPOINT((12.8568370215,14.9497010766,2.56095173322));
#1109=IFCCARTESIANPOINT((13.9286070523,1.0532591771E-05,1.93875932375));
#1110=IFCCARTESIANPOINT((13.9286070523,1.07143910402,1.93875932375));
#1111=IFCCARTESIANPOINT((13.9286070523,2.14286767545,1.93875932375));
#1112=IFCCARTESIANPOINT((13.9286070523,3.2142962469,1.93875932375));
#1113=IFCCARTESIANPOINT((13.9286070523,4.2857248183,1.93875932375));
#1114=IFCCARTESIANPOINT((13.9286070523,5.3571533897,1.93875932375));
#1115=IFCCARTESIANPOINT((13.9286070523,6.4285819612,1.93875932375));
#1116=IFCCARTESIANPOINT((13.9286070523,7.5000105326,1.93875932375));
#1117=IFCCARTESIANPOINT((13.9286070523,8.571439104,1.93875932375));
#1118=IFCCARTESIANPOINT((13.9286070523,9.6428676754,1.93875932375));
#1119=IFCCARTESIANPOINT((13.9286070523,10.7142962469,1.93875932375));
#1120=IFCCARTESIANPOINT((13.9286070523,11.7857248183,1.93875932375));
#1121=IFCCARTESIANPOINT((13.9286070523,12.8571533897,1.93875932375));
#1122=IFCCARTESIANPOINT((13.9286070523,13.9285819612,1.93875932375));
#1123=IFCCARTESIANPOINT((10.6452090386,11.7314780592,4.9981805335));
#1124=IFCCARTESIANPOINT((15.0000356237,1.0532591771E-05,0.59627198567));
#1125=IFCCARTESIANPOINT((15.0000356237,1.07143910402,1.93875932375));
#1126=IFCCARTESIANPOINT((15.0000356237,2.14286767545,1.93875932375));
#1127=IFCCARTESIANPOINT((15.0000356237,3.2142962469,1.93875932375));
#1128=IFCCARTESIANPOINT((15.0000356237,4.2857248183,1.93875932375));
#1129=IFCCARTESIANPOINT((15.0000356237,5.3571533897,1.93875932375));
#1130=IFCCARTESIANPOINT((15.0000356237,6.4285819612,0.59627198567));
#1131=IFCCARTESIANPOINT((15.0000356237,7.5000105326,1.93875932375));
#1132=IFCCARTESIANPOINT((15.0000356237,8.571439104,1.93875932375));
#1133=IFCCARTESIANPOINT((15.0000356237,9.6428676754,0.59627198567));
#1134=IFCCARTESIANPOINT((15.0000356237,10.7142962469,1.93875932375));
#1135=IFCCARTESIANPOINT((15.0000356237,11.7857248183,1.93875932375));
#1136=IFCCARTESIANPOINT((15.0000356237,12.8571533897,1.93875932375));
#1137=IFCCARTESIANPOINT((15.0000356237,13.9285819612,0.59627198567));
#1138=IFCCARTESIANPOINT((15.0000356237,15.0000105326,1.93875932375));
#1139=IFCPOLYLINE((#1140,#1141));
#1140=IFCCARTESIANPOINT((0.0001,0.0001));
#1141=IFCCARTESIANPOINT((0.,0.0001));
#1142=IFCORIENTEDEDGE(*,*,#1143,.F.);
#1143=IFCEDGECURVE(#1144,#1146,#1148,.T.);
#1144=IFCVERTEXPOINT(#1145);
#1145=IFCCARTESIANPOINT((3.5623730998E-05,15.0000105326,1.93875932375));
#1146=IFCVERTEXPOINT(#1147);
#1147=IFCCARTESIANPOINT((3.5623730998E-05,1.0532591771E-05,1.93875932375));
#1148=IFCSURFACECURVE(#1149,(#1165),.PCURVE_S1.);
#1149=IFCBSPLINECURVEWITHKNOTS(9,(#1150,#1151,#1152,#1153,#1154,#1155,#1156,#1157,#1158,#1159,#1160,#1161,#1162,#1163,#1164),.UNSPECIFIED.,.F.,.F.,(10,1,1,1,1,1,10),(0.,0.166666666666667,0.333333333333333,0.5,0.666666666666667,0.833333333333333,1.),.UNSPECIFIED.);
#1150=IFCCARTESIANPOINT((3.5623730998E-05,15.0000105326,1.93875932375));
#1151=IFCCARTESIANPOINT((3.5623730998E-05,13.9285819612,1.93875932375));
#1152=IFCCARTESIANPOINT((3.5623730998E-05,12.8571533897,1.93875932375));
#1153=IFCCARTESIANPOINT((3.5623730905E-05,11.7857248183,3.1754724911));
#1154=IFCCARTESIANPOINT((3.5623730998E-05,10.7142962469,1.93875932375));
#1155=IFCCARTESIANPOINT((3.5623730998E-05,9.6428676754,1.93875932375));
#1156=IFCCARTESIANPOINT((3.5623730905E-05,8.571439104,3.1754724911));
#1157=IFCCARTESIANPOINT((3.5623730998E-05,7.5000105326,1.93875932375));
#1158=IFCCARTESIANPOINT((3.5623730998E-05,6.4285819612,1.93875932375));
#1159=IFCCARTESIANPOINT((3.5623730998E-05,5.3571533897,1.93875932375));
#1160=IFCCARTESIANPOINT((3.5623730905E-05,4.2857248183,3.1754724911));
#1161=IFCCARTESIANPOINT((3.5623730998E-05,3.2142962469,1.93875932375));
#1162=IFCCARTESIANPOINT((3.5623730905E-05,2.14286767545,3.1754724911));
#1163=IFCCARTESIANPOINT((3.5623730998E-05,1.07143910402,1.93875932375));
#1164=IFCCARTESIANPOINT((3.5623730998E-05,1.0532591771E-05,1.93875932375));
#1165=IFCPCURVE(#1166,#1392);
#1166=IFCBSPLINESURFACEWITHKNOTS(9,9,((#1167,#1168,#1169,#1170,#1171,#1172,#1173,#1174,#1175,#1176,#1177,#1178,#1179,#1180,#1181),(#1182,#1183,#1184,#1185,#1186,#1187,#1188,#1189,#1190,#1191,#1192,#1193,#1194,#1195,#1196),(#1197,#1198,#1199,#1200,#1201,#1202,#1203,#1204,#1205,#1206,#1207,#1208,#1209,#1210,#1211),(#1212,#1213,#1214,#1215,#1216,#1217,#1218,#1219,#1220,#1221,#1222,#1223,#1224,#1225,#1226),(#1227,#1228,#1229,#1230,#1231,#1232,#1233,#1234,#1235,#1236,#1237,#1238,#1239,#1240,#1241),(#1242,#1243,#1244,#1245,#1246,#1247,#1248,#1249,#1250,#1251,#1252,#1253,#1254,#1255,#1256),(#1257,#1258,#1259,#1260,#1261,#1262,#1263,#1264,#1265,#1266,#1267,#1268,#1269,#1270,#1271),(#1272,#1273,#1274,#1275,#1276,#1277,#1278,#1279,#1280,#1281,#1282,#1283,#1284,#1285,#1286),(#1287,#1288,#1289,#1290,#1291,#1292,#1293,#1294,#1295,#1296,#1297,#1298,#1299,#1300,#1301),(#1302,#1303,#1304,#1305,#1306,#1307,#1308,#1309,#1310,#1311,#1312,#1313,#1314,#1315,#1316),(#1317,#1318,#1319,#1320,#1321,#1322,#1323,#1324,#1325,#1326,#1327,#1328,#1329,#1330,#1331),(#1332,#1333,#1334,#1335,#1336,#1337,#1338,#1339,#1340,#1341,#1342,#1343,#1344,#1345,#1346),(#1347,#1348,#1349,#1350,#1351,#1352,#1353,#1354,#1355,#1356,#1357,#1358,#1359,#1360,#1361),(#1362,#1363,#1364,#1365,#1366,#1367,#1368,#1369,#1370,#1371,#1372,#1373,#1374,#1375,#1376),(#1377,#1378,#1379,#1380,#1381,#1382,#1383,#1384,#1385,#1386,#1387,#1388,#1389,#1390,#1391)),.UNSPECIFIED.,.F.,.F.,.F.,(10,1,1,1,1,1,10),(10,1,1,1,1,1,10),(0.,0.166666666666667,0.333333333333333,0.5,0.666666666666667,0.833333333333333,1.),(0.,0.166666666666667,0.333333333333333,0.5,0.666666666666667,0.833333333333333,1.),.UNSPECIFIED.);
#1167=IFCCARTESIANPOINT((3.5623730998E-05,1.0532591771E-05,1.93875932375));
#1168=IFCCARTESIANPOINT((3.5623730998E-05,1.07143910402,1.93875932375));
#1169=IFCCARTESIANPOINT((3.5623730905E-05,2.14286767545,3.1754724911));
#1170=IFCCARTESIANPOINT((3.5623730998E-05,3.2142962469,1.93875932375));
#1171=IFCCARTESIANPOINT((3.5623730905E-05,4.2857248183,3.1754724911));
#1172=IFCCARTESIANPOINT((3.5623730998E-05,5.3571533897,1.93875932375));
#1173=IFCCARTESIANPOINT((3.5623730998E-05,6.4285819612,1.93875932375));
#1174=IFCCARTESIANPOINT((3.5623730998E-05,7.5000105326,1.93875932375));
#1175=IFCCARTESIANPOINT((3.5623730905E-05,8.571439104,3.1754724911));
#1176=IFCCARTESIANPOINT((3.5623730998E-05,9.6428676754,1.93875932375));
#1177=IFCCARTESIANPOINT((3.5623730998E-05,10.7142962469,1.93875932375));
#1178=IFCCARTESIANPOINT((3.5623730905E-05,11.7857248183,3.1754724911));
#1179=IFCCARTESIANPOINT((3.5623730998E-05,12.8571533897,1.93875932375));
#1180=IFCCARTESIANPOINT((3.5623730998E-05,13.9285819612,1.93875932375));
#1181=IFCCARTESIANPOINT((3.5623730998E-05,15.0000105326,1.93875932375));
#1182=IFCCARTESIANPOINT((1.07146419516,1.0532591771E-05,1.93875932375));
#1183=IFCCARTESIANPOINT((1.07146419516,1.07143910402,1.93875932375));
#1184=IFCCARTESIANPOINT((1.07146419516,2.14286767545,1.93875932375));
#1185=IFCCARTESIANPOINT((1.07146419516,3.2142962469,1.93875932375));
#1186=IFCCARTESIANPOINT((1.07146419516,4.2857248183,1.93875932375));
#1187=IFCCARTESIANPOINT((1.07146419516,5.3571533897,1.93875932375));
#1188=IFCCARTESIANPOINT((1.07146419516,6.4285819612,1.93875932375));
#1189=IFCCARTESIANPOINT((1.07146419516,7.5000105326,1.93875932375));
#1190=IFCCARTESIANPOINT((1.07146419516,8.571439104,1.93875932375));
#1191=IFCCARTESIANPOINT((1.07146419516,9.6428676754,1.93875932375));
#1192=IFCCARTESIANPOINT((1.07146419516,10.7142962469,1.93875932375));
#1193=IFCCARTESIANPOINT((1.07146419516,11.7857248183,1.93875932375));
#1194=IFCCARTESIANPOINT((1.07146419516,12.8571533897,1.93875932375));
#1195=IFCCARTESIANPOINT((1.07146419516,13.9285819612,1.93875932375));
#1196=IFCCARTESIANPOINT((1.04385994151,14.9865837438,1.54886729649));
#1197=IFCCARTESIANPOINT((2.14289276659,1.0532591771E-05,1.93875932375));
#1198=IFCCARTESIANPOINT((2.14289276659,1.07143910402,1.93875932375));
#1199=IFCCARTESIANPOINT((2.14289276659,2.14286767545,1.93875932375));
#1200=IFCCARTESIANPOINT((2.14289276659,3.2142962469,1.93875932375));
#1201=IFCCARTESIANPOINT((2.14289276659,4.2857248183,1.93875932375));
#1202=IFCCARTESIANPOINT((2.14289276659,5.3571533897,1.93875932375));
#1203=IFCCARTESIANPOINT((2.14289276659,6.4285819612,1.93875932375));
#1204=IFCCARTESIANPOINT((2.14289276659,7.5000105326,1.93875932375));
#1205=IFCCARTESIANPOINT((2.14289276659,8.571439104,1.93875932375));
#1206=IFCCARTESIANPOINT((2.14289276659,9.6428676754,1.93875932375));
#1207=IFCCARTESIANPOINT((2.14289276659,10.7142962469,1.93875932375));
#1208=IFCCARTESIANPOINT((2.14289276659,11.7857248183,1.93875932375));
#1209=IFCCARTESIANPOINT((2.14289276659,12.8571533897,5.1287882986));
#1210=IFCCARTESIANPOINT((2.14289276659,13.9285819612,1.93875932375));
#1211=IFCCARTESIANPOINT((2.14289276659,15.0000105326,1.74110678043));
#1212=IFCCARTESIANPOINT((3.214321338,1.0532591771E-05,1.93875932375));
#1213=IFCCARTESIANPOINT((3.214321338,1.07143910402,1.93875932375));
#1214=IFCCARTESIANPOINT((3.214321338,2.14286767545,5.18065991E-05));
#1215=IFCCARTESIANPOINT((3.214321338,3.2142962469,1.93875932375));
#1216=IFCCARTESIANPOINT((3.214321338,4.2857248183,1.93875932375));
#1217=IFCCARTESIANPOINT((3.214321338,5.3571533897,5.18065991E-05));
#1218=IFCCARTESIANPOINT((3.214321338,6.4285819612,1.93875932375));
#1219=IFCCARTESIANPOINT((3.214321338,7.5000105326,1.93875932375));
#1220=IFCCARTESIANPOINT((3.214321338,8.571439104,1.93875932375));
#1221=IFCCARTESIANPOINT((3.214321338,9.6428676754,1.93875932375));
#1222=IFCCARTESIANPOINT((3.214321338,10.7142962469,1.93875932375));
#1223=IFCCARTESIANPOINT((3.214321338,11.7857248183,1.93875932375));
#1224=IFCCARTESIANPOINT((3.214321338,12.8571533897,5.1287882986));
#1225=IFCCARTESIANPOINT((3.214321338,13.9285819612,1.93875932375));
#1226=IFCCARTESIANPOINT((3.214321338,15.0000105326,1.93875932375));
#1227=IFCCARTESIANPOINT((4.2857499094,1.0532591771E-05,1.93875932375));
#1228=IFCCARTESIANPOINT((4.2857499094,1.07143910402,1.93875932375));
#1229=IFCCARTESIANPOINT((4.2857499094,2.14286767545,1.93875932375));
#1230=IFCCARTESIANPOINT((4.2857499094,3.2142962469,1.93875932375));
#1231=IFCCARTESIANPOINT((4.2857499094,4.2857248183,1.93875932375));
#1232=IFCCARTESIANPOINT((4.2857499094,5.3571533897,1.93875932375));
#1233=IFCCARTESIANPOINT((4.2857499094,6.4285819612,1.93875932375));
#1234=IFCCARTESIANPOINT((4.2857499094,7.5000105326,1.93875932375));
#1235=IFCCARTESIANPOINT((4.2857499094,8.571439104,1.93875932375));
#1236=IFCCARTESIANPOINT((4.2857499094,9.6428676754,1.93875932375));
#1237=IFCCARTESIANPOINT((4.2857499094,10.7142962469,1.93875932375));
#1238=IFCCARTESIANPOINT((4.2857499094,11.7857248183,1.93875932375));
#1239=IFCCARTESIANPOINT((4.2857499094,12.8571533897,1.93875932375));
#1240=IFCCARTESIANPOINT((4.2857499094,13.9285819612,1.93875932375));
#1241=IFCCARTESIANPOINT((4.2857499094,15.0000105326,1.93875932375));
#1242=IFCCARTESIANPOINT((5.3571784809,1.0532591771E-05,1.93875932375));
#1243=IFCCARTESIANPOINT((5.3571784809,1.07143910402,1.93875932375));
#1244=IFCCARTESIANPOINT((5.3571784809,2.14286767545,1.93875932375));
#1245=IFCCARTESIANPOINT((5.3571784809,3.2142962469,1.93875932375));
#1246=IFCCARTESIANPOINT((5.3571784809,4.2857248183,1.93875932375));
#1247=IFCCARTESIANPOINT((5.3571784809,5.3571533897,1.93875932375));
#1248=IFCCARTESIANPOINT((5.3571784809,6.4285819612,1.93875932375));
#1249=IFCCARTESIANPOINT((5.3571784809,7.5000105326,1.93875932375));
#1250=IFCCARTESIANPOINT((5.3571784809,8.571439104,1.93875932375));
#1251=IFCCARTESIANPOINT((5.3571784809,9.6428676754,1.93875932375));
#1252=IFCCARTESIANPOINT((5.3571784809,10.7142962469,5.1287882986));
#1253=IFCCARTESIANPOINT((5.3571784809,11.7857248183,1.93875932375));
#1254=IFCCARTESIANPOINT((5.3571784809,12.8571533897,1.93875932375));
#1255=IFCCARTESIANPOINT((5.3571784809,13.9285819612,1.93875932375));
#1256=IFCCARTESIANPOINT((5.3571784809,15.0000105326,1.93875932375));
#1257=IFCCARTESIANPOINT((6.4286070523,1.0532591771E-05,1.93875932375));
#1258=IFCCARTESIANPOINT((6.4286070523,1.07143910402,1.93875932375));
#1259=IFCCARTESIANPOINT((6.4286070523,2.14286767545,1.93875932375));
#1260=IFCCARTESIANPOINT((6.4286070523,3.2142962469,1.93875932375));
#1261=IFCCARTESIANPOINT((6.4286070523,4.2857248183,1.93875932375));
#1262=IFCCARTESIANPOINT((6.4286070523,5.3571533897,1.93875932375));
#1263=IFCCARTESIANPOINT((6.4286070523,6.4285819612,5.18065991E-05));
#1264=IFCCARTESIANPOINT((6.4286070523,7.5000105326,1.93875932375));
#1265=IFCCARTESIANPOINT((6.4286070523,8.571439104,1.93875932375));
#1266=IFCCARTESIANPOINT((6.4286070523,9.6428676754,1.93875932375));
#1267=IFCCARTESIANPOINT((6.4286070523,10.7142962469,5.1287882986));
#1268=IFCCARTESIANPOINT((6.4286070523,11.7857248183,1.93875932375));
#1269=IFCCARTESIANPOINT((6.4286070523,12.8571533897,1.93875932375));
#1270=IFCCARTESIANPOINT((6.4286070523,13.9285819612,1.93875932375));
#1271=IFCCARTESIANPOINT((6.4286070523,15.0000105326,1.93875932375));
#1272=IFCCARTESIANPOINT((7.5000356237,1.0532591771E-05,1.93875932375));
#1273=IFCCARTESIANPOINT((7.5000356237,1.07143910402,1.93875932375));
#1274=IFCCARTESIANPOINT((7.5000356237,2.14286767545,1.93875932375));
#1275=IFCCARTESIANPOINT((7.5000356237,3.2142962469,1.93875932375));
#1276=IFCCARTESIANPOINT((7.5000356237,4.2857248183,1.93875932375));
#1277=IFCCARTESIANPOINT((7.5000356237,5.3571533897,1.93875932375));
#1278=IFCCARTESIANPOINT((7.5000356237,6.4285819612,1.93875932375));
#1279=IFCCARTESIANPOINT((7.5000356237,7.5000105326,1.93875932375));
#1280=IFCCARTESIANPOINT((7.5000356237,8.571439104,1.93875932375));
#1281=IFCCARTESIANPOINT((7.5000356237,9.6428676754,5.1287882986));
#1282=IFCCARTESIANPOINT((7.5000356237,10.7142962469,1.93875932375));
#1283=IFCCARTESIANPOINT((7.5000356237,11.7857248183,1.93875932375));
#1284=IFCCARTESIANPOINT((7.5000356237,12.8571533897,1.93875932375));
#1285=IFCCARTESIANPOINT((7.5000356237,13.9285819612,1.93875932375));
#1286=IFCCARTESIANPOINT((7.5000356237,15.0000105326,1.93875932375));
#1287=IFCCARTESIANPOINT((8.5714641952,1.0532591771E-05,1.93875932375));
#1288=IFCCARTESIANPOINT((8.5714641952,1.07143910402,1.93875932375));
#1289=IFCCARTESIANPOINT((8.5714641952,2.14286767545,1.93875932375));
#1290=IFCCARTESIANPOINT((8.5714641952,3.2142962469,1.93875932375));
#1291=IFCCARTESIANPOINT((8.5714641952,4.2857248183,1.93875932375));
#1292=IFCCARTESIANPOINT((8.5714641952,5.3571533897,1.93875932375));
#1293=IFCCARTESIANPOINT((8.5714641952,6.4285819612,1.93875932375));
#1294=IFCCARTESIANPOINT((8.5714641952,7.5000105326,1.93875932375));
#1295=IFCCARTESIANPOINT((8.5714641952,8.571439104,1.93875932375));
#1296=IFCCARTESIANPOINT((8.5714641952,9.6428676754,1.93875932375));
#1297=IFCCARTESIANPOINT((8.5714641952,10.7142962469,1.93875932375));
#1298=IFCCARTESIANPOINT((8.5714641952,11.7857248183,1.93875932375));
#1299=IFCCARTESIANPOINT((8.5714641952,12.8571533897,1.93875932375));
#1300=IFCCARTESIANPOINT((8.5714641952,13.9285819612,1.93875932375));
#1301=IFCCARTESIANPOINT((8.5714641952,15.0000105326,1.93875932375));
#1302=IFCCARTESIANPOINT((9.6428927666,1.0532591771E-05,1.93875932375));
#1303=IFCCARTESIANPOINT((9.6428927666,1.07143910402,1.93875932375));
#1304=IFCCARTESIANPOINT((9.6428927666,2.14286767545,1.93875932375));
#1305=IFCCARTESIANPOINT((9.6428927666,3.2142962469,1.93875932375));
#1306=IFCCARTESIANPOINT((9.6428927666,4.2857248183,1.93875932375));
#1307=IFCCARTESIANPOINT((9.6428927666,5.3571533897,1.93875932375));
#1308=IFCCARTESIANPOINT((9.6428927666,6.4285819612,5.18065991E-05));
#1309=IFCCARTESIANPOINT((9.6428927666,7.5000105326,1.93875932375));
#1310=IFCCARTESIANPOINT((9.6428927666,8.571439104,1.93875932375));
#1311=IFCCARTESIANPOINT((9.6428927666,9.6428676754,1.93875932375));
#1312=IFCCARTESIANPOINT((9.6428927666,10.7142962469,1.93875932375));
#1313=IFCCARTESIANPOINT((9.6428927666,11.7857248183,1.93875932375));
#1314=IFCCARTESIANPOINT((9.6428927666,12.8571533897,1.93875932375));
#1315=IFCCARTESIANPOINT((9.6428927666,13.9285819612,1.93875932375));
#1316=IFCCARTESIANPOINT((10.0962936606,15.0376487171,1.35588611674));
#1317=IFCCARTESIANPOINT((10.714321338,1.0532591771E-05,1.93875932375));
#1318=IFCCARTESIANPOINT((10.714321338,1.07143910402,1.93875932375));
#1319=IFCCARTESIANPOINT((10.714321338,2.14286767545,1.93875932375));
#1320=IFCCARTESIANPOINT((10.714321338,3.2142962469,1.93875932375));
#1321=IFCCARTESIANPOINT((10.714321338,4.2857248183,5.18065991E-05));
#1322=IFCCARTESIANPOINT((10.714321338,5.3571533897,1.93875932375));
#1323=IFCCARTESIANPOINT((10.714321338,6.4285819612,1.93875932375));
#1324=IFCCARTESIANPOINT((10.714321338,7.5000105326,1.93875932375));
#1325=IFCCARTESIANPOINT((10.714321338,8.571439104,5.18065991E-05));
#1326=IFCCARTESIANPOINT((10.714321338,9.6428676754,1.93875932375));
#1327=IFCCARTESIANPOINT((10.714321338,10.7142962469,1.93875932375));
#1328=IFCCARTESIANPOINT((10.714321338,11.7857248183,1.93875932375));
#1329=IFCCARTESIANPOINT((10.714321338,12.8571533897,1.93875932375));
#1330=IFCCARTESIANPOINT((10.714321338,13.9285819612,1.93875932375));
#1331=IFCCARTESIANPOINT((10.714321338,15.0000105326,1.93875932375));
#1332=IFCCARTESIANPOINT((11.7857499094,1.0532591771E-05,1.93875932375));
#1333=IFCCARTESIANPOINT((11.7857499094,1.07143910402,1.93875932375));
#1334=IFCCARTESIANPOINT((11.7857499094,2.14286767545,1.93875932375));
#1335=IFCCARTESIANPOINT((11.7857499094,3.2142962469,1.93875932375));
#1336=IFCCARTESIANPOINT((11.7857499094,4.2857248183,1.93875932375));
#1337=IFCCARTESIANPOINT((11.7857499094,5.3571533897,1.93875932375));
#1338=IFCCARTESIANPOINT((11.7857499094,6.4285819612,1.93875932375));
#1339=IFCCARTESIANPOINT((11.7857499094,7.5000105326,1.93875932375));
#1340=IFCCARTESIANPOINT((11.7857499094,8.571439104,1.93875932375));
#1341=IFCCARTESIANPOINT((11.7857499094,9.6428676754,1.93875932375));
#1342=IFCCARTESIANPOINT((11.7857499094,10.7142962469,1.93875932375));
#1343=IFCCARTESIANPOINT((11.7857499094,11.7857248183,1.93875932375));
#1344=IFCCARTESIANPOINT((11.7857499094,12.8571533897,1.93875932375));
#1345=IFCCARTESIANPOINT((11.7857499094,13.9285819612,1.93875932375));
#1346=IFCCARTESIANPOINT((11.3978907176,14.8693563406,2.44049268426));
#1347=IFCCARTESIANPOINT((12.8571784809,1.0532591771E-05,1.93875932375));
#1348=IFCCARTESIANPOINT((12.8571784809,1.07143910402,1.93875932375));
#1349=IFCCARTESIANPOINT((12.8571784809,2.14286767545,1.93875932375));
#1350=IFCCARTESIANPOINT((12.8571784809,3.2142962469,1.93875932375));
#1351=IFCCARTESIANPOINT((12.8571784809,4.2857248183,1.93875932375));
#1352=IFCCARTESIANPOINT((12.8571784809,5.3571533897,1.93875932375));
#1353=IFCCARTESIANPOINT((12.8571784809,6.4285819612,1.93875932375));
#1354=IFCCARTESIANPOINT((12.8571784809,7.5000105326,5.18065991E-05));
#1355=IFCCARTESIANPOINT((12.8571784809,8.571439104,1.93875932375));
#1356=IFCCARTESIANPOINT((12.8571784809,9.6428676754,1.93875932375));
#1357=IFCCARTESIANPOINT((12.8571784809,10.7142962469,1.93875932375));
#1358=IFCCARTESIANPOINT((12.8571784809,11.7857248183,1.93875932375));
#1359=IFCCARTESIANPOINT((12.8571784809,12.8571533897,1.93875932375));
#1360=IFCCARTESIANPOINT((12.8571784809,13.9285819612,1.93875932375));
#1361=IFCCARTESIANPOINT((12.8568370215,14.9497010766,2.56095173322));
#1362=IFCCARTESIANPOINT((13.9286070523,1.0532591771E-05,1.93875932375));
#1363=IFCCARTESIANPOINT((13.9286070523,1.07143910402,1.93875932375));
#1364=IFCCARTESIANPOINT((13.9286070523,2.14286767545,1.93875932375));
#1365=IFCCARTESIANPOINT((13.9286070523,3.2142962469,1.93875932375));
#1366=IFCCARTESIANPOINT((13.9286070523,4.2857248183,1.93875932375));
#1367=IFCCARTESIANPOINT((13.9286070523,5.3571533897,1.93875932375));
#1368=IFCCARTESIANPOINT((13.9286070523,6.4285819612,1.93875932375));
#1369=IFCCARTESIANPOINT((13.9286070523,7.5000105326,1.93875932375));
#1370=IFCCARTESIANPOINT((13.9286070523,8.571439104,1.93875932375));
#1371=IFCCARTESIANPOINT((13.9286070523,9.6428676754,1.93875932375));
#1372=IFCCARTESIANPOINT((13.9286070523,10.7142962469,1.93875932375));
#1373=IFCCARTESIANPOINT((13.9286070523,11.7857248183,1.93875932375));
#1374=IFCCARTESIANPOINT((13.9286070523,12.8571533897,1.93875932375));
#1375=IFCCARTESIANPOINT((13.9286070523,13.9285819612,1.93875932375));
#1376=IFCCARTESIANPOINT((10.6452090386,11.7314780592,4.9981805335));
#1377=IFCCARTESIANPOINT((15.0000356237,1.0532591771E-05,0.59627198567));
#1378=IFCCARTESIANPOINT((15.0000356237,1.07143910402,1.93875932375));
#1379=IFCCARTESIANPOINT((15.0000356237,2.14286767545,1.93875932375));
#1380=IFCCARTESIANPOINT((15.0000356237,3.2142962469,1.93875932375));
#1381=IFCCARTESIANPOINT((15.0000356237,4.2857248183,1.93875932375));
#1382=IFCCARTESIANPOINT((15.0000356237,5.3571533897,1.93875932375));
#1383=IFCCARTESIANPOINT((15.0000356237,6.4285819612,0.59627198567));
#1384=IFCCARTESIANPOINT((15.0000356237,7.5000105326,1.93875932375));
#1385=IFCCARTESIANPOINT((15.0000356237,8.571439104,1.93875932375));
#1386=IFCCARTESIANPOINT((15.0000356237,9.6428676754,0.59627198567));
#1387=IFCCARTESIANPOINT((15.0000356237,10.7142962469,1.93875932375));
#1388=IFCCARTESIANPOINT((15.0000356237,11.7857248183,1.93875932375));
#1389=IFCCARTESIANPOINT((15.0000356237,12.8571533897,1.93875932375));
#1390=IFCCARTESIANPOINT((15.0000356237,13.9285819612,0.59627198567));
#1391=IFCCARTESIANPOINT((15.0000356237,15.0000105326,1.93875932375));
#1392=IFCPOLYLINE((#1393,#1394));
#1393=IFCCARTESIANPOINT((0.,0.0001));
#1394=IFCCARTESIANPOINT((0.,0.));
#1395=IFCBSPLINESURFACEWITHKNOTS(9,9,((#1396,#1397,#1398,#1399,#1400,#1401,#1402,#1403,#1404,#1405,#1406,#1407,#1408,#1409,#1410),(#1411,#1412,#1413,#1414,#1415,#1416,#1417,#1418,#1419,#1420,#1421,#1422,#1423,#1424,#1425),(#1426,#1427,#1428,#1429,#1430,#1431,#1432,#1433,#1434,#1435,#1436,#1437,#1438,#1439,#1440),(#1441,#1442,#1443,#1444,#1445,#1446,#1447,#1448,#1449,#1450,#1451,#1452,#1453,#1454,#1455),(#1456,#1457,#1458,#1459,#1460,#1461,#1462,#1463,#1464,#1465,#1466,#1467,#1468,#1469,#1470),(#1471,#1472,#1473,#1474,#1475,#1476,#1477,#1478,#1479,#1480,#1481,#1482,#1483,#1484,#1485),(#1486,#1487,#1488,#1489,#1490,#1491,#1492,#1493,#1494,#1495,#1496,#1497,#1498,#1499,#1500),(#1501,#1502,#1503,#1504,#1505,#1506,#1507,#1508,#1509,#1510,#1511,#1512,#1513,#1514,#1515),(#1516,#1517,#1518,#1519,#1520,#1521,#1522,#1523,#1524,#1525,#1526,#1527,#1528,#1529,#1530),(#1531,#1532,#1533,#1534,#1535,#1536,#1537,#1538,#1539,#1540,#1541,#1542,#1543,#1544,#1545),(#1546,#1547,#1548,#1549,#1550,#1551,#1552,#1553,#1554,#1555,#1556,#1557,#1558,#1559,#1560),(#1561,#1562,#1563,#1564,#1565,#1566,#1567,#1568,#1569,#1570,#1571,#1572,#1573,#1574,#1575),(#1576,#1577,#1578,#1579,#1580,#1581,#1582,#1583,#1584,#1585,#1586,#1587,#1588,#1589,#1590),(#1591,#1592,#1593,#1594,#1595,#1596,#1597,#1598,#1599,#1600,#1601,#1602,#1603,#1604,#1605),(#1606,#1607,#1608,#1609,#1610,#1611,#1612,#1613,#1614,#1615,#1616,#1617,#1618,#1619,#1620)),.UNSPECIFIED.,.F.,.F.,.F.,(10,1,1,1,1,1,10),(10,1,1,1,1,1,10),(0.,0.166666666666667,0.333333333333333,0.5,0.666666666666667,0.833333333333333,1.),(0.,0.166666666666667,0.333333333333333,0.5,0.666666666666667,0.833333333333333,1.),.UNSPECIFIED.);
#1396=IFCCARTESIANPOINT((3.5623730998E-05,1.0532591771E-05,1.93875932375));
#1397=IFCCARTESIANPOINT((3.5623730998E-05,1.07143910402,1.93875932375));
#1398=IFCCARTESIANPOINT((3.5623730905E-05,2.14286767545,3.1754724911));
#1399=IFCCARTESIANPOINT((3.5623730998E-05,3.2142962469,1.93875932375));
#1400=IFCCARTESIANPOINT((3.5623730905E-05,4.2857248183,3.1754724911));
#1401=IFCCARTESIANPOINT((3.5623730998E-05,5.3571533897,1.93875932375));
#1402=IFCCARTESIANPOINT((3.5623730998E-05,6.4285819612,1.93875932375));
#1403=IFCCARTESIANPOINT((3.5623730998E-05,7.5000105326,1.93875932375));
#1404=IFCCARTESIANPOINT((3.5623730905E-05,8.571439104,3.1754724911));
#1405=IFCCARTESIANPOINT((3.5623730998E-05,9.6428676754,1.93875932375));
#1406=IFCCARTESIANPOINT((3.5623730998E-05,10.7142962469,1.93875932375));
#1407=IFCCARTESIANPOINT((3.5623730905E-05,11.7857248183,3.1754724911));
#1408=IFCCARTESIANPOINT((3.5623730998E-05,12.8571533897,1.93875932375));
#1409=IFCCARTESIANPOINT((3.5623730998E-05,13.9285819612,1.93875932375));
#1410=IFCCARTESIANPOINT((3.5623730998E-05,15.0000105326,1.93875932375));
#1411=IFCCARTESIANPOINT((1.07146419516,1.0532591771E-05,1.93875932375));
#1412=IFCCARTESIANPOINT((1.07146419516,1.07143910402,1.93875932375));
#1413=IFCCARTESIANPOINT((1.07146419516,2.14286767545,1.93875932375));
#1414=IFCCARTESIANPOINT((1.07146419516,3.2142962469,1.93875932375));
#1415=IFCCARTESIANPOINT((1.07146419516,4.2857248183,1.93875932375));
#1416=IFCCARTESIANPOINT((1.07146419516,5.3571533897,1.93875932375));
#1417=IFCCARTESIANPOINT((1.07146419516,6.4285819612,1.93875932375));
#1418=IFCCARTESIANPOINT((1.07146419516,7.5000105326,1.93875932375));
#1419=IFCCARTESIANPOINT((1.07146419516,8.571439104,1.93875932375));
#1420=IFCCARTESIANPOINT((1.07146419516,9.6428676754,1.93875932375));
#1421=IFCCARTESIANPOINT((1.07146419516,10.7142962469,1.93875932375));
#1422=IFCCARTESIANPOINT((1.07146419516,11.7857248183,1.93875932375));
#1423=IFCCARTESIANPOINT((1.07146419516,12.8571533897,1.93875932375));
#1424=IFCCARTESIANPOINT((1.07146419516,13.9285819612,1.93875932375));
#1425=IFCCARTESIANPOINT((1.04385994151,14.9865837438,1.54886729649));
#1426=IFCCARTESIANPOINT((2.14289276659,1.0532591771E-05,1.93875932375));
#1427=IFCCARTESIANPOINT((2.14289276659,1.07143910402,1.93875932375));
#1428=IFCCARTESIANPOINT((2.14289276659,2.14286767545,1.93875932375));
#1429=IFCCARTESIANPOINT((2.14289276659,3.2142962469,1.93875932375));
#1430=IFCCARTESIANPOINT((2.14289276659,4.2857248183,1.93875932375));
#1431=IFCCARTESIANPOINT((2.14289276659,5.3571533897,1.93875932375));
#1432=IFCCARTESIANPOINT((2.14289276659,6.4285819612,1.93875932375));
#1433=IFCCARTESIANPOINT((2.14289276659,7.5000105326,1.93875932375));
#1434=IFCCARTESIANPOINT((2.14289276659,8.571439104,1.93875932375));
#1435=IFCCARTESIANPOINT((2.14289276659,9.6428676754,1.93875932375));
#1436=IFCCARTESIANPOINT((2.14289276659,10.7142962469,1.93875932375));
#1437=IFCCARTESIANPOINT((2.14289276659,11.7857248183,1.93875932375));
#1438=IFCCARTESIANPOINT((2.14289276659,12.8571533897,5.1287882986));
#1439=IFCCARTESIANPOINT((2.14289276659,13.9285819612,1.93875932375));
#1440=IFCCARTESIANPOINT((2.14289276659,15.0000105326,1.74110678043));
#1441=IFCCARTESIANPOINT((3.214321338,1.0532591771E-05,1.93875932375));
#1442=IFCCARTESIANPOINT((3.214321338,1.07143910402,1.93875932375));
#1443=IFCCARTESIANPOINT((3.214321338,2.14286767545,5.18065991E-05));
#1444=IFCCARTESIANPOINT((3.214321338,3.2142962469,1.93875932375));
#1445=IFCCARTESIANPOINT((3.214321338,4.2857248183,1.93875932375));
#1446=IFCCARTESIANPOINT((3.214321338,5.3571533897,5.18065991E-05));
#1447=IFCCARTESIANPOINT((3.214321338,6.4285819612,1.93875932375));
#1448=IFCCARTESIANPOINT((3.214321338,7.5000105326,1.93875932375));
#1449=IFCCARTESIANPOINT((3.214321338,8.571439104,1.93875932375));
#1450=IFCCARTESIANPOINT((3.214321338,9.6428676754,1.93875932375));
#1451=IFCCARTESIANPOINT((3.214321338,10.7142962469,1.93875932375));
#1452=IFCCARTESIANPOINT((3.214321338,11.7857248183,1.93875932375));
#1453=IFCCARTESIANPOINT((3.214321338,12.8571533897,5.1287882986));
#1454=IFCCARTESIANPOINT((3.214321338,13.9285819612,1.93875932375));
#1455=IFCCARTESIANPOINT((3.214321338,15.0000105326,1.93875932375));
#1456=IFCCARTESIANPOINT((4.2857499094,1.0532591771E-05,1.93875932375));
#1457=IFCCARTESIANPOINT((4.2857499094,1.07143910402,1.93875932375));
#1458=IFCCARTESIANPOINT((4.2857499094,2.14286767545,1.93875932375));
#1459=IFCCARTESIANPOINT((4.2857499094,3.2142962469,1.93875932375));
#1460=IFCCARTESIANPOINT((4.2857499094,4.2857248183,1.93875932375));
#1461=IFCCARTESIANPOINT((4.2857499094,5.3571533897,1.93875932375));
#1462=IFCCARTESIANPOINT((4.2857499094,6.4285819612,1.93875932375));
#1463=IFCCARTESIANPOINT((4.2857499094,7.5000105326,1.93875932375));
#1464=IFCCARTESIANPOINT((4.2857499094,8.571439104,1.93875932375));
#1465=IFCCARTESIANPOINT((4.2857499094,9.6428676754,1.93875932375));
#1466=IFCCARTESIANPOINT((4.2857499094,10.7142962469,1.93875932375));
#1467=IFCCARTESIANPOINT((4.2857499094,11.7857248183,1.93875932375));
#1468=IFCCARTESIANPOINT((4.2857499094,12.8571533897,1.93875932375));
#1469=IFCCARTESIANPOINT((4.2857499094,13.9285819612,1.93875932375));
#1470=IFCCARTESIANPOINT((4.2857499094,15.0000105326,1.93875932375));
#1471=IFCCARTESIANPOINT((5.3571784809,1.0532591771E-05,1.93875932375));
#1472=IFCCARTESIANPOINT((5.3571784809,1.07143910402,1.93875932375));
#1473=IFCCARTESIANPOINT((5.3571784809,2.14286767545,1.93875932375));
#1474=IFCCARTESIANPOINT((5.3571784809,3.2142962469,1.93875932375));
#1475=IFCCARTESIANPOINT((5.3571784809,4.2857248183,1.93875932375));
#1476=IFCCARTESIANPOINT((5.3571784809,5.3571533897,1.93875932375));
#1477=IFCCARTESIANPOINT((5.3571784809,6.4285819612,1.93875932375));
#1478=IFCCARTESIANPOINT((5.3571784809,7.5000105326,1.93875932375));
#1479=IFCCARTESIANPOINT((5.3571784809,8.571439104,1.93875932375));
#1480=IFCCARTESIANPOINT((5.3571784809,9.6428676754,1.93875932375));
#1481=IFCCARTESIANPOINT((5.3571784809,10.7142962469,5.1287882986));
#1482=IFCCARTESIANPOINT((5.3571784809,11.7857248183,1.93875932375));
#1483=IFCCARTESIANPOINT((5.3571784809,12.8571533897,1.93875932375));
#1484=IFCCARTESIANPOINT((5.3571784809,13.9285819612,1.93875932375));
#1485=IFCCARTESIANPOINT((5.3571784809,15.0000105326,1.93875932375));
#1486=IFCCARTESIANPOINT((6.4286070523,1.0532591771E-05,1.93875932375));
#1487=IFCCARTESIANPOINT((6.4286070523,1.07143910402,1.93875932375));
#1488=IFCCARTESIANPOINT((6.4286070523,2.14286767545,1.93875932375));
#1489=IFCCARTESIANPOINT((6.4286070523,3.2142962469,1.93875932375));
#1490=IFCCARTESIANPOINT((6.4286070523,4.2857248183,1.93875932375));
#1491=IFCCARTESIANPOINT((6.4286070523,5.3571533897,1.93875932375));
#1492=IFCCARTESIANPOINT((6.4286070523,6.4285819612,5.18065991E-05));
#1493=IFCCARTESIANPOINT((6.4286070523,7.5000105326,1.93875932375));
#1494=IFCCARTESIANPOINT((6.4286070523,8.571439104,1.93875932375));
#1495=IFCCARTESIANPOINT((6.4286070523,9.6428676754,1.93875932375));
#1496=IFCCARTESIANPOINT((6.4286070523,10.7142962469,5.1287882986));
#1497=IFCCARTESIANPOINT((6.4286070523,11.7857248183,1.93875932375));
#1498=IFCCARTESIANPOINT((6.4286070523,12.8571533897,1.93875932375));
#1499=IFCCARTESIANPOINT((6.4286070523,13.9285819612,1.93875932375));
#1500=IFCCARTESIANPOINT((6.4286070523,15.0000105326,1.93875932375));
#1501=IFCCARTESIANPOINT((7.5000356237,1.0532591771E-05,1.93875932375));
#1502=IFCCARTESIANPOINT((7.5000356237,1.07143910402,1.93875932375));
#1503=IFCCARTESIANPOINT((7.5000356237,2.14286767545,1.93875932375));
#1504=IFCCARTESIANPOINT((7.5000356237,3.2142962469,1.93875932375));
#1505=IFCCARTESIANPOINT((7.5000356237,4.2857248183,1.93875932375));
#1506=IFCCARTESIANPOINT((7.5000356237,5.3571533897,1.93875932375));
#1507=IFCCARTESIANPOINT((7.5000356237,6.4285819612,1.93875932375));
#1508=IFCCARTESIANPOINT((7.5000356237,7.5000105326,1.93875932375));
#1509=IFCCARTESIANPOINT((7.5000356237,8.571439104,1.93875932375));
#1510=IFCCARTESIANPOINT((7.5000356237,9.6428676754,5.1287882986));
#1511=IFCCARTESIANPOINT((7.5000356237,10.7142962469,1.93875932375));
#1512=IFCCARTESIANPOINT((7.5000356237,11.7857248183,1.93875932375));
#1513=IFCCARTESIANPOINT((7.5000356237,12.8571533897,1.93875932375));
#1514=IFCCARTESIANPOINT((7.5000356237,13.9285819612,1.93875932375));
#1515=IFCCARTESIANPOINT((7.5000356237,15.0000105326,1.93875932375));
#1516=IFCCARTESIANPOINT((8.5714641952,1.0532591771E-05,1.93875932375));
#1517=IFCCARTESIANPOINT((8.5714641952,1.07143910402,1.93875932375));
#1518=IFCCARTESIANPOINT((8.5714641952,2.14286767545,1.93875932375));
#1519=IFCCARTESIANPOINT((8.5714641952,3.2142962469,1.93875932375));
#1520=IFCCARTESIANPOINT((8.5714641952,4.2857248183,1.93875932375));
#1521=IFCCARTESIANPOINT((8.5714641952,5.3571533897,1.93875932375));
#1522=IFCCARTESIANPOINT((8.5714641952,6.4285819612,1.93875932375));
#1523=IFCCARTESIANPOINT((8.5714641952,7.5000105326,1.93875932375));
#1524=IFCCARTESIANPOINT((8.5714641952,8.571439104,1.93875932375));
#1525=IFCCARTESIANPOINT((8.5714641952,9.6428676754,1.93875932375));
#1526=IFCCARTESIANPOINT((8.5714641952,10.7142962469,1.93875932375));
#1527=IFCCARTESIANPOINT((8.5714641952,11.7857248183,1.93875932375));
#1528=IFCCARTESIANPOINT((8.5714641952,12.8571533897,1.93875932375));
#1529=IFCCARTESIANPOINT((8.5714641952,13.9285819612,1.93875932375));
#1530=IFCCARTESIANPOINT((8.5714641952,15.0000105326,1.93875932375));
#1531=IFCCARTESIANPOINT((9.6428927666,1.0532591771E-05,1.93875932375));
#1532=IFCCARTESIANPOINT((9.6428927666,1.07143910402,1.93875932375));
#1533=IFCCARTESIANPOINT((9.6428927666,2.14286767545,1.93875932375));
#1534=IFCCARTESIANPOINT((9.6428927666,3.2142962469,1.93875932375));
#1535=IFCCARTESIANPOINT((9.6428927666,4.2857248183,1.93875932375));
#1536=IFCCARTESIANPOINT((9.6428927666,5.3571533897,1.93875932375));
#1537=IFCCARTESIANPOINT((9.6428927666,6.4285819612,5.18065991E-05));
#1538=IFCCARTESIANPOINT((9.6428927666,7.5000105326,1.93875932375));
#1539=IFCCARTESIANPOINT((9.6428927666,8.571439104,1.93875932375));
#1540=IFCCARTESIANPOINT((9.6428927666,9.6428676754,1.93875932375));
#1541=IFCCARTESIANPOINT((9.6428927666,10.7142962469,1.93875932375));
#1542=IFCCARTESIANPOINT((9.6428927666,11.7857248183,1.93875932375));
#1543=IFCCARTESIANPOINT((9.6428927666,12.8571533897,1.93875932375));
#1544=IFCCARTESIANPOINT((9.6428927666,13.9285819612,1.93875932375));
#1545=IFCCARTESIANPOINT((10.0962936606,15.0376487171,1.35588611674));
#1546=IFCCARTESIANPOINT((10.714321338,1.0532591771E-05,1.93875932375));
#1547=IFCCARTESIANPOINT((10.714321338,1.07143910402,1.93875932375));
#1548=IFCCARTESIANPOINT((10.714321338,2.14286767545,1.93875932375));
#1549=IFCCARTESIANPOINT((10.714321338,3.2142962469,1.93875932375));
#1550=IFCCARTESIANPOINT((10.714321338,4.2857248183,5.18065991E-05));
#1551=IFCCARTESIANPOINT((10.714321338,5.3571533897,1.93875932375));
#1552=IFCCARTESIANPOINT((10.714321338,6.4285819612,1.93875932375));
#1553=IFCCARTESIANPOINT((10.714321338,7.5000105326,1.93875932375));
#1554=IFCCARTESIANPOINT((10.714321338,8.571439104,5.18065991E-05));
#1555=IFCCARTESIANPOINT((10.714321338,9.6428676754,1.93875932375));
#1556=IFCCARTESIANPOINT((10.714321338,10.7142962469,1.93875932375));
#1557=IFCCARTESIANPOINT((10.714321338,11.7857248183,1.93875932375));
#1558=IFCCARTESIANPOINT((10.714321338,12.8571533897,1.93875932375));
#1559=IFCCARTESIANPOINT((10.714321338,13.9285819612,1.93875932375));
#1560=IFCCARTESIANPOINT((10.714321338,15.0000105326,1.93875932375));
#1561=IFCCARTESIANPOINT((11.7857499094,1.0532591771E-05,1.93875932375));
#1562=IFCCARTESIANPOINT((11.7857499094,1.07143910402,1.93875932375));
#1563=IFCCARTESIANPOINT((11.7857499094,2.14286767545,1.93875932375));
#1564=IFCCARTESIANPOINT((11.7857499094,3.2142962469,1.93875932375));
#1565=IFCCARTESIANPOINT((11.7857499094,4.2857248183,1.93875932375));
#1566=IFCCARTESIANPOINT((11.7857499094,5.3571533897,1.93875932375));
#1567=IFCCARTESIANPOINT((11.7857499094,6.4285819612,1.93875932375));
#1568=IFCCARTESIANPOINT((11.7857499094,7.5000105326,1.93875932375));
#1569=IFCCARTESIANPOINT((11.7857499094,8.571439104,1.93875932375));
#1570=IFCCARTESIANPOINT((11.7857499094,9.6428676754,1.93875932375));
#1571=IFCCARTESIANPOINT((11.7857499094,10.7142962469,1.93875932375));
#1572=IFCCARTESIANPOINT((11.7857499094,11.7857248183,1.93875932375));
#1573=IFCCARTESIANPOINT((11.7857499094,12.8571533897,1.93875932375));
#1574=IFCCARTESIANPOINT((11.7857499094,13.9285819612,1.93875932375));
#1575=IFCCARTESIANPOINT((11.3978907176,14.8693563406,2.44049268426));
#1576=IFCCARTESIANPOINT((12.8571784809,1.0532591771E-05,1.93875932375));
#1577=IFCCARTESIANPOINT((12.8571784809,1.07143910402,1.93875932375));
#1578=IFCCARTESIANPOINT((12.8571784809,2.14286767545,1.93875932375));
#1579=IFCCARTESIANPOINT((12.8571784809,3.2142962469,1.93875932375));
#1580=IFCCARTESIANPOINT((12.8571784809,4.2857248183,1.93875932375));
#1581=IFCCARTESIANPOINT((12.8571784809,5.3571533897,1.93875932375));
#1582=IFCCARTESIANPOINT((12.8571784809,6.4285819612,1.93875932375));
#1583=IFCCARTESIANPOINT((12.8571784809,7.5000105326,5.18065991E-05));
#1584=IFCCARTESIANPOINT((12.8571784809,8.571439104,1.93875932375));
#1585=IFCCARTESIANPOINT((12.8571784809,9.6428676754,1.93875932375));
#1586=IFCCARTESIANPOINT((12.8571784809,10.7142962469,1.93875932375));
#1587=IFCCARTESIANPOINT((12.8571784809,11.7857248183,1.93875932375));
#1588=IFCCARTESIANPOINT((12.8571784809,12.8571533897,1.93875932375));
#1589=IFCCARTESIANPOINT((12.8571784809,13.9285819612,1.93875932375));
#1590=IFCCARTESIANPOINT((12.8568370215,14.9497010766,2.56095173322));
#1591=IFCCARTESIANPOINT((13.9286070523,1.0532591771E-05,1.93875932375));
#1592=IFCCARTESIANPOINT((13.9286070523,1.07143910402,1.93875932375));
#1593=IFCCARTESIANPOINT((13.9286070523,2.14286767545,1.93875932375));
#1594=IFCCARTESIANPOINT((13.9286070523,3.2142962469,1.93875932375));
#1595=IFCCARTESIANPOINT((13.9286070523,4.2857248183,1.93875932375));
#1596=IFCCARTESIANPOINT((13.9286070523,5.3571533897,1.93875932375));
#1597=IFCCARTESIANPOINT((13.9286070523,6.4285819612,1.93875932375));
#1598=IFCCARTESIANPOINT((13.9286070523,7.5000105326,1.93875932375));
#1599=IFCCARTESIANPOINT((13.9286070523,8.571439104,1.93875932375));
#1600=IFCCARTESIANPOINT((13.9286070523,9.6428676754,1.93875932375));
#1601=IFCCARTESIANPOINT((13.9286070523,10.7142962469,1.93875932375));
#1602=IFCCARTESIANPOINT((13.9286070523,11.7857248183,1.93875932375));
#1603=IFCCARTESIANPOINT((13.9286070523,12.8571533897,1.93875932375));
#1604=IFCCARTESIANPOINT((13.9286070523,13.9285819612,1.93875932375));
#1605=IFCCARTESIANPOINT((10.6452090386,11.7314780592,4.9981805335));
#1606=IFCCARTESIANPOINT((15.0000356237,1.0532591771E-05,0.59627198567));
#1607=IFCCARTESIANPOINT((15.0000356237,1.07143910402,1.93875932375));
#1608=IFCCARTESIANPOINT((15.0000356237,2.14286767545,1.93875932375));
#1609=IFCCARTESIANPOINT((15.0000356237,3.2142962469,1.93875932375));
#1610=IFCCARTESIANPOINT((15.0000356237,4.2857248183,1.93875932375));
#1611=IFCCARTESIANPOINT((15.0000356237,5.3571533897,1.93875932375));
#1612=IFCCARTESIANPOINT((15.0000356237,6.4285819612,0.59627198567));
#1613=IFCCARTESIANPOINT((15.0000356237,7.5000105326,1.93875932375));
#1614=IFCCARTESIANPOINT((15.0000356237,8.571439104,1.93875932375));
#1615=IFCCARTESIANPOINT((15.0000356237,9.6428676754,0.59627198567));
#1616=IFCCARTESIANPOINT((15.0000356237,10.7142962469,1.93875932375));
#1617=IFCCARTESIANPOINT((15.0000356237,11.7857248183,1.93875932375));
#1618=IFCCARTESIANPOINT((15.0000356237,12.8571533897,1.93875932375));
#1619=IFCCARTESIANPOINT((15.0000356237,13.9285819612,0.59627198567));
#1620=IFCCARTESIANPOINT((15.0000356237,15.0000105326,1.93875932375));
#1621=IFCBOUNDINGBOX(#1622,15.0000000000001,15.0114148312504,3.33239964536385);
#1622=IFCCARTESIANPOINT((3.5623730952E-05,1.053259176E-05,0.59627198567));
#1623=IFCPERSONANDORGANIZATION(#1624,#1627,$);
#1624=IFCPERSON($,'Last Name','First Name',$,$,$,(#1625),(#1626));
#1625=IFCACTORROLE(.SUPPLIER.,$,$);
#1626=IFCTELECOMADDRESS($,$,$,('Phone'),$,$,('E-Mail'),$,$);
#1627=IFCORGANIZATION($,'Organization Name',$,$,(#1628));
#1628=IFCPOSTALADDRESS($,$,$,$,('Address 1','Address 2'),$,'City/Town','State/Region','Postal Code','Country');
#1629=IFCAPPLICATION(#1630,'***********','OpenBuildings Designer','ABD');
#1630=IFCORGANIZATION($,'Bentley Systems Inc.',$,$,$);
#1631=IFCAXIS2PLACEMENT3D(#1632,#1633,#1634);
#1632=IFCCARTESIANPOINT((0.,0.,0.));
#1633=IFCDIRECTION((0.,0.,1.));
#1634=IFCDIRECTION((1.,0.,0.));
#1635=IFCDIRECTION((0.,1.));
ENDSEC;
END-ISO-10303-21;

ISO-10303-21;
HEADER;
/* Generated by software containing ST-Developer
 * from STEP Tools, Inc. (www.steptools.com) 
 */

FILE_DESCRIPTION(
/* description */ ('ViewDefinition [DesignTransferView]',
'Comment [Comments]',
'Comment [System: OpenBuildings Designer*********** debug build Feb  5
 2020 00:34:15]'),
/* implementation_level */ '2;1');

FILE_NAME(
/* name */ '4.Sphere',
/* time_stamp */ '2020-03-05T12:26:02+03:00',
/* author */ ('First Name Last Name'),
/* organization */ ('Organization Name'),
/* preprocessor_version */ 'ST-DEVELOPER v16.13',
/* originating_system */ 'OpenBuildings Designer***********',
/* authorisation */ 'Administrator');

FILE_SCHEMA (('IFC4'));
ENDSEC;

DATA;
#10=IFCBOUNDINGBOX(#186,12.0594231234222,12.0594231234223,5.86758330849535);
#11=IFCGEOMETRICREPRESENTATIONSUBCONTEXT('Body','Model',*,*,*,*,#13,1.,
 .MODEL_VIEW.,$);
#12=IFCGEOMETRICREPRESENTATIONSUBCONTEXT('Box','Model',*,*,*,*,#13,1.,
 .SKETCH_VIEW.,$);
#13=IFCGEOMETRICREPRESENTATIONCONTEXT($,'Model',3,1.E-6,#68,#189);
#14=IFCSHAPEREPRESENTATION(#11,'Body','SurfaceModel',(#25));
#15=IFCSHAPEREPRESENTATION(#12,'Box','BoundingBox',(#10));
#16=IFCPRODUCTDEFINITIONSHAPE($,$,(#14,#15));
#17=IFCRELCONTAINEDINSPATIALSTRUCTURE('2aJi0CW_wcTJthPGyCEMkK',#252,$,$,
(#20),#18);
#18=IFCBUILDINGSTOREY('0rm7DUqMJhOUcs5v8t1F$L',#252,'Floor 3',
'Administrative and employee lounge',$,#62,$,'Floor 3',.ELEMENT.,9.144);
#19=IFCBUILDING('3_IkJInE13hR6Xwo4VKOgh',#252,'Bldg 1',
'3 Story Building',$,#61,$,'Bldg 1',.ELEMENT.,0.,0.,#244);
#20=IFCBUILDINGELEMENTPROXY('1tA$FB6IgKmuErQnyukDAc',#252,
'Element type: 24','700*bspline surface!Default','',#63,#16,
'700*bspline surface!Default',.NOTDEFINED.);
#21=IFCSURFACESTYLE($,.BOTH.,(#22));
#22=IFCSURFACESTYLESHADING(#23,0.);
#23=IFCCOLOURRGB($,0.,0.,1.);
#24=IFCPRESENTATIONLAYERWITHSTYLE('Elements',$,(#25),$,.U.,.U.,.U.,(#21));
#25=IFCSHELLBASEDSURFACEMODEL((#26));
#26=IFCOPENSHELL((#55));
#27=IFCORIENTEDEDGE(*,*,#32,.T.);
#28=IFCORIENTEDEDGE(*,*,#33,.T.);
#29=IFCORIENTEDEDGE(*,*,#34,.F.);
#30=IFCORIENTEDEDGE(*,*,#35,.F.);
#31=IFCEDGELOOP((#27,#28,#29,#30));
#32=IFCEDGECURVE(#36,#37,#38,.T.);
#33=IFCEDGECURVE(#37,#37,#39,.T.);
#34=IFCEDGECURVE(#37,#36,#40,.T.);
#35=IFCEDGECURVE(#36,#36,#41,.T.);
#36=IFCVERTEXPOINT(#70);
#37=IFCVERTEXPOINT(#166);
#38=IFCSURFACECURVE(#42,(#46),.PCURVE_S1.);
#39=IFCSURFACECURVE(#43,(#47),.PCURVE_S1.);
#40=IFCSURFACECURVE(#44,(#48),.PCURVE_S1.);
#41=IFCSURFACECURVE(#45,(#49),.PCURVE_S1.);
#42=IFCRATIONALBSPLINECURVEWITHKNOTS(3,(#70,#94,#118,#142,#166),
 .UNSPECIFIED.,.F.,.U.,(4,1,4),(0.,0.431315603064826,1.),.UNSPECIFIED.,
(1.,1.,1.,1.,1.));
#43=IFCRATIONALBSPLINECURVEWITHKNOTS(2,(#166,#170,#166,#170,#166,#170,#166,
#170,#166,#170,#166,#170,#166,#170,#166,#170,#166,#170,#166,#170,#166,#170,
#166,#170,#166),.UNSPECIFIED.,.T.,.U.,(3,2,2,2,2,2,2,2,2,2,2,2,3),(0.,0.0833333333333333,
0.166666666666667,0.25,0.333333333333333,0.416666666666667,0.5,0.583333333333333,
0.666666666666667,0.75,0.833333333333333,0.916666666666667,1.),
 .UNSPECIFIED.,(1.,0.965925826289068,1.,0.965925826289068,1.,0.965925826289068,
1.,0.965925826289068,1.,0.965925826289068,1.,0.965925826289068,1.,0.965925826289068,
1.,0.965925826289068,1.,0.965925826289068,1.,0.965925826289068,1.,0.965925826289068,
1.,0.965925826289068,1.));
#44=IFCRATIONALBSPLINECURVEWITHKNOTS(3,(#166,#142,#118,#94,#70),
 .UNSPECIFIED.,.F.,.U.,(4,1,4),(0.,0.568684396935174,1.),.UNSPECIFIED.,
(1.,1.,1.,1.,1.));
#45=IFCRATIONALBSPLINECURVEWITHKNOTS(2,(#70,#172,#92,#173,#90,#174,#88,
#175,#86,#176,#84,#177,#82,#178,#80,#179,#78,#180,#76,#181,#74,#182,#72,
#183,#70),.UNSPECIFIED.,.T.,.U.,(3,2,2,2,2,2,2,2,2,2,2,2,3),(0.,0.0833333333333334,
0.166666666666667,0.25,0.333333333333333,0.416666666666667,0.5,0.583333333333333,
0.666666666666667,0.75,0.833333333333333,0.916666666666667,1.),
 .UNSPECIFIED.,(1.,0.965925826289068,1.,0.965925826289068,1.,0.965925826289068,
1.,0.965925826289068,1.,0.965925826289068,1.,0.965925826289068,1.,0.965925826289068,
1.,0.965925826289068,1.,0.965925826289068,1.,0.965925826289068,1.,0.965925826289068,
1.,0.965925826289068,1.));
#46=IFCPCURVE(#56,#50);
#47=IFCPCURVE(#56,#51);
#48=IFCPCURVE(#56,#52);
#49=IFCPCURVE(#56,#53);
#50=IFCPOLYLINE((#167,#168));
#51=IFCPOLYLINE((#168,#169));
#52=IFCPOLYLINE((#169,#171));
#53=IFCPOLYLINE((#171,#167));
#54=IFCFACEOUTERBOUND(#31,.T.);
#55=IFCADVANCEDFACE((#54),#56,.T.);
#56=IFCRATIONALBSPLINESURFACEWITHKNOTS(3,2,((#70,#71,#72,#73,#74,#75,#76,
#77,#78,#79,#80,#81,#82,#83,#84,#85,#86,#87,#88,#89,#90,#91,#92,#93,#70),
(#94,#95,#96,#97,#98,#99,#100,#101,#102,#103,#104,#105,#106,#107,#108,#109,
#110,#111,#112,#113,#114,#115,#116,#117,#94),(#118,#119,#120,#121,#122,
#123,#124,#125,#126,#127,#128,#129,#130,#131,#132,#133,#134,#135,#136,#137,
#138,#139,#140,#141,#118),(#142,#143,#144,#145,#146,#147,#148,#149,#150,
#151,#152,#153,#154,#155,#156,#157,#158,#159,#160,#161,#162,#163,#164,#165,
#142),(#166,#166,#166,#166,#166,#166,#166,#166,#166,#166,#166,#166,#166,
#166,#166,#166,#166,#166,#166,#166,#166,#166,#166,#166,#166)),
 .UNSPECIFIED.,.F.,.T.,.U.,(4,1,4),(3,2,2,2,2,2,2,2,2,2,2,2,3),(0.,0.431315603064826,
1.),(0.,0.0833333333333333,0.166666666666667,0.25,0.333333333333333,0.416666666666667,
0.5,0.583333333333333,0.666666666666667,0.75,0.833333333333333,0.916666666666667,
1.),.UNSPECIFIED.,((1.,0.965925826289068,1.,0.965925826289068,1.,0.965925826289068,
1.,0.965925826289068,1.,0.965925826289068,1.,0.965925826289068,1.,0.965925826289068,
1.,0.965925826289068,1.,0.965925826289068,1.,0.965925826289068,1.,0.965925826289068,
1.,0.965925826289068,1.),(1.,0.965925826289068,1.,0.965925826289068,1.,
0.965925826289068,1.,0.965925826289068,1.,0.965925826289068,1.,0.965925826289068,
1.,0.965925826289068,1.,0.965925826289068,1.,0.965925826289068,1.,0.965925826289068,
1.,0.965925826289068,1.,0.965925826289068,1.),(1.,0.965925826289068,1.,
0.965925826289068,1.,0.965925826289068,1.,0.965925826289068,1.,0.965925826289068,
1.,0.965925826289068,1.,0.965925826289068,1.,0.965925826289068,1.,0.965925826289068,
1.,0.965925826289068,1.,0.965925826289068,1.,0.965925826289068,1.),(1.,
0.965925826289068,1.,0.965925826289068,1.,0.965925826289068,1.,0.965925826289068,
1.,0.965925826289068,1.,0.965925826289068,1.,0.965925826289068,1.,0.965925826289068,
1.,0.965925826289068,1.,0.965925826289068,1.,0.965925826289068,1.,0.965925826289068,
1.),(1.,0.965925826289068,1.,0.965925826289068,1.,0.965925826289068,1.,
0.965925826289068,1.,0.965925826289068,1.,0.965925826289068,1.,0.965925826289068,
1.,0.965925826289068,1.,0.965925826289068,1.,0.965925826289068,1.,0.965925826289068,
1.,0.965925826289068,1.)));
#57=IFCRELAGGREGATES('1kB3AQvfGpkFm_7uHNRdoP',#252,$,$,#241,(#190));
#58=IFCRELAGGREGATES('0Cp_wDOOr3amQ16IWrGpUy',#252,$,$,#190,(#19));
#59=IFCRELAGGREGATES('1NfacxJUhl1S_ka3CQXVRt',#252,$,$,#19,(#18));
#60=IFCLOCALPLACEMENT($,#64);
#61=IFCLOCALPLACEMENT(#60,#65);
#62=IFCLOCALPLACEMENT(#61,#66);
#63=IFCLOCALPLACEMENT(#62,#67);
#64=IFCAXIS2PLACEMENT3D(#69,#187,#188);
#65=IFCAXIS2PLACEMENT3D(#69,#187,#188);
#66=IFCAXIS2PLACEMENT3D(#184,#187,#188);
#67=IFCAXIS2PLACEMENT3D(#185,#187,#188);
#68=IFCAXIS2PLACEMENT3D(#69,#187,#188);
#69=IFCCARTESIANPOINT((0.,0.,0.));
#70=IFCCARTESIANPOINT((6.4206,11.8574600579,5.8675833085));
#71=IFCCARTESIANPOINT((4.9637977381,11.8574600579,5.8675833085));
#72=IFCCARTESIANPOINT((3.702169971,11.129058927,5.8675833085));
#73=IFCCARTESIANPOINT((2.44054220395,10.4006577961,5.8675833085));
#74=IFCCARTESIANPOINT((1.712141073,9.139030029,5.8675833085));
#75=IFCCARTESIANPOINT((0.98373994206,7.8774022619,5.8675833085));
#76=IFCCARTESIANPOINT((0.98373994206,6.4206,5.8675833085));
#77=IFCCARTESIANPOINT((0.98373994206,4.9637977381,5.8675833085));
#78=IFCCARTESIANPOINT((1.712141073,3.702169971,5.8675833085));
#79=IFCCARTESIANPOINT((2.44054220395,2.44054220395,5.8675833085));
#80=IFCCARTESIANPOINT((3.702169971,1.712141073,5.8675833085));
#81=IFCCARTESIANPOINT((4.9637977381,0.98373994206,5.8675833085));
#82=IFCCARTESIANPOINT((6.4206,0.98373994206,5.8675833085));
#83=IFCCARTESIANPOINT((7.8774022619,0.98373994206,5.8675833085));
#84=IFCCARTESIANPOINT((9.139030029,1.712141073,5.8675833085));
#85=IFCCARTESIANPOINT((10.4006577961,2.44054220395,5.8675833085));
#86=IFCCARTESIANPOINT((11.129058927,3.702169971,5.8675833085));
#87=IFCCARTESIANPOINT((11.8574600579,4.9637977381,5.8675833085));
#88=IFCCARTESIANPOINT((11.8574600579,6.4206,5.8675833085));
#89=IFCCARTESIANPOINT((11.8574600579,7.8774022619,5.8675833085));
#90=IFCCARTESIANPOINT((11.129058927,9.139030029,5.8675833085));
#91=IFCCARTESIANPOINT((10.4006577961,10.4006577961,5.8675833085));
#92=IFCCARTESIANPOINT((9.139030029,11.129058927,5.8675833085));
#93=IFCCARTESIANPOINT((7.8774022619,11.8574600579,5.8675833085));
#94=IFCCARTESIANPOINT((6.4206,12.1538858098,4.2138225685));
#95=IFCCARTESIANPOINT((4.8843706973,12.1538858098,4.2138225685));
#96=IFCCARTESIANPOINT((3.5539570951,11.3857711585,4.2138225685));
#97=IFCCARTESIANPOINT((2.22354349289,10.6176565071,4.2138225685));
#98=IFCCARTESIANPOINT((1.45542884153,9.2872429049,4.2138225685));
#99=IFCCARTESIANPOINT((0.68731419017,7.9568293027,4.2138225685));
#100=IFCCARTESIANPOINT((0.68731419017,6.4206,4.2138225685));
#101=IFCCARTESIANPOINT((0.68731419017,4.8843706973,4.2138225685));
#102=IFCCARTESIANPOINT((1.45542884153,3.5539570951,4.2138225685));
#103=IFCCARTESIANPOINT((2.22354349289,2.22354349289,4.2138225685));
#104=IFCCARTESIANPOINT((3.5539570951,1.45542884153,4.2138225685));
#105=IFCCARTESIANPOINT((4.8843706973,0.68731419017,4.2138225685));
#106=IFCCARTESIANPOINT((6.4206,0.68731419017,4.2138225685));
#107=IFCCARTESIANPOINT((7.9568293027,0.68731419017,4.2138225685));
#108=IFCCARTESIANPOINT((9.2872429049,1.45542884153,4.2138225685));
#109=IFCCARTESIANPOINT((10.6176565071,2.22354349289,4.2138225685));
#110=IFCCARTESIANPOINT((11.3857711585,3.5539570951,4.2138225685));
#111=IFCCARTESIANPOINT((12.1538858098,4.8843706973,4.2138225685));
#112=IFCCARTESIANPOINT((12.1538858098,6.4206,4.2138225685));
#113=IFCCARTESIANPOINT((12.1538858098,7.9568293027,4.2138225685));
#114=IFCCARTESIANPOINT((11.3857711585,9.2872429049,4.2138225685));
#115=IFCCARTESIANPOINT((10.6176565071,10.6176565071,4.2138225685));
#116=IFCCARTESIANPOINT((9.2872429049,11.3857711585,4.2138225685));
#117=IFCCARTESIANPOINT((7.9568293027,12.1538858098,4.2138225685));
#118=IFCCARTESIANPOINT((6.4206,12.8411453242,0.37959832929));
#119=IFCCARTESIANPOINT((4.7002200654,12.8411453242,0.37959832929));
#120=IFCCARTESIANPOINT((3.2103273379,11.9809553569,0.37959832929));
#121=IFCCARTESIANPOINT((1.72043461042,11.1207653896,0.37959832929));
#122=IFCCARTESIANPOINT((0.86024464313,9.6308726621,0.37959832929));
#123=IFCCARTESIANPOINT((5.4675843886E-5,8.1409799346,0.37959832929));
#124=IFCCARTESIANPOINT((5.4675843893E-5,6.4206,0.37959832929));
#125=IFCCARTESIANPOINT((5.4675843982E-5,4.7002200654,0.37959832929));
#126=IFCCARTESIANPOINT((0.86024464313,3.2103273379,0.37959832929));
#127=IFCCARTESIANPOINT((1.72043461042,1.72043461042,0.37959832929));
#128=IFCCARTESIANPOINT((3.2103273379,0.86024464313,0.37959832929));
#129=IFCCARTESIANPOINT((4.7002200654,5.4675843886E-5,0.37959832929));
#130=IFCCARTESIANPOINT((6.4206,5.4675843893E-5,0.37959832929));
#131=IFCCARTESIANPOINT((8.1409799346,5.4675843982E-5,0.37959832929));
#132=IFCCARTESIANPOINT((9.6308726621,0.86024464313,0.37959832929));
#133=IFCCARTESIANPOINT((11.1207653896,1.72043461042,0.37959832929));
#134=IFCCARTESIANPOINT((11.9809553569,3.2103273379,0.37959832929));
#135=IFCCARTESIANPOINT((12.8411453242,4.7002200654,0.37959832929));
#136=IFCCARTESIANPOINT((12.8411453242,6.4206,0.37959832929));
#137=IFCCARTESIANPOINT((12.8411453242,8.1409799346,0.37959832929));
#138=IFCCARTESIANPOINT((11.9809553569,9.6308726621,0.37959832929));
#139=IFCCARTESIANPOINT((11.1207653896,11.1207653896,0.37959832929));
#140=IFCCARTESIANPOINT((9.6308726621,11.9809553569,0.37959832929));
#141=IFCCARTESIANPOINT((8.1409799346,12.8411453242,0.37959832929));
#142=IFCCARTESIANPOINT((6.4206,8.7481962665,0.137613179166));
#143=IFCCARTESIANPOINT((5.7969224601,8.7481962665,0.137613179166));
#144=IFCCARTESIANPOINT((5.2568018667,8.4363574966,0.137613179166));
#145=IFCCARTESIANPOINT((4.7166812734,8.1245187266,0.137613179166));
#146=IFCCARTESIANPOINT((4.4048425034,7.5843981333,0.137613179165));
#147=IFCCARTESIANPOINT((4.0930037335,7.0442775399,0.137613179166));
#148=IFCCARTESIANPOINT((4.0930037335,6.4206,0.137613179166));
#149=IFCCARTESIANPOINT((4.0930037335,5.7969224601,0.137613179166));
#150=IFCCARTESIANPOINT((4.4048425034,5.2568018667,0.137613179165));
#151=IFCCARTESIANPOINT((4.7166812734,4.7166812734,0.137613179166));
#152=IFCCARTESIANPOINT((5.2568018667,4.4048425034,0.137613179166));
#153=IFCCARTESIANPOINT((5.7969224601,4.0930037335,0.137613179166));
#154=IFCCARTESIANPOINT((6.4206,4.0930037335,0.137613179166));
#155=IFCCARTESIANPOINT((7.0442775399,4.0930037335,0.137613179166));
#156=IFCCARTESIANPOINT((7.5843981333,4.4048425034,0.137613179166));
#157=IFCCARTESIANPOINT((8.1245187266,4.7166812734,0.137613179166));
#158=IFCCARTESIANPOINT((8.4363574966,5.2568018667,0.137613179166));
#159=IFCCARTESIANPOINT((8.7481962665,5.7969224601,0.137613179166));
#160=IFCCARTESIANPOINT((8.7481962665,6.4206,0.137613179166));
#161=IFCCARTESIANPOINT((8.7481962665,7.0442775399,0.137613179166));
#162=IFCCARTESIANPOINT((8.4363574966,7.5843981333,0.137613179166));
#163=IFCCARTESIANPOINT((8.1245187266,8.1245187266,0.137613179165));
#164=IFCCARTESIANPOINT((7.5843981333,8.4363574966,0.137613179166));
#165=IFCCARTESIANPOINT((7.0442775399,8.7481962665,0.137613179165));
#166=IFCCARTESIANPOINT((6.4206,6.4206,0.));
#167=IFCCARTESIANPOINT((0.,0.));
#168=IFCCARTESIANPOINT((0.0001,0.));
#169=IFCCARTESIANPOINT((0.0001,0.0001));
#170=IFCCARTESIANPOINT((6.6470942439,6.6470942439,0.));
#171=IFCCARTESIANPOINT((0.,0.0001));
#172=IFCCARTESIANPOINT((8.1552869252,12.2757459581,6.0745692359));
#173=IFCCARTESIANPOINT((10.7675532769,10.7675532769,6.0745692359));
#174=IFCCARTESIANPOINT((12.2757459581,8.1552869252,6.0745692359));
#175=IFCCARTESIANPOINT((12.2757459581,5.1389015626,6.0745692359));
#176=IFCCARTESIANPOINT((10.7675532769,2.52663521103,6.0745692359));
#177=IFCCARTESIANPOINT((8.1552869252,1.01844252973,6.0745692359));
#178=IFCCARTESIANPOINT((5.1389015626,1.01844252973,6.0745692359));
#179=IFCCARTESIANPOINT((2.52663521103,2.52663521103,6.0745692359));
#180=IFCCARTESIANPOINT((1.01844252973,5.1389015626,6.0745692359));
#181=IFCCARTESIANPOINT((1.01844252973,8.1552869252,6.0745692359));
#182=IFCCARTESIANPOINT((2.52663521103,10.7675532769,6.0745692359));
#183=IFCCARTESIANPOINT((5.1389015626,12.2757459581,6.0745692359));
#184=IFCCARTESIANPOINT((0.,0.,9.144));
#185=IFCCARTESIANPOINT((93.5794,-106.4206,-9.144));
#186=IFCCARTESIANPOINT((0.39088843829,0.39088843829,0.));
#187=IFCDIRECTION((0.,0.,1.));
#188=IFCDIRECTION((1.,0.,0.));
#189=IFCDIRECTION((0.,1.));
#190=IFCSITE('006_i1V1D0tAKEh_$vljQt',#252,'Site 1','Site 1',$,#60,$,
'Site 1',.ELEMENT.,$,$,0.,$,#243);
#191=IFCRELDEFINESBYPROPERTIES('1RL1EewHTsROrPcNSlBI56',#252,'Project',
$,(#241),#201);
#192=IFCRELDEFINESBYPROPERTIES('3bjMWmv_Cp6LPxmaCnPGwW',#252,'Site',$,(#190),
#202);
#193=IFCRELDEFINESBYPROPERTIES('2zOmgYUoQmhp9sZMdCZF7s',#252,
'Pset_BuildingCommon',$,(#19),#203);
#194=IFCRELDEFINESBYPROPERTIES('3k0v4EdaEvPD8NSrRFB2MT',#252,
'ArchBuilding',$,(#19),#204);
#195=IFCRELDEFINESBYPROPERTIES('3uIgchz4n8nDCCMgq1BBql',#252,
'ObjectIdentity',$,(#19),#205);
#196=IFCRELDEFINESBYPROPERTIES('1CCmZ_1eEDpfMO62t1jkZj',#252,
'ObjectPostalAddress',$,(#19),#206);
#197=IFCRELDEFINESBYPROPERTIES('24I$F7L8gfSwdAGDvfkVEu',#252,
'Pset_BuildingStoreyCommon',$,(#18),#207);
#198=IFCRELDEFINESBYPROPERTIES('1K1DvjfvPzx3hE5w5ZHK0q',#252,
'ArchFloor',$,(#18),#208);
#199=IFCRELDEFINESBYPROPERTIES('0lrK04aZ0v6OfwsyTXPzZq',#252,'Floor',$,
(#18),#209);
#200=IFCRELDEFINESBYPROPERTIES('1fbDyx4oFQKKacC_b9xURq',#252,
'StructuralFloorCommon',$,(#18),#210);
#201=IFCPROPERTYSET('2rcLYSOKrF02Xv$qFtEF4e',#252,'Project',$,(#211));
#202=IFCPROPERTYSET('2Qswsgonw96KTM1$UoUklF',#252,'Site',$,(#212,#213));
#203=IFCPROPERTYSET('2Ct1rxyLMs3vgB_jB90hMr',#252,'Pset_BuildingCommon',
$,(#214));
#204=IFCPROPERTYSET('3OmKLTEF7bgCV0$1Gr7ajZ',#252,'ArchBuilding',$,(#215,
#216));
#205=IFCPROPERTYSET('1HjNcqn7ak9HvLTK8j8w1d',#252,'ObjectIdentity',$,(#217));
#206=IFCPROPERTYSET('306EzywKpRfIeis4axlC1d',#252,'ObjectPostalAddress',
$,(#218,#219,#220,#221));
#207=IFCPROPERTYSET('381uKge8G33qXIbHr84hB4',#252,
'Pset_BuildingStoreyCommon',$,(#222,#223,#224,#225));
#208=IFCPROPERTYSET('0GZmVQOAmJ6zMOk6j3uleN',#252,'ArchFloor',$,(#226,#227,
#228,#229,#230,#231,#232,#233,#234,#235));
#209=IFCPROPERTYSET('0A3C_vQUNRQZfA6f5qtBlE',#252,'Floor',$,(#236,#237,
#238));
#210=IFCPROPERTYSET('0izo3etGuCpkjROydkcZbT',#252,
'StructuralFloorCommon',$,(#239));
#211=IFCPROPERTYSINGLEVALUE('ProjectName',$,IFCTEXT(
'BuildingTemplate_US'),$);
#212=IFCPROPERTYSINGLEVALUE('SiteName',$,IFCTEXT('Site 1'),$);
#213=IFCPROPERTYSINGLEVALUE('BuildingHeightLimit',$,IFCLENGTHMEASURE(0.),
#388);
#214=IFCPROPERTYSINGLEVALUE('YearOfConstruction',$,IFCLABEL('2006'),$);
#215=IFCPROPERTYSINGLEVALUE('BuildingName',$,IFCTEXT('Bldg 1'),$);
#216=IFCPROPERTYSINGLEVALUE('YearConstructed',$,IFCTEXT('2006'),$);
#217=IFCPROPERTYSINGLEVALUE('Description',$,IFCTEXT('3 Story Building'),
$);
#218=IFCPROPERTYSINGLEVALUE('Address1',$,IFCTEXT(
'203 Rickenhouse Drive'),$);
#219=IFCPROPERTYSINGLEVALUE('City',$,IFCTEXT('Madison'),$);
#220=IFCPROPERTYSINGLEVALUE('PostalCode',$,IFCTEXT('35789'),$);
#221=IFCPROPERTYSINGLEVALUE('Country',$,IFCTEXT('US'),$);
#222=IFCPROPERTYSINGLEVALUE('GrossAreaPlanned',$,IFCAREAMEASURE(22296729.6),
#389);
#223=IFCPROPERTYSINGLEVALUE('GrossPlannedArea',$,IFCAREAMEASURE(22296729.6),
$);
#224=IFCPROPERTYSINGLEVALUE('NetAreaPlanned',$,IFCAREAMEASURE(22296729.6),
#389);
#225=IFCPROPERTYSINGLEVALUE('NetPlannedArea',$,IFCAREAMEASURE(22296729.6),
$);
#226=IFCPROPERTYSINGLEVALUE('FloorName',$,IFCTEXT('Floor 3'),$);
#227=IFCPROPERTYSINGLEVALUE('FinishedFloorElevation',$,IFCREAL(9.144),$);
#228=IFCPROPERTYSINGLEVALUE('Description',$,IFCTEXT(
'Administrative and employee lounge'),$);
#229=IFCPROPERTYSINGLEVALUE('ApproxLength',$,IFCLENGTHMEASURE(6096.),#388);
#230=IFCPROPERTYSINGLEVALUE('ApproxWidth',$,IFCLENGTHMEASURE(3657.6),#388);
#231=IFCPROPERTYSINGLEVALUE('TypicalFloor',$,IFCBOOLEAN(.F.),$);
#232=IFCPROPERTYSINGLEVALUE('TypicalFloorHeight',$,IFCLENGTHMEASURE(0.),
#388);
#233=IFCPROPERTYSINGLEVALUE('TypicalFloorBaseElevation',$,
IFCLENGTHMEASURE(0.),#388);
#234=IFCPROPERTYSINGLEVALUE('ACSRotation',$,IFCTEXT('0.00000000'),$);
#235=IFCPROPERTYSINGLEVALUE('Annotation',$,IFCBOOLEAN(.T.),$);
#236=IFCPROPERTYSINGLEVALUE('GrossArea',$,IFCAREAMEASURE(22296729.6),#389);
#237=IFCPROPERTYSINGLEVALUE('NetArea',$,IFCAREAMEASURE(22296729.6),#389);
#238=IFCPROPERTYSINGLEVALUE('OccupancyType',$,IFCTEXT('Commercial'),$);
#239=IFCPROPERTYSINGLEVALUE('issplicefloor',$,IFCBOOLEAN(.F.),$);
#240=IFCUNITASSIGNMENT((#388,#389,#390,#391,#392,#393,#394,#395,#396,#397,
#398,#399,#400,#401,#402,#403,#404,#405,#406,#407,#408,#409,#385,#411,#412,
#413,#414,#415,#416,#254,#255,#256,#257,#258,#259,#260,#261,#262,#263,#264,
#265,#266,#267,#268,#269,#270,#271,#272,#273,#274,#275,#276,#277,#278,#279,
#280,#281,#282,#283,#284,#285,#286,#287,#288,#289,#290,#291,#292,#293,#294,
#295,#296,#297,#253));
#241=IFCPROJECT('1JmBUThP19Z8wED88xGwCF',#252,'BuildingTemplate_US',
'BuildingTemplate_US',$,'BuildingTemplate_US',$,(#13),#240);
#242=IFCPOSTALADDRESS($,$,$,$,('Address 1','Address 2'),$,'City/Town',
'State/Region','Postal Code','Country');
#243=IFCPOSTALADDRESS($,$,$,'Site 1',$,$,$,$,$,$);
#244=IFCPOSTALADDRESS($,$,$,$,('203 Rickenhouse Drive'),$,'Madison',$,
'35789','US');
#245=IFCTELECOMADDRESS($,$,$,('Phone'),$,$,('E-Mail'),$,$);
#246=IFCACTORROLE(.SUPPLIER.,$,$);
#247=IFCPERSON($,'Last Name','First Name',$,$,$,(#246),(#245));
#248=IFCPERSONANDORGANIZATION(#247,#250,$);
#249=IFCORGANIZATION($,'Bentley Systems Inc.',$,$,$);
#250=IFCORGANIZATION($,'Organization Name',$,$,(#242));
#251=IFCAPPLICATION(#249,'***********','OpenBuildings Designer','ABD');
#252=IFCOWNERHISTORY(#248,#251,$,$,0,$,$,1583400360);
#253=IFCMONETARYUNIT('USD');
#254=IFCDERIVEDUNIT((#298,#299),.ACCELERATIONUNIT.,'(METRE)/(SECOND^2)');
#255=IFCDERIVEDUNIT((#300,#301),.ANGULARVELOCITYUNIT.,
'(DEGREE)/(SECOND)');
#256=IFCDERIVEDUNIT((#302,#303),.CURVATUREUNIT.,'(DEGREE)/(METRE)');
#257=IFCDERIVEDUNIT((#304,#305),.DYNAMICVISCOSITYUNIT.,
'(PASCAL)(SECOND)');
#258=IFCDERIVEDUNIT((#306,#307),.HEATFLUXDENSITYUNIT.,
'(WATT)/(METRE^2)');
#259=IFCDERIVEDUNIT((#308,#309),.HEATINGVALUEUNIT.,'(JOULE)/(GRAM)');
#260=IFCDERIVEDUNIT((#310),.INTEGERCOUNTRATEUNIT.,'1/(SECOND)');
#261=IFCDERIVEDUNIT((#311,#312),.IONCONCENTRATIONUNIT.,
'(GRAM)/(CUBIC_METRE)');
#262=IFCDERIVEDUNIT((#313,#314),.ISOTHERMALMOISTURECAPACITYUNIT.,
'(CUBIC_METRE)/(GRAM)');
#263=IFCDERIVEDUNIT((#315,#316),.KINEMATICVISCOSITYUNIT.,
'(SQUARE_METRE)/(SECOND)');
#264=IFCDERIVEDUNIT((#317,#318),.LINEARFORCEUNIT.,'(NEWTON)/(METRE)');
#265=IFCDERIVEDUNIT((#319,#320),.LINEARMOMENTUNIT.,'(NEWTON)/(METRE)');
#266=IFCDERIVEDUNIT((#321,#322),.LINEARSTIFFNESSUNIT.,
'(NEWTON)/(METRE)');
#267=IFCDERIVEDUNIT((#323,#324),.LINEARVELOCITYUNIT.,'(METRE)/(SECOND)');
#268=IFCDERIVEDUNIT((#325,#326),.LUMINOUSINTENSITYDISTRIBUTIONUNIT.,
'(CANDELA)/(LUMEN)');
#269=IFCDERIVEDUNIT((#327,#328),.MASSDENSITYUNIT.,
'(GRAM)/(CUBIC_METRE)');
#270=IFCDERIVEDUNIT((#329,#330),.MASSFLOWRATEUNIT.,'(GRAM)/(SECOND)');
#271=IFCDERIVEDUNIT((#331,#332),.MASSPERLENGTHUNIT.,'(GRAM)/(METRE)');
#272=IFCDERIVEDUNIT((#333,#334),.MODULUSOFELASTICITYUNIT.,
'(NEWTON)/(SQUARE_METRE)');
#273=IFCDERIVEDUNIT((#335,#336),.MODULUSOFLINEARSUBGRADEREACTIONUNIT.,
'(NEWTON)/(METRE^2)');
#274=IFCDERIVEDUNIT((#337,#338),
 .MODULUSOFROTATIONALSUBGRADEREACTIONUNIT.,'(NEWTON)(METRE)');
#275=IFCDERIVEDUNIT((#339,#340),.MODULUSOFSUBGRADEREACTIONUNIT.,
'(NEWTON)/(CUBIC_METRE)');
#276=IFCDERIVEDUNIT((#341,#342),.MOISTUREDIFFUSIVITYUNIT.,
'(CUBIC_METRE)/(SECOND)');
#277=IFCDERIVEDUNIT((#343,#344),.MOLECULARWEIGHTUNIT.,'(GRAM)/(MOLE)');
#278=IFCDERIVEDUNIT((#345,#346),.PLANARFORCEUNIT.,'(NEWTON)/(METRE^2)');
#279=IFCDERIVEDUNIT((#347),.ROTATIONALFREQUENCYUNIT.,'1/(SECOND)');
#280=IFCDERIVEDUNIT((#348,#349),.ROTATIONALMASSUNIT.,'(GRAM)(METRE^2)');
#281=IFCDERIVEDUNIT((#350,#351,#352),.ROTATIONALSTIFFNESSUNIT.,
'(NEWTON)(METRE)/(DEGREE)');
#282=IFCDERIVEDUNIT((#353),.SECTIONAREAINTEGRALUNIT.,'(METRE^5)');
#283=IFCDERIVEDUNIT((#354),.SECTIONMODULUSUNIT.,'(METRE^3)');
#284=IFCDERIVEDUNIT((#355,#356),.SHEARMODULUSUNIT.,
'(NEWTON)/(SQUARE_METRE)');
#285=IFCDERIVEDUNIT((#357,#358),.SOUNDPOWERUNIT.,'(JOULE)/(SECOND)');
#286=IFCDERIVEDUNIT((#359,#360,#361),.SPECIFICHEATCAPACITYUNIT.,
'(NEWTON)/(GRAM)(KELVIN)');
#287=IFCDERIVEDUNIT((#362,#363),.TEMPERATUREGRADIENTUNIT.,
'(KELVIN)/(METRE)');
#288=IFCDERIVEDUNIT((#364,#365,#366),.THERMALADMITTANCEUNIT.,
'(WATT)/(SQUARE_METRE)(KELVIN)');
#289=IFCDERIVEDUNIT((#367,#368,#369),.THERMALCONDUCTANCEUNIT.,
'(WATT)/(METRE)(KELVIN)');
#290=IFCDERIVEDUNIT((#370),.THERMALEXPANSIONCOEFFICIENTUNIT.,
'1/(KELVIN)');
#291=IFCDERIVEDUNIT((#371,#372),.THERMALRESISTANCEUNIT.,
'(SQUARE_METRE)/(WATT)');
#292=IFCDERIVEDUNIT((#373,#374,#375),.THERMALTRANSMITTANCEUNIT.,
'(WATT)/(SQUARE_METRE)(KELVIN)');
#293=IFCDERIVEDUNIT((#376,#377),.TORQUEUNIT.,'(NEWTON)(METRE)');
#294=IFCDERIVEDUNIT((#378,#379),.VAPORPERMEABILITYUNIT.,
'(GRAM)/(SECOND)');
#295=IFCDERIVEDUNIT((#380,#381),.VOLUMETRICFLOWRATEUNIT.,
'(CUBIC_METRE)/(SECOND)');
#296=IFCDERIVEDUNIT((#382),.WARPINGCONSTANTUNIT.,'(METRE^6)');
#297=IFCDERIVEDUNIT((#383,#384),.WARPINGMOMENTUNIT.,'(NEWTON)(METRE^2)');
#298=IFCDERIVEDUNITELEMENT(#388,1);
#299=IFCDERIVEDUNITELEMENT(#416,-2);
#300=IFCDERIVEDUNITELEMENT(#385,1);
#301=IFCDERIVEDUNITELEMENT(#416,-1);
#302=IFCDERIVEDUNITELEMENT(#385,1);
#303=IFCDERIVEDUNITELEMENT(#388,-1);
#304=IFCDERIVEDUNITELEMENT(#412,1);
#305=IFCDERIVEDUNITELEMENT(#416,1);
#306=IFCDERIVEDUNITELEMENT(#411,1);
#307=IFCDERIVEDUNITELEMENT(#388,-2);
#308=IFCDERIVEDUNITELEMENT(#400,1);
#309=IFCDERIVEDUNITELEMENT(#409,-1);
#310=IFCDERIVEDUNITELEMENT(#416,-1);
#311=IFCDERIVEDUNITELEMENT(#409,1);
#312=IFCDERIVEDUNITELEMENT(#390,-1);
#313=IFCDERIVEDUNITELEMENT(#390,1);
#314=IFCDERIVEDUNITELEMENT(#409,-1);
#315=IFCDERIVEDUNITELEMENT(#389,1);
#316=IFCDERIVEDUNITELEMENT(#416,-1);
#317=IFCDERIVEDUNITELEMENT(#401,1);
#318=IFCDERIVEDUNITELEMENT(#388,-1);
#319=IFCDERIVEDUNITELEMENT(#401,1);
#320=IFCDERIVEDUNITELEMENT(#388,-1);
#321=IFCDERIVEDUNITELEMENT(#401,1);
#322=IFCDERIVEDUNITELEMENT(#388,-1);
#323=IFCDERIVEDUNITELEMENT(#388,1);
#324=IFCDERIVEDUNITELEMENT(#416,-1);
#325=IFCDERIVEDUNITELEMENT(#406,1);
#326=IFCDERIVEDUNITELEMENT(#405,-1);
#327=IFCDERIVEDUNITELEMENT(#409,1);
#328=IFCDERIVEDUNITELEMENT(#390,-1);
#329=IFCDERIVEDUNITELEMENT(#409,1);
#330=IFCDERIVEDUNITELEMENT(#416,-1);
#331=IFCDERIVEDUNITELEMENT(#409,1);
#332=IFCDERIVEDUNITELEMENT(#388,-1);
#333=IFCDERIVEDUNITELEMENT(#401,1);
#334=IFCDERIVEDUNITELEMENT(#389,-1);
#335=IFCDERIVEDUNITELEMENT(#401,1);
#336=IFCDERIVEDUNITELEMENT(#388,-2);
#337=IFCDERIVEDUNITELEMENT(#401,1);
#338=IFCDERIVEDUNITELEMENT(#388,1);
#339=IFCDERIVEDUNITELEMENT(#401,1);
#340=IFCDERIVEDUNITELEMENT(#390,-1);
#341=IFCDERIVEDUNITELEMENT(#390,1);
#342=IFCDERIVEDUNITELEMENT(#416,-1);
#343=IFCDERIVEDUNITELEMENT(#409,1);
#344=IFCDERIVEDUNITELEMENT(#392,-1);
#345=IFCDERIVEDUNITELEMENT(#401,1);
#346=IFCDERIVEDUNITELEMENT(#388,-2);
#347=IFCDERIVEDUNITELEMENT(#416,-1);
#348=IFCDERIVEDUNITELEMENT(#409,1);
#349=IFCDERIVEDUNITELEMENT(#388,2);
#350=IFCDERIVEDUNITELEMENT(#401,1);
#351=IFCDERIVEDUNITELEMENT(#388,1);
#352=IFCDERIVEDUNITELEMENT(#385,-1);
#353=IFCDERIVEDUNITELEMENT(#388,5);
#354=IFCDERIVEDUNITELEMENT(#388,3);
#355=IFCDERIVEDUNITELEMENT(#401,1);
#356=IFCDERIVEDUNITELEMENT(#389,-1);
#357=IFCDERIVEDUNITELEMENT(#400,1);
#358=IFCDERIVEDUNITELEMENT(#416,-1);
#359=IFCDERIVEDUNITELEMENT(#401,1);
#360=IFCDERIVEDUNITELEMENT(#409,-1);
#361=IFCDERIVEDUNITELEMENT(#415,-1);
#362=IFCDERIVEDUNITELEMENT(#415,1);
#363=IFCDERIVEDUNITELEMENT(#388,-1);
#364=IFCDERIVEDUNITELEMENT(#411,1);
#365=IFCDERIVEDUNITELEMENT(#389,-1);
#366=IFCDERIVEDUNITELEMENT(#415,-1);
#367=IFCDERIVEDUNITELEMENT(#411,1);
#368=IFCDERIVEDUNITELEMENT(#388,-1);
#369=IFCDERIVEDUNITELEMENT(#415,-1);
#370=IFCDERIVEDUNITELEMENT(#415,-1);
#371=IFCDERIVEDUNITELEMENT(#389,1);
#372=IFCDERIVEDUNITELEMENT(#411,-1);
#373=IFCDERIVEDUNITELEMENT(#411,1);
#374=IFCDERIVEDUNITELEMENT(#389,-1);
#375=IFCDERIVEDUNITELEMENT(#415,-1);
#376=IFCDERIVEDUNITELEMENT(#401,1);
#377=IFCDERIVEDUNITELEMENT(#388,1);
#378=IFCDERIVEDUNITELEMENT(#409,1);
#379=IFCDERIVEDUNITELEMENT(#416,-1);
#380=IFCDERIVEDUNITELEMENT(#390,1);
#381=IFCDERIVEDUNITELEMENT(#416,-1);
#382=IFCDERIVEDUNITELEMENT(#388,6);
#383=IFCDERIVEDUNITELEMENT(#401,1);
#384=IFCDERIVEDUNITELEMENT(#388,2);
#385=IFCCONVERSIONBASEDUNIT(#386,.PLANEANGLEUNIT.,'degree',#387);
#386=IFCDIMENSIONALEXPONENTS(0,0,0,0,0,0,0);
#387=IFCMEASUREWITHUNIT(IFCREAL(0.0174532925199433),#410);
#388=IFCSIUNIT(*,.LENGTHUNIT.,$,.METRE.);
#389=IFCSIUNIT(*,.AREAUNIT.,$,.SQUARE_METRE.);
#390=IFCSIUNIT(*,.VOLUMEUNIT.,$,.CUBIC_METRE.);
#391=IFCSIUNIT(*,.ABSORBEDDOSEUNIT.,$,.GRAY.);
#392=IFCSIUNIT(*,.AMOUNTOFSUBSTANCEUNIT.,$,.MOLE.);
#393=IFCSIUNIT(*,.DOSEEQUIVALENTUNIT.,$,.SIEVERT.);
#394=IFCSIUNIT(*,.ELECTRICCAPACITANCEUNIT.,$,.FARAD.);
#395=IFCSIUNIT(*,.ELECTRICCHARGEUNIT.,$,.COULOMB.);
#396=IFCSIUNIT(*,.ELECTRICCONDUCTANCEUNIT.,$,.SIEMENS.);
#397=IFCSIUNIT(*,.ELECTRICCURRENTUNIT.,$,.AMPERE.);
#398=IFCSIUNIT(*,.ELECTRICRESISTANCEUNIT.,$,.OHM.);
#399=IFCSIUNIT(*,.ELECTRICVOLTAGEUNIT.,$,.VOLT.);
#400=IFCSIUNIT(*,.ENERGYUNIT.,$,.JOULE.);
#401=IFCSIUNIT(*,.FORCEUNIT.,$,.NEWTON.);
#402=IFCSIUNIT(*,.FREQUENCYUNIT.,$,.HERTZ.);
#403=IFCSIUNIT(*,.ILLUMINANCEUNIT.,$,.LUX.);
#404=IFCSIUNIT(*,.INDUCTANCEUNIT.,$,.HENRY.);
#405=IFCSIUNIT(*,.LUMINOUSFLUXUNIT.,$,.LUMEN.);
#406=IFCSIUNIT(*,.LUMINOUSINTENSITYUNIT.,$,.CANDELA.);
#407=IFCSIUNIT(*,.MAGNETICFLUXDENSITYUNIT.,$,.TESLA.);
#408=IFCSIUNIT(*,.MAGNETICFLUXUNIT.,$,.WEBER.);
#409=IFCSIUNIT(*,.MASSUNIT.,$,.GRAM.);
#410=IFCSIUNIT(*,.PLANEANGLEUNIT.,$,.RADIAN.);
#411=IFCSIUNIT(*,.POWERUNIT.,$,.WATT.);
#412=IFCSIUNIT(*,.PRESSUREUNIT.,$,.PASCAL.);
#413=IFCSIUNIT(*,.RADIOACTIVITYUNIT.,$,.BECQUEREL.);
#414=IFCSIUNIT(*,.SOLIDANGLEUNIT.,$,.STERADIAN.);
#415=IFCSIUNIT(*,.THERMODYNAMICTEMPERATUREUNIT.,$,.KELVIN.);
#416=IFCSIUNIT(*,.TIMEUNIT.,$,.SECOND.);
ENDSEC;
END-ISO-10303-21;

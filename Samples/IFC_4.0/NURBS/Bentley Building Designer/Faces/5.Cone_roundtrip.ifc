ISO-10303-21;
HEADER;
FILE_DESCRIPTION(('ViewDefinition [CoordinationView]'),'2;1');
FILE_NAME('','2020-10-30T00:19:03',(),(),'IfcOpenShell 0.6.0b0','IfcOpenShell 0.6.0b0','');
FILE_SCHEMA(('IFC4'));
ENDSEC;
DATA;
#1=IFCBUILDINGSTOREY('0rm7DUqMJhOUcs5v8t1F$L',#18,'Floor 3','Administrative and employee lounge',$,#36,$,'Floor 3',.ELEMENT.,9.144);
#2=IFCBUILDING('3_IkJInE13hR6Xwo4VKOgh',#18,'Bldg 1','3 Story Building',$,#51,$,'Bldg 1',.ELEMENT.,0.,0.,#61);
#3=IFCBUILDINGELEMENTPROXY('2I0Vc5PdWui6aZ1Sc_tP24',#18,'Element type: 24','788*bspline surface!Default',$,#62,#82,'788*bspline surface!Default',.NOTDEFINED.);
#4=IFCSITE('006_i1V1D0tAKEh_$vljQt',#18,'Site 1','Site 1',$,#83,$,'Site 1',.ELEMENT.,$,$,0.,$,#88);
#5=IFCPROPERTYSET('2rcLYSOKrF02Xv$qFtEF4e',#18,'Project',$,(#89));
#6=IFCPROPERTYSET('2Qswsgonw96KTM1$UoUklF',#18,'Site',$,(#90));
#7=IFCPROPERTYSET('2Ct1rxyLMs3vgB_jB90hMr',#18,'Pset_BuildingCommon',$,(#91));
#8=IFCPROPERTYSET('3OmKLTEF7bgCV0$1Gr7ajZ',#18,'ArchBuilding',$,(#92,#93));
#9=IFCPROPERTYSET('1HjNcqn7ak9HvLTK8j8w1d',#18,'ObjectIdentity',$,(#94));
#10=IFCPROPERTYSET('306EzywKpRfIeis4axlC1d',#18,'ObjectPostalAddress',$,(#95,#96,#97,#98));
#11=IFCPROPERTYSET('381uKge8G33qXIbHr84hB4',#18,'Pset_BuildingStoreyCommon',$,(#99,#101,#102,#104));
#12=IFCPROPERTYSET('0GZmVQOAmJ6zMOk6j3uleN',#18,'ArchFloor',$,(#105,#106,#107,#108,#110,#112,#113,#114));
#13=IFCPROPERTYSET('0A3C_vQUNRQZfA6f5qtBlE',#18,'Floor',$,(#115,#117,#119));
#14=IFCPROPERTYSET('0izo3etGuCpkjROydkcZbT',#18,'StructuralFloorCommon',$,(#120));
#15=IFCPROJECT('1JmBUThP19Z8wED88xGwCF',#18,'BuildingTemplate_US','BuildingTemplate_US',$,'BuildingTemplate_US',$,(#21),#121);
#16=IFCSHAPEREPRESENTATION(#19,'Body','SurfaceModel',(#378));
#17=IFCSHAPEREPRESENTATION(#20,'Box','BoundingBox',(#524));
#18=IFCOWNERHISTORY(#526,#532,$,$,0,$,$,1583400506);
#19=IFCGEOMETRICREPRESENTATIONSUBCONTEXT('Body','Model',*,*,*,*,#21,1.,.MODEL_VIEW.,$);
#20=IFCGEOMETRICREPRESENTATIONSUBCONTEXT('Box','Model',*,*,*,*,#21,1.,.SKETCH_VIEW.,$);
#21=IFCGEOMETRICREPRESENTATIONCONTEXT($,'Model',3,1.E-06,#534,#538);
#22=IFCRELCONTAINEDINSPATIALSTRUCTURE('2aJi0CW_wcTJthPGyCEMkK',#18,$,$,(#3),#1);
#23=IFCRELAGGREGATES('1kB3AQvfGpkFm_7uHNRdoP',#18,$,$,#15,(#4));
#24=IFCRELAGGREGATES('0Cp_wDOOr3amQ16IWrGpUy',#18,$,$,#4,(#2));
#25=IFCRELAGGREGATES('1NfacxJUhl1S_ka3CQXVRt',#18,$,$,#2,(#1));
#26=IFCRELDEFINESBYPROPERTIES('1RL1EewHTsROrPcNSlBI56',#18,'Project',$,(#15),#5);
#27=IFCRELDEFINESBYPROPERTIES('3bjMWmv_Cp6LPxmaCnPGwW',#18,'Site',$,(#4),#6);
#28=IFCRELDEFINESBYPROPERTIES('2zOmgYUoQmhp9sZMdCZF7s',#18,'Pset_BuildingCommon',$,(#2),#7);
#29=IFCRELDEFINESBYPROPERTIES('3k0v4EdaEvPD8NSrRFB2MT',#18,'ArchBuilding',$,(#2),#8);
#30=IFCRELDEFINESBYPROPERTIES('3uIgchz4n8nDCCMgq1BBql',#18,'ObjectIdentity',$,(#2),#9);
#31=IFCRELDEFINESBYPROPERTIES('1CCmZ_1eEDpfMO62t1jkZj',#18,'ObjectPostalAddress',$,(#2),#10);
#32=IFCRELDEFINESBYPROPERTIES('24I$F7L8gfSwdAGDvfkVEu',#18,'Pset_BuildingStoreyCommon',$,(#1),#11);
#33=IFCRELDEFINESBYPROPERTIES('1K1DvjfvPzx3hE5w5ZHK0q',#18,'ArchFloor',$,(#1),#12);
#34=IFCRELDEFINESBYPROPERTIES('0lrK04aZ0v6OfwsyTXPzZq',#18,'Floor',$,(#1),#13);
#35=IFCRELDEFINESBYPROPERTIES('1fbDyx4oFQKKacC_b9xURq',#18,'StructuralFloorCommon',$,(#1),#14);
#36=IFCLOCALPLACEMENT(#37,#47);
#37=IFCLOCALPLACEMENT(#38,#43);
#38=IFCLOCALPLACEMENT($,#39);
#39=IFCAXIS2PLACEMENT3D(#40,#41,#42);
#40=IFCCARTESIANPOINT((0.,0.,0.));
#41=IFCDIRECTION((0.,0.,1.));
#42=IFCDIRECTION((1.,0.,0.));
#43=IFCAXIS2PLACEMENT3D(#44,#45,#46);
#44=IFCCARTESIANPOINT((0.,0.,0.));
#45=IFCDIRECTION((0.,0.,1.));
#46=IFCDIRECTION((1.,0.,0.));
#47=IFCAXIS2PLACEMENT3D(#48,#49,#50);
#48=IFCCARTESIANPOINT((0.,0.,9.144));
#49=IFCDIRECTION((0.,0.,1.));
#50=IFCDIRECTION((1.,0.,0.));
#51=IFCLOCALPLACEMENT(#52,#57);
#52=IFCLOCALPLACEMENT($,#53);
#53=IFCAXIS2PLACEMENT3D(#54,#55,#56);
#54=IFCCARTESIANPOINT((0.,0.,0.));
#55=IFCDIRECTION((0.,0.,1.));
#56=IFCDIRECTION((1.,0.,0.));
#57=IFCAXIS2PLACEMENT3D(#58,#59,#60);
#58=IFCCARTESIANPOINT((0.,0.,0.));
#59=IFCDIRECTION((0.,0.,1.));
#60=IFCDIRECTION((1.,0.,0.));
#61=IFCPOSTALADDRESS($,$,$,$,('203 Rickenhouse Drive'),$,'Madison',$,'35789','US');
#62=IFCLOCALPLACEMENT(#63,#78);
#63=IFCLOCALPLACEMENT(#64,#74);
#64=IFCLOCALPLACEMENT(#65,#70);
#65=IFCLOCALPLACEMENT($,#66);
#66=IFCAXIS2PLACEMENT3D(#67,#68,#69);
#67=IFCCARTESIANPOINT((0.,0.,0.));
#68=IFCDIRECTION((0.,0.,1.));
#69=IFCDIRECTION((1.,0.,0.));
#70=IFCAXIS2PLACEMENT3D(#71,#72,#73);
#71=IFCCARTESIANPOINT((0.,0.,0.));
#72=IFCDIRECTION((0.,0.,1.));
#73=IFCDIRECTION((1.,0.,0.));
#74=IFCAXIS2PLACEMENT3D(#75,#76,#77);
#75=IFCCARTESIANPOINT((0.,0.,9.144));
#76=IFCDIRECTION((0.,0.,1.));
#77=IFCDIRECTION((1.,0.,0.));
#78=IFCAXIS2PLACEMENT3D(#79,#80,#81);
#79=IFCCARTESIANPOINT((71.711,-83.9857,-9.1441));
#80=IFCDIRECTION((0.,0.,1.));
#81=IFCDIRECTION((1.,0.,0.));
#82=IFCPRODUCTDEFINITIONSHAPE($,$,(#16,#17));
#83=IFCLOCALPLACEMENT($,#84);
#84=IFCAXIS2PLACEMENT3D(#85,#86,#87);
#85=IFCCARTESIANPOINT((0.,0.,0.));
#86=IFCDIRECTION((0.,0.,1.));
#87=IFCDIRECTION((1.,0.,0.));
#88=IFCPOSTALADDRESS($,$,$,'Site 1',$,$,$,$,$,$);
#89=IFCPROPERTYSINGLEVALUE('ProjectName',$,IFCTEXT('BuildingTemplate_US'),$);
#90=IFCPROPERTYSINGLEVALUE('SiteName',$,IFCTEXT('Site 1'),$);
#91=IFCPROPERTYSINGLEVALUE('YearOfConstruction',$,IFCLABEL('2006'),$);
#92=IFCPROPERTYSINGLEVALUE('BuildingName',$,IFCTEXT('Bldg 1'),$);
#93=IFCPROPERTYSINGLEVALUE('YearConstructed',$,IFCTEXT('2006'),$);
#94=IFCPROPERTYSINGLEVALUE('Description',$,IFCTEXT('3 Story Building'),$);
#95=IFCPROPERTYSINGLEVALUE('Address1',$,IFCTEXT('203 Rickenhouse Drive'),$);
#96=IFCPROPERTYSINGLEVALUE('City',$,IFCTEXT('Madison'),$);
#97=IFCPROPERTYSINGLEVALUE('PostalCode',$,IFCTEXT('35789'),$);
#98=IFCPROPERTYSINGLEVALUE('Country',$,IFCTEXT('US'),$);
#99=IFCPROPERTYSINGLEVALUE('GrossAreaPlanned',$,IFCAREAMEASURE(22296729.6),#100);
#100=IFCSIUNIT(*,.AREAUNIT.,$,.SQUARE_METRE.);
#101=IFCPROPERTYSINGLEVALUE('GrossPlannedArea',$,IFCAREAMEASURE(22296729.6),$);
#102=IFCPROPERTYSINGLEVALUE('NetAreaPlanned',$,IFCAREAMEASURE(22296729.6),#103);
#103=IFCSIUNIT(*,.AREAUNIT.,$,.SQUARE_METRE.);
#104=IFCPROPERTYSINGLEVALUE('NetPlannedArea',$,IFCAREAMEASURE(22296729.6),$);
#105=IFCPROPERTYSINGLEVALUE('FloorName',$,IFCTEXT('Floor 3'),$);
#106=IFCPROPERTYSINGLEVALUE('FinishedFloorElevation',$,IFCREAL(9.144),$);
#107=IFCPROPERTYSINGLEVALUE('Description',$,IFCTEXT('Administrative and employee lounge'),$);
#108=IFCPROPERTYSINGLEVALUE('ApproxLength',$,IFCLENGTHMEASURE(6096.),#109);
#109=IFCSIUNIT(*,.LENGTHUNIT.,$,.METRE.);
#110=IFCPROPERTYSINGLEVALUE('ApproxWidth',$,IFCLENGTHMEASURE(3657.6),#111);
#111=IFCSIUNIT(*,.LENGTHUNIT.,$,.METRE.);
#112=IFCPROPERTYSINGLEVALUE('TypicalFloor',$,IFCBOOLEAN(.F.),$);
#113=IFCPROPERTYSINGLEVALUE('ACSRotation',$,IFCTEXT('0.00000000'),$);
#114=IFCPROPERTYSINGLEVALUE('Annotation',$,IFCBOOLEAN(.T.),$);
#115=IFCPROPERTYSINGLEVALUE('GrossArea',$,IFCAREAMEASURE(22296729.6),#116);
#116=IFCSIUNIT(*,.AREAUNIT.,$,.SQUARE_METRE.);
#117=IFCPROPERTYSINGLEVALUE('NetArea',$,IFCAREAMEASURE(22296729.6),#118);
#118=IFCSIUNIT(*,.AREAUNIT.,$,.SQUARE_METRE.);
#119=IFCPROPERTYSINGLEVALUE('OccupancyType',$,IFCTEXT('Commercial'),$);
#120=IFCPROPERTYSINGLEVALUE('issplicefloor',$,IFCBOOLEAN(.F.),$);
#121=IFCUNITASSIGNMENT((#122,#123,#124,#125,#126,#127,#128,#129,#130,#131,#132,#133,#134,#135,#136,#137,#138,#139,#140,#141,#142,#143,#144,#147,#148,#149,#150,#151,#152,#153,#158,#165,#172,#177,#182,#187,#190,#195,#200,#205,#210,#215,#220,#225,#230,#235,#240,#245,#250,#255,#260,#265,#270,#275,#280,#283,#288,#297,#300,#303,#308,#313,#320,#325,#332,#339,#342,#347,#354,#359,#364,#369,#372,#377));
#122=IFCSIUNIT(*,.LENGTHUNIT.,$,.METRE.);
#123=IFCSIUNIT(*,.AREAUNIT.,$,.SQUARE_METRE.);
#124=IFCSIUNIT(*,.VOLUMEUNIT.,$,.CUBIC_METRE.);
#125=IFCSIUNIT(*,.ABSORBEDDOSEUNIT.,$,.GRAY.);
#126=IFCSIUNIT(*,.AMOUNTOFSUBSTANCEUNIT.,$,.MOLE.);
#127=IFCSIUNIT(*,.DOSEEQUIVALENTUNIT.,$,.SIEVERT.);
#128=IFCSIUNIT(*,.ELECTRICCAPACITANCEUNIT.,$,.FARAD.);
#129=IFCSIUNIT(*,.ELECTRICCHARGEUNIT.,$,.COULOMB.);
#130=IFCSIUNIT(*,.ELECTRICCONDUCTANCEUNIT.,$,.SIEMENS.);
#131=IFCSIUNIT(*,.ELECTRICCURRENTUNIT.,$,.AMPERE.);
#132=IFCSIUNIT(*,.ELECTRICRESISTANCEUNIT.,$,.OHM.);
#133=IFCSIUNIT(*,.ELECTRICVOLTAGEUNIT.,$,.VOLT.);
#134=IFCSIUNIT(*,.ENERGYUNIT.,$,.JOULE.);
#135=IFCSIUNIT(*,.FORCEUNIT.,$,.NEWTON.);
#136=IFCSIUNIT(*,.FREQUENCYUNIT.,$,.HERTZ.);
#137=IFCSIUNIT(*,.ILLUMINANCEUNIT.,$,.LUX.);
#138=IFCSIUNIT(*,.INDUCTANCEUNIT.,$,.HENRY.);
#139=IFCSIUNIT(*,.LUMINOUSFLUXUNIT.,$,.LUMEN.);
#140=IFCSIUNIT(*,.LUMINOUSINTENSITYUNIT.,$,.CANDELA.);
#141=IFCSIUNIT(*,.MAGNETICFLUXDENSITYUNIT.,$,.TESLA.);
#142=IFCSIUNIT(*,.MAGNETICFLUXUNIT.,$,.WEBER.);
#143=IFCSIUNIT(*,.MASSUNIT.,$,.GRAM.);
#144=IFCCONVERSIONBASEDUNIT($,.PLANEANGLEUNIT.,'degree',#145);
#145=IFCMEASUREWITHUNIT(IFCREAL(0.0174532925199433),#146);
#146=IFCSIUNIT(*,.PLANEANGLEUNIT.,$,.RADIAN.);
#147=IFCSIUNIT(*,.POWERUNIT.,$,.WATT.);
#148=IFCSIUNIT(*,.PRESSUREUNIT.,$,.PASCAL.);
#149=IFCSIUNIT(*,.RADIOACTIVITYUNIT.,$,.BECQUEREL.);
#150=IFCSIUNIT(*,.SOLIDANGLEUNIT.,$,.STERADIAN.);
#151=IFCSIUNIT(*,.THERMODYNAMICTEMPERATUREUNIT.,$,.KELVIN.);
#152=IFCSIUNIT(*,.TIMEUNIT.,$,.SECOND.);
#153=IFCDERIVEDUNIT((#154,#156),.ACCELERATIONUNIT.,'(METRE)/(SECOND^2)');
#154=IFCDERIVEDUNITELEMENT(#155,1);
#155=IFCSIUNIT(*,.LENGTHUNIT.,$,.METRE.);
#156=IFCDERIVEDUNITELEMENT(#157,-2);
#157=IFCSIUNIT(*,.TIMEUNIT.,$,.SECOND.);
#158=IFCDERIVEDUNIT((#159,#163),.ANGULARVELOCITYUNIT.,'(DEGREE)/(SECOND)');
#159=IFCDERIVEDUNITELEMENT(#160,1);
#160=IFCCONVERSIONBASEDUNIT($,.PLANEANGLEUNIT.,'degree',#161);
#161=IFCMEASUREWITHUNIT(IFCREAL(0.0174532925199433),#162);
#162=IFCSIUNIT(*,.PLANEANGLEUNIT.,$,.RADIAN.);
#163=IFCDERIVEDUNITELEMENT(#164,-1);
#164=IFCSIUNIT(*,.TIMEUNIT.,$,.SECOND.);
#165=IFCDERIVEDUNIT((#166,#170),.CURVATUREUNIT.,'(DEGREE)/(METRE)');
#166=IFCDERIVEDUNITELEMENT(#167,1);
#167=IFCCONVERSIONBASEDUNIT($,.PLANEANGLEUNIT.,'degree',#168);
#168=IFCMEASUREWITHUNIT(IFCREAL(0.0174532925199433),#169);
#169=IFCSIUNIT(*,.PLANEANGLEUNIT.,$,.RADIAN.);
#170=IFCDERIVEDUNITELEMENT(#171,-1);
#171=IFCSIUNIT(*,.LENGTHUNIT.,$,.METRE.);
#172=IFCDERIVEDUNIT((#173,#175),.DYNAMICVISCOSITYUNIT.,'(PASCAL)(SECOND)');
#173=IFCDERIVEDUNITELEMENT(#174,1);
#174=IFCSIUNIT(*,.PRESSUREUNIT.,$,.PASCAL.);
#175=IFCDERIVEDUNITELEMENT(#176,1);
#176=IFCSIUNIT(*,.TIMEUNIT.,$,.SECOND.);
#177=IFCDERIVEDUNIT((#178,#180),.HEATFLUXDENSITYUNIT.,'(WATT)/(METRE^2)');
#178=IFCDERIVEDUNITELEMENT(#179,1);
#179=IFCSIUNIT(*,.POWERUNIT.,$,.WATT.);
#180=IFCDERIVEDUNITELEMENT(#181,-2);
#181=IFCSIUNIT(*,.LENGTHUNIT.,$,.METRE.);
#182=IFCDERIVEDUNIT((#183,#185),.HEATINGVALUEUNIT.,'(JOULE)/(GRAM)');
#183=IFCDERIVEDUNITELEMENT(#184,1);
#184=IFCSIUNIT(*,.ENERGYUNIT.,$,.JOULE.);
#185=IFCDERIVEDUNITELEMENT(#186,-1);
#186=IFCSIUNIT(*,.MASSUNIT.,$,.GRAM.);
#187=IFCDERIVEDUNIT((#188),.INTEGERCOUNTRATEUNIT.,'1/(SECOND)');
#188=IFCDERIVEDUNITELEMENT(#189,-1);
#189=IFCSIUNIT(*,.TIMEUNIT.,$,.SECOND.);
#190=IFCDERIVEDUNIT((#191,#193),.IONCONCENTRATIONUNIT.,'(GRAM)/(CUBIC_METRE)');
#191=IFCDERIVEDUNITELEMENT(#192,1);
#192=IFCSIUNIT(*,.MASSUNIT.,$,.GRAM.);
#193=IFCDERIVEDUNITELEMENT(#194,-1);
#194=IFCSIUNIT(*,.VOLUMEUNIT.,$,.CUBIC_METRE.);
#195=IFCDERIVEDUNIT((#196,#198),.ISOTHERMALMOISTURECAPACITYUNIT.,'(CUBIC_METRE)/(GRAM)');
#196=IFCDERIVEDUNITELEMENT(#197,1);
#197=IFCSIUNIT(*,.VOLUMEUNIT.,$,.CUBIC_METRE.);
#198=IFCDERIVEDUNITELEMENT(#199,-1);
#199=IFCSIUNIT(*,.MASSUNIT.,$,.GRAM.);
#200=IFCDERIVEDUNIT((#201,#203),.KINEMATICVISCOSITYUNIT.,'(SQUARE_METRE)/(SECOND)');
#201=IFCDERIVEDUNITELEMENT(#202,1);
#202=IFCSIUNIT(*,.AREAUNIT.,$,.SQUARE_METRE.);
#203=IFCDERIVEDUNITELEMENT(#204,-1);
#204=IFCSIUNIT(*,.TIMEUNIT.,$,.SECOND.);
#205=IFCDERIVEDUNIT((#206,#208),.LINEARFORCEUNIT.,'(NEWTON)/(METRE)');
#206=IFCDERIVEDUNITELEMENT(#207,1);
#207=IFCSIUNIT(*,.FORCEUNIT.,$,.NEWTON.);
#208=IFCDERIVEDUNITELEMENT(#209,-1);
#209=IFCSIUNIT(*,.LENGTHUNIT.,$,.METRE.);
#210=IFCDERIVEDUNIT((#211,#213),.LINEARMOMENTUNIT.,'(NEWTON)/(METRE)');
#211=IFCDERIVEDUNITELEMENT(#212,1);
#212=IFCSIUNIT(*,.FORCEUNIT.,$,.NEWTON.);
#213=IFCDERIVEDUNITELEMENT(#214,-1);
#214=IFCSIUNIT(*,.LENGTHUNIT.,$,.METRE.);
#215=IFCDERIVEDUNIT((#216,#218),.LINEARSTIFFNESSUNIT.,'(NEWTON)/(METRE)');
#216=IFCDERIVEDUNITELEMENT(#217,1);
#217=IFCSIUNIT(*,.FORCEUNIT.,$,.NEWTON.);
#218=IFCDERIVEDUNITELEMENT(#219,-1);
#219=IFCSIUNIT(*,.LENGTHUNIT.,$,.METRE.);
#220=IFCDERIVEDUNIT((#221,#223),.LINEARVELOCITYUNIT.,'(METRE)/(SECOND)');
#221=IFCDERIVEDUNITELEMENT(#222,1);
#222=IFCSIUNIT(*,.LENGTHUNIT.,$,.METRE.);
#223=IFCDERIVEDUNITELEMENT(#224,-1);
#224=IFCSIUNIT(*,.TIMEUNIT.,$,.SECOND.);
#225=IFCDERIVEDUNIT((#226,#228),.LUMINOUSINTENSITYDISTRIBUTIONUNIT.,'(CANDELA)/(LUMEN)');
#226=IFCDERIVEDUNITELEMENT(#227,1);
#227=IFCSIUNIT(*,.LUMINOUSINTENSITYUNIT.,$,.CANDELA.);
#228=IFCDERIVEDUNITELEMENT(#229,-1);
#229=IFCSIUNIT(*,.LUMINOUSFLUXUNIT.,$,.LUMEN.);
#230=IFCDERIVEDUNIT((#231,#233),.MASSDENSITYUNIT.,'(GRAM)/(CUBIC_METRE)');
#231=IFCDERIVEDUNITELEMENT(#232,1);
#232=IFCSIUNIT(*,.MASSUNIT.,$,.GRAM.);
#233=IFCDERIVEDUNITELEMENT(#234,-1);
#234=IFCSIUNIT(*,.VOLUMEUNIT.,$,.CUBIC_METRE.);
#235=IFCDERIVEDUNIT((#236,#238),.MASSFLOWRATEUNIT.,'(GRAM)/(SECOND)');
#236=IFCDERIVEDUNITELEMENT(#237,1);
#237=IFCSIUNIT(*,.MASSUNIT.,$,.GRAM.);
#238=IFCDERIVEDUNITELEMENT(#239,-1);
#239=IFCSIUNIT(*,.TIMEUNIT.,$,.SECOND.);
#240=IFCDERIVEDUNIT((#241,#243),.MASSPERLENGTHUNIT.,'(GRAM)/(METRE)');
#241=IFCDERIVEDUNITELEMENT(#242,1);
#242=IFCSIUNIT(*,.MASSUNIT.,$,.GRAM.);
#243=IFCDERIVEDUNITELEMENT(#244,-1);
#244=IFCSIUNIT(*,.LENGTHUNIT.,$,.METRE.);
#245=IFCDERIVEDUNIT((#246,#248),.MODULUSOFELASTICITYUNIT.,'(NEWTON)/(SQUARE_METRE)');
#246=IFCDERIVEDUNITELEMENT(#247,1);
#247=IFCSIUNIT(*,.FORCEUNIT.,$,.NEWTON.);
#248=IFCDERIVEDUNITELEMENT(#249,-1);
#249=IFCSIUNIT(*,.AREAUNIT.,$,.SQUARE_METRE.);
#250=IFCDERIVEDUNIT((#251,#253),.MODULUSOFLINEARSUBGRADEREACTIONUNIT.,'(NEWTON)/(METRE^2)');
#251=IFCDERIVEDUNITELEMENT(#252,1);
#252=IFCSIUNIT(*,.FORCEUNIT.,$,.NEWTON.);
#253=IFCDERIVEDUNITELEMENT(#254,-2);
#254=IFCSIUNIT(*,.LENGTHUNIT.,$,.METRE.);
#255=IFCDERIVEDUNIT((#256,#258),.MODULUSOFROTATIONALSUBGRADEREACTIONUNIT.,'(NEWTON)(METRE)');
#256=IFCDERIVEDUNITELEMENT(#257,1);
#257=IFCSIUNIT(*,.FORCEUNIT.,$,.NEWTON.);
#258=IFCDERIVEDUNITELEMENT(#259,1);
#259=IFCSIUNIT(*,.LENGTHUNIT.,$,.METRE.);
#260=IFCDERIVEDUNIT((#261,#263),.MODULUSOFSUBGRADEREACTIONUNIT.,'(NEWTON)/(CUBIC_METRE)');
#261=IFCDERIVEDUNITELEMENT(#262,1);
#262=IFCSIUNIT(*,.FORCEUNIT.,$,.NEWTON.);
#263=IFCDERIVEDUNITELEMENT(#264,-1);
#264=IFCSIUNIT(*,.VOLUMEUNIT.,$,.CUBIC_METRE.);
#265=IFCDERIVEDUNIT((#266,#268),.MOISTUREDIFFUSIVITYUNIT.,'(CUBIC_METRE)/(SECOND)');
#266=IFCDERIVEDUNITELEMENT(#267,1);
#267=IFCSIUNIT(*,.VOLUMEUNIT.,$,.CUBIC_METRE.);
#268=IFCDERIVEDUNITELEMENT(#269,-1);
#269=IFCSIUNIT(*,.TIMEUNIT.,$,.SECOND.);
#270=IFCDERIVEDUNIT((#271,#273),.MOLECULARWEIGHTUNIT.,'(GRAM)/(MOLE)');
#271=IFCDERIVEDUNITELEMENT(#272,1);
#272=IFCSIUNIT(*,.MASSUNIT.,$,.GRAM.);
#273=IFCDERIVEDUNITELEMENT(#274,-1);
#274=IFCSIUNIT(*,.AMOUNTOFSUBSTANCEUNIT.,$,.MOLE.);
#275=IFCDERIVEDUNIT((#276,#278),.PLANARFORCEUNIT.,'(NEWTON)/(METRE^2)');
#276=IFCDERIVEDUNITELEMENT(#277,1);
#277=IFCSIUNIT(*,.FORCEUNIT.,$,.NEWTON.);
#278=IFCDERIVEDUNITELEMENT(#279,-2);
#279=IFCSIUNIT(*,.LENGTHUNIT.,$,.METRE.);
#280=IFCDERIVEDUNIT((#281),.ROTATIONALFREQUENCYUNIT.,'1/(SECOND)');
#281=IFCDERIVEDUNITELEMENT(#282,-1);
#282=IFCSIUNIT(*,.TIMEUNIT.,$,.SECOND.);
#283=IFCDERIVEDUNIT((#284,#286),.ROTATIONALMASSUNIT.,'(GRAM)(METRE^2)');
#284=IFCDERIVEDUNITELEMENT(#285,1);
#285=IFCSIUNIT(*,.MASSUNIT.,$,.GRAM.);
#286=IFCDERIVEDUNITELEMENT(#287,2);
#287=IFCSIUNIT(*,.LENGTHUNIT.,$,.METRE.);
#288=IFCDERIVEDUNIT((#289,#291,#293),.ROTATIONALSTIFFNESSUNIT.,'(NEWTON)(METRE)/(DEGREE)');
#289=IFCDERIVEDUNITELEMENT(#290,1);
#290=IFCSIUNIT(*,.FORCEUNIT.,$,.NEWTON.);
#291=IFCDERIVEDUNITELEMENT(#292,1);
#292=IFCSIUNIT(*,.LENGTHUNIT.,$,.METRE.);
#293=IFCDERIVEDUNITELEMENT(#294,-1);
#294=IFCCONVERSIONBASEDUNIT($,.PLANEANGLEUNIT.,'degree',#295);
#295=IFCMEASUREWITHUNIT(IFCREAL(0.0174532925199433),#296);
#296=IFCSIUNIT(*,.PLANEANGLEUNIT.,$,.RADIAN.);
#297=IFCDERIVEDUNIT((#298),.SECTIONAREAINTEGRALUNIT.,'(METRE^5)');
#298=IFCDERIVEDUNITELEMENT(#299,5);
#299=IFCSIUNIT(*,.LENGTHUNIT.,$,.METRE.);
#300=IFCDERIVEDUNIT((#301),.SECTIONMODULUSUNIT.,'(METRE^3)');
#301=IFCDERIVEDUNITELEMENT(#302,3);
#302=IFCSIUNIT(*,.LENGTHUNIT.,$,.METRE.);
#303=IFCDERIVEDUNIT((#304,#306),.SHEARMODULUSUNIT.,'(NEWTON)/(SQUARE_METRE)');
#304=IFCDERIVEDUNITELEMENT(#305,1);
#305=IFCSIUNIT(*,.FORCEUNIT.,$,.NEWTON.);
#306=IFCDERIVEDUNITELEMENT(#307,-1);
#307=IFCSIUNIT(*,.AREAUNIT.,$,.SQUARE_METRE.);
#308=IFCDERIVEDUNIT((#309,#311),.SOUNDPOWERUNIT.,'(JOULE)/(SECOND)');
#309=IFCDERIVEDUNITELEMENT(#310,1);
#310=IFCSIUNIT(*,.ENERGYUNIT.,$,.JOULE.);
#311=IFCDERIVEDUNITELEMENT(#312,-1);
#312=IFCSIUNIT(*,.TIMEUNIT.,$,.SECOND.);
#313=IFCDERIVEDUNIT((#314,#316,#318),.SPECIFICHEATCAPACITYUNIT.,'(NEWTON)/(GRAM)(KELVIN)');
#314=IFCDERIVEDUNITELEMENT(#315,1);
#315=IFCSIUNIT(*,.FORCEUNIT.,$,.NEWTON.);
#316=IFCDERIVEDUNITELEMENT(#317,-1);
#317=IFCSIUNIT(*,.MASSUNIT.,$,.GRAM.);
#318=IFCDERIVEDUNITELEMENT(#319,-1);
#319=IFCSIUNIT(*,.THERMODYNAMICTEMPERATUREUNIT.,$,.KELVIN.);
#320=IFCDERIVEDUNIT((#321,#323),.TEMPERATUREGRADIENTUNIT.,'(KELVIN)/(METRE)');
#321=IFCDERIVEDUNITELEMENT(#322,1);
#322=IFCSIUNIT(*,.THERMODYNAMICTEMPERATUREUNIT.,$,.KELVIN.);
#323=IFCDERIVEDUNITELEMENT(#324,-1);
#324=IFCSIUNIT(*,.LENGTHUNIT.,$,.METRE.);
#325=IFCDERIVEDUNIT((#326,#328,#330),.THERMALADMITTANCEUNIT.,'(WATT)/(SQUARE_METRE)(KELVIN)');
#326=IFCDERIVEDUNITELEMENT(#327,1);
#327=IFCSIUNIT(*,.POWERUNIT.,$,.WATT.);
#328=IFCDERIVEDUNITELEMENT(#329,-1);
#329=IFCSIUNIT(*,.AREAUNIT.,$,.SQUARE_METRE.);
#330=IFCDERIVEDUNITELEMENT(#331,-1);
#331=IFCSIUNIT(*,.THERMODYNAMICTEMPERATUREUNIT.,$,.KELVIN.);
#332=IFCDERIVEDUNIT((#333,#335,#337),.THERMALCONDUCTANCEUNIT.,'(WATT)/(METRE)(KELVIN)');
#333=IFCDERIVEDUNITELEMENT(#334,1);
#334=IFCSIUNIT(*,.POWERUNIT.,$,.WATT.);
#335=IFCDERIVEDUNITELEMENT(#336,-1);
#336=IFCSIUNIT(*,.LENGTHUNIT.,$,.METRE.);
#337=IFCDERIVEDUNITELEMENT(#338,-1);
#338=IFCSIUNIT(*,.THERMODYNAMICTEMPERATUREUNIT.,$,.KELVIN.);
#339=IFCDERIVEDUNIT((#340),.THERMALEXPANSIONCOEFFICIENTUNIT.,'1/(KELVIN)');
#340=IFCDERIVEDUNITELEMENT(#341,-1);
#341=IFCSIUNIT(*,.THERMODYNAMICTEMPERATUREUNIT.,$,.KELVIN.);
#342=IFCDERIVEDUNIT((#343,#345),.THERMALRESISTANCEUNIT.,'(SQUARE_METRE)/(WATT)');
#343=IFCDERIVEDUNITELEMENT(#344,1);
#344=IFCSIUNIT(*,.AREAUNIT.,$,.SQUARE_METRE.);
#345=IFCDERIVEDUNITELEMENT(#346,-1);
#346=IFCSIUNIT(*,.POWERUNIT.,$,.WATT.);
#347=IFCDERIVEDUNIT((#348,#350,#352),.THERMALTRANSMITTANCEUNIT.,'(WATT)/(SQUARE_METRE)(KELVIN)');
#348=IFCDERIVEDUNITELEMENT(#349,1);
#349=IFCSIUNIT(*,.POWERUNIT.,$,.WATT.);
#350=IFCDERIVEDUNITELEMENT(#351,-1);
#351=IFCSIUNIT(*,.AREAUNIT.,$,.SQUARE_METRE.);
#352=IFCDERIVEDUNITELEMENT(#353,-1);
#353=IFCSIUNIT(*,.THERMODYNAMICTEMPERATUREUNIT.,$,.KELVIN.);
#354=IFCDERIVEDUNIT((#355,#357),.TORQUEUNIT.,'(NEWTON)(METRE)');
#355=IFCDERIVEDUNITELEMENT(#356,1);
#356=IFCSIUNIT(*,.FORCEUNIT.,$,.NEWTON.);
#357=IFCDERIVEDUNITELEMENT(#358,1);
#358=IFCSIUNIT(*,.LENGTHUNIT.,$,.METRE.);
#359=IFCDERIVEDUNIT((#360,#362),.VAPORPERMEABILITYUNIT.,'(GRAM)/(SECOND)');
#360=IFCDERIVEDUNITELEMENT(#361,1);
#361=IFCSIUNIT(*,.MASSUNIT.,$,.GRAM.);
#362=IFCDERIVEDUNITELEMENT(#363,-1);
#363=IFCSIUNIT(*,.TIMEUNIT.,$,.SECOND.);
#364=IFCDERIVEDUNIT((#365,#367),.VOLUMETRICFLOWRATEUNIT.,'(CUBIC_METRE)/(SECOND)');
#365=IFCDERIVEDUNITELEMENT(#366,1);
#366=IFCSIUNIT(*,.VOLUMEUNIT.,$,.CUBIC_METRE.);
#367=IFCDERIVEDUNITELEMENT(#368,-1);
#368=IFCSIUNIT(*,.TIMEUNIT.,$,.SECOND.);
#369=IFCDERIVEDUNIT((#370),.WARPINGCONSTANTUNIT.,'(METRE^6)');
#370=IFCDERIVEDUNITELEMENT(#371,6);
#371=IFCSIUNIT(*,.LENGTHUNIT.,$,.METRE.);
#372=IFCDERIVEDUNIT((#373,#375),.WARPINGMOMENTUNIT.,'(NEWTON)(METRE^2)');
#373=IFCDERIVEDUNITELEMENT(#374,1);
#374=IFCSIUNIT(*,.FORCEUNIT.,$,.NEWTON.);
#375=IFCDERIVEDUNITELEMENT(#376,2);
#376=IFCSIUNIT(*,.LENGTHUNIT.,$,.METRE.);
#377=IFCMONETARYUNIT('USD');
#378=IFCSHELLBASEDSURFACEMODEL((#379));
#379=IFCOPENSHELL((#380));
#380=IFCADVANCEDFACE((#381),#509,.T.);
#381=IFCFACEOUTERBOUND(#382,.T.);
#382=IFCEDGELOOP((#383,#417,#446,#480));
#383=IFCORIENTEDEDGE(*,*,#384,.T.);
#384=IFCEDGECURVE(#385,#387,#389,.T.);
#385=IFCVERTEXPOINT(#386);
#386=IFCCARTESIANPOINT((7.7818,8.9856996494,13.4785));
#387=IFCVERTEXPOINT(#388);
#388=IFCCARTESIANPOINT((7.7818,8.9856996494,13.4785));
#389=IFCSURFACECURVE(#390,(#398),.PCURVE_S1.);
#390=IFCRATIONALBSPLINECURVEWITHKNOTS(2,(#391,#392,#393,#394,#395,#396,#397),.UNSPECIFIED.,.T.,.F.,(3,2,2,3),(0.,0.333333333333333,0.666666666666667,1.),.UNSPECIFIED.,(1.,0.5,1.,0.5,1.,0.5,1.));
#391=IFCCARTESIANPOINT((7.7818,8.9856996494,13.4785));
#392=IFCCARTESIANPOINT((15.5636,17.9713992988,26.957));
#393=IFCCARTESIANPOINT((7.7818,8.9856996494,13.4785));
#394=IFCCARTESIANPOINT((15.5636,17.9713992988,26.957));
#395=IFCCARTESIANPOINT((7.7818,8.9856996494,13.4785));
#396=IFCCARTESIANPOINT((15.5636,17.9713992988,26.957));
#397=IFCCARTESIANPOINT((7.7818,8.9856996494,13.4785));
#398=IFCPCURVE(#399,#414);
#399=IFCRATIONALBSPLINESURFACEWITHKNOTS(2,1,((#400,#401),(#402,#403),(#404,#405),(#406,#407),(#408,#409),(#410,#411),(#412,#413)),.UNSPECIFIED.,.T.,.F.,.F.,(3,2,2,3),(2,2),(0.,0.333333333333333,0.666666666666667,1.),(0.,1.),.UNSPECIFIED.,((1.,1.),(0.5,0.5),(1.,1.),(0.5,0.5),(1.,1.),(0.5,0.5),(1.,1.)));
#400=IFCCARTESIANPOINT((7.7818,8.9856996494,13.4785));
#401=IFCCARTESIANPOINT((7.7818,13.4784996494,0.000100000000035));
#402=IFCCARTESIANPOINT((7.7818,8.9856996494,13.4785));
#403=IFCCARTESIANPOINT((4.2131754709E-05,13.4784996494,9.999999993E-05));
#404=IFCCARTESIANPOINT((7.7818,8.9856996494,13.4785));
#405=IFCCARTESIANPOINT((3.8909210659,6.7392996494,0.000100000000035));
#406=IFCCARTESIANPOINT((7.7818,8.9856996494,13.4785));
#407=IFCCARTESIANPOINT((7.7818,9.9649401894E-05,0.00010000000007));
#408=IFCCARTESIANPOINT((7.7818,8.9856996494,13.4785));
#409=IFCCARTESIANPOINT((11.6726789341,6.7392996494,0.000100000000035));
#410=IFCCARTESIANPOINT((7.7818,8.9856996494,13.4785));
#411=IFCCARTESIANPOINT((15.5635578682,13.4784996494,0.000100000000035));
#412=IFCCARTESIANPOINT((7.7818,8.9856996494,13.4785));
#413=IFCCARTESIANPOINT((7.7818,13.4784996494,0.000100000000035));
#414=IFCPOLYLINE((#415,#416));
#415=IFCCARTESIANPOINT((0.,0.));
#416=IFCCARTESIANPOINT((0.0001,0.));
#417=IFCORIENTEDEDGE(*,*,#418,.T.);
#418=IFCEDGECURVE(#419,#421,#423,.T.);
#419=IFCVERTEXPOINT(#420);
#420=IFCCARTESIANPOINT((7.7818,8.9856996494,13.4785));
#421=IFCVERTEXPOINT(#422);
#422=IFCCARTESIANPOINT((7.7818,13.4784996494,0.000100000000035));
#423=IFCSURFACECURVE(#424,(#427),.PCURVE_S1.);
#424=IFCRATIONALBSPLINECURVEWITHKNOTS(1,(#425,#426),.UNSPECIFIED.,.F.,.F.,(2,2),(0.,1.),.UNSPECIFIED.,(1.,1.));
#425=IFCCARTESIANPOINT((7.7818,8.9856996494,13.4785));
#426=IFCCARTESIANPOINT((7.7818,13.4784996494,0.000100000000035));
#427=IFCPCURVE(#428,#443);
#428=IFCRATIONALBSPLINESURFACEWITHKNOTS(2,1,((#429,#430),(#431,#432),(#433,#434),(#435,#436),(#437,#438),(#439,#440),(#441,#442)),.UNSPECIFIED.,.T.,.F.,.F.,(3,2,2,3),(2,2),(0.,0.333333333333333,0.666666666666667,1.),(0.,1.),.UNSPECIFIED.,((1.,1.),(0.5,0.5),(1.,1.),(0.5,0.5),(1.,1.),(0.5,0.5),(1.,1.)));
#429=IFCCARTESIANPOINT((7.7818,8.9856996494,13.4785));
#430=IFCCARTESIANPOINT((7.7818,13.4784996494,0.000100000000035));
#431=IFCCARTESIANPOINT((7.7818,8.9856996494,13.4785));
#432=IFCCARTESIANPOINT((4.2131754709E-05,13.4784996494,9.999999993E-05));
#433=IFCCARTESIANPOINT((7.7818,8.9856996494,13.4785));
#434=IFCCARTESIANPOINT((3.8909210659,6.7392996494,0.000100000000035));
#435=IFCCARTESIANPOINT((7.7818,8.9856996494,13.4785));
#436=IFCCARTESIANPOINT((7.7818,9.9649401894E-05,0.00010000000007));
#437=IFCCARTESIANPOINT((7.7818,8.9856996494,13.4785));
#438=IFCCARTESIANPOINT((11.6726789341,6.7392996494,0.000100000000035));
#439=IFCCARTESIANPOINT((7.7818,8.9856996494,13.4785));
#440=IFCCARTESIANPOINT((15.5635578682,13.4784996494,0.000100000000035));
#441=IFCCARTESIANPOINT((7.7818,8.9856996494,13.4785));
#442=IFCCARTESIANPOINT((7.7818,13.4784996494,0.000100000000035));
#443=IFCPOLYLINE((#444,#445));
#444=IFCCARTESIANPOINT((0.0001,0.));
#445=IFCCARTESIANPOINT((0.0001,0.0001));
#446=IFCORIENTEDEDGE(*,*,#447,.F.);
#447=IFCEDGECURVE(#448,#450,#452,.T.);
#448=IFCVERTEXPOINT(#449);
#449=IFCCARTESIANPOINT((7.7818,13.4784996494,0.000100000000035));
#450=IFCVERTEXPOINT(#451);
#451=IFCCARTESIANPOINT((7.7818,13.4784996494,0.000100000000035));
#452=IFCSURFACECURVE(#453,(#461),.PCURVE_S1.);
#453=IFCRATIONALBSPLINECURVEWITHKNOTS(2,(#454,#455,#456,#457,#458,#459,#460),.UNSPECIFIED.,.T.,.F.,(3,2,2,3),(0.,0.333333333333333,0.666666666666667,1.),.UNSPECIFIED.,(1.,0.5,1.,0.5,1.,0.5,1.));
#454=IFCCARTESIANPOINT((7.7818,13.4784996494,0.000100000000035));
#455=IFCCARTESIANPOINT((31.1271157365,26.9569992988,0.00020000000007));
#456=IFCCARTESIANPOINT((11.6726789341,6.7392996494,0.000100000000035));
#457=IFCCARTESIANPOINT((15.5636,0.000199298803788,0.00020000000014));
#458=IFCCARTESIANPOINT((3.8909210659,6.7392996494,0.000100000000035));
#459=IFCCARTESIANPOINT((8.4263509419E-05,26.9569992988,0.00019999999986));
#460=IFCCARTESIANPOINT((7.7818,13.4784996494,0.000100000000035));
#461=IFCPCURVE(#462,#477);
#462=IFCRATIONALBSPLINESURFACEWITHKNOTS(2,1,((#463,#464),(#465,#466),(#467,#468),(#469,#470),(#471,#472),(#473,#474),(#475,#476)),.UNSPECIFIED.,.T.,.F.,.F.,(3,2,2,3),(2,2),(0.,0.333333333333333,0.666666666666667,1.),(0.,1.),.UNSPECIFIED.,((1.,1.),(0.5,0.5),(1.,1.),(0.5,0.5),(1.,1.),(0.5,0.5),(1.,1.)));
#463=IFCCARTESIANPOINT((7.7818,8.9856996494,13.4785));
#464=IFCCARTESIANPOINT((7.7818,13.4784996494,0.000100000000035));
#465=IFCCARTESIANPOINT((7.7818,8.9856996494,13.4785));
#466=IFCCARTESIANPOINT((4.2131754709E-05,13.4784996494,9.999999993E-05));
#467=IFCCARTESIANPOINT((7.7818,8.9856996494,13.4785));
#468=IFCCARTESIANPOINT((3.8909210659,6.7392996494,0.000100000000035));
#469=IFCCARTESIANPOINT((7.7818,8.9856996494,13.4785));
#470=IFCCARTESIANPOINT((7.7818,9.9649401894E-05,0.00010000000007));
#471=IFCCARTESIANPOINT((7.7818,8.9856996494,13.4785));
#472=IFCCARTESIANPOINT((11.6726789341,6.7392996494,0.000100000000035));
#473=IFCCARTESIANPOINT((7.7818,8.9856996494,13.4785));
#474=IFCCARTESIANPOINT((15.5635578682,13.4784996494,0.000100000000035));
#475=IFCCARTESIANPOINT((7.7818,8.9856996494,13.4785));
#476=IFCCARTESIANPOINT((7.7818,13.4784996494,0.000100000000035));
#477=IFCPOLYLINE((#478,#479));
#478=IFCCARTESIANPOINT((0.0001,0.0001));
#479=IFCCARTESIANPOINT((0.,0.0001));
#480=IFCORIENTEDEDGE(*,*,#481,.F.);
#481=IFCEDGECURVE(#482,#484,#486,.T.);
#482=IFCVERTEXPOINT(#483);
#483=IFCCARTESIANPOINT((7.7818,13.4784996494,0.000100000000035));
#484=IFCVERTEXPOINT(#485);
#485=IFCCARTESIANPOINT((7.7818,8.9856996494,13.4785));
#486=IFCSURFACECURVE(#487,(#490),.PCURVE_S1.);
#487=IFCRATIONALBSPLINECURVEWITHKNOTS(1,(#488,#489),.UNSPECIFIED.,.F.,.F.,(2,2),(0.,1.),.UNSPECIFIED.,(1.,1.));
#488=IFCCARTESIANPOINT((7.7818,13.4784996494,0.000100000000035));
#489=IFCCARTESIANPOINT((7.7818,8.9856996494,13.4785));
#490=IFCPCURVE(#491,#506);
#491=IFCRATIONALBSPLINESURFACEWITHKNOTS(2,1,((#492,#493),(#494,#495),(#496,#497),(#498,#499),(#500,#501),(#502,#503),(#504,#505)),.UNSPECIFIED.,.T.,.F.,.F.,(3,2,2,3),(2,2),(0.,0.333333333333333,0.666666666666667,1.),(0.,1.),.UNSPECIFIED.,((1.,1.),(0.5,0.5),(1.,1.),(0.5,0.5),(1.,1.),(0.5,0.5),(1.,1.)));
#492=IFCCARTESIANPOINT((7.7818,8.9856996494,13.4785));
#493=IFCCARTESIANPOINT((7.7818,13.4784996494,0.000100000000035));
#494=IFCCARTESIANPOINT((7.7818,8.9856996494,13.4785));
#495=IFCCARTESIANPOINT((4.2131754709E-05,13.4784996494,9.999999993E-05));
#496=IFCCARTESIANPOINT((7.7818,8.9856996494,13.4785));
#497=IFCCARTESIANPOINT((3.8909210659,6.7392996494,0.000100000000035));
#498=IFCCARTESIANPOINT((7.7818,8.9856996494,13.4785));
#499=IFCCARTESIANPOINT((7.7818,9.9649401894E-05,0.00010000000007));
#500=IFCCARTESIANPOINT((7.7818,8.9856996494,13.4785));
#501=IFCCARTESIANPOINT((11.6726789341,6.7392996494,0.000100000000035));
#502=IFCCARTESIANPOINT((7.7818,8.9856996494,13.4785));
#503=IFCCARTESIANPOINT((15.5635578682,13.4784996494,0.000100000000035));
#504=IFCCARTESIANPOINT((7.7818,8.9856996494,13.4785));
#505=IFCCARTESIANPOINT((7.7818,13.4784996494,0.000100000000035));
#506=IFCPOLYLINE((#507,#508));
#507=IFCCARTESIANPOINT((0.,0.0001));
#508=IFCCARTESIANPOINT((0.,0.));
#509=IFCRATIONALBSPLINESURFACEWITHKNOTS(2,1,((#510,#511),(#512,#513),(#514,#515),(#516,#517),(#518,#519),(#520,#521),(#522,#523)),.UNSPECIFIED.,.T.,.F.,.F.,(3,2,2,3),(2,2),(0.,0.333333333333333,0.666666666666667,1.),(0.,1.),.UNSPECIFIED.,((1.,1.),(0.5,0.5),(1.,1.),(0.5,0.5),(1.,1.),(0.5,0.5),(1.,1.)));
#510=IFCCARTESIANPOINT((7.7818,8.9856996494,13.4785));
#511=IFCCARTESIANPOINT((7.7818,13.4784996494,0.000100000000035));
#512=IFCCARTESIANPOINT((7.7818,8.9856996494,13.4785));
#513=IFCCARTESIANPOINT((4.2131754709E-05,13.4784996494,9.999999993E-05));
#514=IFCCARTESIANPOINT((7.7818,8.9856996494,13.4785));
#515=IFCCARTESIANPOINT((3.8909210659,6.7392996494,0.000100000000035));
#516=IFCCARTESIANPOINT((7.7818,8.9856996494,13.4785));
#517=IFCCARTESIANPOINT((7.7818,9.9649401894E-05,0.00010000000007));
#518=IFCCARTESIANPOINT((7.7818,8.9856996494,13.4785));
#519=IFCCARTESIANPOINT((11.6726789341,6.7392996494,0.000100000000035));
#520=IFCCARTESIANPOINT((7.7818,8.9856996494,13.4785));
#521=IFCCARTESIANPOINT((15.5635578682,13.4784996494,0.000100000000035));
#522=IFCCARTESIANPOINT((7.7818,8.9856996494,13.4785));
#523=IFCCARTESIANPOINT((7.7818,13.4784996494,0.000100000000035));
#524=IFCBOUNDINGBOX(#525,8.98560000000003,8.98560000000008,13.4784);
#525=IFCCARTESIANPOINT((3.289,4.4928996494,0.000100000000035));
#526=IFCPERSONANDORGANIZATION(#527,#530,$);
#527=IFCPERSON($,'Last Name','First Name',$,$,$,(#528),(#529));
#528=IFCACTORROLE(.SUPPLIER.,$,$);
#529=IFCTELECOMADDRESS($,$,$,('Phone'),$,$,('E-Mail'),$,$);
#530=IFCORGANIZATION($,'Organization Name',$,$,(#531));
#531=IFCPOSTALADDRESS($,$,$,$,('Address 1','Address 2'),$,'City/Town','State/Region','Postal Code','Country');
#532=IFCAPPLICATION(#533,'***********','OpenBuildings Designer','ABD');
#533=IFCORGANIZATION($,'Bentley Systems Inc.',$,$,$);
#534=IFCAXIS2PLACEMENT3D(#535,#536,#537);
#535=IFCCARTESIANPOINT((0.,0.,0.));
#536=IFCDIRECTION((0.,0.,1.));
#537=IFCDIRECTION((1.,0.,0.));
#538=IFCDIRECTION((0.,1.));
ENDSEC;
END-ISO-10303-21;

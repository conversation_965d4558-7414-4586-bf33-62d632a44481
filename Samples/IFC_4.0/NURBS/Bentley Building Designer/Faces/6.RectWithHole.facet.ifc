ISO-10303-21;
HEADER;
/* Generated by software containing ST-Developer
 * from STEP Tools, Inc. (www.steptools.com) 
 */

FILE_DESCRIPTION(
/* description */ ('ViewDefinition [ReferenceView_V1.2]',
'ExchangeRequirement [Architecture, Structural, BuildingService]',
'Comment [Comments]',
'Comment [System: OpenBuildings Designer*********** debug build Feb  5
 2020 00:34:15]'),
/* implementation_level */ '2;1');

FILE_NAME(
/* name */ '6.RectWithHole.facet',
/* time_stamp */ '2020-03-10T17:18:18+03:00',
/* author */ ('First Name Last Name'),
/* organization */ ('Organization Name'),
/* preprocessor_version */ 'ST-DEVELOPER v16.13',
/* originating_system */ 'OpenBuildings Designer***********',
/* authorisation */ 'Administrator');

FILE_SCHEMA (('IFC4'));
ENDSEC;

DATA;
#10=IFCBOUNDINGBOX(#91,12.,12.0270878618007,11.887043827089);
#11=IFCPRESENTATIONLAYERASSIGNMENT('Default',$,(#15),$);
#12=IFCGEOMETRICREPRESENTATIONSUBCONTEXT('Body','Model',*,*,*,*,#14,1.,
 .MODEL_VIEW.,$);
#13=IFCGEOMETRICREPRESENTATIONSUBCONTEXT('Box','Model',*,*,*,*,#14,1.,
 .SKETCH_VIEW.,$);
#14=IFCGEOMETRICREPRESENTATIONCONTEXT($,'Model',3,1.E-6,#87,#94);
#15=IFCSHAPEREPRESENTATION(#12,'Body','Tessellation',(#75));
#16=IFCSHAPEREPRESENTATION(#13,'Box','BoundingBox',(#10));
#17=IFCPRODUCTDEFINITIONSHAPE($,$,(#15,#16));
#18=IFCRELCONTAINEDINSPATIALSTRUCTURE('2aJi0CW_wcTJthPGyCEMkK',#157,$,$,
(#21),#19);
#19=IFCBUILDINGSTOREY('0rm7DUqMJhOUcs5v8t1F$L',#157,'Floor 3',
'Administrative and employee lounge',$,#81,$,'Floor 3',.ELEMENT.,9.144);
#20=IFCBUILDING('3_IkJInE13hR6Xwo4VKOgh',#157,'Bldg 1',
'3 Story Building',$,#80,$,'Bldg 1',.ELEMENT.,0.,0.,#149);
#21=IFCBUILDINGELEMENTPROXY('1vdpKn9biZ$o4qW2SHbk1Q',#157,
'Element type: 24','824*bspline surface!Default','',#82,#17,
'824*bspline surface!Default',.NOTDEFINED.);
#22=IFCSTYLEDITEM(#75,(#23),$);
#23=IFCSURFACESTYLE($,.BOTH.,(#24));
#24=IFCSURFACESTYLESHADING(#25,0.);
#25=IFCCOLOURRGB($,1.,1.,1.);
#26=IFCINDEXEDPOLYGONALFACE((1,2,3,4));
#27=IFCINDEXEDPOLYGONALFACE((4,3,5,6));
#28=IFCINDEXEDPOLYGONALFACE((6,5,7,8));
#29=IFCINDEXEDPOLYGONALFACE((8,7,9,10));
#30=IFCINDEXEDPOLYGONALFACE((10,9,11,12));
#31=IFCINDEXEDPOLYGONALFACE((12,11,13,14));
#32=IFCINDEXEDPOLYGONALFACE((15,16,17,18));
#33=IFCINDEXEDPOLYGONALFACE((18,17,19,20));
#34=IFCINDEXEDPOLYGONALFACE((20,19,21,22));
#35=IFCINDEXEDPOLYGONALFACE((22,21,23,24));
#36=IFCINDEXEDPOLYGONALFACE((24,23,25,26));
#37=IFCINDEXEDPOLYGONALFACE((26,25,27,28));
#38=IFCINDEXEDPOLYGONALFACE((28,27,29,30));
#39=IFCINDEXEDPOLYGONALFACE((30,29,31,32));
#40=IFCINDEXEDPOLYGONALFACE((33,34,16));
#41=IFCINDEXEDPOLYGONALFACE((35,36,37));
#42=IFCINDEXEDPOLYGONALFACE((38,39,14));
#43=IFCINDEXEDPOLYGONALFACE((40,41,42));
#44=IFCINDEXEDPOLYGONALFACE((43,44,39));
#45=IFCINDEXEDPOLYGONALFACE((39,38,43));
#46=IFCINDEXEDPOLYGONALFACE((44,45,42));
#47=IFCINDEXEDPOLYGONALFACE((42,39,44));
#48=IFCINDEXEDPOLYGONALFACE((42,45,46));
#49=IFCINDEXEDPOLYGONALFACE((42,46,40));
#50=IFCINDEXEDPOLYGONALFACE((47,48,14));
#51=IFCINDEXEDPOLYGONALFACE((36,49,13));
#52=IFCINDEXEDPOLYGONALFACE((13,37,36));
#53=IFCINDEXEDPOLYGONALFACE((50,47,14));
#54=IFCINDEXEDPOLYGONALFACE((13,49,51));
#55=IFCINDEXEDPOLYGONALFACE((52,50,14));
#56=IFCINDEXEDPOLYGONALFACE((13,51,53));
#57=IFCINDEXEDPOLYGONALFACE((54,52,14));
#58=IFCINDEXEDPOLYGONALFACE((13,53,54));
#59=IFCINDEXEDPOLYGONALFACE((14,13,54));
#60=IFCINDEXEDPOLYGONALFACE((14,48,38));
#61=IFCINDEXEDPOLYGONALFACE((55,56,34));
#62=IFCINDEXEDPOLYGONALFACE((57,55,34));
#63=IFCINDEXEDPOLYGONALFACE((58,57,34));
#64=IFCINDEXEDPOLYGONALFACE((34,33,58));
#65=IFCINDEXEDPOLYGONALFACE((59,37,34));
#66=IFCINDEXEDPOLYGONALFACE((34,56,59));
#67=IFCINDEXEDPOLYGONALFACE((37,59,35));
#68=IFCINDEXEDPOLYGONALFACE((41,60,15));
#69=IFCINDEXEDPOLYGONALFACE((15,42,41));
#70=IFCINDEXEDPOLYGONALFACE((61,62,16));
#71=IFCINDEXEDPOLYGONALFACE((15,60,61));
#72=IFCINDEXEDPOLYGONALFACE((16,15,61));
#73=IFCINDEXEDPOLYGONALFACE((16,62,33));
#74=IFCCARTESIANPOINTLIST3D(((12.000035623731,3.56237310153773E-5,3.59370000000005),
(3.56237309519202E-5,3.56237309460807E-5,3.5937),(3.56237309528582E-5,2.93863608466212,
2.78092077222723),(12.000035623731,2.93863608466219,2.78092077222725),(3.5623730955108E-5,
5.6865220653193,2.23194191344762),(12.000035623731,5.68652206531939,2.23194191344764),
(3.56237309550618E-5,7.72846921797043,2.15068135964469),(12.000035623731,
7.72846921797052,2.15068135964472),(3.56237309570197E-5,9.18212469647124,
2.44726788666241),(12.000035623731,9.18212469647134,2.44726788666245),(3.56237309634072E-5,
10.3017975988992,3.09416705793496),(12.000035623731,10.3017975988993,3.09416705793501),
(3.56237309668594E-5,11.1303238271614,4.11637057440334),(12.000035623731,
11.1303238271615,4.11637057440339),(12.000035623731,11.9331946287012,9.52918161280188),
(3.56237309882999E-5,11.9331946287011,9.52918161280184),(3.56237309917675E-5,
11.4520530196514,11.3993978793655),(12.000035623731,11.4520530196515,11.3993978793655),
(3.56237309947772E-5,10.7378800730514,12.6472749123606),(12.000035623731,
10.7378800730515,12.6472749123606),(3.56237309908097E-5,9.7745337415019,
13.4646099914071),(12.000035623731,9.77453374150197,13.4646099914071),(3.56237309832677E-5,
8.52450838507296,13.9270525982087),(12.000035623731,8.52450838507304,13.9270525982087),
(3.56237309819346E-5,8.36440669327847,13.9580465984157),(12.000035623731,
8.36440669327854,13.9580465984157),(3.56237309728154E-5,6.53290934780139,
14.0377251867336),(12.000035623731,6.53290934780147,14.0377251867336),(3.56237309608598E-5,
3.66034093007487,13.580601472828),(12.000035623731,3.66034093007493,13.580601472828),
(3.56237309521336E-5,3.5623730946827E-5,12.5937),(12.000035623731,3.56237310159367E-5,
12.5937000000001),(4.57758631818715,12.0208519794837,7.61086559606823),
(3.56237309832068E-5,12.0209350792972,7.61085478261137),(3.10115036289269,
11.7958146892504,5.90528285739555),(3.33272179725709,11.7263674667051,5.62829511736462),
(3.56237309744756E-5,11.7277828274938,5.62767267726536),(8.68193533412983,
11.727398899119,5.62782402641685),(12.000035623731,11.7277828274939,5.62767267726542),
(8.09928297830169,12.0069887052451,7.3670060028364),(7.41463954287985,12.0205869166489,
7.61090970932408),(12.000035623731,12.0209350792973,7.61085478261142),(8.9359411035137,
11.8129547552475,5.98242148675326),(9.00003562373093,11.8750527629544,6.29508376926321),
(8.90915316355799,11.9341467012463,6.66501705277272),(8.6201476217649,11.977770710071,
7.02575217881544),(8.22866860658416,11.63071074409,5.29043595645168),(8.66905657456281,
11.7230609925301,5.60984500394887),(3.40334111827362,11.7051890527262,5.54382577776506),
(7.4714795426918,11.5281761302004,4.9870358706731),(3.93859910704579,11.6033645468155,
5.20494953468713),(6.52486398216909,11.4636549409597,4.81709865582256),
(4.71408665860433,11.5103032908724,4.93851479207616),(5.53712448362505,
11.4616557720407,4.81205487000696),(3.32714164376353,11.9725954872721,6.97646411192229),
(3.06336898921802,11.9256950306372,6.60584179458511),(3.75907361121469,
12.0016057516348,7.29278043174156),(4.51755081725975,12.0204384085141,7.6000680829465),
(3.00241152115253,11.863977578236,6.23481576890388),(7.33019614319314,12.0222641096828,
7.64099260664906),(6.50744970447466,12.0271234855317,7.77476345225691),
(5.48472293484532,12.0271010381953,7.77401605488445)));
#75=IFCPOLYGONALFACESET(#74,.F.,(#26,#27,#28,#29,#30,#31,#32,#33,#34,#35,
#36,#37,#38,#39,#40,#41,#42,#43,#44,#45,#46,#47,#48,#49,#50,#51,#52,#53,
#54,#55,#56,#57,#58,#59,#60,#61,#62,#63,#64,#65,#66,#67,#68,#69,#70,#71,
#72,#73),$);
#76=IFCRELAGGREGATES('1kB3AQvfGpkFm_7uHNRdoP',#157,$,$,#146,(#95));
#77=IFCRELAGGREGATES('0Cp_wDOOr3amQ16IWrGpUy',#157,$,$,#95,(#20));
#78=IFCRELAGGREGATES('1NfacxJUhl1S_ka3CQXVRt',#157,$,$,#20,(#19));
#79=IFCLOCALPLACEMENT($,#83);
#80=IFCLOCALPLACEMENT(#79,#84);
#81=IFCLOCALPLACEMENT(#80,#85);
#82=IFCLOCALPLACEMENT(#81,#86);
#83=IFCAXIS2PLACEMENT3D(#88,#92,#93);
#84=IFCAXIS2PLACEMENT3D(#88,#92,#93);
#85=IFCAXIS2PLACEMENT3D(#89,#92,#93);
#86=IFCAXIS2PLACEMENT3D(#90,#92,#93);
#87=IFCAXIS2PLACEMENT3D(#88,#92,#93);
#88=IFCCARTESIANPOINT((0.,0.,0.));
#89=IFCCARTESIANPOINT((0.,0.,9.144));
#90=IFCCARTESIANPOINT((14.1421,14.1421,37.2623));
#91=IFCCARTESIANPOINT((3.5623730952E-5,3.5623730946E-5,2.15068135964));
#92=IFCDIRECTION((0.,0.,1.));
#93=IFCDIRECTION((1.,0.,0.));
#94=IFCDIRECTION((0.,1.));
#95=IFCSITE('006_i1V1D0tAKEh_$vljQt',#157,'Site 1','Site 1',$,#79,$,
'Site 1',.ELEMENT.,$,$,0.,$,#148);
#96=IFCRELDEFINESBYPROPERTIES('1RL1EewHTsROrPcNSlBI56',#157,'Project',$,
(#146),#106);
#97=IFCRELDEFINESBYPROPERTIES('3bjMWmv_Cp6LPxmaCnPGwW',#157,'Site',$,(#95),
#107);
#98=IFCRELDEFINESBYPROPERTIES('2zOmgYUoQmhp9sZMdCZF7s',#157,
'Pset_BuildingCommon',$,(#20),#108);
#99=IFCRELDEFINESBYPROPERTIES('3k0v4EdaEvPD8NSrRFB2MT',#157,
'ArchBuilding',$,(#20),#109);
#100=IFCRELDEFINESBYPROPERTIES('3uIgchz4n8nDCCMgq1BBql',#157,
'ObjectIdentity',$,(#20),#110);
#101=IFCRELDEFINESBYPROPERTIES('1CCmZ_1eEDpfMO62t1jkZj',#157,
'ObjectPostalAddress',$,(#20),#111);
#102=IFCRELDEFINESBYPROPERTIES('24I$F7L8gfSwdAGDvfkVEu',#157,
'Pset_BuildingStoreyCommon',$,(#19),#112);
#103=IFCRELDEFINESBYPROPERTIES('1K1DvjfvPzx3hE5w5ZHK0q',#157,
'ArchFloor',$,(#19),#113);
#104=IFCRELDEFINESBYPROPERTIES('0lrK04aZ0v6OfwsyTXPzZq',#157,'Floor',$,
(#19),#114);
#105=IFCRELDEFINESBYPROPERTIES('1fbDyx4oFQKKacC_b9xURq',#157,
'StructuralFloorCommon',$,(#19),#115);
#106=IFCPROPERTYSET('2rcLYSOKrF02Xv$qFtEF4e',#157,'Project',$,(#116));
#107=IFCPROPERTYSET('2Qswsgonw96KTM1$UoUklF',#157,'Site',$,(#117,#118));
#108=IFCPROPERTYSET('2Ct1rxyLMs3vgB_jB90hMr',#157,'Pset_BuildingCommon',
$,(#119));
#109=IFCPROPERTYSET('3OmKLTEF7bgCV0$1Gr7ajZ',#157,'ArchBuilding',$,(#120,
#121));
#110=IFCPROPERTYSET('1HjNcqn7ak9HvLTK8j8w1d',#157,'ObjectIdentity',$,(#122));
#111=IFCPROPERTYSET('306EzywKpRfIeis4axlC1d',#157,'ObjectPostalAddress',
$,(#123,#124,#125,#126));
#112=IFCPROPERTYSET('381uKge8G33qXIbHr84hB4',#157,
'Pset_BuildingStoreyCommon',$,(#127,#128,#129,#130));
#113=IFCPROPERTYSET('0GZmVQOAmJ6zMOk6j3uleN',#157,'ArchFloor',$,(#131,#132,
#133,#134,#135,#136,#137,#138,#139,#140));
#114=IFCPROPERTYSET('0A3C_vQUNRQZfA6f5qtBlE',#157,'Floor',$,(#141,#142,
#143));
#115=IFCPROPERTYSET('0izo3etGuCpkjROydkcZbT',#157,
'StructuralFloorCommon',$,(#144));
#116=IFCPROPERTYSINGLEVALUE('ProjectName',$,IFCTEXT(
'BuildingTemplate_US'),$);
#117=IFCPROPERTYSINGLEVALUE('SiteName',$,IFCTEXT('Site 1'),$);
#118=IFCPROPERTYSINGLEVALUE('BuildingHeightLimit',$,IFCLENGTHMEASURE(0.),
#293);
#119=IFCPROPERTYSINGLEVALUE('YearOfConstruction',$,IFCLABEL('2006'),$);
#120=IFCPROPERTYSINGLEVALUE('BuildingName',$,IFCTEXT('Bldg 1'),$);
#121=IFCPROPERTYSINGLEVALUE('YearConstructed',$,IFCTEXT('2006'),$);
#122=IFCPROPERTYSINGLEVALUE('Description',$,IFCTEXT('3 Story Building'),
$);
#123=IFCPROPERTYSINGLEVALUE('Address1',$,IFCTEXT(
'203 Rickenhouse Drive'),$);
#124=IFCPROPERTYSINGLEVALUE('City',$,IFCTEXT('Madison'),$);
#125=IFCPROPERTYSINGLEVALUE('PostalCode',$,IFCTEXT('35789'),$);
#126=IFCPROPERTYSINGLEVALUE('Country',$,IFCTEXT('US'),$);
#127=IFCPROPERTYSINGLEVALUE('GrossAreaPlanned',$,IFCAREAMEASURE(22296729.6),
#294);
#128=IFCPROPERTYSINGLEVALUE('GrossPlannedArea',$,IFCAREAMEASURE(22296729.6),
$);
#129=IFCPROPERTYSINGLEVALUE('NetAreaPlanned',$,IFCAREAMEASURE(22296729.6),
#294);
#130=IFCPROPERTYSINGLEVALUE('NetPlannedArea',$,IFCAREAMEASURE(22296729.6),
$);
#131=IFCPROPERTYSINGLEVALUE('FloorName',$,IFCTEXT('Floor 3'),$);
#132=IFCPROPERTYSINGLEVALUE('FinishedFloorElevation',$,IFCREAL(9.144),$);
#133=IFCPROPERTYSINGLEVALUE('Description',$,IFCTEXT(
'Administrative and employee lounge'),$);
#134=IFCPROPERTYSINGLEVALUE('ApproxLength',$,IFCLENGTHMEASURE(6096.),#293);
#135=IFCPROPERTYSINGLEVALUE('ApproxWidth',$,IFCLENGTHMEASURE(3657.6),#293);
#136=IFCPROPERTYSINGLEVALUE('TypicalFloor',$,IFCBOOLEAN(.F.),$);
#137=IFCPROPERTYSINGLEVALUE('TypicalFloorHeight',$,IFCLENGTHMEASURE(0.),
#293);
#138=IFCPROPERTYSINGLEVALUE('TypicalFloorBaseElevation',$,
IFCLENGTHMEASURE(0.),#293);
#139=IFCPROPERTYSINGLEVALUE('ACSRotation',$,IFCTEXT('0.00000000'),$);
#140=IFCPROPERTYSINGLEVALUE('Annotation',$,IFCBOOLEAN(.T.),$);
#141=IFCPROPERTYSINGLEVALUE('GrossArea',$,IFCAREAMEASURE(22296729.6),#294);
#142=IFCPROPERTYSINGLEVALUE('NetArea',$,IFCAREAMEASURE(22296729.6),#294);
#143=IFCPROPERTYSINGLEVALUE('OccupancyType',$,IFCTEXT('Commercial'),$);
#144=IFCPROPERTYSINGLEVALUE('issplicefloor',$,IFCBOOLEAN(.F.),$);
#145=IFCUNITASSIGNMENT((#293,#294,#295,#296,#297,#298,#299,#300,#301,#302,
#303,#304,#305,#306,#307,#308,#309,#310,#311,#312,#313,#314,#290,#316,#317,
#318,#319,#320,#321,#159,#160,#161,#162,#163,#164,#165,#166,#167,#168,#169,
#170,#171,#172,#173,#174,#175,#176,#177,#178,#179,#180,#181,#182,#183,#184,
#185,#186,#187,#188,#189,#190,#191,#192,#193,#194,#195,#196,#197,#198,#199,
#200,#201,#202,#158));
#146=IFCPROJECT('1JmBUThP19Z8wED88xGwCF',#157,'BuildingTemplate_US',
'BuildingTemplate_US',$,'BuildingTemplate_US',$,(#14),#145);
#147=IFCPOSTALADDRESS($,$,$,$,('Address 1','Address 2'),$,'City/Town',
'State/Region','Postal Code','Country');
#148=IFCPOSTALADDRESS($,$,$,'Site 1',$,$,$,$,$,$);
#149=IFCPOSTALADDRESS($,$,$,$,('203 Rickenhouse Drive'),$,'Madison',$,
'35789','US');
#150=IFCTELECOMADDRESS($,$,$,('Phone'),$,$,('E-Mail'),$,$);
#151=IFCACTORROLE(.SUPPLIER.,$,$);
#152=IFCPERSON($,'Last Name','First Name',$,$,$,(#151),(#150));
#153=IFCPERSONANDORGANIZATION(#152,#155,$);
#154=IFCORGANIZATION($,'Bentley Systems Inc.',$,$,$);
#155=IFCORGANIZATION($,'Organization Name',$,$,(#147));
#156=IFCAPPLICATION(#154,'***********','OpenBuildings Designer','ABD');
#157=IFCOWNERHISTORY(#153,#156,$,$,0,$,$,1583849896);
#158=IFCMONETARYUNIT('USD');
#159=IFCDERIVEDUNIT((#203,#204),.ACCELERATIONUNIT.,'(METRE)/(SECOND^2)');
#160=IFCDERIVEDUNIT((#205,#206),.ANGULARVELOCITYUNIT.,
'(DEGREE)/(SECOND)');
#161=IFCDERIVEDUNIT((#207,#208),.CURVATUREUNIT.,'(DEGREE)/(METRE)');
#162=IFCDERIVEDUNIT((#209,#210),.DYNAMICVISCOSITYUNIT.,
'(PASCAL)(SECOND)');
#163=IFCDERIVEDUNIT((#211,#212),.HEATFLUXDENSITYUNIT.,
'(WATT)/(METRE^2)');
#164=IFCDERIVEDUNIT((#213,#214),.HEATINGVALUEUNIT.,'(JOULE)/(GRAM)');
#165=IFCDERIVEDUNIT((#215),.INTEGERCOUNTRATEUNIT.,'1/(SECOND)');
#166=IFCDERIVEDUNIT((#216,#217),.IONCONCENTRATIONUNIT.,
'(GRAM)/(CUBIC_METRE)');
#167=IFCDERIVEDUNIT((#218,#219),.ISOTHERMALMOISTURECAPACITYUNIT.,
'(CUBIC_METRE)/(GRAM)');
#168=IFCDERIVEDUNIT((#220,#221),.KINEMATICVISCOSITYUNIT.,
'(SQUARE_METRE)/(SECOND)');
#169=IFCDERIVEDUNIT((#222,#223),.LINEARFORCEUNIT.,'(NEWTON)/(METRE)');
#170=IFCDERIVEDUNIT((#224,#225),.LINEARMOMENTUNIT.,'(NEWTON)/(METRE)');
#171=IFCDERIVEDUNIT((#226,#227),.LINEARSTIFFNESSUNIT.,
'(NEWTON)/(METRE)');
#172=IFCDERIVEDUNIT((#228,#229),.LINEARVELOCITYUNIT.,'(METRE)/(SECOND)');
#173=IFCDERIVEDUNIT((#230,#231),.LUMINOUSINTENSITYDISTRIBUTIONUNIT.,
'(CANDELA)/(LUMEN)');
#174=IFCDERIVEDUNIT((#232,#233),.MASSDENSITYUNIT.,
'(GRAM)/(CUBIC_METRE)');
#175=IFCDERIVEDUNIT((#234,#235),.MASSFLOWRATEUNIT.,'(GRAM)/(SECOND)');
#176=IFCDERIVEDUNIT((#236,#237),.MASSPERLENGTHUNIT.,'(GRAM)/(METRE)');
#177=IFCDERIVEDUNIT((#238,#239),.MODULUSOFELASTICITYUNIT.,
'(NEWTON)/(SQUARE_METRE)');
#178=IFCDERIVEDUNIT((#240,#241),.MODULUSOFLINEARSUBGRADEREACTIONUNIT.,
'(NEWTON)/(METRE^2)');
#179=IFCDERIVEDUNIT((#242,#243),
 .MODULUSOFROTATIONALSUBGRADEREACTIONUNIT.,'(NEWTON)(METRE)');
#180=IFCDERIVEDUNIT((#244,#245),.MODULUSOFSUBGRADEREACTIONUNIT.,
'(NEWTON)/(CUBIC_METRE)');
#181=IFCDERIVEDUNIT((#246,#247),.MOISTUREDIFFUSIVITYUNIT.,
'(CUBIC_METRE)/(SECOND)');
#182=IFCDERIVEDUNIT((#248,#249),.MOLECULARWEIGHTUNIT.,'(GRAM)/(MOLE)');
#183=IFCDERIVEDUNIT((#250,#251),.PLANARFORCEUNIT.,'(NEWTON)/(METRE^2)');
#184=IFCDERIVEDUNIT((#252),.ROTATIONALFREQUENCYUNIT.,'1/(SECOND)');
#185=IFCDERIVEDUNIT((#253,#254),.ROTATIONALMASSUNIT.,'(GRAM)(METRE^2)');
#186=IFCDERIVEDUNIT((#255,#256,#257),.ROTATIONALSTIFFNESSUNIT.,
'(NEWTON)(METRE)/(DEGREE)');
#187=IFCDERIVEDUNIT((#258),.SECTIONAREAINTEGRALUNIT.,'(METRE^5)');
#188=IFCDERIVEDUNIT((#259),.SECTIONMODULUSUNIT.,'(METRE^3)');
#189=IFCDERIVEDUNIT((#260,#261),.SHEARMODULUSUNIT.,
'(NEWTON)/(SQUARE_METRE)');
#190=IFCDERIVEDUNIT((#262,#263),.SOUNDPOWERUNIT.,'(JOULE)/(SECOND)');
#191=IFCDERIVEDUNIT((#264,#265,#266),.SPECIFICHEATCAPACITYUNIT.,
'(NEWTON)/(GRAM)(KELVIN)');
#192=IFCDERIVEDUNIT((#267,#268),.TEMPERATUREGRADIENTUNIT.,
'(KELVIN)/(METRE)');
#193=IFCDERIVEDUNIT((#269,#270,#271),.THERMALADMITTANCEUNIT.,
'(WATT)/(SQUARE_METRE)(KELVIN)');
#194=IFCDERIVEDUNIT((#272,#273,#274),.THERMALCONDUCTANCEUNIT.,
'(WATT)/(METRE)(KELVIN)');
#195=IFCDERIVEDUNIT((#275),.THERMALEXPANSIONCOEFFICIENTUNIT.,
'1/(KELVIN)');
#196=IFCDERIVEDUNIT((#276,#277),.THERMALRESISTANCEUNIT.,
'(SQUARE_METRE)/(WATT)');
#197=IFCDERIVEDUNIT((#278,#279,#280),.THERMALTRANSMITTANCEUNIT.,
'(WATT)/(SQUARE_METRE)(KELVIN)');
#198=IFCDERIVEDUNIT((#281,#282),.TORQUEUNIT.,'(NEWTON)(METRE)');
#199=IFCDERIVEDUNIT((#283,#284),.VAPORPERMEABILITYUNIT.,
'(GRAM)/(SECOND)');
#200=IFCDERIVEDUNIT((#285,#286),.VOLUMETRICFLOWRATEUNIT.,
'(CUBIC_METRE)/(SECOND)');
#201=IFCDERIVEDUNIT((#287),.WARPINGCONSTANTUNIT.,'(METRE^6)');
#202=IFCDERIVEDUNIT((#288,#289),.WARPINGMOMENTUNIT.,'(NEWTON)(METRE^2)');
#203=IFCDERIVEDUNITELEMENT(#293,1);
#204=IFCDERIVEDUNITELEMENT(#321,-2);
#205=IFCDERIVEDUNITELEMENT(#290,1);
#206=IFCDERIVEDUNITELEMENT(#321,-1);
#207=IFCDERIVEDUNITELEMENT(#290,1);
#208=IFCDERIVEDUNITELEMENT(#293,-1);
#209=IFCDERIVEDUNITELEMENT(#317,1);
#210=IFCDERIVEDUNITELEMENT(#321,1);
#211=IFCDERIVEDUNITELEMENT(#316,1);
#212=IFCDERIVEDUNITELEMENT(#293,-2);
#213=IFCDERIVEDUNITELEMENT(#305,1);
#214=IFCDERIVEDUNITELEMENT(#314,-1);
#215=IFCDERIVEDUNITELEMENT(#321,-1);
#216=IFCDERIVEDUNITELEMENT(#314,1);
#217=IFCDERIVEDUNITELEMENT(#295,-1);
#218=IFCDERIVEDUNITELEMENT(#295,1);
#219=IFCDERIVEDUNITELEMENT(#314,-1);
#220=IFCDERIVEDUNITELEMENT(#294,1);
#221=IFCDERIVEDUNITELEMENT(#321,-1);
#222=IFCDERIVEDUNITELEMENT(#306,1);
#223=IFCDERIVEDUNITELEMENT(#293,-1);
#224=IFCDERIVEDUNITELEMENT(#306,1);
#225=IFCDERIVEDUNITELEMENT(#293,-1);
#226=IFCDERIVEDUNITELEMENT(#306,1);
#227=IFCDERIVEDUNITELEMENT(#293,-1);
#228=IFCDERIVEDUNITELEMENT(#293,1);
#229=IFCDERIVEDUNITELEMENT(#321,-1);
#230=IFCDERIVEDUNITELEMENT(#311,1);
#231=IFCDERIVEDUNITELEMENT(#310,-1);
#232=IFCDERIVEDUNITELEMENT(#314,1);
#233=IFCDERIVEDUNITELEMENT(#295,-1);
#234=IFCDERIVEDUNITELEMENT(#314,1);
#235=IFCDERIVEDUNITELEMENT(#321,-1);
#236=IFCDERIVEDUNITELEMENT(#314,1);
#237=IFCDERIVEDUNITELEMENT(#293,-1);
#238=IFCDERIVEDUNITELEMENT(#306,1);
#239=IFCDERIVEDUNITELEMENT(#294,-1);
#240=IFCDERIVEDUNITELEMENT(#306,1);
#241=IFCDERIVEDUNITELEMENT(#293,-2);
#242=IFCDERIVEDUNITELEMENT(#306,1);
#243=IFCDERIVEDUNITELEMENT(#293,1);
#244=IFCDERIVEDUNITELEMENT(#306,1);
#245=IFCDERIVEDUNITELEMENT(#295,-1);
#246=IFCDERIVEDUNITELEMENT(#295,1);
#247=IFCDERIVEDUNITELEMENT(#321,-1);
#248=IFCDERIVEDUNITELEMENT(#314,1);
#249=IFCDERIVEDUNITELEMENT(#297,-1);
#250=IFCDERIVEDUNITELEMENT(#306,1);
#251=IFCDERIVEDUNITELEMENT(#293,-2);
#252=IFCDERIVEDUNITELEMENT(#321,-1);
#253=IFCDERIVEDUNITELEMENT(#314,1);
#254=IFCDERIVEDUNITELEMENT(#293,2);
#255=IFCDERIVEDUNITELEMENT(#306,1);
#256=IFCDERIVEDUNITELEMENT(#293,1);
#257=IFCDERIVEDUNITELEMENT(#290,-1);
#258=IFCDERIVEDUNITELEMENT(#293,5);
#259=IFCDERIVEDUNITELEMENT(#293,3);
#260=IFCDERIVEDUNITELEMENT(#306,1);
#261=IFCDERIVEDUNITELEMENT(#294,-1);
#262=IFCDERIVEDUNITELEMENT(#305,1);
#263=IFCDERIVEDUNITELEMENT(#321,-1);
#264=IFCDERIVEDUNITELEMENT(#306,1);
#265=IFCDERIVEDUNITELEMENT(#314,-1);
#266=IFCDERIVEDUNITELEMENT(#320,-1);
#267=IFCDERIVEDUNITELEMENT(#320,1);
#268=IFCDERIVEDUNITELEMENT(#293,-1);
#269=IFCDERIVEDUNITELEMENT(#316,1);
#270=IFCDERIVEDUNITELEMENT(#294,-1);
#271=IFCDERIVEDUNITELEMENT(#320,-1);
#272=IFCDERIVEDUNITELEMENT(#316,1);
#273=IFCDERIVEDUNITELEMENT(#293,-1);
#274=IFCDERIVEDUNITELEMENT(#320,-1);
#275=IFCDERIVEDUNITELEMENT(#320,-1);
#276=IFCDERIVEDUNITELEMENT(#294,1);
#277=IFCDERIVEDUNITELEMENT(#316,-1);
#278=IFCDERIVEDUNITELEMENT(#316,1);
#279=IFCDERIVEDUNITELEMENT(#294,-1);
#280=IFCDERIVEDUNITELEMENT(#320,-1);
#281=IFCDERIVEDUNITELEMENT(#306,1);
#282=IFCDERIVEDUNITELEMENT(#293,1);
#283=IFCDERIVEDUNITELEMENT(#314,1);
#284=IFCDERIVEDUNITELEMENT(#321,-1);
#285=IFCDERIVEDUNITELEMENT(#295,1);
#286=IFCDERIVEDUNITELEMENT(#321,-1);
#287=IFCDERIVEDUNITELEMENT(#293,6);
#288=IFCDERIVEDUNITELEMENT(#306,1);
#289=IFCDERIVEDUNITELEMENT(#293,2);
#290=IFCCONVERSIONBASEDUNIT(#291,.PLANEANGLEUNIT.,'degree',#292);
#291=IFCDIMENSIONALEXPONENTS(0,0,0,0,0,0,0);
#292=IFCMEASUREWITHUNIT(IFCREAL(0.0174532925199433),#315);
#293=IFCSIUNIT(*,.LENGTHUNIT.,$,.METRE.);
#294=IFCSIUNIT(*,.AREAUNIT.,$,.SQUARE_METRE.);
#295=IFCSIUNIT(*,.VOLUMEUNIT.,$,.CUBIC_METRE.);
#296=IFCSIUNIT(*,.ABSORBEDDOSEUNIT.,$,.GRAY.);
#297=IFCSIUNIT(*,.AMOUNTOFSUBSTANCEUNIT.,$,.MOLE.);
#298=IFCSIUNIT(*,.DOSEEQUIVALENTUNIT.,$,.SIEVERT.);
#299=IFCSIUNIT(*,.ELECTRICCAPACITANCEUNIT.,$,.FARAD.);
#300=IFCSIUNIT(*,.ELECTRICCHARGEUNIT.,$,.COULOMB.);
#301=IFCSIUNIT(*,.ELECTRICCONDUCTANCEUNIT.,$,.SIEMENS.);
#302=IFCSIUNIT(*,.ELECTRICCURRENTUNIT.,$,.AMPERE.);
#303=IFCSIUNIT(*,.ELECTRICRESISTANCEUNIT.,$,.OHM.);
#304=IFCSIUNIT(*,.ELECTRICVOLTAGEUNIT.,$,.VOLT.);
#305=IFCSIUNIT(*,.ENERGYUNIT.,$,.JOULE.);
#306=IFCSIUNIT(*,.FORCEUNIT.,$,.NEWTON.);
#307=IFCSIUNIT(*,.FREQUENCYUNIT.,$,.HERTZ.);
#308=IFCSIUNIT(*,.ILLUMINANCEUNIT.,$,.LUX.);
#309=IFCSIUNIT(*,.INDUCTANCEUNIT.,$,.HENRY.);
#310=IFCSIUNIT(*,.LUMINOUSFLUXUNIT.,$,.LUMEN.);
#311=IFCSIUNIT(*,.LUMINOUSINTENSITYUNIT.,$,.CANDELA.);
#312=IFCSIUNIT(*,.MAGNETICFLUXDENSITYUNIT.,$,.TESLA.);
#313=IFCSIUNIT(*,.MAGNETICFLUXUNIT.,$,.WEBER.);
#314=IFCSIUNIT(*,.MASSUNIT.,$,.GRAM.);
#315=IFCSIUNIT(*,.PLANEANGLEUNIT.,$,.RADIAN.);
#316=IFCSIUNIT(*,.POWERUNIT.,$,.WATT.);
#317=IFCSIUNIT(*,.PRESSUREUNIT.,$,.PASCAL.);
#318=IFCSIUNIT(*,.RADIOACTIVITYUNIT.,$,.BECQUEREL.);
#319=IFCSIUNIT(*,.SOLIDANGLEUNIT.,$,.STERADIAN.);
#320=IFCSIUNIT(*,.THERMODYNAMICTEMPERATUREUNIT.,$,.KELVIN.);
#321=IFCSIUNIT(*,.TIMEUNIT.,$,.SECOND.);
ENDSEC;
END-ISO-10303-21;

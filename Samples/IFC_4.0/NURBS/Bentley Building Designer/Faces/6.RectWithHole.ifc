ISO-10303-21;
HEADER;
/* Generated by software containing ST-Developer
 * from STEP Tools, Inc. (www.steptools.com) 
 */

FILE_DESCRIPTION(
/* description */ ('ViewDefinition [DesignTransferView]',
'Comment [Comments]',
'Comment [System: OpenBuildings Designer*********** debug build Feb  5
 2020 00:34:15]'),
/* implementation_level */ '2;1');

FILE_NAME(
/* name */ '6.RectWithHole',
/* time_stamp */ '2020-03-10T17:18:46+03:00',
/* author */ ('First Name Last Name'),
/* organization */ ('Organization Name'),
/* preprocessor_version */ 'ST-DEVELOPER v16.13',
/* originating_system */ 'OpenBuildings Designer***********',
/* authorisation */ 'Administrator');

FILE_SCHEMA (('IFC4'));
ENDSEC;

DATA;
#10=IFCBOUNDINGBOX(#4939,12.0000023283064,12.4488640625344,16.1872575582261);
#11=IFCGEOMETRICREPRESENTATIONSUBCONTEXT('Body','Model',*,*,*,*,#13,1.,
 .MODEL_VIEW.,$);
#12=IFCGEOMETRICREPRESENTATIONSUBCONTEXT('Box','Model',*,*,*,*,#13,1.,
 .SKETCH_VIEW.,$);
#13=IFCGEOMETRICREPRESENTATIONCONTEXT($,'Model',3,1.E-6,#4069,#4942);
#14=IFCSHAPEREPRESENTATION(#11,'Body','SurfaceModel',(#25));
#15=IFCSHAPEREPRESENTATION(#12,'Box','BoundingBox',(#10));
#16=IFCPRODUCTDEFINITIONSHAPE($,$,(#14,#15));
#17=IFCRELCONTAINEDINSPATIALSTRUCTURE('2aJi0CW_wcTJthPGyCEMkK',#5005,$,
$,(#20),#18);
#18=IFCBUILDINGSTOREY('0rm7DUqMJhOUcs5v8t1F$L',#5005,'Floor 3',
'Administrative and employee lounge',$,#4063,$,'Floor 3',.ELEMENT.,9.144);
#19=IFCBUILDING('3_IkJInE13hR6Xwo4VKOgh',#5005,'Bldg 1',
'3 Story Building',$,#4062,$,'Bldg 1',.ELEMENT.,0.,0.,#4997);
#20=IFCBUILDINGELEMENTPROXY('1vdpKn9biZ$o4qW2SHbk1Q',#5005,
'Element type: 24','824*bspline surface!Default','',#4064,#16,
'824*bspline surface!Default',.NOTDEFINED.);
#21=IFCSURFACESTYLE($,.BOTH.,(#22));
#22=IFCSURFACESTYLESHADING(#23,0.);
#23=IFCCOLOURRGB($,1.,1.,1.);
#24=IFCPRESENTATIONLAYERWITHSTYLE('Default',$,(#25),$,.U.,.U.,.U.,(#21));
#25=IFCSHELLBASEDSURFACEMODEL((#26));
#26=IFCOPENSHELL((#4056));
#27=IFCFACEBOUND(#604,.T.);
#28=IFCORIENTEDEDGE(*,*,#605,.T.);
#29=IFCORIENTEDEDGE(*,*,#606,.T.);
#30=IFCORIENTEDEDGE(*,*,#607,.T.);
#31=IFCORIENTEDEDGE(*,*,#608,.T.);
#32=IFCORIENTEDEDGE(*,*,#609,.T.);
#33=IFCORIENTEDEDGE(*,*,#610,.T.);
#34=IFCORIENTEDEDGE(*,*,#611,.T.);
#35=IFCORIENTEDEDGE(*,*,#612,.T.);
#36=IFCORIENTEDEDGE(*,*,#613,.T.);
#37=IFCORIENTEDEDGE(*,*,#614,.T.);
#38=IFCORIENTEDEDGE(*,*,#615,.T.);
#39=IFCORIENTEDEDGE(*,*,#616,.T.);
#40=IFCORIENTEDEDGE(*,*,#617,.T.);
#41=IFCORIENTEDEDGE(*,*,#618,.T.);
#42=IFCORIENTEDEDGE(*,*,#619,.T.);
#43=IFCORIENTEDEDGE(*,*,#620,.T.);
#44=IFCORIENTEDEDGE(*,*,#621,.T.);
#45=IFCORIENTEDEDGE(*,*,#622,.T.);
#46=IFCORIENTEDEDGE(*,*,#623,.T.);
#47=IFCORIENTEDEDGE(*,*,#624,.T.);
#48=IFCORIENTEDEDGE(*,*,#625,.T.);
#49=IFCORIENTEDEDGE(*,*,#626,.T.);
#50=IFCORIENTEDEDGE(*,*,#627,.T.);
#51=IFCORIENTEDEDGE(*,*,#628,.T.);
#52=IFCORIENTEDEDGE(*,*,#629,.T.);
#53=IFCORIENTEDEDGE(*,*,#630,.T.);
#54=IFCORIENTEDEDGE(*,*,#631,.T.);
#55=IFCORIENTEDEDGE(*,*,#632,.T.);
#56=IFCORIENTEDEDGE(*,*,#633,.T.);
#57=IFCORIENTEDEDGE(*,*,#634,.T.);
#58=IFCORIENTEDEDGE(*,*,#635,.T.);
#59=IFCORIENTEDEDGE(*,*,#636,.T.);
#60=IFCORIENTEDEDGE(*,*,#637,.T.);
#61=IFCORIENTEDEDGE(*,*,#638,.T.);
#62=IFCORIENTEDEDGE(*,*,#639,.T.);
#63=IFCORIENTEDEDGE(*,*,#640,.T.);
#64=IFCORIENTEDEDGE(*,*,#641,.T.);
#65=IFCORIENTEDEDGE(*,*,#642,.T.);
#66=IFCORIENTEDEDGE(*,*,#643,.T.);
#67=IFCORIENTEDEDGE(*,*,#644,.T.);
#68=IFCORIENTEDEDGE(*,*,#645,.T.);
#69=IFCORIENTEDEDGE(*,*,#646,.T.);
#70=IFCORIENTEDEDGE(*,*,#647,.T.);
#71=IFCORIENTEDEDGE(*,*,#648,.T.);
#72=IFCORIENTEDEDGE(*,*,#649,.T.);
#73=IFCORIENTEDEDGE(*,*,#650,.T.);
#74=IFCORIENTEDEDGE(*,*,#651,.T.);
#75=IFCORIENTEDEDGE(*,*,#652,.T.);
#76=IFCORIENTEDEDGE(*,*,#653,.T.);
#77=IFCORIENTEDEDGE(*,*,#654,.T.);
#78=IFCORIENTEDEDGE(*,*,#655,.T.);
#79=IFCORIENTEDEDGE(*,*,#656,.T.);
#80=IFCORIENTEDEDGE(*,*,#657,.T.);
#81=IFCORIENTEDEDGE(*,*,#658,.T.);
#82=IFCORIENTEDEDGE(*,*,#659,.T.);
#83=IFCORIENTEDEDGE(*,*,#660,.T.);
#84=IFCORIENTEDEDGE(*,*,#661,.T.);
#85=IFCORIENTEDEDGE(*,*,#662,.T.);
#86=IFCORIENTEDEDGE(*,*,#663,.T.);
#87=IFCORIENTEDEDGE(*,*,#664,.T.);
#88=IFCORIENTEDEDGE(*,*,#665,.T.);
#89=IFCORIENTEDEDGE(*,*,#666,.T.);
#90=IFCORIENTEDEDGE(*,*,#667,.T.);
#91=IFCORIENTEDEDGE(*,*,#668,.T.);
#92=IFCORIENTEDEDGE(*,*,#669,.T.);
#93=IFCORIENTEDEDGE(*,*,#670,.T.);
#94=IFCORIENTEDEDGE(*,*,#671,.T.);
#95=IFCORIENTEDEDGE(*,*,#672,.T.);
#96=IFCORIENTEDEDGE(*,*,#673,.T.);
#97=IFCORIENTEDEDGE(*,*,#674,.T.);
#98=IFCORIENTEDEDGE(*,*,#675,.T.);
#99=IFCORIENTEDEDGE(*,*,#676,.T.);
#100=IFCORIENTEDEDGE(*,*,#677,.T.);
#101=IFCORIENTEDEDGE(*,*,#678,.T.);
#102=IFCORIENTEDEDGE(*,*,#679,.T.);
#103=IFCORIENTEDEDGE(*,*,#680,.T.);
#104=IFCORIENTEDEDGE(*,*,#681,.T.);
#105=IFCORIENTEDEDGE(*,*,#682,.T.);
#106=IFCORIENTEDEDGE(*,*,#683,.T.);
#107=IFCORIENTEDEDGE(*,*,#684,.T.);
#108=IFCORIENTEDEDGE(*,*,#685,.T.);
#109=IFCORIENTEDEDGE(*,*,#686,.T.);
#110=IFCORIENTEDEDGE(*,*,#687,.T.);
#111=IFCORIENTEDEDGE(*,*,#688,.T.);
#112=IFCORIENTEDEDGE(*,*,#689,.T.);
#113=IFCORIENTEDEDGE(*,*,#690,.T.);
#114=IFCORIENTEDEDGE(*,*,#691,.T.);
#115=IFCORIENTEDEDGE(*,*,#692,.T.);
#116=IFCORIENTEDEDGE(*,*,#693,.T.);
#117=IFCORIENTEDEDGE(*,*,#694,.T.);
#118=IFCORIENTEDEDGE(*,*,#695,.T.);
#119=IFCORIENTEDEDGE(*,*,#696,.T.);
#120=IFCORIENTEDEDGE(*,*,#697,.T.);
#121=IFCORIENTEDEDGE(*,*,#698,.T.);
#122=IFCORIENTEDEDGE(*,*,#699,.T.);
#123=IFCORIENTEDEDGE(*,*,#700,.T.);
#124=IFCORIENTEDEDGE(*,*,#701,.T.);
#125=IFCORIENTEDEDGE(*,*,#702,.T.);
#126=IFCORIENTEDEDGE(*,*,#703,.T.);
#127=IFCORIENTEDEDGE(*,*,#704,.T.);
#128=IFCORIENTEDEDGE(*,*,#705,.T.);
#129=IFCORIENTEDEDGE(*,*,#706,.T.);
#130=IFCORIENTEDEDGE(*,*,#707,.T.);
#131=IFCORIENTEDEDGE(*,*,#708,.T.);
#132=IFCORIENTEDEDGE(*,*,#709,.T.);
#133=IFCORIENTEDEDGE(*,*,#710,.T.);
#134=IFCORIENTEDEDGE(*,*,#711,.T.);
#135=IFCORIENTEDEDGE(*,*,#712,.T.);
#136=IFCORIENTEDEDGE(*,*,#713,.T.);
#137=IFCORIENTEDEDGE(*,*,#714,.T.);
#138=IFCORIENTEDEDGE(*,*,#715,.T.);
#139=IFCORIENTEDEDGE(*,*,#716,.T.);
#140=IFCORIENTEDEDGE(*,*,#717,.T.);
#141=IFCORIENTEDEDGE(*,*,#718,.T.);
#142=IFCORIENTEDEDGE(*,*,#719,.T.);
#143=IFCORIENTEDEDGE(*,*,#720,.T.);
#144=IFCORIENTEDEDGE(*,*,#721,.T.);
#145=IFCORIENTEDEDGE(*,*,#722,.T.);
#146=IFCORIENTEDEDGE(*,*,#723,.T.);
#147=IFCORIENTEDEDGE(*,*,#724,.T.);
#148=IFCORIENTEDEDGE(*,*,#725,.T.);
#149=IFCORIENTEDEDGE(*,*,#726,.T.);
#150=IFCORIENTEDEDGE(*,*,#727,.T.);
#151=IFCORIENTEDEDGE(*,*,#728,.T.);
#152=IFCORIENTEDEDGE(*,*,#729,.T.);
#153=IFCORIENTEDEDGE(*,*,#730,.T.);
#154=IFCORIENTEDEDGE(*,*,#731,.T.);
#155=IFCORIENTEDEDGE(*,*,#732,.T.);
#156=IFCORIENTEDEDGE(*,*,#733,.T.);
#157=IFCORIENTEDEDGE(*,*,#734,.T.);
#158=IFCORIENTEDEDGE(*,*,#735,.T.);
#159=IFCORIENTEDEDGE(*,*,#736,.T.);
#160=IFCORIENTEDEDGE(*,*,#737,.T.);
#161=IFCORIENTEDEDGE(*,*,#738,.T.);
#162=IFCORIENTEDEDGE(*,*,#739,.T.);
#163=IFCORIENTEDEDGE(*,*,#740,.T.);
#164=IFCORIENTEDEDGE(*,*,#741,.T.);
#165=IFCORIENTEDEDGE(*,*,#742,.T.);
#166=IFCORIENTEDEDGE(*,*,#743,.T.);
#167=IFCORIENTEDEDGE(*,*,#744,.T.);
#168=IFCORIENTEDEDGE(*,*,#745,.T.);
#169=IFCORIENTEDEDGE(*,*,#746,.T.);
#170=IFCORIENTEDEDGE(*,*,#747,.T.);
#171=IFCORIENTEDEDGE(*,*,#748,.T.);
#172=IFCORIENTEDEDGE(*,*,#749,.T.);
#173=IFCORIENTEDEDGE(*,*,#750,.T.);
#174=IFCORIENTEDEDGE(*,*,#751,.T.);
#175=IFCORIENTEDEDGE(*,*,#752,.T.);
#176=IFCORIENTEDEDGE(*,*,#753,.T.);
#177=IFCORIENTEDEDGE(*,*,#754,.T.);
#178=IFCORIENTEDEDGE(*,*,#755,.T.);
#179=IFCORIENTEDEDGE(*,*,#756,.T.);
#180=IFCORIENTEDEDGE(*,*,#757,.T.);
#181=IFCORIENTEDEDGE(*,*,#758,.T.);
#182=IFCORIENTEDEDGE(*,*,#759,.T.);
#183=IFCORIENTEDEDGE(*,*,#760,.T.);
#184=IFCORIENTEDEDGE(*,*,#761,.T.);
#185=IFCORIENTEDEDGE(*,*,#762,.T.);
#186=IFCORIENTEDEDGE(*,*,#763,.T.);
#187=IFCORIENTEDEDGE(*,*,#764,.T.);
#188=IFCORIENTEDEDGE(*,*,#765,.T.);
#189=IFCORIENTEDEDGE(*,*,#766,.T.);
#190=IFCORIENTEDEDGE(*,*,#767,.T.);
#191=IFCORIENTEDEDGE(*,*,#768,.T.);
#192=IFCORIENTEDEDGE(*,*,#769,.T.);
#193=IFCORIENTEDEDGE(*,*,#770,.T.);
#194=IFCORIENTEDEDGE(*,*,#771,.T.);
#195=IFCORIENTEDEDGE(*,*,#772,.T.);
#196=IFCORIENTEDEDGE(*,*,#773,.T.);
#197=IFCORIENTEDEDGE(*,*,#774,.T.);
#198=IFCORIENTEDEDGE(*,*,#775,.T.);
#199=IFCORIENTEDEDGE(*,*,#776,.T.);
#200=IFCORIENTEDEDGE(*,*,#777,.T.);
#201=IFCORIENTEDEDGE(*,*,#778,.T.);
#202=IFCORIENTEDEDGE(*,*,#779,.T.);
#203=IFCORIENTEDEDGE(*,*,#780,.T.);
#204=IFCORIENTEDEDGE(*,*,#781,.T.);
#205=IFCORIENTEDEDGE(*,*,#782,.T.);
#206=IFCORIENTEDEDGE(*,*,#783,.T.);
#207=IFCORIENTEDEDGE(*,*,#784,.T.);
#208=IFCORIENTEDEDGE(*,*,#785,.T.);
#209=IFCORIENTEDEDGE(*,*,#786,.T.);
#210=IFCORIENTEDEDGE(*,*,#787,.T.);
#211=IFCORIENTEDEDGE(*,*,#788,.T.);
#212=IFCORIENTEDEDGE(*,*,#789,.T.);
#213=IFCORIENTEDEDGE(*,*,#790,.T.);
#214=IFCORIENTEDEDGE(*,*,#791,.T.);
#215=IFCORIENTEDEDGE(*,*,#792,.T.);
#216=IFCORIENTEDEDGE(*,*,#793,.T.);
#217=IFCORIENTEDEDGE(*,*,#794,.T.);
#218=IFCORIENTEDEDGE(*,*,#795,.T.);
#219=IFCORIENTEDEDGE(*,*,#796,.T.);
#220=IFCORIENTEDEDGE(*,*,#797,.T.);
#221=IFCORIENTEDEDGE(*,*,#798,.T.);
#222=IFCORIENTEDEDGE(*,*,#799,.T.);
#223=IFCORIENTEDEDGE(*,*,#800,.T.);
#224=IFCORIENTEDEDGE(*,*,#801,.T.);
#225=IFCORIENTEDEDGE(*,*,#802,.T.);
#226=IFCORIENTEDEDGE(*,*,#803,.T.);
#227=IFCORIENTEDEDGE(*,*,#804,.T.);
#228=IFCORIENTEDEDGE(*,*,#805,.T.);
#229=IFCORIENTEDEDGE(*,*,#806,.T.);
#230=IFCORIENTEDEDGE(*,*,#807,.T.);
#231=IFCORIENTEDEDGE(*,*,#808,.T.);
#232=IFCORIENTEDEDGE(*,*,#809,.T.);
#233=IFCORIENTEDEDGE(*,*,#810,.T.);
#234=IFCORIENTEDEDGE(*,*,#811,.T.);
#235=IFCORIENTEDEDGE(*,*,#812,.T.);
#236=IFCORIENTEDEDGE(*,*,#813,.T.);
#237=IFCORIENTEDEDGE(*,*,#814,.T.);
#238=IFCORIENTEDEDGE(*,*,#815,.T.);
#239=IFCORIENTEDEDGE(*,*,#816,.T.);
#240=IFCORIENTEDEDGE(*,*,#817,.T.);
#241=IFCORIENTEDEDGE(*,*,#818,.T.);
#242=IFCORIENTEDEDGE(*,*,#819,.T.);
#243=IFCORIENTEDEDGE(*,*,#820,.T.);
#244=IFCORIENTEDEDGE(*,*,#821,.T.);
#245=IFCORIENTEDEDGE(*,*,#822,.T.);
#246=IFCORIENTEDEDGE(*,*,#823,.T.);
#247=IFCORIENTEDEDGE(*,*,#824,.T.);
#248=IFCORIENTEDEDGE(*,*,#825,.T.);
#249=IFCORIENTEDEDGE(*,*,#826,.T.);
#250=IFCORIENTEDEDGE(*,*,#827,.T.);
#251=IFCORIENTEDEDGE(*,*,#828,.T.);
#252=IFCORIENTEDEDGE(*,*,#829,.T.);
#253=IFCORIENTEDEDGE(*,*,#830,.T.);
#254=IFCORIENTEDEDGE(*,*,#831,.T.);
#255=IFCORIENTEDEDGE(*,*,#832,.T.);
#256=IFCORIENTEDEDGE(*,*,#833,.T.);
#257=IFCORIENTEDEDGE(*,*,#834,.T.);
#258=IFCORIENTEDEDGE(*,*,#835,.T.);
#259=IFCORIENTEDEDGE(*,*,#836,.T.);
#260=IFCORIENTEDEDGE(*,*,#837,.T.);
#261=IFCORIENTEDEDGE(*,*,#838,.T.);
#262=IFCORIENTEDEDGE(*,*,#839,.T.);
#263=IFCORIENTEDEDGE(*,*,#840,.T.);
#264=IFCORIENTEDEDGE(*,*,#841,.T.);
#265=IFCORIENTEDEDGE(*,*,#842,.T.);
#266=IFCORIENTEDEDGE(*,*,#843,.T.);
#267=IFCORIENTEDEDGE(*,*,#844,.T.);
#268=IFCORIENTEDEDGE(*,*,#845,.T.);
#269=IFCORIENTEDEDGE(*,*,#846,.T.);
#270=IFCORIENTEDEDGE(*,*,#847,.T.);
#271=IFCORIENTEDEDGE(*,*,#848,.T.);
#272=IFCORIENTEDEDGE(*,*,#849,.T.);
#273=IFCORIENTEDEDGE(*,*,#850,.T.);
#274=IFCORIENTEDEDGE(*,*,#851,.T.);
#275=IFCORIENTEDEDGE(*,*,#852,.T.);
#276=IFCORIENTEDEDGE(*,*,#853,.T.);
#277=IFCORIENTEDEDGE(*,*,#854,.T.);
#278=IFCORIENTEDEDGE(*,*,#855,.T.);
#279=IFCORIENTEDEDGE(*,*,#856,.T.);
#280=IFCORIENTEDEDGE(*,*,#857,.T.);
#281=IFCORIENTEDEDGE(*,*,#858,.T.);
#282=IFCORIENTEDEDGE(*,*,#859,.T.);
#283=IFCORIENTEDEDGE(*,*,#860,.T.);
#284=IFCORIENTEDEDGE(*,*,#861,.T.);
#285=IFCORIENTEDEDGE(*,*,#862,.T.);
#286=IFCORIENTEDEDGE(*,*,#863,.T.);
#287=IFCORIENTEDEDGE(*,*,#864,.T.);
#288=IFCORIENTEDEDGE(*,*,#865,.T.);
#289=IFCORIENTEDEDGE(*,*,#866,.T.);
#290=IFCORIENTEDEDGE(*,*,#867,.T.);
#291=IFCORIENTEDEDGE(*,*,#868,.T.);
#292=IFCORIENTEDEDGE(*,*,#869,.T.);
#293=IFCORIENTEDEDGE(*,*,#870,.T.);
#294=IFCORIENTEDEDGE(*,*,#871,.T.);
#295=IFCORIENTEDEDGE(*,*,#872,.T.);
#296=IFCORIENTEDEDGE(*,*,#873,.T.);
#297=IFCORIENTEDEDGE(*,*,#874,.T.);
#298=IFCORIENTEDEDGE(*,*,#875,.T.);
#299=IFCORIENTEDEDGE(*,*,#876,.T.);
#300=IFCORIENTEDEDGE(*,*,#877,.T.);
#301=IFCORIENTEDEDGE(*,*,#878,.T.);
#302=IFCORIENTEDEDGE(*,*,#879,.T.);
#303=IFCORIENTEDEDGE(*,*,#880,.T.);
#304=IFCORIENTEDEDGE(*,*,#881,.T.);
#305=IFCORIENTEDEDGE(*,*,#882,.T.);
#306=IFCORIENTEDEDGE(*,*,#883,.T.);
#307=IFCORIENTEDEDGE(*,*,#884,.T.);
#308=IFCORIENTEDEDGE(*,*,#885,.T.);
#309=IFCORIENTEDEDGE(*,*,#886,.T.);
#310=IFCORIENTEDEDGE(*,*,#887,.T.);
#311=IFCORIENTEDEDGE(*,*,#888,.T.);
#312=IFCORIENTEDEDGE(*,*,#889,.T.);
#313=IFCORIENTEDEDGE(*,*,#890,.T.);
#314=IFCORIENTEDEDGE(*,*,#891,.T.);
#315=IFCORIENTEDEDGE(*,*,#892,.T.);
#316=IFCORIENTEDEDGE(*,*,#893,.T.);
#317=IFCORIENTEDEDGE(*,*,#894,.T.);
#318=IFCORIENTEDEDGE(*,*,#895,.T.);
#319=IFCORIENTEDEDGE(*,*,#896,.T.);
#320=IFCORIENTEDEDGE(*,*,#897,.T.);
#321=IFCORIENTEDEDGE(*,*,#898,.T.);
#322=IFCORIENTEDEDGE(*,*,#899,.T.);
#323=IFCORIENTEDEDGE(*,*,#900,.T.);
#324=IFCORIENTEDEDGE(*,*,#901,.T.);
#325=IFCORIENTEDEDGE(*,*,#902,.T.);
#326=IFCORIENTEDEDGE(*,*,#903,.T.);
#327=IFCORIENTEDEDGE(*,*,#904,.T.);
#328=IFCORIENTEDEDGE(*,*,#905,.T.);
#329=IFCORIENTEDEDGE(*,*,#906,.T.);
#330=IFCORIENTEDEDGE(*,*,#907,.T.);
#331=IFCORIENTEDEDGE(*,*,#908,.T.);
#332=IFCORIENTEDEDGE(*,*,#909,.T.);
#333=IFCORIENTEDEDGE(*,*,#910,.T.);
#334=IFCORIENTEDEDGE(*,*,#911,.T.);
#335=IFCORIENTEDEDGE(*,*,#912,.T.);
#336=IFCORIENTEDEDGE(*,*,#913,.T.);
#337=IFCORIENTEDEDGE(*,*,#914,.T.);
#338=IFCORIENTEDEDGE(*,*,#915,.T.);
#339=IFCORIENTEDEDGE(*,*,#916,.T.);
#340=IFCORIENTEDEDGE(*,*,#917,.T.);
#341=IFCORIENTEDEDGE(*,*,#918,.T.);
#342=IFCORIENTEDEDGE(*,*,#919,.T.);
#343=IFCORIENTEDEDGE(*,*,#920,.T.);
#344=IFCORIENTEDEDGE(*,*,#921,.T.);
#345=IFCORIENTEDEDGE(*,*,#922,.T.);
#346=IFCORIENTEDEDGE(*,*,#923,.T.);
#347=IFCORIENTEDEDGE(*,*,#924,.T.);
#348=IFCORIENTEDEDGE(*,*,#925,.T.);
#349=IFCORIENTEDEDGE(*,*,#926,.T.);
#350=IFCORIENTEDEDGE(*,*,#927,.T.);
#351=IFCORIENTEDEDGE(*,*,#928,.T.);
#352=IFCORIENTEDEDGE(*,*,#929,.T.);
#353=IFCORIENTEDEDGE(*,*,#930,.T.);
#354=IFCORIENTEDEDGE(*,*,#931,.T.);
#355=IFCORIENTEDEDGE(*,*,#932,.T.);
#356=IFCORIENTEDEDGE(*,*,#933,.T.);
#357=IFCORIENTEDEDGE(*,*,#934,.T.);
#358=IFCORIENTEDEDGE(*,*,#935,.T.);
#359=IFCORIENTEDEDGE(*,*,#936,.T.);
#360=IFCORIENTEDEDGE(*,*,#937,.T.);
#361=IFCORIENTEDEDGE(*,*,#938,.T.);
#362=IFCORIENTEDEDGE(*,*,#939,.T.);
#363=IFCORIENTEDEDGE(*,*,#940,.T.);
#364=IFCORIENTEDEDGE(*,*,#941,.T.);
#365=IFCORIENTEDEDGE(*,*,#942,.T.);
#366=IFCORIENTEDEDGE(*,*,#943,.T.);
#367=IFCORIENTEDEDGE(*,*,#944,.T.);
#368=IFCORIENTEDEDGE(*,*,#945,.T.);
#369=IFCORIENTEDEDGE(*,*,#946,.T.);
#370=IFCORIENTEDEDGE(*,*,#947,.T.);
#371=IFCORIENTEDEDGE(*,*,#948,.T.);
#372=IFCORIENTEDEDGE(*,*,#949,.T.);
#373=IFCORIENTEDEDGE(*,*,#950,.T.);
#374=IFCORIENTEDEDGE(*,*,#951,.T.);
#375=IFCORIENTEDEDGE(*,*,#952,.T.);
#376=IFCORIENTEDEDGE(*,*,#953,.T.);
#377=IFCORIENTEDEDGE(*,*,#954,.T.);
#378=IFCORIENTEDEDGE(*,*,#955,.T.);
#379=IFCORIENTEDEDGE(*,*,#956,.T.);
#380=IFCORIENTEDEDGE(*,*,#957,.T.);
#381=IFCORIENTEDEDGE(*,*,#958,.T.);
#382=IFCORIENTEDEDGE(*,*,#959,.T.);
#383=IFCORIENTEDEDGE(*,*,#960,.T.);
#384=IFCORIENTEDEDGE(*,*,#961,.T.);
#385=IFCORIENTEDEDGE(*,*,#962,.T.);
#386=IFCORIENTEDEDGE(*,*,#963,.T.);
#387=IFCORIENTEDEDGE(*,*,#964,.T.);
#388=IFCORIENTEDEDGE(*,*,#965,.T.);
#389=IFCORIENTEDEDGE(*,*,#966,.T.);
#390=IFCORIENTEDEDGE(*,*,#967,.T.);
#391=IFCORIENTEDEDGE(*,*,#968,.T.);
#392=IFCORIENTEDEDGE(*,*,#969,.T.);
#393=IFCORIENTEDEDGE(*,*,#970,.T.);
#394=IFCORIENTEDEDGE(*,*,#971,.T.);
#395=IFCORIENTEDEDGE(*,*,#972,.T.);
#396=IFCORIENTEDEDGE(*,*,#973,.T.);
#397=IFCORIENTEDEDGE(*,*,#974,.T.);
#398=IFCORIENTEDEDGE(*,*,#975,.T.);
#399=IFCORIENTEDEDGE(*,*,#976,.T.);
#400=IFCORIENTEDEDGE(*,*,#977,.T.);
#401=IFCORIENTEDEDGE(*,*,#978,.T.);
#402=IFCORIENTEDEDGE(*,*,#979,.T.);
#403=IFCORIENTEDEDGE(*,*,#980,.T.);
#404=IFCORIENTEDEDGE(*,*,#981,.T.);
#405=IFCORIENTEDEDGE(*,*,#982,.T.);
#406=IFCORIENTEDEDGE(*,*,#983,.T.);
#407=IFCORIENTEDEDGE(*,*,#984,.T.);
#408=IFCORIENTEDEDGE(*,*,#985,.T.);
#409=IFCORIENTEDEDGE(*,*,#986,.T.);
#410=IFCORIENTEDEDGE(*,*,#987,.T.);
#411=IFCORIENTEDEDGE(*,*,#988,.T.);
#412=IFCORIENTEDEDGE(*,*,#989,.T.);
#413=IFCORIENTEDEDGE(*,*,#990,.T.);
#414=IFCORIENTEDEDGE(*,*,#991,.T.);
#415=IFCORIENTEDEDGE(*,*,#992,.T.);
#416=IFCORIENTEDEDGE(*,*,#993,.T.);
#417=IFCORIENTEDEDGE(*,*,#994,.T.);
#418=IFCORIENTEDEDGE(*,*,#995,.T.);
#419=IFCORIENTEDEDGE(*,*,#996,.T.);
#420=IFCORIENTEDEDGE(*,*,#997,.T.);
#421=IFCORIENTEDEDGE(*,*,#998,.T.);
#422=IFCORIENTEDEDGE(*,*,#999,.T.);
#423=IFCORIENTEDEDGE(*,*,#1000,.T.);
#424=IFCORIENTEDEDGE(*,*,#1001,.T.);
#425=IFCORIENTEDEDGE(*,*,#1002,.T.);
#426=IFCORIENTEDEDGE(*,*,#1003,.T.);
#427=IFCORIENTEDEDGE(*,*,#1004,.T.);
#428=IFCORIENTEDEDGE(*,*,#1005,.T.);
#429=IFCORIENTEDEDGE(*,*,#1006,.T.);
#430=IFCORIENTEDEDGE(*,*,#1007,.T.);
#431=IFCORIENTEDEDGE(*,*,#1008,.T.);
#432=IFCORIENTEDEDGE(*,*,#1009,.T.);
#433=IFCORIENTEDEDGE(*,*,#1010,.T.);
#434=IFCORIENTEDEDGE(*,*,#1011,.T.);
#435=IFCORIENTEDEDGE(*,*,#1012,.T.);
#436=IFCORIENTEDEDGE(*,*,#1013,.T.);
#437=IFCORIENTEDEDGE(*,*,#1014,.T.);
#438=IFCORIENTEDEDGE(*,*,#1015,.T.);
#439=IFCORIENTEDEDGE(*,*,#1016,.T.);
#440=IFCORIENTEDEDGE(*,*,#1017,.T.);
#441=IFCORIENTEDEDGE(*,*,#1018,.T.);
#442=IFCORIENTEDEDGE(*,*,#1019,.T.);
#443=IFCORIENTEDEDGE(*,*,#1020,.T.);
#444=IFCORIENTEDEDGE(*,*,#1021,.T.);
#445=IFCORIENTEDEDGE(*,*,#1022,.T.);
#446=IFCORIENTEDEDGE(*,*,#1023,.T.);
#447=IFCORIENTEDEDGE(*,*,#1024,.T.);
#448=IFCORIENTEDEDGE(*,*,#1025,.T.);
#449=IFCORIENTEDEDGE(*,*,#1026,.T.);
#450=IFCORIENTEDEDGE(*,*,#1027,.T.);
#451=IFCORIENTEDEDGE(*,*,#1028,.T.);
#452=IFCORIENTEDEDGE(*,*,#1029,.T.);
#453=IFCORIENTEDEDGE(*,*,#1030,.T.);
#454=IFCORIENTEDEDGE(*,*,#1031,.T.);
#455=IFCORIENTEDEDGE(*,*,#1032,.T.);
#456=IFCORIENTEDEDGE(*,*,#1033,.T.);
#457=IFCORIENTEDEDGE(*,*,#1034,.T.);
#458=IFCORIENTEDEDGE(*,*,#1035,.T.);
#459=IFCORIENTEDEDGE(*,*,#1036,.T.);
#460=IFCORIENTEDEDGE(*,*,#1037,.T.);
#461=IFCORIENTEDEDGE(*,*,#1038,.T.);
#462=IFCORIENTEDEDGE(*,*,#1039,.T.);
#463=IFCORIENTEDEDGE(*,*,#1040,.T.);
#464=IFCORIENTEDEDGE(*,*,#1041,.T.);
#465=IFCORIENTEDEDGE(*,*,#1042,.T.);
#466=IFCORIENTEDEDGE(*,*,#1043,.T.);
#467=IFCORIENTEDEDGE(*,*,#1044,.T.);
#468=IFCORIENTEDEDGE(*,*,#1045,.T.);
#469=IFCORIENTEDEDGE(*,*,#1046,.T.);
#470=IFCORIENTEDEDGE(*,*,#1047,.T.);
#471=IFCORIENTEDEDGE(*,*,#1048,.T.);
#472=IFCORIENTEDEDGE(*,*,#1049,.T.);
#473=IFCORIENTEDEDGE(*,*,#1050,.T.);
#474=IFCORIENTEDEDGE(*,*,#1051,.T.);
#475=IFCORIENTEDEDGE(*,*,#1052,.T.);
#476=IFCORIENTEDEDGE(*,*,#1053,.T.);
#477=IFCORIENTEDEDGE(*,*,#1054,.T.);
#478=IFCORIENTEDEDGE(*,*,#1055,.T.);
#479=IFCORIENTEDEDGE(*,*,#1056,.T.);
#480=IFCORIENTEDEDGE(*,*,#1057,.T.);
#481=IFCORIENTEDEDGE(*,*,#1058,.T.);
#482=IFCORIENTEDEDGE(*,*,#1059,.T.);
#483=IFCORIENTEDEDGE(*,*,#1060,.T.);
#484=IFCORIENTEDEDGE(*,*,#1061,.T.);
#485=IFCORIENTEDEDGE(*,*,#1062,.T.);
#486=IFCORIENTEDEDGE(*,*,#1063,.T.);
#487=IFCORIENTEDEDGE(*,*,#1064,.T.);
#488=IFCORIENTEDEDGE(*,*,#1065,.T.);
#489=IFCORIENTEDEDGE(*,*,#1066,.T.);
#490=IFCORIENTEDEDGE(*,*,#1067,.T.);
#491=IFCORIENTEDEDGE(*,*,#1068,.T.);
#492=IFCORIENTEDEDGE(*,*,#1069,.T.);
#493=IFCORIENTEDEDGE(*,*,#1070,.T.);
#494=IFCORIENTEDEDGE(*,*,#1071,.T.);
#495=IFCORIENTEDEDGE(*,*,#1072,.T.);
#496=IFCORIENTEDEDGE(*,*,#1073,.T.);
#497=IFCORIENTEDEDGE(*,*,#1074,.T.);
#498=IFCORIENTEDEDGE(*,*,#1075,.T.);
#499=IFCORIENTEDEDGE(*,*,#1076,.T.);
#500=IFCORIENTEDEDGE(*,*,#1077,.T.);
#501=IFCORIENTEDEDGE(*,*,#1078,.T.);
#502=IFCORIENTEDEDGE(*,*,#1079,.T.);
#503=IFCORIENTEDEDGE(*,*,#1080,.T.);
#504=IFCORIENTEDEDGE(*,*,#1081,.T.);
#505=IFCORIENTEDEDGE(*,*,#1082,.T.);
#506=IFCORIENTEDEDGE(*,*,#1083,.T.);
#507=IFCORIENTEDEDGE(*,*,#1084,.T.);
#508=IFCORIENTEDEDGE(*,*,#1085,.T.);
#509=IFCORIENTEDEDGE(*,*,#1086,.T.);
#510=IFCORIENTEDEDGE(*,*,#1087,.T.);
#511=IFCORIENTEDEDGE(*,*,#1088,.T.);
#512=IFCORIENTEDEDGE(*,*,#1089,.T.);
#513=IFCORIENTEDEDGE(*,*,#1090,.T.);
#514=IFCORIENTEDEDGE(*,*,#1091,.T.);
#515=IFCORIENTEDEDGE(*,*,#1092,.T.);
#516=IFCORIENTEDEDGE(*,*,#1093,.T.);
#517=IFCORIENTEDEDGE(*,*,#1094,.T.);
#518=IFCORIENTEDEDGE(*,*,#1095,.T.);
#519=IFCORIENTEDEDGE(*,*,#1096,.T.);
#520=IFCORIENTEDEDGE(*,*,#1097,.T.);
#521=IFCORIENTEDEDGE(*,*,#1098,.T.);
#522=IFCORIENTEDEDGE(*,*,#1099,.T.);
#523=IFCORIENTEDEDGE(*,*,#1100,.T.);
#524=IFCORIENTEDEDGE(*,*,#1101,.T.);
#525=IFCORIENTEDEDGE(*,*,#1102,.T.);
#526=IFCORIENTEDEDGE(*,*,#1103,.T.);
#527=IFCORIENTEDEDGE(*,*,#1104,.T.);
#528=IFCORIENTEDEDGE(*,*,#1105,.T.);
#529=IFCORIENTEDEDGE(*,*,#1106,.T.);
#530=IFCORIENTEDEDGE(*,*,#1107,.T.);
#531=IFCORIENTEDEDGE(*,*,#1108,.T.);
#532=IFCORIENTEDEDGE(*,*,#1109,.T.);
#533=IFCORIENTEDEDGE(*,*,#1110,.T.);
#534=IFCORIENTEDEDGE(*,*,#1111,.T.);
#535=IFCORIENTEDEDGE(*,*,#1112,.T.);
#536=IFCORIENTEDEDGE(*,*,#1113,.T.);
#537=IFCORIENTEDEDGE(*,*,#1114,.T.);
#538=IFCORIENTEDEDGE(*,*,#1115,.T.);
#539=IFCORIENTEDEDGE(*,*,#1116,.T.);
#540=IFCORIENTEDEDGE(*,*,#1117,.T.);
#541=IFCORIENTEDEDGE(*,*,#1118,.T.);
#542=IFCORIENTEDEDGE(*,*,#1119,.T.);
#543=IFCORIENTEDEDGE(*,*,#1120,.T.);
#544=IFCORIENTEDEDGE(*,*,#1121,.T.);
#545=IFCORIENTEDEDGE(*,*,#1122,.T.);
#546=IFCORIENTEDEDGE(*,*,#1123,.T.);
#547=IFCORIENTEDEDGE(*,*,#1124,.T.);
#548=IFCORIENTEDEDGE(*,*,#1125,.T.);
#549=IFCORIENTEDEDGE(*,*,#1126,.T.);
#550=IFCORIENTEDEDGE(*,*,#1127,.T.);
#551=IFCORIENTEDEDGE(*,*,#1128,.T.);
#552=IFCORIENTEDEDGE(*,*,#1129,.T.);
#553=IFCORIENTEDEDGE(*,*,#1130,.T.);
#554=IFCORIENTEDEDGE(*,*,#1131,.T.);
#555=IFCORIENTEDEDGE(*,*,#1132,.T.);
#556=IFCORIENTEDEDGE(*,*,#1133,.T.);
#557=IFCORIENTEDEDGE(*,*,#1134,.T.);
#558=IFCORIENTEDEDGE(*,*,#1135,.T.);
#559=IFCORIENTEDEDGE(*,*,#1136,.T.);
#560=IFCORIENTEDEDGE(*,*,#1137,.T.);
#561=IFCORIENTEDEDGE(*,*,#1138,.T.);
#562=IFCORIENTEDEDGE(*,*,#1139,.T.);
#563=IFCORIENTEDEDGE(*,*,#1140,.T.);
#564=IFCORIENTEDEDGE(*,*,#1141,.T.);
#565=IFCORIENTEDEDGE(*,*,#1142,.T.);
#566=IFCORIENTEDEDGE(*,*,#1143,.T.);
#567=IFCORIENTEDEDGE(*,*,#1144,.T.);
#568=IFCORIENTEDEDGE(*,*,#1145,.T.);
#569=IFCORIENTEDEDGE(*,*,#1146,.T.);
#570=IFCORIENTEDEDGE(*,*,#1147,.T.);
#571=IFCORIENTEDEDGE(*,*,#1148,.T.);
#572=IFCORIENTEDEDGE(*,*,#1149,.T.);
#573=IFCORIENTEDEDGE(*,*,#1150,.T.);
#574=IFCORIENTEDEDGE(*,*,#1151,.T.);
#575=IFCORIENTEDEDGE(*,*,#1152,.T.);
#576=IFCORIENTEDEDGE(*,*,#1153,.T.);
#577=IFCORIENTEDEDGE(*,*,#1154,.T.);
#578=IFCORIENTEDEDGE(*,*,#1155,.T.);
#579=IFCORIENTEDEDGE(*,*,#1156,.T.);
#580=IFCORIENTEDEDGE(*,*,#1157,.T.);
#581=IFCORIENTEDEDGE(*,*,#1158,.T.);
#582=IFCORIENTEDEDGE(*,*,#1159,.T.);
#583=IFCORIENTEDEDGE(*,*,#1160,.T.);
#584=IFCORIENTEDEDGE(*,*,#1161,.T.);
#585=IFCORIENTEDEDGE(*,*,#1162,.T.);
#586=IFCORIENTEDEDGE(*,*,#1163,.T.);
#587=IFCORIENTEDEDGE(*,*,#1164,.T.);
#588=IFCORIENTEDEDGE(*,*,#1165,.T.);
#589=IFCORIENTEDEDGE(*,*,#1166,.T.);
#590=IFCORIENTEDEDGE(*,*,#1167,.T.);
#591=IFCORIENTEDEDGE(*,*,#1168,.T.);
#592=IFCORIENTEDEDGE(*,*,#1169,.T.);
#593=IFCORIENTEDEDGE(*,*,#1170,.T.);
#594=IFCORIENTEDEDGE(*,*,#1171,.T.);
#595=IFCORIENTEDEDGE(*,*,#1172,.T.);
#596=IFCORIENTEDEDGE(*,*,#1173,.T.);
#597=IFCORIENTEDEDGE(*,*,#1174,.T.);
#598=IFCORIENTEDEDGE(*,*,#1175,.T.);
#599=IFCORIENTEDEDGE(*,*,#1176,.T.);
#600=IFCORIENTEDEDGE(*,*,#1177,.T.);
#601=IFCORIENTEDEDGE(*,*,#1178,.T.);
#602=IFCORIENTEDEDGE(*,*,#1179,.T.);
#603=IFCEDGELOOP((#28,#29,#30,#31,#32,#33,#34,#35,#36,#37,#38,#39,#40,#41,
#42,#43,#44,#45,#46,#47,#48,#49,#50,#51,#52,#53,#54,#55,#56,#57,#58,#59,
#60,#61,#62,#63,#64,#65,#66,#67,#68,#69,#70,#71,#72,#73,#74,#75,#76,#77,
#78,#79,#80,#81,#82,#83,#84,#85,#86,#87,#88,#89,#90,#91,#92,#93,#94,#95,
#96,#97,#98,#99,#100,#101,#102,#103,#104,#105,#106,#107,#108,#109,#110,
#111,#112,#113,#114,#115,#116,#117,#118,#119,#120,#121,#122,#123,#124,#125,
#126,#127,#128,#129,#130,#131,#132,#133,#134,#135,#136,#137,#138,#139,#140,
#141,#142,#143,#144,#145,#146,#147,#148,#149,#150,#151,#152,#153,#154,#155,
#156,#157,#158,#159,#160,#161,#162,#163,#164,#165,#166,#167,#168,#169,#170,
#171,#172,#173,#174,#175,#176,#177,#178,#179,#180,#181,#182,#183,#184,#185,
#186,#187,#188,#189,#190,#191,#192,#193,#194,#195,#196,#197,#198,#199,#200,
#201,#202,#203,#204,#205,#206,#207,#208,#209,#210,#211,#212,#213,#214,#215,
#216,#217,#218,#219,#220,#221,#222,#223,#224,#225,#226,#227,#228,#229,#230,
#231,#232,#233,#234,#235,#236,#237,#238,#239,#240,#241,#242,#243,#244,#245,
#246,#247,#248,#249,#250,#251,#252,#253,#254,#255,#256,#257,#258,#259,#260,
#261,#262,#263,#264,#265,#266,#267,#268,#269,#270,#271,#272,#273,#274,#275,
#276,#277,#278,#279,#280,#281,#282,#283,#284,#285,#286,#287,#288,#289,#290,
#291,#292,#293,#294,#295,#296,#297,#298,#299,#300,#301,#302,#303,#304,#305,
#306,#307,#308,#309,#310,#311,#312,#313,#314,#315,#316,#317,#318,#319,#320,
#321,#322,#323,#324,#325,#326,#327,#328,#329,#330,#331,#332,#333,#334,#335,
#336,#337,#338,#339,#340,#341,#342,#343,#344,#345,#346,#347,#348,#349,#350,
#351,#352,#353,#354,#355,#356,#357,#358,#359,#360,#361,#362,#363,#364,#365,
#366,#367,#368,#369,#370,#371,#372,#373,#374,#375,#376,#377,#378,#379,#380,
#381,#382,#383,#384,#385,#386,#387,#388,#389,#390,#391,#392,#393,#394,#395,
#396,#397,#398,#399,#400,#401,#402,#403,#404,#405,#406,#407));
#604=IFCEDGELOOP((#408,#409,#410,#411,#412,#413,#414,#415,#416,#417,#418,
#419,#420,#421,#422,#423,#424,#425,#426,#427,#428,#429,#430,#431,#432,#433,
#434,#435,#436,#437,#438,#439,#440,#441,#442,#443,#444,#445,#446,#447,#448,
#449,#450,#451,#452,#453,#454,#455,#456,#457,#458,#459,#460,#461,#462,#463,
#464,#465,#466,#467,#468,#469,#470,#471,#472,#473,#474,#475,#476,#477,#478,
#479,#480,#481,#482,#483,#484,#485,#486,#487,#488,#489,#490,#491,#492,#493,
#494,#495,#496,#497,#498,#499,#500,#501,#502,#503,#504,#505,#506,#507,#508,
#509,#510,#511,#512,#513,#514,#515,#516,#517,#518,#519,#520,#521,#522,#523,
#524,#525,#526,#527,#528,#529,#530,#531,#532,#533,#534,#535,#536,#537,#538,
#539,#540,#541,#542,#543,#544,#545,#546,#547,#548,#549,#550,#551,#552,#553,
#554,#555,#556,#557,#558,#559,#560,#561,#562,#563,#564,#565,#566,#567,#568,
#569,#570,#571,#572,#573,#574,#575,#576,#577,#578,#579,#580,#581,#582,#583,
#584,#585,#586,#587,#588,#589,#590,#591,#592,#593,#594,#595,#596,#597,#598,
#599,#600,#601,#602));
#605=IFCEDGECURVE(#1180,#1181,#1755,.T.);
#606=IFCEDGECURVE(#1181,#1182,#1756,.T.);
#607=IFCEDGECURVE(#1182,#1183,#1757,.T.);
#608=IFCEDGECURVE(#1183,#1184,#1758,.T.);
#609=IFCEDGECURVE(#1184,#1185,#1759,.T.);
#610=IFCEDGECURVE(#1185,#1186,#1760,.T.);
#611=IFCEDGECURVE(#1186,#1187,#1761,.T.);
#612=IFCEDGECURVE(#1187,#1188,#1762,.T.);
#613=IFCEDGECURVE(#1188,#1189,#1763,.T.);
#614=IFCEDGECURVE(#1189,#1190,#1764,.T.);
#615=IFCEDGECURVE(#1190,#1191,#1765,.T.);
#616=IFCEDGECURVE(#1191,#1192,#1766,.T.);
#617=IFCEDGECURVE(#1192,#1193,#1767,.T.);
#618=IFCEDGECURVE(#1193,#1194,#1768,.T.);
#619=IFCEDGECURVE(#1194,#1195,#1769,.T.);
#620=IFCEDGECURVE(#1195,#1196,#1770,.T.);
#621=IFCEDGECURVE(#1196,#1197,#1771,.T.);
#622=IFCEDGECURVE(#1197,#1198,#1772,.T.);
#623=IFCEDGECURVE(#1198,#1199,#1773,.T.);
#624=IFCEDGECURVE(#1199,#1200,#1774,.T.);
#625=IFCEDGECURVE(#1200,#1201,#1775,.T.);
#626=IFCEDGECURVE(#1201,#1202,#1776,.T.);
#627=IFCEDGECURVE(#1202,#1203,#1777,.T.);
#628=IFCEDGECURVE(#1203,#1204,#1778,.T.);
#629=IFCEDGECURVE(#1204,#1205,#1779,.T.);
#630=IFCEDGECURVE(#1205,#1206,#1780,.T.);
#631=IFCEDGECURVE(#1206,#1207,#1781,.T.);
#632=IFCEDGECURVE(#1207,#1208,#1782,.T.);
#633=IFCEDGECURVE(#1208,#1209,#1783,.T.);
#634=IFCEDGECURVE(#1209,#1210,#1784,.T.);
#635=IFCEDGECURVE(#1210,#1211,#1785,.T.);
#636=IFCEDGECURVE(#1211,#1212,#1786,.T.);
#637=IFCEDGECURVE(#1212,#1213,#1787,.T.);
#638=IFCEDGECURVE(#1213,#1214,#1788,.T.);
#639=IFCEDGECURVE(#1214,#1215,#1789,.T.);
#640=IFCEDGECURVE(#1215,#1216,#1790,.T.);
#641=IFCEDGECURVE(#1216,#1217,#1791,.T.);
#642=IFCEDGECURVE(#1217,#1218,#1792,.T.);
#643=IFCEDGECURVE(#1218,#1219,#1793,.T.);
#644=IFCEDGECURVE(#1219,#1220,#1794,.T.);
#645=IFCEDGECURVE(#1220,#1221,#1795,.T.);
#646=IFCEDGECURVE(#1221,#1222,#1796,.T.);
#647=IFCEDGECURVE(#1222,#1223,#1797,.T.);
#648=IFCEDGECURVE(#1223,#1224,#1798,.T.);
#649=IFCEDGECURVE(#1224,#1225,#1799,.T.);
#650=IFCEDGECURVE(#1225,#1226,#1800,.T.);
#651=IFCEDGECURVE(#1226,#1227,#1801,.T.);
#652=IFCEDGECURVE(#1227,#1228,#1802,.T.);
#653=IFCEDGECURVE(#1228,#1229,#1803,.T.);
#654=IFCEDGECURVE(#1229,#1230,#1804,.T.);
#655=IFCEDGECURVE(#1230,#1231,#1805,.T.);
#656=IFCEDGECURVE(#1231,#1232,#1806,.T.);
#657=IFCEDGECURVE(#1232,#1233,#1807,.T.);
#658=IFCEDGECURVE(#1233,#1234,#1808,.T.);
#659=IFCEDGECURVE(#1234,#1235,#1809,.T.);
#660=IFCEDGECURVE(#1235,#1236,#1810,.T.);
#661=IFCEDGECURVE(#1236,#1237,#1811,.T.);
#662=IFCEDGECURVE(#1237,#1238,#1812,.T.);
#663=IFCEDGECURVE(#1238,#1239,#1813,.T.);
#664=IFCEDGECURVE(#1239,#1240,#1814,.T.);
#665=IFCEDGECURVE(#1240,#1241,#1815,.T.);
#666=IFCEDGECURVE(#1241,#1242,#1816,.T.);
#667=IFCEDGECURVE(#1242,#1243,#1817,.T.);
#668=IFCEDGECURVE(#1243,#1244,#1818,.T.);
#669=IFCEDGECURVE(#1244,#1245,#1819,.T.);
#670=IFCEDGECURVE(#1245,#1246,#1820,.T.);
#671=IFCEDGECURVE(#1246,#1247,#1821,.T.);
#672=IFCEDGECURVE(#1247,#1248,#1822,.T.);
#673=IFCEDGECURVE(#1248,#1249,#1823,.T.);
#674=IFCEDGECURVE(#1249,#1250,#1824,.T.);
#675=IFCEDGECURVE(#1250,#1251,#1825,.T.);
#676=IFCEDGECURVE(#1251,#1252,#1826,.T.);
#677=IFCEDGECURVE(#1252,#1253,#1827,.T.);
#678=IFCEDGECURVE(#1253,#1254,#1828,.T.);
#679=IFCEDGECURVE(#1254,#1255,#1829,.T.);
#680=IFCEDGECURVE(#1255,#1256,#1830,.T.);
#681=IFCEDGECURVE(#1256,#1257,#1831,.T.);
#682=IFCEDGECURVE(#1257,#1258,#1832,.T.);
#683=IFCEDGECURVE(#1258,#1259,#1833,.T.);
#684=IFCEDGECURVE(#1259,#1260,#1834,.T.);
#685=IFCEDGECURVE(#1260,#1261,#1835,.T.);
#686=IFCEDGECURVE(#1261,#1262,#1836,.T.);
#687=IFCEDGECURVE(#1262,#1263,#1837,.T.);
#688=IFCEDGECURVE(#1263,#1264,#1838,.T.);
#689=IFCEDGECURVE(#1264,#1265,#1839,.T.);
#690=IFCEDGECURVE(#1265,#1266,#1840,.T.);
#691=IFCEDGECURVE(#1266,#1267,#1841,.T.);
#692=IFCEDGECURVE(#1267,#1268,#1842,.T.);
#693=IFCEDGECURVE(#1268,#1269,#1843,.T.);
#694=IFCEDGECURVE(#1269,#1270,#1844,.T.);
#695=IFCEDGECURVE(#1270,#1271,#1845,.T.);
#696=IFCEDGECURVE(#1271,#1272,#1846,.T.);
#697=IFCEDGECURVE(#1272,#1273,#1847,.T.);
#698=IFCEDGECURVE(#1273,#1274,#1848,.T.);
#699=IFCEDGECURVE(#1274,#1275,#1849,.T.);
#700=IFCEDGECURVE(#1275,#1276,#1850,.T.);
#701=IFCEDGECURVE(#1276,#1277,#1851,.T.);
#702=IFCEDGECURVE(#1277,#1278,#1852,.T.);
#703=IFCEDGECURVE(#1278,#1279,#1853,.T.);
#704=IFCEDGECURVE(#1279,#1280,#1854,.T.);
#705=IFCEDGECURVE(#1280,#1281,#1855,.T.);
#706=IFCEDGECURVE(#1281,#1282,#1856,.T.);
#707=IFCEDGECURVE(#1282,#1283,#1857,.T.);
#708=IFCEDGECURVE(#1283,#1284,#1858,.T.);
#709=IFCEDGECURVE(#1284,#1285,#1859,.T.);
#710=IFCEDGECURVE(#1285,#1286,#1860,.T.);
#711=IFCEDGECURVE(#1286,#1287,#1861,.T.);
#712=IFCEDGECURVE(#1287,#1288,#1862,.T.);
#713=IFCEDGECURVE(#1288,#1289,#1863,.T.);
#714=IFCEDGECURVE(#1289,#1290,#1864,.T.);
#715=IFCEDGECURVE(#1290,#1291,#1865,.T.);
#716=IFCEDGECURVE(#1291,#1292,#1866,.T.);
#717=IFCEDGECURVE(#1292,#1293,#1867,.T.);
#718=IFCEDGECURVE(#1293,#1294,#1868,.T.);
#719=IFCEDGECURVE(#1294,#1295,#1869,.T.);
#720=IFCEDGECURVE(#1295,#1296,#1870,.T.);
#721=IFCEDGECURVE(#1296,#1297,#1871,.T.);
#722=IFCEDGECURVE(#1297,#1298,#1872,.T.);
#723=IFCEDGECURVE(#1298,#1299,#1873,.T.);
#724=IFCEDGECURVE(#1299,#1300,#1874,.T.);
#725=IFCEDGECURVE(#1300,#1301,#1875,.T.);
#726=IFCEDGECURVE(#1301,#1302,#1876,.T.);
#727=IFCEDGECURVE(#1302,#1303,#1877,.T.);
#728=IFCEDGECURVE(#1303,#1304,#1878,.T.);
#729=IFCEDGECURVE(#1304,#1305,#1879,.T.);
#730=IFCEDGECURVE(#1305,#1306,#1880,.T.);
#731=IFCEDGECURVE(#1306,#1307,#1881,.T.);
#732=IFCEDGECURVE(#1307,#1308,#1882,.T.);
#733=IFCEDGECURVE(#1308,#1309,#1883,.T.);
#734=IFCEDGECURVE(#1309,#1310,#1884,.T.);
#735=IFCEDGECURVE(#1310,#1311,#1885,.T.);
#736=IFCEDGECURVE(#1311,#1312,#1886,.T.);
#737=IFCEDGECURVE(#1312,#1313,#1887,.T.);
#738=IFCEDGECURVE(#1313,#1314,#1888,.T.);
#739=IFCEDGECURVE(#1314,#1315,#1889,.T.);
#740=IFCEDGECURVE(#1315,#1316,#1890,.T.);
#741=IFCEDGECURVE(#1316,#1317,#1891,.T.);
#742=IFCEDGECURVE(#1317,#1318,#1892,.T.);
#743=IFCEDGECURVE(#1318,#1319,#1893,.T.);
#744=IFCEDGECURVE(#1319,#1320,#1894,.T.);
#745=IFCEDGECURVE(#1320,#1321,#1895,.T.);
#746=IFCEDGECURVE(#1321,#1322,#1896,.T.);
#747=IFCEDGECURVE(#1322,#1323,#1897,.T.);
#748=IFCEDGECURVE(#1323,#1324,#1898,.T.);
#749=IFCEDGECURVE(#1324,#1325,#1899,.T.);
#750=IFCEDGECURVE(#1325,#1326,#1900,.T.);
#751=IFCEDGECURVE(#1326,#1327,#1901,.T.);
#752=IFCEDGECURVE(#1327,#1328,#1902,.T.);
#753=IFCEDGECURVE(#1328,#1329,#1903,.T.);
#754=IFCEDGECURVE(#1329,#1330,#1904,.T.);
#755=IFCEDGECURVE(#1330,#1331,#1905,.T.);
#756=IFCEDGECURVE(#1331,#1332,#1906,.T.);
#757=IFCEDGECURVE(#1332,#1333,#1907,.T.);
#758=IFCEDGECURVE(#1333,#1334,#1908,.T.);
#759=IFCEDGECURVE(#1334,#1335,#1909,.T.);
#760=IFCEDGECURVE(#1335,#1336,#1910,.T.);
#761=IFCEDGECURVE(#1336,#1337,#1911,.T.);
#762=IFCEDGECURVE(#1337,#1338,#1912,.T.);
#763=IFCEDGECURVE(#1338,#1339,#1913,.T.);
#764=IFCEDGECURVE(#1339,#1340,#1914,.T.);
#765=IFCEDGECURVE(#1340,#1341,#1915,.T.);
#766=IFCEDGECURVE(#1341,#1342,#1916,.T.);
#767=IFCEDGECURVE(#1342,#1343,#1917,.T.);
#768=IFCEDGECURVE(#1343,#1344,#1918,.T.);
#769=IFCEDGECURVE(#1344,#1345,#1919,.T.);
#770=IFCEDGECURVE(#1345,#1346,#1920,.T.);
#771=IFCEDGECURVE(#1346,#1347,#1921,.T.);
#772=IFCEDGECURVE(#1347,#1348,#1922,.T.);
#773=IFCEDGECURVE(#1348,#1349,#1923,.T.);
#774=IFCEDGECURVE(#1349,#1350,#1924,.T.);
#775=IFCEDGECURVE(#1350,#1351,#1925,.T.);
#776=IFCEDGECURVE(#1351,#1352,#1926,.T.);
#777=IFCEDGECURVE(#1352,#1353,#1927,.T.);
#778=IFCEDGECURVE(#1353,#1354,#1928,.T.);
#779=IFCEDGECURVE(#1354,#1355,#1929,.T.);
#780=IFCEDGECURVE(#1355,#1356,#1930,.T.);
#781=IFCEDGECURVE(#1356,#1357,#1931,.T.);
#782=IFCEDGECURVE(#1357,#1358,#1932,.T.);
#783=IFCEDGECURVE(#1358,#1359,#1933,.T.);
#784=IFCEDGECURVE(#1359,#1360,#1934,.T.);
#785=IFCEDGECURVE(#1360,#1361,#1935,.T.);
#786=IFCEDGECURVE(#1361,#1362,#1936,.T.);
#787=IFCEDGECURVE(#1362,#1363,#1937,.T.);
#788=IFCEDGECURVE(#1363,#1364,#1938,.T.);
#789=IFCEDGECURVE(#1364,#1365,#1939,.T.);
#790=IFCEDGECURVE(#1365,#1366,#1940,.T.);
#791=IFCEDGECURVE(#1366,#1367,#1941,.T.);
#792=IFCEDGECURVE(#1367,#1368,#1942,.T.);
#793=IFCEDGECURVE(#1368,#1369,#1943,.T.);
#794=IFCEDGECURVE(#1369,#1370,#1944,.T.);
#795=IFCEDGECURVE(#1370,#1371,#1945,.T.);
#796=IFCEDGECURVE(#1371,#1372,#1946,.T.);
#797=IFCEDGECURVE(#1372,#1373,#1947,.T.);
#798=IFCEDGECURVE(#1373,#1374,#1948,.T.);
#799=IFCEDGECURVE(#1374,#1375,#1949,.T.);
#800=IFCEDGECURVE(#1375,#1376,#1950,.T.);
#801=IFCEDGECURVE(#1376,#1377,#1951,.T.);
#802=IFCEDGECURVE(#1377,#1378,#1952,.T.);
#803=IFCEDGECURVE(#1378,#1379,#1953,.T.);
#804=IFCEDGECURVE(#1379,#1380,#1954,.T.);
#805=IFCEDGECURVE(#1380,#1381,#1955,.T.);
#806=IFCEDGECURVE(#1381,#1382,#1956,.T.);
#807=IFCEDGECURVE(#1382,#1383,#1957,.T.);
#808=IFCEDGECURVE(#1383,#1384,#1958,.T.);
#809=IFCEDGECURVE(#1384,#1385,#1959,.T.);
#810=IFCEDGECURVE(#1385,#1386,#1960,.T.);
#811=IFCEDGECURVE(#1386,#1387,#1961,.T.);
#812=IFCEDGECURVE(#1387,#1388,#1962,.T.);
#813=IFCEDGECURVE(#1388,#1389,#1963,.T.);
#814=IFCEDGECURVE(#1389,#1390,#1964,.T.);
#815=IFCEDGECURVE(#1390,#1391,#1965,.T.);
#816=IFCEDGECURVE(#1391,#1392,#1966,.T.);
#817=IFCEDGECURVE(#1392,#1393,#1967,.T.);
#818=IFCEDGECURVE(#1393,#1394,#1968,.T.);
#819=IFCEDGECURVE(#1394,#1395,#1969,.T.);
#820=IFCEDGECURVE(#1395,#1396,#1970,.T.);
#821=IFCEDGECURVE(#1396,#1397,#1971,.T.);
#822=IFCEDGECURVE(#1397,#1398,#1972,.T.);
#823=IFCEDGECURVE(#1398,#1399,#1973,.T.);
#824=IFCEDGECURVE(#1399,#1400,#1974,.T.);
#825=IFCEDGECURVE(#1400,#1401,#1975,.T.);
#826=IFCEDGECURVE(#1401,#1402,#1976,.T.);
#827=IFCEDGECURVE(#1402,#1403,#1977,.T.);
#828=IFCEDGECURVE(#1403,#1404,#1978,.T.);
#829=IFCEDGECURVE(#1404,#1405,#1979,.T.);
#830=IFCEDGECURVE(#1405,#1406,#1980,.T.);
#831=IFCEDGECURVE(#1406,#1407,#1981,.T.);
#832=IFCEDGECURVE(#1407,#1408,#1982,.T.);
#833=IFCEDGECURVE(#1408,#1409,#1983,.T.);
#834=IFCEDGECURVE(#1409,#1410,#1984,.T.);
#835=IFCEDGECURVE(#1410,#1411,#1985,.T.);
#836=IFCEDGECURVE(#1411,#1412,#1986,.T.);
#837=IFCEDGECURVE(#1412,#1413,#1987,.T.);
#838=IFCEDGECURVE(#1413,#1414,#1988,.T.);
#839=IFCEDGECURVE(#1414,#1415,#1989,.T.);
#840=IFCEDGECURVE(#1415,#1416,#1990,.T.);
#841=IFCEDGECURVE(#1416,#1417,#1991,.T.);
#842=IFCEDGECURVE(#1417,#1418,#1992,.T.);
#843=IFCEDGECURVE(#1418,#1419,#1993,.T.);
#844=IFCEDGECURVE(#1419,#1420,#1994,.T.);
#845=IFCEDGECURVE(#1420,#1421,#1995,.T.);
#846=IFCEDGECURVE(#1421,#1422,#1996,.T.);
#847=IFCEDGECURVE(#1422,#1423,#1997,.T.);
#848=IFCEDGECURVE(#1423,#1424,#1998,.T.);
#849=IFCEDGECURVE(#1424,#1425,#1999,.T.);
#850=IFCEDGECURVE(#1425,#1426,#2000,.T.);
#851=IFCEDGECURVE(#1426,#1427,#2001,.T.);
#852=IFCEDGECURVE(#1427,#1428,#2002,.T.);
#853=IFCEDGECURVE(#1428,#1429,#2003,.T.);
#854=IFCEDGECURVE(#1429,#1430,#2004,.T.);
#855=IFCEDGECURVE(#1430,#1431,#2005,.T.);
#856=IFCEDGECURVE(#1431,#1432,#2006,.T.);
#857=IFCEDGECURVE(#1432,#1433,#2007,.T.);
#858=IFCEDGECURVE(#1433,#1434,#2008,.T.);
#859=IFCEDGECURVE(#1434,#1435,#2009,.T.);
#860=IFCEDGECURVE(#1435,#1436,#2010,.T.);
#861=IFCEDGECURVE(#1436,#1437,#2011,.T.);
#862=IFCEDGECURVE(#1437,#1438,#2012,.T.);
#863=IFCEDGECURVE(#1438,#1439,#2013,.T.);
#864=IFCEDGECURVE(#1439,#1440,#2014,.T.);
#865=IFCEDGECURVE(#1440,#1441,#2015,.T.);
#866=IFCEDGECURVE(#1441,#1442,#2016,.T.);
#867=IFCEDGECURVE(#1442,#1443,#2017,.T.);
#868=IFCEDGECURVE(#1443,#1444,#2018,.T.);
#869=IFCEDGECURVE(#1444,#1445,#2019,.T.);
#870=IFCEDGECURVE(#1445,#1446,#2020,.T.);
#871=IFCEDGECURVE(#1446,#1447,#2021,.T.);
#872=IFCEDGECURVE(#1447,#1448,#2022,.T.);
#873=IFCEDGECURVE(#1448,#1449,#2023,.T.);
#874=IFCEDGECURVE(#1449,#1450,#2024,.T.);
#875=IFCEDGECURVE(#1450,#1451,#2025,.T.);
#876=IFCEDGECURVE(#1451,#1452,#2026,.T.);
#877=IFCEDGECURVE(#1452,#1453,#2027,.T.);
#878=IFCEDGECURVE(#1453,#1454,#2028,.T.);
#879=IFCEDGECURVE(#1454,#1455,#2029,.T.);
#880=IFCEDGECURVE(#1455,#1456,#2030,.T.);
#881=IFCEDGECURVE(#1456,#1457,#2031,.T.);
#882=IFCEDGECURVE(#1457,#1458,#2032,.T.);
#883=IFCEDGECURVE(#1458,#1459,#2033,.T.);
#884=IFCEDGECURVE(#1459,#1460,#2034,.T.);
#885=IFCEDGECURVE(#1460,#1461,#2035,.T.);
#886=IFCEDGECURVE(#1461,#1462,#2036,.T.);
#887=IFCEDGECURVE(#1462,#1463,#2037,.T.);
#888=IFCEDGECURVE(#1463,#1464,#2038,.T.);
#889=IFCEDGECURVE(#1464,#1465,#2039,.T.);
#890=IFCEDGECURVE(#1465,#1466,#2040,.T.);
#891=IFCEDGECURVE(#1466,#1467,#2041,.T.);
#892=IFCEDGECURVE(#1467,#1468,#2042,.T.);
#893=IFCEDGECURVE(#1468,#1469,#2043,.T.);
#894=IFCEDGECURVE(#1469,#1470,#2044,.T.);
#895=IFCEDGECURVE(#1470,#1471,#2045,.T.);
#896=IFCEDGECURVE(#1471,#1472,#2046,.T.);
#897=IFCEDGECURVE(#1472,#1473,#2047,.T.);
#898=IFCEDGECURVE(#1473,#1474,#2048,.T.);
#899=IFCEDGECURVE(#1474,#1475,#2049,.T.);
#900=IFCEDGECURVE(#1475,#1476,#2050,.T.);
#901=IFCEDGECURVE(#1476,#1477,#2051,.T.);
#902=IFCEDGECURVE(#1477,#1478,#2052,.T.);
#903=IFCEDGECURVE(#1478,#1479,#2053,.T.);
#904=IFCEDGECURVE(#1479,#1480,#2054,.T.);
#905=IFCEDGECURVE(#1480,#1481,#2055,.T.);
#906=IFCEDGECURVE(#1481,#1482,#2056,.T.);
#907=IFCEDGECURVE(#1482,#1483,#2057,.T.);
#908=IFCEDGECURVE(#1483,#1484,#2058,.T.);
#909=IFCEDGECURVE(#1484,#1485,#2059,.T.);
#910=IFCEDGECURVE(#1485,#1486,#2060,.T.);
#911=IFCEDGECURVE(#1486,#1487,#2061,.T.);
#912=IFCEDGECURVE(#1487,#1488,#2062,.T.);
#913=IFCEDGECURVE(#1488,#1489,#2063,.T.);
#914=IFCEDGECURVE(#1489,#1490,#2064,.T.);
#915=IFCEDGECURVE(#1490,#1491,#2065,.T.);
#916=IFCEDGECURVE(#1491,#1492,#2066,.T.);
#917=IFCEDGECURVE(#1492,#1493,#2067,.T.);
#918=IFCEDGECURVE(#1493,#1494,#2068,.T.);
#919=IFCEDGECURVE(#1494,#1495,#2069,.T.);
#920=IFCEDGECURVE(#1495,#1496,#2070,.T.);
#921=IFCEDGECURVE(#1496,#1497,#2071,.T.);
#922=IFCEDGECURVE(#1497,#1498,#2072,.T.);
#923=IFCEDGECURVE(#1498,#1499,#2073,.T.);
#924=IFCEDGECURVE(#1499,#1500,#2074,.T.);
#925=IFCEDGECURVE(#1500,#1501,#2075,.T.);
#926=IFCEDGECURVE(#1501,#1502,#2076,.T.);
#927=IFCEDGECURVE(#1502,#1503,#2077,.T.);
#928=IFCEDGECURVE(#1503,#1504,#2078,.T.);
#929=IFCEDGECURVE(#1504,#1505,#2079,.T.);
#930=IFCEDGECURVE(#1505,#1506,#2080,.T.);
#931=IFCEDGECURVE(#1506,#1507,#2081,.T.);
#932=IFCEDGECURVE(#1507,#1508,#2082,.T.);
#933=IFCEDGECURVE(#1508,#1509,#2083,.T.);
#934=IFCEDGECURVE(#1509,#1510,#2084,.T.);
#935=IFCEDGECURVE(#1510,#1511,#2085,.T.);
#936=IFCEDGECURVE(#1511,#1512,#2086,.T.);
#937=IFCEDGECURVE(#1512,#1513,#2087,.T.);
#938=IFCEDGECURVE(#1513,#1514,#2088,.T.);
#939=IFCEDGECURVE(#1514,#1515,#2089,.T.);
#940=IFCEDGECURVE(#1515,#1516,#2090,.T.);
#941=IFCEDGECURVE(#1516,#1517,#2091,.T.);
#942=IFCEDGECURVE(#1517,#1518,#2092,.T.);
#943=IFCEDGECURVE(#1518,#1519,#2093,.T.);
#944=IFCEDGECURVE(#1519,#1520,#2094,.T.);
#945=IFCEDGECURVE(#1520,#1521,#2095,.T.);
#946=IFCEDGECURVE(#1521,#1522,#2096,.T.);
#947=IFCEDGECURVE(#1522,#1523,#2097,.T.);
#948=IFCEDGECURVE(#1523,#1524,#2098,.T.);
#949=IFCEDGECURVE(#1524,#1525,#2099,.T.);
#950=IFCEDGECURVE(#1525,#1526,#2100,.T.);
#951=IFCEDGECURVE(#1526,#1527,#2101,.T.);
#952=IFCEDGECURVE(#1527,#1528,#2102,.T.);
#953=IFCEDGECURVE(#1528,#1529,#2103,.T.);
#954=IFCEDGECURVE(#1529,#1530,#2104,.T.);
#955=IFCEDGECURVE(#1530,#1531,#2105,.T.);
#956=IFCEDGECURVE(#1531,#1532,#2106,.T.);
#957=IFCEDGECURVE(#1532,#1533,#2107,.T.);
#958=IFCEDGECURVE(#1533,#1534,#2108,.T.);
#959=IFCEDGECURVE(#1534,#1535,#2109,.T.);
#960=IFCEDGECURVE(#1535,#1536,#2110,.T.);
#961=IFCEDGECURVE(#1536,#1537,#2111,.T.);
#962=IFCEDGECURVE(#1537,#1538,#2112,.T.);
#963=IFCEDGECURVE(#1538,#1539,#2113,.T.);
#964=IFCEDGECURVE(#1539,#1540,#2114,.T.);
#965=IFCEDGECURVE(#1540,#1541,#2115,.T.);
#966=IFCEDGECURVE(#1541,#1542,#2116,.T.);
#967=IFCEDGECURVE(#1542,#1543,#2117,.T.);
#968=IFCEDGECURVE(#1543,#1544,#2118,.T.);
#969=IFCEDGECURVE(#1544,#1545,#2119,.T.);
#970=IFCEDGECURVE(#1545,#1546,#2120,.T.);
#971=IFCEDGECURVE(#1546,#1547,#2121,.T.);
#972=IFCEDGECURVE(#1547,#1548,#2122,.T.);
#973=IFCEDGECURVE(#1548,#1549,#2123,.T.);
#974=IFCEDGECURVE(#1549,#1550,#2124,.T.);
#975=IFCEDGECURVE(#1550,#1551,#2125,.T.);
#976=IFCEDGECURVE(#1551,#1552,#2126,.T.);
#977=IFCEDGECURVE(#1552,#1553,#2127,.T.);
#978=IFCEDGECURVE(#1553,#1554,#2128,.T.);
#979=IFCEDGECURVE(#1554,#1555,#2129,.T.);
#980=IFCEDGECURVE(#1555,#1556,#2130,.T.);
#981=IFCEDGECURVE(#1556,#1557,#2131,.T.);
#982=IFCEDGECURVE(#1557,#1558,#2132,.T.);
#983=IFCEDGECURVE(#1558,#1559,#2133,.T.);
#984=IFCEDGECURVE(#1559,#1180,#2134,.T.);
#985=IFCEDGECURVE(#1560,#1561,#2135,.T.);
#986=IFCEDGECURVE(#1561,#1562,#2136,.T.);
#987=IFCEDGECURVE(#1562,#1563,#2137,.T.);
#988=IFCEDGECURVE(#1563,#1564,#2138,.T.);
#989=IFCEDGECURVE(#1564,#1565,#2139,.T.);
#990=IFCEDGECURVE(#1565,#1566,#2140,.T.);
#991=IFCEDGECURVE(#1566,#1567,#2141,.T.);
#992=IFCEDGECURVE(#1567,#1568,#2142,.T.);
#993=IFCEDGECURVE(#1568,#1569,#2143,.T.);
#994=IFCEDGECURVE(#1569,#1570,#2144,.T.);
#995=IFCEDGECURVE(#1570,#1571,#2145,.T.);
#996=IFCEDGECURVE(#1571,#1572,#2146,.T.);
#997=IFCEDGECURVE(#1572,#1573,#2147,.T.);
#998=IFCEDGECURVE(#1573,#1574,#2148,.T.);
#999=IFCEDGECURVE(#1574,#1575,#2149,.T.);
#1000=IFCEDGECURVE(#1575,#1576,#2150,.T.);
#1001=IFCEDGECURVE(#1576,#1577,#2151,.T.);
#1002=IFCEDGECURVE(#1577,#1578,#2152,.T.);
#1003=IFCEDGECURVE(#1578,#1579,#2153,.T.);
#1004=IFCEDGECURVE(#1579,#1580,#2154,.T.);
#1005=IFCEDGECURVE(#1580,#1581,#2155,.T.);
#1006=IFCEDGECURVE(#1581,#1582,#2156,.T.);
#1007=IFCEDGECURVE(#1582,#1583,#2157,.T.);
#1008=IFCEDGECURVE(#1583,#1584,#2158,.T.);
#1009=IFCEDGECURVE(#1584,#1585,#2159,.T.);
#1010=IFCEDGECURVE(#1585,#1586,#2160,.T.);
#1011=IFCEDGECURVE(#1586,#1587,#2161,.T.);
#1012=IFCEDGECURVE(#1587,#1588,#2162,.T.);
#1013=IFCEDGECURVE(#1588,#1589,#2163,.T.);
#1014=IFCEDGECURVE(#1589,#1590,#2164,.T.);
#1015=IFCEDGECURVE(#1590,#1591,#2165,.T.);
#1016=IFCEDGECURVE(#1591,#1592,#2166,.T.);
#1017=IFCEDGECURVE(#1592,#1593,#2167,.T.);
#1018=IFCEDGECURVE(#1593,#1594,#2168,.T.);
#1019=IFCEDGECURVE(#1594,#1595,#2169,.T.);
#1020=IFCEDGECURVE(#1595,#1596,#2170,.T.);
#1021=IFCEDGECURVE(#1596,#1597,#2171,.T.);
#1022=IFCEDGECURVE(#1597,#1598,#2172,.T.);
#1023=IFCEDGECURVE(#1598,#1599,#2173,.T.);
#1024=IFCEDGECURVE(#1599,#1600,#2174,.T.);
#1025=IFCEDGECURVE(#1600,#1601,#2175,.T.);
#1026=IFCEDGECURVE(#1601,#1602,#2176,.T.);
#1027=IFCEDGECURVE(#1602,#1603,#2177,.T.);
#1028=IFCEDGECURVE(#1603,#1604,#2178,.T.);
#1029=IFCEDGECURVE(#1604,#1605,#2179,.T.);
#1030=IFCEDGECURVE(#1605,#1606,#2180,.T.);
#1031=IFCEDGECURVE(#1606,#1607,#2181,.T.);
#1032=IFCEDGECURVE(#1607,#1608,#2182,.T.);
#1033=IFCEDGECURVE(#1608,#1609,#2183,.T.);
#1034=IFCEDGECURVE(#1609,#1610,#2184,.T.);
#1035=IFCEDGECURVE(#1610,#1611,#2185,.T.);
#1036=IFCEDGECURVE(#1611,#1612,#2186,.T.);
#1037=IFCEDGECURVE(#1612,#1613,#2187,.T.);
#1038=IFCEDGECURVE(#1613,#1614,#2188,.T.);
#1039=IFCEDGECURVE(#1614,#1615,#2189,.T.);
#1040=IFCEDGECURVE(#1615,#1616,#2190,.T.);
#1041=IFCEDGECURVE(#1616,#1617,#2191,.T.);
#1042=IFCEDGECURVE(#1617,#1618,#2192,.T.);
#1043=IFCEDGECURVE(#1618,#1619,#2193,.T.);
#1044=IFCEDGECURVE(#1619,#1620,#2194,.T.);
#1045=IFCEDGECURVE(#1620,#1621,#2195,.T.);
#1046=IFCEDGECURVE(#1621,#1622,#2196,.T.);
#1047=IFCEDGECURVE(#1622,#1623,#2197,.T.);
#1048=IFCEDGECURVE(#1623,#1624,#2198,.T.);
#1049=IFCEDGECURVE(#1624,#1625,#2199,.T.);
#1050=IFCEDGECURVE(#1625,#1626,#2200,.T.);
#1051=IFCEDGECURVE(#1626,#1627,#2201,.T.);
#1052=IFCEDGECURVE(#1627,#1628,#2202,.T.);
#1053=IFCEDGECURVE(#1628,#1629,#2203,.T.);
#1054=IFCEDGECURVE(#1629,#1630,#2204,.T.);
#1055=IFCEDGECURVE(#1630,#1631,#2205,.T.);
#1056=IFCEDGECURVE(#1631,#1632,#2206,.T.);
#1057=IFCEDGECURVE(#1632,#1633,#2207,.T.);
#1058=IFCEDGECURVE(#1633,#1634,#2208,.T.);
#1059=IFCEDGECURVE(#1634,#1635,#2209,.T.);
#1060=IFCEDGECURVE(#1635,#1636,#2210,.T.);
#1061=IFCEDGECURVE(#1636,#1637,#2211,.T.);
#1062=IFCEDGECURVE(#1637,#1638,#2212,.T.);
#1063=IFCEDGECURVE(#1638,#1639,#2213,.T.);
#1064=IFCEDGECURVE(#1639,#1640,#2214,.T.);
#1065=IFCEDGECURVE(#1640,#1641,#2215,.T.);
#1066=IFCEDGECURVE(#1641,#1642,#2216,.T.);
#1067=IFCEDGECURVE(#1642,#1643,#2217,.T.);
#1068=IFCEDGECURVE(#1643,#1644,#2218,.T.);
#1069=IFCEDGECURVE(#1644,#1645,#2219,.T.);
#1070=IFCEDGECURVE(#1645,#1646,#2220,.T.);
#1071=IFCEDGECURVE(#1646,#1647,#2221,.T.);
#1072=IFCEDGECURVE(#1647,#1648,#2222,.T.);
#1073=IFCEDGECURVE(#1648,#1649,#2223,.T.);
#1074=IFCEDGECURVE(#1649,#1650,#2224,.T.);
#1075=IFCEDGECURVE(#1650,#1651,#2225,.T.);
#1076=IFCEDGECURVE(#1651,#1652,#2226,.T.);
#1077=IFCEDGECURVE(#1652,#1653,#2227,.T.);
#1078=IFCEDGECURVE(#1653,#1654,#2228,.T.);
#1079=IFCEDGECURVE(#1654,#1655,#2229,.T.);
#1080=IFCEDGECURVE(#1655,#1656,#2230,.T.);
#1081=IFCEDGECURVE(#1656,#1657,#2231,.T.);
#1082=IFCEDGECURVE(#1657,#1658,#2232,.T.);
#1083=IFCEDGECURVE(#1658,#1659,#2233,.T.);
#1084=IFCEDGECURVE(#1659,#1660,#2234,.T.);
#1085=IFCEDGECURVE(#1660,#1661,#2235,.T.);
#1086=IFCEDGECURVE(#1661,#1662,#2236,.T.);
#1087=IFCEDGECURVE(#1662,#1663,#2237,.T.);
#1088=IFCEDGECURVE(#1663,#1664,#2238,.T.);
#1089=IFCEDGECURVE(#1664,#1665,#2239,.T.);
#1090=IFCEDGECURVE(#1665,#1666,#2240,.T.);
#1091=IFCEDGECURVE(#1666,#1667,#2241,.T.);
#1092=IFCEDGECURVE(#1667,#1668,#2242,.T.);
#1093=IFCEDGECURVE(#1668,#1669,#2243,.T.);
#1094=IFCEDGECURVE(#1669,#1670,#2244,.T.);
#1095=IFCEDGECURVE(#1670,#1671,#2245,.T.);
#1096=IFCEDGECURVE(#1671,#1672,#2246,.T.);
#1097=IFCEDGECURVE(#1672,#1673,#2247,.T.);
#1098=IFCEDGECURVE(#1673,#1674,#2248,.T.);
#1099=IFCEDGECURVE(#1674,#1675,#2249,.T.);
#1100=IFCEDGECURVE(#1675,#1676,#2250,.T.);
#1101=IFCEDGECURVE(#1676,#1677,#2251,.T.);
#1102=IFCEDGECURVE(#1677,#1678,#2252,.T.);
#1103=IFCEDGECURVE(#1678,#1679,#2253,.T.);
#1104=IFCEDGECURVE(#1679,#1680,#2254,.T.);
#1105=IFCEDGECURVE(#1680,#1681,#2255,.T.);
#1106=IFCEDGECURVE(#1681,#1682,#2256,.T.);
#1107=IFCEDGECURVE(#1682,#1683,#2257,.T.);
#1108=IFCEDGECURVE(#1683,#1684,#2258,.T.);
#1109=IFCEDGECURVE(#1684,#1685,#2259,.T.);
#1110=IFCEDGECURVE(#1685,#1686,#2260,.T.);
#1111=IFCEDGECURVE(#1686,#1687,#2261,.T.);
#1112=IFCEDGECURVE(#1687,#1688,#2262,.T.);
#1113=IFCEDGECURVE(#1688,#1689,#2263,.T.);
#1114=IFCEDGECURVE(#1689,#1690,#2264,.T.);
#1115=IFCEDGECURVE(#1690,#1691,#2265,.T.);
#1116=IFCEDGECURVE(#1691,#1692,#2266,.T.);
#1117=IFCEDGECURVE(#1692,#1693,#2267,.T.);
#1118=IFCEDGECURVE(#1693,#1694,#2268,.T.);
#1119=IFCEDGECURVE(#1694,#1695,#2269,.T.);
#1120=IFCEDGECURVE(#1695,#1696,#2270,.T.);
#1121=IFCEDGECURVE(#1696,#1697,#2271,.T.);
#1122=IFCEDGECURVE(#1697,#1698,#2272,.T.);
#1123=IFCEDGECURVE(#1698,#1699,#2273,.T.);
#1124=IFCEDGECURVE(#1699,#1700,#2274,.T.);
#1125=IFCEDGECURVE(#1700,#1701,#2275,.T.);
#1126=IFCEDGECURVE(#1701,#1702,#2276,.T.);
#1127=IFCEDGECURVE(#1702,#1703,#2277,.T.);
#1128=IFCEDGECURVE(#1703,#1704,#2278,.T.);
#1129=IFCEDGECURVE(#1704,#1705,#2279,.T.);
#1130=IFCEDGECURVE(#1705,#1706,#2280,.T.);
#1131=IFCEDGECURVE(#1706,#1707,#2281,.T.);
#1132=IFCEDGECURVE(#1707,#1708,#2282,.T.);
#1133=IFCEDGECURVE(#1708,#1709,#2283,.T.);
#1134=IFCEDGECURVE(#1709,#1710,#2284,.T.);
#1135=IFCEDGECURVE(#1710,#1711,#2285,.T.);
#1136=IFCEDGECURVE(#1711,#1712,#2286,.T.);
#1137=IFCEDGECURVE(#1712,#1713,#2287,.T.);
#1138=IFCEDGECURVE(#1713,#1714,#2288,.T.);
#1139=IFCEDGECURVE(#1714,#1715,#2289,.T.);
#1140=IFCEDGECURVE(#1715,#1716,#2290,.T.);
#1141=IFCEDGECURVE(#1716,#1717,#2291,.T.);
#1142=IFCEDGECURVE(#1717,#1718,#2292,.T.);
#1143=IFCEDGECURVE(#1718,#1719,#2293,.T.);
#1144=IFCEDGECURVE(#1719,#1720,#2294,.T.);
#1145=IFCEDGECURVE(#1720,#1721,#2295,.T.);
#1146=IFCEDGECURVE(#1721,#1722,#2296,.T.);
#1147=IFCEDGECURVE(#1722,#1723,#2297,.T.);
#1148=IFCEDGECURVE(#1723,#1724,#2298,.T.);
#1149=IFCEDGECURVE(#1724,#1725,#2299,.T.);
#1150=IFCEDGECURVE(#1725,#1726,#2300,.T.);
#1151=IFCEDGECURVE(#1726,#1727,#2301,.T.);
#1152=IFCEDGECURVE(#1727,#1728,#2302,.T.);
#1153=IFCEDGECURVE(#1728,#1729,#2303,.T.);
#1154=IFCEDGECURVE(#1729,#1730,#2304,.T.);
#1155=IFCEDGECURVE(#1730,#1731,#2305,.T.);
#1156=IFCEDGECURVE(#1731,#1732,#2306,.T.);
#1157=IFCEDGECURVE(#1732,#1733,#2307,.T.);
#1158=IFCEDGECURVE(#1733,#1734,#2308,.T.);
#1159=IFCEDGECURVE(#1734,#1735,#2309,.T.);
#1160=IFCEDGECURVE(#1735,#1736,#2310,.T.);
#1161=IFCEDGECURVE(#1736,#1737,#2311,.T.);
#1162=IFCEDGECURVE(#1737,#1738,#2312,.T.);
#1163=IFCEDGECURVE(#1738,#1739,#2313,.T.);
#1164=IFCEDGECURVE(#1739,#1740,#2314,.T.);
#1165=IFCEDGECURVE(#1740,#1741,#2315,.T.);
#1166=IFCEDGECURVE(#1741,#1742,#2316,.T.);
#1167=IFCEDGECURVE(#1742,#1743,#2317,.T.);
#1168=IFCEDGECURVE(#1743,#1744,#2318,.T.);
#1169=IFCEDGECURVE(#1744,#1745,#2319,.T.);
#1170=IFCEDGECURVE(#1745,#1746,#2320,.T.);
#1171=IFCEDGECURVE(#1746,#1747,#2321,.T.);
#1172=IFCEDGECURVE(#1747,#1748,#2322,.T.);
#1173=IFCEDGECURVE(#1748,#1749,#2323,.T.);
#1174=IFCEDGECURVE(#1749,#1750,#2324,.T.);
#1175=IFCEDGECURVE(#1750,#1751,#2325,.T.);
#1176=IFCEDGECURVE(#1751,#1752,#2326,.T.);
#1177=IFCEDGECURVE(#1752,#1753,#2327,.T.);
#1178=IFCEDGECURVE(#1753,#1754,#2328,.T.);
#1179=IFCEDGECURVE(#1754,#1560,#2329,.T.);
#1180=IFCVERTEXPOINT(#4071);
#1181=IFCVERTEXPOINT(#4084);
#1182=IFCVERTEXPOINT(#4086);
#1183=IFCVERTEXPOINT(#4087);
#1184=IFCVERTEXPOINT(#4089);
#1185=IFCVERTEXPOINT(#4090);
#1186=IFCVERTEXPOINT(#4092);
#1187=IFCVERTEXPOINT(#4093);
#1188=IFCVERTEXPOINT(#4095);
#1189=IFCVERTEXPOINT(#4096);
#1190=IFCVERTEXPOINT(#4098);
#1191=IFCVERTEXPOINT(#4099);
#1192=IFCVERTEXPOINT(#4101);
#1193=IFCVERTEXPOINT(#4102);
#1194=IFCVERTEXPOINT(#4104);
#1195=IFCVERTEXPOINT(#4105);
#1196=IFCVERTEXPOINT(#4107);
#1197=IFCVERTEXPOINT(#4108);
#1198=IFCVERTEXPOINT(#4110);
#1199=IFCVERTEXPOINT(#4111);
#1200=IFCVERTEXPOINT(#4113);
#1201=IFCVERTEXPOINT(#4114);
#1202=IFCVERTEXPOINT(#4116);
#1203=IFCVERTEXPOINT(#4117);
#1204=IFCVERTEXPOINT(#4119);
#1205=IFCVERTEXPOINT(#4120);
#1206=IFCVERTEXPOINT(#4122);
#1207=IFCVERTEXPOINT(#4123);
#1208=IFCVERTEXPOINT(#4125);
#1209=IFCVERTEXPOINT(#4126);
#1210=IFCVERTEXPOINT(#4128);
#1211=IFCVERTEXPOINT(#4129);
#1212=IFCVERTEXPOINT(#4131);
#1213=IFCVERTEXPOINT(#4132);
#1214=IFCVERTEXPOINT(#4134);
#1215=IFCVERTEXPOINT(#4135);
#1216=IFCVERTEXPOINT(#4137);
#1217=IFCVERTEXPOINT(#4138);
#1218=IFCVERTEXPOINT(#4140);
#1219=IFCVERTEXPOINT(#4141);
#1220=IFCVERTEXPOINT(#4143);
#1221=IFCVERTEXPOINT(#4144);
#1222=IFCVERTEXPOINT(#4146);
#1223=IFCVERTEXPOINT(#4147);
#1224=IFCVERTEXPOINT(#4149);
#1225=IFCVERTEXPOINT(#4150);
#1226=IFCVERTEXPOINT(#4152);
#1227=IFCVERTEXPOINT(#4153);
#1228=IFCVERTEXPOINT(#4155);
#1229=IFCVERTEXPOINT(#4156);
#1230=IFCVERTEXPOINT(#4158);
#1231=IFCVERTEXPOINT(#4159);
#1232=IFCVERTEXPOINT(#4161);
#1233=IFCVERTEXPOINT(#4162);
#1234=IFCVERTEXPOINT(#4164);
#1235=IFCVERTEXPOINT(#4165);
#1236=IFCVERTEXPOINT(#4167);
#1237=IFCVERTEXPOINT(#4168);
#1238=IFCVERTEXPOINT(#4170);
#1239=IFCVERTEXPOINT(#4171);
#1240=IFCVERTEXPOINT(#4173);
#1241=IFCVERTEXPOINT(#4174);
#1242=IFCVERTEXPOINT(#4176);
#1243=IFCVERTEXPOINT(#4177);
#1244=IFCVERTEXPOINT(#4179);
#1245=IFCVERTEXPOINT(#4180);
#1246=IFCVERTEXPOINT(#4182);
#1247=IFCVERTEXPOINT(#4183);
#1248=IFCVERTEXPOINT(#4185);
#1249=IFCVERTEXPOINT(#4186);
#1250=IFCVERTEXPOINT(#4188);
#1251=IFCVERTEXPOINT(#4189);
#1252=IFCVERTEXPOINT(#4191);
#1253=IFCVERTEXPOINT(#4192);
#1254=IFCVERTEXPOINT(#4194);
#1255=IFCVERTEXPOINT(#4195);
#1256=IFCVERTEXPOINT(#4197);
#1257=IFCVERTEXPOINT(#4198);
#1258=IFCVERTEXPOINT(#4200);
#1259=IFCVERTEXPOINT(#4201);
#1260=IFCVERTEXPOINT(#4203);
#1261=IFCVERTEXPOINT(#4204);
#1262=IFCVERTEXPOINT(#4206);
#1263=IFCVERTEXPOINT(#4207);
#1264=IFCVERTEXPOINT(#4209);
#1265=IFCVERTEXPOINT(#4210);
#1266=IFCVERTEXPOINT(#4212);
#1267=IFCVERTEXPOINT(#4213);
#1268=IFCVERTEXPOINT(#4215);
#1269=IFCVERTEXPOINT(#4216);
#1270=IFCVERTEXPOINT(#4218);
#1271=IFCVERTEXPOINT(#4219);
#1272=IFCVERTEXPOINT(#4221);
#1273=IFCVERTEXPOINT(#4222);
#1274=IFCVERTEXPOINT(#4224);
#1275=IFCVERTEXPOINT(#4225);
#1276=IFCVERTEXPOINT(#4227);
#1277=IFCVERTEXPOINT(#4228);
#1278=IFCVERTEXPOINT(#4230);
#1279=IFCVERTEXPOINT(#4231);
#1280=IFCVERTEXPOINT(#4233);
#1281=IFCVERTEXPOINT(#4234);
#1282=IFCVERTEXPOINT(#4236);
#1283=IFCVERTEXPOINT(#4237);
#1284=IFCVERTEXPOINT(#4239);
#1285=IFCVERTEXPOINT(#4240);
#1286=IFCVERTEXPOINT(#4242);
#1287=IFCVERTEXPOINT(#4243);
#1288=IFCVERTEXPOINT(#4245);
#1289=IFCVERTEXPOINT(#4246);
#1290=IFCVERTEXPOINT(#4248);
#1291=IFCVERTEXPOINT(#4249);
#1292=IFCVERTEXPOINT(#4251);
#1293=IFCVERTEXPOINT(#4252);
#1294=IFCVERTEXPOINT(#4254);
#1295=IFCVERTEXPOINT(#4255);
#1296=IFCVERTEXPOINT(#4257);
#1297=IFCVERTEXPOINT(#4258);
#1298=IFCVERTEXPOINT(#4260);
#1299=IFCVERTEXPOINT(#4261);
#1300=IFCVERTEXPOINT(#4263);
#1301=IFCVERTEXPOINT(#4264);
#1302=IFCVERTEXPOINT(#4266);
#1303=IFCVERTEXPOINT(#4267);
#1304=IFCVERTEXPOINT(#4269);
#1305=IFCVERTEXPOINT(#4270);
#1306=IFCVERTEXPOINT(#4272);
#1307=IFCVERTEXPOINT(#4273);
#1308=IFCVERTEXPOINT(#4275);
#1309=IFCVERTEXPOINT(#4276);
#1310=IFCVERTEXPOINT(#4278);
#1311=IFCVERTEXPOINT(#4279);
#1312=IFCVERTEXPOINT(#4281);
#1313=IFCVERTEXPOINT(#4282);
#1314=IFCVERTEXPOINT(#4284);
#1315=IFCVERTEXPOINT(#4285);
#1316=IFCVERTEXPOINT(#4287);
#1317=IFCVERTEXPOINT(#4288);
#1318=IFCVERTEXPOINT(#4290);
#1319=IFCVERTEXPOINT(#4291);
#1320=IFCVERTEXPOINT(#4293);
#1321=IFCVERTEXPOINT(#4294);
#1322=IFCVERTEXPOINT(#4296);
#1323=IFCVERTEXPOINT(#4297);
#1324=IFCVERTEXPOINT(#4299);
#1325=IFCVERTEXPOINT(#4300);
#1326=IFCVERTEXPOINT(#4302);
#1327=IFCVERTEXPOINT(#4303);
#1328=IFCVERTEXPOINT(#4305);
#1329=IFCVERTEXPOINT(#4306);
#1330=IFCVERTEXPOINT(#4308);
#1331=IFCVERTEXPOINT(#4309);
#1332=IFCVERTEXPOINT(#4311);
#1333=IFCVERTEXPOINT(#4312);
#1334=IFCVERTEXPOINT(#4314);
#1335=IFCVERTEXPOINT(#4315);
#1336=IFCVERTEXPOINT(#4317);
#1337=IFCVERTEXPOINT(#4318);
#1338=IFCVERTEXPOINT(#4320);
#1339=IFCVERTEXPOINT(#4321);
#1340=IFCVERTEXPOINT(#4323);
#1341=IFCVERTEXPOINT(#4324);
#1342=IFCVERTEXPOINT(#4326);
#1343=IFCVERTEXPOINT(#4327);
#1344=IFCVERTEXPOINT(#4329);
#1345=IFCVERTEXPOINT(#4330);
#1346=IFCVERTEXPOINT(#4332);
#1347=IFCVERTEXPOINT(#4333);
#1348=IFCVERTEXPOINT(#4335);
#1349=IFCVERTEXPOINT(#4336);
#1350=IFCVERTEXPOINT(#4338);
#1351=IFCVERTEXPOINT(#4339);
#1352=IFCVERTEXPOINT(#4341);
#1353=IFCVERTEXPOINT(#4342);
#1354=IFCVERTEXPOINT(#4344);
#1355=IFCVERTEXPOINT(#4345);
#1356=IFCVERTEXPOINT(#4347);
#1357=IFCVERTEXPOINT(#4348);
#1358=IFCVERTEXPOINT(#4350);
#1359=IFCVERTEXPOINT(#4351);
#1360=IFCVERTEXPOINT(#4353);
#1361=IFCVERTEXPOINT(#4354);
#1362=IFCVERTEXPOINT(#4356);
#1363=IFCVERTEXPOINT(#4357);
#1364=IFCVERTEXPOINT(#4359);
#1365=IFCVERTEXPOINT(#4360);
#1366=IFCVERTEXPOINT(#4081);
#1367=IFCVERTEXPOINT(#4363);
#1368=IFCVERTEXPOINT(#4365);
#1369=IFCVERTEXPOINT(#4367);
#1370=IFCVERTEXPOINT(#4082);
#1371=IFCVERTEXPOINT(#4369);
#1372=IFCVERTEXPOINT(#4371);
#1373=IFCVERTEXPOINT(#4372);
#1374=IFCVERTEXPOINT(#4374);
#1375=IFCVERTEXPOINT(#4375);
#1376=IFCVERTEXPOINT(#4377);
#1377=IFCVERTEXPOINT(#4378);
#1378=IFCVERTEXPOINT(#4380);
#1379=IFCVERTEXPOINT(#4381);
#1380=IFCVERTEXPOINT(#4383);
#1381=IFCVERTEXPOINT(#4384);
#1382=IFCVERTEXPOINT(#4386);
#1383=IFCVERTEXPOINT(#4387);
#1384=IFCVERTEXPOINT(#4389);
#1385=IFCVERTEXPOINT(#4390);
#1386=IFCVERTEXPOINT(#4392);
#1387=IFCVERTEXPOINT(#4393);
#1388=IFCVERTEXPOINT(#4395);
#1389=IFCVERTEXPOINT(#4396);
#1390=IFCVERTEXPOINT(#4398);
#1391=IFCVERTEXPOINT(#4399);
#1392=IFCVERTEXPOINT(#4401);
#1393=IFCVERTEXPOINT(#4402);
#1394=IFCVERTEXPOINT(#4404);
#1395=IFCVERTEXPOINT(#4405);
#1396=IFCVERTEXPOINT(#4407);
#1397=IFCVERTEXPOINT(#4408);
#1398=IFCVERTEXPOINT(#4410);
#1399=IFCVERTEXPOINT(#4411);
#1400=IFCVERTEXPOINT(#4413);
#1401=IFCVERTEXPOINT(#4414);
#1402=IFCVERTEXPOINT(#4416);
#1403=IFCVERTEXPOINT(#4417);
#1404=IFCVERTEXPOINT(#4419);
#1405=IFCVERTEXPOINT(#4420);
#1406=IFCVERTEXPOINT(#4422);
#1407=IFCVERTEXPOINT(#4423);
#1408=IFCVERTEXPOINT(#4425);
#1409=IFCVERTEXPOINT(#4426);
#1410=IFCVERTEXPOINT(#4428);
#1411=IFCVERTEXPOINT(#4429);
#1412=IFCVERTEXPOINT(#4431);
#1413=IFCVERTEXPOINT(#4432);
#1414=IFCVERTEXPOINT(#4434);
#1415=IFCVERTEXPOINT(#4435);
#1416=IFCVERTEXPOINT(#4437);
#1417=IFCVERTEXPOINT(#4438);
#1418=IFCVERTEXPOINT(#4440);
#1419=IFCVERTEXPOINT(#4441);
#1420=IFCVERTEXPOINT(#4443);
#1421=IFCVERTEXPOINT(#4444);
#1422=IFCVERTEXPOINT(#4446);
#1423=IFCVERTEXPOINT(#4447);
#1424=IFCVERTEXPOINT(#4449);
#1425=IFCVERTEXPOINT(#4450);
#1426=IFCVERTEXPOINT(#4452);
#1427=IFCVERTEXPOINT(#4453);
#1428=IFCVERTEXPOINT(#4455);
#1429=IFCVERTEXPOINT(#4456);
#1430=IFCVERTEXPOINT(#4458);
#1431=IFCVERTEXPOINT(#4459);
#1432=IFCVERTEXPOINT(#4461);
#1433=IFCVERTEXPOINT(#4462);
#1434=IFCVERTEXPOINT(#4464);
#1435=IFCVERTEXPOINT(#4465);
#1436=IFCVERTEXPOINT(#4467);
#1437=IFCVERTEXPOINT(#4468);
#1438=IFCVERTEXPOINT(#4470);
#1439=IFCVERTEXPOINT(#4471);
#1440=IFCVERTEXPOINT(#4473);
#1441=IFCVERTEXPOINT(#4474);
#1442=IFCVERTEXPOINT(#4476);
#1443=IFCVERTEXPOINT(#4477);
#1444=IFCVERTEXPOINT(#4479);
#1445=IFCVERTEXPOINT(#4480);
#1446=IFCVERTEXPOINT(#4482);
#1447=IFCVERTEXPOINT(#4483);
#1448=IFCVERTEXPOINT(#4485);
#1449=IFCVERTEXPOINT(#4486);
#1450=IFCVERTEXPOINT(#4488);
#1451=IFCVERTEXPOINT(#4489);
#1452=IFCVERTEXPOINT(#4491);
#1453=IFCVERTEXPOINT(#4492);
#1454=IFCVERTEXPOINT(#4494);
#1455=IFCVERTEXPOINT(#4495);
#1456=IFCVERTEXPOINT(#4497);
#1457=IFCVERTEXPOINT(#4498);
#1458=IFCVERTEXPOINT(#4500);
#1459=IFCVERTEXPOINT(#4501);
#1460=IFCVERTEXPOINT(#4503);
#1461=IFCVERTEXPOINT(#4504);
#1462=IFCVERTEXPOINT(#4506);
#1463=IFCVERTEXPOINT(#4507);
#1464=IFCVERTEXPOINT(#4509);
#1465=IFCVERTEXPOINT(#4510);
#1466=IFCVERTEXPOINT(#4512);
#1467=IFCVERTEXPOINT(#4513);
#1468=IFCVERTEXPOINT(#4515);
#1469=IFCVERTEXPOINT(#4516);
#1470=IFCVERTEXPOINT(#4518);
#1471=IFCVERTEXPOINT(#4519);
#1472=IFCVERTEXPOINT(#4521);
#1473=IFCVERTEXPOINT(#4522);
#1474=IFCVERTEXPOINT(#4524);
#1475=IFCVERTEXPOINT(#4525);
#1476=IFCVERTEXPOINT(#4527);
#1477=IFCVERTEXPOINT(#4528);
#1478=IFCVERTEXPOINT(#4530);
#1479=IFCVERTEXPOINT(#4531);
#1480=IFCVERTEXPOINT(#4533);
#1481=IFCVERTEXPOINT(#4534);
#1482=IFCVERTEXPOINT(#4536);
#1483=IFCVERTEXPOINT(#4537);
#1484=IFCVERTEXPOINT(#4539);
#1485=IFCVERTEXPOINT(#4540);
#1486=IFCVERTEXPOINT(#4542);
#1487=IFCVERTEXPOINT(#4543);
#1488=IFCVERTEXPOINT(#4545);
#1489=IFCVERTEXPOINT(#4546);
#1490=IFCVERTEXPOINT(#4548);
#1491=IFCVERTEXPOINT(#4549);
#1492=IFCVERTEXPOINT(#4551);
#1493=IFCVERTEXPOINT(#4552);
#1494=IFCVERTEXPOINT(#4554);
#1495=IFCVERTEXPOINT(#4555);
#1496=IFCVERTEXPOINT(#4557);
#1497=IFCVERTEXPOINT(#4558);
#1498=IFCVERTEXPOINT(#4560);
#1499=IFCVERTEXPOINT(#4561);
#1500=IFCVERTEXPOINT(#4563);
#1501=IFCVERTEXPOINT(#4564);
#1502=IFCVERTEXPOINT(#4566);
#1503=IFCVERTEXPOINT(#4567);
#1504=IFCVERTEXPOINT(#4569);
#1505=IFCVERTEXPOINT(#4570);
#1506=IFCVERTEXPOINT(#4572);
#1507=IFCVERTEXPOINT(#4573);
#1508=IFCVERTEXPOINT(#4575);
#1509=IFCVERTEXPOINT(#4576);
#1510=IFCVERTEXPOINT(#4578);
#1511=IFCVERTEXPOINT(#4579);
#1512=IFCVERTEXPOINT(#4581);
#1513=IFCVERTEXPOINT(#4582);
#1514=IFCVERTEXPOINT(#4584);
#1515=IFCVERTEXPOINT(#4585);
#1516=IFCVERTEXPOINT(#4587);
#1517=IFCVERTEXPOINT(#4588);
#1518=IFCVERTEXPOINT(#4590);
#1519=IFCVERTEXPOINT(#4591);
#1520=IFCVERTEXPOINT(#4593);
#1521=IFCVERTEXPOINT(#4594);
#1522=IFCVERTEXPOINT(#4596);
#1523=IFCVERTEXPOINT(#4597);
#1524=IFCVERTEXPOINT(#4599);
#1525=IFCVERTEXPOINT(#4600);
#1526=IFCVERTEXPOINT(#4602);
#1527=IFCVERTEXPOINT(#4603);
#1528=IFCVERTEXPOINT(#4605);
#1529=IFCVERTEXPOINT(#4606);
#1530=IFCVERTEXPOINT(#4608);
#1531=IFCVERTEXPOINT(#4609);
#1532=IFCVERTEXPOINT(#4611);
#1533=IFCVERTEXPOINT(#4612);
#1534=IFCVERTEXPOINT(#4614);
#1535=IFCVERTEXPOINT(#4615);
#1536=IFCVERTEXPOINT(#4617);
#1537=IFCVERTEXPOINT(#4618);
#1538=IFCVERTEXPOINT(#4620);
#1539=IFCVERTEXPOINT(#4621);
#1540=IFCVERTEXPOINT(#4623);
#1541=IFCVERTEXPOINT(#4624);
#1542=IFCVERTEXPOINT(#4626);
#1543=IFCVERTEXPOINT(#4627);
#1544=IFCVERTEXPOINT(#4629);
#1545=IFCVERTEXPOINT(#4630);
#1546=IFCVERTEXPOINT(#4632);
#1547=IFCVERTEXPOINT(#4633);
#1548=IFCVERTEXPOINT(#4635);
#1549=IFCVERTEXPOINT(#4636);
#1550=IFCVERTEXPOINT(#4638);
#1551=IFCVERTEXPOINT(#4639);
#1552=IFCVERTEXPOINT(#4641);
#1553=IFCVERTEXPOINT(#4642);
#1554=IFCVERTEXPOINT(#4644);
#1555=IFCVERTEXPOINT(#4645);
#1556=IFCVERTEXPOINT(#4072);
#1557=IFCVERTEXPOINT(#4648);
#1558=IFCVERTEXPOINT(#4650);
#1559=IFCVERTEXPOINT(#4652);
#1560=IFCVERTEXPOINT(#4654);
#1561=IFCVERTEXPOINT(#4655);
#1562=IFCVERTEXPOINT(#4656);
#1563=IFCVERTEXPOINT(#4658);
#1564=IFCVERTEXPOINT(#4659);
#1565=IFCVERTEXPOINT(#4660);
#1566=IFCVERTEXPOINT(#4661);
#1567=IFCVERTEXPOINT(#4662);
#1568=IFCVERTEXPOINT(#4664);
#1569=IFCVERTEXPOINT(#4665);
#1570=IFCVERTEXPOINT(#4666);
#1571=IFCVERTEXPOINT(#4668);
#1572=IFCVERTEXPOINT(#4669);
#1573=IFCVERTEXPOINT(#4671);
#1574=IFCVERTEXPOINT(#4672);
#1575=IFCVERTEXPOINT(#4674);
#1576=IFCVERTEXPOINT(#4675);
#1577=IFCVERTEXPOINT(#4677);
#1578=IFCVERTEXPOINT(#4678);
#1579=IFCVERTEXPOINT(#4680);
#1580=IFCVERTEXPOINT(#4681);
#1581=IFCVERTEXPOINT(#4683);
#1582=IFCVERTEXPOINT(#4684);
#1583=IFCVERTEXPOINT(#4686);
#1584=IFCVERTEXPOINT(#4687);
#1585=IFCVERTEXPOINT(#4689);
#1586=IFCVERTEXPOINT(#4691);
#1587=IFCVERTEXPOINT(#4693);
#1588=IFCVERTEXPOINT(#4694);
#1589=IFCVERTEXPOINT(#4696);
#1590=IFCVERTEXPOINT(#4697);
#1591=IFCVERTEXPOINT(#4699);
#1592=IFCVERTEXPOINT(#4700);
#1593=IFCVERTEXPOINT(#4702);
#1594=IFCVERTEXPOINT(#4703);
#1595=IFCVERTEXPOINT(#4705);
#1596=IFCVERTEXPOINT(#4706);
#1597=IFCVERTEXPOINT(#4708);
#1598=IFCVERTEXPOINT(#4709);
#1599=IFCVERTEXPOINT(#4711);
#1600=IFCVERTEXPOINT(#4712);
#1601=IFCVERTEXPOINT(#4714);
#1602=IFCVERTEXPOINT(#4715);
#1603=IFCVERTEXPOINT(#4717);
#1604=IFCVERTEXPOINT(#4718);
#1605=IFCVERTEXPOINT(#4720);
#1606=IFCVERTEXPOINT(#4721);
#1607=IFCVERTEXPOINT(#4723);
#1608=IFCVERTEXPOINT(#4724);
#1609=IFCVERTEXPOINT(#4726);
#1610=IFCVERTEXPOINT(#4727);
#1611=IFCVERTEXPOINT(#4729);
#1612=IFCVERTEXPOINT(#4730);
#1613=IFCVERTEXPOINT(#4732);
#1614=IFCVERTEXPOINT(#4733);
#1615=IFCVERTEXPOINT(#4735);
#1616=IFCVERTEXPOINT(#4736);
#1617=IFCVERTEXPOINT(#4738);
#1618=IFCVERTEXPOINT(#4739);
#1619=IFCVERTEXPOINT(#4741);
#1620=IFCVERTEXPOINT(#4742);
#1621=IFCVERTEXPOINT(#4744);
#1622=IFCVERTEXPOINT(#4745);
#1623=IFCVERTEXPOINT(#4747);
#1624=IFCVERTEXPOINT(#4748);
#1625=IFCVERTEXPOINT(#4750);
#1626=IFCVERTEXPOINT(#4751);
#1627=IFCVERTEXPOINT(#4753);
#1628=IFCVERTEXPOINT(#4754);
#1629=IFCVERTEXPOINT(#4756);
#1630=IFCVERTEXPOINT(#4757);
#1631=IFCVERTEXPOINT(#4759);
#1632=IFCVERTEXPOINT(#4761);
#1633=IFCVERTEXPOINT(#4762);
#1634=IFCVERTEXPOINT(#4764);
#1635=IFCVERTEXPOINT(#4765);
#1636=IFCVERTEXPOINT(#4767);
#1637=IFCVERTEXPOINT(#4768);
#1638=IFCVERTEXPOINT(#4770);
#1639=IFCVERTEXPOINT(#4771);
#1640=IFCVERTEXPOINT(#4773);
#1641=IFCVERTEXPOINT(#4774);
#1642=IFCVERTEXPOINT(#4776);
#1643=IFCVERTEXPOINT(#4778);
#1644=IFCVERTEXPOINT(#4779);
#1645=IFCVERTEXPOINT(#4780);
#1646=IFCVERTEXPOINT(#4782);
#1647=IFCVERTEXPOINT(#4783);
#1648=IFCVERTEXPOINT(#4784);
#1649=IFCVERTEXPOINT(#4786);
#1650=IFCVERTEXPOINT(#4788);
#1651=IFCVERTEXPOINT(#4789);
#1652=IFCVERTEXPOINT(#4790);
#1653=IFCVERTEXPOINT(#4791);
#1654=IFCVERTEXPOINT(#4792);
#1655=IFCVERTEXPOINT(#4794);
#1656=IFCVERTEXPOINT(#4795);
#1657=IFCVERTEXPOINT(#4796);
#1658=IFCVERTEXPOINT(#4797);
#1659=IFCVERTEXPOINT(#4798);
#1660=IFCVERTEXPOINT(#4800);
#1661=IFCVERTEXPOINT(#4802);
#1662=IFCVERTEXPOINT(#4803);
#1663=IFCVERTEXPOINT(#4804);
#1664=IFCVERTEXPOINT(#4805);
#1665=IFCVERTEXPOINT(#4807);
#1666=IFCVERTEXPOINT(#4809);
#1667=IFCVERTEXPOINT(#4810);
#1668=IFCVERTEXPOINT(#4811);
#1669=IFCVERTEXPOINT(#4813);
#1670=IFCVERTEXPOINT(#4814);
#1671=IFCVERTEXPOINT(#4815);
#1672=IFCVERTEXPOINT(#4817);
#1673=IFCVERTEXPOINT(#4818);
#1674=IFCVERTEXPOINT(#4819);
#1675=IFCVERTEXPOINT(#4821);
#1676=IFCVERTEXPOINT(#4822);
#1677=IFCVERTEXPOINT(#4824);
#1678=IFCVERTEXPOINT(#4825);
#1679=IFCVERTEXPOINT(#4827);
#1680=IFCVERTEXPOINT(#4828);
#1681=IFCVERTEXPOINT(#4830);
#1682=IFCVERTEXPOINT(#4831);
#1683=IFCVERTEXPOINT(#4833);
#1684=IFCVERTEXPOINT(#4834);
#1685=IFCVERTEXPOINT(#4836);
#1686=IFCVERTEXPOINT(#4837);
#1687=IFCVERTEXPOINT(#4839);
#1688=IFCVERTEXPOINT(#4840);
#1689=IFCVERTEXPOINT(#4842);
#1690=IFCVERTEXPOINT(#4843);
#1691=IFCVERTEXPOINT(#4845);
#1692=IFCVERTEXPOINT(#4846);
#1693=IFCVERTEXPOINT(#4848);
#1694=IFCVERTEXPOINT(#4849);
#1695=IFCVERTEXPOINT(#4851);
#1696=IFCVERTEXPOINT(#4852);
#1697=IFCVERTEXPOINT(#4854);
#1698=IFCVERTEXPOINT(#4855);
#1699=IFCVERTEXPOINT(#4857);
#1700=IFCVERTEXPOINT(#4858);
#1701=IFCVERTEXPOINT(#4860);
#1702=IFCVERTEXPOINT(#4861);
#1703=IFCVERTEXPOINT(#4863);
#1704=IFCVERTEXPOINT(#4864);
#1705=IFCVERTEXPOINT(#4866);
#1706=IFCVERTEXPOINT(#4867);
#1707=IFCVERTEXPOINT(#4869);
#1708=IFCVERTEXPOINT(#4870);
#1709=IFCVERTEXPOINT(#4872);
#1710=IFCVERTEXPOINT(#4873);
#1711=IFCVERTEXPOINT(#4875);
#1712=IFCVERTEXPOINT(#4876);
#1713=IFCVERTEXPOINT(#4878);
#1714=IFCVERTEXPOINT(#4879);
#1715=IFCVERTEXPOINT(#4881);
#1716=IFCVERTEXPOINT(#4882);
#1717=IFCVERTEXPOINT(#4884);
#1718=IFCVERTEXPOINT(#4885);
#1719=IFCVERTEXPOINT(#4887);
#1720=IFCVERTEXPOINT(#4888);
#1721=IFCVERTEXPOINT(#4890);
#1722=IFCVERTEXPOINT(#4891);
#1723=IFCVERTEXPOINT(#4893);
#1724=IFCVERTEXPOINT(#4894);
#1725=IFCVERTEXPOINT(#4896);
#1726=IFCVERTEXPOINT(#4897);
#1727=IFCVERTEXPOINT(#4899);
#1728=IFCVERTEXPOINT(#4900);
#1729=IFCVERTEXPOINT(#4902);
#1730=IFCVERTEXPOINT(#4904);
#1731=IFCVERTEXPOINT(#4905);
#1732=IFCVERTEXPOINT(#4907);
#1733=IFCVERTEXPOINT(#4908);
#1734=IFCVERTEXPOINT(#4910);
#1735=IFCVERTEXPOINT(#4911);
#1736=IFCVERTEXPOINT(#4913);
#1737=IFCVERTEXPOINT(#4914);
#1738=IFCVERTEXPOINT(#4916);
#1739=IFCVERTEXPOINT(#4917);
#1740=IFCVERTEXPOINT(#4918);
#1741=IFCVERTEXPOINT(#4920);
#1742=IFCVERTEXPOINT(#4921);
#1743=IFCVERTEXPOINT(#4922);
#1744=IFCVERTEXPOINT(#4924);
#1745=IFCVERTEXPOINT(#4925);
#1746=IFCVERTEXPOINT(#4926);
#1747=IFCVERTEXPOINT(#4928);
#1748=IFCVERTEXPOINT(#4929);
#1749=IFCVERTEXPOINT(#4930);
#1750=IFCVERTEXPOINT(#4931);
#1751=IFCVERTEXPOINT(#4933);
#1752=IFCVERTEXPOINT(#4934);
#1753=IFCVERTEXPOINT(#4935);
#1754=IFCVERTEXPOINT(#4936);
#1755=IFCSURFACECURVE(#2330,(#2905),.PCURVE_S1.);
#1756=IFCSURFACECURVE(#2331,(#2906),.PCURVE_S1.);
#1757=IFCSURFACECURVE(#2332,(#2907),.PCURVE_S1.);
#1758=IFCSURFACECURVE(#2333,(#2908),.PCURVE_S1.);
#1759=IFCSURFACECURVE(#2334,(#2909),.PCURVE_S1.);
#1760=IFCSURFACECURVE(#2335,(#2910),.PCURVE_S1.);
#1761=IFCSURFACECURVE(#2336,(#2911),.PCURVE_S1.);
#1762=IFCSURFACECURVE(#2337,(#2912),.PCURVE_S1.);
#1763=IFCSURFACECURVE(#2338,(#2913),.PCURVE_S1.);
#1764=IFCSURFACECURVE(#2339,(#2914),.PCURVE_S1.);
#1765=IFCSURFACECURVE(#2340,(#2915),.PCURVE_S1.);
#1766=IFCSURFACECURVE(#2341,(#2916),.PCURVE_S1.);
#1767=IFCSURFACECURVE(#2342,(#2917),.PCURVE_S1.);
#1768=IFCSURFACECURVE(#2343,(#2918),.PCURVE_S1.);
#1769=IFCSURFACECURVE(#2344,(#2919),.PCURVE_S1.);
#1770=IFCSURFACECURVE(#2345,(#2920),.PCURVE_S1.);
#1771=IFCSURFACECURVE(#2346,(#2921),.PCURVE_S1.);
#1772=IFCSURFACECURVE(#2347,(#2922),.PCURVE_S1.);
#1773=IFCSURFACECURVE(#2348,(#2923),.PCURVE_S1.);
#1774=IFCSURFACECURVE(#2349,(#2924),.PCURVE_S1.);
#1775=IFCSURFACECURVE(#2350,(#2925),.PCURVE_S1.);
#1776=IFCSURFACECURVE(#2351,(#2926),.PCURVE_S1.);
#1777=IFCSURFACECURVE(#2352,(#2927),.PCURVE_S1.);
#1778=IFCSURFACECURVE(#2353,(#2928),.PCURVE_S1.);
#1779=IFCSURFACECURVE(#2354,(#2929),.PCURVE_S1.);
#1780=IFCSURFACECURVE(#2355,(#2930),.PCURVE_S1.);
#1781=IFCSURFACECURVE(#2356,(#2931),.PCURVE_S1.);
#1782=IFCSURFACECURVE(#2357,(#2932),.PCURVE_S1.);
#1783=IFCSURFACECURVE(#2358,(#2933),.PCURVE_S1.);
#1784=IFCSURFACECURVE(#2359,(#2934),.PCURVE_S1.);
#1785=IFCSURFACECURVE(#2360,(#2935),.PCURVE_S1.);
#1786=IFCSURFACECURVE(#2361,(#2936),.PCURVE_S1.);
#1787=IFCSURFACECURVE(#2362,(#2937),.PCURVE_S1.);
#1788=IFCSURFACECURVE(#2363,(#2938),.PCURVE_S1.);
#1789=IFCSURFACECURVE(#2364,(#2939),.PCURVE_S1.);
#1790=IFCSURFACECURVE(#2365,(#2940),.PCURVE_S1.);
#1791=IFCSURFACECURVE(#2366,(#2941),.PCURVE_S1.);
#1792=IFCSURFACECURVE(#2367,(#2942),.PCURVE_S1.);
#1793=IFCSURFACECURVE(#2368,(#2943),.PCURVE_S1.);
#1794=IFCSURFACECURVE(#2369,(#2944),.PCURVE_S1.);
#1795=IFCSURFACECURVE(#2370,(#2945),.PCURVE_S1.);
#1796=IFCSURFACECURVE(#2371,(#2946),.PCURVE_S1.);
#1797=IFCSURFACECURVE(#2372,(#2947),.PCURVE_S1.);
#1798=IFCSURFACECURVE(#2373,(#2948),.PCURVE_S1.);
#1799=IFCSURFACECURVE(#2374,(#2949),.PCURVE_S1.);
#1800=IFCSURFACECURVE(#2375,(#2950),.PCURVE_S1.);
#1801=IFCSURFACECURVE(#2376,(#2951),.PCURVE_S1.);
#1802=IFCSURFACECURVE(#2377,(#2952),.PCURVE_S1.);
#1803=IFCSURFACECURVE(#2378,(#2953),.PCURVE_S1.);
#1804=IFCSURFACECURVE(#2379,(#2954),.PCURVE_S1.);
#1805=IFCSURFACECURVE(#2380,(#2955),.PCURVE_S1.);
#1806=IFCSURFACECURVE(#2381,(#2956),.PCURVE_S1.);
#1807=IFCSURFACECURVE(#2382,(#2957),.PCURVE_S1.);
#1808=IFCSURFACECURVE(#2383,(#2958),.PCURVE_S1.);
#1809=IFCSURFACECURVE(#2384,(#2959),.PCURVE_S1.);
#1810=IFCSURFACECURVE(#2385,(#2960),.PCURVE_S1.);
#1811=IFCSURFACECURVE(#2386,(#2961),.PCURVE_S1.);
#1812=IFCSURFACECURVE(#2387,(#2962),.PCURVE_S1.);
#1813=IFCSURFACECURVE(#2388,(#2963),.PCURVE_S1.);
#1814=IFCSURFACECURVE(#2389,(#2964),.PCURVE_S1.);
#1815=IFCSURFACECURVE(#2390,(#2965),.PCURVE_S1.);
#1816=IFCSURFACECURVE(#2391,(#2966),.PCURVE_S1.);
#1817=IFCSURFACECURVE(#2392,(#2967),.PCURVE_S1.);
#1818=IFCSURFACECURVE(#2393,(#2968),.PCURVE_S1.);
#1819=IFCSURFACECURVE(#2394,(#2969),.PCURVE_S1.);
#1820=IFCSURFACECURVE(#2395,(#2970),.PCURVE_S1.);
#1821=IFCSURFACECURVE(#2396,(#2971),.PCURVE_S1.);
#1822=IFCSURFACECURVE(#2397,(#2972),.PCURVE_S1.);
#1823=IFCSURFACECURVE(#2398,(#2973),.PCURVE_S1.);
#1824=IFCSURFACECURVE(#2399,(#2974),.PCURVE_S1.);
#1825=IFCSURFACECURVE(#2400,(#2975),.PCURVE_S1.);
#1826=IFCSURFACECURVE(#2401,(#2976),.PCURVE_S1.);
#1827=IFCSURFACECURVE(#2402,(#2977),.PCURVE_S1.);
#1828=IFCSURFACECURVE(#2403,(#2978),.PCURVE_S1.);
#1829=IFCSURFACECURVE(#2404,(#2979),.PCURVE_S1.);
#1830=IFCSURFACECURVE(#2405,(#2980),.PCURVE_S1.);
#1831=IFCSURFACECURVE(#2406,(#2981),.PCURVE_S1.);
#1832=IFCSURFACECURVE(#2407,(#2982),.PCURVE_S1.);
#1833=IFCSURFACECURVE(#2408,(#2983),.PCURVE_S1.);
#1834=IFCSURFACECURVE(#2409,(#2984),.PCURVE_S1.);
#1835=IFCSURFACECURVE(#2410,(#2985),.PCURVE_S1.);
#1836=IFCSURFACECURVE(#2411,(#2986),.PCURVE_S1.);
#1837=IFCSURFACECURVE(#2412,(#2987),.PCURVE_S1.);
#1838=IFCSURFACECURVE(#2413,(#2988),.PCURVE_S1.);
#1839=IFCSURFACECURVE(#2414,(#2989),.PCURVE_S1.);
#1840=IFCSURFACECURVE(#2415,(#2990),.PCURVE_S1.);
#1841=IFCSURFACECURVE(#2416,(#2991),.PCURVE_S1.);
#1842=IFCSURFACECURVE(#2417,(#2992),.PCURVE_S1.);
#1843=IFCSURFACECURVE(#2418,(#2993),.PCURVE_S1.);
#1844=IFCSURFACECURVE(#2419,(#2994),.PCURVE_S1.);
#1845=IFCSURFACECURVE(#2420,(#2995),.PCURVE_S1.);
#1846=IFCSURFACECURVE(#2421,(#2996),.PCURVE_S1.);
#1847=IFCSURFACECURVE(#2422,(#2997),.PCURVE_S1.);
#1848=IFCSURFACECURVE(#2423,(#2998),.PCURVE_S1.);
#1849=IFCSURFACECURVE(#2424,(#2999),.PCURVE_S1.);
#1850=IFCSURFACECURVE(#2425,(#3000),.PCURVE_S1.);
#1851=IFCSURFACECURVE(#2426,(#3001),.PCURVE_S1.);
#1852=IFCSURFACECURVE(#2427,(#3002),.PCURVE_S1.);
#1853=IFCSURFACECURVE(#2428,(#3003),.PCURVE_S1.);
#1854=IFCSURFACECURVE(#2429,(#3004),.PCURVE_S1.);
#1855=IFCSURFACECURVE(#2430,(#3005),.PCURVE_S1.);
#1856=IFCSURFACECURVE(#2431,(#3006),.PCURVE_S1.);
#1857=IFCSURFACECURVE(#2432,(#3007),.PCURVE_S1.);
#1858=IFCSURFACECURVE(#2433,(#3008),.PCURVE_S1.);
#1859=IFCSURFACECURVE(#2434,(#3009),.PCURVE_S1.);
#1860=IFCSURFACECURVE(#2435,(#3010),.PCURVE_S1.);
#1861=IFCSURFACECURVE(#2436,(#3011),.PCURVE_S1.);
#1862=IFCSURFACECURVE(#2437,(#3012),.PCURVE_S1.);
#1863=IFCSURFACECURVE(#2438,(#3013),.PCURVE_S1.);
#1864=IFCSURFACECURVE(#2439,(#3014),.PCURVE_S1.);
#1865=IFCSURFACECURVE(#2440,(#3015),.PCURVE_S1.);
#1866=IFCSURFACECURVE(#2441,(#3016),.PCURVE_S1.);
#1867=IFCSURFACECURVE(#2442,(#3017),.PCURVE_S1.);
#1868=IFCSURFACECURVE(#2443,(#3018),.PCURVE_S1.);
#1869=IFCSURFACECURVE(#2444,(#3019),.PCURVE_S1.);
#1870=IFCSURFACECURVE(#2445,(#3020),.PCURVE_S1.);
#1871=IFCSURFACECURVE(#2446,(#3021),.PCURVE_S1.);
#1872=IFCSURFACECURVE(#2447,(#3022),.PCURVE_S1.);
#1873=IFCSURFACECURVE(#2448,(#3023),.PCURVE_S1.);
#1874=IFCSURFACECURVE(#2449,(#3024),.PCURVE_S1.);
#1875=IFCSURFACECURVE(#2450,(#3025),.PCURVE_S1.);
#1876=IFCSURFACECURVE(#2451,(#3026),.PCURVE_S1.);
#1877=IFCSURFACECURVE(#2452,(#3027),.PCURVE_S1.);
#1878=IFCSURFACECURVE(#2453,(#3028),.PCURVE_S1.);
#1879=IFCSURFACECURVE(#2454,(#3029),.PCURVE_S1.);
#1880=IFCSURFACECURVE(#2455,(#3030),.PCURVE_S1.);
#1881=IFCSURFACECURVE(#2456,(#3031),.PCURVE_S1.);
#1882=IFCSURFACECURVE(#2457,(#3032),.PCURVE_S1.);
#1883=IFCSURFACECURVE(#2458,(#3033),.PCURVE_S1.);
#1884=IFCSURFACECURVE(#2459,(#3034),.PCURVE_S1.);
#1885=IFCSURFACECURVE(#2460,(#3035),.PCURVE_S1.);
#1886=IFCSURFACECURVE(#2461,(#3036),.PCURVE_S1.);
#1887=IFCSURFACECURVE(#2462,(#3037),.PCURVE_S1.);
#1888=IFCSURFACECURVE(#2463,(#3038),.PCURVE_S1.);
#1889=IFCSURFACECURVE(#2464,(#3039),.PCURVE_S1.);
#1890=IFCSURFACECURVE(#2465,(#3040),.PCURVE_S1.);
#1891=IFCSURFACECURVE(#2466,(#3041),.PCURVE_S1.);
#1892=IFCSURFACECURVE(#2467,(#3042),.PCURVE_S1.);
#1893=IFCSURFACECURVE(#2468,(#3043),.PCURVE_S1.);
#1894=IFCSURFACECURVE(#2469,(#3044),.PCURVE_S1.);
#1895=IFCSURFACECURVE(#2470,(#3045),.PCURVE_S1.);
#1896=IFCSURFACECURVE(#2471,(#3046),.PCURVE_S1.);
#1897=IFCSURFACECURVE(#2472,(#3047),.PCURVE_S1.);
#1898=IFCSURFACECURVE(#2473,(#3048),.PCURVE_S1.);
#1899=IFCSURFACECURVE(#2474,(#3049),.PCURVE_S1.);
#1900=IFCSURFACECURVE(#2475,(#3050),.PCURVE_S1.);
#1901=IFCSURFACECURVE(#2476,(#3051),.PCURVE_S1.);
#1902=IFCSURFACECURVE(#2477,(#3052),.PCURVE_S1.);
#1903=IFCSURFACECURVE(#2478,(#3053),.PCURVE_S1.);
#1904=IFCSURFACECURVE(#2479,(#3054),.PCURVE_S1.);
#1905=IFCSURFACECURVE(#2480,(#3055),.PCURVE_S1.);
#1906=IFCSURFACECURVE(#2481,(#3056),.PCURVE_S1.);
#1907=IFCSURFACECURVE(#2482,(#3057),.PCURVE_S1.);
#1908=IFCSURFACECURVE(#2483,(#3058),.PCURVE_S1.);
#1909=IFCSURFACECURVE(#2484,(#3059),.PCURVE_S1.);
#1910=IFCSURFACECURVE(#2485,(#3060),.PCURVE_S1.);
#1911=IFCSURFACECURVE(#2486,(#3061),.PCURVE_S1.);
#1912=IFCSURFACECURVE(#2487,(#3062),.PCURVE_S1.);
#1913=IFCSURFACECURVE(#2488,(#3063),.PCURVE_S1.);
#1914=IFCSURFACECURVE(#2489,(#3064),.PCURVE_S1.);
#1915=IFCSURFACECURVE(#2490,(#3065),.PCURVE_S1.);
#1916=IFCSURFACECURVE(#2491,(#3066),.PCURVE_S1.);
#1917=IFCSURFACECURVE(#2492,(#3067),.PCURVE_S1.);
#1918=IFCSURFACECURVE(#2493,(#3068),.PCURVE_S1.);
#1919=IFCSURFACECURVE(#2494,(#3069),.PCURVE_S1.);
#1920=IFCSURFACECURVE(#2495,(#3070),.PCURVE_S1.);
#1921=IFCSURFACECURVE(#2496,(#3071),.PCURVE_S1.);
#1922=IFCSURFACECURVE(#2497,(#3072),.PCURVE_S1.);
#1923=IFCSURFACECURVE(#2498,(#3073),.PCURVE_S1.);
#1924=IFCSURFACECURVE(#2499,(#3074),.PCURVE_S1.);
#1925=IFCSURFACECURVE(#2500,(#3075),.PCURVE_S1.);
#1926=IFCSURFACECURVE(#2501,(#3076),.PCURVE_S1.);
#1927=IFCSURFACECURVE(#2502,(#3077),.PCURVE_S1.);
#1928=IFCSURFACECURVE(#2503,(#3078),.PCURVE_S1.);
#1929=IFCSURFACECURVE(#2504,(#3079),.PCURVE_S1.);
#1930=IFCSURFACECURVE(#2505,(#3080),.PCURVE_S1.);
#1931=IFCSURFACECURVE(#2506,(#3081),.PCURVE_S1.);
#1932=IFCSURFACECURVE(#2507,(#3082),.PCURVE_S1.);
#1933=IFCSURFACECURVE(#2508,(#3083),.PCURVE_S1.);
#1934=IFCSURFACECURVE(#2509,(#3084),.PCURVE_S1.);
#1935=IFCSURFACECURVE(#2510,(#3085),.PCURVE_S1.);
#1936=IFCSURFACECURVE(#2511,(#3086),.PCURVE_S1.);
#1937=IFCSURFACECURVE(#2512,(#3087),.PCURVE_S1.);
#1938=IFCSURFACECURVE(#2513,(#3088),.PCURVE_S1.);
#1939=IFCSURFACECURVE(#2514,(#3089),.PCURVE_S1.);
#1940=IFCSURFACECURVE(#2515,(#3090),.PCURVE_S1.);
#1941=IFCSURFACECURVE(#2516,(#3091),.PCURVE_S1.);
#1942=IFCSURFACECURVE(#2517,(#3092),.PCURVE_S1.);
#1943=IFCSURFACECURVE(#2518,(#3093),.PCURVE_S1.);
#1944=IFCSURFACECURVE(#2519,(#3094),.PCURVE_S1.);
#1945=IFCSURFACECURVE(#2520,(#3095),.PCURVE_S1.);
#1946=IFCSURFACECURVE(#2521,(#3096),.PCURVE_S1.);
#1947=IFCSURFACECURVE(#2522,(#3097),.PCURVE_S1.);
#1948=IFCSURFACECURVE(#2523,(#3098),.PCURVE_S1.);
#1949=IFCSURFACECURVE(#2524,(#3099),.PCURVE_S1.);
#1950=IFCSURFACECURVE(#2525,(#3100),.PCURVE_S1.);
#1951=IFCSURFACECURVE(#2526,(#3101),.PCURVE_S1.);
#1952=IFCSURFACECURVE(#2527,(#3102),.PCURVE_S1.);
#1953=IFCSURFACECURVE(#2528,(#3103),.PCURVE_S1.);
#1954=IFCSURFACECURVE(#2529,(#3104),.PCURVE_S1.);
#1955=IFCSURFACECURVE(#2530,(#3105),.PCURVE_S1.);
#1956=IFCSURFACECURVE(#2531,(#3106),.PCURVE_S1.);
#1957=IFCSURFACECURVE(#2532,(#3107),.PCURVE_S1.);
#1958=IFCSURFACECURVE(#2533,(#3108),.PCURVE_S1.);
#1959=IFCSURFACECURVE(#2534,(#3109),.PCURVE_S1.);
#1960=IFCSURFACECURVE(#2535,(#3110),.PCURVE_S1.);
#1961=IFCSURFACECURVE(#2536,(#3111),.PCURVE_S1.);
#1962=IFCSURFACECURVE(#2537,(#3112),.PCURVE_S1.);
#1963=IFCSURFACECURVE(#2538,(#3113),.PCURVE_S1.);
#1964=IFCSURFACECURVE(#2539,(#3114),.PCURVE_S1.);
#1965=IFCSURFACECURVE(#2540,(#3115),.PCURVE_S1.);
#1966=IFCSURFACECURVE(#2541,(#3116),.PCURVE_S1.);
#1967=IFCSURFACECURVE(#2542,(#3117),.PCURVE_S1.);
#1968=IFCSURFACECURVE(#2543,(#3118),.PCURVE_S1.);
#1969=IFCSURFACECURVE(#2544,(#3119),.PCURVE_S1.);
#1970=IFCSURFACECURVE(#2545,(#3120),.PCURVE_S1.);
#1971=IFCSURFACECURVE(#2546,(#3121),.PCURVE_S1.);
#1972=IFCSURFACECURVE(#2547,(#3122),.PCURVE_S1.);
#1973=IFCSURFACECURVE(#2548,(#3123),.PCURVE_S1.);
#1974=IFCSURFACECURVE(#2549,(#3124),.PCURVE_S1.);
#1975=IFCSURFACECURVE(#2550,(#3125),.PCURVE_S1.);
#1976=IFCSURFACECURVE(#2551,(#3126),.PCURVE_S1.);
#1977=IFCSURFACECURVE(#2552,(#3127),.PCURVE_S1.);
#1978=IFCSURFACECURVE(#2553,(#3128),.PCURVE_S1.);
#1979=IFCSURFACECURVE(#2554,(#3129),.PCURVE_S1.);
#1980=IFCSURFACECURVE(#2555,(#3130),.PCURVE_S1.);
#1981=IFCSURFACECURVE(#2556,(#3131),.PCURVE_S1.);
#1982=IFCSURFACECURVE(#2557,(#3132),.PCURVE_S1.);
#1983=IFCSURFACECURVE(#2558,(#3133),.PCURVE_S1.);
#1984=IFCSURFACECURVE(#2559,(#3134),.PCURVE_S1.);
#1985=IFCSURFACECURVE(#2560,(#3135),.PCURVE_S1.);
#1986=IFCSURFACECURVE(#2561,(#3136),.PCURVE_S1.);
#1987=IFCSURFACECURVE(#2562,(#3137),.PCURVE_S1.);
#1988=IFCSURFACECURVE(#2563,(#3138),.PCURVE_S1.);
#1989=IFCSURFACECURVE(#2564,(#3139),.PCURVE_S1.);
#1990=IFCSURFACECURVE(#2565,(#3140),.PCURVE_S1.);
#1991=IFCSURFACECURVE(#2566,(#3141),.PCURVE_S1.);
#1992=IFCSURFACECURVE(#2567,(#3142),.PCURVE_S1.);
#1993=IFCSURFACECURVE(#2568,(#3143),.PCURVE_S1.);
#1994=IFCSURFACECURVE(#2569,(#3144),.PCURVE_S1.);
#1995=IFCSURFACECURVE(#2570,(#3145),.PCURVE_S1.);
#1996=IFCSURFACECURVE(#2571,(#3146),.PCURVE_S1.);
#1997=IFCSURFACECURVE(#2572,(#3147),.PCURVE_S1.);
#1998=IFCSURFACECURVE(#2573,(#3148),.PCURVE_S1.);
#1999=IFCSURFACECURVE(#2574,(#3149),.PCURVE_S1.);
#2000=IFCSURFACECURVE(#2575,(#3150),.PCURVE_S1.);
#2001=IFCSURFACECURVE(#2576,(#3151),.PCURVE_S1.);
#2002=IFCSURFACECURVE(#2577,(#3152),.PCURVE_S1.);
#2003=IFCSURFACECURVE(#2578,(#3153),.PCURVE_S1.);
#2004=IFCSURFACECURVE(#2579,(#3154),.PCURVE_S1.);
#2005=IFCSURFACECURVE(#2580,(#3155),.PCURVE_S1.);
#2006=IFCSURFACECURVE(#2581,(#3156),.PCURVE_S1.);
#2007=IFCSURFACECURVE(#2582,(#3157),.PCURVE_S1.);
#2008=IFCSURFACECURVE(#2583,(#3158),.PCURVE_S1.);
#2009=IFCSURFACECURVE(#2584,(#3159),.PCURVE_S1.);
#2010=IFCSURFACECURVE(#2585,(#3160),.PCURVE_S1.);
#2011=IFCSURFACECURVE(#2586,(#3161),.PCURVE_S1.);
#2012=IFCSURFACECURVE(#2587,(#3162),.PCURVE_S1.);
#2013=IFCSURFACECURVE(#2588,(#3163),.PCURVE_S1.);
#2014=IFCSURFACECURVE(#2589,(#3164),.PCURVE_S1.);
#2015=IFCSURFACECURVE(#2590,(#3165),.PCURVE_S1.);
#2016=IFCSURFACECURVE(#2591,(#3166),.PCURVE_S1.);
#2017=IFCSURFACECURVE(#2592,(#3167),.PCURVE_S1.);
#2018=IFCSURFACECURVE(#2593,(#3168),.PCURVE_S1.);
#2019=IFCSURFACECURVE(#2594,(#3169),.PCURVE_S1.);
#2020=IFCSURFACECURVE(#2595,(#3170),.PCURVE_S1.);
#2021=IFCSURFACECURVE(#2596,(#3171),.PCURVE_S1.);
#2022=IFCSURFACECURVE(#2597,(#3172),.PCURVE_S1.);
#2023=IFCSURFACECURVE(#2598,(#3173),.PCURVE_S1.);
#2024=IFCSURFACECURVE(#2599,(#3174),.PCURVE_S1.);
#2025=IFCSURFACECURVE(#2600,(#3175),.PCURVE_S1.);
#2026=IFCSURFACECURVE(#2601,(#3176),.PCURVE_S1.);
#2027=IFCSURFACECURVE(#2602,(#3177),.PCURVE_S1.);
#2028=IFCSURFACECURVE(#2603,(#3178),.PCURVE_S1.);
#2029=IFCSURFACECURVE(#2604,(#3179),.PCURVE_S1.);
#2030=IFCSURFACECURVE(#2605,(#3180),.PCURVE_S1.);
#2031=IFCSURFACECURVE(#2606,(#3181),.PCURVE_S1.);
#2032=IFCSURFACECURVE(#2607,(#3182),.PCURVE_S1.);
#2033=IFCSURFACECURVE(#2608,(#3183),.PCURVE_S1.);
#2034=IFCSURFACECURVE(#2609,(#3184),.PCURVE_S1.);
#2035=IFCSURFACECURVE(#2610,(#3185),.PCURVE_S1.);
#2036=IFCSURFACECURVE(#2611,(#3186),.PCURVE_S1.);
#2037=IFCSURFACECURVE(#2612,(#3187),.PCURVE_S1.);
#2038=IFCSURFACECURVE(#2613,(#3188),.PCURVE_S1.);
#2039=IFCSURFACECURVE(#2614,(#3189),.PCURVE_S1.);
#2040=IFCSURFACECURVE(#2615,(#3190),.PCURVE_S1.);
#2041=IFCSURFACECURVE(#2616,(#3191),.PCURVE_S1.);
#2042=IFCSURFACECURVE(#2617,(#3192),.PCURVE_S1.);
#2043=IFCSURFACECURVE(#2618,(#3193),.PCURVE_S1.);
#2044=IFCSURFACECURVE(#2619,(#3194),.PCURVE_S1.);
#2045=IFCSURFACECURVE(#2620,(#3195),.PCURVE_S1.);
#2046=IFCSURFACECURVE(#2621,(#3196),.PCURVE_S1.);
#2047=IFCSURFACECURVE(#2622,(#3197),.PCURVE_S1.);
#2048=IFCSURFACECURVE(#2623,(#3198),.PCURVE_S1.);
#2049=IFCSURFACECURVE(#2624,(#3199),.PCURVE_S1.);
#2050=IFCSURFACECURVE(#2625,(#3200),.PCURVE_S1.);
#2051=IFCSURFACECURVE(#2626,(#3201),.PCURVE_S1.);
#2052=IFCSURFACECURVE(#2627,(#3202),.PCURVE_S1.);
#2053=IFCSURFACECURVE(#2628,(#3203),.PCURVE_S1.);
#2054=IFCSURFACECURVE(#2629,(#3204),.PCURVE_S1.);
#2055=IFCSURFACECURVE(#2630,(#3205),.PCURVE_S1.);
#2056=IFCSURFACECURVE(#2631,(#3206),.PCURVE_S1.);
#2057=IFCSURFACECURVE(#2632,(#3207),.PCURVE_S1.);
#2058=IFCSURFACECURVE(#2633,(#3208),.PCURVE_S1.);
#2059=IFCSURFACECURVE(#2634,(#3209),.PCURVE_S1.);
#2060=IFCSURFACECURVE(#2635,(#3210),.PCURVE_S1.);
#2061=IFCSURFACECURVE(#2636,(#3211),.PCURVE_S1.);
#2062=IFCSURFACECURVE(#2637,(#3212),.PCURVE_S1.);
#2063=IFCSURFACECURVE(#2638,(#3213),.PCURVE_S1.);
#2064=IFCSURFACECURVE(#2639,(#3214),.PCURVE_S1.);
#2065=IFCSURFACECURVE(#2640,(#3215),.PCURVE_S1.);
#2066=IFCSURFACECURVE(#2641,(#3216),.PCURVE_S1.);
#2067=IFCSURFACECURVE(#2642,(#3217),.PCURVE_S1.);
#2068=IFCSURFACECURVE(#2643,(#3218),.PCURVE_S1.);
#2069=IFCSURFACECURVE(#2644,(#3219),.PCURVE_S1.);
#2070=IFCSURFACECURVE(#2645,(#3220),.PCURVE_S1.);
#2071=IFCSURFACECURVE(#2646,(#3221),.PCURVE_S1.);
#2072=IFCSURFACECURVE(#2647,(#3222),.PCURVE_S1.);
#2073=IFCSURFACECURVE(#2648,(#3223),.PCURVE_S1.);
#2074=IFCSURFACECURVE(#2649,(#3224),.PCURVE_S1.);
#2075=IFCSURFACECURVE(#2650,(#3225),.PCURVE_S1.);
#2076=IFCSURFACECURVE(#2651,(#3226),.PCURVE_S1.);
#2077=IFCSURFACECURVE(#2652,(#3227),.PCURVE_S1.);
#2078=IFCSURFACECURVE(#2653,(#3228),.PCURVE_S1.);
#2079=IFCSURFACECURVE(#2654,(#3229),.PCURVE_S1.);
#2080=IFCSURFACECURVE(#2655,(#3230),.PCURVE_S1.);
#2081=IFCSURFACECURVE(#2656,(#3231),.PCURVE_S1.);
#2082=IFCSURFACECURVE(#2657,(#3232),.PCURVE_S1.);
#2083=IFCSURFACECURVE(#2658,(#3233),.PCURVE_S1.);
#2084=IFCSURFACECURVE(#2659,(#3234),.PCURVE_S1.);
#2085=IFCSURFACECURVE(#2660,(#3235),.PCURVE_S1.);
#2086=IFCSURFACECURVE(#2661,(#3236),.PCURVE_S1.);
#2087=IFCSURFACECURVE(#2662,(#3237),.PCURVE_S1.);
#2088=IFCSURFACECURVE(#2663,(#3238),.PCURVE_S1.);
#2089=IFCSURFACECURVE(#2664,(#3239),.PCURVE_S1.);
#2090=IFCSURFACECURVE(#2665,(#3240),.PCURVE_S1.);
#2091=IFCSURFACECURVE(#2666,(#3241),.PCURVE_S1.);
#2092=IFCSURFACECURVE(#2667,(#3242),.PCURVE_S1.);
#2093=IFCSURFACECURVE(#2668,(#3243),.PCURVE_S1.);
#2094=IFCSURFACECURVE(#2669,(#3244),.PCURVE_S1.);
#2095=IFCSURFACECURVE(#2670,(#3245),.PCURVE_S1.);
#2096=IFCSURFACECURVE(#2671,(#3246),.PCURVE_S1.);
#2097=IFCSURFACECURVE(#2672,(#3247),.PCURVE_S1.);
#2098=IFCSURFACECURVE(#2673,(#3248),.PCURVE_S1.);
#2099=IFCSURFACECURVE(#2674,(#3249),.PCURVE_S1.);
#2100=IFCSURFACECURVE(#2675,(#3250),.PCURVE_S1.);
#2101=IFCSURFACECURVE(#2676,(#3251),.PCURVE_S1.);
#2102=IFCSURFACECURVE(#2677,(#3252),.PCURVE_S1.);
#2103=IFCSURFACECURVE(#2678,(#3253),.PCURVE_S1.);
#2104=IFCSURFACECURVE(#2679,(#3254),.PCURVE_S1.);
#2105=IFCSURFACECURVE(#2680,(#3255),.PCURVE_S1.);
#2106=IFCSURFACECURVE(#2681,(#3256),.PCURVE_S1.);
#2107=IFCSURFACECURVE(#2682,(#3257),.PCURVE_S1.);
#2108=IFCSURFACECURVE(#2683,(#3258),.PCURVE_S1.);
#2109=IFCSURFACECURVE(#2684,(#3259),.PCURVE_S1.);
#2110=IFCSURFACECURVE(#2685,(#3260),.PCURVE_S1.);
#2111=IFCSURFACECURVE(#2686,(#3261),.PCURVE_S1.);
#2112=IFCSURFACECURVE(#2687,(#3262),.PCURVE_S1.);
#2113=IFCSURFACECURVE(#2688,(#3263),.PCURVE_S1.);
#2114=IFCSURFACECURVE(#2689,(#3264),.PCURVE_S1.);
#2115=IFCSURFACECURVE(#2690,(#3265),.PCURVE_S1.);
#2116=IFCSURFACECURVE(#2691,(#3266),.PCURVE_S1.);
#2117=IFCSURFACECURVE(#2692,(#3267),.PCURVE_S1.);
#2118=IFCSURFACECURVE(#2693,(#3268),.PCURVE_S1.);
#2119=IFCSURFACECURVE(#2694,(#3269),.PCURVE_S1.);
#2120=IFCSURFACECURVE(#2695,(#3270),.PCURVE_S1.);
#2121=IFCSURFACECURVE(#2696,(#3271),.PCURVE_S1.);
#2122=IFCSURFACECURVE(#2697,(#3272),.PCURVE_S1.);
#2123=IFCSURFACECURVE(#2698,(#3273),.PCURVE_S1.);
#2124=IFCSURFACECURVE(#2699,(#3274),.PCURVE_S1.);
#2125=IFCSURFACECURVE(#2700,(#3275),.PCURVE_S1.);
#2126=IFCSURFACECURVE(#2701,(#3276),.PCURVE_S1.);
#2127=IFCSURFACECURVE(#2702,(#3277),.PCURVE_S1.);
#2128=IFCSURFACECURVE(#2703,(#3278),.PCURVE_S1.);
#2129=IFCSURFACECURVE(#2704,(#3279),.PCURVE_S1.);
#2130=IFCSURFACECURVE(#2705,(#3280),.PCURVE_S1.);
#2131=IFCSURFACECURVE(#2706,(#3281),.PCURVE_S1.);
#2132=IFCSURFACECURVE(#2707,(#3282),.PCURVE_S1.);
#2133=IFCSURFACECURVE(#2708,(#3283),.PCURVE_S1.);
#2134=IFCSURFACECURVE(#2709,(#3284),.PCURVE_S1.);
#2135=IFCSURFACECURVE(#2710,(#3285),.PCURVE_S1.);
#2136=IFCSURFACECURVE(#2711,(#3286),.PCURVE_S1.);
#2137=IFCSURFACECURVE(#2712,(#3287),.PCURVE_S1.);
#2138=IFCSURFACECURVE(#2713,(#3288),.PCURVE_S1.);
#2139=IFCSURFACECURVE(#2714,(#3289),.PCURVE_S1.);
#2140=IFCSURFACECURVE(#2715,(#3290),.PCURVE_S1.);
#2141=IFCSURFACECURVE(#2716,(#3291),.PCURVE_S1.);
#2142=IFCSURFACECURVE(#2717,(#3292),.PCURVE_S1.);
#2143=IFCSURFACECURVE(#2718,(#3293),.PCURVE_S1.);
#2144=IFCSURFACECURVE(#2719,(#3294),.PCURVE_S1.);
#2145=IFCSURFACECURVE(#2720,(#3295),.PCURVE_S1.);
#2146=IFCSURFACECURVE(#2721,(#3296),.PCURVE_S1.);
#2147=IFCSURFACECURVE(#2722,(#3297),.PCURVE_S1.);
#2148=IFCSURFACECURVE(#2723,(#3298),.PCURVE_S1.);
#2149=IFCSURFACECURVE(#2724,(#3299),.PCURVE_S1.);
#2150=IFCSURFACECURVE(#2725,(#3300),.PCURVE_S1.);
#2151=IFCSURFACECURVE(#2726,(#3301),.PCURVE_S1.);
#2152=IFCSURFACECURVE(#2727,(#3302),.PCURVE_S1.);
#2153=IFCSURFACECURVE(#2728,(#3303),.PCURVE_S1.);
#2154=IFCSURFACECURVE(#2729,(#3304),.PCURVE_S1.);
#2155=IFCSURFACECURVE(#2730,(#3305),.PCURVE_S1.);
#2156=IFCSURFACECURVE(#2731,(#3306),.PCURVE_S1.);
#2157=IFCSURFACECURVE(#2732,(#3307),.PCURVE_S1.);
#2158=IFCSURFACECURVE(#2733,(#3308),.PCURVE_S1.);
#2159=IFCSURFACECURVE(#2734,(#3309),.PCURVE_S1.);
#2160=IFCSURFACECURVE(#2735,(#3310),.PCURVE_S1.);
#2161=IFCSURFACECURVE(#2736,(#3311),.PCURVE_S1.);
#2162=IFCSURFACECURVE(#2737,(#3312),.PCURVE_S1.);
#2163=IFCSURFACECURVE(#2738,(#3313),.PCURVE_S1.);
#2164=IFCSURFACECURVE(#2739,(#3314),.PCURVE_S1.);
#2165=IFCSURFACECURVE(#2740,(#3315),.PCURVE_S1.);
#2166=IFCSURFACECURVE(#2741,(#3316),.PCURVE_S1.);
#2167=IFCSURFACECURVE(#2742,(#3317),.PCURVE_S1.);
#2168=IFCSURFACECURVE(#2743,(#3318),.PCURVE_S1.);
#2169=IFCSURFACECURVE(#2744,(#3319),.PCURVE_S1.);
#2170=IFCSURFACECURVE(#2745,(#3320),.PCURVE_S1.);
#2171=IFCSURFACECURVE(#2746,(#3321),.PCURVE_S1.);
#2172=IFCSURFACECURVE(#2747,(#3322),.PCURVE_S1.);
#2173=IFCSURFACECURVE(#2748,(#3323),.PCURVE_S1.);
#2174=IFCSURFACECURVE(#2749,(#3324),.PCURVE_S1.);
#2175=IFCSURFACECURVE(#2750,(#3325),.PCURVE_S1.);
#2176=IFCSURFACECURVE(#2751,(#3326),.PCURVE_S1.);
#2177=IFCSURFACECURVE(#2752,(#3327),.PCURVE_S1.);
#2178=IFCSURFACECURVE(#2753,(#3328),.PCURVE_S1.);
#2179=IFCSURFACECURVE(#2754,(#3329),.PCURVE_S1.);
#2180=IFCSURFACECURVE(#2755,(#3330),.PCURVE_S1.);
#2181=IFCSURFACECURVE(#2756,(#3331),.PCURVE_S1.);
#2182=IFCSURFACECURVE(#2757,(#3332),.PCURVE_S1.);
#2183=IFCSURFACECURVE(#2758,(#3333),.PCURVE_S1.);
#2184=IFCSURFACECURVE(#2759,(#3334),.PCURVE_S1.);
#2185=IFCSURFACECURVE(#2760,(#3335),.PCURVE_S1.);
#2186=IFCSURFACECURVE(#2761,(#3336),.PCURVE_S1.);
#2187=IFCSURFACECURVE(#2762,(#3337),.PCURVE_S1.);
#2188=IFCSURFACECURVE(#2763,(#3338),.PCURVE_S1.);
#2189=IFCSURFACECURVE(#2764,(#3339),.PCURVE_S1.);
#2190=IFCSURFACECURVE(#2765,(#3340),.PCURVE_S1.);
#2191=IFCSURFACECURVE(#2766,(#3341),.PCURVE_S1.);
#2192=IFCSURFACECURVE(#2767,(#3342),.PCURVE_S1.);
#2193=IFCSURFACECURVE(#2768,(#3343),.PCURVE_S1.);
#2194=IFCSURFACECURVE(#2769,(#3344),.PCURVE_S1.);
#2195=IFCSURFACECURVE(#2770,(#3345),.PCURVE_S1.);
#2196=IFCSURFACECURVE(#2771,(#3346),.PCURVE_S1.);
#2197=IFCSURFACECURVE(#2772,(#3347),.PCURVE_S1.);
#2198=IFCSURFACECURVE(#2773,(#3348),.PCURVE_S1.);
#2199=IFCSURFACECURVE(#2774,(#3349),.PCURVE_S1.);
#2200=IFCSURFACECURVE(#2775,(#3350),.PCURVE_S1.);
#2201=IFCSURFACECURVE(#2776,(#3351),.PCURVE_S1.);
#2202=IFCSURFACECURVE(#2777,(#3352),.PCURVE_S1.);
#2203=IFCSURFACECURVE(#2778,(#3353),.PCURVE_S1.);
#2204=IFCSURFACECURVE(#2779,(#3354),.PCURVE_S1.);
#2205=IFCSURFACECURVE(#2780,(#3355),.PCURVE_S1.);
#2206=IFCSURFACECURVE(#2781,(#3356),.PCURVE_S1.);
#2207=IFCSURFACECURVE(#2782,(#3357),.PCURVE_S1.);
#2208=IFCSURFACECURVE(#2783,(#3358),.PCURVE_S1.);
#2209=IFCSURFACECURVE(#2784,(#3359),.PCURVE_S1.);
#2210=IFCSURFACECURVE(#2785,(#3360),.PCURVE_S1.);
#2211=IFCSURFACECURVE(#2786,(#3361),.PCURVE_S1.);
#2212=IFCSURFACECURVE(#2787,(#3362),.PCURVE_S1.);
#2213=IFCSURFACECURVE(#2788,(#3363),.PCURVE_S1.);
#2214=IFCSURFACECURVE(#2789,(#3364),.PCURVE_S1.);
#2215=IFCSURFACECURVE(#2790,(#3365),.PCURVE_S1.);
#2216=IFCSURFACECURVE(#2791,(#3366),.PCURVE_S1.);
#2217=IFCSURFACECURVE(#2792,(#3367),.PCURVE_S1.);
#2218=IFCSURFACECURVE(#2793,(#3368),.PCURVE_S1.);
#2219=IFCSURFACECURVE(#2794,(#3369),.PCURVE_S1.);
#2220=IFCSURFACECURVE(#2795,(#3370),.PCURVE_S1.);
#2221=IFCSURFACECURVE(#2796,(#3371),.PCURVE_S1.);
#2222=IFCSURFACECURVE(#2797,(#3372),.PCURVE_S1.);
#2223=IFCSURFACECURVE(#2798,(#3373),.PCURVE_S1.);
#2224=IFCSURFACECURVE(#2799,(#3374),.PCURVE_S1.);
#2225=IFCSURFACECURVE(#2800,(#3375),.PCURVE_S1.);
#2226=IFCSURFACECURVE(#2801,(#3376),.PCURVE_S1.);
#2227=IFCSURFACECURVE(#2802,(#3377),.PCURVE_S1.);
#2228=IFCSURFACECURVE(#2803,(#3378),.PCURVE_S1.);
#2229=IFCSURFACECURVE(#2804,(#3379),.PCURVE_S1.);
#2230=IFCSURFACECURVE(#2805,(#3380),.PCURVE_S1.);
#2231=IFCSURFACECURVE(#2806,(#3381),.PCURVE_S1.);
#2232=IFCSURFACECURVE(#2807,(#3382),.PCURVE_S1.);
#2233=IFCSURFACECURVE(#2808,(#3383),.PCURVE_S1.);
#2234=IFCSURFACECURVE(#2809,(#3384),.PCURVE_S1.);
#2235=IFCSURFACECURVE(#2810,(#3385),.PCURVE_S1.);
#2236=IFCSURFACECURVE(#2811,(#3386),.PCURVE_S1.);
#2237=IFCSURFACECURVE(#2812,(#3387),.PCURVE_S1.);
#2238=IFCSURFACECURVE(#2813,(#3388),.PCURVE_S1.);
#2239=IFCSURFACECURVE(#2814,(#3389),.PCURVE_S1.);
#2240=IFCSURFACECURVE(#2815,(#3390),.PCURVE_S1.);
#2241=IFCSURFACECURVE(#2816,(#3391),.PCURVE_S1.);
#2242=IFCSURFACECURVE(#2817,(#3392),.PCURVE_S1.);
#2243=IFCSURFACECURVE(#2818,(#3393),.PCURVE_S1.);
#2244=IFCSURFACECURVE(#2819,(#3394),.PCURVE_S1.);
#2245=IFCSURFACECURVE(#2820,(#3395),.PCURVE_S1.);
#2246=IFCSURFACECURVE(#2821,(#3396),.PCURVE_S1.);
#2247=IFCSURFACECURVE(#2822,(#3397),.PCURVE_S1.);
#2248=IFCSURFACECURVE(#2823,(#3398),.PCURVE_S1.);
#2249=IFCSURFACECURVE(#2824,(#3399),.PCURVE_S1.);
#2250=IFCSURFACECURVE(#2825,(#3400),.PCURVE_S1.);
#2251=IFCSURFACECURVE(#2826,(#3401),.PCURVE_S1.);
#2252=IFCSURFACECURVE(#2827,(#3402),.PCURVE_S1.);
#2253=IFCSURFACECURVE(#2828,(#3403),.PCURVE_S1.);
#2254=IFCSURFACECURVE(#2829,(#3404),.PCURVE_S1.);
#2255=IFCSURFACECURVE(#2830,(#3405),.PCURVE_S1.);
#2256=IFCSURFACECURVE(#2831,(#3406),.PCURVE_S1.);
#2257=IFCSURFACECURVE(#2832,(#3407),.PCURVE_S1.);
#2258=IFCSURFACECURVE(#2833,(#3408),.PCURVE_S1.);
#2259=IFCSURFACECURVE(#2834,(#3409),.PCURVE_S1.);
#2260=IFCSURFACECURVE(#2835,(#3410),.PCURVE_S1.);
#2261=IFCSURFACECURVE(#2836,(#3411),.PCURVE_S1.);
#2262=IFCSURFACECURVE(#2837,(#3412),.PCURVE_S1.);
#2263=IFCSURFACECURVE(#2838,(#3413),.PCURVE_S1.);
#2264=IFCSURFACECURVE(#2839,(#3414),.PCURVE_S1.);
#2265=IFCSURFACECURVE(#2840,(#3415),.PCURVE_S1.);
#2266=IFCSURFACECURVE(#2841,(#3416),.PCURVE_S1.);
#2267=IFCSURFACECURVE(#2842,(#3417),.PCURVE_S1.);
#2268=IFCSURFACECURVE(#2843,(#3418),.PCURVE_S1.);
#2269=IFCSURFACECURVE(#2844,(#3419),.PCURVE_S1.);
#2270=IFCSURFACECURVE(#2845,(#3420),.PCURVE_S1.);
#2271=IFCSURFACECURVE(#2846,(#3421),.PCURVE_S1.);
#2272=IFCSURFACECURVE(#2847,(#3422),.PCURVE_S1.);
#2273=IFCSURFACECURVE(#2848,(#3423),.PCURVE_S1.);
#2274=IFCSURFACECURVE(#2849,(#3424),.PCURVE_S1.);
#2275=IFCSURFACECURVE(#2850,(#3425),.PCURVE_S1.);
#2276=IFCSURFACECURVE(#2851,(#3426),.PCURVE_S1.);
#2277=IFCSURFACECURVE(#2852,(#3427),.PCURVE_S1.);
#2278=IFCSURFACECURVE(#2853,(#3428),.PCURVE_S1.);
#2279=IFCSURFACECURVE(#2854,(#3429),.PCURVE_S1.);
#2280=IFCSURFACECURVE(#2855,(#3430),.PCURVE_S1.);
#2281=IFCSURFACECURVE(#2856,(#3431),.PCURVE_S1.);
#2282=IFCSURFACECURVE(#2857,(#3432),.PCURVE_S1.);
#2283=IFCSURFACECURVE(#2858,(#3433),.PCURVE_S1.);
#2284=IFCSURFACECURVE(#2859,(#3434),.PCURVE_S1.);
#2285=IFCSURFACECURVE(#2860,(#3435),.PCURVE_S1.);
#2286=IFCSURFACECURVE(#2861,(#3436),.PCURVE_S1.);
#2287=IFCSURFACECURVE(#2862,(#3437),.PCURVE_S1.);
#2288=IFCSURFACECURVE(#2863,(#3438),.PCURVE_S1.);
#2289=IFCSURFACECURVE(#2864,(#3439),.PCURVE_S1.);
#2290=IFCSURFACECURVE(#2865,(#3440),.PCURVE_S1.);
#2291=IFCSURFACECURVE(#2866,(#3441),.PCURVE_S1.);
#2292=IFCSURFACECURVE(#2867,(#3442),.PCURVE_S1.);
#2293=IFCSURFACECURVE(#2868,(#3443),.PCURVE_S1.);
#2294=IFCSURFACECURVE(#2869,(#3444),.PCURVE_S1.);
#2295=IFCSURFACECURVE(#2870,(#3445),.PCURVE_S1.);
#2296=IFCSURFACECURVE(#2871,(#3446),.PCURVE_S1.);
#2297=IFCSURFACECURVE(#2872,(#3447),.PCURVE_S1.);
#2298=IFCSURFACECURVE(#2873,(#3448),.PCURVE_S1.);
#2299=IFCSURFACECURVE(#2874,(#3449),.PCURVE_S1.);
#2300=IFCSURFACECURVE(#2875,(#3450),.PCURVE_S1.);
#2301=IFCSURFACECURVE(#2876,(#3451),.PCURVE_S1.);
#2302=IFCSURFACECURVE(#2877,(#3452),.PCURVE_S1.);
#2303=IFCSURFACECURVE(#2878,(#3453),.PCURVE_S1.);
#2304=IFCSURFACECURVE(#2879,(#3454),.PCURVE_S1.);
#2305=IFCSURFACECURVE(#2880,(#3455),.PCURVE_S1.);
#2306=IFCSURFACECURVE(#2881,(#3456),.PCURVE_S1.);
#2307=IFCSURFACECURVE(#2882,(#3457),.PCURVE_S1.);
#2308=IFCSURFACECURVE(#2883,(#3458),.PCURVE_S1.);
#2309=IFCSURFACECURVE(#2884,(#3459),.PCURVE_S1.);
#2310=IFCSURFACECURVE(#2885,(#3460),.PCURVE_S1.);
#2311=IFCSURFACECURVE(#2886,(#3461),.PCURVE_S1.);
#2312=IFCSURFACECURVE(#2887,(#3462),.PCURVE_S1.);
#2313=IFCSURFACECURVE(#2888,(#3463),.PCURVE_S1.);
#2314=IFCSURFACECURVE(#2889,(#3464),.PCURVE_S1.);
#2315=IFCSURFACECURVE(#2890,(#3465),.PCURVE_S1.);
#2316=IFCSURFACECURVE(#2891,(#3466),.PCURVE_S1.);
#2317=IFCSURFACECURVE(#2892,(#3467),.PCURVE_S1.);
#2318=IFCSURFACECURVE(#2893,(#3468),.PCURVE_S1.);
#2319=IFCSURFACECURVE(#2894,(#3469),.PCURVE_S1.);
#2320=IFCSURFACECURVE(#2895,(#3470),.PCURVE_S1.);
#2321=IFCSURFACECURVE(#2896,(#3471),.PCURVE_S1.);
#2322=IFCSURFACECURVE(#2897,(#3472),.PCURVE_S1.);
#2323=IFCSURFACECURVE(#2898,(#3473),.PCURVE_S1.);
#2324=IFCSURFACECURVE(#2899,(#3474),.PCURVE_S1.);
#2325=IFCSURFACECURVE(#2900,(#3475),.PCURVE_S1.);
#2326=IFCSURFACECURVE(#2901,(#3476),.PCURVE_S1.);
#2327=IFCSURFACECURVE(#2902,(#3477),.PCURVE_S1.);
#2328=IFCSURFACECURVE(#2903,(#3478),.PCURVE_S1.);
#2329=IFCSURFACECURVE(#2904,(#3479),.PCURVE_S1.);
#2330=IFCBSPLINECURVEWITHKNOTS(1,(#4071,#4084),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2331=IFCBSPLINECURVEWITHKNOTS(1,(#4084,#4086),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2332=IFCBSPLINECURVEWITHKNOTS(1,(#4086,#4087),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2333=IFCBSPLINECURVEWITHKNOTS(1,(#4087,#4089),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2334=IFCBSPLINECURVEWITHKNOTS(1,(#4089,#4090),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2335=IFCBSPLINECURVEWITHKNOTS(1,(#4090,#4092),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2336=IFCBSPLINECURVEWITHKNOTS(1,(#4092,#4093),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2337=IFCBSPLINECURVEWITHKNOTS(1,(#4093,#4095),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2338=IFCBSPLINECURVEWITHKNOTS(1,(#4095,#4096),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2339=IFCBSPLINECURVEWITHKNOTS(1,(#4096,#4098),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2340=IFCBSPLINECURVEWITHKNOTS(1,(#4098,#4099),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2341=IFCBSPLINECURVEWITHKNOTS(1,(#4099,#4101),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2342=IFCBSPLINECURVEWITHKNOTS(1,(#4101,#4102),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2343=IFCBSPLINECURVEWITHKNOTS(1,(#4102,#4104),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2344=IFCBSPLINECURVEWITHKNOTS(1,(#4104,#4105),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2345=IFCBSPLINECURVEWITHKNOTS(1,(#4105,#4107),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2346=IFCBSPLINECURVEWITHKNOTS(1,(#4107,#4108),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2347=IFCBSPLINECURVEWITHKNOTS(1,(#4108,#4110),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2348=IFCBSPLINECURVEWITHKNOTS(1,(#4110,#4111),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2349=IFCBSPLINECURVEWITHKNOTS(1,(#4111,#4113),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2350=IFCBSPLINECURVEWITHKNOTS(1,(#4113,#4114),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2351=IFCBSPLINECURVEWITHKNOTS(1,(#4114,#4116),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2352=IFCBSPLINECURVEWITHKNOTS(1,(#4116,#4117),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2353=IFCBSPLINECURVEWITHKNOTS(1,(#4117,#4119),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2354=IFCBSPLINECURVEWITHKNOTS(1,(#4119,#4120),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2355=IFCBSPLINECURVEWITHKNOTS(1,(#4120,#4122),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2356=IFCBSPLINECURVEWITHKNOTS(1,(#4122,#4123),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2357=IFCBSPLINECURVEWITHKNOTS(1,(#4123,#4125),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2358=IFCBSPLINECURVEWITHKNOTS(1,(#4125,#4126),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2359=IFCBSPLINECURVEWITHKNOTS(1,(#4126,#4128),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2360=IFCBSPLINECURVEWITHKNOTS(1,(#4128,#4129),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2361=IFCBSPLINECURVEWITHKNOTS(1,(#4129,#4131),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2362=IFCBSPLINECURVEWITHKNOTS(1,(#4131,#4132),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2363=IFCBSPLINECURVEWITHKNOTS(1,(#4132,#4134),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2364=IFCBSPLINECURVEWITHKNOTS(1,(#4134,#4135),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2365=IFCBSPLINECURVEWITHKNOTS(1,(#4135,#4137),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2366=IFCBSPLINECURVEWITHKNOTS(1,(#4137,#4138),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2367=IFCBSPLINECURVEWITHKNOTS(1,(#4138,#4140),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2368=IFCBSPLINECURVEWITHKNOTS(1,(#4140,#4141),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2369=IFCBSPLINECURVEWITHKNOTS(1,(#4141,#4143),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2370=IFCBSPLINECURVEWITHKNOTS(1,(#4143,#4144),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2371=IFCBSPLINECURVEWITHKNOTS(1,(#4144,#4146),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2372=IFCBSPLINECURVEWITHKNOTS(1,(#4146,#4147),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2373=IFCBSPLINECURVEWITHKNOTS(1,(#4147,#4149),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2374=IFCBSPLINECURVEWITHKNOTS(1,(#4149,#4150),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2375=IFCBSPLINECURVEWITHKNOTS(1,(#4150,#4152),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2376=IFCBSPLINECURVEWITHKNOTS(1,(#4152,#4153),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2377=IFCBSPLINECURVEWITHKNOTS(1,(#4153,#4155),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2378=IFCBSPLINECURVEWITHKNOTS(1,(#4155,#4156),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2379=IFCBSPLINECURVEWITHKNOTS(1,(#4156,#4158),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2380=IFCBSPLINECURVEWITHKNOTS(1,(#4158,#4159),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2381=IFCBSPLINECURVEWITHKNOTS(1,(#4159,#4161),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2382=IFCBSPLINECURVEWITHKNOTS(1,(#4161,#4162),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2383=IFCBSPLINECURVEWITHKNOTS(1,(#4162,#4164),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2384=IFCBSPLINECURVEWITHKNOTS(1,(#4164,#4165),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2385=IFCBSPLINECURVEWITHKNOTS(1,(#4165,#4167),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2386=IFCBSPLINECURVEWITHKNOTS(1,(#4167,#4168),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2387=IFCBSPLINECURVEWITHKNOTS(1,(#4168,#4170),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2388=IFCBSPLINECURVEWITHKNOTS(1,(#4170,#4171),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2389=IFCBSPLINECURVEWITHKNOTS(1,(#4171,#4173),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2390=IFCBSPLINECURVEWITHKNOTS(1,(#4173,#4174),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2391=IFCBSPLINECURVEWITHKNOTS(1,(#4174,#4176),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2392=IFCBSPLINECURVEWITHKNOTS(1,(#4176,#4177),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2393=IFCBSPLINECURVEWITHKNOTS(1,(#4177,#4179),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2394=IFCBSPLINECURVEWITHKNOTS(1,(#4179,#4180),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2395=IFCBSPLINECURVEWITHKNOTS(1,(#4180,#4182),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2396=IFCBSPLINECURVEWITHKNOTS(1,(#4182,#4183),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2397=IFCBSPLINECURVEWITHKNOTS(1,(#4183,#4185),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2398=IFCBSPLINECURVEWITHKNOTS(1,(#4185,#4186),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2399=IFCBSPLINECURVEWITHKNOTS(1,(#4186,#4188),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2400=IFCBSPLINECURVEWITHKNOTS(1,(#4188,#4189),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2401=IFCBSPLINECURVEWITHKNOTS(1,(#4189,#4191),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2402=IFCBSPLINECURVEWITHKNOTS(1,(#4191,#4192),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2403=IFCBSPLINECURVEWITHKNOTS(1,(#4192,#4194),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2404=IFCBSPLINECURVEWITHKNOTS(1,(#4194,#4195),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2405=IFCBSPLINECURVEWITHKNOTS(1,(#4195,#4197),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2406=IFCBSPLINECURVEWITHKNOTS(1,(#4197,#4198),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2407=IFCBSPLINECURVEWITHKNOTS(1,(#4198,#4200),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2408=IFCBSPLINECURVEWITHKNOTS(1,(#4200,#4201),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2409=IFCBSPLINECURVEWITHKNOTS(1,(#4201,#4203),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2410=IFCBSPLINECURVEWITHKNOTS(1,(#4203,#4204),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2411=IFCBSPLINECURVEWITHKNOTS(1,(#4204,#4206),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2412=IFCBSPLINECURVEWITHKNOTS(1,(#4206,#4207),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2413=IFCBSPLINECURVEWITHKNOTS(1,(#4207,#4209),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2414=IFCBSPLINECURVEWITHKNOTS(1,(#4209,#4210),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2415=IFCBSPLINECURVEWITHKNOTS(1,(#4210,#4212),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2416=IFCBSPLINECURVEWITHKNOTS(1,(#4212,#4213),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2417=IFCBSPLINECURVEWITHKNOTS(1,(#4213,#4215),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2418=IFCBSPLINECURVEWITHKNOTS(1,(#4215,#4216),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2419=IFCBSPLINECURVEWITHKNOTS(1,(#4216,#4218),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2420=IFCBSPLINECURVEWITHKNOTS(1,(#4218,#4219),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2421=IFCBSPLINECURVEWITHKNOTS(1,(#4219,#4221),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2422=IFCBSPLINECURVEWITHKNOTS(1,(#4221,#4222),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2423=IFCBSPLINECURVEWITHKNOTS(1,(#4222,#4224),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2424=IFCBSPLINECURVEWITHKNOTS(1,(#4224,#4225),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2425=IFCBSPLINECURVEWITHKNOTS(1,(#4225,#4227),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2426=IFCBSPLINECURVEWITHKNOTS(1,(#4227,#4228),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2427=IFCBSPLINECURVEWITHKNOTS(1,(#4228,#4230),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2428=IFCBSPLINECURVEWITHKNOTS(1,(#4230,#4231),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2429=IFCBSPLINECURVEWITHKNOTS(1,(#4231,#4233),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2430=IFCBSPLINECURVEWITHKNOTS(1,(#4233,#4234),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2431=IFCBSPLINECURVEWITHKNOTS(1,(#4234,#4236),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2432=IFCBSPLINECURVEWITHKNOTS(1,(#4236,#4237),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2433=IFCBSPLINECURVEWITHKNOTS(1,(#4237,#4239),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2434=IFCBSPLINECURVEWITHKNOTS(1,(#4239,#4240),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2435=IFCBSPLINECURVEWITHKNOTS(1,(#4240,#4242),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2436=IFCBSPLINECURVEWITHKNOTS(1,(#4242,#4243),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2437=IFCBSPLINECURVEWITHKNOTS(1,(#4243,#4245),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2438=IFCBSPLINECURVEWITHKNOTS(1,(#4245,#4246),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2439=IFCBSPLINECURVEWITHKNOTS(1,(#4246,#4248),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2440=IFCBSPLINECURVEWITHKNOTS(1,(#4248,#4249),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2441=IFCBSPLINECURVEWITHKNOTS(1,(#4249,#4251),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2442=IFCBSPLINECURVEWITHKNOTS(1,(#4251,#4252),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2443=IFCBSPLINECURVEWITHKNOTS(1,(#4252,#4254),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2444=IFCBSPLINECURVEWITHKNOTS(1,(#4254,#4255),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2445=IFCBSPLINECURVEWITHKNOTS(1,(#4255,#4257),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2446=IFCBSPLINECURVEWITHKNOTS(1,(#4257,#4258),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2447=IFCBSPLINECURVEWITHKNOTS(1,(#4258,#4260),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2448=IFCBSPLINECURVEWITHKNOTS(1,(#4260,#4261),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2449=IFCBSPLINECURVEWITHKNOTS(1,(#4261,#4263),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2450=IFCBSPLINECURVEWITHKNOTS(1,(#4263,#4264),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2451=IFCBSPLINECURVEWITHKNOTS(1,(#4264,#4266),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2452=IFCBSPLINECURVEWITHKNOTS(1,(#4266,#4267),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2453=IFCBSPLINECURVEWITHKNOTS(1,(#4267,#4269),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2454=IFCBSPLINECURVEWITHKNOTS(1,(#4269,#4270),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2455=IFCBSPLINECURVEWITHKNOTS(1,(#4270,#4272),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2456=IFCBSPLINECURVEWITHKNOTS(1,(#4272,#4273),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2457=IFCBSPLINECURVEWITHKNOTS(1,(#4273,#4275),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2458=IFCBSPLINECURVEWITHKNOTS(1,(#4275,#4276),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2459=IFCBSPLINECURVEWITHKNOTS(1,(#4276,#4278),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2460=IFCBSPLINECURVEWITHKNOTS(1,(#4278,#4279),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2461=IFCBSPLINECURVEWITHKNOTS(1,(#4279,#4281),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2462=IFCBSPLINECURVEWITHKNOTS(1,(#4281,#4282),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2463=IFCBSPLINECURVEWITHKNOTS(1,(#4282,#4284),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2464=IFCBSPLINECURVEWITHKNOTS(1,(#4284,#4285),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2465=IFCBSPLINECURVEWITHKNOTS(1,(#4285,#4287),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2466=IFCBSPLINECURVEWITHKNOTS(1,(#4287,#4288),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2467=IFCBSPLINECURVEWITHKNOTS(1,(#4288,#4290),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2468=IFCBSPLINECURVEWITHKNOTS(1,(#4290,#4291),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2469=IFCBSPLINECURVEWITHKNOTS(1,(#4291,#4293),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2470=IFCBSPLINECURVEWITHKNOTS(1,(#4293,#4294),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2471=IFCBSPLINECURVEWITHKNOTS(1,(#4294,#4296),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2472=IFCBSPLINECURVEWITHKNOTS(1,(#4296,#4297),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2473=IFCBSPLINECURVEWITHKNOTS(1,(#4297,#4299),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2474=IFCBSPLINECURVEWITHKNOTS(1,(#4299,#4300),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2475=IFCBSPLINECURVEWITHKNOTS(1,(#4300,#4302),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2476=IFCBSPLINECURVEWITHKNOTS(1,(#4302,#4303),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2477=IFCBSPLINECURVEWITHKNOTS(1,(#4303,#4305),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2478=IFCBSPLINECURVEWITHKNOTS(1,(#4305,#4306),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2479=IFCBSPLINECURVEWITHKNOTS(1,(#4306,#4308),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2480=IFCBSPLINECURVEWITHKNOTS(1,(#4308,#4309),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2481=IFCBSPLINECURVEWITHKNOTS(1,(#4309,#4311),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2482=IFCBSPLINECURVEWITHKNOTS(1,(#4311,#4312),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2483=IFCBSPLINECURVEWITHKNOTS(1,(#4312,#4314),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2484=IFCBSPLINECURVEWITHKNOTS(1,(#4314,#4315),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2485=IFCBSPLINECURVEWITHKNOTS(1,(#4315,#4317),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2486=IFCBSPLINECURVEWITHKNOTS(1,(#4317,#4318),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2487=IFCBSPLINECURVEWITHKNOTS(1,(#4318,#4320),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2488=IFCBSPLINECURVEWITHKNOTS(1,(#4320,#4321),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2489=IFCBSPLINECURVEWITHKNOTS(1,(#4321,#4323),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2490=IFCBSPLINECURVEWITHKNOTS(1,(#4323,#4324),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2491=IFCBSPLINECURVEWITHKNOTS(1,(#4324,#4326),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2492=IFCBSPLINECURVEWITHKNOTS(1,(#4326,#4327),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2493=IFCBSPLINECURVEWITHKNOTS(1,(#4327,#4329),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2494=IFCBSPLINECURVEWITHKNOTS(1,(#4329,#4330),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2495=IFCBSPLINECURVEWITHKNOTS(1,(#4330,#4332),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2496=IFCBSPLINECURVEWITHKNOTS(1,(#4332,#4333),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2497=IFCBSPLINECURVEWITHKNOTS(1,(#4333,#4335),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2498=IFCBSPLINECURVEWITHKNOTS(1,(#4335,#4336),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2499=IFCBSPLINECURVEWITHKNOTS(1,(#4336,#4338),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2500=IFCBSPLINECURVEWITHKNOTS(1,(#4338,#4339),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2501=IFCBSPLINECURVEWITHKNOTS(1,(#4339,#4341),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2502=IFCBSPLINECURVEWITHKNOTS(1,(#4341,#4342),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2503=IFCBSPLINECURVEWITHKNOTS(1,(#4342,#4344),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2504=IFCBSPLINECURVEWITHKNOTS(1,(#4344,#4345),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2505=IFCBSPLINECURVEWITHKNOTS(1,(#4345,#4347),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2506=IFCBSPLINECURVEWITHKNOTS(1,(#4347,#4348),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2507=IFCBSPLINECURVEWITHKNOTS(1,(#4348,#4350),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2508=IFCBSPLINECURVEWITHKNOTS(1,(#4350,#4351),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2509=IFCBSPLINECURVEWITHKNOTS(1,(#4351,#4353),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2510=IFCBSPLINECURVEWITHKNOTS(1,(#4353,#4354),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2511=IFCBSPLINECURVEWITHKNOTS(1,(#4354,#4356),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2512=IFCBSPLINECURVEWITHKNOTS(1,(#4356,#4357),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2513=IFCBSPLINECURVEWITHKNOTS(1,(#4357,#4359),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2514=IFCBSPLINECURVEWITHKNOTS(1,(#4359,#4360),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2515=IFCBSPLINECURVEWITHKNOTS(1,(#4360,#4081),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2516=IFCBSPLINECURVEWITHKNOTS(1,(#4081,#4363),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2517=IFCBSPLINECURVEWITHKNOTS(1,(#4363,#4365),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2518=IFCBSPLINECURVEWITHKNOTS(1,(#4365,#4367),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2519=IFCBSPLINECURVEWITHKNOTS(1,(#4367,#4082),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2520=IFCBSPLINECURVEWITHKNOTS(1,(#4082,#4369),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2521=IFCBSPLINECURVEWITHKNOTS(1,(#4369,#4371),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2522=IFCBSPLINECURVEWITHKNOTS(1,(#4371,#4372),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2523=IFCBSPLINECURVEWITHKNOTS(1,(#4372,#4374),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2524=IFCBSPLINECURVEWITHKNOTS(1,(#4374,#4375),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2525=IFCBSPLINECURVEWITHKNOTS(1,(#4375,#4377),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2526=IFCBSPLINECURVEWITHKNOTS(1,(#4377,#4378),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2527=IFCBSPLINECURVEWITHKNOTS(1,(#4378,#4380),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2528=IFCBSPLINECURVEWITHKNOTS(1,(#4380,#4381),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2529=IFCBSPLINECURVEWITHKNOTS(1,(#4381,#4383),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2530=IFCBSPLINECURVEWITHKNOTS(1,(#4383,#4384),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2531=IFCBSPLINECURVEWITHKNOTS(1,(#4384,#4386),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2532=IFCBSPLINECURVEWITHKNOTS(1,(#4386,#4387),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2533=IFCBSPLINECURVEWITHKNOTS(1,(#4387,#4389),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2534=IFCBSPLINECURVEWITHKNOTS(1,(#4389,#4390),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2535=IFCBSPLINECURVEWITHKNOTS(1,(#4390,#4392),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2536=IFCBSPLINECURVEWITHKNOTS(1,(#4392,#4393),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2537=IFCBSPLINECURVEWITHKNOTS(1,(#4393,#4395),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2538=IFCBSPLINECURVEWITHKNOTS(1,(#4395,#4396),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2539=IFCBSPLINECURVEWITHKNOTS(1,(#4396,#4398),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2540=IFCBSPLINECURVEWITHKNOTS(1,(#4398,#4399),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2541=IFCBSPLINECURVEWITHKNOTS(1,(#4399,#4401),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2542=IFCBSPLINECURVEWITHKNOTS(1,(#4401,#4402),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2543=IFCBSPLINECURVEWITHKNOTS(1,(#4402,#4404),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2544=IFCBSPLINECURVEWITHKNOTS(1,(#4404,#4405),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2545=IFCBSPLINECURVEWITHKNOTS(1,(#4405,#4407),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2546=IFCBSPLINECURVEWITHKNOTS(1,(#4407,#4408),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2547=IFCBSPLINECURVEWITHKNOTS(1,(#4408,#4410),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2548=IFCBSPLINECURVEWITHKNOTS(1,(#4410,#4411),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2549=IFCBSPLINECURVEWITHKNOTS(1,(#4411,#4413),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2550=IFCBSPLINECURVEWITHKNOTS(1,(#4413,#4414),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2551=IFCBSPLINECURVEWITHKNOTS(1,(#4414,#4416),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2552=IFCBSPLINECURVEWITHKNOTS(1,(#4416,#4417),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2553=IFCBSPLINECURVEWITHKNOTS(1,(#4417,#4419),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2554=IFCBSPLINECURVEWITHKNOTS(1,(#4419,#4420),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2555=IFCBSPLINECURVEWITHKNOTS(1,(#4420,#4422),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2556=IFCBSPLINECURVEWITHKNOTS(1,(#4422,#4423),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2557=IFCBSPLINECURVEWITHKNOTS(1,(#4423,#4425),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2558=IFCBSPLINECURVEWITHKNOTS(1,(#4425,#4426),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2559=IFCBSPLINECURVEWITHKNOTS(1,(#4426,#4428),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2560=IFCBSPLINECURVEWITHKNOTS(1,(#4428,#4429),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2561=IFCBSPLINECURVEWITHKNOTS(1,(#4429,#4431),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2562=IFCBSPLINECURVEWITHKNOTS(1,(#4431,#4432),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2563=IFCBSPLINECURVEWITHKNOTS(1,(#4432,#4434),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2564=IFCBSPLINECURVEWITHKNOTS(1,(#4434,#4435),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2565=IFCBSPLINECURVEWITHKNOTS(1,(#4435,#4437),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2566=IFCBSPLINECURVEWITHKNOTS(1,(#4437,#4438),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2567=IFCBSPLINECURVEWITHKNOTS(1,(#4438,#4440),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2568=IFCBSPLINECURVEWITHKNOTS(1,(#4440,#4441),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2569=IFCBSPLINECURVEWITHKNOTS(1,(#4441,#4443),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2570=IFCBSPLINECURVEWITHKNOTS(1,(#4443,#4444),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2571=IFCBSPLINECURVEWITHKNOTS(1,(#4444,#4446),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2572=IFCBSPLINECURVEWITHKNOTS(1,(#4446,#4447),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2573=IFCBSPLINECURVEWITHKNOTS(1,(#4447,#4449),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2574=IFCBSPLINECURVEWITHKNOTS(1,(#4449,#4450),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2575=IFCBSPLINECURVEWITHKNOTS(1,(#4450,#4452),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2576=IFCBSPLINECURVEWITHKNOTS(1,(#4452,#4453),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2577=IFCBSPLINECURVEWITHKNOTS(1,(#4453,#4455),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2578=IFCBSPLINECURVEWITHKNOTS(1,(#4455,#4456),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2579=IFCBSPLINECURVEWITHKNOTS(1,(#4456,#4458),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2580=IFCBSPLINECURVEWITHKNOTS(1,(#4458,#4459),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2581=IFCBSPLINECURVEWITHKNOTS(1,(#4459,#4461),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2582=IFCBSPLINECURVEWITHKNOTS(1,(#4461,#4462),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2583=IFCBSPLINECURVEWITHKNOTS(1,(#4462,#4464),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2584=IFCBSPLINECURVEWITHKNOTS(1,(#4464,#4465),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2585=IFCBSPLINECURVEWITHKNOTS(1,(#4465,#4467),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2586=IFCBSPLINECURVEWITHKNOTS(1,(#4467,#4468),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2587=IFCBSPLINECURVEWITHKNOTS(1,(#4468,#4470),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2588=IFCBSPLINECURVEWITHKNOTS(1,(#4470,#4471),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2589=IFCBSPLINECURVEWITHKNOTS(1,(#4471,#4473),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2590=IFCBSPLINECURVEWITHKNOTS(1,(#4473,#4474),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2591=IFCBSPLINECURVEWITHKNOTS(1,(#4474,#4476),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2592=IFCBSPLINECURVEWITHKNOTS(1,(#4476,#4477),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2593=IFCBSPLINECURVEWITHKNOTS(1,(#4477,#4479),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2594=IFCBSPLINECURVEWITHKNOTS(1,(#4479,#4480),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2595=IFCBSPLINECURVEWITHKNOTS(1,(#4480,#4482),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2596=IFCBSPLINECURVEWITHKNOTS(1,(#4482,#4483),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2597=IFCBSPLINECURVEWITHKNOTS(1,(#4483,#4485),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2598=IFCBSPLINECURVEWITHKNOTS(1,(#4485,#4486),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2599=IFCBSPLINECURVEWITHKNOTS(1,(#4486,#4488),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2600=IFCBSPLINECURVEWITHKNOTS(1,(#4488,#4489),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2601=IFCBSPLINECURVEWITHKNOTS(1,(#4489,#4491),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2602=IFCBSPLINECURVEWITHKNOTS(1,(#4491,#4492),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2603=IFCBSPLINECURVEWITHKNOTS(1,(#4492,#4494),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2604=IFCBSPLINECURVEWITHKNOTS(1,(#4494,#4495),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2605=IFCBSPLINECURVEWITHKNOTS(1,(#4495,#4497),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2606=IFCBSPLINECURVEWITHKNOTS(1,(#4497,#4498),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2607=IFCBSPLINECURVEWITHKNOTS(1,(#4498,#4500),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2608=IFCBSPLINECURVEWITHKNOTS(1,(#4500,#4501),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2609=IFCBSPLINECURVEWITHKNOTS(1,(#4501,#4503),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2610=IFCBSPLINECURVEWITHKNOTS(1,(#4503,#4504),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2611=IFCBSPLINECURVEWITHKNOTS(1,(#4504,#4506),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2612=IFCBSPLINECURVEWITHKNOTS(1,(#4506,#4507),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2613=IFCBSPLINECURVEWITHKNOTS(1,(#4507,#4509),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2614=IFCBSPLINECURVEWITHKNOTS(1,(#4509,#4510),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2615=IFCBSPLINECURVEWITHKNOTS(1,(#4510,#4512),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2616=IFCBSPLINECURVEWITHKNOTS(1,(#4512,#4513),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2617=IFCBSPLINECURVEWITHKNOTS(1,(#4513,#4515),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2618=IFCBSPLINECURVEWITHKNOTS(1,(#4515,#4516),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2619=IFCBSPLINECURVEWITHKNOTS(1,(#4516,#4518),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2620=IFCBSPLINECURVEWITHKNOTS(1,(#4518,#4519),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2621=IFCBSPLINECURVEWITHKNOTS(1,(#4519,#4521),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2622=IFCBSPLINECURVEWITHKNOTS(1,(#4521,#4522),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2623=IFCBSPLINECURVEWITHKNOTS(1,(#4522,#4524),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2624=IFCBSPLINECURVEWITHKNOTS(1,(#4524,#4525),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2625=IFCBSPLINECURVEWITHKNOTS(1,(#4525,#4527),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2626=IFCBSPLINECURVEWITHKNOTS(1,(#4527,#4528),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2627=IFCBSPLINECURVEWITHKNOTS(1,(#4528,#4530),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2628=IFCBSPLINECURVEWITHKNOTS(1,(#4530,#4531),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2629=IFCBSPLINECURVEWITHKNOTS(1,(#4531,#4533),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2630=IFCBSPLINECURVEWITHKNOTS(1,(#4533,#4534),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2631=IFCBSPLINECURVEWITHKNOTS(1,(#4534,#4536),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2632=IFCBSPLINECURVEWITHKNOTS(1,(#4536,#4537),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2633=IFCBSPLINECURVEWITHKNOTS(1,(#4537,#4539),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2634=IFCBSPLINECURVEWITHKNOTS(1,(#4539,#4540),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2635=IFCBSPLINECURVEWITHKNOTS(1,(#4540,#4542),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2636=IFCBSPLINECURVEWITHKNOTS(1,(#4542,#4543),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2637=IFCBSPLINECURVEWITHKNOTS(1,(#4543,#4545),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2638=IFCBSPLINECURVEWITHKNOTS(1,(#4545,#4546),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2639=IFCBSPLINECURVEWITHKNOTS(1,(#4546,#4548),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2640=IFCBSPLINECURVEWITHKNOTS(1,(#4548,#4549),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2641=IFCBSPLINECURVEWITHKNOTS(1,(#4549,#4551),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2642=IFCBSPLINECURVEWITHKNOTS(1,(#4551,#4552),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2643=IFCBSPLINECURVEWITHKNOTS(1,(#4552,#4554),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2644=IFCBSPLINECURVEWITHKNOTS(1,(#4554,#4555),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2645=IFCBSPLINECURVEWITHKNOTS(1,(#4555,#4557),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2646=IFCBSPLINECURVEWITHKNOTS(1,(#4557,#4558),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2647=IFCBSPLINECURVEWITHKNOTS(1,(#4558,#4560),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2648=IFCBSPLINECURVEWITHKNOTS(1,(#4560,#4561),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2649=IFCBSPLINECURVEWITHKNOTS(1,(#4561,#4563),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2650=IFCBSPLINECURVEWITHKNOTS(1,(#4563,#4564),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2651=IFCBSPLINECURVEWITHKNOTS(1,(#4564,#4566),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2652=IFCBSPLINECURVEWITHKNOTS(1,(#4566,#4567),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2653=IFCBSPLINECURVEWITHKNOTS(1,(#4567,#4569),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2654=IFCBSPLINECURVEWITHKNOTS(1,(#4569,#4570),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2655=IFCBSPLINECURVEWITHKNOTS(1,(#4570,#4572),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2656=IFCBSPLINECURVEWITHKNOTS(1,(#4572,#4573),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2657=IFCBSPLINECURVEWITHKNOTS(1,(#4573,#4575),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2658=IFCBSPLINECURVEWITHKNOTS(1,(#4575,#4576),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2659=IFCBSPLINECURVEWITHKNOTS(1,(#4576,#4578),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2660=IFCBSPLINECURVEWITHKNOTS(1,(#4578,#4579),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2661=IFCBSPLINECURVEWITHKNOTS(1,(#4579,#4581),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2662=IFCBSPLINECURVEWITHKNOTS(1,(#4581,#4582),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2663=IFCBSPLINECURVEWITHKNOTS(1,(#4582,#4584),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2664=IFCBSPLINECURVEWITHKNOTS(1,(#4584,#4585),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2665=IFCBSPLINECURVEWITHKNOTS(1,(#4585,#4587),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2666=IFCBSPLINECURVEWITHKNOTS(1,(#4587,#4588),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2667=IFCBSPLINECURVEWITHKNOTS(1,(#4588,#4590),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2668=IFCBSPLINECURVEWITHKNOTS(1,(#4590,#4591),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2669=IFCBSPLINECURVEWITHKNOTS(1,(#4591,#4593),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2670=IFCBSPLINECURVEWITHKNOTS(1,(#4593,#4594),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2671=IFCBSPLINECURVEWITHKNOTS(1,(#4594,#4596),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2672=IFCBSPLINECURVEWITHKNOTS(1,(#4596,#4597),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2673=IFCBSPLINECURVEWITHKNOTS(1,(#4597,#4599),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2674=IFCBSPLINECURVEWITHKNOTS(1,(#4599,#4600),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2675=IFCBSPLINECURVEWITHKNOTS(1,(#4600,#4602),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2676=IFCBSPLINECURVEWITHKNOTS(1,(#4602,#4603),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2677=IFCBSPLINECURVEWITHKNOTS(1,(#4603,#4605),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2678=IFCBSPLINECURVEWITHKNOTS(1,(#4605,#4606),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2679=IFCBSPLINECURVEWITHKNOTS(1,(#4606,#4608),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2680=IFCBSPLINECURVEWITHKNOTS(1,(#4608,#4609),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2681=IFCBSPLINECURVEWITHKNOTS(1,(#4609,#4611),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2682=IFCBSPLINECURVEWITHKNOTS(1,(#4611,#4612),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2683=IFCBSPLINECURVEWITHKNOTS(1,(#4612,#4614),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2684=IFCBSPLINECURVEWITHKNOTS(1,(#4614,#4615),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2685=IFCBSPLINECURVEWITHKNOTS(1,(#4615,#4617),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2686=IFCBSPLINECURVEWITHKNOTS(1,(#4617,#4618),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2687=IFCBSPLINECURVEWITHKNOTS(1,(#4618,#4620),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2688=IFCBSPLINECURVEWITHKNOTS(1,(#4620,#4621),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2689=IFCBSPLINECURVEWITHKNOTS(1,(#4621,#4623),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2690=IFCBSPLINECURVEWITHKNOTS(1,(#4623,#4624),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2691=IFCBSPLINECURVEWITHKNOTS(1,(#4624,#4626),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2692=IFCBSPLINECURVEWITHKNOTS(1,(#4626,#4627),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2693=IFCBSPLINECURVEWITHKNOTS(1,(#4627,#4629),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2694=IFCBSPLINECURVEWITHKNOTS(1,(#4629,#4630),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2695=IFCBSPLINECURVEWITHKNOTS(1,(#4630,#4632),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2696=IFCBSPLINECURVEWITHKNOTS(1,(#4632,#4633),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2697=IFCBSPLINECURVEWITHKNOTS(1,(#4633,#4635),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2698=IFCBSPLINECURVEWITHKNOTS(1,(#4635,#4636),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2699=IFCBSPLINECURVEWITHKNOTS(1,(#4636,#4638),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2700=IFCBSPLINECURVEWITHKNOTS(1,(#4638,#4639),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2701=IFCBSPLINECURVEWITHKNOTS(1,(#4639,#4641),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2702=IFCBSPLINECURVEWITHKNOTS(1,(#4641,#4642),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2703=IFCBSPLINECURVEWITHKNOTS(1,(#4642,#4644),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2704=IFCBSPLINECURVEWITHKNOTS(1,(#4644,#4645),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2705=IFCBSPLINECURVEWITHKNOTS(1,(#4645,#4072),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2706=IFCBSPLINECURVEWITHKNOTS(1,(#4072,#4648),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2707=IFCBSPLINECURVEWITHKNOTS(1,(#4648,#4650),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2708=IFCBSPLINECURVEWITHKNOTS(1,(#4650,#4652),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2709=IFCBSPLINECURVEWITHKNOTS(1,(#4652,#4071),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2710=IFCBSPLINECURVEWITHKNOTS(1,(#4654,#4655),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2711=IFCBSPLINECURVEWITHKNOTS(1,(#4655,#4656),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2712=IFCBSPLINECURVEWITHKNOTS(1,(#4656,#4658),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2713=IFCBSPLINECURVEWITHKNOTS(1,(#4658,#4659),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2714=IFCBSPLINECURVEWITHKNOTS(1,(#4659,#4660),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2715=IFCBSPLINECURVEWITHKNOTS(1,(#4660,#4661),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2716=IFCBSPLINECURVEWITHKNOTS(1,(#4661,#4662),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2717=IFCBSPLINECURVEWITHKNOTS(1,(#4662,#4664),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2718=IFCBSPLINECURVEWITHKNOTS(1,(#4664,#4665),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2719=IFCBSPLINECURVEWITHKNOTS(1,(#4665,#4666),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2720=IFCBSPLINECURVEWITHKNOTS(1,(#4666,#4668),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2721=IFCBSPLINECURVEWITHKNOTS(1,(#4668,#4669),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2722=IFCBSPLINECURVEWITHKNOTS(1,(#4669,#4671),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2723=IFCBSPLINECURVEWITHKNOTS(1,(#4671,#4672),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2724=IFCBSPLINECURVEWITHKNOTS(1,(#4672,#4674),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2725=IFCBSPLINECURVEWITHKNOTS(1,(#4674,#4675),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2726=IFCBSPLINECURVEWITHKNOTS(1,(#4675,#4677),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2727=IFCBSPLINECURVEWITHKNOTS(1,(#4677,#4678),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2728=IFCBSPLINECURVEWITHKNOTS(1,(#4678,#4680),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2729=IFCBSPLINECURVEWITHKNOTS(1,(#4680,#4681),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2730=IFCBSPLINECURVEWITHKNOTS(1,(#4681,#4683),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2731=IFCBSPLINECURVEWITHKNOTS(1,(#4683,#4684),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2732=IFCBSPLINECURVEWITHKNOTS(1,(#4684,#4686),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2733=IFCBSPLINECURVEWITHKNOTS(1,(#4686,#4687),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2734=IFCBSPLINECURVEWITHKNOTS(1,(#4687,#4689),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2735=IFCBSPLINECURVEWITHKNOTS(1,(#4689,#4691),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2736=IFCBSPLINECURVEWITHKNOTS(1,(#4691,#4693),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2737=IFCBSPLINECURVEWITHKNOTS(1,(#4693,#4694),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2738=IFCBSPLINECURVEWITHKNOTS(1,(#4694,#4696),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2739=IFCBSPLINECURVEWITHKNOTS(1,(#4696,#4697),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2740=IFCBSPLINECURVEWITHKNOTS(1,(#4697,#4699),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2741=IFCBSPLINECURVEWITHKNOTS(1,(#4699,#4700),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2742=IFCBSPLINECURVEWITHKNOTS(1,(#4700,#4702),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2743=IFCBSPLINECURVEWITHKNOTS(1,(#4702,#4703),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2744=IFCBSPLINECURVEWITHKNOTS(1,(#4703,#4705),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2745=IFCBSPLINECURVEWITHKNOTS(1,(#4705,#4706),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2746=IFCBSPLINECURVEWITHKNOTS(1,(#4706,#4708),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2747=IFCBSPLINECURVEWITHKNOTS(1,(#4708,#4709),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2748=IFCBSPLINECURVEWITHKNOTS(1,(#4709,#4711),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2749=IFCBSPLINECURVEWITHKNOTS(1,(#4711,#4712),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2750=IFCBSPLINECURVEWITHKNOTS(1,(#4712,#4714),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2751=IFCBSPLINECURVEWITHKNOTS(1,(#4714,#4715),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2752=IFCBSPLINECURVEWITHKNOTS(1,(#4715,#4717),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2753=IFCBSPLINECURVEWITHKNOTS(1,(#4717,#4718),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2754=IFCBSPLINECURVEWITHKNOTS(1,(#4718,#4720),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2755=IFCBSPLINECURVEWITHKNOTS(1,(#4720,#4721),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2756=IFCBSPLINECURVEWITHKNOTS(1,(#4721,#4723),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2757=IFCBSPLINECURVEWITHKNOTS(1,(#4723,#4724),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2758=IFCBSPLINECURVEWITHKNOTS(1,(#4724,#4726),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2759=IFCBSPLINECURVEWITHKNOTS(1,(#4726,#4727),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2760=IFCBSPLINECURVEWITHKNOTS(1,(#4727,#4729),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2761=IFCBSPLINECURVEWITHKNOTS(1,(#4729,#4730),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2762=IFCBSPLINECURVEWITHKNOTS(1,(#4730,#4732),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2763=IFCBSPLINECURVEWITHKNOTS(1,(#4732,#4733),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2764=IFCBSPLINECURVEWITHKNOTS(1,(#4733,#4735),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2765=IFCBSPLINECURVEWITHKNOTS(1,(#4735,#4736),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2766=IFCBSPLINECURVEWITHKNOTS(1,(#4736,#4738),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2767=IFCBSPLINECURVEWITHKNOTS(1,(#4738,#4739),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2768=IFCBSPLINECURVEWITHKNOTS(1,(#4739,#4741),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2769=IFCBSPLINECURVEWITHKNOTS(1,(#4741,#4742),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2770=IFCBSPLINECURVEWITHKNOTS(1,(#4742,#4744),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2771=IFCBSPLINECURVEWITHKNOTS(1,(#4744,#4745),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2772=IFCBSPLINECURVEWITHKNOTS(1,(#4745,#4747),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2773=IFCBSPLINECURVEWITHKNOTS(1,(#4747,#4748),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2774=IFCBSPLINECURVEWITHKNOTS(1,(#4748,#4750),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2775=IFCBSPLINECURVEWITHKNOTS(1,(#4750,#4751),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2776=IFCBSPLINECURVEWITHKNOTS(1,(#4751,#4753),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2777=IFCBSPLINECURVEWITHKNOTS(1,(#4753,#4754),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2778=IFCBSPLINECURVEWITHKNOTS(1,(#4754,#4756),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2779=IFCBSPLINECURVEWITHKNOTS(1,(#4756,#4757),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2780=IFCBSPLINECURVEWITHKNOTS(1,(#4757,#4759),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2781=IFCBSPLINECURVEWITHKNOTS(1,(#4759,#4761),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2782=IFCBSPLINECURVEWITHKNOTS(1,(#4761,#4762),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2783=IFCBSPLINECURVEWITHKNOTS(1,(#4762,#4764),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2784=IFCBSPLINECURVEWITHKNOTS(1,(#4764,#4765),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2785=IFCBSPLINECURVEWITHKNOTS(1,(#4765,#4767),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2786=IFCBSPLINECURVEWITHKNOTS(1,(#4767,#4768),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2787=IFCBSPLINECURVEWITHKNOTS(1,(#4768,#4770),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2788=IFCBSPLINECURVEWITHKNOTS(1,(#4770,#4771),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2789=IFCBSPLINECURVEWITHKNOTS(1,(#4771,#4773),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2790=IFCBSPLINECURVEWITHKNOTS(1,(#4773,#4774),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2791=IFCBSPLINECURVEWITHKNOTS(1,(#4774,#4776),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2792=IFCBSPLINECURVEWITHKNOTS(1,(#4776,#4778),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2793=IFCBSPLINECURVEWITHKNOTS(1,(#4778,#4779),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2794=IFCBSPLINECURVEWITHKNOTS(1,(#4779,#4780),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2795=IFCBSPLINECURVEWITHKNOTS(1,(#4780,#4782),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2796=IFCBSPLINECURVEWITHKNOTS(1,(#4782,#4783),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2797=IFCBSPLINECURVEWITHKNOTS(1,(#4783,#4784),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2798=IFCBSPLINECURVEWITHKNOTS(1,(#4784,#4786),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2799=IFCBSPLINECURVEWITHKNOTS(1,(#4786,#4788),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2800=IFCBSPLINECURVEWITHKNOTS(1,(#4788,#4789),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2801=IFCBSPLINECURVEWITHKNOTS(1,(#4789,#4790),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2802=IFCBSPLINECURVEWITHKNOTS(1,(#4790,#4791),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2803=IFCBSPLINECURVEWITHKNOTS(1,(#4791,#4792),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2804=IFCBSPLINECURVEWITHKNOTS(1,(#4792,#4794),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2805=IFCBSPLINECURVEWITHKNOTS(1,(#4794,#4795),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2806=IFCBSPLINECURVEWITHKNOTS(1,(#4795,#4796),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2807=IFCBSPLINECURVEWITHKNOTS(1,(#4796,#4797),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2808=IFCBSPLINECURVEWITHKNOTS(1,(#4797,#4798),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2809=IFCBSPLINECURVEWITHKNOTS(1,(#4798,#4800),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2810=IFCBSPLINECURVEWITHKNOTS(1,(#4800,#4802),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2811=IFCBSPLINECURVEWITHKNOTS(1,(#4802,#4803),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2812=IFCBSPLINECURVEWITHKNOTS(1,(#4803,#4804),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2813=IFCBSPLINECURVEWITHKNOTS(1,(#4804,#4805),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2814=IFCBSPLINECURVEWITHKNOTS(1,(#4805,#4807),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2815=IFCBSPLINECURVEWITHKNOTS(1,(#4807,#4809),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2816=IFCBSPLINECURVEWITHKNOTS(1,(#4809,#4810),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2817=IFCBSPLINECURVEWITHKNOTS(1,(#4810,#4811),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2818=IFCBSPLINECURVEWITHKNOTS(1,(#4811,#4813),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2819=IFCBSPLINECURVEWITHKNOTS(1,(#4813,#4814),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2820=IFCBSPLINECURVEWITHKNOTS(1,(#4814,#4815),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2821=IFCBSPLINECURVEWITHKNOTS(1,(#4815,#4817),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2822=IFCBSPLINECURVEWITHKNOTS(1,(#4817,#4818),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2823=IFCBSPLINECURVEWITHKNOTS(1,(#4818,#4819),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2824=IFCBSPLINECURVEWITHKNOTS(1,(#4819,#4821),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2825=IFCBSPLINECURVEWITHKNOTS(1,(#4821,#4822),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2826=IFCBSPLINECURVEWITHKNOTS(1,(#4822,#4824),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2827=IFCBSPLINECURVEWITHKNOTS(1,(#4824,#4825),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2828=IFCBSPLINECURVEWITHKNOTS(1,(#4825,#4827),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2829=IFCBSPLINECURVEWITHKNOTS(1,(#4827,#4828),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2830=IFCBSPLINECURVEWITHKNOTS(1,(#4828,#4830),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2831=IFCBSPLINECURVEWITHKNOTS(1,(#4830,#4831),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2832=IFCBSPLINECURVEWITHKNOTS(1,(#4831,#4833),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2833=IFCBSPLINECURVEWITHKNOTS(1,(#4833,#4834),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2834=IFCBSPLINECURVEWITHKNOTS(1,(#4834,#4836),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2835=IFCBSPLINECURVEWITHKNOTS(1,(#4836,#4837),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2836=IFCBSPLINECURVEWITHKNOTS(1,(#4837,#4839),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2837=IFCBSPLINECURVEWITHKNOTS(1,(#4839,#4840),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2838=IFCBSPLINECURVEWITHKNOTS(1,(#4840,#4842),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2839=IFCBSPLINECURVEWITHKNOTS(1,(#4842,#4843),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2840=IFCBSPLINECURVEWITHKNOTS(1,(#4843,#4845),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2841=IFCBSPLINECURVEWITHKNOTS(1,(#4845,#4846),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2842=IFCBSPLINECURVEWITHKNOTS(1,(#4846,#4848),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2843=IFCBSPLINECURVEWITHKNOTS(1,(#4848,#4849),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2844=IFCBSPLINECURVEWITHKNOTS(1,(#4849,#4851),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2845=IFCBSPLINECURVEWITHKNOTS(1,(#4851,#4852),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2846=IFCBSPLINECURVEWITHKNOTS(1,(#4852,#4854),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2847=IFCBSPLINECURVEWITHKNOTS(1,(#4854,#4855),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2848=IFCBSPLINECURVEWITHKNOTS(1,(#4855,#4857),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2849=IFCBSPLINECURVEWITHKNOTS(1,(#4857,#4858),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2850=IFCBSPLINECURVEWITHKNOTS(1,(#4858,#4860),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2851=IFCBSPLINECURVEWITHKNOTS(1,(#4860,#4861),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2852=IFCBSPLINECURVEWITHKNOTS(1,(#4861,#4863),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2853=IFCBSPLINECURVEWITHKNOTS(1,(#4863,#4864),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2854=IFCBSPLINECURVEWITHKNOTS(1,(#4864,#4866),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2855=IFCBSPLINECURVEWITHKNOTS(1,(#4866,#4867),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2856=IFCBSPLINECURVEWITHKNOTS(1,(#4867,#4869),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2857=IFCBSPLINECURVEWITHKNOTS(1,(#4869,#4870),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2858=IFCBSPLINECURVEWITHKNOTS(1,(#4870,#4872),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2859=IFCBSPLINECURVEWITHKNOTS(1,(#4872,#4873),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2860=IFCBSPLINECURVEWITHKNOTS(1,(#4873,#4875),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2861=IFCBSPLINECURVEWITHKNOTS(1,(#4875,#4876),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2862=IFCBSPLINECURVEWITHKNOTS(1,(#4876,#4878),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2863=IFCBSPLINECURVEWITHKNOTS(1,(#4878,#4879),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2864=IFCBSPLINECURVEWITHKNOTS(1,(#4879,#4881),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2865=IFCBSPLINECURVEWITHKNOTS(1,(#4881,#4882),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2866=IFCBSPLINECURVEWITHKNOTS(1,(#4882,#4884),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2867=IFCBSPLINECURVEWITHKNOTS(1,(#4884,#4885),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2868=IFCBSPLINECURVEWITHKNOTS(1,(#4885,#4887),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2869=IFCBSPLINECURVEWITHKNOTS(1,(#4887,#4888),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2870=IFCBSPLINECURVEWITHKNOTS(1,(#4888,#4890),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2871=IFCBSPLINECURVEWITHKNOTS(1,(#4890,#4891),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2872=IFCBSPLINECURVEWITHKNOTS(1,(#4891,#4893),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2873=IFCBSPLINECURVEWITHKNOTS(1,(#4893,#4894),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2874=IFCBSPLINECURVEWITHKNOTS(1,(#4894,#4896),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2875=IFCBSPLINECURVEWITHKNOTS(1,(#4896,#4897),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2876=IFCBSPLINECURVEWITHKNOTS(1,(#4897,#4899),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2877=IFCBSPLINECURVEWITHKNOTS(1,(#4899,#4900),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2878=IFCBSPLINECURVEWITHKNOTS(1,(#4900,#4902),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2879=IFCBSPLINECURVEWITHKNOTS(1,(#4902,#4904),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2880=IFCBSPLINECURVEWITHKNOTS(1,(#4904,#4905),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2881=IFCBSPLINECURVEWITHKNOTS(1,(#4905,#4907),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2882=IFCBSPLINECURVEWITHKNOTS(1,(#4907,#4908),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2883=IFCBSPLINECURVEWITHKNOTS(1,(#4908,#4910),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2884=IFCBSPLINECURVEWITHKNOTS(1,(#4910,#4911),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2885=IFCBSPLINECURVEWITHKNOTS(1,(#4911,#4913),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2886=IFCBSPLINECURVEWITHKNOTS(1,(#4913,#4914),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2887=IFCBSPLINECURVEWITHKNOTS(1,(#4914,#4916),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2888=IFCBSPLINECURVEWITHKNOTS(1,(#4916,#4917),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2889=IFCBSPLINECURVEWITHKNOTS(1,(#4917,#4918),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2890=IFCBSPLINECURVEWITHKNOTS(1,(#4918,#4920),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2891=IFCBSPLINECURVEWITHKNOTS(1,(#4920,#4921),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2892=IFCBSPLINECURVEWITHKNOTS(1,(#4921,#4922),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2893=IFCBSPLINECURVEWITHKNOTS(1,(#4922,#4924),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2894=IFCBSPLINECURVEWITHKNOTS(1,(#4924,#4925),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2895=IFCBSPLINECURVEWITHKNOTS(1,(#4925,#4926),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2896=IFCBSPLINECURVEWITHKNOTS(1,(#4926,#4928),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2897=IFCBSPLINECURVEWITHKNOTS(1,(#4928,#4929),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2898=IFCBSPLINECURVEWITHKNOTS(1,(#4929,#4930),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2899=IFCBSPLINECURVEWITHKNOTS(1,(#4930,#4931),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2900=IFCBSPLINECURVEWITHKNOTS(1,(#4931,#4933),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2901=IFCBSPLINECURVEWITHKNOTS(1,(#4933,#4934),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2902=IFCBSPLINECURVEWITHKNOTS(1,(#4934,#4935),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2903=IFCBSPLINECURVEWITHKNOTS(1,(#4935,#4936),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2904=IFCBSPLINECURVEWITHKNOTS(1,(#4936,#4654),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#2905=IFCPCURVE(#4057,#3480);
#2906=IFCPCURVE(#4057,#3481);
#2907=IFCPCURVE(#4057,#3482);
#2908=IFCPCURVE(#4057,#3483);
#2909=IFCPCURVE(#4057,#3484);
#2910=IFCPCURVE(#4057,#3485);
#2911=IFCPCURVE(#4057,#3486);
#2912=IFCPCURVE(#4057,#3487);
#2913=IFCPCURVE(#4057,#3488);
#2914=IFCPCURVE(#4057,#3489);
#2915=IFCPCURVE(#4057,#3490);
#2916=IFCPCURVE(#4057,#3491);
#2917=IFCPCURVE(#4057,#3492);
#2918=IFCPCURVE(#4057,#3493);
#2919=IFCPCURVE(#4057,#3494);
#2920=IFCPCURVE(#4057,#3495);
#2921=IFCPCURVE(#4057,#3496);
#2922=IFCPCURVE(#4057,#3497);
#2923=IFCPCURVE(#4057,#3498);
#2924=IFCPCURVE(#4057,#3499);
#2925=IFCPCURVE(#4057,#3500);
#2926=IFCPCURVE(#4057,#3501);
#2927=IFCPCURVE(#4057,#3502);
#2928=IFCPCURVE(#4057,#3503);
#2929=IFCPCURVE(#4057,#3504);
#2930=IFCPCURVE(#4057,#3505);
#2931=IFCPCURVE(#4057,#3506);
#2932=IFCPCURVE(#4057,#3507);
#2933=IFCPCURVE(#4057,#3508);
#2934=IFCPCURVE(#4057,#3509);
#2935=IFCPCURVE(#4057,#3510);
#2936=IFCPCURVE(#4057,#3511);
#2937=IFCPCURVE(#4057,#3512);
#2938=IFCPCURVE(#4057,#3513);
#2939=IFCPCURVE(#4057,#3514);
#2940=IFCPCURVE(#4057,#3515);
#2941=IFCPCURVE(#4057,#3516);
#2942=IFCPCURVE(#4057,#3517);
#2943=IFCPCURVE(#4057,#3518);
#2944=IFCPCURVE(#4057,#3519);
#2945=IFCPCURVE(#4057,#3520);
#2946=IFCPCURVE(#4057,#3521);
#2947=IFCPCURVE(#4057,#3522);
#2948=IFCPCURVE(#4057,#3523);
#2949=IFCPCURVE(#4057,#3524);
#2950=IFCPCURVE(#4057,#3525);
#2951=IFCPCURVE(#4057,#3526);
#2952=IFCPCURVE(#4057,#3527);
#2953=IFCPCURVE(#4057,#3528);
#2954=IFCPCURVE(#4057,#3529);
#2955=IFCPCURVE(#4057,#3530);
#2956=IFCPCURVE(#4057,#3531);
#2957=IFCPCURVE(#4057,#3532);
#2958=IFCPCURVE(#4057,#3533);
#2959=IFCPCURVE(#4057,#3534);
#2960=IFCPCURVE(#4057,#3535);
#2961=IFCPCURVE(#4057,#3536);
#2962=IFCPCURVE(#4057,#3537);
#2963=IFCPCURVE(#4057,#3538);
#2964=IFCPCURVE(#4057,#3539);
#2965=IFCPCURVE(#4057,#3540);
#2966=IFCPCURVE(#4057,#3541);
#2967=IFCPCURVE(#4057,#3542);
#2968=IFCPCURVE(#4057,#3543);
#2969=IFCPCURVE(#4057,#3544);
#2970=IFCPCURVE(#4057,#3545);
#2971=IFCPCURVE(#4057,#3546);
#2972=IFCPCURVE(#4057,#3547);
#2973=IFCPCURVE(#4057,#3548);
#2974=IFCPCURVE(#4057,#3549);
#2975=IFCPCURVE(#4057,#3550);
#2976=IFCPCURVE(#4057,#3551);
#2977=IFCPCURVE(#4057,#3552);
#2978=IFCPCURVE(#4057,#3553);
#2979=IFCPCURVE(#4057,#3554);
#2980=IFCPCURVE(#4057,#3555);
#2981=IFCPCURVE(#4057,#3556);
#2982=IFCPCURVE(#4057,#3557);
#2983=IFCPCURVE(#4057,#3558);
#2984=IFCPCURVE(#4057,#3559);
#2985=IFCPCURVE(#4057,#3560);
#2986=IFCPCURVE(#4057,#3561);
#2987=IFCPCURVE(#4057,#3562);
#2988=IFCPCURVE(#4057,#3563);
#2989=IFCPCURVE(#4057,#3564);
#2990=IFCPCURVE(#4057,#3565);
#2991=IFCPCURVE(#4057,#3566);
#2992=IFCPCURVE(#4057,#3567);
#2993=IFCPCURVE(#4057,#3568);
#2994=IFCPCURVE(#4057,#3569);
#2995=IFCPCURVE(#4057,#3570);
#2996=IFCPCURVE(#4057,#3571);
#2997=IFCPCURVE(#4057,#3572);
#2998=IFCPCURVE(#4057,#3573);
#2999=IFCPCURVE(#4057,#3574);
#3000=IFCPCURVE(#4057,#3575);
#3001=IFCPCURVE(#4057,#3576);
#3002=IFCPCURVE(#4057,#3577);
#3003=IFCPCURVE(#4057,#3578);
#3004=IFCPCURVE(#4057,#3579);
#3005=IFCPCURVE(#4057,#3580);
#3006=IFCPCURVE(#4057,#3581);
#3007=IFCPCURVE(#4057,#3582);
#3008=IFCPCURVE(#4057,#3583);
#3009=IFCPCURVE(#4057,#3584);
#3010=IFCPCURVE(#4057,#3585);
#3011=IFCPCURVE(#4057,#3586);
#3012=IFCPCURVE(#4057,#3587);
#3013=IFCPCURVE(#4057,#3588);
#3014=IFCPCURVE(#4057,#3589);
#3015=IFCPCURVE(#4057,#3590);
#3016=IFCPCURVE(#4057,#3591);
#3017=IFCPCURVE(#4057,#3592);
#3018=IFCPCURVE(#4057,#3593);
#3019=IFCPCURVE(#4057,#3594);
#3020=IFCPCURVE(#4057,#3595);
#3021=IFCPCURVE(#4057,#3596);
#3022=IFCPCURVE(#4057,#3597);
#3023=IFCPCURVE(#4057,#3598);
#3024=IFCPCURVE(#4057,#3599);
#3025=IFCPCURVE(#4057,#3600);
#3026=IFCPCURVE(#4057,#3601);
#3027=IFCPCURVE(#4057,#3602);
#3028=IFCPCURVE(#4057,#3603);
#3029=IFCPCURVE(#4057,#3604);
#3030=IFCPCURVE(#4057,#3605);
#3031=IFCPCURVE(#4057,#3606);
#3032=IFCPCURVE(#4057,#3607);
#3033=IFCPCURVE(#4057,#3608);
#3034=IFCPCURVE(#4057,#3609);
#3035=IFCPCURVE(#4057,#3610);
#3036=IFCPCURVE(#4057,#3611);
#3037=IFCPCURVE(#4057,#3612);
#3038=IFCPCURVE(#4057,#3613);
#3039=IFCPCURVE(#4057,#3614);
#3040=IFCPCURVE(#4057,#3615);
#3041=IFCPCURVE(#4057,#3616);
#3042=IFCPCURVE(#4057,#3617);
#3043=IFCPCURVE(#4057,#3618);
#3044=IFCPCURVE(#4057,#3619);
#3045=IFCPCURVE(#4057,#3620);
#3046=IFCPCURVE(#4057,#3621);
#3047=IFCPCURVE(#4057,#3622);
#3048=IFCPCURVE(#4057,#3623);
#3049=IFCPCURVE(#4057,#3624);
#3050=IFCPCURVE(#4057,#3625);
#3051=IFCPCURVE(#4057,#3626);
#3052=IFCPCURVE(#4057,#3627);
#3053=IFCPCURVE(#4057,#3628);
#3054=IFCPCURVE(#4057,#3629);
#3055=IFCPCURVE(#4057,#3630);
#3056=IFCPCURVE(#4057,#3631);
#3057=IFCPCURVE(#4057,#3632);
#3058=IFCPCURVE(#4057,#3633);
#3059=IFCPCURVE(#4057,#3634);
#3060=IFCPCURVE(#4057,#3635);
#3061=IFCPCURVE(#4057,#3636);
#3062=IFCPCURVE(#4057,#3637);
#3063=IFCPCURVE(#4057,#3638);
#3064=IFCPCURVE(#4057,#3639);
#3065=IFCPCURVE(#4057,#3640);
#3066=IFCPCURVE(#4057,#3641);
#3067=IFCPCURVE(#4057,#3642);
#3068=IFCPCURVE(#4057,#3643);
#3069=IFCPCURVE(#4057,#3644);
#3070=IFCPCURVE(#4057,#3645);
#3071=IFCPCURVE(#4057,#3646);
#3072=IFCPCURVE(#4057,#3647);
#3073=IFCPCURVE(#4057,#3648);
#3074=IFCPCURVE(#4057,#3649);
#3075=IFCPCURVE(#4057,#3650);
#3076=IFCPCURVE(#4057,#3651);
#3077=IFCPCURVE(#4057,#3652);
#3078=IFCPCURVE(#4057,#3653);
#3079=IFCPCURVE(#4057,#3654);
#3080=IFCPCURVE(#4057,#3655);
#3081=IFCPCURVE(#4057,#3656);
#3082=IFCPCURVE(#4057,#3657);
#3083=IFCPCURVE(#4057,#3658);
#3084=IFCPCURVE(#4057,#3659);
#3085=IFCPCURVE(#4057,#3660);
#3086=IFCPCURVE(#4057,#3661);
#3087=IFCPCURVE(#4057,#3662);
#3088=IFCPCURVE(#4057,#3663);
#3089=IFCPCURVE(#4057,#3664);
#3090=IFCPCURVE(#4057,#3665);
#3091=IFCPCURVE(#4057,#3666);
#3092=IFCPCURVE(#4057,#3667);
#3093=IFCPCURVE(#4057,#3668);
#3094=IFCPCURVE(#4057,#3669);
#3095=IFCPCURVE(#4057,#3670);
#3096=IFCPCURVE(#4057,#3671);
#3097=IFCPCURVE(#4057,#3672);
#3098=IFCPCURVE(#4057,#3673);
#3099=IFCPCURVE(#4057,#3674);
#3100=IFCPCURVE(#4057,#3675);
#3101=IFCPCURVE(#4057,#3676);
#3102=IFCPCURVE(#4057,#3677);
#3103=IFCPCURVE(#4057,#3678);
#3104=IFCPCURVE(#4057,#3679);
#3105=IFCPCURVE(#4057,#3680);
#3106=IFCPCURVE(#4057,#3681);
#3107=IFCPCURVE(#4057,#3682);
#3108=IFCPCURVE(#4057,#3683);
#3109=IFCPCURVE(#4057,#3684);
#3110=IFCPCURVE(#4057,#3685);
#3111=IFCPCURVE(#4057,#3686);
#3112=IFCPCURVE(#4057,#3687);
#3113=IFCPCURVE(#4057,#3688);
#3114=IFCPCURVE(#4057,#3689);
#3115=IFCPCURVE(#4057,#3690);
#3116=IFCPCURVE(#4057,#3691);
#3117=IFCPCURVE(#4057,#3692);
#3118=IFCPCURVE(#4057,#3693);
#3119=IFCPCURVE(#4057,#3694);
#3120=IFCPCURVE(#4057,#3695);
#3121=IFCPCURVE(#4057,#3696);
#3122=IFCPCURVE(#4057,#3697);
#3123=IFCPCURVE(#4057,#3698);
#3124=IFCPCURVE(#4057,#3699);
#3125=IFCPCURVE(#4057,#3700);
#3126=IFCPCURVE(#4057,#3701);
#3127=IFCPCURVE(#4057,#3702);
#3128=IFCPCURVE(#4057,#3703);
#3129=IFCPCURVE(#4057,#3704);
#3130=IFCPCURVE(#4057,#3705);
#3131=IFCPCURVE(#4057,#3706);
#3132=IFCPCURVE(#4057,#3707);
#3133=IFCPCURVE(#4057,#3708);
#3134=IFCPCURVE(#4057,#3709);
#3135=IFCPCURVE(#4057,#3710);
#3136=IFCPCURVE(#4057,#3711);
#3137=IFCPCURVE(#4057,#3712);
#3138=IFCPCURVE(#4057,#3713);
#3139=IFCPCURVE(#4057,#3714);
#3140=IFCPCURVE(#4057,#3715);
#3141=IFCPCURVE(#4057,#3716);
#3142=IFCPCURVE(#4057,#3717);
#3143=IFCPCURVE(#4057,#3718);
#3144=IFCPCURVE(#4057,#3719);
#3145=IFCPCURVE(#4057,#3720);
#3146=IFCPCURVE(#4057,#3721);
#3147=IFCPCURVE(#4057,#3722);
#3148=IFCPCURVE(#4057,#3723);
#3149=IFCPCURVE(#4057,#3724);
#3150=IFCPCURVE(#4057,#3725);
#3151=IFCPCURVE(#4057,#3726);
#3152=IFCPCURVE(#4057,#3727);
#3153=IFCPCURVE(#4057,#3728);
#3154=IFCPCURVE(#4057,#3729);
#3155=IFCPCURVE(#4057,#3730);
#3156=IFCPCURVE(#4057,#3731);
#3157=IFCPCURVE(#4057,#3732);
#3158=IFCPCURVE(#4057,#3733);
#3159=IFCPCURVE(#4057,#3734);
#3160=IFCPCURVE(#4057,#3735);
#3161=IFCPCURVE(#4057,#3736);
#3162=IFCPCURVE(#4057,#3737);
#3163=IFCPCURVE(#4057,#3738);
#3164=IFCPCURVE(#4057,#3739);
#3165=IFCPCURVE(#4057,#3740);
#3166=IFCPCURVE(#4057,#3741);
#3167=IFCPCURVE(#4057,#3742);
#3168=IFCPCURVE(#4057,#3743);
#3169=IFCPCURVE(#4057,#3744);
#3170=IFCPCURVE(#4057,#3745);
#3171=IFCPCURVE(#4057,#3746);
#3172=IFCPCURVE(#4057,#3747);
#3173=IFCPCURVE(#4057,#3748);
#3174=IFCPCURVE(#4057,#3749);
#3175=IFCPCURVE(#4057,#3750);
#3176=IFCPCURVE(#4057,#3751);
#3177=IFCPCURVE(#4057,#3752);
#3178=IFCPCURVE(#4057,#3753);
#3179=IFCPCURVE(#4057,#3754);
#3180=IFCPCURVE(#4057,#3755);
#3181=IFCPCURVE(#4057,#3756);
#3182=IFCPCURVE(#4057,#3757);
#3183=IFCPCURVE(#4057,#3758);
#3184=IFCPCURVE(#4057,#3759);
#3185=IFCPCURVE(#4057,#3760);
#3186=IFCPCURVE(#4057,#3761);
#3187=IFCPCURVE(#4057,#3762);
#3188=IFCPCURVE(#4057,#3763);
#3189=IFCPCURVE(#4057,#3764);
#3190=IFCPCURVE(#4057,#3765);
#3191=IFCPCURVE(#4057,#3766);
#3192=IFCPCURVE(#4057,#3767);
#3193=IFCPCURVE(#4057,#3768);
#3194=IFCPCURVE(#4057,#3769);
#3195=IFCPCURVE(#4057,#3770);
#3196=IFCPCURVE(#4057,#3771);
#3197=IFCPCURVE(#4057,#3772);
#3198=IFCPCURVE(#4057,#3773);
#3199=IFCPCURVE(#4057,#3774);
#3200=IFCPCURVE(#4057,#3775);
#3201=IFCPCURVE(#4057,#3776);
#3202=IFCPCURVE(#4057,#3777);
#3203=IFCPCURVE(#4057,#3778);
#3204=IFCPCURVE(#4057,#3779);
#3205=IFCPCURVE(#4057,#3780);
#3206=IFCPCURVE(#4057,#3781);
#3207=IFCPCURVE(#4057,#3782);
#3208=IFCPCURVE(#4057,#3783);
#3209=IFCPCURVE(#4057,#3784);
#3210=IFCPCURVE(#4057,#3785);
#3211=IFCPCURVE(#4057,#3786);
#3212=IFCPCURVE(#4057,#3787);
#3213=IFCPCURVE(#4057,#3788);
#3214=IFCPCURVE(#4057,#3789);
#3215=IFCPCURVE(#4057,#3790);
#3216=IFCPCURVE(#4057,#3791);
#3217=IFCPCURVE(#4057,#3792);
#3218=IFCPCURVE(#4057,#3793);
#3219=IFCPCURVE(#4057,#3794);
#3220=IFCPCURVE(#4057,#3795);
#3221=IFCPCURVE(#4057,#3796);
#3222=IFCPCURVE(#4057,#3797);
#3223=IFCPCURVE(#4057,#3798);
#3224=IFCPCURVE(#4057,#3799);
#3225=IFCPCURVE(#4057,#3800);
#3226=IFCPCURVE(#4057,#3801);
#3227=IFCPCURVE(#4057,#3802);
#3228=IFCPCURVE(#4057,#3803);
#3229=IFCPCURVE(#4057,#3804);
#3230=IFCPCURVE(#4057,#3805);
#3231=IFCPCURVE(#4057,#3806);
#3232=IFCPCURVE(#4057,#3807);
#3233=IFCPCURVE(#4057,#3808);
#3234=IFCPCURVE(#4057,#3809);
#3235=IFCPCURVE(#4057,#3810);
#3236=IFCPCURVE(#4057,#3811);
#3237=IFCPCURVE(#4057,#3812);
#3238=IFCPCURVE(#4057,#3813);
#3239=IFCPCURVE(#4057,#3814);
#3240=IFCPCURVE(#4057,#3815);
#3241=IFCPCURVE(#4057,#3816);
#3242=IFCPCURVE(#4057,#3817);
#3243=IFCPCURVE(#4057,#3818);
#3244=IFCPCURVE(#4057,#3819);
#3245=IFCPCURVE(#4057,#3820);
#3246=IFCPCURVE(#4057,#3821);
#3247=IFCPCURVE(#4057,#3822);
#3248=IFCPCURVE(#4057,#3823);
#3249=IFCPCURVE(#4057,#3824);
#3250=IFCPCURVE(#4057,#3825);
#3251=IFCPCURVE(#4057,#3826);
#3252=IFCPCURVE(#4057,#3827);
#3253=IFCPCURVE(#4057,#3828);
#3254=IFCPCURVE(#4057,#3829);
#3255=IFCPCURVE(#4057,#3830);
#3256=IFCPCURVE(#4057,#3831);
#3257=IFCPCURVE(#4057,#3832);
#3258=IFCPCURVE(#4057,#3833);
#3259=IFCPCURVE(#4057,#3834);
#3260=IFCPCURVE(#4057,#3835);
#3261=IFCPCURVE(#4057,#3836);
#3262=IFCPCURVE(#4057,#3837);
#3263=IFCPCURVE(#4057,#3838);
#3264=IFCPCURVE(#4057,#3839);
#3265=IFCPCURVE(#4057,#3840);
#3266=IFCPCURVE(#4057,#3841);
#3267=IFCPCURVE(#4057,#3842);
#3268=IFCPCURVE(#4057,#3843);
#3269=IFCPCURVE(#4057,#3844);
#3270=IFCPCURVE(#4057,#3845);
#3271=IFCPCURVE(#4057,#3846);
#3272=IFCPCURVE(#4057,#3847);
#3273=IFCPCURVE(#4057,#3848);
#3274=IFCPCURVE(#4057,#3849);
#3275=IFCPCURVE(#4057,#3850);
#3276=IFCPCURVE(#4057,#3851);
#3277=IFCPCURVE(#4057,#3852);
#3278=IFCPCURVE(#4057,#3853);
#3279=IFCPCURVE(#4057,#3854);
#3280=IFCPCURVE(#4057,#3855);
#3281=IFCPCURVE(#4057,#3856);
#3282=IFCPCURVE(#4057,#3857);
#3283=IFCPCURVE(#4057,#3858);
#3284=IFCPCURVE(#4057,#3859);
#3285=IFCPCURVE(#4057,#3860);
#3286=IFCPCURVE(#4057,#3861);
#3287=IFCPCURVE(#4057,#3862);
#3288=IFCPCURVE(#4057,#3863);
#3289=IFCPCURVE(#4057,#3864);
#3290=IFCPCURVE(#4057,#3865);
#3291=IFCPCURVE(#4057,#3866);
#3292=IFCPCURVE(#4057,#3867);
#3293=IFCPCURVE(#4057,#3868);
#3294=IFCPCURVE(#4057,#3869);
#3295=IFCPCURVE(#4057,#3870);
#3296=IFCPCURVE(#4057,#3871);
#3297=IFCPCURVE(#4057,#3872);
#3298=IFCPCURVE(#4057,#3873);
#3299=IFCPCURVE(#4057,#3874);
#3300=IFCPCURVE(#4057,#3875);
#3301=IFCPCURVE(#4057,#3876);
#3302=IFCPCURVE(#4057,#3877);
#3303=IFCPCURVE(#4057,#3878);
#3304=IFCPCURVE(#4057,#3879);
#3305=IFCPCURVE(#4057,#3880);
#3306=IFCPCURVE(#4057,#3881);
#3307=IFCPCURVE(#4057,#3882);
#3308=IFCPCURVE(#4057,#3883);
#3309=IFCPCURVE(#4057,#3884);
#3310=IFCPCURVE(#4057,#3885);
#3311=IFCPCURVE(#4057,#3886);
#3312=IFCPCURVE(#4057,#3887);
#3313=IFCPCURVE(#4057,#3888);
#3314=IFCPCURVE(#4057,#3889);
#3315=IFCPCURVE(#4057,#3890);
#3316=IFCPCURVE(#4057,#3891);
#3317=IFCPCURVE(#4057,#3892);
#3318=IFCPCURVE(#4057,#3893);
#3319=IFCPCURVE(#4057,#3894);
#3320=IFCPCURVE(#4057,#3895);
#3321=IFCPCURVE(#4057,#3896);
#3322=IFCPCURVE(#4057,#3897);
#3323=IFCPCURVE(#4057,#3898);
#3324=IFCPCURVE(#4057,#3899);
#3325=IFCPCURVE(#4057,#3900);
#3326=IFCPCURVE(#4057,#3901);
#3327=IFCPCURVE(#4057,#3902);
#3328=IFCPCURVE(#4057,#3903);
#3329=IFCPCURVE(#4057,#3904);
#3330=IFCPCURVE(#4057,#3905);
#3331=IFCPCURVE(#4057,#3906);
#3332=IFCPCURVE(#4057,#3907);
#3333=IFCPCURVE(#4057,#3908);
#3334=IFCPCURVE(#4057,#3909);
#3335=IFCPCURVE(#4057,#3910);
#3336=IFCPCURVE(#4057,#3911);
#3337=IFCPCURVE(#4057,#3912);
#3338=IFCPCURVE(#4057,#3913);
#3339=IFCPCURVE(#4057,#3914);
#3340=IFCPCURVE(#4057,#3915);
#3341=IFCPCURVE(#4057,#3916);
#3342=IFCPCURVE(#4057,#3917);
#3343=IFCPCURVE(#4057,#3918);
#3344=IFCPCURVE(#4057,#3919);
#3345=IFCPCURVE(#4057,#3920);
#3346=IFCPCURVE(#4057,#3921);
#3347=IFCPCURVE(#4057,#3922);
#3348=IFCPCURVE(#4057,#3923);
#3349=IFCPCURVE(#4057,#3924);
#3350=IFCPCURVE(#4057,#3925);
#3351=IFCPCURVE(#4057,#3926);
#3352=IFCPCURVE(#4057,#3927);
#3353=IFCPCURVE(#4057,#3928);
#3354=IFCPCURVE(#4057,#3929);
#3355=IFCPCURVE(#4057,#3930);
#3356=IFCPCURVE(#4057,#3931);
#3357=IFCPCURVE(#4057,#3932);
#3358=IFCPCURVE(#4057,#3933);
#3359=IFCPCURVE(#4057,#3934);
#3360=IFCPCURVE(#4057,#3935);
#3361=IFCPCURVE(#4057,#3936);
#3362=IFCPCURVE(#4057,#3937);
#3363=IFCPCURVE(#4057,#3938);
#3364=IFCPCURVE(#4057,#3939);
#3365=IFCPCURVE(#4057,#3940);
#3366=IFCPCURVE(#4057,#3941);
#3367=IFCPCURVE(#4057,#3942);
#3368=IFCPCURVE(#4057,#3943);
#3369=IFCPCURVE(#4057,#3944);
#3370=IFCPCURVE(#4057,#3945);
#3371=IFCPCURVE(#4057,#3946);
#3372=IFCPCURVE(#4057,#3947);
#3373=IFCPCURVE(#4057,#3948);
#3374=IFCPCURVE(#4057,#3949);
#3375=IFCPCURVE(#4057,#3950);
#3376=IFCPCURVE(#4057,#3951);
#3377=IFCPCURVE(#4057,#3952);
#3378=IFCPCURVE(#4057,#3953);
#3379=IFCPCURVE(#4057,#3954);
#3380=IFCPCURVE(#4057,#3955);
#3381=IFCPCURVE(#4057,#3956);
#3382=IFCPCURVE(#4057,#3957);
#3383=IFCPCURVE(#4057,#3958);
#3384=IFCPCURVE(#4057,#3959);
#3385=IFCPCURVE(#4057,#3960);
#3386=IFCPCURVE(#4057,#3961);
#3387=IFCPCURVE(#4057,#3962);
#3388=IFCPCURVE(#4057,#3963);
#3389=IFCPCURVE(#4057,#3964);
#3390=IFCPCURVE(#4057,#3965);
#3391=IFCPCURVE(#4057,#3966);
#3392=IFCPCURVE(#4057,#3967);
#3393=IFCPCURVE(#4057,#3968);
#3394=IFCPCURVE(#4057,#3969);
#3395=IFCPCURVE(#4057,#3970);
#3396=IFCPCURVE(#4057,#3971);
#3397=IFCPCURVE(#4057,#3972);
#3398=IFCPCURVE(#4057,#3973);
#3399=IFCPCURVE(#4057,#3974);
#3400=IFCPCURVE(#4057,#3975);
#3401=IFCPCURVE(#4057,#3976);
#3402=IFCPCURVE(#4057,#3977);
#3403=IFCPCURVE(#4057,#3978);
#3404=IFCPCURVE(#4057,#3979);
#3405=IFCPCURVE(#4057,#3980);
#3406=IFCPCURVE(#4057,#3981);
#3407=IFCPCURVE(#4057,#3982);
#3408=IFCPCURVE(#4057,#3983);
#3409=IFCPCURVE(#4057,#3984);
#3410=IFCPCURVE(#4057,#3985);
#3411=IFCPCURVE(#4057,#3986);
#3412=IFCPCURVE(#4057,#3987);
#3413=IFCPCURVE(#4057,#3988);
#3414=IFCPCURVE(#4057,#3989);
#3415=IFCPCURVE(#4057,#3990);
#3416=IFCPCURVE(#4057,#3991);
#3417=IFCPCURVE(#4057,#3992);
#3418=IFCPCURVE(#4057,#3993);
#3419=IFCPCURVE(#4057,#3994);
#3420=IFCPCURVE(#4057,#3995);
#3421=IFCPCURVE(#4057,#3996);
#3422=IFCPCURVE(#4057,#3997);
#3423=IFCPCURVE(#4057,#3998);
#3424=IFCPCURVE(#4057,#3999);
#3425=IFCPCURVE(#4057,#4000);
#3426=IFCPCURVE(#4057,#4001);
#3427=IFCPCURVE(#4057,#4002);
#3428=IFCPCURVE(#4057,#4003);
#3429=IFCPCURVE(#4057,#4004);
#3430=IFCPCURVE(#4057,#4005);
#3431=IFCPCURVE(#4057,#4006);
#3432=IFCPCURVE(#4057,#4007);
#3433=IFCPCURVE(#4057,#4008);
#3434=IFCPCURVE(#4057,#4009);
#3435=IFCPCURVE(#4057,#4010);
#3436=IFCPCURVE(#4057,#4011);
#3437=IFCPCURVE(#4057,#4012);
#3438=IFCPCURVE(#4057,#4013);
#3439=IFCPCURVE(#4057,#4014);
#3440=IFCPCURVE(#4057,#4015);
#3441=IFCPCURVE(#4057,#4016);
#3442=IFCPCURVE(#4057,#4017);
#3443=IFCPCURVE(#4057,#4018);
#3444=IFCPCURVE(#4057,#4019);
#3445=IFCPCURVE(#4057,#4020);
#3446=IFCPCURVE(#4057,#4021);
#3447=IFCPCURVE(#4057,#4022);
#3448=IFCPCURVE(#4057,#4023);
#3449=IFCPCURVE(#4057,#4024);
#3450=IFCPCURVE(#4057,#4025);
#3451=IFCPCURVE(#4057,#4026);
#3452=IFCPCURVE(#4057,#4027);
#3453=IFCPCURVE(#4057,#4028);
#3454=IFCPCURVE(#4057,#4029);
#3455=IFCPCURVE(#4057,#4030);
#3456=IFCPCURVE(#4057,#4031);
#3457=IFCPCURVE(#4057,#4032);
#3458=IFCPCURVE(#4057,#4033);
#3459=IFCPCURVE(#4057,#4034);
#3460=IFCPCURVE(#4057,#4035);
#3461=IFCPCURVE(#4057,#4036);
#3462=IFCPCURVE(#4057,#4037);
#3463=IFCPCURVE(#4057,#4038);
#3464=IFCPCURVE(#4057,#4039);
#3465=IFCPCURVE(#4057,#4040);
#3466=IFCPCURVE(#4057,#4041);
#3467=IFCPCURVE(#4057,#4042);
#3468=IFCPCURVE(#4057,#4043);
#3469=IFCPCURVE(#4057,#4044);
#3470=IFCPCURVE(#4057,#4045);
#3471=IFCPCURVE(#4057,#4046);
#3472=IFCPCURVE(#4057,#4047);
#3473=IFCPCURVE(#4057,#4048);
#3474=IFCPCURVE(#4057,#4049);
#3475=IFCPCURVE(#4057,#4050);
#3476=IFCPCURVE(#4057,#4051);
#3477=IFCPCURVE(#4057,#4052);
#3478=IFCPCURVE(#4057,#4053);
#3479=IFCPCURVE(#4057,#4054);
#3480=IFCPOLYLINE((#4083,#4083));
#3481=IFCPOLYLINE((#4083,#4085));
#3482=IFCPOLYLINE((#4085,#4085));
#3483=IFCPOLYLINE((#4085,#4088));
#3484=IFCPOLYLINE((#4088,#4088));
#3485=IFCPOLYLINE((#4088,#4091));
#3486=IFCPOLYLINE((#4091,#4091));
#3487=IFCPOLYLINE((#4091,#4094));
#3488=IFCPOLYLINE((#4094,#4094));
#3489=IFCPOLYLINE((#4094,#4097));
#3490=IFCPOLYLINE((#4097,#4097));
#3491=IFCPOLYLINE((#4097,#4100));
#3492=IFCPOLYLINE((#4100,#4100));
#3493=IFCPOLYLINE((#4100,#4103));
#3494=IFCPOLYLINE((#4103,#4103));
#3495=IFCPOLYLINE((#4103,#4106));
#3496=IFCPOLYLINE((#4106,#4106));
#3497=IFCPOLYLINE((#4106,#4109));
#3498=IFCPOLYLINE((#4109,#4109));
#3499=IFCPOLYLINE((#4109,#4112));
#3500=IFCPOLYLINE((#4112,#4112));
#3501=IFCPOLYLINE((#4112,#4115));
#3502=IFCPOLYLINE((#4115,#4115));
#3503=IFCPOLYLINE((#4115,#4118));
#3504=IFCPOLYLINE((#4118,#4118));
#3505=IFCPOLYLINE((#4118,#4121));
#3506=IFCPOLYLINE((#4121,#4121));
#3507=IFCPOLYLINE((#4121,#4124));
#3508=IFCPOLYLINE((#4124,#4124));
#3509=IFCPOLYLINE((#4124,#4127));
#3510=IFCPOLYLINE((#4127,#4127));
#3511=IFCPOLYLINE((#4127,#4130));
#3512=IFCPOLYLINE((#4130,#4130));
#3513=IFCPOLYLINE((#4130,#4133));
#3514=IFCPOLYLINE((#4133,#4133));
#3515=IFCPOLYLINE((#4133,#4136));
#3516=IFCPOLYLINE((#4136,#4136));
#3517=IFCPOLYLINE((#4136,#4139));
#3518=IFCPOLYLINE((#4139,#4139));
#3519=IFCPOLYLINE((#4139,#4142));
#3520=IFCPOLYLINE((#4142,#4142));
#3521=IFCPOLYLINE((#4142,#4145));
#3522=IFCPOLYLINE((#4145,#4145));
#3523=IFCPOLYLINE((#4145,#4148));
#3524=IFCPOLYLINE((#4148,#4148));
#3525=IFCPOLYLINE((#4148,#4151));
#3526=IFCPOLYLINE((#4151,#4151));
#3527=IFCPOLYLINE((#4151,#4154));
#3528=IFCPOLYLINE((#4154,#4154));
#3529=IFCPOLYLINE((#4154,#4157));
#3530=IFCPOLYLINE((#4157,#4157));
#3531=IFCPOLYLINE((#4157,#4160));
#3532=IFCPOLYLINE((#4160,#4160));
#3533=IFCPOLYLINE((#4160,#4163));
#3534=IFCPOLYLINE((#4163,#4163));
#3535=IFCPOLYLINE((#4163,#4166));
#3536=IFCPOLYLINE((#4166,#4166));
#3537=IFCPOLYLINE((#4166,#4169));
#3538=IFCPOLYLINE((#4169,#4169));
#3539=IFCPOLYLINE((#4169,#4172));
#3540=IFCPOLYLINE((#4172,#4172));
#3541=IFCPOLYLINE((#4172,#4175));
#3542=IFCPOLYLINE((#4175,#4175));
#3543=IFCPOLYLINE((#4175,#4178));
#3544=IFCPOLYLINE((#4178,#4178));
#3545=IFCPOLYLINE((#4178,#4181));
#3546=IFCPOLYLINE((#4181,#4181));
#3547=IFCPOLYLINE((#4181,#4184));
#3548=IFCPOLYLINE((#4184,#4184));
#3549=IFCPOLYLINE((#4184,#4187));
#3550=IFCPOLYLINE((#4187,#4187));
#3551=IFCPOLYLINE((#4187,#4190));
#3552=IFCPOLYLINE((#4190,#4190));
#3553=IFCPOLYLINE((#4190,#4193));
#3554=IFCPOLYLINE((#4193,#4193));
#3555=IFCPOLYLINE((#4193,#4196));
#3556=IFCPOLYLINE((#4196,#4196));
#3557=IFCPOLYLINE((#4196,#4199));
#3558=IFCPOLYLINE((#4199,#4199));
#3559=IFCPOLYLINE((#4199,#4202));
#3560=IFCPOLYLINE((#4202,#4202));
#3561=IFCPOLYLINE((#4202,#4205));
#3562=IFCPOLYLINE((#4205,#4205));
#3563=IFCPOLYLINE((#4205,#4208));
#3564=IFCPOLYLINE((#4208,#4208));
#3565=IFCPOLYLINE((#4208,#4211));
#3566=IFCPOLYLINE((#4211,#4211));
#3567=IFCPOLYLINE((#4211,#4214));
#3568=IFCPOLYLINE((#4214,#4214));
#3569=IFCPOLYLINE((#4214,#4217));
#3570=IFCPOLYLINE((#4217,#4217));
#3571=IFCPOLYLINE((#4217,#4220));
#3572=IFCPOLYLINE((#4220,#4220));
#3573=IFCPOLYLINE((#4220,#4223));
#3574=IFCPOLYLINE((#4223,#4223));
#3575=IFCPOLYLINE((#4223,#4226));
#3576=IFCPOLYLINE((#4226,#4226));
#3577=IFCPOLYLINE((#4226,#4229));
#3578=IFCPOLYLINE((#4229,#4229));
#3579=IFCPOLYLINE((#4229,#4232));
#3580=IFCPOLYLINE((#4232,#4232));
#3581=IFCPOLYLINE((#4232,#4235));
#3582=IFCPOLYLINE((#4235,#4235));
#3583=IFCPOLYLINE((#4235,#4238));
#3584=IFCPOLYLINE((#4238,#4238));
#3585=IFCPOLYLINE((#4238,#4241));
#3586=IFCPOLYLINE((#4241,#4241));
#3587=IFCPOLYLINE((#4241,#4244));
#3588=IFCPOLYLINE((#4244,#4244));
#3589=IFCPOLYLINE((#4244,#4247));
#3590=IFCPOLYLINE((#4247,#4247));
#3591=IFCPOLYLINE((#4247,#4250));
#3592=IFCPOLYLINE((#4250,#4250));
#3593=IFCPOLYLINE((#4250,#4253));
#3594=IFCPOLYLINE((#4253,#4253));
#3595=IFCPOLYLINE((#4253,#4256));
#3596=IFCPOLYLINE((#4256,#4256));
#3597=IFCPOLYLINE((#4256,#4259));
#3598=IFCPOLYLINE((#4259,#4259));
#3599=IFCPOLYLINE((#4259,#4262));
#3600=IFCPOLYLINE((#4262,#4262));
#3601=IFCPOLYLINE((#4262,#4265));
#3602=IFCPOLYLINE((#4265,#4265));
#3603=IFCPOLYLINE((#4265,#4268));
#3604=IFCPOLYLINE((#4268,#4268));
#3605=IFCPOLYLINE((#4268,#4271));
#3606=IFCPOLYLINE((#4271,#4271));
#3607=IFCPOLYLINE((#4271,#4274));
#3608=IFCPOLYLINE((#4274,#4274));
#3609=IFCPOLYLINE((#4274,#4277));
#3610=IFCPOLYLINE((#4277,#4277));
#3611=IFCPOLYLINE((#4277,#4280));
#3612=IFCPOLYLINE((#4280,#4280));
#3613=IFCPOLYLINE((#4280,#4283));
#3614=IFCPOLYLINE((#4283,#4283));
#3615=IFCPOLYLINE((#4283,#4286));
#3616=IFCPOLYLINE((#4286,#4286));
#3617=IFCPOLYLINE((#4286,#4289));
#3618=IFCPOLYLINE((#4289,#4289));
#3619=IFCPOLYLINE((#4289,#4292));
#3620=IFCPOLYLINE((#4292,#4292));
#3621=IFCPOLYLINE((#4292,#4295));
#3622=IFCPOLYLINE((#4295,#4295));
#3623=IFCPOLYLINE((#4295,#4298));
#3624=IFCPOLYLINE((#4298,#4298));
#3625=IFCPOLYLINE((#4298,#4301));
#3626=IFCPOLYLINE((#4301,#4301));
#3627=IFCPOLYLINE((#4301,#4304));
#3628=IFCPOLYLINE((#4304,#4304));
#3629=IFCPOLYLINE((#4304,#4307));
#3630=IFCPOLYLINE((#4307,#4307));
#3631=IFCPOLYLINE((#4307,#4310));
#3632=IFCPOLYLINE((#4310,#4310));
#3633=IFCPOLYLINE((#4310,#4313));
#3634=IFCPOLYLINE((#4313,#4313));
#3635=IFCPOLYLINE((#4313,#4316));
#3636=IFCPOLYLINE((#4316,#4316));
#3637=IFCPOLYLINE((#4316,#4319));
#3638=IFCPOLYLINE((#4319,#4319));
#3639=IFCPOLYLINE((#4319,#4322));
#3640=IFCPOLYLINE((#4322,#4322));
#3641=IFCPOLYLINE((#4322,#4325));
#3642=IFCPOLYLINE((#4325,#4325));
#3643=IFCPOLYLINE((#4325,#4328));
#3644=IFCPOLYLINE((#4328,#4328));
#3645=IFCPOLYLINE((#4328,#4331));
#3646=IFCPOLYLINE((#4331,#4331));
#3647=IFCPOLYLINE((#4331,#4334));
#3648=IFCPOLYLINE((#4334,#4334));
#3649=IFCPOLYLINE((#4334,#4337));
#3650=IFCPOLYLINE((#4337,#4337));
#3651=IFCPOLYLINE((#4337,#4340));
#3652=IFCPOLYLINE((#4340,#4340));
#3653=IFCPOLYLINE((#4340,#4343));
#3654=IFCPOLYLINE((#4343,#4343));
#3655=IFCPOLYLINE((#4343,#4346));
#3656=IFCPOLYLINE((#4346,#4346));
#3657=IFCPOLYLINE((#4346,#4349));
#3658=IFCPOLYLINE((#4349,#4349));
#3659=IFCPOLYLINE((#4349,#4352));
#3660=IFCPOLYLINE((#4352,#4352));
#3661=IFCPOLYLINE((#4352,#4355));
#3662=IFCPOLYLINE((#4355,#4355));
#3663=IFCPOLYLINE((#4355,#4358));
#3664=IFCPOLYLINE((#4358,#4358));
#3665=IFCPOLYLINE((#4358,#4361));
#3666=IFCPOLYLINE((#4361,#4362));
#3667=IFCPOLYLINE((#4362,#4364));
#3668=IFCPOLYLINE((#4364,#4366));
#3669=IFCPOLYLINE((#4366,#4368));
#3670=IFCPOLYLINE((#4368,#4368));
#3671=IFCPOLYLINE((#4368,#4370));
#3672=IFCPOLYLINE((#4370,#4370));
#3673=IFCPOLYLINE((#4370,#4373));
#3674=IFCPOLYLINE((#4373,#4373));
#3675=IFCPOLYLINE((#4373,#4376));
#3676=IFCPOLYLINE((#4376,#4376));
#3677=IFCPOLYLINE((#4376,#4379));
#3678=IFCPOLYLINE((#4379,#4379));
#3679=IFCPOLYLINE((#4379,#4382));
#3680=IFCPOLYLINE((#4382,#4382));
#3681=IFCPOLYLINE((#4382,#4385));
#3682=IFCPOLYLINE((#4385,#4385));
#3683=IFCPOLYLINE((#4385,#4388));
#3684=IFCPOLYLINE((#4388,#4388));
#3685=IFCPOLYLINE((#4388,#4391));
#3686=IFCPOLYLINE((#4391,#4391));
#3687=IFCPOLYLINE((#4391,#4394));
#3688=IFCPOLYLINE((#4394,#4394));
#3689=IFCPOLYLINE((#4394,#4397));
#3690=IFCPOLYLINE((#4397,#4397));
#3691=IFCPOLYLINE((#4397,#4400));
#3692=IFCPOLYLINE((#4400,#4400));
#3693=IFCPOLYLINE((#4400,#4403));
#3694=IFCPOLYLINE((#4403,#4403));
#3695=IFCPOLYLINE((#4403,#4406));
#3696=IFCPOLYLINE((#4406,#4406));
#3697=IFCPOLYLINE((#4406,#4409));
#3698=IFCPOLYLINE((#4409,#4409));
#3699=IFCPOLYLINE((#4409,#4412));
#3700=IFCPOLYLINE((#4412,#4412));
#3701=IFCPOLYLINE((#4412,#4415));
#3702=IFCPOLYLINE((#4415,#4415));
#3703=IFCPOLYLINE((#4415,#4418));
#3704=IFCPOLYLINE((#4418,#4418));
#3705=IFCPOLYLINE((#4418,#4421));
#3706=IFCPOLYLINE((#4421,#4421));
#3707=IFCPOLYLINE((#4421,#4424));
#3708=IFCPOLYLINE((#4424,#4424));
#3709=IFCPOLYLINE((#4424,#4427));
#3710=IFCPOLYLINE((#4427,#4427));
#3711=IFCPOLYLINE((#4427,#4430));
#3712=IFCPOLYLINE((#4430,#4430));
#3713=IFCPOLYLINE((#4430,#4433));
#3714=IFCPOLYLINE((#4433,#4433));
#3715=IFCPOLYLINE((#4433,#4436));
#3716=IFCPOLYLINE((#4436,#4436));
#3717=IFCPOLYLINE((#4436,#4439));
#3718=IFCPOLYLINE((#4439,#4439));
#3719=IFCPOLYLINE((#4439,#4442));
#3720=IFCPOLYLINE((#4442,#4442));
#3721=IFCPOLYLINE((#4442,#4445));
#3722=IFCPOLYLINE((#4445,#4445));
#3723=IFCPOLYLINE((#4445,#4448));
#3724=IFCPOLYLINE((#4448,#4448));
#3725=IFCPOLYLINE((#4448,#4451));
#3726=IFCPOLYLINE((#4451,#4451));
#3727=IFCPOLYLINE((#4451,#4454));
#3728=IFCPOLYLINE((#4454,#4454));
#3729=IFCPOLYLINE((#4454,#4457));
#3730=IFCPOLYLINE((#4457,#4457));
#3731=IFCPOLYLINE((#4457,#4460));
#3732=IFCPOLYLINE((#4460,#4460));
#3733=IFCPOLYLINE((#4460,#4463));
#3734=IFCPOLYLINE((#4463,#4463));
#3735=IFCPOLYLINE((#4463,#4466));
#3736=IFCPOLYLINE((#4466,#4466));
#3737=IFCPOLYLINE((#4466,#4469));
#3738=IFCPOLYLINE((#4469,#4469));
#3739=IFCPOLYLINE((#4469,#4472));
#3740=IFCPOLYLINE((#4472,#4472));
#3741=IFCPOLYLINE((#4472,#4475));
#3742=IFCPOLYLINE((#4475,#4475));
#3743=IFCPOLYLINE((#4475,#4478));
#3744=IFCPOLYLINE((#4478,#4478));
#3745=IFCPOLYLINE((#4478,#4481));
#3746=IFCPOLYLINE((#4481,#4481));
#3747=IFCPOLYLINE((#4481,#4484));
#3748=IFCPOLYLINE((#4484,#4484));
#3749=IFCPOLYLINE((#4484,#4487));
#3750=IFCPOLYLINE((#4487,#4487));
#3751=IFCPOLYLINE((#4487,#4490));
#3752=IFCPOLYLINE((#4490,#4490));
#3753=IFCPOLYLINE((#4490,#4493));
#3754=IFCPOLYLINE((#4493,#4493));
#3755=IFCPOLYLINE((#4493,#4496));
#3756=IFCPOLYLINE((#4496,#4496));
#3757=IFCPOLYLINE((#4496,#4499));
#3758=IFCPOLYLINE((#4499,#4499));
#3759=IFCPOLYLINE((#4499,#4502));
#3760=IFCPOLYLINE((#4502,#4502));
#3761=IFCPOLYLINE((#4502,#4505));
#3762=IFCPOLYLINE((#4505,#4505));
#3763=IFCPOLYLINE((#4505,#4508));
#3764=IFCPOLYLINE((#4508,#4508));
#3765=IFCPOLYLINE((#4508,#4511));
#3766=IFCPOLYLINE((#4511,#4511));
#3767=IFCPOLYLINE((#4511,#4514));
#3768=IFCPOLYLINE((#4514,#4514));
#3769=IFCPOLYLINE((#4514,#4517));
#3770=IFCPOLYLINE((#4517,#4517));
#3771=IFCPOLYLINE((#4517,#4520));
#3772=IFCPOLYLINE((#4520,#4520));
#3773=IFCPOLYLINE((#4520,#4523));
#3774=IFCPOLYLINE((#4523,#4523));
#3775=IFCPOLYLINE((#4523,#4526));
#3776=IFCPOLYLINE((#4526,#4526));
#3777=IFCPOLYLINE((#4526,#4529));
#3778=IFCPOLYLINE((#4529,#4529));
#3779=IFCPOLYLINE((#4529,#4532));
#3780=IFCPOLYLINE((#4532,#4532));
#3781=IFCPOLYLINE((#4532,#4535));
#3782=IFCPOLYLINE((#4535,#4535));
#3783=IFCPOLYLINE((#4535,#4538));
#3784=IFCPOLYLINE((#4538,#4538));
#3785=IFCPOLYLINE((#4538,#4541));
#3786=IFCPOLYLINE((#4541,#4541));
#3787=IFCPOLYLINE((#4541,#4544));
#3788=IFCPOLYLINE((#4544,#4544));
#3789=IFCPOLYLINE((#4544,#4547));
#3790=IFCPOLYLINE((#4547,#4547));
#3791=IFCPOLYLINE((#4547,#4550));
#3792=IFCPOLYLINE((#4550,#4550));
#3793=IFCPOLYLINE((#4550,#4553));
#3794=IFCPOLYLINE((#4553,#4553));
#3795=IFCPOLYLINE((#4553,#4556));
#3796=IFCPOLYLINE((#4556,#4556));
#3797=IFCPOLYLINE((#4556,#4559));
#3798=IFCPOLYLINE((#4559,#4559));
#3799=IFCPOLYLINE((#4559,#4562));
#3800=IFCPOLYLINE((#4562,#4562));
#3801=IFCPOLYLINE((#4562,#4565));
#3802=IFCPOLYLINE((#4565,#4565));
#3803=IFCPOLYLINE((#4565,#4568));
#3804=IFCPOLYLINE((#4568,#4568));
#3805=IFCPOLYLINE((#4568,#4571));
#3806=IFCPOLYLINE((#4571,#4571));
#3807=IFCPOLYLINE((#4571,#4574));
#3808=IFCPOLYLINE((#4574,#4574));
#3809=IFCPOLYLINE((#4574,#4577));
#3810=IFCPOLYLINE((#4577,#4577));
#3811=IFCPOLYLINE((#4577,#4580));
#3812=IFCPOLYLINE((#4580,#4580));
#3813=IFCPOLYLINE((#4580,#4583));
#3814=IFCPOLYLINE((#4583,#4583));
#3815=IFCPOLYLINE((#4583,#4586));
#3816=IFCPOLYLINE((#4586,#4586));
#3817=IFCPOLYLINE((#4586,#4589));
#3818=IFCPOLYLINE((#4589,#4589));
#3819=IFCPOLYLINE((#4589,#4592));
#3820=IFCPOLYLINE((#4592,#4592));
#3821=IFCPOLYLINE((#4592,#4595));
#3822=IFCPOLYLINE((#4595,#4595));
#3823=IFCPOLYLINE((#4595,#4598));
#3824=IFCPOLYLINE((#4598,#4598));
#3825=IFCPOLYLINE((#4598,#4601));
#3826=IFCPOLYLINE((#4601,#4601));
#3827=IFCPOLYLINE((#4601,#4604));
#3828=IFCPOLYLINE((#4604,#4604));
#3829=IFCPOLYLINE((#4604,#4607));
#3830=IFCPOLYLINE((#4607,#4607));
#3831=IFCPOLYLINE((#4607,#4610));
#3832=IFCPOLYLINE((#4610,#4610));
#3833=IFCPOLYLINE((#4610,#4613));
#3834=IFCPOLYLINE((#4613,#4613));
#3835=IFCPOLYLINE((#4613,#4616));
#3836=IFCPOLYLINE((#4616,#4616));
#3837=IFCPOLYLINE((#4616,#4619));
#3838=IFCPOLYLINE((#4619,#4619));
#3839=IFCPOLYLINE((#4619,#4622));
#3840=IFCPOLYLINE((#4622,#4622));
#3841=IFCPOLYLINE((#4622,#4625));
#3842=IFCPOLYLINE((#4625,#4625));
#3843=IFCPOLYLINE((#4625,#4628));
#3844=IFCPOLYLINE((#4628,#4628));
#3845=IFCPOLYLINE((#4628,#4631));
#3846=IFCPOLYLINE((#4631,#4631));
#3847=IFCPOLYLINE((#4631,#4634));
#3848=IFCPOLYLINE((#4634,#4634));
#3849=IFCPOLYLINE((#4634,#4637));
#3850=IFCPOLYLINE((#4637,#4637));
#3851=IFCPOLYLINE((#4637,#4640));
#3852=IFCPOLYLINE((#4640,#4640));
#3853=IFCPOLYLINE((#4640,#4643));
#3854=IFCPOLYLINE((#4643,#4643));
#3855=IFCPOLYLINE((#4643,#4646));
#3856=IFCPOLYLINE((#4646,#4647));
#3857=IFCPOLYLINE((#4647,#4649));
#3858=IFCPOLYLINE((#4649,#4651));
#3859=IFCPOLYLINE((#4651,#4083));
#3860=IFCPOLYLINE((#4653,#4653));
#3861=IFCPOLYLINE((#4653,#4653));
#3862=IFCPOLYLINE((#4653,#4657));
#3863=IFCPOLYLINE((#4657,#4657));
#3864=IFCPOLYLINE((#4657,#4657));
#3865=IFCPOLYLINE((#4657,#4657));
#3866=IFCPOLYLINE((#4657,#4657));
#3867=IFCPOLYLINE((#4657,#4663));
#3868=IFCPOLYLINE((#4663,#4663));
#3869=IFCPOLYLINE((#4663,#4663));
#3870=IFCPOLYLINE((#4663,#4667));
#3871=IFCPOLYLINE((#4667,#4667));
#3872=IFCPOLYLINE((#4667,#4670));
#3873=IFCPOLYLINE((#4670,#4670));
#3874=IFCPOLYLINE((#4670,#4673));
#3875=IFCPOLYLINE((#4673,#4673));
#3876=IFCPOLYLINE((#4673,#4676));
#3877=IFCPOLYLINE((#4676,#4676));
#3878=IFCPOLYLINE((#4676,#4679));
#3879=IFCPOLYLINE((#4679,#4679));
#3880=IFCPOLYLINE((#4679,#4682));
#3881=IFCPOLYLINE((#4682,#4682));
#3882=IFCPOLYLINE((#4682,#4685));
#3883=IFCPOLYLINE((#4685,#4685));
#3884=IFCPOLYLINE((#4685,#4688));
#3885=IFCPOLYLINE((#4688,#4690));
#3886=IFCPOLYLINE((#4690,#4692));
#3887=IFCPOLYLINE((#4692,#4692));
#3888=IFCPOLYLINE((#4692,#4695));
#3889=IFCPOLYLINE((#4695,#4695));
#3890=IFCPOLYLINE((#4695,#4698));
#3891=IFCPOLYLINE((#4698,#4698));
#3892=IFCPOLYLINE((#4698,#4701));
#3893=IFCPOLYLINE((#4701,#4701));
#3894=IFCPOLYLINE((#4701,#4704));
#3895=IFCPOLYLINE((#4704,#4704));
#3896=IFCPOLYLINE((#4704,#4707));
#3897=IFCPOLYLINE((#4707,#4707));
#3898=IFCPOLYLINE((#4707,#4710));
#3899=IFCPOLYLINE((#4710,#4710));
#3900=IFCPOLYLINE((#4710,#4713));
#3901=IFCPOLYLINE((#4713,#4713));
#3902=IFCPOLYLINE((#4713,#4716));
#3903=IFCPOLYLINE((#4716,#4716));
#3904=IFCPOLYLINE((#4716,#4719));
#3905=IFCPOLYLINE((#4719,#4719));
#3906=IFCPOLYLINE((#4719,#4722));
#3907=IFCPOLYLINE((#4722,#4722));
#3908=IFCPOLYLINE((#4722,#4725));
#3909=IFCPOLYLINE((#4725,#4725));
#3910=IFCPOLYLINE((#4725,#4728));
#3911=IFCPOLYLINE((#4728,#4728));
#3912=IFCPOLYLINE((#4728,#4731));
#3913=IFCPOLYLINE((#4731,#4731));
#3914=IFCPOLYLINE((#4731,#4734));
#3915=IFCPOLYLINE((#4734,#4734));
#3916=IFCPOLYLINE((#4734,#4737));
#3917=IFCPOLYLINE((#4737,#4737));
#3918=IFCPOLYLINE((#4737,#4740));
#3919=IFCPOLYLINE((#4740,#4740));
#3920=IFCPOLYLINE((#4740,#4743));
#3921=IFCPOLYLINE((#4743,#4743));
#3922=IFCPOLYLINE((#4743,#4746));
#3923=IFCPOLYLINE((#4746,#4746));
#3924=IFCPOLYLINE((#4746,#4749));
#3925=IFCPOLYLINE((#4749,#4749));
#3926=IFCPOLYLINE((#4749,#4752));
#3927=IFCPOLYLINE((#4752,#4752));
#3928=IFCPOLYLINE((#4752,#4755));
#3929=IFCPOLYLINE((#4755,#4755));
#3930=IFCPOLYLINE((#4755,#4758));
#3931=IFCPOLYLINE((#4758,#4760));
#3932=IFCPOLYLINE((#4760,#4760));
#3933=IFCPOLYLINE((#4760,#4763));
#3934=IFCPOLYLINE((#4763,#4763));
#3935=IFCPOLYLINE((#4763,#4766));
#3936=IFCPOLYLINE((#4766,#4766));
#3937=IFCPOLYLINE((#4766,#4769));
#3938=IFCPOLYLINE((#4769,#4769));
#3939=IFCPOLYLINE((#4769,#4772));
#3940=IFCPOLYLINE((#4772,#4772));
#3941=IFCPOLYLINE((#4772,#4775));
#3942=IFCPOLYLINE((#4775,#4777));
#3943=IFCPOLYLINE((#4777,#4777));
#3944=IFCPOLYLINE((#4777,#4777));
#3945=IFCPOLYLINE((#4777,#4781));
#3946=IFCPOLYLINE((#4781,#4781));
#3947=IFCPOLYLINE((#4781,#4781));
#3948=IFCPOLYLINE((#4781,#4785));
#3949=IFCPOLYLINE((#4785,#4787));
#3950=IFCPOLYLINE((#4787,#4787));
#3951=IFCPOLYLINE((#4787,#4787));
#3952=IFCPOLYLINE((#4787,#4787));
#3953=IFCPOLYLINE((#4787,#4787));
#3954=IFCPOLYLINE((#4787,#4793));
#3955=IFCPOLYLINE((#4793,#4793));
#3956=IFCPOLYLINE((#4793,#4793));
#3957=IFCPOLYLINE((#4793,#4793));
#3958=IFCPOLYLINE((#4793,#4793));
#3959=IFCPOLYLINE((#4793,#4799));
#3960=IFCPOLYLINE((#4799,#4801));
#3961=IFCPOLYLINE((#4801,#4801));
#3962=IFCPOLYLINE((#4801,#4801));
#3963=IFCPOLYLINE((#4801,#4801));
#3964=IFCPOLYLINE((#4801,#4806));
#3965=IFCPOLYLINE((#4806,#4808));
#3966=IFCPOLYLINE((#4808,#4808));
#3967=IFCPOLYLINE((#4808,#4808));
#3968=IFCPOLYLINE((#4808,#4812));
#3969=IFCPOLYLINE((#4812,#4812));
#3970=IFCPOLYLINE((#4812,#4812));
#3971=IFCPOLYLINE((#4812,#4816));
#3972=IFCPOLYLINE((#4816,#4816));
#3973=IFCPOLYLINE((#4816,#4816));
#3974=IFCPOLYLINE((#4816,#4820));
#3975=IFCPOLYLINE((#4820,#4820));
#3976=IFCPOLYLINE((#4820,#4823));
#3977=IFCPOLYLINE((#4823,#4823));
#3978=IFCPOLYLINE((#4823,#4826));
#3979=IFCPOLYLINE((#4826,#4826));
#3980=IFCPOLYLINE((#4826,#4829));
#3981=IFCPOLYLINE((#4829,#4829));
#3982=IFCPOLYLINE((#4829,#4832));
#3983=IFCPOLYLINE((#4832,#4832));
#3984=IFCPOLYLINE((#4832,#4835));
#3985=IFCPOLYLINE((#4835,#4835));
#3986=IFCPOLYLINE((#4835,#4838));
#3987=IFCPOLYLINE((#4838,#4838));
#3988=IFCPOLYLINE((#4838,#4841));
#3989=IFCPOLYLINE((#4841,#4841));
#3990=IFCPOLYLINE((#4841,#4844));
#3991=IFCPOLYLINE((#4844,#4844));
#3992=IFCPOLYLINE((#4844,#4847));
#3993=IFCPOLYLINE((#4847,#4847));
#3994=IFCPOLYLINE((#4847,#4850));
#3995=IFCPOLYLINE((#4850,#4850));
#3996=IFCPOLYLINE((#4850,#4853));
#3997=IFCPOLYLINE((#4853,#4853));
#3998=IFCPOLYLINE((#4853,#4856));
#3999=IFCPOLYLINE((#4856,#4856));
#4000=IFCPOLYLINE((#4856,#4859));
#4001=IFCPOLYLINE((#4859,#4859));
#4002=IFCPOLYLINE((#4859,#4862));
#4003=IFCPOLYLINE((#4862,#4862));
#4004=IFCPOLYLINE((#4862,#4865));
#4005=IFCPOLYLINE((#4865,#4865));
#4006=IFCPOLYLINE((#4865,#4868));
#4007=IFCPOLYLINE((#4868,#4868));
#4008=IFCPOLYLINE((#4868,#4871));
#4009=IFCPOLYLINE((#4871,#4871));
#4010=IFCPOLYLINE((#4871,#4874));
#4011=IFCPOLYLINE((#4874,#4874));
#4012=IFCPOLYLINE((#4874,#4877));
#4013=IFCPOLYLINE((#4877,#4877));
#4014=IFCPOLYLINE((#4877,#4880));
#4015=IFCPOLYLINE((#4880,#4880));
#4016=IFCPOLYLINE((#4880,#4883));
#4017=IFCPOLYLINE((#4883,#4883));
#4018=IFCPOLYLINE((#4883,#4886));
#4019=IFCPOLYLINE((#4886,#4886));
#4020=IFCPOLYLINE((#4886,#4889));
#4021=IFCPOLYLINE((#4889,#4889));
#4022=IFCPOLYLINE((#4889,#4892));
#4023=IFCPOLYLINE((#4892,#4892));
#4024=IFCPOLYLINE((#4892,#4895));
#4025=IFCPOLYLINE((#4895,#4895));
#4026=IFCPOLYLINE((#4895,#4898));
#4027=IFCPOLYLINE((#4898,#4898));
#4028=IFCPOLYLINE((#4898,#4901));
#4029=IFCPOLYLINE((#4901,#4903));
#4030=IFCPOLYLINE((#4903,#4903));
#4031=IFCPOLYLINE((#4903,#4906));
#4032=IFCPOLYLINE((#4906,#4906));
#4033=IFCPOLYLINE((#4906,#4909));
#4034=IFCPOLYLINE((#4909,#4909));
#4035=IFCPOLYLINE((#4909,#4912));
#4036=IFCPOLYLINE((#4912,#4912));
#4037=IFCPOLYLINE((#4912,#4915));
#4038=IFCPOLYLINE((#4915,#4915));
#4039=IFCPOLYLINE((#4915,#4915));
#4040=IFCPOLYLINE((#4915,#4919));
#4041=IFCPOLYLINE((#4919,#4919));
#4042=IFCPOLYLINE((#4919,#4919));
#4043=IFCPOLYLINE((#4919,#4923));
#4044=IFCPOLYLINE((#4923,#4923));
#4045=IFCPOLYLINE((#4923,#4923));
#4046=IFCPOLYLINE((#4923,#4927));
#4047=IFCPOLYLINE((#4927,#4927));
#4048=IFCPOLYLINE((#4927,#4927));
#4049=IFCPOLYLINE((#4927,#4927));
#4050=IFCPOLYLINE((#4927,#4932));
#4051=IFCPOLYLINE((#4932,#4653));
#4052=IFCPOLYLINE((#4653,#4653));
#4053=IFCPOLYLINE((#4653,#4653));
#4054=IFCPOLYLINE((#4653,#4653));
#4055=IFCFACEOUTERBOUND(#603,.T.);
#4056=IFCADVANCEDFACE((#4055,#27),#4057,.T.);
#4057=IFCBSPLINESURFACEWITHKNOTS(3,1,((#4071,#4072),(#4073,#4074),(#4075,
#4076),(#4077,#4078),(#4079,#4080),(#4081,#4082)),.UNSPECIFIED.,.F.,.F.,
 .U.,(4,1,1,4),(2,2),(0.,0.352757198465535,0.647242801534465,1.),(0.,1.),
 .UNSPECIFIED.);
#4058=IFCRELAGGREGATES('1kB3AQvfGpkFm_7uHNRdoP',#5005,$,$,#4994,(#4943));
#4059=IFCRELAGGREGATES('0Cp_wDOOr3amQ16IWrGpUy',#5005,$,$,#4943,(#19));
#4060=IFCRELAGGREGATES('1NfacxJUhl1S_ka3CQXVRt',#5005,$,$,#19,(#18));
#4061=IFCLOCALPLACEMENT($,#4065);
#4062=IFCLOCALPLACEMENT(#4061,#4066);
#4063=IFCLOCALPLACEMENT(#4062,#4067);
#4064=IFCLOCALPLACEMENT(#4063,#4068);
#4065=IFCAXIS2PLACEMENT3D(#4070,#4940,#4941);
#4066=IFCAXIS2PLACEMENT3D(#4070,#4940,#4941);
#4067=IFCAXIS2PLACEMENT3D(#4937,#4940,#4941);
#4068=IFCAXIS2PLACEMENT3D(#4938,#4940,#4941);
#4069=IFCAXIS2PLACEMENT3D(#4070,#4940,#4941);
#4070=IFCCARTESIANPOINT((0.,0.,0.));
#4071=IFCCARTESIANPOINT((3.5623730952E-5,3.5623730946E-5,3.5937));
#4072=IFCCARTESIANPOINT((12.0000356237,3.5623731016E-5,3.5937));
#4073=IFCCARTESIANPOINT((3.5623730952E-5,4.3914612132,2.32602199022));
#4074=IFCCARTESIANPOINT((12.0000356237,4.3914612132,2.32602199022));
#4075=IFCCARTESIANPOINT((3.5623730952E-5,12.448897358,7.2385040252E-5));
#4076=IFCCARTESIANPOINT((12.0000356237,12.448897358,7.2385040321E-5));
#4077=IFCCARTESIANPOINT((3.5623731022E-5,12.448897358,16.187327615));
#4078=IFCCARTESIANPOINT((12.0000356237,12.448897358,16.187327615));
#4079=IFCCARTESIANPOINT((3.5623730952E-5,4.3914612132,13.8613780098));
#4080=IFCCARTESIANPOINT((12.0000356237,4.3914612132,13.8613780098));
#4081=IFCCARTESIANPOINT((3.5623730952E-5,3.5623730946E-5,12.5937));
#4082=IFCCARTESIANPOINT((12.0000356237,3.5623731016E-5,12.5937));
#4083=IFCCARTESIANPOINT((0.,0.));
#4084=IFCCARTESIANPOINT((3.5623730952E-5,0.200815243014,3.535751728));
#4085=IFCCARTESIANPOINT((1.0752688172E-6,0.));
#4086=IFCCARTESIANPOINT((3.5623730952E-5,0.40154402307,3.477884238));
#4087=IFCCARTESIANPOINT((3.5623730952E-5,0.60217112467,3.4201783121));
#4088=IFCCARTESIANPOINT((2.1505376344E-6,0.));
#4089=IFCCARTESIANPOINT((3.5623730952E-5,0.80264570858,3.3627147325));
#4090=IFCCARTESIANPOINT((3.5623730952E-5,1.00291693559,3.3055742812));
#4091=IFCCARTESIANPOINT((3.225806452E-6,0.));
#4092=IFCCARTESIANPOINT((3.5623730952E-5,1.20293396645,3.2488377401));
#4093=IFCCARTESIANPOINT((3.5623730952E-5,1.40264596194,3.1925858916));
#4094=IFCCARTESIANPOINT((4.301075269E-6,0.));
#4095=IFCCARTESIANPOINT((3.5623730952E-5,1.60200208284,3.13689951746));
#4096=IFCCARTESIANPOINT((3.5623730952E-5,1.80095148992,3.08185939991));
#4097=IFCCARTESIANPOINT((5.376344086E-6,0.));
#4098=IFCCARTESIANPOINT((3.5623730952E-5,1.99944334394,3.02754632099));
#4099=IFCCARTESIANPOINT((3.5623730952E-5,2.19742680568,2.97404106276));
#4100=IFCCARTESIANPOINT((6.451612903E-6,0.));
#4101=IFCCARTESIANPOINT((3.5623730952E-5,2.39485103591,2.9214244073));
#4102=IFCCARTESIANPOINT((3.5623730952E-5,2.59166519541,2.86977713667));
#4103=IFCCARTESIANPOINT((7.52688172E-6,0.));
#4104=IFCCARTESIANPOINT((3.5623730952E-5,2.78781844494,2.81918003295));
#4105=IFCCARTESIANPOINT((3.5623730952E-5,2.98325994529,2.76971387819));
#4106=IFCCARTESIANPOINT((8.602150538E-6,0.));
#4107=IFCCARTESIANPOINT((3.5623730952E-5,3.1779388572,2.72145945448));
#4108=IFCCARTESIANPOINT((3.5623730952E-5,3.3718043415,2.67449754388));
#4109=IFCCARTESIANPOINT((9.677419355E-6,0.));
#4110=IFCCARTESIANPOINT((3.5623730952E-5,3.5648055589,2.62890892846));
#4111=IFCCARTESIANPOINT((3.5623730952E-5,3.7568916702,2.58477439029));
#4112=IFCCARTESIANPOINT((1.0752688172E-5,0.));
#4113=IFCCARTESIANPOINT((3.5623730952E-5,3.9480118361,2.54217471144));
#4114=IFCCARTESIANPOINT((3.5623730952E-5,4.1381152175,2.50119067398));
#4115=IFCCARTESIANPOINT((1.1827956989E-5,0.));
#4116=IFCCARTESIANPOINT((3.5623730952E-5,4.3271509751,2.46190305998));
#4117=IFCCARTESIANPOINT((3.5623730952E-5,4.5150682697,2.4243926515));
#4118=IFCCARTESIANPOINT((1.2903225806E-5,0.));
#4119=IFCCARTESIANPOINT((3.5623730953E-5,4.7018162621,2.38874023063));
#4120=IFCCARTESIANPOINT((3.5623730953E-5,4.887344113,2.35502657942));
#4121=IFCCARTESIANPOINT((1.3978494624E-5,0.));
#4122=IFCCARTESIANPOINT((3.5623730953E-5,5.0716009831,2.32333247994));
#4123=IFCCARTESIANPOINT((3.5623730953E-5,5.2545360334,2.29373871427));
#4124=IFCCARTESIANPOINT((1.5053763441E-5,0.));
#4125=IFCCARTESIANPOINT((3.5623730953E-5,5.4360984245,2.26632606448));
#4126=IFCCARTESIANPOINT((3.5623730953E-5,5.6162373172,2.24117531263));
#4127=IFCCARTESIANPOINT((1.6129032258E-5,0.));
#4128=IFCCARTESIANPOINT((3.5623730953E-5,5.7949018723,2.2183672408));
#4129=IFCCARTESIANPOINT((3.5623730953E-5,5.9720412505,2.19798263105));
#4130=IFCCARTESIANPOINT((1.7204301075E-5,0.));
#4131=IFCCARTESIANPOINT((3.5623730953E-5,6.1476046127,2.18010226546));
#4132=IFCCARTESIANPOINT((3.5623730954E-5,6.3215411196,2.16480692608));
#4133=IFCCARTESIANPOINT((1.8279569892E-5,0.));
#4134=IFCCARTESIANPOINT((3.5623730954E-5,6.493799932,2.152177395));
#4135=IFCCARTESIANPOINT((3.5623730954E-5,6.6643302106,2.14229445428));
#4136=IFCCARTESIANPOINT((1.935483871E-5,0.));
#4137=IFCCARTESIANPOINT((3.5623730954E-5,6.8330811162,2.13523888599));
#4138=IFCCARTESIANPOINT((3.5623730954E-5,7.0000018097,2.1310914722));
#4139=IFCCARTESIANPOINT((2.0430107527E-5,0.));
#4140=IFCCARTESIANPOINT((3.5623730955E-5,7.1650414517,2.12993299498));
#4141=IFCCARTESIANPOINT((3.5623730955E-5,7.328149203,2.13184423641));
#4142=IFCCARTESIANPOINT((2.1505376344E-5,0.));
#4143=IFCCARTESIANPOINT((3.5623730955E-5,7.4892742244,2.13690597853));
#4144=IFCCARTESIANPOINT((3.5623730955E-5,7.6483656767,2.14519900344));
#4145=IFCCARTESIANPOINT((2.2580645161E-5,0.));
#4146=IFCCARTESIANPOINT((3.5623730955E-5,7.8053727207,2.1568040932));
#4147=IFCCARTESIANPOINT((3.5623730956E-5,7.9602445171,2.17180202987));
#4148=IFCCARTESIANPOINT((2.3655913978E-5,0.));
#4149=IFCCARTESIANPOINT((3.5623730956E-5,8.1129302267,2.19027359553));
#4150=IFCCARTESIANPOINT((3.5623730956E-5,8.2633790102,2.21229957225));
#4151=IFCCARTESIANPOINT((2.4731182796E-5,0.));
#4152=IFCCARTESIANPOINT((3.5623730957E-5,8.4115400285,2.23796074209));
#4153=IFCCARTESIANPOINT((3.5623730957E-5,8.5573624423,2.26733788712));
#4154=IFCCARTESIANPOINT((2.5806451613E-5,0.));
#4155=IFCCARTESIANPOINT((3.5623730957E-5,8.7007954124,2.30051178942));
#4156=IFCCARTESIANPOINT((3.5623730958E-5,8.8417880995,2.33756323106));
#4157=IFCCARTESIANPOINT((2.688172043E-5,0.));
#4158=IFCCARTESIANPOINT((3.5623730958E-5,8.9802896645,2.3785729941));
#4159=IFCCARTESIANPOINT((3.5623730958E-5,9.116249268,2.42362186061));
#4160=IFCCARTESIANPOINT((2.7956989247E-5,0.));
#4161=IFCCARTESIANPOINT((3.5623730959E-5,9.249616071,2.47279061266));
#4162=IFCCARTESIANPOINT((3.5623730959E-5,9.3803392341,2.52616003232));
#4163=IFCCARTESIANPOINT((2.9032258065E-5,0.));
#4164=IFCCARTESIANPOINT((3.5623730959E-5,9.5083679181,2.58381090167));
#4165=IFCCARTESIANPOINT((3.562373096E-5,9.6336512837,2.64582400277));
#4166=IFCCARTESIANPOINT((3.0107526882E-5,0.));
#4167=IFCCARTESIANPOINT((3.562373096E-5,9.7561384919,2.71228011768));
#4168=IFCCARTESIANPOINT((3.5623730961E-5,9.8757787032,2.78326002849));
#4169=IFCCARTESIANPOINT((3.1182795699E-5,0.));
#4170=IFCCARTESIANPOINT((3.5623730961E-5,9.9925210786,2.85884451726));
#4171=IFCCARTESIANPOINT((3.5623730962E-5,10.1063147788,2.93911436605));
#4172=IFCCARTESIANPOINT((3.2258064516E-5,0.));
#4173=IFCCARTESIANPOINT((3.5623730962E-5,10.2171089645,3.02415035694));
#4174=IFCCARTESIANPOINT((3.5623730963E-5,10.3248527965,3.114033272));
#4175=IFCCARTESIANPOINT((3.3333333333E-5,0.));
#4176=IFCCARTESIANPOINT((3.5623730963E-5,10.4294954356,3.2088438933));
#4177=IFCCARTESIANPOINT((3.5623730964E-5,10.5309860426,3.3086630029));
#4178=IFCCARTESIANPOINT((3.4408602151E-5,0.));
#4179=IFCCARTESIANPOINT((3.5623730964E-5,10.6292737782,3.4135713829));
#4180=IFCCARTESIANPOINT((3.5623730965E-5,10.7243078032,3.5236498153));
#4181=IFCCARTESIANPOINT((3.5483870968E-5,0.));
#4182=IFCCARTESIANPOINT((3.5623730966E-5,10.8160377701,3.638976429));
#4183=IFCCARTESIANPOINT((3.5623730966E-5,10.9044339812,3.7595179319));
#4184=IFCCARTESIANPOINT((3.6559139785E-5,0.));
#4185=IFCCARTESIANPOINT((3.5623730967E-5,10.9894944862,3.8850913132));
#4186=IFCCARTESIANPOINT((3.5623730968E-5,11.0712192851,4.015503039));
#4187=IFCCARTESIANPOINT((3.7634408602E-5,0.));
#4188=IFCCARTESIANPOINT((3.5623730968E-5,11.1496083779,4.1505595757));
#4189=IFCCARTESIANPOINT((3.5623730969E-5,11.2246617647,4.2900673894));
#4190=IFCCARTESIANPOINT((3.8709677419E-5,0.));
#4191=IFCCARTESIANPOINT((3.562373097E-5,11.2963794454,4.4338329463));
#4192=IFCCARTESIANPOINT((3.562373097E-5,11.36476142,4.5816627127));
#4193=IFCCARTESIANPOINT((3.9784946237E-5,0.));
#4194=IFCCARTESIANPOINT((3.5623730971E-5,11.4298076885,4.7333631548));
#4195=IFCCARTESIANPOINT((3.5623730972E-5,11.4915182509,4.8887407388));
#4196=IFCCARTESIANPOINT((4.0860215054E-5,0.));
#4197=IFCCARTESIANPOINT((3.5623730973E-5,11.5498931073,5.0476019308));
#4198=IFCCARTESIANPOINT((3.5623730973E-5,11.6049322576,5.2097531972));
#4199=IFCCARTESIANPOINT((4.1935483871E-5,0.));
#4200=IFCCARTESIANPOINT((3.5623730974E-5,11.6566357018,5.3750010042));
#4201=IFCCARTESIANPOINT((3.5623730975E-5,11.70500344,5.5431518179));
#4202=IFCCARTESIANPOINT((4.3010752688E-5,0.));
#4203=IFCCARTESIANPOINT((3.5623730976E-5,11.750035472,5.7140121045));
#4204=IFCCARTESIANPOINT((3.5623730976E-5,11.791731798,5.8873883304));
#4205=IFCCARTESIANPOINT((4.4086021505E-5,0.));
#4206=IFCCARTESIANPOINT((3.5623730977E-5,11.8300924179,6.0630869616));
#4207=IFCCARTESIANPOINT((3.5623730978E-5,11.8651173317,6.2409144645));
#4208=IFCCARTESIANPOINT((4.5161290323E-5,0.));
#4209=IFCCARTESIANPOINT((3.5623730979E-5,11.8968065395,6.4206773052));
#4210=IFCCARTESIANPOINT((3.5623730979E-5,11.9251600411,6.60218195));
#4211=IFCCARTESIANPOINT((4.623655914E-5,0.));
#4212=IFCCARTESIANPOINT((3.562373098E-5,11.9501778367,6.785234865));
#4213=IFCCARTESIANPOINT((3.5623730981E-5,11.9718599262,6.9696425165));
#4214=IFCCARTESIANPOINT((4.7311827957E-5,0.));
#4215=IFCCARTESIANPOINT((3.5623730982E-5,11.9902063096,7.1552113708));
#4216=IFCCARTESIANPOINT((3.5623730982E-5,12.005216987,7.3417478939));
#4217=IFCCARTESIANPOINT((4.8387096774E-5,0.));
#4218=IFCCARTESIANPOINT((3.5623730983E-5,12.0168919583,7.5290585522));
#4219=IFCCARTESIANPOINT((3.5623730984E-5,12.0252312235,7.7169498118));
#4220=IFCCARTESIANPOINT((4.9462365591E-5,0.));
#4221=IFCCARTESIANPOINT((3.5623730984E-5,12.0302347826,7.905228139));
#4222=IFCCARTESIANPOINT((3.5623730985E-5,12.0319026356,8.0937));
#4223=IFCCARTESIANPOINT((5.0537634409E-5,0.));
#4224=IFCCARTESIANPOINT((3.5623730986E-5,12.0302347826,8.282171861));
#4225=IFCCARTESIANPOINT((3.5623730986E-5,12.0252312235,8.4704501882));
#4226=IFCCARTESIANPOINT((5.1612903226E-5,0.));
#4227=IFCCARTESIANPOINT((3.5623730987E-5,12.0168919583,8.6583414478));
#4228=IFCCARTESIANPOINT((3.5623730988E-5,12.005216987,8.8456521061));
#4229=IFCCARTESIANPOINT((5.2688172043E-5,0.));
#4230=IFCCARTESIANPOINT((3.5623730988E-5,11.9902063096,9.0321886292));
#4231=IFCCARTESIANPOINT((3.5623730989E-5,11.9718599262,9.2177574835));
#4232=IFCCARTESIANPOINT((5.376344086E-5,0.));
#4233=IFCCARTESIANPOINT((3.5623730989E-5,11.9501778367,9.402165135));
#4234=IFCCARTESIANPOINT((3.562373099E-5,11.9251600411,9.58521805));
#4235=IFCCARTESIANPOINT((5.4838709677E-5,0.));
#4236=IFCCARTESIANPOINT((3.562373099E-5,11.8968065395,9.7667226948));
#4237=IFCCARTESIANPOINT((3.5623730991E-5,11.8651173317,9.9464855355));
#4238=IFCCARTESIANPOINT((5.5913978495E-5,0.));
#4239=IFCCARTESIANPOINT((3.5623730991E-5,11.8300924179,10.1243130384));
#4240=IFCCARTESIANPOINT((3.5623730992E-5,11.791731798,10.3000116696));
#4241=IFCCARTESIANPOINT((5.6989247312E-5,0.));
#4242=IFCCARTESIANPOINT((3.5623730992E-5,11.750035472,10.4733878955));
#4243=IFCCARTESIANPOINT((3.5623730992E-5,11.70500344,10.6442481821));
#4244=IFCCARTESIANPOINT((5.8064516129E-5,0.));
#4245=IFCCARTESIANPOINT((3.5623730993E-5,11.6566357018,10.8123989958));
#4246=IFCCARTESIANPOINT((3.5623730993E-5,11.6049322576,10.9776468028));
#4247=IFCCARTESIANPOINT((5.9139784946E-5,0.));
#4248=IFCCARTESIANPOINT((3.5623730993E-5,11.5498931073,11.1397980692));
#4249=IFCCARTESIANPOINT((3.5623730994E-5,11.4915182509,11.2986592612));
#4250=IFCCARTESIANPOINT((6.0215053763E-5,0.));
#4251=IFCCARTESIANPOINT((3.5623730994E-5,11.4298076885,11.4540368452));
#4252=IFCCARTESIANPOINT((3.5623730994E-5,11.36476142,11.6057372873));
#4253=IFCCARTESIANPOINT((6.1290322581E-5,0.));
#4254=IFCCARTESIANPOINT((3.5623730994E-5,11.2963794454,11.7535670537));
#4255=IFCCARTESIANPOINT((3.5623730994E-5,11.2246617647,11.8973326106));
#4256=IFCCARTESIANPOINT((6.2365591398E-5,0.));
#4257=IFCCARTESIANPOINT((3.5623730994E-5,11.1496083779,12.0368404243));
#4258=IFCCARTESIANPOINT((3.5623730994E-5,11.0712192851,12.171896961));
#4259=IFCCARTESIANPOINT((6.3440860215E-5,0.));
#4260=IFCCARTESIANPOINT((3.5623730994E-5,10.9894944862,12.3023086868));
#4261=IFCCARTESIANPOINT((3.5623730994E-5,10.9044339812,12.4278820681));
#4262=IFCCARTESIANPOINT((6.4516129032E-5,0.));
#4263=IFCCARTESIANPOINT((3.5623730994E-5,10.8160377701,12.548423571));
#4264=IFCCARTESIANPOINT((3.5623730994E-5,10.7243078032,12.6637501847));
#4265=IFCCARTESIANPOINT((6.5591397849E-5,0.));
#4266=IFCCARTESIANPOINT((3.5623730994E-5,10.6292737782,12.7738286171));
#4267=IFCCARTESIANPOINT((3.5623730993E-5,10.5309860426,12.8787369971));
#4268=IFCCARTESIANPOINT((6.6666666667E-5,0.));
#4269=IFCCARTESIANPOINT((3.5623730993E-5,10.4294954356,12.9785561067));
#4270=IFCCARTESIANPOINT((3.5623730993E-5,10.3248527965,13.073366728));
#4271=IFCCARTESIANPOINT((6.7741935484E-5,0.));
#4272=IFCCARTESIANPOINT((3.5623730992E-5,10.2171089645,13.1632496431));
#4273=IFCCARTESIANPOINT((3.5623730992E-5,10.1063147788,13.248285634));
#4274=IFCCARTESIANPOINT((6.8817204301E-5,0.));
#4275=IFCCARTESIANPOINT((3.5623730991E-5,9.9925210786,13.3285554827));
#4276=IFCCARTESIANPOINT((3.5623730991E-5,9.8757787032,13.4041399715));
#4277=IFCCARTESIANPOINT((6.9892473118E-5,0.));
#4278=IFCCARTESIANPOINT((3.562373099E-5,9.7561384919,13.4751198823));
#4279=IFCCARTESIANPOINT((3.562373099E-5,9.6336512837,13.5415759972));
#4280=IFCCARTESIANPOINT((7.0967741935E-5,0.));
#4281=IFCCARTESIANPOINT((3.5623730989E-5,9.5083679181,13.6035890983));
#4282=IFCCARTESIANPOINT((3.5623730988E-5,9.3803392341,13.6612399677));
#4283=IFCCARTESIANPOINT((7.2043010753E-5,0.));
#4284=IFCCARTESIANPOINT((3.5623730988E-5,9.249616071,13.7146093873));
#4285=IFCCARTESIANPOINT((3.5623730987E-5,9.116249268,13.7637781394));
#4286=IFCCARTESIANPOINT((7.311827957E-5,0.));
#4287=IFCCARTESIANPOINT((3.5623730986E-5,8.9802896645,13.8088270059));
#4288=IFCCARTESIANPOINT((3.5623730986E-5,8.8417880995,13.8498367689));
#4289=IFCCARTESIANPOINT((7.4193548387E-5,0.));
#4290=IFCCARTESIANPOINT((3.5623730985E-5,8.7007954124,13.8868882106));
#4291=IFCCARTESIANPOINT((3.5623730984E-5,8.5573624423,13.9200621129));
#4292=IFCCARTESIANPOINT((7.5268817204E-5,0.));
#4293=IFCCARTESIANPOINT((3.5623730983E-5,8.4115400285,13.9494392579));
#4294=IFCCARTESIANPOINT((3.5623730982E-5,8.2633790102,13.9751004278));
#4295=IFCCARTESIANPOINT((7.6344086022E-5,0.));
#4296=IFCCARTESIANPOINT((3.5623730981E-5,8.1129302267,13.9971264045));
#4297=IFCCARTESIANPOINT((3.5623730981E-5,7.9602445171,14.0155979701));
#4298=IFCCARTESIANPOINT((7.7419354839E-5,0.));
#4299=IFCCARTESIANPOINT((3.562373098E-5,7.8053727207,14.0305959068));
#4300=IFCCARTESIANPOINT((3.5623730979E-5,7.6483656767,14.0422009966));
#4301=IFCCARTESIANPOINT((7.8494623656E-5,0.));
#4302=IFCCARTESIANPOINT((3.5623730978E-5,7.4892742244,14.0504940215));
#4303=IFCCARTESIANPOINT((3.5623730977E-5,7.328149203,14.0555557636));
#4304=IFCCARTESIANPOINT((7.9569892473E-5,0.));
#4305=IFCCARTESIANPOINT((3.5623730976E-5,7.1650414517,14.057467005));
#4306=IFCCARTESIANPOINT((3.5623730975E-5,7.0000018097,14.0563085278));
#4307=IFCCARTESIANPOINT((8.064516129E-5,0.));
#4308=IFCCARTESIANPOINT((3.5623730974E-5,6.8330811162,14.052161114));
#4309=IFCCARTESIANPOINT((3.5623730973E-5,6.6643302106,14.0451055457));
#4310=IFCCARTESIANPOINT((8.1720430108E-5,0.));
#4311=IFCCARTESIANPOINT((3.5623730973E-5,6.493799932,14.035222605));
#4312=IFCCARTESIANPOINT((3.5623730972E-5,6.3215411196,14.0225930739));
#4313=IFCCARTESIANPOINT((8.2795698925E-5,0.));
#4314=IFCCARTESIANPOINT((3.5623730971E-5,6.1476046127,14.0072977345));
#4315=IFCCARTESIANPOINT((3.562373097E-5,5.9720412505,13.9894173689));
#4316=IFCCARTESIANPOINT((8.3870967742E-5,0.));
#4317=IFCCARTESIANPOINT((3.5623730969E-5,5.7949018723,13.9690327592));
#4318=IFCCARTESIANPOINT((3.5623730968E-5,5.6162373172,13.9462246874));
#4319=IFCCARTESIANPOINT((8.4946236559E-5,0.));
#4320=IFCCARTESIANPOINT((3.5623730967E-5,5.4360984245,13.9210739355));
#4321=IFCCARTESIANPOINT((3.5623730966E-5,5.2545360334,13.8936612857));
#4322=IFCCARTESIANPOINT((8.6021505376E-5,0.));
#4323=IFCCARTESIANPOINT((3.5623730965E-5,5.0716009831,13.8640675201));
#4324=IFCCARTESIANPOINT((3.5623730965E-5,4.887344113,13.8323734206));
#4325=IFCCARTESIANPOINT((8.7096774194E-5,0.));
#4326=IFCCARTESIANPOINT((3.5623730964E-5,4.7018162621,13.7986597694));
#4327=IFCCARTESIANPOINT((3.5623730963E-5,4.5150682697,13.7630073485));
#4328=IFCCARTESIANPOINT((8.8172043011E-5,0.));
#4329=IFCCARTESIANPOINT((3.5623730962E-5,4.3271509751,13.72549694));
#4330=IFCCARTESIANPOINT((3.5623730961E-5,4.1381152175,13.686209326));
#4331=IFCCARTESIANPOINT((8.9247311828E-5,0.));
#4332=IFCCARTESIANPOINT((3.562373096E-5,3.9480118361,13.6452252886));
#4333=IFCCARTESIANPOINT((3.562373096E-5,3.7568916702,13.6026256097));
#4334=IFCCARTESIANPOINT((9.0322580645E-5,0.));
#4335=IFCCARTESIANPOINT((3.5623730959E-5,3.5648055589,13.5584910715));
#4336=IFCCARTESIANPOINT((3.5623730958E-5,3.3718043415,13.5129024561));
#4337=IFCCARTESIANPOINT((9.1397849462E-5,0.));
#4338=IFCCARTESIANPOINT((3.5623730958E-5,3.1779388572,13.4659405455));
#4339=IFCCARTESIANPOINT((3.5623730957E-5,2.98325994529,13.4176861218));
#4340=IFCCARTESIANPOINT((9.247311828E-5,0.));
#4341=IFCCARTESIANPOINT((3.5623730956E-5,2.78781844494,13.3682199671));
#4342=IFCCARTESIANPOINT((3.5623730956E-5,2.59166519541,13.3176228633));
#4343=IFCCARTESIANPOINT((9.3548387097E-5,0.));
#4344=IFCCARTESIANPOINT((3.5623730955E-5,2.39485103591,13.2659755927));
#4345=IFCCARTESIANPOINT((3.5623730955E-5,2.19742680568,13.2133589372));
#4346=IFCCARTESIANPOINT((9.4623655914E-5,0.));
#4347=IFCCARTESIANPOINT((3.5623730954E-5,1.99944334394,13.159853679));
#4348=IFCCARTESIANPOINT((3.5623730954E-5,1.80095148992,13.1055406001));
#4349=IFCCARTESIANPOINT((9.5698924731E-5,0.));
#4350=IFCCARTESIANPOINT((3.5623730953E-5,1.60200208284,13.0505004825));
#4351=IFCCARTESIANPOINT((3.5623730953E-5,1.40264596194,12.9948141084));
#4352=IFCCARTESIANPOINT((9.6774193548E-5,0.));
#4353=IFCCARTESIANPOINT((3.5623730953E-5,1.20293396645,12.9385622599));
#4354=IFCCARTESIANPOINT((3.5623730953E-5,1.00291693559,12.8818257188));
#4355=IFCCARTESIANPOINT((9.7849462366E-5,0.));
#4356=IFCCARTESIANPOINT((3.5623730952E-5,0.80264570858,12.8246852675));
#4357=IFCCARTESIANPOINT((3.5623730952E-5,0.60217112467,12.7672216879));
#4358=IFCCARTESIANPOINT((9.8924731183E-5,0.));
#4359=IFCCARTESIANPOINT((3.5623730952E-5,0.40154402307,12.709515762));
#4360=IFCCARTESIANPOINT((3.5623730952E-5,0.200815243014,12.651648272));
#4361=IFCCARTESIANPOINT((0.0001,0.));
#4362=IFCCARTESIANPOINT((0.0001,2.5E-5));
#4363=IFCCARTESIANPOINT((3.00003562373,3.5623730964E-5,12.5937));
#4364=IFCCARTESIANPOINT((0.0001,5.E-5));
#4365=IFCCARTESIANPOINT((6.0000356237,3.5623730981E-5,12.5937));
#4366=IFCCARTESIANPOINT((0.0001,7.5E-5));
#4367=IFCCARTESIANPOINT((9.0000356237,3.5623730998E-5,12.5937));
#4368=IFCCARTESIANPOINT((0.0001,0.0001));
#4369=IFCCARTESIANPOINT((12.0000356237,0.200815243014,12.651648272));
#4370=IFCCARTESIANPOINT((9.8924731183E-5,0.0001));
#4371=IFCCARTESIANPOINT((12.0000356237,0.40154402307,12.709515762));
#4372=IFCCARTESIANPOINT((12.0000356237,0.60217112467,12.7672216879));
#4373=IFCCARTESIANPOINT((9.7849462366E-5,0.0001));
#4374=IFCCARTESIANPOINT((12.0000356237,0.80264570858,12.8246852675));
#4375=IFCCARTESIANPOINT((12.0000356237,1.00291693559,12.8818257188));
#4376=IFCCARTESIANPOINT((9.6774193548E-5,0.0001));
#4377=IFCCARTESIANPOINT((12.0000356237,1.20293396645,12.9385622599));
#4378=IFCCARTESIANPOINT((12.0000356237,1.40264596194,12.9948141084));
#4379=IFCCARTESIANPOINT((9.5698924731E-5,0.0001));
#4380=IFCCARTESIANPOINT((12.0000356237,1.60200208284,13.0505004825));
#4381=IFCCARTESIANPOINT((12.0000356237,1.80095148992,13.1055406001));
#4382=IFCCARTESIANPOINT((9.4623655914E-5,0.0001));
#4383=IFCCARTESIANPOINT((12.0000356237,1.99944334394,13.159853679));
#4384=IFCCARTESIANPOINT((12.0000356237,2.19742680568,13.2133589372));
#4385=IFCCARTESIANPOINT((9.3548387097E-5,0.0001));
#4386=IFCCARTESIANPOINT((12.0000356237,2.39485103591,13.2659755927));
#4387=IFCCARTESIANPOINT((12.0000356237,2.59166519541,13.3176228633));
#4388=IFCCARTESIANPOINT((9.247311828E-5,0.0001));
#4389=IFCCARTESIANPOINT((12.0000356237,2.78781844494,13.3682199671));
#4390=IFCCARTESIANPOINT((12.0000356237,2.98325994529,13.4176861218));
#4391=IFCCARTESIANPOINT((9.1397849462E-5,0.0001));
#4392=IFCCARTESIANPOINT((12.0000356237,3.1779388572,13.4659405455));
#4393=IFCCARTESIANPOINT((12.0000356237,3.3718043415,13.5129024561));
#4394=IFCCARTESIANPOINT((9.0322580645E-5,0.0001));
#4395=IFCCARTESIANPOINT((12.0000356237,3.5648055589,13.5584910715));
#4396=IFCCARTESIANPOINT((12.0000356237,3.7568916702,13.6026256097));
#4397=IFCCARTESIANPOINT((8.9247311828E-5,0.0001));
#4398=IFCCARTESIANPOINT((12.0000356237,3.9480118361,13.6452252886));
#4399=IFCCARTESIANPOINT((12.0000356237,4.1381152175,13.686209326));
#4400=IFCCARTESIANPOINT((8.8172043011E-5,0.0001));
#4401=IFCCARTESIANPOINT((12.0000356237,4.3271509751,13.72549694));
#4402=IFCCARTESIANPOINT((12.0000356237,4.5150682697,13.7630073485));
#4403=IFCCARTESIANPOINT((8.7096774194E-5,0.0001));
#4404=IFCCARTESIANPOINT((12.0000356237,4.7018162621,13.7986597694));
#4405=IFCCARTESIANPOINT((12.0000356237,4.887344113,13.8323734206));
#4406=IFCCARTESIANPOINT((8.6021505376E-5,0.0001));
#4407=IFCCARTESIANPOINT((12.0000356237,5.0716009831,13.8640675201));
#4408=IFCCARTESIANPOINT((12.0000356237,5.2545360334,13.8936612857));
#4409=IFCCARTESIANPOINT((8.4946236559E-5,0.0001));
#4410=IFCCARTESIANPOINT((12.0000356237,5.4360984245,13.9210739355));
#4411=IFCCARTESIANPOINT((12.0000356237,5.6162373172,13.9462246874));
#4412=IFCCARTESIANPOINT((8.3870967742E-5,0.0001));
#4413=IFCCARTESIANPOINT((12.0000356237,5.7949018723,13.9690327592));
#4414=IFCCARTESIANPOINT((12.0000356237,5.9720412505,13.9894173689));
#4415=IFCCARTESIANPOINT((8.2795698925E-5,0.0001));
#4416=IFCCARTESIANPOINT((12.0000356237,6.1476046127,14.0072977345));
#4417=IFCCARTESIANPOINT((12.0000356237,6.3215411196,14.0225930739));
#4418=IFCCARTESIANPOINT((8.1720430108E-5,0.0001));
#4419=IFCCARTESIANPOINT((12.0000356237,6.493799932,14.035222605));
#4420=IFCCARTESIANPOINT((12.0000356237,6.6643302106,14.0451055457));
#4421=IFCCARTESIANPOINT((8.064516129E-5,0.0001));
#4422=IFCCARTESIANPOINT((12.0000356237,6.8330811162,14.052161114));
#4423=IFCCARTESIANPOINT((12.0000356237,7.0000018097,14.0563085278));
#4424=IFCCARTESIANPOINT((7.9569892473E-5,0.0001));
#4425=IFCCARTESIANPOINT((12.0000356237,7.1650414517,14.057467005));
#4426=IFCCARTESIANPOINT((12.0000356237,7.328149203,14.0555557636));
#4427=IFCCARTESIANPOINT((7.8494623656E-5,0.0001));
#4428=IFCCARTESIANPOINT((12.0000356237,7.4892742244,14.0504940215));
#4429=IFCCARTESIANPOINT((12.0000356237,7.6483656767,14.0422009966));
#4430=IFCCARTESIANPOINT((7.7419354839E-5,0.0001));
#4431=IFCCARTESIANPOINT((12.0000356237,7.8053727207,14.0305959068));
#4432=IFCCARTESIANPOINT((12.0000356237,7.9602445171,14.0155979701));
#4433=IFCCARTESIANPOINT((7.6344086022E-5,0.0001));
#4434=IFCCARTESIANPOINT((12.0000356237,8.1129302267,13.9971264045));
#4435=IFCCARTESIANPOINT((12.0000356237,8.2633790102,13.9751004278));
#4436=IFCCARTESIANPOINT((7.5268817204E-5,0.0001));
#4437=IFCCARTESIANPOINT((12.0000356237,8.4115400285,13.9494392579));
#4438=IFCCARTESIANPOINT((12.0000356237,8.5573624423,13.9200621129));
#4439=IFCCARTESIANPOINT((7.4193548387E-5,0.0001));
#4440=IFCCARTESIANPOINT((12.0000356237,8.7007954124,13.8868882106));
#4441=IFCCARTESIANPOINT((12.0000356237,8.8417880995,13.8498367689));
#4442=IFCCARTESIANPOINT((7.311827957E-5,0.0001));
#4443=IFCCARTESIANPOINT((12.0000356237,8.9802896645,13.8088270059));
#4444=IFCCARTESIANPOINT((12.0000356237,9.116249268,13.7637781394));
#4445=IFCCARTESIANPOINT((7.2043010753E-5,0.0001));
#4446=IFCCARTESIANPOINT((12.0000356237,9.249616071,13.7146093873));
#4447=IFCCARTESIANPOINT((12.0000356237,9.3803392341,13.6612399677));
#4448=IFCCARTESIANPOINT((7.0967741935E-5,0.0001));
#4449=IFCCARTESIANPOINT((12.0000356237,9.5083679181,13.6035890983));
#4450=IFCCARTESIANPOINT((12.0000356237,9.6336512837,13.5415759972));
#4451=IFCCARTESIANPOINT((6.9892473118E-5,0.0001));
#4452=IFCCARTESIANPOINT((12.0000356237,9.7561384919,13.4751198823));
#4453=IFCCARTESIANPOINT((12.0000356237,9.8757787032,13.4041399715));
#4454=IFCCARTESIANPOINT((6.8817204301E-5,0.0001));
#4455=IFCCARTESIANPOINT((12.0000356237,9.9925210786,13.3285554827));
#4456=IFCCARTESIANPOINT((12.0000356237,10.1063147788,13.248285634));
#4457=IFCCARTESIANPOINT((6.7741935484E-5,0.0001));
#4458=IFCCARTESIANPOINT((12.0000356237,10.2171089645,13.1632496431));
#4459=IFCCARTESIANPOINT((12.0000356237,10.3248527965,13.073366728));
#4460=IFCCARTESIANPOINT((6.6666666667E-5,0.0001));
#4461=IFCCARTESIANPOINT((12.0000356237,10.4294954356,12.9785561067));
#4462=IFCCARTESIANPOINT((12.0000356237,10.5309860426,12.8787369971));
#4463=IFCCARTESIANPOINT((6.5591397849E-5,0.0001));
#4464=IFCCARTESIANPOINT((12.0000356237,10.6292737782,12.7738286171));
#4465=IFCCARTESIANPOINT((12.0000356237,10.7243078032,12.6637501847));
#4466=IFCCARTESIANPOINT((6.4516129032E-5,0.0001));
#4467=IFCCARTESIANPOINT((12.0000356237,10.8160377701,12.548423571));
#4468=IFCCARTESIANPOINT((12.0000356237,10.9044339812,12.4278820681));
#4469=IFCCARTESIANPOINT((6.3440860215E-5,0.0001));
#4470=IFCCARTESIANPOINT((12.0000356237,10.9894944862,12.3023086868));
#4471=IFCCARTESIANPOINT((12.0000356237,11.0712192851,12.171896961));
#4472=IFCCARTESIANPOINT((6.2365591398E-5,0.0001));
#4473=IFCCARTESIANPOINT((12.0000356237,11.1496083779,12.0368404243));
#4474=IFCCARTESIANPOINT((12.0000356237,11.2246617647,11.8973326106));
#4475=IFCCARTESIANPOINT((6.1290322581E-5,0.0001));
#4476=IFCCARTESIANPOINT((12.0000356237,11.2963794454,11.7535670537));
#4477=IFCCARTESIANPOINT((12.0000356237,11.36476142,11.6057372873));
#4478=IFCCARTESIANPOINT((6.0215053763E-5,0.0001));
#4479=IFCCARTESIANPOINT((12.0000356237,11.4298076885,11.4540368452));
#4480=IFCCARTESIANPOINT((12.0000356237,11.4915182509,11.2986592612));
#4481=IFCCARTESIANPOINT((5.9139784946E-5,0.0001));
#4482=IFCCARTESIANPOINT((12.0000356237,11.5498931073,11.1397980692));
#4483=IFCCARTESIANPOINT((12.0000356237,11.6049322576,10.9776468028));
#4484=IFCCARTESIANPOINT((5.8064516129E-5,0.0001));
#4485=IFCCARTESIANPOINT((12.0000356237,11.6566357018,10.8123989958));
#4486=IFCCARTESIANPOINT((12.0000356237,11.70500344,10.6442481821));
#4487=IFCCARTESIANPOINT((5.6989247312E-5,0.0001));
#4488=IFCCARTESIANPOINT((12.0000356237,11.750035472,10.4733878955));
#4489=IFCCARTESIANPOINT((12.0000356237,11.791731798,10.3000116696));
#4490=IFCCARTESIANPOINT((5.5913978495E-5,0.0001));
#4491=IFCCARTESIANPOINT((12.0000356237,11.8300924179,10.1243130384));
#4492=IFCCARTESIANPOINT((12.0000356237,11.8651173317,9.9464855355));
#4493=IFCCARTESIANPOINT((5.4838709677E-5,0.0001));
#4494=IFCCARTESIANPOINT((12.0000356237,11.8968065395,9.7667226948));
#4495=IFCCARTESIANPOINT((12.0000356237,11.9251600411,9.58521805));
#4496=IFCCARTESIANPOINT((5.376344086E-5,0.0001));
#4497=IFCCARTESIANPOINT((12.0000356237,11.9501778367,9.402165135));
#4498=IFCCARTESIANPOINT((12.0000356237,11.9718599262,9.2177574835));
#4499=IFCCARTESIANPOINT((5.2688172043E-5,0.0001));
#4500=IFCCARTESIANPOINT((12.0000356237,11.9902063096,9.0321886292));
#4501=IFCCARTESIANPOINT((12.0000356237,12.005216987,8.8456521061));
#4502=IFCCARTESIANPOINT((5.1612903226E-5,0.0001));
#4503=IFCCARTESIANPOINT((12.0000356237,12.0168919583,8.6583414478));
#4504=IFCCARTESIANPOINT((12.0000356237,12.0252312235,8.4704501882));
#4505=IFCCARTESIANPOINT((5.0537634409E-5,0.0001));
#4506=IFCCARTESIANPOINT((12.0000356237,12.0302347826,8.282171861));
#4507=IFCCARTESIANPOINT((12.0000356237,12.0319026356,8.0937));
#4508=IFCCARTESIANPOINT((4.9462365591E-5,0.0001));
#4509=IFCCARTESIANPOINT((12.0000356237,12.0302347826,7.905228139));
#4510=IFCCARTESIANPOINT((12.0000356237,12.0252312235,7.7169498118));
#4511=IFCCARTESIANPOINT((4.8387096774E-5,0.0001));
#4512=IFCCARTESIANPOINT((12.0000356237,12.0168919583,7.5290585522));
#4513=IFCCARTESIANPOINT((12.0000356237,12.005216987,7.3417478939));
#4514=IFCCARTESIANPOINT((4.7311827957E-5,0.0001));
#4515=IFCCARTESIANPOINT((12.0000356237,11.9902063096,7.1552113708));
#4516=IFCCARTESIANPOINT((12.0000356237,11.9718599262,6.9696425165));
#4517=IFCCARTESIANPOINT((4.623655914E-5,0.0001));
#4518=IFCCARTESIANPOINT((12.0000356237,11.9501778367,6.785234865));
#4519=IFCCARTESIANPOINT((12.0000356237,11.9251600411,6.60218195));
#4520=IFCCARTESIANPOINT((4.5161290323E-5,0.0001));
#4521=IFCCARTESIANPOINT((12.0000356237,11.8968065395,6.4206773052));
#4522=IFCCARTESIANPOINT((12.0000356237,11.8651173317,6.2409144645));
#4523=IFCCARTESIANPOINT((4.4086021505E-5,0.0001));
#4524=IFCCARTESIANPOINT((12.0000356237,11.8300924179,6.0630869616));
#4525=IFCCARTESIANPOINT((12.0000356237,11.791731798,5.8873883304));
#4526=IFCCARTESIANPOINT((4.3010752688E-5,0.0001));
#4527=IFCCARTESIANPOINT((12.0000356237,11.750035472,5.7140121045));
#4528=IFCCARTESIANPOINT((12.0000356237,11.70500344,5.5431518179));
#4529=IFCCARTESIANPOINT((4.1935483871E-5,0.0001));
#4530=IFCCARTESIANPOINT((12.0000356237,11.6566357018,5.3750010042));
#4531=IFCCARTESIANPOINT((12.0000356237,11.6049322576,5.2097531972));
#4532=IFCCARTESIANPOINT((4.0860215054E-5,0.0001));
#4533=IFCCARTESIANPOINT((12.0000356237,11.5498931073,5.0476019308));
#4534=IFCCARTESIANPOINT((12.0000356237,11.4915182509,4.8887407388));
#4535=IFCCARTESIANPOINT((3.9784946237E-5,0.0001));
#4536=IFCCARTESIANPOINT((12.0000356237,11.4298076885,4.7333631548));
#4537=IFCCARTESIANPOINT((12.0000356237,11.36476142,4.5816627127));
#4538=IFCCARTESIANPOINT((3.8709677419E-5,0.0001));
#4539=IFCCARTESIANPOINT((12.0000356237,11.2963794454,4.4338329463));
#4540=IFCCARTESIANPOINT((12.0000356237,11.2246617647,4.2900673894));
#4541=IFCCARTESIANPOINT((3.7634408602E-5,0.0001));
#4542=IFCCARTESIANPOINT((12.0000356237,11.1496083779,4.1505595757));
#4543=IFCCARTESIANPOINT((12.0000356237,11.0712192851,4.015503039));
#4544=IFCCARTESIANPOINT((3.6559139785E-5,0.0001));
#4545=IFCCARTESIANPOINT((12.0000356237,10.9894944862,3.8850913132));
#4546=IFCCARTESIANPOINT((12.0000356237,10.9044339812,3.7595179319));
#4547=IFCCARTESIANPOINT((3.5483870968E-5,0.0001));
#4548=IFCCARTESIANPOINT((12.0000356237,10.8160377701,3.638976429));
#4549=IFCCARTESIANPOINT((12.0000356237,10.7243078032,3.5236498153));
#4550=IFCCARTESIANPOINT((3.4408602151E-5,0.0001));
#4551=IFCCARTESIANPOINT((12.0000356237,10.6292737782,3.4135713829));
#4552=IFCCARTESIANPOINT((12.0000356237,10.5309860426,3.3086630029));
#4553=IFCCARTESIANPOINT((3.3333333333E-5,0.0001));
#4554=IFCCARTESIANPOINT((12.0000356237,10.4294954356,3.2088438933));
#4555=IFCCARTESIANPOINT((12.0000356237,10.3248527965,3.114033272));
#4556=IFCCARTESIANPOINT((3.2258064516E-5,0.0001));
#4557=IFCCARTESIANPOINT((12.0000356237,10.2171089645,3.02415035694));
#4558=IFCCARTESIANPOINT((12.0000356237,10.1063147788,2.93911436605));
#4559=IFCCARTESIANPOINT((3.1182795699E-5,0.0001));
#4560=IFCCARTESIANPOINT((12.0000356237,9.9925210786,2.85884451726));
#4561=IFCCARTESIANPOINT((12.0000356237,9.8757787032,2.78326002849));
#4562=IFCCARTESIANPOINT((3.0107526882E-5,0.0001));
#4563=IFCCARTESIANPOINT((12.0000356237,9.7561384919,2.71228011768));
#4564=IFCCARTESIANPOINT((12.0000356237,9.6336512837,2.64582400277));
#4565=IFCCARTESIANPOINT((2.9032258065E-5,0.0001));
#4566=IFCCARTESIANPOINT((12.0000356237,9.5083679181,2.58381090167));
#4567=IFCCARTESIANPOINT((12.0000356237,9.3803392341,2.52616003232));
#4568=IFCCARTESIANPOINT((2.7956989247E-5,0.0001));
#4569=IFCCARTESIANPOINT((12.0000356237,9.249616071,2.47279061266));
#4570=IFCCARTESIANPOINT((12.0000356237,9.116249268,2.42362186061));
#4571=IFCCARTESIANPOINT((2.688172043E-5,0.0001));
#4572=IFCCARTESIANPOINT((12.0000356237,8.9802896645,2.3785729941));
#4573=IFCCARTESIANPOINT((12.0000356237,8.8417880995,2.33756323106));
#4574=IFCCARTESIANPOINT((2.5806451613E-5,0.0001));
#4575=IFCCARTESIANPOINT((12.0000356237,8.7007954124,2.30051178942));
#4576=IFCCARTESIANPOINT((12.0000356237,8.5573624423,2.26733788712));
#4577=IFCCARTESIANPOINT((2.4731182796E-5,0.0001));
#4578=IFCCARTESIANPOINT((12.0000356237,8.4115400285,2.23796074209));
#4579=IFCCARTESIANPOINT((12.0000356237,8.2633790102,2.21229957225));
#4580=IFCCARTESIANPOINT((2.3655913978E-5,0.0001));
#4581=IFCCARTESIANPOINT((12.0000356237,8.1129302267,2.19027359553));
#4582=IFCCARTESIANPOINT((12.0000356237,7.9602445171,2.17180202987));
#4583=IFCCARTESIANPOINT((2.2580645161E-5,0.0001));
#4584=IFCCARTESIANPOINT((12.0000356237,7.8053727207,2.1568040932));
#4585=IFCCARTESIANPOINT((12.0000356237,7.6483656767,2.14519900344));
#4586=IFCCARTESIANPOINT((2.1505376344E-5,0.0001));
#4587=IFCCARTESIANPOINT((12.0000356237,7.4892742244,2.13690597853));
#4588=IFCCARTESIANPOINT((12.0000356237,7.328149203,2.13184423641));
#4589=IFCCARTESIANPOINT((2.0430107527E-5,0.0001));
#4590=IFCCARTESIANPOINT((12.0000356237,7.1650414517,2.12993299498));
#4591=IFCCARTESIANPOINT((12.0000356237,7.0000018097,2.1310914722));
#4592=IFCCARTESIANPOINT((1.935483871E-5,0.0001));
#4593=IFCCARTESIANPOINT((12.0000356237,6.8330811162,2.13523888599));
#4594=IFCCARTESIANPOINT((12.0000356237,6.6643302106,2.14229445428));
#4595=IFCCARTESIANPOINT((1.8279569892E-5,0.0001));
#4596=IFCCARTESIANPOINT((12.0000356237,6.493799932,2.152177395));
#4597=IFCCARTESIANPOINT((12.0000356237,6.3215411196,2.16480692608));
#4598=IFCCARTESIANPOINT((1.7204301075E-5,0.0001));
#4599=IFCCARTESIANPOINT((12.0000356237,6.1476046127,2.18010226546));
#4600=IFCCARTESIANPOINT((12.0000356237,5.9720412505,2.19798263105));
#4601=IFCCARTESIANPOINT((1.6129032258E-5,0.0001));
#4602=IFCCARTESIANPOINT((12.0000356237,5.7949018723,2.2183672408));
#4603=IFCCARTESIANPOINT((12.0000356237,5.6162373172,2.24117531263));
#4604=IFCCARTESIANPOINT((1.5053763441E-5,0.0001));
#4605=IFCCARTESIANPOINT((12.0000356237,5.4360984245,2.26632606448));
#4606=IFCCARTESIANPOINT((12.0000356237,5.2545360334,2.29373871427));
#4607=IFCCARTESIANPOINT((1.3978494624E-5,0.0001));
#4608=IFCCARTESIANPOINT((12.0000356237,5.0716009831,2.32333247994));
#4609=IFCCARTESIANPOINT((12.0000356237,4.887344113,2.35502657942));
#4610=IFCCARTESIANPOINT((1.2903225806E-5,0.0001));
#4611=IFCCARTESIANPOINT((12.0000356237,4.7018162621,2.38874023063));
#4612=IFCCARTESIANPOINT((12.0000356237,4.5150682697,2.4243926515));
#4613=IFCCARTESIANPOINT((1.1827956989E-5,0.0001));
#4614=IFCCARTESIANPOINT((12.0000356237,4.3271509751,2.46190305998));
#4615=IFCCARTESIANPOINT((12.0000356237,4.1381152175,2.50119067398));
#4616=IFCCARTESIANPOINT((1.0752688172E-5,0.0001));
#4617=IFCCARTESIANPOINT((12.0000356237,3.9480118361,2.54217471144));
#4618=IFCCARTESIANPOINT((12.0000356237,3.7568916702,2.58477439029));
#4619=IFCCARTESIANPOINT((9.677419355E-6,0.0001));
#4620=IFCCARTESIANPOINT((12.0000356237,3.5648055589,2.62890892846));
#4621=IFCCARTESIANPOINT((12.0000356237,3.3718043415,2.67449754388));
#4622=IFCCARTESIANPOINT((8.602150538E-6,0.0001));
#4623=IFCCARTESIANPOINT((12.0000356237,3.1779388572,2.72145945448));
#4624=IFCCARTESIANPOINT((12.0000356237,2.98325994529,2.76971387819));
#4625=IFCCARTESIANPOINT((7.52688172E-6,0.0001));
#4626=IFCCARTESIANPOINT((12.0000356237,2.78781844494,2.81918003295));
#4627=IFCCARTESIANPOINT((12.0000356237,2.59166519541,2.86977713667));
#4628=IFCCARTESIANPOINT((6.451612903E-6,0.0001));
#4629=IFCCARTESIANPOINT((12.0000356237,2.39485103591,2.9214244073));
#4630=IFCCARTESIANPOINT((12.0000356237,2.19742680568,2.97404106276));
#4631=IFCCARTESIANPOINT((5.376344086E-6,0.0001));
#4632=IFCCARTESIANPOINT((12.0000356237,1.99944334394,3.02754632099));
#4633=IFCCARTESIANPOINT((12.0000356237,1.80095148992,3.08185939991));
#4634=IFCCARTESIANPOINT((4.301075269E-6,0.0001));
#4635=IFCCARTESIANPOINT((12.0000356237,1.60200208284,3.13689951746));
#4636=IFCCARTESIANPOINT((12.0000356237,1.40264596194,3.1925858916));
#4637=IFCCARTESIANPOINT((3.225806452E-6,0.0001));
#4638=IFCCARTESIANPOINT((12.0000356237,1.20293396645,3.2488377401));
#4639=IFCCARTESIANPOINT((12.0000356237,1.00291693559,3.3055742812));
#4640=IFCCARTESIANPOINT((2.1505376344E-6,0.0001));
#4641=IFCCARTESIANPOINT((12.0000356237,0.80264570858,3.3627147325));
#4642=IFCCARTESIANPOINT((12.0000356237,0.60217112467,3.4201783121));
#4643=IFCCARTESIANPOINT((1.0752688172E-6,0.0001));
#4644=IFCCARTESIANPOINT((12.0000356237,0.40154402307,3.477884238));
#4645=IFCCARTESIANPOINT((12.0000356237,0.200815243014,3.535751728));
#4646=IFCCARTESIANPOINT((0.,0.0001));
#4647=IFCCARTESIANPOINT((0.,7.5E-5));
#4648=IFCCARTESIANPOINT((9.0000356237,3.5623730998E-5,3.5937));
#4649=IFCCARTESIANPOINT((0.,5.E-5));
#4650=IFCCARTESIANPOINT((6.0000356237,3.5623730981E-5,3.5937));
#4651=IFCCARTESIANPOINT((0.,2.5E-5));
#4652=IFCCARTESIANPOINT((3.00003562373,3.5623730964E-5,3.5937));
#4653=IFCCARTESIANPOINT((4.4786249426E-5,7.5E-5));
#4654=IFCCARTESIANPOINT((9.0000356237,11.875052763,6.2950837693));
#4655=IFCCARTESIANPOINT((8.9971602324,11.8869509812,6.3625029991));
#4656=IFCCARTESIANPOINT((8.9885340585,11.8982262049,6.4292452509));
#4657=IFCCARTESIANPOINT((4.5382894554E-5,7.4784345652E-5));
#4658=IFCCARTESIANPOINT((8.974157102,11.9088974968,6.4952906192));
#4659=IFCCARTESIANPOINT((8.9540293628,11.9189836542,6.5606201431));
#4660=IFCCARTESIANPOINT((8.9281508411,11.928503209,6.6252157784));
#4661=IFCCARTESIANPOINT((8.8965215367,11.9374744278,6.6890603702));
#4662=IFCCARTESIANPOINT((8.8591414497,11.9459153118,6.7521376252));
#4663=IFCCARTESIANPOINT((4.6321931908E-5,7.346645797E-5));
#4664=IFCCARTESIANPOINT((8.8160105801,11.9538435968,6.8144320849));
#4665=IFCCARTESIANPOINT((8.7671289279,11.9612767534,6.8759290994));
#4666=IFCCARTESIANPOINT((8.7124964931,11.9682319866,6.9366148007));
#4667=IFCCARTESIANPOINT((4.6852131605E-5,7.2100647099E-5));
#4668=IFCCARTESIANPOINT((8.6521132756,11.9747262361,6.9964760778));
#4669=IFCCARTESIANPOINT((8.5859792756,11.9807761765,7.0555005507));
#4670=IFCCARTESIANPOINT((4.7153827211E-5,7.1090080194E-5));
#4671=IFCCARTESIANPOINT((8.530845247,11.9851607537,7.1005670746));
#4672=IFCCARTESIANPOINT((8.4768479456,11.988976063,7.141587705));
#4673=IFCCARTESIANPOINT((4.7387940154E-5,7.0169990038E-5));
#4674=IFCCARTESIANPOINT((8.4204344284,11.9925340407,7.1815647773));
#4675=IFCCARTESIANPOINT((8.3616046954,11.995847284,7.2204927423));
#4676=IFCCARTESIANPOINT((4.7609449824E-5,6.9169359357E-5));
#4677=IFCCARTESIANPOINT((8.3003587466,11.9989280462,7.2583664529));
#4678=IFCCARTESIANPOINT((8.2366965821,12.001788237,7.2951811437));
#4679=IFCCARTESIANPOINT((4.781835622E-5,6.808818815E-5));
#4680=IFCCARTESIANPOINT((8.1706182018,12.0044394223,7.3309324108));
#4681=IFCCARTESIANPOINT((8.1021236057,12.0068928244,7.365616192));
#4682=IFCCARTESIANPOINT((4.8014659342E-5,6.6926476417E-5));
#4683=IFCCARTESIANPOINT((8.0312127938,12.0091593219,7.3992287475));
#4684=IFCCARTESIANPOINT((7.9578857662,12.0112494495,7.4317666418));
#4685=IFCCARTESIANPOINT((4.8198359189E-5,6.5684224158E-5));
#4686=IFCCARTESIANPOINT((7.8821425227,12.0131733984,7.4632267256));
#4687=IFCCARTESIANPOINT((7.8039830635,12.014941016,7.4936061185));
#4688=IFCCARTESIANPOINT((4.8359733547E-5,6.4441861327E-5));
#4689=IFCCARTESIANPOINT((7.733058983,12.0163783196,7.5195096028));
#4690=IFCCARTESIANPOINT((4.8425250717E-5,6.3890865926E-5));
#4691=IFCCARTESIANPOINT((7.6669395348,12.0175937271,7.542375644));
#4692=IFCCARTESIANPOINT((4.848792557E-5,6.3334400945E-5));
#4693=IFCCARTESIANPOINT((7.6001637371,12.0187100474,7.5642575757));
#4694=IFCCARTESIANPOINT((7.53273159,12.0197334479,7.5851540633));
#4695=IFCCARTESIANPOINT((4.8604748326E-5,6.2205062248E-5));
#4696=IFCCARTESIANPOINT((7.4646430934,12.0206698163,7.6050638973));
#4697=IFCCARTESIANPOINT((7.3958982474,12.0215247606,7.6239859847));
#4698=IFCCARTESIANPOINT((4.8710201813E-5,6.1053845235E-5));
#4699=IFCCARTESIANPOINT((7.3264970519,12.0223036091,7.6419193403));
#4700=IFCCARTESIANPOINT((7.256439507,12.0230114104,7.6588630784));
#4701=IFCCARTESIANPOINT((4.8804286033E-5,5.9880749907E-5));
#4702=IFCCARTESIANPOINT((7.1857256126,12.0236529334,7.6748164053));
#4703=IFCCARTESIANPOINT((7.1143553688,12.0242326674,7.6897786117));
#4704=IFCCARTESIANPOINT((4.8887000984E-5,5.8685776264E-5));
#4705=IFCCARTESIANPOINT((7.0423287755,12.0247548218,7.7037490659));
#4706=IFCCARTESIANPOINT((6.9696458327,12.0252233267,7.7167272077));
#4707=IFCCARTESIANPOINT((4.8958346667E-5,5.7468924306E-5));
#4708=IFCCARTESIANPOINT((6.8963065405,12.025641832,7.7287125417));
#4709=IFCCARTESIANPOINT((6.8223108988,12.0260137082,7.7397046322));
#4710=IFCCARTESIANPOINT((4.9018323083E-5,5.6230194033E-5));
#4711=IFCCARTESIANPOINT((6.7476589077,12.0263420462,7.7497030981));
#4712=IFCCARTESIANPOINT((6.6723505672,12.026629657,7.7587076077));
#4713=IFCCARTESIANPOINT((4.906693023E-5,5.4969585445E-5));
#4714=IFCCARTESIANPOINT((6.5963858771,12.026879072,7.7667178748));
#4715=IFCCARTESIANPOINT((6.5197648377,12.0270925429,7.7737336546));
#4716=IFCCARTESIANPOINT((4.9104168109E-5,5.3687098542E-5));
#4717=IFCCARTESIANPOINT((6.4424874487,12.0272720416,7.7797547404));
#4718=IFCCARTESIANPOINT((6.3645537104,12.0274192605,7.7847809603));
#4719=IFCCARTESIANPOINT((4.913003672E-5,5.2382733323E-5));
#4720=IFCCARTESIANPOINT((6.2859636225,12.0275356123,7.7888121751));
#4721=IFCCARTESIANPOINT((6.2067171853,12.0276222298,7.7918482755));
#4722=IFCCARTESIANPOINT((4.9144536063E-5,5.105648979E-5));
#4723=IFCCARTESIANPOINT((6.1268143985,12.0276799662,7.793889181));
#4724=IFCCARTESIANPOINT((6.0462552623,12.0277093952,7.794934838));
#4725=IFCCARTESIANPOINT((4.9147666127E-5,4.9708545862E-5));
#4726=IFCCARTESIANPOINT((5.9650611272,12.0277108104,7.7949852153));
#4727=IFCCARTESIANPOINT((5.884108578,12.0276842514,7.7940412129));
#4728=IFCCARTESIANPOINT((4.9139436191E-5,4.8364985579E-5));
#4729=IFCCARTESIANPOINT((5.8038338932,12.027629469,7.7921034135));
#4730=IFCCARTESIANPOINT((5.7242370729,12.0275459194,7.7891718676));
#4731=IFCCARTESIANPOINT((4.9119853612E-5,4.7044020777E-5));
#4732=IFCCARTESIANPOINT((5.645318117,12.02743278,7.785246653));
#4733=IFCCARTESIANPOINT((5.5670770256,12.0272889493,7.7803278754));
#4734=IFCCARTESIANPOINT((4.9088918391E-5,4.5745651458E-5));
#4735=IFCCARTESIANPOINT((5.4895137986,12.0271130468,7.7744156707));
#4736=IFCCARTESIANPOINT((5.4126284362,12.0269034133,7.7675102068));
#4737=IFCCARTESIANPOINT((4.9046630526E-5,4.446987762E-5));
#4738=IFCCARTESIANPOINT((5.3364209381,12.0266581107,7.7596116862));
#4739=IFCCARTESIANPOINT((5.2608913046,12.0263749219,7.7507203488));
#4740=IFCCARTESIANPOINT((4.8992990018E-5,4.3216699264E-5));
#4741=IFCCARTESIANPOINT((5.1860395355,12.0260513511,7.7408364757));
#4742=IFCCARTESIANPOINT((5.1118656308,12.0256846234,7.7299603927));
#4743=IFCCARTESIANPOINT((4.8927996868E-5,4.1986116391E-5));
#4744=IFCCARTESIANPOINT((5.0383695906,12.0252716852,7.7180924746));
#4745=IFCCARTESIANPOINT((4.9655514149,12.0248092041,7.7052331498));
#4746=IFCCARTESIANPOINT((4.8851651074E-5,4.0778128999E-5));
#4747=IFCCARTESIANPOINT((4.8934111036,12.0242935687,7.6913829058));
#4748=IFCCARTESIANPOINT((4.8219486568,12.0237208885,7.6765422942));
#4749=IFCCARTESIANPOINT((4.8763952637E-5,3.959273709E-5));
#4750=IFCCARTESIANPOINT((4.7511640745,12.0230869947,7.660711937));
#4751=IFCCARTESIANPOINT((4.6810573566,12.022387439,7.6438925328));
#4752=IFCCARTESIANPOINT((4.8664901557E-5,3.8429940662E-5));
#4753=IFCCARTESIANPOINT((4.6116285032,12.0216174947,7.6260848637));
#4754=IFCCARTESIANPOINT((4.5428775142,12.0207721559,7.6072898027));
#4755=IFCCARTESIANPOINT((4.8554497835E-5,3.7289739716E-5));
#4756=IFCCARTESIANPOINT((4.4748043897,12.0198461381,7.5875083211));
#4757=IFCCARTESIANPOINT((4.4074091296,12.0188338777,7.5667414966));
#4758=IFCCARTESIANPOINT((4.8432741469E-5,3.6172134253E-5));
#4759=IFCCARTESIANPOINT((4.340691734,12.0177295323,7.544990522));
#4760=IFCCARTESIANPOINT((4.8367606045E-5,3.5621804827E-5));
#4761=IFCCARTESIANPOINT((4.2746522029,12.0165269807,7.522256714));
#4762=IFCCARTESIANPOINT((4.2092905362,12.0152198227,7.4985415228));
#4763=IFCCARTESIANPOINT((4.8214495346E-5,3.4433555924E-5));
#4764=IFCCARTESIANPOINT((4.1320623346,12.0135073878,7.4688520243));
#4765=IFCCARTESIANPOINT((4.0553942883,12.0115994876,7.4373763433));
#4766=IFCCARTESIANPOINT((4.8030730627E-5,3.3176287115E-5));
#4767=IFCCARTESIANPOINT((3.9811900776,12.0095260447,7.4048244979));
#4768=IFCCARTESIANPOINT((3.9094497025,12.0072768827,7.3711996175));
#4769=IFCCARTESIANPOINT((4.7834384681E-5,3.200114616E-5));
#4770=IFCCARTESIANPOINT((3.8401731629,12.0048414825,7.336505117));
#4771=IFCCARTESIANPOINT((3.773360459,12.0022089825,7.3007447138));
#4772=IFCCARTESIANPOINT((4.7625457507E-5,3.0908133058E-5));
#4773=IFCCARTESIANPOINT((3.7090115906,11.9993681788,7.2639224471));
#4774=IFCCARTESIANPOINT((3.6471265579,11.9963075247,7.2260426963));
#4775=IFCCARTESIANPOINT((4.7403949106E-5,2.9897247808E-5));
#4776=IFCCARTESIANPOINT((3.5877053607,11.9930151312,7.1871102009));
#4777=IFCCARTESIANPOINT((4.7288476945E-5,2.9422603128E-5));
#4778=IFCCARTESIANPOINT((3.5307479991,11.9894787667,7.1471300805));
#4779=IFCCARTESIANPOINT((3.4762544731,11.9856858573,7.1061078558));
#4780=IFCCARTESIANPOINT((3.4242247827,11.9816234863,7.0640494695));
#4781=IFCCARTESIANPOINT((4.6890166779E-5,2.8017549065E-5));
#4782=IFCCARTESIANPOINT((3.3621415116,11.9760995946,7.0095800211));
#4783=IFCCARTESIANPOINT((3.2997301961,11.9696149556,6.949086672));
#4784=IFCCARTESIANPOINT((3.2432172108,11.9626653054,6.8877865979));
#4785=IFCCARTESIANPOINT((4.6354832665E-5,2.6604724433E-5));
#4786=IFCCARTESIANPOINT((3.1926025557,11.9552338457,6.8256929955));
#4787=IFCCARTESIANPOINT((4.6170949698E-5,2.6232088392E-5));
#4788=IFCCARTESIANPOINT((3.14788623077,11.9473035221,6.762819814));
#4789=IFCCARTESIANPOINT((3.10906823601,11.9388570241,6.6991817802));
#4790=IFCCARTESIANPOINT((3.07614857142,11.9298767854,6.6347944239));
#4791=IFCCARTESIANPOINT((3.04912723701,11.9203449837,6.569674104));
#4792=IFCCARTESIANPOINT((3.02800423278,11.9102435407,6.5038380339));
#4793=IFCCARTESIANPOINT((4.5210747888E-5,2.5106199458E-5));
#4794=IFCCARTESIANPOINT((3.01277955873,11.8995541221,6.4373043089));
#4795=IFCCARTESIANPOINT((3.00345321485,11.8882581377,6.3700919325));
#4796=IFCCARTESIANPOINT((3.00002520114,11.8763367411,6.3022208437));
#4797=IFCCARTESIANPOINT((3.00242719233,11.8639398879,6.2346144846));
#4798=IFCCARTESIANPOINT((3.01040610718,11.851276089,6.1683524229));
#4799=IFCCARTESIANPOINT((4.4206903608E-5,2.519925239E-5));
#4800=IFCCARTESIANPOINT((3.02394591059,11.8382581301,6.1028911918));
#4801=IFCCARTESIANPOINT((4.4010378687E-5,2.5358424823E-5));
#4802=IFCCARTESIANPOINT((3.04304660254,11.8248968899,6.0382337907));
#4803=IFCCARTESIANPOINT((3.06770818305,11.8112031599,5.9743828122));
#4804=IFCCARTESIANPOINT((3.09793065212,11.7971876446,5.911340451));
#4805=IFCCARTESIANPOINT((3.13371400973,11.7828609614,5.8491085134));
#4806=IFCCARTESIANPOINT((4.3240140065E-5,2.6458521935E-5));
#4807=IFCCARTESIANPOINT((3.1750582559,11.7682336406,5.787688426));
#4808=IFCCARTESIANPOINT((4.3051545675E-5,2.6849398057E-5));
#4809=IFCCARTESIANPOINT((3.2219633906,11.7533161253,5.7270812449));
#4810=IFCCARTESIANPOINT((3.2744294139,11.7381187717,5.6672876647));
#4811=IFCCARTESIANPOINT((3.3324563257,11.7226518487,5.6083080274));
#4812=IFCCARTESIANPOINT((4.2495279142E-5,2.8300070853E-5));
#4813=IFCCARTESIANPOINT((3.3960441261,11.7069255384,5.5501423309));
#4814=IFCCARTESIANPOINT((3.4592238322,11.6922904162,5.4975381766));
#4815=IFCCARTESIANPOINT((3.5126316706,11.680573373,5.4564213881));
#4816=IFCCARTESIANPOINT((4.2068881168E-5,2.9735090654E-5));
#4817=IFCCARTESIANPOINT((3.5682465022,11.6689478032,5.4164591351));
#4818=IFCCARTESIANPOINT((3.6260683269,11.6574277596,5.3776421043));
#4819=IFCCARTESIANPOINT((3.6860971446,11.6460269472,5.3399609012));
#4820=IFCCARTESIANPOINT((4.1703744463E-5,3.1235811098E-5));
#4821=IFCCARTESIANPOINT((3.7483329554,11.634758723,5.3034060738));
#4822=IFCCARTESIANPOINT((3.8127757594,11.6236360963,5.267968135));
#4823=IFCCARTESIANPOINT((4.1476169363E-5,3.2328249439E-5));
#4824=IFCCARTESIANPOINT((3.8794255564,11.6126717283,5.2336375851));
#4825=IFCCARTESIANPOINT((3.9482823465,11.6018779325,5.2004049332));
#4826=IFCCARTESIANPOINT((4.1261273759E-5,3.3494254216E-5));
#4827=IFCCARTESIANPOINT((4.0193461297,11.5912666745,5.1682607189));
#4828=IFCCARTESIANPOINT((4.0926169059,11.580849572,5.137195532));
#4829=IFCCARTESIANPOINT((4.1059057651E-5,3.473382543E-5));
#4830=IFCCARTESIANPOINT((4.1680946753,11.5706378949,5.1072000327));
#4831=IFCCARTESIANPOINT((4.2442654084,11.5608324142,5.0788107178));
#4832=IFCCARTESIANPOINT((4.0885998093E-5,3.5925545948E-5));
#4833=IFCCARTESIANPOINT((4.3111011375,11.5526087383,5.0553045669));
#4834=IFCCARTESIANPOINT((4.3784660068,11.5446674941,5.0328619309));
#4835=IFCCARTESIANPOINT((4.0738945679E-5,3.7052703272E-5));
#4836=IFCCARTESIANPOINT((4.4463600164,11.5370173602,5.0114747697));
#4837=IFCCARTESIANPOINT((4.5147831663,11.5296666303,4.9911352904));
#4838=IFCCARTESIANPOINT((4.0605228326E-5,3.8197498606E-5));
#4839=IFCCARTESIANPOINT((4.5837354565,11.5226232136,4.9718359616));
#4840=IFCCARTESIANPOINT((4.653216887,11.5158946341,4.9535695276));
#4841=IFCCARTESIANPOINT((4.0484846034E-5,3.935993195E-5));
#4842=IFCCARTESIANPOINT((4.7232274578,11.5094880315,4.9363290225));
#4843=IFCCARTESIANPOINT((4.7937671689,11.5034101603,4.9201077825));
#4844=IFCCARTESIANPOINT((4.0377798803E-5,4.0540003305E-5));
#4845=IFCCARTESIANPOINT((4.8648360203,11.4976673906,4.9048994584));
#4846=IFCCARTESIANPOINT((4.936434012,11.4922657074,4.8906980268));
#4847=IFCCARTESIANPOINT((4.0284086633E-5,4.1737712669E-5));
#4848=IFCCARTESIANPOINT((5.008561144,11.4872107112,4.877497801));
#4849=IFCCARTESIANPOINT((5.0812174162,11.4825076176,4.865293441));
#4850=IFCCARTESIANPOINT((4.0203709525E-5,4.2953060042E-5));
#4851=IFCCARTESIANPOINT((5.1544028288,11.4781612573,4.854079963));
#4852=IFCCARTESIANPOINT((5.2281173817,11.4741760765,4.8438527481));
#4853=IFCCARTESIANPOINT((4.0136667477E-5,4.4186045426E-5));
#4854=IFCCARTESIANPOINT((5.3023610749,11.4705561363,4.83460755));
#4855=IFCCARTESIANPOINT((5.3771339084,11.4673051135,4.8263405028));
#4856=IFCCARTESIANPOINT((4.008296049E-5,4.543666882E-5));
#4857=IFCCARTESIANPOINT((5.4524358821,11.4644262996,4.819048127));
#4858=IFCCARTESIANPOINT((5.5282669962,11.4619226016,4.812727336));
#4859=IFCCARTESIANPOINT((4.0042588565E-5,4.6704930224E-5));
#4860=IFCCARTESIANPOINT((5.6046272506,11.4597965418,4.8073754411));
#4861=IFCCARTESIANPOINT((5.6815166452,11.4580502575,4.8029901559));
#4862=IFCCARTESIANPOINT((4.0015551701E-5,4.7990829637E-5));
#4863=IFCCARTESIANPOINT((5.7589351802,11.4566855014,4.7995696004));
#4864=IFCCARTESIANPOINT((5.8368828555,11.4557036413,4.7971123039));
#4865=IFCCARTESIANPOINT((4.0001849897E-5,4.9294367061E-5));
#4866=IFCCARTESIANPOINT((5.915359671,11.4551056605,4.7956172076));
#4867=IFCCARTESIANPOINT((5.9943656269,11.454892157,4.7950836664));
#4868=IFCCARTESIANPOINT((4.0001489595E-5,5.0615192379E-5));
#4869=IFCCARTESIANPOINT((6.0738587092,11.4550640878,4.7955133072));
#4870=IFCCARTESIANPOINT((6.1529194896,11.4556223249,4.796908928));
#4871=IFCCARTESIANPOINT((4.0014517046E-5,5.1928258339E-5));
#4872=IFCCARTESIANPOINT((6.2314266245,11.4565662796,4.7992710648));
#4873=IFCCARTESIANPOINT((6.3093801137,11.4578950026,4.8026007401));
#4874=IFCCARTESIANPOINT((4.0040940588E-5,5.3222869448E-5));
#4875=IFCCARTESIANPOINT((6.3867799575,11.4596071561,4.8068993924));
#4876=IFCCARTESIANPOINT((6.4636261556,11.4617010142,4.8121688738));
#4877=IFCCARTESIANPOINT((4.0080760221E-5,5.4499025704E-5));
#4878=IFCCARTESIANPOINT((6.5399187082,11.4641744625,4.8184114464));
#4879=IFCCARTESIANPOINT((6.6156576153,11.4670249984,4.8256297792));
#4880=IFCCARTESIANPOINT((4.0133975945E-5,5.5756727108E-5));
#4881=IFCCARTESIANPOINT((6.6908428767,11.4702497311,4.8338269428));
#4882=IFCCARTESIANPOINT((6.7654744927,11.4738453814,4.8430064049));
#4883=IFCCARTESIANPOINT((4.020058776E-5,5.6995973661E-5));
#4884=IFCCARTESIANPOINT((6.839552463,11.4778082817,4.8531720239));
#4885=IFCCARTESIANPOINT((6.9130767878,11.4821343761,4.8643280425));
#4886=IFCCARTESIANPOINT((4.0280595665E-5,5.8216765361E-5));
#4887=IFCCARTESIANPOINT((6.9860474671,11.4868192207,4.8764790803));
#4888=IFCCARTESIANPOINT((7.0584645008,11.491857983,4.8896301255));
#4889=IFCCARTESIANPOINT((4.0373999661E-5,5.941910221E-5));
#4890=IFCCARTESIANPOINT((7.1303278889,11.4972454422,4.9037865269));
#4891=IFCCARTESIANPOINT((7.2016376315,11.5029759893,4.9189539836));
#4892=IFCCARTESIANPOINT((4.0480799749E-5,6.0602984206E-5));
#4893=IFCCARTESIANPOINT((7.2723937285,11.5090436271,4.9351385353));
#4894=IFCCARTESIANPOINT((7.3425961799,11.5154419698,4.9523465517));
#4895=IFCCARTESIANPOINT((4.0600995927E-5,6.1768411351E-5));
#4896=IFCCARTESIANPOINT((7.4122449858,11.5221642435,4.9705847205));
#4897=IFCCARTESIANPOINT((7.4813401462,11.529203286,4.9898600352));
#4898=IFCCARTESIANPOINT((4.0734588196E-5,6.2915383643E-5));
#4899=IFCCARTESIANPOINT((7.5498816609,11.5365515468,5.0101797825));
#4900=IFCCARTESIANPOINT((7.6178695302,11.5442010869,5.0315515284));
#4901=IFCCARTESIANPOINT((4.0881576556E-5,6.4043901084E-5));
#4902=IFCCARTESIANPOINT((7.6853037538,11.5521435793,5.053983104));
#4903=IFCCARTESIANPOINT((4.0960094269E-5,6.4601239235E-5));
#4904=IFCCARTESIANPOINT((7.7521843319,11.5603703085,5.0774825902));
#4905=IFCCARTESIANPOINT((7.8228998605,11.569449345,5.103737576));
#4906=IFCCARTESIANPOINT((4.1147510351E-5,6.5826362797E-5));
#4907=IFCCARTESIANPOINT((7.8991991594,11.5797193301,5.1338535805));
#4908=IFCCARTESIANPOINT((7.9732329425,11.5901909951,5.1650306565));
#4909=IFCCARTESIANPOINT((4.1356856202E-5,6.7041379885E-5));
#4910=IFCCARTESIANPOINT((8.0450012099,11.6008531352,5.1972780788));
#4911=IFCCARTESIANPOINT((8.1145039616,11.6116942048,5.2306051872));
#4912=IFCCARTESIANPOINT((4.1578758013E-5,6.8180879781E-5));
#4913=IFCCARTESIANPOINT((8.1817411975,11.6227023168,5.2650213669));
#4914=IFCCARTESIANPOINT((8.2467129176,11.6338652434,5.3005360286));
#4915=IFCCARTESIANPOINT((4.1813215782E-5,6.9244862486E-5));
#4916=IFCCARTESIANPOINT((8.3094191221,11.6451704154,5.3371585874));
#4917=IFCCARTESIANPOINT((8.3698598107,11.6566049224,5.3748984419));
#4918=IFCCARTESIANPOINT((8.4280349837,11.6681555132,5.413764952));
#4919=IFCCARTESIANPOINT((4.2188444861E-5,7.0699241809E-5));
#4920=IFCCARTESIANPOINT((8.4839446408,11.6798085951,5.4537674167));
#4921=IFCCARTESIANPOINT((8.5375887823,11.6915502345,5.4949150509));
#4922=IFCCARTESIANPOINT((8.5923463088,11.7041636741,5.5401055513));
#4923=IFCCARTESIANPOINT((4.2648969795E-5,7.214523107E-5));
#4924=IFCCARTESIANPOINT((8.6574633522,11.7200997834,5.5987486971));
#4925=IFCCARTESIANPOINT((8.7169180439,11.7357597967,5.6581734635));
#4926=IFCCARTESIANPOINT((8.7707103841,11.7511342361,5.7183791321));
#4927=IFCCARTESIANPOINT((4.3214294253E-5,7.3490039574E-5));
#4928=IFCCARTESIANPOINT((8.8188403727,11.7662135493,5.7793646563));
#4929=IFCCARTESIANPOINT((8.8613080096,11.78098811,5.8411286542));
#4930=IFCCARTESIANPOINT((8.898113295,11.7954482176,5.9036694002));
#4931=IFCCARTESIANPOINT((8.9292562288,11.8095840973,5.966984818));
#4932=IFCCARTESIANPOINT((4.3988558515E-5,7.4622509894E-5));
#4933=IFCCARTESIANPOINT((8.954736811,11.8233859,6.0310724726));
#4934=IFCCARTESIANPOINT((8.9745550416,11.8368437026,6.0959295626));
#4935=IFCCARTESIANPOINT((8.9887109205,11.8499475074,6.1615529124));
#4936=IFCCARTESIANPOINT((8.9972044479,11.8626872428,6.2279389639));
#4937=IFCCARTESIANPOINT((0.,0.,9.144));
#4938=IFCCARTESIANPOINT((14.1421,14.1421,37.2623));
#4939=IFCCARTESIANPOINT((3.4459577733E-5,3.4459577728E-5,7.1220887038E-5));
#4940=IFCDIRECTION((0.,0.,1.));
#4941=IFCDIRECTION((1.,0.,0.));
#4942=IFCDIRECTION((0.,1.));
#4943=IFCSITE('006_i1V1D0tAKEh_$vljQt',#5005,'Site 1','Site 1',$,#4061,
$,'Site 1',.ELEMENT.,$,$,0.,$,#4996);
#4944=IFCRELDEFINESBYPROPERTIES('1RL1EewHTsROrPcNSlBI56',#5005,
'Project',$,(#4994),#4954);
#4945=IFCRELDEFINESBYPROPERTIES('3bjMWmv_Cp6LPxmaCnPGwW',#5005,'Site',$,
(#4943),#4955);
#4946=IFCRELDEFINESBYPROPERTIES('2zOmgYUoQmhp9sZMdCZF7s',#5005,
'Pset_BuildingCommon',$,(#19),#4956);
#4947=IFCRELDEFINESBYPROPERTIES('3k0v4EdaEvPD8NSrRFB2MT',#5005,
'ArchBuilding',$,(#19),#4957);
#4948=IFCRELDEFINESBYPROPERTIES('3uIgchz4n8nDCCMgq1BBql',#5005,
'ObjectIdentity',$,(#19),#4958);
#4949=IFCRELDEFINESBYPROPERTIES('1CCmZ_1eEDpfMO62t1jkZj',#5005,
'ObjectPostalAddress',$,(#19),#4959);
#4950=IFCRELDEFINESBYPROPERTIES('24I$F7L8gfSwdAGDvfkVEu',#5005,
'Pset_BuildingStoreyCommon',$,(#18),#4960);
#4951=IFCRELDEFINESBYPROPERTIES('1K1DvjfvPzx3hE5w5ZHK0q',#5005,
'ArchFloor',$,(#18),#4961);
#4952=IFCRELDEFINESBYPROPERTIES('0lrK04aZ0v6OfwsyTXPzZq',#5005,'Floor',
$,(#18),#4962);
#4953=IFCRELDEFINESBYPROPERTIES('1fbDyx4oFQKKacC_b9xURq',#5005,
'StructuralFloorCommon',$,(#18),#4963);
#4954=IFCPROPERTYSET('2rcLYSOKrF02Xv$qFtEF4e',#5005,'Project',$,(#4964));
#4955=IFCPROPERTYSET('2Qswsgonw96KTM1$UoUklF',#5005,'Site',$,(#4965,#4966));
#4956=IFCPROPERTYSET('2Ct1rxyLMs3vgB_jB90hMr',#5005,
'Pset_BuildingCommon',$,(#4967));
#4957=IFCPROPERTYSET('3OmKLTEF7bgCV0$1Gr7ajZ',#5005,'ArchBuilding',$,(#4968,
#4969));
#4958=IFCPROPERTYSET('1HjNcqn7ak9HvLTK8j8w1d',#5005,'ObjectIdentity',$,
(#4970));
#4959=IFCPROPERTYSET('306EzywKpRfIeis4axlC1d',#5005,
'ObjectPostalAddress',$,(#4971,#4972,#4973,#4974));
#4960=IFCPROPERTYSET('381uKge8G33qXIbHr84hB4',#5005,
'Pset_BuildingStoreyCommon',$,(#4975,#4976,#4977,#4978));
#4961=IFCPROPERTYSET('0GZmVQOAmJ6zMOk6j3uleN',#5005,'ArchFloor',$,(#4979,
#4980,#4981,#4982,#4983,#4984,#4985,#4986,#4987,#4988));
#4962=IFCPROPERTYSET('0A3C_vQUNRQZfA6f5qtBlE',#5005,'Floor',$,(#4989,#4990,
#4991));
#4963=IFCPROPERTYSET('0izo3etGuCpkjROydkcZbT',#5005,
'StructuralFloorCommon',$,(#4992));
#4964=IFCPROPERTYSINGLEVALUE('ProjectName',$,IFCTEXT(
'BuildingTemplate_US'),$);
#4965=IFCPROPERTYSINGLEVALUE('SiteName',$,IFCTEXT('Site 1'),$);
#4966=IFCPROPERTYSINGLEVALUE('BuildingHeightLimit',$,
IFCLENGTHMEASURE(0.),#5141);
#4967=IFCPROPERTYSINGLEVALUE('YearOfConstruction',$,IFCLABEL('2006'),$);
#4968=IFCPROPERTYSINGLEVALUE('BuildingName',$,IFCTEXT('Bldg 1'),$);
#4969=IFCPROPERTYSINGLEVALUE('YearConstructed',$,IFCTEXT('2006'),$);
#4970=IFCPROPERTYSINGLEVALUE('Description',$,IFCTEXT('3 Story Building'),
$);
#4971=IFCPROPERTYSINGLEVALUE('Address1',$,IFCTEXT(
'203 Rickenhouse Drive'),$);
#4972=IFCPROPERTYSINGLEVALUE('City',$,IFCTEXT('Madison'),$);
#4973=IFCPROPERTYSINGLEVALUE('PostalCode',$,IFCTEXT('35789'),$);
#4974=IFCPROPERTYSINGLEVALUE('Country',$,IFCTEXT('US'),$);
#4975=IFCPROPERTYSINGLEVALUE('GrossAreaPlanned',$,IFCAREAMEASURE(22296729.6),
#5142);
#4976=IFCPROPERTYSINGLEVALUE('GrossPlannedArea',$,IFCAREAMEASURE(22296729.6),
$);
#4977=IFCPROPERTYSINGLEVALUE('NetAreaPlanned',$,IFCAREAMEASURE(22296729.6),
#5142);
#4978=IFCPROPERTYSINGLEVALUE('NetPlannedArea',$,IFCAREAMEASURE(22296729.6),
$);
#4979=IFCPROPERTYSINGLEVALUE('FloorName',$,IFCTEXT('Floor 3'),$);
#4980=IFCPROPERTYSINGLEVALUE('FinishedFloorElevation',$,IFCREAL(9.144),
$);
#4981=IFCPROPERTYSINGLEVALUE('Description',$,IFCTEXT(
'Administrative and employee lounge'),$);
#4982=IFCPROPERTYSINGLEVALUE('ApproxLength',$,IFCLENGTHMEASURE(6096.),#5141);
#4983=IFCPROPERTYSINGLEVALUE('ApproxWidth',$,IFCLENGTHMEASURE(3657.6),#5141);
#4984=IFCPROPERTYSINGLEVALUE('TypicalFloor',$,IFCBOOLEAN(.F.),$);
#4985=IFCPROPERTYSINGLEVALUE('TypicalFloorHeight',$,IFCLENGTHMEASURE(0.),
#5141);
#4986=IFCPROPERTYSINGLEVALUE('TypicalFloorBaseElevation',$,
IFCLENGTHMEASURE(0.),#5141);
#4987=IFCPROPERTYSINGLEVALUE('ACSRotation',$,IFCTEXT('0.00000000'),$);
#4988=IFCPROPERTYSINGLEVALUE('Annotation',$,IFCBOOLEAN(.T.),$);
#4989=IFCPROPERTYSINGLEVALUE('GrossArea',$,IFCAREAMEASURE(22296729.6),#5142);
#4990=IFCPROPERTYSINGLEVALUE('NetArea',$,IFCAREAMEASURE(22296729.6),#5142);
#4991=IFCPROPERTYSINGLEVALUE('OccupancyType',$,IFCTEXT('Commercial'),$);
#4992=IFCPROPERTYSINGLEVALUE('issplicefloor',$,IFCBOOLEAN(.F.),$);
#4993=IFCUNITASSIGNMENT((#5141,#5142,#5143,#5144,#5145,#5146,#5147,#5148,
#5149,#5150,#5151,#5152,#5153,#5154,#5155,#5156,#5157,#5158,#5159,#5160,
#5161,#5162,#5138,#5164,#5165,#5166,#5167,#5168,#5169,#5007,#5008,#5009,
#5010,#5011,#5012,#5013,#5014,#5015,#5016,#5017,#5018,#5019,#5020,#5021,
#5022,#5023,#5024,#5025,#5026,#5027,#5028,#5029,#5030,#5031,#5032,#5033,
#5034,#5035,#5036,#5037,#5038,#5039,#5040,#5041,#5042,#5043,#5044,#5045,
#5046,#5047,#5048,#5049,#5050,#5006));
#4994=IFCPROJECT('1JmBUThP19Z8wED88xGwCF',#5005,'BuildingTemplate_US',
'BuildingTemplate_US',$,'BuildingTemplate_US',$,(#13),#4993);
#4995=IFCPOSTALADDRESS($,$,$,$,('Address 1','Address 2'),$,'City/Town',
'State/Region','Postal Code','Country');
#4996=IFCPOSTALADDRESS($,$,$,'Site 1',$,$,$,$,$,$);
#4997=IFCPOSTALADDRESS($,$,$,$,('203 Rickenhouse Drive'),$,'Madison',$,
'35789','US');
#4998=IFCTELECOMADDRESS($,$,$,('Phone'),$,$,('E-Mail'),$,$);
#4999=IFCACTORROLE(.SUPPLIER.,$,$);
#5000=IFCPERSON($,'Last Name','First Name',$,$,$,(#4999),(#4998));
#5001=IFCPERSONANDORGANIZATION(#5000,#5003,$);
#5002=IFCORGANIZATION($,'Bentley Systems Inc.',$,$,$);
#5003=IFCORGANIZATION($,'Organization Name',$,$,(#4995));
#5004=IFCAPPLICATION(#5002,'***********','OpenBuildings Designer','ABD');
#5005=IFCOWNERHISTORY(#5001,#5004,$,$,0,$,$,1583849924);
#5006=IFCMONETARYUNIT('USD');
#5007=IFCDERIVEDUNIT((#5051,#5052),.ACCELERATIONUNIT.,
'(METRE)/(SECOND^2)');
#5008=IFCDERIVEDUNIT((#5053,#5054),.ANGULARVELOCITYUNIT.,
'(DEGREE)/(SECOND)');
#5009=IFCDERIVEDUNIT((#5055,#5056),.CURVATUREUNIT.,'(DEGREE)/(METRE)');
#5010=IFCDERIVEDUNIT((#5057,#5058),.DYNAMICVISCOSITYUNIT.,
'(PASCAL)(SECOND)');
#5011=IFCDERIVEDUNIT((#5059,#5060),.HEATFLUXDENSITYUNIT.,
'(WATT)/(METRE^2)');
#5012=IFCDERIVEDUNIT((#5061,#5062),.HEATINGVALUEUNIT.,'(JOULE)/(GRAM)');
#5013=IFCDERIVEDUNIT((#5063),.INTEGERCOUNTRATEUNIT.,'1/(SECOND)');
#5014=IFCDERIVEDUNIT((#5064,#5065),.IONCONCENTRATIONUNIT.,
'(GRAM)/(CUBIC_METRE)');
#5015=IFCDERIVEDUNIT((#5066,#5067),.ISOTHERMALMOISTURECAPACITYUNIT.,
'(CUBIC_METRE)/(GRAM)');
#5016=IFCDERIVEDUNIT((#5068,#5069),.KINEMATICVISCOSITYUNIT.,
'(SQUARE_METRE)/(SECOND)');
#5017=IFCDERIVEDUNIT((#5070,#5071),.LINEARFORCEUNIT.,'(NEWTON)/(METRE)');
#5018=IFCDERIVEDUNIT((#5072,#5073),.LINEARMOMENTUNIT.,
'(NEWTON)/(METRE)');
#5019=IFCDERIVEDUNIT((#5074,#5075),.LINEARSTIFFNESSUNIT.,
'(NEWTON)/(METRE)');
#5020=IFCDERIVEDUNIT((#5076,#5077),.LINEARVELOCITYUNIT.,
'(METRE)/(SECOND)');
#5021=IFCDERIVEDUNIT((#5078,#5079),.LUMINOUSINTENSITYDISTRIBUTIONUNIT.,
'(CANDELA)/(LUMEN)');
#5022=IFCDERIVEDUNIT((#5080,#5081),.MASSDENSITYUNIT.,
'(GRAM)/(CUBIC_METRE)');
#5023=IFCDERIVEDUNIT((#5082,#5083),.MASSFLOWRATEUNIT.,'(GRAM)/(SECOND)');
#5024=IFCDERIVEDUNIT((#5084,#5085),.MASSPERLENGTHUNIT.,'(GRAM)/(METRE)');
#5025=IFCDERIVEDUNIT((#5086,#5087),.MODULUSOFELASTICITYUNIT.,
'(NEWTON)/(SQUARE_METRE)');
#5026=IFCDERIVEDUNIT((#5088,#5089),
 .MODULUSOFLINEARSUBGRADEREACTIONUNIT.,'(NEWTON)/(METRE^2)');
#5027=IFCDERIVEDUNIT((#5090,#5091),
 .MODULUSOFROTATIONALSUBGRADEREACTIONUNIT.,'(NEWTON)(METRE)');
#5028=IFCDERIVEDUNIT((#5092,#5093),.MODULUSOFSUBGRADEREACTIONUNIT.,
'(NEWTON)/(CUBIC_METRE)');
#5029=IFCDERIVEDUNIT((#5094,#5095),.MOISTUREDIFFUSIVITYUNIT.,
'(CUBIC_METRE)/(SECOND)');
#5030=IFCDERIVEDUNIT((#5096,#5097),.MOLECULARWEIGHTUNIT.,
'(GRAM)/(MOLE)');
#5031=IFCDERIVEDUNIT((#5098,#5099),.PLANARFORCEUNIT.,
'(NEWTON)/(METRE^2)');
#5032=IFCDERIVEDUNIT((#5100),.ROTATIONALFREQUENCYUNIT.,'1/(SECOND)');
#5033=IFCDERIVEDUNIT((#5101,#5102),.ROTATIONALMASSUNIT.,
'(GRAM)(METRE^2)');
#5034=IFCDERIVEDUNIT((#5103,#5104,#5105),.ROTATIONALSTIFFNESSUNIT.,
'(NEWTON)(METRE)/(DEGREE)');
#5035=IFCDERIVEDUNIT((#5106),.SECTIONAREAINTEGRALUNIT.,'(METRE^5)');
#5036=IFCDERIVEDUNIT((#5107),.SECTIONMODULUSUNIT.,'(METRE^3)');
#5037=IFCDERIVEDUNIT((#5108,#5109),.SHEARMODULUSUNIT.,
'(NEWTON)/(SQUARE_METRE)');
#5038=IFCDERIVEDUNIT((#5110,#5111),.SOUNDPOWERUNIT.,'(JOULE)/(SECOND)');
#5039=IFCDERIVEDUNIT((#5112,#5113,#5114),.SPECIFICHEATCAPACITYUNIT.,
'(NEWTON)/(GRAM)(KELVIN)');
#5040=IFCDERIVEDUNIT((#5115,#5116),.TEMPERATUREGRADIENTUNIT.,
'(KELVIN)/(METRE)');
#5041=IFCDERIVEDUNIT((#5117,#5118,#5119),.THERMALADMITTANCEUNIT.,
'(WATT)/(SQUARE_METRE)(KELVIN)');
#5042=IFCDERIVEDUNIT((#5120,#5121,#5122),.THERMALCONDUCTANCEUNIT.,
'(WATT)/(METRE)(KELVIN)');
#5043=IFCDERIVEDUNIT((#5123),.THERMALEXPANSIONCOEFFICIENTUNIT.,
'1/(KELVIN)');
#5044=IFCDERIVEDUNIT((#5124,#5125),.THERMALRESISTANCEUNIT.,
'(SQUARE_METRE)/(WATT)');
#5045=IFCDERIVEDUNIT((#5126,#5127,#5128),.THERMALTRANSMITTANCEUNIT.,
'(WATT)/(SQUARE_METRE)(KELVIN)');
#5046=IFCDERIVEDUNIT((#5129,#5130),.TORQUEUNIT.,'(NEWTON)(METRE)');
#5047=IFCDERIVEDUNIT((#5131,#5132),.VAPORPERMEABILITYUNIT.,
'(GRAM)/(SECOND)');
#5048=IFCDERIVEDUNIT((#5133,#5134),.VOLUMETRICFLOWRATEUNIT.,
'(CUBIC_METRE)/(SECOND)');
#5049=IFCDERIVEDUNIT((#5135),.WARPINGCONSTANTUNIT.,'(METRE^6)');
#5050=IFCDERIVEDUNIT((#5136,#5137),.WARPINGMOMENTUNIT.,
'(NEWTON)(METRE^2)');
#5051=IFCDERIVEDUNITELEMENT(#5141,1);
#5052=IFCDERIVEDUNITELEMENT(#5169,-2);
#5053=IFCDERIVEDUNITELEMENT(#5138,1);
#5054=IFCDERIVEDUNITELEMENT(#5169,-1);
#5055=IFCDERIVEDUNITELEMENT(#5138,1);
#5056=IFCDERIVEDUNITELEMENT(#5141,-1);
#5057=IFCDERIVEDUNITELEMENT(#5165,1);
#5058=IFCDERIVEDUNITELEMENT(#5169,1);
#5059=IFCDERIVEDUNITELEMENT(#5164,1);
#5060=IFCDERIVEDUNITELEMENT(#5141,-2);
#5061=IFCDERIVEDUNITELEMENT(#5153,1);
#5062=IFCDERIVEDUNITELEMENT(#5162,-1);
#5063=IFCDERIVEDUNITELEMENT(#5169,-1);
#5064=IFCDERIVEDUNITELEMENT(#5162,1);
#5065=IFCDERIVEDUNITELEMENT(#5143,-1);
#5066=IFCDERIVEDUNITELEMENT(#5143,1);
#5067=IFCDERIVEDUNITELEMENT(#5162,-1);
#5068=IFCDERIVEDUNITELEMENT(#5142,1);
#5069=IFCDERIVEDUNITELEMENT(#5169,-1);
#5070=IFCDERIVEDUNITELEMENT(#5154,1);
#5071=IFCDERIVEDUNITELEMENT(#5141,-1);
#5072=IFCDERIVEDUNITELEMENT(#5154,1);
#5073=IFCDERIVEDUNITELEMENT(#5141,-1);
#5074=IFCDERIVEDUNITELEMENT(#5154,1);
#5075=IFCDERIVEDUNITELEMENT(#5141,-1);
#5076=IFCDERIVEDUNITELEMENT(#5141,1);
#5077=IFCDERIVEDUNITELEMENT(#5169,-1);
#5078=IFCDERIVEDUNITELEMENT(#5159,1);
#5079=IFCDERIVEDUNITELEMENT(#5158,-1);
#5080=IFCDERIVEDUNITELEMENT(#5162,1);
#5081=IFCDERIVEDUNITELEMENT(#5143,-1);
#5082=IFCDERIVEDUNITELEMENT(#5162,1);
#5083=IFCDERIVEDUNITELEMENT(#5169,-1);
#5084=IFCDERIVEDUNITELEMENT(#5162,1);
#5085=IFCDERIVEDUNITELEMENT(#5141,-1);
#5086=IFCDERIVEDUNITELEMENT(#5154,1);
#5087=IFCDERIVEDUNITELEMENT(#5142,-1);
#5088=IFCDERIVEDUNITELEMENT(#5154,1);
#5089=IFCDERIVEDUNITELEMENT(#5141,-2);
#5090=IFCDERIVEDUNITELEMENT(#5154,1);
#5091=IFCDERIVEDUNITELEMENT(#5141,1);
#5092=IFCDERIVEDUNITELEMENT(#5154,1);
#5093=IFCDERIVEDUNITELEMENT(#5143,-1);
#5094=IFCDERIVEDUNITELEMENT(#5143,1);
#5095=IFCDERIVEDUNITELEMENT(#5169,-1);
#5096=IFCDERIVEDUNITELEMENT(#5162,1);
#5097=IFCDERIVEDUNITELEMENT(#5145,-1);
#5098=IFCDERIVEDUNITELEMENT(#5154,1);
#5099=IFCDERIVEDUNITELEMENT(#5141,-2);
#5100=IFCDERIVEDUNITELEMENT(#5169,-1);
#5101=IFCDERIVEDUNITELEMENT(#5162,1);
#5102=IFCDERIVEDUNITELEMENT(#5141,2);
#5103=IFCDERIVEDUNITELEMENT(#5154,1);
#5104=IFCDERIVEDUNITELEMENT(#5141,1);
#5105=IFCDERIVEDUNITELEMENT(#5138,-1);
#5106=IFCDERIVEDUNITELEMENT(#5141,5);
#5107=IFCDERIVEDUNITELEMENT(#5141,3);
#5108=IFCDERIVEDUNITELEMENT(#5154,1);
#5109=IFCDERIVEDUNITELEMENT(#5142,-1);
#5110=IFCDERIVEDUNITELEMENT(#5153,1);
#5111=IFCDERIVEDUNITELEMENT(#5169,-1);
#5112=IFCDERIVEDUNITELEMENT(#5154,1);
#5113=IFCDERIVEDUNITELEMENT(#5162,-1);
#5114=IFCDERIVEDUNITELEMENT(#5168,-1);
#5115=IFCDERIVEDUNITELEMENT(#5168,1);
#5116=IFCDERIVEDUNITELEMENT(#5141,-1);
#5117=IFCDERIVEDUNITELEMENT(#5164,1);
#5118=IFCDERIVEDUNITELEMENT(#5142,-1);
#5119=IFCDERIVEDUNITELEMENT(#5168,-1);
#5120=IFCDERIVEDUNITELEMENT(#5164,1);
#5121=IFCDERIVEDUNITELEMENT(#5141,-1);
#5122=IFCDERIVEDUNITELEMENT(#5168,-1);
#5123=IFCDERIVEDUNITELEMENT(#5168,-1);
#5124=IFCDERIVEDUNITELEMENT(#5142,1);
#5125=IFCDERIVEDUNITELEMENT(#5164,-1);
#5126=IFCDERIVEDUNITELEMENT(#5164,1);
#5127=IFCDERIVEDUNITELEMENT(#5142,-1);
#5128=IFCDERIVEDUNITELEMENT(#5168,-1);
#5129=IFCDERIVEDUNITELEMENT(#5154,1);
#5130=IFCDERIVEDUNITELEMENT(#5141,1);
#5131=IFCDERIVEDUNITELEMENT(#5162,1);
#5132=IFCDERIVEDUNITELEMENT(#5169,-1);
#5133=IFCDERIVEDUNITELEMENT(#5143,1);
#5134=IFCDERIVEDUNITELEMENT(#5169,-1);
#5135=IFCDERIVEDUNITELEMENT(#5141,6);
#5136=IFCDERIVEDUNITELEMENT(#5154,1);
#5137=IFCDERIVEDUNITELEMENT(#5141,2);
#5138=IFCCONVERSIONBASEDUNIT(#5139,.PLANEANGLEUNIT.,'degree',#5140);
#5139=IFCDIMENSIONALEXPONENTS(0,0,0,0,0,0,0);
#5140=IFCMEASUREWITHUNIT(IFCREAL(0.0174532925199433),#5163);
#5141=IFCSIUNIT(*,.LENGTHUNIT.,$,.METRE.);
#5142=IFCSIUNIT(*,.AREAUNIT.,$,.SQUARE_METRE.);
#5143=IFCSIUNIT(*,.VOLUMEUNIT.,$,.CUBIC_METRE.);
#5144=IFCSIUNIT(*,.ABSORBEDDOSEUNIT.,$,.GRAY.);
#5145=IFCSIUNIT(*,.AMOUNTOFSUBSTANCEUNIT.,$,.MOLE.);
#5146=IFCSIUNIT(*,.DOSEEQUIVALENTUNIT.,$,.SIEVERT.);
#5147=IFCSIUNIT(*,.ELECTRICCAPACITANCEUNIT.,$,.FARAD.);
#5148=IFCSIUNIT(*,.ELECTRICCHARGEUNIT.,$,.COULOMB.);
#5149=IFCSIUNIT(*,.ELECTRICCONDUCTANCEUNIT.,$,.SIEMENS.);
#5150=IFCSIUNIT(*,.ELECTRICCURRENTUNIT.,$,.AMPERE.);
#5151=IFCSIUNIT(*,.ELECTRICRESISTANCEUNIT.,$,.OHM.);
#5152=IFCSIUNIT(*,.ELECTRICVOLTAGEUNIT.,$,.VOLT.);
#5153=IFCSIUNIT(*,.ENERGYUNIT.,$,.JOULE.);
#5154=IFCSIUNIT(*,.FORCEUNIT.,$,.NEWTON.);
#5155=IFCSIUNIT(*,.FREQUENCYUNIT.,$,.HERTZ.);
#5156=IFCSIUNIT(*,.ILLUMINANCEUNIT.,$,.LUX.);
#5157=IFCSIUNIT(*,.INDUCTANCEUNIT.,$,.HENRY.);
#5158=IFCSIUNIT(*,.LUMINOUSFLUXUNIT.,$,.LUMEN.);
#5159=IFCSIUNIT(*,.LUMINOUSINTENSITYUNIT.,$,.CANDELA.);
#5160=IFCSIUNIT(*,.MAGNETICFLUXDENSITYUNIT.,$,.TESLA.);
#5161=IFCSIUNIT(*,.MAGNETICFLUXUNIT.,$,.WEBER.);
#5162=IFCSIUNIT(*,.MASSUNIT.,$,.GRAM.);
#5163=IFCSIUNIT(*,.PLANEANGLEUNIT.,$,.RADIAN.);
#5164=IFCSIUNIT(*,.POWERUNIT.,$,.WATT.);
#5165=IFCSIUNIT(*,.PRESSUREUNIT.,$,.PASCAL.);
#5166=IFCSIUNIT(*,.RADIOACTIVITYUNIT.,$,.BECQUEREL.);
#5167=IFCSIUNIT(*,.SOLIDANGLEUNIT.,$,.STERADIAN.);
#5168=IFCSIUNIT(*,.THERMODYNAMICTEMPERATUREUNIT.,$,.KELVIN.);
#5169=IFCSIUNIT(*,.TIMEUNIT.,$,.SECOND.);
ENDSEC;
END-ISO-10303-21;

ISO-10303-21;
HEADER;
/* Generated by software containing ST-Developer
 * from STEP Tools, Inc. (www.steptools.com) 
 */

FILE_DESCRIPTION(
/* description */ ('ViewDefinition [ReferenceView_V1.2]',
'ExchangeRequirement [Architecture, Structural, BuildingService]',
'Comment [Comments]',
'Comment [System: OpenBuildings Designer*********** debug build Feb  5
 2020 00:34:15]'),
/* implementation_level */ '2;1');

FILE_NAME(
/* name */ 'bspline surface.facet',
/* time_stamp */ '2020-03-05T12:31:33+03:00',
/* author */ ('First Name Last Name'),
/* organization */ ('Organization Name'),
/* preprocessor_version */ 'ST-DEVELOPER v16.13',
/* originating_system */ 'OpenBuildings Designer***********',
/* authorisation */ 'Administrator');

FILE_SCHEMA (('IFC4'));
ENDSEC;

DATA;
#10=IFCRELDEFINESBYTYPE('0yWWbT7y5ah7KlNAaQnrO3',#2834,
'*Default Exterior Wall*',$,(#21),#20);
#11=IFCRELASSOCIATESCLASSIFICATION('1X0TV8c4moj7_JQXHMcGNY',#2834,
'B2010 ! 2010 Edition UniFormat - Levels One through Three',$,(#20),#12);
#12=IFCCLASSIFICATIONREFERENCE($,'B2010','Exterior Walls',#13,$,$);
#13=IFCCLASSIFICATION('The Construction Specifications Institute (CSI)',
$,$,'2010 Edition UniFormat - Levels One through Three',$,$,$);
#14=IFCELEMENTQUANTITY('0KSv2R_$a6jLerxfR$Wk_M',#2834,
'Wall_Application',$,$,(#16,#17));
#15=IFCELEMENTQUANTITY('3cU$dMMpPy0ae7XJISsXg4',#2834,
'Qto_WallBaseQuantities',$,$,(#18,#19));
#16=IFCQUANTITYLENGTH('Width',$,$,0.3048,$);
#17=IFCQUANTITYLENGTH('Height',$,$,4.2672,$);
#18=IFCQUANTITYLENGTH('Height',$,$,4.2672,$);
#19=IFCQUANTITYLENGTH('Width',$,$,0.3048,$);
#20=IFCWALLTYPE('0NS3N_lmg12Qd8HyEBTJVt',#2834,
'*Default Exterior Wall*',$,$,(#2771,#2772,#2773,#2774,#2775,#2776,#2777,
#14,#2778),$,$,$,.NOTDEFINED.);
#21=IFCWALL('3wca1no693WBlKP7jj9bSB',#2834,'*Default Exterior Wall*',
'Default exterior wall','Wall',#2711,#65,'651*bspline surface!Default',
 .NOTDEFINED.);
#22=IFCINDEXEDPOLYGONALFACEWITHVOIDS((1,2,3,4),((5,6,7)));
#23=IFCINDEXEDPOLYGONALFACEWITHVOIDS((10,8,9,11),((12,13,14,15,16,17,18,
19,20)));
#24=IFCINDEXEDPOLYGONALFACEWITHVOIDS((1,2,3,4),((5,6,7,8)));
#25=IFCBOUNDINGBOX(#2731,11.5550375522955,11.5550375522962,5.86758330849535);
#26=IFCBOUNDINGBOX(#2733,8.25796776432799,8.85488956263439,17.3489201617776);
#27=IFCBOUNDINGBOX(#2735,8.9363759430212,8.88742154057684,13.4784);
#28=IFCBOUNDINGBOX(#2737,12.,12.0270878618007,11.887043827089);
#29=IFCBOUNDINGBOX(#2739,15.,15.0104204182341,3.11913433609218);
#30=IFCBOUNDINGBOX(#2741,15.,7.5,7.5);
#31=IFCBOUNDINGBOX(#2743,15.0000023283065,15.0000023283064,2.32830648681879E-6);
#32=IFCBOUNDINGBOX(#2745,6.54919163802099,23.0657416915475,23.0507999834516);
#33=IFCPRESENTATIONLAYERASSIGNMENT('Default',$,(#38,#39,#46,#48,#50,#52,
#54),$);
#34=IFCPRESENTATIONLAYERASSIGNMENT('Elements',$,(#40,#42,#44),$);
#35=IFCGEOMETRICREPRESENTATIONSUBCONTEXT('Body','Model',*,*,*,*,#37,1.,
 .MODEL_VIEW.,$);
#36=IFCGEOMETRICREPRESENTATIONSUBCONTEXT('Box','Model',*,*,*,*,#37,1.,
 .SKETCH_VIEW.,$);
#37=IFCGEOMETRICREPRESENTATIONCONTEXT($,'Model',3,1.E-6,#2716,#2748);
#38=IFCSHAPEREPRESENTATION(#35,'Body','Tessellation',(#2686));
#39=IFCSHAPEREPRESENTATION(#35,'Body','Tessellation',(#2687));
#40=IFCSHAPEREPRESENTATION(#35,'Body','Tessellation',(#2688));
#41=IFCSHAPEREPRESENTATION(#36,'Box','BoundingBox',(#25));
#42=IFCSHAPEREPRESENTATION(#35,'Body','Tessellation',(#2689));
#43=IFCSHAPEREPRESENTATION(#36,'Box','BoundingBox',(#26));
#44=IFCSHAPEREPRESENTATION(#35,'Body','Tessellation',(#2690));
#45=IFCSHAPEREPRESENTATION(#36,'Box','BoundingBox',(#27));
#46=IFCSHAPEREPRESENTATION(#35,'Body','Tessellation',(#2691));
#47=IFCSHAPEREPRESENTATION(#36,'Box','BoundingBox',(#28));
#48=IFCSHAPEREPRESENTATION(#35,'Body','Tessellation',(#2692));
#49=IFCSHAPEREPRESENTATION(#36,'Box','BoundingBox',(#29));
#50=IFCSHAPEREPRESENTATION(#35,'Body','Tessellation',(#2693));
#51=IFCSHAPEREPRESENTATION(#36,'Box','BoundingBox',(#30));
#52=IFCSHAPEREPRESENTATION(#35,'Body','Tessellation',(#2694));
#53=IFCSHAPEREPRESENTATION(#36,'Box','BoundingBox',(#31));
#54=IFCSHAPEREPRESENTATION(#35,'Body','Tessellation',(#2695));
#55=IFCSHAPEREPRESENTATION(#36,'Box','BoundingBox',(#32));
#56=IFCPRODUCTDEFINITIONSHAPE($,$,(#38));
#57=IFCPRODUCTDEFINITIONSHAPE($,$,(#39));
#58=IFCPRODUCTDEFINITIONSHAPE($,$,(#40,#41));
#59=IFCPRODUCTDEFINITIONSHAPE($,$,(#42,#43));
#60=IFCPRODUCTDEFINITIONSHAPE($,$,(#44,#45));
#61=IFCPRODUCTDEFINITIONSHAPE($,$,(#46,#47));
#62=IFCPRODUCTDEFINITIONSHAPE($,$,(#48,#49));
#63=IFCPRODUCTDEFINITIONSHAPE($,$,(#50,#51));
#64=IFCPRODUCTDEFINITIONSHAPE($,$,(#52,#53));
#65=IFCPRODUCTDEFINITIONSHAPE($,$,(#54,#55));
#66=IFCRELCONTAINEDINSPATIALSTRUCTURE('2aJi0CW_wcTJthPGyCEMkK',#2834,$,
$,(#69,#70,#71,#72,#73,#74,#75,#76,#77,#21),#67);
#67=IFCBUILDINGSTOREY('0rm7DUqMJhOUcs5v8t1F$L',#2834,'Floor 3',
'Administrative and employee lounge',$,#2701,$,'Floor 3',.ELEMENT.,9.144);
#68=IFCBUILDING('3_IkJInE13hR6Xwo4VKOgh',#2834,'Bldg 1',
'3 Story Building',$,#2700,$,'Bldg 1',.ELEMENT.,0.,0.,#2826);
#69=IFCBUILDINGELEMENTPROXY('3aSi40O_0G89We9uslOUWT',#2834,
'Element type: 24','636*bspline surface!Default','',#2702,#56,
'636*bspline surface!Default',.NOTDEFINED.);
#70=IFCBUILDINGELEMENTPROXY('0Aq8869pigt7uFQjBvubFh',#2834,
'Element type: 24','667*bspline surface!Default','',#2703,#57,
'667*bspline surface!Default',.NOTDEFINED.);
#71=IFCBUILDINGELEMENTPROXY('1tA$FB6IgKmuErQnyukDAc',#2834,
'Element type: 24','700*bspline surface!Default','',#2704,#58,
'700*bspline surface!Default',.NOTDEFINED.);
#72=IFCBUILDINGELEMENTPROXY('0Nhc0TstR8GhZh5RveMuCl',#2834,
'Element type: 24','752*bspline surface!Default','',#2705,#59,
'752*bspline surface!Default',.NOTDEFINED.);
#73=IFCBUILDINGELEMENTPROXY('2I0Vc5PdWui6aZ1Sc_tP24',#2834,
'Element type: 24','788*bspline surface!Default','',#2706,#60,
'788*bspline surface!Default',.NOTDEFINED.);
#74=IFCBUILDINGELEMENTPROXY('1vdpKn9biZ$o4qW2SHbk1Q',#2834,
'Element type: 24','824*bspline surface!Default','',#2707,#61,
'824*bspline surface!Default',.NOTDEFINED.);
#75=IFCBUILDINGELEMENTPROXY('1SkVtlXVM5QZmO_SLYA8R9',#2834,
'Element type: 24','1157*bspline surface!Default','',#2708,#62,
'1157*bspline surface!Default',.NOTDEFINED.);
#76=IFCBUILDINGELEMENTPROXY('0jN7$mxA0Mb1eStqg04F5j',#2834,
'Element type: 24','1204*bspline surface!Default','',#2709,#63,
'1204*bspline surface!Default',.NOTDEFINED.);
#77=IFCBUILDINGELEMENTPROXY('3Js5Z1cBieWAPNA4TNWLaA',#2834,
'Element type: 24','1214*bspline surface!Default','',#2710,#64,
'1214*bspline surface!Default',.NOTDEFINED.);
#78=IFCSTYLEDITEM(#2686,(#88),$);
#79=IFCSTYLEDITEM(#2687,(#89),$);
#80=IFCSTYLEDITEM(#2688,(#90),$);
#81=IFCSTYLEDITEM(#2689,(#90),$);
#82=IFCSTYLEDITEM(#2690,(#90),$);
#83=IFCSTYLEDITEM(#2691,(#89),$);
#84=IFCSTYLEDITEM(#2692,(#89),$);
#85=IFCSTYLEDITEM(#2693,(#91),$);
#86=IFCSTYLEDITEM(#2694,(#91),$);
#87=IFCSTYLEDITEM(#2695,(#88),$);
#88=IFCSURFACESTYLE($,.BOTH.,(#92));
#89=IFCSURFACESTYLE($,.BOTH.,(#93));
#90=IFCSURFACESTYLE($,.BOTH.,(#94));
#91=IFCSURFACESTYLE($,.BOTH.,(#95));
#92=IFCSURFACESTYLESHADING(#96,0.);
#93=IFCSURFACESTYLESHADING(#97,0.);
#94=IFCSURFACESTYLESHADING(#98,0.);
#95=IFCSURFACESTYLESHADING(#99,0.);
#96=IFCCOLOURRGB($,1.,1.,0.);
#97=IFCCOLOURRGB($,1.,1.,1.);
#98=IFCCOLOURRGB($,0.,0.,1.);
#99=IFCCOLOURRGB($,1.,0.,0.);
#100=IFCINDEXEDPOLYGONALFACE((1,2,3,4,5,6));
#101=IFCINDEXEDPOLYGONALFACE((1,2,3,4));
#102=IFCINDEXEDPOLYGONALFACE((1,2,3,4));
#103=IFCINDEXEDPOLYGONALFACE((5,1,4,6));
#104=IFCINDEXEDPOLYGONALFACE((7,5,6,8));
#105=IFCINDEXEDPOLYGONALFACE((9,7,8,10));
#106=IFCINDEXEDPOLYGONALFACE((11,9,10,12));
#107=IFCINDEXEDPOLYGONALFACE((13,11,12,14));
#108=IFCINDEXEDPOLYGONALFACE((15,13,14,16));
#109=IFCINDEXEDPOLYGONALFACE((17,15,16,18));
#110=IFCINDEXEDPOLYGONALFACE((19,17,18,20));
#111=IFCINDEXEDPOLYGONALFACE((21,19,20,22));
#112=IFCINDEXEDPOLYGONALFACE((23,21,22,24));
#113=IFCINDEXEDPOLYGONALFACE((25,23,24,26));
#114=IFCINDEXEDPOLYGONALFACE((4,3,27,28));
#115=IFCINDEXEDPOLYGONALFACE((6,4,28,29));
#116=IFCINDEXEDPOLYGONALFACE((8,6,29,30));
#117=IFCINDEXEDPOLYGONALFACE((10,8,30,31));
#118=IFCINDEXEDPOLYGONALFACE((12,10,31,32));
#119=IFCINDEXEDPOLYGONALFACE((14,12,32,33));
#120=IFCINDEXEDPOLYGONALFACE((16,14,33,34));
#121=IFCINDEXEDPOLYGONALFACE((18,16,34,35));
#122=IFCINDEXEDPOLYGONALFACE((20,18,35,36));
#123=IFCINDEXEDPOLYGONALFACE((22,20,36,37));
#124=IFCINDEXEDPOLYGONALFACE((24,22,37,38));
#125=IFCINDEXEDPOLYGONALFACE((26,24,38,39));
#126=IFCINDEXEDPOLYGONALFACE((28,27,40,41));
#127=IFCINDEXEDPOLYGONALFACE((29,28,41,42));
#128=IFCINDEXEDPOLYGONALFACE((30,29,42,43));
#129=IFCINDEXEDPOLYGONALFACE((31,30,43,44));
#130=IFCINDEXEDPOLYGONALFACE((32,31,44,45));
#131=IFCINDEXEDPOLYGONALFACE((33,32,45,46));
#132=IFCINDEXEDPOLYGONALFACE((34,33,46,47));
#133=IFCINDEXEDPOLYGONALFACE((35,34,47,48));
#134=IFCINDEXEDPOLYGONALFACE((36,35,48,49));
#135=IFCINDEXEDPOLYGONALFACE((37,36,49,50));
#136=IFCINDEXEDPOLYGONALFACE((38,37,50,51));
#137=IFCINDEXEDPOLYGONALFACE((39,38,51,52));
#138=IFCINDEXEDPOLYGONALFACE((41,40,53,54));
#139=IFCINDEXEDPOLYGONALFACE((42,41,54,55));
#140=IFCINDEXEDPOLYGONALFACE((43,42,55,56));
#141=IFCINDEXEDPOLYGONALFACE((44,43,56,57));
#142=IFCINDEXEDPOLYGONALFACE((45,44,57,58));
#143=IFCINDEXEDPOLYGONALFACE((46,45,58,59));
#144=IFCINDEXEDPOLYGONALFACE((47,46,59,60));
#145=IFCINDEXEDPOLYGONALFACE((48,47,60,61));
#146=IFCINDEXEDPOLYGONALFACE((49,48,61,62));
#147=IFCINDEXEDPOLYGONALFACE((50,49,62,63));
#148=IFCINDEXEDPOLYGONALFACE((51,50,63,64));
#149=IFCINDEXEDPOLYGONALFACE((52,51,64,65));
#150=IFCINDEXEDPOLYGONALFACE((54,53,66,67));
#151=IFCINDEXEDPOLYGONALFACE((55,54,67,68));
#152=IFCINDEXEDPOLYGONALFACE((56,55,68,69));
#153=IFCINDEXEDPOLYGONALFACE((57,56,69,70));
#154=IFCINDEXEDPOLYGONALFACE((58,57,70,71));
#155=IFCINDEXEDPOLYGONALFACE((59,58,71,72));
#156=IFCINDEXEDPOLYGONALFACE((60,59,72,73));
#157=IFCINDEXEDPOLYGONALFACE((61,60,73,74));
#158=IFCINDEXEDPOLYGONALFACE((62,61,74,75));
#159=IFCINDEXEDPOLYGONALFACE((63,62,75,76));
#160=IFCINDEXEDPOLYGONALFACE((64,63,76,77));
#161=IFCINDEXEDPOLYGONALFACE((65,64,77,78));
#162=IFCINDEXEDPOLYGONALFACE((67,66,79,80));
#163=IFCINDEXEDPOLYGONALFACE((68,67,80,81));
#164=IFCINDEXEDPOLYGONALFACE((69,68,81,82));
#165=IFCINDEXEDPOLYGONALFACE((70,69,82,83));
#166=IFCINDEXEDPOLYGONALFACE((71,70,83,84));
#167=IFCINDEXEDPOLYGONALFACE((72,71,84,85));
#168=IFCINDEXEDPOLYGONALFACE((73,72,85,86));
#169=IFCINDEXEDPOLYGONALFACE((74,73,86,87));
#170=IFCINDEXEDPOLYGONALFACE((75,74,87,88));
#171=IFCINDEXEDPOLYGONALFACE((76,75,88,89));
#172=IFCINDEXEDPOLYGONALFACE((77,76,89,90));
#173=IFCINDEXEDPOLYGONALFACE((78,77,90,91));
#174=IFCINDEXEDPOLYGONALFACE((80,79,92,93));
#175=IFCINDEXEDPOLYGONALFACE((81,80,93,94));
#176=IFCINDEXEDPOLYGONALFACE((82,81,94,95));
#177=IFCINDEXEDPOLYGONALFACE((83,82,95,96));
#178=IFCINDEXEDPOLYGONALFACE((84,83,96,97));
#179=IFCINDEXEDPOLYGONALFACE((85,84,97,98));
#180=IFCINDEXEDPOLYGONALFACE((86,85,98,99));
#181=IFCINDEXEDPOLYGONALFACE((87,86,99,100));
#182=IFCINDEXEDPOLYGONALFACE((88,87,100,101));
#183=IFCINDEXEDPOLYGONALFACE((89,88,101,102));
#184=IFCINDEXEDPOLYGONALFACE((90,89,102,103));
#185=IFCINDEXEDPOLYGONALFACE((91,90,103,104));
#186=IFCINDEXEDPOLYGONALFACE((93,92,105,106));
#187=IFCINDEXEDPOLYGONALFACE((94,93,106,107));
#188=IFCINDEXEDPOLYGONALFACE((95,94,107,108));
#189=IFCINDEXEDPOLYGONALFACE((96,95,108,109));
#190=IFCINDEXEDPOLYGONALFACE((97,96,109,110));
#191=IFCINDEXEDPOLYGONALFACE((98,97,110,111));
#192=IFCINDEXEDPOLYGONALFACE((99,98,111,112));
#193=IFCINDEXEDPOLYGONALFACE((100,99,112,113));
#194=IFCINDEXEDPOLYGONALFACE((101,100,113,114));
#195=IFCINDEXEDPOLYGONALFACE((102,101,114,115));
#196=IFCINDEXEDPOLYGONALFACE((103,102,115,116));
#197=IFCINDEXEDPOLYGONALFACE((104,103,116,117));
#198=IFCINDEXEDPOLYGONALFACE((106,105,118));
#199=IFCINDEXEDPOLYGONALFACE((107,106,118));
#200=IFCINDEXEDPOLYGONALFACE((108,107,118));
#201=IFCINDEXEDPOLYGONALFACE((109,108,118));
#202=IFCINDEXEDPOLYGONALFACE((110,109,118));
#203=IFCINDEXEDPOLYGONALFACE((111,110,118));
#204=IFCINDEXEDPOLYGONALFACE((112,111,118));
#205=IFCINDEXEDPOLYGONALFACE((113,112,118));
#206=IFCINDEXEDPOLYGONALFACE((114,113,118));
#207=IFCINDEXEDPOLYGONALFACE((115,114,118));
#208=IFCINDEXEDPOLYGONALFACE((116,115,118));
#209=IFCINDEXEDPOLYGONALFACE((117,116,118));
#210=IFCINDEXEDPOLYGONALFACE((119,25,26,120));
#211=IFCINDEXEDPOLYGONALFACE((121,119,120,122));
#212=IFCINDEXEDPOLYGONALFACE((123,121,122,124));
#213=IFCINDEXEDPOLYGONALFACE((125,123,124,126));
#214=IFCINDEXEDPOLYGONALFACE((127,125,126,128));
#215=IFCINDEXEDPOLYGONALFACE((129,127,128,130));
#216=IFCINDEXEDPOLYGONALFACE((131,129,130,132));
#217=IFCINDEXEDPOLYGONALFACE((133,131,132,134));
#218=IFCINDEXEDPOLYGONALFACE((135,133,134,136));
#219=IFCINDEXEDPOLYGONALFACE((137,135,136,138));
#220=IFCINDEXEDPOLYGONALFACE((139,137,138,140));
#221=IFCINDEXEDPOLYGONALFACE((2,139,140,3));
#222=IFCINDEXEDPOLYGONALFACE((120,26,39,141));
#223=IFCINDEXEDPOLYGONALFACE((122,120,141,142));
#224=IFCINDEXEDPOLYGONALFACE((124,122,142,143));
#225=IFCINDEXEDPOLYGONALFACE((126,124,143,144));
#226=IFCINDEXEDPOLYGONALFACE((128,126,144,145));
#227=IFCINDEXEDPOLYGONALFACE((130,128,145,146));
#228=IFCINDEXEDPOLYGONALFACE((132,130,146,147));
#229=IFCINDEXEDPOLYGONALFACE((134,132,147,148));
#230=IFCINDEXEDPOLYGONALFACE((136,134,148,149));
#231=IFCINDEXEDPOLYGONALFACE((138,136,149,150));
#232=IFCINDEXEDPOLYGONALFACE((140,138,150,151));
#233=IFCINDEXEDPOLYGONALFACE((3,140,151,27));
#234=IFCINDEXEDPOLYGONALFACE((141,39,52,152));
#235=IFCINDEXEDPOLYGONALFACE((142,141,152,153));
#236=IFCINDEXEDPOLYGONALFACE((143,142,153,154));
#237=IFCINDEXEDPOLYGONALFACE((144,143,154,155));
#238=IFCINDEXEDPOLYGONALFACE((145,144,155,156));
#239=IFCINDEXEDPOLYGONALFACE((146,145,156,157));
#240=IFCINDEXEDPOLYGONALFACE((147,146,157,158));
#241=IFCINDEXEDPOLYGONALFACE((148,147,158,159));
#242=IFCINDEXEDPOLYGONALFACE((149,148,159,160));
#243=IFCINDEXEDPOLYGONALFACE((150,149,160,161));
#244=IFCINDEXEDPOLYGONALFACE((151,150,161,162));
#245=IFCINDEXEDPOLYGONALFACE((27,151,162,40));
#246=IFCINDEXEDPOLYGONALFACE((152,52,65,163));
#247=IFCINDEXEDPOLYGONALFACE((153,152,163,164));
#248=IFCINDEXEDPOLYGONALFACE((154,153,164,165));
#249=IFCINDEXEDPOLYGONALFACE((155,154,165,166));
#250=IFCINDEXEDPOLYGONALFACE((156,155,166,167));
#251=IFCINDEXEDPOLYGONALFACE((157,156,167,168));
#252=IFCINDEXEDPOLYGONALFACE((158,157,168,169));
#253=IFCINDEXEDPOLYGONALFACE((159,158,169,170));
#254=IFCINDEXEDPOLYGONALFACE((160,159,170,171));
#255=IFCINDEXEDPOLYGONALFACE((161,160,171,172));
#256=IFCINDEXEDPOLYGONALFACE((162,161,172,173));
#257=IFCINDEXEDPOLYGONALFACE((40,162,173,53));
#258=IFCINDEXEDPOLYGONALFACE((163,65,78,174));
#259=IFCINDEXEDPOLYGONALFACE((164,163,174,175));
#260=IFCINDEXEDPOLYGONALFACE((165,164,175,176));
#261=IFCINDEXEDPOLYGONALFACE((166,165,176,177));
#262=IFCINDEXEDPOLYGONALFACE((167,166,177,178));
#263=IFCINDEXEDPOLYGONALFACE((168,167,178,179));
#264=IFCINDEXEDPOLYGONALFACE((169,168,179,180));
#265=IFCINDEXEDPOLYGONALFACE((170,169,180,181));
#266=IFCINDEXEDPOLYGONALFACE((171,170,181,182));
#267=IFCINDEXEDPOLYGONALFACE((172,171,182,183));
#268=IFCINDEXEDPOLYGONALFACE((173,172,183,184));
#269=IFCINDEXEDPOLYGONALFACE((53,173,184,66));
#270=IFCINDEXEDPOLYGONALFACE((174,78,91,185));
#271=IFCINDEXEDPOLYGONALFACE((175,174,185,186));
#272=IFCINDEXEDPOLYGONALFACE((176,175,186,187));
#273=IFCINDEXEDPOLYGONALFACE((177,176,187,188));
#274=IFCINDEXEDPOLYGONALFACE((178,177,188,189));
#275=IFCINDEXEDPOLYGONALFACE((179,178,189,190));
#276=IFCINDEXEDPOLYGONALFACE((180,179,190,191));
#277=IFCINDEXEDPOLYGONALFACE((181,180,191,192));
#278=IFCINDEXEDPOLYGONALFACE((182,181,192,193));
#279=IFCINDEXEDPOLYGONALFACE((183,182,193,194));
#280=IFCINDEXEDPOLYGONALFACE((184,183,194,195));
#281=IFCINDEXEDPOLYGONALFACE((66,184,195,79));
#282=IFCINDEXEDPOLYGONALFACE((185,91,104,196));
#283=IFCINDEXEDPOLYGONALFACE((186,185,196,197));
#284=IFCINDEXEDPOLYGONALFACE((187,186,197,198));
#285=IFCINDEXEDPOLYGONALFACE((188,187,198,199));
#286=IFCINDEXEDPOLYGONALFACE((189,188,199,200));
#287=IFCINDEXEDPOLYGONALFACE((190,189,200,201));
#288=IFCINDEXEDPOLYGONALFACE((191,190,201,202));
#289=IFCINDEXEDPOLYGONALFACE((192,191,202,203));
#290=IFCINDEXEDPOLYGONALFACE((193,192,203,204));
#291=IFCINDEXEDPOLYGONALFACE((194,193,204,205));
#292=IFCINDEXEDPOLYGONALFACE((195,194,205,206));
#293=IFCINDEXEDPOLYGONALFACE((79,195,206,92));
#294=IFCINDEXEDPOLYGONALFACE((196,104,117,207));
#295=IFCINDEXEDPOLYGONALFACE((197,196,207,208));
#296=IFCINDEXEDPOLYGONALFACE((198,197,208,209));
#297=IFCINDEXEDPOLYGONALFACE((199,198,209,210));
#298=IFCINDEXEDPOLYGONALFACE((200,199,210,211));
#299=IFCINDEXEDPOLYGONALFACE((201,200,211,212));
#300=IFCINDEXEDPOLYGONALFACE((202,201,212,213));
#301=IFCINDEXEDPOLYGONALFACE((203,202,213,214));
#302=IFCINDEXEDPOLYGONALFACE((204,203,214,215));
#303=IFCINDEXEDPOLYGONALFACE((205,204,215,216));
#304=IFCINDEXEDPOLYGONALFACE((206,205,216,217));
#305=IFCINDEXEDPOLYGONALFACE((92,206,217,105));
#306=IFCINDEXEDPOLYGONALFACE((207,117,118));
#307=IFCINDEXEDPOLYGONALFACE((208,207,118));
#308=IFCINDEXEDPOLYGONALFACE((209,208,118));
#309=IFCINDEXEDPOLYGONALFACE((210,209,118));
#310=IFCINDEXEDPOLYGONALFACE((211,210,118));
#311=IFCINDEXEDPOLYGONALFACE((212,211,118));
#312=IFCINDEXEDPOLYGONALFACE((213,212,118));
#313=IFCINDEXEDPOLYGONALFACE((214,213,118));
#314=IFCINDEXEDPOLYGONALFACE((215,214,118));
#315=IFCINDEXEDPOLYGONALFACE((216,215,118));
#316=IFCINDEXEDPOLYGONALFACE((217,216,118));
#317=IFCINDEXEDPOLYGONALFACE((105,217,118));
#318=IFCINDEXEDPOLYGONALFACE((1,2,3));
#319=IFCINDEXEDPOLYGONALFACE((4,5,6));
#320=IFCINDEXEDPOLYGONALFACE((7,8,9));
#321=IFCINDEXEDPOLYGONALFACE((10,11,12));
#322=IFCINDEXEDPOLYGONALFACE((13,14,15));
#323=IFCINDEXEDPOLYGONALFACE((16,17,18));
#324=IFCINDEXEDPOLYGONALFACE((14,1,19));
#325=IFCINDEXEDPOLYGONALFACE((19,3,17));
#326=IFCINDEXEDPOLYGONALFACE((15,19,16));
#327=IFCINDEXEDPOLYGONALFACE((20,21,22));
#328=IFCINDEXEDPOLYGONALFACE((23,24,25));
#329=IFCINDEXEDPOLYGONALFACE((26,27,13));
#330=IFCINDEXEDPOLYGONALFACE((28,15,22));
#331=IFCINDEXEDPOLYGONALFACE((29,30,28));
#332=IFCINDEXEDPOLYGONALFACE((31,25,24));
#333=IFCINDEXEDPOLYGONALFACE((32,6,33));
#334=IFCINDEXEDPOLYGONALFACE((34,35,36));
#335=IFCINDEXEDPOLYGONALFACE((37,4,32));
#336=IFCINDEXEDPOLYGONALFACE((38,39,40));
#337=IFCINDEXEDPOLYGONALFACE((40,36,41));
#338=IFCINDEXEDPOLYGONALFACE((42,33,2));
#339=IFCINDEXEDPOLYGONALFACE((36,32,42));
#340=IFCINDEXEDPOLYGONALFACE((41,42,1));
#341=IFCINDEXEDPOLYGONALFACE((43,41,14));
#342=IFCINDEXEDPOLYGONALFACE((44,40,43));
#343=IFCINDEXEDPOLYGONALFACE((45,43,13));
#344=IFCINDEXEDPOLYGONALFACE((46,47,48));
#345=IFCINDEXEDPOLYGONALFACE((49,50,51));
#346=IFCINDEXEDPOLYGONALFACE((11,10,48));
#347=IFCINDEXEDPOLYGONALFACE((10,51,52));
#348=IFCINDEXEDPOLYGONALFACE((53,48,52));
#349=IFCINDEXEDPOLYGONALFACE((53,6,5));
#350=IFCINDEXEDPOLYGONALFACE((33,6,53));
#351=IFCINDEXEDPOLYGONALFACE((54,55,56));
#352=IFCINDEXEDPOLYGONALFACE((57,52,58));
#353=IFCINDEXEDPOLYGONALFACE((8,59,60));
#354=IFCINDEXEDPOLYGONALFACE((51,9,58));
#355=IFCINDEXEDPOLYGONALFACE((9,60,61));
#356=IFCINDEXEDPOLYGONALFACE((62,58,61));
#357=IFCINDEXEDPOLYGONALFACE((2,57,62));
#358=IFCINDEXEDPOLYGONALFACE((3,62,63));
#359=IFCINDEXEDPOLYGONALFACE((64,65,63));
#360=IFCINDEXEDPOLYGONALFACE((66,67,61));
#361=IFCINDEXEDPOLYGONALFACE((17,63,65));
#362=IFCINDEXEDPOLYGONALFACE((65,18,17));
#363=IFCINDEXEDPOLYGONALFACE((60,56,66));
#364=IFCINDEXEDPOLYGONALFACE((61,60,66));
#365=IFCINDEXEDPOLYGONALFACE((64,63,61));
#366=IFCINDEXEDPOLYGONALFACE((61,67,64));
#367=IFCINDEXEDPOLYGONALFACE((63,17,3));
#368=IFCINDEXEDPOLYGONALFACE((62,3,2));
#369=IFCINDEXEDPOLYGONALFACE((61,63,62));
#370=IFCINDEXEDPOLYGONALFACE((61,58,9));
#371=IFCINDEXEDPOLYGONALFACE((58,52,51));
#372=IFCINDEXEDPOLYGONALFACE((60,9,8));
#373=IFCINDEXEDPOLYGONALFACE((58,62,57));
#374=IFCINDEXEDPOLYGONALFACE((60,59,54));
#375=IFCINDEXEDPOLYGONALFACE((56,60,54));
#376=IFCINDEXEDPOLYGONALFACE((57,2,33));
#377=IFCINDEXEDPOLYGONALFACE((53,57,33));
#378=IFCINDEXEDPOLYGONALFACE((5,68,53));
#379=IFCINDEXEDPOLYGONALFACE((52,57,53));
#380=IFCINDEXEDPOLYGONALFACE((52,48,10));
#381=IFCINDEXEDPOLYGONALFACE((48,47,69));
#382=IFCINDEXEDPOLYGONALFACE((48,69,11));
#383=IFCINDEXEDPOLYGONALFACE((10,70,71));
#384=IFCINDEXEDPOLYGONALFACE((49,51,10));
#385=IFCINDEXEDPOLYGONALFACE((10,71,49));
#386=IFCINDEXEDPOLYGONALFACE((53,68,46));
#387=IFCINDEXEDPOLYGONALFACE((48,53,46));
#388=IFCINDEXEDPOLYGONALFACE((13,27,45));
#389=IFCINDEXEDPOLYGONALFACE((43,45,72));
#390=IFCINDEXEDPOLYGONALFACE((43,72,44));
#391=IFCINDEXEDPOLYGONALFACE((14,13,43));
#392=IFCINDEXEDPOLYGONALFACE((1,14,41));
#393=IFCINDEXEDPOLYGONALFACE((42,41,36));
#394=IFCINDEXEDPOLYGONALFACE((2,1,42));
#395=IFCINDEXEDPOLYGONALFACE((41,43,40));
#396=IFCINDEXEDPOLYGONALFACE((40,44,38));
#397=IFCINDEXEDPOLYGONALFACE((35,37,32));
#398=IFCINDEXEDPOLYGONALFACE((32,36,35));
#399=IFCINDEXEDPOLYGONALFACE((39,34,36));
#400=IFCINDEXEDPOLYGONALFACE((36,40,39));
#401=IFCINDEXEDPOLYGONALFACE((33,42,32));
#402=IFCINDEXEDPOLYGONALFACE((24,73,31));
#403=IFCINDEXEDPOLYGONALFACE((25,31,29));
#404=IFCINDEXEDPOLYGONALFACE((28,25,29));
#405=IFCINDEXEDPOLYGONALFACE((22,25,28));
#406=IFCINDEXEDPOLYGONALFACE((28,30,26));
#407=IFCINDEXEDPOLYGONALFACE((13,28,26));
#408=IFCINDEXEDPOLYGONALFACE((21,23,25));
#409=IFCINDEXEDPOLYGONALFACE((25,22,21));
#410=IFCINDEXEDPOLYGONALFACE((74,20,22));
#411=IFCINDEXEDPOLYGONALFACE((22,16,74));
#412=IFCINDEXEDPOLYGONALFACE((16,22,15));
#413=IFCINDEXEDPOLYGONALFACE((17,16,19));
#414=IFCINDEXEDPOLYGONALFACE((19,15,14));
#415=IFCINDEXEDPOLYGONALFACE((18,74,16));
#416=IFCINDEXEDPOLYGONALFACE((15,28,13));
#417=IFCINDEXEDPOLYGONALFACE((12,70,10));
#418=IFCINDEXEDPOLYGONALFACE((51,50,7));
#419=IFCINDEXEDPOLYGONALFACE((9,51,7));
#420=IFCINDEXEDPOLYGONALFACE((6,32,4));
#421=IFCINDEXEDPOLYGONALFACE((3,19,1));
#422=IFCINDEXEDPOLYGONALFACE((75,76,8));
#423=IFCINDEXEDPOLYGONALFACE((77,78,70));
#424=IFCINDEXEDPOLYGONALFACE((79,80,55));
#425=IFCINDEXEDPOLYGONALFACE((76,81,59));
#426=IFCINDEXEDPOLYGONALFACE((49,71,82));
#427=IFCINDEXEDPOLYGONALFACE((83,84,50));
#428=IFCINDEXEDPOLYGONALFACE((50,49,83));
#429=IFCINDEXEDPOLYGONALFACE((78,82,71));
#430=IFCINDEXEDPOLYGONALFACE((71,70,78));
#431=IFCINDEXEDPOLYGONALFACE((82,83,49));
#432=IFCINDEXEDPOLYGONALFACE((59,8,76));
#433=IFCINDEXEDPOLYGONALFACE((54,59,81));
#434=IFCINDEXEDPOLYGONALFACE((81,79,54));
#435=IFCINDEXEDPOLYGONALFACE((55,54,79));
#436=IFCINDEXEDPOLYGONALFACE((77,70,12));
#437=IFCINDEXEDPOLYGONALFACE((12,85,77));
#438=IFCINDEXEDPOLYGONALFACE((7,50,84));
#439=IFCINDEXEDPOLYGONALFACE((84,75,7));
#440=IFCINDEXEDPOLYGONALFACE((8,7,75));
#441=IFCINDEXEDPOLYGONALFACE((86,87,88));
#442=IFCINDEXEDPOLYGONALFACE((89,90,91));
#443=IFCINDEXEDPOLYGONALFACE((92,93,94));
#444=IFCINDEXEDPOLYGONALFACE((95,96,97));
#445=IFCINDEXEDPOLYGONALFACE((98,99,100));
#446=IFCINDEXEDPOLYGONALFACE((101,102,103));
#447=IFCINDEXEDPOLYGONALFACE((104,88,99));
#448=IFCINDEXEDPOLYGONALFACE((87,103,102));
#449=IFCINDEXEDPOLYGONALFACE((88,102,105));
#450=IFCINDEXEDPOLYGONALFACE((102,101,106));
#451=IFCINDEXEDPOLYGONALFACE((105,107,108));
#452=IFCINDEXEDPOLYGONALFACE((75,84,109));
#453=IFCINDEXEDPOLYGONALFACE((99,105,110));
#454=IFCINDEXEDPOLYGONALFACE((100,110,81));
#455=IFCINDEXEDPOLYGONALFACE((79,81,110));
#456=IFCINDEXEDPOLYGONALFACE((111,112,113));
#457=IFCINDEXEDPOLYGONALFACE((114,115,116));
#458=IFCINDEXEDPOLYGONALFACE((117,91,90));
#459=IFCINDEXEDPOLYGONALFACE((118,91,115));
#460=IFCINDEXEDPOLYGONALFACE((119,115,91));
#461=IFCINDEXEDPOLYGONALFACE((120,116,115));
#462=IFCINDEXEDPOLYGONALFACE((121,116,77));
#463=IFCINDEXEDPOLYGONALFACE((78,77,116));
#464=IFCINDEXEDPOLYGONALFACE((119,122,123));
#465=IFCINDEXEDPOLYGONALFACE((112,86,104));
#466=IFCINDEXEDPOLYGONALFACE((117,113,122));
#467=IFCINDEXEDPOLYGONALFACE((113,104,98));
#468=IFCINDEXEDPOLYGONALFACE((122,98,109));
#469=IFCINDEXEDPOLYGONALFACE((82,78,120));
#470=IFCINDEXEDPOLYGONALFACE((123,109,84));
#471=IFCINDEXEDPOLYGONALFACE((124,125,126));
#472=IFCINDEXEDPOLYGONALFACE((127,128,129));
#473=IFCINDEXEDPOLYGONALFACE((130,97,131));
#474=IFCINDEXEDPOLYGONALFACE((97,129,132));
#475=IFCINDEXEDPOLYGONALFACE((128,133,134));
#476=IFCINDEXEDPOLYGONALFACE((129,134,135));
#477=IFCINDEXEDPOLYGONALFACE((131,132,136));
#478=IFCINDEXEDPOLYGONALFACE((125,124,90));
#479=IFCINDEXEDPOLYGONALFACE((111,90,124));
#480=IFCINDEXEDPOLYGONALFACE((137,136,112));
#481=IFCINDEXEDPOLYGONALFACE((132,135,138));
#482=IFCINDEXEDPOLYGONALFACE((136,138,86));
#483=IFCINDEXEDPOLYGONALFACE((139,140,141));
#484=IFCINDEXEDPOLYGONALFACE((135,142,143));
#485=IFCINDEXEDPOLYGONALFACE((144,141,140));
#486=IFCINDEXEDPOLYGONALFACE((134,94,142));
#487=IFCINDEXEDPOLYGONALFACE((94,140,145));
#488=IFCINDEXEDPOLYGONALFACE((142,145,146));
#489=IFCINDEXEDPOLYGONALFACE((138,143,87));
#490=IFCINDEXEDPOLYGONALFACE((143,146,103));
#491=IFCINDEXEDPOLYGONALFACE((145,147,148));
#492=IFCINDEXEDPOLYGONALFACE((147,145,140));
#493=IFCINDEXEDPOLYGONALFACE((149,103,146));
#494=IFCINDEXEDPOLYGONALFACE((146,148,150));
#495=IFCINDEXEDPOLYGONALFACE((146,150,149));
#496=IFCINDEXEDPOLYGONALFACE((140,139,151));
#497=IFCINDEXEDPOLYGONALFACE((140,151,147));
#498=IFCINDEXEDPOLYGONALFACE((148,146,145));
#499=IFCINDEXEDPOLYGONALFACE((103,87,143));
#500=IFCINDEXEDPOLYGONALFACE((87,86,138));
#501=IFCINDEXEDPOLYGONALFACE((146,143,142));
#502=IFCINDEXEDPOLYGONALFACE((145,142,94));
#503=IFCINDEXEDPOLYGONALFACE((142,135,134));
#504=IFCINDEXEDPOLYGONALFACE((144,140,94));
#505=IFCINDEXEDPOLYGONALFACE((94,93,144));
#506=IFCINDEXEDPOLYGONALFACE((143,138,135));
#507=IFCINDEXEDPOLYGONALFACE((141,152,153));
#508=IFCINDEXEDPOLYGONALFACE((141,153,139));
#509=IFCINDEXEDPOLYGONALFACE((86,112,136));
#510=IFCINDEXEDPOLYGONALFACE((138,136,132));
#511=IFCINDEXEDPOLYGONALFACE((112,111,137));
#512=IFCINDEXEDPOLYGONALFACE((124,137,111));
#513=IFCINDEXEDPOLYGONALFACE((90,89,154));
#514=IFCINDEXEDPOLYGONALFACE((90,154,125));
#515=IFCINDEXEDPOLYGONALFACE((136,137,131));
#516=IFCINDEXEDPOLYGONALFACE((135,132,129));
#517=IFCINDEXEDPOLYGONALFACE((134,129,128));
#518=IFCINDEXEDPOLYGONALFACE((132,131,97));
#519=IFCINDEXEDPOLYGONALFACE((131,126,155));
#520=IFCINDEXEDPOLYGONALFACE((131,155,130));
#521=IFCINDEXEDPOLYGONALFACE((127,129,97));
#522=IFCINDEXEDPOLYGONALFACE((97,96,127));
#523=IFCINDEXEDPOLYGONALFACE((124,126,131));
#524=IFCINDEXEDPOLYGONALFACE((131,137,124));
#525=IFCINDEXEDPOLYGONALFACE((84,83,123));
#526=IFCINDEXEDPOLYGONALFACE((82,120,123));
#527=IFCINDEXEDPOLYGONALFACE((123,83,82));
#528=IFCINDEXEDPOLYGONALFACE((109,123,122));
#529=IFCINDEXEDPOLYGONALFACE((98,122,113));
#530=IFCINDEXEDPOLYGONALFACE((122,119,117));
#531=IFCINDEXEDPOLYGONALFACE((104,113,112));
#532=IFCINDEXEDPOLYGONALFACE((123,120,119));
#533=IFCINDEXEDPOLYGONALFACE((116,120,78));
#534=IFCINDEXEDPOLYGONALFACE((77,85,121));
#535=IFCINDEXEDPOLYGONALFACE((115,119,120));
#536=IFCINDEXEDPOLYGONALFACE((91,117,119));
#537=IFCINDEXEDPOLYGONALFACE((115,114,118));
#538=IFCINDEXEDPOLYGONALFACE((90,111,117));
#539=IFCINDEXEDPOLYGONALFACE((116,121,156));
#540=IFCINDEXEDPOLYGONALFACE((116,156,114));
#541=IFCINDEXEDPOLYGONALFACE((113,117,111));
#542=IFCINDEXEDPOLYGONALFACE((80,79,110));
#543=IFCINDEXEDPOLYGONALFACE((110,108,80));
#544=IFCINDEXEDPOLYGONALFACE((81,76,100));
#545=IFCINDEXEDPOLYGONALFACE((110,100,99));
#546=IFCINDEXEDPOLYGONALFACE((75,109,100));
#547=IFCINDEXEDPOLYGONALFACE((100,76,75));
#548=IFCINDEXEDPOLYGONALFACE((105,106,107));
#549=IFCINDEXEDPOLYGONALFACE((108,110,105));
#550=IFCINDEXEDPOLYGONALFACE((106,105,102));
#551=IFCINDEXEDPOLYGONALFACE((105,99,88));
#552=IFCINDEXEDPOLYGONALFACE((102,88,87));
#553=IFCINDEXEDPOLYGONALFACE((99,98,104));
#554=IFCINDEXEDPOLYGONALFACE((103,149,157));
#555=IFCINDEXEDPOLYGONALFACE((103,157,101));
#556=IFCINDEXEDPOLYGONALFACE((100,109,98));
#557=IFCINDEXEDPOLYGONALFACE((97,130,158));
#558=IFCINDEXEDPOLYGONALFACE((97,158,95));
#559=IFCINDEXEDPOLYGONALFACE((134,133,92));
#560=IFCINDEXEDPOLYGONALFACE((94,134,92));
#561=IFCINDEXEDPOLYGONALFACE((91,118,159));
#562=IFCINDEXEDPOLYGONALFACE((91,159,89));
#563=IFCINDEXEDPOLYGONALFACE((88,104,86));
#564=IFCINDEXEDPOLYGONALFACE((160,161,162));
#565=IFCINDEXEDPOLYGONALFACE((163,164,165));
#566=IFCINDEXEDPOLYGONALFACE((26,30,166));
#567=IFCINDEXEDPOLYGONALFACE((44,167,168));
#568=IFCINDEXEDPOLYGONALFACE((169,170,171));
#569=IFCINDEXEDPOLYGONALFACE((172,173,174));
#570=IFCINDEXEDPOLYGONALFACE((169,160,175));
#571=IFCINDEXEDPOLYGONALFACE((175,162,173));
#572=IFCINDEXEDPOLYGONALFACE((170,175,172));
#573=IFCINDEXEDPOLYGONALFACE((176,177,178));
#574=IFCINDEXEDPOLYGONALFACE((179,180,178));
#575=IFCINDEXEDPOLYGONALFACE((92,133,181));
#576=IFCINDEXEDPOLYGONALFACE((170,178,180));
#577=IFCINDEXEDPOLYGONALFACE((144,93,171));
#578=IFCINDEXEDPOLYGONALFACE((180,179,152));
#579=IFCINDEXEDPOLYGONALFACE((164,163,182));
#580=IFCINDEXEDPOLYGONALFACE((183,184,185));
#581=IFCINDEXEDPOLYGONALFACE((186,165,164));
#582=IFCINDEXEDPOLYGONALFACE((96,95,187));
#583=IFCINDEXEDPOLYGONALFACE((185,188,189));
#584=IFCINDEXEDPOLYGONALFACE((190,182,161));
#585=IFCINDEXEDPOLYGONALFACE((185,164,190));
#586=IFCINDEXEDPOLYGONALFACE((188,190,160));
#587=IFCINDEXEDPOLYGONALFACE((188,169,181));
#588=IFCINDEXEDPOLYGONALFACE((127,96,191));
#589=IFCINDEXEDPOLYGONALFACE((189,181,133));
#590=IFCINDEXEDPOLYGONALFACE((192,193,194));
#591=IFCINDEXEDPOLYGONALFACE((72,45,195));
#592=IFCINDEXEDPOLYGONALFACE((196,168,167));
#593=IFCINDEXEDPOLYGONALFACE((194,167,195));
#594=IFCINDEXEDPOLYGONALFACE((195,45,27));
#595=IFCINDEXEDPOLYGONALFACE((197,195,198));
#596=IFCINDEXEDPOLYGONALFACE((199,194,197));
#597=IFCINDEXEDPOLYGONALFACE((200,201,199));
#598=IFCINDEXEDPOLYGONALFACE((163,199,202));
#599=IFCINDEXEDPOLYGONALFACE((202,197,203));
#600=IFCINDEXEDPOLYGONALFACE((182,202,204));
#601=IFCINDEXEDPOLYGONALFACE((205,31,73));
#602=IFCINDEXEDPOLYGONALFACE((204,203,206));
#603=IFCINDEXEDPOLYGONALFACE((29,31,205));
#604=IFCINDEXEDPOLYGONALFACE((203,198,166));
#605=IFCINDEXEDPOLYGONALFACE((206,166,205));
#606=IFCINDEXEDPOLYGONALFACE((207,206,208));
#607=IFCINDEXEDPOLYGONALFACE((161,204,207));
#608=IFCINDEXEDPOLYGONALFACE((162,207,209));
#609=IFCINDEXEDPOLYGONALFACE((210,211,209));
#610=IFCINDEXEDPOLYGONALFACE((212,213,208));
#611=IFCINDEXEDPOLYGONALFACE((173,209,211));
#612=IFCINDEXEDPOLYGONALFACE((211,174,173));
#613=IFCINDEXEDPOLYGONALFACE((214,212,208));
#614=IFCINDEXEDPOLYGONALFACE((208,205,214));
#615=IFCINDEXEDPOLYGONALFACE((213,210,209));
#616=IFCINDEXEDPOLYGONALFACE((209,208,213));
#617=IFCINDEXEDPOLYGONALFACE((209,173,162));
#618=IFCINDEXEDPOLYGONALFACE((207,162,161));
#619=IFCINDEXEDPOLYGONALFACE((208,209,207));
#620=IFCINDEXEDPOLYGONALFACE((205,208,206));
#621=IFCINDEXEDPOLYGONALFACE((166,206,203));
#622=IFCINDEXEDPOLYGONALFACE((166,30,29));
#623=IFCINDEXEDPOLYGONALFACE((205,166,29));
#624=IFCINDEXEDPOLYGONALFACE((206,207,204));
#625=IFCINDEXEDPOLYGONALFACE((73,214,205));
#626=IFCINDEXEDPOLYGONALFACE((204,161,182));
#627=IFCINDEXEDPOLYGONALFACE((203,204,202));
#628=IFCINDEXEDPOLYGONALFACE((202,182,163));
#629=IFCINDEXEDPOLYGONALFACE((199,163,200));
#630=IFCINDEXEDPOLYGONALFACE((197,202,199));
#631=IFCINDEXEDPOLYGONALFACE((198,203,197));
#632=IFCINDEXEDPOLYGONALFACE((27,198,195));
#633=IFCINDEXEDPOLYGONALFACE((195,197,194));
#634=IFCINDEXEDPOLYGONALFACE((193,196,167));
#635=IFCINDEXEDPOLYGONALFACE((167,194,193));
#636=IFCINDEXEDPOLYGONALFACE((72,195,167));
#637=IFCINDEXEDPOLYGONALFACE((167,44,72));
#638=IFCINDEXEDPOLYGONALFACE((201,192,194));
#639=IFCINDEXEDPOLYGONALFACE((194,199,201));
#640=IFCINDEXEDPOLYGONALFACE((133,128,189));
#641=IFCINDEXEDPOLYGONALFACE((189,128,127));
#642=IFCINDEXEDPOLYGONALFACE((191,189,127));
#643=IFCINDEXEDPOLYGONALFACE((181,189,188));
#644=IFCINDEXEDPOLYGONALFACE((160,169,188));
#645=IFCINDEXEDPOLYGONALFACE((190,188,185));
#646=IFCINDEXEDPOLYGONALFACE((161,160,190));
#647=IFCINDEXEDPOLYGONALFACE((189,191,185));
#648=IFCINDEXEDPOLYGONALFACE((187,191,96));
#649=IFCINDEXEDPOLYGONALFACE((186,164,185));
#650=IFCINDEXEDPOLYGONALFACE((185,184,186));
#651=IFCINDEXEDPOLYGONALFACE((191,187,183));
#652=IFCINDEXEDPOLYGONALFACE((185,191,183));
#653=IFCINDEXEDPOLYGONALFACE((182,190,164));
#654=IFCINDEXEDPOLYGONALFACE((152,141,180));
#655=IFCINDEXEDPOLYGONALFACE((144,171,180));
#656=IFCINDEXEDPOLYGONALFACE((180,141,144));
#657=IFCINDEXEDPOLYGONALFACE((180,171,170));
#658=IFCINDEXEDPOLYGONALFACE((92,181,171));
#659=IFCINDEXEDPOLYGONALFACE((171,93,92));
#660=IFCINDEXEDPOLYGONALFACE((178,177,215));
#661=IFCINDEXEDPOLYGONALFACE((178,215,179));
#662=IFCINDEXEDPOLYGONALFACE((172,216,176));
#663=IFCINDEXEDPOLYGONALFACE((178,172,176));
#664=IFCINDEXEDPOLYGONALFACE((172,178,170));
#665=IFCINDEXEDPOLYGONALFACE((173,172,175));
#666=IFCINDEXEDPOLYGONALFACE((175,170,169));
#667=IFCINDEXEDPOLYGONALFACE((174,216,172));
#668=IFCINDEXEDPOLYGONALFACE((171,181,169));
#669=IFCINDEXEDPOLYGONALFACE((168,38,44));
#670=IFCINDEXEDPOLYGONALFACE((198,27,26));
#671=IFCINDEXEDPOLYGONALFACE((166,198,26));
#672=IFCINDEXEDPOLYGONALFACE((165,200,163));
#673=IFCINDEXEDPOLYGONALFACE((162,175,160));
#674=IFCINDEXEDPOLYGONALFACE((1,2,3));
#675=IFCINDEXEDPOLYGONALFACE((4,2,1));
#676=IFCINDEXEDPOLYGONALFACE((5,6,1));
#677=IFCINDEXEDPOLYGONALFACE((1,7,8));
#678=IFCINDEXEDPOLYGONALFACE((9,7,1));
#679=IFCINDEXEDPOLYGONALFACE((10,11,1));
#680=IFCINDEXEDPOLYGONALFACE((1,12,13));
#681=IFCINDEXEDPOLYGONALFACE((1,14,12));
#682=IFCINDEXEDPOLYGONALFACE((15,14,1));
#683=IFCINDEXEDPOLYGONALFACE((16,17,1));
#684=IFCINDEXEDPOLYGONALFACE((1,18,19));
#685=IFCINDEXEDPOLYGONALFACE((20,18,1));
#686=IFCINDEXEDPOLYGONALFACE((21,22,1));
#687=IFCINDEXEDPOLYGONALFACE((1,23,24));
#688=IFCINDEXEDPOLYGONALFACE((1,24,21));
#689=IFCINDEXEDPOLYGONALFACE((1,22,20));
#690=IFCINDEXEDPOLYGONALFACE((1,19,16));
#691=IFCINDEXEDPOLYGONALFACE((1,17,15));
#692=IFCINDEXEDPOLYGONALFACE((1,13,10));
#693=IFCINDEXEDPOLYGONALFACE((1,11,9));
#694=IFCINDEXEDPOLYGONALFACE((1,8,5));
#695=IFCINDEXEDPOLYGONALFACE((1,6,4));
#696=IFCINDEXEDPOLYGONALFACE((1,25,23));
#697=IFCINDEXEDPOLYGONALFACE((26,25,1));
#698=IFCINDEXEDPOLYGONALFACE((27,28,1));
#699=IFCINDEXEDPOLYGONALFACE((1,29,30));
#700=IFCINDEXEDPOLYGONALFACE((31,29,1));
#701=IFCINDEXEDPOLYGONALFACE((32,33,1));
#702=IFCINDEXEDPOLYGONALFACE((1,3,34));
#703=IFCINDEXEDPOLYGONALFACE((1,34,32));
#704=IFCINDEXEDPOLYGONALFACE((1,33,31));
#705=IFCINDEXEDPOLYGONALFACE((1,30,27));
#706=IFCINDEXEDPOLYGONALFACE((1,28,26));
#707=IFCINDEXEDPOLYGONALFACE((1,2,3,4));
#708=IFCINDEXEDPOLYGONALFACE((4,3,5,6));
#709=IFCINDEXEDPOLYGONALFACE((6,5,7,8));
#710=IFCINDEXEDPOLYGONALFACE((8,7,9,10));
#711=IFCINDEXEDPOLYGONALFACE((10,9,11,12));
#712=IFCINDEXEDPOLYGONALFACE((12,11,13,14));
#713=IFCINDEXEDPOLYGONALFACE((15,16,17,18));
#714=IFCINDEXEDPOLYGONALFACE((18,17,19,20));
#715=IFCINDEXEDPOLYGONALFACE((20,19,21,22));
#716=IFCINDEXEDPOLYGONALFACE((22,21,23,24));
#717=IFCINDEXEDPOLYGONALFACE((24,23,25,26));
#718=IFCINDEXEDPOLYGONALFACE((26,25,27,28));
#719=IFCINDEXEDPOLYGONALFACE((28,27,29,30));
#720=IFCINDEXEDPOLYGONALFACE((30,29,31,32));
#721=IFCINDEXEDPOLYGONALFACE((33,34,16));
#722=IFCINDEXEDPOLYGONALFACE((35,36,37));
#723=IFCINDEXEDPOLYGONALFACE((38,39,14));
#724=IFCINDEXEDPOLYGONALFACE((40,41,42));
#725=IFCINDEXEDPOLYGONALFACE((43,44,39));
#726=IFCINDEXEDPOLYGONALFACE((39,38,43));
#727=IFCINDEXEDPOLYGONALFACE((44,45,42));
#728=IFCINDEXEDPOLYGONALFACE((42,39,44));
#729=IFCINDEXEDPOLYGONALFACE((42,45,46));
#730=IFCINDEXEDPOLYGONALFACE((42,46,40));
#731=IFCINDEXEDPOLYGONALFACE((47,48,14));
#732=IFCINDEXEDPOLYGONALFACE((36,49,13));
#733=IFCINDEXEDPOLYGONALFACE((13,37,36));
#734=IFCINDEXEDPOLYGONALFACE((50,47,14));
#735=IFCINDEXEDPOLYGONALFACE((13,49,51));
#736=IFCINDEXEDPOLYGONALFACE((52,50,14));
#737=IFCINDEXEDPOLYGONALFACE((13,51,53));
#738=IFCINDEXEDPOLYGONALFACE((54,52,14));
#739=IFCINDEXEDPOLYGONALFACE((13,53,54));
#740=IFCINDEXEDPOLYGONALFACE((14,13,54));
#741=IFCINDEXEDPOLYGONALFACE((14,48,38));
#742=IFCINDEXEDPOLYGONALFACE((55,56,34));
#743=IFCINDEXEDPOLYGONALFACE((57,55,34));
#744=IFCINDEXEDPOLYGONALFACE((58,57,34));
#745=IFCINDEXEDPOLYGONALFACE((34,33,58));
#746=IFCINDEXEDPOLYGONALFACE((59,37,34));
#747=IFCINDEXEDPOLYGONALFACE((34,56,59));
#748=IFCINDEXEDPOLYGONALFACE((37,59,35));
#749=IFCINDEXEDPOLYGONALFACE((41,60,15));
#750=IFCINDEXEDPOLYGONALFACE((15,42,41));
#751=IFCINDEXEDPOLYGONALFACE((61,62,16));
#752=IFCINDEXEDPOLYGONALFACE((15,60,61));
#753=IFCINDEXEDPOLYGONALFACE((16,15,61));
#754=IFCINDEXEDPOLYGONALFACE((16,62,33));
#755=IFCINDEXEDPOLYGONALFACE((1,2,3));
#756=IFCINDEXEDPOLYGONALFACE((4,5,6));
#757=IFCINDEXEDPOLYGONALFACE((7,8,9));
#758=IFCINDEXEDPOLYGONALFACE((10,11,12));
#759=IFCINDEXEDPOLYGONALFACE((13,14,15));
#760=IFCINDEXEDPOLYGONALFACE((3,2,16));
#761=IFCINDEXEDPOLYGONALFACE((14,13,17));
#762=IFCINDEXEDPOLYGONALFACE((18,19,20));
#763=IFCINDEXEDPOLYGONALFACE((21,18,16));
#764=IFCINDEXEDPOLYGONALFACE((22,23,24));
#765=IFCINDEXEDPOLYGONALFACE((25,24,23));
#766=IFCINDEXEDPOLYGONALFACE((18,21,26));
#767=IFCINDEXEDPOLYGONALFACE((24,27,26));
#768=IFCINDEXEDPOLYGONALFACE((25,28,27));
#769=IFCINDEXEDPOLYGONALFACE((29,30,20));
#770=IFCINDEXEDPOLYGONALFACE((31,15,32));
#771=IFCINDEXEDPOLYGONALFACE((30,33,15));
#772=IFCINDEXEDPOLYGONALFACE((34,35,32));
#773=IFCINDEXEDPOLYGONALFACE((36,37,38));
#774=IFCINDEXEDPOLYGONALFACE((29,19,26));
#775=IFCINDEXEDPOLYGONALFACE((36,33,30));
#776=IFCINDEXEDPOLYGONALFACE((29,39,40));
#777=IFCINDEXEDPOLYGONALFACE((39,26,27));
#778=IFCINDEXEDPOLYGONALFACE((28,41,42));
#779=IFCINDEXEDPOLYGONALFACE((39,42,43));
#780=IFCINDEXEDPOLYGONALFACE((41,44,43));
#781=IFCINDEXEDPOLYGONALFACE((45,40,46));
#782=IFCINDEXEDPOLYGONALFACE((47,34,38));
#783=IFCINDEXEDPOLYGONALFACE((37,36,45));
#784=IFCINDEXEDPOLYGONALFACE((37,48,49));
#785=IFCINDEXEDPOLYGONALFACE((34,47,50));
#786=IFCINDEXEDPOLYGONALFACE((47,49,51));
#787=IFCINDEXEDPOLYGONALFACE((49,48,46));
#788=IFCINDEXEDPOLYGONALFACE((40,43,52));
#789=IFCINDEXEDPOLYGONALFACE((43,44,53));
#790=IFCINDEXEDPOLYGONALFACE((54,46,52));
#791=IFCINDEXEDPOLYGONALFACE((54,55,51));
#792=IFCINDEXEDPOLYGONALFACE((56,57,55));
#793=IFCINDEXEDPOLYGONALFACE((58,59,52));
#794=IFCINDEXEDPOLYGONALFACE((60,56,59));
#795=IFCINDEXEDPOLYGONALFACE((60,61,62));
#796=IFCINDEXEDPOLYGONALFACE((58,63,61));
#797=IFCINDEXEDPOLYGONALFACE((64,65,66));
#798=IFCINDEXEDPOLYGONALFACE((67,68,69));
#799=IFCINDEXEDPOLYGONALFACE((70,71,72));
#800=IFCINDEXEDPOLYGONALFACE((6,67,73));
#801=IFCINDEXEDPOLYGONALFACE((70,74,75));
#802=IFCINDEXEDPOLYGONALFACE((76,75,74));
#803=IFCINDEXEDPOLYGONALFACE((6,5,76));
#804=IFCINDEXEDPOLYGONALFACE((77,78,74));
#805=IFCINDEXEDPOLYGONALFACE((79,74,78));
#806=IFCINDEXEDPOLYGONALFACE((79,68,67));
#807=IFCINDEXEDPOLYGONALFACE((65,80,81));
#808=IFCINDEXEDPOLYGONALFACE((82,83,77));
#809=IFCINDEXEDPOLYGONALFACE((69,84,85));
#810=IFCINDEXEDPOLYGONALFACE((83,86,84));
#811=IFCINDEXEDPOLYGONALFACE((84,69,68));
#812=IFCINDEXEDPOLYGONALFACE((87,85,88));
#813=IFCINDEXEDPOLYGONALFACE((89,90,91));
#814=IFCINDEXEDPOLYGONALFACE((92,93,94));
#815=IFCINDEXEDPOLYGONALFACE((95,96,97));
#816=IFCINDEXEDPOLYGONALFACE((89,98,94));
#817=IFCINDEXEDPOLYGONALFACE((99,97,90));
#818=IFCINDEXEDPOLYGONALFACE((100,101,102));
#819=IFCINDEXEDPOLYGONALFACE((103,104,105));
#820=IFCINDEXEDPOLYGONALFACE((95,99,102));
#821=IFCINDEXEDPOLYGONALFACE((106,105,96));
#822=IFCINDEXEDPOLYGONALFACE((106,101,107));
#823=IFCINDEXEDPOLYGONALFACE((104,103,108));
#824=IFCINDEXEDPOLYGONALFACE((103,107,109));
#825=IFCINDEXEDPOLYGONALFACE((101,100,110));
#826=IFCINDEXEDPOLYGONALFACE((110,100,111));
#827=IFCINDEXEDPOLYGONALFACE((107,110,112));
#828=IFCINDEXEDPOLYGONALFACE((111,93,92));
#829=IFCINDEXEDPOLYGONALFACE((113,114,92));
#830=IFCINDEXEDPOLYGONALFACE((115,116,117));
#831=IFCINDEXEDPOLYGONALFACE((82,81,80));
#832=IFCINDEXEDPOLYGONALFACE((83,82,115));
#833=IFCINDEXEDPOLYGONALFACE((118,116,115));
#834=IFCINDEXEDPOLYGONALFACE((88,117,119));
#835=IFCINDEXEDPOLYGONALFACE((118,80,120));
#836=IFCINDEXEDPOLYGONALFACE((121,122,17));
#837=IFCINDEXEDPOLYGONALFACE((64,120,80));
#838=IFCINDEXEDPOLYGONALFACE((120,64,17));
#839=IFCINDEXEDPOLYGONALFACE((13,31,123));
#840=IFCINDEXEDPOLYGONALFACE((123,124,117));
#841=IFCINDEXEDPOLYGONALFACE((124,125,119));
#842=IFCINDEXEDPOLYGONALFACE((124,123,31));
#843=IFCINDEXEDPOLYGONALFACE((126,127,128));
#844=IFCINDEXEDPOLYGONALFACE((129,130,131));
#845=IFCINDEXEDPOLYGONALFACE((132,133,134));
#846=IFCINDEXEDPOLYGONALFACE((135,136,137));
#847=IFCINDEXEDPOLYGONALFACE((138,139,140));
#848=IFCINDEXEDPOLYGONALFACE((141,142,143));
#849=IFCINDEXEDPOLYGONALFACE((144,145,146));
#850=IFCINDEXEDPOLYGONALFACE((147,148,149));
#851=IFCINDEXEDPOLYGONALFACE((150,151,152));
#852=IFCINDEXEDPOLYGONALFACE((153,154,155));
#853=IFCINDEXEDPOLYGONALFACE((156,10,157));
#854=IFCINDEXEDPOLYGONALFACE((158,152,11));
#855=IFCINDEXEDPOLYGONALFACE((159,158,10));
#856=IFCINDEXEDPOLYGONALFACE((154,156,160));
#857=IFCINDEXEDPOLYGONALFACE((161,162,156));
#858=IFCINDEXEDPOLYGONALFACE((163,159,162));
#859=IFCINDEXEDPOLYGONALFACE((164,165,166));
#860=IFCINDEXEDPOLYGONALFACE((167,168,169));
#861=IFCINDEXEDPOLYGONALFACE((161,154,153));
#862=IFCINDEXEDPOLYGONALFACE((170,171,153));
#863=IFCINDEXEDPOLYGONALFACE((172,170,168));
#864=IFCINDEXEDPOLYGONALFACE((173,172,167));
#865=IFCINDEXEDPOLYGONALFACE((174,175,176));
#866=IFCINDEXEDPOLYGONALFACE((177,173,165));
#867=IFCINDEXEDPOLYGONALFACE((178,177,164));
#868=IFCINDEXEDPOLYGONALFACE((179,178,175));
#869=IFCINDEXEDPOLYGONALFACE((180,181,179));
#870=IFCINDEXEDPOLYGONALFACE((171,170,182));
#871=IFCINDEXEDPOLYGONALFACE((146,145,151));
#872=IFCINDEXEDPOLYGONALFACE((183,184,158));
#873=IFCINDEXEDPOLYGONALFACE((185,150,184));
#874=IFCINDEXEDPOLYGONALFACE((186,183,159));
#875=IFCINDEXEDPOLYGONALFACE((187,185,183));
#876=IFCINDEXEDPOLYGONALFACE((188,189,150));
#877=IFCINDEXEDPOLYGONALFACE((188,190,191));
#878=IFCINDEXEDPOLYGONALFACE((190,188,185));
#879=IFCINDEXEDPOLYGONALFACE((192,190,187));
#880=IFCINDEXEDPOLYGONALFACE((177,178,193));
#881=IFCINDEXEDPOLYGONALFACE((194,195,186));
#882=IFCINDEXEDPOLYGONALFACE((172,173,196));
#883=IFCINDEXEDPOLYGONALFACE((197,186,198));
#884=IFCINDEXEDPOLYGONALFACE((199,197,182));
#885=IFCINDEXEDPOLYGONALFACE((200,194,197));
#886=IFCINDEXEDPOLYGONALFACE((201,187,195));
#887=IFCINDEXEDPOLYGONALFACE((202,201,194));
#888=IFCINDEXEDPOLYGONALFACE((199,203,204));
#889=IFCINDEXEDPOLYGONALFACE((179,181,205));
#890=IFCINDEXEDPOLYGONALFACE((196,193,203));
#891=IFCINDEXEDPOLYGONALFACE((206,204,203));
#892=IFCINDEXEDPOLYGONALFACE((200,204,207));
#893=IFCINDEXEDPOLYGONALFACE((202,207,208));
#894=IFCINDEXEDPOLYGONALFACE((209,191,190));
#895=IFCINDEXEDPOLYGONALFACE((210,211,201));
#896=IFCINDEXEDPOLYGONALFACE((212,192,211));
#897=IFCINDEXEDPOLYGONALFACE((213,209,192));
#898=IFCINDEXEDPOLYGONALFACE((212,214,215));
#899=IFCINDEXEDPOLYGONALFACE((207,206,216));
#900=IFCINDEXEDPOLYGONALFACE((210,208,214));
#901=IFCINDEXEDPOLYGONALFACE((208,216,217));
#902=IFCINDEXEDPOLYGONALFACE((214,217,218));
#903=IFCINDEXEDPOLYGONALFACE((219,205,181));
#904=IFCINDEXEDPOLYGONALFACE((220,221,222));
#905=IFCINDEXEDPOLYGONALFACE((223,180,148));
#906=IFCINDEXEDPOLYGONALFACE((221,220,147));
#907=IFCINDEXEDPOLYGONALFACE((223,147,220));
#908=IFCINDEXEDPOLYGONALFACE((224,181,180));
#909=IFCINDEXEDPOLYGONALFACE((225,226,227));
#910=IFCINDEXEDPOLYGONALFACE((228,225,229));
#911=IFCINDEXEDPOLYGONALFACE((230,226,225));
#912=IFCINDEXEDPOLYGONALFACE((231,232,227));
#913=IFCINDEXEDPOLYGONALFACE((206,233,234));
#914=IFCINDEXEDPOLYGONALFACE((235,236,205));
#915=IFCINDEXEDPOLYGONALFACE((232,219,224));
#916=IFCINDEXEDPOLYGONALFACE((219,232,237));
#917=IFCINDEXEDPOLYGONALFACE((236,235,233));
#918=IFCINDEXEDPOLYGONALFACE((217,238,239));
#919=IFCINDEXEDPOLYGONALFACE((233,237,240));
#920=IFCINDEXEDPOLYGONALFACE((216,234,238));
#921=IFCINDEXEDPOLYGONALFACE((238,240,241));
#922=IFCINDEXEDPOLYGONALFACE((242,240,237));
#923=IFCINDEXEDPOLYGONALFACE((243,237,232));
#924=IFCINDEXEDPOLYGONALFACE((244,231,226));
#925=IFCINDEXEDPOLYGONALFACE((245,246,231));
#926=IFCINDEXEDPOLYGONALFACE((247,243,246));
#927=IFCINDEXEDPOLYGONALFACE((240,242,248));
#928=IFCINDEXEDPOLYGONALFACE((243,247,249));
#929=IFCINDEXEDPOLYGONALFACE((242,249,250));
#930=IFCINDEXEDPOLYGONALFACE((239,241,251));
#931=IFCINDEXEDPOLYGONALFACE((252,253,140));
#932=IFCINDEXEDPOLYGONALFACE((254,255,191));
#933=IFCINDEXEDPOLYGONALFACE((256,144,257));
#934=IFCINDEXEDPOLYGONALFACE((258,257,146));
#935=IFCINDEXEDPOLYGONALFACE((253,252,257));
#936=IFCINDEXEDPOLYGONALFACE((259,253,258));
#937=IFCINDEXEDPOLYGONALFACE((213,215,260));
#938=IFCINDEXEDPOLYGONALFACE((254,261,262));
#939=IFCINDEXEDPOLYGONALFACE((261,254,209));
#940=IFCINDEXEDPOLYGONALFACE((260,263,264));
#941=IFCINDEXEDPOLYGONALFACE((215,218,263));
#942=IFCINDEXEDPOLYGONALFACE((262,264,265));
#943=IFCINDEXEDPOLYGONALFACE((259,265,266));
#944=IFCINDEXEDPOLYGONALFACE((265,267,268));
#945=IFCINDEXEDPOLYGONALFACE((269,270,267));
#946=IFCINDEXEDPOLYGONALFACE((248,250,271));
#947=IFCINDEXEDPOLYGONALFACE((263,251,269));
#948=IFCINDEXEDPOLYGONALFACE((251,271,270));
#949=IFCINDEXEDPOLYGONALFACE((250,272,273));
#950=IFCINDEXEDPOLYGONALFACE((274,275,276));
#951=IFCINDEXEDPOLYGONALFACE((277,278,230));
#952=IFCINDEXEDPOLYGONALFACE((279,280,281));
#953=IFCINDEXEDPOLYGONALFACE((282,228,143));
#954=IFCINDEXEDPOLYGONALFACE((141,283,281));
#955=IFCINDEXEDPOLYGONALFACE((284,142,141));
#956=IFCINDEXEDPOLYGONALFACE((285,230,228));
#957=IFCINDEXEDPOLYGONALFACE((286,282,142));
#958=IFCINDEXEDPOLYGONALFACE((287,288,282));
#959=IFCINDEXEDPOLYGONALFACE((289,285,288));
#960=IFCINDEXEDPOLYGONALFACE((290,286,284));
#961=IFCINDEXEDPOLYGONALFACE((291,279,292));
#962=IFCINDEXEDPOLYGONALFACE((293,284,280));
#963=IFCINDEXEDPOLYGONALFACE((294,295,279));
#964=IFCINDEXEDPOLYGONALFACE((296,297,294));
#965=IFCINDEXEDPOLYGONALFACE((298,299,295));
#966=IFCINDEXEDPOLYGONALFACE((287,286,290));
#967=IFCINDEXEDPOLYGONALFACE((299,290,293));
#968=IFCINDEXEDPOLYGONALFACE((300,301,290));
#969=IFCINDEXEDPOLYGONALFACE((302,300,299));
#970=IFCINDEXEDPOLYGONALFACE((303,298,294));
#971=IFCINDEXEDPOLYGONALFACE((304,302,298));
#972=IFCINDEXEDPOLYGONALFACE((301,300,305));
#973=IFCINDEXEDPOLYGONALFACE((247,306,307));
#974=IFCINDEXEDPOLYGONALFACE((308,245,244));
#975=IFCINDEXEDPOLYGONALFACE((309,277,285));
#976=IFCINDEXEDPOLYGONALFACE((310,244,278));
#977=IFCINDEXEDPOLYGONALFACE((311,310,277));
#978=IFCINDEXEDPOLYGONALFACE((310,311,312));
#979=IFCINDEXEDPOLYGONALFACE((245,308,306));
#980=IFCINDEXEDPOLYGONALFACE((307,313,272));
#981=IFCINDEXEDPOLYGONALFACE((306,312,313));
#982=IFCINDEXEDPOLYGONALFACE((313,312,314));
#983=IFCINDEXEDPOLYGONALFACE((311,315,314));
#984=IFCINDEXEDPOLYGONALFACE((302,304,316));
#985=IFCINDEXEDPOLYGONALFACE((309,305,315));
#986=IFCINDEXEDPOLYGONALFACE((315,316,317));
#987=IFCINDEXEDPOLYGONALFACE((314,317,318));
#988=IFCINDEXEDPOLYGONALFACE((316,304,319));
#989=IFCINDEXEDPOLYGONALFACE((320,321,322));
#990=IFCINDEXEDPOLYGONALFACE((323,303,297));
#991=IFCINDEXEDPOLYGONALFACE((324,296,325));
#992=IFCINDEXEDPOLYGONALFACE((274,326,322));
#993=IFCINDEXEDPOLYGONALFACE((327,325,275));
#994=IFCINDEXEDPOLYGONALFACE((324,327,321));
#995=IFCINDEXEDPOLYGONALFACE((328,297,296));
#996=IFCINDEXEDPOLYGONALFACE((303,323,319));
#997=IFCINDEXEDPOLYGONALFACE((328,329,330));
#998=IFCINDEXEDPOLYGONALFACE((323,330,331));
#999=IFCINDEXEDPOLYGONALFACE((330,329,332));
#1000=IFCINDEXEDPOLYGONALFACE((136,320,333));
#1001=IFCINDEXEDPOLYGONALFACE((332,329,321));
#1002=IFCINDEXEDPOLYGONALFACE((320,136,334));
#1003=IFCINDEXEDPOLYGONALFACE((332,334,335));
#1004=IFCINDEXEDPOLYGONALFACE((336,335,337));
#1005=IFCINDEXEDPOLYGONALFACE((338,339,340));
#1006=IFCINDEXEDPOLYGONALFACE((319,331,339));
#1007=IFCINDEXEDPOLYGONALFACE((340,337,341));
#1008=IFCINDEXEDPOLYGONALFACE((267,342,343));
#1009=IFCINDEXEDPOLYGONALFACE((273,344,342));
#1010=IFCINDEXEDPOLYGONALFACE((345,318,344));
#1011=IFCINDEXEDPOLYGONALFACE((343,341,346));
#1012=IFCINDEXEDPOLYGONALFACE((347,348,349));
#1013=IFCINDEXEDPOLYGONALFACE((266,268,348));
#1014=IFCINDEXEDPOLYGONALFACE((347,350,140));
#1015=IFCINDEXEDPOLYGONALFACE((351,352,353));
#1016=IFCINDEXEDPOLYGONALFACE((354,355,130));
#1017=IFCINDEXEDPOLYGONALFACE((356,357,351));
#1018=IFCINDEXEDPOLYGONALFACE((358,359,355));
#1019=IFCINDEXEDPOLYGONALFACE((335,360,356));
#1020=IFCINDEXEDPOLYGONALFACE((361,358,360));
#1021=IFCINDEXEDPOLYGONALFACE((135,362,359));
#1022=IFCINDEXEDPOLYGONALFACE((136,135,361));
#1023=IFCINDEXEDPOLYGONALFACE((360,354,357));
#1024=IFCINDEXEDPOLYGONALFACE((363,346,353));
#1025=IFCINDEXEDPOLYGONALFACE((127,126,364));
#1026=IFCINDEXEDPOLYGONALFACE((365,349,346));
#1027=IFCINDEXEDPOLYGONALFACE((133,349,365));
#1028=IFCINDEXEDPOLYGONALFACE((366,365,363));
#1029=IFCINDEXEDPOLYGONALFACE((366,367,134));
#1030=IFCINDEXEDPOLYGONALFACE((368,369,370));
#1031=IFCINDEXEDPOLYGONALFACE((371,372,8));
#1032=IFCINDEXEDPOLYGONALFACE((373,374,375));
#1033=IFCINDEXEDPOLYGONALFACE((376,131,372));
#1034=IFCINDEXEDPOLYGONALFACE((376,373,352));
#1035=IFCINDEXEDPOLYGONALFACE((373,376,371));
#1036=IFCINDEXEDPOLYGONALFACE((375,374,377));
#1037=IFCINDEXEDPOLYGONALFACE((374,371,7));
#1038=IFCINDEXEDPOLYGONALFACE((378,379,368));
#1039=IFCINDEXEDPOLYGONALFACE((380,381,369));
#1040=IFCINDEXEDPOLYGONALFACE((382,383,384));
#1041=IFCINDEXEDPOLYGONALFACE((4,385,386));
#1042=IFCINDEXEDPOLYGONALFACE((387,388,389));
#1043=IFCINDEXEDPOLYGONALFACE((385,390,128));
#1044=IFCINDEXEDPOLYGONALFACE((391,366,388));
#1045=IFCINDEXEDPOLYGONALFACE((367,366,391));
#1046=IFCINDEXEDPOLYGONALFACE((392,128,127));
#1047=IFCINDEXEDPOLYGONALFACE((387,393,394));
#1048=IFCINDEXEDPOLYGONALFACE((395,392,391));
#1049=IFCINDEXEDPOLYGONALFACE((395,386,128));
#1050=IFCINDEXEDPOLYGONALFACE((384,396,389));
#1051=IFCINDEXEDPOLYGONALFACE((396,72,71));
#1052=IFCINDEXEDPOLYGONALFACE((393,71,75));
#1053=IFCINDEXEDPOLYGONALFACE((76,395,394));
#1054=IFCINDEXEDPOLYGONALFACE((386,395,76));
#1055=IFCINDEXEDPOLYGONALFACE((396,384,66));
#1056=IFCINDEXEDPOLYGONALFACE((121,66,384));
#1057=IFCINDEXEDPOLYGONALFACE((397,398,382));
#1058=IFCINDEXEDPOLYGONALFACE((399,383,382));
#1059=IFCINDEXEDPOLYGONALFACE((383,399,122));
#1060=IFCINDEXEDPOLYGONALFACE((400,401,402));
#1061=IFCINDEXEDPOLYGONALFACE((403,397,381));
#1062=IFCINDEXEDPOLYGONALFACE((402,404,405));
#1063=IFCINDEXEDPOLYGONALFACE((406,379,378));
#1064=IFCINDEXEDPOLYGONALFACE((378,377,407));
#1065=IFCINDEXEDPOLYGONALFACE((380,379,406));
#1066=IFCINDEXEDPOLYGONALFACE((381,380,408));
#1067=IFCINDEXEDPOLYGONALFACE((408,406,409));
#1068=IFCINDEXEDPOLYGONALFACE((410,411,408));
#1069=IFCINDEXEDPOLYGONALFACE((412,409,413));
#1070=IFCINDEXEDPOLYGONALFACE((414,413,415));
#1071=IFCINDEXEDPOLYGONALFACE((404,416,407));
#1072=IFCINDEXEDPOLYGONALFACE((417,418,414));
#1073=IFCINDEXEDPOLYGONALFACE((419,420,409));
#1074=IFCINDEXEDPOLYGONALFACE((419,421,422));
#1075=IFCINDEXEDPOLYGONALFACE((412,418,421));
#1076=IFCINDEXEDPOLYGONALFACE((417,423,424));
#1077=IFCINDEXEDPOLYGONALFACE((423,417,425));
#1078=IFCINDEXEDPOLYGONALFACE((422,421,424));
#1079=IFCINDEXEDPOLYGONALFACE((426,410,422));
#1080=IFCINDEXEDPOLYGONALFACE((427,1,122));
#1081=IFCINDEXEDPOLYGONALFACE((428,426,427));
#1082=IFCINDEXEDPOLYGONALFACE((403,411,410));
#1083=IFCINDEXEDPOLYGONALFACE((397,403,428));
#1084=IFCINDEXEDPOLYGONALFACE((429,430,2));
#1085=IFCINDEXEDPOLYGONALFACE((431,422,432));
#1086=IFCINDEXEDPOLYGONALFACE((431,429,427));
#1087=IFCINDEXEDPOLYGONALFACE((433,434,430));
#1088=IFCINDEXEDPOLYGONALFACE((435,436,437));
#1089=IFCINDEXEDPOLYGONALFACE((438,439,423));
#1090=IFCINDEXEDPOLYGONALFACE((440,435,441));
#1091=IFCINDEXEDPOLYGONALFACE((442,415,416));
#1092=IFCINDEXEDPOLYGONALFACE((443,444,416));
#1093=IFCINDEXEDPOLYGONALFACE((404,402,401));
#1094=IFCINDEXEDPOLYGONALFACE((442,445,446));
#1095=IFCINDEXEDPOLYGONALFACE((447,445,442));
#1096=IFCINDEXEDPOLYGONALFACE((448,444,449));
#1097=IFCINDEXEDPOLYGONALFACE((450,451,443));
#1098=IFCINDEXEDPOLYGONALFACE((452,401,453));
#1099=IFCINDEXEDPOLYGONALFACE((401,400,441));
#1100=IFCINDEXEDPOLYGONALFACE((451,449,444));
#1101=IFCINDEXEDPOLYGONALFACE((454,455,456));
#1102=IFCINDEXEDPOLYGONALFACE((455,454,448));
#1103=IFCINDEXEDPOLYGONALFACE((455,457,458));
#1104=IFCINDEXEDPOLYGONALFACE((459,447,456));
#1105=IFCINDEXEDPOLYGONALFACE((439,460,433));
#1106=IFCINDEXEDPOLYGONALFACE((445,447,459));
#1107=IFCINDEXEDPOLYGONALFACE((459,460,439));
#1108=IFCINDEXEDPOLYGONALFACE((461,462,463));
#1109=IFCINDEXEDPOLYGONALFACE((456,458,464));
#1110=IFCINDEXEDPOLYGONALFACE((465,461,460));
#1111=IFCINDEXEDPOLYGONALFACE((461,465,464));
#1112=IFCINDEXEDPOLYGONALFACE((466,467,468));
#1113=IFCINDEXEDPOLYGONALFACE((469,470,471));
#1114=IFCINDEXEDPOLYGONALFACE((472,473,474));
#1115=IFCINDEXEDPOLYGONALFACE((469,437,436));
#1116=IFCINDEXEDPOLYGONALFACE((475,451,450));
#1117=IFCINDEXEDPOLYGONALFACE((450,453,476));
#1118=IFCINDEXEDPOLYGONALFACE((441,437,476));
#1119=IFCINDEXEDPOLYGONALFACE((477,478,449));
#1120=IFCINDEXEDPOLYGONALFACE((477,475,479));
#1121=IFCINDEXEDPOLYGONALFACE((478,477,480));
#1122=IFCINDEXEDPOLYGONALFACE((481,479,482));
#1123=IFCINDEXEDPOLYGONALFACE((483,482,476));
#1124=IFCINDEXEDPOLYGONALFACE((484,476,437));
#1125=IFCINDEXEDPOLYGONALFACE((485,486,483));
#1126=IFCINDEXEDPOLYGONALFACE((480,479,481));
#1127=IFCINDEXEDPOLYGONALFACE((487,488,481));
#1128=IFCINDEXEDPOLYGONALFACE((489,487,486));
#1129=IFCINDEXEDPOLYGONALFACE((490,489,485));
#1130=IFCINDEXEDPOLYGONALFACE((491,492,488));
#1131=IFCINDEXEDPOLYGONALFACE((457,493,466));
#1132=IFCINDEXEDPOLYGONALFACE((473,494,493));
#1133=IFCINDEXEDPOLYGONALFACE((472,478,492));
#1134=IFCINDEXEDPOLYGONALFACE((494,473,472));
#1135=IFCINDEXEDPOLYGONALFACE((495,496,467));
#1136=IFCINDEXEDPOLYGONALFACE((489,490,497));
#1137=IFCINDEXEDPOLYGONALFACE((498,499,495));
#1138=IFCINDEXEDPOLYGONALFACE((487,499,498));
#1139=IFCINDEXEDPOLYGONALFACE((497,496,495));
#1140=IFCINDEXEDPOLYGONALFACE((497,490,500));
#1141=IFCINDEXEDPOLYGONALFACE((501,502,471));
#1142=IFCINDEXEDPOLYGONALFACE((503,504,505));
#1143=IFCINDEXEDPOLYGONALFACE((504,503,484));
#1144=IFCINDEXEDPOLYGONALFACE((506,505,500));
#1145=IFCINDEXEDPOLYGONALFACE((505,504,507));
#1146=IFCINDEXEDPOLYGONALFACE((508,509,501));
#1147=IFCINDEXEDPOLYGONALFACE((507,504,502));
#1148=IFCINDEXEDPOLYGONALFACE((510,507,501));
#1149=IFCINDEXEDPOLYGONALFACE((509,508,511));
#1150=IFCINDEXEDPOLYGONALFACE((512,513,507));
#1151=IFCINDEXEDPOLYGONALFACE((512,510,514));
#1152=IFCINDEXEDPOLYGONALFACE((515,500,513));
#1153=IFCINDEXEDPOLYGONALFACE((516,517,467));
#1154=IFCINDEXEDPOLYGONALFACE((496,497,515));
#1155=IFCINDEXEDPOLYGONALFACE((516,518,519));
#1156=IFCINDEXEDPOLYGONALFACE((520,521,519));
#1157=IFCINDEXEDPOLYGONALFACE((520,512,522));
#1158=IFCINDEXEDPOLYGONALFACE((523,524,521));
#1159=IFCINDEXEDPOLYGONALFACE((525,519,521));
#1160=IFCINDEXEDPOLYGONALFACE((517,516,525));
#1161=IFCINDEXEDPOLYGONALFACE((524,526,527));
#1162=IFCINDEXEDPOLYGONALFACE((517,528,468));
#1163=IFCINDEXEDPOLYGONALFACE((529,530,463));
#1164=IFCINDEXEDPOLYGONALFACE((531,530,529));
#1165=IFCINDEXEDPOLYGONALFACE((528,517,532));
#1166=IFCINDEXEDPOLYGONALFACE((528,533,529));
#1167=IFCINDEXEDPOLYGONALFACE((527,526,534));
#1168=IFCINDEXEDPOLYGONALFACE((533,535,536));
#1169=IFCINDEXEDPOLYGONALFACE((537,536,535));
#1170=IFCINDEXEDPOLYGONALFACE((530,538,539));
#1171=IFCINDEXEDPOLYGONALFACE((434,539,16));
#1172=IFCINDEXEDPOLYGONALFACE((463,539,434));
#1173=IFCINDEXEDPOLYGONALFACE((22,16,539));
#1174=IFCINDEXEDPOLYGONALFACE((530,531,540));
#1175=IFCINDEXEDPOLYGONALFACE((536,537,541));
#1176=IFCINDEXEDPOLYGONALFACE((538,540,23));
#1177=IFCINDEXEDPOLYGONALFACE((542,23,540));
#1178=IFCINDEXEDPOLYGONALFACE((540,541,542));
#1179=IFCINDEXEDPOLYGONALFACE((23,22,538));
#1180=IFCINDEXEDPOLYGONALFACE((540,531,536));
#1181=IFCINDEXEDPOLYGONALFACE((541,540,536));
#1182=IFCINDEXEDPOLYGONALFACE((540,538,530));
#1183=IFCINDEXEDPOLYGONALFACE((539,538,22));
#1184=IFCINDEXEDPOLYGONALFACE((434,433,463));
#1185=IFCINDEXEDPOLYGONALFACE((2,430,434));
#1186=IFCINDEXEDPOLYGONALFACE((16,2,434));
#1187=IFCINDEXEDPOLYGONALFACE((539,463,530));
#1188=IFCINDEXEDPOLYGONALFACE((535,534,537));
#1189=IFCINDEXEDPOLYGONALFACE((536,531,533));
#1190=IFCINDEXEDPOLYGONALFACE((535,532,527));
#1191=IFCINDEXEDPOLYGONALFACE((534,535,527));
#1192=IFCINDEXEDPOLYGONALFACE((529,468,528));
#1193=IFCINDEXEDPOLYGONALFACE((535,533,528));
#1194=IFCINDEXEDPOLYGONALFACE((532,535,528));
#1195=IFCINDEXEDPOLYGONALFACE((529,533,531));
#1196=IFCINDEXEDPOLYGONALFACE((463,462,529));
#1197=IFCINDEXEDPOLYGONALFACE((468,467,517));
#1198=IFCINDEXEDPOLYGONALFACE((527,521,524));
#1199=IFCINDEXEDPOLYGONALFACE((525,532,517));
#1200=IFCINDEXEDPOLYGONALFACE((527,532,525));
#1201=IFCINDEXEDPOLYGONALFACE((521,527,525));
#1202=IFCINDEXEDPOLYGONALFACE((521,520,523));
#1203=IFCINDEXEDPOLYGONALFACE((522,523,520));
#1204=IFCINDEXEDPOLYGONALFACE((520,519,513));
#1205=IFCINDEXEDPOLYGONALFACE((513,512,520));
#1206=IFCINDEXEDPOLYGONALFACE((519,525,516));
#1207=IFCINDEXEDPOLYGONALFACE((515,518,496));
#1208=IFCINDEXEDPOLYGONALFACE((516,467,496));
#1209=IFCINDEXEDPOLYGONALFACE((496,518,516));
#1210=IFCINDEXEDPOLYGONALFACE((515,513,519));
#1211=IFCINDEXEDPOLYGONALFACE((519,518,515));
#1212=IFCINDEXEDPOLYGONALFACE((514,522,512));
#1213=IFCINDEXEDPOLYGONALFACE((507,510,512));
#1214=IFCINDEXEDPOLYGONALFACE((511,543,509));
#1215=IFCINDEXEDPOLYGONALFACE((543,514,510));
#1216=IFCINDEXEDPOLYGONALFACE((510,509,543));
#1217=IFCINDEXEDPOLYGONALFACE((501,509,510));
#1218=IFCINDEXEDPOLYGONALFACE((502,501,507));
#1219=IFCINDEXEDPOLYGONALFACE((501,544,508));
#1220=IFCINDEXEDPOLYGONALFACE((513,500,505));
#1221=IFCINDEXEDPOLYGONALFACE((507,513,505));
#1222=IFCINDEXEDPOLYGONALFACE((500,490,506));
#1223=IFCINDEXEDPOLYGONALFACE((484,502,504));
#1224=IFCINDEXEDPOLYGONALFACE((505,506,503));
#1225=IFCINDEXEDPOLYGONALFACE((471,544,501));
#1226=IFCINDEXEDPOLYGONALFACE((500,515,497));
#1227=IFCINDEXEDPOLYGONALFACE((495,499,497));
#1228=IFCINDEXEDPOLYGONALFACE((498,488,487));
#1229=IFCINDEXEDPOLYGONALFACE((493,494,498));
#1230=IFCINDEXEDPOLYGONALFACE((495,493,498));
#1231=IFCINDEXEDPOLYGONALFACE((499,487,489));
#1232=IFCINDEXEDPOLYGONALFACE((497,499,489));
#1233=IFCINDEXEDPOLYGONALFACE((466,493,495));
#1234=IFCINDEXEDPOLYGONALFACE((467,466,495));
#1235=IFCINDEXEDPOLYGONALFACE((472,491,494));
#1236=IFCINDEXEDPOLYGONALFACE((492,491,472));
#1237=IFCINDEXEDPOLYGONALFACE((473,493,457));
#1238=IFCINDEXEDPOLYGONALFACE((457,474,473));
#1239=IFCINDEXEDPOLYGONALFACE((466,458,457));
#1240=IFCINDEXEDPOLYGONALFACE((498,494,491));
#1241=IFCINDEXEDPOLYGONALFACE((488,498,491));
#1242=IFCINDEXEDPOLYGONALFACE((485,506,490));
#1243=IFCINDEXEDPOLYGONALFACE((486,485,489));
#1244=IFCINDEXEDPOLYGONALFACE((481,486,487));
#1245=IFCINDEXEDPOLYGONALFACE((488,492,480));
#1246=IFCINDEXEDPOLYGONALFACE((481,488,480));
#1247=IFCINDEXEDPOLYGONALFACE((506,485,483));
#1248=IFCINDEXEDPOLYGONALFACE((483,503,506));
#1249=IFCINDEXEDPOLYGONALFACE((437,469,484));
#1250=IFCINDEXEDPOLYGONALFACE((483,476,484));
#1251=IFCINDEXEDPOLYGONALFACE((484,503,483));
#1252=IFCINDEXEDPOLYGONALFACE((481,482,483));
#1253=IFCINDEXEDPOLYGONALFACE((483,486,481));
#1254=IFCINDEXEDPOLYGONALFACE((480,492,478));
#1255=IFCINDEXEDPOLYGONALFACE((479,480,477));
#1256=IFCINDEXEDPOLYGONALFACE((477,449,451));
#1257=IFCINDEXEDPOLYGONALFACE((451,475,477));
#1258=IFCINDEXEDPOLYGONALFACE((476,453,441));
#1259=IFCINDEXEDPOLYGONALFACE((476,482,450));
#1260=IFCINDEXEDPOLYGONALFACE((482,479,475));
#1261=IFCINDEXEDPOLYGONALFACE((450,482,475));
#1262=IFCINDEXEDPOLYGONALFACE((436,470,469));
#1263=IFCINDEXEDPOLYGONALFACE((449,478,472));
#1264=IFCINDEXEDPOLYGONALFACE((474,449,472));
#1265=IFCINDEXEDPOLYGONALFACE((502,484,469));
#1266=IFCINDEXEDPOLYGONALFACE((471,502,469));
#1267=IFCINDEXEDPOLYGONALFACE((462,464,468));
#1268=IFCINDEXEDPOLYGONALFACE((468,529,462));
#1269=IFCINDEXEDPOLYGONALFACE((464,458,466));
#1270=IFCINDEXEDPOLYGONALFACE((468,464,466));
#1271=IFCINDEXEDPOLYGONALFACE((464,462,461));
#1272=IFCINDEXEDPOLYGONALFACE((460,459,465));
#1273=IFCINDEXEDPOLYGONALFACE((464,465,456));
#1274=IFCINDEXEDPOLYGONALFACE((433,460,461));
#1275=IFCINDEXEDPOLYGONALFACE((463,433,461));
#1276=IFCINDEXEDPOLYGONALFACE((439,438,459));
#1277=IFCINDEXEDPOLYGONALFACE((459,438,445));
#1278=IFCINDEXEDPOLYGONALFACE((424,423,439));
#1279=IFCINDEXEDPOLYGONALFACE((439,432,424));
#1280=IFCINDEXEDPOLYGONALFACE((433,432,439));
#1281=IFCINDEXEDPOLYGONALFACE((456,465,459));
#1282=IFCINDEXEDPOLYGONALFACE((458,456,455));
#1283=IFCINDEXEDPOLYGONALFACE((455,448,474));
#1284=IFCINDEXEDPOLYGONALFACE((474,457,455));
#1285=IFCINDEXEDPOLYGONALFACE((456,447,454));
#1286=IFCINDEXEDPOLYGONALFACE((444,443,451));
#1287=IFCINDEXEDPOLYGONALFACE((441,453,401));
#1288=IFCINDEXEDPOLYGONALFACE((453,450,452));
#1289=IFCINDEXEDPOLYGONALFACE((443,452,450));
#1290=IFCINDEXEDPOLYGONALFACE((449,474,448));
#1291=IFCINDEXEDPOLYGONALFACE((442,454,447));
#1292=IFCINDEXEDPOLYGONALFACE((446,415,442));
#1293=IFCINDEXEDPOLYGONALFACE((401,452,404));
#1294=IFCINDEXEDPOLYGONALFACE((443,416,404));
#1295=IFCINDEXEDPOLYGONALFACE((404,452,443));
#1296=IFCINDEXEDPOLYGONALFACE((442,416,448));
#1297=IFCINDEXEDPOLYGONALFACE((416,444,448));
#1298=IFCINDEXEDPOLYGONALFACE((448,454,442));
#1299=IFCINDEXEDPOLYGONALFACE((441,400,440));
#1300=IFCINDEXEDPOLYGONALFACE((438,423,446));
#1301=IFCINDEXEDPOLYGONALFACE((446,445,438));
#1302=IFCINDEXEDPOLYGONALFACE((437,441,435));
#1303=IFCINDEXEDPOLYGONALFACE((430,432,433));
#1304=IFCINDEXEDPOLYGONALFACE((427,426,431));
#1305=IFCINDEXEDPOLYGONALFACE((431,432,430));
#1306=IFCINDEXEDPOLYGONALFACE((430,429,431));
#1307=IFCINDEXEDPOLYGONALFACE((1,427,429));
#1308=IFCINDEXEDPOLYGONALFACE((2,1,429));
#1309=IFCINDEXEDPOLYGONALFACE((428,398,397));
#1310=IFCINDEXEDPOLYGONALFACE((426,428,403));
#1311=IFCINDEXEDPOLYGONALFACE((410,426,403));
#1312=IFCINDEXEDPOLYGONALFACE((428,427,399));
#1313=IFCINDEXEDPOLYGONALFACE((399,398,428));
#1314=IFCINDEXEDPOLYGONALFACE((122,399,427));
#1315=IFCINDEXEDPOLYGONALFACE((422,431,426));
#1316=IFCINDEXEDPOLYGONALFACE((424,432,422));
#1317=IFCINDEXEDPOLYGONALFACE((425,446,423));
#1318=IFCINDEXEDPOLYGONALFACE((421,418,417));
#1319=IFCINDEXEDPOLYGONALFACE((424,421,417));
#1320=IFCINDEXEDPOLYGONALFACE((421,419,412));
#1321=IFCINDEXEDPOLYGONALFACE((410,420,419));
#1322=IFCINDEXEDPOLYGONALFACE((422,410,419));
#1323=IFCINDEXEDPOLYGONALFACE((409,412,419));
#1324=IFCINDEXEDPOLYGONALFACE((414,425,417));
#1325=IFCINDEXEDPOLYGONALFACE((407,405,404));
#1326=IFCINDEXEDPOLYGONALFACE((415,413,407));
#1327=IFCINDEXEDPOLYGONALFACE((407,416,415));
#1328=IFCINDEXEDPOLYGONALFACE((415,446,425));
#1329=IFCINDEXEDPOLYGONALFACE((415,425,414));
#1330=IFCINDEXEDPOLYGONALFACE((412,413,414));
#1331=IFCINDEXEDPOLYGONALFACE((414,418,412));
#1332=IFCINDEXEDPOLYGONALFACE((408,420,410));
#1333=IFCINDEXEDPOLYGONALFACE((409,420,408));
#1334=IFCINDEXEDPOLYGONALFACE((408,411,381));
#1335=IFCINDEXEDPOLYGONALFACE((406,408,380));
#1336=IFCINDEXEDPOLYGONALFACE((407,413,378));
#1337=IFCINDEXEDPOLYGONALFACE((413,409,406));
#1338=IFCINDEXEDPOLYGONALFACE((378,413,406));
#1339=IFCINDEXEDPOLYGONALFACE((402,405,9));
#1340=IFCINDEXEDPOLYGONALFACE((9,545,402));
#1341=IFCINDEXEDPOLYGONALFACE((381,411,403));
#1342=IFCINDEXEDPOLYGONALFACE((400,402,545));
#1343=IFCINDEXEDPOLYGONALFACE((545,440,400));
#1344=IFCINDEXEDPOLYGONALFACE((122,121,383));
#1345=IFCINDEXEDPOLYGONALFACE((382,398,399));
#1346=IFCINDEXEDPOLYGONALFACE((369,381,397));
#1347=IFCINDEXEDPOLYGONALFACE((382,369,397));
#1348=IFCINDEXEDPOLYGONALFACE((384,383,121));
#1349=IFCINDEXEDPOLYGONALFACE((66,72,396));
#1350=IFCINDEXEDPOLYGONALFACE((76,5,386));
#1351=IFCINDEXEDPOLYGONALFACE((394,75,76));
#1352=IFCINDEXEDPOLYGONALFACE((75,394,393));
#1353=IFCINDEXEDPOLYGONALFACE((71,393,396));
#1354=IFCINDEXEDPOLYGONALFACE((389,370,384));
#1355=IFCINDEXEDPOLYGONALFACE((128,392,395));
#1356=IFCINDEXEDPOLYGONALFACE((391,394,395));
#1357=IFCINDEXEDPOLYGONALFACE((394,391,387));
#1358=IFCINDEXEDPOLYGONALFACE((127,367,392));
#1359=IFCINDEXEDPOLYGONALFACE((391,392,367));
#1360=IFCINDEXEDPOLYGONALFACE((388,387,391));
#1361=IFCINDEXEDPOLYGONALFACE((128,386,385));
#1362=IFCINDEXEDPOLYGONALFACE((396,393,387));
#1363=IFCINDEXEDPOLYGONALFACE((389,396,387));
#1364=IFCINDEXEDPOLYGONALFACE((386,5,4));
#1365=IFCINDEXEDPOLYGONALFACE((370,369,382));
#1366=IFCINDEXEDPOLYGONALFACE((384,370,382));
#1367=IFCINDEXEDPOLYGONALFACE((380,369,368));
#1368=IFCINDEXEDPOLYGONALFACE((368,379,380));
#1369=IFCINDEXEDPOLYGONALFACE((368,375,378));
#1370=IFCINDEXEDPOLYGONALFACE((7,377,374));
#1371=IFCINDEXEDPOLYGONALFACE((377,378,375));
#1372=IFCINDEXEDPOLYGONALFACE((371,374,373));
#1373=IFCINDEXEDPOLYGONALFACE((352,129,376));
#1374=IFCINDEXEDPOLYGONALFACE((372,371,376));
#1375=IFCINDEXEDPOLYGONALFACE((373,375,353));
#1376=IFCINDEXEDPOLYGONALFACE((353,352,373));
#1377=IFCINDEXEDPOLYGONALFACE((8,7,371));
#1378=IFCINDEXEDPOLYGONALFACE((368,370,353));
#1379=IFCINDEXEDPOLYGONALFACE((353,375,368));
#1380=IFCINDEXEDPOLYGONALFACE((134,365,366));
#1381=IFCINDEXEDPOLYGONALFACE((363,388,366));
#1382=IFCINDEXEDPOLYGONALFACE((365,134,133));
#1383=IFCINDEXEDPOLYGONALFACE((346,363,365));
#1384=IFCINDEXEDPOLYGONALFACE((134,367,127));
#1385=IFCINDEXEDPOLYGONALFACE((364,134,127));
#1386=IFCINDEXEDPOLYGONALFACE((389,388,363));
#1387=IFCINDEXEDPOLYGONALFACE((363,370,389));
#1388=IFCINDEXEDPOLYGONALFACE((353,370,363));
#1389=IFCINDEXEDPOLYGONALFACE((357,356,360));
#1390=IFCINDEXEDPOLYGONALFACE((361,334,136));
#1391=IFCINDEXEDPOLYGONALFACE((358,361,135));
#1392=IFCINDEXEDPOLYGONALFACE((359,358,135));
#1393=IFCINDEXEDPOLYGONALFACE((361,360,335));
#1394=IFCINDEXEDPOLYGONALFACE((335,334,361));
#1395=IFCINDEXEDPOLYGONALFACE((356,337,335));
#1396=IFCINDEXEDPOLYGONALFACE((354,360,358));
#1397=IFCINDEXEDPOLYGONALFACE((355,354,358));
#1398=IFCINDEXEDPOLYGONALFACE((356,351,341));
#1399=IFCINDEXEDPOLYGONALFACE((341,337,356));
#1400=IFCINDEXEDPOLYGONALFACE((129,352,351));
#1401=IFCINDEXEDPOLYGONALFACE((351,357,129));
#1402=IFCINDEXEDPOLYGONALFACE((129,357,354));
#1403=IFCINDEXEDPOLYGONALFACE((130,129,354));
#1404=IFCINDEXEDPOLYGONALFACE((351,353,346));
#1405=IFCINDEXEDPOLYGONALFACE((346,341,351));
#1406=IFCINDEXEDPOLYGONALFACE((140,266,347));
#1407=IFCINDEXEDPOLYGONALFACE((348,347,266));
#1408=IFCINDEXEDPOLYGONALFACE((350,133,132));
#1409=IFCINDEXEDPOLYGONALFACE((132,138,350));
#1410=IFCINDEXEDPOLYGONALFACE((133,350,347));
#1411=IFCINDEXEDPOLYGONALFACE((349,133,347));
#1412=IFCINDEXEDPOLYGONALFACE((343,346,348));
#1413=IFCINDEXEDPOLYGONALFACE((346,349,348));
#1414=IFCINDEXEDPOLYGONALFACE((348,268,343));
#1415=IFCINDEXEDPOLYGONALFACE((345,344,273));
#1416=IFCINDEXEDPOLYGONALFACE((273,272,345));
#1417=IFCINDEXEDPOLYGONALFACE((273,342,267));
#1418=IFCINDEXEDPOLYGONALFACE((267,270,273));
#1419=IFCINDEXEDPOLYGONALFACE((343,268,267));
#1420=IFCINDEXEDPOLYGONALFACE((344,318,340));
#1421=IFCINDEXEDPOLYGONALFACE((341,343,342));
#1422=IFCINDEXEDPOLYGONALFACE((341,342,344));
#1423=IFCINDEXEDPOLYGONALFACE((340,341,344));
#1424=IFCINDEXEDPOLYGONALFACE((339,338,319));
#1425=IFCINDEXEDPOLYGONALFACE((317,338,340));
#1426=IFCINDEXEDPOLYGONALFACE((340,318,317));
#1427=IFCINDEXEDPOLYGONALFACE((339,331,336));
#1428=IFCINDEXEDPOLYGONALFACE((337,340,339));
#1429=IFCINDEXEDPOLYGONALFACE((336,337,339));
#1430=IFCINDEXEDPOLYGONALFACE((335,336,332));
#1431=IFCINDEXEDPOLYGONALFACE((334,332,320));
#1432=IFCINDEXEDPOLYGONALFACE((321,320,332));
#1433=IFCINDEXEDPOLYGONALFACE((333,137,136));
#1434=IFCINDEXEDPOLYGONALFACE((336,331,330));
#1435=IFCINDEXEDPOLYGONALFACE((332,336,330));
#1436=IFCINDEXEDPOLYGONALFACE((331,319,323));
#1437=IFCINDEXEDPOLYGONALFACE((330,323,328));
#1438=IFCINDEXEDPOLYGONALFACE((319,304,303));
#1439=IFCINDEXEDPOLYGONALFACE((296,324,328));
#1440=IFCINDEXEDPOLYGONALFACE((329,328,324));
#1441=IFCINDEXEDPOLYGONALFACE((321,329,324));
#1442=IFCINDEXEDPOLYGONALFACE((275,274,327));
#1443=IFCINDEXEDPOLYGONALFACE((321,327,274));
#1444=IFCINDEXEDPOLYGONALFACE((322,321,274));
#1445=IFCINDEXEDPOLYGONALFACE((325,327,324));
#1446=IFCINDEXEDPOLYGONALFACE((297,328,323));
#1447=IFCINDEXEDPOLYGONALFACE((322,333,320));
#1448=IFCINDEXEDPOLYGONALFACE((338,317,316));
#1449=IFCINDEXEDPOLYGONALFACE((319,338,316));
#1450=IFCINDEXEDPOLYGONALFACE((318,345,314));
#1451=IFCINDEXEDPOLYGONALFACE((317,314,315));
#1452=IFCINDEXEDPOLYGONALFACE((315,311,309));
#1453=IFCINDEXEDPOLYGONALFACE((316,315,305));
#1454=IFCINDEXEDPOLYGONALFACE((300,302,316));
#1455=IFCINDEXEDPOLYGONALFACE((316,305,300));
#1456=IFCINDEXEDPOLYGONALFACE((314,312,311));
#1457=IFCINDEXEDPOLYGONALFACE((345,272,313));
#1458=IFCINDEXEDPOLYGONALFACE((314,345,313));
#1459=IFCINDEXEDPOLYGONALFACE((313,307,306));
#1460=IFCINDEXEDPOLYGONALFACE((307,272,250));
#1461=IFCINDEXEDPOLYGONALFACE((250,249,307));
#1462=IFCINDEXEDPOLYGONALFACE((306,247,245));
#1463=IFCINDEXEDPOLYGONALFACE((312,306,308));
#1464=IFCINDEXEDPOLYGONALFACE((312,308,310));
#1465=IFCINDEXEDPOLYGONALFACE((277,309,311));
#1466=IFCINDEXEDPOLYGONALFACE((278,277,310));
#1467=IFCINDEXEDPOLYGONALFACE((285,289,309));
#1468=IFCINDEXEDPOLYGONALFACE((244,310,308));
#1469=IFCINDEXEDPOLYGONALFACE((307,249,247));
#1470=IFCINDEXEDPOLYGONALFACE((301,305,309));
#1471=IFCINDEXEDPOLYGONALFACE((309,289,301));
#1472=IFCINDEXEDPOLYGONALFACE((298,303,304));
#1473=IFCINDEXEDPOLYGONALFACE((294,297,303));
#1474=IFCINDEXEDPOLYGONALFACE((299,298,302));
#1475=IFCINDEXEDPOLYGONALFACE((290,299,300));
#1476=IFCINDEXEDPOLYGONALFACE((293,295,299));
#1477=IFCINDEXEDPOLYGONALFACE((301,289,287));
#1478=IFCINDEXEDPOLYGONALFACE((290,301,287));
#1479=IFCINDEXEDPOLYGONALFACE((295,294,298));
#1480=IFCINDEXEDPOLYGONALFACE((291,325,296));
#1481=IFCINDEXEDPOLYGONALFACE((294,291,296));
#1482=IFCINDEXEDPOLYGONALFACE((279,291,294));
#1483=IFCINDEXEDPOLYGONALFACE((295,293,280));
#1484=IFCINDEXEDPOLYGONALFACE((280,279,295));
#1485=IFCINDEXEDPOLYGONALFACE((275,325,291));
#1486=IFCINDEXEDPOLYGONALFACE((292,276,275));
#1487=IFCINDEXEDPOLYGONALFACE((291,292,275));
#1488=IFCINDEXEDPOLYGONALFACE((284,293,290));
#1489=IFCINDEXEDPOLYGONALFACE((288,287,289));
#1490=IFCINDEXEDPOLYGONALFACE((282,286,287));
#1491=IFCINDEXEDPOLYGONALFACE((142,284,286));
#1492=IFCINDEXEDPOLYGONALFACE((228,282,288));
#1493=IFCINDEXEDPOLYGONALFACE((228,288,285));
#1494=IFCINDEXEDPOLYGONALFACE((141,280,284));
#1495=IFCINDEXEDPOLYGONALFACE((281,280,141));
#1496=IFCINDEXEDPOLYGONALFACE((143,142,282));
#1497=IFCINDEXEDPOLYGONALFACE((281,292,279));
#1498=IFCINDEXEDPOLYGONALFACE((230,285,277));
#1499=IFCINDEXEDPOLYGONALFACE((276,326,274));
#1500=IFCINDEXEDPOLYGONALFACE((273,270,271));
#1501=IFCINDEXEDPOLYGONALFACE((273,271,250));
#1502=IFCINDEXEDPOLYGONALFACE((270,269,251));
#1503=IFCINDEXEDPOLYGONALFACE((269,264,263));
#1504=IFCINDEXEDPOLYGONALFACE((248,271,251));
#1505=IFCINDEXEDPOLYGONALFACE((251,241,248));
#1506=IFCINDEXEDPOLYGONALFACE((269,267,265));
#1507=IFCINDEXEDPOLYGONALFACE((265,264,269));
#1508=IFCINDEXEDPOLYGONALFACE((268,266,265));
#1509=IFCINDEXEDPOLYGONALFACE((259,266,140));
#1510=IFCINDEXEDPOLYGONALFACE((140,253,259));
#1511=IFCINDEXEDPOLYGONALFACE((265,259,262));
#1512=IFCINDEXEDPOLYGONALFACE((263,260,215));
#1513=IFCINDEXEDPOLYGONALFACE((260,264,262));
#1514=IFCINDEXEDPOLYGONALFACE((262,261,260));
#1515=IFCINDEXEDPOLYGONALFACE((209,213,261));
#1516=IFCINDEXEDPOLYGONALFACE((254,262,258));
#1517=IFCINDEXEDPOLYGONALFACE((258,255,254));
#1518=IFCINDEXEDPOLYGONALFACE((260,261,213));
#1519=IFCINDEXEDPOLYGONALFACE((258,262,259));
#1520=IFCINDEXEDPOLYGONALFACE((257,258,253));
#1521=IFCINDEXEDPOLYGONALFACE((146,191,255));
#1522=IFCINDEXEDPOLYGONALFACE((146,255,258));
#1523=IFCINDEXEDPOLYGONALFACE((257,252,256));
#1524=IFCINDEXEDPOLYGONALFACE((191,209,254));
#1525=IFCINDEXEDPOLYGONALFACE((139,256,252));
#1526=IFCINDEXEDPOLYGONALFACE((140,139,252));
#1527=IFCINDEXEDPOLYGONALFACE((239,251,263));
#1528=IFCINDEXEDPOLYGONALFACE((263,218,239));
#1529=IFCINDEXEDPOLYGONALFACE((250,248,242));
#1530=IFCINDEXEDPOLYGONALFACE((249,242,243));
#1531=IFCINDEXEDPOLYGONALFACE((248,241,240));
#1532=IFCINDEXEDPOLYGONALFACE((246,245,247));
#1533=IFCINDEXEDPOLYGONALFACE((231,244,245));
#1534=IFCINDEXEDPOLYGONALFACE((226,230,278));
#1535=IFCINDEXEDPOLYGONALFACE((226,278,244));
#1536=IFCINDEXEDPOLYGONALFACE((232,231,246));
#1537=IFCINDEXEDPOLYGONALFACE((232,246,243));
#1538=IFCINDEXEDPOLYGONALFACE((237,243,242));
#1539=IFCINDEXEDPOLYGONALFACE((241,239,238));
#1540=IFCINDEXEDPOLYGONALFACE((238,217,216));
#1541=IFCINDEXEDPOLYGONALFACE((240,238,234));
#1542=IFCINDEXEDPOLYGONALFACE((240,234,233));
#1543=IFCINDEXEDPOLYGONALFACE((239,218,217));
#1544=IFCINDEXEDPOLYGONALFACE((233,206,236));
#1545=IFCINDEXEDPOLYGONALFACE((237,233,235));
#1546=IFCINDEXEDPOLYGONALFACE((237,235,219));
#1547=IFCINDEXEDPOLYGONALFACE((224,227,232));
#1548=IFCINDEXEDPOLYGONALFACE((205,219,235));
#1549=IFCINDEXEDPOLYGONALFACE((234,216,206));
#1550=IFCINDEXEDPOLYGONALFACE((227,226,231));
#1551=IFCINDEXEDPOLYGONALFACE((225,228,230));
#1552=IFCINDEXEDPOLYGONALFACE((229,143,228));
#1553=IFCINDEXEDPOLYGONALFACE((220,229,225));
#1554=IFCINDEXEDPOLYGONALFACE((227,220,225));
#1555=IFCINDEXEDPOLYGONALFACE((180,223,224));
#1556=IFCINDEXEDPOLYGONALFACE((227,224,223));
#1557=IFCINDEXEDPOLYGONALFACE((220,227,223));
#1558=IFCINDEXEDPOLYGONALFACE((147,546,221));
#1559=IFCINDEXEDPOLYGONALFACE((148,147,223));
#1560=IFCINDEXEDPOLYGONALFACE((222,143,229));
#1561=IFCINDEXEDPOLYGONALFACE((222,229,220));
#1562=IFCINDEXEDPOLYGONALFACE((181,224,219));
#1563=IFCINDEXEDPOLYGONALFACE((218,215,214));
#1564=IFCINDEXEDPOLYGONALFACE((217,214,208));
#1565=IFCINDEXEDPOLYGONALFACE((214,212,210));
#1566=IFCINDEXEDPOLYGONALFACE((216,208,207));
#1567=IFCINDEXEDPOLYGONALFACE((215,213,212));
#1568=IFCINDEXEDPOLYGONALFACE((192,212,213));
#1569=IFCINDEXEDPOLYGONALFACE((211,210,212));
#1570=IFCINDEXEDPOLYGONALFACE((201,202,210));
#1571=IFCINDEXEDPOLYGONALFACE((190,192,209));
#1572=IFCINDEXEDPOLYGONALFACE((208,210,202));
#1573=IFCINDEXEDPOLYGONALFACE((207,202,200));
#1574=IFCINDEXEDPOLYGONALFACE((206,207,204));
#1575=IFCINDEXEDPOLYGONALFACE((203,205,236));
#1576=IFCINDEXEDPOLYGONALFACE((203,236,206));
#1577=IFCINDEXEDPOLYGONALFACE((203,199,196));
#1578=IFCINDEXEDPOLYGONALFACE((205,203,193));
#1579=IFCINDEXEDPOLYGONALFACE((178,179,205));
#1580=IFCINDEXEDPOLYGONALFACE((205,193,178));
#1581=IFCINDEXEDPOLYGONALFACE((204,200,199));
#1582=IFCINDEXEDPOLYGONALFACE((194,200,202));
#1583=IFCINDEXEDPOLYGONALFACE((195,194,201));
#1584=IFCINDEXEDPOLYGONALFACE((197,199,200));
#1585=IFCINDEXEDPOLYGONALFACE((182,196,199));
#1586=IFCINDEXEDPOLYGONALFACE((198,182,197));
#1587=IFCINDEXEDPOLYGONALFACE((170,172,196));
#1588=IFCINDEXEDPOLYGONALFACE((196,182,170));
#1589=IFCINDEXEDPOLYGONALFACE((186,197,194));
#1590=IFCINDEXEDPOLYGONALFACE((173,177,193));
#1591=IFCINDEXEDPOLYGONALFACE((193,196,173));
#1592=IFCINDEXEDPOLYGONALFACE((187,201,211));
#1593=IFCINDEXEDPOLYGONALFACE((187,211,192));
#1594=IFCINDEXEDPOLYGONALFACE((185,187,190));
#1595=IFCINDEXEDPOLYGONALFACE((188,191,146));
#1596=IFCINDEXEDPOLYGONALFACE((146,189,188));
#1597=IFCINDEXEDPOLYGONALFACE((150,185,188));
#1598=IFCINDEXEDPOLYGONALFACE((183,186,195));
#1599=IFCINDEXEDPOLYGONALFACE((183,195,187));
#1600=IFCINDEXEDPOLYGONALFACE((159,163,198));
#1601=IFCINDEXEDPOLYGONALFACE((159,198,186));
#1602=IFCINDEXEDPOLYGONALFACE((184,183,185));
#1603=IFCINDEXEDPOLYGONALFACE((158,159,183));
#1604=IFCINDEXEDPOLYGONALFACE((151,150,189));
#1605=IFCINDEXEDPOLYGONALFACE((151,189,146));
#1606=IFCINDEXEDPOLYGONALFACE((163,171,182));
#1607=IFCINDEXEDPOLYGONALFACE((182,198,163));
#1608=IFCINDEXEDPOLYGONALFACE((174,148,180));
#1609=IFCINDEXEDPOLYGONALFACE((179,174,180));
#1610=IFCINDEXEDPOLYGONALFACE((175,174,179));
#1611=IFCINDEXEDPOLYGONALFACE((164,175,178));
#1612=IFCINDEXEDPOLYGONALFACE((165,164,177));
#1613=IFCINDEXEDPOLYGONALFACE((148,174,176));
#1614=IFCINDEXEDPOLYGONALFACE((176,149,148));
#1615=IFCINDEXEDPOLYGONALFACE((167,165,173));
#1616=IFCINDEXEDPOLYGONALFACE((168,167,172));
#1617=IFCINDEXEDPOLYGONALFACE((153,168,170));
#1618=IFCINDEXEDPOLYGONALFACE((171,163,161));
#1619=IFCINDEXEDPOLYGONALFACE((153,171,161));
#1620=IFCINDEXEDPOLYGONALFACE((165,167,169));
#1621=IFCINDEXEDPOLYGONALFACE((169,166,165));
#1622=IFCINDEXEDPOLYGONALFACE((175,164,166));
#1623=IFCINDEXEDPOLYGONALFACE((166,176,175));
#1624=IFCINDEXEDPOLYGONALFACE((162,161,163));
#1625=IFCINDEXEDPOLYGONALFACE((156,154,161));
#1626=IFCINDEXEDPOLYGONALFACE((160,155,154));
#1627=IFCINDEXEDPOLYGONALFACE((10,156,162));
#1628=IFCINDEXEDPOLYGONALFACE((10,162,159));
#1629=IFCINDEXEDPOLYGONALFACE((11,10,158));
#1630=IFCINDEXEDPOLYGONALFACE((157,160,156));
#1631=IFCINDEXEDPOLYGONALFACE((168,153,155));
#1632=IFCINDEXEDPOLYGONALFACE((155,169,168));
#1633=IFCINDEXEDPOLYGONALFACE((152,158,184));
#1634=IFCINDEXEDPOLYGONALFACE((152,184,150));
#1635=IFCINDEXEDPOLYGONALFACE((149,546,147));
#1636=IFCINDEXEDPOLYGONALFACE((146,257,144));
#1637=IFCINDEXEDPOLYGONALFACE((141,143,222));
#1638=IFCINDEXEDPOLYGONALFACE((222,283,141));
#1639=IFCINDEXEDPOLYGONALFACE((140,350,138));
#1640=IFCINDEXEDPOLYGONALFACE((137,362,135));
#1641=IFCINDEXEDPOLYGONALFACE((134,364,132));
#1642=IFCINDEXEDPOLYGONALFACE((131,376,129));
#1643=IFCINDEXEDPOLYGONALFACE((128,390,126));
#1644=IFCINDEXEDPOLYGONALFACE((124,31,547));
#1645=IFCINDEXEDPOLYGONALFACE((547,125,124));
#1646=IFCINDEXEDPOLYGONALFACE((119,117,124));
#1647=IFCINDEXEDPOLYGONALFACE((117,116,123));
#1648=IFCINDEXEDPOLYGONALFACE((123,120,13));
#1649=IFCINDEXEDPOLYGONALFACE((17,13,120));
#1650=IFCINDEXEDPOLYGONALFACE((80,65,64));
#1651=IFCINDEXEDPOLYGONALFACE((17,64,121));
#1652=IFCINDEXEDPOLYGONALFACE((118,120,123));
#1653=IFCINDEXEDPOLYGONALFACE((123,116,118));
#1654=IFCINDEXEDPOLYGONALFACE((119,548,88));
#1655=IFCINDEXEDPOLYGONALFACE((115,82,118));
#1656=IFCINDEXEDPOLYGONALFACE((115,86,83));
#1657=IFCINDEXEDPOLYGONALFACE((80,118,82));
#1658=IFCINDEXEDPOLYGONALFACE((115,117,88));
#1659=IFCINDEXEDPOLYGONALFACE((88,86,115));
#1660=IFCINDEXEDPOLYGONALFACE((92,87,113));
#1661=IFCINDEXEDPOLYGONALFACE((111,92,114));
#1662=IFCINDEXEDPOLYGONALFACE((114,549,111));
#1663=IFCINDEXEDPOLYGONALFACE((112,109,107));
#1664=IFCINDEXEDPOLYGONALFACE((549,112,110));
#1665=IFCINDEXEDPOLYGONALFACE((111,549,110));
#1666=IFCINDEXEDPOLYGONALFACE((110,107,101));
#1667=IFCINDEXEDPOLYGONALFACE((109,108,103));
#1668=IFCINDEXEDPOLYGONALFACE((108,550,104));
#1669=IFCINDEXEDPOLYGONALFACE((107,103,106));
#1670=IFCINDEXEDPOLYGONALFACE((96,95,106));
#1671=IFCINDEXEDPOLYGONALFACE((101,106,95));
#1672=IFCINDEXEDPOLYGONALFACE((102,101,95));
#1673=IFCINDEXEDPOLYGONALFACE((105,106,103));
#1674=IFCINDEXEDPOLYGONALFACE((100,102,93));
#1675=IFCINDEXEDPOLYGONALFACE((93,111,100));
#1676=IFCINDEXEDPOLYGONALFACE((89,102,99));
#1677=IFCINDEXEDPOLYGONALFACE((90,89,99));
#1678=IFCINDEXEDPOLYGONALFACE((89,94,93));
#1679=IFCINDEXEDPOLYGONALFACE((93,102,89));
#1680=IFCINDEXEDPOLYGONALFACE((97,99,95));
#1681=IFCINDEXEDPOLYGONALFACE((85,87,92));
#1682=IFCINDEXEDPOLYGONALFACE((94,85,92));
#1683=IFCINDEXEDPOLYGONALFACE((91,98,89));
#1684=IFCINDEXEDPOLYGONALFACE((84,86,88));
#1685=IFCINDEXEDPOLYGONALFACE((88,85,84));
#1686=IFCINDEXEDPOLYGONALFACE((87,88,548));
#1687=IFCINDEXEDPOLYGONALFACE((548,113,87));
#1688=IFCINDEXEDPOLYGONALFACE((68,78,84));
#1689=IFCINDEXEDPOLYGONALFACE((78,77,83));
#1690=IFCINDEXEDPOLYGONALFACE((84,78,83));
#1691=IFCINDEXEDPOLYGONALFACE((98,91,551));
#1692=IFCINDEXEDPOLYGONALFACE((69,94,98));
#1693=IFCINDEXEDPOLYGONALFACE((551,69,98));
#1694=IFCINDEXEDPOLYGONALFACE((85,94,69));
#1695=IFCINDEXEDPOLYGONALFACE((77,81,82));
#1696=IFCINDEXEDPOLYGONALFACE((72,66,65));
#1697=IFCINDEXEDPOLYGONALFACE((81,72,65));
#1698=IFCINDEXEDPOLYGONALFACE((67,6,79));
#1699=IFCINDEXEDPOLYGONALFACE((78,68,79));
#1700=IFCINDEXEDPOLYGONALFACE((74,70,77));
#1701=IFCINDEXEDPOLYGONALFACE((76,79,6));
#1702=IFCINDEXEDPOLYGONALFACE((74,79,76));
#1703=IFCINDEXEDPOLYGONALFACE((75,71,70));
#1704=IFCINDEXEDPOLYGONALFACE((73,552,6));
#1705=IFCINDEXEDPOLYGONALFACE((70,72,81));
#1706=IFCINDEXEDPOLYGONALFACE((81,77,70));
#1707=IFCINDEXEDPOLYGONALFACE((67,69,551));
#1708=IFCINDEXEDPOLYGONALFACE((551,73,67));
#1709=IFCINDEXEDPOLYGONALFACE((66,121,64));
#1710=IFCINDEXEDPOLYGONALFACE((61,60,58));
#1711=IFCINDEXEDPOLYGONALFACE((57,56,60));
#1712=IFCINDEXEDPOLYGONALFACE((62,57,60));
#1713=IFCINDEXEDPOLYGONALFACE((59,58,60));
#1714=IFCINDEXEDPOLYGONALFACE((58,52,53));
#1715=IFCINDEXEDPOLYGONALFACE((53,63,58));
#1716=IFCINDEXEDPOLYGONALFACE((54,59,56));
#1717=IFCINDEXEDPOLYGONALFACE((55,54,56));
#1718=IFCINDEXEDPOLYGONALFACE((51,49,54));
#1719=IFCINDEXEDPOLYGONALFACE((52,59,54));
#1720=IFCINDEXEDPOLYGONALFACE((53,52,43));
#1721=IFCINDEXEDPOLYGONALFACE((52,46,40));
#1722=IFCINDEXEDPOLYGONALFACE((46,54,49));
#1723=IFCINDEXEDPOLYGONALFACE((51,50,47));
#1724=IFCINDEXEDPOLYGONALFACE((50,35,34));
#1725=IFCINDEXEDPOLYGONALFACE((49,47,37));
#1726=IFCINDEXEDPOLYGONALFACE((45,48,37));
#1727=IFCINDEXEDPOLYGONALFACE((38,37,47));
#1728=IFCINDEXEDPOLYGONALFACE((46,48,45));
#1729=IFCINDEXEDPOLYGONALFACE((43,42,41));
#1730=IFCINDEXEDPOLYGONALFACE((43,40,39));
#1731=IFCINDEXEDPOLYGONALFACE((42,27,28));
#1732=IFCINDEXEDPOLYGONALFACE((27,42,39));
#1733=IFCINDEXEDPOLYGONALFACE((40,45,29));
#1734=IFCINDEXEDPOLYGONALFACE((36,30,29));
#1735=IFCINDEXEDPOLYGONALFACE((29,45,36));
#1736=IFCINDEXEDPOLYGONALFACE((26,39,29));
#1737=IFCINDEXEDPOLYGONALFACE((38,33,36));
#1738=IFCINDEXEDPOLYGONALFACE((34,32,15));
#1739=IFCINDEXEDPOLYGONALFACE((15,33,38));
#1740=IFCINDEXEDPOLYGONALFACE((15,38,34));
#1741=IFCINDEXEDPOLYGONALFACE((14,20,30));
#1742=IFCINDEXEDPOLYGONALFACE((15,14,30));
#1743=IFCINDEXEDPOLYGONALFACE((32,547,31));
#1744=IFCINDEXEDPOLYGONALFACE((20,19,29));
#1745=IFCINDEXEDPOLYGONALFACE((27,24,25));
#1746=IFCINDEXEDPOLYGONALFACE((26,21,24));
#1747=IFCINDEXEDPOLYGONALFACE((26,19,18));
#1748=IFCINDEXEDPOLYGONALFACE((23,542,25));
#1749=IFCINDEXEDPOLYGONALFACE((24,21,22));
#1750=IFCINDEXEDPOLYGONALFACE((16,22,21));
#1751=IFCINDEXEDPOLYGONALFACE((20,3,18));
#1752=IFCINDEXEDPOLYGONALFACE((14,17,3));
#1753=IFCINDEXEDPOLYGONALFACE((3,20,14));
#1754=IFCINDEXEDPOLYGONALFACE((16,18,3));
#1755=IFCINDEXEDPOLYGONALFACE((15,31,13));
#1756=IFCINDEXEDPOLYGONALFACE((12,157,10));
#1757=IFCINDEXEDPOLYGONALFACE((377,7,405));
#1758=IFCINDEXEDPOLYGONALFACE((405,407,377));
#1759=IFCINDEXEDPOLYGONALFACE((9,405,7));
#1760=IFCINDEXEDPOLYGONALFACE((6,552,4));
#1761=IFCINDEXEDPOLYGONALFACE((17,122,1));
#1762=IFCINDEXEDPOLYGONALFACE((3,17,1));
#1763=IFCINDEXEDPOLYGONALFACE((8,4,3,9));
#1764=IFCINDEXEDPOLYGONALFACE((1,10,11,2));
#1765=IFCINDEXEDPOLYGONALFACE((1,2,3,4));
#1766=IFCINDEXEDPOLYGONALFACE((4,3,5,6));
#1767=IFCINDEXEDPOLYGONALFACE((2,7,8,3));
#1768=IFCINDEXEDPOLYGONALFACE((3,8,9,5));
#1769=IFCINDEXEDPOLYGONALFACE((10,11,12));
#1770=IFCINDEXEDPOLYGONALFACE((10,12,1,4,13));
#1771=IFCINDEXEDPOLYGONALFACE((14,12,15));
#1772=IFCINDEXEDPOLYGONALFACE((16,17,2,1));
#1773=IFCINDEXEDPOLYGONALFACE((6,5,18,19));
#1774=IFCINDEXEDPOLYGONALFACE((20,13,4,6,21));
#1775=IFCINDEXEDPOLYGONALFACE((20,22,13));
#1776=IFCINDEXEDPOLYGONALFACE((10,13,11));
#1777=IFCINDEXEDPOLYGONALFACE((7,23,24,8));
#1778=IFCINDEXEDPOLYGONALFACE((17,25,7,2));
#1779=IFCINDEXEDPOLYGONALFACE((8,24,26,9));
#1780=IFCINDEXEDPOLYGONALFACE((5,9,27,18));
#1781=IFCINDEXEDPOLYGONALFACE((15,12,11));
#1782=IFCINDEXEDPOLYGONALFACE((14,15,28));
#1783=IFCINDEXEDPOLYGONALFACE((14,28,16,1,12));
#1784=IFCINDEXEDPOLYGONALFACE((29,28,30));
#1785=IFCINDEXEDPOLYGONALFACE((31,32,17,16));
#1786=IFCINDEXEDPOLYGONALFACE((19,18,33,34));
#1787=IFCINDEXEDPOLYGONALFACE((35,21,6,19,36));
#1788=IFCINDEXEDPOLYGONALFACE((35,37,21));
#1789=IFCINDEXEDPOLYGONALFACE((20,21,22));
#1790=IFCINDEXEDPOLYGONALFACE((11,13,22));
#1791=IFCINDEXEDPOLYGONALFACE((23,38,39,24));
#1792=IFCINDEXEDPOLYGONALFACE((25,40,23,7));
#1793=IFCINDEXEDPOLYGONALFACE((32,41,25,17));
#1794=IFCINDEXEDPOLYGONALFACE((24,39,42,26));
#1795=IFCINDEXEDPOLYGONALFACE((9,26,43,27));
#1796=IFCINDEXEDPOLYGONALFACE((18,27,44,33));
#1797=IFCINDEXEDPOLYGONALFACE((45,15,11,46));
#1798=IFCINDEXEDPOLYGONALFACE((30,28,15));
#1799=IFCINDEXEDPOLYGONALFACE((29,30,47));
#1800=IFCINDEXEDPOLYGONALFACE((29,47,31,16,28));
#1801=IFCINDEXEDPOLYGONALFACE((48,47,49));
#1802=IFCINDEXEDPOLYGONALFACE((50,51,32,31));
#1803=IFCINDEXEDPOLYGONALFACE((34,33,52,53));
#1804=IFCINDEXEDPOLYGONALFACE((54,36,19,34,55));
#1805=IFCINDEXEDPOLYGONALFACE((54,56,36));
#1806=IFCINDEXEDPOLYGONALFACE((35,36,37));
#1807=IFCINDEXEDPOLYGONALFACE((22,21,37));
#1808=IFCINDEXEDPOLYGONALFACE((46,11,22,57));
#1809=IFCINDEXEDPOLYGONALFACE((38,58,59,39));
#1810=IFCINDEXEDPOLYGONALFACE((40,60,38,23));
#1811=IFCINDEXEDPOLYGONALFACE((41,61,40,25));
#1812=IFCINDEXEDPOLYGONALFACE((51,62,41,32));
#1813=IFCINDEXEDPOLYGONALFACE((39,59,63,42));
#1814=IFCINDEXEDPOLYGONALFACE((26,42,64,43));
#1815=IFCINDEXEDPOLYGONALFACE((27,43,65,44));
#1816=IFCINDEXEDPOLYGONALFACE((33,44,66,52));
#1817=IFCINDEXEDPOLYGONALFACE((67,45,46,68));
#1818=IFCINDEXEDPOLYGONALFACE((69,30,15,45));
#1819=IFCINDEXEDPOLYGONALFACE((49,47,30));
#1820=IFCINDEXEDPOLYGONALFACE((48,49,70));
#1821=IFCINDEXEDPOLYGONALFACE((48,70,50,31,47));
#1822=IFCINDEXEDPOLYGONALFACE((71,70,72));
#1823=IFCINDEXEDPOLYGONALFACE((73,74,51,50));
#1824=IFCINDEXEDPOLYGONALFACE((53,52,75,76));
#1825=IFCINDEXEDPOLYGONALFACE((77,55,34,53,78));
#1826=IFCINDEXEDPOLYGONALFACE((77,79,55));
#1827=IFCINDEXEDPOLYGONALFACE((54,55,56));
#1828=IFCINDEXEDPOLYGONALFACE((37,36,56));
#1829=IFCINDEXEDPOLYGONALFACE((57,22,37,80));
#1830=IFCINDEXEDPOLYGONALFACE((68,46,57,81));
#1831=IFCINDEXEDPOLYGONALFACE((58,82,83,59));
#1832=IFCINDEXEDPOLYGONALFACE((60,84,58,38));
#1833=IFCINDEXEDPOLYGONALFACE((61,85,60,40));
#1834=IFCINDEXEDPOLYGONALFACE((62,86,61,41));
#1835=IFCINDEXEDPOLYGONALFACE((74,87,62,51));
#1836=IFCINDEXEDPOLYGONALFACE((59,83,88,63));
#1837=IFCINDEXEDPOLYGONALFACE((42,63,89,64));
#1838=IFCINDEXEDPOLYGONALFACE((43,64,90,65));
#1839=IFCINDEXEDPOLYGONALFACE((44,65,91,66));
#1840=IFCINDEXEDPOLYGONALFACE((52,66,92,75));
#1841=IFCINDEXEDPOLYGONALFACE((93,67,68,94));
#1842=IFCINDEXEDPOLYGONALFACE((95,69,45,67));
#1843=IFCINDEXEDPOLYGONALFACE((96,49,30,69));
#1844=IFCINDEXEDPOLYGONALFACE((72,70,49));
#1845=IFCINDEXEDPOLYGONALFACE((71,72,97));
#1846=IFCINDEXEDPOLYGONALFACE((71,97,73,50,70));
#1847=IFCINDEXEDPOLYGONALFACE((98,97,99));
#1848=IFCINDEXEDPOLYGONALFACE((100,101,74,73));
#1849=IFCINDEXEDPOLYGONALFACE((76,75,102,103));
#1850=IFCINDEXEDPOLYGONALFACE((104,78,53,76,105));
#1851=IFCINDEXEDPOLYGONALFACE((104,106,78));
#1852=IFCINDEXEDPOLYGONALFACE((77,78,79));
#1853=IFCINDEXEDPOLYGONALFACE((56,55,79));
#1854=IFCINDEXEDPOLYGONALFACE((80,37,56,107));
#1855=IFCINDEXEDPOLYGONALFACE((81,57,80,108));
#1856=IFCINDEXEDPOLYGONALFACE((94,68,81,109));
#1857=IFCINDEXEDPOLYGONALFACE((82,110,111,83));
#1858=IFCINDEXEDPOLYGONALFACE((84,112,82,58));
#1859=IFCINDEXEDPOLYGONALFACE((85,113,84,60));
#1860=IFCINDEXEDPOLYGONALFACE((86,114,85,61));
#1861=IFCINDEXEDPOLYGONALFACE((87,115,86,62));
#1862=IFCINDEXEDPOLYGONALFACE((101,116,87,74));
#1863=IFCINDEXEDPOLYGONALFACE((83,111,117,88));
#1864=IFCINDEXEDPOLYGONALFACE((63,88,118,89));
#1865=IFCINDEXEDPOLYGONALFACE((64,89,119,90));
#1866=IFCINDEXEDPOLYGONALFACE((65,90,120,91));
#1867=IFCINDEXEDPOLYGONALFACE((66,91,121,92));
#1868=IFCINDEXEDPOLYGONALFACE((75,92,122,102));
#1869=IFCINDEXEDPOLYGONALFACE((123,93,94,124));
#1870=IFCINDEXEDPOLYGONALFACE((125,95,67,93));
#1871=IFCINDEXEDPOLYGONALFACE((126,96,69,95));
#1872=IFCINDEXEDPOLYGONALFACE((127,72,49,96));
#1873=IFCINDEXEDPOLYGONALFACE((99,97,72));
#1874=IFCINDEXEDPOLYGONALFACE((98,99,128));
#1875=IFCINDEXEDPOLYGONALFACE((98,128,100,73,97));
#1876=IFCINDEXEDPOLYGONALFACE((129,130,101,100));
#1877=IFCINDEXEDPOLYGONALFACE((103,102,131,132));
#1878=IFCINDEXEDPOLYGONALFACE((133,105,76,103,134));
#1879=IFCINDEXEDPOLYGONALFACE((104,105,106));
#1880=IFCINDEXEDPOLYGONALFACE((133,135,105));
#1881=IFCINDEXEDPOLYGONALFACE((79,78,106));
#1882=IFCINDEXEDPOLYGONALFACE((107,56,79,136));
#1883=IFCINDEXEDPOLYGONALFACE((108,80,107,137));
#1884=IFCINDEXEDPOLYGONALFACE((109,81,108,138));
#1885=IFCINDEXEDPOLYGONALFACE((124,94,109,139));
#1886=IFCINDEXEDPOLYGONALFACE((110,140,141,111));
#1887=IFCINDEXEDPOLYGONALFACE((112,142,110,82));
#1888=IFCINDEXEDPOLYGONALFACE((113,143,112,84));
#1889=IFCINDEXEDPOLYGONALFACE((114,144,113,85));
#1890=IFCINDEXEDPOLYGONALFACE((115,145,114,86));
#1891=IFCINDEXEDPOLYGONALFACE((116,146,115,87));
#1892=IFCINDEXEDPOLYGONALFACE((130,147,116,101));
#1893=IFCINDEXEDPOLYGONALFACE((111,141,148,117));
#1894=IFCINDEXEDPOLYGONALFACE((88,117,149,118));
#1895=IFCINDEXEDPOLYGONALFACE((89,118,150,119));
#1896=IFCINDEXEDPOLYGONALFACE((90,119,151,120));
#1897=IFCINDEXEDPOLYGONALFACE((91,120,152,121));
#1898=IFCINDEXEDPOLYGONALFACE((92,121,153,122));
#1899=IFCINDEXEDPOLYGONALFACE((102,122,154,131));
#1900=IFCINDEXEDPOLYGONALFACE((155,123,124,156));
#1901=IFCINDEXEDPOLYGONALFACE((157,125,93,123));
#1902=IFCINDEXEDPOLYGONALFACE((158,126,95,125));
#1903=IFCINDEXEDPOLYGONALFACE((159,127,96,126));
#1904=IFCINDEXEDPOLYGONALFACE((160,99,72,127));
#1905=IFCINDEXEDPOLYGONALFACE((161,128,99));
#1906=IFCINDEXEDPOLYGONALFACE((162,128,163));
#1907=IFCINDEXEDPOLYGONALFACE((162,164,129,100,128));
#1908=IFCINDEXEDPOLYGONALFACE((165,166,130,129));
#1909=IFCINDEXEDPOLYGONALFACE((132,131,167,168));
#1910=IFCINDEXEDPOLYGONALFACE((169,134,103,132,170));
#1911=IFCINDEXEDPOLYGONALFACE((133,134,135));
#1912=IFCINDEXEDPOLYGONALFACE((169,171,134));
#1913=IFCINDEXEDPOLYGONALFACE((135,106,105));
#1914=IFCINDEXEDPOLYGONALFACE((136,79,106,172));
#1915=IFCINDEXEDPOLYGONALFACE((137,107,136,173));
#1916=IFCINDEXEDPOLYGONALFACE((138,108,137,174));
#1917=IFCINDEXEDPOLYGONALFACE((139,109,138,175));
#1918=IFCINDEXEDPOLYGONALFACE((156,124,139,176));
#1919=IFCINDEXEDPOLYGONALFACE((140,177,178,141));
#1920=IFCINDEXEDPOLYGONALFACE((142,179,140,110));
#1921=IFCINDEXEDPOLYGONALFACE((143,180,142,112));
#1922=IFCINDEXEDPOLYGONALFACE((144,181,143,113));
#1923=IFCINDEXEDPOLYGONALFACE((145,182,144,114));
#1924=IFCINDEXEDPOLYGONALFACE((146,183,145,115));
#1925=IFCINDEXEDPOLYGONALFACE((147,184,146,116));
#1926=IFCINDEXEDPOLYGONALFACE((166,185,147,130));
#1927=IFCINDEXEDPOLYGONALFACE((141,178,186,148));
#1928=IFCINDEXEDPOLYGONALFACE((117,148,187,149));
#1929=IFCINDEXEDPOLYGONALFACE((118,149,188,150));
#1930=IFCINDEXEDPOLYGONALFACE((119,150,189,151));
#1931=IFCINDEXEDPOLYGONALFACE((120,151,190,152));
#1932=IFCINDEXEDPOLYGONALFACE((121,152,191,153));
#1933=IFCINDEXEDPOLYGONALFACE((122,153,192,154));
#1934=IFCINDEXEDPOLYGONALFACE((131,154,193,167));
#1935=IFCINDEXEDPOLYGONALFACE((194,155,156,195));
#1936=IFCINDEXEDPOLYGONALFACE((196,157,123,155));
#1937=IFCINDEXEDPOLYGONALFACE((197,158,125,157));
#1938=IFCINDEXEDPOLYGONALFACE((198,159,126,158));
#1939=IFCINDEXEDPOLYGONALFACE((199,160,127,159));
#1940=IFCINDEXEDPOLYGONALFACE((200,161,99,160));
#1941=IFCINDEXEDPOLYGONALFACE((161,163,128));
#1942=IFCINDEXEDPOLYGONALFACE((162,163,164));
#1943=IFCINDEXEDPOLYGONALFACE((201,164,202));
#1944=IFCINDEXEDPOLYGONALFACE((201,203,165,129,164));
#1945=IFCINDEXEDPOLYGONALFACE((204,205,166,165));
#1946=IFCINDEXEDPOLYGONALFACE((168,167,206,207));
#1947=IFCINDEXEDPOLYGONALFACE((208,170,132,168,209));
#1948=IFCINDEXEDPOLYGONALFACE((169,170,171));
#1949=IFCINDEXEDPOLYGONALFACE((208,210,170));
#1950=IFCINDEXEDPOLYGONALFACE((171,135,134));
#1951=IFCINDEXEDPOLYGONALFACE((172,106,135,211));
#1952=IFCINDEXEDPOLYGONALFACE((173,136,172,212));
#1953=IFCINDEXEDPOLYGONALFACE((174,137,173,213));
#1954=IFCINDEXEDPOLYGONALFACE((175,138,174,214));
#1955=IFCINDEXEDPOLYGONALFACE((176,139,175,215));
#1956=IFCINDEXEDPOLYGONALFACE((195,156,176,216));
#1957=IFCINDEXEDPOLYGONALFACE((177,217,218,178));
#1958=IFCINDEXEDPOLYGONALFACE((179,219,177,140));
#1959=IFCINDEXEDPOLYGONALFACE((180,220,179,142));
#1960=IFCINDEXEDPOLYGONALFACE((181,221,180,143));
#1961=IFCINDEXEDPOLYGONALFACE((182,222,181,144));
#1962=IFCINDEXEDPOLYGONALFACE((183,223,182,145));
#1963=IFCINDEXEDPOLYGONALFACE((184,224,183,146));
#1964=IFCINDEXEDPOLYGONALFACE((185,225,184,147));
#1965=IFCINDEXEDPOLYGONALFACE((205,226,185,166));
#1966=IFCINDEXEDPOLYGONALFACE((178,218,227,186));
#1967=IFCINDEXEDPOLYGONALFACE((148,186,228,187));
#1968=IFCINDEXEDPOLYGONALFACE((149,187,229,188));
#1969=IFCINDEXEDPOLYGONALFACE((150,188,230,189));
#1970=IFCINDEXEDPOLYGONALFACE((151,189,231,190));
#1971=IFCINDEXEDPOLYGONALFACE((152,190,232,191));
#1972=IFCINDEXEDPOLYGONALFACE((153,191,233,192));
#1973=IFCINDEXEDPOLYGONALFACE((154,192,234,193));
#1974=IFCINDEXEDPOLYGONALFACE((167,193,235,206));
#1975=IFCINDEXEDPOLYGONALFACE((236,194,195,237));
#1976=IFCINDEXEDPOLYGONALFACE((238,196,155,194));
#1977=IFCINDEXEDPOLYGONALFACE((239,197,157,196));
#1978=IFCINDEXEDPOLYGONALFACE((240,198,158,197));
#1979=IFCINDEXEDPOLYGONALFACE((241,199,159,198));
#1980=IFCINDEXEDPOLYGONALFACE((242,200,160,199));
#1981=IFCINDEXEDPOLYGONALFACE((243,163,161,200));
#1982=IFCINDEXEDPOLYGONALFACE((163,202,164));
#1983=IFCINDEXEDPOLYGONALFACE((201,202,203));
#1984=IFCINDEXEDPOLYGONALFACE((244,203,245));
#1985=IFCINDEXEDPOLYGONALFACE((244,246,204,165,203));
#1986=IFCINDEXEDPOLYGONALFACE((247,248,205,204));
#1987=IFCINDEXEDPOLYGONALFACE((207,206,249,250));
#1988=IFCINDEXEDPOLYGONALFACE((251,209,168,207,252));
#1989=IFCINDEXEDPOLYGONALFACE((208,209,210));
#1990=IFCINDEXEDPOLYGONALFACE((251,253,209));
#1991=IFCINDEXEDPOLYGONALFACE((210,171,170));
#1992=IFCINDEXEDPOLYGONALFACE((211,135,171,254));
#1993=IFCINDEXEDPOLYGONALFACE((212,172,211,255));
#1994=IFCINDEXEDPOLYGONALFACE((213,173,212,256));
#1995=IFCINDEXEDPOLYGONALFACE((214,174,213,257));
#1996=IFCINDEXEDPOLYGONALFACE((215,175,214,258));
#1997=IFCINDEXEDPOLYGONALFACE((216,176,215,259));
#1998=IFCINDEXEDPOLYGONALFACE((237,195,216,260));
#1999=IFCINDEXEDPOLYGONALFACE((261,262,263));
#2000=IFCINDEXEDPOLYGONALFACE((217,262,261,218));
#2001=IFCINDEXEDPOLYGONALFACE((219,264,217,177));
#2002=IFCINDEXEDPOLYGONALFACE((220,265,219,179));
#2003=IFCINDEXEDPOLYGONALFACE((221,266,220,180));
#2004=IFCINDEXEDPOLYGONALFACE((222,267,221,181));
#2005=IFCINDEXEDPOLYGONALFACE((223,268,222,182));
#2006=IFCINDEXEDPOLYGONALFACE((224,269,223,183));
#2007=IFCINDEXEDPOLYGONALFACE((225,270,224,184));
#2008=IFCINDEXEDPOLYGONALFACE((226,271,225,185));
#2009=IFCINDEXEDPOLYGONALFACE((248,272,226,205));
#2010=IFCINDEXEDPOLYGONALFACE((218,261,273,227));
#2011=IFCINDEXEDPOLYGONALFACE((186,227,274,228));
#2012=IFCINDEXEDPOLYGONALFACE((187,228,275,229));
#2013=IFCINDEXEDPOLYGONALFACE((188,229,276,230));
#2014=IFCINDEXEDPOLYGONALFACE((189,230,277,231));
#2015=IFCINDEXEDPOLYGONALFACE((190,231,278,232));
#2016=IFCINDEXEDPOLYGONALFACE((191,232,279,233));
#2017=IFCINDEXEDPOLYGONALFACE((192,233,280,234));
#2018=IFCINDEXEDPOLYGONALFACE((193,234,281,235));
#2019=IFCINDEXEDPOLYGONALFACE((206,235,282,249));
#2020=IFCINDEXEDPOLYGONALFACE((283,262,284));
#2021=IFCINDEXEDPOLYGONALFACE((283,263,262));
#2022=IFCINDEXEDPOLYGONALFACE((262,285,284));
#2023=IFCINDEXEDPOLYGONALFACE((286,236,237,287));
#2024=IFCINDEXEDPOLYGONALFACE((288,238,194,236));
#2025=IFCINDEXEDPOLYGONALFACE((289,239,196,238));
#2026=IFCINDEXEDPOLYGONALFACE((290,240,197,239));
#2027=IFCINDEXEDPOLYGONALFACE((291,241,198,240));
#2028=IFCINDEXEDPOLYGONALFACE((292,242,199,241));
#2029=IFCINDEXEDPOLYGONALFACE((293,243,200,242));
#2030=IFCINDEXEDPOLYGONALFACE((294,202,163,243));
#2031=IFCINDEXEDPOLYGONALFACE((202,245,203));
#2032=IFCINDEXEDPOLYGONALFACE((244,245,246));
#2033=IFCINDEXEDPOLYGONALFACE((295,246,296));
#2034=IFCINDEXEDPOLYGONALFACE((295,297,247,204,246));
#2035=IFCINDEXEDPOLYGONALFACE((298,299,248,247));
#2036=IFCINDEXEDPOLYGONALFACE((250,249,300,301));
#2037=IFCINDEXEDPOLYGONALFACE((302,252,207,250,303));
#2038=IFCINDEXEDPOLYGONALFACE((251,252,253));
#2039=IFCINDEXEDPOLYGONALFACE((302,304,252));
#2040=IFCINDEXEDPOLYGONALFACE((253,210,209));
#2041=IFCINDEXEDPOLYGONALFACE((254,171,210,305));
#2042=IFCINDEXEDPOLYGONALFACE((255,211,254,306));
#2043=IFCINDEXEDPOLYGONALFACE((256,212,255,307));
#2044=IFCINDEXEDPOLYGONALFACE((257,213,256,308));
#2045=IFCINDEXEDPOLYGONALFACE((258,214,257,309));
#2046=IFCINDEXEDPOLYGONALFACE((259,215,258,310));
#2047=IFCINDEXEDPOLYGONALFACE((260,216,259,311));
#2048=IFCINDEXEDPOLYGONALFACE((287,237,260,312));
#2049=IFCINDEXEDPOLYGONALFACE((313,261,263));
#2050=IFCINDEXEDPOLYGONALFACE((313,314,261));
#2051=IFCINDEXEDPOLYGONALFACE((273,261,314));
#2052=IFCINDEXEDPOLYGONALFACE((264,285,262,217));
#2053=IFCINDEXEDPOLYGONALFACE((265,315,264,219));
#2054=IFCINDEXEDPOLYGONALFACE((266,316,265,220));
#2055=IFCINDEXEDPOLYGONALFACE((267,317,266,221));
#2056=IFCINDEXEDPOLYGONALFACE((268,318,267,222));
#2057=IFCINDEXEDPOLYGONALFACE((269,319,268,223));
#2058=IFCINDEXEDPOLYGONALFACE((270,320,269,224));
#2059=IFCINDEXEDPOLYGONALFACE((271,321,270,225));
#2060=IFCINDEXEDPOLYGONALFACE((272,322,271,226));
#2061=IFCINDEXEDPOLYGONALFACE((299,323,272,248));
#2062=IFCINDEXEDPOLYGONALFACE((227,273,324,274));
#2063=IFCINDEXEDPOLYGONALFACE((228,274,325,275));
#2064=IFCINDEXEDPOLYGONALFACE((229,275,326,276));
#2065=IFCINDEXEDPOLYGONALFACE((230,276,327,277));
#2066=IFCINDEXEDPOLYGONALFACE((231,277,328,278));
#2067=IFCINDEXEDPOLYGONALFACE((232,278,329,279));
#2068=IFCINDEXEDPOLYGONALFACE((233,279,330,280));
#2069=IFCINDEXEDPOLYGONALFACE((234,280,331,281));
#2070=IFCINDEXEDPOLYGONALFACE((235,281,332,282));
#2071=IFCINDEXEDPOLYGONALFACE((249,282,333,300));
#2072=IFCINDEXEDPOLYGONALFACE((283,284,286,287,263));
#2073=IFCINDEXEDPOLYGONALFACE((334,285,335));
#2074=IFCINDEXEDPOLYGONALFACE((334,284,285));
#2075=IFCINDEXEDPOLYGONALFACE((285,336,335));
#2076=IFCINDEXEDPOLYGONALFACE((337,288,236,286));
#2077=IFCINDEXEDPOLYGONALFACE((338,289,238,288));
#2078=IFCINDEXEDPOLYGONALFACE((339,290,239,289));
#2079=IFCINDEXEDPOLYGONALFACE((340,291,240,290));
#2080=IFCINDEXEDPOLYGONALFACE((341,292,241,291));
#2081=IFCINDEXEDPOLYGONALFACE((342,293,242,292));
#2082=IFCINDEXEDPOLYGONALFACE((343,294,243,293));
#2083=IFCINDEXEDPOLYGONALFACE((344,245,202,294));
#2084=IFCINDEXEDPOLYGONALFACE((245,296,246));
#2085=IFCINDEXEDPOLYGONALFACE((295,296,297));
#2086=IFCINDEXEDPOLYGONALFACE((345,297,346));
#2087=IFCINDEXEDPOLYGONALFACE((345,347,298,247,297));
#2088=IFCINDEXEDPOLYGONALFACE((348,349,299,298));
#2089=IFCINDEXEDPOLYGONALFACE((301,300,350,351));
#2090=IFCINDEXEDPOLYGONALFACE((352,303,250,301,353));
#2091=IFCINDEXEDPOLYGONALFACE((302,303,304));
#2092=IFCINDEXEDPOLYGONALFACE((352,354,303));
#2093=IFCINDEXEDPOLYGONALFACE((304,253,252));
#2094=IFCINDEXEDPOLYGONALFACE((305,210,253,355));
#2095=IFCINDEXEDPOLYGONALFACE((306,254,305,356));
#2096=IFCINDEXEDPOLYGONALFACE((307,255,306,357));
#2097=IFCINDEXEDPOLYGONALFACE((308,256,307,358));
#2098=IFCINDEXEDPOLYGONALFACE((309,257,308,359));
#2099=IFCINDEXEDPOLYGONALFACE((310,258,309,360));
#2100=IFCINDEXEDPOLYGONALFACE((311,259,310,361));
#2101=IFCINDEXEDPOLYGONALFACE((312,260,311,362));
#2102=IFCINDEXEDPOLYGONALFACE((313,263,287,312,314));
#2103=IFCINDEXEDPOLYGONALFACE((363,273,314));
#2104=IFCINDEXEDPOLYGONALFACE((363,364,273));
#2105=IFCINDEXEDPOLYGONALFACE((324,273,364));
#2106=IFCINDEXEDPOLYGONALFACE((315,336,285,264));
#2107=IFCINDEXEDPOLYGONALFACE((316,365,315,265));
#2108=IFCINDEXEDPOLYGONALFACE((317,366,316,266));
#2109=IFCINDEXEDPOLYGONALFACE((318,367,317,267));
#2110=IFCINDEXEDPOLYGONALFACE((319,368,318,268));
#2111=IFCINDEXEDPOLYGONALFACE((320,369,319,269));
#2112=IFCINDEXEDPOLYGONALFACE((321,370,320,270));
#2113=IFCINDEXEDPOLYGONALFACE((322,371,321,271));
#2114=IFCINDEXEDPOLYGONALFACE((323,372,322,272));
#2115=IFCINDEXEDPOLYGONALFACE((349,373,323,299));
#2116=IFCINDEXEDPOLYGONALFACE((274,324,374,325));
#2117=IFCINDEXEDPOLYGONALFACE((275,325,375,326));
#2118=IFCINDEXEDPOLYGONALFACE((276,326,376,327));
#2119=IFCINDEXEDPOLYGONALFACE((277,327,377,328));
#2120=IFCINDEXEDPOLYGONALFACE((278,328,378,329));
#2121=IFCINDEXEDPOLYGONALFACE((279,329,379,330));
#2122=IFCINDEXEDPOLYGONALFACE((280,330,380,331));
#2123=IFCINDEXEDPOLYGONALFACE((281,331,381,332));
#2124=IFCINDEXEDPOLYGONALFACE((282,332,382,333));
#2125=IFCINDEXEDPOLYGONALFACE((300,333,383,350));
#2126=IFCINDEXEDPOLYGONALFACE((334,335,337,286,284));
#2127=IFCINDEXEDPOLYGONALFACE((384,336,385));
#2128=IFCINDEXEDPOLYGONALFACE((384,335,336));
#2129=IFCINDEXEDPOLYGONALFACE((336,386,385));
#2130=IFCINDEXEDPOLYGONALFACE((387,338,288,337));
#2131=IFCINDEXEDPOLYGONALFACE((388,339,289,338));
#2132=IFCINDEXEDPOLYGONALFACE((389,340,290,339));
#2133=IFCINDEXEDPOLYGONALFACE((390,341,291,340));
#2134=IFCINDEXEDPOLYGONALFACE((391,342,292,341));
#2135=IFCINDEXEDPOLYGONALFACE((392,343,293,342));
#2136=IFCINDEXEDPOLYGONALFACE((393,344,294,343));
#2137=IFCINDEXEDPOLYGONALFACE((394,296,245,344));
#2138=IFCINDEXEDPOLYGONALFACE((296,346,297));
#2139=IFCINDEXEDPOLYGONALFACE((345,346,347));
#2140=IFCINDEXEDPOLYGONALFACE((395,347,396));
#2141=IFCINDEXEDPOLYGONALFACE((395,397,348,298,347));
#2142=IFCINDEXEDPOLYGONALFACE((398,399,349,348));
#2143=IFCINDEXEDPOLYGONALFACE((351,350,400,401));
#2144=IFCINDEXEDPOLYGONALFACE((402,353,301,351,403));
#2145=IFCINDEXEDPOLYGONALFACE((352,353,354));
#2146=IFCINDEXEDPOLYGONALFACE((402,404,353));
#2147=IFCINDEXEDPOLYGONALFACE((354,304,303));
#2148=IFCINDEXEDPOLYGONALFACE((355,253,304,405));
#2149=IFCINDEXEDPOLYGONALFACE((356,305,355,406));
#2150=IFCINDEXEDPOLYGONALFACE((357,306,356,407));
#2151=IFCINDEXEDPOLYGONALFACE((358,307,357,408));
#2152=IFCINDEXEDPOLYGONALFACE((359,308,358,409));
#2153=IFCINDEXEDPOLYGONALFACE((360,309,359,410));
#2154=IFCINDEXEDPOLYGONALFACE((361,310,360,411));
#2155=IFCINDEXEDPOLYGONALFACE((362,311,361,412));
#2156=IFCINDEXEDPOLYGONALFACE((363,314,312,362,364));
#2157=IFCINDEXEDPOLYGONALFACE((413,324,364));
#2158=IFCINDEXEDPOLYGONALFACE((413,414,324));
#2159=IFCINDEXEDPOLYGONALFACE((374,324,414));
#2160=IFCINDEXEDPOLYGONALFACE((365,386,336,315));
#2161=IFCINDEXEDPOLYGONALFACE((366,415,365,316));
#2162=IFCINDEXEDPOLYGONALFACE((367,416,366,317));
#2163=IFCINDEXEDPOLYGONALFACE((368,417,367,318));
#2164=IFCINDEXEDPOLYGONALFACE((369,418,368,319));
#2165=IFCINDEXEDPOLYGONALFACE((370,419,369,320));
#2166=IFCINDEXEDPOLYGONALFACE((371,420,370,321));
#2167=IFCINDEXEDPOLYGONALFACE((372,421,371,322));
#2168=IFCINDEXEDPOLYGONALFACE((373,422,372,323));
#2169=IFCINDEXEDPOLYGONALFACE((399,423,373,349));
#2170=IFCINDEXEDPOLYGONALFACE((325,374,424,375));
#2171=IFCINDEXEDPOLYGONALFACE((326,375,425,376));
#2172=IFCINDEXEDPOLYGONALFACE((327,376,426,377));
#2173=IFCINDEXEDPOLYGONALFACE((328,377,427,378));
#2174=IFCINDEXEDPOLYGONALFACE((329,378,428,379));
#2175=IFCINDEXEDPOLYGONALFACE((330,379,429,380));
#2176=IFCINDEXEDPOLYGONALFACE((331,380,430,381));
#2177=IFCINDEXEDPOLYGONALFACE((332,381,431,382));
#2178=IFCINDEXEDPOLYGONALFACE((333,382,432,383));
#2179=IFCINDEXEDPOLYGONALFACE((350,383,433,400));
#2180=IFCINDEXEDPOLYGONALFACE((384,385,387,337,335));
#2181=IFCINDEXEDPOLYGONALFACE((434,386,435));
#2182=IFCINDEXEDPOLYGONALFACE((434,385,386));
#2183=IFCINDEXEDPOLYGONALFACE((386,436,435));
#2184=IFCINDEXEDPOLYGONALFACE((437,388,338,387));
#2185=IFCINDEXEDPOLYGONALFACE((438,389,339,388));
#2186=IFCINDEXEDPOLYGONALFACE((439,390,340,389));
#2187=IFCINDEXEDPOLYGONALFACE((440,391,341,390));
#2188=IFCINDEXEDPOLYGONALFACE((441,392,342,391));
#2189=IFCINDEXEDPOLYGONALFACE((442,393,343,392));
#2190=IFCINDEXEDPOLYGONALFACE((443,394,344,393));
#2191=IFCINDEXEDPOLYGONALFACE((444,346,296,394));
#2192=IFCINDEXEDPOLYGONALFACE((346,396,347));
#2193=IFCINDEXEDPOLYGONALFACE((395,396,397));
#2194=IFCINDEXEDPOLYGONALFACE((445,397,446));
#2195=IFCINDEXEDPOLYGONALFACE((445,447,398,348,397));
#2196=IFCINDEXEDPOLYGONALFACE((448,449,399,398));
#2197=IFCINDEXEDPOLYGONALFACE((401,400,450,451));
#2198=IFCINDEXEDPOLYGONALFACE((452,403,351,401,453));
#2199=IFCINDEXEDPOLYGONALFACE((402,403,404));
#2200=IFCINDEXEDPOLYGONALFACE((452,454,403));
#2201=IFCINDEXEDPOLYGONALFACE((404,354,353));
#2202=IFCINDEXEDPOLYGONALFACE((405,304,354,455));
#2203=IFCINDEXEDPOLYGONALFACE((406,355,405,456));
#2204=IFCINDEXEDPOLYGONALFACE((407,356,406,457));
#2205=IFCINDEXEDPOLYGONALFACE((408,357,407,458));
#2206=IFCINDEXEDPOLYGONALFACE((409,358,408,459));
#2207=IFCINDEXEDPOLYGONALFACE((410,359,409,460));
#2208=IFCINDEXEDPOLYGONALFACE((411,360,410,461));
#2209=IFCINDEXEDPOLYGONALFACE((412,361,411,462));
#2210=IFCINDEXEDPOLYGONALFACE((413,364,362,412,414));
#2211=IFCINDEXEDPOLYGONALFACE((463,374,414));
#2212=IFCINDEXEDPOLYGONALFACE((463,464,374));
#2213=IFCINDEXEDPOLYGONALFACE((424,374,464));
#2214=IFCINDEXEDPOLYGONALFACE((415,436,386,365));
#2215=IFCINDEXEDPOLYGONALFACE((416,465,415,366));
#2216=IFCINDEXEDPOLYGONALFACE((417,466,416,367));
#2217=IFCINDEXEDPOLYGONALFACE((418,467,417,368));
#2218=IFCINDEXEDPOLYGONALFACE((419,468,418,369));
#2219=IFCINDEXEDPOLYGONALFACE((420,469,419,370));
#2220=IFCINDEXEDPOLYGONALFACE((421,470,420,371));
#2221=IFCINDEXEDPOLYGONALFACE((422,471,421,372));
#2222=IFCINDEXEDPOLYGONALFACE((423,472,422,373));
#2223=IFCINDEXEDPOLYGONALFACE((449,473,423,399));
#2224=IFCINDEXEDPOLYGONALFACE((375,424,474,425));
#2225=IFCINDEXEDPOLYGONALFACE((376,425,475,426));
#2226=IFCINDEXEDPOLYGONALFACE((377,426,476,427));
#2227=IFCINDEXEDPOLYGONALFACE((378,427,477,428));
#2228=IFCINDEXEDPOLYGONALFACE((379,428,478,429));
#2229=IFCINDEXEDPOLYGONALFACE((380,429,479,430));
#2230=IFCINDEXEDPOLYGONALFACE((381,430,480,431));
#2231=IFCINDEXEDPOLYGONALFACE((382,431,481,432));
#2232=IFCINDEXEDPOLYGONALFACE((383,432,482,433));
#2233=IFCINDEXEDPOLYGONALFACE((400,433,483,450));
#2234=IFCINDEXEDPOLYGONALFACE((434,435,437,387,385));
#2235=IFCINDEXEDPOLYGONALFACE((484,436,485));
#2236=IFCINDEXEDPOLYGONALFACE((484,435,436));
#2237=IFCINDEXEDPOLYGONALFACE((436,486,485));
#2238=IFCINDEXEDPOLYGONALFACE((487,438,388,437));
#2239=IFCINDEXEDPOLYGONALFACE((488,439,389,438));
#2240=IFCINDEXEDPOLYGONALFACE((489,440,390,439));
#2241=IFCINDEXEDPOLYGONALFACE((490,441,391,440));
#2242=IFCINDEXEDPOLYGONALFACE((491,442,392,441));
#2243=IFCINDEXEDPOLYGONALFACE((492,443,393,442));
#2244=IFCINDEXEDPOLYGONALFACE((493,444,394,443));
#2245=IFCINDEXEDPOLYGONALFACE((494,396,346,444));
#2246=IFCINDEXEDPOLYGONALFACE((446,397,396));
#2247=IFCINDEXEDPOLYGONALFACE((445,446,447));
#2248=IFCINDEXEDPOLYGONALFACE((495,447,496));
#2249=IFCINDEXEDPOLYGONALFACE((495,497,448,398,447));
#2250=IFCINDEXEDPOLYGONALFACE((498,499,449,448));
#2251=IFCINDEXEDPOLYGONALFACE((451,450,500,501));
#2252=IFCINDEXEDPOLYGONALFACE((502,453,401,451,503));
#2253=IFCINDEXEDPOLYGONALFACE((452,453,454));
#2254=IFCINDEXEDPOLYGONALFACE((502,504,453));
#2255=IFCINDEXEDPOLYGONALFACE((454,404,403));
#2256=IFCINDEXEDPOLYGONALFACE((455,354,404,505));
#2257=IFCINDEXEDPOLYGONALFACE((456,405,455,506));
#2258=IFCINDEXEDPOLYGONALFACE((457,406,456,507));
#2259=IFCINDEXEDPOLYGONALFACE((458,407,457,508));
#2260=IFCINDEXEDPOLYGONALFACE((459,408,458,509));
#2261=IFCINDEXEDPOLYGONALFACE((460,409,459,510));
#2262=IFCINDEXEDPOLYGONALFACE((461,410,460,511));
#2263=IFCINDEXEDPOLYGONALFACE((462,411,461,512));
#2264=IFCINDEXEDPOLYGONALFACE((463,414,412,462,464));
#2265=IFCINDEXEDPOLYGONALFACE((513,424,464));
#2266=IFCINDEXEDPOLYGONALFACE((513,514,424));
#2267=IFCINDEXEDPOLYGONALFACE((474,424,514));
#2268=IFCINDEXEDPOLYGONALFACE((465,486,436,415));
#2269=IFCINDEXEDPOLYGONALFACE((466,515,465,416));
#2270=IFCINDEXEDPOLYGONALFACE((467,516,466,417));
#2271=IFCINDEXEDPOLYGONALFACE((468,517,467,418));
#2272=IFCINDEXEDPOLYGONALFACE((469,518,468,419));
#2273=IFCINDEXEDPOLYGONALFACE((470,519,469,420));
#2274=IFCINDEXEDPOLYGONALFACE((471,520,470,421));
#2275=IFCINDEXEDPOLYGONALFACE((472,521,471,422));
#2276=IFCINDEXEDPOLYGONALFACE((473,522,472,423));
#2277=IFCINDEXEDPOLYGONALFACE((499,523,473,449));
#2278=IFCINDEXEDPOLYGONALFACE((425,474,524,475));
#2279=IFCINDEXEDPOLYGONALFACE((426,475,525,476));
#2280=IFCINDEXEDPOLYGONALFACE((427,476,526,477));
#2281=IFCINDEXEDPOLYGONALFACE((428,477,527,478));
#2282=IFCINDEXEDPOLYGONALFACE((429,478,528,479));
#2283=IFCINDEXEDPOLYGONALFACE((430,479,529,480));
#2284=IFCINDEXEDPOLYGONALFACE((431,480,530,481));
#2285=IFCINDEXEDPOLYGONALFACE((432,481,531,482));
#2286=IFCINDEXEDPOLYGONALFACE((433,482,532,483));
#2287=IFCINDEXEDPOLYGONALFACE((450,483,533,500));
#2288=IFCINDEXEDPOLYGONALFACE((484,485,487,437,435));
#2289=IFCINDEXEDPOLYGONALFACE((534,485,486));
#2290=IFCINDEXEDPOLYGONALFACE((535,488,438,487));
#2291=IFCINDEXEDPOLYGONALFACE((536,489,439,488));
#2292=IFCINDEXEDPOLYGONALFACE((537,490,440,489));
#2293=IFCINDEXEDPOLYGONALFACE((538,491,441,490));
#2294=IFCINDEXEDPOLYGONALFACE((539,492,442,491));
#2295=IFCINDEXEDPOLYGONALFACE((540,493,443,492));
#2296=IFCINDEXEDPOLYGONALFACE((541,494,444,493));
#2297=IFCINDEXEDPOLYGONALFACE((542,446,396,494));
#2298=IFCINDEXEDPOLYGONALFACE((496,447,446));
#2299=IFCINDEXEDPOLYGONALFACE((495,496,497));
#2300=IFCINDEXEDPOLYGONALFACE((543,497,544));
#2301=IFCINDEXEDPOLYGONALFACE((543,545,498,448,497));
#2302=IFCINDEXEDPOLYGONALFACE((546,547,499,498));
#2303=IFCINDEXEDPOLYGONALFACE((501,500,548,549));
#2304=IFCINDEXEDPOLYGONALFACE((550,503,451,501,551));
#2305=IFCINDEXEDPOLYGONALFACE((502,503,504));
#2306=IFCINDEXEDPOLYGONALFACE((550,552,503));
#2307=IFCINDEXEDPOLYGONALFACE((504,454,453));
#2308=IFCINDEXEDPOLYGONALFACE((505,404,454,553));
#2309=IFCINDEXEDPOLYGONALFACE((506,455,505,554));
#2310=IFCINDEXEDPOLYGONALFACE((507,456,506,555));
#2311=IFCINDEXEDPOLYGONALFACE((508,457,507,556));
#2312=IFCINDEXEDPOLYGONALFACE((509,458,508,557));
#2313=IFCINDEXEDPOLYGONALFACE((510,459,509,558));
#2314=IFCINDEXEDPOLYGONALFACE((511,460,510,559));
#2315=IFCINDEXEDPOLYGONALFACE((512,461,511,560));
#2316=IFCINDEXEDPOLYGONALFACE((513,464,462,512,514));
#2317=IFCINDEXEDPOLYGONALFACE((561,474,514));
#2318=IFCINDEXEDPOLYGONALFACE((561,562,474));
#2319=IFCINDEXEDPOLYGONALFACE((474,562,524));
#2320=IFCINDEXEDPOLYGONALFACE((515,563,486,465));
#2321=IFCINDEXEDPOLYGONALFACE((516,564,515,466));
#2322=IFCINDEXEDPOLYGONALFACE((517,565,516,467));
#2323=IFCINDEXEDPOLYGONALFACE((518,566,517,468));
#2324=IFCINDEXEDPOLYGONALFACE((519,567,518,469));
#2325=IFCINDEXEDPOLYGONALFACE((520,568,519,470));
#2326=IFCINDEXEDPOLYGONALFACE((521,569,520,471));
#2327=IFCINDEXEDPOLYGONALFACE((522,570,521,472));
#2328=IFCINDEXEDPOLYGONALFACE((523,571,522,473));
#2329=IFCINDEXEDPOLYGONALFACE((547,572,523,499));
#2330=IFCINDEXEDPOLYGONALFACE((475,524,573,525));
#2331=IFCINDEXEDPOLYGONALFACE((476,525,574,526));
#2332=IFCINDEXEDPOLYGONALFACE((477,526,575,527));
#2333=IFCINDEXEDPOLYGONALFACE((478,527,576,528));
#2334=IFCINDEXEDPOLYGONALFACE((479,528,577,529));
#2335=IFCINDEXEDPOLYGONALFACE((480,529,578,530));
#2336=IFCINDEXEDPOLYGONALFACE((481,530,579,531));
#2337=IFCINDEXEDPOLYGONALFACE((482,531,580,532));
#2338=IFCINDEXEDPOLYGONALFACE((483,532,581,533));
#2339=IFCINDEXEDPOLYGONALFACE((500,533,582,548));
#2340=IFCINDEXEDPOLYGONALFACE((534,535,487,485));
#2341=IFCINDEXEDPOLYGONALFACE((534,486,583));
#2342=IFCINDEXEDPOLYGONALFACE((563,583,486));
#2343=IFCINDEXEDPOLYGONALFACE((584,536,488,535));
#2344=IFCINDEXEDPOLYGONALFACE((585,537,489,536));
#2345=IFCINDEXEDPOLYGONALFACE((586,538,490,537));
#2346=IFCINDEXEDPOLYGONALFACE((587,539,491,538));
#2347=IFCINDEXEDPOLYGONALFACE((588,540,492,539));
#2348=IFCINDEXEDPOLYGONALFACE((589,541,493,540));
#2349=IFCINDEXEDPOLYGONALFACE((590,542,494,541));
#2350=IFCINDEXEDPOLYGONALFACE((591,496,446,542));
#2351=IFCINDEXEDPOLYGONALFACE((544,497,496));
#2352=IFCINDEXEDPOLYGONALFACE((543,544,545));
#2353=IFCINDEXEDPOLYGONALFACE((592,545,593));
#2354=IFCINDEXEDPOLYGONALFACE((592,594,546,498,545));
#2355=IFCINDEXEDPOLYGONALFACE((549,548,547,546));
#2356=IFCINDEXEDPOLYGONALFACE((595,551,501,549,596));
#2357=IFCINDEXEDPOLYGONALFACE((550,551,552));
#2358=IFCINDEXEDPOLYGONALFACE((595,597,551));
#2359=IFCINDEXEDPOLYGONALFACE((552,504,503));
#2360=IFCINDEXEDPOLYGONALFACE((553,454,504,598));
#2361=IFCINDEXEDPOLYGONALFACE((554,505,553,599));
#2362=IFCINDEXEDPOLYGONALFACE((555,506,554,600));
#2363=IFCINDEXEDPOLYGONALFACE((556,507,555,601));
#2364=IFCINDEXEDPOLYGONALFACE((557,508,556,602));
#2365=IFCINDEXEDPOLYGONALFACE((558,509,557,603));
#2366=IFCINDEXEDPOLYGONALFACE((559,510,558,604));
#2367=IFCINDEXEDPOLYGONALFACE((560,511,559,605));
#2368=IFCINDEXEDPOLYGONALFACE((561,514,512,560,562));
#2369=IFCINDEXEDPOLYGONALFACE((606,524,562));
#2370=IFCINDEXEDPOLYGONALFACE((606,607,524));
#2371=IFCINDEXEDPOLYGONALFACE((524,607,573));
#2372=IFCINDEXEDPOLYGONALFACE((564,608,563,515));
#2373=IFCINDEXEDPOLYGONALFACE((565,609,564,516));
#2374=IFCINDEXEDPOLYGONALFACE((566,610,565,517));
#2375=IFCINDEXEDPOLYGONALFACE((567,611,566,518));
#2376=IFCINDEXEDPOLYGONALFACE((568,612,567,519));
#2377=IFCINDEXEDPOLYGONALFACE((569,613,568,520));
#2378=IFCINDEXEDPOLYGONALFACE((570,614,569,521));
#2379=IFCINDEXEDPOLYGONALFACE((571,615,570,522));
#2380=IFCINDEXEDPOLYGONALFACE((572,616,571,523));
#2381=IFCINDEXEDPOLYGONALFACE((548,582,572,547));
#2382=IFCINDEXEDPOLYGONALFACE((525,573,617,574));
#2383=IFCINDEXEDPOLYGONALFACE((526,574,618,575));
#2384=IFCINDEXEDPOLYGONALFACE((527,575,619,576));
#2385=IFCINDEXEDPOLYGONALFACE((528,576,620,577));
#2386=IFCINDEXEDPOLYGONALFACE((529,577,621,578));
#2387=IFCINDEXEDPOLYGONALFACE((530,578,622,579));
#2388=IFCINDEXEDPOLYGONALFACE((531,579,623,580));
#2389=IFCINDEXEDPOLYGONALFACE((532,580,624,581));
#2390=IFCINDEXEDPOLYGONALFACE((533,581,625,582));
#2391=IFCINDEXEDPOLYGONALFACE((583,584,535,534));
#2392=IFCINDEXEDPOLYGONALFACE((626,563,627));
#2393=IFCINDEXEDPOLYGONALFACE((626,583,563));
#2394=IFCINDEXEDPOLYGONALFACE((608,627,563));
#2395=IFCINDEXEDPOLYGONALFACE((628,585,536,584));
#2396=IFCINDEXEDPOLYGONALFACE((629,586,537,585));
#2397=IFCINDEXEDPOLYGONALFACE((630,587,538,586));
#2398=IFCINDEXEDPOLYGONALFACE((631,588,539,587));
#2399=IFCINDEXEDPOLYGONALFACE((632,589,540,588));
#2400=IFCINDEXEDPOLYGONALFACE((633,590,541,589));
#2401=IFCINDEXEDPOLYGONALFACE((634,591,542,590));
#2402=IFCINDEXEDPOLYGONALFACE((635,544,496,591));
#2403=IFCINDEXEDPOLYGONALFACE((593,545,544));
#2404=IFCINDEXEDPOLYGONALFACE((592,593,594));
#2405=IFCINDEXEDPOLYGONALFACE((636,594,637));
#2406=IFCINDEXEDPOLYGONALFACE((636,596,549,546,594));
#2407=IFCINDEXEDPOLYGONALFACE((595,596,597));
#2408=IFCINDEXEDPOLYGONALFACE((597,552,551));
#2409=IFCINDEXEDPOLYGONALFACE((598,504,552,638));
#2410=IFCINDEXEDPOLYGONALFACE((599,553,598,639));
#2411=IFCINDEXEDPOLYGONALFACE((600,554,599,640));
#2412=IFCINDEXEDPOLYGONALFACE((601,555,600,641));
#2413=IFCINDEXEDPOLYGONALFACE((602,556,601,642));
#2414=IFCINDEXEDPOLYGONALFACE((603,557,602,643));
#2415=IFCINDEXEDPOLYGONALFACE((604,558,603,644));
#2416=IFCINDEXEDPOLYGONALFACE((605,559,604,645));
#2417=IFCINDEXEDPOLYGONALFACE((606,562,560,605,607));
#2418=IFCINDEXEDPOLYGONALFACE((646,573,607));
#2419=IFCINDEXEDPOLYGONALFACE((646,647,573));
#2420=IFCINDEXEDPOLYGONALFACE((573,647,617));
#2421=IFCINDEXEDPOLYGONALFACE((609,648,608,564));
#2422=IFCINDEXEDPOLYGONALFACE((610,649,609,565));
#2423=IFCINDEXEDPOLYGONALFACE((611,650,610,566));
#2424=IFCINDEXEDPOLYGONALFACE((612,651,611,567));
#2425=IFCINDEXEDPOLYGONALFACE((613,652,612,568));
#2426=IFCINDEXEDPOLYGONALFACE((614,653,613,569));
#2427=IFCINDEXEDPOLYGONALFACE((615,654,614,570));
#2428=IFCINDEXEDPOLYGONALFACE((616,655,615,571));
#2429=IFCINDEXEDPOLYGONALFACE((582,625,616,572));
#2430=IFCINDEXEDPOLYGONALFACE((574,617,656,618));
#2431=IFCINDEXEDPOLYGONALFACE((575,618,657,619));
#2432=IFCINDEXEDPOLYGONALFACE((576,619,658,620));
#2433=IFCINDEXEDPOLYGONALFACE((577,620,659,621));
#2434=IFCINDEXEDPOLYGONALFACE((578,621,660,622));
#2435=IFCINDEXEDPOLYGONALFACE((579,622,661,623));
#2436=IFCINDEXEDPOLYGONALFACE((580,623,662,624));
#2437=IFCINDEXEDPOLYGONALFACE((581,624,663,625));
#2438=IFCINDEXEDPOLYGONALFACE((626,627,628,584,583));
#2439=IFCINDEXEDPOLYGONALFACE((664,608,665));
#2440=IFCINDEXEDPOLYGONALFACE((664,627,608));
#2441=IFCINDEXEDPOLYGONALFACE((648,665,608));
#2442=IFCINDEXEDPOLYGONALFACE((666,629,585,628));
#2443=IFCINDEXEDPOLYGONALFACE((667,630,586,629));
#2444=IFCINDEXEDPOLYGONALFACE((668,631,587,630));
#2445=IFCINDEXEDPOLYGONALFACE((669,632,588,631));
#2446=IFCINDEXEDPOLYGONALFACE((670,633,589,632));
#2447=IFCINDEXEDPOLYGONALFACE((671,634,590,633));
#2448=IFCINDEXEDPOLYGONALFACE((672,635,591,634));
#2449=IFCINDEXEDPOLYGONALFACE((673,593,544,635));
#2450=IFCINDEXEDPOLYGONALFACE((637,594,593));
#2451=IFCINDEXEDPOLYGONALFACE((636,637,596));
#2452=IFCINDEXEDPOLYGONALFACE((674,597,596));
#2453=IFCINDEXEDPOLYGONALFACE((638,552,597,675));
#2454=IFCINDEXEDPOLYGONALFACE((639,598,638,676));
#2455=IFCINDEXEDPOLYGONALFACE((640,599,639,677));
#2456=IFCINDEXEDPOLYGONALFACE((641,600,640,678));
#2457=IFCINDEXEDPOLYGONALFACE((642,601,641,679));
#2458=IFCINDEXEDPOLYGONALFACE((643,602,642,680));
#2459=IFCINDEXEDPOLYGONALFACE((644,603,643,681));
#2460=IFCINDEXEDPOLYGONALFACE((645,604,644,682));
#2461=IFCINDEXEDPOLYGONALFACE((646,607,605,645,647));
#2462=IFCINDEXEDPOLYGONALFACE((683,617,647));
#2463=IFCINDEXEDPOLYGONALFACE((683,684,617));
#2464=IFCINDEXEDPOLYGONALFACE((617,684,656));
#2465=IFCINDEXEDPOLYGONALFACE((649,685,648,609));
#2466=IFCINDEXEDPOLYGONALFACE((650,686,649,610));
#2467=IFCINDEXEDPOLYGONALFACE((651,687,650,611));
#2468=IFCINDEXEDPOLYGONALFACE((652,688,651,612));
#2469=IFCINDEXEDPOLYGONALFACE((653,689,652,613));
#2470=IFCINDEXEDPOLYGONALFACE((654,690,653,614));
#2471=IFCINDEXEDPOLYGONALFACE((655,691,654,615));
#2472=IFCINDEXEDPOLYGONALFACE((625,663,655,616));
#2473=IFCINDEXEDPOLYGONALFACE((618,656,692,657));
#2474=IFCINDEXEDPOLYGONALFACE((619,657,693,658));
#2475=IFCINDEXEDPOLYGONALFACE((620,658,694,659));
#2476=IFCINDEXEDPOLYGONALFACE((621,659,695,660));
#2477=IFCINDEXEDPOLYGONALFACE((622,660,696,661));
#2478=IFCINDEXEDPOLYGONALFACE((623,661,697,662));
#2479=IFCINDEXEDPOLYGONALFACE((624,662,698,663));
#2480=IFCINDEXEDPOLYGONALFACE((664,665,666,628,627));
#2481=IFCINDEXEDPOLYGONALFACE((699,648,700));
#2482=IFCINDEXEDPOLYGONALFACE((699,665,648));
#2483=IFCINDEXEDPOLYGONALFACE((685,700,648));
#2484=IFCINDEXEDPOLYGONALFACE((701,667,629,666));
#2485=IFCINDEXEDPOLYGONALFACE((702,668,630,667));
#2486=IFCINDEXEDPOLYGONALFACE((703,669,631,668));
#2487=IFCINDEXEDPOLYGONALFACE((704,670,632,669));
#2488=IFCINDEXEDPOLYGONALFACE((705,671,633,670));
#2489=IFCINDEXEDPOLYGONALFACE((706,672,634,671));
#2490=IFCINDEXEDPOLYGONALFACE((707,673,635,672));
#2491=IFCINDEXEDPOLYGONALFACE((708,637,593,673));
#2492=IFCINDEXEDPOLYGONALFACE((674,596,637));
#2493=IFCINDEXEDPOLYGONALFACE((675,597,674,709));
#2494=IFCINDEXEDPOLYGONALFACE((676,638,675,710));
#2495=IFCINDEXEDPOLYGONALFACE((677,639,676,711));
#2496=IFCINDEXEDPOLYGONALFACE((678,640,677,712));
#2497=IFCINDEXEDPOLYGONALFACE((679,641,678,713));
#2498=IFCINDEXEDPOLYGONALFACE((680,642,679,714));
#2499=IFCINDEXEDPOLYGONALFACE((681,643,680,715));
#2500=IFCINDEXEDPOLYGONALFACE((682,644,681,716));
#2501=IFCINDEXEDPOLYGONALFACE((683,647,645,682,684));
#2502=IFCINDEXEDPOLYGONALFACE((717,656,684));
#2503=IFCINDEXEDPOLYGONALFACE((717,718,656));
#2504=IFCINDEXEDPOLYGONALFACE((656,718,692));
#2505=IFCINDEXEDPOLYGONALFACE((686,719,685,649));
#2506=IFCINDEXEDPOLYGONALFACE((687,720,686,650));
#2507=IFCINDEXEDPOLYGONALFACE((688,721,687,651));
#2508=IFCINDEXEDPOLYGONALFACE((689,722,688,652));
#2509=IFCINDEXEDPOLYGONALFACE((690,723,689,653));
#2510=IFCINDEXEDPOLYGONALFACE((691,724,690,654));
#2511=IFCINDEXEDPOLYGONALFACE((663,698,691,655));
#2512=IFCINDEXEDPOLYGONALFACE((657,692,725,693));
#2513=IFCINDEXEDPOLYGONALFACE((658,693,726,694));
#2514=IFCINDEXEDPOLYGONALFACE((659,694,727,695));
#2515=IFCINDEXEDPOLYGONALFACE((660,695,728,696));
#2516=IFCINDEXEDPOLYGONALFACE((661,696,729,697));
#2517=IFCINDEXEDPOLYGONALFACE((662,697,730,698));
#2518=IFCINDEXEDPOLYGONALFACE((699,700,701,666,665));
#2519=IFCINDEXEDPOLYGONALFACE((731,685,732));
#2520=IFCINDEXEDPOLYGONALFACE((731,700,685));
#2521=IFCINDEXEDPOLYGONALFACE((719,732,685));
#2522=IFCINDEXEDPOLYGONALFACE((733,702,667,701));
#2523=IFCINDEXEDPOLYGONALFACE((734,703,668,702));
#2524=IFCINDEXEDPOLYGONALFACE((735,704,669,703));
#2525=IFCINDEXEDPOLYGONALFACE((736,705,670,704));
#2526=IFCINDEXEDPOLYGONALFACE((737,706,671,705));
#2527=IFCINDEXEDPOLYGONALFACE((738,707,672,706));
#2528=IFCINDEXEDPOLYGONALFACE((739,708,673,707));
#2529=IFCINDEXEDPOLYGONALFACE((709,674,637,708));
#2530=IFCINDEXEDPOLYGONALFACE((710,675,709,740));
#2531=IFCINDEXEDPOLYGONALFACE((711,676,710,741));
#2532=IFCINDEXEDPOLYGONALFACE((712,677,711,742));
#2533=IFCINDEXEDPOLYGONALFACE((713,678,712,743));
#2534=IFCINDEXEDPOLYGONALFACE((714,679,713,744));
#2535=IFCINDEXEDPOLYGONALFACE((715,680,714,745));
#2536=IFCINDEXEDPOLYGONALFACE((716,681,715,746));
#2537=IFCINDEXEDPOLYGONALFACE((717,684,682,716,718));
#2538=IFCINDEXEDPOLYGONALFACE((747,692,718));
#2539=IFCINDEXEDPOLYGONALFACE((747,748,692));
#2540=IFCINDEXEDPOLYGONALFACE((692,748,725));
#2541=IFCINDEXEDPOLYGONALFACE((720,749,719,686));
#2542=IFCINDEXEDPOLYGONALFACE((721,750,720,687));
#2543=IFCINDEXEDPOLYGONALFACE((722,751,721,688));
#2544=IFCINDEXEDPOLYGONALFACE((723,752,722,689));
#2545=IFCINDEXEDPOLYGONALFACE((724,753,723,690));
#2546=IFCINDEXEDPOLYGONALFACE((698,730,724,691));
#2547=IFCINDEXEDPOLYGONALFACE((693,725,754,726));
#2548=IFCINDEXEDPOLYGONALFACE((694,726,755,727));
#2549=IFCINDEXEDPOLYGONALFACE((695,727,756,728));
#2550=IFCINDEXEDPOLYGONALFACE((696,728,757,729));
#2551=IFCINDEXEDPOLYGONALFACE((697,729,758,730));
#2552=IFCINDEXEDPOLYGONALFACE((731,732,733,701,700));
#2553=IFCINDEXEDPOLYGONALFACE((759,719,760));
#2554=IFCINDEXEDPOLYGONALFACE((759,732,719));
#2555=IFCINDEXEDPOLYGONALFACE((719,749,760));
#2556=IFCINDEXEDPOLYGONALFACE((761,734,702,733));
#2557=IFCINDEXEDPOLYGONALFACE((762,735,703,734));
#2558=IFCINDEXEDPOLYGONALFACE((763,736,704,735));
#2559=IFCINDEXEDPOLYGONALFACE((764,737,705,736));
#2560=IFCINDEXEDPOLYGONALFACE((765,738,706,737));
#2561=IFCINDEXEDPOLYGONALFACE((766,739,707,738));
#2562=IFCINDEXEDPOLYGONALFACE((740,709,708,739));
#2563=IFCINDEXEDPOLYGONALFACE((741,710,740,767));
#2564=IFCINDEXEDPOLYGONALFACE((742,711,741,768));
#2565=IFCINDEXEDPOLYGONALFACE((743,712,742,769));
#2566=IFCINDEXEDPOLYGONALFACE((744,713,743,770));
#2567=IFCINDEXEDPOLYGONALFACE((745,714,744,771));
#2568=IFCINDEXEDPOLYGONALFACE((746,715,745,772));
#2569=IFCINDEXEDPOLYGONALFACE((747,718,716,746,748));
#2570=IFCINDEXEDPOLYGONALFACE((773,725,748));
#2571=IFCINDEXEDPOLYGONALFACE((773,774,725));
#2572=IFCINDEXEDPOLYGONALFACE((725,774,754));
#2573=IFCINDEXEDPOLYGONALFACE((750,775,749,720));
#2574=IFCINDEXEDPOLYGONALFACE((751,776,750,721));
#2575=IFCINDEXEDPOLYGONALFACE((752,777,751,722));
#2576=IFCINDEXEDPOLYGONALFACE((753,778,752,723));
#2577=IFCINDEXEDPOLYGONALFACE((730,758,753,724));
#2578=IFCINDEXEDPOLYGONALFACE((726,754,779,755));
#2579=IFCINDEXEDPOLYGONALFACE((727,755,780,756));
#2580=IFCINDEXEDPOLYGONALFACE((728,756,781,757));
#2581=IFCINDEXEDPOLYGONALFACE((729,757,782,758));
#2582=IFCINDEXEDPOLYGONALFACE((759,760,761,733,732));
#2583=IFCINDEXEDPOLYGONALFACE((783,749,784));
#2584=IFCINDEXEDPOLYGONALFACE((783,760,749));
#2585=IFCINDEXEDPOLYGONALFACE((749,775,784));
#2586=IFCINDEXEDPOLYGONALFACE((785,762,734,761));
#2587=IFCINDEXEDPOLYGONALFACE((786,763,735,762));
#2588=IFCINDEXEDPOLYGONALFACE((787,764,736,763));
#2589=IFCINDEXEDPOLYGONALFACE((788,765,737,764));
#2590=IFCINDEXEDPOLYGONALFACE((789,766,738,765));
#2591=IFCINDEXEDPOLYGONALFACE((767,740,739,766));
#2592=IFCINDEXEDPOLYGONALFACE((768,741,767,790));
#2593=IFCINDEXEDPOLYGONALFACE((769,742,768,791));
#2594=IFCINDEXEDPOLYGONALFACE((770,743,769,792));
#2595=IFCINDEXEDPOLYGONALFACE((771,744,770,793));
#2596=IFCINDEXEDPOLYGONALFACE((772,745,771,794));
#2597=IFCINDEXEDPOLYGONALFACE((773,748,746,772,774));
#2598=IFCINDEXEDPOLYGONALFACE((795,754,774));
#2599=IFCINDEXEDPOLYGONALFACE((795,796,754));
#2600=IFCINDEXEDPOLYGONALFACE((754,796,779));
#2601=IFCINDEXEDPOLYGONALFACE((776,797,775,750));
#2602=IFCINDEXEDPOLYGONALFACE((777,798,776,751));
#2603=IFCINDEXEDPOLYGONALFACE((778,799,777,752));
#2604=IFCINDEXEDPOLYGONALFACE((758,782,778,753));
#2605=IFCINDEXEDPOLYGONALFACE((755,779,800,780));
#2606=IFCINDEXEDPOLYGONALFACE((756,780,801,781));
#2607=IFCINDEXEDPOLYGONALFACE((757,781,802,782));
#2608=IFCINDEXEDPOLYGONALFACE((783,784,785,761,760));
#2609=IFCINDEXEDPOLYGONALFACE((803,775,804));
#2610=IFCINDEXEDPOLYGONALFACE((803,784,775));
#2611=IFCINDEXEDPOLYGONALFACE((775,797,804));
#2612=IFCINDEXEDPOLYGONALFACE((805,786,762,785));
#2613=IFCINDEXEDPOLYGONALFACE((806,787,763,786));
#2614=IFCINDEXEDPOLYGONALFACE((807,788,764,787));
#2615=IFCINDEXEDPOLYGONALFACE((808,789,765,788));
#2616=IFCINDEXEDPOLYGONALFACE((790,767,766,789));
#2617=IFCINDEXEDPOLYGONALFACE((791,768,790,809));
#2618=IFCINDEXEDPOLYGONALFACE((792,769,791,810));
#2619=IFCINDEXEDPOLYGONALFACE((793,770,792,811));
#2620=IFCINDEXEDPOLYGONALFACE((794,771,793,812));
#2621=IFCINDEXEDPOLYGONALFACE((795,774,772,794,796));
#2622=IFCINDEXEDPOLYGONALFACE((813,779,796));
#2623=IFCINDEXEDPOLYGONALFACE((813,814,779));
#2624=IFCINDEXEDPOLYGONALFACE((779,814,800));
#2625=IFCINDEXEDPOLYGONALFACE((798,815,797,776));
#2626=IFCINDEXEDPOLYGONALFACE((799,816,798,777));
#2627=IFCINDEXEDPOLYGONALFACE((782,802,799,778));
#2628=IFCINDEXEDPOLYGONALFACE((780,800,817,801));
#2629=IFCINDEXEDPOLYGONALFACE((781,801,818,802));
#2630=IFCINDEXEDPOLYGONALFACE((803,804,805,785,784));
#2631=IFCINDEXEDPOLYGONALFACE((819,797,820));
#2632=IFCINDEXEDPOLYGONALFACE((819,804,797));
#2633=IFCINDEXEDPOLYGONALFACE((797,815,820));
#2634=IFCINDEXEDPOLYGONALFACE((821,806,786,805));
#2635=IFCINDEXEDPOLYGONALFACE((822,807,787,806));
#2636=IFCINDEXEDPOLYGONALFACE((823,808,788,807));
#2637=IFCINDEXEDPOLYGONALFACE((809,790,789,808));
#2638=IFCINDEXEDPOLYGONALFACE((810,791,809,824));
#2639=IFCINDEXEDPOLYGONALFACE((811,792,810,825));
#2640=IFCINDEXEDPOLYGONALFACE((812,793,811,826));
#2641=IFCINDEXEDPOLYGONALFACE((813,796,794,812,814));
#2642=IFCINDEXEDPOLYGONALFACE((827,800,814));
#2643=IFCINDEXEDPOLYGONALFACE((827,828,800));
#2644=IFCINDEXEDPOLYGONALFACE((800,828,817));
#2645=IFCINDEXEDPOLYGONALFACE((816,829,815,798));
#2646=IFCINDEXEDPOLYGONALFACE((802,818,816,799));
#2647=IFCINDEXEDPOLYGONALFACE((801,817,830,818));
#2648=IFCINDEXEDPOLYGONALFACE((819,820,821,805,804));
#2649=IFCINDEXEDPOLYGONALFACE((831,815,832));
#2650=IFCINDEXEDPOLYGONALFACE((831,820,815));
#2651=IFCINDEXEDPOLYGONALFACE((815,829,832));
#2652=IFCINDEXEDPOLYGONALFACE((833,822,806,821));
#2653=IFCINDEXEDPOLYGONALFACE((834,823,807,822));
#2654=IFCINDEXEDPOLYGONALFACE((824,809,808,823));
#2655=IFCINDEXEDPOLYGONALFACE((825,810,824,835));
#2656=IFCINDEXEDPOLYGONALFACE((826,811,825,836));
#2657=IFCINDEXEDPOLYGONALFACE((827,814,812,826,828));
#2658=IFCINDEXEDPOLYGONALFACE((837,817,828));
#2659=IFCINDEXEDPOLYGONALFACE((837,838,817));
#2660=IFCINDEXEDPOLYGONALFACE((818,830,829,816));
#2661=IFCINDEXEDPOLYGONALFACE((817,838,830));
#2662=IFCINDEXEDPOLYGONALFACE((831,832,833,821,820));
#2663=IFCINDEXEDPOLYGONALFACE((839,829,840));
#2664=IFCINDEXEDPOLYGONALFACE((839,832,829));
#2665=IFCINDEXEDPOLYGONALFACE((841,834,822,833));
#2666=IFCINDEXEDPOLYGONALFACE((835,824,823,834));
#2667=IFCINDEXEDPOLYGONALFACE((836,825,835,842));
#2668=IFCINDEXEDPOLYGONALFACE((837,828,826,836,838));
#2669=IFCINDEXEDPOLYGONALFACE((829,830,840));
#2670=IFCINDEXEDPOLYGONALFACE((843,830,838));
#2671=IFCINDEXEDPOLYGONALFACE((839,840,841,833,832));
#2672=IFCINDEXEDPOLYGONALFACE((842,835,834,841));
#2673=IFCINDEXEDPOLYGONALFACE((838,836,842,843));
#2674=IFCINDEXEDPOLYGONALFACE((843,840,830));
#2675=IFCINDEXEDPOLYGONALFACE((843,842,841,840));
#2676=IFCCARTESIANPOINTLIST3D(((13.9271154498794,3.41655943782162,9.99999999944992E-5),
(17.9119797907806,10.318546937532,0.000100000000012049),(13.9271154498794,
17.2205344372423,0.000100000000020912),(5.95738676807697,17.2205344372423,
0.000100000000012224),(1.97252242717574,10.318546937532,9.99999999946738E-5),
(5.95738676807695,3.41655943782165,9.99999999858114E-5)));
#2677=IFCCARTESIANPOINTLIST3D(((15.,6.66666666138809E-5,0.000100000000016951),
(15.,15.0000666666667,9.9999999999489E-5),(0.,15.0000666666667,9.9999999999489E-5),
(0.,6.66666666831588E-5,9.9999999999489E-5)));
#2678=IFCCARTESIANPOINTLIST3D(((5.01343707144797,11.672203543884,5.86758330849528),
(6.42059999999996,11.8574600579402,5.86758330849528),(6.4206,12.0603812561753,
4.67431301902413),(4.96091720068971,11.8682103699608,4.67431301902429),
(3.70216997102988,11.1290589269972,5.86758330849528),(3.60070937191258,
11.3047938396352,4.67431301902429),(2.57615938466843,10.2650406153321,5.8675833084953),
(2.43267242935,10.4085275706506,4.67431301902429),(1.7121410730028,9.13903002897021,
5.86758330849535),(1.5364061603653,9.24049062808802,4.67431301902429),(1.16899645611619,
7.82776292855279,5.8675833084953),(0.972989630039741,7.88028279931091,4.67431301902429),
(0.983739942059717,6.42060000000005,5.86758330849528),(0.78081874382518,
6.42060000000068,4.67431301902429),(1.16899645611583,5.0134370714486,5.86758330849532),
(0.97298963003973,4.96091720069045,4.67431301902429),(1.7121410730028,3.70216997102997,
5.86758330849528),(1.53640616036528,3.60070937191333,4.67431301902429),
(2.57615938466777,2.5761593846686,5.8675833084953),(2.43267242934997,2.43267242935077,
4.67431301902429),(3.70216997102988,1.71214107300289,5.86758330849535),
(3.60070937191254,1.5364061603661,4.67431301902429),(5.01343707144762,1.16899645611616,
5.8675833084953),(4.96091720068967,0.972989630040574,4.67431301902429),
(6.42059999999987,0.98373994205981,5.86758330849528),(6.42059999999991,
0.780818743824796,4.67431301902429),(6.42059999999996,12.1981187761481,
3.27376315980206),(4.92526810729554,12.0012545977515,3.27376315980258),
(3.53184061192615,11.424078030986,3.27376315980258),(2.33527729495338,10.5059227050472,
3.27376315980258),(1.41712196901455,9.30935938807442,3.27376315980257),
(0.839945402248975,7.91593189270505,3.27376315980258),(0.643081223852352,
6.42060000000066,3.27376315980258),(0.839945402248974,4.92526810729627,
3.27376315980257),(1.41712196901455,3.5318406119269,3.27376315980258),(2.33527729495338,
2.33527729495416,3.27376315980258),(3.53184061192615,1.41712196901535,3.27376315980257),
(4.92526810729553,0.839945402249809,3.27376315980258),(6.42059999999992,
0.64308122385194,3.27376315980259),(6.42059999999995,12.1320028126942,2.32997234894577),
(4.94238017782268,11.9373914811215,2.32997234894611),(3.56489859365306,
11.3668199270393,2.32997234894611),(2.38202834105626,10.4591716589443,2.32997234894611),
(1.47438007296125,9.27630140634749,2.32997234894611),(0.903808518879015,
7.89881982217788,2.32997234894611),(0.709197187306185,6.42060000000065,
2.32997234894611),(0.903808518879019,4.94238017782342,2.32997234894611),
(1.47438007296126,3.56489859365381,2.32997234894611),(2.38202834105628,
2.38202834105704,2.32997234894611),(3.56489859365307,1.47438007296205,2.32997234894611),
(4.9423801778227,0.903808518879847,2.32997234894611),(6.42059999999992,
0.709197187305745,2.32997234894614),(6.42059999999995,11.898474038813,1.64640573287725),
(5.00282187208255,11.7118200072481,1.6464057328775),(3.68166298059361,11.1645780763436,
1.6464057328775),(2.54715812066977,10.2940418793308,1.6464057328775),(1.67662192365692,
9.15953701940694,1.6464057328775),(1.12937999275239,7.83837812791801,1.6464057328775),
(0.942725961187291,6.42060000000065,1.6464057328775),(1.1293799927524,5.00282187208329,
1.64640573287749),(1.67662192365693,3.68166298059436,1.6464057328775),(2.54715812066978,
2.54715812067054,1.6464057328775),(3.68166298059363,1.67662192365772,1.6464057328775),
(5.00282187208256,1.12937999275323,1.6464057328775),(6.42059999999991,0.94272596118683,
1.64640573287753),(6.42059999999995,11.509307099911,1.15257189825201),(5.10354568759467,
11.3359136102246,1.15257189825201),(3.87624645004469,10.8275496209413,1.15257189825201),
(2.8223407021811,10.0188592978194,1.15257189825201),(2.01365037905921,8.96495354995586,
1.152571898252),(1.50528638977585,7.73765431240589,1.152571898252),(1.33189290008945,
6.42060000000066,1.152571898252),(1.50528638977585,5.10354568759543,1.152571898252),
(2.01365037905921,3.87624645004545,1.152571898252),(2.8223407021811,2.82234070218188,
1.152571898252),(3.87624645004469,2.01365037906002,1.152571898252),(5.10354568759467,
1.50528638977669,1.152571898252),(6.42059999999991,1.33189290008897,1.15257189825204),
(6.42059999999996,10.8886585413617,0.739387833193804),(5.26418135486268,
10.7364131384728,0.739387833193801),(4.18657072931936,10.2900522024154,
0.739387833193801),(3.26120550666499,9.57999449333557,0.739387833193802),
(2.55114779758513,8.65462927068122,0.739387833193802),(2.10478686152772,
7.57701864513792,0.739387833193803),(1.95254145863876,6.42060000000068,
0.739387833193802),(2.10478686152771,5.26418135486344,0.739387833193803),
(2.5511477975851,4.18657072932013,0.739387833193802),(3.26120550666496,
3.26120550666578,0.739387833193803),(4.18657072931932,2.55114779758594,
0.739387833193803),(5.26418135486263,2.10478686152856,0.739387833193803),
(6.42059999999992,1.95254145863829,0.739387833193833),(6.42059999999996,
10.0089375607161,0.421664498573499),(5.49186989903004,9.88666792333885,
0.4216644985735),(4.62643121964217,9.52819148493417,0.421664498573499),
(3.88326217763158,8.95793782236901,0.4216644985735),(3.31300851506638,8.21476878035845,
0.4216644985735),(2.95453207666165,7.34933010097061,0.4216644985735),(2.83226243928436,
6.42060000000072,0.4216644985735),(2.95453207666162,5.49186989903082,0.4216644985735),
(3.31300851506632,4.62643121964297,0.4216644985735),(3.8832621776315,3.88326217763239,
0.4216644985735),(4.62643121964207,3.31300851506721,0.4216644985735),(5.49186989902992,
2.9545320766625,0.4216644985735),(6.42059999999994,2.83226243928388,0.421664498573523),
(6.42059999999999,8.43970102223927,0.151488812675696),(5.89801820145865,
8.37090182326762,0.151488812675695),(5.41104948888064,8.1691927780665,0.151488812675695),
(4.99287997527431,7.84832002472637,0.151488812675694),(4.67200722193412,
7.43015051112009,0.151488812675695),(4.47029817673291,6.94318179854212,
0.151488812675694),(4.4014989777612,6.4206000000008,0.151488812675695),
(4.47029817673284,5.89801820145947,0.151488812675695),(4.67200722193398,
5.41104948888147,0.151488812675694),(4.99287997527411,4.99287997527514,
0.151488812675694),(5.4110494888804,4.67200722193496,0.151488812675695),
(5.89801820145838,4.47029817673377,0.151488812675694),(6.42059999999998,
4.40149897776071,0.151488812675703),(6.42060000000005,6.42059999999995,
2.13731254916638E-15),(7.827762928552,1.16899645611609,5.86758330849528),
(7.88028279931025,0.972989630039358,4.67431301902416),(9.13903002897003,
1.71214107300289,5.86758330849528),(9.24049062808747,1.53640616036496,4.67431301902416),
(10.2650406153317,2.57615938466812,5.8675833084953),(10.4085275706501,2.43267242934972,
4.67431301902416),(11.1290589269971,3.70216997102989,5.86758330849535),
(11.3047938396349,3.60070937191237,4.67431301902416),(11.6722035438839,
5.01343707144788,5.8675833084953),(11.8682103699605,4.9609172006896,4.67431301902416),
(11.8574600579402,6.42060000000005,5.86758330849528),(12.060381256175,6.42059999999994,
4.67431301902416),(11.6722035438839,7.82776292855211,5.86758330849528),
(11.8682103699605,7.88028279931029,4.67431301902416),(11.1290589269971,
9.13903002897012,5.86758330849528),(11.3047938396349,9.24049062808752,4.67431301902416),
(10.2650406153318,10.2650406153318,5.86758330849532),(10.4085275706502,
10.4085275706502,4.67431301902416),(9.13903002897012,11.1290589269971,5.86758330849528),
(9.24049062808753,11.304793839635,4.67431301902416),(7.8277629285521,11.672203543884,
5.86758330849526),(7.88028279931032,11.8682103699606,4.67431301902416),
(7.91593189270442,0.839945402248588,3.27376315980209),(9.3093593880739,
1.41712196901421,3.27376315980209),(10.5059227050467,2.33527729495311,3.27376315980209),
(11.4240780309856,3.53184061192596,3.27376315980209),(12.0012545977512,
4.92526810729545,3.27376315980209),(12.1981187761479,6.42059999999995,3.27376315980209),
(12.0012545977513,7.91593189270446,3.27376315980209),(11.4240780309857,
9.30935938807395,3.27376315980209),(10.5059227050468,10.5059227050468,3.27376315980209),
(9.30935938807394,11.4240780309857,3.27376315980209),(7.91593189270447,
12.0012545977514,3.27376315980209),(7.89881982217725,0.903808518878672,
2.3299723489458),(9.27630140634696,1.47438007296095,2.3299723489458),(10.4591716589438,
2.38202834105603,2.3299723489458),(11.3668199270389,3.5648985936529,2.3299723489458),
(11.9373914811212,4.94238017782261,2.3299723489458),(12.132002812694,6.42059999999995,
2.3299723489458),(11.9373914811212,7.89881982217728,2.3299723489458),(11.3668199270389,
9.276301406347,2.3299723489458),(10.4591716589439,10.4591716589439,2.3299723489458),
(9.27630140634701,11.366819927039,2.3299723489458),(7.8988198221773,11.9373914811213,
2.3299723489458),(7.83837812791736,1.1293799927521,1.64640573287728),(9.15953701940638,
1.67662192365666,1.64640573287728),(10.2940418793303,2.54715812066957,1.64640573287728),
(11.1645780763432,3.68166298059347,1.64640573287728),(11.7118200072477,
5.00282187208249,1.64640573287728),(11.8984740388129,6.42059999999995,1.64640573287728),
(11.7118200072477,7.8383781279174,1.64640573287728),(11.1645780763432,9.15953701940643,
1.64640573287728),(10.2940418793303,10.2940418793303,1.64640573287728),
(9.15953701940643,11.1645780763433,1.64640573287728),(7.83837812791742,
11.7118200072478,1.64640573287728),(7.73765431240528,1.50528638977541,1.15257189825204),
(8.96495354995537,2.01365037905883,1.15257189825204),(10.0188592978191,
2.82234070218079,1.15257189825204),(10.827549620941,3.87624645004448,1.15257189825204),
(11.3359136102244,5.10354568759458,1.15257189825204),(11.5093070999108,
6.42059999999995,1.15257189825204),(11.3359136102244,7.73765431240532,1.15257189825204),
(10.827549620941,8.96495354995543,1.15257189825204),(10.0188592978191,10.0188592978191,
1.15257189825204),(8.96495354995541,10.8275496209411,1.15257189825204),
(7.73765431240532,11.3359136102245,1.15257189825204),(7.5770186451373,2.10478686152727,
0.739387833193835),(8.65462927068073,2.55114779758473,0.739387833193835),
(9.57999449333519,3.26120550666467,0.739387833193836),(10.2900522024151,
4.18657072931915,0.739387833193836),(10.7364131384726,5.26418135486259,
0.739387833193836),(10.8886585413615,6.42059999999997,0.739387833193835),
(10.7364131384726,7.57701864513735,0.739387833193836),(10.2900522024151,
8.65462927068079,0.739387833193836),(9.5799944933352,9.57999449333527,0.739387833193834),
(8.65462927068074,10.2900522024152,0.739387833193835),(7.57701864513731,
10.7364131384727,0.739387833193836),(7.34933010096997,2.9545320766612,0.421664498573522),
(8.21476878035795,3.31300851506598,0.421664498573522),(8.95793782236863,
3.88326217763127,0.421664498573522),(9.52819148493389,4.62643121964197,
0.421664498573522),(9.88666792333865,5.49186989902996,0.421664498573523),
(10.0089375607159,6.4206,0.421664498573521),(9.88666792333863,7.34933010097004,
0.421664498573522),(9.52819148493387,8.21476878035803,0.421664498573522),
(8.95793782236859,8.95793782236872,0.421664498573522),(8.2147687803579,
9.52819148493399,0.421664498573522),(7.34933010096991,9.88666792333876,
0.421664498573522),(6.94318179854146,4.47029817673243,0.151488812675705),
(7.43015051111959,4.67200722193369,0.151488812675705),(7.84832002472601,
4.99287997527397,0.151488812675705),(8.16919277806624,5.41104948888043,
0.151488812675705),(8.37090182326745,5.89801820145858,0.151488812675705),
(8.43970102223912,6.42060000000007,0.151488812675704),(8.3709018232674,
6.94318179854156,0.151488812675705),(8.16919277806615,7.43015051111969,
0.151488812675704),(7.84832002472587,7.84832002472612,0.151488812675704),
(7.43015051111942,8.16919277806636,0.151488812675704),(6.94318179854127,
8.37090182326757,0.151488812675705)));
#2679=IFCCARTESIANPOINTLIST3D(((7.98771659104031,8.69940084608593,8.91609276227832),
(7.09172217288145,9.34748493560244,8.65819725084397),(7.09171363433498,
9.46822982713382,10.7026812252415),(7.96717885470072,7.41050015788041,1.04947916273193),
(7.09172516404131,8.05744902021838,0.728054158466699),(7.09159445929378,
8.75157941664926,4.66153371020862),(2.98342536817443,9.32491815008212,9.52322691377251),
(2.98342536817443,9.31141482189832,10.7302457480545),(3.92228665555794,
9.76996245120037,10.6130976542916),(3.92223373442648,9.03367703326867,4.52182392982137),
(3.93853197746996,8.30202376942782,0.606541556182053),(2.98342536817457,
7.96871733492675,0.772138913821707),(9.04645694791,6.89334679710115,9.63478513285687),
(8.65404555829221,7.84829576674278,9.25477740134654),(8.65404240194541,
7.92150662547951,11.1620193450981),(7.98778125531723,8.71703495923174,12.8984716559222),
(7.09181147734966,9.39974062070892,12.7552581422539),(7.09172516404131,
8.92356012116045,16.7613237965831),(7.98771039140198,8.79959835127846,10.9012484793102),
(8.14114507608419,8.06645514985442,16.8847621934535),(8.598697177667,7.29439025549152,
16.9959533030964),(8.65407835335918,7.82048859748012,13.0865491186522),
(8.89978115253063,6.78634676395622,17.0691206235939),(8.98762118325408,
6.29886616619967,17.1393265207792),(9.0464653792251,6.8145689639085,13.2976018463165),
(9.16931344001706,5.96525380010832,11.1421413260118),(9.16931344001706,
5.88022798470556,10.0196345282385),(9.04645613225243,6.93627687025724,11.4546038909766),
(9.16931344001706,5.89536791014237,12.5958453758295),(9.16931344001706,
5.9361780271239,11.7469496637124),(9.16931344001705,5.77805899959474,13.5052372253889),
(7.98762544596938,8.12620223419461,4.97046532943395),(7.09176321051202,
9.08316963777244,6.63730457713717),(8.89978115253063,6.12370073460904,1.68880250680396),
(8.598697177667,6.58337792475,1.46042009297957),(8.65400028470116,7.30488129613406,
5.37617641907711),(8.14114507608419,7.28194138862729,1.11335129445836),
(9.16931344001705,4.7702959265621,2.36121754843029),(8.98762118325407,5.68262882805279,
1.90794121729459),(9.04644563668474,6.38332994691995,5.83141544602137),
(8.65406012505148,7.62048194507167,7.31839319983739),(7.98774589406189,
8.45085992915849,6.93173339018368),(9.04646059127479,6.68879567070228,7.75222279243413),
(9.16931344001706,5.44051276294137,6.29074171239435),(9.16931344001706,
5.74146885263656,8.18774302254961),(5.73501628651071,8.47491348726214,0.520644383271804),
(4.96700201479729,8.45022039216293,0.532912707183705),(4.96460218791876,
9.20188940564111,4.43892335283668),(2.98342536817445,8.96816362350665,6.68239791175388),
(2.98342536817444,9.22142346096808,8.69910082009035),(3.92229041025797,
9.63995377734019,8.54181298955589),(4.96471048528149,9.81422573460581,8.47246432563484),
(6.05052274425443,9.11618909043352,4.4813734238968),(2.98342536817442,9.28610647190449,
12.9924773024785),(2.98342536817457,8.82549226795334,16.7754473158597),
(3.93853197746996,9.19386852931012,16.7223945665825),(6.05065814428072,
9.72536557868254,8.5078251178016),(4.96470334600238,9.94976308068861,10.5596870835466),
(2.98342536817442,9.28853981258507,12.7749688488804),(3.92232973723068,
9.70792429477729,12.6903730513719),(4.96478532352028,9.89147160897163,12.6520088358867),
(6.05064918654399,9.85808892931066,10.586904797875),(6.05075199150396,9.79783063693615,
12.6717255847891),(5.73501628651072,9.38494935251168,16.6948755213334),
(6.03560321343466,9.28272582212534,16.7095975322139),(4.30192119252196,
9.33402456964679,16.7022095979847),(4.96700201479729,9.35765810004127,16.6988059482546),
(6.03560321343466,8.38242174984753,0.566597253465516),(4.30192119252194,
8.42883679995901,0.543536763605286),(2.98342536817448,8.65816854309169,
4.7056408797406),(2.98342536817446,8.87596733085427,5.94824071812375),(9.16931344001706,
5.72341754853664,7.94943055868382),(9.16931344001705,5.29053944767417,17.2845435453248),
(7.96717885470072,8.20854061199972,16.8642993537163),(2.12933245661471,
8.71204491354184,9.81000207589274),(2.12933245661471,8.68647487077852,10.9172653094736),
(2.12933245661474,7.72464213015556,3.02330968236555),(2.12933245661478,
8.06831459755489,4.98760250274015),(2.12933245661471,8.63246045130886,13.2562594721185),
(2.12933245661471,8.17681633917281,16.8688682146034),(2.1293324566147,8.64066570386167,
12.9009462425681),(2.1293324566148,8.27479865324297,6.16778184960252),(2.12933245661478,
8.37019083916045,6.96238978688069),(2.12933245661473,8.60780259420045,8.94167335952802),
(2.12933245661471,7.38179607085782,1.06374027644703),(0.925643957853783,
5.42847222540024,10.2176875952155),(0.925656151884684,5.42495858001861,
11.9036726730597),(0.924449379871938,6.37139484791118,11.6227422475555),
(0.991203162418136,4.28557618507794,2.60204190726896),(0.920789293025996,
4.58564638807364,4.42981283353657),(0.914322741951694,5.46914215738851,
4.01643844910301),(2.98342536817455,2.60737840164676,11.8349419550375),
(2.98342536817453,2.5030161773574,12.7618369043351),(2.24351910988165,3.03700335763933,
12.6126916555529),(2.98342536817457,1.57187451819732,3.95029618303887),
(2.98342536817453,2.2355092646683,7.87166609372733),(2.24350069022705,2.735960039527,
7.63172515581597),(1.15392298106253,7.22777587057087,9.50163742854776),
(1.15395741724141,7.28128239935065,11.3527211519158),(1.57535879109789,
8.08777377135563,11.1134551856572),(0.942971453051144,5.72113387603113,
17.2225302707351),(0.924160372920531,6.23774654537197,13.4155385317006),
(0.925516425633527,5.2713929661278,13.6192905833592),(0.924424170697338,
6.34582691811278,9.85262142631463),(1.15356251466152,7.16700599588901,13.2191440984266),
(1.19300821504637,6.65526751647919,17.0879983721931),(1.27164027890215,
6.94903574271682,17.0456905101317),(1.62946499707155,7.46125942542826,16.9719211675778),
(1.57532956914572,8.00953025578333,9.19051832621056),(1.5750236047344,7.9909718850718,
13.0444584987252),(0.925826186101671,4.96981050774401,6.52708532466594),
(0.925585395058717,5.25956439893578,8.41845475395168),(0.924302772000039,
6.15460748222711,8.00211311310406),(1.1930082150464,6.00510037623438,1.74772697968835),
(1.1400376722746,6.32490434549325,3.62620556790606),(1.56346439427562,7.09240013816204,
3.28838604656775),(0.924801884598943,5.85502734114755,6.08864253782772),
(0.942971453051192,5.15989728986142,2.16765105940718),(1.15443937457857,
6.70580933669132,5.66672360697774),(1.57576786094352,7.4595723609865,5.29229519391652),
(1.62946499707158,6.73436096544581,1.38540686191172),(1.15375697237997,
7.01520001899774,7.60198364429541),(1.57518862514598,7.77815035992941,7.24747052442122),
(1.16423603417201,3.73951341022037,4.83337644866793),(1.21016546315098,
3.45027131913001,3.01704823269909),(1.6368475719369,2.68146228285035,3.39901729107613),
(2.98342536817451,2.45662862551181,9.17824446819211),(2.98342536817452,
2.48602449956189,9.69629462199504),(2.24352679874818,3.00145993769004,9.46948081322373),
(2.28327087824873,2.08694745199417,3.69439138193257),(1.61969466897245,
3.36138607875374,7.32266025986366),(1.61971969074795,3.63369044713565,9.17513402098775),
(2.98342536817454,2.57918419106196,11.3380689889564),(2.24352044795941,
3.11392405211403,11.138747275789),(1.61971360774831,3.76195686516085,10.8808701645503),
(1.1652792695691,4.39937823403342,8.8187294424964),(1.16533176983465,4.11887819338932,
6.9481328281039),(1.16529204057947,4.5467914506955,10.5685509782866),(2.28327087824866,
2.32485449035162,17.7116550532612),(2.24353441374477,2.83350201680612,14.1317742624793),
(2.98342536817449,2.30929756545286,14.240871498593),(1.61971235731439,3.7055885062535,
12.4141549041121),(1.16529473136797,4.51531614288559,12.17372889495),(2.9834253681745,
2.40725216645148,13.6123666686975),(1.619726712309,3.51608002785286,13.9884644482059),
(1.16526395524057,4.3427227785714,13.8147370782373),(1.63684757193687,2.9819229656115,
17.617025478197),(1.21016546315098,3.83162452899691,17.4946533040598),(0.991203162418117,
4.75481847834745,17.3616969160755),(1.04765455564093,4.15525134468736,17.4480452727795),
(1.68715421130104,2.8817415208398,17.6314533923414),(2.98342536817442,1.75558662739499,
17.7936397747896),(2.71588571484586,1.92070945456299,17.7698591437646),
(1.04765455564097,3.74308850460614,2.87156723633337),(1.6871542113011,2.59081822224489,
3.44405217599955),(1.27164027890213,6.27090154294449,1.61566841043026),
(0.911345675689066,5.60298044009837,17.2395464720226),(2.71588571484597,
1.72127746988429,3.87606799101505),(0.911345675689085,5.05299219149868,
2.22076495093338),(6.05312937651894,2.16515565221169,11.5162963020892),
(7.10612388608282,2.55075527412001,11.3628530377702),(7.10611887662854,
2.45597794977073,12.7852026947708),(7.10605109372873,2.19251180823961,7.90061135870525),
(6.05305283537781,1.82040618040731,8.08427397966634),(6.04575818013413,
1.15247485744186,4.15866742471638),(9.05072080527052,4.98217994075639,12.0349345488725),
(9.0507146868991,4.55548751073536,6.73407880606221),(9.00164421923011,3.87382354803458,
2.80661386159568),(4.95465817275566,2.07122561347345,11.5536740972417),
(4.95465403719855,1.96124438848267,12.9321442158787),(3.90899393229935,
2.13732293077432,12.8798605125744),(6.05318389895054,1.83406870163809,14.3416909532666),
(7.1061760407994,2.24026010336383,14.2565752540502),(7.10612560243049,1.68324513099591,
17.8040582400205),(6.05312414529782,2.05815229569101,12.9033569949529),
(5.90972673224531,1.24187440268973,17.8676234939293),(4.95409214525046,
1.23312281839706,17.8688838780962),(4.95470128053827,1.73511987680326,14.3623747687531),
(3.9304950315297,1.41233560095967,17.8430740422898),(3.90901849284685,1.91490531504169,
14.3245943907635),(3.90899608434922,2.24189313895736,11.4857593388852),
(7.10614729754083,2.45200707514638,9.72523904060403),(4.43800435247818,
1.09487351805423,4.18728562290198),(4.95409214525056,1.09914984073966,4.18516100824166),
(4.95459761638878,1.72976459774474,8.12894222268238),(5.90972673224537,
1.10706826454388,4.18122688059541),(3.93049503152982,1.26130136850898,4.10459891355533),
(4.95467764370269,1.9841514263891,9.94314335214239),(3.9090061509481,2.15066445159512,
9.86562348145999),(6.05315398811748,2.07579585072917,9.90044846884449),
(3.90896478203765,1.89445124327551,8.04751321893257),(8.22012622433671,
2.35427886061726,3.56157253927116),(8.61114824790391,2.9844588351947,3.24847885782559),
(8.66692636155656,3.64456751987149,7.18377228496669),(9.0507234127956,4.84091121049363,
8.61272317545584),(8.9320246519688,3.50159053614442,2.99155119497638),(8.66696092140877,
3.92001494997609,9.04158573330763),(9.05072128633499,4.99931225664725,10.3884875779646),
(8.0058395775646,2.82408365840199,7.58882763239879),(7.10612560243054,1.50642001213768,
3.98281608702855),(7.97613587963543,2.16857934786062,3.65383403033267),
(8.00590867756406,3.09052740155716,9.42787798917963),(8.66695250474623,
4.05541582478219,10.7640966682575),(8.00589185673687,3.20521540629724,11.1024208061615),
(9.05072625098438,4.81947807815526,13.7159911095998),(8.66695064799458,
4.00835075154605,12.3241571829435),(8.00588821302538,3.1311901133248,12.5846684942332),
(8.6669717419894,3.82522617183471,13.9243702647467),(8.00592971752642,2.92965625975425,
14.1120804808595),(8.22012622433668,2.62031396745165,17.6691036209229),
(7.9761358796354,2.41507552869348,17.698661615223),(8.93202465196878,3.8883434499136,
17.4864847682275),(8.6111482479039,3.31680020050091,17.5687971859726),(9.0016442192301,
4.29974206416159,17.4272360331046),(4.43800435247809,1.2283965497141,17.8695645450494),
(6.04575818013406,1.29205858517555,17.8603960769364)));
#2680=IFCCARTESIANPOINTLIST3D(((7.78180000000011,8.98569964940185,13.4785),
(6.47417596720517,13.2005555810339,0.000100000000017913),(7.78180000000011,
13.4784996494018,0.000100000000037468),(5.95441360998914,13.0900766814985,
0.000100000000011642),(4.44299892809525,11.9919696376509,0.00010000000000291),
(5.30744155614002,12.6200239699462,0.000100000000008731),(3.64113755910407,
10.6031050059315,0.000100000000003088),(4.31075465258039,11.762915833449,
0.000100000000006364),(3.50889328358921,10.3740512017296,0.0001),(3.31361202848948,
8.51607416963289,0.000100000000011642),(3.39720359088703,9.311394759581,
0.000100000000008731),(3.89092106587738,6.73929964940171,0.000100000000031743),
(3.47781567351774,8.01070731458525,0.00010000000001934),(4.7854397063127,
5.74583605258632,0.00010000000003492),(5.14099841850049,5.35094809707405,
0.000100000000040745),(6.8476943554941,4.59107810882501,0.000100000000043656),
(5.87156203474715,5.02568021867831,0.000100000000040745),(8.45141709347643,
4.59107810882503,0.00010000000004618),(7.11218290652381,4.59107810882501,
0.000100000000046045),(8.71590564450614,4.59107810882503,0.000100000000043656),
(10.4226015814997,5.3509480970741,0.000100000000040745),(9.69203796525307,
5.02568021867835,0.000100000000040745),(11.6726789341228,6.73929964940185,
0.000100000000032374),(10.7781602936875,5.74583605258639,0.000100000000036321),
(12.0857843264824,8.01070731458531,0.000100000000037835),(12.2499879715107,
8.51607416963294,0.000100000000034925),(12.054706716411,10.3740512017296,
0.000100000000032014),(12.1663964091131,9.31139475958104,0.000100000000034925),
(11.2528453474198,11.7629158334491,0.000100000000035827),(11.9224624408961,
10.6031050059315,0.000100000000031052),(11.1206010719049,11.9919696376509,
0.000100000000034925),(9.60918639001106,13.0900766814985,0.000100000000034925),
(10.2561584438602,12.6200239699462,0.000100000000034925),(9.08942403279503,
13.2005555810339,0.000100000000034474)));
#2681=IFCCARTESIANPOINTLIST3D(((12.000035623731,3.56237310153773E-5,3.59370000000005),
(3.56237309519202E-5,3.56237309460807E-5,3.5937),(3.56237309528582E-5,2.93863608466212,
2.78092077222723),(12.000035623731,2.93863608466219,2.78092077222725),(3.5623730955108E-5,
5.6865220653193,2.23194191344762),(12.000035623731,5.68652206531939,2.23194191344764),
(3.56237309550618E-5,7.72846921797043,2.15068135964469),(12.000035623731,
7.72846921797052,2.15068135964472),(3.56237309570197E-5,9.18212469647124,
2.44726788666241),(12.000035623731,9.18212469647134,2.44726788666245),(3.56237309634072E-5,
10.3017975988992,3.09416705793496),(12.000035623731,10.3017975988993,3.09416705793501),
(3.56237309668594E-5,11.1303238271614,4.11637057440334),(12.000035623731,
11.1303238271615,4.11637057440339),(12.000035623731,11.9331946287012,9.52918161280188),
(3.56237309882999E-5,11.9331946287011,9.52918161280184),(3.56237309917675E-5,
11.4520530196514,11.3993978793655),(12.000035623731,11.4520530196515,11.3993978793655),
(3.56237309947772E-5,10.7378800730514,12.6472749123606),(12.000035623731,
10.7378800730515,12.6472749123606),(3.56237309908097E-5,9.7745337415019,
13.4646099914071),(12.000035623731,9.77453374150197,13.4646099914071),(3.56237309832677E-5,
8.52450838507296,13.9270525982087),(12.000035623731,8.52450838507304,13.9270525982087),
(3.56237309819346E-5,8.36440669327847,13.9580465984157),(12.000035623731,
8.36440669327854,13.9580465984157),(3.56237309728154E-5,6.53290934780139,
14.0377251867336),(12.000035623731,6.53290934780147,14.0377251867336),(3.56237309608598E-5,
3.66034093007487,13.580601472828),(12.000035623731,3.66034093007493,13.580601472828),
(3.56237309521336E-5,3.5623730946827E-5,12.5937),(12.000035623731,3.56237310159367E-5,
12.5937000000001),(4.57758631818715,12.0208519794837,7.61086559606823),
(3.56237309832068E-5,12.0209350792972,7.61085478261137),(3.10115036289269,
11.7958146892504,5.90528285739555),(3.33272179725709,11.7263674667051,5.62829511736462),
(3.56237309744756E-5,11.7277828274938,5.62767267726536),(8.68193533412983,
11.727398899119,5.62782402641685),(12.000035623731,11.7277828274939,5.62767267726542),
(8.09928297830169,12.0069887052451,7.3670060028364),(7.41463954287985,12.0205869166489,
7.61090970932408),(12.000035623731,12.0209350792973,7.61085478261142),(8.9359411035137,
11.8129547552475,5.98242148675326),(9.00003562373093,11.8750527629544,6.29508376926321),
(8.90915316355799,11.9341467012463,6.66501705277272),(8.6201476217649,11.977770710071,
7.02575217881544),(8.22866860658416,11.63071074409,5.29043595645168),(8.66905657456281,
11.7230609925301,5.60984500394887),(3.40334111827362,11.7051890527262,5.54382577776506),
(7.4714795426918,11.5281761302004,4.9870358706731),(3.93859910704579,11.6033645468155,
5.20494953468713),(6.52486398216909,11.4636549409597,4.81709865582256),
(4.71408665860433,11.5103032908724,4.93851479207616),(5.53712448362505,
11.4616557720407,4.81205487000696),(3.32714164376353,11.9725954872721,6.97646411192229),
(3.06336898921802,11.9256950306372,6.60584179458511),(3.75907361121469,
12.0016057516348,7.29278043174156),(4.51755081725975,12.0204384085141,7.6000680829465),
(3.00241152115253,11.863977578236,6.23481576890388),(7.33019614319314,12.0222641096828,
7.64099260664906),(6.50744970447466,12.0271234855317,7.77476345225691),
(5.48472293484532,12.0271010381953,7.77401605488445)));
#2682=IFCCARTESIANPOINTLIST3D(((6.27551233417533,7.5000105325918,1.84400901157875),
(4.95474580622064,7.50001053259181,1.86980415827464),(4.95474580622063,
6.27548724303618,1.77466593443737),(15.000035623731,7.5000105325918,1.49858058983303),
(14.2383303254824,7.5000105325918,1.68967402830169),(14.2383303254824,6.27548724303617,
1.70723468259626),(7.50605669432379,13.6777170452482,1.93560510185292),
(7.53905347888618,15.0032455169685,1.88859972957176),(6.27955071286552,
15.00034577065,1.93356436024935),(13.9102084432052,13.9124049547169,2.30583308896575),
(15.000035623731,14.5867573426454,1.5128198689705),(15.000035623731,15.0000105325918,
1.93875932375063),(7.50003562373096,4.95472071508149,1.84125677848346),
(6.27551233417533,4.9547207150815,1.83647148432418),(6.27551233417534,3.30075088376784,
1.92962527191859),(3.30077597490696,7.50001053259181,1.86808736472627),
(7.50003562373096,6.27548724303617,1.73271890630208),(3.30077597490695,
6.27548724303618,1.69587934731789),(3.30077597490694,4.95472071508149,1.65562029183978),
(4.95474580622063,4.9547207150815,1.81486246565565),(2.14168670185173,6.27548724303618,
1.81926313830533),(2.14168670185173,7.50001053259181,1.90800149058855),
(1.32282832019869,7.50001053259181,1.97777400212673),(1.32282832019869,
6.27548724303618,1.93856172162374),(3.56237309950465E-5,6.27548724303618,
2.1262541256213),(2.14168670185173,4.9547207150815,1.8008202057411),(1.32282832019869,
4.9547207150815,1.97189359604043),(3.56237309749177E-5,4.95472071508149,
2.37650155559882),(3.30077597490695,4.19492904440796,1.72452666122253),
(4.95474580622064,4.19492904440797,1.86191364251208),(7.50003562373097,
3.30075088376783,1.93060175605675),(6.27551233417534,1.05325917604205E-5,
1.93875932375063),(4.95474580622064,3.30075088376784,1.86366918809022),
(4.95474580622064,1.32280322905956,1.87469445468997),(4.95474580622064,
1.05325917598748E-5,1.93875932375063),(4.19495413554709,3.30075088376784,
1.7628603235939),(4.19495413554708,2.14166161071261,1.68512316141586),(4.95474580622063,
2.14166161071261,1.83324004528767),(2.14168670185173,4.19492904440797,1.83714247236951),
(2.14168670185173,3.30075088376784,1.81959904917086),(3.56237309657571E-5,
4.19492904440795,2.49038797015704),(1.32282832019869,4.19492904440797,1.99772997122474),
(1.32282832019869,3.30075088376784,2.00159413953116),(3.56237309614627E-5,
3.30075088376783,2.54377711160453),(3.30077597490695,3.30075088376784,1.68948286631111),
(2.14168670185172,2.14166161071261,1.76226366974761),(4.19495413554709,
1.32280322905956,1.7846954049841),(3.30077597490694,2.14166161071261,1.57790945066671),
(3.30077597490695,1.32280322905956,1.71955627943336),(4.1949541355471,1.0532591759511E-5,
1.93875932375063),(3.30077597490697,1.05325917596929E-5,1.93875932375063),
(1.32282832019869,2.14166161071261,1.98817396249951),(3.56237309614991E-5,
2.14166161071261,2.54549772948631),(2.14168670185173,1.32280322905956,1.83120410746694),
(2.14168670185174,1.05325917596929E-5,1.93875932375063),(1.3228283201987,
0.761715830840386,1.94862039622665),(1.3228283201987,1.05325917597838E-5,
1.93875932375063),(0.761740921979545,1.32280322905956,2.07653408335157),
(1.32282832019869,1.32280322905956,1.96517416590292),(0.761740921979552,
0.761715830840386,1.99077832581209),(3.56237309994325E-5,0.761715830840386,
2.06915429698748),(3.56237310098692E-5,1.053259175942E-5,1.93875932375063),
(3.5623730982296E-5,1.32280322905956,2.28398552235805),(8.72455891328657,
6.27548724303617,1.68304371356943),(10.0453254412413,6.27548724303617,1.60937334434211),
(10.0453254412413,7.5000105325918,1.61553526464893),(14.2383303254824,4.95472071508148,
1.84999872314221),(13.6772429272632,4.95472071508148,1.88673586287481),
(13.6772429272632,3.30075088376783,1.93484707784769),(11.699295272555,6.27548724303617,
1.73154806807455),(11.699295272555,7.5000105325918,1.63410014279513),(10.8051171119148,
7.5000105325918,1.6194873081429),(15.000035623731,4.95472071508148,1.74137822270637),
(12.8583845456102,6.27548724303617,1.74409212678875),(12.8583845456101,
7.5000105325918,1.62238548436982),(13.6772429272632,7.5000105325918,1.69233473574082),
(11.699295272555,4.95472071508147,1.74431315163044),(12.8583845456102,4.95472071508148,
1.87625362888279),(13.6772429272632,6.27548724303617,1.75997685900662),
(10.0453254412413,4.95472071508147,1.61109042080252),(10.8051171119148,
4.95472071508147,1.61911359968447),(10.8051171119148,4.19492904440794,1.63199026874048),
(11.699295272555,4.19492904440794,1.75144827528914),(12.8583845456102,3.30075088376783,
1.91870937359671),(12.8583845456102,2.14166161071261,1.93509045682401),
(11.699295272555,3.30075088376782,1.82710554051313),(12.8583845456102,1.32280322905956,
1.93540714157257),(11.699295272555,2.14166161071261,1.91943493083555),(14.2383303254824,
1.76726774475585,1.91038926521151),(15.000035623731,1.76726774475585,1.8699792220814),
(15.000035623731,2.14166161071261,1.91894518857262),(13.2327784115669,1.32280322905956,
1.92801822312755),(13.6772429272632,1.32280322905956,1.90675557586643),
(13.6772429272632,2.14166161071261,1.93536076738232),(14.5867824337846,
1.06096680002671,1.71596464212405),(15.000035623731,1.06096680002671,1.59555247777661),
(15.000035623731,1.32280322905956,1.73158686957491),(14.2383303254824,2.14166161071261,
1.93056187113554),(14.5867824337846,1.32280322905956,1.80427199538384),
(13.9390793562961,0.761715830840386,1.7972484675014),(14.2383303254824,
0.761715830840387,1.71052812116378),(14.2383303254824,1.32280322905956,
1.85333576407027),(14.5867824337846,0.413263722538179,1.37303129227203),
(15.000035623731,0.41326372253818,1.06727626649712),(15.000035623731,0.761715830840387,
1.38522768482735),(14.5867824337846,0.761715830840386,1.57943119469673),
(14.2383303254824,0.413263722538179,1.57943125611753),(14.5867824337846,
1.05325917604368E-5,1.06727626807292),(14.2383303254824,1.05325917598748E-5,
1.38522778039806),(13.9390793562961,0.413263722538179,1.71596531135116),
(13.6772429272632,0.761715830840386,1.85333802024453),(13.9390793562961,
1.05325917596019E-5,1.59555350852774),(12.8583845456102,1.05325917596929E-5,
1.9192235618118),(13.2327784115669,1.05325917597245E-5,1.87003492998141),
(10.8051171119148,3.30075088376782,1.75805600667111),(10.0453254412413,
3.30075088376782,1.77575449644466),(10.0453254412413,2.14166161071261,1.91107027576262),
(10.0453254412413,4.19492904440794,1.65069927144681),(10.0453254412413,
1.05325917596929E-5,1.93875932375063),(8.72455891328658,4.95472071508148,
1.77673335110717),(8.72455891328658,7.5000105325918,1.78279445731793),(7.50003562373096,
7.5000105325918,1.87966798668729),(8.72455891328659,3.30075088376783,1.89211839406824),
(8.72455891328659,2.14166161071261,1.93143775636662),(8.72455891328659,
1.05325917596941E-5,1.93875932375063),(15.000035623731,10.0453003501022,
1.4711341273609),(14.2383303254824,10.0453003501022,1.73869955645438),(14.2383303254824,
9.36305314677204,1.69901450977995),(10.0621874144079,13.6767214025219,1.91806045101115),
(10.1545947176102,14.9967935342049,1.8041032875793),(9.52027554933442,15.0097469844875,
1.73718721226682),(15.000035623731,11.6992701814158,1.81619415062506),(13.6772363896751,
11.6992636697952,1.91946127902194),(13.6772429272632,10.8050920207757,1.8914627550737),
(11.3279791681684,14.6988958688214,2.18774781452213),(11.5160793484747,
14.655647571912,2.27387769793196),(11.4724849443013,14.8409536273032,2.35361693767088),
(15.000035623731,12.2304773857726,1.77393100657198),(15.000035623731,12.858359454471,
1.55804777956385),(14.2156494372533,12.8357366162691,1.80388604985107),
(12.0210545480774,14.1511820852406,2.8132894748948),(12.1522391077941,14.0781083408717,
2.63989760104514),(12.2105479055112,13.9611987863215,2.73622344226053),
(15.000035623731,13.4439694631903,1.30630492091155),(15.000035623731,13.6772178361241,
1.24579196877086),(14.4259487994007,13.5169123160831,1.6419048618389),(12.4198488340077,
13.5698369487958,3.00158173294963),(12.5647270311895,13.4786603873022,3.02523029147542),
(11.9634534345626,13.2930282542596,3.71368093097424),(14.3203353393946,
13.6734820781377,1.73343936921321),(15.000035623731,13.9390542651569,1.23203909221498),
(15.000035623731,14.2383052343432,1.30284756061822),(13.101942454981,13.5319120109975,
2.81993421852295),(13.2265443587367,13.5775609659648,2.74911570430918),
(12.6797115544694,13.4453809290682,3.45762316784546),(13.5273021589626,
13.7110219820313,2.56291713824657),(13.5445452844714,13.961195679799,2.93016230502276),
(14.1570494140113,13.8099832886419,1.93472688034462),(13.8477151120512,
13.6820727952028,2.14563034582227),(13.0526515685725,13.6509726764106,3.24068460682652),
(13.4296575398245,13.5989037135133,2.49898790635751),(13.7036042947654,
13.7055728067667,2.32757538020562),(13.5956839262593,13.5973036402326,2.3028162604706),
(12.736546597039,13.4560328393514,2.98797616245936),(12.8108579623539,13.4615293067516,
2.96074602155555),(12.2010961263917,13.2666384597858,3.68083461645311),
(12.8956207075211,13.4752199802521,2.92444270368959),(12.9921640878836,
13.4982593233104,2.87792208531308),(12.402407816377,13.323219688573,3.59921008531574),
(13.3902791484814,13.5469343756061,2.41627712522252),(13.4877533483981,
13.5683092308394,2.3644498326195),(13.3021833031669,13.532300696801,2.45931017872347),
(13.222477782274,13.5236049228753,2.49447779397357),(12.6145352488121,13.4655105108278,
3.01930721411527),(12.6714738382428,13.4576787212248,3.00717864541307),
(12.0593332573989,13.2607067755376,3.71540632176694),(13.1502584731726,
13.5201137843348,2.52263109886759),(13.0846994302564,13.5211591833562,2.54454881867201),
(13.0250474364255,13.526133626127,2.56094226192908),(12.7880614616808,13.5198571472642,
2.77439452410493),(12.9706168638579,13.5344859071867,2.57246003350001),
(13.4668918997297,13.5444500615532,2.33138776281529),(13.9649002603127,
13.643964663285,2.00746907905359),(14.2473269620383,13.7450039478961,1.81708563045069),
(14.0596690894603,13.5941738152148,1.90492119911296),(13.7306899381334,
13.5770036615329,2.15107669848406),(13.8398706821357,13.5416144492912,2.03566225665743),
(14.1358674383861,13.5351729327287,1.83107768749277),(14.3790373183295,
13.597008488771,1.67695604569019),(13.9276568508822,13.4941931218321,1.94970585528459),
(13.9978108604776,13.4373139625956,1.88742112571277),(13.7483101823531,
13.4698431924254,2.03922922696183),(13.1642200327749,13.5215240033681,2.45793769795693),
(13.5979226843381,13.5257129305751,2.19069959489739),(13.7882148114509,
13.560992031907,2.08928513990351),(13.3016893720719,13.5236993961247,2.40784940440031),
(13.5358824143655,13.5372158823804,2.25651221004857),(13.6667206708126,
13.589251616489,2.22193019914187),(13.3730201656569,13.5187874395614,2.33107057548511),
(13.4371649501209,13.5093727649402,2.26321954804927),(13.6536332661809,
13.5103840257753,2.13307424307798),(13.4947653323605,13.4959190798489,2.20344701256072),
(13.2358290952677,13.516855496648,2.38090324324563),(13.3002241152642,13.5076597462379,
2.31255951930879),(13.1190636097232,13.5273825660528,2.41116652491496),
(13.239114012911,13.5026656097185,2.2834585228774),(13.3580492026369,13.4944026495107,
2.25208988062405),(13.4098985294028,13.4775125506272,2.19874244291222),
(13.8239709086434,13.4184625559819,1.96929435733676),(13.546413175473,13.4788527084374,
2.15097136206159),(13.7035866046632,13.4916362236244,2.08283073546156),
(13.5926538551581,13.4585650740867,2.10507419577025),(13.6708811256458,
13.4097311797593,2.03043243994733),(13.4563195988297,13.457382734319,2.15182579344809),
(13.5348520421051,13.4088158273021,2.07479742332883),(13.290042888014,13.4848150098167,
2.23028669255297),(13.3356398705487,13.4638252501449,2.18333629647007),
(13.4127781719419,13.4138035413698,2.10572186908828),(12.930152944292,13.573128313192,
2.4294171195737),(12.3303229882944,13.6956613655564,2.92979290035981),(11.8425792894748,
13.6273102162075,3.49899902114083),(11.8604162193091,14.0363607684128,3.17677138439079),
(12.6254432007897,13.5927302710207,2.76905067364144),(12.7934977288383,
13.5923976502462,2.58079421702792),(12.4215767237618,13.8015284674579,2.64992542479507),
(12.5482886311358,13.7583280629971,2.4984266688136),(12.6595744462156,13.6723163395967,
2.54952056555428),(12.3405992850722,13.9055273227233,2.57304836190265),
(12.2665600475509,13.8310786880799,2.83595162712383),(12.4469043534274,
13.8409751991997,2.43996010140829),(12.651325682287,13.7042003601274,2.37576032666016),
(12.779992115164,13.6362578265097,2.41186903243457),(13.0406674161136,13.5385725914007,
2.30859708625678),(13.0875508715875,13.5165481008345,2.25793078011039),
(12.9883805287382,13.5575558308994,2.36554441493521),(13.182315461487,13.516993907535,
2.34351242185072),(12.8773751168859,13.5881245261721,2.30145501724411),
(13.1295259612804,13.4918214272933,2.21295239479532),(13.2005369567399,
13.4354778267544,2.13792289106664),(12.9556753767011,13.5304563857028,2.21352668188738),
(13.0182487553647,13.4653885217022,2.14405447327964),(12.8016515054281,
13.5720368893786,2.19815021988653),(12.7346528004524,13.6414543210191,2.27705265775394),
(12.5333473799175,13.7694851330382,2.33201259920487),(12.5701799926211,
13.7316618577271,2.28611816381673),(12.6952288628357,13.6737750349891,2.32368701822834),
(12.6032547404489,13.6926979729788,2.2449719171807),(12.8551932860851,13.4975796204486,
2.13552905586697),(12.6594633640878,13.6119908562893,2.17522874805965),
(12.7043822889983,13.5285114750543,2.11971973691218),(13.0679319432845,
13.3947045242583,2.08963755456336),(14.533428398913,13.1795744792039,1.63215333628101),
(14.1585417665015,13.1531689742611,1.78953309053348),(13.8840454349469,
13.3586462008699,1.91849995156291),(14.0535130603027,13.3731317869537,1.84395987643656),
(15.000035623731,13.2327533204278,1.38826308655121),(14.4925692883778,13.350065676666,
1.61781602913878),(14.0974388047724,13.3034385437283,1.81528486496535),
(13.8501088010673,13.1440188188042,1.88720496614555),(13.5972067082648,
13.3512335699941,2.01653402875193),(13.7329934849766,13.3519369348458,1.97488264038663),
(13.7819741649667,13.2872810979302,1.93454781119327),(13.4740258911122,
13.3550661732035,2.04633894000483),(13.5223247315543,13.2896666063969,2.00160560741919),
(13.5895108938999,13.1453697608768,1.94466972935746),(13.6523040078462,
12.8335195930604,1.90583285096427),(12.8072967131363,13.1834223182122,1.99412051384754),
(12.8438621951252,12.8443365047929,1.95121229328192),(13.1071112041369,
13.3198840622884,2.04742017805106),(12.7681730854841,13.3568602333825,2.04146472775149),
(12.740047861725,13.4432087549174,2.07585693341496),(12.4237798985043,13.5801652441942,
2.08246397784229),(12.4673024777329,13.3871179843897,2.02217309359872),
(11.7461925764,14.607617029771,2.50223060800459),(11.8153057175885,14.5331467301085,
2.58475943801155),(11.7688353492531,14.603664553905,2.65714833184057),(12.3857795572026,
13.8720364361249,2.33181249825477),(12.4923884848816,13.8059882477724,2.38312399698475),
(11.9240796315519,14.3798670383732,2.60436003650895),(11.978837091907,14.2760935422296,
2.70240827047488),(11.8479667380208,14.3710884968873,2.8851365413869),(12.2600030703034,
13.9989676137091,2.4976159360014),(11.8621108674589,14.216455602915,3.02357048236165),
(12.0868512620062,14.1784891363736,2.55166969493561),(12.3480902204701,
13.9152315209277,2.38145868649049),(12.1755814830183,14.079197247439,2.42791480007045),
(12.2136959307614,14.029164957004,2.37478186738116),(12.3062935435019,13.9575913414104,
2.43655499053734),(12.248110296182,13.9789894291223,2.32682424727633),(12.1314033764891,
14.1141835009879,2.39582789088013),(11.8921899676642,14.4242363087772,2.56012874564545),
(11.8164646355547,14.49955386278,2.76317970523666),(12.0509513252901,14.2222628369328,
2.51122505581584),(11.9727991669186,14.2975389746538,2.43788695580198),
(12.0128999345289,14.2618831367799,2.47331121001068),(11.8959672594804,
14.3960864008824,2.45911987207996),(11.9308087161538,14.3294671658109,2.40485538716231),
(12.039017072022,14.1743481662285,2.33748927863294),(12.0858630013772,14.1458501373112,
2.36569013147524),(12.1455036129177,14.0319579974158,2.2777492581996),(12.1971655454941,
14.0067927511386,2.30154641188353),(12.0931441328199,14.0546051743623,2.25544225408308),
(11.9909713939338,14.199866863001,2.31116336677145),(12.0401488620655,14.074884697656,
2.23459052741996),(12.1710216945792,13.9758602200858,2.24013335186282),
(12.4776462259671,13.7387390735089,2.2109918279605),(12.524222156426,13.6478884657837,
2.14988465037431),(12.4502396279214,13.7836716759803,2.2470022353999),(12.2791427777297,
13.9287466828947,2.28359613514099),(12.4197191651019,13.8281418188465,2.28714035887972),
(12.30708782392,13.8785055791533,2.24468502573111),(12.3547835544171,13.7782708492887,
2.17831780575781),(12.3931330407133,13.6787108447716,2.12501142633752),
(12.2332212809541,13.811112917425,2.1484843417196),(12.1940009709746,13.9203466048843,
2.20626692406418),(12.0801380107128,13.9542553403318,2.17233560642426),
(12.1124776446352,13.8377287324182,2.12199972841929),(12.1592599558925,
13.6160553988921,2.04921018923719),(11.9326264121149,14.1090268011795,2.1969699106937),
(11.6083523435194,14.6241122791749,2.32397433893474),(11.6974049671818,
14.5833299275111,2.38043840871095),(11.635973309771,14.7514287197525,2.48555194025822),
(11.8918497896572,14.2428295093303,2.26373054991083),(11.8130749386999,
14.4560891280963,2.3927131420975),(11.8575707411185,14.4639669849143,2.51892500034024),
(11.7076606076534,14.6864698469836,2.56532748164996),(11.781237587319,14.5308565679723,
2.44484499954633),(11.8419645293727,14.3832206782589,2.34540930122912),
(11.7481077374884,14.4253890885883,2.29368422014272),(11.7897584580476,
14.2765327301384,2.22241983315769),(11.8238040999396,14.1358105063799,2.16416907329028),
(11.650966199778,14.4581624226053,2.24832002898602),(11.5567212432305,14.8019148257734,
2.41563786541225),(11.5520600977271,14.4835046758111,2.2080648076311),(11.6057773171257,
14.1727232271479,2.10981012029962),(11.7145862720005,14.1566268309671,2.13537348169738),
(11.6642948607649,13.6526728463698,2.00277454009263),(11.9925994764413,
13.8588978394256,2.09879113120188),(11.8738774348103,13.8755045233416,2.078498862641),
(11.9062649056675,13.6388577407974,2.023090400726),(11.6959947478853,12.8560448718688,
1.94470719518674),(12.2068688882948,13.2124636414598,1.97520119698613),
(12.2237843360063,12.8525918613881,1.94893485242747),(12.1887700245075,
13.4081417234614,2.00332316314824),(12.2899571349674,13.5999894768101,2.06496251959521),
(11.6992944073422,11.6992695746619,1.93491247773865),(13.6762365519101,
12.2294750077673,1.91429965155857),(12.857798516386,12.2299115091723,1.93685498967748),
(12.8583807386631,11.6992665053837,1.93580365244039),(14.2374150698324,
12.2295644726505,1.87166875248382),(10.8047895866783,12.8578219400876,1.93975016257589),
(10.0469155184881,12.8583126409832,1.93775230633148),(10.0453258580705,
11.699270169144,1.9440614342805),(10.7958369236004,14.2230751569525,1.96638317674362),
(10.7826097490432,14.9630728254268,2.00575411573049),(11.2132738678282,
13.6659811255007,1.9723412805636),(10.8016438707495,13.6715177651824,1.94910646012619),
(11.1590576001555,14.5394882115552,2.08002463283062),(11.1206179251257,
14.927194188594,2.15637327722914),(11.1850063086191,14.208281753204,2.02848560716361),
(11.3535755867932,14.5182596818971,2.13885035132236),(11.2969664560346,
14.8944924256782,2.24699216227574),(11.699295272555,10.8050920207757,1.91213522891102),
(15.000035623731,10.8050920207757,1.65991427644428),(12.8583845456102,10.8050920207757,
1.92430314138332),(12.8583845456102,10.0453003501022,1.88949283013031),
(13.6772429272632,10.0453003501022,1.84668656229681),(8.72455891328659,
10.8050920207757,2.14805762267117),(8.72455891328659,10.0453003501022,2.20636448310843),
(10.0453254412413,10.0453003501022,1.88186820671964),(8.74506523593222,
13.6788258710492,1.91346859387673),(8.85744437184725,15.0104309508258,1.76795732981976),
(9.36536575986113,12.8585011384936,1.94002504453861),(8.72649265121465,
12.8585110915138,1.94836033978817),(8.72455942020439,11.6992702211666,2.03142080806639),
(9.38733626299522,13.678720324253,1.90800675069672),(7.50060340825157,12.8584065296894,
1.98599855020476),(7.50003577257228,11.6992701937563,2.25388271474995),
(7.50003562373098,10.8050920207757,2.55632790746336),(7.50003562373098,
10.4127421564049,2.63616884884636),(7.50003562373098,10.0453003501021,2.65323158978436),
(8.72455891328659,9.36305314677202,2.16369291232617),(8.72455891328659,
8.72453382214744,2.04647741234648),(10.0453254412413,8.72453382214744,1.70575074250666),
(15.000035623731,8.72453382214744,1.45380716274449),(14.2383303254824,8.72453382214744,
1.69807642675316),(11.699295272555,9.36305314677203,1.76617088135234),(11.699295272555,
10.0453003501022,1.8539517581471),(10.8051171119148,10.0453003501022,1.83215692995024),
(15.000035623731,9.36305314677204,1.40744635914911),(12.8583845456102,9.36305314677203,
1.82196961618472),(13.6772429272632,9.36305314677203,1.80097979296644),
(11.699295272555,8.72453382214744,1.67971094267248),(12.8583845456102,8.72453382214744,
1.73100863037589),(13.6772429272632,8.72453382214744,1.75519357217684),
(10.8051171119148,8.72453382214744,1.64686856073339),(7.50003562373098,
9.69693841665379,2.61388390036061),(7.50003562373098,9.36305314677201,2.53189690652448),
(7.50003562373097,8.72453382214743,2.30228804043007),(4.19493855297138,
14.5867475595859,1.96666797753253),(4.19494423811177,14.238299020531,2.05139891948743),
(4.95479190162863,14.2383090258343,1.98170341520394),(6.89207594143154,
9.69693841665378,2.7247021394335),(4.95476305807623,13.677219255143,2.05805199812614),
(6.27613551971635,13.6772695686451,1.95196123811898),(6.89207594143154,
10.8050920207757,2.74823858865061),(6.2755711003197,12.8583643328268,2.03456512649748),
(6.89207594143154,10.4127421564049,2.81831284591867),(6.27551233417536,
10.8050920207757,2.84635522542801),(6.27551233417536,10.0453003501021,2.84331829639834),
(6.89207594143154,10.0453003501021,2.80458140229646),(5.96012527991924,
10.8050920207757,2.84061626212418),(6.27551234958054,11.6992701826946,2.46601202596288),
(5.6369930126946,11.6992701816768,2.47214801831906),(4.95474587186964,12.2304773911724,
2.30128927425112),(4.95474743306365,12.8583595882839,2.18899055166305),
(5.303107739669,10.8050920207757,2.70546887146747),(5.63699300955078,10.8050920207757,
2.79291446238769),(5.96012527991924,10.4127421564049,2.87286796062249),
(6.27551233417536,10.4127421564049,2.89242073415194),(5.63699300955078,
10.4127421564049,2.8115492261006),(5.63699300955078,10.0453003501021,2.74200302063899),
(4.95474580622066,10.8050920207757,2.58733127891833),(4.95474580622066,
10.4127421564049,2.57918528672864),(5.30310774089833,11.6992701815179,2.4493954342546),
(6.27551233417535,9.363053146772,2.57604479231308),(6.27551233417534,8.72453382214743,
2.26539560343584),(6.89207594143153,9.363053146772,2.60260094725354),(5.63699300955077,
8.72453382214743,2.18248372175319),(4.95474580622064,8.72453382214744,2.09108316328377),
(5.63699300955077,9.36305314677201,2.4646298514956),(4.95474580622066,10.0453003501021,
2.51265586093137),(4.19495413554711,10.0453003501022,2.2349037276524),(4.1949541355471,
8.72453382214744,2.00716478253043),(3.29997301489355,14.9996198822169,1.87788781644224),
(2.76677498171254,14.9986516189921,1.8225796377953),(2.76775516712611,14.5858751963915,
1.94026814482162),(4.58730399991791,11.2301561202153,2.44066592926588),
(4.58730399991791,10.8050920207757,2.45437934636001),(4.19493013116691,
14.9999954621514,1.92604399095266),(3.30025472898812,14.5865037498012,1.96741189980975),
(4.58730401753221,12.2304773863583,2.36825967268655),(4.19495290671879,
13.2327525489442,2.39756643684222),(4.19495378623742,12.8583592351672,2.47512429602994),
(4.58730400003234,11.6992701814196,2.4058809388772),(4.95474580664712,11.6992701814509,
2.42213785946051),(4.19495413545555,11.6992701813583,2.41598718201081),
(4.19495405464108,12.5288259546746,2.4942538531322),(3.30076429030096,12.8583537697599,
2.89447139722872),(3.30065206550949,13.6771575526083,2.51055562715467),
(3.30073486990841,13.2327333223172,2.77993677791185),(4.19495043129256,
13.6772155105173,2.24790699184106),(3.30044490013042,14.2381441622059,2.13553799115554),
(4.19495412145122,12.2304773769228,2.47853014259128),(3.76988992779578,
12.230477332703,2.62882800203533),(3.76989003540394,11.6992701810711,2.45910794164967),
(3.300775503391,12.2304771563739,2.78652181977866),(3.30077597184394,11.6992701799256,
2.52104210211781),(4.19495413554711,11.2301561202153,2.36464824184457),
(4.19495413554711,10.8050920207757,2.32686067359109),(3.76989003610754,
10.8050920207757,2.22393408549402),(3.30077597490697,10.8050920207757,2.15571221509097),
(3.30077597490697,10.0453003501022,2.0338975271717),(3.30077597490697,11.2301561202153,
2.2967108302355),(3.76989003610755,11.2301561202153,2.31625801185991),(2.76956875989281,
11.699270176232,2.55364011839959),(2.14168667241697,11.6992701670987,2.47706008971196),
(2.14168670185164,11.2301561202152,2.23291636418528),(2.13667771814722,
14.5843209583749,1.87843859114071),(2.13397054815702,14.9962573729492,1.74104878620035),
(1.30858440873506,14.993082252866,1.68322356301754),(3.04447394259517,12.6890229486301,
2.97689646201333),(3.04447984877054,12.5288234863927,2.95299455547085),
(3.30077326855424,12.5288246887936,2.88062318729346),(3.04440637010482,
13.2327150598463,2.84945247513237),(2.76841684143832,14.2377449299584,2.14120403929022),
(3.04444215934266,13.0387126233211,2.93120431404288),(3.0444626682149,12.8583485784258,
2.97186417237722),(2.76942575136586,13.2326837551435,2.88274696607814),
(2.76949082525401,13.0386955622929,2.96765730150351),(2.62352484966811,
13.2326614034743,2.88023666941411),(2.76913764485804,13.6770081343175,2.57602712188558),
(2.47047874109866,13.6768572120769,2.55723636810324),(2.13850519366002,
14.2367577394876,2.07867808451582),(2.31070307814832,13.2326002311307,2.82554873052903),
(2.47097419979957,13.232633689058,2.86187258893387),(2.47115023621386,12.8583254477693,
2.98683862012589),(2.62366010443052,12.8583333259361,3.00689489251454),
(2.31092834766835,12.8583159369382,2.94687334218668),(2.14157441703732,
12.8583048388101,2.88526770850109),(2.76954861524742,12.6890185369439,3.01365496426516),
(2.76952811557196,12.8583396796755,3.0093637165552),(2.76956712997526,12.2304765877882,
2.87289959231811),(2.76955935416835,12.5288214252913,2.98757054985363),
(2.47121732954586,12.2304760134795,2.8508688271129),(2.1416821707545,12.2304751818336,
2.76401069183021),(2.14166069478594,12.5288133555564,2.86652165047505),
(2.62370138027385,12.5288199536576,2.98459168802064),(2.47120395743838,
12.5288181289406,2.96477990834091),(1.32262104400658,12.8582586347318,2.44124315037782),
(0.756349679342826,14.2356829183347,1.89652776085301),(1.31695530071299,
14.235448580682,1.94411569262859),(2.14049597760728,13.6766386643817,2.48809672371908),
(1.32063025994913,13.6761486932624,2.19430971316014),(1.32209914819651,
13.2323986490573,2.36267028121065),(2.14129169776506,13.2325611892889,2.76964661902818),
(0.759723173500355,13.676236397429,2.03244363778357),(0.404530643479107,
14.9957505332811,1.80867143143908),(0.409677663442069,14.2365487604263,
1.90324476319084),(0.411937289847635,13.6765604510251,1.98853561254384),
(3.56237310100823E-5,15.0000105325919,1.93875932375063),(0.413161365527088,
12.8582974633222,2.15449777256329),(0.761550649114403,12.8582669051992,
2.2062696231256),(3.56237310051037E-5,13.6772178361241,2.00103320073166),
(1.32278031152005,12.528802653909,2.44869550815785),(1.32282736561586,11.9555606644331,
2.34824765106328),(1.32282826586249,11.6992701549866,2.27888839679454),
(1.32281995585671,12.2304733173324,2.41025517006299),(0.761733243783124,
12.2304736510756,2.26308223432949),(0.413259294525986,12.5288116472685,
2.22166068878197),(0.41328367068479,12.2304748842061,2.26941926869125),
(3.56237309890404E-5,12.8583594544711,2.20001415123069),(3.56237309808271E-5,
12.5288260054691,2.3030341781359),(3.56237309741178E-5,12.2304773857726,
2.38426107184719),(0.76174004570325,11.9555607025216,2.25990210518771),
(3.5623730968287E-5,11.6992701814159,2.45681587818134),(0.413288780267586,
11.6992701651653,2.30187252931774),(1.32282832019849,11.2301561202152,2.15388047300453),
(2.14168670185174,10.8050920207757,2.08052576733846),(2.14168670185174,
10.0453003501022,1.96655734966022),(1.32282832019869,10.0453003501022,1.99896147936414),
(0.761740872100653,11.6992701571546,2.24323197845462),(1.3228283201987,
10.8050920207757,2.06808509548503),(3.56237309762301E-5,10.8050920207757,
2.35869657813387),(0.761740921979543,10.8050920207757,2.13438295226053),
(0.761740921979545,10.0453003501022,2.07802535735097),(3.56237309835803E-5,
10.0453003501022,2.26880603971376),(2.14168670185174,8.72453382214744,1.94249982085887),
(3.30077597490697,8.72453382214744,1.95117125580154),(1.32282832019869,
8.72453382214744,2.00328105613453),(3.56237309760984E-5,8.72453382214744,
2.36206282478682),(3.56237309854124E-5,7.50001053259181,2.24602824640092),
(3.56237310092759E-5,14.2383052343433,1.94935008860201),(0.748665470249253,
14.9936505950595,1.73330177847375),(4.95485760202037,15.000019728145,1.93687970171871),
(11.8663360411557,13.4334823669781,3.63531004811174),(7.50003562373097,
1.05325917586015E-5,1.93875932375063),(11.699295272555,1.05325917596929E-5,
1.93875420256786),(13.6772429272632,1.05325917596929E-5,1.73159234851917),
(15.000035623731,1.05325917596929E-5,0.596271985674758),(15.000035623731,
3.30075088376783,1.92804784099292),(15.000035623731,6.27548724303617,1.4757441040531)));
#2683=IFCCARTESIANPOINTLIST3D(((15.000035623731,7.50003562373085,0.),(15.000035623731,
7.50003562373095,7.5),(3.56237309519202E-5,7.50003562373095,7.5),(3.56237309519202E-5,
7.50003562373095,0.),(9.75003562373095,7.50003562373089,3.75000000000003),
(6.37503562373096,7.50003562373091,1.80144284148504),(6.37503562373096,
7.50003562373091,5.69855715851501),(3.56237309519202E-5,3.56237309519202E-5,
0.),(3.56237309519202E-5,3.56237309519202E-5,7.5),(15.000035623731,3.56237309519202E-5,
0.),(15.000035623731,3.56237309519202E-5,7.5),(6.37503562373096,3.56237309519202E-5,
1.80144284148504),(8.23653273371523,3.56237309519202E-5,2.88613133401241),
(9.15625931437836,3.56237309519202E-5,3.37097147569719),(9.668351707816,
3.56237309519202E-5,3.62821517759284),(9.75003562373097,3.56237309519202E-5,
3.75000000000003),(9.66835170781603,3.56237309519202E-5,3.87178482240721),
(9.20771014620426,3.56237309519202E-5,4.10723895587378),(8.32411466271491,
3.56237309519202E-5,4.55803250982977),(6.37503562373096,3.56237309519202E-5,
5.69855715851501)));
#2684=IFCCARTESIANPOINTLIST3D(((15.0000356237309,3.56237309460994E-5,0.000100000000029104),
(15.000035623731,15.0000356237309,9.99999999793658E-5),(3.56237309880498E-5,
15.0000356237309,9.99999999793658E-5),(3.56237309169956E-5,3.56237309460994E-5,
0.000100000000029104),(7.50003562373097,11.6667022903976,9.99999999904187E-5),
(14.1095778174092,11.6667022903976,9.99999999904187E-5),(14.1095778174092,
2.61788915540305,0.000100000000020423),(7.50003562373093,2.61788915540305,
0.000100000000020423)));
#2685=IFCCARTESIANPOINTLIST3D(((5.88513959487521,16.0459164626568,19.6374585115253),
(5.19101000646328,16.4828342373095,19.6791791004902),(5.19101000646328,
16.4828342373095,21.0187139662086),(5.88513959487521,16.0459164626568,21.0604345551735),
(5.19101000646328,16.7363428775108,22.3340416918415),(5.88513959487521,
16.3152164496123,22.457695567949),(4.26763206013759,16.8138227556455,19.7107846718968),
(4.26763206013759,16.8138227556455,20.987108394802),(4.26763206013759,17.0553686084121,
22.2403672853198),(6.40620000000003,15.4547784704274,20.7166480062731),
(6.59038285562118,15.0635133270203,20.7331895028059),(6.40620000000003,
15.4547784704274,19.5810116773013),(6.40620000000003,15.4547784704274,21.1168813893974),
(6.40620000000003,15.517882052452,19.2535992034788),(6.59038285562118,15.1333954900616,
19.1994015832367),(5.88513959487521,16.3152164496123,18.2401974987498),
(5.19101000646328,16.7363428775108,18.3638513748573),(5.19101000646328,
17.2341976951464,23.5776229063314),(5.88513959487521,16.8440832203404,23.778740896322),
(6.40620000000003,15.6570532062741,22.1663823769888),(6.40620000000003,
15.745443724617,22.6249959349613),(6.59038285562118,15.2725807505329,22.2542679726271),
(2.83964210456039,17.0491006999221,19.7332509915915),(2.83964210456039,
17.0491006999221,20.9646420751072),(4.26763206013759,17.0553686084121,18.4575257813789),
(2.83964210456039,17.2821429947718,22.1737803175648),(4.26763206013759,
17.5297302174846,23.4252653018127),(6.40620000000003,15.745443724617,18.0728971317375),
(6.40620000000003,15.8445559414326,17.8253267827277),(6.59038285562118,
15.4799157755313,17.7036366984083),(5.88513959487521,16.8440832203404,16.9191521703767),
(5.19101000646328,17.2341976951464,17.1202701603673),(5.19101000646328,
17.9584049199587,24.7045113444676),(5.88513959487521,17.6134021517981,24.9758245201752),
(6.40620000000003,16.1178401459667,23.5551972256369),(6.40620000000003,
16.316268833389,24.0508481249619),(6.59038285562118,15.7536825217421,23.7123248935555),
(1.94134075777695,17.0175397962471,19.730237290694),(1.94134075777695,17.0175397962471,
20.9676557760047),(2.83964210456039,17.2821429947718,18.524112749134),(4.26763206013759,
17.5297302174846,17.272627764886),(1.94134075777695,17.2517227843867,22.1827124972546),
(2.83964210456039,17.7398048287495,23.3169643125027),(4.26763206013759,
18.219762918374,24.498977142743),(6.76328380427451,14.408602189602,19.0997810370829),
(6.76328380427451,14.3326639786889,20.7664883263702),(6.40620000000003,
16.316268833389,16.6470449417368),(6.40620000000002,16.4243653919804,16.4788434133545),
(6.59038285562118,16.0916124716871,16.2953696602227),(5.88513959487521,
17.6134021517981,15.7220685465235),(5.19101000646328,17.9584049199586,15.9933817222311),
(5.19101000646328,18.8827898159395,25.6739783220165),(5.88513959487521,
18.5953680533616,26.0056807183715),(6.40620000000003,16.8218316389305,24.8375192058312),
(6.40620000000003,17.146622690042,25.3429039462312),(6.59038285562118,16.4909054313743,
25.0591327048045),(6.76328380427451,14.5598493634968,22.4193847541508),
(1.20017092776277,16.7767933239597,19.7072487898873),(1.20017092776277,
16.7767933239597,20.9906442768113),(1.94134075777695,17.2517227843867,18.5151805694441),
(2.83964210456039,17.7398048287495,17.380928754196),(4.26763206013759,18.219762918374,
16.1989159239557),(1.20017092776277,17.0196775168666,22.2508471353594),
(1.94134075777695,17.7116247773168,23.3314921387383),(2.83964210456039,
18.405545110749,24.3528764118071),(4.26763206013759,19.1005271314077,25.4226960646505),
(6.72220727192618,13.7869222276143,19.0143330930873),(6.72220727192618,
13.7057895376569,20.795049864335),(6.76328380427451,14.7851522186571,17.4743918911679),
(6.40620000000003,17.146622690042,15.3549891204675),(6.40620000000003,17.2385752659328,
15.2585520343421),(6.59038285562118,16.9482527335877,15.0211811495939),
(5.88513959487519,18.5953680533615,14.6922123483272),(5.19101000646327,
18.8827898159394,15.0239147446822),(5.19101000646327,19.9739427044867,26.450984776488),
(5.88513959487521,19.7544901188611,26.8310878046439),(6.40620000000003,
17.7457832079777,25.9712854125613),(6.40620000000003,18.2064941427306,26.4544651349698),
(6.59038285562118,17.4598646545456,26.2501435875252),(6.76328380427451,
15.0826438246688,24.0037981318423),(6.72220727192618,13.9485153165084,22.5610110556025),
(0.727248197524924,16.3967938539932,19.6709632400354),(0.727248197524924,
16.3967938539932,21.0269298266632),(1.20017092776277,17.0196775168666,18.4470459313392),
(1.94134075777695,17.7116247773168,17.3664009279604),(2.83964210456039,
18.405545110749,16.3450166548916),(4.2676320601376,19.1005271314076,15.2751970020482),
(0.727248197524924,16.6534122178237,22.3583923306024),(1.20017092776277,
17.4966674351201,23.4423103507972),(1.94134075777695,18.3806237187498,24.3724748110965),
(2.83964210456039,19.2553022524094,25.2440760531665),(4.26763206013759,
20.1401897431176,26.1630364589713),(6.47699951763728,13.1851936349117,18.9316274016808),
(6.47699951763728,13.099033170607,20.8226947863138),(6.72220727192618,14.1892297931131,
17.2777607923971),(6.76328380427451,15.4498590723935,15.9440832299378),
(6.40620000000002,18.2064941427306,14.2434279317289),(6.40620000000003,
18.260745423599,14.204795769508),(6.59038285562131,18.0215018167454,13.9232169860489),
(5.88513959487457,19.7544901188615,13.8668052620555),(5.19101000647147,
19.9739427044847,14.2469082902073),(5.19101000646328,21.1924264772371,27.0074476701469),
(5.88513959487521,21.0488746567865,27.4222134163174),(6.40620000000002,
18.8592739030118,26.9193074979518),(6.40620000000002,19.4575766756866,27.3453569736723),
(6.59038285562118,18.6285103177809,27.2459629550458),(6.76328380427451,
15.8837551006389,25.4673214570932),(6.72220727192618,14.5070710214652,24.2538046807212),
(6.47699951763728,13.3568006107789,22.6980921910308),(0.377952203648291,
15.7875076345811,19.6127834602307),(0.377952203648292,15.7875076345811,
21.085109606468),(0.727248197524925,16.6534122178237,18.3395007360962),
(1.20017092776277,17.4966674351201,17.2555827159015),(1.94134075777695,
18.3806237187498,16.3254182556022),(2.8396421045604,19.2553022524094,15.4538170135322),
(4.26763206014726,20.1401897431163,14.5348566077251),(0.377952203648291,
16.0661471898608,22.5308289011885),(0.727248197524924,17.1573740892031,
23.6172282278689),(1.20017092776277,18.1905234202895,24.5219713381916),
(1.94134075777695,19.2345402438827,25.2680366882962),(2.83964210456039,
20.2583638166105,25.9583529595442),(4.26763206013759,21.3011746379022,26.6932404949166),
(5.97769398515925,12.6575278291807,18.8591014060345),(5.97769398515925,
12.5669584259719,20.8469370779503),(6.47699951763728,13.6124321087611,17.0874401104072),
(6.72220727192618,14.8994052690468,15.6427728639509),(6.76328380427451,
16.380736510265,14.5594724558565),(6.40620000000002,19.4575766756866,13.3525360930264),
(5.88513959487451,21.0488746567867,13.275679650382),(5.1910100064715,21.1924264772358,
13.6904453965481),(5.19101000646328,22.494201955915,27.3232549841665),(5.88513959487521,
22.4317392374511,27.7576927336233),(6.40620000000002,20.1256977312669,27.650477636103),
(6.40620000000002,20.8546529065052,27.9833803100919),(6.59038285562118,
19.9581875997542,28.0136524917284),(6.76328380427451,16.9366851567938,26.7615463590657),
(6.72220727192618,15.3629815307217,25.8174388876017),(6.47699951763728,
13.9499698857582,24.4957878521627),(5.97769398515925,12.8379161489997,22.8183009174458),
(0.214092166258259,15.0334367038614,19.5407784167739),(0.214092166258259,
15.0334367038614,21.1571146499248),(0.377952203648291,16.0661471898609,
18.1670641655102),(0.727248197524923,17.1573740892031,17.0806648388297),
(1.20017092776276,18.1905234202895,16.1759217285071),(1.94134075777697,
19.2345402438827,15.4298563784025),(2.83964210457202,20.2583638166101,14.7395401071538),
(4.26763206014732,21.3011746379014,14.0046525717797),(0.214092166258259,
15.33933034729,22.7442415816811),(0.37795220364829,16.6133555409043,23.8976892822963),
(0.727248197524924,17.89046497304,24.7579399092706),(1.20017092776277,19.0761677092466,
25.450808336429),(1.94134075777695,20.2425115846393,25.9858098307318),(2.83964210456039,
21.3784765449707,26.4698913025133),(4.26763206013759,22.5415207967473,26.9941452171942),
(5.19662345711427,12.277354369172,18.8068477666932),(5.19662345711427,12.1836084069747,
20.8644032009962),(5.97769398515925,13.1066286309998,16.9205447405888),
(6.47699951763728,14.366616997447,15.3511323518259),(6.72220727192618,15.8939584675886,
14.1634491441207),(6.76328380427417,17.5469942746514,13.3663577811297),
(6.59038285564027,19.2758602940181,13.0377940874091),(6.40620000000003,
20.7384987572773,12.7675585791571),(6.59038285564128,20.6095565233511,12.4158813220189),
(6.40620000000003,20.8546529065052,12.7145127566068),(5.88513959487452,
22.4317392374511,12.9402003330761),(5.19101000647151,22.4942019559144,13.3746380825284),
(5.19101000646327,23.832219582978,27.3869926193901),(5.8851395948752,23.8531035318911,
27.8254006599513),(6.40620000000002,21.5034542880807,28.1407778221849),
(6.40620000000003,22.347228860158,28.3454753184969),(6.59038285562118,21.4049153012252,
28.5278196403524),(6.76328380427451,18.2066066502785,27.8436642825959),
(6.72220727192618,16.4879362375242,27.2001939781456),(6.47699951763728,
14.8589209746423,26.1563201152785),(5.97769398515925,13.4614386389278,24.7079869177888),
(5.19662345711427,12.4640694849997,22.9049090829704),(0.277691356822633,
14.1064332409303,19.4522603186565),(0.277691356822633,14.1064332409303,
21.2456327480422),(0.214092166258259,15.33933034729,17.9536514850176),(0.37795220364829,
16.6133555409043,16.8002037844024),(0.727248197524924,17.8904649730401,
15.9399531574281),(1.20017092776279,19.0761677092467,15.2470847302698),
(1.94134075778561,20.2425115846399,14.712083235968),(2.83964210457207,21.3784765449705,
14.2280017641848),(4.26763206014732,22.5415207967469,13.703747849502),(0.277691356822633,
14.4458312047933,23.0065966013359),(0.214092166258259,15.9400618408756,
24.2447963095104),(0.377952203648289,17.4093551521672,25.1362888542981),
(0.727248197524922,18.8261890547429,25.7392990825463),(1.20017092776277,
20.1215908098648,26.1952507563317),(1.94134075777695,21.368107030521,26.4998520468959),
(2.83964210456039,22.5751566450618,26.7602027535588),(4.26763206013759,
23.8163988777818,27.0548751451413),(4.05160482783559,12.0655797441082,18.7777400144596),
(4.05160482783559,11.9700642880889,20.8741326588581),(5.19662345711427,
12.7422065742047,16.8002997114165),(5.97769398515925,13.8994061053153,15.0953879386792),
(6.47699951763728,15.4228024315445,13.7801352774708),(6.72220727192584,
17.1399929536614,12.888720627024),(6.76328380428025,18.9100565277679,12.4042033786634),
(6.40620000000004,22.0988840654544,12.4126655435019),(6.59038285564128,
22.0309849285392,12.0350107287501),(6.40620000000004,22.347228860158,12.3524177482018),
(5.88513959487452,23.853103531891,12.872492406748),(5.19101000647151,23.8322195829782,
13.3109004473048),(5.19101000646327,25.1581199152132,27.1963569316139),
(5.8851395948752,25.2615957311769,27.6228900544328),(6.40620000000003,22.947300409585,
28.3740602494812),(6.40620000000002,23.8813589528112,28.4185549418541),
(6.59038285562118,22.9208405934307,28.7714575014896),(6.76328380427451,
19.6515148999712,28.6778824495103),(6.72220727192618,17.8447254686934,28.3563331198433),
(6.47699951763728,16.0535888688146,27.6247642243498),(5.97769398515925,
14.4169018938034,26.453490697195),(5.19662345711427,13.1094609052701,24.8608724224274),
(4.05160482783559,12.2558191849969,22.9531539335736),(0.476396611606183,
13.5322139937053,19.3974290286998),(0.476396611606184,13.5322139937053,
21.3004640379989),(0.277691356822633,14.4458312047933,17.6912964653628),
(0.214092166258259,15.9400618408756,16.4530967571883),(0.37795220364829,
17.4093551521673,15.5616042124006),(0.727248197524939,18.826189054743,14.9585939841524),
(1.20017092776953,20.1215908098666,14.50264231037),(1.94134075778566,21.3681070305215,
14.1980410198042),(2.83964210457208,22.5751566450617,13.9376903131393),
(4.26763206014733,23.8163988777819,13.6430179215549),(0.476396611606183,
13.8923657372766,23.1691087255253),(0.277691356822633,15.1123604058123,
24.6715060567328),(0.214092166258258,16.8139191832494,25.6045448758916),
(0.377952203648289,18.4253765237419,26.201861401522),(0.727248197524923,
19.9307268279937,26.5258368702674),(1.20017092776276,21.2890084074645,26.7283925092188),
(1.94134075777695,22.5706446294033,26.7915845117724),(2.83964210456039,
23.8051529801468,26.8187947004407),(4.26763206013759,25.0797314654947,26.8732353409317),
(2.83366864757743,12.1087998410188,18.7836804800465),(2.83366864757743,
12.0136455127524,20.872147019117),(4.05160482783559,12.5392062268344,16.7333175344698),
(5.19662345711427,13.5627892931468,14.9111288160151),(5.97769398515925,
15.0096378713104,13.4440009758162),(6.47699951763729,16.7460533913754,12.4264121240385),
(6.72220727193099,18.5962941470631,11.8607509937792),(6.76328380427858,
20.3593320102269,11.728395122416),(6.40620000000004,23.4982613345086,12.297587313612),
(6.59038285564129,23.4969561267681,11.9067548678496),(6.40620000000004,
23.8813589528112,12.2793381248446),(5.88513959487452,25.2615957311768,13.0750030122665),
(5.19101000647151,25.1581199152141,13.501536135081),(5.19101000646327,26.423981460014,
26.7582379912907),(5.8851395948752,26.6063092574358,27.1574801779873),(6.40620000000003,
24.4098195667463,28.342573776339),(6.40620000000003,25.4015957257795,28.1999778921683),
(6.59038285562118,24.4558218268037,28.7365073638585),(6.76328380427451,
21.2236172583812,29.2366077619149),(6.72220727192618,19.3884712539789,29.2476151645383),
(6.47699951763728,17.4944580201101,28.8525490457565),(5.97769398515925,
15.6727024408521,27.9970769310578),(5.19662345711427,14.0984353160429,26.6675966087643),
(4.05160482783559,12.9133926353429,24.9460368903042),(2.83366864757743,
12.29832001882,22.9433078667095),(0.891136454446381,12.9269953833081,21.3582554082364),
(0.891136454446381,12.9269953833081,19.3396376584624),(1.16078600147965,
12.6473117884423,20.843276032831),(0.476396611606183,13.8923657372766,17.5287843411734),
(0.277691356822633,15.1123604058123,16.0263870099659),(0.214092166258258,
16.8139191832494,15.0933481908071),(0.377952203648223,18.4253765237419,
14.4960316651767),(0.727248197529747,19.9307268279964,14.1720561964359),
(1.20017092776954,21.2890084074658,13.9695005574835),(1.94134075778567,
22.5706446294035,13.9063085549277),(2.83964210457208,23.8051529801469,13.8790983662573),
(4.26763206014733,25.0797314654953,13.8246577257646),(0.89113645444638,
13.3090213043409,23.3403941055434),(0.476396611606183,14.5996524020403,
24.9358254035655),(0.277691356822633,16.0819307420378,26.1801869486356),
(0.214092166258258,17.9293188932449,26.7743424248454),(0.37795220364829,
19.6246979960417,27.0558943557893),(0.727248197524923,21.164157419822,27.0891257474815),
(1.20017092776277,22.5362269883914,27.0309644630536),(1.94134075777695,
23.8066615392814,26.8504632539792),(2.83964210456039,25.0240102779517,26.643549477539),
(4.26763206013759,26.2858584285851,26.4557907403474),(1.16078600147965,
12.7168065529285,19.3179908154979),(1.16078600147965,12.7372153583834,18.8700542048197),
(0.891136454446381,13.3090213043409,17.3574989611554),(1.91027636686428,
12.3379791648582,18.8151804530644),(1.91027636686428,12.2447397562737,20.8616179457049),
(2.83366864757742,12.5806356222758,16.7469876150002),(4.05160482783559,
13.3752777840964,14.8084877568104),(5.19662345711427,14.7119604423181,13.2018224243548),
(5.97769398515931,16.4006012405819,12.0210061126197),(6.47699951764625,
18.2926012385726,11.3347394463752),(6.72220727192999,20.1447059013895,11.1387147347598),
(6.76328380427859,21.9039426137529,11.3145179585806),(6.40620000000005,
24.8951906728058,12.4251051199434),(6.59038285564129,24.9629273249971,12.0350107287501),
(6.40620000000005,25.4015957257795,12.4979151745304),(5.88513959487452,
26.6063092574356,13.540412888712),(5.19101000647152,26.4239814600156,13.9396550754045),
(5.19101000646327,27.5840526829811,26.0884705584315),(5.8851395948752,27.8386426602118,
26.4459921561632),(6.40620000000002,25.8429619766749,28.0472195475207),
(6.40620000000003,26.8529938611494,27.6976441135121),(6.59038285562118,
25.959087046096,28.4241252590672),(6.7632838042745,22.8709139297773,29.501359487967),
(6.72220727192618,21.0681117365833,29.8445595354631),(6.47699951763728,
19.1338693806673,29.7990636360867),(5.97769398515925,17.1873026718028,29.2876890399979),
(5.19662345711427,15.3982808095542,28.2653213546629),(4.05160482783559,
13.9210343472877,26.7868637431548),(2.83366864757742,12.9534072953844,24.9286560707),
(1.91027636686428,12.5236853709231,22.8910980127827),(1.16078600147965,
12.7205989966035,21.376480070908),(1.16078600147965,12.9162775289712,22.8001471233349),
(0.476396611606184,14.5996524020403,15.7620676631332),(0.277691356822633,
16.0819307420378,14.5177061180631),(0.214092166258179,17.9293188932449,
13.9235506418533),(0.377952203651072,19.624697996045,13.6419987109152),
(0.727248197529682,21.1641574198239,13.6087673192225),(1.20017092776954,
22.536226988392,13.6669286036489),(1.94134075778567,23.8066615392813,13.8474298127209),
(2.83964210457209,25.0240102779519,14.054343589159),(4.26763206014733,26.2858584285862,
14.2421023263491),(0.891136454446381,14.0592657342614,25.2144140934506),
(0.476396611606182,15.6285108051144,26.5367603282398),(0.277691356822632,
17.3194994153276,27.4781116187813),(0.214092166258258,19.2459475191972,
27.7119094250749),(0.377952203648288,20.9639729662561,27.6675207409714),
(0.72724819752492,22.4819014349671,27.4088069865313),(1.20017092776276,
23.8181688235347,27.092030879081),(1.94134075777694,25.0314848886217,26.6743602423659),
(2.83964210456039,26.187675860032,26.2408009038063),(4.26763206013759,27.3911871960738,
25.8176288788947),(1.16078600147965,13.1020768789072,17.2951188457401),
(1.16078600147965,13.1830145346668,16.9457491019701),(0.891136454446381,
14.0592657342614,15.4834789732481),(1.91027636686428,12.8003195543592,16.8194747225435),
(2.83366864757743,13.4135461346628,14.8294352925433),(4.05160482783559,
14.5461400141552,13.0669174998158),(5.19662345711434,16.1517093510408,11.728918574645),
(5.9776939851764,18.0262878787011,10.8734711763434),(6.47699951764663,19.9369677172116,
10.5679587645725),(6.72220727193,21.7949740824934,10.6965267083382),(6.76328380427859,
23.4969561267681,11.1751473353482),(6.40620000000005,26.2484224436654,12.7910047511918),
(6.59038285564129,26.3843557301851,12.4158813220189),(6.40620000000005,
26.8529938611494,13.0002489531866),(5.88513959487453,27.8386426602115,14.251900910536),
(5.19101000647151,27.5840526829833,14.609422508264),(5.19101000646327,28.5964055875805,
25.2112617731218),(5.8851395948752,28.9140561995153,25.5141410198332),(6.40620000000003,
27.1996061383775,27.49752742195),(6.40620000000003,28.1830960487277,26.929709257464),
(6.59038285562118,27.380913353867,27.8446437240223),(6.7632838042745,24.5389179464607,
29.4633805415897),(6.72220727192617,22.8280901256078,30.1274213443889),
(6.47699951763728,20.9175968020343,30.4330005139347),(5.97769398515924,
18.9106047656053,30.2826379677499),(5.19662345711427,16.9660029206006,29.6011993630134),
(4.05160482783559,15.2454149622745,28.4147462046593),(2.83366864757742,
13.9572392847741,26.7625230691715),(1.91027636686428,13.1655894372002,24.8364923605423),
(1.16078600147965,13.1130774501814,23.3965811926536),(1.16078600147965,
13.5352161086358,24.6759408404133),(0.476396611606182,15.6285108051144,
14.1611327384589),(0.277691356822761,17.3194994153276,13.2197814479174),
(0.21409216625777,19.2459475191933,12.985983641617),(0.377952203650806,
20.9639729662584,13.0303723257338),(0.727248197529689,22.4819014349679,
13.289086080173),(1.20017092776955,23.8181688235345,13.6058621876215),(1.94134075778568,
25.0314848886214,14.0235328243342),(2.83964210457209,26.1876758600323,14.4570921628918),
(4.26763206014733,27.3911871960752,14.8802641878019),(0.891136454446381,
15.1506128846296,26.9125834068837),(0.476396611606181,16.9417553231474,
27.9140515401752),(0.277691356822631,18.7803374693753,28.518369687881),
(0.214092166258256,20.716218672122,28.3833597623847),(0.377952203648285,
22.3947965475146,28.014634785931),(0.727248197524919,23.836332171373,27.473326474516),
(1.20017092776276,25.0885011949053,26.9093846579187),(1.94134075777694,
26.2008463703329,26.269640298604),(2.83964210456039,27.2540918203503,25.6251053620484),
(4.26763206013758,28.3557683063977,24.9818145889612),(1.16078600147965,
13.862838974477,15.3806362668768),(1.16078600147965,13.9699637977149,15.1340101881593),
(0.89113645444638,15.1506128846296,13.785309659815),(1.91027636686428,13.6164682781601,
14.9405119077874),(2.83366864757742,14.5799815330473,13.094449617559),(4.05160482783563,
16.0130647791979,11.5662119805577),(5.19662345714055,17.8344140252697,10.5411359135561),
(5.97769398517686,19.7547986501859,10.0674533663964),(6.47699951764663,
21.6895026422339,10.0983684467054),(6.72220727193,23.4969561267681,10.5476225739395),
(6.7632838042786,25.0899696397834,11.3145179585806),(6.40620000000005,27.5180449549385,
13.3842163812106),(6.59038285564129,27.718051959518,13.0377940874093),(6.40620000000005,
28.1830960487277,13.7681838092347),(5.88513959487452,28.9140561995148,15.1837520468659),
(5.19101000647151,28.5964055875833,15.4866312935742),(5.19101000646327,
29.4244511021801,24.158316246408),(5.8851395948752,29.793681628073,24.3956062969787),
(6.40620000000003,28.4350917775616,26.7113537154497),(6.40620000000003,
29.343828929908,25.9239284905564),(6.59038285562118,28.674271574711,27.0172300357724),
(6.7632838042745,26.1724574122235,29.1239271370768),(6.72220727192618,24.6101923256208,
30.086844485331),(6.47699951763727,22.7866406516137,30.7333912048379),(5.97769398515924,
20.7856077538546,30.9490141898609),(5.19662345711427,18.7497467371263,30.6310443356499),
(4.05160482783558,16.842728476781,29.7758394631106),(2.83366864757742,15.2766126410887,
28.3842507829545),(1.91027636686428,14.149219953164,26.6334539646571),(1.16078600147965,
13.8799374321238,25.3057006662568),(1.16078600147965,14.4836551456343,26.4086123117331),
(0.476396611606317,16.9417553231474,12.7838415265236),(0.277691356824015,
18.7803374693714,12.1795233788108),(0.214092166257616,20.7162186721195,
12.3145333043067),(0.377952203650813,22.3947965475157,12.6832582807745),
(0.727248197529695,23.8363321713728,13.2245665921883),(1.20017092776955,
25.0885011949044,13.7885084087836),(1.94134075778568,26.2008463703323,14.428252768096),
(2.83964210457209,27.2540918203507,15.0727877046497),(4.26763206014733,
28.3557683063995,15.7160784777357),(0.891136454446378,16.5436186259539,
28.3735257823183),(0.476396611606181,18.4919218776323,29.017920146475),
(0.27769135682263,20.4116464107313,29.2633635189515),(0.214092166258254,
22.2869929217985,28.7644254721376),(0.377952203648285,23.8654550476162,
28.0846908846864),(0.727248197524919,25.1784969732913,27.2803523090361),
(1.20017092776276,26.3013109807831,26.4896271098797),(1.94134075777694,
27.2724822137244,25.6509310559487),(2.83964210456039,28.1847151075809,24.8187156933236),
(4.26763206013758,29.1447392850381,23.9785563766779),(1.16078600147965,
14.9720515022293,13.6434794525366),(1.16078600147965,15.0720335441048,13.4947636208241),
(0.891136454446331,16.5436186259539,12.3243672843804),(1.91027636686428,
14.7594299140499,13.2404417040951),(2.83366864757743,16.0413601041423,11.5994180115556),
(4.05160482786512,17.7275312003354,10.3560094303133),(5.19662345714091,
19.6235492044837,9.70684847801363),(5.97769398517687,21.5970129829737,9.57383352364085),
(6.47699951764664,23.4969561267681,9.94023675695826),(6.72220727193001,
25.1989381710429,10.6965267083382),(6.7632838042786,26.6345802433094,11.728395122416),
(6.40620000000005,28.6665925519918,14.1871359141803),(6.59038285564129,
28.9234923279503,13.8818525202479),(6.40620000000005,29.343828929908,14.7739645761423),
(5.88513959487452,29.7936816280725,16.3022867697203),(5.19101000647151,
29.4244511021834,16.5395768202886),(5.19101000646327,30.0382615044309,22.9676901730482),
(5.8851395948752,30.4457269910553,23.1308147467974),(6.40620000000003,29.5086750329665,
25.7143096184639),(6.40620000000003,30.2932405941043,24.7166533502415),
(6.59038285562118,29.7963818203263,25.9692522232149),(6.7632838042745,27.7175004009099,
28.4942272377987),(6.72220727192617,26.3554724608164,29.7241711026929),
(6.47699951763727,24.6791793197177,30.6902998070511),(5.97769398515924,
22.7502929188361,31.26477624876),(5.19662345711427,20.690512084238,31.3207925057153),
(4.05160482783558,18.6601411958325,30.8251231856837),(2.83366864757742,
16.867886983882,29.7401979784502),(1.91027636686428,15.442041768904,28.222545450804),
(1.16078600147965,14.9934633464483,27.0352507715875),(1.16078600147965,
15.7302235044146,27.9408506617675),(0.476396611595366,18.4919218776433,
11.6799729202428),(0.277691356824434,20.4116464107288,11.4345295477397),
(0.214092166257623,22.2869929217975,11.9334675945533),(0.377952203650818,
23.8654550476159,12.6132021820192),(0.727248197529699,25.17849697329,13.4175407576681),
(1.20017092776955,26.3013109807816,14.2082659568224),(1.94134075778568,
27.2724822137236,15.0469620107512),(2.83964210457209,28.1847151075815,15.8791773733746),
(4.26763206014733,29.1447392850402,16.7193366900194),(0.891136454446378,
18.1879361013018,29.5444389558577),(0.476396611606179,20.2229834103705,
29.8084694596061),(0.277691356822629,22.1544664839209,29.6861670938755),
(0.214092166258254,23.901498394231,28.8413338467263),(0.377952203648286,
25.3227950366037,27.8751570272991),(0.727248197524922,26.4598865078043,
26.8368590792212),(1.20017092776276,27.4127640755616,25.8479293664177),
(1.94134075777694,28.207660707296,24.8405942784607),(2.83964210456039,28.9459105714081,
23.8507769212874),(4.26763206013758,29.7295846651831,22.8441146078005),
(1.16078600147965,16.3901608886645,12.1462820707099),(1.16078600147965,
16.4527710572642,12.0822300976174),(0.891136454430803,18.1879361013104,
11.1534541108558),(1.91027636686425,16.1913991809175,11.7754966480112),
(2.83366864760572,17.7493444182767,10.3937910320643),(4.0516048278653,19.5504370462775,
9.50597447425449),(5.19662345714091,21.5303758929931,9.19591580672142),
(5.97769398517688,23.4969561267681,9.40761003721503),(6.47699951764664,
25.3044096113024,10.0983684467054),(6.72220727193001,26.8492063521468,11.1387147347598),
(6.7632838042786,28.0838557257687,12.404203378663),(6.40620000000005,29.660086813318,
15.1761191893287),(6.59038285564129,29.9640501398696,14.9224103321671),
(6.40620000000004,30.2932405941043,15.9812397164572),(5.88513959487451,
30.4457269910548,17.5670783199015),(5.19101000647151,30.0382615044345,17.730202893649),
(5.19101000646327,30.415652087128,21.6824158814797),(5.8851395948752,30.8466256701564,
21.7654792312952),(6.40620000000002,30.384859111343,24.5389382202486),(6.40620000000003,
30.9970168301046,23.351517905615),(6.59038285562118,30.7101285026294,24.7353738258402),
(6.7632838042745,29.1229421486489,27.5951091733864),(6.72220727192617,28.0062026039773,
29.0513971976979),(6.47699951763727,26.5326140680207,30.3051516373616),
(5.97769398515924,24.7396751616414,31.2194798101618),(5.19662345711427,
22.7241050491969,31.6476293511416),(4.05160482783558,20.6375392918243,31.5278906378288),
(2.83366864757742,18.6784283734265,30.7855145370827),(1.91027636686428,
17.001292738574,29.5512050807248),(1.16078600147965,16.4135231317007,28.523099861912),
(1.16078600147965,17.2336889460737,29.2219746605142),(0.476396611595542,
20.2229834103779,10.8894236071137),(0.277691356824443,22.1544664839198,
11.0117259728154),(0.214092166257629,23.9014983942314,11.8565592199646),
(0.377952203650822,25.3227950366021,12.8227360394063),(0.7272481975297,
26.459886507802,13.8610339874825),(1.20017092776955,27.4127640755595,14.849963700284),
(1.94134075778568,28.207660707295,15.857298788239),(2.83964210457208,28.9459105714087,
16.8471161454109),(4.26763206014732,29.7295846651855,17.8537784588972),
(0.891136454446375,20.0241353928868,30.3830030746259),(0.476396611606177,
22.0723748473321,30.2571269676356),(0.277691356822628,23.9458076306257,
29.7714991898511),(0.214092166258254,25.5013826550928,28.6113052171102),
(0.377952203648284,26.7141444502129,27.3936063133103),(0.727248197524919,
27.6341880185069,26.1588757859073),(1.20017092776276,28.3826896685326,25.0074840559086),
(1.94134075777694,28.9725820637063,23.8679176485903),(2.83964210456038,
29.5101666244937,22.756272875101),(4.26763206013758,30.0891666109946,21.6194909636154),
(1.16078600147963,18.0665062392767,10.9431314202574),(1.91027636685903,
17.8650114087907,10.5941321363932),(2.83366864760574,19.5653581661308,9.54696991412444),
(4.05160482786531,21.4932558774456,8.98539773740302),(5.19662345714092,
23.4969561267682,9.02386233035194),(5.97769398517688,25.3968992705625,9.57383352364085),
(6.47699951764665,27.0569445363247,10.5679587645725),(6.72220727193001,
28.3976181064731,11.8607509937793),(6.7632838042786,29.3937605849956,13.3214086354312),
(6.40620000000004,30.4689851421845,16.3221365804202),(6.59038285564129,
30.8081085727082,16.1278507005995),(6.40620000000004,30.9970168301046,17.3463751610837),
(5.88513959487453,30.8466256701558,18.9324138354036),(5.1910100064715,30.4156520871318,
19.0154771852182),(5.19101000646327,30.5429829713312,20.3489465333493),
(5.88513959487517,30.9818881425823,20.3489465333493),(6.40620000000003,
31.0345575086107,23.2236660958875),(6.40620000000002,31.4297213310687,21.8778617084715),
(6.59038285562117,31.3852879910939,23.3564073418152),(6.7632838042745,30.3422954255193,
26.4563127094714),(6.72220727192617,29.507782219844,28.0907758414936),(6.47699951763727,
28.2856395827829,29.5906860864605),(5.97769398515924,26.6879524880783,30.814623126099),
(5.19662345711427,24.7832612975828,31.6007442213951),(4.05160482783558,
22.7095171653558,31.8608966639348),(2.83366864757742,20.6483502623127,31.4856249445842),
(1.91027636686428,18.7753981466455,30.5754853156456),(1.16078600147965,
18.0890054482852,29.7157918733084),(1.16078600147965,18.9443219504966,30.2096090861025),
(0.891136454430432,20.0241353928923,10.3148899920888),(0.476396611595551,
22.0723748473353,10.4407660990852),(0.277691356824449,23.9458076306261,
10.9263938768397),(0.214092166257633,25.5013826550947,12.0865878495809),
(0.377952203650822,26.7141444502101,13.3042867533946),(0.727248197529701,
27.6341880185037,14.5390172807959),(1.20017092776955,28.3826896685299,15.6904090107927),
(1.94134075778567,28.9725820637051,16.8299754181092),(2.83964210457208,
29.5101666244943,17.9416201915974),(4.26763206014731,30.0891666109971,19.0784021030827),
(0.891136454446374,21.9858514741404,30.8589102465131),(0.476396611606177,
23.9732543634428,30.3476770192577),(0.277691356822627,25.7209261145098,
29.5162756825993),(0.214092166258251,27.0288217187386,28.0826534172974),
(0.377952203648282,27.9892162955104,26.6574432401917),(0.727248197524918,
28.6589591891894,25.270906511783),(1.20017092776276,29.1760321216496,23.9986670616331),
(1.94134075777694,29.5396000321514,22.7680562340253),(2.83964210456039,
29.8570895829243,21.5747617826961),(4.2676320601376,30.2104888940692,20.3489465333493),
(1.16078600147966,19.7823096290828,10.1430391597026),(1.91027636685895,
19.6444790474783,9.76435274903383),(2.83366864760574,21.5008315285347,9.02836138969636),
(4.05160482786531,23.4969561267682,8.81009668048323),(5.19662345714092,
25.4635363605432,9.19591580672142),(5.97769398517688,27.2391136033504,10.0674533663964),
(6.47699951764665,28.7013110149637,11.3347394463752),(6.72220727193001,
29.7971257264006,12.8406967790345),(6.7632838042786,30.5244940246863,14.4521420751219),
(6.40620000000004,31.0690131062044,17.5915719598445),(6.59038285564128,
31.4300213380986,17.4615469299323),(6.40620000000002,31.4297213310687,18.8200313582272),
(6.40620000000002,31.4360521245209,21.8115626399412),(6.40620000000003,
31.5757150289313,20.3489465333493),(6.59038285562118,31.7995283075899,21.8779642894398),
(6.7632838042745,31.3352281749076,25.1155053574509),(6.72220727192617,30.8105441650008,
26.8740811192919),(6.47699951763727,29.880271745991,28.5705352432542),(5.97769398515924,
28.5306825146973,30.0635974778449),(5.19662345711427,26.7998709485588,31.1816879170053),
(4.05160482783558,24.8075408398654,31.8131265579666),(2.83366864757742,
22.7124943386273,31.8173719313375),(1.91027636686428,20.7056766239747,31.261506451295),
(1.16078600147965,19.9596481873376,30.570456117364),(1.16078600147965,20.8055405996388,
30.8710863529179),(0.89113645443044,21.9858514741428,9.83898282020237),
(0.476396611595558,23.9732543634418,10.3502160474633),(0.277691356824454,
25.7209261145117,11.1816173840917),(0.214092166257635,27.0288217187419,
12.6152396493942),(0.377952203650823,27.9892162955065,14.0404498265126),
(0.727248197529699,28.6589591891854,15.4269865549196),(1.20017092776955,
29.1760321216465,16.6992260050676),(1.94134075778567,29.5396000321501,17.9298368326739),
(2.83964210457206,29.857089582925,19.1231312840024),(0.891136454446373,
24.0021828146848,30.9549599463631),(0.476396611606177,25.8569192202972,
30.0768468996809),(0.277691356822626,27.4156645285397,28.9297210146505),
(0.214092166258251,28.4286099577045,27.2744853007219),(0.377952203648281,
29.1019261538398,25.6932746594599),(0.727248197524918,29.4971621197283,
24.2050447799964),(1.20017092776276,29.7641179716235,22.8579396582113),
(1.94134075777694,29.8882211059008,21.580761891949),(2.8396421045604,29.9741407470833,
20.3489465333493),(1.16078600147966,19.9482383650965,10.0985786888866),
(1.16078600147966,21.6109806730192,9.65304823025763),(1.91027636685896,
21.5410022527727,9.25618088774829),(2.83366864760575,23.4969561267682,8.8537231163657),
(4.05160482786532,25.5006563760907,8.98539773740302),(5.19662345714092,
27.3703630490526,9.70684847801363),(5.97769398517688,28.9676243748353,10.8734711763432),
(6.47699951764665,30.1875458038555,12.3754122491068),(6.72220727193001,
31.005205881083,14.0487769337169),(6.76328380427859,31.4416992814545,15.7620469343487),
(6.40620000000003,31.4418593757894,18.9471467134886),(6.59038285564115,
31.8108919313674,18.8829753351204),(6.7632838042745,32.06889756068,23.6170364614039),
(6.72220727192617,31.8713975079492,25.4415571517656),(6.47699951763727,
31.2637655526456,27.2784422257898),(5.97769398515924,30.2069140035209,28.9912442379026),
(5.19662345711426,28.7072314179326,30.4043213943607),(4.05160482783558,
26.862214832336,31.3861603924551),(2.83366864757742,24.8025857411604,31.7697824360447),
(1.91027636686428,22.7282811277856,31.5865772432191),(1.16078600147965,
21.9581931891823,31.0563394211229),(1.16078600147965,22.7557821148115,31.1845270441583),
(0.891136454430447,24.002182814684,9.74293312035246),(0.476396611595563,
25.8569192202919,10.6210461670395),(0.277691356824455,27.415664528543,11.768172052041),
(0.214092166257635,28.428609957709,13.4234077659704),(0.377952203650822,
29.1019261538349,15.0046184072435),(0.727248197529695,29.4971621197236,
16.4928482867053),(1.20017092776954,29.76411797162,17.8399534084888),(1.94134075778564,
29.8882211058994,19.1171311747499),(0.891136454446374,26.0002539463725,
30.6676806877685),(0.476396611606175,27.6552888621275,29.4544251150677),
(0.277691356822625,28.9687706108344,28.0330347999576),(0.214092166258251,
29.6501553779216,26.2160101687784),(0.377952203648283,30.0120577917839,
24.5359481357394),(0.727248197524917,30.1185019722973,22.99981361061),(1.20017092776276,
30.1256922635933,21.6265307061392),(1.94134075777697,30.0058452110259,20.3489465333493),
(1.16078600147966,21.9534317552991,9.62308764274596),(1.16078600147967,
23.4969561267682,9.48804675836643),(1.91027636685896,23.4969561267682,9.08505709739408),
(2.83366864760575,25.4930807250016,9.02836138969636),(4.05160482786532,
27.4434752072588,9.50597447425449),(5.19662345714093,29.1594982282668,10.5411359135558),
(5.97769398517688,30.5299116598944,11.9673965101908),(6.47699951764664,
31.4704904110107,13.658356856262),(6.72220727193,31.9851516663381,15.4482845536444),
(6.76328380427859,32.1175075377015,17.2113224168081),(6.59038285562168,
31.9391477922182,20.3489465333494),(6.7632838042745,32.519036296428,22.010470272721),
(6.72220727192617,32.6552528304722,23.8405869564981),(6.47699951763727,
32.3903597360221,25.7571450737468),(5.97769398515924,31.661202921563,27.6330332019444),
(5.19662345711426,30.4422537126351,29.2943572924441),(4.05160482783558,
28.805577519702,30.5941207550755),(2.83366864757742,26.8494913594562,31.3444305572502),
(1.91027636686427,24.7763107845561,31.5399454559239),(1.16078600147965,
24.012790474679,31.1559060980349),(1.16078600147965,24.7305391438862,31.13956360848),
(0.891136454430452,26.0002539463685,10.0302123789467),(0.476396611595565,
27.6552888621182,11.2434679516513),(0.277691356824456,28.968770610839,12.6648582667347),
(0.214092166257632,29.6501553779273,14.4818828979149),(0.377952203650819,
30.0120577917782,16.161944930963),(0.72724819752969,30.1185019722922,17.6980794560908),
(1.20017092776951,30.1256922635897,19.0713623605602),(0.89113645444637,
27.907849372581,30.0074554916335),(0.476396611606173,29.3033655244717,28.5029076104181),
(0.277691356822624,30.3241110610014,26.8586256146128),(0.21409216625825,
30.6493081451978,24.9454840705652),(0.377952203648284,30.6867166806225,
23.2272924649823),(0.727248197524917,30.5005219057395,21.6987731982846),
(1.20017092776279,30.2476867605747,20.3489465333493),(1.16078600147967,
24.0108810878075,9.53300936636038),(1.16078600147967,25.3829315805171,9.65304823025762),
(1.91027636685897,25.4529100007636,9.25618088774829),(2.83366864760575,
27.4285540874055,9.54696991412444),(4.05160482786532,29.2663810532012,10.356009430313),
(5.19662345714092,30.7765799311116,11.6734287117887),(5.97769398517688,
31.8785061499267,13.3159910002231),(6.47699951764664,32.5111632137423,15.1445916451538),
(6.72220727193,32.7071879253577,16.9966963079707),(6.76328380427892,32.5313847015368,
18.7559330203341),(6.76328380427367,32.6707553247161,20.3489465333493),
(6.72220727192617,33.1361828661701,22.1241251790192),(6.47699951763727,
33.2227903946359,24.0569631195337),(5.97769398515924,32.845446340749,26.0338893686844),
(5.19662345711426,31.9475491999026,27.8885094462422),(4.05160482783558,
30.5733490780483,29.4632056215094),(2.83366864757742,28.7855065218288,30.5553854875183),
(1.91027636686427,26.7820237432331,31.1231535102327),(1.16078600147965,
26.0495664286501,30.8654665867411),(1.16078600147965,26.6644934449677,30.7376832833126),
(0.891136454430456,27.9078493725739,10.6904375750806),(0.476396611595565,
29.3033655244587,12.1949854562987),(0.277691356824453,30.3241110610071,
13.8392674520804),(0.21409216625763,30.6493081452044,15.7524089961292),
(0.377952203650814,30.6867166806162,17.4706006017189),(0.727248197529669,
30.5005219057341,18.9991198684151),(0.891136454446369,29.6560236244154,
28.9981466167465),(0.476396611606173,30.7415834227307,27.2566847075049),
(0.277691356822624,31.4327003438665,25.4489396664677),(0.21409216625825,
31.3899562752565,23.5088271295954),(0.377952203648281,31.1015188903065,
21.8146058727987),(0.727248197524938,30.629414724317,20.3489465333493),
(1.16078600147967,26.0476096556954,9.83114818372832),(1.16078600147967,
27.2116026244535,10.1430391597026),(1.91027636685897,27.3494332060581,9.76435274903383),
(2.83366864760576,29.24456783526,10.3937910320637),(4.05160482786532,30.9139858422238,
11.509674723577),(5.19662345714092,32.1724739483288,13.0693227290059),(5.97769398517687,
32.9724314837743,14.8782782852822),(6.47699951764663,33.277943895545,16.7889581237928),
(6.72220727193033,33.1493759517794,18.6469644890746),(6.72220727192568,
33.2982800861954,20.3489465333494),(6.47699951763727,33.7335235552841,22.2341325960219),
(5.97769398515924,33.7204735190692,24.2467069738126),(5.19662345711426,
33.1733278279394,26.2332785180311),(4.05160482783558,32.1070576404243,28.0308218155686),
(2.83366864757742,30.5465944354844,29.4287461524243),(1.91027636686427,
28.6790778451213,30.3499874652545),(1.16078600147965,27.9952645450842,30.1953123371048),
(1.16078600147965,28.4936763915399,29.9921789021064),(0.891136454430456,
29.6560236244056,11.699746449966),(0.476396611595563,30.7415834227146,13.4412083592091),
(0.277691356824449,31.4327003438731,15.2489534002267),(0.214092166257623,
31.3899562752638,17.1890659371004),(0.377952203650874,31.1015188902998,
18.8832871939012),(0.891136454446369,31.1815931295911,27.6762331155745),
(0.476396611606171,31.9179616148214,25.7607981490415),(0.277691356822623,
32.2544711543458,23.8549266785892),(0.21409216625825,31.8453308149375,21.9579638708886),
(0.377952203648222,31.241472388221,20.3489465333493),(1.16078600147968,
27.9914149979759,10.5066716414001),(1.16078600147967,28.9274060142596,10.9431314202574),
(1.91027636685897,29.1289008447458,10.5941321363929),(2.83366864760575,
30.8859433097838,11.5430945123579),(4.05160482786532,32.3362279365405,12.9319168178937),
(5.19662345714092,33.3047667465617,14.6864044318506),(5.97769398517687,
33.7784492937211,16.6067890567671),(6.47699951764662,33.7475342134121,18.5414930488151),
(6.47699951763746,33.9056659031742,20.3489465333494),(5.97769398515924,
34.2573415354164,22.3305999286917),(5.19662345711426,34.0790450113826,24.3834139137225),
(4.05160482783558,33.3559733472071,26.3443477187684),(2.83366864757742,
32.0745043056925,28.0017779469181),(1.91027636686427,30.4047249967127,29.2460210224662),
(1.16078600147965,29.7798647555242,29.1693520619647),(1.16078600147965,
30.157584836929,28.9277092126408),(0.891136454430452,31.1815931295788,13.0216599511359),
(0.476396611595558,31.9179616148026,14.9370949176692),(0.277691356824443,
32.2544711543531,16.8429663881065),(0.214092166257696,31.8453308149452,
18.7399291958086),(0.89113645444637,32.4294198313583,26.0894923862619),
(0.476396611606173,32.789982727858,24.0693131727451),(0.277691356822624,
32.7597225542455,22.1341984335196),(0.214092166258178,31.99897334191,20.3489465333493),
(1.16078600147967,29.7733867786221,11.5354935286445),(1.16078600147967,
30.4782079321745,12.0290146134515),(1.91027636685897,30.7372446930793,11.7203066230293),
(2.83366864760575,32.3028081477596,12.9599593503337),(4.05160482786531,
33.4898932298045,14.5795216069163),(5.19662345714091,34.1390541821039,16.4755396110649),
(5.9776939851768,34.2720691364767,18.4490033895549),(5.97769398515948,34.4382926229155,
20.3489465333494),(5.19662345711426,34.6347427082019,22.4001028588428),
(4.05160482783558,34.2787863172105,24.4595661547084),(2.83366864757742,
33.3186980738326,26.3216801228834),(1.91027636686427,31.9018866630715,27.8477696347023),
(1.16078600147965,31.3390881069486,27.824262187464),(1.16078600147965,31.6011823524872,
27.5794832495684),(0.891136454430446,32.4294198313441,14.6084006804459),
(0.476396611595551,32.7899827278373,16.6285798939619),(0.277691356824304,
32.7597225542533,18.5636946331776),(0.89113645444637,33.3544040215345,24.2952733729787),
(0.476396611606173,33.3261296467866,22.2433644527802),(0.27769135682276,
32.9301934415537,20.3489465333493),(1.16078600147967,31.3303099249584,12.8811166062354),
(1.16078600147967,31.816888046666,13.367694727943),(1.91027636685897,32.1255960370883,
13.1086579670382),(2.83366864760575,33.4521116280537,14.6013348248575),
(4.05160482786531,34.339928185863,16.4024274528587),(5.19662345714084,34.6499868533961,
18.3823662995743),(5.19662345711444,34.8220403297744,20.3489465333493),
(4.05160482783558,34.8449730379661,22.438819283112),(2.83366864757742,34.2380220443788,
24.4440245985169),(1.91027636686427,33.1210418315162,26.2014826992464),
(1.16078600147965,32.616697607562,26.2081815970326),(1.16078600147965,32.7767196453481,
25.9920957393739),(0.89113645443044,33.3544040215188,16.4026196937263),
(0.476396611595405,33.3261296467648,18.4545286139227),(0.891136454446365,
33.9231143614638,22.358423825657),(0.476396611606317,33.5070246250397,20.3489465333494),
(1.16078600147967,32.6068699985972,14.4959058779559),(1.16078600147967,
32.90277123986,14.9184966458579),(1.91027636685896,33.2517705237246,14.7170018153717),
(2.83366864760574,34.2989327459931,16.417348572712),(4.05160482786526,34.8605049227145,
18.3452462840268),(4.05160482783567,35.0358059796366,20.3489465333493),
(2.83366864757742,34.8020681090908,22.4309178284007),(1.91027636686427,
34.0218649981858,24.3616137851687),(1.16078600147965,33.5665132752617,24.378996962633),
(1.16078600147965,33.6453139425319,24.2180520585904),(0.891136454430527,
33.9231143614471,18.3394692410448),(0.891136454445997,34.1149961777011,
20.3489465333493),(1.16078600147966,33.5575845891724,16.3227484051003),
(1.16078600147966,33.7028635004149,16.634300035664),(1.91027636685896,34.0815499110837,
16.4964694540594),(2.83366864760573,34.817541270421,18.3528219351159),(2.83366864757743,
34.992179543748,20.3489465333493),(1.91027636686427,34.574559998469,22.3890194945808),
(1.16078600147965,34.1540695491856,22.4022795177216),(1.16078600147965,
34.1782351007464,22.3160315346329),(1.16078600147966,34.1483939590439,18.2970423435867),
(1.16078600147965,34.1928544298599,18.4629710796004),(1.91027636685898,
34.5897217723691,18.3929926593539),(1.91027636686423,34.7608455627256,20.3489465333493),
(1.16078600147966,34.357855901751,20.3489465333493)));
#2686=IFCPOLYGONALFACESET(#2676,.F.,(#100),$);
#2687=IFCPOLYGONALFACESET(#2677,.F.,(#101),$);
#2688=IFCPOLYGONALFACESET(#2678,.F.,(#102,#103,#104,#105,#106,#107,#108,
#109,#110,#111,#112,#113,#114,#115,#116,#117,#118,#119,#120,#121,#122,#123,
#124,#125,#126,#127,#128,#129,#130,#131,#132,#133,#134,#135,#136,#137,#138,
#139,#140,#141,#142,#143,#144,#145,#146,#147,#148,#149,#150,#151,#152,#153,
#154,#155,#156,#157,#158,#159,#160,#161,#162,#163,#164,#165,#166,#167,#168,
#169,#170,#171,#172,#173,#174,#175,#176,#177,#178,#179,#180,#181,#182,#183,
#184,#185,#186,#187,#188,#189,#190,#191,#192,#193,#194,#195,#196,#197,#198,
#199,#200,#201,#202,#203,#204,#205,#206,#207,#208,#209,#210,#211,#212,#213,
#214,#215,#216,#217,#218,#219,#220,#221,#222,#223,#224,#225,#226,#227,#228,
#229,#230,#231,#232,#233,#234,#235,#236,#237,#238,#239,#240,#241,#242,#243,
#244,#245,#246,#247,#248,#249,#250,#251,#252,#253,#254,#255,#256,#257,#258,
#259,#260,#261,#262,#263,#264,#265,#266,#267,#268,#269,#270,#271,#272,#273,
#274,#275,#276,#277,#278,#279,#280,#281,#282,#283,#284,#285,#286,#287,#288,
#289,#290,#291,#292,#293,#294,#295,#296,#297,#298,#299,#300,#301,#302,#303,
#304,#305,#306,#307,#308,#309,#310,#311,#312,#313,#314,#315,#316,#317),
$);
#2689=IFCPOLYGONALFACESET(#2679,.F.,(#318,#319,#320,#321,#322,#323,#324,
#325,#326,#327,#328,#329,#330,#331,#332,#333,#334,#335,#336,#337,#338,#339,
#340,#341,#342,#343,#344,#345,#346,#347,#348,#349,#350,#351,#352,#353,#354,
#355,#356,#357,#358,#359,#360,#361,#362,#363,#364,#365,#366,#367,#368,#369,
#370,#371,#372,#373,#374,#375,#376,#377,#378,#379,#380,#381,#382,#383,#384,
#385,#386,#387,#388,#389,#390,#391,#392,#393,#394,#395,#396,#397,#398,#399,
#400,#401,#402,#403,#404,#405,#406,#407,#408,#409,#410,#411,#412,#413,#414,
#415,#416,#417,#418,#419,#420,#421,#422,#423,#424,#425,#426,#427,#428,#429,
#430,#431,#432,#433,#434,#435,#436,#437,#438,#439,#440,#441,#442,#443,#444,
#445,#446,#447,#448,#449,#450,#451,#452,#453,#454,#455,#456,#457,#458,#459,
#460,#461,#462,#463,#464,#465,#466,#467,#468,#469,#470,#471,#472,#473,#474,
#475,#476,#477,#478,#479,#480,#481,#482,#483,#484,#485,#486,#487,#488,#489,
#490,#491,#492,#493,#494,#495,#496,#497,#498,#499,#500,#501,#502,#503,#504,
#505,#506,#507,#508,#509,#510,#511,#512,#513,#514,#515,#516,#517,#518,#519,
#520,#521,#522,#523,#524,#525,#526,#527,#528,#529,#530,#531,#532,#533,#534,
#535,#536,#537,#538,#539,#540,#541,#542,#543,#544,#545,#546,#547,#548,#549,
#550,#551,#552,#553,#554,#555,#556,#557,#558,#559,#560,#561,#562,#563,#564,
#565,#566,#567,#568,#569,#570,#571,#572,#573,#574,#575,#576,#577,#578,#579,
#580,#581,#582,#583,#584,#585,#586,#587,#588,#589,#590,#591,#592,#593,#594,
#595,#596,#597,#598,#599,#600,#601,#602,#603,#604,#605,#606,#607,#608,#609,
#610,#611,#612,#613,#614,#615,#616,#617,#618,#619,#620,#621,#622,#623,#624,
#625,#626,#627,#628,#629,#630,#631,#632,#633,#634,#635,#636,#637,#638,#639,
#640,#641,#642,#643,#644,#645,#646,#647,#648,#649,#650,#651,#652,#653,#654,
#655,#656,#657,#658,#659,#660,#661,#662,#663,#664,#665,#666,#667,#668,#669,
#670,#671,#672,#673),$);
#2690=IFCPOLYGONALFACESET(#2680,.F.,(#674,#675,#676,#677,#678,#679,#680,
#681,#682,#683,#684,#685,#686,#687,#688,#689,#690,#691,#692,#693,#694,#695,
#696,#697,#698,#699,#700,#701,#702,#703,#704,#705,#706),$);
#2691=IFCPOLYGONALFACESET(#2681,.F.,(#707,#708,#709,#710,#711,#712,#713,
#714,#715,#716,#717,#718,#719,#720,#721,#722,#723,#724,#725,#726,#727,#728,
#729,#730,#731,#732,#733,#734,#735,#736,#737,#738,#739,#740,#741,#742,#743,
#744,#745,#746,#747,#748,#749,#750,#751,#752,#753,#754),$);
#2692=IFCPOLYGONALFACESET(#2682,.F.,(#755,#756,#757,#758,#759,#760,#761,
#762,#763,#764,#765,#766,#767,#768,#769,#770,#771,#772,#773,#774,#775,#776,
#777,#778,#779,#780,#781,#782,#783,#784,#785,#786,#787,#788,#789,#790,#791,
#792,#793,#794,#795,#796,#797,#798,#799,#800,#801,#802,#803,#804,#805,#806,
#807,#808,#809,#810,#811,#812,#813,#814,#815,#816,#817,#818,#819,#820,#821,
#822,#823,#824,#825,#826,#827,#828,#829,#830,#831,#832,#833,#834,#835,#836,
#837,#838,#839,#840,#841,#842,#843,#844,#845,#846,#847,#848,#849,#850,#851,
#852,#853,#854,#855,#856,#857,#858,#859,#860,#861,#862,#863,#864,#865,#866,
#867,#868,#869,#870,#871,#872,#873,#874,#875,#876,#877,#878,#879,#880,#881,
#882,#883,#884,#885,#886,#887,#888,#889,#890,#891,#892,#893,#894,#895,#896,
#897,#898,#899,#900,#901,#902,#903,#904,#905,#906,#907,#908,#909,#910,#911,
#912,#913,#914,#915,#916,#917,#918,#919,#920,#921,#922,#923,#924,#925,#926,
#927,#928,#929,#930,#931,#932,#933,#934,#935,#936,#937,#938,#939,#940,#941,
#942,#943,#944,#945,#946,#947,#948,#949,#950,#951,#952,#953,#954,#955,#956,
#957,#958,#959,#960,#961,#962,#963,#964,#965,#966,#967,#968,#969,#970,#971,
#972,#973,#974,#975,#976,#977,#978,#979,#980,#981,#982,#983,#984,#985,#986,
#987,#988,#989,#990,#991,#992,#993,#994,#995,#996,#997,#998,#999,#1000,
#1001,#1002,#1003,#1004,#1005,#1006,#1007,#1008,#1009,#1010,#1011,#1012,
#1013,#1014,#1015,#1016,#1017,#1018,#1019,#1020,#1021,#1022,#1023,#1024,
#1025,#1026,#1027,#1028,#1029,#1030,#1031,#1032,#1033,#1034,#1035,#1036,
#1037,#1038,#1039,#1040,#1041,#1042,#1043,#1044,#1045,#1046,#1047,#1048,
#1049,#1050,#1051,#1052,#1053,#1054,#1055,#1056,#1057,#1058,#1059,#1060,
#1061,#1062,#1063,#1064,#1065,#1066,#1067,#1068,#1069,#1070,#1071,#1072,
#1073,#1074,#1075,#1076,#1077,#1078,#1079,#1080,#1081,#1082,#1083,#1084,
#1085,#1086,#1087,#1088,#1089,#1090,#1091,#1092,#1093,#1094,#1095,#1096,
#1097,#1098,#1099,#1100,#1101,#1102,#1103,#1104,#1105,#1106,#1107,#1108,
#1109,#1110,#1111,#1112,#1113,#1114,#1115,#1116,#1117,#1118,#1119,#1120,
#1121,#1122,#1123,#1124,#1125,#1126,#1127,#1128,#1129,#1130,#1131,#1132,
#1133,#1134,#1135,#1136,#1137,#1138,#1139,#1140,#1141,#1142,#1143,#1144,
#1145,#1146,#1147,#1148,#1149,#1150,#1151,#1152,#1153,#1154,#1155,#1156,
#1157,#1158,#1159,#1160,#1161,#1162,#1163,#1164,#1165,#1166,#1167,#1168,
#1169,#1170,#1171,#1172,#1173,#1174,#1175,#1176,#1177,#1178,#1179,#1180,
#1181,#1182,#1183,#1184,#1185,#1186,#1187,#1188,#1189,#1190,#1191,#1192,
#1193,#1194,#1195,#1196,#1197,#1198,#1199,#1200,#1201,#1202,#1203,#1204,
#1205,#1206,#1207,#1208,#1209,#1210,#1211,#1212,#1213,#1214,#1215,#1216,
#1217,#1218,#1219,#1220,#1221,#1222,#1223,#1224,#1225,#1226,#1227,#1228,
#1229,#1230,#1231,#1232,#1233,#1234,#1235,#1236,#1237,#1238,#1239,#1240,
#1241,#1242,#1243,#1244,#1245,#1246,#1247,#1248,#1249,#1250,#1251,#1252,
#1253,#1254,#1255,#1256,#1257,#1258,#1259,#1260,#1261,#1262,#1263,#1264,
#1265,#1266,#1267,#1268,#1269,#1270,#1271,#1272,#1273,#1274,#1275,#1276,
#1277,#1278,#1279,#1280,#1281,#1282,#1283,#1284,#1285,#1286,#1287,#1288,
#1289,#1290,#1291,#1292,#1293,#1294,#1295,#1296,#1297,#1298,#1299,#1300,
#1301,#1302,#1303,#1304,#1305,#1306,#1307,#1308,#1309,#1310,#1311,#1312,
#1313,#1314,#1315,#1316,#1317,#1318,#1319,#1320,#1321,#1322,#1323,#1324,
#1325,#1326,#1327,#1328,#1329,#1330,#1331,#1332,#1333,#1334,#1335,#1336,
#1337,#1338,#1339,#1340,#1341,#1342,#1343,#1344,#1345,#1346,#1347,#1348,
#1349,#1350,#1351,#1352,#1353,#1354,#1355,#1356,#1357,#1358,#1359,#1360,
#1361,#1362,#1363,#1364,#1365,#1366,#1367,#1368,#1369,#1370,#1371,#1372,
#1373,#1374,#1375,#1376,#1377,#1378,#1379,#1380,#1381,#1382,#1383,#1384,
#1385,#1386,#1387,#1388,#1389,#1390,#1391,#1392,#1393,#1394,#1395,#1396,
#1397,#1398,#1399,#1400,#1401,#1402,#1403,#1404,#1405,#1406,#1407,#1408,
#1409,#1410,#1411,#1412,#1413,#1414,#1415,#1416,#1417,#1418,#1419,#1420,
#1421,#1422,#1423,#1424,#1425,#1426,#1427,#1428,#1429,#1430,#1431,#1432,
#1433,#1434,#1435,#1436,#1437,#1438,#1439,#1440,#1441,#1442,#1443,#1444,
#1445,#1446,#1447,#1448,#1449,#1450,#1451,#1452,#1453,#1454,#1455,#1456,
#1457,#1458,#1459,#1460,#1461,#1462,#1463,#1464,#1465,#1466,#1467,#1468,
#1469,#1470,#1471,#1472,#1473,#1474,#1475,#1476,#1477,#1478,#1479,#1480,
#1481,#1482,#1483,#1484,#1485,#1486,#1487,#1488,#1489,#1490,#1491,#1492,
#1493,#1494,#1495,#1496,#1497,#1498,#1499,#1500,#1501,#1502,#1503,#1504,
#1505,#1506,#1507,#1508,#1509,#1510,#1511,#1512,#1513,#1514,#1515,#1516,
#1517,#1518,#1519,#1520,#1521,#1522,#1523,#1524,#1525,#1526,#1527,#1528,
#1529,#1530,#1531,#1532,#1533,#1534,#1535,#1536,#1537,#1538,#1539,#1540,
#1541,#1542,#1543,#1544,#1545,#1546,#1547,#1548,#1549,#1550,#1551,#1552,
#1553,#1554,#1555,#1556,#1557,#1558,#1559,#1560,#1561,#1562,#1563,#1564,
#1565,#1566,#1567,#1568,#1569,#1570,#1571,#1572,#1573,#1574,#1575,#1576,
#1577,#1578,#1579,#1580,#1581,#1582,#1583,#1584,#1585,#1586,#1587,#1588,
#1589,#1590,#1591,#1592,#1593,#1594,#1595,#1596,#1597,#1598,#1599,#1600,
#1601,#1602,#1603,#1604,#1605,#1606,#1607,#1608,#1609,#1610,#1611,#1612,
#1613,#1614,#1615,#1616,#1617,#1618,#1619,#1620,#1621,#1622,#1623,#1624,
#1625,#1626,#1627,#1628,#1629,#1630,#1631,#1632,#1633,#1634,#1635,#1636,
#1637,#1638,#1639,#1640,#1641,#1642,#1643,#1644,#1645,#1646,#1647,#1648,
#1649,#1650,#1651,#1652,#1653,#1654,#1655,#1656,#1657,#1658,#1659,#1660,
#1661,#1662,#1663,#1664,#1665,#1666,#1667,#1668,#1669,#1670,#1671,#1672,
#1673,#1674,#1675,#1676,#1677,#1678,#1679,#1680,#1681,#1682,#1683,#1684,
#1685,#1686,#1687,#1688,#1689,#1690,#1691,#1692,#1693,#1694,#1695,#1696,
#1697,#1698,#1699,#1700,#1701,#1702,#1703,#1704,#1705,#1706,#1707,#1708,
#1709,#1710,#1711,#1712,#1713,#1714,#1715,#1716,#1717,#1718,#1719,#1720,
#1721,#1722,#1723,#1724,#1725,#1726,#1727,#1728,#1729,#1730,#1731,#1732,
#1733,#1734,#1735,#1736,#1737,#1738,#1739,#1740,#1741,#1742,#1743,#1744,
#1745,#1746,#1747,#1748,#1749,#1750,#1751,#1752,#1753,#1754,#1755,#1756,
#1757,#1758,#1759,#1760,#1761,#1762),$);
#2693=IFCPOLYGONALFACESET(#2683,.F.,(#22,#1763,#23,#1764),$);
#2694=IFCPOLYGONALFACESET(#2684,.F.,(#24),$);
#2695=IFCPOLYGONALFACESET(#2685,.T.,(#1765,#1766,#1767,#1768,#1769,#1770,
#1771,#1772,#1773,#1774,#1775,#1776,#1777,#1778,#1779,#1780,#1781,#1782,
#1783,#1784,#1785,#1786,#1787,#1788,#1789,#1790,#1791,#1792,#1793,#1794,
#1795,#1796,#1797,#1798,#1799,#1800,#1801,#1802,#1803,#1804,#1805,#1806,
#1807,#1808,#1809,#1810,#1811,#1812,#1813,#1814,#1815,#1816,#1817,#1818,
#1819,#1820,#1821,#1822,#1823,#1824,#1825,#1826,#1827,#1828,#1829,#1830,
#1831,#1832,#1833,#1834,#1835,#1836,#1837,#1838,#1839,#1840,#1841,#1842,
#1843,#1844,#1845,#1846,#1847,#1848,#1849,#1850,#1851,#1852,#1853,#1854,
#1855,#1856,#1857,#1858,#1859,#1860,#1861,#1862,#1863,#1864,#1865,#1866,
#1867,#1868,#1869,#1870,#1871,#1872,#1873,#1874,#1875,#1876,#1877,#1878,
#1879,#1880,#1881,#1882,#1883,#1884,#1885,#1886,#1887,#1888,#1889,#1890,
#1891,#1892,#1893,#1894,#1895,#1896,#1897,#1898,#1899,#1900,#1901,#1902,
#1903,#1904,#1905,#1906,#1907,#1908,#1909,#1910,#1911,#1912,#1913,#1914,
#1915,#1916,#1917,#1918,#1919,#1920,#1921,#1922,#1923,#1924,#1925,#1926,
#1927,#1928,#1929,#1930,#1931,#1932,#1933,#1934,#1935,#1936,#1937,#1938,
#1939,#1940,#1941,#1942,#1943,#1944,#1945,#1946,#1947,#1948,#1949,#1950,
#1951,#1952,#1953,#1954,#1955,#1956,#1957,#1958,#1959,#1960,#1961,#1962,
#1963,#1964,#1965,#1966,#1967,#1968,#1969,#1970,#1971,#1972,#1973,#1974,
#1975,#1976,#1977,#1978,#1979,#1980,#1981,#1982,#1983,#1984,#1985,#1986,
#1987,#1988,#1989,#1990,#1991,#1992,#1993,#1994,#1995,#1996,#1997,#1998,
#1999,#2000,#2001,#2002,#2003,#2004,#2005,#2006,#2007,#2008,#2009,#2010,
#2011,#2012,#2013,#2014,#2015,#2016,#2017,#2018,#2019,#2020,#2021,#2022,
#2023,#2024,#2025,#2026,#2027,#2028,#2029,#2030,#2031,#2032,#2033,#2034,
#2035,#2036,#2037,#2038,#2039,#2040,#2041,#2042,#2043,#2044,#2045,#2046,
#2047,#2048,#2049,#2050,#2051,#2052,#2053,#2054,#2055,#2056,#2057,#2058,
#2059,#2060,#2061,#2062,#2063,#2064,#2065,#2066,#2067,#2068,#2069,#2070,
#2071,#2072,#2073,#2074,#2075,#2076,#2077,#2078,#2079,#2080,#2081,#2082,
#2083,#2084,#2085,#2086,#2087,#2088,#2089,#2090,#2091,#2092,#2093,#2094,
#2095,#2096,#2097,#2098,#2099,#2100,#2101,#2102,#2103,#2104,#2105,#2106,
#2107,#2108,#2109,#2110,#2111,#2112,#2113,#2114,#2115,#2116,#2117,#2118,
#2119,#2120,#2121,#2122,#2123,#2124,#2125,#2126,#2127,#2128,#2129,#2130,
#2131,#2132,#2133,#2134,#2135,#2136,#2137,#2138,#2139,#2140,#2141,#2142,
#2143,#2144,#2145,#2146,#2147,#2148,#2149,#2150,#2151,#2152,#2153,#2154,
#2155,#2156,#2157,#2158,#2159,#2160,#2161,#2162,#2163,#2164,#2165,#2166,
#2167,#2168,#2169,#2170,#2171,#2172,#2173,#2174,#2175,#2176,#2177,#2178,
#2179,#2180,#2181,#2182,#2183,#2184,#2185,#2186,#2187,#2188,#2189,#2190,
#2191,#2192,#2193,#2194,#2195,#2196,#2197,#2198,#2199,#2200,#2201,#2202,
#2203,#2204,#2205,#2206,#2207,#2208,#2209,#2210,#2211,#2212,#2213,#2214,
#2215,#2216,#2217,#2218,#2219,#2220,#2221,#2222,#2223,#2224,#2225,#2226,
#2227,#2228,#2229,#2230,#2231,#2232,#2233,#2234,#2235,#2236,#2237,#2238,
#2239,#2240,#2241,#2242,#2243,#2244,#2245,#2246,#2247,#2248,#2249,#2250,
#2251,#2252,#2253,#2254,#2255,#2256,#2257,#2258,#2259,#2260,#2261,#2262,
#2263,#2264,#2265,#2266,#2267,#2268,#2269,#2270,#2271,#2272,#2273,#2274,
#2275,#2276,#2277,#2278,#2279,#2280,#2281,#2282,#2283,#2284,#2285,#2286,
#2287,#2288,#2289,#2290,#2291,#2292,#2293,#2294,#2295,#2296,#2297,#2298,
#2299,#2300,#2301,#2302,#2303,#2304,#2305,#2306,#2307,#2308,#2309,#2310,
#2311,#2312,#2313,#2314,#2315,#2316,#2317,#2318,#2319,#2320,#2321,#2322,
#2323,#2324,#2325,#2326,#2327,#2328,#2329,#2330,#2331,#2332,#2333,#2334,
#2335,#2336,#2337,#2338,#2339,#2340,#2341,#2342,#2343,#2344,#2345,#2346,
#2347,#2348,#2349,#2350,#2351,#2352,#2353,#2354,#2355,#2356,#2357,#2358,
#2359,#2360,#2361,#2362,#2363,#2364,#2365,#2366,#2367,#2368,#2369,#2370,
#2371,#2372,#2373,#2374,#2375,#2376,#2377,#2378,#2379,#2380,#2381,#2382,
#2383,#2384,#2385,#2386,#2387,#2388,#2389,#2390,#2391,#2392,#2393,#2394,
#2395,#2396,#2397,#2398,#2399,#2400,#2401,#2402,#2403,#2404,#2405,#2406,
#2407,#2408,#2409,#2410,#2411,#2412,#2413,#2414,#2415,#2416,#2417,#2418,
#2419,#2420,#2421,#2422,#2423,#2424,#2425,#2426,#2427,#2428,#2429,#2430,
#2431,#2432,#2433,#2434,#2435,#2436,#2437,#2438,#2439,#2440,#2441,#2442,
#2443,#2444,#2445,#2446,#2447,#2448,#2449,#2450,#2451,#2452,#2453,#2454,
#2455,#2456,#2457,#2458,#2459,#2460,#2461,#2462,#2463,#2464,#2465,#2466,
#2467,#2468,#2469,#2470,#2471,#2472,#2473,#2474,#2475,#2476,#2477,#2478,
#2479,#2480,#2481,#2482,#2483,#2484,#2485,#2486,#2487,#2488,#2489,#2490,
#2491,#2492,#2493,#2494,#2495,#2496,#2497,#2498,#2499,#2500,#2501,#2502,
#2503,#2504,#2505,#2506,#2507,#2508,#2509,#2510,#2511,#2512,#2513,#2514,
#2515,#2516,#2517,#2518,#2519,#2520,#2521,#2522,#2523,#2524,#2525,#2526,
#2527,#2528,#2529,#2530,#2531,#2532,#2533,#2534,#2535,#2536,#2537,#2538,
#2539,#2540,#2541,#2542,#2543,#2544,#2545,#2546,#2547,#2548,#2549,#2550,
#2551,#2552,#2553,#2554,#2555,#2556,#2557,#2558,#2559,#2560,#2561,#2562,
#2563,#2564,#2565,#2566,#2567,#2568,#2569,#2570,#2571,#2572,#2573,#2574,
#2575,#2576,#2577,#2578,#2579,#2580,#2581,#2582,#2583,#2584,#2585,#2586,
#2587,#2588,#2589,#2590,#2591,#2592,#2593,#2594,#2595,#2596,#2597,#2598,
#2599,#2600,#2601,#2602,#2603,#2604,#2605,#2606,#2607,#2608,#2609,#2610,
#2611,#2612,#2613,#2614,#2615,#2616,#2617,#2618,#2619,#2620,#2621,#2622,
#2623,#2624,#2625,#2626,#2627,#2628,#2629,#2630,#2631,#2632,#2633,#2634,
#2635,#2636,#2637,#2638,#2639,#2640,#2641,#2642,#2643,#2644,#2645,#2646,
#2647,#2648,#2649,#2650,#2651,#2652,#2653,#2654,#2655,#2656,#2657,#2658,
#2659,#2660,#2661,#2662,#2663,#2664,#2665,#2666,#2667,#2668,#2669,#2670,
#2671,#2672,#2673,#2674,#2675),$);
#2696=IFCRELAGGREGATES('1kB3AQvfGpkFm_7uHNRdoP',#2834,$,$,#2823,(#2749));
#2697=IFCRELAGGREGATES('0Cp_wDOOr3amQ16IWrGpUy',#2834,$,$,#2749,(#68));
#2698=IFCRELAGGREGATES('1NfacxJUhl1S_ka3CQXVRt',#2834,$,$,#68,(#67));
#2699=IFCLOCALPLACEMENT($,#2712);
#2700=IFCLOCALPLACEMENT(#2699,#2713);
#2701=IFCLOCALPLACEMENT(#2700,#2714);
#2702=IFCLOCALPLACEMENT(#2701,#2715);
#2703=IFCLOCALPLACEMENT(#2701,#2717);
#2704=IFCLOCALPLACEMENT(#2701,#2718);
#2705=IFCLOCALPLACEMENT(#2701,#2719);
#2706=IFCLOCALPLACEMENT(#2701,#2720);
#2707=IFCLOCALPLACEMENT(#2701,#2721);
#2708=IFCLOCALPLACEMENT(#2701,#2722);
#2709=IFCLOCALPLACEMENT(#2701,#2723);
#2710=IFCLOCALPLACEMENT(#2701,#2724);
#2711=IFCLOCALPLACEMENT(#2701,#2725);
#2712=IFCAXIS2PLACEMENT3D(#2726,#2746,#2747);
#2713=IFCAXIS2PLACEMENT3D(#2726,#2746,#2747);
#2714=IFCAXIS2PLACEMENT3D(#2727,#2746,#2747);
#2715=IFCAXIS2PLACEMENT3D(#2728,#2746,#2747);
#2716=IFCAXIS2PLACEMENT3D(#2726,#2746,#2747);
#2717=IFCAXIS2PLACEMENT3D(#2729,#2746,#2747);
#2718=IFCAXIS2PLACEMENT3D(#2730,#2746,#2747);
#2719=IFCAXIS2PLACEMENT3D(#2732,#2746,#2747);
#2720=IFCAXIS2PLACEMENT3D(#2734,#2746,#2747);
#2721=IFCAXIS2PLACEMENT3D(#2736,#2746,#2747);
#2722=IFCAXIS2PLACEMENT3D(#2738,#2746,#2747);
#2723=IFCAXIS2PLACEMENT3D(#2740,#2746,#2747);
#2724=IFCAXIS2PLACEMENT3D(#2742,#2746,#2747);
#2725=IFCAXIS2PLACEMENT3D(#2744,#2746,#2747);
#2726=IFCCARTESIANPOINT((0.,0.,0.));
#2727=IFCCARTESIANPOINT((0.,0.,9.144));
#2728=IFCCARTESIANPOINT((14.6172,-36.0816,-9.1441));
#2729=IFCCARTESIANPOINT((-7.5,-8.3334,-9.1441));
#2730=IFCCARTESIANPOINT((93.5794,-106.4206,-9.144));
#2731=IFCCARTESIANPOINT((0.64308122385,0.64308122385,0.));
#2732=IFCCARTESIANPOINT((37.021,-13.0088,37.1934));
#2733=IFCCARTESIANPOINT((0.91134567569,1.09487351805,0.52064438327));
#2734=IFCCARTESIANPOINT((71.711,-83.9857,-9.1441));
#2735=IFCCARTESIANPOINT((3.3136120285,4.5910781088,0.0001));
#2736=IFCCARTESIANPOINT((14.1421,14.1421,37.2623));
#2737=IFCCARTESIANPOINT((3.5623730952E-5,3.5623730946E-5,2.15068135964));
#2738=IFCCARTESIANPOINT((89.1421,-68.3287,38.6153));
#2739=IFCCARTESIANPOINT((3.5623730961E-5,1.053259176E-5,0.59627198567));
#2740=IFCCARTESIANPOINT((114.1421,-89.6079,40.856));
#2741=IFCCARTESIANPOINT((3.5623730952E-5,3.5623730952E-5,0.));
#2742=IFCCARTESIANPOINT((64.1421,-43.3579,40.8559));
#2743=IFCCARTESIANPOINT((3.4459577698E-5,3.4459577728E-5,9.8835846761E-5));
#2744=IFCCARTESIANPOINT((43.5938,-79.2095,-23.7804));
#2745=IFCCARTESIANPOINT((0.214092166258,11.9700642881,8.8100966805));
#2746=IFCDIRECTION((0.,0.,1.));
#2747=IFCDIRECTION((1.,0.,0.));
#2748=IFCDIRECTION((0.,1.));
#2749=IFCSITE('006_i1V1D0tAKEh_$vljQt',#2834,'Site 1','Site 1',$,#2699,
$,'Site 1',.ELEMENT.,$,$,0.,$,#2825);
#2750=IFCRELDEFINESBYPROPERTIES('1RL1EewHTsROrPcNSlBI56',#2834,
'Project',$,(#2823),#2761);
#2751=IFCRELDEFINESBYPROPERTIES('3bjMWmv_Cp6LPxmaCnPGwW',#2834,'Site',$,
(#2749),#2762);
#2752=IFCRELDEFINESBYPROPERTIES('2zOmgYUoQmhp9sZMdCZF7s',#2834,
'Pset_BuildingCommon',$,(#68),#2763);
#2753=IFCRELDEFINESBYPROPERTIES('3k0v4EdaEvPD8NSrRFB2MT',#2834,
'ArchBuilding',$,(#68),#2764);
#2754=IFCRELDEFINESBYPROPERTIES('3uIgchz4n8nDCCMgq1BBql',#2834,
'ObjectIdentity',$,(#68),#2765);
#2755=IFCRELDEFINESBYPROPERTIES('1CCmZ_1eEDpfMO62t1jkZj',#2834,
'ObjectPostalAddress',$,(#68),#2766);
#2756=IFCRELDEFINESBYPROPERTIES('24I$F7L8gfSwdAGDvfkVEu',#2834,
'Pset_BuildingStoreyCommon',$,(#67),#2767);
#2757=IFCRELDEFINESBYPROPERTIES('1K1DvjfvPzx3hE5w5ZHK0q',#2834,
'ArchFloor',$,(#67),#2768);
#2758=IFCRELDEFINESBYPROPERTIES('0lrK04aZ0v6OfwsyTXPzZq',#2834,'Floor',
$,(#67),#2769);
#2759=IFCRELDEFINESBYPROPERTIES('1fbDyx4oFQKKacC_b9xURq',#2834,
'StructuralFloorCommon',$,(#67),#2770);
#2760=IFCRELDEFINESBYPROPERTIES('0wWmVuJT0dMD2gjn9kV2VW',#2834,
'Qto_WallBaseQuantities',$,(#21),#15);
#2761=IFCPROPERTYSET('2rcLYSOKrF02Xv$qFtEF4e',#2834,'Project',$,(#2779));
#2762=IFCPROPERTYSET('2Qswsgonw96KTM1$UoUklF',#2834,'Site',$,(#2780,#2781));
#2763=IFCPROPERTYSET('2Ct1rxyLMs3vgB_jB90hMr',#2834,
'Pset_BuildingCommon',$,(#2782));
#2764=IFCPROPERTYSET('3OmKLTEF7bgCV0$1Gr7ajZ',#2834,'ArchBuilding',$,(#2783,
#2784));
#2765=IFCPROPERTYSET('1HjNcqn7ak9HvLTK8j8w1d',#2834,'ObjectIdentity',$,
(#2785));
#2766=IFCPROPERTYSET('306EzywKpRfIeis4axlC1d',#2834,
'ObjectPostalAddress',$,(#2786,#2787,#2788,#2789));
#2767=IFCPROPERTYSET('381uKge8G33qXIbHr84hB4',#2834,
'Pset_BuildingStoreyCommon',$,(#2790,#2791,#2792,#2793));
#2768=IFCPROPERTYSET('0GZmVQOAmJ6zMOk6j3uleN',#2834,'ArchFloor',$,(#2794,
#2795,#2796,#2797,#2798,#2799,#2800,#2801,#2802,#2803));
#2769=IFCPROPERTYSET('0A3C_vQUNRQZfA6f5qtBlE',#2834,'Floor',$,(#2804,#2805,
#2806));
#2770=IFCPROPERTYSET('0izo3etGuCpkjROydkcZbT',#2834,
'StructuralFloorCommon',$,(#2807));
#2771=IFCPROPERTYSET('0fQNiANe9J5TQvsKqhfdAW',#2834,'Pset_WallCommon',$,
(#2808,#2809,#2810,#2811));
#2772=IFCPROPERTYSET('16i1gNdxBFgAIcqphdG8wM',#2834,
'ObjectClassification',$,(#2812));
#2773=IFCPROPERTYSET('2nhvqxxy13YVxp49bmsvnH',#2834,
'ObjectFireResistance',$,(#2813,#2814));
#2774=IFCPROPERTYSET('29vBjdQvKt0poXwQNpT3XC',#2834,'ObjectIdentity',$,
(#2815));
#2775=IFCPROPERTYSET('39eoxL41zhY8Qd4GdC8QVS',#2834,'ObjectMaterial',$,
(#2816));
#2776=IFCPROPERTYSET('16fmRZHdwxm0E_FuKF66Vi',#2834,
'ObjectSpaceBounding',$,(#2817));
#2777=IFCPROPERTYSET('3SstypxQR69NxtXrEbq2c5',#2834,
'ObjectThermalTransmittance',$,(#2818,#2819));
#2778=IFCPROPERTYSET('0cjqvAvK8RO48bn57_Yvty',#2834,'Wall_Common',$,(#2820,
#2821));
#2779=IFCPROPERTYSINGLEVALUE('ProjectName',$,IFCTEXT(
'BuildingTemplate_US'),$);
#2780=IFCPROPERTYSINGLEVALUE('SiteName',$,IFCTEXT('Site 1'),$);
#2781=IFCPROPERTYSINGLEVALUE('BuildingHeightLimit',$,
IFCLENGTHMEASURE(0.),#2970);
#2782=IFCPROPERTYSINGLEVALUE('YearOfConstruction',$,IFCLABEL('2006'),$);
#2783=IFCPROPERTYSINGLEVALUE('BuildingName',$,IFCTEXT('Bldg 1'),$);
#2784=IFCPROPERTYSINGLEVALUE('YearConstructed',$,IFCTEXT('2006'),$);
#2785=IFCPROPERTYSINGLEVALUE('Description',$,IFCTEXT('3 Story Building'),
$);
#2786=IFCPROPERTYSINGLEVALUE('Address1',$,IFCTEXT(
'203 Rickenhouse Drive'),$);
#2787=IFCPROPERTYSINGLEVALUE('City',$,IFCTEXT('Madison'),$);
#2788=IFCPROPERTYSINGLEVALUE('PostalCode',$,IFCTEXT('35789'),$);
#2789=IFCPROPERTYSINGLEVALUE('Country',$,IFCTEXT('US'),$);
#2790=IFCPROPERTYSINGLEVALUE('GrossAreaPlanned',$,IFCAREAMEASURE(22296729.6),
#2971);
#2791=IFCPROPERTYSINGLEVALUE('GrossPlannedArea',$,IFCAREAMEASURE(22296729.6),
$);
#2792=IFCPROPERTYSINGLEVALUE('NetAreaPlanned',$,IFCAREAMEASURE(22296729.6),
#2971);
#2793=IFCPROPERTYSINGLEVALUE('NetPlannedArea',$,IFCAREAMEASURE(22296729.6),
$);
#2794=IFCPROPERTYSINGLEVALUE('FloorName',$,IFCTEXT('Floor 3'),$);
#2795=IFCPROPERTYSINGLEVALUE('FinishedFloorElevation',$,IFCREAL(9.144),
$);
#2796=IFCPROPERTYSINGLEVALUE('Description',$,IFCTEXT(
'Administrative and employee lounge'),$);
#2797=IFCPROPERTYSINGLEVALUE('ApproxLength',$,IFCLENGTHMEASURE(6096.),#2970);
#2798=IFCPROPERTYSINGLEVALUE('ApproxWidth',$,IFCLENGTHMEASURE(3657.6),#2970);
#2799=IFCPROPERTYSINGLEVALUE('TypicalFloor',$,IFCBOOLEAN(.F.),$);
#2800=IFCPROPERTYSINGLEVALUE('TypicalFloorHeight',$,IFCLENGTHMEASURE(0.),
#2970);
#2801=IFCPROPERTYSINGLEVALUE('TypicalFloorBaseElevation',$,
IFCLENGTHMEASURE(0.),#2970);
#2802=IFCPROPERTYSINGLEVALUE('ACSRotation',$,IFCTEXT('0.00000000'),$);
#2803=IFCPROPERTYSINGLEVALUE('Annotation',$,IFCBOOLEAN(.T.),$);
#2804=IFCPROPERTYSINGLEVALUE('GrossArea',$,IFCAREAMEASURE(22296729.6),#2971);
#2805=IFCPROPERTYSINGLEVALUE('NetArea',$,IFCAREAMEASURE(22296729.6),#2971);
#2806=IFCPROPERTYSINGLEVALUE('OccupancyType',$,IFCTEXT('Commercial'),$);
#2807=IFCPROPERTYSINGLEVALUE('issplicefloor',$,IFCBOOLEAN(.F.),$);
#2808=IFCPROPERTYSINGLEVALUE('Combustible',$,IFCBOOLEAN(.F.),$);
#2809=IFCPROPERTYSINGLEVALUE('Compartmentation',$,IFCBOOLEAN(.F.),$);
#2810=IFCPROPERTYSINGLEVALUE('IsExternal',$,IFCBOOLEAN(.T.),$);
#2811=IFCPROPERTYSINGLEVALUE('LoadBearing',$,IFCBOOLEAN(.F.),$);
#2812=IFCPROPERTYSINGLEVALUE('UniFormat',$,IFCLABEL('B2010'),$);
#2813=IFCPROPERTYSINGLEVALUE('IsCombustible',$,IFCBOOLEAN(.F.),$);
#2814=IFCPROPERTYSINGLEVALUE('Compartmentation',$,IFCBOOLEAN(.F.),$);
#2815=IFCPROPERTYSINGLEVALUE('Description',$,IFCTEXT(
'Default exterior wall'),$);
#2816=IFCPROPERTYSINGLEVALUE('PartDefinition',$,IFCLABEL(
'WallAssembly::*Default Exterior Wall*'),$);
#2817=IFCPROPERTYSINGLEVALUE('IsSpaceBounding',$,IFCBOOLEAN(.T.),$);
#2818=IFCPROPERTYSINGLEVALUE('IsExternal',$,IFCBOOLEAN(.T.),$);
#2819=IFCPROPERTYSINGLEVALUE('IsBelowGrade',$,IFCBOOLEAN(.F.),$);
#2820=IFCPROPERTYSINGLEVALUE('Type',$,IFCTEXT('Schematic'),$);
#2821=IFCPROPERTYSINGLEVALUE('IsCompound',$,IFCBOOLEAN(.F.),$);
#2822=IFCUNITASSIGNMENT((#2970,#2971,#2972,#2973,#2974,#2975,#2976,#2977,
#2978,#2979,#2980,#2981,#2982,#2983,#2984,#2985,#2986,#2987,#2988,#2989,
#2990,#2991,#2967,#2993,#2994,#2995,#2996,#2997,#2998,#2836,#2837,#2838,
#2839,#2840,#2841,#2842,#2843,#2844,#2845,#2846,#2847,#2848,#2849,#2850,
#2851,#2852,#2853,#2854,#2855,#2856,#2857,#2858,#2859,#2860,#2861,#2862,
#2863,#2864,#2865,#2866,#2867,#2868,#2869,#2870,#2871,#2872,#2873,#2874,
#2875,#2876,#2877,#2878,#2879,#2835));
#2823=IFCPROJECT('1JmBUThP19Z8wED88xGwCF',#2834,'BuildingTemplate_US',
'BuildingTemplate_US',$,'BuildingTemplate_US',$,(#37),#2822);
#2824=IFCPOSTALADDRESS($,$,$,$,('Address 1','Address 2'),$,'City/Town',
'State/Region','Postal Code','Country');
#2825=IFCPOSTALADDRESS($,$,$,'Site 1',$,$,$,$,$,$);
#2826=IFCPOSTALADDRESS($,$,$,$,('203 Rickenhouse Drive'),$,'Madison',$,
'35789','US');
#2827=IFCTELECOMADDRESS($,$,$,('Phone'),$,$,('E-Mail'),$,$);
#2828=IFCACTORROLE(.SUPPLIER.,$,$);
#2829=IFCPERSON($,'Last Name','First Name',$,$,$,(#2828),(#2827));
#2830=IFCPERSONANDORGANIZATION(#2829,#2832,$);
#2831=IFCORGANIZATION($,'Bentley Systems Inc.',$,$,$);
#2832=IFCORGANIZATION($,'Organization Name',$,$,(#2824));
#2833=IFCAPPLICATION(#2831,'***********','OpenBuildings Designer','ABD');
#2834=IFCOWNERHISTORY(#2830,#2833,$,$,0,$,$,1583400691);
#2835=IFCMONETARYUNIT('USD');
#2836=IFCDERIVEDUNIT((#2880,#2881),.ACCELERATIONUNIT.,
'(METRE)/(SECOND^2)');
#2837=IFCDERIVEDUNIT((#2882,#2883),.ANGULARVELOCITYUNIT.,
'(DEGREE)/(SECOND)');
#2838=IFCDERIVEDUNIT((#2884,#2885),.CURVATUREUNIT.,'(DEGREE)/(METRE)');
#2839=IFCDERIVEDUNIT((#2886,#2887),.DYNAMICVISCOSITYUNIT.,
'(PASCAL)(SECOND)');
#2840=IFCDERIVEDUNIT((#2888,#2889),.HEATFLUXDENSITYUNIT.,
'(WATT)/(METRE^2)');
#2841=IFCDERIVEDUNIT((#2890,#2891),.HEATINGVALUEUNIT.,'(JOULE)/(GRAM)');
#2842=IFCDERIVEDUNIT((#2892),.INTEGERCOUNTRATEUNIT.,'1/(SECOND)');
#2843=IFCDERIVEDUNIT((#2893,#2894),.IONCONCENTRATIONUNIT.,
'(GRAM)/(CUBIC_METRE)');
#2844=IFCDERIVEDUNIT((#2895,#2896),.ISOTHERMALMOISTURECAPACITYUNIT.,
'(CUBIC_METRE)/(GRAM)');
#2845=IFCDERIVEDUNIT((#2897,#2898),.KINEMATICVISCOSITYUNIT.,
'(SQUARE_METRE)/(SECOND)');
#2846=IFCDERIVEDUNIT((#2899,#2900),.LINEARFORCEUNIT.,'(NEWTON)/(METRE)');
#2847=IFCDERIVEDUNIT((#2901,#2902),.LINEARMOMENTUNIT.,
'(NEWTON)/(METRE)');
#2848=IFCDERIVEDUNIT((#2903,#2904),.LINEARSTIFFNESSUNIT.,
'(NEWTON)/(METRE)');
#2849=IFCDERIVEDUNIT((#2905,#2906),.LINEARVELOCITYUNIT.,
'(METRE)/(SECOND)');
#2850=IFCDERIVEDUNIT((#2907,#2908),.LUMINOUSINTENSITYDISTRIBUTIONUNIT.,
'(CANDELA)/(LUMEN)');
#2851=IFCDERIVEDUNIT((#2909,#2910),.MASSDENSITYUNIT.,
'(GRAM)/(CUBIC_METRE)');
#2852=IFCDERIVEDUNIT((#2911,#2912),.MASSFLOWRATEUNIT.,'(GRAM)/(SECOND)');
#2853=IFCDERIVEDUNIT((#2913,#2914),.MASSPERLENGTHUNIT.,'(GRAM)/(METRE)');
#2854=IFCDERIVEDUNIT((#2915,#2916),.MODULUSOFELASTICITYUNIT.,
'(NEWTON)/(SQUARE_METRE)');
#2855=IFCDERIVEDUNIT((#2917,#2918),
 .MODULUSOFLINEARSUBGRADEREACTIONUNIT.,'(NEWTON)/(METRE^2)');
#2856=IFCDERIVEDUNIT((#2919,#2920),
 .MODULUSOFROTATIONALSUBGRADEREACTIONUNIT.,'(NEWTON)(METRE)');
#2857=IFCDERIVEDUNIT((#2921,#2922),.MODULUSOFSUBGRADEREACTIONUNIT.,
'(NEWTON)/(CUBIC_METRE)');
#2858=IFCDERIVEDUNIT((#2923,#2924),.MOISTUREDIFFUSIVITYUNIT.,
'(CUBIC_METRE)/(SECOND)');
#2859=IFCDERIVEDUNIT((#2925,#2926),.MOLECULARWEIGHTUNIT.,
'(GRAM)/(MOLE)');
#2860=IFCDERIVEDUNIT((#2927,#2928),.PLANARFORCEUNIT.,
'(NEWTON)/(METRE^2)');
#2861=IFCDERIVEDUNIT((#2929),.ROTATIONALFREQUENCYUNIT.,'1/(SECOND)');
#2862=IFCDERIVEDUNIT((#2930,#2931),.ROTATIONALMASSUNIT.,
'(GRAM)(METRE^2)');
#2863=IFCDERIVEDUNIT((#2932,#2933,#2934),.ROTATIONALSTIFFNESSUNIT.,
'(NEWTON)(METRE)/(DEGREE)');
#2864=IFCDERIVEDUNIT((#2935),.SECTIONAREAINTEGRALUNIT.,'(METRE^5)');
#2865=IFCDERIVEDUNIT((#2936),.SECTIONMODULUSUNIT.,'(METRE^3)');
#2866=IFCDERIVEDUNIT((#2937,#2938),.SHEARMODULUSUNIT.,
'(NEWTON)/(SQUARE_METRE)');
#2867=IFCDERIVEDUNIT((#2939,#2940),.SOUNDPOWERUNIT.,'(JOULE)/(SECOND)');
#2868=IFCDERIVEDUNIT((#2941,#2942,#2943),.SPECIFICHEATCAPACITYUNIT.,
'(NEWTON)/(GRAM)(KELVIN)');
#2869=IFCDERIVEDUNIT((#2944,#2945),.TEMPERATUREGRADIENTUNIT.,
'(KELVIN)/(METRE)');
#2870=IFCDERIVEDUNIT((#2946,#2947,#2948),.THERMALADMITTANCEUNIT.,
'(WATT)/(SQUARE_METRE)(KELVIN)');
#2871=IFCDERIVEDUNIT((#2949,#2950,#2951),.THERMALCONDUCTANCEUNIT.,
'(WATT)/(METRE)(KELVIN)');
#2872=IFCDERIVEDUNIT((#2952),.THERMALEXPANSIONCOEFFICIENTUNIT.,
'1/(KELVIN)');
#2873=IFCDERIVEDUNIT((#2953,#2954),.THERMALRESISTANCEUNIT.,
'(SQUARE_METRE)/(WATT)');
#2874=IFCDERIVEDUNIT((#2955,#2956,#2957),.THERMALTRANSMITTANCEUNIT.,
'(WATT)/(SQUARE_METRE)(KELVIN)');
#2875=IFCDERIVEDUNIT((#2958,#2959),.TORQUEUNIT.,'(NEWTON)(METRE)');
#2876=IFCDERIVEDUNIT((#2960,#2961),.VAPORPERMEABILITYUNIT.,
'(GRAM)/(SECOND)');
#2877=IFCDERIVEDUNIT((#2962,#2963),.VOLUMETRICFLOWRATEUNIT.,
'(CUBIC_METRE)/(SECOND)');
#2878=IFCDERIVEDUNIT((#2964),.WARPINGCONSTANTUNIT.,'(METRE^6)');
#2879=IFCDERIVEDUNIT((#2965,#2966),.WARPINGMOMENTUNIT.,
'(NEWTON)(METRE^2)');
#2880=IFCDERIVEDUNITELEMENT(#2970,1);
#2881=IFCDERIVEDUNITELEMENT(#2998,-2);
#2882=IFCDERIVEDUNITELEMENT(#2967,1);
#2883=IFCDERIVEDUNITELEMENT(#2998,-1);
#2884=IFCDERIVEDUNITELEMENT(#2967,1);
#2885=IFCDERIVEDUNITELEMENT(#2970,-1);
#2886=IFCDERIVEDUNITELEMENT(#2994,1);
#2887=IFCDERIVEDUNITELEMENT(#2998,1);
#2888=IFCDERIVEDUNITELEMENT(#2993,1);
#2889=IFCDERIVEDUNITELEMENT(#2970,-2);
#2890=IFCDERIVEDUNITELEMENT(#2982,1);
#2891=IFCDERIVEDUNITELEMENT(#2991,-1);
#2892=IFCDERIVEDUNITELEMENT(#2998,-1);
#2893=IFCDERIVEDUNITELEMENT(#2991,1);
#2894=IFCDERIVEDUNITELEMENT(#2972,-1);
#2895=IFCDERIVEDUNITELEMENT(#2972,1);
#2896=IFCDERIVEDUNITELEMENT(#2991,-1);
#2897=IFCDERIVEDUNITELEMENT(#2971,1);
#2898=IFCDERIVEDUNITELEMENT(#2998,-1);
#2899=IFCDERIVEDUNITELEMENT(#2983,1);
#2900=IFCDERIVEDUNITELEMENT(#2970,-1);
#2901=IFCDERIVEDUNITELEMENT(#2983,1);
#2902=IFCDERIVEDUNITELEMENT(#2970,-1);
#2903=IFCDERIVEDUNITELEMENT(#2983,1);
#2904=IFCDERIVEDUNITELEMENT(#2970,-1);
#2905=IFCDERIVEDUNITELEMENT(#2970,1);
#2906=IFCDERIVEDUNITELEMENT(#2998,-1);
#2907=IFCDERIVEDUNITELEMENT(#2988,1);
#2908=IFCDERIVEDUNITELEMENT(#2987,-1);
#2909=IFCDERIVEDUNITELEMENT(#2991,1);
#2910=IFCDERIVEDUNITELEMENT(#2972,-1);
#2911=IFCDERIVEDUNITELEMENT(#2991,1);
#2912=IFCDERIVEDUNITELEMENT(#2998,-1);
#2913=IFCDERIVEDUNITELEMENT(#2991,1);
#2914=IFCDERIVEDUNITELEMENT(#2970,-1);
#2915=IFCDERIVEDUNITELEMENT(#2983,1);
#2916=IFCDERIVEDUNITELEMENT(#2971,-1);
#2917=IFCDERIVEDUNITELEMENT(#2983,1);
#2918=IFCDERIVEDUNITELEMENT(#2970,-2);
#2919=IFCDERIVEDUNITELEMENT(#2983,1);
#2920=IFCDERIVEDUNITELEMENT(#2970,1);
#2921=IFCDERIVEDUNITELEMENT(#2983,1);
#2922=IFCDERIVEDUNITELEMENT(#2972,-1);
#2923=IFCDERIVEDUNITELEMENT(#2972,1);
#2924=IFCDERIVEDUNITELEMENT(#2998,-1);
#2925=IFCDERIVEDUNITELEMENT(#2991,1);
#2926=IFCDERIVEDUNITELEMENT(#2974,-1);
#2927=IFCDERIVEDUNITELEMENT(#2983,1);
#2928=IFCDERIVEDUNITELEMENT(#2970,-2);
#2929=IFCDERIVEDUNITELEMENT(#2998,-1);
#2930=IFCDERIVEDUNITELEMENT(#2991,1);
#2931=IFCDERIVEDUNITELEMENT(#2970,2);
#2932=IFCDERIVEDUNITELEMENT(#2983,1);
#2933=IFCDERIVEDUNITELEMENT(#2970,1);
#2934=IFCDERIVEDUNITELEMENT(#2967,-1);
#2935=IFCDERIVEDUNITELEMENT(#2970,5);
#2936=IFCDERIVEDUNITELEMENT(#2970,3);
#2937=IFCDERIVEDUNITELEMENT(#2983,1);
#2938=IFCDERIVEDUNITELEMENT(#2971,-1);
#2939=IFCDERIVEDUNITELEMENT(#2982,1);
#2940=IFCDERIVEDUNITELEMENT(#2998,-1);
#2941=IFCDERIVEDUNITELEMENT(#2983,1);
#2942=IFCDERIVEDUNITELEMENT(#2991,-1);
#2943=IFCDERIVEDUNITELEMENT(#2997,-1);
#2944=IFCDERIVEDUNITELEMENT(#2997,1);
#2945=IFCDERIVEDUNITELEMENT(#2970,-1);
#2946=IFCDERIVEDUNITELEMENT(#2993,1);
#2947=IFCDERIVEDUNITELEMENT(#2971,-1);
#2948=IFCDERIVEDUNITELEMENT(#2997,-1);
#2949=IFCDERIVEDUNITELEMENT(#2993,1);
#2950=IFCDERIVEDUNITELEMENT(#2970,-1);
#2951=IFCDERIVEDUNITELEMENT(#2997,-1);
#2952=IFCDERIVEDUNITELEMENT(#2997,-1);
#2953=IFCDERIVEDUNITELEMENT(#2971,1);
#2954=IFCDERIVEDUNITELEMENT(#2993,-1);
#2955=IFCDERIVEDUNITELEMENT(#2993,1);
#2956=IFCDERIVEDUNITELEMENT(#2971,-1);
#2957=IFCDERIVEDUNITELEMENT(#2997,-1);
#2958=IFCDERIVEDUNITELEMENT(#2983,1);
#2959=IFCDERIVEDUNITELEMENT(#2970,1);
#2960=IFCDERIVEDUNITELEMENT(#2991,1);
#2961=IFCDERIVEDUNITELEMENT(#2998,-1);
#2962=IFCDERIVEDUNITELEMENT(#2972,1);
#2963=IFCDERIVEDUNITELEMENT(#2998,-1);
#2964=IFCDERIVEDUNITELEMENT(#2970,6);
#2965=IFCDERIVEDUNITELEMENT(#2983,1);
#2966=IFCDERIVEDUNITELEMENT(#2970,2);
#2967=IFCCONVERSIONBASEDUNIT(#2968,.PLANEANGLEUNIT.,'degree',#2969);
#2968=IFCDIMENSIONALEXPONENTS(0,0,0,0,0,0,0);
#2969=IFCMEASUREWITHUNIT(IFCREAL(0.0174532925199433),#2992);
#2970=IFCSIUNIT(*,.LENGTHUNIT.,$,.METRE.);
#2971=IFCSIUNIT(*,.AREAUNIT.,$,.SQUARE_METRE.);
#2972=IFCSIUNIT(*,.VOLUMEUNIT.,$,.CUBIC_METRE.);
#2973=IFCSIUNIT(*,.ABSORBEDDOSEUNIT.,$,.GRAY.);
#2974=IFCSIUNIT(*,.AMOUNTOFSUBSTANCEUNIT.,$,.MOLE.);
#2975=IFCSIUNIT(*,.DOSEEQUIVALENTUNIT.,$,.SIEVERT.);
#2976=IFCSIUNIT(*,.ELECTRICCAPACITANCEUNIT.,$,.FARAD.);
#2977=IFCSIUNIT(*,.ELECTRICCHARGEUNIT.,$,.COULOMB.);
#2978=IFCSIUNIT(*,.ELECTRICCONDUCTANCEUNIT.,$,.SIEMENS.);
#2979=IFCSIUNIT(*,.ELECTRICCURRENTUNIT.,$,.AMPERE.);
#2980=IFCSIUNIT(*,.ELECTRICRESISTANCEUNIT.,$,.OHM.);
#2981=IFCSIUNIT(*,.ELECTRICVOLTAGEUNIT.,$,.VOLT.);
#2982=IFCSIUNIT(*,.ENERGYUNIT.,$,.JOULE.);
#2983=IFCSIUNIT(*,.FORCEUNIT.,$,.NEWTON.);
#2984=IFCSIUNIT(*,.FREQUENCYUNIT.,$,.HERTZ.);
#2985=IFCSIUNIT(*,.ILLUMINANCEUNIT.,$,.LUX.);
#2986=IFCSIUNIT(*,.INDUCTANCEUNIT.,$,.HENRY.);
#2987=IFCSIUNIT(*,.LUMINOUSFLUXUNIT.,$,.LUMEN.);
#2988=IFCSIUNIT(*,.LUMINOUSINTENSITYUNIT.,$,.CANDELA.);
#2989=IFCSIUNIT(*,.MAGNETICFLUXDENSITYUNIT.,$,.TESLA.);
#2990=IFCSIUNIT(*,.MAGNETICFLUXUNIT.,$,.WEBER.);
#2991=IFCSIUNIT(*,.MASSUNIT.,$,.GRAM.);
#2992=IFCSIUNIT(*,.PLANEANGLEUNIT.,$,.RADIAN.);
#2993=IFCSIUNIT(*,.POWERUNIT.,$,.WATT.);
#2994=IFCSIUNIT(*,.PRESSUREUNIT.,$,.PASCAL.);
#2995=IFCSIUNIT(*,.RADIOACTIVITYUNIT.,$,.BECQUEREL.);
#2996=IFCSIUNIT(*,.SOLIDANGLEUNIT.,$,.STERADIAN.);
#2997=IFCSIUNIT(*,.THERMODYNAMICTEMPERATUREUNIT.,$,.KELVIN.);
#2998=IFCSIUNIT(*,.TIMEUNIT.,$,.SECOND.);
ENDSEC;
END-ISO-10303-21;

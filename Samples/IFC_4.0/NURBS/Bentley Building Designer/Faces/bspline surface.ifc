ISO-10303-21;
HEADER;
/* Generated by software containing ST-Developer
 * from STEP Tools, Inc. (www.steptools.com) 
 */

FILE_DESCRIPTION(
/* description */ ('ViewDefinition [DesignTransferView]',
'Comment [Comments]',
'Comment [System: OpenBuildings Designer*********** debug build Feb  5
 2020 00:34:15]'),
/* implementation_level */ '2;1');

FILE_NAME(
/* name */ 'bspline surface',
/* time_stamp */ '2020-03-05T12:31:09+03:00',
/* author */ ('First Name Last Name'),
/* organization */ ('Organization Name'),
/* preprocessor_version */ 'ST-DEVELOPER v16.13',
/* originating_system */ 'OpenBuildings Designer***********',
/* authorisation */ 'Administrator');

FILE_SCHEMA (('IFC4'));
ENDSEC;

DATA;
#10=IFCRELDEFINESBYTYPE('0yWWbT7y5ah7KlNAaQnrO3',#7147,
'*Default Exterior Wall*',$,(#21),#20);
#11=IFCRELASSOCIATESCLASSIFICATION('1X0TV8c4moj7_JQXHMcGNY',#7147,
'B2010 ! 2010 Edition UniFormat - Levels One through Three',$,(#20),#12);
#12=IFCCLASSIFICATIONREFERENCE($,'B2010','Exterior Walls',#13,$,$);
#13=IFCCLASSIFICATION('The Construction Specifications Institute (CSI)',
$,$,'2010 Edition UniFormat - Levels One through Three',$,$,$);
#14=IFCELEMENTQUANTITY('0KSv2R_$a6jLerxfR$Wk_M',#7147,
'Wall_Application',$,$,(#16,#17));
#15=IFCELEMENTQUANTITY('3cU$dMMpPy0ae7XJISsXg4',#7147,
'Qto_WallBaseQuantities',$,$,(#18,#19));
#16=IFCQUANTITYLENGTH('Width',$,$,0.3048,$);
#17=IFCQUANTITYLENGTH('Height',$,$,4.2672,$);
#18=IFCQUANTITYLENGTH('Height',$,$,4.2672,$);
#19=IFCQUANTITYLENGTH('Width',$,$,0.3048,$);
#20=IFCWALLTYPE('0NS3N_lmg12Qd8HyEBTJVt',#7147,
'*Default Exterior Wall*',$,$,(#7084,#7085,#7086,#7087,#7088,#7089,#7090,
#14,#7091),$,$,$,.NOTDEFINED.);
#21=IFCWALL('3wca1no693WBlKP7jj9bSB',#7147,'*Default Exterior Wall*',
'Default exterior wall','Wall',#5354,#91,'651*bspline surface!Default',
 .NOTDEFINED.);
#22=IFCADVANCEDBREP(#23);
#23=IFCCLOSEDSHELL((#5332));
#24=IFCFACEBOUND(#889,.T.);
#25=IFCFACEBOUND(#892,.T.);
#26=IFCFACEBOUND(#893,.T.);
#27=IFCFACEBOUND(#894,.T.);
#28=IFCFACEBOUND(#895,.T.);
#29=IFCFACEBOUND(#896,.T.);
#30=IFCFACEBOUND(#898,.T.);
#31=IFCRATIONALBSPLINECURVEWITHKNOTS(3,(#5446,#5470,#5494,#5518,#5542),
 .UNSPECIFIED.,.F.,.U.,(4,1,4),(0.,0.431315603064826,1.),.UNSPECIFIED.,
(1.,1.,1.,1.,1.));
#32=IFCRATIONALBSPLINECURVEWITHKNOTS(2,(#5542,#5543,#5542,#5543,#5542,#5543,
#5542,#5543,#5542,#5543,#5542,#5543,#5542,#5543,#5542,#5543,#5542,#5543,
#5542,#5543,#5542,#5543,#5542,#5543,#5542),.UNSPECIFIED.,.T.,.U.,(3,2,2,
2,2,2,2,2,2,2,2,2,3),(0.,0.0833333333333333,0.166666666666667,0.25,0.333333333333333,
0.416666666666667,0.5,0.583333333333333,0.666666666666667,0.75,0.833333333333333,
0.916666666666667,1.),.UNSPECIFIED.,(1.,0.965925826289068,1.,0.965925826289068,
1.,0.965925826289068,1.,0.965925826289068,1.,0.965925826289068,1.,0.965925826289068,
1.,0.965925826289068,1.,0.965925826289068,1.,0.965925826289068,1.,0.965925826289068,
1.,0.965925826289068,1.,0.965925826289068,1.));
#33=IFCRATIONALBSPLINECURVEWITHKNOTS(3,(#5542,#5518,#5494,#5470,#5446),
 .UNSPECIFIED.,.F.,.U.,(4,1,4),(0.,0.568684396935174,1.),.UNSPECIFIED.,
(1.,1.,1.,1.,1.));
#34=IFCRATIONALBSPLINECURVEWITHKNOTS(2,(#5446,#5544,#5468,#5545,#5466,#5546,
#5464,#5547,#5462,#5548,#5460,#5549,#5458,#5550,#5456,#5551,#5454,#5552,
#5452,#5553,#5450,#5554,#5448,#5555,#5446),.UNSPECIFIED.,.T.,.U.,(3,2,2,
2,2,2,2,2,2,2,2,2,3),(0.,0.0833333333333334,0.166666666666667,0.25,0.333333333333333,
0.416666666666667,0.5,0.583333333333333,0.666666666666667,0.75,0.833333333333333,
0.916666666666667,1.),.UNSPECIFIED.,(1.,0.965925826289068,1.,0.965925826289068,
1.,0.965925826289068,1.,0.965925826289068,1.,0.965925826289068,1.,0.965925826289068,
1.,0.965925826289068,1.,0.965925826289068,1.,0.965925826289068,1.,0.965925826289068,
1.,0.965925826289068,1.,0.965925826289068,1.));
#35=IFCRATIONALBSPLINECURVEWITHKNOTS(3,(#5726,#5727,#5728,#5729,#5730,#5731,
#5732,#5733,#5734,#5735,#5736,#5737,#5702,#5738),.UNSPECIFIED.,.F.,.U.,
(4,1,1,1,1,1,1,1,1,1,1,4),(0.,0.0993606295555905,0.198721259111181,0.298081888666771,
0.395269366816157,0.397442518222362,0.490283693559337,0.583124868896311,
0.675966044233286,0.768807219570261,0.814924421267849,1.),.UNSPECIFIED.,
(0.9464100135514,0.964212150059891,0.938148108823264,0.948623475337139,
0.945838523798468,0.946310130827648,0.946499329997145,0.946439092636272,
0.946175307343153,0.947319759299513,0.943729439636988,0.956494077025525,
1.,0.9464100135514));
#36=IFCRATIONALBSPLINECURVEWITHKNOTS(3,(#5738,#5741,#5742,#5743,#5744,#5745,
#5720,#5746,#5747,#5748,#5749,#5750,#5738),.UNSPECIFIED.,.T.,.U.,(4,3,3,
3,4),(0.,0.330599351423307,0.38042684413095,0.676646398900103,1.),
 .UNSPECIFIED.,(0.9464100135514,0.686018107829307,0.7258444249798,0.934957569596847,
0.934467225692252,0.934042030735226,1.,0.75332390362769,0.776972380142488,
0.938914730714086,0.701936787513397,0.678288310998599,0.9464100135514));
#37=IFCRATIONALBSPLINECURVEWITHKNOTS(3,(#5738,#5702,#5737,#5736,#5735,#5734,
#5733,#5732,#5731,#5730,#5729,#5728,#5727,#5726),.UNSPECIFIED.,.F.,.U.,
(4,1,1,1,1,1,1,1,1,1,1,4),(0.,0.185075578732151,0.231192780429739,0.324033955766714,
0.416875131103689,0.509716306440663,0.602557481777638,0.604730633183843,
0.701918111333229,0.801278740888819,0.90063937044441,1.),.UNSPECIFIED.,
(0.9464100135514,1.,0.956494077025525,0.943729439636988,0.947319759299513,
0.946175307343153,0.946439092636272,0.946499329997145,0.946310130827648,
0.945838523798468,0.948623475337138,0.938148108823264,0.964212150059891,
0.9464100135514));
#38=IFCRATIONALBSPLINECURVEWITHKNOTS(3,(#5726,#5751,#5752,#5753,#5754,#5755,
#5564,#5756,#5757,#5758,#5759,#5760,#5726),.UNSPECIFIED.,.T.,.U.,(4,3,3,
3,4),(0.,0.323353601099897,0.61957315586905,0.669400648576693,1.),
 .UNSPECIFIED.,(0.9464100135514,0.678288310998599,0.701936787513397,0.938914730714086,
0.776972380142488,0.75332390362769,1.,0.934042030735226,0.934467225692252,
0.934957569596847,0.7258444249798,0.686018107829307,0.9464100135514));
#39=IFCRATIONALBSPLINECURVEWITHKNOTS(2,(#5763,#5770,#5763,#5770,#5763,#5770,
#5763),.UNSPECIFIED.,.T.,.U.,(3,2,2,3),(0.,0.333333333333333,0.666666666666667,
1.),.UNSPECIFIED.,(1.,0.5,1.,0.5,1.,0.5,1.));
#40=IFCRATIONALBSPLINECURVEWITHKNOTS(1,(#5763,#5764),.UNSPECIFIED.,.F.,
 .U.,(2,2),(0.,1.),.UNSPECIFIED.,(1.,1.));
#41=IFCRATIONALBSPLINECURVEWITHKNOTS(2,(#5764,#5771,#5768,#5772,#5766,#5773,
#5764),.UNSPECIFIED.,.T.,.U.,(3,2,2,3),(0.,0.333333333333333,0.666666666666667,
1.),.UNSPECIFIED.,(1.,0.5,1.,0.5,1.,0.5,1.));
#42=IFCRATIONALBSPLINECURVEWITHKNOTS(1,(#5764,#5763),.UNSPECIFIED.,.F.,
 .U.,(2,2),(0.,1.),.UNSPECIFIED.,(1.,1.));
#43=IFCRATIONALBSPLINECURVEWITHKNOTS(3,(#7012,#7018,#7024,#7030,#7036,#7042,
#7048,#7012),.UNSPECIFIED.,.T.,.U.,(4,1,1,1,1,4),(0.,0.2,0.4,0.6,0.8,1.),
 .UNSPECIFIED.,(1.,1.,1.,1.,1.,1.,1.,1.));
#44=IFCRATIONALBSPLINECURVEWITHKNOTS(2,(#7012,#7054,#7014,#7055,#7016,#7056,
#7012),.UNSPECIFIED.,.T.,.U.,(3,2,2,3),(0.,0.333333333333333,0.666666666666667,
1.),.UNSPECIFIED.,(1.,0.5,1.,0.5,1.,0.5,1.));
#45=IFCRATIONALBSPLINECURVEWITHKNOTS(3,(#7012,#7048,#7042,#7036,#7030,#7024,
#7018,#7012),.UNSPECIFIED.,.T.,.U.,(4,1,1,1,1,4),(0.,0.2,0.4,0.6,0.8,1.),
 .UNSPECIFIED.,(1.,1.,1.,1.,1.,1.,1.,1.));
#46=IFCRATIONALBSPLINECURVEWITHKNOTS(2,(#7012,#7056,#7016,#7055,#7014,#7054,
#7012),.UNSPECIFIED.,.T.,.U.,(3,2,2,3),(0.,0.333333333333333,0.666666666666667,
1.),.UNSPECIFIED.,(1.,0.5,1.,0.5,1.,0.5,1.));
#47=IFCRATIONALBSPLINESURFACEWITHKNOTS(3,2,((#5446,#5447,#5448,#5449,#5450,
#5451,#5452,#5453,#5454,#5455,#5456,#5457,#5458,#5459,#5460,#5461,#5462,
#5463,#5464,#5465,#5466,#5467,#5468,#5469,#5446),(#5470,#5471,#5472,#5473,
#5474,#5475,#5476,#5477,#5478,#5479,#5480,#5481,#5482,#5483,#5484,#5485,
#5486,#5487,#5488,#5489,#5490,#5491,#5492,#5493,#5470),(#5494,#5495,#5496,
#5497,#5498,#5499,#5500,#5501,#5502,#5503,#5504,#5505,#5506,#5507,#5508,
#5509,#5510,#5511,#5512,#5513,#5514,#5515,#5516,#5517,#5494),(#5518,#5519,
#5520,#5521,#5522,#5523,#5524,#5525,#5526,#5527,#5528,#5529,#5530,#5531,
#5532,#5533,#5534,#5535,#5536,#5537,#5538,#5539,#5540,#5541,#5518),(#5542,
#5542,#5542,#5542,#5542,#5542,#5542,#5542,#5542,#5542,#5542,#5542,#5542,
#5542,#5542,#5542,#5542,#5542,#5542,#5542,#5542,#5542,#5542,#5542,#5542)),
 .UNSPECIFIED.,.F.,.T.,.U.,(4,1,4),(3,2,2,2,2,2,2,2,2,2,2,2,3),(0.,0.431315603064826,
1.),(0.,0.0833333333333333,0.166666666666667,0.25,0.333333333333333,0.416666666666667,
0.5,0.583333333333333,0.666666666666667,0.75,0.833333333333333,0.916666666666667,
1.),.UNSPECIFIED.,((1.,0.965925826289068,1.,0.965925826289068,1.,0.965925826289068,
1.,0.965925826289068,1.,0.965925826289068,1.,0.965925826289068,1.,0.965925826289068,
1.,0.965925826289068,1.,0.965925826289068,1.,0.965925826289068,1.,0.965925826289068,
1.,0.965925826289068,1.),(1.,0.965925826289068,1.,0.965925826289068,1.,
0.965925826289068,1.,0.965925826289068,1.,0.965925826289068,1.,0.965925826289068,
1.,0.965925826289068,1.,0.965925826289068,1.,0.965925826289068,1.,0.965925826289068,
1.,0.965925826289068,1.,0.965925826289068,1.),(1.,0.965925826289068,1.,
0.965925826289068,1.,0.965925826289068,1.,0.965925826289068,1.,0.965925826289068,
1.,0.965925826289068,1.,0.965925826289068,1.,0.965925826289068,1.,0.965925826289068,
1.,0.965925826289068,1.,0.965925826289068,1.,0.965925826289068,1.),(1.,
0.965925826289068,1.,0.965925826289068,1.,0.965925826289068,1.,0.965925826289068,
1.,0.965925826289068,1.,0.965925826289068,1.,0.965925826289068,1.,0.965925826289068,
1.,0.965925826289068,1.,0.965925826289068,1.,0.965925826289068,1.,0.965925826289068,
1.),(1.,0.965925826289068,1.,0.965925826289068,1.,0.965925826289068,1.,
0.965925826289068,1.,0.965925826289068,1.,0.965925826289068,1.,0.965925826289068,
1.,0.965925826289068,1.,0.965925826289068,1.,0.965925826289068,1.,0.965925826289068,
1.,0.965925826289068,1.)));
#48=IFCRATIONALBSPLINESURFACEWITHKNOTS(3,3,((#5558,#5559,#5560,#5561,#5562,
#5563,#5564,#5565,#5566,#5567,#5568,#5569,#5558),(#5570,#5571,#5572,#5573,
#5574,#5575,#5576,#5577,#5578,#5579,#5580,#5581,#5570),(#5582,#5583,#5584,
#5585,#5586,#5587,#5588,#5589,#5590,#5591,#5592,#5593,#5582),(#5594,#5595,
#5596,#5597,#5598,#5599,#5600,#5601,#5602,#5603,#5604,#5605,#5594),(#5606,
#5607,#5608,#5609,#5610,#5611,#5612,#5613,#5614,#5615,#5616,#5617,#5606),
(#5618,#5619,#5620,#5621,#5622,#5623,#5624,#5625,#5626,#5627,#5628,#5629,
#5618),(#5630,#5631,#5632,#5633,#5634,#5635,#5636,#5637,#5638,#5639,#5640,
#5641,#5630),(#5642,#5643,#5644,#5645,#5646,#5647,#5648,#5649,#5650,#5651,
#5652,#5653,#5642),(#5654,#5655,#5656,#5657,#5658,#5659,#5660,#5661,#5662,
#5663,#5664,#5665,#5654),(#5666,#5667,#5668,#5669,#5670,#5671,#5672,#5673,
#5674,#5675,#5676,#5677,#5666),(#5678,#5679,#5680,#5681,#5682,#5683,#5684,
#5685,#5686,#5687,#5688,#5689,#5678),(#5690,#5691,#5692,#5693,#5694,#5695,
#5696,#5697,#5698,#5699,#5700,#5701,#5690),(#5702,#5703,#5704,#5705,#5706,
#5707,#5708,#5709,#5710,#5711,#5712,#5713,#5702),(#5714,#5715,#5716,#5717,
#5718,#5719,#5720,#5721,#5722,#5723,#5724,#5725,#5714)),.UNSPECIFIED.,.F.,
 .T.,.U.,(4,1,1,1,1,1,1,1,1,1,1,4),(4,3,3,3,4),(0.,0.0993606295555905,0.198721259111181,
0.298081888666771,0.395269366816157,0.397442518222362,0.490283693559337,
0.583124868896311,0.675966044233286,0.768807219570261,0.814924421267849,
1.),(0.,0.330599351423307,0.38042684413095,0.676646398900103,1.),
 .UNSPECIFIED.,((0.9464100135514,0.686018107829307,0.7258444249798,0.934957569596847,
0.934467225692252,0.934042030735226,1.,0.75332390362769,0.776972380142488,
0.938914730714086,0.701936787513397,0.678288310998599,0.9464100135514),
(0.964212150059891,0.700107392723648,0.739933709874142,0.954548770134187,
0.954134128703842,0.953770086574313,1.,0.768842807536956,0.793251969620159,
0.957907734416511,0.71542811539213,0.691779638877331,0.964212150059891),
(0.938148108823264,0.679479342969697,0.719305660120191,0.925865415504696,
0.925339940465688,0.924886366639885,1.,0.746121674261515,0.76941711945947,
0.930100180275736,0.695675524782946,0.672027048268148,0.938148108823264),
(0.948623475337139,0.687769857496429,0.727596174646922,0.937393308113364,
0.93691236946784,0.936494771310287,1.,0.755253350545403,0.778996408316548,
0.94127615169467,0.703614215251877,0.679965738737079,0.948623475337139),
(0.945838523798468,0.685566058701203,0.725392375851697,0.934329271880583,
0.933836524795205,0.933409391534782,1.,0.752826170513806,0.776450227262621,
0.938305414301814,0.701503837884483,0.677855361369685,0.945838523798468),
(0.946310130827648,0.685938523138846,0.72576484028934,0.934846311697335,
0.934355485197103,0.933929893984335,1.,0.753235848335541,0.776880056860009,
0.938807304441582,0.701860763798234,0.678212287283435,0.946310130827648),
(0.946499329997145,0.686089272111881,0.725915589262374,0.935057054379878,
0.934567141867474,0.934142301108667,1.,0.75340264125931,0.777054934267217,
0.939010790447944,0.702004768063358,0.678356291548559,0.946499329997145),
(0.946439092636272,0.68604405944917,0.725870376599664,0.934996930577619,
0.934507028710518,0.934082226125445,1.,0.753354663833285,0.77700438289783,
0.938950498671166,0.701960628626697,0.678312152111898,0.946439092636272),
(0.946175307343153,0.685821315280111,0.725647632430604,0.934671622276515,
0.934179083967684,0.933751983749091,1.,0.753098968979896,0.776737416494785,
0.938646499465182,0.701752138713144,0.678103662198346,0.946175307343153),
(0.947319759299513,0.686779326406226,0.72660564355672,0.936061997897398,
0.935579989572255,0.935162023289541,1.,0.754192882013268,0.777880231977957,
0.939951887752619,0.702651541601111,0.679003065086312,0.947319759299513),
(0.943729439636988,0.683775507611805,0.723601824762298,0.93170420634923,
0.931189338121228,0.930742877374563,1.,0.750764072005746,0.774298007555714,
0.93585928265296,0.69983096522128,0.676182488706482,0.943729439636988),
(0.956494077025525,0.694454516558345,0.734280833708839,0.947196313064622,
0.946798223049408,0.946453024036334,1.,0.762953673427009,0.787033042130055,
0.950408971261437,0.709858665959078,0.686210189444279,0.956494077025525),
(1.,0.730852630629027,0.77067894777952,1.,1.,1.,1.,0.804500931067691,0.830439308532467,
1.,0.744036655917829,0.720388179403031,1.),(0.9464100135514,0.686018107829307,
0.7258444249798,0.934957569596847,0.934467225692252,0.934042030735226,1.,
0.75332390362769,0.776972380142488,0.938914730714086,0.701936787513397,
0.678288310998599,0.9464100135514)));
#49=IFCRATIONALBSPLINESURFACEWITHKNOTS(2,1,((#5763,#5764),(#5763,#5765),
(#5763,#5766),(#5763,#5767),(#5763,#5768),(#5763,#5769),(#5763,#5764)),
 .UNSPECIFIED.,.T.,.F.,.U.,(3,2,2,3),(2,2),(0.,0.333333333333333,0.666666666666667,
1.),(0.,1.),.UNSPECIFIED.,((1.,1.),(0.5,0.5),(1.,1.),(0.5,0.5),(1.,1.),
(0.5,0.5),(1.,1.)));
#50=IFCRATIONALBSPLINESURFACEWITHKNOTS(3,2,((#7012,#7013,#7014,#7015,#7016,
#7017,#7012),(#7018,#7019,#7020,#7021,#7022,#7023,#7018),(#7024,#7025,#7026,
#7027,#7028,#7029,#7024),(#7030,#7031,#7032,#7033,#7034,#7035,#7030),(#7036,
#7037,#7038,#7039,#7040,#7041,#7036),(#7042,#7043,#7044,#7045,#7046,#7047,
#7042),(#7048,#7049,#7050,#7051,#7052,#7053,#7048),(#7012,#7013,#7014,#7015,
#7016,#7017,#7012)),.UNSPECIFIED.,.T.,.T.,.U.,(4,1,1,1,1,4),(3,2,2,3),(0.,
0.2,0.4,0.6,0.8,1.),(0.,0.333333333333333,0.666666666666667,1.),
 .UNSPECIFIED.,((1.,0.5,1.,0.5,1.,0.5,1.),(1.,0.5,1.,0.5,1.,0.5,1.),(1.,
0.5,1.,0.5,1.,0.5,1.),(1.,0.5,1.,0.5,1.,0.5,1.),(1.,0.5,1.,0.5,1.,0.5,1.),
(1.,0.5,1.,0.5,1.,0.5,1.),(1.,0.5,1.,0.5,1.,0.5,1.),(1.,0.5,1.,0.5,1.,0.5,
1.)));
#51=IFCBOUNDINGBOX(#5424,15.9394597119113,13.8039773477272,2.34830647218089E-6);
#52=IFCBOUNDINGBOX(#5557,12.0594231234222,12.0594231234223,5.86758330849535);
#53=IFCBOUNDINGBOX(#5762,9.16926879363474,11.0586828533057,18.0438142752129);
#54=IFCBOUNDINGBOX(#5775,8.98560000000003,8.98560000000008,13.4784);
#55=IFCBOUNDINGBOX(#6631,12.0000023283064,12.4488640625344,16.1872575582261);
#56=IFCBOUNDINGBOX(#6858,15.0000000000001,15.0114148312504,3.33239964536385);
#57=IFCBOUNDINGBOX(#6961,15.0000023283064,7.5000023383064,7.50000232830644);
#58=IFCBOUNDINGBOX(#7011,15.0000023283065,15.0000023283064,2.32830648681879E-6);
#59=IFCBOUNDINGBOX(#7058,6.9646452680501,23.4969018714794,27.1318852412417);
#60=IFCGEOMETRICREPRESENTATIONSUBCONTEXT('Body','Model',*,*,*,*,#62,1.,
 .MODEL_VIEW.,$);
#61=IFCGEOMETRICREPRESENTATIONSUBCONTEXT('Box','Model',*,*,*,*,#62,1.,
 .SKETCH_VIEW.,$);
#62=IFCGEOMETRICREPRESENTATIONCONTEXT($,'Model',3,1.E-6,#5359,#7061);
#63=IFCSHAPEREPRESENTATION(#60,'Body','SurfaceModel',(#122));
#64=IFCSHAPEREPRESENTATION(#61,'Box','BoundingBox',(#51));
#65=IFCSHAPEREPRESENTATION(#60,'Body','SurfaceModel',(#123));
#66=IFCSHAPEREPRESENTATION(#60,'Body','SurfaceModel',(#124));
#67=IFCSHAPEREPRESENTATION(#61,'Box','BoundingBox',(#52));
#68=IFCSHAPEREPRESENTATION(#60,'Body','SurfaceModel',(#125));
#69=IFCSHAPEREPRESENTATION(#61,'Box','BoundingBox',(#53));
#70=IFCSHAPEREPRESENTATION(#60,'Body','SurfaceModel',(#126));
#71=IFCSHAPEREPRESENTATION(#61,'Box','BoundingBox',(#54));
#72=IFCSHAPEREPRESENTATION(#60,'Body','SurfaceModel',(#127));
#73=IFCSHAPEREPRESENTATION(#61,'Box','BoundingBox',(#55));
#74=IFCSHAPEREPRESENTATION(#60,'Body','SurfaceModel',(#128));
#75=IFCSHAPEREPRESENTATION(#61,'Box','BoundingBox',(#56));
#76=IFCSHAPEREPRESENTATION(#60,'Body','SurfaceModel',(#129));
#77=IFCSHAPEREPRESENTATION(#61,'Box','BoundingBox',(#57));
#78=IFCSHAPEREPRESENTATION(#60,'Body','SurfaceModel',(#130));
#79=IFCSHAPEREPRESENTATION(#61,'Box','BoundingBox',(#58));
#80=IFCSHAPEREPRESENTATION(#60,'Body','AdvancedBrep',(#22));
#81=IFCSHAPEREPRESENTATION(#61,'Box','BoundingBox',(#59));
#82=IFCPRODUCTDEFINITIONSHAPE($,$,(#63,#64));
#83=IFCPRODUCTDEFINITIONSHAPE($,$,(#65));
#84=IFCPRODUCTDEFINITIONSHAPE($,$,(#66,#67));
#85=IFCPRODUCTDEFINITIONSHAPE($,$,(#68,#69));
#86=IFCPRODUCTDEFINITIONSHAPE($,$,(#70,#71));
#87=IFCPRODUCTDEFINITIONSHAPE($,$,(#72,#73));
#88=IFCPRODUCTDEFINITIONSHAPE($,$,(#74,#75));
#89=IFCPRODUCTDEFINITIONSHAPE($,$,(#76,#77));
#90=IFCPRODUCTDEFINITIONSHAPE($,$,(#78,#79));
#91=IFCPRODUCTDEFINITIONSHAPE($,$,(#80,#81));
#92=IFCRELCONTAINEDINSPATIALSTRUCTURE('2aJi0CW_wcTJthPGyCEMkK',#7147,$,
$,(#95,#96,#97,#98,#99,#100,#101,#102,#103,#21),#93);
#93=IFCBUILDINGSTOREY('0rm7DUqMJhOUcs5v8t1F$L',#7147,'Floor 3',
'Administrative and employee lounge',$,#5344,$,'Floor 3',.ELEMENT.,9.144);
#94=IFCBUILDING('3_IkJInE13hR6Xwo4VKOgh',#7147,'Bldg 1',
'3 Story Building',$,#5343,$,'Bldg 1',.ELEMENT.,0.,0.,#7139);
#95=IFCBUILDINGELEMENTPROXY('3aSi40O_0G89We9uslOUWT',#7147,
'Element type: 24','636*bspline surface!Default','',#5345,#82,
'636*bspline surface!Default',.NOTDEFINED.);
#96=IFCBUILDINGELEMENTPROXY('0Aq8869pigt7uFQjBvubFh',#7147,
'Element type: 24','667*bspline surface!Default','',#5346,#83,
'667*bspline surface!Default',.NOTDEFINED.);
#97=IFCBUILDINGELEMENTPROXY('1tA$FB6IgKmuErQnyukDAc',#7147,
'Element type: 24','700*bspline surface!Default','',#5347,#84,
'700*bspline surface!Default',.NOTDEFINED.);
#98=IFCBUILDINGELEMENTPROXY('0Nhc0TstR8GhZh5RveMuCl',#7147,
'Element type: 24','752*bspline surface!Default','',#5348,#85,
'752*bspline surface!Default',.NOTDEFINED.);
#99=IFCBUILDINGELEMENTPROXY('2I0Vc5PdWui6aZ1Sc_tP24',#7147,
'Element type: 24','788*bspline surface!Default','',#5349,#86,
'788*bspline surface!Default',.NOTDEFINED.);
#100=IFCBUILDINGELEMENTPROXY('1vdpKn9biZ$o4qW2SHbk1Q',#7147,
'Element type: 24','824*bspline surface!Default','',#5350,#87,
'824*bspline surface!Default',.NOTDEFINED.);
#101=IFCBUILDINGELEMENTPROXY('1SkVtlXVM5QZmO_SLYA8R9',#7147,
'Element type: 24','1157*bspline surface!Default','',#5351,#88,
'1157*bspline surface!Default',.NOTDEFINED.);
#102=IFCBUILDINGELEMENTPROXY('0jN7$mxA0Mb1eStqg04F5j',#7147,
'Element type: 24','1204*bspline surface!Default','',#5352,#89,
'1204*bspline surface!Default',.NOTDEFINED.);
#103=IFCBUILDINGELEMENTPROXY('3Js5Z1cBieWAPNA4TNWLaA',#7147,
'Element type: 24','1214*bspline surface!Default','',#5353,#90,
'1214*bspline surface!Default',.NOTDEFINED.);
#104=IFCPRESENTATIONLAYERWITHSTYLE('Default',$,(#122,#123,#127,#128,#129,
#130,#22),$,.U.,.U.,.U.,(#111));
#105=IFCPRESENTATIONLAYERWITHSTYLE('Elements',$,(#124,#125,#126),$,.U.,
 .U.,.U.,(#112));
#106=IFCSTYLEDITEM(#122,(#110),$);
#107=IFCSTYLEDITEM(#129,(#113),$);
#108=IFCSTYLEDITEM(#130,(#113),$);
#109=IFCSTYLEDITEM(#22,(#110),$);
#110=IFCSURFACESTYLE($,.BOTH.,(#114));
#111=IFCSURFACESTYLE($,.BOTH.,(#115));
#112=IFCSURFACESTYLE($,.BOTH.,(#116));
#113=IFCSURFACESTYLE($,.BOTH.,(#117));
#114=IFCSURFACESTYLESHADING(#118,0.);
#115=IFCSURFACESTYLESHADING(#119,0.);
#116=IFCSURFACESTYLESHADING(#120,0.);
#117=IFCSURFACESTYLESHADING(#121,0.);
#118=IFCCOLOURRGB($,1.,1.,0.);
#119=IFCCOLOURRGB($,1.,1.,1.);
#120=IFCCOLOURRGB($,0.,0.,1.);
#121=IFCCOLOURRGB($,1.,0.,0.);
#122=IFCSHELLBASEDSURFACEMODEL((#131));
#123=IFCSHELLBASEDSURFACEMODEL((#132));
#124=IFCSHELLBASEDSURFACEMODEL((#133));
#125=IFCSHELLBASEDSURFACEMODEL((#134));
#126=IFCSHELLBASEDSURFACEMODEL((#135));
#127=IFCSHELLBASEDSURFACEMODEL((#136));
#128=IFCSHELLBASEDSURFACEMODEL((#137));
#129=IFCSHELLBASEDSURFACEMODEL((#138));
#130=IFCSHELLBASEDSURFACEMODEL((#139));
#131=IFCOPENSHELL((#5323));
#132=IFCOPENSHELL((#5324));
#133=IFCOPENSHELL((#5325));
#134=IFCOPENSHELL((#5326));
#135=IFCOPENSHELL((#5327));
#136=IFCOPENSHELL((#5328));
#137=IFCOPENSHELL((#5329));
#138=IFCOPENSHELL((#5330));
#139=IFCOPENSHELL((#5331));
#140=IFCORIENTEDEDGE(*,*,#900,.T.);
#141=IFCORIENTEDEDGE(*,*,#901,.T.);
#142=IFCORIENTEDEDGE(*,*,#902,.T.);
#143=IFCORIENTEDEDGE(*,*,#903,.T.);
#144=IFCORIENTEDEDGE(*,*,#904,.T.);
#145=IFCORIENTEDEDGE(*,*,#905,.T.);
#146=IFCORIENTEDEDGE(*,*,#906,.T.);
#147=IFCORIENTEDEDGE(*,*,#907,.T.);
#148=IFCORIENTEDEDGE(*,*,#908,.T.);
#149=IFCORIENTEDEDGE(*,*,#909,.T.);
#150=IFCORIENTEDEDGE(*,*,#910,.T.);
#151=IFCORIENTEDEDGE(*,*,#911,.T.);
#152=IFCORIENTEDEDGE(*,*,#912,.T.);
#153=IFCORIENTEDEDGE(*,*,#913,.T.);
#154=IFCORIENTEDEDGE(*,*,#914,.T.);
#155=IFCORIENTEDEDGE(*,*,#915,.T.);
#156=IFCORIENTEDEDGE(*,*,#916,.T.);
#157=IFCORIENTEDEDGE(*,*,#917,.T.);
#158=IFCORIENTEDEDGE(*,*,#918,.T.);
#159=IFCORIENTEDEDGE(*,*,#919,.T.);
#160=IFCORIENTEDEDGE(*,*,#920,.T.);
#161=IFCORIENTEDEDGE(*,*,#921,.T.);
#162=IFCORIENTEDEDGE(*,*,#922,.T.);
#163=IFCORIENTEDEDGE(*,*,#923,.T.);
#164=IFCORIENTEDEDGE(*,*,#924,.T.);
#165=IFCORIENTEDEDGE(*,*,#925,.T.);
#166=IFCORIENTEDEDGE(*,*,#926,.F.);
#167=IFCORIENTEDEDGE(*,*,#927,.F.);
#168=IFCORIENTEDEDGE(*,*,#928,.T.);
#169=IFCORIENTEDEDGE(*,*,#929,.T.);
#170=IFCORIENTEDEDGE(*,*,#930,.F.);
#171=IFCORIENTEDEDGE(*,*,#931,.F.);
#172=IFCORIENTEDEDGE(*,*,#932,.T.);
#173=IFCORIENTEDEDGE(*,*,#933,.T.);
#174=IFCORIENTEDEDGE(*,*,#934,.F.);
#175=IFCORIENTEDEDGE(*,*,#935,.F.);
#176=IFCORIENTEDEDGE(*,*,#936,.T.);
#177=IFCORIENTEDEDGE(*,*,#937,.T.);
#178=IFCORIENTEDEDGE(*,*,#938,.F.);
#179=IFCORIENTEDEDGE(*,*,#939,.F.);
#180=IFCORIENTEDEDGE(*,*,#940,.T.);
#181=IFCORIENTEDEDGE(*,*,#941,.T.);
#182=IFCORIENTEDEDGE(*,*,#942,.T.);
#183=IFCORIENTEDEDGE(*,*,#943,.T.);
#184=IFCORIENTEDEDGE(*,*,#944,.T.);
#185=IFCORIENTEDEDGE(*,*,#945,.T.);
#186=IFCORIENTEDEDGE(*,*,#946,.T.);
#187=IFCORIENTEDEDGE(*,*,#947,.T.);
#188=IFCORIENTEDEDGE(*,*,#948,.T.);
#189=IFCORIENTEDEDGE(*,*,#949,.T.);
#190=IFCORIENTEDEDGE(*,*,#950,.T.);
#191=IFCORIENTEDEDGE(*,*,#951,.T.);
#192=IFCORIENTEDEDGE(*,*,#952,.T.);
#193=IFCORIENTEDEDGE(*,*,#953,.T.);
#194=IFCORIENTEDEDGE(*,*,#954,.T.);
#195=IFCORIENTEDEDGE(*,*,#955,.T.);
#196=IFCORIENTEDEDGE(*,*,#956,.T.);
#197=IFCORIENTEDEDGE(*,*,#957,.T.);
#198=IFCORIENTEDEDGE(*,*,#958,.T.);
#199=IFCORIENTEDEDGE(*,*,#959,.T.);
#200=IFCORIENTEDEDGE(*,*,#960,.T.);
#201=IFCORIENTEDEDGE(*,*,#961,.T.);
#202=IFCORIENTEDEDGE(*,*,#962,.T.);
#203=IFCORIENTEDEDGE(*,*,#963,.T.);
#204=IFCORIENTEDEDGE(*,*,#964,.T.);
#205=IFCORIENTEDEDGE(*,*,#965,.T.);
#206=IFCORIENTEDEDGE(*,*,#966,.T.);
#207=IFCORIENTEDEDGE(*,*,#967,.T.);
#208=IFCORIENTEDEDGE(*,*,#968,.T.);
#209=IFCORIENTEDEDGE(*,*,#969,.T.);
#210=IFCORIENTEDEDGE(*,*,#970,.T.);
#211=IFCORIENTEDEDGE(*,*,#971,.T.);
#212=IFCORIENTEDEDGE(*,*,#972,.T.);
#213=IFCORIENTEDEDGE(*,*,#973,.T.);
#214=IFCORIENTEDEDGE(*,*,#974,.T.);
#215=IFCORIENTEDEDGE(*,*,#975,.T.);
#216=IFCORIENTEDEDGE(*,*,#976,.T.);
#217=IFCORIENTEDEDGE(*,*,#977,.T.);
#218=IFCORIENTEDEDGE(*,*,#978,.T.);
#219=IFCORIENTEDEDGE(*,*,#979,.T.);
#220=IFCORIENTEDEDGE(*,*,#980,.T.);
#221=IFCORIENTEDEDGE(*,*,#981,.T.);
#222=IFCORIENTEDEDGE(*,*,#982,.T.);
#223=IFCORIENTEDEDGE(*,*,#983,.T.);
#224=IFCORIENTEDEDGE(*,*,#984,.T.);
#225=IFCORIENTEDEDGE(*,*,#985,.T.);
#226=IFCORIENTEDEDGE(*,*,#986,.T.);
#227=IFCORIENTEDEDGE(*,*,#987,.T.);
#228=IFCORIENTEDEDGE(*,*,#988,.T.);
#229=IFCORIENTEDEDGE(*,*,#989,.T.);
#230=IFCORIENTEDEDGE(*,*,#990,.T.);
#231=IFCORIENTEDEDGE(*,*,#991,.T.);
#232=IFCORIENTEDEDGE(*,*,#992,.T.);
#233=IFCORIENTEDEDGE(*,*,#993,.T.);
#234=IFCORIENTEDEDGE(*,*,#994,.T.);
#235=IFCORIENTEDEDGE(*,*,#995,.T.);
#236=IFCORIENTEDEDGE(*,*,#996,.T.);
#237=IFCORIENTEDEDGE(*,*,#997,.T.);
#238=IFCORIENTEDEDGE(*,*,#998,.T.);
#239=IFCORIENTEDEDGE(*,*,#999,.T.);
#240=IFCORIENTEDEDGE(*,*,#1000,.T.);
#241=IFCORIENTEDEDGE(*,*,#1001,.T.);
#242=IFCORIENTEDEDGE(*,*,#1002,.T.);
#243=IFCORIENTEDEDGE(*,*,#1003,.T.);
#244=IFCORIENTEDEDGE(*,*,#1004,.T.);
#245=IFCORIENTEDEDGE(*,*,#1005,.T.);
#246=IFCORIENTEDEDGE(*,*,#1006,.T.);
#247=IFCORIENTEDEDGE(*,*,#1007,.T.);
#248=IFCORIENTEDEDGE(*,*,#1008,.T.);
#249=IFCORIENTEDEDGE(*,*,#1009,.T.);
#250=IFCORIENTEDEDGE(*,*,#1010,.T.);
#251=IFCORIENTEDEDGE(*,*,#1011,.T.);
#252=IFCORIENTEDEDGE(*,*,#1012,.T.);
#253=IFCORIENTEDEDGE(*,*,#1013,.T.);
#254=IFCORIENTEDEDGE(*,*,#1014,.T.);
#255=IFCORIENTEDEDGE(*,*,#1015,.T.);
#256=IFCORIENTEDEDGE(*,*,#1016,.T.);
#257=IFCORIENTEDEDGE(*,*,#1017,.T.);
#258=IFCORIENTEDEDGE(*,*,#1018,.T.);
#259=IFCORIENTEDEDGE(*,*,#1019,.T.);
#260=IFCORIENTEDEDGE(*,*,#1020,.T.);
#261=IFCORIENTEDEDGE(*,*,#1021,.T.);
#262=IFCORIENTEDEDGE(*,*,#1022,.T.);
#263=IFCORIENTEDEDGE(*,*,#1023,.T.);
#264=IFCORIENTEDEDGE(*,*,#1024,.T.);
#265=IFCORIENTEDEDGE(*,*,#1025,.T.);
#266=IFCORIENTEDEDGE(*,*,#1026,.T.);
#267=IFCORIENTEDEDGE(*,*,#1027,.T.);
#268=IFCORIENTEDEDGE(*,*,#1028,.T.);
#269=IFCORIENTEDEDGE(*,*,#1029,.T.);
#270=IFCORIENTEDEDGE(*,*,#1030,.T.);
#271=IFCORIENTEDEDGE(*,*,#1031,.T.);
#272=IFCORIENTEDEDGE(*,*,#1032,.T.);
#273=IFCORIENTEDEDGE(*,*,#1033,.T.);
#274=IFCORIENTEDEDGE(*,*,#1034,.T.);
#275=IFCORIENTEDEDGE(*,*,#1035,.T.);
#276=IFCORIENTEDEDGE(*,*,#1036,.T.);
#277=IFCORIENTEDEDGE(*,*,#1037,.T.);
#278=IFCORIENTEDEDGE(*,*,#1038,.T.);
#279=IFCORIENTEDEDGE(*,*,#1039,.T.);
#280=IFCORIENTEDEDGE(*,*,#1040,.T.);
#281=IFCORIENTEDEDGE(*,*,#1041,.T.);
#282=IFCORIENTEDEDGE(*,*,#1042,.T.);
#283=IFCORIENTEDEDGE(*,*,#1043,.T.);
#284=IFCORIENTEDEDGE(*,*,#1044,.T.);
#285=IFCORIENTEDEDGE(*,*,#1045,.T.);
#286=IFCORIENTEDEDGE(*,*,#1046,.T.);
#287=IFCORIENTEDEDGE(*,*,#1047,.T.);
#288=IFCORIENTEDEDGE(*,*,#1048,.T.);
#289=IFCORIENTEDEDGE(*,*,#1049,.T.);
#290=IFCORIENTEDEDGE(*,*,#1050,.T.);
#291=IFCORIENTEDEDGE(*,*,#1051,.T.);
#292=IFCORIENTEDEDGE(*,*,#1052,.T.);
#293=IFCORIENTEDEDGE(*,*,#1053,.T.);
#294=IFCORIENTEDEDGE(*,*,#1054,.T.);
#295=IFCORIENTEDEDGE(*,*,#1055,.T.);
#296=IFCORIENTEDEDGE(*,*,#1056,.T.);
#297=IFCORIENTEDEDGE(*,*,#1057,.T.);
#298=IFCORIENTEDEDGE(*,*,#1058,.T.);
#299=IFCORIENTEDEDGE(*,*,#1059,.T.);
#300=IFCORIENTEDEDGE(*,*,#1060,.T.);
#301=IFCORIENTEDEDGE(*,*,#1061,.T.);
#302=IFCORIENTEDEDGE(*,*,#1062,.T.);
#303=IFCORIENTEDEDGE(*,*,#1063,.T.);
#304=IFCORIENTEDEDGE(*,*,#1064,.T.);
#305=IFCORIENTEDEDGE(*,*,#1065,.T.);
#306=IFCORIENTEDEDGE(*,*,#1066,.T.);
#307=IFCORIENTEDEDGE(*,*,#1067,.T.);
#308=IFCORIENTEDEDGE(*,*,#1068,.T.);
#309=IFCORIENTEDEDGE(*,*,#1069,.T.);
#310=IFCORIENTEDEDGE(*,*,#1070,.T.);
#311=IFCORIENTEDEDGE(*,*,#1071,.T.);
#312=IFCORIENTEDEDGE(*,*,#1072,.T.);
#313=IFCORIENTEDEDGE(*,*,#1073,.T.);
#314=IFCORIENTEDEDGE(*,*,#1074,.T.);
#315=IFCORIENTEDEDGE(*,*,#1075,.T.);
#316=IFCORIENTEDEDGE(*,*,#1076,.T.);
#317=IFCORIENTEDEDGE(*,*,#1077,.T.);
#318=IFCORIENTEDEDGE(*,*,#1078,.T.);
#319=IFCORIENTEDEDGE(*,*,#1079,.T.);
#320=IFCORIENTEDEDGE(*,*,#1080,.T.);
#321=IFCORIENTEDEDGE(*,*,#1081,.T.);
#322=IFCORIENTEDEDGE(*,*,#1082,.T.);
#323=IFCORIENTEDEDGE(*,*,#1083,.T.);
#324=IFCORIENTEDEDGE(*,*,#1084,.T.);
#325=IFCORIENTEDEDGE(*,*,#1085,.T.);
#326=IFCORIENTEDEDGE(*,*,#1086,.T.);
#327=IFCORIENTEDEDGE(*,*,#1087,.T.);
#328=IFCORIENTEDEDGE(*,*,#1088,.T.);
#329=IFCORIENTEDEDGE(*,*,#1089,.T.);
#330=IFCORIENTEDEDGE(*,*,#1090,.T.);
#331=IFCORIENTEDEDGE(*,*,#1091,.T.);
#332=IFCORIENTEDEDGE(*,*,#1092,.T.);
#333=IFCORIENTEDEDGE(*,*,#1093,.T.);
#334=IFCORIENTEDEDGE(*,*,#1094,.T.);
#335=IFCORIENTEDEDGE(*,*,#1095,.T.);
#336=IFCORIENTEDEDGE(*,*,#1096,.T.);
#337=IFCORIENTEDEDGE(*,*,#1097,.T.);
#338=IFCORIENTEDEDGE(*,*,#1098,.T.);
#339=IFCORIENTEDEDGE(*,*,#1099,.T.);
#340=IFCORIENTEDEDGE(*,*,#1100,.T.);
#341=IFCORIENTEDEDGE(*,*,#1101,.T.);
#342=IFCORIENTEDEDGE(*,*,#1102,.T.);
#343=IFCORIENTEDEDGE(*,*,#1103,.T.);
#344=IFCORIENTEDEDGE(*,*,#1104,.T.);
#345=IFCORIENTEDEDGE(*,*,#1105,.T.);
#346=IFCORIENTEDEDGE(*,*,#1106,.T.);
#347=IFCORIENTEDEDGE(*,*,#1107,.T.);
#348=IFCORIENTEDEDGE(*,*,#1108,.T.);
#349=IFCORIENTEDEDGE(*,*,#1109,.T.);
#350=IFCORIENTEDEDGE(*,*,#1110,.T.);
#351=IFCORIENTEDEDGE(*,*,#1111,.T.);
#352=IFCORIENTEDEDGE(*,*,#1112,.T.);
#353=IFCORIENTEDEDGE(*,*,#1113,.T.);
#354=IFCORIENTEDEDGE(*,*,#1114,.T.);
#355=IFCORIENTEDEDGE(*,*,#1115,.T.);
#356=IFCORIENTEDEDGE(*,*,#1116,.T.);
#357=IFCORIENTEDEDGE(*,*,#1117,.T.);
#358=IFCORIENTEDEDGE(*,*,#1118,.T.);
#359=IFCORIENTEDEDGE(*,*,#1119,.T.);
#360=IFCORIENTEDEDGE(*,*,#1120,.T.);
#361=IFCORIENTEDEDGE(*,*,#1121,.T.);
#362=IFCORIENTEDEDGE(*,*,#1122,.T.);
#363=IFCORIENTEDEDGE(*,*,#1123,.T.);
#364=IFCORIENTEDEDGE(*,*,#1124,.T.);
#365=IFCORIENTEDEDGE(*,*,#1125,.T.);
#366=IFCORIENTEDEDGE(*,*,#1126,.T.);
#367=IFCORIENTEDEDGE(*,*,#1127,.T.);
#368=IFCORIENTEDEDGE(*,*,#1128,.T.);
#369=IFCORIENTEDEDGE(*,*,#1129,.T.);
#370=IFCORIENTEDEDGE(*,*,#1130,.T.);
#371=IFCORIENTEDEDGE(*,*,#1131,.T.);
#372=IFCORIENTEDEDGE(*,*,#1132,.T.);
#373=IFCORIENTEDEDGE(*,*,#1133,.T.);
#374=IFCORIENTEDEDGE(*,*,#1134,.T.);
#375=IFCORIENTEDEDGE(*,*,#1135,.T.);
#376=IFCORIENTEDEDGE(*,*,#1136,.T.);
#377=IFCORIENTEDEDGE(*,*,#1137,.T.);
#378=IFCORIENTEDEDGE(*,*,#1138,.T.);
#379=IFCORIENTEDEDGE(*,*,#1139,.T.);
#380=IFCORIENTEDEDGE(*,*,#1140,.T.);
#381=IFCORIENTEDEDGE(*,*,#1141,.T.);
#382=IFCORIENTEDEDGE(*,*,#1142,.T.);
#383=IFCORIENTEDEDGE(*,*,#1143,.T.);
#384=IFCORIENTEDEDGE(*,*,#1144,.T.);
#385=IFCORIENTEDEDGE(*,*,#1145,.T.);
#386=IFCORIENTEDEDGE(*,*,#1146,.T.);
#387=IFCORIENTEDEDGE(*,*,#1147,.T.);
#388=IFCORIENTEDEDGE(*,*,#1148,.T.);
#389=IFCORIENTEDEDGE(*,*,#1149,.T.);
#390=IFCORIENTEDEDGE(*,*,#1150,.T.);
#391=IFCORIENTEDEDGE(*,*,#1151,.T.);
#392=IFCORIENTEDEDGE(*,*,#1152,.T.);
#393=IFCORIENTEDEDGE(*,*,#1153,.T.);
#394=IFCORIENTEDEDGE(*,*,#1154,.T.);
#395=IFCORIENTEDEDGE(*,*,#1155,.T.);
#396=IFCORIENTEDEDGE(*,*,#1156,.T.);
#397=IFCORIENTEDEDGE(*,*,#1157,.T.);
#398=IFCORIENTEDEDGE(*,*,#1158,.T.);
#399=IFCORIENTEDEDGE(*,*,#1159,.T.);
#400=IFCORIENTEDEDGE(*,*,#1160,.T.);
#401=IFCORIENTEDEDGE(*,*,#1161,.T.);
#402=IFCORIENTEDEDGE(*,*,#1162,.T.);
#403=IFCORIENTEDEDGE(*,*,#1163,.T.);
#404=IFCORIENTEDEDGE(*,*,#1164,.T.);
#405=IFCORIENTEDEDGE(*,*,#1165,.T.);
#406=IFCORIENTEDEDGE(*,*,#1166,.T.);
#407=IFCORIENTEDEDGE(*,*,#1167,.T.);
#408=IFCORIENTEDEDGE(*,*,#1168,.T.);
#409=IFCORIENTEDEDGE(*,*,#1169,.T.);
#410=IFCORIENTEDEDGE(*,*,#1170,.T.);
#411=IFCORIENTEDEDGE(*,*,#1171,.T.);
#412=IFCORIENTEDEDGE(*,*,#1172,.T.);
#413=IFCORIENTEDEDGE(*,*,#1173,.T.);
#414=IFCORIENTEDEDGE(*,*,#1174,.T.);
#415=IFCORIENTEDEDGE(*,*,#1175,.T.);
#416=IFCORIENTEDEDGE(*,*,#1176,.T.);
#417=IFCORIENTEDEDGE(*,*,#1177,.T.);
#418=IFCORIENTEDEDGE(*,*,#1178,.T.);
#419=IFCORIENTEDEDGE(*,*,#1179,.T.);
#420=IFCORIENTEDEDGE(*,*,#1180,.T.);
#421=IFCORIENTEDEDGE(*,*,#1181,.T.);
#422=IFCORIENTEDEDGE(*,*,#1182,.T.);
#423=IFCORIENTEDEDGE(*,*,#1183,.T.);
#424=IFCORIENTEDEDGE(*,*,#1184,.T.);
#425=IFCORIENTEDEDGE(*,*,#1185,.T.);
#426=IFCORIENTEDEDGE(*,*,#1186,.T.);
#427=IFCORIENTEDEDGE(*,*,#1187,.T.);
#428=IFCORIENTEDEDGE(*,*,#1188,.T.);
#429=IFCORIENTEDEDGE(*,*,#1189,.T.);
#430=IFCORIENTEDEDGE(*,*,#1190,.T.);
#431=IFCORIENTEDEDGE(*,*,#1191,.T.);
#432=IFCORIENTEDEDGE(*,*,#1192,.T.);
#433=IFCORIENTEDEDGE(*,*,#1193,.T.);
#434=IFCORIENTEDEDGE(*,*,#1194,.T.);
#435=IFCORIENTEDEDGE(*,*,#1195,.T.);
#436=IFCORIENTEDEDGE(*,*,#1196,.T.);
#437=IFCORIENTEDEDGE(*,*,#1197,.T.);
#438=IFCORIENTEDEDGE(*,*,#1198,.T.);
#439=IFCORIENTEDEDGE(*,*,#1199,.T.);
#440=IFCORIENTEDEDGE(*,*,#1200,.T.);
#441=IFCORIENTEDEDGE(*,*,#1201,.T.);
#442=IFCORIENTEDEDGE(*,*,#1202,.T.);
#443=IFCORIENTEDEDGE(*,*,#1203,.T.);
#444=IFCORIENTEDEDGE(*,*,#1204,.T.);
#445=IFCORIENTEDEDGE(*,*,#1205,.T.);
#446=IFCORIENTEDEDGE(*,*,#1206,.T.);
#447=IFCORIENTEDEDGE(*,*,#1207,.T.);
#448=IFCORIENTEDEDGE(*,*,#1208,.T.);
#449=IFCORIENTEDEDGE(*,*,#1209,.T.);
#450=IFCORIENTEDEDGE(*,*,#1210,.T.);
#451=IFCORIENTEDEDGE(*,*,#1211,.T.);
#452=IFCORIENTEDEDGE(*,*,#1212,.T.);
#453=IFCORIENTEDEDGE(*,*,#1213,.T.);
#454=IFCORIENTEDEDGE(*,*,#1214,.T.);
#455=IFCORIENTEDEDGE(*,*,#1215,.T.);
#456=IFCORIENTEDEDGE(*,*,#1216,.T.);
#457=IFCORIENTEDEDGE(*,*,#1217,.T.);
#458=IFCORIENTEDEDGE(*,*,#1218,.T.);
#459=IFCORIENTEDEDGE(*,*,#1219,.T.);
#460=IFCORIENTEDEDGE(*,*,#1220,.T.);
#461=IFCORIENTEDEDGE(*,*,#1221,.T.);
#462=IFCORIENTEDEDGE(*,*,#1222,.T.);
#463=IFCORIENTEDEDGE(*,*,#1223,.T.);
#464=IFCORIENTEDEDGE(*,*,#1224,.T.);
#465=IFCORIENTEDEDGE(*,*,#1225,.T.);
#466=IFCORIENTEDEDGE(*,*,#1226,.T.);
#467=IFCORIENTEDEDGE(*,*,#1227,.T.);
#468=IFCORIENTEDEDGE(*,*,#1228,.T.);
#469=IFCORIENTEDEDGE(*,*,#1229,.T.);
#470=IFCORIENTEDEDGE(*,*,#1230,.T.);
#471=IFCORIENTEDEDGE(*,*,#1231,.T.);
#472=IFCORIENTEDEDGE(*,*,#1232,.T.);
#473=IFCORIENTEDEDGE(*,*,#1233,.T.);
#474=IFCORIENTEDEDGE(*,*,#1234,.T.);
#475=IFCORIENTEDEDGE(*,*,#1235,.T.);
#476=IFCORIENTEDEDGE(*,*,#1236,.T.);
#477=IFCORIENTEDEDGE(*,*,#1237,.T.);
#478=IFCORIENTEDEDGE(*,*,#1238,.T.);
#479=IFCORIENTEDEDGE(*,*,#1239,.T.);
#480=IFCORIENTEDEDGE(*,*,#1240,.T.);
#481=IFCORIENTEDEDGE(*,*,#1241,.T.);
#482=IFCORIENTEDEDGE(*,*,#1242,.T.);
#483=IFCORIENTEDEDGE(*,*,#1243,.T.);
#484=IFCORIENTEDEDGE(*,*,#1244,.T.);
#485=IFCORIENTEDEDGE(*,*,#1245,.T.);
#486=IFCORIENTEDEDGE(*,*,#1246,.T.);
#487=IFCORIENTEDEDGE(*,*,#1247,.T.);
#488=IFCORIENTEDEDGE(*,*,#1248,.T.);
#489=IFCORIENTEDEDGE(*,*,#1249,.T.);
#490=IFCORIENTEDEDGE(*,*,#1250,.T.);
#491=IFCORIENTEDEDGE(*,*,#1251,.T.);
#492=IFCORIENTEDEDGE(*,*,#1252,.T.);
#493=IFCORIENTEDEDGE(*,*,#1253,.T.);
#494=IFCORIENTEDEDGE(*,*,#1254,.T.);
#495=IFCORIENTEDEDGE(*,*,#1255,.T.);
#496=IFCORIENTEDEDGE(*,*,#1256,.T.);
#497=IFCORIENTEDEDGE(*,*,#1257,.T.);
#498=IFCORIENTEDEDGE(*,*,#1258,.T.);
#499=IFCORIENTEDEDGE(*,*,#1259,.T.);
#500=IFCORIENTEDEDGE(*,*,#1260,.T.);
#501=IFCORIENTEDEDGE(*,*,#1261,.T.);
#502=IFCORIENTEDEDGE(*,*,#1262,.T.);
#503=IFCORIENTEDEDGE(*,*,#1263,.T.);
#504=IFCORIENTEDEDGE(*,*,#1264,.T.);
#505=IFCORIENTEDEDGE(*,*,#1265,.T.);
#506=IFCORIENTEDEDGE(*,*,#1266,.T.);
#507=IFCORIENTEDEDGE(*,*,#1267,.T.);
#508=IFCORIENTEDEDGE(*,*,#1268,.T.);
#509=IFCORIENTEDEDGE(*,*,#1269,.T.);
#510=IFCORIENTEDEDGE(*,*,#1270,.T.);
#511=IFCORIENTEDEDGE(*,*,#1271,.T.);
#512=IFCORIENTEDEDGE(*,*,#1272,.T.);
#513=IFCORIENTEDEDGE(*,*,#1273,.T.);
#514=IFCORIENTEDEDGE(*,*,#1274,.T.);
#515=IFCORIENTEDEDGE(*,*,#1275,.T.);
#516=IFCORIENTEDEDGE(*,*,#1276,.T.);
#517=IFCORIENTEDEDGE(*,*,#1277,.T.);
#518=IFCORIENTEDEDGE(*,*,#1278,.T.);
#519=IFCORIENTEDEDGE(*,*,#1279,.T.);
#520=IFCORIENTEDEDGE(*,*,#1280,.T.);
#521=IFCORIENTEDEDGE(*,*,#1281,.T.);
#522=IFCORIENTEDEDGE(*,*,#1282,.T.);
#523=IFCORIENTEDEDGE(*,*,#1283,.T.);
#524=IFCORIENTEDEDGE(*,*,#1284,.T.);
#525=IFCORIENTEDEDGE(*,*,#1285,.T.);
#526=IFCORIENTEDEDGE(*,*,#1286,.T.);
#527=IFCORIENTEDEDGE(*,*,#1287,.T.);
#528=IFCORIENTEDEDGE(*,*,#1288,.T.);
#529=IFCORIENTEDEDGE(*,*,#1289,.T.);
#530=IFCORIENTEDEDGE(*,*,#1290,.T.);
#531=IFCORIENTEDEDGE(*,*,#1291,.T.);
#532=IFCORIENTEDEDGE(*,*,#1292,.T.);
#533=IFCORIENTEDEDGE(*,*,#1293,.T.);
#534=IFCORIENTEDEDGE(*,*,#1294,.T.);
#535=IFCORIENTEDEDGE(*,*,#1295,.T.);
#536=IFCORIENTEDEDGE(*,*,#1296,.T.);
#537=IFCORIENTEDEDGE(*,*,#1297,.T.);
#538=IFCORIENTEDEDGE(*,*,#1298,.T.);
#539=IFCORIENTEDEDGE(*,*,#1299,.T.);
#540=IFCORIENTEDEDGE(*,*,#1300,.T.);
#541=IFCORIENTEDEDGE(*,*,#1301,.T.);
#542=IFCORIENTEDEDGE(*,*,#1302,.T.);
#543=IFCORIENTEDEDGE(*,*,#1303,.T.);
#544=IFCORIENTEDEDGE(*,*,#1304,.T.);
#545=IFCORIENTEDEDGE(*,*,#1305,.T.);
#546=IFCORIENTEDEDGE(*,*,#1306,.T.);
#547=IFCORIENTEDEDGE(*,*,#1307,.T.);
#548=IFCORIENTEDEDGE(*,*,#1308,.T.);
#549=IFCORIENTEDEDGE(*,*,#1309,.T.);
#550=IFCORIENTEDEDGE(*,*,#1310,.T.);
#551=IFCORIENTEDEDGE(*,*,#1311,.T.);
#552=IFCORIENTEDEDGE(*,*,#1312,.T.);
#553=IFCORIENTEDEDGE(*,*,#1313,.T.);
#554=IFCORIENTEDEDGE(*,*,#1314,.T.);
#555=IFCORIENTEDEDGE(*,*,#1315,.T.);
#556=IFCORIENTEDEDGE(*,*,#1316,.T.);
#557=IFCORIENTEDEDGE(*,*,#1317,.T.);
#558=IFCORIENTEDEDGE(*,*,#1318,.T.);
#559=IFCORIENTEDEDGE(*,*,#1319,.T.);
#560=IFCORIENTEDEDGE(*,*,#1320,.T.);
#561=IFCORIENTEDEDGE(*,*,#1321,.T.);
#562=IFCORIENTEDEDGE(*,*,#1322,.T.);
#563=IFCORIENTEDEDGE(*,*,#1323,.T.);
#564=IFCORIENTEDEDGE(*,*,#1324,.T.);
#565=IFCORIENTEDEDGE(*,*,#1325,.T.);
#566=IFCORIENTEDEDGE(*,*,#1326,.T.);
#567=IFCORIENTEDEDGE(*,*,#1327,.T.);
#568=IFCORIENTEDEDGE(*,*,#1328,.T.);
#569=IFCORIENTEDEDGE(*,*,#1329,.T.);
#570=IFCORIENTEDEDGE(*,*,#1330,.T.);
#571=IFCORIENTEDEDGE(*,*,#1331,.T.);
#572=IFCORIENTEDEDGE(*,*,#1332,.T.);
#573=IFCORIENTEDEDGE(*,*,#1333,.T.);
#574=IFCORIENTEDEDGE(*,*,#1334,.T.);
#575=IFCORIENTEDEDGE(*,*,#1335,.T.);
#576=IFCORIENTEDEDGE(*,*,#1336,.T.);
#577=IFCORIENTEDEDGE(*,*,#1337,.T.);
#578=IFCORIENTEDEDGE(*,*,#1338,.T.);
#579=IFCORIENTEDEDGE(*,*,#1339,.T.);
#580=IFCORIENTEDEDGE(*,*,#1340,.T.);
#581=IFCORIENTEDEDGE(*,*,#1341,.T.);
#582=IFCORIENTEDEDGE(*,*,#1342,.T.);
#583=IFCORIENTEDEDGE(*,*,#1343,.T.);
#584=IFCORIENTEDEDGE(*,*,#1344,.T.);
#585=IFCORIENTEDEDGE(*,*,#1345,.T.);
#586=IFCORIENTEDEDGE(*,*,#1346,.T.);
#587=IFCORIENTEDEDGE(*,*,#1347,.T.);
#588=IFCORIENTEDEDGE(*,*,#1348,.T.);
#589=IFCORIENTEDEDGE(*,*,#1349,.T.);
#590=IFCORIENTEDEDGE(*,*,#1350,.T.);
#591=IFCORIENTEDEDGE(*,*,#1351,.T.);
#592=IFCORIENTEDEDGE(*,*,#1352,.T.);
#593=IFCORIENTEDEDGE(*,*,#1353,.T.);
#594=IFCORIENTEDEDGE(*,*,#1354,.T.);
#595=IFCORIENTEDEDGE(*,*,#1355,.T.);
#596=IFCORIENTEDEDGE(*,*,#1356,.T.);
#597=IFCORIENTEDEDGE(*,*,#1357,.T.);
#598=IFCORIENTEDEDGE(*,*,#1358,.T.);
#599=IFCORIENTEDEDGE(*,*,#1359,.T.);
#600=IFCORIENTEDEDGE(*,*,#1360,.T.);
#601=IFCORIENTEDEDGE(*,*,#1361,.T.);
#602=IFCORIENTEDEDGE(*,*,#1362,.T.);
#603=IFCORIENTEDEDGE(*,*,#1363,.T.);
#604=IFCORIENTEDEDGE(*,*,#1364,.T.);
#605=IFCORIENTEDEDGE(*,*,#1365,.T.);
#606=IFCORIENTEDEDGE(*,*,#1366,.T.);
#607=IFCORIENTEDEDGE(*,*,#1367,.T.);
#608=IFCORIENTEDEDGE(*,*,#1368,.T.);
#609=IFCORIENTEDEDGE(*,*,#1369,.T.);
#610=IFCORIENTEDEDGE(*,*,#1370,.T.);
#611=IFCORIENTEDEDGE(*,*,#1371,.T.);
#612=IFCORIENTEDEDGE(*,*,#1372,.T.);
#613=IFCORIENTEDEDGE(*,*,#1373,.T.);
#614=IFCORIENTEDEDGE(*,*,#1374,.T.);
#615=IFCORIENTEDEDGE(*,*,#1375,.T.);
#616=IFCORIENTEDEDGE(*,*,#1376,.T.);
#617=IFCORIENTEDEDGE(*,*,#1377,.T.);
#618=IFCORIENTEDEDGE(*,*,#1378,.T.);
#619=IFCORIENTEDEDGE(*,*,#1379,.T.);
#620=IFCORIENTEDEDGE(*,*,#1380,.T.);
#621=IFCORIENTEDEDGE(*,*,#1381,.T.);
#622=IFCORIENTEDEDGE(*,*,#1382,.T.);
#623=IFCORIENTEDEDGE(*,*,#1383,.T.);
#624=IFCORIENTEDEDGE(*,*,#1384,.T.);
#625=IFCORIENTEDEDGE(*,*,#1385,.T.);
#626=IFCORIENTEDEDGE(*,*,#1386,.T.);
#627=IFCORIENTEDEDGE(*,*,#1387,.T.);
#628=IFCORIENTEDEDGE(*,*,#1388,.T.);
#629=IFCORIENTEDEDGE(*,*,#1389,.T.);
#630=IFCORIENTEDEDGE(*,*,#1390,.T.);
#631=IFCORIENTEDEDGE(*,*,#1391,.T.);
#632=IFCORIENTEDEDGE(*,*,#1392,.T.);
#633=IFCORIENTEDEDGE(*,*,#1393,.T.);
#634=IFCORIENTEDEDGE(*,*,#1394,.T.);
#635=IFCORIENTEDEDGE(*,*,#1395,.T.);
#636=IFCORIENTEDEDGE(*,*,#1396,.T.);
#637=IFCORIENTEDEDGE(*,*,#1397,.T.);
#638=IFCORIENTEDEDGE(*,*,#1398,.T.);
#639=IFCORIENTEDEDGE(*,*,#1399,.T.);
#640=IFCORIENTEDEDGE(*,*,#1400,.T.);
#641=IFCORIENTEDEDGE(*,*,#1401,.T.);
#642=IFCORIENTEDEDGE(*,*,#1402,.T.);
#643=IFCORIENTEDEDGE(*,*,#1403,.T.);
#644=IFCORIENTEDEDGE(*,*,#1404,.T.);
#645=IFCORIENTEDEDGE(*,*,#1405,.T.);
#646=IFCORIENTEDEDGE(*,*,#1406,.T.);
#647=IFCORIENTEDEDGE(*,*,#1407,.T.);
#648=IFCORIENTEDEDGE(*,*,#1408,.T.);
#649=IFCORIENTEDEDGE(*,*,#1409,.T.);
#650=IFCORIENTEDEDGE(*,*,#1410,.T.);
#651=IFCORIENTEDEDGE(*,*,#1411,.T.);
#652=IFCORIENTEDEDGE(*,*,#1412,.T.);
#653=IFCORIENTEDEDGE(*,*,#1413,.T.);
#654=IFCORIENTEDEDGE(*,*,#1414,.T.);
#655=IFCORIENTEDEDGE(*,*,#1415,.T.);
#656=IFCORIENTEDEDGE(*,*,#1416,.T.);
#657=IFCORIENTEDEDGE(*,*,#1417,.T.);
#658=IFCORIENTEDEDGE(*,*,#1418,.T.);
#659=IFCORIENTEDEDGE(*,*,#1419,.T.);
#660=IFCORIENTEDEDGE(*,*,#1420,.T.);
#661=IFCORIENTEDEDGE(*,*,#1421,.T.);
#662=IFCORIENTEDEDGE(*,*,#1422,.T.);
#663=IFCORIENTEDEDGE(*,*,#1423,.T.);
#664=IFCORIENTEDEDGE(*,*,#1424,.T.);
#665=IFCORIENTEDEDGE(*,*,#1425,.T.);
#666=IFCORIENTEDEDGE(*,*,#1426,.T.);
#667=IFCORIENTEDEDGE(*,*,#1427,.T.);
#668=IFCORIENTEDEDGE(*,*,#1428,.T.);
#669=IFCORIENTEDEDGE(*,*,#1429,.T.);
#670=IFCORIENTEDEDGE(*,*,#1430,.T.);
#671=IFCORIENTEDEDGE(*,*,#1431,.T.);
#672=IFCORIENTEDEDGE(*,*,#1432,.T.);
#673=IFCORIENTEDEDGE(*,*,#1433,.T.);
#674=IFCORIENTEDEDGE(*,*,#1434,.T.);
#675=IFCORIENTEDEDGE(*,*,#1435,.T.);
#676=IFCORIENTEDEDGE(*,*,#1436,.T.);
#677=IFCORIENTEDEDGE(*,*,#1437,.T.);
#678=IFCORIENTEDEDGE(*,*,#1438,.T.);
#679=IFCORIENTEDEDGE(*,*,#1439,.T.);
#680=IFCORIENTEDEDGE(*,*,#1440,.T.);
#681=IFCORIENTEDEDGE(*,*,#1441,.T.);
#682=IFCORIENTEDEDGE(*,*,#1442,.T.);
#683=IFCORIENTEDEDGE(*,*,#1443,.T.);
#684=IFCORIENTEDEDGE(*,*,#1444,.T.);
#685=IFCORIENTEDEDGE(*,*,#1445,.T.);
#686=IFCORIENTEDEDGE(*,*,#1446,.T.);
#687=IFCORIENTEDEDGE(*,*,#1447,.T.);
#688=IFCORIENTEDEDGE(*,*,#1448,.T.);
#689=IFCORIENTEDEDGE(*,*,#1449,.T.);
#690=IFCORIENTEDEDGE(*,*,#1450,.T.);
#691=IFCORIENTEDEDGE(*,*,#1451,.T.);
#692=IFCORIENTEDEDGE(*,*,#1452,.T.);
#693=IFCORIENTEDEDGE(*,*,#1453,.T.);
#694=IFCORIENTEDEDGE(*,*,#1454,.T.);
#695=IFCORIENTEDEDGE(*,*,#1455,.T.);
#696=IFCORIENTEDEDGE(*,*,#1456,.T.);
#697=IFCORIENTEDEDGE(*,*,#1457,.T.);
#698=IFCORIENTEDEDGE(*,*,#1458,.T.);
#699=IFCORIENTEDEDGE(*,*,#1459,.T.);
#700=IFCORIENTEDEDGE(*,*,#1460,.T.);
#701=IFCORIENTEDEDGE(*,*,#1461,.T.);
#702=IFCORIENTEDEDGE(*,*,#1462,.T.);
#703=IFCORIENTEDEDGE(*,*,#1463,.T.);
#704=IFCORIENTEDEDGE(*,*,#1464,.T.);
#705=IFCORIENTEDEDGE(*,*,#1465,.T.);
#706=IFCORIENTEDEDGE(*,*,#1466,.T.);
#707=IFCORIENTEDEDGE(*,*,#1467,.T.);
#708=IFCORIENTEDEDGE(*,*,#1468,.T.);
#709=IFCORIENTEDEDGE(*,*,#1469,.T.);
#710=IFCORIENTEDEDGE(*,*,#1470,.T.);
#711=IFCORIENTEDEDGE(*,*,#1471,.T.);
#712=IFCORIENTEDEDGE(*,*,#1472,.T.);
#713=IFCORIENTEDEDGE(*,*,#1473,.T.);
#714=IFCORIENTEDEDGE(*,*,#1474,.T.);
#715=IFCORIENTEDEDGE(*,*,#1475,.T.);
#716=IFCORIENTEDEDGE(*,*,#1476,.T.);
#717=IFCORIENTEDEDGE(*,*,#1477,.T.);
#718=IFCORIENTEDEDGE(*,*,#1478,.T.);
#719=IFCORIENTEDEDGE(*,*,#1479,.T.);
#720=IFCORIENTEDEDGE(*,*,#1480,.T.);
#721=IFCORIENTEDEDGE(*,*,#1481,.T.);
#722=IFCORIENTEDEDGE(*,*,#1482,.T.);
#723=IFCORIENTEDEDGE(*,*,#1483,.T.);
#724=IFCORIENTEDEDGE(*,*,#1484,.T.);
#725=IFCORIENTEDEDGE(*,*,#1485,.T.);
#726=IFCORIENTEDEDGE(*,*,#1486,.T.);
#727=IFCORIENTEDEDGE(*,*,#1487,.T.);
#728=IFCORIENTEDEDGE(*,*,#1488,.T.);
#729=IFCORIENTEDEDGE(*,*,#1489,.T.);
#730=IFCORIENTEDEDGE(*,*,#1490,.T.);
#731=IFCORIENTEDEDGE(*,*,#1491,.T.);
#732=IFCORIENTEDEDGE(*,*,#1492,.T.);
#733=IFCORIENTEDEDGE(*,*,#1493,.T.);
#734=IFCORIENTEDEDGE(*,*,#1494,.T.);
#735=IFCORIENTEDEDGE(*,*,#1495,.T.);
#736=IFCORIENTEDEDGE(*,*,#1496,.T.);
#737=IFCORIENTEDEDGE(*,*,#1497,.T.);
#738=IFCORIENTEDEDGE(*,*,#1498,.T.);
#739=IFCORIENTEDEDGE(*,*,#1499,.T.);
#740=IFCORIENTEDEDGE(*,*,#1500,.T.);
#741=IFCORIENTEDEDGE(*,*,#1501,.T.);
#742=IFCORIENTEDEDGE(*,*,#1502,.T.);
#743=IFCORIENTEDEDGE(*,*,#1503,.T.);
#744=IFCORIENTEDEDGE(*,*,#1504,.T.);
#745=IFCORIENTEDEDGE(*,*,#1505,.T.);
#746=IFCORIENTEDEDGE(*,*,#1506,.T.);
#747=IFCORIENTEDEDGE(*,*,#1507,.T.);
#748=IFCORIENTEDEDGE(*,*,#1508,.T.);
#749=IFCORIENTEDEDGE(*,*,#1509,.T.);
#750=IFCORIENTEDEDGE(*,*,#1510,.T.);
#751=IFCORIENTEDEDGE(*,*,#1511,.T.);
#752=IFCORIENTEDEDGE(*,*,#1512,.T.);
#753=IFCORIENTEDEDGE(*,*,#1513,.T.);
#754=IFCORIENTEDEDGE(*,*,#1514,.T.);
#755=IFCORIENTEDEDGE(*,*,#1515,.T.);
#756=IFCORIENTEDEDGE(*,*,#1516,.T.);
#757=IFCORIENTEDEDGE(*,*,#1517,.F.);
#758=IFCORIENTEDEDGE(*,*,#1518,.F.);
#759=IFCORIENTEDEDGE(*,*,#1519,.T.);
#760=IFCORIENTEDEDGE(*,*,#1520,.T.);
#761=IFCORIENTEDEDGE(*,*,#1521,.T.);
#762=IFCORIENTEDEDGE(*,*,#1522,.T.);
#763=IFCORIENTEDEDGE(*,*,#1523,.T.);
#764=IFCORIENTEDEDGE(*,*,#1524,.T.);
#765=IFCORIENTEDEDGE(*,*,#1525,.T.);
#766=IFCORIENTEDEDGE(*,*,#1526,.T.);
#767=IFCORIENTEDEDGE(*,*,#1527,.T.);
#768=IFCORIENTEDEDGE(*,*,#1528,.T.);
#769=IFCORIENTEDEDGE(*,*,#1529,.T.);
#770=IFCORIENTEDEDGE(*,*,#1530,.T.);
#771=IFCORIENTEDEDGE(*,*,#1531,.T.);
#772=IFCORIENTEDEDGE(*,*,#1532,.T.);
#773=IFCORIENTEDEDGE(*,*,#1533,.T.);
#774=IFCORIENTEDEDGE(*,*,#1534,.T.);
#775=IFCORIENTEDEDGE(*,*,#1535,.T.);
#776=IFCORIENTEDEDGE(*,*,#1536,.T.);
#777=IFCORIENTEDEDGE(*,*,#1537,.T.);
#778=IFCORIENTEDEDGE(*,*,#1538,.T.);
#779=IFCORIENTEDEDGE(*,*,#1539,.T.);
#780=IFCORIENTEDEDGE(*,*,#1540,.T.);
#781=IFCORIENTEDEDGE(*,*,#1541,.T.);
#782=IFCORIENTEDEDGE(*,*,#1542,.T.);
#783=IFCORIENTEDEDGE(*,*,#1543,.T.);
#784=IFCORIENTEDEDGE(*,*,#1544,.T.);
#785=IFCORIENTEDEDGE(*,*,#1545,.T.);
#786=IFCORIENTEDEDGE(*,*,#1546,.T.);
#787=IFCORIENTEDEDGE(*,*,#1547,.T.);
#788=IFCORIENTEDEDGE(*,*,#1548,.T.);
#789=IFCORIENTEDEDGE(*,*,#1549,.T.);
#790=IFCORIENTEDEDGE(*,*,#1550,.T.);
#791=IFCORIENTEDEDGE(*,*,#1551,.T.);
#792=IFCORIENTEDEDGE(*,*,#1552,.T.);
#793=IFCORIENTEDEDGE(*,*,#1553,.T.);
#794=IFCORIENTEDEDGE(*,*,#1554,.T.);
#795=IFCORIENTEDEDGE(*,*,#1555,.T.);
#796=IFCORIENTEDEDGE(*,*,#1556,.T.);
#797=IFCORIENTEDEDGE(*,*,#1557,.T.);
#798=IFCORIENTEDEDGE(*,*,#1558,.T.);
#799=IFCORIENTEDEDGE(*,*,#1559,.T.);
#800=IFCORIENTEDEDGE(*,*,#1560,.T.);
#801=IFCORIENTEDEDGE(*,*,#1561,.T.);
#802=IFCORIENTEDEDGE(*,*,#1562,.T.);
#803=IFCORIENTEDEDGE(*,*,#1563,.T.);
#804=IFCORIENTEDEDGE(*,*,#1564,.T.);
#805=IFCORIENTEDEDGE(*,*,#1565,.T.);
#806=IFCORIENTEDEDGE(*,*,#1566,.T.);
#807=IFCORIENTEDEDGE(*,*,#1567,.T.);
#808=IFCORIENTEDEDGE(*,*,#1568,.T.);
#809=IFCORIENTEDEDGE(*,*,#1569,.T.);
#810=IFCORIENTEDEDGE(*,*,#1570,.T.);
#811=IFCORIENTEDEDGE(*,*,#1571,.T.);
#812=IFCORIENTEDEDGE(*,*,#1572,.T.);
#813=IFCORIENTEDEDGE(*,*,#1573,.T.);
#814=IFCORIENTEDEDGE(*,*,#1574,.T.);
#815=IFCORIENTEDEDGE(*,*,#1575,.T.);
#816=IFCORIENTEDEDGE(*,*,#1576,.T.);
#817=IFCORIENTEDEDGE(*,*,#1577,.T.);
#818=IFCORIENTEDEDGE(*,*,#1578,.T.);
#819=IFCORIENTEDEDGE(*,*,#1579,.T.);
#820=IFCORIENTEDEDGE(*,*,#1580,.T.);
#821=IFCORIENTEDEDGE(*,*,#1581,.T.);
#822=IFCORIENTEDEDGE(*,*,#1582,.T.);
#823=IFCORIENTEDEDGE(*,*,#1583,.T.);
#824=IFCORIENTEDEDGE(*,*,#1584,.T.);
#825=IFCORIENTEDEDGE(*,*,#1585,.T.);
#826=IFCORIENTEDEDGE(*,*,#1586,.T.);
#827=IFCORIENTEDEDGE(*,*,#1587,.T.);
#828=IFCORIENTEDEDGE(*,*,#1588,.T.);
#829=IFCORIENTEDEDGE(*,*,#1589,.T.);
#830=IFCORIENTEDEDGE(*,*,#1590,.T.);
#831=IFCORIENTEDEDGE(*,*,#1591,.T.);
#832=IFCORIENTEDEDGE(*,*,#1592,.T.);
#833=IFCORIENTEDEDGE(*,*,#1593,.T.);
#834=IFCORIENTEDEDGE(*,*,#1594,.T.);
#835=IFCORIENTEDEDGE(*,*,#1595,.T.);
#836=IFCORIENTEDEDGE(*,*,#1596,.T.);
#837=IFCORIENTEDEDGE(*,*,#1597,.T.);
#838=IFCORIENTEDEDGE(*,*,#1598,.T.);
#839=IFCORIENTEDEDGE(*,*,#1599,.T.);
#840=IFCORIENTEDEDGE(*,*,#1600,.T.);
#841=IFCORIENTEDEDGE(*,*,#1601,.T.);
#842=IFCORIENTEDEDGE(*,*,#1602,.T.);
#843=IFCORIENTEDEDGE(*,*,#1603,.T.);
#844=IFCORIENTEDEDGE(*,*,#1604,.T.);
#845=IFCORIENTEDEDGE(*,*,#1605,.T.);
#846=IFCORIENTEDEDGE(*,*,#1606,.T.);
#847=IFCORIENTEDEDGE(*,*,#1607,.T.);
#848=IFCORIENTEDEDGE(*,*,#1608,.T.);
#849=IFCORIENTEDEDGE(*,*,#1609,.T.);
#850=IFCORIENTEDEDGE(*,*,#1610,.T.);
#851=IFCORIENTEDEDGE(*,*,#1611,.T.);
#852=IFCORIENTEDEDGE(*,*,#1612,.T.);
#853=IFCORIENTEDEDGE(*,*,#1613,.T.);
#854=IFCORIENTEDEDGE(*,*,#1614,.T.);
#855=IFCORIENTEDEDGE(*,*,#1615,.T.);
#856=IFCORIENTEDEDGE(*,*,#1616,.T.);
#857=IFCORIENTEDEDGE(*,*,#1617,.T.);
#858=IFCORIENTEDEDGE(*,*,#1618,.T.);
#859=IFCORIENTEDEDGE(*,*,#1619,.T.);
#860=IFCORIENTEDEDGE(*,*,#1620,.T.);
#861=IFCORIENTEDEDGE(*,*,#1621,.T.);
#862=IFCORIENTEDEDGE(*,*,#1622,.T.);
#863=IFCORIENTEDEDGE(*,*,#1623,.T.);
#864=IFCORIENTEDEDGE(*,*,#1624,.T.);
#865=IFCORIENTEDEDGE(*,*,#1625,.T.);
#866=IFCORIENTEDEDGE(*,*,#1626,.T.);
#867=IFCORIENTEDEDGE(*,*,#1627,.T.);
#868=IFCORIENTEDEDGE(*,*,#1628,.T.);
#869=IFCORIENTEDEDGE(*,*,#1629,.T.);
#870=IFCORIENTEDEDGE(*,*,#1630,.T.);
#871=IFCORIENTEDEDGE(*,*,#1631,.T.);
#872=IFCORIENTEDEDGE(*,*,#1632,.T.);
#873=IFCORIENTEDEDGE(*,*,#1633,.T.);
#874=IFCORIENTEDEDGE(*,*,#1634,.T.);
#875=IFCORIENTEDEDGE(*,*,#1635,.T.);
#876=IFCORIENTEDEDGE(*,*,#1636,.T.);
#877=IFCORIENTEDEDGE(*,*,#1637,.T.);
#878=IFCORIENTEDEDGE(*,*,#1638,.T.);
#879=IFCORIENTEDEDGE(*,*,#1639,.T.);
#880=IFCORIENTEDEDGE(*,*,#1640,.T.);
#881=IFCORIENTEDEDGE(*,*,#1641,.F.);
#882=IFCORIENTEDEDGE(*,*,#1642,.F.);
#883=IFCEDGELOOP((#140,#141,#142,#143,#144,#145,#146,#147,#148,#149,#150,
#151,#152,#153,#154,#155,#156,#157,#158,#159,#160,#161,#162,#163));
#884=IFCEDGELOOP((#164,#165,#166,#167));
#885=IFCEDGELOOP((#168,#169,#170,#171));
#886=IFCEDGELOOP((#172,#173,#174,#175));
#887=IFCEDGELOOP((#176,#177,#178,#179));
#888=IFCEDGELOOP((#180,#181,#182,#183,#184,#185,#186,#187,#188,#189,#190,
#191,#192,#193,#194,#195,#196,#197,#198,#199,#200,#201,#202,#203,#204,#205,
#206,#207,#208,#209,#210,#211,#212,#213,#214,#215,#216,#217,#218,#219,#220,
#221,#222,#223,#224,#225,#226,#227,#228,#229,#230,#231,#232,#233,#234,#235,
#236,#237,#238,#239,#240,#241,#242,#243,#244,#245,#246,#247,#248,#249,#250,
#251,#252,#253,#254,#255,#256,#257,#258,#259,#260,#261,#262,#263,#264,#265,
#266,#267,#268,#269,#270,#271,#272,#273,#274,#275,#276,#277,#278,#279,#280,
#281,#282,#283,#284,#285,#286,#287,#288,#289,#290,#291,#292,#293,#294,#295,
#296,#297,#298,#299,#300,#301,#302,#303,#304,#305,#306,#307,#308,#309,#310,
#311,#312,#313,#314,#315,#316,#317,#318,#319,#320,#321,#322,#323,#324,#325,
#326,#327,#328,#329,#330,#331,#332,#333,#334,#335,#336,#337,#338,#339,#340,
#341,#342,#343,#344,#345,#346,#347,#348,#349,#350,#351,#352,#353,#354,#355,
#356,#357,#358,#359,#360,#361,#362,#363,#364,#365,#366,#367,#368,#369,#370,
#371,#372,#373,#374,#375,#376,#377,#378,#379,#380,#381,#382,#383,#384,#385,
#386,#387,#388,#389,#390,#391,#392,#393,#394,#395,#396,#397,#398,#399,#400,
#401,#402,#403,#404,#405,#406,#407,#408,#409,#410,#411,#412,#413,#414,#415,
#416,#417,#418,#419,#420,#421,#422,#423,#424,#425,#426,#427,#428,#429,#430,
#431,#432,#433,#434,#435,#436,#437,#438,#439,#440,#441,#442,#443,#444,#445,
#446,#447,#448,#449,#450,#451,#452,#453,#454,#455,#456,#457,#458,#459,#460,
#461,#462,#463,#464,#465,#466,#467,#468,#469,#470,#471,#472,#473,#474,#475,
#476,#477,#478,#479,#480,#481,#482,#483,#484,#485,#486,#487,#488,#489,#490,
#491,#492,#493,#494,#495,#496,#497,#498,#499,#500,#501,#502,#503,#504,#505,
#506,#507,#508,#509,#510,#511,#512,#513,#514,#515,#516,#517,#518,#519,#520,
#521,#522,#523,#524,#525,#526,#527,#528,#529,#530,#531,#532,#533,#534,#535,
#536,#537,#538,#539,#540,#541,#542,#543,#544,#545,#546,#547,#548,#549,#550,
#551,#552,#553,#554,#555,#556,#557,#558,#559));
#889=IFCEDGELOOP((#560,#561,#562,#563,#564,#565,#566,#567,#568,#569,#570,
#571,#572,#573,#574,#575,#576,#577,#578,#579,#580,#581,#582,#583,#584,#585,
#586,#587,#588,#589,#590,#591,#592,#593,#594,#595,#596,#597,#598,#599,#600,
#601,#602,#603,#604,#605,#606,#607,#608,#609,#610,#611,#612,#613,#614,#615,
#616,#617,#618,#619,#620,#621,#622,#623,#624,#625,#626,#627,#628,#629,#630,
#631,#632,#633,#634,#635,#636,#637,#638,#639,#640,#641,#642,#643,#644,#645,
#646,#647,#648,#649,#650,#651,#652,#653,#654,#655,#656,#657,#658,#659,#660,
#661,#662,#663,#664,#665,#666,#667,#668,#669,#670,#671,#672,#673,#674,#675,
#676,#677,#678,#679,#680,#681,#682,#683,#684,#685,#686,#687,#688,#689,#690,
#691,#692,#693,#694,#695,#696,#697,#698,#699,#700,#701,#702,#703,#704,#705,
#706,#707,#708,#709,#710,#711,#712,#713,#714,#715,#716,#717,#718,#719,#720,
#721,#722,#723,#724,#725,#726,#727,#728,#729,#730,#731,#732,#733,#734,#735,
#736,#737,#738,#739,#740,#741,#742,#743,#744,#745,#746,#747,#748,#749,#750,
#751,#752,#753,#754));
#890=IFCEDGELOOP((#755,#756,#757,#758));
#891=IFCEDGELOOP((#759,#760,#761,#762,#763,#764,#765,#766,#767,#768,#769,
#770,#771,#772,#773,#774));
#892=IFCEDGELOOP((#775,#776,#777,#778,#779,#780,#781,#782,#783,#784,#785,
#786,#787,#788,#789,#790));
#893=IFCEDGELOOP((#791,#792,#793,#794,#795,#796,#797,#798,#799,#800,#801,
#802));
#894=IFCEDGELOOP((#803,#804,#805,#806,#807,#808,#809,#810,#811,#812,#813,
#814,#815,#816,#817,#818));
#895=IFCEDGELOOP((#819,#820,#821,#822,#823,#824,#825,#826,#827,#828,#829,
#830));
#896=IFCEDGELOOP((#831,#832,#833,#834,#835,#836,#837,#838,#839,#840,#841,
#842,#843,#844,#845,#846));
#897=IFCEDGELOOP((#847,#848,#849,#850,#851,#852,#853,#854,#855,#856,#857,
#858,#859,#860,#861,#862));
#898=IFCEDGELOOP((#863,#864,#865,#866,#867,#868,#869,#870,#871,#872,#873,
#874,#875,#876,#877,#878));
#899=IFCEDGELOOP((#879,#880,#881,#882));
#900=IFCEDGECURVE(#1643,#1644,#2357,.T.);
#901=IFCEDGECURVE(#1644,#1645,#2358,.T.);
#902=IFCEDGECURVE(#1645,#1646,#2359,.T.);
#903=IFCEDGECURVE(#1646,#1647,#2360,.T.);
#904=IFCEDGECURVE(#1647,#1648,#2361,.T.);
#905=IFCEDGECURVE(#1648,#1649,#2362,.T.);
#906=IFCEDGECURVE(#1649,#1650,#2363,.T.);
#907=IFCEDGECURVE(#1650,#1651,#2364,.T.);
#908=IFCEDGECURVE(#1651,#1652,#2365,.T.);
#909=IFCEDGECURVE(#1652,#1653,#2366,.T.);
#910=IFCEDGECURVE(#1653,#1654,#2367,.T.);
#911=IFCEDGECURVE(#1654,#1655,#2368,.T.);
#912=IFCEDGECURVE(#1655,#1656,#2369,.T.);
#913=IFCEDGECURVE(#1656,#1657,#2370,.T.);
#914=IFCEDGECURVE(#1657,#1658,#2371,.T.);
#915=IFCEDGECURVE(#1658,#1659,#2372,.T.);
#916=IFCEDGECURVE(#1659,#1660,#2373,.T.);
#917=IFCEDGECURVE(#1660,#1661,#2374,.T.);
#918=IFCEDGECURVE(#1661,#1662,#2375,.T.);
#919=IFCEDGECURVE(#1662,#1663,#2376,.T.);
#920=IFCEDGECURVE(#1663,#1664,#2377,.T.);
#921=IFCEDGECURVE(#1664,#1665,#2378,.T.);
#922=IFCEDGECURVE(#1665,#1666,#2379,.T.);
#923=IFCEDGECURVE(#1666,#1643,#2380,.T.);
#924=IFCEDGECURVE(#1667,#1668,#2381,.T.);
#925=IFCEDGECURVE(#1668,#1669,#2382,.T.);
#926=IFCEDGECURVE(#1669,#1670,#2383,.T.);
#927=IFCEDGECURVE(#1670,#1667,#2384,.T.);
#928=IFCEDGECURVE(#1671,#1672,#2385,.T.);
#929=IFCEDGECURVE(#1672,#1672,#2386,.T.);
#930=IFCEDGECURVE(#1672,#1671,#2387,.T.);
#931=IFCEDGECURVE(#1671,#1671,#2388,.T.);
#932=IFCEDGECURVE(#1673,#1674,#2389,.T.);
#933=IFCEDGECURVE(#1674,#1674,#2390,.T.);
#934=IFCEDGECURVE(#1674,#1673,#2391,.T.);
#935=IFCEDGECURVE(#1673,#1673,#2392,.T.);
#936=IFCEDGECURVE(#1675,#1675,#2393,.T.);
#937=IFCEDGECURVE(#1675,#1676,#2394,.T.);
#938=IFCEDGECURVE(#1676,#1676,#2395,.T.);
#939=IFCEDGECURVE(#1676,#1675,#2396,.T.);
#940=IFCEDGECURVE(#1677,#1678,#2397,.T.);
#941=IFCEDGECURVE(#1678,#1679,#2398,.T.);
#942=IFCEDGECURVE(#1679,#1680,#2399,.T.);
#943=IFCEDGECURVE(#1680,#1681,#2400,.T.);
#944=IFCEDGECURVE(#1681,#1682,#2401,.T.);
#945=IFCEDGECURVE(#1682,#1683,#2402,.T.);
#946=IFCEDGECURVE(#1683,#1684,#2403,.T.);
#947=IFCEDGECURVE(#1684,#1685,#2404,.T.);
#948=IFCEDGECURVE(#1685,#1686,#2405,.T.);
#949=IFCEDGECURVE(#1686,#1687,#2406,.T.);
#950=IFCEDGECURVE(#1687,#1688,#2407,.T.);
#951=IFCEDGECURVE(#1688,#1689,#2408,.T.);
#952=IFCEDGECURVE(#1689,#1690,#2409,.T.);
#953=IFCEDGECURVE(#1690,#1691,#2410,.T.);
#954=IFCEDGECURVE(#1691,#1692,#2411,.T.);
#955=IFCEDGECURVE(#1692,#1693,#2412,.T.);
#956=IFCEDGECURVE(#1693,#1694,#2413,.T.);
#957=IFCEDGECURVE(#1694,#1695,#2414,.T.);
#958=IFCEDGECURVE(#1695,#1696,#2415,.T.);
#959=IFCEDGECURVE(#1696,#1697,#2416,.T.);
#960=IFCEDGECURVE(#1697,#1698,#2417,.T.);
#961=IFCEDGECURVE(#1698,#1699,#2418,.T.);
#962=IFCEDGECURVE(#1699,#1700,#2419,.T.);
#963=IFCEDGECURVE(#1700,#1701,#2420,.T.);
#964=IFCEDGECURVE(#1701,#1702,#2421,.T.);
#965=IFCEDGECURVE(#1702,#1703,#2422,.T.);
#966=IFCEDGECURVE(#1703,#1704,#2423,.T.);
#967=IFCEDGECURVE(#1704,#1705,#2424,.T.);
#968=IFCEDGECURVE(#1705,#1706,#2425,.T.);
#969=IFCEDGECURVE(#1706,#1707,#2426,.T.);
#970=IFCEDGECURVE(#1707,#1708,#2427,.T.);
#971=IFCEDGECURVE(#1708,#1709,#2428,.T.);
#972=IFCEDGECURVE(#1709,#1710,#2429,.T.);
#973=IFCEDGECURVE(#1710,#1711,#2430,.T.);
#974=IFCEDGECURVE(#1711,#1712,#2431,.T.);
#975=IFCEDGECURVE(#1712,#1713,#2432,.T.);
#976=IFCEDGECURVE(#1713,#1714,#2433,.T.);
#977=IFCEDGECURVE(#1714,#1715,#2434,.T.);
#978=IFCEDGECURVE(#1715,#1716,#2435,.T.);
#979=IFCEDGECURVE(#1716,#1717,#2436,.T.);
#980=IFCEDGECURVE(#1717,#1718,#2437,.T.);
#981=IFCEDGECURVE(#1718,#1719,#2438,.T.);
#982=IFCEDGECURVE(#1719,#1720,#2439,.T.);
#983=IFCEDGECURVE(#1720,#1721,#2440,.T.);
#984=IFCEDGECURVE(#1721,#1722,#2441,.T.);
#985=IFCEDGECURVE(#1722,#1723,#2442,.T.);
#986=IFCEDGECURVE(#1723,#1724,#2443,.T.);
#987=IFCEDGECURVE(#1724,#1725,#2444,.T.);
#988=IFCEDGECURVE(#1725,#1726,#2445,.T.);
#989=IFCEDGECURVE(#1726,#1727,#2446,.T.);
#990=IFCEDGECURVE(#1727,#1728,#2447,.T.);
#991=IFCEDGECURVE(#1728,#1729,#2448,.T.);
#992=IFCEDGECURVE(#1729,#1730,#2449,.T.);
#993=IFCEDGECURVE(#1730,#1731,#2450,.T.);
#994=IFCEDGECURVE(#1731,#1732,#2451,.T.);
#995=IFCEDGECURVE(#1732,#1733,#2452,.T.);
#996=IFCEDGECURVE(#1733,#1734,#2453,.T.);
#997=IFCEDGECURVE(#1734,#1735,#2454,.T.);
#998=IFCEDGECURVE(#1735,#1736,#2455,.T.);
#999=IFCEDGECURVE(#1736,#1737,#2456,.T.);
#1000=IFCEDGECURVE(#1737,#1738,#2457,.T.);
#1001=IFCEDGECURVE(#1738,#1739,#2458,.T.);
#1002=IFCEDGECURVE(#1739,#1740,#2459,.T.);
#1003=IFCEDGECURVE(#1740,#1741,#2460,.T.);
#1004=IFCEDGECURVE(#1741,#1742,#2461,.T.);
#1005=IFCEDGECURVE(#1742,#1743,#2462,.T.);
#1006=IFCEDGECURVE(#1743,#1744,#2463,.T.);
#1007=IFCEDGECURVE(#1744,#1745,#2464,.T.);
#1008=IFCEDGECURVE(#1745,#1746,#2465,.T.);
#1009=IFCEDGECURVE(#1746,#1747,#2466,.T.);
#1010=IFCEDGECURVE(#1747,#1748,#2467,.T.);
#1011=IFCEDGECURVE(#1748,#1749,#2468,.T.);
#1012=IFCEDGECURVE(#1749,#1750,#2469,.T.);
#1013=IFCEDGECURVE(#1750,#1751,#2470,.T.);
#1014=IFCEDGECURVE(#1751,#1752,#2471,.T.);
#1015=IFCEDGECURVE(#1752,#1753,#2472,.T.);
#1016=IFCEDGECURVE(#1753,#1754,#2473,.T.);
#1017=IFCEDGECURVE(#1754,#1755,#2474,.T.);
#1018=IFCEDGECURVE(#1755,#1756,#2475,.T.);
#1019=IFCEDGECURVE(#1756,#1757,#2476,.T.);
#1020=IFCEDGECURVE(#1757,#1758,#2477,.T.);
#1021=IFCEDGECURVE(#1758,#1759,#2478,.T.);
#1022=IFCEDGECURVE(#1759,#1760,#2479,.T.);
#1023=IFCEDGECURVE(#1760,#1761,#2480,.T.);
#1024=IFCEDGECURVE(#1761,#1762,#2481,.T.);
#1025=IFCEDGECURVE(#1762,#1763,#2482,.T.);
#1026=IFCEDGECURVE(#1763,#1764,#2483,.T.);
#1027=IFCEDGECURVE(#1764,#1765,#2484,.T.);
#1028=IFCEDGECURVE(#1765,#1766,#2485,.T.);
#1029=IFCEDGECURVE(#1766,#1767,#2486,.T.);
#1030=IFCEDGECURVE(#1767,#1768,#2487,.T.);
#1031=IFCEDGECURVE(#1768,#1769,#2488,.T.);
#1032=IFCEDGECURVE(#1769,#1770,#2489,.T.);
#1033=IFCEDGECURVE(#1770,#1771,#2490,.T.);
#1034=IFCEDGECURVE(#1771,#1772,#2491,.T.);
#1035=IFCEDGECURVE(#1772,#1773,#2492,.T.);
#1036=IFCEDGECURVE(#1773,#1774,#2493,.T.);
#1037=IFCEDGECURVE(#1774,#1775,#2494,.T.);
#1038=IFCEDGECURVE(#1775,#1776,#2495,.T.);
#1039=IFCEDGECURVE(#1776,#1777,#2496,.T.);
#1040=IFCEDGECURVE(#1777,#1778,#2497,.T.);
#1041=IFCEDGECURVE(#1778,#1779,#2498,.T.);
#1042=IFCEDGECURVE(#1779,#1780,#2499,.T.);
#1043=IFCEDGECURVE(#1780,#1781,#2500,.T.);
#1044=IFCEDGECURVE(#1781,#1782,#2501,.T.);
#1045=IFCEDGECURVE(#1782,#1783,#2502,.T.);
#1046=IFCEDGECURVE(#1783,#1784,#2503,.T.);
#1047=IFCEDGECURVE(#1784,#1785,#2504,.T.);
#1048=IFCEDGECURVE(#1785,#1786,#2505,.T.);
#1049=IFCEDGECURVE(#1786,#1787,#2506,.T.);
#1050=IFCEDGECURVE(#1787,#1788,#2507,.T.);
#1051=IFCEDGECURVE(#1788,#1789,#2508,.T.);
#1052=IFCEDGECURVE(#1789,#1790,#2509,.T.);
#1053=IFCEDGECURVE(#1790,#1791,#2510,.T.);
#1054=IFCEDGECURVE(#1791,#1792,#2511,.T.);
#1055=IFCEDGECURVE(#1792,#1793,#2512,.T.);
#1056=IFCEDGECURVE(#1793,#1794,#2513,.T.);
#1057=IFCEDGECURVE(#1794,#1795,#2514,.T.);
#1058=IFCEDGECURVE(#1795,#1796,#2515,.T.);
#1059=IFCEDGECURVE(#1796,#1797,#2516,.T.);
#1060=IFCEDGECURVE(#1797,#1798,#2517,.T.);
#1061=IFCEDGECURVE(#1798,#1799,#2518,.T.);
#1062=IFCEDGECURVE(#1799,#1800,#2519,.T.);
#1063=IFCEDGECURVE(#1800,#1801,#2520,.T.);
#1064=IFCEDGECURVE(#1801,#1802,#2521,.T.);
#1065=IFCEDGECURVE(#1802,#1803,#2522,.T.);
#1066=IFCEDGECURVE(#1803,#1804,#2523,.T.);
#1067=IFCEDGECURVE(#1804,#1805,#2524,.T.);
#1068=IFCEDGECURVE(#1805,#1806,#2525,.T.);
#1069=IFCEDGECURVE(#1806,#1807,#2526,.T.);
#1070=IFCEDGECURVE(#1807,#1808,#2527,.T.);
#1071=IFCEDGECURVE(#1808,#1809,#2528,.T.);
#1072=IFCEDGECURVE(#1809,#1810,#2529,.T.);
#1073=IFCEDGECURVE(#1810,#1811,#2530,.T.);
#1074=IFCEDGECURVE(#1811,#1812,#2531,.T.);
#1075=IFCEDGECURVE(#1812,#1813,#2532,.T.);
#1076=IFCEDGECURVE(#1813,#1814,#2533,.T.);
#1077=IFCEDGECURVE(#1814,#1815,#2534,.T.);
#1078=IFCEDGECURVE(#1815,#1816,#2535,.T.);
#1079=IFCEDGECURVE(#1816,#1817,#2536,.T.);
#1080=IFCEDGECURVE(#1817,#1818,#2537,.T.);
#1081=IFCEDGECURVE(#1818,#1819,#2538,.T.);
#1082=IFCEDGECURVE(#1819,#1820,#2539,.T.);
#1083=IFCEDGECURVE(#1820,#1821,#2540,.T.);
#1084=IFCEDGECURVE(#1821,#1822,#2541,.T.);
#1085=IFCEDGECURVE(#1822,#1823,#2542,.T.);
#1086=IFCEDGECURVE(#1823,#1824,#2543,.T.);
#1087=IFCEDGECURVE(#1824,#1825,#2544,.T.);
#1088=IFCEDGECURVE(#1825,#1826,#2545,.T.);
#1089=IFCEDGECURVE(#1826,#1827,#2546,.T.);
#1090=IFCEDGECURVE(#1827,#1828,#2547,.T.);
#1091=IFCEDGECURVE(#1828,#1829,#2548,.T.);
#1092=IFCEDGECURVE(#1829,#1830,#2549,.T.);
#1093=IFCEDGECURVE(#1830,#1831,#2550,.T.);
#1094=IFCEDGECURVE(#1831,#1832,#2551,.T.);
#1095=IFCEDGECURVE(#1832,#1833,#2552,.T.);
#1096=IFCEDGECURVE(#1833,#1834,#2553,.T.);
#1097=IFCEDGECURVE(#1834,#1835,#2554,.T.);
#1098=IFCEDGECURVE(#1835,#1836,#2555,.T.);
#1099=IFCEDGECURVE(#1836,#1837,#2556,.T.);
#1100=IFCEDGECURVE(#1837,#1838,#2557,.T.);
#1101=IFCEDGECURVE(#1838,#1839,#2558,.T.);
#1102=IFCEDGECURVE(#1839,#1840,#2559,.T.);
#1103=IFCEDGECURVE(#1840,#1841,#2560,.T.);
#1104=IFCEDGECURVE(#1841,#1842,#2561,.T.);
#1105=IFCEDGECURVE(#1842,#1843,#2562,.T.);
#1106=IFCEDGECURVE(#1843,#1844,#2563,.T.);
#1107=IFCEDGECURVE(#1844,#1845,#2564,.T.);
#1108=IFCEDGECURVE(#1845,#1846,#2565,.T.);
#1109=IFCEDGECURVE(#1846,#1847,#2566,.T.);
#1110=IFCEDGECURVE(#1847,#1848,#2567,.T.);
#1111=IFCEDGECURVE(#1848,#1849,#2568,.T.);
#1112=IFCEDGECURVE(#1849,#1850,#2569,.T.);
#1113=IFCEDGECURVE(#1850,#1851,#2570,.T.);
#1114=IFCEDGECURVE(#1851,#1852,#2571,.T.);
#1115=IFCEDGECURVE(#1852,#1853,#2572,.T.);
#1116=IFCEDGECURVE(#1853,#1854,#2573,.T.);
#1117=IFCEDGECURVE(#1854,#1855,#2574,.T.);
#1118=IFCEDGECURVE(#1855,#1856,#2575,.T.);
#1119=IFCEDGECURVE(#1856,#1857,#2576,.T.);
#1120=IFCEDGECURVE(#1857,#1858,#2577,.T.);
#1121=IFCEDGECURVE(#1858,#1859,#2578,.T.);
#1122=IFCEDGECURVE(#1859,#1860,#2579,.T.);
#1123=IFCEDGECURVE(#1860,#1861,#2580,.T.);
#1124=IFCEDGECURVE(#1861,#1862,#2581,.T.);
#1125=IFCEDGECURVE(#1862,#1863,#2582,.T.);
#1126=IFCEDGECURVE(#1863,#1864,#2583,.T.);
#1127=IFCEDGECURVE(#1864,#1865,#2584,.T.);
#1128=IFCEDGECURVE(#1865,#1866,#2585,.T.);
#1129=IFCEDGECURVE(#1866,#1867,#2586,.T.);
#1130=IFCEDGECURVE(#1867,#1868,#2587,.T.);
#1131=IFCEDGECURVE(#1868,#1869,#2588,.T.);
#1132=IFCEDGECURVE(#1869,#1870,#2589,.T.);
#1133=IFCEDGECURVE(#1870,#1871,#2590,.T.);
#1134=IFCEDGECURVE(#1871,#1872,#2591,.T.);
#1135=IFCEDGECURVE(#1872,#1873,#2592,.T.);
#1136=IFCEDGECURVE(#1873,#1874,#2593,.T.);
#1137=IFCEDGECURVE(#1874,#1875,#2594,.T.);
#1138=IFCEDGECURVE(#1875,#1876,#2595,.T.);
#1139=IFCEDGECURVE(#1876,#1877,#2596,.T.);
#1140=IFCEDGECURVE(#1877,#1878,#2597,.T.);
#1141=IFCEDGECURVE(#1878,#1879,#2598,.T.);
#1142=IFCEDGECURVE(#1879,#1880,#2599,.T.);
#1143=IFCEDGECURVE(#1880,#1881,#2600,.T.);
#1144=IFCEDGECURVE(#1881,#1882,#2601,.T.);
#1145=IFCEDGECURVE(#1882,#1883,#2602,.T.);
#1146=IFCEDGECURVE(#1883,#1884,#2603,.T.);
#1147=IFCEDGECURVE(#1884,#1885,#2604,.T.);
#1148=IFCEDGECURVE(#1885,#1886,#2605,.T.);
#1149=IFCEDGECURVE(#1886,#1887,#2606,.T.);
#1150=IFCEDGECURVE(#1887,#1888,#2607,.T.);
#1151=IFCEDGECURVE(#1888,#1889,#2608,.T.);
#1152=IFCEDGECURVE(#1889,#1890,#2609,.T.);
#1153=IFCEDGECURVE(#1890,#1891,#2610,.T.);
#1154=IFCEDGECURVE(#1891,#1892,#2611,.T.);
#1155=IFCEDGECURVE(#1892,#1893,#2612,.T.);
#1156=IFCEDGECURVE(#1893,#1894,#2613,.T.);
#1157=IFCEDGECURVE(#1894,#1895,#2614,.T.);
#1158=IFCEDGECURVE(#1895,#1896,#2615,.T.);
#1159=IFCEDGECURVE(#1896,#1897,#2616,.T.);
#1160=IFCEDGECURVE(#1897,#1898,#2617,.T.);
#1161=IFCEDGECURVE(#1898,#1899,#2618,.T.);
#1162=IFCEDGECURVE(#1899,#1900,#2619,.T.);
#1163=IFCEDGECURVE(#1900,#1901,#2620,.T.);
#1164=IFCEDGECURVE(#1901,#1902,#2621,.T.);
#1165=IFCEDGECURVE(#1902,#1903,#2622,.T.);
#1166=IFCEDGECURVE(#1903,#1904,#2623,.T.);
#1167=IFCEDGECURVE(#1904,#1905,#2624,.T.);
#1168=IFCEDGECURVE(#1905,#1906,#2625,.T.);
#1169=IFCEDGECURVE(#1906,#1907,#2626,.T.);
#1170=IFCEDGECURVE(#1907,#1908,#2627,.T.);
#1171=IFCEDGECURVE(#1908,#1909,#2628,.T.);
#1172=IFCEDGECURVE(#1909,#1910,#2629,.T.);
#1173=IFCEDGECURVE(#1910,#1911,#2630,.T.);
#1174=IFCEDGECURVE(#1911,#1912,#2631,.T.);
#1175=IFCEDGECURVE(#1912,#1913,#2632,.T.);
#1176=IFCEDGECURVE(#1913,#1914,#2633,.T.);
#1177=IFCEDGECURVE(#1914,#1915,#2634,.T.);
#1178=IFCEDGECURVE(#1915,#1916,#2635,.T.);
#1179=IFCEDGECURVE(#1916,#1917,#2636,.T.);
#1180=IFCEDGECURVE(#1917,#1918,#2637,.T.);
#1181=IFCEDGECURVE(#1918,#1919,#2638,.T.);
#1182=IFCEDGECURVE(#1919,#1920,#2639,.T.);
#1183=IFCEDGECURVE(#1920,#1921,#2640,.T.);
#1184=IFCEDGECURVE(#1921,#1922,#2641,.T.);
#1185=IFCEDGECURVE(#1922,#1923,#2642,.T.);
#1186=IFCEDGECURVE(#1923,#1924,#2643,.T.);
#1187=IFCEDGECURVE(#1924,#1925,#2644,.T.);
#1188=IFCEDGECURVE(#1925,#1926,#2645,.T.);
#1189=IFCEDGECURVE(#1926,#1927,#2646,.T.);
#1190=IFCEDGECURVE(#1927,#1928,#2647,.T.);
#1191=IFCEDGECURVE(#1928,#1929,#2648,.T.);
#1192=IFCEDGECURVE(#1929,#1930,#2649,.T.);
#1193=IFCEDGECURVE(#1930,#1931,#2650,.T.);
#1194=IFCEDGECURVE(#1931,#1932,#2651,.T.);
#1195=IFCEDGECURVE(#1932,#1933,#2652,.T.);
#1196=IFCEDGECURVE(#1933,#1934,#2653,.T.);
#1197=IFCEDGECURVE(#1934,#1935,#2654,.T.);
#1198=IFCEDGECURVE(#1935,#1936,#2655,.T.);
#1199=IFCEDGECURVE(#1936,#1937,#2656,.T.);
#1200=IFCEDGECURVE(#1937,#1938,#2657,.T.);
#1201=IFCEDGECURVE(#1938,#1939,#2658,.T.);
#1202=IFCEDGECURVE(#1939,#1940,#2659,.T.);
#1203=IFCEDGECURVE(#1940,#1941,#2660,.T.);
#1204=IFCEDGECURVE(#1941,#1942,#2661,.T.);
#1205=IFCEDGECURVE(#1942,#1943,#2662,.T.);
#1206=IFCEDGECURVE(#1943,#1944,#2663,.T.);
#1207=IFCEDGECURVE(#1944,#1945,#2664,.T.);
#1208=IFCEDGECURVE(#1945,#1946,#2665,.T.);
#1209=IFCEDGECURVE(#1946,#1947,#2666,.T.);
#1210=IFCEDGECURVE(#1947,#1948,#2667,.T.);
#1211=IFCEDGECURVE(#1948,#1949,#2668,.T.);
#1212=IFCEDGECURVE(#1949,#1950,#2669,.T.);
#1213=IFCEDGECURVE(#1950,#1951,#2670,.T.);
#1214=IFCEDGECURVE(#1951,#1952,#2671,.T.);
#1215=IFCEDGECURVE(#1952,#1953,#2672,.T.);
#1216=IFCEDGECURVE(#1953,#1954,#2673,.T.);
#1217=IFCEDGECURVE(#1954,#1955,#2674,.T.);
#1218=IFCEDGECURVE(#1955,#1956,#2675,.T.);
#1219=IFCEDGECURVE(#1956,#1957,#2676,.T.);
#1220=IFCEDGECURVE(#1957,#1958,#2677,.T.);
#1221=IFCEDGECURVE(#1958,#1959,#2678,.T.);
#1222=IFCEDGECURVE(#1959,#1960,#2679,.T.);
#1223=IFCEDGECURVE(#1960,#1961,#2680,.T.);
#1224=IFCEDGECURVE(#1961,#1962,#2681,.T.);
#1225=IFCEDGECURVE(#1962,#1963,#2682,.T.);
#1226=IFCEDGECURVE(#1963,#1964,#2683,.T.);
#1227=IFCEDGECURVE(#1964,#1965,#2684,.T.);
#1228=IFCEDGECURVE(#1965,#1966,#2685,.T.);
#1229=IFCEDGECURVE(#1966,#1967,#2686,.T.);
#1230=IFCEDGECURVE(#1967,#1968,#2687,.T.);
#1231=IFCEDGECURVE(#1968,#1969,#2688,.T.);
#1232=IFCEDGECURVE(#1969,#1970,#2689,.T.);
#1233=IFCEDGECURVE(#1970,#1971,#2690,.T.);
#1234=IFCEDGECURVE(#1971,#1972,#2691,.T.);
#1235=IFCEDGECURVE(#1972,#1973,#2692,.T.);
#1236=IFCEDGECURVE(#1973,#1974,#2693,.T.);
#1237=IFCEDGECURVE(#1974,#1975,#2694,.T.);
#1238=IFCEDGECURVE(#1975,#1976,#2695,.T.);
#1239=IFCEDGECURVE(#1976,#1977,#2696,.T.);
#1240=IFCEDGECURVE(#1977,#1978,#2697,.T.);
#1241=IFCEDGECURVE(#1978,#1979,#2698,.T.);
#1242=IFCEDGECURVE(#1979,#1980,#2699,.T.);
#1243=IFCEDGECURVE(#1980,#1981,#2700,.T.);
#1244=IFCEDGECURVE(#1981,#1982,#2701,.T.);
#1245=IFCEDGECURVE(#1982,#1983,#2702,.T.);
#1246=IFCEDGECURVE(#1983,#1984,#2703,.T.);
#1247=IFCEDGECURVE(#1984,#1985,#2704,.T.);
#1248=IFCEDGECURVE(#1985,#1986,#2705,.T.);
#1249=IFCEDGECURVE(#1986,#1987,#2706,.T.);
#1250=IFCEDGECURVE(#1987,#1988,#2707,.T.);
#1251=IFCEDGECURVE(#1988,#1989,#2708,.T.);
#1252=IFCEDGECURVE(#1989,#1990,#2709,.T.);
#1253=IFCEDGECURVE(#1990,#1991,#2710,.T.);
#1254=IFCEDGECURVE(#1991,#1992,#2711,.T.);
#1255=IFCEDGECURVE(#1992,#1993,#2712,.T.);
#1256=IFCEDGECURVE(#1993,#1994,#2713,.T.);
#1257=IFCEDGECURVE(#1994,#1995,#2714,.T.);
#1258=IFCEDGECURVE(#1995,#1996,#2715,.T.);
#1259=IFCEDGECURVE(#1996,#1997,#2716,.T.);
#1260=IFCEDGECURVE(#1997,#1998,#2717,.T.);
#1261=IFCEDGECURVE(#1998,#1999,#2718,.T.);
#1262=IFCEDGECURVE(#1999,#2000,#2719,.T.);
#1263=IFCEDGECURVE(#2000,#2001,#2720,.T.);
#1264=IFCEDGECURVE(#2001,#2002,#2721,.T.);
#1265=IFCEDGECURVE(#2002,#2003,#2722,.T.);
#1266=IFCEDGECURVE(#2003,#2004,#2723,.T.);
#1267=IFCEDGECURVE(#2004,#2005,#2724,.T.);
#1268=IFCEDGECURVE(#2005,#2006,#2725,.T.);
#1269=IFCEDGECURVE(#2006,#2007,#2726,.T.);
#1270=IFCEDGECURVE(#2007,#2008,#2727,.T.);
#1271=IFCEDGECURVE(#2008,#2009,#2728,.T.);
#1272=IFCEDGECURVE(#2009,#2010,#2729,.T.);
#1273=IFCEDGECURVE(#2010,#2011,#2730,.T.);
#1274=IFCEDGECURVE(#2011,#2012,#2731,.T.);
#1275=IFCEDGECURVE(#2012,#2013,#2732,.T.);
#1276=IFCEDGECURVE(#2013,#2014,#2733,.T.);
#1277=IFCEDGECURVE(#2014,#2015,#2734,.T.);
#1278=IFCEDGECURVE(#2015,#2016,#2735,.T.);
#1279=IFCEDGECURVE(#2016,#2017,#2736,.T.);
#1280=IFCEDGECURVE(#2017,#2018,#2737,.T.);
#1281=IFCEDGECURVE(#2018,#2019,#2738,.T.);
#1282=IFCEDGECURVE(#2019,#2020,#2739,.T.);
#1283=IFCEDGECURVE(#2020,#2021,#2740,.T.);
#1284=IFCEDGECURVE(#2021,#2022,#2741,.T.);
#1285=IFCEDGECURVE(#2022,#2023,#2742,.T.);
#1286=IFCEDGECURVE(#2023,#2024,#2743,.T.);
#1287=IFCEDGECURVE(#2024,#2025,#2744,.T.);
#1288=IFCEDGECURVE(#2025,#2026,#2745,.T.);
#1289=IFCEDGECURVE(#2026,#2027,#2746,.T.);
#1290=IFCEDGECURVE(#2027,#2028,#2747,.T.);
#1291=IFCEDGECURVE(#2028,#2029,#2748,.T.);
#1292=IFCEDGECURVE(#2029,#2030,#2749,.T.);
#1293=IFCEDGECURVE(#2030,#2031,#2750,.T.);
#1294=IFCEDGECURVE(#2031,#2032,#2751,.T.);
#1295=IFCEDGECURVE(#2032,#2033,#2752,.T.);
#1296=IFCEDGECURVE(#2033,#2034,#2753,.T.);
#1297=IFCEDGECURVE(#2034,#2035,#2754,.T.);
#1298=IFCEDGECURVE(#2035,#2036,#2755,.T.);
#1299=IFCEDGECURVE(#2036,#2037,#2756,.T.);
#1300=IFCEDGECURVE(#2037,#2038,#2757,.T.);
#1301=IFCEDGECURVE(#2038,#2039,#2758,.T.);
#1302=IFCEDGECURVE(#2039,#2040,#2759,.T.);
#1303=IFCEDGECURVE(#2040,#2041,#2760,.T.);
#1304=IFCEDGECURVE(#2041,#2042,#2761,.T.);
#1305=IFCEDGECURVE(#2042,#2043,#2762,.T.);
#1306=IFCEDGECURVE(#2043,#2044,#2763,.T.);
#1307=IFCEDGECURVE(#2044,#2045,#2764,.T.);
#1308=IFCEDGECURVE(#2045,#2046,#2765,.T.);
#1309=IFCEDGECURVE(#2046,#2047,#2766,.T.);
#1310=IFCEDGECURVE(#2047,#2048,#2767,.T.);
#1311=IFCEDGECURVE(#2048,#2049,#2768,.T.);
#1312=IFCEDGECURVE(#2049,#2050,#2769,.T.);
#1313=IFCEDGECURVE(#2050,#2051,#2770,.T.);
#1314=IFCEDGECURVE(#2051,#2052,#2771,.T.);
#1315=IFCEDGECURVE(#2052,#2053,#2772,.T.);
#1316=IFCEDGECURVE(#2053,#2054,#2773,.T.);
#1317=IFCEDGECURVE(#2054,#2055,#2774,.T.);
#1318=IFCEDGECURVE(#2055,#2056,#2775,.T.);
#1319=IFCEDGECURVE(#2056,#1677,#2776,.T.);
#1320=IFCEDGECURVE(#2057,#2058,#2777,.T.);
#1321=IFCEDGECURVE(#2058,#2059,#2778,.T.);
#1322=IFCEDGECURVE(#2059,#2060,#2779,.T.);
#1323=IFCEDGECURVE(#2060,#2061,#2780,.T.);
#1324=IFCEDGECURVE(#2061,#2062,#2781,.T.);
#1325=IFCEDGECURVE(#2062,#2063,#2782,.T.);
#1326=IFCEDGECURVE(#2063,#2064,#2783,.T.);
#1327=IFCEDGECURVE(#2064,#2065,#2784,.T.);
#1328=IFCEDGECURVE(#2065,#2066,#2785,.T.);
#1329=IFCEDGECURVE(#2066,#2067,#2786,.T.);
#1330=IFCEDGECURVE(#2067,#2068,#2787,.T.);
#1331=IFCEDGECURVE(#2068,#2069,#2788,.T.);
#1332=IFCEDGECURVE(#2069,#2070,#2789,.T.);
#1333=IFCEDGECURVE(#2070,#2071,#2790,.T.);
#1334=IFCEDGECURVE(#2071,#2072,#2791,.T.);
#1335=IFCEDGECURVE(#2072,#2073,#2792,.T.);
#1336=IFCEDGECURVE(#2073,#2074,#2793,.T.);
#1337=IFCEDGECURVE(#2074,#2075,#2794,.T.);
#1338=IFCEDGECURVE(#2075,#2076,#2795,.T.);
#1339=IFCEDGECURVE(#2076,#2077,#2796,.T.);
#1340=IFCEDGECURVE(#2077,#2078,#2797,.T.);
#1341=IFCEDGECURVE(#2078,#2079,#2798,.T.);
#1342=IFCEDGECURVE(#2079,#2080,#2799,.T.);
#1343=IFCEDGECURVE(#2080,#2081,#2800,.T.);
#1344=IFCEDGECURVE(#2081,#2082,#2801,.T.);
#1345=IFCEDGECURVE(#2082,#2083,#2802,.T.);
#1346=IFCEDGECURVE(#2083,#2084,#2803,.T.);
#1347=IFCEDGECURVE(#2084,#2085,#2804,.T.);
#1348=IFCEDGECURVE(#2085,#2086,#2805,.T.);
#1349=IFCEDGECURVE(#2086,#2087,#2806,.T.);
#1350=IFCEDGECURVE(#2087,#2088,#2807,.T.);
#1351=IFCEDGECURVE(#2088,#2089,#2808,.T.);
#1352=IFCEDGECURVE(#2089,#2090,#2809,.T.);
#1353=IFCEDGECURVE(#2090,#2091,#2810,.T.);
#1354=IFCEDGECURVE(#2091,#2092,#2811,.T.);
#1355=IFCEDGECURVE(#2092,#2093,#2812,.T.);
#1356=IFCEDGECURVE(#2093,#2094,#2813,.T.);
#1357=IFCEDGECURVE(#2094,#2095,#2814,.T.);
#1358=IFCEDGECURVE(#2095,#2096,#2815,.T.);
#1359=IFCEDGECURVE(#2096,#2097,#2816,.T.);
#1360=IFCEDGECURVE(#2097,#2098,#2817,.T.);
#1361=IFCEDGECURVE(#2098,#2099,#2818,.T.);
#1362=IFCEDGECURVE(#2099,#2100,#2819,.T.);
#1363=IFCEDGECURVE(#2100,#2101,#2820,.T.);
#1364=IFCEDGECURVE(#2101,#2102,#2821,.T.);
#1365=IFCEDGECURVE(#2102,#2103,#2822,.T.);
#1366=IFCEDGECURVE(#2103,#2104,#2823,.T.);
#1367=IFCEDGECURVE(#2104,#2105,#2824,.T.);
#1368=IFCEDGECURVE(#2105,#2106,#2825,.T.);
#1369=IFCEDGECURVE(#2106,#2107,#2826,.T.);
#1370=IFCEDGECURVE(#2107,#2108,#2827,.T.);
#1371=IFCEDGECURVE(#2108,#2109,#2828,.T.);
#1372=IFCEDGECURVE(#2109,#2110,#2829,.T.);
#1373=IFCEDGECURVE(#2110,#2111,#2830,.T.);
#1374=IFCEDGECURVE(#2111,#2112,#2831,.T.);
#1375=IFCEDGECURVE(#2112,#2113,#2832,.T.);
#1376=IFCEDGECURVE(#2113,#2114,#2833,.T.);
#1377=IFCEDGECURVE(#2114,#2115,#2834,.T.);
#1378=IFCEDGECURVE(#2115,#2116,#2835,.T.);
#1379=IFCEDGECURVE(#2116,#2117,#2836,.T.);
#1380=IFCEDGECURVE(#2117,#2118,#2837,.T.);
#1381=IFCEDGECURVE(#2118,#2119,#2838,.T.);
#1382=IFCEDGECURVE(#2119,#2120,#2839,.T.);
#1383=IFCEDGECURVE(#2120,#2121,#2840,.T.);
#1384=IFCEDGECURVE(#2121,#2122,#2841,.T.);
#1385=IFCEDGECURVE(#2122,#2123,#2842,.T.);
#1386=IFCEDGECURVE(#2123,#2124,#2843,.T.);
#1387=IFCEDGECURVE(#2124,#2125,#2844,.T.);
#1388=IFCEDGECURVE(#2125,#2126,#2845,.T.);
#1389=IFCEDGECURVE(#2126,#2127,#2846,.T.);
#1390=IFCEDGECURVE(#2127,#2128,#2847,.T.);
#1391=IFCEDGECURVE(#2128,#2129,#2848,.T.);
#1392=IFCEDGECURVE(#2129,#2130,#2849,.T.);
#1393=IFCEDGECURVE(#2130,#2131,#2850,.T.);
#1394=IFCEDGECURVE(#2131,#2132,#2851,.T.);
#1395=IFCEDGECURVE(#2132,#2133,#2852,.T.);
#1396=IFCEDGECURVE(#2133,#2134,#2853,.T.);
#1397=IFCEDGECURVE(#2134,#2135,#2854,.T.);
#1398=IFCEDGECURVE(#2135,#2136,#2855,.T.);
#1399=IFCEDGECURVE(#2136,#2137,#2856,.T.);
#1400=IFCEDGECURVE(#2137,#2138,#2857,.T.);
#1401=IFCEDGECURVE(#2138,#2139,#2858,.T.);
#1402=IFCEDGECURVE(#2139,#2140,#2859,.T.);
#1403=IFCEDGECURVE(#2140,#2141,#2860,.T.);
#1404=IFCEDGECURVE(#2141,#2142,#2861,.T.);
#1405=IFCEDGECURVE(#2142,#2143,#2862,.T.);
#1406=IFCEDGECURVE(#2143,#2144,#2863,.T.);
#1407=IFCEDGECURVE(#2144,#2145,#2864,.T.);
#1408=IFCEDGECURVE(#2145,#2146,#2865,.T.);
#1409=IFCEDGECURVE(#2146,#2147,#2866,.T.);
#1410=IFCEDGECURVE(#2147,#2148,#2867,.T.);
#1411=IFCEDGECURVE(#2148,#2149,#2868,.T.);
#1412=IFCEDGECURVE(#2149,#2150,#2869,.T.);
#1413=IFCEDGECURVE(#2150,#2151,#2870,.T.);
#1414=IFCEDGECURVE(#2151,#2152,#2871,.T.);
#1415=IFCEDGECURVE(#2152,#2153,#2872,.T.);
#1416=IFCEDGECURVE(#2153,#2154,#2873,.T.);
#1417=IFCEDGECURVE(#2154,#2155,#2874,.T.);
#1418=IFCEDGECURVE(#2155,#2156,#2875,.T.);
#1419=IFCEDGECURVE(#2156,#2157,#2876,.T.);
#1420=IFCEDGECURVE(#2157,#2158,#2877,.T.);
#1421=IFCEDGECURVE(#2158,#2159,#2878,.T.);
#1422=IFCEDGECURVE(#2159,#2160,#2879,.T.);
#1423=IFCEDGECURVE(#2160,#2161,#2880,.T.);
#1424=IFCEDGECURVE(#2161,#2162,#2881,.T.);
#1425=IFCEDGECURVE(#2162,#2163,#2882,.T.);
#1426=IFCEDGECURVE(#2163,#2164,#2883,.T.);
#1427=IFCEDGECURVE(#2164,#2165,#2884,.T.);
#1428=IFCEDGECURVE(#2165,#2166,#2885,.T.);
#1429=IFCEDGECURVE(#2166,#2167,#2886,.T.);
#1430=IFCEDGECURVE(#2167,#2168,#2887,.T.);
#1431=IFCEDGECURVE(#2168,#2169,#2888,.T.);
#1432=IFCEDGECURVE(#2169,#2170,#2889,.T.);
#1433=IFCEDGECURVE(#2170,#2171,#2890,.T.);
#1434=IFCEDGECURVE(#2171,#2172,#2891,.T.);
#1435=IFCEDGECURVE(#2172,#2173,#2892,.T.);
#1436=IFCEDGECURVE(#2173,#2174,#2893,.T.);
#1437=IFCEDGECURVE(#2174,#2175,#2894,.T.);
#1438=IFCEDGECURVE(#2175,#2176,#2895,.T.);
#1439=IFCEDGECURVE(#2176,#2177,#2896,.T.);
#1440=IFCEDGECURVE(#2177,#2178,#2897,.T.);
#1441=IFCEDGECURVE(#2178,#2179,#2898,.T.);
#1442=IFCEDGECURVE(#2179,#2180,#2899,.T.);
#1443=IFCEDGECURVE(#2180,#2181,#2900,.T.);
#1444=IFCEDGECURVE(#2181,#2182,#2901,.T.);
#1445=IFCEDGECURVE(#2182,#2183,#2902,.T.);
#1446=IFCEDGECURVE(#2183,#2184,#2903,.T.);
#1447=IFCEDGECURVE(#2184,#2185,#2904,.T.);
#1448=IFCEDGECURVE(#2185,#2186,#2905,.T.);
#1449=IFCEDGECURVE(#2186,#2187,#2906,.T.);
#1450=IFCEDGECURVE(#2187,#2188,#2907,.T.);
#1451=IFCEDGECURVE(#2188,#2189,#2908,.T.);
#1452=IFCEDGECURVE(#2189,#2190,#2909,.T.);
#1453=IFCEDGECURVE(#2190,#2191,#2910,.T.);
#1454=IFCEDGECURVE(#2191,#2192,#2911,.T.);
#1455=IFCEDGECURVE(#2192,#2193,#2912,.T.);
#1456=IFCEDGECURVE(#2193,#2194,#2913,.T.);
#1457=IFCEDGECURVE(#2194,#2195,#2914,.T.);
#1458=IFCEDGECURVE(#2195,#2196,#2915,.T.);
#1459=IFCEDGECURVE(#2196,#2197,#2916,.T.);
#1460=IFCEDGECURVE(#2197,#2198,#2917,.T.);
#1461=IFCEDGECURVE(#2198,#2199,#2918,.T.);
#1462=IFCEDGECURVE(#2199,#2200,#2919,.T.);
#1463=IFCEDGECURVE(#2200,#2201,#2920,.T.);
#1464=IFCEDGECURVE(#2201,#2202,#2921,.T.);
#1465=IFCEDGECURVE(#2202,#2203,#2922,.T.);
#1466=IFCEDGECURVE(#2203,#2204,#2923,.T.);
#1467=IFCEDGECURVE(#2204,#2205,#2924,.T.);
#1468=IFCEDGECURVE(#2205,#2206,#2925,.T.);
#1469=IFCEDGECURVE(#2206,#2207,#2926,.T.);
#1470=IFCEDGECURVE(#2207,#2208,#2927,.T.);
#1471=IFCEDGECURVE(#2208,#2209,#2928,.T.);
#1472=IFCEDGECURVE(#2209,#2210,#2929,.T.);
#1473=IFCEDGECURVE(#2210,#2211,#2930,.T.);
#1474=IFCEDGECURVE(#2211,#2212,#2931,.T.);
#1475=IFCEDGECURVE(#2212,#2213,#2932,.T.);
#1476=IFCEDGECURVE(#2213,#2214,#2933,.T.);
#1477=IFCEDGECURVE(#2214,#2215,#2934,.T.);
#1478=IFCEDGECURVE(#2215,#2216,#2935,.T.);
#1479=IFCEDGECURVE(#2216,#2217,#2936,.T.);
#1480=IFCEDGECURVE(#2217,#2218,#2937,.T.);
#1481=IFCEDGECURVE(#2218,#2219,#2938,.T.);
#1482=IFCEDGECURVE(#2219,#2220,#2939,.T.);
#1483=IFCEDGECURVE(#2220,#2221,#2940,.T.);
#1484=IFCEDGECURVE(#2221,#2222,#2941,.T.);
#1485=IFCEDGECURVE(#2222,#2223,#2942,.T.);
#1486=IFCEDGECURVE(#2223,#2224,#2943,.T.);
#1487=IFCEDGECURVE(#2224,#2225,#2944,.T.);
#1488=IFCEDGECURVE(#2225,#2226,#2945,.T.);
#1489=IFCEDGECURVE(#2226,#2227,#2946,.T.);
#1490=IFCEDGECURVE(#2227,#2228,#2947,.T.);
#1491=IFCEDGECURVE(#2228,#2229,#2948,.T.);
#1492=IFCEDGECURVE(#2229,#2230,#2949,.T.);
#1493=IFCEDGECURVE(#2230,#2231,#2950,.T.);
#1494=IFCEDGECURVE(#2231,#2232,#2951,.T.);
#1495=IFCEDGECURVE(#2232,#2233,#2952,.T.);
#1496=IFCEDGECURVE(#2233,#2234,#2953,.T.);
#1497=IFCEDGECURVE(#2234,#2235,#2954,.T.);
#1498=IFCEDGECURVE(#2235,#2236,#2955,.T.);
#1499=IFCEDGECURVE(#2236,#2237,#2956,.T.);
#1500=IFCEDGECURVE(#2237,#2238,#2957,.T.);
#1501=IFCEDGECURVE(#2238,#2239,#2958,.T.);
#1502=IFCEDGECURVE(#2239,#2240,#2959,.T.);
#1503=IFCEDGECURVE(#2240,#2241,#2960,.T.);
#1504=IFCEDGECURVE(#2241,#2242,#2961,.T.);
#1505=IFCEDGECURVE(#2242,#2243,#2962,.T.);
#1506=IFCEDGECURVE(#2243,#2244,#2963,.T.);
#1507=IFCEDGECURVE(#2244,#2245,#2964,.T.);
#1508=IFCEDGECURVE(#2245,#2246,#2965,.T.);
#1509=IFCEDGECURVE(#2246,#2247,#2966,.T.);
#1510=IFCEDGECURVE(#2247,#2248,#2967,.T.);
#1511=IFCEDGECURVE(#2248,#2249,#2968,.T.);
#1512=IFCEDGECURVE(#2249,#2250,#2969,.T.);
#1513=IFCEDGECURVE(#2250,#2251,#2970,.T.);
#1514=IFCEDGECURVE(#2251,#2057,#2971,.T.);
#1515=IFCEDGECURVE(#2252,#2253,#2972,.T.);
#1516=IFCEDGECURVE(#2253,#2254,#2973,.T.);
#1517=IFCEDGECURVE(#2254,#2255,#2974,.T.);
#1518=IFCEDGECURVE(#2255,#2252,#2975,.T.);
#1519=IFCEDGECURVE(#2256,#2257,#2976,.T.);
#1520=IFCEDGECURVE(#2257,#2258,#2977,.T.);
#1521=IFCEDGECURVE(#2258,#2259,#2978,.T.);
#1522=IFCEDGECURVE(#2259,#2260,#2979,.T.);
#1523=IFCEDGECURVE(#2260,#2261,#2980,.T.);
#1524=IFCEDGECURVE(#2261,#2262,#2981,.T.);
#1525=IFCEDGECURVE(#2262,#2263,#2982,.T.);
#1526=IFCEDGECURVE(#2263,#2264,#2983,.T.);
#1527=IFCEDGECURVE(#2264,#2265,#2984,.T.);
#1528=IFCEDGECURVE(#2265,#2266,#2985,.T.);
#1529=IFCEDGECURVE(#2266,#2267,#2986,.T.);
#1530=IFCEDGECURVE(#2267,#2268,#2987,.T.);
#1531=IFCEDGECURVE(#2268,#2269,#2988,.T.);
#1532=IFCEDGECURVE(#2269,#2270,#2989,.T.);
#1533=IFCEDGECURVE(#2270,#2271,#2990,.T.);
#1534=IFCEDGECURVE(#2271,#2256,#2991,.T.);
#1535=IFCEDGECURVE(#2272,#2273,#2992,.T.);
#1536=IFCEDGECURVE(#2273,#2274,#2993,.T.);
#1537=IFCEDGECURVE(#2274,#2275,#2994,.T.);
#1538=IFCEDGECURVE(#2275,#2276,#2995,.T.);
#1539=IFCEDGECURVE(#2276,#2277,#2996,.T.);
#1540=IFCEDGECURVE(#2277,#2278,#2997,.T.);
#1541=IFCEDGECURVE(#2278,#2279,#2998,.T.);
#1542=IFCEDGECURVE(#2279,#2280,#2999,.T.);
#1543=IFCEDGECURVE(#2280,#2281,#3000,.T.);
#1544=IFCEDGECURVE(#2281,#2282,#3001,.T.);
#1545=IFCEDGECURVE(#2282,#2283,#3002,.T.);
#1546=IFCEDGECURVE(#2283,#2284,#3003,.T.);
#1547=IFCEDGECURVE(#2284,#2285,#3004,.T.);
#1548=IFCEDGECURVE(#2285,#2286,#3005,.T.);
#1549=IFCEDGECURVE(#2286,#2287,#3006,.T.);
#1550=IFCEDGECURVE(#2287,#2272,#3007,.T.);
#1551=IFCEDGECURVE(#2288,#2289,#3008,.T.);
#1552=IFCEDGECURVE(#2289,#2290,#3009,.T.);
#1553=IFCEDGECURVE(#2290,#2291,#3010,.T.);
#1554=IFCEDGECURVE(#2291,#2292,#3011,.T.);
#1555=IFCEDGECURVE(#2292,#2293,#3012,.T.);
#1556=IFCEDGECURVE(#2293,#2294,#3013,.T.);
#1557=IFCEDGECURVE(#2294,#2295,#3014,.T.);
#1558=IFCEDGECURVE(#2295,#2296,#3015,.T.);
#1559=IFCEDGECURVE(#2296,#2297,#3016,.T.);
#1560=IFCEDGECURVE(#2297,#2298,#3017,.T.);
#1561=IFCEDGECURVE(#2298,#2299,#3018,.T.);
#1562=IFCEDGECURVE(#2299,#2288,#3019,.T.);
#1563=IFCEDGECURVE(#2276,#2300,#3020,.T.);
#1564=IFCEDGECURVE(#2300,#2301,#3021,.T.);
#1565=IFCEDGECURVE(#2301,#2302,#3022,.T.);
#1566=IFCEDGECURVE(#2302,#2256,#3023,.T.);
#1567=IFCEDGECURVE(#2256,#2271,#3024,.T.);
#1568=IFCEDGECURVE(#2271,#2270,#3025,.T.);
#1569=IFCEDGECURVE(#2270,#2269,#3026,.T.);
#1570=IFCEDGECURVE(#2269,#2268,#3027,.T.);
#1571=IFCEDGECURVE(#2268,#2303,#3028,.T.);
#1572=IFCEDGECURVE(#2303,#2304,#3029,.T.);
#1573=IFCEDGECURVE(#2304,#2305,#3030,.T.);
#1574=IFCEDGECURVE(#2305,#2280,#3031,.T.);
#1575=IFCEDGECURVE(#2280,#2279,#3032,.T.);
#1576=IFCEDGECURVE(#2279,#2278,#3033,.T.);
#1577=IFCEDGECURVE(#2278,#2277,#3034,.T.);
#1578=IFCEDGECURVE(#2277,#2276,#3035,.T.);
#1579=IFCEDGECURVE(#2306,#2307,#3036,.T.);
#1580=IFCEDGECURVE(#2307,#2308,#3037,.T.);
#1581=IFCEDGECURVE(#2308,#2309,#3038,.T.);
#1582=IFCEDGECURVE(#2309,#2310,#3039,.T.);
#1583=IFCEDGECURVE(#2310,#2311,#3040,.T.);
#1584=IFCEDGECURVE(#2311,#2312,#3041,.T.);
#1585=IFCEDGECURVE(#2312,#2313,#3042,.T.);
#1586=IFCEDGECURVE(#2313,#2314,#3043,.T.);
#1587=IFCEDGECURVE(#2314,#2315,#3044,.T.);
#1588=IFCEDGECURVE(#2315,#2316,#3045,.T.);
#1589=IFCEDGECURVE(#2316,#2317,#3046,.T.);
#1590=IFCEDGECURVE(#2317,#2306,#3047,.T.);
#1591=IFCEDGECURVE(#2272,#2318,#3048,.T.);
#1592=IFCEDGECURVE(#2318,#2319,#3049,.T.);
#1593=IFCEDGECURVE(#2319,#2320,#3050,.T.);
#1594=IFCEDGECURVE(#2320,#2260,#3051,.T.);
#1595=IFCEDGECURVE(#2260,#2261,#3052,.T.);
#1596=IFCEDGECURVE(#2261,#2262,#3053,.T.);
#1597=IFCEDGECURVE(#2262,#2263,#3054,.T.);
#1598=IFCEDGECURVE(#2263,#2264,#3055,.T.);
#1599=IFCEDGECURVE(#2264,#2321,#3056,.T.);
#1600=IFCEDGECURVE(#2321,#2322,#3057,.T.);
#1601=IFCEDGECURVE(#2322,#2323,#3058,.T.);
#1602=IFCEDGECURVE(#2323,#2284,#3059,.T.);
#1603=IFCEDGECURVE(#2284,#2285,#3060,.T.);
#1604=IFCEDGECURVE(#2285,#2286,#3061,.T.);
#1605=IFCEDGECURVE(#2286,#2287,#3062,.T.);
#1606=IFCEDGECURVE(#2287,#2272,#3063,.T.);
#1607=IFCEDGECURVE(#2324,#2325,#3064,.T.);
#1608=IFCEDGECURVE(#2325,#2326,#3065,.T.);
#1609=IFCEDGECURVE(#2326,#2327,#3066,.T.);
#1610=IFCEDGECURVE(#2327,#2328,#3067,.T.);
#1611=IFCEDGECURVE(#2328,#2329,#3068,.T.);
#1612=IFCEDGECURVE(#2329,#2330,#3069,.T.);
#1613=IFCEDGECURVE(#2330,#2331,#3070,.T.);
#1614=IFCEDGECURVE(#2331,#2332,#3071,.T.);
#1615=IFCEDGECURVE(#2332,#2333,#3072,.T.);
#1616=IFCEDGECURVE(#2333,#2334,#3073,.T.);
#1617=IFCEDGECURVE(#2334,#2335,#3074,.T.);
#1618=IFCEDGECURVE(#2335,#2336,#3075,.T.);
#1619=IFCEDGECURVE(#2336,#2337,#3076,.T.);
#1620=IFCEDGECURVE(#2337,#2338,#3077,.T.);
#1621=IFCEDGECURVE(#2338,#2339,#3078,.T.);
#1622=IFCEDGECURVE(#2339,#2324,#3079,.T.);
#1623=IFCEDGECURVE(#2340,#2341,#3080,.T.);
#1624=IFCEDGECURVE(#2341,#2342,#3081,.T.);
#1625=IFCEDGECURVE(#2342,#2343,#3082,.T.);
#1626=IFCEDGECURVE(#2343,#2344,#3083,.T.);
#1627=IFCEDGECURVE(#2344,#2345,#3084,.T.);
#1628=IFCEDGECURVE(#2345,#2346,#3085,.T.);
#1629=IFCEDGECURVE(#2346,#2347,#3086,.T.);
#1630=IFCEDGECURVE(#2347,#2348,#3087,.T.);
#1631=IFCEDGECURVE(#2348,#2349,#3088,.T.);
#1632=IFCEDGECURVE(#2349,#2350,#3089,.T.);
#1633=IFCEDGECURVE(#2350,#2351,#3090,.T.);
#1634=IFCEDGECURVE(#2351,#2352,#3091,.T.);
#1635=IFCEDGECURVE(#2352,#2353,#3092,.T.);
#1636=IFCEDGECURVE(#2353,#2354,#3093,.T.);
#1637=IFCEDGECURVE(#2354,#2355,#3094,.T.);
#1638=IFCEDGECURVE(#2355,#2340,#3095,.T.);
#1639=IFCEDGECURVE(#2356,#2356,#3096,.T.);
#1640=IFCEDGECURVE(#2356,#2356,#3097,.T.);
#1641=IFCEDGECURVE(#2356,#2356,#3098,.T.);
#1642=IFCEDGECURVE(#2356,#2356,#3099,.T.);
#1643=IFCVERTEXPOINT(#5376);
#1644=IFCVERTEXPOINT(#5377);
#1645=IFCVERTEXPOINT(#5379);
#1646=IFCVERTEXPOINT(#5381);
#1647=IFCVERTEXPOINT(#5383);
#1648=IFCVERTEXPOINT(#5385);
#1649=IFCVERTEXPOINT(#5387);
#1650=IFCVERTEXPOINT(#5389);
#1651=IFCVERTEXPOINT(#5391);
#1652=IFCVERTEXPOINT(#5393);
#1653=IFCVERTEXPOINT(#5395);
#1654=IFCVERTEXPOINT(#5397);
#1655=IFCVERTEXPOINT(#5399);
#1656=IFCVERTEXPOINT(#5401);
#1657=IFCVERTEXPOINT(#5403);
#1658=IFCVERTEXPOINT(#5405);
#1659=IFCVERTEXPOINT(#5407);
#1660=IFCVERTEXPOINT(#5409);
#1661=IFCVERTEXPOINT(#5411);
#1662=IFCVERTEXPOINT(#5413);
#1663=IFCVERTEXPOINT(#5415);
#1664=IFCVERTEXPOINT(#5417);
#1665=IFCVERTEXPOINT(#5419);
#1666=IFCVERTEXPOINT(#5421);
#1667=IFCVERTEXPOINT(#5425);
#1668=IFCVERTEXPOINT(#5437);
#1669=IFCVERTEXPOINT(#5440);
#1670=IFCVERTEXPOINT(#5428);
#1671=IFCVERTEXPOINT(#5446);
#1672=IFCVERTEXPOINT(#5542);
#1673=IFCVERTEXPOINT(#5739);
#1674=IFCVERTEXPOINT(#5740);
#1675=IFCVERTEXPOINT(#5763);
#1676=IFCVERTEXPOINT(#5764);
#1677=IFCVERTEXPOINT(#5776);
#1678=IFCVERTEXPOINT(#5788);
#1679=IFCVERTEXPOINT(#5790);
#1680=IFCVERTEXPOINT(#5791);
#1681=IFCVERTEXPOINT(#5793);
#1682=IFCVERTEXPOINT(#5794);
#1683=IFCVERTEXPOINT(#5796);
#1684=IFCVERTEXPOINT(#5797);
#1685=IFCVERTEXPOINT(#5799);
#1686=IFCVERTEXPOINT(#5800);
#1687=IFCVERTEXPOINT(#5802);
#1688=IFCVERTEXPOINT(#5803);
#1689=IFCVERTEXPOINT(#5805);
#1690=IFCVERTEXPOINT(#5806);
#1691=IFCVERTEXPOINT(#5808);
#1692=IFCVERTEXPOINT(#5809);
#1693=IFCVERTEXPOINT(#5811);
#1694=IFCVERTEXPOINT(#5812);
#1695=IFCVERTEXPOINT(#5814);
#1696=IFCVERTEXPOINT(#5815);
#1697=IFCVERTEXPOINT(#5817);
#1698=IFCVERTEXPOINT(#5818);
#1699=IFCVERTEXPOINT(#5820);
#1700=IFCVERTEXPOINT(#5821);
#1701=IFCVERTEXPOINT(#5823);
#1702=IFCVERTEXPOINT(#5825);
#1703=IFCVERTEXPOINT(#5826);
#1704=IFCVERTEXPOINT(#5828);
#1705=IFCVERTEXPOINT(#5829);
#1706=IFCVERTEXPOINT(#5831);
#1707=IFCVERTEXPOINT(#5832);
#1708=IFCVERTEXPOINT(#5834);
#1709=IFCVERTEXPOINT(#5835);
#1710=IFCVERTEXPOINT(#5837);
#1711=IFCVERTEXPOINT(#5838);
#1712=IFCVERTEXPOINT(#5840);
#1713=IFCVERTEXPOINT(#5841);
#1714=IFCVERTEXPOINT(#5843);
#1715=IFCVERTEXPOINT(#5844);
#1716=IFCVERTEXPOINT(#5846);
#1717=IFCVERTEXPOINT(#5847);
#1718=IFCVERTEXPOINT(#5849);
#1719=IFCVERTEXPOINT(#5850);
#1720=IFCVERTEXPOINT(#5852);
#1721=IFCVERTEXPOINT(#5853);
#1722=IFCVERTEXPOINT(#5854);
#1723=IFCVERTEXPOINT(#5855);
#1724=IFCVERTEXPOINT(#5856);
#1725=IFCVERTEXPOINT(#5857);
#1726=IFCVERTEXPOINT(#5859);
#1727=IFCVERTEXPOINT(#5860);
#1728=IFCVERTEXPOINT(#5862);
#1729=IFCVERTEXPOINT(#5863);
#1730=IFCVERTEXPOINT(#5865);
#1731=IFCVERTEXPOINT(#5866);
#1732=IFCVERTEXPOINT(#5868);
#1733=IFCVERTEXPOINT(#5869);
#1734=IFCVERTEXPOINT(#5871);
#1735=IFCVERTEXPOINT(#5872);
#1736=IFCVERTEXPOINT(#5874);
#1737=IFCVERTEXPOINT(#5875);
#1738=IFCVERTEXPOINT(#5877);
#1739=IFCVERTEXPOINT(#5878);
#1740=IFCVERTEXPOINT(#5880);
#1741=IFCVERTEXPOINT(#5881);
#1742=IFCVERTEXPOINT(#5883);
#1743=IFCVERTEXPOINT(#5884);
#1744=IFCVERTEXPOINT(#5886);
#1745=IFCVERTEXPOINT(#5887);
#1746=IFCVERTEXPOINT(#5888);
#1747=IFCVERTEXPOINT(#5889);
#1748=IFCVERTEXPOINT(#5890);
#1749=IFCVERTEXPOINT(#5892);
#1750=IFCVERTEXPOINT(#5893);
#1751=IFCVERTEXPOINT(#5895);
#1752=IFCVERTEXPOINT(#5896);
#1753=IFCVERTEXPOINT(#5898);
#1754=IFCVERTEXPOINT(#5899);
#1755=IFCVERTEXPOINT(#5901);
#1756=IFCVERTEXPOINT(#5902);
#1757=IFCVERTEXPOINT(#5904);
#1758=IFCVERTEXPOINT(#5905);
#1759=IFCVERTEXPOINT(#5907);
#1760=IFCVERTEXPOINT(#5908);
#1761=IFCVERTEXPOINT(#5910);
#1762=IFCVERTEXPOINT(#5911);
#1763=IFCVERTEXPOINT(#5913);
#1764=IFCVERTEXPOINT(#5914);
#1765=IFCVERTEXPOINT(#5916);
#1766=IFCVERTEXPOINT(#5917);
#1767=IFCVERTEXPOINT(#5919);
#1768=IFCVERTEXPOINT(#5920);
#1769=IFCVERTEXPOINT(#5921);
#1770=IFCVERTEXPOINT(#5922);
#1771=IFCVERTEXPOINT(#5923);
#1772=IFCVERTEXPOINT(#5925);
#1773=IFCVERTEXPOINT(#5926);
#1774=IFCVERTEXPOINT(#5928);
#1775=IFCVERTEXPOINT(#5929);
#1776=IFCVERTEXPOINT(#5931);
#1777=IFCVERTEXPOINT(#5932);
#1778=IFCVERTEXPOINT(#5934);
#1779=IFCVERTEXPOINT(#5935);
#1780=IFCVERTEXPOINT(#5937);
#1781=IFCVERTEXPOINT(#5938);
#1782=IFCVERTEXPOINT(#5940);
#1783=IFCVERTEXPOINT(#5941);
#1784=IFCVERTEXPOINT(#5943);
#1785=IFCVERTEXPOINT(#5944);
#1786=IFCVERTEXPOINT(#5946);
#1787=IFCVERTEXPOINT(#5947);
#1788=IFCVERTEXPOINT(#5949);
#1789=IFCVERTEXPOINT(#5950);
#1790=IFCVERTEXPOINT(#5952);
#1791=IFCVERTEXPOINT(#5953);
#1792=IFCVERTEXPOINT(#5954);
#1793=IFCVERTEXPOINT(#5955);
#1794=IFCVERTEXPOINT(#5956);
#1795=IFCVERTEXPOINT(#5957);
#1796=IFCVERTEXPOINT(#5959);
#1797=IFCVERTEXPOINT(#5960);
#1798=IFCVERTEXPOINT(#5962);
#1799=IFCVERTEXPOINT(#5963);
#1800=IFCVERTEXPOINT(#5965);
#1801=IFCVERTEXPOINT(#5966);
#1802=IFCVERTEXPOINT(#5968);
#1803=IFCVERTEXPOINT(#5969);
#1804=IFCVERTEXPOINT(#5971);
#1805=IFCVERTEXPOINT(#5972);
#1806=IFCVERTEXPOINT(#5974);
#1807=IFCVERTEXPOINT(#5975);
#1808=IFCVERTEXPOINT(#5977);
#1809=IFCVERTEXPOINT(#5978);
#1810=IFCVERTEXPOINT(#5980);
#1811=IFCVERTEXPOINT(#5981);
#1812=IFCVERTEXPOINT(#5983);
#1813=IFCVERTEXPOINT(#5984);
#1814=IFCVERTEXPOINT(#5986);
#1815=IFCVERTEXPOINT(#5987);
#1816=IFCVERTEXPOINT(#5988);
#1817=IFCVERTEXPOINT(#5989);
#1818=IFCVERTEXPOINT(#5990);
#1819=IFCVERTEXPOINT(#5992);
#1820=IFCVERTEXPOINT(#5993);
#1821=IFCVERTEXPOINT(#5995);
#1822=IFCVERTEXPOINT(#5996);
#1823=IFCVERTEXPOINT(#5998);
#1824=IFCVERTEXPOINT(#5999);
#1825=IFCVERTEXPOINT(#6001);
#1826=IFCVERTEXPOINT(#6002);
#1827=IFCVERTEXPOINT(#6004);
#1828=IFCVERTEXPOINT(#6005);
#1829=IFCVERTEXPOINT(#6007);
#1830=IFCVERTEXPOINT(#6008);
#1831=IFCVERTEXPOINT(#6010);
#1832=IFCVERTEXPOINT(#6011);
#1833=IFCVERTEXPOINT(#6013);
#1834=IFCVERTEXPOINT(#6014);
#1835=IFCVERTEXPOINT(#6016);
#1836=IFCVERTEXPOINT(#6017);
#1837=IFCVERTEXPOINT(#6019);
#1838=IFCVERTEXPOINT(#6020);
#1839=IFCVERTEXPOINT(#6022);
#1840=IFCVERTEXPOINT(#6023);
#1841=IFCVERTEXPOINT(#6025);
#1842=IFCVERTEXPOINT(#6027);
#1843=IFCVERTEXPOINT(#6028);
#1844=IFCVERTEXPOINT(#6030);
#1845=IFCVERTEXPOINT(#6031);
#1846=IFCVERTEXPOINT(#6033);
#1847=IFCVERTEXPOINT(#6034);
#1848=IFCVERTEXPOINT(#6036);
#1849=IFCVERTEXPOINT(#6037);
#1850=IFCVERTEXPOINT(#6039);
#1851=IFCVERTEXPOINT(#6040);
#1852=IFCVERTEXPOINT(#6042);
#1853=IFCVERTEXPOINT(#6043);
#1854=IFCVERTEXPOINT(#6045);
#1855=IFCVERTEXPOINT(#6046);
#1856=IFCVERTEXPOINT(#6048);
#1857=IFCVERTEXPOINT(#6049);
#1858=IFCVERTEXPOINT(#6051);
#1859=IFCVERTEXPOINT(#6052);
#1860=IFCVERTEXPOINT(#6054);
#1861=IFCVERTEXPOINT(#6055);
#1862=IFCVERTEXPOINT(#6056);
#1863=IFCVERTEXPOINT(#5786);
#1864=IFCVERTEXPOINT(#6058);
#1865=IFCVERTEXPOINT(#6059);
#1866=IFCVERTEXPOINT(#6061);
#1867=IFCVERTEXPOINT(#5787);
#1868=IFCVERTEXPOINT(#6062);
#1869=IFCVERTEXPOINT(#6064);
#1870=IFCVERTEXPOINT(#6065);
#1871=IFCVERTEXPOINT(#6067);
#1872=IFCVERTEXPOINT(#6068);
#1873=IFCVERTEXPOINT(#6070);
#1874=IFCVERTEXPOINT(#6072);
#1875=IFCVERTEXPOINT(#6073);
#1876=IFCVERTEXPOINT(#6075);
#1877=IFCVERTEXPOINT(#6076);
#1878=IFCVERTEXPOINT(#6078);
#1879=IFCVERTEXPOINT(#6079);
#1880=IFCVERTEXPOINT(#6081);
#1881=IFCVERTEXPOINT(#6083);
#1882=IFCVERTEXPOINT(#6085);
#1883=IFCVERTEXPOINT(#6086);
#1884=IFCVERTEXPOINT(#6088);
#1885=IFCVERTEXPOINT(#6089);
#1886=IFCVERTEXPOINT(#6091);
#1887=IFCVERTEXPOINT(#6092);
#1888=IFCVERTEXPOINT(#6094);
#1889=IFCVERTEXPOINT(#6095);
#1890=IFCVERTEXPOINT(#6097);
#1891=IFCVERTEXPOINT(#6098);
#1892=IFCVERTEXPOINT(#6100);
#1893=IFCVERTEXPOINT(#6102);
#1894=IFCVERTEXPOINT(#6103);
#1895=IFCVERTEXPOINT(#6105);
#1896=IFCVERTEXPOINT(#6106);
#1897=IFCVERTEXPOINT(#6108);
#1898=IFCVERTEXPOINT(#6109);
#1899=IFCVERTEXPOINT(#6111);
#1900=IFCVERTEXPOINT(#6112);
#1901=IFCVERTEXPOINT(#6114);
#1902=IFCVERTEXPOINT(#6115);
#1903=IFCVERTEXPOINT(#6117);
#1904=IFCVERTEXPOINT(#6119);
#1905=IFCVERTEXPOINT(#6121);
#1906=IFCVERTEXPOINT(#6122);
#1907=IFCVERTEXPOINT(#6124);
#1908=IFCVERTEXPOINT(#6125);
#1909=IFCVERTEXPOINT(#6127);
#1910=IFCVERTEXPOINT(#6128);
#1911=IFCVERTEXPOINT(#6130);
#1912=IFCVERTEXPOINT(#6131);
#1913=IFCVERTEXPOINT(#6132);
#1914=IFCVERTEXPOINT(#6133);
#1915=IFCVERTEXPOINT(#6134);
#1916=IFCVERTEXPOINT(#6136);
#1917=IFCVERTEXPOINT(#6137);
#1918=IFCVERTEXPOINT(#6139);
#1919=IFCVERTEXPOINT(#6140);
#1920=IFCVERTEXPOINT(#6142);
#1921=IFCVERTEXPOINT(#6143);
#1922=IFCVERTEXPOINT(#6145);
#1923=IFCVERTEXPOINT(#6146);
#1924=IFCVERTEXPOINT(#6148);
#1925=IFCVERTEXPOINT(#6149);
#1926=IFCVERTEXPOINT(#6151);
#1927=IFCVERTEXPOINT(#6152);
#1928=IFCVERTEXPOINT(#6154);
#1929=IFCVERTEXPOINT(#6155);
#1930=IFCVERTEXPOINT(#6157);
#1931=IFCVERTEXPOINT(#6158);
#1932=IFCVERTEXPOINT(#6160);
#1933=IFCVERTEXPOINT(#6161);
#1934=IFCVERTEXPOINT(#6163);
#1935=IFCVERTEXPOINT(#6164);
#1936=IFCVERTEXPOINT(#6165);
#1937=IFCVERTEXPOINT(#6166);
#1938=IFCVERTEXPOINT(#6167);
#1939=IFCVERTEXPOINT(#6169);
#1940=IFCVERTEXPOINT(#6170);
#1941=IFCVERTEXPOINT(#6172);
#1942=IFCVERTEXPOINT(#6174);
#1943=IFCVERTEXPOINT(#6175);
#1944=IFCVERTEXPOINT(#6177);
#1945=IFCVERTEXPOINT(#6178);
#1946=IFCVERTEXPOINT(#6180);
#1947=IFCVERTEXPOINT(#6181);
#1948=IFCVERTEXPOINT(#6183);
#1949=IFCVERTEXPOINT(#6184);
#1950=IFCVERTEXPOINT(#6186);
#1951=IFCVERTEXPOINT(#6187);
#1952=IFCVERTEXPOINT(#6189);
#1953=IFCVERTEXPOINT(#6190);
#1954=IFCVERTEXPOINT(#6192);
#1955=IFCVERTEXPOINT(#6193);
#1956=IFCVERTEXPOINT(#6195);
#1957=IFCVERTEXPOINT(#6196);
#1958=IFCVERTEXPOINT(#6198);
#1959=IFCVERTEXPOINT(#6199);
#1960=IFCVERTEXPOINT(#6200);
#1961=IFCVERTEXPOINT(#6201);
#1962=IFCVERTEXPOINT(#6203);
#1963=IFCVERTEXPOINT(#6204);
#1964=IFCVERTEXPOINT(#6206);
#1965=IFCVERTEXPOINT(#6208);
#1966=IFCVERTEXPOINT(#6209);
#1967=IFCVERTEXPOINT(#6211);
#1968=IFCVERTEXPOINT(#6212);
#1969=IFCVERTEXPOINT(#6214);
#1970=IFCVERTEXPOINT(#6215);
#1971=IFCVERTEXPOINT(#6217);
#1972=IFCVERTEXPOINT(#6218);
#1973=IFCVERTEXPOINT(#6220);
#1974=IFCVERTEXPOINT(#6221);
#1975=IFCVERTEXPOINT(#6223);
#1976=IFCVERTEXPOINT(#6224);
#1977=IFCVERTEXPOINT(#6226);
#1978=IFCVERTEXPOINT(#6227);
#1979=IFCVERTEXPOINT(#6229);
#1980=IFCVERTEXPOINT(#6230);
#1981=IFCVERTEXPOINT(#6232);
#1982=IFCVERTEXPOINT(#6233);
#1983=IFCVERTEXPOINT(#6234);
#1984=IFCVERTEXPOINT(#6235);
#1985=IFCVERTEXPOINT(#6236);
#1986=IFCVERTEXPOINT(#6238);
#1987=IFCVERTEXPOINT(#6239);
#1988=IFCVERTEXPOINT(#6241);
#1989=IFCVERTEXPOINT(#6242);
#1990=IFCVERTEXPOINT(#6244);
#1991=IFCVERTEXPOINT(#6245);
#1992=IFCVERTEXPOINT(#6247);
#1993=IFCVERTEXPOINT(#6248);
#1994=IFCVERTEXPOINT(#6250);
#1995=IFCVERTEXPOINT(#6251);
#1996=IFCVERTEXPOINT(#6253);
#1997=IFCVERTEXPOINT(#6254);
#1998=IFCVERTEXPOINT(#6256);
#1999=IFCVERTEXPOINT(#6257);
#2000=IFCVERTEXPOINT(#6259);
#2001=IFCVERTEXPOINT(#6260);
#2002=IFCVERTEXPOINT(#6262);
#2003=IFCVERTEXPOINT(#6263);
#2004=IFCVERTEXPOINT(#6265);
#2005=IFCVERTEXPOINT(#6266);
#2006=IFCVERTEXPOINT(#6267);
#2007=IFCVERTEXPOINT(#6268);
#2008=IFCVERTEXPOINT(#6269);
#2009=IFCVERTEXPOINT(#6271);
#2010=IFCVERTEXPOINT(#6272);
#2011=IFCVERTEXPOINT(#6274);
#2012=IFCVERTEXPOINT(#6276);
#2013=IFCVERTEXPOINT(#6277);
#2014=IFCVERTEXPOINT(#6279);
#2015=IFCVERTEXPOINT(#6280);
#2016=IFCVERTEXPOINT(#6282);
#2017=IFCVERTEXPOINT(#6283);
#2018=IFCVERTEXPOINT(#6285);
#2019=IFCVERTEXPOINT(#6286);
#2020=IFCVERTEXPOINT(#6288);
#2021=IFCVERTEXPOINT(#6290);
#2022=IFCVERTEXPOINT(#6292);
#2023=IFCVERTEXPOINT(#6293);
#2024=IFCVERTEXPOINT(#6295);
#2025=IFCVERTEXPOINT(#6296);
#2026=IFCVERTEXPOINT(#6298);
#2027=IFCVERTEXPOINT(#6299);
#2028=IFCVERTEXPOINT(#6301);
#2029=IFCVERTEXPOINT(#6302);
#2030=IFCVERTEXPOINT(#6304);
#2031=IFCVERTEXPOINT(#6305);
#2032=IFCVERTEXPOINT(#6307);
#2033=IFCVERTEXPOINT(#6309);
#2034=IFCVERTEXPOINT(#6310);
#2035=IFCVERTEXPOINT(#6312);
#2036=IFCVERTEXPOINT(#6313);
#2037=IFCVERTEXPOINT(#6315);
#2038=IFCVERTEXPOINT(#6316);
#2039=IFCVERTEXPOINT(#6318);
#2040=IFCVERTEXPOINT(#6319);
#2041=IFCVERTEXPOINT(#6321);
#2042=IFCVERTEXPOINT(#6322);
#2043=IFCVERTEXPOINT(#6324);
#2044=IFCVERTEXPOINT(#6326);
#2045=IFCVERTEXPOINT(#6328);
#2046=IFCVERTEXPOINT(#6329);
#2047=IFCVERTEXPOINT(#6331);
#2048=IFCVERTEXPOINT(#6332);
#2049=IFCVERTEXPOINT(#6334);
#2050=IFCVERTEXPOINT(#6335);
#2051=IFCVERTEXPOINT(#6337);
#2052=IFCVERTEXPOINT(#6338);
#2053=IFCVERTEXPOINT(#5777);
#2054=IFCVERTEXPOINT(#6340);
#2055=IFCVERTEXPOINT(#6341);
#2056=IFCVERTEXPOINT(#6343);
#2057=IFCVERTEXPOINT(#6345);
#2058=IFCVERTEXPOINT(#6346);
#2059=IFCVERTEXPOINT(#6347);
#2060=IFCVERTEXPOINT(#6349);
#2061=IFCVERTEXPOINT(#6350);
#2062=IFCVERTEXPOINT(#6351);
#2063=IFCVERTEXPOINT(#6352);
#2064=IFCVERTEXPOINT(#6353);
#2065=IFCVERTEXPOINT(#6355);
#2066=IFCVERTEXPOINT(#6356);
#2067=IFCVERTEXPOINT(#6357);
#2068=IFCVERTEXPOINT(#6359);
#2069=IFCVERTEXPOINT(#6360);
#2070=IFCVERTEXPOINT(#6362);
#2071=IFCVERTEXPOINT(#6363);
#2072=IFCVERTEXPOINT(#6365);
#2073=IFCVERTEXPOINT(#6366);
#2074=IFCVERTEXPOINT(#6368);
#2075=IFCVERTEXPOINT(#6369);
#2076=IFCVERTEXPOINT(#6371);
#2077=IFCVERTEXPOINT(#6372);
#2078=IFCVERTEXPOINT(#6374);
#2079=IFCVERTEXPOINT(#6375);
#2080=IFCVERTEXPOINT(#6377);
#2081=IFCVERTEXPOINT(#6378);
#2082=IFCVERTEXPOINT(#6380);
#2083=IFCVERTEXPOINT(#6382);
#2084=IFCVERTEXPOINT(#6383);
#2085=IFCVERTEXPOINT(#6385);
#2086=IFCVERTEXPOINT(#6386);
#2087=IFCVERTEXPOINT(#6388);
#2088=IFCVERTEXPOINT(#6389);
#2089=IFCVERTEXPOINT(#6391);
#2090=IFCVERTEXPOINT(#6392);
#2091=IFCVERTEXPOINT(#6394);
#2092=IFCVERTEXPOINT(#6395);
#2093=IFCVERTEXPOINT(#6397);
#2094=IFCVERTEXPOINT(#6398);
#2095=IFCVERTEXPOINT(#6400);
#2096=IFCVERTEXPOINT(#6401);
#2097=IFCVERTEXPOINT(#6403);
#2098=IFCVERTEXPOINT(#6404);
#2099=IFCVERTEXPOINT(#6406);
#2100=IFCVERTEXPOINT(#6407);
#2101=IFCVERTEXPOINT(#6409);
#2102=IFCVERTEXPOINT(#6410);
#2103=IFCVERTEXPOINT(#6412);
#2104=IFCVERTEXPOINT(#6413);
#2105=IFCVERTEXPOINT(#6415);
#2106=IFCVERTEXPOINT(#6416);
#2107=IFCVERTEXPOINT(#6418);
#2108=IFCVERTEXPOINT(#6419);
#2109=IFCVERTEXPOINT(#6421);
#2110=IFCVERTEXPOINT(#6422);
#2111=IFCVERTEXPOINT(#6424);
#2112=IFCVERTEXPOINT(#6425);
#2113=IFCVERTEXPOINT(#6427);
#2114=IFCVERTEXPOINT(#6428);
#2115=IFCVERTEXPOINT(#6430);
#2116=IFCVERTEXPOINT(#6431);
#2117=IFCVERTEXPOINT(#6433);
#2118=IFCVERTEXPOINT(#6434);
#2119=IFCVERTEXPOINT(#6436);
#2120=IFCVERTEXPOINT(#6437);
#2121=IFCVERTEXPOINT(#6439);
#2122=IFCVERTEXPOINT(#6440);
#2123=IFCVERTEXPOINT(#6442);
#2124=IFCVERTEXPOINT(#6443);
#2125=IFCVERTEXPOINT(#6445);
#2126=IFCVERTEXPOINT(#6446);
#2127=IFCVERTEXPOINT(#6448);
#2128=IFCVERTEXPOINT(#6449);
#2129=IFCVERTEXPOINT(#6451);
#2130=IFCVERTEXPOINT(#6452);
#2131=IFCVERTEXPOINT(#6454);
#2132=IFCVERTEXPOINT(#6455);
#2133=IFCVERTEXPOINT(#6457);
#2134=IFCVERTEXPOINT(#6458);
#2135=IFCVERTEXPOINT(#6460);
#2136=IFCVERTEXPOINT(#6461);
#2137=IFCVERTEXPOINT(#6463);
#2138=IFCVERTEXPOINT(#6464);
#2139=IFCVERTEXPOINT(#6466);
#2140=IFCVERTEXPOINT(#6468);
#2141=IFCVERTEXPOINT(#6469);
#2142=IFCVERTEXPOINT(#6470);
#2143=IFCVERTEXPOINT(#6472);
#2144=IFCVERTEXPOINT(#6473);
#2145=IFCVERTEXPOINT(#6474);
#2146=IFCVERTEXPOINT(#6476);
#2147=IFCVERTEXPOINT(#6478);
#2148=IFCVERTEXPOINT(#6479);
#2149=IFCVERTEXPOINT(#6480);
#2150=IFCVERTEXPOINT(#6481);
#2151=IFCVERTEXPOINT(#6482);
#2152=IFCVERTEXPOINT(#6484);
#2153=IFCVERTEXPOINT(#6485);
#2154=IFCVERTEXPOINT(#6486);
#2155=IFCVERTEXPOINT(#6487);
#2156=IFCVERTEXPOINT(#6488);
#2157=IFCVERTEXPOINT(#6490);
#2158=IFCVERTEXPOINT(#6492);
#2159=IFCVERTEXPOINT(#6493);
#2160=IFCVERTEXPOINT(#6494);
#2161=IFCVERTEXPOINT(#6495);
#2162=IFCVERTEXPOINT(#6497);
#2163=IFCVERTEXPOINT(#6499);
#2164=IFCVERTEXPOINT(#6500);
#2165=IFCVERTEXPOINT(#6501);
#2166=IFCVERTEXPOINT(#6503);
#2167=IFCVERTEXPOINT(#6504);
#2168=IFCVERTEXPOINT(#6505);
#2169=IFCVERTEXPOINT(#6507);
#2170=IFCVERTEXPOINT(#6509);
#2171=IFCVERTEXPOINT(#6510);
#2172=IFCVERTEXPOINT(#6512);
#2173=IFCVERTEXPOINT(#6513);
#2174=IFCVERTEXPOINT(#6515);
#2175=IFCVERTEXPOINT(#6516);
#2176=IFCVERTEXPOINT(#6518);
#2177=IFCVERTEXPOINT(#6519);
#2178=IFCVERTEXPOINT(#6521);
#2179=IFCVERTEXPOINT(#6522);
#2180=IFCVERTEXPOINT(#6524);
#2181=IFCVERTEXPOINT(#6525);
#2182=IFCVERTEXPOINT(#6527);
#2183=IFCVERTEXPOINT(#6529);
#2184=IFCVERTEXPOINT(#6530);
#2185=IFCVERTEXPOINT(#6532);
#2186=IFCVERTEXPOINT(#6533);
#2187=IFCVERTEXPOINT(#6535);
#2188=IFCVERTEXPOINT(#6536);
#2189=IFCVERTEXPOINT(#6538);
#2190=IFCVERTEXPOINT(#6539);
#2191=IFCVERTEXPOINT(#6541);
#2192=IFCVERTEXPOINT(#6542);
#2193=IFCVERTEXPOINT(#6544);
#2194=IFCVERTEXPOINT(#6545);
#2195=IFCVERTEXPOINT(#6547);
#2196=IFCVERTEXPOINT(#6548);
#2197=IFCVERTEXPOINT(#6550);
#2198=IFCVERTEXPOINT(#6551);
#2199=IFCVERTEXPOINT(#6553);
#2200=IFCVERTEXPOINT(#6554);
#2201=IFCVERTEXPOINT(#6556);
#2202=IFCVERTEXPOINT(#6557);
#2203=IFCVERTEXPOINT(#6559);
#2204=IFCVERTEXPOINT(#6560);
#2205=IFCVERTEXPOINT(#6562);
#2206=IFCVERTEXPOINT(#6563);
#2207=IFCVERTEXPOINT(#6565);
#2208=IFCVERTEXPOINT(#6566);
#2209=IFCVERTEXPOINT(#6568);
#2210=IFCVERTEXPOINT(#6569);
#2211=IFCVERTEXPOINT(#6571);
#2212=IFCVERTEXPOINT(#6572);
#2213=IFCVERTEXPOINT(#6574);
#2214=IFCVERTEXPOINT(#6575);
#2215=IFCVERTEXPOINT(#6577);
#2216=IFCVERTEXPOINT(#6578);
#2217=IFCVERTEXPOINT(#6580);
#2218=IFCVERTEXPOINT(#6581);
#2219=IFCVERTEXPOINT(#6583);
#2220=IFCVERTEXPOINT(#6584);
#2221=IFCVERTEXPOINT(#6586);
#2222=IFCVERTEXPOINT(#6587);
#2223=IFCVERTEXPOINT(#6589);
#2224=IFCVERTEXPOINT(#6591);
#2225=IFCVERTEXPOINT(#6592);
#2226=IFCVERTEXPOINT(#6594);
#2227=IFCVERTEXPOINT(#6595);
#2228=IFCVERTEXPOINT(#6597);
#2229=IFCVERTEXPOINT(#6598);
#2230=IFCVERTEXPOINT(#6600);
#2231=IFCVERTEXPOINT(#6601);
#2232=IFCVERTEXPOINT(#6603);
#2233=IFCVERTEXPOINT(#6604);
#2234=IFCVERTEXPOINT(#6606);
#2235=IFCVERTEXPOINT(#6607);
#2236=IFCVERTEXPOINT(#6609);
#2237=IFCVERTEXPOINT(#6611);
#2238=IFCVERTEXPOINT(#6612);
#2239=IFCVERTEXPOINT(#6613);
#2240=IFCVERTEXPOINT(#6615);
#2241=IFCVERTEXPOINT(#6616);
#2242=IFCVERTEXPOINT(#6618);
#2243=IFCVERTEXPOINT(#6619);
#2244=IFCVERTEXPOINT(#6621);
#2245=IFCVERTEXPOINT(#6622);
#2246=IFCVERTEXPOINT(#6623);
#2247=IFCVERTEXPOINT(#6624);
#2248=IFCVERTEXPOINT(#6626);
#2249=IFCVERTEXPOINT(#6627);
#2250=IFCVERTEXPOINT(#6628);
#2251=IFCVERTEXPOINT(#6629);
#2252=IFCVERTEXPOINT(#6632);
#2253=IFCVERTEXPOINT(#6842);
#2254=IFCVERTEXPOINT(#6856);
#2255=IFCVERTEXPOINT(#6646);
#2256=IFCVERTEXPOINT(#6863);
#2257=IFCVERTEXPOINT(#6875);
#2258=IFCVERTEXPOINT(#6861);
#2259=IFCVERTEXPOINT(#6876);
#2260=IFCVERTEXPOINT(#6859);
#2261=IFCVERTEXPOINT(#6877);
#2262=IFCVERTEXPOINT(#6878);
#2263=IFCVERTEXPOINT(#6879);
#2264=IFCVERTEXPOINT(#6860);
#2265=IFCVERTEXPOINT(#6880);
#2266=IFCVERTEXPOINT(#6862);
#2267=IFCVERTEXPOINT(#6881);
#2268=IFCVERTEXPOINT(#6864);
#2269=IFCVERTEXPOINT(#6883);
#2270=IFCVERTEXPOINT(#6885);
#2271=IFCVERTEXPOINT(#6887);
#2272=IFCVERTEXPOINT(#6867);
#2273=IFCVERTEXPOINT(#6888);
#2274=IFCVERTEXPOINT(#6889);
#2275=IFCVERTEXPOINT(#6890);
#2276=IFCVERTEXPOINT(#6865);
#2277=IFCVERTEXPOINT(#6892);
#2278=IFCVERTEXPOINT(#6894);
#2279=IFCVERTEXPOINT(#6896);
#2280=IFCVERTEXPOINT(#6866);
#2281=IFCVERTEXPOINT(#6897);
#2282=IFCVERTEXPOINT(#6898);
#2283=IFCVERTEXPOINT(#6900);
#2284=IFCVERTEXPOINT(#6868);
#2285=IFCVERTEXPOINT(#6902);
#2286=IFCVERTEXPOINT(#6904);
#2287=IFCVERTEXPOINT(#6906);
#2288=IFCVERTEXPOINT(#6908);
#2289=IFCVERTEXPOINT(#6909);
#2290=IFCVERTEXPOINT(#6911);
#2291=IFCVERTEXPOINT(#6913);
#2292=IFCVERTEXPOINT(#6915);
#2293=IFCVERTEXPOINT(#6917);
#2294=IFCVERTEXPOINT(#6919);
#2295=IFCVERTEXPOINT(#6921);
#2296=IFCVERTEXPOINT(#6923);
#2297=IFCVERTEXPOINT(#6925);
#2298=IFCVERTEXPOINT(#6927);
#2299=IFCVERTEXPOINT(#6929);
#2300=IFCVERTEXPOINT(#6930);
#2301=IFCVERTEXPOINT(#6931);
#2302=IFCVERTEXPOINT(#6932);
#2303=IFCVERTEXPOINT(#6933);
#2304=IFCVERTEXPOINT(#6934);
#2305=IFCVERTEXPOINT(#6935);
#2306=IFCVERTEXPOINT(#6938);
#2307=IFCVERTEXPOINT(#6939);
#2308=IFCVERTEXPOINT(#6941);
#2309=IFCVERTEXPOINT(#6943);
#2310=IFCVERTEXPOINT(#6945);
#2311=IFCVERTEXPOINT(#6947);
#2312=IFCVERTEXPOINT(#6949);
#2313=IFCVERTEXPOINT(#6951);
#2314=IFCVERTEXPOINT(#6953);
#2315=IFCVERTEXPOINT(#6955);
#2316=IFCVERTEXPOINT(#6957);
#2317=IFCVERTEXPOINT(#6959);
#2318=IFCVERTEXPOINT(#6869);
#2319=IFCVERTEXPOINT(#6871);
#2320=IFCVERTEXPOINT(#6873);
#2321=IFCVERTEXPOINT(#6874);
#2322=IFCVERTEXPOINT(#6872);
#2323=IFCVERTEXPOINT(#6870);
#2324=IFCVERTEXPOINT(#6963);
#2325=IFCVERTEXPOINT(#6966);
#2326=IFCVERTEXPOINT(#6967);
#2327=IFCVERTEXPOINT(#6968);
#2328=IFCVERTEXPOINT(#6965);
#2329=IFCVERTEXPOINT(#6969);
#2330=IFCVERTEXPOINT(#6970);
#2331=IFCVERTEXPOINT(#6971);
#2332=IFCVERTEXPOINT(#6964);
#2333=IFCVERTEXPOINT(#6972);
#2334=IFCVERTEXPOINT(#6973);
#2335=IFCVERTEXPOINT(#6974);
#2336=IFCVERTEXPOINT(#6962);
#2337=IFCVERTEXPOINT(#6975);
#2338=IFCVERTEXPOINT(#6976);
#2339=IFCVERTEXPOINT(#6977);
#2340=IFCVERTEXPOINT(#6980);
#2341=IFCVERTEXPOINT(#6981);
#2342=IFCVERTEXPOINT(#6983);
#2343=IFCVERTEXPOINT(#6985);
#2344=IFCVERTEXPOINT(#6987);
#2345=IFCVERTEXPOINT(#6989);
#2346=IFCVERTEXPOINT(#6991);
#2347=IFCVERTEXPOINT(#6993);
#2348=IFCVERTEXPOINT(#6995);
#2349=IFCVERTEXPOINT(#6997);
#2350=IFCVERTEXPOINT(#6999);
#2351=IFCVERTEXPOINT(#7001);
#2352=IFCVERTEXPOINT(#7003);
#2353=IFCVERTEXPOINT(#7005);
#2354=IFCVERTEXPOINT(#7007);
#2355=IFCVERTEXPOINT(#7009);
#2356=IFCVERTEXPOINT(#7012);
#2357=IFCSURFACECURVE(#3100,(#3827),.PCURVE_S1.);
#2358=IFCSURFACECURVE(#3101,(#3828),.PCURVE_S1.);
#2359=IFCSURFACECURVE(#3102,(#3829),.PCURVE_S1.);
#2360=IFCSURFACECURVE(#3103,(#3830),.PCURVE_S1.);
#2361=IFCSURFACECURVE(#3104,(#3831),.PCURVE_S1.);
#2362=IFCSURFACECURVE(#3105,(#3832),.PCURVE_S1.);
#2363=IFCSURFACECURVE(#3106,(#3833),.PCURVE_S1.);
#2364=IFCSURFACECURVE(#3107,(#3834),.PCURVE_S1.);
#2365=IFCSURFACECURVE(#3108,(#3835),.PCURVE_S1.);
#2366=IFCSURFACECURVE(#3109,(#3836),.PCURVE_S1.);
#2367=IFCSURFACECURVE(#3110,(#3837),.PCURVE_S1.);
#2368=IFCSURFACECURVE(#3111,(#3838),.PCURVE_S1.);
#2369=IFCSURFACECURVE(#3112,(#3839),.PCURVE_S1.);
#2370=IFCSURFACECURVE(#3113,(#3840),.PCURVE_S1.);
#2371=IFCSURFACECURVE(#3114,(#3841),.PCURVE_S1.);
#2372=IFCSURFACECURVE(#3115,(#3842),.PCURVE_S1.);
#2373=IFCSURFACECURVE(#3116,(#3843),.PCURVE_S1.);
#2374=IFCSURFACECURVE(#3117,(#3844),.PCURVE_S1.);
#2375=IFCSURFACECURVE(#3118,(#3845),.PCURVE_S1.);
#2376=IFCSURFACECURVE(#3119,(#3846),.PCURVE_S1.);
#2377=IFCSURFACECURVE(#3120,(#3847),.PCURVE_S1.);
#2378=IFCSURFACECURVE(#3121,(#3848),.PCURVE_S1.);
#2379=IFCSURFACECURVE(#3122,(#3849),.PCURVE_S1.);
#2380=IFCSURFACECURVE(#3123,(#3850),.PCURVE_S1.);
#2381=IFCSURFACECURVE(#3124,(#3851),.PCURVE_S1.);
#2382=IFCSURFACECURVE(#3125,(#3852),.PCURVE_S1.);
#2383=IFCSURFACECURVE(#3126,(#3853),.PCURVE_S1.);
#2384=IFCSURFACECURVE(#3127,(#3854),.PCURVE_S1.);
#2385=IFCSURFACECURVE(#31,(#3855),.PCURVE_S1.);
#2386=IFCSURFACECURVE(#32,(#3856),.PCURVE_S1.);
#2387=IFCSURFACECURVE(#33,(#3857),.PCURVE_S1.);
#2388=IFCSURFACECURVE(#34,(#3858),.PCURVE_S1.);
#2389=IFCSURFACECURVE(#35,(#3859),.PCURVE_S1.);
#2390=IFCSURFACECURVE(#36,(#3860),.PCURVE_S1.);
#2391=IFCSURFACECURVE(#37,(#3861),.PCURVE_S1.);
#2392=IFCSURFACECURVE(#38,(#3862),.PCURVE_S1.);
#2393=IFCSURFACECURVE(#39,(#3863),.PCURVE_S1.);
#2394=IFCSURFACECURVE(#40,(#3864),.PCURVE_S1.);
#2395=IFCSURFACECURVE(#41,(#3865),.PCURVE_S1.);
#2396=IFCSURFACECURVE(#42,(#3866),.PCURVE_S1.);
#2397=IFCSURFACECURVE(#3128,(#3867),.PCURVE_S1.);
#2398=IFCSURFACECURVE(#3129,(#3868),.PCURVE_S1.);
#2399=IFCSURFACECURVE(#3130,(#3869),.PCURVE_S1.);
#2400=IFCSURFACECURVE(#3131,(#3870),.PCURVE_S1.);
#2401=IFCSURFACECURVE(#3132,(#3871),.PCURVE_S1.);
#2402=IFCSURFACECURVE(#3133,(#3872),.PCURVE_S1.);
#2403=IFCSURFACECURVE(#3134,(#3873),.PCURVE_S1.);
#2404=IFCSURFACECURVE(#3135,(#3874),.PCURVE_S1.);
#2405=IFCSURFACECURVE(#3136,(#3875),.PCURVE_S1.);
#2406=IFCSURFACECURVE(#3137,(#3876),.PCURVE_S1.);
#2407=IFCSURFACECURVE(#3138,(#3877),.PCURVE_S1.);
#2408=IFCSURFACECURVE(#3139,(#3878),.PCURVE_S1.);
#2409=IFCSURFACECURVE(#3140,(#3879),.PCURVE_S1.);
#2410=IFCSURFACECURVE(#3141,(#3880),.PCURVE_S1.);
#2411=IFCSURFACECURVE(#3142,(#3881),.PCURVE_S1.);
#2412=IFCSURFACECURVE(#3143,(#3882),.PCURVE_S1.);
#2413=IFCSURFACECURVE(#3144,(#3883),.PCURVE_S1.);
#2414=IFCSURFACECURVE(#3145,(#3884),.PCURVE_S1.);
#2415=IFCSURFACECURVE(#3146,(#3885),.PCURVE_S1.);
#2416=IFCSURFACECURVE(#3147,(#3886),.PCURVE_S1.);
#2417=IFCSURFACECURVE(#3148,(#3887),.PCURVE_S1.);
#2418=IFCSURFACECURVE(#3149,(#3888),.PCURVE_S1.);
#2419=IFCSURFACECURVE(#3150,(#3889),.PCURVE_S1.);
#2420=IFCSURFACECURVE(#3151,(#3890),.PCURVE_S1.);
#2421=IFCSURFACECURVE(#3152,(#3891),.PCURVE_S1.);
#2422=IFCSURFACECURVE(#3153,(#3892),.PCURVE_S1.);
#2423=IFCSURFACECURVE(#3154,(#3893),.PCURVE_S1.);
#2424=IFCSURFACECURVE(#3155,(#3894),.PCURVE_S1.);
#2425=IFCSURFACECURVE(#3156,(#3895),.PCURVE_S1.);
#2426=IFCSURFACECURVE(#3157,(#3896),.PCURVE_S1.);
#2427=IFCSURFACECURVE(#3158,(#3897),.PCURVE_S1.);
#2428=IFCSURFACECURVE(#3159,(#3898),.PCURVE_S1.);
#2429=IFCSURFACECURVE(#3160,(#3899),.PCURVE_S1.);
#2430=IFCSURFACECURVE(#3161,(#3900),.PCURVE_S1.);
#2431=IFCSURFACECURVE(#3162,(#3901),.PCURVE_S1.);
#2432=IFCSURFACECURVE(#3163,(#3902),.PCURVE_S1.);
#2433=IFCSURFACECURVE(#3164,(#3903),.PCURVE_S1.);
#2434=IFCSURFACECURVE(#3165,(#3904),.PCURVE_S1.);
#2435=IFCSURFACECURVE(#3166,(#3905),.PCURVE_S1.);
#2436=IFCSURFACECURVE(#3167,(#3906),.PCURVE_S1.);
#2437=IFCSURFACECURVE(#3168,(#3907),.PCURVE_S1.);
#2438=IFCSURFACECURVE(#3169,(#3908),.PCURVE_S1.);
#2439=IFCSURFACECURVE(#3170,(#3909),.PCURVE_S1.);
#2440=IFCSURFACECURVE(#3171,(#3910),.PCURVE_S1.);
#2441=IFCSURFACECURVE(#3172,(#3911),.PCURVE_S1.);
#2442=IFCSURFACECURVE(#3173,(#3912),.PCURVE_S1.);
#2443=IFCSURFACECURVE(#3174,(#3913),.PCURVE_S1.);
#2444=IFCSURFACECURVE(#3175,(#3914),.PCURVE_S1.);
#2445=IFCSURFACECURVE(#3176,(#3915),.PCURVE_S1.);
#2446=IFCSURFACECURVE(#3177,(#3916),.PCURVE_S1.);
#2447=IFCSURFACECURVE(#3178,(#3917),.PCURVE_S1.);
#2448=IFCSURFACECURVE(#3179,(#3918),.PCURVE_S1.);
#2449=IFCSURFACECURVE(#3180,(#3919),.PCURVE_S1.);
#2450=IFCSURFACECURVE(#3181,(#3920),.PCURVE_S1.);
#2451=IFCSURFACECURVE(#3182,(#3921),.PCURVE_S1.);
#2452=IFCSURFACECURVE(#3183,(#3922),.PCURVE_S1.);
#2453=IFCSURFACECURVE(#3184,(#3923),.PCURVE_S1.);
#2454=IFCSURFACECURVE(#3185,(#3924),.PCURVE_S1.);
#2455=IFCSURFACECURVE(#3186,(#3925),.PCURVE_S1.);
#2456=IFCSURFACECURVE(#3187,(#3926),.PCURVE_S1.);
#2457=IFCSURFACECURVE(#3188,(#3927),.PCURVE_S1.);
#2458=IFCSURFACECURVE(#3189,(#3928),.PCURVE_S1.);
#2459=IFCSURFACECURVE(#3190,(#3929),.PCURVE_S1.);
#2460=IFCSURFACECURVE(#3191,(#3930),.PCURVE_S1.);
#2461=IFCSURFACECURVE(#3192,(#3931),.PCURVE_S1.);
#2462=IFCSURFACECURVE(#3193,(#3932),.PCURVE_S1.);
#2463=IFCSURFACECURVE(#3194,(#3933),.PCURVE_S1.);
#2464=IFCSURFACECURVE(#3195,(#3934),.PCURVE_S1.);
#2465=IFCSURFACECURVE(#3196,(#3935),.PCURVE_S1.);
#2466=IFCSURFACECURVE(#3197,(#3936),.PCURVE_S1.);
#2467=IFCSURFACECURVE(#3198,(#3937),.PCURVE_S1.);
#2468=IFCSURFACECURVE(#3199,(#3938),.PCURVE_S1.);
#2469=IFCSURFACECURVE(#3200,(#3939),.PCURVE_S1.);
#2470=IFCSURFACECURVE(#3201,(#3940),.PCURVE_S1.);
#2471=IFCSURFACECURVE(#3202,(#3941),.PCURVE_S1.);
#2472=IFCSURFACECURVE(#3203,(#3942),.PCURVE_S1.);
#2473=IFCSURFACECURVE(#3204,(#3943),.PCURVE_S1.);
#2474=IFCSURFACECURVE(#3205,(#3944),.PCURVE_S1.);
#2475=IFCSURFACECURVE(#3206,(#3945),.PCURVE_S1.);
#2476=IFCSURFACECURVE(#3207,(#3946),.PCURVE_S1.);
#2477=IFCSURFACECURVE(#3208,(#3947),.PCURVE_S1.);
#2478=IFCSURFACECURVE(#3209,(#3948),.PCURVE_S1.);
#2479=IFCSURFACECURVE(#3210,(#3949),.PCURVE_S1.);
#2480=IFCSURFACECURVE(#3211,(#3950),.PCURVE_S1.);
#2481=IFCSURFACECURVE(#3212,(#3951),.PCURVE_S1.);
#2482=IFCSURFACECURVE(#3213,(#3952),.PCURVE_S1.);
#2483=IFCSURFACECURVE(#3214,(#3953),.PCURVE_S1.);
#2484=IFCSURFACECURVE(#3215,(#3954),.PCURVE_S1.);
#2485=IFCSURFACECURVE(#3216,(#3955),.PCURVE_S1.);
#2486=IFCSURFACECURVE(#3217,(#3956),.PCURVE_S1.);
#2487=IFCSURFACECURVE(#3218,(#3957),.PCURVE_S1.);
#2488=IFCSURFACECURVE(#3219,(#3958),.PCURVE_S1.);
#2489=IFCSURFACECURVE(#3220,(#3959),.PCURVE_S1.);
#2490=IFCSURFACECURVE(#3221,(#3960),.PCURVE_S1.);
#2491=IFCSURFACECURVE(#3222,(#3961),.PCURVE_S1.);
#2492=IFCSURFACECURVE(#3223,(#3962),.PCURVE_S1.);
#2493=IFCSURFACECURVE(#3224,(#3963),.PCURVE_S1.);
#2494=IFCSURFACECURVE(#3225,(#3964),.PCURVE_S1.);
#2495=IFCSURFACECURVE(#3226,(#3965),.PCURVE_S1.);
#2496=IFCSURFACECURVE(#3227,(#3966),.PCURVE_S1.);
#2497=IFCSURFACECURVE(#3228,(#3967),.PCURVE_S1.);
#2498=IFCSURFACECURVE(#3229,(#3968),.PCURVE_S1.);
#2499=IFCSURFACECURVE(#3230,(#3969),.PCURVE_S1.);
#2500=IFCSURFACECURVE(#3231,(#3970),.PCURVE_S1.);
#2501=IFCSURFACECURVE(#3232,(#3971),.PCURVE_S1.);
#2502=IFCSURFACECURVE(#3233,(#3972),.PCURVE_S1.);
#2503=IFCSURFACECURVE(#3234,(#3973),.PCURVE_S1.);
#2504=IFCSURFACECURVE(#3235,(#3974),.PCURVE_S1.);
#2505=IFCSURFACECURVE(#3236,(#3975),.PCURVE_S1.);
#2506=IFCSURFACECURVE(#3237,(#3976),.PCURVE_S1.);
#2507=IFCSURFACECURVE(#3238,(#3977),.PCURVE_S1.);
#2508=IFCSURFACECURVE(#3239,(#3978),.PCURVE_S1.);
#2509=IFCSURFACECURVE(#3240,(#3979),.PCURVE_S1.);
#2510=IFCSURFACECURVE(#3241,(#3980),.PCURVE_S1.);
#2511=IFCSURFACECURVE(#3242,(#3981),.PCURVE_S1.);
#2512=IFCSURFACECURVE(#3243,(#3982),.PCURVE_S1.);
#2513=IFCSURFACECURVE(#3244,(#3983),.PCURVE_S1.);
#2514=IFCSURFACECURVE(#3245,(#3984),.PCURVE_S1.);
#2515=IFCSURFACECURVE(#3246,(#3985),.PCURVE_S1.);
#2516=IFCSURFACECURVE(#3247,(#3986),.PCURVE_S1.);
#2517=IFCSURFACECURVE(#3248,(#3987),.PCURVE_S1.);
#2518=IFCSURFACECURVE(#3249,(#3988),.PCURVE_S1.);
#2519=IFCSURFACECURVE(#3250,(#3989),.PCURVE_S1.);
#2520=IFCSURFACECURVE(#3251,(#3990),.PCURVE_S1.);
#2521=IFCSURFACECURVE(#3252,(#3991),.PCURVE_S1.);
#2522=IFCSURFACECURVE(#3253,(#3992),.PCURVE_S1.);
#2523=IFCSURFACECURVE(#3254,(#3993),.PCURVE_S1.);
#2524=IFCSURFACECURVE(#3255,(#3994),.PCURVE_S1.);
#2525=IFCSURFACECURVE(#3256,(#3995),.PCURVE_S1.);
#2526=IFCSURFACECURVE(#3257,(#3996),.PCURVE_S1.);
#2527=IFCSURFACECURVE(#3258,(#3997),.PCURVE_S1.);
#2528=IFCSURFACECURVE(#3259,(#3998),.PCURVE_S1.);
#2529=IFCSURFACECURVE(#3260,(#3999),.PCURVE_S1.);
#2530=IFCSURFACECURVE(#3261,(#4000),.PCURVE_S1.);
#2531=IFCSURFACECURVE(#3262,(#4001),.PCURVE_S1.);
#2532=IFCSURFACECURVE(#3263,(#4002),.PCURVE_S1.);
#2533=IFCSURFACECURVE(#3264,(#4003),.PCURVE_S1.);
#2534=IFCSURFACECURVE(#3265,(#4004),.PCURVE_S1.);
#2535=IFCSURFACECURVE(#3266,(#4005),.PCURVE_S1.);
#2536=IFCSURFACECURVE(#3267,(#4006),.PCURVE_S1.);
#2537=IFCSURFACECURVE(#3268,(#4007),.PCURVE_S1.);
#2538=IFCSURFACECURVE(#3269,(#4008),.PCURVE_S1.);
#2539=IFCSURFACECURVE(#3270,(#4009),.PCURVE_S1.);
#2540=IFCSURFACECURVE(#3271,(#4010),.PCURVE_S1.);
#2541=IFCSURFACECURVE(#3272,(#4011),.PCURVE_S1.);
#2542=IFCSURFACECURVE(#3273,(#4012),.PCURVE_S1.);
#2543=IFCSURFACECURVE(#3274,(#4013),.PCURVE_S1.);
#2544=IFCSURFACECURVE(#3275,(#4014),.PCURVE_S1.);
#2545=IFCSURFACECURVE(#3276,(#4015),.PCURVE_S1.);
#2546=IFCSURFACECURVE(#3277,(#4016),.PCURVE_S1.);
#2547=IFCSURFACECURVE(#3278,(#4017),.PCURVE_S1.);
#2548=IFCSURFACECURVE(#3279,(#4018),.PCURVE_S1.);
#2549=IFCSURFACECURVE(#3280,(#4019),.PCURVE_S1.);
#2550=IFCSURFACECURVE(#3281,(#4020),.PCURVE_S1.);
#2551=IFCSURFACECURVE(#3282,(#4021),.PCURVE_S1.);
#2552=IFCSURFACECURVE(#3283,(#4022),.PCURVE_S1.);
#2553=IFCSURFACECURVE(#3284,(#4023),.PCURVE_S1.);
#2554=IFCSURFACECURVE(#3285,(#4024),.PCURVE_S1.);
#2555=IFCSURFACECURVE(#3286,(#4025),.PCURVE_S1.);
#2556=IFCSURFACECURVE(#3287,(#4026),.PCURVE_S1.);
#2557=IFCSURFACECURVE(#3288,(#4027),.PCURVE_S1.);
#2558=IFCSURFACECURVE(#3289,(#4028),.PCURVE_S1.);
#2559=IFCSURFACECURVE(#3290,(#4029),.PCURVE_S1.);
#2560=IFCSURFACECURVE(#3291,(#4030),.PCURVE_S1.);
#2561=IFCSURFACECURVE(#3292,(#4031),.PCURVE_S1.);
#2562=IFCSURFACECURVE(#3293,(#4032),.PCURVE_S1.);
#2563=IFCSURFACECURVE(#3294,(#4033),.PCURVE_S1.);
#2564=IFCSURFACECURVE(#3295,(#4034),.PCURVE_S1.);
#2565=IFCSURFACECURVE(#3296,(#4035),.PCURVE_S1.);
#2566=IFCSURFACECURVE(#3297,(#4036),.PCURVE_S1.);
#2567=IFCSURFACECURVE(#3298,(#4037),.PCURVE_S1.);
#2568=IFCSURFACECURVE(#3299,(#4038),.PCURVE_S1.);
#2569=IFCSURFACECURVE(#3300,(#4039),.PCURVE_S1.);
#2570=IFCSURFACECURVE(#3301,(#4040),.PCURVE_S1.);
#2571=IFCSURFACECURVE(#3302,(#4041),.PCURVE_S1.);
#2572=IFCSURFACECURVE(#3303,(#4042),.PCURVE_S1.);
#2573=IFCSURFACECURVE(#3304,(#4043),.PCURVE_S1.);
#2574=IFCSURFACECURVE(#3305,(#4044),.PCURVE_S1.);
#2575=IFCSURFACECURVE(#3306,(#4045),.PCURVE_S1.);
#2576=IFCSURFACECURVE(#3307,(#4046),.PCURVE_S1.);
#2577=IFCSURFACECURVE(#3308,(#4047),.PCURVE_S1.);
#2578=IFCSURFACECURVE(#3309,(#4048),.PCURVE_S1.);
#2579=IFCSURFACECURVE(#3310,(#4049),.PCURVE_S1.);
#2580=IFCSURFACECURVE(#3311,(#4050),.PCURVE_S1.);
#2581=IFCSURFACECURVE(#3312,(#4051),.PCURVE_S1.);
#2582=IFCSURFACECURVE(#3313,(#4052),.PCURVE_S1.);
#2583=IFCSURFACECURVE(#3314,(#4053),.PCURVE_S1.);
#2584=IFCSURFACECURVE(#3315,(#4054),.PCURVE_S1.);
#2585=IFCSURFACECURVE(#3316,(#4055),.PCURVE_S1.);
#2586=IFCSURFACECURVE(#3317,(#4056),.PCURVE_S1.);
#2587=IFCSURFACECURVE(#3318,(#4057),.PCURVE_S1.);
#2588=IFCSURFACECURVE(#3319,(#4058),.PCURVE_S1.);
#2589=IFCSURFACECURVE(#3320,(#4059),.PCURVE_S1.);
#2590=IFCSURFACECURVE(#3321,(#4060),.PCURVE_S1.);
#2591=IFCSURFACECURVE(#3322,(#4061),.PCURVE_S1.);
#2592=IFCSURFACECURVE(#3323,(#4062),.PCURVE_S1.);
#2593=IFCSURFACECURVE(#3324,(#4063),.PCURVE_S1.);
#2594=IFCSURFACECURVE(#3325,(#4064),.PCURVE_S1.);
#2595=IFCSURFACECURVE(#3326,(#4065),.PCURVE_S1.);
#2596=IFCSURFACECURVE(#3327,(#4066),.PCURVE_S1.);
#2597=IFCSURFACECURVE(#3328,(#4067),.PCURVE_S1.);
#2598=IFCSURFACECURVE(#3329,(#4068),.PCURVE_S1.);
#2599=IFCSURFACECURVE(#3330,(#4069),.PCURVE_S1.);
#2600=IFCSURFACECURVE(#3331,(#4070),.PCURVE_S1.);
#2601=IFCSURFACECURVE(#3332,(#4071),.PCURVE_S1.);
#2602=IFCSURFACECURVE(#3333,(#4072),.PCURVE_S1.);
#2603=IFCSURFACECURVE(#3334,(#4073),.PCURVE_S1.);
#2604=IFCSURFACECURVE(#3335,(#4074),.PCURVE_S1.);
#2605=IFCSURFACECURVE(#3336,(#4075),.PCURVE_S1.);
#2606=IFCSURFACECURVE(#3337,(#4076),.PCURVE_S1.);
#2607=IFCSURFACECURVE(#3338,(#4077),.PCURVE_S1.);
#2608=IFCSURFACECURVE(#3339,(#4078),.PCURVE_S1.);
#2609=IFCSURFACECURVE(#3340,(#4079),.PCURVE_S1.);
#2610=IFCSURFACECURVE(#3341,(#4080),.PCURVE_S1.);
#2611=IFCSURFACECURVE(#3342,(#4081),.PCURVE_S1.);
#2612=IFCSURFACECURVE(#3343,(#4082),.PCURVE_S1.);
#2613=IFCSURFACECURVE(#3344,(#4083),.PCURVE_S1.);
#2614=IFCSURFACECURVE(#3345,(#4084),.PCURVE_S1.);
#2615=IFCSURFACECURVE(#3346,(#4085),.PCURVE_S1.);
#2616=IFCSURFACECURVE(#3347,(#4086),.PCURVE_S1.);
#2617=IFCSURFACECURVE(#3348,(#4087),.PCURVE_S1.);
#2618=IFCSURFACECURVE(#3349,(#4088),.PCURVE_S1.);
#2619=IFCSURFACECURVE(#3350,(#4089),.PCURVE_S1.);
#2620=IFCSURFACECURVE(#3351,(#4090),.PCURVE_S1.);
#2621=IFCSURFACECURVE(#3352,(#4091),.PCURVE_S1.);
#2622=IFCSURFACECURVE(#3353,(#4092),.PCURVE_S1.);
#2623=IFCSURFACECURVE(#3354,(#4093),.PCURVE_S1.);
#2624=IFCSURFACECURVE(#3355,(#4094),.PCURVE_S1.);
#2625=IFCSURFACECURVE(#3356,(#4095),.PCURVE_S1.);
#2626=IFCSURFACECURVE(#3357,(#4096),.PCURVE_S1.);
#2627=IFCSURFACECURVE(#3358,(#4097),.PCURVE_S1.);
#2628=IFCSURFACECURVE(#3359,(#4098),.PCURVE_S1.);
#2629=IFCSURFACECURVE(#3360,(#4099),.PCURVE_S1.);
#2630=IFCSURFACECURVE(#3361,(#4100),.PCURVE_S1.);
#2631=IFCSURFACECURVE(#3362,(#4101),.PCURVE_S1.);
#2632=IFCSURFACECURVE(#3363,(#4102),.PCURVE_S1.);
#2633=IFCSURFACECURVE(#3364,(#4103),.PCURVE_S1.);
#2634=IFCSURFACECURVE(#3365,(#4104),.PCURVE_S1.);
#2635=IFCSURFACECURVE(#3366,(#4105),.PCURVE_S1.);
#2636=IFCSURFACECURVE(#3367,(#4106),.PCURVE_S1.);
#2637=IFCSURFACECURVE(#3368,(#4107),.PCURVE_S1.);
#2638=IFCSURFACECURVE(#3369,(#4108),.PCURVE_S1.);
#2639=IFCSURFACECURVE(#3370,(#4109),.PCURVE_S1.);
#2640=IFCSURFACECURVE(#3371,(#4110),.PCURVE_S1.);
#2641=IFCSURFACECURVE(#3372,(#4111),.PCURVE_S1.);
#2642=IFCSURFACECURVE(#3373,(#4112),.PCURVE_S1.);
#2643=IFCSURFACECURVE(#3374,(#4113),.PCURVE_S1.);
#2644=IFCSURFACECURVE(#3375,(#4114),.PCURVE_S1.);
#2645=IFCSURFACECURVE(#3376,(#4115),.PCURVE_S1.);
#2646=IFCSURFACECURVE(#3377,(#4116),.PCURVE_S1.);
#2647=IFCSURFACECURVE(#3378,(#4117),.PCURVE_S1.);
#2648=IFCSURFACECURVE(#3379,(#4118),.PCURVE_S1.);
#2649=IFCSURFACECURVE(#3380,(#4119),.PCURVE_S1.);
#2650=IFCSURFACECURVE(#3381,(#4120),.PCURVE_S1.);
#2651=IFCSURFACECURVE(#3382,(#4121),.PCURVE_S1.);
#2652=IFCSURFACECURVE(#3383,(#4122),.PCURVE_S1.);
#2653=IFCSURFACECURVE(#3384,(#4123),.PCURVE_S1.);
#2654=IFCSURFACECURVE(#3385,(#4124),.PCURVE_S1.);
#2655=IFCSURFACECURVE(#3386,(#4125),.PCURVE_S1.);
#2656=IFCSURFACECURVE(#3387,(#4126),.PCURVE_S1.);
#2657=IFCSURFACECURVE(#3388,(#4127),.PCURVE_S1.);
#2658=IFCSURFACECURVE(#3389,(#4128),.PCURVE_S1.);
#2659=IFCSURFACECURVE(#3390,(#4129),.PCURVE_S1.);
#2660=IFCSURFACECURVE(#3391,(#4130),.PCURVE_S1.);
#2661=IFCSURFACECURVE(#3392,(#4131),.PCURVE_S1.);
#2662=IFCSURFACECURVE(#3393,(#4132),.PCURVE_S1.);
#2663=IFCSURFACECURVE(#3394,(#4133),.PCURVE_S1.);
#2664=IFCSURFACECURVE(#3395,(#4134),.PCURVE_S1.);
#2665=IFCSURFACECURVE(#3396,(#4135),.PCURVE_S1.);
#2666=IFCSURFACECURVE(#3397,(#4136),.PCURVE_S1.);
#2667=IFCSURFACECURVE(#3398,(#4137),.PCURVE_S1.);
#2668=IFCSURFACECURVE(#3399,(#4138),.PCURVE_S1.);
#2669=IFCSURFACECURVE(#3400,(#4139),.PCURVE_S1.);
#2670=IFCSURFACECURVE(#3401,(#4140),.PCURVE_S1.);
#2671=IFCSURFACECURVE(#3402,(#4141),.PCURVE_S1.);
#2672=IFCSURFACECURVE(#3403,(#4142),.PCURVE_S1.);
#2673=IFCSURFACECURVE(#3404,(#4143),.PCURVE_S1.);
#2674=IFCSURFACECURVE(#3405,(#4144),.PCURVE_S1.);
#2675=IFCSURFACECURVE(#3406,(#4145),.PCURVE_S1.);
#2676=IFCSURFACECURVE(#3407,(#4146),.PCURVE_S1.);
#2677=IFCSURFACECURVE(#3408,(#4147),.PCURVE_S1.);
#2678=IFCSURFACECURVE(#3409,(#4148),.PCURVE_S1.);
#2679=IFCSURFACECURVE(#3410,(#4149),.PCURVE_S1.);
#2680=IFCSURFACECURVE(#3411,(#4150),.PCURVE_S1.);
#2681=IFCSURFACECURVE(#3412,(#4151),.PCURVE_S1.);
#2682=IFCSURFACECURVE(#3413,(#4152),.PCURVE_S1.);
#2683=IFCSURFACECURVE(#3414,(#4153),.PCURVE_S1.);
#2684=IFCSURFACECURVE(#3415,(#4154),.PCURVE_S1.);
#2685=IFCSURFACECURVE(#3416,(#4155),.PCURVE_S1.);
#2686=IFCSURFACECURVE(#3417,(#4156),.PCURVE_S1.);
#2687=IFCSURFACECURVE(#3418,(#4157),.PCURVE_S1.);
#2688=IFCSURFACECURVE(#3419,(#4158),.PCURVE_S1.);
#2689=IFCSURFACECURVE(#3420,(#4159),.PCURVE_S1.);
#2690=IFCSURFACECURVE(#3421,(#4160),.PCURVE_S1.);
#2691=IFCSURFACECURVE(#3422,(#4161),.PCURVE_S1.);
#2692=IFCSURFACECURVE(#3423,(#4162),.PCURVE_S1.);
#2693=IFCSURFACECURVE(#3424,(#4163),.PCURVE_S1.);
#2694=IFCSURFACECURVE(#3425,(#4164),.PCURVE_S1.);
#2695=IFCSURFACECURVE(#3426,(#4165),.PCURVE_S1.);
#2696=IFCSURFACECURVE(#3427,(#4166),.PCURVE_S1.);
#2697=IFCSURFACECURVE(#3428,(#4167),.PCURVE_S1.);
#2698=IFCSURFACECURVE(#3429,(#4168),.PCURVE_S1.);
#2699=IFCSURFACECURVE(#3430,(#4169),.PCURVE_S1.);
#2700=IFCSURFACECURVE(#3431,(#4170),.PCURVE_S1.);
#2701=IFCSURFACECURVE(#3432,(#4171),.PCURVE_S1.);
#2702=IFCSURFACECURVE(#3433,(#4172),.PCURVE_S1.);
#2703=IFCSURFACECURVE(#3434,(#4173),.PCURVE_S1.);
#2704=IFCSURFACECURVE(#3435,(#4174),.PCURVE_S1.);
#2705=IFCSURFACECURVE(#3436,(#4175),.PCURVE_S1.);
#2706=IFCSURFACECURVE(#3437,(#4176),.PCURVE_S1.);
#2707=IFCSURFACECURVE(#3438,(#4177),.PCURVE_S1.);
#2708=IFCSURFACECURVE(#3439,(#4178),.PCURVE_S1.);
#2709=IFCSURFACECURVE(#3440,(#4179),.PCURVE_S1.);
#2710=IFCSURFACECURVE(#3441,(#4180),.PCURVE_S1.);
#2711=IFCSURFACECURVE(#3442,(#4181),.PCURVE_S1.);
#2712=IFCSURFACECURVE(#3443,(#4182),.PCURVE_S1.);
#2713=IFCSURFACECURVE(#3444,(#4183),.PCURVE_S1.);
#2714=IFCSURFACECURVE(#3445,(#4184),.PCURVE_S1.);
#2715=IFCSURFACECURVE(#3446,(#4185),.PCURVE_S1.);
#2716=IFCSURFACECURVE(#3447,(#4186),.PCURVE_S1.);
#2717=IFCSURFACECURVE(#3448,(#4187),.PCURVE_S1.);
#2718=IFCSURFACECURVE(#3449,(#4188),.PCURVE_S1.);
#2719=IFCSURFACECURVE(#3450,(#4189),.PCURVE_S1.);
#2720=IFCSURFACECURVE(#3451,(#4190),.PCURVE_S1.);
#2721=IFCSURFACECURVE(#3452,(#4191),.PCURVE_S1.);
#2722=IFCSURFACECURVE(#3453,(#4192),.PCURVE_S1.);
#2723=IFCSURFACECURVE(#3454,(#4193),.PCURVE_S1.);
#2724=IFCSURFACECURVE(#3455,(#4194),.PCURVE_S1.);
#2725=IFCSURFACECURVE(#3456,(#4195),.PCURVE_S1.);
#2726=IFCSURFACECURVE(#3457,(#4196),.PCURVE_S1.);
#2727=IFCSURFACECURVE(#3458,(#4197),.PCURVE_S1.);
#2728=IFCSURFACECURVE(#3459,(#4198),.PCURVE_S1.);
#2729=IFCSURFACECURVE(#3460,(#4199),.PCURVE_S1.);
#2730=IFCSURFACECURVE(#3461,(#4200),.PCURVE_S1.);
#2731=IFCSURFACECURVE(#3462,(#4201),.PCURVE_S1.);
#2732=IFCSURFACECURVE(#3463,(#4202),.PCURVE_S1.);
#2733=IFCSURFACECURVE(#3464,(#4203),.PCURVE_S1.);
#2734=IFCSURFACECURVE(#3465,(#4204),.PCURVE_S1.);
#2735=IFCSURFACECURVE(#3466,(#4205),.PCURVE_S1.);
#2736=IFCSURFACECURVE(#3467,(#4206),.PCURVE_S1.);
#2737=IFCSURFACECURVE(#3468,(#4207),.PCURVE_S1.);
#2738=IFCSURFACECURVE(#3469,(#4208),.PCURVE_S1.);
#2739=IFCSURFACECURVE(#3470,(#4209),.PCURVE_S1.);
#2740=IFCSURFACECURVE(#3471,(#4210),.PCURVE_S1.);
#2741=IFCSURFACECURVE(#3472,(#4211),.PCURVE_S1.);
#2742=IFCSURFACECURVE(#3473,(#4212),.PCURVE_S1.);
#2743=IFCSURFACECURVE(#3474,(#4213),.PCURVE_S1.);
#2744=IFCSURFACECURVE(#3475,(#4214),.PCURVE_S1.);
#2745=IFCSURFACECURVE(#3476,(#4215),.PCURVE_S1.);
#2746=IFCSURFACECURVE(#3477,(#4216),.PCURVE_S1.);
#2747=IFCSURFACECURVE(#3478,(#4217),.PCURVE_S1.);
#2748=IFCSURFACECURVE(#3479,(#4218),.PCURVE_S1.);
#2749=IFCSURFACECURVE(#3480,(#4219),.PCURVE_S1.);
#2750=IFCSURFACECURVE(#3481,(#4220),.PCURVE_S1.);
#2751=IFCSURFACECURVE(#3482,(#4221),.PCURVE_S1.);
#2752=IFCSURFACECURVE(#3483,(#4222),.PCURVE_S1.);
#2753=IFCSURFACECURVE(#3484,(#4223),.PCURVE_S1.);
#2754=IFCSURFACECURVE(#3485,(#4224),.PCURVE_S1.);
#2755=IFCSURFACECURVE(#3486,(#4225),.PCURVE_S1.);
#2756=IFCSURFACECURVE(#3487,(#4226),.PCURVE_S1.);
#2757=IFCSURFACECURVE(#3488,(#4227),.PCURVE_S1.);
#2758=IFCSURFACECURVE(#3489,(#4228),.PCURVE_S1.);
#2759=IFCSURFACECURVE(#3490,(#4229),.PCURVE_S1.);
#2760=IFCSURFACECURVE(#3491,(#4230),.PCURVE_S1.);
#2761=IFCSURFACECURVE(#3492,(#4231),.PCURVE_S1.);
#2762=IFCSURFACECURVE(#3493,(#4232),.PCURVE_S1.);
#2763=IFCSURFACECURVE(#3494,(#4233),.PCURVE_S1.);
#2764=IFCSURFACECURVE(#3495,(#4234),.PCURVE_S1.);
#2765=IFCSURFACECURVE(#3496,(#4235),.PCURVE_S1.);
#2766=IFCSURFACECURVE(#3497,(#4236),.PCURVE_S1.);
#2767=IFCSURFACECURVE(#3498,(#4237),.PCURVE_S1.);
#2768=IFCSURFACECURVE(#3499,(#4238),.PCURVE_S1.);
#2769=IFCSURFACECURVE(#3500,(#4239),.PCURVE_S1.);
#2770=IFCSURFACECURVE(#3501,(#4240),.PCURVE_S1.);
#2771=IFCSURFACECURVE(#3502,(#4241),.PCURVE_S1.);
#2772=IFCSURFACECURVE(#3503,(#4242),.PCURVE_S1.);
#2773=IFCSURFACECURVE(#3504,(#4243),.PCURVE_S1.);
#2774=IFCSURFACECURVE(#3505,(#4244),.PCURVE_S1.);
#2775=IFCSURFACECURVE(#3506,(#4245),.PCURVE_S1.);
#2776=IFCSURFACECURVE(#3507,(#4246),.PCURVE_S1.);
#2777=IFCSURFACECURVE(#3508,(#4247),.PCURVE_S1.);
#2778=IFCSURFACECURVE(#3509,(#4248),.PCURVE_S1.);
#2779=IFCSURFACECURVE(#3510,(#4249),.PCURVE_S1.);
#2780=IFCSURFACECURVE(#3511,(#4250),.PCURVE_S1.);
#2781=IFCSURFACECURVE(#3512,(#4251),.PCURVE_S1.);
#2782=IFCSURFACECURVE(#3513,(#4252),.PCURVE_S1.);
#2783=IFCSURFACECURVE(#3514,(#4253),.PCURVE_S1.);
#2784=IFCSURFACECURVE(#3515,(#4254),.PCURVE_S1.);
#2785=IFCSURFACECURVE(#3516,(#4255),.PCURVE_S1.);
#2786=IFCSURFACECURVE(#3517,(#4256),.PCURVE_S1.);
#2787=IFCSURFACECURVE(#3518,(#4257),.PCURVE_S1.);
#2788=IFCSURFACECURVE(#3519,(#4258),.PCURVE_S1.);
#2789=IFCSURFACECURVE(#3520,(#4259),.PCURVE_S1.);
#2790=IFCSURFACECURVE(#3521,(#4260),.PCURVE_S1.);
#2791=IFCSURFACECURVE(#3522,(#4261),.PCURVE_S1.);
#2792=IFCSURFACECURVE(#3523,(#4262),.PCURVE_S1.);
#2793=IFCSURFACECURVE(#3524,(#4263),.PCURVE_S1.);
#2794=IFCSURFACECURVE(#3525,(#4264),.PCURVE_S1.);
#2795=IFCSURFACECURVE(#3526,(#4265),.PCURVE_S1.);
#2796=IFCSURFACECURVE(#3527,(#4266),.PCURVE_S1.);
#2797=IFCSURFACECURVE(#3528,(#4267),.PCURVE_S1.);
#2798=IFCSURFACECURVE(#3529,(#4268),.PCURVE_S1.);
#2799=IFCSURFACECURVE(#3530,(#4269),.PCURVE_S1.);
#2800=IFCSURFACECURVE(#3531,(#4270),.PCURVE_S1.);
#2801=IFCSURFACECURVE(#3532,(#4271),.PCURVE_S1.);
#2802=IFCSURFACECURVE(#3533,(#4272),.PCURVE_S1.);
#2803=IFCSURFACECURVE(#3534,(#4273),.PCURVE_S1.);
#2804=IFCSURFACECURVE(#3535,(#4274),.PCURVE_S1.);
#2805=IFCSURFACECURVE(#3536,(#4275),.PCURVE_S1.);
#2806=IFCSURFACECURVE(#3537,(#4276),.PCURVE_S1.);
#2807=IFCSURFACECURVE(#3538,(#4277),.PCURVE_S1.);
#2808=IFCSURFACECURVE(#3539,(#4278),.PCURVE_S1.);
#2809=IFCSURFACECURVE(#3540,(#4279),.PCURVE_S1.);
#2810=IFCSURFACECURVE(#3541,(#4280),.PCURVE_S1.);
#2811=IFCSURFACECURVE(#3542,(#4281),.PCURVE_S1.);
#2812=IFCSURFACECURVE(#3543,(#4282),.PCURVE_S1.);
#2813=IFCSURFACECURVE(#3544,(#4283),.PCURVE_S1.);
#2814=IFCSURFACECURVE(#3545,(#4284),.PCURVE_S1.);
#2815=IFCSURFACECURVE(#3546,(#4285),.PCURVE_S1.);
#2816=IFCSURFACECURVE(#3547,(#4286),.PCURVE_S1.);
#2817=IFCSURFACECURVE(#3548,(#4287),.PCURVE_S1.);
#2818=IFCSURFACECURVE(#3549,(#4288),.PCURVE_S1.);
#2819=IFCSURFACECURVE(#3550,(#4289),.PCURVE_S1.);
#2820=IFCSURFACECURVE(#3551,(#4290),.PCURVE_S1.);
#2821=IFCSURFACECURVE(#3552,(#4291),.PCURVE_S1.);
#2822=IFCSURFACECURVE(#3553,(#4292),.PCURVE_S1.);
#2823=IFCSURFACECURVE(#3554,(#4293),.PCURVE_S1.);
#2824=IFCSURFACECURVE(#3555,(#4294),.PCURVE_S1.);
#2825=IFCSURFACECURVE(#3556,(#4295),.PCURVE_S1.);
#2826=IFCSURFACECURVE(#3557,(#4296),.PCURVE_S1.);
#2827=IFCSURFACECURVE(#3558,(#4297),.PCURVE_S1.);
#2828=IFCSURFACECURVE(#3559,(#4298),.PCURVE_S1.);
#2829=IFCSURFACECURVE(#3560,(#4299),.PCURVE_S1.);
#2830=IFCSURFACECURVE(#3561,(#4300),.PCURVE_S1.);
#2831=IFCSURFACECURVE(#3562,(#4301),.PCURVE_S1.);
#2832=IFCSURFACECURVE(#3563,(#4302),.PCURVE_S1.);
#2833=IFCSURFACECURVE(#3564,(#4303),.PCURVE_S1.);
#2834=IFCSURFACECURVE(#3565,(#4304),.PCURVE_S1.);
#2835=IFCSURFACECURVE(#3566,(#4305),.PCURVE_S1.);
#2836=IFCSURFACECURVE(#3567,(#4306),.PCURVE_S1.);
#2837=IFCSURFACECURVE(#3568,(#4307),.PCURVE_S1.);
#2838=IFCSURFACECURVE(#3569,(#4308),.PCURVE_S1.);
#2839=IFCSURFACECURVE(#3570,(#4309),.PCURVE_S1.);
#2840=IFCSURFACECURVE(#3571,(#4310),.PCURVE_S1.);
#2841=IFCSURFACECURVE(#3572,(#4311),.PCURVE_S1.);
#2842=IFCSURFACECURVE(#3573,(#4312),.PCURVE_S1.);
#2843=IFCSURFACECURVE(#3574,(#4313),.PCURVE_S1.);
#2844=IFCSURFACECURVE(#3575,(#4314),.PCURVE_S1.);
#2845=IFCSURFACECURVE(#3576,(#4315),.PCURVE_S1.);
#2846=IFCSURFACECURVE(#3577,(#4316),.PCURVE_S1.);
#2847=IFCSURFACECURVE(#3578,(#4317),.PCURVE_S1.);
#2848=IFCSURFACECURVE(#3579,(#4318),.PCURVE_S1.);
#2849=IFCSURFACECURVE(#3580,(#4319),.PCURVE_S1.);
#2850=IFCSURFACECURVE(#3581,(#4320),.PCURVE_S1.);
#2851=IFCSURFACECURVE(#3582,(#4321),.PCURVE_S1.);
#2852=IFCSURFACECURVE(#3583,(#4322),.PCURVE_S1.);
#2853=IFCSURFACECURVE(#3584,(#4323),.PCURVE_S1.);
#2854=IFCSURFACECURVE(#3585,(#4324),.PCURVE_S1.);
#2855=IFCSURFACECURVE(#3586,(#4325),.PCURVE_S1.);
#2856=IFCSURFACECURVE(#3587,(#4326),.PCURVE_S1.);
#2857=IFCSURFACECURVE(#3588,(#4327),.PCURVE_S1.);
#2858=IFCSURFACECURVE(#3589,(#4328),.PCURVE_S1.);
#2859=IFCSURFACECURVE(#3590,(#4329),.PCURVE_S1.);
#2860=IFCSURFACECURVE(#3591,(#4330),.PCURVE_S1.);
#2861=IFCSURFACECURVE(#3592,(#4331),.PCURVE_S1.);
#2862=IFCSURFACECURVE(#3593,(#4332),.PCURVE_S1.);
#2863=IFCSURFACECURVE(#3594,(#4333),.PCURVE_S1.);
#2864=IFCSURFACECURVE(#3595,(#4334),.PCURVE_S1.);
#2865=IFCSURFACECURVE(#3596,(#4335),.PCURVE_S1.);
#2866=IFCSURFACECURVE(#3597,(#4336),.PCURVE_S1.);
#2867=IFCSURFACECURVE(#3598,(#4337),.PCURVE_S1.);
#2868=IFCSURFACECURVE(#3599,(#4338),.PCURVE_S1.);
#2869=IFCSURFACECURVE(#3600,(#4339),.PCURVE_S1.);
#2870=IFCSURFACECURVE(#3601,(#4340),.PCURVE_S1.);
#2871=IFCSURFACECURVE(#3602,(#4341),.PCURVE_S1.);
#2872=IFCSURFACECURVE(#3603,(#4342),.PCURVE_S1.);
#2873=IFCSURFACECURVE(#3604,(#4343),.PCURVE_S1.);
#2874=IFCSURFACECURVE(#3605,(#4344),.PCURVE_S1.);
#2875=IFCSURFACECURVE(#3606,(#4345),.PCURVE_S1.);
#2876=IFCSURFACECURVE(#3607,(#4346),.PCURVE_S1.);
#2877=IFCSURFACECURVE(#3608,(#4347),.PCURVE_S1.);
#2878=IFCSURFACECURVE(#3609,(#4348),.PCURVE_S1.);
#2879=IFCSURFACECURVE(#3610,(#4349),.PCURVE_S1.);
#2880=IFCSURFACECURVE(#3611,(#4350),.PCURVE_S1.);
#2881=IFCSURFACECURVE(#3612,(#4351),.PCURVE_S1.);
#2882=IFCSURFACECURVE(#3613,(#4352),.PCURVE_S1.);
#2883=IFCSURFACECURVE(#3614,(#4353),.PCURVE_S1.);
#2884=IFCSURFACECURVE(#3615,(#4354),.PCURVE_S1.);
#2885=IFCSURFACECURVE(#3616,(#4355),.PCURVE_S1.);
#2886=IFCSURFACECURVE(#3617,(#4356),.PCURVE_S1.);
#2887=IFCSURFACECURVE(#3618,(#4357),.PCURVE_S1.);
#2888=IFCSURFACECURVE(#3619,(#4358),.PCURVE_S1.);
#2889=IFCSURFACECURVE(#3620,(#4359),.PCURVE_S1.);
#2890=IFCSURFACECURVE(#3621,(#4360),.PCURVE_S1.);
#2891=IFCSURFACECURVE(#3622,(#4361),.PCURVE_S1.);
#2892=IFCSURFACECURVE(#3623,(#4362),.PCURVE_S1.);
#2893=IFCSURFACECURVE(#3624,(#4363),.PCURVE_S1.);
#2894=IFCSURFACECURVE(#3625,(#4364),.PCURVE_S1.);
#2895=IFCSURFACECURVE(#3626,(#4365),.PCURVE_S1.);
#2896=IFCSURFACECURVE(#3627,(#4366),.PCURVE_S1.);
#2897=IFCSURFACECURVE(#3628,(#4367),.PCURVE_S1.);
#2898=IFCSURFACECURVE(#3629,(#4368),.PCURVE_S1.);
#2899=IFCSURFACECURVE(#3630,(#4369),.PCURVE_S1.);
#2900=IFCSURFACECURVE(#3631,(#4370),.PCURVE_S1.);
#2901=IFCSURFACECURVE(#3632,(#4371),.PCURVE_S1.);
#2902=IFCSURFACECURVE(#3633,(#4372),.PCURVE_S1.);
#2903=IFCSURFACECURVE(#3634,(#4373),.PCURVE_S1.);
#2904=IFCSURFACECURVE(#3635,(#4374),.PCURVE_S1.);
#2905=IFCSURFACECURVE(#3636,(#4375),.PCURVE_S1.);
#2906=IFCSURFACECURVE(#3637,(#4376),.PCURVE_S1.);
#2907=IFCSURFACECURVE(#3638,(#4377),.PCURVE_S1.);
#2908=IFCSURFACECURVE(#3639,(#4378),.PCURVE_S1.);
#2909=IFCSURFACECURVE(#3640,(#4379),.PCURVE_S1.);
#2910=IFCSURFACECURVE(#3641,(#4380),.PCURVE_S1.);
#2911=IFCSURFACECURVE(#3642,(#4381),.PCURVE_S1.);
#2912=IFCSURFACECURVE(#3643,(#4382),.PCURVE_S1.);
#2913=IFCSURFACECURVE(#3644,(#4383),.PCURVE_S1.);
#2914=IFCSURFACECURVE(#3645,(#4384),.PCURVE_S1.);
#2915=IFCSURFACECURVE(#3646,(#4385),.PCURVE_S1.);
#2916=IFCSURFACECURVE(#3647,(#4386),.PCURVE_S1.);
#2917=IFCSURFACECURVE(#3648,(#4387),.PCURVE_S1.);
#2918=IFCSURFACECURVE(#3649,(#4388),.PCURVE_S1.);
#2919=IFCSURFACECURVE(#3650,(#4389),.PCURVE_S1.);
#2920=IFCSURFACECURVE(#3651,(#4390),.PCURVE_S1.);
#2921=IFCSURFACECURVE(#3652,(#4391),.PCURVE_S1.);
#2922=IFCSURFACECURVE(#3653,(#4392),.PCURVE_S1.);
#2923=IFCSURFACECURVE(#3654,(#4393),.PCURVE_S1.);
#2924=IFCSURFACECURVE(#3655,(#4394),.PCURVE_S1.);
#2925=IFCSURFACECURVE(#3656,(#4395),.PCURVE_S1.);
#2926=IFCSURFACECURVE(#3657,(#4396),.PCURVE_S1.);
#2927=IFCSURFACECURVE(#3658,(#4397),.PCURVE_S1.);
#2928=IFCSURFACECURVE(#3659,(#4398),.PCURVE_S1.);
#2929=IFCSURFACECURVE(#3660,(#4399),.PCURVE_S1.);
#2930=IFCSURFACECURVE(#3661,(#4400),.PCURVE_S1.);
#2931=IFCSURFACECURVE(#3662,(#4401),.PCURVE_S1.);
#2932=IFCSURFACECURVE(#3663,(#4402),.PCURVE_S1.);
#2933=IFCSURFACECURVE(#3664,(#4403),.PCURVE_S1.);
#2934=IFCSURFACECURVE(#3665,(#4404),.PCURVE_S1.);
#2935=IFCSURFACECURVE(#3666,(#4405),.PCURVE_S1.);
#2936=IFCSURFACECURVE(#3667,(#4406),.PCURVE_S1.);
#2937=IFCSURFACECURVE(#3668,(#4407),.PCURVE_S1.);
#2938=IFCSURFACECURVE(#3669,(#4408),.PCURVE_S1.);
#2939=IFCSURFACECURVE(#3670,(#4409),.PCURVE_S1.);
#2940=IFCSURFACECURVE(#3671,(#4410),.PCURVE_S1.);
#2941=IFCSURFACECURVE(#3672,(#4411),.PCURVE_S1.);
#2942=IFCSURFACECURVE(#3673,(#4412),.PCURVE_S1.);
#2943=IFCSURFACECURVE(#3674,(#4413),.PCURVE_S1.);
#2944=IFCSURFACECURVE(#3675,(#4414),.PCURVE_S1.);
#2945=IFCSURFACECURVE(#3676,(#4415),.PCURVE_S1.);
#2946=IFCSURFACECURVE(#3677,(#4416),.PCURVE_S1.);
#2947=IFCSURFACECURVE(#3678,(#4417),.PCURVE_S1.);
#2948=IFCSURFACECURVE(#3679,(#4418),.PCURVE_S1.);
#2949=IFCSURFACECURVE(#3680,(#4419),.PCURVE_S1.);
#2950=IFCSURFACECURVE(#3681,(#4420),.PCURVE_S1.);
#2951=IFCSURFACECURVE(#3682,(#4421),.PCURVE_S1.);
#2952=IFCSURFACECURVE(#3683,(#4422),.PCURVE_S1.);
#2953=IFCSURFACECURVE(#3684,(#4423),.PCURVE_S1.);
#2954=IFCSURFACECURVE(#3685,(#4424),.PCURVE_S1.);
#2955=IFCSURFACECURVE(#3686,(#4425),.PCURVE_S1.);
#2956=IFCSURFACECURVE(#3687,(#4426),.PCURVE_S1.);
#2957=IFCSURFACECURVE(#3688,(#4427),.PCURVE_S1.);
#2958=IFCSURFACECURVE(#3689,(#4428),.PCURVE_S1.);
#2959=IFCSURFACECURVE(#3690,(#4429),.PCURVE_S1.);
#2960=IFCSURFACECURVE(#3691,(#4430),.PCURVE_S1.);
#2961=IFCSURFACECURVE(#3692,(#4431),.PCURVE_S1.);
#2962=IFCSURFACECURVE(#3693,(#4432),.PCURVE_S1.);
#2963=IFCSURFACECURVE(#3694,(#4433),.PCURVE_S1.);
#2964=IFCSURFACECURVE(#3695,(#4434),.PCURVE_S1.);
#2965=IFCSURFACECURVE(#3696,(#4435),.PCURVE_S1.);
#2966=IFCSURFACECURVE(#3697,(#4436),.PCURVE_S1.);
#2967=IFCSURFACECURVE(#3698,(#4437),.PCURVE_S1.);
#2968=IFCSURFACECURVE(#3699,(#4438),.PCURVE_S1.);
#2969=IFCSURFACECURVE(#3700,(#4439),.PCURVE_S1.);
#2970=IFCSURFACECURVE(#3701,(#4440),.PCURVE_S1.);
#2971=IFCSURFACECURVE(#3702,(#4441),.PCURVE_S1.);
#2972=IFCSURFACECURVE(#3703,(#4442),.PCURVE_S1.);
#2973=IFCSURFACECURVE(#3704,(#4443),.PCURVE_S1.);
#2974=IFCSURFACECURVE(#3705,(#4444),.PCURVE_S1.);
#2975=IFCSURFACECURVE(#3706,(#4445),.PCURVE_S1.);
#2976=IFCSURFACECURVE(#3707,(#4446),.PCURVE_S1.);
#2977=IFCSURFACECURVE(#3708,(#4447),.PCURVE_S1.);
#2978=IFCSURFACECURVE(#3709,(#4448),.PCURVE_S1.);
#2979=IFCSURFACECURVE(#3710,(#4449),.PCURVE_S1.);
#2980=IFCSURFACECURVE(#3711,(#4450),.PCURVE_S1.);
#2981=IFCSURFACECURVE(#3712,(#4451),.PCURVE_S1.);
#2982=IFCSURFACECURVE(#3713,(#4452),.PCURVE_S1.);
#2983=IFCSURFACECURVE(#3714,(#4453),.PCURVE_S1.);
#2984=IFCSURFACECURVE(#3715,(#4454),.PCURVE_S1.);
#2985=IFCSURFACECURVE(#3716,(#4455),.PCURVE_S1.);
#2986=IFCSURFACECURVE(#3717,(#4456),.PCURVE_S1.);
#2987=IFCSURFACECURVE(#3718,(#4457),.PCURVE_S1.);
#2988=IFCSURFACECURVE(#3719,(#4458),.PCURVE_S1.);
#2989=IFCSURFACECURVE(#3720,(#4459),.PCURVE_S1.);
#2990=IFCSURFACECURVE(#3721,(#4460),.PCURVE_S1.);
#2991=IFCSURFACECURVE(#3722,(#4461),.PCURVE_S1.);
#2992=IFCSURFACECURVE(#3723,(#4462),.PCURVE_S1.);
#2993=IFCSURFACECURVE(#3724,(#4463),.PCURVE_S1.);
#2994=IFCSURFACECURVE(#3725,(#4464),.PCURVE_S1.);
#2995=IFCSURFACECURVE(#3726,(#4465),.PCURVE_S1.);
#2996=IFCSURFACECURVE(#3727,(#4466),.PCURVE_S1.);
#2997=IFCSURFACECURVE(#3728,(#4467),.PCURVE_S1.);
#2998=IFCSURFACECURVE(#3729,(#4468),.PCURVE_S1.);
#2999=IFCSURFACECURVE(#3730,(#4469),.PCURVE_S1.);
#3000=IFCSURFACECURVE(#3731,(#4470),.PCURVE_S1.);
#3001=IFCSURFACECURVE(#3732,(#4471),.PCURVE_S1.);
#3002=IFCSURFACECURVE(#3733,(#4472),.PCURVE_S1.);
#3003=IFCSURFACECURVE(#3734,(#4473),.PCURVE_S1.);
#3004=IFCSURFACECURVE(#3735,(#4474),.PCURVE_S1.);
#3005=IFCSURFACECURVE(#3736,(#4475),.PCURVE_S1.);
#3006=IFCSURFACECURVE(#3737,(#4476),.PCURVE_S1.);
#3007=IFCSURFACECURVE(#3738,(#4477),.PCURVE_S1.);
#3008=IFCSURFACECURVE(#3739,(#4478),.PCURVE_S1.);
#3009=IFCSURFACECURVE(#3740,(#4479),.PCURVE_S1.);
#3010=IFCSURFACECURVE(#3741,(#4480),.PCURVE_S1.);
#3011=IFCSURFACECURVE(#3742,(#4481),.PCURVE_S1.);
#3012=IFCSURFACECURVE(#3743,(#4482),.PCURVE_S1.);
#3013=IFCSURFACECURVE(#3744,(#4483),.PCURVE_S1.);
#3014=IFCSURFACECURVE(#3745,(#4484),.PCURVE_S1.);
#3015=IFCSURFACECURVE(#3746,(#4485),.PCURVE_S1.);
#3016=IFCSURFACECURVE(#3747,(#4486),.PCURVE_S1.);
#3017=IFCSURFACECURVE(#3748,(#4487),.PCURVE_S1.);
#3018=IFCSURFACECURVE(#3749,(#4488),.PCURVE_S1.);
#3019=IFCSURFACECURVE(#3750,(#4489),.PCURVE_S1.);
#3020=IFCSURFACECURVE(#3751,(#4490),.PCURVE_S1.);
#3021=IFCSURFACECURVE(#3752,(#4491),.PCURVE_S1.);
#3022=IFCSURFACECURVE(#3753,(#4492),.PCURVE_S1.);
#3023=IFCSURFACECURVE(#3754,(#4493),.PCURVE_S1.);
#3024=IFCSURFACECURVE(#3755,(#4494),.PCURVE_S1.);
#3025=IFCSURFACECURVE(#3756,(#4495),.PCURVE_S1.);
#3026=IFCSURFACECURVE(#3757,(#4496),.PCURVE_S1.);
#3027=IFCSURFACECURVE(#3758,(#4497),.PCURVE_S1.);
#3028=IFCSURFACECURVE(#3759,(#4498),.PCURVE_S1.);
#3029=IFCSURFACECURVE(#3760,(#4499),.PCURVE_S1.);
#3030=IFCSURFACECURVE(#3761,(#4500),.PCURVE_S1.);
#3031=IFCSURFACECURVE(#3762,(#4501),.PCURVE_S1.);
#3032=IFCSURFACECURVE(#3763,(#4502),.PCURVE_S1.);
#3033=IFCSURFACECURVE(#3764,(#4503),.PCURVE_S1.);
#3034=IFCSURFACECURVE(#3765,(#4504),.PCURVE_S1.);
#3035=IFCSURFACECURVE(#3766,(#4505),.PCURVE_S1.);
#3036=IFCSURFACECURVE(#3767,(#4506),.PCURVE_S1.);
#3037=IFCSURFACECURVE(#3768,(#4507),.PCURVE_S1.);
#3038=IFCSURFACECURVE(#3769,(#4508),.PCURVE_S1.);
#3039=IFCSURFACECURVE(#3770,(#4509),.PCURVE_S1.);
#3040=IFCSURFACECURVE(#3771,(#4510),.PCURVE_S1.);
#3041=IFCSURFACECURVE(#3772,(#4511),.PCURVE_S1.);
#3042=IFCSURFACECURVE(#3773,(#4512),.PCURVE_S1.);
#3043=IFCSURFACECURVE(#3774,(#4513),.PCURVE_S1.);
#3044=IFCSURFACECURVE(#3775,(#4514),.PCURVE_S1.);
#3045=IFCSURFACECURVE(#3776,(#4515),.PCURVE_S1.);
#3046=IFCSURFACECURVE(#3777,(#4516),.PCURVE_S1.);
#3047=IFCSURFACECURVE(#3778,(#4517),.PCURVE_S1.);
#3048=IFCSURFACECURVE(#3779,(#4518),.PCURVE_S1.);
#3049=IFCSURFACECURVE(#3780,(#4519),.PCURVE_S1.);
#3050=IFCSURFACECURVE(#3781,(#4520),.PCURVE_S1.);
#3051=IFCSURFACECURVE(#3782,(#4521),.PCURVE_S1.);
#3052=IFCSURFACECURVE(#3783,(#4522),.PCURVE_S1.);
#3053=IFCSURFACECURVE(#3784,(#4523),.PCURVE_S1.);
#3054=IFCSURFACECURVE(#3785,(#4524),.PCURVE_S1.);
#3055=IFCSURFACECURVE(#3786,(#4525),.PCURVE_S1.);
#3056=IFCSURFACECURVE(#3787,(#4526),.PCURVE_S1.);
#3057=IFCSURFACECURVE(#3788,(#4527),.PCURVE_S1.);
#3058=IFCSURFACECURVE(#3789,(#4528),.PCURVE_S1.);
#3059=IFCSURFACECURVE(#3790,(#4529),.PCURVE_S1.);
#3060=IFCSURFACECURVE(#3791,(#4530),.PCURVE_S1.);
#3061=IFCSURFACECURVE(#3792,(#4531),.PCURVE_S1.);
#3062=IFCSURFACECURVE(#3793,(#4532),.PCURVE_S1.);
#3063=IFCSURFACECURVE(#3794,(#4533),.PCURVE_S1.);
#3064=IFCSURFACECURVE(#3795,(#4534),.PCURVE_S1.);
#3065=IFCSURFACECURVE(#3796,(#4535),.PCURVE_S1.);
#3066=IFCSURFACECURVE(#3797,(#4536),.PCURVE_S1.);
#3067=IFCSURFACECURVE(#3798,(#4537),.PCURVE_S1.);
#3068=IFCSURFACECURVE(#3799,(#4538),.PCURVE_S1.);
#3069=IFCSURFACECURVE(#3800,(#4539),.PCURVE_S1.);
#3070=IFCSURFACECURVE(#3801,(#4540),.PCURVE_S1.);
#3071=IFCSURFACECURVE(#3802,(#4541),.PCURVE_S1.);
#3072=IFCSURFACECURVE(#3803,(#4542),.PCURVE_S1.);
#3073=IFCSURFACECURVE(#3804,(#4543),.PCURVE_S1.);
#3074=IFCSURFACECURVE(#3805,(#4544),.PCURVE_S1.);
#3075=IFCSURFACECURVE(#3806,(#4545),.PCURVE_S1.);
#3076=IFCSURFACECURVE(#3807,(#4546),.PCURVE_S1.);
#3077=IFCSURFACECURVE(#3808,(#4547),.PCURVE_S1.);
#3078=IFCSURFACECURVE(#3809,(#4548),.PCURVE_S1.);
#3079=IFCSURFACECURVE(#3810,(#4549),.PCURVE_S1.);
#3080=IFCSURFACECURVE(#3811,(#4550),.PCURVE_S1.);
#3081=IFCSURFACECURVE(#3812,(#4551),.PCURVE_S1.);
#3082=IFCSURFACECURVE(#3813,(#4552),.PCURVE_S1.);
#3083=IFCSURFACECURVE(#3814,(#4553),.PCURVE_S1.);
#3084=IFCSURFACECURVE(#3815,(#4554),.PCURVE_S1.);
#3085=IFCSURFACECURVE(#3816,(#4555),.PCURVE_S1.);
#3086=IFCSURFACECURVE(#3817,(#4556),.PCURVE_S1.);
#3087=IFCSURFACECURVE(#3818,(#4557),.PCURVE_S1.);
#3088=IFCSURFACECURVE(#3819,(#4558),.PCURVE_S1.);
#3089=IFCSURFACECURVE(#3820,(#4559),.PCURVE_S1.);
#3090=IFCSURFACECURVE(#3821,(#4560),.PCURVE_S1.);
#3091=IFCSURFACECURVE(#3822,(#4561),.PCURVE_S1.);
#3092=IFCSURFACECURVE(#3823,(#4562),.PCURVE_S1.);
#3093=IFCSURFACECURVE(#3824,(#4563),.PCURVE_S1.);
#3094=IFCSURFACECURVE(#3825,(#4564),.PCURVE_S1.);
#3095=IFCSURFACECURVE(#3826,(#4565),.PCURVE_S1.);
#3096=IFCSURFACECURVE(#43,(#4566),.PCURVE_S1.);
#3097=IFCSURFACECURVE(#44,(#4567),.PCURVE_S1.);
#3098=IFCSURFACECURVE(#45,(#4568),.PCURVE_S1.);
#3099=IFCSURFACECURVE(#46,(#4569),.PCURVE_S1.);
#3100=IFCBSPLINECURVEWITHKNOTS(1,(#5376,#5377),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3101=IFCBSPLINECURVEWITHKNOTS(1,(#5377,#5379),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3102=IFCBSPLINECURVEWITHKNOTS(1,(#5379,#5381),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3103=IFCBSPLINECURVEWITHKNOTS(1,(#5381,#5383),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3104=IFCBSPLINECURVEWITHKNOTS(1,(#5383,#5385),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3105=IFCBSPLINECURVEWITHKNOTS(1,(#5385,#5387),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3106=IFCBSPLINECURVEWITHKNOTS(1,(#5387,#5389),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3107=IFCBSPLINECURVEWITHKNOTS(1,(#5389,#5391),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3108=IFCBSPLINECURVEWITHKNOTS(1,(#5391,#5393),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3109=IFCBSPLINECURVEWITHKNOTS(1,(#5393,#5395),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3110=IFCBSPLINECURVEWITHKNOTS(1,(#5395,#5397),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3111=IFCBSPLINECURVEWITHKNOTS(1,(#5397,#5399),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3112=IFCBSPLINECURVEWITHKNOTS(1,(#5399,#5401),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3113=IFCBSPLINECURVEWITHKNOTS(1,(#5401,#5403),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3114=IFCBSPLINECURVEWITHKNOTS(1,(#5403,#5405),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3115=IFCBSPLINECURVEWITHKNOTS(1,(#5405,#5407),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3116=IFCBSPLINECURVEWITHKNOTS(1,(#5407,#5409),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3117=IFCBSPLINECURVEWITHKNOTS(1,(#5409,#5411),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3118=IFCBSPLINECURVEWITHKNOTS(1,(#5411,#5413),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3119=IFCBSPLINECURVEWITHKNOTS(1,(#5413,#5415),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3120=IFCBSPLINECURVEWITHKNOTS(1,(#5415,#5417),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3121=IFCBSPLINECURVEWITHKNOTS(1,(#5417,#5419),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3122=IFCBSPLINECURVEWITHKNOTS(1,(#5419,#5421),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3123=IFCBSPLINECURVEWITHKNOTS(1,(#5421,#5376),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3124=IFCBSPLINECURVEWITHKNOTS(3,(#5425,#5429,#5433,#5437),
 .UNSPECIFIED.,.F.,.U.,(4,4),(0.,1.),.UNSPECIFIED.);
#3125=IFCBSPLINECURVEWITHKNOTS(3,(#5437,#5438,#5439,#5440),
 .UNSPECIFIED.,.F.,.U.,(4,4),(0.,1.),.UNSPECIFIED.);
#3126=IFCBSPLINECURVEWITHKNOTS(3,(#5440,#5436,#5432,#5428),
 .UNSPECIFIED.,.F.,.U.,(4,4),(0.,1.),.UNSPECIFIED.);
#3127=IFCBSPLINECURVEWITHKNOTS(3,(#5428,#5427,#5426,#5425),
 .UNSPECIFIED.,.F.,.U.,(4,4),(0.,1.),.UNSPECIFIED.);
#3128=IFCBSPLINECURVEWITHKNOTS(1,(#5776,#5788),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3129=IFCBSPLINECURVEWITHKNOTS(1,(#5788,#5790),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3130=IFCBSPLINECURVEWITHKNOTS(1,(#5790,#5791),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3131=IFCBSPLINECURVEWITHKNOTS(1,(#5791,#5793),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3132=IFCBSPLINECURVEWITHKNOTS(1,(#5793,#5794),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3133=IFCBSPLINECURVEWITHKNOTS(1,(#5794,#5796),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3134=IFCBSPLINECURVEWITHKNOTS(1,(#5796,#5797),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3135=IFCBSPLINECURVEWITHKNOTS(1,(#5797,#5799),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3136=IFCBSPLINECURVEWITHKNOTS(1,(#5799,#5800),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3137=IFCBSPLINECURVEWITHKNOTS(1,(#5800,#5802),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3138=IFCBSPLINECURVEWITHKNOTS(1,(#5802,#5803),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3139=IFCBSPLINECURVEWITHKNOTS(1,(#5803,#5805),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3140=IFCBSPLINECURVEWITHKNOTS(1,(#5805,#5806),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3141=IFCBSPLINECURVEWITHKNOTS(1,(#5806,#5808),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3142=IFCBSPLINECURVEWITHKNOTS(1,(#5808,#5809),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3143=IFCBSPLINECURVEWITHKNOTS(1,(#5809,#5811),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3144=IFCBSPLINECURVEWITHKNOTS(1,(#5811,#5812),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3145=IFCBSPLINECURVEWITHKNOTS(1,(#5812,#5814),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3146=IFCBSPLINECURVEWITHKNOTS(1,(#5814,#5815),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3147=IFCBSPLINECURVEWITHKNOTS(1,(#5815,#5817),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3148=IFCBSPLINECURVEWITHKNOTS(1,(#5817,#5818),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3149=IFCBSPLINECURVEWITHKNOTS(1,(#5818,#5820),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3150=IFCBSPLINECURVEWITHKNOTS(1,(#5820,#5821),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3151=IFCBSPLINECURVEWITHKNOTS(1,(#5821,#5823),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3152=IFCBSPLINECURVEWITHKNOTS(1,(#5823,#5825),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3153=IFCBSPLINECURVEWITHKNOTS(1,(#5825,#5826),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3154=IFCBSPLINECURVEWITHKNOTS(1,(#5826,#5828),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3155=IFCBSPLINECURVEWITHKNOTS(1,(#5828,#5829),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3156=IFCBSPLINECURVEWITHKNOTS(1,(#5829,#5831),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3157=IFCBSPLINECURVEWITHKNOTS(1,(#5831,#5832),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3158=IFCBSPLINECURVEWITHKNOTS(1,(#5832,#5834),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3159=IFCBSPLINECURVEWITHKNOTS(1,(#5834,#5835),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3160=IFCBSPLINECURVEWITHKNOTS(1,(#5835,#5837),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3161=IFCBSPLINECURVEWITHKNOTS(1,(#5837,#5838),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3162=IFCBSPLINECURVEWITHKNOTS(1,(#5838,#5840),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3163=IFCBSPLINECURVEWITHKNOTS(1,(#5840,#5841),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3164=IFCBSPLINECURVEWITHKNOTS(1,(#5841,#5843),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3165=IFCBSPLINECURVEWITHKNOTS(1,(#5843,#5844),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3166=IFCBSPLINECURVEWITHKNOTS(1,(#5844,#5846),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3167=IFCBSPLINECURVEWITHKNOTS(1,(#5846,#5847),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3168=IFCBSPLINECURVEWITHKNOTS(1,(#5847,#5849),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3169=IFCBSPLINECURVEWITHKNOTS(1,(#5849,#5850),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3170=IFCBSPLINECURVEWITHKNOTS(1,(#5850,#5852),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3171=IFCBSPLINECURVEWITHKNOTS(1,(#5852,#5853),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3172=IFCBSPLINECURVEWITHKNOTS(1,(#5853,#5854),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3173=IFCBSPLINECURVEWITHKNOTS(1,(#5854,#5855),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3174=IFCBSPLINECURVEWITHKNOTS(1,(#5855,#5856),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3175=IFCBSPLINECURVEWITHKNOTS(1,(#5856,#5857),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3176=IFCBSPLINECURVEWITHKNOTS(1,(#5857,#5859),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3177=IFCBSPLINECURVEWITHKNOTS(1,(#5859,#5860),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3178=IFCBSPLINECURVEWITHKNOTS(1,(#5860,#5862),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3179=IFCBSPLINECURVEWITHKNOTS(1,(#5862,#5863),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3180=IFCBSPLINECURVEWITHKNOTS(1,(#5863,#5865),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3181=IFCBSPLINECURVEWITHKNOTS(1,(#5865,#5866),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3182=IFCBSPLINECURVEWITHKNOTS(1,(#5866,#5868),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3183=IFCBSPLINECURVEWITHKNOTS(1,(#5868,#5869),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3184=IFCBSPLINECURVEWITHKNOTS(1,(#5869,#5871),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3185=IFCBSPLINECURVEWITHKNOTS(1,(#5871,#5872),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3186=IFCBSPLINECURVEWITHKNOTS(1,(#5872,#5874),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3187=IFCBSPLINECURVEWITHKNOTS(1,(#5874,#5875),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3188=IFCBSPLINECURVEWITHKNOTS(1,(#5875,#5877),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3189=IFCBSPLINECURVEWITHKNOTS(1,(#5877,#5878),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3190=IFCBSPLINECURVEWITHKNOTS(1,(#5878,#5880),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3191=IFCBSPLINECURVEWITHKNOTS(1,(#5880,#5881),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3192=IFCBSPLINECURVEWITHKNOTS(1,(#5881,#5883),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3193=IFCBSPLINECURVEWITHKNOTS(1,(#5883,#5884),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3194=IFCBSPLINECURVEWITHKNOTS(1,(#5884,#5886),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3195=IFCBSPLINECURVEWITHKNOTS(1,(#5886,#5887),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3196=IFCBSPLINECURVEWITHKNOTS(1,(#5887,#5888),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3197=IFCBSPLINECURVEWITHKNOTS(1,(#5888,#5889),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3198=IFCBSPLINECURVEWITHKNOTS(1,(#5889,#5890),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3199=IFCBSPLINECURVEWITHKNOTS(1,(#5890,#5892),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3200=IFCBSPLINECURVEWITHKNOTS(1,(#5892,#5893),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3201=IFCBSPLINECURVEWITHKNOTS(1,(#5893,#5895),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3202=IFCBSPLINECURVEWITHKNOTS(1,(#5895,#5896),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3203=IFCBSPLINECURVEWITHKNOTS(1,(#5896,#5898),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3204=IFCBSPLINECURVEWITHKNOTS(1,(#5898,#5899),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3205=IFCBSPLINECURVEWITHKNOTS(1,(#5899,#5901),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3206=IFCBSPLINECURVEWITHKNOTS(1,(#5901,#5902),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3207=IFCBSPLINECURVEWITHKNOTS(1,(#5902,#5904),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3208=IFCBSPLINECURVEWITHKNOTS(1,(#5904,#5905),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3209=IFCBSPLINECURVEWITHKNOTS(1,(#5905,#5907),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3210=IFCBSPLINECURVEWITHKNOTS(1,(#5907,#5908),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3211=IFCBSPLINECURVEWITHKNOTS(1,(#5908,#5910),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3212=IFCBSPLINECURVEWITHKNOTS(1,(#5910,#5911),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3213=IFCBSPLINECURVEWITHKNOTS(1,(#5911,#5913),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3214=IFCBSPLINECURVEWITHKNOTS(1,(#5913,#5914),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3215=IFCBSPLINECURVEWITHKNOTS(1,(#5914,#5916),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3216=IFCBSPLINECURVEWITHKNOTS(1,(#5916,#5917),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3217=IFCBSPLINECURVEWITHKNOTS(1,(#5917,#5919),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3218=IFCBSPLINECURVEWITHKNOTS(1,(#5919,#5920),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3219=IFCBSPLINECURVEWITHKNOTS(1,(#5920,#5921),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3220=IFCBSPLINECURVEWITHKNOTS(1,(#5921,#5922),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3221=IFCBSPLINECURVEWITHKNOTS(1,(#5922,#5923),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3222=IFCBSPLINECURVEWITHKNOTS(1,(#5923,#5925),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3223=IFCBSPLINECURVEWITHKNOTS(1,(#5925,#5926),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3224=IFCBSPLINECURVEWITHKNOTS(1,(#5926,#5928),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3225=IFCBSPLINECURVEWITHKNOTS(1,(#5928,#5929),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3226=IFCBSPLINECURVEWITHKNOTS(1,(#5929,#5931),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3227=IFCBSPLINECURVEWITHKNOTS(1,(#5931,#5932),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3228=IFCBSPLINECURVEWITHKNOTS(1,(#5932,#5934),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3229=IFCBSPLINECURVEWITHKNOTS(1,(#5934,#5935),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3230=IFCBSPLINECURVEWITHKNOTS(1,(#5935,#5937),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3231=IFCBSPLINECURVEWITHKNOTS(1,(#5937,#5938),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3232=IFCBSPLINECURVEWITHKNOTS(1,(#5938,#5940),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3233=IFCBSPLINECURVEWITHKNOTS(1,(#5940,#5941),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3234=IFCBSPLINECURVEWITHKNOTS(1,(#5941,#5943),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3235=IFCBSPLINECURVEWITHKNOTS(1,(#5943,#5944),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3236=IFCBSPLINECURVEWITHKNOTS(1,(#5944,#5946),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3237=IFCBSPLINECURVEWITHKNOTS(1,(#5946,#5947),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3238=IFCBSPLINECURVEWITHKNOTS(1,(#5947,#5949),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3239=IFCBSPLINECURVEWITHKNOTS(1,(#5949,#5950),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3240=IFCBSPLINECURVEWITHKNOTS(1,(#5950,#5952),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3241=IFCBSPLINECURVEWITHKNOTS(1,(#5952,#5953),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3242=IFCBSPLINECURVEWITHKNOTS(1,(#5953,#5954),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3243=IFCBSPLINECURVEWITHKNOTS(1,(#5954,#5955),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3244=IFCBSPLINECURVEWITHKNOTS(1,(#5955,#5956),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3245=IFCBSPLINECURVEWITHKNOTS(1,(#5956,#5957),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3246=IFCBSPLINECURVEWITHKNOTS(1,(#5957,#5959),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3247=IFCBSPLINECURVEWITHKNOTS(1,(#5959,#5960),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3248=IFCBSPLINECURVEWITHKNOTS(1,(#5960,#5962),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3249=IFCBSPLINECURVEWITHKNOTS(1,(#5962,#5963),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3250=IFCBSPLINECURVEWITHKNOTS(1,(#5963,#5965),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3251=IFCBSPLINECURVEWITHKNOTS(1,(#5965,#5966),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3252=IFCBSPLINECURVEWITHKNOTS(1,(#5966,#5968),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3253=IFCBSPLINECURVEWITHKNOTS(1,(#5968,#5969),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3254=IFCBSPLINECURVEWITHKNOTS(1,(#5969,#5971),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3255=IFCBSPLINECURVEWITHKNOTS(1,(#5971,#5972),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3256=IFCBSPLINECURVEWITHKNOTS(1,(#5972,#5974),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3257=IFCBSPLINECURVEWITHKNOTS(1,(#5974,#5975),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3258=IFCBSPLINECURVEWITHKNOTS(1,(#5975,#5977),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3259=IFCBSPLINECURVEWITHKNOTS(1,(#5977,#5978),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3260=IFCBSPLINECURVEWITHKNOTS(1,(#5978,#5980),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3261=IFCBSPLINECURVEWITHKNOTS(1,(#5980,#5981),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3262=IFCBSPLINECURVEWITHKNOTS(1,(#5981,#5983),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3263=IFCBSPLINECURVEWITHKNOTS(1,(#5983,#5984),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3264=IFCBSPLINECURVEWITHKNOTS(1,(#5984,#5986),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3265=IFCBSPLINECURVEWITHKNOTS(1,(#5986,#5987),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3266=IFCBSPLINECURVEWITHKNOTS(1,(#5987,#5988),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3267=IFCBSPLINECURVEWITHKNOTS(1,(#5988,#5989),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3268=IFCBSPLINECURVEWITHKNOTS(1,(#5989,#5990),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3269=IFCBSPLINECURVEWITHKNOTS(1,(#5990,#5992),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3270=IFCBSPLINECURVEWITHKNOTS(1,(#5992,#5993),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3271=IFCBSPLINECURVEWITHKNOTS(1,(#5993,#5995),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3272=IFCBSPLINECURVEWITHKNOTS(1,(#5995,#5996),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3273=IFCBSPLINECURVEWITHKNOTS(1,(#5996,#5998),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3274=IFCBSPLINECURVEWITHKNOTS(1,(#5998,#5999),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3275=IFCBSPLINECURVEWITHKNOTS(1,(#5999,#6001),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3276=IFCBSPLINECURVEWITHKNOTS(1,(#6001,#6002),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3277=IFCBSPLINECURVEWITHKNOTS(1,(#6002,#6004),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3278=IFCBSPLINECURVEWITHKNOTS(1,(#6004,#6005),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3279=IFCBSPLINECURVEWITHKNOTS(1,(#6005,#6007),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3280=IFCBSPLINECURVEWITHKNOTS(1,(#6007,#6008),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3281=IFCBSPLINECURVEWITHKNOTS(1,(#6008,#6010),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3282=IFCBSPLINECURVEWITHKNOTS(1,(#6010,#6011),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3283=IFCBSPLINECURVEWITHKNOTS(1,(#6011,#6013),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3284=IFCBSPLINECURVEWITHKNOTS(1,(#6013,#6014),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3285=IFCBSPLINECURVEWITHKNOTS(1,(#6014,#6016),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3286=IFCBSPLINECURVEWITHKNOTS(1,(#6016,#6017),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3287=IFCBSPLINECURVEWITHKNOTS(1,(#6017,#6019),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3288=IFCBSPLINECURVEWITHKNOTS(1,(#6019,#6020),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3289=IFCBSPLINECURVEWITHKNOTS(1,(#6020,#6022),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3290=IFCBSPLINECURVEWITHKNOTS(1,(#6022,#6023),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3291=IFCBSPLINECURVEWITHKNOTS(1,(#6023,#6025),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3292=IFCBSPLINECURVEWITHKNOTS(1,(#6025,#6027),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3293=IFCBSPLINECURVEWITHKNOTS(1,(#6027,#6028),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3294=IFCBSPLINECURVEWITHKNOTS(1,(#6028,#6030),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3295=IFCBSPLINECURVEWITHKNOTS(1,(#6030,#6031),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3296=IFCBSPLINECURVEWITHKNOTS(1,(#6031,#6033),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3297=IFCBSPLINECURVEWITHKNOTS(1,(#6033,#6034),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3298=IFCBSPLINECURVEWITHKNOTS(1,(#6034,#6036),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3299=IFCBSPLINECURVEWITHKNOTS(1,(#6036,#6037),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3300=IFCBSPLINECURVEWITHKNOTS(1,(#6037,#6039),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3301=IFCBSPLINECURVEWITHKNOTS(1,(#6039,#6040),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3302=IFCBSPLINECURVEWITHKNOTS(1,(#6040,#6042),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3303=IFCBSPLINECURVEWITHKNOTS(1,(#6042,#6043),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3304=IFCBSPLINECURVEWITHKNOTS(1,(#6043,#6045),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3305=IFCBSPLINECURVEWITHKNOTS(1,(#6045,#6046),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3306=IFCBSPLINECURVEWITHKNOTS(1,(#6046,#6048),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3307=IFCBSPLINECURVEWITHKNOTS(1,(#6048,#6049),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3308=IFCBSPLINECURVEWITHKNOTS(1,(#6049,#6051),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3309=IFCBSPLINECURVEWITHKNOTS(1,(#6051,#6052),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3310=IFCBSPLINECURVEWITHKNOTS(1,(#6052,#6054),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3311=IFCBSPLINECURVEWITHKNOTS(1,(#6054,#6055),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3312=IFCBSPLINECURVEWITHKNOTS(1,(#6055,#6056),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3313=IFCBSPLINECURVEWITHKNOTS(1,(#6056,#5786),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3314=IFCBSPLINECURVEWITHKNOTS(1,(#5786,#6058),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3315=IFCBSPLINECURVEWITHKNOTS(1,(#6058,#6059),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3316=IFCBSPLINECURVEWITHKNOTS(1,(#6059,#6061),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3317=IFCBSPLINECURVEWITHKNOTS(1,(#6061,#5787),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3318=IFCBSPLINECURVEWITHKNOTS(1,(#5787,#6062),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3319=IFCBSPLINECURVEWITHKNOTS(1,(#6062,#6064),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3320=IFCBSPLINECURVEWITHKNOTS(1,(#6064,#6065),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3321=IFCBSPLINECURVEWITHKNOTS(1,(#6065,#6067),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3322=IFCBSPLINECURVEWITHKNOTS(1,(#6067,#6068),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3323=IFCBSPLINECURVEWITHKNOTS(1,(#6068,#6070),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3324=IFCBSPLINECURVEWITHKNOTS(1,(#6070,#6072),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3325=IFCBSPLINECURVEWITHKNOTS(1,(#6072,#6073),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3326=IFCBSPLINECURVEWITHKNOTS(1,(#6073,#6075),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3327=IFCBSPLINECURVEWITHKNOTS(1,(#6075,#6076),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3328=IFCBSPLINECURVEWITHKNOTS(1,(#6076,#6078),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3329=IFCBSPLINECURVEWITHKNOTS(1,(#6078,#6079),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3330=IFCBSPLINECURVEWITHKNOTS(1,(#6079,#6081),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3331=IFCBSPLINECURVEWITHKNOTS(1,(#6081,#6083),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3332=IFCBSPLINECURVEWITHKNOTS(1,(#6083,#6085),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3333=IFCBSPLINECURVEWITHKNOTS(1,(#6085,#6086),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3334=IFCBSPLINECURVEWITHKNOTS(1,(#6086,#6088),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3335=IFCBSPLINECURVEWITHKNOTS(1,(#6088,#6089),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3336=IFCBSPLINECURVEWITHKNOTS(1,(#6089,#6091),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3337=IFCBSPLINECURVEWITHKNOTS(1,(#6091,#6092),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3338=IFCBSPLINECURVEWITHKNOTS(1,(#6092,#6094),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3339=IFCBSPLINECURVEWITHKNOTS(1,(#6094,#6095),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3340=IFCBSPLINECURVEWITHKNOTS(1,(#6095,#6097),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3341=IFCBSPLINECURVEWITHKNOTS(1,(#6097,#6098),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3342=IFCBSPLINECURVEWITHKNOTS(1,(#6098,#6100),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3343=IFCBSPLINECURVEWITHKNOTS(1,(#6100,#6102),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3344=IFCBSPLINECURVEWITHKNOTS(1,(#6102,#6103),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3345=IFCBSPLINECURVEWITHKNOTS(1,(#6103,#6105),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3346=IFCBSPLINECURVEWITHKNOTS(1,(#6105,#6106),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3347=IFCBSPLINECURVEWITHKNOTS(1,(#6106,#6108),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3348=IFCBSPLINECURVEWITHKNOTS(1,(#6108,#6109),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3349=IFCBSPLINECURVEWITHKNOTS(1,(#6109,#6111),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3350=IFCBSPLINECURVEWITHKNOTS(1,(#6111,#6112),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3351=IFCBSPLINECURVEWITHKNOTS(1,(#6112,#6114),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3352=IFCBSPLINECURVEWITHKNOTS(1,(#6114,#6115),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3353=IFCBSPLINECURVEWITHKNOTS(1,(#6115,#6117),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3354=IFCBSPLINECURVEWITHKNOTS(1,(#6117,#6119),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3355=IFCBSPLINECURVEWITHKNOTS(1,(#6119,#6121),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3356=IFCBSPLINECURVEWITHKNOTS(1,(#6121,#6122),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3357=IFCBSPLINECURVEWITHKNOTS(1,(#6122,#6124),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3358=IFCBSPLINECURVEWITHKNOTS(1,(#6124,#6125),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3359=IFCBSPLINECURVEWITHKNOTS(1,(#6125,#6127),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3360=IFCBSPLINECURVEWITHKNOTS(1,(#6127,#6128),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3361=IFCBSPLINECURVEWITHKNOTS(1,(#6128,#6130),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3362=IFCBSPLINECURVEWITHKNOTS(1,(#6130,#6131),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3363=IFCBSPLINECURVEWITHKNOTS(1,(#6131,#6132),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3364=IFCBSPLINECURVEWITHKNOTS(1,(#6132,#6133),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3365=IFCBSPLINECURVEWITHKNOTS(1,(#6133,#6134),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3366=IFCBSPLINECURVEWITHKNOTS(1,(#6134,#6136),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3367=IFCBSPLINECURVEWITHKNOTS(1,(#6136,#6137),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3368=IFCBSPLINECURVEWITHKNOTS(1,(#6137,#6139),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3369=IFCBSPLINECURVEWITHKNOTS(1,(#6139,#6140),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3370=IFCBSPLINECURVEWITHKNOTS(1,(#6140,#6142),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3371=IFCBSPLINECURVEWITHKNOTS(1,(#6142,#6143),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3372=IFCBSPLINECURVEWITHKNOTS(1,(#6143,#6145),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3373=IFCBSPLINECURVEWITHKNOTS(1,(#6145,#6146),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3374=IFCBSPLINECURVEWITHKNOTS(1,(#6146,#6148),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3375=IFCBSPLINECURVEWITHKNOTS(1,(#6148,#6149),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3376=IFCBSPLINECURVEWITHKNOTS(1,(#6149,#6151),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3377=IFCBSPLINECURVEWITHKNOTS(1,(#6151,#6152),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3378=IFCBSPLINECURVEWITHKNOTS(1,(#6152,#6154),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3379=IFCBSPLINECURVEWITHKNOTS(1,(#6154,#6155),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3380=IFCBSPLINECURVEWITHKNOTS(1,(#6155,#6157),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3381=IFCBSPLINECURVEWITHKNOTS(1,(#6157,#6158),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3382=IFCBSPLINECURVEWITHKNOTS(1,(#6158,#6160),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3383=IFCBSPLINECURVEWITHKNOTS(1,(#6160,#6161),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3384=IFCBSPLINECURVEWITHKNOTS(1,(#6161,#6163),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3385=IFCBSPLINECURVEWITHKNOTS(1,(#6163,#6164),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3386=IFCBSPLINECURVEWITHKNOTS(1,(#6164,#6165),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3387=IFCBSPLINECURVEWITHKNOTS(1,(#6165,#6166),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3388=IFCBSPLINECURVEWITHKNOTS(1,(#6166,#6167),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3389=IFCBSPLINECURVEWITHKNOTS(1,(#6167,#6169),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3390=IFCBSPLINECURVEWITHKNOTS(1,(#6169,#6170),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3391=IFCBSPLINECURVEWITHKNOTS(1,(#6170,#6172),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3392=IFCBSPLINECURVEWITHKNOTS(1,(#6172,#6174),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3393=IFCBSPLINECURVEWITHKNOTS(1,(#6174,#6175),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3394=IFCBSPLINECURVEWITHKNOTS(1,(#6175,#6177),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3395=IFCBSPLINECURVEWITHKNOTS(1,(#6177,#6178),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3396=IFCBSPLINECURVEWITHKNOTS(1,(#6178,#6180),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3397=IFCBSPLINECURVEWITHKNOTS(1,(#6180,#6181),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3398=IFCBSPLINECURVEWITHKNOTS(1,(#6181,#6183),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3399=IFCBSPLINECURVEWITHKNOTS(1,(#6183,#6184),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3400=IFCBSPLINECURVEWITHKNOTS(1,(#6184,#6186),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3401=IFCBSPLINECURVEWITHKNOTS(1,(#6186,#6187),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3402=IFCBSPLINECURVEWITHKNOTS(1,(#6187,#6189),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3403=IFCBSPLINECURVEWITHKNOTS(1,(#6189,#6190),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3404=IFCBSPLINECURVEWITHKNOTS(1,(#6190,#6192),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3405=IFCBSPLINECURVEWITHKNOTS(1,(#6192,#6193),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3406=IFCBSPLINECURVEWITHKNOTS(1,(#6193,#6195),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3407=IFCBSPLINECURVEWITHKNOTS(1,(#6195,#6196),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3408=IFCBSPLINECURVEWITHKNOTS(1,(#6196,#6198),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3409=IFCBSPLINECURVEWITHKNOTS(1,(#6198,#6199),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3410=IFCBSPLINECURVEWITHKNOTS(1,(#6199,#6200),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3411=IFCBSPLINECURVEWITHKNOTS(1,(#6200,#6201),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3412=IFCBSPLINECURVEWITHKNOTS(1,(#6201,#6203),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3413=IFCBSPLINECURVEWITHKNOTS(1,(#6203,#6204),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3414=IFCBSPLINECURVEWITHKNOTS(1,(#6204,#6206),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3415=IFCBSPLINECURVEWITHKNOTS(1,(#6206,#6208),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3416=IFCBSPLINECURVEWITHKNOTS(1,(#6208,#6209),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3417=IFCBSPLINECURVEWITHKNOTS(1,(#6209,#6211),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3418=IFCBSPLINECURVEWITHKNOTS(1,(#6211,#6212),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3419=IFCBSPLINECURVEWITHKNOTS(1,(#6212,#6214),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3420=IFCBSPLINECURVEWITHKNOTS(1,(#6214,#6215),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3421=IFCBSPLINECURVEWITHKNOTS(1,(#6215,#6217),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3422=IFCBSPLINECURVEWITHKNOTS(1,(#6217,#6218),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3423=IFCBSPLINECURVEWITHKNOTS(1,(#6218,#6220),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3424=IFCBSPLINECURVEWITHKNOTS(1,(#6220,#6221),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3425=IFCBSPLINECURVEWITHKNOTS(1,(#6221,#6223),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3426=IFCBSPLINECURVEWITHKNOTS(1,(#6223,#6224),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3427=IFCBSPLINECURVEWITHKNOTS(1,(#6224,#6226),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3428=IFCBSPLINECURVEWITHKNOTS(1,(#6226,#6227),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3429=IFCBSPLINECURVEWITHKNOTS(1,(#6227,#6229),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3430=IFCBSPLINECURVEWITHKNOTS(1,(#6229,#6230),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3431=IFCBSPLINECURVEWITHKNOTS(1,(#6230,#6232),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3432=IFCBSPLINECURVEWITHKNOTS(1,(#6232,#6233),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3433=IFCBSPLINECURVEWITHKNOTS(1,(#6233,#6234),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3434=IFCBSPLINECURVEWITHKNOTS(1,(#6234,#6235),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3435=IFCBSPLINECURVEWITHKNOTS(1,(#6235,#6236),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3436=IFCBSPLINECURVEWITHKNOTS(1,(#6236,#6238),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3437=IFCBSPLINECURVEWITHKNOTS(1,(#6238,#6239),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3438=IFCBSPLINECURVEWITHKNOTS(1,(#6239,#6241),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3439=IFCBSPLINECURVEWITHKNOTS(1,(#6241,#6242),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3440=IFCBSPLINECURVEWITHKNOTS(1,(#6242,#6244),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3441=IFCBSPLINECURVEWITHKNOTS(1,(#6244,#6245),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3442=IFCBSPLINECURVEWITHKNOTS(1,(#6245,#6247),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3443=IFCBSPLINECURVEWITHKNOTS(1,(#6247,#6248),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3444=IFCBSPLINECURVEWITHKNOTS(1,(#6248,#6250),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3445=IFCBSPLINECURVEWITHKNOTS(1,(#6250,#6251),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3446=IFCBSPLINECURVEWITHKNOTS(1,(#6251,#6253),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3447=IFCBSPLINECURVEWITHKNOTS(1,(#6253,#6254),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3448=IFCBSPLINECURVEWITHKNOTS(1,(#6254,#6256),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3449=IFCBSPLINECURVEWITHKNOTS(1,(#6256,#6257),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3450=IFCBSPLINECURVEWITHKNOTS(1,(#6257,#6259),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3451=IFCBSPLINECURVEWITHKNOTS(1,(#6259,#6260),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3452=IFCBSPLINECURVEWITHKNOTS(1,(#6260,#6262),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3453=IFCBSPLINECURVEWITHKNOTS(1,(#6262,#6263),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3454=IFCBSPLINECURVEWITHKNOTS(1,(#6263,#6265),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3455=IFCBSPLINECURVEWITHKNOTS(1,(#6265,#6266),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3456=IFCBSPLINECURVEWITHKNOTS(1,(#6266,#6267),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3457=IFCBSPLINECURVEWITHKNOTS(1,(#6267,#6268),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3458=IFCBSPLINECURVEWITHKNOTS(1,(#6268,#6269),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3459=IFCBSPLINECURVEWITHKNOTS(1,(#6269,#6271),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3460=IFCBSPLINECURVEWITHKNOTS(1,(#6271,#6272),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3461=IFCBSPLINECURVEWITHKNOTS(1,(#6272,#6274),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3462=IFCBSPLINECURVEWITHKNOTS(1,(#6274,#6276),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3463=IFCBSPLINECURVEWITHKNOTS(1,(#6276,#6277),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3464=IFCBSPLINECURVEWITHKNOTS(1,(#6277,#6279),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3465=IFCBSPLINECURVEWITHKNOTS(1,(#6279,#6280),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3466=IFCBSPLINECURVEWITHKNOTS(1,(#6280,#6282),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3467=IFCBSPLINECURVEWITHKNOTS(1,(#6282,#6283),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3468=IFCBSPLINECURVEWITHKNOTS(1,(#6283,#6285),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3469=IFCBSPLINECURVEWITHKNOTS(1,(#6285,#6286),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3470=IFCBSPLINECURVEWITHKNOTS(1,(#6286,#6288),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3471=IFCBSPLINECURVEWITHKNOTS(1,(#6288,#6290),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3472=IFCBSPLINECURVEWITHKNOTS(1,(#6290,#6292),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3473=IFCBSPLINECURVEWITHKNOTS(1,(#6292,#6293),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3474=IFCBSPLINECURVEWITHKNOTS(1,(#6293,#6295),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3475=IFCBSPLINECURVEWITHKNOTS(1,(#6295,#6296),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3476=IFCBSPLINECURVEWITHKNOTS(1,(#6296,#6298),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3477=IFCBSPLINECURVEWITHKNOTS(1,(#6298,#6299),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3478=IFCBSPLINECURVEWITHKNOTS(1,(#6299,#6301),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3479=IFCBSPLINECURVEWITHKNOTS(1,(#6301,#6302),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3480=IFCBSPLINECURVEWITHKNOTS(1,(#6302,#6304),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3481=IFCBSPLINECURVEWITHKNOTS(1,(#6304,#6305),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3482=IFCBSPLINECURVEWITHKNOTS(1,(#6305,#6307),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3483=IFCBSPLINECURVEWITHKNOTS(1,(#6307,#6309),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3484=IFCBSPLINECURVEWITHKNOTS(1,(#6309,#6310),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3485=IFCBSPLINECURVEWITHKNOTS(1,(#6310,#6312),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3486=IFCBSPLINECURVEWITHKNOTS(1,(#6312,#6313),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3487=IFCBSPLINECURVEWITHKNOTS(1,(#6313,#6315),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3488=IFCBSPLINECURVEWITHKNOTS(1,(#6315,#6316),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3489=IFCBSPLINECURVEWITHKNOTS(1,(#6316,#6318),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3490=IFCBSPLINECURVEWITHKNOTS(1,(#6318,#6319),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3491=IFCBSPLINECURVEWITHKNOTS(1,(#6319,#6321),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3492=IFCBSPLINECURVEWITHKNOTS(1,(#6321,#6322),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3493=IFCBSPLINECURVEWITHKNOTS(1,(#6322,#6324),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3494=IFCBSPLINECURVEWITHKNOTS(1,(#6324,#6326),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3495=IFCBSPLINECURVEWITHKNOTS(1,(#6326,#6328),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3496=IFCBSPLINECURVEWITHKNOTS(1,(#6328,#6329),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3497=IFCBSPLINECURVEWITHKNOTS(1,(#6329,#6331),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3498=IFCBSPLINECURVEWITHKNOTS(1,(#6331,#6332),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3499=IFCBSPLINECURVEWITHKNOTS(1,(#6332,#6334),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3500=IFCBSPLINECURVEWITHKNOTS(1,(#6334,#6335),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3501=IFCBSPLINECURVEWITHKNOTS(1,(#6335,#6337),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3502=IFCBSPLINECURVEWITHKNOTS(1,(#6337,#6338),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3503=IFCBSPLINECURVEWITHKNOTS(1,(#6338,#5777),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3504=IFCBSPLINECURVEWITHKNOTS(1,(#5777,#6340),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3505=IFCBSPLINECURVEWITHKNOTS(1,(#6340,#6341),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3506=IFCBSPLINECURVEWITHKNOTS(1,(#6341,#6343),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3507=IFCBSPLINECURVEWITHKNOTS(1,(#6343,#5776),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3508=IFCBSPLINECURVEWITHKNOTS(1,(#6345,#6346),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3509=IFCBSPLINECURVEWITHKNOTS(1,(#6346,#6347),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3510=IFCBSPLINECURVEWITHKNOTS(1,(#6347,#6349),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3511=IFCBSPLINECURVEWITHKNOTS(1,(#6349,#6350),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3512=IFCBSPLINECURVEWITHKNOTS(1,(#6350,#6351),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3513=IFCBSPLINECURVEWITHKNOTS(1,(#6351,#6352),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3514=IFCBSPLINECURVEWITHKNOTS(1,(#6352,#6353),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3515=IFCBSPLINECURVEWITHKNOTS(1,(#6353,#6355),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3516=IFCBSPLINECURVEWITHKNOTS(1,(#6355,#6356),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3517=IFCBSPLINECURVEWITHKNOTS(1,(#6356,#6357),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3518=IFCBSPLINECURVEWITHKNOTS(1,(#6357,#6359),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3519=IFCBSPLINECURVEWITHKNOTS(1,(#6359,#6360),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3520=IFCBSPLINECURVEWITHKNOTS(1,(#6360,#6362),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3521=IFCBSPLINECURVEWITHKNOTS(1,(#6362,#6363),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3522=IFCBSPLINECURVEWITHKNOTS(1,(#6363,#6365),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3523=IFCBSPLINECURVEWITHKNOTS(1,(#6365,#6366),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3524=IFCBSPLINECURVEWITHKNOTS(1,(#6366,#6368),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3525=IFCBSPLINECURVEWITHKNOTS(1,(#6368,#6369),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3526=IFCBSPLINECURVEWITHKNOTS(1,(#6369,#6371),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3527=IFCBSPLINECURVEWITHKNOTS(1,(#6371,#6372),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3528=IFCBSPLINECURVEWITHKNOTS(1,(#6372,#6374),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3529=IFCBSPLINECURVEWITHKNOTS(1,(#6374,#6375),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3530=IFCBSPLINECURVEWITHKNOTS(1,(#6375,#6377),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3531=IFCBSPLINECURVEWITHKNOTS(1,(#6377,#6378),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3532=IFCBSPLINECURVEWITHKNOTS(1,(#6378,#6380),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3533=IFCBSPLINECURVEWITHKNOTS(1,(#6380,#6382),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3534=IFCBSPLINECURVEWITHKNOTS(1,(#6382,#6383),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3535=IFCBSPLINECURVEWITHKNOTS(1,(#6383,#6385),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3536=IFCBSPLINECURVEWITHKNOTS(1,(#6385,#6386),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3537=IFCBSPLINECURVEWITHKNOTS(1,(#6386,#6388),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3538=IFCBSPLINECURVEWITHKNOTS(1,(#6388,#6389),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3539=IFCBSPLINECURVEWITHKNOTS(1,(#6389,#6391),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3540=IFCBSPLINECURVEWITHKNOTS(1,(#6391,#6392),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3541=IFCBSPLINECURVEWITHKNOTS(1,(#6392,#6394),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3542=IFCBSPLINECURVEWITHKNOTS(1,(#6394,#6395),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3543=IFCBSPLINECURVEWITHKNOTS(1,(#6395,#6397),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3544=IFCBSPLINECURVEWITHKNOTS(1,(#6397,#6398),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3545=IFCBSPLINECURVEWITHKNOTS(1,(#6398,#6400),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3546=IFCBSPLINECURVEWITHKNOTS(1,(#6400,#6401),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3547=IFCBSPLINECURVEWITHKNOTS(1,(#6401,#6403),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3548=IFCBSPLINECURVEWITHKNOTS(1,(#6403,#6404),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3549=IFCBSPLINECURVEWITHKNOTS(1,(#6404,#6406),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3550=IFCBSPLINECURVEWITHKNOTS(1,(#6406,#6407),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3551=IFCBSPLINECURVEWITHKNOTS(1,(#6407,#6409),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3552=IFCBSPLINECURVEWITHKNOTS(1,(#6409,#6410),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3553=IFCBSPLINECURVEWITHKNOTS(1,(#6410,#6412),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3554=IFCBSPLINECURVEWITHKNOTS(1,(#6412,#6413),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3555=IFCBSPLINECURVEWITHKNOTS(1,(#6413,#6415),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3556=IFCBSPLINECURVEWITHKNOTS(1,(#6415,#6416),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3557=IFCBSPLINECURVEWITHKNOTS(1,(#6416,#6418),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3558=IFCBSPLINECURVEWITHKNOTS(1,(#6418,#6419),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3559=IFCBSPLINECURVEWITHKNOTS(1,(#6419,#6421),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3560=IFCBSPLINECURVEWITHKNOTS(1,(#6421,#6422),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3561=IFCBSPLINECURVEWITHKNOTS(1,(#6422,#6424),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3562=IFCBSPLINECURVEWITHKNOTS(1,(#6424,#6425),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3563=IFCBSPLINECURVEWITHKNOTS(1,(#6425,#6427),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3564=IFCBSPLINECURVEWITHKNOTS(1,(#6427,#6428),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3565=IFCBSPLINECURVEWITHKNOTS(1,(#6428,#6430),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3566=IFCBSPLINECURVEWITHKNOTS(1,(#6430,#6431),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3567=IFCBSPLINECURVEWITHKNOTS(1,(#6431,#6433),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3568=IFCBSPLINECURVEWITHKNOTS(1,(#6433,#6434),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3569=IFCBSPLINECURVEWITHKNOTS(1,(#6434,#6436),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3570=IFCBSPLINECURVEWITHKNOTS(1,(#6436,#6437),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3571=IFCBSPLINECURVEWITHKNOTS(1,(#6437,#6439),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3572=IFCBSPLINECURVEWITHKNOTS(1,(#6439,#6440),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3573=IFCBSPLINECURVEWITHKNOTS(1,(#6440,#6442),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3574=IFCBSPLINECURVEWITHKNOTS(1,(#6442,#6443),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3575=IFCBSPLINECURVEWITHKNOTS(1,(#6443,#6445),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3576=IFCBSPLINECURVEWITHKNOTS(1,(#6445,#6446),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3577=IFCBSPLINECURVEWITHKNOTS(1,(#6446,#6448),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3578=IFCBSPLINECURVEWITHKNOTS(1,(#6448,#6449),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3579=IFCBSPLINECURVEWITHKNOTS(1,(#6449,#6451),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3580=IFCBSPLINECURVEWITHKNOTS(1,(#6451,#6452),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3581=IFCBSPLINECURVEWITHKNOTS(1,(#6452,#6454),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3582=IFCBSPLINECURVEWITHKNOTS(1,(#6454,#6455),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3583=IFCBSPLINECURVEWITHKNOTS(1,(#6455,#6457),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3584=IFCBSPLINECURVEWITHKNOTS(1,(#6457,#6458),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3585=IFCBSPLINECURVEWITHKNOTS(1,(#6458,#6460),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3586=IFCBSPLINECURVEWITHKNOTS(1,(#6460,#6461),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3587=IFCBSPLINECURVEWITHKNOTS(1,(#6461,#6463),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3588=IFCBSPLINECURVEWITHKNOTS(1,(#6463,#6464),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3589=IFCBSPLINECURVEWITHKNOTS(1,(#6464,#6466),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3590=IFCBSPLINECURVEWITHKNOTS(1,(#6466,#6468),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3591=IFCBSPLINECURVEWITHKNOTS(1,(#6468,#6469),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3592=IFCBSPLINECURVEWITHKNOTS(1,(#6469,#6470),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3593=IFCBSPLINECURVEWITHKNOTS(1,(#6470,#6472),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3594=IFCBSPLINECURVEWITHKNOTS(1,(#6472,#6473),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3595=IFCBSPLINECURVEWITHKNOTS(1,(#6473,#6474),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3596=IFCBSPLINECURVEWITHKNOTS(1,(#6474,#6476),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3597=IFCBSPLINECURVEWITHKNOTS(1,(#6476,#6478),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3598=IFCBSPLINECURVEWITHKNOTS(1,(#6478,#6479),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3599=IFCBSPLINECURVEWITHKNOTS(1,(#6479,#6480),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3600=IFCBSPLINECURVEWITHKNOTS(1,(#6480,#6481),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3601=IFCBSPLINECURVEWITHKNOTS(1,(#6481,#6482),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3602=IFCBSPLINECURVEWITHKNOTS(1,(#6482,#6484),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3603=IFCBSPLINECURVEWITHKNOTS(1,(#6484,#6485),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3604=IFCBSPLINECURVEWITHKNOTS(1,(#6485,#6486),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3605=IFCBSPLINECURVEWITHKNOTS(1,(#6486,#6487),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3606=IFCBSPLINECURVEWITHKNOTS(1,(#6487,#6488),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3607=IFCBSPLINECURVEWITHKNOTS(1,(#6488,#6490),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3608=IFCBSPLINECURVEWITHKNOTS(1,(#6490,#6492),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3609=IFCBSPLINECURVEWITHKNOTS(1,(#6492,#6493),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3610=IFCBSPLINECURVEWITHKNOTS(1,(#6493,#6494),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3611=IFCBSPLINECURVEWITHKNOTS(1,(#6494,#6495),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3612=IFCBSPLINECURVEWITHKNOTS(1,(#6495,#6497),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3613=IFCBSPLINECURVEWITHKNOTS(1,(#6497,#6499),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3614=IFCBSPLINECURVEWITHKNOTS(1,(#6499,#6500),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3615=IFCBSPLINECURVEWITHKNOTS(1,(#6500,#6501),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3616=IFCBSPLINECURVEWITHKNOTS(1,(#6501,#6503),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3617=IFCBSPLINECURVEWITHKNOTS(1,(#6503,#6504),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3618=IFCBSPLINECURVEWITHKNOTS(1,(#6504,#6505),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3619=IFCBSPLINECURVEWITHKNOTS(1,(#6505,#6507),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3620=IFCBSPLINECURVEWITHKNOTS(1,(#6507,#6509),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3621=IFCBSPLINECURVEWITHKNOTS(1,(#6509,#6510),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3622=IFCBSPLINECURVEWITHKNOTS(1,(#6510,#6512),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3623=IFCBSPLINECURVEWITHKNOTS(1,(#6512,#6513),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3624=IFCBSPLINECURVEWITHKNOTS(1,(#6513,#6515),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3625=IFCBSPLINECURVEWITHKNOTS(1,(#6515,#6516),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3626=IFCBSPLINECURVEWITHKNOTS(1,(#6516,#6518),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3627=IFCBSPLINECURVEWITHKNOTS(1,(#6518,#6519),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3628=IFCBSPLINECURVEWITHKNOTS(1,(#6519,#6521),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3629=IFCBSPLINECURVEWITHKNOTS(1,(#6521,#6522),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3630=IFCBSPLINECURVEWITHKNOTS(1,(#6522,#6524),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3631=IFCBSPLINECURVEWITHKNOTS(1,(#6524,#6525),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3632=IFCBSPLINECURVEWITHKNOTS(1,(#6525,#6527),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3633=IFCBSPLINECURVEWITHKNOTS(1,(#6527,#6529),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3634=IFCBSPLINECURVEWITHKNOTS(1,(#6529,#6530),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3635=IFCBSPLINECURVEWITHKNOTS(1,(#6530,#6532),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3636=IFCBSPLINECURVEWITHKNOTS(1,(#6532,#6533),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3637=IFCBSPLINECURVEWITHKNOTS(1,(#6533,#6535),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3638=IFCBSPLINECURVEWITHKNOTS(1,(#6535,#6536),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3639=IFCBSPLINECURVEWITHKNOTS(1,(#6536,#6538),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3640=IFCBSPLINECURVEWITHKNOTS(1,(#6538,#6539),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3641=IFCBSPLINECURVEWITHKNOTS(1,(#6539,#6541),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3642=IFCBSPLINECURVEWITHKNOTS(1,(#6541,#6542),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3643=IFCBSPLINECURVEWITHKNOTS(1,(#6542,#6544),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3644=IFCBSPLINECURVEWITHKNOTS(1,(#6544,#6545),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3645=IFCBSPLINECURVEWITHKNOTS(1,(#6545,#6547),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3646=IFCBSPLINECURVEWITHKNOTS(1,(#6547,#6548),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3647=IFCBSPLINECURVEWITHKNOTS(1,(#6548,#6550),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3648=IFCBSPLINECURVEWITHKNOTS(1,(#6550,#6551),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3649=IFCBSPLINECURVEWITHKNOTS(1,(#6551,#6553),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3650=IFCBSPLINECURVEWITHKNOTS(1,(#6553,#6554),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3651=IFCBSPLINECURVEWITHKNOTS(1,(#6554,#6556),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3652=IFCBSPLINECURVEWITHKNOTS(1,(#6556,#6557),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3653=IFCBSPLINECURVEWITHKNOTS(1,(#6557,#6559),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3654=IFCBSPLINECURVEWITHKNOTS(1,(#6559,#6560),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3655=IFCBSPLINECURVEWITHKNOTS(1,(#6560,#6562),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3656=IFCBSPLINECURVEWITHKNOTS(1,(#6562,#6563),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3657=IFCBSPLINECURVEWITHKNOTS(1,(#6563,#6565),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3658=IFCBSPLINECURVEWITHKNOTS(1,(#6565,#6566),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3659=IFCBSPLINECURVEWITHKNOTS(1,(#6566,#6568),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3660=IFCBSPLINECURVEWITHKNOTS(1,(#6568,#6569),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3661=IFCBSPLINECURVEWITHKNOTS(1,(#6569,#6571),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3662=IFCBSPLINECURVEWITHKNOTS(1,(#6571,#6572),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3663=IFCBSPLINECURVEWITHKNOTS(1,(#6572,#6574),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3664=IFCBSPLINECURVEWITHKNOTS(1,(#6574,#6575),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3665=IFCBSPLINECURVEWITHKNOTS(1,(#6575,#6577),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3666=IFCBSPLINECURVEWITHKNOTS(1,(#6577,#6578),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3667=IFCBSPLINECURVEWITHKNOTS(1,(#6578,#6580),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3668=IFCBSPLINECURVEWITHKNOTS(1,(#6580,#6581),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3669=IFCBSPLINECURVEWITHKNOTS(1,(#6581,#6583),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3670=IFCBSPLINECURVEWITHKNOTS(1,(#6583,#6584),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3671=IFCBSPLINECURVEWITHKNOTS(1,(#6584,#6586),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3672=IFCBSPLINECURVEWITHKNOTS(1,(#6586,#6587),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3673=IFCBSPLINECURVEWITHKNOTS(1,(#6587,#6589),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3674=IFCBSPLINECURVEWITHKNOTS(1,(#6589,#6591),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3675=IFCBSPLINECURVEWITHKNOTS(1,(#6591,#6592),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3676=IFCBSPLINECURVEWITHKNOTS(1,(#6592,#6594),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3677=IFCBSPLINECURVEWITHKNOTS(1,(#6594,#6595),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3678=IFCBSPLINECURVEWITHKNOTS(1,(#6595,#6597),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3679=IFCBSPLINECURVEWITHKNOTS(1,(#6597,#6598),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3680=IFCBSPLINECURVEWITHKNOTS(1,(#6598,#6600),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3681=IFCBSPLINECURVEWITHKNOTS(1,(#6600,#6601),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3682=IFCBSPLINECURVEWITHKNOTS(1,(#6601,#6603),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3683=IFCBSPLINECURVEWITHKNOTS(1,(#6603,#6604),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3684=IFCBSPLINECURVEWITHKNOTS(1,(#6604,#6606),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3685=IFCBSPLINECURVEWITHKNOTS(1,(#6606,#6607),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3686=IFCBSPLINECURVEWITHKNOTS(1,(#6607,#6609),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3687=IFCBSPLINECURVEWITHKNOTS(1,(#6609,#6611),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3688=IFCBSPLINECURVEWITHKNOTS(1,(#6611,#6612),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3689=IFCBSPLINECURVEWITHKNOTS(1,(#6612,#6613),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3690=IFCBSPLINECURVEWITHKNOTS(1,(#6613,#6615),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3691=IFCBSPLINECURVEWITHKNOTS(1,(#6615,#6616),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3692=IFCBSPLINECURVEWITHKNOTS(1,(#6616,#6618),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3693=IFCBSPLINECURVEWITHKNOTS(1,(#6618,#6619),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3694=IFCBSPLINECURVEWITHKNOTS(1,(#6619,#6621),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3695=IFCBSPLINECURVEWITHKNOTS(1,(#6621,#6622),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3696=IFCBSPLINECURVEWITHKNOTS(1,(#6622,#6623),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3697=IFCBSPLINECURVEWITHKNOTS(1,(#6623,#6624),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3698=IFCBSPLINECURVEWITHKNOTS(1,(#6624,#6626),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3699=IFCBSPLINECURVEWITHKNOTS(1,(#6626,#6627),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3700=IFCBSPLINECURVEWITHKNOTS(1,(#6627,#6628),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3701=IFCBSPLINECURVEWITHKNOTS(1,(#6628,#6629),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3702=IFCBSPLINECURVEWITHKNOTS(1,(#6629,#6345),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3703=IFCBSPLINECURVEWITHKNOTS(9,(#6632,#6647,#6662,#6677,#6692,#6707,#6722,
#6737,#6752,#6767,#6782,#6797,#6812,#6827,#6842),.UNSPECIFIED.,.F.,.U.,
(10,1,1,1,1,1,10),(0.,0.166666666666667,0.333333333333333,0.5,0.666666666666667,
0.833333333333333,1.),.UNSPECIFIED.);
#3704=IFCBSPLINECURVEWITHKNOTS(9,(#6842,#6843,#6844,#6845,#6846,#6847,#6848,
#6849,#6850,#6851,#6852,#6853,#6854,#6855,#6856),.UNSPECIFIED.,.F.,.U.,
(10,1,1,1,1,1,10),(0.,0.166666666666667,0.333333333333333,0.5,0.666666666666667,
0.833333333333333,1.),.UNSPECIFIED.);
#3705=IFCBSPLINECURVEWITHKNOTS(9,(#6856,#6841,#6826,#6811,#6796,#6781,#6766,
#6751,#6736,#6721,#6706,#6691,#6676,#6661,#6646),.UNSPECIFIED.,.F.,.U.,
(10,1,1,1,1,1,10),(0.,0.166666666666667,0.333333333333333,0.5,0.666666666666667,
0.833333333333333,1.),.UNSPECIFIED.);
#3706=IFCBSPLINECURVEWITHKNOTS(9,(#6646,#6645,#6644,#6643,#6642,#6641,#6640,
#6639,#6638,#6637,#6636,#6635,#6634,#6633,#6632),.UNSPECIFIED.,.F.,.U.,
(10,1,1,1,1,1,10),(0.,0.166666666666667,0.333333333333333,0.5,0.666666666666667,
0.833333333333333,1.),.UNSPECIFIED.);
#3707=IFCBSPLINECURVEWITHKNOTS(1,(#6863,#6875),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3708=IFCBSPLINECURVEWITHKNOTS(1,(#6875,#6861),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3709=IFCBSPLINECURVEWITHKNOTS(1,(#6861,#6876),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3710=IFCBSPLINECURVEWITHKNOTS(1,(#6876,#6859),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3711=IFCBSPLINECURVEWITHKNOTS(1,(#6859,#6877),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3712=IFCBSPLINECURVEWITHKNOTS(1,(#6877,#6878),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3713=IFCBSPLINECURVEWITHKNOTS(1,(#6878,#6879),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3714=IFCBSPLINECURVEWITHKNOTS(1,(#6879,#6860),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3715=IFCBSPLINECURVEWITHKNOTS(1,(#6860,#6880),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3716=IFCBSPLINECURVEWITHKNOTS(1,(#6880,#6862),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3717=IFCBSPLINECURVEWITHKNOTS(1,(#6862,#6881),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3718=IFCBSPLINECURVEWITHKNOTS(1,(#6881,#6864),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3719=IFCBSPLINECURVEWITHKNOTS(1,(#6864,#6883),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3720=IFCBSPLINECURVEWITHKNOTS(1,(#6883,#6885),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3721=IFCBSPLINECURVEWITHKNOTS(1,(#6885,#6887),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3722=IFCBSPLINECURVEWITHKNOTS(1,(#6887,#6863),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3723=IFCBSPLINECURVEWITHKNOTS(1,(#6867,#6888),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3724=IFCBSPLINECURVEWITHKNOTS(1,(#6888,#6889),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3725=IFCBSPLINECURVEWITHKNOTS(1,(#6889,#6890),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3726=IFCBSPLINECURVEWITHKNOTS(1,(#6890,#6865),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3727=IFCBSPLINECURVEWITHKNOTS(1,(#6865,#6892),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3728=IFCBSPLINECURVEWITHKNOTS(1,(#6892,#6894),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3729=IFCBSPLINECURVEWITHKNOTS(1,(#6894,#6896),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3730=IFCBSPLINECURVEWITHKNOTS(1,(#6896,#6866),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3731=IFCBSPLINECURVEWITHKNOTS(1,(#6866,#6897),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3732=IFCBSPLINECURVEWITHKNOTS(1,(#6897,#6898),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3733=IFCBSPLINECURVEWITHKNOTS(1,(#6898,#6900),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3734=IFCBSPLINECURVEWITHKNOTS(1,(#6900,#6868),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3735=IFCBSPLINECURVEWITHKNOTS(1,(#6868,#6902),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3736=IFCBSPLINECURVEWITHKNOTS(1,(#6902,#6904),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3737=IFCBSPLINECURVEWITHKNOTS(1,(#6904,#6906),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3738=IFCBSPLINECURVEWITHKNOTS(1,(#6906,#6867),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3739=IFCBSPLINECURVEWITHKNOTS(1,(#6908,#6909),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3740=IFCBSPLINECURVEWITHKNOTS(1,(#6909,#6911),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3741=IFCBSPLINECURVEWITHKNOTS(1,(#6911,#6913),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3742=IFCBSPLINECURVEWITHKNOTS(1,(#6913,#6915),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3743=IFCBSPLINECURVEWITHKNOTS(1,(#6915,#6917),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3744=IFCBSPLINECURVEWITHKNOTS(1,(#6917,#6919),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3745=IFCBSPLINECURVEWITHKNOTS(1,(#6919,#6921),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3746=IFCBSPLINECURVEWITHKNOTS(1,(#6921,#6923),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3747=IFCBSPLINECURVEWITHKNOTS(1,(#6923,#6925),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3748=IFCBSPLINECURVEWITHKNOTS(1,(#6925,#6927),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3749=IFCBSPLINECURVEWITHKNOTS(1,(#6927,#6929),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3750=IFCBSPLINECURVEWITHKNOTS(1,(#6929,#6908),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3751=IFCBSPLINECURVEWITHKNOTS(1,(#6865,#6930),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3752=IFCBSPLINECURVEWITHKNOTS(1,(#6930,#6931),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3753=IFCBSPLINECURVEWITHKNOTS(1,(#6931,#6932),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3754=IFCBSPLINECURVEWITHKNOTS(1,(#6932,#6863),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3755=IFCBSPLINECURVEWITHKNOTS(1,(#6863,#6887),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3756=IFCBSPLINECURVEWITHKNOTS(1,(#6887,#6885),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3757=IFCBSPLINECURVEWITHKNOTS(1,(#6885,#6883),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3758=IFCBSPLINECURVEWITHKNOTS(1,(#6883,#6864),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3759=IFCBSPLINECURVEWITHKNOTS(1,(#6864,#6933),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3760=IFCBSPLINECURVEWITHKNOTS(1,(#6933,#6934),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3761=IFCBSPLINECURVEWITHKNOTS(1,(#6934,#6935),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3762=IFCBSPLINECURVEWITHKNOTS(1,(#6935,#6866),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3763=IFCBSPLINECURVEWITHKNOTS(1,(#6866,#6896),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3764=IFCBSPLINECURVEWITHKNOTS(1,(#6896,#6894),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3765=IFCBSPLINECURVEWITHKNOTS(1,(#6894,#6892),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3766=IFCBSPLINECURVEWITHKNOTS(1,(#6892,#6865),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3767=IFCBSPLINECURVEWITHKNOTS(1,(#6938,#6939),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3768=IFCBSPLINECURVEWITHKNOTS(1,(#6939,#6941),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3769=IFCBSPLINECURVEWITHKNOTS(1,(#6941,#6943),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3770=IFCBSPLINECURVEWITHKNOTS(1,(#6943,#6945),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3771=IFCBSPLINECURVEWITHKNOTS(1,(#6945,#6947),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3772=IFCBSPLINECURVEWITHKNOTS(1,(#6947,#6949),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3773=IFCBSPLINECURVEWITHKNOTS(1,(#6949,#6951),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3774=IFCBSPLINECURVEWITHKNOTS(1,(#6951,#6953),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3775=IFCBSPLINECURVEWITHKNOTS(1,(#6953,#6955),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3776=IFCBSPLINECURVEWITHKNOTS(1,(#6955,#6957),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3777=IFCBSPLINECURVEWITHKNOTS(1,(#6957,#6959),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3778=IFCBSPLINECURVEWITHKNOTS(1,(#6959,#6938),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3779=IFCBSPLINECURVEWITHKNOTS(1,(#6867,#6869),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3780=IFCBSPLINECURVEWITHKNOTS(1,(#6869,#6871),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3781=IFCBSPLINECURVEWITHKNOTS(1,(#6871,#6873),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3782=IFCBSPLINECURVEWITHKNOTS(1,(#6873,#6859),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3783=IFCBSPLINECURVEWITHKNOTS(1,(#6859,#6877),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3784=IFCBSPLINECURVEWITHKNOTS(1,(#6877,#6878),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3785=IFCBSPLINECURVEWITHKNOTS(1,(#6878,#6879),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3786=IFCBSPLINECURVEWITHKNOTS(1,(#6879,#6860),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3787=IFCBSPLINECURVEWITHKNOTS(1,(#6860,#6874),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3788=IFCBSPLINECURVEWITHKNOTS(1,(#6874,#6872),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3789=IFCBSPLINECURVEWITHKNOTS(1,(#6872,#6870),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3790=IFCBSPLINECURVEWITHKNOTS(1,(#6870,#6868),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3791=IFCBSPLINECURVEWITHKNOTS(1,(#6868,#6902),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3792=IFCBSPLINECURVEWITHKNOTS(1,(#6902,#6904),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3793=IFCBSPLINECURVEWITHKNOTS(1,(#6904,#6906),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3794=IFCBSPLINECURVEWITHKNOTS(1,(#6906,#6867),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3795=IFCBSPLINECURVEWITHKNOTS(1,(#6963,#6966),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3796=IFCBSPLINECURVEWITHKNOTS(1,(#6966,#6967),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3797=IFCBSPLINECURVEWITHKNOTS(1,(#6967,#6968),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3798=IFCBSPLINECURVEWITHKNOTS(1,(#6968,#6965),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3799=IFCBSPLINECURVEWITHKNOTS(1,(#6965,#6969),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3800=IFCBSPLINECURVEWITHKNOTS(1,(#6969,#6970),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3801=IFCBSPLINECURVEWITHKNOTS(1,(#6970,#6971),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3802=IFCBSPLINECURVEWITHKNOTS(1,(#6971,#6964),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3803=IFCBSPLINECURVEWITHKNOTS(1,(#6964,#6972),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3804=IFCBSPLINECURVEWITHKNOTS(1,(#6972,#6973),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3805=IFCBSPLINECURVEWITHKNOTS(1,(#6973,#6974),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3806=IFCBSPLINECURVEWITHKNOTS(1,(#6974,#6962),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3807=IFCBSPLINECURVEWITHKNOTS(1,(#6962,#6975),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3808=IFCBSPLINECURVEWITHKNOTS(1,(#6975,#6976),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3809=IFCBSPLINECURVEWITHKNOTS(1,(#6976,#6977),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3810=IFCBSPLINECURVEWITHKNOTS(1,(#6977,#6963),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3811=IFCBSPLINECURVEWITHKNOTS(1,(#6980,#6981),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3812=IFCBSPLINECURVEWITHKNOTS(1,(#6981,#6983),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3813=IFCBSPLINECURVEWITHKNOTS(1,(#6983,#6985),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3814=IFCBSPLINECURVEWITHKNOTS(1,(#6985,#6987),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3815=IFCBSPLINECURVEWITHKNOTS(1,(#6987,#6989),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3816=IFCBSPLINECURVEWITHKNOTS(1,(#6989,#6991),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3817=IFCBSPLINECURVEWITHKNOTS(1,(#6991,#6993),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3818=IFCBSPLINECURVEWITHKNOTS(1,(#6993,#6995),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3819=IFCBSPLINECURVEWITHKNOTS(1,(#6995,#6997),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3820=IFCBSPLINECURVEWITHKNOTS(1,(#6997,#6999),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3821=IFCBSPLINECURVEWITHKNOTS(1,(#6999,#7001),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3822=IFCBSPLINECURVEWITHKNOTS(1,(#7001,#7003),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3823=IFCBSPLINECURVEWITHKNOTS(1,(#7003,#7005),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3824=IFCBSPLINECURVEWITHKNOTS(1,(#7005,#7007),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3825=IFCBSPLINECURVEWITHKNOTS(1,(#7007,#7009),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3826=IFCBSPLINECURVEWITHKNOTS(1,(#7009,#6980),.UNSPECIFIED.,.F.,.U.,(2,
2),(0.,1.),.UNSPECIFIED.);
#3827=IFCPCURVE(#5333,#4570);
#3828=IFCPCURVE(#5333,#4571);
#3829=IFCPCURVE(#5333,#4572);
#3830=IFCPCURVE(#5333,#4573);
#3831=IFCPCURVE(#5333,#4574);
#3832=IFCPCURVE(#5333,#4575);
#3833=IFCPCURVE(#5333,#4576);
#3834=IFCPCURVE(#5333,#4577);
#3835=IFCPCURVE(#5333,#4578);
#3836=IFCPCURVE(#5333,#4579);
#3837=IFCPCURVE(#5333,#4580);
#3838=IFCPCURVE(#5333,#4581);
#3839=IFCPCURVE(#5333,#4582);
#3840=IFCPCURVE(#5333,#4583);
#3841=IFCPCURVE(#5333,#4584);
#3842=IFCPCURVE(#5333,#4585);
#3843=IFCPCURVE(#5333,#4586);
#3844=IFCPCURVE(#5333,#4587);
#3845=IFCPCURVE(#5333,#4588);
#3846=IFCPCURVE(#5333,#4589);
#3847=IFCPCURVE(#5333,#4590);
#3848=IFCPCURVE(#5333,#4591);
#3849=IFCPCURVE(#5333,#4592);
#3850=IFCPCURVE(#5333,#4593);
#3851=IFCPCURVE(#5334,#4594);
#3852=IFCPCURVE(#5334,#4595);
#3853=IFCPCURVE(#5334,#4596);
#3854=IFCPCURVE(#5334,#4597);
#3855=IFCPCURVE(#47,#4598);
#3856=IFCPCURVE(#47,#4599);
#3857=IFCPCURVE(#47,#4600);
#3858=IFCPCURVE(#47,#4601);
#3859=IFCPCURVE(#48,#4602);
#3860=IFCPCURVE(#48,#4603);
#3861=IFCPCURVE(#48,#4604);
#3862=IFCPCURVE(#48,#4605);
#3863=IFCPCURVE(#49,#4606);
#3864=IFCPCURVE(#49,#4607);
#3865=IFCPCURVE(#49,#4608);
#3866=IFCPCURVE(#49,#4609);
#3867=IFCPCURVE(#5335,#4610);
#3868=IFCPCURVE(#5335,#4611);
#3869=IFCPCURVE(#5335,#4612);
#3870=IFCPCURVE(#5335,#4613);
#3871=IFCPCURVE(#5335,#4614);
#3872=IFCPCURVE(#5335,#4615);
#3873=IFCPCURVE(#5335,#4616);
#3874=IFCPCURVE(#5335,#4617);
#3875=IFCPCURVE(#5335,#4618);
#3876=IFCPCURVE(#5335,#4619);
#3877=IFCPCURVE(#5335,#4620);
#3878=IFCPCURVE(#5335,#4621);
#3879=IFCPCURVE(#5335,#4622);
#3880=IFCPCURVE(#5335,#4623);
#3881=IFCPCURVE(#5335,#4624);
#3882=IFCPCURVE(#5335,#4625);
#3883=IFCPCURVE(#5335,#4626);
#3884=IFCPCURVE(#5335,#4627);
#3885=IFCPCURVE(#5335,#4628);
#3886=IFCPCURVE(#5335,#4629);
#3887=IFCPCURVE(#5335,#4630);
#3888=IFCPCURVE(#5335,#4631);
#3889=IFCPCURVE(#5335,#4632);
#3890=IFCPCURVE(#5335,#4633);
#3891=IFCPCURVE(#5335,#4634);
#3892=IFCPCURVE(#5335,#4635);
#3893=IFCPCURVE(#5335,#4636);
#3894=IFCPCURVE(#5335,#4637);
#3895=IFCPCURVE(#5335,#4638);
#3896=IFCPCURVE(#5335,#4639);
#3897=IFCPCURVE(#5335,#4640);
#3898=IFCPCURVE(#5335,#4641);
#3899=IFCPCURVE(#5335,#4642);
#3900=IFCPCURVE(#5335,#4643);
#3901=IFCPCURVE(#5335,#4644);
#3902=IFCPCURVE(#5335,#4645);
#3903=IFCPCURVE(#5335,#4646);
#3904=IFCPCURVE(#5335,#4647);
#3905=IFCPCURVE(#5335,#4648);
#3906=IFCPCURVE(#5335,#4649);
#3907=IFCPCURVE(#5335,#4650);
#3908=IFCPCURVE(#5335,#4651);
#3909=IFCPCURVE(#5335,#4652);
#3910=IFCPCURVE(#5335,#4653);
#3911=IFCPCURVE(#5335,#4654);
#3912=IFCPCURVE(#5335,#4655);
#3913=IFCPCURVE(#5335,#4656);
#3914=IFCPCURVE(#5335,#4657);
#3915=IFCPCURVE(#5335,#4658);
#3916=IFCPCURVE(#5335,#4659);
#3917=IFCPCURVE(#5335,#4660);
#3918=IFCPCURVE(#5335,#4661);
#3919=IFCPCURVE(#5335,#4662);
#3920=IFCPCURVE(#5335,#4663);
#3921=IFCPCURVE(#5335,#4664);
#3922=IFCPCURVE(#5335,#4665);
#3923=IFCPCURVE(#5335,#4666);
#3924=IFCPCURVE(#5335,#4667);
#3925=IFCPCURVE(#5335,#4668);
#3926=IFCPCURVE(#5335,#4669);
#3927=IFCPCURVE(#5335,#4670);
#3928=IFCPCURVE(#5335,#4671);
#3929=IFCPCURVE(#5335,#4672);
#3930=IFCPCURVE(#5335,#4673);
#3931=IFCPCURVE(#5335,#4674);
#3932=IFCPCURVE(#5335,#4675);
#3933=IFCPCURVE(#5335,#4676);
#3934=IFCPCURVE(#5335,#4677);
#3935=IFCPCURVE(#5335,#4678);
#3936=IFCPCURVE(#5335,#4679);
#3937=IFCPCURVE(#5335,#4680);
#3938=IFCPCURVE(#5335,#4681);
#3939=IFCPCURVE(#5335,#4682);
#3940=IFCPCURVE(#5335,#4683);
#3941=IFCPCURVE(#5335,#4684);
#3942=IFCPCURVE(#5335,#4685);
#3943=IFCPCURVE(#5335,#4686);
#3944=IFCPCURVE(#5335,#4687);
#3945=IFCPCURVE(#5335,#4688);
#3946=IFCPCURVE(#5335,#4689);
#3947=IFCPCURVE(#5335,#4690);
#3948=IFCPCURVE(#5335,#4691);
#3949=IFCPCURVE(#5335,#4692);
#3950=IFCPCURVE(#5335,#4693);
#3951=IFCPCURVE(#5335,#4694);
#3952=IFCPCURVE(#5335,#4695);
#3953=IFCPCURVE(#5335,#4696);
#3954=IFCPCURVE(#5335,#4697);
#3955=IFCPCURVE(#5335,#4698);
#3956=IFCPCURVE(#5335,#4699);
#3957=IFCPCURVE(#5335,#4700);
#3958=IFCPCURVE(#5335,#4701);
#3959=IFCPCURVE(#5335,#4702);
#3960=IFCPCURVE(#5335,#4703);
#3961=IFCPCURVE(#5335,#4704);
#3962=IFCPCURVE(#5335,#4705);
#3963=IFCPCURVE(#5335,#4706);
#3964=IFCPCURVE(#5335,#4707);
#3965=IFCPCURVE(#5335,#4708);
#3966=IFCPCURVE(#5335,#4709);
#3967=IFCPCURVE(#5335,#4710);
#3968=IFCPCURVE(#5335,#4711);
#3969=IFCPCURVE(#5335,#4712);
#3970=IFCPCURVE(#5335,#4713);
#3971=IFCPCURVE(#5335,#4714);
#3972=IFCPCURVE(#5335,#4715);
#3973=IFCPCURVE(#5335,#4716);
#3974=IFCPCURVE(#5335,#4717);
#3975=IFCPCURVE(#5335,#4718);
#3976=IFCPCURVE(#5335,#4719);
#3977=IFCPCURVE(#5335,#4720);
#3978=IFCPCURVE(#5335,#4721);
#3979=IFCPCURVE(#5335,#4722);
#3980=IFCPCURVE(#5335,#4723);
#3981=IFCPCURVE(#5335,#4724);
#3982=IFCPCURVE(#5335,#4725);
#3983=IFCPCURVE(#5335,#4726);
#3984=IFCPCURVE(#5335,#4727);
#3985=IFCPCURVE(#5335,#4728);
#3986=IFCPCURVE(#5335,#4729);
#3987=IFCPCURVE(#5335,#4730);
#3988=IFCPCURVE(#5335,#4731);
#3989=IFCPCURVE(#5335,#4732);
#3990=IFCPCURVE(#5335,#4733);
#3991=IFCPCURVE(#5335,#4734);
#3992=IFCPCURVE(#5335,#4735);
#3993=IFCPCURVE(#5335,#4736);
#3994=IFCPCURVE(#5335,#4737);
#3995=IFCPCURVE(#5335,#4738);
#3996=IFCPCURVE(#5335,#4739);
#3997=IFCPCURVE(#5335,#4740);
#3998=IFCPCURVE(#5335,#4741);
#3999=IFCPCURVE(#5335,#4742);
#4000=IFCPCURVE(#5335,#4743);
#4001=IFCPCURVE(#5335,#4744);
#4002=IFCPCURVE(#5335,#4745);
#4003=IFCPCURVE(#5335,#4746);
#4004=IFCPCURVE(#5335,#4747);
#4005=IFCPCURVE(#5335,#4748);
#4006=IFCPCURVE(#5335,#4749);
#4007=IFCPCURVE(#5335,#4750);
#4008=IFCPCURVE(#5335,#4751);
#4009=IFCPCURVE(#5335,#4752);
#4010=IFCPCURVE(#5335,#4753);
#4011=IFCPCURVE(#5335,#4754);
#4012=IFCPCURVE(#5335,#4755);
#4013=IFCPCURVE(#5335,#4756);
#4014=IFCPCURVE(#5335,#4757);
#4015=IFCPCURVE(#5335,#4758);
#4016=IFCPCURVE(#5335,#4759);
#4017=IFCPCURVE(#5335,#4760);
#4018=IFCPCURVE(#5335,#4761);
#4019=IFCPCURVE(#5335,#4762);
#4020=IFCPCURVE(#5335,#4763);
#4021=IFCPCURVE(#5335,#4764);
#4022=IFCPCURVE(#5335,#4765);
#4023=IFCPCURVE(#5335,#4766);
#4024=IFCPCURVE(#5335,#4767);
#4025=IFCPCURVE(#5335,#4768);
#4026=IFCPCURVE(#5335,#4769);
#4027=IFCPCURVE(#5335,#4770);
#4028=IFCPCURVE(#5335,#4771);
#4029=IFCPCURVE(#5335,#4772);
#4030=IFCPCURVE(#5335,#4773);
#4031=IFCPCURVE(#5335,#4774);
#4032=IFCPCURVE(#5335,#4775);
#4033=IFCPCURVE(#5335,#4776);
#4034=IFCPCURVE(#5335,#4777);
#4035=IFCPCURVE(#5335,#4778);
#4036=IFCPCURVE(#5335,#4779);
#4037=IFCPCURVE(#5335,#4780);
#4038=IFCPCURVE(#5335,#4781);
#4039=IFCPCURVE(#5335,#4782);
#4040=IFCPCURVE(#5335,#4783);
#4041=IFCPCURVE(#5335,#4784);
#4042=IFCPCURVE(#5335,#4785);
#4043=IFCPCURVE(#5335,#4786);
#4044=IFCPCURVE(#5335,#4787);
#4045=IFCPCURVE(#5335,#4788);
#4046=IFCPCURVE(#5335,#4789);
#4047=IFCPCURVE(#5335,#4790);
#4048=IFCPCURVE(#5335,#4791);
#4049=IFCPCURVE(#5335,#4792);
#4050=IFCPCURVE(#5335,#4793);
#4051=IFCPCURVE(#5335,#4794);
#4052=IFCPCURVE(#5335,#4795);
#4053=IFCPCURVE(#5335,#4796);
#4054=IFCPCURVE(#5335,#4797);
#4055=IFCPCURVE(#5335,#4798);
#4056=IFCPCURVE(#5335,#4799);
#4057=IFCPCURVE(#5335,#4800);
#4058=IFCPCURVE(#5335,#4801);
#4059=IFCPCURVE(#5335,#4802);
#4060=IFCPCURVE(#5335,#4803);
#4061=IFCPCURVE(#5335,#4804);
#4062=IFCPCURVE(#5335,#4805);
#4063=IFCPCURVE(#5335,#4806);
#4064=IFCPCURVE(#5335,#4807);
#4065=IFCPCURVE(#5335,#4808);
#4066=IFCPCURVE(#5335,#4809);
#4067=IFCPCURVE(#5335,#4810);
#4068=IFCPCURVE(#5335,#4811);
#4069=IFCPCURVE(#5335,#4812);
#4070=IFCPCURVE(#5335,#4813);
#4071=IFCPCURVE(#5335,#4814);
#4072=IFCPCURVE(#5335,#4815);
#4073=IFCPCURVE(#5335,#4816);
#4074=IFCPCURVE(#5335,#4817);
#4075=IFCPCURVE(#5335,#4818);
#4076=IFCPCURVE(#5335,#4819);
#4077=IFCPCURVE(#5335,#4820);
#4078=IFCPCURVE(#5335,#4821);
#4079=IFCPCURVE(#5335,#4822);
#4080=IFCPCURVE(#5335,#4823);
#4081=IFCPCURVE(#5335,#4824);
#4082=IFCPCURVE(#5335,#4825);
#4083=IFCPCURVE(#5335,#4826);
#4084=IFCPCURVE(#5335,#4827);
#4085=IFCPCURVE(#5335,#4828);
#4086=IFCPCURVE(#5335,#4829);
#4087=IFCPCURVE(#5335,#4830);
#4088=IFCPCURVE(#5335,#4831);
#4089=IFCPCURVE(#5335,#4832);
#4090=IFCPCURVE(#5335,#4833);
#4091=IFCPCURVE(#5335,#4834);
#4092=IFCPCURVE(#5335,#4835);
#4093=IFCPCURVE(#5335,#4836);
#4094=IFCPCURVE(#5335,#4837);
#4095=IFCPCURVE(#5335,#4838);
#4096=IFCPCURVE(#5335,#4839);
#4097=IFCPCURVE(#5335,#4840);
#4098=IFCPCURVE(#5335,#4841);
#4099=IFCPCURVE(#5335,#4842);
#4100=IFCPCURVE(#5335,#4843);
#4101=IFCPCURVE(#5335,#4844);
#4102=IFCPCURVE(#5335,#4845);
#4103=IFCPCURVE(#5335,#4846);
#4104=IFCPCURVE(#5335,#4847);
#4105=IFCPCURVE(#5335,#4848);
#4106=IFCPCURVE(#5335,#4849);
#4107=IFCPCURVE(#5335,#4850);
#4108=IFCPCURVE(#5335,#4851);
#4109=IFCPCURVE(#5335,#4852);
#4110=IFCPCURVE(#5335,#4853);
#4111=IFCPCURVE(#5335,#4854);
#4112=IFCPCURVE(#5335,#4855);
#4113=IFCPCURVE(#5335,#4856);
#4114=IFCPCURVE(#5335,#4857);
#4115=IFCPCURVE(#5335,#4858);
#4116=IFCPCURVE(#5335,#4859);
#4117=IFCPCURVE(#5335,#4860);
#4118=IFCPCURVE(#5335,#4861);
#4119=IFCPCURVE(#5335,#4862);
#4120=IFCPCURVE(#5335,#4863);
#4121=IFCPCURVE(#5335,#4864);
#4122=IFCPCURVE(#5335,#4865);
#4123=IFCPCURVE(#5335,#4866);
#4124=IFCPCURVE(#5335,#4867);
#4125=IFCPCURVE(#5335,#4868);
#4126=IFCPCURVE(#5335,#4869);
#4127=IFCPCURVE(#5335,#4870);
#4128=IFCPCURVE(#5335,#4871);
#4129=IFCPCURVE(#5335,#4872);
#4130=IFCPCURVE(#5335,#4873);
#4131=IFCPCURVE(#5335,#4874);
#4132=IFCPCURVE(#5335,#4875);
#4133=IFCPCURVE(#5335,#4876);
#4134=IFCPCURVE(#5335,#4877);
#4135=IFCPCURVE(#5335,#4878);
#4136=IFCPCURVE(#5335,#4879);
#4137=IFCPCURVE(#5335,#4880);
#4138=IFCPCURVE(#5335,#4881);
#4139=IFCPCURVE(#5335,#4882);
#4140=IFCPCURVE(#5335,#4883);
#4141=IFCPCURVE(#5335,#4884);
#4142=IFCPCURVE(#5335,#4885);
#4143=IFCPCURVE(#5335,#4886);
#4144=IFCPCURVE(#5335,#4887);
#4145=IFCPCURVE(#5335,#4888);
#4146=IFCPCURVE(#5335,#4889);
#4147=IFCPCURVE(#5335,#4890);
#4148=IFCPCURVE(#5335,#4891);
#4149=IFCPCURVE(#5335,#4892);
#4150=IFCPCURVE(#5335,#4893);
#4151=IFCPCURVE(#5335,#4894);
#4152=IFCPCURVE(#5335,#4895);
#4153=IFCPCURVE(#5335,#4896);
#4154=IFCPCURVE(#5335,#4897);
#4155=IFCPCURVE(#5335,#4898);
#4156=IFCPCURVE(#5335,#4899);
#4157=IFCPCURVE(#5335,#4900);
#4158=IFCPCURVE(#5335,#4901);
#4159=IFCPCURVE(#5335,#4902);
#4160=IFCPCURVE(#5335,#4903);
#4161=IFCPCURVE(#5335,#4904);
#4162=IFCPCURVE(#5335,#4905);
#4163=IFCPCURVE(#5335,#4906);
#4164=IFCPCURVE(#5335,#4907);
#4165=IFCPCURVE(#5335,#4908);
#4166=IFCPCURVE(#5335,#4909);
#4167=IFCPCURVE(#5335,#4910);
#4168=IFCPCURVE(#5335,#4911);
#4169=IFCPCURVE(#5335,#4912);
#4170=IFCPCURVE(#5335,#4913);
#4171=IFCPCURVE(#5335,#4914);
#4172=IFCPCURVE(#5335,#4915);
#4173=IFCPCURVE(#5335,#4916);
#4174=IFCPCURVE(#5335,#4917);
#4175=IFCPCURVE(#5335,#4918);
#4176=IFCPCURVE(#5335,#4919);
#4177=IFCPCURVE(#5335,#4920);
#4178=IFCPCURVE(#5335,#4921);
#4179=IFCPCURVE(#5335,#4922);
#4180=IFCPCURVE(#5335,#4923);
#4181=IFCPCURVE(#5335,#4924);
#4182=IFCPCURVE(#5335,#4925);
#4183=IFCPCURVE(#5335,#4926);
#4184=IFCPCURVE(#5335,#4927);
#4185=IFCPCURVE(#5335,#4928);
#4186=IFCPCURVE(#5335,#4929);
#4187=IFCPCURVE(#5335,#4930);
#4188=IFCPCURVE(#5335,#4931);
#4189=IFCPCURVE(#5335,#4932);
#4190=IFCPCURVE(#5335,#4933);
#4191=IFCPCURVE(#5335,#4934);
#4192=IFCPCURVE(#5335,#4935);
#4193=IFCPCURVE(#5335,#4936);
#4194=IFCPCURVE(#5335,#4937);
#4195=IFCPCURVE(#5335,#4938);
#4196=IFCPCURVE(#5335,#4939);
#4197=IFCPCURVE(#5335,#4940);
#4198=IFCPCURVE(#5335,#4941);
#4199=IFCPCURVE(#5335,#4942);
#4200=IFCPCURVE(#5335,#4943);
#4201=IFCPCURVE(#5335,#4944);
#4202=IFCPCURVE(#5335,#4945);
#4203=IFCPCURVE(#5335,#4946);
#4204=IFCPCURVE(#5335,#4947);
#4205=IFCPCURVE(#5335,#4948);
#4206=IFCPCURVE(#5335,#4949);
#4207=IFCPCURVE(#5335,#4950);
#4208=IFCPCURVE(#5335,#4951);
#4209=IFCPCURVE(#5335,#4952);
#4210=IFCPCURVE(#5335,#4953);
#4211=IFCPCURVE(#5335,#4954);
#4212=IFCPCURVE(#5335,#4955);
#4213=IFCPCURVE(#5335,#4956);
#4214=IFCPCURVE(#5335,#4957);
#4215=IFCPCURVE(#5335,#4958);
#4216=IFCPCURVE(#5335,#4959);
#4217=IFCPCURVE(#5335,#4960);
#4218=IFCPCURVE(#5335,#4961);
#4219=IFCPCURVE(#5335,#4962);
#4220=IFCPCURVE(#5335,#4963);
#4221=IFCPCURVE(#5335,#4964);
#4222=IFCPCURVE(#5335,#4965);
#4223=IFCPCURVE(#5335,#4966);
#4224=IFCPCURVE(#5335,#4967);
#4225=IFCPCURVE(#5335,#4968);
#4226=IFCPCURVE(#5335,#4969);
#4227=IFCPCURVE(#5335,#4970);
#4228=IFCPCURVE(#5335,#4971);
#4229=IFCPCURVE(#5335,#4972);
#4230=IFCPCURVE(#5335,#4973);
#4231=IFCPCURVE(#5335,#4974);
#4232=IFCPCURVE(#5335,#4975);
#4233=IFCPCURVE(#5335,#4976);
#4234=IFCPCURVE(#5335,#4977);
#4235=IFCPCURVE(#5335,#4978);
#4236=IFCPCURVE(#5335,#4979);
#4237=IFCPCURVE(#5335,#4980);
#4238=IFCPCURVE(#5335,#4981);
#4239=IFCPCURVE(#5335,#4982);
#4240=IFCPCURVE(#5335,#4983);
#4241=IFCPCURVE(#5335,#4984);
#4242=IFCPCURVE(#5335,#4985);
#4243=IFCPCURVE(#5335,#4986);
#4244=IFCPCURVE(#5335,#4987);
#4245=IFCPCURVE(#5335,#4988);
#4246=IFCPCURVE(#5335,#4989);
#4247=IFCPCURVE(#5335,#4990);
#4248=IFCPCURVE(#5335,#4991);
#4249=IFCPCURVE(#5335,#4992);
#4250=IFCPCURVE(#5335,#4993);
#4251=IFCPCURVE(#5335,#4994);
#4252=IFCPCURVE(#5335,#4995);
#4253=IFCPCURVE(#5335,#4996);
#4254=IFCPCURVE(#5335,#4997);
#4255=IFCPCURVE(#5335,#4998);
#4256=IFCPCURVE(#5335,#4999);
#4257=IFCPCURVE(#5335,#5000);
#4258=IFCPCURVE(#5335,#5001);
#4259=IFCPCURVE(#5335,#5002);
#4260=IFCPCURVE(#5335,#5003);
#4261=IFCPCURVE(#5335,#5004);
#4262=IFCPCURVE(#5335,#5005);
#4263=IFCPCURVE(#5335,#5006);
#4264=IFCPCURVE(#5335,#5007);
#4265=IFCPCURVE(#5335,#5008);
#4266=IFCPCURVE(#5335,#5009);
#4267=IFCPCURVE(#5335,#5010);
#4268=IFCPCURVE(#5335,#5011);
#4269=IFCPCURVE(#5335,#5012);
#4270=IFCPCURVE(#5335,#5013);
#4271=IFCPCURVE(#5335,#5014);
#4272=IFCPCURVE(#5335,#5015);
#4273=IFCPCURVE(#5335,#5016);
#4274=IFCPCURVE(#5335,#5017);
#4275=IFCPCURVE(#5335,#5018);
#4276=IFCPCURVE(#5335,#5019);
#4277=IFCPCURVE(#5335,#5020);
#4278=IFCPCURVE(#5335,#5021);
#4279=IFCPCURVE(#5335,#5022);
#4280=IFCPCURVE(#5335,#5023);
#4281=IFCPCURVE(#5335,#5024);
#4282=IFCPCURVE(#5335,#5025);
#4283=IFCPCURVE(#5335,#5026);
#4284=IFCPCURVE(#5335,#5027);
#4285=IFCPCURVE(#5335,#5028);
#4286=IFCPCURVE(#5335,#5029);
#4287=IFCPCURVE(#5335,#5030);
#4288=IFCPCURVE(#5335,#5031);
#4289=IFCPCURVE(#5335,#5032);
#4290=IFCPCURVE(#5335,#5033);
#4291=IFCPCURVE(#5335,#5034);
#4292=IFCPCURVE(#5335,#5035);
#4293=IFCPCURVE(#5335,#5036);
#4294=IFCPCURVE(#5335,#5037);
#4295=IFCPCURVE(#5335,#5038);
#4296=IFCPCURVE(#5335,#5039);
#4297=IFCPCURVE(#5335,#5040);
#4298=IFCPCURVE(#5335,#5041);
#4299=IFCPCURVE(#5335,#5042);
#4300=IFCPCURVE(#5335,#5043);
#4301=IFCPCURVE(#5335,#5044);
#4302=IFCPCURVE(#5335,#5045);
#4303=IFCPCURVE(#5335,#5046);
#4304=IFCPCURVE(#5335,#5047);
#4305=IFCPCURVE(#5335,#5048);
#4306=IFCPCURVE(#5335,#5049);
#4307=IFCPCURVE(#5335,#5050);
#4308=IFCPCURVE(#5335,#5051);
#4309=IFCPCURVE(#5335,#5052);
#4310=IFCPCURVE(#5335,#5053);
#4311=IFCPCURVE(#5335,#5054);
#4312=IFCPCURVE(#5335,#5055);
#4313=IFCPCURVE(#5335,#5056);
#4314=IFCPCURVE(#5335,#5057);
#4315=IFCPCURVE(#5335,#5058);
#4316=IFCPCURVE(#5335,#5059);
#4317=IFCPCURVE(#5335,#5060);
#4318=IFCPCURVE(#5335,#5061);
#4319=IFCPCURVE(#5335,#5062);
#4320=IFCPCURVE(#5335,#5063);
#4321=IFCPCURVE(#5335,#5064);
#4322=IFCPCURVE(#5335,#5065);
#4323=IFCPCURVE(#5335,#5066);
#4324=IFCPCURVE(#5335,#5067);
#4325=IFCPCURVE(#5335,#5068);
#4326=IFCPCURVE(#5335,#5069);
#4327=IFCPCURVE(#5335,#5070);
#4328=IFCPCURVE(#5335,#5071);
#4329=IFCPCURVE(#5335,#5072);
#4330=IFCPCURVE(#5335,#5073);
#4331=IFCPCURVE(#5335,#5074);
#4332=IFCPCURVE(#5335,#5075);
#4333=IFCPCURVE(#5335,#5076);
#4334=IFCPCURVE(#5335,#5077);
#4335=IFCPCURVE(#5335,#5078);
#4336=IFCPCURVE(#5335,#5079);
#4337=IFCPCURVE(#5335,#5080);
#4338=IFCPCURVE(#5335,#5081);
#4339=IFCPCURVE(#5335,#5082);
#4340=IFCPCURVE(#5335,#5083);
#4341=IFCPCURVE(#5335,#5084);
#4342=IFCPCURVE(#5335,#5085);
#4343=IFCPCURVE(#5335,#5086);
#4344=IFCPCURVE(#5335,#5087);
#4345=IFCPCURVE(#5335,#5088);
#4346=IFCPCURVE(#5335,#5089);
#4347=IFCPCURVE(#5335,#5090);
#4348=IFCPCURVE(#5335,#5091);
#4349=IFCPCURVE(#5335,#5092);
#4350=IFCPCURVE(#5335,#5093);
#4351=IFCPCURVE(#5335,#5094);
#4352=IFCPCURVE(#5335,#5095);
#4353=IFCPCURVE(#5335,#5096);
#4354=IFCPCURVE(#5335,#5097);
#4355=IFCPCURVE(#5335,#5098);
#4356=IFCPCURVE(#5335,#5099);
#4357=IFCPCURVE(#5335,#5100);
#4358=IFCPCURVE(#5335,#5101);
#4359=IFCPCURVE(#5335,#5102);
#4360=IFCPCURVE(#5335,#5103);
#4361=IFCPCURVE(#5335,#5104);
#4362=IFCPCURVE(#5335,#5105);
#4363=IFCPCURVE(#5335,#5106);
#4364=IFCPCURVE(#5335,#5107);
#4365=IFCPCURVE(#5335,#5108);
#4366=IFCPCURVE(#5335,#5109);
#4367=IFCPCURVE(#5335,#5110);
#4368=IFCPCURVE(#5335,#5111);
#4369=IFCPCURVE(#5335,#5112);
#4370=IFCPCURVE(#5335,#5113);
#4371=IFCPCURVE(#5335,#5114);
#4372=IFCPCURVE(#5335,#5115);
#4373=IFCPCURVE(#5335,#5116);
#4374=IFCPCURVE(#5335,#5117);
#4375=IFCPCURVE(#5335,#5118);
#4376=IFCPCURVE(#5335,#5119);
#4377=IFCPCURVE(#5335,#5120);
#4378=IFCPCURVE(#5335,#5121);
#4379=IFCPCURVE(#5335,#5122);
#4380=IFCPCURVE(#5335,#5123);
#4381=IFCPCURVE(#5335,#5124);
#4382=IFCPCURVE(#5335,#5125);
#4383=IFCPCURVE(#5335,#5126);
#4384=IFCPCURVE(#5335,#5127);
#4385=IFCPCURVE(#5335,#5128);
#4386=IFCPCURVE(#5335,#5129);
#4387=IFCPCURVE(#5335,#5130);
#4388=IFCPCURVE(#5335,#5131);
#4389=IFCPCURVE(#5335,#5132);
#4390=IFCPCURVE(#5335,#5133);
#4391=IFCPCURVE(#5335,#5134);
#4392=IFCPCURVE(#5335,#5135);
#4393=IFCPCURVE(#5335,#5136);
#4394=IFCPCURVE(#5335,#5137);
#4395=IFCPCURVE(#5335,#5138);
#4396=IFCPCURVE(#5335,#5139);
#4397=IFCPCURVE(#5335,#5140);
#4398=IFCPCURVE(#5335,#5141);
#4399=IFCPCURVE(#5335,#5142);
#4400=IFCPCURVE(#5335,#5143);
#4401=IFCPCURVE(#5335,#5144);
#4402=IFCPCURVE(#5335,#5145);
#4403=IFCPCURVE(#5335,#5146);
#4404=IFCPCURVE(#5335,#5147);
#4405=IFCPCURVE(#5335,#5148);
#4406=IFCPCURVE(#5335,#5149);
#4407=IFCPCURVE(#5335,#5150);
#4408=IFCPCURVE(#5335,#5151);
#4409=IFCPCURVE(#5335,#5152);
#4410=IFCPCURVE(#5335,#5153);
#4411=IFCPCURVE(#5335,#5154);
#4412=IFCPCURVE(#5335,#5155);
#4413=IFCPCURVE(#5335,#5156);
#4414=IFCPCURVE(#5335,#5157);
#4415=IFCPCURVE(#5335,#5158);
#4416=IFCPCURVE(#5335,#5159);
#4417=IFCPCURVE(#5335,#5160);
#4418=IFCPCURVE(#5335,#5161);
#4419=IFCPCURVE(#5335,#5162);
#4420=IFCPCURVE(#5335,#5163);
#4421=IFCPCURVE(#5335,#5164);
#4422=IFCPCURVE(#5335,#5165);
#4423=IFCPCURVE(#5335,#5166);
#4424=IFCPCURVE(#5335,#5167);
#4425=IFCPCURVE(#5335,#5168);
#4426=IFCPCURVE(#5335,#5169);
#4427=IFCPCURVE(#5335,#5170);
#4428=IFCPCURVE(#5335,#5171);
#4429=IFCPCURVE(#5335,#5172);
#4430=IFCPCURVE(#5335,#5173);
#4431=IFCPCURVE(#5335,#5174);
#4432=IFCPCURVE(#5335,#5175);
#4433=IFCPCURVE(#5335,#5176);
#4434=IFCPCURVE(#5335,#5177);
#4435=IFCPCURVE(#5335,#5178);
#4436=IFCPCURVE(#5335,#5179);
#4437=IFCPCURVE(#5335,#5180);
#4438=IFCPCURVE(#5335,#5181);
#4439=IFCPCURVE(#5335,#5182);
#4440=IFCPCURVE(#5335,#5183);
#4441=IFCPCURVE(#5335,#5184);
#4442=IFCPCURVE(#5336,#5185);
#4443=IFCPCURVE(#5336,#5186);
#4444=IFCPCURVE(#5336,#5187);
#4445=IFCPCURVE(#5336,#5188);
#4446=IFCPCURVE(#5337,#5189);
#4447=IFCPCURVE(#5337,#5190);
#4448=IFCPCURVE(#5337,#5191);
#4449=IFCPCURVE(#5337,#5192);
#4450=IFCPCURVE(#5337,#5193);
#4451=IFCPCURVE(#5337,#5194);
#4452=IFCPCURVE(#5337,#5195);
#4453=IFCPCURVE(#5337,#5196);
#4454=IFCPCURVE(#5337,#5197);
#4455=IFCPCURVE(#5337,#5198);
#4456=IFCPCURVE(#5337,#5199);
#4457=IFCPCURVE(#5337,#5200);
#4458=IFCPCURVE(#5337,#5201);
#4459=IFCPCURVE(#5337,#5202);
#4460=IFCPCURVE(#5337,#5203);
#4461=IFCPCURVE(#5337,#5204);
#4462=IFCPCURVE(#5337,#5205);
#4463=IFCPCURVE(#5337,#5206);
#4464=IFCPCURVE(#5337,#5207);
#4465=IFCPCURVE(#5337,#5208);
#4466=IFCPCURVE(#5337,#5209);
#4467=IFCPCURVE(#5337,#5210);
#4468=IFCPCURVE(#5337,#5211);
#4469=IFCPCURVE(#5337,#5212);
#4470=IFCPCURVE(#5337,#5213);
#4471=IFCPCURVE(#5337,#5214);
#4472=IFCPCURVE(#5337,#5215);
#4473=IFCPCURVE(#5337,#5216);
#4474=IFCPCURVE(#5337,#5217);
#4475=IFCPCURVE(#5337,#5218);
#4476=IFCPCURVE(#5337,#5219);
#4477=IFCPCURVE(#5337,#5220);
#4478=IFCPCURVE(#5337,#5221);
#4479=IFCPCURVE(#5337,#5222);
#4480=IFCPCURVE(#5337,#5223);
#4481=IFCPCURVE(#5337,#5224);
#4482=IFCPCURVE(#5337,#5225);
#4483=IFCPCURVE(#5337,#5226);
#4484=IFCPCURVE(#5337,#5227);
#4485=IFCPCURVE(#5337,#5228);
#4486=IFCPCURVE(#5337,#5229);
#4487=IFCPCURVE(#5337,#5230);
#4488=IFCPCURVE(#5337,#5231);
#4489=IFCPCURVE(#5337,#5232);
#4490=IFCPCURVE(#5337,#5233);
#4491=IFCPCURVE(#5337,#5234);
#4492=IFCPCURVE(#5337,#5235);
#4493=IFCPCURVE(#5337,#5236);
#4494=IFCPCURVE(#5337,#5237);
#4495=IFCPCURVE(#5337,#5238);
#4496=IFCPCURVE(#5337,#5239);
#4497=IFCPCURVE(#5337,#5240);
#4498=IFCPCURVE(#5337,#5241);
#4499=IFCPCURVE(#5337,#5242);
#4500=IFCPCURVE(#5337,#5243);
#4501=IFCPCURVE(#5337,#5244);
#4502=IFCPCURVE(#5337,#5245);
#4503=IFCPCURVE(#5337,#5246);
#4504=IFCPCURVE(#5337,#5247);
#4505=IFCPCURVE(#5337,#5248);
#4506=IFCPCURVE(#5337,#5249);
#4507=IFCPCURVE(#5337,#5250);
#4508=IFCPCURVE(#5337,#5251);
#4509=IFCPCURVE(#5337,#5252);
#4510=IFCPCURVE(#5337,#5253);
#4511=IFCPCURVE(#5337,#5254);
#4512=IFCPCURVE(#5337,#5255);
#4513=IFCPCURVE(#5337,#5256);
#4514=IFCPCURVE(#5337,#5257);
#4515=IFCPCURVE(#5337,#5258);
#4516=IFCPCURVE(#5337,#5259);
#4517=IFCPCURVE(#5337,#5260);
#4518=IFCPCURVE(#5337,#5261);
#4519=IFCPCURVE(#5337,#5262);
#4520=IFCPCURVE(#5337,#5263);
#4521=IFCPCURVE(#5337,#5264);
#4522=IFCPCURVE(#5337,#5265);
#4523=IFCPCURVE(#5337,#5266);
#4524=IFCPCURVE(#5337,#5267);
#4525=IFCPCURVE(#5337,#5268);
#4526=IFCPCURVE(#5337,#5269);
#4527=IFCPCURVE(#5337,#5270);
#4528=IFCPCURVE(#5337,#5271);
#4529=IFCPCURVE(#5337,#5272);
#4530=IFCPCURVE(#5337,#5273);
#4531=IFCPCURVE(#5337,#5274);
#4532=IFCPCURVE(#5337,#5275);
#4533=IFCPCURVE(#5337,#5276);
#4534=IFCPCURVE(#5338,#5277);
#4535=IFCPCURVE(#5338,#5278);
#4536=IFCPCURVE(#5338,#5279);
#4537=IFCPCURVE(#5338,#5280);
#4538=IFCPCURVE(#5338,#5281);
#4539=IFCPCURVE(#5338,#5282);
#4540=IFCPCURVE(#5338,#5283);
#4541=IFCPCURVE(#5338,#5284);
#4542=IFCPCURVE(#5338,#5285);
#4543=IFCPCURVE(#5338,#5286);
#4544=IFCPCURVE(#5338,#5287);
#4545=IFCPCURVE(#5338,#5288);
#4546=IFCPCURVE(#5338,#5289);
#4547=IFCPCURVE(#5338,#5290);
#4548=IFCPCURVE(#5338,#5291);
#4549=IFCPCURVE(#5338,#5292);
#4550=IFCPCURVE(#5338,#5293);
#4551=IFCPCURVE(#5338,#5294);
#4552=IFCPCURVE(#5338,#5295);
#4553=IFCPCURVE(#5338,#5296);
#4554=IFCPCURVE(#5338,#5297);
#4555=IFCPCURVE(#5338,#5298);
#4556=IFCPCURVE(#5338,#5299);
#4557=IFCPCURVE(#5338,#5300);
#4558=IFCPCURVE(#5338,#5301);
#4559=IFCPCURVE(#5338,#5302);
#4560=IFCPCURVE(#5338,#5303);
#4561=IFCPCURVE(#5338,#5304);
#4562=IFCPCURVE(#5338,#5305);
#4563=IFCPCURVE(#5338,#5306);
#4564=IFCPCURVE(#5338,#5307);
#4565=IFCPCURVE(#5338,#5308);
#4566=IFCPCURVE(#50,#5309);
#4567=IFCPCURVE(#50,#5310);
#4568=IFCPCURVE(#50,#5311);
#4569=IFCPCURVE(#50,#5312);
#4570=IFCPOLYLINE((#5374,#5375));
#4571=IFCPOLYLINE((#5375,#5378));
#4572=IFCPOLYLINE((#5378,#5380));
#4573=IFCPOLYLINE((#5380,#5382));
#4574=IFCPOLYLINE((#5382,#5384));
#4575=IFCPOLYLINE((#5384,#5386));
#4576=IFCPOLYLINE((#5386,#5388));
#4577=IFCPOLYLINE((#5388,#5390));
#4578=IFCPOLYLINE((#5390,#5392));
#4579=IFCPOLYLINE((#5392,#5394));
#4580=IFCPOLYLINE((#5394,#5396));
#4581=IFCPOLYLINE((#5396,#5398));
#4582=IFCPOLYLINE((#5398,#5400));
#4583=IFCPOLYLINE((#5400,#5402));
#4584=IFCPOLYLINE((#5402,#5404));
#4585=IFCPOLYLINE((#5404,#5406));
#4586=IFCPOLYLINE((#5406,#5408));
#4587=IFCPOLYLINE((#5408,#5410));
#4588=IFCPOLYLINE((#5410,#5412));
#4589=IFCPOLYLINE((#5412,#5414));
#4590=IFCPOLYLINE((#5414,#5416));
#4591=IFCPOLYLINE((#5416,#5418));
#4592=IFCPOLYLINE((#5418,#5420));
#4593=IFCPOLYLINE((#5420,#5374));
#4594=IFCPOLYLINE((#5441,#5442));
#4595=IFCPOLYLINE((#5442,#5443));
#4596=IFCPOLYLINE((#5443,#5444));
#4597=IFCPOLYLINE((#5444,#5441));
#4598=IFCPOLYLINE((#5441,#5442));
#4599=IFCPOLYLINE((#5442,#5443));
#4600=IFCPOLYLINE((#5443,#5444));
#4601=IFCPOLYLINE((#5444,#5441));
#4602=IFCPOLYLINE((#5441,#5442));
#4603=IFCPOLYLINE((#5442,#5443));
#4604=IFCPOLYLINE((#5443,#5444));
#4605=IFCPOLYLINE((#5444,#5441));
#4606=IFCPOLYLINE((#5441,#5442));
#4607=IFCPOLYLINE((#5442,#5443));
#4608=IFCPOLYLINE((#5443,#5444));
#4609=IFCPOLYLINE((#5444,#5441));
#4610=IFCPOLYLINE((#5441,#5441));
#4611=IFCPOLYLINE((#5441,#5789));
#4612=IFCPOLYLINE((#5789,#5789));
#4613=IFCPOLYLINE((#5789,#5792));
#4614=IFCPOLYLINE((#5792,#5792));
#4615=IFCPOLYLINE((#5792,#5795));
#4616=IFCPOLYLINE((#5795,#5795));
#4617=IFCPOLYLINE((#5795,#5798));
#4618=IFCPOLYLINE((#5798,#5798));
#4619=IFCPOLYLINE((#5798,#5801));
#4620=IFCPOLYLINE((#5801,#5801));
#4621=IFCPOLYLINE((#5801,#5804));
#4622=IFCPOLYLINE((#5804,#5804));
#4623=IFCPOLYLINE((#5804,#5807));
#4624=IFCPOLYLINE((#5807,#5807));
#4625=IFCPOLYLINE((#5807,#5810));
#4626=IFCPOLYLINE((#5810,#5810));
#4627=IFCPOLYLINE((#5810,#5813));
#4628=IFCPOLYLINE((#5813,#5813));
#4629=IFCPOLYLINE((#5813,#5816));
#4630=IFCPOLYLINE((#5816,#5816));
#4631=IFCPOLYLINE((#5816,#5819));
#4632=IFCPOLYLINE((#5819,#5819));
#4633=IFCPOLYLINE((#5819,#5822));
#4634=IFCPOLYLINE((#5822,#5824));
#4635=IFCPOLYLINE((#5824,#5824));
#4636=IFCPOLYLINE((#5824,#5827));
#4637=IFCPOLYLINE((#5827,#5827));
#4638=IFCPOLYLINE((#5827,#5830));
#4639=IFCPOLYLINE((#5830,#5830));
#4640=IFCPOLYLINE((#5830,#5833));
#4641=IFCPOLYLINE((#5833,#5833));
#4642=IFCPOLYLINE((#5833,#5836));
#4643=IFCPOLYLINE((#5836,#5836));
#4644=IFCPOLYLINE((#5836,#5839));
#4645=IFCPOLYLINE((#5839,#5839));
#4646=IFCPOLYLINE((#5839,#5842));
#4647=IFCPOLYLINE((#5842,#5842));
#4648=IFCPOLYLINE((#5842,#5845));
#4649=IFCPOLYLINE((#5845,#5845));
#4650=IFCPOLYLINE((#5845,#5848));
#4651=IFCPOLYLINE((#5848,#5848));
#4652=IFCPOLYLINE((#5848,#5851));
#4653=IFCPOLYLINE((#5851,#5851));
#4654=IFCPOLYLINE((#5851,#5414));
#4655=IFCPOLYLINE((#5414,#5414));
#4656=IFCPOLYLINE((#5414,#5414));
#4657=IFCPOLYLINE((#5414,#5414));
#4658=IFCPOLYLINE((#5414,#5858));
#4659=IFCPOLYLINE((#5858,#5858));
#4660=IFCPOLYLINE((#5858,#5861));
#4661=IFCPOLYLINE((#5861,#5861));
#4662=IFCPOLYLINE((#5861,#5864));
#4663=IFCPOLYLINE((#5864,#5864));
#4664=IFCPOLYLINE((#5864,#5867));
#4665=IFCPOLYLINE((#5867,#5867));
#4666=IFCPOLYLINE((#5867,#5870));
#4667=IFCPOLYLINE((#5870,#5870));
#4668=IFCPOLYLINE((#5870,#5873));
#4669=IFCPOLYLINE((#5873,#5873));
#4670=IFCPOLYLINE((#5873,#5876));
#4671=IFCPOLYLINE((#5876,#5876));
#4672=IFCPOLYLINE((#5876,#5879));
#4673=IFCPOLYLINE((#5879,#5879));
#4674=IFCPOLYLINE((#5879,#5882));
#4675=IFCPOLYLINE((#5882,#5882));
#4676=IFCPOLYLINE((#5882,#5885));
#4677=IFCPOLYLINE((#5885,#5416));
#4678=IFCPOLYLINE((#5416,#5416));
#4679=IFCPOLYLINE((#5416,#5416));
#4680=IFCPOLYLINE((#5416,#5416));
#4681=IFCPOLYLINE((#5416,#5891));
#4682=IFCPOLYLINE((#5891,#5891));
#4683=IFCPOLYLINE((#5891,#5894));
#4684=IFCPOLYLINE((#5894,#5894));
#4685=IFCPOLYLINE((#5894,#5897));
#4686=IFCPOLYLINE((#5897,#5897));
#4687=IFCPOLYLINE((#5897,#5900));
#4688=IFCPOLYLINE((#5900,#5900));
#4689=IFCPOLYLINE((#5900,#5903));
#4690=IFCPOLYLINE((#5903,#5903));
#4691=IFCPOLYLINE((#5903,#5906));
#4692=IFCPOLYLINE((#5906,#5906));
#4693=IFCPOLYLINE((#5906,#5909));
#4694=IFCPOLYLINE((#5909,#5909));
#4695=IFCPOLYLINE((#5909,#5912));
#4696=IFCPOLYLINE((#5912,#5912));
#4697=IFCPOLYLINE((#5912,#5915));
#4698=IFCPOLYLINE((#5915,#5915));
#4699=IFCPOLYLINE((#5915,#5918));
#4700=IFCPOLYLINE((#5918,#5918));
#4701=IFCPOLYLINE((#5918,#5418));
#4702=IFCPOLYLINE((#5418,#5418));
#4703=IFCPOLYLINE((#5418,#5418));
#4704=IFCPOLYLINE((#5418,#5924));
#4705=IFCPOLYLINE((#5924,#5924));
#4706=IFCPOLYLINE((#5924,#5927));
#4707=IFCPOLYLINE((#5927,#5927));
#4708=IFCPOLYLINE((#5927,#5930));
#4709=IFCPOLYLINE((#5930,#5930));
#4710=IFCPOLYLINE((#5930,#5933));
#4711=IFCPOLYLINE((#5933,#5933));
#4712=IFCPOLYLINE((#5933,#5936));
#4713=IFCPOLYLINE((#5936,#5936));
#4714=IFCPOLYLINE((#5936,#5939));
#4715=IFCPOLYLINE((#5939,#5939));
#4716=IFCPOLYLINE((#5939,#5942));
#4717=IFCPOLYLINE((#5942,#5942));
#4718=IFCPOLYLINE((#5942,#5945));
#4719=IFCPOLYLINE((#5945,#5945));
#4720=IFCPOLYLINE((#5945,#5948));
#4721=IFCPOLYLINE((#5948,#5948));
#4722=IFCPOLYLINE((#5948,#5951));
#4723=IFCPOLYLINE((#5951,#5951));
#4724=IFCPOLYLINE((#5951,#5420));
#4725=IFCPOLYLINE((#5420,#5420));
#4726=IFCPOLYLINE((#5420,#5420));
#4727=IFCPOLYLINE((#5420,#5420));
#4728=IFCPOLYLINE((#5420,#5958));
#4729=IFCPOLYLINE((#5958,#5958));
#4730=IFCPOLYLINE((#5958,#5961));
#4731=IFCPOLYLINE((#5961,#5961));
#4732=IFCPOLYLINE((#5961,#5964));
#4733=IFCPOLYLINE((#5964,#5964));
#4734=IFCPOLYLINE((#5964,#5967));
#4735=IFCPOLYLINE((#5967,#5967));
#4736=IFCPOLYLINE((#5967,#5970));
#4737=IFCPOLYLINE((#5970,#5970));
#4738=IFCPOLYLINE((#5970,#5973));
#4739=IFCPOLYLINE((#5973,#5973));
#4740=IFCPOLYLINE((#5973,#5976));
#4741=IFCPOLYLINE((#5976,#5976));
#4742=IFCPOLYLINE((#5976,#5979));
#4743=IFCPOLYLINE((#5979,#5979));
#4744=IFCPOLYLINE((#5979,#5982));
#4745=IFCPOLYLINE((#5982,#5982));
#4746=IFCPOLYLINE((#5982,#5985));
#4747=IFCPOLYLINE((#5985,#5374));
#4748=IFCPOLYLINE((#5374,#5374));
#4749=IFCPOLYLINE((#5374,#5374));
#4750=IFCPOLYLINE((#5374,#5374));
#4751=IFCPOLYLINE((#5374,#5991));
#4752=IFCPOLYLINE((#5991,#5991));
#4753=IFCPOLYLINE((#5991,#5994));
#4754=IFCPOLYLINE((#5994,#5994));
#4755=IFCPOLYLINE((#5994,#5997));
#4756=IFCPOLYLINE((#5997,#5997));
#4757=IFCPOLYLINE((#5997,#6000));
#4758=IFCPOLYLINE((#6000,#6000));
#4759=IFCPOLYLINE((#6000,#6003));
#4760=IFCPOLYLINE((#6003,#6003));
#4761=IFCPOLYLINE((#6003,#6006));
#4762=IFCPOLYLINE((#6006,#6006));
#4763=IFCPOLYLINE((#6006,#6009));
#4764=IFCPOLYLINE((#6009,#6009));
#4765=IFCPOLYLINE((#6009,#6012));
#4766=IFCPOLYLINE((#6012,#6012));
#4767=IFCPOLYLINE((#6012,#6015));
#4768=IFCPOLYLINE((#6015,#6015));
#4769=IFCPOLYLINE((#6015,#6018));
#4770=IFCPOLYLINE((#6018,#6018));
#4771=IFCPOLYLINE((#6018,#6021));
#4772=IFCPOLYLINE((#6021,#6021));
#4773=IFCPOLYLINE((#6021,#6024));
#4774=IFCPOLYLINE((#6024,#6026));
#4775=IFCPOLYLINE((#6026,#6026));
#4776=IFCPOLYLINE((#6026,#6029));
#4777=IFCPOLYLINE((#6029,#6029));
#4778=IFCPOLYLINE((#6029,#6032));
#4779=IFCPOLYLINE((#6032,#6032));
#4780=IFCPOLYLINE((#6032,#6035));
#4781=IFCPOLYLINE((#6035,#6035));
#4782=IFCPOLYLINE((#6035,#6038));
#4783=IFCPOLYLINE((#6038,#6038));
#4784=IFCPOLYLINE((#6038,#6041));
#4785=IFCPOLYLINE((#6041,#6041));
#4786=IFCPOLYLINE((#6041,#6044));
#4787=IFCPOLYLINE((#6044,#6044));
#4788=IFCPOLYLINE((#6044,#6047));
#4789=IFCPOLYLINE((#6047,#6047));
#4790=IFCPOLYLINE((#6047,#6050));
#4791=IFCPOLYLINE((#6050,#6050));
#4792=IFCPOLYLINE((#6050,#6053));
#4793=IFCPOLYLINE((#6053,#6053));
#4794=IFCPOLYLINE((#6053,#5442));
#4795=IFCPOLYLINE((#5442,#5442));
#4796=IFCPOLYLINE((#5442,#6057));
#4797=IFCPOLYLINE((#6057,#5382));
#4798=IFCPOLYLINE((#5382,#6060));
#4799=IFCPOLYLINE((#6060,#5443));
#4800=IFCPOLYLINE((#5443,#5443));
#4801=IFCPOLYLINE((#5443,#6063));
#4802=IFCPOLYLINE((#6063,#6063));
#4803=IFCPOLYLINE((#6063,#6066));
#4804=IFCPOLYLINE((#6066,#6066));
#4805=IFCPOLYLINE((#6066,#6069));
#4806=IFCPOLYLINE((#6069,#6071));
#4807=IFCPOLYLINE((#6071,#6071));
#4808=IFCPOLYLINE((#6071,#6074));
#4809=IFCPOLYLINE((#6074,#6074));
#4810=IFCPOLYLINE((#6074,#6077));
#4811=IFCPOLYLINE((#6077,#6077));
#4812=IFCPOLYLINE((#6077,#6080));
#4813=IFCPOLYLINE((#6080,#6082));
#4814=IFCPOLYLINE((#6082,#6084));
#4815=IFCPOLYLINE((#6084,#6084));
#4816=IFCPOLYLINE((#6084,#6087));
#4817=IFCPOLYLINE((#6087,#6087));
#4818=IFCPOLYLINE((#6087,#6090));
#4819=IFCPOLYLINE((#6090,#6090));
#4820=IFCPOLYLINE((#6090,#6093));
#4821=IFCPOLYLINE((#6093,#6093));
#4822=IFCPOLYLINE((#6093,#6096));
#4823=IFCPOLYLINE((#6096,#6096));
#4824=IFCPOLYLINE((#6096,#6099));
#4825=IFCPOLYLINE((#6099,#6101));
#4826=IFCPOLYLINE((#6101,#6101));
#4827=IFCPOLYLINE((#6101,#6104));
#4828=IFCPOLYLINE((#6104,#6104));
#4829=IFCPOLYLINE((#6104,#6107));
#4830=IFCPOLYLINE((#6107,#6107));
#4831=IFCPOLYLINE((#6107,#6110));
#4832=IFCPOLYLINE((#6110,#6110));
#4833=IFCPOLYLINE((#6110,#6113));
#4834=IFCPOLYLINE((#6113,#6113));
#4835=IFCPOLYLINE((#6113,#6116));
#4836=IFCPOLYLINE((#6116,#6118));
#4837=IFCPOLYLINE((#6118,#6120));
#4838=IFCPOLYLINE((#6120,#6120));
#4839=IFCPOLYLINE((#6120,#6123));
#4840=IFCPOLYLINE((#6123,#6123));
#4841=IFCPOLYLINE((#6123,#6126));
#4842=IFCPOLYLINE((#6126,#6126));
#4843=IFCPOLYLINE((#6126,#6129));
#4844=IFCPOLYLINE((#6129,#6129));
#4845=IFCPOLYLINE((#6129,#5390));
#4846=IFCPOLYLINE((#5390,#5390));
#4847=IFCPOLYLINE((#5390,#5390));
#4848=IFCPOLYLINE((#5390,#6135));
#4849=IFCPOLYLINE((#6135,#6135));
#4850=IFCPOLYLINE((#6135,#6138));
#4851=IFCPOLYLINE((#6138,#6138));
#4852=IFCPOLYLINE((#6138,#6141));
#4853=IFCPOLYLINE((#6141,#6141));
#4854=IFCPOLYLINE((#6141,#6144));
#4855=IFCPOLYLINE((#6144,#6144));
#4856=IFCPOLYLINE((#6144,#6147));
#4857=IFCPOLYLINE((#6147,#6147));
#4858=IFCPOLYLINE((#6147,#6150));
#4859=IFCPOLYLINE((#6150,#6150));
#4860=IFCPOLYLINE((#6150,#6153));
#4861=IFCPOLYLINE((#6153,#6153));
#4862=IFCPOLYLINE((#6153,#6156));
#4863=IFCPOLYLINE((#6156,#6156));
#4864=IFCPOLYLINE((#6156,#6159));
#4865=IFCPOLYLINE((#6159,#6159));
#4866=IFCPOLYLINE((#6159,#6162));
#4867=IFCPOLYLINE((#6162,#6162));
#4868=IFCPOLYLINE((#6162,#5392));
#4869=IFCPOLYLINE((#5392,#5392));
#4870=IFCPOLYLINE((#5392,#5392));
#4871=IFCPOLYLINE((#5392,#6168));
#4872=IFCPOLYLINE((#6168,#6168));
#4873=IFCPOLYLINE((#6168,#6171));
#4874=IFCPOLYLINE((#6171,#6173));
#4875=IFCPOLYLINE((#6173,#6173));
#4876=IFCPOLYLINE((#6173,#6176));
#4877=IFCPOLYLINE((#6176,#6176));
#4878=IFCPOLYLINE((#6176,#6179));
#4879=IFCPOLYLINE((#6179,#6179));
#4880=IFCPOLYLINE((#6179,#6182));
#4881=IFCPOLYLINE((#6182,#6182));
#4882=IFCPOLYLINE((#6182,#6185));
#4883=IFCPOLYLINE((#6185,#6185));
#4884=IFCPOLYLINE((#6185,#6188));
#4885=IFCPOLYLINE((#6188,#6188));
#4886=IFCPOLYLINE((#6188,#6191));
#4887=IFCPOLYLINE((#6191,#6191));
#4888=IFCPOLYLINE((#6191,#6194));
#4889=IFCPOLYLINE((#6194,#6194));
#4890=IFCPOLYLINE((#6194,#6197));
#4891=IFCPOLYLINE((#6197,#6197));
#4892=IFCPOLYLINE((#6197,#5394));
#4893=IFCPOLYLINE((#5394,#5394));
#4894=IFCPOLYLINE((#5394,#6202));
#4895=IFCPOLYLINE((#6202,#6202));
#4896=IFCPOLYLINE((#6202,#6205));
#4897=IFCPOLYLINE((#6205,#6207));
#4898=IFCPOLYLINE((#6207,#6207));
#4899=IFCPOLYLINE((#6207,#6210));
#4900=IFCPOLYLINE((#6210,#6210));
#4901=IFCPOLYLINE((#6210,#6213));
#4902=IFCPOLYLINE((#6213,#6213));
#4903=IFCPOLYLINE((#6213,#6216));
#4904=IFCPOLYLINE((#6216,#6216));
#4905=IFCPOLYLINE((#6216,#6219));
#4906=IFCPOLYLINE((#6219,#6219));
#4907=IFCPOLYLINE((#6219,#6222));
#4908=IFCPOLYLINE((#6222,#6222));
#4909=IFCPOLYLINE((#6222,#6225));
#4910=IFCPOLYLINE((#6225,#6225));
#4911=IFCPOLYLINE((#6225,#6228));
#4912=IFCPOLYLINE((#6228,#6228));
#4913=IFCPOLYLINE((#6228,#6231));
#4914=IFCPOLYLINE((#6231,#6231));
#4915=IFCPOLYLINE((#6231,#5396));
#4916=IFCPOLYLINE((#5396,#5396));
#4917=IFCPOLYLINE((#5396,#5396));
#4918=IFCPOLYLINE((#5396,#6237));
#4919=IFCPOLYLINE((#6237,#6237));
#4920=IFCPOLYLINE((#6237,#6240));
#4921=IFCPOLYLINE((#6240,#6240));
#4922=IFCPOLYLINE((#6240,#6243));
#4923=IFCPOLYLINE((#6243,#6243));
#4924=IFCPOLYLINE((#6243,#6246));
#4925=IFCPOLYLINE((#6246,#6246));
#4926=IFCPOLYLINE((#6246,#6249));
#4927=IFCPOLYLINE((#6249,#6249));
#4928=IFCPOLYLINE((#6249,#6252));
#4929=IFCPOLYLINE((#6252,#6252));
#4930=IFCPOLYLINE((#6252,#6255));
#4931=IFCPOLYLINE((#6255,#6255));
#4932=IFCPOLYLINE((#6255,#6258));
#4933=IFCPOLYLINE((#6258,#6258));
#4934=IFCPOLYLINE((#6258,#6261));
#4935=IFCPOLYLINE((#6261,#6261));
#4936=IFCPOLYLINE((#6261,#6264));
#4937=IFCPOLYLINE((#6264,#6264));
#4938=IFCPOLYLINE((#6264,#5398));
#4939=IFCPOLYLINE((#5398,#5398));
#4940=IFCPOLYLINE((#5398,#5398));
#4941=IFCPOLYLINE((#5398,#6270));
#4942=IFCPOLYLINE((#6270,#6270));
#4943=IFCPOLYLINE((#6270,#6273));
#4944=IFCPOLYLINE((#6273,#6275));
#4945=IFCPOLYLINE((#6275,#6275));
#4946=IFCPOLYLINE((#6275,#6278));
#4947=IFCPOLYLINE((#6278,#6278));
#4948=IFCPOLYLINE((#6278,#6281));
#4949=IFCPOLYLINE((#6281,#6281));
#4950=IFCPOLYLINE((#6281,#6284));
#4951=IFCPOLYLINE((#6284,#6284));
#4952=IFCPOLYLINE((#6284,#6287));
#4953=IFCPOLYLINE((#6287,#6289));
#4954=IFCPOLYLINE((#6289,#6291));
#4955=IFCPOLYLINE((#6291,#6291));
#4956=IFCPOLYLINE((#6291,#6294));
#4957=IFCPOLYLINE((#6294,#6294));
#4958=IFCPOLYLINE((#6294,#6297));
#4959=IFCPOLYLINE((#6297,#6297));
#4960=IFCPOLYLINE((#6297,#6300));
#4961=IFCPOLYLINE((#6300,#6300));
#4962=IFCPOLYLINE((#6300,#6303));
#4963=IFCPOLYLINE((#6303,#6303));
#4964=IFCPOLYLINE((#6303,#6306));
#4965=IFCPOLYLINE((#6306,#6308));
#4966=IFCPOLYLINE((#6308,#6308));
#4967=IFCPOLYLINE((#6308,#6311));
#4968=IFCPOLYLINE((#6311,#6311));
#4969=IFCPOLYLINE((#6311,#6314));
#4970=IFCPOLYLINE((#6314,#6314));
#4971=IFCPOLYLINE((#6314,#6317));
#4972=IFCPOLYLINE((#6317,#6317));
#4973=IFCPOLYLINE((#6317,#6320));
#4974=IFCPOLYLINE((#6320,#6320));
#4975=IFCPOLYLINE((#6320,#6323));
#4976=IFCPOLYLINE((#6323,#6325));
#4977=IFCPOLYLINE((#6325,#6327));
#4978=IFCPOLYLINE((#6327,#6327));
#4979=IFCPOLYLINE((#6327,#6330));
#4980=IFCPOLYLINE((#6330,#6330));
#4981=IFCPOLYLINE((#6330,#6333));
#4982=IFCPOLYLINE((#6333,#6333));
#4983=IFCPOLYLINE((#6333,#6336));
#4984=IFCPOLYLINE((#6336,#6336));
#4985=IFCPOLYLINE((#6336,#5444));
#4986=IFCPOLYLINE((#5444,#6339));
#4987=IFCPOLYLINE((#6339,#5406));
#4988=IFCPOLYLINE((#5406,#6342));
#4989=IFCPOLYLINE((#6342,#5441));
#4990=IFCPOLYLINE((#6344,#6344));
#4991=IFCPOLYLINE((#6344,#6344));
#4992=IFCPOLYLINE((#6344,#6348));
#4993=IFCPOLYLINE((#6348,#6348));
#4994=IFCPOLYLINE((#6348,#6348));
#4995=IFCPOLYLINE((#6348,#6348));
#4996=IFCPOLYLINE((#6348,#6348));
#4997=IFCPOLYLINE((#6348,#6354));
#4998=IFCPOLYLINE((#6354,#6354));
#4999=IFCPOLYLINE((#6354,#6354));
#5000=IFCPOLYLINE((#6354,#6358));
#5001=IFCPOLYLINE((#6358,#6358));
#5002=IFCPOLYLINE((#6358,#6361));
#5003=IFCPOLYLINE((#6361,#6361));
#5004=IFCPOLYLINE((#6361,#6364));
#5005=IFCPOLYLINE((#6364,#6364));
#5006=IFCPOLYLINE((#6364,#6367));
#5007=IFCPOLYLINE((#6367,#6367));
#5008=IFCPOLYLINE((#6367,#6370));
#5009=IFCPOLYLINE((#6370,#6370));
#5010=IFCPOLYLINE((#6370,#6373));
#5011=IFCPOLYLINE((#6373,#6373));
#5012=IFCPOLYLINE((#6373,#6376));
#5013=IFCPOLYLINE((#6376,#6376));
#5014=IFCPOLYLINE((#6376,#6379));
#5015=IFCPOLYLINE((#6379,#6381));
#5016=IFCPOLYLINE((#6381,#6381));
#5017=IFCPOLYLINE((#6381,#6384));
#5018=IFCPOLYLINE((#6384,#6384));
#5019=IFCPOLYLINE((#6384,#6387));
#5020=IFCPOLYLINE((#6387,#6387));
#5021=IFCPOLYLINE((#6387,#6390));
#5022=IFCPOLYLINE((#6390,#6390));
#5023=IFCPOLYLINE((#6390,#6393));
#5024=IFCPOLYLINE((#6393,#6393));
#5025=IFCPOLYLINE((#6393,#6396));
#5026=IFCPOLYLINE((#6396,#6396));
#5027=IFCPOLYLINE((#6396,#6399));
#5028=IFCPOLYLINE((#6399,#6399));
#5029=IFCPOLYLINE((#6399,#6402));
#5030=IFCPOLYLINE((#6402,#6402));
#5031=IFCPOLYLINE((#6402,#6405));
#5032=IFCPOLYLINE((#6405,#6405));
#5033=IFCPOLYLINE((#6405,#6408));
#5034=IFCPOLYLINE((#6408,#6408));
#5035=IFCPOLYLINE((#6408,#6411));
#5036=IFCPOLYLINE((#6411,#6411));
#5037=IFCPOLYLINE((#6411,#6414));
#5038=IFCPOLYLINE((#6414,#6414));
#5039=IFCPOLYLINE((#6414,#6417));
#5040=IFCPOLYLINE((#6417,#6417));
#5041=IFCPOLYLINE((#6417,#6420));
#5042=IFCPOLYLINE((#6420,#6420));
#5043=IFCPOLYLINE((#6420,#6423));
#5044=IFCPOLYLINE((#6423,#6423));
#5045=IFCPOLYLINE((#6423,#6426));
#5046=IFCPOLYLINE((#6426,#6426));
#5047=IFCPOLYLINE((#6426,#6429));
#5048=IFCPOLYLINE((#6429,#6429));
#5049=IFCPOLYLINE((#6429,#6432));
#5050=IFCPOLYLINE((#6432,#6432));
#5051=IFCPOLYLINE((#6432,#6435));
#5052=IFCPOLYLINE((#6435,#6435));
#5053=IFCPOLYLINE((#6435,#6438));
#5054=IFCPOLYLINE((#6438,#6438));
#5055=IFCPOLYLINE((#6438,#6441));
#5056=IFCPOLYLINE((#6441,#6441));
#5057=IFCPOLYLINE((#6441,#6444));
#5058=IFCPOLYLINE((#6444,#6444));
#5059=IFCPOLYLINE((#6444,#6447));
#5060=IFCPOLYLINE((#6447,#6447));
#5061=IFCPOLYLINE((#6447,#6450));
#5062=IFCPOLYLINE((#6450,#6450));
#5063=IFCPOLYLINE((#6450,#6453));
#5064=IFCPOLYLINE((#6453,#6453));
#5065=IFCPOLYLINE((#6453,#6456));
#5066=IFCPOLYLINE((#6456,#6456));
#5067=IFCPOLYLINE((#6456,#6459));
#5068=IFCPOLYLINE((#6459,#6459));
#5069=IFCPOLYLINE((#6459,#6462));
#5070=IFCPOLYLINE((#6462,#6462));
#5071=IFCPOLYLINE((#6462,#6465));
#5072=IFCPOLYLINE((#6465,#6467));
#5073=IFCPOLYLINE((#6467,#6467));
#5074=IFCPOLYLINE((#6467,#6467));
#5075=IFCPOLYLINE((#6467,#6471));
#5076=IFCPOLYLINE((#6471,#6471));
#5077=IFCPOLYLINE((#6471,#6471));
#5078=IFCPOLYLINE((#6471,#6475));
#5079=IFCPOLYLINE((#6475,#6477));
#5080=IFCPOLYLINE((#6477,#6477));
#5081=IFCPOLYLINE((#6477,#6477));
#5082=IFCPOLYLINE((#6477,#6477));
#5083=IFCPOLYLINE((#6477,#6477));
#5084=IFCPOLYLINE((#6477,#6483));
#5085=IFCPOLYLINE((#6483,#6483));
#5086=IFCPOLYLINE((#6483,#6483));
#5087=IFCPOLYLINE((#6483,#6483));
#5088=IFCPOLYLINE((#6483,#6483));
#5089=IFCPOLYLINE((#6483,#6489));
#5090=IFCPOLYLINE((#6489,#6491));
#5091=IFCPOLYLINE((#6491,#6491));
#5092=IFCPOLYLINE((#6491,#6491));
#5093=IFCPOLYLINE((#6491,#6491));
#5094=IFCPOLYLINE((#6491,#6496));
#5095=IFCPOLYLINE((#6496,#6498));
#5096=IFCPOLYLINE((#6498,#6498));
#5097=IFCPOLYLINE((#6498,#6498));
#5098=IFCPOLYLINE((#6498,#6502));
#5099=IFCPOLYLINE((#6502,#6502));
#5100=IFCPOLYLINE((#6502,#6502));
#5101=IFCPOLYLINE((#6502,#6506));
#5102=IFCPOLYLINE((#6506,#6508));
#5103=IFCPOLYLINE((#6508,#6508));
#5104=IFCPOLYLINE((#6508,#6511));
#5105=IFCPOLYLINE((#6511,#6511));
#5106=IFCPOLYLINE((#6511,#6514));
#5107=IFCPOLYLINE((#6514,#6514));
#5108=IFCPOLYLINE((#6514,#6517));
#5109=IFCPOLYLINE((#6517,#6517));
#5110=IFCPOLYLINE((#6517,#6520));
#5111=IFCPOLYLINE((#6520,#6520));
#5112=IFCPOLYLINE((#6520,#6523));
#5113=IFCPOLYLINE((#6523,#6523));
#5114=IFCPOLYLINE((#6523,#6526));
#5115=IFCPOLYLINE((#6526,#6528));
#5116=IFCPOLYLINE((#6528,#6528));
#5117=IFCPOLYLINE((#6528,#6531));
#5118=IFCPOLYLINE((#6531,#6531));
#5119=IFCPOLYLINE((#6531,#6534));
#5120=IFCPOLYLINE((#6534,#6534));
#5121=IFCPOLYLINE((#6534,#6537));
#5122=IFCPOLYLINE((#6537,#6537));
#5123=IFCPOLYLINE((#6537,#6540));
#5124=IFCPOLYLINE((#6540,#6540));
#5125=IFCPOLYLINE((#6540,#6543));
#5126=IFCPOLYLINE((#6543,#6543));
#5127=IFCPOLYLINE((#6543,#6546));
#5128=IFCPOLYLINE((#6546,#6546));
#5129=IFCPOLYLINE((#6546,#6549));
#5130=IFCPOLYLINE((#6549,#6549));
#5131=IFCPOLYLINE((#6549,#6552));
#5132=IFCPOLYLINE((#6552,#6552));
#5133=IFCPOLYLINE((#6552,#6555));
#5134=IFCPOLYLINE((#6555,#6555));
#5135=IFCPOLYLINE((#6555,#6558));
#5136=IFCPOLYLINE((#6558,#6558));
#5137=IFCPOLYLINE((#6558,#6561));
#5138=IFCPOLYLINE((#6561,#6561));
#5139=IFCPOLYLINE((#6561,#6564));
#5140=IFCPOLYLINE((#6564,#6564));
#5141=IFCPOLYLINE((#6564,#6567));
#5142=IFCPOLYLINE((#6567,#6567));
#5143=IFCPOLYLINE((#6567,#6570));
#5144=IFCPOLYLINE((#6570,#6570));
#5145=IFCPOLYLINE((#6570,#6573));
#5146=IFCPOLYLINE((#6573,#6573));
#5147=IFCPOLYLINE((#6573,#6576));
#5148=IFCPOLYLINE((#6576,#6576));
#5149=IFCPOLYLINE((#6576,#6579));
#5150=IFCPOLYLINE((#6579,#6579));
#5151=IFCPOLYLINE((#6579,#6582));
#5152=IFCPOLYLINE((#6582,#6582));
#5153=IFCPOLYLINE((#6582,#6585));
#5154=IFCPOLYLINE((#6585,#6585));
#5155=IFCPOLYLINE((#6585,#6588));
#5156=IFCPOLYLINE((#6588,#6590));
#5157=IFCPOLYLINE((#6590,#6590));
#5158=IFCPOLYLINE((#6590,#6593));
#5159=IFCPOLYLINE((#6593,#6593));
#5160=IFCPOLYLINE((#6593,#6596));
#5161=IFCPOLYLINE((#6596,#6596));
#5162=IFCPOLYLINE((#6596,#6599));
#5163=IFCPOLYLINE((#6599,#6599));
#5164=IFCPOLYLINE((#6599,#6602));
#5165=IFCPOLYLINE((#6602,#6602));
#5166=IFCPOLYLINE((#6602,#6605));
#5167=IFCPOLYLINE((#6605,#6605));
#5168=IFCPOLYLINE((#6605,#6608));
#5169=IFCPOLYLINE((#6608,#6610));
#5170=IFCPOLYLINE((#6610,#6610));
#5171=IFCPOLYLINE((#6610,#6610));
#5172=IFCPOLYLINE((#6610,#6614));
#5173=IFCPOLYLINE((#6614,#6614));
#5174=IFCPOLYLINE((#6614,#6617));
#5175=IFCPOLYLINE((#6617,#6617));
#5176=IFCPOLYLINE((#6617,#6620));
#5177=IFCPOLYLINE((#6620,#6620));
#5178=IFCPOLYLINE((#6620,#6620));
#5179=IFCPOLYLINE((#6620,#6620));
#5180=IFCPOLYLINE((#6620,#6625));
#5181=IFCPOLYLINE((#6625,#6344));
#5182=IFCPOLYLINE((#6344,#6344));
#5183=IFCPOLYLINE((#6344,#6344));
#5184=IFCPOLYLINE((#6344,#6344));
#5185=IFCPOLYLINE((#5441,#5442));
#5186=IFCPOLYLINE((#5442,#5443));
#5187=IFCPOLYLINE((#5443,#5444));
#5188=IFCPOLYLINE((#5444,#5441));
#5189=IFCPOLYLINE((#5414,#5839));
#5190=IFCPOLYLINE((#5839,#5822));
#5191=IFCPOLYLINE((#5822,#5804));
#5192=IFCPOLYLINE((#5804,#5441));
#5193=IFCPOLYLINE((#5441,#6342));
#5194=IFCPOLYLINE((#6342,#5406));
#5195=IFCPOLYLINE((#5406,#6339));
#5196=IFCPOLYLINE((#6339,#5444));
#5197=IFCPOLYLINE((#5444,#6320));
#5198=IFCPOLYLINE((#6320,#6300));
#5199=IFCPOLYLINE((#6300,#6284));
#5200=IFCPOLYLINE((#6284,#5398));
#5201=IFCPOLYLINE((#5398,#6882));
#5202=IFCPOLYLINE((#6882,#6884));
#5203=IFCPOLYLINE((#6884,#6886));
#5204=IFCPOLYLINE((#6886,#5414));
#5205=IFCPOLYLINE((#5418,#5915));
#5206=IFCPOLYLINE((#5915,#5906));
#5207=IFCPOLYLINE((#5906,#5897));
#5208=IFCPOLYLINE((#5897,#5416));
#5209=IFCPOLYLINE((#5416,#6891));
#5210=IFCPOLYLINE((#6891,#6893));
#5211=IFCPOLYLINE((#6893,#6895));
#5212=IFCPOLYLINE((#6895,#5396));
#5213=IFCPOLYLINE((#5396,#6225));
#5214=IFCPOLYLINE((#6225,#6216));
#5215=IFCPOLYLINE((#6216,#6899));
#5216=IFCPOLYLINE((#6899,#5394));
#5217=IFCPOLYLINE((#5394,#6901));
#5218=IFCPOLYLINE((#6901,#6903));
#5219=IFCPOLYLINE((#6903,#6905));
#5220=IFCPOLYLINE((#6905,#5418));
#5221=IFCPOLYLINE((#6344,#6907));
#5222=IFCPOLYLINE((#6907,#6910));
#5223=IFCPOLYLINE((#6910,#6912));
#5224=IFCPOLYLINE((#6912,#6914));
#5225=IFCPOLYLINE((#6914,#6916));
#5226=IFCPOLYLINE((#6916,#6918));
#5227=IFCPOLYLINE((#6918,#6920));
#5228=IFCPOLYLINE((#6920,#6922));
#5229=IFCPOLYLINE((#6922,#6924));
#5230=IFCPOLYLINE((#6924,#6926));
#5231=IFCPOLYLINE((#6926,#6928));
#5232=IFCPOLYLINE((#6928,#6344));
#5233=IFCPOLYLINE((#5416,#5882));
#5234=IFCPOLYLINE((#5882,#5873));
#5235=IFCPOLYLINE((#5873,#5864));
#5236=IFCPOLYLINE((#5864,#5414));
#5237=IFCPOLYLINE((#5414,#6886));
#5238=IFCPOLYLINE((#6886,#6884));
#5239=IFCPOLYLINE((#6884,#6882));
#5240=IFCPOLYLINE((#6882,#5398));
#5241=IFCPOLYLINE((#5398,#6258));
#5242=IFCPOLYLINE((#6258,#6249));
#5243=IFCPOLYLINE((#6249,#6240));
#5244=IFCPOLYLINE((#6240,#5396));
#5245=IFCPOLYLINE((#5396,#6895));
#5246=IFCPOLYLINE((#6895,#6893));
#5247=IFCPOLYLINE((#6893,#6891));
#5248=IFCPOLYLINE((#6891,#5416));
#5249=IFCPOLYLINE((#6936,#6937));
#5250=IFCPOLYLINE((#6937,#6940));
#5251=IFCPOLYLINE((#6940,#6942));
#5252=IFCPOLYLINE((#6942,#6944));
#5253=IFCPOLYLINE((#6944,#6946));
#5254=IFCPOLYLINE((#6946,#6948));
#5255=IFCPOLYLINE((#6948,#6950));
#5256=IFCPOLYLINE((#6950,#6952));
#5257=IFCPOLYLINE((#6952,#6954));
#5258=IFCPOLYLINE((#6954,#6956));
#5259=IFCPOLYLINE((#6956,#6958));
#5260=IFCPOLYLINE((#6958,#6936));
#5261=IFCPOLYLINE((#5418,#5420));
#5262=IFCPOLYLINE((#5420,#5374));
#5263=IFCPOLYLINE((#5374,#6024));
#5264=IFCPOLYLINE((#6024,#5442));
#5265=IFCPOLYLINE((#5442,#6057));
#5266=IFCPOLYLINE((#6057,#5382));
#5267=IFCPOLYLINE((#5382,#6060));
#5268=IFCPOLYLINE((#6060,#5443));
#5269=IFCPOLYLINE((#5443,#6096));
#5270=IFCPOLYLINE((#6096,#5390));
#5271=IFCPOLYLINE((#5390,#5392));
#5272=IFCPOLYLINE((#5392,#5394));
#5273=IFCPOLYLINE((#5394,#6901));
#5274=IFCPOLYLINE((#6901,#6903));
#5275=IFCPOLYLINE((#6903,#6905));
#5276=IFCPOLYLINE((#6905,#5418));
#5277=IFCPOLYLINE((#5444,#5398));
#5278=IFCPOLYLINE((#5398,#5394));
#5279=IFCPOLYLINE((#5394,#5390));
#5280=IFCPOLYLINE((#5390,#5443));
#5281=IFCPOLYLINE((#5443,#6060));
#5282=IFCPOLYLINE((#6060,#5382));
#5283=IFCPOLYLINE((#5382,#6057));
#5284=IFCPOLYLINE((#6057,#5442));
#5285=IFCPOLYLINE((#5442,#5374));
#5286=IFCPOLYLINE((#5374,#5418));
#5287=IFCPOLYLINE((#5418,#5414));
#5288=IFCPOLYLINE((#5414,#5441));
#5289=IFCPOLYLINE((#5441,#6342));
#5290=IFCPOLYLINE((#6342,#5406));
#5291=IFCPOLYLINE((#5406,#6339));
#5292=IFCPOLYLINE((#6339,#5444));
#5293=IFCPOLYLINE((#6978,#6979));
#5294=IFCPOLYLINE((#6979,#6982));
#5295=IFCPOLYLINE((#6982,#6984));
#5296=IFCPOLYLINE((#6984,#6986));
#5297=IFCPOLYLINE((#6986,#6988));
#5298=IFCPOLYLINE((#6988,#6990));
#5299=IFCPOLYLINE((#6990,#6992));
#5300=IFCPOLYLINE((#6992,#6994));
#5301=IFCPOLYLINE((#6994,#6996));
#5302=IFCPOLYLINE((#6996,#6998));
#5303=IFCPOLYLINE((#6998,#7000));
#5304=IFCPOLYLINE((#7000,#7002));
#5305=IFCPOLYLINE((#7002,#7004));
#5306=IFCPOLYLINE((#7004,#7006));
#5307=IFCPOLYLINE((#7006,#7008));
#5308=IFCPOLYLINE((#7008,#6978));
#5309=IFCPOLYLINE((#5441,#5442));
#5310=IFCPOLYLINE((#5442,#5443));
#5311=IFCPOLYLINE((#5443,#5444));
#5312=IFCPOLYLINE((#5444,#5441));
#5313=IFCFACEOUTERBOUND(#883,.T.);
#5314=IFCFACEOUTERBOUND(#884,.T.);
#5315=IFCFACEOUTERBOUND(#885,.T.);
#5316=IFCFACEOUTERBOUND(#886,.T.);
#5317=IFCFACEOUTERBOUND(#887,.T.);
#5318=IFCFACEOUTERBOUND(#888,.T.);
#5319=IFCFACEOUTERBOUND(#890,.T.);
#5320=IFCFACEOUTERBOUND(#891,.T.);
#5321=IFCFACEOUTERBOUND(#897,.T.);
#5322=IFCFACEOUTERBOUND(#899,.T.);
#5323=IFCADVANCEDFACE((#5313),#5333,.T.);
#5324=IFCADVANCEDFACE((#5314),#5334,.T.);
#5325=IFCADVANCEDFACE((#5315),#47,.T.);
#5326=IFCADVANCEDFACE((#5316),#48,.T.);
#5327=IFCADVANCEDFACE((#5317),#49,.T.);
#5328=IFCADVANCEDFACE((#5318,#24),#5335,.T.);
#5329=IFCADVANCEDFACE((#5319),#5336,.T.);
#5330=IFCADVANCEDFACE((#5320,#25,#26,#27,#28,#29),#5337,.T.);
#5331=IFCADVANCEDFACE((#5321,#30),#5338,.T.);
#5332=IFCADVANCEDFACE((#5322),#50,.T.);
#5333=IFCBSPLINESURFACEWITHKNOTS(1,1,((#5370,#5371),(#5372,#5373)),
 .UNSPECIFIED.,.F.,.F.,.U.,(2,2),(2,2),(0.,1.),(0.,1.),.UNSPECIFIED.);
#5334=IFCBSPLINESURFACEWITHKNOTS(3,3,((#5425,#5426,#5427,#5428),(#5429,
#5430,#5431,#5432),(#5433,#5434,#5435,#5436),(#5437,#5438,#5439,#5440)),
 .UNSPECIFIED.,.F.,.F.,.U.,(4,4),(4,4),(0.,1.),(0.,1.),.UNSPECIFIED.);
#5335=IFCBSPLINESURFACEWITHKNOTS(3,1,((#5776,#5777),(#5778,#5779),(#5780,
#5781),(#5782,#5783),(#5784,#5785),(#5786,#5787)),.UNSPECIFIED.,.F.,.F.,
 .U.,(4,1,1,4),(2,2),(0.,0.352757198465535,0.647242801534465,1.),(0.,1.),
 .UNSPECIFIED.);
#5336=IFCBSPLINESURFACEWITHKNOTS(9,9,((#6632,#6633,#6634,#6635,#6636,#6637,
#6638,#6639,#6640,#6641,#6642,#6643,#6644,#6645,#6646),(#6647,#6648,#6649,
#6650,#6651,#6652,#6653,#6654,#6655,#6656,#6657,#6658,#6659,#6660,#6661),
(#6662,#6663,#6664,#6665,#6666,#6667,#6668,#6669,#6670,#6671,#6672,#6673,
#6674,#6675,#6676),(#6677,#6678,#6679,#6680,#6681,#6682,#6683,#6684,#6685,
#6686,#6687,#6688,#6689,#6690,#6691),(#6692,#6693,#6694,#6695,#6696,#6697,
#6698,#6699,#6700,#6701,#6702,#6703,#6704,#6705,#6706),(#6707,#6708,#6709,
#6710,#6711,#6712,#6713,#6714,#6715,#6716,#6717,#6718,#6719,#6720,#6721),
(#6722,#6723,#6724,#6725,#6726,#6727,#6728,#6729,#6730,#6731,#6732,#6733,
#6734,#6735,#6736),(#6737,#6738,#6739,#6740,#6741,#6742,#6743,#6744,#6745,
#6746,#6747,#6748,#6749,#6750,#6751),(#6752,#6753,#6754,#6755,#6756,#6757,
#6758,#6759,#6760,#6761,#6762,#6763,#6764,#6765,#6766),(#6767,#6768,#6769,
#6770,#6771,#6772,#6773,#6774,#6775,#6776,#6777,#6778,#6779,#6780,#6781),
(#6782,#6783,#6784,#6785,#6786,#6787,#6788,#6789,#6790,#6791,#6792,#6793,
#6794,#6795,#6796),(#6797,#6798,#6799,#6800,#6801,#6802,#6803,#6804,#6805,
#6806,#6807,#6808,#6809,#6810,#6811),(#6812,#6813,#6814,#6815,#6816,#6817,
#6818,#6819,#6820,#6821,#6822,#6823,#6824,#6825,#6826),(#6827,#6828,#6829,
#6830,#6831,#6832,#6833,#6834,#6835,#6836,#6837,#6838,#6839,#6840,#6841),
(#6842,#6843,#6844,#6845,#6846,#6847,#6848,#6849,#6850,#6851,#6852,#6853,
#6854,#6855,#6856)),.UNSPECIFIED.,.F.,.F.,.U.,(10,1,1,1,1,1,10),(10,1,1,
1,1,1,10),(0.,0.166666666666667,0.333333333333333,0.5,0.666666666666667,
0.833333333333333,1.),(0.,0.166666666666667,0.333333333333333,0.5,0.666666666666667,
0.833333333333333,1.),.UNSPECIFIED.);
#5337=IFCBSPLINESURFACEWITHKNOTS(1,1,((#6859,#6860),(#6861,#6862),(#6863,
#6864),(#6865,#6866),(#6867,#6868),(#6869,#6870),(#6871,#6872),(#6873,#6874),
(#6859,#6860)),.UNSPECIFIED.,.T.,.F.,.U.,(2,1,1,1,1,1,1,1,2),(2,2),(0.,
0.125,0.25,0.375,0.5,0.625,0.75,0.875,1.),(0.,1.),.UNSPECIFIED.);
#5338=IFCBSPLINESURFACEWITHKNOTS(1,1,((#6962,#6963),(#6964,#6965)),
 .UNSPECIFIED.,.F.,.F.,.U.,(2,2),(2,2),(0.,1.),(0.,1.),.UNSPECIFIED.);
#5339=IFCRELAGGREGATES('1kB3AQvfGpkFm_7uHNRdoP',#7147,$,$,#7136,(#7062));
#5340=IFCRELAGGREGATES('0Cp_wDOOr3amQ16IWrGpUy',#7147,$,$,#7062,(#94));
#5341=IFCRELAGGREGATES('1NfacxJUhl1S_ka3CQXVRt',#7147,$,$,#94,(#93));
#5342=IFCLOCALPLACEMENT($,#5355);
#5343=IFCLOCALPLACEMENT(#5342,#5356);
#5344=IFCLOCALPLACEMENT(#5343,#5357);
#5345=IFCLOCALPLACEMENT(#5344,#5358);
#5346=IFCLOCALPLACEMENT(#5344,#5360);
#5347=IFCLOCALPLACEMENT(#5344,#5361);
#5348=IFCLOCALPLACEMENT(#5344,#5362);
#5349=IFCLOCALPLACEMENT(#5344,#5363);
#5350=IFCLOCALPLACEMENT(#5344,#5364);
#5351=IFCLOCALPLACEMENT(#5344,#5365);
#5352=IFCLOCALPLACEMENT(#5344,#5366);
#5353=IFCLOCALPLACEMENT(#5344,#5367);
#5354=IFCLOCALPLACEMENT(#5344,#5368);
#5355=IFCAXIS2PLACEMENT3D(#5369,#7059,#7060);
#5356=IFCAXIS2PLACEMENT3D(#5369,#7059,#7060);
#5357=IFCAXIS2PLACEMENT3D(#5422,#7059,#7060);
#5358=IFCAXIS2PLACEMENT3D(#5423,#7059,#7060);
#5359=IFCAXIS2PLACEMENT3D(#5369,#7059,#7060);
#5360=IFCAXIS2PLACEMENT3D(#5445,#7059,#7060);
#5361=IFCAXIS2PLACEMENT3D(#5556,#7059,#7060);
#5362=IFCAXIS2PLACEMENT3D(#5761,#7059,#7060);
#5363=IFCAXIS2PLACEMENT3D(#5774,#7059,#7060);
#5364=IFCAXIS2PLACEMENT3D(#6630,#7059,#7060);
#5365=IFCAXIS2PLACEMENT3D(#6857,#7059,#7060);
#5366=IFCAXIS2PLACEMENT3D(#6960,#7059,#7060);
#5367=IFCAXIS2PLACEMENT3D(#7010,#7059,#7060);
#5368=IFCAXIS2PLACEMENT3D(#7057,#7059,#7060);
#5369=IFCCARTESIANPOINT((0.,0.,0.));
#5370=IFCCARTESIANPOINT((11.9546076011,7.5625464995E-5,9.9999999986E-5));
#5371=IFCCARTESIANPOINT((1.4578429633E-5,6.9020631252,9.9999999986E-5));
#5372=IFCCARTESIANPOINT((19.8844876395,13.7350307499,0.000100000000021));
#5373=IFCCARTESIANPOINT((7.9298946168,20.6370182496,0.000100000000003));
#5374=IFCCARTESIANPOINT((7.5125628141E-5,0.));
#5375=IFCCARTESIANPOINT((8.1407035176E-5,1.25E-5));
#5376=IFCCARTESIANPOINT((17.9119797908,10.3185469375,0.000100000000012));
#5377=IFCCARTESIANPOINT((16.9157637056,12.0440438125,0.000100000000012));
#5378=IFCCARTESIANPOINT((8.7688442211E-5,2.5E-5));
#5379=IFCCARTESIANPOINT((15.9195476203,13.7695406874,0.000100000000013));
#5380=IFCCARTESIANPOINT((9.3969849246E-5,3.75E-5));
#5381=IFCCARTESIANPOINT((14.9233315351,15.4950375623,0.000100000000012));
#5382=IFCCARTESIANPOINT((0.000100251256281,5.E-5));
#5383=IFCCARTESIANPOINT((13.9071911282,17.1860244997,0.000100000000012));
#5384=IFCCARTESIANPOINT((9.3969849246E-5,6.25E-5));
#5385=IFCCARTESIANPOINT((11.9346832794,17.2205344372,0.000100000000008));
#5386=IFCCARTESIANPOINT((8.7688442211E-5,7.5E-5));
#5387=IFCCARTESIANPOINT((9.942251109,17.2205344372,0.000100000000005));
#5388=IFCCARTESIANPOINT((8.1407035176E-5,8.75E-5));
#5389=IFCCARTESIANPOINT((7.9498189385,17.2205344372,0.000100000000002));
#5390=IFCCARTESIANPOINT((7.5125628141E-5,0.0001));
#5391=IFCCARTESIANPOINT((5.9573867681,17.2205344372,9.9999999999E-5));
#5392=IFCCARTESIANPOINT((6.256281407E-5,0.0001));
#5393=IFCCARTESIANPOINT((4.9611706829,15.4950375623,9.9999999997E-5));
#5394=IFCCARTESIANPOINT((5.E-5,0.0001));
#5395=IFCCARTESIANPOINT((3.9649545976,13.7695406874,9.9999999995E-5));
#5396=IFCCARTESIANPOINT((3.743718593E-5,0.0001));
#5397=IFCCARTESIANPOINT((2.9687385124,12.0440438125,9.9999999992E-5));
#5398=IFCCARTESIANPOINT((2.4874371859E-5,0.0001));
#5399=IFCCARTESIANPOINT((1.97252242718,10.3185469375,9.999999999E-5));
#5400=IFCCARTESIANPOINT((1.8592964824E-5,8.75E-5));
#5401=IFCCARTESIANPOINT((2.9687385124,8.5930500626,9.9999999989E-5));
#5402=IFCCARTESIANPOINT((1.2311557789E-5,7.5E-5));
#5403=IFCCARTESIANPOINT((3.9649545976,6.8675531877,9.9999999988E-5));
#5404=IFCCARTESIANPOINT((6.030150754E-6,6.25E-5));
#5405=IFCCARTESIANPOINT((4.9611706829,5.1420563127,9.9999999987E-5));
#5406=IFCCARTESIANPOINT((-2.5125628141E-7,5.E-5));
#5407=IFCCARTESIANPOINT((5.9773110898,3.4510693753,9.9999999986E-5));
#5408=IFCCARTESIANPOINT((6.030150754E-6,3.75E-5));
#5409=IFCCARTESIANPOINT((7.9498189385,3.4165594378,9.9999999988E-5));
#5410=IFCCARTESIANPOINT((1.2311557789E-5,2.5E-5));
#5411=IFCCARTESIANPOINT((9.942251109,3.4165594378,9.999999999E-5));
#5412=IFCCARTESIANPOINT((1.8592964824E-5,1.25E-5));
#5413=IFCCARTESIANPOINT((11.9346832794,3.4165594378,9.9999999992E-5));
#5414=IFCCARTESIANPOINT((2.4874371859E-5,0.));
#5415=IFCCARTESIANPOINT((13.9271154499,3.4165594378,9.9999999994E-5));
#5416=IFCCARTESIANPOINT((3.743718593E-5,0.));
#5417=IFCCARTESIANPOINT((14.9233315351,5.1420563127,9.9999999999E-5));
#5418=IFCCARTESIANPOINT((5.E-5,0.));
#5419=IFCCARTESIANPOINT((15.9195476203,6.8675531877,0.000100000000003));
#5420=IFCCARTESIANPOINT((6.256281407E-5,0.));
#5421=IFCCARTESIANPOINT((16.9157637056,8.5930500626,0.000100000000008));
#5422=IFCCARTESIANPOINT((0.,0.,9.144));
#5423=IFCCARTESIANPOINT((14.6172,-36.0816,-9.1441));
#5424=IFCCARTESIANPOINT((1.97252125302,3.4165582637,9.8825846767E-5));
#5425=IFCCARTESIANPOINT((0.,6.6666666685E-5,9.9999999999E-5));
#5426=IFCCARTESIANPOINT((0.,5.0000666667,9.9999999999E-5));
#5427=IFCCARTESIANPOINT((0.,10.0000666667,9.9999999999E-5));
#5428=IFCCARTESIANPOINT((0.,15.0000666667,9.9999999999E-5));
#5429=IFCCARTESIANPOINT((5.,6.6666666685E-5,9.9999999999E-5));
#5430=IFCCARTESIANPOINT((5.,5.0000666667,9.9999999999E-5));
#5431=IFCCARTESIANPOINT((5.,10.0000666667,9.9999999999E-5));
#5432=IFCCARTESIANPOINT((5.,15.0000666667,9.9999999999E-5));
#5433=IFCCARTESIANPOINT((10.,6.6666666685E-5,0.000100000000017));
#5434=IFCCARTESIANPOINT((10.,5.0000666667,9.9999999999E-5));
#5435=IFCCARTESIANPOINT((10.,10.0000666667,9.9999999999E-5));
#5436=IFCCARTESIANPOINT((10.,15.0000666667,9.9999999999E-5));
#5437=IFCCARTESIANPOINT((15.,6.6666666615E-5,0.000100000000017));
#5438=IFCCARTESIANPOINT((15.,5.0000666667,9.9999999999E-5));
#5439=IFCCARTESIANPOINT((15.,10.0000666667,9.9999999999E-5));
#5440=IFCCARTESIANPOINT((15.,15.0000666667,9.9999999999E-5));
#5441=IFCCARTESIANPOINT((0.,0.));
#5442=IFCCARTESIANPOINT((0.0001,0.));
#5443=IFCCARTESIANPOINT((0.0001,0.0001));
#5444=IFCCARTESIANPOINT((0.,0.0001));
#5445=IFCCARTESIANPOINT((-7.5,-8.3334,-9.1441));
#5446=IFCCARTESIANPOINT((6.4206,11.8574600579,5.8675833085));
#5447=IFCCARTESIANPOINT((4.9637977381,11.8574600579,5.8675833085));
#5448=IFCCARTESIANPOINT((3.702169971,11.129058927,5.8675833085));
#5449=IFCCARTESIANPOINT((2.44054220395,10.4006577961,5.8675833085));
#5450=IFCCARTESIANPOINT((1.712141073,9.139030029,5.8675833085));
#5451=IFCCARTESIANPOINT((0.98373994206,7.8774022619,5.8675833085));
#5452=IFCCARTESIANPOINT((0.98373994206,6.4206,5.8675833085));
#5453=IFCCARTESIANPOINT((0.98373994206,4.9637977381,5.8675833085));
#5454=IFCCARTESIANPOINT((1.712141073,3.702169971,5.8675833085));
#5455=IFCCARTESIANPOINT((2.44054220395,2.44054220395,5.8675833085));
#5456=IFCCARTESIANPOINT((3.702169971,1.712141073,5.8675833085));
#5457=IFCCARTESIANPOINT((4.9637977381,0.98373994206,5.8675833085));
#5458=IFCCARTESIANPOINT((6.4206,0.98373994206,5.8675833085));
#5459=IFCCARTESIANPOINT((7.8774022619,0.98373994206,5.8675833085));
#5460=IFCCARTESIANPOINT((9.139030029,1.712141073,5.8675833085));
#5461=IFCCARTESIANPOINT((10.4006577961,2.44054220395,5.8675833085));
#5462=IFCCARTESIANPOINT((11.129058927,3.702169971,5.8675833085));
#5463=IFCCARTESIANPOINT((11.8574600579,4.9637977381,5.8675833085));
#5464=IFCCARTESIANPOINT((11.8574600579,6.4206,5.8675833085));
#5465=IFCCARTESIANPOINT((11.8574600579,7.8774022619,5.8675833085));
#5466=IFCCARTESIANPOINT((11.129058927,9.139030029,5.8675833085));
#5467=IFCCARTESIANPOINT((10.4006577961,10.4006577961,5.8675833085));
#5468=IFCCARTESIANPOINT((9.139030029,11.129058927,5.8675833085));
#5469=IFCCARTESIANPOINT((7.8774022619,11.8574600579,5.8675833085));
#5470=IFCCARTESIANPOINT((6.4206,12.1538858098,4.2138225685));
#5471=IFCCARTESIANPOINT((4.8843706973,12.1538858098,4.2138225685));
#5472=IFCCARTESIANPOINT((3.5539570951,11.3857711585,4.2138225685));
#5473=IFCCARTESIANPOINT((2.22354349289,10.6176565071,4.2138225685));
#5474=IFCCARTESIANPOINT((1.45542884153,9.2872429049,4.2138225685));
#5475=IFCCARTESIANPOINT((0.68731419017,7.9568293027,4.2138225685));
#5476=IFCCARTESIANPOINT((0.68731419017,6.4206,4.2138225685));
#5477=IFCCARTESIANPOINT((0.68731419017,4.8843706973,4.2138225685));
#5478=IFCCARTESIANPOINT((1.45542884153,3.5539570951,4.2138225685));
#5479=IFCCARTESIANPOINT((2.22354349289,2.22354349289,4.2138225685));
#5480=IFCCARTESIANPOINT((3.5539570951,1.45542884153,4.2138225685));
#5481=IFCCARTESIANPOINT((4.8843706973,0.68731419017,4.2138225685));
#5482=IFCCARTESIANPOINT((6.4206,0.68731419017,4.2138225685));
#5483=IFCCARTESIANPOINT((7.9568293027,0.68731419017,4.2138225685));
#5484=IFCCARTESIANPOINT((9.2872429049,1.45542884153,4.2138225685));
#5485=IFCCARTESIANPOINT((10.6176565071,2.22354349289,4.2138225685));
#5486=IFCCARTESIANPOINT((11.3857711585,3.5539570951,4.2138225685));
#5487=IFCCARTESIANPOINT((12.1538858098,4.8843706973,4.2138225685));
#5488=IFCCARTESIANPOINT((12.1538858098,6.4206,4.2138225685));
#5489=IFCCARTESIANPOINT((12.1538858098,7.9568293027,4.2138225685));
#5490=IFCCARTESIANPOINT((11.3857711585,9.2872429049,4.2138225685));
#5491=IFCCARTESIANPOINT((10.6176565071,10.6176565071,4.2138225685));
#5492=IFCCARTESIANPOINT((9.2872429049,11.3857711585,4.2138225685));
#5493=IFCCARTESIANPOINT((7.9568293027,12.1538858098,4.2138225685));
#5494=IFCCARTESIANPOINT((6.4206,12.8411453242,0.37959832929));
#5495=IFCCARTESIANPOINT((4.7002200654,12.8411453242,0.37959832929));
#5496=IFCCARTESIANPOINT((3.2103273379,11.9809553569,0.37959832929));
#5497=IFCCARTESIANPOINT((1.72043461042,11.1207653896,0.37959832929));
#5498=IFCCARTESIANPOINT((0.86024464313,9.6308726621,0.37959832929));
#5499=IFCCARTESIANPOINT((5.4675843886E-5,8.1409799346,0.37959832929));
#5500=IFCCARTESIANPOINT((5.4675843893E-5,6.4206,0.37959832929));
#5501=IFCCARTESIANPOINT((5.4675843982E-5,4.7002200654,0.37959832929));
#5502=IFCCARTESIANPOINT((0.86024464313,3.2103273379,0.37959832929));
#5503=IFCCARTESIANPOINT((1.72043461042,1.72043461042,0.37959832929));
#5504=IFCCARTESIANPOINT((3.2103273379,0.86024464313,0.37959832929));
#5505=IFCCARTESIANPOINT((4.7002200654,5.4675843886E-5,0.37959832929));
#5506=IFCCARTESIANPOINT((6.4206,5.4675843893E-5,0.37959832929));
#5507=IFCCARTESIANPOINT((8.1409799346,5.4675843982E-5,0.37959832929));
#5508=IFCCARTESIANPOINT((9.6308726621,0.86024464313,0.37959832929));
#5509=IFCCARTESIANPOINT((11.1207653896,1.72043461042,0.37959832929));
#5510=IFCCARTESIANPOINT((11.9809553569,3.2103273379,0.37959832929));
#5511=IFCCARTESIANPOINT((12.8411453242,4.7002200654,0.37959832929));
#5512=IFCCARTESIANPOINT((12.8411453242,6.4206,0.37959832929));
#5513=IFCCARTESIANPOINT((12.8411453242,8.1409799346,0.37959832929));
#5514=IFCCARTESIANPOINT((11.9809553569,9.6308726621,0.37959832929));
#5515=IFCCARTESIANPOINT((11.1207653896,11.1207653896,0.37959832929));
#5516=IFCCARTESIANPOINT((9.6308726621,11.9809553569,0.37959832929));
#5517=IFCCARTESIANPOINT((8.1409799346,12.8411453242,0.37959832929));
#5518=IFCCARTESIANPOINT((6.4206,8.7481962665,0.137613179166));
#5519=IFCCARTESIANPOINT((5.7969224601,8.7481962665,0.137613179166));
#5520=IFCCARTESIANPOINT((5.2568018667,8.4363574966,0.137613179166));
#5521=IFCCARTESIANPOINT((4.7166812734,8.1245187266,0.137613179166));
#5522=IFCCARTESIANPOINT((4.4048425034,7.5843981333,0.137613179165));
#5523=IFCCARTESIANPOINT((4.0930037335,7.0442775399,0.137613179166));
#5524=IFCCARTESIANPOINT((4.0930037335,6.4206,0.137613179166));
#5525=IFCCARTESIANPOINT((4.0930037335,5.7969224601,0.137613179166));
#5526=IFCCARTESIANPOINT((4.4048425034,5.2568018667,0.137613179165));
#5527=IFCCARTESIANPOINT((4.7166812734,4.7166812734,0.137613179166));
#5528=IFCCARTESIANPOINT((5.2568018667,4.4048425034,0.137613179166));
#5529=IFCCARTESIANPOINT((5.7969224601,4.0930037335,0.137613179166));
#5530=IFCCARTESIANPOINT((6.4206,4.0930037335,0.137613179166));
#5531=IFCCARTESIANPOINT((7.0442775399,4.0930037335,0.137613179166));
#5532=IFCCARTESIANPOINT((7.5843981333,4.4048425034,0.137613179166));
#5533=IFCCARTESIANPOINT((8.1245187266,4.7166812734,0.137613179166));
#5534=IFCCARTESIANPOINT((8.4363574966,5.2568018667,0.137613179166));
#5535=IFCCARTESIANPOINT((8.7481962665,5.7969224601,0.137613179166));
#5536=IFCCARTESIANPOINT((8.7481962665,6.4206,0.137613179166));
#5537=IFCCARTESIANPOINT((8.7481962665,7.0442775399,0.137613179166));
#5538=IFCCARTESIANPOINT((8.4363574966,7.5843981333,0.137613179166));
#5539=IFCCARTESIANPOINT((8.1245187266,8.1245187266,0.137613179165));
#5540=IFCCARTESIANPOINT((7.5843981333,8.4363574966,0.137613179166));
#5541=IFCCARTESIANPOINT((7.0442775399,8.7481962665,0.137613179165));
#5542=IFCCARTESIANPOINT((6.4206,6.4206,0.));
#5543=IFCCARTESIANPOINT((6.6470942439,6.6470942439,0.));
#5544=IFCCARTESIANPOINT((8.1552869252,12.2757459581,6.0745692359));
#5545=IFCCARTESIANPOINT((10.7675532769,10.7675532769,6.0745692359));
#5546=IFCCARTESIANPOINT((12.2757459581,8.1552869252,6.0745692359));
#5547=IFCCARTESIANPOINT((12.2757459581,5.1389015626,6.0745692359));
#5548=IFCCARTESIANPOINT((10.7675532769,2.52663521103,6.0745692359));
#5549=IFCCARTESIANPOINT((8.1552869252,1.01844252973,6.0745692359));
#5550=IFCCARTESIANPOINT((5.1389015626,1.01844252973,6.0745692359));
#5551=IFCCARTESIANPOINT((2.52663521103,2.52663521103,6.0745692359));
#5552=IFCCARTESIANPOINT((1.01844252973,5.1389015626,6.0745692359));
#5553=IFCCARTESIANPOINT((1.01844252973,8.1552869252,6.0745692359));
#5554=IFCCARTESIANPOINT((2.52663521103,10.7675532769,6.0745692359));
#5555=IFCCARTESIANPOINT((5.1389015626,12.2757459581,6.0745692359));
#5556=IFCCARTESIANPOINT((93.5794,-106.4206,-9.144));
#5557=IFCCARTESIANPOINT((0.39088843829,0.39088843829,0.));
#5558=IFCCARTESIANPOINT((9.16931344,5.2905394477,17.2845435453));
#5559=IFCCARTESIANPOINT((9.16931344,8.925207466,16.7610865496));
#5560=IFCCARTESIANPOINT((5.989122546,10.543111079,16.5280795843));
#5561=IFCCARTESIANPOINT((2.98342536817,8.825492268,16.7754473159));
#5562=IFCCARTESIANPOINT((2.67683347621,8.650288989,16.8006797117));
#5563=IFCCARTESIANPOINT((2.38931806657,8.4341469805,16.8318080144));
#5564=IFCCARTESIANPOINT((2.12933245661,8.1768163392,16.8688682146));
#5565=IFCCARTESIANPOINT((0.0106146387554,6.079734832,17.1708853397));
#5566=IFCCARTESIANPOINT((0.46751644245,3.1933137976,17.5865814296));
#5567=IFCCARTESIANPOINT((2.98342536817,1.75558662739,17.7936397748));
#5568=IFCCARTESIANPOINT((6.023375574,0.0183937734817,18.0438265175));
#5569=IFCCARTESIANPOINT((9.16931344,1.69501951494,17.8023625188));
#5570=IFCCARTESIANPOINT((9.16931344,5.3366238142,16.9645528399));
#5571=IFCCARTESIANPOINT((9.16931344,8.9656105095,16.4137135865));
#5572=IFCCARTESIANPOINT((5.9972594096,10.5952890013,16.1973947));
#5573=IFCCARTESIANPOINT((2.98342536817,8.876721403,16.4197334747));
#5574=IFCCARTESIANPOINT((2.67691270939,8.7017837059,16.4434296526));
#5575=IFCCARTESIANPOINT((2.38937988322,8.4858372815,16.4733080618));
#5576=IFCCARTESIANPOINT((2.12933245661,8.2525218921,16.3432002914));
#5577=IFCCARTESIANPOINT((0.008036077941,6.1275648563,16.8214194628));
#5578=IFCCARTESIANPOINT((0.47079282012,3.2426670183,17.2311623264));
#5579=IFCCARTESIANPOINT((2.98342536817,1.80507738116,17.4499965312));
#5580=IFCCARTESIANPOINT((6.0284872034,0.062542557742,17.7174137286));
#5581=IFCCARTESIANPOINT((9.16931344,1.74974028319,17.4634861149));
#5582=IFCCARTESIANPOINT((9.16931344,5.5357532359,15.6193914319));
#5583=IFCCARTESIANPOINT((9.16931344,9.1699748703,15.0837018433));
#5584=IFCCARTESIANPOINT((5.9852380484,10.7806704646,14.8326459167));
#5585=IFCCARTESIANPOINT((2.98342536817,9.0652108298,15.1024589725));
#5586=IFCCARTESIANPOINT((2.67679556098,8.890048863,15.129538355));
#5587=IFCCARTESIANPOINT((2.38928848241,8.6740176059,15.1626603933));
#5588=IFCCARTESIANPOINT((2.12933245661,8.4039329978,15.2918644449));
#5589=IFCCARTESIANPOINT((0.0118477542612,6.3235590197,15.5134380471));
#5590=IFCCARTESIANPOINT((0.46594881899,3.4390818986,15.9510189812));
#5591=IFCCARTESIANPOINT((2.98342536817,2.00253712521,16.1624689451));
#5592=IFCCARTESIANPOINT((6.0209359824,0.269388398587,16.4166172759));
#5593=IFCCARTESIANPOINT((9.16931344,1.93949554456,16.1695221205));
#5594=IFCCARTESIANPOINT((9.16931344,5.7246574504,14.269521272));
#5595=IFCCARTESIANPOINT((9.16931344,9.3374959399,13.6089224312));
#5596=IFCCARTESIANPOINT((5.9901511169,10.9475785105,13.3178672778));
#5597=IFCCARTESIANPOINT((2.98342536817,9.2397088828,13.6260551475));
#5598=IFCCARTESIANPOINT((2.67684350601,9.0655486366,13.6575958014));
#5599=IFCCARTESIANPOINT((2.38932589219,8.8506783695,13.6965746295));
#5600=IFCCARTESIANPOINT((2.12933245661,8.5987741669,13.7150350466));
#5601=IFCCARTESIANPOINT((0.0102883537865,6.5094220522,14.1235792485));
#5602=IFCCARTESIANPOINT((0.46793115239,3.6398751738,14.6452474657));
#5603=IFCCARTESIANPOINT((2.98342536817,2.21051901401,14.9066709992));
#5604=IFCCARTESIANPOINT((6.0240216231,0.48275344242,15.2228951018));
#5605=IFCCARTESIANPOINT((9.16931344,2.15098081528,14.9181278764));
#5606=IFCCARTESIANPOINT((9.16931344,5.8878118802,12.8042943504));
#5607=IFCCARTESIANPOINT((9.16931344,9.4663330667,11.9691484087));
#5608=IFCCARTESIANPOINT((5.9888571914,11.0587471956,11.5967120909));
#5609=IFCCARTESIANPOINT((2.98342536817,9.3678165978,11.9922924617));
#5610=IFCCARTESIANPOINT((2.67683088802,9.1953229351,12.0326196923));
#5611=IFCCARTESIANPOINT((2.38931604715,8.9825274348,12.0823542321));
#5612=IFCCARTESIANPOINT((2.12933245661,8.7281144827,12.1500476459));
#5613=IFCCARTESIANPOINT((0.0106988305932,6.6647432,12.6235464776));
#5614=IFCCARTESIANPOINT((0.46740942828,3.8230382364,13.2874544541));
#5615=IFCCARTESIANPOINT((2.98342536817,2.40758776462,13.6177725392));
#5616=IFCCARTESIANPOINT((6.0232089087,0.69748731056,14.0168034457));
#5617=IFCCARTESIANPOINT((9.16931344,2.34778240475,13.6315833847));
#5618=IFCCARTESIANPOINT((9.16931344,5.9479735463,11.9040044864));
#5619=IFCCARTESIANPOINT((9.16931344,9.4783877321,10.8892170875));
#5620=IFCCARTESIANPOINT((5.9890736624,11.049791867,10.4374174024));
#5621=IFCCARTESIANPOINT((2.98342536817,9.3814712889,10.9170949372));
#5622=IFCCARTESIANPOINT((2.67683299943,9.2112941973,10.9660207825));
#5623=IFCCARTESIANPOINT((2.38931769457,9.0013534388,11.0263766195));
#5624=IFCCARTESIANPOINT((2.12933245661,8.7511295389,11.099405471));
#5625=IFCCARTESIANPOINT((0.0106301480889,6.7145207152,11.6837461195));
#5626=IFCCARTESIANPOINT((0.4674967291,3.9109169237,12.4897186433));
#5627=IFCCARTESIANPOINT((2.98342536817,2.51444052215,12.8911216457));
#5628=IFCCARTESIANPOINT((6.0233448708,0.82712232982,13.376117737));
#5629=IFCCARTESIANPOINT((9.16931344,2.45557904174,12.9080201299));
#5630=IFCCARTESIANPOINT((9.16931344,5.9712053409,11.0268728952));
#5631=IFCCARTESIANPOINT((9.16931344,9.4474754439,9.840361889));
#5632=IFCCARTESIANPOINT((5.9891662431,10.9949540141,9.3122761407));
#5633=IFCCARTESIANPOINT((2.98342536817,9.3521607643,9.8728825067));
#5634=IFCCARTESIANPOINT((2.67683390239,9.1845920815,9.9300686119));
#5635=IFCCARTESIANPOINT((2.3893183991,8.9778680349,10.0006188239));
#5636=IFCCARTESIANPOINT((2.12933245661,8.731994954,10.083455383));
#5637=IFCCARTESIANPOINT((0.0106007751142,6.7260179673,10.7691792359));
#5638=IFCCARTESIANPOINT((0.46753406395,3.9653633305,11.711354808));
#5639=IFCCARTESIANPOINT((2.98342536817,2.5902812162,12.1806957958));
#5640=IFCCARTESIANPOINT((6.0234030197,0.92875375586,12.747810491));
#5641=IFCCARTESIANPOINT((9.16931344,2.5323820321,12.2004765892));
#5642=IFCCARTESIANPOINT((9.16931344,5.9612297205,10.1626249993));
#5643=IFCCARTESIANPOINT((9.16931344,9.3777558474,8.8106260815));
#5644=IFCCARTESIANPOINT((5.9891489638,10.8986128137,8.2087159236));
#5645=IFCCARTESIANPOINT((2.98342536817,9.2840520441,8.8477165607));
#5646=IFCCARTESIANPOINT((2.67683373387,9.1193627197,8.9128943278));
#5647=IFCCARTESIANPOINT((2.3893182676,8.9161913496,8.9933007224));
#5648=IFCCARTESIANPOINT((2.12933245661,8.6743916017,9.0906893725));
#5649=IFCCARTESIANPOINT((0.0106062572572,6.7030600028,9.8691198739));
#5650=IFCCARTESIANPOINT((0.46752709584,3.9898533051,10.9428630878));
#5651=IFCCARTESIANPOINT((2.98342536817,2.63840803385,11.4776599005));
#5652=IFCCARTESIANPOINT((6.0233921667,1.00545356297,12.1238535172));
#5653=IFCCARTESIANPOINT((9.16931344,2.58149066857,11.5001615784));
#5654=IFCCARTESIANPOINT((9.16931344,5.845551425,8.8221054316));
#5655=IFCCARTESIANPOINT((9.16931344,9.194589465,7.3134175837));
#5656=IFCCARTESIANPOINT((5.9889647852,10.6850399375,6.6421455016));
#5657=IFCCARTESIANPOINT((2.98342536817,9.1025307701,7.3548221495));
#5658=IFCCARTESIANPOINT((2.67683193748,8.9411005381,7.4275259425));
#5659=IFCCARTESIANPOINT((2.38931686599,8.7419510682,7.5172209328));
#5660=IFCCARTESIANPOINT((2.12933245661,8.5042389059,7.6205051789));
#5661=IFCCARTESIANPOINT((0.0106646926311,6.57270755,8.4943641197));
#5662=IFCCARTESIANPOINT((0.46745282068,3.9132170388,9.6922700787));
#5663=IFCCARTESIANPOINT((2.98342536817,2.58851038543,10.2890344393));
#5664=IFCCARTESIANPOINT((6.023276487,0.987972935,11.0100659405));
#5665=IFCCARTESIANPOINT((9.16931344,2.53260950349,10.3142756814));
#5666=IFCCARTESIANPOINT((9.16931344,5.6527736552,7.4173689068));
#5667=IFCCARTESIANPOINT((9.16931344,8.9543899136,5.8115469727));
#5668=IFCCARTESIANPOINT((5.9897263658,10.425198844,5.0954945641));
#5669=IFCCARTESIANPOINT((2.98342536817,8.8645194916,5.8556511485));
#5670=IFCCARTESIANPOINT((2.67683936454,8.7053561594,5.9331532411));
#5671=IFCCARTESIANPOINT((2.38932266086,8.5089963833,6.0287491436));
#5672=IFCCARTESIANPOINT((2.12933245661,8.2773077347,6.1531881017));
#5673=IFCCARTESIANPOINT((0.0104230858371,6.3697653097,7.0694953333));
#5674=IFCCARTESIANPOINT((0.46775991424,3.7474704605,8.3456277183));
#5675=IFCCARTESIANPOINT((2.98342536817,2.44134462437,8.9808668706));
#5676=IFCCARTESIANPOINT((6.023754838,0.86280709833,9.748545952));
#5677=IFCCARTESIANPOINT((9.16931344,2.38667911315,9.0071909039));
#5678=IFCCARTESIANPOINT((9.16931344,5.4573360488,6.2604565163));
#5679=IFCCARTESIANPOINT((9.16931344,8.7498039608,4.6275193578));
#5680=IFCCARTESIANPOINT((5.9873374813,10.2120394273,3.9047767372));
#5681=IFCCARTESIANPOINT((2.98342536817,8.6573376158,4.6719103775));
#5682=IFCCARTESIANPOINT((2.67681605885,8.4986618348,4.7502829339));
#5683=IFCCARTESIANPOINT((2.38930447645,8.3029281819,4.8470266995));
#5684=IFCCARTESIANPOINT((2.12933245661,8.0644688845,4.9353479781));
#5685=IFCCARTESIANPOINT((0.0111811587045,6.1718058642,5.9029969298));
#5686=IFCCARTESIANPOINT((0.46679632932,3.5581336367,7.1966951423));
#5687=IFCCARTESIANPOINT((2.98342536817,2.2561839446,7.842512697));
#5688=IFCCARTESIANPOINT((6.0222544956,0.68410917989,8.6225028999));
#5689=IFCCARTESIANPOINT((9.16931344,2.20030544162,7.8711684257));
#5690=IFCCARTESIANPOINT((9.16931344,5.1358463974,4.4384805282));
#5691=IFCCARTESIANPOINT((9.16931344,8.4181332505,2.8187281831));
#5692=IFCCARTESIANPOINT((5.995739955,9.8911218593,2.0799035238));
#5693=IFCCARTESIANPOINT((2.98342536817,8.3362747688,2.86617003259));
#5694=IFCCARTESIANPOINT((2.67689791679,8.1779961175,2.94583847495));
#5695=IFCCARTESIANPOINT((2.38936834137,7.9826509075,3.04383766758));
#5696=IFCCARTESIANPOINT((2.12933245661,7.7662079995,3.2373844275));
#5697=IFCCARTESIANPOINT((0.0085175486631,5.8506609365,4.1007027171));
#5698=IFCCARTESIANPOINT((0.47018153609,3.2408560984,5.4010372841));
#5699=IFCCARTESIANPOINT((2.98342536817,1.9409570811,6.0420546225));
#5700=IFCCARTESIANPOINT((6.0275332802,0.36634392313,6.8176787067));
#5701=IFCCARTESIANPOINT((9.16931344,1.88985310697,6.0627375887));
#5702=IFCCARTESIANPOINT((9.16931344,4.8646037536,2.89447794281));
#5703=IFCCARTESIANPOINT((9.16931344,8.1303554288,1.35590151291));
#5704=IFCCARTESIANPOINT((6.0226316367,9.6425200335,0.57029242351));
#5705=IFCCARTESIANPOINT((2.98342536817,8.0831792089,1.41935967999));
#5706=IFCCARTESIANPOINT((2.67715774327,7.9256860471,1.50291535758));
#5707=IFCCARTESIANPOINT((2.38957095876,7.730928073,1.60428015037));
#5708=IFCCARTESIANPOINT((2.12933245661,7.5527080765,2.03015638846));
#5709=IFCCARTESIANPOINT((4.4646382449E-5,5.5868400213,2.64415176233));
#5710=IFCCARTESIANPOINT((0.48094049083,2.9790905576,3.9481065487));
#5711=IFCCARTESIANPOINT((2.98342536817,1.67937256761,4.5581402403));
#5712=IFCCARTESIANPOINT((6.0444578415,0.088738770405,5.2996814927));
#5713=IFCCARTESIANPOINT((9.16931344,1.64243419905,4.547862933));
#5714=IFCCARTESIANPOINT((9.16931344,4.7702959266,2.36121754843));
#5715=IFCCARTESIANPOINT((9.16931344,8.058939536,0.72731362226));
#5716=IFCCARTESIANPOINT((5.989122546,9.5228169385,1.2242298527E-5));
#5717=IFCCARTESIANPOINT((2.98342536817,7.9687173349,0.77213891382));
#5718=IFCCARTESIANPOINT((2.67683347621,7.8101936016,0.85089860352));
#5719=IFCCARTESIANPOINT((2.38931806657,7.6146285509,0.94806161116));
#5720=IFCCARTESIANPOINT((2.12933245661,7.3817960709,1.06374027645));
#5721=IFCCARTESIANPOINT((0.0106146387555,5.4843590388,2.00644802604));
#5722=IFCCARTESIANPOINT((0.46751644245,2.87272847418,3.303990095));
#5723=IFCCARTESIANPOINT((2.98342536817,1.5718745182,3.950296183));
#5724=IFCCARTESIANPOINT((6.023375574,6.4342248382E-5,4.7312220366));
#5725=IFCCARTESIANPOINT((9.16931344,1.51707346173,3.9775231106));
#5726=IFCCARTESIANPOINT((9.6885211576,5.5901135575,18.2632720468));
#5727=IFCCARTESIANPOINT((9.509643121,5.5346987838,17.5942118536));
#5728=IFCCARTESIANPOINT((9.7738441871,5.9007241861,16.6491743521));
#5729=IFCCARTESIANPOINT((9.6659145366,6.0346993293,15.0423446636));
#5730=IFCCARTESIANPOINT((9.6943751066,6.2249651839,13.53750564));
#5731=IFCCARTESIANPOINT((9.6895437778,6.285437884,12.5793903062));
#5732=IFCCARTESIANPOINT((9.6876068999,6.3087264319,11.6501645017));
#5733=IFCCARTESIANPOINT((9.6882234804,6.298587798,10.7377485549));
#5734=IFCCARTESIANPOINT((9.6909244712,6.1780849486,9.323964981));
#5735=IFCCARTESIANPOINT((9.679216917,5.9671231384,7.8298471387));
#5736=IFCCARTESIANPOINT((9.7160404825,5.7827337154,6.6337408301));
#5737=IFCCARTESIANPOINT((9.5863776476,5.3694492425,4.64036384));
#5738=IFCCARTESIANPOINT((9.6885211576,5.0404115111,2.49492029313));
#5739=IFCCARTESIANPOINT((10.2371287485,5.9066508991,19.2974205527));
#5740=IFCCARTESIANPOINT((10.2371287485,5.3258222535,2.63619388786));
#5741=IFCCARTESIANPOINT((13.3659933103,11.7474151835,1.06019595395));
#5742=IFCCARTESIANPOINT((8.2512482563,13.1196391552,1.6866284434E-5));
#5743=IFCCARTESIANPOINT((3.1909740775,8.5230791151,0.8258544975));
#5744=IFCCARTESIANPOINT((2.86455576248,8.3579106756,0.91057083665));
#5745=IFCCARTESIANPOINT((2.55804127432,8.1523403663,1.01500958197));
#5746=IFCCARTESIANPOINT((0.0140904048104,7.2802137466,2.66345992259));
#5747=IFCCARTESIANPOINT((0.60171565218,3.6973366719,4.2523906634));
#5748=IFCCARTESIANPOINT((3.1775253605,1.67413979862,4.2073002519));
#5749=IFCCARTESIANPOINT((8.5810797797,9.1663878467E-5,6.7402394642));
#5750=IFCCARTESIANPOINT((13.518312628,2.23662038271,5.8640596426));
#5751=IFCCARTESIANPOINT((13.518312628,2.49896612909,26.2460110695));
#5752=IFCCARTESIANPOINT((8.5810797797,0.0262043161277,25.7057712867));
#5753=IFCCARTESIANPOINT((3.1775253605,1.86980411529,18.9512840652));
#5754=IFCCARTESIANPOINT((0.60171565218,4.1099450626,22.6347575269));
#5755=IFCCARTESIANPOINT((0.0140904048103,8.0705454887,22.7934959412));
#5756=IFCCARTESIANPOINT((2.55804127432,9.0297296084,18.0203967922));
#5757=IFCCARTESIANPOINT((2.86455576248,9.2569206829,17.9788859895));
#5758=IFCCARTESIANPOINT((3.1909740775,9.4394575272,17.9424691145));
#5759=IFCCARTESIANPOINT((8.2512482563,14.525304206,22.7708294167));
#5760=IFCCARTESIANPOINT((13.3659933103,13.0101630907,24.4324258475));
#5761=IFCCARTESIANPOINT((37.021,-13.0088,37.1934));
#5762=IFCCARTESIANPOINT((4.4646382454E-5,6.434224838E-5,1.224229853E-5));
#5763=IFCCARTESIANPOINT((7.7818,8.9856996494,13.4785));
#5764=IFCCARTESIANPOINT((7.7818,13.4784996494,0.000100000000035));
#5765=IFCCARTESIANPOINT((4.2131754709E-5,13.4784996494,9.999999993E-5));
#5766=IFCCARTESIANPOINT((3.8909210659,6.7392996494,0.000100000000035));
#5767=IFCCARTESIANPOINT((7.7818,9.9649401894E-5,0.00010000000007));
#5768=IFCCARTESIANPOINT((11.6726789341,6.7392996494,0.000100000000035));
#5769=IFCCARTESIANPOINT((15.5635578682,13.4784996494,0.000100000000035));
#5770=IFCCARTESIANPOINT((15.5636,17.9713992988,26.957));
#5771=IFCCARTESIANPOINT((31.1271157365,26.9569992988,0.00020000000007));
#5772=IFCCARTESIANPOINT((15.5636,0.000199298803788,0.00020000000014));
#5773=IFCCARTESIANPOINT((8.4263509419E-5,26.9569992988,0.00019999999986));
#5774=IFCCARTESIANPOINT((71.711,-83.9857,-9.1441));
#5775=IFCCARTESIANPOINT((3.289,4.4928996494,0.000100000000035));
#5776=IFCCARTESIANPOINT((3.5623730952E-5,3.5623730946E-5,3.5937));
#5777=IFCCARTESIANPOINT((12.0000356237,3.5623731016E-5,3.5937));
#5778=IFCCARTESIANPOINT((3.5623730952E-5,4.3914612132,2.32602199022));
#5779=IFCCARTESIANPOINT((12.0000356237,4.3914612132,2.32602199022));
#5780=IFCCARTESIANPOINT((3.5623730952E-5,12.448897358,7.2385040252E-5));
#5781=IFCCARTESIANPOINT((12.0000356237,12.448897358,7.2385040321E-5));
#5782=IFCCARTESIANPOINT((3.5623731022E-5,12.448897358,16.187327615));
#5783=IFCCARTESIANPOINT((12.0000356237,12.448897358,16.187327615));
#5784=IFCCARTESIANPOINT((3.5623730952E-5,4.3914612132,13.8613780098));
#5785=IFCCARTESIANPOINT((12.0000356237,4.3914612132,13.8613780098));
#5786=IFCCARTESIANPOINT((3.5623730952E-5,3.5623730946E-5,12.5937));
#5787=IFCCARTESIANPOINT((12.0000356237,3.5623731016E-5,12.5937));
#5788=IFCCARTESIANPOINT((3.5623730952E-5,0.200815243014,3.535751728));
#5789=IFCCARTESIANPOINT((1.0752688172E-6,0.));
#5790=IFCCARTESIANPOINT((3.5623730952E-5,0.40154402307,3.477884238));
#5791=IFCCARTESIANPOINT((3.5623730952E-5,0.60217112467,3.4201783121));
#5792=IFCCARTESIANPOINT((2.1505376344E-6,0.));
#5793=IFCCARTESIANPOINT((3.5623730952E-5,0.80264570858,3.3627147325));
#5794=IFCCARTESIANPOINT((3.5623730952E-5,1.00291693559,3.3055742812));
#5795=IFCCARTESIANPOINT((3.225806452E-6,0.));
#5796=IFCCARTESIANPOINT((3.5623730952E-5,1.20293396645,3.2488377401));
#5797=IFCCARTESIANPOINT((3.5623730952E-5,1.40264596194,3.1925858916));
#5798=IFCCARTESIANPOINT((4.301075269E-6,0.));
#5799=IFCCARTESIANPOINT((3.5623730952E-5,1.60200208284,3.13689951746));
#5800=IFCCARTESIANPOINT((3.5623730952E-5,1.80095148992,3.08185939991));
#5801=IFCCARTESIANPOINT((5.376344086E-6,0.));
#5802=IFCCARTESIANPOINT((3.5623730952E-5,1.99944334394,3.02754632099));
#5803=IFCCARTESIANPOINT((3.5623730952E-5,2.19742680568,2.97404106276));
#5804=IFCCARTESIANPOINT((6.451612903E-6,0.));
#5805=IFCCARTESIANPOINT((3.5623730952E-5,2.39485103591,2.9214244073));
#5806=IFCCARTESIANPOINT((3.5623730952E-5,2.59166519541,2.86977713667));
#5807=IFCCARTESIANPOINT((7.52688172E-6,0.));
#5808=IFCCARTESIANPOINT((3.5623730952E-5,2.78781844494,2.81918003295));
#5809=IFCCARTESIANPOINT((3.5623730952E-5,2.98325994529,2.76971387819));
#5810=IFCCARTESIANPOINT((8.602150538E-6,0.));
#5811=IFCCARTESIANPOINT((3.5623730952E-5,3.1779388572,2.72145945448));
#5812=IFCCARTESIANPOINT((3.5623730952E-5,3.3718043415,2.67449754388));
#5813=IFCCARTESIANPOINT((9.677419355E-6,0.));
#5814=IFCCARTESIANPOINT((3.5623730952E-5,3.5648055589,2.62890892846));
#5815=IFCCARTESIANPOINT((3.5623730952E-5,3.7568916702,2.58477439029));
#5816=IFCCARTESIANPOINT((1.0752688172E-5,0.));
#5817=IFCCARTESIANPOINT((3.5623730952E-5,3.9480118361,2.54217471144));
#5818=IFCCARTESIANPOINT((3.5623730952E-5,4.1381152175,2.50119067398));
#5819=IFCCARTESIANPOINT((1.1827956989E-5,0.));
#5820=IFCCARTESIANPOINT((3.5623730952E-5,4.3271509751,2.46190305998));
#5821=IFCCARTESIANPOINT((3.5623730952E-5,4.5150682697,2.4243926515));
#5822=IFCCARTESIANPOINT((1.2903225806E-5,0.));
#5823=IFCCARTESIANPOINT((3.5623730953E-5,4.7018162621,2.38874023063));
#5824=IFCCARTESIANPOINT((1.3440860215E-5,0.));
#5825=IFCCARTESIANPOINT((3.5623730953E-5,4.887344113,2.35502657942));
#5826=IFCCARTESIANPOINT((3.5623730953E-5,5.0716009831,2.32333247994));
#5827=IFCCARTESIANPOINT((1.4516129032E-5,0.));
#5828=IFCCARTESIANPOINT((3.5623730953E-5,5.2545360334,2.29373871427));
#5829=IFCCARTESIANPOINT((3.5623730953E-5,5.4360984245,2.26632606448));
#5830=IFCCARTESIANPOINT((1.5591397849E-5,0.));
#5831=IFCCARTESIANPOINT((3.5623730953E-5,5.6162373172,2.24117531263));
#5832=IFCCARTESIANPOINT((3.5623730953E-5,5.7949018723,2.2183672408));
#5833=IFCCARTESIANPOINT((1.6666666667E-5,0.));
#5834=IFCCARTESIANPOINT((3.5623730953E-5,5.9720412505,2.19798263105));
#5835=IFCCARTESIANPOINT((3.5623730953E-5,6.1476046127,2.18010226546));
#5836=IFCCARTESIANPOINT((1.7741935484E-5,0.));
#5837=IFCCARTESIANPOINT((3.5623730954E-5,6.3215411196,2.16480692608));
#5838=IFCCARTESIANPOINT((3.5623730954E-5,6.493799932,2.152177395));
#5839=IFCCARTESIANPOINT((1.8817204301E-5,0.));
#5840=IFCCARTESIANPOINT((3.5623730954E-5,6.6643302106,2.14229445428));
#5841=IFCCARTESIANPOINT((3.5623730954E-5,6.8330811162,2.13523888599));
#5842=IFCCARTESIANPOINT((1.9892473118E-5,0.));
#5843=IFCCARTESIANPOINT((3.5623730954E-5,7.0000018097,2.1310914722));
#5844=IFCCARTESIANPOINT((3.5623730955E-5,7.1650414517,2.12993299498));
#5845=IFCCARTESIANPOINT((2.0967741935E-5,0.));
#5846=IFCCARTESIANPOINT((3.5623730955E-5,7.328149203,2.13184423641));
#5847=IFCCARTESIANPOINT((3.5623730955E-5,7.4892742244,2.13690597853));
#5848=IFCCARTESIANPOINT((2.2043010753E-5,0.));
#5849=IFCCARTESIANPOINT((3.5623730955E-5,7.6483656767,2.14519900344));
#5850=IFCCARTESIANPOINT((3.5623730955E-5,7.8053727207,2.1568040932));
#5851=IFCCARTESIANPOINT((2.311827957E-5,0.));
#5852=IFCCARTESIANPOINT((3.5623730956E-5,7.9602445171,2.17180202987));
#5853=IFCCARTESIANPOINT((3.5623730956E-5,8.1129302267,2.19027359553));
#5854=IFCCARTESIANPOINT((3.5623730956E-5,8.2633790102,2.21229957225));
#5855=IFCCARTESIANPOINT((3.5623730957E-5,8.4115400285,2.23796074209));
#5856=IFCCARTESIANPOINT((3.5623730957E-5,8.5573624423,2.26733788712));
#5857=IFCCARTESIANPOINT((3.5623730957E-5,8.7007954124,2.30051178942));
#5858=IFCCARTESIANPOINT((2.6344086022E-5,0.));
#5859=IFCCARTESIANPOINT((3.5623730958E-5,8.8417880995,2.33756323106));
#5860=IFCCARTESIANPOINT((3.5623730958E-5,8.9802896645,2.3785729941));
#5861=IFCCARTESIANPOINT((2.7419354839E-5,0.));
#5862=IFCCARTESIANPOINT((3.5623730958E-5,9.116249268,2.42362186061));
#5863=IFCCARTESIANPOINT((3.5623730959E-5,9.249616071,2.47279061266));
#5864=IFCCARTESIANPOINT((2.8494623656E-5,0.));
#5865=IFCCARTESIANPOINT((3.5623730959E-5,9.3803392341,2.52616003232));
#5866=IFCCARTESIANPOINT((3.5623730959E-5,9.5083679181,2.58381090167));
#5867=IFCCARTESIANPOINT((2.9569892473E-5,0.));
#5868=IFCCARTESIANPOINT((3.562373096E-5,9.6336512837,2.64582400277));
#5869=IFCCARTESIANPOINT((3.562373096E-5,9.7561384919,2.71228011768));
#5870=IFCCARTESIANPOINT((3.064516129E-5,0.));
#5871=IFCCARTESIANPOINT((3.5623730961E-5,9.8757787032,2.78326002849));
#5872=IFCCARTESIANPOINT((3.5623730961E-5,9.9925210786,2.85884451726));
#5873=IFCCARTESIANPOINT((3.1720430108E-5,0.));
#5874=IFCCARTESIANPOINT((3.5623730962E-5,10.1063147788,2.93911436605));
#5875=IFCCARTESIANPOINT((3.5623730962E-5,10.2171089645,3.02415035694));
#5876=IFCCARTESIANPOINT((3.2795698925E-5,0.));
#5877=IFCCARTESIANPOINT((3.5623730963E-5,10.3248527965,3.114033272));
#5878=IFCCARTESIANPOINT((3.5623730963E-5,10.4294954356,3.2088438933));
#5879=IFCCARTESIANPOINT((3.3870967742E-5,0.));
#5880=IFCCARTESIANPOINT((3.5623730964E-5,10.5309860426,3.3086630029));
#5881=IFCCARTESIANPOINT((3.5623730964E-5,10.6292737782,3.4135713829));
#5882=IFCCARTESIANPOINT((3.4946236559E-5,0.));
#5883=IFCCARTESIANPOINT((3.5623730965E-5,10.7243078032,3.5236498153));
#5884=IFCCARTESIANPOINT((3.5623730966E-5,10.8160377701,3.638976429));
#5885=IFCCARTESIANPOINT((3.6021505376E-5,0.));
#5886=IFCCARTESIANPOINT((3.5623730966E-5,10.9044339812,3.7595179319));
#5887=IFCCARTESIANPOINT((3.5623730967E-5,10.9894944862,3.8850913132));
#5888=IFCCARTESIANPOINT((3.5623730968E-5,11.0712192851,4.015503039));
#5889=IFCCARTESIANPOINT((3.5623730968E-5,11.1496083779,4.1505595757));
#5890=IFCCARTESIANPOINT((3.5623730969E-5,11.2246617647,4.2900673894));
#5891=IFCCARTESIANPOINT((3.8709677419E-5,0.));
#5892=IFCCARTESIANPOINT((3.562373097E-5,11.2963794454,4.4338329463));
#5893=IFCCARTESIANPOINT((3.562373097E-5,11.36476142,4.5816627127));
#5894=IFCCARTESIANPOINT((3.9784946237E-5,0.));
#5895=IFCCARTESIANPOINT((3.5623730971E-5,11.4298076885,4.7333631548));
#5896=IFCCARTESIANPOINT((3.5623730972E-5,11.4915182509,4.8887407388));
#5897=IFCCARTESIANPOINT((4.0860215054E-5,0.));
#5898=IFCCARTESIANPOINT((3.5623730973E-5,11.5498931073,5.0476019308));
#5899=IFCCARTESIANPOINT((3.5623730973E-5,11.6049322576,5.2097531972));
#5900=IFCCARTESIANPOINT((4.1935483871E-5,0.));
#5901=IFCCARTESIANPOINT((3.5623730974E-5,11.6566357018,5.3750010042));
#5902=IFCCARTESIANPOINT((3.5623730975E-5,11.70500344,5.5431518179));
#5903=IFCCARTESIANPOINT((4.3010752688E-5,0.));
#5904=IFCCARTESIANPOINT((3.5623730976E-5,11.750035472,5.7140121045));
#5905=IFCCARTESIANPOINT((3.5623730976E-5,11.791731798,5.8873883304));
#5906=IFCCARTESIANPOINT((4.4086021505E-5,0.));
#5907=IFCCARTESIANPOINT((3.5623730977E-5,11.8300924179,6.0630869616));
#5908=IFCCARTESIANPOINT((3.5623730978E-5,11.8651173317,6.2409144645));
#5909=IFCCARTESIANPOINT((4.5161290323E-5,0.));
#5910=IFCCARTESIANPOINT((3.5623730979E-5,11.8968065395,6.4206773052));
#5911=IFCCARTESIANPOINT((3.5623730979E-5,11.9251600411,6.60218195));
#5912=IFCCARTESIANPOINT((4.623655914E-5,0.));
#5913=IFCCARTESIANPOINT((3.562373098E-5,11.9501778367,6.785234865));
#5914=IFCCARTESIANPOINT((3.5623730981E-5,11.9718599262,6.9696425165));
#5915=IFCCARTESIANPOINT((4.7311827957E-5,0.));
#5916=IFCCARTESIANPOINT((3.5623730982E-5,11.9902063096,7.1552113708));
#5917=IFCCARTESIANPOINT((3.5623730982E-5,12.005216987,7.3417478939));
#5918=IFCCARTESIANPOINT((4.8387096774E-5,0.));
#5919=IFCCARTESIANPOINT((3.5623730983E-5,12.0168919583,7.5290585522));
#5920=IFCCARTESIANPOINT((3.5623730984E-5,12.0252312235,7.7169498118));
#5921=IFCCARTESIANPOINT((3.5623730984E-5,12.0302347826,7.905228139));
#5922=IFCCARTESIANPOINT((3.5623730985E-5,12.0319026356,8.0937));
#5923=IFCCARTESIANPOINT((3.5623730986E-5,12.0302347826,8.282171861));
#5924=IFCCARTESIANPOINT((5.1075268817E-5,0.));
#5925=IFCCARTESIANPOINT((3.5623730986E-5,12.0252312235,8.4704501882));
#5926=IFCCARTESIANPOINT((3.5623730987E-5,12.0168919583,8.6583414478));
#5927=IFCCARTESIANPOINT((5.2150537634E-5,0.));
#5928=IFCCARTESIANPOINT((3.5623730988E-5,12.005216987,8.8456521061));
#5929=IFCCARTESIANPOINT((3.5623730988E-5,11.9902063096,9.0321886292));
#5930=IFCCARTESIANPOINT((5.3225806452E-5,0.));
#5931=IFCCARTESIANPOINT((3.5623730989E-5,11.9718599262,9.2177574835));
#5932=IFCCARTESIANPOINT((3.5623730989E-5,11.9501778367,9.402165135));
#5933=IFCCARTESIANPOINT((5.4301075269E-5,0.));
#5934=IFCCARTESIANPOINT((3.562373099E-5,11.9251600411,9.58521805));
#5935=IFCCARTESIANPOINT((3.562373099E-5,11.8968065395,9.7667226948));
#5936=IFCCARTESIANPOINT((5.5376344086E-5,0.));
#5937=IFCCARTESIANPOINT((3.5623730991E-5,11.8651173317,9.9464855355));
#5938=IFCCARTESIANPOINT((3.5623730991E-5,11.8300924179,10.1243130384));
#5939=IFCCARTESIANPOINT((5.6451612903E-5,0.));
#5940=IFCCARTESIANPOINT((3.5623730992E-5,11.791731798,10.3000116696));
#5941=IFCCARTESIANPOINT((3.5623730992E-5,11.750035472,10.4733878955));
#5942=IFCCARTESIANPOINT((5.752688172E-5,0.));
#5943=IFCCARTESIANPOINT((3.5623730992E-5,11.70500344,10.6442481821));
#5944=IFCCARTESIANPOINT((3.5623730993E-5,11.6566357018,10.8123989958));
#5945=IFCCARTESIANPOINT((5.8602150538E-5,0.));
#5946=IFCCARTESIANPOINT((3.5623730993E-5,11.6049322576,10.9776468028));
#5947=IFCCARTESIANPOINT((3.5623730993E-5,11.5498931073,11.1397980692));
#5948=IFCCARTESIANPOINT((5.9677419355E-5,0.));
#5949=IFCCARTESIANPOINT((3.5623730994E-5,11.4915182509,11.2986592612));
#5950=IFCCARTESIANPOINT((3.5623730994E-5,11.4298076885,11.4540368452));
#5951=IFCCARTESIANPOINT((6.0752688172E-5,0.));
#5952=IFCCARTESIANPOINT((3.5623730994E-5,11.36476142,11.6057372873));
#5953=IFCCARTESIANPOINT((3.5623730994E-5,11.2963794454,11.7535670537));
#5954=IFCCARTESIANPOINT((3.5623730994E-5,11.2246617647,11.8973326106));
#5955=IFCCARTESIANPOINT((3.5623730994E-5,11.1496083779,12.0368404243));
#5956=IFCCARTESIANPOINT((3.5623730994E-5,11.0712192851,12.171896961));
#5957=IFCCARTESIANPOINT((3.5623730994E-5,10.9894944862,12.3023086868));
#5958=IFCCARTESIANPOINT((6.3978494624E-5,0.));
#5959=IFCCARTESIANPOINT((3.5623730994E-5,10.9044339812,12.4278820681));
#5960=IFCCARTESIANPOINT((3.5623730994E-5,10.8160377701,12.548423571));
#5961=IFCCARTESIANPOINT((6.5053763441E-5,0.));
#5962=IFCCARTESIANPOINT((3.5623730994E-5,10.7243078032,12.6637501847));
#5963=IFCCARTESIANPOINT((3.5623730994E-5,10.6292737782,12.7738286171));
#5964=IFCCARTESIANPOINT((6.6129032258E-5,0.));
#5965=IFCCARTESIANPOINT((3.5623730993E-5,10.5309860426,12.8787369971));
#5966=IFCCARTESIANPOINT((3.5623730993E-5,10.4294954356,12.9785561067));
#5967=IFCCARTESIANPOINT((6.7204301075E-5,0.));
#5968=IFCCARTESIANPOINT((3.5623730993E-5,10.3248527965,13.073366728));
#5969=IFCCARTESIANPOINT((3.5623730992E-5,10.2171089645,13.1632496431));
#5970=IFCCARTESIANPOINT((6.8279569892E-5,0.));
#5971=IFCCARTESIANPOINT((3.5623730992E-5,10.1063147788,13.248285634));
#5972=IFCCARTESIANPOINT((3.5623730991E-5,9.9925210786,13.3285554827));
#5973=IFCCARTESIANPOINT((6.935483871E-5,0.));
#5974=IFCCARTESIANPOINT((3.5623730991E-5,9.8757787032,13.4041399715));
#5975=IFCCARTESIANPOINT((3.562373099E-5,9.7561384919,13.4751198823));
#5976=IFCCARTESIANPOINT((7.0430107527E-5,0.));
#5977=IFCCARTESIANPOINT((3.562373099E-5,9.6336512837,13.5415759972));
#5978=IFCCARTESIANPOINT((3.5623730989E-5,9.5083679181,13.6035890983));
#5979=IFCCARTESIANPOINT((7.1505376344E-5,0.));
#5980=IFCCARTESIANPOINT((3.5623730988E-5,9.3803392341,13.6612399677));
#5981=IFCCARTESIANPOINT((3.5623730988E-5,9.249616071,13.7146093873));
#5982=IFCCARTESIANPOINT((7.2580645161E-5,0.));
#5983=IFCCARTESIANPOINT((3.5623730987E-5,9.116249268,13.7637781394));
#5984=IFCCARTESIANPOINT((3.5623730986E-5,8.9802896645,13.8088270059));
#5985=IFCCARTESIANPOINT((7.3655913978E-5,0.));
#5986=IFCCARTESIANPOINT((3.5623730986E-5,8.8417880995,13.8498367689));
#5987=IFCCARTESIANPOINT((3.5623730985E-5,8.7007954124,13.8868882106));
#5988=IFCCARTESIANPOINT((3.5623730984E-5,8.5573624423,13.9200621129));
#5989=IFCCARTESIANPOINT((3.5623730983E-5,8.4115400285,13.9494392579));
#5990=IFCCARTESIANPOINT((3.5623730982E-5,8.2633790102,13.9751004278));
#5991=IFCCARTESIANPOINT((7.6344086022E-5,0.));
#5992=IFCCARTESIANPOINT((3.5623730981E-5,8.1129302267,13.9971264045));
#5993=IFCCARTESIANPOINT((3.5623730981E-5,7.9602445171,14.0155979701));
#5994=IFCCARTESIANPOINT((7.7419354839E-5,0.));
#5995=IFCCARTESIANPOINT((3.562373098E-5,7.8053727207,14.0305959068));
#5996=IFCCARTESIANPOINT((3.5623730979E-5,7.6483656767,14.0422009966));
#5997=IFCCARTESIANPOINT((7.8494623656E-5,0.));
#5998=IFCCARTESIANPOINT((3.5623730978E-5,7.4892742244,14.0504940215));
#5999=IFCCARTESIANPOINT((3.5623730977E-5,7.328149203,14.0555557636));
#6000=IFCCARTESIANPOINT((7.9569892473E-5,0.));
#6001=IFCCARTESIANPOINT((3.5623730976E-5,7.1650414517,14.057467005));
#6002=IFCCARTESIANPOINT((3.5623730975E-5,7.0000018097,14.0563085278));
#6003=IFCCARTESIANPOINT((8.064516129E-5,0.));
#6004=IFCCARTESIANPOINT((3.5623730974E-5,6.8330811162,14.052161114));
#6005=IFCCARTESIANPOINT((3.5623730973E-5,6.6643302106,14.0451055457));
#6006=IFCCARTESIANPOINT((8.1720430108E-5,0.));
#6007=IFCCARTESIANPOINT((3.5623730973E-5,6.493799932,14.035222605));
#6008=IFCCARTESIANPOINT((3.5623730972E-5,6.3215411196,14.0225930739));
#6009=IFCCARTESIANPOINT((8.2795698925E-5,0.));
#6010=IFCCARTESIANPOINT((3.5623730971E-5,6.1476046127,14.0072977345));
#6011=IFCCARTESIANPOINT((3.562373097E-5,5.9720412505,13.9894173689));
#6012=IFCCARTESIANPOINT((8.3870967742E-5,0.));
#6013=IFCCARTESIANPOINT((3.5623730969E-5,5.7949018723,13.9690327592));
#6014=IFCCARTESIANPOINT((3.5623730968E-5,5.6162373172,13.9462246874));
#6015=IFCCARTESIANPOINT((8.4946236559E-5,0.));
#6016=IFCCARTESIANPOINT((3.5623730967E-5,5.4360984245,13.9210739355));
#6017=IFCCARTESIANPOINT((3.5623730966E-5,5.2545360334,13.8936612857));
#6018=IFCCARTESIANPOINT((8.6021505376E-5,0.));
#6019=IFCCARTESIANPOINT((3.5623730965E-5,5.0716009831,13.8640675201));
#6020=IFCCARTESIANPOINT((3.5623730965E-5,4.887344113,13.8323734206));
#6021=IFCCARTESIANPOINT((8.7096774194E-5,0.));
#6022=IFCCARTESIANPOINT((3.5623730964E-5,4.7018162621,13.7986597694));
#6023=IFCCARTESIANPOINT((3.5623730963E-5,4.5150682697,13.7630073485));
#6024=IFCCARTESIANPOINT((8.8172043011E-5,0.));
#6025=IFCCARTESIANPOINT((3.5623730962E-5,4.3271509751,13.72549694));
#6026=IFCCARTESIANPOINT((8.8709677419E-5,0.));
#6027=IFCCARTESIANPOINT((3.5623730961E-5,4.1381152175,13.686209326));
#6028=IFCCARTESIANPOINT((3.562373096E-5,3.9480118361,13.6452252886));
#6029=IFCCARTESIANPOINT((8.9784946237E-5,0.));
#6030=IFCCARTESIANPOINT((3.562373096E-5,3.7568916702,13.6026256097));
#6031=IFCCARTESIANPOINT((3.5623730959E-5,3.5648055589,13.5584910715));
#6032=IFCCARTESIANPOINT((9.0860215054E-5,0.));
#6033=IFCCARTESIANPOINT((3.5623730958E-5,3.3718043415,13.5129024561));
#6034=IFCCARTESIANPOINT((3.5623730958E-5,3.1779388572,13.4659405455));
#6035=IFCCARTESIANPOINT((9.1935483871E-5,0.));
#6036=IFCCARTESIANPOINT((3.5623730957E-5,2.98325994529,13.4176861218));
#6037=IFCCARTESIANPOINT((3.5623730956E-5,2.78781844494,13.3682199671));
#6038=IFCCARTESIANPOINT((9.3010752688E-5,0.));
#6039=IFCCARTESIANPOINT((3.5623730956E-5,2.59166519541,13.3176228633));
#6040=IFCCARTESIANPOINT((3.5623730955E-5,2.39485103591,13.2659755927));
#6041=IFCCARTESIANPOINT((9.4086021505E-5,0.));
#6042=IFCCARTESIANPOINT((3.5623730955E-5,2.19742680568,13.2133589372));
#6043=IFCCARTESIANPOINT((3.5623730954E-5,1.99944334394,13.159853679));
#6044=IFCCARTESIANPOINT((9.5161290323E-5,0.));
#6045=IFCCARTESIANPOINT((3.5623730954E-5,1.80095148992,13.1055406001));
#6046=IFCCARTESIANPOINT((3.5623730953E-5,1.60200208284,13.0505004825));
#6047=IFCCARTESIANPOINT((9.623655914E-5,0.));
#6048=IFCCARTESIANPOINT((3.5623730953E-5,1.40264596194,12.9948141084));
#6049=IFCCARTESIANPOINT((3.5623730953E-5,1.20293396645,12.9385622599));
#6050=IFCCARTESIANPOINT((9.7311827957E-5,0.));
#6051=IFCCARTESIANPOINT((3.5623730953E-5,1.00291693559,12.8818257188));
#6052=IFCCARTESIANPOINT((3.5623730952E-5,0.80264570858,12.8246852675));
#6053=IFCCARTESIANPOINT((9.8387096774E-5,0.));
#6054=IFCCARTESIANPOINT((3.5623730952E-5,0.60217112467,12.7672216879));
#6055=IFCCARTESIANPOINT((3.5623730952E-5,0.40154402307,12.709515762));
#6056=IFCCARTESIANPOINT((3.5623730952E-5,0.200815243014,12.651648272));
#6057=IFCCARTESIANPOINT((0.0001,2.5E-5));
#6058=IFCCARTESIANPOINT((3.00003562373,3.5623730964E-5,12.5937));
#6059=IFCCARTESIANPOINT((6.0000356237,3.5623730981E-5,12.5937));
#6060=IFCCARTESIANPOINT((0.0001,7.5E-5));
#6061=IFCCARTESIANPOINT((9.0000356237,3.5623730998E-5,12.5937));
#6062=IFCCARTESIANPOINT((12.0000356237,0.200815243014,12.651648272));
#6063=IFCCARTESIANPOINT((9.8924731183E-5,0.0001));
#6064=IFCCARTESIANPOINT((12.0000356237,0.40154402307,12.709515762));
#6065=IFCCARTESIANPOINT((12.0000356237,0.60217112467,12.7672216879));
#6066=IFCCARTESIANPOINT((9.7849462366E-5,0.0001));
#6067=IFCCARTESIANPOINT((12.0000356237,0.80264570858,12.8246852675));
#6068=IFCCARTESIANPOINT((12.0000356237,1.00291693559,12.8818257188));
#6069=IFCCARTESIANPOINT((9.6774193548E-5,0.0001));
#6070=IFCCARTESIANPOINT((12.0000356237,1.20293396645,12.9385622599));
#6071=IFCCARTESIANPOINT((9.623655914E-5,0.0001));
#6072=IFCCARTESIANPOINT((12.0000356237,1.40264596194,12.9948141084));
#6073=IFCCARTESIANPOINT((12.0000356237,1.60200208284,13.0505004825));
#6074=IFCCARTESIANPOINT((9.5161290323E-5,0.0001));
#6075=IFCCARTESIANPOINT((12.0000356237,1.80095148992,13.1055406001));
#6076=IFCCARTESIANPOINT((12.0000356237,1.99944334394,13.159853679));
#6077=IFCCARTESIANPOINT((9.4086021505E-5,0.0001));
#6078=IFCCARTESIANPOINT((12.0000356237,2.19742680568,13.2133589372));
#6079=IFCCARTESIANPOINT((12.0000356237,2.39485103591,13.2659755927));
#6080=IFCCARTESIANPOINT((9.3010752688E-5,0.0001));
#6081=IFCCARTESIANPOINT((12.0000356237,2.59166519541,13.3176228633));
#6082=IFCCARTESIANPOINT((9.247311828E-5,0.0001));
#6083=IFCCARTESIANPOINT((12.0000356237,2.78781844494,13.3682199671));
#6084=IFCCARTESIANPOINT((9.1935483871E-5,0.0001));
#6085=IFCCARTESIANPOINT((12.0000356237,2.98325994529,13.4176861218));
#6086=IFCCARTESIANPOINT((12.0000356237,3.1779388572,13.4659405455));
#6087=IFCCARTESIANPOINT((9.0860215054E-5,0.0001));
#6088=IFCCARTESIANPOINT((12.0000356237,3.3718043415,13.5129024561));
#6089=IFCCARTESIANPOINT((12.0000356237,3.5648055589,13.5584910715));
#6090=IFCCARTESIANPOINT((8.9784946237E-5,0.0001));
#6091=IFCCARTESIANPOINT((12.0000356237,3.7568916702,13.6026256097));
#6092=IFCCARTESIANPOINT((12.0000356237,3.9480118361,13.6452252886));
#6093=IFCCARTESIANPOINT((8.8709677419E-5,0.0001));
#6094=IFCCARTESIANPOINT((12.0000356237,4.1381152175,13.686209326));
#6095=IFCCARTESIANPOINT((12.0000356237,4.3271509751,13.72549694));
#6096=IFCCARTESIANPOINT((8.7634408602E-5,0.0001));
#6097=IFCCARTESIANPOINT((12.0000356237,4.5150682697,13.7630073485));
#6098=IFCCARTESIANPOINT((12.0000356237,4.7018162621,13.7986597694));
#6099=IFCCARTESIANPOINT((8.6559139785E-5,0.0001));
#6100=IFCCARTESIANPOINT((12.0000356237,4.887344113,13.8323734206));
#6101=IFCCARTESIANPOINT((8.6021505376E-5,0.0001));
#6102=IFCCARTESIANPOINT((12.0000356237,5.0716009831,13.8640675201));
#6103=IFCCARTESIANPOINT((12.0000356237,5.2545360334,13.8936612857));
#6104=IFCCARTESIANPOINT((8.4946236559E-5,0.0001));
#6105=IFCCARTESIANPOINT((12.0000356237,5.4360984245,13.9210739355));
#6106=IFCCARTESIANPOINT((12.0000356237,5.6162373172,13.9462246874));
#6107=IFCCARTESIANPOINT((8.3870967742E-5,0.0001));
#6108=IFCCARTESIANPOINT((12.0000356237,5.7949018723,13.9690327592));
#6109=IFCCARTESIANPOINT((12.0000356237,5.9720412505,13.9894173689));
#6110=IFCCARTESIANPOINT((8.2795698925E-5,0.0001));
#6111=IFCCARTESIANPOINT((12.0000356237,6.1476046127,14.0072977345));
#6112=IFCCARTESIANPOINT((12.0000356237,6.3215411196,14.0225930739));
#6113=IFCCARTESIANPOINT((8.1720430108E-5,0.0001));
#6114=IFCCARTESIANPOINT((12.0000356237,6.493799932,14.035222605));
#6115=IFCCARTESIANPOINT((12.0000356237,6.6643302106,14.0451055457));
#6116=IFCCARTESIANPOINT((8.064516129E-5,0.0001));
#6117=IFCCARTESIANPOINT((12.0000356237,6.8330811162,14.052161114));
#6118=IFCCARTESIANPOINT((8.0107526882E-5,0.0001));
#6119=IFCCARTESIANPOINT((12.0000356237,7.0000018097,14.0563085278));
#6120=IFCCARTESIANPOINT((7.9569892473E-5,0.0001));
#6121=IFCCARTESIANPOINT((12.0000356237,7.1650414517,14.057467005));
#6122=IFCCARTESIANPOINT((12.0000356237,7.328149203,14.0555557636));
#6123=IFCCARTESIANPOINT((7.8494623656E-5,0.0001));
#6124=IFCCARTESIANPOINT((12.0000356237,7.4892742244,14.0504940215));
#6125=IFCCARTESIANPOINT((12.0000356237,7.6483656767,14.0422009966));
#6126=IFCCARTESIANPOINT((7.7419354839E-5,0.0001));
#6127=IFCCARTESIANPOINT((12.0000356237,7.8053727207,14.0305959068));
#6128=IFCCARTESIANPOINT((12.0000356237,7.9602445171,14.0155979701));
#6129=IFCCARTESIANPOINT((7.6344086022E-5,0.0001));
#6130=IFCCARTESIANPOINT((12.0000356237,8.1129302267,13.9971264045));
#6131=IFCCARTESIANPOINT((12.0000356237,8.2633790102,13.9751004278));
#6132=IFCCARTESIANPOINT((12.0000356237,8.4115400285,13.9494392579));
#6133=IFCCARTESIANPOINT((12.0000356237,8.5573624423,13.9200621129));
#6134=IFCCARTESIANPOINT((12.0000356237,8.7007954124,13.8868882106));
#6135=IFCCARTESIANPOINT((7.3655913978E-5,0.0001));
#6136=IFCCARTESIANPOINT((12.0000356237,8.8417880995,13.8498367689));
#6137=IFCCARTESIANPOINT((12.0000356237,8.9802896645,13.8088270059));
#6138=IFCCARTESIANPOINT((7.2580645161E-5,0.0001));
#6139=IFCCARTESIANPOINT((12.0000356237,9.116249268,13.7637781394));
#6140=IFCCARTESIANPOINT((12.0000356237,9.249616071,13.7146093873));
#6141=IFCCARTESIANPOINT((7.1505376344E-5,0.0001));
#6142=IFCCARTESIANPOINT((12.0000356237,9.3803392341,13.6612399677));
#6143=IFCCARTESIANPOINT((12.0000356237,9.5083679181,13.6035890983));
#6144=IFCCARTESIANPOINT((7.0430107527E-5,0.0001));
#6145=IFCCARTESIANPOINT((12.0000356237,9.6336512837,13.5415759972));
#6146=IFCCARTESIANPOINT((12.0000356237,9.7561384919,13.4751198823));
#6147=IFCCARTESIANPOINT((6.935483871E-5,0.0001));
#6148=IFCCARTESIANPOINT((12.0000356237,9.8757787032,13.4041399715));
#6149=IFCCARTESIANPOINT((12.0000356237,9.9925210786,13.3285554827));
#6150=IFCCARTESIANPOINT((6.8279569892E-5,0.0001));
#6151=IFCCARTESIANPOINT((12.0000356237,10.1063147788,13.248285634));
#6152=IFCCARTESIANPOINT((12.0000356237,10.2171089645,13.1632496431));
#6153=IFCCARTESIANPOINT((6.7204301075E-5,0.0001));
#6154=IFCCARTESIANPOINT((12.0000356237,10.3248527965,13.073366728));
#6155=IFCCARTESIANPOINT((12.0000356237,10.4294954356,12.9785561067));
#6156=IFCCARTESIANPOINT((6.6129032258E-5,0.0001));
#6157=IFCCARTESIANPOINT((12.0000356237,10.5309860426,12.8787369971));
#6158=IFCCARTESIANPOINT((12.0000356237,10.6292737782,12.7738286171));
#6159=IFCCARTESIANPOINT((6.5053763441E-5,0.0001));
#6160=IFCCARTESIANPOINT((12.0000356237,10.7243078032,12.6637501847));
#6161=IFCCARTESIANPOINT((12.0000356237,10.8160377701,12.548423571));
#6162=IFCCARTESIANPOINT((6.3978494624E-5,0.0001));
#6163=IFCCARTESIANPOINT((12.0000356237,10.9044339812,12.4278820681));
#6164=IFCCARTESIANPOINT((12.0000356237,10.9894944862,12.3023086868));
#6165=IFCCARTESIANPOINT((12.0000356237,11.0712192851,12.171896961));
#6166=IFCCARTESIANPOINT((12.0000356237,11.1496083779,12.0368404243));
#6167=IFCCARTESIANPOINT((12.0000356237,11.2246617647,11.8973326106));
#6168=IFCCARTESIANPOINT((6.1290322581E-5,0.0001));
#6169=IFCCARTESIANPOINT((12.0000356237,11.2963794454,11.7535670537));
#6170=IFCCARTESIANPOINT((12.0000356237,11.36476142,11.6057372873));
#6171=IFCCARTESIANPOINT((6.0215053763E-5,0.0001));
#6172=IFCCARTESIANPOINT((12.0000356237,11.4298076885,11.4540368452));
#6173=IFCCARTESIANPOINT((5.9677419355E-5,0.0001));
#6174=IFCCARTESIANPOINT((12.0000356237,11.4915182509,11.2986592612));
#6175=IFCCARTESIANPOINT((12.0000356237,11.5498931073,11.1397980692));
#6176=IFCCARTESIANPOINT((5.8602150538E-5,0.0001));
#6177=IFCCARTESIANPOINT((12.0000356237,11.6049322576,10.9776468028));
#6178=IFCCARTESIANPOINT((12.0000356237,11.6566357018,10.8123989958));
#6179=IFCCARTESIANPOINT((5.752688172E-5,0.0001));
#6180=IFCCARTESIANPOINT((12.0000356237,11.70500344,10.6442481821));
#6181=IFCCARTESIANPOINT((12.0000356237,11.750035472,10.4733878955));
#6182=IFCCARTESIANPOINT((5.6451612903E-5,0.0001));
#6183=IFCCARTESIANPOINT((12.0000356237,11.791731798,10.3000116696));
#6184=IFCCARTESIANPOINT((12.0000356237,11.8300924179,10.1243130384));
#6185=IFCCARTESIANPOINT((5.5376344086E-5,0.0001));
#6186=IFCCARTESIANPOINT((12.0000356237,11.8651173317,9.9464855355));
#6187=IFCCARTESIANPOINT((12.0000356237,11.8968065395,9.7667226948));
#6188=IFCCARTESIANPOINT((5.4301075269E-5,0.0001));
#6189=IFCCARTESIANPOINT((12.0000356237,11.9251600411,9.58521805));
#6190=IFCCARTESIANPOINT((12.0000356237,11.9501778367,9.402165135));
#6191=IFCCARTESIANPOINT((5.3225806452E-5,0.0001));
#6192=IFCCARTESIANPOINT((12.0000356237,11.9718599262,9.2177574835));
#6193=IFCCARTESIANPOINT((12.0000356237,11.9902063096,9.0321886292));
#6194=IFCCARTESIANPOINT((5.2150537634E-5,0.0001));
#6195=IFCCARTESIANPOINT((12.0000356237,12.005216987,8.8456521061));
#6196=IFCCARTESIANPOINT((12.0000356237,12.0168919583,8.6583414478));
#6197=IFCCARTESIANPOINT((5.1075268817E-5,0.0001));
#6198=IFCCARTESIANPOINT((12.0000356237,12.0252312235,8.4704501882));
#6199=IFCCARTESIANPOINT((12.0000356237,12.0302347826,8.282171861));
#6200=IFCCARTESIANPOINT((12.0000356237,12.0319026356,8.0937));
#6201=IFCCARTESIANPOINT((12.0000356237,12.0302347826,7.905228139));
#6202=IFCCARTESIANPOINT((4.8924731183E-5,0.0001));
#6203=IFCCARTESIANPOINT((12.0000356237,12.0252312235,7.7169498118));
#6204=IFCCARTESIANPOINT((12.0000356237,12.0168919583,7.5290585522));
#6205=IFCCARTESIANPOINT((4.7849462366E-5,0.0001));
#6206=IFCCARTESIANPOINT((12.0000356237,12.005216987,7.3417478939));
#6207=IFCCARTESIANPOINT((4.7311827957E-5,0.0001));
#6208=IFCCARTESIANPOINT((12.0000356237,11.9902063096,7.1552113708));
#6209=IFCCARTESIANPOINT((12.0000356237,11.9718599262,6.9696425165));
#6210=IFCCARTESIANPOINT((4.623655914E-5,0.0001));
#6211=IFCCARTESIANPOINT((12.0000356237,11.9501778367,6.785234865));
#6212=IFCCARTESIANPOINT((12.0000356237,11.9251600411,6.60218195));
#6213=IFCCARTESIANPOINT((4.5161290323E-5,0.0001));
#6214=IFCCARTESIANPOINT((12.0000356237,11.8968065395,6.4206773052));
#6215=IFCCARTESIANPOINT((12.0000356237,11.8651173317,6.2409144645));
#6216=IFCCARTESIANPOINT((4.4086021505E-5,0.0001));
#6217=IFCCARTESIANPOINT((12.0000356237,11.8300924179,6.0630869616));
#6218=IFCCARTESIANPOINT((12.0000356237,11.791731798,5.8873883304));
#6219=IFCCARTESIANPOINT((4.3010752688E-5,0.0001));
#6220=IFCCARTESIANPOINT((12.0000356237,11.750035472,5.7140121045));
#6221=IFCCARTESIANPOINT((12.0000356237,11.70500344,5.5431518179));
#6222=IFCCARTESIANPOINT((4.1935483871E-5,0.0001));
#6223=IFCCARTESIANPOINT((12.0000356237,11.6566357018,5.3750010042));
#6224=IFCCARTESIANPOINT((12.0000356237,11.6049322576,5.2097531972));
#6225=IFCCARTESIANPOINT((4.0860215054E-5,0.0001));
#6226=IFCCARTESIANPOINT((12.0000356237,11.5498931073,5.0476019308));
#6227=IFCCARTESIANPOINT((12.0000356237,11.4915182509,4.8887407388));
#6228=IFCCARTESIANPOINT((3.9784946237E-5,0.0001));
#6229=IFCCARTESIANPOINT((12.0000356237,11.4298076885,4.7333631548));
#6230=IFCCARTESIANPOINT((12.0000356237,11.36476142,4.5816627127));
#6231=IFCCARTESIANPOINT((3.8709677419E-5,0.0001));
#6232=IFCCARTESIANPOINT((12.0000356237,11.2963794454,4.4338329463));
#6233=IFCCARTESIANPOINT((12.0000356237,11.2246617647,4.2900673894));
#6234=IFCCARTESIANPOINT((12.0000356237,11.1496083779,4.1505595757));
#6235=IFCCARTESIANPOINT((12.0000356237,11.0712192851,4.015503039));
#6236=IFCCARTESIANPOINT((12.0000356237,10.9894944862,3.8850913132));
#6237=IFCCARTESIANPOINT((3.6021505376E-5,0.0001));
#6238=IFCCARTESIANPOINT((12.0000356237,10.9044339812,3.7595179319));
#6239=IFCCARTESIANPOINT((12.0000356237,10.8160377701,3.638976429));
#6240=IFCCARTESIANPOINT((3.4946236559E-5,0.0001));
#6241=IFCCARTESIANPOINT((12.0000356237,10.7243078032,3.5236498153));
#6242=IFCCARTESIANPOINT((12.0000356237,10.6292737782,3.4135713829));
#6243=IFCCARTESIANPOINT((3.3870967742E-5,0.0001));
#6244=IFCCARTESIANPOINT((12.0000356237,10.5309860426,3.3086630029));
#6245=IFCCARTESIANPOINT((12.0000356237,10.4294954356,3.2088438933));
#6246=IFCCARTESIANPOINT((3.2795698925E-5,0.0001));
#6247=IFCCARTESIANPOINT((12.0000356237,10.3248527965,3.114033272));
#6248=IFCCARTESIANPOINT((12.0000356237,10.2171089645,3.02415035694));
#6249=IFCCARTESIANPOINT((3.1720430108E-5,0.0001));
#6250=IFCCARTESIANPOINT((12.0000356237,10.1063147788,2.93911436605));
#6251=IFCCARTESIANPOINT((12.0000356237,9.9925210786,2.85884451726));
#6252=IFCCARTESIANPOINT((3.064516129E-5,0.0001));
#6253=IFCCARTESIANPOINT((12.0000356237,9.8757787032,2.78326002849));
#6254=IFCCARTESIANPOINT((12.0000356237,9.7561384919,2.71228011768));
#6255=IFCCARTESIANPOINT((2.9569892473E-5,0.0001));
#6256=IFCCARTESIANPOINT((12.0000356237,9.6336512837,2.64582400277));
#6257=IFCCARTESIANPOINT((12.0000356237,9.5083679181,2.58381090167));
#6258=IFCCARTESIANPOINT((2.8494623656E-5,0.0001));
#6259=IFCCARTESIANPOINT((12.0000356237,9.3803392341,2.52616003232));
#6260=IFCCARTESIANPOINT((12.0000356237,9.249616071,2.47279061266));
#6261=IFCCARTESIANPOINT((2.7419354839E-5,0.0001));
#6262=IFCCARTESIANPOINT((12.0000356237,9.116249268,2.42362186061));
#6263=IFCCARTESIANPOINT((12.0000356237,8.9802896645,2.3785729941));
#6264=IFCCARTESIANPOINT((2.6344086022E-5,0.0001));
#6265=IFCCARTESIANPOINT((12.0000356237,8.8417880995,2.33756323106));
#6266=IFCCARTESIANPOINT((12.0000356237,8.7007954124,2.30051178942));
#6267=IFCCARTESIANPOINT((12.0000356237,8.5573624423,2.26733788712));
#6268=IFCCARTESIANPOINT((12.0000356237,8.4115400285,2.23796074209));
#6269=IFCCARTESIANPOINT((12.0000356237,8.2633790102,2.21229957225));
#6270=IFCCARTESIANPOINT((2.3655913978E-5,0.0001));
#6271=IFCCARTESIANPOINT((12.0000356237,8.1129302267,2.19027359553));
#6272=IFCCARTESIANPOINT((12.0000356237,7.9602445171,2.17180202987));
#6273=IFCCARTESIANPOINT((2.2580645161E-5,0.0001));
#6274=IFCCARTESIANPOINT((12.0000356237,7.8053727207,2.1568040932));
#6275=IFCCARTESIANPOINT((2.2043010753E-5,0.0001));
#6276=IFCCARTESIANPOINT((12.0000356237,7.6483656767,2.14519900344));
#6277=IFCCARTESIANPOINT((12.0000356237,7.4892742244,2.13690597853));
#6278=IFCCARTESIANPOINT((2.0967741935E-5,0.0001));
#6279=IFCCARTESIANPOINT((12.0000356237,7.328149203,2.13184423641));
#6280=IFCCARTESIANPOINT((12.0000356237,7.1650414517,2.12993299498));
#6281=IFCCARTESIANPOINT((1.9892473118E-5,0.0001));
#6282=IFCCARTESIANPOINT((12.0000356237,7.0000018097,2.1310914722));
#6283=IFCCARTESIANPOINT((12.0000356237,6.8330811162,2.13523888599));
#6284=IFCCARTESIANPOINT((1.8817204301E-5,0.0001));
#6285=IFCCARTESIANPOINT((12.0000356237,6.6643302106,2.14229445428));
#6286=IFCCARTESIANPOINT((12.0000356237,6.493799932,2.152177395));
#6287=IFCCARTESIANPOINT((1.7741935484E-5,0.0001));
#6288=IFCCARTESIANPOINT((12.0000356237,6.3215411196,2.16480692608));
#6289=IFCCARTESIANPOINT((1.7204301075E-5,0.0001));
#6290=IFCCARTESIANPOINT((12.0000356237,6.1476046127,2.18010226546));
#6291=IFCCARTESIANPOINT((1.6666666667E-5,0.0001));
#6292=IFCCARTESIANPOINT((12.0000356237,5.9720412505,2.19798263105));
#6293=IFCCARTESIANPOINT((12.0000356237,5.7949018723,2.2183672408));
#6294=IFCCARTESIANPOINT((1.5591397849E-5,0.0001));
#6295=IFCCARTESIANPOINT((12.0000356237,5.6162373172,2.24117531263));
#6296=IFCCARTESIANPOINT((12.0000356237,5.4360984245,2.26632606448));
#6297=IFCCARTESIANPOINT((1.4516129032E-5,0.0001));
#6298=IFCCARTESIANPOINT((12.0000356237,5.2545360334,2.29373871427));
#6299=IFCCARTESIANPOINT((12.0000356237,5.0716009831,2.32333247994));
#6300=IFCCARTESIANPOINT((1.3440860215E-5,0.0001));
#6301=IFCCARTESIANPOINT((12.0000356237,4.887344113,2.35502657942));
#6302=IFCCARTESIANPOINT((12.0000356237,4.7018162621,2.38874023063));
#6303=IFCCARTESIANPOINT((1.2365591398E-5,0.0001));
#6304=IFCCARTESIANPOINT((12.0000356237,4.5150682697,2.4243926515));
#6305=IFCCARTESIANPOINT((12.0000356237,4.3271509751,2.46190305998));
#6306=IFCCARTESIANPOINT((1.1290322581E-5,0.0001));
#6307=IFCCARTESIANPOINT((12.0000356237,4.1381152175,2.50119067398));
#6308=IFCCARTESIANPOINT((1.0752688172E-5,0.0001));
#6309=IFCCARTESIANPOINT((12.0000356237,3.9480118361,2.54217471144));
#6310=IFCCARTESIANPOINT((12.0000356237,3.7568916702,2.58477439029));
#6311=IFCCARTESIANPOINT((9.677419355E-6,0.0001));
#6312=IFCCARTESIANPOINT((12.0000356237,3.5648055589,2.62890892846));
#6313=IFCCARTESIANPOINT((12.0000356237,3.3718043415,2.67449754388));
#6314=IFCCARTESIANPOINT((8.602150538E-6,0.0001));
#6315=IFCCARTESIANPOINT((12.0000356237,3.1779388572,2.72145945448));
#6316=IFCCARTESIANPOINT((12.0000356237,2.98325994529,2.76971387819));
#6317=IFCCARTESIANPOINT((7.52688172E-6,0.0001));
#6318=IFCCARTESIANPOINT((12.0000356237,2.78781844494,2.81918003295));
#6319=IFCCARTESIANPOINT((12.0000356237,2.59166519541,2.86977713667));
#6320=IFCCARTESIANPOINT((6.451612903E-6,0.0001));
#6321=IFCCARTESIANPOINT((12.0000356237,2.39485103591,2.9214244073));
#6322=IFCCARTESIANPOINT((12.0000356237,2.19742680568,2.97404106276));
#6323=IFCCARTESIANPOINT((5.376344086E-6,0.0001));
#6324=IFCCARTESIANPOINT((12.0000356237,1.99944334394,3.02754632099));
#6325=IFCCARTESIANPOINT((4.838709677E-6,0.0001));
#6326=IFCCARTESIANPOINT((12.0000356237,1.80095148992,3.08185939991));
#6327=IFCCARTESIANPOINT((4.301075269E-6,0.0001));
#6328=IFCCARTESIANPOINT((12.0000356237,1.60200208284,3.13689951746));
#6329=IFCCARTESIANPOINT((12.0000356237,1.40264596194,3.1925858916));
#6330=IFCCARTESIANPOINT((3.225806452E-6,0.0001));
#6331=IFCCARTESIANPOINT((12.0000356237,1.20293396645,3.2488377401));
#6332=IFCCARTESIANPOINT((12.0000356237,1.00291693559,3.3055742812));
#6333=IFCCARTESIANPOINT((2.1505376344E-6,0.0001));
#6334=IFCCARTESIANPOINT((12.0000356237,0.80264570858,3.3627147325));
#6335=IFCCARTESIANPOINT((12.0000356237,0.60217112467,3.4201783121));
#6336=IFCCARTESIANPOINT((1.0752688172E-6,0.0001));
#6337=IFCCARTESIANPOINT((12.0000356237,0.40154402307,3.477884238));
#6338=IFCCARTESIANPOINT((12.0000356237,0.200815243014,3.535751728));
#6339=IFCCARTESIANPOINT((0.,7.5E-5));
#6340=IFCCARTESIANPOINT((9.0000356237,3.5623730998E-5,3.5937));
#6341=IFCCARTESIANPOINT((6.0000356237,3.5623730981E-5,3.5937));
#6342=IFCCARTESIANPOINT((0.,2.5E-5));
#6343=IFCCARTESIANPOINT((3.00003562373,3.5623730964E-5,3.5937));
#6344=IFCCARTESIANPOINT((4.4786249426E-5,7.5E-5));
#6345=IFCCARTESIANPOINT((9.0000356237,11.875052763,6.2950837693));
#6346=IFCCARTESIANPOINT((8.9971602324,11.8869509812,6.3625029991));
#6347=IFCCARTESIANPOINT((8.9885340585,11.8982262049,6.4292452509));
#6348=IFCCARTESIANPOINT((4.5382894554E-5,7.4784345652E-5));
#6349=IFCCARTESIANPOINT((8.974157102,11.9088974968,6.4952906192));
#6350=IFCCARTESIANPOINT((8.9540293628,11.9189836542,6.5606201431));
#6351=IFCCARTESIANPOINT((8.9281508411,11.928503209,6.6252157784));
#6352=IFCCARTESIANPOINT((8.8965215367,11.9374744278,6.6890603702));
#6353=IFCCARTESIANPOINT((8.8591414497,11.9459153118,6.7521376252));
#6354=IFCCARTESIANPOINT((4.6321931908E-5,7.346645797E-5));
#6355=IFCCARTESIANPOINT((8.8160105801,11.9538435968,6.8144320849));
#6356=IFCCARTESIANPOINT((8.7671289279,11.9612767534,6.8759290994));
#6357=IFCCARTESIANPOINT((8.7124964931,11.9682319866,6.9366148007));
#6358=IFCCARTESIANPOINT((4.6852131605E-5,7.2100647099E-5));
#6359=IFCCARTESIANPOINT((8.6521132756,11.9747262361,6.9964760778));
#6360=IFCCARTESIANPOINT((8.5859792756,11.9807761765,7.0555005507));
#6361=IFCCARTESIANPOINT((4.7153827211E-5,7.1090080194E-5));
#6362=IFCCARTESIANPOINT((8.530845247,11.9851607537,7.1005670746));
#6363=IFCCARTESIANPOINT((8.4768479456,11.988976063,7.141587705));
#6364=IFCCARTESIANPOINT((4.7387940154E-5,7.0169990038E-5));
#6365=IFCCARTESIANPOINT((8.4204344284,11.9925340407,7.1815647773));
#6366=IFCCARTESIANPOINT((8.3616046954,11.995847284,7.2204927423));
#6367=IFCCARTESIANPOINT((4.7609449824E-5,6.9169359357E-5));
#6368=IFCCARTESIANPOINT((8.3003587466,11.9989280462,7.2583664529));
#6369=IFCCARTESIANPOINT((8.2366965821,12.001788237,7.2951811437));
#6370=IFCCARTESIANPOINT((4.781835622E-5,6.808818815E-5));
#6371=IFCCARTESIANPOINT((8.1706182018,12.0044394223,7.3309324108));
#6372=IFCCARTESIANPOINT((8.1021236057,12.0068928244,7.365616192));
#6373=IFCCARTESIANPOINT((4.8014659342E-5,6.6926476417E-5));
#6374=IFCCARTESIANPOINT((8.0312127938,12.0091593219,7.3992287475));
#6375=IFCCARTESIANPOINT((7.9578857662,12.0112494495,7.4317666418));
#6376=IFCCARTESIANPOINT((4.8198359189E-5,6.5684224158E-5));
#6377=IFCCARTESIANPOINT((7.8821425227,12.0131733984,7.4632267256));
#6378=IFCCARTESIANPOINT((7.8039830635,12.014941016,7.4936061185));
#6379=IFCCARTESIANPOINT((4.8359733547E-5,6.4441861327E-5));
#6380=IFCCARTESIANPOINT((7.733058983,12.0163783196,7.5195096028));
#6381=IFCCARTESIANPOINT((4.8425250717E-5,6.3890865926E-5));
#6382=IFCCARTESIANPOINT((7.6669395348,12.0175937271,7.542375644));
#6383=IFCCARTESIANPOINT((7.6001637371,12.0187100474,7.5642575757));
#6384=IFCCARTESIANPOINT((4.8547758106E-5,6.2772466386E-5));
#6385=IFCCARTESIANPOINT((7.53273159,12.0197334479,7.5851540633));
#6386=IFCCARTESIANPOINT((7.4646430934,12.0206698163,7.6050638973));
#6387=IFCCARTESIANPOINT((4.8658896228E-5,6.1632188531E-5));
#6388=IFCCARTESIANPOINT((7.3958982474,12.0215247606,7.6239859847));
#6389=IFCCARTESIANPOINT((7.3264970519,12.0223036091,7.6419193403));
#6390=IFCCARTESIANPOINT((4.8758665081E-5,6.047003236E-5));
#6391=IFCCARTESIANPOINT((7.256439507,12.0230114104,7.6588630784));
#6392=IFCCARTESIANPOINT((7.1857256126,12.0236529334,7.6748164053));
#6393=IFCCARTESIANPOINT((4.8847064667E-5,5.9285997875E-5));
#6394=IFCCARTESIANPOINT((7.1143553688,12.0242326674,7.6897786117));
#6395=IFCCARTESIANPOINT((7.0423287755,12.0247548218,7.7037490659));
#6396=IFCCARTESIANPOINT((4.8924094984E-5,5.8080085075E-5));
#6397=IFCCARTESIANPOINT((6.9696458327,12.0252233267,7.7167272077));
#6398=IFCCARTESIANPOINT((6.8963065405,12.025641832,7.7287125417));
#6399=IFCCARTESIANPOINT((4.8989756034E-5,5.6852293959E-5));
#6400=IFCCARTESIANPOINT((6.8223108988,12.0260137082,7.7397046322));
#6401=IFCCARTESIANPOINT((6.7476589077,12.0263420462,7.7497030981));
#6402=IFCCARTESIANPOINT((4.9044047815E-5,5.5602624529E-5));
#6403=IFCCARTESIANPOINT((6.6723505672,12.026629657,7.7587076077));
#6404=IFCCARTESIANPOINT((6.5963858771,12.026879072,7.7667178748));
#6405=IFCCARTESIANPOINT((4.9086970328E-5,5.4331076783E-5));
#6406=IFCCARTESIANPOINT((6.5197648377,12.0270925429,7.7737336546));
#6407=IFCCARTESIANPOINT((6.4424874487,12.0272720416,7.7797547404));
#6408=IFCCARTESIANPOINT((4.9118523573E-5,5.3037650722E-5));
#6409=IFCCARTESIANPOINT((6.3645537104,12.0274192605,7.7847809603));
#6410=IFCCARTESIANPOINT((6.2859636225,12.0275356123,7.7888121751));
#6411=IFCCARTESIANPOINT((4.913870755E-5,5.1722346346E-5));
#6412=IFCCARTESIANPOINT((6.2067171853,12.0276222298,7.7918482755));
#6413=IFCCARTESIANPOINT((6.1268143985,12.0276799662,7.793889181));
#6414=IFCCARTESIANPOINT((4.9147522259E-5,5.0385163655E-5));
#6415=IFCCARTESIANPOINT((6.0462552623,12.0277093952,7.794934838));
#6416=IFCCARTESIANPOINT((5.9650611272,12.0277108104,7.7949852153));
#6417=IFCCARTESIANPOINT((4.9144970239E-5,4.9033941285E-5));
#6418=IFCCARTESIANPOINT((5.884108578,12.0276842514,7.7940412129));
#6419=IFCCARTESIANPOINT((5.8038338932,12.027629469,7.7921034135));
#6420=IFCCARTESIANPOINT((4.9131063982E-5,4.7701678743E-5));
#6421=IFCCARTESIANPOINT((5.7242370729,12.0275459194,7.7891718676));
#6422=IFCCARTESIANPOINT((5.645318117,12.02743278,7.785246653));
#6423=IFCCARTESIANPOINT((4.9105805082E-5,4.6392011682E-5));
#6424=IFCCARTESIANPOINT((5.5670770256,12.0272889493,7.7803278754));
#6425=IFCCARTESIANPOINT((5.4895137986,12.0271130468,7.7744156707));
#6426=IFCCARTESIANPOINT((4.9069193539E-5,4.5104940104E-5));
#6427=IFCCARTESIANPOINT((5.4126284362,12.0269034133,7.7675102068));
#6428=IFCCARTESIANPOINT((5.3364209381,12.0266581107,7.7596116862));
#6429=IFCCARTESIANPOINT((4.9021229352E-5,4.3840464007E-5));
#6430=IFCCARTESIANPOINT((5.2608913046,12.0263749219,7.7507203488));
#6431=IFCCARTESIANPOINT((5.1860395355,12.0260513511,7.7408364757));
#6432=IFCCARTESIANPOINT((4.8961912523E-5,4.2598583392E-5));
#6433=IFCCARTESIANPOINT((5.1118656308,12.0256846234,7.7299603927));
#6434=IFCCARTESIANPOINT((5.0383695906,12.0252716852,7.7180924746));
#6435=IFCCARTESIANPOINT((4.8891243051E-5,4.137929826E-5));
#6436=IFCCARTESIANPOINT((4.9655514149,12.0248092041,7.7052331498));
#6437=IFCCARTESIANPOINT((4.8934111036,12.0242935687,7.6913829058));
#6438=IFCCARTESIANPOINT((4.8809220936E-5,4.0182608609E-5));
#6439=IFCCARTESIANPOINT((4.8219486568,12.0237208885,7.6765422942));
#6440=IFCCARTESIANPOINT((4.7511640745,12.0230869947,7.660711937));
#6441=IFCCARTESIANPOINT((4.8715846178E-5,3.9008514441E-5));
#6442=IFCCARTESIANPOINT((4.6810573566,12.022387439,7.6438925328));
#6443=IFCCARTESIANPOINT((4.6116285032,12.0216174947,7.6260848637));
#6444=IFCCARTESIANPOINT((4.8611118776E-5,3.7857015754E-5));
#6445=IFCCARTESIANPOINT((4.5428775142,12.0207721559,7.6072898027));
#6446=IFCCARTESIANPOINT((4.4748043897,12.0198461381,7.5875083211));
#6447=IFCCARTESIANPOINT((4.8495038732E-5,3.6728112549E-5));
#6448=IFCCARTESIANPOINT((4.4074091296,12.0188338777,7.5667414966));
#6449=IFCCARTESIANPOINT((4.340691734,12.0177295323,7.544990522));
#6450=IFCCARTESIANPOINT((4.8367606045E-5,3.5621804827E-5));
#6451=IFCCARTESIANPOINT((4.2746522029,12.0165269807,7.522256714));
#6452=IFCCARTESIANPOINT((4.2092905362,12.0152198227,7.4985415228));
#6453=IFCCARTESIANPOINT((4.8214495346E-5,3.4433555924E-5));
#6454=IFCCARTESIANPOINT((4.1320623346,12.0135073878,7.4688520243));
#6455=IFCCARTESIANPOINT((4.0553942883,12.0115994876,7.4373763433));
#6456=IFCCARTESIANPOINT((4.8030730627E-5,3.3176287115E-5));
#6457=IFCCARTESIANPOINT((3.9811900776,12.0095260447,7.4048244979));
#6458=IFCCARTESIANPOINT((3.9094497025,12.0072768827,7.3711996175));
#6459=IFCCARTESIANPOINT((4.7834384681E-5,3.200114616E-5));
#6460=IFCCARTESIANPOINT((3.8401731629,12.0048414825,7.336505117));
#6461=IFCCARTESIANPOINT((3.773360459,12.0022089825,7.3007447138));
#6462=IFCCARTESIANPOINT((4.7625457507E-5,3.0908133058E-5));
#6463=IFCCARTESIANPOINT((3.7090115906,11.9993681788,7.2639224471));
#6464=IFCCARTESIANPOINT((3.6471265579,11.9963075247,7.2260426963));
#6465=IFCCARTESIANPOINT((4.7403949106E-5,2.9897247808E-5));
#6466=IFCCARTESIANPOINT((3.5877053607,11.9930151312,7.1871102009));
#6467=IFCCARTESIANPOINT((4.7288476945E-5,2.9422603128E-5));
#6468=IFCCARTESIANPOINT((3.5307479991,11.9894787667,7.1471300805));
#6469=IFCCARTESIANPOINT((3.4762544731,11.9856858573,7.1061078558));
#6470=IFCCARTESIANPOINT((3.4242247827,11.9816234863,7.0640494695));
#6471=IFCCARTESIANPOINT((4.6890166779E-5,2.8017549065E-5));
#6472=IFCCARTESIANPOINT((3.3621415116,11.9760995946,7.0095800211));
#6473=IFCCARTESIANPOINT((3.2997301961,11.9696149556,6.949086672));
#6474=IFCCARTESIANPOINT((3.2432172108,11.9626653054,6.8877865979));
#6475=IFCCARTESIANPOINT((4.6354832665E-5,2.6604724433E-5));
#6476=IFCCARTESIANPOINT((3.1926025557,11.9552338457,6.8256929955));
#6477=IFCCARTESIANPOINT((4.6170949698E-5,2.6232088392E-5));
#6478=IFCCARTESIANPOINT((3.14788623077,11.9473035221,6.762819814));
#6479=IFCCARTESIANPOINT((3.10906823601,11.9388570241,6.6991817802));
#6480=IFCCARTESIANPOINT((3.07614857142,11.9298767854,6.6347944239));
#6481=IFCCARTESIANPOINT((3.04912723701,11.9203449837,6.569674104));
#6482=IFCCARTESIANPOINT((3.02800423278,11.9102435407,6.5038380339));
#6483=IFCCARTESIANPOINT((4.5210747888E-5,2.5106199458E-5));
#6484=IFCCARTESIANPOINT((3.01277955873,11.8995541221,6.4373043089));
#6485=IFCCARTESIANPOINT((3.00345321485,11.8882581377,6.3700919325));
#6486=IFCCARTESIANPOINT((3.00002520114,11.8763367411,6.3022208437));
#6487=IFCCARTESIANPOINT((3.00242719233,11.8639398879,6.2346144846));
#6488=IFCCARTESIANPOINT((3.01040610718,11.851276089,6.1683524229));
#6489=IFCCARTESIANPOINT((4.4206903608E-5,2.519925239E-5));
#6490=IFCCARTESIANPOINT((3.02394591059,11.8382581301,6.1028911918));
#6491=IFCCARTESIANPOINT((4.4010378687E-5,2.5358424823E-5));
#6492=IFCCARTESIANPOINT((3.04304660254,11.8248968899,6.0382337907));
#6493=IFCCARTESIANPOINT((3.06770818305,11.8112031599,5.9743828122));
#6494=IFCCARTESIANPOINT((3.09793065212,11.7971876446,5.911340451));
#6495=IFCCARTESIANPOINT((3.13371400973,11.7828609614,5.8491085134));
#6496=IFCCARTESIANPOINT((4.3240140065E-5,2.6458521935E-5));
#6497=IFCCARTESIANPOINT((3.1750582559,11.7682336406,5.787688426));
#6498=IFCCARTESIANPOINT((4.3051545675E-5,2.6849398057E-5));
#6499=IFCCARTESIANPOINT((3.2219633906,11.7533161253,5.7270812449));
#6500=IFCCARTESIANPOINT((3.2744294139,11.7381187717,5.6672876647));
#6501=IFCCARTESIANPOINT((3.3324563257,11.7226518487,5.6083080274));
#6502=IFCCARTESIANPOINT((4.2495279142E-5,2.8300070853E-5));
#6503=IFCCARTESIANPOINT((3.3960441261,11.7069255384,5.5501423309));
#6504=IFCCARTESIANPOINT((3.4592238322,11.6922904162,5.4975381766));
#6505=IFCCARTESIANPOINT((3.5126316706,11.680573373,5.4564213881));
#6506=IFCCARTESIANPOINT((4.2068881168E-5,2.9735090654E-5));
#6507=IFCCARTESIANPOINT((3.5682465022,11.6689478032,5.4164591351));
#6508=IFCCARTESIANPOINT((4.1943999059E-5,3.0216939193E-5));
#6509=IFCCARTESIANPOINT((3.6260683269,11.6574277596,5.3776421043));
#6510=IFCCARTESIANPOINT((3.6860971446,11.6460269472,5.3399609012));
#6511=IFCCARTESIANPOINT((4.1703744463E-5,3.1235811098E-5));
#6512=IFCCARTESIANPOINT((3.7483329554,11.634758723,5.3034060738));
#6513=IFCCARTESIANPOINT((3.8127757594,11.6236360963,5.267968135));
#6514=IFCCARTESIANPOINT((4.1476169363E-5,3.2328249439E-5));
#6515=IFCCARTESIANPOINT((3.8794255564,11.6126717283,5.2336375851));
#6516=IFCCARTESIANPOINT((3.9482823465,11.6018779325,5.2004049332));
#6517=IFCCARTESIANPOINT((4.1261273759E-5,3.3494254216E-5));
#6518=IFCCARTESIANPOINT((4.0193461297,11.5912666745,5.1682607189));
#6519=IFCCARTESIANPOINT((4.0926169059,11.580849572,5.137195532));
#6520=IFCCARTESIANPOINT((4.1059057651E-5,3.473382543E-5));
#6521=IFCCARTESIANPOINT((4.1680946753,11.5706378949,5.1072000327));
#6522=IFCCARTESIANPOINT((4.2442654084,11.5608324142,5.0788107178));
#6523=IFCCARTESIANPOINT((4.0885998093E-5,3.5925545948E-5));
#6524=IFCCARTESIANPOINT((4.3111011375,11.5526087383,5.0553045669));
#6525=IFCCARTESIANPOINT((4.3784660068,11.5446674941,5.0328619309));
#6526=IFCCARTESIANPOINT((4.0738945679E-5,3.7052703272E-5));
#6527=IFCCARTESIANPOINT((4.4463600164,11.5370173602,5.0114747697));
#6528=IFCCARTESIANPOINT((4.067042012E-5,3.7622896188E-5));
#6529=IFCCARTESIANPOINT((4.5147831663,11.5296666303,4.9911352904));
#6530=IFCCARTESIANPOINT((4.5837354565,11.5226232136,4.9718359616));
#6531=IFCCARTESIANPOINT((4.0543370297E-5,3.8776510527E-5));
#6532=IFCCARTESIANPOINT((4.653216887,11.5158946341,4.9535695276));
#6533=IFCCARTESIANPOINT((4.7232274578,11.5094880315,4.9363290225));
#6534=IFCCARTESIANPOINT((4.0429655536E-5,3.9947762876E-5));
#6535=IFCCARTESIANPOINT((4.7937671689,11.5034101603,4.9201077825));
#6536=IFCCARTESIANPOINT((4.8648360203,11.4976673906,4.9048994584));
#6537=IFCCARTESIANPOINT((4.0329275836E-5,4.1136653235E-5));
#6538=IFCCARTESIANPOINT((4.936434012,11.4922657074,4.8906980268));
#6539=IFCCARTESIANPOINT((5.008561144,11.4872107112,4.877497801));
#6540=IFCCARTESIANPOINT((4.0242231196E-5,4.2343181604E-5));
#6541=IFCCARTESIANPOINT((5.0812174162,11.4825076176,4.865293441));
#6542=IFCCARTESIANPOINT((5.1544028288,11.4781612573,4.854079963));
#6543=IFCCARTESIANPOINT((4.0168521618E-5,4.3567347983E-5));
#6544=IFCCARTESIANPOINT((5.2281173817,11.4741760765,4.8438527481));
#6545=IFCCARTESIANPOINT((5.3023610749,11.4705561363,4.83460755));
#6546=IFCCARTESIANPOINT((4.0108147101E-5,4.4809152372E-5));
#6547=IFCCARTESIANPOINT((5.3771339084,11.4673051135,4.8263405028));
#6548=IFCCARTESIANPOINT((5.4524358821,11.4644262996,4.819048127));
#6549=IFCCARTESIANPOINT((4.0061107645E-5,4.6068594771E-5));
#6550=IFCCARTESIANPOINT((5.5282669962,11.4619226016,4.812727336));
#6551=IFCCARTESIANPOINT((5.6046272506,11.4597965418,4.8073754411));
#6552=IFCCARTESIANPOINT((4.002740325E-5,4.7345675179E-5));
#6553=IFCCARTESIANPOINT((5.6815166452,11.4580502575,4.8029901559));
#6554=IFCCARTESIANPOINT((5.7589351802,11.4566855014,4.7995696004));
#6555=IFCCARTESIANPOINT((4.0007033916E-5,4.8640393598E-5));
#6556=IFCCARTESIANPOINT((5.8368828555,11.4557036413,4.7971123039));
#6557=IFCCARTESIANPOINT((5.915359671,11.4551056605,4.7956172076));
#6558=IFCCARTESIANPOINT((3.9999999643E-5,4.9952750026E-5));
#6559=IFCCARTESIANPOINT((5.9943656269,11.454892157,4.7950836664));
#6560=IFCCARTESIANPOINT((6.0738587092,11.4550640878,4.7955133072));
#6561=IFCCARTESIANPOINT((4.0006328809E-5,5.1274032216E-5));
#6562=IFCCARTESIANPOINT((6.1529194896,11.4556223249,4.796908928));
#6563=IFCCARTESIANPOINT((6.2314266245,11.4565662796,4.7992710648));
#6564=IFCCARTESIANPOINT((4.0026054306E-5,5.257787075E-5));
#6565=IFCCARTESIANPOINT((6.3093801137,11.4578950026,4.8026007401));
#6566=IFCCARTESIANPOINT((6.3867799575,11.4596071561,4.8068993924));
#6567=IFCCARTESIANPOINT((4.0059175893E-5,5.3863254432E-5));
#6568=IFCCARTESIANPOINT((6.4636261556,11.4617010142,4.8121688738));
#6569=IFCCARTESIANPOINT((6.5399187082,11.4641744625,4.8184114464));
#6570=IFCCARTESIANPOINT((4.0105693572E-5,5.5130183263E-5));
#6571=IFCCARTESIANPOINT((6.6156576153,11.4670249984,4.8256297792));
#6572=IFCCARTESIANPOINT((6.6908428767,11.4702497311,4.8338269428));
#6573=IFCCARTESIANPOINT((4.0165607341E-5,5.6378657241E-5));
#6574=IFCCARTESIANPOINT((6.7654744927,11.4738453814,4.8430064049));
#6575=IFCCARTESIANPOINT((6.839552463,11.4778082817,4.8531720239));
#6576=IFCCARTESIANPOINT((4.0238917201E-5,5.7608676368E-5));
#6577=IFCCARTESIANPOINT((6.9130767878,11.4821343761,4.8643280425));
#6578=IFCCARTESIANPOINT((6.9860474671,11.4868192207,4.8764790803));
#6579=IFCCARTESIANPOINT((4.0325623152E-5,5.8820240642E-5));
#6580=IFCCARTESIANPOINT((7.0584645008,11.491857983,4.8896301255));
#6581=IFCCARTESIANPOINT((7.1303278889,11.4972454422,4.9037865269));
#6582=IFCCARTESIANPOINT((4.0425725194E-5,6.0013350065E-5));
#6583=IFCCARTESIANPOINT((7.2016376315,11.5029759893,4.9189539836));
#6584=IFCCARTESIANPOINT((7.2723937285,11.5090436271,4.9351385353));
#6585=IFCCARTESIANPOINT((4.0539223326E-5,6.1188004635E-5));
#6586=IFCCARTESIANPOINT((7.3425961799,11.5154419698,4.9523465517));
#6587=IFCCARTESIANPOINT((7.4122449858,11.5221642435,4.9705847205));
#6588=IFCCARTESIANPOINT((4.066611755E-5,6.2344204354E-5));
#6589=IFCCARTESIANPOINT((7.4813401462,11.529203286,4.9898600352));
#6590=IFCCARTESIANPOINT((4.0734588196E-5,6.2915383643E-5));
#6591=IFCCARTESIANPOINT((7.5498816609,11.5365515468,5.0101797825));
#6592=IFCCARTESIANPOINT((7.6178695302,11.5442010869,5.0315515284));
#6593=IFCCARTESIANPOINT((4.0881576556E-5,6.4043901084E-5));
#6594=IFCCARTESIANPOINT((7.6853037538,11.5521435793,5.053983104));
#6595=IFCCARTESIANPOINT((7.7521843319,11.5603703085,5.0774825902));
#6596=IFCCARTESIANPOINT((4.1047545911E-5,6.5190535306E-5));
#6597=IFCCARTESIANPOINT((7.8228998605,11.569449345,5.103737576));
#6598=IFCCARTESIANPOINT((7.8991991594,11.5797193301,5.1338535805));
#6599=IFCCARTESIANPOINT((4.1250613782E-5,6.644331099E-5));
#6600=IFCCARTESIANPOINT((7.9732329425,11.5901909951,5.1650306565));
#6601=IFCCARTESIANPOINT((8.0450012099,11.6008531352,5.1972780788));
#6602=IFCCARTESIANPOINT((4.1466237613E-5,6.7620569482E-5));
#6603=IFCCARTESIANPOINT((8.1145039616,11.6116942048,5.2306051872));
#6604=IFCCARTESIANPOINT((8.1817411975,11.6227023168,5.2650213669));
#6605=IFCCARTESIANPOINT((4.1694417402E-5,6.8722310783E-5));
#6606=IFCCARTESIANPOINT((8.2467129176,11.6338652434,5.3005360286));
#6607=IFCCARTESIANPOINT((8.3094191221,11.6451704154,5.3371585874));
#6608=IFCCARTESIANPOINT((4.1935153152E-5,6.9748534892E-5));
#6609=IFCCARTESIANPOINT((8.3698598107,11.6566049224,5.3748984419));
#6610=IFCCARTESIANPOINT((4.2060229511E-5,7.0233327999E-5));
#6611=IFCCARTESIANPOINT((8.4280349837,11.6681555132,5.413764952));
#6612=IFCCARTESIANPOINT((8.4839446408,11.6798085951,5.4537674167));
#6613=IFCCARTESIANPOINT((8.5375887823,11.6915502345,5.4949150509));
#6614=IFCCARTESIANPOINT((4.2463456641E-5,7.1602589043E-5));
#6615=IFCCARTESIANPOINT((8.5923463088,11.7041636741,5.5401055513));
#6616=IFCCARTESIANPOINT((8.6574633522,11.7200997834,5.5987486971));
#6617=IFCCARTESIANPOINT((4.2835947116E-5,7.2640686835E-5));
#6618=IFCCARTESIANPOINT((8.7169180439,11.7357597967,5.6581734635));
#6619=IFCCARTESIANPOINT((8.7707103841,11.7511342361,5.7183791321));
#6620=IFCCARTESIANPOINT((4.3214294253E-5,7.3490039574E-5));
#6621=IFCCARTESIANPOINT((8.8188403727,11.7662135493,5.7793646563));
#6622=IFCCARTESIANPOINT((8.8613080096,11.78098811,5.8411286542));
#6623=IFCCARTESIANPOINT((8.898113295,11.7954482176,5.9036694002));
#6624=IFCCARTESIANPOINT((8.9292562288,11.8095840973,5.966984818));
#6625=IFCCARTESIANPOINT((4.3988558515E-5,7.4622509894E-5));
#6626=IFCCARTESIANPOINT((8.954736811,11.8233859,6.0310724726));
#6627=IFCCARTESIANPOINT((8.9745550416,11.8368437026,6.0959295626));
#6628=IFCCARTESIANPOINT((8.9887109205,11.8499475074,6.1615529124));
#6629=IFCCARTESIANPOINT((8.9972044479,11.8626872428,6.2279389639));
#6630=IFCCARTESIANPOINT((14.1421,14.1421,37.2623));
#6631=IFCCARTESIANPOINT((3.4459577733E-5,3.4459577728E-5,7.1220887038E-5));
#6632=IFCCARTESIANPOINT((3.5623730998E-5,1.0532591771E-5,1.93875932375));
#6633=IFCCARTESIANPOINT((3.5623730998E-5,1.07143910402,1.93875932375));
#6634=IFCCARTESIANPOINT((3.5623730905E-5,2.14286767545,3.1754724911));
#6635=IFCCARTESIANPOINT((3.5623730998E-5,3.2142962469,1.93875932375));
#6636=IFCCARTESIANPOINT((3.5623730905E-5,4.2857248183,3.1754724911));
#6637=IFCCARTESIANPOINT((3.5623730998E-5,5.3571533897,1.93875932375));
#6638=IFCCARTESIANPOINT((3.5623730998E-5,6.4285819612,1.93875932375));
#6639=IFCCARTESIANPOINT((3.5623730998E-5,7.5000105326,1.93875932375));
#6640=IFCCARTESIANPOINT((3.5623730905E-5,8.571439104,3.1754724911));
#6641=IFCCARTESIANPOINT((3.5623730998E-5,9.6428676754,1.93875932375));
#6642=IFCCARTESIANPOINT((3.5623730998E-5,10.7142962469,1.93875932375));
#6643=IFCCARTESIANPOINT((3.5623730905E-5,11.7857248183,3.1754724911));
#6644=IFCCARTESIANPOINT((3.5623730998E-5,12.8571533897,1.93875932375));
#6645=IFCCARTESIANPOINT((3.5623730998E-5,13.9285819612,1.93875932375));
#6646=IFCCARTESIANPOINT((3.5623730998E-5,15.0000105326,1.93875932375));
#6647=IFCCARTESIANPOINT((1.07146419516,1.0532591771E-5,1.93875932375));
#6648=IFCCARTESIANPOINT((1.07146419516,1.07143910402,1.93875932375));
#6649=IFCCARTESIANPOINT((1.07146419516,2.14286767545,1.93875932375));
#6650=IFCCARTESIANPOINT((1.07146419516,3.2142962469,1.93875932375));
#6651=IFCCARTESIANPOINT((1.07146419516,4.2857248183,1.93875932375));
#6652=IFCCARTESIANPOINT((1.07146419516,5.3571533897,1.93875932375));
#6653=IFCCARTESIANPOINT((1.07146419516,6.4285819612,1.93875932375));
#6654=IFCCARTESIANPOINT((1.07146419516,7.5000105326,1.93875932375));
#6655=IFCCARTESIANPOINT((1.07146419516,8.571439104,1.93875932375));
#6656=IFCCARTESIANPOINT((1.07146419516,9.6428676754,1.93875932375));
#6657=IFCCARTESIANPOINT((1.07146419516,10.7142962469,1.93875932375));
#6658=IFCCARTESIANPOINT((1.07146419516,11.7857248183,1.93875932375));
#6659=IFCCARTESIANPOINT((1.07146419516,12.8571533897,1.93875932375));
#6660=IFCCARTESIANPOINT((1.07146419516,13.9285819612,1.93875932375));
#6661=IFCCARTESIANPOINT((1.04385994151,14.9865837438,1.54886729649));
#6662=IFCCARTESIANPOINT((2.14289276659,1.0532591771E-5,1.93875932375));
#6663=IFCCARTESIANPOINT((2.14289276659,1.07143910402,1.93875932375));
#6664=IFCCARTESIANPOINT((2.14289276659,2.14286767545,1.93875932375));
#6665=IFCCARTESIANPOINT((2.14289276659,3.2142962469,1.93875932375));
#6666=IFCCARTESIANPOINT((2.14289276659,4.2857248183,1.93875932375));
#6667=IFCCARTESIANPOINT((2.14289276659,5.3571533897,1.93875932375));
#6668=IFCCARTESIANPOINT((2.14289276659,6.4285819612,1.93875932375));
#6669=IFCCARTESIANPOINT((2.14289276659,7.5000105326,1.93875932375));
#6670=IFCCARTESIANPOINT((2.14289276659,8.571439104,1.93875932375));
#6671=IFCCARTESIANPOINT((2.14289276659,9.6428676754,1.93875932375));
#6672=IFCCARTESIANPOINT((2.14289276659,10.7142962469,1.93875932375));
#6673=IFCCARTESIANPOINT((2.14289276659,11.7857248183,1.93875932375));
#6674=IFCCARTESIANPOINT((2.14289276659,12.8571533897,5.1287882986));
#6675=IFCCARTESIANPOINT((2.14289276659,13.9285819612,1.93875932375));
#6676=IFCCARTESIANPOINT((2.14289276659,15.0000105326,1.74110678043));
#6677=IFCCARTESIANPOINT((3.214321338,1.0532591771E-5,1.93875932375));
#6678=IFCCARTESIANPOINT((3.214321338,1.07143910402,1.93875932375));
#6679=IFCCARTESIANPOINT((3.214321338,2.14286767545,5.18065991E-5));
#6680=IFCCARTESIANPOINT((3.214321338,3.2142962469,1.93875932375));
#6681=IFCCARTESIANPOINT((3.214321338,4.2857248183,1.93875932375));
#6682=IFCCARTESIANPOINT((3.214321338,5.3571533897,5.18065991E-5));
#6683=IFCCARTESIANPOINT((3.214321338,6.4285819612,1.93875932375));
#6684=IFCCARTESIANPOINT((3.214321338,7.5000105326,1.93875932375));
#6685=IFCCARTESIANPOINT((3.214321338,8.571439104,1.93875932375));
#6686=IFCCARTESIANPOINT((3.214321338,9.6428676754,1.93875932375));
#6687=IFCCARTESIANPOINT((3.214321338,10.7142962469,1.93875932375));
#6688=IFCCARTESIANPOINT((3.214321338,11.7857248183,1.93875932375));
#6689=IFCCARTESIANPOINT((3.214321338,12.8571533897,5.1287882986));
#6690=IFCCARTESIANPOINT((3.214321338,13.9285819612,1.93875932375));
#6691=IFCCARTESIANPOINT((3.214321338,15.0000105326,1.93875932375));
#6692=IFCCARTESIANPOINT((4.2857499094,1.0532591771E-5,1.93875932375));
#6693=IFCCARTESIANPOINT((4.2857499094,1.07143910402,1.93875932375));
#6694=IFCCARTESIANPOINT((4.2857499094,2.14286767545,1.93875932375));
#6695=IFCCARTESIANPOINT((4.2857499094,3.2142962469,1.93875932375));
#6696=IFCCARTESIANPOINT((4.2857499094,4.2857248183,1.93875932375));
#6697=IFCCARTESIANPOINT((4.2857499094,5.3571533897,1.93875932375));
#6698=IFCCARTESIANPOINT((4.2857499094,6.4285819612,1.93875932375));
#6699=IFCCARTESIANPOINT((4.2857499094,7.5000105326,1.93875932375));
#6700=IFCCARTESIANPOINT((4.2857499094,8.571439104,1.93875932375));
#6701=IFCCARTESIANPOINT((4.2857499094,9.6428676754,1.93875932375));
#6702=IFCCARTESIANPOINT((4.2857499094,10.7142962469,1.93875932375));
#6703=IFCCARTESIANPOINT((4.2857499094,11.7857248183,1.93875932375));
#6704=IFCCARTESIANPOINT((4.2857499094,12.8571533897,1.93875932375));
#6705=IFCCARTESIANPOINT((4.2857499094,13.9285819612,1.93875932375));
#6706=IFCCARTESIANPOINT((4.2857499094,15.0000105326,1.93875932375));
#6707=IFCCARTESIANPOINT((5.3571784809,1.0532591771E-5,1.93875932375));
#6708=IFCCARTESIANPOINT((5.3571784809,1.07143910402,1.93875932375));
#6709=IFCCARTESIANPOINT((5.3571784809,2.14286767545,1.93875932375));
#6710=IFCCARTESIANPOINT((5.3571784809,3.2142962469,1.93875932375));
#6711=IFCCARTESIANPOINT((5.3571784809,4.2857248183,1.93875932375));
#6712=IFCCARTESIANPOINT((5.3571784809,5.3571533897,1.93875932375));
#6713=IFCCARTESIANPOINT((5.3571784809,6.4285819612,1.93875932375));
#6714=IFCCARTESIANPOINT((5.3571784809,7.5000105326,1.93875932375));
#6715=IFCCARTESIANPOINT((5.3571784809,8.571439104,1.93875932375));
#6716=IFCCARTESIANPOINT((5.3571784809,9.6428676754,1.93875932375));
#6717=IFCCARTESIANPOINT((5.3571784809,10.7142962469,5.1287882986));
#6718=IFCCARTESIANPOINT((5.3571784809,11.7857248183,1.93875932375));
#6719=IFCCARTESIANPOINT((5.3571784809,12.8571533897,1.93875932375));
#6720=IFCCARTESIANPOINT((5.3571784809,13.9285819612,1.93875932375));
#6721=IFCCARTESIANPOINT((5.3571784809,15.0000105326,1.93875932375));
#6722=IFCCARTESIANPOINT((6.4286070523,1.0532591771E-5,1.93875932375));
#6723=IFCCARTESIANPOINT((6.4286070523,1.07143910402,1.93875932375));
#6724=IFCCARTESIANPOINT((6.4286070523,2.14286767545,1.93875932375));
#6725=IFCCARTESIANPOINT((6.4286070523,3.2142962469,1.93875932375));
#6726=IFCCARTESIANPOINT((6.4286070523,4.2857248183,1.93875932375));
#6727=IFCCARTESIANPOINT((6.4286070523,5.3571533897,1.93875932375));
#6728=IFCCARTESIANPOINT((6.4286070523,6.4285819612,5.18065991E-5));
#6729=IFCCARTESIANPOINT((6.4286070523,7.5000105326,1.93875932375));
#6730=IFCCARTESIANPOINT((6.4286070523,8.571439104,1.93875932375));
#6731=IFCCARTESIANPOINT((6.4286070523,9.6428676754,1.93875932375));
#6732=IFCCARTESIANPOINT((6.4286070523,10.7142962469,5.1287882986));
#6733=IFCCARTESIANPOINT((6.4286070523,11.7857248183,1.93875932375));
#6734=IFCCARTESIANPOINT((6.4286070523,12.8571533897,1.93875932375));
#6735=IFCCARTESIANPOINT((6.4286070523,13.9285819612,1.93875932375));
#6736=IFCCARTESIANPOINT((6.4286070523,15.0000105326,1.93875932375));
#6737=IFCCARTESIANPOINT((7.5000356237,1.0532591771E-5,1.93875932375));
#6738=IFCCARTESIANPOINT((7.5000356237,1.07143910402,1.93875932375));
#6739=IFCCARTESIANPOINT((7.5000356237,2.14286767545,1.93875932375));
#6740=IFCCARTESIANPOINT((7.5000356237,3.2142962469,1.93875932375));
#6741=IFCCARTESIANPOINT((7.5000356237,4.2857248183,1.93875932375));
#6742=IFCCARTESIANPOINT((7.5000356237,5.3571533897,1.93875932375));
#6743=IFCCARTESIANPOINT((7.5000356237,6.4285819612,1.93875932375));
#6744=IFCCARTESIANPOINT((7.5000356237,7.5000105326,1.93875932375));
#6745=IFCCARTESIANPOINT((7.5000356237,8.571439104,1.93875932375));
#6746=IFCCARTESIANPOINT((7.5000356237,9.6428676754,5.1287882986));
#6747=IFCCARTESIANPOINT((7.5000356237,10.7142962469,1.93875932375));
#6748=IFCCARTESIANPOINT((7.5000356237,11.7857248183,1.93875932375));
#6749=IFCCARTESIANPOINT((7.5000356237,12.8571533897,1.93875932375));
#6750=IFCCARTESIANPOINT((7.5000356237,13.9285819612,1.93875932375));
#6751=IFCCARTESIANPOINT((7.5000356237,15.0000105326,1.93875932375));
#6752=IFCCARTESIANPOINT((8.5714641952,1.0532591771E-5,1.93875932375));
#6753=IFCCARTESIANPOINT((8.5714641952,1.07143910402,1.93875932375));
#6754=IFCCARTESIANPOINT((8.5714641952,2.14286767545,1.93875932375));
#6755=IFCCARTESIANPOINT((8.5714641952,3.2142962469,1.93875932375));
#6756=IFCCARTESIANPOINT((8.5714641952,4.2857248183,1.93875932375));
#6757=IFCCARTESIANPOINT((8.5714641952,5.3571533897,1.93875932375));
#6758=IFCCARTESIANPOINT((8.5714641952,6.4285819612,1.93875932375));
#6759=IFCCARTESIANPOINT((8.5714641952,7.5000105326,1.93875932375));
#6760=IFCCARTESIANPOINT((8.5714641952,8.571439104,1.93875932375));
#6761=IFCCARTESIANPOINT((8.5714641952,9.6428676754,1.93875932375));
#6762=IFCCARTESIANPOINT((8.5714641952,10.7142962469,1.93875932375));
#6763=IFCCARTESIANPOINT((8.5714641952,11.7857248183,1.93875932375));
#6764=IFCCARTESIANPOINT((8.5714641952,12.8571533897,1.93875932375));
#6765=IFCCARTESIANPOINT((8.5714641952,13.9285819612,1.93875932375));
#6766=IFCCARTESIANPOINT((8.5714641952,15.0000105326,1.93875932375));
#6767=IFCCARTESIANPOINT((9.6428927666,1.0532591771E-5,1.93875932375));
#6768=IFCCARTESIANPOINT((9.6428927666,1.07143910402,1.93875932375));
#6769=IFCCARTESIANPOINT((9.6428927666,2.14286767545,1.93875932375));
#6770=IFCCARTESIANPOINT((9.6428927666,3.2142962469,1.93875932375));
#6771=IFCCARTESIANPOINT((9.6428927666,4.2857248183,1.93875932375));
#6772=IFCCARTESIANPOINT((9.6428927666,5.3571533897,1.93875932375));
#6773=IFCCARTESIANPOINT((9.6428927666,6.4285819612,5.18065991E-5));
#6774=IFCCARTESIANPOINT((9.6428927666,7.5000105326,1.93875932375));
#6775=IFCCARTESIANPOINT((9.6428927666,8.571439104,1.93875932375));
#6776=IFCCARTESIANPOINT((9.6428927666,9.6428676754,1.93875932375));
#6777=IFCCARTESIANPOINT((9.6428927666,10.7142962469,1.93875932375));
#6778=IFCCARTESIANPOINT((9.6428927666,11.7857248183,1.93875932375));
#6779=IFCCARTESIANPOINT((9.6428927666,12.8571533897,1.93875932375));
#6780=IFCCARTESIANPOINT((9.6428927666,13.9285819612,1.93875932375));
#6781=IFCCARTESIANPOINT((10.0962936606,15.0376487171,1.35588611674));
#6782=IFCCARTESIANPOINT((10.714321338,1.0532591771E-5,1.93875932375));
#6783=IFCCARTESIANPOINT((10.714321338,1.07143910402,1.93875932375));
#6784=IFCCARTESIANPOINT((10.714321338,2.14286767545,1.93875932375));
#6785=IFCCARTESIANPOINT((10.714321338,3.2142962469,1.93875932375));
#6786=IFCCARTESIANPOINT((10.714321338,4.2857248183,5.18065991E-5));
#6787=IFCCARTESIANPOINT((10.714321338,5.3571533897,1.93875932375));
#6788=IFCCARTESIANPOINT((10.714321338,6.4285819612,1.93875932375));
#6789=IFCCARTESIANPOINT((10.714321338,7.5000105326,1.93875932375));
#6790=IFCCARTESIANPOINT((10.714321338,8.571439104,5.18065991E-5));
#6791=IFCCARTESIANPOINT((10.714321338,9.6428676754,1.93875932375));
#6792=IFCCARTESIANPOINT((10.714321338,10.7142962469,1.93875932375));
#6793=IFCCARTESIANPOINT((10.714321338,11.7857248183,1.93875932375));
#6794=IFCCARTESIANPOINT((10.714321338,12.8571533897,1.93875932375));
#6795=IFCCARTESIANPOINT((10.714321338,13.9285819612,1.93875932375));
#6796=IFCCARTESIANPOINT((10.714321338,15.0000105326,1.93875932375));
#6797=IFCCARTESIANPOINT((11.7857499094,1.0532591771E-5,1.93875932375));
#6798=IFCCARTESIANPOINT((11.7857499094,1.07143910402,1.93875932375));
#6799=IFCCARTESIANPOINT((11.7857499094,2.14286767545,1.93875932375));
#6800=IFCCARTESIANPOINT((11.7857499094,3.2142962469,1.93875932375));
#6801=IFCCARTESIANPOINT((11.7857499094,4.2857248183,1.93875932375));
#6802=IFCCARTESIANPOINT((11.7857499094,5.3571533897,1.93875932375));
#6803=IFCCARTESIANPOINT((11.7857499094,6.4285819612,1.93875932375));
#6804=IFCCARTESIANPOINT((11.7857499094,7.5000105326,1.93875932375));
#6805=IFCCARTESIANPOINT((11.7857499094,8.571439104,1.93875932375));
#6806=IFCCARTESIANPOINT((11.7857499094,9.6428676754,1.93875932375));
#6807=IFCCARTESIANPOINT((11.7857499094,10.7142962469,1.93875932375));
#6808=IFCCARTESIANPOINT((11.7857499094,11.7857248183,1.93875932375));
#6809=IFCCARTESIANPOINT((11.7857499094,12.8571533897,1.93875932375));
#6810=IFCCARTESIANPOINT((11.7857499094,13.9285819612,1.93875932375));
#6811=IFCCARTESIANPOINT((11.3978907176,14.8693563406,2.44049268426));
#6812=IFCCARTESIANPOINT((12.8571784809,1.0532591771E-5,1.93875932375));
#6813=IFCCARTESIANPOINT((12.8571784809,1.07143910402,1.93875932375));
#6814=IFCCARTESIANPOINT((12.8571784809,2.14286767545,1.93875932375));
#6815=IFCCARTESIANPOINT((12.8571784809,3.2142962469,1.93875932375));
#6816=IFCCARTESIANPOINT((12.8571784809,4.2857248183,1.93875932375));
#6817=IFCCARTESIANPOINT((12.8571784809,5.3571533897,1.93875932375));
#6818=IFCCARTESIANPOINT((12.8571784809,6.4285819612,1.93875932375));
#6819=IFCCARTESIANPOINT((12.8571784809,7.5000105326,5.18065991E-5));
#6820=IFCCARTESIANPOINT((12.8571784809,8.571439104,1.93875932375));
#6821=IFCCARTESIANPOINT((12.8571784809,9.6428676754,1.93875932375));
#6822=IFCCARTESIANPOINT((12.8571784809,10.7142962469,1.93875932375));
#6823=IFCCARTESIANPOINT((12.8571784809,11.7857248183,1.93875932375));
#6824=IFCCARTESIANPOINT((12.8571784809,12.8571533897,1.93875932375));
#6825=IFCCARTESIANPOINT((12.8571784809,13.9285819612,1.93875932375));
#6826=IFCCARTESIANPOINT((12.8568370215,14.9497010766,2.56095173322));
#6827=IFCCARTESIANPOINT((13.9286070523,1.0532591771E-5,1.93875932375));
#6828=IFCCARTESIANPOINT((13.9286070523,1.07143910402,1.93875932375));
#6829=IFCCARTESIANPOINT((13.9286070523,2.14286767545,1.93875932375));
#6830=IFCCARTESIANPOINT((13.9286070523,3.2142962469,1.93875932375));
#6831=IFCCARTESIANPOINT((13.9286070523,4.2857248183,1.93875932375));
#6832=IFCCARTESIANPOINT((13.9286070523,5.3571533897,1.93875932375));
#6833=IFCCARTESIANPOINT((13.9286070523,6.4285819612,1.93875932375));
#6834=IFCCARTESIANPOINT((13.9286070523,7.5000105326,1.93875932375));
#6835=IFCCARTESIANPOINT((13.9286070523,8.571439104,1.93875932375));
#6836=IFCCARTESIANPOINT((13.9286070523,9.6428676754,1.93875932375));
#6837=IFCCARTESIANPOINT((13.9286070523,10.7142962469,1.93875932375));
#6838=IFCCARTESIANPOINT((13.9286070523,11.7857248183,1.93875932375));
#6839=IFCCARTESIANPOINT((13.9286070523,12.8571533897,1.93875932375));
#6840=IFCCARTESIANPOINT((13.9286070523,13.9285819612,1.93875932375));
#6841=IFCCARTESIANPOINT((10.6452090386,11.7314780592,4.9981805335));
#6842=IFCCARTESIANPOINT((15.0000356237,1.0532591771E-5,0.59627198567));
#6843=IFCCARTESIANPOINT((15.0000356237,1.07143910402,1.93875932375));
#6844=IFCCARTESIANPOINT((15.0000356237,2.14286767545,1.93875932375));
#6845=IFCCARTESIANPOINT((15.0000356237,3.2142962469,1.93875932375));
#6846=IFCCARTESIANPOINT((15.0000356237,4.2857248183,1.93875932375));
#6847=IFCCARTESIANPOINT((15.0000356237,5.3571533897,1.93875932375));
#6848=IFCCARTESIANPOINT((15.0000356237,6.4285819612,0.59627198567));
#6849=IFCCARTESIANPOINT((15.0000356237,7.5000105326,1.93875932375));
#6850=IFCCARTESIANPOINT((15.0000356237,8.571439104,1.93875932375));
#6851=IFCCARTESIANPOINT((15.0000356237,9.6428676754,0.59627198567));
#6852=IFCCARTESIANPOINT((15.0000356237,10.7142962469,1.93875932375));
#6853=IFCCARTESIANPOINT((15.0000356237,11.7857248183,1.93875932375));
#6854=IFCCARTESIANPOINT((15.0000356237,12.8571533897,1.93875932375));
#6855=IFCCARTESIANPOINT((15.0000356237,13.9285819612,0.59627198567));
#6856=IFCCARTESIANPOINT((15.0000356237,15.0000105326,1.93875932375));
#6857=IFCCARTESIANPOINT((89.1421,-68.3287,38.6153));
#6858=IFCCARTESIANPOINT((3.5623730952E-5,1.053259176E-5,0.59627198567));
#6859=IFCCARTESIANPOINT((3.5623730952E-5,7.5000356237,0.));
#6860=IFCCARTESIANPOINT((3.5623730952E-5,7.5000356237,7.5));
#6861=IFCCARTESIANPOINT((7.5000356237,7.5000356237,0.));
#6862=IFCCARTESIANPOINT((7.5000356237,7.5000356237,7.5));
#6863=IFCCARTESIANPOINT((15.0000356237,7.5000356237,0.));
#6864=IFCCARTESIANPOINT((15.0000356237,7.5000356237,7.5));
#6865=IFCCARTESIANPOINT((15.0000356237,3.5623730952E-5,0.));
#6866=IFCCARTESIANPOINT((15.0000356237,3.5623730952E-5,7.5));
#6867=IFCCARTESIANPOINT((3.5623730952E-5,3.5623730952E-5,0.));
#6868=IFCCARTESIANPOINT((3.5623730952E-5,3.5623730952E-5,7.5));
#6869=IFCCARTESIANPOINT((3.5623730952E-5,1.87503562373,0.));
#6870=IFCCARTESIANPOINT((3.5623730952E-5,1.87503562373,7.5));
#6871=IFCCARTESIANPOINT((3.5623730952E-5,3.7500356237,0.));
#6872=IFCCARTESIANPOINT((3.5623730952E-5,3.7500356237,7.5));
#6873=IFCCARTESIANPOINT((3.5623730952E-5,5.6250356237,0.));
#6874=IFCCARTESIANPOINT((3.5623730952E-5,5.6250356237,7.5));
#6875=IFCCARTESIANPOINT((11.2500356237,7.5000356237,0.));
#6876=IFCCARTESIANPOINT((3.7500356237,7.5000356237,0.));
#6877=IFCCARTESIANPOINT((3.5623730952E-5,7.5000356237,1.875));
#6878=IFCCARTESIANPOINT((3.5623730952E-5,7.5000356237,3.75));
#6879=IFCCARTESIANPOINT((3.5623730952E-5,7.5000356237,5.625));
#6880=IFCCARTESIANPOINT((3.7500356237,7.5000356237,7.5));
#6881=IFCCARTESIANPOINT((11.2500356237,7.5000356237,7.5));
#6882=IFCCARTESIANPOINT((2.5E-5,7.5E-5));
#6883=IFCCARTESIANPOINT((15.0000356237,7.5000356237,5.625));
#6884=IFCCARTESIANPOINT((2.5E-5,5.E-5));
#6885=IFCCARTESIANPOINT((15.0000356237,7.5000356237,3.75));
#6886=IFCCARTESIANPOINT((2.5E-5,2.5E-5));
#6887=IFCCARTESIANPOINT((15.0000356237,7.5000356237,1.875));
#6888=IFCCARTESIANPOINT((3.7500356237,3.5623730952E-5,0.));
#6889=IFCCARTESIANPOINT((7.5000356237,3.5623730952E-5,0.));
#6890=IFCCARTESIANPOINT((11.2500356237,3.5623730952E-5,0.));
#6891=IFCCARTESIANPOINT((3.75E-5,2.5E-5));
#6892=IFCCARTESIANPOINT((15.0000356237,3.5623730952E-5,1.875));
#6893=IFCCARTESIANPOINT((3.75E-5,5.E-5));
#6894=IFCCARTESIANPOINT((15.0000356237,3.5623730952E-5,3.75));
#6895=IFCCARTESIANPOINT((3.75E-5,7.5E-5));
#6896=IFCCARTESIANPOINT((15.0000356237,3.5623730952E-5,5.625));
#6897=IFCCARTESIANPOINT((11.2500356237,3.5623730952E-5,7.5));
#6898=IFCCARTESIANPOINT((7.5000356237,3.5623730952E-5,7.5));
#6899=IFCCARTESIANPOINT((4.6875E-5,0.0001));
#6900=IFCCARTESIANPOINT((3.7500356237,3.5623730952E-5,7.5));
#6901=IFCCARTESIANPOINT((5.E-5,7.5E-5));
#6902=IFCCARTESIANPOINT((3.5623730952E-5,3.5623730952E-5,5.625));
#6903=IFCCARTESIANPOINT((5.E-5,5.E-5));
#6904=IFCCARTESIANPOINT((3.5623730952E-5,3.5623730952E-5,3.75));
#6905=IFCCARTESIANPOINT((5.E-5,2.5E-5));
#6906=IFCCARTESIANPOINT((3.5623730952E-5,3.5623730952E-5,1.875));
#6907=IFCCARTESIANPOINT((4.3984375E-5,6.9485571585E-5));
#6908=IFCCARTESIANPOINT((6.3750356237,3.5623730952E-5,5.6985571585));
#6909=IFCCARTESIANPOINT((7.2187856237,3.5623730952E-5,5.2114178689));
#6910=IFCCARTESIANPOINT((4.328125E-5,6.2990381057E-5));
#6911=IFCCARTESIANPOINT((8.0625356237,3.5623730952E-5,4.7242785793));
#6912=IFCCARTESIANPOINT((4.2578125E-5,5.6495190528E-5));
#6913=IFCCARTESIANPOINT((8.9062856237,3.5623730952E-5,4.2371392896));
#6914=IFCCARTESIANPOINT((4.1875E-5,5.E-5));
#6915=IFCCARTESIANPOINT((9.7500356237,3.5623730952E-5,3.75));
#6916=IFCCARTESIANPOINT((4.2578125E-5,4.3504809472E-5));
#6917=IFCCARTESIANPOINT((8.9062856237,3.5623730952E-5,3.2628607104));
#6918=IFCCARTESIANPOINT((4.328125E-5,3.7009618943E-5));
#6919=IFCCARTESIANPOINT((8.0625356237,3.5623730952E-5,2.77572142074));
#6920=IFCCARTESIANPOINT((4.3984375E-5,3.0514428415E-5));
#6921=IFCCARTESIANPOINT((7.2187856237,3.5623730952E-5,2.28858213111));
#6922=IFCCARTESIANPOINT((4.46875E-5,2.4019237886E-5));
#6923=IFCCARTESIANPOINT((6.3750356237,3.5623730952E-5,1.80144284149));
#6924=IFCCARTESIANPOINT((4.46875E-5,3.7009618943E-5));
#6925=IFCCARTESIANPOINT((6.3750356237,3.5623730952E-5,2.77572142074));
#6926=IFCCARTESIANPOINT((4.46875E-5,5.E-5));
#6927=IFCCARTESIANPOINT((6.3750356237,3.5623730952E-5,3.75));
#6928=IFCCARTESIANPOINT((4.46875E-5,6.2990381057E-5));
#6929=IFCCARTESIANPOINT((6.3750356237,3.5623730952E-5,4.7242785793));
#6930=IFCCARTESIANPOINT((15.0000356237,1.87503562373,0.));
#6931=IFCCARTESIANPOINT((15.0000356237,3.7500356237,0.));
#6932=IFCCARTESIANPOINT((15.0000356237,5.6250356237,0.));
#6933=IFCCARTESIANPOINT((15.0000356237,5.6250356237,7.5));
#6934=IFCCARTESIANPOINT((15.0000356237,3.7500356237,7.5));
#6935=IFCCARTESIANPOINT((15.0000356237,1.87503562373,7.5));
#6936=IFCCARTESIANPOINT((1.625E-5,5.E-5));
#6937=IFCCARTESIANPOINT((1.484375E-5,5.6495190528E-5));
#6938=IFCCARTESIANPOINT((9.7500356237,7.5000356237,3.75));
#6939=IFCCARTESIANPOINT((8.9062856237,7.5000356237,4.2371392896));
#6940=IFCCARTESIANPOINT((1.34375E-5,6.2990381057E-5));
#6941=IFCCARTESIANPOINT((8.0625356237,7.5000356237,4.7242785793));
#6942=IFCCARTESIANPOINT((1.203125E-5,6.9485571585E-5));
#6943=IFCCARTESIANPOINT((7.2187856237,7.5000356237,5.2114178689));
#6944=IFCCARTESIANPOINT((1.0625E-5,7.5980762114E-5));
#6945=IFCCARTESIANPOINT((6.3750356237,7.5000356237,5.6985571585));
#6946=IFCCARTESIANPOINT((1.0625E-5,6.2990381057E-5));
#6947=IFCCARTESIANPOINT((6.3750356237,7.5000356237,4.7242785793));
#6948=IFCCARTESIANPOINT((1.0625E-5,5.E-5));
#6949=IFCCARTESIANPOINT((6.3750356237,7.5000356237,3.75));
#6950=IFCCARTESIANPOINT((1.0625E-5,3.7009618943E-5));
#6951=IFCCARTESIANPOINT((6.3750356237,7.5000356237,2.77572142074));
#6952=IFCCARTESIANPOINT((1.0625E-5,2.4019237886E-5));
#6953=IFCCARTESIANPOINT((6.3750356237,7.5000356237,1.80144284149));
#6954=IFCCARTESIANPOINT((1.203125E-5,3.0514428415E-5));
#6955=IFCCARTESIANPOINT((7.2187856237,7.5000356237,2.28858213111));
#6956=IFCCARTESIANPOINT((1.34375E-5,3.7009618943E-5));
#6957=IFCCARTESIANPOINT((8.0625356237,7.5000356237,2.77572142074));
#6958=IFCCARTESIANPOINT((1.484375E-5,4.3504809472E-5));
#6959=IFCCARTESIANPOINT((8.9062856237,7.5000356237,3.2628607104));
#6960=IFCCARTESIANPOINT((114.1421,-89.6079,40.856));
#6961=IFCCARTESIANPOINT((3.4459577733E-5,3.4459577732E-5,-1.1641532185E-6));
#6962=IFCCARTESIANPOINT((3.5623730917E-5,3.5623730946E-5,0.000100000000023));
#6963=IFCCARTESIANPOINT((3.5623730987E-5,15.0000356237,9.9999999977E-5));
#6964=IFCCARTESIANPOINT((15.0000356237,3.5623730946E-5,0.000100000000023));
#6965=IFCCARTESIANPOINT((15.0000356237,15.0000356237,9.9999999977E-5));
#6966=IFCCARTESIANPOINT((3.7500356237,15.0000356237,9.9999999977E-5));
#6967=IFCCARTESIANPOINT((7.5000356237,15.0000356237,9.9999999977E-5));
#6968=IFCCARTESIANPOINT((11.2500356237,15.0000356237,9.9999999977E-5));
#6969=IFCCARTESIANPOINT((15.0000356237,11.2500356237,9.9999999988E-5));
#6970=IFCCARTESIANPOINT((15.0000356237,7.5000356237,0.0001));
#6971=IFCCARTESIANPOINT((15.0000356237,3.7500356237,0.000100000000012));
#6972=IFCCARTESIANPOINT((11.2500356237,3.5623730946E-5,0.000100000000023));
#6973=IFCCARTESIANPOINT((7.5000356237,3.5623730946E-5,0.000100000000023));
#6974=IFCCARTESIANPOINT((3.7500356237,3.5623730946E-5,0.000100000000023));
#6975=IFCCARTESIANPOINT((3.5623730934E-5,3.7500356237,0.000100000000012));
#6976=IFCCARTESIANPOINT((3.5623730952E-5,7.5000356237,0.0001));
#6977=IFCCARTESIANPOINT((3.5623730969E-5,11.2500356237,9.9999999988E-5));
#6978=IFCCARTESIANPOINT((9.4063614625E-5,1.7452356878E-5));
#6979=IFCCARTESIANPOINT((9.4063614625E-5,3.2533712103E-5));
#6980=IFCCARTESIANPOINT((14.1095778174,2.6178891554,0.000100000000015));
#6981=IFCCARTESIANPOINT((14.1095778174,4.8800924392,0.000100000000008));
#6982=IFCCARTESIANPOINT((9.4063614625E-5,4.7615067328E-5));
#6983=IFCCARTESIANPOINT((14.1095778174,7.1422957229,0.000100000000001));
#6984=IFCCARTESIANPOINT((9.4063614625E-5,6.2696422553E-5));
#6985=IFCCARTESIANPOINT((14.1095778174,9.4044990066,9.9999999994E-5));
#6986=IFCCARTESIANPOINT((9.4063614625E-5,7.7777777778E-5));
#6987=IFCCARTESIANPOINT((14.1095778174,11.6667022904,9.9999999987E-5));
#6988=IFCCARTESIANPOINT((8.3047710968E-5,7.7777777778E-5));
#6989=IFCCARTESIANPOINT((12.457192269,11.6667022904,9.9999999987E-5));
#6990=IFCCARTESIANPOINT((7.2031807312E-5,7.7777777778E-5));
#6991=IFCCARTESIANPOINT((10.8048067206,11.6667022904,9.9999999987E-5));
#6992=IFCCARTESIANPOINT((6.1015903656E-5,7.7777777778E-5));
#6993=IFCCARTESIANPOINT((9.1524211722,11.6667022904,9.9999999987E-5));
#6994=IFCCARTESIANPOINT((5.E-5,7.7777777778E-5));
#6995=IFCCARTESIANPOINT((7.5000356237,11.6667022904,9.9999999987E-5));
#6996=IFCCARTESIANPOINT((5.E-5,6.2696422553E-5));
#6997=IFCCARTESIANPOINT((7.5000356237,9.4044990066,9.9999999994E-5));
#6998=IFCCARTESIANPOINT((5.E-5,4.7615067328E-5));
#6999=IFCCARTESIANPOINT((7.5000356237,7.1422957229,0.000100000000001));
#7000=IFCCARTESIANPOINT((5.E-5,3.2533712103E-5));
#7001=IFCCARTESIANPOINT((7.5000356237,4.8800924392,0.000100000000008));
#7002=IFCCARTESIANPOINT((5.E-5,1.7452356878E-5));
#7003=IFCCARTESIANPOINT((7.5000356237,2.6178891554,0.000100000000015));
#7004=IFCCARTESIANPOINT((6.1015903656E-5,1.7452356878E-5));
#7005=IFCCARTESIANPOINT((9.1524211722,2.6178891554,0.000100000000015));
#7006=IFCCARTESIANPOINT((7.2031807312E-5,1.7452356878E-5));
#7007=IFCCARTESIANPOINT((10.8048067206,2.6178891554,0.000100000000015));
#7008=IFCCARTESIANPOINT((8.3047710968E-5,1.7452356878E-5));
#7009=IFCCARTESIANPOINT((12.457192269,2.6178891554,0.000100000000015));
#7010=IFCCARTESIANPOINT((64.1421,-43.3579,40.8559));
#7011=IFCCARTESIANPOINT((3.4459577698E-5,3.4459577728E-5,9.8835846761E-5));
#7012=IFCCARTESIANPOINT((1.16078600148,34.357855902,20.3489465333));
#7013=IFCCARTESIANPOINT((1.16078600148,34.357855902,1.53731630717));
#7014=IFCCARTESIANPOINT((1.16078600148,18.0665062393,10.9431314203));
#7015=IFCCARTESIANPOINT((1.16078600148,1.7751565768,20.3489465333));
#7016=IFCCARTESIANPOINT((1.16078600148,18.0665062393,29.7547616464));
#7017=IFCCARTESIANPOINT((1.16078600148,34.357855902,39.16057676));
#7018=IFCCARTESIANPOINT((2.32156021282,35.245407063,20.3489465333));
#7019=IFCCARTESIANPOINT((2.32156021282,35.245407063,3.2602418072E-5));
#7020=IFCCARTESIANPOINT((2.32156021282,17.6227306589,10.1744895679));
#7021=IFCCARTESIANPOINT((2.32156021282,5.4255288735E-5,20.3489465333));
#7022=IFCCARTESIANPOINT((2.32156021282,17.6227306589,30.5234034988));
#7023=IFCCARTESIANPOINT((2.32156021282,35.245407063,40.697860464));
#7024=IFCCARTESIANPOINT((6.9646570582,35.245407063,20.3489465333));
#7025=IFCCARTESIANPOINT((6.9646570582,35.245407063,3.260241808E-5));
#7026=IFCCARTESIANPOINT((6.9646570582,17.6227306589,10.1744895679));
#7027=IFCCARTESIANPOINT((6.9646570582,5.4255288688E-5,20.3489465333));
#7028=IFCCARTESIANPOINT((6.9646570582,17.6227306589,30.5234034988));
#7029=IFCCARTESIANPOINT((6.9646570582,35.245407063,40.697860464));
#7030=IFCCARTESIANPOINT((6.9646570582,31.092345024,20.3489465333));
#7031=IFCCARTESIANPOINT((6.9646570582,31.092345024,7.1933470601));
#7032=IFCCARTESIANPOINT((6.9646570582,19.6992616781,13.7711467967));
#7033=IFCCARTESIANPOINT((6.9646570582,8.3061783323,20.3489465333));
#7034=IFCCARTESIANPOINT((6.9646570582,19.6992616781,26.92674627));
#7035=IFCCARTESIANPOINT((6.9646570582,31.092345024,33.504546007));
#7036=IFCCARTESIANPOINT((3.6139147091,29.839503015,20.3489465333));
#7037=IFCCARTESIANPOINT((3.6139147091,29.839503015,9.3633330736));
#7038=IFCCARTESIANPOINT((3.6139147091,20.3256826827,14.8561398035));
#7039=IFCCARTESIANPOINT((3.6139147091,10.8118623503,20.3489465333));
#7040=IFCCARTESIANPOINT((3.6139147091,20.3256826827,25.8417532632));
#7041=IFCCARTESIANPOINT((3.6139147091,29.839503015,31.3345599931));
#7042=IFCCARTESIANPOINT((1.1790137971E-5,29.920100098,20.3489465333));
#7043=IFCCARTESIANPOINT((1.1790138041E-5,29.920100098,9.2237348309));
#7044=IFCCARTESIANPOINT((1.1790137971E-5,20.2853841412,14.7863406821));
#7045=IFCCARTESIANPOINT((1.1790137971E-5,10.6506681844,20.3489465333));
#7046=IFCCARTESIANPOINT((1.1790137971E-5,20.2853841412,25.9115523846));
#7047=IFCCARTESIANPOINT((1.1790137971E-5,29.920100098,31.4741582358));
#7048=IFCCARTESIANPOINT((1.1790137971E-5,33.470304741,20.3489465333));
#7049=IFCCARTESIANPOINT((1.1790138041E-5,33.470304741,3.07460001191));
#7050=IFCCARTESIANPOINT((1.1790137971E-5,18.5102818197,11.7117732726));
#7051=IFCCARTESIANPOINT((1.1790137971E-5,3.5502588983,20.3489465333));
#7052=IFCCARTESIANPOINT((1.1790137971E-5,18.5102818197,28.9861197941));
#7053=IFCCARTESIANPOINT((1.1790137971E-5,33.470304741,37.623293055));
#7054=IFCCARTESIANPOINT((2.32157200296,68.*********,3.07463261433));
#7055=IFCCARTESIANPOINT((2.32157200296,3.5503131536,40.697893067));
#7056=IFCCARTESIANPOINT((2.32157200296,68.*********,78.*********));
#7057=IFCCARTESIANPOINT((43.5938,-79.2095,-23.7804));
#7058=IFCCARTESIANPOINT((1.1790137971E-5,11.*********,6.7830039127));
#7059=IFCDIRECTION((0.,0.,1.));
#7060=IFCDIRECTION((1.,0.,0.));
#7061=IFCDIRECTION((0.,1.));
#7062=IFCSITE('006_i1V1D0tAKEh_$vljQt',#7147,'Site 1','Site 1',$,#5342,
$,'Site 1',.ELEMENT.,$,$,0.,$,#7138);
#7063=IFCRELDEFINESBYPROPERTIES('1RL1EewHTsROrPcNSlBI56',#7147,
'Project',$,(#7136),#7074);
#7064=IFCRELDEFINESBYPROPERTIES('3bjMWmv_Cp6LPxmaCnPGwW',#7147,'Site',$,
(#7062),#7075);
#7065=IFCRELDEFINESBYPROPERTIES('2zOmgYUoQmhp9sZMdCZF7s',#7147,
'Pset_BuildingCommon',$,(#94),#7076);
#7066=IFCRELDEFINESBYPROPERTIES('3k0v4EdaEvPD8NSrRFB2MT',#7147,
'ArchBuilding',$,(#94),#7077);
#7067=IFCRELDEFINESBYPROPERTIES('3uIgchz4n8nDCCMgq1BBql',#7147,
'ObjectIdentity',$,(#94),#7078);
#7068=IFCRELDEFINESBYPROPERTIES('1CCmZ_1eEDpfMO62t1jkZj',#7147,
'ObjectPostalAddress',$,(#94),#7079);
#7069=IFCRELDEFINESBYPROPERTIES('24I$F7L8gfSwdAGDvfkVEu',#7147,
'Pset_BuildingStoreyCommon',$,(#93),#7080);
#7070=IFCRELDEFINESBYPROPERTIES('1K1DvjfvPzx3hE5w5ZHK0q',#7147,
'ArchFloor',$,(#93),#7081);
#7071=IFCRELDEFINESBYPROPERTIES('0lrK04aZ0v6OfwsyTXPzZq',#7147,'Floor',
$,(#93),#7082);
#7072=IFCRELDEFINESBYPROPERTIES('1fbDyx4oFQKKacC_b9xURq',#7147,
'StructuralFloorCommon',$,(#93),#7083);
#7073=IFCRELDEFINESBYPROPERTIES('0wWmVuJT0dMD2gjn9kV2VW',#7147,
'Qto_WallBaseQuantities',$,(#21),#15);
#7074=IFCPROPERTYSET('2rcLYSOKrF02Xv$qFtEF4e',#7147,'Project',$,(#7092));
#7075=IFCPROPERTYSET('2Qswsgonw96KTM1$UoUklF',#7147,'Site',$,(#7093,#7094));
#7076=IFCPROPERTYSET('2Ct1rxyLMs3vgB_jB90hMr',#7147,
'Pset_BuildingCommon',$,(#7095));
#7077=IFCPROPERTYSET('3OmKLTEF7bgCV0$1Gr7ajZ',#7147,'ArchBuilding',$,(#7096,
#7097));
#7078=IFCPROPERTYSET('1HjNcqn7ak9HvLTK8j8w1d',#7147,'ObjectIdentity',$,
(#7098));
#7079=IFCPROPERTYSET('306EzywKpRfIeis4axlC1d',#7147,
'ObjectPostalAddress',$,(#7099,#7100,#7101,#7102));
#7080=IFCPROPERTYSET('381uKge8G33qXIbHr84hB4',#7147,
'Pset_BuildingStoreyCommon',$,(#7103,#7104,#7105,#7106));
#7081=IFCPROPERTYSET('0GZmVQOAmJ6zMOk6j3uleN',#7147,'ArchFloor',$,(#7107,
#7108,#7109,#7110,#7111,#7112,#7113,#7114,#7115,#7116));
#7082=IFCPROPERTYSET('0A3C_vQUNRQZfA6f5qtBlE',#7147,'Floor',$,(#7117,#7118,
#7119));
#7083=IFCPROPERTYSET('0izo3etGuCpkjROydkcZbT',#7147,
'StructuralFloorCommon',$,(#7120));
#7084=IFCPROPERTYSET('0fQNiANe9J5TQvsKqhfdAW',#7147,'Pset_WallCommon',$,
(#7121,#7122,#7123,#7124));
#7085=IFCPROPERTYSET('16i1gNdxBFgAIcqphdG8wM',#7147,
'ObjectClassification',$,(#7125));
#7086=IFCPROPERTYSET('2nhvqxxy13YVxp49bmsvnH',#7147,
'ObjectFireResistance',$,(#7126,#7127));
#7087=IFCPROPERTYSET('29vBjdQvKt0poXwQNpT3XC',#7147,'ObjectIdentity',$,
(#7128));
#7088=IFCPROPERTYSET('39eoxL41zhY8Qd4GdC8QVS',#7147,'ObjectMaterial',$,
(#7129));
#7089=IFCPROPERTYSET('16fmRZHdwxm0E_FuKF66Vi',#7147,
'ObjectSpaceBounding',$,(#7130));
#7090=IFCPROPERTYSET('3SstypxQR69NxtXrEbq2c5',#7147,
'ObjectThermalTransmittance',$,(#7131,#7132));
#7091=IFCPROPERTYSET('0cjqvAvK8RO48bn57_Yvty',#7147,'Wall_Common',$,(#7133,
#7134));
#7092=IFCPROPERTYSINGLEVALUE('ProjectName',$,IFCTEXT(
'BuildingTemplate_US'),$);
#7093=IFCPROPERTYSINGLEVALUE('SiteName',$,IFCTEXT('Site 1'),$);
#7094=IFCPROPERTYSINGLEVALUE('BuildingHeightLimit',$,
IFCLENGTHMEASURE(0.),#7283);
#7095=IFCPROPERTYSINGLEVALUE('YearOfConstruction',$,IFCLABEL('2006'),$);
#7096=IFCPROPERTYSINGLEVALUE('BuildingName',$,IFCTEXT('Bldg 1'),$);
#7097=IFCPROPERTYSINGLEVALUE('YearConstructed',$,IFCTEXT('2006'),$);
#7098=IFCPROPERTYSINGLEVALUE('Description',$,IFCTEXT('3 Story Building'),
$);
#7099=IFCPROPERTYSINGLEVALUE('Address1',$,IFCTEXT(
'203 Rickenhouse Drive'),$);
#7100=IFCPROPERTYSINGLEVALUE('City',$,IFCTEXT('Madison'),$);
#7101=IFCPROPERTYSINGLEVALUE('PostalCode',$,IFCTEXT('35789'),$);
#7102=IFCPROPERTYSINGLEVALUE('Country',$,IFCTEXT('US'),$);
#7103=IFCPROPERTYSINGLEVALUE('GrossAreaPlanned',$,IFCAREAMEASURE(22296729.6),
#7284);
#7104=IFCPROPERTYSINGLEVALUE('GrossPlannedArea',$,IFCAREAMEASURE(22296729.6),
$);
#7105=IFCPROPERTYSINGLEVALUE('NetAreaPlanned',$,IFCAREAMEASURE(22296729.6),
#7284);
#7106=IFCPROPERTYSINGLEVALUE('NetPlannedArea',$,IFCAREAMEASURE(22296729.6),
$);
#7107=IFCPROPERTYSINGLEVALUE('FloorName',$,IFCTEXT('Floor 3'),$);
#7108=IFCPROPERTYSINGLEVALUE('FinishedFloorElevation',$,IFCREAL(9.144),
$);
#7109=IFCPROPERTYSINGLEVALUE('Description',$,IFCTEXT(
'Administrative and employee lounge'),$);
#7110=IFCPROPERTYSINGLEVALUE('ApproxLength',$,IFCLENGTHMEASURE(6096.),#7283);
#7111=IFCPROPERTYSINGLEVALUE('ApproxWidth',$,IFCLENGTHMEASURE(3657.6),#7283);
#7112=IFCPROPERTYSINGLEVALUE('TypicalFloor',$,IFCBOOLEAN(.F.),$);
#7113=IFCPROPERTYSINGLEVALUE('TypicalFloorHeight',$,IFCLENGTHMEASURE(0.),
#7283);
#7114=IFCPROPERTYSINGLEVALUE('TypicalFloorBaseElevation',$,
IFCLENGTHMEASURE(0.),#7283);
#7115=IFCPROPERTYSINGLEVALUE('ACSRotation',$,IFCTEXT('0.00000000'),$);
#7116=IFCPROPERTYSINGLEVALUE('Annotation',$,IFCBOOLEAN(.T.),$);
#7117=IFCPROPERTYSINGLEVALUE('GrossArea',$,IFCAREAMEASURE(22296729.6),#7284);
#7118=IFCPROPERTYSINGLEVALUE('NetArea',$,IFCAREAMEASURE(22296729.6),#7284);
#7119=IFCPROPERTYSINGLEVALUE('OccupancyType',$,IFCTEXT('Commercial'),$);
#7120=IFCPROPERTYSINGLEVALUE('issplicefloor',$,IFCBOOLEAN(.F.),$);
#7121=IFCPROPERTYSINGLEVALUE('Combustible',$,IFCBOOLEAN(.F.),$);
#7122=IFCPROPERTYSINGLEVALUE('Compartmentation',$,IFCBOOLEAN(.F.),$);
#7123=IFCPROPERTYSINGLEVALUE('IsExternal',$,IFCBOOLEAN(.T.),$);
#7124=IFCPROPERTYSINGLEVALUE('LoadBearing',$,IFCBOOLEAN(.F.),$);
#7125=IFCPROPERTYSINGLEVALUE('UniFormat',$,IFCLABEL('B2010'),$);
#7126=IFCPROPERTYSINGLEVALUE('IsCombustible',$,IFCBOOLEAN(.F.),$);
#7127=IFCPROPERTYSINGLEVALUE('Compartmentation',$,IFCBOOLEAN(.F.),$);
#7128=IFCPROPERTYSINGLEVALUE('Description',$,IFCTEXT(
'Default exterior wall'),$);
#7129=IFCPROPERTYSINGLEVALUE('PartDefinition',$,IFCLABEL(
'WallAssembly::*Default Exterior Wall*'),$);
#7130=IFCPROPERTYSINGLEVALUE('IsSpaceBounding',$,IFCBOOLEAN(.T.),$);
#7131=IFCPROPERTYSINGLEVALUE('IsExternal',$,IFCBOOLEAN(.T.),$);
#7132=IFCPROPERTYSINGLEVALUE('IsBelowGrade',$,IFCBOOLEAN(.F.),$);
#7133=IFCPROPERTYSINGLEVALUE('Type',$,IFCTEXT('Schematic'),$);
#7134=IFCPROPERTYSINGLEVALUE('IsCompound',$,IFCBOOLEAN(.F.),$);
#7135=IFCUNITASSIGNMENT((#7283,#7284,#7285,#7286,#7287,#7288,#7289,#7290,
#7291,#7292,#7293,#7294,#7295,#7296,#7297,#7298,#7299,#7300,#7301,#7302,
#7303,#7304,#7280,#7306,#7307,#7308,#7309,#7310,#7311,#7149,#7150,#7151,
#7152,#7153,#7154,#7155,#7156,#7157,#7158,#7159,#7160,#7161,#7162,#7163,
#7164,#7165,#7166,#7167,#7168,#7169,#7170,#7171,#7172,#7173,#7174,#7175,
#7176,#7177,#7178,#7179,#7180,#7181,#7182,#7183,#7184,#7185,#7186,#7187,
#7188,#7189,#7190,#7191,#7192,#7148));
#7136=IFCPROJECT('1JmBUThP19Z8wED88xGwCF',#7147,'BuildingTemplate_US',
'BuildingTemplate_US',$,'BuildingTemplate_US',$,(#62),#7135);
#7137=IFCPOSTALADDRESS($,$,$,$,('Address 1','Address 2'),$,'City/Town',
'State/Region','Postal Code','Country');
#7138=IFCPOSTALADDRESS($,$,$,'Site 1',$,$,$,$,$,$);
#7139=IFCPOSTALADDRESS($,$,$,$,('203 Rickenhouse Drive'),$,'Madison',$,
'35789','US');
#7140=IFCTELECOMADDRESS($,$,$,('Phone'),$,$,('E-Mail'),$,$);
#7141=IFCACTORROLE(.SUPPLIER.,$,$);
#7142=IFCPERSON($,'Last Name','First Name',$,$,$,(#7141),(#7140));
#7143=IFCPERSONANDORGANIZATION(#7142,#7145,$);
#7144=IFCORGANIZATION($,'Bentley Systems Inc.',$,$,$);
#7145=IFCORGANIZATION($,'Organization Name',$,$,(#7137));
#7146=IFCAPPLICATION(#7144,'***********','OpenBuildings Designer','ABD');
#7147=IFCOWNERHISTORY(#7143,#7146,$,$,0,$,$,1583400665);
#7148=IFCMONETARYUNIT('USD');
#7149=IFCDERIVEDUNIT((#7193,#7194),.ACCELERATIONUNIT.,
'(METRE)/(SECOND^2)');
#7150=IFCDERIVEDUNIT((#7195,#7196),.ANGULARVELOCITYUNIT.,
'(DEGREE)/(SECOND)');
#7151=IFCDERIVEDUNIT((#7197,#7198),.CURVATUREUNIT.,'(DEGREE)/(METRE)');
#7152=IFCDERIVEDUNIT((#7199,#7200),.DYNAMICVISCOSITYUNIT.,
'(PASCAL)(SECOND)');
#7153=IFCDERIVEDUNIT((#7201,#7202),.HEATFLUXDENSITYUNIT.,
'(WATT)/(METRE^2)');
#7154=IFCDERIVEDUNIT((#7203,#7204),.HEATINGVALUEUNIT.,'(JOULE)/(GRAM)');
#7155=IFCDERIVEDUNIT((#7205),.INTEGERCOUNTRATEUNIT.,'1/(SECOND)');
#7156=IFCDERIVEDUNIT((#7206,#7207),.IONCONCENTRATIONUNIT.,
'(GRAM)/(CUBIC_METRE)');
#7157=IFCDERIVEDUNIT((#7208,#7209),.ISOTHERMALMOISTURECAPACITYUNIT.,
'(CUBIC_METRE)/(GRAM)');
#7158=IFCDERIVEDUNIT((#7210,#7211),.KINEMATICVISCOSITYUNIT.,
'(SQUARE_METRE)/(SECOND)');
#7159=IFCDERIVEDUNIT((#7212,#7213),.LINEARFORCEUNIT.,'(NEWTON)/(METRE)');
#7160=IFCDERIVEDUNIT((#7214,#7215),.LINEARMOMENTUNIT.,
'(NEWTON)/(METRE)');
#7161=IFCDERIVEDUNIT((#7216,#7217),.LINEARSTIFFNESSUNIT.,
'(NEWTON)/(METRE)');
#7162=IFCDERIVEDUNIT((#7218,#7219),.LINEARVELOCITYUNIT.,
'(METRE)/(SECOND)');
#7163=IFCDERIVEDUNIT((#7220,#7221),.LUMINOUSINTENSITYDISTRIBUTIONUNIT.,
'(CANDELA)/(LUMEN)');
#7164=IFCDERIVEDUNIT((#7222,#7223),.MASSDENSITYUNIT.,
'(GRAM)/(CUBIC_METRE)');
#7165=IFCDERIVEDUNIT((#7224,#7225),.MASSFLOWRATEUNIT.,'(GRAM)/(SECOND)');
#7166=IFCDERIVEDUNIT((#7226,#7227),.MASSPERLENGTHUNIT.,'(GRAM)/(METRE)');
#7167=IFCDERIVEDUNIT((#7228,#7229),.MODULUSOFELASTICITYUNIT.,
'(NEWTON)/(SQUARE_METRE)');
#7168=IFCDERIVEDUNIT((#7230,#7231),
 .MODULUSOFLINEARSUBGRADEREACTIONUNIT.,'(NEWTON)/(METRE^2)');
#7169=IFCDERIVEDUNIT((#7232,#7233),
 .MODULUSOFROTATIONALSUBGRADEREACTIONUNIT.,'(NEWTON)(METRE)');
#7170=IFCDERIVEDUNIT((#7234,#7235),.MODULUSOFSUBGRADEREACTIONUNIT.,
'(NEWTON)/(CUBIC_METRE)');
#7171=IFCDERIVEDUNIT((#7236,#7237),.MOISTUREDIFFUSIVITYUNIT.,
'(CUBIC_METRE)/(SECOND)');
#7172=IFCDERIVEDUNIT((#7238,#7239),.MOLECULARWEIGHTUNIT.,
'(GRAM)/(MOLE)');
#7173=IFCDERIVEDUNIT((#7240,#7241),.PLANARFORCEUNIT.,
'(NEWTON)/(METRE^2)');
#7174=IFCDERIVEDUNIT((#7242),.ROTATIONALFREQUENCYUNIT.,'1/(SECOND)');
#7175=IFCDERIVEDUNIT((#7243,#7244),.ROTATIONALMASSUNIT.,
'(GRAM)(METRE^2)');
#7176=IFCDERIVEDUNIT((#7245,#7246,#7247),.ROTATIONALSTIFFNESSUNIT.,
'(NEWTON)(METRE)/(DEGREE)');
#7177=IFCDERIVEDUNIT((#7248),.SECTIONAREAINTEGRALUNIT.,'(METRE^5)');
#7178=IFCDERIVEDUNIT((#7249),.SECTIONMODULUSUNIT.,'(METRE^3)');
#7179=IFCDERIVEDUNIT((#7250,#7251),.SHEARMODULUSUNIT.,
'(NEWTON)/(SQUARE_METRE)');
#7180=IFCDERIVEDUNIT((#7252,#7253),.SOUNDPOWERUNIT.,'(JOULE)/(SECOND)');
#7181=IFCDERIVEDUNIT((#7254,#7255,#7256),.SPECIFICHEATCAPACITYUNIT.,
'(NEWTON)/(GRAM)(KELVIN)');
#7182=IFCDERIVEDUNIT((#7257,#7258),.TEMPERATUREGRADIENTUNIT.,
'(KELVIN)/(METRE)');
#7183=IFCDERIVEDUNIT((#7259,#7260,#7261),.THERMALADMITTANCEUNIT.,
'(WATT)/(SQUARE_METRE)(KELVIN)');
#7184=IFCDERIVEDUNIT((#7262,#7263,#7264),.THERMALCONDUCTANCEUNIT.,
'(WATT)/(METRE)(KELVIN)');
#7185=IFCDERIVEDUNIT((#7265),.THERMALEXPANSIONCOEFFICIENTUNIT.,
'1/(KELVIN)');
#7186=IFCDERIVEDUNIT((#7266,#7267),.THERMALRESISTANCEUNIT.,
'(SQUARE_METRE)/(WATT)');
#7187=IFCDERIVEDUNIT((#7268,#7269,#7270),.THERMALTRANSMITTANCEUNIT.,
'(WATT)/(SQUARE_METRE)(KELVIN)');
#7188=IFCDERIVEDUNIT((#7271,#7272),.TORQUEUNIT.,'(NEWTON)(METRE)');
#7189=IFCDERIVEDUNIT((#7273,#7274),.VAPORPERMEABILITYUNIT.,
'(GRAM)/(SECOND)');
#7190=IFCDERIVEDUNIT((#7275,#7276),.VOLUMETRICFLOWRATEUNIT.,
'(CUBIC_METRE)/(SECOND)');
#7191=IFCDERIVEDUNIT((#7277),.WARPINGCONSTANTUNIT.,'(METRE^6)');
#7192=IFCDERIVEDUNIT((#7278,#7279),.WARPINGMOMENTUNIT.,
'(NEWTON)(METRE^2)');
#7193=IFCDERIVEDUNITELEMENT(#7283,1);
#7194=IFCDERIVEDUNITELEMENT(#7311,-2);
#7195=IFCDERIVEDUNITELEMENT(#7280,1);
#7196=IFCDERIVEDUNITELEMENT(#7311,-1);
#7197=IFCDERIVEDUNITELEMENT(#7280,1);
#7198=IFCDERIVEDUNITELEMENT(#7283,-1);
#7199=IFCDERIVEDUNITELEMENT(#7307,1);
#7200=IFCDERIVEDUNITELEMENT(#7311,1);
#7201=IFCDERIVEDUNITELEMENT(#7306,1);
#7202=IFCDERIVEDUNITELEMENT(#7283,-2);
#7203=IFCDERIVEDUNITELEMENT(#7295,1);
#7204=IFCDERIVEDUNITELEMENT(#7304,-1);
#7205=IFCDERIVEDUNITELEMENT(#7311,-1);
#7206=IFCDERIVEDUNITELEMENT(#7304,1);
#7207=IFCDERIVEDUNITELEMENT(#7285,-1);
#7208=IFCDERIVEDUNITELEMENT(#7285,1);
#7209=IFCDERIVEDUNITELEMENT(#7304,-1);
#7210=IFCDERIVEDUNITELEMENT(#7284,1);
#7211=IFCDERIVEDUNITELEMENT(#7311,-1);
#7212=IFCDERIVEDUNITELEMENT(#7296,1);
#7213=IFCDERIVEDUNITELEMENT(#7283,-1);
#7214=IFCDERIVEDUNITELEMENT(#7296,1);
#7215=IFCDERIVEDUNITELEMENT(#7283,-1);
#7216=IFCDERIVEDUNITELEMENT(#7296,1);
#7217=IFCDERIVEDUNITELEMENT(#7283,-1);
#7218=IFCDERIVEDUNITELEMENT(#7283,1);
#7219=IFCDERIVEDUNITELEMENT(#7311,-1);
#7220=IFCDERIVEDUNITELEMENT(#7301,1);
#7221=IFCDERIVEDUNITELEMENT(#7300,-1);
#7222=IFCDERIVEDUNITELEMENT(#7304,1);
#7223=IFCDERIVEDUNITELEMENT(#7285,-1);
#7224=IFCDERIVEDUNITELEMENT(#7304,1);
#7225=IFCDERIVEDUNITELEMENT(#7311,-1);
#7226=IFCDERIVEDUNITELEMENT(#7304,1);
#7227=IFCDERIVEDUNITELEMENT(#7283,-1);
#7228=IFCDERIVEDUNITELEMENT(#7296,1);
#7229=IFCDERIVEDUNITELEMENT(#7284,-1);
#7230=IFCDERIVEDUNITELEMENT(#7296,1);
#7231=IFCDERIVEDUNITELEMENT(#7283,-2);
#7232=IFCDERIVEDUNITELEMENT(#7296,1);
#7233=IFCDERIVEDUNITELEMENT(#7283,1);
#7234=IFCDERIVEDUNITELEMENT(#7296,1);
#7235=IFCDERIVEDUNITELEMENT(#7285,-1);
#7236=IFCDERIVEDUNITELEMENT(#7285,1);
#7237=IFCDERIVEDUNITELEMENT(#7311,-1);
#7238=IFCDERIVEDUNITELEMENT(#7304,1);
#7239=IFCDERIVEDUNITELEMENT(#7287,-1);
#7240=IFCDERIVEDUNITELEMENT(#7296,1);
#7241=IFCDERIVEDUNITELEMENT(#7283,-2);
#7242=IFCDERIVEDUNITELEMENT(#7311,-1);
#7243=IFCDERIVEDUNITELEMENT(#7304,1);
#7244=IFCDERIVEDUNITELEMENT(#7283,2);
#7245=IFCDERIVEDUNITELEMENT(#7296,1);
#7246=IFCDERIVEDUNITELEMENT(#7283,1);
#7247=IFCDERIVEDUNITELEMENT(#7280,-1);
#7248=IFCDERIVEDUNITELEMENT(#7283,5);
#7249=IFCDERIVEDUNITELEMENT(#7283,3);
#7250=IFCDERIVEDUNITELEMENT(#7296,1);
#7251=IFCDERIVEDUNITELEMENT(#7284,-1);
#7252=IFCDERIVEDUNITELEMENT(#7295,1);
#7253=IFCDERIVEDUNITELEMENT(#7311,-1);
#7254=IFCDERIVEDUNITELEMENT(#7296,1);
#7255=IFCDERIVEDUNITELEMENT(#7304,-1);
#7256=IFCDERIVEDUNITELEMENT(#7310,-1);
#7257=IFCDERIVEDUNITELEMENT(#7310,1);
#7258=IFCDERIVEDUNITELEMENT(#7283,-1);
#7259=IFCDERIVEDUNITELEMENT(#7306,1);
#7260=IFCDERIVEDUNITELEMENT(#7284,-1);
#7261=IFCDERIVEDUNITELEMENT(#7310,-1);
#7262=IFCDERIVEDUNITELEMENT(#7306,1);
#7263=IFCDERIVEDUNITELEMENT(#7283,-1);
#7264=IFCDERIVEDUNITELEMENT(#7310,-1);
#7265=IFCDERIVEDUNITELEMENT(#7310,-1);
#7266=IFCDERIVEDUNITELEMENT(#7284,1);
#7267=IFCDERIVEDUNITELEMENT(#7306,-1);
#7268=IFCDERIVEDUNITELEMENT(#7306,1);
#7269=IFCDERIVEDUNITELEMENT(#7284,-1);
#7270=IFCDERIVEDUNITELEMENT(#7310,-1);
#7271=IFCDERIVEDUNITELEMENT(#7296,1);
#7272=IFCDERIVEDUNITELEMENT(#7283,1);
#7273=IFCDERIVEDUNITELEMENT(#7304,1);
#7274=IFCDERIVEDUNITELEMENT(#7311,-1);
#7275=IFCDERIVEDUNITELEMENT(#7285,1);
#7276=IFCDERIVEDUNITELEMENT(#7311,-1);
#7277=IFCDERIVEDUNITELEMENT(#7283,6);
#7278=IFCDERIVEDUNITELEMENT(#7296,1);
#7279=IFCDERIVEDUNITELEMENT(#7283,2);
#7280=IFCCONVERSIONBASEDUNIT(#7281,.PLANEANGLEUNIT.,'degree',#7282);
#7281=IFCDIMENSIONALEXPONENTS(0,0,0,0,0,0,0);
#7282=IFCMEASUREWITHUNIT(IFCREAL(0.0174532925199433),#7305);
#7283=IFCSIUNIT(*,.LENGTHUNIT.,$,.METRE.);
#7284=IFCSIUNIT(*,.AREAUNIT.,$,.SQUARE_METRE.);
#7285=IFCSIUNIT(*,.VOLUMEUNIT.,$,.CUBIC_METRE.);
#7286=IFCSIUNIT(*,.ABSORBEDDOSEUNIT.,$,.GRAY.);
#7287=IFCSIUNIT(*,.AMOUNTOFSUBSTANCEUNIT.,$,.MOLE.);
#7288=IFCSIUNIT(*,.DOSEEQUIVALENTUNIT.,$,.SIEVERT.);
#7289=IFCSIUNIT(*,.ELECTRICCAPACITANCEUNIT.,$,.FARAD.);
#7290=IFCSIUNIT(*,.ELECTRICCHARGEUNIT.,$,.COULOMB.);
#7291=IFCSIUNIT(*,.ELECTRICCONDUCTANCEUNIT.,$,.SIEMENS.);
#7292=IFCSIUNIT(*,.ELECTRICCURRENTUNIT.,$,.AMPERE.);
#7293=IFCSIUNIT(*,.ELECTRICRESISTANCEUNIT.,$,.OHM.);
#7294=IFCSIUNIT(*,.ELECTRICVOLTAGEUNIT.,$,.VOLT.);
#7295=IFCSIUNIT(*,.ENERGYUNIT.,$,.JOULE.);
#7296=IFCSIUNIT(*,.FORCEUNIT.,$,.NEWTON.);
#7297=IFCSIUNIT(*,.FREQUENCYUNIT.,$,.HERTZ.);
#7298=IFCSIUNIT(*,.ILLUMINANCEUNIT.,$,.LUX.);
#7299=IFCSIUNIT(*,.INDUCTANCEUNIT.,$,.HENRY.);
#7300=IFCSIUNIT(*,.LUMINOUSFLUXUNIT.,$,.LUMEN.);
#7301=IFCSIUNIT(*,.LUMINOUSINTENSITYUNIT.,$,.CANDELA.);
#7302=IFCSIUNIT(*,.MAGNETICFLUXDENSITYUNIT.,$,.TESLA.);
#7303=IFCSIUNIT(*,.MAGNETICFLUXUNIT.,$,.WEBER.);
#7304=IFCSIUNIT(*,.MASSUNIT.,$,.GRAM.);
#7305=IFCSIUNIT(*,.PLANEANGLEUNIT.,$,.RADIAN.);
#7306=IFCSIUNIT(*,.POWERUNIT.,$,.WATT.);
#7307=IFCSIUNIT(*,.PRESSUREUNIT.,$,.PASCAL.);
#7308=IFCSIUNIT(*,.RADIOACTIVITYUNIT.,$,.BECQUEREL.);
#7309=IFCSIUNIT(*,.SOLIDANGLEUNIT.,$,.STERADIAN.);
#7310=IFCSIUNIT(*,.THERMODYNAMICTEMPERATUREUNIT.,$,.KELVIN.);
#7311=IFCSIUNIT(*,.TIMEUNIT.,$,.SECOND.);
ENDSEC;
END-ISO-10303-21;

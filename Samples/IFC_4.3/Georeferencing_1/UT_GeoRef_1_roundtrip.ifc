ISO-10303-21;
HEADER;
FILE_DESCRIPTION(('ViewDefinition [CoordinationView]'),'2;1');
FILE_NAME('','2020-10-30T00:19:14',(),(),'IfcOpenShell 0.6.0b0','IfcOpenShell 0.6.0b0','');
FILE_SCHEMA(('IFC4X3_RC1'));
ENDSEC;
DATA;
#1=IFCPROJECT('2DAvEupIz0HQr73cMaawtY',#3,'GeoRef_1','Test adapted from ProVI',$,$,$,(#4),#6);
#2=IFCSITE('27H$neCQf1NwtmczxBInPR',#3,$,$,$,#16,$,$,.ELEMENT.,$,$,$,$,$);
#3=IFCOWNERHISTORY(#21,#29,$,.ADDED.,$,$,$,1548096817);
#4=IFCGEOMETRICREPRESENTATIONCONTEXT($,'Model',3,1.E-06,#33,#37);
#5=IFCRELAGGREGATES('3Hu7f6BmT14B_XS9yS78Jr',#3,$,$,#1,(#2));
#6=IFCUNITASSIGNMENT((#7,#8,#9,#10,#13,#14,#15));
#7=IFCSIUNIT(*,.LENGTHUNIT.,$,.METRE.);
#8=IFCSIUNIT(*,.AREAUNIT.,$,.SQUARE_METRE.);
#9=IFCSIUNIT(*,.VOLUMEUNIT.,$,.CUBIC_METRE.);
#10=IFCCONVERSIONBASEDUNIT($,.PLANEANGLEUNIT.,'GRADIAN',#11);
#11=IFCMEASUREWITHUNIT(IFCPLANEANGLEMEASURE(0.015707963267949),#12);
#12=IFCSIUNIT(*,.PLANEANGLEUNIT.,$,.RADIAN.);
#13=IFCSIUNIT(*,.MASSUNIT.,.KILO.,.GRAM.);
#14=IFCSIUNIT(*,.TIMEUNIT.,$,.SECOND.);
#15=IFCMONETARYUNIT('EUR');
#16=IFCLOCALPLACEMENT($,#17);
#17=IFCAXIS2PLACEMENT3D(#18,#19,#20);
#18=IFCCARTESIANPOINT((0.,0.,0.));
#19=IFCDIRECTION((0.,0.,1.));
#20=IFCDIRECTION((1.,0.,0.));
#21=IFCPERSONANDORGANIZATION(#22,#25,(#28));
#22=IFCPERSON($,'Jaud','\X2\0160\X0\tefan',$,$,$,(#23),(#24));
#23=IFCACTORROLE(.CIVILENGINEER.,$,$);
#24=IFCPOSTALADDRESS(.OFFICE.,$,$,$,('Arcisstra\X2\00DF\X0\e 21'),$,'M\X2\00FC\X0\nchen','Bayern','80333','Deutschland');
#25=IFCORGANIZATION($,'TUM',$,(#26),(#27));
#26=IFCACTORROLE(.CIVILENGINEER.,$,$);
#27=IFCPOSTALADDRESS(.OFFICE.,$,$,$,('Arcisstra\X2\00DF\X0\e 21'),$,'M\X2\00FC\X0\nchen','Bayern','80333','Deutschland');
#28=IFCACTORROLE(.CIVILENGINEER.,$,$);
#29=IFCAPPLICATION(#30,'6.0','Programmsystem f\X2\00FC\X0\r Verkehrs- und Infrastrukturplanung','ProVI');
#30=IFCORGANIZATION($,'ProVI GmbH','www.provi-cad.de',(#31),(#32));
#31=IFCACTORROLE(.USERDEFINED.,'Softwareentwickler',$);
#32=IFCPOSTALADDRESS(.OFFICE.,'Main office',$,'MUC/IT',('Garmischer Stra\X2\00DF\X0\e 19-21'),$,'M\X2\00FC\X0\nchen','Bayern','81373','Germany');
#33=IFCAXIS2PLACEMENT3D(#34,#35,#36);
#34=IFCCARTESIANPOINT((0.,0.,0.));
#35=IFCDIRECTION((0.,0.,1.));
#36=IFCDIRECTION((1.,0.,0.));
#37=IFCDIRECTION((0.,1.,0.));
ENDSEC;
END-ISO-10303-21;

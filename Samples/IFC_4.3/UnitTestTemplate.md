
## *topic\_nr*

### Intent

*Include a short description of the unit test scenario(s).*

### Prerequisites

This scenario builds upon following other scenarios:
- *list other scenarios that should be supported before this one*

### Content

This scenario covers the following concepts and/or IFC entities:
- *list IFC concepts covered*
- *list IFC entities specifically addressed*

### Supporting files

Following files correspond to this scenario:

| Filename                          | Description                               |
|-----------------------------------|-------------------------------------------|
| *filename*                        | *short description*                       |


## Instructions

- Your task is to replace all *italic* elements with your content.

- In order to be more version control friendly, please split longer sentences in lines,
 so that no line length surpasses 100 characters (see this sentence as an example).

- Please name the files according to this schema:
`UT_topic_nr[_additional enumeration for files of same sort].fileending`.
> Example: `UT_GeoRef_1_2.png` is the 2nd figure of the 1st UT within the Georeferencing topic.

- Please put all files (including this one) in a folder named `topic_nr`
 and rename this file to `readme.md`.

- Delete this instructions section.

- Create a [pull request](https://help.github.com/en/github/collaborating-with-issues-and-pull-requests/creating-a-pull-request-from-a-fork) to the main repository.

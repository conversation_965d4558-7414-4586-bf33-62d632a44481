{"type": "IfcProject", "globalId": "ce9363d7-0416-40f5-a6e2-53ccc7408153", "ownerHistory": {"type": "IfcOwnerHistory", "globalId": "c2d8c84f-23f5-44f8-b508-ba2c5967cffa", "owningUser": {"type": "IfcPersonAndOrganization", "globalId": "99d89809-3af6-4c22-87e5-ff6a5560be55", "thePerson": {"type": "<PERSON><PERSON><PERSON><PERSON>", "globalId": "8ef50f46-62ab-4065-a8cc-60859352b305", "identification": {"type": "IfcIdentifier", "value": "<PERSON>"}}, "theOrganization": {"type": "IfcOrganization", "globalId": "c1544245-a036-4b93-92aa-42547afc453d", "name": {"type": "IfcLabel", "value": "<PERSON><PERSON><PERSON>"}}}, "owningApplication": {"applicationDeveloper": {"type": "IfcOrganization", "globalId": "53abb4f0-90f7-489d-8382-c5491421d5b3", "name": {"type": "IfcLabel", "value": "Constructivity.com LLC"}}, "version": {"type": "IfcLabel", "value": "0.9.1"}, "applicationFullName": {"type": "IfcLabel", "value": "Constructivity"}, "applicationIdentifier": {"type": "IfcIdentifier", "value": "CONSTRUCTIVITY"}}, "state": "READWRITE", "changeAction": "NOTDEFINED", "creationDate": {"type": "IfcTimeStamp", "value": 1321047295}}, "name": {"type": "IfcLabel", "value": "HVAC Product Type Library Example"}, "description": {"type": "IfcText", "value": "Demonstrates an air terminal type, which may be instantiated in buildings within referencing files."}, "objectType": {"type": "IfcLabel", "value": "ProductLibrary"}, "representationContexts": [{"type": "IfcGeometricRepresentationContext", "globalId": "382037ae-bb99-480c-81f6-7c58ce28a2b8", "contextIdentifier": {"type": "IfcLabel", "value": "3D"}, "contextType": {"type": "IfcLabel", "value": "Model"}, "coordinateSpaceDimension": {"type": "IfcDimensionCount", "value": 3}, "precision": {"type": "IfcReal", "value": 1e-05}, "worldCoordinateSystem": {"type": "IfcAxis2Placement3D", "location": {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": 0.0}, {"type": "IfcLengthMeasure", "value": 0.0}, {"type": "IfcLengthMeasure", "value": 0.0}]}}}], "unitsInContext": {"units": [{"type": "IfcConversionBasedUnit", "globalId": "21e9b0f5-8ffd-402b-ba77-7a037e249184", "dimensions": {"lengthExponent": 1, "massExponent": 0, "timeExponent": 0, "electricCurrentExponent": 0, "thermodynamicTemperatureExponent": 0, "amountOfSubstanceExponent": 0, "luminousIntensityExponent": 0}, "unitType": "LENGTHUNIT", "name": "inch", "conversionFactor": {"type": "IfcMeasureWithUnit", "valueComponent": {"type": "IfcLengthMeasure", "value": 0.0254}, "unitComponent": {"type": "IfcSIUnit", "globalId": "914558bf-c923-4b9d-85c5-d799885f6d21", "dimensions": {"lengthExponent": 1, "massExponent": 0, "timeExponent": 0, "electricCurrentExponent": 0, "thermodynamicTemperatureExponent": 0, "amountOfSubstanceExponent": 0, "luminousIntensityExponent": 0}, "unitType": "LENGTHUNIT", "name": "METRE"}}}]}, "declares": [{"type": "IfcRelDeclares", "globalId": "3f97ef3c-c486-4e94-a7e5-a1d6380afb65", "ownerHistory": "c2d8c84f-23f5-44f8-b508-ba2c5967cffa", "relatedDefinitions": [{"type": "IfcAirTerminalType", "globalId": "4f39c682-589e-4c1f-b8a0-8f34be56f32b", "ownerHistory": "c2d8c84f-23f5-44f8-b508-ba2c5967cffa", "name": {"type": "IfcLabel", "value": "Acme Diffuser 1234"}, "description": {"type": "IfcText", "value": "Ceiling diffuser"}, "isNestedBy": [{"type": "IfcRelNests", "globalId": "7c65cf01-5cef-4ad9-a0f5-b92ff38b8b31", "ownerHistory": "c2d8c84f-23f5-44f8-b508-ba2c5967cffa", "relatedObjects": [{"type": "IfcDistributionPort", "globalId": "2f310718-4379-44c3-8146-81ca22e3d5ea", "ownerHistory": "c2d8c84f-23f5-44f8-b508-ba2c5967cffa", "name": {"type": "IfcLabel", "value": "Inlet"}, "isDefinedBy": [{"type": "IfcRelDefinesByProperties", "globalId": "28771d36-4644-460f-b919-ffc3e09daef7", "ownerHistory": "c2d8c84f-23f5-44f8-b508-ba2c5967cffa", "name": {"type": "IfcLabel", "value": "Pset_DistributionPortCommon"}, "relatingPropertyDefinition": {"type": "IfcPropertySet", "globalId": "d69d1da5-3510-4bd5-a892-feeb64557164", "ownerHistory": "c2d8c84f-23f5-44f8-b508-ba2c5967cffa", "name": {"type": "IfcLabel", "value": "Pset_DistributionPortCommon"}, "description": {"type": "IfcText", "value": "Common attributes attached to an instance of IfcDistributionPort."}, "isDefinedBy": [{"type": "IfcRelDefinesByTemplate", "globalId": "8d286279-a6d0-422d-9475-e65613bec10c", "ownerHistory": "c2d8c84f-23f5-44f8-b508-ba2c5967cffa", "relatedPropertySets": ["d69d1da5-3510-4bd5-a892-feeb64557164"], "relatingTemplate": {"type": "IfcPropertySetTemplate", "globalId": "8980b34b-4895-43de-a38a-a852bc09493b", "ownerHistory": "c2d8c84f-23f5-44f8-b508-ba2c5967cffa", "name": {"type": "IfcLabel", "value": "Pset_DistributionPortCommon"}, "description": {"type": "IfcText", "value": "Common attributes attached to an instance of IfcDistributionPort."}, "hasContext": [{"type": "IfcRelDeclares", "globalId": "21bff64c-c636-4b08-b796-4e6b7f3fe6d7", "ownerHistory": "c2d8c84f-23f5-44f8-b508-ba2c5967cffa", "name": {"type": "IfcLabel", "value": "PROJECT"}, "relatedDefinitions": [{"type": "IfcPropertySetTemplate", "globalId": "4a19b614-0ecb-4ae4-9111-d1509aa0e15f", "ownerHistory": "c2d8c84f-23f5-44f8-b508-ba2c5967cffa", "name": {"type": "IfcLabel", "value": "Pset_DistributionPortTypeAirConditioning"}, "description": {"type": "IfcText", "value": "Duct port occurrence attributes attached to an instance of IfcDistributionPort."}, "hasContext": ["21bff64c-c636-4b08-b796-4e6b7f3fe6d7"], "hasAssociations": [{"type": "IfcRelAssociatesLibrary", "globalId": "8dd0a49d-a462-43bc-85fd-8bdb45d47266", "ownerHistory": "c2d8c84f-23f5-44f8-b508-ba2c5967cffa", "relatingLibrary": {"type": "IfcLibraryReference", "identification": {"type": "IfcIdentifier", "value": "Pset_DistributionPortTypeAirConditioning"}, "name": {"type": "IfcLabel", "value": "Pset_DistributionPortTypeAirConditioning"}, "referencedLibrary": {"type": "IfcLibraryInformation", "name": {"type": "IfcLabel", "value": "IFC4"}, "publisher": {"type": "IfcOrganization", "globalId": "0223c0a2-caf6-4cf0-bfd3-dec8870e3e4a", "name": {"type": "IfcLabel", "value": "<PERSON><PERSON><PERSON>"}}, "versionDate": {"type": "IfcDateTime", "value": "2011-09-26T20:52:24"}, "location": {"type": "IfcURIReference", "value": "http://buildingsmart-tech.org/ifc/IFC2x4/rc3/html/annex/annex-a/ifc2x4_rc3-templates.ifc"}}}}], "templateType": "PSET_OCCURRENCEDRIVEN", "applicableEntity": {"type": "IfcIdentifier", "value": "IfcDistributionPort/AIRCONDITIONING"}, "hasPropertyTemplates": [{"type": "IfcSimplePropertyTemplate", "globalId": "ca737fd8-17c3-485e-85bf-d3ebdaf11827", "ownerHistory": "c2d8c84f-23f5-44f8-b508-ba2c5967cffa", "name": {"type": "IfcLabel", "value": "DryBulbTemperature"}, "description": {"type": "IfcText", "value": "Dry bulb temperature of the air."}, "partOfPsetTemplate": ["4a19b614-0ecb-4ae4-9111-d1509aa0e15f"], "templateType": "P_BOUNDEDVALUE", "primaryMeasureType": {"type": "IfcLabel", "value": "IfcThermodynamicTemperatureMeasure"}, "secondaryMeasureType": {"type": "IfcLabel"}, "accessState": "READWRITE"}, {"type": "IfcSimplePropertyTemplate", "globalId": "ffa5c4f5-56e6-4982-8daa-dde807df9889", "ownerHistory": "c2d8c84f-23f5-44f8-b508-ba2c5967cffa", "name": {"type": "IfcLabel", "value": "ConnectionType"}, "description": {"type": "IfcText", "value": "The end-style treatment of the duct port:\\X\\0A\\X\\0ABEADEDSLEEVE: Beaded Sleeve. \\X\\0ACOMPRESSION: Compression. \\X\\0ACRIMP: Crimp. \\X\\0ADRAWBAND: Drawband. \\X\\0ADRIVESLIP: Drive slip. \\X\\0AFLANGED: Flanged. \\X\\0AOUTSIDESLEEVE: Outside Sleeve. \\X\\0ASLIPON: Slipon. \\X\\0ASOLDERED: Soldered. \\X\\0ASSLIP: S-Slip. \\X\\0ASTANDINGSEAM: Standing seam. \\X\\0ASWEDGE: Swedge. \\X\\0AWELDED: Welded. \\X\\0AOTHER: Another type of end-style has been applied.\\X\\0ANONE: No end-style has been applied."}, "partOfPsetTemplate": ["4a19b614-0ecb-4ae4-9111-d1509aa0e15f"], "templateType": "P_ENUMERATEDVALUE", "primaryMeasureType": {"type": "IfcLabel", "value": "IfcLabel"}, "enumerators": {"type": "IfcPropertyEnumeration", "name": {"type": "IfcLabel", "value": "PEnum_DuctConnectionType"}, "enumerationValues": [{"type": "IfcLabel", "value": "BEADEDSLEEVE"}, {"type": "IfcLabel", "value": "COMPRESSION"}, {"type": "IfcLabel", "value": "CRIMP"}, {"type": "IfcLabel", "value": "DRAWBAND"}, {"type": "IfcLabel", "value": "DRIVESLIP"}, {"type": "IfcLabel", "value": "FLANGED"}, {"type": "IfcLabel", "value": "OUTSIDESLEEVE"}, {"type": "IfcLabel", "value": "SLIPON"}, {"type": "IfcLabel", "value": "SOLDERED"}, {"type": "IfcLabel", "value": "SSLIP"}, {"type": "IfcLabel", "value": "STANDINGSEAM"}, {"type": "IfcLabel", "value": "SWEDGE"}, {"type": "IfcLabel", "value": "WELDED"}, {"type": "IfcLabel", "value": "OTHER"}, {"type": "IfcLabel", "value": "NONE"}, {"type": "IfcLabel", "value": "USERDEFINED"}, {"type": "IfcLabel", "value": "NOTDEFINED"}]}, "accessState": "READWRITE"}, {"type": "IfcSimplePropertyTemplate", "globalId": "e88b5620-6165-4058-a831-b14f4dfe5a88", "ownerHistory": "c2d8c84f-23f5-44f8-b508-ba2c5967cffa", "name": {"type": "IfcLabel", "value": "ConnectionSubType"}, "description": {"type": "IfcText", "value": "The physical port connection subtype that further qualifies the ConnectionType."}, "partOfPsetTemplate": ["4a19b614-0ecb-4ae4-9111-d1509aa0e15f"], "templateType": "P_SINGLEVALUE", "primaryMeasureType": {"type": "IfcLabel", "value": "IfcLabel"}, "secondaryMeasureType": {"type": "IfcLabel"}, "accessState": "READWRITE"}, {"type": "IfcSimplePropertyTemplate", "globalId": "3237166c-9039-4910-9f5a-25263468380d", "ownerHistory": "c2d8c84f-23f5-44f8-b508-ba2c5967cffa", "name": {"type": "IfcLabel", "value": "VolumetricFlowRate"}, "description": {"type": "IfcText", "value": "The volumetric flow rate of the fluid."}, "partOfPsetTemplate": ["4a19b614-0ecb-4ae4-9111-d1509aa0e15f"], "templateType": "P_BOUNDEDVALUE", "primaryMeasureType": {"type": "IfcLabel", "value": "IfcVolumetricFlowRateMeasure"}, "secondaryMeasureType": {"type": "IfcLabel"}, "accessState": "READWRITE"}, {"type": "IfcSimplePropertyTemplate", "globalId": "3513dad5-9dc9-44fd-a374-ddd8cabeed9e", "ownerHistory": "c2d8c84f-23f5-44f8-b508-ba2c5967cffa", "name": {"type": "IfcLabel", "value": "Pressure"}, "description": {"type": "IfcText", "value": "The pressure of the fluid."}, "partOfPsetTemplate": ["4a19b614-0ecb-4ae4-9111-d1509aa0e15f"], "templateType": "P_BOUNDEDVALUE", "primaryMeasureType": {"type": "IfcLabel", "value": "IfcPressureMeasure"}, "secondaryMeasureType": {"type": "IfcLabel"}, "accessState": "READWRITE"}, {"type": "IfcSimplePropertyTemplate", "globalId": "22ff217f-49fc-4c2c-b896-ba9d0c849e02", "ownerHistory": "c2d8c84f-23f5-44f8-b508-ba2c5967cffa", "name": {"type": "IfcLabel", "value": "Velocity"}, "description": {"type": "IfcText", "value": "The velocity of the fluid."}, "partOfPsetTemplate": ["4a19b614-0ecb-4ae4-9111-d1509aa0e15f"], "templateType": "P_BOUNDEDVALUE", "primaryMeasureType": {"type": "IfcLabel", "value": "IfcLinearVelocityMeasure"}, "secondaryMeasureType": {"type": "IfcLabel"}, "accessState": "READWRITE"}, {"type": "IfcSimplePropertyTemplate", "globalId": "5d1dd0ac-0c94-4962-9762-05d4a8d56953", "ownerHistory": "c2d8c84f-23f5-44f8-b508-ba2c5967cffa", "name": {"type": "IfcLabel", "value": "NominalHeight"}, "description": {"type": "IfcText", "value": "The nominal height of the duct connection."}, "partOfPsetTemplate": ["4a19b614-0ecb-4ae4-9111-d1509aa0e15f"], "templateType": "P_SINGLEVALUE", "primaryMeasureType": {"type": "IfcLabel", "value": "IfcPositiveLengthMeasure"}, "secondaryMeasureType": {"type": "IfcLabel"}, "accessState": "READWRITE"}, {"type": "IfcSimplePropertyTemplate", "globalId": "77c36253-7a68-4816-8d26-52dd44af1021", "ownerHistory": "c2d8c84f-23f5-44f8-b508-ba2c5967cffa", "name": {"type": "IfcLabel", "value": "WetBulbTemperature"}, "description": {"type": "IfcText", "value": "Wet bulb temperature of the air."}, "partOfPsetTemplate": ["4a19b614-0ecb-4ae4-9111-d1509aa0e15f"], "templateType": "P_BOUNDEDVALUE", "primaryMeasureType": {"type": "IfcLabel", "value": "IfcThermodynamicTemperatureMeasure"}, "secondaryMeasureType": {"type": "IfcLabel"}, "accessState": "READWRITE"}, {"type": "IfcSimplePropertyTemplate", "globalId": "de03c4be-c3cd-4977-8b88-1b5c6abf17b4", "ownerHistory": "c2d8c84f-23f5-44f8-b508-ba2c5967cffa", "name": {"type": "IfcLabel", "value": "NominalWidth"}, "description": {"type": "IfcText", "value": "The nominal width or diameter of the duct connection."}, "partOfPsetTemplate": ["4a19b614-0ecb-4ae4-9111-d1509aa0e15f"], "templateType": "P_SINGLEVALUE", "primaryMeasureType": {"type": "IfcLabel", "value": "IfcPositiveLengthMeasure"}, "secondaryMeasureType": {"type": "IfcLabel"}, "accessState": "READWRITE"}], "defines": [{"type": "IfcRelDefinesByTemplate", "globalId": "b5e7c084-a055-4011-84b3-50b4bc90f176", "ownerHistory": "c2d8c84f-23f5-44f8-b508-ba2c5967cffa", "relatedPropertySets": [{"type": "IfcPropertySet", "globalId": "440957e2-c596-460a-9b2a-307ddebe3c5d", "ownerHistory": "c2d8c84f-23f5-44f8-b508-ba2c5967cffa", "name": {"type": "IfcLabel", "value": "Pset_DistributionPortTypeAirConditioning"}, "isDefinedBy": ["b5e7c084-a055-4011-84b3-50b4bc90f176"], "hasProperties": [{"type": "IfcPropertyBoundedValue", "name": {"type": "IfcIdentifier", "value": "DryBulbTemperature"}}, {"type": "IfcPropertySingleValue", "name": {"type": "IfcIdentifier", "value": "NominalWidth"}, "nominalValue": {"type": "IfcPositiveLengthMeasure", "value": 12.0}}, {"type": "IfcPropertySingleValue", "name": {"type": "IfcIdentifier", "value": "NominalHeight"}, "nominalValue": {"type": "IfcPositiveLengthMeasure", "value": 12.0}}, {"type": "IfcPropertyBoundedValue", "name": {"type": "IfcIdentifier", "value": "WetBulbTemperature"}}, {"type": "IfcPropertyEnumeratedValue", "name": {"type": "IfcIdentifier", "value": "ConnectionType"}, "enumerationValues": [{"type": "IfcLabel", "value": "OUTSIDESLEEVE"}], "enumerationReference": {"type": "IfcPropertyEnumeration", "name": {"type": "IfcLabel", "value": "PEnum_DuctConnectionType"}, "enumerationValues": [{"type": "IfcLabel", "value": "BEADEDSLEEVE"}, {"type": "IfcLabel", "value": "COMPRESSION"}, {"type": "IfcLabel", "value": "CRIMP"}, {"type": "IfcLabel", "value": "DRAWBAND"}, {"type": "IfcLabel", "value": "DRIVESLIP"}, {"type": "IfcLabel", "value": "FLANGED"}, {"type": "IfcLabel", "value": "OUTSIDESLEEVE"}, {"type": "IfcLabel", "value": "SLIPON"}, {"type": "IfcLabel", "value": "SOLDERED"}, {"type": "IfcLabel", "value": "SSLIP"}, {"type": "IfcLabel", "value": "STANDINGSEAM"}, {"type": "IfcLabel", "value": "SWEDGE"}, {"type": "IfcLabel", "value": "WELDED"}, {"type": "IfcLabel", "value": "OTHER"}, {"type": "IfcLabel", "value": "NONE"}, {"type": "IfcLabel", "value": "USERDEFINED"}, {"type": "IfcLabel", "value": "NOTDEFINED"}]}}, {"type": "IfcPropertyBoundedValue", "name": {"type": "IfcIdentifier", "value": "VolumetricFlowRate"}}, {"type": "IfcPropertyBoundedValue", "name": {"type": "IfcIdentifier", "value": "Velocity"}}, {"type": "IfcPropertyBoundedValue", "name": {"type": "IfcIdentifier", "value": "Pressure"}}, {"type": "IfcPropertySingleValue", "name": {"type": "IfcIdentifier", "value": "ConnectionSubType"}}]}], "relatingTemplate": "4a19b614-0ecb-4ae4-9111-d1509aa0e15f"}]}, "8980b34b-4895-43de-a38a-a852bc09493b", {"type": "IfcPropertySetTemplate", "globalId": "ad776a84-5aff-41ea-885a-8320b3e406a5", "ownerHistory": "c2d8c84f-23f5-44f8-b508-ba2c5967cffa", "name": {"type": "IfcLabel", "value": "Pset_AirTerminalTypeCommon"}, "description": {"type": "IfcText", "value": "Air terminal type common attributes.\\X\\0ASoundLevel attribute deleted in IFC2x2 Pset Addendum: Use IfcSoundProperties instead."}, "hasContext": ["21bff64c-c636-4b08-b796-4e6b7f3fe6d7"], "hasAssociations": [{"type": "IfcRelAssociatesLibrary", "globalId": "85255b67-3bcf-4c16-a985-5d44bd361b44", "ownerHistory": "c2d8c84f-23f5-44f8-b508-ba2c5967cffa", "relatingLibrary": {"type": "IfcLibraryReference", "identification": {"type": "IfcIdentifier", "value": "Pset_AirTerminalTypeCommon"}, "name": {"type": "IfcLabel", "value": "Pset_AirTerminalTypeCommon"}, "referencedLibrary": {"type": "IfcLibraryInformation", "name": {"type": "IfcLabel", "value": "IFC4"}, "publisher": "0223c0a2-caf6-4cf0-bfd3-dec8870e3e4a", "versionDate": {"type": "IfcDateTime", "value": "2011-09-26T20:52:24"}, "location": {"type": "IfcURIReference", "value": "http://buildingsmart-tech.org/ifc/IFC2x4/rc3/html/annex/annex-a/ifc2x4_rc3-templates.ifc"}}}}], "templateType": "PSET_TYPEDRIVENOVERRIDE", "applicableEntity": {"type": "IfcIdentifier", "value": "IfcAirTerminal"}, "hasPropertyTemplates": [{"type": "IfcSimplePropertyTemplate", "globalId": "35a82085-4f6f-4791-9c1b-135d114d16db", "ownerHistory": "c2d8c84f-23f5-44f8-b508-ba2c5967cffa", "name": {"type": "IfcLabel", "value": "AirFlowrateRange"}, "description": {"type": "IfcText", "value": "Air flowrate range within which the air terminal is designed to operate."}, "partOfPsetTemplate": ["ad776a84-5aff-41ea-885a-8320b3e406a5"], "templateType": "P_BOUNDEDVALUE", "primaryMeasureType": {"type": "IfcLabel", "value": "IfcVolumetricFlowRateMeasure"}, "secondaryMeasureType": {"type": "IfcLabel"}, "accessState": "READWRITE"}, {"type": "IfcSimplePropertyTemplate", "globalId": "d6fd7b9b-b5b9-4fd7-983a-9a206cf4fa04", "ownerHistory": "c2d8c84f-23f5-44f8-b508-ba2c5967cffa", "name": {"type": "IfcLabel", "value": "FlowControlType"}, "description": {"type": "IfcText", "value": "Type of flow control element that may be included as a part of the construction of the air terminal."}, "partOfPsetTemplate": ["ad776a84-5aff-41ea-885a-8320b3e406a5"], "templateType": "P_ENUMERATEDVALUE", "primaryMeasureType": {"type": "IfcLabel", "value": "IfcLabel"}, "enumerators": {"type": "IfcPropertyEnumeration", "name": {"type": "IfcLabel", "value": "PEnum_AirTerminalFlowControlType"}, "enumerationValues": [{"type": "IfcLabel", "value": "DAMPER"}, {"type": "IfcLabel", "value": "BELLOWS"}, {"type": "IfcLabel", "value": "NONE"}, {"type": "IfcLabel", "value": "OTHER"}, {"type": "IfcLabel", "value": "NOTKNOWN"}, {"type": "IfcLabel", "value": "UNSET"}]}, "accessState": "READWRITE"}, {"type": "IfcSimplePropertyTemplate", "globalId": "524d71b4-5c3b-4623-a257-b5a48a7224cf", "ownerHistory": "c2d8c84f-23f5-44f8-b508-ba2c5967cffa", "name": {"type": "IfcLabel", "value": "TemperatureRange"}, "description": {"type": "IfcText", "value": "Temperature range within which the air terminal is designed to operate."}, "partOfPsetTemplate": ["ad776a84-5aff-41ea-885a-8320b3e406a5"], "templateType": "P_BOUNDEDVALUE", "primaryMeasureType": {"type": "IfcLabel", "value": "IfcThermodynamicTemperatureMeasure"}, "secondaryMeasureType": {"type": "IfcLabel"}, "accessState": "READWRITE"}, {"type": "IfcSimplePropertyTemplate", "globalId": "8834a0f5-2d03-4417-a491-2ad7eb6dfa71", "ownerHistory": "c2d8c84f-23f5-44f8-b508-ba2c5967cffa", "name": {"type": "IfcLabel", "value": "MountingType"}, "description": {"type": "IfcText", "value": "The way the air terminal is mounted to the ceiling, wall, etc.\\X\\0A\\X\\0ASurface: mounted to the surface of something (e.g., wall, duct, etc.).\\X\\0AFlat flush: mounted flat and flush with a surface.\\X\\0ALay-in: mounted in a lay-in type ceiling (e.g., a dropped ceiling grid)."}, "partOfPsetTemplate": ["ad776a84-5aff-41ea-885a-8320b3e406a5"], "templateType": "P_ENUMERATEDVALUE", "primaryMeasureType": {"type": "IfcLabel", "value": "IfcLabel"}, "enumerators": {"type": "IfcPropertyEnumeration", "name": {"type": "IfcLabel", "value": "PEnum_AirTerminalMountingType"}, "enumerationValues": [{"type": "IfcLabel", "value": "SURFACE"}, {"type": "IfcLabel", "value": "FLATFLUSH"}, {"type": "IfcLabel", "value": "LAYIN"}, {"type": "IfcLabel", "value": "OTHER"}, {"type": "IfcLabel", "value": "NOTKNOWN"}, {"type": "IfcLabel", "value": "UNSET"}]}, "accessState": "READWRITE"}, {"type": "IfcSimplePropertyTemplate", "globalId": "132e8e27-2777-456e-aea5-68a33bf678d1", "ownerHistory": "c2d8c84f-23f5-44f8-b508-ba2c5967cffa", "name": {"type": "IfcLabel", "value": "AirDiffusionPerformanceIndex"}, "description": {"type": "IfcText", "value": "The Air Diffusion Performance Index (ADPI) is used for cooling mode conditions. If several measurements of air velocity and air temperature are made throughout the occupied zone of a space, the ADPI is the percentage of locations where measurements were taken that meet the specifications for effective draft temperature and air velocity."}, "partOfPsetTemplate": ["ad776a84-5aff-41ea-885a-8320b3e406a5"], "templateType": "P_SINGLEVALUE", "primaryMeasureType": {"type": "IfcLabel", "value": "IfcReal"}, "secondaryMeasureType": {"type": "IfcLabel"}, "accessState": "READWRITE"}, {"type": "IfcSimplePropertyTemplate", "globalId": "360d162c-2e3f-4547-8537-2f17b7ad17f1", "ownerHistory": "c2d8c84f-23f5-44f8-b508-ba2c5967cffa", "name": {"type": "IfcLabel", "value": "<PERSON><PERSON><PERSON>"}, "description": {"type": "IfcText", "value": "Shape of the air terminal. Slot is typically a long narrow supply device with an aspect ratio generally greater than 10 to 1."}, "partOfPsetTemplate": ["ad776a84-5aff-41ea-885a-8320b3e406a5"], "templateType": "P_ENUMERATEDVALUE", "primaryMeasureType": {"type": "IfcLabel", "value": "IfcLabel"}, "enumerators": {"type": "IfcPropertyEnumeration", "name": {"type": "IfcLabel", "value": "PEnum_AirTerminalShape"}, "enumerationValues": [{"type": "IfcLabel", "value": "ROUND"}, {"type": "IfcLabel", "value": "RECTANGULAR"}, {"type": "IfcLabel", "value": "SQUARE"}, {"type": "IfcLabel", "value": "SLOT"}, {"type": "IfcLabel", "value": "OTHER"}, {"type": "IfcLabel", "value": "NOTKNOWN"}, {"type": "IfcLabel", "value": "UNSET"}]}, "accessState": "READWRITE"}, {"type": "IfcSimplePropertyTemplate", "globalId": "5e028f17-1a52-400a-8480-5a5ea77a7216", "ownerHistory": "c2d8c84f-23f5-44f8-b508-ba2c5967cffa", "name": {"type": "IfcLabel", "value": "NumberOfSlots"}, "description": {"type": "IfcText", "value": "Number of slots."}, "partOfPsetTemplate": ["ad776a84-5aff-41ea-885a-8320b3e406a5"], "templateType": "P_SINGLEVALUE", "primaryMeasureType": {"type": "IfcLabel", "value": "IfcInteger"}, "secondaryMeasureType": {"type": "IfcLabel"}, "accessState": "READWRITE"}, {"type": "IfcSimplePropertyTemplate", "globalId": "6b5b093f-6ba5-4469-8dea-98e41c7f288e", "ownerHistory": "c2d8c84f-23f5-44f8-b508-ba2c5967cffa", "name": {"type": "IfcLabel", "value": "HasSoundAttenuator"}, "description": {"type": "IfcText", "value": "If TRUE, the air terminal has sound attenuation."}, "partOfPsetTemplate": ["ad776a84-5aff-41ea-885a-8320b3e406a5"], "templateType": "P_SINGLEVALUE", "primaryMeasureType": {"type": "IfcLabel", "value": "IfcBoolean"}, "secondaryMeasureType": {"type": "IfcLabel"}, "accessState": "READWRITE"}, {"type": "IfcSimplePropertyTemplate", "globalId": "8b44d5ff-33a7-43c9-9b15-398f5634ec86", "ownerHistory": "c2d8c84f-23f5-44f8-b508-ba2c5967cffa", "name": {"type": "IfcLabel", "value": "CoreType"}, "description": {"type": "IfcText", "value": "Identifies the way the core of the AirTerminal is constructed."}, "partOfPsetTemplate": ["ad776a84-5aff-41ea-885a-8320b3e406a5"], "templateType": "P_ENUMERATEDVALUE", "primaryMeasureType": {"type": "IfcLabel", "value": "IfcLabel"}, "enumerators": {"type": "IfcPropertyEnumeration", "name": {"type": "IfcLabel", "value": "PEnum_AirTerminalCoreType"}, "enumerationValues": [{"type": "IfcLabel", "value": "SHUTTERBLADE"}, {"type": "IfcLabel", "value": "CURVEDBLADE"}, {"type": "IfcLabel", "value": "REMOVABLE"}, {"type": "IfcLabel", "value": "REVERSIBLE"}, {"type": "IfcLabel", "value": "NONE"}, {"type": "IfcLabel", "value": "OTHER"}, {"type": "IfcLabel", "value": "NOTKNOWN"}, {"type": "IfcLabel", "value": "UNSET"}]}, "accessState": "READWRITE"}, {"type": "IfcSimplePropertyTemplate", "globalId": "3c1cf942-e444-49a2-b339-a58084651581", "ownerHistory": "c2d8c84f-23f5-44f8-b508-ba2c5967cffa", "name": {"type": "IfcLabel", "value": "FlowPattern"}, "description": {"type": "IfcText", "value": "Flow pattern."}, "partOfPsetTemplate": ["ad776a84-5aff-41ea-885a-8320b3e406a5"], "templateType": "P_ENUMERATEDVALUE", "primaryMeasureType": {"type": "IfcLabel", "value": "IfcLabel"}, "enumerators": {"type": "IfcPropertyEnumeration", "name": {"type": "IfcLabel", "value": "PEnum_AirTerminalFlowPattern"}, "enumerationValues": [{"type": "IfcLabel", "value": "LINEARSINGLE"}, {"type": "IfcLabel", "value": "LINEARDOUBLE"}, {"type": "IfcLabel", "value": "LINEARFOURWAY"}, {"type": "IfcLabel", "value": "RADIAL"}, {"type": "IfcLabel", "value": "SWIRL"}, {"type": "IfcLabel", "value": "DISPLACMENT"}, {"type": "IfcLabel", "value": "COMPACTJET"}, {"type": "IfcLabel", "value": "OTHER"}, {"type": "IfcLabel", "value": "NOTKNOWN"}, {"type": "IfcLabel", "value": "UNSET"}]}, "accessState": "READWRITE"}, {"type": "IfcSimplePropertyTemplate", "globalId": "e9ce0c08-0b1f-49ad-a19d-52a1df3de049", "ownerHistory": "c2d8c84f-23f5-44f8-b508-ba2c5967cffa", "name": {"type": "IfcLabel", "value": "Reference"}, "description": {"type": "IfcText", "value": "Reference ID for this specified type in this project (e.g. type 'A-1'), provided, if there is no classification reference to a recognized classification system used."}, "partOfPsetTemplate": ["ad776a84-5aff-41ea-885a-8320b3e406a5"], "templateType": "P_SINGLEVALUE", "primaryMeasureType": {"type": "IfcLabel", "value": "IfcIdentifier"}, "secondaryMeasureType": {"type": "IfcLabel"}, "accessState": "READWRITE"}, {"type": "IfcSimplePropertyTemplate", "globalId": "452ad894-e19e-49bd-a26c-aaa7b340a90f", "ownerHistory": "c2d8c84f-23f5-44f8-b508-ba2c5967cffa", "name": {"type": "IfcLabel", "value": "<PERSON>hr<PERSON><PERSON><PERSON><PERSON>"}, "description": {"type": "IfcText", "value": "The horizontal or vertical axial distance an airstream travels after leaving an AirTerminal before the maximum stream velocity is reduced to a specified terminal velocity under isothermal conditions at the upper value of the AirFlowrateRange."}, "partOfPsetTemplate": ["ad776a84-5aff-41ea-885a-8320b3e406a5"], "templateType": "P_SINGLEVALUE", "primaryMeasureType": {"type": "IfcLabel", "value": "IfcLengthMeasure"}, "secondaryMeasureType": {"type": "IfcLabel"}, "accessState": "READWRITE"}, {"type": "IfcSimplePropertyTemplate", "globalId": "15ec7120-5783-4a4c-ba02-2632b2bb11f1", "ownerHistory": "c2d8c84f-23f5-44f8-b508-ba2c5967cffa", "name": {"type": "IfcLabel", "value": "FinishColor"}, "description": {"type": "IfcText", "value": "The finish color for the air terminal."}, "partOfPsetTemplate": ["ad776a84-5aff-41ea-885a-8320b3e406a5"], "templateType": "P_SINGLEVALUE", "primaryMeasureType": {"type": "IfcLabel", "value": "IfcLabel"}, "secondaryMeasureType": {"type": "IfcLabel"}, "accessState": "READWRITE"}, {"type": "IfcSimplePropertyTemplate", "globalId": "bdfaac66-29f7-4725-8efa-3cb900aeee5e", "ownerHistory": "c2d8c84f-23f5-44f8-b508-ba2c5967cffa", "name": {"type": "IfcLabel", "value": "HasIntegralControl"}, "description": {"type": "IfcText", "value": "If TRUE, a self powered temperature control is included in the AirTerminal."}, "partOfPsetTemplate": ["ad776a84-5aff-41ea-885a-8320b3e406a5"], "templateType": "P_SINGLEVALUE", "primaryMeasureType": {"type": "IfcLabel", "value": "IfcBoolean"}, "secondaryMeasureType": {"type": "IfcLabel"}, "accessState": "READWRITE"}, {"type": "IfcSimplePropertyTemplate", "globalId": "2f431ba9-f0a5-418b-a12f-96f1fd09fe4e", "ownerHistory": "c2d8c84f-23f5-44f8-b508-ba2c5967cffa", "name": {"type": "IfcLabel", "value": "CoreSetVertical"}, "description": {"type": "IfcText", "value": "Degree of vertical (in the Y-axis of the LocalPlacement) blade set from the centerline."}, "partOfPsetTemplate": ["ad776a84-5aff-41ea-885a-8320b3e406a5"], "templateType": "P_SINGLEVALUE", "primaryMeasureType": {"type": "IfcLabel", "value": "IfcPlaneAngleMeasure"}, "secondaryMeasureType": {"type": "IfcLabel"}, "accessState": "READWRITE"}, {"type": "IfcSimplePropertyTemplate", "globalId": "a462f4ab-dcec-41f0-b2cc-d33e191bcab7", "ownerHistory": "c2d8c84f-23f5-44f8-b508-ba2c5967cffa", "name": {"type": "IfcLabel", "value": "AirFlowrateVersusFlowControlElement"}, "description": {"type": "IfcText", "value": "Air flowrate versus flow control element position at nominal pressure drop."}, "partOfPsetTemplate": ["ad776a84-5aff-41ea-885a-8320b3e406a5"], "templateType": "P_TABLEVALUE", "primaryMeasureType": {"type": "IfcLabel", "value": "IfcVolumetricFlowRateMeasure"}, "secondaryMeasureType": {"type": "IfcLabel", "value": "IfcPositiveRatioMeasure"}, "accessState": "READWRITE"}, {"type": "IfcSimplePropertyTemplate", "globalId": "c025c437-60a6-4e24-9089-2a57463dde1f", "ownerHistory": "c2d8c84f-23f5-44f8-b508-ba2c5967cffa", "name": {"type": "IfcLabel", "value": "EffectiveArea"}, "description": {"type": "IfcText", "value": "Effective discharge area of the air terminal."}, "partOfPsetTemplate": ["ad776a84-5aff-41ea-885a-8320b3e406a5"], "templateType": "P_SINGLEVALUE", "primaryMeasureType": {"type": "IfcLabel", "value": "IfcAreaMeasure"}, "secondaryMeasureType": {"type": "IfcLabel"}, "accessState": "READWRITE"}, {"type": "IfcSimplePropertyTemplate", "globalId": "a7a95863-3b62-4003-9f77-40b4b523e932", "ownerHistory": "c2d8c84f-23f5-44f8-b508-ba2c5967cffa", "name": {"type": "IfcLabel", "value": "NeckArea"}, "description": {"type": "IfcText", "value": "Neck area of the air terminal."}, "partOfPsetTemplate": ["ad776a84-5aff-41ea-885a-8320b3e406a5"], "templateType": "P_SINGLEVALUE", "primaryMeasureType": {"type": "IfcLabel", "value": "IfcAreaMeasure"}, "secondaryMeasureType": {"type": "IfcLabel"}, "accessState": "READWRITE"}, {"type": "IfcSimplePropertyTemplate", "globalId": "5482c216-9e11-443f-b8de-63ae30adda29", "ownerHistory": "c2d8c84f-23f5-44f8-b508-ba2c5967cffa", "name": {"type": "IfcLabel", "value": "HasThermalInsulation"}, "description": {"type": "IfcText", "value": "If TRUE, the air terminal has thermal insulation."}, "partOfPsetTemplate": ["ad776a84-5aff-41ea-885a-8320b3e406a5"], "templateType": "P_SINGLEVALUE", "primaryMeasureType": {"type": "IfcLabel", "value": "IfcBoolean"}, "secondaryMeasureType": {"type": "IfcLabel"}, "accessState": "READWRITE"}, {"type": "IfcSimplePropertyTemplate", "globalId": "ece68a95-34d3-4f10-bb48-a3461c773452", "ownerHistory": "c2d8c84f-23f5-44f8-b508-ba2c5967cffa", "name": {"type": "IfcLabel", "value": "CoreSetHorizontal"}, "description": {"type": "IfcText", "value": "Degree of horizontal (in the X-axis of the LocalPlacement) blade set from the centerline."}, "partOfPsetTemplate": ["ad776a84-5aff-41ea-885a-8320b3e406a5"], "templateType": "P_SINGLEVALUE", "primaryMeasureType": {"type": "IfcLabel", "value": "IfcPlaneAngleMeasure"}, "secondaryMeasureType": {"type": "IfcLabel"}, "accessState": "READWRITE"}, {"type": "IfcSimplePropertyTemplate", "globalId": "f60984b1-c56f-4f45-adbf-503a2fafe0eb", "ownerHistory": "c2d8c84f-23f5-44f8-b508-ba2c5967cffa", "name": {"type": "IfcLabel", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "description": {"type": "IfcText", "value": "Slot width."}, "partOfPsetTemplate": ["ad776a84-5aff-41ea-885a-8320b3e406a5"], "templateType": "P_SINGLEVALUE", "primaryMeasureType": {"type": "IfcLabel", "value": "IfcPositiveLengthMeasure"}, "secondaryMeasureType": {"type": "IfcLabel"}, "accessState": "READWRITE"}, {"type": "IfcSimplePropertyTemplate", "globalId": "e698ec7d-20c9-4da2-b8a2-15ea50b57aee", "ownerHistory": "c2d8c84f-23f5-44f8-b508-ba2c5967cffa", "name": {"type": "IfcLabel", "value": "FaceType"}, "description": {"type": "IfcText", "value": "Identifies how the terminal face of an AirTerminal is constructed."}, "partOfPsetTemplate": ["ad776a84-5aff-41ea-885a-8320b3e406a5"], "templateType": "P_ENUMERATEDVALUE", "primaryMeasureType": {"type": "IfcLabel", "value": "IfcLabel"}, "enumerators": {"type": "IfcPropertyEnumeration", "name": {"type": "IfcLabel", "value": "PEnum_AirTerminalFaceType"}, "enumerationValues": [{"type": "IfcLabel", "value": "FOURWAYPATTERN"}, {"type": "IfcLabel", "value": "SINGLEDEFLECTION"}, {"type": "IfcLabel", "value": "DOUBLEDEFLECTION"}, {"type": "IfcLabel", "value": "SIGHTPROOF"}, {"type": "IfcLabel", "value": "EGGCRATE"}, {"type": "IfcLabel", "value": "PERFORATED"}, {"type": "IfcLabel", "value": "LOUVERED"}, {"type": "IfcLabel", "value": "OTHER"}, {"type": "IfcLabel", "value": "NOTKNOWN"}, {"type": "IfcLabel", "value": "UNSET"}]}, "accessState": "READWRITE"}, {"type": "IfcSimplePropertyTemplate", "globalId": "7c06e44b-1d9d-4cad-9e9d-a9afce31197f", "ownerHistory": "c2d8c84f-23f5-44f8-b508-ba2c5967cffa", "name": {"type": "IfcLabel", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "description": {"type": "IfcText", "value": "Slot length."}, "partOfPsetTemplate": ["ad776a84-5aff-41ea-885a-8320b3e406a5"], "templateType": "P_SINGLEVALUE", "primaryMeasureType": {"type": "IfcLabel", "value": "IfcPositiveLengthMeasure"}, "secondaryMeasureType": {"type": "IfcLabel"}, "accessState": "READWRITE"}, {"type": "IfcSimplePropertyTemplate", "globalId": "7540add2-e15b-4010-952d-c8a845bf902d", "ownerHistory": "c2d8c84f-23f5-44f8-b508-ba2c5967cffa", "name": {"type": "IfcLabel", "value": "FinishType"}, "description": {"type": "IfcText", "value": "The type of finish for the air terminal."}, "partOfPsetTemplate": ["ad776a84-5aff-41ea-885a-8320b3e406a5"], "templateType": "P_ENUMERATEDVALUE", "primaryMeasureType": {"type": "IfcLabel", "value": "IfcLabel"}, "enumerators": {"type": "IfcPropertyEnumeration", "name": {"type": "IfcLabel", "value": "PEnum_AirTerminalFinishType"}, "enumerationValues": [{"type": "IfcLabel", "value": "ANNODIZED"}, {"type": "IfcLabel", "value": "PAINTED"}, {"type": "IfcLabel", "value": "NONE"}, {"type": "IfcLabel", "value": "OTHER"}, {"type": "IfcLabel", "value": "NOTKNOWN"}, {"type": "IfcLabel", "value": "UNSET"}]}, "accessState": "READWRITE"}, {"type": "IfcSimplePropertyTemplate", "globalId": "e25d1437-06f1-41b6-a121-25a433f411ed", "ownerHistory": "c2d8c84f-23f5-44f8-b508-ba2c5967cffa", "name": {"type": "IfcLabel", "value": "DischargeDirection"}, "description": {"type": "IfcText", "value": "Discharge direction of the air terminal.\\X\\0A\\X\\0AParallel: discharges parallel to mounting surface designed so that flow attaches to the surface.\\X\\0APerpendicular:  discharges away from mounting surface.\\X\\0AAdjustable: both parallel and perpendicular discharge."}, "partOfPsetTemplate": ["ad776a84-5aff-41ea-885a-8320b3e406a5"], "templateType": "P_ENUMERATEDVALUE", "primaryMeasureType": {"type": "IfcLabel", "value": "IfcLabel"}, "enumerators": {"type": "IfcPropertyEnumeration", "name": {"type": "IfcLabel", "value": "PEnum_AirTerminalDischargeDirection"}, "enumerationValues": [{"type": "IfcLabel", "value": "PARALLEL"}, {"type": "IfcLabel", "value": "PERPENDICULAR"}, {"type": "IfcLabel", "value": "ADJUSTABLE"}, {"type": "IfcLabel", "value": "OTHER"}, {"type": "IfcLabel", "value": "NOTKNOWN"}, {"type": "IfcLabel", "value": "UNSET"}]}, "accessState": "READWRITE"}, {"type": "IfcSimplePropertyTemplate", "globalId": "22b48ad5-3414-4e6c-8fae-336161939d4f", "ownerHistory": "c2d8c84f-23f5-44f8-b508-ba2c5967cffa", "name": {"type": "IfcLabel", "value": "Status"}, "description": {"type": "IfcText", "value": "Status of the element, predominately used in renovation or retrofitting projects. The status can be assigned to as \"New\" - element designed as new addition, \"Existing\" - element exists and remains, \"Demolish\" - element existed but is to be demolished,  \"Temporary\" - element will exists only temporary (like a temporary support structure)."}, "partOfPsetTemplate": ["ad776a84-5aff-41ea-885a-8320b3e406a5"], "templateType": "P_ENUMERATEDVALUE", "primaryMeasureType": {"type": "IfcLabel", "value": "IfcLabel"}, "enumerators": {"type": "IfcPropertyEnumeration", "name": {"type": "IfcLabel", "value": "PEnum_Status"}, "enumerationValues": [{"type": "IfcLabel", "value": "NEW"}, {"type": "IfcLabel", "value": "EXISTING"}, {"type": "IfcLabel", "value": "DEMOLISH"}, {"type": "IfcLabel", "value": "TEMPORARY"}, {"type": "IfcLabel", "value": "OTHER"}, {"type": "IfcLabel", "value": "NOTKNOWN"}, {"type": "IfcLabel", "value": "UNSET"}]}, "accessState": "READWRITE"}], "defines": [{"type": "IfcRelDefinesByTemplate", "globalId": "96c563da-b857-4866-9377-12d5e974d03c", "ownerHistory": "c2d8c84f-23f5-44f8-b508-ba2c5967cffa", "relatedPropertySets": [{"type": "IfcPropertySet", "globalId": "c46cb661-483e-412c-963c-797c8689b399", "ownerHistory": "c2d8c84f-23f5-44f8-b508-ba2c5967cffa", "name": {"type": "IfcLabel", "value": "Pset_AirTerminalTypeCommon"}, "isDefinedBy": ["96c563da-b857-4866-9377-12d5e974d03c"], "hasProperties": [{"type": "IfcPropertySingleValue", "name": {"type": "IfcIdentifier", "value": "HasSoundAttenuator"}}, {"type": "IfcPropertySingleValue", "name": {"type": "IfcIdentifier", "value": "AirDiffusionPerformanceIndex"}}, {"type": "IfcPropertySingleValue", "name": {"type": "IfcIdentifier", "value": "NeckArea"}}, {"type": "IfcPropertyEnumeratedValue", "name": {"type": "IfcIdentifier", "value": "FlowPattern"}, "enumerationReference": {"type": "IfcPropertyEnumeration", "name": {"type": "IfcLabel", "value": "PEnum_AirTerminalFlowPattern"}, "enumerationValues": [{"type": "IfcLabel", "value": "LINEARSINGLE"}, {"type": "IfcLabel", "value": "LINEARDOUBLE"}, {"type": "IfcLabel", "value": "LINEARFOURWAY"}, {"type": "IfcLabel", "value": "RADIAL"}, {"type": "IfcLabel", "value": "SWIRL"}, {"type": "IfcLabel", "value": "DISPLACMENT"}, {"type": "IfcLabel", "value": "COMPACTJET"}, {"type": "IfcLabel", "value": "OTHER"}, {"type": "IfcLabel", "value": "NOTKNOWN"}, {"type": "IfcLabel", "value": "UNSET"}]}}, {"type": "IfcPropertySingleValue", "name": {"type": "IfcIdentifier", "value": "NumberOfSlots"}}, {"type": "IfcPropertySingleValue", "name": {"type": "IfcIdentifier", "value": "HasIntegralControl"}}, {"type": "IfcPropertySingleValue", "name": {"type": "IfcIdentifier", "value": "HasThermalInsulation"}}, {"type": "IfcPropertyEnumeratedValue", "name": {"type": "IfcIdentifier", "value": "FaceType"}, "enumerationReference": {"type": "IfcPropertyEnumeration", "name": {"type": "IfcLabel", "value": "PEnum_AirTerminalFaceType"}, "enumerationValues": [{"type": "IfcLabel", "value": "FOURWAYPATTERN"}, {"type": "IfcLabel", "value": "SINGLEDEFLECTION"}, {"type": "IfcLabel", "value": "DOUBLEDEFLECTION"}, {"type": "IfcLabel", "value": "SIGHTPROOF"}, {"type": "IfcLabel", "value": "EGGCRATE"}, {"type": "IfcLabel", "value": "PERFORATED"}, {"type": "IfcLabel", "value": "LOUVERED"}, {"type": "IfcLabel", "value": "OTHER"}, {"type": "IfcLabel", "value": "NOTKNOWN"}, {"type": "IfcLabel", "value": "UNSET"}]}}, {"type": "IfcPropertyEnumeratedValue", "name": {"type": "IfcIdentifier", "value": "FinishType"}, "enumerationReference": {"type": "IfcPropertyEnumeration", "name": {"type": "IfcLabel", "value": "PEnum_AirTerminalFinishType"}, "enumerationValues": [{"type": "IfcLabel", "value": "ANNODIZED"}, {"type": "IfcLabel", "value": "PAINTED"}, {"type": "IfcLabel", "value": "NONE"}, {"type": "IfcLabel", "value": "OTHER"}, {"type": "IfcLabel", "value": "NOTKNOWN"}, {"type": "IfcLabel", "value": "UNSET"}]}}, {"type": "IfcPropertySingleValue", "name": {"type": "IfcIdentifier", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, {"type": "IfcPropertyBoundedValue", "name": {"type": "IfcIdentifier", "value": "AirFlowrateRange"}}, {"type": "IfcPropertyEnumeratedValue", "name": {"type": "IfcIdentifier", "value": "CoreType"}, "enumerationReference": {"type": "IfcPropertyEnumeration", "name": {"type": "IfcLabel", "value": "PEnum_AirTerminalCoreType"}, "enumerationValues": [{"type": "IfcLabel", "value": "SHUTTERBLADE"}, {"type": "IfcLabel", "value": "CURVEDBLADE"}, {"type": "IfcLabel", "value": "REMOVABLE"}, {"type": "IfcLabel", "value": "REVERSIBLE"}, {"type": "IfcLabel", "value": "NONE"}, {"type": "IfcLabel", "value": "OTHER"}, {"type": "IfcLabel", "value": "NOTKNOWN"}, {"type": "IfcLabel", "value": "UNSET"}]}}, {"type": "IfcPropertySingleValue", "name": {"type": "IfcIdentifier", "value": "CoreSetHorizontal"}}, {"type": "IfcPropertyEnumeratedValue", "name": {"type": "IfcIdentifier", "value": "DischargeDirection"}, "enumerationReference": {"type": "IfcPropertyEnumeration", "name": {"type": "IfcLabel", "value": "PEnum_AirTerminalDischargeDirection"}, "enumerationValues": [{"type": "IfcLabel", "value": "PARALLEL"}, {"type": "IfcLabel", "value": "PERPENDICULAR"}, {"type": "IfcLabel", "value": "ADJUSTABLE"}, {"type": "IfcLabel", "value": "OTHER"}, {"type": "IfcLabel", "value": "NOTKNOWN"}, {"type": "IfcLabel", "value": "UNSET"}]}}, {"type": "IfcPropertyEnumeratedValue", "name": {"type": "IfcIdentifier", "value": "MountingType"}, "enumerationReference": {"type": "IfcPropertyEnumeration", "name": {"type": "IfcLabel", "value": "PEnum_AirTerminalMountingType"}, "enumerationValues": [{"type": "IfcLabel", "value": "SURFACE"}, {"type": "IfcLabel", "value": "FLATFLUSH"}, {"type": "IfcLabel", "value": "LAYIN"}, {"type": "IfcLabel", "value": "OTHER"}, {"type": "IfcLabel", "value": "NOTKNOWN"}, {"type": "IfcLabel", "value": "UNSET"}]}}, {"type": "IfcPropertySingleValue", "name": {"type": "IfcIdentifier", "value": "CoreSetVertical"}}, {"type": "IfcPropertyEnumeratedValue", "name": {"type": "IfcIdentifier", "value": "<PERSON><PERSON><PERSON>"}, "enumerationValues": [{"type": "IfcLabel", "value": "SQUARE"}], "enumerationReference": {"type": "IfcPropertyEnumeration", "name": {"type": "IfcLabel", "value": "PEnum_AirTerminalShape"}, "enumerationValues": [{"type": "IfcLabel", "value": "ROUND"}, {"type": "IfcLabel", "value": "RECTANGULAR"}, {"type": "IfcLabel", "value": "SQUARE"}, {"type": "IfcLabel", "value": "SLOT"}, {"type": "IfcLabel", "value": "OTHER"}, {"type": "IfcLabel", "value": "NOTKNOWN"}, {"type": "IfcLabel", "value": "UNSET"}]}}, {"type": "IfcPropertySingleValue", "name": {"type": "IfcIdentifier", "value": "Reference"}}, {"type": "IfcPropertyBoundedValue", "name": {"type": "IfcIdentifier", "value": "TemperatureRange"}}, {"type": "IfcPropertySingleValue", "name": {"type": "IfcIdentifier", "value": "FinishColor"}}, {"type": "IfcPropertyEnumeratedValue", "name": {"type": "IfcIdentifier", "value": "FlowControlType"}, "enumerationReference": {"type": "IfcPropertyEnumeration", "name": {"type": "IfcLabel", "value": "PEnum_AirTerminalFlowControlType"}, "enumerationValues": [{"type": "IfcLabel", "value": "DAMPER"}, {"type": "IfcLabel", "value": "BELLOWS"}, {"type": "IfcLabel", "value": "NONE"}, {"type": "IfcLabel", "value": "OTHER"}, {"type": "IfcLabel", "value": "NOTKNOWN"}, {"type": "IfcLabel", "value": "UNSET"}]}}, {"type": "IfcPropertyEnumeratedValue", "name": {"type": "IfcIdentifier", "value": "Status"}, "enumerationReference": {"type": "IfcPropertyEnumeration", "name": {"type": "IfcLabel", "value": "PEnum_Status"}, "enumerationValues": [{"type": "IfcLabel", "value": "NEW"}, {"type": "IfcLabel", "value": "EXISTING"}, {"type": "IfcLabel", "value": "DEMOLISH"}, {"type": "IfcLabel", "value": "TEMPORARY"}, {"type": "IfcLabel", "value": "OTHER"}, {"type": "IfcLabel", "value": "NOTKNOWN"}, {"type": "IfcLabel", "value": "UNSET"}]}}, {"type": "IfcPropertySingleValue", "name": {"type": "IfcIdentifier", "value": "<PERSON>hr<PERSON><PERSON><PERSON><PERSON>"}}, {"type": "IfcPropertyTableValue", "name": {"type": "IfcIdentifier", "value": "AirFlowrateVersusFlowControlElement"}}, {"type": "IfcPropertySingleValue", "name": {"type": "IfcIdentifier", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, {"type": "IfcPropertySingleValue", "name": {"type": "IfcIdentifier", "value": "EffectiveArea"}}]}], "relatingTemplate": "ad776a84-5aff-41ea-885a-8320b3e406a5"}]}, {"type": "IfcPropertySetTemplate", "globalId": "3aeaeb12-e90f-40be-90a2-4a0e84527b6d", "ownerHistory": "c2d8c84f-23f5-44f8-b508-ba2c5967cffa", "name": {"type": "IfcLabel", "value": "Pset_ManufacturerTypeInformation"}, "description": {"type": "IfcText", "value": "Defines characteristics of types (ranges) of manufactured products that may be given by the manufacturer. Note that the term 'manufactured' may also be used to refer to products that are supplied and identified by the supplier or that are assembled off site by a third party provider. \\X\\0AHISTORY: This property set replaces the entity IfcManufacturerInformation from previous IFC releases. IFC 2x4: AssemblyPlace property added."}, "hasContext": ["21bff64c-c636-4b08-b796-4e6b7f3fe6d7"], "hasAssociations": [{"type": "IfcRelAssociatesLibrary", "globalId": "c9854d29-399d-4ec3-a330-4dd2b105d56e", "ownerHistory": "c2d8c84f-23f5-44f8-b508-ba2c5967cffa", "relatingLibrary": {"type": "IfcLibraryReference", "identification": {"type": "IfcIdentifier", "value": "Pset_ManufacturerTypeInformation"}, "name": {"type": "IfcLabel", "value": "Pset_ManufacturerTypeInformation"}, "referencedLibrary": {"type": "IfcLibraryInformation", "name": {"type": "IfcLabel", "value": "IFC4"}, "publisher": "0223c0a2-caf6-4cf0-bfd3-dec8870e3e4a", "versionDate": {"type": "IfcDateTime", "value": "2011-09-26T20:52:24"}, "location": {"type": "IfcURIReference", "value": "http://buildingsmart-tech.org/ifc/IFC2x4/rc3/html/annex/annex-a/ifc2x4_rc3-templates.ifc"}}}}], "templateType": "PSET_TYPEDRIVENOVERRIDE", "applicableEntity": {"type": "IfcIdentifier", "value": "IfcElement"}, "hasPropertyTemplates": [{"type": "IfcSimplePropertyTemplate", "globalId": "2340a5ba-0fa3-4ee5-a501-a1700a2e49c0", "ownerHistory": "c2d8c84f-23f5-44f8-b508-ba2c5967cffa", "name": {"type": "IfcLabel", "value": "<PERSON><PERSON><PERSON><PERSON>"}, "description": {"type": "IfcText", "value": "The descriptive model name of the product model (or product line) as assigned by the manufacturer of the manufactured item."}, "partOfPsetTemplate": ["3aeaeb12-e90f-40be-90a2-4a0e84527b6d"], "templateType": "P_SINGLEVALUE", "primaryMeasureType": {"type": "IfcLabel", "value": "IfcLabel"}, "secondaryMeasureType": {"type": "IfcLabel"}, "accessState": "READWRITE"}, {"type": "IfcSimplePropertyTemplate", "globalId": "fad33fbe-592e-4144-93c7-976840a81679", "ownerHistory": "c2d8c84f-23f5-44f8-b508-ba2c5967cffa", "name": {"type": "IfcLabel", "value": "ModelReference"}, "description": {"type": "IfcText", "value": "The model number or designator of the product model (or product line) as assigned by the manufacturer of the manufactured item."}, "partOfPsetTemplate": ["3aeaeb12-e90f-40be-90a2-4a0e84527b6d"], "templateType": "P_SINGLEVALUE", "primaryMeasureType": {"type": "IfcLabel", "value": "IfcLabel"}, "secondaryMeasureType": {"type": "IfcLabel"}, "accessState": "READWRITE"}, {"type": "IfcSimplePropertyTemplate", "globalId": "1cb586fe-2acf-413c-9f6c-948030652d40", "ownerHistory": "c2d8c84f-23f5-44f8-b508-ba2c5967cffa", "name": {"type": "IfcLabel", "value": "Manufacturer"}, "description": {"type": "IfcText", "value": "The organization that manufactured and/or assembled the item."}, "partOfPsetTemplate": ["3aeaeb12-e90f-40be-90a2-4a0e84527b6d"], "templateType": "P_SINGLEVALUE", "primaryMeasureType": {"type": "IfcLabel", "value": "IfcLabel"}, "secondaryMeasureType": {"type": "IfcLabel"}, "accessState": "READWRITE"}, {"type": "IfcSimplePropertyTemplate", "globalId": "bd3be35b-fcf1-4280-aac8-9cb938787e77", "ownerHistory": "c2d8c84f-23f5-44f8-b508-ba2c5967cffa", "name": {"type": "IfcLabel", "value": "ProductionYear"}, "description": {"type": "IfcText", "value": "The year of production of the manufactured item."}, "partOfPsetTemplate": ["3aeaeb12-e90f-40be-90a2-4a0e84527b6d"], "templateType": "P_SINGLEVALUE", "primaryMeasureType": {"type": "IfcLabel", "value": "IfcLabel"}, "secondaryMeasureType": {"type": "IfcLabel"}, "accessState": "READWRITE"}, {"type": "IfcSimplePropertyTemplate", "globalId": "1d1e6f95-2252-4efc-97c2-536bea2c9ee8", "ownerHistory": "c2d8c84f-23f5-44f8-b508-ba2c5967cffa", "name": {"type": "IfcLabel", "value": "AssemblyPlace"}, "description": {"type": "IfcText", "value": "Enumeration defining where the assembly is intended to take place, either in a factory or on the building site."}, "partOfPsetTemplate": ["3aeaeb12-e90f-40be-90a2-4a0e84527b6d"], "templateType": "P_ENUMERATEDVALUE", "primaryMeasureType": {"type": "IfcLabel", "value": "IfcLabel"}, "enumerators": {"type": "IfcPropertyEnumeration", "name": {"type": "IfcLabel", "value": "PEnum_AssemblyPlace"}, "enumerationValues": [{"type": "IfcLabel", "value": "FACTORY"}, {"type": "IfcLabel", "value": "OFFSITE"}, {"type": "IfcLabel", "value": "SITE"}, {"type": "IfcLabel", "value": "OTHER"}, {"type": "IfcLabel", "value": "NOTKNOWN"}, {"type": "IfcLabel", "value": "UNSET"}]}, "accessState": "READWRITE"}, {"type": "IfcSimplePropertyTemplate", "globalId": "76ab99e8-ded9-414f-b3b4-97dba60b6d0c", "ownerHistory": "c2d8c84f-23f5-44f8-b508-ba2c5967cffa", "name": {"type": "IfcLabel", "value": "ArticleNumber"}, "description": {"type": "IfcText", "value": "Article number or reference that is be applied to a configured product according to a standard scheme for article number definition as defined by the manufacturer. It is often used as the purchasing number."}, "partOfPsetTemplate": ["3aeaeb12-e90f-40be-90a2-4a0e84527b6d"], "templateType": "P_SINGLEVALUE", "primaryMeasureType": {"type": "IfcLabel", "value": "IfcIdentifier"}, "secondaryMeasureType": {"type": "IfcLabel"}, "accessState": "READWRITE"}, {"type": "IfcSimplePropertyTemplate", "globalId": "e8470f04-90e1-4619-b571-da16b1981202", "ownerHistory": "c2d8c84f-23f5-44f8-b508-ba2c5967cffa", "name": {"type": "IfcLabel", "value": "GlobalTradeItemNumber"}, "description": {"type": "IfcText", "value": "The Global Trade Item Number (GTIN) is an identifier for trade items developed by GS1 (www.gs1.org)."}, "partOfPsetTemplate": ["3aeaeb12-e90f-40be-90a2-4a0e84527b6d"], "templateType": "P_SINGLEVALUE", "primaryMeasureType": {"type": "IfcLabel", "value": "IfcIdentifier"}, "secondaryMeasureType": {"type": "IfcLabel"}, "accessState": "READWRITE"}], "defines": [{"type": "IfcRelDefinesByTemplate", "globalId": "6a8cfd1d-a203-4cb1-93e7-7ce36edd6f26", "ownerHistory": "c2d8c84f-23f5-44f8-b508-ba2c5967cffa", "relatedPropertySets": [{"type": "IfcPropertySet", "globalId": "95580094-1679-4fb0-b7dd-ce97aa631bc4", "ownerHistory": "c2d8c84f-23f5-44f8-b508-ba2c5967cffa", "name": {"type": "IfcLabel", "value": "Pset_ManufacturerTypeInformation"}, "isDefinedBy": ["6a8cfd1d-a203-4cb1-93e7-7ce36edd6f26"], "hasProperties": [{"type": "IfcPropertySingleValue", "name": {"type": "IfcIdentifier", "value": "ProductionYear"}, "nominalValue": {"type": "IfcLabel", "value": "2011"}}, {"type": "IfcPropertyEnumeratedValue", "name": {"type": "IfcIdentifier", "value": "AssemblyPlace"}, "enumerationValues": [{"type": "IfcLabel", "value": "FACTORY"}], "enumerationReference": {"type": "IfcPropertyEnumeration", "name": {"type": "IfcLabel", "value": "PEnum_AssemblyPlace"}, "enumerationValues": [{"type": "IfcLabel", "value": "FACTORY"}, {"type": "IfcLabel", "value": "OFFSITE"}, {"type": "IfcLabel", "value": "SITE"}, {"type": "IfcLabel", "value": "OTHER"}, {"type": "IfcLabel", "value": "NOTKNOWN"}, {"type": "IfcLabel", "value": "UNSET"}]}}, {"type": "IfcPropertySingleValue", "name": {"type": "IfcIdentifier", "value": "<PERSON><PERSON><PERSON><PERSON>"}, "nominalValue": {"type": "IfcLabel", "value": "Ceiling Diffuser"}}, {"type": "IfcPropertySingleValue", "name": {"type": "IfcIdentifier", "value": "ModelReference"}, "nominalValue": {"type": "IfcLabel", "value": "1234"}}, {"type": "IfcPropertySingleValue", "name": {"type": "IfcIdentifier", "value": "GlobalTradeItemNumber"}}, {"type": "IfcPropertySingleValue", "name": {"type": "IfcIdentifier", "value": "ArticleNumber"}}, {"type": "IfcPropertySingleValue", "name": {"type": "IfcIdentifier", "value": "Manufacturer"}, "nominalValue": {"type": "IfcLabel", "value": "Acme"}}]}], "relatingTemplate": "3aeaeb12-e90f-40be-90a2-4a0e84527b6d"}]}]}], "hasAssociations": [{"type": "IfcRelAssociatesLibrary", "globalId": "8465b5c3-8b68-4a7d-90e9-3aa944f738d6", "ownerHistory": "c2d8c84f-23f5-44f8-b508-ba2c5967cffa", "relatingLibrary": {"type": "IfcLibraryReference", "identification": {"type": "IfcIdentifier", "value": "Pset_DistributionPortCommon"}, "name": {"type": "IfcLabel", "value": "Pset_DistributionPortCommon"}, "referencedLibrary": {"type": "IfcLibraryInformation", "name": {"type": "IfcLabel", "value": "IFC4"}, "publisher": "0223c0a2-caf6-4cf0-bfd3-dec8870e3e4a", "versionDate": {"type": "IfcDateTime", "value": "2011-09-26T20:52:24"}, "location": {"type": "IfcURIReference", "value": "http://buildingsmart-tech.org/ifc/IFC2x4/rc3/html/annex/annex-a/ifc2x4_rc3-templates.ifc"}}}}], "templateType": "PSET_OCCURRENCEDRIVEN", "applicableEntity": {"type": "IfcIdentifier", "value": "IfcDistributionPort"}, "hasPropertyTemplates": [{"type": "IfcSimplePropertyTemplate", "globalId": "fe955b5b-a740-43dd-9399-6694bf1b36e1", "ownerHistory": "c2d8c84f-23f5-44f8-b508-ba2c5967cffa", "name": {"type": "IfcLabel", "value": "PortNumber"}, "description": {"type": "IfcText", "value": "The port index for logically ordering the port within the containing element or element type."}, "partOfPsetTemplate": ["8980b34b-4895-43de-a38a-a852bc09493b"], "templateType": "P_SINGLEVALUE", "primaryMeasureType": {"type": "IfcLabel", "value": "IfcInteger"}, "secondaryMeasureType": {"type": "IfcLabel"}, "accessState": "READWRITE"}, {"type": "IfcSimplePropertyTemplate", "globalId": "9f36a47d-b831-4adc-8072-5851a40fbc71", "ownerHistory": "c2d8c84f-23f5-44f8-b508-ba2c5967cffa", "name": {"type": "IfcLabel", "value": "ColorCode"}, "description": {"type": "IfcText", "value": "Name of a color for identifying the connector, if applicable."}, "partOfPsetTemplate": ["8980b34b-4895-43de-a38a-a852bc09493b"], "templateType": "P_SINGLEVALUE", "primaryMeasureType": {"type": "IfcLabel", "value": "IfcLabel"}, "secondaryMeasureType": {"type": "IfcLabel"}, "accessState": "READWRITE"}], "defines": ["8d286279-a6d0-422d-9475-e65613bec10c"]}}], "hasProperties": [{"type": "IfcPropertySingleValue", "name": {"type": "IfcIdentifier", "value": "PortNumber"}, "description": {"type": "IfcText", "value": "The port index for logically ordering the port within the containing element or element type."}}, {"type": "IfcPropertySingleValue", "name": {"type": "IfcIdentifier", "value": "ColorCode"}, "description": {"type": "IfcText", "value": "Name of a color for identifying the connector, if applicable."}}]}}, {"type": "IfcRelDefinesByProperties", "globalId": "99afb10c-9f4f-4385-b966-72c9f96b6cd3", "ownerHistory": "c2d8c84f-23f5-44f8-b508-ba2c5967cffa", "name": {"type": "IfcLabel", "value": "Pset_DistributionPortTypeAirConditioning"}, "relatingPropertyDefinition": "440957e2-c596-460a-9b2a-307ddebe3c5d"}], "objectPlacement": {"type": "IfcLocalPlacement", "globalId": "c50829bd-01dc-491c-8bee-a994a374d8ec", "relativePlacement": {"type": "IfcAxis2Placement3D", "location": {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": 12.0}, {"type": "IfcLengthMeasure", "value": 12.0}, {"type": "IfcLengthMeasure", "value": 4.0}]}}}, "flowDirection": "SINK", "predefinedType": "DUCT", "systemType": "AIRCONDITIONING"}]}], "hasContext": ["3f97ef3c-c486-4e94-a7e5-a1d6380afb65"], "hasPropertySets": ["95580094-1679-4fb0-b7dd-ce97aa631bc4", "c46cb661-483e-412c-963c-797c8689b399"], "representationMaps": [{"type": "IfcRepresentationMap", "mappingOrigin": {"type": "IfcAxis2Placement3D", "location": {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": 0.0}, {"type": "IfcLengthMeasure", "value": 0.0}, {"type": "IfcLengthMeasure", "value": 0.0}]}}, "mappedRepresentation": {"type": "IfcShapeRepresentation", "contextOfItems": "382037ae-bb99-480c-81f6-7c58ce28a2b8", "representationIdentifier": {"type": "IfcLabel", "value": "Body"}, "representationType": {"type": "IfcLabel", "value": "AdvancedSweptSolid"}, "items": [{"type": "IfcExtrudedAreaSolidTapered", "sweptArea": {"type": "IfcRectangleHollowProfileDef", "profileType": "AREA", "xDim": {"type": "IfcPositiveLengthMeasure", "value": 24.0}, "yDim": {"type": "IfcPositiveLengthMeasure", "value": 24.0}, "wallThickness": {"type": "IfcPositiveLengthMeasure", "value": 2.0}, "innerFilletRadius": {"type": "IfcNonNegativeLengthMeasure", "value": 10.0}, "outerFilletRadius": {"type": "IfcNonNegativeLengthMeasure", "value": 0.0}}, "position": {"type": "IfcAxis2Placement3D", "location": {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": 12.0}, {"type": "IfcLengthMeasure", "value": 12.0}, {"type": "IfcLengthMeasure", "value": 0.0}]}}, "extrudedDirection": {"type": "IfcDirection", "directionRatios": [{"type": "IfcReal", "value": 0.0}, {"type": "IfcReal", "value": 0.0}, {"type": "IfcReal", "value": 1.0}]}, "depth": {"type": "IfcPositiveLengthMeasure", "value": 4.0}, "endSweptArea": {"type": "IfcDerivedProfileDef", "profileType": "AREA", "parentProfile": {"type": "IfcRectangleHollowProfileDef", "profileType": "AREA", "xDim": {"type": "IfcPositiveLengthMeasure", "value": 24.0}, "yDim": {"type": "IfcPositiveLengthMeasure", "value": 24.0}, "wallThickness": {"type": "IfcPositiveLengthMeasure", "value": 2.0}, "innerFilletRadius": {"type": "IfcNonNegativeLengthMeasure", "value": 10.0}, "outerFilletRadius": {"type": "IfcNonNegativeLengthMeasure", "value": 0.0}}, "operator": {"type": "IfcCartesianTransformationOperator2D", "localOrigin": {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": 0.0}, {"type": "IfcLengthMeasure", "value": 0.0}]}, "scale": {"type": "IfcReal", "value": 0.5}}}}]}}], "predefinedType": "DIFFUSER"}, {"type": "IfcProjectLibrary", "globalId": "75a72ffd-1d99-4c92-8569-15c6104137c5", "ownerHistory": "c2d8c84f-23f5-44f8-b508-ba2c5967cffa", "name": {"type": "IfcLabel", "value": "IFC4"}, "hasContext": ["3f97ef3c-c486-4e94-a7e5-a1d6380afb65"], "hasAssociations": [{"type": "IfcRelAssociatesLibrary", "globalId": "8ae6b68d-57c1-4520-a3df-a0a5d987280b", "ownerHistory": "c2d8c84f-23f5-44f8-b508-ba2c5967cffa", "relatingLibrary": {"type": "IfcLibraryInformation", "name": {"type": "IfcLabel", "value": "IFC4"}, "publisher": "0223c0a2-caf6-4cf0-bfd3-dec8870e3e4a", "versionDate": {"type": "IfcDateTime", "value": "2011-09-26T20:52:24"}, "location": {"type": "IfcURIReference", "value": "http://buildingsmart-tech.org/ifc/IFC2x4/rc3/html/annex/annex-a/ifc2x4_rc3-templates.ifc"}}}], "declares": ["21bff64c-c636-4b08-b796-4e6b7f3fe6d7"]}]}]}
ISO-10303-21;
HEADER;
FILE_DESCRIPTION(('ViewDefinition [notYetAssigned]'),'2;1');
FILE_NAME(
/* name */ '',
/* time_stamp */ '2014-12-09T00:27:55',
/* author */ ('Jon'),
/* organization */ ('Unknown'),
/* preprocessor_version */ 'ggIFC - Exporter by Geometry Gym Pty Ltd',
/* originating_system */ 'ggIFC - Exporter by Geometry Gym Pty Ltd',
/* authorization */ 'None');

FILE_SCHEMA (('IFC4'));
ENDSEC;

DATA;
#1= IFCAPPLICATION(#2,'*******','ggRhinoIFC - Geometry Gym Plug-in for Rhino3d','ggRhinoIFC');
#2= IFCORGANIZATION($,'Geometry Gym Pty Ltd',$,$,$);
#3= IFCPERSONANDORGANIZATION(#4,#5,$);
#4= IFCPERSON('Jon','Jon',$,$,$,$,$,$);
#5= IFCORGANIZATION($,'Geometry Gym Pty Ltd',$,$,$);
#6= IFCOWNERHISTORY(#3,#1,$,.ADDED.,1418084874,$,$,1418084874);
#7= IFCGEOMETRICREPRESENTATIONCONTEXT($,'Model',3,0.0001,#8,#10);
#8= IFCAXIS2PLACEMENT3D(#9,$,$);
#9= IFCCARTESIANPOINT((0.0,0.0,0.0));
#10= IFCDIRECTION((0.0,1.0));
#11= IFCGEOMETRICREPRESENTATIONSUBCONTEXT('Axis','Model',*,*,*,*,#7,$,.MODEL_VIEW.,$);
#12= IFCGEOMETRICREPRESENTATIONSUBCONTEXT('Body','Model',*,*,*,*,#7,$,.MODEL_VIEW.,$);
#13= IFCGEOMETRICREPRESENTATIONCONTEXT($,'Model',3,0.0001,#8,#14);
#14= IFCDIRECTION((0.0,1.0));
#50= IFCBUILDING('2DaUviXGv3IgGDZ0SQr$Ko',$,'IfcBuilding',$,$,#51,$,$,.ELEMENT.,$,$,#57);
#51= IFCLOCALPLACEMENT($,#52);
#52= IFCAXIS2PLACEMENT3D(#53,$,$);
#53= IFCCARTESIANPOINT((0.0,0.0,0.0));
#54= IFCRELCONTAINEDINSPATIALSTRUCTURE('2hAI9$QOD2e8687AnmExGA',$,'Building','Building Container for Elements',(#328),#50);
#55= IFCLOCALPLACEMENT(#51,#52);
#57= IFCPOSTALADDRESS($,$,$,$,$,$,$,'Unknown',$,$);
#100= IFCPROJECT('2Fhz9x2116KfAdF6b5PcUu',#6,'IfcProject',$,$,'IfcProject','',(#13),#101);
#101= IFCUNITASSIGNMENT((#102,#103,#104));
#102= IFCSIUNIT(*,.LENGTHUNIT.,.MILLI.,.METRE.);
#103= IFCSIUNIT(*,.PLANEANGLEUNIT.,$,.RADIAN.);
#104= IFCSIUNIT(*,.TIMEUNIT.,$,.SECOND.);
#105= IFCRELAGGREGATES('2$lmeJojXCcRL8JGmCaFu_',$,'Project Container','Project Container for Buildings',#100,(#50));
#200= IFCCARTESIANPOINT((0.0,253.099263998677,0.0));
#201= IFCCARTESIANPOINT((0.0,247.792422124388,-83.9999999999991));
#202= IFCCARTESIANPOINT((0.0,268.843232748677,0.0));
#203= IFCCARTESIANPOINT((0.0,247.792422124388,-93.9999999999991));
#204= IFCVERTEXPOINT(#200);
#205= IFCVERTEXPOINT(#201);
#206= IFCVERTEXPOINT(#202);
#207= IFCVERTEXPOINT(#203);
#208= IFCPOLYLINE((#200,#201));
#209= IFCEDGECURVE(#204,#205,#208,.T.);
#210= IFCBSPLINECURVEWITHKNOTS(3,(#211,#212,#213,#214,#211,#212,#213),.UNSPECIFIED.,.T.,.T.,(1,1,1,1,1,1,1,1,1,1,1),(-7.0,-6.0,-5.0,-4.0,-3.0,-2.0,-1.0,0.0,1.0,2.0,3.0),.UNSPECIFIED.);
#211= IFCCARTESIANPOINT((239.758213537139,192.193559404919,-83.9999999999991));
#212= IFCCARTESIANPOINT((0.0,275.591853484122,-83.9999999999991));
#213= IFCCARTESIANPOINT((-239.75821353295,192.193559404918,-83.9999999999991));
#214= IFCCARTESIANPOINT((0.0,-108.13323051355,-83.9999999999991));
#215= IFCEDGECURVE(#205,#205,#210,.T.);
#216= IFCCARTESIANPOINT((-437.751000004175,168.150654933496));
#217= IFCCARTESIANPOINT((0.0,295.573568531267));
#218= IFCCARTESIANPOINT((437.751000006541,168.150654933498));
#219= IFCCARTESIANPOINT((0.0,-290.713822148428));
#220= IFCCARTESIANPOINT((-437.751000004175,168.150654933496));
#221= IFCCARTESIANPOINT((0.0,295.573568531267));
#222= IFCCARTESIANPOINT((437.751000006541,168.150654933498));
#223= IFCBSPLINECURVEWITHKNOTS(3,(#216,#217,#218,#219,#220,#221,#222),.UNSPECIFIED.,.T.,.T.,(1,1,1,1,1,1,1,1,1,1,1),(-7.0,-6.0,-5.0,-4.0,-3.0,-2.0,-1.0,0.0,1.0,2.0,3.0),.UNSPECIFIED.);
#224= IFCEDGECURVE(#204,#204,#223,.T.);
#225= IFCPOLYLINE((#202,#203));
#226= IFCEDGECURVE(#206,#207,#225,.T.);
#227= IFCBSPLINECURVEWITHKNOTS(3,(#228,#229,#230,#231,#228,#229,#230),.UNSPECIFIED.,.T.,.T.,(1,1,1,1,1,1,1,1,1,1,1),(-7.0,-6.0,-5.0,-4.0,-3.0,-2.0,-1.0,0.0,1.0,2.0,3.0),.UNSPECIFIED.);
#228= IFCCARTESIANPOINT((-239.758213535044,192.193559378247,-93.9999999999991));
#229= IFCCARTESIANPOINT((0.0,275.591853497458,-93.9999999999991));
#230= IFCCARTESIANPOINT((239.758213535045,192.193559378248,-93.9999999999991));
#231= IFCCARTESIANPOINT((0.0,-108.133230500215,-93.9999999999991));
#232= IFCEDGECURVE(#207,#207,#227,.T.);
#233= IFCCARTESIANPOINT((457.685108750143,177.051077752302));
#234= IFCCARTESIANPOINT((0.0,314.739310246865));
#235= IFCCARTESIANPOINT((-457.685108750141,177.051077752299));
#236= IFCCARTESIANPOINT((0.0,-318.77998625438));
#237= IFCCARTESIANPOINT((457.685108750143,177.051077752302));
#238= IFCCARTESIANPOINT((0.0,314.739310246865));
#239= IFCCARTESIANPOINT((-457.685108750141,177.051077752299));
#240= IFCBSPLINECURVEWITHKNOTS(3,(#233,#234,#235,#236,#237,#238,#239),.UNSPECIFIED.,.T.,.T.,(1,1,1,1,1,1,1,1,1,1,1),(-7.0,-6.0,-5.0,-4.0,-3.0,-2.0,-1.0,0.0,1.0,2.0,3.0),.UNSPECIFIED.);
#241= IFCEDGECURVE(#206,#206,#240,.T.);
#242= IFCORIENTEDEDGE(*,*,#209,.T.);
#243= IFCORIENTEDEDGE(*,*,#215,.T.);
#244= IFCORIENTEDEDGE(*,*,#209,.F.);
#245= IFCORIENTEDEDGE(*,*,#224,.T.);
#246= IFCEDGELOOP((#242,#243,#244,#245));
#247= IFCFACEOUTERBOUND(#246,.T.);
#248= IFCBSPLINESURFACEWITHKNOTS(3,3,((#249,#250,#251,#252,#249,#250,#251),(#253,#254,#255,#256,#253,#254,#255),(#257,#258,#259,#260,#257,#258,#259),(#261,#262,#263,#264,#261,#262,#263)),.UNSPECIFIED.,.F.,.T.,.F.,(4,4),(1,1,1,1,1,1,1,1,1,1,1),(0.0,14.7110308353668),(-7.0,-6.0,-5.0,-4.0,-3.0,-2.0,-1.0,0.0,1.0,2.0,3.0),.UNSPECIFIED.);
#249= IFCCARTESIANPOINT((437.751000006541,168.150654933498,0.0));
#250= IFCCARTESIANPOINT((0.0,295.573568531267,0.0));
#251= IFCCARTESIANPOINT((-437.751000004175,168.150654933496,0.0));
#252= IFCCARTESIANPOINT((0.0,-290.713822148428,0.0));
#253= IFCCARTESIANPOINT((371.75340451674,176.164956423972,-27.9999999999997));
#254= IFCCARTESIANPOINT((0.0,288.912996848885,-27.9999999999997));
#255= IFCCARTESIANPOINT((-371.753404513767,176.16495642397,-27.9999999999997));
#256= IFCCARTESIANPOINT((0.0,-229.853624936802,-27.9999999999997));
#257= IFCCARTESIANPOINT((305.75580902694,184.179257914445,-55.9999999999994));
#258= IFCCARTESIANPOINT((0.0,282.252425166504,-55.9999999999994));
#259= IFCCARTESIANPOINT((-305.755809023358,184.179257914444,-55.9999999999994));
#260= IFCCARTESIANPOINT((0.0,-168.993427725176,-55.9999999999994));
#261= IFCCARTESIANPOINT((239.758213537139,192.193559404919,-83.9999999999991));
#262= IFCCARTESIANPOINT((0.0,275.591853484122,-83.9999999999991));
#263= IFCCARTESIANPOINT((-239.75821353295,192.193559404918,-83.9999999999991));
#264= IFCCARTESIANPOINT((0.0,-108.13323051355,-83.9999999999991));
#265= IFCADVANCEDFACE((#247),#248,.F.);
#266= IFCORIENTEDEDGE(*,*,#226,.T.);
#267= IFCORIENTEDEDGE(*,*,#232,.T.);
#268= IFCORIENTEDEDGE(*,*,#226,.F.);
#269= IFCORIENTEDEDGE(*,*,#241,.T.);
#270= IFCEDGELOOP((#266,#267,#268,#269));
#271= IFCFACEOUTERBOUND(#270,.T.);
#272= IFCBSPLINESURFACEWITHKNOTS(3,3,((#273,#274,#275,#276,#273,#274,#275),(#277,#278,#279,#280,#277,#278,#279),(#281,#282,#283,#284,#281,#282,#283),(#285,#286,#287,#288,#285,#286,#287)),.UNSPECIFIED.,.F.,.T.,.F.,(4,4),(1,1,1,1,1,1,1,1,1,1,1),(0.0,15.4213505620632),(-3.0,-2.0,-1.0,0.0,1.0,2.0,3.0,4.0,5.0,6.0,7.0),.UNSPECIFIED.);
#273= IFCCARTESIANPOINT((-457.685108750141,177.051077752299,0.0));
#274= IFCCARTESIANPOINT((0.0,314.739310246865,0.0));
#275= IFCCARTESIANPOINT((457.685108750143,177.051077752302,0.0));
#276= IFCCARTESIANPOINT((0.0,-318.77998625438,0.0));
#277= IFCCARTESIANPOINT((-385.042810345109,182.098571627615,-31.333333333333));
#278= IFCCARTESIANPOINT((0.0,301.690157997063,-31.333333333333));
#279= IFCCARTESIANPOINT((385.04281034511,182.098571627617,-31.333333333333));
#280= IFCCARTESIANPOINT((0.0,-248.564401002992,-31.333333333333));
#281= IFCCARTESIANPOINT((-312.400511940076,187.146065502931,-62.666666666666));
#282= IFCCARTESIANPOINT((0.0,288.64100574726,-62.666666666666));
#283= IFCCARTESIANPOINT((312.400511940078,187.146065502933,-62.666666666666));
#284= IFCCARTESIANPOINT((0.0,-178.348815751603,-62.6666666666661));
#285= IFCCARTESIANPOINT((-239.758213535044,192.193559378247,-93.9999999999991));
#286= IFCCARTESIANPOINT((0.0,275.591853497458,-93.9999999999991));
#287= IFCCARTESIANPOINT((239.758213535045,192.193559378248,-93.9999999999991));
#288= IFCCARTESIANPOINT((0.0,-108.133230500215,-93.9999999999991));
#289= IFCADVANCEDFACE((#271),#272,.F.);
#290= IFCORIENTEDEDGE(*,*,#215,.F.);
#291= IFCEDGELOOP((#290));
#292= IFCFACEOUTERBOUND(#291,.T.);
#293= IFCAXIS2PLACEMENT3D(#201,$,$);
#294= IFCPLANE(#293);
#295= IFCADVANCEDFACE((#292),#294,.T.);
#296= IFCORIENTEDEDGE(*,*,#232,.T.);
#297= IFCEDGELOOP((#296));
#298= IFCFACEOUTERBOUND(#297,.T.);
#299= IFCAXIS2PLACEMENT3D(#203,$,$);
#300= IFCPLANE(#299);
#301= IFCADVANCEDFACE((#298),#300,.F.);
#302= IFCORIENTEDEDGE(*,*,#241,.F.);
#303= IFCEDGELOOP((#302));
#304= IFCFACEOUTERBOUND(#303,.T.);
#305= IFCORIENTEDEDGE(*,*,#224,.F.);
#306= IFCEDGELOOP((#305));
#307= IFCFACEBOUND(#306,.T.);
#308= IFCAXIS2PLACEMENT3D(#200,$,$);
#309= IFCPLANE(#308);
#310= IFCADVANCEDFACE((#304,#307),#309,.T.);
#311= IFCCLOSEDSHELL((#265,#289,#295,#301,#310));
#312= IFCADVANCEDBREP(#311);
#313= IFCREPRESENTATIONMAP(#314,#316);
#314= IFCAXIS2PLACEMENT3D(#315,$,$);
#315= IFCCARTESIANPOINT((0.0,0.0,0.0));
#316= IFCSHAPEREPRESENTATION(#12,'Body','AdvancedBrep',(#312));
#317= IFCMATERIAL('Ceramic',$,$);
#318= IFCRELASSOCIATESMATERIAL('365q5WGdjCUhLdyD1hcKEq',$,'MatAssoc','Material Associates',(#320),#317);
#320= IFCSANITARYTERMINALTYPE('0O3LhGDpjD0Ah1_8D8_sBO',$,'IFCSANITARYTERMINALTYPE',$,$,$,(#313),$,$,.WASHHANDBASIN.);
#321= IFCRELDEFINESBYTYPE('2fdlJW759Ahxs7mR1ddo3x',$,$,$,(#328),#320);
#322= IFCDIRECTION((1.0,0.0,0.0));
#323= IFCDIRECTION((0.0,1.0,0.0));
#324= IFCCARTESIANPOINT((0.0,0.0,0.0));
#325= IFCCARTESIANTRANSFORMATIONOPERATOR3D(#322,#323,#324,1.0,#326);
#326= IFCDIRECTION((0.0,0.0,1.0));
#327= IFCMAPPEDITEM(#313,#325);
#328= IFCSANITARYTERMINAL('1li7KIQzH9cPSAQD2J80Oc',$,$,$,$,#55,#329,$,.NOTDEFINED.);
#329= IFCPRODUCTDEFINITIONSHAPE($,$,(#330));
#330= IFCSHAPEREPRESENTATION(#12,'Body','MappedRepresentation',(#327));
ENDSEC;

END-ISO-10303-21;


<IfcProject type="IfcProject" globalId="8fafd27b-0810-4652-92a7-3c69456667b8">
  <ownerHistory type="IfcOwnerHistory" globalId="d2f6c0b9-e6fa-4398-a55b-10f5accc5083" changeAction="ADDED">
    <owningUser type="IfcPersonAndOrganization" globalId="366536c4-3e19-41ea-965c-8ae862e22154">
      <thePerson type="IfcPerson" globalId="253b5aac-5dc3-43cd-98da-fc62c899a992">
        <identification type="IfcIdentifier" value="Jon"/>
        <familyName type="IfcLabel" value="Jon"/>
      </thePerson>
      <theOrganization type="IfcOrganization" globalId="0070ad3d-0f8f-4c32-a10c-6b81390d3ecb">
        <name type="IfcLabel" value="Geometry Gym Pty Ltd"/>
      </theOrganization>
    </owningUser>
    <owningApplication>
      <applicationDeveloper type="IfcOrganization" globalId="ee2772cc-7448-4b31-b350-5a587e42fa07">
        <name type="IfcLabel" value="Geometry Gym Pty Ltd"/>
      </applicationDeveloper>
      <version type="IfcLabel" value="*******"/>
      <applicationFullName type="IfcLabel" value="ggRhinoIFC - Geometry Gym Plug-in for Rhino3d"/>
      <applicationIdentifier type="IfcIdentifier" value="ggRhinoIFC"/>
    </owningApplication>
    <lastModifiedDate type="IfcTimeStamp" value="1418084874"/>
    <creationDate type="IfcTimeStamp" value="1418084874"/>
  </ownerHistory>
  <name type="IfcLabel" value="IfcProject"/>
  <isDecomposedBy type="IfcRelAggregates" globalId="bfbf0a13-cad8-4c99-b548-4d0c0c90fe3e">
    <description type="IfcText" value="Project Container for Buildings"/>
    <name type="IfcLabel" value="Project Container"/>
    <relatedObjects>
      <IfcObjectDefinition type="IfcBuilding" globalId="8d91ee6c-850e-434a-a40d-8c071ad7f532" compositionType="ELEMENT">
        <name type="IfcLabel" value="IfcBuilding"/>
        <objectPlacement type="IfcLocalPlacement" globalId="60214d98-36c3-46e3-bcce-ac67693d6555">
          <relativePlacement type="IfcAxis2Placement3D">
            <location type="IfcCartesianPoint">
              <coordinates>
                <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
              </coordinates>
            </location>
          </relativePlacement>
        </objectPlacement>
        <containsElements>
          <IfcRelContainedInSpatialStructure type="IfcRelContainedInSpatialStructure" globalId="ab29227f-6983-42a0-8188-1cac703bb40a">
            <description type="IfcText" value="Building Container for Elements"/>
            <name type="IfcLabel" value="Building"/>
            <relatedElements>
              <IfcProduct type="IfcSanitaryTerminal" globalId="6fb07512-6bd4-4999-970a-68d093200626" predefinedType="NOTDEFINED">
                <isTypedBy>
                  <IfcRelDefinesByType type="IfcRelDefinesByType" globalId="a99ef4e0-1c52-4aaf-bd87-c1b0679f20fb">
                    <relatingType type="IfcSanitaryTerminalType" globalId="180d5ad0-373b-4d00-aac1-f88348fb62d8" predefinedType="WASHHANDBASIN">
                      <name type="IfcLabel" value="IFCSANITARYTERMINALTYPE"/>
                      <hasAssociations>
                        <IfcRelAssociates type="IfcRelAssociatesMaterial" globalId="c6174160-427b-4c7a-b567-f0d06b9943b4">
                          <description type="IfcText" value="Material Associates"/>
                          <relatingMaterial type="IfcMaterial" globalId="f9395efc-1594-482c-9148-a47905749bf4">
                            <name type="IfcLabel" value="Ceramic"/>
                          </relatingMaterial>
                          <name type="IfcLabel" value="MatAssoc"/>
                        </IfcRelAssociates>
                      </hasAssociations>
                      <representationMaps>
                        <IfcRepresentationMap type="IfcRepresentationMap">
                          <mappingOrigin type="IfcAxis2Placement3D">
                            <location type="IfcCartesianPoint">
                              <coordinates>
                                <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                              </coordinates>
                            </location>
                          </mappingOrigin>
                          <mappedRepresentation type="IfcShapeRepresentation">
                            <contextOfItems type="IfcGeometricRepresentationSubContext" globalId="a7d469e7-37b7-4a63-b07e-c863b65a1b9e" targetView="MODEL_VIEW">
                              <contextIdentifier type="IfcLabel" value="Body"/>
                              <contextType type="IfcLabel" value="Model"/>
                            </contextOfItems>
                            <representationIdentifier type="IfcLabel" value="Body"/>
                            <representationType type="IfcLabel" value="AdvancedBrep"/>
                            <items>
                              <IfcRepresentationItem type="IfcAdvancedBrep">
                                <outer type="IfcClosedShell">
                                  <cfsFaces>
                                    <IfcFace type="IfcAdvancedFace">
                                      <bounds>
                                        <IfcFaceBound type="IfcFaceOuterBound">
                                          <bound type="IfcEdgeLoop">
                                            <edgeList>
                                              <IfcOrientedEdge type="IfcOrientedEdge">
                                                <edgeElement type="IfcEdgeCurve">
                                                  <edgeStart type="IfcVertexPoint">
                                                    <vertexGeometry type="IfcCartesianPoint">
                                                      <coordinates>
                                                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                        <IfcLengthMeasure type="IfcLengthMeasure" value="247.792422124388"/>
                                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-93.9999999999991"/>
                                                      </coordinates>
                                                    </vertexGeometry>
                                                  </edgeStart>
                                                  <edgeEnd type="IfcVertexPoint">
                                                    <vertexGeometry type="IfcCartesianPoint">
                                                      <coordinates>
                                                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                        <IfcLengthMeasure type="IfcLengthMeasure" value="247.792422124388"/>
                                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-93.9999999999991"/>
                                                      </coordinates>
                                                    </vertexGeometry>
                                                  </edgeEnd>
                                                  <edgeGeometry type="IfcBSplineCurveWithKnots" curveForm="UNSPECIFIED">
                                                    <closedCurve type="IfcLogical" value="false"/>
                                                    <selfIntersect type="IfcLogical" value="false"/>
                                                    <knotSpec>UNSPECIFIED</knotSpec>
                                                    <upperIndexOnControlPoints>0</upperIndexOnControlPoints>
                                                    <degree type="IfcInteger" value="3"/>
                                                    <controlPointsList>
                                                      <IfcCartesianPoint type="IfcCartesianPoint">
                                                        <coordinates>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="-239.758213535044"/>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="192.193559378247"/>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="-93.9999999999991"/>
                                                        </coordinates>
                                                      </IfcCartesianPoint>
                                                      <IfcCartesianPoint type="IfcCartesianPoint">
                                                        <coordinates>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="275.591853497458"/>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="-93.9999999999991"/>
                                                        </coordinates>
                                                      </IfcCartesianPoint>
                                                      <IfcCartesianPoint type="IfcCartesianPoint">
                                                        <coordinates>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="239.758213535045"/>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="192.193559378248"/>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="-93.9999999999991"/>
                                                        </coordinates>
                                                      </IfcCartesianPoint>
                                                      <IfcCartesianPoint type="IfcCartesianPoint">
                                                        <coordinates>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="-108.133230500215"/>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="-93.9999999999991"/>
                                                        </coordinates>
                                                      </IfcCartesianPoint>
                                                      <IfcCartesianPoint type="IfcCartesianPoint">
                                                        <coordinates>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="-239.758213535044"/>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="192.193559378247"/>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="-93.9999999999991"/>
                                                        </coordinates>
                                                      </IfcCartesianPoint>
                                                      <IfcCartesianPoint type="IfcCartesianPoint">
                                                        <coordinates>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="275.591853497458"/>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="-93.9999999999991"/>
                                                        </coordinates>
                                                      </IfcCartesianPoint>
                                                      <IfcCartesianPoint type="IfcCartesianPoint">
                                                        <coordinates>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="239.758213535045"/>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="192.193559378248"/>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="-93.9999999999991"/>
                                                        </coordinates>
                                                      </IfcCartesianPoint>
                                                    </controlPointsList>
                                                    <knotMultiplicities>
                                                      <IfcInteger type="IfcInteger" value="1"/>
                                                      <IfcInteger type="IfcInteger" value="1"/>
                                                      <IfcInteger type="IfcInteger" value="1"/>
                                                      <IfcInteger type="IfcInteger" value="1"/>
                                                      <IfcInteger type="IfcInteger" value="1"/>
                                                      <IfcInteger type="IfcInteger" value="1"/>
                                                      <IfcInteger type="IfcInteger" value="1"/>
                                                      <IfcInteger type="IfcInteger" value="1"/>
                                                      <IfcInteger type="IfcInteger" value="1"/>
                                                      <IfcInteger type="IfcInteger" value="1"/>
                                                      <IfcInteger type="IfcInteger" value="1"/>
                                                    </knotMultiplicities>
                                                    <knots>
                                                      <IfcParameterValue type="IfcParameterValue" value="-7.0"/>
                                                      <IfcParameterValue type="IfcParameterValue" value="-6.0"/>
                                                      <IfcParameterValue type="IfcParameterValue" value="-5.0"/>
                                                      <IfcParameterValue type="IfcParameterValue" value="-4.0"/>
                                                      <IfcParameterValue type="IfcParameterValue" value="-3.0"/>
                                                      <IfcParameterValue type="IfcParameterValue" value="-2.0"/>
                                                      <IfcParameterValue type="IfcParameterValue" value="-1.0"/>
                                                      <IfcParameterValue type="IfcParameterValue" value="0.0"/>
                                                      <IfcParameterValue type="IfcParameterValue" value="1.0"/>
                                                      <IfcParameterValue type="IfcParameterValue" value="2.0"/>
                                                      <IfcParameterValue type="IfcParameterValue" value="3.0"/>
                                                    </knots>
                                                  </edgeGeometry>
                                                  <sameSense type="IfcBoolean" value="false"/>
                                                </edgeElement>
                                                <orientation type="IfcBoolean" value="false"/>
                                              </IfcOrientedEdge>
                                            </edgeList>
                                          </bound>
                                          <orientation type="IfcBoolean" value="false"/>
                                        </IfcFaceBound>
                                      </bounds>
                                      <faceSurface type="IfcPlane">
                                        <position type="IfcAxis2Placement3D">
                                          <location type="IfcCartesianPoint">
                                            <coordinates>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="247.792422124388"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="-93.9999999999991"/>
                                            </coordinates>
                                          </location>
                                        </position>
                                      </faceSurface>
                                      <sameSense type="IfcBoolean" value="false"/>
                                    </IfcFace>
                                    <IfcFace type="IfcAdvancedFace">
                                      <bounds>
                                        <IfcFaceBound type="IfcFaceOuterBound">
                                          <bound type="IfcEdgeLoop">
                                            <edgeList>
                                              <IfcOrientedEdge type="IfcOrientedEdge">
                                                <edgeElement type="IfcEdgeCurve">
                                                  <edgeStart type="IfcVertexPoint">
                                                    <vertexGeometry type="IfcCartesianPoint">
                                                      <coordinates>
                                                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                        <IfcLengthMeasure type="IfcLengthMeasure" value="253.099263998677"/>
                                                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                      </coordinates>
                                                    </vertexGeometry>
                                                  </edgeStart>
                                                  <edgeEnd type="IfcVertexPoint">
                                                    <vertexGeometry type="IfcCartesianPoint">
                                                      <coordinates>
                                                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                        <IfcLengthMeasure type="IfcLengthMeasure" value="247.792422124388"/>
                                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-83.9999999999991"/>
                                                      </coordinates>
                                                    </vertexGeometry>
                                                  </edgeEnd>
                                                  <edgeGeometry type="IfcPolyline">
                                                    <points>
                                                      <IfcCartesianPoint type="IfcCartesianPoint">
                                                        <coordinates>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="253.099263998677"/>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                        </coordinates>
                                                      </IfcCartesianPoint>
                                                      <IfcCartesianPoint type="IfcCartesianPoint">
                                                        <coordinates>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="247.792422124388"/>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="-83.9999999999991"/>
                                                        </coordinates>
                                                      </IfcCartesianPoint>
                                                    </points>
                                                  </edgeGeometry>
                                                  <sameSense type="IfcBoolean" value="false"/>
                                                </edgeElement>
                                                <orientation type="IfcBoolean" value="false"/>
                                              </IfcOrientedEdge>
                                              <IfcOrientedEdge type="IfcOrientedEdge">
                                                <edgeElement type="IfcEdgeCurve">
                                                  <edgeStart type="IfcVertexPoint">
                                                    <vertexGeometry type="IfcCartesianPoint">
                                                      <coordinates>
                                                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                        <IfcLengthMeasure type="IfcLengthMeasure" value="247.792422124388"/>
                                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-83.9999999999991"/>
                                                      </coordinates>
                                                    </vertexGeometry>
                                                  </edgeStart>
                                                  <edgeEnd type="IfcVertexPoint">
                                                    <vertexGeometry type="IfcCartesianPoint">
                                                      <coordinates>
                                                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                        <IfcLengthMeasure type="IfcLengthMeasure" value="247.792422124388"/>
                                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-83.9999999999991"/>
                                                      </coordinates>
                                                    </vertexGeometry>
                                                  </edgeEnd>
                                                  <edgeGeometry type="IfcBSplineCurveWithKnots" curveForm="UNSPECIFIED">
                                                    <closedCurve type="IfcLogical" value="false"/>
                                                    <selfIntersect type="IfcLogical" value="false"/>
                                                    <knotSpec>UNSPECIFIED</knotSpec>
                                                    <upperIndexOnControlPoints>0</upperIndexOnControlPoints>
                                                    <degree type="IfcInteger" value="3"/>
                                                    <controlPointsList>
                                                      <IfcCartesianPoint type="IfcCartesianPoint">
                                                        <coordinates>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="239.758213537139"/>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="192.193559404919"/>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="-83.9999999999991"/>
                                                        </coordinates>
                                                      </IfcCartesianPoint>
                                                      <IfcCartesianPoint type="IfcCartesianPoint">
                                                        <coordinates>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="275.591853484122"/>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="-83.9999999999991"/>
                                                        </coordinates>
                                                      </IfcCartesianPoint>
                                                      <IfcCartesianPoint type="IfcCartesianPoint">
                                                        <coordinates>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="-239.75821353295"/>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="192.193559404918"/>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="-83.9999999999991"/>
                                                        </coordinates>
                                                      </IfcCartesianPoint>
                                                      <IfcCartesianPoint type="IfcCartesianPoint">
                                                        <coordinates>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="-108.13323051355"/>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="-83.9999999999991"/>
                                                        </coordinates>
                                                      </IfcCartesianPoint>
                                                      <IfcCartesianPoint type="IfcCartesianPoint">
                                                        <coordinates>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="239.758213537139"/>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="192.193559404919"/>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="-83.9999999999991"/>
                                                        </coordinates>
                                                      </IfcCartesianPoint>
                                                      <IfcCartesianPoint type="IfcCartesianPoint">
                                                        <coordinates>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="275.591853484122"/>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="-83.9999999999991"/>
                                                        </coordinates>
                                                      </IfcCartesianPoint>
                                                      <IfcCartesianPoint type="IfcCartesianPoint">
                                                        <coordinates>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="-239.75821353295"/>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="192.193559404918"/>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="-83.9999999999991"/>
                                                        </coordinates>
                                                      </IfcCartesianPoint>
                                                    </controlPointsList>
                                                    <knotMultiplicities>
                                                      <IfcInteger type="IfcInteger" value="1"/>
                                                      <IfcInteger type="IfcInteger" value="1"/>
                                                      <IfcInteger type="IfcInteger" value="1"/>
                                                      <IfcInteger type="IfcInteger" value="1"/>
                                                      <IfcInteger type="IfcInteger" value="1"/>
                                                      <IfcInteger type="IfcInteger" value="1"/>
                                                      <IfcInteger type="IfcInteger" value="1"/>
                                                      <IfcInteger type="IfcInteger" value="1"/>
                                                      <IfcInteger type="IfcInteger" value="1"/>
                                                      <IfcInteger type="IfcInteger" value="1"/>
                                                      <IfcInteger type="IfcInteger" value="1"/>
                                                    </knotMultiplicities>
                                                    <knots>
                                                      <IfcParameterValue type="IfcParameterValue" value="-7.0"/>
                                                      <IfcParameterValue type="IfcParameterValue" value="-6.0"/>
                                                      <IfcParameterValue type="IfcParameterValue" value="-5.0"/>
                                                      <IfcParameterValue type="IfcParameterValue" value="-4.0"/>
                                                      <IfcParameterValue type="IfcParameterValue" value="-3.0"/>
                                                      <IfcParameterValue type="IfcParameterValue" value="-2.0"/>
                                                      <IfcParameterValue type="IfcParameterValue" value="-1.0"/>
                                                      <IfcParameterValue type="IfcParameterValue" value="0.0"/>
                                                      <IfcParameterValue type="IfcParameterValue" value="1.0"/>
                                                      <IfcParameterValue type="IfcParameterValue" value="2.0"/>
                                                      <IfcParameterValue type="IfcParameterValue" value="3.0"/>
                                                    </knots>
                                                  </edgeGeometry>
                                                  <sameSense type="IfcBoolean" value="false"/>
                                                </edgeElement>
                                                <orientation type="IfcBoolean" value="false"/>
                                              </IfcOrientedEdge>
                                              <IfcOrientedEdge type="IfcOrientedEdge">
                                                <edgeElement type="IfcEdgeCurve">
                                                  <edgeStart type="IfcVertexPoint">
                                                    <vertexGeometry type="IfcCartesianPoint">
                                                      <coordinates>
                                                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                        <IfcLengthMeasure type="IfcLengthMeasure" value="253.099263998677"/>
                                                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                      </coordinates>
                                                    </vertexGeometry>
                                                  </edgeStart>
                                                  <edgeEnd type="IfcVertexPoint">
                                                    <vertexGeometry type="IfcCartesianPoint">
                                                      <coordinates>
                                                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                        <IfcLengthMeasure type="IfcLengthMeasure" value="247.792422124388"/>
                                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-83.9999999999991"/>
                                                      </coordinates>
                                                    </vertexGeometry>
                                                  </edgeEnd>
                                                  <edgeGeometry type="IfcPolyline">
                                                    <points>
                                                      <IfcCartesianPoint type="IfcCartesianPoint">
                                                        <coordinates>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="253.099263998677"/>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                        </coordinates>
                                                      </IfcCartesianPoint>
                                                      <IfcCartesianPoint type="IfcCartesianPoint">
                                                        <coordinates>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="247.792422124388"/>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="-83.9999999999991"/>
                                                        </coordinates>
                                                      </IfcCartesianPoint>
                                                    </points>
                                                  </edgeGeometry>
                                                  <sameSense type="IfcBoolean" value="false"/>
                                                </edgeElement>
                                                <orientation type="IfcBoolean" value="false"/>
                                              </IfcOrientedEdge>
                                              <IfcOrientedEdge type="IfcOrientedEdge">
                                                <edgeElement type="IfcEdgeCurve">
                                                  <edgeStart type="IfcVertexPoint">
                                                    <vertexGeometry type="IfcCartesianPoint">
                                                      <coordinates>
                                                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                        <IfcLengthMeasure type="IfcLengthMeasure" value="253.099263998677"/>
                                                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                      </coordinates>
                                                    </vertexGeometry>
                                                  </edgeStart>
                                                  <edgeEnd type="IfcVertexPoint">
                                                    <vertexGeometry type="IfcCartesianPoint">
                                                      <coordinates>
                                                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                        <IfcLengthMeasure type="IfcLengthMeasure" value="253.099263998677"/>
                                                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                      </coordinates>
                                                    </vertexGeometry>
                                                  </edgeEnd>
                                                  <edgeGeometry type="IfcBSplineCurveWithKnots" curveForm="UNSPECIFIED">
                                                    <closedCurve type="IfcLogical" value="false"/>
                                                    <selfIntersect type="IfcLogical" value="false"/>
                                                    <knotSpec>UNSPECIFIED</knotSpec>
                                                    <upperIndexOnControlPoints>0</upperIndexOnControlPoints>
                                                    <degree type="IfcInteger" value="3"/>
                                                    <controlPointsList>
                                                      <IfcCartesianPoint type="IfcCartesianPoint">
                                                        <coordinates>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="-437.751000004175"/>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="168.150654933496"/>
                                                        </coordinates>
                                                      </IfcCartesianPoint>
                                                      <IfcCartesianPoint type="IfcCartesianPoint">
                                                        <coordinates>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="295.573568531267"/>
                                                        </coordinates>
                                                      </IfcCartesianPoint>
                                                      <IfcCartesianPoint type="IfcCartesianPoint">
                                                        <coordinates>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="437.751000006541"/>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="168.150654933498"/>
                                                        </coordinates>
                                                      </IfcCartesianPoint>
                                                      <IfcCartesianPoint type="IfcCartesianPoint">
                                                        <coordinates>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="-290.713822148428"/>
                                                        </coordinates>
                                                      </IfcCartesianPoint>
                                                      <IfcCartesianPoint type="IfcCartesianPoint">
                                                        <coordinates>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="-437.751000004175"/>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="168.150654933496"/>
                                                        </coordinates>
                                                      </IfcCartesianPoint>
                                                      <IfcCartesianPoint type="IfcCartesianPoint">
                                                        <coordinates>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="295.573568531267"/>
                                                        </coordinates>
                                                      </IfcCartesianPoint>
                                                      <IfcCartesianPoint type="IfcCartesianPoint">
                                                        <coordinates>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="437.751000006541"/>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="168.150654933498"/>
                                                        </coordinates>
                                                      </IfcCartesianPoint>
                                                    </controlPointsList>
                                                    <knotMultiplicities>
                                                      <IfcInteger type="IfcInteger" value="1"/>
                                                      <IfcInteger type="IfcInteger" value="1"/>
                                                      <IfcInteger type="IfcInteger" value="1"/>
                                                      <IfcInteger type="IfcInteger" value="1"/>
                                                      <IfcInteger type="IfcInteger" value="1"/>
                                                      <IfcInteger type="IfcInteger" value="1"/>
                                                      <IfcInteger type="IfcInteger" value="1"/>
                                                      <IfcInteger type="IfcInteger" value="1"/>
                                                      <IfcInteger type="IfcInteger" value="1"/>
                                                      <IfcInteger type="IfcInteger" value="1"/>
                                                      <IfcInteger type="IfcInteger" value="1"/>
                                                    </knotMultiplicities>
                                                    <knots>
                                                      <IfcParameterValue type="IfcParameterValue" value="-7.0"/>
                                                      <IfcParameterValue type="IfcParameterValue" value="-6.0"/>
                                                      <IfcParameterValue type="IfcParameterValue" value="-5.0"/>
                                                      <IfcParameterValue type="IfcParameterValue" value="-4.0"/>
                                                      <IfcParameterValue type="IfcParameterValue" value="-3.0"/>
                                                      <IfcParameterValue type="IfcParameterValue" value="-2.0"/>
                                                      <IfcParameterValue type="IfcParameterValue" value="-1.0"/>
                                                      <IfcParameterValue type="IfcParameterValue" value="0.0"/>
                                                      <IfcParameterValue type="IfcParameterValue" value="1.0"/>
                                                      <IfcParameterValue type="IfcParameterValue" value="2.0"/>
                                                      <IfcParameterValue type="IfcParameterValue" value="3.0"/>
                                                    </knots>
                                                  </edgeGeometry>
                                                  <sameSense type="IfcBoolean" value="false"/>
                                                </edgeElement>
                                                <orientation type="IfcBoolean" value="false"/>
                                              </IfcOrientedEdge>
                                            </edgeList>
                                          </bound>
                                          <orientation type="IfcBoolean" value="false"/>
                                        </IfcFaceBound>
                                      </bounds>
                                      <faceSurface type="IfcBSplineSurfaceWithKnots" surfaceForm="UNSPECIFIED" knotSpec="UNSPECIFIED">
                                        <vknots>
                                          <vknots type="IfcParameterValue" value="-7.0"/>
                                          <vknots type="IfcParameterValue" value="-6.0"/>
                                          <vknots type="IfcParameterValue" value="-5.0"/>
                                          <vknots type="IfcParameterValue" value="-4.0"/>
                                          <vknots type="IfcParameterValue" value="-3.0"/>
                                          <vknots type="IfcParameterValue" value="-2.0"/>
                                          <vknots type="IfcParameterValue" value="-1.0"/>
                                          <vknots type="IfcParameterValue" value="0.0"/>
                                          <vknots type="IfcParameterValue" value="1.0"/>
                                          <vknots type="IfcParameterValue" value="2.0"/>
                                          <vknots type="IfcParameterValue" value="3.0"/>
                                        </vknots>
                                        <vmultiplicities>
                                          <vmultiplicities type="IfcInteger" value="1"/>
                                          <vmultiplicities type="IfcInteger" value="1"/>
                                          <vmultiplicities type="IfcInteger" value="1"/>
                                          <vmultiplicities type="IfcInteger" value="1"/>
                                          <vmultiplicities type="IfcInteger" value="1"/>
                                          <vmultiplicities type="IfcInteger" value="1"/>
                                          <vmultiplicities type="IfcInteger" value="1"/>
                                          <vmultiplicities type="IfcInteger" value="1"/>
                                          <vmultiplicities type="IfcInteger" value="1"/>
                                          <vmultiplicities type="IfcInteger" value="1"/>
                                          <vmultiplicities type="IfcInteger" value="1"/>
                                        </vmultiplicities>
                                        <uknots>
                                          <uknots type="IfcParameterValue" value="0.0"/>
                                          <uknots type="IfcParameterValue" value="14.7110308353668"/>
                                        </uknots>
                                        <umultiplicities>
                                          <umultiplicities type="IfcInteger" value="4"/>
                                          <umultiplicities type="IfcInteger" value="4"/>
                                        </umultiplicities>
                                        <vdegree type="IfcInteger" value="3"/>
                                        <udegree type="IfcInteger" value="3"/>
                                        <uclosed type="IfcLogical" value="false"/>
                                        <vclosed type="IfcLogical" value="false"/>
                                        <uDegree type="IfcInteger" value="3"/>
                                        <vDegree type="IfcInteger" value="3"/>
                                        <controlPointsList>
                                          <IfcCartesianPoint type="IfcCartesianPoint">
                                            <coordinates>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="437.751000006541"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="168.150654933498"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                            </coordinates>
                                          </IfcCartesianPoint>
                                          <IfcCartesianPoint type="IfcCartesianPoint">
                                            <coordinates>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="295.573568531267"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                            </coordinates>
                                          </IfcCartesianPoint>
                                          <IfcCartesianPoint type="IfcCartesianPoint">
                                            <coordinates>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="-437.751000004175"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="168.150654933496"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                            </coordinates>
                                          </IfcCartesianPoint>
                                          <IfcCartesianPoint type="IfcCartesianPoint">
                                            <coordinates>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="-290.713822148428"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                            </coordinates>
                                          </IfcCartesianPoint>
                                          <IfcCartesianPoint type="IfcCartesianPoint">
                                            <coordinates>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="437.751000006541"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="168.150654933498"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                            </coordinates>
                                          </IfcCartesianPoint>
                                          <IfcCartesianPoint type="IfcCartesianPoint">
                                            <coordinates>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="295.573568531267"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                            </coordinates>
                                          </IfcCartesianPoint>
                                          <IfcCartesianPoint type="IfcCartesianPoint">
                                            <coordinates>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="-437.751000004175"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="168.150654933496"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                            </coordinates>
                                          </IfcCartesianPoint>
                                          <IfcCartesianPoint type="IfcCartesianPoint">
                                            <coordinates>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="371.75340451674"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="176.164956423972"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="-27.9999999999997"/>
                                            </coordinates>
                                          </IfcCartesianPoint>
                                          <IfcCartesianPoint type="IfcCartesianPoint">
                                            <coordinates>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="288.912996848885"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="-27.9999999999997"/>
                                            </coordinates>
                                          </IfcCartesianPoint>
                                          <IfcCartesianPoint type="IfcCartesianPoint">
                                            <coordinates>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="-371.753404513767"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="176.16495642397"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="-27.9999999999997"/>
                                            </coordinates>
                                          </IfcCartesianPoint>
                                          <IfcCartesianPoint type="IfcCartesianPoint">
                                            <coordinates>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="-229.853624936802"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="-27.9999999999997"/>
                                            </coordinates>
                                          </IfcCartesianPoint>
                                          <IfcCartesianPoint type="IfcCartesianPoint">
                                            <coordinates>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="371.75340451674"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="176.164956423972"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="-27.9999999999997"/>
                                            </coordinates>
                                          </IfcCartesianPoint>
                                          <IfcCartesianPoint type="IfcCartesianPoint">
                                            <coordinates>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="288.912996848885"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="-27.9999999999997"/>
                                            </coordinates>
                                          </IfcCartesianPoint>
                                          <IfcCartesianPoint type="IfcCartesianPoint">
                                            <coordinates>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="-371.753404513767"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="176.16495642397"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="-27.9999999999997"/>
                                            </coordinates>
                                          </IfcCartesianPoint>
                                          <IfcCartesianPoint type="IfcCartesianPoint">
                                            <coordinates>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="305.75580902694"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="184.179257914445"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="-55.9999999999994"/>
                                            </coordinates>
                                          </IfcCartesianPoint>
                                          <IfcCartesianPoint type="IfcCartesianPoint">
                                            <coordinates>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="282.252425166504"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="-55.9999999999994"/>
                                            </coordinates>
                                          </IfcCartesianPoint>
                                          <IfcCartesianPoint type="IfcCartesianPoint">
                                            <coordinates>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="-305.755809023358"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="184.179257914444"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="-55.9999999999994"/>
                                            </coordinates>
                                          </IfcCartesianPoint>
                                          <IfcCartesianPoint type="IfcCartesianPoint">
                                            <coordinates>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="-168.993427725176"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="-55.9999999999994"/>
                                            </coordinates>
                                          </IfcCartesianPoint>
                                          <IfcCartesianPoint type="IfcCartesianPoint">
                                            <coordinates>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="305.75580902694"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="184.179257914445"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="-55.9999999999994"/>
                                            </coordinates>
                                          </IfcCartesianPoint>
                                          <IfcCartesianPoint type="IfcCartesianPoint">
                                            <coordinates>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="282.252425166504"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="-55.9999999999994"/>
                                            </coordinates>
                                          </IfcCartesianPoint>
                                          <IfcCartesianPoint type="IfcCartesianPoint">
                                            <coordinates>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="-305.755809023358"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="184.179257914444"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="-55.9999999999994"/>
                                            </coordinates>
                                          </IfcCartesianPoint>
                                          <IfcCartesianPoint type="IfcCartesianPoint">
                                            <coordinates>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="239.758213537139"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="192.193559404919"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="-83.9999999999991"/>
                                            </coordinates>
                                          </IfcCartesianPoint>
                                          <IfcCartesianPoint type="IfcCartesianPoint">
                                            <coordinates>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="275.591853484122"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="-83.9999999999991"/>
                                            </coordinates>
                                          </IfcCartesianPoint>
                                          <IfcCartesianPoint type="IfcCartesianPoint">
                                            <coordinates>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="-239.75821353295"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="192.193559404918"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="-83.9999999999991"/>
                                            </coordinates>
                                          </IfcCartesianPoint>
                                          <IfcCartesianPoint type="IfcCartesianPoint">
                                            <coordinates>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="-108.13323051355"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="-83.9999999999991"/>
                                            </coordinates>
                                          </IfcCartesianPoint>
                                          <IfcCartesianPoint type="IfcCartesianPoint">
                                            <coordinates>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="239.758213537139"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="192.193559404919"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="-83.9999999999991"/>
                                            </coordinates>
                                          </IfcCartesianPoint>
                                          <IfcCartesianPoint type="IfcCartesianPoint">
                                            <coordinates>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="275.591853484122"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="-83.9999999999991"/>
                                            </coordinates>
                                          </IfcCartesianPoint>
                                          <IfcCartesianPoint type="IfcCartesianPoint">
                                            <coordinates>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="-239.75821353295"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="192.193559404918"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="-83.9999999999991"/>
                                            </coordinates>
                                          </IfcCartesianPoint>
                                        </controlPointsList>
                                        <uClosed type="IfcLogical" value="false"/>
                                        <vClosed type="IfcLogical" value="false"/>
                                        <selfIntersect type="IfcLogical" value="false"/>
                                        <uMultiplicities>
                                          <uMultiplicity type="IfcInteger" value="4"/>
                                          <uMultiplicity type="IfcInteger" value="4"/>
                                        </uMultiplicities>
                                        <vMultiplicities>
                                          <vMultiplicity type="IfcInteger" value="1"/>
                                          <vMultiplicity type="IfcInteger" value="1"/>
                                          <vMultiplicity type="IfcInteger" value="1"/>
                                          <vMultiplicity type="IfcInteger" value="1"/>
                                          <vMultiplicity type="IfcInteger" value="1"/>
                                          <vMultiplicity type="IfcInteger" value="1"/>
                                          <vMultiplicity type="IfcInteger" value="1"/>
                                          <vMultiplicity type="IfcInteger" value="1"/>
                                          <vMultiplicity type="IfcInteger" value="1"/>
                                          <vMultiplicity type="IfcInteger" value="1"/>
                                          <vMultiplicity type="IfcInteger" value="1"/>
                                        </vMultiplicities>
                                        <uKnots>
                                          <uKnot type="IfcParameterValue" value="0.0"/>
                                          <uKnot type="IfcParameterValue" value="14.7110308353668"/>
                                        </uKnots>
                                        <vKnots>
                                          <vKnot type="IfcParameterValue" value="-7.0"/>
                                          <vKnot type="IfcParameterValue" value="-6.0"/>
                                          <vKnot type="IfcParameterValue" value="-5.0"/>
                                          <vKnot type="IfcParameterValue" value="-4.0"/>
                                          <vKnot type="IfcParameterValue" value="-3.0"/>
                                          <vKnot type="IfcParameterValue" value="-2.0"/>
                                          <vKnot type="IfcParameterValue" value="-1.0"/>
                                          <vKnot type="IfcParameterValue" value="0.0"/>
                                          <vKnot type="IfcParameterValue" value="1.0"/>
                                          <vKnot type="IfcParameterValue" value="2.0"/>
                                          <vKnot type="IfcParameterValue" value="3.0"/>
                                        </vKnots>
                                      </faceSurface>
                                      <sameSense type="IfcBoolean" value="false"/>
                                    </IfcFace>
                                    <IfcFace type="IfcAdvancedFace">
                                      <bounds>
                                        <IfcFaceBound type="IfcFaceOuterBound">
                                          <bound type="IfcEdgeLoop">
                                            <edgeList>
                                              <IfcOrientedEdge type="IfcOrientedEdge">
                                                <edgeElement type="IfcEdgeCurve">
                                                  <edgeStart type="IfcVertexPoint">
                                                    <vertexGeometry type="IfcCartesianPoint">
                                                      <coordinates>
                                                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                        <IfcLengthMeasure type="IfcLengthMeasure" value="268.843232748677"/>
                                                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                      </coordinates>
                                                    </vertexGeometry>
                                                  </edgeStart>
                                                  <edgeEnd type="IfcVertexPoint">
                                                    <vertexGeometry type="IfcCartesianPoint">
                                                      <coordinates>
                                                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                        <IfcLengthMeasure type="IfcLengthMeasure" value="247.792422124388"/>
                                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-93.9999999999991"/>
                                                      </coordinates>
                                                    </vertexGeometry>
                                                  </edgeEnd>
                                                  <edgeGeometry type="IfcPolyline">
                                                    <points>
                                                      <IfcCartesianPoint type="IfcCartesianPoint">
                                                        <coordinates>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="268.843232748677"/>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                        </coordinates>
                                                      </IfcCartesianPoint>
                                                      <IfcCartesianPoint type="IfcCartesianPoint">
                                                        <coordinates>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="247.792422124388"/>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="-93.9999999999991"/>
                                                        </coordinates>
                                                      </IfcCartesianPoint>
                                                    </points>
                                                  </edgeGeometry>
                                                  <sameSense type="IfcBoolean" value="false"/>
                                                </edgeElement>
                                                <orientation type="IfcBoolean" value="false"/>
                                              </IfcOrientedEdge>
                                              <IfcOrientedEdge type="IfcOrientedEdge">
                                                <edgeElement type="IfcEdgeCurve">
                                                  <edgeStart type="IfcVertexPoint">
                                                    <vertexGeometry type="IfcCartesianPoint">
                                                      <coordinates>
                                                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                        <IfcLengthMeasure type="IfcLengthMeasure" value="247.792422124388"/>
                                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-93.9999999999991"/>
                                                      </coordinates>
                                                    </vertexGeometry>
                                                  </edgeStart>
                                                  <edgeEnd type="IfcVertexPoint">
                                                    <vertexGeometry type="IfcCartesianPoint">
                                                      <coordinates>
                                                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                        <IfcLengthMeasure type="IfcLengthMeasure" value="247.792422124388"/>
                                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-93.9999999999991"/>
                                                      </coordinates>
                                                    </vertexGeometry>
                                                  </edgeEnd>
                                                  <edgeGeometry type="IfcBSplineCurveWithKnots" curveForm="UNSPECIFIED">
                                                    <closedCurve type="IfcLogical" value="false"/>
                                                    <selfIntersect type="IfcLogical" value="false"/>
                                                    <knotSpec>UNSPECIFIED</knotSpec>
                                                    <upperIndexOnControlPoints>0</upperIndexOnControlPoints>
                                                    <degree type="IfcInteger" value="3"/>
                                                    <controlPointsList>
                                                      <IfcCartesianPoint type="IfcCartesianPoint">
                                                        <coordinates>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="-239.758213535044"/>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="192.193559378247"/>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="-93.9999999999991"/>
                                                        </coordinates>
                                                      </IfcCartesianPoint>
                                                      <IfcCartesianPoint type="IfcCartesianPoint">
                                                        <coordinates>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="275.591853497458"/>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="-93.9999999999991"/>
                                                        </coordinates>
                                                      </IfcCartesianPoint>
                                                      <IfcCartesianPoint type="IfcCartesianPoint">
                                                        <coordinates>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="239.758213535045"/>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="192.193559378248"/>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="-93.9999999999991"/>
                                                        </coordinates>
                                                      </IfcCartesianPoint>
                                                      <IfcCartesianPoint type="IfcCartesianPoint">
                                                        <coordinates>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="-108.133230500215"/>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="-93.9999999999991"/>
                                                        </coordinates>
                                                      </IfcCartesianPoint>
                                                      <IfcCartesianPoint type="IfcCartesianPoint">
                                                        <coordinates>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="-239.758213535044"/>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="192.193559378247"/>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="-93.9999999999991"/>
                                                        </coordinates>
                                                      </IfcCartesianPoint>
                                                      <IfcCartesianPoint type="IfcCartesianPoint">
                                                        <coordinates>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="275.591853497458"/>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="-93.9999999999991"/>
                                                        </coordinates>
                                                      </IfcCartesianPoint>
                                                      <IfcCartesianPoint type="IfcCartesianPoint">
                                                        <coordinates>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="239.758213535045"/>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="192.193559378248"/>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="-93.9999999999991"/>
                                                        </coordinates>
                                                      </IfcCartesianPoint>
                                                    </controlPointsList>
                                                    <knotMultiplicities>
                                                      <IfcInteger type="IfcInteger" value="1"/>
                                                      <IfcInteger type="IfcInteger" value="1"/>
                                                      <IfcInteger type="IfcInteger" value="1"/>
                                                      <IfcInteger type="IfcInteger" value="1"/>
                                                      <IfcInteger type="IfcInteger" value="1"/>
                                                      <IfcInteger type="IfcInteger" value="1"/>
                                                      <IfcInteger type="IfcInteger" value="1"/>
                                                      <IfcInteger type="IfcInteger" value="1"/>
                                                      <IfcInteger type="IfcInteger" value="1"/>
                                                      <IfcInteger type="IfcInteger" value="1"/>
                                                      <IfcInteger type="IfcInteger" value="1"/>
                                                    </knotMultiplicities>
                                                    <knots>
                                                      <IfcParameterValue type="IfcParameterValue" value="-7.0"/>
                                                      <IfcParameterValue type="IfcParameterValue" value="-6.0"/>
                                                      <IfcParameterValue type="IfcParameterValue" value="-5.0"/>
                                                      <IfcParameterValue type="IfcParameterValue" value="-4.0"/>
                                                      <IfcParameterValue type="IfcParameterValue" value="-3.0"/>
                                                      <IfcParameterValue type="IfcParameterValue" value="-2.0"/>
                                                      <IfcParameterValue type="IfcParameterValue" value="-1.0"/>
                                                      <IfcParameterValue type="IfcParameterValue" value="0.0"/>
                                                      <IfcParameterValue type="IfcParameterValue" value="1.0"/>
                                                      <IfcParameterValue type="IfcParameterValue" value="2.0"/>
                                                      <IfcParameterValue type="IfcParameterValue" value="3.0"/>
                                                    </knots>
                                                  </edgeGeometry>
                                                  <sameSense type="IfcBoolean" value="false"/>
                                                </edgeElement>
                                                <orientation type="IfcBoolean" value="false"/>
                                              </IfcOrientedEdge>
                                              <IfcOrientedEdge type="IfcOrientedEdge">
                                                <edgeElement type="IfcEdgeCurve">
                                                  <edgeStart type="IfcVertexPoint">
                                                    <vertexGeometry type="IfcCartesianPoint">
                                                      <coordinates>
                                                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                        <IfcLengthMeasure type="IfcLengthMeasure" value="268.843232748677"/>
                                                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                      </coordinates>
                                                    </vertexGeometry>
                                                  </edgeStart>
                                                  <edgeEnd type="IfcVertexPoint">
                                                    <vertexGeometry type="IfcCartesianPoint">
                                                      <coordinates>
                                                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                        <IfcLengthMeasure type="IfcLengthMeasure" value="247.792422124388"/>
                                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-93.9999999999991"/>
                                                      </coordinates>
                                                    </vertexGeometry>
                                                  </edgeEnd>
                                                  <edgeGeometry type="IfcPolyline">
                                                    <points>
                                                      <IfcCartesianPoint type="IfcCartesianPoint">
                                                        <coordinates>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="268.843232748677"/>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                        </coordinates>
                                                      </IfcCartesianPoint>
                                                      <IfcCartesianPoint type="IfcCartesianPoint">
                                                        <coordinates>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="247.792422124388"/>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="-93.9999999999991"/>
                                                        </coordinates>
                                                      </IfcCartesianPoint>
                                                    </points>
                                                  </edgeGeometry>
                                                  <sameSense type="IfcBoolean" value="false"/>
                                                </edgeElement>
                                                <orientation type="IfcBoolean" value="false"/>
                                              </IfcOrientedEdge>
                                              <IfcOrientedEdge type="IfcOrientedEdge">
                                                <edgeElement type="IfcEdgeCurve">
                                                  <edgeStart type="IfcVertexPoint">
                                                    <vertexGeometry type="IfcCartesianPoint">
                                                      <coordinates>
                                                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                        <IfcLengthMeasure type="IfcLengthMeasure" value="268.843232748677"/>
                                                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                      </coordinates>
                                                    </vertexGeometry>
                                                  </edgeStart>
                                                  <edgeEnd type="IfcVertexPoint">
                                                    <vertexGeometry type="IfcCartesianPoint">
                                                      <coordinates>
                                                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                        <IfcLengthMeasure type="IfcLengthMeasure" value="268.843232748677"/>
                                                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                      </coordinates>
                                                    </vertexGeometry>
                                                  </edgeEnd>
                                                  <edgeGeometry type="IfcBSplineCurveWithKnots" curveForm="UNSPECIFIED">
                                                    <closedCurve type="IfcLogical" value="false"/>
                                                    <selfIntersect type="IfcLogical" value="false"/>
                                                    <knotSpec>UNSPECIFIED</knotSpec>
                                                    <upperIndexOnControlPoints>0</upperIndexOnControlPoints>
                                                    <degree type="IfcInteger" value="3"/>
                                                    <controlPointsList>
                                                      <IfcCartesianPoint type="IfcCartesianPoint">
                                                        <coordinates>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="457.685108750143"/>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="177.051077752302"/>
                                                        </coordinates>
                                                      </IfcCartesianPoint>
                                                      <IfcCartesianPoint type="IfcCartesianPoint">
                                                        <coordinates>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="314.739310246865"/>
                                                        </coordinates>
                                                      </IfcCartesianPoint>
                                                      <IfcCartesianPoint type="IfcCartesianPoint">
                                                        <coordinates>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="-457.685108750141"/>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="177.051077752299"/>
                                                        </coordinates>
                                                      </IfcCartesianPoint>
                                                      <IfcCartesianPoint type="IfcCartesianPoint">
                                                        <coordinates>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="-318.77998625438"/>
                                                        </coordinates>
                                                      </IfcCartesianPoint>
                                                      <IfcCartesianPoint type="IfcCartesianPoint">
                                                        <coordinates>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="457.685108750143"/>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="177.051077752302"/>
                                                        </coordinates>
                                                      </IfcCartesianPoint>
                                                      <IfcCartesianPoint type="IfcCartesianPoint">
                                                        <coordinates>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="314.739310246865"/>
                                                        </coordinates>
                                                      </IfcCartesianPoint>
                                                      <IfcCartesianPoint type="IfcCartesianPoint">
                                                        <coordinates>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="-457.685108750141"/>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="177.051077752299"/>
                                                        </coordinates>
                                                      </IfcCartesianPoint>
                                                    </controlPointsList>
                                                    <knotMultiplicities>
                                                      <IfcInteger type="IfcInteger" value="1"/>
                                                      <IfcInteger type="IfcInteger" value="1"/>
                                                      <IfcInteger type="IfcInteger" value="1"/>
                                                      <IfcInteger type="IfcInteger" value="1"/>
                                                      <IfcInteger type="IfcInteger" value="1"/>
                                                      <IfcInteger type="IfcInteger" value="1"/>
                                                      <IfcInteger type="IfcInteger" value="1"/>
                                                      <IfcInteger type="IfcInteger" value="1"/>
                                                      <IfcInteger type="IfcInteger" value="1"/>
                                                      <IfcInteger type="IfcInteger" value="1"/>
                                                      <IfcInteger type="IfcInteger" value="1"/>
                                                    </knotMultiplicities>
                                                    <knots>
                                                      <IfcParameterValue type="IfcParameterValue" value="-7.0"/>
                                                      <IfcParameterValue type="IfcParameterValue" value="-6.0"/>
                                                      <IfcParameterValue type="IfcParameterValue" value="-5.0"/>
                                                      <IfcParameterValue type="IfcParameterValue" value="-4.0"/>
                                                      <IfcParameterValue type="IfcParameterValue" value="-3.0"/>
                                                      <IfcParameterValue type="IfcParameterValue" value="-2.0"/>
                                                      <IfcParameterValue type="IfcParameterValue" value="-1.0"/>
                                                      <IfcParameterValue type="IfcParameterValue" value="0.0"/>
                                                      <IfcParameterValue type="IfcParameterValue" value="1.0"/>
                                                      <IfcParameterValue type="IfcParameterValue" value="2.0"/>
                                                      <IfcParameterValue type="IfcParameterValue" value="3.0"/>
                                                    </knots>
                                                  </edgeGeometry>
                                                  <sameSense type="IfcBoolean" value="false"/>
                                                </edgeElement>
                                                <orientation type="IfcBoolean" value="false"/>
                                              </IfcOrientedEdge>
                                            </edgeList>
                                          </bound>
                                          <orientation type="IfcBoolean" value="false"/>
                                        </IfcFaceBound>
                                      </bounds>
                                      <faceSurface type="IfcBSplineSurfaceWithKnots" surfaceForm="UNSPECIFIED" knotSpec="UNSPECIFIED">
                                        <vknots>
                                          <vknots type="IfcParameterValue" value="-3.0"/>
                                          <vknots type="IfcParameterValue" value="-2.0"/>
                                          <vknots type="IfcParameterValue" value="-1.0"/>
                                          <vknots type="IfcParameterValue" value="0.0"/>
                                          <vknots type="IfcParameterValue" value="1.0"/>
                                          <vknots type="IfcParameterValue" value="2.0"/>
                                          <vknots type="IfcParameterValue" value="3.0"/>
                                          <vknots type="IfcParameterValue" value="4.0"/>
                                          <vknots type="IfcParameterValue" value="5.0"/>
                                          <vknots type="IfcParameterValue" value="6.0"/>
                                          <vknots type="IfcParameterValue" value="7.0"/>
                                        </vknots>
                                        <vmultiplicities>
                                          <vmultiplicities type="IfcInteger" value="1"/>
                                          <vmultiplicities type="IfcInteger" value="1"/>
                                          <vmultiplicities type="IfcInteger" value="1"/>
                                          <vmultiplicities type="IfcInteger" value="1"/>
                                          <vmultiplicities type="IfcInteger" value="1"/>
                                          <vmultiplicities type="IfcInteger" value="1"/>
                                          <vmultiplicities type="IfcInteger" value="1"/>
                                          <vmultiplicities type="IfcInteger" value="1"/>
                                          <vmultiplicities type="IfcInteger" value="1"/>
                                          <vmultiplicities type="IfcInteger" value="1"/>
                                          <vmultiplicities type="IfcInteger" value="1"/>
                                        </vmultiplicities>
                                        <uknots>
                                          <uknots type="IfcParameterValue" value="0.0"/>
                                          <uknots type="IfcParameterValue" value="15.4213505620632"/>
                                        </uknots>
                                        <umultiplicities>
                                          <umultiplicities type="IfcInteger" value="4"/>
                                          <umultiplicities type="IfcInteger" value="4"/>
                                        </umultiplicities>
                                        <vdegree type="IfcInteger" value="3"/>
                                        <udegree type="IfcInteger" value="3"/>
                                        <uclosed type="IfcLogical" value="false"/>
                                        <vclosed type="IfcLogical" value="false"/>
                                        <uDegree type="IfcInteger" value="3"/>
                                        <vDegree type="IfcInteger" value="3"/>
                                        <controlPointsList>
                                          <IfcCartesianPoint type="IfcCartesianPoint">
                                            <coordinates>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="-457.685108750141"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="177.051077752299"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                            </coordinates>
                                          </IfcCartesianPoint>
                                          <IfcCartesianPoint type="IfcCartesianPoint">
                                            <coordinates>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="314.739310246865"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                            </coordinates>
                                          </IfcCartesianPoint>
                                          <IfcCartesianPoint type="IfcCartesianPoint">
                                            <coordinates>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="457.685108750143"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="177.051077752302"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                            </coordinates>
                                          </IfcCartesianPoint>
                                          <IfcCartesianPoint type="IfcCartesianPoint">
                                            <coordinates>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="-318.77998625438"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                            </coordinates>
                                          </IfcCartesianPoint>
                                          <IfcCartesianPoint type="IfcCartesianPoint">
                                            <coordinates>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="-457.685108750141"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="177.051077752299"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                            </coordinates>
                                          </IfcCartesianPoint>
                                          <IfcCartesianPoint type="IfcCartesianPoint">
                                            <coordinates>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="314.739310246865"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                            </coordinates>
                                          </IfcCartesianPoint>
                                          <IfcCartesianPoint type="IfcCartesianPoint">
                                            <coordinates>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="457.685108750143"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="177.051077752302"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                            </coordinates>
                                          </IfcCartesianPoint>
                                          <IfcCartesianPoint type="IfcCartesianPoint">
                                            <coordinates>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="-385.042810345109"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="182.098571627615"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="-31.333333333333"/>
                                            </coordinates>
                                          </IfcCartesianPoint>
                                          <IfcCartesianPoint type="IfcCartesianPoint">
                                            <coordinates>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="301.690157997063"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="-31.333333333333"/>
                                            </coordinates>
                                          </IfcCartesianPoint>
                                          <IfcCartesianPoint type="IfcCartesianPoint">
                                            <coordinates>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="385.04281034511"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="182.098571627617"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="-31.333333333333"/>
                                            </coordinates>
                                          </IfcCartesianPoint>
                                          <IfcCartesianPoint type="IfcCartesianPoint">
                                            <coordinates>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="-248.564401002992"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="-31.333333333333"/>
                                            </coordinates>
                                          </IfcCartesianPoint>
                                          <IfcCartesianPoint type="IfcCartesianPoint">
                                            <coordinates>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="-385.042810345109"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="182.098571627615"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="-31.333333333333"/>
                                            </coordinates>
                                          </IfcCartesianPoint>
                                          <IfcCartesianPoint type="IfcCartesianPoint">
                                            <coordinates>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="301.690157997063"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="-31.333333333333"/>
                                            </coordinates>
                                          </IfcCartesianPoint>
                                          <IfcCartesianPoint type="IfcCartesianPoint">
                                            <coordinates>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="385.04281034511"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="182.098571627617"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="-31.333333333333"/>
                                            </coordinates>
                                          </IfcCartesianPoint>
                                          <IfcCartesianPoint type="IfcCartesianPoint">
                                            <coordinates>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="-312.400511940076"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="187.146065502931"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="-62.666666666666"/>
                                            </coordinates>
                                          </IfcCartesianPoint>
                                          <IfcCartesianPoint type="IfcCartesianPoint">
                                            <coordinates>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="288.64100574726"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="-62.666666666666"/>
                                            </coordinates>
                                          </IfcCartesianPoint>
                                          <IfcCartesianPoint type="IfcCartesianPoint">
                                            <coordinates>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="312.400511940078"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="187.146065502933"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="-62.666666666666"/>
                                            </coordinates>
                                          </IfcCartesianPoint>
                                          <IfcCartesianPoint type="IfcCartesianPoint">
                                            <coordinates>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="-178.348815751603"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="-62.6666666666661"/>
                                            </coordinates>
                                          </IfcCartesianPoint>
                                          <IfcCartesianPoint type="IfcCartesianPoint">
                                            <coordinates>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="-312.400511940076"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="187.146065502931"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="-62.666666666666"/>
                                            </coordinates>
                                          </IfcCartesianPoint>
                                          <IfcCartesianPoint type="IfcCartesianPoint">
                                            <coordinates>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="288.64100574726"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="-62.666666666666"/>
                                            </coordinates>
                                          </IfcCartesianPoint>
                                          <IfcCartesianPoint type="IfcCartesianPoint">
                                            <coordinates>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="312.400511940078"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="187.146065502933"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="-62.666666666666"/>
                                            </coordinates>
                                          </IfcCartesianPoint>
                                          <IfcCartesianPoint type="IfcCartesianPoint">
                                            <coordinates>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="-239.758213535044"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="192.193559378247"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="-93.9999999999991"/>
                                            </coordinates>
                                          </IfcCartesianPoint>
                                          <IfcCartesianPoint type="IfcCartesianPoint">
                                            <coordinates>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="275.591853497458"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="-93.9999999999991"/>
                                            </coordinates>
                                          </IfcCartesianPoint>
                                          <IfcCartesianPoint type="IfcCartesianPoint">
                                            <coordinates>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="239.758213535045"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="192.193559378248"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="-93.9999999999991"/>
                                            </coordinates>
                                          </IfcCartesianPoint>
                                          <IfcCartesianPoint type="IfcCartesianPoint">
                                            <coordinates>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="-108.133230500215"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="-93.9999999999991"/>
                                            </coordinates>
                                          </IfcCartesianPoint>
                                          <IfcCartesianPoint type="IfcCartesianPoint">
                                            <coordinates>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="-239.758213535044"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="192.193559378247"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="-93.9999999999991"/>
                                            </coordinates>
                                          </IfcCartesianPoint>
                                          <IfcCartesianPoint type="IfcCartesianPoint">
                                            <coordinates>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="275.591853497458"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="-93.9999999999991"/>
                                            </coordinates>
                                          </IfcCartesianPoint>
                                          <IfcCartesianPoint type="IfcCartesianPoint">
                                            <coordinates>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="239.758213535045"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="192.193559378248"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="-93.9999999999991"/>
                                            </coordinates>
                                          </IfcCartesianPoint>
                                        </controlPointsList>
                                        <uClosed type="IfcLogical" value="false"/>
                                        <vClosed type="IfcLogical" value="false"/>
                                        <selfIntersect type="IfcLogical" value="false"/>
                                        <uMultiplicities>
                                          <uMultiplicity type="IfcInteger" value="4"/>
                                          <uMultiplicity type="IfcInteger" value="4"/>
                                        </uMultiplicities>
                                        <vMultiplicities>
                                          <vMultiplicity type="IfcInteger" value="1"/>
                                          <vMultiplicity type="IfcInteger" value="1"/>
                                          <vMultiplicity type="IfcInteger" value="1"/>
                                          <vMultiplicity type="IfcInteger" value="1"/>
                                          <vMultiplicity type="IfcInteger" value="1"/>
                                          <vMultiplicity type="IfcInteger" value="1"/>
                                          <vMultiplicity type="IfcInteger" value="1"/>
                                          <vMultiplicity type="IfcInteger" value="1"/>
                                          <vMultiplicity type="IfcInteger" value="1"/>
                                          <vMultiplicity type="IfcInteger" value="1"/>
                                          <vMultiplicity type="IfcInteger" value="1"/>
                                        </vMultiplicities>
                                        <uKnots>
                                          <uKnot type="IfcParameterValue" value="0.0"/>
                                          <uKnot type="IfcParameterValue" value="15.4213505620632"/>
                                        </uKnots>
                                        <vKnots>
                                          <vKnot type="IfcParameterValue" value="-3.0"/>
                                          <vKnot type="IfcParameterValue" value="-2.0"/>
                                          <vKnot type="IfcParameterValue" value="-1.0"/>
                                          <vKnot type="IfcParameterValue" value="0.0"/>
                                          <vKnot type="IfcParameterValue" value="1.0"/>
                                          <vKnot type="IfcParameterValue" value="2.0"/>
                                          <vKnot type="IfcParameterValue" value="3.0"/>
                                          <vKnot type="IfcParameterValue" value="4.0"/>
                                          <vKnot type="IfcParameterValue" value="5.0"/>
                                          <vKnot type="IfcParameterValue" value="6.0"/>
                                          <vKnot type="IfcParameterValue" value="7.0"/>
                                        </vKnots>
                                      </faceSurface>
                                      <sameSense type="IfcBoolean" value="false"/>
                                    </IfcFace>
                                    <IfcFace type="IfcAdvancedFace">
                                      <bounds>
                                        <IfcFaceBound type="IfcFaceOuterBound">
                                          <bound type="IfcEdgeLoop">
                                            <edgeList>
                                              <IfcOrientedEdge type="IfcOrientedEdge">
                                                <edgeElement type="IfcEdgeCurve">
                                                  <edgeStart type="IfcVertexPoint">
                                                    <vertexGeometry type="IfcCartesianPoint">
                                                      <coordinates>
                                                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                        <IfcLengthMeasure type="IfcLengthMeasure" value="247.792422124388"/>
                                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-83.9999999999991"/>
                                                      </coordinates>
                                                    </vertexGeometry>
                                                  </edgeStart>
                                                  <edgeEnd type="IfcVertexPoint">
                                                    <vertexGeometry type="IfcCartesianPoint">
                                                      <coordinates>
                                                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                        <IfcLengthMeasure type="IfcLengthMeasure" value="247.792422124388"/>
                                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-83.9999999999991"/>
                                                      </coordinates>
                                                    </vertexGeometry>
                                                  </edgeEnd>
                                                  <edgeGeometry type="IfcBSplineCurveWithKnots" curveForm="UNSPECIFIED">
                                                    <closedCurve type="IfcLogical" value="false"/>
                                                    <selfIntersect type="IfcLogical" value="false"/>
                                                    <knotSpec>UNSPECIFIED</knotSpec>
                                                    <upperIndexOnControlPoints>0</upperIndexOnControlPoints>
                                                    <degree type="IfcInteger" value="3"/>
                                                    <controlPointsList>
                                                      <IfcCartesianPoint type="IfcCartesianPoint">
                                                        <coordinates>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="239.758213537139"/>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="192.193559404919"/>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="-83.9999999999991"/>
                                                        </coordinates>
                                                      </IfcCartesianPoint>
                                                      <IfcCartesianPoint type="IfcCartesianPoint">
                                                        <coordinates>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="275.591853484122"/>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="-83.9999999999991"/>
                                                        </coordinates>
                                                      </IfcCartesianPoint>
                                                      <IfcCartesianPoint type="IfcCartesianPoint">
                                                        <coordinates>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="-239.75821353295"/>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="192.193559404918"/>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="-83.9999999999991"/>
                                                        </coordinates>
                                                      </IfcCartesianPoint>
                                                      <IfcCartesianPoint type="IfcCartesianPoint">
                                                        <coordinates>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="-108.13323051355"/>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="-83.9999999999991"/>
                                                        </coordinates>
                                                      </IfcCartesianPoint>
                                                      <IfcCartesianPoint type="IfcCartesianPoint">
                                                        <coordinates>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="239.758213537139"/>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="192.193559404919"/>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="-83.9999999999991"/>
                                                        </coordinates>
                                                      </IfcCartesianPoint>
                                                      <IfcCartesianPoint type="IfcCartesianPoint">
                                                        <coordinates>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="275.591853484122"/>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="-83.9999999999991"/>
                                                        </coordinates>
                                                      </IfcCartesianPoint>
                                                      <IfcCartesianPoint type="IfcCartesianPoint">
                                                        <coordinates>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="-239.75821353295"/>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="192.193559404918"/>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="-83.9999999999991"/>
                                                        </coordinates>
                                                      </IfcCartesianPoint>
                                                    </controlPointsList>
                                                    <knotMultiplicities>
                                                      <IfcInteger type="IfcInteger" value="1"/>
                                                      <IfcInteger type="IfcInteger" value="1"/>
                                                      <IfcInteger type="IfcInteger" value="1"/>
                                                      <IfcInteger type="IfcInteger" value="1"/>
                                                      <IfcInteger type="IfcInteger" value="1"/>
                                                      <IfcInteger type="IfcInteger" value="1"/>
                                                      <IfcInteger type="IfcInteger" value="1"/>
                                                      <IfcInteger type="IfcInteger" value="1"/>
                                                      <IfcInteger type="IfcInteger" value="1"/>
                                                      <IfcInteger type="IfcInteger" value="1"/>
                                                      <IfcInteger type="IfcInteger" value="1"/>
                                                    </knotMultiplicities>
                                                    <knots>
                                                      <IfcParameterValue type="IfcParameterValue" value="-7.0"/>
                                                      <IfcParameterValue type="IfcParameterValue" value="-6.0"/>
                                                      <IfcParameterValue type="IfcParameterValue" value="-5.0"/>
                                                      <IfcParameterValue type="IfcParameterValue" value="-4.0"/>
                                                      <IfcParameterValue type="IfcParameterValue" value="-3.0"/>
                                                      <IfcParameterValue type="IfcParameterValue" value="-2.0"/>
                                                      <IfcParameterValue type="IfcParameterValue" value="-1.0"/>
                                                      <IfcParameterValue type="IfcParameterValue" value="0.0"/>
                                                      <IfcParameterValue type="IfcParameterValue" value="1.0"/>
                                                      <IfcParameterValue type="IfcParameterValue" value="2.0"/>
                                                      <IfcParameterValue type="IfcParameterValue" value="3.0"/>
                                                    </knots>
                                                  </edgeGeometry>
                                                  <sameSense type="IfcBoolean" value="false"/>
                                                </edgeElement>
                                                <orientation type="IfcBoolean" value="false"/>
                                              </IfcOrientedEdge>
                                            </edgeList>
                                          </bound>
                                          <orientation type="IfcBoolean" value="false"/>
                                        </IfcFaceBound>
                                      </bounds>
                                      <faceSurface type="IfcPlane">
                                        <position type="IfcAxis2Placement3D">
                                          <location type="IfcCartesianPoint">
                                            <coordinates>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="247.792422124388"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="-83.9999999999991"/>
                                            </coordinates>
                                          </location>
                                        </position>
                                      </faceSurface>
                                      <sameSense type="IfcBoolean" value="false"/>
                                    </IfcFace>
                                    <IfcFace type="IfcAdvancedFace">
                                      <bounds>
                                        <IfcFaceBound type="IfcFaceOuterBound">
                                          <bound type="IfcEdgeLoop">
                                            <edgeList>
                                              <IfcOrientedEdge type="IfcOrientedEdge">
                                                <edgeElement type="IfcEdgeCurve">
                                                  <edgeStart type="IfcVertexPoint">
                                                    <vertexGeometry type="IfcCartesianPoint">
                                                      <coordinates>
                                                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                        <IfcLengthMeasure type="IfcLengthMeasure" value="268.843232748677"/>
                                                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                      </coordinates>
                                                    </vertexGeometry>
                                                  </edgeStart>
                                                  <edgeEnd type="IfcVertexPoint">
                                                    <vertexGeometry type="IfcCartesianPoint">
                                                      <coordinates>
                                                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                        <IfcLengthMeasure type="IfcLengthMeasure" value="268.843232748677"/>
                                                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                      </coordinates>
                                                    </vertexGeometry>
                                                  </edgeEnd>
                                                  <edgeGeometry type="IfcBSplineCurveWithKnots" curveForm="UNSPECIFIED">
                                                    <closedCurve type="IfcLogical" value="false"/>
                                                    <selfIntersect type="IfcLogical" value="false"/>
                                                    <knotSpec>UNSPECIFIED</knotSpec>
                                                    <upperIndexOnControlPoints>0</upperIndexOnControlPoints>
                                                    <degree type="IfcInteger" value="3"/>
                                                    <controlPointsList>
                                                      <IfcCartesianPoint type="IfcCartesianPoint">
                                                        <coordinates>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="457.685108750143"/>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="177.051077752302"/>
                                                        </coordinates>
                                                      </IfcCartesianPoint>
                                                      <IfcCartesianPoint type="IfcCartesianPoint">
                                                        <coordinates>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="314.739310246865"/>
                                                        </coordinates>
                                                      </IfcCartesianPoint>
                                                      <IfcCartesianPoint type="IfcCartesianPoint">
                                                        <coordinates>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="-457.685108750141"/>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="177.051077752299"/>
                                                        </coordinates>
                                                      </IfcCartesianPoint>
                                                      <IfcCartesianPoint type="IfcCartesianPoint">
                                                        <coordinates>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="-318.77998625438"/>
                                                        </coordinates>
                                                      </IfcCartesianPoint>
                                                      <IfcCartesianPoint type="IfcCartesianPoint">
                                                        <coordinates>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="457.685108750143"/>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="177.051077752302"/>
                                                        </coordinates>
                                                      </IfcCartesianPoint>
                                                      <IfcCartesianPoint type="IfcCartesianPoint">
                                                        <coordinates>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="314.739310246865"/>
                                                        </coordinates>
                                                      </IfcCartesianPoint>
                                                      <IfcCartesianPoint type="IfcCartesianPoint">
                                                        <coordinates>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="-457.685108750141"/>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="177.051077752299"/>
                                                        </coordinates>
                                                      </IfcCartesianPoint>
                                                    </controlPointsList>
                                                    <knotMultiplicities>
                                                      <IfcInteger type="IfcInteger" value="1"/>
                                                      <IfcInteger type="IfcInteger" value="1"/>
                                                      <IfcInteger type="IfcInteger" value="1"/>
                                                      <IfcInteger type="IfcInteger" value="1"/>
                                                      <IfcInteger type="IfcInteger" value="1"/>
                                                      <IfcInteger type="IfcInteger" value="1"/>
                                                      <IfcInteger type="IfcInteger" value="1"/>
                                                      <IfcInteger type="IfcInteger" value="1"/>
                                                      <IfcInteger type="IfcInteger" value="1"/>
                                                      <IfcInteger type="IfcInteger" value="1"/>
                                                      <IfcInteger type="IfcInteger" value="1"/>
                                                    </knotMultiplicities>
                                                    <knots>
                                                      <IfcParameterValue type="IfcParameterValue" value="-7.0"/>
                                                      <IfcParameterValue type="IfcParameterValue" value="-6.0"/>
                                                      <IfcParameterValue type="IfcParameterValue" value="-5.0"/>
                                                      <IfcParameterValue type="IfcParameterValue" value="-4.0"/>
                                                      <IfcParameterValue type="IfcParameterValue" value="-3.0"/>
                                                      <IfcParameterValue type="IfcParameterValue" value="-2.0"/>
                                                      <IfcParameterValue type="IfcParameterValue" value="-1.0"/>
                                                      <IfcParameterValue type="IfcParameterValue" value="0.0"/>
                                                      <IfcParameterValue type="IfcParameterValue" value="1.0"/>
                                                      <IfcParameterValue type="IfcParameterValue" value="2.0"/>
                                                      <IfcParameterValue type="IfcParameterValue" value="3.0"/>
                                                    </knots>
                                                  </edgeGeometry>
                                                  <sameSense type="IfcBoolean" value="false"/>
                                                </edgeElement>
                                                <orientation type="IfcBoolean" value="false"/>
                                              </IfcOrientedEdge>
                                            </edgeList>
                                          </bound>
                                          <orientation type="IfcBoolean" value="false"/>
                                        </IfcFaceBound>
                                        <IfcFaceBound type="IfcFaceBound">
                                          <bound type="IfcEdgeLoop">
                                            <edgeList>
                                              <IfcOrientedEdge type="IfcOrientedEdge">
                                                <edgeElement type="IfcEdgeCurve">
                                                  <edgeStart type="IfcVertexPoint">
                                                    <vertexGeometry type="IfcCartesianPoint">
                                                      <coordinates>
                                                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                        <IfcLengthMeasure type="IfcLengthMeasure" value="253.099263998677"/>
                                                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                      </coordinates>
                                                    </vertexGeometry>
                                                  </edgeStart>
                                                  <edgeEnd type="IfcVertexPoint">
                                                    <vertexGeometry type="IfcCartesianPoint">
                                                      <coordinates>
                                                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                        <IfcLengthMeasure type="IfcLengthMeasure" value="253.099263998677"/>
                                                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                      </coordinates>
                                                    </vertexGeometry>
                                                  </edgeEnd>
                                                  <edgeGeometry type="IfcBSplineCurveWithKnots" curveForm="UNSPECIFIED">
                                                    <closedCurve type="IfcLogical" value="false"/>
                                                    <selfIntersect type="IfcLogical" value="false"/>
                                                    <knotSpec>UNSPECIFIED</knotSpec>
                                                    <upperIndexOnControlPoints>0</upperIndexOnControlPoints>
                                                    <degree type="IfcInteger" value="3"/>
                                                    <controlPointsList>
                                                      <IfcCartesianPoint type="IfcCartesianPoint">
                                                        <coordinates>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="-437.751000004175"/>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="168.150654933496"/>
                                                        </coordinates>
                                                      </IfcCartesianPoint>
                                                      <IfcCartesianPoint type="IfcCartesianPoint">
                                                        <coordinates>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="295.573568531267"/>
                                                        </coordinates>
                                                      </IfcCartesianPoint>
                                                      <IfcCartesianPoint type="IfcCartesianPoint">
                                                        <coordinates>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="437.751000006541"/>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="168.150654933498"/>
                                                        </coordinates>
                                                      </IfcCartesianPoint>
                                                      <IfcCartesianPoint type="IfcCartesianPoint">
                                                        <coordinates>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="-290.713822148428"/>
                                                        </coordinates>
                                                      </IfcCartesianPoint>
                                                      <IfcCartesianPoint type="IfcCartesianPoint">
                                                        <coordinates>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="-437.751000004175"/>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="168.150654933496"/>
                                                        </coordinates>
                                                      </IfcCartesianPoint>
                                                      <IfcCartesianPoint type="IfcCartesianPoint">
                                                        <coordinates>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="295.573568531267"/>
                                                        </coordinates>
                                                      </IfcCartesianPoint>
                                                      <IfcCartesianPoint type="IfcCartesianPoint">
                                                        <coordinates>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="437.751000006541"/>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="168.150654933498"/>
                                                        </coordinates>
                                                      </IfcCartesianPoint>
                                                    </controlPointsList>
                                                    <knotMultiplicities>
                                                      <IfcInteger type="IfcInteger" value="1"/>
                                                      <IfcInteger type="IfcInteger" value="1"/>
                                                      <IfcInteger type="IfcInteger" value="1"/>
                                                      <IfcInteger type="IfcInteger" value="1"/>
                                                      <IfcInteger type="IfcInteger" value="1"/>
                                                      <IfcInteger type="IfcInteger" value="1"/>
                                                      <IfcInteger type="IfcInteger" value="1"/>
                                                      <IfcInteger type="IfcInteger" value="1"/>
                                                      <IfcInteger type="IfcInteger" value="1"/>
                                                      <IfcInteger type="IfcInteger" value="1"/>
                                                      <IfcInteger type="IfcInteger" value="1"/>
                                                    </knotMultiplicities>
                                                    <knots>
                                                      <IfcParameterValue type="IfcParameterValue" value="-7.0"/>
                                                      <IfcParameterValue type="IfcParameterValue" value="-6.0"/>
                                                      <IfcParameterValue type="IfcParameterValue" value="-5.0"/>
                                                      <IfcParameterValue type="IfcParameterValue" value="-4.0"/>
                                                      <IfcParameterValue type="IfcParameterValue" value="-3.0"/>
                                                      <IfcParameterValue type="IfcParameterValue" value="-2.0"/>
                                                      <IfcParameterValue type="IfcParameterValue" value="-1.0"/>
                                                      <IfcParameterValue type="IfcParameterValue" value="0.0"/>
                                                      <IfcParameterValue type="IfcParameterValue" value="1.0"/>
                                                      <IfcParameterValue type="IfcParameterValue" value="2.0"/>
                                                      <IfcParameterValue type="IfcParameterValue" value="3.0"/>
                                                    </knots>
                                                  </edgeGeometry>
                                                  <sameSense type="IfcBoolean" value="false"/>
                                                </edgeElement>
                                                <orientation type="IfcBoolean" value="false"/>
                                              </IfcOrientedEdge>
                                            </edgeList>
                                          </bound>
                                          <orientation type="IfcBoolean" value="false"/>
                                        </IfcFaceBound>
                                      </bounds>
                                      <faceSurface type="IfcPlane">
                                        <position type="IfcAxis2Placement3D">
                                          <location type="IfcCartesianPoint">
                                            <coordinates>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="253.099263998677"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                            </coordinates>
                                          </location>
                                        </position>
                                      </faceSurface>
                                      <sameSense type="IfcBoolean" value="false"/>
                                    </IfcFace>
                                  </cfsFaces>
                                </outer>
                              </IfcRepresentationItem>
                            </items>
                          </mappedRepresentation>
                        </IfcRepresentationMap>
                      </representationMaps>
                    </relatingType>
                  </IfcRelDefinesByType>
                </isTypedBy>
                <objectPlacement type="IfcLocalPlacement" globalId="dbe08876-76d6-4452-8044-25a35bee1e89">
                  <relativePlacement type="IfcAxis2Placement3D">
                    <location type="IfcCartesianPoint">
                      <coordinates>
                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                      </coordinates>
                    </location>
                  </relativePlacement>
                  <placementRelTo>60214d98-36c3-46e3-bcce-ac67693d6555</placementRelTo>
                </objectPlacement>
                <representation type="IfcProductDefinitionShape">
                  <representations>
                    <IfcRepresentation type="IfcShapeRepresentation">
                      <contextOfItems>a7d469e7-37b7-4a63-b07e-c863b65a1b9e</contextOfItems>
                      <representationIdentifier type="IfcLabel" value="Body"/>
                      <representationType type="IfcLabel" value="MappedRepresentation"/>
                      <items>
                        <IfcRepresentationItem type="IfcMappedItem">
                          <mappingSource type="IfcRepresentationMap">
                            <mappingOrigin type="IfcAxis2Placement3D">
                              <location type="IfcCartesianPoint">
                                <coordinates>
                                  <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                  <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                  <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                </coordinates>
                              </location>
                            </mappingOrigin>
                            <mappedRepresentation type="IfcShapeRepresentation">
                              <contextOfItems>a7d469e7-37b7-4a63-b07e-c863b65a1b9e</contextOfItems>
                              <representationIdentifier type="IfcLabel" value="Body"/>
                              <representationType type="IfcLabel" value="AdvancedBrep"/>
                              <items>
                                <IfcRepresentationItem type="IfcAdvancedBrep">
                                  <outer type="IfcClosedShell">
                                    <cfsFaces>
                                      <IfcFace type="IfcAdvancedFace">
                                        <bounds>
                                          <IfcFaceBound type="IfcFaceOuterBound">
                                            <bound type="IfcEdgeLoop">
                                              <edgeList>
                                                <IfcOrientedEdge type="IfcOrientedEdge">
                                                  <edgeElement type="IfcEdgeCurve">
                                                    <edgeStart type="IfcVertexPoint">
                                                      <vertexGeometry type="IfcCartesianPoint">
                                                        <coordinates>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="247.792422124388"/>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="-93.9999999999991"/>
                                                        </coordinates>
                                                      </vertexGeometry>
                                                    </edgeStart>
                                                    <edgeEnd type="IfcVertexPoint">
                                                      <vertexGeometry type="IfcCartesianPoint">
                                                        <coordinates>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="247.792422124388"/>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="-93.9999999999991"/>
                                                        </coordinates>
                                                      </vertexGeometry>
                                                    </edgeEnd>
                                                    <edgeGeometry type="IfcBSplineCurveWithKnots" curveForm="UNSPECIFIED">
                                                      <closedCurve type="IfcLogical" value="false"/>
                                                      <selfIntersect type="IfcLogical" value="false"/>
                                                      <knotSpec>UNSPECIFIED</knotSpec>
                                                      <upperIndexOnControlPoints>0</upperIndexOnControlPoints>
                                                      <degree type="IfcInteger" value="3"/>
                                                      <controlPointsList>
                                                        <IfcCartesianPoint type="IfcCartesianPoint">
                                                          <coordinates>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="-239.758213535044"/>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="192.193559378247"/>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="-93.9999999999991"/>
                                                          </coordinates>
                                                        </IfcCartesianPoint>
                                                        <IfcCartesianPoint type="IfcCartesianPoint">
                                                          <coordinates>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="275.591853497458"/>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="-93.9999999999991"/>
                                                          </coordinates>
                                                        </IfcCartesianPoint>
                                                        <IfcCartesianPoint type="IfcCartesianPoint">
                                                          <coordinates>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="239.758213535045"/>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="192.193559378248"/>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="-93.9999999999991"/>
                                                          </coordinates>
                                                        </IfcCartesianPoint>
                                                        <IfcCartesianPoint type="IfcCartesianPoint">
                                                          <coordinates>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="-108.133230500215"/>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="-93.9999999999991"/>
                                                          </coordinates>
                                                        </IfcCartesianPoint>
                                                        <IfcCartesianPoint type="IfcCartesianPoint">
                                                          <coordinates>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="-239.758213535044"/>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="192.193559378247"/>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="-93.9999999999991"/>
                                                          </coordinates>
                                                        </IfcCartesianPoint>
                                                        <IfcCartesianPoint type="IfcCartesianPoint">
                                                          <coordinates>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="275.591853497458"/>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="-93.9999999999991"/>
                                                          </coordinates>
                                                        </IfcCartesianPoint>
                                                        <IfcCartesianPoint type="IfcCartesianPoint">
                                                          <coordinates>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="239.758213535045"/>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="192.193559378248"/>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="-93.9999999999991"/>
                                                          </coordinates>
                                                        </IfcCartesianPoint>
                                                      </controlPointsList>
                                                      <knotMultiplicities>
                                                        <IfcInteger type="IfcInteger" value="1"/>
                                                        <IfcInteger type="IfcInteger" value="1"/>
                                                        <IfcInteger type="IfcInteger" value="1"/>
                                                        <IfcInteger type="IfcInteger" value="1"/>
                                                        <IfcInteger type="IfcInteger" value="1"/>
                                                        <IfcInteger type="IfcInteger" value="1"/>
                                                        <IfcInteger type="IfcInteger" value="1"/>
                                                        <IfcInteger type="IfcInteger" value="1"/>
                                                        <IfcInteger type="IfcInteger" value="1"/>
                                                        <IfcInteger type="IfcInteger" value="1"/>
                                                        <IfcInteger type="IfcInteger" value="1"/>
                                                      </knotMultiplicities>
                                                      <knots>
                                                        <IfcParameterValue type="IfcParameterValue" value="-7.0"/>
                                                        <IfcParameterValue type="IfcParameterValue" value="-6.0"/>
                                                        <IfcParameterValue type="IfcParameterValue" value="-5.0"/>
                                                        <IfcParameterValue type="IfcParameterValue" value="-4.0"/>
                                                        <IfcParameterValue type="IfcParameterValue" value="-3.0"/>
                                                        <IfcParameterValue type="IfcParameterValue" value="-2.0"/>
                                                        <IfcParameterValue type="IfcParameterValue" value="-1.0"/>
                                                        <IfcParameterValue type="IfcParameterValue" value="0.0"/>
                                                        <IfcParameterValue type="IfcParameterValue" value="1.0"/>
                                                        <IfcParameterValue type="IfcParameterValue" value="2.0"/>
                                                        <IfcParameterValue type="IfcParameterValue" value="3.0"/>
                                                      </knots>
                                                    </edgeGeometry>
                                                    <sameSense type="IfcBoolean" value="false"/>
                                                  </edgeElement>
                                                  <orientation type="IfcBoolean" value="false"/>
                                                </IfcOrientedEdge>
                                              </edgeList>
                                            </bound>
                                            <orientation type="IfcBoolean" value="false"/>
                                          </IfcFaceBound>
                                        </bounds>
                                        <faceSurface type="IfcPlane">
                                          <position type="IfcAxis2Placement3D">
                                            <location type="IfcCartesianPoint">
                                              <coordinates>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="247.792422124388"/>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="-93.9999999999991"/>
                                              </coordinates>
                                            </location>
                                          </position>
                                        </faceSurface>
                                        <sameSense type="IfcBoolean" value="false"/>
                                      </IfcFace>
                                      <IfcFace type="IfcAdvancedFace">
                                        <bounds>
                                          <IfcFaceBound type="IfcFaceOuterBound">
                                            <bound type="IfcEdgeLoop">
                                              <edgeList>
                                                <IfcOrientedEdge type="IfcOrientedEdge">
                                                  <edgeElement type="IfcEdgeCurve">
                                                    <edgeStart type="IfcVertexPoint">
                                                      <vertexGeometry type="IfcCartesianPoint">
                                                        <coordinates>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="253.099263998677"/>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                        </coordinates>
                                                      </vertexGeometry>
                                                    </edgeStart>
                                                    <edgeEnd type="IfcVertexPoint">
                                                      <vertexGeometry type="IfcCartesianPoint">
                                                        <coordinates>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="247.792422124388"/>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="-83.9999999999991"/>
                                                        </coordinates>
                                                      </vertexGeometry>
                                                    </edgeEnd>
                                                    <edgeGeometry type="IfcPolyline">
                                                      <points>
                                                        <IfcCartesianPoint type="IfcCartesianPoint">
                                                          <coordinates>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="253.099263998677"/>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                          </coordinates>
                                                        </IfcCartesianPoint>
                                                        <IfcCartesianPoint type="IfcCartesianPoint">
                                                          <coordinates>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="247.792422124388"/>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="-83.9999999999991"/>
                                                          </coordinates>
                                                        </IfcCartesianPoint>
                                                      </points>
                                                    </edgeGeometry>
                                                    <sameSense type="IfcBoolean" value="false"/>
                                                  </edgeElement>
                                                  <orientation type="IfcBoolean" value="false"/>
                                                </IfcOrientedEdge>
                                                <IfcOrientedEdge type="IfcOrientedEdge">
                                                  <edgeElement type="IfcEdgeCurve">
                                                    <edgeStart type="IfcVertexPoint">
                                                      <vertexGeometry type="IfcCartesianPoint">
                                                        <coordinates>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="247.792422124388"/>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="-83.9999999999991"/>
                                                        </coordinates>
                                                      </vertexGeometry>
                                                    </edgeStart>
                                                    <edgeEnd type="IfcVertexPoint">
                                                      <vertexGeometry type="IfcCartesianPoint">
                                                        <coordinates>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="247.792422124388"/>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="-83.9999999999991"/>
                                                        </coordinates>
                                                      </vertexGeometry>
                                                    </edgeEnd>
                                                    <edgeGeometry type="IfcBSplineCurveWithKnots" curveForm="UNSPECIFIED">
                                                      <closedCurve type="IfcLogical" value="false"/>
                                                      <selfIntersect type="IfcLogical" value="false"/>
                                                      <knotSpec>UNSPECIFIED</knotSpec>
                                                      <upperIndexOnControlPoints>0</upperIndexOnControlPoints>
                                                      <degree type="IfcInteger" value="3"/>
                                                      <controlPointsList>
                                                        <IfcCartesianPoint type="IfcCartesianPoint">
                                                          <coordinates>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="239.758213537139"/>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="192.193559404919"/>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="-83.9999999999991"/>
                                                          </coordinates>
                                                        </IfcCartesianPoint>
                                                        <IfcCartesianPoint type="IfcCartesianPoint">
                                                          <coordinates>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="275.591853484122"/>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="-83.9999999999991"/>
                                                          </coordinates>
                                                        </IfcCartesianPoint>
                                                        <IfcCartesianPoint type="IfcCartesianPoint">
                                                          <coordinates>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="-239.75821353295"/>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="192.193559404918"/>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="-83.9999999999991"/>
                                                          </coordinates>
                                                        </IfcCartesianPoint>
                                                        <IfcCartesianPoint type="IfcCartesianPoint">
                                                          <coordinates>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="-108.13323051355"/>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="-83.9999999999991"/>
                                                          </coordinates>
                                                        </IfcCartesianPoint>
                                                        <IfcCartesianPoint type="IfcCartesianPoint">
                                                          <coordinates>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="239.758213537139"/>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="192.193559404919"/>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="-83.9999999999991"/>
                                                          </coordinates>
                                                        </IfcCartesianPoint>
                                                        <IfcCartesianPoint type="IfcCartesianPoint">
                                                          <coordinates>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="275.591853484122"/>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="-83.9999999999991"/>
                                                          </coordinates>
                                                        </IfcCartesianPoint>
                                                        <IfcCartesianPoint type="IfcCartesianPoint">
                                                          <coordinates>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="-239.75821353295"/>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="192.193559404918"/>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="-83.9999999999991"/>
                                                          </coordinates>
                                                        </IfcCartesianPoint>
                                                      </controlPointsList>
                                                      <knotMultiplicities>
                                                        <IfcInteger type="IfcInteger" value="1"/>
                                                        <IfcInteger type="IfcInteger" value="1"/>
                                                        <IfcInteger type="IfcInteger" value="1"/>
                                                        <IfcInteger type="IfcInteger" value="1"/>
                                                        <IfcInteger type="IfcInteger" value="1"/>
                                                        <IfcInteger type="IfcInteger" value="1"/>
                                                        <IfcInteger type="IfcInteger" value="1"/>
                                                        <IfcInteger type="IfcInteger" value="1"/>
                                                        <IfcInteger type="IfcInteger" value="1"/>
                                                        <IfcInteger type="IfcInteger" value="1"/>
                                                        <IfcInteger type="IfcInteger" value="1"/>
                                                      </knotMultiplicities>
                                                      <knots>
                                                        <IfcParameterValue type="IfcParameterValue" value="-7.0"/>
                                                        <IfcParameterValue type="IfcParameterValue" value="-6.0"/>
                                                        <IfcParameterValue type="IfcParameterValue" value="-5.0"/>
                                                        <IfcParameterValue type="IfcParameterValue" value="-4.0"/>
                                                        <IfcParameterValue type="IfcParameterValue" value="-3.0"/>
                                                        <IfcParameterValue type="IfcParameterValue" value="-2.0"/>
                                                        <IfcParameterValue type="IfcParameterValue" value="-1.0"/>
                                                        <IfcParameterValue type="IfcParameterValue" value="0.0"/>
                                                        <IfcParameterValue type="IfcParameterValue" value="1.0"/>
                                                        <IfcParameterValue type="IfcParameterValue" value="2.0"/>
                                                        <IfcParameterValue type="IfcParameterValue" value="3.0"/>
                                                      </knots>
                                                    </edgeGeometry>
                                                    <sameSense type="IfcBoolean" value="false"/>
                                                  </edgeElement>
                                                  <orientation type="IfcBoolean" value="false"/>
                                                </IfcOrientedEdge>
                                                <IfcOrientedEdge type="IfcOrientedEdge">
                                                  <edgeElement type="IfcEdgeCurve">
                                                    <edgeStart type="IfcVertexPoint">
                                                      <vertexGeometry type="IfcCartesianPoint">
                                                        <coordinates>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="253.099263998677"/>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                        </coordinates>
                                                      </vertexGeometry>
                                                    </edgeStart>
                                                    <edgeEnd type="IfcVertexPoint">
                                                      <vertexGeometry type="IfcCartesianPoint">
                                                        <coordinates>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="247.792422124388"/>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="-83.9999999999991"/>
                                                        </coordinates>
                                                      </vertexGeometry>
                                                    </edgeEnd>
                                                    <edgeGeometry type="IfcPolyline">
                                                      <points>
                                                        <IfcCartesianPoint type="IfcCartesianPoint">
                                                          <coordinates>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="253.099263998677"/>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                          </coordinates>
                                                        </IfcCartesianPoint>
                                                        <IfcCartesianPoint type="IfcCartesianPoint">
                                                          <coordinates>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="247.792422124388"/>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="-83.9999999999991"/>
                                                          </coordinates>
                                                        </IfcCartesianPoint>
                                                      </points>
                                                    </edgeGeometry>
                                                    <sameSense type="IfcBoolean" value="false"/>
                                                  </edgeElement>
                                                  <orientation type="IfcBoolean" value="false"/>
                                                </IfcOrientedEdge>
                                                <IfcOrientedEdge type="IfcOrientedEdge">
                                                  <edgeElement type="IfcEdgeCurve">
                                                    <edgeStart type="IfcVertexPoint">
                                                      <vertexGeometry type="IfcCartesianPoint">
                                                        <coordinates>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="253.099263998677"/>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                        </coordinates>
                                                      </vertexGeometry>
                                                    </edgeStart>
                                                    <edgeEnd type="IfcVertexPoint">
                                                      <vertexGeometry type="IfcCartesianPoint">
                                                        <coordinates>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="253.099263998677"/>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                        </coordinates>
                                                      </vertexGeometry>
                                                    </edgeEnd>
                                                    <edgeGeometry type="IfcBSplineCurveWithKnots" curveForm="UNSPECIFIED">
                                                      <closedCurve type="IfcLogical" value="false"/>
                                                      <selfIntersect type="IfcLogical" value="false"/>
                                                      <knotSpec>UNSPECIFIED</knotSpec>
                                                      <upperIndexOnControlPoints>0</upperIndexOnControlPoints>
                                                      <degree type="IfcInteger" value="3"/>
                                                      <controlPointsList>
                                                        <IfcCartesianPoint type="IfcCartesianPoint">
                                                          <coordinates>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="-437.751000004175"/>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="168.150654933496"/>
                                                          </coordinates>
                                                        </IfcCartesianPoint>
                                                        <IfcCartesianPoint type="IfcCartesianPoint">
                                                          <coordinates>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="295.573568531267"/>
                                                          </coordinates>
                                                        </IfcCartesianPoint>
                                                        <IfcCartesianPoint type="IfcCartesianPoint">
                                                          <coordinates>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="437.751000006541"/>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="168.150654933498"/>
                                                          </coordinates>
                                                        </IfcCartesianPoint>
                                                        <IfcCartesianPoint type="IfcCartesianPoint">
                                                          <coordinates>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="-290.713822148428"/>
                                                          </coordinates>
                                                        </IfcCartesianPoint>
                                                        <IfcCartesianPoint type="IfcCartesianPoint">
                                                          <coordinates>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="-437.751000004175"/>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="168.150654933496"/>
                                                          </coordinates>
                                                        </IfcCartesianPoint>
                                                        <IfcCartesianPoint type="IfcCartesianPoint">
                                                          <coordinates>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="295.573568531267"/>
                                                          </coordinates>
                                                        </IfcCartesianPoint>
                                                        <IfcCartesianPoint type="IfcCartesianPoint">
                                                          <coordinates>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="437.751000006541"/>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="168.150654933498"/>
                                                          </coordinates>
                                                        </IfcCartesianPoint>
                                                      </controlPointsList>
                                                      <knotMultiplicities>
                                                        <IfcInteger type="IfcInteger" value="1"/>
                                                        <IfcInteger type="IfcInteger" value="1"/>
                                                        <IfcInteger type="IfcInteger" value="1"/>
                                                        <IfcInteger type="IfcInteger" value="1"/>
                                                        <IfcInteger type="IfcInteger" value="1"/>
                                                        <IfcInteger type="IfcInteger" value="1"/>
                                                        <IfcInteger type="IfcInteger" value="1"/>
                                                        <IfcInteger type="IfcInteger" value="1"/>
                                                        <IfcInteger type="IfcInteger" value="1"/>
                                                        <IfcInteger type="IfcInteger" value="1"/>
                                                        <IfcInteger type="IfcInteger" value="1"/>
                                                      </knotMultiplicities>
                                                      <knots>
                                                        <IfcParameterValue type="IfcParameterValue" value="-7.0"/>
                                                        <IfcParameterValue type="IfcParameterValue" value="-6.0"/>
                                                        <IfcParameterValue type="IfcParameterValue" value="-5.0"/>
                                                        <IfcParameterValue type="IfcParameterValue" value="-4.0"/>
                                                        <IfcParameterValue type="IfcParameterValue" value="-3.0"/>
                                                        <IfcParameterValue type="IfcParameterValue" value="-2.0"/>
                                                        <IfcParameterValue type="IfcParameterValue" value="-1.0"/>
                                                        <IfcParameterValue type="IfcParameterValue" value="0.0"/>
                                                        <IfcParameterValue type="IfcParameterValue" value="1.0"/>
                                                        <IfcParameterValue type="IfcParameterValue" value="2.0"/>
                                                        <IfcParameterValue type="IfcParameterValue" value="3.0"/>
                                                      </knots>
                                                    </edgeGeometry>
                                                    <sameSense type="IfcBoolean" value="false"/>
                                                  </edgeElement>
                                                  <orientation type="IfcBoolean" value="false"/>
                                                </IfcOrientedEdge>
                                              </edgeList>
                                            </bound>
                                            <orientation type="IfcBoolean" value="false"/>
                                          </IfcFaceBound>
                                        </bounds>
                                        <faceSurface type="IfcBSplineSurfaceWithKnots" surfaceForm="UNSPECIFIED" knotSpec="UNSPECIFIED">
                                          <vknots>
                                            <vknots type="IfcParameterValue" value="-7.0"/>
                                            <vknots type="IfcParameterValue" value="-6.0"/>
                                            <vknots type="IfcParameterValue" value="-5.0"/>
                                            <vknots type="IfcParameterValue" value="-4.0"/>
                                            <vknots type="IfcParameterValue" value="-3.0"/>
                                            <vknots type="IfcParameterValue" value="-2.0"/>
                                            <vknots type="IfcParameterValue" value="-1.0"/>
                                            <vknots type="IfcParameterValue" value="0.0"/>
                                            <vknots type="IfcParameterValue" value="1.0"/>
                                            <vknots type="IfcParameterValue" value="2.0"/>
                                            <vknots type="IfcParameterValue" value="3.0"/>
                                          </vknots>
                                          <vmultiplicities>
                                            <vmultiplicities type="IfcInteger" value="1"/>
                                            <vmultiplicities type="IfcInteger" value="1"/>
                                            <vmultiplicities type="IfcInteger" value="1"/>
                                            <vmultiplicities type="IfcInteger" value="1"/>
                                            <vmultiplicities type="IfcInteger" value="1"/>
                                            <vmultiplicities type="IfcInteger" value="1"/>
                                            <vmultiplicities type="IfcInteger" value="1"/>
                                            <vmultiplicities type="IfcInteger" value="1"/>
                                            <vmultiplicities type="IfcInteger" value="1"/>
                                            <vmultiplicities type="IfcInteger" value="1"/>
                                            <vmultiplicities type="IfcInteger" value="1"/>
                                          </vmultiplicities>
                                          <uknots>
                                            <uknots type="IfcParameterValue" value="0.0"/>
                                            <uknots type="IfcParameterValue" value="14.7110308353668"/>
                                          </uknots>
                                          <umultiplicities>
                                            <umultiplicities type="IfcInteger" value="4"/>
                                            <umultiplicities type="IfcInteger" value="4"/>
                                          </umultiplicities>
                                          <vdegree type="IfcInteger" value="3"/>
                                          <udegree type="IfcInteger" value="3"/>
                                          <uclosed type="IfcLogical" value="false"/>
                                          <vclosed type="IfcLogical" value="false"/>
                                          <uDegree type="IfcInteger" value="3"/>
                                          <vDegree type="IfcInteger" value="3"/>
                                          <controlPointsList>
                                            <IfcCartesianPoint type="IfcCartesianPoint">
                                              <coordinates>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="437.751000006541"/>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="168.150654933498"/>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                              </coordinates>
                                            </IfcCartesianPoint>
                                            <IfcCartesianPoint type="IfcCartesianPoint">
                                              <coordinates>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="295.573568531267"/>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                              </coordinates>
                                            </IfcCartesianPoint>
                                            <IfcCartesianPoint type="IfcCartesianPoint">
                                              <coordinates>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="-437.751000004175"/>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="168.150654933496"/>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                              </coordinates>
                                            </IfcCartesianPoint>
                                            <IfcCartesianPoint type="IfcCartesianPoint">
                                              <coordinates>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="-290.713822148428"/>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                              </coordinates>
                                            </IfcCartesianPoint>
                                            <IfcCartesianPoint type="IfcCartesianPoint">
                                              <coordinates>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="437.751000006541"/>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="168.150654933498"/>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                              </coordinates>
                                            </IfcCartesianPoint>
                                            <IfcCartesianPoint type="IfcCartesianPoint">
                                              <coordinates>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="295.573568531267"/>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                              </coordinates>
                                            </IfcCartesianPoint>
                                            <IfcCartesianPoint type="IfcCartesianPoint">
                                              <coordinates>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="-437.751000004175"/>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="168.150654933496"/>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                              </coordinates>
                                            </IfcCartesianPoint>
                                            <IfcCartesianPoint type="IfcCartesianPoint">
                                              <coordinates>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="371.75340451674"/>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="176.164956423972"/>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="-27.9999999999997"/>
                                              </coordinates>
                                            </IfcCartesianPoint>
                                            <IfcCartesianPoint type="IfcCartesianPoint">
                                              <coordinates>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="288.912996848885"/>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="-27.9999999999997"/>
                                              </coordinates>
                                            </IfcCartesianPoint>
                                            <IfcCartesianPoint type="IfcCartesianPoint">
                                              <coordinates>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="-371.753404513767"/>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="176.16495642397"/>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="-27.9999999999997"/>
                                              </coordinates>
                                            </IfcCartesianPoint>
                                            <IfcCartesianPoint type="IfcCartesianPoint">
                                              <coordinates>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="-229.853624936802"/>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="-27.9999999999997"/>
                                              </coordinates>
                                            </IfcCartesianPoint>
                                            <IfcCartesianPoint type="IfcCartesianPoint">
                                              <coordinates>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="371.75340451674"/>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="176.164956423972"/>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="-27.9999999999997"/>
                                              </coordinates>
                                            </IfcCartesianPoint>
                                            <IfcCartesianPoint type="IfcCartesianPoint">
                                              <coordinates>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="288.912996848885"/>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="-27.9999999999997"/>
                                              </coordinates>
                                            </IfcCartesianPoint>
                                            <IfcCartesianPoint type="IfcCartesianPoint">
                                              <coordinates>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="-371.753404513767"/>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="176.16495642397"/>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="-27.9999999999997"/>
                                              </coordinates>
                                            </IfcCartesianPoint>
                                            <IfcCartesianPoint type="IfcCartesianPoint">
                                              <coordinates>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="305.75580902694"/>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="184.179257914445"/>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="-55.9999999999994"/>
                                              </coordinates>
                                            </IfcCartesianPoint>
                                            <IfcCartesianPoint type="IfcCartesianPoint">
                                              <coordinates>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="282.252425166504"/>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="-55.9999999999994"/>
                                              </coordinates>
                                            </IfcCartesianPoint>
                                            <IfcCartesianPoint type="IfcCartesianPoint">
                                              <coordinates>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="-305.755809023358"/>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="184.179257914444"/>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="-55.9999999999994"/>
                                              </coordinates>
                                            </IfcCartesianPoint>
                                            <IfcCartesianPoint type="IfcCartesianPoint">
                                              <coordinates>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="-168.993427725176"/>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="-55.9999999999994"/>
                                              </coordinates>
                                            </IfcCartesianPoint>
                                            <IfcCartesianPoint type="IfcCartesianPoint">
                                              <coordinates>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="305.75580902694"/>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="184.179257914445"/>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="-55.9999999999994"/>
                                              </coordinates>
                                            </IfcCartesianPoint>
                                            <IfcCartesianPoint type="IfcCartesianPoint">
                                              <coordinates>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="282.252425166504"/>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="-55.9999999999994"/>
                                              </coordinates>
                                            </IfcCartesianPoint>
                                            <IfcCartesianPoint type="IfcCartesianPoint">
                                              <coordinates>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="-305.755809023358"/>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="184.179257914444"/>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="-55.9999999999994"/>
                                              </coordinates>
                                            </IfcCartesianPoint>
                                            <IfcCartesianPoint type="IfcCartesianPoint">
                                              <coordinates>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="239.758213537139"/>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="192.193559404919"/>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="-83.9999999999991"/>
                                              </coordinates>
                                            </IfcCartesianPoint>
                                            <IfcCartesianPoint type="IfcCartesianPoint">
                                              <coordinates>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="275.591853484122"/>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="-83.9999999999991"/>
                                              </coordinates>
                                            </IfcCartesianPoint>
                                            <IfcCartesianPoint type="IfcCartesianPoint">
                                              <coordinates>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="-239.75821353295"/>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="192.193559404918"/>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="-83.9999999999991"/>
                                              </coordinates>
                                            </IfcCartesianPoint>
                                            <IfcCartesianPoint type="IfcCartesianPoint">
                                              <coordinates>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="-108.13323051355"/>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="-83.9999999999991"/>
                                              </coordinates>
                                            </IfcCartesianPoint>
                                            <IfcCartesianPoint type="IfcCartesianPoint">
                                              <coordinates>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="239.758213537139"/>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="192.193559404919"/>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="-83.9999999999991"/>
                                              </coordinates>
                                            </IfcCartesianPoint>
                                            <IfcCartesianPoint type="IfcCartesianPoint">
                                              <coordinates>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="275.591853484122"/>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="-83.9999999999991"/>
                                              </coordinates>
                                            </IfcCartesianPoint>
                                            <IfcCartesianPoint type="IfcCartesianPoint">
                                              <coordinates>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="-239.75821353295"/>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="192.193559404918"/>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="-83.9999999999991"/>
                                              </coordinates>
                                            </IfcCartesianPoint>
                                          </controlPointsList>
                                          <uClosed type="IfcLogical" value="false"/>
                                          <vClosed type="IfcLogical" value="false"/>
                                          <selfIntersect type="IfcLogical" value="false"/>
                                          <uMultiplicities>
                                            <uMultiplicity type="IfcInteger" value="4"/>
                                            <uMultiplicity type="IfcInteger" value="4"/>
                                          </uMultiplicities>
                                          <vMultiplicities>
                                            <vMultiplicity type="IfcInteger" value="1"/>
                                            <vMultiplicity type="IfcInteger" value="1"/>
                                            <vMultiplicity type="IfcInteger" value="1"/>
                                            <vMultiplicity type="IfcInteger" value="1"/>
                                            <vMultiplicity type="IfcInteger" value="1"/>
                                            <vMultiplicity type="IfcInteger" value="1"/>
                                            <vMultiplicity type="IfcInteger" value="1"/>
                                            <vMultiplicity type="IfcInteger" value="1"/>
                                            <vMultiplicity type="IfcInteger" value="1"/>
                                            <vMultiplicity type="IfcInteger" value="1"/>
                                            <vMultiplicity type="IfcInteger" value="1"/>
                                          </vMultiplicities>
                                          <uKnots>
                                            <uKnot type="IfcParameterValue" value="0.0"/>
                                            <uKnot type="IfcParameterValue" value="14.7110308353668"/>
                                          </uKnots>
                                          <vKnots>
                                            <vKnot type="IfcParameterValue" value="-7.0"/>
                                            <vKnot type="IfcParameterValue" value="-6.0"/>
                                            <vKnot type="IfcParameterValue" value="-5.0"/>
                                            <vKnot type="IfcParameterValue" value="-4.0"/>
                                            <vKnot type="IfcParameterValue" value="-3.0"/>
                                            <vKnot type="IfcParameterValue" value="-2.0"/>
                                            <vKnot type="IfcParameterValue" value="-1.0"/>
                                            <vKnot type="IfcParameterValue" value="0.0"/>
                                            <vKnot type="IfcParameterValue" value="1.0"/>
                                            <vKnot type="IfcParameterValue" value="2.0"/>
                                            <vKnot type="IfcParameterValue" value="3.0"/>
                                          </vKnots>
                                        </faceSurface>
                                        <sameSense type="IfcBoolean" value="false"/>
                                      </IfcFace>
                                      <IfcFace type="IfcAdvancedFace">
                                        <bounds>
                                          <IfcFaceBound type="IfcFaceOuterBound">
                                            <bound type="IfcEdgeLoop">
                                              <edgeList>
                                                <IfcOrientedEdge type="IfcOrientedEdge">
                                                  <edgeElement type="IfcEdgeCurve">
                                                    <edgeStart type="IfcVertexPoint">
                                                      <vertexGeometry type="IfcCartesianPoint">
                                                        <coordinates>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="268.843232748677"/>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                        </coordinates>
                                                      </vertexGeometry>
                                                    </edgeStart>
                                                    <edgeEnd type="IfcVertexPoint">
                                                      <vertexGeometry type="IfcCartesianPoint">
                                                        <coordinates>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="247.792422124388"/>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="-93.9999999999991"/>
                                                        </coordinates>
                                                      </vertexGeometry>
                                                    </edgeEnd>
                                                    <edgeGeometry type="IfcPolyline">
                                                      <points>
                                                        <IfcCartesianPoint type="IfcCartesianPoint">
                                                          <coordinates>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="268.843232748677"/>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                          </coordinates>
                                                        </IfcCartesianPoint>
                                                        <IfcCartesianPoint type="IfcCartesianPoint">
                                                          <coordinates>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="247.792422124388"/>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="-93.9999999999991"/>
                                                          </coordinates>
                                                        </IfcCartesianPoint>
                                                      </points>
                                                    </edgeGeometry>
                                                    <sameSense type="IfcBoolean" value="false"/>
                                                  </edgeElement>
                                                  <orientation type="IfcBoolean" value="false"/>
                                                </IfcOrientedEdge>
                                                <IfcOrientedEdge type="IfcOrientedEdge">
                                                  <edgeElement type="IfcEdgeCurve">
                                                    <edgeStart type="IfcVertexPoint">
                                                      <vertexGeometry type="IfcCartesianPoint">
                                                        <coordinates>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="247.792422124388"/>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="-93.9999999999991"/>
                                                        </coordinates>
                                                      </vertexGeometry>
                                                    </edgeStart>
                                                    <edgeEnd type="IfcVertexPoint">
                                                      <vertexGeometry type="IfcCartesianPoint">
                                                        <coordinates>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="247.792422124388"/>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="-93.9999999999991"/>
                                                        </coordinates>
                                                      </vertexGeometry>
                                                    </edgeEnd>
                                                    <edgeGeometry type="IfcBSplineCurveWithKnots" curveForm="UNSPECIFIED">
                                                      <closedCurve type="IfcLogical" value="false"/>
                                                      <selfIntersect type="IfcLogical" value="false"/>
                                                      <knotSpec>UNSPECIFIED</knotSpec>
                                                      <upperIndexOnControlPoints>0</upperIndexOnControlPoints>
                                                      <degree type="IfcInteger" value="3"/>
                                                      <controlPointsList>
                                                        <IfcCartesianPoint type="IfcCartesianPoint">
                                                          <coordinates>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="-239.758213535044"/>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="192.193559378247"/>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="-93.9999999999991"/>
                                                          </coordinates>
                                                        </IfcCartesianPoint>
                                                        <IfcCartesianPoint type="IfcCartesianPoint">
                                                          <coordinates>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="275.591853497458"/>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="-93.9999999999991"/>
                                                          </coordinates>
                                                        </IfcCartesianPoint>
                                                        <IfcCartesianPoint type="IfcCartesianPoint">
                                                          <coordinates>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="239.758213535045"/>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="192.193559378248"/>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="-93.9999999999991"/>
                                                          </coordinates>
                                                        </IfcCartesianPoint>
                                                        <IfcCartesianPoint type="IfcCartesianPoint">
                                                          <coordinates>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="-108.133230500215"/>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="-93.9999999999991"/>
                                                          </coordinates>
                                                        </IfcCartesianPoint>
                                                        <IfcCartesianPoint type="IfcCartesianPoint">
                                                          <coordinates>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="-239.758213535044"/>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="192.193559378247"/>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="-93.9999999999991"/>
                                                          </coordinates>
                                                        </IfcCartesianPoint>
                                                        <IfcCartesianPoint type="IfcCartesianPoint">
                                                          <coordinates>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="275.591853497458"/>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="-93.9999999999991"/>
                                                          </coordinates>
                                                        </IfcCartesianPoint>
                                                        <IfcCartesianPoint type="IfcCartesianPoint">
                                                          <coordinates>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="239.758213535045"/>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="192.193559378248"/>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="-93.9999999999991"/>
                                                          </coordinates>
                                                        </IfcCartesianPoint>
                                                      </controlPointsList>
                                                      <knotMultiplicities>
                                                        <IfcInteger type="IfcInteger" value="1"/>
                                                        <IfcInteger type="IfcInteger" value="1"/>
                                                        <IfcInteger type="IfcInteger" value="1"/>
                                                        <IfcInteger type="IfcInteger" value="1"/>
                                                        <IfcInteger type="IfcInteger" value="1"/>
                                                        <IfcInteger type="IfcInteger" value="1"/>
                                                        <IfcInteger type="IfcInteger" value="1"/>
                                                        <IfcInteger type="IfcInteger" value="1"/>
                                                        <IfcInteger type="IfcInteger" value="1"/>
                                                        <IfcInteger type="IfcInteger" value="1"/>
                                                        <IfcInteger type="IfcInteger" value="1"/>
                                                      </knotMultiplicities>
                                                      <knots>
                                                        <IfcParameterValue type="IfcParameterValue" value="-7.0"/>
                                                        <IfcParameterValue type="IfcParameterValue" value="-6.0"/>
                                                        <IfcParameterValue type="IfcParameterValue" value="-5.0"/>
                                                        <IfcParameterValue type="IfcParameterValue" value="-4.0"/>
                                                        <IfcParameterValue type="IfcParameterValue" value="-3.0"/>
                                                        <IfcParameterValue type="IfcParameterValue" value="-2.0"/>
                                                        <IfcParameterValue type="IfcParameterValue" value="-1.0"/>
                                                        <IfcParameterValue type="IfcParameterValue" value="0.0"/>
                                                        <IfcParameterValue type="IfcParameterValue" value="1.0"/>
                                                        <IfcParameterValue type="IfcParameterValue" value="2.0"/>
                                                        <IfcParameterValue type="IfcParameterValue" value="3.0"/>
                                                      </knots>
                                                    </edgeGeometry>
                                                    <sameSense type="IfcBoolean" value="false"/>
                                                  </edgeElement>
                                                  <orientation type="IfcBoolean" value="false"/>
                                                </IfcOrientedEdge>
                                                <IfcOrientedEdge type="IfcOrientedEdge">
                                                  <edgeElement type="IfcEdgeCurve">
                                                    <edgeStart type="IfcVertexPoint">
                                                      <vertexGeometry type="IfcCartesianPoint">
                                                        <coordinates>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="268.843232748677"/>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                        </coordinates>
                                                      </vertexGeometry>
                                                    </edgeStart>
                                                    <edgeEnd type="IfcVertexPoint">
                                                      <vertexGeometry type="IfcCartesianPoint">
                                                        <coordinates>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="247.792422124388"/>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="-93.9999999999991"/>
                                                        </coordinates>
                                                      </vertexGeometry>
                                                    </edgeEnd>
                                                    <edgeGeometry type="IfcPolyline">
                                                      <points>
                                                        <IfcCartesianPoint type="IfcCartesianPoint">
                                                          <coordinates>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="268.843232748677"/>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                          </coordinates>
                                                        </IfcCartesianPoint>
                                                        <IfcCartesianPoint type="IfcCartesianPoint">
                                                          <coordinates>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="247.792422124388"/>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="-93.9999999999991"/>
                                                          </coordinates>
                                                        </IfcCartesianPoint>
                                                      </points>
                                                    </edgeGeometry>
                                                    <sameSense type="IfcBoolean" value="false"/>
                                                  </edgeElement>
                                                  <orientation type="IfcBoolean" value="false"/>
                                                </IfcOrientedEdge>
                                                <IfcOrientedEdge type="IfcOrientedEdge">
                                                  <edgeElement type="IfcEdgeCurve">
                                                    <edgeStart type="IfcVertexPoint">
                                                      <vertexGeometry type="IfcCartesianPoint">
                                                        <coordinates>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="268.843232748677"/>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                        </coordinates>
                                                      </vertexGeometry>
                                                    </edgeStart>
                                                    <edgeEnd type="IfcVertexPoint">
                                                      <vertexGeometry type="IfcCartesianPoint">
                                                        <coordinates>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="268.843232748677"/>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                        </coordinates>
                                                      </vertexGeometry>
                                                    </edgeEnd>
                                                    <edgeGeometry type="IfcBSplineCurveWithKnots" curveForm="UNSPECIFIED">
                                                      <closedCurve type="IfcLogical" value="false"/>
                                                      <selfIntersect type="IfcLogical" value="false"/>
                                                      <knotSpec>UNSPECIFIED</knotSpec>
                                                      <upperIndexOnControlPoints>0</upperIndexOnControlPoints>
                                                      <degree type="IfcInteger" value="3"/>
                                                      <controlPointsList>
                                                        <IfcCartesianPoint type="IfcCartesianPoint">
                                                          <coordinates>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="457.685108750143"/>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="177.051077752302"/>
                                                          </coordinates>
                                                        </IfcCartesianPoint>
                                                        <IfcCartesianPoint type="IfcCartesianPoint">
                                                          <coordinates>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="314.739310246865"/>
                                                          </coordinates>
                                                        </IfcCartesianPoint>
                                                        <IfcCartesianPoint type="IfcCartesianPoint">
                                                          <coordinates>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="-457.685108750141"/>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="177.051077752299"/>
                                                          </coordinates>
                                                        </IfcCartesianPoint>
                                                        <IfcCartesianPoint type="IfcCartesianPoint">
                                                          <coordinates>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="-318.77998625438"/>
                                                          </coordinates>
                                                        </IfcCartesianPoint>
                                                        <IfcCartesianPoint type="IfcCartesianPoint">
                                                          <coordinates>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="457.685108750143"/>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="177.051077752302"/>
                                                          </coordinates>
                                                        </IfcCartesianPoint>
                                                        <IfcCartesianPoint type="IfcCartesianPoint">
                                                          <coordinates>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="314.739310246865"/>
                                                          </coordinates>
                                                        </IfcCartesianPoint>
                                                        <IfcCartesianPoint type="IfcCartesianPoint">
                                                          <coordinates>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="-457.685108750141"/>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="177.051077752299"/>
                                                          </coordinates>
                                                        </IfcCartesianPoint>
                                                      </controlPointsList>
                                                      <knotMultiplicities>
                                                        <IfcInteger type="IfcInteger" value="1"/>
                                                        <IfcInteger type="IfcInteger" value="1"/>
                                                        <IfcInteger type="IfcInteger" value="1"/>
                                                        <IfcInteger type="IfcInteger" value="1"/>
                                                        <IfcInteger type="IfcInteger" value="1"/>
                                                        <IfcInteger type="IfcInteger" value="1"/>
                                                        <IfcInteger type="IfcInteger" value="1"/>
                                                        <IfcInteger type="IfcInteger" value="1"/>
                                                        <IfcInteger type="IfcInteger" value="1"/>
                                                        <IfcInteger type="IfcInteger" value="1"/>
                                                        <IfcInteger type="IfcInteger" value="1"/>
                                                      </knotMultiplicities>
                                                      <knots>
                                                        <IfcParameterValue type="IfcParameterValue" value="-7.0"/>
                                                        <IfcParameterValue type="IfcParameterValue" value="-6.0"/>
                                                        <IfcParameterValue type="IfcParameterValue" value="-5.0"/>
                                                        <IfcParameterValue type="IfcParameterValue" value="-4.0"/>
                                                        <IfcParameterValue type="IfcParameterValue" value="-3.0"/>
                                                        <IfcParameterValue type="IfcParameterValue" value="-2.0"/>
                                                        <IfcParameterValue type="IfcParameterValue" value="-1.0"/>
                                                        <IfcParameterValue type="IfcParameterValue" value="0.0"/>
                                                        <IfcParameterValue type="IfcParameterValue" value="1.0"/>
                                                        <IfcParameterValue type="IfcParameterValue" value="2.0"/>
                                                        <IfcParameterValue type="IfcParameterValue" value="3.0"/>
                                                      </knots>
                                                    </edgeGeometry>
                                                    <sameSense type="IfcBoolean" value="false"/>
                                                  </edgeElement>
                                                  <orientation type="IfcBoolean" value="false"/>
                                                </IfcOrientedEdge>
                                              </edgeList>
                                            </bound>
                                            <orientation type="IfcBoolean" value="false"/>
                                          </IfcFaceBound>
                                        </bounds>
                                        <faceSurface type="IfcBSplineSurfaceWithKnots" surfaceForm="UNSPECIFIED" knotSpec="UNSPECIFIED">
                                          <vknots>
                                            <vknots type="IfcParameterValue" value="-3.0"/>
                                            <vknots type="IfcParameterValue" value="-2.0"/>
                                            <vknots type="IfcParameterValue" value="-1.0"/>
                                            <vknots type="IfcParameterValue" value="0.0"/>
                                            <vknots type="IfcParameterValue" value="1.0"/>
                                            <vknots type="IfcParameterValue" value="2.0"/>
                                            <vknots type="IfcParameterValue" value="3.0"/>
                                            <vknots type="IfcParameterValue" value="4.0"/>
                                            <vknots type="IfcParameterValue" value="5.0"/>
                                            <vknots type="IfcParameterValue" value="6.0"/>
                                            <vknots type="IfcParameterValue" value="7.0"/>
                                          </vknots>
                                          <vmultiplicities>
                                            <vmultiplicities type="IfcInteger" value="1"/>
                                            <vmultiplicities type="IfcInteger" value="1"/>
                                            <vmultiplicities type="IfcInteger" value="1"/>
                                            <vmultiplicities type="IfcInteger" value="1"/>
                                            <vmultiplicities type="IfcInteger" value="1"/>
                                            <vmultiplicities type="IfcInteger" value="1"/>
                                            <vmultiplicities type="IfcInteger" value="1"/>
                                            <vmultiplicities type="IfcInteger" value="1"/>
                                            <vmultiplicities type="IfcInteger" value="1"/>
                                            <vmultiplicities type="IfcInteger" value="1"/>
                                            <vmultiplicities type="IfcInteger" value="1"/>
                                          </vmultiplicities>
                                          <uknots>
                                            <uknots type="IfcParameterValue" value="0.0"/>
                                            <uknots type="IfcParameterValue" value="15.4213505620632"/>
                                          </uknots>
                                          <umultiplicities>
                                            <umultiplicities type="IfcInteger" value="4"/>
                                            <umultiplicities type="IfcInteger" value="4"/>
                                          </umultiplicities>
                                          <vdegree type="IfcInteger" value="3"/>
                                          <udegree type="IfcInteger" value="3"/>
                                          <uclosed type="IfcLogical" value="false"/>
                                          <vclosed type="IfcLogical" value="false"/>
                                          <uDegree type="IfcInteger" value="3"/>
                                          <vDegree type="IfcInteger" value="3"/>
                                          <controlPointsList>
                                            <IfcCartesianPoint type="IfcCartesianPoint">
                                              <coordinates>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="-457.685108750141"/>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="177.051077752299"/>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                              </coordinates>
                                            </IfcCartesianPoint>
                                            <IfcCartesianPoint type="IfcCartesianPoint">
                                              <coordinates>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="314.739310246865"/>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                              </coordinates>
                                            </IfcCartesianPoint>
                                            <IfcCartesianPoint type="IfcCartesianPoint">
                                              <coordinates>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="457.685108750143"/>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="177.051077752302"/>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                              </coordinates>
                                            </IfcCartesianPoint>
                                            <IfcCartesianPoint type="IfcCartesianPoint">
                                              <coordinates>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="-318.77998625438"/>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                              </coordinates>
                                            </IfcCartesianPoint>
                                            <IfcCartesianPoint type="IfcCartesianPoint">
                                              <coordinates>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="-457.685108750141"/>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="177.051077752299"/>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                              </coordinates>
                                            </IfcCartesianPoint>
                                            <IfcCartesianPoint type="IfcCartesianPoint">
                                              <coordinates>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="314.739310246865"/>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                              </coordinates>
                                            </IfcCartesianPoint>
                                            <IfcCartesianPoint type="IfcCartesianPoint">
                                              <coordinates>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="457.685108750143"/>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="177.051077752302"/>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                              </coordinates>
                                            </IfcCartesianPoint>
                                            <IfcCartesianPoint type="IfcCartesianPoint">
                                              <coordinates>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="-385.042810345109"/>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="182.098571627615"/>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="-31.333333333333"/>
                                              </coordinates>
                                            </IfcCartesianPoint>
                                            <IfcCartesianPoint type="IfcCartesianPoint">
                                              <coordinates>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="301.690157997063"/>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="-31.333333333333"/>
                                              </coordinates>
                                            </IfcCartesianPoint>
                                            <IfcCartesianPoint type="IfcCartesianPoint">
                                              <coordinates>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="385.04281034511"/>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="182.098571627617"/>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="-31.333333333333"/>
                                              </coordinates>
                                            </IfcCartesianPoint>
                                            <IfcCartesianPoint type="IfcCartesianPoint">
                                              <coordinates>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="-248.564401002992"/>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="-31.333333333333"/>
                                              </coordinates>
                                            </IfcCartesianPoint>
                                            <IfcCartesianPoint type="IfcCartesianPoint">
                                              <coordinates>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="-385.042810345109"/>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="182.098571627615"/>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="-31.333333333333"/>
                                              </coordinates>
                                            </IfcCartesianPoint>
                                            <IfcCartesianPoint type="IfcCartesianPoint">
                                              <coordinates>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="301.690157997063"/>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="-31.333333333333"/>
                                              </coordinates>
                                            </IfcCartesianPoint>
                                            <IfcCartesianPoint type="IfcCartesianPoint">
                                              <coordinates>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="385.04281034511"/>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="182.098571627617"/>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="-31.333333333333"/>
                                              </coordinates>
                                            </IfcCartesianPoint>
                                            <IfcCartesianPoint type="IfcCartesianPoint">
                                              <coordinates>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="-312.400511940076"/>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="187.146065502931"/>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="-62.666666666666"/>
                                              </coordinates>
                                            </IfcCartesianPoint>
                                            <IfcCartesianPoint type="IfcCartesianPoint">
                                              <coordinates>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="288.64100574726"/>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="-62.666666666666"/>
                                              </coordinates>
                                            </IfcCartesianPoint>
                                            <IfcCartesianPoint type="IfcCartesianPoint">
                                              <coordinates>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="312.400511940078"/>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="187.146065502933"/>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="-62.666666666666"/>
                                              </coordinates>
                                            </IfcCartesianPoint>
                                            <IfcCartesianPoint type="IfcCartesianPoint">
                                              <coordinates>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="-178.348815751603"/>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="-62.6666666666661"/>
                                              </coordinates>
                                            </IfcCartesianPoint>
                                            <IfcCartesianPoint type="IfcCartesianPoint">
                                              <coordinates>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="-312.400511940076"/>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="187.146065502931"/>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="-62.666666666666"/>
                                              </coordinates>
                                            </IfcCartesianPoint>
                                            <IfcCartesianPoint type="IfcCartesianPoint">
                                              <coordinates>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="288.64100574726"/>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="-62.666666666666"/>
                                              </coordinates>
                                            </IfcCartesianPoint>
                                            <IfcCartesianPoint type="IfcCartesianPoint">
                                              <coordinates>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="312.400511940078"/>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="187.146065502933"/>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="-62.666666666666"/>
                                              </coordinates>
                                            </IfcCartesianPoint>
                                            <IfcCartesianPoint type="IfcCartesianPoint">
                                              <coordinates>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="-239.758213535044"/>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="192.193559378247"/>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="-93.9999999999991"/>
                                              </coordinates>
                                            </IfcCartesianPoint>
                                            <IfcCartesianPoint type="IfcCartesianPoint">
                                              <coordinates>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="275.591853497458"/>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="-93.9999999999991"/>
                                              </coordinates>
                                            </IfcCartesianPoint>
                                            <IfcCartesianPoint type="IfcCartesianPoint">
                                              <coordinates>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="239.758213535045"/>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="192.193559378248"/>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="-93.9999999999991"/>
                                              </coordinates>
                                            </IfcCartesianPoint>
                                            <IfcCartesianPoint type="IfcCartesianPoint">
                                              <coordinates>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="-108.133230500215"/>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="-93.9999999999991"/>
                                              </coordinates>
                                            </IfcCartesianPoint>
                                            <IfcCartesianPoint type="IfcCartesianPoint">
                                              <coordinates>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="-239.758213535044"/>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="192.193559378247"/>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="-93.9999999999991"/>
                                              </coordinates>
                                            </IfcCartesianPoint>
                                            <IfcCartesianPoint type="IfcCartesianPoint">
                                              <coordinates>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="275.591853497458"/>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="-93.9999999999991"/>
                                              </coordinates>
                                            </IfcCartesianPoint>
                                            <IfcCartesianPoint type="IfcCartesianPoint">
                                              <coordinates>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="239.758213535045"/>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="192.193559378248"/>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="-93.9999999999991"/>
                                              </coordinates>
                                            </IfcCartesianPoint>
                                          </controlPointsList>
                                          <uClosed type="IfcLogical" value="false"/>
                                          <vClosed type="IfcLogical" value="false"/>
                                          <selfIntersect type="IfcLogical" value="false"/>
                                          <uMultiplicities>
                                            <uMultiplicity type="IfcInteger" value="4"/>
                                            <uMultiplicity type="IfcInteger" value="4"/>
                                          </uMultiplicities>
                                          <vMultiplicities>
                                            <vMultiplicity type="IfcInteger" value="1"/>
                                            <vMultiplicity type="IfcInteger" value="1"/>
                                            <vMultiplicity type="IfcInteger" value="1"/>
                                            <vMultiplicity type="IfcInteger" value="1"/>
                                            <vMultiplicity type="IfcInteger" value="1"/>
                                            <vMultiplicity type="IfcInteger" value="1"/>
                                            <vMultiplicity type="IfcInteger" value="1"/>
                                            <vMultiplicity type="IfcInteger" value="1"/>
                                            <vMultiplicity type="IfcInteger" value="1"/>
                                            <vMultiplicity type="IfcInteger" value="1"/>
                                            <vMultiplicity type="IfcInteger" value="1"/>
                                          </vMultiplicities>
                                          <uKnots>
                                            <uKnot type="IfcParameterValue" value="0.0"/>
                                            <uKnot type="IfcParameterValue" value="15.4213505620632"/>
                                          </uKnots>
                                          <vKnots>
                                            <vKnot type="IfcParameterValue" value="-3.0"/>
                                            <vKnot type="IfcParameterValue" value="-2.0"/>
                                            <vKnot type="IfcParameterValue" value="-1.0"/>
                                            <vKnot type="IfcParameterValue" value="0.0"/>
                                            <vKnot type="IfcParameterValue" value="1.0"/>
                                            <vKnot type="IfcParameterValue" value="2.0"/>
                                            <vKnot type="IfcParameterValue" value="3.0"/>
                                            <vKnot type="IfcParameterValue" value="4.0"/>
                                            <vKnot type="IfcParameterValue" value="5.0"/>
                                            <vKnot type="IfcParameterValue" value="6.0"/>
                                            <vKnot type="IfcParameterValue" value="7.0"/>
                                          </vKnots>
                                        </faceSurface>
                                        <sameSense type="IfcBoolean" value="false"/>
                                      </IfcFace>
                                      <IfcFace type="IfcAdvancedFace">
                                        <bounds>
                                          <IfcFaceBound type="IfcFaceOuterBound">
                                            <bound type="IfcEdgeLoop">
                                              <edgeList>
                                                <IfcOrientedEdge type="IfcOrientedEdge">
                                                  <edgeElement type="IfcEdgeCurve">
                                                    <edgeStart type="IfcVertexPoint">
                                                      <vertexGeometry type="IfcCartesianPoint">
                                                        <coordinates>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="247.792422124388"/>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="-83.9999999999991"/>
                                                        </coordinates>
                                                      </vertexGeometry>
                                                    </edgeStart>
                                                    <edgeEnd type="IfcVertexPoint">
                                                      <vertexGeometry type="IfcCartesianPoint">
                                                        <coordinates>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="247.792422124388"/>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="-83.9999999999991"/>
                                                        </coordinates>
                                                      </vertexGeometry>
                                                    </edgeEnd>
                                                    <edgeGeometry type="IfcBSplineCurveWithKnots" curveForm="UNSPECIFIED">
                                                      <closedCurve type="IfcLogical" value="false"/>
                                                      <selfIntersect type="IfcLogical" value="false"/>
                                                      <knotSpec>UNSPECIFIED</knotSpec>
                                                      <upperIndexOnControlPoints>0</upperIndexOnControlPoints>
                                                      <degree type="IfcInteger" value="3"/>
                                                      <controlPointsList>
                                                        <IfcCartesianPoint type="IfcCartesianPoint">
                                                          <coordinates>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="239.758213537139"/>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="192.193559404919"/>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="-83.9999999999991"/>
                                                          </coordinates>
                                                        </IfcCartesianPoint>
                                                        <IfcCartesianPoint type="IfcCartesianPoint">
                                                          <coordinates>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="275.591853484122"/>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="-83.9999999999991"/>
                                                          </coordinates>
                                                        </IfcCartesianPoint>
                                                        <IfcCartesianPoint type="IfcCartesianPoint">
                                                          <coordinates>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="-239.75821353295"/>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="192.193559404918"/>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="-83.9999999999991"/>
                                                          </coordinates>
                                                        </IfcCartesianPoint>
                                                        <IfcCartesianPoint type="IfcCartesianPoint">
                                                          <coordinates>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="-108.13323051355"/>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="-83.9999999999991"/>
                                                          </coordinates>
                                                        </IfcCartesianPoint>
                                                        <IfcCartesianPoint type="IfcCartesianPoint">
                                                          <coordinates>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="239.758213537139"/>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="192.193559404919"/>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="-83.9999999999991"/>
                                                          </coordinates>
                                                        </IfcCartesianPoint>
                                                        <IfcCartesianPoint type="IfcCartesianPoint">
                                                          <coordinates>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="275.591853484122"/>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="-83.9999999999991"/>
                                                          </coordinates>
                                                        </IfcCartesianPoint>
                                                        <IfcCartesianPoint type="IfcCartesianPoint">
                                                          <coordinates>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="-239.75821353295"/>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="192.193559404918"/>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="-83.9999999999991"/>
                                                          </coordinates>
                                                        </IfcCartesianPoint>
                                                      </controlPointsList>
                                                      <knotMultiplicities>
                                                        <IfcInteger type="IfcInteger" value="1"/>
                                                        <IfcInteger type="IfcInteger" value="1"/>
                                                        <IfcInteger type="IfcInteger" value="1"/>
                                                        <IfcInteger type="IfcInteger" value="1"/>
                                                        <IfcInteger type="IfcInteger" value="1"/>
                                                        <IfcInteger type="IfcInteger" value="1"/>
                                                        <IfcInteger type="IfcInteger" value="1"/>
                                                        <IfcInteger type="IfcInteger" value="1"/>
                                                        <IfcInteger type="IfcInteger" value="1"/>
                                                        <IfcInteger type="IfcInteger" value="1"/>
                                                        <IfcInteger type="IfcInteger" value="1"/>
                                                      </knotMultiplicities>
                                                      <knots>
                                                        <IfcParameterValue type="IfcParameterValue" value="-7.0"/>
                                                        <IfcParameterValue type="IfcParameterValue" value="-6.0"/>
                                                        <IfcParameterValue type="IfcParameterValue" value="-5.0"/>
                                                        <IfcParameterValue type="IfcParameterValue" value="-4.0"/>
                                                        <IfcParameterValue type="IfcParameterValue" value="-3.0"/>
                                                        <IfcParameterValue type="IfcParameterValue" value="-2.0"/>
                                                        <IfcParameterValue type="IfcParameterValue" value="-1.0"/>
                                                        <IfcParameterValue type="IfcParameterValue" value="0.0"/>
                                                        <IfcParameterValue type="IfcParameterValue" value="1.0"/>
                                                        <IfcParameterValue type="IfcParameterValue" value="2.0"/>
                                                        <IfcParameterValue type="IfcParameterValue" value="3.0"/>
                                                      </knots>
                                                    </edgeGeometry>
                                                    <sameSense type="IfcBoolean" value="false"/>
                                                  </edgeElement>
                                                  <orientation type="IfcBoolean" value="false"/>
                                                </IfcOrientedEdge>
                                              </edgeList>
                                            </bound>
                                            <orientation type="IfcBoolean" value="false"/>
                                          </IfcFaceBound>
                                        </bounds>
                                        <faceSurface type="IfcPlane">
                                          <position type="IfcAxis2Placement3D">
                                            <location type="IfcCartesianPoint">
                                              <coordinates>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="247.792422124388"/>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="-83.9999999999991"/>
                                              </coordinates>
                                            </location>
                                          </position>
                                        </faceSurface>
                                        <sameSense type="IfcBoolean" value="false"/>
                                      </IfcFace>
                                      <IfcFace type="IfcAdvancedFace">
                                        <bounds>
                                          <IfcFaceBound type="IfcFaceOuterBound">
                                            <bound type="IfcEdgeLoop">
                                              <edgeList>
                                                <IfcOrientedEdge type="IfcOrientedEdge">
                                                  <edgeElement type="IfcEdgeCurve">
                                                    <edgeStart type="IfcVertexPoint">
                                                      <vertexGeometry type="IfcCartesianPoint">
                                                        <coordinates>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="268.843232748677"/>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                        </coordinates>
                                                      </vertexGeometry>
                                                    </edgeStart>
                                                    <edgeEnd type="IfcVertexPoint">
                                                      <vertexGeometry type="IfcCartesianPoint">
                                                        <coordinates>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="268.843232748677"/>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                        </coordinates>
                                                      </vertexGeometry>
                                                    </edgeEnd>
                                                    <edgeGeometry type="IfcBSplineCurveWithKnots" curveForm="UNSPECIFIED">
                                                      <closedCurve type="IfcLogical" value="false"/>
                                                      <selfIntersect type="IfcLogical" value="false"/>
                                                      <knotSpec>UNSPECIFIED</knotSpec>
                                                      <upperIndexOnControlPoints>0</upperIndexOnControlPoints>
                                                      <degree type="IfcInteger" value="3"/>
                                                      <controlPointsList>
                                                        <IfcCartesianPoint type="IfcCartesianPoint">
                                                          <coordinates>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="457.685108750143"/>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="177.051077752302"/>
                                                          </coordinates>
                                                        </IfcCartesianPoint>
                                                        <IfcCartesianPoint type="IfcCartesianPoint">
                                                          <coordinates>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="314.739310246865"/>
                                                          </coordinates>
                                                        </IfcCartesianPoint>
                                                        <IfcCartesianPoint type="IfcCartesianPoint">
                                                          <coordinates>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="-457.685108750141"/>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="177.051077752299"/>
                                                          </coordinates>
                                                        </IfcCartesianPoint>
                                                        <IfcCartesianPoint type="IfcCartesianPoint">
                                                          <coordinates>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="-318.77998625438"/>
                                                          </coordinates>
                                                        </IfcCartesianPoint>
                                                        <IfcCartesianPoint type="IfcCartesianPoint">
                                                          <coordinates>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="457.685108750143"/>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="177.051077752302"/>
                                                          </coordinates>
                                                        </IfcCartesianPoint>
                                                        <IfcCartesianPoint type="IfcCartesianPoint">
                                                          <coordinates>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="314.739310246865"/>
                                                          </coordinates>
                                                        </IfcCartesianPoint>
                                                        <IfcCartesianPoint type="IfcCartesianPoint">
                                                          <coordinates>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="-457.685108750141"/>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="177.051077752299"/>
                                                          </coordinates>
                                                        </IfcCartesianPoint>
                                                      </controlPointsList>
                                                      <knotMultiplicities>
                                                        <IfcInteger type="IfcInteger" value="1"/>
                                                        <IfcInteger type="IfcInteger" value="1"/>
                                                        <IfcInteger type="IfcInteger" value="1"/>
                                                        <IfcInteger type="IfcInteger" value="1"/>
                                                        <IfcInteger type="IfcInteger" value="1"/>
                                                        <IfcInteger type="IfcInteger" value="1"/>
                                                        <IfcInteger type="IfcInteger" value="1"/>
                                                        <IfcInteger type="IfcInteger" value="1"/>
                                                        <IfcInteger type="IfcInteger" value="1"/>
                                                        <IfcInteger type="IfcInteger" value="1"/>
                                                        <IfcInteger type="IfcInteger" value="1"/>
                                                      </knotMultiplicities>
                                                      <knots>
                                                        <IfcParameterValue type="IfcParameterValue" value="-7.0"/>
                                                        <IfcParameterValue type="IfcParameterValue" value="-6.0"/>
                                                        <IfcParameterValue type="IfcParameterValue" value="-5.0"/>
                                                        <IfcParameterValue type="IfcParameterValue" value="-4.0"/>
                                                        <IfcParameterValue type="IfcParameterValue" value="-3.0"/>
                                                        <IfcParameterValue type="IfcParameterValue" value="-2.0"/>
                                                        <IfcParameterValue type="IfcParameterValue" value="-1.0"/>
                                                        <IfcParameterValue type="IfcParameterValue" value="0.0"/>
                                                        <IfcParameterValue type="IfcParameterValue" value="1.0"/>
                                                        <IfcParameterValue type="IfcParameterValue" value="2.0"/>
                                                        <IfcParameterValue type="IfcParameterValue" value="3.0"/>
                                                      </knots>
                                                    </edgeGeometry>
                                                    <sameSense type="IfcBoolean" value="false"/>
                                                  </edgeElement>
                                                  <orientation type="IfcBoolean" value="false"/>
                                                </IfcOrientedEdge>
                                              </edgeList>
                                            </bound>
                                            <orientation type="IfcBoolean" value="false"/>
                                          </IfcFaceBound>
                                          <IfcFaceBound type="IfcFaceBound">
                                            <bound type="IfcEdgeLoop">
                                              <edgeList>
                                                <IfcOrientedEdge type="IfcOrientedEdge">
                                                  <edgeElement type="IfcEdgeCurve">
                                                    <edgeStart type="IfcVertexPoint">
                                                      <vertexGeometry type="IfcCartesianPoint">
                                                        <coordinates>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="253.099263998677"/>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                        </coordinates>
                                                      </vertexGeometry>
                                                    </edgeStart>
                                                    <edgeEnd type="IfcVertexPoint">
                                                      <vertexGeometry type="IfcCartesianPoint">
                                                        <coordinates>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="253.099263998677"/>
                                                          <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                        </coordinates>
                                                      </vertexGeometry>
                                                    </edgeEnd>
                                                    <edgeGeometry type="IfcBSplineCurveWithKnots" curveForm="UNSPECIFIED">
                                                      <closedCurve type="IfcLogical" value="false"/>
                                                      <selfIntersect type="IfcLogical" value="false"/>
                                                      <knotSpec>UNSPECIFIED</knotSpec>
                                                      <upperIndexOnControlPoints>0</upperIndexOnControlPoints>
                                                      <degree type="IfcInteger" value="3"/>
                                                      <controlPointsList>
                                                        <IfcCartesianPoint type="IfcCartesianPoint">
                                                          <coordinates>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="-437.751000004175"/>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="168.150654933496"/>
                                                          </coordinates>
                                                        </IfcCartesianPoint>
                                                        <IfcCartesianPoint type="IfcCartesianPoint">
                                                          <coordinates>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="295.573568531267"/>
                                                          </coordinates>
                                                        </IfcCartesianPoint>
                                                        <IfcCartesianPoint type="IfcCartesianPoint">
                                                          <coordinates>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="437.751000006541"/>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="168.150654933498"/>
                                                          </coordinates>
                                                        </IfcCartesianPoint>
                                                        <IfcCartesianPoint type="IfcCartesianPoint">
                                                          <coordinates>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="-290.713822148428"/>
                                                          </coordinates>
                                                        </IfcCartesianPoint>
                                                        <IfcCartesianPoint type="IfcCartesianPoint">
                                                          <coordinates>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="-437.751000004175"/>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="168.150654933496"/>
                                                          </coordinates>
                                                        </IfcCartesianPoint>
                                                        <IfcCartesianPoint type="IfcCartesianPoint">
                                                          <coordinates>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="295.573568531267"/>
                                                          </coordinates>
                                                        </IfcCartesianPoint>
                                                        <IfcCartesianPoint type="IfcCartesianPoint">
                                                          <coordinates>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="437.751000006541"/>
                                                            <IfcLengthMeasure type="IfcLengthMeasure" value="168.150654933498"/>
                                                          </coordinates>
                                                        </IfcCartesianPoint>
                                                      </controlPointsList>
                                                      <knotMultiplicities>
                                                        <IfcInteger type="IfcInteger" value="1"/>
                                                        <IfcInteger type="IfcInteger" value="1"/>
                                                        <IfcInteger type="IfcInteger" value="1"/>
                                                        <IfcInteger type="IfcInteger" value="1"/>
                                                        <IfcInteger type="IfcInteger" value="1"/>
                                                        <IfcInteger type="IfcInteger" value="1"/>
                                                        <IfcInteger type="IfcInteger" value="1"/>
                                                        <IfcInteger type="IfcInteger" value="1"/>
                                                        <IfcInteger type="IfcInteger" value="1"/>
                                                        <IfcInteger type="IfcInteger" value="1"/>
                                                        <IfcInteger type="IfcInteger" value="1"/>
                                                      </knotMultiplicities>
                                                      <knots>
                                                        <IfcParameterValue type="IfcParameterValue" value="-7.0"/>
                                                        <IfcParameterValue type="IfcParameterValue" value="-6.0"/>
                                                        <IfcParameterValue type="IfcParameterValue" value="-5.0"/>
                                                        <IfcParameterValue type="IfcParameterValue" value="-4.0"/>
                                                        <IfcParameterValue type="IfcParameterValue" value="-3.0"/>
                                                        <IfcParameterValue type="IfcParameterValue" value="-2.0"/>
                                                        <IfcParameterValue type="IfcParameterValue" value="-1.0"/>
                                                        <IfcParameterValue type="IfcParameterValue" value="0.0"/>
                                                        <IfcParameterValue type="IfcParameterValue" value="1.0"/>
                                                        <IfcParameterValue type="IfcParameterValue" value="2.0"/>
                                                        <IfcParameterValue type="IfcParameterValue" value="3.0"/>
                                                      </knots>
                                                    </edgeGeometry>
                                                    <sameSense type="IfcBoolean" value="false"/>
                                                  </edgeElement>
                                                  <orientation type="IfcBoolean" value="false"/>
                                                </IfcOrientedEdge>
                                              </edgeList>
                                            </bound>
                                            <orientation type="IfcBoolean" value="false"/>
                                          </IfcFaceBound>
                                        </bounds>
                                        <faceSurface type="IfcPlane">
                                          <position type="IfcAxis2Placement3D">
                                            <location type="IfcCartesianPoint">
                                              <coordinates>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="253.099263998677"/>
                                                <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                              </coordinates>
                                            </location>
                                          </position>
                                        </faceSurface>
                                        <sameSense type="IfcBoolean" value="false"/>
                                      </IfcFace>
                                    </cfsFaces>
                                  </outer>
                                </IfcRepresentationItem>
                              </items>
                            </mappedRepresentation>
                          </mappingSource>
                          <mappingTarget type="IfcCartesianTransformationOperator3D">
                            <axis1 type="IfcDirection">
                              <directionRatios>
                                <IfcReal type="IfcReal" value="1.0"/>
                                <IfcReal type="IfcReal" value="0.0"/>
                                <IfcReal type="IfcReal" value="0.0"/>
                              </directionRatios>
                            </axis1>
                            <axis2 type="IfcDirection">
                              <directionRatios>
                                <IfcReal type="IfcReal" value="0.0"/>
                                <IfcReal type="IfcReal" value="1.0"/>
                                <IfcReal type="IfcReal" value="0.0"/>
                              </directionRatios>
                            </axis2>
                            <localOrigin type="IfcCartesianPoint">
                              <coordinates>
                                <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                              </coordinates>
                            </localOrigin>
                            <scale type="IfcReal" value="1.0"/>
                            <axis3 type="IfcDirection">
                              <directionRatios>
                                <IfcReal type="IfcReal" value="0.0"/>
                                <IfcReal type="IfcReal" value="0.0"/>
                                <IfcReal type="IfcReal" value="1.0"/>
                              </directionRatios>
                            </axis3>
                          </mappingTarget>
                        </IfcRepresentationItem>
                      </items>
                    </IfcRepresentation>
                  </representations>
                </representation>
              </IfcProduct>
            </relatedElements>
          </IfcRelContainedInSpatialStructure>
        </containsElements>
        <buildingAddress type="IfcPostalAddress">
          <region type="IfcLabel" value="Unknown"/>
        </buildingAddress>
      </IfcObjectDefinition>
    </relatedObjects>
  </isDecomposedBy>
  <longName type="IfcLabel" value="IfcProject"/>
  <phase type="IfcLabel"/>
  <representationContexts>
    <IfcRepresentationContext type="IfcGeometricRepresentationContext" globalId="b0cf1edb-be02-478f-a84f-fd08da430d63">
      <worldCoordinateSystem type="IfcAxis2Placement3D">
        <location type="IfcCartesianPoint">
          <coordinates>
            <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
            <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
            <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
          </coordinates>
        </location>
      </worldCoordinateSystem>
      <contextType type="IfcLabel" value="Model"/>
      <coordinateSpaceDimension type="IfcDimensionCount" value="3"/>
      <precision type="IfcReal" value="1.0E-4"/>
      <trueNorth type="IfcDirection">
        <directionRatios>
          <IfcReal type="IfcReal" value="0.0"/>
          <IfcReal type="IfcReal" value="1.0"/>
        </directionRatios>
      </trueNorth>
    </IfcRepresentationContext>
  </representationContexts>
  <unitsInContext>
    <units>
      <IfcUnit type="IfcSIUnit" globalId="4db338a4-b3b0-4cfc-b537-12cf743a6a63">
        <dimensions lengthExponent="1" massExponent="0" timeExponent="0" electricCurrentExponent="0" thermodynamicTemperatureExponent="0" amountOfSubstanceExponent="0" luminousIntensityExponent="0"/>
        <unitType>LENGTHUNIT</unitType>
        <prefix>MILLI</prefix>
        <name>METRE</name>
      </IfcUnit>
      <IfcUnit type="IfcSIUnit" globalId="4ec93437-a327-4bee-9b10-39877b6eb192">
        <dimensions lengthExponent="0" massExponent="0" timeExponent="1" electricCurrentExponent="0" thermodynamicTemperatureExponent="0" amountOfSubstanceExponent="0" luminousIntensityExponent="0"/>
        <unitType>TIMEUNIT</unitType>
        <name>SECOND</name>
      </IfcUnit>
      <IfcUnit type="IfcSIUnit" globalId="38645cc0-6b49-48c6-a930-9222f46c038a">
        <dimensions lengthExponent="0" massExponent="0" timeExponent="0" electricCurrentExponent="0" thermodynamicTemperatureExponent="0" amountOfSubstanceExponent="0" luminousIntensityExponent="0"/>
        <unitType>PLANEANGLEUNIT</unitType>
        <name>RADIAN</name>
      </IfcUnit>
    </units>
  </unitsInContext>
</IfcProject>

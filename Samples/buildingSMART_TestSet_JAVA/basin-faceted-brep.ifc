ISO-10303-21;
HEADER;
FILE_DESCRIPTION(('ViewDefinition [notYetAssigned]'),'2;1');
FILE_NAME(
/* name */ '',
/* time_stamp */ '2014-12-09T00:27:55',
/* author */ ('Jon'),
/* organization */ ('Unknown'),
/* preprocessor_version */ 'ggIFC - Exporter by Geometry Gym Pty Ltd',
/* originating_system */ 'ggIFC - Exporter by Geometry Gym Pty Ltd',
/* authorization */ 'None');

FILE_SCHEMA (('IFC4'));
ENDSEC;

DATA;
#1= IFCAPPLICATION(#2,'*******','ggRhinoIFC - Geometry Gym Plug-in for Rhino3d','ggRhinoIFC');
#2= IFCORGANIZATION($,'Geometry Gym Pty Ltd',$,$,$);
#3= IFCPERSONANDORGANIZATION(#4,#5,$);
#4= IFCPERSON('Jon','Jon',$,$,$,$,$,$);
#5= IFCORGANIZATION($,'Geometry Gym Pty Ltd',$,$,$);
#6= IFCOWNERHISTORY(#3,#1,$,.ADDED.,1418084875,$,$,1418084875);
#7= IFCGEOMETRICREPRESENTATIONCONTEXT($,'Model',3,0.0001,#8,#10);
#8= IFCAXIS2PLACEMENT3D(#9,$,$);
#9= IFCCARTESIANPOINT((0.0,0.0,0.0));
#10= IFCDIRECTION((0.0,1.0));
#11= IFCGEOMETRICREPRESENTATIONSUBCONTEXT('Axis','Model',*,*,*,*,#7,$,.MODEL_VIEW.,$);
#12= IFCGEOMETRICREPRESENTATIONSUBCONTEXT('Body','Model',*,*,*,*,#7,$,.MODEL_VIEW.,$);
#13= IFCGEOMETRICREPRESENTATIONCONTEXT($,'Model',3,0.0001,#8,#14);
#14= IFCDIRECTION((0.0,1.0));
#50= IFCBUILDING('2UrSelLcv7p8sV8DUl$XR_',$,'IfcBuilding',$,$,#51,$,$,.ELEMENT.,$,$,#57);
#51= IFCLOCALPLACEMENT($,#52);
#52= IFCAXIS2PLACEMENT3D(#53,$,$);
#53= IFCCARTESIANPOINT((0.0,0.0,0.0));
#54= IFCRELCONTAINEDINSPATIALSTRUCTURE('1nLCX25sz2RPQ379yFIRFo',$,'Building','Building Container for Elements',(#868),#50);
#55= IFCLOCALPLACEMENT(#51,#52);
#57= IFCPOSTALADDRESS($,$,$,$,$,$,$,'Unknown',$,$);
#100= IFCPROJECT('0HXpBSJeXCieb8ZCRZtdyz',#6,'IfcProject',$,$,'IfcProject','',(#13),#101);
#101= IFCUNITASSIGNMENT((#102,#103,#104));
#102= IFCSIUNIT(*,.LENGTHUNIT.,.MILLI.,.METRE.);
#103= IFCSIUNIT(*,.PLANEANGLEUNIT.,$,.RADIAN.);
#104= IFCSIUNIT(*,.TIMEUNIT.,$,.SECOND.);
#105= IFCRELAGGREGATES('0uZ92lKhT6EwklfgWpFFGq',$,'Project Container','Project Container for Buildings',#100,(#50));
#200= IFCCARTESIANPOINT((-300.0,150.0,0.0));
#201= IFCCARTESIANPOINT((0.0,253.099263998677,0.0));
#202= IFCCARTESIANPOINT((-304.891071307496,109.822256120206,0.0));
#203= IFCCARTESIANPOINT((-296.940075211924,69.9862810835965,0.0));
#204= IFCCARTESIANPOINT((-280.922701731312,32.5907488402735,0.0));
#205= IFCCARTESIANPOINT((-259.608278768049,-2.08529338102262,0.0));
#206= IFCCARTESIANPOINT((-234.428168068557,-34.0753473673235,0.0));
#207= IFCCARTESIANPOINT((-206.125816386308,-63.3430855287523,0.0));
#208= IFCCARTESIANPOINT((-175.071534188435,-89.671802213621,0.0));
#209= IFCCARTESIANPOINT((-141.412934526735,-112.569598180375,0.0));
#210= IFCCARTESIANPOINT((-105.215114341225,-131.178510124149,0.0));
#211= IFCCARTESIANPOINT((-66.7978479682479,-144.600522500239,0.0));
#212= IFCCARTESIANPOINT((-26.8019125425913,-152.078206375312,0.0));
#213= IFCCARTESIANPOINT((13.8710862775596,-153.121660363236,0.0));
#214= IFCCARTESIANPOINT((54.1903326227934,-147.658103330519,0.0));
#215= IFCCARTESIANPOINT((93.1938862047157,-136.056391059388,0.0));
#216= IFCCARTESIANPOINT((130.150407237097,-119.008883827061,0.0));
#217= IFCCARTESIANPOINT((164.629900262907,-97.37165169902,0.0));
#218= IFCCARTESIANPOINT((196.521914802755,-72.0656434367093,0.0));
#219= IFCCARTESIANPOINT((225.728273711876,-43.6997637700286,0.0));
#220= IFCCARTESIANPOINT((251.961645534009,-12.5658965535741,0.0));
#221= IFCCARTESIANPOINT((274.62087031373,21.2509318856294,0.0));
#222= IFCCARTESIANPOINT((292.544561947164,57.7814080324017,0.0));
#223= IFCCARTESIANPOINT((303.485368351088,96.9291090994897,0.0));
#224= IFCCARTESIANPOINT((303.288957242437,137.460278743209,0.0));
#225= IFCCARTESIANPOINT((287.369755242598,174.609406522514,0.0));
#226= IFCCARTESIANPOINT((258.965081713391,203.584218912768,0.0));
#227= IFCCARTESIANPOINT((224.363000998313,224.953681400811,0.0));
#228= IFCCARTESIANPOINT((186.818017180943,240.67163971588,0.0));
#229= IFCCARTESIANPOINT((147.756154156487,252.155742473922,0.0));
#230= IFCCARTESIANPOINT((107.854433144861,260.280582237886,0.0));
#231= IFCCARTESIANPOINT((67.4765332607867,265.571769764524,0.0));
#232= IFCCARTESIANPOINT((26.8458427708863,268.331827033896,0.0));
#233= IFCCARTESIANPOINT((-13.8771780303431,268.706812331913,0.0));
#234= IFCCARTESIANPOINT((-54.5530293999268,266.715969920949,0.0));
#235= IFCCARTESIANPOINT((-95.0316202590492,262.255497380798,0.0));
#236= IFCCARTESIANPOINT((-135.115612880362,255.077835581894,0.0));
#237= IFCCARTESIANPOINT((-174.498313981775,244.738726835566,0.0));
#238= IFCCARTESIANPOINT((-212.631973130034,230.493081814022,0.0));
#239= IFCCARTESIANPOINT((-248.405687646327,211.118276332925,0.0));
#240= IFCCARTESIANPOINT((-279.319862190373,184.775801315119,0.0));
#241= IFCCARTESIANPOINT((38.4756983219429,252.033478287557,0.0));
#242= IFCCARTESIANPOINT((76.82797079471,248.781505427916,0.0));
#243= IFCCARTESIANPOINT((114.905040261981,243.168611097888,0.0));
#244= IFCCARTESIANPOINT((152.483825019629,234.862972697005,0.0));
#245= IFCCARTESIANPOINT((189.184260308893,223.297735395069,0.0));
#246= IFCCARTESIANPOINT((224.265826794532,207.523990642274,0.0));
#247= IFCCARTESIANPOINT((256.087669717028,185.991172830599,0.0));
#248= IFCCARTESIANPOINT((280.765071736094,156.699063336262,0.0));
#249= IFCCARTESIANPOINT((291.585141614082,120.093338245881,0.0));
#250= IFCCARTESIANPOINT((287.786485451514,81.9448662146907,0.0));
#251= IFCCARTESIANPOINT((274.528157897687,45.8738463140147,0.0));
#252= IFCCARTESIANPOINT((255.376512280737,12.5180862102588,0.0));
#253= IFCCARTESIANPOINT((232.162082007755,-18.1641105843033,0.0));
#254= IFCCARTESIANPOINT((205.82058249047,-46.2135089189517,0.0));
#255= IFCCARTESIANPOINT((176.831543285532,-71.5181976728152,0.0));
#256= IFCCARTESIANPOINT((145.419108882987,-93.7387882260769,0.0));
#257= IFCCARTESIANPOINT((111.672515835794,-112.209605260782,0.0));
#258= IFCCARTESIANPOINT((75.812198248967,-126.124204222316,0.0));
#259= IFCCARTESIANPOINT((38.3440539351622,-134.804771445296,0.0));
#260= IFCCARTESIANPOINT((0.00000562489231925,-137.758996454453,0.0));
#261= IFCCARTESIANPOINT((-38.3440494938959,-134.804772131387,0.0));
#262= IFCCARTESIANPOINT((-75.8121974100292,-126.124204482417,0.0));
#263= IFCCARTESIANPOINT((-111.672515690142,-112.209605328934,0.0));
#264= IFCCARTESIANPOINT((-145.419108622881,-93.7387883895915,0.0));
#265= IFCCARTESIANPOINT((-176.831543103321,-71.5181978165611,0.0));
#266= IFCCARTESIANPOINT((-205.820584751455,-46.2135067401479,0.0));
#267= IFCCARTESIANPOINT((-232.162080231681,-18.164112680286,0.0));
#268= IFCCARTESIANPOINT((-255.37651255073,12.5180866141667,0.0));
#269= IFCCARTESIANPOINT((-274.528157992219,45.8738465115148,0.0));
#270= IFCCARTESIANPOINT((-287.786482832935,81.9448557189429,0.0));
#271= IFCCARTESIANPOINT((-291.585141527273,120.093339480335,0.0));
#272= IFCCARTESIANPOINT((-280.765069841138,156.69906670217,0.0));
#273= IFCCARTESIANPOINT((-256.087676107765,185.991167334279,0.0));
#274= IFCCARTESIANPOINT((-224.265825523088,207.523991330957,0.0));
#275= IFCCARTESIANPOINT((-189.184272565133,223.297730817421,0.0));
#276= IFCCARTESIANPOINT((-152.483829695455,234.862971464014,0.0));
#277= IFCCARTESIANPOINT((-114.905033181042,243.168612385642,0.0));
#278= IFCCARTESIANPOINT((-76.8279738129532,248.781505081298,0.0));
#279= IFCCARTESIANPOINT((-38.4757029862493,252.0334780278,0.0));
#280= IFCCARTESIANPOINT((-157.154914340418,175.808617178122,-93.9999999999991));
#281= IFCCARTESIANPOINT((-159.799786377107,153.46782071584,-93.9999999999991));
#282= IFCCARTESIANPOINT((-156.563435238971,131.153861277019,-93.9999999999991));
#283= IFCCARTESIANPOINT((-149.376131896764,109.760102145713,-93.9999999999991));
#284= IFCCARTESIANPOINT((-139.405144871723,89.5038839334345,-93.9999999999991));
#285= IFCCARTESIANPOINT((-127.294335499358,70.445523587566,-93.9999999999991));
#286= IFCCARTESIANPOINT((-113.387459490709,52.6538727330131,-93.9999999999991));
#287= IFCCARTESIANPOINT((-97.8447661146846,36.2725918775068,-93.9999999999991));
#288= IFCCARTESIANPOINT((-80.698001162248,21.582584637739,-93.9999999999991));
#289= IFCCARTESIANPOINT((-61.8871867593298,9.10501350136987,-93.9999999999991));
#290= IFCCARTESIANPOINT((-41.4394014166823,-0.440153063918815,-93.9999999999991));
#291= IFCCARTESIANPOINT((-19.662998563232,-6.32985285628291,-93.9999999999991));
#292= IFCCARTESIANPOINT((2.83012817778957,-7.98927385674093,-93.9999999999991));
#293= IFCCARTESIANPOINT((25.2161298976784,-5.23363177905661,-93.9999999999991));
#294= IFCCARTESIANPOINT((46.7052656338443,1.63611866403177,-93.9999999999991));
#295= IFCCARTESIANPOINT((66.7603568037461,11.9848882796374,-93.9999999999991));
#296= IFCCARTESIANPOINT((85.1475914236449,25.0817028555501,-93.9999999999991));
#297= IFCCARTESIANPOINT((101.889510015946,40.2328202653944,-93.9999999999991));
#298= IFCCARTESIANPOINT((117.029708390029,56.9871779332136,-93.9999999999991));
#299= IFCCARTESIANPOINT((130.507477385555,75.1059075118196,-93.9999999999991));
#300= IFCCARTESIANPOINT((142.123444996071,94.4691487187488,-93.9999999999991));
#301= IFCCARTESIANPOINT((151.473017550452,115.017962619083,-93.9999999999991));
#302= IFCCARTESIANPOINT((157.807019966901,136.675894195075,-93.9999999999991));
#303= IFCCARTESIANPOINT((159.780564770642,159.127525554679,-93.9999999999991));
#304= IFCCARTESIANPOINT((155.311843754385,181.158856579337,-93.9999999999991));
#305= IFCCARTESIANPOINT((143.527109004701,200.308104946409,-93.9999999999991));
#306= IFCCARTESIANPOINT((126.728646947386,215.328663366711,-93.9999999999991));
#307= IFCCARTESIANPOINT((107.202467334812,226.634520930962,-93.9999999999991));
#308= IFCCARTESIANPOINT((86.2268790106761,234.98230335346,-93.9999999999991));
#309= IFCCARTESIANPOINT((64.4507343905464,240.958334023702,-93.9999999999991));
#310= IFCCARTESIANPOINT((42.2218098296953,244.946818741202,-93.9999999999991));
#311= IFCCARTESIANPOINT((19.7473348710258,247.180277362528,-93.9999999999991));
#312= IFCCARTESIANPOINT((-2.83037829161053,247.779902642456,-93.9999999999991));
#313= IFCCARTESIANPOINT((-25.3937160660832,246.777117598331,-93.9999999999991));
#314= IFCCARTESIANPOINT((-47.8217104720131,244.119106659353,-93.9999999999991));
#315= IFCCARTESIANPOINT((-69.9604240039862,239.659498513825,-93.9999999999991));
#316= IFCCARTESIANPOINT((-91.576683901205,233.132310979361,-93.9999999999991));
#317= IFCCARTESIANPOINT((-112.267364103545,224.107056891456,-93.9999999999991));
#318= IFCCARTESIANPOINT((-131.264894314891,211.943402767825,-93.9999999999991));
#319= IFCCARTESIANPOINT((-147.061746316906,195.888143420344,-93.9999999999991));
#320= IFCCARTESIANPOINT((0.0,247.792422124388,-83.9999999999991));
#321= IFCCARTESIANPOINT((-22.5714426203723,246.991542511388,-83.9999999999991));
#322= IFCCARTESIANPOINT((-45.0238143941789,244.546840992317,-83.9999999999991));
#323= IFCCARTESIANPOINT((-67.2093088278616,240.324720105693,-83.9999999999991));
#324= IFCCARTESIANPOINT((-88.9083392880943,234.076268171425,-83.9999999999991));
#325= IFCCARTESIANPOINT((-109.746783760019,225.394569776618,-83.9999999999991));
#326= IFCCARTESIANPOINT((-129.019214178155,213.666092805292,-83.9999999999991));
#327= IFCCARTESIANPOINT((-145.336255413146,198.131561931792,-83.9999999999991));
#328= IFCCARTESIANPOINT((-156.295574267743,178.505166800292,-83.9999999999991));
#329= IFCCARTESIANPOINT((-159.838406313243,156.297849730896,-83.9999999999991));
#330= IFCCARTESIANPOINT((-157.217231378449,133.907674702395,-83.9999999999991));
#331= IFCCARTESIANPOINT((-150.446948503868,112.380106954109,-83.9999999999991));
#332= IFCCARTESIANPOINT((-140.781370993691,91.9771671791993,-83.9999999999991));
#333= IFCCARTESIANPOINT((-128.915130782292,72.7659081552236,-83.9999999999991));
#334= IFCCARTESIANPOINT((-115.221432502533,54.8097171953257,-83.9999999999991));
#335= IFCCARTESIANPOINT((-99.8796470071603,38.239929890392,-83.9999999999991));
#336= IFCCARTESIANPOINT((-82.9357654214438,23.3156500424069,-83.9999999999991));
#337= IFCCARTESIANPOINT((-64.3369337417203,10.5226649501159,-83.9999999999991));
#338= IFCCARTESIANPOINT((-44.0834019165823,0.569910981625322,-83.9999999999991));
#339= IFCCARTESIANPOINT((-22.4460832273862,-5.81475796938246,-83.9999999999991));
#340= IFCCARTESIANPOINT((0.00000176219393597,-8.0243005407274,-83.9999999999991));
#341= IFCCARTESIANPOINT((22.446084481302,-5.81475772180151,-83.9999999999991));
#342= IFCCARTESIANPOINT((44.0834024872804,0.569911206685156,-83.9999999999991));
#343= IFCCARTESIANPOINT((64.3369357795818,10.5226661545967,-83.9999999999991));
#344= IFCCARTESIANPOINT((82.9357630974296,23.3156482145766,-83.9999999999991));
#345= IFCCARTESIANPOINT((99.8796474348905,38.2399303092091,-83.9999999999991));
#346= IFCCARTESIANPOINT((115.221437914664,54.8097236343718,-83.9999999999991));
#347= IFCCARTESIANPOINT((128.915131219959,72.7659087899957,-83.9999999999991));
#348= IFCCARTESIANPOINT((140.781372023645,91.9771690603084,-83.9999999999991));
#349= IFCCARTESIANPOINT((150.44694977305,112.380110135675,-83.9999999999991));
#350= IFCCARTESIANPOINT((157.217230633118,133.907671395464,-83.9999999999991));
#351= IFCCARTESIANPOINT((159.838406320187,156.297847499286,-83.9999999999991));
#352= IFCCARTESIANPOINT((156.295574340587,178.505166588871,-83.9999999999991));
#353= IFCCARTESIANPOINT((145.336254078924,198.131563599338,-83.9999999999991));
#354= IFCCARTESIANPOINT((129.019208284081,213.666097201998,-83.9999999999991));
#355= IFCCARTESIANPOINT((109.746785929842,225.394568694178,-83.9999999999991));
#356= IFCCARTESIANPOINT((88.90833880495,234.07626833846,-83.9999999999991));
#357= IFCCARTESIANPOINT((67.2093069026739,240.324720559443,-83.9999999999991));
#358= IFCCARTESIANPOINT((45.0238122743352,244.546841305597,-83.9999999999991));
#359= IFCCARTESIANPOINT((22.5714426167356,246.991542511648,-83.9999999999991));
#360= IFCPOLYLOOP((#202,#203,#204,#205,#206,#207,#208,#209,#210,#211,#212,#213,#214,#215,#216,#217,#218,#219,#220,#221,#222,#223,#224,#225,#226,#227,#228,#229,#230,#231,#232,#233,#234,#235,#236,#237,#238,#239,#240));
#361= IFCFACEOUTERBOUND(#360,.T.);
#362= IFCPOLYLOOP((#241,#242,#243,#244,#245,#246,#247,#248,#249,#250,#251,#252,#253,#254,#255,#256,#257,#258,#259,#260,#261,#262,#263,#264,#265,#266,#267,#268,#269,#270,#271,#272,#273,#274,#275,#276,#277,#278,#279,#201));
#363= IFCFACEBOUND(#362,.T.);
#364= IFCFACE((#361,#363));
#365= IFCPOLYLOOP((#281,#282,#283,#284,#285,#286,#287,#288,#289,#290,#291,#292,#293,#294,#295,#296,#297,#298,#299,#300,#301,#302,#303,#304,#305,#306,#307,#308,#309,#310,#311,#312,#313,#314,#315,#316,#317,#318,#319,#280));
#366= IFCFACEOUTERBOUND(#365,.T.);
#367= IFCFACE((#366));
#368= IFCPOLYLOOP((#321,#322,#323,#324,#325,#326,#327,#328,#329,#330,#331,#332,#333,#334,#335,#336,#337,#338,#339,#340,#341,#342,#343,#344,#345,#346,#347,#348,#349,#350,#351,#352,#353,#354,#355,#356,#357,#358,#359,#320));
#369= IFCFACEOUTERBOUND(#368,.T.);
#370= IFCFACE((#369));
#371= IFCPOLYLOOP((#200,#319,#280));
#372= IFCFACEOUTERBOUND(#371,.T.);
#373= IFCFACE((#372));
#374= IFCPOLYLOOP((#240,#318,#319));
#375= IFCFACEOUTERBOUND(#374,.T.);
#376= IFCFACE((#375));
#377= IFCPOLYLOOP((#239,#317,#318));
#378= IFCFACEOUTERBOUND(#377,.T.);
#379= IFCFACE((#378));
#380= IFCPOLYLOOP((#238,#316,#317));
#381= IFCFACEOUTERBOUND(#380,.T.);
#382= IFCFACE((#381));
#383= IFCPOLYLOOP((#237,#315,#316));
#384= IFCFACEOUTERBOUND(#383,.T.);
#385= IFCFACE((#384));
#386= IFCPOLYLOOP((#236,#314,#315));
#387= IFCFACEOUTERBOUND(#386,.T.);
#388= IFCFACE((#387));
#389= IFCPOLYLOOP((#235,#313,#314));
#390= IFCFACEOUTERBOUND(#389,.T.);
#391= IFCFACE((#390));
#392= IFCPOLYLOOP((#234,#312,#313));
#393= IFCFACEOUTERBOUND(#392,.T.);
#394= IFCFACE((#393));
#395= IFCPOLYLOOP((#233,#311,#312));
#396= IFCFACEOUTERBOUND(#395,.T.);
#397= IFCFACE((#396));
#398= IFCPOLYLOOP((#232,#310,#311));
#399= IFCFACEOUTERBOUND(#398,.T.);
#400= IFCFACE((#399));
#401= IFCPOLYLOOP((#231,#309,#310));
#402= IFCFACEOUTERBOUND(#401,.T.);
#403= IFCFACE((#402));
#404= IFCPOLYLOOP((#230,#308,#309));
#405= IFCFACEOUTERBOUND(#404,.T.);
#406= IFCFACE((#405));
#407= IFCPOLYLOOP((#229,#307,#308));
#408= IFCFACEOUTERBOUND(#407,.T.);
#409= IFCFACE((#408));
#410= IFCPOLYLOOP((#228,#306,#307));
#411= IFCFACEOUTERBOUND(#410,.T.);
#412= IFCFACE((#411));
#413= IFCPOLYLOOP((#227,#305,#306));
#414= IFCFACEOUTERBOUND(#413,.T.);
#415= IFCFACE((#414));
#416= IFCPOLYLOOP((#226,#304,#305));
#417= IFCFACEOUTERBOUND(#416,.T.);
#418= IFCFACE((#417));
#419= IFCPOLYLOOP((#225,#303,#304));
#420= IFCFACEOUTERBOUND(#419,.T.);
#421= IFCFACE((#420));
#422= IFCPOLYLOOP((#224,#302,#303));
#423= IFCFACEOUTERBOUND(#422,.T.);
#424= IFCFACE((#423));
#425= IFCPOLYLOOP((#223,#301,#302));
#426= IFCFACEOUTERBOUND(#425,.T.);
#427= IFCFACE((#426));
#428= IFCPOLYLOOP((#222,#300,#301));
#429= IFCFACEOUTERBOUND(#428,.T.);
#430= IFCFACE((#429));
#431= IFCPOLYLOOP((#221,#299,#300));
#432= IFCFACEOUTERBOUND(#431,.T.);
#433= IFCFACE((#432));
#434= IFCPOLYLOOP((#220,#298,#299));
#435= IFCFACEOUTERBOUND(#434,.T.);
#436= IFCFACE((#435));
#437= IFCPOLYLOOP((#219,#297,#298));
#438= IFCFACEOUTERBOUND(#437,.T.);
#439= IFCFACE((#438));
#440= IFCPOLYLOOP((#218,#296,#297));
#441= IFCFACEOUTERBOUND(#440,.T.);
#442= IFCFACE((#441));
#443= IFCPOLYLOOP((#217,#295,#296));
#444= IFCFACEOUTERBOUND(#443,.T.);
#445= IFCFACE((#444));
#446= IFCPOLYLOOP((#216,#294,#295));
#447= IFCFACEOUTERBOUND(#446,.T.);
#448= IFCFACE((#447));
#449= IFCPOLYLOOP((#215,#293,#294));
#450= IFCFACEOUTERBOUND(#449,.T.);
#451= IFCFACE((#450));
#452= IFCPOLYLOOP((#214,#292,#293));
#453= IFCFACEOUTERBOUND(#452,.T.);
#454= IFCFACE((#453));
#455= IFCPOLYLOOP((#213,#291,#292));
#456= IFCFACEOUTERBOUND(#455,.T.);
#457= IFCFACE((#456));
#458= IFCPOLYLOOP((#212,#290,#291));
#459= IFCFACEOUTERBOUND(#458,.T.);
#460= IFCFACE((#459));
#461= IFCPOLYLOOP((#211,#289,#290));
#462= IFCFACEOUTERBOUND(#461,.T.);
#463= IFCFACE((#462));
#464= IFCPOLYLOOP((#210,#288,#289));
#465= IFCFACEOUTERBOUND(#464,.T.);
#466= IFCFACE((#465));
#467= IFCPOLYLOOP((#209,#287,#288));
#468= IFCFACEOUTERBOUND(#467,.T.);
#469= IFCFACE((#468));
#470= IFCPOLYLOOP((#208,#286,#287));
#471= IFCFACEOUTERBOUND(#470,.T.);
#472= IFCFACE((#471));
#473= IFCPOLYLOOP((#207,#285,#286));
#474= IFCFACEOUTERBOUND(#473,.T.);
#475= IFCFACE((#474));
#476= IFCPOLYLOOP((#206,#284,#285));
#477= IFCFACEOUTERBOUND(#476,.T.);
#478= IFCFACE((#477));
#479= IFCPOLYLOOP((#205,#283,#284));
#480= IFCFACEOUTERBOUND(#479,.T.);
#481= IFCFACE((#480));
#482= IFCPOLYLOOP((#204,#282,#283));
#483= IFCFACEOUTERBOUND(#482,.T.);
#484= IFCFACE((#483));
#485= IFCPOLYLOOP((#203,#281,#282));
#486= IFCFACEOUTERBOUND(#485,.T.);
#487= IFCFACE((#486));
#488= IFCPOLYLOOP((#202,#280,#281));
#489= IFCFACEOUTERBOUND(#488,.T.);
#490= IFCFACE((#489));
#491= IFCPOLYLOOP((#200,#240,#319));
#492= IFCFACEOUTERBOUND(#491,.T.);
#493= IFCFACE((#492));
#494= IFCPOLYLOOP((#240,#239,#318));
#495= IFCFACEOUTERBOUND(#494,.T.);
#496= IFCFACE((#495));
#497= IFCPOLYLOOP((#239,#238,#317));
#498= IFCFACEOUTERBOUND(#497,.T.);
#499= IFCFACE((#498));
#500= IFCPOLYLOOP((#238,#237,#316));
#501= IFCFACEOUTERBOUND(#500,.T.);
#502= IFCFACE((#501));
#503= IFCPOLYLOOP((#237,#236,#315));
#504= IFCFACEOUTERBOUND(#503,.T.);
#505= IFCFACE((#504));
#506= IFCPOLYLOOP((#236,#235,#314));
#507= IFCFACEOUTERBOUND(#506,.T.);
#508= IFCFACE((#507));
#509= IFCPOLYLOOP((#235,#234,#313));
#510= IFCFACEOUTERBOUND(#509,.T.);
#511= IFCFACE((#510));
#512= IFCPOLYLOOP((#234,#233,#312));
#513= IFCFACEOUTERBOUND(#512,.T.);
#514= IFCFACE((#513));
#515= IFCPOLYLOOP((#233,#232,#311));
#516= IFCFACEOUTERBOUND(#515,.T.);
#517= IFCFACE((#516));
#518= IFCPOLYLOOP((#232,#231,#310));
#519= IFCFACEOUTERBOUND(#518,.T.);
#520= IFCFACE((#519));
#521= IFCPOLYLOOP((#231,#230,#309));
#522= IFCFACEOUTERBOUND(#521,.T.);
#523= IFCFACE((#522));
#524= IFCPOLYLOOP((#230,#229,#308));
#525= IFCFACEOUTERBOUND(#524,.T.);
#526= IFCFACE((#525));
#527= IFCPOLYLOOP((#229,#228,#307));
#528= IFCFACEOUTERBOUND(#527,.T.);
#529= IFCFACE((#528));
#530= IFCPOLYLOOP((#228,#227,#306));
#531= IFCFACEOUTERBOUND(#530,.T.);
#532= IFCFACE((#531));
#533= IFCPOLYLOOP((#227,#226,#305));
#534= IFCFACEOUTERBOUND(#533,.T.);
#535= IFCFACE((#534));
#536= IFCPOLYLOOP((#226,#225,#304));
#537= IFCFACEOUTERBOUND(#536,.T.);
#538= IFCFACE((#537));
#539= IFCPOLYLOOP((#225,#224,#303));
#540= IFCFACEOUTERBOUND(#539,.T.);
#541= IFCFACE((#540));
#542= IFCPOLYLOOP((#224,#223,#302));
#543= IFCFACEOUTERBOUND(#542,.T.);
#544= IFCFACE((#543));
#545= IFCPOLYLOOP((#223,#222,#301));
#546= IFCFACEOUTERBOUND(#545,.T.);
#547= IFCFACE((#546));
#548= IFCPOLYLOOP((#222,#221,#300));
#549= IFCFACEOUTERBOUND(#548,.T.);
#550= IFCFACE((#549));
#551= IFCPOLYLOOP((#221,#220,#299));
#552= IFCFACEOUTERBOUND(#551,.T.);
#553= IFCFACE((#552));
#554= IFCPOLYLOOP((#220,#219,#298));
#555= IFCFACEOUTERBOUND(#554,.T.);
#556= IFCFACE((#555));
#557= IFCPOLYLOOP((#219,#218,#297));
#558= IFCFACEOUTERBOUND(#557,.T.);
#559= IFCFACE((#558));
#560= IFCPOLYLOOP((#218,#217,#296));
#561= IFCFACEOUTERBOUND(#560,.T.);
#562= IFCFACE((#561));
#563= IFCPOLYLOOP((#217,#216,#295));
#564= IFCFACEOUTERBOUND(#563,.T.);
#565= IFCFACE((#564));
#566= IFCPOLYLOOP((#216,#215,#294));
#567= IFCFACEOUTERBOUND(#566,.T.);
#568= IFCFACE((#567));
#569= IFCPOLYLOOP((#215,#214,#293));
#570= IFCFACEOUTERBOUND(#569,.T.);
#571= IFCFACE((#570));
#572= IFCPOLYLOOP((#214,#213,#292));
#573= IFCFACEOUTERBOUND(#572,.T.);
#574= IFCFACE((#573));
#575= IFCPOLYLOOP((#213,#212,#291));
#576= IFCFACEOUTERBOUND(#575,.T.);
#577= IFCFACE((#576));
#578= IFCPOLYLOOP((#212,#211,#290));
#579= IFCFACEOUTERBOUND(#578,.T.);
#580= IFCFACE((#579));
#581= IFCPOLYLOOP((#211,#210,#289));
#582= IFCFACEOUTERBOUND(#581,.T.);
#583= IFCFACE((#582));
#584= IFCPOLYLOOP((#210,#209,#288));
#585= IFCFACEOUTERBOUND(#584,.T.);
#586= IFCFACE((#585));
#587= IFCPOLYLOOP((#209,#208,#287));
#588= IFCFACEOUTERBOUND(#587,.T.);
#589= IFCFACE((#588));
#590= IFCPOLYLOOP((#208,#207,#286));
#591= IFCFACEOUTERBOUND(#590,.T.);
#592= IFCFACE((#591));
#593= IFCPOLYLOOP((#207,#206,#285));
#594= IFCFACEOUTERBOUND(#593,.T.);
#595= IFCFACE((#594));
#596= IFCPOLYLOOP((#206,#205,#284));
#597= IFCFACEOUTERBOUND(#596,.T.);
#598= IFCFACE((#597));
#599= IFCPOLYLOOP((#205,#204,#283));
#600= IFCFACEOUTERBOUND(#599,.T.);
#601= IFCFACE((#600));
#602= IFCPOLYLOOP((#204,#203,#282));
#603= IFCFACEOUTERBOUND(#602,.T.);
#604= IFCFACE((#603));
#605= IFCPOLYLOOP((#203,#202,#281));
#606= IFCFACEOUTERBOUND(#605,.T.);
#607= IFCFACE((#606));
#608= IFCPOLYLOOP((#202,#200,#280));
#609= IFCFACEOUTERBOUND(#608,.T.);
#610= IFCFACE((#609));
#611= IFCPOLYLOOP((#201,#359,#320));
#612= IFCFACEOUTERBOUND(#611,.T.);
#613= IFCFACE((#612));
#614= IFCPOLYLOOP((#241,#358,#359));
#615= IFCFACEOUTERBOUND(#614,.T.);
#616= IFCFACE((#615));
#617= IFCPOLYLOOP((#242,#357,#358));
#618= IFCFACEOUTERBOUND(#617,.T.);
#619= IFCFACE((#618));
#620= IFCPOLYLOOP((#243,#356,#357));
#621= IFCFACEOUTERBOUND(#620,.T.);
#622= IFCFACE((#621));
#623= IFCPOLYLOOP((#244,#355,#356));
#624= IFCFACEOUTERBOUND(#623,.T.);
#625= IFCFACE((#624));
#626= IFCPOLYLOOP((#245,#354,#355));
#627= IFCFACEOUTERBOUND(#626,.T.);
#628= IFCFACE((#627));
#629= IFCPOLYLOOP((#246,#353,#354));
#630= IFCFACEOUTERBOUND(#629,.T.);
#631= IFCFACE((#630));
#632= IFCPOLYLOOP((#247,#352,#353));
#633= IFCFACEOUTERBOUND(#632,.T.);
#634= IFCFACE((#633));
#635= IFCPOLYLOOP((#248,#351,#352));
#636= IFCFACEOUTERBOUND(#635,.T.);
#637= IFCFACE((#636));
#638= IFCPOLYLOOP((#249,#350,#351));
#639= IFCFACEOUTERBOUND(#638,.T.);
#640= IFCFACE((#639));
#641= IFCPOLYLOOP((#250,#349,#350));
#642= IFCFACEOUTERBOUND(#641,.T.);
#643= IFCFACE((#642));
#644= IFCPOLYLOOP((#251,#348,#349));
#645= IFCFACEOUTERBOUND(#644,.T.);
#646= IFCFACE((#645));
#647= IFCPOLYLOOP((#252,#347,#348));
#648= IFCFACEOUTERBOUND(#647,.T.);
#649= IFCFACE((#648));
#650= IFCPOLYLOOP((#253,#346,#347));
#651= IFCFACEOUTERBOUND(#650,.T.);
#652= IFCFACE((#651));
#653= IFCPOLYLOOP((#254,#345,#346));
#654= IFCFACEOUTERBOUND(#653,.T.);
#655= IFCFACE((#654));
#656= IFCPOLYLOOP((#255,#344,#345));
#657= IFCFACEOUTERBOUND(#656,.T.);
#658= IFCFACE((#657));
#659= IFCPOLYLOOP((#256,#343,#344));
#660= IFCFACEOUTERBOUND(#659,.T.);
#661= IFCFACE((#660));
#662= IFCPOLYLOOP((#257,#342,#343));
#663= IFCFACEOUTERBOUND(#662,.T.);
#664= IFCFACE((#663));
#665= IFCPOLYLOOP((#258,#341,#342));
#666= IFCFACEOUTERBOUND(#665,.T.);
#667= IFCFACE((#666));
#668= IFCPOLYLOOP((#259,#340,#341));
#669= IFCFACEOUTERBOUND(#668,.T.);
#670= IFCFACE((#669));
#671= IFCPOLYLOOP((#260,#339,#340));
#672= IFCFACEOUTERBOUND(#671,.T.);
#673= IFCFACE((#672));
#674= IFCPOLYLOOP((#261,#338,#339));
#675= IFCFACEOUTERBOUND(#674,.T.);
#676= IFCFACE((#675));
#677= IFCPOLYLOOP((#262,#337,#338));
#678= IFCFACEOUTERBOUND(#677,.T.);
#679= IFCFACE((#678));
#680= IFCPOLYLOOP((#263,#336,#337));
#681= IFCFACEOUTERBOUND(#680,.T.);
#682= IFCFACE((#681));
#683= IFCPOLYLOOP((#264,#335,#336));
#684= IFCFACEOUTERBOUND(#683,.T.);
#685= IFCFACE((#684));
#686= IFCPOLYLOOP((#265,#334,#335));
#687= IFCFACEOUTERBOUND(#686,.T.);
#688= IFCFACE((#687));
#689= IFCPOLYLOOP((#266,#333,#334));
#690= IFCFACEOUTERBOUND(#689,.T.);
#691= IFCFACE((#690));
#692= IFCPOLYLOOP((#267,#332,#333));
#693= IFCFACEOUTERBOUND(#692,.T.);
#694= IFCFACE((#693));
#695= IFCPOLYLOOP((#268,#331,#332));
#696= IFCFACEOUTERBOUND(#695,.T.);
#697= IFCFACE((#696));
#698= IFCPOLYLOOP((#269,#330,#331));
#699= IFCFACEOUTERBOUND(#698,.T.);
#700= IFCFACE((#699));
#701= IFCPOLYLOOP((#270,#329,#330));
#702= IFCFACEOUTERBOUND(#701,.T.);
#703= IFCFACE((#702));
#704= IFCPOLYLOOP((#271,#328,#329));
#705= IFCFACEOUTERBOUND(#704,.T.);
#706= IFCFACE((#705));
#707= IFCPOLYLOOP((#272,#327,#328));
#708= IFCFACEOUTERBOUND(#707,.T.);
#709= IFCFACE((#708));
#710= IFCPOLYLOOP((#273,#326,#327));
#711= IFCFACEOUTERBOUND(#710,.T.);
#712= IFCFACE((#711));
#713= IFCPOLYLOOP((#274,#325,#326));
#714= IFCFACEOUTERBOUND(#713,.T.);
#715= IFCFACE((#714));
#716= IFCPOLYLOOP((#275,#324,#325));
#717= IFCFACEOUTERBOUND(#716,.T.);
#718= IFCFACE((#717));
#719= IFCPOLYLOOP((#276,#323,#324));
#720= IFCFACEOUTERBOUND(#719,.T.);
#721= IFCFACE((#720));
#722= IFCPOLYLOOP((#277,#322,#323));
#723= IFCFACEOUTERBOUND(#722,.T.);
#724= IFCFACE((#723));
#725= IFCPOLYLOOP((#278,#321,#322));
#726= IFCFACEOUTERBOUND(#725,.T.);
#727= IFCFACE((#726));
#728= IFCPOLYLOOP((#279,#320,#321));
#729= IFCFACEOUTERBOUND(#728,.T.);
#730= IFCFACE((#729));
#731= IFCPOLYLOOP((#201,#241,#359));
#732= IFCFACEOUTERBOUND(#731,.T.);
#733= IFCFACE((#732));
#734= IFCPOLYLOOP((#241,#242,#358));
#735= IFCFACEOUTERBOUND(#734,.T.);
#736= IFCFACE((#735));
#737= IFCPOLYLOOP((#242,#243,#357));
#738= IFCFACEOUTERBOUND(#737,.T.);
#739= IFCFACE((#738));
#740= IFCPOLYLOOP((#243,#244,#356));
#741= IFCFACEOUTERBOUND(#740,.T.);
#742= IFCFACE((#741));
#743= IFCPOLYLOOP((#244,#245,#355));
#744= IFCFACEOUTERBOUND(#743,.T.);
#745= IFCFACE((#744));
#746= IFCPOLYLOOP((#245,#246,#354));
#747= IFCFACEOUTERBOUND(#746,.T.);
#748= IFCFACE((#747));
#749= IFCPOLYLOOP((#246,#247,#353));
#750= IFCFACEOUTERBOUND(#749,.T.);
#751= IFCFACE((#750));
#752= IFCPOLYLOOP((#247,#248,#352));
#753= IFCFACEOUTERBOUND(#752,.T.);
#754= IFCFACE((#753));
#755= IFCPOLYLOOP((#248,#249,#351));
#756= IFCFACEOUTERBOUND(#755,.T.);
#757= IFCFACE((#756));
#758= IFCPOLYLOOP((#249,#250,#350));
#759= IFCFACEOUTERBOUND(#758,.T.);
#760= IFCFACE((#759));
#761= IFCPOLYLOOP((#250,#251,#349));
#762= IFCFACEOUTERBOUND(#761,.T.);
#763= IFCFACE((#762));
#764= IFCPOLYLOOP((#251,#252,#348));
#765= IFCFACEOUTERBOUND(#764,.T.);
#766= IFCFACE((#765));
#767= IFCPOLYLOOP((#252,#253,#347));
#768= IFCFACEOUTERBOUND(#767,.T.);
#769= IFCFACE((#768));
#770= IFCPOLYLOOP((#253,#254,#346));
#771= IFCFACEOUTERBOUND(#770,.T.);
#772= IFCFACE((#771));
#773= IFCPOLYLOOP((#254,#255,#345));
#774= IFCFACEOUTERBOUND(#773,.T.);
#775= IFCFACE((#774));
#776= IFCPOLYLOOP((#255,#256,#344));
#777= IFCFACEOUTERBOUND(#776,.T.);
#778= IFCFACE((#777));
#779= IFCPOLYLOOP((#256,#257,#343));
#780= IFCFACEOUTERBOUND(#779,.T.);
#781= IFCFACE((#780));
#782= IFCPOLYLOOP((#257,#258,#342));
#783= IFCFACEOUTERBOUND(#782,.T.);
#784= IFCFACE((#783));
#785= IFCPOLYLOOP((#258,#259,#341));
#786= IFCFACEOUTERBOUND(#785,.T.);
#787= IFCFACE((#786));
#788= IFCPOLYLOOP((#259,#260,#340));
#789= IFCFACEOUTERBOUND(#788,.T.);
#790= IFCFACE((#789));
#791= IFCPOLYLOOP((#260,#261,#339));
#792= IFCFACEOUTERBOUND(#791,.T.);
#793= IFCFACE((#792));
#794= IFCPOLYLOOP((#261,#262,#338));
#795= IFCFACEOUTERBOUND(#794,.T.);
#796= IFCFACE((#795));
#797= IFCPOLYLOOP((#262,#263,#337));
#798= IFCFACEOUTERBOUND(#797,.T.);
#799= IFCFACE((#798));
#800= IFCPOLYLOOP((#263,#264,#336));
#801= IFCFACEOUTERBOUND(#800,.T.);
#802= IFCFACE((#801));
#803= IFCPOLYLOOP((#264,#265,#335));
#804= IFCFACEOUTERBOUND(#803,.T.);
#805= IFCFACE((#804));
#806= IFCPOLYLOOP((#265,#266,#334));
#807= IFCFACEOUTERBOUND(#806,.T.);
#808= IFCFACE((#807));
#809= IFCPOLYLOOP((#266,#267,#333));
#810= IFCFACEOUTERBOUND(#809,.T.);
#811= IFCFACE((#810));
#812= IFCPOLYLOOP((#267,#268,#332));
#813= IFCFACEOUTERBOUND(#812,.T.);
#814= IFCFACE((#813));
#815= IFCPOLYLOOP((#268,#269,#331));
#816= IFCFACEOUTERBOUND(#815,.T.);
#817= IFCFACE((#816));
#818= IFCPOLYLOOP((#269,#270,#330));
#819= IFCFACEOUTERBOUND(#818,.T.);
#820= IFCFACE((#819));
#821= IFCPOLYLOOP((#270,#271,#329));
#822= IFCFACEOUTERBOUND(#821,.T.);
#823= IFCFACE((#822));
#824= IFCPOLYLOOP((#271,#272,#328));
#825= IFCFACEOUTERBOUND(#824,.T.);
#826= IFCFACE((#825));
#827= IFCPOLYLOOP((#272,#273,#327));
#828= IFCFACEOUTERBOUND(#827,.T.);
#829= IFCFACE((#828));
#830= IFCPOLYLOOP((#273,#274,#326));
#831= IFCFACEOUTERBOUND(#830,.T.);
#832= IFCFACE((#831));
#833= IFCPOLYLOOP((#274,#275,#325));
#834= IFCFACEOUTERBOUND(#833,.T.);
#835= IFCFACE((#834));
#836= IFCPOLYLOOP((#275,#276,#324));
#837= IFCFACEOUTERBOUND(#836,.T.);
#838= IFCFACE((#837));
#839= IFCPOLYLOOP((#276,#277,#323));
#840= IFCFACEOUTERBOUND(#839,.T.);
#841= IFCFACE((#840));
#842= IFCPOLYLOOP((#277,#278,#322));
#843= IFCFACEOUTERBOUND(#842,.T.);
#844= IFCFACE((#843));
#845= IFCPOLYLOOP((#278,#279,#321));
#846= IFCFACEOUTERBOUND(#845,.T.);
#847= IFCFACE((#846));
#848= IFCPOLYLOOP((#279,#201,#320));
#849= IFCFACEOUTERBOUND(#848,.T.);
#850= IFCFACE((#849));
#851= IFCCLOSEDSHELL((#364,#367,#370,#373,#376,#379,#382,#385,#388,#391,#394,#397,#400,#403,#406,#409,#412,#415,#418,#421,#424,#427,#430,#433,#436,#439,#442,#445,#448,#451,#454,#457,#460,#463,#466,#469,#472,#475,#478,#481,#484,#487,#490,#493,#496,#499,#502,#505,#508,#511,#514,#517,#520,#523,#526,#529,#532,#535,#538,#541,#544,#547,#550,#553,#556,#559,#562,#565,#568,#571,#574,#577,#580,#583,#586,#589,#592,#595,#598,#601,#604,#607,#610,#613,#616,#619,#622,#625,#628,#631,#634,#637,#640,#643,#646,#649,#652,#655,#658,#661,#664,#667,#670,#673,#676,#679,#682,#685,#688,#691,#694,#697,#700,#703,#706,#709,#712,#715,#718,#721,#724,#727,#730,#733,#736,#739,#742,#745,#748,#751,#754,#757,#760,#763,#766,#769,#772,#775,#778,#781,#784,#787,#790,#793,#796,#799,#802,#805,#808,#811,#814,#817,#820,#823,#826,#829,#832,#835,#838,#841,#844,#847,#850));
#852= IFCFACETEDBREP(#851);
#853= IFCREPRESENTATIONMAP(#854,#856);
#854= IFCAXIS2PLACEMENT3D(#855,$,$);
#855= IFCCARTESIANPOINT((0.0,0.0,0.0));
#856= IFCSHAPEREPRESENTATION(#12,'Body','Brep',(#852));
#857= IFCMATERIAL('Ceramic',$,$);
#858= IFCRELASSOCIATESMATERIAL('2V4PnJ8zX1nwtfgVsb30Vx',$,'MatAssoc','Material Associates',(#860),#857);
#860= IFCSANITARYTERMINALTYPE('1kNLoccErCohWO1HlSUjUS',$,'IFCSANITARYTERMINALTYPE',$,$,$,(#853),$,$,.WASHHANDBASIN.);
#861= IFCRELDEFINESBYTYPE('0eraknYh91k81bLjGOgkw2',$,$,$,(#868),#860);
#862= IFCDIRECTION((1.0,0.0,0.0));
#863= IFCDIRECTION((0.0,1.0,0.0));
#864= IFCCARTESIANPOINT((0.0,0.0,0.0));
#865= IFCCARTESIANTRANSFORMATIONOPERATOR3D(#862,#863,#864,1.0,#866);
#866= IFCDIRECTION((0.0,0.0,1.0));
#867= IFCMAPPEDITEM(#853,#865);
#868= IFCSANITARYTERMINAL('3tkl48paX9yw0lahGPGmeU',$,$,$,$,#55,#869,$,.NOTDEFINED.);
#869= IFCPRODUCTDEFINITIONSHAPE($,$,(#870));
#870= IFCSHAPEREPRESENTATION(#12,'Body','MappedRepresentation',(#867));
ENDSEC;

END-ISO-10303-21;


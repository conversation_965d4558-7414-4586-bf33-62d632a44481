ISO-10303-21;
HEADER;
FILE_DESCRIPTION(('ViewDefinition [notYetAssigned]'),'2;1');
FILE_NAME(
/* name */ '',
/* time_stamp */ '2014-12-09T00:27:55',
/* author */ ('Jon'),
/* organization */ ('Unknown'),
/* preprocessor_version */ 'ggIFC - Exporter by Geometry Gym Pty Ltd',
/* originating_system */ 'ggIFC - Exporter by Geometry Gym Pty Ltd',
/* authorization */ 'None');

FILE_SCHEMA (('IFC4'));
ENDSEC;

DATA;
#1= IFCAPPLICATION(#2,'*******','ggRhinoIFC - Geometry Gym Plug-in for Rhino3d','ggRhinoIFC');
#2= IFCORGANIZATION($,'Geometry Gym Pty Ltd',$,$,$);
#3= IFCPERSONANDORGANIZATION(#4,#5,$);
#4= IFCPERSON('Jon','Jon',$,$,$,$,$,$);
#5= IFCORGANIZATION($,'Geometry Gym Pty Ltd',$,$,$);
#6= IFCOWNERHISTORY(#3,#1,$,.ADDED.,1418084875,$,$,1418084875);
#7= IFCGEOMETRICREPRESENTATIONCONTEXT($,'Model',3,0.0001,#8,#10);
#8= IFCAXIS2PLACEMENT3D(#9,$,$);
#9= IFCCARTESIANPOINT((0.0,0.0,0.0));
#10= IFCDIRECTION((0.0,1.0));
#11= IFCGEOMETRICREPRESENTATIONSUBCONTEXT('Axis','Model',*,*,*,*,#7,$,.MODEL_VIEW.,$);
#12= IFCGEOMETRICREPRESENTATIONSUBCONTEXT('Body','Model',*,*,*,*,#7,$,.MODEL_VIEW.,$);
#13= IFCGEOMETRICREPRESENTATIONCONTEXT($,'Model',3,0.0001,#8,#14);
#14= IFCDIRECTION((0.0,1.0));
#50= IFCBUILDING('0MRYiPpfn0RRBz4hjR$d0R',$,'IfcBuilding',$,$,#51,$,$,.ELEMENT.,$,$,#57);
#51= IFCLOCALPLACEMENT($,#52);
#52= IFCAXIS2PLACEMENT3D(#53,$,$);
#53= IFCCARTESIANPOINT((0.0,0.0,0.0));
#54= IFCRELCONTAINEDINSPATIALSTRUCTURE('2YVMFu6$54ggBelxInCL1O',$,'Building','Building Container for Elements',(#217),#50);
#55= IFCLOCALPLACEMENT(#51,#52);
#57= IFCPOSTALADDRESS($,$,$,$,$,$,$,'Unknown',$,$);
#100= IFCPROJECT('3SXUMunn9EXfAFTjVxyt84',#6,'IfcProject',$,$,'IfcProject','',(#13),#101);
#101= IFCUNITASSIGNMENT((#102,#103,#104));
#102= IFCSIUNIT(*,.LENGTHUNIT.,.MILLI.,.METRE.);
#103= IFCSIUNIT(*,.PLANEANGLEUNIT.,$,.RADIAN.);
#104= IFCSIUNIT(*,.TIMEUNIT.,$,.SECOND.);
#105= IFCRELAGGREGATES('2nzrR4fRv81gWFsNf1sIl$',$,'Project Container','Project Container for Buildings',#100,(#50));
#200= IFCCARTESIANPOINTLIST3D(((-300.0,150.0,0.0),(-260.012578,202.771984,0.0),(-200.897703,235.427328,0.0),(-135.653172,254.960516,0.0),(-68.351281,265.485063,0.0),(2.288734,268.839531,0.0),(72.81782,265.023844,0.0),(139.786906,254.038063,0.0),(201.174906,235.317031,0.0),(259.220938,203.387031,0.0),(300.0,150.0,0.0),(301.12175,84.866148,0.0),(274.727594,21.433672,0.0),(235.605922,-32.723826,0.0),(186.088641,-80.939688,0.0),(130.136258,-119.016594,0.0),(67.084977,-144.523266,0.0),(1.477218,-153.498641,0.0),(-64.392137,-145.234375,0.0),(-128.935,-119.668008,0.0),(-185.4365,-81.474469,0.0),(-235.751609,-32.555805,0.0),(-275.439625,22.660475,0.0),(-301.2465,85.400219,0.0),(0.0,253.099266,0.0),(-65.777992,249.952375,0.0),(-128.508695,240.511688,0.0),(-189.983266,222.998141,0.0),(-246.840234,193.330969,0.0),(-286.93375,143.116359,0.0),(-288.338563,84.231891,0.0),(-263.388344,25.178932,0.0),(-224.986906,-26.382564,0.0),(-176.642109,-71.667547,0.0),(-122.550633,-106.846461,0.0),(-61.391031,-130.155953,0.0),(1.00923,-137.756953,0.0),(63.202145,-129.69757,0.0),(123.138398,-106.540977,0.0),(176.955734,-71.42018,0.0),(224.650078,-26.756678,0.0),(262.387781,23.516443,0.0),(288.070906,83.103938,0.0),(286.93375,143.116359,0.0),(248.344641,192.212875,0.0),(191.622094,222.376281,0.0),(129.659992,240.269531,0.0),(64.742059,250.052203,0.0),(-157.154922,175.808609,-94.0),(-136.207516,207.772813,-94.0),(-105.240203,227.552281,-94.0),(-71.061875,239.383609,-94.0),(-35.805801,245.758375,-94.0),(1.198953,247.790172,-94.0),(38.145594,245.479016,-94.0),(73.227336,238.824875,-94.0),(105.385414,227.485469,-94.0),(135.792813,208.145344,-94.0),(157.154922,175.808609,-94.0),(157.742547,136.356797,-94.0),(143.915969,97.9355,-94.0),(123.422102,65.13209,-94.0),(97.482477,35.927559,-94.0),(68.171844,12.864227,-94.0),(35.142449,-2.585266,-94.0),(0.77384,-8.021682,-94.0),(-33.731801,-3.015985,-94.0),(-67.542563,12.469661,-94.0),(-97.140859,35.603637,-94.0),(-123.498414,65.233859,-94.0),(-144.288969,98.678578,-94.0),(-157.807906,136.680281,-94.0),(-300.0,150.0,0.0),(-228.577453,162.904313,-47.0),(-157.154922,175.808609,-94.0),(-260.012578,202.771984,0.0),(-136.207516,207.772813,-94.0),(-200.897703,235.427328,0.0),(-105.240203,227.552281,-94.0),(-135.653172,254.960516,0.0),(-71.061875,239.383609,-94.0),(-68.351281,265.485063,0.0),(-35.805801,245.758375,-94.0),(2.288734,268.839531,0.0),(1.198953,247.790172,-94.0),(72.81782,265.023844,0.0),(38.145594,245.479016,-94.0),(139.786906,254.038063,0.0),(73.227336,238.824875,-94.0),(201.174906,235.317031,0.0),(105.385414,227.485469,-94.0),(259.220938,203.387031,0.0),(135.792813,208.145344,-94.0),(300.0,150.0,0.0),(157.154922,175.808609,-94.0),(301.12175,84.866148,0.0),(157.742547,136.356797,-94.0),(274.727594,21.433672,0.0),(143.915969,97.9355,-94.0),(235.605922,-32.723826,0.0),(123.422102,65.13209,-94.0),(186.088641,-80.939688,0.0),(97.482477,35.927559,-94.0),(130.136258,-119.016594,0.0),(68.171844,12.864227,-94.0),(67.084977,-144.523266,0.0),(35.142449,-2.585266,-94.0),(1.477218,-153.498641,0.0),(0.77384,-8.021682,-94.0),(-64.392137,-145.234375,0.0),(-33.731801,-3.015985,-94.0),(-128.935,-119.668008,0.0),(-67.542563,12.469661,-94.0),(-185.4365,-81.474469,0.0),(-97.140859,35.603637,-94.0),(-235.751609,-32.555805,0.0),(-123.498414,65.233859,-94.0),(-275.439625,22.660475,0.0),(-144.288969,98.678578,-94.0),(-301.2465,85.400219,0.0),(-157.807906,136.680281,-94.0),(-300.0,150.0,0.0),(-228.577453,162.904313,-47.0),(-157.154922,175.808609,-94.0),(-103.357523,247.172063,-47.0),(-153.068953,231.489813,-47.0),(-52.078543,255.621719,-47.0),(1.743843,258.314844,-47.0),(55.481707,255.251438,-47.0),(106.507117,246.431469,-47.0),(197.506875,205.766188,-47.0),(153.280156,231.40125,-47.0),(228.577453,162.904313,-47.0),(229.432141,110.611469,-47.0),(209.321781,59.684586,-47.0),(179.514016,16.204132,-47.0),(141.785563,-22.506064,-47.0),(51.113715,-73.554266,-47.0),(99.154047,-53.076184,-47.0),(1.125529,-80.760164,-47.0),(-49.061969,-74.12518,-47.0),(-98.238781,-53.599176,-47.0),(-141.288688,-22.935416,-47.0),(-209.864297,60.669523,-47.0),(-179.625016,16.339027,-47.0),(-229.527203,111.04025,-47.0),(0.0,247.792422,-84.0),(35.45952,245.798125,-84.0),(71.015367,239.395359,-84.0),(104.952289,227.684234,-84.0),(136.019484,207.942281,-84.0),(157.154922,175.808609,-84.0),(157.77775,136.530484,-84.0),(143.710984,97.530469,-84.0),(123.041867,64.626715,-84.0),(96.919461,35.394453,-84.0),(67.443461,12.407895,-84.0),(34.616102,-2.748099,-84.0),(0.55276,-8.022964,-84.0),(-33.624148,-3.048111,-84.0),(-67.121539,12.207951,-84.0),(-96.747688,35.232555,-84.0),(-123.226352,64.87157,-84.0),(-144.259,98.61857,-84.0),(-157.924344,137.268734,-84.0),(-157.154922,175.808609,-84.0),(-135.195516,208.674078,-84.0),(-104.054703,228.091234,-84.0),(-70.384797,239.553859,-84.0),(-36.026906,245.732781,-84.0),(0.0,247.792422,-84.0),(0.0,253.099266,0.0),(64.742059,250.052203,0.0),(129.659992,240.269531,0.0),(191.622094,222.376281,0.0),(248.344641,192.212875,0.0),(286.93375,143.116359,0.0),(288.070906,83.103938,0.0),(262.387781,23.516443,0.0),(224.650078,-26.756678,0.0),(176.955734,-71.42018,0.0),(123.138398,-106.540977,0.0),(63.202145,-129.69757,0.0),(1.00923,-137.756953,0.0),(-61.391031,-130.155953,0.0),(-122.550633,-106.846461,0.0),(-176.642109,-71.667547,0.0),(-224.986906,-26.382564,0.0),(-263.388344,25.178932,0.0),(-288.338563,84.231891,0.0),(-286.93375,143.116359,0.0),(-246.840234,193.330969,0.0),(-189.983266,222.998141,0.0),(-128.508695,240.511688,0.0),(-65.777992,249.952375,0.0),(0.0,253.099266,0.0),(0.0,247.792422,-84.0),(35.45952,245.798125,-84.0),(71.015367,239.395359,-84.0),(104.952289,227.684234,-84.0),(136.019484,207.942281,-84.0),(157.154922,175.808609,-84.0),(157.77775,136.530484,-84.0),(143.710984,97.530469,-84.0),(123.041867,64.626715,-84.0),(96.919461,35.394453,-84.0),(67.443461,12.407895,-84.0),(34.616102,-2.748099,-84.0),(0.55276,-8.022964,-84.0),(-33.624148,-3.048111,-84.0),(-67.121539,12.207951,-84.0),(-96.747688,35.232555,-84.0),(-123.226352,64.87157,-84.0),(-144.259,98.61857,-84.0),(-157.924344,137.268734,-84.0),(-157.154922,175.808609,-84.0),(-135.195516,208.674078,-84.0),(-104.054703,228.091234,-84.0),(-70.384797,239.553859,-84.0),(-36.026906,245.732781,-84.0)));
#201= IFCTRIANGULATEDFACESET(#200,$,.T.,((28,2,29),(1,29,2),(30,1,24),(29,1,30),(24,31,30),(3,2,28),(5,4,27),(6,5,25),(25,5,26),(4,28,27),(5,27,26),(3,28,4),(23,32,31),(33,32,23),(24,23,31),(34,22,21),(23,22,33),(22,34,33),(21,20,35),(36,35,20),(34,21,35),(37,36,19),(20,19,36),(18,37,19),(7,6,48),(8,7,47),(7,48,47),(8,47,46),(46,9,8),(46,45,10),(11,10,45),(12,11,44),(45,44,11),(10,9,46),(12,44,43),(15,39,16),(40,39,15),(38,16,39),(18,17,37),(16,38,17),(17,38,37),(13,43,42),(12,43,13),(14,13,42),(15,14,40),(14,41,40),(42,41,14),(48,6,25),(50,72,49),(51,72,50),(71,72,52),(51,52,72),(53,71,52),(69,70,63),(71,54,70),(66,67,65),(67,68,65),(68,69,64),(71,53,54),(54,55,61),(55,56,61),(58,60,57),(60,56,57),(59,60,58),(65,68,64),(69,63,64),(62,63,70),(62,54,61),(61,56,60),(62,70,54),(74,73,76),(80,125,126),(126,76,78),(126,77,76),(76,77,74),(82,127,125),(127,82,84),(127,83,81),(125,81,79),(128,84,129),(88,130,86),(92,131,90),(90,132,88),(94,133,92),(96,134,94),(98,135,96),(128,85,83),(77,75,74),(77,126,79),(85,128,87),(87,129,89),(131,93,132),(134,97,133),(97,134,99),(133,95,131),(132,91,130),(135,98,136),(102,137,100),(106,138,104),(104,139,102),(137,103,136),(108,140,106),(138,107,139),(139,105,137),(99,135,101),(141,110,112),(114,143,142),(141,111,109),(110,141,140),(118,144,145),(120,146,144),(116,145,143),(122,123,146),(140,109,138),(111,141,142),(113,142,143),(145,117,115),(146,121,119),(123,124,121),(144,119,117),(148,173,172),(149,174,173),(151,176,175),(152,177,176),(150,175,174),(154,179,178),(155,180,179),(157,182,181),(158,183,182),(156,181,180),(153,178,177),(160,185,159),(161,186,160),(163,188,162),(164,189,163),(162,187,161),(166,191,165),(167,192,166),(169,194,168),(171,196,170),(170,195,169),(168,193,167),(165,190,164),(159,184,183),(217,216,215),(217,215,218),(220,219,214),(215,219,218),(197,220,214),(214,213,197),(219,215,214),(210,208,211),(213,212,205),(212,211,207),(197,213,205),(198,204,199),(200,199,203),(203,202,201),(200,203,201),(203,199,204),(209,208,210),(208,207,211),(206,212,207),(212,206,205),(197,205,204),(197,204,198),(80,126,78),(82,125,80),(127,84,128),(127,81,125),(125,79,126),(84,86,129),(130,129,86),(131,132,90),(132,130,88),(133,131,92),(134,133,94),(135,134,96),(128,83,127),(128,129,87),(129,130,89),(93,91,132),(97,95,133),(134,135,99),(95,93,131),(91,89,130),(98,100,136),(137,136,100),(138,139,104),(139,137,102),(103,101,136),(140,138,106),(107,105,139),(105,103,137),(135,136,101),(141,112,142),(114,142,112),(141,109,140),(110,140,108),(118,145,116),(120,144,118),(116,143,114),(122,146,120),(109,107,138),(111,142,113),(113,143,115),(145,115,143),(146,119,144),(123,121,146),(144,117,145),(148,172,147),(149,173,148),(151,175,150),(152,176,151),(150,174,149),(154,178,153),(155,179,154),(157,181,156),(158,182,157),(156,180,155),(153,177,152),(185,184,159),(186,185,160),(188,187,162),(189,188,163),(187,186,161),(191,190,165),(192,191,166),(194,193,168),(196,195,170),(195,194,169),(193,192,167),(190,189,164),(159,183,158)),$);
#202= IFCREPRESENTATIONMAP(#203,#205);
#203= IFCAXIS2PLACEMENT3D(#204,$,$);
#204= IFCCARTESIANPOINT((0.0,0.0,0.0));
#205= IFCSHAPEREPRESENTATION(#12,'Body','Tessellation',(#201));
#206= IFCMATERIAL('Ceramic',$,$);
#207= IFCRELASSOCIATESMATERIAL('1Dd05NJPHCnwujFD$zyHB4',$,'MatAssoc','Material Associates',(#209),#206);
#209= IFCSANITARYTERMINALTYPE('14smeVOBv8HRjwdm9$muyM',$,'IFCSANITARYTERMINALTYPE',$,$,$,(#202),$,$,.WASHHANDBASIN.);
#210= IFCRELDEFINESBYTYPE('38Tc2o9wrEpw2I0HFj2zW7',$,$,$,(#217),#209);
#211= IFCDIRECTION((1.0,0.0,0.0));
#212= IFCDIRECTION((0.0,1.0,0.0));
#213= IFCCARTESIANPOINT((0.0,0.0,0.0));
#214= IFCCARTESIANTRANSFORMATIONOPERATOR3D(#211,#212,#213,1.0,#215);
#215= IFCDIRECTION((0.0,0.0,1.0));
#216= IFCMAPPEDITEM(#202,#214);
#217= IFCSANITARYTERMINAL('0Zk2_ch2P32wrl1QuECi58',$,$,$,$,#55,#218,$,.NOTDEFINED.);
#218= IFCPRODUCTDEFINITIONSHAPE($,$,(#219));
#219= IFCSHAPEREPRESENTATION(#12,'Body','MappedRepresentation',(#216));
ENDSEC;

END-ISO-10303-21;


{"type": "IfcProject", "globalId": "dc85e5b8-c712-4e86-928f-76d7fbf37204", "ownerHistory": {"type": "IfcOwnerHistory", "globalId": "be4a5a86-315d-4402-988b-ad5644727dce", "owningUser": {"type": "IfcPersonAndOrganization", "globalId": "00d2fb48-f52a-4ad9-80c7-b935eab1ca37", "thePerson": {"type": "<PERSON><PERSON><PERSON><PERSON>", "globalId": "5eaa926f-ca78-49c5-b0ee-ccb7ccc47538", "identification": {"type": "IfcIdentifier", "value": "<PERSON>"}, "familyName": {"type": "IfcLabel", "value": "<PERSON>"}}, "theOrganization": {"type": "IfcOrganization", "globalId": "b777f61c-b051-4928-81ed-53e91f1e38bb", "name": {"type": "IfcLabel", "value": "Geometry Gym Pty Ltd"}}}, "owningApplication": {"applicationDeveloper": {"type": "IfcOrganization", "globalId": "4a77a5ac-2607-45c3-a491-0a877f802de1", "name": {"type": "IfcLabel", "value": "Geometry Gym Pty Ltd"}}, "version": {"type": "IfcLabel", "value": "1.0.0.0"}, "applicationFullName": {"type": "IfcLabel", "value": "ggRhinoIFC - Geometry Gym Plug-in for Rhino3d"}, "applicationIdentifier": {"type": "IfcIdentifier", "value": "ggRhinoIFC"}}, "changeAction": "ADDED", "lastModifiedDate": {"type": "IfcTimeStamp", "value": 1418084875}, "creationDate": {"type": "IfcTimeStamp", "value": 1418084875}}, "name": {"type": "IfcLabel", "value": "IfcProject"}, "isDecomposedBy": {"type": "IfcRelAggregates", "globalId": "b1f756c4-a5be-4806-a80f-d97a41d92bff", "name": {"type": "IfcLabel", "value": "Project Container"}, "description": {"type": "IfcText", "value": "Project Container for Buildings"}, "relatedObjects": [{"type": "IfcBuilding", "globalId": "166e2b19-ce9c-406d-b2fd-12bb5bfe701b", "name": {"type": "IfcLabel", "value": "IfcBuilding"}, "objectPlacement": {"type": "IfcLocalPlacement", "globalId": "8e7bb3b2-dab2-4c61-b2be-d6c4700dd97d", "relativePlacement": {"type": "IfcAxis2Placement3D", "location": {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": 0.0}, {"type": "IfcLengthMeasure", "value": 0.0}, {"type": "IfcLengthMeasure", "value": 0.0}]}}}, "containsElements": [{"type": "IfcRelContainedInSpatialStructure", "globalId": "a27d63f8-1bf1-44aa-a2e8-bfb4b1315058", "name": {"type": "IfcLabel", "value": "Building"}, "description": {"type": "IfcText", "value": "Building Container for Elements"}, "relatedElements": [{"type": "IfcSanitaryTerminal", "globalId": "23b82fa6-ac26-430b-ad6f-05ae0e32c148", "isTypedBy": [{"type": "IfcRelDefinesByType", "globalId": "c87660b2-27ad-4ecf-a092-0113ed0bd807", "relatingType": {"type": "IfcSanitaryTerminalType", "globalId": "44db0a1f-60be-4845-bb7a-9f027fc38f16", "name": {"type": "IfcLabel", "value": "IFCSANITARYTERMINALTYPE"}, "hasAssociations": [{"type": "IfcRelAssociatesMaterial", "globalId": "4d9c0157-4d94-4cc7-ae2d-3cdffdf112c4", "name": {"type": "IfcLabel", "value": "MatAssoc"}, "description": {"type": "IfcText", "value": "Material Associates"}, "relatingMaterial": {"type": "IfcMaterial", "globalId": "49d76149-1139-405a-af5c-7fb7a9164add", "name": {"type": "IfcLabel", "value": "Ceram<PERSON>"}}}], "representationMaps": [{"type": "IfcRepresentationMap", "mappingOrigin": {"type": "IfcAxis2Placement3D", "location": {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": 0.0}, {"type": "IfcLengthMeasure", "value": 0.0}, {"type": "IfcLengthMeasure", "value": 0.0}]}}, "mappedRepresentation": {"type": "IfcShapeRepresentation", "contextOfItems": {"type": "IfcGeometricRepresentationSubContext", "globalId": "5c4747f7-3260-41e7-8816-6a7d4ec258cb", "contextIdentifier": {"type": "IfcLabel", "value": "Body"}, "contextType": {"type": "IfcLabel", "value": "Model"}, "targetView": "MODEL_VIEW"}, "representationIdentifier": {"type": "IfcLabel", "value": "Body"}, "representationType": {"type": "IfcLabel", "value": "Tessellation"}, "items": [{"type": "IfcTriangulatedFaceSet", "coordinates": {"type": "IfcCartesianPointList3D", "globalId": "9f9e7f21-149f-44bb-9c8d-b8f6df6e7469", "coordList": [{"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": -300.0}, {"type": "IfcLengthMeasure", "value": 150.0}, {"type": "IfcLengthMeasure", "value": 0.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": -260.012578}, {"type": "IfcLengthMeasure", "value": 202.771984}, {"type": "IfcLengthMeasure", "value": 0.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": -200.897703}, {"type": "IfcLengthMeasure", "value": 235.427328}, {"type": "IfcLengthMeasure", "value": 0.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": -135.653172}, {"type": "IfcLengthMeasure", "value": 254.960516}, {"type": "IfcLengthMeasure", "value": 0.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": -68.351281}, {"type": "IfcLengthMeasure", "value": 265.485063}, {"type": "IfcLengthMeasure", "value": 0.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": 2.288734}, {"type": "IfcLengthMeasure", "value": 268.839531}, {"type": "IfcLengthMeasure", "value": 0.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": 72.81782}, {"type": "IfcLengthMeasure", "value": 265.023844}, {"type": "IfcLengthMeasure", "value": 0.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": 139.786906}, {"type": "IfcLengthMeasure", "value": 254.038063}, {"type": "IfcLengthMeasure", "value": 0.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": 201.174906}, {"type": "IfcLengthMeasure", "value": 235.317031}, {"type": "IfcLengthMeasure", "value": 0.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": 259.220938}, {"type": "IfcLengthMeasure", "value": 203.387031}, {"type": "IfcLengthMeasure", "value": 0.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": 300.0}, {"type": "IfcLengthMeasure", "value": 150.0}, {"type": "IfcLengthMeasure", "value": 0.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": 301.12175}, {"type": "IfcLengthMeasure", "value": 84.866148}, {"type": "IfcLengthMeasure", "value": 0.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": 274.727594}, {"type": "IfcLengthMeasure", "value": 21.433672}, {"type": "IfcLengthMeasure", "value": 0.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": 235.605922}, {"type": "IfcLengthMeasure", "value": -32.723826}, {"type": "IfcLengthMeasure", "value": 0.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": 186.088641}, {"type": "IfcLengthMeasure", "value": -80.939688}, {"type": "IfcLengthMeasure", "value": 0.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": 130.136258}, {"type": "IfcLengthMeasure", "value": -119.016594}, {"type": "IfcLengthMeasure", "value": 0.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": 67.084977}, {"type": "IfcLengthMeasure", "value": -144.523266}, {"type": "IfcLengthMeasure", "value": 0.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": 1.477218}, {"type": "IfcLengthMeasure", "value": -153.498641}, {"type": "IfcLengthMeasure", "value": 0.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": -64.392137}, {"type": "IfcLengthMeasure", "value": -145.234375}, {"type": "IfcLengthMeasure", "value": 0.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": -128.935}, {"type": "IfcLengthMeasure", "value": -119.668008}, {"type": "IfcLengthMeasure", "value": 0.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": -185.4365}, {"type": "IfcLengthMeasure", "value": -81.474469}, {"type": "IfcLengthMeasure", "value": 0.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": -235.751609}, {"type": "IfcLengthMeasure", "value": -32.555805}, {"type": "IfcLengthMeasure", "value": 0.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": -275.439625}, {"type": "IfcLengthMeasure", "value": 22.660475}, {"type": "IfcLengthMeasure", "value": 0.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": -301.2465}, {"type": "IfcLengthMeasure", "value": 85.400219}, {"type": "IfcLengthMeasure", "value": 0.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": 0.0}, {"type": "IfcLengthMeasure", "value": 253.099266}, {"type": "IfcLengthMeasure", "value": 0.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": -65.777992}, {"type": "IfcLengthMeasure", "value": 249.952375}, {"type": "IfcLengthMeasure", "value": 0.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": -128.508695}, {"type": "IfcLengthMeasure", "value": 240.511688}, {"type": "IfcLengthMeasure", "value": 0.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": -189.983266}, {"type": "IfcLengthMeasure", "value": 222.998141}, {"type": "IfcLengthMeasure", "value": 0.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": -246.840234}, {"type": "IfcLengthMeasure", "value": 193.330969}, {"type": "IfcLengthMeasure", "value": 0.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": -286.93375}, {"type": "IfcLengthMeasure", "value": 143.116359}, {"type": "IfcLengthMeasure", "value": 0.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": -288.338563}, {"type": "IfcLengthMeasure", "value": 84.231891}, {"type": "IfcLengthMeasure", "value": 0.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": -263.388344}, {"type": "IfcLengthMeasure", "value": 25.178932}, {"type": "IfcLengthMeasure", "value": 0.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": -224.986906}, {"type": "IfcLengthMeasure", "value": -26.382564}, {"type": "IfcLengthMeasure", "value": 0.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": -176.642109}, {"type": "IfcLengthMeasure", "value": -71.667547}, {"type": "IfcLengthMeasure", "value": 0.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": -122.550633}, {"type": "IfcLengthMeasure", "value": -106.846461}, {"type": "IfcLengthMeasure", "value": 0.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": -61.391031}, {"type": "IfcLengthMeasure", "value": -130.155953}, {"type": "IfcLengthMeasure", "value": 0.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": 1.00923}, {"type": "IfcLengthMeasure", "value": -137.756953}, {"type": "IfcLengthMeasure", "value": 0.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": 63.202145}, {"type": "IfcLengthMeasure", "value": -129.69757}, {"type": "IfcLengthMeasure", "value": 0.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": 123.138398}, {"type": "IfcLengthMeasure", "value": -106.540977}, {"type": "IfcLengthMeasure", "value": 0.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": 176.955734}, {"type": "IfcLengthMeasure", "value": -71.42018}, {"type": "IfcLengthMeasure", "value": 0.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": 224.650078}, {"type": "IfcLengthMeasure", "value": -26.756678}, {"type": "IfcLengthMeasure", "value": 0.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": 262.387781}, {"type": "IfcLengthMeasure", "value": 23.516443}, {"type": "IfcLengthMeasure", "value": 0.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": 288.070906}, {"type": "IfcLengthMeasure", "value": 83.103938}, {"type": "IfcLengthMeasure", "value": 0.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": 286.93375}, {"type": "IfcLengthMeasure", "value": 143.116359}, {"type": "IfcLengthMeasure", "value": 0.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": 248.344641}, {"type": "IfcLengthMeasure", "value": 192.212875}, {"type": "IfcLengthMeasure", "value": 0.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": 191.622094}, {"type": "IfcLengthMeasure", "value": 222.376281}, {"type": "IfcLengthMeasure", "value": 0.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": 129.659992}, {"type": "IfcLengthMeasure", "value": 240.269531}, {"type": "IfcLengthMeasure", "value": 0.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": 64.742059}, {"type": "IfcLengthMeasure", "value": 250.052203}, {"type": "IfcLengthMeasure", "value": 0.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": -157.154922}, {"type": "IfcLengthMeasure", "value": 175.808609}, {"type": "IfcLengthMeasure", "value": -94.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": -136.207516}, {"type": "IfcLengthMeasure", "value": 207.772813}, {"type": "IfcLengthMeasure", "value": -94.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": -105.240203}, {"type": "IfcLengthMeasure", "value": 227.552281}, {"type": "IfcLengthMeasure", "value": -94.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": -71.061875}, {"type": "IfcLengthMeasure", "value": 239.383609}, {"type": "IfcLengthMeasure", "value": -94.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": -35.805801}, {"type": "IfcLengthMeasure", "value": 245.758375}, {"type": "IfcLengthMeasure", "value": -94.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": 1.198953}, {"type": "IfcLengthMeasure", "value": 247.790172}, {"type": "IfcLengthMeasure", "value": -94.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": 38.145594}, {"type": "IfcLengthMeasure", "value": 245.479016}, {"type": "IfcLengthMeasure", "value": -94.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": 73.227336}, {"type": "IfcLengthMeasure", "value": 238.824875}, {"type": "IfcLengthMeasure", "value": -94.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": 105.385414}, {"type": "IfcLengthMeasure", "value": 227.485469}, {"type": "IfcLengthMeasure", "value": -94.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": 135.792813}, {"type": "IfcLengthMeasure", "value": 208.145344}, {"type": "IfcLengthMeasure", "value": -94.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": 157.154922}, {"type": "IfcLengthMeasure", "value": 175.808609}, {"type": "IfcLengthMeasure", "value": -94.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": 157.742547}, {"type": "IfcLengthMeasure", "value": 136.356797}, {"type": "IfcLengthMeasure", "value": -94.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": 143.915969}, {"type": "IfcLengthMeasure", "value": 97.9355}, {"type": "IfcLengthMeasure", "value": -94.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": 123.422102}, {"type": "IfcLengthMeasure", "value": 65.13209}, {"type": "IfcLengthMeasure", "value": -94.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": 97.482477}, {"type": "IfcLengthMeasure", "value": 35.927559}, {"type": "IfcLengthMeasure", "value": -94.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": 68.171844}, {"type": "IfcLengthMeasure", "value": 12.864227}, {"type": "IfcLengthMeasure", "value": -94.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": 35.142449}, {"type": "IfcLengthMeasure", "value": -2.585266}, {"type": "IfcLengthMeasure", "value": -94.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": 0.77384}, {"type": "IfcLengthMeasure", "value": -8.021682}, {"type": "IfcLengthMeasure", "value": -94.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": -33.731801}, {"type": "IfcLengthMeasure", "value": -3.015985}, {"type": "IfcLengthMeasure", "value": -94.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": -67.542563}, {"type": "IfcLengthMeasure", "value": 12.469661}, {"type": "IfcLengthMeasure", "value": -94.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": -97.140859}, {"type": "IfcLengthMeasure", "value": 35.603637}, {"type": "IfcLengthMeasure", "value": -94.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": -123.498414}, {"type": "IfcLengthMeasure", "value": 65.233859}, {"type": "IfcLengthMeasure", "value": -94.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": -144.288969}, {"type": "IfcLengthMeasure", "value": 98.678578}, {"type": "IfcLengthMeasure", "value": -94.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": -157.807906}, {"type": "IfcLengthMeasure", "value": 136.680281}, {"type": "IfcLengthMeasure", "value": -94.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": -300.0}, {"type": "IfcLengthMeasure", "value": 150.0}, {"type": "IfcLengthMeasure", "value": 0.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": -228.577453}, {"type": "IfcLengthMeasure", "value": 162.904313}, {"type": "IfcLengthMeasure", "value": -47.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": -157.154922}, {"type": "IfcLengthMeasure", "value": 175.808609}, {"type": "IfcLengthMeasure", "value": -94.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": -260.012578}, {"type": "IfcLengthMeasure", "value": 202.771984}, {"type": "IfcLengthMeasure", "value": 0.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": -136.207516}, {"type": "IfcLengthMeasure", "value": 207.772813}, {"type": "IfcLengthMeasure", "value": -94.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": -200.897703}, {"type": "IfcLengthMeasure", "value": 235.427328}, {"type": "IfcLengthMeasure", "value": 0.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": -105.240203}, {"type": "IfcLengthMeasure", "value": 227.552281}, {"type": "IfcLengthMeasure", "value": -94.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": -135.653172}, {"type": "IfcLengthMeasure", "value": 254.960516}, {"type": "IfcLengthMeasure", "value": 0.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": -71.061875}, {"type": "IfcLengthMeasure", "value": 239.383609}, {"type": "IfcLengthMeasure", "value": -94.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": -68.351281}, {"type": "IfcLengthMeasure", "value": 265.485063}, {"type": "IfcLengthMeasure", "value": 0.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": -35.805801}, {"type": "IfcLengthMeasure", "value": 245.758375}, {"type": "IfcLengthMeasure", "value": -94.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": 2.288734}, {"type": "IfcLengthMeasure", "value": 268.839531}, {"type": "IfcLengthMeasure", "value": 0.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": 1.198953}, {"type": "IfcLengthMeasure", "value": 247.790172}, {"type": "IfcLengthMeasure", "value": -94.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": 72.81782}, {"type": "IfcLengthMeasure", "value": 265.023844}, {"type": "IfcLengthMeasure", "value": 0.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": 38.145594}, {"type": "IfcLengthMeasure", "value": 245.479016}, {"type": "IfcLengthMeasure", "value": -94.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": 139.786906}, {"type": "IfcLengthMeasure", "value": 254.038063}, {"type": "IfcLengthMeasure", "value": 0.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": 73.227336}, {"type": "IfcLengthMeasure", "value": 238.824875}, {"type": "IfcLengthMeasure", "value": -94.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": 201.174906}, {"type": "IfcLengthMeasure", "value": 235.317031}, {"type": "IfcLengthMeasure", "value": 0.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": 105.385414}, {"type": "IfcLengthMeasure", "value": 227.485469}, {"type": "IfcLengthMeasure", "value": -94.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": 259.220938}, {"type": "IfcLengthMeasure", "value": 203.387031}, {"type": "IfcLengthMeasure", "value": 0.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": 135.792813}, {"type": "IfcLengthMeasure", "value": 208.145344}, {"type": "IfcLengthMeasure", "value": -94.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": 300.0}, {"type": "IfcLengthMeasure", "value": 150.0}, {"type": "IfcLengthMeasure", "value": 0.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": 157.154922}, {"type": "IfcLengthMeasure", "value": 175.808609}, {"type": "IfcLengthMeasure", "value": -94.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": 301.12175}, {"type": "IfcLengthMeasure", "value": 84.866148}, {"type": "IfcLengthMeasure", "value": 0.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": 157.742547}, {"type": "IfcLengthMeasure", "value": 136.356797}, {"type": "IfcLengthMeasure", "value": -94.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": 274.727594}, {"type": "IfcLengthMeasure", "value": 21.433672}, {"type": "IfcLengthMeasure", "value": 0.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": 143.915969}, {"type": "IfcLengthMeasure", "value": 97.9355}, {"type": "IfcLengthMeasure", "value": -94.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": 235.605922}, {"type": "IfcLengthMeasure", "value": -32.723826}, {"type": "IfcLengthMeasure", "value": 0.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": 123.422102}, {"type": "IfcLengthMeasure", "value": 65.13209}, {"type": "IfcLengthMeasure", "value": -94.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": 186.088641}, {"type": "IfcLengthMeasure", "value": -80.939688}, {"type": "IfcLengthMeasure", "value": 0.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": 97.482477}, {"type": "IfcLengthMeasure", "value": 35.927559}, {"type": "IfcLengthMeasure", "value": -94.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": 130.136258}, {"type": "IfcLengthMeasure", "value": -119.016594}, {"type": "IfcLengthMeasure", "value": 0.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": 68.171844}, {"type": "IfcLengthMeasure", "value": 12.864227}, {"type": "IfcLengthMeasure", "value": -94.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": 67.084977}, {"type": "IfcLengthMeasure", "value": -144.523266}, {"type": "IfcLengthMeasure", "value": 0.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": 35.142449}, {"type": "IfcLengthMeasure", "value": -2.585266}, {"type": "IfcLengthMeasure", "value": -94.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": 1.477218}, {"type": "IfcLengthMeasure", "value": -153.498641}, {"type": "IfcLengthMeasure", "value": 0.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": 0.77384}, {"type": "IfcLengthMeasure", "value": -8.021682}, {"type": "IfcLengthMeasure", "value": -94.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": -64.392137}, {"type": "IfcLengthMeasure", "value": -145.234375}, {"type": "IfcLengthMeasure", "value": 0.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": -33.731801}, {"type": "IfcLengthMeasure", "value": -3.015985}, {"type": "IfcLengthMeasure", "value": -94.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": -128.935}, {"type": "IfcLengthMeasure", "value": -119.668008}, {"type": "IfcLengthMeasure", "value": 0.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": -67.542563}, {"type": "IfcLengthMeasure", "value": 12.469661}, {"type": "IfcLengthMeasure", "value": -94.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": -185.4365}, {"type": "IfcLengthMeasure", "value": -81.474469}, {"type": "IfcLengthMeasure", "value": 0.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": -97.140859}, {"type": "IfcLengthMeasure", "value": 35.603637}, {"type": "IfcLengthMeasure", "value": -94.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": -235.751609}, {"type": "IfcLengthMeasure", "value": -32.555805}, {"type": "IfcLengthMeasure", "value": 0.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": -123.498414}, {"type": "IfcLengthMeasure", "value": 65.233859}, {"type": "IfcLengthMeasure", "value": -94.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": -275.439625}, {"type": "IfcLengthMeasure", "value": 22.660475}, {"type": "IfcLengthMeasure", "value": 0.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": -144.288969}, {"type": "IfcLengthMeasure", "value": 98.678578}, {"type": "IfcLengthMeasure", "value": -94.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": -301.2465}, {"type": "IfcLengthMeasure", "value": 85.400219}, {"type": "IfcLengthMeasure", "value": 0.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": -157.807906}, {"type": "IfcLengthMeasure", "value": 136.680281}, {"type": "IfcLengthMeasure", "value": -94.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": -300.0}, {"type": "IfcLengthMeasure", "value": 150.0}, {"type": "IfcLengthMeasure", "value": 0.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": -228.577453}, {"type": "IfcLengthMeasure", "value": 162.904313}, {"type": "IfcLengthMeasure", "value": -47.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": -157.154922}, {"type": "IfcLengthMeasure", "value": 175.808609}, {"type": "IfcLengthMeasure", "value": -94.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": -103.357523}, {"type": "IfcLengthMeasure", "value": 247.172063}, {"type": "IfcLengthMeasure", "value": -47.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": -153.068953}, {"type": "IfcLengthMeasure", "value": 231.489813}, {"type": "IfcLengthMeasure", "value": -47.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": -52.078543}, {"type": "IfcLengthMeasure", "value": 255.621719}, {"type": "IfcLengthMeasure", "value": -47.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": 1.743843}, {"type": "IfcLengthMeasure", "value": 258.314844}, {"type": "IfcLengthMeasure", "value": -47.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": 55.481707}, {"type": "IfcLengthMeasure", "value": 255.251438}, {"type": "IfcLengthMeasure", "value": -47.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": 106.507117}, {"type": "IfcLengthMeasure", "value": 246.431469}, {"type": "IfcLengthMeasure", "value": -47.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": 197.506875}, {"type": "IfcLengthMeasure", "value": 205.766188}, {"type": "IfcLengthMeasure", "value": -47.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": 153.280156}, {"type": "IfcLengthMeasure", "value": 231.40125}, {"type": "IfcLengthMeasure", "value": -47.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": 228.577453}, {"type": "IfcLengthMeasure", "value": 162.904313}, {"type": "IfcLengthMeasure", "value": -47.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": 229.432141}, {"type": "IfcLengthMeasure", "value": 110.611469}, {"type": "IfcLengthMeasure", "value": -47.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": 209.321781}, {"type": "IfcLengthMeasure", "value": 59.684586}, {"type": "IfcLengthMeasure", "value": -47.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": 179.514016}, {"type": "IfcLengthMeasure", "value": 16.204132}, {"type": "IfcLengthMeasure", "value": -47.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": 141.785563}, {"type": "IfcLengthMeasure", "value": -22.506064}, {"type": "IfcLengthMeasure", "value": -47.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": 51.113715}, {"type": "IfcLengthMeasure", "value": -73.554266}, {"type": "IfcLengthMeasure", "value": -47.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": 99.154047}, {"type": "IfcLengthMeasure", "value": -53.076184}, {"type": "IfcLengthMeasure", "value": -47.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": 1.125529}, {"type": "IfcLengthMeasure", "value": -80.760164}, {"type": "IfcLengthMeasure", "value": -47.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": -49.061969}, {"type": "IfcLengthMeasure", "value": -74.12518}, {"type": "IfcLengthMeasure", "value": -47.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": -98.238781}, {"type": "IfcLengthMeasure", "value": -53.599176}, {"type": "IfcLengthMeasure", "value": -47.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": -141.288688}, {"type": "IfcLengthMeasure", "value": -22.935416}, {"type": "IfcLengthMeasure", "value": -47.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": -209.864297}, {"type": "IfcLengthMeasure", "value": 60.669523}, {"type": "IfcLengthMeasure", "value": -47.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": -179.625016}, {"type": "IfcLengthMeasure", "value": 16.339027}, {"type": "IfcLengthMeasure", "value": -47.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": -229.527203}, {"type": "IfcLengthMeasure", "value": 111.04025}, {"type": "IfcLengthMeasure", "value": -47.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": 0.0}, {"type": "IfcLengthMeasure", "value": 247.792422}, {"type": "IfcLengthMeasure", "value": -84.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": 35.45952}, {"type": "IfcLengthMeasure", "value": 245.798125}, {"type": "IfcLengthMeasure", "value": -84.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": 71.015367}, {"type": "IfcLengthMeasure", "value": 239.395359}, {"type": "IfcLengthMeasure", "value": -84.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": 104.952289}, {"type": "IfcLengthMeasure", "value": 227.684234}, {"type": "IfcLengthMeasure", "value": -84.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": 136.019484}, {"type": "IfcLengthMeasure", "value": 207.942281}, {"type": "IfcLengthMeasure", "value": -84.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": 157.154922}, {"type": "IfcLengthMeasure", "value": 175.808609}, {"type": "IfcLengthMeasure", "value": -84.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": 157.77775}, {"type": "IfcLengthMeasure", "value": 136.530484}, {"type": "IfcLengthMeasure", "value": -84.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": 143.710984}, {"type": "IfcLengthMeasure", "value": 97.530469}, {"type": "IfcLengthMeasure", "value": -84.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": 123.041867}, {"type": "IfcLengthMeasure", "value": 64.626715}, {"type": "IfcLengthMeasure", "value": -84.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": 96.919461}, {"type": "IfcLengthMeasure", "value": 35.394453}, {"type": "IfcLengthMeasure", "value": -84.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": 67.443461}, {"type": "IfcLengthMeasure", "value": 12.407895}, {"type": "IfcLengthMeasure", "value": -84.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": 34.616102}, {"type": "IfcLengthMeasure", "value": -2.748099}, {"type": "IfcLengthMeasure", "value": -84.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": 0.55276}, {"type": "IfcLengthMeasure", "value": -8.022964}, {"type": "IfcLengthMeasure", "value": -84.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": -33.624148}, {"type": "IfcLengthMeasure", "value": -3.048111}, {"type": "IfcLengthMeasure", "value": -84.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": -67.121539}, {"type": "IfcLengthMeasure", "value": 12.207951}, {"type": "IfcLengthMeasure", "value": -84.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": -96.747688}, {"type": "IfcLengthMeasure", "value": 35.232555}, {"type": "IfcLengthMeasure", "value": -84.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": -123.226352}, {"type": "IfcLengthMeasure", "value": 64.87157}, {"type": "IfcLengthMeasure", "value": -84.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": -144.259}, {"type": "IfcLengthMeasure", "value": 98.61857}, {"type": "IfcLengthMeasure", "value": -84.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": -157.924344}, {"type": "IfcLengthMeasure", "value": 137.268734}, {"type": "IfcLengthMeasure", "value": -84.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": -157.154922}, {"type": "IfcLengthMeasure", "value": 175.808609}, {"type": "IfcLengthMeasure", "value": -84.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": -135.195516}, {"type": "IfcLengthMeasure", "value": 208.674078}, {"type": "IfcLengthMeasure", "value": -84.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": -104.054703}, {"type": "IfcLengthMeasure", "value": 228.091234}, {"type": "IfcLengthMeasure", "value": -84.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": -70.384797}, {"type": "IfcLengthMeasure", "value": 239.553859}, {"type": "IfcLengthMeasure", "value": -84.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": -36.026906}, {"type": "IfcLengthMeasure", "value": 245.732781}, {"type": "IfcLengthMeasure", "value": -84.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": 0.0}, {"type": "IfcLengthMeasure", "value": 247.792422}, {"type": "IfcLengthMeasure", "value": -84.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": 0.0}, {"type": "IfcLengthMeasure", "value": 253.099266}, {"type": "IfcLengthMeasure", "value": 0.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": 64.742059}, {"type": "IfcLengthMeasure", "value": 250.052203}, {"type": "IfcLengthMeasure", "value": 0.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": 129.659992}, {"type": "IfcLengthMeasure", "value": 240.269531}, {"type": "IfcLengthMeasure", "value": 0.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": 191.622094}, {"type": "IfcLengthMeasure", "value": 222.376281}, {"type": "IfcLengthMeasure", "value": 0.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": 248.344641}, {"type": "IfcLengthMeasure", "value": 192.212875}, {"type": "IfcLengthMeasure", "value": 0.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": 286.93375}, {"type": "IfcLengthMeasure", "value": 143.116359}, {"type": "IfcLengthMeasure", "value": 0.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": 288.070906}, {"type": "IfcLengthMeasure", "value": 83.103938}, {"type": "IfcLengthMeasure", "value": 0.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": 262.387781}, {"type": "IfcLengthMeasure", "value": 23.516443}, {"type": "IfcLengthMeasure", "value": 0.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": 224.650078}, {"type": "IfcLengthMeasure", "value": -26.756678}, {"type": "IfcLengthMeasure", "value": 0.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": 176.955734}, {"type": "IfcLengthMeasure", "value": -71.42018}, {"type": "IfcLengthMeasure", "value": 0.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": 123.138398}, {"type": "IfcLengthMeasure", "value": -106.540977}, {"type": "IfcLengthMeasure", "value": 0.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": 63.202145}, {"type": "IfcLengthMeasure", "value": -129.69757}, {"type": "IfcLengthMeasure", "value": 0.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": 1.00923}, {"type": "IfcLengthMeasure", "value": -137.756953}, {"type": "IfcLengthMeasure", "value": 0.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": -61.391031}, {"type": "IfcLengthMeasure", "value": -130.155953}, {"type": "IfcLengthMeasure", "value": 0.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": -122.550633}, {"type": "IfcLengthMeasure", "value": -106.846461}, {"type": "IfcLengthMeasure", "value": 0.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": -176.642109}, {"type": "IfcLengthMeasure", "value": -71.667547}, {"type": "IfcLengthMeasure", "value": 0.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": -224.986906}, {"type": "IfcLengthMeasure", "value": -26.382564}, {"type": "IfcLengthMeasure", "value": 0.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": -263.388344}, {"type": "IfcLengthMeasure", "value": 25.178932}, {"type": "IfcLengthMeasure", "value": 0.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": -288.338563}, {"type": "IfcLengthMeasure", "value": 84.231891}, {"type": "IfcLengthMeasure", "value": 0.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": -286.93375}, {"type": "IfcLengthMeasure", "value": 143.116359}, {"type": "IfcLengthMeasure", "value": 0.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": -246.840234}, {"type": "IfcLengthMeasure", "value": 193.330969}, {"type": "IfcLengthMeasure", "value": 0.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": -189.983266}, {"type": "IfcLengthMeasure", "value": 222.998141}, {"type": "IfcLengthMeasure", "value": 0.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": -128.508695}, {"type": "IfcLengthMeasure", "value": 240.511688}, {"type": "IfcLengthMeasure", "value": 0.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": -65.777992}, {"type": "IfcLengthMeasure", "value": 249.952375}, {"type": "IfcLengthMeasure", "value": 0.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": 0.0}, {"type": "IfcLengthMeasure", "value": 253.099266}, {"type": "IfcLengthMeasure", "value": 0.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": 0.0}, {"type": "IfcLengthMeasure", "value": 247.792422}, {"type": "IfcLengthMeasure", "value": -84.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": 35.45952}, {"type": "IfcLengthMeasure", "value": 245.798125}, {"type": "IfcLengthMeasure", "value": -84.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": 71.015367}, {"type": "IfcLengthMeasure", "value": 239.395359}, {"type": "IfcLengthMeasure", "value": -84.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": 104.952289}, {"type": "IfcLengthMeasure", "value": 227.684234}, {"type": "IfcLengthMeasure", "value": -84.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": 136.019484}, {"type": "IfcLengthMeasure", "value": 207.942281}, {"type": "IfcLengthMeasure", "value": -84.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": 157.154922}, {"type": "IfcLengthMeasure", "value": 175.808609}, {"type": "IfcLengthMeasure", "value": -84.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": 157.77775}, {"type": "IfcLengthMeasure", "value": 136.530484}, {"type": "IfcLengthMeasure", "value": -84.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": 143.710984}, {"type": "IfcLengthMeasure", "value": 97.530469}, {"type": "IfcLengthMeasure", "value": -84.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": 123.041867}, {"type": "IfcLengthMeasure", "value": 64.626715}, {"type": "IfcLengthMeasure", "value": -84.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": 96.919461}, {"type": "IfcLengthMeasure", "value": 35.394453}, {"type": "IfcLengthMeasure", "value": -84.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": 67.443461}, {"type": "IfcLengthMeasure", "value": 12.407895}, {"type": "IfcLengthMeasure", "value": -84.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": 34.616102}, {"type": "IfcLengthMeasure", "value": -2.748099}, {"type": "IfcLengthMeasure", "value": -84.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": 0.55276}, {"type": "IfcLengthMeasure", "value": -8.022964}, {"type": "IfcLengthMeasure", "value": -84.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": -33.624148}, {"type": "IfcLengthMeasure", "value": -3.048111}, {"type": "IfcLengthMeasure", "value": -84.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": -67.121539}, {"type": "IfcLengthMeasure", "value": 12.207951}, {"type": "IfcLengthMeasure", "value": -84.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": -96.747688}, {"type": "IfcLengthMeasure", "value": 35.232555}, {"type": "IfcLengthMeasure", "value": -84.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": -123.226352}, {"type": "IfcLengthMeasure", "value": 64.87157}, {"type": "IfcLengthMeasure", "value": -84.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": -144.259}, {"type": "IfcLengthMeasure", "value": 98.61857}, {"type": "IfcLengthMeasure", "value": -84.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": -157.924344}, {"type": "IfcLengthMeasure", "value": 137.268734}, {"type": "IfcLengthMeasure", "value": -84.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": -157.154922}, {"type": "IfcLengthMeasure", "value": 175.808609}, {"type": "IfcLengthMeasure", "value": -84.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": -135.195516}, {"type": "IfcLengthMeasure", "value": 208.674078}, {"type": "IfcLengthMeasure", "value": -84.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": -104.054703}, {"type": "IfcLengthMeasure", "value": 228.091234}, {"type": "IfcLengthMeasure", "value": -84.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": -70.384797}, {"type": "IfcLengthMeasure", "value": 239.553859}, {"type": "IfcLengthMeasure", "value": -84.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": -36.026906}, {"type": "IfcLengthMeasure", "value": 245.732781}, {"type": "IfcLengthMeasure", "value": -84.0}]}]}, "closed": {"type": "IfcBoolean", "value": false}, "coordIndex": [{"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 28}, {"type": "IfcPositiveInteger", "value": 2}, {"type": "IfcPositiveInteger", "value": 29}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 1}, {"type": "IfcPositiveInteger", "value": 29}, {"type": "IfcPositiveInteger", "value": 2}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 30}, {"type": "IfcPositiveInteger", "value": 1}, {"type": "IfcPositiveInteger", "value": 24}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 29}, {"type": "IfcPositiveInteger", "value": 1}, {"type": "IfcPositiveInteger", "value": 30}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 24}, {"type": "IfcPositiveInteger", "value": 31}, {"type": "IfcPositiveInteger", "value": 30}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 3}, {"type": "IfcPositiveInteger", "value": 2}, {"type": "IfcPositiveInteger", "value": 28}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 5}, {"type": "IfcPositiveInteger", "value": 4}, {"type": "IfcPositiveInteger", "value": 27}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 6}, {"type": "IfcPositiveInteger", "value": 5}, {"type": "IfcPositiveInteger", "value": 25}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 25}, {"type": "IfcPositiveInteger", "value": 5}, {"type": "IfcPositiveInteger", "value": 26}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 4}, {"type": "IfcPositiveInteger", "value": 28}, {"type": "IfcPositiveInteger", "value": 27}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 5}, {"type": "IfcPositiveInteger", "value": 27}, {"type": "IfcPositiveInteger", "value": 26}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 3}, {"type": "IfcPositiveInteger", "value": 28}, {"type": "IfcPositiveInteger", "value": 4}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 23}, {"type": "IfcPositiveInteger", "value": 32}, {"type": "IfcPositiveInteger", "value": 31}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 33}, {"type": "IfcPositiveInteger", "value": 32}, {"type": "IfcPositiveInteger", "value": 23}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 24}, {"type": "IfcPositiveInteger", "value": 23}, {"type": "IfcPositiveInteger", "value": 31}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 34}, {"type": "IfcPositiveInteger", "value": 22}, {"type": "IfcPositiveInteger", "value": 21}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 23}, {"type": "IfcPositiveInteger", "value": 22}, {"type": "IfcPositiveInteger", "value": 33}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 22}, {"type": "IfcPositiveInteger", "value": 34}, {"type": "IfcPositiveInteger", "value": 33}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 21}, {"type": "IfcPositiveInteger", "value": 20}, {"type": "IfcPositiveInteger", "value": 35}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 36}, {"type": "IfcPositiveInteger", "value": 35}, {"type": "IfcPositiveInteger", "value": 20}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 34}, {"type": "IfcPositiveInteger", "value": 21}, {"type": "IfcPositiveInteger", "value": 35}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 37}, {"type": "IfcPositiveInteger", "value": 36}, {"type": "IfcPositiveInteger", "value": 19}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 20}, {"type": "IfcPositiveInteger", "value": 19}, {"type": "IfcPositiveInteger", "value": 36}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 18}, {"type": "IfcPositiveInteger", "value": 37}, {"type": "IfcPositiveInteger", "value": 19}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 7}, {"type": "IfcPositiveInteger", "value": 6}, {"type": "IfcPositiveInteger", "value": 48}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 8}, {"type": "IfcPositiveInteger", "value": 7}, {"type": "IfcPositiveInteger", "value": 47}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 7}, {"type": "IfcPositiveInteger", "value": 48}, {"type": "IfcPositiveInteger", "value": 47}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 8}, {"type": "IfcPositiveInteger", "value": 47}, {"type": "IfcPositiveInteger", "value": 46}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 46}, {"type": "IfcPositiveInteger", "value": 9}, {"type": "IfcPositiveInteger", "value": 8}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 46}, {"type": "IfcPositiveInteger", "value": 45}, {"type": "IfcPositiveInteger", "value": 10}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 11}, {"type": "IfcPositiveInteger", "value": 10}, {"type": "IfcPositiveInteger", "value": 45}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 12}, {"type": "IfcPositiveInteger", "value": 11}, {"type": "IfcPositiveInteger", "value": 44}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 45}, {"type": "IfcPositiveInteger", "value": 44}, {"type": "IfcPositiveInteger", "value": 11}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 10}, {"type": "IfcPositiveInteger", "value": 9}, {"type": "IfcPositiveInteger", "value": 46}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 12}, {"type": "IfcPositiveInteger", "value": 44}, {"type": "IfcPositiveInteger", "value": 43}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 15}, {"type": "IfcPositiveInteger", "value": 39}, {"type": "IfcPositiveInteger", "value": 16}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 40}, {"type": "IfcPositiveInteger", "value": 39}, {"type": "IfcPositiveInteger", "value": 15}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 38}, {"type": "IfcPositiveInteger", "value": 16}, {"type": "IfcPositiveInteger", "value": 39}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 18}, {"type": "IfcPositiveInteger", "value": 17}, {"type": "IfcPositiveInteger", "value": 37}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 16}, {"type": "IfcPositiveInteger", "value": 38}, {"type": "IfcPositiveInteger", "value": 17}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 17}, {"type": "IfcPositiveInteger", "value": 38}, {"type": "IfcPositiveInteger", "value": 37}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 13}, {"type": "IfcPositiveInteger", "value": 43}, {"type": "IfcPositiveInteger", "value": 42}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 12}, {"type": "IfcPositiveInteger", "value": 43}, {"type": "IfcPositiveInteger", "value": 13}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 14}, {"type": "IfcPositiveInteger", "value": 13}, {"type": "IfcPositiveInteger", "value": 42}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 15}, {"type": "IfcPositiveInteger", "value": 14}, {"type": "IfcPositiveInteger", "value": 40}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 14}, {"type": "IfcPositiveInteger", "value": 41}, {"type": "IfcPositiveInteger", "value": 40}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 42}, {"type": "IfcPositiveInteger", "value": 41}, {"type": "IfcPositiveInteger", "value": 14}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 48}, {"type": "IfcPositiveInteger", "value": 6}, {"type": "IfcPositiveInteger", "value": 25}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 50}, {"type": "IfcPositiveInteger", "value": 72}, {"type": "IfcPositiveInteger", "value": 49}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 51}, {"type": "IfcPositiveInteger", "value": 72}, {"type": "IfcPositiveInteger", "value": 50}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 71}, {"type": "IfcPositiveInteger", "value": 72}, {"type": "IfcPositiveInteger", "value": 52}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 51}, {"type": "IfcPositiveInteger", "value": 52}, {"type": "IfcPositiveInteger", "value": 72}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 53}, {"type": "IfcPositiveInteger", "value": 71}, {"type": "IfcPositiveInteger", "value": 52}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 69}, {"type": "IfcPositiveInteger", "value": 70}, {"type": "IfcPositiveInteger", "value": 63}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 71}, {"type": "IfcPositiveInteger", "value": 54}, {"type": "IfcPositiveInteger", "value": 70}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 66}, {"type": "IfcPositiveInteger", "value": 67}, {"type": "IfcPositiveInteger", "value": 65}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 67}, {"type": "IfcPositiveInteger", "value": 68}, {"type": "IfcPositiveInteger", "value": 65}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 68}, {"type": "IfcPositiveInteger", "value": 69}, {"type": "IfcPositiveInteger", "value": 64}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 71}, {"type": "IfcPositiveInteger", "value": 53}, {"type": "IfcPositiveInteger", "value": 54}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 54}, {"type": "IfcPositiveInteger", "value": 55}, {"type": "IfcPositiveInteger", "value": 61}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 55}, {"type": "IfcPositiveInteger", "value": 56}, {"type": "IfcPositiveInteger", "value": 61}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 58}, {"type": "IfcPositiveInteger", "value": 60}, {"type": "IfcPositiveInteger", "value": 57}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 60}, {"type": "IfcPositiveInteger", "value": 56}, {"type": "IfcPositiveInteger", "value": 57}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 59}, {"type": "IfcPositiveInteger", "value": 60}, {"type": "IfcPositiveInteger", "value": 58}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 65}, {"type": "IfcPositiveInteger", "value": 68}, {"type": "IfcPositiveInteger", "value": 64}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 69}, {"type": "IfcPositiveInteger", "value": 63}, {"type": "IfcPositiveInteger", "value": 64}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 62}, {"type": "IfcPositiveInteger", "value": 63}, {"type": "IfcPositiveInteger", "value": 70}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 62}, {"type": "IfcPositiveInteger", "value": 54}, {"type": "IfcPositiveInteger", "value": 61}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 61}, {"type": "IfcPositiveInteger", "value": 56}, {"type": "IfcPositiveInteger", "value": 60}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 62}, {"type": "IfcPositiveInteger", "value": 70}, {"type": "IfcPositiveInteger", "value": 54}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 74}, {"type": "IfcPositiveInteger", "value": 73}, {"type": "IfcPositiveInteger", "value": 76}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 80}, {"type": "IfcPositiveInteger", "value": 125}, {"type": "IfcPositiveInteger", "value": 126}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 126}, {"type": "IfcPositiveInteger", "value": 76}, {"type": "IfcPositiveInteger", "value": 78}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 126}, {"type": "IfcPositiveInteger", "value": 77}, {"type": "IfcPositiveInteger", "value": 76}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 76}, {"type": "IfcPositiveInteger", "value": 77}, {"type": "IfcPositiveInteger", "value": 74}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 82}, {"type": "IfcPositiveInteger", "value": 127}, {"type": "IfcPositiveInteger", "value": 125}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 127}, {"type": "IfcPositiveInteger", "value": 82}, {"type": "IfcPositiveInteger", "value": 84}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 127}, {"type": "IfcPositiveInteger", "value": 83}, {"type": "IfcPositiveInteger", "value": 81}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 125}, {"type": "IfcPositiveInteger", "value": 81}, {"type": "IfcPositiveInteger", "value": 79}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 128}, {"type": "IfcPositiveInteger", "value": 84}, {"type": "IfcPositiveInteger", "value": 129}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 88}, {"type": "IfcPositiveInteger", "value": 130}, {"type": "IfcPositiveInteger", "value": 86}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 92}, {"type": "IfcPositiveInteger", "value": 131}, {"type": "IfcPositiveInteger", "value": 90}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 90}, {"type": "IfcPositiveInteger", "value": 132}, {"type": "IfcPositiveInteger", "value": 88}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 94}, {"type": "IfcPositiveInteger", "value": 133}, {"type": "IfcPositiveInteger", "value": 92}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 96}, {"type": "IfcPositiveInteger", "value": 134}, {"type": "IfcPositiveInteger", "value": 94}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 98}, {"type": "IfcPositiveInteger", "value": 135}, {"type": "IfcPositiveInteger", "value": 96}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 128}, {"type": "IfcPositiveInteger", "value": 85}, {"type": "IfcPositiveInteger", "value": 83}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 77}, {"type": "IfcPositiveInteger", "value": 75}, {"type": "IfcPositiveInteger", "value": 74}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 77}, {"type": "IfcPositiveInteger", "value": 126}, {"type": "IfcPositiveInteger", "value": 79}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 85}, {"type": "IfcPositiveInteger", "value": 128}, {"type": "IfcPositiveInteger", "value": 87}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 87}, {"type": "IfcPositiveInteger", "value": 129}, {"type": "IfcPositiveInteger", "value": 89}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 131}, {"type": "IfcPositiveInteger", "value": 93}, {"type": "IfcPositiveInteger", "value": 132}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 134}, {"type": "IfcPositiveInteger", "value": 97}, {"type": "IfcPositiveInteger", "value": 133}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 97}, {"type": "IfcPositiveInteger", "value": 134}, {"type": "IfcPositiveInteger", "value": 99}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 133}, {"type": "IfcPositiveInteger", "value": 95}, {"type": "IfcPositiveInteger", "value": 131}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 132}, {"type": "IfcPositiveInteger", "value": 91}, {"type": "IfcPositiveInteger", "value": 130}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 135}, {"type": "IfcPositiveInteger", "value": 98}, {"type": "IfcPositiveInteger", "value": 136}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 102}, {"type": "IfcPositiveInteger", "value": 137}, {"type": "IfcPositiveInteger", "value": 100}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 106}, {"type": "IfcPositiveInteger", "value": 138}, {"type": "IfcPositiveInteger", "value": 104}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 104}, {"type": "IfcPositiveInteger", "value": 139}, {"type": "IfcPositiveInteger", "value": 102}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 137}, {"type": "IfcPositiveInteger", "value": 103}, {"type": "IfcPositiveInteger", "value": 136}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 108}, {"type": "IfcPositiveInteger", "value": 140}, {"type": "IfcPositiveInteger", "value": 106}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 138}, {"type": "IfcPositiveInteger", "value": 107}, {"type": "IfcPositiveInteger", "value": 139}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 139}, {"type": "IfcPositiveInteger", "value": 105}, {"type": "IfcPositiveInteger", "value": 137}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 99}, {"type": "IfcPositiveInteger", "value": 135}, {"type": "IfcPositiveInteger", "value": 101}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 141}, {"type": "IfcPositiveInteger", "value": 110}, {"type": "IfcPositiveInteger", "value": 112}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 114}, {"type": "IfcPositiveInteger", "value": 143}, {"type": "IfcPositiveInteger", "value": 142}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 141}, {"type": "IfcPositiveInteger", "value": 111}, {"type": "IfcPositiveInteger", "value": 109}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 110}, {"type": "IfcPositiveInteger", "value": 141}, {"type": "IfcPositiveInteger", "value": 140}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 118}, {"type": "IfcPositiveInteger", "value": 144}, {"type": "IfcPositiveInteger", "value": 145}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 120}, {"type": "IfcPositiveInteger", "value": 146}, {"type": "IfcPositiveInteger", "value": 144}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 116}, {"type": "IfcPositiveInteger", "value": 145}, {"type": "IfcPositiveInteger", "value": 143}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 122}, {"type": "IfcPositiveInteger", "value": 123}, {"type": "IfcPositiveInteger", "value": 146}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 140}, {"type": "IfcPositiveInteger", "value": 109}, {"type": "IfcPositiveInteger", "value": 138}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 111}, {"type": "IfcPositiveInteger", "value": 141}, {"type": "IfcPositiveInteger", "value": 142}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 113}, {"type": "IfcPositiveInteger", "value": 142}, {"type": "IfcPositiveInteger", "value": 143}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 145}, {"type": "IfcPositiveInteger", "value": 117}, {"type": "IfcPositiveInteger", "value": 115}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 146}, {"type": "IfcPositiveInteger", "value": 121}, {"type": "IfcPositiveInteger", "value": 119}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 123}, {"type": "IfcPositiveInteger", "value": 124}, {"type": "IfcPositiveInteger", "value": 121}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 144}, {"type": "IfcPositiveInteger", "value": 119}, {"type": "IfcPositiveInteger", "value": 117}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 148}, {"type": "IfcPositiveInteger", "value": 173}, {"type": "IfcPositiveInteger", "value": 172}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 149}, {"type": "IfcPositiveInteger", "value": 174}, {"type": "IfcPositiveInteger", "value": 173}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 151}, {"type": "IfcPositiveInteger", "value": 176}, {"type": "IfcPositiveInteger", "value": 175}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 152}, {"type": "IfcPositiveInteger", "value": 177}, {"type": "IfcPositiveInteger", "value": 176}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 150}, {"type": "IfcPositiveInteger", "value": 175}, {"type": "IfcPositiveInteger", "value": 174}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 154}, {"type": "IfcPositiveInteger", "value": 179}, {"type": "IfcPositiveInteger", "value": 178}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 155}, {"type": "IfcPositiveInteger", "value": 180}, {"type": "IfcPositiveInteger", "value": 179}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 157}, {"type": "IfcPositiveInteger", "value": 182}, {"type": "IfcPositiveInteger", "value": 181}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 158}, {"type": "IfcPositiveInteger", "value": 183}, {"type": "IfcPositiveInteger", "value": 182}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 156}, {"type": "IfcPositiveInteger", "value": 181}, {"type": "IfcPositiveInteger", "value": 180}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 153}, {"type": "IfcPositiveInteger", "value": 178}, {"type": "IfcPositiveInteger", "value": 177}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 160}, {"type": "IfcPositiveInteger", "value": 185}, {"type": "IfcPositiveInteger", "value": 159}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 161}, {"type": "IfcPositiveInteger", "value": 186}, {"type": "IfcPositiveInteger", "value": 160}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 163}, {"type": "IfcPositiveInteger", "value": 188}, {"type": "IfcPositiveInteger", "value": 162}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 164}, {"type": "IfcPositiveInteger", "value": 189}, {"type": "IfcPositiveInteger", "value": 163}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 162}, {"type": "IfcPositiveInteger", "value": 187}, {"type": "IfcPositiveInteger", "value": 161}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 166}, {"type": "IfcPositiveInteger", "value": 191}, {"type": "IfcPositiveInteger", "value": 165}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 167}, {"type": "IfcPositiveInteger", "value": 192}, {"type": "IfcPositiveInteger", "value": 166}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 169}, {"type": "IfcPositiveInteger", "value": 194}, {"type": "IfcPositiveInteger", "value": 168}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 171}, {"type": "IfcPositiveInteger", "value": 196}, {"type": "IfcPositiveInteger", "value": 170}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 170}, {"type": "IfcPositiveInteger", "value": 195}, {"type": "IfcPositiveInteger", "value": 169}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 168}, {"type": "IfcPositiveInteger", "value": 193}, {"type": "IfcPositiveInteger", "value": 167}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 165}, {"type": "IfcPositiveInteger", "value": 190}, {"type": "IfcPositiveInteger", "value": 164}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 159}, {"type": "IfcPositiveInteger", "value": 184}, {"type": "IfcPositiveInteger", "value": 183}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 217}, {"type": "IfcPositiveInteger", "value": 216}, {"type": "IfcPositiveInteger", "value": 215}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 217}, {"type": "IfcPositiveInteger", "value": 215}, {"type": "IfcPositiveInteger", "value": 218}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 220}, {"type": "IfcPositiveInteger", "value": 219}, {"type": "IfcPositiveInteger", "value": 214}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 215}, {"type": "IfcPositiveInteger", "value": 219}, {"type": "IfcPositiveInteger", "value": 218}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 197}, {"type": "IfcPositiveInteger", "value": 220}, {"type": "IfcPositiveInteger", "value": 214}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 214}, {"type": "IfcPositiveInteger", "value": 213}, {"type": "IfcPositiveInteger", "value": 197}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 219}, {"type": "IfcPositiveInteger", "value": 215}, {"type": "IfcPositiveInteger", "value": 214}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 210}, {"type": "IfcPositiveInteger", "value": 208}, {"type": "IfcPositiveInteger", "value": 211}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 213}, {"type": "IfcPositiveInteger", "value": 212}, {"type": "IfcPositiveInteger", "value": 205}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 212}, {"type": "IfcPositiveInteger", "value": 211}, {"type": "IfcPositiveInteger", "value": 207}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 197}, {"type": "IfcPositiveInteger", "value": 213}, {"type": "IfcPositiveInteger", "value": 205}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 198}, {"type": "IfcPositiveInteger", "value": 204}, {"type": "IfcPositiveInteger", "value": 199}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 200}, {"type": "IfcPositiveInteger", "value": 199}, {"type": "IfcPositiveInteger", "value": 203}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 203}, {"type": "IfcPositiveInteger", "value": 202}, {"type": "IfcPositiveInteger", "value": 201}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 200}, {"type": "IfcPositiveInteger", "value": 203}, {"type": "IfcPositiveInteger", "value": 201}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 203}, {"type": "IfcPositiveInteger", "value": 199}, {"type": "IfcPositiveInteger", "value": 204}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 209}, {"type": "IfcPositiveInteger", "value": 208}, {"type": "IfcPositiveInteger", "value": 210}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 208}, {"type": "IfcPositiveInteger", "value": 207}, {"type": "IfcPositiveInteger", "value": 211}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 206}, {"type": "IfcPositiveInteger", "value": 212}, {"type": "IfcPositiveInteger", "value": 207}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 212}, {"type": "IfcPositiveInteger", "value": 206}, {"type": "IfcPositiveInteger", "value": 205}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 197}, {"type": "IfcPositiveInteger", "value": 205}, {"type": "IfcPositiveInteger", "value": 204}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 197}, {"type": "IfcPositiveInteger", "value": 204}, {"type": "IfcPositiveInteger", "value": 198}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 80}, {"type": "IfcPositiveInteger", "value": 126}, {"type": "IfcPositiveInteger", "value": 78}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 82}, {"type": "IfcPositiveInteger", "value": 125}, {"type": "IfcPositiveInteger", "value": 80}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 127}, {"type": "IfcPositiveInteger", "value": 84}, {"type": "IfcPositiveInteger", "value": 128}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 127}, {"type": "IfcPositiveInteger", "value": 81}, {"type": "IfcPositiveInteger", "value": 125}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 125}, {"type": "IfcPositiveInteger", "value": 79}, {"type": "IfcPositiveInteger", "value": 126}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 84}, {"type": "IfcPositiveInteger", "value": 86}, {"type": "IfcPositiveInteger", "value": 129}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 130}, {"type": "IfcPositiveInteger", "value": 129}, {"type": "IfcPositiveInteger", "value": 86}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 131}, {"type": "IfcPositiveInteger", "value": 132}, {"type": "IfcPositiveInteger", "value": 90}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 132}, {"type": "IfcPositiveInteger", "value": 130}, {"type": "IfcPositiveInteger", "value": 88}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 133}, {"type": "IfcPositiveInteger", "value": 131}, {"type": "IfcPositiveInteger", "value": 92}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 134}, {"type": "IfcPositiveInteger", "value": 133}, {"type": "IfcPositiveInteger", "value": 94}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 135}, {"type": "IfcPositiveInteger", "value": 134}, {"type": "IfcPositiveInteger", "value": 96}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 128}, {"type": "IfcPositiveInteger", "value": 83}, {"type": "IfcPositiveInteger", "value": 127}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 128}, {"type": "IfcPositiveInteger", "value": 129}, {"type": "IfcPositiveInteger", "value": 87}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 129}, {"type": "IfcPositiveInteger", "value": 130}, {"type": "IfcPositiveInteger", "value": 89}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 93}, {"type": "IfcPositiveInteger", "value": 91}, {"type": "IfcPositiveInteger", "value": 132}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 97}, {"type": "IfcPositiveInteger", "value": 95}, {"type": "IfcPositiveInteger", "value": 133}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 134}, {"type": "IfcPositiveInteger", "value": 135}, {"type": "IfcPositiveInteger", "value": 99}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 95}, {"type": "IfcPositiveInteger", "value": 93}, {"type": "IfcPositiveInteger", "value": 131}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 91}, {"type": "IfcPositiveInteger", "value": 89}, {"type": "IfcPositiveInteger", "value": 130}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 98}, {"type": "IfcPositiveInteger", "value": 100}, {"type": "IfcPositiveInteger", "value": 136}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 137}, {"type": "IfcPositiveInteger", "value": 136}, {"type": "IfcPositiveInteger", "value": 100}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 138}, {"type": "IfcPositiveInteger", "value": 139}, {"type": "IfcPositiveInteger", "value": 104}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 139}, {"type": "IfcPositiveInteger", "value": 137}, {"type": "IfcPositiveInteger", "value": 102}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 103}, {"type": "IfcPositiveInteger", "value": 101}, {"type": "IfcPositiveInteger", "value": 136}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 140}, {"type": "IfcPositiveInteger", "value": 138}, {"type": "IfcPositiveInteger", "value": 106}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 107}, {"type": "IfcPositiveInteger", "value": 105}, {"type": "IfcPositiveInteger", "value": 139}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 105}, {"type": "IfcPositiveInteger", "value": 103}, {"type": "IfcPositiveInteger", "value": 137}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 135}, {"type": "IfcPositiveInteger", "value": 136}, {"type": "IfcPositiveInteger", "value": 101}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 141}, {"type": "IfcPositiveInteger", "value": 112}, {"type": "IfcPositiveInteger", "value": 142}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 114}, {"type": "IfcPositiveInteger", "value": 142}, {"type": "IfcPositiveInteger", "value": 112}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 141}, {"type": "IfcPositiveInteger", "value": 109}, {"type": "IfcPositiveInteger", "value": 140}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 110}, {"type": "IfcPositiveInteger", "value": 140}, {"type": "IfcPositiveInteger", "value": 108}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 118}, {"type": "IfcPositiveInteger", "value": 145}, {"type": "IfcPositiveInteger", "value": 116}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 120}, {"type": "IfcPositiveInteger", "value": 144}, {"type": "IfcPositiveInteger", "value": 118}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 116}, {"type": "IfcPositiveInteger", "value": 143}, {"type": "IfcPositiveInteger", "value": 114}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 122}, {"type": "IfcPositiveInteger", "value": 146}, {"type": "IfcPositiveInteger", "value": 120}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 109}, {"type": "IfcPositiveInteger", "value": 107}, {"type": "IfcPositiveInteger", "value": 138}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 111}, {"type": "IfcPositiveInteger", "value": 142}, {"type": "IfcPositiveInteger", "value": 113}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 113}, {"type": "IfcPositiveInteger", "value": 143}, {"type": "IfcPositiveInteger", "value": 115}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 145}, {"type": "IfcPositiveInteger", "value": 115}, {"type": "IfcPositiveInteger", "value": 143}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 146}, {"type": "IfcPositiveInteger", "value": 119}, {"type": "IfcPositiveInteger", "value": 144}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 123}, {"type": "IfcPositiveInteger", "value": 121}, {"type": "IfcPositiveInteger", "value": 146}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 144}, {"type": "IfcPositiveInteger", "value": 117}, {"type": "IfcPositiveInteger", "value": 145}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 148}, {"type": "IfcPositiveInteger", "value": 172}, {"type": "IfcPositiveInteger", "value": 147}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 149}, {"type": "IfcPositiveInteger", "value": 173}, {"type": "IfcPositiveInteger", "value": 148}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 151}, {"type": "IfcPositiveInteger", "value": 175}, {"type": "IfcPositiveInteger", "value": 150}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 152}, {"type": "IfcPositiveInteger", "value": 176}, {"type": "IfcPositiveInteger", "value": 151}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 150}, {"type": "IfcPositiveInteger", "value": 174}, {"type": "IfcPositiveInteger", "value": 149}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 154}, {"type": "IfcPositiveInteger", "value": 178}, {"type": "IfcPositiveInteger", "value": 153}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 155}, {"type": "IfcPositiveInteger", "value": 179}, {"type": "IfcPositiveInteger", "value": 154}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 157}, {"type": "IfcPositiveInteger", "value": 181}, {"type": "IfcPositiveInteger", "value": 156}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 158}, {"type": "IfcPositiveInteger", "value": 182}, {"type": "IfcPositiveInteger", "value": 157}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 156}, {"type": "IfcPositiveInteger", "value": 180}, {"type": "IfcPositiveInteger", "value": 155}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 153}, {"type": "IfcPositiveInteger", "value": 177}, {"type": "IfcPositiveInteger", "value": 152}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 185}, {"type": "IfcPositiveInteger", "value": 184}, {"type": "IfcPositiveInteger", "value": 159}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 186}, {"type": "IfcPositiveInteger", "value": 185}, {"type": "IfcPositiveInteger", "value": 160}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 188}, {"type": "IfcPositiveInteger", "value": 187}, {"type": "IfcPositiveInteger", "value": 162}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 189}, {"type": "IfcPositiveInteger", "value": 188}, {"type": "IfcPositiveInteger", "value": 163}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 187}, {"type": "IfcPositiveInteger", "value": 186}, {"type": "IfcPositiveInteger", "value": 161}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 191}, {"type": "IfcPositiveInteger", "value": 190}, {"type": "IfcPositiveInteger", "value": 165}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 192}, {"type": "IfcPositiveInteger", "value": 191}, {"type": "IfcPositiveInteger", "value": 166}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 194}, {"type": "IfcPositiveInteger", "value": 193}, {"type": "IfcPositiveInteger", "value": 168}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 196}, {"type": "IfcPositiveInteger", "value": 195}, {"type": "IfcPositiveInteger", "value": 170}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 195}, {"type": "IfcPositiveInteger", "value": 194}, {"type": "IfcPositiveInteger", "value": 169}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 193}, {"type": "IfcPositiveInteger", "value": 192}, {"type": "IfcPositiveInteger", "value": 167}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 190}, {"type": "IfcPositiveInteger", "value": 189}, {"type": "IfcPositiveInteger", "value": 164}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 159}, {"type": "IfcPositiveInteger", "value": 183}, {"type": "IfcPositiveInteger", "value": 158}]}]}]}}], "predefinedType": "WASHHANDBASIN"}}], "objectPlacement": {"type": "IfcLocalPlacement", "globalId": "b2750739-412a-41f0-be15-53a43870b5bb", "placementRelTo": "8e7bb3b2-dab2-4c61-b2be-d6c4700dd97d", "relativePlacement": {"type": "IfcAxis2Placement3D", "location": {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": 0.0}, {"type": "IfcLengthMeasure", "value": 0.0}, {"type": "IfcLengthMeasure", "value": 0.0}]}}}, "representation": {"type": "IfcProductDefinitionShape", "representations": [{"type": "IfcShapeRepresentation", "contextOfItems": "5c4747f7-3260-41e7-8816-6a7d4ec258cb", "representationIdentifier": {"type": "IfcLabel", "value": "Body"}, "representationType": {"type": "IfcLabel", "value": "MappedRepresentation"}, "items": [{"type": "IfcMappedItem", "mappingSource": {"type": "IfcRepresentationMap", "mappingOrigin": {"type": "IfcAxis2Placement3D", "location": {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": 0.0}, {"type": "IfcLengthMeasure", "value": 0.0}, {"type": "IfcLengthMeasure", "value": 0.0}]}}, "mappedRepresentation": {"type": "IfcShapeRepresentation", "contextOfItems": "5c4747f7-3260-41e7-8816-6a7d4ec258cb", "representationIdentifier": {"type": "IfcLabel", "value": "Body"}, "representationType": {"type": "IfcLabel", "value": "Tessellation"}, "items": [{"type": "IfcTriangulatedFaceSet", "coordinates": "9f9e7f21-149f-44bb-9c8d-b8f6df6e7469", "closed": {"type": "IfcBoolean", "value": false}, "coordIndex": [{"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 28}, {"type": "IfcPositiveInteger", "value": 2}, {"type": "IfcPositiveInteger", "value": 29}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 1}, {"type": "IfcPositiveInteger", "value": 29}, {"type": "IfcPositiveInteger", "value": 2}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 30}, {"type": "IfcPositiveInteger", "value": 1}, {"type": "IfcPositiveInteger", "value": 24}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 29}, {"type": "IfcPositiveInteger", "value": 1}, {"type": "IfcPositiveInteger", "value": 30}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 24}, {"type": "IfcPositiveInteger", "value": 31}, {"type": "IfcPositiveInteger", "value": 30}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 3}, {"type": "IfcPositiveInteger", "value": 2}, {"type": "IfcPositiveInteger", "value": 28}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 5}, {"type": "IfcPositiveInteger", "value": 4}, {"type": "IfcPositiveInteger", "value": 27}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 6}, {"type": "IfcPositiveInteger", "value": 5}, {"type": "IfcPositiveInteger", "value": 25}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 25}, {"type": "IfcPositiveInteger", "value": 5}, {"type": "IfcPositiveInteger", "value": 26}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 4}, {"type": "IfcPositiveInteger", "value": 28}, {"type": "IfcPositiveInteger", "value": 27}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 5}, {"type": "IfcPositiveInteger", "value": 27}, {"type": "IfcPositiveInteger", "value": 26}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 3}, {"type": "IfcPositiveInteger", "value": 28}, {"type": "IfcPositiveInteger", "value": 4}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 23}, {"type": "IfcPositiveInteger", "value": 32}, {"type": "IfcPositiveInteger", "value": 31}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 33}, {"type": "IfcPositiveInteger", "value": 32}, {"type": "IfcPositiveInteger", "value": 23}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 24}, {"type": "IfcPositiveInteger", "value": 23}, {"type": "IfcPositiveInteger", "value": 31}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 34}, {"type": "IfcPositiveInteger", "value": 22}, {"type": "IfcPositiveInteger", "value": 21}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 23}, {"type": "IfcPositiveInteger", "value": 22}, {"type": "IfcPositiveInteger", "value": 33}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 22}, {"type": "IfcPositiveInteger", "value": 34}, {"type": "IfcPositiveInteger", "value": 33}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 21}, {"type": "IfcPositiveInteger", "value": 20}, {"type": "IfcPositiveInteger", "value": 35}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 36}, {"type": "IfcPositiveInteger", "value": 35}, {"type": "IfcPositiveInteger", "value": 20}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 34}, {"type": "IfcPositiveInteger", "value": 21}, {"type": "IfcPositiveInteger", "value": 35}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 37}, {"type": "IfcPositiveInteger", "value": 36}, {"type": "IfcPositiveInteger", "value": 19}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 20}, {"type": "IfcPositiveInteger", "value": 19}, {"type": "IfcPositiveInteger", "value": 36}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 18}, {"type": "IfcPositiveInteger", "value": 37}, {"type": "IfcPositiveInteger", "value": 19}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 7}, {"type": "IfcPositiveInteger", "value": 6}, {"type": "IfcPositiveInteger", "value": 48}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 8}, {"type": "IfcPositiveInteger", "value": 7}, {"type": "IfcPositiveInteger", "value": 47}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 7}, {"type": "IfcPositiveInteger", "value": 48}, {"type": "IfcPositiveInteger", "value": 47}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 8}, {"type": "IfcPositiveInteger", "value": 47}, {"type": "IfcPositiveInteger", "value": 46}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 46}, {"type": "IfcPositiveInteger", "value": 9}, {"type": "IfcPositiveInteger", "value": 8}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 46}, {"type": "IfcPositiveInteger", "value": 45}, {"type": "IfcPositiveInteger", "value": 10}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 11}, {"type": "IfcPositiveInteger", "value": 10}, {"type": "IfcPositiveInteger", "value": 45}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 12}, {"type": "IfcPositiveInteger", "value": 11}, {"type": "IfcPositiveInteger", "value": 44}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 45}, {"type": "IfcPositiveInteger", "value": 44}, {"type": "IfcPositiveInteger", "value": 11}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 10}, {"type": "IfcPositiveInteger", "value": 9}, {"type": "IfcPositiveInteger", "value": 46}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 12}, {"type": "IfcPositiveInteger", "value": 44}, {"type": "IfcPositiveInteger", "value": 43}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 15}, {"type": "IfcPositiveInteger", "value": 39}, {"type": "IfcPositiveInteger", "value": 16}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 40}, {"type": "IfcPositiveInteger", "value": 39}, {"type": "IfcPositiveInteger", "value": 15}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 38}, {"type": "IfcPositiveInteger", "value": 16}, {"type": "IfcPositiveInteger", "value": 39}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 18}, {"type": "IfcPositiveInteger", "value": 17}, {"type": "IfcPositiveInteger", "value": 37}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 16}, {"type": "IfcPositiveInteger", "value": 38}, {"type": "IfcPositiveInteger", "value": 17}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 17}, {"type": "IfcPositiveInteger", "value": 38}, {"type": "IfcPositiveInteger", "value": 37}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 13}, {"type": "IfcPositiveInteger", "value": 43}, {"type": "IfcPositiveInteger", "value": 42}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 12}, {"type": "IfcPositiveInteger", "value": 43}, {"type": "IfcPositiveInteger", "value": 13}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 14}, {"type": "IfcPositiveInteger", "value": 13}, {"type": "IfcPositiveInteger", "value": 42}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 15}, {"type": "IfcPositiveInteger", "value": 14}, {"type": "IfcPositiveInteger", "value": 40}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 14}, {"type": "IfcPositiveInteger", "value": 41}, {"type": "IfcPositiveInteger", "value": 40}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 42}, {"type": "IfcPositiveInteger", "value": 41}, {"type": "IfcPositiveInteger", "value": 14}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 48}, {"type": "IfcPositiveInteger", "value": 6}, {"type": "IfcPositiveInteger", "value": 25}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 50}, {"type": "IfcPositiveInteger", "value": 72}, {"type": "IfcPositiveInteger", "value": 49}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 51}, {"type": "IfcPositiveInteger", "value": 72}, {"type": "IfcPositiveInteger", "value": 50}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 71}, {"type": "IfcPositiveInteger", "value": 72}, {"type": "IfcPositiveInteger", "value": 52}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 51}, {"type": "IfcPositiveInteger", "value": 52}, {"type": "IfcPositiveInteger", "value": 72}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 53}, {"type": "IfcPositiveInteger", "value": 71}, {"type": "IfcPositiveInteger", "value": 52}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 69}, {"type": "IfcPositiveInteger", "value": 70}, {"type": "IfcPositiveInteger", "value": 63}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 71}, {"type": "IfcPositiveInteger", "value": 54}, {"type": "IfcPositiveInteger", "value": 70}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 66}, {"type": "IfcPositiveInteger", "value": 67}, {"type": "IfcPositiveInteger", "value": 65}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 67}, {"type": "IfcPositiveInteger", "value": 68}, {"type": "IfcPositiveInteger", "value": 65}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 68}, {"type": "IfcPositiveInteger", "value": 69}, {"type": "IfcPositiveInteger", "value": 64}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 71}, {"type": "IfcPositiveInteger", "value": 53}, {"type": "IfcPositiveInteger", "value": 54}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 54}, {"type": "IfcPositiveInteger", "value": 55}, {"type": "IfcPositiveInteger", "value": 61}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 55}, {"type": "IfcPositiveInteger", "value": 56}, {"type": "IfcPositiveInteger", "value": 61}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 58}, {"type": "IfcPositiveInteger", "value": 60}, {"type": "IfcPositiveInteger", "value": 57}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 60}, {"type": "IfcPositiveInteger", "value": 56}, {"type": "IfcPositiveInteger", "value": 57}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 59}, {"type": "IfcPositiveInteger", "value": 60}, {"type": "IfcPositiveInteger", "value": 58}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 65}, {"type": "IfcPositiveInteger", "value": 68}, {"type": "IfcPositiveInteger", "value": 64}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 69}, {"type": "IfcPositiveInteger", "value": 63}, {"type": "IfcPositiveInteger", "value": 64}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 62}, {"type": "IfcPositiveInteger", "value": 63}, {"type": "IfcPositiveInteger", "value": 70}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 62}, {"type": "IfcPositiveInteger", "value": 54}, {"type": "IfcPositiveInteger", "value": 61}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 61}, {"type": "IfcPositiveInteger", "value": 56}, {"type": "IfcPositiveInteger", "value": 60}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 62}, {"type": "IfcPositiveInteger", "value": 70}, {"type": "IfcPositiveInteger", "value": 54}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 74}, {"type": "IfcPositiveInteger", "value": 73}, {"type": "IfcPositiveInteger", "value": 76}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 80}, {"type": "IfcPositiveInteger", "value": 125}, {"type": "IfcPositiveInteger", "value": 126}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 126}, {"type": "IfcPositiveInteger", "value": 76}, {"type": "IfcPositiveInteger", "value": 78}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 126}, {"type": "IfcPositiveInteger", "value": 77}, {"type": "IfcPositiveInteger", "value": 76}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 76}, {"type": "IfcPositiveInteger", "value": 77}, {"type": "IfcPositiveInteger", "value": 74}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 82}, {"type": "IfcPositiveInteger", "value": 127}, {"type": "IfcPositiveInteger", "value": 125}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 127}, {"type": "IfcPositiveInteger", "value": 82}, {"type": "IfcPositiveInteger", "value": 84}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 127}, {"type": "IfcPositiveInteger", "value": 83}, {"type": "IfcPositiveInteger", "value": 81}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 125}, {"type": "IfcPositiveInteger", "value": 81}, {"type": "IfcPositiveInteger", "value": 79}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 128}, {"type": "IfcPositiveInteger", "value": 84}, {"type": "IfcPositiveInteger", "value": 129}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 88}, {"type": "IfcPositiveInteger", "value": 130}, {"type": "IfcPositiveInteger", "value": 86}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 92}, {"type": "IfcPositiveInteger", "value": 131}, {"type": "IfcPositiveInteger", "value": 90}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 90}, {"type": "IfcPositiveInteger", "value": 132}, {"type": "IfcPositiveInteger", "value": 88}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 94}, {"type": "IfcPositiveInteger", "value": 133}, {"type": "IfcPositiveInteger", "value": 92}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 96}, {"type": "IfcPositiveInteger", "value": 134}, {"type": "IfcPositiveInteger", "value": 94}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 98}, {"type": "IfcPositiveInteger", "value": 135}, {"type": "IfcPositiveInteger", "value": 96}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 128}, {"type": "IfcPositiveInteger", "value": 85}, {"type": "IfcPositiveInteger", "value": 83}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 77}, {"type": "IfcPositiveInteger", "value": 75}, {"type": "IfcPositiveInteger", "value": 74}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 77}, {"type": "IfcPositiveInteger", "value": 126}, {"type": "IfcPositiveInteger", "value": 79}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 85}, {"type": "IfcPositiveInteger", "value": 128}, {"type": "IfcPositiveInteger", "value": 87}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 87}, {"type": "IfcPositiveInteger", "value": 129}, {"type": "IfcPositiveInteger", "value": 89}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 131}, {"type": "IfcPositiveInteger", "value": 93}, {"type": "IfcPositiveInteger", "value": 132}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 134}, {"type": "IfcPositiveInteger", "value": 97}, {"type": "IfcPositiveInteger", "value": 133}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 97}, {"type": "IfcPositiveInteger", "value": 134}, {"type": "IfcPositiveInteger", "value": 99}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 133}, {"type": "IfcPositiveInteger", "value": 95}, {"type": "IfcPositiveInteger", "value": 131}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 132}, {"type": "IfcPositiveInteger", "value": 91}, {"type": "IfcPositiveInteger", "value": 130}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 135}, {"type": "IfcPositiveInteger", "value": 98}, {"type": "IfcPositiveInteger", "value": 136}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 102}, {"type": "IfcPositiveInteger", "value": 137}, {"type": "IfcPositiveInteger", "value": 100}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 106}, {"type": "IfcPositiveInteger", "value": 138}, {"type": "IfcPositiveInteger", "value": 104}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 104}, {"type": "IfcPositiveInteger", "value": 139}, {"type": "IfcPositiveInteger", "value": 102}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 137}, {"type": "IfcPositiveInteger", "value": 103}, {"type": "IfcPositiveInteger", "value": 136}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 108}, {"type": "IfcPositiveInteger", "value": 140}, {"type": "IfcPositiveInteger", "value": 106}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 138}, {"type": "IfcPositiveInteger", "value": 107}, {"type": "IfcPositiveInteger", "value": 139}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 139}, {"type": "IfcPositiveInteger", "value": 105}, {"type": "IfcPositiveInteger", "value": 137}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 99}, {"type": "IfcPositiveInteger", "value": 135}, {"type": "IfcPositiveInteger", "value": 101}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 141}, {"type": "IfcPositiveInteger", "value": 110}, {"type": "IfcPositiveInteger", "value": 112}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 114}, {"type": "IfcPositiveInteger", "value": 143}, {"type": "IfcPositiveInteger", "value": 142}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 141}, {"type": "IfcPositiveInteger", "value": 111}, {"type": "IfcPositiveInteger", "value": 109}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 110}, {"type": "IfcPositiveInteger", "value": 141}, {"type": "IfcPositiveInteger", "value": 140}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 118}, {"type": "IfcPositiveInteger", "value": 144}, {"type": "IfcPositiveInteger", "value": 145}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 120}, {"type": "IfcPositiveInteger", "value": 146}, {"type": "IfcPositiveInteger", "value": 144}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 116}, {"type": "IfcPositiveInteger", "value": 145}, {"type": "IfcPositiveInteger", "value": 143}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 122}, {"type": "IfcPositiveInteger", "value": 123}, {"type": "IfcPositiveInteger", "value": 146}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 140}, {"type": "IfcPositiveInteger", "value": 109}, {"type": "IfcPositiveInteger", "value": 138}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 111}, {"type": "IfcPositiveInteger", "value": 141}, {"type": "IfcPositiveInteger", "value": 142}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 113}, {"type": "IfcPositiveInteger", "value": 142}, {"type": "IfcPositiveInteger", "value": 143}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 145}, {"type": "IfcPositiveInteger", "value": 117}, {"type": "IfcPositiveInteger", "value": 115}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 146}, {"type": "IfcPositiveInteger", "value": 121}, {"type": "IfcPositiveInteger", "value": 119}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 123}, {"type": "IfcPositiveInteger", "value": 124}, {"type": "IfcPositiveInteger", "value": 121}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 144}, {"type": "IfcPositiveInteger", "value": 119}, {"type": "IfcPositiveInteger", "value": 117}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 148}, {"type": "IfcPositiveInteger", "value": 173}, {"type": "IfcPositiveInteger", "value": 172}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 149}, {"type": "IfcPositiveInteger", "value": 174}, {"type": "IfcPositiveInteger", "value": 173}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 151}, {"type": "IfcPositiveInteger", "value": 176}, {"type": "IfcPositiveInteger", "value": 175}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 152}, {"type": "IfcPositiveInteger", "value": 177}, {"type": "IfcPositiveInteger", "value": 176}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 150}, {"type": "IfcPositiveInteger", "value": 175}, {"type": "IfcPositiveInteger", "value": 174}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 154}, {"type": "IfcPositiveInteger", "value": 179}, {"type": "IfcPositiveInteger", "value": 178}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 155}, {"type": "IfcPositiveInteger", "value": 180}, {"type": "IfcPositiveInteger", "value": 179}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 157}, {"type": "IfcPositiveInteger", "value": 182}, {"type": "IfcPositiveInteger", "value": 181}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 158}, {"type": "IfcPositiveInteger", "value": 183}, {"type": "IfcPositiveInteger", "value": 182}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 156}, {"type": "IfcPositiveInteger", "value": 181}, {"type": "IfcPositiveInteger", "value": 180}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 153}, {"type": "IfcPositiveInteger", "value": 178}, {"type": "IfcPositiveInteger", "value": 177}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 160}, {"type": "IfcPositiveInteger", "value": 185}, {"type": "IfcPositiveInteger", "value": 159}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 161}, {"type": "IfcPositiveInteger", "value": 186}, {"type": "IfcPositiveInteger", "value": 160}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 163}, {"type": "IfcPositiveInteger", "value": 188}, {"type": "IfcPositiveInteger", "value": 162}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 164}, {"type": "IfcPositiveInteger", "value": 189}, {"type": "IfcPositiveInteger", "value": 163}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 162}, {"type": "IfcPositiveInteger", "value": 187}, {"type": "IfcPositiveInteger", "value": 161}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 166}, {"type": "IfcPositiveInteger", "value": 191}, {"type": "IfcPositiveInteger", "value": 165}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 167}, {"type": "IfcPositiveInteger", "value": 192}, {"type": "IfcPositiveInteger", "value": 166}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 169}, {"type": "IfcPositiveInteger", "value": 194}, {"type": "IfcPositiveInteger", "value": 168}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 171}, {"type": "IfcPositiveInteger", "value": 196}, {"type": "IfcPositiveInteger", "value": 170}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 170}, {"type": "IfcPositiveInteger", "value": 195}, {"type": "IfcPositiveInteger", "value": 169}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 168}, {"type": "IfcPositiveInteger", "value": 193}, {"type": "IfcPositiveInteger", "value": 167}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 165}, {"type": "IfcPositiveInteger", "value": 190}, {"type": "IfcPositiveInteger", "value": 164}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 159}, {"type": "IfcPositiveInteger", "value": 184}, {"type": "IfcPositiveInteger", "value": 183}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 217}, {"type": "IfcPositiveInteger", "value": 216}, {"type": "IfcPositiveInteger", "value": 215}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 217}, {"type": "IfcPositiveInteger", "value": 215}, {"type": "IfcPositiveInteger", "value": 218}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 220}, {"type": "IfcPositiveInteger", "value": 219}, {"type": "IfcPositiveInteger", "value": 214}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 215}, {"type": "IfcPositiveInteger", "value": 219}, {"type": "IfcPositiveInteger", "value": 218}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 197}, {"type": "IfcPositiveInteger", "value": 220}, {"type": "IfcPositiveInteger", "value": 214}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 214}, {"type": "IfcPositiveInteger", "value": 213}, {"type": "IfcPositiveInteger", "value": 197}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 219}, {"type": "IfcPositiveInteger", "value": 215}, {"type": "IfcPositiveInteger", "value": 214}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 210}, {"type": "IfcPositiveInteger", "value": 208}, {"type": "IfcPositiveInteger", "value": 211}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 213}, {"type": "IfcPositiveInteger", "value": 212}, {"type": "IfcPositiveInteger", "value": 205}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 212}, {"type": "IfcPositiveInteger", "value": 211}, {"type": "IfcPositiveInteger", "value": 207}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 197}, {"type": "IfcPositiveInteger", "value": 213}, {"type": "IfcPositiveInteger", "value": 205}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 198}, {"type": "IfcPositiveInteger", "value": 204}, {"type": "IfcPositiveInteger", "value": 199}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 200}, {"type": "IfcPositiveInteger", "value": 199}, {"type": "IfcPositiveInteger", "value": 203}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 203}, {"type": "IfcPositiveInteger", "value": 202}, {"type": "IfcPositiveInteger", "value": 201}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 200}, {"type": "IfcPositiveInteger", "value": 203}, {"type": "IfcPositiveInteger", "value": 201}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 203}, {"type": "IfcPositiveInteger", "value": 199}, {"type": "IfcPositiveInteger", "value": 204}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 209}, {"type": "IfcPositiveInteger", "value": 208}, {"type": "IfcPositiveInteger", "value": 210}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 208}, {"type": "IfcPositiveInteger", "value": 207}, {"type": "IfcPositiveInteger", "value": 211}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 206}, {"type": "IfcPositiveInteger", "value": 212}, {"type": "IfcPositiveInteger", "value": 207}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 212}, {"type": "IfcPositiveInteger", "value": 206}, {"type": "IfcPositiveInteger", "value": 205}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 197}, {"type": "IfcPositiveInteger", "value": 205}, {"type": "IfcPositiveInteger", "value": 204}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 197}, {"type": "IfcPositiveInteger", "value": 204}, {"type": "IfcPositiveInteger", "value": 198}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 80}, {"type": "IfcPositiveInteger", "value": 126}, {"type": "IfcPositiveInteger", "value": 78}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 82}, {"type": "IfcPositiveInteger", "value": 125}, {"type": "IfcPositiveInteger", "value": 80}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 127}, {"type": "IfcPositiveInteger", "value": 84}, {"type": "IfcPositiveInteger", "value": 128}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 127}, {"type": "IfcPositiveInteger", "value": 81}, {"type": "IfcPositiveInteger", "value": 125}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 125}, {"type": "IfcPositiveInteger", "value": 79}, {"type": "IfcPositiveInteger", "value": 126}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 84}, {"type": "IfcPositiveInteger", "value": 86}, {"type": "IfcPositiveInteger", "value": 129}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 130}, {"type": "IfcPositiveInteger", "value": 129}, {"type": "IfcPositiveInteger", "value": 86}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 131}, {"type": "IfcPositiveInteger", "value": 132}, {"type": "IfcPositiveInteger", "value": 90}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 132}, {"type": "IfcPositiveInteger", "value": 130}, {"type": "IfcPositiveInteger", "value": 88}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 133}, {"type": "IfcPositiveInteger", "value": 131}, {"type": "IfcPositiveInteger", "value": 92}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 134}, {"type": "IfcPositiveInteger", "value": 133}, {"type": "IfcPositiveInteger", "value": 94}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 135}, {"type": "IfcPositiveInteger", "value": 134}, {"type": "IfcPositiveInteger", "value": 96}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 128}, {"type": "IfcPositiveInteger", "value": 83}, {"type": "IfcPositiveInteger", "value": 127}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 128}, {"type": "IfcPositiveInteger", "value": 129}, {"type": "IfcPositiveInteger", "value": 87}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 129}, {"type": "IfcPositiveInteger", "value": 130}, {"type": "IfcPositiveInteger", "value": 89}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 93}, {"type": "IfcPositiveInteger", "value": 91}, {"type": "IfcPositiveInteger", "value": 132}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 97}, {"type": "IfcPositiveInteger", "value": 95}, {"type": "IfcPositiveInteger", "value": 133}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 134}, {"type": "IfcPositiveInteger", "value": 135}, {"type": "IfcPositiveInteger", "value": 99}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 95}, {"type": "IfcPositiveInteger", "value": 93}, {"type": "IfcPositiveInteger", "value": 131}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 91}, {"type": "IfcPositiveInteger", "value": 89}, {"type": "IfcPositiveInteger", "value": 130}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 98}, {"type": "IfcPositiveInteger", "value": 100}, {"type": "IfcPositiveInteger", "value": 136}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 137}, {"type": "IfcPositiveInteger", "value": 136}, {"type": "IfcPositiveInteger", "value": 100}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 138}, {"type": "IfcPositiveInteger", "value": 139}, {"type": "IfcPositiveInteger", "value": 104}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 139}, {"type": "IfcPositiveInteger", "value": 137}, {"type": "IfcPositiveInteger", "value": 102}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 103}, {"type": "IfcPositiveInteger", "value": 101}, {"type": "IfcPositiveInteger", "value": 136}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 140}, {"type": "IfcPositiveInteger", "value": 138}, {"type": "IfcPositiveInteger", "value": 106}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 107}, {"type": "IfcPositiveInteger", "value": 105}, {"type": "IfcPositiveInteger", "value": 139}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 105}, {"type": "IfcPositiveInteger", "value": 103}, {"type": "IfcPositiveInteger", "value": 137}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 135}, {"type": "IfcPositiveInteger", "value": 136}, {"type": "IfcPositiveInteger", "value": 101}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 141}, {"type": "IfcPositiveInteger", "value": 112}, {"type": "IfcPositiveInteger", "value": 142}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 114}, {"type": "IfcPositiveInteger", "value": 142}, {"type": "IfcPositiveInteger", "value": 112}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 141}, {"type": "IfcPositiveInteger", "value": 109}, {"type": "IfcPositiveInteger", "value": 140}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 110}, {"type": "IfcPositiveInteger", "value": 140}, {"type": "IfcPositiveInteger", "value": 108}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 118}, {"type": "IfcPositiveInteger", "value": 145}, {"type": "IfcPositiveInteger", "value": 116}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 120}, {"type": "IfcPositiveInteger", "value": 144}, {"type": "IfcPositiveInteger", "value": 118}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 116}, {"type": "IfcPositiveInteger", "value": 143}, {"type": "IfcPositiveInteger", "value": 114}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 122}, {"type": "IfcPositiveInteger", "value": 146}, {"type": "IfcPositiveInteger", "value": 120}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 109}, {"type": "IfcPositiveInteger", "value": 107}, {"type": "IfcPositiveInteger", "value": 138}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 111}, {"type": "IfcPositiveInteger", "value": 142}, {"type": "IfcPositiveInteger", "value": 113}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 113}, {"type": "IfcPositiveInteger", "value": 143}, {"type": "IfcPositiveInteger", "value": 115}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 145}, {"type": "IfcPositiveInteger", "value": 115}, {"type": "IfcPositiveInteger", "value": 143}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 146}, {"type": "IfcPositiveInteger", "value": 119}, {"type": "IfcPositiveInteger", "value": 144}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 123}, {"type": "IfcPositiveInteger", "value": 121}, {"type": "IfcPositiveInteger", "value": 146}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 144}, {"type": "IfcPositiveInteger", "value": 117}, {"type": "IfcPositiveInteger", "value": 145}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 148}, {"type": "IfcPositiveInteger", "value": 172}, {"type": "IfcPositiveInteger", "value": 147}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 149}, {"type": "IfcPositiveInteger", "value": 173}, {"type": "IfcPositiveInteger", "value": 148}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 151}, {"type": "IfcPositiveInteger", "value": 175}, {"type": "IfcPositiveInteger", "value": 150}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 152}, {"type": "IfcPositiveInteger", "value": 176}, {"type": "IfcPositiveInteger", "value": 151}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 150}, {"type": "IfcPositiveInteger", "value": 174}, {"type": "IfcPositiveInteger", "value": 149}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 154}, {"type": "IfcPositiveInteger", "value": 178}, {"type": "IfcPositiveInteger", "value": 153}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 155}, {"type": "IfcPositiveInteger", "value": 179}, {"type": "IfcPositiveInteger", "value": 154}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 157}, {"type": "IfcPositiveInteger", "value": 181}, {"type": "IfcPositiveInteger", "value": 156}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 158}, {"type": "IfcPositiveInteger", "value": 182}, {"type": "IfcPositiveInteger", "value": 157}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 156}, {"type": "IfcPositiveInteger", "value": 180}, {"type": "IfcPositiveInteger", "value": 155}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 153}, {"type": "IfcPositiveInteger", "value": 177}, {"type": "IfcPositiveInteger", "value": 152}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 185}, {"type": "IfcPositiveInteger", "value": 184}, {"type": "IfcPositiveInteger", "value": 159}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 186}, {"type": "IfcPositiveInteger", "value": 185}, {"type": "IfcPositiveInteger", "value": 160}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 188}, {"type": "IfcPositiveInteger", "value": 187}, {"type": "IfcPositiveInteger", "value": 162}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 189}, {"type": "IfcPositiveInteger", "value": 188}, {"type": "IfcPositiveInteger", "value": 163}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 187}, {"type": "IfcPositiveInteger", "value": 186}, {"type": "IfcPositiveInteger", "value": 161}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 191}, {"type": "IfcPositiveInteger", "value": 190}, {"type": "IfcPositiveInteger", "value": 165}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 192}, {"type": "IfcPositiveInteger", "value": 191}, {"type": "IfcPositiveInteger", "value": 166}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 194}, {"type": "IfcPositiveInteger", "value": 193}, {"type": "IfcPositiveInteger", "value": 168}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 196}, {"type": "IfcPositiveInteger", "value": 195}, {"type": "IfcPositiveInteger", "value": 170}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 195}, {"type": "IfcPositiveInteger", "value": 194}, {"type": "IfcPositiveInteger", "value": 169}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 193}, {"type": "IfcPositiveInteger", "value": 192}, {"type": "IfcPositiveInteger", "value": 167}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 190}, {"type": "IfcPositiveInteger", "value": 189}, {"type": "IfcPositiveInteger", "value": 164}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 159}, {"type": "IfcPositiveInteger", "value": 183}, {"type": "IfcPositiveInteger", "value": 158}]}]}]}}, "mappingTarget": {"type": "IfcCartesianTransformationOperator3D", "axis1": {"type": "IfcDirection", "directionRatios": [{"type": "IfcReal", "value": 1.0}, {"type": "IfcReal", "value": 0.0}, {"type": "IfcReal", "value": 0.0}]}, "axis2": {"type": "IfcDirection", "directionRatios": [{"type": "IfcReal", "value": 0.0}, {"type": "IfcReal", "value": 1.0}, {"type": "IfcReal", "value": 0.0}]}, "localOrigin": {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": 0.0}, {"type": "IfcLengthMeasure", "value": 0.0}, {"type": "IfcLengthMeasure", "value": 0.0}]}, "scale": {"type": "IfcReal", "value": 1.0}, "axis3": {"type": "IfcDirection", "directionRatios": [{"type": "IfcReal", "value": 0.0}, {"type": "IfcReal", "value": 0.0}, {"type": "IfcReal", "value": 1.0}]}}}]}]}, "predefinedType": "NOTDEFINED"}]}], "compositionType": "ELEMENT", "buildingAddress": {"type": "IfcPostalAddress", "region": {"type": "IfcLabel", "value": "Unknown"}}}]}, "longName": {"type": "IfcLabel", "value": "IfcProject"}, "phase": {"type": "IfcLabel"}, "representationContexts": [{"type": "IfcGeometricRepresentationContext", "globalId": "edb92bec-a9c5-4e34-9fb5-c55f0c21b042", "contextType": {"type": "IfcLabel", "value": "Model"}, "coordinateSpaceDimension": {"type": "IfcDimensionCount", "value": 3}, "precision": {"type": "IfcReal", "value": 0.0001}, "worldCoordinateSystem": {"type": "IfcAxis2Placement3D", "location": {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": 0.0}, {"type": "IfcLengthMeasure", "value": 0.0}, {"type": "IfcLengthMeasure", "value": 0.0}]}}, "trueNorth": {"type": "IfcDirection", "directionRatios": [{"type": "IfcReal", "value": 0.0}, {"type": "IfcReal", "value": 1.0}]}}], "unitsInContext": {"units": [{"type": "IfcSIUnit", "globalId": "38fc7f6f-467e-4042-b071-fced9cea899c", "dimensions": {"lengthExponent": 1, "massExponent": 0, "timeExponent": 0, "electricCurrentExponent": 0, "thermodynamicTemperatureExponent": 0, "amountOfSubstanceExponent": 0, "luminousIntensityExponent": 0}, "unitType": "LENGTHUNIT", "prefix": "MILLI", "name": "METRE"}, {"type": "IfcSIUnit", "globalId": "79e690e1-feaa-4f73-988e-b3b9224999a8", "dimensions": {"lengthExponent": 0, "massExponent": 0, "timeExponent": 0, "electricCurrentExponent": 0, "thermodynamicTemperatureExponent": 0, "amountOfSubstanceExponent": 0, "luminousIntensityExponent": 0}, "unitType": "PLANEANGLEUNIT", "name": "RADIAN"}, {"type": "IfcSIUnit", "globalId": "a158d685-a5a6-4b85-aae4-95f6ac9bd237", "dimensions": {"lengthExponent": 0, "massExponent": 0, "timeExponent": 1, "electricCurrentExponent": 0, "thermodynamicTemperatureExponent": 0, "amountOfSubstanceExponent": 0, "luminousIntensityExponent": 0}, "unitType": "TIMEUNIT", "name": "SECOND"}]}}
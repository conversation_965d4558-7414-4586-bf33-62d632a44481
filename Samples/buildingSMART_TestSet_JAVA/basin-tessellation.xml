<IfcProject type="IfcProject" globalId="dc85e5b8-c712-4e86-928f-76d7fbf37204">
  <ownerHistory type="IfcOwnerHistory" globalId="be4a5a86-315d-4402-988b-ad5644727dce" changeAction="ADDED">
    <owningUser type="IfcPersonAndOrganization" globalId="00d2fb48-f52a-4ad9-80c7-b935eab1ca37">
      <thePerson type="IfcPerson" globalId="5eaa926f-ca78-49c5-b0ee-ccb7ccc47538">
        <identification type="IfcIdentifier" value="Jon"/>
        <familyName type="IfcLabel" value="Jon"/>
      </thePerson>
      <theOrganization type="IfcOrganization" globalId="b777f61c-b051-4928-81ed-53e91f1e38bb">
        <name type="IfcLabel" value="Geometry Gym Pty Ltd"/>
      </theOrganization>
    </owningUser>
    <owningApplication>
      <applicationDeveloper type="IfcOrganization" globalId="4a77a5ac-2607-45c3-a491-0a877f802de1">
        <name type="IfcLabel" value="Geometry Gym Pty Ltd"/>
      </applicationDeveloper>
      <version type="IfcLabel" value="*******"/>
      <applicationFullName type="IfcLabel" value="ggRhinoIFC - Geometry Gym Plug-in for Rhino3d"/>
      <applicationIdentifier type="IfcIdentifier" value="ggRhinoIFC"/>
    </owningApplication>
    <lastModifiedDate type="IfcTimeStamp" value="1418084875"/>
    <creationDate type="IfcTimeStamp" value="1418084875"/>
  </ownerHistory>
  <name type="IfcLabel" value="IfcProject"/>
  <isDecomposedBy type="IfcRelAggregates" globalId="b1f756c4-a5be-4806-a80f-d97a41d92bff">
    <description type="IfcText" value="Project Container for Buildings"/>
    <name type="IfcLabel" value="Project Container"/>
    <relatedObjects>
      <IfcObjectDefinition type="IfcBuilding" globalId="166e2b19-ce9c-406d-b2fd-12bb5bfe701b" compositionType="ELEMENT">
        <name type="IfcLabel" value="IfcBuilding"/>
        <objectPlacement type="IfcLocalPlacement" globalId="8e7bb3b2-dab2-4c61-b2be-d6c4700dd97d">
          <relativePlacement type="IfcAxis2Placement3D">
            <location type="IfcCartesianPoint">
              <coordinates>
                <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
              </coordinates>
            </location>
          </relativePlacement>
        </objectPlacement>
        <containsElements>
          <IfcRelContainedInSpatialStructure type="IfcRelContainedInSpatialStructure" globalId="a27d63f8-1bf1-44aa-a2e8-bfb4b1315058">
            <description type="IfcText" value="Building Container for Elements"/>
            <name type="IfcLabel" value="Building"/>
            <relatedElements>
              <IfcProduct type="IfcSanitaryTerminal" globalId="23b82fa6-ac26-430b-ad6f-05ae0e32c148" predefinedType="NOTDEFINED">
                <isTypedBy>
                  <IfcRelDefinesByType type="IfcRelDefinesByType" globalId="c87660b2-27ad-4ecf-a092-0113ed0bd807">
                    <relatingType type="IfcSanitaryTerminalType" globalId="44db0a1f-60be-4845-bb7a-9f027fc38f16" predefinedType="WASHHANDBASIN">
                      <name type="IfcLabel" value="IFCSANITARYTERMINALTYPE"/>
                      <hasAssociations>
                        <IfcRelAssociates type="IfcRelAssociatesMaterial" globalId="4d9c0157-4d94-4cc7-ae2d-3cdffdf112c4">
                          <description type="IfcText" value="Material Associates"/>
                          <relatingMaterial type="IfcMaterial" globalId="49d76149-1139-405a-af5c-7fb7a9164add">
                            <name type="IfcLabel" value="Ceramic"/>
                          </relatingMaterial>
                          <name type="IfcLabel" value="MatAssoc"/>
                        </IfcRelAssociates>
                      </hasAssociations>
                      <representationMaps>
                        <IfcRepresentationMap type="IfcRepresentationMap">
                          <mappingOrigin type="IfcAxis2Placement3D">
                            <location type="IfcCartesianPoint">
                              <coordinates>
                                <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                              </coordinates>
                            </location>
                          </mappingOrigin>
                          <mappedRepresentation type="IfcShapeRepresentation">
                            <contextOfItems type="IfcGeometricRepresentationSubContext" globalId="5c4747f7-3260-41e7-8816-6a7d4ec258cb" targetView="MODEL_VIEW">
                              <contextIdentifier type="IfcLabel" value="Body"/>
                              <contextType type="IfcLabel" value="Model"/>
                            </contextOfItems>
                            <representationIdentifier type="IfcLabel" value="Body"/>
                            <representationType type="IfcLabel" value="Tessellation"/>
                            <items>
                              <IfcRepresentationItem type="IfcTriangulatedFaceSet">
                                <coordinates type="IfcCartesianPointList3D" globalId="9f9e7f21-149f-44bb-9c8d-b8f6df6e7469">
                                  <coordList>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-300.0"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="150.0"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-260.012578"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="202.771984"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-200.897703"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="235.427328"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-135.653172"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="254.960516"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-68.351281"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="265.485063"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="2.288734"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="268.839531"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="72.81782"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="265.023844"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="139.786906"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="254.038063"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="201.174906"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="235.317031"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="259.220938"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="203.387031"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="300.0"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="150.0"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="301.12175"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="84.866148"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="274.727594"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="21.433672"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="235.605922"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-32.723826"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="186.088641"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-80.939688"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="130.136258"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-119.016594"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="67.084977"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-144.523266"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="1.477218"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-153.498641"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-64.392137"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-145.234375"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-128.935"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-119.668008"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-185.4365"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-81.474469"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-235.751609"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-32.555805"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-275.439625"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="22.660475"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-301.2465"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="85.400219"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="253.099266"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-65.777992"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="249.952375"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-128.508695"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="240.511688"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-189.983266"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="222.998141"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-246.840234"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="193.330969"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-286.93375"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="143.116359"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-288.338563"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="84.231891"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-263.388344"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="25.178932"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-224.986906"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-26.382564"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-176.642109"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-71.667547"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-122.550633"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-106.846461"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-61.391031"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-130.155953"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="1.00923"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-137.756953"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="63.202145"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-129.69757"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="123.138398"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-106.540977"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="176.955734"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-71.42018"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="224.650078"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-26.756678"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="262.387781"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="23.516443"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="288.070906"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="83.103938"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="286.93375"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="143.116359"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="248.344641"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="192.212875"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="191.622094"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="222.376281"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="129.659992"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="240.269531"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="64.742059"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="250.052203"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-157.154922"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="175.808609"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-94.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-136.207516"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="207.772813"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-94.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-105.240203"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="227.552281"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-94.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-71.061875"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="239.383609"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-94.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-35.805801"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="245.758375"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-94.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="1.198953"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="247.790172"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-94.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="38.145594"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="245.479016"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-94.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="73.227336"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="238.824875"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-94.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="105.385414"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="227.485469"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-94.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="135.792813"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="208.145344"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-94.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="157.154922"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="175.808609"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-94.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="157.742547"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="136.356797"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-94.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="143.915969"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="97.9355"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-94.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="123.422102"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="65.13209"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-94.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="97.482477"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="35.927559"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-94.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="68.171844"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="12.864227"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-94.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="35.142449"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-2.585266"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-94.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.77384"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-8.021682"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-94.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-33.731801"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-3.015985"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-94.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-67.542563"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="12.469661"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-94.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-97.140859"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="35.603637"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-94.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-123.498414"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="65.233859"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-94.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-144.288969"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="98.678578"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-94.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-157.807906"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="136.680281"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-94.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-300.0"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="150.0"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-228.577453"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="162.904313"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-47.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-157.154922"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="175.808609"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-94.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-260.012578"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="202.771984"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-136.207516"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="207.772813"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-94.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-200.897703"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="235.427328"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-105.240203"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="227.552281"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-94.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-135.653172"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="254.960516"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-71.061875"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="239.383609"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-94.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-68.351281"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="265.485063"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-35.805801"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="245.758375"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-94.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="2.288734"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="268.839531"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="1.198953"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="247.790172"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-94.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="72.81782"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="265.023844"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="38.145594"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="245.479016"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-94.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="139.786906"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="254.038063"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="73.227336"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="238.824875"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-94.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="201.174906"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="235.317031"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="105.385414"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="227.485469"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-94.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="259.220938"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="203.387031"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="135.792813"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="208.145344"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-94.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="300.0"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="150.0"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="157.154922"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="175.808609"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-94.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="301.12175"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="84.866148"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="157.742547"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="136.356797"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-94.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="274.727594"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="21.433672"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="143.915969"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="97.9355"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-94.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="235.605922"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-32.723826"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="123.422102"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="65.13209"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-94.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="186.088641"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-80.939688"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="97.482477"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="35.927559"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-94.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="130.136258"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-119.016594"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="68.171844"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="12.864227"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-94.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="67.084977"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-144.523266"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="35.142449"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-2.585266"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-94.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="1.477218"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-153.498641"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.77384"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-8.021682"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-94.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-64.392137"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-145.234375"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-33.731801"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-3.015985"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-94.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-128.935"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-119.668008"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-67.542563"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="12.469661"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-94.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-185.4365"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-81.474469"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-97.140859"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="35.603637"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-94.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-235.751609"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-32.555805"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-123.498414"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="65.233859"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-94.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-275.439625"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="22.660475"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-144.288969"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="98.678578"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-94.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-301.2465"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="85.400219"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-157.807906"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="136.680281"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-94.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-300.0"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="150.0"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-228.577453"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="162.904313"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-47.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-157.154922"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="175.808609"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-94.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-103.357523"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="247.172063"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-47.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-153.068953"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="231.489813"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-47.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-52.078543"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="255.621719"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-47.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="1.743843"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="258.314844"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-47.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="55.481707"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="255.251438"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-47.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="106.507117"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="246.431469"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-47.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="197.506875"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="205.766188"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-47.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="153.280156"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="231.40125"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-47.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="228.577453"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="162.904313"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-47.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="229.432141"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="110.611469"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-47.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="209.321781"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="59.684586"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-47.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="179.514016"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="16.204132"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-47.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="141.785563"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-22.506064"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-47.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="51.113715"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-73.554266"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-47.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="99.154047"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-53.076184"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-47.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="1.125529"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-80.760164"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-47.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-49.061969"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-74.12518"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-47.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-98.238781"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-53.599176"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-47.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-141.288688"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-22.935416"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-47.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-209.864297"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="60.669523"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-47.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-179.625016"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="16.339027"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-47.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-229.527203"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="111.04025"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-47.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="247.792422"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-84.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="35.45952"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="245.798125"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-84.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="71.015367"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="239.395359"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-84.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="104.952289"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="227.684234"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-84.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="136.019484"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="207.942281"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-84.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="157.154922"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="175.808609"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-84.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="157.77775"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="136.530484"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-84.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="143.710984"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="97.530469"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-84.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="123.041867"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="64.626715"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-84.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="96.919461"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="35.394453"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-84.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="67.443461"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="12.407895"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-84.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="34.616102"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-2.748099"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-84.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.55276"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-8.022964"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-84.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-33.624148"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-3.048111"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-84.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-67.121539"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="12.207951"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-84.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-96.747688"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="35.232555"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-84.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-123.226352"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="64.87157"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-84.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-144.259"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="98.61857"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-84.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-157.924344"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="137.268734"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-84.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-157.154922"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="175.808609"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-84.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-135.195516"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="208.674078"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-84.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-104.054703"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="228.091234"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-84.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-70.384797"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="239.553859"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-84.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-36.026906"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="245.732781"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-84.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="247.792422"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-84.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="253.099266"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="64.742059"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="250.052203"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="129.659992"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="240.269531"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="191.622094"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="222.376281"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="248.344641"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="192.212875"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="286.93375"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="143.116359"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="288.070906"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="83.103938"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="262.387781"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="23.516443"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="224.650078"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-26.756678"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="176.955734"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-71.42018"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="123.138398"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-106.540977"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="63.202145"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-129.69757"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="1.00923"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-137.756953"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-61.391031"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-130.155953"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-122.550633"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-106.846461"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-176.642109"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-71.667547"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-224.986906"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-26.382564"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-263.388344"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="25.178932"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-288.338563"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="84.231891"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-286.93375"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="143.116359"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-246.840234"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="193.330969"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-189.983266"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="222.998141"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-128.508695"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="240.511688"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-65.777992"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="249.952375"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="253.099266"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="247.792422"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-84.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="35.45952"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="245.798125"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-84.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="71.015367"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="239.395359"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-84.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="104.952289"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="227.684234"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-84.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="136.019484"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="207.942281"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-84.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="157.154922"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="175.808609"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-84.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="157.77775"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="136.530484"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-84.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="143.710984"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="97.530469"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-84.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="123.041867"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="64.626715"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-84.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="96.919461"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="35.394453"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-84.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="67.443461"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="12.407895"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-84.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="34.616102"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-2.748099"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-84.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.55276"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-8.022964"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-84.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-33.624148"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-3.048111"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-84.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-67.121539"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="12.207951"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-84.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-96.747688"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="35.232555"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-84.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-123.226352"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="64.87157"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-84.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-144.259"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="98.61857"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-84.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-157.924344"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="137.268734"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-84.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-157.154922"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="175.808609"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-84.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-135.195516"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="208.674078"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-84.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-104.054703"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="228.091234"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-84.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-70.384797"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="239.553859"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-84.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                    <IfcCartesianPoint type="IfcCartesianPoint">
                                      <coordinates>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-36.026906"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="245.732781"/>
                                        <IfcLengthMeasure type="IfcLengthMeasure" value="-84.0"/>
                                      </coordinates>
                                    </IfcCartesianPoint>
                                  </coordList>
                                </coordinates>
                                <closed type="IfcBoolean" value="false"/>
                                <coordIndex>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="28"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="2"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="29"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="1"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="29"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="2"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="30"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="1"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="24"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="29"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="1"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="30"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="24"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="31"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="30"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="3"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="2"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="28"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="5"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="4"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="27"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="6"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="5"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="25"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="25"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="5"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="26"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="4"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="28"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="27"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="5"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="27"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="26"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="3"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="28"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="4"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="23"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="32"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="31"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="33"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="32"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="23"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="24"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="23"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="31"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="34"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="22"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="21"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="23"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="22"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="33"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="22"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="34"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="33"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="21"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="20"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="35"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="36"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="35"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="20"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="34"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="21"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="35"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="37"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="36"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="19"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="20"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="19"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="36"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="18"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="37"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="19"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="7"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="6"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="48"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="8"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="7"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="47"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="7"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="48"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="47"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="8"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="47"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="46"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="46"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="9"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="8"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="46"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="45"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="10"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="11"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="10"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="45"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="12"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="11"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="44"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="45"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="44"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="11"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="10"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="9"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="46"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="12"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="44"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="43"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="15"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="39"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="16"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="40"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="39"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="15"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="38"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="16"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="39"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="18"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="17"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="37"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="16"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="38"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="17"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="17"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="38"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="37"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="13"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="43"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="42"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="12"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="43"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="13"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="14"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="13"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="42"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="15"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="14"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="40"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="14"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="41"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="40"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="42"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="41"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="14"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="48"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="6"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="25"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="50"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="72"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="49"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="51"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="72"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="50"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="71"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="72"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="52"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="51"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="52"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="72"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="53"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="71"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="52"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="69"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="70"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="63"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="71"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="54"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="70"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="66"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="67"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="65"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="67"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="68"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="65"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="68"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="69"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="64"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="71"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="53"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="54"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="54"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="55"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="61"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="55"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="56"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="61"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="58"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="60"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="57"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="60"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="56"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="57"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="59"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="60"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="58"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="65"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="68"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="64"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="69"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="63"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="64"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="62"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="63"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="70"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="62"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="54"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="61"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="61"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="56"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="60"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="62"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="70"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="54"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="74"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="73"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="76"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="80"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="125"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="126"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="126"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="76"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="78"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="126"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="77"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="76"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="76"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="77"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="74"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="82"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="127"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="125"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="127"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="82"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="84"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="127"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="83"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="81"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="125"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="81"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="79"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="128"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="84"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="129"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="88"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="130"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="86"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="92"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="131"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="90"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="90"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="132"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="88"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="94"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="133"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="92"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="96"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="134"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="94"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="98"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="135"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="96"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="128"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="85"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="83"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="77"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="75"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="74"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="77"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="126"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="79"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="85"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="128"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="87"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="87"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="129"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="89"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="131"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="93"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="132"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="134"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="97"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="133"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="97"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="134"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="99"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="133"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="95"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="131"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="132"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="91"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="130"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="135"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="98"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="136"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="102"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="137"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="100"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="106"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="138"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="104"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="104"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="139"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="102"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="137"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="103"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="136"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="108"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="140"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="106"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="138"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="107"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="139"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="139"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="105"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="137"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="99"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="135"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="101"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="141"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="110"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="112"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="114"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="143"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="142"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="141"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="111"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="109"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="110"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="141"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="140"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="118"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="144"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="145"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="120"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="146"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="144"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="116"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="145"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="143"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="122"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="123"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="146"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="140"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="109"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="138"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="111"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="141"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="142"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="113"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="142"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="143"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="145"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="117"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="115"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="146"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="121"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="119"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="123"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="124"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="121"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="144"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="119"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="117"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="148"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="173"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="172"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="149"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="174"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="173"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="151"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="176"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="175"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="152"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="177"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="176"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="150"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="175"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="174"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="154"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="179"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="178"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="155"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="180"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="179"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="157"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="182"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="181"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="158"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="183"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="182"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="156"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="181"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="180"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="153"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="178"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="177"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="160"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="185"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="159"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="161"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="186"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="160"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="163"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="188"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="162"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="164"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="189"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="163"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="162"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="187"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="161"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="166"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="191"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="165"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="167"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="192"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="166"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="169"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="194"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="168"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="171"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="196"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="170"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="170"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="195"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="169"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="168"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="193"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="167"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="165"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="190"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="164"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="159"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="184"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="183"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="217"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="216"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="215"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="217"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="215"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="218"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="220"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="219"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="214"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="215"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="219"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="218"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="197"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="220"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="214"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="214"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="213"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="197"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="219"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="215"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="214"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="210"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="208"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="211"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="213"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="212"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="205"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="212"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="211"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="207"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="197"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="213"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="205"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="198"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="204"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="199"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="200"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="199"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="203"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="203"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="202"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="201"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="200"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="203"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="201"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="203"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="199"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="204"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="209"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="208"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="210"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="208"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="207"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="211"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="206"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="212"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="207"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="212"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="206"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="205"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="197"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="205"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="204"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="197"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="204"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="198"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="80"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="126"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="78"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="82"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="125"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="80"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="127"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="84"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="128"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="127"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="81"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="125"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="125"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="79"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="126"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="84"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="86"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="129"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="130"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="129"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="86"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="131"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="132"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="90"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="132"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="130"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="88"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="133"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="131"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="92"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="134"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="133"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="94"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="135"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="134"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="96"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="128"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="83"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="127"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="128"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="129"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="87"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="129"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="130"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="89"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="93"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="91"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="132"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="97"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="95"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="133"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="134"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="135"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="99"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="95"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="93"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="131"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="91"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="89"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="130"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="98"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="100"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="136"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="137"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="136"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="100"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="138"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="139"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="104"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="139"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="137"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="102"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="103"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="101"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="136"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="140"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="138"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="106"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="107"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="105"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="139"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="105"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="103"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="137"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="135"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="136"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="101"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="141"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="112"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="142"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="114"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="142"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="112"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="141"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="109"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="140"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="110"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="140"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="108"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="118"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="145"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="116"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="120"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="144"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="118"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="116"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="143"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="114"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="122"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="146"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="120"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="109"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="107"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="138"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="111"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="142"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="113"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="113"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="143"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="115"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="145"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="115"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="143"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="146"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="119"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="144"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="123"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="121"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="146"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="144"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="117"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="145"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="148"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="172"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="147"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="149"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="173"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="148"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="151"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="175"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="150"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="152"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="176"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="151"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="150"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="174"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="149"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="154"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="178"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="153"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="155"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="179"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="154"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="157"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="181"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="156"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="158"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="182"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="157"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="156"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="180"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="155"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="153"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="177"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="152"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="185"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="184"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="159"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="186"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="185"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="160"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="188"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="187"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="162"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="189"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="188"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="163"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="187"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="186"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="161"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="191"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="190"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="165"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="192"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="191"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="166"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="194"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="193"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="168"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="196"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="195"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="170"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="195"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="194"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="169"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="193"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="192"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="167"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="190"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="189"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="164"/>
                                  </positiveIntegers>
                                  <positiveIntegers>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="159"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="183"/>
                                    <IfcPositiveInteger type="IfcPositiveInteger" value="158"/>
                                  </positiveIntegers>
                                </coordIndex>
                              </IfcRepresentationItem>
                            </items>
                          </mappedRepresentation>
                        </IfcRepresentationMap>
                      </representationMaps>
                    </relatingType>
                  </IfcRelDefinesByType>
                </isTypedBy>
                <objectPlacement type="IfcLocalPlacement" globalId="b2750739-412a-41f0-be15-53a43870b5bb">
                  <relativePlacement type="IfcAxis2Placement3D">
                    <location type="IfcCartesianPoint">
                      <coordinates>
                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                      </coordinates>
                    </location>
                  </relativePlacement>
                  <placementRelTo>8e7bb3b2-dab2-4c61-b2be-d6c4700dd97d</placementRelTo>
                </objectPlacement>
                <representation type="IfcProductDefinitionShape">
                  <representations>
                    <IfcRepresentation type="IfcShapeRepresentation">
                      <contextOfItems>5c4747f7-3260-41e7-8816-6a7d4ec258cb</contextOfItems>
                      <representationIdentifier type="IfcLabel" value="Body"/>
                      <representationType type="IfcLabel" value="MappedRepresentation"/>
                      <items>
                        <IfcRepresentationItem type="IfcMappedItem">
                          <mappingSource type="IfcRepresentationMap">
                            <mappingOrigin type="IfcAxis2Placement3D">
                              <location type="IfcCartesianPoint">
                                <coordinates>
                                  <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                  <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                  <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                </coordinates>
                              </location>
                            </mappingOrigin>
                            <mappedRepresentation type="IfcShapeRepresentation">
                              <contextOfItems>5c4747f7-3260-41e7-8816-6a7d4ec258cb</contextOfItems>
                              <representationIdentifier type="IfcLabel" value="Body"/>
                              <representationType type="IfcLabel" value="Tessellation"/>
                              <items>
                                <IfcRepresentationItem type="IfcTriangulatedFaceSet">
                                  <coordinates>9f9e7f21-149f-44bb-9c8d-b8f6df6e7469</coordinates>
                                  <closed type="IfcBoolean" value="false"/>
                                  <coordIndex>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="28"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="2"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="29"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="1"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="29"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="2"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="30"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="1"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="24"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="29"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="1"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="30"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="24"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="31"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="30"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="3"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="2"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="28"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="5"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="4"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="27"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="6"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="5"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="25"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="25"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="5"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="26"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="4"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="28"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="27"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="5"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="27"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="26"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="3"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="28"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="4"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="23"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="32"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="31"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="33"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="32"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="23"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="24"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="23"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="31"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="34"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="22"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="21"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="23"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="22"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="33"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="22"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="34"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="33"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="21"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="20"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="35"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="36"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="35"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="20"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="34"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="21"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="35"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="37"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="36"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="19"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="20"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="19"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="36"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="18"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="37"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="19"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="7"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="6"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="48"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="8"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="7"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="47"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="7"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="48"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="47"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="8"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="47"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="46"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="46"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="9"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="8"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="46"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="45"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="10"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="11"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="10"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="45"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="12"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="11"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="44"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="45"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="44"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="11"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="10"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="9"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="46"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="12"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="44"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="43"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="15"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="39"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="16"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="40"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="39"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="15"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="38"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="16"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="39"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="18"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="17"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="37"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="16"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="38"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="17"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="17"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="38"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="37"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="13"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="43"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="42"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="12"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="43"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="13"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="14"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="13"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="42"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="15"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="14"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="40"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="14"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="41"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="40"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="42"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="41"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="14"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="48"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="6"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="25"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="50"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="72"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="49"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="51"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="72"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="50"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="71"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="72"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="52"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="51"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="52"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="72"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="53"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="71"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="52"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="69"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="70"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="63"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="71"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="54"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="70"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="66"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="67"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="65"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="67"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="68"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="65"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="68"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="69"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="64"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="71"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="53"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="54"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="54"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="55"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="61"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="55"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="56"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="61"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="58"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="60"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="57"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="60"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="56"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="57"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="59"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="60"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="58"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="65"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="68"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="64"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="69"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="63"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="64"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="62"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="63"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="70"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="62"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="54"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="61"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="61"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="56"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="60"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="62"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="70"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="54"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="74"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="73"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="76"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="80"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="125"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="126"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="126"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="76"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="78"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="126"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="77"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="76"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="76"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="77"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="74"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="82"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="127"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="125"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="127"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="82"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="84"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="127"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="83"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="81"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="125"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="81"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="79"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="128"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="84"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="129"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="88"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="130"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="86"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="92"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="131"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="90"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="90"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="132"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="88"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="94"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="133"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="92"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="96"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="134"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="94"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="98"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="135"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="96"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="128"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="85"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="83"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="77"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="75"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="74"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="77"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="126"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="79"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="85"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="128"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="87"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="87"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="129"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="89"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="131"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="93"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="132"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="134"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="97"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="133"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="97"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="134"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="99"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="133"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="95"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="131"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="132"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="91"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="130"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="135"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="98"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="136"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="102"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="137"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="100"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="106"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="138"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="104"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="104"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="139"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="102"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="137"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="103"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="136"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="108"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="140"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="106"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="138"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="107"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="139"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="139"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="105"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="137"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="99"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="135"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="101"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="141"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="110"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="112"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="114"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="143"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="142"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="141"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="111"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="109"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="110"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="141"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="140"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="118"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="144"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="145"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="120"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="146"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="144"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="116"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="145"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="143"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="122"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="123"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="146"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="140"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="109"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="138"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="111"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="141"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="142"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="113"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="142"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="143"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="145"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="117"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="115"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="146"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="121"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="119"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="123"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="124"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="121"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="144"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="119"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="117"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="148"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="173"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="172"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="149"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="174"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="173"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="151"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="176"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="175"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="152"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="177"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="176"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="150"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="175"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="174"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="154"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="179"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="178"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="155"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="180"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="179"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="157"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="182"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="181"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="158"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="183"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="182"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="156"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="181"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="180"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="153"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="178"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="177"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="160"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="185"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="159"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="161"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="186"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="160"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="163"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="188"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="162"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="164"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="189"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="163"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="162"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="187"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="161"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="166"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="191"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="165"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="167"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="192"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="166"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="169"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="194"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="168"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="171"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="196"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="170"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="170"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="195"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="169"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="168"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="193"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="167"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="165"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="190"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="164"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="159"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="184"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="183"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="217"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="216"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="215"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="217"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="215"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="218"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="220"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="219"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="214"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="215"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="219"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="218"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="197"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="220"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="214"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="214"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="213"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="197"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="219"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="215"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="214"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="210"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="208"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="211"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="213"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="212"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="205"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="212"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="211"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="207"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="197"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="213"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="205"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="198"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="204"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="199"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="200"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="199"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="203"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="203"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="202"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="201"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="200"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="203"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="201"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="203"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="199"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="204"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="209"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="208"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="210"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="208"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="207"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="211"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="206"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="212"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="207"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="212"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="206"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="205"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="197"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="205"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="204"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="197"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="204"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="198"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="80"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="126"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="78"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="82"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="125"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="80"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="127"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="84"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="128"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="127"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="81"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="125"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="125"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="79"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="126"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="84"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="86"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="129"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="130"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="129"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="86"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="131"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="132"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="90"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="132"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="130"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="88"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="133"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="131"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="92"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="134"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="133"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="94"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="135"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="134"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="96"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="128"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="83"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="127"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="128"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="129"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="87"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="129"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="130"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="89"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="93"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="91"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="132"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="97"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="95"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="133"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="134"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="135"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="99"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="95"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="93"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="131"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="91"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="89"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="130"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="98"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="100"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="136"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="137"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="136"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="100"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="138"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="139"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="104"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="139"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="137"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="102"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="103"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="101"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="136"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="140"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="138"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="106"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="107"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="105"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="139"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="105"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="103"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="137"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="135"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="136"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="101"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="141"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="112"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="142"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="114"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="142"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="112"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="141"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="109"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="140"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="110"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="140"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="108"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="118"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="145"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="116"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="120"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="144"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="118"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="116"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="143"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="114"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="122"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="146"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="120"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="109"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="107"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="138"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="111"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="142"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="113"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="113"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="143"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="115"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="145"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="115"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="143"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="146"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="119"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="144"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="123"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="121"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="146"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="144"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="117"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="145"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="148"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="172"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="147"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="149"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="173"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="148"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="151"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="175"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="150"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="152"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="176"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="151"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="150"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="174"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="149"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="154"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="178"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="153"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="155"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="179"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="154"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="157"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="181"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="156"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="158"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="182"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="157"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="156"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="180"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="155"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="153"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="177"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="152"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="185"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="184"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="159"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="186"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="185"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="160"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="188"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="187"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="162"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="189"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="188"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="163"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="187"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="186"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="161"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="191"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="190"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="165"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="192"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="191"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="166"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="194"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="193"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="168"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="196"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="195"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="170"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="195"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="194"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="169"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="193"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="192"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="167"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="190"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="189"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="164"/>
                                    </positiveIntegers>
                                    <positiveIntegers>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="159"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="183"/>
                                      <IfcPositiveInteger type="IfcPositiveInteger" value="158"/>
                                    </positiveIntegers>
                                  </coordIndex>
                                </IfcRepresentationItem>
                              </items>
                            </mappedRepresentation>
                          </mappingSource>
                          <mappingTarget type="IfcCartesianTransformationOperator3D">
                            <axis1 type="IfcDirection">
                              <directionRatios>
                                <IfcReal type="IfcReal" value="1.0"/>
                                <IfcReal type="IfcReal" value="0.0"/>
                                <IfcReal type="IfcReal" value="0.0"/>
                              </directionRatios>
                            </axis1>
                            <axis2 type="IfcDirection">
                              <directionRatios>
                                <IfcReal type="IfcReal" value="0.0"/>
                                <IfcReal type="IfcReal" value="1.0"/>
                                <IfcReal type="IfcReal" value="0.0"/>
                              </directionRatios>
                            </axis2>
                            <localOrigin type="IfcCartesianPoint">
                              <coordinates>
                                <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                              </coordinates>
                            </localOrigin>
                            <scale type="IfcReal" value="1.0"/>
                            <axis3 type="IfcDirection">
                              <directionRatios>
                                <IfcReal type="IfcReal" value="0.0"/>
                                <IfcReal type="IfcReal" value="0.0"/>
                                <IfcReal type="IfcReal" value="1.0"/>
                              </directionRatios>
                            </axis3>
                          </mappingTarget>
                        </IfcRepresentationItem>
                      </items>
                    </IfcRepresentation>
                  </representations>
                </representation>
              </IfcProduct>
            </relatedElements>
          </IfcRelContainedInSpatialStructure>
        </containsElements>
        <buildingAddress type="IfcPostalAddress">
          <region type="IfcLabel" value="Unknown"/>
        </buildingAddress>
      </IfcObjectDefinition>
    </relatedObjects>
  </isDecomposedBy>
  <longName type="IfcLabel" value="IfcProject"/>
  <phase type="IfcLabel"/>
  <representationContexts>
    <IfcRepresentationContext type="IfcGeometricRepresentationContext" globalId="edb92bec-a9c5-4e34-9fb5-c55f0c21b042">
      <worldCoordinateSystem type="IfcAxis2Placement3D">
        <location type="IfcCartesianPoint">
          <coordinates>
            <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
            <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
            <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
          </coordinates>
        </location>
      </worldCoordinateSystem>
      <contextType type="IfcLabel" value="Model"/>
      <coordinateSpaceDimension type="IfcDimensionCount" value="3"/>
      <precision type="IfcReal" value="1.0E-4"/>
      <trueNorth type="IfcDirection">
        <directionRatios>
          <IfcReal type="IfcReal" value="0.0"/>
          <IfcReal type="IfcReal" value="1.0"/>
        </directionRatios>
      </trueNorth>
    </IfcRepresentationContext>
  </representationContexts>
  <unitsInContext>
    <units>
      <IfcUnit type="IfcSIUnit" globalId="38fc7f6f-467e-4042-b071-fced9cea899c">
        <dimensions lengthExponent="1" massExponent="0" timeExponent="0" electricCurrentExponent="0" thermodynamicTemperatureExponent="0" amountOfSubstanceExponent="0" luminousIntensityExponent="0"/>
        <unitType>LENGTHUNIT</unitType>
        <prefix>MILLI</prefix>
        <name>METRE</name>
      </IfcUnit>
      <IfcUnit type="IfcSIUnit" globalId="79e690e1-feaa-4f73-988e-b3b9224999a8">
        <dimensions lengthExponent="0" massExponent="0" timeExponent="0" electricCurrentExponent="0" thermodynamicTemperatureExponent="0" amountOfSubstanceExponent="0" luminousIntensityExponent="0"/>
        <unitType>PLANEANGLEUNIT</unitType>
        <name>RADIAN</name>
      </IfcUnit>
      <IfcUnit type="IfcSIUnit" globalId="a158d685-a5a6-4b85-aae4-95f6ac9bd237">
        <dimensions lengthExponent="0" massExponent="0" timeExponent="1" electricCurrentExponent="0" thermodynamicTemperatureExponent="0" amountOfSubstanceExponent="0" luminousIntensityExponent="0"/>
        <unitType>TIMEUNIT</unitType>
        <name>SECOND</name>
      </IfcUnit>
    </units>
  </unitsInContext>
</IfcProject>

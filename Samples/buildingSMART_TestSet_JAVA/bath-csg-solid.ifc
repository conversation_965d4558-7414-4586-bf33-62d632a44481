ISO-10303-21;
HEADER;
FILE_DESCRIPTION(('ViewDefinition [notYetAssigned]'),'2;1');
FILE_NAME(
/* name */ '',
/* time_stamp */ '2014-12-09T00:27:54',
/* author */ ('Jon'),
/* organization */ ('Unknown'),
/* preprocessor_version */ 'ggIFC - Exporter by Geometry Gym Pty Ltd',
/* originating_system */ 'ggIFC - Exporter by Geometry Gym Pty Ltd',
/* authorization */ 'None');

FILE_SCHEMA (('IFC4'));
ENDSEC;

DATA;
#1= IFCAPPLICATION(#2,'*******','ggRhinoIFC - Geometry Gym Plug-in for Rhino3d','ggRhinoIFC');
#2= IFCORGANIZATION($,'Geometry Gym Pty Ltd',$,$,$);
#3= IFCPERSONANDORGANIZATION(#4,#5,$);
#4= IFCPERSON('Jon','Jon',$,$,$,$,$,$);
#5= IFCORGANIZATION($,'Geometry Gym Pty Ltd',$,$,$);
#6= IFCOWNERHISTORY(#3,#1,$,.ADDED.,1418084874,$,$,1418084874);
#7= IFCGEOMETRICREPRESENTATIONCONTEXT($,'Model',3,0.0001,#8,#10);
#8= IFCAXIS2PLACEMENT3D(#9,$,$);
#9= IFCCARTESIANPOINT((0.0,0.0,0.0));
#10= IFCDIRECTION((0.0,1.0));
#11= IFCGEOMETRICREPRESENTATIONSUBCONTEXT('Axis','Model',*,*,*,*,#7,$,.MODEL_VIEW.,$);
#12= IFCGEOMETRICREPRESENTATIONSUBCONTEXT('Body','Model',*,*,*,*,#7,$,.MODEL_VIEW.,$);
#13= IFCGEOMETRICREPRESENTATIONCONTEXT($,'Model',3,0.0001,#8,#14);
#14= IFCDIRECTION((0.0,1.0));
#50= IFCBUILDING('3Wp1ibyzH8seZGY8I0Q4qQ',$,'IfcBuilding',$,$,#51,$,$,.ELEMENT.,$,$,#57);
#51= IFCLOCALPLACEMENT($,#52);
#52= IFCAXIS2PLACEMENT3D(#53,$,$);
#53= IFCCARTESIANPOINT((0.0,0.0,0.0));
#54= IFCRELCONTAINEDINSPATIALSTRUCTURE('0Zzeel8LbF$Pt6eEPXJ05p',$,'Building','Building Container for Elements',(#228),#50);
#55= IFCLOCALPLACEMENT(#51,#52);
#57= IFCPOSTALADDRESS($,$,$,$,$,$,$,'Unknown',$,$);
#100= IFCPROJECT('2kdrNhTWDDBvOcaAeVZ_84',#6,'IfcProject',$,$,'IfcProject','',(#13),#101);
#101= IFCUNITASSIGNMENT((#102,#103,#104));
#102= IFCSIUNIT(*,.LENGTHUNIT.,.MILLI.,.METRE.);
#103= IFCSIUNIT(*,.PLANEANGLEUNIT.,$,.RADIAN.);
#104= IFCSIUNIT(*,.TIMEUNIT.,$,.SECOND.);
#105= IFCRELAGGREGATES('0kCAYw_ir1GOKf2JBpKTcv',$,'Project Container','Project Container for Buildings',#100,(#50));
#200= IFCBLOCK(#201,2000.0,800.0,800.0);
#201= IFCAXIS2PLACEMENT3D(#202,#203,#204);
#202= IFCCARTESIANPOINT((0.0,0.0,0.0));
#203= IFCDIRECTION((0.0,0.0,1.0));
#204= IFCDIRECTION((1.0,0.0,0.0));
#205= IFCROUNDEDRECTANGLEPROFILEDEF(.AREA.,'VoidProfile',$,1800.0,600.0,200.0);
#206= IFCAXIS2PLACEMENT3D(#207,#208,#209);
#207= IFCCARTESIANPOINT((1000.0,400.0,100.0));
#208= IFCDIRECTION((0.0,0.0,1.0));
#209= IFCDIRECTION((1.0,0.0,0.0));
#210= IFCDIRECTION((0.0,0.0,1.0));
#211= IFCEXTRUDEDAREASOLID(#205,#206,#210,700.0);
#212= IFCBOOLEANRESULT(.DIFFERENCE.,#200,#211);
#213= IFCREPRESENTATIONMAP(#214,#216);
#214= IFCAXIS2PLACEMENT3D(#215,$,$);
#215= IFCCARTESIANPOINT((0.0,0.0,0.0));
#216= IFCSHAPEREPRESENTATION(#12,'Body','CSG',(#212));
#217= IFCMATERIAL('Ceramic',$,$);
#218= IFCRELASSOCIATESMATERIAL('3oqv0yf6b6fBAUVMtrD4YU',$,'MatAssoc','Material Associates',(#220),#217);
#220= IFCSANITARYTERMINALTYPE('3tazpf9xH05PFV$Hnk7kSd',$,'IFCSANITARYTERMINALTYPE',$,$,$,(#213),$,$,.BATH.);
#221= IFCRELDEFINESBYTYPE('0c_vOxzcPCZxI6pXZtWtjR',$,$,$,(#228),#220);
#222= IFCDIRECTION((1.0,0.0,0.0));
#223= IFCDIRECTION((0.0,1.0,0.0));
#224= IFCCARTESIANPOINT((0.0,0.0,0.0));
#225= IFCCARTESIANTRANSFORMATIONOPERATOR3D(#222,#223,#224,1.0,#226);
#226= IFCDIRECTION((0.0,0.0,1.0));
#227= IFCMAPPEDITEM(#213,#225);
#228= IFCSANITARYTERMINAL('0R_AIYbPX8Uv51WTQ8fVir',$,$,$,$,#55,#229,$,.NOTDEFINED.);
#229= IFCPRODUCTDEFINITIONSHAPE($,$,(#230));
#230= IFCSHAPEREPRESENTATION(#12,'Body','MappedRepresentation',(#227));
ENDSEC;

END-ISO-10303-21;


<IfcProject type="IfcProject" globalId="3d3abbb1-5f3a-4a05-af6d-bde757590997">
  <ownerHistory type="IfcOwnerHistory" globalId="722a9d06-3c3d-4f8b-83ac-c1fe3c5c9dc1" changeAction="ADDED">
    <owningUser type="IfcPersonAndOrganization" globalId="aa36c62c-2595-46a2-8460-03154bac117f">
      <thePerson type="IfcPerson" globalId="b4b83949-f157-4c65-b4e5-752baed6029a">
        <familyName type="IfcLabel" value="Mirtschin"/>
        <givenName type="IfcLabel" value="Jon"/>
      </thePerson>
      <theOrganization type="IfcOrganization" globalId="97c5224f-ae89-4188-9892-5d369a145558">
        <name type="IfcLabel" value="Geometry Gym Pty Ltd"/>
      </theOrganization>
    </owningUser>
    <owningApplication>
      <applicationDeveloper type="IfcOrganization" globalId="4c7e2853-bc22-450a-8f6a-65a7cdfb13bb">
        <name type="IfcLabel" value="Geometry Gym Pty Ltd"/>
      </applicationDeveloper>
      <version type="IfcLabel" value="********"/>
      <applicationFullName type="IfcLabel" value="Geometry Gym Plug-in for Grasshopper3d"/>
      <applicationIdentifier type="IfcIdentifier" value="ggGrasshopperIFC"/>
    </owningApplication>
    <lastModifiedDate type="IfcTimeStamp" value="1402094752"/>
    <creationDate type="IfcTimeStamp" value="1402094752"/>
  </ownerHistory>
  <name type="IfcLabel" value="Grasshopper Project"/>
  <isDecomposedBy type="IfcRelAggregates" globalId="8c324abe-a4ee-4e97-8487-932be8e06166">
    <description type="IfcText" value="Project Container for Buildings"/>
    <ownerHistory>722a9d06-3c3d-4f8b-83ac-c1fe3c5c9dc1</ownerHistory>
    <name type="IfcLabel" value="Project Container"/>
    <relatedObjects>
      <IfcObjectDefinition type="IfcBuilding" globalId="f8e62fc5-3fbd-4ccb-9141-d564f0da5308">
        <description type="IfcText" value="GH Building"/>
        <compositionType>ELEMENT</compositionType>
        <ownerHistory>722a9d06-3c3d-4f8b-83ac-c1fe3c5c9dc1</ownerHistory>
        <name type="IfcLabel" value="Grasshopper Building"/>
        <objectPlacement type="IfcLocalPlacement" globalId="a4d53fa2-f528-417b-ac0d-64fafd556a69">
          <relativePlacement type="IfcAxis2Placement3D">
            <location type="IfcCartesianPoint">
              <coordinates>
                <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
              </coordinates>
            </location>
            <axis type="IfcDirection">
              <directionRatios>
                <IfcReal type="IfcReal" value="0.0"/>
                <IfcReal type="IfcReal" value="0.0"/>
                <IfcReal type="IfcReal" value="1.0"/>
              </directionRatios>
            </axis>
            <refDirection type="IfcDirection">
              <directionRatios>
                <IfcReal type="IfcReal" value="1.0"/>
                <IfcReal type="IfcReal" value="0.0"/>
                <IfcReal type="IfcReal" value="0.0"/>
              </directionRatios>
            </refDirection>
          </relativePlacement>
        </objectPlacement>
        <longName type="IfcLabel" value="GH Building"/>
        <containsElements>
          <IfcRelContainedInSpatialStructure type="IfcRelContainedInSpatialStructure" globalId="85da3c75-e250-42d1-9151-f8529953c039">
            <description type="IfcText" value="Building Container for Elements"/>
            <ownerHistory>722a9d06-3c3d-4f8b-83ac-c1fe3c5c9dc1</ownerHistory>
            <name type="IfcLabel" value="Building"/>
            <relatedElements>
              <IfcProduct type="IfcBeamStandardCase" globalId="f90411c4-f72d-46e8-9da8-9bac65e22b81">
                <ownerHistory>722a9d06-3c3d-4f8b-83ac-c1fe3c5c9dc1</ownerHistory>
                <hasAssociations>
                  <IfcRelAssociates type="IfcRelAssociatesMaterial" globalId="0985cf6f-d5f9-4b91-94f5-7b46f483e10d">
                    <description type="IfcText" value="Material Associates"/>
                    <relatingMaterial type="IfcMaterialProfileSetUsage">
                      <forProfileSet type="IfcMaterialProfileSet" globalId="ffe800b4-c137-419f-ba63-26c93882b13d">
                        <name type="IfcLabel" value="IPE600"/>
                        <materialProfiles>
                          <IfcMaterialProfile type="IfcMaterialProfile" globalId="4df70c51-932d-4130-b055-ec0d7470169c">
                            <name type="IfcLabel" value="IPE600"/>
                            <material type="IfcMaterial" globalId="bd694f58-1dab-48d7-ab42-ba10f0d32ff4">
                              <name type="IfcLabel" value="S355JR"/>
                            </material>
                            <profile type="IfcIShapeProfileDef" profileType="AREA">
                              <profileName type="IfcLabel" value="IPE600"/>
                              <position type="IfcAxis2Placement2D">
                                <location type="IfcCartesianPoint">
                                  <coordinates>
                                    <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                    <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                  </coordinates>
                                </location>
                              </position>
                              <overallWidth type="IfcPositiveLengthMeasure" value="0.22"/>
                              <overallDepth type="IfcPositiveLengthMeasure" value="0.6"/>
                              <webThickness type="IfcPositiveLengthMeasure" value="0.012"/>
                              <flangeThickness type="IfcPositiveLengthMeasure" value="0.019"/>
                              <filletRadius type="IfcNonNegativeLengthMeasure" value="0.024"/>
                            </profile>
                            <priority type="IfcInteger" value="0"/>
                          </IfcMaterialProfile>
                        </materialProfiles>
                      </forProfileSet>
                      <cardinalPoint type="IfcCardinalPointReference" value="5"/>
                    </relatingMaterial>
                    <ownerHistory>722a9d06-3c3d-4f8b-83ac-c1fe3c5c9dc1</ownerHistory>
                    <name type="IfcLabel" value="MatAssoc"/>
                  </IfcRelAssociates>
                </hasAssociations>
                <isTypedBy>
                  <IfcRelDefinesByType type="IfcRelDefinesByType" globalId="240e1410-3463-4db7-b1fa-fde240b50996">
                    <ownerHistory>722a9d06-3c3d-4f8b-83ac-c1fe3c5c9dc1</ownerHistory>
                    <name type="IfcLabel" value="IPE600"/>
                    <relatingType type="IfcBeamType" globalId="8cf0af3b-ac08-493d-ba19-558a84e81d1c" predefinedType="BEAM">
                      <ownerHistory>722a9d06-3c3d-4f8b-83ac-c1fe3c5c9dc1</ownerHistory>
                      <name type="IfcLabel" value="IPE600"/>
                      <hasAssociations>
                        <IfcRelAssociates type="IfcRelAssociatesMaterial" globalId="d93de3be-d641-4069-9506-c8f54e288638">
                          <description type="IfcText" value="Material Associates"/>
                          <relatingMaterial>ffe800b4-c137-419f-ba63-26c93882b13d</relatingMaterial>
                          <ownerHistory>722a9d06-3c3d-4f8b-83ac-c1fe3c5c9dc1</ownerHistory>
                          <name type="IfcLabel" value="MatAssoc"/>
                        </IfcRelAssociates>
                      </hasAssociations>
                    </relatingType>
                  </IfcRelDefinesByType>
                </isTypedBy>
                <objectPlacement type="IfcLocalPlacement" globalId="912170fe-7f1d-4eb3-b140-3dd5c4e54567">
                  <relativePlacement type="IfcAxis2Placement3D">
                    <location type="IfcCartesianPoint">
                      <coordinates>
                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                      </coordinates>
                    </location>
                    <axis type="IfcDirection">
                      <directionRatios>
                        <IfcReal type="IfcReal" value="0.68965517"/>
                        <IfcReal type="IfcReal" value="0.72413793"/>
                        <IfcReal type="IfcReal" value="0.0"/>
                      </directionRatios>
                    </axis>
                    <refDirection type="IfcDirection">
                      <directionRatios>
                        <IfcReal type="IfcReal" value="-0.72413793"/>
                        <IfcReal type="IfcReal" value="0.68965517"/>
                        <IfcReal type="IfcReal" value="0.0"/>
                      </directionRatios>
                    </refDirection>
                  </relativePlacement>
                </objectPlacement>
                <representation type="IfcProductDefinitionShape">
                  <representations>
                    <IfcRepresentation type="IfcShapeRepresentation">
                      <contextOfItems type="IfcGeometricRepresentationSubContext" globalId="4ee1f1a5-5462-43d9-bf6c-ea5b07565760" targetView="MODEL_VIEW">
                        <contextIdentifier type="IfcLabel" value="Axis"/>
                        <contextType type="IfcLabel" value="Model"/>
                      </contextOfItems>
                      <representationIdentifier type="IfcLabel" value="Axis"/>
                      <representationType type="IfcLabel" value="Curve3D"/>
                      <items>
                        <IfcRepresentationItem type="IfcTrimmedCurve" masterRepresentation="PARAMETER">
                          <basisCurve type="IfcCircle">
                            <position type="IfcAxis2Placement3D">
                              <location type="IfcCartesianPoint">
                                <coordinates>
                                  <IfcLengthMeasure type="IfcLengthMeasure" value="7.25"/>
                                  <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                  <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                </coordinates>
                              </location>
                              <axis type="IfcDirection">
                                <directionRatios>
                                  <IfcReal type="IfcReal" value="0.0"/>
                                  <IfcReal type="IfcReal" value="1.0"/>
                                  <IfcReal type="IfcReal" value="0.0"/>
                                </directionRatios>
                              </axis>
                              <refDirection type="IfcDirection">
                                <directionRatios>
                                  <IfcReal type="IfcReal" value="-1.0"/>
                                  <IfcReal type="IfcReal" value="0.0"/>
                                  <IfcReal type="IfcReal" value="0.0"/>
                                </directionRatios>
                              </refDirection>
                            </position>
                            <radius type="IfcPositiveLengthMeasure" value="7.25"/>
                          </basisCurve>
                          <trim1>
                            <trim1 type="IfcParameterValue" value="0.0"/>
                            <trim1 type="IfcCartesianPoint">
                              <coordinates>
                                <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                              </coordinates>
                            </trim1>
                          </trim1>
                          <trim2>
                            <trim2 type="IfcParameterValue" value="1.52202550844946"/>
                            <trim2 type="IfcCartesianPoint">
                              <coordinates>
                                <IfcLengthMeasure type="IfcLengthMeasure" value="6.89655172413793"/>
                                <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                <IfcLengthMeasure type="IfcLengthMeasure" value="7.24137931034483"/>
                              </coordinates>
                            </trim2>
                          </trim2>
                          <senseAgreement type="IfcBoolean" value="false"/>
                        </IfcRepresentationItem>
                      </items>
                    </IfcRepresentation>
                    <IfcRepresentation type="IfcShapeRepresentation">
                      <contextOfItems type="IfcGeometricRepresentationSubContext" globalId="d45a5819-18aa-4aac-be4a-a2dab15f9beb" targetView="MODEL_VIEW">
                        <contextIdentifier type="IfcLabel" value="Body"/>
                        <contextType type="IfcLabel" value="Model"/>
                      </contextOfItems>
                      <representationIdentifier type="IfcLabel" value="Body"/>
                      <representationType type="IfcLabel" value="SweptSolid"/>
                      <items>
                        <IfcRepresentationItem type="IfcRevolvedAreaSolid">
                          <angle type="IfcPlaneAngleMeasure" value="1.52202550844946"/>
                          <sweptArea type="IfcIShapeProfileDef" profileType="AREA">
                            <profileName type="IfcLabel" value="IPE600"/>
                            <position type="IfcAxis2Placement2D">
                              <location type="IfcCartesianPoint">
                                <coordinates>
                                  <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                  <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                </coordinates>
                              </location>
                            </position>
                            <overallWidth type="IfcPositiveLengthMeasure" value="0.22"/>
                            <overallDepth type="IfcPositiveLengthMeasure" value="0.6"/>
                            <webThickness type="IfcPositiveLengthMeasure" value="0.012"/>
                            <flangeThickness type="IfcPositiveLengthMeasure" value="0.019"/>
                            <filletRadius type="IfcNonNegativeLengthMeasure" value="0.024"/>
                          </sweptArea>
                          <axis type="IfcAxis1Placement">
                            <location type="IfcCartesianPoint">
                              <coordinates>
                                <IfcLengthMeasure type="IfcLengthMeasure" value="7.25"/>
                                <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                              </coordinates>
                            </location>
                            <axis type="IfcDirection">
                              <directionRatios>
                                <IfcReal type="IfcReal" value="0.0"/>
                                <IfcReal type="IfcReal" value="1.0"/>
                                <IfcReal type="IfcReal" value="0.0"/>
                              </directionRatios>
                            </axis>
                          </axis>
                        </IfcRepresentationItem>
                      </items>
                    </IfcRepresentation>
                  </representations>
                </representation>
              </IfcProduct>
            </relatedElements>
          </IfcRelContainedInSpatialStructure>
        </containsElements>
      </IfcObjectDefinition>
    </relatedObjects>
  </isDecomposedBy>
  <longName type="IfcLabel" value="Grasshopper Project"/>
  <phase type="IfcLabel"/>
  <representationContexts>
    <IfcRepresentationContext type="IfcGeometricRepresentationContext" globalId="7bfb076a-8c44-4c04-9572-1170f93b48c9">
      <worldCoordinateSystem type="IfcAxis2Placement3D">
        <location type="IfcCartesianPoint">
          <coordinates>
            <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
            <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
            <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
          </coordinates>
        </location>
        <axis type="IfcDirection">
          <directionRatios>
            <IfcReal type="IfcReal" value="0.0"/>
            <IfcReal type="IfcReal" value="0.0"/>
            <IfcReal type="IfcReal" value="1.0"/>
          </directionRatios>
        </axis>
        <refDirection type="IfcDirection">
          <directionRatios>
            <IfcReal type="IfcReal" value="1.0"/>
            <IfcReal type="IfcReal" value="0.0"/>
            <IfcReal type="IfcReal" value="0.0"/>
          </directionRatios>
        </refDirection>
      </worldCoordinateSystem>
      <contextType type="IfcLabel" value="Model"/>
      <coordinateSpaceDimension type="IfcDimensionCount" value="3"/>
      <precision type="IfcReal" value="1.0E-5"/>
      <trueNorth type="IfcDirection">
        <directionRatios>
          <IfcReal type="IfcReal" value="0.0"/>
          <IfcReal type="IfcReal" value="1.0"/>
        </directionRatios>
      </trueNorth>
      <hasSubContexts>
        <IfcGeometricRepresentationSubContext>d45a5819-18aa-4aac-be4a-a2dab15f9beb</IfcGeometricRepresentationSubContext>
        <IfcGeometricRepresentationSubContext>4ee1f1a5-5462-43d9-bf6c-ea5b07565760</IfcGeometricRepresentationSubContext>
      </hasSubContexts>
    </IfcRepresentationContext>
  </representationContexts>
  <unitsInContext>
    <units>
      <IfcUnit type="IfcSIUnit" globalId="f48c156c-9911-47ef-8e08-c37ea1c1fae0">
        <dimensions lengthExponent="3" massExponent="0" timeExponent="0" electricCurrentExponent="0" thermodynamicTemperatureExponent="0" amountOfSubstanceExponent="0" luminousIntensityExponent="0"/>
        <unitType>VOLUMEUNIT</unitType>
        <name>CUBIC_METRE</name>
      </IfcUnit>
      <IfcUnit type="IfcSIUnit" globalId="674faa0d-bb18-424d-826c-70355aa4592f">
        <dimensions lengthExponent="1" massExponent="0" timeExponent="0" electricCurrentExponent="0" thermodynamicTemperatureExponent="0" amountOfSubstanceExponent="0" luminousIntensityExponent="0"/>
        <unitType>LENGTHUNIT</unitType>
        <name>METRE</name>
      </IfcUnit>
      <IfcUnit type="IfcSIUnit" globalId="1fd82e5c-eaef-4750-a358-4cd54c236065">
        <dimensions lengthExponent="2" massExponent="0" timeExponent="0" electricCurrentExponent="0" thermodynamicTemperatureExponent="0" amountOfSubstanceExponent="0" luminousIntensityExponent="0"/>
        <unitType>AREAUNIT</unitType>
        <name>SQUARE_METRE</name>
      </IfcUnit>
      <IfcUnit type="IfcSIUnit" globalId="21ce0bf6-9f6f-42f1-9311-39d7574f5493">
        <dimensions lengthExponent="0" massExponent="0" timeExponent="0" electricCurrentExponent="0" thermodynamicTemperatureExponent="0" amountOfSubstanceExponent="0" luminousIntensityExponent="0"/>
        <unitType>PLANEANGLEUNIT</unitType>
        <name>RADIAN</name>
      </IfcUnit>
    </units>
  </unitsInContext>
</IfcProject>

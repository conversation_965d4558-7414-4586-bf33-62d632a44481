<IfcProject type="IfcProject" globalId="01702def-0561-48bf-b523-93e1a1b3f6d0">
  <ownerHistory type="IfcOwnerHistory" globalId="219bbd20-3ba1-4d9c-a9e6-dcf5eb53d743" changeAction="ADDED">
    <owningUser type="IfcPersonAndOrganization" globalId="dc2363ef-a9e0-4a15-8e87-a1599ed65498">
      <thePerson type="IfcPerson" globalId="354ac708-2228-4539-b03a-acc292347a6d">
        <identification type="IfcIdentifier" value="Jon"/>
        <familyName type="IfcLabel" value="Jon"/>
      </thePerson>
      <theOrganization type="IfcOrganization" globalId="f61296ea-c026-4fb3-937b-26b56ca5d8d7">
        <name type="IfcLabel" value="Geometry Gym Pty Ltd"/>
      </theOrganization>
    </owningUser>
    <owningApplication>
      <applicationDeveloper type="IfcOrganization" globalId="2f0cb4ba-8416-4488-b05a-c6c57f7c4289">
        <name type="IfcLabel" value="Geometry Gym Pty Ltd"/>
      </applicationDeveloper>
      <version type="IfcLabel" value="*******"/>
      <applicationFullName type="IfcLabel" value="ggRhinoIFC - Geometry Gym Plug-in for Rhino3d"/>
      <applicationIdentifier type="IfcIdentifier" value="ggRhinoIFC"/>
    </owningApplication>
    <lastModifiedDate type="IfcTimeStamp" value="1418084874"/>
    <creationDate type="IfcTimeStamp" value="1418084874"/>
  </ownerHistory>
  <name type="IfcLabel" value="IfcProject"/>
  <isDecomposedBy type="IfcRelAggregates" globalId="13efd591-b7db-4ebf-905b-9a89e6ff46bc">
    <description type="IfcText" value="Project Container for Buildings"/>
    <name type="IfcLabel" value="Project Container"/>
    <relatedObjects>
      <IfcObjectDefinition type="IfcBuilding" globalId="3b26ff9e-7b1c-4db3-ab03-6cbedce61af4" compositionType="ELEMENT">
        <name type="IfcLabel" value="IfcBuilding"/>
        <objectPlacement type="IfcLocalPlacement" globalId="de3f0a69-a78a-4200-b0cc-7c35c2103373">
          <relativePlacement type="IfcAxis2Placement3D">
            <location type="IfcCartesianPoint">
              <coordinates>
                <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
              </coordinates>
            </location>
          </relativePlacement>
        </objectPlacement>
        <containsElements>
          <IfcRelContainedInSpatialStructure type="IfcRelContainedInSpatialStructure" globalId="9d5fd33c-0e09-42c1-9c49-4d9fa0beb03d">
            <description type="IfcText" value="Building Container for Elements"/>
            <name type="IfcLabel" value="Building"/>
            <relatedElements>
              <IfcProduct type="IfcBeamStandardCase" globalId="fee20e30-fc4e-4bb5-a23e-9276af772ec1">
                <name type="IfcLabel" value="BotLeft"/>
                <hasAssociations>
                  <IfcRelAssociates type="IfcRelAssociatesMaterial" globalId="151f8b12-fc45-4b9c-9ba0-0acc803b3837">
                    <description type="IfcText" value="Material Associates"/>
                    <relatingMaterial type="IfcMaterialProfileSetUsage">
                      <forProfileSet type="IfcMaterialProfileSet" globalId="dea5ad38-673d-470f-a723-37f3f2ca7e7e">
                        <name type="IfcLabel" value="IPE200"/>
                        <materialProfiles>
                          <IfcMaterialProfile type="IfcMaterialProfile" globalId="d3de0452-5e19-4e01-abac-9134a0a6100f">
                            <name type="IfcLabel" value="IPE200"/>
                            <material type="IfcMaterial" globalId="bfc7f884-dac9-4b60-ab61-ee1fd83b6df9">
                              <name type="IfcLabel" value="S355JR"/>
                              <category type="IfcLabel" value="Steel"/>
                            </material>
                            <profile type="IfcIShapeProfileDef" profileType="AREA">
                              <profileName type="IfcLabel" value="IPE200"/>
                              <overallWidth type="IfcPositiveLengthMeasure" value="100.0"/>
                              <overallDepth type="IfcPositiveLengthMeasure" value="200.0"/>
                              <webThickness type="IfcPositiveLengthMeasure" value="5.6"/>
                              <flangeThickness type="IfcPositiveLengthMeasure" value="8.5"/>
                              <filletRadius type="IfcNonNegativeLengthMeasure" value="12.0"/>
                            </profile>
                            <priority type="IfcInteger" value="0"/>
                          </IfcMaterialProfile>
                        </materialProfiles>
                      </forProfileSet>
                      <cardinalPoint type="IfcCardinalPointReference" value="1"/>
                    </relatingMaterial>
                    <name type="IfcLabel" value="MatAssoc"/>
                  </IfcRelAssociates>
                </hasAssociations>
                <isTypedBy>
                  <IfcRelDefinesByType type="IfcRelDefinesByType" globalId="088fe1e1-33e1-4c5d-bfd2-57b0db400e9f">
                    <name type="IfcLabel" value="IPE200"/>
                    <relatingType type="IfcBeamType" globalId="0c49e54e-1723-46cf-894b-4c1f12a87313" predefinedType="JOIST">
                      <name type="IfcLabel" value="IPE200"/>
                      <hasAssociations>
                        <IfcRelAssociates type="IfcRelAssociatesMaterial" globalId="f7e18a60-8198-4ccb-ab7b-b453bd545f0c">
                          <description type="IfcText" value="Material Associates"/>
                          <relatingMaterial>dea5ad38-673d-470f-a723-37f3f2ca7e7e</relatingMaterial>
                          <name type="IfcLabel" value="MatAssoc"/>
                        </IfcRelAssociates>
                      </hasAssociations>
                    </relatingType>
                  </IfcRelDefinesByType>
                </isTypedBy>
                <objectPlacement type="IfcLocalPlacement" globalId="5ecc0db4-49fd-487c-95fa-b4de991e974a">
                  <relativePlacement type="IfcAxis2Placement3D">
                    <location type="IfcCartesianPoint">
                      <coordinates>
                        <IfcLengthMeasure type="IfcLengthMeasure" value="500.0"/>
                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                      </coordinates>
                    </location>
                    <axis type="IfcDirection">
                      <directionRatios>
                        <IfcReal type="IfcReal" value="0.0"/>
                        <IfcReal type="IfcReal" value="1.0"/>
                        <IfcReal type="IfcReal" value="0.0"/>
                      </directionRatios>
                    </axis>
                    <refDirection type="IfcDirection">
                      <directionRatios>
                        <IfcReal type="IfcReal" value="-1.0"/>
                        <IfcReal type="IfcReal" value="0.0"/>
                        <IfcReal type="IfcReal" value="0.0"/>
                      </directionRatios>
                    </refDirection>
                  </relativePlacement>
                </objectPlacement>
                <representation type="IfcProductDefinitionShape">
                  <representations>
                    <IfcRepresentation type="IfcShapeRepresentation">
                      <contextOfItems type="IfcGeometricRepresentationSubContext" globalId="4e897eb0-5043-401f-94f3-93bc358e09c2" targetView="MODEL_VIEW">
                        <contextIdentifier type="IfcLabel" value="Axis"/>
                        <contextType type="IfcLabel" value="Model"/>
                      </contextOfItems>
                      <representationIdentifier type="IfcLabel" value="Axis"/>
                      <representationType type="IfcLabel" value="Curve3D"/>
                      <items>
                        <IfcRepresentationItem type="IfcPolyline">
                          <points>
                            <IfcCartesianPoint type="IfcCartesianPoint">
                              <coordinates>
                                <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                              </coordinates>
                            </IfcCartesianPoint>
                            <IfcCartesianPoint type="IfcCartesianPoint">
                              <coordinates>
                                <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                <IfcLengthMeasure type="IfcLengthMeasure" value="1000.0"/>
                              </coordinates>
                            </IfcCartesianPoint>
                          </points>
                        </IfcRepresentationItem>
                      </items>
                    </IfcRepresentation>
                    <IfcRepresentation type="IfcShapeRepresentation">
                      <contextOfItems type="IfcGeometricRepresentationSubContext" globalId="43f39f10-8a49-4f63-8bce-269039d9e7f4" targetView="MODEL_VIEW">
                        <contextIdentifier type="IfcLabel" value="Body"/>
                        <contextType type="IfcLabel" value="Model"/>
                      </contextOfItems>
                      <representationIdentifier type="IfcLabel" value="Body"/>
                      <representationType type="IfcLabel" value="SweptSolid"/>
                      <items>
                        <IfcRepresentationItem type="IfcExtrudedAreaSolid">
                          <sweptArea type="IfcIShapeProfileDef" profileType="AREA">
                            <profileName type="IfcLabel" value="IPE200"/>
                            <overallWidth type="IfcPositiveLengthMeasure" value="100.0"/>
                            <overallDepth type="IfcPositiveLengthMeasure" value="200.0"/>
                            <webThickness type="IfcPositiveLengthMeasure" value="5.6"/>
                            <flangeThickness type="IfcPositiveLengthMeasure" value="8.5"/>
                            <filletRadius type="IfcNonNegativeLengthMeasure" value="12.0"/>
                          </sweptArea>
                          <position type="IfcAxis2Placement3D">
                            <location type="IfcCartesianPoint">
                              <coordinates>
                                <IfcLengthMeasure type="IfcLengthMeasure" value="-50.0"/>
                                <IfcLengthMeasure type="IfcLengthMeasure" value="100.0"/>
                                <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                              </coordinates>
                            </location>
                            <axis type="IfcDirection">
                              <directionRatios>
                                <IfcReal type="IfcReal" value="0.0"/>
                                <IfcReal type="IfcReal" value="0.0"/>
                                <IfcReal type="IfcReal" value="1.0"/>
                              </directionRatios>
                            </axis>
                            <refDirection type="IfcDirection">
                              <directionRatios>
                                <IfcReal type="IfcReal" value="1.0"/>
                                <IfcReal type="IfcReal" value="0.0"/>
                                <IfcReal type="IfcReal" value="0.0"/>
                              </directionRatios>
                            </refDirection>
                          </position>
                          <extrudedDirection type="IfcDirection">
                            <directionRatios>
                              <IfcReal type="IfcReal" value="0.0"/>
                              <IfcReal type="IfcReal" value="0.0"/>
                              <IfcReal type="IfcReal" value="1.0"/>
                            </directionRatios>
                          </extrudedDirection>
                          <depth type="IfcPositiveLengthMeasure" value="1000.0"/>
                        </IfcRepresentationItem>
                      </items>
                    </IfcRepresentation>
                  </representations>
                </representation>
              </IfcProduct>
              <IfcProduct type="IfcBeamStandardCase" globalId="da522dea-5be0-4ede-8bb8-8ea431fa19d6">
                <name type="IfcLabel" value="TopRight"/>
                <hasAssociations>
                  <IfcRelAssociates type="IfcRelAssociatesMaterial" globalId="eafc0da2-e8c7-4785-8bf5-5ed7e5ce0ab6">
                    <description type="IfcText" value="Material Associates"/>
                    <relatingMaterial type="IfcMaterialProfileSetUsage">
                      <forProfileSet>dea5ad38-673d-470f-a723-37f3f2ca7e7e</forProfileSet>
                      <cardinalPoint type="IfcCardinalPointReference" value="9"/>
                    </relatingMaterial>
                    <name type="IfcLabel" value="MatAssoc"/>
                  </IfcRelAssociates>
                </hasAssociations>
                <isTypedBy>
                  <IfcRelDefinesByType>088fe1e1-33e1-4c5d-bfd2-57b0db400e9f</IfcRelDefinesByType>
                </isTypedBy>
                <objectPlacement type="IfcLocalPlacement" globalId="0abed0d3-6451-4d1c-829b-4a39950ea810">
                  <relativePlacement type="IfcAxis2Placement3D">
                    <location type="IfcCartesianPoint">
                      <coordinates>
                        <IfcLengthMeasure type="IfcLengthMeasure" value="500.0"/>
                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                      </coordinates>
                    </location>
                    <axis type="IfcDirection">
                      <directionRatios>
                        <IfcReal type="IfcReal" value="0.0"/>
                        <IfcReal type="IfcReal" value="1.0"/>
                        <IfcReal type="IfcReal" value="0.0"/>
                      </directionRatios>
                    </axis>
                    <refDirection type="IfcDirection">
                      <directionRatios>
                        <IfcReal type="IfcReal" value="-1.0"/>
                        <IfcReal type="IfcReal" value="0.0"/>
                        <IfcReal type="IfcReal" value="0.0"/>
                      </directionRatios>
                    </refDirection>
                  </relativePlacement>
                </objectPlacement>
                <representation type="IfcProductDefinitionShape">
                  <representations>
                    <IfcRepresentation type="IfcShapeRepresentation">
                      <contextOfItems>4e897eb0-5043-401f-94f3-93bc358e09c2</contextOfItems>
                      <representationIdentifier type="IfcLabel" value="Axis"/>
                      <representationType type="IfcLabel" value="Curve3D"/>
                      <items>
                        <IfcRepresentationItem type="IfcPolyline">
                          <points>
                            <IfcCartesianPoint type="IfcCartesianPoint">
                              <coordinates>
                                <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                              </coordinates>
                            </IfcCartesianPoint>
                            <IfcCartesianPoint type="IfcCartesianPoint">
                              <coordinates>
                                <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                <IfcLengthMeasure type="IfcLengthMeasure" value="1000.0"/>
                              </coordinates>
                            </IfcCartesianPoint>
                          </points>
                        </IfcRepresentationItem>
                      </items>
                    </IfcRepresentation>
                    <IfcRepresentation type="IfcShapeRepresentation">
                      <contextOfItems>43f39f10-8a49-4f63-8bce-269039d9e7f4</contextOfItems>
                      <representationIdentifier type="IfcLabel" value="Body"/>
                      <representationType type="IfcLabel" value="SweptSolid"/>
                      <items>
                        <IfcRepresentationItem type="IfcExtrudedAreaSolid">
                          <sweptArea type="IfcIShapeProfileDef" profileType="AREA">
                            <profileName type="IfcLabel" value="IPE200"/>
                            <overallWidth type="IfcPositiveLengthMeasure" value="100.0"/>
                            <overallDepth type="IfcPositiveLengthMeasure" value="200.0"/>
                            <webThickness type="IfcPositiveLengthMeasure" value="5.6"/>
                            <flangeThickness type="IfcPositiveLengthMeasure" value="8.5"/>
                            <filletRadius type="IfcNonNegativeLengthMeasure" value="12.0"/>
                          </sweptArea>
                          <position type="IfcAxis2Placement3D">
                            <location type="IfcCartesianPoint">
                              <coordinates>
                                <IfcLengthMeasure type="IfcLengthMeasure" value="50.0"/>
                                <IfcLengthMeasure type="IfcLengthMeasure" value="-100.0"/>
                                <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                              </coordinates>
                            </location>
                            <axis type="IfcDirection">
                              <directionRatios>
                                <IfcReal type="IfcReal" value="0.0"/>
                                <IfcReal type="IfcReal" value="0.0"/>
                                <IfcReal type="IfcReal" value="1.0"/>
                              </directionRatios>
                            </axis>
                            <refDirection type="IfcDirection">
                              <directionRatios>
                                <IfcReal type="IfcReal" value="1.0"/>
                                <IfcReal type="IfcReal" value="0.0"/>
                                <IfcReal type="IfcReal" value="0.0"/>
                              </directionRatios>
                            </refDirection>
                          </position>
                          <extrudedDirection type="IfcDirection">
                            <directionRatios>
                              <IfcReal type="IfcReal" value="0.0"/>
                              <IfcReal type="IfcReal" value="0.0"/>
                              <IfcReal type="IfcReal" value="1.0"/>
                            </directionRatios>
                          </extrudedDirection>
                          <depth type="IfcPositiveLengthMeasure" value="1000.0"/>
                        </IfcRepresentationItem>
                      </items>
                    </IfcRepresentation>
                  </representations>
                </representation>
              </IfcProduct>
              <IfcProduct type="IfcBeamStandardCase" globalId="06e1f37d-580b-48d7-8aac-93d7e8ed0bbb">
                <name type="IfcLabel" value="TopMid"/>
                <hasAssociations>
                  <IfcRelAssociates type="IfcRelAssociatesMaterial" globalId="8b04d404-0161-4e68-8417-de9014b4331e">
                    <description type="IfcText" value="Material Associates"/>
                    <relatingMaterial type="IfcMaterialProfileSetUsage">
                      <forProfileSet>dea5ad38-673d-470f-a723-37f3f2ca7e7e</forProfileSet>
                      <cardinalPoint type="IfcCardinalPointReference" value="8"/>
                    </relatingMaterial>
                    <name type="IfcLabel" value="MatAssoc"/>
                  </IfcRelAssociates>
                </hasAssociations>
                <isTypedBy>
                  <IfcRelDefinesByType>088fe1e1-33e1-4c5d-bfd2-57b0db400e9f</IfcRelDefinesByType>
                </isTypedBy>
                <objectPlacement type="IfcLocalPlacement" globalId="5a43d11d-79df-459c-b41d-a3c88eb7e26d">
                  <relativePlacement type="IfcAxis2Placement3D">
                    <location type="IfcCartesianPoint">
                      <coordinates>
                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                      </coordinates>
                    </location>
                    <axis type="IfcDirection">
                      <directionRatios>
                        <IfcReal type="IfcReal" value="0.0"/>
                        <IfcReal type="IfcReal" value="1.0"/>
                        <IfcReal type="IfcReal" value="0.0"/>
                      </directionRatios>
                    </axis>
                    <refDirection type="IfcDirection">
                      <directionRatios>
                        <IfcReal type="IfcReal" value="-1.0"/>
                        <IfcReal type="IfcReal" value="0.0"/>
                        <IfcReal type="IfcReal" value="0.0"/>
                      </directionRatios>
                    </refDirection>
                  </relativePlacement>
                </objectPlacement>
                <representation type="IfcProductDefinitionShape">
                  <representations>
                    <IfcRepresentation type="IfcShapeRepresentation">
                      <contextOfItems>4e897eb0-5043-401f-94f3-93bc358e09c2</contextOfItems>
                      <representationIdentifier type="IfcLabel" value="Axis"/>
                      <representationType type="IfcLabel" value="Curve3D"/>
                      <items>
                        <IfcRepresentationItem type="IfcPolyline">
                          <points>
                            <IfcCartesianPoint type="IfcCartesianPoint">
                              <coordinates>
                                <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                              </coordinates>
                            </IfcCartesianPoint>
                            <IfcCartesianPoint type="IfcCartesianPoint">
                              <coordinates>
                                <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                <IfcLengthMeasure type="IfcLengthMeasure" value="1000.0"/>
                              </coordinates>
                            </IfcCartesianPoint>
                          </points>
                        </IfcRepresentationItem>
                      </items>
                    </IfcRepresentation>
                    <IfcRepresentation type="IfcShapeRepresentation">
                      <contextOfItems>43f39f10-8a49-4f63-8bce-269039d9e7f4</contextOfItems>
                      <representationIdentifier type="IfcLabel" value="Body"/>
                      <representationType type="IfcLabel" value="SweptSolid"/>
                      <items>
                        <IfcRepresentationItem type="IfcExtrudedAreaSolid">
                          <sweptArea type="IfcIShapeProfileDef" profileType="AREA">
                            <profileName type="IfcLabel" value="IPE200"/>
                            <overallWidth type="IfcPositiveLengthMeasure" value="100.0"/>
                            <overallDepth type="IfcPositiveLengthMeasure" value="200.0"/>
                            <webThickness type="IfcPositiveLengthMeasure" value="5.6"/>
                            <flangeThickness type="IfcPositiveLengthMeasure" value="8.5"/>
                            <filletRadius type="IfcNonNegativeLengthMeasure" value="12.0"/>
                          </sweptArea>
                          <position type="IfcAxis2Placement3D">
                            <location type="IfcCartesianPoint">
                              <coordinates>
                                <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                <IfcLengthMeasure type="IfcLengthMeasure" value="-100.0"/>
                                <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                              </coordinates>
                            </location>
                            <axis type="IfcDirection">
                              <directionRatios>
                                <IfcReal type="IfcReal" value="0.0"/>
                                <IfcReal type="IfcReal" value="0.0"/>
                                <IfcReal type="IfcReal" value="1.0"/>
                              </directionRatios>
                            </axis>
                            <refDirection type="IfcDirection">
                              <directionRatios>
                                <IfcReal type="IfcReal" value="1.0"/>
                                <IfcReal type="IfcReal" value="0.0"/>
                                <IfcReal type="IfcReal" value="0.0"/>
                              </directionRatios>
                            </refDirection>
                          </position>
                          <extrudedDirection type="IfcDirection">
                            <directionRatios>
                              <IfcReal type="IfcReal" value="0.0"/>
                              <IfcReal type="IfcReal" value="0.0"/>
                              <IfcReal type="IfcReal" value="1.0"/>
                            </directionRatios>
                          </extrudedDirection>
                          <depth type="IfcPositiveLengthMeasure" value="1000.0"/>
                        </IfcRepresentationItem>
                      </items>
                    </IfcRepresentation>
                  </representations>
                </representation>
              </IfcProduct>
              <IfcProduct type="IfcBeamStandardCase" globalId="ccf249bd-bf10-4df2-931f-f29c8f04ff2f">
                <name type="IfcLabel" value="BotMid"/>
                <hasAssociations>
                  <IfcRelAssociates type="IfcRelAssociatesMaterial" globalId="7bf89dd8-fb51-493a-92b7-f63af1d56d01">
                    <description type="IfcText" value="Material Associates"/>
                    <relatingMaterial type="IfcMaterialProfileSetUsage">
                      <forProfileSet>dea5ad38-673d-470f-a723-37f3f2ca7e7e</forProfileSet>
                      <cardinalPoint type="IfcCardinalPointReference" value="2"/>
                    </relatingMaterial>
                    <name type="IfcLabel" value="MatAssoc"/>
                  </IfcRelAssociates>
                </hasAssociations>
                <isTypedBy>
                  <IfcRelDefinesByType>088fe1e1-33e1-4c5d-bfd2-57b0db400e9f</IfcRelDefinesByType>
                </isTypedBy>
                <objectPlacement type="IfcLocalPlacement" globalId="7f608db8-2027-476a-aba5-961681e81f9f">
                  <relativePlacement type="IfcAxis2Placement3D">
                    <location type="IfcCartesianPoint">
                      <coordinates>
                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                      </coordinates>
                    </location>
                    <axis type="IfcDirection">
                      <directionRatios>
                        <IfcReal type="IfcReal" value="0.0"/>
                        <IfcReal type="IfcReal" value="1.0"/>
                        <IfcReal type="IfcReal" value="0.0"/>
                      </directionRatios>
                    </axis>
                    <refDirection type="IfcDirection">
                      <directionRatios>
                        <IfcReal type="IfcReal" value="-1.0"/>
                        <IfcReal type="IfcReal" value="0.0"/>
                        <IfcReal type="IfcReal" value="0.0"/>
                      </directionRatios>
                    </refDirection>
                  </relativePlacement>
                </objectPlacement>
                <representation type="IfcProductDefinitionShape">
                  <representations>
                    <IfcRepresentation type="IfcShapeRepresentation">
                      <contextOfItems>4e897eb0-5043-401f-94f3-93bc358e09c2</contextOfItems>
                      <representationIdentifier type="IfcLabel" value="Axis"/>
                      <representationType type="IfcLabel" value="Curve3D"/>
                      <items>
                        <IfcRepresentationItem type="IfcPolyline">
                          <points>
                            <IfcCartesianPoint type="IfcCartesianPoint">
                              <coordinates>
                                <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                              </coordinates>
                            </IfcCartesianPoint>
                            <IfcCartesianPoint type="IfcCartesianPoint">
                              <coordinates>
                                <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                <IfcLengthMeasure type="IfcLengthMeasure" value="1000.0"/>
                              </coordinates>
                            </IfcCartesianPoint>
                          </points>
                        </IfcRepresentationItem>
                      </items>
                    </IfcRepresentation>
                    <IfcRepresentation type="IfcShapeRepresentation">
                      <contextOfItems>43f39f10-8a49-4f63-8bce-269039d9e7f4</contextOfItems>
                      <representationIdentifier type="IfcLabel" value="Body"/>
                      <representationType type="IfcLabel" value="SweptSolid"/>
                      <items>
                        <IfcRepresentationItem type="IfcExtrudedAreaSolid">
                          <sweptArea type="IfcIShapeProfileDef" profileType="AREA">
                            <profileName type="IfcLabel" value="IPE200"/>
                            <overallWidth type="IfcPositiveLengthMeasure" value="100.0"/>
                            <overallDepth type="IfcPositiveLengthMeasure" value="200.0"/>
                            <webThickness type="IfcPositiveLengthMeasure" value="5.6"/>
                            <flangeThickness type="IfcPositiveLengthMeasure" value="8.5"/>
                            <filletRadius type="IfcNonNegativeLengthMeasure" value="12.0"/>
                          </sweptArea>
                          <position type="IfcAxis2Placement3D">
                            <location type="IfcCartesianPoint">
                              <coordinates>
                                <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                <IfcLengthMeasure type="IfcLengthMeasure" value="100.0"/>
                                <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                              </coordinates>
                            </location>
                            <axis type="IfcDirection">
                              <directionRatios>
                                <IfcReal type="IfcReal" value="0.0"/>
                                <IfcReal type="IfcReal" value="0.0"/>
                                <IfcReal type="IfcReal" value="1.0"/>
                              </directionRatios>
                            </axis>
                            <refDirection type="IfcDirection">
                              <directionRatios>
                                <IfcReal type="IfcReal" value="1.0"/>
                                <IfcReal type="IfcReal" value="0.0"/>
                                <IfcReal type="IfcReal" value="0.0"/>
                              </directionRatios>
                            </refDirection>
                          </position>
                          <extrudedDirection type="IfcDirection">
                            <directionRatios>
                              <IfcReal type="IfcReal" value="0.0"/>
                              <IfcReal type="IfcReal" value="0.0"/>
                              <IfcReal type="IfcReal" value="1.0"/>
                            </directionRatios>
                          </extrudedDirection>
                          <depth type="IfcPositiveLengthMeasure" value="1000.0"/>
                        </IfcRepresentationItem>
                      </items>
                    </IfcRepresentation>
                  </representations>
                </representation>
              </IfcProduct>
            </relatedElements>
          </IfcRelContainedInSpatialStructure>
        </containsElements>
        <buildingAddress type="IfcPostalAddress">
          <region type="IfcLabel" value="Unknown"/>
        </buildingAddress>
      </IfcObjectDefinition>
    </relatedObjects>
  </isDecomposedBy>
  <longName type="IfcLabel" value="IfcProject"/>
  <phase type="IfcLabel"/>
  <representationContexts>
    <IfcRepresentationContext type="IfcGeometricRepresentationContext" globalId="86b9e985-7d09-4e54-949d-84ad1d1cff63">
      <worldCoordinateSystem type="IfcAxis2Placement3D">
        <location type="IfcCartesianPoint">
          <coordinates>
            <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
            <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
            <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
          </coordinates>
        </location>
      </worldCoordinateSystem>
      <contextType type="IfcLabel" value="Model"/>
      <coordinateSpaceDimension type="IfcDimensionCount" value="3"/>
      <precision type="IfcReal" value="1.0E-4"/>
      <trueNorth type="IfcDirection">
        <directionRatios>
          <IfcReal type="IfcReal" value="0.0"/>
          <IfcReal type="IfcReal" value="1.0"/>
        </directionRatios>
      </trueNorth>
    </IfcRepresentationContext>
  </representationContexts>
  <unitsInContext>
    <units>
      <IfcUnit type="IfcSIUnit" globalId="7f554c3f-9112-4408-87aa-3ff3c9cb94a2">
        <dimensions lengthExponent="0" massExponent="0" timeExponent="1" electricCurrentExponent="0" thermodynamicTemperatureExponent="0" amountOfSubstanceExponent="0" luminousIntensityExponent="0"/>
        <unitType>TIMEUNIT</unitType>
        <name>SECOND</name>
      </IfcUnit>
      <IfcUnit type="IfcSIUnit" globalId="6306dfb6-d4aa-437f-badc-85804f558de0">
        <dimensions lengthExponent="0" massExponent="0" timeExponent="0" electricCurrentExponent="0" thermodynamicTemperatureExponent="0" amountOfSubstanceExponent="0" luminousIntensityExponent="0"/>
        <unitType>PLANEANGLEUNIT</unitType>
        <name>RADIAN</name>
      </IfcUnit>
      <IfcUnit type="IfcSIUnit" globalId="ced558f6-f0f3-498b-9aa1-bc830c8e2572">
        <dimensions lengthExponent="1" massExponent="0" timeExponent="0" electricCurrentExponent="0" thermodynamicTemperatureExponent="0" amountOfSubstanceExponent="0" luminousIntensityExponent="0"/>
        <unitType>LENGTHUNIT</unitType>
        <prefix>MILLI</prefix>
        <name>METRE</name>
      </IfcUnit>
    </units>
  </unitsInContext>
</IfcProject>

ISO-10303-21;
HEADER;
FILE_DESCRIPTION(('ViewDefinition [notYetAssigned]'),'2;1');
FILE_NAME(
/* name */ '',
/* time_stamp */ '2014-12-09T00:27:54',
/* author */ ('Jon'),
/* organization */ ('Unknown'),
/* preprocessor_version */ 'ggIFC - Exporter by Geometry Gym Pty Ltd',
/* originating_system */ 'ggIFC - Exporter by Geometry Gym Pty Ltd',
/* authorization */ 'None');

FILE_SCHEMA (('IFC4'));
ENDSEC;

DATA;
#1= IFCAPPLICATION(#2,'*******','ggRhinoIFC - Geometry Gym Plug-in for Rhino3d','ggRhinoIFC');
#2= IFCORGANIZATION($,'Geometry Gym Pty Ltd',$,$,$);
#3= IFCPERSONANDORGANIZATION(#4,#5,$);
#4= IFCPERSON('Jon','Jon',$,$,$,$,$,$);
#5= IFCORGANIZATION($,'Geometry Gym Pty Ltd',$,$,$);
#6= IFCOWNERHISTORY(#3,#1,$,.ADDED.,1418084874,$,$,1418084874);
#7= IFCGEOMETRICREPRESENTATIONCONTEXT($,'Model',3,0.0001,#8,#10);
#8= IFCAXIS2PLACEMENT3D(#9,$,$);
#9= IFCCARTESIANPOINT((0.0,0.0,0.0));
#10= IFCDIRECTION((0.0,1.0));
#11= IFCGEOMETRICREPRESENTATIONSUBCONTEXT('Axis','Model',*,*,*,*,#7,$,.MODEL_VIEW.,$);
#12= IFCGEOMETRICREPRESENTATIONSUBCONTEXT('Body','Model',*,*,*,*,#7,$,.MODEL_VIEW.,$);
#13= IFCGEOMETRICREPRESENTATIONCONTEXT($,'Model',3,0.0001,#8,#14);
#14= IFCDIRECTION((0.0,1.0));
#50= IFCBUILDING('3Sbzd4ubr6MvQtuA5xSQKS',$,'IfcBuilding',$,$,#51,$,$,.ELEMENT.,$,$,#57);
#51= IFCLOCALPLACEMENT($,#52);
#52= IFCAXIS2PLACEMENT3D(#53,$,$);
#53= IFCCARTESIANPOINT((0.0,0.0,0.0));
#54= IFCRELCONTAINEDINSPATIALSTRUCTURE('1pYMoWYJT0suxl9uDicwCi',$,'Building','Building Container for Elements',(#210,#307),#50);
#57= IFCPOSTALADDRESS($,$,$,$,$,$,$,'Unknown',$,$);
#100= IFCPROJECT('0nRpkpSj55bAQmOIUwgFF9',#6,'IfcProject',$,$,'IfcProject','',(#13),#101);
#101= IFCUNITASSIGNMENT((#102,#103,#104));
#102= IFCSIUNIT(*,.LENGTHUNIT.,.MILLI.,.METRE.);
#103= IFCSIUNIT(*,.PLANEANGLEUNIT.,$,.RADIAN.);
#104= IFCSIUNIT(*,.TIMEUNIT.,$,.SECOND.);
#105= IFCRELAGGREGATES('0ZSK$NTwX0ZumGiD_l88DU',$,'Project Container','Project Container for Buildings',#100,(#50));
#200= IFCMATERIAL('S355JR',$,'Steel');
#203= IFCISHAPEPROFILEDEF(.AREA.,'IPE200',$,100.0,200.0,5.6,8.5,12.0,$,$);
#204= IFCMATERIALPROFILE('IPE200',$,#200,#203,0,$);
#206= IFCMATERIALPROFILESET('IPE200',$,(#204),$);
#207= IFCRELASSOCIATESMATERIAL('3NJpPYshn7hvwqSvq74w67',$,'MatAssoc','Material Associates',(#208),#206);
#208= IFCBEAMTYPE('16fAEydZP8ZgekqrUBnNvl',$,'IPE200',$,$,$,$,$,$,.JOIST.);
#209= IFCRELDEFINESBYTYPE('0XWV4v8MvBRA5$Yt3yMuwJ',$,'IPE200',$,(#210),#208);
#210= IFCBEAMSTANDARDCASE('3jBl1CX_54IhgRT3DV2Tbh',$,'IPE200',$,$,#211,#225,$,$);
#211= IFCLOCALPLACEMENT($,#212);
#212= IFCAXIS2PLACEMENT3D(#213,#214,#215);
#213= IFCCARTESIANPOINT((0.0,0.0,0.0));
#214= IFCDIRECTION((0.0,1.0,0.0));
#215= IFCDIRECTION((-1.0,0.0,0.0));
#216= IFCMATERIALPROFILESETUSAGE(#206,5,$);
#217= IFCRELASSOCIATESMATERIAL('2ULDvS3oj4XwZ$ILa8_K3F',$,'MatAssoc','Material Associates',(#210),#216);
#218= IFCCARTESIANPOINT((0.0,0.0,0.0));
#219= IFCCARTESIANPOINT((0.0,0.0,1000.0));
#220= IFCPOLYLINE((#218,#219));
#221= IFCSHAPEREPRESENTATION(#11,'Axis','Curve3D',(#220));
#222= IFCDIRECTION((0.0,0.0,1.0));
#223= IFCEXTRUDEDAREASOLID(#203,$,#222,1000.0);
#224= IFCSHAPEREPRESENTATION(#12,'Body','SweptSolid',(#223));
#225= IFCPRODUCTDEFINITIONSHAPE($,$,(#221,#224));
#300= IFCCIRCLEHOLLOWPROFILEDEF(.AREA.,'CHS219.1x6.3',$,109.55,6.3);
#301= IFCMATERIALPROFILE('CHS219.1x6.3',$,#200,#300,0,$);
#303= IFCMATERIALPROFILESET('CHS219.1x6.3',$,(#301),$);
#304= IFCRELASSOCIATESMATERIAL('2iU9Vxa0H3zRLEmsAS1uqs',$,'MatAssoc','Material Associates',(#305),#303);
#305= IFCBEAMTYPE('0n2M0T11f6lBa0JnJAL6rh',$,'CHS219.1x6.3',$,$,$,$,$,$,.BEAM.);
#306= IFCRELDEFINESBYTYPE('3DMO3upez8Ex7IsbPK_lk2',$,'CHS219.1x6.3',$,(#307),#305);
#307= IFCBEAMSTANDARDCASE('1jfH$sd7T6QP28910X4fYG',$,'CHS219.1x6.3',$,$,#308,#322,$,$);
#308= IFCLOCALPLACEMENT($,#309);
#309= IFCAXIS2PLACEMENT3D(#310,#311,#312);
#310= IFCCARTESIANPOINT((500.0,0.0,0.0));
#311= IFCDIRECTION((0.0,1.0,0.0));
#312= IFCDIRECTION((-1.0,0.0,0.0));
#313= IFCMATERIALPROFILESETUSAGE(#303,5,$);
#314= IFCRELASSOCIATESMATERIAL('1qLmfo31b0cwgLLrCT5LaU',$,'MatAssoc','Material Associates',(#307),#313);
#315= IFCCARTESIANPOINT((0.0,0.0,0.0));
#316= IFCCARTESIANPOINT((0.0,0.0,1000.0));
#317= IFCPOLYLINE((#315,#316));
#318= IFCSHAPEREPRESENTATION(#11,'Axis','Curve3D',(#317));
#319= IFCDIRECTION((0.0,0.0,1.0));
#320= IFCEXTRUDEDAREASOLID(#300,$,#319,1000.0);
#321= IFCSHAPEREPRESENTATION(#12,'Body','SweptSolid',(#320));
#322= IFCPRODUCTDEFINITIONSHAPE($,$,(#318,#321));
ENDSEC;

END-ISO-10303-21;


<IfcProject type="IfcProject" globalId="3b7266e8-127d-4e31-a10d-59cd1eb671a7">
  <ownerHistory type="IfcOwnerHistory" globalId="77629125-cbe1-44ad-9b43-eae0cfdd8008" changeAction="ADDED">
    <owningUser type="IfcPersonAndOrganization" globalId="99901d94-66ea-44f4-a46b-00317be7461c">
      <thePerson type="IfcPerson" globalId="762f0ccd-8342-4d3d-bbd3-2064e43e39b4">
        <familyName type="IfcLabel" value="Liebich"/>
        <givenName type="IfcLabel" value="Thomas"/>
      </thePerson>
      <theOrganization type="IfcOrganization" globalId="f788c9cc-071d-46f4-9052-8885fe0c7c82">
        <name type="IfcLabel" value="buildingSMART International"/>
      </theOrganization>
    </owningUser>
    <owningApplication>
      <applicationDeveloper>f788c9cc-071d-46f4-9052-8885fe0c7c82</applicationDeveloper>
      <version type="IfcLabel" value="1.0"/>
      <applicationFullName type="IfcLabel" value="IFC text editor"/>
      <applicationIdentifier type="IfcIdentifier" value="ifcTE"/>
    </owningApplication>
    <lastModifiedDate type="IfcTimeStamp" value="1320688800"/>
    <creationDate type="IfcTimeStamp" value="1320688800"/>
  </ownerHistory>
  <name type="IfcLabel" value="proxy with brep"/>
  <isDecomposedBy type="IfcRelAggregates" globalId="a22f491f-f885-4116-8813-24d281d90c1d">
    <relatedObjects>
      <IfcObjectDefinition type="IfcBuilding" globalId="8f323372-d7b4-4d74-85d2-001ba786c219" compositionType="ELEMENT">
        <name type="IfcLabel" value="Test Building"/>
        <objectPlacement type="IfcLocalPlacement" globalId="d1e7e8b0-8238-405a-8b75-e26d2d2000b6">
          <relativePlacement type="IfcAxis2Placement3D">
            <location type="IfcCartesianPoint">
              <coordinates>
                <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
              </coordinates>
            </location>
          </relativePlacement>
        </objectPlacement>
        <containsElements>
          <IfcRelContainedInSpatialStructure type="IfcRelContainedInSpatialStructure" globalId="9dc7b8ee-7617-4022-8378-5b8ade78f5fc">
            <name type="IfcLabel" value="Physical model"/>
            <relatedElements>
              <IfcProduct type="IfcBuildingElementProxy" globalId="6e779871-965f-4c83-a22f-9969c19db132">
                <description type="IfcText" value="sample proxy"/>
                <name type="IfcLabel" value="P-1"/>
                <objectPlacement type="IfcLocalPlacement" globalId="4f66f683-7b09-42a3-a3c9-1cc0d1c5e1d4">
                  <relativePlacement type="IfcAxis2Placement3D">
                    <location type="IfcCartesianPoint">
                      <coordinates>
                        <IfcLengthMeasure type="IfcLengthMeasure" value="1000.0"/>
                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                      </coordinates>
                    </location>
                  </relativePlacement>
                  <placementRelTo>d1e7e8b0-8238-405a-8b75-e26d2d2000b6</placementRelTo>
                </objectPlacement>
                <representation type="IfcProductDefinitionShape">
                  <representations>
                    <IfcRepresentation type="IfcShapeRepresentation">
                      <contextOfItems type="IfcGeometricRepresentationSubContext" globalId="3c77a4ae-2448-476d-a39e-bdced6ad732d" targetView="MODEL_VIEW">
                        <contextIdentifier type="IfcLabel" value="Body"/>
                        <contextType type="IfcLabel" value="Model"/>
                      </contextOfItems>
                      <representationIdentifier type="IfcLabel" value="Body"/>
                      <representationType type="IfcLabel" value="Brep"/>
                      <items>
                        <IfcRepresentationItem type="IfcFacetedBrep">
                          <outer type="IfcClosedShell">
                            <cfsFaces>
                              <IfcFace type="IfcFace">
                                <bounds>
                                  <IfcFaceBound type="IfcFaceOuterBound">
                                    <bound type="IfcPolyLoop">
                                      <polygon>
                                        <IfcCartesianPoint type="IfcCartesianPoint">
                                          <coordinates>
                                            <IfcLengthMeasure type="IfcLengthMeasure" value="-500.0"/>
                                            <IfcLengthMeasure type="IfcLengthMeasure" value="-500.0"/>
                                            <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                          </coordinates>
                                        </IfcCartesianPoint>
                                        <IfcCartesianPoint type="IfcCartesianPoint">
                                          <coordinates>
                                            <IfcLengthMeasure type="IfcLengthMeasure" value="-500.0"/>
                                            <IfcLengthMeasure type="IfcLengthMeasure" value="500.0"/>
                                            <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                          </coordinates>
                                        </IfcCartesianPoint>
                                        <IfcCartesianPoint type="IfcCartesianPoint">
                                          <coordinates>
                                            <IfcLengthMeasure type="IfcLengthMeasure" value="500.0"/>
                                            <IfcLengthMeasure type="IfcLengthMeasure" value="500.0"/>
                                            <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                          </coordinates>
                                        </IfcCartesianPoint>
                                        <IfcCartesianPoint type="IfcCartesianPoint">
                                          <coordinates>
                                            <IfcLengthMeasure type="IfcLengthMeasure" value="500.0"/>
                                            <IfcLengthMeasure type="IfcLengthMeasure" value="-500.0"/>
                                            <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                          </coordinates>
                                        </IfcCartesianPoint>
                                      </polygon>
                                    </bound>
                                    <orientation type="IfcBoolean" value="false"/>
                                  </IfcFaceBound>
                                </bounds>
                              </IfcFace>
                              <IfcFace type="IfcFace">
                                <bounds>
                                  <IfcFaceBound type="IfcFaceOuterBound">
                                    <bound type="IfcPolyLoop">
                                      <polygon>
                                        <IfcCartesianPoint type="IfcCartesianPoint">
                                          <coordinates>
                                            <IfcLengthMeasure type="IfcLengthMeasure" value="-500.0"/>
                                            <IfcLengthMeasure type="IfcLengthMeasure" value="500.0"/>
                                            <IfcLengthMeasure type="IfcLengthMeasure" value="2000.0"/>
                                          </coordinates>
                                        </IfcCartesianPoint>
                                        <IfcCartesianPoint type="IfcCartesianPoint">
                                          <coordinates>
                                            <IfcLengthMeasure type="IfcLengthMeasure" value="-500.0"/>
                                            <IfcLengthMeasure type="IfcLengthMeasure" value="500.0"/>
                                            <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                          </coordinates>
                                        </IfcCartesianPoint>
                                        <IfcCartesianPoint type="IfcCartesianPoint">
                                          <coordinates>
                                            <IfcLengthMeasure type="IfcLengthMeasure" value="-500.0"/>
                                            <IfcLengthMeasure type="IfcLengthMeasure" value="-500.0"/>
                                            <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                          </coordinates>
                                        </IfcCartesianPoint>
                                        <IfcCartesianPoint type="IfcCartesianPoint">
                                          <coordinates>
                                            <IfcLengthMeasure type="IfcLengthMeasure" value="-500.0"/>
                                            <IfcLengthMeasure type="IfcLengthMeasure" value="-500.0"/>
                                            <IfcLengthMeasure type="IfcLengthMeasure" value="2000.0"/>
                                          </coordinates>
                                        </IfcCartesianPoint>
                                      </polygon>
                                    </bound>
                                    <orientation type="IfcBoolean" value="false"/>
                                  </IfcFaceBound>
                                </bounds>
                              </IfcFace>
                              <IfcFace type="IfcFace">
                                <bounds>
                                  <IfcFaceBound type="IfcFaceOuterBound">
                                    <bound type="IfcPolyLoop">
                                      <polygon>
                                        <IfcCartesianPoint type="IfcCartesianPoint">
                                          <coordinates>
                                            <IfcLengthMeasure type="IfcLengthMeasure" value="500.0"/>
                                            <IfcLengthMeasure type="IfcLengthMeasure" value="-500.0"/>
                                            <IfcLengthMeasure type="IfcLengthMeasure" value="2000.0"/>
                                          </coordinates>
                                        </IfcCartesianPoint>
                                        <IfcCartesianPoint type="IfcCartesianPoint">
                                          <coordinates>
                                            <IfcLengthMeasure type="IfcLengthMeasure" value="500.0"/>
                                            <IfcLengthMeasure type="IfcLengthMeasure" value="-500.0"/>
                                            <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                          </coordinates>
                                        </IfcCartesianPoint>
                                        <IfcCartesianPoint type="IfcCartesianPoint">
                                          <coordinates>
                                            <IfcLengthMeasure type="IfcLengthMeasure" value="500.0"/>
                                            <IfcLengthMeasure type="IfcLengthMeasure" value="500.0"/>
                                            <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                          </coordinates>
                                        </IfcCartesianPoint>
                                        <IfcCartesianPoint type="IfcCartesianPoint">
                                          <coordinates>
                                            <IfcLengthMeasure type="IfcLengthMeasure" value="500.0"/>
                                            <IfcLengthMeasure type="IfcLengthMeasure" value="500.0"/>
                                            <IfcLengthMeasure type="IfcLengthMeasure" value="2000.0"/>
                                          </coordinates>
                                        </IfcCartesianPoint>
                                      </polygon>
                                    </bound>
                                    <orientation type="IfcBoolean" value="false"/>
                                  </IfcFaceBound>
                                </bounds>
                              </IfcFace>
                              <IfcFace type="IfcFace">
                                <bounds>
                                  <IfcFaceBound type="IfcFaceOuterBound">
                                    <bound type="IfcPolyLoop">
                                      <polygon>
                                        <IfcCartesianPoint type="IfcCartesianPoint">
                                          <coordinates>
                                            <IfcLengthMeasure type="IfcLengthMeasure" value="500.0"/>
                                            <IfcLengthMeasure type="IfcLengthMeasure" value="-500.0"/>
                                            <IfcLengthMeasure type="IfcLengthMeasure" value="2000.0"/>
                                          </coordinates>
                                        </IfcCartesianPoint>
                                        <IfcCartesianPoint type="IfcCartesianPoint">
                                          <coordinates>
                                            <IfcLengthMeasure type="IfcLengthMeasure" value="500.0"/>
                                            <IfcLengthMeasure type="IfcLengthMeasure" value="500.0"/>
                                            <IfcLengthMeasure type="IfcLengthMeasure" value="2000.0"/>
                                          </coordinates>
                                        </IfcCartesianPoint>
                                        <IfcCartesianPoint type="IfcCartesianPoint">
                                          <coordinates>
                                            <IfcLengthMeasure type="IfcLengthMeasure" value="-500.0"/>
                                            <IfcLengthMeasure type="IfcLengthMeasure" value="500.0"/>
                                            <IfcLengthMeasure type="IfcLengthMeasure" value="2000.0"/>
                                          </coordinates>
                                        </IfcCartesianPoint>
                                        <IfcCartesianPoint type="IfcCartesianPoint">
                                          <coordinates>
                                            <IfcLengthMeasure type="IfcLengthMeasure" value="-500.0"/>
                                            <IfcLengthMeasure type="IfcLengthMeasure" value="-500.0"/>
                                            <IfcLengthMeasure type="IfcLengthMeasure" value="2000.0"/>
                                          </coordinates>
                                        </IfcCartesianPoint>
                                      </polygon>
                                    </bound>
                                    <orientation type="IfcBoolean" value="false"/>
                                  </IfcFaceBound>
                                </bounds>
                              </IfcFace>
                              <IfcFace type="IfcFace">
                                <bounds>
                                  <IfcFaceBound type="IfcFaceOuterBound">
                                    <bound type="IfcPolyLoop">
                                      <polygon>
                                        <IfcCartesianPoint type="IfcCartesianPoint">
                                          <coordinates>
                                            <IfcLengthMeasure type="IfcLengthMeasure" value="500.0"/>
                                            <IfcLengthMeasure type="IfcLengthMeasure" value="500.0"/>
                                            <IfcLengthMeasure type="IfcLengthMeasure" value="2000.0"/>
                                          </coordinates>
                                        </IfcCartesianPoint>
                                        <IfcCartesianPoint type="IfcCartesianPoint">
                                          <coordinates>
                                            <IfcLengthMeasure type="IfcLengthMeasure" value="500.0"/>
                                            <IfcLengthMeasure type="IfcLengthMeasure" value="500.0"/>
                                            <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                          </coordinates>
                                        </IfcCartesianPoint>
                                        <IfcCartesianPoint type="IfcCartesianPoint">
                                          <coordinates>
                                            <IfcLengthMeasure type="IfcLengthMeasure" value="-500.0"/>
                                            <IfcLengthMeasure type="IfcLengthMeasure" value="500.0"/>
                                            <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                          </coordinates>
                                        </IfcCartesianPoint>
                                        <IfcCartesianPoint type="IfcCartesianPoint">
                                          <coordinates>
                                            <IfcLengthMeasure type="IfcLengthMeasure" value="-500.0"/>
                                            <IfcLengthMeasure type="IfcLengthMeasure" value="500.0"/>
                                            <IfcLengthMeasure type="IfcLengthMeasure" value="2000.0"/>
                                          </coordinates>
                                        </IfcCartesianPoint>
                                      </polygon>
                                    </bound>
                                    <orientation type="IfcBoolean" value="false"/>
                                  </IfcFaceBound>
                                </bounds>
                              </IfcFace>
                              <IfcFace type="IfcFace">
                                <bounds>
                                  <IfcFaceBound type="IfcFaceOuterBound">
                                    <bound type="IfcPolyLoop">
                                      <polygon>
                                        <IfcCartesianPoint type="IfcCartesianPoint">
                                          <coordinates>
                                            <IfcLengthMeasure type="IfcLengthMeasure" value="-500.0"/>
                                            <IfcLengthMeasure type="IfcLengthMeasure" value="-500.0"/>
                                            <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                          </coordinates>
                                        </IfcCartesianPoint>
                                        <IfcCartesianPoint type="IfcCartesianPoint">
                                          <coordinates>
                                            <IfcLengthMeasure type="IfcLengthMeasure" value="500.0"/>
                                            <IfcLengthMeasure type="IfcLengthMeasure" value="-500.0"/>
                                            <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                          </coordinates>
                                        </IfcCartesianPoint>
                                        <IfcCartesianPoint type="IfcCartesianPoint">
                                          <coordinates>
                                            <IfcLengthMeasure type="IfcLengthMeasure" value="500.0"/>
                                            <IfcLengthMeasure type="IfcLengthMeasure" value="-500.0"/>
                                            <IfcLengthMeasure type="IfcLengthMeasure" value="2000.0"/>
                                          </coordinates>
                                        </IfcCartesianPoint>
                                        <IfcCartesianPoint type="IfcCartesianPoint">
                                          <coordinates>
                                            <IfcLengthMeasure type="IfcLengthMeasure" value="-500.0"/>
                                            <IfcLengthMeasure type="IfcLengthMeasure" value="-500.0"/>
                                            <IfcLengthMeasure type="IfcLengthMeasure" value="2000.0"/>
                                          </coordinates>
                                        </IfcCartesianPoint>
                                      </polygon>
                                    </bound>
                                    <orientation type="IfcBoolean" value="false"/>
                                  </IfcFaceBound>
                                </bounds>
                              </IfcFace>
                            </cfsFaces>
                          </outer>
                        </IfcRepresentationItem>
                      </items>
                    </IfcRepresentation>
                  </representations>
                </representation>
              </IfcProduct>
            </relatedElements>
          </IfcRelContainedInSpatialStructure>
        </containsElements>
      </IfcObjectDefinition>
    </relatedObjects>
  </isDecomposedBy>
  <representationContexts>
    <IfcRepresentationContext type="IfcGeometricRepresentationContext" globalId="fa08afe0-194b-4bba-b88c-4659fd3e804c">
      <worldCoordinateSystem type="IfcAxis2Placement3D">
        <location type="IfcCartesianPoint">
          <coordinates>
            <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
            <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
            <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
          </coordinates>
        </location>
      </worldCoordinateSystem>
      <contextType type="IfcLabel" value="Model"/>
      <coordinateSpaceDimension type="IfcDimensionCount" value="3"/>
      <precision type="IfcReal" value="1.0E-5"/>
      <hasSubContexts>
        <IfcGeometricRepresentationSubContext>3c77a4ae-2448-476d-a39e-bdced6ad732d</IfcGeometricRepresentationSubContext>
      </hasSubContexts>
    </IfcRepresentationContext>
  </representationContexts>
  <unitsInContext>
    <units>
      <IfcUnit type="IfcSIUnit" globalId="8dcf0bbf-281e-4887-bedc-0f96f050e072">
        <dimensions lengthExponent="1" massExponent="0" timeExponent="0" electricCurrentExponent="0" thermodynamicTemperatureExponent="0" amountOfSubstanceExponent="0" luminousIntensityExponent="0"/>
        <unitType>LENGTHUNIT</unitType>
        <prefix>MILLI</prefix>
        <name>METRE</name>
      </IfcUnit>
      <IfcUnit type="IfcConversionBasedUnit" globalId="8b8067da-836a-471c-9a5b-3c48dc16c901">
        <dimensions lengthExponent="0" massExponent="0" timeExponent="0" electricCurrentExponent="0" thermodynamicTemperatureExponent="0" amountOfSubstanceExponent="0" luminousIntensityExponent="0"/>
        <unitType>PLANEANGLEUNIT</unitType>
        <name>degree</name>
        <conversionFactor type="IfcMeasureWithUnit">
          <valueComponent type="IfcPlaneAngleMeasure" value="0.017453293"/>
          <unitComponent type="IfcSIUnit" globalId="fec3507a-cd5f-435b-acae-04b362814b68">
            <dimensions lengthExponent="0" massExponent="0" timeExponent="0" electricCurrentExponent="0" thermodynamicTemperatureExponent="0" amountOfSubstanceExponent="0" luminousIntensityExponent="0"/>
            <unitType>PLANEANGLEUNIT</unitType>
            <name>RADIAN</name>
          </unitComponent>
        </conversionFactor>
      </IfcUnit>
    </units>
  </unitsInContext>
</IfcProject>

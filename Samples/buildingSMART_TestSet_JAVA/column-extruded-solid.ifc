ISO-10303-21;
HEADER;
FILE_DESCRIPTION(('ViewDefinition [notYetAssigned]'),'2;1');
FILE_NAME(
/* name */ '',
/* time_stamp */ '2014-12-09T00:27:55',
/* author */ ('Jon'),
/* organization */ ('Unknown'),
/* preprocessor_version */ 'ggIFC - Exporter by Geometry Gym Pty Ltd',
/* originating_system */ 'ggIFC - Exporter by Geometry Gym Pty Ltd',
/* authorization */ 'None');

FILE_SCHEMA (('IFC4'));
ENDSEC;

DATA;
#1= IFCAPPLICATION(#2,'*******','ggRhinoIFC - Geometry Gym Plug-in for Rhino3d','ggRhinoIFC');
#2= IFCORGANIZATION($,'Geometry Gym Pty Ltd',$,$,$);
#3= IFCPERSONANDORGANIZATION(#4,#5,$);
#4= IFCPERSON('Jon','Jon',$,$,$,$,$,$);
#5= IFCORGANIZATION($,'Geometry Gym Pty Ltd',$,$,$);
#6= IFCOWNERHISTORY(#3,#1,$,.ADDED.,1418084875,$,$,1418084875);
#7= IFCGEOMETRICREPRESENTATIONCONTEXT($,'Model',3,0.0001,#8,#10);
#8= IFCAXIS2PLACEMENT3D(#9,$,$);
#9= IFCCARTESIANPOINT((0.0,0.0,0.0));
#10= IFCDIRECTION((0.0,1.0));
#11= IFCGEOMETRICREPRESENTATIONSUBCONTEXT('Axis','Model',*,*,*,*,#7,$,.MODEL_VIEW.,$);
#12= IFCGEOMETRICREPRESENTATIONSUBCONTEXT('Body','Model',*,*,*,*,#7,$,.MODEL_VIEW.,$);
#13= IFCGEOMETRICREPRESENTATIONCONTEXT($,'Model',3,0.0001,#8,#14);
#14= IFCDIRECTION((0.0,1.0));
#50= IFCBUILDING('0y81Ev6rvAUxlnuftcyvNq',$,'IfcBuilding',$,$,#51,$,$,.ELEMENT.,$,$,#57);
#51= IFCLOCALPLACEMENT($,#52);
#52= IFCAXIS2PLACEMENT3D(#53,$,$);
#53= IFCCARTESIANPOINT((0.0,0.0,0.0));
#54= IFCRELCONTAINEDINSPATIALSTRUCTURE('16C2nttC9EMe6T5CFDJvvD',$,'Building','Building Container for Elements',(#210),#50);
#57= IFCPOSTALADDRESS($,$,$,$,$,$,$,'Unknown',$,$);
#100= IFCPROJECT('3mKjRoX3jBMhIv_Zx1c4Bd',#6,'IfcProject',$,$,'IfcProject','',(#13),#101);
#101= IFCUNITASSIGNMENT((#102,#103,#104));
#102= IFCSIUNIT(*,.LENGTHUNIT.,.MILLI.,.METRE.);
#103= IFCSIUNIT(*,.PLANEANGLEUNIT.,$,.RADIAN.);
#104= IFCSIUNIT(*,.TIMEUNIT.,$,.SECOND.);
#105= IFCRELAGGREGATES('1eUNQhZdv10f0JKAi0YxI7',$,'Project Container','Project Container for Buildings',#100,(#50));
#200= IFCMATERIAL('S355JR',$,'Steel');
#203= IFCISHAPEPROFILEDEF(.AREA.,'IPE200',$,100.0,200.0,5.6,8.5,12.0,$,$);
#204= IFCMATERIALPROFILE('IPE200',$,#200,#203,0,$);
#206= IFCMATERIALPROFILESET('IPE200',$,(#204),$);
#207= IFCRELASSOCIATESMATERIAL('1JF_AI9zr32PG0e2sisON_',$,'MatAssoc','Material Associates',(#208),#206);
#208= IFCCOLUMNTYPE('0tgmoOfo1CO8hHRLJKMijW',$,'IPE200',$,$,$,$,$,$,.COLUMN.);
#209= IFCRELDEFINESBYTYPE('0hOB2rtFnDYPhXS61OgZx1',$,'IPE200',$,(#210),#208);
#210= IFCCOLUMNSTANDARDCASE('2RBigpQc1BvfKj13SBZAbb',$,$,$,$,#211,#225,$,$);
#211= IFCLOCALPLACEMENT($,#212);
#212= IFCAXIS2PLACEMENT3D(#213,#214,#215);
#213= IFCCARTESIANPOINT((0.0,0.0,0.0));
#214= IFCDIRECTION((0.0,0.0,1.0));
#215= IFCDIRECTION((0.0,-1.0,0.0));
#216= IFCMATERIALPROFILESETUSAGE(#206,5,$);
#217= IFCRELASSOCIATESMATERIAL('3sCvSHUL5DMBDiJ_zxR0YZ',$,'MatAssoc','Material Associates',(#210),#216);
#218= IFCCARTESIANPOINT((0.0,0.0,0.0));
#219= IFCCARTESIANPOINT((0.0,0.0,2000.0));
#220= IFCPOLYLINE((#218,#219));
#221= IFCSHAPEREPRESENTATION(#11,'Axis','Curve3D',(#220));
#222= IFCDIRECTION((0.0,0.0,1.0));
#223= IFCEXTRUDEDAREASOLID(#203,$,#222,2000.0);
#224= IFCSHAPEREPRESENTATION(#12,'Body','SweptSolid',(#223));
#225= IFCPRODUCTDEFINITIONSHAPE($,$,(#221,#224));
ENDSEC;

END-ISO-10303-21;


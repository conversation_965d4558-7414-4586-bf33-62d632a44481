{"type": "IfcProject", "globalId": "0cecd97b-f4a0-4b12-8568-63a2684ad6bb", "name": {"type": "IfcLabel", "value": "Project"}, "isDecomposedBy": {"type": "IfcRelAggregates", "globalId": "bcfa4959-0040-4020-be52-afc0a0a76470", "relatedObjects": [{"type": "IfcSite", "globalId": "16024b97-b89a-43ad-8449-e0293ac37d68", "name": {"type": "IfcLabel", "value": "Site #1"}, "objectPlacement": {"type": "IfcLocalPlacement", "globalId": "19dff021-8990-49b7-92dd-74e65bccbe60", "relativePlacement": {"type": "IfcAxis2Placement3D", "location": {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": 0.0}, {"type": "IfcLengthMeasure", "value": 0.0}, {"type": "IfcLengthMeasure", "value": 0.0}]}}}, "containsElements": [{"type": "IfcRelContainedInSpatialStructure", "globalId": "0f0727b2-4f6d-458f-aed4-2bbb0d05fa33", "relatedElements": [{"type": "IfcColumn", "globalId": "a079088b-cebd-4fe4-8915-48f4ccc2c4ae", "name": {"type": "IfcLabel", "value": "Column #1"}, "objectPlacement": {"type": "IfcLocalPlacement", "globalId": "b99de2ca-b9b7-456b-92ac-f21e11372b56", "placementRelTo": "19dff021-8990-49b7-92dd-74e65bccbe60", "relativePlacement": {"type": "IfcAxis2Placement3D", "location": {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": 432.0}, {"type": "IfcLengthMeasure", "value": 288.0}, {"type": "IfcLengthMeasure", "value": 48.0}]}, "axis": {"type": "IfcDirection", "directionRatios": [{"type": "IfcReal", "value": 0.0}, {"type": "IfcReal", "value": 0.0}, {"type": "IfcReal", "value": 1.0}]}, "refDirection": {"type": "IfcDirection", "directionRatios": [{"type": "IfcReal", "value": 1.0}, {"type": "IfcReal", "value": 0.0}, {"type": "IfcReal", "value": 0.0}]}}}, "representation": {"type": "IfcProductDefinitionShape", "representations": [{"type": "IfcShapeRepresentation", "contextOfItems": {"type": "IfcGeometricRepresentationSubContext", "globalId": "a87c8440-1add-47b2-86fc-9d8892d5de11", "contextIdentifier": {"type": "IfcLabel", "value": "Body"}, "contextType": {"type": "IfcLabel", "value": "Model"}, "targetView": "MODEL_VIEW"}, "representationIdentifier": {"type": "IfcLabel", "value": "Body"}, "representationType": {"type": "IfcLabel", "value": "Tessellation"}, "items": [{"type": "IfcTriangulatedFaceSet", "coordinates": {"type": "IfcCartesianPointList3D", "globalId": "278ef20f-ba23-47e9-9a14-846d64e11489", "coordList": [{"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": -4.0}, {"type": "IfcLengthMeasure", "value": 4.0}, {"type": "IfcLengthMeasure", "value": 0.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": -4.0}, {"type": "IfcLengthMeasure", "value": -4.0}, {"type": "IfcLengthMeasure", "value": 0.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": 4.0}, {"type": "IfcLengthMeasure", "value": -4.0}, {"type": "IfcLengthMeasure", "value": 0.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": 4.0}, {"type": "IfcLengthMeasure", "value": 4.0}, {"type": "IfcLengthMeasure", "value": 0.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": -4.0}, {"type": "IfcLengthMeasure", "value": 4.0}, {"type": "IfcLengthMeasure", "value": 120.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": -4.0}, {"type": "IfcLengthMeasure", "value": -4.0}, {"type": "IfcLengthMeasure", "value": 120.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": 4.0}, {"type": "IfcLengthMeasure", "value": -4.0}, {"type": "IfcLengthMeasure", "value": 120.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": 4.0}, {"type": "IfcLengthMeasure", "value": 4.0}, {"type": "IfcLengthMeasure", "value": 120.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": -4.0}, {"type": "IfcLengthMeasure", "value": 4.0}, {"type": "IfcLengthMeasure", "value": 0.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": -4.0}, {"type": "IfcLengthMeasure", "value": -4.0}, {"type": "IfcLengthMeasure", "value": 0.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": -4.0}, {"type": "IfcLengthMeasure", "value": -4.0}, {"type": "IfcLengthMeasure", "value": 120.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": -4.0}, {"type": "IfcLengthMeasure", "value": 4.0}, {"type": "IfcLengthMeasure", "value": 120.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": -4.0}, {"type": "IfcLengthMeasure", "value": -4.0}, {"type": "IfcLengthMeasure", "value": 0.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": 4.0}, {"type": "IfcLengthMeasure", "value": -4.0}, {"type": "IfcLengthMeasure", "value": 0.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": 4.0}, {"type": "IfcLengthMeasure", "value": -4.0}, {"type": "IfcLengthMeasure", "value": 120.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": -4.0}, {"type": "IfcLengthMeasure", "value": -4.0}, {"type": "IfcLengthMeasure", "value": 120.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": 4.0}, {"type": "IfcLengthMeasure", "value": -4.0}, {"type": "IfcLengthMeasure", "value": 0.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": 4.0}, {"type": "IfcLengthMeasure", "value": 4.0}, {"type": "IfcLengthMeasure", "value": 0.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": 4.0}, {"type": "IfcLengthMeasure", "value": 4.0}, {"type": "IfcLengthMeasure", "value": 120.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": 4.0}, {"type": "IfcLengthMeasure", "value": -4.0}, {"type": "IfcLengthMeasure", "value": 120.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": 4.0}, {"type": "IfcLengthMeasure", "value": 4.0}, {"type": "IfcLengthMeasure", "value": 0.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": -4.0}, {"type": "IfcLengthMeasure", "value": 4.0}, {"type": "IfcLengthMeasure", "value": 0.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": -4.0}, {"type": "IfcLengthMeasure", "value": 4.0}, {"type": "IfcLengthMeasure", "value": 120.0}]}, {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": 4.0}, {"type": "IfcLengthMeasure", "value": 4.0}, {"type": "IfcLengthMeasure", "value": 120.0}]}]}, "normals": [{"parameterValues": [{"type": "IfcParameterValue", "value": 0.0}, {"type": "IfcParameterValue", "value": 0.0}, {"type": "IfcParameterValue", "value": -1.0}]}, {"parameterValues": [{"type": "IfcParameterValue", "value": 0.0}, {"type": "IfcParameterValue", "value": 0.0}, {"type": "IfcParameterValue", "value": -1.0}]}, {"parameterValues": [{"type": "IfcParameterValue", "value": 0.0}, {"type": "IfcParameterValue", "value": 0.0}, {"type": "IfcParameterValue", "value": -1.0}]}, {"parameterValues": [{"type": "IfcParameterValue", "value": 0.0}, {"type": "IfcParameterValue", "value": 0.0}, {"type": "IfcParameterValue", "value": -1.0}]}, {"parameterValues": [{"type": "IfcParameterValue", "value": 0.0}, {"type": "IfcParameterValue", "value": 0.0}, {"type": "IfcParameterValue", "value": 1.0}]}, {"parameterValues": [{"type": "IfcParameterValue", "value": 0.0}, {"type": "IfcParameterValue", "value": 0.0}, {"type": "IfcParameterValue", "value": 1.0}]}, {"parameterValues": [{"type": "IfcParameterValue", "value": 0.0}, {"type": "IfcParameterValue", "value": 0.0}, {"type": "IfcParameterValue", "value": 1.0}]}, {"parameterValues": [{"type": "IfcParameterValue", "value": 0.0}, {"type": "IfcParameterValue", "value": 0.0}, {"type": "IfcParameterValue", "value": 1.0}]}, {"parameterValues": [{"type": "IfcParameterValue", "value": -1.0}, {"type": "IfcParameterValue", "value": 0.0}, {"type": "IfcParameterValue", "value": 0.0}]}, {"parameterValues": [{"type": "IfcParameterValue", "value": -1.0}, {"type": "IfcParameterValue", "value": 0.0}, {"type": "IfcParameterValue", "value": 0.0}]}, {"parameterValues": [{"type": "IfcParameterValue", "value": -1.0}, {"type": "IfcParameterValue", "value": 0.0}, {"type": "IfcParameterValue", "value": 0.0}]}, {"parameterValues": [{"type": "IfcParameterValue", "value": -1.0}, {"type": "IfcParameterValue", "value": 0.0}, {"type": "IfcParameterValue", "value": 0.0}]}, {"parameterValues": [{"type": "IfcParameterValue", "value": 0.0}, {"type": "IfcParameterValue", "value": -1.0}, {"type": "IfcParameterValue", "value": 0.0}]}, {"parameterValues": [{"type": "IfcParameterValue", "value": 0.0}, {"type": "IfcParameterValue", "value": -1.0}, {"type": "IfcParameterValue", "value": 0.0}]}, {"parameterValues": [{"type": "IfcParameterValue", "value": 0.0}, {"type": "IfcParameterValue", "value": -1.0}, {"type": "IfcParameterValue", "value": 0.0}]}, {"parameterValues": [{"type": "IfcParameterValue", "value": 0.0}, {"type": "IfcParameterValue", "value": -1.0}, {"type": "IfcParameterValue", "value": 0.0}]}, {"parameterValues": [{"type": "IfcParameterValue", "value": 1.0}, {"type": "IfcParameterValue", "value": 0.0}, {"type": "IfcParameterValue", "value": 0.0}]}, {"parameterValues": [{"type": "IfcParameterValue", "value": 1.0}, {"type": "IfcParameterValue", "value": 0.0}, {"type": "IfcParameterValue", "value": 0.0}]}, {"parameterValues": [{"type": "IfcParameterValue", "value": 1.0}, {"type": "IfcParameterValue", "value": 0.0}, {"type": "IfcParameterValue", "value": 0.0}]}, {"parameterValues": [{"type": "IfcParameterValue", "value": 1.0}, {"type": "IfcParameterValue", "value": 0.0}, {"type": "IfcParameterValue", "value": 0.0}]}, {"parameterValues": [{"type": "IfcParameterValue", "value": 0.0}, {"type": "IfcParameterValue", "value": 1.0}, {"type": "IfcParameterValue", "value": 0.0}]}, {"parameterValues": [{"type": "IfcParameterValue", "value": 0.0}, {"type": "IfcParameterValue", "value": 1.0}, {"type": "IfcParameterValue", "value": 0.0}]}, {"parameterValues": [{"type": "IfcParameterValue", "value": 0.0}, {"type": "IfcParameterValue", "value": 1.0}, {"type": "IfcParameterValue", "value": 0.0}]}, {"parameterValues": [{"type": "IfcParameterValue", "value": 0.0}, {"type": "IfcParameterValue", "value": 1.0}, {"type": "IfcParameterValue", "value": 0.0}]}], "closed": {"type": "IfcBoolean", "value": false}, "coordIndex": [{"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 1}, {"type": "IfcPositiveInteger", "value": 3}, {"type": "IfcPositiveInteger", "value": 2}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 1}, {"type": "IfcPositiveInteger", "value": 4}, {"type": "IfcPositiveInteger", "value": 3}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 5}, {"type": "IfcPositiveInteger", "value": 6}, {"type": "IfcPositiveInteger", "value": 7}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 5}, {"type": "IfcPositiveInteger", "value": 7}, {"type": "IfcPositiveInteger", "value": 8}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 9}, {"type": "IfcPositiveInteger", "value": 10}, {"type": "IfcPositiveInteger", "value": 11}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 9}, {"type": "IfcPositiveInteger", "value": 11}, {"type": "IfcPositiveInteger", "value": 12}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 13}, {"type": "IfcPositiveInteger", "value": 14}, {"type": "IfcPositiveInteger", "value": 15}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 13}, {"type": "IfcPositiveInteger", "value": 15}, {"type": "IfcPositiveInteger", "value": 16}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 17}, {"type": "IfcPositiveInteger", "value": 18}, {"type": "IfcPositiveInteger", "value": 19}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 17}, {"type": "IfcPositiveInteger", "value": 19}, {"type": "IfcPositiveInteger", "value": 20}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 21}, {"type": "IfcPositiveInteger", "value": 22}, {"type": "IfcPositiveInteger", "value": 23}]}, {"positiveIntegers": [{"type": "IfcPositiveInteger", "value": 21}, {"type": "IfcPositiveInteger", "value": 23}, {"type": "IfcPositiveInteger", "value": 24}]}]}]}]}, "predefinedType": "COLUMN"}]}], "compositionType": "ELEMENT"}]}, "representationContexts": [{"type": "IfcGeometricRepresentationContext", "globalId": "8b8fca3b-2812-49c3-a4df-61f6f930d2ba", "contextIdentifier": {"type": "IfcLabel", "value": "3D"}, "contextType": {"type": "IfcLabel", "value": "Model"}, "coordinateSpaceDimension": {"type": "IfcDimensionCount", "value": 3}, "precision": {"type": "IfcReal", "value": 1e-05}, "worldCoordinateSystem": {"type": "IfcAxis2Placement3D", "location": {"type": "IfcCartesianPoint", "coordinates": [{"type": "IfcLengthMeasure", "value": 0.0}, {"type": "IfcLengthMeasure", "value": 0.0}, {"type": "IfcLengthMeasure", "value": 0.0}]}}, "hasSubContexts": ["a87c8440-1add-47b2-86fc-9d8892d5de11"]}], "unitsInContext": {"units": [{"type": "IfcConversionBasedUnit", "globalId": "80ed5c19-fec9-4763-b7bf-0ce50c5aa9d6", "dimensions": {"lengthExponent": 1, "massExponent": 0, "timeExponent": 0, "electricCurrentExponent": 0, "thermodynamicTemperatureExponent": 0, "amountOfSubstanceExponent": 0, "luminousIntensityExponent": 0}, "unitType": "LENGTHUNIT", "name": "inch", "conversionFactor": {"type": "IfcMeasureWithUnit", "valueComponent": {"type": "IfcLengthMeasure", "value": 0.0254}, "unitComponent": {"type": "IfcSIUnit", "globalId": "cefb7cf2-8d77-4a67-a16c-11ab2920f647", "dimensions": {"lengthExponent": 1, "massExponent": 0, "timeExponent": 0, "electricCurrentExponent": 0, "thermodynamicTemperatureExponent": 0, "amountOfSubstanceExponent": 0, "luminousIntensityExponent": 0}, "unitType": "LENGTHUNIT", "name": "METRE"}}}]}}
ISO-10303-21;
HEADER;
/* NOTE a valid model view name has to be asserted, replacing 'notYetAssigned' ----------------- */
FILE_DESCRIPTION(
    ( 'ViewDefinition [notYetAssigned]'
     ,'Comment [manual creation of example file]'
	)
	,'2;1');
/* NOTE standard header information according to ISO 10303-21 ---------------------------------- */
FILE_NAME(
	'IfcTask.ifc',
	'2010-09-23T23:27:15',
	('<PERSON>'),
	('buildingSMART International'),
	'IFC text editor',
	'IFC text editor',
	'reference file created for the IFC4 specification');
/* NOTE schema name to be replaced with 'IFC4' after the final release ------------------------- */
FILE_SCHEMA(('IFC4'));
ENDSEC;

DATA;
/* Project context ----------------------------------------------------------------------------- */
/* required for every IFC data file ------------------------------------------------------------ */
#1= IFCAPPLICATION(#2,'0.7','Constructivity','CONSTRUCTIVITY');
#2= IFCORGANIZATION($,'Constructivity.com LLC',$,$,$);
#3= IFCPERSON('','Chipman','Tim',$,$,$,$,$);
#4= IFCORGANIZATION($,'buildingSMART',$,$,$);
#5= IFCPERSONANDORGANIZATION(#3,#4,$);
/* Project units ------------------------------------------------------------------------------- */
/* this project is based on imperial units (inch) ---------------------------------------------- */
#26= IFCSIUNIT(*,.LENGTHUNIT.,$,.METRE.);
#27= IFCMEASUREWITHUNIT(IFCLENGTHMEASURE(0.0254),#26);
#28= IFCCONVERSIONBASEDUNIT(#29,.LENGTHUNIT.,'inch',#27);
#29= IFCDIMENSIONALEXPONENTS(1,0,0,0,0,0,0);
#199= IFCUNITASSIGNMENT((#28));
/* Project ------------------------------------------------------------------------------------- */
/* declaring units, representation context and owner history for this data set ----------------- */
#200= IFCPROJECT('2ww9A9wXX54eT3Wns4rLsr',#201,'example project',$,$,$,$,(#204,#207),#199);
#201= IFCOWNERHISTORY(#5,#1,.READWRITE.,.ADDED.,1285284288,$,$,1285284288);
#202= IFCCARTESIANPOINT((0.,0.,0.));
#203= IFCAXIS2PLACEMENT3D(#202,$,$);
#204= IFCGEOMETRICREPRESENTATIONCONTEXT('3D','Model',3,1.0E-05,#203,$);
#205= IFCCARTESIANPOINT((0.,0.,0.));
#206= IFCAXIS2PLACEMENT3D(#205,$,$);
#207= IFCGEOMETRICREPRESENTATIONCONTEXT('2D','Plan',2,1.0E-05,#206,$);

/* Spatial structure of site, building, and single storey -------------------------------------- */
/* the site has a footprint and body geometric representation ---------------------------------- */
#208= IFCSITE('2uTUkDuu1Caw4X2OaltWYD',#201,'Site #1',$,$,#229,#210,$,.ELEMENT.,$,$,$,$,$);
#210= IFCPRODUCTDEFINITIONSHAPE($,$,(#212,#214));
#212= IFCSHAPEREPRESENTATION(#204,'FootPrint','GeometricCurveSet',(#221));
#214= IFCSHAPEREPRESENTATION(#204,'Body','SweptSolid',(#260));
#216= IFCCARTESIANPOINT((0.,0.));
#217= IFCCARTESIANPOINT((1536.,0.));
#218= IFCCARTESIANPOINT((1536.,768.));
#219= IFCCARTESIANPOINT((0.,768.));
#220= IFCPOLYLINE((#216,#217,#218,#219,#216));
#221= IFCGEOMETRICCURVESET((#220));
#223= IFCARBITRARYCLOSEDPROFILEDEF(.AREA.,$,#220);
#224= IFCEXTRUDEDAREASOLID(#223,#226,#227,48.);
#225= IFCCARTESIANPOINT((0.,0.,0.));
#226= IFCAXIS2PLACEMENT3D(#225,$,$);
#227= IFCDIRECTION((0.,0.,1.));
#229= IFCLOCALPLACEMENT($,#231);
#230= IFCCARTESIANPOINT((0.,0.,0.));
#231= IFCAXIS2PLACEMENT3D(#230,$,$);
#233= IFCRELAGGREGATES('3lhFWDvkb6Cwv__4__szPn',#201,$,$,#200,(#208));
#235= IFCDIRECTION((1.,0.,0.));

/* the building has a footprint geometric representation --------------------------------------- */
/* --------------------------------------------------------------------------------------------- */
#236= IFCBUILDING('39A9p5Pi1EIRKAduawfVgP',#201,'Building #1',$,$,#252,#245,$,.ELEMENT.,$,$,$);
#238= IFCCARTESIANPOINT((0.,0.));
#239= IFCCARTESIANPOINT((288.,0.));
#240= IFCCARTESIANPOINT((288.,192.));
#241= IFCCARTESIANPOINT((0.,192.));
#242= IFCPOLYLINE((#238,#239,#240,#241,#238));
#243= IFCGEOMETRICCURVESET((#242));
#245= IFCPRODUCTDEFINITIONSHAPE($,$,(#247));
#247= IFCSHAPEREPRESENTATION(#204,'FootPrint','GeometricCurveSet',(#243));
#250= IFCRELAGGREGATES('2Ugw7SsXLDQBYCez8hipzK',#201,$,$,#208,(#236));
#252= IFCLOCALPLACEMENT(#229,#257);
#253= IFCCARTESIANPOINT((0.,0.,0.));
#254= IFCAXIS2PLACEMENT3D(#253,$,$);
#256= IFCCARTESIANPOINT((576.,240.,48.));
#257= IFCAXIS2PLACEMENT3D(#256,$,$);
#259= IFCARBITRARYCLOSEDPROFILEDEF(.AREA.,$,#220);
#260= IFCEXTRUDEDAREASOLID(#259,#262,#263,48.);
#261= IFCCARTESIANPOINT((0.,0.,0.));
#262= IFCAXIS2PLACEMENT3D(#261,$,$);
#263= IFCDIRECTION((0.,0.,1.));

/* the building storey has a footprint geometric representation -------------------------------- */
/* --------------------------------------------------------------------------------------------- */
#265= IFCBUILDINGSTOREY('3nYYuCp$XC4vhvJ2cMbpd2',#201,'Ground Level',$,$,#270,#281,$,.ELEMENT.,0.);
#266= IFCRELAGGREGATES('2RHRlut4v6yPP4JCrvm4IE',#201,$,$,#236,(#265));
#270= IFCLOCALPLACEMENT(#252,#272);
#271= IFCCARTESIANPOINT((0.,0.,0.));
#272= IFCAXIS2PLACEMENT3D(#271,$,$);
#274= IFCCARTESIANPOINT((0.,0.));
#275= IFCCARTESIANPOINT((288.,0.));
#276= IFCCARTESIANPOINT((288.,192.));
#277= IFCCARTESIANPOINT((0.,192.));
#278= IFCCARTESIANPOINT((0.,0.));
#279= IFCPOLYLINE((#274,#275,#276,#277,#278));
#280= IFCGEOMETRICCURVESET((#279));
#281= IFCPRODUCTDEFINITIONSHAPE($,$,(#283));
#283= IFCSHAPEREPRESENTATION(#204,'FootPrint','GeometricCurveSet',(#280));

/* Slab having rectangular shape and concrete material layer ----------------------------------- */
/* --------------------------------------------------------------------------------------------- */
#286= IFCCARTESIANPOINT((0.,0.));
#287= IFCCARTESIANPOINT((288.,0.));
#288= IFCCARTESIANPOINT((288.,192.));
#289= IFCCARTESIANPOINT((0.,192.));
#290= IFCCARTESIANPOINT((0.,0.));
#291= IFCPOLYLINE((#286,#287,#288,#289,#290));
#292= IFCSLABSTANDARDCASE('20WrgxqpbDtferON_k1P2X',#201,'Slab #1',$,$,#352,#302,$,.BASESLAB.);
#300= IFCGEOMETRICCURVESET((#291));
#302= IFCPRODUCTDEFINITIONSHAPE($,$,(#304,#346));
#304= IFCSHAPEREPRESENTATION(#204,'FootPrint','GeometricCurveSet',(#300));
#310= IFCMATERIALLAYER(#315,6.,$,'Core','Material from which the slab is constructed.',$,$);
#311= IFCMATERIALLAYERSET((#310),$,$);
#313= IFCMATERIALLAYERSETUSAGE(#311,.AXIS3.,.POSITIVE.,0.,$);
#314= IFCRELASSOCIATESMATERIAL('2kASSjs2f6Phd71DH$IPe5',#201,'IfcSlabStandardCase',$,(#292),#313);
#315= IFCMATERIAL('Concrete (Nominal)',$,'Concrete');
#341= IFCARBITRARYCLOSEDPROFILEDEF(.AREA.,$,#279);
#342= IFCEXTRUDEDAREASOLID(#341,#344,#345,6.);
#343= IFCCARTESIANPOINT((0.,0.,0.));
#344= IFCAXIS2PLACEMENT3D(#343,$,$);
#345= IFCDIRECTION((0.,0.,1.));
#346= IFCSHAPEREPRESENTATION(#204,'Body','SweptSolid',(#534));
#532= IFCDIRECTION((0.,0.,1.));
#533= IFCARBITRARYCLOSEDPROFILEDEF(.AREA.,$,#291);
#534= IFCEXTRUDEDAREASOLID(#533,#536,#532,6.);
#535= IFCCARTESIANPOINT((0.,0.,0.));
#536= IFCAXIS2PLACEMENT3D(#535,$,$);

#349= IFCRELCONTAINEDINSPATIALSTRUCTURE('19iKmngSP0bR$GMfGwTIoD',#201,$,$,(#292,#356,#385,#407,#429),#265);
#350= IFCCARTESIANPOINT((0.,0.,0.));
#351= IFCAXIS2PLACEMENT3D(#350,$,$);
#352= IFCLOCALPLACEMENT(#270,#351);
#355= IFCDIRECTION((0.,1.,0.));

/* Walls for exterior, positioned along edges of rectangular slab ------------------------------ */
/* --------------------------------------------------------------------------------------------- */

/* Standard case walls #1 with axis and body geometry and material layer set defined ----------- */
/* --------------------------------------------------------------------------------------------- */
#356= IFCWALLSTANDARDCASE('26tmERtwL8G8UQn5BoglEh',#201,'Wall #1',$,$,#362,#379,$,.NOTDEFINED.);
#358= IFCRELCONNECTSELEMENTS('2ZHJEGFvL98QhEDnQsGNrK',#201,$,$,$,#292,#356);
#359= IFCCARTESIANPOINT((0.,0.,6.));
#360= IFCDIRECTION((0.,0.,1.));
#361= IFCAXIS2PLACEMENT3D(#359,#360,#355);
#362= IFCLOCALPLACEMENT(#270,#361);
#365= IFCMATERIALLAYERWITHOFFSETS(#370,6.,$,'Block','Structural core of the wall, such as concrete masonry units.',$,$,.AXIS1.,(0.,0.));
#366= IFCMATERIALLAYERSET((#365),$,$);
#368= IFCMATERIALLAYERSETUSAGE(#366,.AXIS2.,.POSITIVE.,-3.,$);
#369= IFCRELASSOCIATESMATERIAL('0cuSbpct575BKLAXM$olZ_',#201,'IfcWallStandardCase',$,(#356,#385,#407,#429),#368);
#370= IFCMATERIAL('Block (Nominal)',$,'Block');
#376= IFCCARTESIANPOINT((0.,0.));
#377= IFCCARTESIANPOINT((192.,0.));
#378= IFCPOLYLINE((#376,#377));
#379= IFCPRODUCTDEFINITIONSHAPE($,$,(#381,#548));
#381= IFCSHAPEREPRESENTATION(#204,'Axis','Curve2D',(#378));
#384= IFCDIRECTION((1.,0.,0.));
#538= IFCCARTESIANPOINT((3.,-3.));
#539= IFCCARTESIANPOINT((189.,-3.));
#540= IFCCARTESIANPOINT((195.,3.));
#541= IFCCARTESIANPOINT((-3.,3.));
#542= IFCPOLYLINE((#538,#539,#540,#541,#538));
#543= IFCARBITRARYCLOSEDPROFILEDEF(.AREA.,$,#542);
#544= IFCEXTRUDEDAREASOLID(#543,#546,#547,120.);
#545= IFCCARTESIANPOINT((0.,0.,0.));
#546= IFCAXIS2PLACEMENT3D(#545,$,$);
#547= IFCDIRECTION((0.,0.,1.));
#548= IFCSHAPEREPRESENTATION(#204,'Body','SweptSolid',(#544));


/* Standard case walls #2 with axis and body geometry and material layer set defined ----------- */
/* --------------------------------------------------------------------------------------------- */
#385= IFCWALLSTANDARDCASE('2qqyx9mIf27vQuEGt7ts8$',#201,'Wall #2',$,$,#394,#401,$,.NOTDEFINED.);
#387= IFCRELCONNECTSELEMENTS('04lBS8dRv8082xho3Ve2L0',#201,$,$,$,#292,#385);
#388= IFCRELCONNECTSPATHELEMENTS('2aU84ph_f2iwRK7mEx6APn',#201,$,$,$,#356,#385,(),(),.ATSTART.,.ATEND.);
#391= IFCCARTESIANPOINT((0.,192.,6.));
#392= IFCDIRECTION((0.,0.,1.));
#393= IFCAXIS2PLACEMENT3D(#391,#392,#384);
#394= IFCLOCALPLACEMENT(#270,#393);
#398= IFCCARTESIANPOINT((0.,0.));
#399= IFCCARTESIANPOINT((288.,0.));
#400= IFCPOLYLINE((#398,#399));
#401= IFCPRODUCTDEFINITIONSHAPE($,$,(#403,#561));
#403= IFCSHAPEREPRESENTATION(#204,'Axis','Curve2D',(#400));
#406= IFCDIRECTION((0.,-1.,0.));
#551= IFCCARTESIANPOINT((3.,-3.));
#552= IFCCARTESIANPOINT((285.,-3.));
#553= IFCCARTESIANPOINT((291.,3.));
#554= IFCCARTESIANPOINT((-3.,3.));
#555= IFCPOLYLINE((#551,#552,#553,#554,#551));
#556= IFCARBITRARYCLOSEDPROFILEDEF(.AREA.,$,#555);
#557= IFCEXTRUDEDAREASOLID(#556,#559,#560,120.);
#558= IFCCARTESIANPOINT((0.,0.,0.));
#559= IFCAXIS2PLACEMENT3D(#558,$,$);
#560= IFCDIRECTION((0.,0.,1.));
#561= IFCSHAPEREPRESENTATION(#204,'Body','SweptSolid',(#557));

/* Standard case walls #3 with axis and body geometry and material layer set defined ----------- */
/* --------------------------------------------------------------------------------------------- */
#407= IFCWALLSTANDARDCASE('0edwXkNgP03AySQjQQlat2',#201,'Wall #3',$,$,#416,#423,$,.NOTDEFINED.);
#409= IFCRELCONNECTSELEMENTS('1_c3omXFD7UPfF9thiXwlM',#201,$,$,$,#292,#407);
#410= IFCRELCONNECTSPATHELEMENTS('3cHo0l0vT7YecRcCZH80A_',#201,$,$,$,#385,#407,(),(),.ATSTART.,.ATEND.);
#413= IFCCARTESIANPOINT((288.,192.,6.));
#414= IFCDIRECTION((0.,0.,1.));
#415= IFCAXIS2PLACEMENT3D(#413,#414,#406);
#416= IFCLOCALPLACEMENT(#270,#415);
#420= IFCCARTESIANPOINT((0.,0.));
#421= IFCCARTESIANPOINT((192.,0.));
#422= IFCPOLYLINE((#420,#421));
#423= IFCPRODUCTDEFINITIONSHAPE($,$,(#425,#574));
#425= IFCSHAPEREPRESENTATION(#204,'Axis','Curve2D',(#422));
#428= IFCDIRECTION((-1.,0.,0.));
#564= IFCCARTESIANPOINT((3.,-3.));
#565= IFCCARTESIANPOINT((189.,-3.));
#566= IFCCARTESIANPOINT((195.,3.));
#567= IFCCARTESIANPOINT((-3.,3.));
#568= IFCPOLYLINE((#564,#565,#566,#567,#564));
#569= IFCARBITRARYCLOSEDPROFILEDEF(.AREA.,$,#568);
#570= IFCEXTRUDEDAREASOLID(#569,#572,#573,120.);
#571= IFCCARTESIANPOINT((0.,0.,0.));
#572= IFCAXIS2PLACEMENT3D(#571,$,$);
#573= IFCDIRECTION((0.,0.,1.));
#574= IFCSHAPEREPRESENTATION(#204,'Body','SweptSolid',(#570));

/* Standard case walls #4 with axis and body geometry and material layer set defined ----------- */
/* --------------------------------------------------------------------------------------------- */
#429= IFCWALLSTANDARDCASE('27D5wuD5z86hr07xvbZLKW',#201,'Wall #4',$,$,#438,#445,$,.NOTDEFINED.);
#431= IFCRELCONNECTSELEMENTS('2GwDyB_Xb1mBzqynA5hpmk',#201,$,$,$,#292,#429);
#432= IFCRELCONNECTSPATHELEMENTS('0N2CJ8VSz1yx6iStYDzqLN',#201,$,$,$,#407,#429,(),(),.ATSTART.,.ATEND.);
#435= IFCCARTESIANPOINT((288.,0.,6.));
#436= IFCDIRECTION((0.,0.,1.));
#437= IFCAXIS2PLACEMENT3D(#435,#436,#428);
#438= IFCLOCALPLACEMENT(#270,#437);
#442= IFCCARTESIANPOINT((0.,0.));
#443= IFCCARTESIANPOINT((288.,0.));
#444= IFCPOLYLINE((#442,#443));
#445= IFCPRODUCTDEFINITIONSHAPE($,$,(#447,#587));
#447= IFCSHAPEREPRESENTATION(#204,'Axis','Curve2D',(#444));
#450= IFCRELCONNECTSPATHELEMENTS('2FxBB642r3GfFLy0QsWZoT',#201,$,$,$,#429,#356,(),(),.ATSTART.,.ATEND.);
#577= IFCCARTESIANPOINT((3.,-3.));
#578= IFCCARTESIANPOINT((285.,-3.));
#579= IFCCARTESIANPOINT((291.,3.));
#580= IFCCARTESIANPOINT((-3.,3.));
#581= IFCPOLYLINE((#577,#578,#579,#580,#577));
#582= IFCARBITRARYCLOSEDPROFILEDEF(.AREA.,$,#581);
#583= IFCEXTRUDEDAREASOLID(#582,#585,#586,120.);
#584= IFCCARTESIANPOINT((0.,0.,0.));
#585= IFCAXIS2PLACEMENT3D(#584,$,$);
#586= IFCDIRECTION((0.,0.,1.));
#587= IFCSHAPEREPRESENTATION(#204,'Body','SweptSolid',(#583));


/* Root task ----------------------------------------------------------------------------------- */
/* Scheduled start is specified which constrains the start of all descendent tasks ------------- */
#597= IFCTASKTIME($,.PREDICTED.,$,.WORKTIME.,'P0Y0M1DT16H0M0S','2010-09-20T08:00:00',$,'2010-09-20T08:00:00', '2010-09-24T16:00:00','2010-09-20T08:00:00','2010-09-20T16:00:00','P0Y0M0DT0H0M0S','P0Y0M0DT0H0M0S',.T.,$,$,$,$,$,$);
#598= IFCTASK('0HSpq15hDC2R2RlJos8Ql5',#201,'Ground Level',$,$,$,$,$,$,.F.,$,#597,$);
#599= IFCRELDECLARES('0CWmoqi1D5fvyIWw6QZMVg',#201,'PROCESS',$,#200,(#598));
#611= IFCRELASSIGNSTOPRODUCT('3fzXeaVoT7R8p4ro97HwNg',#201,$,$,(#598),$,#265);

/* Tasks for slabs, where each nested task is assigned to a specific slab ---------------------- */
/* Scheduled start and finish are not specified, such that early start and finish take effect -- */
#614= IFCTASKTIME($,.PREDICTED.,$,.WORKTIME.,'P0Y0M0DT8H0M0S',$,$,'2010-09-20T08:00:00','2010-09-20T16:00:00',$,$,$,$,$,$,$,$,$,$,$);
#615= IFCTASK('3OFhQ6xenBvBxNI4NhGlTz',#201,'Slab (Standard)',$,$,'IfcSlabStandardCase',$,$,$,.F.,$,#614,$);
#618= IFCRELNESTS('084eMXNPf4iQUJ1DL9YVyg',#201,$,$,#598,(#615,#639));
#620= IFCTASKTIME($,.PREDICTED.,$,.WORKTIME.,'P0Y0M0DT8H0M0S',$,$,'2010-09-20T08:00:00','2010-09-20T16:00:00', '2010-09-20T08:00:00','2010-09-20T16:00:00','P0Y0M0DT16H0M0S','P0Y0M0DT1H0M0S',.T.,$,$,$,$,$,$);
#621= IFCTASK('3vesIabXX7guLU5lIRuXh7',#201,'Slab #1',$,$,$,$,$,$,.F.,$,#620,$);
#622= IFCRELNESTS('25bUvqUPT8sO$Hp1WU0jhy',#201,$,$,#615,(#621));
#624= IFCRELASSIGNSTOPRODUCT('3vXaSpK9P0fg8T$uh5yD3o',#201,$,$,(#621),$,#292);

/* Tasks for walls where each nested task is assigned to a specific wall ----------------------- */
#638= IFCTASKTIME($,.PREDICTED.,$,.WORKTIME.,'P0Y0M1DT8H0M0S',$,$,'2010-09-21T08:00:00','2010-09-24T16:00:00',$,$,$,$,$,$,$,$,$,$,$);
#639= IFCTASK('0njFKjg5HAEuwb1SdMaVRR',#201,'Wall (Standard)',$,$,'IfcWallStandardCase',$,$,$,.F.,$,#638,$);
#643= IFCTASKTIME($,.PREDICTED.,$,.WORKTIME.,'P0Y0M0DT8H0M0S',$,$,'2010-09-21T08:00:00','2010-09-21T16:00:00', '2010-09-21T08:00:00','2010-09-21T16:00:00','P0Y0M0DT0H0M0S','P0Y0M0DT0H0M0S',.T.,$,$,$,$,$,$);
#644= IFCTASK('0Kq8RwbZH3afCy87RX8dso',#201,'Wall #1',$,$,$,$,$,$,.F.,$,#643,$);
#645= IFCRELNESTS('3i7xt4aCT70BYgowuH355l',#201,$,$,#639,(#644,#662,#676,#690));
#647= IFCRELASSIGNSTOPRODUCT('3Q5arGx_f1TBv9qlup9fDC',#201,$,$,(#644),$,#356);
#661= IFCTASKTIME($,.PREDICTED.,$,.WORKTIME.,'P0Y0M0DT8H0M0S',$,$,'2010-09-22T08:00:00','2010-09-22T16:00:00', '2010-09-22T08:00:00','2010-09-22T16:00:00','P0Y0M0DT0H0M0S','P0Y0M0DT0H0M0S',.T.,$,$,$,$,$,$);
#662= IFCTASK('0hLKmeFATD$PWRDGqXzqOk',#201,'Wall #2',$,$,$,$,$,$,.F.,$,#661,$);
#664= IFCRELASSIGNSTOPRODUCT('0$ba11SzL0hQrmYcN5xQqx',#201,$,$,(#662),$,#385);
#675= IFCTASKTIME($,.PREDICTED.,$,.WORKTIME.,'P0Y0M0DT8H0M0S',$,$,'2010-09-23T08:00:00','2010-09-23T16:00:00', '2010-09-23T08:00:00','2010-09-23T16:00:00','P0Y0M0DT0H0M0S','P0Y0M0DT0H0M0S',.T.,$,$,$,$,$,$);
#676= IFCTASK('0Yz5hS48v9jfiCNjUF0okF',#201,'Wall #3',$,$,$,$,$,$,.F.,$,#675,$);
#678= IFCRELASSIGNSTOPRODUCT('3gkG6$UaT7GwCw3Fu$NJuW',#201,$,$,(#676),$,#407);
#689= IFCTASKTIME($,.PREDICTED.,$,.WORKTIME.,'P0Y0M0DT8H0M0S',$,$,'2010-09-24T08:00:00','2010-09-24T16:00:00', '2010-09-24T08:00:00','2010-09-24T16:00:00','P0Y0M0DT0H0M0S','P0Y0M0DT0H0M0S',.T.,$,$,$,$,$,$);
#690= IFCTASK('0kKoBDlp13eBAIY8350FFo',#201,'Wall #4',$,$,$,$,$,$,.F.,$,#689,$);
#692= IFCRELASSIGNSTOPRODUCT('1ayXYhiTj4fuJRkoMMiKB1',#201,$,$,(#690),$,#429);

/* Sequences of tasks where each wall must be constructed after supporting slab ---------------- */
#703= IFCRELSEQUENCE('0ytgSSysP98eeo2xw37OMq',#201,$,$,#621,#644,$,.FINISH_START.,$);
#723= IFCRELSEQUENCE('0wK7QBCOv15RnMy6Ia9ykQ',#201,$,$,#621,#662,$,.FINISH_START.,$);
#728= IFCRELSEQUENCE('2Y5tFvjSfCngQp$$PYkXhS',#201,$,$,#621,#676,$,.FINISH_START.,$);
#733= IFCRELSEQUENCE('2hWL_Juob0BPVRlnbe1v3c',#201,$,$,#621,#690,$,.FINISH_START.,$);

/* Sequences of tasks indicating order of wall construction; otherwise could be omitted if ----- */
/* scheduled in parallel ----------------------------------------------------------------------- */
#739= IFCRELSEQUENCE('0GVHMGhZ58UhwQX6IyHGzD',#201,$,$,#644,#662,$,.FINISH_START.,$);
#755= IFCRELSEQUENCE('2cOAlYj8X46PLPMFHuoV8X',#201,$,$,#662,#676,$,.FINISH_START.,$);
#768= IFCRELSEQUENCE('3OZBIrkNX0IAUQIs8NADJm',#201,$,$,#676,#690,$,.FINISH_START.,$);

/* Work plan includes a set of related calendars and schedules, -------------------------------- */
/* each having assigned tasks and resources ---------------------------------------------------- */
#778= IFCWORKPLAN('0eVhmaYyb3sBLb0LoNiW62',#201,'Work Plan #1',$,$,$,'2010-09-23T16:26:16',$,$,$,$,'2010-09-23T00:00:00',$,$);
#779= IFCRELDECLARES('3n6EjscYnB4hf4mKVrfRVJ',#201,'CONTROL',$,#200,(#778));

/* Work calendar indicates work hours of 08:00-16:00 Monday thru Friday; the calendar is applied */
/* to the root task and indirectly to all descendent tasks unless overridden ------------------- */
#783= IFCWORKCALENDAR('0LHFCz8r5EQw4GeNNMS$Xp',#201,$,$,$,$,(#789),$,.FIRSTSHIFT.);
#784= IFCRELNESTS('0if6u97Ln58xH$wewg0rH3',#201,$,$,#778,(#783,#794));
#787= IFCTIMEPERIOD('08:00:00','16:00:00');
#788= IFCRECURRENCEPATTERN(.WEEKLY.,$,(1,2,3,4,5),$,$,$,$,(#787));
#789= IFCWORKTIME('Standard',$,$,#788,$,$);
#791= IFCRELASSIGNSTOCONTROL('3DnVao$j10NByLyVF3hP9m',#201,$,$,(#598),$,#783);

/* Work schedule specifies a view of tasks using planned times --------------------------------- */
#794= IFCWORKSCHEDULE('2LoWUCZHr7YvZks8lmqjcw',#201,$,$,$,$,'2010-09-23T16:26:42',$,$,$,$,'2010-09-23T00:00:00',$,.PLANNED.);
#797= IFCRELASSIGNSTOCONTROL('0_6CMgJyT5jviMnquaXCct',#201,$,$,(#598),$,#794);

ENDSEC;

END-ISO-10303-21;

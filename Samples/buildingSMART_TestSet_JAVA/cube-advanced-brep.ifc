ISO-10303-21;
HEADER;
FILE_DESCRIPTION(('ViewDefinition [notYetAssigned]'),'2;1');
FILE_NAME(
/* name */ 'advanced_brep.ifc',
/* time_stamp */ '2012-05-31T11:53:44',
/* author */ ('Jon'),
/* organization */ ('Unknown'),
/* preprocessor_version */ 'ssiIFC - Grasshopper Plug-in by Geometry Gym Pty Ltd',
/* originating_system */ 'ssiIFC - Grasshopper Plug-in by Geometry Gym Pty Ltd',
/* authorization */ 'None');

FILE_SCHEMA (('IFC4'));
ENDSEC;

DATA;

/* single owner history sufficient if not otherwise required by the view definition ------------ */
/* provides the person and application creating the data set, and the time it is created ------- */
#1= IFCAPPLICATION(#2,'********','ssiRhinoIFC - Geometry Gym Plug-in for Rhino3d','ssiRhinoIFC');
#2= IFCORGANIZATION($,'Geometry Gym Pty Ltd',$,$,$);
#3= IFCPERSONANDORGANIZATION(#4,#5,$);
#4= IFCPERSON($,'Jon',$,$,$,$,$,$);
#5= IFCORGANIZATION($,'UNKNOWN',$,$,$);
#6= IFCOWNERHISTORY(#3,#1,$,.ADDED.,1338465163,$,$,1338465163);

/* each IFC data set containing geometry has to define a geometric representation context ------ */
/* the attribute 'ContextType' has to be 'Model' for 3D model geometry ------------------------- */
#7= IFCCARTESIANPOINT((0.0,0.0,0.0));
#8= IFCDIRECTION((1.0,0.0,0.0));
#9= IFCDIRECTION((0.0,1.0,0.0));
#10= IFCDIRECTION((0.0,0.0,1.0));
#11= IFCAXIS2PLACEMENT3D(#7,#10,#8);
#12= IFCAXIS2PLACEMENT2D(#13,$);
#13= IFCCARTESIANPOINT((0.0,0.0));
#14= IFCAXIS2PLACEMENT3D(#7,#10,#8);
#15= IFCGEOMETRICREPRESENTATIONCONTEXT('Body','Model',3,0.00001,#14,#16);
#16= IFCDIRECTION((0.0,1.0));

/* each IFC data set containing geometry has to define at absolute minimum length and angle ---- */
#17= IFCSIUNIT(*,.LENGTHUNIT.,$,.METRE.);
#18= IFCSIUNIT(*,.AREAUNIT.,$,.SQUARE_METRE.);
#19= IFCSIUNIT(*,.VOLUMEUNIT.,$,.CUBIC_METRE.);
#27= IFCSIUNIT(*,.PLANEANGLEUNIT.,$,.RADIAN.);
#26= IFCUNITASSIGNMENT((#17,#18,#19,#27));

/* each IFC data set containing elements in a building context has to include a building ------- */
/* at absolute minimum (could have a site and stories as well) --------------------------------- */
#25= IFCPROJECT('2oT9YYbSrBUghtexcAcblU',#6,'Grasshopper Project',$,$,'Grasshopper Project',$,(#15),#26);
#21= IFCBUILDING('3qzoyCPy1CtfV237Rle9$t',#6,'Grasshopper Building','GH Building',$,#22,$,$,.ELEMENT.,$,$,$);
#22= IFCLOCALPLACEMENT($,#11);
#23= IFCRELCONTAINEDINSPATIALSTRUCTURE('0LkkopPYL7VANMLhZDjkkq',#6,'Building','Building Container for Elements',(#181),#21);
#28= IFCRELAGGREGATES('2C1MZiLqv228Cmw2sZ7QPO',#6,'Project Container','Project Container for Buildings',#25,(#21));

/* geometry definition of the advanced brep ---------------------------------------------------- */
/* previded as the 'AdvancedBrep' representation type for the 'Body' of a building element proxy */
#29= IFCCLOSEDSHELL((#104,#115,#131,#147,#163,#179));
#30= IFCVERTEXPOINT(#31);
#31= IFCCARTESIANPOINT((-0.5,-0.5,0.0));
#32= IFCVERTEXPOINT(#33);
#33= IFCCARTESIANPOINT((0.5,-0.5,0.0));
#34= IFCVERTEXPOINT(#35);
#35= IFCCARTESIANPOINT((0.5,0.5,0.0));
#36= IFCVERTEXPOINT(#37);
#37= IFCCARTESIANPOINT((-0.5,0.5,0.0));
#38= IFCVERTEXPOINT(#39);
#39= IFCCARTESIANPOINT((-0.683012701892219,-0.183012701892219,1.0));
#40= IFCVERTEXPOINT(#41);
#41= IFCCARTESIANPOINT((-0.183012701892219,0.683012701892219,1.0));
#42= IFCVERTEXPOINT(#43);
#43= IFCCARTESIANPOINT((0.683012701892219,0.183012701892219,1.0));
#44= IFCVERTEXPOINT(#45);
#45= IFCCARTESIANPOINT((0.183012701892219,-0.683012701892219,1.0));
#46= IFCCARTESIANPOINT((-0.5,0.5));
#47= IFCCARTESIANPOINT((-0.5,-0.5));
#48= IFCPOLYLINE((#47,#46));
#49= IFCEDGECURVE(#30,#36,#48,.T.);
#50= IFCCARTESIANPOINT((-0.5,-0.5));
#51= IFCCARTESIANPOINT((0.5,-0.5));
#52= IFCPOLYLINE((#51,#50));
#53= IFCEDGECURVE(#32,#30,#52,.T.);
#54= IFCCARTESIANPOINT((0.5,-0.5));
#55= IFCCARTESIANPOINT((0.5,0.5));
#56= IFCPOLYLINE((#55,#54));
#57= IFCEDGECURVE(#34,#32,#56,.T.);
#58= IFCCARTESIANPOINT((0.5,0.5));
#59= IFCCARTESIANPOINT((-0.5,0.5));
#60= IFCPOLYLINE((#59,#58));
#61= IFCEDGECURVE(#36,#34,#60,.T.);
#62= IFCCARTESIANPOINT((0.183012701892219,-0.683012701892219,1.0));
#63= IFCCARTESIANPOINT((-0.683012701892219,-0.183012701892219,1.0));
#64= IFCPOLYLINE((#63,#62));
#65= IFCEDGECURVE(#38,#44,#64,.T.);
#66= IFCCARTESIANPOINT((-0.683012701892219,-0.183012701892219,1.0));
#67= IFCCARTESIANPOINT((-0.183012701892219,0.683012701892219,1.0));
#68= IFCPOLYLINE((#67,#66));
#69= IFCEDGECURVE(#40,#38,#68,.T.);
#70= IFCCARTESIANPOINT((-0.183012701892219,0.683012701892219,1.0));
#71= IFCCARTESIANPOINT((0.683012701892219,0.183012701892219,1.0));
#72= IFCPOLYLINE((#71,#70));
#73= IFCEDGECURVE(#42,#40,#72,.T.);
#74= IFCCARTESIANPOINT((0.683012701892219,0.183012701892219,1.0));
#75= IFCCARTESIANPOINT((0.183012701892219,-0.683012701892219,1.0));
#76= IFCPOLYLINE((#75,#74));
#77= IFCEDGECURVE(#44,#42,#76,.T.);
#78= IFCCARTESIANPOINT((0.183012701892219,-0.683012701892219,1.0));
#79= IFCCARTESIANPOINT((-0.5,-0.5,0.0));
#80= IFCPOLYLINE((#79,#78));
#81= IFCEDGECURVE(#30,#44,#80,.T.);
#82= IFCCARTESIANPOINT((0.683012701892219,0.183012701892219,1.0));
#83= IFCCARTESIANPOINT((0.5,-0.5,0.0));
#84= IFCPOLYLINE((#83,#82));
#85= IFCEDGECURVE(#32,#42,#84,.T.);
#86= IFCCARTESIANPOINT((-0.183012701892219,0.683012701892219,1.0));
#87= IFCCARTESIANPOINT((0.5,0.5,0.0));
#88= IFCPOLYLINE((#87,#86));
#89= IFCEDGECURVE(#34,#40,#88,.T.);
#90= IFCCARTESIANPOINT((-0.683012701892219,-0.183012701892219,1.0));
#91= IFCCARTESIANPOINT((-0.5,0.5,0.0));
#92= IFCPOLYLINE((#91,#90));
#93= IFCEDGECURVE(#36,#38,#92,.T.);
#94= IFCORIENTEDEDGE(*,*,#49,.T.);
#95= IFCORIENTEDEDGE(*,*,#61,.T.);
#96= IFCORIENTEDEDGE(*,*,#57,.T.);
#97= IFCORIENTEDEDGE(*,*,#53,.T.);
#98= IFCEDGELOOP((#94,#95,#96,#97));
#99= IFCFACEOUTERBOUND(#98,.T.);
#100= IFCPLANE(#101);
#101= IFCAXIS2PLACEMENT3D(#102,#103,#8);
#102= IFCCARTESIANPOINT((-0.5,-0.5,0.0));
#103= IFCDIRECTION((0.0,0.0,-1.0));
#104= IFCADVANCEDFACE((#99),#100,.T.);
#105= IFCORIENTEDEDGE(*,*,#65,.T.);
#106= IFCORIENTEDEDGE(*,*,#77,.T.);
#107= IFCORIENTEDEDGE(*,*,#73,.T.);
#108= IFCORIENTEDEDGE(*,*,#69,.T.);
#109= IFCEDGELOOP((#105,#106,#107,#108));
#110= IFCFACEOUTERBOUND(#109,.T.);
#111= IFCPLANE(#112);
#112= IFCAXIS2PLACEMENT3D(#113,#10,#114);
#113= IFCCARTESIANPOINT((-0.683012701892219,-0.183012701892219,1.0));
#114= IFCDIRECTION((-0.5,-0.8660254,0.0));
#115= IFCADVANCEDFACE((#110),#111,.T.);
#116= IFCORIENTEDEDGE(*,*,#93,.T.);
#117= IFCORIENTEDEDGE(*,*,#65,.T.);
#118= IFCORIENTEDEDGE(*,*,#81,.F.);
#119= IFCORIENTEDEDGE(*,*,#49,.T.);
#120= IFCEDGELOOP((#116,#117,#118,#119));
#121= IFCFACEOUTERBOUND(#120,.T.);
#122= IFCBSPLINESURFACEWITHKNOTS(3,1,((#123,#124),(#125,#126),(#127,#128),(#129,#130)),.UNSPECIFIED.,.F.,.F.,.U.,(4,4),(2,2),(0.0,1224.74487139159),(3.0,4.0),.UNSPECIFIED.);
#123= IFCCARTESIANPOINT((-0.5,0.5,0.0));
#124= IFCCARTESIANPOINT((-0.5,-0.5,0.0));
#125= IFCCARTESIANPOINT((-0.561004233964073,0.27232909936926,0.333333333333333));
#126= IFCCARTESIANPOINT((-0.27232909936926,-0.561004233964073,0.333333333333333));
#127= IFCCARTESIANPOINT((-0.622008467928146,0.0446581987385206,0.666666666666667));
#128= IFCCARTESIANPOINT((-0.0446581987385206,-0.622008467928146,0.666666666666667));
#129= IFCCARTESIANPOINT((-0.683012701892219,-0.183012701892219,1.0));
#130= IFCCARTESIANPOINT((0.183012701892219,-0.683012701892219,1.0));
#131= IFCADVANCEDFACE((#121),#122,.F.);
#132= IFCORIENTEDEDGE(*,*,#53,.T.);
#133= IFCORIENTEDEDGE(*,*,#81,.T.);
#134= IFCORIENTEDEDGE(*,*,#77,.T.);
#135= IFCORIENTEDEDGE(*,*,#85,.F.);
#136= IFCEDGELOOP((#132,#133,#134,#135));
#137= IFCFACEOUTERBOUND(#136,.T.);
#138= IFCBSPLINESURFACEWITHKNOTS(3,1,((#139,#140),(#141,#142),(#143,#144),(#145,#146)),.UNSPECIFIED.,.F.,.F.,.U.,(4,4),(2,2),(0.0,1224.74487139159),(0.0,1.0),.UNSPECIFIED.);
#139= IFCCARTESIANPOINT((-0.5,-0.5,0.0));
#140= IFCCARTESIANPOINT((0.5,-0.5,0.0));
#141= IFCCARTESIANPOINT((-0.27232909936926,-0.561004233964073,0.333333333333333));
#142= IFCCARTESIANPOINT((0.561004233964073,-0.27232909936926,0.333333333333333));
#143= IFCCARTESIANPOINT((-0.0446581987385206,-0.622008467928146,0.666666666666667));
#144= IFCCARTESIANPOINT((0.622008467928146,-0.0446581987385206,0.666666666666667));
#145= IFCCARTESIANPOINT((0.183012701892219,-0.683012701892219,1.0));
#146= IFCCARTESIANPOINT((0.683012701892219,0.183012701892219,1.0));
#147= IFCADVANCEDFACE((#137),#138,.F.);
#148= IFCORIENTEDEDGE(*,*,#57,.T.);
#149= IFCORIENTEDEDGE(*,*,#85,.T.);
#150= IFCORIENTEDEDGE(*,*,#73,.T.);
#151= IFCORIENTEDEDGE(*,*,#89,.F.);
#152= IFCEDGELOOP((#148,#149,#150,#151));
#153= IFCFACEOUTERBOUND(#152,.T.);
#154= IFCBSPLINESURFACEWITHKNOTS(3,1,((#155,#156),(#157,#158),(#159,#160),(#161,#162)),.UNSPECIFIED.,.F.,.F.,.U.,(4,4),(2,2),(0.0,1224.74487139159),(1.0,2.0),.UNSPECIFIED.);
#155= IFCCARTESIANPOINT((0.5,-0.5,0.0));
#156= IFCCARTESIANPOINT((0.5,0.5,0.0));
#157= IFCCARTESIANPOINT((0.561004233964073,-0.27232909936926,0.333333333333333));
#158= IFCCARTESIANPOINT((0.27232909936926,0.561004233964073,0.333333333333333));
#159= IFCCARTESIANPOINT((0.622008467928146,-0.0446581987385206,0.666666666666667));
#160= IFCCARTESIANPOINT((0.0446581987385206,0.622008467928146,0.666666666666667));
#161= IFCCARTESIANPOINT((0.683012701892219,0.183012701892219,1.0));
#162= IFCCARTESIANPOINT((-0.183012701892219,0.683012701892219,1.0));
#163= IFCADVANCEDFACE((#153),#154,.F.);
#164= IFCORIENTEDEDGE(*,*,#61,.T.);
#165= IFCORIENTEDEDGE(*,*,#89,.T.);
#166= IFCORIENTEDEDGE(*,*,#69,.T.);
#167= IFCORIENTEDEDGE(*,*,#93,.F.);
#168= IFCEDGELOOP((#164,#165,#166,#167));
#169= IFCFACEOUTERBOUND(#168,.T.);
#170= IFCBSPLINESURFACEWITHKNOTS(3,1,((#171,#172),(#173,#174),(#175,#176),(#177,#178)),.UNSPECIFIED.,.F.,.F.,.U.,(4,4),(2,2),(0.0,1224.74487139159),(2.0,3.0),.UNSPECIFIED.);
#171= IFCCARTESIANPOINT((0.5,0.5,0.0));
#172= IFCCARTESIANPOINT((-0.5,0.5,0.0));
#173= IFCCARTESIANPOINT((0.27232909936926,0.561004233964073,0.333333333333333));
#174= IFCCARTESIANPOINT((-0.561004233964073,0.27232909936926,0.333333333333333));
#175= IFCCARTESIANPOINT((0.0446581987385206,0.622008467928146,0.666666666666667));
#176= IFCCARTESIANPOINT((-0.622008467928146,0.0446581987385206,0.666666666666667));
#177= IFCCARTESIANPOINT((-0.183012701892219,0.683012701892219,1.0));
#178= IFCCARTESIANPOINT((-0.683012701892219,-0.183012701892219,1.0));
#179= IFCADVANCEDFACE((#169),#170,.F.);
#180= IFCADVANCEDBREP(#29);
#181= IFCBUILDINGELEMENTPROXY('1hMBdOkWj7WhC2kvgZp44F',#6,'BuildingElementProxy',$,$,#184,#182,$,.NOTDEFINED.);
#182= IFCPRODUCTDEFINITIONSHAPE($,$,(#183));
#183= IFCSHAPEREPRESENTATION(#15,'Body','AdvancedBrep',(#180));
#184= IFCLOCALPLACEMENT($,#11);
ENDSEC;

END-ISO-10303-21;



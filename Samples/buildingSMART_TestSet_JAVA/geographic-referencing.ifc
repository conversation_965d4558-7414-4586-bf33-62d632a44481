ISO-10303-21;
HEADER;

FILE_DESCRIPTION(
/* description */ ('GeoReference'),
/* implementation level */ '2;1');

FILE_NAME(
/* name */ 'GeoReference.ifc',
/* time_stamp */ '2014-08-22T15:03:00',
/* author */ ('Geiger'),
/* organization */ ('KIT'),
/* preprocessor_version */ 'Handmade',
/* originating_system */ '',
/* authorisation */ 'none');

FILE_SCHEMA (('IFC4'));
ENDSEC;

DATA;
/* Geographic reference*/
#1= IFCPROJECTEDCRS('EPSG:25832','UTM in band 32','ETRS89',$,'UTM','UTM32',#3);
#2= IFCMAPCONVERSION(#100011,#1,458658.78,5438232.79,113.7,0.270600445976,0.962691746426,$);
#3= IFCSIUNIT(*,.LENGTHUNIT.,$,.METRE.);

/* BuildingElementProxy */
#149= IFCBUILDINGELEMENTPROXY('0rBru4syGxGOZb$M8kVJuS',#100005,'Geographic Position',$,$,#121,#5037,$,$);
#121= IFCLOCALPLACEMENT(#5044,#243);
#243= IFCAXIS2PLACEMENT3D(#173,#217,#218);
#173= IFCCARTESIANPOINT((0.,0.,0.));
#217= IFCDIRECTION((0.,0.,1.));
#218= IFCDIRECTION((1.,0.,0.));

#5037= IFCPRODUCTDEFINITIONSHAPE('',$,(#5038));
#5038= IFCSHAPEREPRESENTATION(#100011,'Body','CSG',(#5000,#6000));

/* Cone geometry */
#5000= IFCCSGSOLID(#5001);
#5001= IFCRIGHTCIRCULARCONE(#5002, 500., 150.);
#5002= IFCAXIS2PLACEMENT3D(#5003,#5005,#5004);
#5003= IFCCARTESIANPOINT((0.,0.,500.));
#5004= IFCDIRECTION((-1.,0.,0.));
#5005= IFCDIRECTION((0.,0.,-1.));

/* Cylinder geometry */
#6000= IFCCSGSOLID(#6001);
#6001= IFCRIGHTCIRCULARCYLINDER(#6002, 500., 50.);
#6002= IFCAXIS2PLACEMENT3D(#6003,#6005,#6004);
#6003= IFCCARTESIANPOINT((0.,0.,500.));
#6004= IFCDIRECTION((1.,0.,0.));
#6005= IFCDIRECTION((0.,0.,1.));

/* BuildingStorey */
#5043= IFCBUILDINGSTOREY('3_fv_WeK63IPclwapZa9MD',#100005,'Storey 1',$,$,#5044,$,$,.ELEMENT.,-1.6);
#5044= IFCLOCALPLACEMENT(#100025,#100040);
#5045= IFCRELCONTAINEDINSPATIALSTRUCTURE('2lLFgu3KhSGw4jMC3$Ak50',#100005,'Storey 1',$,(#149),#5043);
#5046= IFCRELAGGREGATES('3INy2PAPGPJ8Dpwhrq610o',#100005,'All stories',$,#100023,(#5043));

/* Person, Org, App, Project */
#100001= IFCPERSON($,'AGeiger',$,$,$,$,$,$);
#100002= IFCORGANIZATION($,'KIT',$,$,$);
#100003= IFCPERSONANDORGANIZATION(#100001,#100002,$);
#100004= IFCAPPLICATION(#100002,'Unknown','IFCExplorer','Unknown');
#100005= IFCOWNERHISTORY(#100003,#100004,$,.NOTDEFINED.,$,$,$,1122650864);
#100010= IFCPROJECT('01JwSt5ycUHvKFlMZUleKS',#100005,'Notch',$,$,$,$,(#100011),#100060);
#100011= IFCGEOMETRICREPRESENTATIONCONTEXT($,'Model',3,1.0E-5,#100040,$);

/* Site */
#100020= IFCSITE('1BPCQAtTW7GhKrUF$Sytrr',#100005,'Site',$,$,#100022,$,$,.ELEMENT.,(49,5,44,124),(8,26,1,320000),113.7,$,$);
#100021= IFCRELAGGREGATES('2vOmTZNmbvJAJRbSGMMNrC',#100005,$,$,#100010,(#100020));
#100022= IFCLOCALPLACEMENT($,#100040);

/* Building */
#100023= IFCBUILDING('0cVTYHAI3nGeTXA2X4Gxyo',#100005,'Building',$,$,#100025,$,$,.ELEMENT.,$,$,$);
#100024= IFCRELAGGREGATES('1F_GRhxz1GJgGgcJgzjvKX',#100005,$,$,#100020,(#100023));
#100025= IFCLOCALPLACEMENT(#100022,#100040);

/* Placement */
#100040= IFCAXIS2PLACEMENT3D(#100041,#100044,#100042);
#100041= IFCCARTESIANPOINT((0.,0.,0.));
#100042= IFCDIRECTION((1.,0.,0.));
#100044= IFCDIRECTION((0.,0.,1.));

/* Units */
#100060= IFCUNITASSIGNMENT((#100061,#100062,#100063,#100064));
#100061= IFCSIUNIT(*,.LENGTHUNIT.,.MILLI.,.METRE.);
#100062= IFCSIUNIT(*,.AREAUNIT.,$,.SQUARE_METRE.);
#100063= IFCSIUNIT(*,.VOLUMEUNIT.,$,.CUBIC_METRE.);
#100064= IFCSIUNIT(*,.PLANEANGLEUNIT.,$,.RADIAN.);

ENDSEC;

END-ISO-10303-21;

ISO-10303-21;
HEADER;
FILE_DESCRIPTION(('ViewDefinition [notYetAssigned]','Option [Filter: VisibleElements]'),'2;1');
FILE_NAME('grid placement.ifc','2013-07-13T10:17:34',('Architect'),('Building Designer Office'),'any','any','The authorising person');
FILE_SCHEMA(('IFC4'));
ENDSEC;

DATA;
/* Owner history as dummy information, maybe omitted  ---------------------- */
#1= IFCORGANIZATION($,'Name',$,$,$);
#5= IFCAPPLICATION(#1,'Version','Name','Identifier');
#6= IFCPERSON($,'Family name','Given name',$,$,$,$,$);
#8= IFCORGANIZATION($,'Name',$,$,$);
#12= IFCPERSONANDORGANIZATION(#6,#8,$);
#13= IFCOWNERHISTORY(#12,#5,$,.NOTDEFINED.,$,$,$,1247473054);

/* Global unit definitions  ------------------------------------------------ */
#14= IFCSIUNIT(*,.LENGTHUNIT.,.MILLI.,.METRE.);
#15= IFCSIUNIT(*,.AREAUNIT.,$,.SQUARE_METRE.);
#16= IFCSIUNIT(*,.VOLUMEUNIT.,$,.CUBIC_METRE.);
#17= IFCSIUNIT(*,.PLANEANGLEUNIT.,$,.RADIAN.);
#18= IFCMEASUREWITHUNIT(IFCPLANEANGLEMEASURE(0.017453293),#17);
#19= IFCDIMENSIONALEXPONENTS(0,0,0,0,0,0,0);
#20= IFCCONVERSIONBASEDUNIT(#19,.PLANEANGLEUNIT.,'DEGREE',#18);
#21= IFCSIUNIT(*,.SOLIDANGLEUNIT.,$,.STERADIAN.);
#22= IFCSIUNIT(*,.MASSUNIT.,$,.GRAM.);
#23= IFCSIUNIT(*,.TIMEUNIT.,$,.SECOND.);
#24= IFCSIUNIT(*,.THERMODYNAMICTEMPERATUREUNIT.,$,.DEGREE_CELSIUS.);
#25= IFCSIUNIT(*,.LUMINOUSINTENSITYUNIT.,$,.LUMEN.);
#26= IFCUNITASSIGNMENT((#14,#15,#16,#20,#21,#22,#23,#24,#25));

/* Defining reusable commonly used directions and points ------------------- */
#28= IFCDIRECTION((1.,0.,0.));
#32= IFCDIRECTION((0.,1.,0.));
#36= IFCDIRECTION((0.,0.,1.));
#40= IFCCARTESIANPOINT((0.,0.,0.));

/* Defining project and representation contexts ---------------------------- */
#44= IFCAXIS2PLACEMENT3D(#40,#36,#28);
#47= IFCDIRECTION((0.,1.));
#51= IFCGEOMETRICREPRESENTATIONCONTEXT($,'Model',3,1.0000000E-5,#44,#47);
#52= IFCGEOMETRICREPRESENTATIONSUBCONTEXT('FootPrint','Model',*,*,*,*,#51,$,.MODEL_VIEW.,$);
#53= IFCGEOMETRICREPRESENTATIONSUBCONTEXT('Body','Model',*,*,*,*,#51,$,.MODEL_VIEW.,$);
#54= IFCPROJECT('2btxazyiP8WAIfB0dw8meA',#13,'Default Project',$,$,$,$,(#51),#26);

/* Defining site, building and first story --------------------------------- */
#61= IFCLOCALPLACEMENT($,#44);
#64= IFCSITE('0AU$p8piP1M9KEzK5w32XM',#13,'Default Site',$,$,#61,$,$,.ELEMENT.,(24,28,0),(54,25,0),$,$,$);
#74= IFCLOCALPLACEMENT(#61,#44);
#77= IFCBUILDING('0IuzxQ0mr3BOdtlAS8ZLt_',#13,'Default Building',$,$,#74,$,$,.ELEMENT.,$,$,$);
#87= IFCAXIS2PLACEMENT3D(#40,#36,#28);
#90= IFCLOCALPLACEMENT(#74,#87);
#93= IFCBUILDINGSTOREY('3AG_0oB1zBfhm3BFIxGOjF',#13,'Ground Floor',$,$,#90,$,$,.ELEMENT.,0.);

/* Defining the grid axes geometry ----------------------------------------- */
#103= IFCCARTESIANPOINT((0.,0.));
#107= IFCCARTESIANPOINT((23000.,0.));
#111= IFCPOLYLINE((#103,#107));
#115= IFCGRIDAXIS('5',#111,.T.);
#120= IFCCARTESIANPOINT((0.,-2000.));
#124= IFCCARTESIANPOINT((23000.,-2000.));
#128= IFCPOLYLINE((#120,#124));
#132= IFCGRIDAXIS('4',#128,.T.);
#137= IFCCARTESIANPOINT((0.,-6000.));
#141= IFCCARTESIANPOINT((23000.,-6000.));
#145= IFCPOLYLINE((#137,#141));
#149= IFCGRIDAXIS('3',#145,.T.);
#154= IFCCARTESIANPOINT((0.,-8000.));
#158= IFCCARTESIANPOINT((23000.,-8000.));
#162= IFCPOLYLINE((#154,#158));
#166= IFCGRIDAXIS('2',#162,.T.);
#171= IFCCARTESIANPOINT((0.,-12000.));
#175= IFCCARTESIANPOINT((23000.,-12000.));
#179= IFCPOLYLINE((#171,#175));
#183= IFCGRIDAXIS('1',#179,.T.);
#188= IFCCARTESIANPOINT((19000.,-16000.));
#192= IFCCARTESIANPOINT((19000.,3000.));
#196= IFCPOLYLINE((#188,#192));
#200= IFCGRIDAXIS('A',#196,.T.);
#205= IFCCARTESIANPOINT((15000.,-16000.));
#209= IFCCARTESIANPOINT((15000.,3000.));
#213= IFCPOLYLINE((#205,#209));
#217= IFCGRIDAXIS('B',#213,.T.);
#222= IFCCARTESIANPOINT((11000.,-16000.));
#226= IFCCARTESIANPOINT((11000.,3000.));
#230= IFCPOLYLINE((#222,#226));
#234= IFCGRIDAXIS('C',#230,.T.);
#239= IFCCARTESIANPOINT((7000.,-16000.));
#243= IFCCARTESIANPOINT((7000.,3000.));
#247= IFCPOLYLINE((#239,#243));
#251= IFCGRIDAXIS('D',#247,.T.);
#256= IFCCARTESIANPOINT((3000.,-16000.));
#260= IFCCARTESIANPOINT((3000.,3000.));
#264= IFCPOLYLINE((#256,#260));
#268= IFCGRIDAXIS('E',#264,.T.);
#273= IFCCARTESIANPOINT((-17000.,16000.,0.));

/* Defining the grid with 5 u-axes and 5 v-axes  --------------------------- */
#277= IFCAXIS2PLACEMENT3D(#273,#36,#28);
#280= IFCLOCALPLACEMENT(#90,#277);
#283= IFCGRID('0fuUMCx0jFggWzjspLeC2b',#13,$,$,$,#280,#286,(#115,#132,#149,#166,#183),(#200,#217,#234,#251,#268),$,$);
#284= IFCGEOMETRICCURVESET((#111,#128,#145,#162,#179,#196,#213,#230,#247,#264));
#285= IFCSHAPEREPRESENTATION(#52,'FootPrint','GeometricCurveSet',(#284));
#286= IFCPRODUCTDEFINITIONSHAPE($,$,(#285));

/* Column with grid placement ---------------------------------------------- */
#293= IFCCOLUMN('2E6Q5P3bD23h5JOtEANY6k',#13,'CRE - 001',$,$,#351,#344,$,$);
#312= IFCDIRECTION((1.,0.));
#316= IFCCARTESIANPOINT((0.,0.));
#320= IFCAXIS2PLACEMENT2D(#316,#312);
#323= IFCRECTANGLEPROFILEDEF(.AREA.,'',#320,300.,300.);
#324= IFCAXIS2PLACEMENT3D(#40,#36,#28);
#327= IFCEXTRUDEDAREASOLID(#323,#324,#36,2800.);
#330= IFCDIRECTION((6.1232340E-17,1.));
#338= IFCSHAPEREPRESENTATION(#53,'Body','SweptSolid',(#327));
#344= IFCPRODUCTDEFINITIONSHAPE($,$,(#338));
#348= IFCVIRTUALGRIDINTERSECTION((#268,#183),(0.,0.,0.));
#351= IFCGRIDPLACEMENT(#348,$);
#354= IFCMATERIAL('Structural Concrete',$,$);
#357= IFCDIRECTION((6.1232340E-17,1.));
#505= IFCRELASSOCIATESMATERIAL('2ATJkq8SP8Nwm_CddbSUNm',#13,$,$,(#293),#354);

/* Column with grid placement ---------------------------------------------- */
#519= IFCCOLUMN('3SVzKRDSn40RvQ9U19U6kS',#13,'CRE - 001',$,$,#569,#562,$,$);
#538= IFCDIRECTION((1.,0.));
#542= IFCCARTESIANPOINT((0.,0.));
#546= IFCAXIS2PLACEMENT2D(#542,#538);
#549= IFCRECTANGLEPROFILEDEF(.AREA.,'',#546,300.,300.);
#550= IFCAXIS2PLACEMENT3D(#40,#36,#28);
#553= IFCEXTRUDEDAREASOLID(#549,#550,#36,2800.);
#556= IFCSHAPEREPRESENTATION(#53,'Body','SweptSolid',(#553));
#562= IFCPRODUCTDEFINITIONSHAPE($,$,(#556));
#566= IFCVIRTUALGRIDINTERSECTION((#268,#166),(0.,0.,0.));
#569= IFCGRIDPLACEMENT(#566,$);
#572= IFCRELASSOCIATESMATERIAL('3nfekYZyrDse$wWaYIT6nT',#13,$,$,(#519),#354);

/* Column with grid placement ---------------------------------------------- */
#582= IFCCOLUMN('002c3Y6Or2aOf9mUU7hAvu',#13,'CRE - 001',$,$,#632,#625,$,$);
#601= IFCDIRECTION((1.,0.));
#605= IFCCARTESIANPOINT((0.,0.));
#609= IFCAXIS2PLACEMENT2D(#605,#601);
#612= IFCRECTANGLEPROFILEDEF(.AREA.,'',#609,300.,300.);
#613= IFCAXIS2PLACEMENT3D(#40,#36,#28);
#616= IFCEXTRUDEDAREASOLID(#612,#613,#36,2800.);
#619= IFCSHAPEREPRESENTATION(#53,'Body','SweptSolid',(#616));
#625= IFCPRODUCTDEFINITIONSHAPE($,$,(#619));
#629= IFCVIRTUALGRIDINTERSECTION((#268,#149),(0.,0.,0.));
#632= IFCGRIDPLACEMENT(#629,$);
#635= IFCRELASSOCIATESMATERIAL('0snYjDQCP0eQo4c1JRlcY5',#13,$,$,(#582),#354);

/* Column with grid placement ---------------------------------------------- */
#645= IFCCOLUMN('1y22xNyUvFPB4zIQ9TNnTI',#13,'CRE - 001',$,$,#695,#688,$,$);
#664= IFCDIRECTION((1.,0.));
#668= IFCCARTESIANPOINT((0.,0.));
#672= IFCAXIS2PLACEMENT2D(#668,#664);
#675= IFCRECTANGLEPROFILEDEF(.AREA.,'',#672,300.,300.);
#676= IFCAXIS2PLACEMENT3D(#40,#36,#28);
#679= IFCEXTRUDEDAREASOLID(#675,#676,#36,2800.);
#682= IFCSHAPEREPRESENTATION(#53,'Body','SweptSolid',(#679));
#688= IFCPRODUCTDEFINITIONSHAPE($,$,(#682));
#692= IFCVIRTUALGRIDINTERSECTION((#268,#132),(0.,0.,0.));
#695= IFCGRIDPLACEMENT(#692,$);
#698= IFCRELASSOCIATESMATERIAL('1ebZRfr2H3thQgmzsIxFDn',#13,$,$,(#645),#354);

/* Column with grid placement ---------------------------------------------- */
#708= IFCCOLUMN('1fu9dbbH5BwwDx0ER7ETo_',#13,'CRE - 001',$,$,#758,#751,$,$);
#727= IFCDIRECTION((1.,0.));
#731= IFCCARTESIANPOINT((0.,0.));
#735= IFCAXIS2PLACEMENT2D(#731,#727);
#738= IFCRECTANGLEPROFILEDEF(.AREA.,'',#735,300.,300.);
#739= IFCAXIS2PLACEMENT3D(#40,#36,#28);
#742= IFCEXTRUDEDAREASOLID(#738,#739,#36,2800.);
#745= IFCSHAPEREPRESENTATION(#53,'Body','SweptSolid',(#742));
#751= IFCPRODUCTDEFINITIONSHAPE($,$,(#745));
#755= IFCVIRTUALGRIDINTERSECTION((#268,#115),(0.,0.,0.));
#758= IFCGRIDPLACEMENT(#755,$);
#761= IFCRELASSOCIATESMATERIAL('2b6fbnDeD7FAJbwuuFP7fd',#13,$,$,(#708),#354);

/* Column with grid placement ---------------------------------------------- */
#771= IFCCOLUMN('3rLFU7pKTC0w1RlhdUf_T2',#13,'CRE - 001',$,$,#821,#814,$,$);
#790= IFCDIRECTION((1.,0.));
#794= IFCCARTESIANPOINT((0.,0.));
#798= IFCAXIS2PLACEMENT2D(#794,#790);
#801= IFCRECTANGLEPROFILEDEF(.AREA.,'',#798,300.,300.);
#802= IFCAXIS2PLACEMENT3D(#40,#36,#28);
#805= IFCEXTRUDEDAREASOLID(#801,#802,#36,2800.);
#808= IFCSHAPEREPRESENTATION(#53,'Body','SweptSolid',(#805));
#814= IFCPRODUCTDEFINITIONSHAPE($,$,(#808));
#818= IFCVIRTUALGRIDINTERSECTION((#251,#183),(0.,0.,0.));
#821= IFCGRIDPLACEMENT(#818,$);
#824= IFCRELASSOCIATESMATERIAL('1zEryVYB50Y99wJfI_CxFW',#13,$,$,(#771),#354);

/* Column with grid placement ---------------------------------------------- */
#834= IFCCOLUMN('2N_A2Icv9AJvaoWqRYJchR',#13,'CRE - 001',$,$,#884,#877,$,$);
#853= IFCDIRECTION((1.,0.));
#857= IFCCARTESIANPOINT((0.,0.));
#861= IFCAXIS2PLACEMENT2D(#857,#853);
#864= IFCRECTANGLEPROFILEDEF(.AREA.,'',#861,300.,300.);
#865= IFCAXIS2PLACEMENT3D(#40,#36,#28);
#868= IFCEXTRUDEDAREASOLID(#864,#865,#36,2800.);
#871= IFCSHAPEREPRESENTATION(#53,'Body','SweptSolid',(#868));
#877= IFCPRODUCTDEFINITIONSHAPE($,$,(#871));
#881= IFCVIRTUALGRIDINTERSECTION((#251,#166),(0.,0.,0.));
#884= IFCGRIDPLACEMENT(#881,$);
#887= IFCRELASSOCIATESMATERIAL('04YhPfxCb18voKDnykCPKa',#13,$,$,(#834),#354);

/* Column with grid placement ---------------------------------------------- */
#897= IFCCOLUMN('0slTgBVK10Ketv8nvGZqUC',#13,'CRE - 001',$,$,#947,#940,$,$);
#916= IFCDIRECTION((1.,0.));
#920= IFCCARTESIANPOINT((0.,0.));
#924= IFCAXIS2PLACEMENT2D(#920,#916);
#927= IFCRECTANGLEPROFILEDEF(.AREA.,'',#924,300.,300.);
#928= IFCAXIS2PLACEMENT3D(#40,#36,#28);
#931= IFCEXTRUDEDAREASOLID(#927,#928,#36,2800.);
#934= IFCSHAPEREPRESENTATION(#53,'Body','SweptSolid',(#931));
#940= IFCPRODUCTDEFINITIONSHAPE($,$,(#934));
#944= IFCVIRTUALGRIDINTERSECTION((#251,#149),(0.,0.,0.));
#947= IFCGRIDPLACEMENT(#944,$);
#950= IFCRELASSOCIATESMATERIAL('3ADJGROcj1reAZrek3Ih43',#13,$,$,(#897),#354);

/* Column with grid placement ---------------------------------------------- */
#960= IFCCOLUMN('2DpWx6JKf7qRzGJNETj8dw',#13,'CRE - 001',$,$,#1010,#1003,$,$);
#979= IFCDIRECTION((1.,0.));
#983= IFCCARTESIANPOINT((0.,0.));
#987= IFCAXIS2PLACEMENT2D(#983,#979);
#990= IFCRECTANGLEPROFILEDEF(.AREA.,'',#987,300.,300.);
#991= IFCAXIS2PLACEMENT3D(#40,#36,#28);
#994= IFCEXTRUDEDAREASOLID(#990,#991,#36,2800.);
#997= IFCSHAPEREPRESENTATION(#53,'Body','SweptSolid',(#994));
#1003= IFCPRODUCTDEFINITIONSHAPE($,$,(#997));
#1007= IFCVIRTUALGRIDINTERSECTION((#251,#132),(0.,0.,0.));
#1010= IFCGRIDPLACEMENT(#1007,$);
#1013= IFCRELASSOCIATESMATERIAL('1ej_tV7eL8vuX6xJj3Xoxn',#13,$,$,(#960),#354);

/* Column with grid placement ---------------------------------------------- */
#1023= IFCCOLUMN('3bJspXGYDEoQwjnHqkbpKe',#13,'CRE - 001',$,$,#1073,#1066,$,$);
#1042= IFCDIRECTION((1.,0.));
#1046= IFCCARTESIANPOINT((0.,0.));
#1050= IFCAXIS2PLACEMENT2D(#1046,#1042);
#1053= IFCRECTANGLEPROFILEDEF(.AREA.,'',#1050,300.,300.);
#1054= IFCAXIS2PLACEMENT3D(#40,#36,#28);
#1057= IFCEXTRUDEDAREASOLID(#1053,#1054,#36,2800.);
#1060= IFCSHAPEREPRESENTATION(#53,'Body','SweptSolid',(#1057));
#1066= IFCPRODUCTDEFINITIONSHAPE($,$,(#1060));
#1070= IFCVIRTUALGRIDINTERSECTION((#251,#115),(0.,0.,0.));
#1073= IFCGRIDPLACEMENT(#1070,$);
#1076= IFCRELASSOCIATESMATERIAL('0L2mkv1m9AjAgDU16VxxRY',#13,$,$,(#1023),#354);

/* Column with grid placement ---------------------------------------------- */
#1086= IFCCOLUMN('3sNRA7UHP0x9bDzO0SwHVt',#13,'CRE - 001',$,$,#1136,#1129,$,$);
#1105= IFCDIRECTION((1.,0.));
#1109= IFCCARTESIANPOINT((0.,0.));
#1113= IFCAXIS2PLACEMENT2D(#1109,#1105);
#1116= IFCRECTANGLEPROFILEDEF(.AREA.,'',#1113,300.,300.);
#1117= IFCAXIS2PLACEMENT3D(#40,#36,#28);
#1120= IFCEXTRUDEDAREASOLID(#1116,#1117,#36,2800.);
#1123= IFCSHAPEREPRESENTATION(#53,'Body','SweptSolid',(#1120));
#1129= IFCPRODUCTDEFINITIONSHAPE($,$,(#1123));
#1133= IFCVIRTUALGRIDINTERSECTION((#234,#183),(0.,0.,0.));
#1136= IFCGRIDPLACEMENT(#1133,$);
#1139= IFCRELASSOCIATESMATERIAL('2DFTpVzLb0GOz94dnHBIIq',#13,$,$,(#1086),#354);

/* Column with grid placement ---------------------------------------------- */
#1149= IFCCOLUMN('1vABltQ550OwXnYj4H1WTw',#13,'CRE - 001',$,$,#1199,#1192,$,$);
#1168= IFCDIRECTION((1.,0.));
#1172= IFCCARTESIANPOINT((0.,0.));
#1176= IFCAXIS2PLACEMENT2D(#1172,#1168);
#1179= IFCRECTANGLEPROFILEDEF(.AREA.,'',#1176,300.,300.);
#1180= IFCAXIS2PLACEMENT3D(#40,#36,#28);
#1183= IFCEXTRUDEDAREASOLID(#1179,#1180,#36,2800.);
#1186= IFCSHAPEREPRESENTATION(#53,'Body','SweptSolid',(#1183));
#1192= IFCPRODUCTDEFINITIONSHAPE($,$,(#1186));
#1196= IFCVIRTUALGRIDINTERSECTION((#234,#166),(0.,0.,0.));
#1199= IFCGRIDPLACEMENT(#1196,$);
#1202= IFCRELASSOCIATESMATERIAL('2pkwqk6HjC78QH3P0jN49W',#13,$,$,(#1149),#354);

/* Column with grid placement ---------------------------------------------- */
#1212= IFCCOLUMN('3kEtX5Q8T598aWzoK0Y3VK',#13,'CRE - 001',$,$,#1262,#1255,$,$);
#1231= IFCDIRECTION((1.,0.));
#1235= IFCCARTESIANPOINT((0.,0.));
#1239= IFCAXIS2PLACEMENT2D(#1235,#1231);
#1242= IFCRECTANGLEPROFILEDEF(.AREA.,'',#1239,300.,300.);
#1243= IFCAXIS2PLACEMENT3D(#40,#36,#28);
#1246= IFCEXTRUDEDAREASOLID(#1242,#1243,#36,2800.);
#1249= IFCSHAPEREPRESENTATION(#53,'Body','SweptSolid',(#1246));
#1255= IFCPRODUCTDEFINITIONSHAPE($,$,(#1249));
#1259= IFCVIRTUALGRIDINTERSECTION((#234,#149),(0.,0.,0.));
#1262= IFCGRIDPLACEMENT(#1259,$);
#1265= IFCRELASSOCIATESMATERIAL('0jQTVYfXPFTe1hDM_MkTM2',#13,$,$,(#1212),#354);

/* Column with grid placement ---------------------------------------------- */
#1275= IFCCOLUMN('23XrrMv196EgYSv3SrHeh9',#13,'CRE - 001',$,$,#1325,#1318,$,$);
#1294= IFCDIRECTION((1.,0.));
#1298= IFCCARTESIANPOINT((0.,0.));
#1302= IFCAXIS2PLACEMENT2D(#1298,#1294);
#1305= IFCRECTANGLEPROFILEDEF(.AREA.,'',#1302,300.,300.);
#1306= IFCAXIS2PLACEMENT3D(#40,#36,#28);
#1309= IFCEXTRUDEDAREASOLID(#1305,#1306,#36,2800.);
#1312= IFCSHAPEREPRESENTATION(#53,'Body','SweptSolid',(#1309));
#1318= IFCPRODUCTDEFINITIONSHAPE($,$,(#1312));
#1322= IFCVIRTUALGRIDINTERSECTION((#234,#132),(0.,0.,0.));
#1325= IFCGRIDPLACEMENT(#1322,$);
#1328= IFCRELASSOCIATESMATERIAL('1LPl3a4iPBlOYvfIxAwQj2',#13,$,$,(#1275),#354);

/* Column with grid placement ---------------------------------------------- */
#1338= IFCCOLUMN('0eJs1Ppf94_RzyPXHgJ3IY',#13,'CRE - 001',$,$,#1388,#1381,$,$);
#1357= IFCDIRECTION((1.,0.));
#1361= IFCCARTESIANPOINT((0.,0.));
#1365= IFCAXIS2PLACEMENT2D(#1361,#1357);
#1368= IFCRECTANGLEPROFILEDEF(.AREA.,'',#1365,300.,300.);
#1369= IFCAXIS2PLACEMENT3D(#40,#36,#28);
#1372= IFCEXTRUDEDAREASOLID(#1368,#1369,#36,2800.);
#1375= IFCSHAPEREPRESENTATION(#53,'Body','SweptSolid',(#1372));
#1381= IFCPRODUCTDEFINITIONSHAPE($,$,(#1375));
#1385= IFCVIRTUALGRIDINTERSECTION((#234,#115),(0.,0.,0.));
#1388= IFCGRIDPLACEMENT(#1385,$);
#1391= IFCRELASSOCIATESMATERIAL('3sjfDIOc9F1ec0f1lD90H2',#13,$,$,(#1338),#354);

/* Column with grid placement ---------------------------------------------- */
#1401= IFCCOLUMN('1oPiECsTf5rBXznvK9MdP_',#13,'CRE - 001',$,$,#1451,#1444,$,$);
#1420= IFCDIRECTION((1.,0.));
#1424= IFCCARTESIANPOINT((0.,0.));
#1428= IFCAXIS2PLACEMENT2D(#1424,#1420);
#1431= IFCRECTANGLEPROFILEDEF(.AREA.,'',#1428,300.,300.);
#1432= IFCAXIS2PLACEMENT3D(#40,#36,#28);
#1435= IFCEXTRUDEDAREASOLID(#1431,#1432,#36,2800.);
#1438= IFCSHAPEREPRESENTATION(#53,'Body','SweptSolid',(#1435));
#1444= IFCPRODUCTDEFINITIONSHAPE($,$,(#1438));
#1448= IFCVIRTUALGRIDINTERSECTION((#217,#183),(0.,0.,0.));
#1451= IFCGRIDPLACEMENT(#1448,$);
#1454= IFCRELASSOCIATESMATERIAL('1nQn_EHJ1CAgQ3A_5kVjgu',#13,$,$,(#1401),#354);

/* Column with grid placement ---------------------------------------------- */
#1464= IFCCOLUMN('1W7_sZW7157AYdrmDlC7VM',#13,'CRE - 001',$,$,#1514,#1507,$,$);
#1483= IFCDIRECTION((1.,0.));
#1487= IFCCARTESIANPOINT((0.,0.));
#1491= IFCAXIS2PLACEMENT2D(#1487,#1483);
#1494= IFCRECTANGLEPROFILEDEF(.AREA.,'',#1491,300.,300.);
#1495= IFCAXIS2PLACEMENT3D(#40,#36,#28);
#1498= IFCEXTRUDEDAREASOLID(#1494,#1495,#36,2800.);
#1501= IFCSHAPEREPRESENTATION(#53,'Body','SweptSolid',(#1498));
#1507= IFCPRODUCTDEFINITIONSHAPE($,$,(#1501));
#1511= IFCVIRTUALGRIDINTERSECTION((#217,#166),(0.,0.,0.));
#1514= IFCGRIDPLACEMENT(#1511,$);
#1517= IFCRELASSOCIATESMATERIAL('3hdVqzwW58b8CHoK91M_e3',#13,$,$,(#1464),#354);

/* Column with grid placement ---------------------------------------------- */
#1527= IFCCOLUMN('1FN9fX7IT6q8a1vB_hVsGg',#13,'CRE - 001',$,$,#1577,#1570,$,$);
#1546= IFCDIRECTION((1.,0.));
#1550= IFCCARTESIANPOINT((0.,0.));
#1554= IFCAXIS2PLACEMENT2D(#1550,#1546);
#1557= IFCRECTANGLEPROFILEDEF(.AREA.,'',#1554,300.,300.);
#1558= IFCAXIS2PLACEMENT3D(#40,#36,#28);
#1561= IFCEXTRUDEDAREASOLID(#1557,#1558,#36,2800.);
#1564= IFCSHAPEREPRESENTATION(#53,'Body','SweptSolid',(#1561));
#1570= IFCPRODUCTDEFINITIONSHAPE($,$,(#1564));
#1574= IFCVIRTUALGRIDINTERSECTION((#217,#149),(0.,0.,0.));
#1577= IFCGRIDPLACEMENT(#1574,$);
#1580= IFCRELASSOCIATESMATERIAL('2qMelZARf5XeVVPFD8KNr_',#13,$,$,(#1527),#354);

/* Column with grid placement ---------------------------------------------- */
#1590= IFCCOLUMN('0$iPJRpPTEEPdl0kHj5EAo',#13,'CRE - 001',$,$,#1640,#1633,$,$);
#1609= IFCDIRECTION((1.,0.));
#1613= IFCCARTESIANPOINT((0.,0.));
#1617= IFCAXIS2PLACEMENT2D(#1613,#1609);
#1620= IFCRECTANGLEPROFILEDEF(.AREA.,'',#1617,300.,300.);
#1621= IFCAXIS2PLACEMENT3D(#40,#36,#28);
#1624= IFCEXTRUDEDAREASOLID(#1620,#1621,#36,2800.);
#1627= IFCSHAPEREPRESENTATION(#53,'Body','SweptSolid',(#1624));
#1633= IFCPRODUCTDEFINITIONSHAPE($,$,(#1627));
#1637= IFCVIRTUALGRIDINTERSECTION((#217,#132),(0.,0.,0.));
#1640= IFCGRIDPLACEMENT(#1637,$);
#1643= IFCRELASSOCIATESMATERIAL('1o9JnTwfr2Evj4BwWdpHfT',#13,$,$,(#1590),#354);

/* Column with grid placement ---------------------------------------------- */
#1653= IFCCOLUMN('3n$Eued0XDYv5tkLaUxJAx',#13,'CRE - 001',$,$,#1703,#1696,$,$);
#1672= IFCDIRECTION((1.,0.));
#1676= IFCCARTESIANPOINT((0.,0.));
#1680= IFCAXIS2PLACEMENT2D(#1676,#1672);
#1683= IFCRECTANGLEPROFILEDEF(.AREA.,'',#1680,300.,300.);
#1684= IFCAXIS2PLACEMENT3D(#40,#36,#28);
#1687= IFCEXTRUDEDAREASOLID(#1683,#1684,#36,2800.);
#1690= IFCSHAPEREPRESENTATION(#53,'Body','SweptSolid',(#1687));
#1696= IFCPRODUCTDEFINITIONSHAPE($,$,(#1690));
#1700= IFCVIRTUALGRIDINTERSECTION((#217,#115),(0.,0.,0.));
#1703= IFCGRIDPLACEMENT(#1700,$);
#1706= IFCRELASSOCIATESMATERIAL('3diy35i5v7ihfG8n$1pm4M',#13,$,$,(#1653),#354);

/* Column with grid placement ---------------------------------------------- */
#1716= IFCCOLUMN('3$2$CWw7T5mPXSiQPW1aw9',#13,'CRE - 001',$,$,#1766,#1759,$,$);
#1735= IFCDIRECTION((1.,0.));
#1739= IFCCARTESIANPOINT((0.,0.));
#1743= IFCAXIS2PLACEMENT2D(#1739,#1735);
#1746= IFCRECTANGLEPROFILEDEF(.AREA.,'',#1743,300.,300.);
#1747= IFCAXIS2PLACEMENT3D(#40,#36,#28);
#1750= IFCEXTRUDEDAREASOLID(#1746,#1747,#36,2800.);
#1753= IFCSHAPEREPRESENTATION(#53,'Body','SweptSolid',(#1750));
#1759= IFCPRODUCTDEFINITIONSHAPE($,$,(#1753));
#1763= IFCVIRTUALGRIDINTERSECTION((#200,#183),(0.,0.,0.));
#1766= IFCGRIDPLACEMENT(#1763,$);
#1769= IFCRELASSOCIATESMATERIAL('0AJItPgPX0NuEgejqR7P8f',#13,$,$,(#1716),#354);

/* Column with grid placement ---------------------------------------------- */
#1779= IFCCOLUMN('09ERL8h6nBRBE$$PvUJIuO',#13,'CRE - 001',$,$,#1829,#1822,$,$);
#1798= IFCDIRECTION((1.,0.));
#1802= IFCCARTESIANPOINT((0.,0.));
#1806= IFCAXIS2PLACEMENT2D(#1802,#1798);
#1809= IFCRECTANGLEPROFILEDEF(.AREA.,'',#1806,300.,300.);
#1810= IFCAXIS2PLACEMENT3D(#40,#36,#28);
#1813= IFCEXTRUDEDAREASOLID(#1809,#1810,#36,2800.);
#1816= IFCSHAPEREPRESENTATION(#53,'Body','SweptSolid',(#1813));
#1822= IFCPRODUCTDEFINITIONSHAPE($,$,(#1816));
#1826= IFCVIRTUALGRIDINTERSECTION((#200,#166),(0.,0.,0.));
#1829= IFCGRIDPLACEMENT(#1826,$);
#1832= IFCRELASSOCIATESMATERIAL('0o7iyvBu15aeZ8UMrdLbAj',#13,$,$,(#1779),#354);

/* Column with grid placement ---------------------------------------------- */
#1842= IFCCOLUMN('1ZW6qv_7PEevosrWrjUrOw',#13,'CRE - 001',$,$,#1892,#1885,$,$);
#1861= IFCDIRECTION((1.,0.));
#1865= IFCCARTESIANPOINT((0.,0.));
#1869= IFCAXIS2PLACEMENT2D(#1865,#1861);
#1872= IFCRECTANGLEPROFILEDEF(.AREA.,'',#1869,300.,300.);
#1873= IFCAXIS2PLACEMENT3D(#40,#36,#28);
#1876= IFCEXTRUDEDAREASOLID(#1872,#1873,#36,2800.);
#1879= IFCSHAPEREPRESENTATION(#53,'Body','SweptSolid',(#1876));
#1885= IFCPRODUCTDEFINITIONSHAPE($,$,(#1879));
#1889= IFCVIRTUALGRIDINTERSECTION((#200,#149),(0.,0.,0.));
#1892= IFCGRIDPLACEMENT(#1889,$);
#1895= IFCRELASSOCIATESMATERIAL('2g0UVZDhT2OA5mAph2RRwW',#13,$,$,(#1842),#354);

/* Column with grid placement ---------------------------------------------- */
#1905= IFCCOLUMN('2$cnEmlKb7RxirtiLyffW9',#13,'CRE - 001',$,$,#1955,#1948,$,$);
#1924= IFCDIRECTION((1.,0.));
#1928= IFCCARTESIANPOINT((0.,0.));
#1932= IFCAXIS2PLACEMENT2D(#1928,#1924);
#1935= IFCRECTANGLEPROFILEDEF(.AREA.,'',#1932,300.,300.);
#1936= IFCAXIS2PLACEMENT3D(#40,#36,#28);
#1939= IFCEXTRUDEDAREASOLID(#1935,#1936,#36,2800.);
#1942= IFCSHAPEREPRESENTATION(#53,'Body','SweptSolid',(#1939));
#1948= IFCPRODUCTDEFINITIONSHAPE($,$,(#1942));
#1952= IFCVIRTUALGRIDINTERSECTION((#200,#132),(0.,0.,0.));
#1955= IFCGRIDPLACEMENT(#1952,$);
#1958= IFCRELASSOCIATESMATERIAL('3fel0v29z0yfIc72Hd$FLH',#13,$,$,(#1905),#354);

/* Column with grid placement ---------------------------------------------- */
#1968= IFCCOLUMN('2haSWJcSfEAgcrJRiuVsNA',#13,'CRE - 001',$,$,#2018,#2011,$,$);
#1987= IFCDIRECTION((1.,0.));
#1991= IFCCARTESIANPOINT((0.,0.));
#1995= IFCAXIS2PLACEMENT2D(#1991,#1987);
#1998= IFCRECTANGLEPROFILEDEF(.AREA.,'',#1995,300.,300.);
#1999= IFCAXIS2PLACEMENT3D(#40,#36,#28);
#2002= IFCEXTRUDEDAREASOLID(#1998,#1999,#36,2800.);
#2005= IFCSHAPEREPRESENTATION(#53,'Body','SweptSolid',(#2002));
#2011= IFCPRODUCTDEFINITIONSHAPE($,$,(#2005));
#2015= IFCVIRTUALGRIDINTERSECTION((#200,#115),(0.,0.,0.));
#2018= IFCGRIDPLACEMENT(#2015,$);
#2021= IFCRELASSOCIATESMATERIAL('2mtSsiV6DBBBqw1_FHq9yw',#13,$,$,(#1968),#354);

/* Beam with local placement ----------------------------------------------- */
#2031= IFCBEAM('0sj55LkZn33RYYV9cNp2vs',#13,'BMR - 001',$,$,#2085,#2074,$,$);
#2050= IFCDIRECTION((1.,0.));
#2054= IFCCARTESIANPOINT((-100.,0.));
#2058= IFCAXIS2PLACEMENT2D(#2054,#2050);
#2061= IFCRECTANGLEPROFILEDEF(.AREA.,'',#2058,200.,200.);
#2062= IFCAXIS2PLACEMENT3D(#40,#32,#36);
#2065= IFCEXTRUDEDAREASOLID(#2061,#2062,#36,12000.);
#2068= IFCSHAPEREPRESENTATION(#53,'Body','SweptSolid',(#2065));
#2074= IFCPRODUCTDEFINITIONSHAPE($,$,(#2068));
#2078= IFCCARTESIANPOINT((-14000.,4000.,2800.));
#2082= IFCAXIS2PLACEMENT3D(#2078,#36,#28);
#2085= IFCLOCALPLACEMENT(#90,#2082);
#2088= IFCRELASSOCIATESMATERIAL('395DGAbFz7IRhGOTHDhBEA',#13,$,$,(#2031),#354);

/* Beam with local placement ----------------------------------------------- */
#2100= IFCBEAM('3R6gApVrPDBBjeleun$ig8',#13,'BMR - 001',$,$,#2154,#2143,$,$);
#2119= IFCDIRECTION((1.,0.));
#2123= IFCCARTESIANPOINT((-100.,0.));
#2127= IFCAXIS2PLACEMENT2D(#2123,#2119);
#2130= IFCRECTANGLEPROFILEDEF(.AREA.,'',#2127,200.,200.);
#2131= IFCAXIS2PLACEMENT3D(#40,#32,#36);
#2134= IFCEXTRUDEDAREASOLID(#2130,#2131,#36,12000.);
#2137= IFCSHAPEREPRESENTATION(#53,'Body','SweptSolid',(#2134));
#2143= IFCPRODUCTDEFINITIONSHAPE($,$,(#2137));
#2147= IFCCARTESIANPOINT((-10000.,4000.,2800.));
#2151= IFCAXIS2PLACEMENT3D(#2147,#36,#28);
#2154= IFCLOCALPLACEMENT(#90,#2151);
#2157= IFCRELASSOCIATESMATERIAL('0bQ8chmun9UOk$Z_NNlmg_',#13,$,$,(#2100),#354);

/* Beam with local placement ----------------------------------------------- */
#2167= IFCBEAM('0QMS1qce10WvjrKIO5PpTf',#13,'BMR - 001',$,$,#2221,#2210,$,$);
#2186= IFCDIRECTION((1.,0.));
#2190= IFCCARTESIANPOINT((-100.,0.));
#2194= IFCAXIS2PLACEMENT2D(#2190,#2186);
#2197= IFCRECTANGLEPROFILEDEF(.AREA.,'',#2194,200.,200.);
#2198= IFCAXIS2PLACEMENT3D(#40,#32,#36);
#2201= IFCEXTRUDEDAREASOLID(#2197,#2198,#36,12000.);
#2204= IFCSHAPEREPRESENTATION(#53,'Body','SweptSolid',(#2201));
#2210= IFCPRODUCTDEFINITIONSHAPE($,$,(#2204));
#2214= IFCCARTESIANPOINT((-6000.,4000.,2800.));
#2218= IFCAXIS2PLACEMENT3D(#2214,#36,#28);
#2221= IFCLOCALPLACEMENT(#90,#2218);
#2224= IFCRELASSOCIATESMATERIAL('2gdBfewsLA5Q6MxOyuBaJS',#13,$,$,(#2167),#354);

/* Beam with local placement ----------------------------------------------- */
#2234= IFCBEAM('2Ib4Kw7NbENgEHiTJh_Leu',#13,'BMR - 001',$,$,#2288,#2277,$,$);
#2253= IFCDIRECTION((1.,0.));
#2257= IFCCARTESIANPOINT((-100.,0.));
#2261= IFCAXIS2PLACEMENT2D(#2257,#2253);
#2264= IFCRECTANGLEPROFILEDEF(.AREA.,'',#2261,200.,200.);
#2265= IFCAXIS2PLACEMENT3D(#40,#32,#36);
#2268= IFCEXTRUDEDAREASOLID(#2264,#2265,#36,12000.);
#2271= IFCSHAPEREPRESENTATION(#53,'Body','SweptSolid',(#2268));
#2277= IFCPRODUCTDEFINITIONSHAPE($,$,(#2271));
#2281= IFCCARTESIANPOINT((-2000.,4000.,2800.));
#2285= IFCAXIS2PLACEMENT3D(#2281,#36,#28);
#2288= IFCLOCALPLACEMENT(#90,#2285);
#2291= IFCRELASSOCIATESMATERIAL('1BKlPhJQj4buntvqU28dfk',#13,$,$,(#2234),#354);

/* Beam with local placement ----------------------------------------------- */
#2301= IFCBEAM('3Hx5aT6pD1iPGYA3CCHPRS',#13,'BMR - 001',$,$,#2359,#2344,$,$);
#2320= IFCDIRECTION((1.,0.));
#2324= IFCCARTESIANPOINT((-100.,0.));
#2328= IFCAXIS2PLACEMENT2D(#2324,#2320);
#2331= IFCRECTANGLEPROFILEDEF(.AREA.,'',#2328,200.,200.);
#2332= IFCAXIS2PLACEMENT3D(#40,#32,#36);
#2335= IFCEXTRUDEDAREASOLID(#2331,#2332,#36,16000.);
#2338= IFCSHAPEREPRESENTATION(#53,'Body','SweptSolid',(#2335));
#2344= IFCPRODUCTDEFINITIONSHAPE($,$,(#2338));
#2348= IFCDIRECTION((1.9428903E-16,-1.,0.));
#2352= IFCCARTESIANPOINT((-14000.,4000.,2800.));
#2356= IFCAXIS2PLACEMENT3D(#2352,#36,#2348);
#2359= IFCLOCALPLACEMENT(#90,#2356);
#2362= IFCRELASSOCIATESMATERIAL('3aQPpPgfX3hR4JMj79gyHg',#13,$,$,(#2301),#354);

/* Beam with local placement ----------------------------------------------- */
#2372= IFCBEAM('1Eouahjef86QHNj1gS81gC',#13,'BMR - 001',$,$,#2426,#2415,$,$);
#2391= IFCDIRECTION((1.,0.));
#2395= IFCCARTESIANPOINT((-100.,0.));
#2399= IFCAXIS2PLACEMENT2D(#2395,#2391);
#2402= IFCRECTANGLEPROFILEDEF(.AREA.,'',#2399,200.,200.);
#2403= IFCAXIS2PLACEMENT3D(#40,#32,#36);
#2406= IFCEXTRUDEDAREASOLID(#2402,#2403,#36,12000.);
#2409= IFCSHAPEREPRESENTATION(#53,'Body','SweptSolid',(#2406));
#2415= IFCPRODUCTDEFINITIONSHAPE($,$,(#2409));
#2419= IFCCARTESIANPOINT((2000.,4000.,2800.));
#2423= IFCAXIS2PLACEMENT3D(#2419,#36,#28);
#2426= IFCLOCALPLACEMENT(#90,#2423);
#2429= IFCRELASSOCIATESMATERIAL('12hCI2$Af87h_yq7FqKlS_',#13,$,$,(#2372),#354);

/* Beam with local placement ----------------------------------------------- */
#2439= IFCBEAM('2inLgxVS94Te6b5ADXjH94',#13,'BMR - 001',$,$,#2497,#2482,$,$);
#2458= IFCDIRECTION((1.,0.));
#2462= IFCCARTESIANPOINT((-100.,0.));
#2466= IFCAXIS2PLACEMENT2D(#2462,#2458);
#2469= IFCRECTANGLEPROFILEDEF(.AREA.,'',#2466,200.,200.);
#2470= IFCAXIS2PLACEMENT3D(#40,#32,#36);
#2473= IFCEXTRUDEDAREASOLID(#2469,#2470,#36,16000.);
#2476= IFCSHAPEREPRESENTATION(#53,'Body','SweptSolid',(#2473));
#2482= IFCPRODUCTDEFINITIONSHAPE($,$,(#2476));
#2486= IFCDIRECTION((1.6653345E-16,-1.,0.));
#2490= IFCCARTESIANPOINT((-14000.,8000.,2800.));
#2494= IFCAXIS2PLACEMENT3D(#2490,#36,#2486);
#2497= IFCLOCALPLACEMENT(#90,#2494);
#2500= IFCRELASSOCIATESMATERIAL('3SSTTenEP0ZxzQoK27AEY3',#13,$,$,(#2439),#354);

/* Beam with local placement ----------------------------------------------- */
#2510= IFCBEAM('1v5rDYwSnB7gVTP_r2LzCU',#13,'BMR - 001',$,$,#2568,#2553,$,$);
#2529= IFCDIRECTION((1.,0.));
#2533= IFCCARTESIANPOINT((-100.,0.));
#2537= IFCAXIS2PLACEMENT2D(#2533,#2529);
#2540= IFCRECTANGLEPROFILEDEF(.AREA.,'',#2537,200.,200.);
#2541= IFCAXIS2PLACEMENT3D(#40,#32,#36);
#2544= IFCEXTRUDEDAREASOLID(#2540,#2541,#36,16000.);
#2547= IFCSHAPEREPRESENTATION(#53,'Body','SweptSolid',(#2544));
#2553= IFCPRODUCTDEFINITIONSHAPE($,$,(#2547));
#2557= IFCDIRECTION((2.2204460E-16,-1.,0.));
#2561= IFCCARTESIANPOINT((-14000.,10000.,2800.));
#2565= IFCAXIS2PLACEMENT3D(#2561,#36,#2557);
#2568= IFCLOCALPLACEMENT(#90,#2565);
#2571= IFCRELASSOCIATESMATERIAL('1X$dpCdIr83vpygtBICfMM',#13,$,$,(#2510),#354);

/* Beam with local placement ----------------------------------------------- */
#2581= IFCBEAM('0z$eamUiL5uvdZJOfKdMkc',#13,'BMR - 001',$,$,#2639,#2624,$,$);
#2600= IFCDIRECTION((1.,0.));
#2604= IFCCARTESIANPOINT((-100.,0.));
#2608= IFCAXIS2PLACEMENT2D(#2604,#2600);
#2611= IFCRECTANGLEPROFILEDEF(.AREA.,'',#2608,200.,200.);
#2612= IFCAXIS2PLACEMENT3D(#40,#32,#36);
#2615= IFCEXTRUDEDAREASOLID(#2611,#2612,#36,16000.);
#2618= IFCSHAPEREPRESENTATION(#53,'Body','SweptSolid',(#2615));
#2624= IFCPRODUCTDEFINITIONSHAPE($,$,(#2618));
#2628= IFCDIRECTION((2.2204460E-16,-1.,0.));
#2632= IFCCARTESIANPOINT((-14000.,14000.,2800.));
#2636= IFCAXIS2PLACEMENT3D(#2632,#36,#2628);
#2639= IFCLOCALPLACEMENT(#90,#2636);
#2642= IFCRELASSOCIATESMATERIAL('1lkTw9iNT77A3UD5oHSTav',#13,$,$,(#2581),#354);

/* Beam with local placement ----------------------------------------------- */
#2652= IFCBEAM('0HgX8eMdT5IAlhYvOpq09J',#13,'BMR - 001',$,$,#2710,#2695,$,$);
#2671= IFCDIRECTION((1.,0.));
#2675= IFCCARTESIANPOINT((-100.,0.));
#2679= IFCAXIS2PLACEMENT2D(#2675,#2671);
#2682= IFCRECTANGLEPROFILEDEF(.AREA.,'',#2679,200.,200.);
#2683= IFCAXIS2PLACEMENT3D(#40,#32,#36);
#2686= IFCEXTRUDEDAREASOLID(#2682,#2683,#36,16000.);
#2689= IFCSHAPEREPRESENTATION(#53,'Body','SweptSolid',(#2686));
#2695= IFCPRODUCTDEFINITIONSHAPE($,$,(#2689));
#2699= IFCDIRECTION((2.2204460E-16,-1.,0.));
#2703= IFCCARTESIANPOINT((-14000.,16000.,2800.));
#2707= IFCAXIS2PLACEMENT3D(#2703,#36,#2699);
#2710= IFCLOCALPLACEMENT(#90,#2707);
#2713= IFCRELASSOCIATESMATERIAL('0e9JZKFZHE1xGhwKv0kdNS',#13,$,$,(#2652),#354);

/* assignment to spatial structure ------------------------------------------ */
#2723= IFCRELCONTAINEDINSPATIALSTRUCTURE('16oImQoYj9_A1f$3cMD$tG',#13,'BuildingStoreyContainer','BuildingStoreyContainer for Elements',(#283, #293,#519,#582,#645,#708,#771,#834,#897,#960,#1023,#1086,#1149,#1212,#1275,#1338,#1401,#1464,#1527,#1590,#1653,#1716,#1779,#1842,#1905,#1968,#2031,#2100,#2167,#2234,#2301,#2372,#2439,#2510,#2581,#2652),#93);
#2767= IFCRELAGGREGATES('0_hC48GjH8oB6PdJvbX9vr',#13,'BuildingContainer','BuildingContainer for BuildingStories',#77,(#93));
#2771= IFCRELAGGREGATES('2a9WYlGob2OvXZ5E5NGAwK',#13,'SiteContainer','SiteContainer For Buildings',#64,(#77));
#2773= IFCRELAGGREGATES('3RXI8OG4DF5fPc9Gd_kmYm',#13,'ProjectContainer','ProjectContainer for Sites',#54,(#64));
ENDSEC;

END-ISO-10303-21;

ISO-10303-21;
HEADER;
FILE_DESCRIPTION(('ViewDefinition [notYetAssigned]'),'2;1');
FILE_NAME(
/* name */ '',
/* time_stamp */ '2014-12-09T00:27:55',
/* author */ ('Jon'),
/* organization */ ('Unknown'),
/* preprocessor_version */ 'ggIFC - Exporter by Geometry Gym Pty Ltd',
/* originating_system */ 'ggIFC - Exporter by Geometry Gym Pty Ltd',
/* authorization */ 'None');

FILE_SCHEMA (('IFC4'));
ENDSEC;

DATA;
#1= IFCAPPLICATION(#2,'*******','ggRhinoIFC - Geometry Gym Plug-in for Rhino3d','ggRhinoIFC');
#2= IFCORGANIZATION($,'Geometry Gym Pty Ltd',$,$,$);
#3= IFCPERSONANDORGANIZATION(#4,#5,$);
#4= IFCPERSON('Jon','Jon',$,$,$,$,$,$);
#5= IFCORGANIZATION($,'Geometry Gym Pty Ltd',$,$,$);
#6= IFCOWNERHISTORY(#3,#1,$,.ADDED.,1418084875,$,$,1418084875);
#7= IFCGEOMETRICREPRESENTATIONCONTEXT($,'Model',3,0.0001,#8,#10);
#8= IFCAXIS2PLACEMENT3D(#9,$,$);
#9= IFCCARTESIANPOINT((0.0,0.0,0.0));
#10= IFCDIRECTION((0.0,1.0));
#11= IFCGEOMETRICREPRESENTATIONSUBCONTEXT('Axis','Model',*,*,*,*,#7,$,.MODEL_VIEW.,$);
#12= IFCGEOMETRICREPRESENTATIONSUBCONTEXT('Body','Model',*,*,*,*,#7,$,.MODEL_VIEW.,$);
#13= IFCGEOMETRICREPRESENTATIONCONTEXT($,'Model',3,0.0001,#8,#14);
#14= IFCDIRECTION((0.0,1.0));
#50= IFCBUILDING('0WoLgLOcf80OfpMfE5JDJy',$,'IfcBuilding',$,$,#51,$,$,.ELEMENT.,$,$,#57);
#51= IFCLOCALPLACEMENT($,#52);
#52= IFCAXIS2PLACEMENT3D(#53,$,$);
#53= IFCCARTESIANPOINT((0.0,0.0,0.0));
#54= IFCRELCONTAINEDINSPATIALSTRUCTURE('1UK80jTi5Ff9jgVJzyecRb',$,'Building','Building Container for Elements',(#220),#50);
#55= IFCLOCALPLACEMENT(#51,#52);
#57= IFCPOSTALADDRESS($,$,$,$,$,$,$,'Unknown',$,$);
#100= IFCPROJECT('1BOGo$cBv9ePqXvg1A7XlA',#6,'IfcProject',$,$,'IfcProject','',(#13),#101);
#101= IFCUNITASSIGNMENT((#102,#103,#104));
#102= IFCSIUNIT(*,.LENGTHUNIT.,.MILLI.,.METRE.);
#103= IFCSIUNIT(*,.PLANEANGLEUNIT.,$,.RADIAN.);
#104= IFCSIUNIT(*,.TIMEUNIT.,$,.SECOND.);
#105= IFCRELAGGREGATES('27SF9JXCv0IfCGc6IOUgDJ',$,'Project Container','Project Container for Buildings',#100,(#50));
#200= IFCDOCUMENTREFERENCE($,'MyReinforcementCode','MyCodeISO3766',$,$);
#201= IFCRELASSOCIATESDOCUMENT('23vj62OUfDfg63sHB1nX6x',$,$,$,(#100),#200);
#202= IFCMATERIAL('ReinforcingSteel',$,$);
#203= IFCRELASSOCIATESMATERIAL('3asHesm0T6gxQ0aGbrsMIX',$,'MatAssoc','Material Associates',(#212),#202);
#205= IFCINDEXEDPOLYCURVE(#206,(IFCLINEINDEX((1,2)),IFCARCINDEX((2,3,4)),IFCLINEINDEX((4,5)),IFCARCINDEX((5,6,7)),IFCLINEINDEX((7,8)),IFCARCINDEX((8,9,10)),IFCLINEINDEX((10,11)),IFCARCINDEX((11,12,13)),IFCLINEINDEX((13,14)),IFCARCINDEX((14,15,16)),IFCLINEINDEX((16,17)),IFCARCINDEX((17,18,19)),IFCLINEINDEX((19,20))),$);
#206= IFCCARTESIANPOINTLIST3D(((-69.0,0.0,-122.0),(-69.0,0.0,-79.0),(-54.9411254969544,0.0,-45.0588745030457),(-21.0000000000001,0.0,-31.0),(21.0,0.0,-31.0),(54.9411254969543,0.0,-45.0588745030456),(69.0,0.0,-78.9999999999999),(69.0,0.00000000000000089,-321.0),(54.993978595716,1.21791490472038,-354.941125496954),(21.1804517666064,4.1582215855126,-369.0),(-20.6616529376114,7.79666547283599,-369.0),(-54.4751797667207,10.7369721536282,-354.941125496954),(-68.4812011710042,11.9548870583485,-320.999999999999),(-69.0,12.0,-79.0),(-54.9411254969544,12.0,-45.0588745030457),(-21.0000000000001,12.0,-31.0),(21.0,12.0,-31.0),(54.9411254969543,12.0,-45.0588745030456),(69.0,12.0,-78.9999999999999),(-69.0,0.0,-122.0)));
#207= IFCSWEPTDISKSOLID(#205,6.0,$,$,$);
#208= IFCREPRESENTATIONMAP(#209,#211);
#209= IFCAXIS2PLACEMENT3D(#210,$,$);
#210= IFCCARTESIANPOINT((0.0,0.0,0.0));
#211= IFCSHAPEREPRESENTATION(#12,'Body','AdvancedSweptSolid',(#207));
#212= IFCREINFORCINGBARTYPE('3dAMwbGE92VuH_d2LLFmoy',$,'12 Diameter Ligature',$,$,$,(#208),$,$,.LIGATURE.,12.0,113.097335529233,1150.0,.TEXTURED.,$,$);
#213= IFCRELDEFINESBYTYPE('0KX7K6Ekv7YvL4uzxaJvJ8',$,'12 Diameter Ligature',$,(#220),#212);
#214= IFCDIRECTION((1.0,0.0,0.0));
#215= IFCDIRECTION((0.0,1.0,0.0));
#216= IFCCARTESIANPOINT((0.0,0.0,0.0));
#217= IFCCARTESIANTRANSFORMATIONOPERATOR3D(#214,#215,#216,1.0,#218);
#218= IFCDIRECTION((0.0,0.0,1.0));
#219= IFCMAPPEDITEM(#208,#217);
#220= IFCREINFORCINGBAR('381QpY9RP89gKaSFbzIfbP',$,$,$,$,#55,#221,$,$,$,$,$,$,$);
#221= IFCPRODUCTDEFINITIONSHAPE($,$,(#222));
#222= IFCSHAPEREPRESENTATION(#12,'Body','MappedRepresentation',(#219));
ENDSEC;

END-ISO-10303-21;


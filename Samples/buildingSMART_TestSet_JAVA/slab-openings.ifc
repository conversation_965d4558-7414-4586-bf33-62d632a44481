ISO-10303-21;
HEADER;
FILE_DESCRIPTION(('ViewDefinition [notYetAssigned]'),'2;1');
FILE_NAME(
/* name */ '',
/* time_stamp */ '2014-12-09T00:27:54',
/* author */ ('Jon'),
/* organization */ ('Unknown'),
/* preprocessor_version */ 'ggIFC - Exporter by Geometry Gym Pty Ltd',
/* originating_system */ 'ggIFC - Exporter by Geometry Gym Pty Ltd',
/* authorization */ 'None');

FILE_SCHEMA (('IFC4'));
ENDSEC;

DATA;
#1= IFCAPPLICATION(#2,'*******','ggRhinoIFC - Geometry Gym Plug-in for Rhino3d','ggRhinoIFC');
#2= IFCORGANIZATION($,'Geometry Gym Pty Ltd',$,$,$);
#3= IFCPERSONANDORGANIZATION(#4,#5,$);
#4= IFCPERSON('Jon','Jon',$,$,$,$,$,$);
#5= IFCORGANIZATION($,'Geometry Gym Pty Ltd',$,$,$);
#6= IFCOWNERHISTORY(#3,#1,$,.ADDED.,1418084874,$,$,1418084874);
#7= IFCGEOMETRICREPRESENTATIONCONTEXT($,'Model',3,0.0001,#8,#10);
#8= IFCAXIS2PLACEMENT3D(#9,$,$);
#9= IFCCARTESIANPOINT((0.0,0.0,0.0));
#10= IFCDIRECTION((0.0,1.0));
#11= IFCGEOMETRICREPRESENTATIONSUBCONTEXT('Axis','Model',*,*,*,*,#7,$,.MODEL_VIEW.,$);
#12= IFCGEOMETRICREPRESENTATIONSUBCONTEXT('Body','Model',*,*,*,*,#7,$,.MODEL_VIEW.,$);
#13= IFCGEOMETRICREPRESENTATIONCONTEXT($,'Model',3,0.0001,#8,#14);
#14= IFCDIRECTION((0.0,1.0));
#50= IFCBUILDING('0VCRxBtkXADAyUqmmi8wgL',$,'IfcBuilding',$,$,#51,$,$,.ELEMENT.,$,$,#57);
#51= IFCLOCALPLACEMENT($,#52);
#52= IFCAXIS2PLACEMENT3D(#53,$,$);
#53= IFCCARTESIANPOINT((0.0,0.0,0.0));
#54= IFCRELCONTAINEDINSPATIALSTRUCTURE('1LjW27S1r6k9t60_o5PKpn',$,'Building','Building Container for Elements',(#302),#50);
#57= IFCPOSTALADDRESS($,$,$,$,$,$,$,'Unknown',$,$);
#100= IFCPROJECT('25v_KTp7PE7xsqL0jcdJ0K',#6,'IfcProject',$,$,'IfcProject','',(#13),#101);
#101= IFCUNITASSIGNMENT((#102,#103,#104));
#102= IFCSIUNIT(*,.LENGTHUNIT.,.MILLI.,.METRE.);
#103= IFCSIUNIT(*,.PLANEANGLEUNIT.,$,.RADIAN.);
#104= IFCSIUNIT(*,.TIMEUNIT.,$,.SECOND.);
#105= IFCRELAGGREGATES('0xtt_FAs1E0f27x$DsDUKo',$,'Project Container','Project Container for Buildings',#100,(#50));
#200= IFCMATERIAL('Concrete',$,$);
#203= IFCMATERIALLAYER(#200,200.0,.F.,'Core',$,$,$);
#205= IFCMATERIALLAYERSET((#203),'200mm Concrete',$);
#206= IFCRELASSOCIATESMATERIAL('2mG3GPofn9w8XVcm9bJ52u',$,'MatAssoc','Material Associates',(#300),#205);
#300= IFCSLABTYPE('1EDjpMzcT3JAl5OwRhbtOd',$,'200mm Concrete',$,$,$,$,$,$,.FLOOR.);
#301= IFCRELDEFINESBYTYPE('2tnpQwgGjCVwF6buy_NKcq',$,'200mm Concrete',$,(#302),#300);
#302= IFCSLABSTANDARDCASE('21hyH1VvT7FO4OaH6TIJak',$,$,$,$,#307,#316,$,$);
#303= IFCAXIS2PLACEMENT3D(#304,#305,#306);
#304= IFCCARTESIANPOINT((0.0,0.0,-200.0));
#305= IFCDIRECTION((0.0,0.0,1.0));
#306= IFCDIRECTION((1.0,0.0,0.0));
#307= IFCLOCALPLACEMENT($,#317);
#308= IFCINDEXEDPOLYCURVE(#309,(IFCLINEINDEX((1,2)),IFCARCINDEX((2,3,4)),IFCLINEINDEX((4,5)),IFCARCINDEX((5,6,7))),.F.);
#309= IFCCARTESIANPOINTLIST2D(((0.0,0.0),(1000.0,0.0),(1400.0,2000.0),(1000.0,4000.0),(0.0,4000.0),(-400.0,2000.0),(0.0,0.0)));
#310= IFCARBITRARYCLOSEDPROFILEDEF(.AREA.,'Slab Perimeter',#308);
#311= IFCMATERIALLAYERSETUSAGE(#205,.AXIS3.,.POSITIVE.,-200.0,$);
#312= IFCRELASSOCIATESMATERIAL('0ChRP8kzb8SAliQ69Ds9or',$,'MatAssoc','Material Associates',(#302),#311);
#313= IFCDIRECTION((0.0,0.0,1.0));
#314= IFCEXTRUDEDAREASOLID(#310,$,#313,200.0);
#315= IFCSHAPEREPRESENTATION(#12,'Body','SweptSolid',(#314));
#316= IFCPRODUCTDEFINITIONSHAPE($,$,(#315));
#317= IFCAXIS2PLACEMENT3D(#318,#319,#320);
#318= IFCCARTESIANPOINT((0.0,0.0,-200.0));
#319= IFCDIRECTION((0.0,0.0,1.0));
#320= IFCDIRECTION((1.0,0.0,0.0));
#321= IFCCIRCLEPROFILEDEF(.AREA.,'100DIA',$,50.0);
#322= IFCAXIS2PLACEMENT3D(#323,#324,#325);
#323= IFCCARTESIANPOINT((100.0,300.0,-200.0));
#324= IFCDIRECTION((0.0,0.0,1.0));
#325= IFCDIRECTION((1.0,0.0,0.0));
#326= IFCDIRECTION((0.0,0.0,1.0));
#327= IFCEXTRUDEDAREASOLID(#321,#322,#326,200.0);
#328= IFCOPENINGSTANDARDCASE('1WQ6dDOJ5AMB88HOUHiWD1',$,'Opening',$,$,#331,#329,$,.OPENING.);
#329= IFCPRODUCTDEFINITIONSHAPE($,$,(#330));
#330= IFCSHAPEREPRESENTATION(#12,'Body','SweptSolid',(#327));
#331= IFCLOCALPLACEMENT($,#52);
#332= IFCRELVOIDSELEMENT('23zFgTv0j0rh6X$0Qe8oaV',$,$,$,#302,#328);
#333= IFCRECTANGLEPROFILEDEF(.AREA.,'RecessRectangle',$,1000.0,500.0);
#334= IFCAXIS2PLACEMENT3D(#335,#336,#337);
#335= IFCCARTESIANPOINT((500.0,1000.0,-50.0));
#336= IFCDIRECTION((0.0,0.0,1.0));
#337= IFCDIRECTION((1.0,0.0,0.0));
#338= IFCDIRECTION((0.0,0.0,1.0));
#339= IFCEXTRUDEDAREASOLID(#333,#334,#338,50.0);
#340= IFCOPENINGSTANDARDCASE('29xhFZFR94UAIjYUaRULkc',$,'Recess',$,$,#343,#341,$,.RECESS.);
#341= IFCPRODUCTDEFINITIONSHAPE($,$,(#342));
#342= IFCSHAPEREPRESENTATION(#12,'Body','SweptSolid',(#339));
#343= IFCLOCALPLACEMENT($,#52);
#344= IFCRELVOIDSELEMENT('0HzTSTYerFFPCOvIO24epW',$,$,$,#302,#340);
ENDSEC;

END-ISO-10303-21;


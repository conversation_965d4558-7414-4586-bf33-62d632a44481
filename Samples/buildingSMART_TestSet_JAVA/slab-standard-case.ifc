ISO-10303-21;
HEADER;
FILE_DESCRIPTION(('ViewDefinition [notYetAssigned]'),'2;1');
FILE_NAME(
/* name */ '',
/* time_stamp */ '2014-12-09T00:27:54',
/* author */ ('Jon'),
/* organization */ ('Unknown'),
/* preprocessor_version */ 'ggIFC - Exporter by Geometry Gym Pty Ltd',
/* originating_system */ 'ggIFC - Exporter by Geometry Gym Pty Ltd',
/* authorization */ 'None');

FILE_SCHEMA (('IFC4'));
ENDSEC;

DATA;
#1= IFCAPPLICATION(#2,'*******','ggRhinoIFC - Geometry Gym Plug-in for Rhino3d','ggRhinoIFC');
#2= IFCORGANIZATION($,'Geometry Gym Pty Ltd',$,$,$);
#3= IFCPERSONANDORGANIZATION(#4,#5,$);
#4= IFCPERSON('Jon','Jon',$,$,$,$,$,$);
#5= IFCORGANIZATION($,'Geometry Gym Pty Ltd',$,$,$);
#6= IFCOWNERHISTORY(#3,#1,$,.ADDED.,1418084874,$,$,1418084874);
#7= IFCGEOMETRICREPRESENTATIONCONTEXT($,'Model',3,0.0001,#8,#10);
#8= IFCAXIS2PLACEMENT3D(#9,$,$);
#9= IFCCARTESIANPOINT((0.0,0.0,0.0));
#10= IFCDIRECTION((0.0,1.0));
#11= IFCGEOMETRICREPRESENTATIONSUBCONTEXT('Axis','Model',*,*,*,*,#7,$,.MODEL_VIEW.,$);
#12= IFCGEOMETRICREPRESENTATIONSUBCONTEXT('Body','Model',*,*,*,*,#7,$,.MODEL_VIEW.,$);
#13= IFCGEOMETRICREPRESENTATIONCONTEXT($,'Model',3,0.0001,#8,#14);
#14= IFCDIRECTION((0.0,1.0));
#50= IFCBUILDING('385$tKiSf6rQEPOC3cePN3',$,'IfcBuilding',$,$,#51,$,$,.ELEMENT.,$,$,#57);
#51= IFCLOCALPLACEMENT($,#52);
#52= IFCAXIS2PLACEMENT3D(#53,$,$);
#53= IFCCARTESIANPOINT((0.0,0.0,0.0));
#54= IFCRELCONTAINEDINSPATIALSTRUCTURE('3BarjS4FL9geN9Y48GlNbV',$,'Building','Building Container for Elements',(#302),#50);
#57= IFCPOSTALADDRESS($,$,$,$,$,$,$,'Unknown',$,$);
#100= IFCPROJECT('3IU0sKDyX2mAWXvsGInP94',#6,'IfcProject',$,$,'IfcProject','',(#13),#101);
#101= IFCUNITASSIGNMENT((#102,#103,#104));
#102= IFCSIUNIT(*,.LENGTHUNIT.,.MILLI.,.METRE.);
#103= IFCSIUNIT(*,.PLANEANGLEUNIT.,$,.RADIAN.);
#104= IFCSIUNIT(*,.TIMEUNIT.,$,.SECOND.);
#105= IFCRELAGGREGATES('3lG7RzYM5Axu8hxBvgUmsp',$,'Project Container','Project Container for Buildings',#100,(#50));
#200= IFCMATERIAL('Concrete',$,$);
#203= IFCMATERIALLAYER(#200,200.0,.F.,'Core',$,$,$);
#205= IFCMATERIALLAYERSET((#203),'200mm Concrete',$);
#206= IFCRELASSOCIATESMATERIAL('3T98a4jwDANuzfZGb2gESH',$,'MatAssoc','Material Associates',(#300),#205);
#300= IFCSLABTYPE('1rr$WxzZr8s9JAhFh8EyGd',$,'200mm Concrete',$,$,$,$,$,$,.FLOOR.);
#301= IFCRELDEFINESBYTYPE('3JEKUx4VTBnwf0YrnD0Z5E',$,'200mm Concrete',$,(#302),#300);
#302= IFCSLABSTANDARDCASE('1uKP3mPKPDSxR9_M8BpQJz',$,$,$,$,#307,#316,$,$);
#303= IFCAXIS2PLACEMENT3D(#304,#305,#306);
#304= IFCCARTESIANPOINT((0.0,0.0,-200.0));
#305= IFCDIRECTION((0.0,0.0,1.0));
#306= IFCDIRECTION((1.0,0.0,0.0));
#307= IFCLOCALPLACEMENT($,#317);
#308= IFCINDEXEDPOLYCURVE(#309,(IFCLINEINDEX((1,2)),IFCARCINDEX((2,3,4)),IFCLINEINDEX((4,5)),IFCARCINDEX((5,6,7))),.F.);
#309= IFCCARTESIANPOINTLIST2D(((0.0,0.0),(1000.0,0.0),(1399.99999999987,2000.0),(1000.0,4000.0),(0.0,4000.0),(-400.0,2000.0),(0.0,0.0)));
#310= IFCARBITRARYCLOSEDPROFILEDEF(.AREA.,'Slab Perim',#308);
#311= IFCMATERIALLAYERSETUSAGE(#205,.AXIS3.,.POSITIVE.,-200.0,$);
#312= IFCRELASSOCIATESMATERIAL('33b_pm1lv2HhvpkPXAXPdW',$,'MatAssoc','Material Associates',(#302),#311);
#313= IFCDIRECTION((0.0,0.0,1.0));
#314= IFCEXTRUDEDAREASOLID(#310,$,#313,200.0);
#315= IFCSHAPEREPRESENTATION(#12,'Body','SweptSolid',(#314));
#316= IFCPRODUCTDEFINITIONSHAPE($,$,(#315));
#317= IFCAXIS2PLACEMENT3D(#318,#319,#320);
#318= IFCCARTESIANPOINT((0.0,0.0,-200.0));
#319= IFCDIRECTION((0.0,0.0,1.0));
#320= IFCDIRECTION((1.0,0.0,0.0));
ENDSEC;

END-ISO-10303-21;


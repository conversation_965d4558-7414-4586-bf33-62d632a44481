ISO-10303-21;
HEADER;
FILE_DESCRIPTION($,'2;1');
FILE_NAME('IfcStructuralCurveMember01.ifc','2011-11-07T14:57:06',(''),(''),'Constructivity 0.9.1','Constructivity 0.9.1','');
FILE_SCHEMA(('IFC4'));
ENDSEC;

DATA;

/* Owner history user and application */
#1= IFCAPPLICATION(#2,'0.8','Constructivity','CONSTRUCTIVITY');
#2= IFCORGANIZATION($,'Constructivity.com LLC',$,$,$);
#3= IFCPERSON('Tim',$,$,$,$,$,$,$);
#4= IFCORGANIZATION($,'Tim-PC',$,$,$);
#5= IFCPERSONANDORGANIZATION(#3,#4,$);

/* Units */
#9= IFCSIUNIT(*,.AREAUNIT.,$,.SQUARE_METRE.);
#10= IFCMEASUREWITHUNIT(IFCAREAMEASURE(0.0006452),#9);
#11= IFCDIMENSIONALEXPONENTS(2,0,0,0,0,0,0);
#12= IFCCONVERSIONBASEDUNIT(#11,.AREAUNIT.,'square inch',#10);
#21= IFCSIUNIT(*,.FORCEUNIT.,$,.NEWTON.);
#22= IFCMEASUREWITHUNIT(IFCMASSMEASURE(4.44822162),#21);
#23= IFCDIMENSIONALEXPONENTS(1,1,-2,0,0,0,0);
#24= IFCCONVERSIONBASEDUNIT(#23,.FORCEUNIT.,'pound-force',#22);
#28= IFCSIUNIT(*,.LENGTHUNIT.,$,.METRE.);
#29= IFCMEASUREWITHUNIT(IFCLENGTHMEASURE(0.0254),#28);
#30= IFCDIMENSIONALEXPONENTS(1,0,0,0,0,0,0);
#31= IFCCONVERSIONBASEDUNIT(#30,.LENGTHUNIT.,'inch',#29);
#36= IFCSIUNIT(*,.MASSUNIT.,.KILO.,.GRAM.);
#37= IFCMEASUREWITHUNIT(IFCMASSMEASURE(0.45359237),#36);
#38= IFCDIMENSIONALEXPONENTS(0,1,0,0,0,0,0);
#39= IFCCONVERSIONBASEDUNIT(#38,.MASSUNIT.,'pound',#37);
#40= IFCSIUNIT(*,.PLANEANGLEUNIT.,$,.RADIAN.);
#41= IFCMEASUREWITHUNIT(IFCPLANEANGLEMEASURE(0.0174532925199433),#40);
#42= IFCDIMENSIONALEXPONENTS(0,0,0,0,0,0,0);
#43= IFCCONVERSIONBASEDUNIT(#42,.PLANEANGLEUNIT.,'degree',#41);
#45= IFCSIUNIT(*,.PRESSUREUNIT.,$,.PASCAL.);
#46= IFCMEASUREWITHUNIT(IFCPRESSUREMEASURE(6894.7572932),#45);
#47= IFCDIMENSIONALEXPONENTS(-1,1,-2,0,0,0,0);
#48= IFCCONVERSIONBASEDUNIT(#47,.PRESSUREUNIT.,'pound-force per square inch',#46);
#56= IFCSIUNIT(*,.VOLUMEUNIT.,$,.CUBIC_METRE.);
#57= IFCMEASUREWITHUNIT(IFCVOLUMEMEASURE(1.639E-05),#56);
#58= IFCDIMENSIONALEXPONENTS(3,0,0,0,0,0,0);
#59= IFCCONVERSIONBASEDUNIT(#58,.VOLUMEUNIT.,'cubic inch',#57);
#96= IFCDERIVEDUNITELEMENT(#24,1);
#97= IFCDERIVEDUNITELEMENT(#31,-1);
#98= IFCDERIVEDUNIT((#96,#97),.LINEARFORCEUNIT.,$);
#99= IFCDERIVEDUNITELEMENT(#24,1);
#100= IFCDERIVEDUNITELEMENT(#31,1);
#101= IFCDERIVEDUNITELEMENT(#31,-1);
#102= IFCDERIVEDUNIT((#99,#100,#101),.LINEARMOMENTUNIT.,$);
#103= IFCDERIVEDUNITELEMENT(#24,1);
#104= IFCDERIVEDUNITELEMENT(#31,-1);
#105= IFCDERIVEDUNIT((#103,#104),.LINEARSTIFFNESSUNIT.,$);
#112= IFCDERIVEDUNITELEMENT(#39,1);
#113= IFCDERIVEDUNITELEMENT(#59,-1);
#114= IFCDERIVEDUNIT((#112,#113),.MASSDENSITYUNIT.,$);
#118= IFCDERIVEDUNITELEMENT(#39,1);
#119= IFCDERIVEDUNITELEMENT(#31,-1);
#120= IFCDERIVEDUNIT((#118,#119),.MASSPERLENGTHUNIT.,$);
#121= IFCDERIVEDUNITELEMENT(#48,1);
#122= IFCDERIVEDUNIT((#121),.MODULUSOFELASTICITYUNIT.,$);
#140= IFCDERIVEDUNITELEMENT(#31,4);
#141= IFCDERIVEDUNIT((#140),.MOMENTOFINERTIAUNIT.,$);
#142= IFCDERIVEDUNITELEMENT(#24,1);
#143= IFCDERIVEDUNITELEMENT(#12,-1);
#144= IFCDERIVEDUNIT((#142,#143),.PLANARFORCEUNIT.,$);
#147= IFCDERIVEDUNITELEMENT(#39,1);
#148= IFCDERIVEDUNITELEMENT(#12,-1);
#149= IFCDERIVEDUNIT((#147,#148),.ROTATIONALMASSUNIT.,$);
#150= IFCDERIVEDUNITELEMENT(#24,1);
#151= IFCDERIVEDUNITELEMENT(#31,1);
#152= IFCDERIVEDUNITELEMENT(#43,-1);
#153= IFCDERIVEDUNIT((#150,#151,#152),.ROTATIONALSTIFFNESSUNIT.,$);
#154= IFCDERIVEDUNITELEMENT(#31,5);
#155= IFCDERIVEDUNIT((#154),.SECTIONAREAINTEGRALUNIT.,$);
#156= IFCDERIVEDUNITELEMENT(#31,3);
#157= IFCDERIVEDUNIT((#156),.SECTIONMODULUSUNIT.,$);
#158= IFCDERIVEDUNITELEMENT(#48,1);
#159= IFCDERIVEDUNIT((#158),.SHEARMODULUSUNIT.,$);
#207= IFCUNITASSIGNMENT((#12,#24,#31,#39,#43,#48,#59,#98,#102,#105,#114,#120,#122,#141,#144,#149,#153,#155,#157,#159));

/* The project */
#208= IFCPROJECT('0QjBRF7yLCTh4HRF3GOF1t',#209,'Project',$,$,$,$,(#212,#215),#207);

/* Owner history referenced by all objects */
#209= IFCOWNERHISTORY(#5,#1,.READWRITE.,.NOCHANGE.,$,$,$,1320677205);

/* Project coordinate system */
#210= IFCCARTESIANPOINT((0.,0.,0.));
#211= IFCAXIS2PLACEMENT3D(#210,$,$);
#212= IFCGEOMETRICREPRESENTATIONCONTEXT('3D','Model',3,0.00001,#211,$);
#213= IFCCARTESIANPOINT((0.,0.,0.));
#214= IFCAXIS2PLACEMENT3D(#213,$,$);
#215= IFCGEOMETRICREPRESENTATIONCONTEXT('2D','Plan',2,0.00001,#214,$);

/* Structural analysis model */
#216= IFCSTRUCTURALANALYSISMODEL('0VYesmxUHFNez26MoJx5F3',#209,'Structural Analysis #1',$,$,.NOTDEFINED.,#219,(#312),(#2729),#220);
#218= IFCCARTESIANPOINT((0.,0.,0.));
#219= IFCAXIS2PLACEMENT3D(#218,$,$);
#220= IFCLOCALPLACEMENT($,#222);
#221= IFCCARTESIANPOINT((0.,0.,0.));
#222= IFCAXIS2PLACEMENT3D(#221,$,$);

/* Analysis model is declared on project; this distinguishes top-level analysis models from potential aggregated sub-models */
#224= IFCRELDECLARES('1Frhk31Z12CR9mBbodz9XE',#209,'GROUP',$,#208,(#216));

/* The structural member for the left column (object) */
#228= IFCSTRUCTURALCURVEMEMBER('3eXlZ8csrAvfIIXVwC_gVP',#209,'Curve Member #1',$,$,$,#255,.RIGID_JOINED_MEMBER.,#230);
#230= IFCDIRECTION((1.,0.,0.));

/* The structural node underneath the left column */
#232= IFCCARTESIANPOINT((0.,0.,0.));
#233= IFCVERTEXPOINT(#232);
#234= IFCTOPOLOGYREPRESENTATION(#212,'Reference','Vertex',(#233));
#235= IFCPRODUCTDEFINITIONSHAPE($,$,(#234));
#236= IFCSTRUCTURALPOINTCONNECTION('3539fAVu96i8mFr0cgUqeI',#209,'Point Connection #1',$,$,$,#235,#242,$);

/* assignment of connections and members to the analysis model */
#239= IFCRELASSIGNSTOGROUP('3mDDAcu390$A5orhhUv4qv',#209,$,$,(#236,#247,#228,#271,#280,#263,#296),.PRODUCT.,#216);

/* fixed restraint at node underneath left column */
#242= IFCBOUNDARYNODECONDITION('Fixed',IFCBOOLEAN(.T.),IFCBOOLEAN(.T.),IFCBOOLEAN(.T.),IFCBOOLEAN(.T.),IFCBOOLEAN(.T.),IFCBOOLEAN(.T.));

/* The structural node above the left column */
#243= IFCCARTESIANPOINT((0.,0.,120.));
#244= IFCVERTEXPOINT(#243);
#245= IFCTOPOLOGYREPRESENTATION(#212,'Reference','Vertex',(#244));
#246= IFCPRODUCTDEFINITIONSHAPE($,$,(#245));
#247= IFCSTRUCTURALPOINTCONNECTION('2mc6ibF258HPIpTmqg6DSl',#209,'Point Connection #2',$,$,$,#246,$,$);

/* The structural member for the left column (representation) */
#252= IFCEDGE(#233,#244);
#253= IFCTOPOLOGYREPRESENTATION(#212,'Reference','Edge',(#252));
#255= IFCPRODUCTDEFINITIONSHAPE($,$,(#253));
#258= IFCRELCONNECTSSTRUCTURALMEMBER('2Z9w70JuDEUx6TnggLE2wU',#209,$,$,#228,#236,$,$,$,$);
#260= IFCRELCONNECTSSTRUCTURALMEMBER('1GClK7cwT80xzpZuaAlGXp',#209,$,$,#228,#247,$,$,$,$);

/* The structural member for the right column (object) */
#263= IFCSTRUCTURALCURVEMEMBER('3jULd7ui93JOXl5trkpgTT',#209,'Curve Member #2',$,$,$,#288,.RIGID_JOINED_MEMBER.,#265);
#265= IFCDIRECTION((1.,0.,0.));

/* The structural node underneath the right column */
#267= IFCCARTESIANPOINT((192.,0.,0.));
#268= IFCVERTEXPOINT(#267);
#269= IFCTOPOLOGYREPRESENTATION(#212,'Reference','Vertex',(#268));
#270= IFCPRODUCTDEFINITIONSHAPE($,$,(#269));
#271= IFCSTRUCTURALPOINTCONNECTION('1dqi3aUQP3yeww5muaF15h',#209,'Point Connection #3',$,$,$,#270,#275,$);
#275= IFCBOUNDARYNODECONDITION('Fixed',IFCBOOLEAN(.T.),IFCBOOLEAN(.T.),IFCBOOLEAN(.T.),IFCBOOLEAN(.T.),IFCBOOLEAN(.T.),IFCBOOLEAN(.T.));

/* The structural node connecting the right column to the beam */
#276= IFCCARTESIANPOINT((192.,0.,120.));
#277= IFCVERTEXPOINT(#276);
#278= IFCTOPOLOGYREPRESENTATION(#212,'Reference','Vertex',(#277));
#279= IFCPRODUCTDEFINITIONSHAPE($,$,(#278));
#280= IFCSTRUCTURALPOINTCONNECTION('0IHrRf6abAZwDys7n7fbS2',#209,'Point Connection #4',$,$,$,#279,$,$);

/* The structural member for the right column (representation) */
#285= IFCEDGE(#268,#277);
#286= IFCTOPOLOGYREPRESENTATION(#212,'Reference','Edge',(#285));
#288= IFCPRODUCTDEFINITIONSHAPE($,$,(#286));
#291= IFCRELCONNECTSSTRUCTURALMEMBER('0r1xJykBf1OOYUn7dRt3DM',#209,$,$,#263,#271,$,$,$,$);
#293= IFCRELCONNECTSSTRUCTURALMEMBER('1$D3QsVBj2kf4iUp5hUEu2',#209,$,$,#263,#280,$,$,$,$);

/* The structural member for the beam */
#296= IFCSTRUCTURALCURVEMEMBER('25vEW7EzrBTvz5cbNWzhP$',#209,'Curve Member #3',$,$,$,#304,.RIGID_JOINED_MEMBER.,#298);
#298= IFCDIRECTION((0.,0.,1.));
#301= IFCEDGE(#244,#277);
#302= IFCTOPOLOGYREPRESENTATION(#212,'Reference','Edge',(#301));
#304= IFCPRODUCTDEFINITIONSHAPE($,$,(#302));
#307= IFCRELCONNECTSSTRUCTURALMEMBER('3ZUyJTZMHEev9njAeNDQUT',#209,$,$,#296,#247,$,$,$,$);
#309= IFCRELCONNECTSSTRUCTURALMEMBER('3Y3WZZzV16XQ$1wEZLWjJX',#209,$,$,#296,#280,$,$,$,$);

/* single load case */
#312= IFCSTRUCTURALLOADCASE('2fv4DZfY55exwX8QDy8dmw',#209,'Structural Load Case #1',$,$,.LOAD_CASE.,.NOTDEFINED.,.NOTDEFINED.,1.,$,(0.,0.,0.));

/* linear load along left half of beam */
#317= IFCSTRUCTURALCURVEACTION('2WSwGyLsrFNA9TLOq_ifyd',#209,'Structural Curve Action #1',$,$,$,$,#326,.GLOBAL_COORDS.,.F.,$,.LINEAR.);
#326= IFCSTRUCTURALLOADCONFIGURATION($,(#327,#329),((96.),(192.)));
#327= IFCSTRUCTURALLOADLINEARFORCE('Nominal',$,$,-100.,$,$,$);
#329= IFCSTRUCTURALLOADLINEARFORCE('Nominal',$,$,-100.,$,$,$);
#335= IFCRELCONNECTSSTRUCTURALACTIVITY('0XvroPpOb4FPsGBZQ$pgtA',#209,$,$,#296,#317);
#337= IFCRELASSIGNSTOGROUP('2OygXKIkL35eDtUalQjese',#209,$,$,(#317),.PRODUCT.,#312);

/* Material profile set associated with each structural member */
#340= IFCMATERIALPROFILESET($,$,(#342),$);
#342= IFCMATERIALPROFILE($,$,#353,#419,$,$);
#344= IFCMATERIALPROFILESETUSAGE(#340,$,$);
#345= IFCRELASSOCIATESMATERIAL('01kVXT9hb7C80NqRflZZF9',#209,$,$,(#228,#263,#296),#344);

/* Material and properties */
#353= IFCMATERIAL('ASTM A36',$,'Steel');
#371= IFCPROPERTYSINGLEVALUE('MassDensity',$,IFCMASSDENSITYMEASURE(0.284011391108717),$);
#372= IFCMATERIALPROPERTIES('Pset_MaterialCommon',$,(#371),#353);
#375= IFCPROPERTYSINGLEVALUE('YoungModulus',$,IFCMODULUSOFELASTICITYMEASURE(29.),$);
#376= IFCPROPERTYSINGLEVALUE('ShearModulus',$,IFCMODULUSOFELASTICITYMEASURE(11.2),$);
#379= IFCMATERIALPROPERTIES('Pset_MaterialMechanical',$,(#375,#376),#353);

/* Profile and properties */
#419= IFCISHAPEPROFILEDEF(.AREA.,'W10X30',$,5.81,10.5,0.3,0.51,0.125,$,$);
#965= IFCPROPERTYSINGLEVALUE('MassPerLength',$,IFCMASSPERLENGTHMEASURE(2.5),$);
#966= IFCPROPERTYSINGLEVALUE('CrossSectionArea',$,IFCAREAMEASURE(8.84),$);
#974= IFCPROPERTYSINGLEVALUE('MomentOfInertiaY',$,IFCMOMENTOFINERTIAMEASURE(170.),$);
#975= IFCPROPERTYSINGLEVALUE('MomentOfInertiaZ',$,IFCMOMENTOFINERTIAMEASURE(16.7),$);
#985= IFCPROPERTYSINGLEVALUE('TorsionalSectionModulus',$,IFCSECTIONMODULUSMEASURE(0.622),$);
#990= IFCPROFILEPROPERTIES('Pset_ProfileMechanical',$,(#965,#966,#974,#975,#985),#419);

/* The result group indicating reactions for the load case */
#2729= IFCSTRUCTURALRESULTGROUP('3nK7dm3u9EYhoBHOTo765A',#209,$,$,$,.FIRST_ORDER_THEORY.,#312,.T.);

/* The node below the left column has no displacement (it has fixed boundary condition so it shouldn't) */
#2732= IFCSTRUCTURALLOADSINGLEDISPLACEMENT($,0.,0.,0.,0.,0.,0.);
#2733= IFCSTRUCTURALPOINTREACTION('0Ci9_J7iLDhuBtYHGEpcTw',#209,$,$,$,$,$,#2732,.GLOBAL_COORDS.);
#2735= IFCRELCONNECTSSTRUCTURALACTIVITY('2nVw9fFML1G8QuiGnXOdEh',#209,$,$,#236,#2733);

/* Each reaction is assigned to the result group. */
#2737= IFCRELASSIGNSTOGROUP('2dN5hErLP9zB0e5nA$yT0F',#209,$,$,(#2733,#2741,#2747,#2753,#2759,#2765,#2773,#2781,#2789),.PRODUCT.,#2729);

/* The node below the left column has a reaction with force upwards and to the right and moment counter-clockwise as facing +Y */
#2740= IFCSTRUCTURALLOADSINGLEFORCE($,1422.66326629449,0.,2278.52897011915,0.,66694.8548930371,0.);
#2741= IFCSTRUCTURALPOINTREACTION('1VIuS9lz50U9$sbX2t8CSb',#209,$,$,$,$,$,#2740,.GLOBAL_COORDS.);
#2743= IFCRELCONNECTSSTRUCTURALACTIVITY('2l0cjBJHH8nxFBsGxDKSfi',#209,$,$,#236,#2741);

/* The node above the left column has a displacement to the left (sway) */
#2746= IFCSTRUCTURALLOADSINGLEDISPLACEMENT($,-0.00112040278567376,0.,-7.54271659073925E-05,0.,3.08969735441016E-05,0.);
#2747= IFCSTRUCTURALPOINTREACTION('2Xxob9ZATFeQmLnunjg4E$',#209,$,$,$,$,$,#2746,.GLOBAL_COORDS.);
#2749= IFCRELCONNECTSSTRUCTURALACTIVITY('2iaDWtrbn6W9TfDxfsikIH',#209,$,$,#247,#2747);

/* The node below the right column has no displacement (it has a fixed boundary condition so it shouldn't) */
#2752= IFCSTRUCTURALLOADSINGLEDISPLACEMENT($,0.,0.,0.,0.,0.,0.);
#2753= IFCSTRUCTURALPOINTREACTION('2YqhTmxQv6_AQPAcaE66Xu',#209,$,$,$,$,$,#2752,.GLOBAL_COORDS.);
#2755= IFCRELCONNECTSSTRUCTURALACTIVITY('0yGyLgS9T3Y9D6VtgcdAkS',#209,$,$,#271,#2753);

/* The node below the right column has a reaction with force updwards and to the left and moment clockwise as facing +Y */
#2758= IFCSTRUCTURALLOADSINGLEFORCE($,-1422.73493120008,0.,7321.47102988085,0.,-43375.4476654014,0.);
#2759= IFCSTRUCTURALPOINTREACTION('1b1zM4uHz8exw0fvIGvpGQ',#209,$,$,$,$,$,#2758,.GLOBAL_COORDS.);
#2761= IFCRELCONNECTSSTRUCTURALACTIVITY('0Whra2O$v9wePeIU6IImNQ',#209,$,$,#271,#2759);

/* The node above the right column has a displacement to the left (sway) */
#2764= IFCSTRUCTURALLOADSINGLEDISPLACEMENT($,-0.00119575654821984,0.,-0.000242365937540883,0.,-6.94996291089951E-05,0.);
#2765= IFCSTRUCTURALPOINTREACTION('33628eH254VAe0O7d63nzI',#209,$,$,$,$,$,#2764,.GLOBAL_COORDS.);
#2767= IFCRELCONNECTSSTRUCTURALACTIVITY('2ukIn71Pr2VwtcAQ5GPi8T',#209,$,$,#280,#2765);

/* The member for the left column has end reactions for which shear and moment diagrams may be derived */
#2770= IFCSTRUCTURALLOADSINGLEFORCE('Head',2278.52897011915,0.,-1422.66326629449,0.,66694.8548930371,0.);
#2771= IFCSTRUCTURALLOADSINGLEFORCE('Tail',-2278.52897011915,0.,1422.66326629449,0.,104027.289932507,0.);
#2772= IFCSTRUCTURALLOADCONFIGURATION('Member End Reactions',(#2770,#2771),((0.),(120.)));
#2773= IFCSTRUCTURALCURVEREACTION('0SH7YcIWrB8Q4VcWjfXpnn',#209,$,$,$,$,$,#2772,.GLOBAL_COORDS.,.DISCRETE.);
#2775= IFCRELCONNECTSSTRUCTURALACTIVITY('2vTn7XCur6sByitir9a9jj',#209,$,$,#228,#2773);

/* The member for the right column has end reactions for which shear and moment diagrams may be derived */
#2778= IFCSTRUCTURALLOADSINGLEFORCE('Head',7321.47102988085,0.,1422.73493120008,0.,-43375.4476654014,0.);
#2779= IFCSTRUCTURALLOADSINGLEFORCE('Tail',-7321.47102988085,0.,-1422.73493120008,0.,-127343.989381682,0.);
#2780= IFCSTRUCTURALLOADCONFIGURATION('Member End Reactions',(#2778,#2779),((0.),(120.)));
#2781= IFCSTRUCTURALCURVEREACTION('1L3TtLyyPEzgk1VyvDrWiH',#209,$,$,$,$,$,#2780,.GLOBAL_COORDS.,.DISCRETE.);
#2783= IFCRELCONNECTSSTRUCTURALACTIVITY('23GMRW_cf7tA667rAWkQOC',#209,$,$,#263,#2781);

/* The member for the beam has end reactions for which shear and moment diagrams may be derived */
#2786= IFCSTRUCTURALLOADSINGLEFORCE('Head',1422.69473557039,0.,2278.52222225513,0.,-104030.36194645,0.);
#2787= IFCSTRUCTURALLOADSINGLEFORCE('Tail',-1422.69473557039,0.,7321.47777774487,0.,127353.*********,0.);
#2788= IFCSTRUCTURALLOADCONFIGURATION('Member End Reactions',(#2786,#2787),((0.),(192.)));
#2789= IFCSTRUCTURALCURVEREACTION('2ssucs0rX8KgBecASOwgSd',#209,$,$,$,$,$,#2788,.GLOBAL_COORDS.,.DISCRETE.);
#2791= IFCRELCONNECTSSTRUCTURALACTIVITY('1tmGmLIfTFnBfWx$tia32R',#209,$,$,#296,#2789);

ENDSEC;

END-ISO-10303-21;

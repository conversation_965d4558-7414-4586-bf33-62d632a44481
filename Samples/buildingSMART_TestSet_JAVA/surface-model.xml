<IfcProject type="IfcProject" globalId="3b7266e8-127d-4e31-a10d-59cd1eb671a7">
  <ownerHistory type="IfcOwnerHistory" globalId="01f2aa16-cc23-44d1-9d85-47d1b5531e5f" changeAction="ADDED">
    <owningUser type="IfcPersonAndOrganization" globalId="d1b34fb2-1709-41e9-9dd1-3e7efa225c4e">
      <thePerson type="IfcPerson" globalId="20de23be-1089-4389-95c7-f987fccf0a4e">
        <familyName type="IfcLabel" value="Liebich"/>
        <givenName type="IfcLabel" value="Thomas"/>
      </thePerson>
      <theOrganization type="IfcOrganization" globalId="64a99960-08a8-415f-8638-98c6fdccbcb6">
        <name type="IfcLabel" value="buildingSMART International"/>
      </theOrganization>
    </owningUser>
    <owningApplication>
      <applicationDeveloper>64a99960-08a8-415f-8638-98c6fdccbcb6</applicationDeveloper>
      <version type="IfcLabel" value="1.0"/>
      <applicationFullName type="IfcLabel" value="IFC text editor"/>
      <applicationIdentifier type="IfcIdentifier" value="ifcTE"/>
    </owningApplication>
    <lastModifiedDate type="IfcTimeStamp" value="1320688800"/>
    <creationDate type="IfcTimeStamp" value="1320688800"/>
  </ownerHistory>
  <name type="IfcLabel" value="proxy with surface model"/>
  <isDecomposedBy type="IfcRelAggregates" globalId="a22f491f-f885-4116-8813-24d281d90c1d">
    <relatedObjects>
      <IfcObjectDefinition type="IfcBuilding" globalId="8f323372-d7b4-4d74-85d2-001ba786c219" compositionType="ELEMENT">
        <name type="IfcLabel" value="Test Building"/>
        <objectPlacement type="IfcLocalPlacement" globalId="5c55ca1a-3292-4f08-8a16-d89331f1e56d">
          <relativePlacement type="IfcAxis2Placement3D">
            <location type="IfcCartesianPoint">
              <coordinates>
                <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
              </coordinates>
            </location>
          </relativePlacement>
        </objectPlacement>
        <containsElements>
          <IfcRelContainedInSpatialStructure type="IfcRelContainedInSpatialStructure" globalId="9dc7b8ee-7617-4022-8378-5b8ade78f5fc">
            <name type="IfcLabel" value="Physical model"/>
            <relatedElements>
              <IfcProduct type="IfcBuildingElementProxy" globalId="6e779871-965f-4c83-a22f-9969c19db132">
                <description type="IfcText" value="sample proxy"/>
                <name type="IfcLabel" value="P-1"/>
                <objectPlacement type="IfcLocalPlacement" globalId="de065e67-f847-427d-b4b9-e0a84f71c834">
                  <relativePlacement type="IfcAxis2Placement3D">
                    <location type="IfcCartesianPoint">
                      <coordinates>
                        <IfcLengthMeasure type="IfcLengthMeasure" value="1000.0"/>
                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                        <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                      </coordinates>
                    </location>
                  </relativePlacement>
                  <placementRelTo>5c55ca1a-3292-4f08-8a16-d89331f1e56d</placementRelTo>
                </objectPlacement>
                <representation type="IfcProductDefinitionShape" globalId="cd7e2d42-6d01-4eff-bf4d-af978c1bd73a">
                  <representations>
                    <IfcRepresentation type="IfcShapeRepresentation" globalId="64ba8818-16f2-44bb-a73a-d9d0409ba6a8">
                      <contextOfItems type="IfcGeometricRepresentationSubContext" globalId="3479d033-6bfb-4742-b50b-8c8adba62e20" targetView="MODEL_VIEW">
                        <contextIdentifier type="IfcLabel" value="Body"/>
                        <contextType type="IfcLabel" value="Model"/>
                      </contextOfItems>
                      <representationIdentifier type="IfcLabel" value="Body"/>
                      <representationType type="IfcLabel" value="SurfaceModel"/>
                      <items>
                        <IfcRepresentationItem type="IfcFaceBasedSurfaceModel">
                          <dim>3</dim>
                          <fbsmFaces>
                            <IfcConnectedFaceSet type="IfcConnectedFaceSet">
                              <cfsFaces>
                                <IfcFace type="IfcFace">
                                  <bounds>
                                    <IfcFaceBound type="IfcFaceOuterBound">
                                      <bound type="IfcPolyLoop">
                                        <polygon>
                                          <IfcCartesianPoint type="IfcCartesianPoint">
                                            <coordinates>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="-500.0"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="500.0"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="2000.0"/>
                                            </coordinates>
                                          </IfcCartesianPoint>
                                          <IfcCartesianPoint type="IfcCartesianPoint">
                                            <coordinates>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="-500.0"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="500.0"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                            </coordinates>
                                          </IfcCartesianPoint>
                                          <IfcCartesianPoint type="IfcCartesianPoint">
                                            <coordinates>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="-500.0"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="-500.0"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                            </coordinates>
                                          </IfcCartesianPoint>
                                          <IfcCartesianPoint type="IfcCartesianPoint">
                                            <coordinates>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="-500.0"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="-500.0"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="2000.0"/>
                                            </coordinates>
                                          </IfcCartesianPoint>
                                        </polygon>
                                      </bound>
                                      <orientation type="IfcBoolean" value="false"/>
                                    </IfcFaceBound>
                                  </bounds>
                                </IfcFace>
                                <IfcFace type="IfcFace">
                                  <bounds>
                                    <IfcFaceBound type="IfcFaceOuterBound">
                                      <bound type="IfcPolyLoop">
                                        <polygon>
                                          <IfcCartesianPoint type="IfcCartesianPoint">
                                            <coordinates>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="-500.0"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="-500.0"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                            </coordinates>
                                          </IfcCartesianPoint>
                                          <IfcCartesianPoint type="IfcCartesianPoint">
                                            <coordinates>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="-500.0"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="500.0"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                            </coordinates>
                                          </IfcCartesianPoint>
                                          <IfcCartesianPoint type="IfcCartesianPoint">
                                            <coordinates>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="500.0"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="500.0"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                            </coordinates>
                                          </IfcCartesianPoint>
                                          <IfcCartesianPoint type="IfcCartesianPoint">
                                            <coordinates>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="500.0"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="-500.0"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                            </coordinates>
                                          </IfcCartesianPoint>
                                        </polygon>
                                      </bound>
                                      <orientation type="IfcBoolean" value="false"/>
                                    </IfcFaceBound>
                                  </bounds>
                                </IfcFace>
                                <IfcFace type="IfcFace">
                                  <bounds>
                                    <IfcFaceBound type="IfcFaceOuterBound">
                                      <bound type="IfcPolyLoop">
                                        <polygon>
                                          <IfcCartesianPoint type="IfcCartesianPoint">
                                            <coordinates>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="500.0"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="-500.0"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="2000.0"/>
                                            </coordinates>
                                          </IfcCartesianPoint>
                                          <IfcCartesianPoint type="IfcCartesianPoint">
                                            <coordinates>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="500.0"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="-500.0"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                            </coordinates>
                                          </IfcCartesianPoint>
                                          <IfcCartesianPoint type="IfcCartesianPoint">
                                            <coordinates>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="500.0"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="500.0"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                            </coordinates>
                                          </IfcCartesianPoint>
                                          <IfcCartesianPoint type="IfcCartesianPoint">
                                            <coordinates>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="500.0"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="500.0"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="2000.0"/>
                                            </coordinates>
                                          </IfcCartesianPoint>
                                        </polygon>
                                      </bound>
                                      <orientation type="IfcBoolean" value="false"/>
                                    </IfcFaceBound>
                                  </bounds>
                                </IfcFace>
                                <IfcFace type="IfcFace">
                                  <bounds>
                                    <IfcFaceBound type="IfcFaceOuterBound">
                                      <bound type="IfcPolyLoop">
                                        <polygon>
                                          <IfcCartesianPoint type="IfcCartesianPoint">
                                            <coordinates>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="500.0"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="-500.0"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="2000.0"/>
                                            </coordinates>
                                          </IfcCartesianPoint>
                                          <IfcCartesianPoint type="IfcCartesianPoint">
                                            <coordinates>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="500.0"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="500.0"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="2000.0"/>
                                            </coordinates>
                                          </IfcCartesianPoint>
                                          <IfcCartesianPoint type="IfcCartesianPoint">
                                            <coordinates>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="-500.0"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="500.0"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="2000.0"/>
                                            </coordinates>
                                          </IfcCartesianPoint>
                                          <IfcCartesianPoint type="IfcCartesianPoint">
                                            <coordinates>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="-500.0"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="-500.0"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="2000.0"/>
                                            </coordinates>
                                          </IfcCartesianPoint>
                                        </polygon>
                                      </bound>
                                      <orientation type="IfcBoolean" value="false"/>
                                    </IfcFaceBound>
                                  </bounds>
                                </IfcFace>
                                <IfcFace type="IfcFace">
                                  <bounds>
                                    <IfcFaceBound type="IfcFaceOuterBound">
                                      <bound type="IfcPolyLoop">
                                        <polygon>
                                          <IfcCartesianPoint type="IfcCartesianPoint">
                                            <coordinates>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="500.0"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="500.0"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="2000.0"/>
                                            </coordinates>
                                          </IfcCartesianPoint>
                                          <IfcCartesianPoint type="IfcCartesianPoint">
                                            <coordinates>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="500.0"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="500.0"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                            </coordinates>
                                          </IfcCartesianPoint>
                                          <IfcCartesianPoint type="IfcCartesianPoint">
                                            <coordinates>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="-500.0"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="500.0"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                            </coordinates>
                                          </IfcCartesianPoint>
                                          <IfcCartesianPoint type="IfcCartesianPoint">
                                            <coordinates>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="-500.0"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="500.0"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="2000.0"/>
                                            </coordinates>
                                          </IfcCartesianPoint>
                                        </polygon>
                                      </bound>
                                      <orientation type="IfcBoolean" value="false"/>
                                    </IfcFaceBound>
                                  </bounds>
                                </IfcFace>
                                <IfcFace type="IfcFace">
                                  <bounds>
                                    <IfcFaceBound type="IfcFaceOuterBound">
                                      <bound type="IfcPolyLoop">
                                        <polygon>
                                          <IfcCartesianPoint type="IfcCartesianPoint">
                                            <coordinates>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="-500.0"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="-500.0"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                            </coordinates>
                                          </IfcCartesianPoint>
                                          <IfcCartesianPoint type="IfcCartesianPoint">
                                            <coordinates>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="500.0"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="-500.0"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
                                            </coordinates>
                                          </IfcCartesianPoint>
                                          <IfcCartesianPoint type="IfcCartesianPoint">
                                            <coordinates>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="500.0"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="-500.0"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="2000.0"/>
                                            </coordinates>
                                          </IfcCartesianPoint>
                                          <IfcCartesianPoint type="IfcCartesianPoint">
                                            <coordinates>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="-500.0"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="-500.0"/>
                                              <IfcLengthMeasure type="IfcLengthMeasure" value="2000.0"/>
                                            </coordinates>
                                          </IfcCartesianPoint>
                                        </polygon>
                                      </bound>
                                      <orientation type="IfcBoolean" value="false"/>
                                    </IfcFaceBound>
                                  </bounds>
                                </IfcFace>
                              </cfsFaces>
                            </IfcConnectedFaceSet>
                          </fbsmFaces>
                        </IfcRepresentationItem>
                      </items>
                    </IfcRepresentation>
                  </representations>
                </representation>
              </IfcProduct>
            </relatedElements>
          </IfcRelContainedInSpatialStructure>
        </containsElements>
      </IfcObjectDefinition>
    </relatedObjects>
  </isDecomposedBy>
  <representationContexts>
    <IfcRepresentationContext type="IfcGeometricRepresentationContext" globalId="196ffd5f-ab95-4d3d-8bbb-0870d8d0de2e">
      <worldCoordinateSystem type="IfcAxis2Placement3D">
        <location type="IfcCartesianPoint">
          <coordinates>
            <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
            <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
            <IfcLengthMeasure type="IfcLengthMeasure" value="0.0"/>
          </coordinates>
        </location>
      </worldCoordinateSystem>
      <contextType type="IfcLabel" value="Model"/>
      <coordinateSpaceDimension type="IfcDimensionCount" value="3"/>
      <precision type="IfcReal" value="1.0E-5"/>
      <hasSubContexts>
        <IfcGeometricRepresentationSubContext>3479d033-6bfb-4742-b50b-8c8adba62e20</IfcGeometricRepresentationSubContext>
      </hasSubContexts>
    </IfcRepresentationContext>
  </representationContexts>
  <unitsInContext>
    <units>
      <IfcUnit type="IfcSIUnit" globalId="777937fd-a348-4da9-8e8b-c0816a5938f9">
        <dimensions lengthExponent="1" massExponent="0" timeExponent="0" electricCurrentExponent="0" thermodynamicTemperatureExponent="0" amountOfSubstanceExponent="0" luminousIntensityExponent="0"/>
        <unitType>LENGTHUNIT</unitType>
        <prefix>MILLI</prefix>
        <name>METRE</name>
      </IfcUnit>
      <IfcUnit type="IfcConversionBasedUnit" globalId="af5ad7bc-3db5-433a-8a12-5051a52057d9">
        <dimensions lengthExponent="0" massExponent="0" timeExponent="0" electricCurrentExponent="0" thermodynamicTemperatureExponent="0" amountOfSubstanceExponent="0" luminousIntensityExponent="0"/>
        <unitType>PLANEANGLEUNIT</unitType>
        <name>degree</name>
        <conversionFactor type="IfcMeasureWithUnit">
          <valueComponent type="IfcPlaneAngleMeasure" value="0.017453293"/>
          <unitComponent type="IfcSIUnit" globalId="252f583c-bb35-4e79-8113-fda320f472f3">
            <dimensions lengthExponent="0" massExponent="0" timeExponent="0" electricCurrentExponent="0" thermodynamicTemperatureExponent="0" amountOfSubstanceExponent="0" luminousIntensityExponent="0"/>
            <unitType>PLANEANGLEUNIT</unitType>
            <name>RADIAN</name>
          </unitComponent>
        </conversionFactor>
      </IfcUnit>
    </units>
  </unitsInContext>
</IfcProject>

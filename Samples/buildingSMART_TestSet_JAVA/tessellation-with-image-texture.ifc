ISO-10303-21;
HEADER;
FILE_DESCRIPTION($,'2;1');
FILE_NAME('triangulatedfaceset-texture-image.ifc','2016-01-20T18:38:10',(''),(''),'Constructivity *******','Constructivity *******','');
FILE_SCHEMA(('IFC4'));
ENDSEC;

DATA;

#1= IFCPROJECT('2BkIlLW5T7aQj3Uy9BMFxr',$,'Project',$,$,$,$,(#2,#3),#4);
#2= IFCGEOMETRICREPRESENTATIONCONTEXT('3D','Model',3,1.0E-05,#7,$);
#3= IFCGEOMETRICREPRESENTATIONCONTEXT('2D','Plan',2,1.0E-05,#8,$);
#4= IFCUNITASSIGNMENT((#9,#10,#11,#12,#13,#14,#15,#16,#17,#18,#19,#20,#21,#22,#23,#24,#25,#26,#27,#28,#29,#30,#31,#32,#33,#34,#35,#36,#37,#38,#39,#40,#41,#42,#43,#44,#45,#46,#47,#48,#49,#50,#51,#52,#53,#54,#55,#56,#57,#58,#59,#60,#61,#62,#63,#64,#65,#66,#67,#68,#69,#70,#71,#72,#73,#74,#75,#76,#77,#78,#79,#80,#81,#82,#83,#84,#85,#86));
#7= IFCAXIS2PLACEMENT3D(#87,$,$);
#8= IFCAXIS2PLACEMENT3D(#88,$,$);
#9= IFCSIUNIT(*,.ABSORBEDDOSEUNIT.,$,.GRAY.);
#10= IFCSIUNIT(*,.AMOUNTOFSUBSTANCEUNIT.,$,.MOLE.);
#11= IFCSIUNIT(*,.AREAUNIT.,$,.SQUARE_METRE.);
#12= IFCSIUNIT(*,.DOSEEQUIVALENTUNIT.,$,.SIEVERT.);
#13= IFCSIUNIT(*,.ELECTRICCAPACITANCEUNIT.,$,.FARAD.);
#14= IFCSIUNIT(*,.ELECTRICCHARGEUNIT.,$,.COULOMB.);
#15= IFCSIUNIT(*,.ELECTRICCONDUCTANCEUNIT.,$,.SIEMENS.);
#16= IFCSIUNIT(*,.ELECTRICCURRENTUNIT.,$,.AMPERE.);
#17= IFCSIUNIT(*,.ELECTRICRESISTANCEUNIT.,$,.OHM.);
#18= IFCSIUNIT(*,.ELECTRICVOLTAGEUNIT.,$,.VOLT.);
#19= IFCSIUNIT(*,.ENERGYUNIT.,$,.JOULE.);
#20= IFCSIUNIT(*,.FORCEUNIT.,$,.NEWTON.);
#21= IFCSIUNIT(*,.FREQUENCYUNIT.,$,.HERTZ.);
#22= IFCSIUNIT(*,.ILLUMINANCEUNIT.,$,.LUX.);
#23= IFCSIUNIT(*,.INDUCTANCEUNIT.,$,.HENRY.);
#24= IFCSIUNIT(*,.LENGTHUNIT.,$,.METRE.);
#25= IFCSIUNIT(*,.LUMINOUSFLUXUNIT.,$,.LUMEN.);
#26= IFCSIUNIT(*,.LUMINOUSINTENSITYUNIT.,$,.CANDELA.);
#27= IFCSIUNIT(*,.MAGNETICFLUXDENSITYUNIT.,$,.TESLA.);
#28= IFCSIUNIT(*,.MAGNETICFLUXUNIT.,$,.WEBER.);
#29= IFCSIUNIT(*,.MASSUNIT.,.KILO.,.GRAM.);
#30= IFCCONVERSIONBASEDUNIT(#89,.PLANEANGLEUNIT.,'degree',#90);
#31= IFCSIUNIT(*,.POWERUNIT.,$,.WATT.);
#32= IFCSIUNIT(*,.PRESSUREUNIT.,$,.PASCAL.);
#33= IFCSIUNIT(*,.RADIOACTIVITYUNIT.,$,.BECQUEREL.);
#34= IFCSIUNIT(*,.SOLIDANGLEUNIT.,$,.STERADIAN.);
#35= IFCSIUNIT(*,.THERMODYNAMICTEMPERATUREUNIT.,$,.DEGREE_CELSIUS.);
#36= IFCSIUNIT(*,.TIMEUNIT.,$,.SECOND.);
#37= IFCSIUNIT(*,.VOLUMEUNIT.,$,.CUBIC_METRE.);
#38= IFCDERIVEDUNIT((#91,#92,#93),.ACCELERATIONUNIT.,$);
#39= IFCDERIVEDUNIT((#94,#95),.ANGULARVELOCITYUNIT.,$);
#40= IFCDERIVEDUNIT((#96),.COMPOUNDPLANEANGLEUNIT.,$);
#41= IFCDERIVEDUNIT((#97,#98),.CURVATUREUNIT.,$);
#42= IFCDERIVEDUNIT((#99,#100),.DYNAMICVISCOSITYUNIT.,$);
#43= IFCDERIVEDUNIT((#101,#102),.HEATFLUXDENSITYUNIT.,$);
#44= IFCDERIVEDUNIT((#103,#104),.HEATINGVALUEUNIT.,$);
#45= IFCDERIVEDUNIT((#105,#106),.IONCONCENTRATIONUNIT.,$);
#46= IFCDERIVEDUNIT((#107),.INTEGERCOUNTRATEUNIT.,$);
#47= IFCDERIVEDUNIT((#108,#109),.ISOTHERMALMOISTURECAPACITYUNIT.,$);
#48= IFCDERIVEDUNIT((#110,#111),.KINEMATICVISCOSITYUNIT.,$);
#49= IFCDERIVEDUNIT((#112,#113),.LINEARFORCEUNIT.,$);
#50= IFCDERIVEDUNIT((#114,#115,#116),.LINEARMOMENTUNIT.,$);
#51= IFCDERIVEDUNIT((#117,#118),.LINEARSTIFFNESSUNIT.,$);
#52= IFCDERIVEDUNIT((#119,#120),.LINEARVELOCITYUNIT.,$);
#53= IFCDERIVEDUNIT((#121,#122),.LUMINOUSINTENSITYDISTRIBUTIONUNIT.,$);
#54= IFCDERIVEDUNIT((#123,#124),.MASSDENSITYUNIT.,$);
#55= IFCDERIVEDUNIT((#125,#126),.MASSFLOWRATEUNIT.,$);
#56= IFCDERIVEDUNIT((#127,#128),.MASSPERLENGTHUNIT.,$);
#57= IFCDERIVEDUNIT((#129),.MODULUSOFELASTICITYUNIT.,$);
#58= IFCDERIVEDUNIT((#130,#131),.MODULUSOFLINEARSUBGRADEREACTIONUNIT.,$);
#59= IFCDERIVEDUNIT((#132,#133,#134,#135),.MODULUSOFROTATIONALSUBGRADEREACTIONUNIT.,$);
#60= IFCDERIVEDUNIT((#136,#137),.MODULUSOFSUBGRADEREACTIONUNIT.,$);
#61= IFCDERIVEDUNIT((#138,#139),.MOISTUREDIFFUSIVITYUNIT.,$);
#62= IFCDERIVEDUNIT((#140,#141),.MOLECULARWEIGHTUNIT.,$);
#63= IFCDERIVEDUNIT((#142),.MOMENTOFINERTIAUNIT.,$);
#64= IFCDERIVEDUNIT((#143,#144),.PLANARFORCEUNIT.,$);
#65= IFCDERIVEDUNIT((#145),.ROTATIONALFREQUENCYUNIT.,$);
#66= IFCDERIVEDUNIT((#146,#147),.ROTATIONALMASSUNIT.,$);
#67= IFCDERIVEDUNIT((#148,#149,#150),.ROTATIONALSTIFFNESSUNIT.,$);
#68= IFCDERIVEDUNIT((#151),.SECTIONAREAINTEGRALUNIT.,$);
#69= IFCDERIVEDUNIT((#152),.SECTIONMODULUSUNIT.,$);
#70= IFCDERIVEDUNIT((#153),.SHEARMODULUSUNIT.,$);
#71= IFCDERIVEDUNIT((#154),.SOUNDPOWERUNIT.,$);
#72= IFCDERIVEDUNIT((#155),.SOUNDPRESSUREUNIT.,$);
#73= IFCDERIVEDUNIT((#156,#157,#158),.SPECIFICHEATCAPACITYUNIT.,$);
#74= IFCDERIVEDUNIT((#159,#160),.TEMPERATUREGRADIENTUNIT.,$);
#75= IFCDERIVEDUNIT((#161,#162),.TEMPERATURERATEOFCHANGEUNIT.,$);
#76= IFCDERIVEDUNIT((#163,#164,#165),.THERMALADMITTANCEUNIT.,$);
#77= IFCDERIVEDUNIT((#166,#167,#168),.THERMALCONDUCTANCEUNIT.,$);
#78= IFCDERIVEDUNIT((#169),.THERMALEXPANSIONCOEFFICIENTUNIT.,$);
#79= IFCDERIVEDUNIT((#170,#171,#172),.THERMALRESISTANCEUNIT.,$);
#80= IFCDERIVEDUNIT((#173,#174,#175),.THERMALTRANSMITTANCEUNIT.,$);
#81= IFCDERIVEDUNIT((#176,#177),.TORQUEUNIT.,$);
#82= IFCDERIVEDUNIT((#178,#179,#180),.VAPORPERMEABILITYUNIT.,$);
#83= IFCDERIVEDUNIT((#181,#182),.VOLUMETRICFLOWRATEUNIT.,$);
#84= IFCDERIVEDUNIT((#183),.WARPINGCONSTANTUNIT.,$);
#85= IFCDERIVEDUNIT((#184,#185),.WARPINGMOMENTUNIT.,$);
#86= IFCMONETARYUNIT('USD');
#87= IFCCARTESIANPOINT((0.,0.,0.));
#88= IFCCARTESIANPOINT((0.,0.,0.));
#89= IFCDIMENSIONALEXPONENTS(0,0,0,0,0,0,0);
#90= IFCMEASUREWITHUNIT(IFCPLANEANGLEMEASURE(0.0174532925199433),#186);
#91= IFCDERIVEDUNITELEMENT(#24,1);
#92= IFCDERIVEDUNITELEMENT(#36,-1);
#93= IFCDERIVEDUNITELEMENT(#36,-1);
#94= IFCDERIVEDUNITELEMENT(#30,1);
#95= IFCDERIVEDUNITELEMENT(#36,-1);
#96= IFCDERIVEDUNITELEMENT(#30,1);
#97= IFCDERIVEDUNITELEMENT(#30,1);
#98= IFCDERIVEDUNITELEMENT(#24,-1);
#99= IFCDERIVEDUNITELEMENT(#32,1);
#100= IFCDERIVEDUNITELEMENT(#36,1);
#101= IFCDERIVEDUNITELEMENT(#31,1);
#102= IFCDERIVEDUNITELEMENT(#11,-1);
#103= IFCDERIVEDUNITELEMENT(#19,1);
#104= IFCDERIVEDUNITELEMENT(#29,-1);
#105= IFCDERIVEDUNITELEMENT(#187,1);
#106= IFCDERIVEDUNITELEMENT(#37,-1);
#107= IFCDERIVEDUNITELEMENT(#36,-1);
#108= IFCDERIVEDUNITELEMENT(#37,1);
#109= IFCDERIVEDUNITELEMENT(#9,-1);
#110= IFCDERIVEDUNITELEMENT(#11,1);
#111= IFCDERIVEDUNITELEMENT(#36,-1);
#112= IFCDERIVEDUNITELEMENT(#20,1);
#113= IFCDERIVEDUNITELEMENT(#24,-1);
#114= IFCDERIVEDUNITELEMENT(#20,1);
#115= IFCDERIVEDUNITELEMENT(#24,1);
#116= IFCDERIVEDUNITELEMENT(#24,-1);
#117= IFCDERIVEDUNITELEMENT(#20,1);
#118= IFCDERIVEDUNITELEMENT(#24,-1);
#119= IFCDERIVEDUNITELEMENT(#24,1);
#120= IFCDERIVEDUNITELEMENT(#36,-1);
#121= IFCDERIVEDUNITELEMENT(#26,1);
#122= IFCDERIVEDUNITELEMENT(#25,-1);
#123= IFCDERIVEDUNITELEMENT(#29,1);
#124= IFCDERIVEDUNITELEMENT(#37,-1);
#125= IFCDERIVEDUNITELEMENT(#29,1);
#126= IFCDERIVEDUNITELEMENT(#36,-1);
#127= IFCDERIVEDUNITELEMENT(#29,1);
#128= IFCDERIVEDUNITELEMENT(#24,-1);
#129= IFCDERIVEDUNITELEMENT(#32,1);
#130= IFCDERIVEDUNITELEMENT(#20,1);
#131= IFCDERIVEDUNITELEMENT(#11,-1);
#132= IFCDERIVEDUNITELEMENT(#20,1);
#133= IFCDERIVEDUNITELEMENT(#24,1);
#134= IFCDERIVEDUNITELEMENT(#24,-1);
#135= IFCDERIVEDUNITELEMENT(#30,-1);
#136= IFCDERIVEDUNITELEMENT(#20,1);
#137= IFCDERIVEDUNITELEMENT(#37,-1);
#138= IFCDERIVEDUNITELEMENT(#37,1);
#139= IFCDERIVEDUNITELEMENT(#36,-1);
#140= IFCDERIVEDUNITELEMENT(#187,1);
#141= IFCDERIVEDUNITELEMENT(#10,-1);
#142= IFCDERIVEDUNITELEMENT(#24,4);
#143= IFCDERIVEDUNITELEMENT(#20,1);
#144= IFCDERIVEDUNITELEMENT(#11,-1);
#145= IFCDERIVEDUNITELEMENT(#36,-1);
#146= IFCDERIVEDUNITELEMENT(#29,1);
#147= IFCDERIVEDUNITELEMENT(#11,-1);
#148= IFCDERIVEDUNITELEMENT(#20,1);
#149= IFCDERIVEDUNITELEMENT(#24,1);
#150= IFCDERIVEDUNITELEMENT(#30,-1);
#151= IFCDERIVEDUNITELEMENT(#24,5);
#152= IFCDERIVEDUNITELEMENT(#24,3);
#153= IFCDERIVEDUNITELEMENT(#32,1);
#154= IFCDERIVEDUNITELEMENT(#188,0);
#155= IFCDERIVEDUNITELEMENT(#189,0);
#156= IFCDERIVEDUNITELEMENT(#19,1);
#157= IFCDERIVEDUNITELEMENT(#29,-1);
#158= IFCDERIVEDUNITELEMENT(#190,-1);
#159= IFCDERIVEDUNITELEMENT(#190,1);
#160= IFCDERIVEDUNITELEMENT(#24,-1);
#161= IFCDERIVEDUNITELEMENT(#190,1);
#162= IFCDERIVEDUNITELEMENT(#36,-1);
#163= IFCDERIVEDUNITELEMENT(#31,1);
#164= IFCDERIVEDUNITELEMENT(#11,-1);
#165= IFCDERIVEDUNITELEMENT(#190,-1);
#166= IFCDERIVEDUNITELEMENT(#31,1);
#167= IFCDERIVEDUNITELEMENT(#190,-1);
#168= IFCDERIVEDUNITELEMENT(#24,-1);
#169= IFCDERIVEDUNITELEMENT(#190,-1);
#170= IFCDERIVEDUNITELEMENT(#11,1);
#171= IFCDERIVEDUNITELEMENT(#190,1);
#172= IFCDERIVEDUNITELEMENT(#31,-1);
#173= IFCDERIVEDUNITELEMENT(#31,1);
#174= IFCDERIVEDUNITELEMENT(#11,-1);
#175= IFCDERIVEDUNITELEMENT(#190,1);
#176= IFCDERIVEDUNITELEMENT(#20,1);
#177= IFCDERIVEDUNITELEMENT(#24,1);
#178= IFCDERIVEDUNITELEMENT(#29,1);
#179= IFCDERIVEDUNITELEMENT(#36,-1);
#180= IFCDERIVEDUNITELEMENT(#24,-1);
#181= IFCDERIVEDUNITELEMENT(#37,1);
#182= IFCDERIVEDUNITELEMENT(#36,-1);
#183= IFCDERIVEDUNITELEMENT(#24,6);
#184= IFCDERIVEDUNITELEMENT(#20,1);
#185= IFCDERIVEDUNITELEMENT(#24,2);
#186= IFCSIUNIT(*,.PLANEANGLEUNIT.,$,.RADIAN.);
#187= IFCSIUNIT(*,.MASSUNIT.,$,.GRAM.);
#188= IFCSIUNIT(*,.POWERUNIT.,.PICO.,.WATT.);
#189= IFCSIUNIT(*,.PRESSUREUNIT.,.MICRO.,.PASCAL.);
#190= IFCSIUNIT(*,.THERMODYNAMICTEMPERATUREUNIT.,$,.KELVIN.);

#5= IFCRELAGGREGATES('2itvH51TLF$vSczv308eND',$,$,$,#1,(#191));

#6= IFCRELDECLARES('1HmT$jWs52sPjBfKd4uec0',$,$,$,#1,(#192));

#191= IFCSITE('12Jhas3DnFkfm06Be8GeKW',$,'Site #1',$,$,#193,#194,$,.ELEMENT.,$,$,$,$,$);
#193= IFCLOCALPLACEMENT($,#196);
#194= IFCPRODUCTDEFINITIONSHAPE($,$,(#197));
#196= IFCAXIS2PLACEMENT3D(#198,$,$);
#197= IFCSHAPEREPRESENTATION(#2,'FootPrint','GeometricCurveSet',(#199));
#198= IFCCARTESIANPOINT((0.,0.,0.));
#199= IFCGEOMETRICCURVESET((#200));
#200= IFCPOLYLINE((#201,#202,#203,#204,#201));
#201= IFCCARTESIANPOINT((0.,0.));
#202= IFCCARTESIANPOINT((40.,0.));
#203= IFCCARTESIANPOINT((40.,20.));
#204= IFCCARTESIANPOINT((0.,20.));

#192= IFCMEMBERTYPE('1AgtYeFKDEuRnJ8pGQA3pA',$,$,$,$,$,(#205),$,$,.POST.);
#205= IFCREPRESENTATIONMAP(#207,#208);
#207= IFCAXIS2PLACEMENT3D(#209,$,$);
#208= IFCSHAPEREPRESENTATION(#2,'Body','Tessellation',(#210));
#209= IFCCARTESIANPOINT((0.,0.,0.));
#210= IFCTRIANGULATEDFACESET(#211,((0.,0.,-1.),(0.,0.,1.),(0.,0.,-1.),(0.,0.,-1.),(0.,0.,-1.),(0.,0.,-1.),(0.,0.,-1.),(0.,0.,-1.),(0.,0.,-1.),(0.,0.,-1.),(0.,0.,-1.),(0.,0.,-1.),(0.,0.,-1.),(0.,0.,-1.),(0.,0.,-1.),(0.,0.,-1.),(0.,0.,-1.),(0.,0.,-1.),(0.,0.,-1.),(0.,0.,1.),(0.,0.,1.),(0.,0.,1.),(0.,0.,1.),(0.,0.,1.),(0.,0.,1.),(0.,0.,1.),(0.,0.,1.),(0.,0.,1.),(0.,0.,1.),(0.,0.,1.),(0.,0.,1.),(0.,0.,1.),(0.,0.,1.),(0.,0.,1.),(0.,0.,1.),(0.,0.,1.),(1.,0.,0.),(0.923879532511287,0.38268343236509,0.),(0.707106781186548,0.707106781186547,0.),(0.38268343236509,0.923879532511287,0.),(6.12303176911189E-17,1.,0.),(-0.38268343236509,0.923879532511287,0.),(-0.707106781186547,0.707106781186548,0.),(-0.923879532511287,0.38268343236509,0.),(-1.,1.22460635382238E-16,0.),(-0.923879532511287,-0.38268343236509,0.),(-0.707106781186548,-0.707106781186547,0.),(-0.38268343236509,-0.923879532511287,0.),(-1.83690953073357E-16,-1.,0.),(0.38268343236509,-0.923879532511287,0.),(0.707106781186547,-0.707106781186548,0.),(0.923879532511287,-0.38268343236509,0.),(1.,-2.44921270764475E-16,0.),(1.,0.,0.),(0.923879532511287,0.38268343236509,0.),(0.707106781186548,0.707106781186547,0.),(0.38268343236509,0.923879532511287,0.),(6.12303176911189E-17,1.,0.),(-0.38268343236509,0.923879532511287,0.),(-0.707106781186547,0.707106781186548,0.),(-0.923879532511287,0.38268343236509,0.),(-1.,1.22460635382238E-16,0.),(-0.923879532511287,-0.38268343236509,0.),(-0.707106781186548,-0.707106781186547,0.),(-0.38268343236509,-0.923879532511287,0.),(-1.83690953073357E-16,-1.,0.),(0.38268343236509,-0.923879532511287,0.),(0.707106781186547,-0.707106781186548,0.),(0.923879532511287,-0.38268343236509,0.),(1.,-2.44921270764475E-16,0.)),.T.,((1,4,3),(38,54,37),(38,55,54),(2,20,21),(1,5,4),(39,55,38),(39,56,55),(2,21,22),(1,6,5),(40,56,39),(40,57,56),(2,22,23),(1,7,6),(41,57,40),(41,58,57),(2,23,24),(1,8,7),(42,58,41),(42,59,58),(2,24,25),(1,9,8),(43,59,42),(43,60,59),(2,25,26),(1,10,9),(44,60,43),(44,61,60),(2,26,27),(1,11,10),(45,61,44),(45,62,61),(2,27,28),(1,12,11),(46,62,45),(46,63,62),(2,28,29),(1,13,12),(47,63,46),(47,64,63),(2,29,30),(1,14,13),(48,64,47),(48,65,64),(2,30,31),(1,15,14),(49,65,48),(49,66,65),(2,31,32),(1,16,15),(50,66,49),(50,67,66),(2,32,33),(1,17,16),(51,67,50),(51,68,67),(2,33,34),(1,18,17),(52,68,51),(52,69,68),(2,34,35),(1,19,18),(53,69,52),(53,70,69),(2,35,36)),((1,4,3),(38,54,37),(38,55,54),(2,20,21),(1,5,4),(39,55,38),(39,56,55),(2,21,22),(1,6,5),(40,56,39),(40,57,56),(2,22,23),(1,7,6),(41,57,40),(41,58,57),(2,23,24),(1,8,7),(42,58,41),(42,59,58),(2,24,25),(1,9,8),(43,59,42),(43,60,59),(2,25,26),(1,10,9),(44,60,43),(44,61,60),(2,26,27),(1,11,10),(45,61,44),(45,62,61),(2,27,28),(1,12,11),(46,62,45),(46,63,62),(2,28,29),(1,13,12),(47,63,46),(47,64,63),(2,29,30),(1,14,13),(48,64,47),(48,65,64),(2,30,31),(1,15,14),(49,65,48),(49,66,65),(2,31,32),(1,16,15),(50,66,49),(50,67,66),(2,32,33),(1,17,16),(51,67,50),(51,68,67),(2,33,34),(1,18,17),(52,68,51),(52,69,68),(2,34,35),(1,19,18),(53,69,52),(53,70,69),(2,35,36)));
#211= IFCCARTESIANPOINTLIST3D(((0.,0.,0.),(0.,0.,4.),(2.,0.,0.),(1.84775906502257,0.76536686473018,0.),(1.4142135623731,1.41421356237309,0.),(0.76536686473018,1.84775906502257,0.),(1.22460635382238E-16,2.,0.),(-0.765366864730179,1.84775906502257,0.),(-1.41421356237309,1.4142135623731,0.),(-1.84775906502257,0.76536686473018,0.),(-2.,2.44921270764475E-16,0.),(-1.84775906502257,-0.765366864730179,0.),(-1.4142135623731,-1.41421356237309,0.),(-0.765366864730181,-1.84775906502257,0.),(-3.67381906146713E-16,-2.,0.),(0.76536686473018,-1.84775906502257,0.),(1.41421356237309,-1.4142135623731,0.),(1.84775906502257,-0.765366864730181,0.),(2.,-4.89842541528951E-16,0.),(2.,0.,4.),(1.84775906502257,0.76536686473018,4.),(1.4142135623731,1.41421356237309,4.),(0.76536686473018,1.84775906502257,4.),(1.22460635382238E-16,2.,4.),(-0.765366864730179,1.84775906502257,4.),(-1.41421356237309,1.4142135623731,4.),(-1.84775906502257,0.76536686473018,4.),(-2.,2.44921270764475E-16,4.),(-1.84775906502257,-0.765366864730179,4.),(-1.4142135623731,-1.41421356237309,4.),(-0.765366864730181,-1.84775906502257,4.),(-3.67381906146713E-16,-2.,4.),(0.76536686473018,-1.84775906502257,4.),(1.41421356237309,-1.4142135623731,4.),(1.84775906502257,-0.765366864730181,4.),(2.,-4.89842541528951E-16,4.),(2.,0.,0.),(1.84775906502257,0.76536686473018,0.),(1.4142135623731,1.41421356237309,0.),(0.76536686473018,1.84775906502257,0.),(1.22460635382238E-16,2.,0.),(-0.765366864730179,1.84775906502257,0.),(-1.41421356237309,1.4142135623731,0.),(-1.84775906502257,0.76536686473018,0.),(-2.,2.44921270764475E-16,0.),(-1.84775906502257,-0.765366864730179,0.),(-1.4142135623731,-1.41421356237309,0.),(-0.765366864730181,-1.84775906502257,0.),(-3.67381906146713E-16,-2.,0.),(0.76536686473018,-1.84775906502257,0.),(1.41421356237309,-1.4142135623731,0.),(1.84775906502257,-0.765366864730181,0.),(2.,-4.89842541528951E-16,0.),(2.,0.,4.),(1.84775906502257,0.76536686473018,4.),(1.4142135623731,1.41421356237309,4.),(0.76536686473018,1.84775906502257,4.),(1.22460635382238E-16,2.,4.),(-0.765366864730179,1.84775906502257,4.),(-1.41421356237309,1.4142135623731,4.),(-1.84775906502257,0.76536686473018,4.),(-2.,2.44921270764475E-16,4.),(-1.84775906502257,-0.765366864730179,4.),(-1.4142135623731,-1.41421356237309,4.),(-0.765366864730181,-1.84775906502257,4.),(-3.67381906146713E-16,-2.,4.),(0.76536686473018,-1.84775906502257,4.),(1.41421356237309,-1.4142135623731,4.),(1.84775906502257,-0.765366864730181,4.),(2.,-4.89842541528951E-16,4.)));
#212= IFCSTYLEDITEM(#210,(#214),$);
#213= IFCINDEXEDTRIANGLETEXTUREMAP((#219),#210,#215,((1,4,3),(38,54,37),(38,55,54),(2,20,21),(1,5,4),(39,55,38),(39,56,55),(2,21,22),(1,6,5),(40,56,39),(40,57,56),(2,22,23),(1,7,6),(41,57,40),(41,58,57),(2,23,24),(1,8,7),(42,58,41),(42,59,58),(2,24,25),(1,9,8),(43,59,42),(43,60,59),(2,25,26),(1,10,9),(44,60,43),(44,61,60),(2,26,27),(1,11,10),(45,61,44),(45,62,61),(2,27,28),(1,12,11),(46,62,45),(46,63,62),(2,28,29),(1,13,12),(47,63,46),(47,64,63),(2,29,30),(1,14,13),(48,64,47),(48,65,64),(2,30,31),(1,15,14),(49,65,48),(49,66,65),(2,31,32),(1,16,15),(50,66,49),(50,67,66),(2,32,33),(1,17,16),(51,67,50),(51,68,67),(2,33,34),(1,18,17),(52,68,51),(52,69,68),(2,34,35),(1,19,18),(53,69,52),(53,70,69),(2,35,36)));
#214= IFCSURFACESTYLE($,.POSITIVE.,(#216,#217));
#215= IFCTEXTUREVERTEXLIST(((0.5,0.5),(0.5,0.5),(1.,0.5),(0.961939766255643,0.308658283817455),(0.853553390593274,0.146446609406726),(0.691341716182545,0.0380602337443566),(0.5,0.),(0.308658283817455,0.0380602337443566),(0.146446609406726,0.146446609406726),(0.0380602337443566,0.308658283817455),(0.,0.5),(0.0380602337443566,0.691341716182545),(0.146446609406726,0.853553390593274),(0.308658283817455,0.961939766255643),(0.5,1.),(0.691341716182545,0.961939766255643),(0.853553390593274,0.853553390593274),(0.961939766255643,0.691341716182545),(1.,0.5),(1.,0.5),(0.961939766255643,0.691341716182545),(0.853553390593274,0.853553390593274),(0.691341716182545,0.961939766255643),(0.5,1.),(0.308658283817455,0.961939766255643),(0.146446609406726,0.853553390593274),(0.0380602337443566,0.691341716182545),(0.,0.5),(0.0380602337443566,0.308658283817455),(0.146446609406726,0.146446609406726),(0.308658283817455,0.0380602337443567),(0.5,0.),(0.691341716182545,0.0380602337443567),(0.853553390593274,0.146446609406726),(0.961939766255643,0.308658283817455),(1.,0.5),(-0.25,0.),(-0.1875,0.),(-0.125,0.),(-0.0625,0.),(0.,0.),(0.0625,0.),(0.125,0.),(0.1875,0.),(0.25,0.),(0.3125,0.),(0.375,0.),(0.4375,0.),(0.5,0.),(0.5625,0.),(0.625,0.),(0.6875,0.),(0.75,0.),(-0.25,1.),(-0.1875,1.),(-0.125,1.),(-0.0625,1.),(0.,1.),(0.0625,1.),(0.125,1.),(0.1875,1.),(0.25,1.),(0.3125,1.),(0.375,1.),(0.4375,1.),(0.5,1.),(0.5625,1.),(0.625,1.),(0.6875,1.),(0.75,1.)));
#216= IFCSURFACESTYLERENDERING(#218,$,$,$,$,$,$,$,.NOTDEFINED.);
#217= IFCSURFACESTYLEWITHTEXTURES((#219));
#218= IFCCOLOURRGB($,1.,1.,1.);
#219= IFCIMAGETEXTURE(.T.,.T.,'TEXTURE',$,$,'diagnostic.png');

#195= IFCRELCONTAINEDINSPATIALSTRUCTURE('2H$O_OUzH91QU2yMIvZZ$h',$,$,$,(#220),#191);

#206= IFCRELDEFINESBYTYPE('208LEfhXv418VHoTjAT$dC',$,$,$,(#220),#192);

#220= IFCMEMBER('3K_lgFFuT4$xmawQPom25e',$,$,$,$,#221,#222,$,$);
#221= IFCLOCALPLACEMENT(#193,#223);
#222= IFCPRODUCTDEFINITIONSHAPE($,$,(#224));
#223= IFCAXIS2PLACEMENT3D(#225,#226,#227);
#224= IFCSHAPEREPRESENTATION(#2,'Body','MappedRepresentation',(#228));
#225= IFCCARTESIANPOINT((1.92970621585846,1.9296875,0.));
#226= IFCDIRECTION((0.,0.,1.));
#227= IFCDIRECTION((1.,0.,0.));
#228= IFCMAPPEDITEM(#205,#229);
#229= IFCCARTESIANTRANSFORMATIONOPERATOR3D($,$,#230,1.,$);
#230= IFCCARTESIANPOINT((0.,0.,0.));
ENDSEC;

END-ISO-10303-21;

<?php
$groupe = new AcquisitionsProjetsGroupesEquipements(intval($_GET['groupe_id']));
$projet = new AcquisitionsProjets($groupe->acquisition_projet_id);

$filtre = &$_SESSION['acquisition_groupe_' . intval($_GET['groupe_id'])];
$filtre['fiche_id'] = $filtre['fiche_id'] ?? [];
$filtre['code_alternatif_2'] = $filtre['code_alternatif_2'] ?? [];
$filtre['validite'] = $filtre['validite'] ?? '';

if (isset($_POST['clear_filtre'])) {
    $filtre['fiche_id'] = [];
    $filtre['code_alternatif_2'] = [];
    $filtre['validite'] = '';
}

if (isset($_POST['filtre_fiche_id_' . intval($_GET['groupe_id'])])) {
    $filtre['fiche_id'] = $_POST['filtre_fiche_id_' . intval($_GET['groupe_id'])];
} elseif (isset($_POST['filtrer'])) {
    $filtre['fiche_id'] = [];
}

if (isset($_POST['filtre_code_alternatif_2_' . intval($_GET['groupe_id'])])) {
    $filtre['code_alternatif_2'] = $_POST['filtre_code_alternatif_2_' . intval($_GET['groupe_id'])];
} elseif (isset($_POST['filtrer'])) {
    $filtre['code_alternatif_2'] = [];
}

if (isset($_POST['filtre_validite_' . intval($_GET['groupe_id'])])) {
    $filtre['validite'] = $_POST['filtre_validite_' . intval($_GET['groupe_id'])];
} elseif (isset($_POST['filtrer'])) {
    $filtre['validite'] = '';
}

$equipement = null;
if ($groupe->equipement_bio_medicaux_type_id > 0) {
	$equipement = new EquipementsBioMedicauxTypes($groupe->equipement_bio_medicaux_type_id);
}

$sql = "select * from suivis_budgetaires where projet_id = ?";
$suivi = $db->getRow($sql, [getProjetId()]);
if (isset($suivi['id'])) {
	$suiviBudgetaire = new SuivisBudgetaires($suivi['id']);
}

$residuel_comm_totals = array();
$residuel_dist_totals = array();
$fiches_ids = array();
$groupe_fiches = [];

$groupe_donnees = $db->getRow( "CALL acq_groupe_data(?)", [$groupe->id] );
$sql = "SELECT f.id, agq.*, f.code AS local, f.code_alternatif_2
        FROM cache_acq_groupe_quantites agq 
        JOIN fiches f ON f.id = agq.fiche_id
        order by local;";
$fichesAll = $db->getAssoc($sql);

$erreurs = $groupe->getErreurs(array_keys($fichesAll));

$fiches = [];
foreach ($fichesAll as $fiche) {
    $valide = !isset($erreurs[$fiche['fiche_id']]);
    if ($filtre['validite'] <> '') {
        if (($valide && $filtre['validite'] == '0') || (!$valide && $filtre['validite'] == '1')) {
            continue;
        }
    }
    if (count($filtre['fiche_id']) > 0 && !in_array($fiche['fiche_id'], $filtre['fiche_id'])) {
        continue;
    }
    if (count($filtre['code_alternatif_2']) > 0 && !in_array($fiche['code_alternatif_2'], $filtre['code_alternatif_2'])) {
        continue;
    }
    if ((int)$fiche['requise_actuelle'] == 0 && (int)$fiche['existante_actuelle'] == 0 && (int)$fiche['demenager_actuelle'] == 0 && (int)$fiche['commande'] == 0 && (int)$fiche['distribue'] == 0) {
        unset($fichesAll[$fiche['fiche_id']]);
        continue;
    }
    $fiches[] = $fiche;
}

$groupe_fiches[$groupe->id] = $groupe->fiches;

if(!isset($residuel_comm_totals[$groupe->id])) {
    $residuel_comm_totals[$groupe->id] = 0;
}
if(!isset($residuel_dist_totals[$groupe->id])) {
    $residuel_dist_totals[$groupe->id] = 0;
}
$totaux = [];
$totaux['requise_init'] = 0;
$totaux['existante_init'] = 0;
$totaux['demenager_init'] = 0;
$totaux['requise_actuelle'] = 0;
$totaux['existante_actuelle'] = 0;
$totaux['demenager_actuelle'] = 0;
$totaux['remplacement_actuelle'] = 0;
$totaux['col_nouveaux'] = 0;
$totaux['developpement_actuelle'] = 0;
if (isset($suiviBudgetaire)) {
	foreach ($suiviBudgetaire->getPortefeuilles(true) as $portefeuille) {
		$totaux['qt_portefeuille_' . $portefeuille['portefeuille_numero']] = 0;
	}
}
$totaux['qte_totale_budgetee'] = 0;
$totaux['cout_totale_budgete'] = 0;
$totaux['a_traiter'] = 0;
$totaux['deja_traite'] = 0;
$totaux['residuel_com'] = 0;
$totaux['commande'] = 0;
$totaux['cout_norme'] = 0;
$totaux['cout_taxe_net'] = 0;
$totaux['cout_projete'] = 0;
$totaux['quantite_autorise'] = 0;
$totaux['prix_unitaire_autorise'] = 0;
for ($ii = 1; $ii <= 5; $ii++) {
	if (!isset($totaux['cout_supplementaire_' . $ii])) {
		$totaux['cout_supplementaire_' . $ii] = 0.0;
	}
}
for ($ii = 1; $ii <= 10; $ii++) {
	if (!isset($totaux['colonne_supplementaire_detail_' . $ii])) {
		$totaux['colonne_supplementaire_detail_' . $ii] = 0;
	}
	if ($totaux['colonne_supplementaire_detail_' . $ii] <> 'N/A') {
		if (isset($fiches[0]) && isset($fiches[0]['colonne_supplementaire_detail_' . $ii]) && is_numeric($fiches[0]['colonne_supplementaire_detail_' . $ii])) {
			$totaux['colonne_supplementaire_detail_' . $ii] = 0;
		} else {
			$totaux['colonne_supplementaire_detail_' . $ii] = 'N/A';
		}
	}
}

$totaux['distribue'] = 0;
foreach ($fiches as $key => $fiche) {
	$fiche['remplacement_actuelle'] = (int)$fiche['existante_actuelle'] - (int)$fiche['demenager_actuelle'];
	$fiche['col_nouveaux'] = (int)$fiche['requise_actuelle'] - (int)$fiche['demenager_actuelle'];
	$fiche['developpement_actuelle'] = (int)$fiche['requise_actuelle'] - (int)$fiche['existante_actuelle'];
	$fiche['quantite_autorise'] = isset($groupe_fiches[$groupe->id][$fiche['fiche_id']]) ? $groupe_fiches[$groupe->id][$fiche['fiche_id']]['quantite_autorise'] : 0;
	$fiche['prix_unitaire_autorise'] = isset($groupe_fiches[$groupe->id][$fiche['fiche_id']]) ? $groupe_fiches[$groupe->id][$fiche['fiche_id']]['prix_unitaire_autorise'] : 0;
	for ($ii = 1; $ii <= 5; $ii++) {
		$fiche['cout_supplementaire_' . $ii] = isset($groupe_fiches[$groupe->id][$fiche['fiche_id']]) ? $groupe_fiches[$groupe->id][$fiche['fiche_id']]['cout_supplementaire_' . $ii] : 0;
	}
	$fiche['colonne_supplementaire_detail_1'] = isset($groupe_fiches[$groupe->id][$fiche['fiche_id']]) ? $groupe_fiches[$groupe->id][$fiche['fiche_id']]['colonne_supplementaire_detail_1'] : 0;
	$fiche['colonne_supplementaire_detail_2'] = isset($groupe_fiches[$groupe->id][$fiche['fiche_id']]) ? $groupe_fiches[$groupe->id][$fiche['fiche_id']]['colonne_supplementaire_detail_2'] : 0;
	$fiche['colonne_supplementaire_detail_3'] = isset($groupe_fiches[$groupe->id][$fiche['fiche_id']]) ? $groupe_fiches[$groupe->id][$fiche['fiche_id']]['colonne_supplementaire_detail_3'] : 0;
	$fiche['colonne_supplementaire_detail_4'] = isset($groupe_fiches[$groupe->id][$fiche['fiche_id']]) ? $groupe_fiches[$groupe->id][$fiche['fiche_id']]['colonne_supplementaire_detail_4'] : 0;
	$fiche['colonne_supplementaire_detail_5'] = isset($groupe_fiches[$groupe->id][$fiche['fiche_id']]) ? $groupe_fiches[$groupe->id][$fiche['fiche_id']]['colonne_supplementaire_detail_5'] : 0;
	$fiche['colonne_supplementaire_detail_6'] = isset($groupe_fiches[$groupe->id][$fiche['fiche_id']]) ? $groupe_fiches[$groupe->id][$fiche['fiche_id']]['colonne_supplementaire_detail_6'] : 0;
	$fiche['colonne_supplementaire_detail_7'] = isset($groupe_fiches[$groupe->id][$fiche['fiche_id']]) ? $groupe_fiches[$groupe->id][$fiche['fiche_id']]['colonne_supplementaire_detail_7'] : 0;
	$fiche['colonne_supplementaire_detail_8'] = isset($groupe_fiches[$groupe->id][$fiche['fiche_id']]) ? $groupe_fiches[$groupe->id][$fiche['fiche_id']]['colonne_supplementaire_detail_8'] : 0;
	$fiche['colonne_supplementaire_detail_9'] = isset($groupe_fiches[$groupe->id][$fiche['fiche_id']]) ? $groupe_fiches[$groupe->id][$fiche['fiche_id']]['colonne_supplementaire_detail_9'] : 0;
	$fiche['colonne_supplementaire_detail_10'] = isset($groupe_fiches[$groupe->id][$fiche['fiche_id']]) ? $groupe_fiches[$groupe->id][$fiche['fiche_id']]['colonne_supplementaire_detail_10'] : 0;

	$fiches[$key]['remplacement_actuelle'] = $fiche['remplacement_actuelle'];
	$fiches[$key]['col_nouveaux'] = $fiche['col_nouveaux'];
	$fiches[$key]['developpement_actuelle'] = $fiche['developpement_actuelle'];
	$fiches[$key]['residuel_com'] = ($fiche['a_traiter'] - $fiche['deja_traite']);
	$fiches[$key]['quantite_autorise'] = $fiche['quantite_autorise'];
	$fiches[$key]['prix_unitaire_autorise'] = $fiche['prix_unitaire_autorise'];
	for ($ii = 1; $ii <= 5; $ii++) {
		$fiches[$key]['cout_supplementaire_' . $ii] = $fiche['cout_supplementaire_' . $ii];
	}
	$fiches[$key]['colonne_supplementaire_detail_1'] = $fiche['colonne_supplementaire_detail_1'];
	$fiches[$key]['colonne_supplementaire_detail_2'] = $fiche['colonne_supplementaire_detail_2'];
	$fiches[$key]['colonne_supplementaire_detail_3'] = $fiche['colonne_supplementaire_detail_3'];
	$fiches[$key]['colonne_supplementaire_detail_4'] = $fiche['colonne_supplementaire_detail_4'];
	$fiches[$key]['colonne_supplementaire_detail_5'] = $fiche['colonne_supplementaire_detail_5'];
	$fiches[$key]['colonne_supplementaire_detail_6'] = $fiche['colonne_supplementaire_detail_6'];
	$fiches[$key]['colonne_supplementaire_detail_7'] = $fiche['colonne_supplementaire_detail_7'];
	$fiches[$key]['colonne_supplementaire_detail_8'] = $fiche['colonne_supplementaire_detail_8'];
	$fiches[$key]['colonne_supplementaire_detail_9'] = $fiche['colonne_supplementaire_detail_9'];
	$fiches[$key]['colonne_supplementaire_detail_10'] = $fiche['colonne_supplementaire_detail_10'];

	$residuel_comm_totals[$groupe->id] += ($fiche['a_traiter'] - $fiche['deja_traite']);
    $residuel_dist_totals[$groupe->id] += $fiche['residuel_dist'];
    $fiches_ids[$fiche['fiche_id']] = $fiche['fiche_id'];

	$totaux['requise_init'] += $fiche['requise_init'];
	$totaux['existante_init'] += $fiche['existante_init'];
	$totaux['demenager_init'] += $fiche['demenager_init'];
	$totaux['requise_actuelle'] += $fiche['requise_actuelle'];
	$totaux['existante_actuelle'] += $fiche['existante_actuelle'];
	$totaux['demenager_actuelle'] += $fiche['demenager_actuelle'];
	$totaux['remplacement_actuelle'] += $fiche['remplacement_actuelle'];
	$totaux['col_nouveaux'] += $fiche['col_nouveaux'];
	$totaux['developpement_actuelle'] += $fiche['developpement_actuelle'];
	if (isset($suiviBudgetaire)) {
		foreach ($suiviBudgetaire->getPortefeuilles(true) as $portefeuille) {
			$totaux['qt_portefeuille_' . $portefeuille['portefeuille_numero']] += $fiche['qt_portefeuille_' . $portefeuille['portefeuille_numero']];
		}
	}
    $totaux['qte_totale_budgetee'] += $fiche['qte_totale_budgetee'];
    $totaux['cout_totale_budgete'] += $fiche['cout_totale_budgete'];
	$totaux['a_traiter'] += $fiche['a_traiter'];
	$totaux['deja_traite'] += $fiche['deja_traite'];
	$totaux['residuel_com'] += ($fiche['a_traiter'] - $fiche['deja_traite']);
	$totaux['commande'] += $fiche['commande'];
	$totaux['cout_norme'] += ((int)$fiche['commande'] * $groupe->cout_norme);
	$totaux['cout_taxe_net'] += $groupe->cout_taxe_net;
	$totaux['cout_projete'] += $groupe->cout_projete;
	$totaux['quantite_autorise'] += $fiche['quantite_autorise'];
	$totaux['prix_unitaire_autorise'] += $fiche['prix_unitaire_autorise'];

	for ($ii = 1; $ii <= 5; $ii++) {
		$totaux['cout_supplementaire_' . $ii] += (float)$fiche['cout_supplementaire_' . $ii];
	}

	for ($ii = 1; $ii <= 10; $ii++) {
		if ($totaux['colonne_supplementaire_detail_' . $ii] <> 'N/A') {
			if (is_numeric($fiche['colonne_supplementaire_detail_' . $ii])) {
				$totaux['colonne_supplementaire_detail_' . $ii] += $fiche['colonne_supplementaire_detail_' . $ii];
			} else {
				$totaux['colonne_supplementaire_detail_' . $ii] = 'N/A';
			}
		}
	}
	$totaux['distribue'] += $fiche['distribue'];
}

$qte_a_commander_totale = 0;
foreach($fiches as $i => $fiche) {
	$qte_a_commander_totale += $fiche['commande'];
}

$qte_distribuee_totale = 0;
foreach($fiches as $i => $fiche) {
	$qte_distribuee_totale += isset($fiche['distribue']) && dm_strlen($fiche['distribue']) > 0 ? (int)$fiche['distribue'] : 0;
}
$qte_residuel_dist_total = $groupe->quantite_disponible - $qte_distribuee_totale;

$admin = AcquisitionsProjetsAdministrations::getAdminForProjet();

$fiches_infos = Fiches::getListe(['ids' => $fiches_ids]);
?>
<div style="background-image: url(css/light/images/paper_02.png); border-color: #dddddd #e7e7e7 #dddddd #e7e7e7;">
    <div style="width: 50%; float: left; margin-bottom: 10px;">
        &nbsp;&nbsp;&nbsp;&nbsp;
        <select id="filtre_fiche_id_<?= $groupe->id ?>" placeholder="- <?= txt('Locaux') ?> -" multiple style="width: 300px!important;">
            <?
            $codesAlternatifs = [];
            foreach ($fichesAll as $i => $local) {
                if (dm_strlen($local->code_alternatif_2) > 0) {
                    $codesAlternatifs[] = $local->code_alternatif_2;
                }
                $txt_supp = '';
                if (isset($fiches_infos[$local->fiche_id])) {
                    $txt_supp .= ' - ';
                    $fiche_info = $fiches_infos[$local->fiche_id];
                    $bat_niv_split = dm_explode(' - ', $fiche_info->batiment_niveau_element);
                    foreach ($bat_niv_split as $iter => $bat_niv_spl) {
                        if ($iter > 0) {
                            $txt_supp .= ($iter > 1 ? ' - ' : '') . $bat_niv_spl;
                        }
                    }
                }
                $sel = in_array($local->fiche_id, $filtre['fiche_id']) ? ' selected="selected"' : '';
                ?>
                <option value="<?= $local->fiche_id ?>"<?= $sel ?>><?= $local->local . $txt_supp ?></option>
                <?
            }
            ?>
        </select>&nbsp;&nbsp;&nbsp;&nbsp;
        <? if ($admin->isColonneVisible('col_code_alternatif_2')): ?>
            <select id="filtre_code_alternatif_2_<?= $groupe->id ?>" placeholder="- <?= txt('Code alternatif 2') ?> -" multiple style="width: 160px!important;">
                <?
                foreach ($codesAlternatifs as $i => $codeAlternatif) {
                    $sel = in_array($codeAlternatif, $filtre['code_alternatif_2']) ? ' selected="selected"' : '';
                    ?>
                    <option value="<?= $codeAlternatif ?>"<?= $sel ?>><?= $codeAlternatif ?></option>
                    <?
                }
                ?>
            </select>&nbsp;&nbsp;&nbsp;&nbsp;
        <? endif ?>
        <? if ($admin->isColonneVisible('financement_complete')): ?>
            <select id="filtre_validite_<?= $groupe->id ?>" style="width: 90px!important;">
                <option value=""<?= ($filtre['validite'] == '' ? ' selected="selected"' : '') ?>>- <?= txt('Validité') ?> -</option>
                <option value="1"<?= ($filtre['validite'] == '1' ? ' selected="selected"' : '') ?>><?= txt('Oui') ?></option>
                <option value="0"<?= ($filtre['validite'] == '0' ? ' selected="selected"' : '') ?>><?= txt('Non') ?></option>
            </select>&nbsp;&nbsp;&nbsp;&nbsp;
        <? endif ?>
        <span>
            <input type="button" class="filtrer_fiches" data-groupe_id="<?= $groupe->id ?>" name="filtrer" value="Filtrer" style="width: 70px!important; height: 22px!important;" />
            <a class="clear_filtre_fiches" data-groupe_id="<?= $groupe->id ?>" style="display: inline!important; position: relative; top: 10px;" title="" data-text="Libérer tous les filtres">
                <img src="css/images/icons/dark/bended_arrow_left.png"></a>
        </span>
    </div>
    <div style="width: 48%; padding: 10px; text-align: right; float: right;">
        <span style="display: <?= ($admin->isColonneVisible('col_residuel_comm') ? '' : 'none') ?>;"><?= $admin->getLabelForColonne('col_residuel_comm') ?>: <span style="color: blue; font-weight: bold"><?= $residuel_comm_totals[$groupe->id] ?></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
        <span style="display: <?= ($admin->isColonneVisible('col_total_recu') ? '' : 'none') ?>;"><?= $admin->getLabelForColonne('col_total_recu') ?>: <span style="color: blue; font-weight: bold"><?= (int)$groupe->quantite_disponible ?></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
        <span style="display: <?= ($admin->isColonneVisible('col_total_distribue') ? '' : 'none') ?>;"><?= $admin->getLabelForColonne('col_total_distribue') ?>: <span style="color: blue; font-weight: bold"><?= $qte_distribuee_totale ?></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
        <span style="display: <?= ($admin->isColonneVisible('col_residuel_distribue') ? '' : 'none') ?>;"><?= $admin->getLabelForColonne('col_residuel_distribue') ?>: <span style="color: blue; font-weight: bold"><?= $qte_residuel_dist_total ?></span></span>
    </div><br style="clear: both;" />
</div>
<table id="datatable_<?= $groupe->id ?>" style="background-image: url(css/light/images/paper_02.png);">
    <thead><tr><th width="300" style="display: <?= ($admin->isColonneVisible('col_local') ? '' : 'none') ?>; border-bottom: none!important; text-shadow: 0 -1px 0 #ffffff; border-color: #dddddd #e7e7e7 #dddddd #e7e7e7; font-weight: 700; padding: 12px 5px; background-image: url(css/light/images/paper_02.png);">
            <?= $admin->getLabelForColonne('col_local') ?>
        </th><th width="75" style="display: <?= ($admin->isColonneVisible('col_code_alternatif_2') ? '' : 'none') ?>; border-bottom: none!important; text-shadow: 0 -1px 0 #ffffff; border-color: #dddddd #e7e7e7 #dddddd #e7e7e7; font-weight: 700; padding: 12px 5px; background-image: url(css/light/images/paper_02.png);">
            <?= $admin->getLabelForColonne('col_code_alternatif_2') ?>
        </th><th width="75" style="display: <?= ($admin->isColonneVisible('financement_complete') ? '' : 'none') ?>;text-align: center; border-bottom: none!important; text-shadow: 0 -1px 0 #ffffff; border-color: #dddddd #e7e7e7 #dddddd #e7e7e7; font-weight: 700; padding: 12px 5px; background-image: url(css/light/images/paper_02.png);">
            <?= $admin->getLabelForColonne('financement_complete') ?>
        </th><th width="75" style="display: <?= ($admin->isColonneVisible('col_requis_j1') ? '' : 'none') ?>;text-align: center; border-bottom: none!important; text-shadow: 0 -1px 0 #ffffff; border-color: #dddddd #e7e7e7 #dddddd #e7e7e7; font-weight: 700; padding: 12px 5px; background-image: url(css/light/images/paper_02.png);">
            <?= $admin->getLabelForColonne('col_requis_j1') ?>
        </th><th width="75" style="display: <?= ($admin->isColonneVisible('col_existante_j1') ? '' : 'none') ?>;text-align: center; border-bottom: none!important; text-shadow: 0 -1px 0 #ffffff; border-color: #dddddd #e7e7e7 #dddddd #e7e7e7; font-weight: 700; padding: 12px 5px; background-image: url(css/light/images/paper_02.png);">
            <?= $admin->getLabelForColonne('col_existante_j1') ?>
        </th><th width="75" style="display: <?= ($admin->isColonneVisible('col_conserve_j1') ? '' : 'none') ?>;text-align: center; border-bottom: none!important; text-shadow: 0 -1px 0 #ffffff; border-color: #dddddd #e7e7e7 #dddddd #e7e7e7; font-weight: 700; padding: 12px 5px; background-image: url(css/light/images/paper_02.png);">
            <?= $admin->getLabelForColonne('col_conserve_j1') ?>
        </th><th width="75" style="display: <?= ($admin->isColonneVisible('requise_actuelle') ? '' : 'none') ?>;text-align: center; border-bottom: none!important; text-shadow: 0 -1px 0 #ffffff; border-color: #dddddd #e7e7e7 #dddddd #e7e7e7; font-weight: 700; padding: 12px 5px; background-image: url(css/light/images/paper_02.png);">
            <?= $admin->getLabelForColonne('requise_actuelle') ?>
        </th><th width="75" style="display: <?= ($admin->isColonneVisible('existante_actuelle') ? '' : 'none') ?>;text-align: center; border-bottom: none!important; text-shadow: 0 -1px 0 #ffffff; border-color: #dddddd #e7e7e7 #dddddd #e7e7e7; font-weight: 700; padding: 12px 5px; background-image: url(css/light/images/paper_02.png);">
            <?= $admin->getLabelForColonne('existante_actuelle') ?>
        </th><th width="75" style="display: <?= ($admin->isColonneVisible('demenager_actuelle') ? '' : 'none') ?>;text-align: center; border-bottom: none!important; text-shadow: 0 -1px 0 #ffffff; border-color: #dddddd #e7e7e7 #dddddd #e7e7e7; font-weight: 700; padding: 12px 5px; background-image: url(css/light/images/paper_02.png);">
            <?= $admin->getLabelForColonne('demenager_actuelle') ?>
        </th><th width="100" style="display: <?= ($admin->isColonneVisible('remplacement_actuelle') ? '' : 'none') ?>;text-align: center; border-bottom: none!important; text-shadow: 0 -1px 0 #ffffff; border-color: #dddddd #e7e7e7 #dddddd #e7e7e7; font-weight: 700; padding: 12px 5px; background-image: url(css/light/images/paper_02.png);">
            <?= $admin->getLabelForColonne('remplacement_actuelle') ?>
        </th><th width="100" style="display: <?= ($admin->isColonneVisible('col_nouveaux') ? '' : 'none') ?>;text-align: center; border-bottom: none!important; text-shadow: 0 -1px 0 #ffffff; border-color: #dddddd #e7e7e7 #dddddd #e7e7e7; font-weight: 700; padding: 12px 5px; background-image: url(css/light/images/paper_02.png);">
            <?= $admin->getLabelForColonne('col_nouveaux') ?>
        </th><th width="100" style="display: <?= ($admin->isColonneVisible('developpement_actuelle') ? '' : 'none') ?>;text-align: center; border-bottom: none!important; text-shadow: 0 -1px 0 #ffffff; border-color: #dddddd #e7e7e7 #dddddd #e7e7e7; font-weight: 700; padding: 12px 5px; background-image: url(css/light/images/paper_02.png);">
            <?= $admin->getLabelForColonne('developpement_actuelle') ?>
        </th>
        <?
        if (isset($suiviBudgetaire)) {
            foreach ($suiviBudgetaire->getPortefeuilles(true) as $portefeuille) {
                ?>
                <th width="75" style="display: <?= ($admin->isColonneVisible('qt_portefeuille_' . $portefeuille['portefeuille_numero']) ? '' : 'none') ?>;text-align: center; border-bottom: none!important; text-shadow: 0 -1px 0 #ffffff; border-color: #dddddd #e7e7e7 #dddddd #e7e7e7; font-weight: 700; padding: 12px 5px; background-image: url(css/light/images/paper_02.png);">
                    <?= $admin->getLabelForColonne('qt_portefeuille_' . $portefeuille['portefeuille_numero']) ?>
                </th>
                <?
            }
        }
        ?>

        <th width="75" style="display: <?= ($admin->isColonneVisible('col_qte_totale_budgetee') ? '' : 'none') ?>;text-align: center; border-bottom: none!important; text-shadow: 0 -1px 0 #ffffff; border-color: #dddddd #e7e7e7 #dddddd #e7e7e7; font-weight: 700; padding: 12px 5px; background-image: url(css/light/images/paper_02.png);">
            <?= $admin->getLabelForColonne('col_qte_totale_budgetee') ?>
        </th>

        <th width="75" style="display: <?= ($admin->isColonneVisible('col_cout_totale_budgete') ? '' : 'none') ?>;text-align: center; border-bottom: none!important; text-shadow: 0 -1px 0 #ffffff; border-color: #dddddd #e7e7e7 #dddddd #e7e7e7; font-weight: 700; padding: 12px 5px; background-image: url(css/light/images/paper_02.png);">
            <?= $admin->getLabelForColonne('col_cout_totale_budgete') ?>
        </th>

        <th width="75" style="display: <?= ($admin->isColonneVisible('col_a_traiter') ? '' : 'none') ?>;text-align: center; border-bottom: none!important; text-shadow: 0 -1px 0 #ffffff; border-color: #dddddd #e7e7e7 #dddddd #e7e7e7; font-weight: 700; padding: 12px 5px; background-image: url(css/light/images/paper_02.png);">
		    <?= $admin->getLabelForColonne('col_a_traiter') ?>
        </th>

        <th width="75" style="display: <?= ($admin->isColonneVisible('col_deja_traite') ? '' : 'none') ?>;text-align: center; border-bottom: none!important; text-shadow: 0 -1px 0 #ffffff; border-color: #dddddd #e7e7e7 #dddddd #e7e7e7; font-weight: 700; padding: 12px 5px; background-image: url(css/light/images/paper_02.png);">
            <?= $admin->getLabelForColonne('col_deja_traite') ?>
        </th>

        <th width="100" style="display: <?= ($admin->isColonneVisible('col_residuel_com') ? '' : 'none') ?>;text-align: center; border-bottom: none!important; text-shadow: 0 -1px 0 #ffffff; border-color: #dddddd #e7e7e7 #dddddd #e7e7e7; font-weight: 700; padding: 12px 5px; background-image: url(css/light/images/paper_02.png);">
            <?= $admin->getLabelForColonne('col_residuel_com') ?>
        </th>


        <th width="80" style="display: <?= ($admin->isColonneVisible('col_a_commander') ? '' : 'none') ?>;text-align: center; border-bottom: none!important; text-shadow: 0 -1px 0 #ffffff; border-color: #dddddd #e7e7e7 #dddddd #e7e7e7; font-weight: 700; padding: 12px 5px; background-image: url(css/light/images/paper_02.png);">
            <?= $admin->getLabelForColonne('col_a_commander') ?><br />
            <?php if (havePermission(array('administration_quantite_commander_acquisitions')) && !$groupe->isLocked()){ ?>
                <input type="button" class="tout_associer" value="<?=txt('Tout soumettre')?>" data-parent_id="groupe_<?=$groupe->id?>" data-projet_numero="<?=$projet->numero?>" data-projet_id="<?=$projet->id?>" style="padding: 0 5px; margin-left: 5px;" />
            <?php } ?>
        </th>

        <th width="100" style="display: <?= ($admin->isColonneVisible('cout_norme') ? '' : 'none') ?>;text-align: center; border-bottom: none!important; text-shadow: 0 -1px 0 #ffffff; border-color: #dddddd #e7e7e7 #dddddd #e7e7e7; font-weight: 700; padding: 12px 5px; background-image: url(css/light/images/paper_02.png);">
            <?= $admin->getLabelForColonne('cout_norme') ?>
        </th>
        
    	<th width="100" style="display: <?= ($admin->isColonneVisible('cout_taxe_net') ? '' : 'none') ?>;text-align: center; border-bottom: none!important; text-shadow: 0 -1px 0 #ffffff; border-color: #dddddd #e7e7e7 #dddddd #e7e7e7; font-weight: 700; padding: 12px 5px; background-image: url(css/light/images/paper_02.png);">
            <?= $admin->getLabelForColonne('cout_taxe_net') ?>
        </th>
    
        <th width="100" style="display: <?= ($admin->isColonneVisible('cout_projete') ? '' : 'none') ?>;text-align: center; border-bottom: none!important; text-shadow: 0 -1px 0 #ffffff; border-color: #dddddd #e7e7e7 #dddddd #e7e7e7; font-weight: 700; padding: 12px 5px; background-image: url(css/light/images/paper_02.png);">
            <?= $admin->getLabelForColonne('cout_projete') ?>
        </th>

        <th width="100" style="display: <?= ($admin->isColonneVisible('quantite_autorise') ? '' : 'none') ?>;text-align: center; border-bottom: none!important; text-shadow: 0 -1px 0 #ffffff; border-color: #dddddd #e7e7e7 #dddddd #e7e7e7; font-weight: 700; padding: 12px 5px; background-image: url(css/light/images/paper_02.png);">
            <?= $admin->getLabelForColonne('quantite_autorise') ?>
        </th>

        <th width="100" style="display: <?= ($admin->isColonneVisible('prix_unitaire_autorise') ? '' : 'none') ?>;text-align: center; border-bottom: none!important; text-shadow: 0 -1px 0 #ffffff; border-color: #dddddd #e7e7e7 #dddddd #e7e7e7; font-weight: 700; padding: 12px 5px; background-image: url(css/light/images/paper_02.png);">
            <?= $admin->getLabelForColonne('prix_unitaire_autorise') ?>
        </th>
        <th width="100" style="display: <?= ($admin->isColonneVisible('colonne_supplementaire_detail_1') ? '' : 'none') ?>;text-align: center; border-bottom: none!important; text-shadow: 0 -1px 0 #ffffff; border-color: #dddddd #e7e7e7 #dddddd #e7e7e7; font-weight: 700; padding: 12px 5px; background-image: url(css/light/images/paper_02.png);">
            <?= $admin->getLabelForColonne('colonne_supplementaire_detail_1') ?>
        </th>
        <th width="100" style="display: <?= ($admin->isColonneVisible('colonne_supplementaire_detail_2') ? '' : 'none') ?>;text-align: center; border-bottom: none!important; text-shadow: 0 -1px 0 #ffffff; border-color: #dddddd #e7e7e7 #dddddd #e7e7e7; font-weight: 700; padding: 12px 5px; background-image: url(css/light/images/paper_02.png);">
            <?= $admin->getLabelForColonne('colonne_supplementaire_detail_2') ?>
        </th>
        <th width="100" style="display: <?= ($admin->isColonneVisible('colonne_supplementaire_detail_3') ? '' : 'none') ?>;text-align: center; border-bottom: none!important; text-shadow: 0 -1px 0 #ffffff; border-color: #dddddd #e7e7e7 #dddddd #e7e7e7; font-weight: 700; padding: 12px 5px; background-image: url(css/light/images/paper_02.png);">
            <?= $admin->getLabelForColonne('colonne_supplementaire_detail_3') ?>
        </th>
        <th width="100" style="display: <?= ($admin->isColonneVisible('colonne_supplementaire_detail_4') ? '' : 'none') ?>;text-align: center; border-bottom: none!important; text-shadow: 0 -1px 0 #ffffff; border-color: #dddddd #e7e7e7 #dddddd #e7e7e7; font-weight: 700; padding: 12px 5px; background-image: url(css/light/images/paper_02.png);">
            <?= $admin->getLabelForColonne('colonne_supplementaire_detail_4') ?>
        </th>
        <th width="100" style="display: <?= ($admin->isColonneVisible('colonne_supplementaire_detail_5') ? '' : 'none') ?>;text-align: center; border-bottom: none!important; text-shadow: 0 -1px 0 #ffffff; border-color: #dddddd #e7e7e7 #dddddd #e7e7e7; font-weight: 700; padding: 12px 5px; background-image: url(css/light/images/paper_02.png);">
            <?= $admin->getLabelForColonne('colonne_supplementaire_detail_5') ?>
        </th>
        <th width="100" style="display: <?= ($admin->isColonneVisible('colonne_supplementaire_detail_6') ? '' : 'none') ?>;text-align: center; border-bottom: none!important; text-shadow: 0 -1px 0 #ffffff; border-color: #dddddd #e7e7e7 #dddddd #e7e7e7; font-weight: 700; padding: 12px 5px; background-image: url(css/light/images/paper_02.png);">
            <?= $admin->getLabelForColonne('colonne_supplementaire_detail_6') ?>
        </th>
        <th width="100" style="display: <?= ($admin->isColonneVisible('colonne_supplementaire_detail_7') ? '' : 'none') ?>;text-align: center; border-bottom: none!important; text-shadow: 0 -1px 0 #ffffff; border-color: #dddddd #e7e7e7 #dddddd #e7e7e7; font-weight: 700; padding: 12px 5px; background-image: url(css/light/images/paper_02.png);">
            <?= $admin->getLabelForColonne('colonne_supplementaire_detail_7') ?>
        </th>
        <th width="100" style="display: <?= ($admin->isColonneVisible('colonne_supplementaire_detail_8') ? '' : 'none') ?>;text-align: center; border-bottom: none!important; text-shadow: 0 -1px 0 #ffffff; border-color: #dddddd #e7e7e7 #dddddd #e7e7e7; font-weight: 700; padding: 12px 5px; background-image: url(css/light/images/paper_02.png);">
            <?= $admin->getLabelForColonne('colonne_supplementaire_detail_8') ?>
        </th>
        <th width="100" style="display: <?= ($admin->isColonneVisible('colonne_supplementaire_detail_9') ? '' : 'none') ?>;text-align: center; border-bottom: none!important; text-shadow: 0 -1px 0 #ffffff; border-color: #dddddd #e7e7e7 #dddddd #e7e7e7; font-weight: 700; padding: 12px 5px; background-image: url(css/light/images/paper_02.png);">
            <?= $admin->getLabelForColonne('colonne_supplementaire_detail_91') ?>
        </th>
        <th width="100" style="display: <?= ($admin->isColonneVisible('colonne_supplementaire_detail_10') ? '' : 'none') ?>;text-align: center; border-bottom: none!important; text-shadow: 0 -1px 0 #ffffff; border-color: #dddddd #e7e7e7 #dddddd #e7e7e7; font-weight: 700; padding: 12px 5px; background-image: url(css/light/images/paper_02.png);">
            <?= $admin->getLabelForColonne('colonne_supplementaire_detail_10') ?>
        </th>

        <th width="100" style="display: <?= ($admin->isColonneVisible('col_residuel_dist') ? '' : 'none') ?>;text-align: center; border-bottom: none!important; text-shadow: 0 -1px 0 #ffffff; border-color: #dddddd #e7e7e7 #dddddd #e7e7e7; font-weight: 700; padding: 12px 5px; background-image: url(css/light/images/paper_02.png);">
		    <?= $admin->getLabelForColonne('col_residuel_dist') ?>
        </th>

        <th width="80" style="display: <?= ($admin->isColonneVisible('col_a_distribuer') ? '' : 'none') ?>;text-align: center; border-bottom: none!important; text-shadow: 0 -1px 0 #ffffff; border-color: #dddddd #e7e7e7 #dddddd #e7e7e7; font-weight: 700; padding: 12px 5px; background-image: url(css/light/images/paper_02.png);">
            <?= $admin->getLabelForColonne('col_a_distribuer') ?><br />
            <?php if (havePermission(['administration_quantite_recus_acquisitions', 'administration_quantite_approuvees'])) { ?>
                <input type="button" class="tout_distribuer" value="<?=txt('Tout accepter')?>" data-parent_id="groupe_<?=$groupe->id?>" style="padding: 0 5px; margin-left: 5px;" data-projet_numero="<?=$projet->numero?>" data-projet_id="<?=$projet->id?>" />
            <?php } ?>
        </th>

        <th style="width: 50px; text-align: center; vertical-align: middle; border-bottom: none!important; text-shadow: 0 -1px 0 #ffffff; border-color: #dddddd #e7e7e7 #dddddd #e7e7e7; font-weight: 700; padding: 12px 5px; background-image: url(css/light/images/paper_02.png);">

        </th>
    </tr>
    </thead>
    <tbody>
    <tr style="background-color: #ffffe9;">
        <td width="300" height="35"
            style="text-align: right; display: <?= ($admin->isColonneVisible('col_local') ? '' : 'none') ?>;padding: 0px 4px!important;">
			<?= txt('Totaux') ?>:
        </td>
        <td style="display: <?= ($admin->isColonneVisible('col_code_alternatif_2') ? '' : 'none') ?>;text-align: center; padding: 0px 4px!important;"
            width="75">
            &nbsp;
        </td>
        <td class=""
            style="display: <?= ($admin->isColonneVisible('financement_complete') ? '' : 'none') ?>;text-align: center;"
            width="75">
        </td>
        <td style="display: <?= ($admin->isColonneVisible('col_requis_j1') ? '' : 'none') ?>;text-align: center; padding: 0px 4px!important;"
            width="75">
			<?= $totaux['requise_init'] ?>
        </td>
        <td style="display: <?= ($admin->isColonneVisible('col_existante_j1') ? '' : 'none') ?>;text-align: center; padding: 0px 4px!important;"
            width="75">
			<?= $totaux['existante_init'] ?>
        </td>
        <td style="display: <?= ($admin->isColonneVisible('col_conserve_j1') ? '' : 'none') ?>;text-align: center; padding: 0px 4px!important;"
            width="75">
			<?= $totaux['demenager_init'] ?>
        </td>
        <td class="<?= ($totaux['requise_init'] <> $totaux['requise_actuelle'] ? 'isalert' : '') ?>"
            style="display: <?= ($admin->isColonneVisible('requise_actuelle') ? '' : 'none') ?>;text-align: center;"
            width="75">
			<?= $totaux['requise_actuelle'] ?>
        </td>
        <td class=""
            style="display: <?= ($admin->isColonneVisible('existante_actuelle') ? '' : 'none') ?>;text-align: center;"
            width="75">
			<?= $totaux['existante_actuelle'] ?>
        </td>
        <td class="<?= ($totaux['demenager_init'] <> $totaux['demenager_actuelle'] ? 'isalert' : '') ?>"
            style="display: <?= ($admin->isColonneVisible('demenager_actuelle') ? '' : 'none') ?>;text-align: center;"
            width="75">
			<?= $totaux['demenager_actuelle'] ?>
        </td>
        <td class="<?= ($totaux['remplacement_actuelle'] < 0 ? 'isalert' : '') ?>"
            style="display: <?= ($admin->isColonneVisible('remplacement_actuelle') ? '' : 'none') ?>;text-align: center;"
            width="100">
			<?= $totaux['remplacement_actuelle'] ?>
        </td>
        <td class="<?= ($totaux['col_nouveaux'] < 0 ? 'isalert' : '') ?>"
            style="display: <?= ($admin->isColonneVisible('col_nouveaux') ? '' : 'none') ?>;text-align: center;"
            width="100">
			<?= $totaux['col_nouveaux'] ?>
        </td>
        <td class="<?= ($totaux['developpement_actuelle'] < 0 ? 'isalert' : '') ?>"
            style="display: <?= ($admin->isColonneVisible('developpement_actuelle') ? '' : 'none') ?>;text-align: center;"
            width="100">
			<?= $totaux['developpement_actuelle'] ?>
        </td>
		<?
		if (isset($suiviBudgetaire)) {
			foreach ($suiviBudgetaire->getPortefeuilles(true) as $portefeuille) {
				?>
                <td class=""
                    style="display: <?= ($admin->isColonneVisible('qt_portefeuille_' . $portefeuille['portefeuille_numero']) ? '' : 'none') ?>;text-align: center;"
                    width="75">
					<?= $totaux['qt_portefeuille_' . $portefeuille['portefeuille_numero']] ?>
                </td>
				<?
			}
		}
		?>
        <td style="display: <?= ($admin->isColonneVisible('col_qte_totale_budgetee') ? '' : 'none') ?>;text-align: center;" width="75">
            <?= $totaux['qte_totale_budgetee'] ?>
        </td>
        <td style="display: <?= ($admin->isColonneVisible('col_cout_totale_budgete') ? '' : 'none') ?>;text-align: center;" width="75">
            <?= dm_number_format($totaux['cout_totale_budgete'], 2, ',', ' ') ?>$
        </td>
        <td
                style="display: <?= ($admin->isColonneVisible('col_a_traiter') ? '' : 'none') ?>;text-align: center;"
                width="75">
		    <?= $totaux['a_traiter'] ?>
        </td>
        <td
            style="display: <?= ($admin->isColonneVisible('col_deja_traite') ? '' : 'none') ?>;text-align: center;"
            width="75">
			<?= $totaux['deja_traite'] ?>
        </td>
		<?
		$class = $totaux['residuel_com'] == 0 ? '' : 'isalert';
		$class = $totaux['residuel_com'] < 0 ? 'isalertNegatif' : $class;
		?>
        <td style="display: <?= ($admin->isColonneVisible('col_residuel_com') ? '' : 'none') ?>;text-align: center;"
            width="100" class="residuel_com <?= $class ?>">
			<?= $totaux['residuel_com'] ?>
        </td>
        <td style="width: 80px!important;display: <?= ($admin->isColonneVisible('col_a_commander') ? '' : 'none') ?>;text-align: center;"
            width="200">
				<?= $totaux['commande'] ?>
        </td>
        <td style="display: <?= ($admin->isColonneVisible('cout_norme') ? '' : 'none') ?>;text-align: right; white-space: nowrap;"
            width="100">
			<?= dm_number_format($totaux['cout_norme'], 2, ',', ' ') ?>$
        </td>
        <td style="display: <?= ($admin->isColonneVisible('cout_taxe_net') ? '' : 'none') ?>;text-align: right; white-space: nowrap;"
            width="100">
			<?= dm_number_format($totaux['cout_taxe_net'], 2, ',', ' ') ?>$
        </td>
        <td style="display: <?= ($admin->isColonneVisible('cout_projete') ? '' : 'none') ?>;text-align: right; white-space: nowrap;"
            width="100">
			<?= dm_number_format($totaux['cout_projete'], 2, ',', ' ') ?>$
        </td>
        <td style="display: <?= ($admin->isColonneVisible('quantite_autorise') ? '' : 'none') ?>;text-align: center;"
            width="100">
			<?= $totaux['quantite_autorise'] ?>
        </td>
        <td style="display: <?= ($admin->isColonneVisible('prix_unitaire_autorise') ? '' : 'none') ?>;text-align: right; white-space: nowrap;"
            width="100">
			<?= (dm_strlen($totaux['prix_unitaire_autorise']) > 0 ? dm_number_format($totaux['prix_unitaire_autorise'], 2, ',', ' ') . '$' : '') ?>
        </td>
        <td style="display: <?= ($admin->isColonneVisible('colonne_supplementaire_detail_1') ? '' : 'none') ?>;text-align: center;"
            width="100">
			<?= $totaux['colonne_supplementaire_detail_1'] ?>
        </td>
        <td style="display: <?= ($admin->isColonneVisible('colonne_supplementaire_detail_2') ? '' : 'none') ?>;text-align: center;"
            width="100">
			<?= $totaux['colonne_supplementaire_detail_2'] ?>
        </td>
        <td style="display: <?= ($admin->isColonneVisible('colonne_supplementaire_detail_3') ? '' : 'none') ?>;text-align: center;"
            width="100">
			<?= $totaux['colonne_supplementaire_detail_3'] ?>
        </td>
        <td style="display: <?= ($admin->isColonneVisible('colonne_supplementaire_detail_4') ? '' : 'none') ?>;text-align: center;"
            width="100">
			<?= $totaux['colonne_supplementaire_detail_4'] ?>
        </td>
        <td style="display: <?= ($admin->isColonneVisible('colonne_supplementaire_detail_5') ? '' : 'none') ?>;text-align: center;"
            width="100">
			<?= $totaux['colonne_supplementaire_detail_5'] ?>
        </td>
        <td style="display: <?= ($admin->isColonneVisible('colonne_supplementaire_detail_6') ? '' : 'none') ?>;text-align: center;"
            width="100">
			<?= $totaux['colonne_supplementaire_detail_6'] ?>
        </td>
        <td style="display: <?= ($admin->isColonneVisible('colonne_supplementaire_detail_7') ? '' : 'none') ?>;text-align: center;"
            width="100">
			<?= $totaux['colonne_supplementaire_detail_7'] ?>
        </td>
        <td style="display: <?= ($admin->isColonneVisible('colonne_supplementaire_detail_8') ? '' : 'none') ?>;text-align: center;"
            width="100">
			<?= $totaux['colonne_supplementaire_detail_8'] ?>
        </td>
        <td style="display: <?= ($admin->isColonneVisible('colonne_supplementaire_detail_9') ? '' : 'none') ?>;text-align: center;"
            width="100">
			<?= $totaux['colonne_supplementaire_detail_9'] ?>
        </td>
        <td style="display: <?= ($admin->isColonneVisible('colonne_supplementaire_detail_10') ? '' : 'none') ?>;text-align: center;"
            width="100">
			<?= $totaux['colonne_supplementaire_detail_10'] ?>
        </td>
	    <?
	    $residuel_dist = $totaux['commande'] - $totaux['distribue'];
	    $class = $residuel_dist == 0 ? '' : 'isalert';
	    $class = $residuel_dist < 0 ? 'isalertNegatif' : $class;
	    ?>
        <td style="display: <?= ($admin->isColonneVisible('col_residuel_dist') ? '' : 'none') ?>;text-align: center;"
            width="100" class="residuel_dist <?= $class ?>">
		    <?= $residuel_dist ?>
        </td>
        <td style="width: 80px!important; display: <?= ($admin->isColonneVisible('col_a_distribuer') ? '' : 'none') ?>;text-align: center;"
            width="80">
			<?= $totaux['distribue'] ?>
        </td>
        <td style="width: 20px;">
        </td>
    </tr>
    <?
    $fiche_id_developpementDeblances = AcquisitionsProjetsGroupesEquipements::getLocauxDeveloppementDeblances($groupe->id);
    $fiche_id_remplacementDeblances = AcquisitionsProjetsGroupesEquipements::getLocauxRemplacementDeblances($groupe->id);
    $fiche_id_aReviser = array_merge($fiche_id_developpementDeblances, $fiche_id_remplacementDeblances);
    foreach ($fiches as $fiche) {
        if ((int)$fiche['requise_actuelle'] == 0 && (int)$fiche['existante_actuelle'] == 0 && (int)$fiche['demenager_actuelle'] == 0 && (int)$fiche['commande'] == 0 && (int)$fiche['distribue'] == 0) {
            continue;
        }
        $messagesErreurs = [];
        $valide = !isset($erreurs[$fiche['fiche_id']]);
        if (!$valide) {
            $messagesErreurs = explode('||', $erreurs[$fiche['fiche_id']]->get('messages_erreur'));
        }
	    ?>
        <tr id="tr_gr_<?php echo $groupe->id . "_" . $fiche['fiche_id']; ?>" class="<?= ($valide ? 'valide' : 'invalide') ?>">
            <td width="300" class="struct" fiche="<?= $fiche['fiche_id'] ?>"
                style="display: <?= ($admin->isColonneVisible('col_local') ? '' : 'none') ?>;padding: 0px 4px!important;">
			    <?
			    if (isset($fiches_infos[$fiche['fiche_id']])) {
				    $fiche_info = $fiches_infos[$fiche['fiche_id']];
				    $txt_supp = '';
				    if (isset($fiche_info->batiment_niveau_element)) {
					    $txt_supp .= '<br />';
					    $bat_niv_split = dm_explode(' - ', $fiche_info->batiment_niveau_element);
					    foreach ($bat_niv_split as $iter => $bat_niv_spl) {
						    if ($iter > 0) {
							    $txt_supp .= ($iter > 1 ? ' - ' : '') . $bat_niv_spl;
						    }
					    }
				    }
				    print $fiche['local'] . $txt_supp;
			    } else {
				    print '<span style="color: red;">' . $fiche['local'] . txt(' (Supprimé)') . '</span>';
			    }
			    ?>
            </td>
            <td style="display: <?= ($admin->isColonneVisible('col_code_alternatif_2') ? '' : 'none') ?>;text-align: center; padding: 0px 4px!important;"
                width="75">
                <?= $fiche['code_alternatif_2'] ?>
            </td>
            <?
            $class = !$valide ? 'isalertNegatif' : 'isalertPositif';
            ?>
            <td class="<?= $class ?>"
                style="display: <?= ($admin->isColonneVisible('financement_complete') ? '' : 'none') ?>;text-align: center;"
                width="75" <?= ($valide ? '' : 'title="' . implode('<br />', $messagesErreurs) . '"') ?>>
                <?= $valide ? txt('Oui') : txt('Non') ?>
            </td>
            <td style="display: <?= ($admin->isColonneVisible('col_requis_j1') ? '' : 'none') ?>;text-align: center; padding: 0px 4px!important;"
                width="75">
			    <?= $fiche['requise_init'] ?>
            </td>
            <td style="display: <?= ($admin->isColonneVisible('col_existante_j1') ? '' : 'none') ?>;text-align: center; padding: 0px 4px!important;"
                width="75">
			    <?= $fiche['existante_init'] ?>
            </td>
            <td style="display: <?= ($admin->isColonneVisible('col_conserve_j1') ? '' : 'none') ?>;text-align: center; padding: 0px 4px!important;"
                width="75">
			    <?= $fiche['demenager_init'] ?>
            </td>
            <td class="<?= ($fiche['requise_init'] <> $fiche['requise_actuelle'] ? 'isalert' : '') ?>"
                style="display: <?= ($admin->isColonneVisible('requise_actuelle') ? '' : 'none') ?>;text-align: center;"
                width="75">
			    <?= $fiche['requise_actuelle'] ?>
            </td>
            <td class=""
                style="display: <?= ($admin->isColonneVisible('existante_actuelle') ? '' : 'none') ?>;text-align: center;"
                width="75">
			    <?= $fiche['existante_actuelle'] ?>
            </td>
            <td class="<?= ($fiche['demenager_init'] <> $fiche['demenager_actuelle'] ? 'isalert' : '') ?>"
                style="display: <?= ($admin->isColonneVisible('demenager_actuelle') ? '' : 'none') ?>;text-align: center;"
                width="75">
			    <?= $fiche['demenager_actuelle'] ?>
            </td>
            <td class="<?= ($fiche['remplacement_actuelle'] < 0 ? 'isalert' : '') ?>"
                style="display: <?= ($admin->isColonneVisible('remplacement_actuelle') ? '' : 'none') ?>;text-align: center;"
                width="100">
			    <?= $fiche['remplacement_actuelle'] ?>
            </td>
            <td class="<?= ($fiche['col_nouveaux'] < 0 ? 'isalert' : '') ?>"
                style="display: <?= ($admin->isColonneVisible('col_nouveaux') ? '' : 'none') ?>;text-align: center;"
                width="100">
			    <?= $fiche['col_nouveaux'] ?>
            </td>
            <td class="<?= ($fiche['developpement_actuelle'] < 0 ? 'isalert' : '') ?>"
                style="display: <?= ($admin->isColonneVisible('developpement_actuelle') ? '' : 'none') ?>;text-align: center;"
                width="100">
			    <?= $fiche['developpement_actuelle'] ?>
            </td>
		    <?
		    if (isset($suiviBudgetaire)) {
			    foreach ($suiviBudgetaire->getPortefeuilles(true) as $portefeuille) {
				    ?>
                    <td class=""
                        style="display: <?= ($admin->isColonneVisible('qt_portefeuille_' . $portefeuille['portefeuille_numero']) ? '' : 'none') ?>;text-align: center;"
                        width="75">
					    <?= $fiche['qt_portefeuille_' . $portefeuille['portefeuille_numero']] ?>
                    </td>
				    <?
			    }
		    }
		    ?>
            <td style="display: <?= ($admin->isColonneVisible('col_qte_totale_budgetee') ? '' : 'none') ?>;text-align: center;" width="75">
                <?= $fiche['qte_totale_budgetee'] ?>
            </td>
            <td style="display: <?= ($admin->isColonneVisible('col_cout_totale_budgete') ? '' : 'none') ?>;text-align: center;" width="75">
                <?= dm_number_format($fiche['cout_totale_budgete'], 2, ',', ' ') ?>$
            </td>
            <td id="a_traiter_<?= $groupe->id . 'x' . $fiche['fiche_id'] ?>"
                style="display: <?= ($admin->isColonneVisible('col_a_traiter') ? '' : 'none') ?>;text-align: center;"
                width="75"
                class="a_traiter_<?= $projet->id ?>_<?= $groupe->equipement_bio_medicaux_type_id ?>_<?= $fiche['fiche_id'] ?>">
		        <?= $fiche['a_traiter'] ?>
            </td>
            <td id="deja_<?= $groupe->id . 'x' . $fiche['fiche_id'] ?>"
                style="display: <?= ($admin->isColonneVisible('col_deja_traite') ? '' : 'none') ?>;text-align: center;"
                width="75"
                class="deja_<?= $projet->id ?>_<?= $groupe->equipement_bio_medicaux_type_id ?>_<?= $fiche['fiche_id'] ?>">
			    <?= $fiche['deja_traite'] ?>
            </td>
		    <?
		    $class = $fiche['residuel_com'] == 0 ? '' : 'isalert';
		    $class = $fiche['residuel_com'] < 0 ? 'isalertNegatif' : $class;
		    ?>
            <td style="display: <?= ($admin->isColonneVisible('col_residuel_com') ? '' : 'none') ?>;text-align: center;"
                width="100" class="residuel_com <?= $class ?>">
			    <?= $fiche['residuel_com'] ?>
            </td>
            <td style="width: 80px!important; display: <?= ($admin->isColonneVisible('col_a_commander') ? '' : 'none') ?>;text-align: center;"
                width="80">
			    <?php if (havePermission(array('administration_quantite_commander_acquisitions')) && !$groupe->isLocked()) { ?>
                    <select id="quantite_groupe_<?= $fiche['fiche_id'] ?>_<?= $groupe->id ?>"
                            data-parent_id="groupe_<?= $groupe->id ?>"
                            data-equipement_id="<?= $groupe->equipement_bio_medicaux_type_id ?>"
                            class="quantite_groupe quantite_groupe_<?= $groupe->equipement_bio_medicaux_type_id ?>_<?= $fiche['fiche_id'] ?>"
                            style="width: 60px!important;" data-fiche_id="<?= $fiche['fiche_id'] ?>"
                            data-acquisition_projet_groupe_equipement_id="<?= $groupe->id ?>"
                            data-quantite_a_traiter="<?= $fiche['a_traiter'] ?>"
                            data-projet_numero="<?= $projet->numero ?>" data-projet_id="<?= $projet->id ?>">
					    <?php
					    $selected = false;
					    for ($quantite = 0; $quantite <= $fiche['a_traiter']; $quantite++) {
						    $sel = '';
						    if ($quantite == $fiche['commande']) {
							    $sel = ' selected="selected"';
							    $selected = true;
						    }
						    print '<option value="' . $quantite . '"' . $sel . '>' . $quantite . '</option>';
					    }
					    if (!$selected && dm_strlen($fiche['commande']) > 0) {
						    print '<option value="' . $fiche['commande'] . '" selected="selected">' . $fiche['commande'] . '</option>';
					    }
					    ?>
                    </select>
			    <?php } else {
				    echo $fiche['commande'];
			    } ?>
            </td>
            <td style="display: <?= ($admin->isColonneVisible('cout_norme') ? '' : 'none') ?>;text-align: right; white-space: nowrap;"
                width="100">
			    <?= dm_number_format(((int)$fiche['commande'] * $groupe->cout_norme), 2, ',', ' ') ?>$
            </td>
            <td style="display: <?= ($admin->isColonneVisible('cout_taxe_net') ? '' : 'none') ?>;text-align: right; white-space: nowrap;"
                width="100">
			    <?= dm_number_format($groupe->cout_taxe_net, 2, ',', ' ') ?>$
            </td>
            <td style="display: <?= ($admin->isColonneVisible('cout_projete') ? '' : 'none') ?>;text-align: right; white-space: nowrap;"
                width="100">
			    <?= dm_number_format($groupe->cout_projete, 2, ',', ' ') ?>$
            </td>
            <td style="display: <?= ($admin->isColonneVisible('quantite_autorise') ? '' : 'none') ?>;text-align: center;"
                width="100">
			    <?= $fiche['quantite_autorise'] ?>
            </td>
            <td style="display: <?= ($admin->isColonneVisible('prix_unitaire_autorise') ? '' : 'none') ?>;text-align: right; white-space: nowrap;"
                width="100">
			    <?= (dm_strlen($fiche['prix_unitaire_autorise']) > 0 ? dm_number_format($fiche['prix_unitaire_autorise'], 2, ',', ' ') . '$' : '') ?>
            </td>
            <td style="display: <?= ($admin->isColonneVisible('colonne_supplementaire_detail_1') ? '' : 'none') ?>;text-align: center;"
                width="100">
	            <?= $fiche['colonne_supplementaire_detail_1'] ?>
            </td>
            <td style="display: <?= ($admin->isColonneVisible('colonne_supplementaire_detail_2') ? '' : 'none') ?>;text-align: center;"
                width="100">
			    <?= $fiche['colonne_supplementaire_detail_2'] ?>
            </td>
            <td style="display: <?= ($admin->isColonneVisible('colonne_supplementaire_detail_3') ? '' : 'none') ?>;text-align: center;"
                width="100">
			    <?= $fiche['colonne_supplementaire_detail_3'] ?>
            </td>
            <td style="display: <?= ($admin->isColonneVisible('colonne_supplementaire_detail_4') ? '' : 'none') ?>;text-align: center;"
                width="100">
			    <?= $fiche['colonne_supplementaire_detail_4'] ?>
            </td>
            <td style="display: <?= ($admin->isColonneVisible('colonne_supplementaire_detail_5') ? '' : 'none') ?>;text-align: center;"
                width="100">
			    <?= $fiche['colonne_supplementaire_detail_5'] ?>
            </td>
            <td style="display: <?= ($admin->isColonneVisible('colonne_supplementaire_detail_6') ? '' : 'none') ?>;text-align: center;"
                width="100">
			    <?= $fiche['colonne_supplementaire_detail_6'] ?>
            </td>
            <td style="display: <?= ($admin->isColonneVisible('colonne_supplementaire_detail_7') ? '' : 'none') ?>;text-align: center;"
                width="100">
			    <?= $fiche['colonne_supplementaire_detail_7'] ?>
            </td>
            <td style="display: <?= ($admin->isColonneVisible('colonne_supplementaire_detail_8') ? '' : 'none') ?>;text-align: center;"
                width="100">
			    <?= $fiche['colonne_supplementaire_detail_8'] ?>
            </td>
            <td style="display: <?= ($admin->isColonneVisible('colonne_supplementaire_detail_9') ? '' : 'none') ?>;text-align: center;"
                width="100">
			    <?= $fiche['colonne_supplementaire_detail_9'] ?>
            </td>
            <td style="display: <?= ($admin->isColonneVisible('colonne_supplementaire_detail_10') ? '' : 'none') ?>;text-align: center;"
                width="100">
			    <?= $fiche['colonne_supplementaire_detail_10'] ?>
            </td>
	        <?
	        $residuel_dist = $fiche['commande'] - $fiche['distribue'];
	        $class = $residuel_dist == 0 ? '' : 'isalert';
	        $class = $residuel_dist < 0 ? 'isalertNegatif' : $class;
	        ?>
            <td style="display: <?= ($admin->isColonneVisible('col_residuel_dist') ? '' : 'none') ?>;text-align: center;"
                width="100" class="residuel_dist <?= $class ?>">
		        <?= $residuel_dist ?>
            </td>
            <td style="width: 80px!important; display: <?= ($admin->isColonneVisible('col_a_distribuer') ? '' : 'none') ?>;text-align: center;"
                width="80">
			    <?php if (havePermission(['administration_quantite_recus_acquisitions', 'administration_quantite_approuvees'])) { ?>
                    <select id="distribuee_groupe_<?= $fiche['fiche_id'] ?>_<?= $groupe->id ?>"
                            class="quantite_distribuee" style="width: 60px!important;"
                            data-parent_id="groupe_<?= $groupe->id ?>" data-fiche_id="<?= $fiche['fiche_id'] ?>"
                            data-acquisition_projet_groupe_equipement_id="<?= $groupe->id ?>"
                            data-quantite_a_commander="<?= $fiche['commande'] ?>"
                            data-projet_numero="<?= $projet->numero ?>" data-projet_id="<?= $projet->id ?>">
					    <?php
					    $selected = false;
					    for ($quantite = 0; $quantite <= $fiche['commande']; $quantite++) {
						    $sel = '';
						    if ($quantite == $fiche['distribue']) {
							    $sel = ' selected="selected"';
							    $selected = true;
						    }
						    print '<option value="' . $quantite . '"' . $sel . '>' . $quantite . '</option>';
					    }
					    if (!$selected && dm_strlen($fiche['distribue']) > 0) {
						    print '<option value="' . $fiche['distribue'] . '" selected="selected">' . $fiche['distribue'] . '</option>';
					    }
					    ?>
                    </select>
			    <?php } else {
				    echo $fiche['distribue'];
			    } ?>
            </td>
            <td style="width: 20px!important; text-align: center;">
                <a href="index.php?section=fiche&module=form_logs&fiche_id=<?php echo $fiche['fiche_id']; ?>&formulaire=EquipementsBioMedicaux"
                   class="fancy" style="position: relative; top: 2px;"
                   title="<?= txt('Consulter l\'historique des modifications') ?>">
                    <img src="css/images/icons/dark/clock.png"/></a>
            </td>
        </tr>
	    <?
    }
    ?>
    </tbody>
    </table>

<?php
$filtre_global_to_use = getCleFiltreGlobalToUse();
$isFiltreActifFiches = Fiches::isFiltreActif(($_SESSION['filtre_top' . $filtre_global_to_use] ?? []), ($_SESSION['filtre_avance' . $filtre_global_to_use] ?? []));
$titleFiches = txt('Aucun filtre actif');
if ($isFiltreActifFiches) {
    $titleFiches = txt('Filtre(s) actif(s)');
}
$isFiltreActifEquipement = EquipementsBioMedicauxTypes::isFiltreActif(($_SESSION['filtre_avance_equipement' . $filtre_global_to_use] ?? []));
$titleEqu = txt('Aucun filtre actif');
if ($isFiltreActifEquipement) {
    $titleEqu = txt('Filtre(s) actif(s)');
}

if (isset($_GET['desafecte_filtre_avance'])) {
    $_SESSION['filtre_avance_acquisitions_projets_2'] = array();
    header("Location: index.php?section=acquisition&module=" . $_GET['module']);
    die();
}

$filtre = &$_SESSION['filtre_acquisition_4'];
$filtre['date_debut'] = $filtre['date_debut'] ?? '';
$filtre['date_fin'] = $filtre['date_fin'] ?? '';
$filtre['projet_id'] = $filtre['projet_id'] ?? [];
$filtre['numero_groupe'] = $filtre['numero_groupe'] ?? [];
$filtre['equipement_bio_medicaux_type_id'] = $filtre['equipement_bio_medicaux_type_id'] ?? [];
$filtre['bon_commande'] = $filtre['bon_commande'] ?? [];
$filtre['statut'] = $filtre['statut'] ?? [];
$filtre['titre_jalon_en_cours'] = $filtre['titre_jalon_en_cours'] ?? [];
$filtre['order_by_col'] = $filtre['order_by_col'] ?? 'numero';
$filtre['order_by_dir'] = $filtre['order_by_dir'] ?? 'asc';
$filtre['page'] = $filtre['page'] ?? 1;
$filtre['elements_a_revise'] = $filtre['elements_a_revise'] ?? 0;
$filtre['fiche_ids'] = $filtre['fiche_ids'] ?? [];
$filtre['echeancier'] = $filtre['echeancier'] ?? [];
$filtre['mode'] = $filtre['mode'] ?? 'ajout';

if (isset($_GET['clear_filtre'])) {
    $filtre['date_debut'] = '';
    $filtre['date_fin'] = '';
    $filtre['projet_id'] = [];
    $filtre['numero_groupe'] = [];
    $filtre['equipement_bio_medicaux_type_id'] = [];
    $filtre['bon_commande'] = [];
    $filtre['statut'] = [];
    $filtre['titre_jalon_en_cours'] = [];
    $filtre['elements_a_revise'] = 0;
    $filtre['fiche_ids'] = [];
    $filtre['echeancier'] = [];
}

if (isset($_GET['page']) && intval($_GET['page']) > 0) {
    $filtre['page'] = intval($_GET['page']);
}

if (isset($_GET['elements_a_revise'])) {
    $filtre['elements_a_revise'] = (int)$_GET['elements_a_revise'];
    $filtre['page'] = 1;
}

if (isset($_POST['date_debut'])) {
    $filtre['date_debut'] = $_POST['date_debut'];
    $filtre['page'] = 1;
}

if (isset($_POST['date_fin'])) {
    $filtre['date_fin'] = $_POST['date_fin'];
    $filtre['page'] = 1;
}

if (isset($_GET['mode'])) {
    $filtre['mode'] = $_GET['mode'];
}

if (isset($_POST['projet_id'])) {
    $filtre['projet_id'] = $_POST['projet_id'];
    $filtre['page'] = 1;
} else if (isset($_POST['filtrer'])) {
    $filtre['projet_id'] = [];
}

if (isset($_GET['projet_id'])) {
    $filtre['projet_id'] = [$_GET['projet_id']];
    $filtre['page'] = 1;
}

if (isset($_POST['numero_groupe'])) {
    $filtre['numero_groupe'] = $_POST['numero_groupe'];
    $filtre['page'] = 1;
} else if (isset($_POST['filtrer'])) {
    $filtre['numero_groupe'] = [];
}

if (isset($_POST['equipement_bio_medicaux_type_id'])) {
    $filtre['equipement_bio_medicaux_type_id'] = $_POST['equipement_bio_medicaux_type_id'];
    $filtre['page'] = 1;
} else if (isset($_POST['filtrer'])) {
    $filtre['equipement_bio_medicaux_type_id'] = [];
}

if (isset($_POST['bon_commande'])) {
    $filtre['bon_commande'] = $_POST['bon_commande'];
    $filtre['page'] = 1;
} else if (isset($_POST['filtrer'])) {
    $filtre['bon_commande'] = [];
}

if (isset($_POST['statut'])) {
    $filtre['statut'] = $_POST['statut'];
    $filtre['page'] = 1;
} else if (isset($_POST['filtrer'])) {
    $filtre['statut'] = [];
}

if (isset($_POST['titre_jalon_en_cours'])) {
    $filtre['titre_jalon_en_cours'] = $_POST['titre_jalon_en_cours'];
    $filtre['page'] = 1;
} else if (isset($_POST['filtrer'])) {
    $filtre['titre_jalon_en_cours'] = [];
}

if (isset($_POST['fiche_ids'])) {
    $filtre['fiche_ids'] = $_POST['fiche_ids'];
    $filtre['page'] = 1;
} else if (isset($_POST['filtrer'])) {
    $filtre['fiche_ids'] = [];
}

if (isset($_POST['echeancier'])) {
    $filtre['echeancier'] = $_POST['echeancier'];
    $filtre['page'] = 1;
} else if (isset($_POST['filtrer'])) {
    $filtre['echeancier'] = [];
}

$filtre['a_reviser'] = (isset($_GET['a_reviser']))? $_GET['a_reviser'] : 'false';
$filtre['a_distribuer'] = (isset($_GET['a_distribuer']))? $_GET['a_distribuer'] : 'false';
$filtre['avec_fiches_plus_moins'] = (isset($_GET['avec_fiches_plus_moins']))? $_GET['avec_fiches_plus_moins'] : 'false';
$filtre['delai_critique_jalon_en_cours'] = (isset($_GET['delai_critique_jalon_en_cours']))? $_GET['delai_critique_jalon_en_cours'] : 'false';
$filtre['jalon_reel_en_retard'] = (isset($_GET['jalon_reel_en_retard']))? $_GET['jalon_reel_en_retard'] : 'false';

if(isset($_GET['order_by_col']) && isset($_GET['order_by_dir']))
{
    $filtre['order_by_col'] = $_GET['order_by_col'];
    $filtre['order_by_dir'] = $_GET['order_by_dir'];
    $filtre['page'] = 1;
}

$isFiltreActif = false;
$title = txt('Aucun filtre actif');
$filtre['filtre_avance'] = array();
if (AcquisitionsProjets::isFiltreAvanceActif()) {
    $title = txt('Filtre(s) actif(s)');
    $isFiltreActif = true;
    $filtre['filtre_avance'] = $_SESSION['filtre_avance_acquisitions_projets_2'];
}

$nb_par_page = 150;
if (isset($_SESSION['nb_total_pages']) && $filtre['page'] > 1 && !isset($_GET['clear_filtre'])) {
    $nb_total = $_SESSION['nb_total_pages'];
}
else {
    $nb_total = AcquisitionsProjets::getCount($filtre);
    $_SESSION['nb_total_pages'] = $nb_total;
}

$nb_pages = ceil($nb_total / $nb_par_page);
$limit = $filtre['page'] == 1 ? 0 : ($filtre['page']-1) * $nb_par_page;
$sql_limit = $is_print ? '' : "LIMIT $limit, $nb_par_page";

if($isFiltreActifFiches) {
    $fiches = Fiches::getListe(array('filtre_global_to_use' => $filtre_global_to_use));
    $filtre['fiche_ids'] = empty($fiches) ? [-1] : array_keys($fiches);
}

if ($isFiltreActifEquipement) {
    $equips = EquipementsBioMedicauxTypes::getListe( [ 'filtre_global_to_use' => $filtre_global_to_use ] );
    $filtre['equipement_bio_medicaux_type_id'] = empty($equips) ? [-1] : array_keys($equips);
}
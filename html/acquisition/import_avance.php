<?
require_once __DIR__ . '/../../vendor/autoload.php';

use PhpOffice\PhpSpreadsheet\Cell\Coordinate;
use PhpOffice\PhpSpreadsheet\IOFactory;

ini_set("memory_limit","10000M");
ini_set('max_execution_time', 30000);

$pivots = ['numero_projet', 'numero', 'produit_no', 'col_bon_commande', 'col_local'];
$admin = AcquisitionsProjetsAdministrations::getAdminForProjet();

$etape = 'valider';

if(isset($_POST['synchroniser'])) {
	$sql = "select numero, id from acquisitions_projets";
	$assocProjets = $db->getAssoc($sql);

	$sql = "select concat(numero, '-', acquisition_projet_id), id from acquisitions_projets_groupes_equipements";
	$assocGroupesNumero = $db->getAssoc($sql);

	$sql = "select concat(produit_no, '-', acquisition_projet_id), id from acquisitions_projets_groupes_equipements";
	$assocGroupesProduit = $db->getAssoc($sql);

	$sql = "select code, id from fiches";
	$assocFiches = $db->getAssoc($sql);

	$all_cols = $_SESSION['synchro_data']['all_cols'];
	$champs = $_SESSION['synchro_data']['champs'];
	$lignes = $_SESSION['synchro_data']['lignes_a_importer'];

	$pivotsOk = true;
	if (dm_strlen($_POST["alias_numero_projet"]) == 0 || (in_array($_POST['niveau_detail'], ['groupe', 'fiche']) && dm_strlen($_POST["alias_numero"]) == 0)) {
		$pivotsOk = false;
	}

	if (!$pivotsOk) {
		setErreur(txt('Les 2 premiers pivots sont obligatoires.'));
	}

	$projetNotExists = [];
	$projetNotRempli = [];
	$groupeNotExists = [];
	$groupeNotRempli = [];
	$ficheNotExists = [];
	$ficheNotRempli = [];
	$denormaliseesNonImportables = [];
    $importGrmSansBonCommandeOuNoProduit = [];
	foreach ($lignes as $row => $lesChamps) {

		$projet_id = 0;
		if (isset($lesChamps[$_POST['alias_numero_projet']]) && dm_strlen(dm_trim($lesChamps[$_POST['alias_numero_projet']])) == 0) {
			$projetNotRempli[] = $row;
		} else if (!isset($assocProjets[$lesChamps[$_POST['alias_numero_projet']]])) {
			$projetNotExists[] = $lesChamps[$_POST['alias_numero_projet']];
		} else {
			$projet_id = $assocProjets[$lesChamps[$_POST['alias_numero_projet']]]['id'];
		}

        if (in_array($_POST['niveau_detail'], ['groupe', 'fiche', 'grm'])) {
            if (dm_strlen(dm_trim($lesChamps[$_POST['alias_numero']])) == 0 && (!isset($lesChamps[$_POST['alias_produit_no']]) || dm_strlen(dm_trim($lesChamps[$_POST['alias_produit_no']])) == 0)) {
                $groupeNotRempli[] = $row;
            } else if (!isset($assocGroupesNumero[$lesChamps[$_POST['alias_numero']] . '-' . $projet_id])) {
                if ($_POST['niveau_detail'] == 'grm') {
                    if (!isset($lesChamps[$_POST['alias_produit_no']]) || dm_strlen(dm_trim($lesChamps[$_POST['alias_produit_no']])) == 0 || !isset($lesChamps[$_POST['alias_bon_commande']]) || dm_strlen(dm_trim($lesChamps[$_POST['alias_bon_commande']])) == 0) {
                        $importGrmSansBonCommandeOuNoProduit[] = $row;
                    }
                } else {
                    if (!isset($lesChamps[$_POST['alias_produit_no']]) || !isset($assocGroupesProduit[$lesChamps[$_POST['alias_produit_no']] . '-' . $projet_id])) {
                        $groupeNotExists[] = strlen(trim($lesChamps[$_POST['alias_numero']])) == 0 ? '[VIDE]' : $lesChamps[$_POST['alias_numero']];
                    }
                }
            }

            if (dm_strlen($_POST["alias_col_local"]) > 0) {
                if (dm_strlen(dm_trim($lesChamps[$_POST['alias_col_local']])) == 0) {
                    $ficheNotRempli[] = $row;
                } else if (!isset($assocFiches[$lesChamps[$_POST['alias_col_local']]])) {
                    $ficheNotExists[] = $lesChamps[$_POST['alias_col_local']];
                }
            }

//            if (isset($_POST['donnees_denormalisees']) && count($_POST['donnees_denormalisees']) > 0) {
//                if (!isset($lesChamps[$_POST['alias_produit_no']]) || dm_strlen(dm_trim($lesChamps[$_POST['alias_produit_no']])) == 0 || !isset($lesChamps[$_POST['alias_bon_commande']]) || dm_strlen(dm_trim($lesChamps[$_POST['alias_bon_commande']])) == 0) {
//                    $denormaliseesNonImportables[] = $row;
//                }
//            }
        }
	}

	if (count($projetNotExists) > 0) {
		setErreur(txt('Les numéros de projet suivants n\'existent pas dans la base de données de DocMatic: ') . dm_implode(', ', $projetNotExists));
	}

	if (count($groupeNotExists) > 0) {
		setErreur(txt('Les numéros de groupe suivants n\'existent pas dans la base de données de DocMatic: ') . dm_implode(', ', $groupeNotExists));
	}

	if (count($ficheNotExists) > 0) {
		setErreur(txt('Les locaux suivants n\'existent pas dans la base de données de DocMatic: ') . dm_implode(', ', $ficheNotExists));
	}

	if (count($projetNotRempli) > 0) {
		setErreur(txt('Le numéro du projet est obligatoire. Voici les lignes pour lesquelles nous n\'avons pas l\'information: ') . dm_implode(', ', $projetNotRempli));
	}

	if (count($groupeNotRempli) > 0) {
		setErreur(txt('Le numéro du groupe ou le numéro du produit (GRM) est obligatoire. Voici les lignes pour lesquelles nous n\'avons pas l\'information: ') . dm_implode(', ', $groupeNotRempli));
	}

	if (count($ficheNotRempli) > 0) {
		setErreur(txt('Le local est obligatoire. Voici les lignes pour lesquelles nous n\'avons pas l\'information: ') . dm_implode(', ', $ficheNotRempli));
	}

	if (count($denormaliseesNonImportables) > 0) {
		setErreur(txt('Le numéro du produit (GRM) et le numéro de bon de commande sont obligatoires si on veut importer des données complémentaires du bon de commande. Voici les lignes pour lesquelles nous n\'avons pas l\'information: ') . dm_implode(', ', $denormaliseesNonImportables));
	}

	if (count($importGrmSansBonCommandeOuNoProduit) > 0) {
        setErreur(txt('Le numéro du produit (GRM) et le numéro de bon de commande sont obligatoires lors d\'import GRM lorsqu\'il n\'y a pas de numéro de groupe. Voici les lignes pour lesquelles nous n\'avons pas l\'information: ') . dm_implode(', ', $importGrmSansBonCommandeOuNoProduit));
    }

	if (nbErreurs() > 0) {
		$etape = 'synchroniser';
	} else {
		$champs_to_import = array();
		foreach ($all_cols as $key => $champ) {
			if (isset($_POST['alias_' . $key]) && dm_strlen($_POST['alias_' . $key]) > 0) {
				$champs_to_import[$_POST['alias_' . $key]] = $champ;
			}
		}
        if (isset($_POST['donnees_denormalisees'])) {
	        foreach ($_POST['donnees_denormalisees'] as $champDenorm) {
		        $champs_to_import[$champDenorm] = ['format' => 'string', 'table' => 'donnees_denormalisees'];
	        }
        }

		foreach ($lignes as $row => $lesChamps) {
			$projet_id = $assocProjets[$lesChamps[$_POST['alias_numero_projet']]]['id'];
			$bon_commande = '';
			$produit_no = '';
			$groupe_id = 0;
			$fiche_id = 0;
			if (in_array($_POST['niveau_detail'], ['groupe', 'grm', 'fiche'])) {
                $bon_commande = isset($lesChamps[$_POST["alias_bon_commande"]]) && dm_strlen($lesChamps[$_POST["alias_bon_commande"]]) > 0 ? $lesChamps[$_POST["alias_bon_commande"]] : '';
                $produit_no = isset($lesChamps[$_POST["alias_produit_no"]]) && dm_strlen($lesChamps[$_POST["alias_produit_no"]]) > 0 ? $lesChamps[$_POST["alias_produit_no"]] : '';
                $groupe_id_by_noProduit = isset($assocGroupesProduit[$produit_no . '-' . $projet_id]) ? $assocGroupesProduit[$produit_no . '-' . $projet_id]['id'] : -1;
                $groupe_id = isset($assocGroupesNumero[$lesChamps[$_POST['alias_numero']] . '-' . $projet_id]) ? $assocGroupesNumero[$lesChamps[$_POST['alias_numero']] . '-' . $projet_id]['id'] : $groupe_id_by_noProduit;
                $fiche_id = dm_strlen($_POST["alias_col_local"]) > 0 ? $assocFiches[$lesChamps[$_POST['alias_col_local']]]['id'] : 0;
            }
            $updating_groupe = [];
            $updating_groupe_data = [];
			$denorm_data = array();
			foreach ($champs_to_import as $alias => $champ) {
				$new_valeur = $lesChamps[$alias] ?? '';
				if ($champ['format'] == 'int') {
					$new_valeur = (int)$new_valeur;
				} else if ($champ['format'] == 'float') {
					$new_valeur = (float)$new_valeur;
				}
				if (isset($champ['col_bd']) && in_array($champ['col_bd'], ['bloquant_type_id', 'site_livraison_id', 'priorite_acquisition_id', 'local_transitoire_id', 'mode_adjudication_id', 'sous_volet_responsable_id', 'phase_incidence_contraignante_id', 'priorisation_id', 'mode_sollicitation_id', 'menu_supplementaire_a_id','responsable_dossier_a_id','responsable_dossier_b_id','responsable_dossier_c_id','responsable_dossier_d_id','responsable_dossier_e_id','responsable_dossier_f_id','responsable_dossier_g_id','responsable_dossier_h_id','responsable_dossier_i_id','responsable_dossier_j_id','responsable_dossier_1_id','responsable_dossier_2_id','responsable_dossier_3_id','responsable_dossier_4_id','responsable_dossier_5_id','responsable_dossier_6_id','responsable_dossier_7_id','responsable_dossier_8_id','responsable_dossier_9_id','responsable_dossier_10_id', 'responsable_dossier_contractuel_id' ])) {
					$tableAssoc = [
						'site_livraison_id' => 'sites_livraisons_types',
						'bloquant_type_id' => 'bloquants_types',
						'local_transitoire_id' => 'locals_transitoires_types',
						'priorite_acquisition_id' => 'priorites_acquisitions_types',
						'sous_volet_responsable_id' => 'acquisitions_sous_volets_responsables_types',
						'phase_incidence_contraignante_id' => 'acquisitions_phases_incidences_contraignantes_types',
						'priorisation_id' => 'acquisitions_projets_priorisations_types',
						'mode_sollicitation_id' => 'acquisitions_projets_modes_sollicitations_types',
                        'mode_adjudication_id' => 'acquisitions_projets_modes_adjudications_types',
                        'responsable_dossier_contractuel_id' => 'contrats_responsables_dossiers_types',
                        'menu_supplementaire_a_id' => 'acquisitions_menus_supplementaires_a_types',
                        'responsable_dossier_a_id' => 'acquisitions_responsables_dossiers_a_types',
                        'responsable_dossier_b_id' => 'acquisitions_responsables_dossiers_b_types',
                        'responsable_dossier_c_id' => 'acquisitions_responsables_dossiers_c_types',
                        'responsable_dossier_d_id' => 'acquisitions_responsables_dossiers_d_types',
                        'responsable_dossier_e_id' => 'acquisitions_responsables_dossiers_e_types',
                        'responsable_dossier_f_id' => 'acquisitions_responsables_dossiers_f_types',
                        'responsable_dossier_g_id' => 'acquisitions_responsables_dossiers_g_types',
                        'responsable_dossier_h_id' => 'acquisitions_responsables_dossiers_h_types',
                        'responsable_dossier_i_id' => 'acquisitions_responsables_dossiers_i_types',
                        'responsable_dossier_j_id' => 'acquisitions_responsables_dossiers_j_types',
                        'responsable_dossier_1_id' => 'acquisitions_responsables_dossiers_types',
                        'responsable_dossier_2_id' => 'acquisitions_responsables_dossiers_types',
                        'responsable_dossier_3_id' => 'acquisitions_responsables_dossiers_types',
                        'responsable_dossier_4_id' => 'acquisitions_responsables_dossiers_types',
                        'responsable_dossier_5_id' => 'acquisitions_responsables_dossiers_types',
                        'responsable_dossier_6_id' => 'acquisitions_responsables_dossiers_types',
                        'responsable_dossier_7_id' => 'acquisitions_responsables_dossiers_types',
                        'responsable_dossier_8_id' => 'acquisitions_responsables_dossiers_types',
                        'responsable_dossier_9_id' => 'acquisitions_responsables_dossiers_types',
                        'responsable_dossier_10_id' => 'acquisitions_responsables_dossiers_types'
					];
					$sql = "select id from `" . $tableAssoc[$champ['col_bd']] . "` where nom = ?";
					$idTmp = $db->getOne($sql, [$new_valeur]);
					if ((int)$idTmp > 0) {
						$new_valeur = (int)$idTmp;
					} else if (dm_strlen(dm_trim($new_valeur)) > 0) {
						$sql = "insert into `" . $tableAssoc[$champ['col_bd']] . "` (nom) values (?)";
						$db->query($sql, [$new_valeur]);
						$new_valeur = $db->lastInsertId();

						$sql = "insert into `" . $tableAssoc[$champ['col_bd']] . "_projets` (liste_item_id, projet_id) values (?, ?)";
						$db->query($sql, [$new_valeur, getProjetId()]);
					}
				}

                if (dm_strlen($new_valeur) > 0 || ($champ['format'] == 'int' && (int)$new_valeur > 0)) {
	                if ($champ['table'] == 'acquisitions_projets') {
		                $sql = "update acquisitions_projets set `" . $champ['col_bd'] . "` = ? where id = ?";
		                $db->query($sql, [$new_valeur, $projet_id]);
	                } else if ($champ['table'] == 'acquisitions_projets_groupes_equipements') {
                        if ($groupe_id > 0) {
                            $sql = "update acquisitions_projets_groupes_equipements set `" . $champ['col_bd'] . "` = ? where id = ?";
                            $db->query($sql, [$new_valeur, $groupe_id]);
                        } else {
                            $updating_groupe[] = "`" . $champ['col_bd'] . "` = ?";
                            $updating_groupe_data[] = $new_valeur;
                        }
	                } else if ($champ['table'] == 'acquisitions_projets_groupes_equipements_fiches') {
		                $sql = "update acquisitions_projets_groupes_equipements_fiches set `" . $champ['col_bd'] . "` = ? where fiche_id = ? and acquisition_projet_groupe_equipement_id = ?";
		                $db->query($sql, [$new_valeur, $fiche_id, $groupe_id]);
	                } else if (in_array($alias, $_POST['donnees_denormalisees'])) {
		                $denorm_data[] = [$alias => $new_valeur];
	                }
                }
			}

            if ($_POST['niveau_detail'] == 'grm' && $groupe_id == -1 && count($updating_groupe) > 0) {
                $sql_update = "INSERT INTO acquisitions_projets_groupes_equipements SET ".dm_implode(',',$updating_groupe).", numero = '',equipement_bio_medicaux_type_id = null, acquisition_projet_id = (SELECT id FROM acquisitions_projets WHERE numero = ?)";
                $data = array_merge($updating_groupe_data, [$lesChamps[$_POST['alias_numero_projet']]]);
                $db->query($sql_update, $data);
            }

			if (count($denorm_data) > 0) {
				$rows = $db->getAll("SELECT id FROM acquisitions_projets_denormalisees WHERE projet_numero = ? AND bon_commande = ? AND produit_no = ?", array($lesChamps[$_POST['alias_numero_projet']], $bon_commande, $produit_no));
				if (count($rows) > 0) {
                    $query = "UPDATE acquisitions_projets_denormalisees SET data = ? WHERE projet_numero = ? AND bon_commande = ? AND produit_no = ?";
                    $data = [serialize($denorm_data), $lesChamps[$_POST['alias_numero_projet']], $bon_commande, $produit_no];
                } else {
                    $query = "INSERT INTO acquisitions_projets_denormalisees SET data = ?, projet_numero = ?, bon_commande = ?, produit_no = ?";
                    $data = [serialize($denorm_data), $lesChamps[$_POST['alias_numero_projet']], $bon_commande, $produit_no];
                }
				$db->query($query, $data);
			}

			unset($lignes[$row]);
		}
		unset($_SESSION['synchro_data']);
		$db->commit();
		?>
		<script>
			parent.window.location.reload();
			parent.$.fancybox.close();
		</script>
		<?
		die();
	}
} else if(isset($_POST['valider'])) {
	$_SESSION['synchro_data']['lignes_a_importer'] = array();
	$alias = array();
	$champs = array();

	if (!isset($_FILES['fichier']) || !is_uploaded_file($_FILES['fichier']['tmp_name'])) {
		setErreur(txt('Vous devez sélectionner le fichier à importer.'));
	} else {
		$all_cols = AcquisitionsProjetsAdministrations::getChampsColonnesImportables();

		$all_alias = array();
		foreach ($all_cols as $key => $champ) {
			$all_alias[$champ['label']] = $champ['label'];
		}

		$spreadsheet = IOFactory::load($_FILES['fichier']['tmp_name']);

		foreach ($spreadsheet->getWorksheetIterator() as $iter_sheet => $worksheet) {
			if ($iter_sheet > 0) {
				break;
			}
			$highestRow = $worksheet->getHighestDataRow(); // e.g. 10
			$highestColumn = $worksheet->getHighestDataColumn(); // e.g 'F'
			$highestColumnIndex = Coordinate::columnIndexFromString($highestColumn);
			$nrColumns = ord($highestColumn) - 64;

			$row_titre = 1;
			for ($row = 1; $row <= $highestRow; ++$row) {
				for ($col = 1; $col <= $highestColumnIndex; ++$col) {
					$cell = $worksheet->getCellByColumnAndRow($col, $row);
					$val = sanitizeString((string)$cell->getCalculatedValue());

					$val = dm_trim($val) == '.' ? '' : $val;
					if ($row == $row_titre && dm_strlen($val) > 0) {
						$val = dm_trim(dm_preg_replace("/\r|\n/", "", $val));
						$alias[$col] = $val;
						$champs[$val] = $val;
					} else if ($row > $row_titre && isset($alias[$col])) {
						$_SESSION['synchro_data']['lignes_a_importer'][$row][$alias[$col]] = $val;
					}
				}
			}
		}

		$spreadsheet->disconnectWorksheets();
		unset($spreadsheet);

		$_SESSION['synchro_data']['champs'] = $champs;

		$_SESSION['synchro_data']['all_cols'] = $all_cols;

		$_SESSION['synchro_data']['type_import'] = 'excel';
	}

	$etape = 'synchroniser';
}

$champsNonSelectionnes = [];
$all_cols_all = AcquisitionsProjetsAdministrations::getChampsColonnesByLabel();
$noHeader = true;
include('config/header.inc.php');
?>
	<script>
		var $document = $(document);
		$document.ready(function(){
			$document.on('change', 'select.uniques', function(){

				var valeur = this.value;
				var id_select = this.id;

				$document.find('select.uniques').each(function(){

					if(this.value == valeur && this.id != id_select)
					{
						this.value = '';
					}

				});

				$('#donnees_denormalisees').find(`input[value="${valeur}"]`).prop('checked', false);

			});

			$document.on('change', '#niveau_detail', function() {
                if ($(this).val() === 'projet') {
                    $('tr.groupe').hide();
					$('tr.groupe').find('select').val('');
					$('tr.groupe').find('input').prop('checked', false);
					$('tr.fiche').hide();
					$('tr.fiche').find('select').val('');
					$('tr.non_grm').show();
                } else if ($(this).val() === 'groupe') {
					$('tr.groupe').show();
					$('tr.fiche').hide();
					$('tr.fiche').find('select').val('');
					$('tr.non_grm').show();
				} else if ($(this).val() === 'fiche') {
					$('tr.groupe').show();
					$('tr.fiche').show();
					$('tr.non_grm').show();
				} else if ($(this).val() === 'grm') {
					$('tr.groupe').show();
					$('tr.fiche').hide();
					$('tr.fiche').find('select').val('');
					$('tr.non_grm').hide();
					$('tr.non_grm').find('select').val('');
				}
            });
            <?
            if (isset($_POST['niveau_detail'])) {
            ?>
			    $document.find('#niveau_detail').val('<?= $_POST['niveau_detail'] ?>').trigger('change');

            <?
            }
            ?>
		});
	</script>
	<style>
		.blocsFiche, #fiche table, #fiche tr, #fiche td, #fiche th, .ui-dialog table, .ui-dialog tr, .ui-dialog td, .ui-dialog th {
			background: none!important;
		}
		.blocsFicheTitre {
			margin-top: 0px!important;
		}
		.readonly {
			text-align: center;
		}
        label.chk {
            font-size: inherit!important;
            background: none!important;
            font-weight: inherit!important;
            text-align: left;
            margin-left: 145px;
            display: block;
        }
	</style>

	<div id="popupContent">
		<form id="form" autocomplete="off" method="post" action="index.php?section=acquisition&module=import_avance" enctype="multipart/form-data">

			<label><?=txt('Mise-à-jour des données')?></label>
			<fieldset style="min-height: 175px; padding: 10px 0 0 50px;">
				<?
				if (!isset($all_cols)) {
					?>
					<div id="editables">
						<?
						erreurs();
						?>
						<section style="margin: 10px 0 10px 0;">
							<span for="fichier"><?php print txt('Fichier MS Excel à importer'); ?></span>
							<span>
                            <input type="file" id="fichier" name="fichier" style="border: solid 0px!important;"/>
                        </span>
						</section>

					</div>
					<?
				} else {
					?>
					<div id="editables">
						<?
						messages();
						erreurs();
						?>
                        <h4 style="margin-bottom: 10px;">
							<?= txt('Niveau de détail') ?>:<br/>
                            <select id="niveau_detail" name="niveau_detail">
                                <option value="projet"><?= txt('1 - Projets') ?></option>
                                <option value="groupe"><?= txt('2 - Projets et groupes') ?></option>
                                <option value="fiche" selected="selected"><?= txt('3 - Projets, groupes et locaux ') ?></option>
                                <option value="grm"><?= txt('4 - Import GRM ') ?></option>
                            </select>
                        </h4>
						<section style="margin: 10px 0 50px 0;">
							<table style="width: 95%!important; margin: 10px 0 0 0;">
								<tr>
									<th>
										<?= txt('Champs') ?>
									</th>
									<th>
										<?= txt('Colonnes détectées dans le fichier importé') ?>
									</th>
								</tr>
								<?
								foreach ($champs as $i => $alias) {
									$champsNonSelectionnes[$alias] = $alias;
								}
								foreach ($pivots as $pivot) {
                                    $ensemble = 'projet';
                                    if (in_array($pivot, ['numero', 'produit_no', 'col_bon_commande'])) {
	                                    $ensemble = 'groupe';
                                    } else if (in_array($pivot, ['col_local'])) {
	                                    $ensemble = 'fiche';
                                    }
									?>
									<tr class="<?= $ensemble ?> <?= (in_array($pivot, ['produit_no', 'col_bon_commande']) ? 'non_grm' : '') ?>" style="background-color: #e2ffe2">
										<td style="border-right: solid 1px transparent!important;">
											<?= (in_array($pivot, ['produit_no', 'col_bon_commande', 'col_local']) ? $admin->getLabelForColonne($pivot) : $admin->getLabelForChamp($pivot)) ?>
										</td>
										<td style="border-left: solid 1px transparent!important; position: relative;">
											<div style="position: absolute; left: -29px; top: 10px; color: red; font-weight: bold;">
												<-- <?= txt('Pivot') ?> -->
											</div>
											<? affichageSelect($pivot, (in_array($pivot, ['produit_no', 'col_bon_commande', 'col_local']) ? $admin->getLabelForColonne($pivot) : $admin->getLabelForChamp($pivot)), true); ?>
                                            <span style="width: 24px; height: 24px; display: inline-block;">&nbsp;</span>
										</td>
									</tr>
									<?
								}
								foreach ($all_cols as $key => $champ) {

									?>
									<tr class="<?= $champ['ensemble'] ?> champs"><td>
											<?= ucfirst($champ['ensemble'] == 'fiche' ? txt('Local') : $champ['ensemble']) . ' - <span>' . $champ['label'] . '</span>' ?>
										</td><td>
											<? affichageSelect($key, $champ['label']) ?>
                                            <img src="css/images/icons/dark/cross_3.png" class="resetAssoc" />
										</td></tr>
									<?
								}
								?>
                                <tr class="groupe"><td style="vertical-align: top">
										<?= txt('Données complémentaires du bon de commande') ?>
                                    </td><td>
			                                <?
                                            if (isset($champsNonSelectionnes)) {
                                                foreach($champsNonSelectionnes as $i => $alias)
                                                {
                                                    if (!isset($all_cols_all[trim($alias)])) {
                                                        $chk = ' checked="checked"';
                                                        ?>
                                                        <label class="chk"><input type="checkbox" id="donnees_denormalisees" name="donnees_denormalisees[]" value="<?=$alias?>"<?=$chk?> />&nbsp;<?=$alias?></label>
                                                        <?
                                                    }
                                                }
                                            }
			                                ?>
                                    </td></tr>
							</table>
						</section>
					</div>
					<?
				}
				?>
			</fieldset>

			<div style="text-align: right; width: 98%!important;">
                <?
                if ($etape == 'synchroniser') {
                    ?>
                    <button class="assocAll all" style="color: green;"><?= txt('Tout associer') ?></button>
                    <button class="resetAll all" style="color: red;"><?= txt('Tout dissocier') ?></button>
                    <?
                }
                ?>
				<button class="submit" value="submitbuttonvalue" name="<?=$etape?>"><?=txt($etape)?></button>
				<button onclick="parent.$.fancybox.close();return false;" name="close"><?=txt('Annuler')?></button>

			</div>
		</form>
	</div>
<?
include('config/footer.inc.php');

function affichageSelect($key, $selected, $isPivot = false)
{
	global $champs, $champsNonSelectionnes;
	?>
	<select id="alias_<?=$key?>" name="alias_<?=$key?>" class="uniques <?= ($isPivot ? '' : 'colonnes') ?>" style="max-width: 250px!important;">
		<option value=""> </option>
		<?
		foreach($champs as $i => $alias)
		{
		    $leMeme = trim(sansaccent($alias)) == trim(sansaccent($selected));
			$sel = $leMeme  ? ' selected="selected"' : '';
			if ($leMeme) {
				unset($champsNonSelectionnes[$alias]);
            }
			?>
			<option value="<?=$alias?>"<?=$sel?>><?=$alias?></option>
			<?
		}
		?>
	</select>
	<?
}
function clean($v) {
	return dm_str_replace("'","\\'",$v);
}
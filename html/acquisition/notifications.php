<?
include('config/header.inc.php');

if (isset($_GET['lu'])) {
    $id = (int)$_GET['lu'];
    $sql = "update notifications_acquisitions_projets_jalons_usagers set ts_lu = current_timestamp where id = ?";
    $db->query($sql, [$id]);

    header('Location: index.php?section=acquisition&module=notifications');
    exit;
}
if (isset($_GET['nonlu'])) {
    $id = (int)$_GET['nonlu'];
    $sql = "update notifications_acquisitions_projets_jalons_usagers set ts_lu = null where id = ?";
    $db->query($sql, [$id]);

    header('Location: index.php?section=acquisition&module=notifications');
    exit;
}

$titre = txt('Notifications');
$table = 'notifications_acquisitions_projets_jalons';
$type = 'client';

include('acquisition/titre.inc.php');
?>
    <style>
        #content {
            background-color: #F1F1F1!important;
            background-image: none!important;
            border-color: #ededed;
        }
    </style>
    <br style="clear: both" />
    <div id="ongletContent" style="position: relative; top: 5px;">
        <?
        getListePageStandard($table, $type);
        ?>
    </div>
<?

$includeAdminIndex=1;
include('config/footer.inc.php');
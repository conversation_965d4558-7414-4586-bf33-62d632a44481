function updateProjetsAlert() {
	checkResiduelGroupes();
    $('.acq').each(function(){
        let projet_id = $(this).data('projet_id');
        if($(this).find('.isalert').length > 0)
        {
            $('#projet_alert_'+projet_id).show();
        }
        else
        {
            $('#projet_alert_'+projet_id).hide();
        }
    });
}

$(document).ready(function() {
    <?php if (isset($_GET['project_id']) || isset($_GET['projet_id']) || (isset($_GET['numero_groupe']) && $_GET['numero_groupe'] != '')) {
    if (isset($_GET['projet_id']) && $_GET['projet_id'] != 0) {
        $projet_id_open = $_GET['projet_id'];
    } elseif (isset($_GET['project_id'])) {
        $projet_id_open = $_GET['project_id'];
    } else {
        $sql_numero_groupe = "SELECT acquisition_projet_id FROM acquisitions_projets_groupes_equipements WHERE id = ?";

        $projet_id_open = $db->getOne($sql_numero_groupe, array($_GET['numero_groupe']));
    }
    ?>
		toggleAcq('<?php echo $projet_id_open; ?>');
    <?php } ?>

    updateProjetsAlert();

    $(document).on('click', 'A.duplicate', function(){
        var obj = $(this);
        $.confirm('<?=dm_addslashes(txt('Êtes-vous certain de vouloir dupliquer ce projet d\'approvisionnement?', false))?>', function(){
            var table = obj.attr('tableName');
            var id = obj.attr('idElement');
            var id_project = obj.attr('idProject');

            var url = "index.php";
            $.get(url, { action: 'duplicate_groupe_equipement', id: id, table: table }, function(data){

                <?php
                if (isset($_GET['page'])) {
                    $page = $_GET['page'];
                } else {
                    $page = '1';
                }
                ?>

                window.location.href ='index.php?section=acquisition&module=projets&project_id=' + id_project + '&page=' + '<?php echo $page; ?>';

            });
        });
    });

	$(document).on('change', 'select.quantite_groupe', function(){
		cache_qte_deja_commander = [];
		updateQuantitesTotales($(this).data('parent_id'), $(this).data('acquisition_projet_groupe_equipement_id'));
		$.get("index.php", { action: 'save_quantite_groupe', quantite: $(this).val(), fiche_id: $(this).data('fiche_id'), acquisition_projet_groupe_equipement_id: $(this).data('acquisition_projet_groupe_equipement_id') });
	});

	$(document).on('click', 'input.tout_associer', function(){
		let quantites = {};
		let fiche_ids = {};
		let acquisition_projet_groupe_equipement_ids = {};
		let iter = 0;

		cache_qte_deja_commander = [];
		let parent_id = $(this).data('parent_id');
		$('#' + parent_id).find('select.quantite_groupe').each(function(){
			let quantite_a_traiter = $(this).data('quantite_a_traiter');
			$(this).val(quantite_a_traiter);
			quantites[iter] = quantite_a_traiter;
			fiche_ids[iter] = $(this).data('fiche_id');
			acquisition_projet_groupe_equipement_ids[iter] = $(this).data('acquisition_projet_groupe_equipement_id');
			iter++;

			updateQuantitesTotales(parent_id, $(this).data('acquisition_projet_groupe_equipement_id'));
		});

		$.post("index.php?action=save_quantite_groupe_all", { quantites: quantites, fiche_ids: fiche_ids, acquisition_projet_groupe_equipement_ids: acquisition_projet_groupe_equipement_ids });
	});

	$(document).on('change', 'select.quantite_distribuee', function(){
		cache_qte_deja_commander = [];
		updateQuantitesTotales($(this).data('parent_id'), $(this).data('acquisition_projet_groupe_equipement_id'));
		$.get("index.php", { action: 'save_quantite_distribuee', quantite: $(this).val(), fiche_id: $(this).data('fiche_id'), acquisition_projet_groupe_equipement_id: $(this).data('acquisition_projet_groupe_equipement_id') });
	});

	$(document).on('click', 'input.tout_distribuer', function(){
		let quantites = {};
		let fiche_ids = {};
		let acquisition_projet_groupe_equipement_ids = {};
		let iter = 0;

		cache_qte_deja_commander = [];
		let parent_id = $(this).data('parent_id');
		$('#' + parent_id).find('select.quantite_distribuee').each(function(){

			let qte_residuel = parseInt($('#qte_residuel_' + $(this).data('acquisition_projet_groupe_equipement_id')).html());
			let quantite_a_commander = $('#quantite_groupe_' + $(this).data('fiche_id') + '_' + $(this).data('acquisition_projet_groupe_equipement_id')).val();

			let quantite_distribue = $(this).val();
			if(quantite_distribue != quantite_a_commander)
			{
				if(qte_residuel >= (quantite_a_commander - quantite_distribue))
				{
					quantite_distribue = quantite_a_commander;
				}
				else if(qte_residuel > 0)
				{
					quantite_distribue = qte_residuel;
				}
				$(this).val(quantite_distribue);
				quantites[iter] = quantite_distribue;
				fiche_ids[iter] = $(this).data('fiche_id');
				acquisition_projet_groupe_equipement_ids[iter] = $(this).data('acquisition_projet_groupe_equipement_id');
				iter++;

				updateQuantitesTotales(parent_id, $(this).data('acquisition_projet_groupe_equipement_id'));
			}
		});

		$.post("index.php?action=save_quantite_distribuee_all", { quantites: quantites, fiche_ids: fiche_ids, acquisition_projet_groupe_equipement_ids: acquisition_projet_groupe_equipement_ids });
	});

	$(document).on('click', '#filter_btn', function(){
		$('#acquisitions_projets_form').toggle(400);
	});

	//console.time('1');
	cache_qte_deja_commander = [];
	$(document).find('tr.groupes').each(function() {
		let projet_id = $(this).data('projet_id');
		let groupe_id = $(this).data('groupe_id');
		if($('#groupe_' + groupe_id).find('tr').length > 1)
		{
			updateQuantitesTotales('acquisition_' + projet_id, groupe_id);
		}
	});
	//console.timeEnd('1');
});

cache_qte_deja_commander = [];
function updateQuantitesTotales(parent_id, groupe_id)
{
	let qte_a_commander_totale = 0;
	let qte_a_distribuer_totale = 0;
	let qte_a_traiters = [];
	let qte_a_commanders = [];

	$(document).find('select.quantite_groupe').each(function(){

		//console.time('1');
		let fiche_id = $(this).data('fiche_id');
		let groupe_id_courant = $(this).data('acquisition_projet_groupe_equipement_id');
		let $tr = $('#tr_gr_' + groupe_id_courant + '_' + fiche_id);
		let qte_deja_commander = 0;

		//console.timeEnd('1');
		//console.time('2');
		if(cache_qte_deja_commander[$(this).data('equipement_id') + '_' + $(this).data('fiche_id')] != null)
		{
			qte_deja_commander = cache_qte_deja_commander[$(this).data('equipement_id') + '_' + $(this).data('fiche_id')];
		}
		else
		{
			let listObj = $('#acquisitions_projets')[0].querySelectorAll('select.quantite_groupe_' + $(this).data('equipement_id') + '_' + $(this).data('fiche_id'));
			for(let i in listObj)
			{
				if(listObj[i].value !== undefined) {
					qte_deja_commander += parseInt(listObj[i].value);
				}
			}
			cache_qte_deja_commander[$(this).data('equipement_id') + '_' + $(this).data('fiche_id')] = qte_deja_commander;
		}
		//console.timeEnd('2');
		//console.time('3');

		let old_qte_deja_commander = $tr.find('span.deja_commander').html();
		if (old_qte_deja_commander != qte_deja_commander) {
			$tr.find('span.deja_commander').html(qte_deja_commander);
		}

		let qte_a_traiter = parseInt($tr.find('.a_traiter').html());
		if(qte_a_traiters[groupe_id_courant] === undefined)
		{
			qte_a_traiters[groupe_id_courant] = 0;
		}
		qte_a_traiters[groupe_id_courant] += qte_a_traiter;
		//console.timeEnd('3');

		//console.time('4');
		let qte_residuel_ligne = qte_a_traiter - qte_deja_commander;
		if(qte_residuel_ligne != 0)
		{
			if(!$tr.find('td.residuel_com').hasClass('isalert')) {
				$tr.find('td.residuel_com').addClass('isalert');
			}
		}
		else
		{
			if($tr.find('td.residuel_com').hasClass('isalert')) {
				$tr.find('td.residuel_com').removeClass('isalert');
			}
		}

		$tr.find('td.residuel_com').html(qte_residuel_ligne);
		//console.timeEnd('4');

	});

	let iter = 0;
	$('#' + parent_id).find('select.quantite_groupe').each(function(){
		let fiche_id = $(this).data('fiche_id');
		let groupe_id_courant = $(this).data('acquisition_projet_groupe_equipement_id');
		let $tr = $('#tr_gr_' + groupe_id_courant + '_' + fiche_id);

		let qte_a_comm = parseInt($(this).val());
		qte_a_commander_totale += qte_a_comm;
		if(qte_a_commanders[groupe_id_courant] === undefined)
		{
			qte_a_commanders[groupe_id_courant] = 0;
		}
		qte_a_commanders[groupe_id_courant] += qte_a_comm;

		let qte_residuel_dist_ligne = qte_a_comm - parseInt($tr.find('.quantite_distribuee').val());
		if(qte_residuel_dist_ligne != 0)
		{
			$tr.find('td.residuel_dist').addClass('isalert');
		}
		else
		{
			$tr.find('td.residuel_dist').removeClass('isalert');
		}
		$tr.find('td.residuel_dist').html(qte_residuel_dist_ligne);

		iter++;
	});

	iter = 0;
	$('#' + parent_id).find('select.quantite_distribuee').each(function(){
		qte_a_distribuer_totale += parseInt($(this).val());
		iter++;
	});

	let quantite_disponible_totale = parseInt($('#quantite_disponible_totale_' + groupe_id).html()|0);
	$('#qte_distribuee_totale_' + groupe_id).html(qte_a_distribuer_totale);
	for(let groupe_id_tmp in qte_a_commanders) {
		$('#qte_a_commander_totale_' + groupe_id_tmp).html(qte_a_commanders[groupe_id_tmp]);
	}

	//if(quantite_disponible_totale > 0)
	{
		let qte_residuel = quantite_disponible_totale - qte_a_distribuer_totale;
		$('#qte_residuel_' + groupe_id).html(qte_residuel);
		if(qte_residuel != 0)
		{
			$('#qte_residuel_' + groupe_id).addClass('isalert');
		}
		else
		{
			$('#qte_residuel_' + groupe_id).removeClass('isalert');
		}
	}

	$(document).find('tr.groupes').each(function() {
		let groupe_id = $(this).data('groupe_id');
		if($('#groupe_' + groupe_id).find('tr').length > 1) {
			let qte_res_com_total = 0;
			$('#groupe_' + groupe_id).find('.residuel_com').each(function () {
				qte_res_com_total += parseInt($(this).html());
			});
			$('#qte_residuel_com_' + groupe_id).html(qte_res_com_total);
			if (qte_res_com_total != 0) {
				$('#qte_residuel_com_' + groupe_id).addClass('isalert');
			}
			else {
				$('#qte_residuel_com_' + groupe_id).removeClass('isalert');
			}
		}
	});

	updateProjetsAlert();
}

function toggleAcq(projet_id, numero) {
    $('.acq').each(function() {
        if($(this).css('display') !== 'none' && this.id !== 'acquisition_' + projet_id) {
            <?php if (!isset($_GET['project_id'])) { ?>
            $(this).slideToggle(400);
            <?php } ?>
        }
    });

    if ($('#acquisition_' + projet_id).html() == '') {
    	$('#acquisition_' + projet_id).load("/acquisition/acquisitions_projet.php?projet_id="+projet_id+"&projet_numero="+numero)
    }
    $('#acquisition_' + projet_id).slideToggle(400);
}

function toggleGr(groupe_id) {
    $('.gr').each(function(){
        if($(this).css('display') !== 'none' && this.id !== 'groupe_' + groupe_id)
        {
            $(this).slideToggle(400);
        }
    });
    $('#groupe_' + groupe_id).slideToggle(400);
}

function toggleCom(groupe_id)
{
    $('.co').each(function(){
        if($(this).css('display') !== 'none' && this.id !== 'com_' + groupe_id)
        {
            $(this).slideToggle(400);
        }
    });
    $('#com_' + groupe_id).slideToggle(400);
}

function checkResiduelGroupes() {
	$('div.gr').each(function(){
		var total_res = 0;
		$(this).find('.residuel_com').each(function(){
			total_res += parseInt($(this).html());
        });
		total_res += parseInt($('#tr_' + $(this).data('groupe_id')).find('span.residuel_dist').html());

		if(total_res !== 0) {
			$('#alert_' + $(this).data('groupe_id')).show();
        } else {
			$('#alert_' + $(this).data('groupe_id')).hide();
        }
    });
}

$('#blocFiltre1').on('click', '#filtrer', function(){
	appliqueFiltre1();
});
$('#blocFiltre1').on('change', '#projet_id', function(){
    appliqueFiltre1();
});
$('#blocFiltre1').on('change', '#numero_groupe', function(){
    appliqueFiltre1();
});
$('#blocFiltre1').on('change', '#statut', function(){
    appliqueFiltre1();
});
$('#blocFiltre1').on('change', '#titre_jalon_en_cours', function(){
    appliqueFiltre1();
});
$('#blocFiltre1').on('change', '#reference', function(){
    appliqueFiltre1();
});
$(document).on('click', '#a_reviser', function(){
    appliqueFiltre1();
});
$(document).on('click', '#a_distribuer', function(){
    appliqueFiltre1();
});

function appliqueFiltre1() {
    if ($('#a_reviser').is(":checked"))
    {
        a_reviser_check = true;
    } else {
        a_reviser_check = false;
    }
    if ($('#a_distribuer').is(":checked"))
    {
        a_distribuer_check = true;
    } else {
        a_distribuer_check = false;
    }


    window.location.href = 'index.php?section=acquisition&module=projets&projet_id=' +
        $("select#projet_id").val() + '&numero_groupe=' + $("select#numero_groupe").val() + '&date_debut=' + $("#date_debut").val() + '&date_fin=' + $("#date_fin").val() +
        '&statut='+$("select#statut").val() + '&titre_jalon_en_cours=' + $("select#titre_jalon_en_cours").val() +
        '&reference=' + $("select#reference").val() + '&a_reviser=' + a_reviser_check + '&a_distribuer=' + a_distribuer_check;
}

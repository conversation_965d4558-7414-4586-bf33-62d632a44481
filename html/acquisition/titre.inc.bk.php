<h1 class="ficheTitre" style="margin-bottom: 20px; position: relative;">
    <div style="float: left;">
        <?= $titre ?>
    </div>
    <div style="float: right;">
        <a class="btn  <?=($_GET['module'] == 'equipements_non_traites' ? 'actif' : '')?>" href="index.php?section=acquisition&module=equipements_non_traites"><?php echo txt('Non traités'); ?></a>
		<? if (GestionnaireRoute::instance()->haveAccesRoute('acquisition-locaux')) { ?>
            <a class="btn <?=($_GET['module'] == 'locaux' ? 'actif' : '')?>" href="index.php?section=acquisition&module=locaux"><?php echo txt('Locaux'); ?></a>
		<? } ?>
        <a class="btn <?=($_GET['module'] == 'projets' ? 'actif' : '')?>" href="index.php?section=acquisition&module=projets"><?php echo txt('Gestion spécifique'); ?></a>
        <a class="btn <?=($_GET['module'] == 'calendrier_acquisitions' ? 'actif' : '')?>" href="index.php?section=acquisition&module=calendrier_acquisitions"><?php echo txt('Calendrier'); ?></a>
        <a class="btn <?=($_GET['module'] == 'gantt' ? 'actif' : '')?>" href="index.php?section=acquisition&module=gantt">Gantt</a>
		<?
		if (havePermission('admin_acquisitions') && $_GET['module'] == 'projets') {
			?>
            <a class="btn fancy_big" href="index.php?section=admin&module=edit&table=acquisitions_projets_administrations&type=client&id=<?= (isset($admin) ? $admin->id : 0) ?>"><?php echo txt('Administration'); ?></a>
			<?
		}
		if (isMaster() && $_GET['module'] == 'projets') {
			$adminSuivi = SuivisBudgetairesAdministrations::getAdminForProjet();
			?>
            <a class="btn fancy_big" href="index.php?section=admin&module=edit&table=suivis_budgetaires_administrations&type=client&id=<?= (isset($adminSuivi) ? $adminSuivi->id : 0) ?>"><?php echo txt('Administration suivi budgétaire'); ?></a>
			<?
		}
		if (havePermission('synchronisation_donnes_acquisitions') && $_GET['module'] == 'projets') {
			?>
            <a class="btn fancy" href="index.php?section=acquisition&module=import_avance"><?=txt('Importation')?></a>
			<?
		}
		if (havePermission('gestion_groupe_usager_acquisitions')) {
			?>
            <a class="btn <?=($_GET['module'] == 'groupes_usagers' ? 'actif' : '')?>" href="index.php?section=acquisition&module=groupes_usagers"><?= txt('Groupes d\'usager') ?></a>
			<?
		}

        if (havePermission('gestion_usagers_notifications_jalons')) {
            ?>
            <a class="btn <?=($_GET['module'] == 'notifications' ? 'actif' : '')?>" href="index.php?section=acquisition&module=notifications"><?= txt('Notifications') ?></a>
            <?
        }
		?>
    </div>
</h1>
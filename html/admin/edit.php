<?php
ini_set("memory_limit","8000M");
ini_set('max_execution_time', 30000);

$id = intval($_GET['id']);
$table = $_GET['table'] ?? '';
$typeTable = $_GET['type'];

if (in_array($table, ['prises_electriques_coordinations', 'prises_communications_coordinations'])) {
    $_GET['equipement_bio_medicaux_coordination'] = 1;
}
if (isset($_GET['formulaire'])) {
    $className = $_GET['formulaire'];
} else {
    $className = $tables[$typeTable][$table]['className'];
}

$isValidationBtns = isset($tables['master'][$table]) && isMaster() && $id > 0;

if (!isset($validColumns['doc'][$table]) && !in_array($table, $TablesCategoriesPermissions)) {
    die();
}
if (isset($_POST) && count($_POST) > 0 && !isset($_POST['checks']) && !isset($_GET['stayOpen'])) {
    PopupEdition::traitePostData($id, $table, $className);
}

$noHeader = true;
include('config/header.inc.php');
if (isset($_GET['formulaire']) && isset($_GET['from_fiche']) && !(isset($_POST) && count($_POST) > 0)) {
	callNodeWebservice("formulaire-ouvert", Formulaires::getIdFromNom($_GET['formulaire']), intval($_GET['from_fiche']));
}
?>
<script>
	function closePopup() {
		<?
		if (isset($_GET['formulaire']) && isset($_GET['from_fiche'])) {
		?>
		    formulaireFerme('<?=$CodeClient?>', <?=getIdUsager()?>, <?=getOuvrageId()?>, <?=Formulaires::getIdFromNom($_GET['formulaire'])?>, <?=intval($_GET['from_fiche'])?>);
		<?
		} else {
		?>
            window.parent.lastOpenedForm = "";
            parent.$.fancybox.close();
		<?
		}
		?>
	}
</script>
<?
$useData = false;
if ($id == 0 || (isset($_POST) && count($_POST) > 0)) {
    if (isset($_POST) && count($_POST) > 0) {
        $useData = true;
    }
    $data = PopupEdition::ajustementDataForObj($id, $table);
    $obj = PopupEdition::preparationObjAfterSubmit($id, $data, $className, $table);
}
else {
    $obj = new $className($id);

    if ($table == 'fiches') {
        $_GET['is_gabarit'] = $obj->is_gabarit;
        if (isset($_GET['mode']) && $_GET['mode'] == 'cloner_fiche') {
            $obj->code = '';
            $obj->code_alternatif = '';
            $obj->id_revit = '';
            $obj->id_codebook = '';
            $obj->batiment_niveau_element_id = 0;
        }

        if (isset($_GET['batiment_id'])) {
            $obj->batiment_id = intval($_GET['batiment_id']);
        }
    }

    if ($table == 'formulaires_listes' && isset($_GET['mode']) && $_GET['mode'] == 'cloner') {
        $obj->id = 0;
        $_GET['id'] = 0;
        $id = 0;
    }

    if (in_array($table, $TablesCategoriesPermissions)) {
        $sql_permission = "SELECT nom FROM master.permissions WHERE id = ?";
        $permission_info = $db->getAll($sql_permission, [$_GET['id']]);

        foreach ($permission_info as $perm_info) {
            $obj->nom = $perm_info['nom'];
        }
    }

    if (isset($_GET['date_travaux'])) {
        $obj->date_travaux = $_GET['date_travaux'];
        $obj->date_travaux_readonly = 1;
    }
}

PopupEdition::afficheFormulaire($id, $obj, $className, $table, $typeTable, $useData, $isValidationBtns);

$includeAdminEdition = 1;
include('config/footer.inc.php');
?>
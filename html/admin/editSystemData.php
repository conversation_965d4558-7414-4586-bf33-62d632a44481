<?php
$table = $_GET['onglet'];
$type = $_GET['type'];
$to_compare = $_GET['to_compare'] ?? '';

$_SESSION['to_compare'] = $to_compare;
if ($table <> 'gestions_portes_types') {
    $_SESSION['selected_onglet_' . $type] = $table;
}

$canExportIntrinseque = false;
$usagerInfo = getUsager();
$className = $tables[$type][$table]['className'];

$formulaires_listes = array();
if (method_exists($className, 'getFormulairesListes')
    && is_callable(array($className, 'getFormulairesListes')))
{
    $formulaires_listes = $className::getFormulairesListes($className);
}

$isMasterTable = isset($tables['master'][$table]);
?>
<script>
$(document).ready(function(){

	$('a.exportBio').click(function(){

		$.startObjectLoader($('.ui-dialog'), 'export_loader');

		fetch($(this).data('href') + ($('#exclure_inactifs').prop('checked') ? '&exclure_inactifs=1' : '') + ($('#inclure_quantites').prop('checked') ? '&inclure_quantites=1' : ''), {
			method: 'GET'
		})
			.then(response => response.blob())
			.then(blob => {
				var url = window.URL.createObjectURL(blob);
				var a = document.createElement('a');
				a.href = url;
				a.download = "Équipements biomédicaux.xlsx";
				document.body.appendChild(a); // we need to append the element to the dom -> otherwise it will not work in firefox
				a.click();
				a.remove();  //afterwards we remove the element again
				$.stopObjectLoader('export_loader');
			});

		return false;

	});
	
    $(document).find("a.fancy").click(function(){
        const href = $(this).attr('href');
        $.fancybox({
            'overlayShow'		:	false,
            'type'				:	'iframe',
            'modal'				:	true,
            'centerOnScroll'	:	true,
            'width'             :   1024,
            'href'              :	href,
            'onComplete'		:	function(){
                $(document).find('#fancybox-frame').load(function() { // wait for frame to load and then gets it's height
                    fancybox_resize();
                });
            },
			'onClosed'			:	function(){
				$('html').css({ overflow: 'auto' });
			}
        });
        return false;
    });

    $(document).find("a.fancy_big").click(function(){
        const href = $(this).attr('href');
        $.fancybox({
            'overlayShow'		:	false,
            'type'				:	'iframe',
            'modal'				:	true,
            'centerOnScroll'	:	true,
            'width'             :   1024,
            'href'              :	href,
            'onComplete'		:	function(){
                $(document).find('#fancybox-frame').load(function() { // wait for frame to load and then gets it's height
                    fancybox_resize(1400);
                });
            },
			'onClosed'			:	function(){
				$('html').css({ overflow: 'auto' });
			}
        });
        return false;
    });

    $(document).find("a.fancy_small").click(function(){
        const href = $(this).attr('href');
        $.fancybox({
            'overlayShow'		:	false,
            'type'				:	'iframe',
            'modal'				:	true,
            'centerOnScroll'	:	true,
            'width'             :   1024,
            'href'              :	href,
            'onComplete'		:	function(){
                $(document).find('#fancybox-frame').load(function() { // wait for frame to load and then gets it's height
                    fancybox_resize(500);
                });
            },
			'onClosed'			:	function(){
				$('html').css({ overflow: 'auto' });
			}
        });
        return false;
    });
});
</script>
<style>
<?
if (!defined("$className::CAN_COMPARE") || !$className::CAN_COMPARE) {
    print '#to_compare_parent { display: none; }';
}
?>
th, td { white-space: nowrap; }
.dtsp-searchPane button, .dtsp-titleRow button {
     -webkit-box-shadow: inherit!important;
    -moz-box-shadow: inherit!important;
     box-shadow: inherit!important;
}
div.dtsp-topRow.dtsp-bordered {
    border: 2px solid #dfdfdf;
    border-radius: 3px;
}
.dataTables_wrapper {
    min-height: 150px!important;
}
</style>
<div class="mainTitle">
    <?
    if (method_exists($className, 'afficheTitreEtOnglets') && !in_array($className, ['GestionsPortesTypes', 'Echeanciers'])) {
        ?>
        <style>h1 { margin-bottom: 0!important; }</style>
        <?
        $className::afficheJavascript();
        $className::afficheTitreEtOnglets();
        $className::afficheCustomStyle();
        $className::traiteFiltre(!empty($to_compare));
        $className::afficheFiltre(215);
        print '<br style="clear: both;" />';
    } else {
        ?>
        <h1 class="putLeft">
            <?=$tables[$type][$table]['nom']?><?=(isMaster() ? ' ['.$table.']' : '')?>
        </h1>
        <?
    }
    ?>
	<div class="putRight" style="position: relative;">
<?
    $condition = false;
    if (GestionnaireRoute::instance()->routeExiste("admin-edit-$table-ajout") || GestionnaireRoute::instance()->routeExiste("admin-edit-$table")) {
	    $condition = GestionnaireRoute::instance()->haveAccesRoute("admin-edit-$table-ajout");
    } else {
	    $condition = havePermission('gestion_elements_listes');
    }
    if (in_array($table, $tables_for_listes) && isMasterOrMario()) {
?>
        <a style="margin-right: 25px;" class="insert desactiverInutilises" data-action="desactiverInutilises" data-table="<?= $table ?>" data-confirmation="<?= txt('Êtes-vous certain de vouloir désactiver les éléments non utilisés?', false) ?>" title="<?= txt('Désactiver tous les éléments non utilisés') ?>">
            <span><?= txt('Désactiver les éléments non utilisés') ?></span><img src="css/images/icons/dark/scissors.png" /></a>
    <?
    }
    if($className == 'EquipementsBioMedicauxTypes')
    {
        $admin = AdminListeColonnesBioMedicaux::getRow(['projet_id' => getProjetId()]);
        $id = isset($admin) ? $admin->get('id') : 0;
        ?>
        <a href="index.php?section=admin&module=edit&table=admin_liste_colonnes_bio_medicaux&type=client&id=<?= $id ?>" style="margin-right: 25px;" class="fancy insert" title="<?=txt('Administration des colonnes à afficher')?>">
            <span><?=txt('Colonnes affichées')?></span><img src="css/images/icons/dark/table.png" /></a>
        <?
    }

    if (count($formulaires_listes) > 0) {
        if (GestionnaireRoute::instance()->haveAccesRoute('admin-import_documents_lot')) {
?>
            <a href="index.php?section=admin&module=import_documents_lot&nom_table=<?=$table?>" style="margin-right: 25px;" class="fancy_big insert" title="<?=txt('Import de documents en lot')?>">
                    <span><?=txt('Documents')?></span><img src="css/images/icons/dark/folder.png" /></a>
<?
        }
        if (GestionnaireRoute::instance()->haveAccesRoute('admin-logs_listes_param')) {
?>
            <a href="index.php?section=admin&module=logs_listes_param&id=<?=$formulaires_listes[0]->id?>" style="margin-right: 25px;" class="fancy_big insert" title="<?=txt('Historique des imports des paramètres intrinsèques')?>" data-bigger_fancy="true">
                <span><?=txt('Historique')?></span><img src="css/images/icons/dark/clock.png" /></a>
            <?
        }
        if (GestionnaireRoute::instance()->haveAccesRoute('admin-import_listes_parametres')) {
            ?>
            <a href="index.php?section=admin&module=import_listes_parametres&type=<?=$type?>&id=<?=$formulaires_listes[0]->id?>" style="margin-right: 25px;" class="fancy insert" tableName="<?=$table?>" title="<?=txt('Importer les paramètres intrinsèques')?>">
                <span><?=txt('Importer')?></span><img src="css/images/icons/dark/arrow_down_left.png" /></a>
<?
        }
	    if (GestionnaireRoute::instance()->haveAccesRoute('export_listes_param')) {
		    ?>
            <a href="javascript: void(0);" class="insert" style="margin-right: 25px;" id="export_intrinseque">
                <span><?=txt('Exporter')?></span><img src="css/images/icons/dark/arrow_up_right.png"></a>
		    <?
		    $canExportIntrinseque = true;
	    }
    }
    else if($table == 'batiments_niveaux')
    {
?>
            <a href="index.php?section=admin&module=import_batiments_niveaux" style="margin-right: 25px;" class="fancy insert" title="<?=txt('Importer')?>">
                <span><?=txt('Importer')?></span><img src="css/images/icons/dark/arrow_down_left.png" /></a>

            <a href="index.php?section=admin&module=export_batiments_niveaux" style="margin-right: 25px;" class="exporter insert" title="<?=txt('Exporter')?>">
                <span><?=txt('Exporter')?></span><img src="css/images/icons/dark/arrow_up_right.png"></a>
<?
    }
    else if($table == 'usagers')
    {
?>
            <a href="index.php?section=admin&module=export_usagers" style="margin-right: 25px;" class="exporter insert" title="<?=txt('Exporter')?>">
                <span><?=txt('Exporter')?></span><img src="css/images/icons/dark/arrow_up_right.png"></a>
<?
    }

    if ($table == 'modeles' && GestionnaireRoute::instance()->haveAccesRoute('admin-import_modeles')) {
        ?>
        <a href="index.php?section=admin&module=import_modeles" style="margin-right: 25px;" class="fancy insert" tableName="<?=$table?>" title="<?=txt('Importer les modèles')?>">
            <span><?=txt('Importer')?></span><img src="css/images/icons/dark/arrow_down_left.png" /></a>
        <?
    }

    if (in_array($table, $tables_for_listes) && isMasterOrMario()) {
	    ?>
        <a href="javascript: void(0);" style="margin-right: 25px;" class="is_actif_all" data-table="<?=$table?>" title="<?=txt('Activer tous les éléments')?>">
            <span><?=txt('Activer tous les éléments')?></span><img src="css/images/icons/dark/tick.png" /></a>
	    <?
    }

    if($table <> 'formulaires' && $condition)
    {
        if (!in_array($table, $TablesCategoriesPermissions)) {
            ?>
            <a href="index.php?section=admin&module=edit&table=<?= $table ?>&type=<?= $type ?>&id=0"
               class="<?= (in_array($table, $TablesFancyBig) ? 'fancy_big' : 'insert fancy') ?>" tableName="<?= $table ?>">
                <span><?= txt('Ajouter') ?></span><img src="css/images/icons/dark/plus.png"/></a>
            <?
        }
    } else {
	    ?>
            <img src="css/images/icons/dark/plus.png" style="opacity: 0;" />
	    <?
    }
        ?>
        <div style="position: absolute; right: 315px; bottom: -39px; z-index: 111; display: none; width: 380px;">
            <?
            if ($className == 'EquipementsBioMedicauxTypes') {
            ?>
                <label class="label" style="float: right;">
                    <input type="checkbox" id="displayCoordination" value="0" style="position: relative; top: 2px;"/>&nbsp;<?= txt('Coordination') ?>
                </label>
	            <?
            }
            ?>
            <label class="label" style="margin-right: 40px; float: right;">
                <input type="checkbox" id="showInactifs" value="0" style="position: relative; top: 2px;"/>&nbsp;<?= txt('Inclure les inactifs') ?>
            </label>
        </div>
	</div>
</div><br style="clear: both;" />
<iframe id="dataTableIframe" src="datatable.php?onglet=<?= $_GET['onglet'] ?>&type=<?= $_GET['type'] ?>&to_compare=<?= $to_compare ?>" style="width:100%; height: 700px;">
</iframe>
<br style="clear: both;" />
<script>
    $.startObjectLoader($('#dataTableIframe'), 'dataTableIframe_loader');
</script>
<?
if ($canExportIntrinseque) {
    ?>
    <div id="dialog-export-intrinseque" title="<?=txt('Export')?>" style="display: none; margin: 5px 15px;">
        <p id="message" style="margin-top: 45px;">

            <a data-href="index.php?section=admin&module=export_listes_param&type=<?=$type?>&id=<?=$formulaires_listes[0]->id?>&type=regulier" style="margin-right: 25px;" class="exportBio insert">
                <span><?=txt('Export régulier')?></span><img src="css/images/icons/dark/arrow_up_right.png"></a>

            <span style="margin: 0 0 20px 0; display: block;"><?=txt('Cet export permet de générer un fichier Ms Excel contenant l’ensemble des données « brutes » de la librairie. Il ne comportera aucun alias, aucune conversion booléenne, ni colonne de validation.')?></span>

            <a data-href="index.php?section=admin&module=export_listes_param&type=<?=$type?>&id=<?=$formulaires_listes[0]->id?>&type=converti" style="margin-right: 25px;" class="exportBio insert">
                <span><?=txt('Export converti')?></span><img src="css/images/icons/dark/arrow_up_right.png"></a>

            <span style="margin: 0 0 20px 0; display: block;"><?=txt('Cet export permet de générer un fichier Ms Excel contenant l’ensemble des données « converties » de la librairie. Il comportera les alias, les conversions booléennes et les colonnes de validation.')?></span>

            <label><input type="checkbox" id="exclure_inactifs" name="exclure_inactifs" value="1" style="position: relative; top: 2px;" />&nbsp;<?=txt('Exclure les éléments inactifs')?></label>
            <br /><br />
            <label><input type="checkbox" id="inclure_quantites" name="inclure_quantites" value="1" style="position: relative; top: 2px;" />&nbsp;<?=txt('Inclusion des totaux distribués')?></label>

        </p>
    </div>
    <?
}


<?
$fiche_id = (int)$_GET['fiche_id'];
$formulaire_liste = new FormulairesListes(intval($_GET['formulaire_liste_id']));

$is_public = 1;
if (havePermission('can_edit_documents') || isMaster()) {
	$is_public = 'all';
}
$documents = DocumentsListesElements::getListe(array('is_public' => $is_public, 'nom_table' => $formulaire_liste->nom_table, 'liste_item_id' => intval($_GET['liste_item_id']), 'fiche_id' => $fiche_id));

$images = DocumentsListesElements::getListe(array('nom_table' => $formulaire_liste->nom_table, 'liste_item_id' => intval($_GET['liste_item_id']), 'is_image_principale' => 1));
$images = array_values($images);
$image = isset($images[0]) ? $images[0] : null;

$equipement = new EquipementsBioMedicauxTypes(intval($_GET['liste_item_id']));

$url_image = 'Docmatic.png';
if (isset($image)) {
	$url_image = 'index.php?action=download_document&id=' . $image->id;
}

$noHeader = true;
include('config/header.inc.php');
?>
    <script>
		$(document).ready(function () {
			$(document).on('change', 'select.is_public_fiche', function () {
				let document_id = $(this).data('document_id');
				let fiche_id = $(this).data('fiche_id');
				let is_public_fiche = $(this).val();

				$.get('index.php', {
					action: 'update_document_is_public_fiche',
					document_id: document_id,
					fiche_id: fiche_id,
					is_public_fiche: is_public_fiche
				});
			});
		});
    </script>
    <style>
        .not_enabled {
            opacity: 0.3;
        }
    </style>
    <div id="popupContent" class="edit_popupContent" style="">
        <fieldset>
            <form>
                <?
                $nom_equipement = $equipement->nom.' - '.$equipement->description;
                if (dm_strlen($nom_equipement) > 80) {
	                $nom_equipement = dm_substr($nom_equipement, 0, 80).'...';
                }
                ?>
                <label>
                    <span style="float: left; font-size: 16px; font-weight: 700;">
                        <?=$nom_equipement?>
                    </span>
                    <?= txt('Requis techniques') ?>
                </label>
            </form>
            <div id="editables" style="max-width: 1005px;">
                <div style="margin-top: 22px; width: 48%; float: left;">
					<?
					if (count($documents) > 0 && (isMaster() || havePermission('consultation_documents'))) {
						?>
                        <div style="font-weight: bold; font-size: 16px; margin-left: 25px; margin-bottom: 7px;">
							<?= txt('Document(s) lié(s):') ?>
                        </div>
						<?
						if (havePermission('can_edit_documents') || isMaster()) {
							?>
                            <div style="margin: 15px 0 10px 25px;">
                                <div style="width: 100px; float: left; text-align: center; font-weight: bold;">
									<?= txt('Réglage global') ?>
                                </div>
                                <div style="margin-left: 10px; width: 100px; float: left; text-align: center; font-weight: bold;">
									<?= txt('Réglage fiche') ?>
                                </div>
                                <div style="margin-left: 5px; float: left;">
                                    &nbsp;
                                </div>
                                <div style="margin-left: 10px; width: 100px; float: left; text-align: center; font-weight: bold;">
									<?= txt('Document(s)') ?>
                                </div>
                                <br style="clear: both;"/>
                            </div>
							<?
						}
						?>
						<?
						foreach ($documents as $document) {
							?>
                            <div style="margin: 0 0 7px 25px;">
								<?
								if (havePermission('can_edit_documents') || isMaster()) {
									$img_txt = $document->is_public == 1 ? txt('Visible') : 'Non-visible';
									$chk = $document->is_public_fiche == 1 ? ' checked="checked"' : '';
									?>
                                    <div style="width: 100px; float: left; text-align: center;">
                                        <span style="position: relative; top: 2px;"><?= $img_txt ?></span>
                                    </div>
                                    <div style="margin-left: 10px; width: 100px; float: left; text-align: center;">
                                        <select class="is_public_fiche" data-document_id="<?= $document->id ?>"
                                                data-fiche_id="<?= $fiche_id ?>">
                                            <option value="-1"<?= ($document->is_public_fiche == -1 ? ' selected="selected"' : '') ?>><?= txt('Comme global') ?></option>
                                            <option value="1"<?= ($document->is_public_fiche == 1 ? ' selected="selected"' : '') ?>><?= txt('Visible') ?></option>
                                            <option value="0"<?= ($document->is_public_fiche == 0 ? ' selected="selected"' : '') ?>><?= txt('Non-visible') ?></option>
                                        </select>
                                    </div>
									<?
								}
								?>
                                <div style="margin-left: <?= (havePermission('can_edit_documents') || isMaster() ? '30px' : '0px') ?>; float: left;; width: 205px">
                                    <a href="index.php?action=download_document&id=<?= $document->id ?>" target="_blank"
                                       style="position: relative; top: 2px;"><?= $document->nom ?></a>
                                </div>
                                <br style="clear: both;"/>
                            </div>
							<?
						}
					}
					?>
                </div>
                <div style="margin-top: 22px; width: 48%; float: left;">
                    <img src="<?= $url_image ?>" id="thumb_preview" height="200"
                         style="<?= (isset($image) ? '' : 'display: none;') ?> float: right; box-shadow: 9px 9px 11px -6px rgba(0,0,0,0.45);">
                </div>
                <div style="margin-top: 22px; width: 100%; float: left;">
		            <?
		            $equipement->getValuesPopupForFondamentaux();
		            ?>
                </div>
				<?
                if (!empty(ClientsMaster::instance()->code_client_referentiel) && !empty($equipement->referentiel_code_equipement)) {
                    ?>
                    <div style="margin-top: 22px; width: 100%; float: left;">
                    <?
                    $equipement->getValuesPopupForDonneesReferencielles();
                        ?>
                    </div>
                    <?
                }
				if (havePermission('admin_budgets')) {
					?>
                    <div style="margin-top: 22px; width: 100%; float: left;">
						<?
						$equipement->getValuesPopupForBudget();
						?>
                    </div>
					<?
				}
				?>

                <div style="margin-top: 22px; width: 100%; float: left;">
					<?= FormulairesListesParametres::getValuesPopup($formulaire_liste, intval($_GET['liste_item_id'])); ?>
                </div>
            </div>
        </fieldset>

        <div style="text-align: right; width: 98%!important;">
            <?
            if (GestionnaireRoute::instance()->haveAccesRoute('gestion_equipements-index')) {
            ?>
                <a onclick="parent.window.location = 'index.php?section=gestion_equipements&module=index&forced_equipement_id=<?= $equipement->id ?>'" style="margin-right: 25px; text-decoration: underline">
                    <?= txt('Afficher la liste des locaux') ?></a>
            <?
            }
            ?>
            <button onclick="parent.$.fancybox.close();return false;" name="close"><?= txt('Fermer') ?></button>

        </div>
    </div>
<?
include('config/footer.inc.php');
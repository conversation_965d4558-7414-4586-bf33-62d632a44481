<?
require_once __DIR__ . '/../../vendor/autoload.php';

use PhpOffice\PhpSpreadsheet\Cell\Coordinate;
use PhpOffice\PhpSpreadsheet\IOFactory;

ini_set("memory_limit","10000M");
ini_set('max_execution_time', 30000);

$etape = 'valider';
if(isset($_GET['export_doublons'])) {
    $formulaire_custom = new FormulairesCustoms(intval($_SESSION['synchro_data']['formulaire_id']));
    $lignes_to_import = $_SESSION['synchro_lignes_doublons'];
	$params_to_import = $_SESSION['params_to_import'];
	unset($_SESSION['synchro_lignes_doublons']);
	unset($_SESSION['params_to_import']);

	$col = 0;
	$dataArray = array();
	$dataArray[0][$col++] = 'Fiche';
	foreach ($lignes_to_import as $fiche_code => $elements_data) {
		foreach ($elements_data as $iter => $data_to_import) {
			foreach ($data_to_import as $parametre_id => $valeur) {
				$dataArray[0][$col++] = $nom_parametre;
			}
			break;
		}
		break;
	}

	$noLigne = 1;
	foreach ($lignes_to_import as $fiche_code => $elements_data) {
		foreach ($elements_data as $iter => $data_to_import) {
			$col = 0;
			$dataArray[$noLigne][$col++] = $fiche_code;
			foreach ($data_to_import as $nom_parametre => $valeur) {
				$dataArray[$noLigne][$col++] = $valeur['new_valeur'];
			}
			$noLigne++;
		}
		unset($lignes_to_import[$fiche_code]);
	}
	exportExcelFromDataArray($dataArray, 'doublons_' . $formulaire_custom->nom_formulaire, 'doublons_' . $formulaire_custom->nom_formulaire);
	exit;
} else if(isset($_POST['synchroniser'])) {
    $formulaire_custom = new FormulairesCustoms(intval($_SESSION['synchro_data']['formulaire_id']));

    $ficheByCode = Fiches::getListe(['by_code' => 1]);
	$nb_alias_popu = 0;
	foreach ($_POST as $key => $value) {
		if (dm_substr($key, 0, 6) == 'alias_' && isset($value) && dm_strlen($value) > 0) {
			$nb_alias_popu++;
		}
	}

	$all_cols = $_SESSION['synchro_data']['all_cols'];
	$champs = $_SESSION['synchro_data']['champs'];
	$lignes = $_SESSION['synchro_data']['lignes_a_importer'];

	$params_to_import = array();

	foreach ($all_cols as $i => $parametre) {
		if (isset($_POST['alias_' . ($parametre['id'])]) && dm_strlen($_POST['alias_' . ($parametre['id'])]) > 0) {
			$params_to_import[$_POST['alias_' . ($parametre['id'])]] = $parametre;
		}
	}

    $fichesNonValides = [];
	$champsNumeriquesNonValides = [];
	$lignes_to_import = array();
	foreach ($lignes as $col_iter => $champs) {
        $fiche_code = $champs[$_POST['code_pivot_externe']];
        $iter = isset($lignes_to_import[$fiche_code]) ? count($lignes_to_import[$fiche_code]) + 1 : 1;
        foreach ($params_to_import as $lettreCol => $parametre) {
            $new_valeur = $champs[$lettreCol] ?? '';
            if ($parametre['format'] == 'numerique') {
                $new_valeur = dm_str_replace('.', ',', $new_valeur);
            }
            $lignes_to_import[$fiche_code][$iter][$parametre['id']] = array(
                'parametre_id' => $parametre['id'],
                'new_valeur' => dm_trim($new_valeur),
                'type' => 'dynamic'
            );
        }
        unset($lignes[$col_iter]);
	}

	if (count($champsNumeriquesNonValides) > 0) {
		setErreur(txt('Les cellules suivantes doivent être numériques') . ': ');
		foreach ($champsNumeriquesNonValides as $i => $champ) {
			setErreur(txt('Ligne ') . $champ['ligne'] . ', ' . txt('Paramètre ') . '"' . $champ['nom_parametre'] . '"');
		}
	}

	if ($nb_alias_popu == 0) {
		setErreur(txt('En plus du pivot, vous devez minimalement associer un second champ pour que la matrice soit en mesure d\'interpréter adéquatement les données à importer.'));
	}

	if (isset($_POST['code_pivot_externe']) && dm_strlen($_POST['code_pivot_externe']) == 0) {
		setErreur(txt('La colonne associée au pivot est obligatoire.'));
	}

	if (nbErreurs() > 0) {
		$etape = 'synchroniser';

		foreach ($all_cols as $i => $parametre) {
			if (isset($_POST['alias_' . ($parametre['id'])])) {
				$all_cols[$i]['alias'] = $_POST['alias_' . ($parametre['id'])];
			}
		}
	} else {
		$db->autocommit(false);

        $listeFormuleByNomSortie = ConversionsUnites::getListeFormuleByNomSortie();

		$unitesNonSupportees = [];
		$donnees_rapport = array();
		$valeurs = FormulairesCustoms::getValeursByElements($formulaire_custom->id);
		$fiche_ids_changed = array();
        $champs_a_notifier = array();
		foreach ($lignes_to_import as $fiche_code => $elements_data) {
			$data_to_import = array();
			if (count($elements_data) > 1) {
				$occurences = array();
				foreach ($elements_data as $iter => $data_to_import_tmp) {
					$occurences[uniqueKey(serialize($data_to_import_tmp))] = 1;
					$data_to_import = $data_to_import_tmp;
				}

				if (count($occurences) == 1) {
					unset($lignes_to_import[$fiche_code]);
				}
			} else {
				$data_to_import = $elements_data[1];
				unset($lignes_to_import[$fiche_code]);
			}

			$statut_existant = 'Existant';
			if (!isset($ficheByCode[$fiche_code])) {
                $sql = "INSERT INTO `fiches` 
                            (`fiche_type_id`, `fiche_sous_type_id`, `code`, `ts_ajout`, `ouvrage_id`) 
                            VALUES ('2000002', '2000002', ?, current_timestamp, ?)";
                $db->query($sql, [$fiche_code, getOuvrageId()]);
                $fiche_id = $db->lastInsertId();
				$statut_existant = 'Nouveau';

                $data_to_add = array();
                $data_to_add['formulaire'] = 'GenerauxLocaux';
                $data_to_add['fiche_id'] = $fiche_id;

                $metas = GenerauxLocaux::getFlatChampsSaisieMetaData();
                foreach($metas as $key => $meta) {
                    $champ_id = $meta['champ_id'];
                    $data_to_add[$champ_id][1] = '';
                }

                $data_to_add['code'][1] = $fiche_code;
                $data_to_add['fiche_type_id'][1] = 2000002;
                $data_to_add['fiche_sous_type_id'][1] = 2000002;
                $data_to_add['raison'] = 'Importation via le formulaire personnalisé ' . $formulaire_custom->nom_formulaire;
                GenerauxLocaux::save($data_to_add, true);
			} else {
                $fiche_id = $ficheByCode[$fiche_code]->id;
            }

			foreach ($data_to_import as $nom_parametre => $valeur) {
                $parametre = $formulaire_custom->listes_parametres[$valeur['parametre_id']];
                $new_valeur = dm_trim($valeur['new_valeur']);
                $parametre_id = $valeur['parametre_id'];

                if ($parametre['format'] == 'numerique') {
                    $unite = dm_trim(sanitizeString(conserverSeulementTexte($new_valeur)));
                    if (dm_strlen($unite) > 0) {
                        $chiffre = floatVal(dm_str_replace($unite, '', $new_valeur));
                        if ($unite == $parametre['unite']) {
                            $new_valeur = $chiffre;
                        } else {
                            $unitesNonSupportees[$unite] = $unite . ' (Paramètre: ' . $parametre['nom_parametre'] . ')';
                        }
                    }

                }

                $cle_rapport = uniqueKey($fiche_code . $nom_parametre);
                if ((!isset($valeurs[$fiche_id]) || !isset($valeurs[$fiche_id][$parametre_id])) && dm_strlen($new_valeur) > 0) {
                    $sql = "insert into formulaires_customs_parametres_valeurs (formulaire_custom_parametre_id, fiche_id, valeur) values (?, ?, ?)";
                    $db->query($sql, array($parametre_id, $fiche_id, $new_valeur));

                    $donnees_rapport[$cle_rapport] = array(
                        'fiche_id' => $fiche_id,
                        'statut' => $statut_existant,
                        'formulaire_custom_parametre_id' => $parametre_id,
                        'ancienne_valeur' => '',
                        'nouvelle_valeur' => $new_valeur,
                    );
                    $fiche_ids_changed[] = $fiche_id;
                    $champs_a_notifier[] = $parametre_id;
                } else {
                    $old_valeur = isset($valeurs[$fiche_id][$parametre_id]) ? dm_trim($valeurs[$fiche_id][$parametre_id]['valeur']) : '';
                    if ($old_valeur <> $new_valeur) {
                        $sql = "update formulaires_customs_parametres_valeurs set valeur = ? where formulaire_custom_parametre_id = ? and fiche_id = ?";
                        $db->query($sql, array($new_valeur, $parametre_id, $fiche_id));

                        $donnees_rapport[$cle_rapport] = array(
                            'fiche_id' => $fiche_id,
                            'statut' => $statut_existant,
                            'formulaire_custom_parametre_id' => $parametre_id,
                            'ancienne_valeur' => $old_valeur,
                            'nouvelle_valeur' => $new_valeur,
                        );
                        $fiche_ids_changed[] = $fiche_id;
                        $champs_a_notifier[] = $parametre_id;
                    }
                }
			}
		}

		if (count($unitesNonSupportees) > 0) {
			setErreur(txt('Attention! L\'importation n\'a pu être complétée parce que la ou les unités suivantes ne peuvent être importées/converties; ') . '<br /><br />' .dm_implode('<br />', $unitesNonSupportees));
			header('Location: index.php?section=admin&module=import_formulaires_custom&type=client&id='.$formulaire_custom->id);
			exit;
		} else {
			unset($_SESSION['synchro_data']);
			if (count($donnees_rapport) > 0) {
                $bundle_id = getNextSequenceId('formulaires_customs_logs');
				foreach ($donnees_rapport as $key => $donnee) {
					$sql = "insert into `formulaires_customs_logs` (formulaire_custom_id, fiche_id, statut, formulaire_custom_parametre_id, ancienne_valeur, nouvelle_valeur, usager_ajout_id, bundle_id) "
						. "values (?, ?, ?, ?, ?, ?, ?, ?)";
					$db->query($sql, array($formulaire_custom->id, $donnee['fiche_id'], $donnee['statut'], $donnee['formulaire_custom_parametre_id'], $donnee['ancienne_valeur'], $donnee['nouvelle_valeur'], getIdUsager(), $bundle_id));
					unset($donnees_rapport[$key]);
			    }

				if (count($champs_a_notifier) > 0) {
                    foreach ($fiche_ids_changed as $fiche_id) {
                        NotificationsGestion::creationNotificationsCustom(
                            $bundle_id,
                            $fiche_id,
                            $formulaire_custom->id,
                            $champs_a_notifier
                        );
                    }
				}
			}

			$db->commit();

			if (count($lignes_to_import)) {
				$_SESSION['synchro_lignes_doublons'] = $lignes_to_import;
				$_SESSION['params_to_import'] = $params_to_import;
				?>
                <script>
					alert('<?=dm_addslashes(txt('Voici un rapport des éléments qui n\'ont pu être importés car ils contiennent des données contradictoires.', false))?>');
					parent.loading_beforeunload = false;
					parent.window.location = 'index.php?section=admin&module=import_formulaires_custom&export_doublons=1&id=<?=$formulaire_custom->id?>';
					parent.$.fancybox.close();
                </script>
				<?
				exit;
			} else {
				?>
                <script>
					parent.window.location.reload();
					parent.$.fancybox.close();
                </script>
				<?
			}
			die();
		}
	}
}
else if(isset($_POST['valider'])) {
    $_SESSION['synchro_data']['all_cols'] = [];
    $_SESSION['synchro_data']['champs'] = [];
	$_SESSION['synchro_data']['lignes_a_importer'] = [];
	$alias = array();
	$champs = array();

	if (!isset($_FILES['fichier']) || !is_uploaded_file($_FILES['fichier']['tmp_name'])) {
		setErreur(txt('Vous devez sélectionner le fichier à importer.'));
	} else {
        $_SESSION['synchro_data']['formulaire_id'] = $_POST['formulaire_id'];
        $formulaire_custom = new FormulairesCustoms(intval($_SESSION['synchro_data']['formulaire_id']));

		$all_cols = $formulaire_custom->listes_parametres;

		$all_alias = array();
		foreach ($all_cols as $parametre) {
			if (dm_strlen($parametre['nom_parametre']) > 0) {
				$all_alias[$parametre['nom_parametre']] = $parametre['nom_parametre'];
			}
		}
		$spreadsheet = IOFactory::load($_FILES['fichier']['tmp_name']);

		$element_names = array();
		foreach ($spreadsheet->getWorksheetIterator() as $iter_sheet => $worksheet) {
			if ($iter_sheet > 0) {
				break;
			}
			$highestRow = $worksheet->getHighestDataRow(); // e.g. 10
			$highestColumn = $worksheet->getHighestDataColumn(); // e.g 'F'
			$highestColumnIndex = Coordinate::columnIndexFromString($highestColumn);
			$nrColumns = ord($highestColumn) - 64;

			$nb_actif = 0;
			$row_titre = 0;
			for ($row = 1; $row <= $highestRow; ++$row) {
				$nb = 0;
				for ($col = 1; $col <= $highestColumnIndex; ++$col) {
					$cell = $worksheet->getCellByColumnAndRow($col, $row);
					$val = sanitizeString((string)$cell->getCalculatedValue());

					if (isset($all_alias[$val])) {
						$nb++;
					}
				}

				if ($nb > $nb_actif) {
					$row_titre = $row;
                    break;
				}
			}

			if ($nb == 0) {
				$row_titre = 1;
			}

			for ($row = 1; $row <= $highestRow; ++$row) {
				for ($col = 1; $col <= $highestColumnIndex; ++$col) {
					$cell = $worksheet->getCellByColumnAndRow($col, $row);
					$val = sanitizeString((string)$cell->getCalculatedValue());

					$val = dm_trim($val) == '.' ? '' : $val;
					if ($row == $row_titre && dm_strlen($val) > 0) {
						$val = dm_trim(dm_preg_replace("/\r|\n/", "", $val));
						$alias[$col] = $val;
						$champs[getExcelColumnLetter($col)] = $val;
					} else if ($row > $row_titre && isset($alias[$col])) {
						$_SESSION['synchro_data']['lignes_a_importer'][$row][getExcelColumnLetter($col)] = $val;
					}
				}
			}
		}

		$spreadsheet->disconnectWorksheets();
		unset($spreadsheet);

		$_SESSION['synchro_data']['champs'] = $champs;

		$_SESSION['synchro_data']['all_cols'] = $all_cols;

		$_SESSION['synchro_data']['type_import'] = 'excel';
	}

	$etape = 'synchroniser';
}

$noHeader = true;
include('config/header.inc.php');
?>
    <script>
		var $document = $(document);
		$document.ready(function(){

			$document.on('change', 'select.uniques', function(){

				var valeur = this.value;
				var id_select = this.id;

				$document.find('select.uniques').each(function(){

					if(this.value == valeur && this.id != id_select)
					{
						this.value = '';
					}

				});

			});

		});
    </script>
    <style>
        .blocsFiche, #fiche table, #fiche tr, #fiche td, #fiche th, .ui-dialog table, .ui-dialog tr, .ui-dialog td, .ui-dialog th {
            background: none!important;
        }
        .blocsFicheTitre {
            margin-top: 0px!important;
        }
        .readonly {
            text-align: center;
        }
    </style>

    <div id="popupContent">

        <form id="form" autocomplete="off" method="post" action="index.php?section=admin&module=import_formulaires_custom" enctype="multipart/form-data">
            <label><?=txt('Mise-à-jour des données')?></label>
            <fieldset style="min-height: 175px; padding: 10px 0 0 50px;">
				<?
				if(!isset($all_cols))
				{
					?>
                    <div id="editables">
						<?
						erreurs();
						?>
                        <section style="margin: 10px 0 10px 0;">
                            <span for="fichier"><?php print txt('Fichier MS Excel à importer'); ?></span>
                            <span>
                                <input type="file" id="fichier" name="fichier" style="border: solid 0px!important;" />
                            </span>
                        </section>

                        <section style="margin: 10px 0 10px 0;">
                            <span for="formulaire_id"><?php print txt('Formulaire'); ?></span><br />
                            <span>
                                <select id="formulaire_id" name="formulaire_id">
                                    <option value=""><?=txt(' - Sélectionnez un formulaire - ')?></option>
                                    <?
                                    $formulairesCustoms = FormulairesCustoms::getListe();
                                    foreach ($formulairesCustoms as $i => $formulairesCustom) {
                                        ?>
                                        <option value="<?= $formulairesCustom->id ?>"><?= $formulairesCustom->nom_formulaire ?></option>
                                        <?php
                                    }
                                    ?>
                                </select>
                            </span>
                        </section>

                    </div>
					<?
				}
				else
				{
					?>
                    <div id="editables">
						<?
						messages();
						erreurs();
						?>
                        <section style="margin: 10px 0 50px 0;">
                            <table style="width: 95%!important; margin: 10px 0 0 0;">
                                <tr><th>
										<?=txt('Paramètres personnalisés')?>
                                    </th><th>
										<?=txt('Colonnes détectées dans le fichier importé')?>
                                    </th></tr>
                                <tr style="background-color: #e2ffe2"><td style="border-right: solid 1px transparent!important;">
                                        <?=txt('Fiche', false) ?>
                                    </td><td style="border-left: solid 1px transparent!important; position: relative;">
                                        <div style="position: absolute; left: -29px; top: 10px; color: red; font-weight: bold;"><-- <?=txt('Pivot')?> --></div>
                                        <select name="code_pivot_externe" class="uniques" style="max-width: 250px!important;">
                                            <option value=""> </option>
											<?
                                            foreach ($champs as $lettreCol => $alias) {
                                                $sel = $alias == txt('Fiche', false) ? ' selected="selected"' : '';
												?>
                                                <option value="<?= $lettreCol ?>"<?= $sel ?>><?= $alias . ' (' . $lettreCol . ')' ?></option>
                                                <?
											}
											?>
                                        </select>
                                        <span style="width: 24px; height: 24px; display: inline-block;">&nbsp;</span>
                                    </td></tr>
								<?
                                $champsSelectionnes = [];
								foreach($all_cols as $parametre)
								{
                                    if (!in_array($parametre->getRaw('format'), ['categorie', 'section'])) {
									?>
                                    <tr class="champs"><td>
											<?=$parametre['nom_parametre']?>
                                        </td><td>
                                            <select id="alias_<?=($parametre['id'])?>" name="alias_<?=($parametre['id'])?>" class="uniques colonnes" style="max-width: 250px!important;">
                                                <option value=""> </option>
												<?
                                                $alreadySelected = false;
												foreach ($champs as $lettreCol => $alias) {
													$selected = (isset($parametre['alias']) && $alias == $parametre->getRaw('alias')) || $alias == $parametre->getRaw('nom_parametre');
                                                    $sel = '';
                                                    if ($selected && !$alreadySelected) {
                                                        if (!isset($champsSelectionnes[$alias]) || !in_array($lettreCol, $champsSelectionnes[$alias])) {
                                                            $champsSelectionnes[$alias][] = $lettreCol;
                                                            $sel = ' selected="selected"';
                                                            $alreadySelected = true;
                                                        }
                                                    }
													?>
                                                    <option value="<?=$lettreCol?>"<?=$sel?>><?=$alias . ' (' . $lettreCol . ')'?></option>
													<?
												}
												?>
                                            </select>
                                            <img src="css/images/icons/dark/cross_3.png" class="resetAssoc" />
                                        </td></tr>
									<?
                                    }
								}
								?>
                            </table>
                        </section>
                    </div>
					<?
				}
				?>
            </fieldset>

            <div style="text-align: right; width: 98%!important;">
                <?
                if ($etape == 'synchroniser') {
                    ?>
                    <button class="assocAll all" style="color: green;"><?= txt('Tout associer') ?></button>
                    <button class="resetAll all" style="color: red;"><?= txt('Tout dissocier') ?></button>
                    <?
                }
                ?>
                <button class="submit" value="submitbuttonvalue" name="<?=$etape?>"><?=txt($etape)?></button>
                <button onclick="parent.$.fancybox.close();return false;" name="close"><?=txt('Annuler')?></button>

            </div>
        </form>
    </div>
<?
include('config/footer.inc.php');
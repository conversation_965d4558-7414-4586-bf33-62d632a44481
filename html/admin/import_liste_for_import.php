<?
require_once __DIR__ . '/../../vendor/autoload.php';

use PhpOffice\PhpSpreadsheet\Cell\Coordinate;
use PhpOffice\PhpSpreadsheet\IOFactory;

ini_set("memory_limit","10000M");
ini_set('max_execution_time', 30000);

$import = new Imports(intval($_GET['id']));
$class_name = $import->formulaire_id > 0 ? Formulaires::getNomFromId($import->formulaire_id) : '';

$etape = 'valider';

if(isset($_GET['export_doublons']))
{
    $lignes_to_import = $_SESSION['synchro_lignes_doublons'];
    $params_to_import = $_SESSION['params_to_import'];
    unset($_SESSION['synchro_lignes_doublons']);
    unset($_SESSION['params_to_import']);
    
    $col = 0;
    $dataArray = array();
    $dataArray[0][$col++] = txt('Code externe');
    foreach($params_to_import as $alias => $parametre)
    {
        $dataArray[0][$col++] = $parametre['nom_parametre'];
    }
    $noLigne = 1;
    foreach($lignes_to_import as $code_externe => $elements_data)
    {
        foreach($elements_data as $iter => $data_to_import)
        {
            $col = 0;
            $dataArray[$noLigne][$col++] = $code_externe;
            foreach($data_to_import as $nom_parametre => $valeur)
            {
                $dataArray[$noLigne][$col++] = $valeur['new_valeur'];
            }
            $noLigne++;
        }
    }

	exportExcelFromDataArray($dataArray, 'doublons_'.$import->nom, 'doublons_'.$import->nom);
    exit;
}
else if(isset($_POST['synchroniser']))
{
    $nb_alias_popu = 0;
    foreach($_POST as $key => $value)
    {
        if(dm_substr($key, 0, 6) == 'alias_' && isset($value) && dm_strlen($value) > 0)
        {
            $nb_alias_popu++;
        }
    }

    if($nb_alias_popu == 0)
    {
        setErreur(txt('En plus du pivot, vous devez minimalement associer un second champ pour que la matrice soit en mesure d\'interpréter adéquatement les données à importer.'));
    }
    
    if(isset($_POST['code_pivot_externe']) && dm_strlen($_POST['code_pivot_externe']) == 0)
    {
        setErreur(txt('La colonne associée au pivot est obligatoire.'));
    }
    
    if(nbErreurs() > 0)
    {
        $all_cols = $_SESSION['synchro_data']['all_cols'];
        $champs = $_SESSION['synchro_data']['champs'];
        $etape = 'synchroniser';

        foreach($all_cols as $i => $parametre)
        {
            if(isset($_POST['alias_'.uniqueKey($parametre['nom_parametre'])]))
            {
                $all_cols[$i]['alias'] = $_POST['alias_'.uniqueKey($parametre['nom_parametre'])];
            }
        }
    }
    else
    {
        $db->autocommit(false);

        $params_to_import = array();
        $params_static_to_import = array();
        $all_cols = $_SESSION['synchro_data']['all_cols'];
        $lignes = $_SESSION['synchro_data']['lignes_a_importer'];
        
        foreach($all_cols as $i => $parametre)
        {
            if(isset($_POST['alias_'.uniqueKey($parametre['nom_parametre'])]) && dm_strlen($_POST['alias_'.uniqueKey($parametre['nom_parametre'])]) > 0)
            {
                $params_to_import[$_POST['alias_'.uniqueKey($parametre['nom_parametre'])]] = $parametre;
            }
        }
        
        $lignes_to_import = array();
        foreach($lignes as $col_iter => $champs)
        {
            $code_externe = $champs[$_POST['code_pivot_externe']];
            $iter = isset($lignes_to_import[$code_externe]) ? count($lignes_to_import[$code_externe]) + 1 : 1;
            foreach($params_to_import as $alias => $parametre)
            {
                $new_valeur = isset($champs[$alias]) ? $champs[$alias] : '';
                $lignes_to_import[$code_externe][$iter][$parametre['nom_parametre']] = array(
                    'new_valeur' => dm_trim($new_valeur),
                );
            }
        }
        
        $assocs_valeurs = array();
        $metas = $class_name::getChampsSaisieMetaData();

        $iter = 0;
        foreach ($metas as $key => $meta)
        {
            if($meta['type'] == 'select' && !in_array($key, array('quantite_existante', 'quantite', 'quantite_fantome', 'conformite_du_besoin_electricite', 'conformite_du_besoin_plomberie', 'conformite_du_besoin_mecanique', 'conformite_du_besoin_telecom', 'conformite_du_besoin_architecture', 'conformite_du_besoin_structure', 'conformite_du_besoin', 'equipement_electricite', 'equipement_plomberie')))
            {
                if(!($class_name::CAN_HAVE_COORDINATION && $key == 'commentaires'))
                {
                    $classe_liste = $meta['classe'];
                    $assocs_valeurs[html_entity_decode($tables['client'][$meta['table']]['nom'])] = $classe_liste;
                    $iter++;
                }
            }
        }

        unset($_SESSION['synchro_data']);
        $donnees_rapport = array();
//        $valeurs = $import->getValeursByCodeExternes();
        foreach($lignes_to_import as $code_externe => $elements_data)
        {
            if(count($elements_data) > 1)
            {
                $occurences = array();
                $data_to_import = array();
                foreach($elements_data as $iter => $data_to_import_tmp)
                {
                    $occurences[uniqueKey(serialize($data_to_import_tmp))] = 1;
                    $data_to_import = $data_to_import_tmp;
                }
                
                if(count($occurences) == 1)
                {
                    unset($lignes_to_import[$code_externe]);
                }
                else
                {
                    continue;
                }
            }
            else
            {
                $data_to_import = $elements_data[1];
                unset($lignes_to_import[$code_externe]);
            }
            
            $element_id = $import->getIdByCodeExterne($code_externe);
            
            $iter_ligne = 0;
            $code_interne = '';
            foreach($data_to_import as $nom_parametre => $valeur)
            {
                $new_valeur = $valeur['new_valeur'];
                $classe_name_item = $assocs_valeurs[$nom_parametre];
                $liste_item_id = $classe_name_item::getIdByName($new_valeur);
                $code_interne .= ($iter_ligne > 0 ? '_' : '').$liste_item_id;
                $iter_ligne++;
            }

            if($element_id > 0)
            {
                $sql = "UPDATE imports_listes_valeurs SET import_id = ?, code_valeur_interne = ?, code_valeur_externe = ?
                        WHERE id = ?";
                $db->query($sql, array($import->id, $code_interne, $code_externe, $element_id));
            }
            else
            {
                $sql = "INSERT INTO imports_listes_valeurs (import_id, code_valeur_interne, code_valeur_externe) 
                        VALUES (?, ?, ?)";
                $db->query($sql, array($import->id, $code_interne, $code_externe));
            }
        }
        
        $db->commit();
        
        if(count($lignes_to_import))
        {
            $_SESSION['synchro_lignes_doublons'] = $lignes_to_import;
            $_SESSION['params_to_import'] = $params_to_import;
?>
            <script>
                alert('<?=dm_addslashes(txt('Voici un rapport des éléments qui n\'ont pu être importés car ils contiennent des données contradictoires.', false))?>');
                parent.loading_beforeunload = false;
                parent.window.location = 'index.php?section=admin&module=import_liste_for_import&export_doublons=1&id=<?=$import->id?>';
                parent.$.fancybox.close();
            </script>
<?
            exit;
        }
        else
        {
?>
            <script>
                parent.window.location.reload();
                parent.$.fancybox.close();
            </script>
<?
        }
        die();
    }
}
else if(isset($_POST['valider']))
{
    $_SESSION['synchro_data']['lignes_a_importer'] = array();
    $alias = array();
    $champs = array();

    if(!isset($_FILES['fichier']) || !is_uploaded_file($_FILES['fichier']['tmp_name']))
    {
        setErreur(txt('Vous devez sélectionner le fichier à importer.'));
    }
    else
    {
        $assocs_valeurs = array();
        $metas = $class_name::getChampsSaisieMetaData();

        foreach ($metas as $key => $meta)
        {
            if($meta['type'] == 'select' && !in_array($key, array('quantite_existante', 'quantite', 'quantite_fantome', 'conformite_du_besoin_electricite', 'conformite_du_besoin_plomberie', 'conformite_du_besoin_mecanique', 'conformite_du_besoin_telecom', 'conformite_du_besoin_architecture', 'conformite_du_besoin_structure', 'conformite_du_besoin', 'equipement_electricite', 'equipement_plomberie')))
            {
                if(!($class_name::CAN_HAVE_COORDINATION && $key == 'commentaires'))
                {
                    $all_alias[$meta['titre']] = html_entity_decode($tables['client'][$meta['table']]['nom']);
                    $all_cols[$meta['titre']]['nom_parametre'] = html_entity_decode($tables['client'][$meta['table']]['nom']);
                }
            }
        }

	    $spreadsheet = IOFactory::load($_FILES['fichier']['tmp_name']);

        $code_externes = array();
        foreach ($spreadsheet->getWorksheetIterator() as $iter_sheet => $worksheet)
        {
            if($iter_sheet > 0)
            {
                break;
            }
            $highestRow         = $worksheet->getHighestDataRow(); // e.g. 10
            $highestColumn      = $worksheet->getHighestDataColumn(); // e.g 'F'
            $highestColumnIndex = Coordinate::columnIndexFromString($highestColumn);
            $nrColumns = ord($highestColumn) - 64;

            $nb_actif = 0;
            $row_titre = 0;
            for ($row = 1; $row <= $highestRow; ++ $row) 
            {
                $nb = 0;
                for ($col = 1; $col <= $highestColumnIndex; ++ $col)
                {
                    $cell = $worksheet->getCellByColumnAndRow($col, $row);
                    $val = sanitizeString((string)$cell->getCalculatedValue());

                    if(isset($all_alias[$val]))
                    {
                        $nb++;
                    }
                }

                if($nb > $nb_actif)
                {
                    $row_titre = $row;
                    break;
                }
            }
 
            if($nb == 0)
            {
                $row_titre = 1;
            }
            for ($row = 1; $row <= $highestRow; ++ $row) 
            {                
                for ($col = 1; $col <= $highestColumnIndex; ++ $col)
                {
                    $cell = $worksheet->getCellByColumnAndRow($col, $row);
                    $val = sanitizeString((string)$cell->getCalculatedValue());

                    $val = dm_trim($val) == '.' ? '' : $val;
                    if($row == $row_titre && dm_strlen($val) > 0)
                    {
                        $alias[$col] = $val;
                        $champs[$val] = $val;
                    }
                    else if($row > $row_titre && isset($alias[$col]))
                    {
                        $_SESSION['synchro_data']['lignes_a_importer'][$row][$alias[$col]] = $val;
                    }
                }
            }
        }
        
        $spreadsheet->disconnectWorksheets();
        unset($spreadsheet);
        
        $_SESSION['synchro_data']['champs'] = $champs;
        
        $_SESSION['synchro_data']['all_cols'] = $all_cols;
        
        $_SESSION['synchro_data']['type_import'] = 'excel';
    }

    $etape = 'synchroniser';
}

$noHeader = true;
include('config/header.inc.php');
?>
<script>
var $document = $(document);
$document.ready(function(){
    
    $document.on('change', 'select.uniques', function(){
        
        var valeur = this.value;
        var id_select = this.id;
        
        $document.find('select.uniques').each(function(){
            
            if(this.value == valeur && this.id != id_select)
            {
                this.value = '';
            }
            
        });
        
    });
    
});    
</script>
<style>
    .blocsFiche, #fiche table, #fiche tr, #fiche td, #fiche th, .ui-dialog table, .ui-dialog tr, .ui-dialog td, .ui-dialog th {
        background: none!important;
    }
    .blocsFicheTitre {
        margin-top: 0px!important;
    }
    .readonly {
        text-align: center;
    }
</style>

<div id="popupContent">

    <form id="form" autocomplete="off" method="post" action="index.php?section=admin&module=import_liste_for_import&id=<?=intval($_GET['id'])?>" enctype="multipart/form-data">
        <label><?=txt('Mise-à-jour des données')?></label>
        <fieldset style="min-height: 175px; padding: 10px 0 0 50px;">
<?
            if(!isset($all_cols))
            {
?>           
                <div id="editables">
<?
                    erreurs();
?>
                    <section style="margin: 10px 0 10px 0;">
                        <span for="fichier"><?php print txt('Fichier MS Excel à importer'); ?></span>
                        <span>
                            <input type="file" id="fichier" name="fichier" style="border: solid 0px!important;" />
                        </span>
                    </section>
                    
                </div>
<?
            }
            else
            {
?>           
                <div id="editables">
<?
                messages();
                erreurs();
?>           
                <section style="margin: 10px 0 50px 0;">
                    <table style="width: 95%!important; margin: 10px 0 0 0;">
                        <tr><th>
                            <?=txt('Éléments importables')?>
                        </th><th>
                            <?=txt('Colonnes détectées dans le fichier importé')?>
                        </th></tr>
                        <tr style="background-color: #e2ffe2"><td style="border-right: solid 1px transparent!important;">
                            <?=txt('Code externe')?>
                        </td><td style="border-left: solid 1px transparent!important; position: relative;">
                            <div style="position: absolute; left: -29px; top: 10px; color: red; font-weight: bold;"><-- <?=txt('Pivot')?> --></div>
                            <select name="code_pivot_externe" class="uniques" style="max-width: 250px!important;">
                                <option value=""> </option>
<?
                                foreach($champs as $i => $alias)
                                {
                                    $sel = $alias == txt('Code externe') ? ' selected="selected"' : '';
?>
                                    <option value="<?=$alias?>"<?=$sel?>><?=$alias?></option>
<?
                                }
?>
                            </select>
                                <span style="width: 24px; height: 24px; display: inline-block;">&nbsp;</span>
                        </td></tr>
<?
                        foreach($all_cols as $parametre)
                        {
?>
                            <tr class="champs"><td>
                                    <span><?=$parametre['nom_parametre']?></span>
                            </td><td>
                                <select id="alias_<?=uniqueKey($parametre['nom_parametre'])?>" name="alias_<?=uniqueKey($parametre['nom_parametre'])?>" class="uniques colonnes" style="max-width: 250px!important;">
                                    <option value=""> </option>
<?
                                    foreach($champs as $i => $alias)
                                    {
                                        $sel = (isset($parametre['alias']) && $alias == $parametre['alias']) || $alias == $parametre['nom_parametre']  ? ' selected="selected"' : '';
?>
                                        <option value="<?=$alias?>"<?=$sel?>><?=$alias?></option>
<?
                                    }
?>
                                </select>
                                    <img src="css/images/icons/dark/cross_3.png" class="resetAssoc" />
                            </td></tr>
<?
                        }
?>
                    </table>
                </section>
                </div>
<?
            }
?>
        </fieldset>

        <div style="text-align: right; width: 98%!important;">
            <?
            if ($etape == 'synchroniser') {
                ?>
                <button class="assocAll all" style="color: green;"><?= txt('Tout associer') ?></button>
                <button class="resetAll all" style="color: red;"><?= txt('Tout dissocier') ?></button>
                <?
            }
            ?>
            <button class="submit" value="submitbuttonvalue" name="<?=$etape?>"><?=txt($etape)?></button>
            <button onclick="parent.$.fancybox.close();return false;" name="close"><?=txt('Annuler')?></button>
            
        </div>
    </form>
</div>    
<?
include('config/footer.inc.php');
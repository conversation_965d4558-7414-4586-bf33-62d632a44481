<html xmlns:v="urn:schemas-microsoft-com:vml"
xmlns:o="urn:schemas-microsoft-com:office:office"
xmlns:w="urn:schemas-microsoft-com:office:word"
xmlns:m="http://schemas.microsoft.com/office/2004/12/omml"
xmlns="http://www.w3.org/TR/REC-html40">

<head>
<meta http-equiv=Content-Type content="text/html; charset=windows-1252">
<meta name=ProgId content=Word.Document>
<meta name=Generator content="Microsoft Word 15">
<meta name=Originator content="Microsoft Word 15">
<link rel=File-List href="images/fr/filelist.xml">
<link rel=Edit-Time-Data href="images/fr/editdata.mso">
<!--[if !mso]>
<style>
v\:* {behavior:url(#default#VML);}
o\:* {behavior:url(#default#VML);}
w\:* {behavior:url(#default#VML);}
.shape {behavior:url(#default#VML);}
</style>
<![endif]-->
<link rel=themeData href="images/fr/themedata.thmx">
<link rel=colorSchemeMapping
href="images/fr/colorschememapping.xml">
<!--[if gte mso 9]><xml>
 <w:WordDocument>
  <w:SpellingState>Clean</w:SpellingState>
  <w:GrammarState>Clean</w:GrammarState>
  <w:TrackMoves>false</w:TrackMoves>
  <w:TrackFormatting/>
  <w:HyphenationZone>21</w:HyphenationZone>
  <w:ValidateAgainstSchemas/>
  <w:SaveIfXMLInvalid>false</w:SaveIfXMLInvalid>
  <w:IgnoreMixedContent>false</w:IgnoreMixedContent>
  <w:AlwaysShowPlaceholderText>false</w:AlwaysShowPlaceholderText>
  <w:DoNotPromoteQF/>
  <w:LidThemeOther>FR-CA</w:LidThemeOther>
  <w:LidThemeAsian>X-NONE</w:LidThemeAsian>
  <w:LidThemeComplexScript>X-NONE</w:LidThemeComplexScript>
  <w:Compatibility>
   <w:BreakWrappedTables/>
   <w:SplitPgBreakAndParaMark/>
  </w:Compatibility>
  <w:BrowserLevel>MicrosoftInternetExplorer4</w:BrowserLevel>
  <m:mathPr>
   <m:mathFont m:val="Cambria Math"/>
   <m:brkBin m:val="before"/>
   <m:brkBinSub m:val="&#45;-"/>
   <m:smallFrac m:val="off"/>
   <m:dispDef/>
   <m:lMargin m:val="0"/>
   <m:rMargin m:val="0"/>
   <m:defJc m:val="centerGroup"/>
   <m:wrapIndent m:val="1440"/>
   <m:intLim m:val="subSup"/>
   <m:naryLim m:val="undOvr"/>
  </m:mathPr></w:WordDocument>
</xml><![endif]--><!--[if gte mso 9]><xml>
 <w:LatentStyles DefLockedState="false" DefUnhideWhenUsed="false"
  DefSemiHidden="false" DefQFormat="false" DefPriority="99"
  LatentStyleCount="375">
  <w:LsdException Locked="false" Priority="0" QFormat="true" Name="Normal"/>
  <w:LsdException Locked="false" Priority="9" QFormat="true" Name="heading 1"/>
  <w:LsdException Locked="false" Priority="9" QFormat="true" Name="heading 2"/>
  <w:LsdException Locked="false" Priority="9" QFormat="true" Name="heading 3"/>
  <w:LsdException Locked="false" Priority="9" SemiHidden="true"
   UnhideWhenUsed="true" QFormat="true" Name="heading 4"/>
  <w:LsdException Locked="false" Priority="9" SemiHidden="true"
   UnhideWhenUsed="true" QFormat="true" Name="heading 5"/>
  <w:LsdException Locked="false" Priority="9" SemiHidden="true"
   UnhideWhenUsed="true" QFormat="true" Name="heading 6"/>
  <w:LsdException Locked="false" Priority="9" SemiHidden="true"
   UnhideWhenUsed="true" QFormat="true" Name="heading 7"/>
  <w:LsdException Locked="false" Priority="9" SemiHidden="true"
   UnhideWhenUsed="true" QFormat="true" Name="heading 8"/>
  <w:LsdException Locked="false" Priority="9" SemiHidden="true"
   UnhideWhenUsed="true" QFormat="true" Name="heading 9"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="index 1"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="index 2"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="index 3"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="index 4"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="index 5"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="index 6"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="index 7"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="index 8"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="index 9"/>
  <w:LsdException Locked="false" Priority="39" SemiHidden="true"
   UnhideWhenUsed="true" Name="toc 1"/>
  <w:LsdException Locked="false" Priority="39" SemiHidden="true"
   UnhideWhenUsed="true" Name="toc 2"/>
  <w:LsdException Locked="false" Priority="39" SemiHidden="true"
   UnhideWhenUsed="true" Name="toc 3"/>
  <w:LsdException Locked="false" Priority="39" SemiHidden="true"
   UnhideWhenUsed="true" Name="toc 4"/>
  <w:LsdException Locked="false" Priority="39" SemiHidden="true"
   UnhideWhenUsed="true" Name="toc 5"/>
  <w:LsdException Locked="false" Priority="39" SemiHidden="true"
   UnhideWhenUsed="true" Name="toc 6"/>
  <w:LsdException Locked="false" Priority="39" SemiHidden="true"
   UnhideWhenUsed="true" Name="toc 7"/>
  <w:LsdException Locked="false" Priority="39" SemiHidden="true"
   UnhideWhenUsed="true" Name="toc 8"/>
  <w:LsdException Locked="false" Priority="39" SemiHidden="true"
   UnhideWhenUsed="true" Name="toc 9"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Normal Indent"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="footnote text"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="annotation text"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="header"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="footer"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="index heading"/>
  <w:LsdException Locked="false" Priority="35" SemiHidden="true"
   UnhideWhenUsed="true" QFormat="true" Name="caption"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="table of figures"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="envelope address"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="envelope return"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="footnote reference"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="annotation reference"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="line number"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="page number"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="endnote reference"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="endnote text"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="table of authorities"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="macro"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="toa heading"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="List"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="List Bullet"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="List Number"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="List 2"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="List 3"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="List 4"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="List 5"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="List Bullet 2"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="List Bullet 3"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="List Bullet 4"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="List Bullet 5"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="List Number 2"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="List Number 3"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="List Number 4"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="List Number 5"/>
  <w:LsdException Locked="false" Priority="10" QFormat="true" Name="Title"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Closing"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Signature"/>
  <w:LsdException Locked="false" Priority="1" SemiHidden="true"
   UnhideWhenUsed="true" Name="Default Paragraph Font"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Body Text"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Body Text Indent"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="List Continue"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="List Continue 2"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="List Continue 3"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="List Continue 4"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="List Continue 5"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Message Header"/>
  <w:LsdException Locked="false" Priority="11" QFormat="true" Name="Subtitle"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Salutation"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Date"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Body Text First Indent"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Body Text First Indent 2"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Note Heading"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Body Text 2"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Body Text 3"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Body Text Indent 2"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Body Text Indent 3"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Block Text"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Hyperlink"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="FollowedHyperlink"/>
  <w:LsdException Locked="false" Priority="22" QFormat="true" Name="Strong"/>
  <w:LsdException Locked="false" Priority="20" QFormat="true" Name="Emphasis"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Document Map"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Plain Text"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="E-mail Signature"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="HTML Top of Form"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="HTML Bottom of Form"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Normal (Web)"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="HTML Acronym"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="HTML Address"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="HTML Cite"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="HTML Code"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="HTML Definition"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="HTML Keyboard"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="HTML Preformatted"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="HTML Sample"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="HTML Typewriter"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="HTML Variable"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Normal Table"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="annotation subject"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="No List"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Outline List 1"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Outline List 2"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Outline List 3"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Simple 1"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Simple 2"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Simple 3"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Classic 1"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Classic 2"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Classic 3"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Classic 4"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Colorful 1"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Colorful 2"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Colorful 3"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Columns 1"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Columns 2"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Columns 3"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Columns 4"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Columns 5"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Grid 1"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Grid 2"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Grid 3"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Grid 4"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Grid 5"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Grid 6"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Grid 7"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Grid 8"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table List 1"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table List 2"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table List 3"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table List 4"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table List 5"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table List 6"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table List 7"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table List 8"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table 3D effects 1"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table 3D effects 2"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table 3D effects 3"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Contemporary"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Elegant"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Professional"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Subtle 1"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Subtle 2"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Web 1"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Web 2"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Web 3"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Balloon Text"/>
  <w:LsdException Locked="false" Priority="39" Name="Table Grid"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Table Theme"/>
  <w:LsdException Locked="false" SemiHidden="true" Name="Placeholder Text"/>
  <w:LsdException Locked="false" Priority="1" QFormat="true" Name="No Spacing"/>
  <w:LsdException Locked="false" Priority="60" Name="Light Shading"/>
  <w:LsdException Locked="false" Priority="61" Name="Light List"/>
  <w:LsdException Locked="false" Priority="62" Name="Light Grid"/>
  <w:LsdException Locked="false" Priority="63" Name="Medium Shading 1"/>
  <w:LsdException Locked="false" Priority="64" Name="Medium Shading 2"/>
  <w:LsdException Locked="false" Priority="65" Name="Medium List 1"/>
  <w:LsdException Locked="false" Priority="66" Name="Medium List 2"/>
  <w:LsdException Locked="false" Priority="67" Name="Medium Grid 1"/>
  <w:LsdException Locked="false" Priority="68" Name="Medium Grid 2"/>
  <w:LsdException Locked="false" Priority="69" Name="Medium Grid 3"/>
  <w:LsdException Locked="false" Priority="70" Name="Dark List"/>
  <w:LsdException Locked="false" Priority="71" Name="Colorful Shading"/>
  <w:LsdException Locked="false" Priority="72" Name="Colorful List"/>
  <w:LsdException Locked="false" Priority="73" Name="Colorful Grid"/>
  <w:LsdException Locked="false" Priority="60" Name="Light Shading Accent 1"/>
  <w:LsdException Locked="false" Priority="61" Name="Light List Accent 1"/>
  <w:LsdException Locked="false" Priority="62" Name="Light Grid Accent 1"/>
  <w:LsdException Locked="false" Priority="63" Name="Medium Shading 1 Accent 1"/>
  <w:LsdException Locked="false" Priority="64" Name="Medium Shading 2 Accent 1"/>
  <w:LsdException Locked="false" Priority="65" Name="Medium List 1 Accent 1"/>
  <w:LsdException Locked="false" SemiHidden="true" Name="Revision"/>
  <w:LsdException Locked="false" Priority="34" QFormat="true"
   Name="List Paragraph"/>
  <w:LsdException Locked="false" Priority="29" QFormat="true" Name="Quote"/>
  <w:LsdException Locked="false" Priority="30" QFormat="true"
   Name="Intense Quote"/>
  <w:LsdException Locked="false" Priority="66" Name="Medium List 2 Accent 1"/>
  <w:LsdException Locked="false" Priority="67" Name="Medium Grid 1 Accent 1"/>
  <w:LsdException Locked="false" Priority="68" Name="Medium Grid 2 Accent 1"/>
  <w:LsdException Locked="false" Priority="69" Name="Medium Grid 3 Accent 1"/>
  <w:LsdException Locked="false" Priority="70" Name="Dark List Accent 1"/>
  <w:LsdException Locked="false" Priority="71" Name="Colorful Shading Accent 1"/>
  <w:LsdException Locked="false" Priority="72" Name="Colorful List Accent 1"/>
  <w:LsdException Locked="false" Priority="73" Name="Colorful Grid Accent 1"/>
  <w:LsdException Locked="false" Priority="60" Name="Light Shading Accent 2"/>
  <w:LsdException Locked="false" Priority="61" Name="Light List Accent 2"/>
  <w:LsdException Locked="false" Priority="62" Name="Light Grid Accent 2"/>
  <w:LsdException Locked="false" Priority="63" Name="Medium Shading 1 Accent 2"/>
  <w:LsdException Locked="false" Priority="64" Name="Medium Shading 2 Accent 2"/>
  <w:LsdException Locked="false" Priority="65" Name="Medium List 1 Accent 2"/>
  <w:LsdException Locked="false" Priority="66" Name="Medium List 2 Accent 2"/>
  <w:LsdException Locked="false" Priority="67" Name="Medium Grid 1 Accent 2"/>
  <w:LsdException Locked="false" Priority="68" Name="Medium Grid 2 Accent 2"/>
  <w:LsdException Locked="false" Priority="69" Name="Medium Grid 3 Accent 2"/>
  <w:LsdException Locked="false" Priority="70" Name="Dark List Accent 2"/>
  <w:LsdException Locked="false" Priority="71" Name="Colorful Shading Accent 2"/>
  <w:LsdException Locked="false" Priority="72" Name="Colorful List Accent 2"/>
  <w:LsdException Locked="false" Priority="73" Name="Colorful Grid Accent 2"/>
  <w:LsdException Locked="false" Priority="60" Name="Light Shading Accent 3"/>
  <w:LsdException Locked="false" Priority="61" Name="Light List Accent 3"/>
  <w:LsdException Locked="false" Priority="62" Name="Light Grid Accent 3"/>
  <w:LsdException Locked="false" Priority="63" Name="Medium Shading 1 Accent 3"/>
  <w:LsdException Locked="false" Priority="64" Name="Medium Shading 2 Accent 3"/>
  <w:LsdException Locked="false" Priority="65" Name="Medium List 1 Accent 3"/>
  <w:LsdException Locked="false" Priority="66" Name="Medium List 2 Accent 3"/>
  <w:LsdException Locked="false" Priority="67" Name="Medium Grid 1 Accent 3"/>
  <w:LsdException Locked="false" Priority="68" Name="Medium Grid 2 Accent 3"/>
  <w:LsdException Locked="false" Priority="69" Name="Medium Grid 3 Accent 3"/>
  <w:LsdException Locked="false" Priority="70" Name="Dark List Accent 3"/>
  <w:LsdException Locked="false" Priority="71" Name="Colorful Shading Accent 3"/>
  <w:LsdException Locked="false" Priority="72" Name="Colorful List Accent 3"/>
  <w:LsdException Locked="false" Priority="73" Name="Colorful Grid Accent 3"/>
  <w:LsdException Locked="false" Priority="60" Name="Light Shading Accent 4"/>
  <w:LsdException Locked="false" Priority="61" Name="Light List Accent 4"/>
  <w:LsdException Locked="false" Priority="62" Name="Light Grid Accent 4"/>
  <w:LsdException Locked="false" Priority="63" Name="Medium Shading 1 Accent 4"/>
  <w:LsdException Locked="false" Priority="64" Name="Medium Shading 2 Accent 4"/>
  <w:LsdException Locked="false" Priority="65" Name="Medium List 1 Accent 4"/>
  <w:LsdException Locked="false" Priority="66" Name="Medium List 2 Accent 4"/>
  <w:LsdException Locked="false" Priority="67" Name="Medium Grid 1 Accent 4"/>
  <w:LsdException Locked="false" Priority="68" Name="Medium Grid 2 Accent 4"/>
  <w:LsdException Locked="false" Priority="69" Name="Medium Grid 3 Accent 4"/>
  <w:LsdException Locked="false" Priority="70" Name="Dark List Accent 4"/>
  <w:LsdException Locked="false" Priority="71" Name="Colorful Shading Accent 4"/>
  <w:LsdException Locked="false" Priority="72" Name="Colorful List Accent 4"/>
  <w:LsdException Locked="false" Priority="73" Name="Colorful Grid Accent 4"/>
  <w:LsdException Locked="false" Priority="60" Name="Light Shading Accent 5"/>
  <w:LsdException Locked="false" Priority="61" Name="Light List Accent 5"/>
  <w:LsdException Locked="false" Priority="62" Name="Light Grid Accent 5"/>
  <w:LsdException Locked="false" Priority="63" Name="Medium Shading 1 Accent 5"/>
  <w:LsdException Locked="false" Priority="64" Name="Medium Shading 2 Accent 5"/>
  <w:LsdException Locked="false" Priority="65" Name="Medium List 1 Accent 5"/>
  <w:LsdException Locked="false" Priority="66" Name="Medium List 2 Accent 5"/>
  <w:LsdException Locked="false" Priority="67" Name="Medium Grid 1 Accent 5"/>
  <w:LsdException Locked="false" Priority="68" Name="Medium Grid 2 Accent 5"/>
  <w:LsdException Locked="false" Priority="69" Name="Medium Grid 3 Accent 5"/>
  <w:LsdException Locked="false" Priority="70" Name="Dark List Accent 5"/>
  <w:LsdException Locked="false" Priority="71" Name="Colorful Shading Accent 5"/>
  <w:LsdException Locked="false" Priority="72" Name="Colorful List Accent 5"/>
  <w:LsdException Locked="false" Priority="73" Name="Colorful Grid Accent 5"/>
  <w:LsdException Locked="false" Priority="60" Name="Light Shading Accent 6"/>
  <w:LsdException Locked="false" Priority="61" Name="Light List Accent 6"/>
  <w:LsdException Locked="false" Priority="62" Name="Light Grid Accent 6"/>
  <w:LsdException Locked="false" Priority="63" Name="Medium Shading 1 Accent 6"/>
  <w:LsdException Locked="false" Priority="64" Name="Medium Shading 2 Accent 6"/>
  <w:LsdException Locked="false" Priority="65" Name="Medium List 1 Accent 6"/>
  <w:LsdException Locked="false" Priority="66" Name="Medium List 2 Accent 6"/>
  <w:LsdException Locked="false" Priority="67" Name="Medium Grid 1 Accent 6"/>
  <w:LsdException Locked="false" Priority="68" Name="Medium Grid 2 Accent 6"/>
  <w:LsdException Locked="false" Priority="69" Name="Medium Grid 3 Accent 6"/>
  <w:LsdException Locked="false" Priority="70" Name="Dark List Accent 6"/>
  <w:LsdException Locked="false" Priority="71" Name="Colorful Shading Accent 6"/>
  <w:LsdException Locked="false" Priority="72" Name="Colorful List Accent 6"/>
  <w:LsdException Locked="false" Priority="73" Name="Colorful Grid Accent 6"/>
  <w:LsdException Locked="false" Priority="19" QFormat="true"
   Name="Subtle Emphasis"/>
  <w:LsdException Locked="false" Priority="21" QFormat="true"
   Name="Intense Emphasis"/>
  <w:LsdException Locked="false" Priority="31" QFormat="true"
   Name="Subtle Reference"/>
  <w:LsdException Locked="false" Priority="32" QFormat="true"
   Name="Intense Reference"/>
  <w:LsdException Locked="false" Priority="33" QFormat="true" Name="Book Title"/>
  <w:LsdException Locked="false" Priority="37" SemiHidden="true"
   UnhideWhenUsed="true" Name="Bibliography"/>
  <w:LsdException Locked="false" Priority="39" QFormat="true" Name="TOC Heading"/>
  <w:LsdException Locked="false" Priority="41" Name="Plain Table 1"/>
  <w:LsdException Locked="false" Priority="42" Name="Plain Table 2"/>
  <w:LsdException Locked="false" Priority="43" Name="Plain Table 3"/>
  <w:LsdException Locked="false" Priority="44" Name="Plain Table 4"/>
  <w:LsdException Locked="false" Priority="45" Name="Plain Table 5"/>
  <w:LsdException Locked="false" Priority="40" Name="Grid Table Light"/>
  <w:LsdException Locked="false" Priority="46" Name="Grid Table 1 Light"/>
  <w:LsdException Locked="false" Priority="47" Name="Grid Table 2"/>
  <w:LsdException Locked="false" Priority="48" Name="Grid Table 3"/>
  <w:LsdException Locked="false" Priority="49" Name="Grid Table 4"/>
  <w:LsdException Locked="false" Priority="50" Name="Grid Table 5 Dark"/>
  <w:LsdException Locked="false" Priority="51" Name="Grid Table 6 Colorful"/>
  <w:LsdException Locked="false" Priority="52" Name="Grid Table 7 Colorful"/>
  <w:LsdException Locked="false" Priority="46"
   Name="Grid Table 1 Light Accent 1"/>
  <w:LsdException Locked="false" Priority="47" Name="Grid Table 2 Accent 1"/>
  <w:LsdException Locked="false" Priority="48" Name="Grid Table 3 Accent 1"/>
  <w:LsdException Locked="false" Priority="49" Name="Grid Table 4 Accent 1"/>
  <w:LsdException Locked="false" Priority="50" Name="Grid Table 5 Dark Accent 1"/>
  <w:LsdException Locked="false" Priority="51"
   Name="Grid Table 6 Colorful Accent 1"/>
  <w:LsdException Locked="false" Priority="52"
   Name="Grid Table 7 Colorful Accent 1"/>
  <w:LsdException Locked="false" Priority="46"
   Name="Grid Table 1 Light Accent 2"/>
  <w:LsdException Locked="false" Priority="47" Name="Grid Table 2 Accent 2"/>
  <w:LsdException Locked="false" Priority="48" Name="Grid Table 3 Accent 2"/>
  <w:LsdException Locked="false" Priority="49" Name="Grid Table 4 Accent 2"/>
  <w:LsdException Locked="false" Priority="50" Name="Grid Table 5 Dark Accent 2"/>
  <w:LsdException Locked="false" Priority="51"
   Name="Grid Table 6 Colorful Accent 2"/>
  <w:LsdException Locked="false" Priority="52"
   Name="Grid Table 7 Colorful Accent 2"/>
  <w:LsdException Locked="false" Priority="46"
   Name="Grid Table 1 Light Accent 3"/>
  <w:LsdException Locked="false" Priority="47" Name="Grid Table 2 Accent 3"/>
  <w:LsdException Locked="false" Priority="48" Name="Grid Table 3 Accent 3"/>
  <w:LsdException Locked="false" Priority="49" Name="Grid Table 4 Accent 3"/>
  <w:LsdException Locked="false" Priority="50" Name="Grid Table 5 Dark Accent 3"/>
  <w:LsdException Locked="false" Priority="51"
   Name="Grid Table 6 Colorful Accent 3"/>
  <w:LsdException Locked="false" Priority="52"
   Name="Grid Table 7 Colorful Accent 3"/>
  <w:LsdException Locked="false" Priority="46"
   Name="Grid Table 1 Light Accent 4"/>
  <w:LsdException Locked="false" Priority="47" Name="Grid Table 2 Accent 4"/>
  <w:LsdException Locked="false" Priority="48" Name="Grid Table 3 Accent 4"/>
  <w:LsdException Locked="false" Priority="49" Name="Grid Table 4 Accent 4"/>
  <w:LsdException Locked="false" Priority="50" Name="Grid Table 5 Dark Accent 4"/>
  <w:LsdException Locked="false" Priority="51"
   Name="Grid Table 6 Colorful Accent 4"/>
  <w:LsdException Locked="false" Priority="52"
   Name="Grid Table 7 Colorful Accent 4"/>
  <w:LsdException Locked="false" Priority="46"
   Name="Grid Table 1 Light Accent 5"/>
  <w:LsdException Locked="false" Priority="47" Name="Grid Table 2 Accent 5"/>
  <w:LsdException Locked="false" Priority="48" Name="Grid Table 3 Accent 5"/>
  <w:LsdException Locked="false" Priority="49" Name="Grid Table 4 Accent 5"/>
  <w:LsdException Locked="false" Priority="50" Name="Grid Table 5 Dark Accent 5"/>
  <w:LsdException Locked="false" Priority="51"
   Name="Grid Table 6 Colorful Accent 5"/>
  <w:LsdException Locked="false" Priority="52"
   Name="Grid Table 7 Colorful Accent 5"/>
  <w:LsdException Locked="false" Priority="46"
   Name="Grid Table 1 Light Accent 6"/>
  <w:LsdException Locked="false" Priority="47" Name="Grid Table 2 Accent 6"/>
  <w:LsdException Locked="false" Priority="48" Name="Grid Table 3 Accent 6"/>
  <w:LsdException Locked="false" Priority="49" Name="Grid Table 4 Accent 6"/>
  <w:LsdException Locked="false" Priority="50" Name="Grid Table 5 Dark Accent 6"/>
  <w:LsdException Locked="false" Priority="51"
   Name="Grid Table 6 Colorful Accent 6"/>
  <w:LsdException Locked="false" Priority="52"
   Name="Grid Table 7 Colorful Accent 6"/>
  <w:LsdException Locked="false" Priority="46" Name="List Table 1 Light"/>
  <w:LsdException Locked="false" Priority="47" Name="List Table 2"/>
  <w:LsdException Locked="false" Priority="48" Name="List Table 3"/>
  <w:LsdException Locked="false" Priority="49" Name="List Table 4"/>
  <w:LsdException Locked="false" Priority="50" Name="List Table 5 Dark"/>
  <w:LsdException Locked="false" Priority="51" Name="List Table 6 Colorful"/>
  <w:LsdException Locked="false" Priority="52" Name="List Table 7 Colorful"/>
  <w:LsdException Locked="false" Priority="46"
   Name="List Table 1 Light Accent 1"/>
  <w:LsdException Locked="false" Priority="47" Name="List Table 2 Accent 1"/>
  <w:LsdException Locked="false" Priority="48" Name="List Table 3 Accent 1"/>
  <w:LsdException Locked="false" Priority="49" Name="List Table 4 Accent 1"/>
  <w:LsdException Locked="false" Priority="50" Name="List Table 5 Dark Accent 1"/>
  <w:LsdException Locked="false" Priority="51"
   Name="List Table 6 Colorful Accent 1"/>
  <w:LsdException Locked="false" Priority="52"
   Name="List Table 7 Colorful Accent 1"/>
  <w:LsdException Locked="false" Priority="46"
   Name="List Table 1 Light Accent 2"/>
  <w:LsdException Locked="false" Priority="47" Name="List Table 2 Accent 2"/>
  <w:LsdException Locked="false" Priority="48" Name="List Table 3 Accent 2"/>
  <w:LsdException Locked="false" Priority="49" Name="List Table 4 Accent 2"/>
  <w:LsdException Locked="false" Priority="50" Name="List Table 5 Dark Accent 2"/>
  <w:LsdException Locked="false" Priority="51"
   Name="List Table 6 Colorful Accent 2"/>
  <w:LsdException Locked="false" Priority="52"
   Name="List Table 7 Colorful Accent 2"/>
  <w:LsdException Locked="false" Priority="46"
   Name="List Table 1 Light Accent 3"/>
  <w:LsdException Locked="false" Priority="47" Name="List Table 2 Accent 3"/>
  <w:LsdException Locked="false" Priority="48" Name="List Table 3 Accent 3"/>
  <w:LsdException Locked="false" Priority="49" Name="List Table 4 Accent 3"/>
  <w:LsdException Locked="false" Priority="50" Name="List Table 5 Dark Accent 3"/>
  <w:LsdException Locked="false" Priority="51"
   Name="List Table 6 Colorful Accent 3"/>
  <w:LsdException Locked="false" Priority="52"
   Name="List Table 7 Colorful Accent 3"/>
  <w:LsdException Locked="false" Priority="46"
   Name="List Table 1 Light Accent 4"/>
  <w:LsdException Locked="false" Priority="47" Name="List Table 2 Accent 4"/>
  <w:LsdException Locked="false" Priority="48" Name="List Table 3 Accent 4"/>
  <w:LsdException Locked="false" Priority="49" Name="List Table 4 Accent 4"/>
  <w:LsdException Locked="false" Priority="50" Name="List Table 5 Dark Accent 4"/>
  <w:LsdException Locked="false" Priority="51"
   Name="List Table 6 Colorful Accent 4"/>
  <w:LsdException Locked="false" Priority="52"
   Name="List Table 7 Colorful Accent 4"/>
  <w:LsdException Locked="false" Priority="46"
   Name="List Table 1 Light Accent 5"/>
  <w:LsdException Locked="false" Priority="47" Name="List Table 2 Accent 5"/>
  <w:LsdException Locked="false" Priority="48" Name="List Table 3 Accent 5"/>
  <w:LsdException Locked="false" Priority="49" Name="List Table 4 Accent 5"/>
  <w:LsdException Locked="false" Priority="50" Name="List Table 5 Dark Accent 5"/>
  <w:LsdException Locked="false" Priority="51"
   Name="List Table 6 Colorful Accent 5"/>
  <w:LsdException Locked="false" Priority="52"
   Name="List Table 7 Colorful Accent 5"/>
  <w:LsdException Locked="false" Priority="46"
   Name="List Table 1 Light Accent 6"/>
  <w:LsdException Locked="false" Priority="47" Name="List Table 2 Accent 6"/>
  <w:LsdException Locked="false" Priority="48" Name="List Table 3 Accent 6"/>
  <w:LsdException Locked="false" Priority="49" Name="List Table 4 Accent 6"/>
  <w:LsdException Locked="false" Priority="50" Name="List Table 5 Dark Accent 6"/>
  <w:LsdException Locked="false" Priority="51"
   Name="List Table 6 Colorful Accent 6"/>
  <w:LsdException Locked="false" Priority="52"
   Name="List Table 7 Colorful Accent 6"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Mention"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Smart Hyperlink"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Hashtag"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   Name="Unresolved Mention"/>
 </w:LatentStyles>
</xml><![endif]-->
<style>
<!--
 /* Font Definitions */
 @font-face
	{font-family:"Cambria Math";
	panose-1:2 4 5 3 5 4 6 3 2 4;
	mso-font-charset:0;
	mso-generic-font-family:roman;
	mso-font-pitch:variable;
	mso-font-signature:3 0 0 0 1 0;}
@font-face
	{font-family:Calibri;
	panose-1:2 15 5 2 2 2 4 3 2 4;
	mso-font-charset:0;
	mso-generic-font-family:swiss;
	mso-font-pitch:variable;
	mso-font-signature:-536859905 -1073732485 9 0 511 0;}
@font-face
	{font-family:Cambria;
	panose-1:2 4 5 3 5 4 6 3 2 4;
	mso-font-charset:0;
	mso-generic-font-family:roman;
	mso-font-pitch:variable;
	mso-font-signature:-536869121 1107305727 33554432 0 415 0;}
@font-face
	{font-family:Tahoma;
	panose-1:2 11 6 4 3 5 4 4 2 4;
	mso-font-charset:0;
	mso-generic-font-family:swiss;
	mso-font-pitch:variable;
	mso-font-signature:-520081665 -1073717157 41 0 66047 0;}
 /* Style Definitions */
 p.MsoNormal, li.MsoNormal, div.MsoNormal
	{mso-style-unhide:no;
	mso-style-qformat:yes;
	mso-style-parent:"";
	margin-top:0cm;
	margin-right:0cm;
	margin-bottom:10.0pt;
	margin-left:0cm;
	line-height:115%;
	mso-pagination:widow-orphan;
	font-size:11.0pt;
	font-family:"Calibri",sans-serif;
	mso-fareast-font-family:"Times New Roman";
	mso-fareast-theme-font:minor-fareast;}
h1
	{mso-style-priority:9;
	mso-style-unhide:no;
	mso-style-qformat:yes;
	mso-style-link:"Titre 1 Car";
	margin-top:24.0pt;
	margin-right:0cm;
	margin-bottom:0cm;
	margin-left:0cm;
	margin-bottom:.0001pt;
	line-height:115%;
	mso-pagination:widow-orphan;
	page-break-after:avoid;
	mso-outline-level:1;
	font-size:14.0pt;
	font-family:"Cambria",serif;
	mso-fareast-font-family:"Times New Roman";
	mso-fareast-theme-font:minor-fareast;
	color:#365F91;}
h2
	{mso-style-priority:9;
	mso-style-unhide:no;
	mso-style-qformat:yes;
	mso-style-link:"Titre 2 Car";
	margin-top:10.0pt;
	margin-right:0cm;
	margin-bottom:0cm;
	margin-left:0cm;
	margin-bottom:.0001pt;
	line-height:115%;
	mso-pagination:widow-orphan;
	page-break-after:avoid;
	mso-outline-level:2;
	font-size:13.0pt;
	font-family:"Cambria",serif;
	mso-fareast-font-family:"Times New Roman";
	mso-fareast-theme-font:minor-fareast;
	color:#4F81BD;}
h3
	{mso-style-priority:9;
	mso-style-unhide:no;
	mso-style-qformat:yes;
	mso-style-link:"Titre 3 Car";
	margin-top:10.0pt;
	margin-right:0cm;
	margin-bottom:0cm;
	margin-left:0cm;
	margin-bottom:.0001pt;
	line-height:115%;
	mso-pagination:widow-orphan;
	page-break-after:avoid;
	mso-outline-level:3;
	font-size:11.0pt;
	font-family:"Cambria",serif;
	mso-fareast-font-family:"Times New Roman";
	mso-fareast-theme-font:minor-fareast;
	color:#4F81BD;}
p.MsoToc1, li.MsoToc1, div.MsoToc1
	{mso-style-update:auto;
	mso-style-noshow:yes;
	mso-style-priority:39;
	margin-top:0cm;
	margin-right:0cm;
	margin-bottom:5.0pt;
	margin-left:18.0pt;
	text-align:justify;
	text-indent:-18.0pt;
	line-height:115%;
	mso-pagination:widow-orphan;
	font-size:11.0pt;
	font-family:"Calibri",sans-serif;
	mso-fareast-font-family:"Times New Roman";
	mso-fareast-theme-font:minor-fareast;
	text-decoration:underline;
	text-underline:single;}
p.MsoToc2, li.MsoToc2, div.MsoToc2
	{mso-style-update:auto;
	mso-style-noshow:yes;
	mso-style-priority:39;
	margin-top:0cm;
	margin-right:0cm;
	margin-bottom:5.0pt;
	margin-left:11.0pt;
	line-height:115%;
	mso-pagination:widow-orphan;
	font-size:11.0pt;
	font-family:"Calibri",sans-serif;
	mso-fareast-font-family:"Times New Roman";
	mso-fareast-theme-font:minor-fareast;}
p.MsoToc3, li.MsoToc3, div.MsoToc3
	{mso-style-update:auto;
	mso-style-noshow:yes;
	mso-style-priority:39;
	margin-top:0cm;
	margin-right:0cm;
	margin-bottom:5.0pt;
	margin-left:22.0pt;
	line-height:115%;
	mso-pagination:widow-orphan;
	font-size:11.0pt;
	font-family:"Calibri",sans-serif;
	mso-fareast-font-family:"Times New Roman";
	mso-fareast-theme-font:minor-fareast;}
p.MsoHeader, li.MsoHeader, div.MsoHeader
	{mso-style-noshow:yes;
	mso-style-priority:99;
	mso-style-link:"En-tête Car";
	margin:0cm;
	margin-bottom:.0001pt;
	mso-pagination:widow-orphan;
	font-size:11.0pt;
	font-family:"Calibri",sans-serif;
	mso-fareast-font-family:"Times New Roman";
	mso-fareast-theme-font:minor-fareast;}
p.MsoFooter, li.MsoFooter, div.MsoFooter
	{mso-style-noshow:yes;
	mso-style-priority:99;
	mso-style-link:"Pied de page Car";
	margin:0cm;
	margin-bottom:.0001pt;
	mso-pagination:widow-orphan;
	font-size:11.0pt;
	font-family:"Calibri",sans-serif;
	mso-fareast-font-family:"Times New Roman";
	mso-fareast-theme-font:minor-fareast;}
a:link, span.MsoHyperlink
	{mso-style-noshow:yes;
	mso-style-priority:99;
	color:blue;
	text-decoration:underline;
	text-underline:single;}
a:visited, span.MsoHyperlinkFollowed
	{mso-style-noshow:yes;
	mso-style-priority:99;
	color:purple;
	text-decoration:underline;
	text-underline:single;}
p.MsoAcetate, li.MsoAcetate, div.MsoAcetate
	{mso-style-noshow:yes;
	mso-style-priority:99;
	mso-style-link:"Texte de bulles Car";
	margin:0cm;
	margin-bottom:.0001pt;
	mso-pagination:widow-orphan;
	font-size:8.0pt;
	font-family:"Tahoma",sans-serif;
	mso-fareast-font-family:"Times New Roman";
	mso-fareast-theme-font:minor-fareast;}
p.MsoListParagraph, li.MsoListParagraph, div.MsoListParagraph
	{mso-style-priority:34;
	mso-style-unhide:no;
	mso-style-qformat:yes;
	margin-top:0cm;
	margin-right:0cm;
	margin-bottom:10.0pt;
	margin-left:36.0pt;
	line-height:115%;
	mso-pagination:widow-orphan;
	font-size:11.0pt;
	font-family:"Calibri",sans-serif;
	mso-fareast-font-family:"Times New Roman";
	mso-fareast-theme-font:minor-fareast;}
p.MsoTocHeading, li.MsoTocHeading, div.MsoTocHeading
	{mso-style-priority:39;
	mso-style-unhide:no;
	mso-style-qformat:yes;
	margin-top:24.0pt;
	margin-right:0cm;
	margin-bottom:0cm;
	margin-left:0cm;
	margin-bottom:.0001pt;
	line-height:115%;
	mso-pagination:widow-orphan;
	page-break-after:avoid;
	font-size:14.0pt;
	font-family:"Cambria",serif;
	mso-fareast-font-family:"Times New Roman";
	mso-fareast-theme-font:minor-fareast;
	mso-bidi-font-family:"Times New Roman";
	color:#365F91;
	font-weight:bold;}
span.Titre1Car
	{mso-style-name:"Titre 1 Car";
	mso-style-priority:9;
	mso-style-unhide:no;
	mso-style-locked:yes;
	mso-style-link:"Titre 1";
	font-family:"Cambria",serif;
	mso-ascii-font-family:Cambria;
	mso-hansi-font-family:Cambria;
	color:#365F91;
	font-weight:bold;}
span.Titre2Car
	{mso-style-name:"Titre 2 Car";
	mso-style-noshow:yes;
	mso-style-priority:9;
	mso-style-unhide:no;
	mso-style-locked:yes;
	mso-style-link:"Titre 2";
	font-family:"Cambria",serif;
	mso-ascii-font-family:Cambria;
	mso-hansi-font-family:Cambria;
	color:#4F81BD;
	font-weight:bold;}
span.Titre3Car
	{mso-style-name:"Titre 3 Car";
	mso-style-noshow:yes;
	mso-style-priority:9;
	mso-style-unhide:no;
	mso-style-locked:yes;
	mso-style-link:"Titre 3";
	font-family:"Cambria",serif;
	mso-ascii-font-family:Cambria;
	mso-hansi-font-family:Cambria;
	color:#4F81BD;
	font-weight:bold;}
p.msonormal0, li.msonormal0, div.msonormal0
	{mso-style-name:msonormal;
	mso-style-unhide:no;
	mso-margin-top-alt:auto;
	margin-right:0cm;
	mso-margin-bottom-alt:auto;
	margin-left:0cm;
	mso-pagination:widow-orphan;
	font-size:12.0pt;
	font-family:"Times New Roman",serif;
	mso-fareast-font-family:"Times New Roman";
	mso-fareast-theme-font:minor-fareast;}
span.En-tteCar
	{mso-style-name:"En-tête Car";
	mso-style-noshow:yes;
	mso-style-priority:99;
	mso-style-unhide:no;
	mso-style-locked:yes;
	mso-style-link:En-tête;}
span.PieddepageCar
	{mso-style-name:"Pied de page Car";
	mso-style-noshow:yes;
	mso-style-priority:99;
	mso-style-unhide:no;
	mso-style-locked:yes;
	mso-style-link:"Pied de page";}
span.TextedebullesCar
	{mso-style-name:"Texte de bulles Car";
	mso-style-noshow:yes;
	mso-style-priority:99;
	mso-style-unhide:no;
	mso-style-locked:yes;
	mso-style-link:"Texte de bulles";
	font-family:"Tahoma",sans-serif;
	mso-ascii-font-family:Tahoma;
	mso-hansi-font-family:Tahoma;
	mso-bidi-font-family:Tahoma;}
p.msolistparagraphcxspfirst, li.msolistparagraphcxspfirst, div.msolistparagraphcxspfirst
	{mso-style-name:msolistparagraphcxspfirst;
	mso-style-unhide:no;
	margin-top:0cm;
	margin-right:0cm;
	margin-bottom:0cm;
	margin-left:36.0pt;
	margin-bottom:.0001pt;
	line-height:115%;
	mso-pagination:widow-orphan;
	font-size:11.0pt;
	font-family:"Calibri",sans-serif;
	mso-fareast-font-family:"Times New Roman";
	mso-fareast-theme-font:minor-fareast;}
p.msolistparagraphcxspmiddle, li.msolistparagraphcxspmiddle, div.msolistparagraphcxspmiddle
	{mso-style-name:msolistparagraphcxspmiddle;
	mso-style-unhide:no;
	margin-top:0cm;
	margin-right:0cm;
	margin-bottom:0cm;
	margin-left:36.0pt;
	margin-bottom:.0001pt;
	line-height:115%;
	mso-pagination:widow-orphan;
	font-size:11.0pt;
	font-family:"Calibri",sans-serif;
	mso-fareast-font-family:"Times New Roman";
	mso-fareast-theme-font:minor-fareast;}
p.msolistparagraphcxsplast, li.msolistparagraphcxsplast, div.msolistparagraphcxsplast
	{mso-style-name:msolistparagraphcxsplast;
	mso-style-unhide:no;
	margin-top:0cm;
	margin-right:0cm;
	margin-bottom:10.0pt;
	margin-left:36.0pt;
	line-height:115%;
	mso-pagination:widow-orphan;
	font-size:11.0pt;
	font-family:"Calibri",sans-serif;
	mso-fareast-font-family:"Times New Roman";
	mso-fareast-theme-font:minor-fareast;}
p.msopapdefault, li.msopapdefault, div.msopapdefault
	{mso-style-name:msopapdefault;
	mso-style-unhide:no;
	mso-margin-top-alt:auto;
	margin-right:0cm;
	margin-bottom:10.0pt;
	margin-left:0cm;
	line-height:115%;
	mso-pagination:widow-orphan;
	font-size:12.0pt;
	font-family:"Times New Roman",serif;
	mso-fareast-font-family:"Times New Roman";
	mso-fareast-theme-font:minor-fareast;}
span.SpellE
	{mso-style-name:"";
	mso-spl-e:yes;}
span.GramE
	{mso-style-name:"";
	mso-gram-e:yes;}
.MsoChpDefault
	{mso-style-type:export-only;
	mso-default-props:yes;
	font-size:10.0pt;
	mso-ansi-font-size:10.0pt;
	mso-bidi-font-size:10.0pt;}
@page WordSection1
	{size:612.0pt 792.0pt;
	margin:35.45pt 59.15pt 42.55pt 70.85pt;
	mso-header-margin:35.4pt;
	mso-footer-margin:35.4pt;
	mso-paper-source:0;}
div.WordSection1
	{page:WordSection1;}
-->
</style>
<!--[if gte mso 10]>
<style>
 /* Style Definitions */
 table.MsoNormalTable
	{mso-style-name:"Tableau Normal";
	mso-tstyle-rowband-size:0;
	mso-tstyle-colband-size:0;
	mso-style-noshow:yes;
	mso-style-priority:99;
	mso-style-parent:"";
	mso-padding-alt:0cm 5.4pt 0cm 5.4pt;
	mso-para-margin:0cm;
	mso-para-margin-bottom:.0001pt;
	mso-pagination:widow-orphan;
	font-size:10.0pt;
	font-family:"Times New Roman",serif;}
</style>
<![endif]--><!--[if gte mso 9]><xml>
 <o:shapedefaults v:ext="edit" spidmax="1026"/>
</xml><![endif]--><!--[if gte mso 9]><xml>
 <o:shapelayout v:ext="edit">
  <o:idmap v:ext="edit" data="1"/>
 </o:shapelayout></xml><![endif]-->
</head>

<body lang=FR-CA link=blue vlink=purple style='tab-interval:35.4pt; margin: 0 45px 45px 45px!important;'>

<div class=WordSection1 style='max-width: 1024px;'>

<p class=MsoNormal align=center style='text-align:center'><span
style='font-size:16.0pt;line-height:115%'>&nbsp;</span></p>

<p class=MsoNormal align=center style='text-align:center'><span
style='font-size:14.0pt;line-height:115%'>&nbsp;</span></p>

<p class=MsoNormal align=center style='text-align:center'><span
style='font-size:16.0pt;line-height:115%'>DOCUMENT DE FORMATION</span></p>

<p class=MsoNormal align=center style='text-align:center'><span
style='font-size:12.0pt;line-height:115%'>SUR LES</span></p>

<p class=MsoNormal align=center style='text-align:center'><span
style='font-size:16.0pt;line-height:115%'>FONCTIONS D’IMPORTATION DE DONNÉES</span></p>

<p class=MsoNormal align=center style='text-align:center'><span
style='font-size:10.0pt;line-height:115%'>(RÉVISION DU 2018-07-09)</span></p>

<p class=MsoNormal>&nbsp;</p>

<p class=MsoNormal>&nbsp;</p>

<p class=MsoNormal><b><span style='font-size:9.0pt;line-height:115%;color:red'>DOCUMENT
CONFIDENTIEL</span></b></p>

<p class=MsoNormal style='text-align:justify'><b><u><span style='font-size:
9.0pt;line-height:115%'>Avis important :</span></u></b><span style='font-size:
9.0pt;line-height:115%'> Toute reproduction et/ou distribution partielle ou
entière de ce document, sans le consentement écrit de Solutions DocMatic <span
class=SpellE>inc.</span> est formellement interdite et contreviendra à
l’actuelle entente. La reproduction et/ou distribution de ce document est
autorisée dans le seul but d’être rendu disponible aux utilisateurs inscrits au
registre des usagers officiels de la plateforme DocMatic. Pour toute demande
concernant la reproduction et/ou la distribution de ce document, veuillez s’il
vous plaît vous référer à l’adresse de messagerie suivante&nbsp;: <span
class=MsoHyperlink><span style='color:#0000BF'><a
href="mailto:<EMAIL>"><span style='color:#0000BF'><EMAIL></span></a></span></span></span></p>

<p class=MsoNormal align=center style='text-align:center'><span
style='font-size:16.0pt;line-height:115%'>&nbsp;</span></p>

<p class=MsoNormal align=right style='text-align:right'><a
href="http://www.docmatic.ca/" target="_blank"><span style='mso-no-proof:yes;
text-decoration:none;text-underline:none'><img border=0 width=296 height=220
id="_x0000_i1048" src="images/fr/image001.png"
alt="Logo Docmatic"></span></a></p>

<p class=MsoTocHeading><span lang=FR style='mso-ansi-language:FR'>Table des
matières</span></p>

<p class=MsoNormal><span lang=FR style='mso-ansi-language:FR'>&nbsp;</span></p>

<p class=MsoListParagraph>Avant-propos</p>

<p class=MsoListParagraph style='margin-left:54.0pt;text-indent:-18.0pt'><span
lang=FR style='mso-ansi-language:FR'>1.</span><span lang=FR style='font-size:
7.0pt;line-height:115%;font-family:"Times New Roman",serif;mso-ansi-language:
FR'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span>Définition d’une matrice
d’importation</p>

<p class=MsoListParagraph>2.&nbsp;&nbsp;&nbsp; Matrice de type «&nbsp;Liste
d’éléments&nbsp;»</p>

<p class=MsoListParagraph style='text-indent:34.9pt'>a)&nbsp; Type de fichier
attendu </p>

<p class=MsoListParagraph style='text-indent:34.9pt'>b)&nbsp; Exemple de
fichier Ms Excel de type «&nbsp;Liste d’éléments&nbsp;»</p>

<p class=MsoListParagraph style='text-indent:34.9pt'>c)&nbsp; Procédure de
création d’une matrice de type «&nbsp;Liste d’éléments&nbsp;»</p>

<p class=MsoListParagraph>3.&nbsp;&nbsp;&nbsp; Procédure d’importation des
données de type «&nbsp;Liste d’éléments&nbsp;»</p>

<p class=MsoListParagraph>4.&nbsp;&nbsp;&nbsp; Matrice de type
«&nbsp;Propriétés spécifiques&nbsp;»</p>

<p class=MsoListParagraph style='text-indent:34.9pt'>a)&nbsp; Type de fichier
attendu </p>

<p class=MsoListParagraph style='text-indent:34.9pt'>b)&nbsp; Exemple de
fichier Ms Excel de type «&nbsp;Propriétés spécifiques&nbsp;»</p>

<p class=MsoListParagraph style='text-indent:34.9pt'>c)&nbsp; Procédure de
création d’une matrice de type «&nbsp;Propriétés spécifiques&nbsp;»</p>

<p class=MsoListParagraph>5.&nbsp;&nbsp;&nbsp; Les matrices de champs
dénormalisés</p>

<p class=MsoListParagraph style='text-indent:34.9pt'>a)&nbsp; Définition d’un
«&nbsp;champ dénormalisé&nbsp;»&nbsp; </p>

<p class=MsoListParagraph style='text-indent:34.9pt'>b)&nbsp; Procédure de
création d’une matrice de «&nbsp;champ dénormalisé&nbsp;»&nbsp;</p>

<p class=MsoListParagraph>6. &nbsp;&nbsp;&nbsp;Procédure d’importation des
données de type «&nbsp;Propriétés spécifiques&nbsp;»</p>

<p class=MsoListParagraph>7. &nbsp;&nbsp;&nbsp;Traitement des éléments
interdépendants </p>

<p class=MsoListParagraph>8.&nbsp; &nbsp;&nbsp;Conclusion</p>

<p class=MsoNormal style='text-align:justify'><o:p>&nbsp;</o:p></p>

<p class=MsoNormal style='text-align:justify'><b><u>Avant-propos&nbsp;:</u></b>
La plateforme DocMatic permet l’importation de données grâce à son système ETL
(<span class=SpellE>Extract</span> <span class=SpellE>Transform</span> <span
class=SpellE>Load</span>). Ce système exploite le principe des «&nbsp;Matrices
d’importation&nbsp;». Les matrices d’importation permettent de gérer les
imports de données en provenance de fichiers Ms Excel ou encore, directement du
«&nbsp;Service WEB&nbsp;» (plugin <span class=SpellE>LinkMatic</span>). Dans ce
document, nous traiterons plus particulièrement des <u>importations à partir de
fichiers Ms Excel</u>.</p>

<p class=MsoNormal style='text-align:justify'><o:p>&nbsp;</o:p></p>

<p class=MsoNormal>1.<span style='font-size:7.0pt;line-height:115%;font-family:
"Times New Roman",serif'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span><u>Définition
d’une matrice d’importation :</u> Une matrice d’importation est une recette
d’association des champs de données à <span class=GramE>importer&nbsp; (</span>colonnes
d’un fichier Ms Excel) aux champs des formulaires DocMatic. En créant une
matrice d’importation, vous indiquez au module d’importation de données,
l’origine et la destination de chaque donnée à importer. Il existe trois (3)
types de matrice d’importation, tel qu’il y a également trois (3) différents
types de formulaires;</p>

<p class=MsoNormal style='text-align:justify'><o:p>&nbsp;</o:p></p>

<p class=MsoListParagraph style='margin-left:70.9pt;text-align:justify;
text-indent:-14.2pt'>a)<span style='font-size:7.0pt;line-height:115%;
font-family:"Times New Roman",serif'>&nbsp;&nbsp;&nbsp; </span>La matrice de
type «&nbsp;Liste d’éléments&nbsp;»;</p>

<p class=MsoListParagraph style='margin-left:70.9pt;text-align:justify;
text-indent:-14.2pt'>b)<span style='font-size:7.0pt;line-height:115%;
font-family:"Times New Roman",serif'>&nbsp;&nbsp;&nbsp; </span>La matrice de
type «&nbsp;Propriétés spécifiques&nbsp;»;</p>

<p class=MsoListParagraph style='margin-left:70.9pt;text-align:justify;
text-indent:-14.2pt'>c)<span style='font-size:7.0pt;line-height:115%;
font-family:"Times New Roman",serif'>&nbsp;&nbsp;&nbsp; </span>La matrice de
type «&nbsp;Liste de raccords&nbsp;» (non traitée dans ce document)</p>

<p class=MsoListParagraph>&nbsp;</p>

<p class=MsoNormal>2.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; <u>La matrice de type
«&nbsp;Liste d’éléments&nbsp;»&nbsp;:</u> Comme son nom l’indique, ce type de
matrice permet d’importer des données sous forme de «&nbsp;listes&nbsp;» dans
les formulaires de type «&nbsp;Liste d’éléments&nbsp;». Les formulaires de ce
type sont généralement associés à des fiches de type «&nbsp;Locaux&nbsp;». Il
s’agit de formulaires permettant de définir le contenu des locaux, comme par
exemple;</p>

<p class=MsoListParagraph>&nbsp;</p>

<p class=MsoListParagraph style='margin-left:70.9pt;text-align:justify;
text-indent:-14.2pt'>a)<span style='font-size:7.0pt;line-height:115%;
font-family:"Times New Roman",serif'>&nbsp;&nbsp;&nbsp; </span>Prises
électriques;</p>

<p class=MsoListParagraph style='margin-left:70.9pt;text-align:justify;
text-indent:-14.2pt'>b)<span style='font-size:7.0pt;line-height:115%;
font-family:"Times New Roman",serif'>&nbsp;&nbsp;&nbsp; </span>Appareils
d’éclairage;</p>

<p class=MsoListParagraph style='margin-left:70.9pt;text-align:justify;
text-indent:-14.2pt'>c)<span style='font-size:7.0pt;line-height:115%;
font-family:"Times New Roman",serif'>&nbsp;&nbsp;&nbsp; </span>Toilettes;</p>

<p class=MsoListParagraph style='margin-left:70.9pt;text-align:justify;
text-indent:-14.2pt'>d)<span style='font-size:7.0pt;line-height:115%;
font-family:"Times New Roman",serif'>&nbsp;&nbsp;&nbsp; </span>Diffuseurs;</p>

<p class=MsoListParagraph style='margin-left:70.9pt;text-align:justify;
text-indent:-14.2pt'>e)<span style='font-size:7.0pt;line-height:115%;
font-family:"Times New Roman",serif'>&nbsp;&nbsp;&nbsp; </span>Haut-parleurs;</p>

<p class=MsoListParagraph style='margin-left:70.9pt;text-align:justify;
text-indent:-14.2pt'>f)<span style='font-size:7.0pt;line-height:115%;
font-family:"Times New Roman",serif'>&nbsp;&nbsp;&nbsp;&nbsp; </span>Autres
éléments de listes</p>

<p class=MsoNormal style='text-align:justify'><b>Note&nbsp;:</b> Il existe
actuellement un seul formulaire de type «&nbsp;Liste d’éléments&nbsp;» associé
à des fiches d’un type autre que «&nbsp;Fiche de locaux&nbsp;». Il s’agit du
formulaire «&nbsp;Dispositifs électriques de champ&nbsp;» que l’on peut
associer aux fiches d’équipements seulement.</p>

<p class=MsoNormal style='text-align:justify'>&nbsp;</p>

<p class=MsoListParagraph style='text-align:justify;text-indent:-18.0pt'>a)<span
style='font-size:7.0pt;line-height:115%;font-family:"Times New Roman",serif'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span><u>Type de fichier attendu&nbsp;:</u> Le fichier de données (Ms Excel)
doit avoir minimalement trois (3) colonnes afin de pouvoir être traité par
l’outil d’importation. Pour ce type d’importation, l’ordre des colonnes dans le
fichier d’importation (Ms Excel) doit être tel que mentionné ci-après;</p>

<p class=MsoListParagraph style='text-align:justify'>&nbsp;</p>

<p class=MsoListParagraph style='margin-left:71.4pt;text-align:justify;
text-indent:-18.0pt'><span style='font-family:Symbol'>·</span><span
style='font-size:7.0pt;line-height:115%;font-family:"Times New Roman",serif'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span><u>Première colonne</u>&nbsp;: Code du local (ou de l’équipement&nbsp;:
cas exceptionnel)</p>

<p class=MsoNormal style='margin-left:70.8pt;text-align:justify'>Cette colonne
permet d’indiquer à l’outil d’importation la destination (<u>fiche</u>) de
l’objet à importer.</p>

<p class=MsoListParagraph style='margin-left:71.4pt;text-align:justify;
text-indent:-18.0pt'><span style='font-family:Symbol'>·</span><span
style='font-size:7.0pt;line-height:115%;font-family:"Times New Roman",serif'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span><u>Deuxième colonne</u>&nbsp;: Type</p>

<p class=MsoNormal style='margin-left:70.8pt;text-align:justify'>Généralement
associé au paramètre <b>«&nbsp;<span class=SpellE><u>Family_Name</u></span>&nbsp;»</b>
de Revit (<span class=SpellE>Family_Name</span> / Type), cette colonne
indiquera à l’outil d’importation, le type d’élément à importer.</p>

<p class=MsoListParagraph style='margin-left:71.4pt;text-align:justify;
text-indent:-18.0pt'><span style='font-family:Symbol'>·</span><span
style='font-size:7.0pt;line-height:115%;font-family:"Times New Roman",serif'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span><u>Troisième colonne&nbsp;</u>: Particularité</p>

<p class=MsoNormal style='margin-left:70.8pt;text-align:justify'>Généralement
associé au «&nbsp;<b><u>Type</u></b>&nbsp;» de Revit (<span class=SpellE>Family_Name</span>
/ Type), cette colonne sera jumelée automatiquement par l’outil d’importation à
la deuxième colonne du fichier (type) afin de déterminer le type d’élément à
importer, ainsi que certaines précisions comme par exemple, la méthode
d’installation ou tout autre détails spécifiques.</p>

<p class=MsoListParagraph style='margin-left:71.4pt;text-align:justify;
text-indent:-18.0pt'><span style='font-family:Symbol'>·</span><span
style='font-size:7.0pt;line-height:115%;font-family:"Times New Roman",serif'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span><u>Quatrième colonne (optionnelle)</u>&nbsp;: Quantité</p>

<p class=MsoListParagraph style='margin-left:71.4pt;text-align:justify'>&nbsp;</p>

<p class=MsoListParagraph style='margin-left:71.4pt;text-align:justify'>Cette
colonne (si présente) permet d’indiquer à l’outil d’importation DocMatic, la quantité
d’éléments à importer. Dans le cas où cette colonne serait absente, l’outil
d’importation considérerait alors chaque ligne de données comme correspondant à
la quantité de «&nbsp;1&nbsp;». Une compilation du nombre d’occurrence de la
combinaison «&nbsp;Local / type d’élément / quantité&nbsp;» se fera dans tous
les cas, que la colonne de quantité soit présente ou non.</p>

<p class=MsoListParagraph style='margin-left:71.4pt;text-align:justify'>&nbsp;</p>

<p class=MsoListParagraph style='margin-left:71.4pt;text-align:justify'>&nbsp;</p>

<p class=MsoListParagraph style='margin-left:0cm;text-indent:10.9pt'>b)<span
style='font-size:7.0pt;line-height:115%;font-family:"Times New Roman",serif'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span><u>Exemple de fichier Ms Excel pour l’importation d’une liste de prises
électriques</u></p>

<p class=MsoListParagraph style='margin-left:10.9pt'>&nbsp;</p>

<table class=MsoNormalTable border=0 cellspacing=0 cellpadding=0
 style='border-collapse:collapse;mso-yfti-tbllook:1184;mso-padding-alt:0cm 0cm 0cm 0cm'>
 <tr style='mso-yfti-irow:0;mso-yfti-firstrow:yes'>
  <td width=652 valign=top style='width:489.0pt;padding:0cm 5.4pt 0cm 5.4pt'>
  <p class=MsoNormal style='margin-bottom:0cm;margin-bottom:.0001pt;line-height:
  normal'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
  <span style='mso-no-proof:yes'><img border=0 width=454 height=291
  id="_x0000_i1047" src="images/fr/image002.png"></span></p>
  </td>
 </tr>
 <tr style='mso-yfti-irow:1;mso-yfti-lastrow:yes'>
  <td width=652 valign=top style='width:489.0pt;padding:0cm 5.4pt 0cm 5.4pt'>
  <p class=MsoNormal align=center style='margin-bottom:0cm;margin-bottom:.0001pt;
  text-align:center;line-height:normal'><u><span style='font-size:8.0pt'>Illustration
  1</span></u></p>
  <p class=MsoNormal align=center style='margin-bottom:0cm;margin-bottom:.0001pt;
  text-align:center;line-height:normal'><span style='font-size:8.0pt'>&nbsp;</span></p>
  <p class=MsoNormal style='margin-bottom:0cm;margin-bottom:.0001pt;line-height:
  normal'>&nbsp;</p>
  </td>
 </tr>
</table>

<p class=MsoListParagraph style='margin-left:0cm;text-indent:10.9pt'>c)<span
style='font-size:7.0pt;line-height:115%;font-family:"Times New Roman",serif'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span><u>Procédure de création d’une matrice de type «&nbsp;Liste
d’éléments&nbsp;»&nbsp;</u></p>

<p class=MsoListParagraph style='margin-left:3.8pt'>&nbsp;</p>

<p class=MsoListParagraph style='margin-left:2.0cm;text-align:justify;
text-indent:-21.25pt'>1.<span style='font-size:7.0pt;line-height:115%;
font-family:"Times New Roman",serif'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span>Allez dans l’onglet «&nbsp;Librairies&nbsp;», sous la section
«&nbsp;Administration BIM&nbsp;»;</p>

<p class=MsoListParagraph style='margin-left:2.0cm;text-align:justify;
text-indent:-21.25pt'>2.<span style='font-size:7.0pt;line-height:115%;
font-family:"Times New Roman",serif'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span>Cliquez sur le volet «&nbsp;Matrices d’importation&nbsp;»;</p>

<p class=MsoListParagraph style='margin-left:2.0cm;text-align:justify;
text-indent:-21.25pt'>3.<span style='font-size:7.0pt;line-height:115%;
font-family:"Times New Roman",serif'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span>Cliquez sur «&nbsp;Ajouter&nbsp;» (coin supérieur droit de la liste de
matrices);</p>

<p class=MsoListParagraph style='margin-left:2.0cm;text-align:justify;
text-indent:-21.25pt'>4.<span style='font-size:7.0pt;line-height:115%;
font-family:"Times New Roman",serif'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span>Créez votre matrice en suivant l’exemple ci-dessous en l’adaptant à
votre besoin;</p>

<table class=MsoNormalTable border=0 cellspacing=0 cellpadding=0
 style='border-collapse:collapse;mso-yfti-tbllook:1184;mso-padding-alt:0cm 0cm 0cm 0cm'>
 <tr style='mso-yfti-irow:0;mso-yfti-firstrow:yes'>
  <td width=652 valign=top style='width:489.0pt;padding:0cm 5.4pt 0cm 5.4pt'>
  <p class=MsoNormal align=center style='margin-bottom:0cm;margin-bottom:.0001pt;
  text-align:center;line-height:normal'><span style='mso-no-proof:yes'><img
  border=0 width=560 height=259 id="_x0000_i1046"
  src="images/fr/image003.png"></span></p>
  </td>
 </tr>
 <tr style='mso-yfti-irow:1;mso-yfti-lastrow:yes'>
  <td width=652 valign=top style='width:489.0pt;padding:0cm 5.4pt 0cm 5.4pt'>
  <p class=MsoNormal align=center style='margin-bottom:0cm;margin-bottom:.0001pt;
  text-align:center;line-height:normal'><u><span style='font-size:8.0pt'>Illustration
  2</span></u></p>
  </td>
 </tr>
</table>

<p class=MsoListParagraph style='margin-left:35.45pt'>&nbsp;</p>

<p class=MsoListParagraph style='margin-left:2.0cm;text-align:justify;
text-indent:-21.25pt'>5.<span style='font-size:7.0pt;line-height:115%;
font-family:"Times New Roman",serif'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span>Choisissez le type «&nbsp;Liste d’élément&nbsp;», le nom qui correspond
à votre besoin, la description qui représente l’utilité de votre matrice, le
type de fiche «&nbsp;Local&nbsp;», le numéro de lot (optionnel), le formulaire
correspondant à votre besoin, et le mode de gestion «&nbsp;Manuelle&nbsp;»;</p>

<p class=MsoListParagraph style='margin-left:2.0cm;text-align:justify;
text-indent:-21.25pt'>6.<span style='font-size:7.0pt;line-height:115%;
font-family:"Times New Roman",serif'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span>Sauvegardez la matrice.</p>

<p class=MsoListParagraph style='margin-left:71.25pt;text-align:justify'>&nbsp;</p>

<p class=MsoNormal style='text-align:justify'><b><u>Note&nbsp;:</u></b> À ce
stade, il n’est pas requis d’entrer d’autres informations dans votre matrice
(section du bas) parce que lors de l’importation des données, l’outil
d’importation détectera automatiquement les éléments manquants (selon
l’information contenue dans votre fichier Ms Excel) et vous proposera de
compléter votre matrice automatiquement, ce qui vous fera sauver du temps et
vous évitera également les fautes de frappe.</p>

<p class=MsoNormal><o:p>&nbsp;</o:p></p>

<p class=MsoNormal>3.<span style='font-size:7.0pt;line-height:115%;font-family:
"Times New Roman",serif'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span><u>Procédure
d’importation des données de type «&nbsp;Liste d’éléments&nbsp;»&nbsp;:</u></p>

<p class=MsoNormal style='text-align:justify'><o:p>&nbsp;</o:p></p>

<p class=MsoNormal style='margin-left:18.0pt;text-align:justify'>Votre matrice
est maintenant créée et votre fichier de données (Ms Excel) contient tous les
éléments requis. Il ne reste maintenant qu’à lier ces deux requis pour amorcer
le processus d’importation de données. Voici comment procéder;</p>

<p class=MsoNormal style='margin-left:18.0pt;text-align:justify'><o:p>&nbsp;</o:p></p>

<p class=MsoListParagraph style='text-align:justify'>&nbsp;a)<span
style='font-size:7.0pt;line-height:115%;font-family:"Times New Roman",serif'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span>Allez dans l’onglet «&nbsp;Fiche&nbsp;» et activez le petit menu
déroulant latéral;</p>

<table class=MsoNormalTable border=0 cellspacing=0 cellpadding=0
 style='border-collapse:collapse;mso-yfti-tbllook:1184;mso-padding-alt:0cm 0cm 0cm 0cm'>
 <tr style='mso-yfti-irow:0;mso-yfti-firstrow:yes'>
  <td width=652 valign=top style='width:489.0pt;padding:0cm 5.4pt 0cm 5.4pt'>
  <p class=MsoNormal align=center style='margin-bottom:0cm;margin-bottom:.0001pt;
  text-align:center;line-height:normal'><span style='mso-no-proof:yes'><img
  border=0 width=222 height=301 id="_x0000_i1045"
  src="images/fr/image004.png"></span></p>
  </td>
 </tr>
 <tr style='mso-yfti-irow:1;mso-yfti-lastrow:yes'>
  <td width=652 valign=top style='width:489.0pt;padding:0cm 5.4pt 0cm 5.4pt'>
  <p class=MsoNormal align=center style='margin-bottom:0cm;margin-bottom:.0001pt;
  text-align:center;line-height:normal'><u><span style='font-size:8.0pt'>Illustration
  3</span></u></p>
  </td>
 </tr>
</table>

<p class=MsoNormal align=center style='margin-bottom:12.0pt;text-align:center'><span
style='font-size:8.0pt;line-height:115%'>&nbsp;</span></p>

<p class=MsoListParagraph style='text-align:justify;text-indent:-18.0pt'>b)<span
style='font-size:7.0pt;line-height:115%;font-family:"Times New Roman",serif'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span>Dans la section «&nbsp;Données&nbsp;», cliquez sur le lien
«&nbsp;Importer&nbsp;»;</p>

<p class=MsoListParagraph style='text-align:justify'>&nbsp;</p>

<p class=MsoListParagraph style='text-align:justify;text-indent:-18.0pt'>c)<span
style='font-size:7.0pt;line-height:115%;font-family:"Times New Roman",serif'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span>Retracez votre matrice dans la liste et cliquez sur l’icône
d’importation de fichier à l’extrême droite de la ligne;</p>

<p class=MsoListParagraph>&nbsp;</p>

<table class=MsoNormalTable border=0 cellspacing=0 cellpadding=0
 style='margin-left:36.0pt;border-collapse:collapse;mso-yfti-tbllook:1184;
 mso-padding-alt:0cm 0cm 0cm 0cm'>
 <tr style='mso-yfti-irow:0;mso-yfti-firstrow:yes'>
  <td width=652 valign=top style='width:489.0pt;padding:0cm 5.4pt 0cm 5.4pt'>
  <p class=MsoListParagraph align=center style='margin:0cm;margin-bottom:.0001pt;
  text-align:center;line-height:normal'><span style='mso-no-proof:yes'><img
  border=0 width=599 height=232 id="_x0000_i1044"
  src="images/fr/image005.png"></span></p>
  </td>
 </tr>
 <tr style='mso-yfti-irow:1;mso-yfti-lastrow:yes'>
  <td width=652 valign=top style='width:489.0pt;padding:0cm 5.4pt 0cm 5.4pt'>
  <p class=MsoListParagraph align=center style='margin:0cm;margin-bottom:.0001pt;
  text-align:center;line-height:normal'><u><span style='font-size:8.0pt'>Illustration
  4</span></u></p>
  </td>
 </tr>
</table>

<p class=MsoNormal style='text-align:justify'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
<span style='font-size:8.0pt;line-height:115%'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></p>

<p class=MsoListParagraph style='text-align:justify;text-indent:-18.0pt'>d)<span
style='font-size:7.0pt;line-height:115%;font-family:"Times New Roman",serif'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span>Appuyez sur le bouton «&nbsp;Choisir un fichier&nbsp;», puis cliquez sur
valider une fois le fichier choisi;&nbsp;&nbsp; </p>

<table class=MsoNormalTable border=0 cellspacing=0 cellpadding=0
 style='border-collapse:collapse;mso-yfti-tbllook:1184;mso-padding-alt:0cm 0cm 0cm 0cm'>
 <tr style='mso-yfti-irow:0;mso-yfti-firstrow:yes'>
  <td width=652 valign=top style='width:489.0pt;padding:0cm 5.4pt 0cm 5.4pt'>
  <p class=MsoNormal align=center style='margin-bottom:0cm;margin-bottom:.0001pt;
  text-align:center;line-height:normal'><span style='mso-no-proof:yes'><img
  border=0 width=568 height=181 id="_x0000_i1043"
  src="images/fr/image006.png"></span></p>
  </td>
 </tr>
 <tr style='mso-yfti-irow:1;mso-yfti-lastrow:yes'>
  <td width=652 valign=top style='width:489.0pt;padding:0cm 5.4pt 0cm 5.4pt'>
  <p class=MsoNormal align=center style='margin-bottom:0cm;margin-bottom:.0001pt;
  text-align:center;line-height:normal'><u><span style='font-size:8.0pt'>Illustration
  5</span></u></p>
  </td>
 </tr>
</table>

<p class=MsoListParagraph><span style='font-size:8.0pt;line-height:115%'>&nbsp;</span></p>

<p class=MsoListParagraph><span style='font-size:8.0pt;line-height:115%'>&nbsp;</span></p>

<p class=MsoListParagraph style='text-align:justify;text-indent:-18.0pt'>e)<span
style='font-size:7.0pt;line-height:115%;font-family:"Times New Roman",serif'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span>L’exemple suivant (voir illustration 6) représente l’interface
d’association de champs. En vous servant des menus déroulants, associez les
composantes de votre matrice aux colonnes ayant été détectées dans votre
fichier de données par l’outil d’importation. Le pivot doit impérativement être
associé à la colonne qui contient les codes des locaux (ou équipements dans un
cas particulier).</p>

<p class=MsoListParagraph style='text-align:justify'>&nbsp;</p>

<p class=MsoListParagraph style='text-align:justify'>Dans cet exemple, les
composantes <span class=SpellE>Family_Name</span>, Type et Quantité possèdent
les mêmes noms que les colonnes du fichier associé. Ceci est un exemple typique
d’importation de données en provenance de Revit puisque les noms <span
class=SpellE>Family_Name</span> et Type correspondent aux termes généralement
utilisés dans Revit. Il ne s’agit pas d’une limitation de l’outil, et vous
pourrez évidemment associer les colonnes détectées sans égard au nom qu’elles
portent. </p>

<p class=MsoListParagraph style='text-align:justify'>&nbsp;</p>

<p class=MsoListParagraph style='text-align:justify'>La colonne quantité est
optionnelle parce que <span class=GramE>certains outil</span> d’exportation
Revit (ex&nbsp;: <span class=SpellE>BimLink</span>) ne produisent pas d’emblée
de colonne quantité (peut être paramétré autrement), mais produisent plutôt un
fichier où chaque ligne correspond à une valeur égale à «&nbsp;1&nbsp;». Tel
qu’indiqué précédemment, une compilation (nombre d’occurrence) de la
combinaison «&nbsp;Local / type d’élément / quantité&nbsp;» permettra de
déterminer la quantité totale d’éléments à considérer, que la colonne de
quantité soit présente ou non.</p>

<p class=MsoNormal style='margin-left:35.45pt'>&nbsp;</p>

<p class=MsoNormal style='margin-left:35.45pt'>Une fois l’association
complétée, appuyez sur le bouton «&nbsp;Synchroniser&nbsp;». </p>

<table class=MsoNormalTable border=0 cellspacing=0 cellpadding=0
 style='margin-left:35.45pt;border-collapse:collapse;mso-yfti-tbllook:1184;
 mso-padding-alt:0cm 0cm 0cm 0cm'>
 <tr style='mso-yfti-irow:0;mso-yfti-firstrow:yes'>
  <td width=610 valign=top style='width:457.35pt;padding:0cm 5.4pt 0cm 5.4pt'>
  <p class=MsoNormal align=center style='margin-bottom:0cm;margin-bottom:.0001pt;
  text-align:center;line-height:normal'><span style='mso-no-proof:yes'><img
  border=0 width=551 height=217 id="_x0000_i1042"
  src="images/fr/image007.png"></span></p>
  </td>
 </tr>
 <tr style='mso-yfti-irow:1;mso-yfti-lastrow:yes'>
  <td width=610 valign=top style='width:457.35pt;padding:0cm 5.4pt 0cm 5.4pt'>
  <p class=MsoNormal align=center style='margin-bottom:0cm;margin-bottom:.0001pt;
  text-align:center;line-height:normal'><u><span style='font-size:8.0pt'>Illustration
  6</span></u></p>
  </td>
 </tr>
</table>

<p class=MsoNormal style='margin-left:35.45pt'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</p>

<p class=MsoListParagraph style='text-align:justify;text-indent:-18.0pt'>f)<span
style='font-size:7.0pt;line-height:115%;font-family:"Times New Roman",serif'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span>L’étape suivante consiste à mettre à jour (si ce n’est déjà fait) la
matrice d’importation dans l’éventualité où, comme dans l’exemple suivant,
l’outil d’importation détectait la présence de certains éléments non reconnus
pas la matrice. En observant l’exemple suivant (voir l’illustration 7), il est
possible de faire trois (3) observations importantes; </p>

<p class=MsoListParagraph style='text-align:justify'>&nbsp;</p>

<p class=MsoListParagraph style='margin-left:72.0pt;text-indent:-72.0pt'><span
style='font-size:7.0pt;line-height:115%;font-family:"Times New Roman",serif'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span>i.<span style='font-size:7.0pt;line-height:115%;font-family:"Times New Roman",serif'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span>La concaténation des champs «&nbsp;<span class=SpellE>Family_Name</span>&nbsp;»
et «&nbsp;Type&nbsp;» avec l’ajout du caractère spécial «&nbsp;©&nbsp;» entre
les deux;</p>

<p class=MsoListParagraph style='margin-left:72.0pt;text-indent:-72.0pt'><span
style='font-size:7.0pt;line-height:115%;font-family:"Times New Roman",serif'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span>ii.<span style='font-size:7.0pt;line-height:115%;font-family:"Times New Roman",serif'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span>L’énumération d’une liste d’éléments (en rouge) non reconnus par la
matrice d’importation. </p>

<p class=MsoListParagraph style='margin-left:72.0pt;text-indent:-72.0pt'><span
style='font-size:7.0pt;line-height:115%;font-family:"Times New Roman",serif'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span>iii.<span style='font-size:7.0pt;line-height:115%;font-family:"Times New Roman",serif'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span>La possibilité de mettre la matrice à jour de manière
«&nbsp;semi-automatisée&nbsp;» via le lien «&nbsp;Cliquez ici pour mettre la
matrice à jour&nbsp;».</p>

<p class=MsoNormal style='margin-left:35.45pt'>&nbsp;</p>

<p class=MsoNormal style='margin-left:35.45pt'>Cliquez sur le lien en bleu pour
mettre la matrice à jour. &nbsp;</p>

<table class=MsoNormalTable border=0 cellspacing=0 cellpadding=0
 style='margin-left:35.45pt;border-collapse:collapse;mso-yfti-tbllook:1184;
 mso-padding-alt:0cm 0cm 0cm 0cm'>
 <tr style='mso-yfti-irow:0;mso-yfti-firstrow:yes'>
  <td width=652 valign=top style='width:489.0pt;padding:0cm 5.4pt 0cm 5.4pt'>
  <p class=MsoNormal align=center style='margin-bottom:0cm;margin-bottom:.0001pt;
  text-align:center;line-height:normal'><span style='mso-no-proof:yes'><img
  border=0 width=608 height=365 id="_x0000_i1041"
  src="images/fr/image008.png"></span></p>
  </td>
 </tr>
 <tr style='mso-yfti-irow:1;mso-yfti-lastrow:yes'>
  <td width=652 valign=top style='width:489.0pt;padding:0cm 5.4pt 0cm 5.4pt'>
  <p class=MsoNormal align=center style='margin-bottom:0cm;margin-bottom:.0001pt;
  text-align:center;line-height:normal'><u><span style='font-size:8.0pt'>Illustration
  7</span></u></p>
  </td>
 </tr>
</table>

<p class=MsoNormal style='margin-left:35.45pt'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
&nbsp;</p>

<p class=MsoListParagraph style='text-align:justify;text-indent:-18.0pt'>g)<span
style='font-size:7.0pt;line-height:115%;font-family:"Times New Roman",serif'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span>De retour dans l’outil de création/gestion de matrice (voir
l’illustration 8). Comme vous pouvez le constater, la colonne des champs
externes a été remplie automatiquement à partir de la liste de noms d’éléments
non détectés précédemment. Il ne reste alors qu’à compléter les correspondances
d’éléments DocMatic pour chacun des «&nbsp;Codes externes&nbsp;» en
sélectionnant des valeurs dans les colonnes de droite. </p>

<p class=MsoListParagraph style='text-align:justify'>&nbsp;</p>

<p class=MsoListParagraph style='margin-left:72.0pt;text-align:justify;
text-indent:-72.0pt'><span style='font-size:7.0pt;line-height:115%;font-family:
"Times New Roman",serif'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span>i.<span style='font-size:7.0pt;line-height:115%;font-family:"Times New Roman",serif'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span>Il est impératif que chaque élément externe soit associé à une combinaison
d’éléments DocMatic distincte sans quoi la matrice ne sera pas valide
(enregistrement refusé).</p>

<p class=MsoListParagraph style='text-align:justify'>&nbsp;</p>

<p class=MsoListParagraph style='margin-left:72.0pt;text-align:justify;
text-indent:-72.0pt'><span style='font-size:7.0pt;line-height:115%;font-family:
"Times New Roman",serif'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span>ii.<span style='font-size:7.0pt;line-height:115%;font-family:"Times New Roman",serif'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span>En observant cet exemple, nous constatons qu’il est possible d’ajouter
des précisions concernant par exemple, le type d’alimentation et le grade de
prise. Il est par ailleurs possible à ce stade, de <u>générer intuitivement</u>
un niveau de précision accru en sélectionnant des éléments dans l’un ou l’autre
des champs du formulaire affecté par la matrice.</p>

<p class=MsoListParagraph style='margin-left:72.0pt;text-align:justify'>&nbsp;</p>

<p class=MsoListParagraph style='margin-left:72.0pt;text-align:justify;
text-indent:-72.0pt'><span style='font-size:7.0pt;line-height:115%;font-family:
"Times New Roman",serif'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span>iii.<span style='font-size:7.0pt;line-height:115%;font-family:"Times New Roman",serif'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span>La gestion de la matrice d’importation est la tâche la plus importante
du processus d’importation. Elle nécessite une attention particulière et
requiert également que chaque élément externe à associer trouve une correspondance
dans la liste des éléments DocMatic. Par conséquent, en début de projet, il
sera requis de mettre les librairies d’éléments à jour pour être en mesure de
compléter vos matrices. Une fois complétée, une matrice d’importation pourra
générer des dizaines de milliers de lignes de données en un seul clic de
souris.</p>

<p class=MsoNormal style='margin-left:35.45pt'>Complétez l’association des
«&nbsp;Codes externes&nbsp;» aux éléments DocMatic et cliquez <span
class=GramE>sur &nbsp;«</span>&nbsp;Sauvegarder&nbsp;».</p>

<table class=MsoNormalTable border=0 cellspacing=0 cellpadding=0
 style='margin-left:35.45pt;border-collapse:collapse;mso-yfti-tbllook:1184;
 mso-padding-alt:0cm 0cm 0cm 0cm'>
 <tr style='mso-yfti-irow:0;mso-yfti-firstrow:yes'>
  <td width=652 valign=top style='width:489.0pt;padding:0cm 5.4pt 0cm 5.4pt'>
  <p class=MsoNormal align=center style='margin-bottom:0cm;margin-bottom:.0001pt;
  text-align:center;line-height:normal'><span style='mso-no-proof:yes'><img
  border=0 width=526 height=379 id="_x0000_i1040"
  src="images/fr/image009.png"></span></p>
  </td>
 </tr>
 <tr style='mso-yfti-irow:1;mso-yfti-lastrow:yes'>
  <td width=652 valign=top style='width:489.0pt;padding:0cm 5.4pt 0cm 5.4pt'>
  <p class=MsoNormal align=center style='margin-bottom:0cm;margin-bottom:.0001pt;
  text-align:center;line-height:normal'><u><span style='font-size:8.0pt'>Illustration
  8</span></u></p>
  </td>
 </tr>
</table>

<p class=MsoNormal style='margin-top:0cm;margin-right:0cm;margin-bottom:12.0pt;
margin-left:35.45pt'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</p>

<p class=MsoListParagraph style='text-align:justify;text-indent:-18.0pt'>h)<span
style='font-size:7.0pt;line-height:115%;font-family:"Times New Roman",serif'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span>La mise à jour d’une matrice d’importation implique la <u>réassociation
du fichier de données</u> et la <u>resynchronisation des informations</u> qu’il
contient. Pour ce faire, répétez les étapes a) à e). Une fois fait, vous
constaterez (voir l’illustration 9) qu’il est possible que les fiches de locaux
(ou d’équipement dans un cas précis) visées par l’importation en cours ne
soient pas créée dans la base de données. À ce stade, il est possible de créer
automatiquement toutes les fiches requises en cliquant sur le lien
«&nbsp;Ajouter tous les éléments au registre DocMatic&nbsp;» au-dessus de la
liste.</p>

<p class=MsoNormal style='margin-left:35.45pt'>&nbsp;</p>

<p class=MsoNormal style='margin-left:35.45pt'>Si aucune fiche n’est manquante,
passez à l’étape suivante.&nbsp;&nbsp; </p>

<table class=MsoNormalTable border=0 cellspacing=0 cellpadding=0
 style='margin-left:35.45pt;border-collapse:collapse;mso-yfti-tbllook:1184;
 mso-padding-alt:0cm 0cm 0cm 0cm'>
 <tr style='mso-yfti-irow:0;mso-yfti-firstrow:yes'>
  <td width=652 valign=top style='width:489.0pt;padding:0cm 5.4pt 0cm 5.4pt'>
  <p class=MsoNormal align=center style='margin-bottom:0cm;margin-bottom:.0001pt;
  text-align:center;line-height:normal'><span style='mso-no-proof:yes'><img
  border=0 width=581 height=350 id="_x0000_i1039"
  src="images/fr/image010.png"></span></p>
  </td>
 </tr>
 <tr style='mso-yfti-irow:1;mso-yfti-lastrow:yes'>
  <td width=652 valign=top style='width:489.0pt;padding:0cm 5.4pt 0cm 5.4pt'>
  <p class=MsoNormal align=center style='margin-bottom:0cm;margin-bottom:.0001pt;
  text-align:center;line-height:normal'><u><span style='font-size:8.0pt'>Illustration
  9</span></u></p>
  </td>
 </tr>
</table>

<p class=MsoNormal style='margin-left:35.45pt'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
<span style='font-size:8.0pt;line-height:115%'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></p>

<p class=MsoListParagraph style='text-align:justify;text-indent:-18.0pt'>i)<span
style='font-size:7.0pt;line-height:115%;font-family:"Times New Roman",serif'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span>Nous voici maintenant à l’étape de l’importation des données (voir l’illustration
10). L’exemple suivant illustre 16 erreurs de concordance, soit l’ensemble des <u>discordances
d’information</u> (au niveau qualitatif et/ou quantitatif) détectées par
l’outil d’importation, <u>entre le contenu actuel de DocMatic et le contenu
présent dans votre fichier de données</u>. À ce stade, aucune donnée n’a encore
été importée et l’icône du petit fichier Ms Excel dans le coin supérieur droit
de la grille vous permettra (si requis) d’exporter un rapport de discordance
sans que vous n’ayez à importer quoi que ce soit. Cette fonction peut être
extrêmement utile si vous désirez comparer des valeurs de paramètres externes
comme par exemple des données de calculs.</p>

<p class=MsoListParagraph style='text-align:justify'>&nbsp;</p>

<p class=MsoListParagraph style='text-align:justify'>Passons maintenant en
revue la structure de la grille;</p>

<p class=MsoListParagraph style='text-align:justify'>&nbsp;</p>

<p class=MsoListParagraph style='margin-left:72.0pt;text-align:justify;
text-indent:-72.0pt'><span style='font-size:7.0pt;line-height:115%;font-family:
"Times New Roman",serif'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span>i.<span style='font-size:7.0pt;line-height:115%;font-family:"Times New Roman",serif'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span>Celle-ci se compose de six (6) colonnes. Les premières et deuxièmes sont
respectivement le «&nbsp;Code du local de destination&nbsp;» et le
«&nbsp;Formulaire&nbsp;de <span class=GramE>destination»</span>. Les troisièmes
et quatrièmes représentent la comparaison des valeurs présentes dans DocMatic,
versus dans le fichier d’importation. Dans le cas présent, aucune donnée n’est
présente dans DocMatic alors que le fichier d’importation contient une
multitude d’information. La cinquième colonne représente la quantité d’éléments
à importer. La dernière colonne contient <span class=GramE>un icône</span>
représentant une loupe permettant d’accéder directement au formulaire visé,
dans le but d’y mettre l’information à jour de façon «&nbsp;manuelle&nbsp;».</p>

<p class=MsoListParagraph style='margin-left:72.0pt;text-align:justify'>&nbsp;</p>

<table class=MsoNormalTable border=0 cellspacing=0 cellpadding=0
 style='margin-left:72.0pt;border-collapse:collapse;mso-yfti-tbllook:1184;
 mso-padding-alt:0cm 0cm 0cm 0cm'>
 <tr style='mso-yfti-irow:0;mso-yfti-firstrow:yes'>
  <td width=652 valign=top style='width:489.0pt;padding:0cm 5.4pt 0cm 5.4pt'>
  <p class=MsoListParagraph align=center style='margin:0cm;margin-bottom:.0001pt;
  text-align:center;line-height:normal'><span style='mso-no-proof:yes'><img
  border=0 width=559 height=430 id="_x0000_i1038"
  src="images/fr/image011.png"></span></p>
  </td>
 </tr>
 <tr style='mso-yfti-irow:1;mso-yfti-lastrow:yes'>
  <td width=652 valign=top style='width:489.0pt;padding:0cm 5.4pt 0cm 5.4pt'>
  <p class=MsoListParagraph align=center style='margin:0cm;margin-bottom:.0001pt;
  text-align:center;line-height:normal'><u><span style='font-size:8.0pt'>Illustration
  10</span></u></p>
  </td>
 </tr>
</table>

<p class=MsoListParagraph style='margin-left:72.0pt;text-align:justify'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
<span style='font-size:8.0pt;line-height:115%'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></p>

<p class=MsoListParagraph style='margin-left:72.0pt;text-align:justify;
text-indent:-72.0pt'><span style='font-size:7.0pt;line-height:115%;font-family:
"Times New Roman",serif'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span>ii.<span style='font-size:7.0pt;line-height:115%;font-family:"Times New Roman",serif'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span><u>Mise à jour manuelle (via la loupe)&nbsp;:</u> Rare sont les
contextes où il serait bénéfique d’opter pour une mise à jour manuelle des formulaires
car ceci impliquerait alors une charge de travail considérable. Pour le bien de
la formation, nous allons tout de même passer en revue le contexte de mise à
jour manuelle en cliquant sur la loupe. En observant le formulaire suivant, on
peut constater qu’un encadré pointillé est ajouté dans la partie supérieure du
formulaire. Cet encadré contient les données du fichier d’importation,
lesquelles sont présentées sous la forme du <span class=SpellE>Family_Name</span>
et du Type concaténé (colonne de gauche), de la correspondance de type DocMatic
ainsi que toutes les particularités associées (colonne du centre, suivi de la
quantité par type (colonne de droite). Il suffit donc de s’inspirer de la
colonne du centre pour mettre manuellement à jour les données du formulaire en
sélectionnant les éléments relatifs dans les menus déroulants du formulaire.</p>

<p class=MsoNormal style='margin-left:17.8pt;text-indent:35.45pt'>&nbsp;</p>

<p class=MsoNormal style='margin-left:17.8pt;text-indent:35.45pt'>Une fois
l’information mise à jour manuellement, cliquez sur le bouton
«&nbsp;Sauvegarder&nbsp;».&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </p>

<table class=MsoNormalTable border=0 cellspacing=0 cellpadding=0
 style='margin-left:17.8pt;border-collapse:collapse;mso-yfti-tbllook:1184;
 mso-padding-alt:0cm 0cm 0cm 0cm'>
 <tr style='mso-yfti-irow:0;mso-yfti-firstrow:yes'>
  <td width=652 valign=top style='width:489.0pt;padding:0cm 5.4pt 0cm 5.4pt'>
  <p class=MsoNormal align=center style='margin-bottom:0cm;margin-bottom:.0001pt;
  text-align:center;line-height:normal'><span style='mso-no-proof:yes'><img
  border=0 width=555 height=272 id="_x0000_i1037"
  src="images/fr/image012.png"></span></p>
  </td>
 </tr>
 <tr style='mso-yfti-irow:1;mso-yfti-lastrow:yes'>
  <td width=652 valign=top style='width:489.0pt;padding:0cm 5.4pt 0cm 5.4pt'>
  <p class=MsoNormal align=center style='margin-bottom:0cm;margin-bottom:.0001pt;
  text-align:center;line-height:normal'><u><span style='font-size:8.0pt'>Illustration
  11</span></u></p>
  </td>
 </tr>
</table>

<p class=MsoNormal style='margin-left:17.8pt;text-indent:35.45pt'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span
style='font-size:8.0pt;line-height:115%'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></p>

<p class=MsoListParagraph style='margin-left:72.0pt;text-align:justify;
text-indent:-72.0pt'><span style='font-size:7.0pt;line-height:115%;font-family:
"Times New Roman",serif'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span>iii.<span style='font-size:7.0pt;line-height:115%;font-family:"Times New Roman",serif'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span><u>Mise à jour automatique (via le bouton «&nbsp;Tout importer&nbsp;»)</u>&nbsp;:
Revenu à la grille de détection des erreurs de concordance (voir l’illustration
10), nous optons cette fois pour <u>l’importation automatique des données</u>.
En cliquant sur le lien «&nbsp;Tout importer&nbsp;», nous voyons apparaître la
fenêtre d’avertissement suivante (voir l’illustration 12). Lire attentivement
ce qui s’y trouve car à ce stade, l’option que vous allez choisir peut avoir de
très lourdes conséquences. En lisant le texte suivant, nous constatons qu’il
est possible d’importer l’ensemble des données sans considération pour ce qui
se trouve déjà dans les fiches concernées par l’import. Ceci revient à dire
qu’en cliquant sur «&nbsp;Oui&nbsp;», tous les formulaires visés seront
nettoyés des données qu’ils contiennent afin d’y importer les nouvelles
données. <u>Il n’est pas possible en mode d’importation automatique,
d’ajouter/enlever en partie seulement, l’information contenue dans un
formulaire</u>. En cliquant sur l’option «&nbsp;Non&nbsp;», seuls les
formulaires ne contenant actuellement aucunes données seront affectés, les
autres étant sélectivement «&nbsp;non considérés&nbsp;» dans la procédure
d’importation automatique. <u>Ceci constitue l’option la plus sécuritaire si
vous ne pouvez être absolument certain que votre fichier de données contient la
totalité des données requises dans les locaux visés par l’import</u>. </p>

<p class=MsoListParagraph style='margin-left:72.0pt;text-align:justify'>&nbsp;</p>

<p class=MsoListParagraph style='margin-left:72.0pt;text-align:justify'>En
cliquant sur «&nbsp;Non&nbsp;», vous pourrez encore procéder à l’ajustement
manuel des données via la loupe pour toutes les fiches où l’outil d’importation
continuera de détecter des discordances d’information.</p>

<p class=MsoListParagraph style='margin-left:72.0pt;text-align:justify'>&nbsp;&nbsp;</p>

<p class=MsoListParagraph style='margin-left:54.0pt'>Cliquez sur
«&nbsp;Oui&nbsp;» pour importer automatiquement l’ensemble des données ou sur
«&nbsp;Non&nbsp;» pour effectuer une importation sélective.</p>

<p class=MsoListParagraph style='margin-left:54.0pt'>&nbsp;</p>

<table class=MsoNormalTable border=0 cellspacing=0 cellpadding=0
 style='margin-left:54.0pt;border-collapse:collapse;mso-yfti-tbllook:1184;
 mso-padding-alt:0cm 0cm 0cm 0cm'>
 <tr style='mso-yfti-irow:0;mso-yfti-firstrow:yes'>
  <td width=652 valign=top style='width:489.0pt;padding:0cm 5.4pt 0cm 5.4pt'>
  <p class=MsoListParagraph align=center style='margin:0cm;margin-bottom:.0001pt;
  text-align:center;line-height:normal'><span style='mso-no-proof:yes'><img
  border=0 width=525 height=517 id="_x0000_i1036"
  src="images/fr/image013.png"></span></p>
  </td>
 </tr>
 <tr style='mso-yfti-irow:1;mso-yfti-lastrow:yes'>
  <td width=652 valign=top style='width:489.0pt;padding:0cm 5.4pt 0cm 5.4pt'>
  <p class=MsoListParagraph align=center style='margin:0cm;margin-bottom:.0001pt;
  text-align:center;line-height:normal'><u><span style='font-size:8.0pt'>Illustration
  12</span></u></p>
  </td>
 </tr>
</table>

<p class=MsoListParagraph style='text-align:justify'>&nbsp;</p>

<p class=MsoListParagraph style='text-align:justify;text-indent:-18.0pt'>j)<span
style='font-size:7.0pt;line-height:115%;font-family:"Times New Roman",serif'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span>Les données de votre matrice de type «&nbsp;Liste d’éléments&nbsp;» sont
maintenant importées et il vous est possible de visualiser le résultat en consultant
les formulaires des fiches <span class=GramE>affectées &nbsp;(</span>voir
l’illustration 13). Nous pouvons constater que l’outil d’importation a bien
réparti les quantités par type d’éléments dans le formulaire. La colonne
«&nbsp;Installation&nbsp;» a également été considérée par la matrice, ce qui a
eu pour résultat de «&nbsp;normaliser&nbsp;» l’information, c'est-à-dire de
séparer l’information par type afin de la traiter indépendamment des autres
informations dans le but d’optimiser le traitement des données (ex&nbsp;:
filtration par type d’installation).</p>

<p class=MsoListParagraph style='text-align:justify'>&nbsp;</p>

<table class=MsoNormalTable border=0 cellspacing=0 cellpadding=0
 style='margin-left:36.0pt;border-collapse:collapse;mso-yfti-tbllook:1184;
 mso-padding-alt:0cm 0cm 0cm 0cm'>
 <tr style='mso-yfti-irow:0;mso-yfti-firstrow:yes'>
  <td width=652 valign=top style='width:489.0pt;padding:0cm 5.4pt 0cm 5.4pt'>
  <p class=MsoListParagraph align=center style='margin:0cm;margin-bottom:.0001pt;
  text-align:center;line-height:normal'><span style='mso-no-proof:yes'><img
  border=0 width=565 height=317 id="_x0000_i1035"
  src="images/fr/image014.png"></span></p>
  </td>
 </tr>
 <tr style='mso-yfti-irow:1;mso-yfti-lastrow:yes'>
  <td width=652 valign=top style='width:489.0pt;padding:0cm 5.4pt 0cm 5.4pt'>
  <p class=MsoListParagraph align=center style='margin:0cm;margin-bottom:.0001pt;
  text-align:center;line-height:normal'><u><span style='font-size:8.0pt'>Illustration
  13</span></u></p>
  </td>
 </tr>
</table>

<p class=MsoNormal style='text-align:justify'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</p>

<p class=MsoNormal>4.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; <span class=GramE><u>Matrice&nbsp;
de</u></span><u> type «&nbsp;Propriétés spécifiques&nbsp;»&nbsp;:</u> Comme son
nom l’indique, ce type de matrice permet d’importer des données de tout genre
dans des formulaires de type «&nbsp;Propriétés spécifiques. Les formulaires de
ce type sont associés à tous les types de fiches. Il s’agit de formulaires
permettant de définir les propriétés d’un élément donné, comme par exemple;</p>

<p class=MsoListParagraph>&nbsp;</p>

<p class=MsoListParagraph style='margin-left:108.0pt;text-align:justify;
text-indent:-9.0pt'><span style='font-family:Symbol'>·</span><span
style='font-size:7.0pt;line-height:115%;font-family:"Times New Roman",serif'>&nbsp;&nbsp;
</span>Locaux;</p>

<p class=MsoListParagraph style='margin-left:108.0pt;text-align:justify;
text-indent:-9.0pt'><span style='font-family:Symbol'>·</span><span
style='font-size:7.0pt;line-height:115%;font-family:"Times New Roman",serif'>&nbsp;&nbsp;
</span>Équipements;</p>

<p class=MsoListParagraph style='margin-left:108.0pt;text-align:justify;
text-indent:-9.0pt'><span style='font-family:Symbol'>·</span><span
style='font-size:7.0pt;line-height:115%;font-family:"Times New Roman",serif'>&nbsp;&nbsp;
</span>Instruments;</p>

<p class=MsoListParagraph style='margin-left:108.0pt;text-align:justify;
text-indent:-9.0pt'><span style='font-family:Symbol'>·</span><span
style='font-size:7.0pt;line-height:115%;font-family:"Times New Roman",serif'>&nbsp;&nbsp;
</span>Autres</p>

<p class=MsoListParagraph style='margin-left:108.0pt;text-align:justify'><span
style='color:gray'>&nbsp;</span></p>

<p class=MsoListParagraph style='text-align:justify;text-indent:-18.0pt'>a)<span
style='font-size:7.0pt;line-height:115%;font-family:"Times New Roman",serif'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span><u>Type de fichier attendu&nbsp;:</u> Le fichier de données (Ms Excel)
doit avoir minimalement deux (2) colonnes afin de pouvoir être traité par
l’outil d’importation.</p>

<p class=MsoListParagraph style='text-align:justify'>&nbsp;</p>

<p class=MsoListParagraph style='margin-left:71.4pt;text-align:justify;
text-indent:-18.0pt'><span style='font-family:Symbol'>·</span><span
style='font-size:7.0pt;line-height:115%;font-family:"Times New Roman",serif'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span><u>Pivot</u>: </p>

<p class=MsoListParagraph style='margin-left:71.4pt;text-align:justify'>&nbsp;</p>

<p class=MsoListParagraph style='margin-left:71.4pt;text-align:justify'>Le
pivot correspond au «&nbsp;Code principal de la fiche&nbsp;» et sert de
référence à l’importation. Il est également possible de pivoter sur le code
alternatif ou les id externes 1 ou 2, mais cette manœuvre est assez rare et ne
sera pas abordée dans le présent texte.</p>

<p class=MsoNormal style='margin-left:70.8pt;text-align:justify'>Cette colonne
permet d’indiquer à l’outil d’importation la destination (<u>fiche</u>) du
paramètre à importer. Il est impératif que cette colonne ne comporte aucun
doublon car l’outil d’importation refusera l’association du fichier.</p>

<p class=MsoListParagraph style='margin-left:71.4pt;text-align:justify;
text-indent:-18.0pt'><span style='font-family:Symbol'>·</span><span
style='font-size:7.0pt;line-height:115%;font-family:"Times New Roman",serif'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span><u>Colonnes de paramètres</u>&nbsp;: </p>

<p class=MsoNormal style='margin-left:70.8pt;text-align:justify'>Ces colonnes
du fichier d’importation (Ms Excel) contiennent l’information propre à chaque
paramètre à traiter. La quantité, le nom et l’ordre de ces colonnes dans le
fichier n’a pas d’importance puisque l’outil vous permettra d’associer chaque
paramètre à traiter, à l’une ou l’autre des colonnes détectées en cours
d’opération;</p>

<p class=MsoNormal style='margin-left:70.8pt;text-align:justify'>&nbsp;</p>

<p class=MsoListParagraph style='margin-left:0cm;text-indent:10.9pt'>b)<span
style='font-size:7.0pt;line-height:115%;font-family:"Times New Roman",serif'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span><u>Exemple de fichier Ms Excel pour l’importation des «&nbsp;Propriétés <span
class=GramE>générales»</span> des pompes</u></p>

<table class=MsoNormalTable border=0 cellspacing=0 cellpadding=0
 style='border-collapse:collapse;mso-yfti-tbllook:1184;mso-padding-alt:0cm 0cm 0cm 0cm'>
 <tr style='mso-yfti-irow:0;mso-yfti-firstrow:yes'>
  <td width=652 valign=top style='width:489.0pt;padding:0cm 5.4pt 0cm 5.4pt'>
  <p class=MsoNormal style='margin-bottom:0cm;margin-bottom:.0001pt;line-height:
  normal'><span style='color:gray;mso-no-proof:yes'><img border=0 width=649
  height=211 id="_x0000_i1034" src="images/fr/image015.png"></span></p>
  </td>
 </tr>
 <tr style='mso-yfti-irow:1;mso-yfti-lastrow:yes'>
  <td width=652 valign=top style='width:489.0pt;padding:0cm 5.4pt 0cm 5.4pt'>
  <p class=MsoListParagraph align=center style='margin:0cm;margin-bottom:.0001pt;
  text-align:center;line-height:normal'><u><span style='font-size:8.0pt'>Illustration
  14</span></u></p>
  </td>
 </tr>
</table>

<p class=MsoNormal>&nbsp;</p>

<p class=MsoListParagraph style='margin-left:0cm;text-indent:10.9pt'>c)<span
style='font-size:7.0pt;line-height:115%;font-family:"Times New Roman",serif'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span><u>Procédure de création d’une matrice de type «&nbsp;Propriétés <span
class=GramE>spécifiques»</span>&nbsp;</u></p>

<p class=MsoListParagraph style='margin-left:3.8pt'>&nbsp;</p>

<p class=MsoListParagraph style='margin-left:49.65pt;text-align:justify;
text-indent:-14.2pt'>1.<span style='font-size:7.0pt;line-height:115%;
font-family:"Times New Roman",serif'>&nbsp;&nbsp;&nbsp; </span>Allez dans
l’onglet «&nbsp;Librairies&nbsp;», sous la section «&nbsp;Administration
BIM&nbsp;»;</p>

<p class=MsoListParagraph style='margin-left:49.65pt;text-align:justify;
text-indent:-14.2pt'>2.<span style='font-size:7.0pt;line-height:115%;
font-family:"Times New Roman",serif'>&nbsp;&nbsp;&nbsp; </span>Cliquez sur le
volet «&nbsp;Matrices d’importation&nbsp;»;</p>

<p class=MsoListParagraph style='margin-left:49.65pt;text-align:justify;
text-indent:-14.2pt'>3.<span style='font-size:7.0pt;line-height:115%;
font-family:"Times New Roman",serif'>&nbsp;&nbsp;&nbsp; </span>Cliquez sur
«&nbsp;Ajouter&nbsp;» (coin supérieur droit de la liste de matrices);</p>

<p class=MsoListParagraph style='margin-left:49.65pt;text-align:justify;
text-indent:-14.2pt'>4.<span style='font-size:7.0pt;line-height:115%;
font-family:"Times New Roman",serif'>&nbsp;&nbsp;&nbsp; </span>Créez votre
matrice en suivant l’exemple ci-dessous en l’adaptant à vos besoins;</p>

<table class=MsoNormalTable border=0 cellspacing=0 cellpadding=0
 style='border-collapse:collapse;mso-yfti-tbllook:1184;mso-padding-alt:0cm 0cm 0cm 0cm'>
 <tr style='mso-yfti-irow:0;mso-yfti-firstrow:yes'>
  <td width=652 valign=top style='width:489.0pt;padding:0cm 5.4pt 0cm 5.4pt'>
  <p class=MsoNormal align=center style='margin-bottom:0cm;margin-bottom:.0001pt;
  text-align:center;line-height:normal'>&nbsp;</p>
  <p class=MsoNormal align=center style='margin-bottom:0cm;margin-bottom:.0001pt;
  text-align:center;line-height:normal'>&nbsp;</p>
  <table class=MsoNormalTable border=0 cellspacing=0 cellpadding=0
   style='border-collapse:collapse;mso-yfti-tbllook:1184;mso-padding-alt:0cm 0cm 0cm 0cm'>
   <tr style='mso-yfti-irow:0;mso-yfti-firstrow:yes'>
    <td width=637 valign=top style='width:477.45pt;padding:0cm 5.4pt 0cm 5.4pt'>
    <p class=MsoNormal align=center style='margin-bottom:0cm;margin-bottom:
    .0001pt;text-align:center;line-height:normal'><b style='mso-bidi-font-weight:
    normal'><span style='color:gray;mso-no-proof:yes'><img border=0 width=607
    height=314 id="_x0000_i1033" src="images/fr/image016.png"></span></b></p>
    </td>
   </tr>
   <tr style='mso-yfti-irow:1;mso-yfti-lastrow:yes'>
    <td width=637 valign=top style='width:477.45pt;padding:0cm 5.4pt 0cm 5.4pt'>
    <p class=MsoNormal align=center style='margin-bottom:0cm;margin-bottom:
    .0001pt;text-align:center;line-height:normal'><b><u><span style='font-size:
    8.0pt'>Illustration 15</span></u></b></p>
    </td>
   </tr>
  </table>
  </td>
 </tr>
 <tr style='mso-yfti-irow:1;mso-yfti-lastrow:yes'>
  <td width=652 valign=top style='width:489.0pt;padding:0cm 5.4pt 0cm 5.4pt'>
  <p class=MsoNormal align=center style='margin-bottom:0cm;margin-bottom:.0001pt;
  text-align:center;line-height:normal'><span style='font-size:8.0pt'>&nbsp;</span></p>
  </td>
 </tr>
</table>

<p class=MsoListParagraph align=center style='margin:0cm;margin-bottom:.0001pt;
text-align:center;line-height:normal'><span style='color:gray'>&nbsp;</span></p>

<p class=MsoListParagraph style='margin-left:49.65pt;text-align:justify;
text-indent:-14.2pt'>5.<span style='font-size:7.0pt;line-height:115%;
font-family:"Times New Roman",serif'>&nbsp;&nbsp;&nbsp; </span>Choisir le type
«&nbsp;Propriétés spécifiques&nbsp;», le nom qui correspond à votre besoin, la
description qui représente l’utilité de votre matrice, le type de fiche, le
numéro de lot (optionnel);</p>

<p class=MsoListParagraph style='margin-left:49.65pt;text-align:justify'>&nbsp;</p>

<p class=MsoListParagraph style='margin-left:49.65pt;text-align:justify;
text-indent:-14.2pt'>6.<span style='font-size:7.0pt;line-height:115%;
font-family:"Times New Roman",serif'>&nbsp;&nbsp;&nbsp; </span>Dans la partie
inférieure (section de sélection des paramètres traités par la matrice),
ajoutez autant de lignes que requis en vous servant de l’icône représentant un
plus «&nbsp;<b>+</b>&nbsp;»;</p>

<p class=MsoListParagraph>&nbsp;</p>

<p class=MsoListParagraph style='margin-left:49.65pt;text-align:justify;
text-indent:-14.2pt'>7.<span style='font-size:7.0pt;line-height:115%;
font-family:"Times New Roman",serif'>&nbsp;&nbsp;&nbsp; </span>Pour chacune des
lignes, sélectionnez le formulaire et le nom du paramètre désiré. Le champ
«&nbsp;Code&nbsp;» (voir première ligne de l’illustration ci-dessus) n’est pas
obligatoire. Il constituera pour le présent exercice, le «&nbsp;Pivot&nbsp;» de
la matrice. Les champs pivots sont intrinsèques aux matrices. Il n’est donc pas
requis de les ajouter dans la liste de champs à importer, à moins que vous
désiriez en <u>importer la valeur</u> en pivotant sur un autre champ
«&nbsp;pivot&nbsp;» parmi les trois suivants;</p>

<p class=MsoListParagraph>&nbsp;</p>

<p class=MsoListParagraph style='margin-left:143.4pt;text-align:justify;
text-indent:-18.0pt'><span style='font-family:Symbol'>·</span><span
style='font-size:7.0pt;line-height:115%;font-family:"Times New Roman",serif'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span>Code alternatif</p>

<p class=MsoListParagraph style='margin-left:143.4pt;text-align:justify;
text-indent:-18.0pt'><span style='font-family:Symbol'>·</span><span
style='font-size:7.0pt;line-height:115%;font-family:"Times New Roman",serif'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span>Id externe 1</p>

<p class=MsoListParagraph style='margin-left:143.4pt;text-align:justify;
text-indent:-18.0pt'><span style='font-family:Symbol'>·</span><span
style='font-size:7.0pt;line-height:115%;font-family:"Times New Roman",serif'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span>Id externe 2</p>

<p class=MsoListParagraph style='margin-left:143.4pt;text-align:justify'>&nbsp;</p>

<p class=MsoNormal style='text-align:justify'><b>Note&nbsp;: </b>La colonne
«&nbsp;Champ externe&nbsp;» contient une liste de paramètres
«&nbsp;pré-associés&nbsp;» permettant d’accélérer la création des matrices.
Dans le cas où votre administrateur BIM aurait pré-associé certains noms de
paramètres Revit à certains champs DocMatic (via l’outil de «&nbsp;Corrélation
des champs externes&nbsp;»), vous pourriez, plutôt que de sélectionner un
formulaire et un champ (colonnes 2 et 3), ne sélectionner qu’un nom de
«&nbsp;Champ externe&nbsp;» dans la première colonne, et les colonnes 2 et 3 se
remplieraient automatiquement.</p>

<p class=MsoListParagraph style='margin-left:49.65pt;text-align:justify;
text-indent:-14.2pt'>8.<span style='font-size:7.0pt;line-height:115%;
font-family:"Times New Roman",serif'>&nbsp;&nbsp;&nbsp; </span>Sauvegardez
votre matrice.</p>

<p class=MsoListParagraph style='margin-left:49.65pt;text-align:justify'>&nbsp;</p>

<p class=MsoNormal>5.<span style='font-size:7.0pt;line-height:115%;font-family:
"Times New Roman",serif'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span><u>Les
«&nbsp;champs dénormalisés&nbsp;»&nbsp;:</u></p>

<p class=MsoListParagraph>&nbsp;</p>

<p class=MsoListParagraph style='text-align:justify;text-indent:-18.0pt'>a)<span
style='font-size:7.0pt;line-height:115%;font-family:"Times New Roman",serif'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span><u>Définition d’un «&nbsp;champ dénormalisé&nbsp;»</u>&nbsp;:</p>

<p class=MsoListParagraph style='text-align:justify'>&nbsp;</p>

<p class=MsoListParagraph style='text-align:justify'>Afin de mieux comprendre
ce qu’est un «&nbsp;champ dénormalisé&nbsp;», voyons d’abord ce qu’est une
donnée «&nbsp;normalisée&nbsp;». La normalisation des données part du principe
de gestion optimisée de l’information. Ce principe veut que les données soient
séparées par type, format et sujet, dans le but d’être «&nbsp;stockées&nbsp;»
dans des champs distincts. Cette méthode permet d’optimiser l’indexation des
bases de données et permet surtout de rechercher l’information avec plus de
précision. Le fait de «&nbsp;normaliser&nbsp;» l’information, implique
l’uniformisation des unités et des formats de saisie parce que les valeurs sont
généralement associées à des menus déroulants dont le contenu doit être
uniformisé pour avoir un certain sens.</p>

<p class=MsoNormal style='margin-left:35.45pt;text-align:justify'>Sans céder à
la facilité, certaines situations (ex&nbsp;: travail en consortium) peuvent
nous contraindre à déployer diverses stratégies d’entrées de données moins
orthodoxes. Tout en facilitant une portion du travail, ces stratégies ne
devraient jamais avoir d’impacts directs sur les principes de base de
l’optimisation de données. Afin de faciliter l’entrée des données dites
«&nbsp;non conventionnelles&nbsp;», la plateforme DocMatic propose une
stratégie de «&nbsp;champs dénormalisés&nbsp;» permettant d’importer tout type
d’information à même les formulaires normalisés, dans le but premier de rendre
l’information disponible rapidement, peu importe le format. Cette stratégie
s’appelle&nbsp;: La création ponctuelle de «&nbsp;champs dénormalisés&nbsp;».</p>

<p class=MsoNormal style='margin-left:35.45pt;text-align:justify'>En plus de
permettre la gestion des données dénormalisées <u>en importation</u>, cette
fonction permet également de générer de l’information dénormalisée <u>en
exportation</u> grâce au principe du matriçage (voir l’illustration 16).</p>

<p class=MsoNormal style='margin-left:35.45pt;text-align:justify'>Pour utiliser
les «&nbsp;champs dénormalisés&nbsp;», il faut d’abord les créer. La fonction
de création des champs dénormalisés est offerte au gestionnaires
BIM. Une fois créés, ces champs (de type texte) deviennent automatiquement
disponibles dans vos formulaires et dans vos matrices, selon la méthode établie
pour les autres champs (champs normalisés).</p>

<p class=MsoNormal style='margin-left:35.45pt;text-align:justify'>&nbsp;</p>

<p class=MsoListParagraph style='text-align:justify;text-indent:-18.0pt'>b)<span
style='font-size:7.0pt;line-height:115%;font-family:"Times New Roman",serif'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span><u>Procédure de création d’une matrice de «&nbsp;champ
dénormalisé&nbsp;»&nbsp;:</u></p>

<p class=MsoListParagraph style='text-align:justify'>&nbsp;</p>

<p class=MsoListParagraph style='margin-left:70.9pt;text-align:justify;
text-indent:-14.2pt'>1.<span style='font-size:7.0pt;line-height:115%;
font-family:"Times New Roman",serif'>&nbsp;&nbsp;&nbsp; </span>Allez dans
l’onglet «&nbsp;Librairies&nbsp;», sous la section «&nbsp;Administration
BIM&nbsp;»;</p>

<p class=MsoListParagraph style='margin-left:70.9pt;text-align:justify;
text-indent:-14.2pt'>2.<span style='font-size:7.0pt;line-height:115%;
font-family:"Times New Roman",serif'>&nbsp;&nbsp;&nbsp; </span>Cliquez sur le
volet «&nbsp;Champs dénormalisés&nbsp;»;</p>

<p class=MsoListParagraph style='margin-left:70.9pt;text-align:justify;
text-indent:-14.2pt'>3.<span style='font-size:7.0pt;line-height:115%;
font-family:"Times New Roman",serif'>&nbsp;&nbsp;&nbsp; </span>Cliquez sur
«&nbsp;Ajouter&nbsp;» (coin supérieur droit de la liste de matrices);</p>

<p class=MsoListParagraph style='margin-left:70.9pt;text-align:justify;
text-indent:-14.2pt'>4.<span style='font-size:7.0pt;line-height:115%;
font-family:"Times New Roman",serif'>&nbsp;&nbsp;&nbsp; </span>Créez votre
matrice en suivant l’exemple ci-dessous en l’adaptant à vos besoins;</p>

<p class=MsoListParagraph style='margin-left:70.9pt;text-align:justify;
text-indent:-14.2pt'>5.<span style='font-size:7.0pt;line-height:115%;
font-family:"Times New Roman",serif'>&nbsp;&nbsp;&nbsp; </span>Sauvegardez
votre matrice de champ dénormalisé.</p>

<table class=MsoNormalTable border=0 cellspacing=0 cellpadding=0
 style='border-collapse:collapse;mso-yfti-tbllook:1184;mso-padding-alt:0cm 0cm 0cm 0cm'>
 <tr style='mso-yfti-irow:0;mso-yfti-firstrow:yes'>
  <td width=657 valign=top style='width:492.8pt;padding:0cm 5.4pt 0cm 5.4pt'>
  <p class=MsoNormal style='margin-bottom:0cm;margin-bottom:.0001pt;text-align:
  justify;line-height:normal'>&nbsp;<span style='mso-no-proof:yes'><img
  border=0 width=656 height=282 id="_x0000_i1032"
  src="images/fr/image017.png"></span></p>
  </td>
 </tr>
 <tr style='mso-yfti-irow:1;mso-yfti-lastrow:yes'>
  <td width=657 valign=top style='width:492.8pt;padding:0cm 5.4pt 0cm 5.4pt'>
  <p class=MsoNormal align=center style='margin-bottom:0cm;margin-bottom:.0001pt;
  text-align:center;line-height:normal'><u><span style='font-size:8.0pt'>Illustration
  16</span></u></p>
  </td>
 </tr>
</table>

<p class=MsoNormal style='text-align:justify'>&nbsp;</p>

<p class=MsoNormal style='text-align:justify'>L’exemple ci-dessus (illustration
16) montre une matrice de champ dénormalisé servant à créer le champ
dénormalisé «&nbsp;Revit – Tension&nbsp;» dans le formulaire «&nbsp;Propriétés
électriques (alimentation principale). En observant la ligne du bas, on peut
apercevoir trois champs, à savoir;</p>

<p class=MsoListParagraph style='margin-left:2.0cm;text-align:justify;
text-indent:-21.25pt'><span style='font-family:Symbol'>·</span><span
style='font-size:7.0pt;line-height:115%;font-family:"Times New Roman",serif'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span>Préfixe&nbsp;: Champ servant à inscrire une chaine de caractères devant
se retrouver devant la valeur inscrite au champ tension <u>lorsque celle-ci
sera exportée</u>;</p>

<p class=MsoListParagraph style='margin-left:2.0cm;text-align:justify'>&nbsp;</p>

<p class=MsoListParagraph style='margin-left:2.0cm;text-align:justify;
text-indent:-21.25pt'><span style='font-family:Symbol'>·</span><span
style='font-size:7.0pt;line-height:115%;font-family:"Times New Roman",serif'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span>Champ&nbsp;: Champ normalisé associé au nouveau champ dénormalisé afin
de créer une correspondance. Cette correspondance permettra à DocMatic de
présenter l’information dénormalisée (visuellement dans le formulaire) à droite
de son champ normalisé, de manière à optimiser la conversion manuelle du format
(à la convenance de l’utilisateur);</p>

<p class=MsoListParagraph>&nbsp;</p>

<p class=MsoListParagraph style='margin-left:2.0cm;text-align:justify;
text-indent:-21.25pt'><span style='font-family:Symbol'>·</span><span
style='font-size:7.0pt;line-height:115%;font-family:"Times New Roman",serif'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span>Suffixe&nbsp;: Champ servant à inscrire une chaine de caractères devant
se retrouver après la valeur inscrite au champ tension <u>lorsque celle-ci sera
exportée</u>.</p>

<p class=MsoNormal style='text-align:justify'>&nbsp;</p>

<p class=MsoNormal style='margin-left:35.45pt;text-align:justify'>En résumé, il
sera possible d’importer (sans se soucier de la forme, des unités, etc.) les
valeurs de&nbsp;«&nbsp;Tension&nbsp;» dans le nouveau champ «&nbsp;Revit –
Tension&nbsp;», valeur qui se retrouvera associée (visuellement dans le formulaire)
au champ normalisé «&nbsp;Tension&nbsp;». Par la suite, il sera possible de
réexporter la valeur de tension normalisée en la dénormalisant à l’aide des
valeurs inscrites aux champs «&nbsp;préfixe&nbsp;» et suffixe&nbsp;». Ce type
de matrice peut également servir à <u>jumeler de l’information provenance de
plusieurs champs</u>, ou <u>composer des chaines de textes</u> tels les artères
d’alimentation souvent présentées sous la forme suivante&nbsp;: <u>2</u><span
class=GramE>x(</span><u>3#10 <span class=SpellE>cu</span> vert </u>+ <u>1#12 <span
class=SpellE>cu</span></u>, <u>C-41mm ø</u>). Pour ce faire, il suffit
d’ajouter des lignes à la matrice, de sélectionner les champs devant composer
la chaîne de texte et d’ajouter les préfixes et suffixes requis. Les espaces,
les lettres, les chiffres et les caractères spéciaux sont autorisés. Tel que
mentionné précédemment, votre nouveau champ dénormalisé pourra être utilisé
dans les matrices d’exportation à l’instar des autres champs.</p>

<p class=MsoNormal style='margin-left:35.45pt;text-align:justify'>&nbsp;</p>

<p class=MsoNormal>6.<span style='font-size:7.0pt;line-height:115%;font-family:
"Times New Roman",serif'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span><u>Procédure
d’importation des données de type «&nbsp;Propriétés spécifiques&nbsp;»&nbsp;:</u></p>

<p class=MsoNormal><o:p>&nbsp;</o:p></p>

<p class=MsoNormal style='text-align:justify'>Votre matrice est maintenant
créée et votre fichier de données (Ms Excel) contient tous les éléments requis.
Il ne reste maintenant qu’à lier ces deux composantes pour amorcer le processus
d’importation de données. Voici comment procéder;</p>

<p class=MsoListParagraph style='text-align:justify'>&nbsp;</p>

<p class=MsoListParagraph style='text-align:justify;text-indent:-18.0pt'>a)<span
style='font-size:7.0pt;line-height:115%;font-family:"Times New Roman",serif'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span>Allez dans l’onglet «&nbsp;Fiche&nbsp;» et activez le petit menu
déroulant latéral;</p>

<table class=MsoNormalTable border=0 cellspacing=0 cellpadding=0
 style='border-collapse:collapse;mso-yfti-tbllook:1184;mso-padding-alt:0cm 0cm 0cm 0cm'>
 <tr style='mso-yfti-irow:0;mso-yfti-firstrow:yes'>
  <td width=652 valign=top style='width:489.0pt;padding:0cm 5.4pt 0cm 5.4pt'>
  <p class=MsoNormal align=center style='margin-bottom:0cm;margin-bottom:.0001pt;
  text-align:center;line-height:normal'><span style='mso-no-proof:yes'><img
  border=0 width=222 height=301 id="_x0000_i1031"
  src="images/fr/image004.png"></span></p>
  </td>
 </tr>
 <tr style='mso-yfti-irow:1;mso-yfti-lastrow:yes'>
  <td width=652 valign=top style='width:489.0pt;padding:0cm 5.4pt 0cm 5.4pt'>
  <p class=MsoNormal align=center style='margin-bottom:0cm;margin-bottom:.0001pt;
  text-align:center;line-height:normal'><u><span style='font-size:8.0pt'>Illustration
  17</span></u></p>
  </td>
 </tr>
</table>

<p class=MsoNormal align=center style='margin-bottom:12.0pt;text-align:center'><span
style='font-size:8.0pt;line-height:115%'>&nbsp;</span></p>

<p class=MsoListParagraph style='text-align:justify;text-indent:-18.0pt'>b)<span
style='font-size:7.0pt;line-height:115%;font-family:"Times New Roman",serif'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span>Dans la section «&nbsp;Données&nbsp;», cliquez sur le lien
«&nbsp;Importer&nbsp;»;</p>

<p class=MsoListParagraph style='text-align:justify'>&nbsp;</p>

<p class=MsoListParagraph style='text-align:justify;text-indent:-18.0pt'>c)<span
style='font-size:7.0pt;line-height:115%;font-family:"Times New Roman",serif'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span>Retracez votre matrice dans la liste et cliquez sur l’icône d’importation
de fichier à l’extrême droite de la ligne;</p>

<table class=MsoNormalTable border=0 cellspacing=0 cellpadding=0
 style='margin-left:36.0pt;border-collapse:collapse;mso-yfti-tbllook:1184;
 mso-padding-alt:0cm 0cm 0cm 0cm'>
 <tr style='mso-yfti-irow:0;mso-yfti-firstrow:yes'>
  <td width=609 valign=top style='width:456.8pt;padding:0cm 5.4pt 0cm 5.4pt'>
  <p class=MsoListParagraph align=center style='margin:0cm;margin-bottom:.0001pt;
  text-align:center;line-height:normal'><span style='mso-no-proof:yes'><img
  border=0 width=530 height=210 id="_x0000_i1030"
  src="images/fr/image018.png"></span></p>
  </td>
 </tr>
 <tr style='mso-yfti-irow:1;mso-yfti-lastrow:yes'>
  <td width=609 valign=top style='width:456.8pt;padding:0cm 5.4pt 0cm 5.4pt'>
  <p class=MsoListParagraph align=center style='margin:0cm;margin-bottom:.0001pt;
  text-align:center;line-height:normal'><u><span style='font-size:8.0pt'>Illustration
  18</span></u></p>
  </td>
 </tr>
</table>

<p class=MsoNormal style='text-align:justify'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
<span style='font-size:8.0pt;line-height:115%'>&nbsp;&nbsp;&nbsp;&nbsp;</span></p>

<p class=MsoListParagraph style='text-align:justify;text-indent:-18.0pt'>d)<span
style='font-size:7.0pt;line-height:115%;font-family:"Times New Roman",serif'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span>Appuyez sur le bouton «&nbsp;Choisir un fichier&nbsp;», puis cliquez sur
valider une fois le fichier choisi;</p>

<p class=MsoListParagraph style='text-align:justify'>&nbsp;</p>

<div align=right>

<table class=MsoNormalTable border=0 cellspacing=0 cellpadding=0
 style='border-collapse:collapse;mso-yfti-tbllook:1184;mso-padding-alt:0cm 0cm 0cm 0cm'>
 <tr style='mso-yfti-irow:0;mso-yfti-firstrow:yes'>
  <td width=664 colspan=2 valign=top style='width:498.3pt;padding:0cm 5.4pt 0cm 5.4pt'>
  <p class=MsoListParagraph style='margin-bottom:0cm;margin-bottom:.0001pt;
  line-height:normal'><span style='mso-no-proof:yes'><img border=0 width=595
  height=199 id="_x0000_i1029" src="images/fr/image019.png"></span></p>
  </td>
  <td width=652 style='width:489.0pt;padding:0cm 0cm 0cm 0cm'>
  <p class=MsoNormal>&nbsp;</p>
  </td>
 </tr>
 <tr style='mso-yfti-irow:1'>
  <td width=664 colspan=2 valign=top style='width:498.3pt;padding:0cm 5.4pt 0cm 5.4pt'>
  <p class=MsoListParagraph style='margin-bottom:0cm;margin-bottom:.0001pt;
  line-height:normal'><span style='font-size:8.0pt'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
  <u>Illustration 19</u></span></p>
  </td>
  <td width=652 style='width:489.0pt;padding:0cm 0cm 0cm 0cm'>
  <p class=MsoNormal>&nbsp;</p>
  </td>
 </tr>
 <tr style='mso-yfti-irow:2'>
  <td width=652 style='width:489.0pt;padding:0cm 0cm 0cm 0cm'>
  <p class=MsoNormal>&nbsp;</p>
  </td>
  <td width=664 colspan=2 valign=top style='width:498.3pt;padding:0cm 5.4pt 0cm 5.4pt'>
  <p class=MsoNormal align=center style='margin-bottom:0cm;margin-bottom:.0001pt;
  text-align:center;line-height:normal'>&nbsp;</p>
  </td>
 </tr>
 <tr style='mso-yfti-irow:3;mso-yfti-lastrow:yes'>
  <td width=2184 style='width:1638.35pt;padding:0cm 0cm 0cm 0cm'></td>
  <td width=107 style='width:80.25pt;padding:0cm 0cm 0cm 0cm'></td>
  <td width=746 style='width:559.5pt;padding:0cm 0cm 0cm 0cm'></td>
 </tr>
</table>

</div>

<p class=MsoListParagraph><span style='font-size:8.0pt;line-height:115%'>&nbsp;</span></p>

<p class=MsoListParagraph><span style='font-size:8.0pt;line-height:115%'>&nbsp;</span></p>

<p class=MsoListParagraph style='text-align:justify;text-indent:-18.0pt'>e)<span
style='font-size:7.0pt;line-height:115%;font-family:"Times New Roman",serif'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span>En vous servant des menus déroulants, associez les composantes de votre
matrice aux colonnes ayant été détectées dans votre fichier de données par
l’outil d’importation (voir illustration 20). Le pivot doit impérativement être
associé à la colonne qui contient les codes des locaux, équipements,
instruments ou véhicules selon la matrice. Comme vous pourrez le constater, il
n’est possible d’associer une colonne du fichier d’importation qu’à un seul
champ DocMatic. Ainsi, si vous sélectionnez la même colonne pour un second
champ, la première association sera détruite. Cette étape d’association offre
l’opportunité de sélectionner le ou les champs que l’on désire traiter sans
avoir à modifier la matrice d’importation. Vous pourrez donc importer vos
données, item par item, de manière à garder un meilleur contrôle.</p>

<table class=MsoNormalTable border=0 cellspacing=0 cellpadding=0
 style='border-collapse:collapse;mso-yfti-tbllook:1184;mso-padding-alt:0cm 0cm 0cm 0cm'>
 <tr style='mso-yfti-irow:0;mso-yfti-firstrow:yes'>
  <td width=657 valign=top style='width:492.8pt;padding:0cm 5.4pt 0cm 5.4pt'>
  <p class=MsoNormal align=center style='margin-bottom:0cm;margin-bottom:.0001pt;
  text-align:center;line-height:normal'>&nbsp;</p>
  <p class=MsoNormal align=center style='margin-bottom:0cm;margin-bottom:.0001pt;
  text-align:center;line-height:normal'><span style='mso-no-proof:yes'><img
  border=0 width=648 height=273 id="_x0000_i1028"
  src="images/fr/image020.png"></span></p>
  </td>
 </tr>
 <tr style='mso-yfti-irow:1;mso-yfti-lastrow:yes'>
  <td width=657 valign=top style='width:492.8pt;padding:0cm 5.4pt 0cm 5.4pt'>
  <p class=MsoNormal align=center style='margin-bottom:0cm;margin-bottom:.0001pt;
  text-align:center;line-height:normal'><u><span style='font-size:8.0pt'>Illustration
  20</span></u></p>
  <p class=MsoNormal align=center style='margin-bottom:0cm;margin-bottom:.0001pt;
  text-align:center;line-height:normal'><span style='font-size:8.0pt'>&nbsp;</span></p>
  <p class=MsoNormal align=center style='margin-bottom:0cm;margin-bottom:.0001pt;
  text-align:center;line-height:normal'>&nbsp;</p>
  </td>
 </tr>
</table>

<p class=MsoListParagraph style='margin-left:35.45pt;text-align:justify;
text-indent:-18.0pt'>f)<span style='font-size:7.0pt;line-height:115%;
font-family:"Times New Roman",serif'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span>L’illustration suivante indique que les fiches des équipements visés par
l’importation en cours ne sont pas créées dans la base de données DocMatic. Tel
que vu précédemment, il est possible de créer automatiquement toutes les fiches
requises en cliquant sur le lien «&nbsp;Ajouter tous les éléments au registre
DocMatic&nbsp;» au-dessus de la liste. Si aucune fiche n’est manquante, passez
à l’étape suivante.</p>

<p class=MsoListParagraph style='margin-left:35.45pt;text-align:justify'>&nbsp;</p>

<table class=MsoNormalTable border=0 cellspacing=0 cellpadding=0
 style='border-collapse:collapse;mso-yfti-tbllook:1184;mso-padding-alt:0cm 0cm 0cm 0cm'>
 <tr style='mso-yfti-irow:0;mso-yfti-firstrow:yes'>
  <td width=657 valign=top style='width:492.8pt;padding:0cm 5.4pt 0cm 5.4pt'>
  <p class=MsoNormal align=center style='margin-bottom:0cm;margin-bottom:.0001pt;
  text-align:center;line-height:normal'><span style='mso-no-proof:yes'><img
  border=0 width=402 height=351 id="_x0000_i1027"
  src="images/fr/image021.png"></span></p>
  </td>
 </tr>
 <tr style='mso-yfti-irow:1;mso-yfti-lastrow:yes'>
  <td width=657 valign=top style='width:492.8pt;padding:0cm 5.4pt 0cm 5.4pt'>
  <p class=MsoNormal align=center style='margin-bottom:0cm;margin-bottom:.0001pt;
  text-align:center;line-height:normal'><u><span style='font-size:8.0pt'>Illustration
  21</span></u></p>
  <p class=MsoNormal align=center style='margin-bottom:0cm;margin-bottom:.0001pt;
  text-align:center;line-height:normal'><span style='font-size:8.0pt'>&nbsp;</span></p>
  <p class=MsoNormal align=center style='margin-bottom:0cm;margin-bottom:.0001pt;
  text-align:center;line-height:normal'>&nbsp;</p>
  </td>
 </tr>
</table>

<p class=MsoListParagraph style='text-align:justify;text-indent:-18.0pt'>g)<span
style='font-size:7.0pt;line-height:115%;font-family:"Times New Roman",serif'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span>Une fois les fiches créées, l’outil d’importation nous indique la liste
des erreurs de concordance (voir illustration ci-dessous), c'est-à-dire la
liste de toutes les divergences d’information relevées entre les valeurs
inscrites dans DocMatic et les valeurs contenues dans le fichier d’importation
(Ms Excel).</p>

<table class=MsoNormalTable border=0 cellspacing=0 cellpadding=0
 style='border-collapse:collapse;mso-yfti-tbllook:1184;mso-padding-alt:0cm 0cm 0cm 0cm'>
 <tr style='mso-yfti-irow:0;mso-yfti-firstrow:yes'>
  <td width=642 valign=top style='width:481.25pt;padding:0cm 5.4pt 0cm 5.4pt'>
  <p class=MsoNormal align=center style='margin-bottom:0cm;margin-bottom:.0001pt;
  text-align:center;line-height:normal'><span style='font-size:8.0pt'>&nbsp;</span></p>
  <p class=MsoNormal align=center style='margin-bottom:0cm;margin-bottom:.0001pt;
  text-align:center;line-height:normal'><span style='mso-no-proof:yes'><img
  border=0 width=474 height=412 id="_x0000_i1026"
  src="images/fr/image022.png"></span></p>
  </td>
 </tr>
 <tr style='mso-yfti-irow:1;mso-yfti-lastrow:yes'>
  <td width=642 valign=top style='width:481.25pt;padding:0cm 5.4pt 0cm 5.4pt'>
  <p class=MsoNormal align=center style='margin-bottom:0cm;margin-bottom:.0001pt;
  text-align:center;line-height:normal'><u><span style='font-size:8.0pt'>Illustration
  22</span></u></p>
  </td>
 </tr>
</table>

<p class=MsoNormal style='text-align:justify'>&nbsp;</p>

<p class=MsoNormal style='margin-left:35.45pt;text-align:justify'>Il est
possible de conserver l’information présente dans DocMatic ou de la remplacer
par la donnée contenue dans le fichier d’importation. Pour ce faire, vous
n’avez qu’à cliquer à la pièce (ligne par ligne) sur l’un ou l’autre des <u>crochets</u>
(correspondant à la donnée DocMatic ou à la donnée importée) ou encore, cliquer
sur <u>tout conserver</u> ou <u>tout importer</u> pour effectuer l’importation
complète d’un seul clic de souris.</p>

<p class=MsoNormal style='margin-left:35.45pt;text-align:justify'>En observant
l’illustration de plus près, nous pouvons aussi constater la présence de petits
triangles rouges. En passant votre souris au-dessus de ces triangles, un avis à
l’utilisateur vous sera affiché. Dans le cas présent, DocMatic nous informe de
l’absence de l’unité de mesure dans l’information en voie d’importation. La
donnée est importable automatiquement mais un avis à l’utilisateur est affiché
pour informer les usagers d’un potentiel risque d’erreur.</p>

<p class=MsoNormal style='margin-left:35.45pt;text-align:justify'>À ce titre,
l’outil d’importation accompli une panoplie de vérifications et procèdera même
parfois à quelques ajustements automatiques afin d’uniformiser l’information de
la manière la plus efficace possible sans risquer de corrompre la donnée.</p>

<p class=MsoNormal style='margin-left:35.45pt;text-align:justify'>Voici
quelques-unes des tâches effectuées pendant le processus d’importation;</p>

<p class=MsoListParagraph style='margin-left:3.0cm;text-align:justify;
text-indent:-14.15pt'><span style='font-family:Symbol'>·</span><span
style='font-size:7.0pt;line-height:115%;font-family:"Times New Roman",serif'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span>Détection et affichage de la liste des doublons dans les données
fondamentales (pivots);</p>

<p class=MsoListParagraph style='margin-left:3.0cm;text-align:justify;
text-indent:-14.15pt'><span style='font-family:Symbol'>·</span><span
style='font-size:7.0pt;line-height:115%;font-family:"Times New Roman",serif'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span>Uniformisation des unités de mesures (espacement, minuscules,
majuscules, etc.);</p>

<p class=MsoListParagraph style='margin-left:3.0cm;text-align:justify;
text-indent:-14.15pt'><span style='font-family:Symbol'>·</span><span
style='font-size:7.0pt;line-height:115%;font-family:"Times New Roman",serif'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span>Uniformisation des points vs virgules comme délimiteur de décimale;</p>

<p class=MsoListParagraph style='margin-left:3.0cm;text-align:justify;
text-indent:-14.15pt'><span style='font-family:Symbol'>·</span><span
style='font-size:7.0pt;line-height:115%;font-family:"Times New Roman",serif'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span>Détection de fautes de frappe (noms apparents);</p>

<p class=MsoListParagraph style='margin-left:3.0cm;text-align:justify;
text-indent:-14.15pt'><span style='font-family:Symbol'>·</span><span
style='font-size:7.0pt;line-height:115%;font-family:"Times New Roman",serif'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span>Détection de changement de code, basé sur la correspondance de <span
class=SpellE>id_externes</span>;</p>

<p class=MsoListParagraph style='margin-left:3.0cm;text-align:justify;
text-indent:-14.15pt'><span style='font-family:Symbol'>·</span><span
style='font-size:7.0pt;line-height:115%;font-family:"Times New Roman",serif'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span>Autres fonctions du genre.</p>

<p class=MsoListParagraph style='margin-left:3.0cm;text-align:justify'>&nbsp;</p>

<p class=MsoListParagraph style='text-align:justify;text-indent:-18.0pt'>h)<span
style='font-size:7.0pt;line-height:115%;font-family:"Times New Roman",serif'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span>Il est maintenant possible de constater la présence des données
importées dans les formulaires en consultant les fiches impactées (voir
illustration 23).</p>

<p class=MsoListParagraph style='text-align:justify'>&nbsp;</p>

<table class=MsoNormalTable border=0 cellspacing=0 cellpadding=0
 style='border-collapse:collapse;mso-yfti-tbllook:1184;mso-padding-alt:0cm 0cm 0cm 0cm'>
 <tr style='mso-yfti-irow:0;mso-yfti-firstrow:yes'>
  <td width=652 valign=top style='width:489.0pt;padding:0cm 5.4pt 0cm 5.4pt'>
  <p class=MsoNormal style='margin-bottom:0cm;margin-bottom:.0001pt;text-align:
  justify;line-height:normal'><span style='mso-no-proof:yes'><img border=0
  width=663 height=367 id="_x0000_i1025"
  src="images/fr/image023.png"></span></p>
  </td>
 </tr>
 <tr style='mso-yfti-irow:1;mso-yfti-lastrow:yes'>
  <td width=652 valign=top style='width:489.0pt;padding:0cm 5.4pt 0cm 5.4pt'>
  <p class=MsoNormal align=center style='margin-bottom:0cm;margin-bottom:.0001pt;
  text-align:center;line-height:normal'><u><span style='font-size:8.0pt'>Illustration
  22</span></u></p>
  </td>
 </tr>
</table>

<p class=MsoNormal style='text-align:justify'>&nbsp;</p>

<p class=MsoNormal>7.<span style='font-size:7.0pt;line-height:115%;font-family:
"Times New Roman",serif'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span><u>Traitement
des données interdépendantes (champs maîtres-esclaves)&nbsp;:</u></p>

<p class=MsoListParagraph>&nbsp;</p>

<p class=MsoListParagraph style='text-indent:-18.0pt'>a)<span style='font-size:
7.0pt;line-height:115%;font-family:"Times New Roman",serif'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span><u>Définition des champs interdépendants&nbsp;: </u></p>

<p class=MsoNormal style='margin-left:49.65pt;text-align:justify'>Les champs
interdépendants sont des champs soumis à une interrelation
«&nbsp;maître-esclave&nbsp;». La liste des valeurs possibles dans le champ esclave
dépend invariablement de la valeur présente dans le champ maître. Ainsi, si la
valeur du champ maître est modifiée, la valeur du champ esclave doit
obligatoirement être révisée.</p>

<p class=MsoNormal style='margin-left:49.65pt;text-align:justify'>Dans un
contexte d’importation de données où l’optimisation des procédures
informatiques présuppose un maximum d’automatismes, la ligne de conduite est
très mince. Il devient rapidement nécessaire d’établir des stratégies
d’intégration pour préserver l’intégrité des données tout en conservant un
certain niveau de performance.</p>

<p class=MsoListParagraph style='text-indent:-18.0pt'>b)<span style='font-size:
7.0pt;line-height:115%;font-family:"Times New Roman",serif'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</span><u>Stratégie de traitement des données maître-esclave processus
d’importation&nbsp;: </u></p>

<p class=MsoNormal style='margin-left:49.65pt;text-align:justify'>Afin
d’utiliser l’outil d’importation de la manière la plus optimale, il sera requis
de bien assimiler les stratégies programmées suivantes. Afin de simplifier le
texte suivant, nous utiliseront les lettre «&nbsp;<b>M</b>&nbsp;» pour <span
class=SpellE>donnée</span> maître et «&nbsp;<b>E</b>&nbsp;» pour <span
class=SpellE>donnée</span> esclave. Aussi, nous utiliserons le terme «&nbsp;<b>1</b>&nbsp;»
pour présence et «&nbsp;<b>0</b>&nbsp;» pour absence. </p>

<p class=MsoListParagraph style='margin-left:3.0cm;text-indent:-14.15pt'><span
style='font-family:Symbol'>·</span><span style='font-size:7.0pt;line-height:
115%;font-family:"Times New Roman",serif'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span>Importation
de <b>M</b> lorsque <b>E</b> = <b>0</b></p>

<p class=MsoListParagraph style='margin-left:3.0cm'>&nbsp;</p>

<p class=MsoListParagraph style='margin-left:3.0cm;text-indent:21.3pt'>L’importation
se fait correctement <span class=GramE>avec&nbsp; le</span> <u>crochet</u> ou
la <u>manière automatique</u></p>

<p class=MsoListParagraph style='margin-left:3.0cm;text-indent:21.3pt'>&nbsp;</p>

<p class=MsoListParagraph style='margin-left:3.0cm;text-indent:-14.15pt'><span
style='font-family:Symbol'>·</span><span style='font-size:7.0pt;line-height:
115%;font-family:"Times New Roman",serif'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span>Importation
de <b>M</b> lorsque <b>E</b> = <b>1</b></p>

<p class=MsoNormal style='margin-top:0cm;margin-right:0cm;margin-bottom:0cm;
margin-left:106.4pt;margin-bottom:.0001pt;line-height:normal;background:white'><u><span
style='font-size:10.0pt'>Donne le message suivant&nbsp;:</span></u><span
style='font-size:10.0pt'> <i><span style='color:black'>&quot;Attention!
DocMatic ne peut importer cette donnée parce que les informations inscrites
au(x) champ(s) qui en dépend(<span class=SpellE>ent</span>) deviendrai(en)t
erronée(s)</span></i><span style='color:black'> <i>Astuce! Servez-vous de la
loupe pour traiter le problème directement via le formulaire.&quot;.</i></span></span></p>

<p class=MsoNormal style='margin-top:0cm;margin-right:0cm;margin-bottom:0cm;
margin-left:106.4pt;margin-bottom:.0001pt;line-height:normal;background:white'>&nbsp;</p>

<p class=MsoListParagraph style='margin-left:3.0cm;text-indent:-14.15pt'><span
style='font-family:Symbol'>·</span><span style='font-size:7.0pt;line-height:
115%;font-family:"Times New Roman",serif'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span>Importation
de <b>E</b> lorsque <b>M</b> = <b>1 </b>mais que <b>E </b>reste valide selon <b>M</b></p>

<p class=MsoListParagraph style='margin-left:3.0cm'>&nbsp;</p>

<p class=MsoListParagraph style='margin-left:3.0cm;text-indent:21.3pt'>L’importation
se fait correctement <span class=GramE>ave&nbsp; le</span> <u>crochet</u> ou la
<u>manière automatique</u></p>

<p class=MsoListParagraph style='margin-left:3.0cm'>&nbsp;</p>

<p class=MsoListParagraph style='margin-left:3.0cm;text-indent:-14.15pt'><span
style='font-family:Symbol'>·</span><span style='font-size:7.0pt;line-height:
115%;font-family:"Times New Roman",serif'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span>Importation
de <b>E</b> si <b>M</b> = <b>1</b> et que <b>E</b> devient invalide selon <b>M</b></p>

<p class=MsoNormal style='margin-top:0cm;margin-right:0cm;margin-bottom:0cm;
margin-left:106.4pt;margin-bottom:.0001pt;line-height:normal;background:white'><u><span
style='font-size:10.0pt'>Donne le message suivant&nbsp;:</span></u><span
style='font-size:10.0pt'> <span style='color:black;background:white'>&quot;Attention!
Cette donnée ne peut être importée telle quelle parce que sa valeur ne concorde
pas avec la valeur inscrite au champ&nbsp;dont elle dépend. Astuce! Servez-vous
de la loupe pour traiter le problème directement via le formulaire.&quot;</span></span></p>

<p class=MsoNormal style='margin-top:0cm;margin-right:0cm;margin-bottom:0cm;
margin-left:106.4pt;margin-bottom:.0001pt;line-height:normal;background:white'><span
style='font-size:12.0pt;color:black;background:white'>&nbsp;</span></p>

<p class=MsoNormal style='margin-left:21.3pt;text-align:justify'>À la lecture
de ces informations, nous pouvons donc conclure que la meilleure stratégie
concernant l’importation des données inhérentes aux champs interdépendants est
la suivante;</p>

<p class=MsoNormal style='margin-left:21.3pt;text-align:justify;text-indent:
14.15pt'><u>Méthode régulière&nbsp;:</u></p>

<p class=MsoListParagraph style='margin-left:144.0pt;text-align:justify;
text-indent:-18.0pt'>1.<span style='font-size:7.0pt;line-height:115%;
font-family:"Times New Roman",serif'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span>N’importez
que les données <b>M</b> dans un premier temps</p>

<p class=MsoListParagraph style='margin-left:144.0pt;text-align:justify;
text-indent:-18.0pt'>2.<span style='font-size:7.0pt;line-height:115%;
font-family:"Times New Roman",serif'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span>Poursuivre
avec l’importation des données <b>E</b></p>

<p class=MsoListParagraph style='margin-left:144.0pt;text-align:justify;
text-indent:-18.0pt'>3.<span style='font-size:7.0pt;line-height:115%;
font-family:"Times New Roman",serif'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span>Traitez
les cas problématiques manuellement (via la loupe)</p>

<p class=MsoListParagraph style='margin-left:144.0pt;text-align:justify'>&nbsp;</p>

<p class=MsoNormal style='margin-left:21.3pt;text-align:justify'>Dans
l’éventualité où vous opteriez pour une stratégie plus&nbsp;«&nbsp;agressive&nbsp;»
permettant de limiter les interventions manuelles, il serait possible de
procéder comme suit;</p>

<p class=MsoNormal style='margin-left:21.3pt;text-align:justify;text-indent:
14.15pt'><u>Méthode agressive&nbsp;:</u></p>

<p class=MsoListParagraph style='margin-left:144.0pt;text-align:justify;
text-indent:-18.0pt'>1.<span style='font-size:7.0pt;line-height:115%;
font-family:"Times New Roman",serif'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span>Dans
un premier temps, importez des <u>valeurs nulles</u> dans le champ <b>E</b> en
associant (par exemple) une colonne vide du fichier d’importation (la colonne
choisie devra posséder un titre pour être associable). Ceci aura pour effet
d’éliminer la contrainte liée à la valeur du paramètre esclave «&nbsp;<b>E&nbsp;</b>».
<u>Il faut <span class=GramE>par contre</span> avoir une source d’information
complète pour pouvoir remettre les valeurs<b> E</b> en place à la fin du
présent processus;</u></p>

<p class=MsoListParagraph style='margin-left:144.0pt;text-align:justify;
text-indent:-18.0pt'>2.<span style='font-size:7.0pt;line-height:115%;
font-family:"Times New Roman",serif'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span>Importez
toutes les valeurs <b>M;</b></p>

<p class=MsoListParagraph style='margin-left:144.0pt;text-align:justify;
text-indent:-18.0pt'>3.<span style='font-size:7.0pt;line-height:115%;
font-family:"Times New Roman",serif'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span>Terminez
avec la réimportation des valeurs <b>E. </b></p>

<p class=MsoNormal>8.<span style='font-size:7.0pt;line-height:115%;font-family:
"Times New Roman",serif'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span><u>Conclusion&nbsp;:</u></p>

<p class=MsoNormal>&nbsp;</p>

<p class=MsoNormal style='margin-left:49.65pt;text-align:justify'>Ceci complète
la documentation concernant les fonctions d’importation de la plateforme
DocMatic. Pour toutes questions supplémentaires concernant l’importation des
données ou sur la gestion des matrices, veuillez s’il vous plaît communiquer
avec nous à l’adresse courriel suivante&nbsp;: <span class=MsoHyperlink><a
href="<EMAIL>"><EMAIL></a></span></p>

<p class=MsoListParagraph style='margin-left:144.0pt;text-align:justify'>&nbsp;</p>

<p class=MsoNormal align=center style='margin-left:52.9pt;text-align:center'>----------------------------</p>

<p class=MsoNormal style='margin-left:52.9pt;text-align:justify'>&nbsp;</p>

<p class=MsoNormal style='margin-left:35.45pt;text-align:justify'>&nbsp;</p>

</div>

</body>

</html>

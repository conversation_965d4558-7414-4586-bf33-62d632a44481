<?
chdir('../');
$doNotCheckLogin=1;
include('config/inc.php');

try {
	
	$projet_id = isset($_GET['projet_id']) ? intval($_GET['projet_id']) : -1;
	$headers = ApiUtils::getRequestHeaders();
	$api_key = $headers['DOCMATIC-API-KEY'];

	if(isset($api_key) && $api_key == $API_keys[$CodeClient]) 
	{
		if($projet_id == -1)
		{
			$response = '{"error":{"text": "Le paramètre projet_id est manquant." }}';
		}
		else
		{
			$projet = new Projets($projet_id);
			if(!isset($projet))
			{
				$response = '{"error":{"text": "Ce projet n\'existe pas." }}';
			}
			else
			{
				$response_array = array();
				$formulaires_tmp = Formulaires::getListe(array('projet_id' => $projet_id));

				$formulaires_objects = array();
				$formulaires = array();
				foreach($formulaires_tmp as $i => $row)
				{
					$className = $row->nom;

					$type = '';
					if(isset($row->fiche_type) && dm_strlen($row->fiche_type) > 0)
					{
						$type = ' ('.$row->fiche_type.')';
					}

					if(isset($row->fiche_sous_type) && dm_strlen($row->fiche_sous_type) > 0)
					{
						$type = ' ('.$row->fiche_sous_type.')';
					}

					$formulaires[$row->id] = $className::TITRE.$type;
					$formulaires_objects[$row->id] = $row;
				}

				uasort ($formulaires, 'custom_sort');
				foreach($formulaires as $id => $titre)
				{
					$formulaire = $formulaires_objects[$id];

					$class_name = $formulaire->nom;
					$metas = $class_name::getFlatChampsSaisieMetaData();
					if(isset($metas) && is_array($metas))
					{
						$champs = array();
						foreach($metas as $key => $meta)
						{
							$champs[] = array(
								'id' => $key,
								'label' => $meta['titre'],
							);
						}
						$response_array[] = array(
							'id' => $formulaire->id,
							'label' => $titre,
							'champs' => $champs
						);
					}
				}
				$response = json_encode($response_array);
			}
		}
	} 
	else 
	{
		$response = '{"error":{"text": "La clé de l\'api est invalide" }}';
	}

	header('Content-type: application/json');
	print $response;
	
} catch(Exception $e) {
	
	echo '{"error":{"text": Une erreur interne s\'est produite.}}';
	envoiCourriel("<EMAIL>", "Erreur: docmatic API GetFormsInfo", $e->getMessage());
		
}
<?php

class AccessoiresSanitairesEnsembles extends FicheFormWrapper {
	private static $CLASS_NAME='AccessoiresSanitairesEnsembles';
	const SQL_TABLE_NAME='accessoires_sanitaires_ensembles';
	const TITRE='Ensemble d\'accessoires sanitaires';
	public $id;
	public $fiche_id;
	public $no;
	public $accessoire_sanitaire_ensemble_type_id;
	public $accessoire_sanitaire_ensemble_type;
	public $qte_requise;
	public $quantite;
	public $commentaires;
    
    public static function getChampsSaisieMetaData()
    {
        return array(
            
            'accessoire_sanitaire_ensemble_type' => array(
                'classe'      	=> 'AccessoiresSanitairesEnsemblesTypes',
                'table'      	=> 'accessoires_sanitaires_ensembles_types',
                'champ_id'      => 'accessoire_sanitaire_ensemble_type_id',
                'type'          => 'select',
                'format'        => 'int',
                'titre'         => txt('Ensemble d\'accessoire', false),
                'css_editeur'   => 'large',
                'obligatoire'	=> 1,
            ),
            
            'qte_requise' => array(
				'classe'      	=> '',
				'table'      	=> '',
				'champ_id'      => 'qte_requise',
				'type'          => 'input',
				'format'        => 'int',
				'titre'         => txt('Quantité', false),
				'css_editeur'   => 'tiny'
			),
            
            'commentaires' => array(
				'classe'      	=> '',
				'table'      	=> '',
				'champ_id'      => 'commentaires',
				'type'          => 'input',
                'format'        => 'text',
                'titre'         => txt('Commentaires', false),
                'css_editeur'   => 'xxlarge'
            ),
            
        );
    }
    
    public static function getListe($filter = array(), $orderBy = 'ordre', $extra_data = array()) 
	{
        $extra_data = array(
            'accessoire_sanitaire_ensemble_type' => array('table' => 'accessoires_sanitaires_ensembles_types'),
            
        );
        return parent::getListe($filter, $orderBy, $extra_data);
    }
}
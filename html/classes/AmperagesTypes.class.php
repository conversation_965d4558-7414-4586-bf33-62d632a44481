<?

class AmperagesTypes extends ListClassWrapper {
	private static $CLASS_NAME='AmperagesTypes';
	const SQL_TABLE_NAME='amperages_types';
	const SORT_NUMERICALLY=true;
	public $id;
	public $nom;

	public static function getChampsSaisieMetaData()
	{
		return array('nom' => array( 'classe' => '', 'table' => '', 'champ_id' => 'nom', 'type' => 'input', 'format' => 'text', 'titre' => txt('Nom', false), 'css_editeur' => 'large'));
	}
}
<?php

class AppareilsChauffages extends FicheFormWrapper {
	private static $CLASS_NAME='AppareilsChauffages';
	const SQL_TABLE_NAME='appareils_chauffages';
	const TITRE='Appareils de chauffage';
	public $id;
	public $fiche_id;
	public $appareil_chauffage_type_id;
	public $alimentation_electrique_type_id;
	public $installation_type_id;
	public $prise_electrique_grade_id;
	public $qte_requise;
	public $commentaires;
    
    public static function getChampsSaisieMetaData()
    {
        return array(
            
            'appareil_chauffage_type' => array(
                'classe'      	=> 'AppareilsChauffagesTypes',
                'table'      	=> 'appareils_chauffages_types',
                'champ_id'      => 'appareil_chauffage_type_id',
                'type'          => 'select',
                'format'        => 'int',
                'titre'         => txt('Type d\'appareil', false),
                'css_editeur'   => 'average',
                'obligatoire'	=> 1,
            ),
            
            'alimentation_electrique_type' => array(
                'classe'      	=> 'AlimentationsElectriquesTypes',
                'table'      	=> 'alimentations_electriques_types',
                'champ_id'      => 'alimentation_electrique_type_id',
                'type'          => 'select',
                'format'        => 'int',
                'titre'         => txt('Alimentation', false),
                'css_editeur'   => 'average'
            ),
            
            'installation_type' => array(
				'classe'      	=> 'InstallationsTypes',
				'table'      	=> 'installations_types',
                'champ_id'      => 'installation_type_id',
                'type'          => 'select',
                'format'        => 'int',
                'titre'         => txt('Installation', false),
                'css_editeur'   => 'average'
            ),
            
            'prise_electrique_grade' => array(
                'classe'      	=> 'PrisesElectriquesGrades',
                'table'      	=> 'prises_electriques_grades',
                'champ_id'      => 'prise_electrique_grade_id',
                'type'          => 'select',
                'format'        => 'int',
                'titre'         => txt('Grade', false),
                'css_editeur'   => 'medium'
            ),
            
            'qte_requise' => array(
				'classe'      	=> '',
				'table'      	=> '',
				'champ_id'      => 'qte_requise',
				'type'          => 'input',
				'format'        => 'int',
				'titre'         => txt('Quantité', false),
				'css_editeur'   => 'tiny'
			),
            
            'commentaires' => array(
				'table'      	=> '',
				'champ_id'      => 'commentaires',
				'type'          => 'input',
                'format'        => 'text',
                'titre'         => txt('Commentaires', false),
                'css_editeur'   => 'large'
            ),
            
        );
    }
}
<?php

class AppelsLocauxGeneraux extends FicheFormWrapper {
	private static $CLASS_NAME='AppelsLocauxGeneraux';
	const SQL_TABLE_NAME='appels_locaux_generaux';
	const TITRE='Équipements d\'appels locaux et généraux';
	public $id;
	public $fiche_id;
	public $appel_locaux_generaux_equipement_type_id;
	public $installation_type_id;
	public $appel_locaux_generaux_type_id;
	public $appel_locaux_generaux_bloc_id;
	public $appel_locaux_generaux_etage_id;
	public $appel_locaux_generaux_zone_id;
	public $qte_requise;
	public $appel_locaux_generaux_equipement_type;
	public $installation_type;
	public $appel_locaux_generaux_type;
	public $appel_locaux_generaux_bloc;
	public $appel_locaux_generaux_etage;
	public $appel_locaux_generaux_zone;
	public $quantite;
	public $commentaires;
	
    public static function getChampsSaisieMetaData()
    {
        return array(
            
            'appel_locaux_generaux_equipement_type' => array(
                'classe'      	=> 'AppelsLocauxGenerauxEquipementsTypes',
                'table'      	=> 'appels_locaux_generaux_equipements_types',
                'champ_id'      => 'appel_locaux_generaux_equipement_type_id',
                'type'          => 'select',
                'format'        => 'int',
                'titre'         => txt('Média', false),
                'css_editeur'   => 'large',
                'obligatoire'	=> 1,
            ),
            
            'installation_type' => array(
				'classe'      	=> 'InstallationsTypes',
				'table'      	=> 'installations_types',
                'champ_id'      => 'installation_type_id',
                'type'          => 'select',
                'format'        => 'int',
                'titre'         => txt('Installation', false),
                'css_editeur'   => 'medium'
            ),
            
            'appel_locaux_generaux_type' => array(
                'classe'      	=> 'AppelsLocauxGenerauxTypes',
                'table'      	=> 'appels_locaux_generaux_types',
                'champ_id'      => 'appel_locaux_generaux_type_id',
                'type'          => 'select',
                'format'        => 'int',
                'titre'         => txt('Type d\'appel', false),
                'css_editeur'   => 'average'
            ),
            
            'appel_locaux_generaux_bloc' => array(
                'classe'      	=> 'AppelsLocauxGenerauxBlocs',
                'table'      	=> 'appels_locaux_generaux_blocs',
                'champ_id'      => 'appel_locaux_generaux_bloc_id',
                'type'          => 'select',
                'format'        => 'int',
                'titre'         => txt('Bloc', false),
                'css_editeur'   => 'small'
            ),
            
            'appel_locaux_generaux_etage' => array(
                'classe'      	=> 'AppelsLocauxGenerauxEtages',
                'table'      	=> 'appels_locaux_generaux_etages',
                'champ_id'      => 'appel_locaux_generaux_etage_id',
                'type'          => 'select',
                'format'        => 'int',
                'titre'         => txt('Étage', false),
                'css_editeur'   => 'small'
            ),
            
            'appel_locaux_generaux_zone' => array(
                'classe'      	=> 'AppelsLocauxGenerauxZones',
                'table'      	=> 'appels_locaux_generaux_zones',
                'champ_id'      => 'appel_locaux_generaux_zone_id',
                'type'          => 'select',
                'format'        => 'int',
                'titre'         => txt('Zone', false),
                'css_editeur'   => 'small'
            ),
            
            'qte_requise' => array(
				'classe'      	=> '',
				'table'      	=> '',
				'champ_id'      => 'qte_requise',
				'type'          => 'input',
				'format'        => 'int',
				'titre'         => txt('Quantité', false),
				'css_editeur'   => 'tiny'
			),
            
            'commentaires' => array(
				'table'      	=> '',
				'champ_id'      => 'commentaires',
				'type'          => 'input',
                'format'        => 'text',
                'titre'         => txt('Commentaires', false),
                'css_editeur'   => 'average'
            ),
            
        );
    }

	public static function getListe($filter = array(), $orderBy = 'ordre', $extra_data = array())
	{
        $extra_data = array(
            'appel_locaux_generaux_equipement_type' => array('table' => 'appels_locaux_generaux_equipements_types'),
            'installation_type' => array('table' => 'installations_types'),
            'appel_locaux_generaux_type' => array('table' => 'appels_locaux_generaux_types'),
            'appel_locaux_generaux_bloc' => array('table' => 'appels_locaux_generaux_blocs'),
            'appel_locaux_generaux_etage' => array('table' => 'appels_locaux_generaux_etages'),
            'appel_locaux_generaux_zone' => array('table' => 'appels_locaux_generaux_zones'),
            
        );
        return parent::getListe($filter, $orderBy, $extra_data);
    }
}

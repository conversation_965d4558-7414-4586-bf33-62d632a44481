<?php

class AppelsLocauxGenerauxBlocs extends ListClassWrapper {
	private static $CLASS_NAME='AppelsLocauxGenerauxBlocs';
	const SQL_TABLE_NAME='appels_locaux_generaux_blocs';
	const CAN_COMPARE=false;
	public $id;
	public $batiment_id;
	public $nom;
	public $batiment;
	public $isValide;
	public $code_client_ajout;
	public $usager_ajout_id;
	public $ts_ajout;
	public $code_ajout;
	public $ts_valide;
	public $note_ajout;
	public $ts_sync;
	public $ts_delete;

	public static function getChampsSaisieMetaData()
	{
		return array(
            
            'nom' => array( 
                'classe' => '', 
                'table' => '', 
                'champ_id' => 'nom', 
                'type' => 'input', 
                'format' => 'text', 
                'titre' => txt('Nom', false), 
                'css_editeur'   => 'large',
            ),
			
		);
	}
	
	protected function setDataForId($id)
	{
        global $db;
		$sql = "select *, (select nom from batiments where id = batiment_id) as batiment,
				case when ts_valide is null then 0 else 1 end as is<PERSON><PERSON><PERSON>
                from ".self::SQL_TABLE_NAME." 
                where id = ?";
		$row = $db->getRow($sql, array($id));
		
		$this->populateThisWithThat($row);
	}
	
	public static function getListe($filter = array(), $orderBy = 'nom', $extra_data = array())
	{
        global $db, $db_name;
        
        $db_name_to_use = $db_name;
        if(isset($filter['forced_bd']))
        {
            $db_name_to_use = $filter['forced_bd'];
        }
        
        $where = '';
        $data = array();
        
        if(isset($filter['fiche_id']))
        {
            $where .= ' and batiment_id = (select batiment_id from '.$db_name_to_use.'.fiches where id = ?)';
            $data[] = intval($filter['fiche_id']);
        }
        
        if(getProjetId() > 0)
        {
            $where .= ' and batiment_id in (select id from '.$db_name_to_use.'.batiments where ouvrage_id in (select ouvrage_id from '.$db_name_to_use.'.projets where id = ?))';
            $data[] = getProjetId();
        }
        else if(getOuvrageId() > 0)
        {
            $where .= ' and batiment_id in (select id from '.$db_name_to_use.'.batiments where ouvrage_id = ?)';
            $data[] = getOuvrageId();
        }
        
		$sql = "select *, (select nom from $db_name_to_use.batiments where id = batiment_id) as batiment,
				case when ts_valide is null then 0 else 1 end as isValide, $db_name_to_use.".self::SQL_TABLE_NAME.".nom as compare_data
                from $db_name_to_use.".self::SQL_TABLE_NAME." 
                where true $where
                order by $orderBy";
		$rows = $db->getAll($sql, $data);
			
		$key = 'id';
		if(isset($filter['forced_key']))
		{
			$key = "compare_data";
		}
		
		$liste = array();
		foreach($rows as $i => $row)
		{
			$liste[$row[$key]] = new self(intval($row['id']), $row);
		}
		
		return $liste;
	}
	
    public function getEditorHTML($iteration = 0, $only_champs_denormalises = false)
	{
?>
		<section>
			<label for="batiment_id"><?php print txt('Bâtiment'); ?></label>
			<div>
				<select id="batiment_id" name="batiment_id">
                    <?php Batiment::getOptions(array(), $this->batiment_id, true); ?>
				</select>
			</div>
		</section>

		<section>
            <label for="nom"><?php print txt('Nom'); ?></label>
            <div>
                <input id="nom" type="text" name="nom" value="<?php print ($this->nom); ?>" />
            </div>
        </section>
<?php
	}
	
	public static function getDynamicTableTitle()
	{
?>
		{ "sTitle": "<?php print txt('Bâtiment', false); ?>", "sClass": "dataTableLargetTitle" },
		{ "sTitle": "<?php print txt('Nom'); ?>", "sClass": "dataTableLargetTitle" },
<?php
	}
	
	public function getDynamicTableContent()
	{
        $is_comparaison = isset($this->statut_comparaison);
        $is_comparaison_actif = isset($this->statut_comparaison) && in_array($this->statut_comparaison, array(-1, 0));
		
		$color = "";
		if($is_comparaison)
		{
			if($this->statut_comparaison == 0)
			{
				$color = "color: green; font-weight: bold;";
			}
			else if($this->statut_comparaison == -1)
			{
				$color = "color: red; font-weight: bold;";
			}
		}
?>
		"<span style=\"<?=$color?>\"><?php print dm_addslashes($this->getRaw('batiment')); ?></span>",
		"<span style=\"<?=$color?>\"><?php print dm_addslashes($this->getRaw('nom')); ?></span>"
<?php
	}
    
    public static function delete($id)
	{
        global $db;
        $db->autocommit(false);
        $db->query("DELETE FROM `".self::SQL_TABLE_NAME."` WHERE id = ?", array($id));
        $db->commit();
	}
    
    public static function validate($data, $id)
	{
        $valide = true;
        foreach($data as $colomnName => $value)
        {
            if($colomnName == 'nom')
            {
                if(dm_strlen($value) == 0)
                {
                    setErreur(txt('Le nom est obligatoire.'));
                    $valide = false;
                }
            }
        }
        return $valide;
	}
}

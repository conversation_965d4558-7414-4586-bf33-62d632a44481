<?php

class AppelsLocauxGenerauxTypes extends ListClassWrapper {
	private static $CLASS_NAME='AppelsLocauxGenerauxTypes';
	const SQL_TABLE_NAME='appels_locaux_generaux_types';
	public $id;
	public $nom;
	public $nom_en;
	public $isValide;
	public $code_client_ajout;
	public $usager_ajout_id;
	public $ts_ajout;
	public $code_ajout;
	public $ts_valide;
	public $note_ajout;
	public $ts_sync;
	public $ts_delete;

	public static function getChampsSaisieMetaData()
	{
		return array('nom' => array( 'classe' => '', 'table' => '', 'champ_id' => 'nom', 'type' => 'input', 'format' => 'text', 'titre' => txt('Nom', false), 'css_editeur' => 'large'), 'nom_en' => array( 'classe' => '', 'table' => '', 'champ_id' => 'nom_en', 'type' => 'input', 'format' => 'text', 'titre' => txt('Nom anglais', false), 'css_editeur' => 'large'));
	}
}

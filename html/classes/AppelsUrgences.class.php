<?php

class AppelsUrgences extends FicheFormWrapper {
	private static $CLASS_NAME='AppelsUrgences';
	const SQL_TABLE_NAME='appels_urgences';
	const TITRE='Appel d\'urgence';
	public $id;
	public $fiche_id;
	public $appel_urgence_type_id;
	public $installation_type_id;
	public $qte_requise;
	public $appel_urgence_type;
	public $installation_type;
	public $quantite;
	public $commentaires;
    
    public static function getChampsSaisieMetaData()
    {
        return array(
            
            'appel_urgence_type' => array(
                'classe'      	=> 'AppelsUrgencesTypes',
                'table'      	=> 'appels_urgences_types',
                'champ_id'      => 'appel_urgence_type_id',
                'type'          => 'select',
                'format'        => 'int',
                'titre'         => txt('Équipement d\'appel d\'urgence', false),
                'css_editeur'   => 'large',
                'obligatoire'	=> 1,
            ),
            
            'installation_type' => array(
				'classe'      	=> 'InstallationsTypes',
				'table'      	=> 'installations_types',
                'champ_id'      => 'installation_type_id',
                'type'          => 'select',
                'format'        => 'int',
                'titre'         => txt('Installation', false),
                'css_editeur'   => 'average'
            ),
            
            'qte_requise' => array(
				'classe'      	=> '',
				'table'      	=> '',
				'champ_id'      => 'qte_requise',
				'type'          => 'input',
				'format'        => 'int',
				'titre'         => txt('Quantité', false),
				'css_editeur'   => 'tiny'
			),
            
            'commentaires' => array(
				'table'      	=> '',
				'champ_id'      => 'commentaires',
				'type'          => 'input',
                'format'        => 'text',
                'titre'         => txt('Commentaires', false),
                'css_editeur'   => 'xxlarge'
            ),
            
        );
    }

	public static function getListe($filter = array(), $orderBy = 'ordre', $extra_data = array())
	{
        $extra_data = array(
            'appel_urgence_type' => array('table' => 'appels_urgences_types'),
            'installation_type' => array('table' => 'installations_types'),
            
        );
        return parent::getListe($filter, $orderBy, $extra_data);
    }
}

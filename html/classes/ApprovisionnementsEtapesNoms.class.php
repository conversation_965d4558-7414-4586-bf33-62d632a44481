<?
class ApprovisionnementsEtapesNoms extends ListClassWrapper
{
    private static $CLASS_NAME='ApprovisionnementsEtapesNoms';
	const SQL_TABLE_NAME='approvisionnements_etapes_noms';
	const COMPARE_MANUALLY=true;
	public $id;
	public $nom;
	public $nom_fr;
	public $nom_en;
	public $code;
	public $isValide;
	public $code_client_ajout;
	public $usager_ajout_id;
	public $ts_ajout;
	public $code_ajout;
	public $ts_valide;
	public $note_ajout;
	public $ts_sync;
	public $ts_delete;

	public static function getChampsSaisieMetaData()
	{
		return array(
            
            'nom_fr' => array( 
                'classe' => '', 
                'table' => '', 
                'champ_id' => 'nom_fr', 
                'type' => 'input', 
                'format' => 'text', 
                'titre' => txt('Nom', false), 
                'css_editeur'   => 'large',
            ),
            
            'nom_en' => array( 
                'classe' => '', 
                'table' => '', 
                'champ_id' => 'nom_en', 
                'type' => 'input', 
                'format' => 'text', 
                'titre' => txt('Nom anglais', false), 
                'css_editeur'   => 'large',
            ),
			
		);
	}
	
	protected function setDataForId($id)
	{
        global $db, $Langue;
        
		$sql = "select approvisionnements_etapes_noms.*, approvisionnements_etapes_noms.nom_$Langue as nom
                from approvisionnements_etapes_noms 
                where approvisionnements_etapes_noms.id = ?";
		$row = $db->getRow($sql, array($id));
		
		$this->populateThisWithThat($row);
	}
	
	public static function getListe($filter = array(), $orderBy = 'nom', $all_data = false)
	{
        global $db, $Langue, $db_name;
        
        $db_name_to_use = $db_name;
        if(isset($filter['forced_bd']))
        {
            $db_name_to_use = $filter['forced_bd'];
        }
        
        $currentClassName = get_called_class();
        $table_name = $currentClassName::SQL_TABLE_NAME;
        
        $join = '';
        $where = '';
        $data = array();
        $extra_data = "";

        if(!$all_data)
        {
            $join .= " join $db_name_to_use.$table_name"."_projets on liste_item_id = $db_name_to_use.$table_name.id";
            $where .= " and projet_id = ?";
            $data[] = getProjetId();
            $extra_data = ", 1 as is_actif";
        }
        else if(getProjetId() > 0)
        {
            $join .= "left join $db_name_to_use.$table_name"."_projets on liste_item_id = $db_name_to_use.$table_name.id and projet_id = ?";
            $where .= "";
            $data[] = getProjetId();
            $extra_data .= ", case when projet_id is not null then 1 else 0 end as is_actif";
        }
        
		$sql = "select $db_name_to_use.approvisionnements_etapes_noms.*, $db_name_to_use.approvisionnements_etapes_noms.nom_$Langue as nom, concat(nom_fr, nom_en) as compare_data $extra_data
                from $db_name_to_use.approvisionnements_etapes_noms 
                $join
                where true $where
                order by $db_name_to_use.approvisionnements_etapes_noms.$orderBy";
        //print db::interpolateQuery($sql, $data);
		$rows = $db->getAll($sql, $data);
			
		$key = 'id';
		if(isset($filter['forced_key']))
		{
            $key = "compare_data";
		}
        
		$liste = array();
		foreach($rows as $i => $row)
		{
			$liste[$row[$key]] = new self(intval($row['id']), $row);
		}
		
		return $liste;
	}
	
    public function getEditorHTML($iteration = 0, $only_champs_denormalises = false)
	{
?>			
		<section>
            <label for="nom_fr"><?=txt('Nom FR')?></label>
            <div>
                <input id="nom_fr" type="text" name="nom_fr" value="<?=($this->nom_fr)?>" />
            </div>
        </section>

		<section>
            <label for="nom_en"><?=txt('Nom EN')?></label>
            <div>
                <input id="nom_en" type="text" name="nom_en" value="<?=($this->nom_en)?>" />
            </div>
        </section>
<?
	}
	
	public static function getDynamicTableTitle()
	{
?>
		{ "sTitle": "<?=txt('Nom')?>", "sClass": "dataTableLargetTitle" },
		{ "sTitle": "<?=txt('Nom anglais')?>", "sClass": "dataTableLargetTitle" },
		{ "sTitle": "<?=txt('Générique', false)?>", "sClass": "dataTableVeryShortTitle" },
<?
        $projet = getProjetInfo();
        if(isset($projet))
        {
?>
            { "sTitle": "<?=txt('Actif')?>", "sClass": "dataTableVeryShortTitle" },
<?
        }
	}
	
	public function getDynamicTableContent()
	{
        $projet = getProjetInfo();
        $currentClassName = get_called_class();
        $table_name = $currentClassName::SQL_TABLE_NAME;
        
        $is_comparaison = isset($this->statut_comparaison);
        $is_comparaison_actif = isset($this->statut_comparaison) && in_array($this->statut_comparaison, array(-1, 0));
		
		$color = "";
		if($is_comparaison)
		{
			if($this->statut_comparaison == 0)
			{
				$color = "color: green; font-weight: bold;";
			}
			else if($this->statut_comparaison == -1)
			{
				$color = "color: red; font-weight: bold;";
			}
		}
?>
		"<span style=\"<?=$color?>\"><?=dm_addslashes($this->getRaw('nom_fr'))?></span>",
		"<span style=\"<?=$color?>\"><?=dm_addslashes($this->getRaw('nom_en'))?></span>",
<?
        if(isset($projet))
        {
			if($is_comparaison_actif)
			{
?>
				"",
				''
<?
			}
			else
			{
?>
				"<?=($this->is_defaut == 1 ? 'Oui' : 'Non')?>",
				'<span style="display: none;" class="is_actif_filter"><?= ($this->is_actif == 1 ? 'Oui' : 'Non') ?></span><input type="checkbox" id="is_actif_<?=$this->id?>" class="is_actif"<?=($this->is_actif == 1 ? ' checked="checked"' : '')?> data-id="<?=$this->id?>" data-table="<?=$table_name?>" />'
<?
			}
        }
        else
        {
			if($is_comparaison_actif)
			{
?>
				""
<?
			}
			else
			{
?>
				'<input type="checkbox" id="is_defaut_<?=$this->id?>" class="is_defaut"<?=($this->is_defaut == 1 ? ' checked="checked"' : '')?> data-id="<?=$this->id?>" data-table="<?=$table_name?>" />'
<?
			}
        }
	}
	
	public static function getOptions($filter = array(), $selected = 0, $all_data = false)
	{
        global $Langue;
        
        $col_nom = "nom_$Langue";
            
        $rows = self::getListe($filter, $col_nom);
        foreach($rows as $i => $row)
        {
            $sel = $row->id == $selected ? ' selected="selected"' : '';
?>
            <option value="<?=$row->id?>"<?=$sel?>><?=$row->$col_nom?></option>
<?
        }
	}
    
    public static function delete($id)
	{
        global $db;
        $db->autocommit(false);
        $db->query("DELETE FROM `approvisionnements_etapes_noms_projets` WHERE liste_item_id = ?", array($id));
        $db->query("DELETE FROM `approvisionnements_etapes_noms` WHERE id = ?", array($id));
        $db->commit();
	}
    
    public static function validate($data, $id)
	{
        global $db;
        
        $valide = true;
        return $valide;
	}
}
?>
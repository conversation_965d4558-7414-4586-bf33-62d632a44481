<?

class CalibresProtectionsTypes extends ListClassWrapper {
	private static $CLASS_NAME='CalibresProtectionsTypes';
	const SQL_TABLE_NAME='calibres_protections_types';
	const SORT_NUMERICALLY=true;
	const COMPARE_MANUALLY=true;
	public $id;
	public $nom;
	public $nombre_poles;
    public $nombre_phase_type_id;
    public $nombre_phase_type;
	public $isValide;
	public $code_client_ajout;
	public $usager_ajout_id;
	public $ts_ajout;
	public $code_ajout;
	public $ts_valide;
	public $note_ajout;
	public $ts_sync;
	public $ts_delete;

	public static function getChampsSaisieMetaData()
	{
		return array(
            'nom' => array( 'classe' => '', 'table' => '', 'champ_id' => 'nom', 'type' => 'input', 'format' => 'text', 'titre' => txt('Nom', false), 'css_editeur'   => 'large'),
            
            'nombre_poles' => array(
                'classe'        => '',
                'table'      	=> '',
                'champ_id'      => 'nombre_poles',
                'type'          => 'input',
                'format'        => 'text',
                'titre'         => txt('Nombre de pôles', false),
                'css_editeur'   => 'xxlarge'
            ),
            
            'nombre_phase_type' => array(
                'classe'        => 'NombresPhasesTypes',
                'table'      	=> 'nombres_phases_types',
                'champ_id'      => 'nombre_phase_type_id',
                'type'          => 'select',
                'format'        => 'int',
                'titre'         => txt('Nombre de phase(s)/fils', false),
                'css_editeur'   => 'xxlarge'
            ),
        );
	}
	
    public static function getListe($filter = array(), $orderBy = 'tb1.nom', $all_data = false, $display_phase = true)
	{
        global $db, $buffer, $db_name;
        
        $db_name_to_use = $db_name;
        if(isset($filter['forced_bd']))
        {
            $db_name_to_use = $filter['forced_bd'];
        }
        
        $currentClassName = get_called_class();
        $table_name = $currentClassName::SQL_TABLE_NAME;

        if(isset($buffer["liste_$currentClassName"]))
        {
            $liste = $buffer["liste_$currentClassName"];
        }
        else
        {            
            $join = '';
            $where = '';
            $data = array();
            $extra_data = "";

            if(!$all_data)
            {
                $join .= " join $db_name_to_use.$table_name"."_projets on liste_item_id = tb1.id";
                $where .= " and projet_id = ?";
                $data[] = getProjetId();
                $extra_data = ", 1 as is_actif";
            }
            else if(getProjetId() > 0)
            {
                $join .= "left join $db_name_to_use.$table_name"."_projets on liste_item_id = tb1.id and projet_id = ?";
                $where .= "";
                $data[] = getProjetId();
                $extra_data .= ", case when projet_id is not null then 1 else 0 end as is_actif";
            }
            
            if(isset($filter['nombre_phase_type_id']))
            {
                $where .= " and nombre_phase_type_id = ?";
                $data[] = intval($filter['nombre_phase_type_id']);
            }
            
            if(isset($filter['nombre_phase_fil_primaire_id']))
            {
                $where .= " and nombre_phase_type_id = ?";
                $data[] = intval($filter['nombre_phase_fil_primaire_id']);
            }
            
            if($currentClassName::SORT_NUMERICALLY)
            {
                $extra_data .= ", case when cast(tb1.nom as DECIMAL(20,10)) = 0 then 1000000 else cast(tb1.nom as DECIMAL(20,10)) end as order_by_num_col";
                $orderBy = "order_by_num_col, tb1.nom";
            }

            $sql = "select tb1.*, tb1.nom, nb_phases.nom as nombre_phase_type, concat(tb1.nom, nombre_poles, nb_phases.nom) as compare_data $extra_data
                    from $db_name_to_use.".$table_name." as tb1
                    $join
                    left join $db_name_to_use.nombres_phases_types as nb_phases on nb_phases.id = nombre_phase_type_id
                    where true $where 
                    order by $orderBy";
            //printDebug(dm_nl2br(db::interpolateQuery($sql, $data)));
            $rows = $db->getAll($sql, $data);
			
			$key = 'id';
			if(isset($filter['forced_key']))
			{
				$key = "compare_data";
			}

            $liste = array();
            foreach($rows as $i => $row)
            {
                $liste[$row[$key]] = new $currentClassName(intval($row['id']), $row);
            }

            $buffer["liste_$currentClassName"] = $liste;
        }
		
		return $liste;
	}
	
    public static function validate($data, $id)
	{
        $valide = true;
        foreach($data as $colomnName => $value)
        {
            $value = $value[0];
            if($colomnName == 'nom')
            {
                if(dm_strlen($value) == 0)
                {
                    setErreur(txt('Le nom est obligatoire.'));
                    $valide = false;
                }
            }
            else if($colomnName == 'nombre_poles')
            {
                if(dm_strlen($value) == 0)
                {
                    setErreur(txt('Le nombre de pôles est obligatoire.'));
                    $valide = false;
                }
            }
            else if($colomnName == 'nombre_phase_type_id')
            {
                if(dm_strlen($value) == 0)
                {
                    setErreur(txt('Le nombre de phase(s)/fils est obligatoire.'));
                    $valide = false;
                }
            }
        }
        return $valide;
	}
}
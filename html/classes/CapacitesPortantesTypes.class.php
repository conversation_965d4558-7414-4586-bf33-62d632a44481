<?php

class CapacitesPortantesTypes extends ListClassWrapper {
	private static $CLASS_NAME='CapacitesPortantesTypes';
	const SQL_TABLE_NAME='capacites_portantes_types';
	public $id;
	public $nom;
	public $nom_en;
	public $is_defaut;


	public static function getChampsSaisieMetaData()
	{
		return array('nom' => array( 'classe' => '', 'table' => '', 'champ_id' => 'nom', 'type' => 'input', 'format' => 'text', 'titre' => txt('Nom', false), 'css_editeur' => 'large'), 'nom_en' => array( 'classe' => '', 'table' => '', 'champ_id' => 'nom_en', 'type' => 'input', 'format' => 'text', 'titre' => txt('Nom anglais', false), 'css_editeur' => 'large'));
	}
}

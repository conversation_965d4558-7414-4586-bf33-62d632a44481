<?php
#[AllowDynamicProperties]
class ClientsMaster extends DbRow {
    use Crudeur;
    static $schema = 'master';
    static $table_name = 'clients';
    static $table_alias = 'c';
    static $isCrudeur = true;

    private static $instance;

    public $id;
    public $db;
    public $nom;
    public $is_financement_etendu;
    public $is_actif;
    public $releve_mobile;
    public $code_client_referentiel;

    function __construct(string $codeClient)
    {
        $sql = "select * from master.clients WHERE db = ?";
        $row = db::instance()->getRow($sql, array($codeClient));

        if (isset($row)) {
            $this->populateThisWithThat($row);
        }
    }

    public static function instance()
    {
        if (self::$instance == null) {
            self::$instance = new self(Config::instance()->codeClient);
        }
        return self::$instance;
    }


    /**
     * @param $filter
     * @param $orderBy
     * @return ClientsMaster[]
     */
    public static function getListe($filter = array(), $orderBy = 'id'): array
    {
        global $db_user, $db_pswd, $db_host;
        $db = new db($db_user, $db_pswd, $db_host, 'master');

        $where = ' and is_actif = 1';
        $data = array();

        $sql = "select *
                from master.clients 
                where true $where
                order by $orderBy";
        $rows = $db->getAll($sql, $data);

        $liste = array();
        foreach ($rows as $row) {
            $liste[$row['db']] = $row;
        }

        return $liste;
    }
}
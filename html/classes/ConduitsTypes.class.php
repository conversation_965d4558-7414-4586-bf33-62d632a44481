<?php

class ConduitsTypes extends ListClassWrapper {
	private static $CLASS_NAME='ConduitsTypes';
	const SQL_TABLE_NAME='conduits_types';
	const COMPARE_MANUALLY=true;
	public $id;
	public $nom;
	public $diametre_conduit_electricite_id;
    public $diametre_conduit_electricite;
	public $isValide;
	public $code_client_ajout;
	public $usager_ajout_id;
	public $ts_ajout;
	public $code_ajout;
	public $ts_valide;
	public $note_ajout;
	public $ts_sync;
	public $ts_delete;
    
    public static function getChampsSaisieMetaData()
    {
        return array(
            
            'nom' => array(
                'classe'      	=> '',
                'table'      	=> '',
                'champ_id'      => 'nom',
                'type'          => 'input',
                'format'        => 'text',
                'titre'         => txt('Nom', false),
                'css_editeur'   => 'large'
            ),
            
            'diametre_conduit_electricite' => array(
				'classe'      	=> 'DiametresConduitsElectricites',
				'table'      	=> 'diametres_conduits_electricites',
                'champ_id'      => 'diametre_conduit_electricite_id',
                'type'          => 'select',
                'format'        => 'int',
                'titre'         => txt('Diamètre', false),
                'css_editeur'   => 'small',
                'sql_for_nom'   => "case when metrique is not null and imperial is not null
                                        then concat(metrique, ' (', imperial, ')')
                                    when metrique is not null and imperial is null
                                        then metrique
                                    when metrique is null and imperial is not null
                                        then imperial
                                    end"
            )
            
        );
    }
}

<?
class ConversionsNomsColonnes extends ListClassWrapper {
	private static $CLASS_NAME='ConversionsNomsColonnes';
	const SQL_TABLE_NAME='conversions_noms_colonnes';
	public $conversions_recettes;
    public $nom_id;
    public $nom;
    public $nom_en;
    public $is_defaut;
    public $is_actif;


	public static function getListe($filter = array(), $orderBy = 'nom', $all_data = false)
	{
		global $db;

		$where = '';
		$data = array(getProjetId());

		$sql = "select cnc.id, cnc.*, 
                  case when projet_id is not null then 1 else 0 end as is_actif
                from conversions_noms_colonnes cnc 
                left join conversions_noms_colonnes_projets on liste_item_id = cnc.id and projet_id = ?
                where true $where
                order by $orderBy";
		$rows = $db->getAssoc($sql, $data);

		$conversions_recettes_tmp = [];
		if (count($rows) > 0) {
			$where = ' and cr.nom_colonne_id in (' . dm_implode(', ', array_keys($rows)) . ')';
			$data = array(getProjetId());

			$sql = "select cr.*, parametre.nom_parametre as formulaire_liste_nom_parametre
                from conversions_recettes cr 
                left join formulaires_listes_parametres parametre on cr.formulaire_liste_nom_parametre_id = parametre.id
                left join conversions_recettes_projets on liste_item_id = cr.id and projet_id = ?
                where true $where";
			$conversions_recettes_tmp = $db->getAll($sql, $data);
		}

		$conversions_recettes = array();
		foreach ($conversions_recettes_tmp as $i => $conversion_recette) {
			$conversions_recettes[$conversion_recette['nom_colonne_id']]['formulaire_liste_nom_parametre_id'] = $conversion_recette['formulaire_liste_nom_parametre_id'];
			$conversions_recettes[$conversion_recette['nom_colonne_id']]['formulaire_liste_nom_parametre'] = $conversion_recette['formulaire_liste_nom_parametre'];
			$conversions_recettes[$conversion_recette['nom_colonne_id']]['valeurs_parametres'][] = $conversion_recette['valeur_parametre'];
		}

		$liste = array();
		foreach ($rows as $i => $row) {
			$row['conversions_recettes'] = isset($conversions_recettes[$row['id']]) ? $conversions_recettes[$row['id']] : [];
			$liste[intval($row['id'])] = new self(intval($row['id']), $row);
		}

		return $liste;
	}
}
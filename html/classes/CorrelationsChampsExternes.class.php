<?
class CorrelationsChampsExternes extends DbRow {
	public $id;
	public $projet_id;
	public $code_externe;
	public $formulaire;
	public $nom_champ;
    public $fiche_type;
    public $fiche_sous_type;
	
	protected function setDataForId($id)
	{
        global $db;
        
		$sql = "select *, code_externe as nom
                from correlations_champs_externes 
                where id = ?";
		$row = $db->getRow($sql, array($id));
		
		$this->populateThisWithThat($row);
	}
	
	public static function getListe($filter = array(), $orderBy = 'code_externe')
	{
        global $db, $Langue;
        
        $where = '';
        $data = array();
        
        if(getProjetId() > 0)
        {
            $where .= ' and projet_id = ?';
            $data[] = getProjetId();
        }
        
        if(isset($filter['fiche_sous_type_id']))
        {
            $where .= " and formulaire in (select nom from formulaires where id in (select formulaire_id from formulaires_fiches_sous_types where fiche_sous_type_id = ?)) and nom_champ not like '\_\_\_%'";
            $data[] = intval($filter['fiche_sous_type_id']);
        }
        
        if(isset($filter['id']) && intval($filter['id']) > 0)
        {
            $where .= " and correlations_champs_externes.id = ? and nom_champ not like '\_\_\_%'";
            $data[] = intval($filter['id']);
        }
        
        if(isset($filter['formulaire']))
        {
            $where .= " and formulaire = ? and nom_champ not like '\_\_\_%'";
            $data[] = $filter['formulaire'];
        }
        
        if(isset($filter['formulaire_id']) && intval($filter['formulaire_id']) > 0)
        {
            $formulaire_filtre = Formulaires::getNomFromId(intval($filter['formulaire_id']));
            $where .= " and formulaire = ? and nom_champ not like '\_\_\_%'";
            $data[] = $formulaire_filtre;
        }
        
        if(isset($filter['code_externe_in']) && is_array($filter['code_externe_in']))
        {
            $where .= " and code_externe in (".db::placeHolders($filter['code_externe_in']).")";
	        $data = array_merge($data, $filter['code_externe_in']);
        }
        
		$sql = "select correlations_champs_externes.*, code_externe as nom,
                    (select fiches_types.nom_$Langue from fiches_types where fiches_types.formulaire_general = formulaires.nom) as fiche_type,
                    (select fiches_sous_types.nom_$Langue from fiches_sous_types where fiches_sous_types.formulaire_general = formulaires.nom) as fiche_sous_type
                from correlations_champs_externes 
                left join formulaires on formulaires.nom = correlations_champs_externes.formulaire
                where true $where
                order by code_externe";
        //print db::interpolateQuery($sql, $data);
		$rows = $db->getAll($sql, $data);
		
		$liste = array();
		foreach($rows as $i => $row)
		{
			$liste[intval($row['id'])] = new self(intval($row['id']), $row);
		}
		
		return $liste;
	}
	
    public function getEditorHTML($iteration = 0, $only_champs_denormalises = false)
	{
?>			
        <input id="projet_id" type="hidden" name="projet_id" value="<?=getProjetId()?>" />
		<section>
            <label for="code_externe"><?=txt('Champ externe')?></label>
            <div>
                <input id="code" type="text" name="code_externe" value="<?=($this->code_externe)?>" />
            </div>
        </section>

		<section>
            <label for="formulaire"><?=txt('Formulaire')?></label>
            <div>
                <select id="formulaire" name="formulaire">
                    <option value=""> </option>
                    <?=Formulaires::getOptionsByNom(array('ids' => array_merge(Formulaires::getFormulairesIdForTypeForm('UNIQUE'), Formulaires::getFormulairesIdForTypeForm('LISTE_COORDINATION'))), $this->formulaire)?>
                </select>
            </div>
        </section>

        <section>
            <label for="champ_nom"><?=txt('Champ')?></label>
            <div>
<?
                    $className = $this->formulaire;
                    $metas = array();
                    if(dm_strlen($className) > 0)
                    {
                        $metas = $className::getFlatChampsSaisieMetaData();
                    }
?>
                    <select id="nom_champ" name="nom_champ">
                        <option value=""> </option>
<?
                        $found = false;
                        foreach($metas as $key => $meta)
                        {
                            if(isset($meta['titre']) && !isset($meta['liste']) && !isset($meta['source_externe']) && dm_strlen($meta['titre']) > 0)
                            {
                                $sel = $key == $this->nom_champ ? ' selected="selected"' : '';
                                if($key == $this->nom_champ)
                                {
                                    $found = true;
                                }
?>
                                <option value="<?=$key?>"<?=$sel?>><?=$meta['titre']?></option>
<?
                            }
                        }
?>
                    </select>
<?
                    if(!$found)
                    {
                        print '<br />'.$this->nom_champ;
                    }
?>
            </div>
        </section>
<?
	}
	
	public static function getDynamicTableTitle()
	{
?>
		{ "sTitle": "<?=txt('Champ externe')?>" },
		{ "sTitle": "<?=txt('Formulaire')?>" },
		{ "sTitle": "<?=txt('Champ')?>" },
<?
	}
	
	public function getDynamicTableContent()
	{
        $className = $this->formulaire;
        $metas = $className::getFlatChampsSaisieMetaData();
            
        $type = '';
        if(isset($this->fiche_type) && dm_strlen($this->fiche_type) > 0)
        {
            $type = ' ('.$this->fiche_type.')';
        }

        if(isset($this->fiche_sous_type) && dm_strlen($this->fiche_sous_type) > 0)
        {
            $type = ' ('.$this->fiche_sous_type.')';
        }
?>
		"<?=dm_addslashes($this->getRaw('code_externe'))?>",
		"<?=dm_addslashes($className::TITRE.$type)?>",
        "<?=dm_addslashes(isset($metas[$this->nom_champ]['titre']) ? $metas[$this->nom_champ]['titre'] : $this->nom_champ)?>"
<?
	}
	
	public static function getOptions($filter = array(), $selected = 0, $includeEmpty = false)
	{
        if($includeEmpty)
        {
?>
            <option value=""> </option>
<?
        }
        $rows = self::getListe($filter, 'code_externe');
        foreach($rows as $i => $row)
        {
            $form = '';
            if(isset($row->fiche_type) && dm_strlen($row->fiche_type) > 0)
            {
                $form = ' ('.$row->fiche_type.')';
            }
            
            $sel = $row->id == $selected ? ' selected="selected"' : '';
            $class = $row->formulaire;
?>
            <option value="<?=$row->id?>"<?=$sel?> data-code_externe="<?=$row->code_externe?>"><?=$row->code_externe.' ('.$class::TITRE.')'?></option>
<?
        }
	}
    
    public static function delete($id)
	{
        global $db;
        $db->autocommit(false);
        $db->query("DELETE FROM `correlations_champs_externes` WHERE id = ?", array($id));
        $db->commit();
	}
    
    public static function validate($data, $id)
	{
        $valide = true;
        foreach($data as $colomnName => $value)
        {
            if($colomnName == 'code_externe')
            {
                if(dm_strlen($value) == 0)
                {
                    setErreur(txt('Le champ externe est obligatoire.'));
                    $valide = false;
                }
            }
            else if($colomnName == 'formulaire')
            {
                if(dm_strlen($value) == 0)
                {
                    setErreur(txt('Le formulaire est obligatoire.'));
                    $valide = false;
                }
            }
            else if($colomnName == 'nom_champ')
            {
                if(dm_strlen($value) == 0)
                {
                    setErreur(txt('Le champ est obligatoire.'));
                    $valide = false;
                }
            }
        }
        return $valide;
	}
}
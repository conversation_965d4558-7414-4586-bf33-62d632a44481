<?
class Cuves extends FicheFormWrapper {
	private static $CLASS_NAME='Cuves';
	const SQL_TABLE_NAME='cuves';
	const TITRE='Cuves';
	public $id;
	public $fiche_id;
	public $projet_id;
	public $type_id;
	public $qte_requise;
	public $projet;
	public $type;
	public $quantite;
	public $commentaires;
    
    public static function getChampsSaisieMetaData()
    {
        return array(
            
            'type' => array(
                'classe'      	=> 'CuvesTypes',
                'table'      	=> 'cuves_types',
                'champ_id'      => 'type_id',
                'type'          => 'select',
                'format'        => 'int',
                'titre'         => txt('Type de cuve', false),
                'css_editeur'   => 'xxlarge',
                'obligatoire'	=> 1,
            ),
            
            'qte_requise' => array(
				'classe'      	=> '',
				'table'      	=> '',
				'champ_id'      => 'qte_requise',
				'type'          => 'input',
				'format'        => 'int',
				'titre'         => txt('Quantité', false),
				'css_editeur'   => 'tiny'
			),
            
            'commentaires' => array(
                'classe'      	=> '',
                'table'      	=> '',
                'champ_id'      => 'commentaires',
                'type'          => 'input',
                'format'        => 'text',
                'titre'         => txt('Commentaires', false),
                'css_editeur'   => 'xxlarge'
            ),
            
        );
    }
}
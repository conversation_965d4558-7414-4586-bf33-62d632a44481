<?php
#[AllowDynamicProperties]
class DbRow implements ArrayAccess
{
    public function __construct($id = 0, $row = null)
    {
        if (isset($row) && (is_object($row) || is_array($row))) {
            $this->populateThisWithThat($row);
        } else {
            $this->setDataForId($id);
        }
    }

    protected function setDataForId($id)
    {
    }

    public function offsetExists(mixed $offset): bool
    {
        return isset($this->$offset);
    }

    public function &offsetGet(mixed $offset): mixed
    {
        $this->$offset = $this->$offset ?? null;
        $var = is_string($this->$offset) ? dm_htmlspecialchars(
            (string)$this->$offset,
            ENT_QUOTES,
            'UTF-8'
        ) : $this->$offset;
        return $var;
    }

    public function offsetSet(mixed $offset, mixed $value): void
    {
        $this->$offset = $value;
    }

    public function offsetUnset(mixed $offset): void
    {
        unset($this->$offset);
    }

    public function get($key)
    {
        $this->$key = $this->$key ?? null;
        return is_string($this->$key) ? dm_htmlspecialchars((string)$this->$key, ENT_QUOTES, 'UTF-8') : $this->$key;
    }

    public function getRaw(string $key): mixed
    {
        $this->$key = $this->$key ?? null;
        return is_string($this->$key) ? htmlspecialchars_decode($this->$key) : $this->$key;
    }

    public function populateThisWithThat($that): void
    {
        if (isset($that)) {
            foreach ($that as $key => $value) {
                $this->$key = $key == 'id' ? (int)$that[$key] : $that[$key];
            }
        }
    }
}
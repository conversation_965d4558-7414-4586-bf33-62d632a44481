<?php

class DispositifsElectriquesChamps extends FicheFormWrapper {
	private static $CLASS_NAME='DispositifsElectriquesChamps';
	const SQL_TABLE_NAME='dispositifs_electriques_champs';
	const TITRE='Dispositif électrique de champs';
	public $id;
	public $fiche_id;
	public $code;
	public $dispositif_electrique_champ_type_id;
	public $fourni_installe_f_id;
	public $fourni_installe_i_id;
	public $fourni_installe_r_id;
	public $qte_requise;
	public $quantite_alim_distinct_id;
	public $dispositif_electrique_champ_type;
	public $fourni_installe_f;
	public $fourni_installe_i;
	public $fourni_installe_r;
	public $quantite;
	public $qte_alim_distinct;
	public $commentaires;
    
    public static function getChampsSaisieMetaData()
    {
        return array(
            
            'code' => array(
                'classe'      	=> '',
                'table'      	=> '',
                'champ_id'      => 'code',
                'type'          => 'input',
                'format'        => 'text',
                'titre'         => txt('Code', false),
                'css_editeur'   => 'medium',
                'obligatoire'	=> 1,
            ),
            
            'dispositif_electrique_champ_type' => array(
				'classe'      	=> 'DispositifsElectriquesChampsTypes',
				'table'      	=> 'dispositifs_electriques_champs_types',
                'champ_id'      => 'dispositif_electrique_champ_type_id',
                'type'          => 'select',
                'format'        => 'int',
                'titre'         => txt('Dispositif', false),
                'css_editeur'   => 'large'
            ),
            
            'qte_requise' => array(
				'classe'      	=> '',
				'table'      	=> '',
				'champ_id'      => 'qte_requise',
				'type'          => 'input',
				'format'        => 'int',
				'titre'         => txt('Quantité', false),
				'css_editeur'   => 'tiny'
			),
            
            'fourni_installe_f' => array(
				'classe'      	=> 'FournisInstalles',
				'table'      	=> 'fournis_installes',
                'champ_id'      => 'fourni_installe_f_id',
                'type'          => 'select',
                'format'        => 'int',
                'titre'         => txt('Fourni', false),
                'css_editeur'   => 'medium'
            ),
            
            'fourni_installe_i' => array(
				'classe'      	=> 'FournisInstalles',
				'table'      	=> 'fournis_installes',
                'champ_id'      => 'fourni_installe_i_id',
                'type'          => 'select',
                'format'        => 'int',
                'titre'         => txt('Installé', false),
                'css_editeur'   => 'medium'
            ),
            
            'fourni_installe_r' => array(
				'classe'      	=> 'FournisInstalles',
				'table'      	=> 'fournis_installes',
                'champ_id'      => 'fourni_installe_r_id',
                'type'          => 'select',
                'format'        => 'int',
                'titre'         => txt('Raccordé', false),
                'css_editeur'   => 'medium'
            ),

            'qte_alim_distinct' => array(
                'classe'      	=> '',
                'table'      	=> '',
                'champ_id'      => 'qte_alim_distinct',
                'type'          => 'input',
                'format'        => 'int',
                'titre'         => txt('Alimentation(s) distincte(s)', false),
                'css_editeur'   => 'tiny'
            ),
            
            'commentaires' => array(
				'table'      	=> '',
				'champ_id'      => 'commentaires',
				'type'          => 'input',
                'format'        => 'text',
                'titre'         => txt('Commentaires', false),
                'css_editeur'   => 'average'
            ),
            
        );
    }

	public static function getListe($filter = array(), $orderBy = 'ordre', $extra_data = array())
	{
        $extra_data = array(
            
            'dispositif_electrique_champ_type' => array('table' => 'dispositifs_electriques_champs_types'),
            'fourni_installe_f' => array('table' => 'fournis_installes'),
            'fourni_installe_i' => array('table' => 'fournis_installes'),
            'fourni_installe_r' => array('table' => 'fournis_installes'),
        );
        return parent::getListe($filter, $orderBy, $extra_data);
    }
}

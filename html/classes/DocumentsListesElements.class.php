<?php

use Aws\S3\MultipartUploader;
use Aws\S3\S3Client;
use Symfony\Component\Validator\Constraint;
use Symfony\Component\Validator\Constraints\File;
use Symfony\Component\Validator\ConstraintValidator;
use Symfony\Component\Validator\ConstraintViolationListInterface;
use Symfony\Component\Validator\Validation;

class FileExtension extends Constraint
{
    public $message = "L'extension du fichier {{ name }} n'est pas valide ({{ extension }}).<br />Les extensions autorisées sont {{ extensions }}.";

    public function validatedBy()
    {
        return static::class.'Validator';
    }
}

class FileExtensionValidator extends ConstraintValidator
{
    public function validate($value, Constraint $constraint)
    {
        $extensionsValides = ['pdf', 'xls', 'xlsx', 'doc', 'rtf', 'pdf', 'txt', 'ppt', 'pptx', 'jpg', 'jpeg', 'png', 'gif', 'apng', 'avif', 'apng', 'jfif', 'pjpeg', 'pjp', 'svg', 'webp'];
        if (!preg_match('/\.(' . implode('|', $extensionsValides) . ')$/', strtolower($value))) {
            $ext = pathinfo($value, PATHINFO_EXTENSION);
            $message = str_replace(['{{ name }}', '{{ extension }}', '{{ extensions }}'], [$value, $ext, implode(', ', $extensionsValides)], $constraint->message);
            $this->context->buildViolation($message)->addViolation();
        }
    }
}

class DocumentsListesElements extends DbRow {
	public $id;
	public $nom_table;
	public $liste_item_id;
	public $google_id;
	public $aws_id;
	public $nom;
	public $mime_type;
	public $size;
    public $is_public;
    public $rotation;
    public $is_image_principale;

	const allowableTypes = array(
		IMAGETYPE_GIF,
		IMAGETYPE_JPEG,
		IMAGETYPE_PNG
	);
	
	protected function setDataForId($id)
	{
        global $db;
        
		$sql = "select *
                from documents_listes_elements 
                where id = ?";
		$row = $db->getRow($sql, array($id));
		
		$this->populateThisWithThat($row);
	}

	public static function getDataFiltre($filter = array())
	{
		global $validColumns;

		$where = '';
		$data = array();

		$extra_select = '';
		$joins_filtre = array();
		if(isset($filter['fiche_id']))
		{
			$extra_select .= ", case when dlef.fiche_id is not null then dlef.is_public_fiche else -1 end as is_public_fiche";
			$joins_filtre[] = 'left join documents_listes_elements_fiches dlef on dlef.document_liste_element_id = dle.id and dlef.fiche_id = ?';
			$data[] = $filter['fiche_id'];
		}

		if(isset($filter['ids']))
		{
			$where .= ' and dle.id in (\''.dm_implode('\',\'', $filter['ids']).'\')';
		}

		if(isset($filter['nom_table']) && dm_strlen($filter['nom_table']) > 0)
		{
			if (isset($filter['exclure_inactif']) && $filter['exclure_inactif'] && isset($validColumns['doc'][$filter['nom_table']])) {
				$joins_filtre[] = 'join `' . $filter['nom_table'] . '_projets` pp on pp.liste_item_id = dle.liste_item_id and pp.projet_id = ?';
				$data[] = getProjetId();
			}

			$where .= ' and dle.nom_table = ?';
			$data[] = $filter['nom_table'];
		}

		if(isset($filter['liste_item_id']) && $filter['liste_item_id'] <> 0)
		{
			$where .= ' and dle.liste_item_id = ?';
			$data[] = intval($filter['liste_item_id']);
		}

		if(isset($filter['mime_type']) && strlen($filter['mime_type']) > 0)
        {
            $where .= ' and dle.mime_type = ?';
            $data[] = $filter['mime_type'];
        }

		if(isset($filter['mime_type_not']) && strlen($filter['mime_type_not']) > 0)
        {
            $where .= ' and dle.mime_type <> ?';
            $data[] = $filter['mime_type_not'];
        }

		if(isset($filter['is_image_principale']))
		{
			$where .= ' and dle.is_image_principale = ?';
			$data[] = intval($filter['is_image_principale']);
		}
		else
		{
			$where .= ' and dle.is_image_principale = 0';
		}

		if(!isset($filter['is_public']) || $filter['is_public'] == 1)
		{
			if(isset($filter['fiche_id']))
			{
				$where .= ' and ((dle.is_public = 1 and dlef.is_public_fiche is null) or (dlef.is_public_fiche is not null and dlef.is_public_fiche = 1))';
			}
			else
			{
				$where .= ' and dle.is_public = 1';
			}
		}

		return [$where, $data, $extra_select, $joins_filtre];
	}

	/**
	 * @param array filter
	 * @param string orderBy
	 * @return self[]
	 */
	public static function getListe($filter = array(), $orderBy = 'nom')
	{
        global $db;
        
        list($where, $data, $extra_select, $joins_filtre) = self::getDataFiltre($filter);

        $array_key = 'id';
        if (isset($filter['force_array_key'])) {
	        $array_key = $filter['force_array_key'];
        }

		$sql = "select * $extra_select
                from documents_listes_elements dle
                ".dm_implode("\n", $joins_filtre)."
                where true $where
                order by $orderBy";
//        printDebug(dm_nl2br(db::interpolateQuery($sql, $data)));
		$rows = $db->getAll($sql, $data);
		
		$liste = array();
		foreach($rows as $i => $row)
		{
			$liste[$row[$array_key]] = new self(intval($row['id']), $row);
		}
		
		return $liste;
	}

	public static function getListeByElementId($filter = array(), $orderBy = 'nom')
	{
		global $db;

		list($where, $data, $extra_select, $joins_filtre) = self::getDataFiltre($filter);

		$array_key = 'id';
		if (isset($filter['force_array_key'])) {
			$array_key = $filter['force_array_key'];
		}

		$sql = "select * $extra_select
                from documents_listes_elements dle
                ".dm_implode("\n", $joins_filtre)."
                where true $where
                order by $orderBy";
		//printDebug(dm_nl2br(db::interpolateQuery($sql, $data)));
		$rows = $db->getAll($sql, $data);

		$liste = array();
		foreach($rows as $i => $row)
		{
			$liste[$row['liste_item_id']][$row[$array_key]] = new self(intval($row['id']), $row);
		}

		return $liste;
	}

	public static function getCount($filter = array())
	{
		global $db;

		list($where, $data, $extra_select, $joins_filtre) = self::getDataFiltre($filter);

		$sql = "select count(*) as nb
                from documents_listes_elements dle
                ".dm_implode("\n", $joins_filtre)."
                where true $where";
		//printDebug(dm_nl2br(db::interpolateQuery($sql, $data)));
		$nb = $db->getOne($sql, $data);

		return $nb;
	}

	private static function getAwsS3Client()
	{
		global $isDev, $ENDPOINT;

		putenv ( "AWS_ACCESS_KEY_ID=" . $_ENV['AWS_ACCESS_KEY_ID'] );
		putenv ( "AWS_SECRET_ACCESS_KEY=" . $_ENV['AWS_SECRET_ACCESS_KEY'] );
		$ENDPOINT = $_ENV['AWS_ENDPOINT'];

		$params = [
			'region'  => 'ca-central-1',
			'version' => 'latest',
			'endpoint' => $ENDPOINT
		];
		if ($isDev) {
			$params['scheme'] = 'http';
		}
		return new S3Client($params);
	}
    
    public static function setRegisterStreamWrapper()
    {
    	$client = self::getAwsS3Client();
	    $client->registerStreamWrapper();
		return $client;
    }

    public static function getThumbnailPathFile($id, $thumbnail, $original_name)
    {
	    global $CodeClient;
	    $path_parts = pathinfo($original_name);
	    $path = '/var/www/images/thumbnails/'.$CodeClient;
	    $file_name = $id . '_' . $thumbnail . '.' . (strtolower($path_parts['extension']) == 'jpeg' ? 'jpg' : strtolower($path_parts['extension']));

	    return $path . '/' . $file_name;
    }

    public function getExt()
    {
	    $path_parts = pathinfo($this->getRaw('nom'));
	    return (strtolower($path_parts['extension']) == 'jpeg' ? 'jpg' : strtolower($path_parts['extension']));
    }
    
    public function download($thumbnail = null)
    {
    	global $S3_BUCKET, $CodeClient;

    	$file_name = $this->getRaw('nom');
    	if ($this->nom_table == 'fiches') {
    		$fiche = new Fiches($this->liste_item_id);
		    $file_name = $fiche->code . '_' . $this->id . '.' . $this->getExt();
	    }
	    header('Content-Description: File Transfer');
	    header('Content-Type: ' . $this->mime_type);
	    header('Content-Disposition: attachment; filename=' . $file_name);
	    header('Content-Transfer-Encoding: binary');
	    header('Connection: Keep-Alive');
	    header('Expires: 0');
	    header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
	    header('Pragma: public');

    	if ($thumbnail == 300) {
		    $content = file_get_contents(self::getThumbnailPathFile($this->id, 300, $this->getRaw('nom')));
		    header('Content-Length: ' . dm_strlen($content));
		    print $content;
	    } else {
		    header('Content-Length: ' . $this->size);
		    self::setRegisterStreamWrapper();
		    $directory = self::getAwsDirectory($this->is_image_principale, $this->getRaw('nom'), $this->nom_table);
		    $content = file_get_contents("s3://$S3_BUCKET/bucket1/$CodeClient/$directory/" . $this->getRaw('nom'));
		    header('Content-Length: ' . dm_strlen($content));
		    print $content;
	    }
    }

    public function display()
    {
        global $S3_BUCKET, $CodeClient;

        header('Content-type: application/pdf');
        header('Content-Disposition: inline; filename="' . $this->getRaw('nom') . '"');
        header('Content-Transfer-Encoding: binary');
        header('Accept-Ranges: bytes');

        self::setRegisterStreamWrapper();
        $directory = self::getAwsDirectory($this->is_image_principale, $this->getRaw('nom'), $this->nom_table);
        $content = file_get_contents("s3://$S3_BUCKET/bucket1/$CodeClient/$directory/" . $this->getRaw('nom'));
        header('Content-Length: ' . dm_strlen($content));
        print $content;
    }

    public static function getColNomForTable($table): string
    {
        return ['fiches' => 'code', 'questions' => 'code', 'inventaires_lignes' => 'numero_inventaire', 'inventaires_locaux' => 'numero_local'][$table] ?? 'nom';
    }

    /**
     * @param $file
     * @return array
     */
    public static function validateFile($file): array
    {
        $validator = Validation::createValidator();

        $violations1 = $validator->validate(
            $file['tmp_name'],
            [
                new File([
                    'maxSize' => '120M',
                    'maxSizeMessage' => txt('Le fichier est trop volumineux ({{ size }} {{ suffix }}). La taille maximale autorisée est de {{ limit }} {{ suffix }}.')
                ])
            ]
        );

        $violations2 = $validator->validate(
            $file['name'],
            [
                new FileExtension()
            ]
        );

        $violations = [];
        foreach ($violations1 as $violation) {
            $violations[] = $violation;
        }
        foreach ($violations2 as $violation) {
            $violations[] = $violation;
        }

        return $violations;
    }
    
    public static function add($nom_table, $liste_item_id, $file, $is_image_principale = 0)
    {
        global $db, $CodeClient, $isDev;
        
        $db->autocommit(false);

        if(dm_strlen($file['name']) > 0) {
	        if ($is_image_principale == 1) {
		        $sql = "select * from documents_listes_elements where nom_table = ? and liste_item_id = ? and projet_id = ? and is_image_principale = 1";
		        $image = $db->getRow($sql, array($nom_table, $liste_item_id, getProjetId()));

		        if (isset($image) && $image['id']) {
			        $image_obj = new self($image['id'], $image);
			        $image_obj->delete();
		        }
	        }

	        $col = self::getColNomForTable($nom_table);
	        $sql = "select `" . db::tableColonneValide($col) . "` from `" . db::tableColonneValide($nom_table) . "` where id = ?";
	        $codePrefixe = $db->getOne($sql, [$liste_item_id]);

	        $fileName = dm_preg_replace("/[^a-zA-Z0-9\-_ .]+/", "", strip_accents($file['name']));
			$explodedFileName = dm_explode('_', $fileName);

			if ($explodedFileName[0] <> $codePrefixe) {
				$fileName = $codePrefixe . '_' . $fileName;
			}

	        $sql = "select count(*) from documents_listes_elements where nom = ?";
	        $nb = $db->getOne($sql, [$fileName]);

	        $sql = "insert into documents_listes_elements (nom_table, liste_item_id, nom, mime_type, size, projet_id, is_image_principale) 
                    values (?, ?, ?, ?, ?, ?, ?)";
	        $db->query($sql, array($nom_table, $liste_item_id, $fileName, $file['type'], $file['size'], getProjetId(), $is_image_principale));

	        $new_id = $db->lastInsertId();
	        $path_parts = pathinfo($file['name']);

	        $directory = 'documents';
            if (in_array(strtolower($path_parts['extension']), ['jpg', 'jpeg', 'png', 'gif', 'apng', 'avif', 'apng', 'jfif', 'pjpeg', 'pjp', 'svg', 'webp'])) {
		        self::createThumbnail($file['tmp_name'], self::getThumbnailPathFile($new_id, 300, $file['name']));
		        if ($is_image_principale == 1) {
			        $directory = 'images';
		        } else if ($nom_table == 'fiches') {
			        $directory = 'photos_fiches';
		        } else if ($nom_table == 'questions') {
                    $directory = 'qrt_bcf';
                }
	        }

	        if ($nb > 0) {
		        $path_parts = pathinfo($fileName);
		        $fileName = $path_parts['filename'] . '_' . $new_id . '.'.$path_parts['extension'];
		        $sql = "update documents_listes_elements set nom = ? where id = ?";
		        $db->query($sql, [$fileName, $new_id]);
	        }

            if (Config::instance()->isDev) {
                print "Write file /$CodeClient/$directory/$fileName<br />";
            } else {
                self::uploadToAws($directory, $fileName, $file);
            }

            $db->commit();
        }
    }

    private static function getAwsDirectory($is_image_principale, $fileName, $nom_table)
    {
	    $directory = 'documents';
	    $path_parts = pathinfo($fileName);
	    if (in_array(strtolower($path_parts['extension']), ['jpg', 'jpeg', 'png', 'gif', 'apng', 'avif', 'apng', 'jfif', 'pjpeg', 'pjp', 'svg', 'webp'])) {
		    if ($is_image_principale == 1) {
			    $directory = 'images';
		    } else if ($nom_table == 'fiches') {
			    $directory = 'photos_fiches';
		    } else if ($nom_table == 'questions') {
                $directory = 'qrt_bcf';
            }
	    }
	    return $directory;
    }

    public function getAwsDownloadUrl(): string
    {
        $directory = self::getAwsDirectory($this->is_image_principale, $this->getRaw('nom'), $this->nom_table);
        return 'https://docmatic.os-qc1.cirrusproject.ca/bucket1/' . Config::instance()->codeClient . '/' . $directory . '/' . $this->getRaw('nom');
    }

    private static function uploadToAws($directory, $fileName, $file)
    {
    	global $S3_BUCKET, $CodeClient;
    	self::setRegisterStreamWrapper();
	    file_put_contents("s3://$S3_BUCKET/bucket1/$CodeClient/$directory/$fileName", file_get_contents($file['tmp_name']));
	}

    public static function createThumbnail($path_source, $path_dest)
    {
    	global $CodeClient;

	    $path = '/var/www/images/thumbnails/'.$CodeClient;
	    if (!is_dir($path)) {
		    mkdir($path, 0777, true);
	    }

	    self::generateThmbnail($path_source, 300, 300, $path_dest);
    }
    
    public function delete()
    {
        global $db, $S3_BUCKET, $CodeClient;
        $db->autocommit(false);

	    $sql = "delete from documents_listes_elements_fiches where document_liste_element_id = ?";
	    $db->query($sql, [$this->id]);
        
        $sql = "delete from documents_listes_elements where id = ?";
        $db->query($sql, array($this->id));

        if (!Config::instance()->isDev) {
            self::setRegisterStreamWrapper();
            $directory = self::getAwsDirectory($this->is_image_principale, $this->getRaw('nom'), $this->nom_table);
            unlink("s3://$S3_BUCKET/bucket1/$CodeClient/$directory/{$this->getRaw('nom')}");
        }
        
        $db->commit();
    }

	public static function makeZipFile()
	{
		global $S3_BUCKET, $CodeClient;

		$path = '/var/www/temp/'.$CodeClient.'_documents';
		if (!is_dir($path)) {
			mkdir($path, 0777, true);
		}
		if (is_file("$path.zip")) {
			unlink("$path.zip");
		}

		$client = self::getAwsS3Client();
		$client->downloadBucket($path, $S3_BUCKET, $CodeClient);

		$cmd = "zip -r -q -x .\* $path.zip $path/* 2>&1";
		exec($cmd, $output, $ret);
		if ($ret <> 0) {
			throw new Exception('Erreur lors de l\'export des documents '.$cmd.getDebug($output));
		}

		$output = null;
		$ret = null;
		$cmd = "rm -rf $path 2>&1";
		exec($cmd, $output, $ret);
		if ($ret <> 0) {
			throw new Exception('Erreur lors de la suppression du répertoire temporaire '.$cmd.getDebug($output));
		}

		return "$path.zip";
	}

	public static function makeZipFileForLibrairie($nom_table, $exclure_inactif = false)
	{
		global $S3_BUCKET, $CodeClient;

		$path = '/var/www/temp/'.$CodeClient.'_documents';
		if (!is_dir($path)) {
			mkdir($path, 0777, true);
		}
		if (is_file("$path.zip")) {
			unlink("$path.zip");
		}

		$fichersInexistants = [];
		$listeDocuments = self::getListe([ 'nom_table' => $nom_table, 'exclure_inactif' => $exclure_inactif ]);

		self::setRegisterStreamWrapper();
		$directory = 'documents';
		foreach ($listeDocuments as $iter => $document) {
			if (is_file("s3://$S3_BUCKET/bucket1/$CodeClient/$directory/" . $document->nom)) {
				$content = file_get_contents("s3://$S3_BUCKET/bucket1/$CodeClient/$directory/" . $document->nom);
				file_put_contents($path . "/" . $document->nom, $content);
			} else {
				$fichersInexistants[] = $document->nom;
			}
			unset($listeDocuments[$iter]);
		}

		$cmd = "cd $path ; zip -r $path.zip . ; cd - 2>&1";
		exec($cmd, $output, $ret);
		if ($ret <> 0) {
			throw new Exception('Erreur lors de l\'export des documents '.$cmd.getDebug($output));
		}

		$output = null;
		$ret = null;
		$cmd = "rm -rf $path 2>&1";
		exec($cmd, $output, $ret);
		if ($ret <> 0) {
			throw new Exception('Erreur lors de la suppression du répertoire temporaire '.$cmd.getDebug($output));
		}

		$clientS3 = DocumentsListesElements::setRegisterStreamWrapper();

		$uploader = new MultipartUploader($clientS3, "$path.zip", [
			'Bucket' => $S3_BUCKET,
			'Key' => "bucket1/$CodeClient/downloads/" . $CodeClient . '_documents.zip',
		]);

		$uploader->upload();

		$clientS3->putObjectAcl([
			'Bucket' => $S3_BUCKET,
			'Key' => "bucket1/$CodeClient/downloads/" . $CodeClient . '_documents.zip',
			'ACL' => 'public-read'
		]);

		$output = null;
		$ret = null;
		$cmd = "rm -f $path.zip 2>&1";
		exec($cmd, $output, $ret);
		if ($ret <> 0) {
			throw new Exception('Erreur lors de la suppression du fichier zip temporaire '.$cmd.getDebug($output));
		}

		return $fichersInexistants;
	}

	public static function makeZipFileByFicheCode()
	{
		global $S3_BUCKET, $CodeClient, $db;

		$path = '/var/www/temp/'.$CodeClient.'_documents';
		if (!is_dir($path)) {
			mkdir($path, 0777, true);
		}
		if (is_file("$path.zip")) {
			unlink("$path.zip");
		}

		$client = self::getAwsS3Client();
		$client->downloadBucket($path, $S3_BUCKET, $CodeClient . '/photos_fiches');

		$cmd = "zip -r -q -x .\* $path.zip $path/* 2>&1";
		exec($cmd, $output, $ret);
		if ($ret <> 0) {
			throw new Exception('Erreur lors de l\'export des documents '.$cmd.getDebug($output));
		}

		$output = null;
		$ret = null;
		$cmd = "rm -rf $path 2>&1";
		exec($cmd, $output, $ret);
		if ($ret <> 0) {
			throw new Exception('Erreur lors de la suppression du répertoire temporaire '.$cmd.getDebug($output));
		}

		return "$path.zip";
	}

	/**
	 * Retrieve a list of File resources.
	 *
	 * @param Google_Service_Drive $service Drive API service instance.
	 * @return Array List of Google_Service_Drive_DriveFile resources.
	 */
	public static function retrieveAllFiles($service) {
		$result = array();
		$pageToken = NULL;

		do {
			try {
				$parameters = array();
				if ($pageToken) {
					$parameters['pageToken'] = $pageToken;
				}
				$files = $service->files->listFiles($parameters);

				$result = array_merge($result, $files->getFiles());
				$pageToken = $files->getNextPageToken();
			} catch (Exception $e) {
				print "An error occurred: " . $e->getMessage();
				$pageToken = NULL;
			}
			sleep(2);
		} while ($pageToken);
		return $result;
	}

	public static function imageCreateFromFile($filename, $imageType)
	{

		switch ($imageType) {
			case IMAGETYPE_GIF  :
				return imagecreatefromgif($filename);
			case IMAGETYPE_JPEG :
				return imagecreatefromjpeg($filename);
			case IMAGETYPE_PNG  :
				return imagecreatefrompng($filename);
			default             :
				return false;
		}

	}

	/**
	 * Generates a thumbnail image using the file at $sourceFilename and either writing it
	 * out to a new file or directly to the browser.
	 *
	 * @param string $sourceFilename Filename for the image to have thumbnail made from
	 * @param integer $maxWidth The maxium width for the resulting thumbnail
	 * @param integer $maxHeight The maxium height for the resulting thumbnail
	 * @param string $targetFormatOrFilename Either a filename extension (gif|jpg|png) or the
	 *   filename the resulting file should be written to. This is optional and if not specified
	 *   will send a jpg to the browser.
	 * @return boolean true if the image could be created, false if not
	 */
	public static function generateThmbnail($sourceFilename, $maxWidth, $maxHeight, $targetFormatOrFilename = 'jpg')
	{
		$exif = @exif_read_data($sourceFilename);
		$size = getimagesize($sourceFilename); // 0 = width, 1 = height, 2 = type

		// check to make sure source image is in allowable format
		if (!in_array($size[2], self::allowableTypes)) {
			return false;
		}

		// work out the extension, what target filename should be and output function to call
		$pathinfo = pathinfo($targetFormatOrFilename);
		if ($pathinfo['basename'] == $pathinfo['filename']) {
			$extension = strtolower($targetFormatOrFilename);
			// set target to null so writes out to browser
			$targetFormatOrFilename = null;
		} else {
			$extension = strtolower($pathinfo['extension']);
		}

		switch ($extension) {
			case 'gif' :
				$function = 'imagegif';
				break;
			case 'png' :
				$function = 'imagepng';
				break;
			default    :
				$function = 'imagejpeg';
				break;
		}

		// load the image and return false if didn't work
		$source = self::imageCreateFromFile($sourceFilename, $size[2]);
		if (!$source) {
			return false;
		}

		// write out the appropriate HTTP headers if going to browser
		if ($targetFormatOrFilename == null) {
			if ($extension == 'jpg') {
				header("Content-Type: image/jpeg");
			} else {
				header("Content-Type: image/$extension");
			}
		}

		// if the source fits within the maximum then no need to resize
		if ($size[0] <= $maxWidth && $size[1] <= $maxHeight) {
			$function($source, $targetFormatOrFilename);
		} else {

			$ratioWidth = $maxWidth / $size[0];
			$ratioHeight = $maxHeight / $size[1];

			// use smallest ratio
			if ($ratioWidth < $ratioHeight) {
				$newWidth = $maxWidth;
				$newHeight = round($size[1] * $ratioWidth);
			} else {
				$newWidth = round($size[0] * $ratioHeight);
				$newHeight = $maxHeight;
			}

			$target = imagecreatetruecolor($newWidth, $newHeight);
			imagecopyresampled($target, $source, 0, 0, 0, 0, $newWidth, $newHeight, $size[0], $size[1]);

			if($exif && isset($exif['Orientation'])) {
				$orientation = $exif['Orientation'];
				if ($orientation != 1) {
					$deg = 0;
					switch ($orientation) {
						case 3:
							$deg = 180;
							break;
						case 6:
							$deg = 270;
							break;
						case 8:
							$deg = 90;
							break;
					}
					if ($deg) {
						$target = imagerotate($target, $deg, 0);
					}
				}
			}
			$function($target, $targetFormatOrFilename);
		}

		return true;
	}

    public static function afficheFormulaireMobile($nom_table, $liste_item_id)
    {
        $liste_item_id = $liste_item_id > 0 ? $liste_item_id : -1;
        $documents = DocumentsListesElements::getListe(
            array('nom_table' => $nom_table, 'liste_item_id' => $liste_item_id)
        );
        if (count($documents) > 0) {
            ?>
            <table style="margin-top: 22px;">
                <tr>
                    <td style="text-align: center!important; width: 22px;">&nbsp;</td>
                    <td style="text-align: left; font-weight: 700;"><?= txt('Nom') ?></td>
                </tr>
                <?php

                foreach ($documents as $document) {
                    ?>
                    <tr>
                        <td style="text-align: center!important;">
                            <?php
                            if (($nom_table == 'fiches' && (havePermission('can_edit_documents') || isMaster())) || $nom_table <> 'fiches') {
                                ?>
                                <a class="deleteDocument" data-document_id="<?= $document->id ?>"
                                   style="position: relative; top: 2px;"><img
                                        src="css/images/icons/dark/trashcan.png"/></a>
                                <?php
                            }
                            ?>
                        </td>
                        <td>
                            <a href="index.php?action=download_document&id=<?= $document->id ?>"
                               target="_blank"><?= $document->nom ?></a>
                        </td>
                    </tr>
                    </tr>
                    <?php
                }
                ?>
            </table>
            <?php
        }
        ?>
        <div id="ajoutImageContainer" style="max-width: 300px;">
        </div>
        <?php
    }

    public static function editeurPhotos($table, $liste_item_id, $afficheAucunePhotoLabel = true)
    {
        $photos = self::getListe(['nom_table' => $table, 'liste_item_id' => $liste_item_id]);
        if (count($photos) > 0) {
            foreach ($photos as $photo) {
                $photo->afficheEditeurPhoto(!$afficheAucunePhotoLabel);
            }
        } else if ($afficheAucunePhotoLabel) {
            print '<div style="margin-bottom: 50px;">' . txt('Aucune photos') . '</div>';
        }
    }

    public function afficheEditeurPhoto($hideEdition = false)
    {
        ?>
        <div id="thumb_preview_<?=$this->id?>" class="document" style="text-align: center; margin: 15px; float: left; width: 300px; height: 330px; position: relative;">
            <a href="index.php?action=download_document&id=<?=$this->id?>" target="_blank" style="display: inline-block; width: 300px; height: 300px;">
                <img src="index.php?action=download_document&thumbnail=300&id=<?=$this->id?>" style="max-width: 300px; max-height: 300px; transform-origin: center center; transform: <?=(in_array($this->rotation, [90, 270]) ? 'translateY(36px)' : '')?> rotate(<?=$this->rotation?>deg);"></a>
            <?php
            if (!$hideEdition) {
                ?>
                <div style="position: absolute; right: 10px;">
                    <a class="rotate" data-document_id="<?=$this->id?>"><img src="css/images/icons/dark/refresh_4.png" style="transform: rotate(<?=$this->rotation?>deg);" /></a>&nbsp;
                    <a class="deleteDocument" data-document_id="<?=$this->id?>"><img src="css/images/icons/dark/cross_3.png" /></a>
                </div>
                <?php
            }
            ?>
        </div>
        <?php
    }
}
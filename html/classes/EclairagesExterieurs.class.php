<?php

class EclairagesExterieurs extends FicheFormWrapper {
	private static $CLASS_NAME='EclairagesExterieurs';
	const SQL_TABLE_NAME='eclairages_exterieurs';
	const TITRE='Éclairage extérieur';
	public $id;
	public $fiche_id;
	public $eclairage_exterieur_type_id;
	public $eclairage_ratio_urgence_id;
	public $eclairage_exterieur_type;
	public $eclairage_ratio_urgence;
	public $vital;
	public $protection_personnes;
	public $installation_type_id;
	public $eclairage_controle_type_id;
	public $eclairage_lumens_type_id;
	public $principal_secondaire_id;
	public $installation_type;
	public $eclairage_controle_type;
	public $eclairage_lumens_type;
	public $principal_secondaire;
	public $qte_requise;
	public $quantite;
	public $quantite_controle_id;
	public $qte_controle;
	public $commentaires;
    
    public static function getChampsSaisieMetaData()
    {
        return array(
            
            'eclairage_exterieur_type' => array(
                'classe'      	=> 'EclairagesExterieursTypes',
                'table'      	=> 'eclairages_exterieurs_types',
                'champ_id'      => 'eclairage_exterieur_type_id',
                'type'          => 'select',
                'format'        => 'int',
                'titre'         => txt('Type d\'appareil', false),
                'css_editeur'   => 'large',
                'obligatoire'	=> 1,
            ),
            
            'principal_secondaire' => array(
                'classe'      	=> 'PrincipalsSecondaires',
                'table'      	=> 'principals_secondaires',
                'champ_id'      => 'principal_secondaire_id',
                'type'          => 'select',
                'format'        => 'int',
                'titre'         => txt('Princ. / Sec.', false),
                'css_editeur'   => 'medium'
            ),
/*          
            'eclairage_ratio_urgence' => array(
                'table'      	=> 'eclairages_ratios_urgences',
                'champ_id'      => 'eclairage_ratio_urgence_id',
                'type'          => 'select',
                'format'        => 'int',
                'titre'         => txt('Princ. / Sec.', false),
                'css_editeur'   => 'xxlarge'
            ),
*/            
            'installation_type' => array(
				'classe'      	=> 'InstallationsTypes',
				'table'      	=> 'installations_types',
                'champ_id'      => 'installation_type_id',
                'type'          => 'select',
                'format'        => 'int',
                'titre'         => txt('Installation', false),
                'css_editeur'   => 'medium'
            ),
            
            'eclairage_lumens_type' => array(
                'classe'      	=> 'EclairagesLumensTypes',
                'table'      	=> 'eclairages_lumens_types',
                'champ_id'      => 'eclairage_lumens_type_id',
                'type'          => 'select',
                'format'        => 'int',
                'titre'         => txt('Lumens', false),
                'css_editeur'   => 'medium'
            ),
            
            'qte_requise' => array(
				'classe'      	=> '',
				'table'      	=> '',
				'champ_id'      => 'qte_requise',
				'type'          => 'input',
				'format'        => 'int',
				'titre'         => txt('Quantité', false),
				'css_editeur'   => 'tiny'
			),
            
            'eclairage_controle_type' => array(
                'classe'      	=> 'EclairagesControlesTypes',
                'table'      	=> 'eclairages_controles_types',
                'champ_id'      => 'eclairage_controle_type_id',
                'type'          => 'select',
                'format'        => 'int',
                'titre'         => txt('Contrôle', false),
                'css_editeur'   => 'average'
            ),

            'qte_controle' => array(
                'classe'      	=> '',
                'table'      	=> '',
                'champ_id'      => 'qte_controle',
                'type'          => 'input',
                'format'        => 'int',
                'titre'         => txt('Qté contrôle', false),
                'css_editeur'   => 'tiny'
            ),
            
            'commentaires' => array(
				'table'      	=> '',
				'champ_id'      => 'commentaires',
				'type'          => 'input',
                'format'        => 'text',
                'titre'         => txt('Commentaires', false),
                'css_editeur'   => 'average'
            ),
            
        );
    }

	public static function getListe($filter = array(), $orderBy = 'ordre', $extra_data = array())
	{
        $extra_data = array(
            'eclairage_exterieur_type' => array('table' => 'eclairages_exterieurs_types'),
            'eclairage_ratio_urgence' => array('table' => 'eclairages_ratios_urgences'),
            'installation_type' => array('table' => 'installations_types'),
            'eclairage_controle_type' => array('table' => 'eclairages_controles_types'),
            'eclairage_lumens_type' => array('table' => 'eclairages_lumens_types'),
            'principal_secondaire' => array('table' => 'principals_secondaires'),
        );
        return parent::getListe($filter, $orderBy, $extra_data);
    }
}
<?php

class EclairagesParcs extends FicheFormWrapper {
	private static $CLASS_NAME='EclairagesParcs';
	const SQL_TABLE_NAME='eclairages_parcs';
	const TITRE='Éclairage de parc';
	public $id;
	public $fiche_id;
	public $eclairage_parc_type_id;
	public $eclairage_ratio_urgence_id;
	public $eclairage_parc_type;
	public $eclairage_ratio_urgence;
	public $vital;
	public $protection_personnes;
	public $installation_type_id;
	public $eclairage_controle_type_id;
	public $eclairage_lumens_type_id;
	public $principal_secondaire_id;
	public $installation_type;
	public $eclairage_controle_type;
	public $eclairage_lumens_type;
	public $principal_secondaire;
	public $qte_requise;
	public $quantite;
	public $quantite_controle_id;
	public $qte_controle;
	public $commentaires;
    
    public static function getChampsSaisieMetaData()
    {
        return array(
            
            'eclairage_parc_type' => array(
                'classe'      	=> 'EclairagesParcsTypes',
                'table'      	=> 'eclairages_parcs_types',
                'champ_id'      => 'eclairage_parc_type_id',
                'type'          => 'select',
                'format'        => 'int',
                'titre'         => txt('Type d\'appareil', false),
                'css_editeur'   => 'large',
                'obligatoire'	=> 1,
            ),
            
            'principal_secondaire' => array(
                'classe'      	=> 'PrincipalsSecondaires',
                'table'      	=> 'principals_secondaires',
                'champ_id'      => 'principal_secondaire_id',
                'type'          => 'select',
                'format'        => 'int',
                'titre'         => txt('Princ. / Sec.', false),
                'css_editeur'   => 'medium'
            ),
           
/*          
            'eclairage_ratio_urgence' => array(
                'table'      	=> 'eclairages_ratios_urgences',
                'champ_id'      => 'eclairage_ratio_urgence_id',
                'type'          => 'select',
                'format'        => 'int',
                'titre'         => txt('Princ. / Sec.', false),
                'css_editeur'   => 'xxlarge'
            ),
*/             
            'installation_type' => array(
				'classe'      	=> 'InstallationsTypes',
				'table'      	=> 'installations_types',
                'champ_id'      => 'installation_type_id',
                'type'          => 'select',
                'format'        => 'int',
                'titre'         => txt('Installation', false),
                'css_editeur'   => 'medium'
            ),
            
            'eclairage_lumens_type' => array(
                'classe'      	=> 'EclairagesLumensTypes',
                'table'      	=> 'eclairages_lumens_types',
                'champ_id'      => 'eclairage_lumens_type_id',
                'type'          => 'select',
                'format'        => 'int',
                'titre'         => txt('Lumens', false),
                'css_editeur'   => 'medium'
            ),
            
            'qte_requise' => array(
				'classe'      	=> '',
				'table'      	=> '',
				'champ_id'      => 'qte_requise',
				'type'          => 'input',
				'format'        => 'int',
				'titre'         => txt('Quantité', false),
				'css_editeur'   => 'tiny'
			),
            
            'eclairage_controle_type' => array(
                'classe'      	=> 'EclairagesControlesTypes',
                'table'      	=> 'eclairages_controles_types',
                'champ_id'      => 'eclairage_controle_type_id',
                'type'          => 'select',
                'format'        => 'int',
                'titre'         => txt('Contrôle', false),
                'css_editeur'   => 'average'
            ),

            'qte_controle' => array(
                'classe'      	=> '',
                'table'      	=> '',
                'champ_id'      => 'qte_controle',
                'type'          => 'input',
                'format'        => 'int',
                'titre'         => txt('Qté contrôle', false),
                'css_editeur'   => 'tiny'
            ),
            
            'commentaires' => array(
				'table'      	=> '',
				'champ_id'      => 'commentaires',
				'type'          => 'input',
                'format'        => 'text',
                'titre'         => txt('Commentaires', false),
                'css_editeur'   => 'average'
            ),
            
        );
    }
}
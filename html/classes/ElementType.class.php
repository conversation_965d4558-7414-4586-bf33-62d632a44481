<?php
class ElementType extends DbRow {
	public $id;
	public $nom;
	public $isValide;
	public $code_client_ajout;
	public $usager_ajout_id;
	public $ts_ajout;
	public $code_ajout;
	public $ts_valide;
	public $note_ajout;
	public $ts_sync;
	public $ts_delete;
	
	protected function setDataForId($id)
	{
        global $db;
        
		$sql = "select *,
				case when ts_valide is null then 0 else 1 end as isValide
                from elements_types 
                where id = ?";
		$row = $db->getRow($sql, array($id));
		
		$this->populateThisWithThat($row);
	}
	
	public static function getListe($filter = array(), $orderBy = 'nom')
	{
        global $db;
        
        $where = '';
        $data = array();
        
		$sql = "select *,
				case when ts_valide is null then 0 else 1 end as isValide
                from elements_types 
                where true $where
                order by $orderBy";
		$rows = $db->getAll($sql, $data);
		
		$liste = array();
		foreach($rows as $i => $row)
		{
			$liste[intval($row['id'])] = new self(intval($row['id']), $row);
		}
		
		return $liste;
	}
	
    public function getEditorHTML($iteration = 0, $only_champs_denormalises = false)
	{
?>			
		<section>
            <label for="nom"><?php print txt('Nom'); ?></label>
            <div>
                <input id="nom" type="text" name="nom" value="<?php print ($this->nom); ?>" />
            </div>
        </section>
<?php
	}
	
	public static function getDynamicTableTitle()
	{
?>
		{ "sTitle": "<?php print txt('Nom'); ?>", "sClass": "dataTableLargetTitle" },
<?php
	}
	
	public function getDynamicTableContent()
	{
?>
		"<?php print dm_addslashes($this->getRaw('nom')); ?>"
<?php
	}
	
	public static function getOptions($filter = array(), $selected = 0)
	{
        $rows = self::getListe($filter, 'nom');
        foreach($rows as $i => $row)
        {
            $sel = $row->id == $selected ? ' selected="selected"' : '';
?>
            <option value="<?php print $row->id; ?>"<?php print $sel; ?>><?php print $row->nom; ?></option>
<?php
        }
	}
    
    public static function delete($id)
	{
        global $db;
        $db->autocommit(false);
        $db->query("DELETE FROM `elements_types` WHERE id = ?", array($id));
        $db->commit();
	}
    
    public static function validate($data, $id)
	{
        $valide = true;
        foreach($data as $colomnName => $value)
        {
            if($colomnName == 'nom')
            {
                if(dm_strlen($value) == 0)
                {
                    setErreur(txt('Le nom est obligatoire.'));
                    $valide = false;
                }
            }
        }
        return $valide;
	}
}
?>
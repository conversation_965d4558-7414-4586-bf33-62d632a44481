<?php
class EquipementsMep extends DbRow {
    use GestionnairePage;
    public $id;
    public $projet_id;
    public $code;
    public $description;
    public $ordre;
    public $has_historique_non_lu;
    public $barcode_nouveau;
    public $marque_id;
    public $modele_id;
    public $numero_serie;
    public $date_fabrication;
    public $hauteur;
    public $largeur;
    public $profondeur;
    public $type_installation_id;
    public $condition_vetuste_id;
    public $remarque;
    public $condition_vetuste;
    public $commentaire;

    /**
     * @param int $id - ??
     * @param null $row - ??
     * @throws Exception
     */
    public function __construct($id, $row = null)
    {
        if ($id == 0) {
            $sql = "SELECT `COLUMN_NAME`, `DATA_TYPE`
                    FROM `INFORMATION_SCHEMA`.`COLUMNS` 
                    WHERE `TABLE_SCHEMA`='" . Config::instance()->dbName . "' 
                        AND `TABLE_NAME`='equipements_mep'
                        order by DATA_TYPE;";
            $cols = db::instance()->getAll($sql, []);

            foreach ($cols as $data) {
                $col_name = $data['COLUMN_NAME'];
                $this->$col_name = ($data['DATA_TYPE'] == 'int' ? 0 : '');
            }
        } else {
            if (isset($row) && (is_object($row) || is_array($row))) {
                $this->populateThisWithThat($row);
            } else {
                $this->setDataForId($id);
            }
        }
    }

    protected function setDataForId($id): void
    {
        $sql = "select tb.*
                from equipements_mep tb
                where tb.id = ?";
        $row = db::instance()->getRow($sql, array($id));

        $this->populateThisWithThat($row);
    }

    private static function getFiltreData($filter)
    {
        $where = "";
        $data = [];

        if (isset($filter['text_search']) && dm_strlen($filter['text_search']) > 0) {
            $where .= " and (exists (select 1 from equipements_mep_valeurs where equipement_mep_id = tb.id and valeur like ?) or tb.description like ? or tb.code like ?)";
            $data[] = "%" . $filter['text_search'] . "%";
            $data[] = "%" . $filter['text_search'] . "%";
            $data[] = "%" . $filter['text_search'] . "%";
        }

        if (isset($filter['mise_en_service_id'])) {
            $where .= " and tb.mise_en_service_id = ?";
            $data[] = intval($filter['mise_en_service_id']);
        }

        if (isset($filter['procedure_id'])) {
            $where .= " and exists (select 1 from procedures_mises_en_service_mep_equipements t where t.equipement_mep_id = tb.id and t.procedure_id = ?)";
            $data[] = intval($filter['procedure_id']);
        }

        if(isset($filter['have_no_procedure']))
        {
            if ($filter['have_no_procedure'] == 1) {
                $where .= " and not exists (select 1 from procedures_mises_en_service_mep_equipements pige where pige.equipement_mep_id = tb.id)";
            } else {
                $where .= " and exists (select 1 from procedures_mises_en_service_mep_equipements pige where pige.equipement_mep_id = tb.id)";
            }
        }

        if(isset($filter['have_no_planif']))
        {
            if ($filter['have_no_planif'] == 1) {
                $where .= " and not exists (select 1 from procedures_mises_en_service_planifiees_mep_equipements pige where pige.equipement_mep_id = tb.id)";
            } else {
                $where .= " and exists (select 1 from procedures_mises_en_service_planifiees_mep_equipements pige where pige.equipement_mep_id = tb.id)";
            }
        }

        if (isset($filter['ids_to_exclude']) && count($filter['ids_to_exclude']) > 0) {
            $where .= " and tb.id not in (" . db::placeHolders($filter['ids_to_exclude']) . ")";
            $data = array_merge($data, $filter['ids_to_exclude']);
        }

        if (isset($filter['ids'])) {
            if (count($filter['ids']) > 0) {
                $where .= " and tb.id in (" . db::placeHolders($filter['ids']) . ")";
                $data = array_merge($data, $filter['ids']);
            } else {
                $where .= " and false";
            }
        }

        return array($where, $data);
    }

    /**
     * @param array $filter - Filters
     * @param string $orderBy - Order by
     * @return mixed
     * @throws Exception
     */
    public static function getCount($filter = array(), $orderBy = 'id')
    {
        list($where, $data) = self::getFiltreData($filter);

        $sql = "select count(*)
                from equipements_mep tb
                where true $where";
        return db::instance()->getOne($sql, $data);
    }


    /**
     * @param array $filter - Filters
     * @param string $orderBy - Order
     * @param string $sql_limit - Limit Offset
     * @return EquipementsMep[]
     * @throws Exception
     */
    public static function getListe(array $filter = [], string $orderBy = 'tb.id', string $sql_limit = ''): array
    {
        list($where, $data) = self::getFiltreData($filter);

        $sql = "select tb.*, cvt.nom as condition_vetuste
                from equipements_mep tb
                left join conditions_vetustes_types cvt on cvt.id = tb.condition_vetuste_id
                where true $where
                order by $orderBy
                $sql_limit";
        $rows = db::instance()->getAll($sql, $data);

        $key = 'id';
        if (isset($filter['key_to_use'])) {
            $key = $filter['key_to_use'];
        }
        $liste = array();
        if (isset($filter['for_json'])) {
            foreach ($rows as $i => $row) {
                if (isset($row[$filter['id_to_use']]) && strlen($row[$filter['id_to_use']]) > 0) {
                    $row["index"] = $i;
                    $liste[$row[$key]] = ['id' => $row[$filter['id_to_use']], 'text' => $row[$filter['text_to_use']]];
                }
            }
        } else {
            foreach ($rows as $i => $row) {
                $row["index"] = $i;
                $liste[$row[$key]] = new self(intval($row['id']), $row);
            }
        }
        return $liste;
    }

    public static function getOptions($filter = array(), $selected = 0, $includeEmpty = false)
    {
        if ($includeEmpty) {
            ?>
            <option value=""></option>
            <?php
        }
        $rows = self::getListe($filter);
        foreach ($rows as $i => $row) {
            $sel = $row->id == $selected ? ' selected="selected"' : '';
            ?>
            <option value="<?= $row->id ?>"<?= $sel ?>><?= $row->code . " - " . $row->description ?></option>
            <?
        }
    }

    public static function getListeSimple($filter = [], $orderBy = 'tb.id', $sql_limit = ''): array
    {
        list($where, $data) = self::getFiltreData($filter);

        $sql = "select tb.*
                from equipements_mep tb
                where true $where
                order by $orderBy
                $sql_limit";
        $rows = db::instance()->getAll($sql, $data);

        $key = 'id';
        if (isset($filter['key_to_use'])) {
            $key = $filter['key_to_use'];
        }
        $liste = array();
        if (isset($filter['for_json'])) {
            foreach ($rows as $i => $row) {
                $row["index"] = $i;
                $liste[$row[$key]] = ['id' => $row[$filter['id_to_use']], 'text' => $row[$filter['text_to_use']]];
            }
        } else {
            foreach ($rows as $i => $row) {
                $row["index"] = $i;
                $liste[$row[$key]] = new self(intval($row['id']), $row);
            }
        }
        return $liste;
    }

    public static function getListeValeurs($filter = [], $orderBy = 'equipement_mep_id', $raw = false)
    {
        list($where, $data) = self::getFiltreData($filter);

        $sql = "select tb.id as equipement_mep_id, ic.id as champ_id, iv.valeur, tb.code
                from equipements_mep tb
                join equipements_mep_valeurs iv on iv.equipement_mep_id = tb.id
                join equipements_mep_champs ic on iv.equipement_mep_champ_id = ic.id
                where true $where
                order by $orderBy";
        $rows = db::instance()->getAll($sql, $data);

        $key = 'equipement_mep_id';
        if (isset($filter['key_to_use'])) {
            $key = $filter['key_to_use'];
        }

        $liste = array();
        foreach ($rows as $i => $row) {
            $iter = $key <> 'index' ? $row[$key] : $i;
            $liste[$iter][(int)$row['champ_id']] = $raw ? $row->getRaw('valeur') : $row['valeur'];
        }
        return $liste;
    }

    public static function getResumeToolTip($equipement_mep_id, $mise_en_service_id)
    {
        $valeurs = EquipementsMep::getListeValeurs(['equipement_mep_id' => $equipement_mep_id]);
        $valeurs = $valeurs[$equipement_mep_id] ?? [];
        $mise_en_service = new MisesEnServices($mise_en_service_id);

        $html = '';
        foreach ($mise_en_service->champs as $champ) {
            $html .= '<div style="margin-bottom: 5px;">' . $champ->nom . ': <b style="color: blue;">' . ($valeurs[$champ->id] ?? '-') . "</b></div>";
        }
        return $html;
    }

    public static function delete($id): void
    {
        db::instance()->autocommit(false);
        db::instance()->query("DELETE FROM `equipements_mep_valeurs` WHERE equipement_mep_id = ?", array($id));
        db::instance()->query("DELETE FROM `equipements_mep` WHERE id = ?", array($id));
        db::instance()->commit();
    }

    /**
     * @param array $filter
     * @param bool $includeEmpty
     * @return false|string
     */
    public static function getJsonOptions($filter = array(), $includeEmpty = false)
    {
        $filter['for_json'] = 1;
        $json_array = [];
        if ($includeEmpty) {
            $json_array[] = ['id' => '', 'text' => ''];
        }
        $json_array = array_merge($json_array, array_values(self::getListe($filter)));
        return json_encode($json_array);
    }

    public static function getFormMobileHTML($equipement_mep_id, $is_from_mobile = true)
    {
        ?>
        <fieldset class="noMinHeight">
            <style>
                fieldset {
                    padding: 5px;
                }

                input[type=text], select, textarea {
                    min-width: 250px;
                    max-width: 250px;
                    width: 250px;
                }

                fieldset.cotacote section > div {
                    margin: 0 0 10px 0 !important;
                    padding: 0 0 0 0 !important;
                }

                fieldset.cotacote label {
                    width: 200px !important;
                    margin: 0 0 5px 0 !important;
                    padding: 0 0 0 0 !important;
                }

                fieldset.cotacote textarea {
                    height: 50px !important;
                    min-height: 50px !important;
                }

                section {
                    margin: 0 0 0 0 !important;
                    padding: 0 0 0 0 !important;
                }

                .select2-search-choice-close {
                    top: 3px !important;
                }
            </style>
            <fieldset class="noMinHeight cotacote">
                <?php
                if ($is_from_mobile) {
                    ?>
                    <section class="formulaire_general">
                        <label></label>
                        <div style="">
                            <select id="equipement_mep_id" name="equipement_mep_id"></select>
                        </div>
                    </section>
                    <?php
                } else {
                    ?>
                    <input type="hidden" name="equipement_mep_id" value="<?= $equipement_mep_id ?>" />
                    <?php
                }
                $miseEnService = MisesEnServices::getMiseEnServiceCourant();
                ?>
                <input type="hidden" name="mise_en_service_id" value="<?= $miseEnService->id ?>" />
                <?
                $miseEnService = MisesEnServices::getMiseEnServiceCourant();
                $ligne = new self($equipement_mep_id);
                $champsActifs = $miseEnService->getChampsActifs();
                foreach (EquipementsMep::getChamps() as $nomChamp => $meta) {
                    if (in_array($nomChamp, $champsActifs)) {
                        ?>
                        <section class="formulaire_general">
                            <label style=""><?= $meta['titre'] ?></label>
                            <div style="display: inline-block; position: relative;">
                                <? self::afficheChampEdition($nomChamp, $meta, $ligne->$nomChamp) ?>
                            </div>
                        </section>
                        <?
                    }
                }

                $valeurs = EquipementsMep::getListeValeurs(['equipement_mep_id' => $equipement_mep_id]);
                foreach ($miseEnService->champs as $champ) {
                    if (in_array($champ->id, $champsActifs)) {
                        ?>
                        <section class="formulaire_general">
                            <label style=""><?= $champ->nom ?></label>
                            <div style="">
                                <? self::afficheChampEdition('equipements_mep_valeurs[' . $equipement_mep_id . '-' . $champ->id . ']', ['type' => 'inputText'], (isset($valeurs[$equipement_mep_id]) && isset($valeurs[$equipement_mep_id][$champ->id]) ? $valeurs[$equipement_mep_id][$champ->id] : '')) ?>
                            </div>
                        </section>
                        <?
                    }
                }
                ?>
                <br style="clear: both;">
            </fieldset>
            <br style="clear: both;">
        </fieldset>
        <script>
			$(document).ready(function () {

				load_locaux_apartenance();

				$('select.is-select2').select2({width: '250px', allowClear: true});

				$('select.is-select2').on('change', function (evt) {
					$('.select2-focusser').blur();
				});
				$('select.is-select2').on('select2-close', function (evt) {
					setTimeout(() => {
						$('.select2-focusser').blur();
					}, 10);
				});
				$('select.is-select2').on('select2-closing', function (evt) {
					setTimeout(() => {
						$('.select2-focusser').blur();
					}, 10);
				});

				if (<?= ($is_from_mobile ? 'true' : 'false') ?>) {
					$.startObjectLoader($('#fiche_id'), 'ligne_id_loader');
					$.get('index.php', {
						action: 'getEquipementsMep'
					}, function (json_data) {
						loading_dynamic_combobox('equipement_mep_id', <?= $equipement_mep_id ?>, json_data);
						$.stopObjectLoader('ligne_id_loader');

						$('#equipement_mep_id').on('change', function (evt) {
							window.location = `index.php?section=releveMobile&module=inventaire&ligne_id_sel=${this.value}`;
						});
					}, 'json');
				}

				$('#marque_id').on('change', function () {
					reloadModeles(0);
				});

				$('#ajoutImage').click(function () {
					$('#ajoutImageContainer').append('<input type="file" name="images[]" accept="image/gif, image/jpeg, image/png" capture />');
					parent.fancybox_resize();
				});

				setTimeout(() => {
					$('.select2-search input').prop('focus', false);
				}, 100);
			});

			function load_locaux_apartenance() {
				const url = 'index.php?section=fiche&module=index';
				$.get(url, { action: 'getFichesList', term: '', batiment_id: '', fiche_type_id: '', fiche_sous_type_id: 2000002, numero_lot: '', code_alternatif: '', id_revit: '', id_codebook: '', procedure_entretien_id: '', selected: '', disable_fitre_avance: 1 }, function(json_data){
					loading_dynamic_combobox('fiche_local_id', $('#fiche_local_id').val(), json_data);
				}, 'json');
			}

			function reloadMarques(selected_id) {
				const url = 'index.php';
				$.get(url, {action: 'getMarques', selected_id: selected_id}, function (html) {
					$('#marque_id').html(html);
					$('#marque_id').select2('destroy');
					$('#marque_id').select2({
						width: '250px',
						allowClear: true
					});

					$('#marque_id').on('change', function (evt) {
						$('.select2-focusser').blur();
					});
				});
			}

			function reloadModeles(selected_id) {
				const url = 'index.php';
				$.get(url, {
					action: 'getModelesFromMarque',
					marque_id: $('#marque_id').val(),
					selected_id: selected_id
				}, function (html) {
					$('#modele_id').html(html);
					$('#modele_id').select2('destroy');
					$('#modele_id').select2({
						width: '250px',
						allowClear: true
					});

					$('#modele_id').on('change', function (evt) {
						$('.select2-focusser').blur();
					});
				});
			}

			function openMarquePopup() {
				$.fancybox({
					'overlayShow': false,
					'type': 'iframe',
					'modal': true,
					'centerOnScroll': true,
					'width': 1024,
					'href': 'index.php?section=admin&module=edit&table=marques&type=client&id=0&popupMobile=1&callback=reloadMarques',
					'onComplete': function () {
						$('#fancybox-frame').load(function () { // wait for frame to load and then gets it's height
							fancybox_resize(300);
						});
					},
					'onClosed'			:	function(){
						$('html').css({ overflow: 'auto' });
					}
				});
			}

			function openModelePopup() {
				const marque_id = $('#marque_id').val();
				$.fancybox({
					'overlayShow': false,
					'type': 'iframe',
					'modal': true,
					'centerOnScroll': true,
					'width': 1024,
					'href': 'index.php?section=admin&module=edit&table=modeles&type=client&id=0&popupMobile=1&callback=reloadModeles' + (marque_id > 0 ? '&marque_id=' + marque_id : ''),
					'onComplete': function () {
						$('#fancybox-frame').load(function () { // wait for frame to load and then gets it's height
							fancybox_resize(300);
						});
					},
					'onClosed'			:	function(){
						$('html').css({ overflow: 'auto' });
					}
				});
			}
        </script>
        <?php
    }

    public function getEditorHTML($iteration = 0, $only_champs_denormalises = false)
    {
        self::getFormMobileHTML($this->id, false);
    }

    public static function validate($data, $fiche_id)
    {
        return true;
    }

    public static function afficheChampEdition($nomChamp, $meta, $valeur)
    {
        switch ($meta['type']) {
            case 'inputText':
                ?>
                <input type="text" id="<?= $nomChamp ?>" name="<?= $nomChamp ?>" value="<?= $valeur ?>" autocomplete="off" class="<?= ($meta['htmlClass'] ?? '') ?>">
                <?
                break;
            case 'textarea':
                ?>
                <textarea id="<?= $nomChamp ?>" name="<?= $nomChamp ?>" style="min-height: 100px; overflow: hidden; overflow-wrap: break-word; resize: none; height: 50px;" class="<?= ($meta['htmlClass'] ?? '') ?>"><?= $valeur ?></textarea>
                <?
                break;
            case 'select':
                if ($nomChamp == 'inventaire_local_id' && isset($_GET['inventaire_local_id']) && (int)($valeur ?? 0) == 0) {
                    $valeur = (int)$_GET['inventaire_local_id'];
                }
                ?>
                <select id="<?= $nomChamp ?>" name="<?= $nomChamp ?>" class="<?= ($meta['htmlClass'] ?? '') ?>">
                    <option value=""></option>
                    <?
                    if (isset($meta['classe'])) {
                        $classe = $meta['classe'];
                        $classe::getOptions(($meta['filtre'] ?? []), ($valeur ?? 0));
                    }
                    ?>
                </select>
                <?
                break;
            case 'date':
                ?>
                <input type="text" id="<?= $nomChamp ?>" name="<?= $nomChamp ?>"
                       value="<?= $valeur ?>"
                       style="min-width: 75px!important; width: 75px!important; text-align: center;"
                       class="date hasDatepicker <?= ($meta['htmlClass'] ?? '') ?>" autocomplete="off" />
                <?
                break;
            case 'float':
                ?>
                <input type="number" id="<?= $nomChamp ?>" name="<?= $nomChamp ?>" value="<?= $valeur ?>"
                       style="min-width: 75px!important; width: 75px!important; text-align: right;"
                       class="justInteger <?= ($meta['htmlClass'] ?? '') ?>" autocomplete="off" />
                &nbsp;<?= $meta['unite'] ?>
                <?
                break;
        }
        if (isset($meta['htmlExtra'])) {
            print $meta['htmlExtra'];
        }
    }

    public static function getChamps()
    {
        return [
            'code' => [
                'titre' => txt('Code de l\'équipement', false),
                'type' => 'inputText',
            ],
            'description' => [
                'titre' => txt('Description de l\'équipement', false),
                'type' => 'inputText',
            ],
            'barcode_nouveau' => [
                'titre' => txt('Numéro étiquette', false),
                'type' => 'inputText',
            ],
            'marque_id' => [
                'titre' => txt('Marque', false),
                'type' => 'select',
                'classe' => 'Marque',
                'table' => 'marques',
                'colonneCompare' => 'nom',
                'htmlExtra' => '<a href="javascript: openMarquePopup();" style="position: absolute; top: -1px;" class="fancy_small">
                                    <img src="css/images/icons/dark/plus.png"></a>',
                'htmlClass' => 'is-select2'
            ],
            'modele_id' => [
                'titre' => txt('Modèle', false),
                'type' => 'select',
                'classe' => 'Modele',
                'table' => 'modeles',
                'colonneCompare' => 'numero_modele',
                'htmlExtra' => '<a href="javascript: openModelePopup();" style="position: absolute; top: -1px;" class="fancy_small">
                                    <img src="css/images/icons/dark/plus.png"></a>',
                'htmlClass' => 'is-select2'
            ],
            'numero_serie' => [
                'titre' => txt('Numéro de série', false),
                'type' => 'inputText',
            ],
            'date_fabrication' => [
                'titre' => txt('Date de fabrication', false),
                'type' => 'date',
            ],
            'hauteur' => [
                'titre' => txt('Hauteur', false),
                'type' => 'float',
                'unite' => 'mm',
            ],
            'largeur' => [
                'titre' => txt('Largeur', false),
                'type' => 'float',
                'unite' => 'mm',
            ],
            'profondeur' => [
                'titre' => txt('Profondeur', false),
                'type' => 'float',
                'unite' => 'mm',
            ],
            'type_installation_id' => [
                'titre' => txt('Type d\'installation', false),
                'type' => 'select',
                'classe' => 'InstallationsTypes',
                'table' => 'installations_types',
                'colonneCompare' => 'nom',
                'htmlClass' => 'is-select2'
            ],
            'condition_vetuste_id' => [
                'titre' => txt('Condition de l\'équipement', false),
                'type' => 'select',
                'classe' => 'ConditionsVetustesTypes',
                'table' => 'conditions_vetustes_types',
                'colonneCompare' => 'nom',
                'htmlClass' => 'is-select2'
            ],
            'remarque' => [
                'titre' => txt('Commentaires', false),
                'type' => 'textarea',
            ],
        ];
    }

    public static function traitementSauvegardeSupplementaire($id, $requestData, $is_ajout)
    {
        $valeurs = EquipementsMep::getListeValeurs(['equipement_mep_id' => $id]);
        if (isset($requestData['equipements_mep_valeurs'])) {
            foreach ($requestData['equipements_mep_valeurs'] as $key => $value) {
                list($equipement_mep_id, $equipement_mep_champ_id) = explode('-', $key);
                if (isset($valeurs[$equipement_mep_id]) && isset($valeurs[$equipement_mep_id][$equipement_mep_champ_id])) {
                    $sql = "UPDATE equipements_mep_valeurs SET valeur = ? WHERE equipement_mep_id = ? AND equipement_mep_champ_id = ?";
                } else {
                    $sql = "INSERT INTO equipements_mep_valeurs (valeur, equipement_mep_id, equipement_mep_champ_id) VALUES (?, ?, ?)";
                }
                db::instance()->query($sql, [$value, $id, $equipement_mep_champ_id]);
            }
        }
    }

    /**
     * @return GestionnaireInterface
     */
    public static function getClasseContexte(): string
    {
        /** @var GestionnaireInterface $currentClassName */
        $currentClassName = get_called_class();
        return $currentClassName;
    }

    public static function getListeForDatatable($modeSelection = false): array
    {
        $listeForDatatable = [];
        $listeEquipements = $_POST['listeEquipements'] ?? [];

        $assocs = [];
        if (count($listeEquipements) > 0) {
            $assocs = self::getListe(['ids' => $listeEquipements]);
        }

        $filtre = $_SESSION['filtresGestionnairePage_' . self::getClasseContexte()];
        if (isset($_GET['table']) && $_GET['table'] == 'procedures_mises_en_service_planifiees_mep') {
            $filtre['have_no_planif'] = 1;
        } else if ($_GET['module'] == 'aTraiterPlanifications') {
            $filtre['have_no_planif'] = 1;
        } else if (isset($_GET['table']) && $_GET['table'] == 'procedures_mises_en_service_mep') {
            $filtre['have_no_procedure'] = 1;
        } else if ($_GET['module'] == 'aTraiterProcedures') {
            $filtre['have_no_procedure'] = 1;
        }

        $listeFiltre = self::getListe($filtre);
        foreach ($assocs as $row) {
            $id = $row->id;
            $listeForDatatable[] = [
                $row->code,
                $row->description,
                <<<HTML
            <input type="checkbox" name="listeEquipements[]" class="checks" value="{$id}" checked="checked" />
HTML
            ];
        }

        foreach ($listeFiltre as $row) {
            $id = $row->id;
            if (!isset($assocs[$id])) {
                $buttonsCheckbox = <<<HTML
                <a href="index.php?section=admin&module=edit&table=equipements_mep&type=client&id={$row->id}&from_gestionnaire=1" class="fancy">
                    <img src="css/images/icons/dark/create_write.png"></a>
                <a class="dle_btn dle_delete_generique" tableName="equipements_mep" idElement="{$row->id}">
                    <img src="css/images/icons/dark/trashcan.png" /></a>
HTML;
                if ($modeSelection) {
                    $buttonsCheckbox = <<<HTML
            <input type="checkbox" name="listeEquipements[]" class="checks" value="{$id}" />
HTML;
                }
                $listeForDatatable[] = [
                    $row->code,
                    $row->description,
                    $buttonsCheckbox
                ];
            }
        }
        return $listeForDatatable;
    }

    public static function getListeColonnes($modeSelection = false): array
    {
        return [
            'code_equipement' => [
                'titre' => txt('Code de l\'équipement', false),
                'isData' => true
            ],
            'description_equipement' => [
                'titre' => txt('Description', false),
                'isData' => true
            ],
            'boutons' => [
                'titre' => $modeSelection ? '' : '<a href="index.php?section=admin&module=edit&id=0&table=equipements_mep&type=client&from_gestionnaire=1" class="fancy_big" title="' . txt('Ajouter un équipement MEP') . '">
                                <img src="css/images/icons/dark/plus.png"></a>',
                'isData' => false
            ],
        ];
    }

    public static function afficheCustomStyle()
    {
        ?>
        <style>
            .div_filtre {
                float: left;
                margin: 5px;
                line-height: 27px;
            }
            .div_filtre > div {
                vertical-align: bottom!important;
            }
            #datatable_filter {
                display: none;
            }
        </style>
        <?
    }

    public static function afficheJavascript()
    {
        ?>
        <script src="js/jquery.shiftcheckbox.js?ts=<?=filemtime('js/jquery.shiftcheckbox.js')?>"></script>
        <script>
			$(document).ready(function () {
				$(document).on('click', '#all', function(){
					$('input.checks').prop('checked', this.checked);
				});
				$('input.checks').shiftcheckbox();

				$(document).on('click', '#insert', function(){
					if($('input.checks:checked').length == 0)
					{
						alert('<?=txt('Vous devez sélectionner au moins un équipement dans la grille ci-dessous.', false)?>');
					} else {
						// fake.php pour initialiser le fancybox avant de pousser le form form_equipements_checked dans le iframe
						$.fancybox({
							'href'              :   'fake.php',
							'overlayShow'		:	false,
							'type'				:	'iframe',
							'modal'				:	true,
							'centerOnScroll'	:	true,
							'width'             :   1024,
							'onComplete'		:	function(){
								let loaded = false;
								$('#fancybox-frame').load(function() {

									// ici on fait un tour de passe-passe pour pousser le data des checkbox en POST plutôt qu'en GET
									if (!loaded) {
										let url = 'index.php?section=admin&module=edit&table=procedures_mises_en_service_mep&type=client&id=0&stayOpen=1';
										$('#form_equipements_checked').attr('target', $('#fancybox-frame').attr('name'));
										$('#form_equipements_checked').attr('action', url);
										$('#form_equipements_checked').submit();
										loaded = true;
									} else {
										fancybox_resize(1400);
									}
								});
							},
							'onClosed'			:	function(){
								$('html').css({ overflow: 'auto' });
							}
						});
					}
					return false;
				});

				$(document).on('click', '#update', function(){
					if ($('input.checks:checked').length === 0) {
						alert('<?=txt('Vous devez sélectionner au moins un équipement dans la grille ci-dessous.', false)?>');
					} else {
						$("#dialog-ajout-procedure-existante").dialog({
							resizable: false,
							height: 325,
							width: 550,
							modal: true,
							buttons: {
								"Ajouter": function () {
									if($('#procedure_id').val() == 0) {
										alert('<?=txt('Vous devez sélectionner une procédure.', false)?>');
									} else {
										const r = confirm('<?=txt("Êtes-vous certain de vouloir ajouter ce(s) équipement(s) à cette procédure?", false)?>');
										if (r === true) {
											after_insert_procedure($('#procedure_id').val());
										}
									}
									$(this).dialog("close");
								},
								"Annuler": function () {
									$(this).dialog("close");
								}
							}
						});
					}
				});



				$(document).on('click', '#insertPlanif', function(){
					if($('input.checks:checked').length == 0)
					{
						alert('<?=txt('Vous devez sélectionner au moins un équipement dans la grille ci-dessous.', false)?>');
					} else {
						// fake.php pour initialiser le fancybox avant de pousser le form form_equipements_checked dans le iframe
						$.fancybox({
							'href'              :   'fake.php',
							'overlayShow'		:	false,
							'type'				:	'iframe',
							'modal'				:	true,
							'centerOnScroll'	:	true,
							'width'             :   1024,
							'onComplete'		:	function(){
								let loaded = false;
								$('#fancybox-frame').load(function() {

									// ici on fait un tour de passe-passe pour pousser le data des checkbox en POST plutôt qu'en GET
									if (!loaded) {
										let url = 'index.php?section=admin&module=edit&table=procedures_mises_en_service_planifiees_mep&type=client&id=0&stayOpen=1';
										$('#form_equipements_checked').attr('target', $('#fancybox-frame').attr('name'));
										$('#form_equipements_checked').attr('action', url);
										$('#form_equipements_checked').submit();
										loaded = true;
									} else {
										fancybox_resize(1400);
									}
								});
							},
							'onClosed'			:	function(){
								$('html').css({ overflow: 'auto' });
							}
						});
					}
					return false;
				});

				$(document).on('click', '#updatePlanif', function(){
					if ($('input.checks:checked').length === 0) {
						alert('<?=txt('Vous devez sélectionner au moins un équipement dans la grille ci-dessous.', false)?>');
					} else {
						$("#dialog-ajout-planification-existante").dialog({
							resizable: false,
							height: 325,
							width: 550,
							modal: true,
							buttons: {
								"Ajouter": function () {
									if($('#planification_id').val() == 0) {
										alert('<?=txt('Vous devez sélectionner une planification.', false)?>');
									} else {
										const r = confirm('<?=txt("Êtes-vous certain de vouloir ajouter ce(s) équipement(s) à cette planification?", false)?>');
										if (r === true) {
											after_insert_procedure_planif($('#planification_id').val());
										}
									}
									$(this).dialog("close");
								},
								"Annuler": function () {
									$(this).dialog("close");
								}
							}
						});
					}
				});
			});

			function after_insert_procedure(procedure_id)
			{
				const data = $('#form_equipements_checked').serialize() + '&procedure_id=' + procedure_id;
				const url = 'index.php?action=update_procedures_mises_en_service_mep_non_traites';
				$.post(url, data, function(html){
					if (html === 'ok') {
						window.location.href = "index.php?section=equipements_mep&module=aTraiterProcedures&apres_update_equipement_non_traites=1";
					} else {
						alert('<?= txt('Une erreur est survenue. Veuillez réessayer ultérieurement', false) ?>');
					}
				});
			}

			function after_insert_procedure_planif(planification_id)
			{
				const data = $('#form_equipements_checked').serialize() + '&planification_id=' + planification_id;
				const url = 'index.php?action=update_planifications_mises_en_service_mep_non_traites';
				$.post(url, data, function(html){
					if (html === 'ok') {
						window.location.href = "index.php?section=equipements_mep&module=aTraiterPlanifications&apres_update_equipement_non_traites=1";
					} else {
						alert('<?= txt('Une erreur est survenue. Veuillez réessayer ultérieurement', false) ?>');
					}
				});
			}
        </script>
        <?php
    }

    public static function getListeFiltres()
    {
        return [
            [
                'titre' => txt('Équipements MEP'),
                'nomChamp' => 'code',
                'type' => 'champ_multiple',
                'largeur' => '300',
                'filtreData' => [
                    'classe' => "EquipementsMep",
                    'filtre' => []
                ]
            ],
            'boutons' => [
                'titre' => txt('Filtrer', false),
                'type' => 'boutonNonSubmit',
            ]
        ];
    }

    public static function getUrl(): string
    {
        return '';
    }

    public static function getAutresOptions(): array
    {
        return [];
    }

    public static function getTitre(): string
    {
        return txt('Mise en service - Équipements MEP');
    }

    public static function afficheTitreEtOnglets(): void
    {
        ?>
        <div style="float: right;">
            <?php
            foreach (self::getOngletData() as $onglet) {
                if (isset($onglet['conditionAffichage']) && $onglet['conditionAffichage']) {
                    ?>
                    <a class="<?= $onglet['classes'] ?> <?=($onglet['actif'] ? 'actif' : '')?>" href="<?= $onglet['url'] ?>"><?= $onglet['titre'] ?></a>
                    <?php
                } else if (isset($onglet['separation'])) {
                    ?>
                    <div style="display: inline-block; border-left: solid 1px; margin-left: 14px; margin-right: 10px;">&nbsp;</div>
                    <?php
                }
            }
            ?>
        </div>
        <?php
    }

    public static function getOngletData(): array
    {
        $mise_en_service = MisesEnServices::getMiseEnServiceCourant();
        return [
            [
                'titre' => txt('Équipements MEP'),
                'url' => 'index.php?section=equipements_mep&module=index',
                'conditionAffichage' => true,
                'actif' => ($_GET['module'] == 'index'),
                'classes' => 'btn',
            ],
            [
                'titre' => txt('Gestion des champs'),
                'url' => 'index.php?section=admin&module=edit&table=mises_en_services&type=client&id=' . $mise_en_service->id,
                'conditionAffichage' => true,
                'actif' => false,
                'classes' => 'btn fancy',
            ],
            [
                'titre' => txt('Importation'),
                'url' => 'index.php?section=equipements_mep&module=import&id=' . $mise_en_service->id,
                'conditionAffichage' => true,
                'actif' => false,
                'classes' => 'btn fancy',
            ],
            [
                'separation' => 1,
            ],
            [
                'titre' => txt('Non-traités'),
                'url' => 'index.php?section=equipements_mep&module=aTraiterProcedures',
                'conditionAffichage' => true,
                'actif' => ($_GET['module'] == 'aTraiterProcedures'),
                'classes' => 'btn',
            ],
            [
                'titre' => txt('Procédures'),
                'url' => 'index.php?section=equipements_mep&module=procedures',
                'conditionAffichage' => GestionnaireRoute::instance()->haveAccesRoute('inspection-procedures'),
                'actif' => ($_GET['module'] == 'procedures'),
                'classes' => 'btn',
            ],
            [
                'separation' => 1,
            ],
            [
                'titre' => txt('Non-planifiés'),
                'url' => 'index.php?section=equipements_mep&module=aTraiterPlanifications',
                'conditionAffichage' => true,
                'actif' => ($_GET['module'] == 'aTraiterPlanifications'),
                'classes' => 'btn',
            ],
            [
                'titre' => txt('Planification'),
                'url' => 'index.php?section=equipements_mep&module=planification',
                'conditionAffichage' => GestionnaireRoute::instance()->haveAccesRoute('inspection-historique'),
                'actif' => ($_GET['module'] == 'planification'),
                'classes' => 'btn',
            ],
            [
                'titre' => txt('Calendrier'),
                'url' => 'index.php?section=equipements_mep&module=calendrier',
                'conditionAffichage' => GestionnaireRoute::instance()->haveAccesRoute('inspection-historique'),
                'actif' => ($_GET['module'] == 'calendrier'),
                'classes' => 'btn',
            ]
        ];
    }

    public static function afficheTitre(): void
    {
        ?>
        <h1 class="ficheTitre" style="margin-bottom: 6px; position: relative;">
            <?= self::getClasseContexte()::getTitre() ?>
            <?php
            foreach (self::getClasseContexte()::getAutresOptions() as $option) {
                ?>
                <a class="<?= $option['classes'] ?>" href="<?= $option['url'] ?>"<?= ($option['classes'] == 'fancy' ? ' style="position: relative; top: 4px;"' : '') ?>><img src="<?= $option['image'] ?>" /></a>
                <?php
            }
            ?>
            <div style="display: inline-block; position: absolute; right: 10px; top: 12px;  z-index: 1;">
                <?
                include('inspection/onglets.inc.php');
                ?>
            </div>
        </h1>
        <?php
    }
}

<?php
class ExportsAutomatiques extends DbRow
{
    const MODULE_ACQUISITION = 'acquisition';
    const MODULE_FINANCEMENT = 'financement';

    public int $id = 0;
    public int $projet_id = 0;
    public string $nom = '';
    public string $module = '';
    public int $modele_id = 0;
    public string $jours_semaine = '';
    public string $heures = '';
    public int $is_actif = 1;

    protected function setDataForId($id): void
    {
        $sql = "SELECT ea.*
                FROM exports_automatiques ea
                where ea.id = ?";
        $row = db::instance()->getRow($sql, array($id));

        $this->populateThisWithThat($row);
    }

    /**
     * @param array $filter
     * @param string $orderBy
     * @return ExportsAutomatiques[]
     */
    public static function getListe(array $filter = array(), string $orderBy = 'nom'): array
    {
        $where = '';
        $data = [];
        if (getProjetId() > 0) {
            $where = ' and projet_id = ?';
            $data = array(getProjetId());
        }
        $sql = "SELECT ea.*
                FROM exports_automatiques ea
                where true $where
                order by $orderBy";
        $rows = db::instance()->getAll($sql, $data);

        $liste = [];
        foreach ($rows as $row) {
            $liste[intval($row['id'])] = new self(intval($row['id']), $row);
        }

        return $liste;
    }

    public function getJoursSemaine()
    {
        return strlen($this->jours_semaine) ? json_decode($this->getRaw('jours_semaine'), true) : [];
    }

    public function getHeures()
    {
        return strlen($this->heures) ? json_decode($this->getRaw('heures'), true) : [];
    }

    public function getEditorHTML($iteration = 0, $only_champs_denormalises = false): void
    {
        ?>
        <section>
            <label for="nom"><?= txt('Nom') ?></label>
            <div>
                <input id="nom" type="text" name="nom" value="<?= ($this->nom) ?>" />
            </div>
        </section>

        <section>
            <label for="module"><?= txt('Module') ?></label>
            <div>
                <select id="module" name="module">
                    <option value="">- <?php print txt('Sélectionnez le module'); ?> -</option>
                    <?php
                    foreach (self::getModulesDisponibles() as $module => $nomModule) {
                        ?>
                        <option value="<?= $module ?>"<?= ($this->module == $module ? ' selected="selected"' : '') ?>><?= $nomModule ?></option>
                        <?php
                    }
                    ?>
                </select>
            </div>
        </section>

        <section>
            <label for="modele_id"><?= txt('Modèle') ?></label>
            <div>
                <select id="modele_id" name="modele_id">
                </select>
            </div>
        </section>

        <section>
            <label for="jours_semaine"><?= txt('Jour(s) de la semaine') ?></label>
            <div>
                <select id="jours_semaine" name="jours_semaine[]" multiple style="height: 160px!important;">
                    <?php
                    foreach (self::getTousLesJoursSemaine() as $jour => $nomJour) {
                        ?>
                        <option value="<?= $jour ?>"<?= (in_array($jour, $this->getJoursSemaine()) ? ' selected="selected"' : '') ?>><?= $nomJour ?></option>
                        <?php
                    }
                    ?>
                </select>
            </div>
        </section>

        <section>
            <label for="heures"><?= txt('Heure(s)') ?></label>
            <div>
                <table style="margin-bottom: 10px; width: 700px;" class="formFieldsTable">

                    <tbody data-cur_iteration="<?=(count($this->getHeures()) + 1)?>">
                    <?php
                        $heures = $this->getHeures();
                        if (empty($heures)) {
                            $heures = [''];
                        }
                        foreach ($heures as $heure) {
                            ?>
                            <tr class="editable">
                                <td style="padding: 5px!important;">
                                    <span class="delSortBtns">
                                        <a class="delete">
                                            <img src="css/images/icons/dark/trashcan.png" /></a>
                                    </span>
                                </td>
                                <td style="padding: 5px!important;">
                                    <input type="time" name="heures[]" value="<?= $heure ?>" style="width: 100px!important;" />
                                </td>
                            </tr>
                            <?php
                        }
                    ?>
                    </tbody>

                    <tfoot>
                    <tr class="top_block">
                        <td colspan="2" style="padding: 2px!important;">
                            <a href="javascript: void(0);" class="add_line" style="">
                                <img src="css/images/icons/dark/plus.png" /></a>
                        </td>
                    </tr>
                    </tfoot>

                </table>
            </div>
        </section>

        <section>
            <label for="is_actif"><?php print txt('Actif'); ?></label>
            <div>
                <input id="is_actif" type="checkbox" name="is_actif" value="1" <?= ($this->is_actif == 1 ? ' checked="checked"' : '') ?> />
            </div>
        </section>

        <?php if ($this->id > 0) { ?>
            <section>
                <label for="url"><?php print txt('URL de téléchargement'); ?></label>
                <div style="position: relative; top: -13px;">
                    <span id="lienTelechargement"><?= $this->getLienTelechargement() ?></span>
                    <img src="css/images/icons/dark/copy.png" style="cursor: pointer; position: relative; top: 7px;" id="copy" data-id_to_copy="lienTelechargement" title="<?= txt('Copier') ?>" />
                    <a href="<?= $this->getLienTelechargement() ?>" target="_blank" id="lienTelechargement" title="<?= txt('Exporter') ?>">
                        <img src="css/images/icons/dark/DM_export_CSV.png" style="cursor: pointer; position: relative; top: 5px;" /></a>
                </div>
            </section>
        <?php } ?>

        <script>
            const modelesParModule = <?= json_encode(self::getModelesParModule()) ?>;
			let selected_modele_id = <?= $this->modele_id ?>;
            majModeles();
            $('#module').change(function(){
                majModeles();
				selected_modele_id = 0;
            });

			$('#copy').click(function(e){
				copyToClipboard('#' + $(this).data('id_to_copy'));
			});
            function majModeles() {
                $('#modele_id').html('');
                const module = $('#module').val();
                if (modelesParModule[module]) {
                    const modeles = modelesParModule[module];
                    $('#modele_id').append(`<option value="">- <?= txt('Sélectionnez le modèle') ?> -</option>`);
                    for (const modele_id in modeles) {
                        const modele = modeles[modele_id];
                        $('#modele_id').append(`<option value="${modele_id}"${modele_id == selected_modele_id ? ' selected="selected"' : ''}>${modele.nom}</option>`);
                    }
                }
            }
        </script>
        <?php

    }

    public static function getDynamicTableTitle()
    {
        ?>
        { "sTitle": "<?= txt('Nom', false) ?>", "sClass": "dataTableShortTitle" },
        { "sTitle": "<?= txt('Module', false) ?>", "sClass": "dataTableShortTitle" },
        { "sTitle": "<?= txt('Modèle', false) ?>", "sClass": "dataTableAvgtTitle" },
        { "sTitle": "<?= txt('Jour(s)', false) ?>", "sClass": "dataTableAvgtTitle" },
        { "sTitle": "<?= txt('Heure(s)', false) ?>", "sClass": "dataTableAvgtTitle" },
        { "sTitle": "<?= txt('Actif', false) ?>", "sClass": "dataTableAvgtTitle" },
        <?php
    }

    public function getDynamicTableContent()
    {
        ?>
        "<?= dm_addslashes($this->getRaw('nom')) ?>",
        "<?= dm_addslashes($this->getNomModule()) ?>",
        "<?= dm_addslashes($this->getNomModele()) ?>",
        "<?= dm_addslashes(implode(', ', $this->getJoursSemaine())) ?>",
        "<?= dm_addslashes(implode(', ', $this->getHeures())) ?>",
        "<?= $this->is_actif == 1 ? 'Oui' : 'Non' ?>"
        <?php
    }

    public static function delete($id): void
    {
        db::instance()->autocommit(false);
        db::instance()->query("DELETE FROM `exports_automatiques` WHERE id = ?", array($id));
        db::instance()->commit();
    }

    public function getNomModele()
    {
        $sql = "select nom
                from `" . $this->getTableModele() . "`
                where id = ?";
        return db::instance()->getOne($sql, [$this->modele_id]);
    }

    public function getNomModule(): string
    {
        return match ($this->module) {
            self::MODULE_ACQUISITION => txt('Acquisitions'),
            self::MODULE_FINANCEMENT => txt('Financements'),
            default => '',
        };
    }

    public static function getTableModele_static(string $module): string
    {
        return match ($module) {
            self::MODULE_ACQUISITION => 'acquisitions_projets_exports_modeles',
            self::MODULE_FINANCEMENT => 'financements_projets_exports_modeles',
            default => '',
        };
    }

    public function getTableModele(): string
    {
        return self::getTableModele_static($this->module);
    }

    public function getChampsToExport(): array
    {
        $sql = "select * from `{$this->getTableModele()}` where id = ?";
        $modele = db::instance()->getRow($sql, [$this->modele_id]);
        return json_decode($modele->getRaw('json_data'), true);
    }

    public static function getModulesDisponibles(): array
    {
        return [
            self::MODULE_ACQUISITION => txt('Acquisitions'),
            self::MODULE_FINANCEMENT => txt('Financements'),
        ];
    }

    public static function getModelesParModule() {
        $modelesParModule = [];
        foreach (ExportsAutomatiques::getModulesDisponibles() as $module => $nomModule) {
            $sql = "select id, nom
                    from `" . self::getTableModele_static($module) . "`
                    where ouvrage_id = ?
                    order by nom";
            $modelesParModule[$module] = db::instance()->getAssoc($sql, [getOuvrageId()]);
        }
        return $modelesParModule;
    }

    public static function validate($data, $id): bool
    {
        $valide = true;
        foreach ($data as $colomnName => $value) {
            if ($colomnName == 'nom') {
                if (dm_strlen($value) == 0) {
                    setErreur(txt('Le nom est obligatoire.'));
                    $valide = false;
                }
            } else if ($colomnName == 'module') {
                if (dm_strlen($value) == 0) {
                    setErreur(txt('Le module est obligatoire.'));
                    $valide = false;
                }
            } else if ($colomnName == 'modele_id') {
                if (dm_strlen($value) == 0) {
                    setErreur(txt('Le modèle est obligatoire.'));
                    $valide = false;
                }
            } else if ($colomnName == 'jours_semaine') {
                if (count($value) == 0) {
                    setErreur(txt('Vous devez sélectionner au moins 1 jour de semaine.'));
                    $valide = false;
                }
            } else if ($colomnName == 'heures') {
                if (count($value) == 0) {
                    setErreur(txt('Vous devez sélectionner au moins 1 heure.'));
                    $valide = false;
                } else {
                    foreach ($value as $heure) {
                        if (empty($heure)) {
                            setErreur(txt('Vous devez saisir une heure sur chaque ligne ajouté.'));
                            $valide = false;
                            break;
                        }
                    }
                }
            }
        }
        return $valide;
    }

    public static function getTousLesJoursSemaine(): array
    {
        return [
            'dimanche' => txt('Dimanche'),
            'lundi' => txt('Lundi'),
            'mardi' => txt('Mardi'),
            'mercredi' => txt('Mercredi'),
            'jeudi' => txt('Jeudi'),
            'vendredi' => txt('Vendredi'),
            'samedi' => txt('Samedi'),
        ];
    }

    public static function getTousLesJoursSemaineByIndex(): array
    {
        return [
            'dimanche',
            'lundi',
            'mardi',
            'mercredi',
            'jeudi',
            'vendredi',
            'samedi',
        ];
    }

    public static function traitementSauvegardeSupplementaire($id, $data, $is_ajout, $objAnciennesValeurs = null)
    {
        $sql = "update exports_automatiques set is_actif = ?, heures = ?, jours_semaine = ? where id = ?";
        db::instance()->query($sql, [(isset($data['is_actif']) ? 1 : 0), json_encode($data['heures']), json_encode($data['jours_semaine']), $id]);
    }

    public function getNomFichier(): string
    {
        $nom_fichier = "synchro_" . $this->getRaw('module') . "_" . str_replace(' ', '_', $this->getRaw('nom'));
        $nom_fichier = dm_str_replace("'", "", $nom_fichier);
        $nom_fichier = strip_accents($nom_fichier);
        return dm_preg_replace("/[^a-zA-Z0-9\-_]+/", "", $nom_fichier);
    }

    public function getLienTelechargement(): string
    {
        return "https://docmatic.os-qc1.cirrusproject.ca/bucket1/" . Config::instance()->codeClient . "/synchro_ia/" . $this->getNomFichier() . ".xlsx";
    }
}
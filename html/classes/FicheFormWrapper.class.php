<?php

/**
 * Class FicheFormWrapper
 */
class FicheFormWrapper extends DbRow
{
    /**
     *
     */
    const CAN_DELETE=true;
    /**
     * @var string
     */
    public static $TYPE_FORM='LISTE';
    /**
     *
     */
    const CAN_HAVE_COORDINATION=false;
    /**
     *
     */
    const SQL_TABLE_NAME='';
    /**
     *
     */
    const SQL_TABLE_NAME_COORD='';
    /**
     *
     */
    const SQL_COLOMN_NAME_COORD='';
    /**
     * @var
     */
    public $fiche_type_id;
    /**
     * @var
     */
    public $fiche_sous_type_id;
    /**
     * @var
     */
    public $batiment_id;

    /**
     *
     */
    public static function getChampsSaisieMetaData()
    {

    }

    /**
     * @param $id
     */
    protected function setDataForId($id)
    {
        global $db, $db_name, $global_fiche_type_id, $global_fiche_sous_type_id, $global_batiment_id;

        /** @var self $currentClassName */
        $currentClassName = get_called_class();
        $table_name = $currentClassName::SQL_TABLE_NAME;

        if ($id == 0) {
            $sql = "SELECT `COLUMN_NAME`, `DATA_TYPE`
                    FROM `INFORMATION_SCHEMA`.`COLUMNS` 
                    WHERE `TABLE_SCHEMA`='" . $db_name . "' 
                        AND `TABLE_NAME`='" . $table_name . "'
                        order by DATA_TYPE;";
            $cols = $db->getAll($sql, array());

            foreach ($cols as $data) {
                $col_name = $data['COLUMN_NAME'];
                if ($col_name == 'statut') {
                    $this->$col_name = 1;
                } else {
                    if ($data['DATA_TYPE'] == 'int') {
                        $this->$col_name = 0;
                    } else {
                        $this->$col_name = '';
                    }
                }
            }
        } else {
            $sql = "select *
                    from " . $table_name . " 
                    where id = ?";
            $row = $db->getRow($sql, array($id));

            $this->populateThisWithThat($row);
        }

        $this->fiche_type_id = $global_fiche_type_id;
        $this->fiche_sous_type_id = $global_fiche_sous_type_id;
        $this->batiment_id = $global_batiment_id;
    }

    /**
     * @param array $filter
     * @param string $orderBy
     * @param null $extra_data
     * @return array
     */
    public static function getListe($filter = array(), $orderBy = 'ordre', $extra_data = null)
    {
        global $db, $db_archive, $buffer, $db_name, $Langue, $forms_with_qte, $formulaire_id;

        /** @var self $currentClassName */
        $currentClassName = get_called_class();
        $table_name = $currentClassName::SQL_TABLE_NAME;

        $db_n = $db_name;
        $db_na = $db_name.'_archive';

        /*if(isset($buffer["liste_$currentClassName"]))
        {
            $liste = $buffer["liste_$currentClassName"];
        }
        else*/
        {
            if(!isset($extra_data))
            {
                $metas = $currentClassName::getFlatChampsSaisieMetaData();

                if(isset($metas) && is_array($metas))
                {
                    foreach($metas as $key =>$meta)
                    {
                        if($meta['type'] == 'select' && !isset($meta['liste']))
                        {
                            $extra_data[$key] = array('table' => $meta['table'], 'alias' => 'alias_'.uniqid());
                            if(isset($meta['sql_for_nom']))
                            {
                                $extra_data[$key]['sql_for_nom'] = self::formatSqlForNom($meta['sql_for_nom'], $extra_data[$key]['alias']);
                            }
                        }
                    }
                }
            }

            $extra_cols = '';
            $extra_joins = array();

            if(isset($extra_data) && is_array($extra_data))
            {
                $iter_alias = 1;
                foreach($extra_data as $nom => $data)
                {
                    $defaut_nom = ($data['table'] == 'modeles' ? 'numero_modele' : getChampNomFromLangue($data['table']));
                    $alias = isset($data['alias']) ? $data['alias'] : $data['table'].'_'.$iter_alias;
                    if($data['table'] == 'fiches')
                    {
                        $extra_cols .= $data['table'] == 'fiches' ? ', concat('.$alias.'.code, (case when '.$alias.'.ts_delete is not null then \' (fiche supprimée)\' else \'\' end)) as '.$nom : ', '.$alias.'.nom as '.$nom;
                    }
                    else
                    {
                        $extra_cols .= isset($data['sql_for_nom']) ? ', '.$data['sql_for_nom'].' as '.$nom : ', '.$alias.'.'.$defaut_nom.' as '.$nom;//', '.$alias.'.'.$defaut_nom.' as '.$nom;
                    }
                    $extra_joins[] = 'left join '.$db_n.'.'.$data['table'].' as '.$alias.' on '.$db_n.'.'.$alias.'.id = '.$table_name.'.'.$nom.'_id';
                    $iter_alias++;
                }
            }

            if(isset($filter['is_for_archive']) && !isset($filter['token_archive']))
            {
                $forms_with_qte = isset($forms_with_qte) ? $forms_with_qte : Formulaires::getFormulairesIdWithQuantite();
                $formulaire_id = isset($formulaire_id) ? $formulaire_id : Formulaires::getIdFromNom($currentClassName);
                if(in_array($formulaire_id, $forms_with_qte))
                {
                    $extra_cols .= " , case when quantite_id = 999999 then $table_name.id + quantite_id else $table_name.id end as id";
                }
            }

            $where = '';
            $data = array();

            if(isset($filter['fiche_id']))
            {
                $where .= " and `" . db::tableColonneValide($table_name) . "`.fiche_id = ?";
                $data[] = (int)($filter['fiche_id']);
            }

            if(isset($filter['projet_id']))
            {
                $where .= " and `" . db::tableColonneValide($table_name) . "`.projet_id = ?";
                $data[] = (int)($filter['projet_id']);
            }

            if(isset($filter['fiches_ids']))
            {
                $where .= " and `" . db::tableColonneValide($table_name) . "`.fiche_id in (".db::placeHolders($filter['fiches_ids']).")";
                $data = array_merge($data, $filter['fiches_ids']);
            }

            if(isset($filter['liste_item_ids']))
            {
                $formulaire_id = $currentClassName::getFormulaireId();
                $form = new Formulaires($formulaire_id);
                $first_champ = $form->getFirstChamp();
                $where .= " and `" . db::tableColonneValide($table_name) . "`." . $first_champ['champ_id'] . " in (".db::placeHolders($filter['liste_item_ids']).")";
                $data = array_merge($data, $filter['liste_item_ids']);
            }

            if(isset($filter['statut']))
            {
                $where .= " and statut = ?";
                $data[] = (int)($filter['statut']);
            }

            $db_to_use = $db;
            $db_n_to_use = $db_n;
            if(isset($filter['token_archive']))
            {
                if(dm_strlen($filter['token_archive']) > 0)
                {
                    $where .= " and token_archive = ?";
                    $data[] = $filter['token_archive'];
                }
                else
                {
                    $where .= " and token_archive is null";
                }

                db_archive();
                $db_to_use = $db_archive;
                $db_n_to_use = $db_na;
            } else {
                if (in_array($currentClassName, ['EquipementsBioMedicaux', 'EquipementsBioMedicauxNoCoord'])) {
                    for ($iterDepot = 1; $iterDepot <= 5; $iterDepot++) {
                        $extra_cols .= ', depot' . $iterDepot . '.id as depot_id' . $iterDepot;
                        $extra_cols .= ', depot' . $iterDepot . '.nom as depot' . $iterDepot;
                        $extra_cols .= ', case when depot' . $iterDepot . '.ts_verrouille is not null then 1 else 0 end as isVerrouilleDepot' . $iterDepot;
                        $extra_cols .= ', case when depot' . $iterDepot . '.ts_photo_j0 is not null then depot' . $iterDepot . '.id else 0 end as isPhotoJ0Depot' . $iterDepot;
                        $extra_joins[] = 'left join ' . $db_n . '.financements_projets_depots as depot' . $iterDepot . ' on ' . $db_n . '.depot' . $iterDepot . '.id = ' . $table_name . '.depot_source' . $iterDepot . '_id';
                    }

                    if (isset($filter['extraireProjetDepotFinancement'])) {
                        for ($iterDepot = 1; $iterDepot <= 5; $iterDepot++) {
                            $extra_cols .= ', projet' . $iterDepot . '.numero as numero_projet' . $iterDepot;
                            $extra_joins[] = 'left join ' . $db_n . '.financements_projets as projet' . $iterDepot . ' on ' . $db_n . '.projet' . $iterDepot . '.id = ' . $db_n . '.depot' . $iterDepot . '.financement_projet_id';
                        }
                    }

                    $extra_cols .= ", prix_unitaire_nouveau";

                    if (isset($filter['explodedByNumeroInventaire'])) {
                        $extra_cols .= ", ife.numero_inventaire";
                        $extra_joins[] = 'left join ' . $db_n . '.inventaires_fiches_equipements as ife on ife.fiche_id = ' . $table_name . '.fiche_id and ife.equipement_bio_medicaux_type_id = ' . $table_name . '.equipement_bio_medicaux_type_id ';
                    }

                    if (isset($filter['is_coordination_electricite']) || isset($filter['is_coordination_telecom'])) {
                        $col = isset($filter['is_coordination_electricite']) ? 'is_coordination_electricite' : 'is_coordination_telecom';
                        $extra_joins[] = "join equipements_bio_medicaux_types_projets ebmtp on ebmtp.liste_item_id = equipement_bio_medicaux_type_id and ebmtp.projet_id = " . getProjetId() . " and $col = 1";
                    }

                    if (isset($filter['acquisition_projet_id']) && $filter['acquisition_projet_id'] > 0) {
                        $sql = "select distinct equipement_bio_medicaux_type_id from acquisitions_projets_groupes_equipements where acquisition_projet_id = ?";
                        $equipement_bio_medicaux_type_ids = $db->getCol($sql, [$filter['acquisition_projet_id']]);
                        $where .= " and equipement_bio_medicaux_type_id in (".db::placeHolders($equipement_bio_medicaux_type_ids).")";
                        $data = array_merge($data, $equipement_bio_medicaux_type_ids);
                    }

                    $orderBy = getColonneTriEquipementsGBM();
                    if ($orderBy == 'equipement_bio_medicaux_type_description') {
                        $aliasToUse = $extra_data['equipement_bio_medicaux_type']['alias'];
                        $extra_cols .= ", coalesce($aliasToUse.description, '') as equipement_bio_medicaux_type_description";
//                        $extra_joins[] = 'left join ' . $db_n . '.equipements_bio_medicaux_types as ebmt on ebmt.id = equipement_bio_medicaux_type_id';
                    }
                }
            }

            $sql = "select ".$table_name.".* $extra_cols
                    from ".$db_n_to_use.".".$table_name." 
                    ".dm_implode("\n", $extra_joins)."
                    where true $where 
                    order by $orderBy";
//            printDebug(dm_nl2br(db::interpolateQuery($sql, $data)));exit;
            $rows = $db_to_use->getAll($sql, $data);

            $key_for_liste = isset($filter['code_pivot_ligne_for_key']) ? $filter['code_pivot_ligne_for_key'] : 'id';
            $liste = array();
            foreach($rows as $i => $row)
            {
                if(in_array($currentClassName, array('GenerauxEquipements', 'GenerauxInstruments')))
                {
                    $row['coord_x'] = dm_strlen($row['coord_x']) > 0 ? dm_trim($row['coord_x'], '0') : $row['coord_x'];
                    $row['coord_y'] = dm_strlen($row['coord_y']) > 0 ? dm_trim($row['coord_y'], '0') : $row['coord_y'];
                    $row['coord_z'] = dm_strlen($row['coord_z']) > 0 ? dm_trim($row['coord_z'], '0') : $row['coord_z'];
                }
                $liste[$row[$key_for_liste]] = new $currentClassName((int)($row['id']), $row);
            }

            $buffer["liste_$currentClassName"] = $liste;
        }
        return $liste;
    }

    /**
     * Retourne une liste d'équipements avec seulement les champs spécifiés
     *
     * @param array $filter Filtres à appliquer
     * @param string $orderBy Ordre de tri
     * @param array $champsSelectionnes Liste des champs à retourner
     * @return array Liste des équipements avec seulement les champs sélectionnés
     */
    public static function getListeAvecChampsSpecifiques($filter = array(), $orderBy = 'id', $champsSelectionnes = [])
    {
        // Récupérer la liste complète
        $listeComplete = self::getListe($filter, $orderBy);

        // Filtrer les champs pour chaque objet
        $listeFiltree = [];
        foreach ($listeComplete as $key => $equipement) {
            $equipementFiltre = new stdClass();

            foreach ($champsSelectionnes as $champ) {
                if (property_exists($equipement, $champ)) {
                    $equipementFiltre->$champ = $equipement->$champ;
                }
            }

            $listeFiltree[$key] = $equipementFiltre;
        }

        return $listeFiltree;
    }

    /**
     * @param $sql_for_nom
     * @param $alias
     * @return mixed
     */
    public static function formatSqlForNom($sql_for_nom, $alias)
    {
        global $Langue;

        $search = array('%alias%', 'fiches_sous_types', 'metrique', 'imperial', 'standard', 'code', 'nom,', 'nom_en,', 'kva_types.'.$alias.'.nom', 'kva_types.'.$alias.'.nom_en', 'nombres_phases_types.'.$alias.'.nom', 'nombres_phases_types.'.$alias.'.nom_en', 'tensions_types.'.$alias.'.nom', 'tensions_types.'.$alias.'.nom_en', 'nomenclatures.nom_'.$Langue, 'nombre_poles', 'numero_modele');
        $replace = array($alias, $alias, $alias.'.metrique', $alias.'.imperial', $alias.'.standard', $alias.'.code', $alias.'.nom,', $alias.'.nom_en,', $alias.'.nom', $alias.'.nom_en', 'nombres_phases_types.nom', 'nombres_phases_types.nom_en', 'tensions_types.nom', 'tensions_types.nom_en', $alias.'.nom_'.$Langue, $alias.'.nombre_poles', $alias.'.numero_modele');

        return dm_str_replace($search, $replace, $sql_for_nom);
    }

    /**
     * @param $fiche_id
     * @return mixed
     */
    public static function getOne($fiche_id)
    {
        global $db, $db_name, $Langue;

        /** @var self $currentClassName */
        $currentClassName = get_called_class();
        $table_name = $currentClassName::SQL_TABLE_NAME;

        $info_get_from_list = array();
        $extra_cols = '';
        $extra_joins = array();
        if(isset($currentClassName::$AUTO) && $currentClassName::$AUTO)
        {
            $metas = $currentClassName::getFlatChampsSaisieMetaData();

            $extra_data = array();
            foreach($metas as $key =>$meta)
            {
                if(isset($meta['liste']))
                {
                    $info_get_from_list[$meta['champ_id']]['liste'] = $meta['liste'];
                    $info_get_from_list[$meta['champ_id']]['key'] = $key;
                }
                else if($meta['type'] == 'select')
                {
                    $extra_data[$key] = array('table' => $meta['table'], 'alias' => 'alias_'.uniqid());
                    if(isset($meta['sql_for_nom']))
                    {
                        $extra_data[$key]['sql_for_nom'] = self::formatSqlForNom($meta['sql_for_nom'], $extra_data[$key]['alias']);
                    }
                }
            }

            $iter_alias = 1;
            foreach($extra_data as $nom => $data)
            {
                $defaut_nom = ($data['table'] == 'modeles' ? 'numero_modele' : getChampNomFromLangue($data['table']));
                $alias = isset($data['alias']) ? $data['alias'] : $data['table'].'_'.$iter_alias;
                $extra_cols .= isset($data['sql_for_nom']) ? ', '.$data['sql_for_nom'].' as '.$nom : ', '.$alias.'.'.$defaut_nom.' as '.$nom;//', '.$alias.'.'.$defaut_nom.' as '.$nom;
                $extra_joins[] = 'left join '.$db_name.'.'.$data['table'].' as '.$alias.' on '.$db_name.'.'.$alias.'.id = '.$table_name.'.'.$nom.'_id';
                $iter_alias++;
            }
            /*
                        if(in_array($currentClassName, array('GenerauxInstruments', 'GenerauxEquipements')))
                        {
                            $extra_cols .= ', marques.nom as marque, marque_id';
                            $extra_joins[] = 'left join '.$db_name.'.marques on '.$db_name.'.marques.id = marque_id';
                        }
             */
        }

        $where = '';
        $data = array();

        $where .= " and fiche_id = ?";
        $data[] = $fiche_id;

        $sql = "select ".$table_name.".* $extra_cols
                from ".$table_name." 
                ".dm_implode("\n", $extra_joins)."
                where true $where";
        //printDebug(dm_nl2br(db::interpolateQuery($sql, $data)));
        $row = $db->getRow($sql, $data);

        if(isset($row))
        {
            $obj = new $currentClassName((int)($row['id']), $row);
            if(in_array($currentClassName, array('GenerauxEquipements', 'GenerauxInstruments')))
            {
                $obj->coord_x = dm_strlen($obj->coord_x) > 0 ? dm_trim($obj->coord_x, '0') : $obj->coord_x;
                $obj->coord_y = dm_strlen($obj->coord_y) > 0 ? dm_trim($obj->coord_y, '0') : $obj->coord_y;
                $obj->coord_z = dm_strlen($obj->coord_z) > 0 ? dm_trim($obj->coord_z, '0') : $obj->coord_z;
            }
        }
        else
        {
            $obj = new $currentClassName(0);
        }

        foreach($info_get_from_list as $champ_id => $info_liste)
        {
            if(isset($obj->$champ_id) && $obj->$champ_id > 0)
            {
                $key = $info_liste['key'];
                $obj->$key = isset($info_liste['liste'][$info_liste['key']]) ? $info_liste['liste'][$info_liste['key']]['nom'] : '';
            }
        }

        return $obj;
    }

    /**
     * @param $id
     */
    public static function delete($id)
    {
        global $db;

        /** @var self $currentClassName */
        $currentClassName = get_called_class();
        $table_name = $currentClassName::SQL_TABLE_NAME;

        $db->autocommit(false);
        $db->query("DELETE FROM `" . db::tableColonneValide($table_name) . "` WHERE id = ?", array($id));
        $db->commit();
    }

    /**
     *
     */
    public function getFiltreHTML()
    {
        /** @var self $currentClassName */
        $currentClassName = get_called_class();
        ?>
        <fieldset class="noMinHeight">
            <table class="formFieldsTable">

                <thead>
                <?php
                $currentClassName::getFormHTMLTitre();
                ?>
                </thead>

                <tbody data-cur_iteration="0">
                <?php
                $this->getFormHTMLLigne(0);
                ?>
                </tbody>

            </table>
        </fieldset>
        <?php
    }

    /**
     * @param $data
     * @return array
     */
    public static function dataToListe($data)
    {
        /** @var self $currentClassName */
        $currentClassName = get_called_class();

        $liste = array();
        $data_tmp = array();
        foreach($data as $champ => $valeurs)
        {
            if(is_array($valeurs))
            {
                foreach($valeurs as $iter => $valeur)
                {
                    $data_tmp[$iter][$champ] = $valeur;
                }
            }
        }

        foreach($data_tmp as $iter => $valeurs)
        {
            $liste[$iter] = new $currentClassName(0, $valeurs);
        }

        return $liste;
    }

    public static function addDataCoordinationRaccords($fiche_id, $liste)
    {
        global $db;
        /** @var self $currentClassName */
        $currentClassName = get_called_class();
        $table = $currentClassName::SQL_TABLE_NAME;
        $table_coord = $currentClassName::SQL_TABLE_NAME_COORD;
        $column_coord = $currentClassName::SQL_COLOMN_NAME_COORD;

        $sql = "select *, ebmc.nom as code_equipement, (coalesce(ebm.qte_requise, 0) + coalesce(ebm.qte_fantome, 0)) as qte_equipement_requis_now
                from `" . db::tableColonneValide($table_coord) . "` t
                join equipements_bio_medicaux ebm on ebm.id = t.equipement_bio_medicaux_id
                join equipements_bio_medicaux_types ebmc on ebmc.id = ebm.equipement_bio_medicaux_type_id
                where `" . db::tableColonneValide($column_coord) . "` in (select id from `" . db::tableColonneValide($table) . "` where fiche_id = ?)
                    and `equipement_bio_medicaux_id` in (select id from `equipements_bio_medicaux` where fiche_id = ?)";
        $coords = $db->getAll($sql, [$fiche_id, $fiche_id]);

        $nouvelleListe = [];
        foreach ($liste as $it => $ligne) {
            if (!isset($ligne->equipements_lies)) {
                $ligne->equipements_lies = [];
            }
            foreach ($coords as $coord) {
                if ($ligne->id == $coord[$column_coord]) {
                    $ligne->equipements_lies[] = $coord;
                }
            }
            $nouvelleListe[$it] = $ligne;
        }

        $qte_besoin_equipement_total_par_equipement = [];
        foreach ($nouvelleListe as $it => $ligne) {
            foreach ($ligne->equipements_lies as $iii => $equipement_lie) {
                if (!isset($qte_besoin_equipement_total_par_equipement[$equipement_lie['equipement_bio_medicaux_id']])) {
                    $qte_besoin_equipement_total_par_equipement[$equipement_lie['equipement_bio_medicaux_id']] = 0;
                }
                $qte_besoin_equipement_total_par_equipement[$equipement_lie['equipement_bio_medicaux_id']] += $equipement_lie['qte_besoin_equipement'];
            }
        }

        foreach ($nouvelleListe as $it => $ligne) {
            foreach ($ligne->equipements_lies as $iii => $equipement_lie) {
                $equipement_lie['is_qte_equipement_requis_ok'] = $qte_besoin_equipement_total_par_equipement[$equipement_lie['equipement_bio_medicaux_id']] == $equipement_lie['qte_equipement_requis_now'];
                $ligne->equipements_lies[$iii] = $equipement_lie;
            }
        }

        return $nouvelleListe;
    }

    public static function addDataCoordinationRaccordsAll($liste)
    {
        global $db;
        /** @var self $currentClassName */
        $currentClassName = get_called_class();
        $table = $currentClassName::SQL_TABLE_NAME;
        $table_coord = $currentClassName::SQL_TABLE_NAME_COORD;
        $column_coord = $currentClassName::SQL_COLOMN_NAME_COORD;

        $sql = "select *, ebmc.nom as code_equipement, (coalesce(ebm.qte_requise, 0) + coalesce(ebm.qte_fantome, 0)) as qte_equipement_requis_now
                from `" . db::tableColonneValide($table_coord) . "` t
                join equipements_bio_medicaux ebm on ebm.id = t.equipement_bio_medicaux_id
                join equipements_bio_medicaux_types ebmc on ebmc.id = ebm.equipement_bio_medicaux_type_id
                where ebm.projet_id = ?";
        $coords_tmp = $db->getAll($sql, [getProjetId()]);

        $all_coords = [];
        foreach ($coords_tmp as $coord) {
            $all_coords[$coord[$column_coord]][] = $coord;
        }

        $nouvelleListe = [];
        foreach ($liste as $it => $ligne) {
            $ligne->equipements_lies = $all_coords[$ligne->id] ?? [];
            $nouvelleListe[$it] = $ligne;
        }

        $qte_besoin_equipement_total_par_equipement = [];
        foreach ($nouvelleListe as $ligne) {
            foreach ($ligne->equipements_lies as $equipement_lie) {
                if (!isset($qte_besoin_equipement_total_par_equipement[$equipement_lie['equipement_bio_medicaux_id']])) {
                    $qte_besoin_equipement_total_par_equipement[$equipement_lie['equipement_bio_medicaux_id']] = 0;
                }
                $qte_besoin_equipement_total_par_equipement[$equipement_lie['equipement_bio_medicaux_id']] += $equipement_lie['qte_besoin_equipement'];
            }
        }

        foreach ($nouvelleListe as $ligne) {
            foreach ($ligne->equipements_lies as $iii => $equipement_lie) {
                $equipement_lie['is_qte_equipement_requis_ok'] = $qte_besoin_equipement_total_par_equipement[$equipement_lie['equipement_bio_medicaux_id']] == $equipement_lie['qte_equipement_requis_now'];
                $ligne->equipements_lies[$iii] = $equipement_lie;
            }
        }

        return $nouvelleListe;
    }

    /**
     * @param $fiche_id
     * @param null $liste_data
     */
    public static function getFormHTML($fiche_id, $liste_data = null, $forced_liste = null)
    {
        global $db_name;
        /** @var self $currentClassName */
        $currentClassName = get_called_class();

        /** @var self[] $liste */
        if (isset($forced_liste)) {
            $liste = $forced_liste;
        } else if (isset($liste_data)) {
            $liste = $currentClassName::dataToListe($liste_data);
        } else {
            $liste = $currentClassName::getListe(array('fiche_id' => $fiche_id)) ?? [];
        }
        $canAdd = $currentClassName::canAddDeleteMove();
        ?>
        <fieldset class="noMinHeight">
            <table class="formFieldsTable">

                <thead>
                <?php
                $currentClassName::getFormHTMLTitre();
                ?>
                </thead>

                <tbody data-cur_iteration="<?= (count($liste) == 0 ? 1 : count($liste)) ?>">
                <?php
                // première ligne invisible
                $ligne = new $currentClassName(0);
                $ligne->id = -1;
                if (isset($_GET['equipement_bio_medicaux_coordination'])) {
                    $ligne->equipements_lies = [];
                }
                $ligne->getFormHTMLLigne(0, null, true);

                $fiche = new Fiches($fiche_id);
                $gabarit = $fiche->getGabaritByFormulaire(Formulaires::getIdFromNom($currentClassName));
                $is_ligne_sans_gabarit = false;
                $motif_obligatoire = false;
                $iteration = 1;
                if (count($liste) > 0) {
                    foreach ($liste as $i => $ligne) {
                        $ligne->getFormHTMLLigne($iteration++, $gabarit);
                        if ($ligne->statut == 1) {
                            $is_ligne_sans_gabarit = true;
                        }
                    }
                    $motif_obligatoire = true;
                }

                if (!$is_ligne_sans_gabarit) {
                    $ligne = new $currentClassName(0);
                    $ligne->getFormHTMLLigne($iteration);
                }
                ?>
                </tbody>

                <tfoot>
                <tr><td>
                        <?php
                        if ($canAdd) {
                            ?>
                            <a href="javascript: void(0);" class="add_line" style="">
                                <img src="css/images/icons/dark/plus.png"/></a>
                            <?php
                        }
                        ?>
                    </td>
                    <td colspan="10">
                        &nbsp;
                    </td></tr>
                </tfoot>

            </table>
            <?php
            $fiche_formulaire_commentaire = self::getCommentaireFormulaire($fiche_id);
            $fiche_formulaire_commentaire = $fiche_formulaire_commentaire ?? '';
            ?>
            <section>
                <label><?=txt('Commentaires')?></label>
                <div>
                    <textarea name="fiche_formulaire_commentaire" style="min-height: 75px; width: 600px;"><?=($fiche_formulaire_commentaire)?></textarea>
                </div>
            </section>
            <?php
            if($motif_obligatoire)
            {
                ?>
                <section>
                    <label style="font-weight: bold;">*<?=txt('Motif du changement')?></label>
                    <div>
                        <select id="motif_changement" data-name="raison">
                            <option value=""> </option>
                            <?=MotifsChangements::getOptions(array(), 1)?>
                        </select>
                        <div style="color: blue; margin-top: 10px;"><?=txt('SVP, écrire un motif particulier ou choisir parmi la sélection proposée.')?></div>
                    </div>
                </section>
                <?php
            }
            else
            {
                ?>
                <input type="hidden" name="raison" value="Création du formulaire" />
                <?php
            }
            ?>
        </fieldset>
        <?php
    }

    /**
     * @param $fiche_id
     * @param null $token_archive
     * @return null
     */
    public static function getCommentaireFormulaire($fiche_id, $token_archive = null)
    {
        global $db, $db_name, $db_name_archive;

        $currentClassName = get_called_class();
        $currentClassName = $currentClassName == 'EquipementsBioMedicauxNoCoord' ? 'EquipementsBioMedicaux' : $currentClassName;

        if(!isset($token_archive))
        {
            $sql = "select commentaire
                    from fiches_formulaires
                    where fiche_id = ? and formulaire_id = (select id from formulaires where nom = ?)";
            $row = $db->getRow($sql, array($fiche_id, $currentClassName));
        }
        else
        {
            $sql = "select commentaire
                    from ".$db_name_archive.".fiches_formulaires
                    where fiche_id = ? and formulaire_id = (select id from ".$db_name.".formulaires where nom = ?)".(isset($token_archive) && dm_strlen($token_archive) > 0 ? " and token_archive = '".$token_archive."'" : " and token_archive is null");
            $row = $db->getRow($sql, array($fiche_id, $currentClassName));
        }

        return isset($row) && dm_strlen($row['commentaire']) > 0 ? $row['commentaire'] : null;
    }

    /**
     * @param $fiche_id
     * @param null $liste_data
     */
    public static function getUniqueFormMobileHTML($fiche_id, $liste_data = null)
    {
        /** @var self $currentClassName */
        $currentClassName = get_called_class();

        if(isset($liste_data))
        {
            $objs = $currentClassName::dataToListe($liste_data);
            if(isset($objs[0]))
            {
                $obj = $objs[0];
            }
            else
            {
                $obj = new $currentClassName(0);
            }
        }
        else
        {
            $obj = $currentClassName::getOne($fiche_id);
        }
        $obj->fiche_id = $fiche_id;
        ?>
        <fieldset class="noMinHeight">
            <input type="hidden" name="id[]" value="<?=$obj->id?>" />
            <?php
            $obj->getEditorMobileHTML();
            ?>
            <input type="hidden" name="raison" value="Informations provenant du formulaire de relevé mobile" />
        </fieldset>
        <?php
    }

    /**
     * @param $fiche_id
     * @param null $liste_data
     */
    public static function getUniqueFormHTML($fiche_id, $liste_data = null)
    {
        /** @var self $currentClassName */
        $currentClassName = get_called_class();

        if(isset($liste_data))
        {
            $objs = $currentClassName::dataToListe($liste_data);
            if(isset($objs[0]))
            {
                $obj = $objs[0];
            }
            else
            {
                $obj = new $currentClassName(0);
            }
        }
        else
        {
            $obj = $currentClassName::getOne($fiche_id);
        }
        $obj->fiche_id = $fiche_id;

        $metas_tmp = $currentClassName::getChampsSaisieMetaData();

        //if(!isset($metas_tmp['regroupements']))
        {
            ?>
            <fieldset class="noMinHeight">
            <?php
        }
        ?>
        <input type="hidden" name="id[]" value="<?=$obj->id?>" />
        <?php
        $obj->getEditorHTML();
        if($obj->id > 0)
        {
            ?>
            <section>
                <label style="font-weight: bold;">*<?=txt('Motif du changement')?></label>
                <div>
                    <select id="motif_changement" data-name="raison">
                        <option value=""> </option>
                        <?=MotifsChangements::getOptions(array(), 1)?>
                    </select>
                    <div style="color: blue; margin-top: 10px;"><?=txt('SVP, écrire un motif particulier ou choisir parmi la sélection proposée.')?></div>
                </div>
            </section>
            <?php
        }
        else
        {
            ?>
            <input type="hidden" name="raison" value="Création du formulaire" />
            <?php
        }

        //if(!isset($metas_tmp['regroupements']))
        {
            ?>
            </fieldset>
            <?php
        }
    }

    /**
     * @param $fiche_id
     * @param $liste
     * @param $last_liste
     * @param $token_archive
     * @param $last_token_archive
     */
    public static function getDiffArchiveHTML($fiche_id, $liste, $last_liste, $token_archive, $last_token_archive)
    {
        /** @var self $currentClassName */
        $currentClassName = get_called_class();
        ?>
        <h2 class="blocsFicheTitre" style="height: 30px;">
            <?=txt($currentClassName::TITRE)?></h2>

        <fieldset class="noMinHeight">
            <table class="formFieldsTable formFieldsTable_readonly">

                <thead>
                <?php
                $currentClassName::getFicheHTMLTitre(false);
                ?>
                </thead>

                <tbody>
                <?php
                if(count($liste) > 0) {
                    foreach ($liste as $id => $ligne) {
                        if ($ligne->ordre == 999999) {
                            continue;
                        }
                        if (isset($last_liste[$id])) {
                            $ligne_diff = $last_liste[$id];
                            unset($last_liste[$id]);
                        } else {
                            $ligne_diff = new $currentClassName(0);
                        }

                        $ligne->getDiffArchiveHTMLLigne($ligne_diff);
                    }

                    foreach ($last_liste as $id => $ligne) {
                        /*if (isset($ligne->quantite) && $ligne->quantite == 0) {
                            continue;
                        }*/
                        $ligne_diff = new $currentClassName(0);
                        $ligne_diff->getDiffArchiveHTMLLigne($ligne);
                    }
                }
                ?>
                </tbody>
                <?php
                $fiche_formulaire_commentaire = self::getCommentaireFormulaire($fiche_id, $token_archive);
                $last_fiche_formulaire_commentaire = self::getCommentaireFormulaire($fiche_id, $last_token_archive);
                $last_fiche_formulaire_commentaire = isset($last_fiche_formulaire_commentaire) ? $last_fiche_formulaire_commentaire : '';
                if(isset($fiche_formulaire_commentaire) || isset($last_fiche_formulaire_commentaire))
                {
                    ?>
                    <tfoot style="maregin-top: 15px;">
                    <tr><td class="title">
                            <?=txt('Commentaires')?>
                        </td>
                        <td colspan="10">
                            <div style="max-width: 800px;"><?=dm_nl2br(diff_by_word($last_fiche_formulaire_commentaire, $fiche_formulaire_commentaire))?></div>
                        </td></tr>
                    </tfoot>
                    <?php
                }
                ?>
            </table>
        </fieldset>
        <?php
    }

    /**
     * @param $ligne_diff
     */
    public function getDiffArchiveHTMLLigne($ligne_diff)
    {
        /** @var self $currentClassName */
        $currentClassName = get_called_class();
        $metas = $currentClassName::getChampsSaisieMetaData();
        ?>
        <tr>
            <?php
            foreach ($metas as $key => $meta)
            {
                if(!isset($meta['hidden']) && !in_array($key, array('conformite_du_besoin_electricite', 'conformite_du_besoin_plomberie', 'conformite_du_besoin_mecanique', 'conformite_du_besoin_telecom', 'conformite_du_besoin_architecture', 'conformite_du_besoin_structure', 'conformite_du_besoin')))
                {
                    $old_value = isset($ligne_diff->$key) ? $ligne_diff->$key : '';
                    $cur_value = isset($this->$key) ? $this->$key : '';
                    /*if(isset($this->quantite) && $this->quantite == '0' && isset($ligne_diff->quantite) && $ligne_diff->quantite <> '0')
                    {
                        $cur_value = '';
                    }*/

                    /*$extra_style = '';
                    if($key == 'commentaires_plomberie')
                    {
                        $extra_style = 'border-right: solid 1px #AAA!important;';
                    }
                    else if(in_array($key, array('equipement_electricite', 'equipement_plomberie')))
                    {
                        $extra_style = 'border-left: solid 1px #AAA!important;';
                    }*/
                    ?>
                    <td style="">
                        <?=diff($old_value, $cur_value)?>
                    </td>
                    <?php
                    if($key == 'commentaires_electricite')
                    {
                        ?>
                        <td>
                            &nbsp;
                        </td>
                        <?php
                    }
                }
            }
            ?>
        </tr>
        <?php
    }

    /**
     * @param $fiche
     * @param null $liste
     * @param int $nb_archives
     * @param bool $can_edit
     * @param null $projet_courant_id
     * @param null $gabarit
     */
    public static function getFicheHTML($fiche, $liste = null, $nb_archives = 0, $can_edit = false, $projet_courant_id = null, $gabarit = null)
    {
        global $isPDF, $db, $CodeClient, $db_name;

        $isPDF = $isPDF ?? false;

        /** @var self $currentClassName */
        $currentClassName = get_called_class();

        $usager = getUsager();
        $disciplines_auth = $usager->getDisciplinesAutorisees();

        if(!isset($liste))
        {
            $liste = $currentClassName::getListe(array('fiche_id' => $fiche->id));
        }
        if (in_array($currentClassName, ['PrisesElectriquesCoordinations', 'PrisesCommunicationsCoordinations'])) {
            $liste = $currentClassName::addDataCoordinationRaccords($fiche->id, $liste);
        }
        ?>
        <h2 class="blocsFicheTitre" style="height: 30px;">
            <?=txt($currentClassName::TITRE)?>
            <?php
            if(!$isPDF)
            {
                $is_projet_courant_ou_ferme = true;
                $projet_courant = null;
                if(isset($projet_courant_id) && $projet_courant_id > 0 && $projet_courant_id <> getProjetId())
                {
                    $projet_courant = new Projets($projet_courant_id);
                    if(isset($projet_courant) && !isset($projet_courant->ts_fermeture))
                    {
                        $is_projet_courant_ou_ferme = false;
                    }
                }

                $override_condition = in_array($currentClassName, array('ProprieteDesRaccordsDePlomberieSansCoord', 'ProprieteDeLinstrumentationDeMesureDetectionEtAnalyse', 'ProprieteDesRaccordsDePlomberie', 'ProprieteDesRaccordsDeControle', 'ProprietesControles', 'ProprietesControlesAuxiliaires', 'ProprietesElectriques', 'ProprietesElectriquesAuxiliaires'));
                if(!$override_condition)
                {
                    $override_condition = in_array($currentClassName, array('ProprietesRaccordementsControles', 'ProprietesRaccordementsControlesAuxiliaires')) && (isset($disciplines_auth[8]) || isset($disciplines_auth[11]));
                }

                $can_edit_form = !isFicheReadonly() && ($can_edit || $override_condition) && $is_projet_courant_ou_ferme && ($fiche->is_gabarit == 1 || ($fiche->is_gabarit == 0 && isMicroProjet())) ? 1 : 0;
                if(isAutorizedFicheForStatut($fiche->id) && $can_edit_form)
                {
                    $formulaire_id = Formulaires::getIdFromNom($currentClassName);
                    $table_name = $currentClassName::SQL_TABLE_NAME == 'equipements_bio_medicaux' ? 'equipements_bio_medicaux_no_coord' : $currentClassName::SQL_TABLE_NAME;
                    $currentClassNameTmp = $currentClassName == 'EquipementsBioMedicaux' ? 'EquipementsBioMedicauxNoCoord' : $currentClassName;
                    $classFancy = $currentClassName == 'EquipementsBioMedicaux' ? 'fancy_avg' : 'edit';
                    $classFancy = in_array($currentClassName, ['PrisesElectriquesCoordinations', 'PrisesCommunicationsCoordinations']) ? 'fancy_big' : $classFancy;
                    if (in_array($CodeClient, ['sqi', 'phvs']) && $currentClassName == 'EquipementsBioMedicaux') {
                        $classFancy = 'fancy_big';
                    }
                    ?>
                    <a href="index.php?section=admin&module=edit&table=<?=$table_name?>&id=0&from_fiche=<?=$fiche->id?>&type=client&formulaire=<?=$currentClassNameTmp?>" class="<?=$classFancy?> editor_btn" id="editor_btn_<?=getOuvrageId()?>_<?=$formulaire_id?>_<?=$fiche->id?>" style="position: relative; top: 7px;" title="<?=txt("Éditer le formulaire")?>">
                        <img src="css/images/icons/dark/create_write.png" /></a>
                    <?php
                }

                if($nb_archives > 0)
                {
                    ?>
                    <a href="index.php?section=fiche&module=form_logs&fiche_id=<?=$fiche->id?>&formulaire=<?=$currentClassName?>&can_edit_form=<?=$can_edit_form?>" class="edit" style="position: relative; top: 7px;" title="<?=txt("Consulter l'historique des modifications")?>">
                        <img src="css/images/icons/dark/clock.png" /></a>
                    <?php
                }

                if(GestionnaireRoute::instance()->haveAccesRoute('fiche-clone_formulaire') && $can_edit && ($fiche->is_gabarit == 1 || ($fiche->is_gabarit == 0 && isMicroProjet())))
                {
                    ?>
                    <a href="index.php?section=fiche&module=clone_formulaire&fiche_id=<?=$fiche->id?>&formulaire=<?=$currentClassName?>&projet_id=<?=getProjetId()?>" class="edit" style="position: relative; top: 7px;" title="<?=txt("Cloner le formulaire")?>">
                        <img src="css/images/icons/dark/duplicate.png" /></a>
                    <?php
                }

                $sql = "select count(*) from fiches_gabarits_formulaires where fiche_id = ? and formulaire_id = ?";
                $nbFormulairesGabarits = db::instance()->getOne($sql, array($fiche->id, Formulaires::getIdFromNom($currentClassName)));

                if(GestionnaireRoute::instance()->haveAccesRoute('delete_formulaire') && isAutorizedFicheForStatut($fiche->id) && $currentClassName::CAN_DELETE && $can_edit && $is_projet_courant_ou_ferme && ($fiche->is_gabarit == 1 || ($fiche->is_gabarit == 0 && (isMicroProjet() || $nbFormulairesGabarits == 0))))
                {
                    $canDelete = true;
                    if (in_array($currentClassName, ['EquipementsBioMedicaux', 'EquipementsBioMedicauxNoCoord'])) {
                        $fiches_verrouillees_ou_j0 = Fiches::getFicheIdsDepotsVerouilles();
                        if (in_array($fiche->id, $fiches_verrouillees_ou_j0)) {
                            $canDelete = false;
                        }
                    }
                    if ($canDelete) {
                        ?>
                        <a href="javascript: void(0);" class="delete_formulaire" data-formulaire="<?=$currentClassName?>" data-fiche_id="<?=$fiche->id?>" style="position: relative; top: 7px;" title="<?=txt("Supprimer le formulaire")?>">
                            <img src="css/images/icons/dark/trashcan.png" /></a>
                        <?php
                    } else {
                        ?>
                        <a href="javascript: void(0);" onclick="alert('<?= txt('Attention! Ce formulaire ne peut être supprimé parce qu’il comporte des équipements qui ont été associés à une/des demandes de financement') ?>')" style="opacity: 0.5;position: relative; top: 7px;" title="<?=txt("Supprimer le formulaire")?>">
                            <img src="css/images/icons/dark/trashcan.png" /></a>
                        <?php
                    }
                }
            }

            if (!$isPDF && in_array($currentClassName, ['EquipementsBioMedicaux', 'EquipementsBioMedicauxNoCoord']) && havePermission('admin_budgets')) {
                $total = 0;
                $total_dev = 0;
                $total_rempl = 0;
                if(count($liste) > 0) {
                    foreach ($liste as $ligne) {
                        $qte_dev = ($ligne->qte_requise - $ligne->qte_conservee);
                        $total_dev += ($qte_dev * $ligne->prix_unitaire_nouveau);

                        $qte_rempl = ($ligne->qte_existante - $ligne->qte_conservee);
                        $total_rempl += ($qte_rempl * $ligne->prix_unitaire_nouveau);

                        $total += (($qte_dev + $qte_rempl) * $ligne->prix_unitaire_nouveau);
                    }
                }
                $total = dm_number_format(max($total, 0), 2, ',', ' ') . '$';
                $total_dev = dm_number_format(max($total_dev, 0), 2, ',', ' ') . '$';
                $total_rempl = dm_number_format(max($total_rempl, 0), 2, ',', ' ') . '$';
                ?>
                <span style="position: absolute; right: 20px; top: 14px; font-weight: bold"><?= "Total : <b style=\"color: blue\">$total</b> | " . txt('Développement') . ": <b style=\"color: blue\">$total_dev</b> | " . txt('Remplacement') . " : <b style=\"color: blue\">$total_rempl</b>" ?></span>
                <?php
            }
            ?>
        </h2>
        <?php
        if(isset($projet_courant))
        {
            $message = '';
            $img = '';
            $couleur = isset($projet_courant->ts_fermeture) ? 'orange' : 'red';
            if(getProjetId() == 0)
            {
                $couleur = 'black';
                $img = '<img src="css/spacer.gif" style="position: relative; top: 7px; width: 24px; height: 23px;" />';
            }
            else if(!isset($projet_courant->ts_fermeture))
            {
                $directeur = new Usager($projet_courant->usager_id_directeur);
                $message = txt('Attention! Cette fiche n\'est pas modifiable parce qu\'elle est présentement en cours'."\n".' de révision dans le cadre du projet '.$projet_courant->code.' - '.$projet_courant->nom.'.'."\n\n".'Astuce! S\'il est absolument nécessaire de modifier cette fiche, veuillez communiquer avec '."\n".'le chargé de ce projet '.$directeur->email.' afin de coordonner et transmettre vos besoins.', false);
                $img = '<img src="css/images/icons/dark/alert_red.png" style="position: relative; top: 7px;" />';
            }
            ?>
            <span style="color: <?=$couleur?>; display: block; font-weight: bold; position: absolute; right: 20px; top: 0px; height: 16px; font-style: italic;" title="<?=$message?>">
                <?=$img.txt('Associé au projet ').$projet_courant->code.' - '.$projet_courant->nom?> (<?=$projet_courant->getStatut(true, false)?>)</span>
            <?php
        }
        ?>
        <fieldset class="noMinHeight">
            <table class="formFieldsTable formFieldsTable_readonly">
                <thead>
                <?php
                if ($isPDF && count($liste) == 0) {
                    print '<tr><th colspan="20">' . txt('Non applicable') . '</th></tr>';
                } else {
                    $currentClassName::getFicheHTMLTitre(isset($gabarit));
                }
                ?>
                </thead>

                <tbody>
                <?php
                if(count($liste) > 0) {
                    foreach ($liste as $iteration => $ligne) {
                        $ligne->getFicheHTMLLigne($iteration, $gabarit);
                    }
                }
                ?>
                </tbody>
                <?php
                $fiche_formulaire_commentaire = self::getCommentaireFormulaire($fiche->id);
                if (isset($fiche_formulaire_commentaire)) {
                    ?>
                    <tfoot style="maregin-top: 15px;">
                    <tr><td class="title">
                            <?=txt('Commentaires')?>
                        </td>
                        <td colspan="10">
                            <div style="max-width: 800px;"><?=dm_nl2br($fiche_formulaire_commentaire)?></div>
                        </td></tr>
                    </tfoot>
                    <?php
                }
                ?>
            </table>
        </fieldset>
        <?php
    }

    /**
     * @param $fiche
     * @param $obj
     * @param int $nb_archives
     * @param bool $can_edit
     * @param null $projet_courant_id
     */
    public static function getUniqueFicheHTML($fiche, $obj, $nb_archives = 0, $can_edit = false, $projet_courant_id = null)
    {
        global $isPDF;

        /** @var self $currentClassName */
        $currentClassName = get_called_class();
        ?>
        <h2 class="blocsFicheTitre" style="height: 30px;">
            <?=txt($currentClassName::TITRE)?>
            <?php
            if(!$isPDF)
            {
                $is_projet_courant_ou_ferme = true;
                $projet_courant = null;
                if(isset($projet_courant_id) && $projet_courant_id > 0 && $projet_courant_id <> getProjetId())
                {
                    $projet_courant = new Projets($projet_courant_id);
                    if(isset($projet_courant) && !isset($projet_courant->ts_fermeture))
                    {
                        $is_projet_courant_ou_ferme = false;
                    }
                }

                $usager = getUsager();
                $disciplines_auth = $usager->getDisciplinesAutorisees();
                $override_condition = in_array($currentClassName, array('ProprieteDesRaccordsDePlomberieSansCoord', 'ProprieteDeLinstrumentationDeMesureDetectionEtAnalyse', 'ProprieteDesRaccordsDePlomberie', 'ProprieteDesRaccordsDeControle', 'ProprietesControles', 'ProprietesControlesAuxiliaires', 'ProprietesElectriques', 'ProprietesElectriquesAuxiliaires'));
                if(!$override_condition)
                {
                    $override_condition = in_array($currentClassName, array('ProprietesRaccordementsControles', 'ProprietesRaccordementsControlesAuxiliaires')) && (isset($disciplines_auth[8]) || isset($disciplines_auth[11]));
                }

                if (str_starts_with((string)$currentClassName, 'Generaux')) {
                    if (!$override_condition) {
                        $override_condition = Discipline::isDisciplineEditableForCode('GEN');
                    }
                }

                $can_edit_form = GestionnaireRoute::instance()->haveAccesRoute("admin-edit-" . $currentClassName::SQL_TABLE_NAME) && !isFicheReadonly() && ($can_edit || $override_condition) && $is_projet_courant_ou_ferme && ($fiche->is_gabarit == 1 || ($fiche->is_gabarit == 0 && isMicroProjet())) ? 1 : 0;
                if(isAutorizedFicheForStatut($fiche->id) && $can_edit_form)
                {
                    $formulaire_id = Formulaires::getIdFromNom($currentClassName);
                    ?>
                    <a href="index.php?section=admin&module=edit&table=<?=$currentClassName::SQL_TABLE_NAME?>&id=0&from_fiche=<?=$fiche->id?>&type=client&formulaire=<?=$currentClassName?><?=(isset($_GET['from_coordination']) ? '&from_coordination='.$_GET['from_coordination'] : '')?>" class="edit editor_btn" id="editor_btn_<?=getOuvrageId()?>_<?=$formulaire_id?>_<?=$fiche->id?>" style="position: relative; top: 7px;" title="<?=txt("Éditer le formulaire")?>">
                        <img src="css/images/icons/dark/create_write.png" /></a>
                    <?php
                }

                if($nb_archives > 0)
                {
                    ?>
                    <a href="index.php?section=fiche&module=form_logs&fiche_id=<?=$fiche->id?>&formulaire=<?=$currentClassName?>&can_edit_form=<?=$can_edit_form?>" class="edit" style="position: relative; top: 7px;" title="<?=txt("Consulter l'historique des modifications")?>">
                        <img src="css/images/icons/dark/clock.png" /></a>
                    <?php
                }

                if(GestionnaireRoute::instance()->haveAccesRoute("fiche-clone_formulaire") && $can_edit_form && ($fiche->is_gabarit == 1 || ($fiche->is_gabarit == 0 && isMicroProjet())))
                {
                    ?>
                    <a href="index.php?section=fiche&module=clone_formulaire&fiche_id=<?=$fiche->id?>&formulaire=<?=$currentClassName?>&projet_id=<?=getProjetId()?>" class="fancy_big" style="position: relative; top: 7px;" title="<?=txt("Cloner le formulaire")?>">
                        <img src="css/images/icons/dark/duplicate.png" /></a>
                    <?php
                }

                if(in_array($currentClassName, array('ProprietesElectriques', 'ProprietesElectriquesAuxiliaires')))
                {
                    if($currentClassName == 'ProprietesElectriques')
                    {
                        $form_tmp = new Formulaires(Formulaires::getIdFromNom('RaccordsElectriquesCoordinations'));
                        if($form_tmp->isRempliForFiche($fiche->id))
                        {
                            $can_edit = false;
                        }

                        $form_tmp = new Formulaires(Formulaires::getIdFromNom('ProprietesElectriquesAuxiliaires'));
                        if($form_tmp->isRempliForFiche($fiche->id))
                        {
                            $can_edit = false;
                        }
                    }
                    else if($currentClassName == 'ProprietesElectriquesAuxiliaires')
                    {
                        /*$form_tmp = new Formulaires(Formulaires::getIdFromNom('RaccordsElectriquesCoordinationsAuxiliaires'));
                        if($form_tmp->isRempliForFiche($fiche->id))
                        {
                            $can_edit = false;
                        }*/
                    }
                }

                $sql = "select count(*) from fiches_gabarits_formulaires where fiche_id = ? and formulaire_id = ?";
                $nbFormulairesGabarits = db::instance()->getOne($sql, array($fiche->id, Formulaires::getIdFromNom($currentClassName)));

                if(GestionnaireRoute::instance()->haveAccesRoute("delete_formulaire") && isAutorizedFicheForStatut($fiche->id) && $currentClassName::CAN_DELETE && $can_edit && $is_projet_courant_ou_ferme && ($fiche->is_gabarit == 1 || ($fiche->is_gabarit == 0 && (isMicroProjet() || $nbFormulairesGabarits == 0))))
                {
                    ?>
                    <a href="javascript: void(0);" class="delete_formulaire" data-formulaire="<?=$currentClassName?>" data-fiche_id="<?=$fiche->id?>" style="position: relative; top: 7px;" title="<?=txt("Supprimer le formulaire")?>">
                        <img src="css/images/icons/dark/trashcan.png" /></a>
                    <?php
                }
            }

            if(isset($projet_courant))
            {
                $message = '';
                $img = '';
                $couleur = isset($projet_courant->ts_fermeture) ? 'orange' : 'red';
                if(getProjetId() == 0)
                {
                    $couleur = 'black';
                    $img = '<img src="css/spacer.gif" style="position: relative; top: 7px; width: 24px; height: 23px;" />';
                }
                else if(!isset($projet_courant->ts_fermeture))
                {
                    $directeur = new Usager($projet_courant->usager_id_directeur);
                    $message = txt('Attention! Cette fiche n\'est pas modifiable parce qu\'elle est présentement en cours de révision dans le cadre du projet '.$projet_courant->code.' - '.$projet_courant->nom.'.'."\n\n".'Astuce! S\'il est absolument nécessaire de modifier cette fiche, veuillez communiquer avec le chargé de ce projet '.$directeur->email.' afin de coordonner et transmettre vos besoins.', false);
                    $img = '<img src="css/images/icons/dark/alert_red.png" style="position: relative; top: 7px;" />';
                }
                ?>
                <span style="color: <?=$couleur?>; font-weight: bold; position: absolute; right: 20px; top: 0px; display: block; height: 16px; font-style: italic;" title="<?=$message?>">
                    <?=$img.txt('Associé au projet ').$projet_courant->code.' - '.$projet_courant->nom?> (<?=$projet_courant->getStatut(true, false)?>)</span>
                <?php
            }
            ?>
        </h2>
        <?php
    }

    /**
     * @param $data
     * @param bool $already_in_transac
     * @return bool
     */
    public static function save($data, $already_in_transac = false)
    {
        global $db_name, $db, $db_name_archive, $CodeClient, $sql_logs;

        $formulaire = $data['formulaire'];
        $db_n = $db_name;
        $db_a = $db_name_archive;

        if(!$already_in_transac)
        {
            $db->autocommit(false);
        }
        $fiche_id = isset($data['fiche_id']) ? (int)($data['fiche_id']) : 0;
        $fiche = new Fiches($fiche_id);
        $table = $formulaire::SQL_TABLE_NAME;

        $projet_id = getProjetId();
        if(isset($data['projet_id']))
        {
            $projet_id = is_array($data['projet_id']) ? $data['projet_id'][0] : $data['projet_id'];
        }

        $formulaire_id = $formulaire::getFormulaireId();

        $form = new Formulaires($formulaire_id);
        $first_champ = $form->getFirstChamp();
        $forms_with_qte = Formulaires::getFormulairesIdWithQuantite();
        $is_qte_form = in_array($formulaire_id, $forms_with_qte);

        if($fiche_id > 0)
        {
            $isValide = $formulaire::validate($data, $fiche_id);
            if(!$isValide)
            {
                return false;
            }
            else
            {
                $sql = "SELECT *
                        FROM `INFORMATION_SCHEMA`.`COLUMNS` 
                        WHERE `TABLE_SCHEMA`='$db_n' 
                            AND `TABLE_NAME`='$table'
                            AND `COLUMN_NAME` not in ('id', 'fiche_id', 'no', 'projet_id', 'statut', 'token_archive', 'ts_ajout', 'usager_ajout_id')
                        order by DATA_TYPE;";
                $cols = $db->getAll($sql, array());

                $sql = "SELECT `COLUMN_NAME`
                        FROM `INFORMATION_SCHEMA`.`COLUMNS` 
                        WHERE `TABLE_SCHEMA`='$db_a' 
                            AND `TABLE_NAME`='$table'
                        order by DATA_TYPE;";
                $colsArchives = $db->getCol($sql, array());

                $colTemoin = $first_champ['champ_id'];

                $isColOrdre = false;
                foreach($cols as $iterCol => $col) {
                    if ($col['COLUMN_NAME'] == 'ordre') {
                        $isColOrdre = true;
                    }
                }

                $sql = "select * from `" . db::tableColonneValide($db_n) . "`.`" . db::tableColonneValide($table) . "` where `fiche_id` = ?";
                $lignes_existantes_tmp = $db->getAll($sql, array($fiche_id));

                $lignes_existantes = array();
                $lignes_types_existantes = array();
                $ligne_existante = null;
                if(in_array($formulaire::$TYPE_FORM, array('LISTE', 'LISTE_COORDINATION')))
                {
                    foreach($lignes_existantes_tmp as $iter => $ligne)
                    {
                        $lignes_existantes[$ligne['id']] = $ligne;

                        if($is_qte_form && isset($first_champ))
                        {
                            $lignes_types_existantes[$ligne[$first_champ['champ_id']]] = $ligne['id'];
                        }
                    }
                }
                else if(in_array($formulaire::$TYPE_FORM, array('UNIQUE')))
                {
                    foreach($lignes_existantes_tmp as $iter => $ligne)
                    {
                        $ligne_existante = $ligne;
                    }
                }

                $sql = "DELETE FROM `" . db::tableColonneValide($db_n) . "`.fiches_formulaires WHERE fiche_id = ? and formulaire_id = ?";
                $db->query($sql, array($fiche_id, $formulaire_id));

                $token_archive = uniqid();
                $sql = "update `" . db::tableColonneValide($db_n) . "`.`fiches_logs` set token_archive = ? where `fiche_id` = ? and formulaire_id = ? and token_archive is null";
                $db->query($sql, array($token_archive, $fiche_id, $formulaire_id));

                $sql = "update `" . db::tableColonneValide($db_a) . "`.`" . db::tableColonneValide($table) . "` set token_archive = ? where `fiche_id` = ? and token_archive is null";
                $db->query($sql, array($token_archive, $fiche_id));

                $sql = "update `" . db::tableColonneValide($db_a) . "`.`fiches_formulaires` set token_archive = ? where `fiche_id` = ? and formulaire_id = ? and token_archive is null";
                $db->query($sql, array($token_archive, $fiche_id, $formulaire_id));

                $nb_lignes = 0;
                $champs_a_notifier = array();
                if(!isset($data[$colTemoin]) || !is_array($data[$colTemoin])) {
                    $data_keys = array_values(array_keys($data));
                    foreach ($data_keys as $data_key) {
                        if (is_array($data[$data_key])) {
                            $colTemoin = $data_key;
                        }
                    }
                }

                if(isset($data[$colTemoin]) && is_array($data[$colTemoin]))
                {
                    $ordre = 1;
                    foreach($data[$colTemoin] as $iterLine => $tmp)
                    {
                        // C'est la ligne fantôme qui sert de gabarit lorsqu'on ajoute une ligne.
                        if (isset($data['id_ligne'][$iterLine]) && $data['id_ligne'][$iterLine] == -1) {
                            continue;
                        }
                        $updateStatement = array();
                        $insertStatementCol = array();
                        $insertStatementVal = array();
                        $dataTable = array();

                        $insertStatementCol_archive = array();
                        $insertStatementVal_archive = array();
                        $dataTable_archive = array();

                        $updateStatement[] = '`fiche_id` = ?';
                        $insertStatementCol[] = '`fiche_id`';
                        $insertStatementVal[] = '?';
                        $dataTable[] = $fiche_id;

                        $insertStatementCol_archive[] = '`fiche_id`';
                        $insertStatementVal_archive[] = '?';
                        $dataTable_archive[] = $fiche_id;

                        $updateStatement[] = '`projet_id` = ?';
                        $insertStatementCol[] = '`projet_id`';
                        $insertStatementVal[] = '?';
                        $dataTable[] = $projet_id;

                        $insertStatementCol_archive[] = '`projet_id`';
                        $insertStatementVal_archive[] = '?';
                        $dataTable_archive[] = $projet_id;

                        if($isColOrdre)
                        {
                            $updateStatement[] = '`ordre` = ?';
                            $insertStatementCol[] = '`ordre`';
                            $insertStatementVal[] = '?';
                            $dataTable[] = $ordre;

                            $insertStatementCol_archive[] = '`ordre`';
                            $insertStatementVal_archive[] = '?';
                            $dataTable_archive[] = $ordre;
                        }

                        if(isset($data['statut'][$iterLine]))
                        {
                            $updateStatement[] = '`statut` = ?';
                            $insertStatementCol[] = '`statut`';
                            $insertStatementVal[] = '?';
                            $dataTable[] = (int)($data['statut'][$iterLine]);

                            $insertStatementCol_archive[] = '`statut`';
                            $insertStatementVal_archive[] = '?';
                            $dataTable_archive[] = (int)($data['statut'][$iterLine]);
                        }

                        if(isset($data['inactivation'][$iterLine]))
                        {
                            $updateStatement[] = '`ts_inactivation` = now()';
                            $insertStatementCol[] = '`ts_inactivation`';
                            $insertStatementVal[] = 'now()';

                            $updateStatement[] = '`usager_inactivation_id` = ?';
                            $insertStatementCol[] = '`usager_inactivation_id`';
                            $insertStatementVal[] = '?';
                            $dataTable[] = getIdUsager();
                        }

                        if(isset($data['reactivation'][$iterLine]))
                        {
                            $updateStatement[] = '`ts_inactivation` = null';
                            $insertStatementCol[] = '`ts_inactivation`';
                            $insertStatementVal[] = 'null';

                            $updateStatement[] = '`usager_inactivation_id` = null';
                            $insertStatementCol[] = '`usager_inactivation_id`';
                            $insertStatementVal[] = 'null';
                        }

                        $champsValides = 0;
                        foreach($cols as $iterCol => $col)
                        {
                            if (!isset($data[$col['COLUMN_NAME']])) {
                                continue;
                            }
                            $val_for_notification = null;
                            if($table == 'equipements_bio_medicaux' && in_array($col['COLUMN_NAME'], array('ts_solution_structure', 'ts_solution_architecture', 'ts_solution_telecom', 'ts_solution_mecanique', 'ts_solution_plomberie', 'ts_solution_electricite')))
                            {
                                continue;
                            }
                            if($table == 'equipements_bio_medicaux' && in_array($col['COLUMN_NAME'], array('usager_solution_structure_id', 'usager_solution_architecture_id', 'usager_solution_telecom_id', 'usager_solution_mecanique_id', 'usager_solution_plomberie_id', 'usager_solution_electricite_id')))
                            {
                                $exploded_col_name = dm_explode('_', $col['COLUMN_NAME']);
                                $discipline = $exploded_col_name[2];

                                if(!isset($data[$col['COLUMN_NAME']][$iterLine]))
                                {
                                    continue;
                                }

                                $val = (int)($data[$col['COLUMN_NAME']][$iterLine]);
                                if($val == 0)
                                {
                                    if ($col['IS_NULLABLE'] == 'NO') {
                                        $updateStatement[] = '`'.$col['COLUMN_NAME'].'` = ?';
                                        $insertStatementCol[] = '`'.$col['COLUMN_NAME'].'`';
                                        $insertStatementVal[] = '?';
                                        $dataTable[] = $col['COLUMN_DEFAULT'];
                                    } else {
                                        $updateStatement[] = '`'.$col['COLUMN_NAME'].'` = null';
                                        $insertStatementCol[] = '`'.$col['COLUMN_NAME'].'`';
                                        $insertStatementVal[] = 'null';
                                    }

                                    if (in_array($col['COLUMN_NAME'], $colsArchives)) {
                                        $insertStatementCol_archive[] = '`'.$col['COLUMN_NAME'].'`';
                                        $insertStatementVal_archive[] = 'null';
                                    }

                                    $updateStatement[] = "`ts_solution_$discipline` = null";
                                    $insertStatementCol[] = "`ts_solution_$discipline`";
                                    $insertStatementVal[] = 'null';

                                    if (in_array("ts_solution_$discipline", $colsArchives)) {
                                        $insertStatementCol_archive[] = "`ts_solution_$discipline`";
                                        $insertStatementVal_archive[] = 'null';
                                    }
                                }
                                else
                                {
                                    $update_current_bd = false;
                                    if(isset($data['id_ligne'][$iterLine]) && (int)($data['id_ligne'][$iterLine]) > 0 && isset($lignes_existantes[$data['id_ligne'][$iterLine]]))
                                    {
                                        $id_ligne = (int)($data['id_ligne'][$iterLine]);
                                        if(isset($lignes_existantes[$id_ligne]))
                                        {
                                            $ligne = $lignes_existantes[$id_ligne];
                                            if(
                                                isset($data["ts_solution_$discipline"][$iterLine])
                                                && $ligne["ts_solution_$discipline"] <> $data["ts_solution_$discipline"][$iterLine]
                                                &&  isset($data["conformite_du_besoin_".$discipline."_id"][$iterLine])
                                                && $data["conformite_du_besoin_".$discipline."_id"][$iterLine] <> $ligne["conformite_du_besoin_".$discipline."_id"]
                                            )
                                            {
                                                $update_current_bd = true;
                                            }
                                        }
                                    }
                                    else if(isset($data["conformite_du_besoin_".$discipline."_id"][$iterLine]) && in_array($data["conformite_du_besoin_".$discipline."_id"][$iterLine], array(1,2)))
                                    {
                                        $update_current_bd = true;
                                    }

                                    if($update_current_bd)
                                    {
                                        $updateStatement[] = '`'.$col['COLUMN_NAME'].'` = ?';
                                        $insertStatementCol[] = '`'.$col['COLUMN_NAME'].'`';
                                        $insertStatementVal[] = '?';
                                        $dataTable[] = $val;

                                        if (in_array($col['COLUMN_NAME'], $colsArchives)) {
                                            $insertStatementCol_archive[] = '`'.$col['COLUMN_NAME'].'`';
                                            $insertStatementVal_archive[] = '?';
                                            $dataTable_archive[] = $val;
                                        }

                                        $updateStatement[] = "`ts_solution_$discipline` = ?";
                                        $insertStatementCol[] = "`ts_solution_$discipline`";
                                        $insertStatementVal[] = '?';
                                        $dataTable[] = $data["ts_solution_$discipline"][$iterLine];

                                        if (in_array("ts_solution_$discipline", $colsArchives)) {
                                            $insertStatementCol_archive[] = "`ts_solution_$discipline`";
                                            $insertStatementVal_archive[] = '?';
                                            $dataTable_archive[] = $data["ts_solution_$discipline"][$iterLine];
                                        }
                                    }
                                }
                            }
                            else if(isset($data[$col['COLUMN_NAME']][$iterLine]) && $data[$col['COLUMN_NAME']][$iterLine] === '%force_null%')
                            {
                                $updateStatement[] = '`'.$col['COLUMN_NAME'].'` = null';
                                $insertStatementCol[] = '`'.$col['COLUMN_NAME'].'`';
                                $insertStatementVal[] = 'null';

                                if (in_array($col['COLUMN_NAME'], $colsArchives)) {
                                    $insertStatementCol_archive[] = '`'.$col['COLUMN_NAME'].'`';
                                    $insertStatementVal_archive[] = 'null';
                                }
                            }
                            else if(!isset($_GET['fromMobile']) && dm_substr($table, 0, 8) == 'generaux' && !isset($data['is_importation']) && in_array($col['COLUMN_NAME'], Fiches::getListeColonnes()))
                            {
                                $col_name = $col['COLUMN_NAME'];
                                $val = $fiche->$col_name;
                                if(!isset($val) || $val === 0 || dm_strlen($val) === 0)
                                {
                                    if ($col['IS_NULLABLE'] == 'NO') {
                                        $updateStatement[] = '`'.$col['COLUMN_NAME'].'` = ?';
                                        $insertStatementCol[] = '`'.$col['COLUMN_NAME'].'`';
                                        $insertStatementVal[] = '?';
                                        $dataTable[] = $col['COLUMN_DEFAULT'];
                                    } else {
                                        $updateStatement[] = '`'.$col['COLUMN_NAME'].'` = null';
                                        $insertStatementCol[] = '`'.$col['COLUMN_NAME'].'`';
                                        $insertStatementVal[] = 'null';
                                    }

                                    if (in_array($col['COLUMN_NAME'], $colsArchives)) {
                                        $insertStatementCol_archive[] = '`'.$col['COLUMN_NAME'].'`';
                                        $insertStatementVal_archive[] = 'null';
                                    }
                                }
                                else
                                {
                                    $updateStatement[] = '`'.$col['COLUMN_NAME'].'` = ?';
                                    $insertStatementCol[] = '`'.$col['COLUMN_NAME'].'`';
                                    $insertStatementVal[] = '?';
                                    $dataTable[] = $val;

                                    if (in_array($col['COLUMN_NAME'], $colsArchives)) {
                                        $insertStatementCol_archive[] = '`'.$col['COLUMN_NAME'].'`';
                                        $insertStatementVal_archive[] = '?';
                                        $dataTable_archive[] = $val;
                                    }

                                    $val_for_notification = $val;
                                }
                                $champsValides++;
                            }
                            else if($col['DATA_TYPE'] == 'tinyint')
                            {
                                $updateStatement[] = '`'.$col['COLUMN_NAME'].'` = ?';
                                $insertStatementCol[] = '`'.$col['COLUMN_NAME'].'`';
                                $insertStatementVal[] = '?';

                                $val = isset($data[$col['COLUMN_NAME']][$iterLine]) && (int)($data[$col['COLUMN_NAME']][$iterLine]) == 1 ? 1 : 0;
                                $dataTable[] = $val;

                                if (in_array($col['COLUMN_NAME'], $colsArchives)) {
                                    $insertStatementCol_archive[] = '`'.$col['COLUMN_NAME'].'`';
                                    $insertStatementVal_archive[] = '?';
                                    $dataTable_archive[] = $val;
                                }

                                $val_for_notification = $val;
                                $champsValides++;
                            }
                            else if(in_array($col['DATA_TYPE'], array('double', 'decimal')))
                            {
                                $val = isset($data[$col['COLUMN_NAME']][$iterLine]) ? $data[$col['COLUMN_NAME']][$iterLine] : '';
                                if(dm_strlen($val) === 0)
                                {
                                    if ($col['IS_NULLABLE'] == 'NO') {
                                        $updateStatement[] = '`'.$col['COLUMN_NAME'].'` = ?';
                                        $insertStatementCol[] = '`'.$col['COLUMN_NAME'].'`';
                                        $insertStatementVal[] = '?';
                                        $dataTable[] = $col['COLUMN_DEFAULT'];
                                    } else {
                                        $updateStatement[] = '`'.$col['COLUMN_NAME'].'` = null';
                                        $insertStatementCol[] = '`'.$col['COLUMN_NAME'].'`';
                                        $insertStatementVal[] = 'null';
                                    }

                                    if (in_array($col['COLUMN_NAME'], $colsArchives)) {
                                        $insertStatementCol_archive[] = '`'.$col['COLUMN_NAME'].'`';
                                        $insertStatementVal_archive[] = 'null';
                                    }
                                }
                                else
                                {
                                    $updateStatement[] = '`'.$col['COLUMN_NAME'].'` = ?';
                                    $insertStatementCol[] = '`'.$col['COLUMN_NAME'].'`';
                                    $insertStatementVal[] = '?';

                                    $nb_decimal = in_array($col['COLUMN_NAME'], array('coord_x', 'coord_y', 'coord_z')) ? 15 : 2;
                                    $val = dm_number_format($val, $nb_decimal, '.', '');
                                    $dataTable[] = $val;

                                    if (in_array($col['COLUMN_NAME'], $colsArchives)) {
                                        $insertStatementCol_archive[] = '`'.$col['COLUMN_NAME'].'`';
                                        $insertStatementVal_archive[] = '?';
                                        $dataTable_archive[] = $val;
                                    }

                                    $val_for_notification = $val;
                                }
                                $champsValides++;
                            }
                            else if(isset($data[$col['COLUMN_NAME']][$iterLine]))
                            {
                                $val = ($col['DATA_TYPE'] == 'int' ? (int)($data[$col['COLUMN_NAME']][$iterLine]) : (string)($data[$col['COLUMN_NAME']][$iterLine]));
                                if(($val === 0 && !str_starts_with($col['COLUMN_NAME'], 'qte_')) || dm_strlen($val) === 0)
                                {
                                    if (!in_array('`' . $col['COLUMN_NAME'] . '`', $insertStatementCol)) {
                                        if ($col['IS_NULLABLE'] == 'NO') {
                                            $updateStatement[] = '`' . $col['COLUMN_NAME'] . '` = ?';
                                            $insertStatementCol[] = '`' . $col['COLUMN_NAME'] . '`';
                                            $insertStatementVal[] = '?';
                                            $dataTable[] = $col['COLUMN_DEFAULT'];
                                        } else {
                                            $updateStatement[] = '`' . $col['COLUMN_NAME'] . '` = null';
                                            $insertStatementCol[] = '`' . $col['COLUMN_NAME'] . '`';
                                            $insertStatementVal[] = 'null';
                                        }
                                    }

                                    if (in_array($col['COLUMN_NAME'], $colsArchives)) {
                                        $insertStatementCol_archive[] = '`'.$col['COLUMN_NAME'].'`';
                                        $insertStatementVal_archive[] = 'null';
                                    }
                                }
                                else
                                {
                                    if (!in_array('`' . $col['COLUMN_NAME'] . '`', $insertStatementCol)) {
                                        $updateStatement[] = '`' . $col['COLUMN_NAME'] . '` = ?';
                                        $insertStatementCol[] = '`' . $col['COLUMN_NAME'] . '`';
                                        $insertStatementVal[] = '?';
                                        $dataTable[] = $val;
                                    }

                                    if (in_array($col['COLUMN_NAME'], $colsArchives)) {
                                        $insertStatementCol_archive[] = '`'.$col['COLUMN_NAME'].'`';
                                        $insertStatementVal_archive[] = '?';
                                        $dataTable_archive[] = $val;
                                    }

                                    $val_for_notification = $val;
                                }

                                $champsValides++;
                            }

                            if(in_array($formulaire::$TYPE_FORM, array('LISTE', 'LISTE_COORDINATION')))
                            {
                                if(isset($data['id_ligne'][$iterLine]) && (int)($data['id_ligne'][$iterLine]) > 0 && isset($lignes_existantes[$data['id_ligne'][$iterLine]]))
                                {
                                    if($val_for_notification <> $lignes_existantes[$data['id_ligne'][$iterLine]][$col['COLUMN_NAME']])
                                    {
                                        $champs_a_notifier[] = $col['COLUMN_NAME'];
                                    }
                                }
                                else
                                {
                                    $champs_a_notifier[] = $col['COLUMN_NAME'];
                                }
                            }
                            else if(in_array($formulaire::$TYPE_FORM, array('UNIQUE')))
                            {
                                if(isset($ligne_existante))
                                {
                                    if($val_for_notification <> $ligne_existante[$col['COLUMN_NAME']])
                                    {
                                        $champs_a_notifier[] = $col['COLUMN_NAME'];
                                    }
                                }
                                else
                                {
                                    $champs_a_notifier[] = $col['COLUMN_NAME'];
                                }
                            }
                        }

                        if($champsValides > 0)
                        {
                            $laLigneExistante = null;
                            $isInsert = false;
                            $id_ligne = null;
                            if(isset($data['id_ligne'][$iterLine]) && (int)($data['id_ligne'][$iterLine]) > 0 && isset($lignes_existantes[$data['id_ligne'][$iterLine]]))
                            {
                                $id_ligne = (int)($data['id_ligne'][$iterLine]);

                                $dataTableUpdate = $dataTable;
                                $dataTableUpdate[] = $id_ligne;

                                $sql = "UPDATE `" . db::tableColonneValide($db_n) . "`.`" . db::tableColonneValide($table) . "` SET  ".dm_implode(", ", $updateStatement)." WHERE id = ?";
//                                printDebug(dm_nl2br(db::interpolateQuery($sql, $dataTableUpdate)));
                                $db->query($sql, $dataTableUpdate);

                                $laLigneExistante = $lignes_existantes[$id_ligne];
                                unset($lignes_existantes[$id_ligne]);
                            }
                            else if(isset($ligne_existante))
                            {
                                $id_ligne = (int)($ligne_existante['id']);
                                $laLigneExistante = $ligne_existante;

                                $dataTableUpdate = $dataTable;
                                $dataTableUpdate[] = $id_ligne;

                                $sql = "UPDATE `" . db::tableColonneValide($db_n) . "`.`" . db::tableColonneValide($table) . "` SET  ".dm_implode(", ", $updateStatement)." WHERE id = ?";
//                                printDebug(dm_nl2br(db::interpolateQuery($sql, $dataTableUpdate)));
                                $db->query($sql, $dataTableUpdate);
                            }
                            else
                            {
                                $sql = "INSERT INTO `" . db::tableColonneValide($db_n) . "`.`" . db::tableColonneValide($table) . "` (".dm_implode(", ", $insertStatementCol).") VALUES (".dm_implode(", ", $insertStatementVal).")";
//                                printDebug(dm_nl2br(db::interpolateQuery($sql, $dataTable)));exit;
                                $db->query($sql, $dataTable);
                                $id_ligne = $db->lastInsertId();
                                $isInsert = true;

                                if ($table == 'equipements_bio_medicaux') {
                                    $sql = "INSERT INTO acquisitions_projets_groupes_equipements_fiches (fiche_id, acquisition_projet_groupe_equipement_id, quantite, quantite_requise, quantite_existante, quantite_distribuee, quantite_demenager) 
                                            SELECT ebm.fiche_id, apge.id, 0 as quantite, qte_requise as quantite_requise, qte_existante as quantite_existante, 0 as quantite_distribuee, qte_conservee as quantite_demenager 
                                            from equipements_bio_medicaux ebm 
                                            join acquisitions_projets_fiches apf on apf.fiche_id = ebm.fiche_id 
                                            join acquisitions_projets_groupes_equipements apge on apge.equipement_bio_medicaux_type_id = ebm.equipement_bio_medicaux_type_id and apge.acquisition_projet_id = apf.acquisition_projet_id
                                            where ebm.id = ?
                                            ON DUPLICATE KEY UPDATE fiche_id = ebm.fiche_id;";
                                    $db->query($sql, [$id_ligne]);
                                }
                            }

                            if ($table == 'equipements_bio_medicaux') {
                                if (isset($data['equipement_bio_medicaux_type_id'][$iterLine]) && ($isInsert || (isset($laLigneExistante) && (int)$data['equipement_bio_medicaux_type_id'][$iterLine] <> $laLigneExistante['equipement_bio_medicaux_type_id']))) {
                                    $equiType = new EquipementsBioMedicauxTypes((int)$data['equipement_bio_medicaux_type_id'][$iterLine]);
                                    if ($equiType->taxable == 1) {
                                        $sql = "update equipements_bio_medicaux set taux_taxation = 1.05687875 where id = ?";
                                        $db->query($sql, [$id_ligne]);
                                    }

                                    $sql = "select fpd.*
                                            from financements_projets_fiches f 
                                            join financements_projets fp on f.financement_projet_id = fp.id
                                            join financements_projets_depots fpd on fp.id = fpd.financement_projet_id
                                            where fiche_id = ?";
                                    $financement_projet_depots = $db->getAll($sql, [$fiche_id]);

                                    $updatesDepotsSources = [
                                        1 => 'depot_source1_id = null',
                                        2 => 'depot_source2_id = null',
                                        3 => 'depot_source3_id = null',
                                        4 => 'depot_source4_id = null',
                                        5 => 'depot_source5_id = null',
                                    ];
                                    foreach ($financement_projet_depots as $depot) {
                                        $sql = "select count(*) as nb
                                                from equipements_bio_medicaux ebm
                                                where depot_source" . (int)$depot['source_financement_id'] . "_id = ? 
                                                    and equipement_bio_medicaux_type_id = ? 
                                                    and id <> ?";
                                        $nb = $db->getOne($sql, [$depot['id'], $data['equipement_bio_medicaux_type_id'][$iterLine], $id_ligne]);

                                        if ($nb > 0) {
                                            $updatesDepotsSources[(int)$depot['source_financement_id']] = "depot_source" . (int)$depot['source_financement_id'] . "_id = " . $depot['id'];
                                        }
                                    }

                                    $sql = "update equipements_bio_medicaux set " . implode(', ', $updatesDepotsSources) . " where id = ?";
                                    $db->query($sql, [$id_ligne]);
                                }
                            }

                            $insertStatementCol_archive[] = '`ts_ajout`';
                            $insertStatementVal_archive[] = 'current_timestamp';

                            $insertStatementCol_archive[] = '`usager_ajout_id`';
                            $insertStatementVal_archive[] = getIdUsager();

                            $insertStatementCol_archive[] = '`id`';
                            $insertStatementVal_archive[] = '?';
                            $dataTable_archive[] = $id_ligne;

                            $sql = "INSERT INTO `" . db::tableColonneValide($db_a) . "`.`" . db::tableColonneValide($table) . "` (".dm_implode(", ", $insertStatementCol_archive).") VALUES (".dm_implode(", ", $insertStatementVal_archive).")";
                            //printDebug(dm_nl2br(db::interpolateQuery($sql, $dataTable)));
                            $db->query($sql, $dataTable_archive);

                            if($is_qte_form && isset($first_champ) && isset($data[$first_champ['champ_id']][$iterLine]) && $data[$first_champ['champ_id']][$iterLine] > 0)
                            {
                                unset($lignes_types_existantes[$data[$first_champ['champ_id']][$iterLine]]);
                            }

                            $ordre++;
                            $nb_lignes++;

                            if(in_array($table, ['prises_electriques_coordinations', 'prises_communications_coordinations']) && isset($_GET['equipement_bio_medicaux_coordination']))
                            {
                                $table_coord = $formulaire::SQL_TABLE_NAME_COORD;
                                $col_coord = $formulaire::SQL_COLOMN_NAME_COORD;
                                $sql = "select equipement_bio_medicaux_id, equipement_bio_medicaux_id from `" . db::tableColonneValide($table_coord) . "` where `" . db::tableColonneValide($col_coord) . "` = ?";
                                $equipements_to_delete = $db->getAssoc($sql, [$id_ligne]);

                                if (isset($data['qte_equipement_requis_now'][$iterLine])) {
                                    foreach ($data['qte_equipement_requis_now'][$iterLine] as $ii => $val) {
                                        $qte_prise_requise = (int)$data['qte_requise'][$iterLine] ?? 0;
                                        $sql = "INSERT INTO `" . db::tableColonneValide($table_coord) . "` (equipement_bio_medicaux_id, `" . db::tableColonneValide($col_coord) . "`, qte_equipement_requis, qte_prise_requise, qte_besoin_equipement) values 
                                                (?, ?, ?, ?, ?)
                                                ON DUPLICATE KEY UPDATE qte_equipement_requis = ?, qte_prise_requise = ?, qte_besoin_equipement = ?";
                                        $db->query($sql, [
                                            $data['equipement_bio_medicaux_id'][$iterLine][$ii],
                                            $id_ligne,
                                            $data['qte_equipement_requis_now'][$iterLine][$ii],
                                            $qte_prise_requise,
                                            $data['qte_besoin_equipement'][$iterLine][$ii],
                                            $data['qte_equipement_requis_now'][$iterLine][$ii],
                                            $qte_prise_requise,
                                            $data['qte_besoin_equipement'][$iterLine][$ii],
                                        ]);
                                        unset($equipements_to_delete[$data['equipement_bio_medicaux_id'][$iterLine][$ii]]);
                                    }
                                }

                                if (count($equipements_to_delete) > 0) {
                                    $dataQuery = array_merge([$id_ligne], array_keys($equipements_to_delete));
                                    $sql = "delete from `" . db::tableColonneValide($table_coord) . "` where `" . db::tableColonneValide($col_coord) . "` = ? and equipement_bio_medicaux_id in (" . db::placeHolders($equipements_to_delete) . ")";
                                    $db->query($sql, $dataQuery);
                                }
                            }
                        }
                    }
                }
                if(isset($lignes_existantes) && is_array($lignes_existantes) && count($lignes_existantes) > 0)
                {
                    foreach($lignes_existantes as $iter_tmp => $ligne_to_delete)
                    {
                        $sql = "delete from `" . db::tableColonneValide($db_n) . "`.`" . db::tableColonneValide($table) . "` where `id` = ?";
                        $db->query($sql, array((int)($ligne_to_delete['id'])));

                        if ($table == 'equipements_bio_medicaux') {
                            $sql = "delete from acquisitions_projets_groupes_equipements_fiches where fiche_id = ? and acquisition_projet_groupe_equipement_id in (
                                        select id from acquisitions_projets_groupes_equipements where equipement_bio_medicaux_type_id = ? 
                                    )";
                            $db->query($sql, [(int)($ligne_to_delete['fiche_id']), (int)($ligne_to_delete['equipement_bio_medicaux_type_id'])]);
                        }
                    }
                }

                if($is_qte_form && isset($first_champ) && isset($lignes_types_existantes) && is_array($lignes_types_existantes) && count($lignes_types_existantes) > 0)
                {
                    foreach($lignes_types_existantes as $champ_id_tmp => $id_tmp)
                    {
                        if(dm_strlen($champ_id_tmp) > 0 && (int)($champ_id_tmp) > 0)
                        {
                            $sql = "INSERT INTO `" . db::tableColonneValide($db_a) . "`.`" . db::tableColonneValide($table) . "` (id, fiche_id, projet_id, ".$first_champ['champ_id'].", qte_requise, ordre) VALUES (?, ?, ?, ?, ?, ?);";
                            $db->query($sql, array($id_tmp, $fiche_id, $projet_id, $champ_id_tmp, 0, 999999));
                        }
                    }
                }

                $sql = "insert into `" . db::tableColonneValide($db_n) . "`.`fiches_logs` (fiche_id, formulaire_id, projet_id, ts_ajout, usager_ajout_id, code, nb_lignes, raison, code_client) values (?, ?, ?, current_timestamp, ?, ?, ?, ?, ?)";
                $db->query($sql, array($fiche_id, $formulaire_id, $projet_id, getIdUsager(), 'updateFicheForm', $nb_lignes, (isset($data['raison']) ? $data['raison'] : ''), $CodeClient));
                $fiche_log_id = $db->lastInsertId();

                NotificationsGestion::creationNotifications($fiche_log_id, $fiche_id, $formulaire_id, $champs_a_notifier, 0);

                $sql = "UPDATE `" . db::tableColonneValide($db_n) . "`.`fiches` SET ts_modif = current_timestamp, usager_modif_id = ? WHERE id = ?";
                $db->query($sql, array(getIdUsager(), $fiche_id));

                $sql = "INSERT INTO `" . db::tableColonneValide($db_a) . "`.fiches_formulaires (fiche_id, formulaire_id, commentaire, ts_ajout, usager_ajout_id) 
                        VALUES (?, ?, ?, current_timestamp, ?)";
                $db->query($sql, array($fiche_id, $formulaire_id, (isset($data['fiche_formulaire_commentaire']) ? $data['fiche_formulaire_commentaire'] : ''), getIdUsager()));

                $sql = "INSERT INTO `" . db::tableColonneValide($db_n) . "`.fiches_formulaires (fiche_id, formulaire_id, commentaire) 
                        VALUES (?, ?, ?)";
                $db->query($sql, array($fiche_id, $formulaire_id, (isset($data['fiche_formulaire_commentaire']) ? $data['fiche_formulaire_commentaire'] : '')));

                if($form->discipline_id > 0)
                {
                    $sql = "select count(*) as nb from `" . db::tableColonneValide($db_n) . "`.fiches_disciplines where fiche_id = ? and discipline_id = ?";
                    $row_nb = $db->getRow($sql, array($fiche_id, $form->discipline_id));

                    if($row_nb['nb'] == 0)
                    {
                        $sql = "insert into `" . db::tableColonneValide($db_n) . "`.fiches_disciplines (fiche_id, discipline_id) values(?, ?)";
                        $db->query($sql, array($fiche_id, $form->discipline_id));
                    }
                }

                if(in_array($formulaire, array('ProprietesElectriques', 'EquipementsDistributionsElectriques', 'EquipementsTransformationsElectriques')))
                {
                    $sql = "UPDATE `" . db::tableColonneValide($db_n) . "`.`raccords_electriques_coordinations` SET statut_exigence = 0 WHERE fiche_id = ?";
                    $db->query($sql, array($fiche_id));
                }

                if(in_array($formulaire, array('ProprietesElectriquesAuxiliaires', 'EquipementsDistributionsElectriques', 'EquipementsTransformationsElectriques')))
                {
                    $sql = "UPDATE `" . db::tableColonneValide($db_n) . "`.`raccords_electriques_coordinations_auxiliaires` SET statut_exigence = 0 WHERE fiche_id = ?";
                    $db->query($sql, array($fiche_id));
                }

                if (dm_substr($table, 0, 8) == 'generaux' && isset($_GET['fromMobile'])) {
                    $table_to_update = 'fiches';
                    $champ_id_to_use = 'id';
                    $data_to_use = $data;

                    $updateStatement = array();
                    $dataUpdate = array();

                    if(isset($data_to_use['fiche_local_id'][0]) && dm_strlen($data_to_use['fiche_local_id'][0]) > 0 && (int)($data_to_use['fiche_local_id'][0]) > 0)
                    {
                        $updateStatement[] = '`fiche_local_id` = ?';
                        $dataUpdate[] = $data_to_use['fiche_local_id'][0];
                    }
                    else
                    {
                        $updateStatement[] = '`fiche_local_id` = null';
                    }

                    $dataUpdate[] = $fiche_id;
                    $sql = "UPDATE `" . db::tableColonneValide($db_n) . "`.`" . db::tableColonneValide($table_to_update) . "` SET  ".dm_implode(", ", $updateStatement)." WHERE $champ_id_to_use = ?";
//	                printDebug(dm_nl2br(db::interpolateQuery($sql, $dataUpdate)));exit;
                    $db->query($sql, $dataUpdate);
                } else if(dm_substr($table, 0, 8) == 'generaux' && isset($data['is_importation']) && $data['is_importation'] == 1) {
                    $table_to_update = 'fiches';
                    $champ_id_to_use = 'id';
                    $data_to_use = $data;

                    $updateStatement = array();
                    $dataUpdate = array();

                    if(isset($data_to_use['code'][0]) && dm_strlen($data_to_use['code'][0]) > 0)
                    {
                        $updateStatement[] = '`code` = ?';
                        $dataUpdate[] = $data_to_use['code'][0];
                    }
                    else
                    {
                        $updateStatement[] = '`code` = null';
                    }

                    if(isset($data_to_use['numero_lot'][0]) && dm_strlen($data_to_use['numero_lot'][0]) > 0)
                    {
                        $updateStatement[] = '`numero_lot` = ?';
                        $dataUpdate[] = $data_to_use['numero_lot'][0];
                    }
                    else
                    {
                        $updateStatement[] = '`numero_lot` = null';
                    }

                    if(isset($data_to_use['code_alternatif'][0]) && dm_strlen($data_to_use['code_alternatif'][0]) > 0)
                    {
                        $updateStatement[] = '`code_alternatif` = ?';
                        $dataUpdate[] = $data_to_use['code_alternatif'][0];
                    }
                    else
                    {
                        $updateStatement[] = '`code_alternatif` = null';
                    }

                    if(isset($data_to_use['code_alternatif_2'][0]) && dm_strlen($data_to_use['code_alternatif_2'][0]) > 0)
                    {
                        $updateStatement[] = '`code_alternatif_2` = ?';
                        $dataUpdate[] = $data_to_use['code_alternatif_2'][0];
                    }
                    else
                    {
                        $updateStatement[] = '`code_alternatif_2` = null';
                    }

                    if(isset($data_to_use['id_revit'][0]) && dm_strlen($data_to_use['id_revit'][0]) > 0)
                    {
                        $updateStatement[] = '`id_revit` = ?';
                        $dataUpdate[] = $data_to_use['id_revit'][0];
                    }
                    else
                    {
                        $updateStatement[] = '`id_revit` = null';
                    }

                    if(isset($data_to_use['revit_unique_id'][0]) && dm_strlen($data_to_use['revit_unique_id'][0]) > 0)
                    {
                        $updateStatement[] = '`revit_unique_id` = ?';
                        $dataUpdate[] = $data_to_use['revit_unique_id'][0];
                    }
                    else
                    {
                        $updateStatement[] = '`revit_unique_id` = null';
                    }

                    if(isset($data_to_use['id_codebook'][0]) && dm_strlen($data_to_use['id_codebook'][0]) > 0)
                    {
                        $updateStatement[] = '`id_codebook` = ?';
                        $dataUpdate[] = $data_to_use['id_codebook'][0];
                    }
                    else
                    {
                        $updateStatement[] = '`id_codebook` = null';
                    }

                    if(isset($data_to_use['batiment_id'][0]) && dm_strlen($data_to_use['batiment_id'][0]) > 0 && (int)($data_to_use['batiment_id'][0]) > 0)
                    {
                        $updateStatement[] = '`batiment_id` = ?';
                        $dataUpdate[] = $data_to_use['batiment_id'][0];
                    }
                    else
                    {
                        $updateStatement[] = '`batiment_id` = null';
                    }

                    if(isset($data_to_use['route_id'][0]) && dm_strlen($data_to_use['route_id'][0]) > 0 && (int)($data_to_use['route_id'][0]) > 0)
                    {
                        $updateStatement[] = '`route_id` = ?';
                        $dataUpdate[] = $data_to_use['route_id'][0];
                    }
                    else
                    {
                        $updateStatement[] = '`route_id` = null';
                    }

                    if(isset($data_to_use['chainage_debut'][0]) && dm_strlen($data_to_use['chainage_debut'][0]) > 0 && (int)($data_to_use['chainage_debut'][0]) > 0)
                    {
                        $updateStatement[] = '`chainage_debut` = ?';
                        $dataUpdate[] = $data_to_use['chainage_debut'][0];
                    }
                    else
                    {
                        $updateStatement[] = '`chainage_debut` = null';
                    }

                    if(isset($data_to_use['chainage_fin'][0]) && dm_strlen($data_to_use['chainage_fin'][0]) > 0 && (int)($data_to_use['chainage_fin'][0]) > 0)
                    {
                        $updateStatement[] = '`chainage_fin` = ?';
                        $dataUpdate[] = $data_to_use['chainage_fin'][0];
                    }
                    else
                    {
                        $updateStatement[] = '`chainage_fin` = null';
                    }

                    if(isset($data_to_use['fiche_type_id'][0]) && dm_strlen($data_to_use['fiche_type_id'][0]) > 0 && (int)($data_to_use['fiche_type_id'][0]) > 0)
                    {
                        $updateStatement[] = '`fiche_type_id` = ?';
                        $dataUpdate[] = $data_to_use['fiche_type_id'][0];
                    }
                    else
                    {
                        $updateStatement[] = '`fiche_type_id` = null';
                    }

                    if(isset($data_to_use['fiche_sous_type_id'][0]) && dm_strlen($data_to_use['fiche_sous_type_id'][0]) > 0 && (int)($data_to_use['fiche_sous_type_id'][0]) > 0)
                    {
                        $updateStatement[] = '`fiche_sous_type_id` = ?';
                        $dataUpdate[] = $data_to_use['fiche_sous_type_id'][0];
                    }
                    else
                    {
                        $updateStatement[] = '`fiche_sous_type_id` = null';
                    }

                    if(isset($data_to_use['batiment_niveau_element_id'][0]) && dm_strlen($data_to_use['batiment_niveau_element_id'][0]) > 0 && (int)($data_to_use['batiment_niveau_element_id'][0]) > 0)
                    {
                        $updateStatement[] = '`batiment_niveau_element_id` = ?';
                        $dataUpdate[] = $data_to_use['batiment_niveau_element_id'][0];
                    }
                    else
                    {
                        $updateStatement[] = '`batiment_niveau_element_id` = null';
                    }

                    if(isset($data_to_use['fiche_local_id'][0]) && dm_strlen($data_to_use['fiche_local_id'][0]) > 0 && (int)($data_to_use['fiche_local_id'][0]) > 0)
                    {
                        $updateStatement[] = '`fiche_local_id` = ?';
                        $dataUpdate[] = $data_to_use['fiche_local_id'][0];
                    }
                    else
                    {
                        $updateStatement[] = '`fiche_local_id` = null';
                    }

                    if(isset($data_to_use['fiche_local_destination_id'][0]) && dm_strlen($data_to_use['fiche_local_destination_id'][0]) > 0 && (int)($data_to_use['fiche_local_destination_id'][0]) > 0)
                    {
                        $updateStatement[] = '`fiche_local_destination_id` = ?';
                        $dataUpdate[] = $data_to_use['fiche_local_destination_id'][0];
                    }
                    else
                    {
                        $updateStatement[] = '`fiche_local_destination_id` = null';
                    }

                    if(isset($data_to_use['fiche_local_controleur_id'][0]) && dm_strlen($data_to_use['fiche_local_controleur_id'][0]) > 0 && (int)($data_to_use['fiche_local_controleur_id'][0]) > 0)
                    {
                        $updateStatement[] = '`fiche_local_controleur_id` = ?';
                        $dataUpdate[] = $data_to_use['fiche_local_controleur_id'][0];
                    }
                    else
                    {
                        $updateStatement[] = '`fiche_local_controleur_id` = null';
                    }

                    if(isset($data_to_use['fiche_parent_id'][0]) && dm_strlen($data_to_use['fiche_parent_id'][0]) > 0 && (int)($data_to_use['fiche_parent_id'][0]) > 0)
                    {
                        $updateStatement[] = '`fiche_parent_id` = ?';
                        $dataUpdate[] = $data_to_use['fiche_parent_id'][0];
                    }
                    else
                    {
                        $updateStatement[] = '`fiche_parent_id` = null';
                    }

                    if(isset($data_to_use['gestion_porte_type_id'][0]) && dm_strlen($data_to_use['gestion_porte_type_id'][0]) > 0 && (int)($data_to_use['gestion_porte_type_id'][0]) > 0)
                    {
                        $updateStatement[] = '`gestion_porte_type_id` = ?';
                        $dataUpdate[] = $data_to_use['gestion_porte_type_id'][0];
                    }
                    else
                    {
                        $updateStatement[] = '`gestion_porte_type_id` = null';
                    }

                    if(isset($data_to_use['description'][0]) && dm_strlen($data_to_use['description'][0]) > 0 && (int)($data_to_use['description'][0]) > 0)
                    {
                        $updateStatement[] = '`description` = ?';
                        $dataUpdate[] = $data_to_use['description'][0];
                    }
                    else
                    {
                        $updateStatement[] = '`description` = null';
                    }

                    $dataUpdate[] = $fiche_id;
                    $sql = "UPDATE `" . db::tableColonneValide($db_n) . "`.`" . db::tableColonneValide($table_to_update) . "` SET  ".dm_implode(", ", $updateStatement)." WHERE $champ_id_to_use = ?";
                    //printDebug(dm_nl2br(db::interpolateQuery($sql, $dataUpdate)));exit;
                    $db->query($sql, $dataUpdate);
                }

                $champs_textes = ChampsTextesExports::getListe(array('formulaire_id' => $formulaire_id));
                foreach($champs_textes as $i => $champ_texte)
                {
                    $champ_texte->regenererTextes($fiche_id);
                }

                if(!$already_in_transac)
                {
                    $db->commit();
                }
            }
        }
        return true;
    }

    /**
     * @return array|void
     */
    public static function getFlatChampsSaisieMetaData(bool $ignoreHidden = false, bool $ignoreCoords = false): array
    {
        global $cache_meta;

        $metas = [];
        /** @var self $currentClassName */
        $currentClassName = get_called_class();

        $cacheKey = $currentClassName . '-' . ($ignoreHidden ? 1 : 0) . ($ignoreCoords ? 1 : 0);

        if (isset($cache_meta[$cacheKey])) {
            $metas = $cache_meta[$cacheKey];
        } else {
            $metas_tmp = $currentClassName::getChampsSaisieMetaData();

            if (isset($metas_tmp['regroupements'])) {
                foreach ($metas_tmp['regroupements'] as $id => $regroupement) {
                    foreach ($regroupement['data'] as $key => $meta) {
                        if ((!$ignoreHidden || (!isset($meta['hidden']) && !isset($meta['hidden_from_fiche']))) && (!$ignoreCoords || !isset($meta['is_reponse_coord']))) {
                            $metas[$key] = $meta;
                        }
                    }
                }
            } else {
                if ($ignoreHidden || $ignoreCoords) {
                    foreach ($metas_tmp as $key => $meta) {
                        if ((!$ignoreHidden || (!isset($meta['hidden']) && !isset($meta['hidden_from_fiche']))) && (!$ignoreCoords || !isset($meta['is_reponse_coord']))) {
                            $metas[$key] = $meta;
                        }
                    }
                } else {
                    $metas = $metas_tmp;
                }
            }

            $cache_meta[$cacheKey] = $metas;
        }

        return $metas ?? [];
    }

    /**
     * @return array
     */
    public static function getChampsObligatoires()
    {
        /** @var self $currentClassName */
        $currentClassName = get_called_class();
        $metas = $currentClassName::getFlatChampsSaisieMetaData();

        $champs_obligatoires = array();
        foreach($metas as $key => $meta)
        {
            if(isset($meta['obligatoire']) && $meta['obligatoire'])
            {
                $champs_obligatoires[$meta['champ_id']] = $meta['titre'];
            }
        }

        return $champs_obligatoires;
    }

    /**
     * @return array
     */
    public static function getChampsRecommandes()
    {
        /** @var self $currentClassName */
        $currentClassName = get_called_class();
        $metas = $currentClassName::getFlatChampsSaisieMetaData();

        $champs_recommandes = array();
        foreach($metas as $key => $meta)
        {
            if(isset($meta['recommande']) && $meta['recommande'])
            {
                $champs_recommandes[$meta['champ_id']] = $meta['titre'];
            }
        }

        return $champs_recommandes;
    }

    /**
     * @param $data
     * @param $id
     * @return bool
     */
    public static function validate($data, $id)
    {
        $valide = true;

        /** @var self $currentClassName */
        $currentClassName = get_called_class();
        $champs_obligatoires = $currentClassName::getChampsObligatoires();
        $champs_recommandes = $currentClassName::getChampsRecommandes();

        $formulaire_id = Formulaires::getIdFromNom($currentClassName);
        if ($formulaire_id > 0) {
            $formulaire = new Formulaires($formulaire_id);
            if (in_array($formulaire->type_formulaire_id, [1, 2])) {
                $message_obligatoire = txt("Les champs suivants sont obligatoires: ");
                $champs_obligatoires_vides = array();

                $message_recommandes = txt(dm_addslashes("Attention! Un ou plusieurs champs requis pour la coordination des raccords sont manquants. Veuillez prendre note que vous devrez rééditer ce formulaire ultérieurement afin d'y inscrire l'information manquante sans quoi le raccord ne pourra être complété adéquatement."), false);
                $champs_recommandes_vides = array();

                foreach ($data as $colomnName => $value) {
                    if ($colomnName == 'raison') {
                        if (dm_strlen($value) == 0) {
                            setErreur(txt('Le motif du changement est obligatoire.'));
                            $valide = false;
                        }
                    }

                    if ((!isset($value[0]) || dm_strlen($value[0]) == 0) && isset($champs_obligatoires[$colomnName])) {
                        $champs_obligatoires_vides[$colomnName] = $champs_obligatoires[$colomnName];
                        $valide = false;
                    } else if ((!isset($value[0]) || dm_strlen($value[0]) == 0) && isset($champs_recommandes[$colomnName])) {
                        $champs_recommandes_vides[$colomnName] = dm_addslashes($champs_recommandes[$colomnName]);
                        $valide = true;
                    }
                }

                if (count($champs_obligatoires_vides) > 0) {
                    setErreur($message_obligatoire . '<br />' . dm_implode('<br />', $champs_obligatoires_vides));
                }

                if (count($champs_recommandes_vides) > 0) {
                    setAvertissements($message_recommandes . '\n\n' . txt('Champ(s) manquant(s)') . ':\n\n- ' . dm_implode('\n- ', $champs_recommandes_vides));
                }
            }
        }

        return $valide;
    }

    /**
     * @param $fiche_id
     */
    public static function deleteFormulaire($fiche_id)
    {
        global $db, $db_name_archive;

        /** @var self $currentClassName */
        $currentClassName = get_called_class();
        $table = $currentClassName::SQL_TABLE_NAME;

        $formulaire_id = $currentClassName::getFormulaireId();

        $db->autocommit(false);

        if ($table == 'equipements_bio_medicaux') {
            $sql = "delete from acquisitions_projets_groupes_equipements_fiches where fiche_id = ? and acquisition_projet_groupe_equipement_id in (
                        select id from acquisitions_projets_groupes_equipements where equipement_bio_medicaux_type_id in (
                            select equipement_bio_medicaux_type_id from equipements_bio_medicaux where fiche_id = ?
                        )
                    )";
            $db->query($sql, [$fiche_id, $fiche_id]);
        }

        $sql = "select * from `" . db::tableColonneValide($table) . "` where `fiche_id` = ?";
        $lignes_existantes_tmp = $db->getAll($sql, array($fiche_id));

        $sql = "delete from `" . db::tableColonneValide($table) . "` where `fiche_id` = ?";
        $db->query($sql, array($fiche_id));

        $sql = "select count(*) as nb from `" . db::tableColonneValide($db_name_archive) . "`.`" . db::tableColonneValide($table) . "` where `fiche_id` = ? and token_archive is null";
        $row = $db->getRow($sql, array($fiche_id));

        $sql = "delete from `fiches_formulaires` where `fiche_id` = ? and `formulaire_id` = ?";
        $db->query($sql, array($fiche_id, $formulaire_id));

        $sql = "delete from `fiches_gabarits_formulaires` where `fiche_id` = ? and `formulaire_id` = ?";
        $db->query($sql, array($fiche_id, $formulaire_id));

        $sql = "delete from `fiches_gabarits_formulaires_lignes_inactives` where `fiche_id` = ? and `formulaire_id` = ?";
        $db->query($sql, array($fiche_id, $formulaire_id));

        $sql = "delete from `fiches_gabarits` where `fiche_id` = ? and not exists (
            select 1 from fiches_gabarits_formulaires fgf where fgf.`fiche_id` = ? and fgf.`gabarit_id` = `fiches_gabarits`.gabarit_id
        )";
        $db->query($sql, array($fiche_id, $fiche_id));

        $sql = "select discipline_id from formulaires where id = ?";
        $discipline_id = $db->getOne($sql, array($formulaire_id));

        $sql = "select count(*) from fiches_formulaires where fiche_id = ? and formulaire_id in (select id from formulaires where discipline_id = ?)";
        $nb = $db->getOne($sql, array($fiche_id, $discipline_id));

        if($nb == 0)
        {
            $sql = "delete from fiches_disciplines where fiche_id = ? and discipline_id = ?";
            $db->query($sql, array($fiche_id, $discipline_id));
        }

        if(isset($row['nb']) && (int)($row['nb']) > 0)
        {
            $token_archive = uniqid();
            $sql = "update `" . db::tableColonneValide($db_name_archive) . "`.`" . db::tableColonneValide($table) . "` set token_archive = ? where `fiche_id` = ? and token_archive is null";
            $db->query($sql, array($token_archive, $fiche_id));

            $sql = "update `fiches_logs` set token_archive = ? where `fiche_id` = ? and formulaire_id = ? and token_archive is null";
            $db->query($sql, array($token_archive, $fiche_id, $formulaire_id));
        }

        $sql = "insert into `fiches_logs` (fiche_id, formulaire_id, projet_id, ts_ajout, usager_ajout_id, code, nb_lignes, raison) values (?, ?, ?, current_timestamp, ?, ?, ?, ?)";
        $db->query($sql, array($fiche_id, $formulaire_id, getProjetId(), getIdUsager(), 'deleteFicheForm', 0, 'Suppression du formulaire'));

        if(isset($row['nb']) && (int)($row['nb']) > 0)
        {
            $form = new Formulaires($formulaire_id);
            $first_champ = $form->getFirstChamp();
            $forms_ids = Formulaires::getFormulairesIdWithQuantite();
            $is_qte_form = in_array($formulaire_id, $forms_ids);

            $lignes_types_existantes = array();
            if(in_array($currentClassName::$TYPE_FORM, array('LISTE', 'LISTE_COORDINATION')))
            {
                foreach($lignes_existantes_tmp as $iter => $ligne)
                {
                    if($is_qte_form && isset($first_champ))
                    {
                        $lignes_types_existantes[$ligne[$first_champ['champ_id']]] = $ligne['id'];
                    }
                }
            }

            $token_archive = uniqid();
            if($is_qte_form && isset($first_champ) && isset($lignes_types_existantes) && is_array($lignes_types_existantes) && count($lignes_types_existantes) > 0)
            {
                foreach($lignes_types_existantes as $champ_id_tmp => $id_tmp)
                {
                    if(dm_strlen($champ_id_tmp) > 0 && (int)($champ_id_tmp) > 0)
                    {
                        if ($table == 'equipements_bio_medicaux') {
                            $sql = "INSERT INTO `" . db::tableColonneValide($db_name_archive) . "`.`" . db::tableColonneValide($table) . "` (id, fiche_id, projet_id, ".$first_champ['champ_id'].", qte_requise, qte_existante, qte_fantome, qte_conservee, ordre, token_archive) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?);";
                            $db->query($sql, array($id_tmp, $fiche_id, getProjetId(), $champ_id_tmp, 0, 0, 0, 0, 999999, $token_archive));
                        } else {
                            $sql = "INSERT INTO `" . db::tableColonneValide($db_name_archive) . "`.`" . db::tableColonneValide($table) . "` (id, fiche_id, projet_id, ".$first_champ['champ_id'].", qte_requise, ordre, token_archive) VALUES (?, ?, ?, ?, ?, ?, ?);";
                            $db->query($sql, array($id_tmp, $fiche_id, getProjetId(), $champ_id_tmp, 0, 999999, $token_archive));
                        }
                    }
                }

                $sql = "insert into `fiches_logs` (fiche_id, formulaire_id, projet_id, ts_ajout, usager_ajout_id, code, nb_lignes, raison, token_archive) values (?, ?, ?, current_timestamp, ?, ?, ?, ?, ?)";
                $db->query($sql, array($fiche_id, $formulaire_id, getProjetId(), getIdUsager(), 'updateFicheForm', 0, 'Suppression du formulaire (ajustement des bilans)', $token_archive));
            }
        }

        $db->commit();
    }

    /**
     * @return mixed
     */
    public static function getFormulaireId()
    {
        global $db;

        /** @var self $currentClassName */
        $currentClassName = get_called_class();
        $currentClassName = $currentClassName == 'EquipementsBioMedicauxNoCoord' ? 'EquipementsBioMedicaux' : $currentClassName;

        $sql = "select id from `formulaires` where `nom` = ?";
        $row = $db->getRow($sql, array($currentClassName));

        return $row['id'];
    }

    /**
     * @param $fiche_id
     * @return int
     */
    public static function getNbArchivesForFiche($fiche_id)
    {
        global $db;

        /** @var self $currentClassName */
        $currentClassName = get_called_class();

        $sql = "select count(*) as nb from fiches_logs where fiche_id = ? and formulaire_id = (select id from formulaires where nom = ?) and code = 'updateFicheForm' and token_archive is not null";
        $row = $db->getRow($sql, array($fiche_id, $currentClassName));

        return isset($row['nb']) ? (int)($row['nb']) : 0;
    }

    /**
     * @param $fiche_id
     * @return array
     */
    public static function getArchivesForFiche($fiche_id)
    {
        global $db, $db_archive, $buffer, $db_name, $Langue, $CodeClient;

        /** @var self $currentClassName */
        $currentClassName = get_called_class();

        $sql_nom_usager = "CONCAT(u.prenom, ' ', u.nom)";
        $usager_join = "join usagers u on u.id = usager_ajout_id";

        $formulaire_id_row = $db->getRow("select id from formulaires where nom = ?", array($currentClassName));
        $formulaire_id = (int)($formulaire_id_row['id']);

        $sql = "select token_archive, fl.ts_ajout, $sql_nom_usager as usager, projet_id, p.code, p.nom, raison, coalesce(token_archive, 99999999) as ordre_token_archive
                from fiches_logs fl
                $usager_join
                join projets p on p.id = projet_id
                where fiche_id = ? and formulaire_id = ? and fl.code = 'updateFicheForm'
                order by ts_ajout desc, ordre_token_archive desc";
        $rows = $db->getAll($sql, array($fiche_id, $formulaire_id));

        return $rows;
    }

    /**
     * @return array
     */
    public static function getChampsReadOnly()
    {
        global $db;

        /** @var self $currentClassName */
        $currentClassName = get_called_class();
        $currentClassName = $currentClassName == 'EquipementsBioMedicauxNoCoord' ? 'EquipementsBioMedicaux' : $currentClassName;
        $sql = "select champ_nom 
                from formulaires_readonly fr
                join formulaires on formulaires.id = formulaire_id
                join formulaires_champs_readonly on formulaire_readonly_id = fr.id
                where formulaires.nom = ? and projet_id = ? and not exists (select usager_id from formulaires_usagers_exceptions_readonly fuer where fuer.formulaire_readonly_id = fr.id and fuer.usager_id = ?)";
        $champs_tmp = $db->getAll($sql, array($currentClassName, getProjetId(), getIdUsager()));

        $champs = array();
        foreach($champs_tmp as $i => $champ)
        {
            $champs[$champ['champ_nom']] = $champ['champ_nom'];
        }

        return $champs;
    }

    /**
     * @return array
     */
    public static function getChampsHidden()
    {
        global $db;

        /** @var self $currentClassName */
        $currentClassName = get_called_class();
        $currentClassName = $currentClassName == 'EquipementsBioMedicauxNoCoord' ? 'EquipementsBioMedicaux' : $currentClassName;
        $sql = "select champ_nom 
                from formulaires_hidden fr
                join formulaires on formulaires.id = formulaire_id
                join formulaires_champs_hidden on formulaire_hidden_id = fr.id
                where formulaires.nom = ? and projet_id = ? and not exists (select usager_id from formulaires_usagers_exceptions_hidden fuer where fuer.formulaire_hidden_id = fr.id and fuer.usager_id = ?)";
        $champs_tmp = $db->getAll($sql, array($currentClassName, getProjetId(), getIdUsager()));

        $champs = array();
        foreach($champs_tmp as $i => $champ)
        {
            $champs[$champ['champ_nom']] = $champ['champ_nom'];
        }

        return $champs;
    }

    /**
     * @return boolean
     */
    public static function canAddDeleteMove()
    {
        global $db, $Cache_canAddDeleteMove;

        if (!isset($Cache_canAddDeleteMove)) {
            /** @var self $currentClassName */
            $currentClassName = get_called_class();
            $currentClassName = $currentClassName == 'EquipementsBioMedicauxNoCoord' ? 'EquipementsBioMedicaux' : $currentClassName;

            $sql = "select fl.id, case when exists (select usager_id from formulaires_limitations_usagers_exceptions fuer where fuer.formulaire_limitation_id = fl.id and fuer.usager_id = ?) then 1 else 0 end canDoIt
                    from formulaires_limitations fl
                    join formulaires on formulaires.id = formulaire_id
                    where formulaires.nom = ? and projet_id = ?";
            $row = $db->getRow($sql, array(getIdUsager(), $currentClassName, getProjetId()));

            if (!isset($row)) {
                $Cache_canAddDeleteMove = true;
            } else {
                $Cache_canAddDeleteMove = (intval($row['canDoIt']) == 1);
            }
        }

        return $Cache_canAddDeleteMove;
    }

    /**
     *
     */
    public function getDocumentEditorHTML()
    {
        global $tables, $db_name;
        /** @var self $currentClassName */
        $currentClassName = get_called_class();

        $images = DocumentsListesElements::getListe(array('nom_table' => $currentClassName::SQL_TABLE_NAME, 'liste_item_id' => $this->id, 'is_image_principale' => 1));
        $images = array_values($images);
        $image = isset($images[0]) ? $images[0] : null;

        $url_image = 'Docmatic.png';
        if(isset($image))
        {
            $url_image = 'index.php?action=download_document&id='.$image->id;
        }

        $nom = $tables['client'][$currentClassName::SQL_TABLE_NAME]['nom'];
        ?>
        <script>
			$(document).ready(function(){
				$('#file').change(function(){
					$('#upload').show();
				});
				$('#image').change(function(){
					$('#upload_image').show();
				});
			});
        </script>
        <form action="index.php?action=upload_document&nom_table=<?=$currentClassName::SQL_TABLE_NAME?>&liste_item_id=<?=$this->id?>" method="POST" enctype="multipart/form-data">
            <?php
            $titre_gauche = '';
            if ($currentClassName::SQL_TABLE_NAME == 'equipements_bio_medicaux_types' && !isset($_GET['only_cat'])) {
                $titre_gauche = $this->nom.' - '.$this->description;
                if (dm_strlen($titre_gauche) > 80) {
                    $titre_gauche = dm_substr($titre_gauche, 0, 80).'...';
                }
                $titre = txt('Document(s) et image');
            }
            ?>
            <label style="width: 98%!important;"><span style="float: left; font-size: 16px; font-weight: 700;"><?=$titre_gauche?></span><?=$titre?></label>
            <? erreurs(); ?>
            <? messages(); ?>
            <br />
            <div style="margin-top: 22px; width: 48%; float: left;">
                <div style="font-weight: bold; font-size: 16px; margin-bottom: 7px;">
                    <?=txt('Document(s) lié(s):')?>
                </div>
                <?php
                if(havePermission('can_edit_documents') || isMaster())
                {
                    ?>
                    <div>
                        <input type="file" name="file" id="file" />
                        <button class="submit" value="submitbuttonvalue" name="upload" id="upload" style="display: none;"><?=txt('Télécharger')?></button>
                    </div>
                    <?php
                }

                $is_public = 1;
                if(havePermission('can_edit_documents') || isMaster())
                {
                    $is_public = 'all';
                }
                $documents = DocumentsListesElements::getListe(array('is_public' => $is_public, 'nom_table' => $currentClassName::SQL_TABLE_NAME, 'liste_item_id' => $this->id));
                if(count($documents) > 0)
                {
                    ?>
                    <table style="margin-top: 22px;">
                        <tr>
                            <td style="text-align: center!important; width: 22px;">&nbsp;</td>
                            <td style="text-align: center!important; width: 75px; font-weight: 700;"><?=txt('Visible')?></td>
                            <td style="text-align: left; font-weight: 700;"><?=txt('Nom')?></td>
                        </tr>
                        <?php

                        foreach($documents as $document)
                        {
                            ?>
                            <tr><td style="text-align: center!important;">
                                    <?php
                                    if(havePermission('can_edit_documents') || isMaster())
                                    {
                                        ?>
                                        <a href="index.php?action=delete_document&id=<?=$document->id?>" style="position: relative; top: 2px;" onclick="return confirm('<?=txt('Êtes-vous certain de vouloir supprimer ce document?')?>')"><img src="css/images/icons/dark/trashcan.png" /></a>
                                        <?php
                                    }
                                    ?>
                                </td><td style="text-align: center!important;">
                                    <?php
                                    if(havePermission('can_edit_documents') || isMaster())
                                    {
                                        $chk = $document->is_public == 1 ? ' checked="checked"' : '';
                                        ?>
                                        <input type="checkbox" name="is_public" class="is_public" data-document_id="<?=$document->id?>" value="1"<?=$chk?> />
                                        <?php
                                    }
                                    ?>
                                </td><td>
                                    <a href="index.php?action=download_document&id=<?=$document->id?>" target="_blank"><?=$document->nom?></a>
                                </td></tr>
                            </tr>
                            <?php
                        }
                        if(isMaster())
                        {
                            ?>
                            <tr><td colspan="3" style="padding-top: 100px!important;">
                                    <div style="border: solid 1px red!important; padding: 5px; width: 166px;">
                                        <a href="index.php?action=delete_document_all&nom_table=<?=$currentClassName::SQL_TABLE_NAME?>&liste_item_id=<?=$this->id?>&is_image_principale=1" onclick="return confirm('<?=txt('Êtes-vous certain de vouloir supprimer tous les documents de la liste?')?>')">
                                            <img src="css/images/icons/dark/trashcan.png" /><span style="position: relative; top: -7px; font-weight: bold!important;"><?=txt('Effacer tous les documents')?></span></a><br />
                                        <a href="index.php?action=delete_document_all&nom_table=<?=$currentClassName::SQL_TABLE_NAME?>&liste_item_id=<?=$this->id?>&is_image_principale=0" onclick="return confirm('<?=txt('Êtes-vous certain de vouloir supprimer toutes les images de la liste?')?>')">
                                            <img src="css/images/icons/dark/trashcan.png" /><span style="position: relative; top: -7px; font-weight: bold!important;"><?=txt('Effacer toutes les images')?></span></a>
                                    </div>
                                </td></tr>
                            <?php
                        }
                        ?>
                    </table>
                    <?php
                }
                ?>
            </div>

            <div style="margin-top: 22px; width: 48%; float: left; vertical-align: top!important;">
                <div style="font-weight: bold; font-size: 16px; margin-bottom: 7px;">
                    <?=txt('Image:')?>
                </div>
                <?php
                if(havePermission('can_edit_documents') || isMaster())
                {
                    ?>
                    <div>
                        <input type="file" name="image" id="image" />
                        <button class="submit" value="submitbuttonvalue" name="upload_image" id="upload_image" style="display: none;"><?=txt('Télécharger')?></button>
                    </div>
                    <?php
                }

                ?>
                <table style="margin-top: 22px;"><tr><td style="vertical-align: top!important; width: 30px;">
                            <?php
                            if((havePermission('can_edit_documents') || isMaster()) && isset($image))
                            {
                                ?>
                                <a href="index.php?action=delete_document&id=<?=$image->id?>" onclick="return confirm('<?=txt('Êtes-vous certain de vouloir supprimer cette image?')?>')"><img src="css/images/icons/dark/trashcan.png" /></a>
                                <?php
                            }
                            ?>
                        </td><td style="vertical-align: top!important;">
                            <img src="<?=$url_image?>" id="thumb_preview" height="200" style="<?=(isset($image) ? '' : 'display: none;')?>">
                        </td></tr></table>
            </div>
        </form>
        <?php
    }

    /**
     * @param int $iteration
     * @param bool $only_champs_denormalises
     */
    public function getEditorMobileHTML($iteration = 0, $only_champs_denormalises = false)
    {
        global $is_filtre_contexte, $CodeClient;

        /** @var self $currentClassName */
        $currentClassName = get_called_class();
        $metas_tmp = $currentClassName::getChampsSaisieMetaData();
        $champs_readonly = $currentClassName::getChampsReadOnly();
        $champs_hidden = $currentClassName::getChampsHidden();

        $usager = getUsager();
        $disciplines_auth = $usager->getDisciplinesAutorisees();

        $metas = array();
        if(!isset($metas_tmp['regroupements']))
        {
            $metas = array('regroupements' => array(1 => array('data' => $metas_tmp)));
        }
        else if(in_array($currentClassName, array('RaccordsElectriquesCoordinations', 'RaccordsElectriquesCoordinationsAuxiliaires')))
        {
            if($only_champs_denormalises)
            {
                $id_to_use = -1;
                foreach($metas_tmp['regroupements'] as $id => $datas)
                {
                    foreach($datas['data'] as $nom_champ => $data)
                    {
                        if(dm_substr($nom_champ, 0, 13) == 'champ_combine')
                        {
                            $id_to_use = $id;
                            break;
                        }
                    }
                }

                if(isset($metas_tmp['regroupements'][$id_to_use]))
                {
                    $metas = array('regroupements' => array(1 => array('data' => $metas_tmp['regroupements'][$id_to_use]['data'])));
                }
                else
                {
                    $metas = array();
                }
            }
            else
            {
                $metas = array('regroupements' => array(1 => array('data' => $metas_tmp['regroupements'][4]['data'])));
            }
        }
        else
        {
            $metas = $metas_tmp;
            ?>
            <style>
                fieldset {
                    padding: 5px;
                }
                input[type=text], select, textarea {
                    min-width: 250px;
                    max-width: 250px;
                    width: 250px;
                }
                fieldset.cotacote section > div {
                    margin: 0 0 10px 0!important;
                    padding: 0 0 0 0!important;
                }
                fieldset.cotacote label {
                    width: 200px !important;
                    margin: 0 0 5px 0!important;
                    padding: 0 0 0 0!important;
                }
                fieldset.cotacote textarea {
                    height: 50px !important;
                    min-height: 50px !important;
                }
                section {
                    margin: 0 0 0 0!important;
                    padding: 0 0 0 0!important;
                }
            </style>
            <?php
        }
        foreach($metas['regroupements'] as $id => $regroupement)
        {
            global $buffer;

            if((isset($regroupement['titre']) && $regroupement['titre'] == 'Coordination' && $CodeClient <> 'sqi') || $id <> 1)
            {
                continue;
            }

            if(isset($regroupement['titre']) && !$only_champs_denormalises)
            {
                ?>
                <fieldset class="noMinHeight <?=($currentClassName == 'RaccordsElectriquesCoordinations' ? '' : 'cotacote')?>">
                <?php
            }

            if ($currentClassName == 'GenerauxEquipements') {
                $champs_editables = ['marque', 'modele', 'fiche_local', 'remarque', 'code_norme', 'barcode_nouveau', 'numero_serie', 'date_fabrication', 'condition_vetuste', 'type_installation', 'hauteur', 'largeur', 'profondeur'];
                $regroupement['data']['fiche_local'] = [
                    'classe'      	=> 'Fiches',
                    'table'      	=> 'fiches',
                    'champ_id'      => 'fiche_local_id',
                    'type'          => 'select',
                    'format'        => 'int',
                    'titre'         => txt('Local d\'appartenance', false),
                    'css_editeur'   => 'xxlarge',
                    'dependance_id' => 'fiche_sous_type_id',
                    'dependance_champ_liste' => 'fiche_sous_type_id',
                    'dependance_force_val' => 2000002,
                    'sql_for_nom'   => 'code'
                ];
            }
            foreach($regroupement['data'] as $key => $meta)
            {
                $is_hidden_field = false;
                if (!in_array($key, $champs_editables) || isset($champs_hidden[$key])) {
                    $is_hidden_field = true;
                }
                $is_reponse_coord = isset($meta['is_reponse_coord']) ? $meta['is_reponse_coord'] : 'yo';
                if(isset($meta['is_reponse_coord']) && $this->$is_reponse_coord == 0)
                {
                    continue;
                }

                $meta_champ_id = $meta['champ_id'];
                if($only_champs_denormalises && !isset($meta['champ_texte_denormalise_id']))
                {
                    continue;
                }

                if(isset($meta['hidden']))
                {
                    if(isset($meta['filtre']) && isset($is_filtre_contexte))
                    {
                        $is_hidden_field = false;
                    }
                    else
                    {
                        $is_hidden_field = true;
                    }
                }

                if($is_hidden_field)
                {
                    $val_tmp = isset($meta['default_value']) ? $meta['default_value'] : $this->$meta_champ_id;
                    ?>
                    <input type="hidden" id="<?=$meta['champ_id']?>" name="<?=$meta['champ_id']?>[<?=$iteration?>]" value="<?=($val_tmp)?>" />
                    <?php
                }
                else
                {
                    ?>
                    <section class="formulaire_general">
                        <?php
                        if(isset($meta['titre']) && !isset($meta['hide_titre']))
                        {
                            ?>
                            <label style="<?=((isset($meta['obligatoire']) && $meta['obligatoire']) || (isset($meta['recommande']) && $meta['recommande']) ? 'color: red;' : '')?>"><?=$meta['titre']?></label>
                            <?php
                        }
                        ?>
                        <div style="<?=(isset($meta['titre']) && !isset($meta['hide_titre']) ? '' : 'width: 100%!important;')?>">
                            <?php
                            if(isset($meta['discipline_can_edit']) && !isset($disciplines_auth[(int)($meta['discipline_can_edit'])]))
                            {
                                print self::getReadonlyToDisplay($meta, $key, $this);
                                ?>
                                <input type="hidden" id="<?=$meta['champ_id']?>" name="<?=$meta['champ_id']?>[<?=$iteration?>]" value="<?=($this->$meta_champ_id)?>" />
                                <?php
                            }
                            else if(isset($champs_readonly[$key]))
                            {
                                print self::getReadonlyToDisplay($meta, $key, $this);
                                ?>
                                <input type="hidden" id="<?=$meta['champ_id']?>" name="<?=$meta['champ_id']?>[<?=$iteration?>]" value="<?=($this->$meta_champ_id)?>" />
                                <?php
                            }
                            else if(isset($meta['source_externe']))
                            {
                                if(isset($this->fiche_id) && $this->fiche_id > 0)
                                {
                                    if($key == 'nomenclature')
                                    {
                                        $fiche = new Fiches($this->fiche_id);
                                        $fiche_type = new FicheType($fiche->fiche_type_id);
                                        $classe_generale = $fiche_type->formulaire_general;
                                    }
                                    $objet_externe = $classe_generale::getOne($this->fiche_id);
                                    print $objet_externe->$key;
                                }
                            }
                            else if($meta['type'] == 'select')
                            {
                                if(isset($_GET['dependance_champ_liste']) && $meta['champ_id'] == $_GET['dependance_champ_liste'])
                                {
                                    $nom_obj = '';
                                    if(isset($meta['liste']))
                                    {
                                        foreach($meta['liste'] as $i => $row)
                                        {
                                            if($row['id'] == $this->$meta_champ_id)
                                            {
                                                $nom_obj = $row['nom'];
                                            }
                                        }
                                    }
                                    else
                                    {
                                        $obj_liste = new $meta['classe']($this->$meta_champ_id);
                                        $nom_obj = $obj_liste->nom;
                                    }
                                    print $nom_obj.'<span style="color: red; margin-left: 20px;">('.txt('Valeur dans la fiche visé par cet import').')</span>';
                                    ?>
                                    <input type="hidden" id="<?=$meta['champ_id']?>" name="<?=$meta['champ_id']?>[<?=$iteration?>]" value="<?=($this->$meta_champ_id)?>" />
                                    <?php
                                }
                                else
                                {
                                    ?>
                                    <div style="display: inline-block; position: relative;">
                                        <select id="<?=$meta['champ_id']?>" name="<?=$meta['champ_id']?>[<?=$iteration?>]" style="<?=(isset($meta['unite']) || in_array($key, array('quantite')) ? 'min-width: 75px!important; text-align: right; padding-right: 3px;' : '')?>">
                                            <option value=""> </option>
                                            <?php
                                            if(isset($meta['liste']))
                                            {
                                                foreach($meta['liste'] as $i => $row)
                                                {
                                                    $sel = $row['id'] == $this->$meta_champ_id ? ' selected="selected"' : '';
                                                    ?>
                                                    <option value="<?=$row['id']?>"<?=$sel?>><?=$row['nom']?></option>
                                                    <?php
                                                }
                                            }
                                            else
                                            {
                                                $filtre = array('for_edition' => 1);
                                                if(isset($meta['dependance_id']))
                                                {
                                                    $meta_dependance_id = $meta['dependance_id'];
                                                    $filtre = array('no_filtre_global' => 1, $meta['dependance_id'] => (isset($meta['dependance_force_val']) ? $meta['dependance_force_val'] : (isset($this->$meta_dependance_id) ? $this->$meta_dependance_id : 0)));
                                                    if ($key == 'fiche_local' && $currentClassName == 'GenerauxEquipements') {
                                                        $filtre['fiche_statut_id_not'] = 2;
                                                    }
                                                    if(isset($buffer["liste_".$meta['classe']]))
                                                    {
                                                        unset($buffer["liste_".$meta['classe']]);
                                                    }
                                                }
                                                else if(isset($meta['sql_for_nom']))
                                                {
                                                    $filtre = array('sql_for_nom' => $meta['sql_for_nom']);
                                                }
                                                else if(isset($meta['from_fiche_id']))
                                                {
                                                    $filtre = array('fiche_id' => $this->fiche_id);
                                                }

                                                if(isset($meta['extra_filtre']))
                                                {
                                                    $filtre = array_merge($filtre, $meta['extra_filtre']);
                                                }

                                                $meta['classe']::getOptions($filtre, $this->$meta_champ_id);

                                            }
                                            ?>
                                        </select>
                                        <?php
                                        if(isset($meta['unite']))
                                        {
                                            print '&nbsp;'.$meta['unite'];
                                        }
                                        if(in_array($meta_champ_id, ['marque_id', 'modele_id']))
                                        {
                                            $url = '';
                                            if ($meta_champ_id == 'marque_id') {
                                                $url = 'javascript: openMarquePopup();';
                                            } else if ($meta_champ_id == 'modele_id') {
                                                $url = 'javascript: openModelePopup();';
                                            }
                                            print '<a href="' . $url . '" style="position: absolute; right: -25px; top: -1px;" class="fancy_small"><img src="css/images/icons/dark/plus.png" /></a>';
                                        }
                                        ?>
                                    </div>
                                    <?php
                                }
                            }
                            else if($meta['type'] == 'radio')
                            {
                                foreach($meta['liste'] as $i => $row)
                                {
                                    $chk = $row['id'] == $this->$meta_champ_id ? ' checked="checked"' : '';
                                    ?>
                                    <div>
                                        <input type="radio" name="<?=$meta['champ_id']?>" id="<?=$meta['champ_id']?>_<?=$row['id']?>"<?=$chk?> class="<?=$meta['champ_id']?>" value="<?=$row['id']?>" /><label for="<?=$meta['champ_id']?>_<?=$row['id']?>" style="width: 425px!important; position: relative; top: 3px;"><?=$row['nom']?></label>
                                    </div>
                                    <?php
                                }
                            }
                            else if($meta['type'] == 'checkbox')
                            {
                                if ($meta_champ_id == 'taxable' && $this->id == 0) {
                                    $this->$meta_champ_id = 1;
                                }
                                ?>
                                <input type="checkbox" name="<?=$meta['champ_id']?>[<?=$iteration?>]" value="1"<?=($this->$meta_champ_id == 1 ? ' checked="checked"' : '')?> style="position: relative; top: 0px;" />
                                <?php
                            }
                            else if($meta['type'] == 'textarea')
                            {
                                ?>
                                <textarea id="<?=$meta['champ_id']?>" name="<?=$meta['champ_id']?>[<?=$iteration?>]" style="min-height: 100px;"><?=(isset($this->$meta_champ_id) ? ($this->$meta_champ_id) : '')?></textarea>
                                <?php
                            }
                            else if($meta['type'] == 'input')
                            {
                                if(isset($meta['unite_prefixe']))
                                {
                                    print '&nbsp;'.$meta['unite_prefixe'];
                                }

                                $val = $this->$meta_champ_id;
                                if(isset($meta['number_format']))
                                {
                                    $val = dm_number_format((float)($this->$meta_champ_id), $meta['number_format'], '.', '');
                                }

                                if(isset($meta['default_value']))
                                {
                                    $val = $val == 0 || dm_strlen($val) == 0 ? $meta['default_value'] : $val;
                                }

                                $align = in_array($meta['format'], array('int', 'float')) ? 'right' : 'center';
                                $classFormat = '';
                                if ($meta['format'] == 'int') {
                                    $classFormat = 'justInteger';
                                    if (str_starts_with($meta['champ_id'], 'qte_')) {
                                        $classFormat = 'justQuantity';
                                    }
                                } else if ($meta['format'] == 'float') {
                                    $classFormat = 'justFloat';
                                } else if ($meta['format'] == 'date') {
                                    $classFormat = 'date hasDatepicker';
                                }
                                ?>
                                <input type="text" id="<?=$meta['champ_id']?>" name="<?=$meta['champ_id']?>[<?=$iteration?>]"
                                       value="<?=((isset($meta['no_trailing_zero']) ? dm_str_replace('.00', '', $val) : $val))?>"
                                       style="<?=(in_array($meta['format'], array('int', 'float', 'date')) || isset($meta['unite']) ? 'min-width: 75px!important; width: 75px!important; text-align: '.$align.';' : '')?>
                                       <?=(isset($meta['champ_texte_denormalise_id']) ? 'min-width: 325px!important; width: 325px!important;' : '')?>"
                                       class="<?= $classFormat ?>
                                        <?=(isset($meta['champ_texte_denormalise_id']) ? ' champ_combine' : '')?>"
                                    <?=(isset($meta['champ_texte_denormalise_id']) ? ' data-champ_texte_denormalise_id="'.$meta['champ_texte_denormalise_id'].'"' : '')?>/>
                                <?php
                                if(isset($meta['unite']))
                                {
                                    print '&nbsp;'.$meta['unite'];
                                }
                            }
                            ?>
                        </div>
                    </section>
                    <?php
                }
            }
            /*
                        $formulaires_listes = $currentClassName::getFormulairesListes();
                        if(count($formulaires_listes) > 0)
                        {
                            foreach($formulaires_listes as $formulaire_liste)
                            {
                                $valeurs = FormulairesListes::getValeursForElement($formulaire_liste->id, $this->id);
                                foreach($formulaire_liste->listes_parametres as $ordre => $parametre)
                                {
            ?>
                                    <section class="formulaire_general">

                                        <label style=""><?=$parametre['nom_parametre']?></label>
                                        <div>
                                            <input type="text" id="parametre_<?=$parametre['id']?>" name="formulaire_liste_parametre[<?=$parametre['id']?>]"
                                               value="<?=(isset($valeurs[$parametre['id']]) ? $valeurs[$parametre['id']]['valeur'] : '')?>" />
                                        </div>

                                    </section>
            <?php
                                }
                            }
                        }
             */
            if(isset($regroupement['titre']))
            {
                ?>
                <br style="clear: both;" />
                </fieldset>
                <?php
            }
        }
        ?>
        <br style="clear: both;" />
        <?php
    }

    /**
     * @param int $iteration
     * @param bool $only_champs_denormalises
     */
    public function getEditorHTML($iteration = 0, $only_champs_denormalises = false)
    {
        global $is_filtre_contexte, $CodeClient;

        /** @var self $currentClassName */
        $currentClassName = get_called_class();
        $metas_tmp = $currentClassName::getChampsSaisieMetaData();
        $champs_readonly = $currentClassName::getChampsReadOnly();
        $champs_hidden = $currentClassName::getChampsHidden();

        $usager = getUsager();
        $disciplines_auth = $usager->getDisciplinesAutorisees();

        $metas = array();
        if(!isset($metas_tmp['regroupements']))
        {
            $metas = array('regroupements' => array(1 => array('data' => $metas_tmp)));
        }
        else if(in_array($currentClassName, array('RaccordsElectriquesCoordinations', 'RaccordsElectriquesCoordinationsAuxiliaires')))
        {
            if($only_champs_denormalises)
            {
                $id_to_use = -1;
                foreach($metas_tmp['regroupements'] as $id => $datas)
                {
                    foreach($datas['data'] as $nom_champ => $data)
                    {
                        if(dm_substr($nom_champ, 0, 13) == 'champ_combine')
                        {
                            $id_to_use = $id;
                            break;
                        }
                    }
                }

                if(isset($metas_tmp['regroupements'][$id_to_use]))
                {
                    $metas = array('regroupements' => array(1 => array('data' => $metas_tmp['regroupements'][$id_to_use]['data'])));
                }
                else
                {
                    $metas = array();
                }
            }
            else
            {
                $metas = array('regroupements' => array(1 => array('data' => $metas_tmp['regroupements'][4]['data'])));
            }
        }
        else
        {
            $metas = $metas_tmp;
            ?>
            <style>
                fieldset.cotacote {
                    margin: 0 0 17px 30px;
                    width: 42%;
                    float: left;
                }
                fieldset.cotacote section > div {
                    width: 150px !important;
                }
                #editables fieldset.cotacote label {
                    width: 240px !important;
                }
                fieldset.cotacote input[type=text], fieldset.cotacote select {
                    min-width: 150px !important;
                    width: 150px !important;
                }
                fieldset.cotacote textarea {
                    height: 50px !important;
                    min-height: 50px !important;
                }
            </style>
            <?php
        }
        ?>
        <script>
            <?php
            $champs_textes_denormalises = array();
            $metas_tmp = $currentClassName::getFlatChampsSaisieMetaData();
            foreach($metas_tmp as $key => $meta_tmp)
            {
            if(isset($meta_tmp['champ_texte_denormalise_id']))
            {
            $row = new ChampsTextesDenormalises($meta_tmp['champ_texte_denormalise_id']);
            $formulaire = $row->formulaire;
            $form_meta = $formulaire::getFlatChampsSaisieMetaData();
            $vals = $row->getLignes($this->fiche_id);
            $champs_textes_denormalises[$key] = array('row' => $row, 'texte' => (isset($vals[0]['texte']) ? $vals[0]['texte'] : ''));
            ?>
			function maj_recommendation_<?=$key?>()
			{
				var url = 'index.php';
				$.get(url, {
					action: 'getRecommendationFromChampTexteDenormalise',
					champ_texte_denormalise_id: <?=$row->id?>
                    <?php
                    foreach($row->colonnes as $i => $colonne)
                    {
                        if(isset($form_meta[$colonne['nom_champ']]))
                        {
                            $champ_id = $form_meta[$colonne['nom_champ']]['champ_id'];
                            print "\n," . $champ_id . ": $('#" . $champ_id . "').val()";
                        }
                    }
                    ?>
				}, function(html){

					$('#recommendation_<?=$key?>').html(html.trim());

					var val = $('#<?=$key?>').val();
					if(html.trim() == val.trim())
					{
						$('#recommendation_<?=$key?>').css('color', 'green');
						$('#copie_recommendation_<?=$key?>').css('display', 'none');
					}
					else
					{
						$('#recommendation_<?=$key?>').css('color', 'red');
						$('#copie_recommendation_<?=$key?>').css('display', 'inline');
					}

				});
			}
            <?php
            }
            }
            ?>

			function maj_suggestion(obj)
			{
				var url = 'index.php?action=getRecommendationFromChampCombine';
				$.post(url, {
					champ_texte_denormalise_id: $(obj).data('champ_texte_denormalise_id'),
					texte: obj.value
				}, function(json_data){

					$.each(json_data, function( id_element, texte ) {

						$('#' + id_element).css('float', 'left');
						if($('#bloc_suggestion_' + id_element).find('div.suggestion_' + $(obj).data('champ_texte_denormalise_id')).length)
						{
							$('#bloc_suggestion_' + id_element).find('div.suggestion_' + $(obj).data('champ_texte_denormalise_id')).replaceWith('<div class="suggestion_' + $(obj).data('champ_texte_denormalise_id') + '" style="color: blue!important;">' + texte + '</div>');
						}
						else
						{
							$('#bloc_suggestion_' + id_element).find('span.suggestions').append('<div class="suggestion_' + $(obj).data('champ_texte_denormalise_id') + '" style="color: blue!important;">' + texte + '</div>');
						}

					});

				}, 'json');
			}

			$(document).ready(function(){

				$('input.champ_combine').keyup(function(){

					maj_suggestion(this);

				});

				$('input.champ_combine').each(function(){

					maj_suggestion(this);

				});
                <?php
                foreach($metas_tmp as $key => $meta_tmp)
                {
                if(isset($meta_tmp['champ_texte_denormalise_id']))
                {
                ?>
				$('#copie_recommendation_<?=$key?>').click(function(){

					var val = $('#recommendation_<?=$key?>').html();
					$('#<?=$key?>').val(val.trim());
					$('#recommendation_<?=$key?>').css('color', 'green');
					$('#copie_recommendation_<?=$key?>').css('display', 'none');

				});

				$('#<?=$key?>').keyup(function(){

					var val = $('#<?=$key?>').val();
					var html = $('#recommendation_<?=$key?>').html();
					if(html.trim() == val.trim() || html.trim() == '')
					{
						$('#recommendation_<?=$key?>').css('color', 'green');
						$('#copie_recommendation_<?=$key?>').css('display', 'none');
					}
					else
					{
						$('#recommendation_<?=$key?>').css('color', 'red');
						$('#copie_recommendation_<?=$key?>').css('display', 'inline');
					}

				});
                <?php
                foreach($row->colonnes as $i => $colonne)
                {
                $champ_id = $form_meta[$colonne['nom_champ']]['champ_id'];
                if(isset($form_meta[$colonne['nom_champ']]))
                {
                ?>
				$('#<?=$champ_id?>').change(function () {

					maj_recommendation_<?=$key?>();

				});
                <?php
                }
                }
                }
                }
                ?>
			});
            <?php
            ?>

        </script>
        <?php
        foreach($metas['regroupements'] as $id => $regroupement)
        {
            global $buffer;

            if(isset($regroupement['titre']) && $regroupement['titre'] == 'Coordination' && $CodeClient <> 'sqi')
            {
                continue;
            }

            if(isset($regroupement['titre']) && !$only_champs_denormalises)
            {
                ?>
                <fieldset class="noMinHeight <?=($currentClassName == 'RaccordsElectriquesCoordinations' || count($champs_textes_denormalises) > 0 ? '' : 'cotacote')?>" style="<?=(count($champs_textes_denormalises) > 0 ? 'margin: 0 0 17px 30px;' : '')?>">
                <h5 style="text-decoration: underline; margin: 15px 0;"><?=$regroupement['titre']?></h5>
                <?php
            }

            $champs_ignores = array();
            foreach($regroupement['data'] as $key => $meta)
            {
                $is_reponse_coord = isset($meta['is_reponse_coord']) ? $meta['is_reponse_coord'] : 'yo';
                if(isset($meta['is_reponse_coord']) && $this->$is_reponse_coord == 0)
                {
                    continue;
                }

                $meta_champ_id = $meta['champ_id'];
                if($only_champs_denormalises && !isset($meta['champ_texte_denormalise_id']))
                {
                    continue;
                }

                if(in_array($key, $champs_ignores))
                {
                    continue;
                }

                $is_hidden_field = false;
                if(isset($meta['hidden']) || isset($champs_hidden[$key]))
                {
                    if(isset($meta['filtre']) && isset($is_filtre_contexte))
                    {
                        $is_hidden_field = false;
                    }
                    else
                    {
                        $is_hidden_field = true;
                    }
                }

                if($is_hidden_field)
                {
                    $val_tmp = isset($meta['default_value']) ? $meta['default_value'] : $this->$meta_champ_id;
                    ?>
                    <input type="hidden" id="<?=$meta['champ_id']?>" name="<?=$meta['champ_id']?>[<?=$iteration?>]" value="<?=($val_tmp)?>" />
                    <?php
                }
                else
                {
                    ?>
                    <section class="formulaire_general <?=(isset($champs_textes_denormalises[$key]) ? "champ_denormalise" : '')?>">
                        <?php
                        if(isset($meta['titre']) && !isset($meta['hide_titre']))
                        {
                            ?>
                            <label style="<?=((isset($meta['obligatoire']) && $meta['obligatoire']) || (isset($meta['recommande']) && $meta['recommande']) ? 'color: red;' : '')?><?=(count($champs_textes_denormalises) > 0 && !in_array($currentClassName, array('RaccordsElectriquesCoordinations', 'RaccordsElectriquesCoordinationsAuxiliaires')) ? 'width: 240px !important;' : '')?>"><?=$meta['titre']?></label>
                            <?php
                        }
                        ?>
                        <div style="<?=(isset($meta['titre']) && !isset($meta['hide_titre']) ? '' : 'width: 100%!important;')?>">
                            <?php
                            if(isset($meta['readonly']) && $meta['readonly'])
                            {
                                print self::getReadonlyToDisplay($meta, $key, $this);
                                ?>
                                <input type="hidden" id="<?=$meta['champ_id']?>" name="<?=$meta['champ_id']?>[<?=$iteration?>]" value="<?=($this->$meta_champ_id)?>" />
                                <?php
                            }
                            else if(isset($meta['discipline_can_edit']) && !isset($disciplines_auth[(int)($meta['discipline_can_edit'])]))
                            {
                                print self::getReadonlyToDisplay($meta, $key, $this);
                                ?>
                                <input type="hidden" id="<?=$meta['champ_id']?>" name="<?=$meta['champ_id']?>[<?=$iteration?>]" value="<?=($this->$meta_champ_id)?>" />
                                <?php
                            }
                            else if(isset($champs_readonly[$key]))
                            {
                                print self::getReadonlyToDisplay($meta, $key, $this);
                                ?>
                                <input type="hidden" id="<?=$meta['champ_id']?>" name="<?=$meta['champ_id']?>[<?=$iteration?>]" value="<?=($this->$meta_champ_id)?>" />
                                <?php
                            }
                            else if(isset($meta['source_externe']))
                            {
                                if(isset($this->fiche_id) && $this->fiche_id > 0)
                                {
                                    if($key == 'nomenclature')
                                    {
                                        $fiche = new Fiches($this->fiche_id);
                                        $fiche_type = new FicheType($fiche->fiche_type_id);
                                        $classe_generale = $fiche_type->formulaire_general;
                                    }
                                    $objet_externe = $classe_generale::getOne($this->fiche_id);
                                    print $objet_externe->$key;
                                }
                            }
                            else if($meta['type'] == 'select')
                            {
                                if(isset($_GET['dependance_champ_liste']) && $meta['champ_id'] == $_GET['dependance_champ_liste'])
                                {
                                    $nom_obj = '';
                                    if(isset($meta['liste']))
                                    {
                                        foreach($meta['liste'] as $i => $row)
                                        {
                                            if($row['id'] == $this->$meta_champ_id)
                                            {
                                                $nom_obj = $row['nom'];
                                            }
                                        }
                                    }
                                    else
                                    {
                                        $obj_liste = new $meta['classe']($this->$meta_champ_id);
                                        $nom_obj = $obj_liste->nom;
                                    }
                                    print $nom_obj.'<span style="color: red; margin-left: 20px;">('.txt('Valeur dans la fiche visé par cet import').')</span>';
                                    ?>
                                    <input type="hidden" id="<?=$meta['champ_id']?>" name="<?=$meta['champ_id']?>[<?=$iteration?>]" value="<?=($this->$meta_champ_id)?>" />
                                    <?php
                                }
                                else
                                {
                                    ?>
                                    <select id="<?=$meta['champ_id']?>" name="<?=$meta['champ_id']?>[<?=$iteration?>]" style="<?=(isset($meta['unite']) || in_array($key, array('quantite')) ? 'min-width: 75px!important; text-align: right; padding-right: 3px;' : '')?>">
                                        <option value=""> </option>
                                        <?php
                                        if(isset($meta['liste']))
                                        {
                                            foreach($meta['liste'] as $i => $row)
                                            {
                                                $sel = $row['id'] == $this->$meta_champ_id ? ' selected="selected"' : '';
                                                ?>
                                                <option value="<?=$row['id']?>"<?=$sel?>><?=$row['nom']?></option>
                                                <?php
                                            }
                                        }
                                        else
                                        {
                                            $filtre = array('for_edition' => 1);
                                            if(isset($meta['dependance_id']))
                                            {
                                                $meta_dependance_id = $meta['dependance_id'];
                                                $filtre = array('no_filtre_global' => 1, $meta['dependance_id'] => (isset($meta['dependance_force_val']) ? $meta['dependance_force_val'] : (isset($this->$meta_dependance_id) ? $this->$meta_dependance_id : 0)));
                                                if(isset($buffer["liste_".$meta['classe']]))
                                                {
                                                    unset($buffer["liste_".$meta['classe']]);
                                                }
                                            }
                                            else if(isset($meta['sql_for_nom']))
                                            {
                                                $filtre = array('sql_for_nom' => $meta['sql_for_nom']);
                                            }
                                            else if(isset($meta['from_fiche_id']))
                                            {
                                                $filtre = array('fiche_id' => $this->fiche_id);
                                            }

                                            if(isset($meta['extra_filtre']))
                                            {
                                                $filtre = array_merge($filtre, $meta['extra_filtre']);
                                            }
                                            ?>
                                            <?=$meta['classe']::getOptions($filtre, $this->$meta_champ_id)?>
                                            <?php
                                        }
                                        ?>
                                    </select>
                                    <?php
                                    if(isset($meta['unite']))
                                    {
                                        print '&nbsp;'.$meta['unite'];
                                    }
                                }
                            }
                            else if($meta['type'] == 'radio')
                            {
                                foreach($meta['liste'] as $i => $row)
                                {
                                    $chk = $row['id'] == $this->$meta_champ_id ? ' checked="checked"' : '';
                                    ?>
                                    <div>
                                        <input type="radio" name="<?=$meta['champ_id']?>" id="<?=$meta['champ_id']?>_<?=$row['id']?>"<?=$chk?> class="<?=$meta['champ_id']?>" value="<?=$row['id']?>" /><label for="<?=$meta['champ_id']?>_<?=$row['id']?>" style="width: 425px!important; position: relative; top: 3px;"><?=$row['nom']?></label>
                                    </div>
                                    <?php
                                }
                            }
                            else if($meta['type'] == 'checkbox')
                            {
                                if ($meta_champ_id == 'taxable' && $this->id == 0) {
                                    $this->$meta_champ_id = 1;
                                }
                                ?>
                                <input type="checkbox" name="<?=$meta['champ_id']?>[<?=$iteration?>]" value="1"<?=($this->$meta_champ_id == 1 ? ' checked="checked"' : '')?> style="position: relative; top: 0px;" />
                                <?php
                            }
                            else if($meta['type'] == 'textarea')
                            {
                                ?>
                                <textarea id="<?=$meta['champ_id']?>" name="<?=$meta['champ_id']?>[<?=$iteration?>]" style="min-height: 100px;"><?=(isset($this->$meta_champ_id) ? ($this->$meta_champ_id) : '')?></textarea>
                                <?php
                            }
                            else if($meta['type'] == 'input')
                            {
                                if(isset($meta['unite_prefixe']))
                                {
                                    print '&nbsp;'.$meta['unite_prefixe'];
                                }

                                $val = $this->$meta_champ_id;
                                if(isset($meta['number_format']))
                                {
                                    $val = dm_number_format((float)($this->$meta_champ_id), $meta['number_format'], '.', '');
                                }

                                if(isset($meta['default_value']))
                                {
                                    $val = $val == 0 || dm_strlen($val) == 0 ? $meta['default_value'] : $val;
                                }

                                $align = in_array($meta['format'], array('int', 'float')) ? 'right' : 'center';
                                $classFormat = '';
                                if ($meta['format'] == 'int') {
                                    $classFormat = 'justInteger';
                                    if (str_starts_with($meta['champ_id'], 'qte_')) {
                                        $classFormat = 'justQuantity';
                                    }
                                } else if ($meta['format'] == 'float') {
                                    $classFormat = 'justFloat';
                                } else if ($meta['format'] == 'date') {
                                    $classFormat = 'date hasDatepicker';
                                }
                                ?>
                                <input type="text" id="<?=$meta['champ_id']?>" name="<?=$meta['champ_id']?>[<?=$iteration?>]"
                                       value="<?=((isset($meta['no_trailing_zero']) ? dm_str_replace('.00', '', $val) : $val))?>"
                                       style="<?=(in_array($meta['format'], array('int', 'float', 'date')) || isset($meta['unite']) ? 'min-width: 75px!important; width: 75px!important; text-align: '.$align.';' : '')?>
                                       <?=(isset($meta['champ_texte_denormalise_id']) ? 'min-width: 325px!important; width: 325px!important;' : '')?>"
                                       class="<?= $classFormat ?>
                                        <?=(isset($meta['champ_texte_denormalise_id']) ? ' champ_combine' : '')?>"
                                    <?=(isset($meta['champ_texte_denormalise_id']) ? ' data-champ_texte_denormalise_id="'.$meta['champ_texte_denormalise_id'].'"' : '')?>/>
                                <?php
                                if(isset($meta['unite']))
                                {
                                    print '&nbsp;'.$meta['unite'];
                                }
                            }

                            if(!isset($champs_readonly[$key]))
                            {
                                if(isset($champs_textes_denormalises[$key]))
                                {
                                    $row = $champs_textes_denormalises[$key]['row'];
                                    $texte = $champs_textes_denormalises[$key]['texte'];
                                    $color = isset($val) && $texte == $val ? 'green' : 'red';
                                    $display_lien_copie = (isset($val) && $texte == $val) || dm_strlen(dm_trim($texte)) == 0 ? 'none' : 'inline';
                                    ?>
                                    <div style="display: inline-block; margin-left: 10px; ">

                                        <a id="copie_recommendation_<?=$row->nom_champ?>" href="javascript: void(0);" title="<?=txt('Copier la valeur dans le champ.')?>" style="display: <?=$display_lien_copie?>;"><img src="css/images/icons/dark/arrow_left.png" height="16" style="position: relative; top: 3px;" /></a>

                                        <span id="recommendation_<?=$row->nom_champ?>" class="" style="color: <?=$color?>!important;">
    <?php
    print $texte;
    ?>
                                    </span>

                                    </div>
                                    <?php
                                }
                                else if(count($champs_textes_denormalises) > 0)
                                {
                                    ?>
                                    <div id="bloc_suggestion_<?=$meta['champ_id']?>" style="display: inline-block; margin-left: 10px; ">

                                        <a id="copie_suggestion_<?=$meta['champ_id']?>" data-champ_id="<?=$meta['champ_id']?>" href="javascript: void(0);" title="<?=txt('Copier la valeur dans le champ.')?>" style="display: none;"><img src="css/images/icons/dark/arrow_left.png" height="16" style="position: relative; top: 3px;" /></a>

                                        <span class="suggestions">
                                    </span>

                                    </div>
                                    <?php
                                }
                            }

                            if(isset($meta['champ_associe']))
                            {
                                $meta_assoc = $regroupement['data'][$meta['champ_associe']];
                                unset($regroupement['data'][$meta['champ_associe']]);
                                $champs_ignores[] = $meta['champ_associe'];
                                $meta_assoc_champ_id = $meta_assoc['champ_id'];
                                ?>
                                &nbsp;
                                <select id="<?=$meta_assoc['champ_id']?>" name="<?=$meta_assoc['champ_id']?>[<?=$iteration?>]" style="min-width: 75px!important;">
                                    <option value=""> </option>
                                    <?=$meta_assoc['classe']::getOptions(array(), $this->$meta_assoc_champ_id)?>
                                </select>
                                <?php
                            }

                            if(isset($meta['is_reponse_coord']) && dm_substr($meta['champ_id'], 0, 20) == 'conformite_du_besoin' && havePermission(array('repondant_qrt_bcf', 'demandeur_qrt_bcf')) && isset($disciplines_auth[(int)($meta['discipline_can_edit'])]))
                            {
                                print '<span class="messagerie" style="position: relative; top: 7px;" data-discipline_id="'.$meta['discipline_can_edit'].'">';
                                $qrts = Questions::getListe(array('fiche_id' => $this->fiche_id, 'classe_formulaire' => $currentClassName, 'discipline_id' => $meta['discipline_can_edit']));
                                if(count($qrts) > 0)
                                {
                                    $qrt = array_values($qrts)[0];
                                    ?>
                                    <a href="javascript: void(0);" class="start_qrt" data-qrt_id="<?=$qrt->id?>">
                                        <img src="css/images/icons/dark/speech_bubble.png" /><?=($qrt->nb_notifications_all > 0 ? '<span class="extra">'.$qrt->nb_notifications_all.'</span>' : '')?></a>
                                    <?php
                                }
                                else
                                {
                                    ?>
                                    <a href="index.php?section=admin&module=edit&table=questions&type=client&id=0&fiche_id=<?=$this->fiche_id?>&nom_table=&liste_item_id=&classe_formulaire=<?=$currentClassName?>&discipline_id=<?=$meta['discipline_can_edit']?>" class="fancy" data-small_fancy="true" style="opacity: 0.5;">
                                        <img src="css/images/icons/dark/speech_bubble.png" /></a>
                                    <?php
                                }
                                print '</span>';
                            }
                            ?>
                        </div>
                    </section>
                    <?php
                }
            }
            /*
                        $formulaires_listes = $currentClassName::getFormulairesListes();
                        if(count($formulaires_listes) > 0)
                        {
                            foreach($formulaires_listes as $formulaire_liste)
                            {
                                $valeurs = FormulairesListes::getValeursForElement($formulaire_liste->id, $this->id);
                                foreach($formulaire_liste->listes_parametres as $ordre => $parametre)
                                {
            ?>
                                    <section class="formulaire_general">

                                        <label style=""><?=$parametre['nom_parametre']?></label>
                                        <div>
                                            <input type="text" id="parametre_<?=$parametre['id']?>" name="formulaire_liste_parametre[<?=$parametre['id']?>]"
                                               value="<?=(isset($valeurs[$parametre['id']]) ? $valeurs[$parametre['id']]['valeur'] : '')?>" />
                                        </div>

                                    </section>
            <?php
                                }
                            }
                        }
             */
            if(isset($regroupement['titre']))
            {
                ?>
                <br style="clear: both;" />
                </fieldset>
                <?php
            }
        }
        ?>
        <br style="clear: both;" />
        <?php
    }

    /**
     * @param null $currentClassName_form
     * @return array
     */
    public static function getFormulairesListes($currentClassName_form = null)
    {
        global $CacheFormulaireListe;

        /** @var self $currentClassName */
        $currentClassName = get_called_class();
        $table_name = $currentClassName::SQL_TABLE_NAME;

        if(isset($CacheFormulaireListe[$table_name]))
        {
            $formulaires_listes = $CacheFormulaireListe[$table_name];
        }
        else
        {
            $formulaires_listes = FormulairesListes::getListe(array('projet_actuel' => 1, 'nom_table' => $table_name));
            if (count($formulaires_listes) == 0) {
                $formulaires_listes = FormulairesListes::getListe(array('projet_multi' => 1, 'nom_table' => $table_name));
            }
        }

        return $formulaires_listes;
    }

    /**
     * @param $fiche_id
     * @param $obj
     */
    public static function getAutoFicheHtml($fiche_id, $obj)
    {
        global $modele_impression;

        /** @var self $currentClassName */
        $currentClassName = get_called_class();
        $metas_tmp = $currentClassName::getChampsSaisieMetaData();
        $champs_hidden = $currentClassName::getChampsHidden();

        $champs_obligatoires = null;
        if (isset($modele_impression) && isset($modele_impression->formulaires[(string)$currentClassName]) && isset($modele_impression->formulaires[(string)$currentClassName]['champs'])) {
            $champs_obligatoires = array_keys($modele_impression->formulaires[(string)$currentClassName]['champs']);
        }

        $margin_left = 0;
        $brClearBoth = false;
        if(!isset($metas_tmp['regroupements']))
        {
            $metas = array('regroupements' => array(1 => array('data' => $metas_tmp)));
        }
        else
        {
            $metas = $metas_tmp;
            $margin_left = 25;
            $brClearBoth = true;
        }
        ?>
        <div class="blocsFiche">
            <?php

            foreach($metas['regroupements'] as $id => $regroupement)
            {
                $champs_ignores = array();
                $affiche_titre = false;
                foreach($regroupement['data'] as $key => $meta)
                {
                    if(in_array($key, $champs_ignores))
                    {
                        continue;
                    }

                    if(self::getReadonlyToDisplay($meta, $key, $obj, isset($champs_obligatoires)) <> '&nbsp;' && (!isset($champs_obligatoires) || (in_array($key, $champs_obligatoires))))
                    {
                        $affiche_titre = true;
                    }

                    if(isset($meta['champ_associe']))
                    {
                        unset($regroupement['data'][$meta['champ_associe']]);
                        $champs_ignores[] = $meta['champ_associe'];
                    }
                }

                if(isset($regroupement['titre']) && $regroupement['titre'] == 'Coordination')
                {
                    continue;
                }

                if(isset($regroupement['titre']) && $affiche_titre)
                {
                    ?>
                    <fieldset class="noMinHeight <?=($currentClassName == 'RaccordsElectriquesCoordinations' ? '' : 'cotacote')?>">
                    <h5 style="text-decoration: underline; margin: 15px 0;"><?=$regroupement['titre']?></h5>
                    <?php
                }

                foreach($regroupement['data'] as $key => $meta)
                {
                    $meta_champ_id = $meta['champ_id'];
                    if(in_array($key, $champs_ignores) || isset($meta['hidden_from_fiche']))
                    {
                        continue;
                    }
                    if(isset($meta['source_externe']))
                    {
                        if($fiche_id > 0)
                        {
                            if($key == 'nomenclature')
                            {
                                $fiche = new Fiches($fiche_id);
                                $fiche_type = new FicheType($fiche->fiche_type_id);
                                $classe_generale = $fiche_type->formulaire_general;
                            }
                            $objet_externe = $classe_generale::getOne($fiche_id);
                            ?>
                            <section class="formulaire_general" style="margin-left: <?=$margin_left.'px'?>;">
                                <label><?=$meta['titre']?></label>
                                <div style="<?=(isset($meta['titre']) ? '' : 'margin: 0px 0px 0px 0px!important;')?>">
                                    <?=$objet_externe->$key?>
                                </div>
                            </section>
                            <?php
                        }
                    }
                    else if(!isset($champs_hidden[$key]) && (((isset($obj->$key) || isset($obj->$meta_champ_id)) && !isset($champs_obligatoires)) || (isset($champs_obligatoires) && in_array($key, $champs_obligatoires))) && !isset($meta['hidden']))
                    {
                        ?>
                        <section class="formulaire_general" style="margin-left: <?=$margin_left.'px'?>;">
                            <?php
                            if(isset($meta['titre']))
                            {
                                ?>
                                <label><?=$meta['titre']?></label>
                                <?php
                            }
                            ?>
                            <div style="<?=(isset($meta['titre']) ? '' : 'margin: 0px 0px 0px 0px!important;')?>">
                                <?php
                                print self::getReadonlyToDisplay($meta, $key, $obj, isset($champs_obligatoires));
                                ?>
                            </div>
                        </section>
                        <?php
                    }
                }
                ?>
                </fieldset>
                <?php
            }
            ?>
        </div>
        <?php
        if ($brClearBoth) {
            ?>
            <br style="clear: both" />
            <br style="clear: both" />
            <?php
        }
    }

    /**
     * @param $meta
     * @param $key
     * @param $obj
     * @param bool $tirets
     * @return string
     */
    public static function getReadonlyToDisplay($meta, $key, $obj, $tirets = false)
    {
        $meta_champ_id = $meta['champ_id'];
        $to_display = '';
        if($meta['type'] == 'checkbox')
        {
            $to_display .= (isset($obj->$key) && $obj->$key == 1 ? txt('Oui') : txt('Non'));
        }
        else if(isset($meta['liste']))
        {
            foreach($meta['liste'] as $i => $row)
            {
                if($row['id'] == $obj->$meta_champ_id)
                {
                    $to_display .= $row['nom'];
                }
            }
        }
        else
        {
            if(isset($meta['classe']) && $meta['classe'] == 'Fiches')
            {
                if(isset($obj->$key))
                {
                    $to_display .= '<a href="javascript: void(0);" class="liens_internes" fiche_id="'.$obj->$meta_champ_id.'">';
                    $to_display .= $obj->$key;
                    $to_display .= '</a>';
                }
            }
            else
            {
                $to_display .= (isset($meta['unite_prefixe']) ? $meta['unite_prefixe'].'' : '').(isset($obj->$key) ? (isset($meta['no_trailing_zero']) ? dm_str_replace('.00', '', $obj->$key) : dm_nl2br($obj->$key)) : ($tirets ? '---' : '&nbsp;')).(isset($meta['unite']) ? '&nbsp;'.$meta['unite'] : '');
            }
        }

        if(isset($meta['extra_select']))
        {
            $class_extra = $meta['extra_select']['classe'];
            if(isset($obj->$meta_champ_id))
            {
                $meta_extra_select = $meta['extra_select']['champ_id'];
                $obj_extra = new $class_extra($obj->$meta_extra_select);
                $to_display .= '&nbsp;'.$obj_extra->nom;
            }
        }

        if((dm_strlen($to_display) == 0 || $to_display == '&nbsp;') && $tirets)
        {
            $to_display = '---';
        }

        if(isset($meta['champ_associe']))
        {
            $meta_champ_associe = $meta['champ_associe'];
            $to_display .= '&nbsp;'.$obj->$meta_champ_associe;
        }

        return $to_display;
    }

    /**
     * @param $meta
     * @param $iteration
     * @param $obj
     */
    public static function getEditableToDisplay($meta, $iteration, $obj)
    {
        /** @var self $currentClassName */
        $currentClassName = get_called_class();
        $class_extra = '';
        if($currentClassName::$TYPE_FORM == 'LISTE_COORDINATION' || $currentClassName::CAN_HAVE_COORDINATION)
        {
            if(isset($meta['is_reponse_coord']))
            {
                $class_extra = 'is_reponse_coord '.$meta['is_reponse_coord'];
                if(in_array($meta['champ_id'], array('conformite_du_besoin_electricite_id', 'conformite_du_besoin_plomberie_id', 'conformite_du_besoin_mecanique_id', 'conformite_du_besoin_telecom_id', 'conformite_du_besoin_architecture_id', 'conformite_du_besoin_structure_id')))
                {
                    $class_extra .= ' is_reponse_coord_solution';
                }
            }
            else
            {
                $class_extra = 'is_besoin_coord';
                if(in_array($meta['champ_id'], array('equipement_bio_medicaux_type_id', 'accessoire_architecturaux_type_id')))
                {
                    $class_extra .= ' is_wipe_all';
                }
            }
        }

        $meta_champ_id = $meta['champ_id'];
        if(isset($meta['hidden']))
        {
            $val_tmp = isset($meta['default_value']) ? $meta['default_value'] : $obj->$meta_champ_id;
            ?>
            <input type="hidden" name="<?=$meta['champ_id']?>[<?=$iteration?>]" class="<?=$meta['champ_id']?> <?=$class_extra?>" value="<?=($val_tmp)?>" />
            <?php
        }
        else if($meta['type'] == 'select')
        {
            ?>
            <select name="<?=$meta['champ_id']?>[<?=$iteration?>]" class="<?=($meta['champ_id'] == 'equipement_bio_medicaux_type_id' && $currentClassName == 'EquipementsBioMedicauxNoCoord' ? 'to_populate_select2' : 'to_populate')?> <?=$meta['champ_id']?> <?=$class_extra?> <?=(isset($meta['check_if_coord']) ? 'check_if_coord' : '')?>" data-classname="<?=$meta['classe']?>">
                <?php
                if (in_array($meta['classe'], ['Quantites'])) {
                    ?>
                    <option value="999999">0</option>
                    <?php
                } else {
                    ?>
                    <option value=""> </option>
                    <?php
                }
                $filtre = array('just_selected' => 1);
                $meta['classe']::getOptions($filtre, $obj->$meta_champ_id);
                ?>
            </select>
            <?php
        }
        else if($meta['type'] == 'input')
        {
            if(isset($meta['unite_prefixe']))
            {
                print '<span style="position: absolute; right: 6px; top: 6px;">&nbsp;'.$meta['unite_prefixe'].'</span>';
            }

            $val = $obj->$meta_champ_id;
            if(isset($meta['number_format']))
            {
                $val = dm_number_format((float)($obj->$meta_champ_id), $meta['number_format'], '.', '');
            }
            $classFormat = '';
            if ($meta['format'] == 'int') {
                $classFormat = 'justInteger';
                if (str_starts_with($meta['champ_id'], 'qte_')) {
                    $classFormat = 'justQuantity';
                }
            } else if ($meta['format'] == 'float') {
                $classFormat = 'justFloat';
            } else if ($meta['format'] == 'date') {
                $classFormat = 'date hasDatepicker';
            }
            ?>
            <input type="text" data-class_old="<?=$meta['champ_id']?>_old" name="<?=$meta['champ_id']?>[<?=$iteration?>]" value="<?=($val)?>" style="<?=($currentClassName::CAN_HAVE_COORDINATION && $meta['champ_id'] == 'commentaires' ? 'width: 790px;' : '')?><?=(in_array($meta['format'], array('int', 'float')) ? 'max-width: 75px!important; text-align: right;' : '')?><?=(isset($meta['unite']) && in_array($meta['format'], array('int', 'float')) ? 'padding-right: 23px;' : '')?>" class="<?= $classFormat ?> <?=$class_extra?> <?=$meta['champ_id']?><?=($meta['format'] == 'date' ? 'date hasDatepicker' : '')?>" />
            <?php
            ?>
            <input type="hidden" name="<?=$meta['champ_id']?>_old[<?=$iteration?>]" class="<?=$meta['champ_id']?>_old" value="<?=($val)?>" />
            <?php
            if(isset($meta['unite']))
            {
                print '<span style="position: absolute; right: 6px; top: 6px;">&nbsp;'.$meta['unite'].'</span>';
            }
        }
        else if($meta['type'] == 'checkbox')
        {
            if (!isset($obj->$meta_champ_id) || strlen($obj->$meta_champ_id) == 0) {
                $obj->$meta_champ_id = isset($meta['default_value']) ? $meta['default_value'] : 0;
            }
            $val = $obj->$meta_champ_id;
            ?>
            <input type="checkbox" name="<?=$meta['champ_id']?>[<?=$iteration?>]" value="1"<?=($val == 1 ? ' checked="checked"' : '')?> class="<?=$meta['champ_id']?> <?=$class_extra?>" <?= (isset($meta['default_value']) ? ' data-default_value="'.$meta['default_value'].'"' : '') ?> />
            <?php
        } else if ($meta['type'] == 'equipements_lies') {
            ?>
            <div id="equipements_lies-<?= $iteration ?>" class="equipements_lies" style="display: inline-block; max-width: 400px;" data-iteration="<?= $iteration ?>">
                <?php
                if (isset($obj->equipements_lies) && is_array($obj->equipements_lies)) {
                    foreach ($obj->equipements_lies as $iii => $equipement_lie) {
                        ?>
                        <span class="equi-<?= $equipement_lie['equipement_bio_medicaux_id'] ?> equi" style="<?= ($equipement_lie['is_qte_equipement_requis_ok'] ? '' : 'color: red;') ?>">
                            <input type="hidden" class="equipement_bio_medicaux_id" name="equipement_bio_medicaux_id[<?=$iteration?>][]" value="<?= $equipement_lie['equipement_bio_medicaux_id'] ?>" />
                            <input type="hidden" class="qte_besoin_equipement" name="qte_besoin_equipement[<?=$iteration?>][]" value="<?= $equipement_lie['qte_besoin_equipement'] ?>" />
                            <input type="hidden" class="qte_equipement_requis_now" name="qte_equipement_requis_now[<?=$iteration?>][]" value="<?= $equipement_lie['qte_equipement_requis_now'] ?>" />
                            <?= $equipement_lie['code_equipement'] . ' (' . $equipement_lie['qte_besoin_equipement'] . ')' ?><?= (isset($obj->equipements_lies[$iii+1]) ? ', ' : '') ?>
                        </span>
                        <?php
                    }
                }
                ?>
            </div>
            <a class="lier" data-iteration="<?=$iteration?>" style="<?= ($obj->dedie == 1 ? '' : 'display: none;') ?>"><img src="css/images/icons/dark/link_2.png" style="float: right" /></a>
            <?php
        }
    }

    /**
     * @param $fiche_id
     * @param $obj
     * @param $last_obj
     * @param $token_archive
     * @param $last_token_archive
     */
    public static function getAutoDiffArchiveHtml($fiche_id, $obj, $last_obj, $token_archive, $last_token_archive)
    {
        /** @var self $currentClassName */
        $currentClassName = get_called_class();
        $metas_tmp = $currentClassName::getChampsSaisieMetaData();

        $metas = array();
        if(!isset($metas_tmp['regroupements']))
        {
            $metas = array('regroupements' => array(1 => array('data' => $metas_tmp)));
        }
        else
        {
            $metas = $metas_tmp;
        }
        ?>
        <div class="blocsFiche">
            <?php

            foreach($metas['regroupements'] as $id => $regroupement)
            {
                if(isset($regroupement['titre']))
                {
                    ?>
                    <h5 style="text-decoration: underline; margin: 15px 0;"><?=$regroupement['titre']?></h5>
                    <?php
                }
                foreach($regroupement['data'] as $key => $meta)
                {
                    $meta_champ_id = $meta['champ_id'];
                    //if(!isset($meta['hidden']))
                    {
                        ?>
                        <section class="formulaire_general">
                            <?php
                            if(!isset($meta['titre']) || dm_strlen($meta['titre']) == 0)
                            {
                                continue;
                            }
                            if(isset($meta['titre']))
                            {
                                ?>
                                <label><?=$meta['titre']?></label>
                                <?php
                            }
                            ?>
                            <div style="<?=(isset($meta['titre']) ? '' : 'margin: 0px 0px 0px 0px!important;')?>">
                                <?php
                                if($meta['type'] == 'checkbox')
                                {
                                    $curVal = (isset($obj->$key) && $obj->$key == 1 ? txt('Oui') : txt('Non'));

                                    if(isset($last_obj->$key) && $last_obj->$key == '')
                                    {
                                        $oldVal = '';
                                    }
                                    else if(isset($last_obj->$key) && in_array($last_obj->$key, array(0, 1)))
                                    {
                                        $oldVal = ($last_obj->$key == 1 ? txt('Oui') : txt('Non'));
                                    }
                                }
                                else if(isset($meta['liste']))
                                {
                                    $curVal = (isset($meta['liste'][$obj->$meta_champ_id]) ? $meta['liste'][$obj->$meta_champ_id]['nom'] : '');
                                    $oldVal = (isset($meta['liste'][$last_obj->$meta_champ_id]) ? $meta['liste'][$last_obj->$meta_champ_id]['nom'] : '');
                                }
                                else
                                {
                                    $curVal = (isset($obj->$key) ? $obj->$key : '');
                                    $oldVal = (isset($last_obj->$key) ? $last_obj->$key : '');
                                }
                                print diff($oldVal, $curVal);
                                ?>
                            </div>
                        </section>
                        <?php
                    }
                }
            }
            ?>
        </div>
        <?php
    }

    /**
     * @param $is_gabarit
     */
    public static function getFicheHTMLTitre($is_gabarit, $filtre = [])
    {
        /** @var self $currentClassName */
        $currentClassName = get_called_class();
        $metas = $currentClassName::getFlatChampsSaisieMetaData();
        $champs_hidden = $currentClassName::getChampsHidden();

        if($currentClassName::CAN_HAVE_COORDINATION)
        {
            ?>
            <tr>
                <?php
                $first_found = false;
                foreach ($metas as $key => $meta)
                {
                    if(in_array($key, array('equipement_electricite', 'equipement_plomberie')))// && !isset($meta['is_reponse_coord']))
                    {
                        if($key == 'equipement_plomberie')
                        {
                            ?>
                            <td>
                                &nbsp;
                            </td>
                            <?php
                        }
                        ?>
                        <td class="title" style="padding-bottom: 15px!important; border-bottom: solid 1px #AAA!important; text-align: center!important;" colspan="<?=($key == 'equipement_electricite' ? 3 : 2)?>">
                            <?=($key == 'equipement_electricite' ? txt('Électricité') : txt('Plomberie'))?>
                        </td>
                        <?php
                        $first_found = true;
                    }
                }
                if ($first_found) {
                    ?>
                    <td class="title">
                        &nbsp;
                    </td>
                    <?php
                }
                ?>
            </tr>
            <?php
        }
        ?>
        <tr>
            <?php
            foreach ($metas as $key => $meta)
            {
                if (isset($filtre['cols'])) {
                    if (!in_array($key, $filtre['cols'])) {
                        continue;
                    }
                }
                if(!isset($champs_hidden[$key]) && !isset($meta['hidden']) && !in_array($key, array('commentaires_electricite', 'commentaires_plomberie', 'commentaires_mecanique', 'commentaires_telecom', 'commentaires_architecture', 'commentaires_structure', 'conformite_du_besoin_electricite', 'conformite_du_besoin_plomberie', 'conformite_du_besoin_mecanique', 'conformite_du_besoin_telecom', 'conformite_du_besoin_architecture', 'conformite_du_besoin_structure', 'conformite_du_besoin')))// && !isset($meta['is_reponse_coord']))
                {
                    /*$extra_style = '';
                    if($key == 'commentaires_plomberie')
                    {
                        $extra_style = 'border-right: solid 1px #AAA!important;';
                    }
                    else if(in_array($key, array('equipement_electricite', 'equipement_plomberie')))
                    {
                        $extra_style = 'border-left: solid 1px #AAA!important;';
                    }*/
                    ?>
                    <td class="title" style="<?=($currentClassName::$TYPE_FORM == 'LISTE_COORDINATION' || $currentClassName::CAN_HAVE_COORDINATION ? 'padding-bottom: 15px!important; padding-top: 15px!important;' : '')?>">
                        <?=$meta['titre']?>
                    </td>
                    <?php
                    if($key == 'commentaires_electricite')
                    {
                        ?>
                        <td>
                            &nbsp;
                        </td>
                        <?php
                    }
                }
            }
            ?>
            <td class="title" style="<?=($currentClassName::$TYPE_FORM == 'LISTE_COORDINATION' || $currentClassName::CAN_HAVE_COORDINATION ? 'padding-bottom: 15px!important; padding-top: 15px!important;' : '')?>">
                <?=($is_gabarit ? txt('Gabarit') : '')?>
            </td>
        </tr>
        <?php
    }

    /**
     * @param $iteration
     * @param $gabarit
     */
    public function getFicheHTMLLigne($iteration, $gabarit = null, $filtre = [])
    {
        /** @var self $currentClassName */
        $currentClassName = get_called_class();
        $metas = $currentClassName::getFlatChampsSaisieMetaData();
        $champs_hidden = $currentClassName::getChampsHidden();

        $afficher_inactif = isset($this->ts_inactivation);
        $code_equipement = '';
        $formulaires_listes = [];
        $type_id = null;
        if (in_array($currentClassName, ['EquipementsBioMedicaux', 'EquipementsBioMedicauxNoCoord'])) {
            $codes = dm_explode(' - ', $this->equipement_bio_medicaux_type);
            $code_equipement_court = isset($codes[2]) ? $codes[1] : ($codes[0] ?? '');
            $code_equipement = $this->equipement_bio_medicaux_type;
            $formulaires_listes = EquipementsBioMedicauxTypes::getFormulairesListes($currentClassName);
            $type_id = $this->equipement_bio_medicaux_type_id;
        }
        ?>
        <tr class="rows" style="<?= ($afficher_inactif ? 'color: red; opacity: 0.75;' : '') ?>" id="equi-<?= $this->id ?>" data-id="<?= $this->id ?>"
            <?= (count($formulaires_listes) > 0 ? ' data-fiche_id="' . $this->fiche_id . '"' : '') ?>
            <?= (isset($type_id) ? ' data-type_id="' . $type_id . '"' : '') ?>
            <?= (count($formulaires_listes) > 0 ? ' data-formulaire_liste_id="' . $formulaires_listes[0]->id . '"' : '') ?>
            <?= (in_array($currentClassName, ['EquipementsBioMedicaux', 'EquipementsBioMedicauxNoCoord']) ? ' data-qte_equipement_requis="' . ((int)$this->qte_requise + (int)$this->qte_fantome) . '"' : '') ?>
            <?= (in_array($currentClassName, ['EquipementsBioMedicaux', 'EquipementsBioMedicauxNoCoord']) ? ' data-code_equipement="' . $code_equipement . '"' : '') ?>
            <?= (in_array($currentClassName, ['EquipementsBioMedicaux', 'EquipementsBioMedicauxNoCoord']) ? ' data-code_equipement_court="' . $code_equipement_court . '"' : '') ?>
        >
            <?php
            $nb_cols = 0;
            $disciplines_coords_champs = array();
            foreach ($metas as $key => $meta)
            {
                if (isset($filtre['cols'])) {
                    if (!in_array($key, $filtre['cols'])) {
                        continue;
                    }
                }
                $meta_champ_id = $meta['champ_id'];
                /*if(isset($meta['is_reponse_coord']))
                {
                    $disciplines_coords_champs[$meta['is_reponse_coord']][$key] = $meta;
                    continue;
                }*/

                if(!isset($champs_hidden[$key]) && !isset($meta['hidden']) && !in_array($key, array('commentaires_electricite', 'commentaires_plomberie', 'commentaires_mecanique', 'commentaires_telecom', 'commentaires_architecture', 'commentaires_structure', 'conformite_du_besoin_electricite', 'conformite_du_besoin_plomberie', 'conformite_du_besoin_mecanique', 'conformite_du_besoin_telecom', 'conformite_du_besoin_architecture', 'conformite_du_besoin_structure', 'conformite_du_besoin')))
                {
                    $val = '';
                    if(isset($meta['classe']) && $meta['classe'] == 'Fiches')
                    {
                        if(isset($this->$key))
                        {
                            $val = '<a href="javascript: void(0);" class="liens_internes" fiche_id="'.$this->$meta_champ_id.'">';
                            $val .= $this->$key;
                            $val .= '</a>';
                        }
                    }
                    else
                    {
                        $val = $this->$key;
                        if(isset($meta['number_format']))
                        {
                            $val = dm_number_format((float)($this->$key), $meta['number_format'], '.', '');
                        }
                    }

                    if ($meta['type'] == 'equipements_lies') {
                        ?>
                        <td class="<?= $key ?>_<?= $meta_champ_id ?>" style="<?= ($key == 'qte_restante' && $val <> 0 ? 'color: red; font-weight: bold;' : '') ?>">
                            <?php
                            if (isset($this->equipements_lies) && is_array($this->equipements_lies)) {
                                foreach ($this->equipements_lies as $iii => $equipement_lie) {
                                    ?>
                                    <span class="equi-<?= $equipement_lie['equipement_bio_medicaux_id'] ?> equi" style="<?= ($equipement_lie['is_qte_equipement_requis_ok'] ? '' : 'color: red;') ?>">
                                    <?= $equipement_lie['code_equipement'] . ' (' . $equipement_lie['qte_besoin_equipement'] . ')' ?><?= (isset($this->equipements_lies[$iii+1]) ? ', ' : '') ?>
                                </span>
                                    <?php
                                }
                            }
                            ?>
                        </td>
                        <?php
                    } else {
                        ?>
                        <td class="<?= $meta_champ_id ?>" style="<?=((isset($this->statut) && $this->statut == 2) ? 'color: blue!important;' : '')?><?= ($key == 'qte_restante' && $val <> 0 ? 'color: red; font-weight: bold;' : '') ?>">
                            <?php
                            if (count($formulaires_listes) > 0 && $meta_champ_id == 'equipement_bio_medicaux_type_id') {
                                ?>
                                <a href="index.php?section=admin&module=popup_listes_param&fiche_id=<?=$this->fiche_id?>&formulaire_liste_id=<?=$formulaires_listes[0]->id?>&liste_item_id=<?=$this->$meta_champ_id?>" class="fancy add" style="<?= ($afficher_inactif ? 'color: red;' : '') ?>">
                                    <?=$val?></a>
                                <?php
                            } else if ($meta['type'] == 'checkbox') {
                                if (dm_strlen($val) > 0) {
                                    print (int)$val == 1 ? txt('Oui') : txt('Non');
                                }
                            } else {
                                ?>
                                <?=$val.(isset($meta['unite']) ? ' '.$meta['unite'] : '')?>
                                <?php
                            }
                            ?>
                        </td>
                        <?php
                    }
                    if($key == 'commentaires_electricite')
                    {
                        ?>
                        <td>
                            &nbsp;
                        </td>
                        <?php
                    }
                    $nb_cols++;
                }
            }
            ?>
            <td style="<?=(isset($this->statut) && $this->statut == 2 ? 'color: blue!important;' : '')?>">
                <?php
                if(isset($gabarit) && isset($this->statut) && $this->statut == 2)
                {
                    ?>
                    <?=$gabarit->code?>
                    <?php
                }
                ?>
                &nbsp;
            </td>
        </tr>
        <?php
        $nb_cols++;
        if(count($disciplines_coords_champs) > 0 && false)
        {
            ?>
            <tr>
                <td style="position: relative;" colspan="<?=$nb_cols?>">
                    <div style="position: absolute; left: 0px; top: 0px; text-align: left; width: 985px;">
                        <?php
                        foreach($disciplines_coords_champs as $discipline => $coords_champs)
                        {
                        ?>
                        <div class="coords_champs_block <?=$discipline?>">
                            <?php
                            if(in_array($discipline, array('is_coordination_electricite', 'is_coordination_plomberie', 'is_coordination_mecanique', 'is_coordination_telecom', 'is_coordination_architecture', 'is_coordination_structure')))
                            {
                            if($discipline == 'is_coordination_electricite')
                            {
                                $titre_discipline = txt('Électricité:');
                            }
                            else if($discipline == 'is_coordination_plomberie')
                            {
                                $titre_discipline = txt('Plomberie:');
                            }
                            elseif($discipline == 'is_coordination_mecanique')
                            {
                                $titre_discipline = txt('Mécanique:');
                            }
                            elseif($discipline == 'is_coordination_telecom')
                            {
                                $titre_discipline = txt('Télécommunication:');
                            }
                            elseif($discipline == 'is_coordination_architecture')
                            {
                                $titre_discipline = txt('Architecture:');
                            }
                            elseif($discipline == 'is_coordination_structure')
                            {
                                $titre_discipline = txt('Structure:');
                            }
                            ?>
                            <div style="font-weight: bold; color: blue; margin-bottom: 10px;"><?=$titre_discipline?>&nbsp;&nbsp;&nbsp;&nbsp;
                                <?php
                                }

                                foreach($coords_champs as $key => $coord_champ)
                                {
                                    ?>
                                    <span style="display: inline-block;">
                                    <?php
                                    if(dm_substr($coord_champ['champ_id'], 0, 20) == 'conformite_du_besoin')
                                    {
                                        print '<span style="color: blue;">'.txt('L\'information ci-dessus permet d\'assurer la coordination des besoins').'</span>';
                                    }
                                    else
                                    {
                                        print $coord_champ['titre'].': ';
                                    }

                                    print '&nbsp;<b style="font-weight: bold;">'.self::getReadonlyToDisplay($coord_champ, $key, $this, true).'</b>';
                                    ?>
                        </span>
                                    <?php
                                    if(in_array($discipline, array('is_coordination_electricite', 'is_coordination_plomberie', 'is_coordination_mecanique', 'is_coordination_telecom', 'is_coordination_architecture', 'is_coordination_structure')) && dm_substr($coord_champ['champ_id'], 0, 20) == 'conformite_du_besoin')
                                    {
                                        print '</div>';
                                    }
                                }
                                ?>
                            </div>
                            <?php
                            }
                            ?>
                        </div>
                </td>
            </tr>
            <?php
        }
    }

    /**
     *
     */
    public static function getFormHTMLTitre()
    {
        /** @var self $currentClassName */
        $currentClassName = get_called_class();
        $metas = $currentClassName::getFlatChampsSaisieMetaData();
        $champs_hidden = $currentClassName::getChampsHidden();
        ?>
        <tr>

            <td class="title tiny" style="<?=($currentClassName::$TYPE_FORM == 'LISTE_COORDINATION' || $currentClassName::CAN_HAVE_COORDINATION ? 'padding-bottom: 15px!important;' : '')?>">
                &nbsp;
            </td>
            <?php
            foreach ($metas as $key => $meta)
            {
                if(!isset($champs_hidden[$key]) && !isset($meta['is_reponse_coord']))
                {
                    ?>
                    <td class="title <?=!($currentClassName::CAN_HAVE_COORDINATION && $key == 'commentaires' && $currentClassName <> 'EquipementsBioMedicaux') ? $meta['css_editeur'] : ''?>" style="<?=($currentClassName::$TYPE_FORM == 'LISTE_COORDINATION' || $currentClassName::CAN_HAVE_COORDINATION ? 'padding-bottom: 15px!important;' : '')?><?=(isset($meta['hidden']) ? 'display: none;' : '')?>">
                        <?=!($currentClassName::CAN_HAVE_COORDINATION && $key == 'commentaires' && $currentClassName <> 'EquipementsBioMedicaux') ? $meta['titre'] : ''?>
                    </td>
                    <?php
                }
            }
            ?>

        </tr>
        <?php
    }

    /**
     * @param $iteration
     * @param null $gabarit
     */
    public function getFormHTMLLigne($iteration, $gabarit = null, $isInvisible = false)
    {
        global $db;
        /** @var self $currentClassName */
        $currentClassName = get_called_class();
        $metas_tmp = $currentClassName::getChampsSaisieMetaData();
        $champs_readonly = $currentClassName::getChampsReadOnly();
        $champs_hidden = $currentClassName::getChampsHidden();
        $canAddDeleteMove = $currentClassName::canAddDeleteMove();

        $usager = getUsager();
        $disciplines_auth = $usager->getDisciplinesAutorisees();

        $metas = array();
        if(!isset($metas_tmp['regroupements']))
        {
            $metas = array('regroupements' => array(1 => array('data' => $metas_tmp)));
        }
        else
        {
            $metas = $metas_tmp;
        }

        $listeToInactivate = [];
        $disciplines_coords_champs = array();
        foreach($metas['regroupements'] as $id => $regroupement)
        {
            $id_ligne = $this->id_ligne ?? $this->id;
            $afficher_inactif = isset($this->ts_inactivation);
            ?>
            <tr style="<?=($isInvisible ? 'display: none;' : '') . ($afficher_inactif ? 'color: red; opacity: 0.75;' : '')?>" class="<?=(isset($this->statut) && $this->statut == 1 ? 'editable' : '')?> <?=($currentClassName::$TYPE_FORM == 'LISTE_COORDINATION' || $currentClassName::CAN_HAVE_COORDINATION ? 'ligne_type_coordination' : '')?>
            <?=(isset($_GET['id_ligne_active']) && (int)($_GET['id_ligne_active']) == $id_ligne ? 'ligne_active' : '')?><?=($currentClassName::CAN_HAVE_COORDINATION ? ' have_desc_besoin' : '')?>">
            <td class="<?=(isset($this->equipement_bio_medicaux_type_id) && $this->equipement_bio_medicaux_type_id > 0 ? 'average' : 'tiny')?>">
                <?php

                $canDelete = $canAddDeleteMove;
                $canInactivate = false;
                $equipement_non_editable = false;
                if (in_array($currentClassName, ['EquipementsBioMedicauxNoCoord', 'EquipementsBioMedicaux'])) {
                    list($canInactivate, $equipement_non_editable, $canDeleteOverrwrite) = $this->getDroitEditionEquipement();
                    if (!$canDeleteOverrwrite) {
                        $canDelete = $canDeleteOverrwrite;
                    }
                }

                if($id == 1)
                {
                    ?>
                    <input type="hidden" class="statut" name="statut[<?=$iteration?>]" value="<?=(isset($this->statut) ? $this->statut : 0)?>" />
                    <input type="hidden" class="id_ligne" name="id_ligne[<?=$iteration?>]" value="<?=$id_ligne?>" />
                    <?php
                    if(isset($this->statut) && $this->statut == 1)
                    {
                        ?>
                        <span class="delSortBtns">
<?php
if(havePermission('edition_projet') && $canAddDeleteMove)
{
    ?>
    <a href="javascript: void(0);" class="sort">
                                <img src="css/images/icons/dark/triangle_up_down.png" /></a>
    <?php
}

if(isset($this->equipement_bio_medicaux_type_id) && $this->equipement_bio_medicaux_type_id > 0)
{
    $formulaires_listes = array();
    if (method_exists('EquipementsBioMedicauxTypes', 'getFormulairesListes')
        && is_callable(array('EquipementsBioMedicauxTypes', 'getFormulairesListes')))
    {
        $formulaires_listes = EquipementsBioMedicauxTypes::getFormulairesListes($currentClassName);
    }

    if(count($formulaires_listes) > 0)
    {
        ?>
        <a href="index.php?section=admin&module=popup_listes_param&fiche_id=<?=$this->fiche_id?>&formulaire_liste_id=<?=$formulaires_listes[0]->id?>&liste_item_id=<?=$this->equipement_bio_medicaux_type_id?>" class="fancy" data-small_fancy="true">
									<img src="css/images/icons/dark/folder.png" /></a>
        <?php
    }
}
if(havePermission('edition_projet') && !$equipement_non_editable && $canDelete)
{
    ?>
    <a href="javascript: void(0);" class="delete">
                                <img src="css/images/icons/dark/trashcan.png" /></a>
    <?php
}
if ($canInactivate) {
    if (isset($this->ts_inactivation)) {
        ?>
        <a href="javascript: void(0);" class="activateLigne" id="activateLigne-<?= $this->id ?>">
                                    <img src="css/images/icons/dark/bended_arrow_left.png" /></a>
        <script>
                                    $(document).ready(function(){
										setTimeout(() => {
											inactivateLigneEquipement($('#<?= 'activateLigne-' . $this->id ?>'), false);
										}, 50);
									});
                                </script>
                                <?php
    } else {
    ?>
                                <a href="javascript: void(0);" class="inactivateLigne">
                                    <img src="css/images/icons/dark/trashcan.png" /></a>
        <?php
    }
}
?>
                    </span>
                        <?php
                    }
                    else if(isset($this->statut) && $this->statut == 2)
                    {
                        ?>
                        <span class="delSortBtns">

                        <b style="display: inline-block; width: 24px;font-size: 16px; font-weight: bold; color: black; position: absolute; right: 2px; top: -10px;" title="<?=txt('Provient du gabarit ').$gabarit->code?>">G</b>
                        
                    </span>
                        <?php
                    }
                }
                else
                {
                    print '&nbsp;';
                }
                ?>
            </td>
            <?php
            foreach ($regroupement['data'] as $key => $meta)
            {
                $meta_champ_id = $meta['champ_id'];
                if(isset($meta['is_reponse_coord']) && !isset($meta['hidden']))
                {
                    $disciplines_coords_champs[$meta['is_reponse_coord']][$key] = $meta;
                    continue;
                }

                if($currentClassName::CAN_HAVE_COORDINATION && $key == 'commentaires' && $currentClassName <> 'EquipementsBioMedicaux')
                {
                    ?>
                    <td style="position: relative;">
                    <?php
                }
                else
                {
                    $classSuppl = '';
                    $dataSuppl = '';
                    if (isset($meta['obligatoire']) && $meta['obligatoire']) {
                        $classSuppl = 'obligatoire';
                        $dataSuppl = ' data-titrechamp="' . $meta['titre'] . '" data-nomchamp="' . $meta['champ_id'] . '"';
                    }
                    ?>
                    <td class="<?= $meta['css_editeur'] . ' ' . $classSuppl ?>" style="position: relative; <?=(isset($champs_hidden[$key]) || isset($meta['hidden']) ? 'display: none;' : '')?>"<?= $dataSuppl ?>>
                    <?php
                }

                if(in_array($currentClassName, ['EquipementsBioMedicauxNoCoord', 'EquipementsBioMedicaux']) && in_array($meta['champ_id'], array('specificite_installation', 'qte_fantome', 'qte_existante', 'qte_conservee', 'equipement_bio_medicaux_type_id', 'fourni_installe_id', 'existant_nouveau_id', 'qte_requise', 'commentaires', 'remarques'))
                    && (!isset($disciplines_auth[16]) || $equipement_non_editable))
                {
                    $champs_readonly[$key] = 1;
                }

                if(isset($this->statut) && $this->statut == 2)
                {
                    print !is_array($this->$key) ? $this->$key : '';
                    ?>
                    <input type="hidden" name="<?=$meta['champ_id']?>[<?=$iteration?>]" value="<?=$this->$meta_champ_id?>" />
                    <input type="hidden" name="<?=$key?>[<?=$iteration?>]" value="<?=($this->$key)?>" />
                    <?php
                }
                else if(isset($champs_readonly[$key]))
                {
                    $extra_data = '';
                    $class_element = '';
                    $id_obj = $key.'_'.$this->$meta_champ_id;
                    if(isset($meta['classe']) && dm_strlen($meta['classe']) > 0)
                    {
                        $class_element = $meta['classe'];
                        $tablename_element = $meta['table'];
                        $liste_element = $class_element::getListe();
                        if(isset($liste_element[$this->$meta_champ_id]))
                        {
                            $this_element = $liste_element[$this->$meta_champ_id];
                            if($class_element::CAN_BE_COORD)
                            {
                                $extra_data .= ' data-is_coordination_electricite="'.$this_element->is_coordination_electricite.'" data-is_coordination_plomberie="'.$this_element->is_coordination_plomberie.'"';
                            }
                            else if($class_element::CAN_BE_COORD_FULL)
                            {
                                $extra_data .= ' data-is_coordination_electricite="'.$this_element->is_coordination_electricite.'" data-is_coordination_plomberie="'.$this_element->is_coordination_plomberie.'" data-is_coordination_mecanique="'.$this_element->is_coordination_mecanique.'" data-is_coordination_telecom="'.$this_element->is_coordination_telecom.'" data-is_coordination_architecture="'.$this_element->is_coordination_architecture.'" data-is_coordination_structure="'.$this_element->is_coordination_structure.'"';
                            }
                        }
                        $id_obj = $class_element.'-'.$this->$meta_champ_id;
                    }
                    if ($meta['table'] == 'quantites' && (!isset($this->$meta_champ_id) || ($this->$meta_champ_id) == 0)) {
                        $this->$meta_champ_id = 999999;
                    }
                    print '<span class="hyb_read">'.self::getReadonlyToDisplay($meta, $key, $this).'</span>';
                    ?>
                    <input type="hidden" data-classname="<?=$class_element?>" data-tablename="<?=$tablename_element?>" id="<?=$id_obj?>" name="<?=$meta_champ_id?>[<?=$iteration?>]" value="<?=($this->$meta_champ_id)?>" class=" <?=(isset($meta['check_if_coord']) ? 'check_if_coord' : '')?> <?=$meta['champ_id']?>"<?=$extra_data?> />
                    <?php
                }
                else
                {
                    if($currentClassName::CAN_HAVE_COORDINATION && $key == 'commentaires' && $currentClassName <> 'EquipementsBioMedicaux')
                    {
                        ?>
                        <div style="position: absolute; right: 12px; top: 45px; text-align: right; width: 985px;">
                            <div>
                                <span style="display: inline-block;">
<?php
print $meta['titre'].': ';

self::getEditableToDisplay($meta, $iteration, $this);
?>
                                </span>
                            </div>
                        </div>
                        <?php
                    }
                    else
                    {
                        self::getEditableToDisplay($meta, $iteration, $this);
                    }
                }
                ?>
                </td>
                <?php
            }

            if(count($disciplines_coords_champs) > 0)
            {
                $top = '85px';
                $right = '25px';
                if($currentClassName == 'AccessoiresArchitecturaux')
                {
                    $top = '45px';
                    $right = '-78px';
                }
                else if($currentClassName == 'EquipementsBioMedicaux')
                {
                    $top = '45px';
                    $right = '20px';
                }
                else if($currentClassName == 'ProprieteDeLinstrumentationDeMesureDetectionEtAnalyse')
                {
                    $top = '40px';
                    $right = '-191px';
                }
                else if($currentClassName == 'ProprieteDesRaccordsDePlomberie')
                {
                    $top = '40px';
                    $right = '-62px';
                }
                else if($currentClassName == 'ProprieteDesRaccordsDePlomberieSansCoord')
                {
                    $top = '40px';
                    $right = '-16px';
                }
                else if($currentClassName == 'ProprieteDesRaccordsDeControle')
                {
                    $top = '40px';
                    $right = '-200px';
                }
                ?>
                <td style="position: relative;">
                    <div style="position: absolute; right: <?=$right?>; top: <?=$top?>; text-align: left; width: 900px;">
                        <?php
                        foreach($disciplines_coords_champs as $discipline => $coords_champs)
                        {
                        ?>
                        <div class="coords_champs_block <?=$discipline?>" style="display: <?=($currentClassName::CAN_HAVE_COORDINATION ? 'none' : '')?>;">
                            <?php
                            if(in_array($discipline, array('is_coordination_electricite', 'is_coordination_plomberie', 'is_coordination_mecanique', 'is_coordination_telecom', 'is_coordination_architecture', 'is_coordination_structure')))
                            {
                            $titre_discipline = '';
                            if($discipline == 'is_coordination_electricite')
                            {
                                $titre_discipline = txt('Électricité:');
                            }
                            else if($discipline == 'is_coordination_plomberie')
                            {
                                $titre_discipline = txt('Plomberie:');
                            }
                            elseif($discipline == 'is_coordination_mecanique')
                            {
                                $titre_discipline = txt('Mécanique:');
                            }
                            elseif($discipline == 'is_coordination_telecom')
                            {
                                $titre_discipline = txt('Télécommunication:');
                            }
                            elseif($discipline == 'is_coordination_architecture')
                            {
                                $titre_discipline = txt('Architecture:');
                            }
                            elseif($discipline == 'is_coordination_structure')
                            {
                                $titre_discipline = txt('Structure:');
                            }
                            ?>
                            <div style="font-weight: bold; color: black; margin-bottom: 10px;"><?=$titre_discipline?>&nbsp;&nbsp;&nbsp;&nbsp;
                                <?php
                                }

                                foreach($coords_champs as $key => $coord_champ)
                                {
                                    ?>
                                    <span style="display: inline-block; position: relative;">
<?php
if(dm_substr($coord_champ['champ_id'], 0, 20) == 'conformite_du_besoin')
{
    print '<span>'.txt('L\'information ci-dessus permet d\'assurer la coordination des besoins').':</span>';
}
else
{
    print $coord_champ['titre'].': ';
}

if(isset($coord_champ['discipline_can_edit']))
{
    if(isset($disciplines_auth[(int)($coord_champ['discipline_can_edit'])]))
    {
        self::getEditableToDisplay($coord_champ, $iteration, $this);
    }
    else
    {
        $class_extra = '';
        $class_extra_ro = '';
        if($currentClassName::$TYPE_FORM == 'LISTE_COORDINATION' || $currentClassName::CAN_HAVE_COORDINATION)
        {
            if(isset($coord_champ['is_reponse_coord']))
            {
                $class_extra = 'is_reponse_coord '.$coord_champ['is_reponse_coord'];
                $class_extra_ro = 'is_reponse_coord_readonly '.$coord_champ['is_reponse_coord'];
                if(in_array($coord_champ['champ_id'], array('conformite_du_besoin_electricite_id', 'conformite_du_besoin_plomberie_id', 'conformite_du_besoin_mecanique_id', 'conformite_du_besoin_telecom_id', 'conformite_du_besoin_architecture_id', 'conformite_du_besoin_structure_id')))
                {
                    $class_extra .= ' is_reponse_coord_solution';
                    $class_extra_ro .= ' is_reponse_coord_solution_readonly';
                }
            }
            else
            {
                $class_extra = 'is_besoin_coord';
                $class_extra_ro = 'is_besoin_coord_readonly';
                if(in_array($coord_champ['champ_id'], array('equipement_bio_medicaux_type_id', 'accessoire_architecturaux_type_id')))
                {
                    $class_extra .= ' is_wipe_all';
                    $class_extra_ro .= ' is_wipe_all_readonly';
                }
            }
        }
        $coord_champ_id = $coord_champ['champ_id'];
        print '<b style="font-weight: bold; margin-left: 15px; color: blue;" class="'.$class_extra_ro.'">'.self::getReadonlyToDisplay($coord_champ, $key, $this, true).'</b>';
        ?>
        <input type="hidden" id="<?=$coord_champ['champ_id'].'_'.$this->id?>" name="<?=$coord_champ['champ_id']?>[<?=$iteration?>]" value="<?=($this->$coord_champ_id)?>" class="<?=$class_extra?>" />
        <?php
    }
}
else
{
    self::getEditableToDisplay($coord_champ, $iteration, $this);
}
//($currentClassName == 'ProprieteDeLinstrumentationDeMesureDetectionEtAnalyse' && $coord_champ['champ_id'] == 'instrument_id') ||
//	                                (in_array($currentClassName, array('ProprieteDesRaccordsDePlomberie', 'ProprieteDesRaccordsDePlomberieSansCoord')) && $coord_champ['champ_id'] == 'reseau_de_distribution_drainage_id') ||
//	                                ($currentClassName == 'ProprieteDesRaccordsDeControle' && $coord_champ['champ_id'] == 'automate_id')
$form = new Formulaires(Formulaires::getIdFromNom($currentClassName));
$first_champ = $form->getFirstChamp();

$is_qrt = false;
$champ_id = $first_champ['champ_id'];
$id_for_listeChamp = $this->$champ_id;
if(in_array($currentClassName, array('EquipementsBioMedicaux', 'AccessoiresArchitecturaux')))
{
    $is_qrt = dm_substr($coord_champ['champ_id'], 0, 20) == 'conformite_du_besoin' && isset($this->$champ_id) && $this->$champ_id > 0;
}
else if($currentClassName == 'ProprieteDeLinstrumentationDeMesureDetectionEtAnalyse')
{
    $is_qrt = $coord_champ['champ_id'] == 'instrument_id';
    $id_for_listeChamp = $this->id;
}
else if($currentClassName == 'ProprieteDesRaccordsDePlomberieSansCoord')
{
    $is_qrt = $coord_champ['champ_id'] == 'reseau_de_distribution_drainage_id';
    $id_for_listeChamp = $this->id;
}
else if($currentClassName == 'ProprieteDesRaccordsDeControle')
{
    $is_qrt = $coord_champ['champ_id'] == 'automate_id';
    $id_for_listeChamp = $this->id;
}
else if($currentClassName == 'ProprieteDesRaccordsDePlomberie')
{
    $is_qrt = $coord_champ['champ_id'] == 'reseau_de_distribution_drainage_id';
    $id_for_listeChamp = $this->id;
}

if(($is_qrt) && havePermission(array('repondant_qrt_bcf', 'demandeur_qrt_bcf')) && isset($disciplines_auth[(int)($coord_champ['discipline_can_edit'])]))
{
    print '<span class="messagerie" style="position: absolute; top: -13px; right: -30px; z-index: 10;" data-discipline_id="'.$coord_champ['discipline_can_edit'].'">';
    $qrts = Questions::getListe(['fiche_id' => $this->fiche_id, 'nom_table' => $first_champ['table'], 'liste_item_id' => $id_for_listeChamp, 'classe_formulaire' => $currentClassName, 'discipline_id' => $coord_champ['discipline_can_edit']]);
    if(count($qrts) > 0)
    {
        $qrt = array_values($qrts)[0];
        ?>
        <a href="javascript: void(0);" class="start_qrt" data-qrt_id="<?=$qrt->id?>" style="position: relative; top: 3px;">
                                            <img src="css/images/icons/dark/speech_bubble.png" /><?=($qrt->nb_notifications_all > 0 ? '<span class="extra">'.$qrt->nb_notifications_all.'</span>' : '')?></a>
        <?php
    }
    else
    {
        ?>
        <a href="index.php?section=admin&module=edit&table=questions&type=client&id=0&fiche_id=<?=$this->fiche_id?>&nom_table=<?=$first_champ['table']?>&liste_item_id=<?=$id_for_listeChamp?>&classe_formulaire=<?=$currentClassName?>&discipline_id=<?=$coord_champ['discipline_can_edit']?>&from_coordination=1" class="fancy" data-small_fancy="true" style="position: relative; top: 4px; opacity: 0.5;">
                                            <img src="css/images/icons/dark/speech_bubble.png" /></a>
        <?php
    }
    print '</span>';
}
?>
                            </span>
                                    <?php

                                    if(in_array($discipline, array('is_coordination_electricite', 'is_coordination_plomberie', 'is_coordination_mecanique', 'is_coordination_telecom', 'is_coordination_architecture', 'is_coordination_structure')) && dm_substr($coord_champ['champ_id'], 0, 20) == 'conformite_du_besoin')
                                    {
                                        print '</div>';
                                    }
                                }
                                ?>
                            </div>
                            <?php
                            }
                            ?>
                        </div>
                </td>
                <?php
            }
        }
        ?>
        </tr>
        <?php
    }

    /**
     * @param $iteration_ligne
     * @param $code_valeur_interne
     * @param $code_valeur_externe
     * @param $element_id
     */
    public static function getImportHTMLLigne($iteration_ligne, $code_valeur_interne, $code_valeur_externe, $element_id)
    {
        /** @var self $currentClassName */
        $currentClassName = get_called_class();
        $metas = $currentClassName::getChampsSaisieMetaData();
        ?>
        <tr class="editable">

            <td class="tiny">
                <span class="delSortBtns">

                    <a href="javascript: void(0);" class="sort">
                        <img src="css/images/icons/dark/triangle_up_down.png" /></a>

                    <a href="javascript: void(0);" class="delete">
                        <img src="css/images/icons/dark/trashcan.png" /></a>
                                    
                    <input type="hidden" name="element_liste_id[<?=$iteration_ligne?>]" value="<?=$element_id?>" class="element_id" />

                </span>
            </td>

            <td class="average">

                <input id="code_valeur_externe" type="text" name="code_valeur_externe[<?=$iteration_ligne?>]" value="<?=($code_valeur_externe)?>" style="width: 230px!important;" />

            </td>
            <?php
            $valeurs = dm_explode('_', $code_valeur_interne);

            $iteration = 0;
            foreach ($metas as $key => $meta)
            {
                if($meta['type'] == 'select' && !in_array($key, array('quantite_existante', 'quantite', 'quantite_fantome', 'quantite_demenager', 'conformite_du_besoin_electricite', 'conformite_du_besoin_plomberie', 'conformite_du_besoin_mecanique', 'conformite_du_besoin_telecom', 'conformite_du_besoin_architecture', 'conformite_du_besoin_structure', 'conformite_du_besoin', 'equipement_electricite', 'equipement_plomberie')))
                {
                    ?>
                    <td class="<?=$meta['css_editeur']?>" style="position: relative;">
                        <select name="<?=$meta['champ_id']?>[<?=$iteration_ligne?>]">
                            <option value=""> </option>
                            <?php
                            $meta['classe']::getOptions(array(), (isset($valeurs[$iteration]) ? (int)($valeurs[$iteration]) : 0));
                            ?>
                        </select>
                    </td>
                    <?php
                    $iteration++;
                }
            }
            ?>
        </tr>
        <?php
    }

    /**
     *
     */
    public static function getImportHTMLLigneTitre()
    {
        /** @var self $currentClassName */
        $currentClassName = get_called_class();
        $metas = $currentClassName::getChampsSaisieMetaData();
        ?>
        <tr>

            <td class="tiny">
                &nbsp;
            </td>

            <td class="title average">

                <?=txt('Code externe')?>

            </td>
            <?php
            foreach ($metas as $key => $meta)
            {
                if($meta['type'] == 'select' && !in_array($key, array('quantite_existante', 'quantite', 'quantite_fantome', 'quantite_demenager', 'conformite_du_besoin_electricite', 'conformite_du_besoin_plomberie', 'conformite_du_besoin_mecanique', 'conformite_du_besoin_telecom', 'conformite_du_besoin_architecture', 'conformite_du_besoin_structure', 'conformite_du_besoin', 'equipement_electricite', 'equipement_plomberie')))
                {
                    ?>
                    <td class="title <?=$meta['css_editeur']?>" style="position: relative;">
                        <?php
                        if(!($currentClassName::CAN_HAVE_COORDINATION && $key == 'commentaires' && $currentClassName <> 'EquipementsBioMedicaux'))
                        {
                            print $meta['titre'];
                        }
                        ?>
                    </td>
                    <?php
                }
            }
            ?>
        </tr>
        <?php
    }

    /**
     * @return array|mixed
     */
    public static function getMetaFromChampForCoordination()
    {
        /** @var self $currentClassName */
        $currentClassName = get_called_class();
        $metas = $currentClassName::getFlatChampsSaisieMetaData();

        foreach($metas as $key => $meta_tmp)
        {
            if(isset($meta_tmp['check_if_coord']))
            {
                return $meta_tmp;
            }
        }

        return array();
    }

    /**
     * @param $fiche_id
     * @return array
     */
    public static function getRaccordsElectriques($fiche_id)
    {
        global $db;

        $formulaires = Formulaires::getListe();
        $raccords = array();
        foreach($formulaires as $i => $formulaire)
        {
            $class_name = $formulaire->nom;
            if($class_name::CAN_HAVE_COORDINATION && !in_array($class_name, ['EquipementsBioMedicaux', 'EquipementsBioMedicauxNoCoord']))
            {
                $champ_coord = $class_name::getMetaFromChampForCoordination();
                $sql = "select fiches.code as fiche, fiches.ts_delete, fiche_id, circuit_electricite, tmp.nom as nom_element, 
                        '".$class_name::TITRE."' as nom_formulaire
                        from ".$class_name::SQL_TABLE_NAME."
                        join ".$champ_coord['table']." as tmp on tmp.id = ".$champ_coord['champ_id']."
                        join fiches on fiches.id = fiche_id
                        where equipement_electricite_id = ?";
                //printDebug(dm_nl2br(db::interpolateQuery($sql, array($fiche_id))));
                $raccords_tmp = $db->getAll($sql, array($fiche_id));
                $raccords = array_merge($raccords, $raccords_tmp);
            }
        }

        return $raccords;
    }

    /**
     * @param $fiche_id
     * @return array
     */
    public static function getRaccordsPlomberies($fiche_id)
    {
        global $db;

        $formulaires = Formulaires::getListe();
        $raccords = array();
        foreach($formulaires as $i => $formulaire)
        {
            $class_name = $formulaire->nom;
            if($class_name::CAN_HAVE_COORDINATION && !in_array($class_name, ['EquipementsBioMedicaux', 'EquipementsBioMedicauxNoCoord']))
            {
                $champ_coord = $class_name::getMetaFromChampForCoordination();
                $sql = "select fiches.code as fiche, fiches.ts_delete, fiche_id, tmp.nom as nom_element, '".$class_name::TITRE."' as nom_formulaire
                        from ".$class_name::SQL_TABLE_NAME."
                        join ".$champ_coord['table']." as tmp on tmp.id = ".$champ_coord['champ_id']."
                        join fiches on fiches.id = fiche_id
                        where equipement_plomberie_id = ?";
                $raccords_tmp = $db->getAll($sql, array($fiche_id));
                $raccords = array_merge($raccords, $raccords_tmp);
            }
        }

        return $raccords;
    }

    /**
     * @param $metas
     * @return mixed
     */
    public static function parseMetaData($metas)
    {
        global $cache_combine_data, $db;

        if(isset($cache_combine_data))
        {
            $combine_data = $cache_combine_data;
        }
        else
        {
            $sql = "select champs_textes_denormalises.id, formulaires.nom, nom_champ, champs_textes_denormalises.nom as titre, champs_textes_denormalises.formulaire_id
                    from champs_textes_denormalises
                    join formulaires on formulaires.id = formulaire_id
                    where projet_id = ?
                    order by champs_textes_denormalises.nom";
            $champs_combines = $db->getAll($sql, array(getProjetId()));
            $combine_data = array();
            foreach($champs_combines as $i => $champ_combine)
            {
                $combine_data[$champ_combine['nom']][] = $champ_combine;
            }

            $cache_combine_data = $combine_data;
        }

        /** @var self $currentClassName */
        $currentClassName = get_called_class();
        if(isset($combine_data[$currentClassName]))
        {
            $ramene_comm = false;
            if(isset($metas['commentaires']))
            {
                $meta_tmp = $metas['commentaires'];
                unset($metas['commentaires']);
                $ramene_comm = true;
            }

            if(isset($metas['regroupements']))
            {
                $id_regroupement = 9999;
                foreach($metas['regroupements'] as $id => $regroupement)
                {
                    if(isset($regroupement['data']['commentaires']) || isset($regroupement['data']['conformite_du_besoin_electricite']))
                    {
                        if($id_regroupement == 9999)
                        {
                            $id_regroupement = $id;
                        }
                        $metas['regroupements'][($id+1)] = $regroupement;
                    }
                }
                unset($metas['regroupements'][$id_regroupement]);
            }

            foreach($combine_data[$currentClassName] as $i => $champ_combine)
            {
                if(isset($metas['regroupements']))
                {
                    $metas['regroupements'][$id_regroupement]['titre'] = txt('Champs dénormalisés', false);
                    $metas['regroupements'][$id_regroupement]['data'][$champ_combine['nom_champ']] = array(
                        'classe'                => '',
                        'table'                 => '',
                        'champ_id'              => $champ_combine['nom_champ'],
                        'type'                  => 'input',
                        'format'                => 'text',
                        'titre'                 => txt($champ_combine['titre'], false),
                        'css_editeur'           => 'xxlarge',
                        'champ_texte_denormalise_id' => $champ_combine['id'],
                    );
                }
                else
                {
                    $metas[$champ_combine['nom_champ']] = array(
                        'classe'                => '',
                        'table'                 => '',
                        'champ_id'              => $champ_combine['nom_champ'],
                        'type'                  => 'input',
                        'format'                => 'text',
                        'titre'                 => txt($champ_combine['titre'], false),
                        'css_editeur'           => 'xxlarge',
                        'champ_texte_denormalise_id' => $champ_combine['id'],
                    );
                }
            }

            if(isset($metas['regroupements']))
            {
                ksort($metas['regroupements']);
            }

            if($ramene_comm)
            {
                $metas['commentaires'] = $meta_tmp;
            }

        }
        /*
                $currentClassName = get_called_class();
                if(isset($combine_data[$currentClassName]))
                {
                    if(!isset($metas['regroupements']))
                    {
                        $metas_tmp = $metas;
                        $metas_tmp['regroupements'][1]['titre'] = txt('Champs normalisés', false);
                        $metas_tmp['regroupements'][1]['data'] = $metas;
                    }

                    foreach($combine_data[$currentClassName] as $i => $champ_combine)
                    {
                        $id_regroupement = 20;
                        $metas_tmp['regroupements'][$id_regroupement]['titre'] = txt('Champs dénormalisés', false);
                        $metas_tmp['regroupements'][$id_regroupement]['data'][$champ_combine['nom_champ']] = array(
                            'classe'                => '',
                            'table'                 => '',
                            'champ_id'              => $champ_combine['nom_champ'],
                            'type'                  => 'input',
                            'format'                => 'text',
                            'titre'                 => txt($champ_combine['titre'], false),
                            'css_editeur'           => 'xxlarge',
                            'champ_texte_denormalise_id' => $champ_combine['id'],
                            //'hide_titre'    => 1,
                        );
                    }

                    $metas = $metas_tmp;
                }
        */
        return $metas;
    }

    public function displayMultipleSelect ($label, $nomListe, $champ_id, $className) {
        ?>
        <section>
            <label for="<?= $nomListe ?>"><?= $label ?></label>
            <div>
                <div>
                    <select class="multipleSelect" data-colname="<?= $champ_id ?>" id="champ_nom_adder">
                        <option value=""> </option>
                        <?php
                        $className::getOptions();
                        ?>
                    </select>
                </div>
                <div id="<?= $champ_id ?>">
                    <?php
                    if(isset($this->$nomListe) && is_array($this->$nomListe))
                    {
                        foreach($this->$nomListe as $obj)
                        {
                            ?>
                            <div id="<?= $champ_id ?>_<?= $obj->id ?>" class="top_block">
                                <a href="javascript: void(0);" class="del_multiple">
                                    <img src="css/images/icons/dark/trashcan.png" /></a>
                                <span><?= $obj->nom ?></span>
                                <input type="hidden" name="<?= $champ_id ?>[]" value="<?= $obj->id ?>" />
                            </div>
                            <?php
                        }
                    }
                    ?>
                </div>
            </div>
        </section>
        <?php
    }

    public static function haveEquipementsLies()
    {
        /** @var self $currentClassName */
        $currentClassName = get_called_class();

        $metas = $currentClassName::getFlatChampsSaisieMetaData();
        foreach ($metas as $meta) {
            if ($meta['type'] == 'equipements_lies') {
                return true;
            }
        }
        return false;
    }

    /**
     * @return FicheFormWrapper
     */
    protected static function getClasseContexte()
    {
        /** @var FicheFormWrapper $currentClassName */
        $currentClassName = get_called_class();
        return $currentClassName;
    }

    public static function getStandardFormatFromFormatType($type, $format): string
    {
        return match ($type . '-' . $format) {
            'checkbox-int' => 'bool',
            'input-int', 'readonly-int' => 'integer',
            'input-date' => 'date',
            'input-float' => 'double',
            default => 'string',
        };
    }

    public static function getValeurForFormatType($valeur, $type, $format): float|bool|int|string
    {
        $type = self::getStandardFormatFromFormatType($type, $format);
        return match ($type) {
            'bool' => ((int)$valeur == 1),
            'integer' => (int)$valeur,
            'double' => (float)$valeur,
            default => (string)$valeur,
        };
    }
}
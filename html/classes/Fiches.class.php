<?php

use JetBrains\PhpStorm\Pure;

/**
 * Class Fiches
 */
class Fiches extends DbRow
{
	/**
	 * @var
	 */
	public $id;
	/**
	 * @var
	 */
	public $fiche_parent_id;
	/**
	 * @var
	 */
	public $fiche_type_id;
	/**
	 * @var
	 */
	public $fiche_sous_type_id;
	/**
	 * @var
	 */
	public $batiment_id;
	/**
	 * @var
	 */
	public $code_alternatif;
	/**
	 * @var
	 */
	public $code_alternatif_2;
	/**
	 * @var
	 */
	public $id_revit;
	/**
	 * @var
	 */
	public $revit_unique_id;
	/**
	 * @var
	 */
	public $id_codebook;
	/**
	 * @var
	 */
	public $route_id;
	/**
	 * @var
	 */
	public $route;
	/**
	 * @var
	 */
	public $chainage_debut;
	/**
	 * @var
	 */
	public $chainage_fin;
	/**
	 * @var
	 */
	public $code;
	/**
	 * @var
	 */
	public $batiment_niveau_element_id;
	/**
	 * @var
	 */
	public $batiment_niveau_element;
	/**
	 * @var
	 */
	public $ts_ajout;
	/**
	 * @var
	 */
	public $ts_delete;
	/**
	 * @var
	 */
	public $usager_ajout_id;
	/**
	 * @var
	 */
	public $ts_modif;
	/**
	 * @var
	 */
	public $usager_modif_id;
	/**
	 * @var
	 */
	public $fiche_type;
	/**
	 * @var
	 */
	public $fiche_sous_type;
	/**
	 * @var
	 */
	public $batiment;
	/**
	 * @var
	 */
	public $is_gabarit;
	/**
	 * @var
	 */
	public $numero_lot;
	/**
	 * @var
	 */
	public $fiche_local_id;
	/**
	 * @var
	 */
	public $fiche_local_destination_id;
	/**
	 * @var
	 */
	public $fiche_local_controleur_id;
	/**
	 * @var
	 */
	public $description;
	/**
	 * @var
	 */
	public $commentaire;
	/**
	 * @var
	 */
	public $ouvrage_id;
	/**
	 * @var
	 */
	public $ouvrage;
	/**
	 * @var
	 */
	public $fiche_statut_id;
	/**
	 * @var
	 */
	public $fiche_statut;
	/**
	 * @var
	 */
	public $is_sans_structure_hierarchique;
	/*
	 * @var
	 */
	public $code_equipement;
	/*
	 * @var
	 */
    public $description_equipement;
	/*
	 * @var
	 */
	public $is_statut_desactive;
    public $gestion_porte_type_id;
    public $gestion_porte_type;
    public $gestion_porte_quincaillerie_type_id;
    public $utilite;
    public $code_source;
    public $code_destination;
    public $code_parent;
    public $nb_archives;
    public $nb_lignesForm;
    public $fichierIfc;
    public $gabarit_associe;

	/**
	 * @param $id
	 */
	protected function setDataForId($id)
	{
		global $db, $db_name, $Langue;

		if($id == 0)
		{
			$sql = "SELECT `COLUMN_NAME`, `DATA_TYPE`
                    FROM `INFORMATION_SCHEMA`.`COLUMNS` 
                    WHERE `TABLE_SCHEMA`='".$db_name."' 
                        AND `TABLE_NAME`='fiches'
                        order by DATA_TYPE;";
			$cols = $db->getAll($sql, array());

			foreach($cols as $data)
			{
				$col_name = $data['COLUMN_NAME'];
				$this->$col_name = ($data['DATA_TYPE'] == 'int' ? 0 : '');
			}
		}
		else
		{
			$sql = "select fiches.*, utilite, case when statut.id = 999999 then 1 else 0 end as is_statut_desactive, fiches_types.nom_$Langue as fiche_type, fiches_sous_types.nom_$Langue as fiche_sous_type, batiments.nom as batiment, concat(routes.code, ' - ', routes.nom) as route, statut.nom as fiche_statut
                    FROM fiches
                    join fiches_types on fiches_types.id = fiche_type_id
                    join fiches_sous_types on fiches_sous_types.id = fiche_sous_type_id
                    left join fiches_statuts statut on fiches.fiche_statut_id = statut.id
                    left join batiments on batiments.id = batiment_id
                    left join routes on routes.id = route_id
                    LEFT JOIN generaux_locaux gl ON gl.fiche_id = fiches.id
                    where fiches.id = ?";
			//printDebug(dm_nl2br(db::interpolateQuery($sql, array($id))));
			$row = $db->getRow($sql, array($id));

			$this->populateThisWithThat($row);
		}
	}

	/**
	 * @param array $filter
	 * @param string $orderBy
	 * @return Fiches[]
	 */
	public static function getListe($filter = array(), $orderBy = 'fiche.code')
	{
		global $db, $Langue, $champs_filtres_speciaux, $CodeClient, $champs_filtres_speciaux_equipements_bio_medicaux;

		$where = '';
		$data = array();

		$inclure_supprimees = (isset($filter['inclure_supprimees']) && (int)($filter['inclure_supprimees']) == 1);
		$inclure_supprimees = (isset($filter['just_deleted']) && (int)($filter['just_deleted']) == 1) ? true : $inclure_supprimees;
		$inclure_archivees = (isset($filter['inclure_archivees']) && (int)($filter['inclure_archivees']) == 1);
		$suffix_filtres_globaux = (isset($filter['filtre_global_to_use']) ? $filter['filtre_global_to_use'] : '');

		$key_filtre_top = 'filtre_top' . $suffix_filtres_globaux;
		$key_filtre_avance = 'filtre_avance' . $suffix_filtres_globaux;

		$joins_filtre = array();
		if (isset($_SESSION[$key_filtre_top]) && is_array($_SESSION[$key_filtre_top]) && (!isset($filter['no_filtre_global']) || (int)($filter['no_filtre_global']) == 0)) {
			if (isset($_SESSION[$key_filtre_top]['code_fiche_search']) && dm_strlen($_SESSION[$key_filtre_top]['code_fiche_search']) > 0) {
				$filter['term'] = $_SESSION[$key_filtre_top]['code_fiche_search'];
			}
			if (isset($_SESSION[$key_filtre_top]['batiment_id']) && (int)($_SESSION[$key_filtre_top]['batiment_id']) > 0) {
				$filter['batiment_id'] = $_SESSION[$key_filtre_top]['batiment_id'];
			}
			if (isset($_SESSION[$key_filtre_top]['fiche_type_id']) && (int)($_SESSION[$key_filtre_top]['fiche_type_id']) > 0) {
				$filter['fiche_type_id'] = $_SESSION[$key_filtre_top]['fiche_type_id'];
			}
			if (isset($_SESSION[$key_filtre_top]['fiche_sous_type_id']) && (int)($_SESSION[$key_filtre_top]['fiche_sous_type_id']) > 0) {
				$filter['fiche_sous_type_id'] = $_SESSION[$key_filtre_top]['fiche_sous_type_id'];
			}
			if (isset($_SESSION[$key_filtre_top]['numero_lot']) && dm_strlen($_SESSION[$key_filtre_top]['numero_lot']) > 0) {
				$filter['numero_lot'] = $_SESSION[$key_filtre_top]['numero_lot'];
			}
			if (isset($_SESSION[$key_filtre_top]['code_alternatif']) && dm_strlen($_SESSION[$key_filtre_top]['code_alternatif']) > 0) {
				$filter['code_alternatif'] = $_SESSION[$key_filtre_top]['code_alternatif'];
			}
			if (isset($_SESSION[$key_filtre_top]['code_alternatif_2']) && dm_strlen($_SESSION[$key_filtre_top]['code_alternatif_2']) > 0) {
				$filter['code_alternatif_2'] = $_SESSION[$key_filtre_top]['code_alternatif_2'];
			}
			if (isset($_SESSION[$key_filtre_top]['id_revit']) && dm_strlen($_SESSION[$key_filtre_top]['id_revit']) > 0) {
				$filter['id_revit'] = $_SESSION[$key_filtre_top]['id_revit'];
			}
			if (isset($_SESSION[$key_filtre_top]['id_codebook']) && dm_strlen($_SESSION[$key_filtre_top]['id_codebook']) > 0) {
				$filter['id_codebook'] = $_SESSION[$key_filtre_top]['id_codebook'];
			}
			if (isset($_SESSION[$key_filtre_top]['niveau']) && count($_SESSION[$key_filtre_top]['niveau']) > 0) {
				$filter['niveau'] = $_SESSION[$key_filtre_top]['niveau'];
			}
			if (isset($_SESSION[$key_filtre_top]['fiche_statut_id']) && is_array($_SESSION[$key_filtre_top]['fiche_statut_id'])) {
				$filter['fiche_statut_id'] = $_SESSION[$key_filtre_top]['fiche_statut_id'];
			}
			if (isset($_SESSION[$key_filtre_top]['fiche_codes']) && is_array($_SESSION[$key_filtre_top]['fiche_codes'])) {
				$filter['codes'] = $_SESSION[$key_filtre_top]['fiche_codes'];
			}
            if (isset($_SESSION[$key_filtre_top]['codes_alternatifs']) && is_array($_SESSION[$key_filtre_top]['codes_alternatifs'])) {
                $filter['codes_alternatifs'] = $_SESSION[$key_filtre_top]['codes_alternatifs'];
            }
            if (isset($_SESSION[$key_filtre_top]['ids_revits']) && is_array($_SESSION[$key_filtre_top]['ids_revits'])) {
                $filter['ids_revits'] = $_SESSION[$key_filtre_top]['ids_revits'];
            }
            if (isset($_SESSION[$key_filtre_top]['ids_codebooks']) && is_array($_SESSION[$key_filtre_top]['ids_codebooks'])) {
                $filter['ids_codebooks'] = $_SESSION[$key_filtre_top]['ids_codebooks'];
            }
			if (isset($_SESSION[$key_filtre_top]['echeancier_id']) && is_array($_SESSION[$key_filtre_top]['echeancier_id'])) {
				$filter['echeancier_id'] = $_SESSION[$key_filtre_top]['echeancier_id'];
			}
			if (isset($_SESSION[$key_filtre_top]['is_orphelin']) && intval($_SESSION[$key_filtre_top]['is_orphelin']) == 1) {
				$filter['is_orphelin'] = $_SESSION[$key_filtre_top]['is_orphelin'];
			}
		}

		if (isset($_SESSION[$key_filtre_avance]) && is_array($_SESSION[$key_filtre_avance]) && (!isset($filter['no_filtre_global']) || (int)($filter['no_filtre_global']) == 0)) {
			$iter = 0;
			foreach ($_SESSION[$key_filtre_avance] as $formulaire_id => $champs) {
				if ($formulaire_id === 0) {
					foreach ($champs as $niveau_id => $valeur) {
						//if(is_int($valeur) && (int)($valeur) > 0)
						{
							$joins_filtre[] = "join fiches_batiment_niveaux_elements as alias$iter on alias$iter.fiche_id = fiche.id and alias$iter.batiment_niveau_id = ?";
							$data[] = $niveau_id;

							$joins_filtre[] = "join batiments_niveaux_elements as alias_2_$iter on alias$iter.batiment_niveau_element_id = alias_2_$iter.id and alias_2_$iter.nom like ?";
							$data[] = '%' . $valeur . '%';

							$iter++;
						}
					}
				} else if ($formulaire_id == 'ids') {
					$filter['ids'] = $champs;
				} else if (str_starts_with($formulaire_id, 'custom_')) {
                    $joinCardinality = 'inner ';
                    $join_filtre_clause = '';
                    foreach ($champs as $nom_champ => $valeurs) {
                        if (isset($champs_filtres_speciaux[$nom_champ])) {
                            if ($nom_champ == 'absence_donnees') {
                                $joinCardinality = 'left ';
                                $where .= " and fcpv$iter.id is null";
                            }
                        } else {
                            $join_filtre_clause = " and fcpv$iter.formulaire_custom_parametre_id = ?";
                            $data[] = $nom_champ;

                            if (!is_array($valeurs)) {
                                $valeurs = array($valeurs);
                            }
                            if (count($valeurs) == 0) {
                                continue;
                            }

                            if (count($valeurs) == 1 && $valeurs[0] == -1) {
                                $joinCardinality = 'left ';
                                $where .= " and fcpv$iter.id is null";
                            }

                            $join_filtre_clause .= " and (";

                            $iter_or = 0;
                            foreach ($valeurs as $valeur) {
                                if (isset($champs_filtres_speciaux[$valeur])) {
                                    if ($valeur == 'presence_donnees') {
                                        $join_filtre_clause .= ($iter_or == 0 ? '' : ' or') . " fcpv$iter.valeur is not null";
                                        $iter_or++;
                                    }
                                } else if (is_string($valeur)) {
                                    $join_filtre_clause .= ($iter_or == 0 ? '' : ' or') . " fcpv$iter.valeur = ?";
                                    $data[] = $valeur;
                                    $iter_or++;
                                }
                            }

                            $join_filtre_clause .= ")";
                        }

                        $join_filtre = "$joinCardinality join formulaires_customs_parametres_valeurs as fcpv$iter on fcpv$iter.fiche_id = fiche.id $join_filtre_clause";
                        $joins_filtre[] = $join_filtre;
                        $iter++;
                    }
                } else {
					$className = Formulaires::getNomFromId($formulaire_id);
					$formsWithQtys = Formulaires::getFormulairesClassNameWithQuantite();
					$isFormWithQ = in_array($className, $formsWithQtys);
					$form = new Formulaires($formulaire_id);
					$first_champ = $form->getFirstChamp();

					$add_left_join = false;
					$add_join = false;
					$add_joins_doublon = false;
					$join_filtre_clause = '';
					$left_join_where_clause = '';
					foreach ($champs as $nom_champ => $valeurs) {
					    if (isset($champs_filtres_speciaux[$nom_champ])) {
					        if ($nom_champ == 'presence_donnees') {
						        $add_join = true;
						        if ($isFormWithQ) {
							        $join_filtre_clause .= " and " . $first_champ['champ_id'] . " is not null";
                                }
                            } else if ($nom_champ == 'absence_donnees') {
						        $add_left_join = true;
						        if ($isFormWithQ) {
							        $left_join_where_clause .= " and (alias$iter.id is null or " . $first_champ['champ_id'] . " is null)";
						        }
					        } else if ($nom_champ == 'presence_doublons' && $isFormWithQ) {
						        $add_joins_doublon = true;
					        }
                        } else {
						    if (!is_array($valeurs)) {
							    $valeurs = array($valeurs);
						    }
						    if (count($valeurs) == 0) {
							    continue;
						    }
						    $is_param_intrinseque = false;
						    $exploded_champ_nom = dm_explode('__', $nom_champ);
						    if ($exploded_champ_nom[0] == 'intrinseque') {
							    $nom_champ = $exploded_champ_nom[1] . '_id';
							    $valeur_id_intrinseque = $exploded_champ_nom[2];
							    $is_param_intrinseque = true;
						    }

						    if ($is_param_intrinseque) {
							    $sql = "select distinct liste_item_id
                                    from formulaires_listes_parametres_valeurs flpv
                                    join formulaires_listes_parametres flp on flp.id = formulaire_liste_parametre_id
                                    where valeur in (" . db::placeHolders($valeurs) . ") and formulaire_liste_parametre_id = ?";
							    $data_liste_items = array_merge($valeurs, array($valeur_id_intrinseque));
							    $valeurs = $db->getCol($sql, $data_liste_items);
						    }

						    $join_filtre_clause .= " and (";
						    if (count($valeurs) == 1 && $valeurs[0] == -1) {
							    $add_left_join = true;
							    $left_join_where_clause = " and alias$iter.id is null";
						    }

						    $iter_or = 0;
						    foreach ($valeurs as $iter_valeur => $valeur) {
							    if (isset($champs_filtres_speciaux[$valeur])) {
								    if ($valeur == 'presence_donnees') {
									    $join_filtre_clause .= ($iter_or == 0 ? '' : ' or') . " alias$iter." . $nom_champ . " is not null";
									    $add_join = true;
									    $iter_or++;
								    } else if ($valeur == 'absence_donnees') {
                                        $left_join_where_clause .= ($iter_or == 0 ? '' : ' or') . " (alias$iter.id is null or alias$iter." . $nom_champ . " is null)";
									    $add_left_join = true;
									    $iter_or++;
								    }
							    } else if ($valeur == -1) {
								    if (isset($champs_filtres_speciaux_equipements_bio_medicaux[$nom_champ])) {
									    $join_filtre_clause .= ($iter_or == 0 ? '' : ' or') . " exists (select id from equipements_bio_medicaux_types ebmt where id = alias$iter.equipement_bio_medicaux_type_id and ebmt.$nom_champ" . "_id is null)";
								    } else {
									    $join_filtre_clause .= ($iter_or == 0 ? '' : ' or') . " alias$iter." . $nom_champ . " is null";
								    }
								    $add_join = true;
								    $iter_or++;
							    } else if ((int)($valeur) > 0) {
								    if (isset($champs_filtres_speciaux_equipements_bio_medicaux[$nom_champ])) {
									    $join_filtre_clause .= ($iter_or == 0 ? '' : ' or') . " exists (select id from equipements_bio_medicaux_types ebmt where id = alias$iter.equipement_bio_medicaux_type_id and ebmt.$nom_champ" . "_id = ?)";
								    } else {
									    $join_filtre_clause .= ($iter_or == 0 ? '' : ' or') . " alias$iter." . $nom_champ . " = ?";
								    }
								    $data[] = $valeur;
								    $add_join = true;
								    $iter_or++;
							    } else if (is_string($valeur)) {
								    $join_filtre_clause .= ($iter_or == 0 ? '' : ' or') . " alias$iter." . $nom_champ . " like ?";
								    $data[] = '%' . $valeur . '%';
								    $add_join = true;
								    $iter_or++;
							    }
						    }

						    $join_filtre_clause .= ")";
					    }
					}
					if ($add_join) {
                        $join_filtre = "join " . $className::SQL_TABLE_NAME . " as alias$iter on alias$iter.fiche_id = fiche.id$join_filtre_clause";
                        $joins_filtre[] = $join_filtre;
					} else if($add_left_join){
						$join_filtre = "left join " . $className::SQL_TABLE_NAME . " as alias$iter on alias$iter.fiche_id = fiche.id";
						$joins_filtre[] = $join_filtre;
						$where .= $left_join_where_clause;
                    } else if ($add_joins_doublon) {
						$joins_filtre[] = "join " . $className::SQL_TABLE_NAME . " as alias$iter on alias$iter.fiche_id = fiche.id";
						$joins_filtre[] = "join " . $className::SQL_TABLE_NAME . " as alias$iter$iter on alias$iter$iter.fiche_id = fiche.id and alias$iter$iter." . $first_champ['champ_id'] . " = alias$iter." . $first_champ['champ_id'] . " and alias$iter$iter.id <> alias$iter.id";
                    }
					$iter++;
				}
			}
		}

		if (isset($filter['niveau']) && count($filter['niveau']) > 0) {
			foreach ($filter['niveau'] as $niveau_id => $niveau_element_id) {
				if (is_numeric($niveau_element_id) && (int)($niveau_element_id) > 0) {
					$joins_filtre[] = "join fiches_batiment_niveaux_elements as niveau_$niveau_id on niveau_$niveau_id.fiche_id = fiche.id and niveau_$niveau_id.batiment_niveau_id = ?";
					$data[] = $niveau_id;

					$joins_filtre[] = "join batiments_niveaux_elements as niveau_element_$niveau_id on niveau_$niveau_id.batiment_niveau_element_id = niveau_element_$niveau_id.id and niveau_element_$niveau_id.id = ?";
					$data[] = (int)($niveau_element_id);
				} else if (dm_strlen($niveau_element_id) > 0) {
					$joins_filtre[] = "join fiches_batiment_niveaux_elements as niveau_$niveau_id on niveau_$niveau_id.fiche_id = fiche.id and niveau_$niveau_id.batiment_niveau_id = ?";
					$data[] = $niveau_id;

					$joins_filtre[] = "join batiments_niveaux_elements as niveau_element_$niveau_id on niveau_$niveau_id.batiment_niveau_element_id = niveau_element_$niveau_id.id and niveau_element_$niveau_id.nom like ?";
					$data[] = '%' . $niveau_element_id . '%';
				}
			}
		}

		$extra_select = "";

        $where .= ($inclure_supprimees ? '' : ' and fiche.ts_delete is null') . ($inclure_archivees ? '' : ' and fiche.ts_archive is null');

        if (!isset($filter['ignore_ouvrage'])) {
            if (isset($filter['projet_id_destination']) && (int)($filter['projet_id_destination']) > 0) {
                $where .= ' and fiche.ouvrage_id in (select ouvrage_id from projets where id = ?)';
                $data[] = (int)($filter['projet_id_destination']);
            } else {
                $where .= ' and fiche.ouvrage_id = ?';
                if (isset($filter['ouvrage_id'])) {
                    $data[] = $filter['ouvrage_id'];
                } else {
                    $data[] = getOuvrageId();
                }
            }
        }

		if (getProjetId() > 0 && isFicheReadonly()) {
			$where .= ' and exists (select fiche_id from fiches_logs where fiche_id = fiche.id and projet_id = ? limit 1)';
			$data[] = getProjetId();
		}

		if (isset($filter['form_for_ligne'])) {
			$iter = isset($iter) ? $iter : 1;
			$className_tmp = $filter['form_for_ligne'];
			$joins_filtre[] = "left join " . $className_tmp::SQL_TABLE_NAME . " as alias$iter on alias$iter.fiche_id = fiche.id";
			$extra_select .= ", alias$iter." . $filter['code_pivot_ligne'];
			$iter++;
		}

		if (isset($filter['extract_nomenclature']) || isset($filter['get_generaux_equipements_data'])) {
			$joins_filtre[] = 'left join generaux_equipements ge on ge.fiche_id = fiche.id';

			if (isset($filter['extract_nomenclature'])) {
				$joins_filtre[] = "left join nomenclatures as n on n.id = ge.nomenclature_id";
				$extra_select .= ", concat(n.code, ' - ', n.nom_" . $Langue . ") as nomenclature";
			}

			if (isset($filter['get_generaux_equipements_data'])) {
				$extra_select .= ", ebmt.nom as code_equipement, ebmt.description_en as description_equipement";
				$joins_filtre[] = 'left join equipements_bio_medicaux_types ebmt on ebmt.id = ge.code_norme_id';
			}
        }

		if (isset($filter['term']) && dm_strlen($filter['term']) > 0) {
			$where .= ' and fiche.code like ?';
			$data[] = '%' . $filter['term'] . '%';
		}

		if (isset($filter['batiment_id']) && (int)($filter['batiment_id']) > 0) {
			$where .= ' and fiche.batiment_id = ?';
			$data[] = (int)($filter['batiment_id']);
		}

		if (isset($filter['fiche_type_id']) && (int)($filter['fiche_type_id']) > 0) {
			$where .= ' and fiche.fiche_type_id = ?';
			$data[] = (int)($filter['fiche_type_id']);
		}

		if (isset($filter['fiche_type_id_exclude']) && (int)($filter['fiche_type_id_exclude']) > 0) {
			$where .= ' and fiche.fiche_type_id <> ?';
			$data[] = (int)($filter['fiche_type_id_exclude']);
		}

		if (isset($filter['fiche_parent_id_null'])) {
			$where .= ' and fiche.fiche_parent_id is null';
		}

		if (isset($filter['fiche_local_id_null'])) {
			$where .= ' and fiche.fiche_local_id is null';
		}

		if (isset($filter['numero_lot']) && dm_strlen($filter['numero_lot']) > 0) {
			$where .= ' and fiche.numero_lot = ?';
			$data[] = $filter['numero_lot'];
		}

        if (isset($filter['codes_alternatifs']) && is_array($filter['codes_alternatifs'])) {
            $where .= " and fiche.code_alternatif in (" . db::placeHolders($filter['codes_alternatifs']) . ")";
            $data = array_merge($data, $filter['codes_alternatifs']);
        }

        if (isset($filter['code_alternatif']) && dm_strlen($filter['code_alternatif']) > 0) {
			$where .= ' and fiche.code_alternatif like ?';
			$data[] = '%' . $filter['code_alternatif'] . '%';
		}

		if (isset($filter['code_alternatif_2']) && dm_strlen($filter['code_alternatif_2']) > 0) {
			$where .= ' and fiche.code_alternatif_2 like ?';
			$data[] = '%' . $filter['code_alternatif_2'] . '%';
		}

        if (isset($filter['ids_revits']) && is_array($filter['ids_revits'])) {
            $where .= " and fiche.id_revit in (" . db::placeHolders($filter['ids_revits']) . ")";
            $data = array_merge($data, $filter['ids_revits']);
        }

        if (isset($filter['id_revit']) && dm_strlen($filter['id_revit']) > 0) {
			$where .= ' and fiche.id_revit like ?';
			$data[] = '%' . $filter['id_revit'] . '%';
		}

        if (isset($filter['revit_unique_id']) && dm_strlen($filter['revit_unique_id']) > 0) {
            $where .= ' and fiche.revit_unique_id = ?';
            $data[] = $filter['revit_unique_id'];
        }

        if (isset($filter['ids_codebooks']) && is_array($filter['ids_codebooks'])) {
            $where .= " and fiche.id_codebook in (" . db::placeHolders($filter['ids_codebooks']) . ")";
            $data = array_merge($data, $filter['ids_codebooks']);
        }
        if (isset($filter['id_codebook']) && dm_strlen($filter['id_codebook']) > 0) {
			$where .= ' and fiche.id_codebook like ?';
			$data[] = '%' . $filter['id_codebook'] . '%';
		}

		if (isset($filter['numero_lot_not']) && dm_strlen($filter['numero_lot_not']) > 0) {
			$where .= ' and fiche.numero_lot <> ?';
			$data[] = $filter['numero_lot_not'];
		}

        if (isset($filter['fiche_parent_id']) && is_array($filter['fiche_parent_id'])) {
            if (count($filter['fiche_parent_id']) > 0) {
                $where .= " and fiche.fiche_parent_id in (" . db::placeHolders($filter['fiche_parent_id']) . ")";
                $data = array_merge($data, $filter['fiche_parent_id']);
            }
        } else if (isset($filter['fiche_parent_id']) && $filter['fiche_parent_id'] == 'exists') {
            $where .= ' and exists (select id from fiches f2 where f2.fiche_parent_id = fiche.id)';
        } else if (isset($filter['fiche_parent_id']) && (int)($filter['fiche_parent_id']) > 0) {
			$where .= ' and fiche.fiche_parent_id = ?';
			$data[] = (int)($filter['fiche_parent_id']);
		}

        if (isset($filter['fiche_local_id']) && is_array($filter['fiche_local_id'])) {
            if (count($filter['fiche_local_id']) > 0) {
                $where .= " and fiche.fiche_local_id in (" . db::placeHolders($filter['fiche_local_id']) . ")";
                $data = array_merge($data, $filter['fiche_local_id']);
            }
        } else if (isset($filter['fiche_local_id']) && $filter['fiche_local_id'] == 'exists') {
            $where .= ' and exists (select id from fiches f2 where f2.fiche_local_id = fiche.id)';
        } else if (isset($filter['fiche_local_id'])) {
			$where .= ' and fiche.fiche_local_id = ?';
			$data[] = (int)($filter['fiche_local_id']);
		}

        if (isset($filter['fiche_local_destination_id']) && is_array($filter['fiche_local_destination_id'])) {
            if (count($filter['fiche_local_destination_id']) > 0) {
                $where .= " and fiche.fiche_local_destination_id in (" . db::placeHolders($filter['fiche_local_destination_id']) . ")";
                $data = array_merge($data, $filter['fiche_local_destination_id']);
            }
        } else if (isset($filter['fiche_local_destination_id']) && $filter['fiche_local_destination_id'] == 'exists') {
            $where .= ' and exists (select id from fiches f2 where f2.fiche_local_destination_id = fiche.id)';
        } else if (isset($filter['fiche_local_destination_id'])) {
			$where .= ' and fiche.fiche_local_destination_id = ?';
			$data[] = (int)($filter['fiche_local_destination_id']);
		}

        if (isset($filter['gestion_porte_type_id']) && is_array($filter['gestion_porte_type_id'])) {
            if (count($filter['gestion_porte_type_id']) > 0) {
                $where .= " and fiche.gestion_porte_type_id in (" . db::placeHolders($filter['gestion_porte_type_id']) . ")";
                $data = array_merge($data, $filter['gestion_porte_type_id']);
            }
        } else if (isset($filter['gestion_porte_type_id'])) {
            $where .= ' and fiche.gestion_porte_type_id = ?';
            $data[] = (int)($filter['gestion_porte_type_id']);
        }

		if (isset($filter['fiche_local_controleur_id'])) {
			$where .= ' and fiche.fiche_local_controleur_id = ?';
			$data[] = (int)($filter['fiche_local_controleur_id']);
		}

		if (isset($filter['is_sans_structure_hierarchique'])) {
			$where .= ' and fiche.is_sans_structure_hierarchique = ?';
			$data[] = (int)($filter['is_sans_structure_hierarchique']);
		}

		if (isset($filter['fiche_sous_type_id']) && is_array($filter['fiche_sous_type_id'])) {
			$where .= " and fiche.fiche_sous_type_id in (" . db::placeHolders($filter['fiche_sous_type_id']) . ")";
			$data = array_merge($data, $filter['fiche_sous_type_id']);
		} else if (isset($filter['fiche_sous_type_id']) && (int)($filter['fiche_sous_type_id']) > 0) {
			$where .= ' and fiche.fiche_sous_type_id = ?';
			$data[] = (int)($filter['fiche_sous_type_id']);
		} else {
            if (Projets::instance()->isGestionUnifieePortes()) {
                $where .= ' and fiche.fiche_sous_type_id not in (2000013, 2000014)';
            }
        }

		if (isset($filter['fiche_sous_type_id_not']) && (int)($filter['fiche_sous_type_id_not']) > 0) {
			$where .= ' and fiche.fiche_sous_type_id <> ?';
			$data[] = (int)($filter['fiche_sous_type_id_not']);
		}

		if (isset($filter['excluded_fiche_id']) && (int)($filter['excluded_fiche_id']) > 0) {
			$where .= ' and fiche.id <> ?';
			$data[] = (int)($filter['excluded_fiche_id']);
		}

		if (isset($filter['procedure_entretien_id']) && (int)($filter['procedure_entretien_id']) > 0) {
			$where .= ' and fiche.id <> ?';
			$data[] = (int)($filter['excluded_fiche_id']);
		}

		if (isset($filter['echeancier_id'])) {
			if (is_array($filter['echeancier_id'])) {
				if (count($filter['echeancier_id']) == 1 && $filter['echeancier_id'][0] == -1) {
					$where .= " and not exists (select fiche_id from echeanciers_fiches where fiche_id = fiche.id)";
				} else if (in_array(-1, $filter['echeancier_id'])) {
					$where .= " and (not exists (select fiche_id from echeanciers_fiches where fiche_id = fiche.id) or exists (select fiche_id from echeanciers_fiches where fiche_id = fiche.id and echeancier_id in (" . db::placeHolders($filter['echeancier_id']) . ")))";
					$data = array_merge($data, $filter['echeancier_id']);
				} else if (count($filter['echeancier_id']) > 0) {
					$where .= " and exists (select fiche_id from echeanciers_fiches where fiche_id = fiche.id and echeancier_id in (" . db::placeHolders($filter['echeancier_id']) . "))";
					$data = array_merge($data, $filter['echeancier_id']);
				}
			} else if ((int)($filter['echeancier_id']) > 0) {
				$where .= ' and exists (select fiche_id from echeanciers_fiches where fiche_id = fiche.id and echeancier_id = ?)';
				$data[] = (int)($filter['echeancier_id']);
			}
		}

		if (isset($filter['fiche_statut_id']) && is_array($filter['fiche_statut_id'])) {
			if (count($filter['fiche_statut_id']) == 1 && $filter['fiche_statut_id'][0] == -1) {
				$where .= " and fiche.fiche_statut_id is null";
			} else if (in_array(-1, $filter['fiche_statut_id'])) {
				$where .= " and (fiche.fiche_statut_id is null or fiche.fiche_statut_id in (" . db::placeHolders($filter['fiche_statut_id']) . "))";
				$data = array_merge($data, $filter['fiche_statut_id']);
			} else {
				$where .= " and fiche.fiche_statut_id in (" . db::placeHolders($filter['fiche_statut_id']) . ")";
				$data = array_merge($data, $filter['fiche_statut_id']);
			}
		}

		if (isset($filter['fiche_statut_id_not'])) {
			$where .= " and (fiche.fiche_statut_id <> ? or fiche.fiche_statut_id is null)";
			$data[] = $filter['fiche_statut_id_not'];
		}

		if ($CodeClient <> 'hsc' && isset($filter['exclureFichesStatutNonEditable']) && (int)($filter['exclureFichesStatutNonEditable']) == 1) {
		    $fichesAuth = getAutorizedFichesForStatut(($filter['projet_id_destination'] ?? null));
			if (count($fichesAuth) > 0) {
				$where .= " and fiche.id in (" . db::placeHolders($fichesAuth) . ")";
				$data = array_merge($data, $fichesAuth);
			} else {
				$where .= " and false";
			}
		}

		if (isset($filter['echeancier_ids']) && count($filter['echeancier_ids']) > 0) {
			$where .= ' and exists (select fiche_id from echeanciers_fiches where fiche_id = fiche.id and echeancier_id in (' . db::placeHolders($filter['echeancier_ids']) . '))';
			$data = array_merge($data, $filter['echeancier_ids']);
		}

        if ($CodeClient <> 'hsc' && isset($filter['exclureFichesFromDepotsVerouilles']) && (int)($filter['exclureFichesFromDepotsVerouilles']) == 1) {
            $fiches_not_in = self::getFicheIdsDepotsVerouilles();
	        if (count($fiches_not_in) > 0) {
		        $where .= " and fiche.id not in (" . db::placeHolders($fiches_not_in) . ")";
		        $data = array_merge($data, $fiches_not_in);
	        }
        }

		if (isset($filter['ids'])) {
			if (count($filter['ids']) > 0) {
				$where .= " and fiche.id in (" . db::placeHolders($filter['ids']) . ")";
				$data = array_merge($data, $filter['ids']);
			} else {
				$where .= " and false";
			}
		}

		if (isset($filter['codes'])) {
			if (count($filter['codes']) > 0) {
				$where .= " and fiche.code in (" . db::placeHolders($filter['codes']) . ")";
				$data = array_merge($data, $filter['codes']);
			} else {
				$where .= " and false";
			}
		}

		if (isset($filter['projet_formulaire'])) {
			$formulaire_id = (int)($filter['projet_formulaire']['formulaire_id']);
			$projet_id = (int)($filter['projet_formulaire']['projet_id']);

			$where .= ' and not exists (select fiche_id from fiches_logs join projets on projets.id = projet_id where fiche_id = fiche.id and formulaire_id = ? and projet_id <> ? and token_archive is null and ts_fermeture is null limit 1)';
			$data[] = $formulaire_id;
			$data[] = $projet_id;
		}

		if (isset($filter['is_gabarit'])) {
			$where .= ' and fiche.is_gabarit = ?';
			$data[] = (int)($filter['is_gabarit']);
		} else {
			$where .= ' and fiche.is_gabarit = ?';
			$data[] = isset($_SESSION['is_gabarit']) ? (int)($_SESSION['is_gabarit']) : 0;
		}

		if (isset($filter['gabarit_id_to_affecte'])) {
			$where .= ' and not exists (select fiche_id from fiches_gabarits where fiche_id = fiche.id and gabarit_id = ' . (int)($filter['gabarit_id_to_affecte']) . ')';
		}

		if (isset($filter['gabarit_id_to_dissocier'])) {
			$where .= ' and exists (select fiche_id from fiches_gabarits where fiche_id = fiche.id and gabarit_id = ' . (int)($filter['gabarit_id_to_dissocier']) . ')';
		}

		if (isset($filter['just_deleted'])) {
			$where .= ' and fiche.ts_delete is not null';
		}

		if (isset($filter['exclure_avec_statut'])) {
			$where .= ' and fiche.fiche_statut_id is null';
		}

        if (isset($filter['equipement_controle_acces_type_id'])) {
            if (count($filter['equipement_controle_acces_type_id']) > 0) {
                $where .= " and exists (select id from controles_acces where equipement_controle_acces_type_id in (" . db::placeHolders($filter['equipement_controle_acces_type_id']) . ") and fiche_id = fiche.id)";
                $data = array_merge($data, $filter['equipement_controle_acces_type_id']);
            } else {
                $where .= " and false";
            }
        }

        if (isset($filter['acquisition_projet_groupe_equipement_id'])) {
            $where .= ' and exists (select 1 from acquisitions_projets_groupes_equipements_fiches apgef where acquisition_projet_groupe_equipement_id = ? and apgef.fiche_id = fiche.id)';
            $data[] = (int)($filter['acquisition_projet_groupe_equipement_id']);
        }

		if (isset($filter['extract_is_orphelin'])) {
			$sql = "select count(*) from batiments_niveaux where ouvrage_id = ?";
			$nb_batiments_niveaux = $db->getOne($sql, array(getOuvrageId()));
			if (intval($nb_batiments_niveaux) > 0) {
				$extra_select .= ", case when fiche.batiment_niveau_element_id is null and fiche.fiche_sous_type_id = 2000002 and fiche.is_sans_structure_hierarchique = 0 then 1 else 0 end as is_orphelin";
			} else {
				$extra_select .= ", 0 as is_orphelin";
			}
		}

		if (isset($filter['is_orphelin'])) {
			$sql = "select count(*) from batiments_niveaux where ouvrage_id = ?";
			$nb_batiments_niveaux = $db->getOne($sql, array(getOuvrageId()));
			if (intval($nb_batiments_niveaux) > 0) {
				$where .= ' and fiche.batiment_niveau_element_id is null and fiche.fiche_sous_type_id = 2000002 and fiche.is_sans_structure_hierarchique = 0';
			}
		}

		if (isset($filter['extract_code_parent'])) {
			$extra_select .= ", case when fiche_parent.code is not null then fiche_parent.code else 
                                    case when fiche_local_parent.code is not null and fiche.fiche_sous_type_id = 2000002 then fiche_local_parent.code else fiche.code end
                                end as code_parent";
			$joins_filtre['left_fiche_parent'] = 'left join fiches as fiche_parent on fiche_parent.id = fiche.fiche_parent_id';
			$joins_filtre['left_fiche_local_parent'] = 'left join fiches as fiche_local_parent on fiche_local_parent.id = fiche.fiche_local_id';
		}

		if (isset($filter['extract_code_destination'])) {
			$extra_select .= ", fiche_local_destination.code as code_destination";
			$joins_filtre[] = 'left join fiches as fiche_local_destination on fiche_local_destination.id = fiche.fiche_local_destination_id';
		}

		if (isset($filter['extract_code_source'])) {
            $extra_select .= ", fiche_local_source.code as code_source";
            $joins_filtre[] = 'left join fiches as fiche_local_source on fiche_local_source.id = fiche.fiche_local_id';
        }

		if (isset($filter['extract_gestion_porte_type'])) {
            $extra_select .= ", gpt.nom as gestion_porte_type";
            $joins_filtre[] = 'left join gestions_portes_types as gpt on gpt.id = fiche.gestion_porte_type_id';
        }

		if (isset($filter['force_ordre'])) {
			$orderBy = $filter['force_ordre'];
		}

		if ($orderBy == 'code_ordre') {
			$extra_select .= ", case when fiche_parent.code is not null then concat(fiche_parent.code, '!', fiche.code) else 
                                    case when fiche_local_parent.code is not null and fiche.fiche_sous_type_id = 2000002 then concat(fiche_local_parent.code, '!', fiche.code) else fiche.code end
                                end as code_ordre";
			if (!isset($joins_filtre['left_fiche_parent'])) {
				$joins_filtre['left_fiche_parent'] = 'left join fiches as fiche_parent on fiche_parent.id = fiche.fiche_parent_id';
			}
			if (!isset($joins_filtre['left_fiche_local_parent'])) {
				$joins_filtre['left_fiche_local_parent'] = 'left join fiches as fiche_local_parent on fiche_local_parent.id = fiche.fiche_local_id';
			}
		} else {
			$extra_select .= ", fiches_types.nom_$Langue as fiche_type";
			$joins_filtre[] = 'join fiches_types on fiches_types.id = fiche.fiche_type_id';

			$extra_select .= ", fiches_sous_types.nom_$Langue as fiche_sous_type";
			$joins_filtre[] = "join fiches_sous_types on fiches_sous_types.id = fiche.fiche_sous_type_id";

			$extra_select .= ", routes.nom as route";
			$joins_filtre[] = 'left join routes on routes.id = fiche.route_id';

			$extra_select .= ", batiments.nom as batiment";
			$joins_filtre[] = 'left join batiments on batiments.id = fiche.batiment_id';
		}

		if (isset($filter['get_generaux_locaux_data'])) {
			$extra_select .= ", generaux_locaux.utilite";
			$joins_filtre[] = 'left join generaux_locaux on generaux_locaux.fiche_id = fiche.id';
        }

        if (isset($filter['get_nb_archives'])) {
            $extra_select .= ", (select count(*) as nb from fiches_logs where fiche_id = fiche.id and formulaire_id = (select id from formulaires where nom = '" . $filter['get_nb_archives'] . "') and code = 'updateFicheForm' and token_archive is not null) as nb_archives";
        }

        if (isset($filter['get_nb_lignesForm'])) {
            $extra_select .= ", (select count(*) as nb from `" . $filter['get_nb_lignesForm'] . "` where fiche_id = fiche.id) as nb_lignesForm";
        }

        if (isset($filter['get_generaux_groupes_controles_data'])) {
            $extra_select .= ", generaux_groupes_controles.utilite";
            $joins_filtre[] = 'left join generaux_groupes_controles on generaux_groupes_controles.fiche_id = fiche.id';
        }

        if (isset($filter['equipementAVerifierVerouille'])) {
            $extra_select .= ", equipement_bio_medicaux_type_id";
            $joins_filtre[] = "left join equipements_bio_medicaux ebm on ebm.fiche_id = fiche.id and equipement_bio_medicaux_type_id = '" . $filter['equipementAVerifierVerouille'] . "'";

            $extra_select .= ', case when depot1.ts_verrouille is not null then 1 else 0 end as isVerrouilleDepot1, case when depot2.ts_verrouille is not null then 1 else 0 end as isVerrouilleDepot2, case when depot3.ts_verrouille is not null then 1 else 0 end as isVerrouilleDepot3, case when depot4.ts_verrouille is not null then 1 else 0 end as isVerrouilleDepot4, case when depot5.ts_verrouille is not null then 1 else 0 end as isVerrouilleDepot5';
            $extra_select .= ', case when depot1.ts_photo_j0 is not null then depot1.id else 0 end as isPhotoJ0Depot1, case when depot2.ts_photo_j0 is not null then depot2.id else 0 end as isPhotoJ0Depot2, case when depot3.ts_photo_j0 is not null then depot3.id else 0 end as isPhotoJ0Depot3, case when depot4.ts_photo_j0 is not null then depot4.id else 0 end as isPhotoJ0Depot4, case when depot5.ts_photo_j0 is not null then depot5.id else 0 end as isPhotoJ0Depot5';
            $extra_select .= ', depot1.id as depot_id1, depot2.id as depot_id2, depot3.id as depot_id3, depot4.id as depot_id4, depot5.id as depot_id5';
            for ($iterDepot = 1; $iterDepot <= 5; $iterDepot++) {
                $joins_filtre[] = 'left join financements_projets_depots as depot' . $iterDepot . ' on depot' . $iterDepot . '.id = ebm.depot_source' . $iterDepot . '_id';
            }
        }

        if (isset($filter['extraireGabaritAssocie'])) {
            $extra_select .= ', gabarit.code as gabarit_associe';
            $joins_filtre[] = 'left join fiches_gabarits fg on fg.fiche_id = fiche.id';
            $joins_filtre[] = 'left join fiches as gabarit on gabarit.id = fg.gabarit_id';
        }

		$sql = "select distinct fiche.*, bne.nom as batiment_niveau_element, statut.nom as fiche_statut, case when statut.id = 999999 then 1 else 0 end as is_statut_desactive
                    $extra_select
                FROM fiches as fiche 
                left join batiments_niveaux_elements bne on bne.id = fiche.batiment_niveau_element_id
                left join fiches_statuts statut on fiche.fiche_statut_id = statut.id
                " . dm_implode("\n", $joins_filtre) . "
                where true $where
                order by $orderBy"
			. (isset($filter['limit']) ? " limit 0, " . $filter['limit'] : "");
            $rows = $db->getAll($sql, $data);

		$liste = array();
		foreach ($rows as $i => $row) {
			$key_ligne = (int)($row['id']);
			if (isset($filter['form_for_ligne'])) {
				$key_ligne = $i;
			} else if (isset($filter['by_code'])) {
				$key_ligne = $row['code'];
			}

			if (isset($filter['for_json'])) {
				$left_padding = '';
				$txt_supp = '';
				if (dm_strlen($row['code_alternatif_2']) > 0) {
					$txt_supp = ' ('.$row['code_alternatif_2'].')';
				}
				if (dm_strlen($row['ts_delete']) > 0) {
					$txt_supp .= $row['is_gabarit'] == 1 ? txt(' (gabarit supprimé)') : txt(' (fiche supprimée)');
				} else if (dm_strlen($row['ts_archive']) > 0) {
					$txt_supp .= $row['is_gabarit'] == 1 ? txt(' (gabarit archivé)') : txt(' (fiche archivée)');
				}
                $verrouille = false;
                if (isset($filter['equipementAVerifierVerouille'])) {
                    list($canInactivate, $equipement_non_editable, $canDelete) = EquipementsBioMedicaux::getDroitEditionEquipementStatic($row, ($row['equipement_bio_medicaux_type_id'] ?? 0));
                    if ($equipement_non_editable) {
                        $verrouille = true;
                    }
                }
				$liste[$i] = array('id' => $row['id'], 'text' => $left_padding . $row['code'] . $txt_supp, 'batiment_niveau_element' => $row['batiment_niveau_element'], 'verrouille' => $verrouille);
			} else {
				$liste[$key_ligne] = new self((int)($row['id']), $row);
			}

			unset($rows[$i]);
		}

		return $liste;
	}

    public static function getListeByBatimentSousType($filter = [], $orderBy = 'fiche.code')
    {
        $listeFiches = self::getListe($filter, $orderBy);

        $listeFichesByBatimentSousType = [];
        foreach ($listeFiches as $fiche) {
            $batiment_id = $fiche->batiment_id ?? -1;
            if (!isset($listeFichesByBatimentSousType[$batiment_id])) {
                $listeFichesByBatimentSousType[$batiment_id] = [];
            }
            if (!isset($listeFichesByBatimentSousType[$batiment_id][$fiche->fiche_sous_type_id])) {
                $listeFichesByBatimentSousType[$batiment_id][$fiche->fiche_sous_type_id] = [];
            }
            $listeFichesByBatimentSousType[$batiment_id][$fiche->fiche_sous_type_id][$fiche->id] = $fiche;
        }
        return $listeFichesByBatimentSousType;
    }

	/**
	 * @param array $filter
	 * @param int|int[] $selected
	 * @param bool $includeEmpty
	 */
	public static function getOptions($filter = [], $selected = [], $includeEmpty = false)
	{
		if ($includeEmpty) {
			?>
            <option value=""> </option>
			<?php
		}

        if (!is_array($selected)) {
            $selected = [$selected];
        }

		$rows = self::getListe($filter);
		foreach ($rows as $i => $row) {
			$txt_supp = '';
            if (dm_strlen($row->code_alternatif_2) > 0) {
                $txt_supp = ' ('.$row->code_alternatif_2.')';
            }
			if (dm_strlen($row->ts_delete) > 0) {
				$txt_supp .= $row->is_gabarit == 1 ? txt(' (gabarit supprimé)') : txt(' (fiche supprimée)');
			} else if (dm_strlen($row->ts_archive) > 0) {
				$txt_supp .= $row->is_gabarit == 1 ? txt(' (gabarit archivé)') : txt(' (fiche archivée)');
			}
			$sel = in_array($row->id, $selected) ? ' selected="selected"' : '';
			?>
            <option value="<?= $row->id ?>"<?= $sel ?> <?= (dm_strlen($row->ts_delete) > 0 ? 'style="color: red;"' : '') . (dm_strlen($row->ts_archive) > 0 ? 'style="color: orange;"' : '') ?>>
				<?= $row->code . $txt_supp ?></option>
			<?php
		}
	}

	/**
	 * @param array $filter
	 * @param int $selected
	 * @param bool $includeEmpty
	 * @return false|string
	 */
	public static function getJsonOptions($filter = array(), $includeEmpty = false)
	{
		$filter['for_json'] = 1;
		$json_array = array();
		if ($includeEmpty) {
			$json_array[] = array('id' => '', 'text' => '');
		}
		$json_array = array_merge($json_array, self::getListe($filter));

		if (isset($filter['fiches_to_preserve'])) {
			$filter['no_filtre_global'] = 1;
			$filter['ids'] = $filter['fiches_to_preserve'];
			$json_array_2 = self::getListe($filter);
			$json_array = array_merge($json_array_2, $json_array);
		}
		return json_encode($json_array);
	}

	/**
	 * @param int $iteration
	 * @param bool $only_champs_denormalises
	 */
	public function getEditorHTML($iteration = 0, $only_champs_denormalises = false)
	{
		?>
        <input id="this_fiche_id" type="hidden" value="<?= $this->id ?>"/>

        <section style="display: <?= (isset($_GET['from_gestion_portes']) ? 'none' : '') ?>;">
            <label for="fiche_type_id_fromFiche" style="color: red;"><?= txt('Type') ?></label>
            <div>
				<?
				if ($this->id == 0) {
					?>
                    <select id="fiche_type_id_fromFiche"
                            name="fiche_type_id" <?= (isset($_GET['add_sub_room']) || isset($_GET['add_porte']) || isset($_GET['import_id']) || isset($_GET['from_gestion_portes']) ? ' class="readonly"' : '') ?>>
						<?php FicheType::getOptions(array(), $this->fiche_type_id, true) ?>
                    </select>
					<?
				} else {
					print $this->fiche_type;
					?>
                    <input type="hidden" id="fiche_type_id_fromFiche" name="fiche_type_id"
                           value="<?= $this->fiche_type_id ?>"/>
					<?
				}
				?>
            </div>
        </section>
		<?
		if (!isset($this->fiche_type_id)) {
			$this->fiche_type_id = 0;
		}

		$display_for_route = "";
		$display_not_for_route = "";
		if ($this->fiche_sous_type_id == 0) {
			$display_for_route = "display: none;";
			$display_not_for_route = "display: none;";
		} else if ($this->fiche_sous_type_id == 2000015) {
			$display_for_route = "";
			$display_not_for_route = "display: none;";
		} else {
			$display_for_route = "display: none;";
			$display_not_for_route = "";
		}
		?>
        <section style="display: <?= (isset($_GET['from_gestion_portes']) ? 'none' : '') ?>;">
            <label for="fiche_sous_type_id_fromFiche" style="color: red;"><?= txt('Sous-Type') ?></label>
            <div>
				<?
				if ($this->id == 0) {
					?>
                    <select id="fiche_sous_type_id_fromFiche"
                            name="fiche_sous_type_id" <?= ($this->fiche_sous_type_id > 0 && (isset($_GET['add_sub_room']) || isset($_GET['add_porte']) || isset($_GET['import_id']) || isset($_GET['from_gestion_portes'])) ? ' class="readonly"' : '') ?>>
						<?php FicheSousType::getOptions(array('fiche_type_id' => $this->fiche_type_id), $this->fiche_sous_type_id, true) ?>
                    </select>
					<?
				} else {
					print $this->fiche_sous_type;
					?>
                    <input type="hidden" id="fiche_sous_type_id_fromFiche" name="fiche_sous_type_id"
                           value="<?= $this->fiche_sous_type_id ?>"/>
					<?
				}
				?>
            </div>
        </section>
		<?
		$force_readonly = isset($_GET['import_id']) ? ' force_readonly' : '';
		?>
        <section>
            <label for="code" style="color: red;"><?= txt('Code') ?></label>
            <div>
                <input id="code" type="text" name="code" value="<?= ($this->code) ?>" maxlength="45" style="width: 255px;"
                       class="<?= ($this->fiche_sous_type_id == 2000015 || isset($_GET['import_id']) ? 'readonly' : '') . $force_readonly ?>"/>
            </div>
        </section>

        <section style="display: <?= (isset($_GET['from_gestion_portes']) && $this->fiche_sous_type_id == 2000013 ? 'none' : '') ?>;">
            <label for="code"><?= txt('Identifiant unique Revit') ?></label>
            <div>
                <?= $this->revit_unique_id ?>
            </div>
        </section>

        <section class="bloc_for_route" style="<?= $display_for_route ?>">
            <label for="route_id"><?= txt('Route') ?></label>
            <div>
                <?
                if ($this->id == 0) {
                    ?>
                    <select id="route_id" name="route_id">
                        <?php Routes::getOptions(array('ouvrage_id' => getOuvrageId()), $this->route_id, true) ?>
                    </select>
                    <?
                } else {
                    if ($this->route_id > 0) {
                        $route = new Routes($this->route_id);
                    }
                    print $this->route;
                    ?>
                    <input type="hidden" id="route_id" name="route_id" value="<?= $this->route_id ?>"/>
                    <input type="hidden" id="route_option_<?= $this->route_id ?>"
                           data-code="<?= (isset($route) ? $route->code : '') ?>"/>
                    <?
                }
                ?>
            </div>
        </section>

        <? if (!isset($_GET['from_gestion_portes'])) { ?>
            <section class="bloc_not_for_route" style="<?= $display_not_for_route ?>">
                <label for="batiment_id"><?= txt('Bâtiment') ?></label>
                <div>
                    <select id="batiment_id"
                            name="batiment_id" <?= (isset($_GET['add_sub_room']) || isset($_GET['add_composante']) || isset($_GET['add_porte']) ? ' class="readonly"' : '') ?>>
                        <?php Batiment::getOptions(array('ouvrage_id' => getOuvrageId()), $this->batiment_id, true) ?>
                    </select>
                </div>
            </section>

            <section class="bloc_for_route" style="<?= $display_for_route ?>">
                <label for="chainage"><?= txt('Chaînage') ?></label>
                <div>
                    <select id="chainage" name="chainage">
                        <?
                        if ($this->route_id > 0) {
                            $route = new Routes($this->route_id);
                            $route->getOptionsChainage($this->chainage_debut, $this->chainage_fin);
                        }
                        ?>
                    </select>
                    <input id="chainage_debut" type="hidden" id="chainage_debut" name="chainage_debut"
                           value="<?= $this->chainage_debut ?>"/>
                    <input id="chainage_fin" type="hidden" id="chainage_fin" name="chainage_fin"
                           value="<?= $this->chainage_fin ?>"/>
                </div>
            </section>
            <section class="bloc_not_for_route" style="<?= $display_not_for_route ?>">
                <label for="numero_lot"><?= txt('Numéro de lot') ?></label>
                <div>
                    <input id="numero_lot" type="text" name="numero_lot" value="<?= $this->numero_lot ?>" maxlength="200"
                           style="width: 125px;"/>
                </div>
            </section>
        <?
        }
		if (($this->is_gabarit == 1) || (isset($_GET['from_gestion_portes']) && $this->fiche_sous_type_id == 2000013)) {
			?>
            <section class="is_gabarit">
                <label for="description"><?= txt('Description') ?></label>
                <div>
                    <input id="description" type="text" name="description" value="<?= ($this->description) ?>" maxlength="255" style="width: 255px;"/>
                </div>
            </section>

            <input id="is_gabarit" type="hidden" name="is_gabarit" value="<?= (int)$this->is_gabarit ?>"/>
			<?
		}
		?>
        <section class="bloc_not_for_route not_gabarit" <?= ($this->is_gabarit == 1 || in_array($this->fiche_sous_type_id, array(2000015, 2000013, 2000014, 0)) ? ' style="display: none;"' : '') ?>>
            <label for="code_alternatif"><?= txt('Code alternatif') ?></label>
            <div>
                <input id="code_alternatif" type="text" name="code_alternatif" value="<?= ($this->code_alternatif) ?>"
                       maxlength="45" style="width: 255px;"/>
            </div>
        </section>

        <section class="bloc_not_for_route not_gabarit" <?= ($this->is_gabarit == 1 || in_array($this->fiche_sous_type_id, array(2000015, 2000013, 2000014, 0)) ? ' style="display: none;"' : '') ?>>
            <label for="code_alternatif_2"><?= txt('Code alternatif 2') ?></label>
            <div>
                <input id="code_alternatif_2" type="text" name="code_alternatif_2" value="<?= ($this->code_alternatif_2) ?>"
                       maxlength="45" style="width: 255px;"/>
            </div>
        </section>

        <? if (!isset($_GET['from_gestion_portes'])) { ?>
            <section
                    class="bloc_not_for_route not_gabarit" <?= ($this->is_gabarit == 1 || in_array($this->fiche_sous_type_id, array(2000015, 0)) ? ' style="display: none;"' : '') ?>>
                <label for="id_revit"><?= txt('ID Externe 1') ?></label>
                <div>
                    <input id="id_revit" type="text" name="id_revit" value="<?= $this->id_revit ?>" maxlength="20"
                           style="width: 255px;"/>
                </div>
            </section>

            <section
                    class="bloc_not_for_route not_gabarit" <?= ($this->is_gabarit == 1 || in_array($this->fiche_sous_type_id, array(2000015, 0)) ? ' style="display: none;"' : '') ?>>
                <label for="id_codebook"><?= txt('ID Externe 2') ?></label>
                <div>
                    <input id="id_codebook" type="text" name="id_codebook" value="<?= $this->id_codebook ?>" maxlength="20"
                           style="width: 255px;"/>
                </div>
            </section>
            <?
        }
		if ($this->id > 0 && isMaster()) {
			?>
            <section>
                <label for="projet_id"><?= txt('Projet') ?></label>
                <div>
                    <select id="projet_id" name="projet_id">
						<?php Projets::getOptions(array('all_autres_projets' => 1), getProjetId(), true) ?>
                    </select>
                </div>
            </section>
			<?
		}

		if ($this->is_gabarit <> 1 && isMaster()) {
			?>
            <section class="bloc_not_for_route" id="batiment_niveau_element_id_section"
                     style="<?= ($this->fiche_sous_type_id == 2000002 && $this->is_gabarit <> 1 && !in_array($this->fiche_sous_type_id, array(2000015, 0)) ? '' : 'display: none;') ?>">
                <label for="batiment_niveau_element_id"><?= txt('Structure hiérarchique du local') ?></label>
                <div>
                    <select name="batiment_niveau_element_id"<?= (!isset($this->ouvrage_id) || $this->ouvrage_id == 0 ? ' disabled="disabled"' : '') ?>
                            style="max-width: 600px!important;">
                        <option value=""> </option>
						<?
						$batiments_niveaux_elements = BatimentsNiveaux::getAllLevelsFromBatiment($this->ouvrage_id);
						foreach ($batiments_niveaux_elements as $element) {
							?>
                            <option value="<?= $element['id'] ?>"<?= ($element['id'] == $this->batiment_niveau_element_id || $element['code_local'] == $this->code ? ' selected="selected"' : '') ?>><?= $element['code'] ?></option>
							<?
						}
						?>
                    </select>
                </div>
            </section>
			<?
		}

		if ($this->fiche_sous_type_id == 2000014) {
			?>
            <section class="bloc_not_for_route" id="fiche_local_id_section">
                <label for="fiche_local_id"><?= (txt('Local (de)')) ?></label>
                <div>
                    <select id="fiche_local_id" name="fiche_local_id"
                            class="lazy_loaded_fiches <?= (isset($_GET['add_sub_room']) ? ' readonly' : '') ?>" <?= (isset($_GET['add_sub_room']) ? '' : 'disabled') ?>>
						<?php Fiches::getOptions(array('ids' => array($this->fiche_local_id), 'fiche_sous_type_id' => 2000002, 'fiche_local_id_null' => 1, 'excluded_fiche_id' => $this->id, 'no_filtre_global' => 1), $this->fiche_local_id, true) ?>
                    </select>
                </div>
            </section>
			<?
		} else if ($this->fiche_type_id <> 2000001 && $this->fiche_sous_type_id <> 2000013) {
			$sous_locaux = Fiches::getListe(array('fiche_local_id' => $this->id, 'fiche_sous_type_id' => 2000002, 'no_filtre_global' => 1));
			if (count($sous_locaux) == 0) {
				?>
                <section class="bloc_not_for_route" id="fiche_local_id_section"
                         style="<?= ($this->fiche_type_id <> 2000001 && !in_array($this->fiche_sous_type_id, array(2000015, 0)) ? '' : 'display: none;') ?>">
                    <label for="fiche_local_id"><?= ($this->fiche_sous_type_id == 2000002 ? txt('Local maître') : txt('Local d\'appartenance')) ?></label>
                    <div>
                        <select id="fiche_local_id" name="fiche_local_id"
                                class="lazy_loaded_fiches <?= (isset($_GET['add_sub_room']) ? ' readonly' : '') ?>" <?= (isset($_GET['add_sub_room']) ? '' : 'disabled') ?>>
							<?php Fiches::getOptions(array('ids' => array($this->fiche_local_id), 'fiche_sous_type_id' => 2000002, 'fiche_local_id_null' => 1, 'excluded_fiche_id' => $this->id, 'no_filtre_global' => 1), $this->fiche_local_id, true) ?>
                        </select>
                    </div>
                </section>
				<?
			}
		}
		?>
        <section class="bloc_not_for_route" id="fiche_local_destination_id_section"
                 style="<?= (in_array($this->fiche_sous_type_id, array(2000000, 2000001, 2000003, 2000006, 2000007, 2000008, 2000009, 2000011, 2000012, 2000016, 2000014)) ? '' : 'display: none;') ?>">
            <label for="fiche_local_destination_id"><?= (in_array($this->fiche_sous_type_id, array(2000000, 2000001, 2000003, 2000006, 2000007, 2000008, 2000009, 2000011, 2000012, 2000016)) ? txt('Local desservi par cet élément') : txt('Local (vers)')) ?></label>
            <div>
                <select id="fiche_local_destination_id" name="fiche_local_destination_id"
                        class="lazy_loaded_fiches <?= (isset($_GET['add_sub_room']) ? ' readonly' : '') ?>" <?= (isset($_GET['add_sub_room']) ? '' : 'disabled') ?>>
					<?php Fiches::getOptions(array('ids' => array($this->fiche_local_destination_id), 'fiche_sous_type_id' => 2000002, 'fiche_local_id_null' => 1, 'no_filtre_global' => 1), $this->fiche_local_destination_id, true) ?>
                </select>
            </div>
        </section>
		<?

		if ((!in_array($this->fiche_type_id, array(2000002)) && !in_array($this->fiche_sous_type_id, array(2000005)))) {
			?>
            <section class="bloc_not_for_route" id="fiche_parent_id_section" style="<?= $display_not_for_route ?>">
                <label for="fiche_parent_id"><?= txt('Équip./système d\'appartenance (fiche maître)') ?></label>
                <div>
                    <select id="fiche_parent_id" name="fiche_parent_id"
                            class="lazy_loaded_fiches <?= (isset($_GET['add_composante']) || isset($_GET['add_porte']) ? ' readonly' : '') ?>"
                            disabled>
						<?php Fiches::getOptions(array('ids' => array($this->fiche_parent_id), 'fiche_type_id_exclude' => 2000002, 'fiche_parent_id_null' => 1, 'no_filtre_global' => 1), $this->fiche_parent_id, true) ?>
                    </select>
                </div>
            </section>
			<?
		} else if (in_array($this->fiche_sous_type_id, array(2000014))) {
            if (isset($_GET['from_gestion_portes'])) {
                ?>
                <section class="bloc_not_for_route" id="gestion_porte_type_id_section">
                    <label for="gestion_porte_type_id"><?= txt('Type de porte') ?></label>
                    <div>
                        <select id="gestion_porte_type_id" name="gestion_porte_type_id">
                            <?php GestionsPortesTypes::getOptions([], $this->gestion_porte_type_id, true) ?>
                        </select>
                    </div>
                </section>

                <section class="bloc_not_for_route" id="fiche_parent_id_section">
                    <label for="fiche_parent_id"><?= txt('Groupe de quincaillerie') ?></label>
                    <div>
                        <select id="fiche_parent_id" name="fiche_parent_id"
                                class="lazy_loaded_fiches <?= (isset($_GET['add_composante']) || isset($_GET['add_porte']) ? ' readonly' : '') ?>" <?= (isset($_GET['add_composante']) || isset($_GET['add_porte']) ? '' : 'disabled') ?>>
                            <?php Fiches::getOptions(array('ids' => array($this->fiche_parent_id), 'fiche_sous_type_id' => 2000013, 'no_filtre_global' => 1), $this->fiche_parent_id, true) ?>
                        </select>
                    </div>
                </section>
                <?
            } else {
                ?>
                <section class="bloc_not_for_route" id="fiche_parent_id_section">
                    <label for="fiche_parent_id"><?= txt('Groupe de quincaillerie') ?></label>
                    <div>
                        <select id="fiche_parent_id" name="fiche_parent_id"
                                class="lazy_loaded_fiches <?= (isset($_GET['add_composante']) || isset($_GET['add_porte']) ? ' readonly' : '') ?>" <?= (isset($_GET['add_composante']) || isset($_GET['add_porte']) ? '' : 'disabled') ?>>
                            <?php Fiches::getOptions(array('ids' => array($this->fiche_parent_id), 'fiche_sous_type_id' => 2000013, 'no_filtre_global' => 1), $this->fiche_parent_id, true) ?>
                        </select>
                    </div>
                </section>

                <section class="bloc_not_for_route" id="fiche_local_controleur_id_section">
                    <label for="fiche_local_controleur_id"><?= (txt('Localisation du contrôleur')) ?></label>
                    <div>
                        <select id="fiche_local_controleur_id" name="fiche_local_controleur_id"
                                class="lazy_loaded_fiches <?= (isset($_GET['add_composante']) || isset($_GET['add_porte']) ? ' readonly' : '') ?>" <?= (isset($_GET['add_composante']) || isset($_GET['add_porte']) ? '' : 'disabled') ?>>
                            <?php Fiches::getOptions(array('ids' => array($this->fiche_local_id), 'fiche_sous_type_id' => 2000002, 'fiche_local_id_null' => 1, 'excluded_fiche_id' => $this->id, 'no_filtre_global' => 1), $this->fiche_local_controleur_id, true) ?>
                        </select>
                    </div>
                </section>
                <?
            }
		}

			?>
        <section class="is_gabarit" <?= ($this->is_gabarit == 1 ? '' : ' style="display: none;"') ?>>
            <label for="commentaire"><?= txt('Commentaire') ?></label>
            <div>
                <textarea id="commentaire" name="commentaire"
                          style="min-height: 100px;"><?= ($this->commentaire) ?></textarea>
            </div>
        </section>
        <? if (!isset($_GET['from_gestion_portes'])) { ?>
            <section>
                <label for="fiche_statut_id"><?= txt('Statut') ?></label>
                <div>
                    <select id="fiche_statut_id" name="fiche_statut_id">
                        <?php FichesStatuts::getOptions(array(), $this->fiche_statut_id, true) ?>
                    </select>
                </div>
            </section>
        <? } ?>

        <section>
            <label for="pdf"><?= txt('PDF') ?></label>
            <div>
                <input type="file" id="pdf" name="pdf" accept=".pdf" />
                <br />
                <?
                if ($this->id > 0) {
                    $documentPDFs = DocumentsListesElements::getListe([
                        'nom_table' => 'fiches',
                        'liste_item_id' => $this->id,
                        'is_image_principale' => 0,
                        'mime_type' => 'application/pdf'
                    ]);

                    foreach ($documentPDFs as $documentPDF) {
                    ?>
                        <div id="document_pdf_<?= $documentPDF->id ?>">
                            <a href="index.php?action=download_document&id=<?= $documentPDF->id ?>" target="_blank"
                               style="position: relative; top: 2px;"><?= $documentPDF->nom ?></a>
                            <input type="hidden" name="document_liste_element_id" value="<?= $documentPDF->id ?>"  />
                            <a href="javascript: void(0);" style="position: relative; top: 8px;" onclick="$('#document_pdf_<?= $documentPDF->id ?>').remove()">
                                <img src="css/images/icons/dark/trashcan.png" /></a>
                        </div>
                    <?
                    }
                }
                ?>
            </div>
        </section>
		<?
	}

	/**
	 * @param $data
	 * @param $id
	 * @return bool
	 */
	public static function validate($data, $id)
	{
		global $db;

		$valide = true;
		foreach ($data as $colomnName => $values) {
			$values = is_array($values) ? $values : array($values);
			foreach ($values as $i => $value) {
				if ($colomnName == 'code') {
					if (dm_strlen($value) == 0) {
						setErreur(txt('Le code est obligatoire.'));
						$valide = false;
					} else {
						$sql = "select fiches.code
                                from fiches
                                left join batiments on batiments.id = fiches.batiment_id
                                left join routes on routes.id = fiches.route_id
                                where fiches.code = ? and fiches.id <> ? and fiches.ouvrage_id = ?";
						$code = $db->getOne($sql, array($value, $id, getOuvrageId()));

						if (dm_strlen($code) > 0) {
							if (isset($_GET['import_id'])) {
								setErreur(dm_str_replace('%%', '<span style="font-weight: bold;">' . $value . '</span>', txt('AJOUT DE FICHE IMPOSSIBLE!') . '<br/>' . txt('Le code %% existe déjà dans l\'ouvrage actuel. Il est écrit de la manière suivante: ', false) . $code . '<br>' . txt('Voir à corriger le code de la fiche manuellement ou régulariser l\'erreur à la source du fichier importé.', false)));
								$valide = false;
							} else {
								setErreur(dm_str_replace('%%', '<span style="font-weight: bold;">' . $value . '</span>', txt('Le code %% existe déjà dans l\'ouvrage actuel', false)));
								$valide = false;
							}
						}
					}
				} else if ($colomnName == 'code_alternatif') {
					if (dm_strlen($value) > 0) {
						$sql = "select fiches.* 
                                from fiches 
                                left join batiments on batiments.id = fiches.batiment_id
                                left join routes on routes.id = fiches.route_id
                                where code_alternatif = ? and fiches.id <> ? and fiches.ouvrage_id = ? limit 1";
						$fiche = $db->getRow($sql, array($value, $id, getOuvrageId()));

						if (isset($fiche) && is_array($fiche)) {
							$message = txt('Le code alternatif ') . '<span style="font-weight: bold;">' . $value . '</span>' . txt(' existe déjà dans l\'ouvrage actuel au niveau de la fiche suivante: ') . '<span style="font-weight: bold;">' . $fiche['code'] . '</span>';
							if (isset($_GET['import_data'])) {
								$import_url = 'index.php?section=fiche&module=comparaison&import_data=1&fiche_id_delete_code_alternatif=' . (int)($fiche['id']) . '&fiche_code=' . urlencode($_GET['fiche_code']) . '&ligne_code=' . (isset($_GET['ligne_code']) ? $_GET['ligne_code'] : '') . '&formulaire=' . $_GET['formulaire'] . '&nom_champ=' . $_GET['nom_champ'] . '&valeur=' . urlencode($_GET['valeur']) . '&import_id=' . (int)($_GET['import_id']);
								$message .= '<br />' . txt('Est-ce que vous désirez l\'effacer de la fiche ') . '<span style="font-weight: bold;">' . $fiche['code'] . '</span>' . txt(' pour l\'importer dans la fiche ') . '<span style="font-weight: bold;">' . $_GET['fiche_code'] . '</span>' . '?';
								$message .= '&nbsp;&nbsp;&nbsp;&nbsp;<a href="' . $import_url . '" style="font-size: 16px; color: red; text-decoration: underline!important;font-weight: bold!important;">' . txt('Oui') . '</a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<a href="index.php?section=fiche&module=comparaison&import_id=' . (int)($_GET['import_id']) . '" style="font-size: 16px; color: red;text-decoration: underline !important;  font-weight: bold!important;">' . txt('Non') . '</a>';
							}
							setErreur($message);
							$valide = false;
						}
					}
				} else if ($colomnName == 'id_revit') {
					if (dm_strlen($value) > 0) {
						$sql = "select fiches.code 
                                from fiches 
                                left join batiments on batiments.id = fiches.batiment_id
                                left join routes on routes.id = fiches.route_id
                                where id_revit = ? and fiches.id <> ? and fiches.ouvrage_id = ?";
						$codes = $db->getCol($sql, array($value, $id, getOuvrageId()));

						if (count($codes) > 0) {
							setErreur(dm_str_replace('%%', '<span style="font-weight: bold;">' . $value . '</span>', txt('Le code externe %% existe déjà dans l\'ouvrage actuel au niveau de la ou les fiche(s) suivante(s): ', false) . '<span style="font-weight: bold;">' . dm_implode(', ', $codes) . '</span>'));
							$valide = false;
						}
					}
				} else if ($colomnName == 'fiche_type_id') {
					if (dm_strlen($value) == 0) {
						setErreur(txt('Le type est obligatoire.'));
						$valide = false;
					}
				} else if ($colomnName == 'fiche_sous_type_id') {
					if (dm_strlen($value) == 0) {
						setErreur(txt('Le sous-type est obligatoire.'));
						$valide = false;
					}
				}
				/*                else if($colomnName == 'batiment_id')
								{
									if(dm_strlen($value) == 0 && ((!is_array($data['fiche_sous_type_id']) && $data['fiche_sous_type_id'] <> 2000015) || (is_array($data['fiche_sous_type_id']) && $data['fiche_sous_type_id'][0] <> 2000015)))
									{
										setErreur(txt('Le bâtiment est obligatoire.'));
										$valide = false;
									}
								}
								else if($colomnName == 'route_id')
								{
									if(dm_strlen($value) == 0 && ((!is_array($data['fiche_sous_type_id']) && $data['fiche_sous_type_id'] == 2000015) || (is_array($data['fiche_sous_type_id']) && $data['fiche_sous_type_id'][0] == 2000015)))
									{
										setErreur(txt('La route est obligatoire.'));
										$valide = false;
									}
								}
							  else if($colomnName == 'fiche_parent_id')
								{
									if(dm_strlen($value) > 0 && $data['fiche_type_id'] == 2000002)
									{
										setErreur(txt('L\'ajout d\'un local à titre de composante n\'est pas permis.'));
										$valide = false;
									}
								}
				 */
			}
		}

        if (isset($_FILES['pdf']) && is_uploaded_file($_FILES['pdf']['tmp_name'])) {
            $violations = DocumentsListesElements::validateFile($_FILES['pdf']);
            if (count($violations) > 0) {
                foreach ($violations as $violation) {
                    setErreur($violation->getMessage());
                }
            }
        }

		return $valide;
	}

	/**
	 * @param $data
	 * @return int|string
	 */
	public static function cloner($data)
	{
		global $db, $db_name;

		$new_id = 0;
		if (Fiches::validate($data, 0)) {
			$db->autocommit(false);

			$fiche_id = $data['fiche_id'];
			$fiche = new Fiches($fiche_id);

			$code_fiche_source = $fiche->code;

			$fiche->code = $data['code'];
			$fiche->batiment_id = $data['batiment_id'];
			$fiche->route_id = $data['route_id'];
			$fiche->chainage_debut = $data['chainage_debut'];
			$fiche->chainage_fin = $data['chainage_fin'];

			if (isset($data['fiche_local_id'])) {
				$fiche->fiche_local_id = $data['fiche_local_id'];
			}

			if (isset($data['fiche_local_destination_id'])) {
				$fiche->fiche_local_destination_id = $data['fiche_local_destination_id'];
			}

			if (isset($data['fiche_local_controleur_id'])) {
				$fiche->fiche_local_controleur_id = $data['fiche_local_controleur_id'];
			}

			if (isset($data['fiche_parent_id'])) {
				$fiche->fiche_parent_id = $data['fiche_parent_id'];
			}

			$insertStatementCol = array();
			$insertStatementVal = array();
			$data_to_save = array();

			$insertStatementCol[] = '`code`';
			$insertStatementVal[] = '?';
			$data_to_save[] = $fiche->code;

			$insertStatementCol[] = '`ouvrage_id`';
			$insertStatementVal[] = '?';
			$data_to_save[] = $fiche->ouvrage_id;

			if (isset($fiche->batiment_id) && $fiche->batiment_id > 0) {
				$insertStatementCol[] = '`batiment_id`';
				$insertStatementVal[] = '?';
				$data_to_save[] = $fiche->batiment_id;
			}

			if (isset($fiche->route_id) && $fiche->route_id > 0) {
				$insertStatementCol[] = '`route_id`';
				$insertStatementVal[] = '?';
				$data_to_save[] = $fiche->route_id;
			}

			if (isset($fiche->chainage_debut) && $fiche->chainage_debut > 0) {
				$insertStatementCol[] = '`chainage_debut`';
				$insertStatementVal[] = '?';
				$data_to_save[] = $fiche->chainage_debut;
			}

			if (isset($fiche->chainage_fin) && $fiche->chainage_fin > 0) {
				$insertStatementCol[] = '`chainage_fin`';
				$insertStatementVal[] = '?';
				$data_to_save[] = $fiche->chainage_fin;
			}

			$insertStatementCol[] = '`fiche_type_id`';
			$insertStatementVal[] = '?';
			$data_to_save[] = $fiche->fiche_type_id;

			$insertStatementCol[] = '`fiche_sous_type_id`';
			$insertStatementVal[] = '?';
			$data_to_save[] = $fiche->fiche_sous_type_id;

			$insertStatementCol[] = '`is_gabarit`';
			$insertStatementVal[] = '?';
			$data_to_save[] = $fiche->is_gabarit;

			if (isset($fiche->fiche_local_id) && $fiche->fiche_local_id > 0) {
				$insertStatementCol[] = '`fiche_local_id`';
				$insertStatementVal[] = '?';
				$data_to_save[] = $fiche->fiche_local_id;
			}

			if (isset($fiche->fiche_local_destination_id) && $fiche->fiche_local_destination_id > 0) {
				$insertStatementCol[] = '`fiche_local_destination_id`';
				$insertStatementVal[] = '?';
				$data_to_save[] = $fiche->fiche_local_destination_id;
			}

			if (isset($fiche->fiche_local_controleur_id) && $fiche->fiche_local_controleur_id > 0) {
				$insertStatementCol[] = '`fiche_local_controleur_id`';
				$insertStatementVal[] = '?';
				$data_to_save[] = $fiche->fiche_local_controleur_id;
			}

			if (isset($fiche->fiche_parent_id) && $fiche->fiche_parent_id > 0) {
				$insertStatementCol[] = '`fiche_parent_id`';
				$insertStatementVal[] = '?';
				$data_to_save[] = $fiche->fiche_parent_id;
			}

			if (isset($data['numero_lot']) && dm_strlen($data['numero_lot']) > 0) {
				$insertStatementCol[] = '`numero_lot`';
				$insertStatementVal[] = '?';
				$data_to_save[] = $data['numero_lot'];
			}

			if (isset($data['code_alternatif']) && dm_strlen($data['code_alternatif']) > 0) {
				$insertStatementCol[] = '`code_alternatif`';
				$insertStatementVal[] = '?';
				$data_to_save[] = $data['code_alternatif'];
			}

			if (isset($data['code_alternatif_2']) && dm_strlen($data['code_alternatif_2']) > 0) {
				$insertStatementCol[] = '`code_alternatif_2`';
				$insertStatementVal[] = '?';
				$data_to_save[] = $data['code_alternatif_2'];
			}

			if (isset($data['id_revit']) && dm_strlen($data['id_revit']) > 0) {
				$insertStatementCol[] = '`id_revit`';
				$insertStatementVal[] = '?';
				$data_to_save[] = $data['id_revit'];
			}

			if (isset($data['id_codebook']) && dm_strlen($data['id_codebook']) > 0) {
				$insertStatementCol[] = '`id_codebook`';
				$insertStatementVal[] = '?';
				$data_to_save[] = $data['id_codebook'];
			}

			if (isset($data['batiment_niveau_element_id']) && (int)($data['batiment_niveau_element_id']) > 0) {
				$insertStatementCol[] = '`batiment_niveau_element_id`';
				$insertStatementVal[] = '?';
				$data_to_save[] = $data['batiment_niveau_element_id'];
			}

			$sql = "INSERT INTO `fiches` (" . dm_implode(", ", $insertStatementCol) . ") VALUES (" . dm_implode(", ", $insertStatementVal) . ")";
			$db->query($sql, $data_to_save);

			$new_id = $db->lastInsertId();

			$classNameGeneral = Fiches::getFormulaireGeneral($fiche->fiche_type_id, $fiche->fiche_sous_type_id);

			if (isset($data['batiment_niveau_element_id']) && (int)($data['batiment_niveau_element_id']) > 0) {
				$sql = "insert into fiches_batiment_niveaux_elements 
                        select ?, batiment_niveau_id, batiment_niveau_element_id
                        from fiches_batiment_niveaux_elements
                        where fiche_id = ?";
				$db->query($sql, array($new_id, $fiche->id));
			}

			$sql = "INSERT INTO `fiches_disciplines` (fiche_id, discipline_id) 
                    select ?, discipline_id from fiches_disciplines where fiche_id = ?";
			$db->query($sql, array($new_id, $fiche->id));

			$sql = "select * from formulaires";
			$formulaires = $db->getAll($sql);

			foreach ($formulaires as $i => $formulaire) {
				$className = $formulaire['nom'];
				if ($className == $classNameGeneral) {
					$data_to_add = array();
					$data_to_add['formulaire'] = $classNameGeneral;
					$data_to_add['fiche_id'] = $new_id;

					$obj_tmp = $classNameGeneral::getOne($fiche_id);
					$metas = $classNameGeneral::getFlatChampsSaisieMetaData();
					foreach($metas as $key => $meta)
					{
						$champ_id_tmp = $meta['champ_id'];
						if(isset($data[$champ_id_tmp]))
						{
							$data_to_add[$champ_id_tmp][] = $data[$champ_id_tmp];
						}
						else if(isset($obj_tmp->$champ_id_tmp))
						{
							$data_to_add[$meta['champ_id']][] = $obj_tmp->$champ_id_tmp;
						}
						else
						{
							$data_to_add[$meta['champ_id']][] = '';
						}
					}

					$data_to_add['raison'] = "Information provenant du clonage de la fiche " . $code_fiche_source;

					$classNameGeneral::save($data_to_add, true);
				} else {

					$liste = $className::getListe(array('fiche_id' => $fiche_id));
					$table = $className::SQL_TABLE_NAME;

					if (isset($liste) && count($liste) > 0) {
						$sql = "SELECT `COLUMN_NAME`, `DATA_TYPE`
                            FROM `INFORMATION_SCHEMA`.`COLUMNS` 
                            WHERE `TABLE_SCHEMA`='$db_name' 
                                AND `TABLE_NAME`='$table'
                                AND `COLUMN_NAME` not in ('id', 'fiche_id', 'no', 'ordre', 'statut', 'projet_id', 'token_archive', 'ts_ajout', 'usager_ajout_id', '" . dm_implode("', '", Fiches::getListeColonnes()) . "')
                        order by DATA_TYPE;";
						$cols = $db->getAll($sql, array());

						$data_to_save = array();

						$iter = 0;
						foreach ($liste as $i => $obj) {
							foreach ($cols as $j => $col) {
								$col_name = $col['COLUMN_NAME'];
								$data_to_save[$col['COLUMN_NAME']][$iter] = $obj->$col_name;
							}
							$iter++;
						}
						$data_to_save['fiche_id'] = $new_id;
						$data_to_save['formulaire'] = $className;
						$data_to_save['raison'] = "Information provenant du clonage de la fiche " . $code_fiche_source;

						if (in_array($className, array('RaccordsElectriquesCoordinations', 'RaccordsElectriquesCoordinationsAuxiliaires'))) {
							$data_to_save['source_alimentation_id'][0] = '';
							$data_to_save['statut_exigence'][0] = '';
							$data_to_save['statut_solution'][0] = '';
							if (isset($data_to_save['usager_exigence_id'][0]) && (int)($data_to_save['usager_exigence_id'][0]) > 0) {
								$data_to_save['usager_exigence_id'][0] = getIdUsager();
								$data_to_save['ts_exigence'][0] = date('Y-m-d H:i:s');
							} else {
								$data_to_save['usager_exigence_id'][0] = '';
								$data_to_save['ts_exigence'][0] = '';
							}
							$data_to_save['usager_solution_id'][0] = '';
							$data_to_save['ts_solution'][0] = '';
						} else if (in_array($className, array('ProprietesRaccordementsControles', 'ProprietesRaccordementsControlesAuxiliaires'))) {
							$data_to_save['statut_exigence'][0] = '';
							$data_to_save['statut_solution'][0] = '';
							$data_to_save['usager_solution_id'][0] = '';
							$data_to_save['ts_solution'][0] = '';
						} else if (in_array($className, array('ProprieteDesRaccordsDePlomberie', 'ProprieteDesRaccordsDeControle', 'ProprieteDeLinstrumentationDeMesureDetectionEtAnalyse'))) {
							$iter = 0;
							foreach ($liste as $i => $obj) {
								$data_to_save['conformite_du_besoin_id'][$iter] = '';
								$data_to_save['reseau_de_distribution_drainage_id'][$iter] = '';
								$data_to_save['automate_id'][$iter] = '';
								$data_to_save['instrument_id'][$iter] = '';
								if (isset($data_to_save['usager_exigence_id'][$iter]) && (int)($data_to_save['usager_exigence_id'][$iter]) > 0) {
									$data_to_save['usager_exigence_id'][$iter] = getIdUsager();
									$data_to_save['ts_exigence'][$iter] = date('Y-m-d H:i:s');
								} else {
									$data_to_save['usager_exigence_id'][$iter] = '';
									$data_to_save['ts_exigence'][$iter] = '';
								}
								$data_to_save['usager_solution_id'][$iter] = '';
								$data_to_save['ts_solution'][$iter] = '';
								$iter++;
							}
						} else if ($className::CAN_HAVE_COORDINATION) {
							$iter = 0;
							foreach ($liste as $i => $obj) {
								$data_to_save['conformite_du_besoin_electricite_id'][$iter] = '';
								$data_to_save['equipement_electricite_id'][$iter] = '';
								$data_to_save['circuit_electricite'][$iter] = '';
								$data_to_save['commentaires_electricite'][$iter] = '';
								if (isset($data_to_save['usager_exigence_electricite_id'][$iter]) && (int)($data_to_save['usager_exigence_electricite_id'][$iter]) > 0) {
									$data_to_save['usager_exigence_electricite_id'][$iter] = getIdUsager();
									$data_to_save['ts_exigence_electricite'][$iter] = date('Y-m-d H:i:s');
								} else {
									$data_to_save['usager_exigence_electricite_id'][$iter] = '';
									$data_to_save['ts_exigence_electricite'][$iter] = '';
								}
								$data_to_save['usager_solution_electricite_id'][$iter] = '';
								$data_to_save['ts_solution_electricite'][$iter] = '';
								$data_to_save['conformite_du_besoin_plomberie_id'][$iter] = '';
								$data_to_save['equipement_plomberie_id'][$iter] = '';
								$data_to_save['commentaires_plomberie'][$iter] = '';
								if (isset($data_to_save['usager_exigence_plomberie_id'][$iter]) && (int)($data_to_save['usager_exigence_plomberie_id'][$iter]) > 0) {
									$data_to_save['usager_exigence_plomberie_id'][$iter] = getIdUsager();
									$data_to_save['ts_exigence_plomberie'][$iter] = date('Y-m-d H:i:s');
								} else {
									$data_to_save['usager_exigence_plomberie_id'][$iter] = '';
									$data_to_save['ts_exigence_plomberie'][$iter] = '';
								}
								$data_to_save['usager_solution_plomberie_id'][$iter] = '';
								$data_to_save['ts_solution_plomberie'][$iter] = '';
								$iter++;
							}
						}

						if (isset($data['projet_id'])) {
							$data_to_save['projet_id'] = (int)($data['projet_id']);
						}

						$className::save($data_to_save, true);
					}
				}
			}

			if ($fiche->fiche_type_id == 2000003) {
				$composantes = Fiches::getListe(array('fiche_parent_id' => $fiche_id, 'no_filtre_global' => 1));
				foreach ($composantes as $composante) {
					$data_clonage = array();
					$data_clonage['fiche_id'] = $composante->id;
					$data_clonage['code'] = $fiche->code . ' - ' . $composante->code;
					$data_clonage['batiment_id'] = $fiche->batiment_id;
					$data_clonage['fiche_parent_id'] = $new_id;

					Fiches::cloner($data_clonage);
				}
			}
			$db->commit();
		}

		return $new_id;
	}

	/**
	 * @param $data
	 */
	public static function clonerFormulaire($data)
	{
		global $db, $db_name, $db_name_archive;

		if (isset($data['listeFiches']) && is_array($data['listeFiches']) && count($data['listeFiches']) > 0) {
			$db->autocommit(false);

            $fiche_id = $data['fiche_id'];
            $formulaire_id = $data['formulaire']::getFormulaireId();
            $projet_id_destination = $data['projet_id_destination'] ?? getProjetId();

            $codeClientDestination = $data['codeClientDestination'] ?? Config::instance()->codeClient;
            $db_name = $codeClientDestination;
            $db_name_archive = $db_name . '_archive';

            $formulaire = new Formulaires($formulaire_id);
            $className = $formulaire->nom;

            $liste = $className::getListe(array('fiche_id' => $fiche_id));
            $table = $className::SQL_TABLE_NAME;

            $metas = $className::getFlatChampsSaisieMetaData();
            $metaDatasSelect = [];
            if (isset($metas) && is_array($metas)) {
                foreach ($metas as $meta) {
                    if ($meta['type'] == 'select' && !isset($meta['liste'])) {
                        $metaDatasSelect[$meta['champ_id']] = $meta;
                    }
                }
            }

			foreach ($data['listeFiches'] as $fiche_id_destination) {
				$sql = "select discipline_id, (select count(*) from `" . db::tableColonneValide($codeClientDestination) . "`.fiches_disciplines where fiche_id = ? and fiches_disciplines.discipline_id = formulaires.discipline_id) as nbFichesDisc 
				        from `" . db::tableColonneValide($codeClientDestination) . "`.formulaires 
				        where id = ?";
				$row = $db->getRow($sql, array($fiche_id_destination, $formulaire_id));
				$discipline_id = (int)($row['discipline_id']);
				$nbFichesDisc = (int)($row['nbFichesDisc']);

				if ($nbFichesDisc == 0 && $discipline_id > 0) {
					$sql = "INSERT INTO `" . db::tableColonneValide($codeClientDestination) . "`.`fiches_disciplines` (fiche_id, discipline_id) values (?, ?)";
					$db->query($sql, array($fiche_id_destination, $discipline_id));
				}

                $listeExistante = $className::getListe(['fiche_id' => $fiche_id_destination]);
                if (count($listeExistante) > 0) {
                    $objExistant = array_values($listeExistante)[0];
                }

				if (isset($liste) && count($liste) > 0) {
					$sql = "SELECT `COLUMN_NAME`, `DATA_TYPE`
                            FROM `INFORMATION_SCHEMA`.`COLUMNS` 
                            WHERE `TABLE_SCHEMA` = ?
                                AND `TABLE_NAME` = ?
                                AND `COLUMN_NAME` not in ('id', 'fiche_id', 'no', 'ordre', 'statut', 'projet_id', 'token_archive', 'ts_ajout', 'usager_ajout_id', '" . dm_implode("', '", Fiches::getListeColonnes()) . "')
                        order by DATA_TYPE;";
					$cols = $db->getAll($sql, [$db_name, $table]);

					$data_to_save = array();
                    $data_to_save['projet_id'] = $projet_id_destination;
                    $data_to_save['fiche_id'] = $fiche_id_destination;
                    $data_to_save['formulaire'] = $className;

					$iter = 0;
					foreach ($liste as $obj) {
						foreach ($cols as $col) {
                            $col_name = $col['COLUMN_NAME'];
                            if (!isset($data['champs']) || in_array($col_name, $data['champs'])) {
                                $data_to_save[$col['COLUMN_NAME']][$iter] = $obj->$col_name ?? '%force_null%';
                            } else if (isset($data['champs'])) {
                                $data_to_save[$col['COLUMN_NAME']][$iter] = $objExistant->$col_name ?? '%force_null%';
                            }
                            if (isset($metaDatasSelect[$col_name]) && $projet_id_destination <> getProjetId()) {
                                $meta = $metaDatasSelect[$col_name];
                                $sql = "INSERT INTO `" . db::tableColonneValide($codeClientDestination) . "`.`" . db::tableColonneValide($meta['table']) . "_projets` (liste_item_id, projet_id) 
                                        select id, '" . $projet_id_destination . "'
                                        from `" . db::tableColonneValide($codeClientDestination) . "`.`" . db::tableColonneValide($meta['table']) . "`
                                        left join `" . db::tableColonneValide($codeClientDestination) . "`.`" . db::tableColonneValide($meta['table']) . "_projets` tb on liste_item_id = id and projet_id = '" . $projet_id_destination . "'
                                        where tb.liste_item_id is null and id = ?";
                                $db->query($sql, [$obj->$col_name]);
                            }
						}
						$iter++;
					}

					$sql = "select code from `" . db::tableColonneValide($codeClientDestination) . "`.fiches where id = ?";
					$row_code = $db->getRow($sql, array($fiche_id));

					$data_to_save['raison'] = txt("Information provenant du clonage du formulaire ") . txt($className::TITRE, false) . txt(" de la fiche ") . $row_code['code'];

					if (in_array($className, array('RaccordsElectriquesCoordinations', 'RaccordsElectriquesCoordinationsAuxiliaires'))) {
						$data_to_save['source_alimentation_id'][0] = '';
						$data_to_save['statut_exigence'][0] = '';
						$data_to_save['statut_solution'][0] = '';
						if (isset($data_to_save['usager_exigence_id'][0]) && (int)($data_to_save['usager_exigence_id'][0]) > 0) {
							$data_to_save['usager_exigence_id'][0] = getIdUsager();
							$data_to_save['ts_exigence'][0] = date('Y-m-d H:i:s');
						} else {
							$data_to_save['usager_exigence_id'][0] = '';
							$data_to_save['ts_exigence'][0] = '';
						}
						$data_to_save['usager_solution_id'][0] = '';
						$data_to_save['ts_solution'][0] = '';
					} else if (in_array($className, array('ProprietesRaccordementsControles', 'ProprietesRaccordementsControlesAuxiliaires'))) {
						$data_to_save['statut_exigence'][0] = '';
						$data_to_save['statut_solution'][0] = '';
						$data_to_save['usager_solution_id'][0] = '';
						$data_to_save['ts_solution'][0] = '';
					} else if (in_array($className, array('ProprieteDesRaccordsDePlomberie', 'ProprieteDesRaccordsDeControle', 'ProprieteDeLinstrumentationDeMesureDetectionEtAnalyse'))) {
						$iter = 0;
						foreach ($liste as $obj) {
							$data_to_save['conformite_du_besoin_id'][$iter] = '';
							$data_to_save['reseau_de_distribution_drainage_id'][$iter] = '';
							$data_to_save['automate_id'][$iter] = '';
							$data_to_save['instrument_id'][$iter] = '';
							if (isset($data_to_save['usager_exigence_id'][$iter]) && (int)($data_to_save['usager_exigence_id'][$iter]) > 0) {
								$data_to_save['usager_exigence_id'][$iter] = getIdUsager();
								$data_to_save['ts_exigence'][$iter] = date('Y-m-d H:i:s');
							} else {
								$data_to_save['usager_exigence_id'][$iter] = '';
								$data_to_save['ts_exigence'][$iter] = '';
							}
							$data_to_save['usager_solution_id'][$iter] = '';
							$data_to_save['ts_solution'][$iter] = '';
							$iter++;
						}
					} else if ($className::CAN_HAVE_COORDINATION) {
						$iter = 0;
						foreach ($liste as $obj) {
							$data_to_save['conformite_du_besoin_electricite_id'][$iter] = '';
							$data_to_save['equipement_electricite_id'][$iter] = '';
							$data_to_save['circuit_electricite'][$iter] = '';
							$data_to_save['commentaires_electricite'][$iter] = '';
							if (isset($data_to_save['usager_exigence_electricite_id'][$iter]) && (int)($data_to_save['usager_exigence_electricite_id'][$iter]) > 0) {
								$data_to_save['usager_exigence_electricite_id'][$iter] = getIdUsager();
								$data_to_save['ts_exigence_electricite'][$iter] = date('Y-m-d H:i:s');
							} else {
								$data_to_save['usager_exigence_electricite_id'][$iter] = '';
								$data_to_save['ts_exigence_electricite'][$iter] = '';
							}
							$data_to_save['usager_solution_electricite_id'][$iter] = '';
							$data_to_save['ts_solution_electricite'][$iter] = '';
							$data_to_save['conformite_du_besoin_plomberie_id'][$iter] = '';
							$data_to_save['equipement_plomberie_id'][$iter] = '';
							$data_to_save['commentaires_plomberie'][$iter] = '';
							if (isset($data_to_save['usager_exigence_plomberie_id'][$iter]) && (int)($data_to_save['usager_exigence_plomberie_id'][$iter]) > 0) {
								$data_to_save['usager_exigence_plomberie_id'][$iter] = getIdUsager();
								$data_to_save['ts_exigence_plomberie'][$iter] = date('Y-m-d H:i:s');
							} else {
								$data_to_save['usager_exigence_plomberie_id'][$iter] = '';
								$data_to_save['ts_exigence_plomberie'][$iter] = '';
							}
							$data_to_save['usager_solution_plomberie_id'][$iter] = '';
							$data_to_save['ts_solution_plomberie'][$iter] = '';
							$iter++;
						}
					}
					$className::save($data_to_save, true);
				}
			}
            $db->commit();
		}
	}

	/**
	 * @param $ouvrage_id
	 * @param $batiment_niveau_element_id
	 * @param $fiche_id
	 */
	public static function ajusterHierarchieFiltre($ouvrage_id, $batiment_niveau_element_id, $fiche_id)
	{
		global $db;

		$sql = "delete from fiches_batiment_niveaux_elements where fiche_id = ?";
		$db->query($sql, array($fiche_id));

		$fiches_batiment_niveaux_elements = BatimentsNiveaux::getHierarchieIds($ouvrage_id, $batiment_niveau_element_id);
		foreach ($fiches_batiment_niveaux_elements as $batiment_niveau_id => $batiment_niveau_element_id_to_save) {
			$sql = "INSERT INTO fiches_batiment_niveaux_elements (fiche_id, batiment_niveau_id, batiment_niveau_element_id) 
                    VALUES (?, ?, ?)";
			$db->query($sql, array($fiche_id, $batiment_niveau_id, $batiment_niveau_element_id_to_save));
		}
	}

	/**
	 * @return array
	 */
	public function getFormulaires()
	{
		global $db;
		$sql = "select formulaire_id "
			. "from fiches_formulaires "
			. "where fiche_id = ?";
		$formulaire_ids_tmp = $db->getAll($sql, array($this->id));

		$formulaire_ids = array();
		foreach ($formulaire_ids_tmp as $i => $row) {
			$formulaire_ids[] = (int)($row['formulaire_id']);
		}

		return Formulaires::getListe(array('ids' => $formulaire_ids));
	}

	/**
	 * @param $gabarit
	 * @param $formulaire
	 */
	public function affecterGabarit($gabarit, $formulaire, $remplaceDonnees = false): void
    {
		global $db, $db_name, $buffer;

		$className = $formulaire->nom;

		$metas = $className::getFlatChampsSaisieMetaData();

		if (isset($buffer) && isset($buffer["liste_$className"])) {
			unset($buffer["liste_$className"]);
		}
		$liste_gabarit = $className::getListe(array('fiche_id' => $gabarit->id));
		if (isset($buffer) && isset($buffer["liste_$className"])) {
			unset($buffer["liste_$className"]);
		}

		$liste_fiche = array();
		if ($className::$TYPE_FORM == 'LISTE') {
			$liste_fiche = $className::getListe(array('fiche_id' => $this->id, 'statut' => 1));
		}

		$table = $className::SQL_TABLE_NAME;

		if (isset($liste_gabarit) && count($liste_gabarit) > 0) {
			$sql = "SELECT `COLUMN_NAME`, `DATA_TYPE`
                    FROM `INFORMATION_SCHEMA`.`COLUMNS` 
                    WHERE `TABLE_SCHEMA`='$db_name' 
                        AND `TABLE_NAME`='$table'
                        AND `COLUMN_NAME` not in ('id', 'fiche_id', 'no', 'ordre', 'statut', 'projet_id', 'token_archive', 'ts_ajout', 'usager_ajout_id', '" . dm_implode("', '", Fiches::getListeColonnes()) . "')
                        order by DATA_TYPE;";
			$cols = $db->getAll($sql);

			$data_to_save = array();

            $sql = "select fiche_sous_type_id from formulaires_fiches_sous_types where formulaire_id = ?";
            $fiche_sous_type_ids = db::instance()->getCol($sql, [$formulaire->id]);
            foreach ($fiche_sous_type_ids as $fiche_sous_type_id) {
                $fiche_sous_type = new FicheSousType($fiche_sous_type_id);
                $fiche_type = new FicheType($fiche_sous_type->fiche_type_id);
                $classNameGeneraux = '';
                if (dm_strlen($fiche_type->formulaire_general) > 0) {
                    $classNameGeneraux = $fiche_type->formulaire_general;
                } else if (dm_strlen($fiche_sous_type->formulaire_general) > 0) {
                    $classNameGeneraux = $fiche_sous_type->formulaire_general;
                }
                if (!empty($classNameGeneraux)) {
                    $tableGeneraux = $classNameGeneraux::SQL_TABLE_NAME;
                    $sql = "select projet_id from `" . db::tableColonneValide($tableGeneraux) . "` where fiche_id = ?";
                    $data_to_save['projet_id'] = $db->getOne($sql, [$this->id]);
                    break;
                }
            }

			$iter = 0;
			foreach ($liste_gabarit as $obj) {
				foreach ($cols as $col) {
					$col_name = $col['COLUMN_NAME'];
					if (isset($obj->$col_name) && (int)$obj->$col_name > 0 && isset($metas[dm_substr($col_name, 0, -3)])) {
						$meta = $metas[dm_substr($col_name, 0, -3)];
						$sql = "INSERT INTO `" . $meta['table'] . "_projets` (liste_item_id, projet_id) 
                                select id, '" . getProjetId() . "'
                                from `" . $meta['table'] . "`
                                left join `" . $meta['table'] . "_projets` tb on liste_item_id = id and projet_id = '" . getProjetId() . "'
                                where tb.liste_item_id is null and id = ?";
						$db->query($sql, [$obj->$col_name]);
					}
					$data_to_save[$col_name][$iter] = isset($obj->$col_name) ? $obj->$col_name : '';
				}
				$data_to_save['statut'][$iter] = 2;
				$iter++;
			}

            if (!$remplaceDonnees) {
                foreach ($liste_fiche as $obj) {
                    foreach ($cols as $col) {
                        $col_name = $col['COLUMN_NAME'];
                        $data_to_save[$col_name][$iter] = isset($obj->$col_name) ? $obj->$col_name : '';
                    }
                    $iter++;
                }
            }

			$data_to_save['fiche_id'] = $this->id;
			$data_to_save['formulaire'] = $className;
			$data_to_save['raison'] = 'Affectation des données par le gabarit ' . $gabarit->code;

			$fiche_formulaire_commentaire = $className::getCommentaireFormulaire($this->id);
			$fiche_formulaire_commentaire = isset($fiche_formulaire_commentaire) ? $fiche_formulaire_commentaire : '';

			$data_to_save['fiche_formulaire_commentaire'] = $fiche_formulaire_commentaire;

			$className::save($data_to_save, true);
		}
	}

	/**
	 * @param $gabarit
	 * @param $formulaire
	 */
	public function dissocierGabarit($gabarit, $formulaire)
	{
		global $db;

		$className = $formulaire->nom;
		$table = $className::SQL_TABLE_NAME;

		$sql = "update `" . db::tableColonneValide($table) . "` set statut = 1 where fiche_id = ? and projet_id = ?";
		$db->query($sql, array($this->id, getProjetId()));

		$sql = "DELETE FROM fiches_gabarits WHERE fiche_id = ? AND gabarit_id = ?";
		$db->query($sql, array($this->id, $gabarit->id));

		$sql = "DELETE FROM fiches_gabarits_formulaires WHERE fiche_id = ? AND gabarit_id = ? AND formulaire_id = ?";
		$db->query($sql, array($this->id, $gabarit->id, $formulaire->id));

		$sql = "DELETE FROM fiches_gabarits_formulaires_lignes_inactives WHERE fiche_id = ? AND gabarit_id = ? AND formulaire_id = ?";
		$db->query($sql, array($this->id, $gabarit->id, $formulaire->id));

		print 'yo';
	}

	/**
	 * @param $formulaire_id
	 * @return Fiches|null
	 */
	public function getGabaritByFormulaire($formulaire_id)
	{
		global $db;

		$sql = "select gabarit_id "
			. "from fiches_gabarits_formulaires "
			. "where fiche_id = ? and formulaire_id = ?";
		$gabarit_row = $db->getRow($sql, array($this->id, $formulaire_id));

		if (isset($gabarit_row) && (int)($gabarit_row['gabarit_id']) > 0) {
			return new Fiches((int)($gabarit_row['gabarit_id']));
		} else {
			return null;
		}
	}

	/**
	 * @return array
	 */
	public function getFormulairesGabarits()
	{
		global $db;

		$sql = "select gabarit_id, formulaire_id "
			. "from fiches_gabarits_formulaires "
			. "where fiche_id = ?";
		$rows = $db->getAll($sql, array($this->id));

		$gabarits_buffer = array();
		$formulaires_gabarits = array();
		foreach ($rows as $row) {
			if (isset($gabarits_buffer[(int)($row['gabarit_id'])])) {
				$gabarit = $gabarits_buffer[(int)($row['gabarit_id'])];
			} else {
				$gabarit = new Fiches((int)($row['gabarit_id']));
				$gabarits_buffer[(int)($row['gabarit_id'])] = $gabarit;
			}
			$formulaires_gabarits[(int)($row['formulaire_id'])] = $gabarit;
		}

		return $formulaires_gabarits;
	}

	/**
	 * @param $formulaire_id
	 * @return array
	 */
	public function getFichesAffectedByGabarit($formulaire_id)
	{
		global $db;

		$sql = "select distinct fiche_id "
			. "from fiches_gabarits "
			. "where gabarit_id = ?";
		$fiches_rows = $db->getAll($sql, array($this->id));

		$ids = array();
		foreach ($fiches_rows as $row) {
			$ids[] = (int)($row['fiche_id']);
		}

		if (count($ids) > 0) {
			return Fiches::getListe(array('ids' => $ids, 'is_gabarit' => 0, 'no_filtre_global' => 1 , 'ignore_ouvrage' => 1 ));
		} else {
			return array();
		}
	}

	/**
	 * @param $formulaire_id
	 * @return bool
	 */
	public function isFormulaireAffectedByGabarit($formulaire_id)
	{
		global $db;

		$sql = "select count(*) as nb "
			. "from fiches_gabarits_formulaires "
			. "where fiche_id = ? and formulaire_id = ?";
		$row_count = $db->getRow($sql, array($this->id, $formulaire_id));

		return ((int)($row_count['nb']) > 0);
	}

	/**
	 * @param $code
	 * @param string $champ_code
	 * @return int
     */
	public static function getIdByCode($code, string $champ_code = 'code'): int
    {
		$sql = "select f.id 
                from fiches f
                left join batiments b on b.id = f.batiment_id
                left join routes r on r.id = f.route_id
                where f." . db::tableColonneValide($champ_code) . " = ? and f.ouvrage_id = ?";
		$row = db::instance()->getRow($sql, array($code, getOuvrageId()));
		return (int)($row['id'] ?? 0);
	}

	/**
	 * @return string
	 */
	public function getNomFormate()
	{
		global $db;

		if ($this->fiche_sous_type_id == 2000002) {
			$form_generaux = GenerauxLocaux::getOne($this->id);
			$str_retour = txt('Local') . (isset($form_generaux) && dm_strlen($form_generaux->utilite) > 0 ? ' - ' . $form_generaux->utilite : '');
		} else if ($this->fiche_sous_type_id == 2000013) {
			$form_generaux = GenerauxGroupesControles::getOne($this->id);
			$str_retour = txt('Groupe de quincaillerie pour le contrôle d\'accès') . (isset($form_generaux) && dm_strlen($form_generaux->utilite) > 0 ? ' - ' . $form_generaux->utilite : '');
		} else if ($this->fiche_sous_type_id == 2000014) {
			$form_generaux = GenerauxPortes::getOne($this->id);
			$str_retour = txt('Portes / accès') . (isset($form_generaux) && dm_strlen($form_generaux->utilite) > 0 ? ' - ' . $form_generaux->utilite : '');
        } else if ($this->fiche_sous_type_id == 2000018) {
			$form_generaux = GenerauxInfrastructures::getOne($this->id);
			$str_retour = txt('(A) Infrastructures') . (isset($form_generaux) && dm_strlen($form_generaux->utilite) > 0 ? ' - ' . $form_generaux->utilite : '');
        } else if ($this->fiche_sous_type_id == 2000019) {
			$form_generaux = GenerauxSuperstructures::getOne($this->id);
			$str_retour = txt('(B) Superstructure et enveloppe') . (isset($form_generaux) && dm_strlen($form_generaux->utilite) > 0 ? ' - ' . $form_generaux->utilite : '');
        } else if ($this->fiche_sous_type_id == 2000020) {
			$form_generaux = GenerauxAmenagementsInterieurs::getOne($this->id);
			$str_retour = txt('(C) Aménagement intérieur de base') . (isset($form_generaux) && dm_strlen($form_generaux->utilite) > 0 ? ' - ' . $form_generaux->utilite : '');
        } else if ($this->fiche_sous_type_id == 2000021) {
			$form_generaux = GenerauxServices::getOne($this->id);
			$str_retour = txt('(D) Services') . (isset($form_generaux) && dm_strlen($form_generaux->utilite) > 0 ? ' - ' . $form_generaux->utilite : '');
        } else if ($this->fiche_sous_type_id == 2000022) {
			$form_generaux = GenerauxEquipementsFixes::getOne($this->id);
			$str_retour = txt('(E) Équipement fixe et ameublement de base') . (isset($form_generaux) && dm_strlen($form_generaux->utilite) > 0 ? ' - ' . $form_generaux->utilite : '');
        } else if ($this->fiche_sous_type_id == 2000023) {
			$form_generaux = GenerauxAmenagementsEmplacements::getOne($this->id);
			$str_retour = txt('(G) Aménagement de l’emplacement') . (isset($form_generaux) && dm_strlen($form_generaux->utilite) > 0 ? ' - ' . $form_generaux->utilite : '');
		} else if ($this->fiche_type_id == 2000000) {
			$form_generaux = GenerauxInstruments::getOne($this->id);
			$nomenclature_nom = '';
			if (isset($form_generaux) && $form_generaux->nomenclature_id > 0) {
				$nomenclature = new Nomenclature($form_generaux->nomenclature_id);
				$nomenclature_nom = $nomenclature->nom;
			}

			$sous_type = new FicheSousType($this->fiche_sous_type_id);
			$str_retour = $sous_type->nom . (dm_strlen($nomenclature_nom) > 0 ? ' - ' . $nomenclature_nom : '');
		} else if ($this->fiche_type_id == 2000003) {
			$form_generaux = GenerauxEquipements::getOne($this->id);
			$nomenclature_nom = '';
			if (isset($form_generaux) && $form_generaux->nomenclature_id > 0) {
				$nomenclature = new Nomenclature($form_generaux->nomenclature_id);
				$nomenclature_nom = $nomenclature->nom;
			}

			$sous_type = new FicheSousType($this->fiche_sous_type_id);
			$str_retour = $sous_type->nom . (dm_strlen($nomenclature_nom) > 0 ? ' - ' . $nomenclature_nom : '');
		} else if ($this->fiche_type_id == 2000001) {
			$form_generaux = GenerauxVehicules::getOne($this->id);
			$nomenclature_nom = '';
			if (isset($form_generaux) && $form_generaux->nomenclature_id > 0) {
				$nomenclature = new Nomenclature($form_generaux->nomenclature_id);
				$nomenclature_nom = $nomenclature->nom;
			}

			$sous_type = new FicheSousType($this->fiche_sous_type_id);
			$str_retour = $sous_type->nom . (dm_strlen($nomenclature_nom) > 0 ? ' - ' . $nomenclature_nom : '');
		} else if ($this->fiche_sous_type_id == 2000015) {
			$str_retour = txt('Route');
		}

		return ($this->is_gabarit ? txt('Gabarit - ') : '') . $str_retour;
	}

	/**
	 * @return array
	 */
	public static function getAllNumerosLots()
	{
		global $db;


		$where = ' and fiches.ouvrage_id = ?';
		$data = array(getOuvrageId());

		if (getProjetId() > 0 && isFicheReadonly()) {
			$where .= ' and exists (select fiche_id from fiches_logs where fiche_id = fiches.id and projet_id = ? limit 1)';
			$data[] = getProjetId();
		}

		$sql = "select distinct numero_lot
                from fiches
                left join batiments on batiments.id = fiches.batiment_id
                left join routes on routes.id = fiches.route_id
                where numero_lot is not null $where
                order by numero_lot";
		$lots_tmp = $db->getAll($sql, $data);

		$lots = array();
		foreach ($lots_tmp as $lot) {
			$lots[] = $lot['numero_lot'];
		}
		return $lots;
	}

	/**
	 * @param $table
	 * @param $id
	 * @param null $projet_id
	 * @return array
	 */
	public static function getCodesFichesReferenced($table, $id, $projet_id = null)
	{
		global $db, $db_name, $tables_for_listes;

		$sql = "SELECT TABLE_NAME, COLUMN_NAME 
                FROM information_schema.KEY_COLUMN_USAGE 
                WHERE REFERENCED_TABLE_NAME = ? AND REFERENCED_COLUMN_NAME = 'id' AND TABLE_SCHEMA = ?;";
		$references = $db->getAll($sql, array($table, $db_name));

		$codes_utilises = array();
		foreach ($references as $reference) {
			if (dm_substr($reference['TABLE_NAME'], 0, 4) <> 'rvt_' && dm_substr($reference['TABLE_NAME'], -8) <> '_projets' && !in_array($reference['TABLE_NAME'], $tables_for_listes) && !in_array($reference['TABLE_NAME'], array('procedures_inspections_gbm_equipements', 'exemptions_verrouillages_equipements', 'inventaires_lignes', 'fournisseurs_marques', 'manuels', 'inventaires_fiches_equipements', 'fiches_statuts_usagers', 'procedures_entretiens', 'fonctions_batiments', 'financements_projets_groupes_equipements', 'acquisitions_projets_groupes_equipements', 'suivis_budgetaires_gestions_generales', 'formulaires_fiches_sous_types'))) {
				$where = 'tbl.' . $reference['COLUMN_NAME'] . " = ?";
				$data = array($id);
				if (in_array($reference['COLUMN_NAME'], ['fiche_statut_id', 'fiche_sous_type_id']) && $reference['TABLE_NAME'] == 'fiches') {
					$sql = "select tbl.code as code, tbl.id as id
                            from fiches tbl
                            where $where";
                } else {
                    if (isset($projet_id)) {
                        $where .= " and projet_id = ?";
                        $data[] = $projet_id;
                    }

                    $sql = "select " . (!isset($projet_id) ? "concat(fiches.code, ' - Projet: ', projets.code, ' - ', projets.nom)" : "fiches.code") . " as code, fiches.id as id
                            from " . $reference['TABLE_NAME'] . " tbl
                            join fiches on fiches.id = tbl.fiche_id
                            join projets on projets.id = tbl.projet_id
                            where $where";
                }
//				printDebug(dm_nl2br(db::interpolateQuery($sql, $data)));
				$rows = $db->getAll($sql, $data);

				foreach ($rows as $i => $row) {
					$codes_utilises[$row['id']] = $row['code'];
				}
			}
		}

		return $codes_utilises;
	}

	/**
	 * @param $table
	 * @param $id
	 * @param null $projet_id
	 * @return array
	 */
	public static function getTablesColonnesReferenced($table, $id, $projet_id = null)
	{
		global $db, $db_name, $tables_for_listes;

		$sql = "SELECT table_name, column_name 
                FROM information_schema.KEY_COLUMN_USAGE 
                WHERE REFERENCED_TABLE_NAME = ? AND REFERENCED_COLUMN_NAME = 'id' AND TABLE_SCHEMA = ?;";
		$references = $db->getAll($sql, array($table, $db_name));

		$tables_colonnes = array();
		foreach ($references as $reference) {
			if (dm_substr($reference['TABLE_NAME'], -8) <> '_projets' && !in_array($reference['TABLE_NAME'], $tables_for_listes) && !in_array($reference['TABLE_NAME'], array('suivis_budgetaires_gestions_generales', 'procedures_entretiens', 'fonctions_batiments', 'financements_projets_groupes_equipements', 'acquisitions_projets_groupes_equipements'))) {
				$tables_colonnes[$reference['TABLE_NAME']] = $reference['COLUMN_NAME'];
			}
		}

		return $tables_colonnes;
	}

	/**
	 * @param $fiche_type_id
	 * @param $fiche_sous_type_id
	 * @return mixed
	 */
	public static function getFormulaireGeneral($fiche_type_id, $fiche_sous_type_id)
	{
		$fiche_type = new FicheType($fiche_type_id);
		if (dm_strlen($fiche_type->formulaire_general) > 0) {
			$classNameGeneral = $fiche_type->formulaire_general;
		} else {
			$fiche_sous_type = new FicheSousType($fiche_sous_type_id);
			if (dm_strlen($fiche_sous_type->formulaire_general) > 0) {
				$classNameGeneral = $fiche_sous_type->formulaire_general;
			}
		}

		return $classNameGeneral;
	}

	/**
	 * @param $fiche_id
	 */
	public static function definitive_delete($fiche_id)
	{
		global $db, $CodeClient;

		$fiche_gabarit = new Fiches($fiche_id);

		if ($fiche_gabarit->is_gabarit == 1) {
			$formulaires = $fiche_gabarit->getFormulaires();
			$fiches = Fiches::getListe(array('is_gabarit' => 0, 'gabarit_id_to_dissocier' => $fiche_gabarit->id, 'no_filtre_global' => 1));
			foreach ($fiches as $fiche) {
				foreach ($formulaires as $i => $formulaire) {
					$fiche->dissocierGabarit($fiche_gabarit, $formulaire);
				}
			}

			$sql = "DELETE FROM fiches_gabarits WHERE gabarit_id = ?";
			$db->query($sql, array($fiche_gabarit->id));

			$sql = "DELETE FROM fiches_gabarits_formulaires WHERE gabarit_id = ?";
			$db->query($sql, array($fiche_gabarit->id));

			$sql = "DELETE FROM fiches_gabarits_formulaires_lignes_inactives WHERE gabarit_id = ?";
			$db->query($sql, array($fiche_gabarit->id));
		}

		$forms = Formulaires::getListe([], 'nom', true);
		foreach ($forms as $form) {
			$class = $form->nom;
			$sql = "delete from " . $class::SQL_TABLE_NAME . " where fiche_id = ?";
			$db->query($sql, array($fiche_id));
		}

		$sql = "delete from procedures_entretiens_taches where procedure_id = (select id from procedures_entretiens where fiche_id = ?)";
		$db->query($sql, array($fiche_id));

		$sql = "delete from procedures_entretiens where fiche_id = ?";
		$db->query($sql, array($fiche_id));

		$sql = "delete from proprietes_dinstallation_et_dutilisation_facteurs where proprietes_dinstallation_et_dutilisation_id in (select id from proprietes_dinstallation_et_dutilisation where fiche_id = ?)";
		$db->query($sql, array($fiche_id));

		$sql = "delete from proprietes_dinstallation_et_dutilisation where fiche_id = ?";
		$db->query($sql, array($fiche_id));

		$sql = "delete from generaux_composantes where fiche_id = ?";
		$db->query($sql, array($fiche_id));

		$sql = "delete from fiches_logs_notifications where fiche_log_id in (select id from fiches_logs where fiche_id = ?)";
		$db->query($sql, array($fiche_id));

		$sql = "delete from fiches_logs where fiche_id = ?";
		$db->query($sql, array($fiche_id));

		$sql = "delete from fiches_formulaires where fiche_id = ?";
		$db->query($sql, array($fiche_id));

		$sql = "delete from fiches_formulaires_commentaires where fiche_id = ?";
		$db->query($sql, array($fiche_id));

		$sql = "delete from fiches_disciplines where fiche_id = ?";
		$db->query($sql, array($fiche_id));

		$sql = "delete from fiches_batiment_niveaux_elements where fiche_id = ?";
		$db->query($sql, array($fiche_id));

		$sql = "delete from fiches_formulaires_projets_champs_textes where fiche_id = ?";
		$db->query($sql, array($fiche_id));

		$sql = "delete from liens_internes where fiche_1_id = ?";
		$db->query($sql, array($fiche_id));

		$sql = "delete from liens_internes where fiche_2_id = ?";
		$db->query($sql, array($fiche_id));

		$sql = "delete from fiches_gabarits where fiche_id = ?";
		$db->query($sql, array($fiche_id));

		$sql = "delete from procedures_entretiens_planifiees_fiches where fiche_id = ?";
		$db->query($sql, array($fiche_id));

		$sql = "delete from fiches_gabarits_formulaires where fiche_id = ?";
		$db->query($sql, array($fiche_id));

		$sql = "delete from notifications_gestion_fiches where fiche_id = ?";
		$db->query($sql, array($fiche_id));

		$sql = "delete from acquisitions_projets_fiches where fiche_id = ?";
		$db->query($sql, array($fiche_id));

		$sql = "delete from acquisitions_projets_groupes_equipements_fiches where fiche_id = ?";
		$db->query($sql, array($fiche_id));

		$sql = "delete from echeanciers_fiches where fiche_id = ?";
		$db->query($sql, array($fiche_id));

		$sql = "delete from financements_projets_fiches where fiche_id = ?";
		$db->query($sql, array($fiche_id));

		$sql = "delete from financements_projets_groupes_equipements_fiches where fiche_id = ?";
		$db->query($sql, array($fiche_id));

		$sql = "delete from inventaires_fiches_equipements where fiche_id = ?";
		$db->query($sql, array($fiche_id));

		$sql = "select qf.*, f.code as fiche_code
                from questions_fiches qf
                join fiches f on qf.fiche_id = f.id
                where fiche_id = ?";
		$questions_fiches = $db->getAll($sql, array($fiche_id));
		foreach($questions_fiches as $question_fiche)
        {
            $sql = "update questions_fiches set fiche_code = ?, fiche_id = null where question_id = ? and fiche_id = ?";
	        $db->query($sql, array($question_fiche['fiche_code'], $question_fiche['question_id'], $fiche_id));
        }

		$sql = "select q.*, f.code as fiche_code
                from questions q
                join fiches f on q.fiche_id = f.id
                where fiche_id = ?";
		$questions = $db->getAll($sql, array($fiche_id));
		foreach($questions as $question)
		{
			$sql = "update questions set fiche_code = ?, fiche_id = null where id = ?";
			$db->query($sql, array($question['fiche_code'], $question['id']));
		}

		$sql = "delete from fiches where id = ?";
		$db->query($sql, array($fiche_id));
	}

	/**
	 * @param $fiche_id
	 */
	public static function recuperation($fiche_id)
	{
		global $db;

		$sql = "UPDATE `fiches` set ts_delete = NULL, usager_delete_id = NULL WHERE id = ?;";
		$db->query($sql, array($fiche_id));

		$sql = "UPDATE `fiches` set ts_delete = NULL, usager_delete_id = NULL WHERE fiche_parent_id = ?;";
		$db->query($sql, array($fiche_id));

		$sql = "insert into `fiches_logs` (fiche_id, projet_id, ts_ajout, usager_ajout_id, code, nb_lignes, raison) values (?, ?, current_timestamp, ?, ?, ?, ?)";
		$db->query($sql, array($fiche_id, getProjetId(), getIdUsager(), 'deleteFiche', 0, 'Récupération de la fiche'));
	}

	/**
	 * @return bool
	 */
	public function isLocal()
	{
		return $this->fiche_sous_type_id == 2000002;
	}

	/**
	 * @return bool
	 */
	public function isEquipement()
	{
		return in_array($this->fiche_sous_type_id, array(2000003, 2000006, 2000007, 2000008, 2000009, 2000011, 2000012, 2000016));
	}

	/**
	 * @return bool
	 */
	public function isInstrument()
	{
		return in_array($this->fiche_sous_type_id, array(2000000, 2000001));
	}

	/**
	 * @return array
	 */
	public static function getListeColonnes()
	{
		return array('code', 'numero_lot', 'code_alternatif', 'code_alternatif_2', 'id_revit', 'id_codebook', 'batiment_id', 'fiche_type_id', 'fiche_sous_type_id', 'batiment_niveau_element_id', 'fiche_local_id', 'fiche_local_controleur_id', 'fiche_local_destination_id', 'fiche_parent_id', 'route_id', 'chainage_debut', 'chainage_fin');
	}

	/**
	 * @return array
	 */
	public static function getLocauxTypesNumbers()
	{
		global $db;

		$sql = "select substring(code, 1, char_length(code) - 2) as code_type, count(*) as nb
                from fiches f
                where ouvrage_id = ? and f.fiche_sous_type_id = 2000002
                group by code_type";
		$locaux_types = $db->getAssoc($sql, array(getOuvrageId()));

		return $locaux_types;
	}

	/**
	 * @param $filtre_top
	 * @param $filtre_avance
	 * @return bool
	 */
	public static function isFiltreActif($filtre_top, $filtre_avance)
	{
		$is_actif = false;
		if (isset($filtre_avance) && count($filtre_avance) > 0) {
			$is_actif = true;
		} else if (isset($filtre_top) && count($filtre_top) > 0) {
			if (isset($filtre_top['code_fiche_search']) && dm_strlen($filtre_top['code_fiche_search']) > 0) {
				$is_actif = true;
			}
			if (isset($filtre_top['batiment_id']) && (int)($filtre_top['batiment_id']) > 0) {
				$is_actif = true;
			}
			if (isset($filtre_top['fiche_type_id']) && (int)($filtre_top['fiche_type_id']) > 0) {
				$is_actif = true;
			}
			if (isset($filtre_top['fiche_sous_type_id'])) {
				if (!is_array($filtre_top['fiche_sous_type_id']) && (int)($filtre_top['fiche_sous_type_id']) > 0) {
					$is_actif = true;
				}
			}
			if (isset($filtre_top['numero_lot']) && dm_strlen($filtre_top['numero_lot']) > 0) {
				$is_actif = true;
			}
			if (isset($filtre_top['code_alternatif']) && dm_strlen($filtre_top['code_alternatif']) > 0) {
				$is_actif = true;
			}
			if (isset($filtre_top['code_alternatif_2']) && dm_strlen($filtre_top['code_alternatif_2']) > 0) {
				$is_actif = true;
			}
			if (isset($filtre_top['id_revit']) && dm_strlen($filtre_top['id_revit']) > 0) {
				$is_actif = true;
			}
			if (isset($filtre_top['id_codebook']) && dm_strlen($filtre_top['id_codebook']) > 0) {
				$is_actif = true;
			}
            if (isset($filtre_top['codes_alternatifs']) && is_array($filtre_top['codes_alternatifs']) && count($filtre_top['codes_alternatifs']) > 0) {
                $is_actif = true;
            }
            if (isset($filtre_top['ids_revits']) && is_array($filtre_top['ids_revits']) && count($filtre_top['ids_revits']) > 0) {
                $is_actif = true;
            }
            if (isset($filtre_top['ids_codebooks']) && is_array($filtre_top['ids_codebooks']) && count($filtre_top['ids_codebooks']) > 0) {
                $is_actif = true;
            }
			if (isset($filtre_top['fiche_codes']) && is_array($filtre_top['fiche_codes']) && count($filtre_top['fiche_codes']) > 0) {
				$is_actif = true;
			}
            if (isset($filtre_top['fiche_statut_id']) && is_array($filtre_top['fiche_statut_id']) && count($filtre_top['fiche_statut_id']) > 0) {
                $is_actif = true;
            }
			if (isset($filtre_top['echeancier_id']) && is_array($filtre_top['echeancier_id']) && count($filtre_top['echeancier_id']) > 0) {
				$is_actif = true;
			}
			if (isset($filtre_top['is_orphelin']) && intval($filtre_top['is_orphelin']) == 1) {
				$is_actif = true;
			}
			if (isset($filtre_top['niveau']) && count($filtre_top['niveau']) > 0) {
				$is_actif = true;
			}
		}

		return $is_actif;
	}

	/**
	 * @param array $selected_fiches
	 * @param string $filtre_global_to_use
	 * @param array $extra_filter
	 * @param bool $rechercheNiveau
	 * @param int $height
	 */
	public static function getMultiSelector(array $selected_fiches, string $filtre_global_to_use, array $extra_filter = [], bool $rechercheNiveau = false, $height = 330, $titreGauche = 'fiche(s) disponible(s)', $titreDroite = 'fiche(s) associée(s)')
	{
		?>
        <style>
            table, table thead tr, table tfoot tr, table th {
                background-image: none;
            }
            td {
                border: solid 0px white!important;
                text-align: left!important;
                padding: 0px!important;
            }
        </style>
        <script>
			var timout = setTimeout(function () {
			}, 1);

			function filtre_avance_callback(isFiltreActif) {
				if (isFiltreActif == 1) {
					$('#clear_filtre').show();
					$('#filter').css('opacity', '1');
					$('#filter').attr('title', '<?=txt('Filtre(s) actif(s)')?>');
				} else {
					$('#clear_filtre').hide();
					$('#filter').css('opacity', '0.5');
					$('#filter').attr('title', '<?=txt('Aucun filtre actif')?>');
				}
				appliqueFiltre(0);
			}

			var fichesVerrouillesAssociees = [];
			function appliqueFiltre(disable_fitre_avance) {
				clearTimeout(timout);
				timout = setTimeout(function () {

					let selected_list = [];
					$('#listeFiches_to').find('option').each(function () {
						selected_list.push(parseInt(this.value));
					});
					let selected_list_ver = [];
					for (const ficheVerrouille of fichesVerrouillesAssociees) {
						selected_list_ver.push(ficheVerrouille.id);
					}

					let data = {};
					data.action = 'getFichesList';
					data.filtre_global_to_use = '<?=$filtre_global_to_use?>';
					if (disable_fitre_avance !== undefined && disable_fitre_avance === 1) {
						data.disable_fitre_avance = 1;
					}
                    <?
                    foreach ($extra_filter as $key => $value) {
                        if (is_array($value)) {
                            ?>
                            data.<?=$key?> = [];
                            $(document).find('[name="<?= $key ?>[]"]').each(function(){
                                data.<?=$key?>.push(this.value);
                            });
                            <?
                        } else {
                            ?>
                            if ($('#<?=$key?>').length) {
                                data.<?=$key?> = $('#<?=$key?>').val();
                            }
                            <?
                        }
                    }
                    ?>
					if (data.identifierFichesFromDepotsVerouillesPourEquipement) {
						if ($('#formulaire_id_for_items').val() == 144) {
							data.liste_item_id = $('#liste_item_id').val();
						} else {
							data.liste_item_id = -1;
						}
					}

					$.startObjectLoader($('#listeFiches'), 'multiselect_loader');
					$.get('index.php', data, function (json_data) {
						$('#listeFiches').html('');
						fichesVerrouillesAssociees = [];
						const listeFiches = document.querySelector(`#listeFiches`);
						listeFiches.innerHTML = '';
						const listeFiches_to = document.querySelector(`#listeFiches_to`);
						listeFiches_to.innerHTML = '';
						$.each(json_data, function (i, data) {
							setTimeout(() => {
								const li = document.createElement('li');
								li.innerHTML = `${data.text + getBatimentTxt(data)}`;
								li.value = data.id;

                                if (!selected_list.includes(data.id) && !selected_list_ver.includes(data.id)) {
                                    if (!data.verrouille) {
										listeFiches.appendChild(li);
                                    }
                                } else {
                                    if (selected_list_ver.includes(data.id)) {
										listeFiches_to.appendChild(li);
                                    }
                                    if (data.verrouille) {
                                        fichesVerrouillesAssociees.push(data);
                                    }
                                }
							}, 1);
						});
						setTimeout(() => {
                            recomptageFiches();
                            $.stopObjectLoader('multiselect_loader');
						}, 50);
					}, 'json');

				}, 1);
			}

			function getBatimentTxt(data) {
				let bat_niv = '';
				if (data.batiment_niveau_element !== null) {
					bat_niv = ' - ';
					const bat_niv_split = data.batiment_niveau_element.split(' - ');
					for (let iter in bat_niv_split) {
						if (iter > 0) {
							bat_niv = bat_niv + (iter > 1 ? ' - ' : '') + bat_niv_split[iter];
						}
					}
				}
				return bat_niv;
            }
        </script>
		<?
		$title = txt('Aucun filtre actif');
		$isFiltreActif = false;
		if (isset($_SESSION['filtre_top' . $filtre_global_to_use]) && isset($_SESSION['filtre_avance' . $filtre_global_to_use])) {
			$title = txt('Filtre(s) actif(s)');
			$isFiltreActif = Fiches::isFiltreActif($_SESSION['filtre_top' . $filtre_global_to_use], $_SESSION['filtre_avance' . $filtre_global_to_use]);
		}

		$options_left = array();
		$options_right = array();
        $filter = ['filtre_global_to_use' => $filtre_global_to_use];
        foreach($extra_filter as $key => $value)
        {
            $filter[$key] = $value;
        }

        $all_fiches = Fiches::getListe($filter);
        if (isset($all_fiches) && is_array($all_fiches) && count($all_fiches) > 0) {
            foreach ($all_fiches as $fiche_id => $fiche) {
                if (!isset($selected_fiches[$fiche->id])) {
                    $txt_supp = '';
                    if (dm_strlen($fiche->code_alternatif_2) > 0) {
                        $txt_supp = ' ('.$fiche->code_alternatif_2.')';
                    } else if (dm_strlen($fiche->ts_delete) > 0) {
                        $txt_supp = txt(' (fiche supprimée)');
                    }

                    if ($rechercheNiveau && isset($fiche->batiment_niveau_element)) {
                        $txt_supp .= ' - ';
                        $bat_niv_split = dm_explode(' - ', $fiche->batiment_niveau_element);
                        foreach ($bat_niv_split as $iter => $bat_niv_spl) {
                            if ($iter > 0) {
                                $txt_supp .= ($iter > 1 ? ' - ' : '') . $bat_niv_spl;
                            }
                        }
                    }
                    $options_left[] = ['value' => $fiche->id, 'text' => $fiche->code. $txt_supp, 'style' => '', 'class' => ''];
                }
            }
        }
        $isAfficheMessageRouge = false;
        if (isset($selected_fiches) && is_array($selected_fiches) && count($selected_fiches) > 0) {
            $selected_fiches_ids = array_keys($selected_fiches);
            $fiches_sel = Fiches::getListe(['ids' => $selected_fiches_ids, 'inclure_supprimees' => 1]);
            foreach ($fiches_sel as $fiche_id => $fiche) {
                $txt_supp = '';
                if (dm_strlen($fiche->code_alternatif_2) > 0) {
                    $txt_supp = ' ('.$fiche->code_alternatif_2.')';
                } else if (dm_strlen($fiche->ts_delete) > 0) {
                    $txt_supp = txt(' (fiche supprimée)');
                }
                if ($rechercheNiveau && isset($fiche->batiment_niveau_element)) {
                    $txt_supp .= ' - ';
                    $bat_niv_split = dm_explode(' - ', $fiche->batiment_niveau_element);
                    foreach ($bat_niv_split as $iter => $bat_niv_spl) {
                        if ($iter > 0) {
                            $txt_supp .= ($iter > 1 ? ' - ' : '') . $bat_niv_spl;
                        }
                    }
                }
                $isFicheEnTrop = isset($all_fiches[$fiche->id]) ? false : true;
                $color = $isFicheEnTrop ? 'color: red!important;' : '';
                $class = $isFicheEnTrop ? 'isFicheEnTrop' : '';
                if ($isFicheEnTrop) {
                    $isAfficheMessageRouge = true;
                }
                $options_right[$fiche_id] = ['value' => $fiche_id, 'text' => $fiche->code . $txt_supp, 'style' => $color, 'class' => $class];
            }
        }
        ?>
        <table>
            <tr>
                <td style="width: 43%; vertical-align: top;">
                    <b style="font-weight: bold; display: inline-block; width: 100%; margin-bottom: 10px;">&nbsp;
                        <span id="nbGauche" style="color: blue;font-weight: bold;"><?= count($options_left) ?></span>&nbsp;<?= txt($titreGauche) ?>
                        <a href="index.php?section=filtre_avance&module=filtre_avance&type=<?= $filtre_global_to_use ?>&smaller=1&callback=filtre_avance_callback"
                           class="fancy"
                           style="position: relative; top: 8px; left: 15px; <?= ($isFiltreActif ? '' : 'opacity: 0.5;') ?>"
                           title="<?= $title ?>">
                            <img src="css/images/icons/dark/filter.png"/></a>

                        <a href="javascript: void(0);" id="clear_filtre"
                           style="display: <?= ($isFiltreActif ? 'inline!important' : 'none') ?>;position: relative; top: 8px; left: 15px; "
                           title="Libérer tous les filtres">
                            <img src="css/images/icons/dark/bended_arrow_left.png"></a>
                    </b>
                    <input type="text" id="codeFiche" value="" autocomplete="off" style="min-width: 100%!important;" placeholder="Recherche..."/><br />
                    <ul id="listeFiches" class="listeFichesMultiple" style="height: <?=$height?>px!important; font-size: 11px; width: 100%; max-width: 320px; min-width: auto!important;"></ul>
                </td>
                <td style="width: 14%; text-align: center!important; vertical-align: top; padding-top: 70px!important;">
                    <span class="fas fa-angle-double-right" id="listeFiches_rightAll"
                          style="font-size: 20px; cursor: pointer; opacity: 0.7; width: 40px; margin-bottom: 10px;"></span><br/>
                    <span class="fas fa-angle-right" id="listeFiches_rightSelected"
                          style="font-size: 20px; cursor: pointer; opacity: 0.7; width: 40px; margin-bottom: 10px;"></span><br/>
                    <span class="fas fa-angle-left" id="listeFiches_leftSelected"
                          style="font-size: 20px; cursor: pointer; opacity: 0.7; width: 40px; margin-bottom: 10px;"></span><br/>
                    <span class="fas fa-angle-double-left" id="listeFiches_leftAll"
                          style="font-size: 20px; cursor: pointer; opacity: 0.7; width: 40px; margin-bottom: 10px;"></span>
                </td>
                <td style="width: 43%; vertical-align: top; padding-top: 12px!important; position: relative">
                    <b style="font-weight: bold; display: inline-block; width: 100%; margin-bottom: 10px;">
                        <span id="nbDroite" style="color: blue;font-weight: bold;"><?= count($options_right) ?></span>&nbsp;<?= txt($titreDroite) ?>
                    </b><br />
                    <a id="visualiserFichesVerouillees" style="font-weight: bold; display: inline-block; margin-bottom: 10px; margin-left: 55px; text-decoration: underline; position: absolute; right: 0;color: red!important;">
                        <span id="nbVerrouilles" style="color: red;font-weight: bold;">0</span>&nbsp;<?= txt('fiche(s) verrouillée(s)') ?>
                    </a>
                    <div style="height: 30px;">
                    <b id="messageRouge" style="color: red; display: none;"><?=txt('Fiches en trop ou supprimées dans cette liste')?></b>
                    </div>
                    <ul id="listeFiches_to" class="listeFichesMultiple" style="height: <?=$height?>px!important; font-size: 11px; width: 100%; max-width: 320px; min-width: auto!important;"></ul>
                </td>
            </tr>
        </table>
        <script>
			function recomptageFiches() {
				if (fichesVerrouillesAssociees.length) {
					$('#nbVerrouilles').parent().show();
					$('#nbVerrouilles').html(fichesVerrouillesAssociees.length);
				} else {
					$('#nbVerrouilles').parent().hide();
				}
				for (const ficheVerrouille of fichesVerrouillesAssociees) {
					$(`li[value="${ficheVerrouille.id}"]`).remove();
				}
                $('#nbGauche').html($('#listeFiches').find('li').length);
				$('#nbDroite').html($('#listeFiches_to').find('li').length);
            }

			var theGlobalMulti = null;
			$(document).ready(function () {

				$('#visualiserFichesVerouillees').click(function(){
					$('#dialog-verouillees').remove();
					$('body').append(`<div id="dialog-verouillees" title="Liste des locaux vérouillés" style="display: none; margin: 5px 15px;">
                                        <p id="listeFichesVerouillees" style="margin-top: 10px;"></p>
                                    </div>`);

					const dialog = $('#dialog-verouillees');
					let tableauListeLocauxVerrouilles = '';
					for (const ficheVerrouille of fichesVerrouillesAssociees) {
						tableauListeLocauxVerrouilles += `<div class="tooltip_struct_ver" data-fiche_id="${ficheVerrouille.id}">${ficheVerrouille.text + getBatimentTxt(ficheVerrouille)}</div>`;
					}
					$('#listeFichesVerouillees').html(tableauListeLocauxVerrouilles);
					initDynamicToolTip('tooltip_struct_ver', 'body', '.tooltip_struct_ver', 'data-fiche_id', 'get_hierarchie_tooltip', 'fiche_id');

					dialog.find("#listeFichesVerouillees").html();
					dialog.dialog({
						height:450,
						width:900,
						modal: true,
						buttons: {
							// "Exporter la liste": function() {
							// 	$.startObjectLoader($('body'), 'export_loader');
							// 	let formData = new FormData();
							// 	for (const ficheVerrouille of fichesVerrouillesAssociees) {
							// 		tableauListeLocauxVerrouilles += `<div class="tooltip_struct_ver" data-fiche_id="${ficheVerrouille.id}">${ficheVerrouille.text + getBatimentTxt(ficheVerrouille)}</div>`;
							// 	}
							// 	formData.append('fiche_', 'John');
							// 	formData.append('password', 'John123');
                            //
							// 	fetch('index.php?section=fiche&module=exporterVerrouillees', {
							// 		method: 'POST',
							// 		body: formData
							// 	})
							// 		.then(response => response.blob())
							// 		.then(blob => {
							// 			var url = window.URL.createObjectURL(blob);
							// 			var a = document.createElement('a');
							// 			a.href = url;
							// 			a.download = "acquisition.xlsx";
							// 			document.body.appendChild(a); // we need to append the element to the dom -> otherwise it will not work in firefox
							// 			a.click();
							// 			a.remove();  //afterwards we remove the element again
							// 			$.stopObjectLoader('export_loader');
							// 		});
							// },
							"Fermer": function() {
								$(this).dialog( "close" );
							}
						}
					});
                });

				$('#clear_filtre').click(function () {
					appliqueFiltre(1);
					$('#clear_filtre').hide();
					$('#filter').css('opacity', '0.5');
					$('#filter').attr('title', '<?=txt('Aucun filtre actif')?>');
				});

				<?
                if ($isAfficheMessageRouge) {
                    ?>
                    $('#messageRouge').show();
                <?
                }
                ?>

                const listeFiches = document.querySelector(`#listeFiches`);
                const listeFiches_to = document.querySelector(`#listeFiches_to`);

                const options_left = <?= json_encode($options_left) ?>;
                const options_right = <?= json_encode(array_values($options_right)) ?>;

                $.startObjectLoader($(listeFiches).closest('table'), 'listeFiches_loader');
                // Exécutez les deux chargements en parallèle
                Promise.all([
					loadOptions(options_left, listeFiches),
					loadOptions(options_right, listeFiches_to)
                ]).then(() => {
                    // Appeler stopObjectLoader lorsque les deux chargements sont terminés
                    $.stopObjectLoader('listeFiches_loader');
                });

                $('#listeFiches').multiselect({
                    submitAllLeft: false,
                    submitAllRight: true,
                    beforeMoveToRight: function ($left, $right, $options) {
                        if ($options.length > 100) {
                            return confirm('<?=txt('Êtes-vous certain de vouloir associer toutes ces fiches?', false)?>\n\n<?=dm_addslashes(txt('Note: L\'opération prendra plusieurs secondes à se compléter si le volume de fiche est important.', false))?>');
                        } else if ($options.length == 0) {
                            alert('<?=dm_addslashes(txt('Veuillez d\'abord sélectionner une ou plusieurs fiches à associer.', false))?>');
                            return false;
                        }
                        return true;
                    },
                    beforeMoveToLeft: function ($left, $right, $options) {
                        if ($options.length > 100) {
                            return confirm('<?=txt('Êtes-vous certain de vouloir dissocier toutes ces fiches?', false)?>\n\n<?=dm_addslashes(txt('Note: L\'opération prendra plusieurs secondes à se compléter si le volume de fiche est important.', false))?>');
                        } else if ($options.length == 0) {
                            alert('<?=dm_addslashes(txt('Veuillez d\'abord sélectionner une ou plusieurs fiches à dissocier.', false))?>');
                            return false;
                        }
                        return true;
                    },
                    afterMoveToLeft: function ($left, $right, $options) {
                        if ($right.find('option.isFicheEnTrop').length === 0) {
                            $('#messageRouge').hide();
                        } else {
                            $('#messageRouge').show();
                        }
                        recomptageFiches();

                        if (typeof afterMoveToLeftCallback === 'function') {
                            afterMoveToLeftCallback();
                        }
                    },
                    afterMoveToRight: function ($left, $right, $options) {
                        if ($right.find('option.isFicheEnTrop').length === 0) {
                            $('#messageRouge').hide();
                        } else {
                            $('#messageRouge').show();
                        }
                        recomptageFiches();

                        if (typeof afterMoveToRightCallback === 'function') {
                            afterMoveToRightCallback();
                        }
                    }

                });

                DmUlMultiSelector.addNew('listeFiches');
				theGlobalMulti = DmUlMultiSelector.addNew('listeFiches_to', 'listeFiches', 'all');
			});

			function loadOptions(options, targetList) {
				return new Promise((resolve) => {
					setTimeout(() => {
						if (options.length === 0) {
							resolve();
                        } else {
						$.each(options, function (i, data) {
							setTimeout(() => {
								const li = document.createElement('li');
								li.innerHTML = data.text;
								li.value = data.value;
								li.class = data.class;
								li.style = data.style;
								targetList.appendChild(li);

								// Vérifiez si c'est le dernier élément pour résoudre la promesse
								if (i === options.length - 1) {
									resolve();
								}
							}, 1);
						});
						}
					}, 1);
				});
			}
        </script>
		<?
	}

	/**
	 * @param $fiche_id
	 * @param $formulaire_id
	 * @param null $token_archive
	 * @return null
	 */
	public static function getLastTokenArchive($fiche_id, $formulaire_id, $token_archive = null)
	{
		global $db;

		$sql = "select token_archive from fiches_logs where fiche_id = ? and formulaire_id = ? and projet_id = ? order by ts_ajout desc";
		$fiche_logs = $db->getAll($sql, array($fiche_id, $formulaire_id, getProjetId()));

		$found = false;
		$last_token_archive = null;
		foreach($fiche_logs as $log)
		{
			if($found)
			{
				$last_token_archive = $log['token_archive'];
				break;
			}

			if((!isset($token_archive) || dm_strlen($token_archive) == 0) && dm_strlen($log['token_archive']) == 0)
			{
				$found = true;
			}
			else if(isset($token_archive) && dm_strlen($token_archive) > 0 && $log['token_archive'] == $token_archive)
			{
				$found = true;
			}
		}

		return $last_token_archive;
	}

	public static function getFichesWithEcheanciers($filtre) {
	    global $db;

	    $where = "";
	    $data = [getOuvrageId()];

	    if (isset($filtre['affichage']) && $filtre['affichage'] <> 'tous') {
	        if ($filtre['affichage'] == 'sans') {
                $where .= " and nb_echeanciers = 0";
            } else if ($filtre['affichage'] == 'multiple') {
		        $where .= " and nb_echeanciers > 1";
            }
        }

        $whereJoin = '';
        if (isset($filtre['echeancier']) && is_array($filtre['echeancier']) && count($filtre['echeancier']) > 0) {
            $whereJoin .= " and e2.id in (".db::placeHolders($filtre['echeancier']).")";
            $data = array_merge($data, $filtre['echeancier']);
        }

        if (isset($filtre['fiche_ids']) && count($filtre['fiche_ids']) > 0) {
            if (isset($filtre['fiche_ids'][0]) && $filtre['fiche_ids'][0] == -1) {
                $where .= " and false";
            } else {
                $where .= " and tmp.fiche_id in (".db::placeHolders($filtre['fiche_ids']).")";
                $data = array_merge($data, $filtre['fiche_ids']);
            }
        }

        if (isset($filtre['type']) && count($filtre['type']) > 0) {
            $where .= " and tmp.type in (".db::placeHolders($filtre['type']).")";
            $data = array_merge($data, $filtre['type']);
        }

	    $sql = "select  tmp.id, 
                        tmp.code, 
                        tmp.code_alternatif, 
                        tmp.code_alternatif_2, 
                        tmp.batiment_niveau_element, 
                        tmp.type, 
                        tmp.fiche_id, 
                        tmp.echeanciers, 
                        tmp.nb_echeanciers from (
                    select 
                        f.id, 
                        f.code, 
                        f.code_alternatif, 
                        f.code_alternatif_2, 
                        bne.nom as batiment_niveau_element, 
                        e.type, 
                        f.id as fiche_id, 
                        GROUP_CONCAT(e.nom SEPARATOR ', ') as echeanciers, 
                        count(distinct e.id) as nb_echeanciers
                    from fiches f 
                    left join echeanciers_fiches ef on f.id = ef.fiche_id
                    left join echeanciers e on ef.echeancier_id = e.id
                    left join batiments_niveaux_elements bne on bne.id = f.batiment_niveau_element_id
                    where f.ouvrage_id = ? 
                        and f.ts_delete is null 
                        and f.ts_archive is null 
                        and f.fiche_sous_type_id = 2000002 
                        and f.is_gabarit = 0  
                    group by f.id, fiche_id, f.code, f.code_alternatif, f.code_alternatif_2, e.type, batiment_niveau_element
                ) tmp
                inner join echeanciers_fiches ef2 on tmp.fiche_id = ef2.fiche_id
                inner join echeanciers e2 on ef2.echeancier_id = e2.id and e2.type = tmp.type $whereJoin
                where true $where
                order by code";
	    $fiches_tmp = $db->getAll($sql, $data);
	    $fiches = [];
	    foreach ($fiches_tmp as $fiche) {
	        $fiches[] = new self($fiche['id'], $fiche);
        }
        return $fiches;
    }

    public static function getFichesWithEcheanciersForDatatable($filtre) {
        $fiches = self::getFichesWithEcheanciers($filtre);
        $data = [];
        foreach ($fiches as $fiche) {
            $data[] = [
                '<span class="tooltip_struct" style="padding: 4px" data-fiche_id="' . $fiche->id . '">' . $fiche->getCodeWithBatiment() . '</span>',
                Echeanciers::getNomTypeByCode($fiche->type),
                $fiche->echeanciers,
                '<a href="index.php?section=echeancier_global&module=edition&fiche_id=' . $fiche->id . '" class="editer fancy"><img src="css/images/icons/dark/create_write.png" /></a>'
            ];
        }
        return $data;
    }

    public function getCodeWithBatiment() {
	    $bat_niv = '';
        if (isset($this->batiment_niveau_element)) {
	        $bat_niv = ' - ';
            $bat_niv_split = dm_explode(' - ', $this->batiment_niveau_element);
            foreach ($bat_niv_split as $iter => $niv) {
                if ($iter > 0) {
	                $bat_niv = $bat_niv . ($iter > 1 ? ' - ' : '') . $niv;
                }
            }
        }
	    return $this->code.$bat_niv;
    }

	public static function getCodeWithBatimentStatique($code_fiche, $batiment_niveau_element) {
		$bat_niv = '';
		if (isset($batiment_niveau_element)) {
			$bat_niv = ' - ';
			$bat_niv_split = dm_explode(' - ', $batiment_niveau_element);
			foreach ($bat_niv_split as $iter => $niv) {
				if ($iter > 0) {
					$bat_niv = $bat_niv . ($iter > 1 ? ' - ' : '') . $niv;
				}
			}
		}
		return $code_fiche.$bat_niv;
	}

	public static function getFicheIdsDepotsVerouilles() {
		global $db, $CacheFicheIdsDepotsVerouilles;

        if (!isset($CacheFicheIdsDepotsVerouilles)) {
	        $extra_cols = [];
	        $extra_joins = [];
	        $where = ' and (depot1.ts_verrouille is not null 
						or depot2.ts_verrouille is not null 
						or depot3.ts_verrouille is not null 
						or depot4.ts_verrouille is not null 
						or depot5.ts_verrouille is not null 
						or depot1.ts_photo_j0 is not null
						or depot2.ts_photo_j0 is not null 
						or depot3.ts_photo_j0 is not null 
						or depot4.ts_photo_j0 is not null 
						or depot5.ts_photo_j0 is not null)';
	        for ($iterDepot = 1; $iterDepot <= 5; $iterDepot++) {
		        $extra_cols[] = 'depot' . $iterDepot . '.financement_projet_id as financement_projet_id_' . $iterDepot;
		        $extra_joins[] = 'left join financements_projets_depots as depot' . $iterDepot . ' on depot' . $iterDepot . '.id = tb.depot_source' . $iterDepot . '_id';
	        }
	        $sql = "select distinct " . dm_implode(', ', $extra_cols) . "
                from equipements_bio_medicaux tb
                " . dm_implode("\n", $extra_joins) . "
                where projet_id = ? $where ";
	        $projets_tmp = $db->getAll($sql, [getProjetId()]);

	        $CacheFicheIdsDepotsVerouilles = [];
            if (count($projets_tmp) > 0) {
                $projets = [];
                foreach ($projets_tmp as $row) {
	                for ($iterDepot = 1; $iterDepot <= 5; $iterDepot++) {
                        if (isset($row['financement_projet_id_' . $iterDepot]) && $row['financement_projet_id_' . $iterDepot] > 0) {
	                        $projets[$row['financement_projet_id_' . $iterDepot]] = $row['financement_projet_id_' . $iterDepot];
                        }
	                }
                }

                foreach ($projets as $financement_projet_id => $fiche_ids) {
	                $sql = "select distinct fiche_id 
                        from financements_projets_fiches
                        where financement_projet_id = $financement_projet_id";
	                $CacheFicheIdsDepotsVerouilles = array_merge($CacheFicheIdsDepotsVerouilles, $db->getCol($sql, []));
                }
            }
        }
		return $CacheFicheIdsDepotsVerouilles;
	}

    public static function traitementSauvegardeSupplementaire($id, $requestData, $is_ajout, $objAnciennesValeurs = null)
    {
        $classNameGeneral = Fiches::getFormulaireGeneral(intval($requestData['fiche_type_id']), intval($requestData['fiche_sous_type_id']));
        if (dm_strlen($classNameGeneral) > 0) {
            $data_to_add = array();
            $data_to_add['formulaire'] = $classNameGeneral;
            $data_to_add['fiche_id'] = $id;

            $obj_tmp = $classNameGeneral::getOne($id);
            $metas = $classNameGeneral::getFlatChampsSaisieMetaData();
            foreach ($metas as $key => $meta) {
                $champ_id_tmp = $meta['champ_id'];
                if (isset($requestData[$champ_id_tmp])) {
                    $data_to_add[$meta['champ_id']][] = $requestData[$meta['champ_id']];
                } else if (isset($obj_tmp->$champ_id_tmp)) {
                    $data_to_add[$meta['champ_id']][] = $obj_tmp->$champ_id_tmp;
                } else {
                    $data_to_add[$meta['champ_id']][] = '';
                }
            }

            $data_to_add['raison'] = 'Mise à jour des propriétés générales de la fiche';

            $classNameGeneral::save($data_to_add, true);
        }

        if (isset($requestData['batiment_niveau_element_id']) && intval($requestData['batiment_niveau_element_id']) > 0) {
            Fiches::ajusterHierarchieFiltre(getOuvrageId(), intval($requestData['batiment_niveau_element_id']), $id);
        }

        if (isset($_FILES['pdf']) && is_uploaded_file($_FILES['pdf']['tmp_name'])) {
            $documentPDFs = DocumentsListesElements::getListe([
                'nom_table' => 'fiches',
                'liste_item_id' => $id,
                'is_image_principale' => 0,
                'mime_type' => 'application/pdf'
            ]);

            foreach ($documentPDFs as $documentPDF) {
                $documentPDF->delete();
            }
            DocumentsListesElements::add('fiches', $id, $_FILES['pdf']);
        } else if (!isset($_POST['document_liste_element_id'])) {
            $documentPDFs = DocumentsListesElements::getListe([
                'nom_table' => 'fiches',
                'liste_item_id' => $id,
                'is_image_principale' => 0,
                'mime_type' => 'application/pdf'
            ]);

            foreach ($documentPDFs as $documentPDF) {
                $documentPDF->delete();
            }
        }
    }

    public static function getFicheFromRevitUniqueId($revit_unique_id): ?Fiches
    {
        $fiches = array_values(self::getListe(['revit_unique_id' => $revit_unique_id]));
        return $fiches[0] ?? null;
    }

    public function afficheOnglets($disciplines, $formulairesCustoms, $isOngletEcheanciers, $isOngletInventaires, $isOngletProcessus, $isOngletDocuments, $isOnglet3Dviewer, $isOngletPDF): void
    {
        ?>
        <ul id="fiche_tabs" class="tabrow">

            <li class="<?= ('GEN' == $_GET['f_section'] ? 'selected' : '') ?> fiche_tabs" data-code="GEN" data-id="-1"
                data-fiche_id="<?= $this->id ?>">
                <?= txt('Général') ?></li>
            <?
            foreach ($disciplines as $discipline) {
                ?>
                <li class="<?= ($discipline->code == $_GET['f_section'] ? 'selected' : '') ?> fiche_tabs"
                    data-code="<?= $discipline->code ?>" data-id="<?= $discipline->id ?>"
                    data-fiche_id="<?= $this->id ?>">
                    <?= $discipline->nom ?></li>
                <?
            }

            if (isMaster()) {
                ?>
                <li class="<?= ('ENT' == $_GET['f_section'] ? 'selected' : '') ?> fiche_tabs" data-code="ENT" data-id="-1"
                    data-fiche_id="<?= $this->id ?>">
                    <?= txt('Maintenance') ?></li>
                <?
            }

            if ($isOngletEcheanciers) {
                ?>
                <li class="<?= ('ECH' == $_GET['f_section'] ? 'selected' : '') ?> fiche_tabs" data-code="ECH" data-id="-1"
                    data-fiche_id="<?= $this->id ?>">
                    <?= txt('Échéanciers') ?></li>
                <?
            }

            if ($isOngletInventaires) {
                ?>
                <li class="<?= ('INV' == $_GET['f_section'] ? 'selected' : '') ?> fiche_tabs" data-code="INV" data-id="-1"
                    data-fiche_id="<?= $this->id ?>">
                    <?= txt('Inventaire') ?></li>
                <?
            }

            if ($isOngletProcessus) {
                ?>
                <li class="<?= ('PRO' == $_GET['f_section'] ? 'selected' : '') ?> fiche_tabs" data-code="PRO" data-id="-1"
                    data-fiche_id="<?= $this->id ?>">
                    <?= txt('Processus') ?></li>
                <?
            }

            if ($isOngletDocuments) {
                ?>
                <li class="<?= ('PHO' == $_GET['f_section'] ? 'selected' : '') ?> fiche_tabs" data-code="PHO" data-id="-1"
                    data-fiche_id="<?= $this->id ?>">
                    <?= txt('Photos') ?></li>
                <?
            }

            if ($isOnglet3Dviewer) {
                ?>
                <li class="<?= ('V3D' == $_GET['f_section'] ? 'selected' : '') ?> fiche_tabs" data-code="V3D" data-id="-1"
                    data-fiche_id="<?= $this->id ?>">
                    <?= txt('3D') ?></li>
                <?
            }

            if ($isOngletPDF) {
                ?>
                <li class="<?= ('PDF' == $_GET['f_section'] ? 'selected' : '') ?> fiche_tabs" data-code="PDF" data-id="-1"
                    data-fiche_id="<?= $this->id ?>">
                    <?= txt('PDF') ?></li>
                <?
            }

            if (!empty($formulairesCustoms)) {
                foreach ($formulairesCustoms as $formCust) {
                    $codeOnglet = "custom_$formCust->id";
                    ?>
                    <li class="<?= ($codeOnglet == $_GET['f_section'] ? 'selected' : '') ?> fiche_tabs" data-code="<?= $codeOnglet ?>" data-id="-1"
                        data-fiche_id="<?= $this->id ?>">
                        <?= $formCust->nom_onglet ?></li>
                    <?
                }
            }
            ?>
        </ul>
        <?php
    }

    public function getFichier()
    {
        if (!isset($this->fichier)) {
            $this->fichier = array_values(Visualisateur3dFichiers::getListe(['fiche_id' => $this->id]))[0] ?? null;
        }
        return $this->fichier;
    }

    public function getUrlFichierIfc(): string
    {
        $fichier = $this->getFichier();
        $url = '';
        if (!empty($fichier)) {
            $url = 'index.php?action=download_fichier_ifc&id=' . $fichier->id . '&uniqid=' . uniqid();
        }
        return $url;
    }

    public function getUrlFichierXkt(): string
    {
        $fichier = $this->getFichier();
        $url = '';
        if (!empty($fichier)) {
            $url = 'index.php?action=download_fichier_xkt&id=' . $fichier->id . '&uniqid=' . uniqid();
        }
        return $url;
    }
}

?>
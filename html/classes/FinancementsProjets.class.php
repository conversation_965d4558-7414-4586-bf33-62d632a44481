<?php

/**
 * Class FinancementProjets
 */
class FinancementsProjets extends DbRow {
    public $id;
    public $numero;
	public $numero_projet;
    public $description;
	public $description_projet;
    public $fiches = array();
    public $jalons = array();
    public $jalons_modeles = '';
    public $groupes = array();
    public $ts_ajout;
    public $usager_ajout_id;
    public $usager_ajout;
    public $ts_modif;
    public $usager_modif_id;
    public $usager_modif;
    public $priorite_financement_id;
	public $priorite_financement;
    public $intervenant_1;
    public $intervenant_2;
    public $intervenant_3;
    public $intervenant_4;
	public $responsable_dossier_a_id;
	public $responsable_dossier_a;
	public $responsable_dossier_b_id;
	public $responsable_dossier_b;
	public $responsable_dossier_c_id;
	public $responsable_dossier_c;
	public $responsable_dossier_d_id;
	public $responsable_dossier_d;
	public $responsable_dossier_e_id;
	public $responsable_dossier_e;
    public $ref_nego_existante;
    public $livraison_estime;
    public $debut_contrat;
    public $expiration_contrat;
    public $ref_achat;
    public $ao;
    public $mode_financement;
    public $portee_ao;
    public $num_requisition;
    public $projet_drv;
    public $bon_commande;
    public $avant_taxes;
    public $code_taxe;
    public $cout_taxe_net;
    public $cout_projete;
    public $fournisseur;
    public $manufacturier;
    public $modele;
    public $commentaires;
    public $livraison_reserve;
    public $site_livraison;
    public $local_transitoire;
    public $statut_livraison;
    public $deficience_livraison;
    public $commentaire_logistique;
    public $echeancier_id;
	public $echeancier;
	public $echeanciers;
	public $ts_verrouille;
	public $bloquant_type_id;
	public $bloquant_type;
    public $suivi_administratif;
	public $champ_supplementaire_projet_1;
	public $champ_supplementaire_projet_2;
	public $champ_supplementaire_projet_3;
	public $champ_supplementaire_projet_4;
	public $champ_supplementaire_projet_5;
	public $champ_supplementaire_projet_6;
	public $champ_supplementaire_projet_7;
	public $champ_supplementaire_projet_8;
	public $champ_supplementaire_projet_9;
	public $champ_supplementaire_projet_10;
	public $champ_reference;
    public $nbDepots;
	public $depots;

    /**
     * Get the status legend for Gantt
     *
     * @return null
     */
    public static function getLegendeStatutsGantt()
    {
        // TODO: Move the HTML and CSS out of the logic and class.
        ?>
        <style>
            #statuts {
                margin: 0 0 40px 0;
                float: left;
            }

            #statuts > div {
                display: inline;
                margin: 0 0 0 15px;
            }
        </style>
        <div id="statuts">
            <div style="font-weight: bold;">
                <?php echo txt('Légende') ?>:
            </div>
            <?php
            $statuts = FinancementsProjets::getStatutsGantt();

            foreach ($statuts as $code => $statut) {
                ?>
                <div>
                    <?php echo $statut['nom']; ?> 
                    <div style="background-color: <?php echo $statut['color']; ?>;
                            width: 20px; height: 20px; display: inline-block;
                            position: relative; top: -2px;">
                         
                    </div>
                </div>
                <?php
            }
            ?>
        </div>
        <br style="clear: both;"/>
        <?php
    }

    /**
     * Get the different statuses
     *
     * @return array
     */
    public static function getStatuts()
    {
        return array(
            'en_cours' => array(
                'code' => 'en_cours',
                'nom' => txt('En cours', false),
                'color' => 'green'
            ),

            'complete' => array(
                'code' => 'complete',
                'nom' => txt('Complété', false),
                'color' => 'black'
            ),

            'en_retard' => array(
                'code' => 'en_retard',
                'nom' => txt('En retard', false),
                'color' => 'red'
            ),
        );
    }

    /**
     * Get the different statuses Gantt
     *
     * @return array
     */
    public static function getStatutsGantt()
    {
        return array(
            'projet' => array(
                'code' => 'projet',
                'nom' => txt('Projet'),
                'color' => '#3db9d3'
            ),

            'en_cours' => array(
                'code' => 'en_cours',
                'nom' => txt('Jalon en cours'),
                'color' => 'green'
            ),

            'complete' => array(
                'code' => 'complete',
                'nom' => txt('Jalon complété'),
                'color' => 'black'
            ),

            'en_retard' => array(
                'code' => 'en_retard',
                'nom' => txt('Jalon en retard'),
                'color' => 'red'
            ),
            'global' => array(
                'code' => 'en_retard',
                'nom' => txt('Échéancier Global'),
                'color' => '#606060'
            ),
            'jalon_global' => array(
                'code' => 'en_retard',
                'nom' => txt('Jalon d\'échéancier Global'),
                'color' => '#a0a0a0'
            ),
        );
    }


    /**
     * Show the accounts set to a specific project
     *
     * @param  int $id - id
     * @throws Exception
     */
    protected function setDataForId($id)
    {
        global $db;

        $sql = "select ap.*, concat(u_ajout.prenom, ' ', u_ajout.nom) as usager_ajout, concat(u_modif.prenom, ' ', u_modif.nom) as usager_modif,
                  (select nom_jalon from financements_projets_jalons where financement_projet_id = ap.id order by date_jalon limit 1) as titre_dernier_jalon,
                  (select max(date_jalon) from financements_projets_jalons where financement_projet_id = ap.id) as date_dernier_jalon,
                  pat.nom as priorite_financement, numero as numero_projet, description as description_projet
                from financements_projets ap
                left join priorites_financements_types pat on ap.priorite_financement_id = pat.id
                left join usagers u_ajout on u_ajout.id = ap.usager_ajout_id
                left join usagers u_modif on u_modif.id = ap.usager_modif_id
                where ap.id = ?";
        $row = $db->getRow($sql, array($id));

        $this->populateThisWithThat($row);

        $sql = "select f.id, f.*
                from financements_projets_fiches ef
                join fiches f on ef.fiche_id = f.id
                where financement_projet_id = ?
                order by f.code";
        $fiches = $db->getAssoc($sql, array($id));
        $this->fiches = isset($fiches) ? $fiches : array();

        $sql = "select ej.*,1 AS actif
                from financements_projets_jalons ej
                where financement_projet_id = ?
                order by ej.date_jalon";
        $jalons = $db->getAll($sql, array($id));

        $this->jalons = isset($jalons) ? $jalons : array();

        $sql = "select ej.*, ebmt.nom as code_equipement, ebmt.description as description_equipement, slt.nom as site_livraison, ltt.nom as local_transitoire, 
                    prix_unitaire_nouveau as cout_norme
                from financements_projets_groupes_equipements ej
	            left join equipements_bio_medicaux_types ebmt on ej.equipement_bio_medicaux_type_id = ebmt.id
                left join sites_livraisons_types slt on slt.id = ej.site_livraison_id
                left join locals_transitoires_types ltt on ltt.id = ej.local_transitoire_id
                where financement_projet_id = ?
                order by ej.numero";
        $groupes = $db->getAll($sql, array($id));

        $this->echeanciers = Echeanciers::getListe(['financement_projet_id' => $this->id, 'type' => 'financement']);

        foreach ($groupes as $i => $groupe) {
            $groupe['financement_projet_fiches'] = isset($fiches) ? $fiches : array();
            $this->groupes[$groupe['id']] = new FinancementsProjetsGroupesEquipements($groupe['id'], $groupe, false);
        }

	    $sql = "select fpd.*
                from financements_projets_depots fpd
                where financement_projet_id = ?";
	    $depots = $db->getAll($sql, array($id));

	    $this->depots = [];
	    foreach ($depots as $i => $depot) {
		    $this->depots[$depot['id']] = new FinancementsProjetsDepots($depot['id']);
	    }
    }

    private static function getFiltreData($filter) {

        global $db;

	    $where = ' and projet_id = ?';
	    $data = array(getProjetId());

	    if (isset($filter['elements_a_revise']) && $filter['elements_a_revise'] == 1) {
		    $where .= " and (SELECT financements_projet_residuel(e.id, 'com') = 1)";
	    }
	    if (isset($filter['elements_a_revise']) && $filter['elements_a_revise'] == 2) {
		    $where .= " and (SELECT financements_projet_residuel(e.id, 'dist') = 1)";
	    }
	    if (isset($filter['elements_a_revise']) && $filter['elements_a_revise'] == 3) {
		    $where .= " and getFinancementsProjetsNbFichesConflits(e.id) > 0";
	    }

	    if (isset($filter['financement_projet_id']) && $filter['financement_projet_id'] != 0 && $filter['financement_projet_id'] != '') {
		    $where .= " and e.id = ".intval($filter['financement_projet_id']);
	    }

	    if (isset($filter['date_debut']) && dm_strlen($filter['date_debut']) > 0) {
		    $where .= " and (select max(date_jalon) from financements_projets_jalons where financement_projet_id = e.id) > ?";
		    $data[] = $filter['date_debut'];
	    }

	    if (isset($filter['date_fin']) && dm_strlen($filter['date_fin']) > 0) {
		    $where .= " and (select max(date_jalon) from financements_projets_jalons where financement_projet_id = e.id) < ?";
		    $data[] = $filter['date_fin'];
	    }

	    if (isset($filter['numero_groupe']) && intval($filter['numero_groupe']) > 0) {
		    $where .= " and e.id in (select financement_projet_id from financements_projets_groupes_equipements where id = ?)";
		    $data[] = (int)$filter['numero_groupe'];
	    }

	    if (isset($filter['bon_commande']) && $filter['bon_commande'] != '') {
		    $where .= " and e.id in (select financement_projet_id from financements_projets_groupes_equipements where bon_commande = ?)";
		    $data[] = dm_str_replace(array("'",'\\'), array("''",''), $filter['bon_commande']);
	    }

        if (isset($filter['projet_id']) && is_array($filter['projet_id'])) {
            if (count($filter['projet_id']) > 0) {
                if (isset($filter['projet_id'][0]) && $filter['projet_id'][0] == -1) {
                    $where .= " and false";
                } else {
                    $where .= " and e.id in (" . db::placeHolders($filter['projet_id']) . ")";
                    $data = array_merge($data, $filter['projet_id']);
                }
            }
        } else if (isset($filter['projet_id']) && intval($filter['projet_id']) > 0) {
		    $where .= " and e.id = ?";
		    $data[] = (int)$filter['projet_id'];
	    }

	    if (isset($filter['reference']) && intval($filter['reference']) > 0) {
		    $where .= " and code_reference = ?";
		    $data[] = (int)$filter['reference'];
	    }

	    if (isset($filter['statut']) && dm_strlen($filter['statut']) > 0) {
		    $where .= " and (case 
                    when (select count(nom_jalon) from financements_projets_jalons where financement_projet_id = e.id and ts_complete is null) = 0 then 'complete'
                    when (select min(date_jalon) from financements_projets_jalons where financement_projet_id = e.id and ts_complete is null) < now() then 'en_retard'
                    else 'en_cours'
                  end) = ?";
		    $data[] = $filter['statut'];
	    }

	    if (isset($filter['titre_prochain_jalon']) && is_array($filter['titre_prochain_jalon']) && count($filter['titre_prochain_jalon']) > 0) {
	        $sqll = "select jalon from jalons_en_cours where id in (".db::placeHolders($filter['titre_prochain_jalon']).")";
	        $jalons = $db->getCol($sqll, $filter['titre_prochain_jalon']);

		    $where .= " and e.id IN (SELECT id FROM jalons_en_cours WHERE jalon in (".db::placeHolders($jalons)."))";
		    $data = array_merge($data, $jalons);
	    } else if (isset($filter['titre_prochain_jalon']) && intval($filter['titre_prochain_jalon']) > 0) {
		    $where .= " and e.id IN (SELECT id FROM jalons_en_cours WHERE jalon LIKE (SELECT jalon FROM jalons_en_cours WHERE id = ?))";
		    $data[] = (int)$filter['titre_prochain_jalon'];
	    }

	    if (isset($filter['type_groupe']) && dm_strlen($filter['type_groupe']) > 0) {
	        $equipement_bio_medicaux_type_id = $db->getOne("select id from equipements_bio_medicaux_types where nom = ?", [$filter['type_groupe']]);
		    $where .= " and exists (select id from financements_projets_groupes_equipements where financement_projet_id = e.id and equipement_bio_medicaux_type_id = ?)";
		    $data[] = (int)$equipement_bio_medicaux_type_id;
	    }

	    if (isset($filter['echeancier']) && is_array($filter['echeancier']) && count($filter['echeancier']) > 0) {
		    $where .= " and exists (select financement_projet_id from financements_projets_echeanciers where financement_projet_id = e.id and echeancier_id in (".db::placeHolders($filter['echeancier'])."))";
		    $data = array_merge($data, $filter['echeancier']);
	    } else if (isset($filter['echeancier']) && intval($filter['echeancier']) > 0) {
		    $where .= " and exists (select financement_projet_id from financements_projets_echeanciers where financement_projet_id = e.id and echeancier_id = ?)";
		    $data[] = (int)$filter['echeancier'];
	    }

	    if (isset($filter['fiche_id']) && intval($filter['fiche_id']) > 0) {
		    $where .= " and exists (select financement_projet_id from financements_projets_fiches where financement_projet_id = e.id and fiche_id = ?)";
		    $data[] = (int)$filter['fiche_id'];
	    }

	    if (isset($filter['fiche_ids']) && count($filter['fiche_ids']) > 0) {
            if (isset($filter['fiche_ids'][0]) && $filter['fiche_ids'][0] == -1) {
                $where .= " and false";
            } else {
                $where .= " and exists (select financement_projet_id from financements_projets_fiches where financement_projet_id = e.id and fiche_id in (".db::placeHolders($filter['fiche_ids'])."))";
                $data = array_merge($data, $filter['fiche_ids']);
            }
	    }

	    if (isset($filter['equipement_bio_medicaux_type_id_via_depot'])) {
            $fiches_depots = [];
            $filtreValide = false;
            $sql = "select fiche_id, concat(cast(coalesce(depot_source1_id, 0) as char(20)), '-', cast(coalesce(depot_source2_id, 0) as char(20)), '-', 
                            cast(coalesce(depot_source3_id, 0) as char(20)), '-', cast(coalesce(depot_source4_id, 0) as char(20)), '-', cast(coalesce(depot_source5_id, 0) as char(20))) as depots 
                        from equipements_bio_medicaux ";
            if (is_array($filter['equipement_bio_medicaux_type_id_via_depot'])) {
                if (count($filter['equipement_bio_medicaux_type_id_via_depot']) > 0) {
                    $whereFichesDepots = " where equipement_bio_medicaux_type_id in (".db::placeHolders($filter['equipement_bio_medicaux_type_id_via_depot']).")";
                    $dataFichesDepots = $filter['equipement_bio_medicaux_type_id_via_depot'];
                    $fiches_depots = $db->getAll($sql . $whereFichesDepots, $dataFichesDepots);
                    $filtreValide = true;
                }
            } else if (intval($filter['equipement_bio_medicaux_type_id_via_depot']) > 0) {
                $whereFichesDepots = "where equipement_bio_medicaux_type_id = ?";
                $fiches_depots = $db->getAll($sql . $whereFichesDepots, [intval($filter['equipement_bio_medicaux_type_id_via_depot'])]);
                $filtreValide = true;
            }

            if ($filtreValide) {
                $financement_projet_depot_ids = [];
                foreach ($fiches_depots as $fiche_depot) {
                    $ids = dm_explode('-', $fiche_depot['depots']);
                    foreach ($ids as $id) {
                        if ((int)$id > 0) {
                            $financement_projet_depot_ids[$id] = $id;
                        }
                    }
                }

                if (count($financement_projet_depot_ids) > 0) {
                    $where .= " and exists (select financement_projet_id from financements_projets_depots where financement_projet_id = e.id and id in (" . db::placeHolders($financement_projet_depot_ids) . "))";
                    $data = array_merge($data, $financement_projet_depot_ids);
                } else {
                    $where .= " and false";
                }
            }
	    }

	    if (isset($filter['filtre_avance']) && count($filter['filtre_avance']) > 0) {
		    $where .= " and (";
		    $iter = 0;
		    foreach ($filter['filtre_avance'] as $colonne => $valeur) {
			    if (dm_strlen(dm_trim($valeur)) > 0) {
				    $enssemble = FinancementsProjetsAdministrations::getEnssembleForChamp($colonne);
			        $colonne = FinancementsProjetsAdministrations::getColBdForChamp($colonne);
				    if ($valeur == '%non-vide%') {
					    if ($enssemble == 'projet') {
						    $where .= ($iter == 0 ? '' : ' and ') . " (trim(e.`" . db::tableColonneValide($colonne) . "`) = '' or e.`" . db::tableColonneValide($colonne) . "` is not null)";
					    } else {
						    $where .= ($iter == 0 ? '' : ' and ') . " e.id in (select financement_projet_id from financements_projets_groupes_equipements where (`" . db::tableColonneValide($colonne) . "` = '' or `" . db::tableColonneValide($colonne) . "` is not null))";
					    }
				    } else if ($valeur == '%vide%') {
					    if ($enssemble == 'projet') {
						    $where .= ($iter == 0 ? '' : ' and ') . " (trim(e.`" . db::tableColonneValide($colonne) . "`) = '' or e.`" . db::tableColonneValide($colonne) . "` is null)";
					    } else {
						    $where .= ($iter == 0 ? '' : ' and ') . " e.id in (select financement_projet_id from financements_projets_groupes_equipements where (`" . db::tableColonneValide($colonne) . "` = '' or `" . db::tableColonneValide($colonne) . "` is null))";
					    }
				    } else {
					    if ($enssemble == 'projet') {
						    $where .= ($iter == 0 ? '' : ' and ') . " e.`" . db::tableColonneValide($colonne) . "` = ?";
						    $data[] = $valeur;
					    } else {
						    $where .= ($iter == 0 ? '' : ' and ') . " e.id in (select financement_projet_id from financements_projets_groupes_equipements where `" . db::tableColonneValide($colonne) . "` = ?)";
						    $data[] = $valeur;
					    }
				    }
				    $iter++;
			    }
		    }
		    $where .= ")";
	    }

	    return array($where, $data);
    }

    /**
     * getCount
     *
     * @param  array $filter - Filters
     * @param  string $orderBy - Order by
     * @return mixed
     * @throws Exception
     */
    public static function getCount($filter = array(), $orderBy = 'nom')
    {
        global $db;

        list($where, $data) = self::getFiltreData($filter);

        $sql = "select count(*)
                from financements_projets e
                where true $where";
//	    printDebug(dm_nl2br(db::interpolateQuery($sql, $data)));
        $nb = $db->getOne($sql, $data);

        return $nb;
    }

    /**
     * Get Jalons for a specific project
     *
     * @param  int $projet_id - Id of the project
     * @param  string $jalon_id - If specified, get this specific jalon only
     * @param  string $statut - If specified, get this specific status only
     * @return array
     * @throws Exception
     */
    public static function getJalons($projet_id, $jalon_id = '', $statut = '', $projet_id_requested = 0, $date_debut = '', $date_fin = '')
    {
	    global $db;

	    $statuts = [];
	    if (is_array($statut)) {
		    $statuts = $statut;
	    } else if ($statut <> '') {
		    $statuts = [$statut];
	    }
	    if ((is_array($jalon_id) && count($jalon_id) > 0) || $jalon_id != '') {

		    $jalon_ids = is_array($jalon_id) ? $jalon_id : [$jalon_id];

		    $sql_nom_jalon = "SELECT nom_jalon FROM financements_projets_jalons WHERE id in (" . db::placeHolders($jalon_ids) . ")";
		    $noms_jalon = $db->getCol($sql_nom_jalon, $jalon_ids);

		    $where = "true";
		    $data = array();

		    if ($projet_id_requested != 0) {
			    $where .= " AND financement_projet_id = ?";
			    $data[] = $projet_id_requested;
		    }
		    if ($date_debut != '') {
			    $where .= " AND date_jalon >= ?";
			    $data[] = $date_debut;
		    }
		    if ($date_fin != '') {
			    $where .= " AND date_jalon <= ?";
			    $data[] = $date_fin;
		    }
		    if (count($statuts) > 0) {
			    $wheres_ststut = [];
			    if (in_array("complete", $statuts)) {
				    $wheres_ststut[] = "ts_complete IS NOT NULL";
			    }
			    if (in_array("en_retard", $statuts)) {
				    $wheres_ststut[] = "((date_jalon < CURDATE() OR date_jalon = CURDATE()) AND ts_complete IS NULL)";
			    }
			    if (in_array("en_cours", $statuts)) {
				    $wheres_ststut[] = "((date_jalon > CURDATE()) AND ts_complete IS NULL)";
			    }
			    $where .= " AND (" . dm_implode(" OR ", $wheres_ststut) . ")";
		    }

		    if (count($noms_jalon) > 0) {
			    $where .= " AND nom_jalon in (" . db::placeHolders($noms_jalon) . ")";
			    $data = array_merge($data, $noms_jalon);

			    $sql = "SELECT * FROM financements_projets_jalons WHERE " . $where;
			    $result = $db->getAll($sql, $data);
		    } else {
		        $result = [];
            }

	    } else {
		    $where = "financement_projet_id = ? ";
		    $data = [$projet_id];
		    if ($date_debut != '') {
			    $where .= " AND date_jalon >= ?";
			    $data[] = $date_debut;
		    }
		    if ($date_fin != '') {
			    $where .= " AND date_jalon <= ?";
			    $data[] = $date_fin;
		    }
		    if (count($statuts) > 0) {
			    $wheres_ststut = [];
			    if (in_array("complete", $statuts)) {
				    $wheres_ststut[] = "ts_complete IS NOT NULL";
			    }
			    if (in_array("en_retard", $statuts)) {
				    $wheres_ststut[] = "((date_jalon < CURDATE() OR date_jalon = CURDATE()) AND ts_complete IS NULL)";
			    }
			    if (in_array("en_cours", $statuts)) {
				    $wheres_ststut[] = "((date_jalon > CURDATE()) AND ts_complete IS NULL)";
			    }
			    $where .= " AND (" . dm_implode(" OR ", $wheres_ststut) . ")";
		    }
		    $sql = "SELECT * FROM financements_projets_jalons WHERE " . $where;
		    $result = $db->getAll($sql, $data);
	    }


	    return $result;
    }

    /**
     * Get the Global Echeancier for a Projet
     *
     * @param  string $projet_id     - Id of the project
     * @param  string $echeancier_id - Id if the echeancier
     * @return array|null
     * @throws Exception
     */
    public static function getEcheancierOfProjectGlobal($projet_id = '', $echeancier_id = '')
    {
        global $db;

        $where = 'true';
        $data = [];
        if ($projet_id != '') {
            $where .= " AND echeancier_id in (select echeancier_id from financements_projets_echeanciers where financement_projet_id = ?)";
	        $data[] = $projet_id;
        }
        if ($echeancier_id != '') {
            $where .= " AND ef.echeancier_id = ?";
	        $data[] = $echeancier_id;
        }

        if ($projet_id != '') {
            $sql = "SELECT DISTINCT ef.echeancier_id, ef.fiche_id
              FROM echeanciers_fiches ef
              WHERE " . $where;

        } else {
            $sql = "SELECT DISTINCT ef.echeancier_id
              FROM echeanciers_fiches ef
              JOIN financements_projets_fiches apf USING (fiche_id)";
        }
        $results = $db->getAll($sql, $data);

	    $sql = "SELECT e.id, e.nom, e.description, min(date_debut) as date_debut, max(date_fin) as date_fin, e.id
                FROM echeanciers e
                join echeanciers_jalons ej on e.id = ej.echeancier_id
                group by e.id, e.nom, e.description";
	    $echanciers_infos = $db->getAssoc($sql);

        foreach ($results as $result) {

            $echancier_info = $echanciers_infos[$result['echeancier_id']];

            $date_deb = dm_explode("-", $echancier_info['date_debut']);
            $date_debut = $date_deb['2'] . "-" . $date_deb['1'] . "-" . $date_deb['0'];

            $datetime1 = new DateTime($echancier_info['date_debut']);
            $datetime2 = new DateTime($echancier_info['date_fin']);

            $difference = $datetime1->diff($datetime2);

            $echanciers_projet[$echancier_info['id']] = array(
                'info' => $echancier_info,
                'date_debut' => $date_debut,
                'duration' => $difference->d
            );

        }

        if (isset($echanciers_projet)) {
            return $echanciers_projet;
        } else {
            return array();
        }

    }


    /**
     * Listing function of Financement
     *
     * @param  array  $filter  - Filters
     * @param  string $orderBy - Order
     * @return array
     * @throws Exception
     */
    public static function getListe($filter = [], $orderBy = 'numero')
    {
        global $db;
	    list($where, $data) = self::getFiltreData($filter);

        $sql = "select e.*, e.id, concat(e.numero, ' - ', e.description) as projet, 
                  (select nom_jalon from financements_projets_jalons where financement_projet_id = e.id and ts_complete is null order by date_jalon limit 1) as titre_prochain_jalon,
                  (select min(date_jalon) from financements_projets_jalons where financement_projet_id = e.id and ts_complete is null) as date_prochain_jalon,
                  (select nom_jalon from financements_projets_jalons where financement_projet_id = e.id order by date_jalon limit 1) as titre_dernier_jalon,
                  (select max(date_jalon) from financements_projets_jalons where financement_projet_id = e.id) as date_dernier_jalon,
                  case 
                    when (select count(nom_jalon) from financements_projets_jalons where financement_projet_id = e.id and ts_complete is null) = 0 then 3
                    when (select min(date_jalon) from financements_projets_jalons where financement_projet_id = e.id and ts_complete is null) < now() then 2
                    else 1 
                  end as statut, pat.nom as priorite_financement, numero as numero_projet, description as description_projet, commentaires as commentaires_projet, bt.nom as bloquant_type
                from financements_projets e
                left join priorites_financements_types pat on e.priorite_financement_id = pat.id
                left join bloquants_types bt on bt.id = bloquant_type_id 
                where true $where
                order by $orderBy";
                //printDebug(dm_nl2br(db::interpolateQuery($sql, $data)));
        $rows = $db->getAssoc($sql, $data);

        $fiches = array();
        $jalons = array();
        $groupes = array();
	    $echeanciers = array();
        $financement_projet_ids = array_keys($rows);

        if (count($financement_projet_ids) > 0) {
            if (isset($filter['includeFiches'])) {
	            $sql = "select f.*, financement_projet_id
	                from financements_projets_fiches ef
	                join fiches f on ef.fiche_id = f.id
	                where financement_projet_id in (" . db::placeHolders($financement_projet_ids) . ")
                    order by f.code";
	            $fiches_tmp = $db->getAll($sql, $financement_projet_ids);

	            foreach ($fiches_tmp as $row_col) {
		            $fiches[intval($row_col['financement_projet_id'])][$row_col['id']] = $row_col;
	            }
            }
            $sql = "select ej.*
	                from financements_projets_jalons ej
	                where financement_projet_id in (" . db::placeHolders($financement_projet_ids) . ")
                    order by ej.date_jalon";
            $jalons_tmp = $db->getAll($sql, $financement_projet_ids);

            foreach ($jalons_tmp as $row_col) {
                $jalons[intval($row_col['financement_projet_id'])][] = $row_col;
            }

            $sql = "select ej.*, ebmt.nom as code_equipement, ebmt.description as description_equipement, 
                    prix_unitaire_nouveau as cout_norme
	                from financements_projets_groupes_equipements ej
	                join equipements_bio_medicaux_types ebmt on ej.equipement_bio_medicaux_type_id = ebmt.id
	                where financement_projet_id in (" . db::placeHolders($financement_projet_ids) . ")
                    order by ej.numero";
            $groupes_tmp = $db->getAll($sql, $financement_projet_ids);

            foreach ($groupes_tmp as $row_col) {
                $groupes[intval($row_col['financement_projet_id'])][] = new FinancementsProjetsGroupesEquipements($row_col['id'], $row_col);
            }

	        $sql = "select e.*, ape.financement_projet_id
	                from echeanciers e
                    join financements_projets_echeanciers ape on ape.echeancier_id = e.id
	                where ape.financement_projet_id in (" . db::placeHolders($financement_projet_ids) . ")
                    order by e.nom";
	        $echeanciers_tmp = $db->getAll($sql, $financement_projet_ids);

	        foreach ($echeanciers_tmp as $row_col) {
		        $echeanciers[intval($row_col['financement_projet_id'])][] = new Echeanciers($row_col['id'], $row_col);
	        }
        }

        $liste = array();

        $goon = true;
        foreach ($rows as $i => $row) {
	        if (isset($filter['includeFiches'])) {
		        $row['fiches'] = isset($fiches[intval($row['id'])]) ? $fiches[intval($row['id'])] : array();
	        }
            $row['jalons'] = isset($jalons[intval($row['id'])]) ? $jalons[intval($row['id'])] : array();
            $row['groupes'] = isset($groupes[intval($row['id'])]) ? $groupes[intval($row['id'])] : array();
	        $row['echeanciers'] = isset($echeanciers[intval($row['id'])]) ? $echeanciers[intval($row['id'])] : array();
	        $liste[intval($row['id'])] = new self(intval($row['id']), $row);
        }
        return $liste;
    }

    public function getGroupesFiches()
    {
        global $db;
	    $sql = "select  concat(f.id, '-', fpge.id) as cle,
                        f.id as fiche_id,
                        fpge.equipement_bio_medicaux_type_id,
                        f.code as local,
                        fpge.id as groupe_id,
                        COALESCE(fpgef.quantite_requise,0) as requise_init, 
                        COALESCE(fpgef.quantite_existante,0) as existante_init, 
                        COALESCE(fpgef.quantite_demenager,0) as demenager_init, 
                        COALESCE(fpgef.quantite,0) as commande, 
                        COALESCE(fpgef.quantite_distribuee,0) as distribue,
                        COALESCE(fpgef.quantite,0) as quantite,
                        COALESCE(fpgef.quantite_requise,0) as quantite_requise,
                        COALESCE(fpgef.quantite_existante,0) as quantite_existante,
                        COALESCE(fpgef.quantite_distribuee,0) as quantite_distribuee,
                        COALESCE(fpgef.quantite_demenager,0) as quantite_demenager,
                        COALESCE(fpgef.prix_unitaire_autorise,0) as prix_unitaire_autorise,
                        COALESCE(fpgef.quantite_autorise,0) as quantite_autorise,
                        COALESCE(fpgef.cout_supplementaire_1,0) as cout_supplementaire_1,
                        COALESCE(fpgef.cout_supplementaire_2,0) as cout_supplementaire_2,
                        COALESCE(fpgef.cout_supplementaire_3,0) as cout_supplementaire_3,
                        COALESCE(fpgef.cout_supplementaire_4,0) as cout_supplementaire_4,
                        COALESCE(fpgef.cout_supplementaire_5,0) as cout_supplementaire_5,
                        fpgef.colonne_supplementaire_detail_1,
                        fpgef.colonne_supplementaire_detail_2,
                        fpgef.colonne_supplementaire_detail_3,
                        fpgef.colonne_supplementaire_detail_4,
                        fpgef.colonne_supplementaire_detail_5,
                        fpgef.colonne_supplementaire_detail_6,
                        fpgef.colonne_supplementaire_detail_7,
                        fpgef.colonne_supplementaire_detail_8,
                        fpgef.colonne_supplementaire_detail_9,
                        fpgef.colonne_supplementaire_detail_10
                from financements_projets_groupes_equipements_fiches fpgef
                join financements_projets_groupes_equipements fpge on fpge.id = fpgef.financement_projet_groupe_equipement_id
                join fiches f on f.id = fpgef.fiche_id
                where financement_projet_id = ?";
	    $lignes_1 = $db->getAssoc($sql, [$this->id]);

	    $sql = "SELECT concat(e.fiche_id, '-', fpge.id) as cle,
                        e.equipement_bio_medicaux_type_id, 
                        e.fiche_id, 
                        ebmt.nom as code_equipement, 
                        ebmt.description as description_equipement,
                        fpge.cout_projete,
                        fpge.cout_taxe_net,
                        ebmt.prix_unitaire_nouveau as cout_norme,   
                        fpge.quantite_disponible,
                        fpge.id as groupe_id, 
                        sum(coalesce(qte_requise, 0)) AS requise_actuelle, 
                        sum(coalesce(qte_existante, 0)) AS existante_actuelle, 
                        sum(coalesce(qte_conservee, 0)) AS demenager_actuelle,
                        sum(coalesce(qte_source1, 0)) AS qt_portefeuille_1,
                        sum(coalesce(qte_source2, 0)) AS qt_portefeuille_2,
                        sum(coalesce(qte_source3, 0)) AS qt_portefeuille_3,
                        sum(coalesce(qte_source4, 0)) AS qt_portefeuille_4,
                        sum(coalesce(qte_source5, 0)) AS qt_portefeuille_5,
                        COALESCE((SELECT SUM(quantite) FROM financements_projets_groupes_equipements_fiches
                        WHERE fiche_id = e.fiche_id AND financement_projet_groupe_equipement_id IN  
                        (SELECT id FROM financements_projets_groupes_equipements
                        WHERE equipement_bio_medicaux_type_id = e.equipement_bio_medicaux_type_id)), 0) AS deja_traite
                FROM equipements_bio_medicaux e
                JOIN equipements_bio_medicaux_types ebmt on e.equipement_bio_medicaux_type_id = ebmt.id
                JOIN financements_projets_groupes_equipements fpge on fpge.equipement_bio_medicaux_type_id = e.equipement_bio_medicaux_type_id and financement_projet_id = ?
                WHERE e.projet_id = (SELECT projet_id FROM financements_projets WHERE id = fpge.financement_projet_id) 
                  AND EXISTS (SELECT fiche_id FROM financements_projets_fiches WHERE financement_projet_id = fpge.financement_projet_id AND fiche_id = e.fiche_id)
                GROUP BY e.equipement_bio_medicaux_type_id, code_equipement, description_equipement, e.fiche_id";
	    $lignes_2 = $db->getAssoc($sql, [$this->id]);

	    $lignes = [];
	    foreach ($lignes_1 as $cle => $fiche_1) {
		    if (isset($lignes_2[$cle])) {
		        $fiche_2 = $lignes_2[$cle];
		        $fiche = [];
			    foreach ($fiche_1 as $col => $data) {
				    $fiche[$col] = $data;
			    }
			    foreach ($fiche_2 as $col => $data) {
				    $fiche[$col] = $data;
			    }
			    $lignes[] = $fiche;
            }
        }

        return $lignes;
    }

    /**
     * Listing function of Projets
     *
     * @param  array  $filter  - Filters
     * @param  string $orderBy - Order
     * @return array
     * @throws Exception
     */
    public static function getListeProjets($filter = [], $orderBy = 'numero', $sql_limit = '')
    {
        global $db;
	    list($where, $data) = self::getFiltreData($filter);

        $sql = "select e.*, e.id, e.numero, e.description, 
                  (select nom_jalon from financements_projets_jalons where financement_projet_id = e.id and ts_complete is null order by date_jalon limit 1) as titre_prochain_jalon,
                  (select min(date_jalon) from financements_projets_jalons where financement_projet_id = e.id and ts_complete is null) as date_prochain_jalon,
                  (select nom_jalon from financements_projets_jalons where financement_projet_id = e.id order by date_jalon limit 1) as titre_dernier_jalon,
                  (select max(date_jalon) from financements_projets_jalons where financement_projet_id = e.id) as date_dernier_jalon,
                  case 
                    when (select count(nom_jalon) from financements_projets_jalons where financement_projet_id = e.id and ts_complete is null) = 0 then 3
                    when (select min(date_jalon) from financements_projets_jalons where financement_projet_id = e.id and ts_complete is null) < now() then 2
                    else 1 
                  end as statut, bt.nom as bloquant_type, (select count(*) from financements_projets_depots where financement_projet_id = e.id) as nbDepots
                from financements_projets e
                left join bloquants_types bt on bt.id = bloquant_type_id 
                where true $where
                order by $orderBy ".$sql_limit;
//	    printDebug(dm_nl2br(db::interpolateQuery($sql, $data)));
        $rows = $db->getAssoc($sql, $data);

        $liste = array();

        foreach ($rows as $row) {
	        $liste[intval($row['id'])] = new self(intval($row['id']), $row);
        }
        return $liste;
    }

    public static function getListeProjets_simple($filter = [])
    {
        global $db;

        list($where, $data) = self::getFiltreData($filter);

         $sql = "SELECT id, numero, description, description as description_projet, id, numero as nom
                FROM financements_projets e
                WHERE true $where
                ORDER BY numero";
        $rows = $db->getAll($sql, $data);

        $liste = array();

        foreach ($rows as $row) {
	        $liste[intval($row['id'])] = new self(intval($row['id']), $row);
        }
        return $liste;
   }
    
    /**
     * Listing function of Financement par projet
     *
     * @param  Integer	$projet_id
     * @return array
     * @throws Exception
     */
    public static function getListeParProjet($projet_id = 0)
    {
        global $db;

        $row = array();
        $row['jalons'] = array();
        $row['groupes'] = array();

        if ($projet_id > 0) {
            $sql = "select ej.*
	                from financements_projets_jalons ej
	                where financement_projet_id = ?
                    order by ej.date_jalon";
            $jalons_tmp = $db->getAll($sql, array($projet_id));

            foreach ($jalons_tmp as $row_col) {
                $row['jalons'][] = $row_col;
            }

            $sql = "select ej.*, ebmt.nom as code_equipement, ebmt.description as description_equipement, 
                      case when equipement_bio_medicaux_type_id is null then 1 else 0 end as is_donnees_denormalisees, prix_unitaire_nouveau as cout_norme
	                from financements_projets_groupes_equipements ej
	                left join equipements_bio_medicaux_types ebmt on ej.equipement_bio_medicaux_type_id = ebmt.id
	                where financement_projet_id = ?
                    order by ej.bon_commande, is_donnees_denormalisees";
            $groupes_tmp = $db->getAll($sql, array($projet_id));

            foreach ($groupes_tmp as $row_col) {
                $row['groupes'][] = new FinancementsProjetsGroupesEquipements($row_col['id'], $row_col);
            }
        }

        return $row;
    }

	public static function getOptions($filter = array(), $selected = 0, $includeEmpty = false)
	{
		if($includeEmpty)
		{
			?>
            <option value=""> </option>
			<?php
		}
		$rows = self::getListe($filter);
		foreach($rows as $i => $row)
		{
			$sel = $row->id == $selected ? ' selected="selected"' : '';
            ?>
            <option value="<?=$row->id?>"<?=$sel?>><?=$row->numero." - ".$row->description?></option>
            <?php
		}
	}

    static public function getFinancementsJalonGroupName($project_id_requested = '')
    {
        global $db;

        if ($project_id_requested != '' && $project_id_requested != 0) {
            $sql_jalons = "SELECT * FROM financements_projets_jalons WHERE financement_projet_id = '".$project_id_requested."' ORDER BY nom_jalon ASC";
        } else {
            $sql_jalons = "SELECT * FROM financements_projets_jalons ORDER BY nom_jalon ASC";
        }
        $result_jalons = $db->getAll($sql_jalons);

        $jalons = array();

        foreach ($result_jalons as $jalon){

            if (!in_array($jalon['nom_jalon'], array_column($jalons, 'nom_jalon'))) {
                $jalons[] = array('id' => $jalon['id'], 'nom_jalon' => $jalon['nom_jalon']);

            }

        }

        return $jalons;
    }


    /**
     * Editor of field
     *
     * @param  int $iteration - ??
     * @param  bool $only_champs_denormalises - ??
     * @return null
     */
    public function getEditorHTML($iteration = 0, $only_champs_denormalises = false)
    {
        // TODO: Remove CSS and HTML from Class and Logic

        global $db;
        $admin = FinancementsProjetsAdministrations::getAdminForProjet();
        ?>
        <script>
            $(document).ready(() => {
            	$('#echeancier_id').change(() => {
					appliqueFiltre(1);
                });

            	$('select.combobox').combobox();

                init();
            });
            
            function init() {
                $('.complete_txt').change(function() {
               		var item_id = $(this).parent().data("ordre");
                	if ($(this).prop('checked') == true) {
                		for (i=item_id-1; i > 0; i--) {
                			if ($("#jalon_"+i).data("etat") == '1' && $("#complete_"+i).prop('checked') == false) {
                				$("#complete_"+item_id).prop('checked','');
                			}
                		}
                		$(this).parent().find(".date_jalon_txt").prop("readonly",'true').css({"position":"relative","z-index":"-1"});
                		$(this).parent().find(".nb_jours_jalon_txt").prop("readonly",'true');
                	}
                	else {
                 		$(this).parent().parent().find("div").each(function() {
                 			if ($(this).data("ordre") >= item_id) {
				         		$(this).find(".date_jalon_txt").prop("readonly",'').css({"z-index":"0"});
				        		$(this).find(".nb_jours_jalon_txt").prop("readonly",'');
                 				$(this).find(".complete_txt").prop('checked','');
                 			}
                 		});
                	}
            	});
            	
            	$('.del_jalon').click(function() {
            		let p = $(this).parent();
            		if (p.data("etat") == 1) {
	            		p.fadeTo(200, 0.3);
	            		p.data("etat", 0);
	            		p.find(".jalon_actif").val(0);
	         //   		p.find(".date_jalon_txt").val('');
	            		p.find(".complete_txt").prop('checked','');
	            		p.find('.fields').css({pointerEvents: 'none'});
	            		p.find('.del_jalon').find('img').attr('src', 'css/images/icons/dark/cross_3.png');
	            	}
	            	else {
	            		p.fadeTo(200, 1);
	            		p.data("etat", 1);
	            		p.find(".jalon_actif").val(1);
						p.find('.fields').css({pointerEvents: 'auto'});
						p.find('.del_jalon').find('img').attr('src', 'css/images/icons/dark/tick.png');
	            	}
            	});
            	
            	$('#jalons').find(".top_block").each(function(index) {
            		if ( $(this).data("etat") == '0' ) {
            			$(this).fadeTo(200, 0.3);
            		}
            	});
            }
             
            function jalons_calcul(fonc, item) {
            	let table = [];
            	$("#erreurs").remove();
            	$('#jalons').find(".top_block").each(function(index) {
		        	if ($(this).data("ordre") > 0) {
		        		let cpl = ($(this).find(".complete_txt").prop('checked'))? 1 : 0;
		        		let d = [
		        			$(this).find(".date_jalon_txt").val(),
		        			$(this).find(".nb_jours_jalon_txt").val(),
		        			$(this).data("etat"),
		        			cpl
		        			];
		        		table.push(d);
		        	}
            	});
            	let data = {fonc: fonc, item: item, table: table};
            	$.post('financement/jalons_calcul_financements.php', data, function(data) {
					if (data.statut == 'ERREUR') {
						$("#bloc_erreur").html('<b id="erreurs" style="color: red; padding: 0 0 0 20px"></b>');
						$("#erreurs").html("La date du/des jalons marqués comme complétés n'a pas été modifiée lors du recalcul.");
						parent.fancybox_resize();
					}
			//		else if (data.statut == 'OK') {
						let table = data.table;
						for (i=0; i < table.length; i++) {
							$("#date_jalon_"+(i+1)).val(table[i][0]);
						}
			//		}
				}, 'json');
            }
            
            function pour_save() {
				if($('#numero').length)
				{
					$("#erreurs").remove();
					let message = '';
					let pattern = /^[A-Z0-9]+$/g;
					if ($('#numero').val().length < 5 || $('#numero').val().length > 15)
						message += "Le numéro doit comporter entre 5 et 15 caractères.\n";
					
					if (pattern.test($('#numero').val()) === false)
						message += "Le numéro doit être composé de seulement des caractères majuscules.\n";
					
					if (message != '') {
						$("#bloc_erreur").html('<b id="erreurs" style="color: red;"></b>');
						$("#erreurs").html(message);
						parent.fancybox_resize();
						return false;
					}
				}
				
				var derniere_date = '';
				$('.date_jalon_txt').each(function() {
					if ( $(this).parent().data("etat") == 1 ) {
						if ( $(this).val() > derniere_date )
							derniere_date = $(this).val();
						else {
							derniere_date = '-1'
							return false;
						}
					}
				});
				if (derniere_date == '-1') {
					$("#bloc_erreur").html('<b id="erreurs" style="color: red;"></b>');
					$("#erreurs").html("Les dates des jalons sont mal ordonnancées.");
					parent.fancybox_resize();
					return false;
				}
            }
        </script>
        <style>
            span.custom-combobox input {
                width: 222px!important;
            }
            .custom-combobox-toggle {
                top: -2px!important;
            }
            ul.ui-menu{
                max-height: 300px; overflow-y: scroll;
            }
            #popupContent {
                max-height: 1500px !important;
            }

            .blocsFiche, #fiche table, #fiche tr, #fiche td, #fiche th, .ui-dialog table, .ui-dialog tr, .ui-dialog td, .ui-dialog th {
                background: none !important;
            }

            .blocsFicheTitre {
                margin-top: 0px !important;
            }

            .span_mise_en_page {
                display: block !important;
            }

            .span_mise_en_page label {
                display: inline-block !important;
                background: none !important;
                position: relative;
                top: -6px;
                margin-bottom: 5px;
                font-size: 13px;
            }

            #blocDonnees select, #blocFiltre select {
                width: 260px !important;
            }

            div.comboselectbox.searchable div.combowrap:first-child ul.comboselect {
                height: 90% !important;
            }

            div.comboselectbox div.combowrap {
                padding: 10px !important;
                width: 175px !important;
            }

            li.used a.add {
                background-image: none !important;
            }

            #blocDonnees input, #blocDonnees textarea {
                width: 300px !important;
                box-sizing: border-box;
            }

            #jalons input, #jalons span {
                margin-left: 5px;
                padding-left: 3px;
            }

            #jalons input.nom_jalon_txt, #jalons span.nom_jalon_txt {
                width: 175px;
            }

            #jalons input.date, #jalons span.date {
                width: 70px;
            }

            #jalons input.nb_jours_jalon_txt, #jalons span.nb_jours_jalon_txt {
                width: 20px;
            }

            #jalons span.btns {
                width: 13px;
            }

            #jalons span {
                display: inline-block;
                font-weight: bold;
            }

            #jalons .jalons {
                margin-bottom: 5px;
                height: 22px;
            }

            fieldset.cotacote {
                margin: 0 0 17px 30px;
                width: 42%;
                float: left;
            }

            fieldset.cotacote section > div {
                width: 150px !important;
            }

            #editables fieldset.cotacote label {
                width: 240px !important;
            }

            fieldset.cotacote input[type=text], fieldset.cotacote select {
                min-width: 150px !important;
                width: 150px !important;
            }

            fieldset.cotacote textarea {
                height: 50px !important;
                min-height: 50px !important;
            }
        </style>
        <fieldset>

            <div style="float: left;">
                <div id="blocDonnees" style="padding: 0 0 0 30px; width: 620px;">
                    <b style="font-weight: bold;"><?= txt('Identification du projet de financement:') ?></b><br/><br/>
                    <table>
                        <?php
                        if (isset($_GET['after_form_action'])) {
                            echo "<input type=\"hidden\" name=\"after_form_action\" value=\"" . ($_GET['after_form_action']) . "\">";
                        }

                        if ($admin->isChampActif('numero_projet')) {
                        ?>
                            <tr style="width: 450px;  display: table-row;">
                                <td style="width: 100px; padding-bottom: 5px!important; color: red;"><?= $admin->getLabelForChamp('numero_projet') ?></td>
                                <td style="padding-bottom: 5px!important;"><input type="text" id="numero" name="numero"
                                                                                  value="<?= $this->numero ?>"/></td>
                            </tr>
<?php
                        }

                        if ($admin->isChampActif('description_projet')) {
?>
                        <tr style="width: 450px; margin-bottom: 5px; display: table-row;">
                            <td style="width: 100px; vertical-align: top;"><?= $admin->getLabelForChamp('description_projet') ?></td>
                            <td><textarea style="margin-bottom: 2px;" id="description"
                                          name="description"><?= ($this->description) ?></textarea></td>
                        </tr>
	                        <?php
                        }

                        if ($admin->isChampActif('champ_reference')) {
	                        ?>
                            <tr style="width: 450px; margin-bottom: 5px; display: table-row;">
                                <td style="width: 100px; vertical-align: top; padding-bottom: 5px;"><?= $admin->getLabelForChamp('champ_reference') ?></td>
                                <td style="padding-bottom: 5px!important;"><select name="champ_reference">
                                        <option value=""> </option>
				                        <?php
				                        foreach (self::getChampsReference() as $champ => $label) {
					                        ?>
                                            <option value="<?= $champ ?>"<?= ($champ == $this->champ_reference ? ' selected="selected"' : '') ?>><?= $label ?></option>
					                        <?php
				                        }
				                        ?>
                                    </select></td>
                            </tr>
	                        <?php
                        }

                        if ($admin->isChampActif('commentaires_projet')) {
                        ?>
                        <tr style="width: 450px; margin-bottom: 2px; display: table-row;">
                            <td style="width: 100px; vertical-align: top"><?= $admin->getLabelForChamp('commentaires_projet') ?></td>
                            <td><textarea id="commentaires" name="commentaires" style="margin-bottom: 2px;"><?= ($this->commentaires) ?></textarea>
                            </td>
                        </tr>
	                        <?php
                        }

                        if ($admin->isChampActif('priorite_financement')) {
                        ?>
                        <tr style="width: 450px; margin-bottom: 5px; display: table-row;height: 27px">
                            <td style="width: 100px; vertical-align: top; padding-bottom: 5px"><?= $admin->getLabelForChamp('priorite_financement') ?></td>
                            <td style="padding-bottom: 5px!important;">
	                            <?php
	                            if (havePermission('priorité_projet_financement')) {
	                            ?>
                                    <select name="priorite_financement_id">
                                        <option value=""> </option>
                                        <?php
                                        PrioritesFinancementsTypes::getOptions(array(), $this->priorite_financement_id);
                                        ?>
                                    </select>
		                            <?php
	                            } else {
		                            print $this->priorite_financement;
	                            }
	                            ?>
                            </td>
                        </tr>
	                        <?php
                        }

                        if ($admin->isChampActif('bloquant_type')) {
                        ?>
                        <tr style="width: 450px; margin-bottom: 5px; display: table-row;">
                            <td style="width: 100px; vertical-align: top; padding-bottom: 5px"><?= $admin->getLabelForChamp('bloquant_type') ?></td>
                            <td style="padding-bottom: 5px!important;"><select name="bloquant_type_id">
                                    <option value=""> </option>
				                    <?php
				                    BloquantsFinancementsTypes::getOptions(array(), $this->bloquant_type_id);
				                    ?>
                                </select></td>
                        </tr>
<?php
                        }
?>
                    </table>
                </div>
                <?php
                if ($admin->isBlocActif([
                    'intervenant_1', 'intervenant_2', 'intervenant_3', 'intervenant_4',
					'responsable_dossier_a_id', 'responsable_dossier_b_id', 'responsable_dossier_c_id', 'responsable_dossier_d_id', 'responsable_dossier_e_id', 
                    'champ_supplementaire_projet_1', 'champ_supplementaire_projet_2', 'champ_supplementaire_projet_3', 'champ_supplementaire_projet_4',
	                'champ_supplementaire_projet_5', 'champ_supplementaire_projet_6', 'champ_supplementaire_projet_7', 'champ_supplementaire_projet_8',
	                'champ_supplementaire_projet_9', 'champ_supplementaire_projet_10'
                ])) {
                ?>
                <div id="blocDonnees1" style="padding: 30px 0 0 30px; width: 450px;">
                    <b style="font-weight: bold;"><?=txt('Responsables du dossier')?></b><br/><br/>
                    <table>
                        <?php
                        for ($i = 1; $i <= 4; $i++) {
	                        $champ = 'intervenant_' . $i;
                            if ($admin->isChampActif($champ)) {
                            ?>
                            <tr style="width: 450px; margin-bottom: 5px; display: table-row;">
                                <td style="width: 100px; height: 27px;"><?= $admin->getLabelForChamp($champ) ?></td>
                                <td>
                                    <select data-name="<?= $champ ?>" class="combobox" data-position="bottom">
                                        <option value=""> </option>
                                        <?php
                                        $allValues = self::getAllDistinctValeurColonne($champ);
                                        foreach ($allValues as $value) {
                                            ?>
                                            <option value="<?= $value ?>"<?= ($value == dm_trim($this->$champ) ? ' selected="selected"' : '') ?>><?= $value ?></option>
                                            <?php
                                        }
                                        ?>
                                    </select>
                                </td>
                            </tr>
                                <?php
                            }
                        }
                        for ($lettre = 'a'; $lettre <= 'e'; $lettre++) {
				                $nom_champ = 'responsable_dossier_' . $lettre . '_id';
                                $class = "FinancementsResponsablesDossiers" . strtoupper($lettre) . "Types";
				                if ($admin->isChampActif($nom_champ)) {
					                ?>
                                    <tr style="width: 450px; margin-bottom: 5px; display: table-row;">
                                        <td style="width: 100px; height: 27px;"><?= $admin->getLabelForChamp($nom_champ) ?></td>
                                        <td>
                                            <select name="<?= $nom_champ ?>">
                                                <option value=""> </option>
								                <?php
								                $class::getOptions([], $this->$nom_champ);
								                ?>
                                            </select>
                                        </td>
                                    </tr>
                                <?php
                            }
                        }

                        for ($i = 1; $i <= 10; $i++) {
	                        $champ = 'champ_supplementaire_projet_' . $i;
	                        if ($admin->isChampActif($champ)) {
		                        ?>
                                <tr style="width: 450px; margin-bottom: 5px; display: table-row;">
                                    <td style="width: 100px; height: 27px;"><?= $admin->getLabelForChamp($champ) ?></td>
                                    <td>
                                        <input type="text" id="<?= $champ ?>" name="<?= $champ ?>" value="<?= $this->$champ ?>" style="max-width: 370px" />
                                    </td>
                                </tr>
		                        <?php
	                        }
                        }
                        ?>
                    </table>
                </div>
                <?php
                }

                if (self::isGestionDesJalons()) {
                ?>
                <div id="blocJalons" style="padding: 40px 0 0 30px; width: 600px;">
                    <b style="color: blue; cursor:pointer;" onclick="$('#jalons_modeles_liste').show()"><?= txt('Sélection du jalon modèle') ?></b>
                    <select style="display:none; margin-left : 27px;" id="jalons_modeles_liste" onchange="change_jalon_modele(this.value)">
                        <option value="">Jalons modèles</option>
                        <?php
                        global $db;
                        $sql_jalon_modele = "SELECT groupe_jalon,MIN(id) AS id FROM financements_jalons_modeles GROUP BY groupe_jalon ORDER BY groupe_jalon ASC";
                        $jalon_modeles = $db->getAll($sql_jalon_modele);

                        foreach ($jalon_modeles as $jalon_modele) {
							echo '<option value="' . $jalon_modele['id'] . '">' . $jalon_modele['groupe_jalon'] . '</option>';
                        }
                        ?>
                    </select><br><br>
				<div>
					<b style="font-weight: bold ;"><?= txt('Jalon modèle pré-enregistré:') ?></b>    	
					<input type="text" name="jalons_modeles" id="jalons_modeles" style="margin-left : 15px; min-height: 20px;" readonly value="<?php echo $this->jalons_modeles ?>">
                
                    <div id="bloc_taches" style="width: 670px; margin-bottom: 5px;"></div>
                        <div id="jalons">
                            <div class="top_block" data-ordre="0">

                                <span class="btns"> </span>

                                <span class="nom_jalon_txt">Nom</span>

                                <span class="date" style="margin-left: 125px;">Durée</span>

                                <span class="date" style="margin-left: 3px;">Échéance</span>

                                <span class="complete">Complété</span>

                            </div>
                            <?php
                            if (isset($this->jalons) && is_array($this->jalons)) {
                                if (count($this->jalons) == 0) {
                                    $this->jalons = array(
                                        array(
                                            'nom_jalon' => '',
                                            'nb_jours_jalon' => '',
                                            'date_jalon' => '',
                                            'ts_complete' => '',
	                                        'id_jalon' => '',
	                                        'actif' => ''
                                        )
                                    );
                                }

								$sql = "select *, 0 AS actif
										from financements_projets_jalons_non_utilises
										where financement_projet_id = ?
										order by id_jalon";
								$jalons_non_utilises = $db->getAll($sql, array($this->id));

                                $a = 1;
                                $jalons = array_merge($this->jalons, $jalons_non_utilises);
                                usort($jalons, function($a, $b) {
									return $a['id_jalon'] - $b['id_jalon'];
								});
                                foreach ($jalons as $jalon) {
                                    $complete = (isset($jalon['ts_complete']) && dm_strlen($jalon['ts_complete']) > 0);

                                    ?>
                                    <div id="jalon_<?php print $a; ?>" data-ordre="<?php print $a; ?>"
                                         class="top_block" data-etat="<?= $jalon['actif'] ?>">

                                        <input type="hidden" name="jalon_actif[]" class="jalon_actif" value="<?= $jalon['actif'] ?>">
                                        <a href="javascript: void(0);" class="del_jalon">
                                            <img src="css/images/icons/dark/<?=($jalon['actif'] == 1 ? 'tick' : 'cross_3')?>.png"/></a>
                                        <span class="fields" style="<?=($jalon['actif'] == 0 ? 'pointer-events: none;' : '')?>">
                                            <input type="text" style="width: 275px" name="nom_jalon[]" class="nom_jalon_txt"
                                                   value="<?= ($jalon['nom_jalon']) ?>" readonly />
                                            <input type="number" min="1" name="nb_jours_jalon[]" id="nb_jours_jalon_<?= $a ?>" style="margin-left: 10px; width:40px;"
                                                   class="nb_jours_jalon_txt justInteger"
                                                   value="<?= $jalon['nb_jours_jalon'] ?>" <?= ($complete ? 'readonly' : '') ?> autocomplete="off" /> <?= txt('jours') ?>
                                            <input type="text" name="date_jalon[]" id="date_jalon_<?= $a ?>" class="date_jalon_txt date hasDatepicker"
                                                   value="<?= $jalon['date_jalon'] ?>" <?= ($complete ? 'readonly' : '') ?> autocomplete="off" />
                                            <input type="checkbox" id="complete_<?= $a ?>" name="complete_<?= $a ?>" style="margin-left:33px;"
                                                   class="complete_txt"
                                                   value="1" <?= ($complete ? ' checked="checked"' : '') ?> />
                                            <input type="hidden" name="ordre[]"
                                                   value="<?= $a ?>"/>
                                            <input type="hidden" name="id_jalon[]"
                                                   value="<?= $jalon['id_jalon'] ?>"/>
                                            <span style="padding: 0px 0 0 10px; cursor:pointer;" onclick="jalons_calcul('apres',<?= $a ?>)">
                                                <img src="css/images/icons/dark/triangle_down.png"/></span>
                                            <span style="padding: 0px 0 0 0px; cursor:pointer;" onclick="jalons_calcul('bi',<?= $a ?>)">
                                                <img src="css/images/icons/dark/triangle_up_down.png"/></span>
                                            <span style="padding: 0px 0 0 0px; cursor:pointer;" onclick="jalons_calcul('avant',<?= $a ?>)">
                                                <img src="css/images/icons/dark/triangle_up.png"/></span>
                                        </span>

                                    </div>
                                    <?php
                                    $a++;
                                }
                            }
                            ?>
                        </div>
                    </div>
                </div>
                <?php
                }
                ?>
            </div>

            <div id="fiches_parent" style="margin: 0 0 15px 10px; height: 700px; width: 620px; float: left; position: relative;">
                <?php
                if($this->isDepotLockedOrJ0()) {
	                print '<h5 style="color: red;">' . txt('La liste des fiches associées ne peut être modifiée car ce projet contient un dépôt verrouillé ou comportant une photo.
') . '</h5>';
                }
                ?>
                <div>
                    <b style="font-weight: bold; margin-right: 10px;"><?= txt('Échéancier global:') ?></b>
                    <select class="multipleSelect" data-colname="echeancier_id">
                        <option value=""> </option>
                        <?=Echeanciers::getOptions(['type' => 'financement'], 0)?>
                    </select>
                </div>
                <div id="echeancier_id">
		            <?php
		            if(isset($this->echeanciers) && is_array($this->echeanciers))
		            {
			            foreach($this->echeanciers as $i => $echeancier)
			            {
				            ?>
                            <div id="echeancier_id_<?php print $echeancier->id; ?>" class="top_block">
                                <a href="javascript: void(0);" class="del_multiple">
                                    <img src="css/images/icons/dark/trashcan.png" /></a>
                                <span><?php print $echeancier->nom; ?></span>
                                <input type="hidden" name="echeancier_id[]" value="<?php print $echeancier->id; ?>" />
                            </div>
				            <?php
			            }
		            }
		            ?>
                </div>
                <?php
                    $echeancier_ids = isset($this->echeanciers) && is_array($this->echeanciers) ? array_keys($this->echeanciers) : [];
                    Fiches::getMultiSelector(($this->fiches ?? []), '_financements_projets_forFiche', ['echeancier_id' => $echeancier_ids, 'fiche_sous_type_id' => 2000002], true, 570);

                if ($admin->isChampActif('suivi_administratif')) {
                ?>
                <div style="width: 620px; margin: 20px 0;">
                    <div style="width: 100px; vertical-align: top; font-weight: bold; margin-bottom: 10px;">
	                    <?= $admin->getLabelForChamp('suivi_administratif') ?>
                    </div>
                    <div style="width: 620px;">
                        <?php
                        if (havePermission('suivi_administratif_projet_financement')) {
	                        ?>
                            <textarea id="suivi_administratif" name="suivi_administratif"
                                      style="margin-bottom: 2px; "><?= ($this->suivi_administratif) ?></textarea>
	                        <?php
                        } else {
                            print dm_nl2br($this->suivi_administratif);
                        }
                        ?>
                    </div>
                </div>
	                <?php
                }
                ?>
            </div>

        </fieldset>
        <script>
            function change_jalon_modele(id) {
				if (id == '') {
					if ($('#jalons_modeles_base').val() > 0)
						$('#jalons_modeles').val( $('#jalons_modeles_base').val() );
					return;
				}
                var html = '';
                $('#jalons').html(html);
                $('#jalons_modeles').val( $('#jalons_modeles_liste option:selected').text() );
                <?php

                $sql_groupes = "SELECT * FROM financements_jalons_modeles ORDER BY groupe_jalon ASC, order_jalon ASC";
                $groupes_modeles = $db->getAll($sql_groupes);

                echo "var groupes = {";
                $a = "0";
                foreach ($groupes_modeles as $groupe_modele) {

                        echo "".$a.": {";
                        echo "'jalonId' : " . $groupe_modele['id'] . ",
                            'groupeJalon' : '" . dm_addslashes($groupe_modele['groupe_jalon']) . "',
                            'nomJalon' : '" . dm_addslashes($groupe_modele['nom_jalon']) . "',
                            'dureeJalon': '" . $groupe_modele['duree_jalon'] . "',
                            'ordreJalon': '" . $groupe_modele['order_jalon'] . "'
                        },";
                        $a++;
                }

                echo "};";
?>
                a = 0;
                for (var key in groupes) {
                    if (groupes[a]['jalonId'] === parseInt(id)) {
                        var group_name = groupes[key]['groupeJalon'];
                    }
                    a++;
                }
                var html = '';
                a = 1;

                html += '<div class="top_block"> <span class="btns"> </span><span class="nom_jalon_txt">Nom</span> ';
                html += '<span class="date" style="margin-left: 128px;">Durée</span> <span class="date" style="margin-left: 3px;">Échéance</span> <span class="complete" style="margin-left: 5px;">Complété</span></div>';

                for (var key1 in groupes) {
                    if (groupes[key1]['groupeJalon'] === group_name) {

                        html += '<div id="jalon_' + a + '" data-ordre="' + a + '" class="jalons top_block" data-etat="1">';
                        html += '<input type="hidden" name="jalon_actif[]" class="jalon_actif" value="1">';
                        html += '<a href="javascript: void(0);" class="del_jalon">';
                        html += '<img src="css/images/icons/dark/tick.png" /></a> ';
                        html += '<span class="fields">';
                        html += '<input type="text" style="width: 275px" name="nom_jalon[]" class="nom_jalon_txt" value="' + groupes[key1]['nomJalon'] + '" autocomplete="off" readonly /> ';
                        html += '<input type="number" min="1" name="nb_jours_jalon[]" id="nb_jours_jalon_' + a + '" class="nb_jours_jalon_txt justInteger" value="' + groupes[key1]['dureeJalon'] + '" style="margin-left: 20px; width:30px;" autocomplete="off" /> jours ';
                        html += '<input type="text" name="date_jalon[]" id="date_jalon_' + a + '" class="date_jalon_txt date hasDatepicker" value="" autocomplete="none" /> ';
                        html += '<input type="checkbox" style="margin-left: 34px;" id="complete_' + a + '" name="complete_' + a + '" style="margin-left: 15px;" class="complete_txt" value="1" />';
						html += '<input type="hidden" name="id_jalon[]" value="' + a + '"/>';
						html += '<input type="hidden" name="ordre[]" value="' + a + '"/>';
                        html += '<span style="padding: 0px 0 0 13px; cursor:pointer;" onclick="jalons_calcul(\'apres\',' + a + ')"><img src="css/images/icons/dark/triangle_down.png"/></span>';
                        html += '<span style="padding: 0px 0 0 3px; cursor:pointer;" onclick="jalons_calcul(\'bi\',' + a + ')"><img src="css/images/icons/dark/triangle_up_down.png"/></span>';
                        html += '<span style="padding: 0px 0 0 3px; cursor:pointer;" onclick="jalons_calcul(\'avant\',' + a + ')"><img src="css/images/icons/dark/triangle_up.png"/></span>';
                        html += '</span>';
                        html += '</div>';

                        $('#jalon_' + a).find('input.hasDatepicker').datetimepicker({
                            timepicker:false,
                            format:'Y-m-d',
                            lang:'fr',
							scrollMonth : false,
							scrollInput : false
                        });
                        a = a + 1;
                    }

                }
                $('#jalons').append(html);
                init();

				$('#jalons').find('input.hasDatepicker').datetimepicker({
					timepicker:false,
					format:'Y-m-d',
					lang:'fr',
					scrollMonth : false,
					scrollInput : false
				});
				
				$('#jalons_modeles_liste').hide();

				parent.fancybox_resize();

            }
            <?php
            if($this->isDepotLockedOrJ0())
            {
            ?>
                $('#echeancier_id').find('.del_multiple').hide();
                $(document).find('.fa-angle-left').addClass('readonly').css({opacity: '0', cursor: 'default'});
			    $(document).find('.fa-angle-double-left').addClass('readonly').css({opacity: '0', cursor: 'default'});
                $('[data-colname="echeancier_id"]').addClass('readonly');
                // $('#listeFiches').addClass('readonly');
			    // $('#listeFiches').css({pointerEvents: 'none', opacity: '0.5'});
                $('#listeFiches_to').css({pointerEvents: 'none', opacity: '0.5'});
			    $('#listeFiches_to').removeClass('readonly').addClass('readonly_scrollable');
			<?php
			}
            ?>
        </script>
        <?php
    }

    public function isDepotLockedOrJ0()
    {
        global $db;

        $sql = "select count(*) from financements_projets_depots where (ts_verrouille is not null or ts_photo_j0 is not null) and financement_projet_id = ?";
        $nb = $db->getOne($sql, [$this->id]);

        return isset($nb) && (int)$nb > 0;
    }

    public static function getAllDistinctValeurColonne($colonne)
    {
        global $db;
        $sql = "select distinct trim($colonne) COLlATE utf8_bin as ele from financements_projets where projet_id = ? order by $colonne";
        return $db->getCol($sql, [getProjetId()]);
    }

    public static function delete($id)
    {
        global $db;
        $db->autocommit(false);
        $db->query("DELETE FROM `financements_projets` WHERE id = ?", array($id));
        $db->commit();
    }

    public static function validate($data, $id)
    {
        global $db;
        $valide = true;
        foreach ($data as $colomnName => $value) {
            if ($colomnName == 'numero') {
                if (dm_strlen($value) == 0) {
                    setErreur(txt('Le numéro est obligatoire.'));
                    $valide = false;
                } else {
                    if ($id == 0) {
	                    $sql = "select count(*) as nb from financements_projets where projet_id = ? and numero = ?";
	                    $nb = $db->getOne($sql, [getProjetId(), dm_trim($value)]);

	                    if ($nb > 0) {
		                    setErreur(txt('Ce numéro de projet existe déjà.'));
		                    $valide = false;
	                    }
                    }
                }
            }
//            else if ($colomnName == 'description') {
//                if (dm_strlen($value) == 0) {
//                    setErreur(txt('La description est obligatoire.'));
//                    $valide = false;
//                }
//            }
        }

        if (self::isGestionDesJalons()) {
            if (!isset($data['nom_jalon'])) {
                setErreur(txt('Vous devez ajouter au moins 1 jalon.'));
                $valide = false;
            } else {
                $champs = array('date_jalon', 'nom_jalon', 'nb_jours_jalon');
                $nb_jalons = 0;
                $nb_jalons_incomplets = 0;
                $nb_sous_1 = 0;
                foreach ($data['nom_jalon'] as $ordre => $nom_jalon) {
                    $nb_remplis = 0;
                    if ($data['jalon_actif'][$ordre] == 1) {
                        foreach ($champs as $champ) {
                            if (dm_strlen($_POST[$champ][$ordre]) > 0) {
                                $nb_remplis++;
                            }
                            if ($champ == 'nb_jours_jalon' && dm_strlen($_POST[$champ][$ordre]) > 0 && intval($_POST[$champ][$ordre]) < 1) {
                                $nb_sous_1++;
                            }
                        }
                        if ($nb_remplis > 0) {
                            $nb_jalons++;
                        }
                        if ($nb_remplis < 3) {
                            $nb_jalons_incomplets++;
                        }
                    }
                }

                if ($nb_sous_1 > 0) {
                    setErreur(txt('La durée d\'un jalon ne peut être inférieure à 1 jour.'));
                    $valide = false;
                }
                if ($nb_jalons == 0) {
                    setErreur(txt('Vous devez ajouter au moins 1 jalon.'));
                    $valide = false;
                } else {
                    if ($nb_jalons_incomplets > 0) {
                        setErreur(txt('Un jalon que vous tentez d\'ajouter est incomplet. Vous devez saisir le nom, l\'échéance et la durée.'));
                        $valide = false;
                    }
                }
            }
        }

        if (!isset($data['listeFiches'])) {
            setErreur(txt('Vous devez associer au moins 1 fiche.'));
            $valide = false;
        }
        return $valide;
    }

    public static function getChampsReference()
    {
        $champsReference = [];
	    foreach (FinancementsProjetsAdministrationsUnifiees::getColonnesMetas() as $blocsNiv2) {
		    foreach ($blocsNiv2 as $bloc) {
			    foreach ($bloc['colonnes'] as $champ) {
			        if (isset($champ['reference']) && $champ['reference']) {
				        $champsReference[$champ['nom']] = $champ['label'];
                    }
			    }
		    }
	    }
	    return $champsReference;
    }

	public function getNomsEcheanciers()
	{
		$nomEcheanciers = '';
		foreach ($this->echeanciers as $i => $row) {
			$nomEcheanciers .= ($i === 0 ? '' : ', ').$row->nom;
		}
		return $nomEcheanciers;
	}

    /**
     * Colors of statues
     *
     * @return string
     */
    public function statutColor()
    {
        switch ($this->statut) {
            case 1:
                return "green";
                break;
            case 2:
                echo "red";
                break;
            case 3:
                echo "black";
                break;
        }
    }

    /**
     * Test of statuses
     *
     * @return string
     */
    public function statutTexte()
    {
        switch ($this->statut) {
            case 1:
                return txt('En cours');
                break;
            case 2:
                return txt('En retard');
                break;
            case 3:
                return txt('Complété');
                break;
        }
    }

    /**
     * Select box for Projets
     *
     * @param string $selected
     * @throws Exception
     */
    public static function selectBoxProjects($selected = '', $multiple = false)
    {
        $selecteds = [];
        if ($multiple) {
            $selecteds = $selected;
        } else if ($selected <> '') {
            $selecteds = [$selected];
        }

        ?>
        <select id="projet_id" name="projet_id" style="width: <?= ($multiple ? '300' : '160') ?>px; " class="filtre <?= ($multiple ? 'champ_multiple' : '') ?>" <?= ($multiple ? 'multiple data-placeholder="- Projets -"' : '') ?>>
            <?php
            if (!$multiple) {
                ?>
                <option value="0">- Projets -</option>
                <?php
            }
            // Showing the Projet select box
            $projets = FinancementsProjets::getListeProjets_simple();

            foreach ($projets as $projet) {
                echo "<option value=\"" . $projet->id . "\" ";

                if (in_array($projet->id, $selecteds)) {
                    echo "selected";
                }
                echo ">" . $projet->numero . "</option>";
            }
            ?>
        </select>
        <?php
    }

    /**
     * Select Box for Jalons
     *
     * @param string $selected - Selectec choice
     * @throws Exception
     */
    public static function selectBoxJalons($selected = '', $project_id_requested = '', $multiple = false)
    {
        $selecteds = [];
        if ($multiple) {
	        $selecteds = $selected;
        } else if ($selected <> '') {
	        $selecteds = [$selected];
        }
        ?>
        <select id="jalon_id" name="jalon_id" style="width: <?= ($multiple ? '300' : '160') ?>px; <?=(count($selecteds) == 0 ? '' : 'background-color: rgb(240, 210, 181);')?>" class="filtre <?= ($multiple ? 'champ_multiple' : '') ?>" <?= ($multiple ? 'multiple placeholder="- Jalons -"' : '') ?>>
            <?php
            if (!$multiple) {
	            ?>
                <option value="">- Jalons -</option>
	        <?php
	        }
            if ($project_id_requested != '') {
                $filtre['financement_projet_id'] = intval($project_id_requested);

            } else {
                $filtre = array();
            }

            /*if (isset($filtre['financement_projet_id'])) {
                $projets_jalon = FinancementsProjets::getListe($filtre);
                foreach ($projets_jalon as $projet_jalon) {

                    foreach ($projet_jalon->jalons as $jalon) {
                        echo "<option value=\"" . $jalon['id'] . "\" ";
                        if ($selected == $jalon['id']) {
                            echo "selected";
                        }
                        echo ">" . $projet_jalon->numero . " - " . $jalon['nom_jalon'] . "</option>";
                    }
                }
            } else {*/
                $projets_jalon = FinancementsProjets::getFinancementsJalonGroupName($project_id_requested);

                foreach ($projets_jalon as $jalon) {
                    echo "<option value=\"" . $jalon['id'] . "\" ";
                    if (in_array($jalon['id'], $selecteds)) {
                        echo "selected";
                    }
                    echo ">" . $jalon['nom_jalon'] . "</option>";

               /* }*/
            }
            ?>
        </select>
        <?php
    }

    /**
     * Select Box for Equipements
     *
     * @param string $selected
     * @throws Exception
     */
    public static function selectBoxEquipements($selected = '', $multiple = false)
    {
        $selecteds = [];
        if ($multiple) {
            $selecteds = $selected;
        } else if ($selected <> '') {
            $selecteds = [$selected];
        }

        ?>
        <select id="equipement_bio_medicaux_type_id" name="equipement_bio_medicaux_type_id" style="width: 300px; " class="filtre <?= ($multiple ? 'champ_multiple' : '') ?>" <?= ($multiple ? 'multiple data-placeholder="- Équipement -"' : '') ?>>
            <?php
            if (!$multiple) {
                ?>
                <option value="">- Équipement -</option>
                <?php
            }
            $equipements = EquipementsBioMedicauxTypes::getListe(['with_description' => 1]);
            foreach ($equipements as $equipement) {
                echo "<option value=\"" . $equipement->id . "\" ";

                if (in_array($equipement->id, $selecteds)) {
                    echo "selected";
                }
                echo ">" . $equipement->getNom() . "</option>";
            }
            ?>
        </select>
        <?php
    }

	/**
	 * Select Box for Equipements
	 *
	 * @param string $selected
	 * @throws Exception
	 */
	public static function selectBoxGroupes($selected = '', $project_id_requested = '', $numero_groupe_requested = '')
	{
		global $db;

		if ($numero_groupe_requested != '') {

			$filtre['code_groupe'] = $numero_groupe_requested;
		}
		?>
        <select id="numero_groupe" name="numero_groupe" style="width: 150px; " class="filtre">
            <option value="">- Numéro de groupe -</option>
			<?php
			// Showing the Type d'équipement select box
			$filtre['financement_projet_id'] = intval($project_id_requested);

			$projet = (intval($project_id_requested) > 0)? "p.id = ".intval($project_id_requested) : "p.projet_id = ".getProjetId();
			$sql = "SELECT g.id, g.numero FROM financements_projets_groupes_equipements g
            	JOIN financements_projets p ON p.id = g.financement_projet_id
            	WHERE $projet and g.numero <> '' and g.numero is not null ORDER BY g.numero";
			$groupes = $db->getAll($sql);

			foreach ($groupes as $groupe) {
				echo "<option value=\"" . $groupe['id'] . "\" ";

				if ($selected == $groupe['id']) {
					echo "selected";
				}
				echo ">" . $groupe['numero'] . "</option>";
			}
			?>
        </select>
		<?php
	}

    /**
     * Sélection par Bon de commande
     *
     * @param string $selected
     * @throws Exception
     */
    public static function selectBoxEBonCommande($selected = '', $project_id_requested = '', $numero_groupe_requested = '')
    {
        global $db;

        if ($numero_groupe_requested != '') {
            $filtre['code_groupe'] = $numero_groupe_requested;
        }
        ?>
        <select id="bon_commande" name="bon_commande" style="width: 160px; " class="filtre">
            <option value="">- Bon de commande -</option>
            <?php
            $filtre['financement_projet_id'] = intval($project_id_requested);

            $projet = (intval($project_id_requested) > 0)? "p.id = ".intval($project_id_requested) : "p.projet_id = ".getProjetId();
            $sql = "SELECT DISTINCT g.bon_commande FROM financements_projets_groupes_equipements g
            	JOIN financements_projets p ON p.id = g.financement_projet_id
            	WHERE bon_commande IS NOT null AND bon_commande <> '' AND $projet ORDER BY bon_commande";
            $bons = $db->getAll($sql);

            foreach ($bons as $bon) {
                    echo "<option value=\"" . $bon['bon_commande'] . "\" ";

                    if ($selected == $bon['bon_commande']) {
                        echo "selected";
                    }
                    echo ">" . $bon['bon_commande'] . "</option>";
            }
            ?>
        </select>
        <?php
    }

    /**
     * Select Box for Status
     *
     * @param string $selected
     */
    public static function selectBoxStatus($selected = '', $multiple = false)
    {
	    $selecteds = [];
	    if ($multiple) {
		    $selecteds = $selected;
	    } else if ($selected <> '') {
		    $selecteds = [$selected];
	    }
        ?>
        <select id="statut" name="statut" style="width: <?= ($multiple ? '300' : '160') ?>px; <?=(count($selecteds) == 0 ? '' : 'background-color: rgb(240, 210, 181);')?>" class="filtre <?= ($multiple ? 'champ_multiple' : '') ?>" <?= ($multiple ? 'multiple placeholder="- Statut -"' : '') ?>>
            <?php
            if (!$multiple) {
	            ?>
                <option value="">- Statut -</option>
	            <?php
            }
            $statuts = FinancementsProjets::getStatuts();

            foreach ($statuts as $statut) {
                echo "<option value=\"" . $statut['code'] . "\" ";
	            if (in_array($statut['code'], $selecteds)) {
                    echo "selected";
                }
                echo ">" . $statut['nom'] . "</option>";
            }
            ?>
        </select>
        <?php
    }


    public static function selectBoxCodeGroupe($selected = '', $project_id_requested = 0)
    {
        global $db;

        // Showing the Type d'équipement select box
        $filtre['financement_projet_id'] = intval($project_id_requested);
        $projets_groupes = FinancementsProjets::getListe($filtre);

        ?>
        <select id="type_groupe" name="type_groupe" style="width: 160px; " class="filtre">
            <option value="">- Type d'équipement</option>
            <?php

            $equipements = array();
            foreach ($projets_groupes as $projet_groupe) {
                foreach ($projet_groupe->groupes as $groupe) {
	                $equipements[$groupe->code_equipement] = $groupe->code_equipement . " - ". $groupe->description_equipement;
                }
            }

            asort($equipements);
            foreach ($equipements as $code => $equipement) {
	            echo "<option value=\"" . $code . "\" ";

	            if ($selected == $code) {
		            echo "selected";
	            }
	            echo ">" . $equipement . "</option>";
            }
            ?>
        </select>
        <?php
    }

    /**
     * Select Box for Echeancier
     *
     * @param string $selected
     * @throws Exception
     */
    public static function selectBoxEcheancier($selected = '', $multiple = false)
    {
        ?>

        <select id="echeancier" name="echeancier" style="width: <?= ($multiple ? '300' : '160') ?>px;" class="filtre champ_multiple" <?= ($multiple ? 'multiple placeholder="- Échéanciers globaux -"' : '') ?>>
            <?php
            if (!$multiple) {
	            ?>
                <option value="">- Échéanciers globaux -</option>
	            <?php
            }
             Echeanciers::getOptions(['type' => 'financement'], $selected, $multiple);
            ?>

        </select>
        <?php
    }

    /**
     * Get the listing of projects for Gantt
     *
     * @param  int $project_id_requested - If a specific Project ID is requested
     * @param  int $jalon_id_requested   - If a specific Jalon ID is requested
     * @param  int $groupe_id_requested  - If a specific Groupe of equipment is requested
     * @return array
     * @throws Exception
     */
    public static function ganttGetListe($project_id_requested = 0, $jalon_id_requested = 0, $type_groupe_requested = '', $numero_groupe_requested = '')
    {
        global $db;

        if ($project_id_requested != 0) {
            $filtre['financement_projet_id'] = $project_id_requested;
        } else {
            $filtre = array();
        }

        if ($numero_groupe_requested != '' && $numero_groupe_requested != 0) {
            $filtre['numero_groupe'] = $numero_groupe_requested;
        }
        if ($type_groupe_requested != '') {
            $filtre['type_groupe'] = $type_groupe_requested;

        }

        // We filter jalons if an ID is asked in the _GET
        if ($jalon_id_requested != 0) {

                if ($project_id_requested == 0) {
                    // We get the name of the jalon
                    $sql_jalons_nom = "SELECT nom_jalon 
                    FROM financements_projets_jalons 
                    WHERE id = '".$jalon_id_requested."'";

                    $nom_jalon = $db->getOne($sql_jalons_nom);

                    $sql_projet_of_jalon = "SELECT financement_projet_id 
                    FROM financements_projets_jalons 
                    WHERE nom_jalon = '".dm_addslashes($nom_jalon)."'";

                   $projet_of_jalon = $db->getAll($sql_projet_of_jalon);

                } else {
                    $sql_projet_of_jalon = "SELECT financement_projet_id 
                    FROM financements_projets_jalons 
                    WHERE id = '".$jalon_id_requested."'";

                    $projet_of_jalon = $db->getOne($sql_projet_of_jalon);
                }


                if (isset($projet_of_jalon)) {
                    $filtre['financement_projet_id'] = $projet_of_jalon;
                }

        }



        return FinancementsProjets::getListe($filtre);
    }

    /**
     * Get the dates for specific project (Global or Financement)
     *
     * @param  int    $project_id   - Specific ID of a project
     * @param  string $project_type - Global project or standard financement project
     * @return mixed
     * @throws Exception
     */
    public static function ganttGetDatesProject($project_id, $project_type = 'standard')
    {
        global $db;

        if ($project_type == 'standard') {
            // Getting Standard jalons
            $jalons_start_sql = "SELECT date_jalon, coalesce(nb_jours_jalon, 0) as nb_jours_jalon FROM financements_projets_jalons WHERE financement_projet_id = ? ORDER BY date_jalon ASC LIMIT 0,1";
            $jalons_start_tmp = $db->getRow($jalons_start_sql, array($project_id));
	        $jalons_start = Date('Y-m-d', strtotime($jalons_start_tmp['date_jalon'].' - '.$jalons_start_tmp['nb_jours_jalon'].' days'));

            $jalons_end_sql = "SELECT date_jalon FROM financements_projets_jalons WHERE financement_projet_id = ? ORDER BY date_jalon DESC LIMIT 0,1";
            $jalons_end = $db->getOne($jalons_end_sql, array($project_id));

        } elseif ($project_type == 'global') {
            // Getting Global Jalons
            $jalons_start_sql = "SELECT date_debut FROM echeanciers_jalons WHERE echeancier_id = ? ORDER BY date_debut ASC LIMIT 0,1";
            $jalons_start = $db->getOne($jalons_start_sql, array($project_id));

            $jalons_end_sql = "SELECT date_fin FROM echeanciers_jalons WHERE echeancier_id = ? ORDER BY date_fin DESC LIMIT 0,1";
            $jalons_end = $db->getOne($jalons_end_sql, array($project_id));

        }

        $dates['start'] = new DateTime($jalons_start);
        $dates['end'] = new DateTime($jalons_end);

        return $dates;
    }

    /**
     * Showing the portion of javascript needed to show Global Projects
     *
     * @param  object $project_info            - The info of the main standard project
     * @param  string $echeancier_id_requested - If a specific Global project is requested
     * @throws Exception
     */
    public static function ganttShowProjectGlobal($project_info, $echeancier_id_requested = '')
    {
        /* Showing each global jalon for fiches */
        if (isset($echeancier_id_requested)) {
            $echeanciers_globaux = FinancementsProjets::getEcheancierOfProjectGlobal($project_info->id, $echeancier_id_requested);

        } else {
            $echeanciers_globaux = FinancementsProjets::getEcheancierOfProjectGlobal($project_info->id);

        }

        foreach ($echeanciers_globaux as $echeancier) {

            // Calculating the start and end of a project
            $dates_global = FinancementsProjets::ganttGetDatesProject($echeancier['info']['id'], 'global');

            $date_jalon1 = $dates_global['start'];
            $date_jalon2 = $dates_global['end'];

            $duration_jalong = $date_jalon2
                ->diff($date_jalon1)->format("%a");
            ?>
            {
            "id": "e<?php echo $project_info->numero."-".$echeancier['info']['id']; ?>",
            "text": "Global - <?php echo $project_info->numero." - ". $echeancier['info']['nom']; ?>",
            "textbox": "<?php echo $project_info->numero." - ". $echeancier['info']['nom']; ?>",
            "start_date": "<?php echo date_format($dates_global['start'], 'd-m-Y'); ?>",
            "duration": <?php echo (dm_strlen($duration_jalong) == 0 ? 0 : $duration_jalong); ?>,
            "order": 1,
            "progress": "0",
            "numero_projet": "<?php echo $project_info->numero; ?>",
            "open": true,
            "draggable": false,
            "color": "#606060",
            "parent": "0",
            "bgcolor": "gray",
            "echeancier_id": <?=$echeancier['info']['id']?>
            },
            <?php
            // Showing Jalons of this global project
            FinancementsProjets::ganttShowJalonsProjectGlobal($echeancier['info'], $project_info);
        }
    }

    /**
     * Showing the Jalons of a Global Project
     *
     * @param  array $echeancier_info - Info about the Global project
     * @param  object $projet_info    - Info about the Standard project
     * @throws Exception
     */
    public static function ganttShowJalonsProjectGlobal($echeancier_info, $projet_info)
    {
        global $db;
        // Getting the Jalons of Global Echeancier

        $sql_jalon_global = "SELECT * FROM echeanciers_jalons WHERE echeancier_id = ?";
        $jalon_echeancier = $db->getAll($sql_jalon_global, array($echeancier_info['id']));

        $premier = true;
        foreach ($jalon_echeancier as $jalon_global) {
            if (! $premier) echo ',';
            
            $date_jalon1 = new DateTime($jalon_global['date_debut']);
            $date_jalon2 = new DateTime($jalon_global['date_fin']);

            $duration_jalong = $date_jalon2
                ->diff($date_jalon1)->format("%a");
            ?>
            {
            "id": "eg<?php echo $projet_info->numero."-".$echeancier_info['id']."-".$jalon_global['id']; ?>",
            "text": "Global - <?php echo $jalon_global['nom_jalon']; ?>",
            "textbox": "<?php echo $jalon_global['nom_jalon']; ?>",
            "start_date": "<?php echo date_format($date_jalon1, 'd-m-Y'); ?>",
            "duration": <?php echo (dm_strlen($duration_jalong) == 0 ? 0 : $duration_jalong); ?>,
            "order": 1,
            "numero_projet": "<?php echo $projet_info->numero; ?>",
            "progress": "0",
            "draggable": false,
            "open": true,
            "color": "#a0a0a0",
            "parent": "e<?php echo $projet_info->numero."-".$echeancier_info['id']; ?>",
            "bgcolor": "green",
            "is_jalon": 1
            }
            <?php
            $premier = false;
        }
    }

	/*
		Affiche seulement les projets sur le graphique du Gantt
	*/
    public static function ganttProjets($projet_info, $jalon_id_requested = 0, $project_id_requested = 0)
    {
        global $db;

        // Calculating the start and end of a project
        $dates_project = FinancementsProjets::ganttGetDatesProject($projet_info->id);

        $start_date_to_use = $dates_project['start'];
        $last_date_to_use = $dates_project['end'];

        if ($last_date_to_use && $start_date_to_use) {
            $duration = $last_date_to_use
                ->diff($start_date_to_use)->format("%a");

        } else {
            $duration = '1';

        }
        ?>
        {
        id: '<?php echo $projet_info->numero; ?>',
        id_of_project: '<?php echo $projet_info->id; ?>',
        text: "<?php echo $projet_info->numero." - ". $projet_info->description; ?>",
        start_date: "<?php
        //$start_date_p = new DateTime($f_jalon);
        echo date_format($start_date_to_use, 'd-m-Y');
        ?>",
        duration: <?php echo (dm_strlen($duration) == 0 ? 0 : $duration); ?>,
        order: 10,
        bgcolor: 'white',
        numero_projet: '<?php echo $projet_info->numero; ?>',
        progress: 1,
        'draggable': false,
        <?php if ($jalon_id_requested != 0) { ?>
        open: true
        <?php } else { ?>
        open: false
        <?php } ?>
        },
        <?php
    }

    /*
    	Gantt par projet. Retour en JSON
    */
    public static function ganttProjetJSON($projet_info, $jalon_id_requested = 0, $project_id_requested = 0)
    {
        global $db;

        // Calculating the start and end of a project
        $dates_project = FinancementsProjets::ganttGetDatesProject($projet_info->id);

        $start_date_to_use = $dates_project['start'];
        $last_date_to_use = $dates_project['end'];

        if ($last_date_to_use && $start_date_to_use) {
            $duration = $last_date_to_use
                ->diff($start_date_to_use)->format("%a");

        } else {
            $duration = '1';

        }
        ?>
        {
        "id": "<?php echo $projet_info->numero; ?>",
        "id_of_project": "<?php echo $projet_info->id; ?>",
        "text": "<?php echo $projet_info->numero.' - '. $projet_info->description; ?>",
        "start_date": "<?php echo date_format($start_date_to_use, 'd-m-Y'); ?>",
        "duration": "<?php echo (dm_strlen($duration) == 0 ? 0 : $duration); ?>",
        "order": 10,
        "bgcolor": "white",
        "numero_projet": "<?php echo $projet_info->numero; ?>",
        "progress": 1,
        "draggable": false,
        "open": true
        },
        <?php
        // Showing Projects Jalons
        $jalons = FinancementsProjets::getJalons($projet_info->id);

        $premier = true;
        foreach ($jalons as $jalon) {
            if (! $premier) echo ',';
            
            if ((isset($jalon_id_requested) && $jalon_id_requested == $jalon['id'] && $project_id_requested != 0) || $jalon_id_requested == '') {
                // Showing Jalons of project
                FinancementsProjets::ganttJalonJSON($projet_info, $jalon);

            } elseif (isset($jalon_id_requested) && $project_id_requested == 0) {

                // We get the name of the jalon
                $sql_jalons_nom = "SELECT nom_jalon 
                    FROM financements_projets_jalons 
                    WHERE id = '".$jalon_id_requested."'";

                $nom_jalon = $db->getOne($sql_jalons_nom);

                $sql_ids_of_jalon = "SELECT * 
                    FROM financements_projets_jalons 
                    WHERE nom_jalon = '".dm_addslashes($nom_jalon)."'";

                $ids_of_jalon = $db->getAll($sql_ids_of_jalon);
					 $total_id = count($ids_of_jalon);
                for ($i=0; $i < $total_id; $i++) {
                    FinancementsProjets::ganttJalonJSON($projet_info, $ids_of_jalon[$i]);
                     echo ',';
                }
            }
            $premier = false;
        }
    }

    public static function ganttJalonJSON($projet_info, $jalon_info)
    {
        global $db;

        $sql_projet = "SELECT * FROM financements_projets WHERE id = '".$jalon_info['financement_projet_id']."'";
        $info_projet = $db->getAll($sql_projet);

        foreach ($info_projet as $infoprojet) {
            $projet_info = $infoprojet;
        }

        $today = date("Y-m-d");

        if ($jalon_info['ts_complete'] != null) {
            $color = "black";
        } else {
            if ($jalon_info['date_jalon'] < $today) {
                $color = "red";

            } else {
                $color = "green";
            }
        }
        ?>
        {
        "id": <?php echo $jalon_info['id']; ?>,
        "id_of_project": "<?php echo $projet_info['id']; ?>",
        "text": "<?php echo $projet_info['numero'] . " - " . $jalon_info['nom_jalon']; ?>",
        "textbox": "<?php echo $jalon_info['nom_jalon']; ?>",
        "start_date": "<?php
        $start_date = new DateTime($jalon_info['date_jalon']);
        date_sub($start_date, date_interval_create_from_date_string($jalon_info['nb_jours_jalon'] . ' days'));

        echo date_format($start_date, 'd-m-Y');
        ?>",
        "duration": <?php echo (dm_strlen($jalon_info['nb_jours_jalon']) == 0 ? 0 : $jalon_info['nb_jours_jalon']); ?>,
        "end_date": "<?php date_add($start_date, date_interval_create_from_date_string($jalon_info['nb_jours_jalon'].' days')); ?>",
        "order": 10,
        "progress": 1,
        "is_jalon": 1,
        "numero_projet": "<?php echo $projet_info['numero']; ?>",
        "draggable": false,
        "open": true,
        "color": "<?php echo $color; ?>",
        "parent": "<?php echo $projet_info['numero']; ?>",
        "bgcolor": "white"
        }
        <?php
    }


    /**
     * Showing the javascript portion to show the financements Projects
     *
     * @param  object $projet_info        - Information about the project
     * @param  int $jalon_id_requested - If a specific jalon is requested
     * @throws Exception
     */
    public static function ganttShowProjects($projet_info, $jalon_id_requested = 0, $project_id_requested = 0)
    {
        global $db;

        // Calculating the start and end of a project
        $dates_project = FinancementsProjets::ganttGetDatesProject($projet_info->id);

        $start_date_to_use = $dates_project['start'];
        $last_date_to_use = $dates_project['end'];

        if ($last_date_to_use && $start_date_to_use) {
            $duration = $last_date_to_use
                ->diff($start_date_to_use)->format("%a");

        } else {
            $duration = '1';

        }
        ?>
        {
        id: '<?php echo $projet_info->numero; ?>',
        id_of_project: '<?php echo $projet_info->id; ?>',
        text: "<?php echo $projet_info->numero." - ". $projet_info->description; ?>",
        start_date: "<?php
        //$start_date_p = new DateTime($f_jalon);
        echo date_format($start_date_to_use, 'd-m-Y');
        ?>",
        duration: <?php echo (dm_strlen($duration) == 0 ? 0 : $duration); ?>,
        order: 10,
        bgcolor: 'white',
        numero_projet: '<?php echo $projet_info->numero; ?>',
        progress: 1,
        'draggable': false,
        <?php if ($jalon_id_requested != 0) { ?>
        open: true
        <?php } else { ?>
        open: false
        <?php } ?>
        },
        <?php
        // Showing Projects Jalons
        $jalons = FinancementsProjets::getJalons($projet_info->id);

        foreach ($jalons as $jalon) {
            if ((isset($jalon_id_requested) && $jalon_id_requested == $jalon['id'] && $project_id_requested != 0) || $jalon_id_requested == '') {
                // Showing Jalons of project
                FinancementsProjets::ganttShowProjectJalon($projet_info, $jalon);

            } elseif (isset($jalon_id_requested) && $project_id_requested == 0) {

                // We get the name of the jalon
                $sql_jalons_nom = "SELECT nom_jalon 
                    FROM financements_projets_jalons 
                    WHERE id = '".$jalon_id_requested."'";

                $nom_jalon = $db->getOne($sql_jalons_nom);

                $sql_ids_of_jalon = "SELECT * 
                    FROM financements_projets_jalons 
                    WHERE nom_jalon = '".dm_addslashes($nom_jalon)."'";

                $ids_of_jalon = $db->getAll($sql_ids_of_jalon);

                foreach ($ids_of_jalon as $id_of_jalon) {
                    FinancementsProjets::ganttShowProjectJalon($projet_info, $id_of_jalon);
                }

            }
        }
    }

    /**
     * Showing Jalons of a specific standard project
     *
     * @param  object $projet_info - Informations about the project
     * @param  array  $jalon_info  - Informations about the jalon
     * @throws Exception
     */
    public static function ganttShowProjectJalon($projet_info, $jalon_info)
    {
        global $db;

        $sql_projet = "SELECT * FROM financements_projets WHERE id = '".$jalon_info['financement_projet_id']."'";
        $info_projet = $db->getAll($sql_projet);

        foreach ($info_projet as $infoprojet) {
            $projet_info = $infoprojet;
        }

        $today = date("Y-m-d");

        if ($jalon_info['ts_complete'] != null) {
            $color = "black";
        } else {
            if ($jalon_info['date_jalon'] < $today) {
                $color = "red";

            } else {
                $color = "green";
            }
        }
        ?>
        {
        id: <?php echo $jalon_info['id']; ?>,
        id_of_project: '<?php echo $projet_info['id']; ?>',
        text: "<?php echo $projet_info['numero'] . " - " . $jalon_info['nom_jalon']; ?>",
        textbox: "<?php echo $jalon_info['nom_jalon']; ?>",
        start_date: "<?php
        $start_date = new DateTime($jalon_info['date_jalon']);
        date_sub($start_date, date_interval_create_from_date_string($jalon_info['nb_jours_jalon'] . ' days'));

        echo date_format($start_date, 'd-m-Y');
        ?>",
        duration: <?php echo (dm_strlen($jalon_info['nb_jours_jalon']) == 0 ? 0 : $jalon_info['nb_jours_jalon']); ?>,
        end_date: "<?php date_add($start_date, date_interval_create_from_date_string($jalon_info['nb_jours_jalon'].' days')); ?>",
        order: 10,
        progress: 1,
        is_jalon: 1,
        numero_projet: '<?php echo $projet_info['numero']; ?>',
        draggable: false,
        open: true,
        color: '<?php echo $color; ?>',
        parent: '<?php echo $projet_info['numero']; ?>',
        bgcolor: 'white'
        },
        <?php
    }

	public function isLockedAll()
	{
		if($this->isLocked())
		{
			return true;
		}
		else if($this->isGroupeLocked())
        {
	        return true;
        }
		return false;
	}

    public function isLocked()
    {
	    if(dm_strlen($this->ts_verrouille) > 0)
	    {
		    return true;
	    }
        return false;
    }

	public function isGroupeLocked()
	{
        foreach($this->groupes as $groupe)
        {
            if($groupe->isLocked())
            {
                return true;
            }
        }
        return false;
	}

	public static function getValeursForFiltreAvance($type, $colonne, $valeur_selected) {
        global $db;

		?>
        <option value=""> </option>
        <option value="%vide%"<?= ("%vide%" == $valeur_selected ? ' selected="selected"' : '') ?>><?= txt('[Champ vide]') ?></option>
        <option value="%non-vide%"<?=("%non-vide%" == $valeur_selected ? ' selected="selected"' : '')?>><?=txt('[Champ non-vide]')?></option>
		<?php
		if ($colonne == 'priorite_financement_id') {
			PrioritesFinancementsTypes::getOptions(array(), $valeur_selected);
		} else if ($colonne == 'bloquant_type_id') {
			BloquantsTypes::getOptions(array(), $valeur_selected);
		} else if (in_array($colonne, ['responsable_dossier_a_id', 'responsable_dossier_b_id', 'responsable_dossier_c_id', 'responsable_dossier_d_id', 'responsable_dossier_e_id'])) {
            list($resp, $doss, $lettre, $id) = explode( '_', $colonne);
            $classe = "FinancementsResponsablesDossiers" . strtoupper($lettre) . "Types";
			$classe::getOptions(array(), $valeur_selected);
		} else if ($colonne == 'code_equipement') {
			EquipementsBioMedicauxTypes::getOptions(array(), $valeur_selected);
		} else {
            if ($type == 'projet') {
				$sql = "select distinct `" . db::tableColonneValide($colonne) . "` COLlATE utf8_bin as valeur from financements_projets where projet_id = ? order by `" . db::tableColonneValide($colonne) . "`";
				$liste = $db->getAll($sql, array(getProjetId()));
			} else {
				$sql = "select distinct `" . db::tableColonneValide($colonne) . "` COLlATE utf8_bin as valeur from financements_projets_groupes_equipements where financement_projet_id in (select id from financements_projets where projet_id = ?) order by `" . db::tableColonneValide($colonne) . "`";
				$liste = $db->getAll($sql, array(getProjetId()));
			}
			foreach ($liste as $option) {
				if (dm_strlen(dm_trim($option['valeur'])) > 0) {
					?>
                    <option value="<?= $option['valeur'] ?>"<?= ($option['valeur'] == $valeur_selected ? ' selected="selected"' : '') ?>><?= $option['valeur'] ?></option>
					<?php
				}
			}
		}
    }

    public static function isFiltreAvanceActif() {
        if (!isset($_SESSION['filtre_avance_financements_projets_liste'])) {
            return false;
        } else {
            foreach ($_SESSION['filtre_avance_financements_projets_liste'] as $valeur) {
                if (dm_strlen($valeur) > 0) {
                    return true;
                }
            }
        }
    }

    public function getInfoLocauxEquipements() {
        global $db;

        $depots = FinancementsProjetsDepots::getListe(['financement_projet_id' => $this->id]);
        $liste = [];
        foreach ($depots as $depot) {
	        if (count($this->fiches) > 0) {
	            $sql = "select distinct ebm.id, ebm.*, f.code as fiche_code, f.code_alternatif, f.code_alternatif_2, ebmt.nom as code_equipement, ebmt.description as desctiption_equipement, 
                    ebmt.prix_unitaire_nouveau, ebmt.taxable, ph.nom as phase_incidence, ebmtcr.nom as categorie_responsabilite, '' as projet_acquisition, 
                    '' as structure_hierarchique, 
                    prix_unitaire_last as prix_unitaire_last_1, prix_unitaire_last as prix_unitaire_last_2, prix_unitaire_last as prix_unitaire_last_3, 
                    prix_unitaire_last as prix_unitaire_last_4, prix_unitaire_last as prix_unitaire_last_5,
                    ebmt.prix_unitaire_nouveau as prix_unitaire_nouveau_1, ebmt.prix_unitaire_nouveau as prix_unitaire_nouveau_2,
                    ebmt.prix_unitaire_nouveau as prix_unitaire_nouveau_3, ebmt.prix_unitaire_nouveau as prix_unitaire_nouveau_4,
                    ebmt.prix_unitaire_nouveau as prix_unitaire_nouveau_5,
                     case when ts_j0_source" . $depot->source_financement_id . " is not null then 1 else 0 end as isJ0Source,
                    fpd1.nom as depot_source1,
                    fpd2.nom as depot_source2,
                    fpd3.nom as depot_source3,
                    fpd4.nom as depot_source4,
                    fpd5.nom as depot_source5, f.id as fiche_id,    
                    fsit1.nom as analyse_source1_statut_interne,
                    fsit2.nom as analyse_source2_statut_interne,
                    fsit3.nom as analyse_source3_statut_interne,
                    fsit4.nom as analyse_source4_statut_interne,
                    fsit5.nom as analyse_source5_statut_interne,
                    fset1.nom as analyse_source1_statut_externe, statut.nom as statut, fiche_statut_id, ebmtc.nom as categorie_equipement, 
                    case when fpd" . $depot->source_financement_id . ".ts_verrouille is not null then 1 else 0 end as isVerrouilleDepot,
                    " . $depot->source_financement_id . " as source_financement_id
                from equipements_bio_medicaux ebm
                join equipements_bio_medicaux_types ebmt on ebmt.id = ebm.equipement_bio_medicaux_type_id
                left join equipements_bio_medicaux_types_categories ebmtc on ebmtc.id = ebmt.equipement_bio_medicaux_type_categorie_id
                left join phases_incidences ph on ph.id = ebmt.phase_incidence_id
                left join equipements_bio_medicaux_types_categories_responsabilite ebmtcr on ebmtcr.id = ebmt.equipements_bio_medicaux_types_categorie_responsabilite_id
                join fiches f on f.id = ebm.fiche_id
                left join fiches_statuts statut on f.fiche_statut_id = statut.id
                left join financements_projets_depots fpd1 on fpd1.id = ebm.depot_source1_id
                left join financements_projets_depots fpd2 on fpd2.id = ebm.depot_source2_id
                left join financements_projets_depots fpd3 on fpd3.id = ebm.depot_source3_id
                left join financements_projets_depots fpd4 on fpd4.id = ebm.depot_source4_id
                left join financements_projets_depots fpd5 on fpd5.id = ebm.depot_source5_id
                left join financements_statuts_internes_types fsit1 on fsit1.id = ebm.analyse_source1_statut_interne_id
                left join financements_statuts_internes_types fsit2 on fsit2.id = ebm.analyse_source2_statut_interne_id
                left join financements_statuts_internes_types fsit3 on fsit3.id = ebm.analyse_source3_statut_interne_id
                left join financements_statuts_internes_types fsit4 on fsit4.id = ebm.analyse_source4_statut_interne_id
                left join financements_statuts_internes_types fsit5 on fsit5.id = ebm.analyse_source5_statut_interne_id
                left join financements_statuts_externes_types fset1 on fset1.id = ebm.analyse_source1_statut_externe_id
                where ebm.projet_id = ? and fiche_id in (" . db::placeHolders($this->fiches) . ") and depot_source" . $depot->source_financement_id . "_id = ?";
	            $liste_tmp = $db->getAssoc($sql, array_merge([getProjetId()], array_keys($this->fiches), [$depot->id]));
	            foreach ($liste_tmp as $id => $ligne) {
		            $liste[$id] = $ligne;
	            }
            }
        }
        return array_values($liste);
    }

    public static function isGestionDesJalons(): bool
    {
        global $CodeClient, $isDev;
        $codesClientToNotBe = $isDev ? ['sqi'] : ['phvs'];
        return !in_array($CodeClient, $codesClientToNotBe);
    }

	public static function isGestionDuClendrier(): bool
	{
		global $CodeClient, $isDev;
		$codesClientToNotBe = $isDev ? ['sqi'] : ['phvs'];
		return !in_array($CodeClient, $codesClientToNotBe);
	}

    public static function getListeErreursImport()
    {
        global $db;

        $sql = "select id, ts_insert
                from financements_imports_erreurs
                where projet_id = ?
                order by ts_insert desc";
        return $db->getAll($sql, [getProjetId()]);
    }

	public static function getErreurImport($id)
	{
		global $db;

		$sql = "select *
                from financements_imports_erreurs
                where id = ?";
		$data_titre = $db->getRow($sql, [$id]);

		$sql = "select *
                from financements_imports_erreurs_lignes
                where financement_import_erreur_id = ?";
		$data_lignes = $db->getAll($sql, [$id]);

        return [$data_titre, $data_lignes];
	}

    public static function traitementSauvegardeSupplementaire($id, $data, $is_ajout, $objAnciennesValeurs = null)
    {
        global $db;
        $sql = "DELETE FROM financements_projets_echeanciers WHERE financement_projet_id = ?";
        $db->query($sql, array($id));

        if (isset($data['echeancier_id']) && count($data['echeancier_id']) > 0) {
            foreach ($data['echeancier_id'] as $ordre => $echeancier_id) {
                $sql = "INSERT INTO financements_projets_echeanciers (financement_projet_id, echeancier_id) 
                            VALUES (?, ?)";
                $db->query($sql, array($id, $echeancier_id));
            }
        }

        $sql = "select fiche_id from financements_projets_fiches WHERE financement_projet_id = ?";
        $fiches_ids_toDelete = $db->getAssoc($sql, [$id]);
        $fiches_added = [];

        if (isset($data['listeFiches']) && count($data['listeFiches']) > 0) {
            foreach ($data['listeFiches'] as $ordre => $fiche_id) {
                if (isset($fiches_ids_toDelete[$fiche_id])) {
                    unset($fiches_ids_toDelete[$fiche_id]);
                } else {
                    $fiches_added[] = $fiche_id;
                    $sql = "INSERT INTO financements_projets_fiches (financement_projet_id, fiche_id) 
                            VALUES (?, ?)";
                    $db->query($sql, array($id, $fiche_id));
                }
            }
        }
        if (count($fiches_ids_toDelete) > 0) {
            $sql = "DELETE FROM financements_projets_fiches WHERE financement_projet_id = ? and fiche_id in (" . dm_implode(', ', array_keys($fiches_ids_toDelete)) . ")";
            $db->query($sql, array($id));
        }

        $depots = FinancementsProjetsDepots::getListe(['financement_projet_id' => $id]);

        $bougeDeDepots = [];
        foreach ($depots as $depot) {
            $sql = "select equipement_bio_medicaux_type_id 
                    from financements_projets_depots_equipements fpde
                    where financement_projet_depot_id = ? and exists (
                        select 1 from equipements_bio_medicaux e where e.depot_source" . $depot->source_financement_id . "_id = fpde.financement_projet_depot_id and fiche_id in (" . db::placeHolders($data['listeFiches']) . ") and e.equipement_bio_medicaux_type_id = fpde.equipement_bio_medicaux_type_id
                    )";
            $equipement_bio_medicaux_type_ids = db::instance()->getCol($sql, array_merge([$depot->id], $data['listeFiches']));

            if (count($equipement_bio_medicaux_type_ids) > 0) {
                $sql = "select fiche_id, depot_source" . $depot->source_financement_id . "_id AS depot_source_id, equipement_bio_medicaux_type_id, fpd.financement_projet_id,
                            f.code as code_fiche, ebmt.nom as code_equipement, fp.numero as projet, fpd.nom as depot
                        from equipements_bio_medicaux e 
                        join financements_projets_depots fpd on fpd.id = e.depot_source" . $depot->source_financement_id . "_id
                        join financements_projets fp on fp.id = fpd.financement_projet_id
                        join fiches f on f.id = e.fiche_id
                        join equipements_bio_medicaux_types ebmt on ebmt.id = e.equipement_bio_medicaux_type_id
                        WHERE e.fiche_id in (" . db::placeHolders($data['listeFiches']) . ") 
                            and e.equipement_bio_medicaux_type_id in (" . db::placeHolders($equipement_bio_medicaux_type_ids) . ")
                            and e.projet_id = ?
                            and depot_source" . $depot->source_financement_id . "_id is not null
                            and depot_source" . $depot->source_financement_id . "_id <> ?";
                $bougeDeDepots = array_merge($bougeDeDepots, $db->getAll($sql, array_merge($data['listeFiches'], $equipement_bio_medicaux_type_ids, [getProjetId(), $depot->id])));
            }

            FinancementsProjetsDepots::updateEquipementsBioMedicaux($depot->id, $data['listeFiches'], $equipement_bio_medicaux_type_ids);
        }

        $_SESSION['avertissementsDepotTransferes'] = null;
        if (count($bougeDeDepots) > 0) {
            $elementsDeplaces = '<br /><table><tr><th>' . txt('Projet') . '</th><th>' . txt('Dépôt') . '</th><th>' . txt('Local') . '</th><th>' . txt('Type d’équipement') . '</th></tr>';

            foreach ($bougeDeDepots as $bougeDeDepot) {
                $elementsDeplaces .= '<tr><td>' . $bougeDeDepot['projet'] . '</td><td>' . $bougeDeDepot['depot'] . '</td><td>' . $bougeDeDepot['code_fiche'] . '</td><td>' . $bougeDeDepot['code_equipement'] . '</td></tr>';
            }
            $elementsDeplaces .= '</table>';
            $_SESSION['avertissementsDepotTransferes'] = txt('Attention! Cet enregistrement a eu effet de transférer certaines occurrences d’équipements appartenant à d’autres demandes d’autorisation de financement (DA).

La cause de ce transfert vient du fait qu’un type d’équipement dans un local donné ne peut appartenir à plusieurs dépôts (par source de financement).

Voici le détail des éléments transférés et leurs provenances :

' . $elementsDeplaces . '
 
Astuce : Pour éviter tout transfert automatique d’équipement(s), nous vous recommandons d’éliminer toute redondance de « Locaux / Type d’équipement » en effectuant une sélection de locaux exclusive à chaque DA, et si cette contrainte est impossible, éliminer les répétitions de types d’équipements dans les dépôts des DA où les sélections de locaux ne sont pas exclusives.', false);
        }

//        if (!$is_ajout) {
//            $projetFinancement = new FinancementsProjets($id);
//            foreach ($projetFinancement->depots as $depot) {
//                if (count($depot->equipements) > 0) {
//                    if (count($fiches_ids_toDelete) > 0) {
//                        $sql = "update equipements_bio_medicaux set
//                                depot_source" . $depot->source_financement_id . "_id = null,
//                                " . $depot->getSqlToNullDepot() . "
//                                where projet_id = ? and depot_source" . $depot->source_financement_id . "_id = ? and equipement_bio_medicaux_type_id in (" . db::placeHolders(array_keys($depot->equipements)) . ")
//                                and fiche_id in (" . db::placeHolders(array_keys($fiches_ids_toDelete)) . ")";
//                        $db->query($sql, array_merge([getProjetId(), $depot->id], array_keys($depot->equipements), array_keys($fiches_ids_toDelete)));
//                    }
//                    if (count($fiches_added) > 0) {
//                        $sql = "update equipements_bio_medicaux set depot_source" . $depot->source_financement_id . "_id = ? where projet_id = ? and equipement_bio_medicaux_type_id in (" . db::placeHolders(array_keys($depot->equipements)) . ") and fiche_id in (" . db::placeHolders($fiches_added) . ")";
//                        $db->query($sql, array_merge([$depot->id, getProjetId()], array_keys($depot->equipements), $fiches_added));
//                    }
//                }
//            }
//        }

        $sql = "DELETE FROM financements_projets_jalons WHERE financement_projet_id = ?";
        $db->query($sql, array($id));
        $sql = "DELETE FROM financements_projets_jalons_non_utilises WHERE financement_projet_id = ?";
        $db->query($sql, array($id));

        if (isset($data['nom_jalon']) && count($data['nom_jalon']) > 0) {
            foreach ($data['nom_jalon'] as $i => $nom_jalon) {
                $ordre = intval($data['ordre'][$i]);
                $tableToUse = ($data['jalon_actif'][$i] == 1) ? 'financements_projets_jalons' : 'financements_projets_jalons_non_utilises';
                //    if ($data['jalon_actif'][$i] != 1) $data['date_jalon'][$i] = null;
                $sql = "INSERT INTO $tableToUse (financement_projet_id, nom_jalon, date_jalon, id_jalon, nb_jours_jalon, ts_complete) 
                            VALUES (?, ?, " . (dm_strlen($data['date_jalon'][$i]) > 0 ? "'" . $data['date_jalon'][$i] . "'" : 'null') . ", ?, " . (intval($data['nb_jours_jalon'][$i]) > 0 ? intval($data['nb_jours_jalon'][$i]) : 'null') . ", " . (isset($data['complete_' . $ordre]) ? 'current_timestamp' : 'null') . ")";
                $db->query($sql, array($id, $nom_jalon, $i));
            }
        }

        if (isset($_POST['listeFiches']) && count($_POST['listeFiches']) > 0) {
            $sql = "select * from financements_projets_groupes_equipements where financement_projet_id = ?";
            $liste_groupes = $db->getAll($sql, array($id));

            foreach ($liste_groupes as $groupe) {
                $sql = "select f.id as fiche_id, f.code, sum(qte_requise) as quantite_requise_actuelle, sum(qte_existante) as quantite_existante_actuelle, sum(qte_conservee) as quantite_demenager_actuelle
							from equipements_bio_medicaux ebm
							join fiches f on ebm.fiche_id = f.id
							where projet_id = ? and ebm.fiche_id in (" . db::placeHolders($_POST['listeFiches']) . ")
							 and equipement_bio_medicaux_type_id = ? and not exists (select fiche_id from financements_projets_groupes_equipements_fiches where financement_projet_groupe_equipement_id = ? and fiche_id = f.id)
							group by fiche_id, f.code
							order by f.code";
                $fiches_qte = $db->getAll($sql, array_merge([getProjetId()], $_POST['listeFiches'], [$groupe['equipement_bio_medicaux_type_id'], $groupe['id']]));

                foreach ($fiches_qte as $fiche) {
                    $sql = "insert into financements_projets_groupes_equipements_fiches (financement_projet_groupe_equipement_id, fiche_id, quantite_requise, quantite_existante, quantite_demenager) values (?, ?, ?, ?, ?)";
                    $db->query($sql, array($groupe['id'], $fiche['fiche_id'], $fiche['quantite_requise_actuelle'], $fiche['quantite_existante_actuelle'], $fiche['quantite_demenager_actuelle']));
                }

                $sql = "delete from financements_projets_groupes_equipements_fiches where financement_projet_groupe_equipement_id = ? and fiche_id not in (" . db::placeHolders($_POST['listeFiches']) . ")";
                $db->query($sql, array_merge([$groupe['id']], $_POST['listeFiches']));
            }
        }
    }

    public function traitementApresSubmit($requestData)
    {
        global $db;
        if (isset($requestData['echeancier_id']) || isset($requestData['nom_jalon']) || isset($requestData['listeFiches'])) {
            $this->jalons = array();
            if (isset($requestData['nom_jalon']) && is_array($requestData['nom_jalon'])) {
                foreach ($requestData['nom_jalon'] as $i => $nom_jalon) {
                    $ordre = intval($requestData['ordre'][$i]);
                    $jalon_array = array(
                        'nom_jalon' => $requestData['nom_jalon'][$i],
                        'date_jalon' => $requestData['date_jalon'][$i],
                        'nb_jours_jalon' => $requestData['nb_jours_jalon'][$i],
                        'id_jalon' => $requestData['id_jalon'][$i],
                        'ts_complete' => '',
                        'actif' => $requestData['jalon_actif'][$i],
                        'ordre' => $requestData['ordre'][$i]
                    );
                    if (isset($requestData['complete_' . $ordre])) {
                        $jalon_array['ts_complete'] = 1;
                    }
                    $this->jalons[] = $jalon_array;
                }
            }

            $this->fiches = array();
            if (isset($requestData['listeFiches']) && is_array($requestData['listeFiches'])) {
                if (count($requestData['listeFiches']) > 0) {
                    $sql = "select id as cle, id, code from fiches where id in (" . db::placeHolders($requestData['listeFiches']) . ")";
                    $fiches = $db->getAssoc($sql, $requestData['listeFiches']);
                    foreach ($requestData['listeFiches'] as $fiche_id) {
                        $this->fiches[$fiche_id] = $fiches[$fiche_id];
                    }
                }
            }

            $this->echeanciers = array();
            if (isset($requestData['echeancier_id']) && is_array($requestData['echeancier_id'])) {
                if (count($requestData['echeancier_id']) > 0) {
                    foreach ($requestData['echeancier_id'] as $echeancier_id) {
                        $this->echeanciers[$echeancier_id] = new Echeanciers($echeancier_id);
                    }
                }
            }
        }

        if (isset($requestData['c'])) {
            $this->fiches = array();
            if (isset($requestData['c']) && is_array($requestData['c'])) {
                if (count($requestData['c']) > 0) {
                    $fiches_ids = [];
                    foreach ($requestData['c'] as $fiche_equ_id) {
                        list($fiche_id, $equipement_id) = dm_explode('_', $fiche_equ_id);
                        $fiches_ids[] = $fiche_id;
                    }
                    $sql = "select id as cle, id, code from fiches where id in (" . db::placeHolders($fiches_ids) . ")";
                    $fiches = $db->getAssoc($sql, $fiches_ids);

                    foreach ($fiches_ids as $fiche_id) {
                        $this->fiches[$fiche_id] = $fiches[$fiche_id];
                    }
                }
            }
        }
    }
}

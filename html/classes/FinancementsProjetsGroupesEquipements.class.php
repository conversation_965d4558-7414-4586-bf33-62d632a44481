<?
class FinancementsProjetsGroupesEquipements extends DbRow {
	public $id;
	public $numero;
	public $description;
	public $equipement_bio_medicaux_type_id;
	public $fiches = array();
	public $ref_nego_existante;
	public $livraison_estime;
	public $debut_contrat;
	public $expiration_contrat;
	public $ref_achat;
	public $ao;
	public $mode_financement;
	public $portee_ao;
	public $num_requisition;
	public $quantite_disponible;
	public $projet_drv;
	public $bon_commande;
	public $avant_taxes;
	public $code_taxe;
	public $fournisseur;
	public $manufacturier;
	public $modele;
	public $commentaires;
	public $statut_livraison;
	public $deficience_livraison;
	public $commentaire_logistique;
	public $financement_projet_id;
	public $champ_supplementaire_groupe_1;
	public $champ_supplementaire_groupe_2;
	public $champ_supplementaire_groupe_3;
	public $champ_supplementaire_groupe_4;
	public $champ_supplementaire_groupe_5;
	public $champ_supplementaire_groupe_6;
	public $champ_supplementaire_groupe_7;
	public $champ_supplementaire_groupe_8;
	public $champ_supplementaire_groupe_9;
	public $champ_supplementaire_groupe_10;
	public $cout_norme;
	public $cout_taxe_net;
	public $cout_projete;

	public function __construct($id, $row = null, $getFiches = true)
	{
	    global $db;

		if (isset($row) && (is_object($row) || is_array($row)))
		{
			$this->populateThisWithThat($row);
		}
		else if ($id > 0)
		{
			$this->setDataForId($id);
		}
		if ($getFiches) {
			$sql = "select fiche_id, t.*
                    from financements_projets_groupes_equipements_fiches t
                    where financement_projet_groupe_equipement_id = ?";
			$this->fiches = $db->getAssoc($sql, array($this->id));
		}
	}

	protected function setDataForId($id)
	{
		global $db;

		$sql = "select t.*, ebmt.prix_unitaire_nouveau as cout_norme
                from financements_projets_groupes_equipements t
                left join equipements_bio_medicaux_types ebmt on t.equipement_bio_medicaux_type_id = ebmt.id
                where t.id = ?";
		$row = $db->getRow($sql, array($id));

		$this->populateThisWithThat($row);
	}

	public static function getListe($filter = array(), $orderBy = 'numero')
	{
		global $db;

		$where = '';
		$data = array();

		if(isset($filter['financement_projet_id']))
		{
			$where .= " and financement_projet_id = ?";
			$data[] = (int)$filter['financement_projet_id'];
		}

		$sql = "select e.*, e.id, ebmt.prix_unitaire_nouveau as cout_norme
                from financements_projets_groupes_equipements e
                left join equipements_bio_medicaux_types ebmt on t.equipement_bio_medicaux_type_id = ebmt.id
                where true $where
                order by $orderBy";
		$rows = $db->getAssoc($sql, $data);

		$liste = array();
		foreach($rows as $i => $row)
		{
			$liste[intval($row['id'])] = new self(intval($row['id']), $row);
		}

		return $liste;
	}

	public function getEditorHTML($iteration = 0, $only_champs_denormalises = false)
	{
	    global $db;

		$admin = FinancementsProjetsAdministrations::getAdminForProjet();

	    $sql_get_last_numero_groupe = "SELECT id FROM financements_projets_groupes_equipements ORDER BY id DESC LIMIT 0,1";
	    $last_id = $db->getOne($sql_get_last_numero_groupe);
        $financement_projet_id = (isset($_GET['financement_projet_id']) ? intval($_GET['financement_projet_id']) : $this->financement_projet_id);

		?>
         <script>
        $(document).ready(function(){
			$('#equipement_bio_medicaux_type_id').select2({
				width: '680px'
			});
        });
        </script>
        <style>
            fieldset.cotacote {
                margin: 0 0 17px 30px;
                width: 42%;
                float: left;
            }
            fieldset.cotacote section > div {
                width: 150px !important;
            }
            #editables fieldset.cotacote label {
                width: 225px !important;
            }
            fieldset.cotacote input[type=text], fieldset.cotacote select {
                min-width: 150px !important;
                width: 150px !important;
            }
            fieldset.cotacote textarea {
                height: 50px !important;
                min-height: 50px !important;
            }
        </style>
		<fieldset class="noMinHeight" style="margin-left:30px;">
            <input type="hidden" name="financement_projet_id" value="<?=$financement_projet_id?>" />
            <section class="formulaire_general">
                <h5 style="text-decoration: underline; margin: 15px 0;"><?= txt('Paramètres généraux') ?></h5>
                <?
                if ($admin->isChampActif('numero')) {
                ?>
                    <div>
                        <label><?= $admin->getLabelForChamp('numero') ?></label>
                        <?php
                        if ($this->numero) {
                         echo $this->numero;

                        } else {
                            $new_id = $last_id + 1;
                            $new_numero = str_pad($new_id, 5, '0', STR_PAD_LEFT);
                            print $new_numero;
                            $this->numero = $new_numero;
                        }
                        ?>
                        <input type="hidden" name="numero" value="<?=$this->numero?>" />
                    </div>
<?
                }
                if ($admin->isChampActif('description')) {
?>
                <div>
					<label><?= $admin->getLabelForChamp('description') ?></label>
                    <input type="text" id="description" name="description" value="<?=($this->description)?>" style="" class="" autocomplete="off" />
                </div>
	                <?
                }
                if ($admin->isChampActif('code_equipement')) {
                ?>
                    <div style="width: 900px!important;">
                        <label><?= $admin->getLabelForChamp('code_equipement') ?></label>
                        <select id="equipement_bio_medicaux_type_id" name="equipement_bio_medicaux_type_id" <?=($this->id > 0 ? ' class="readonly"' : '')?> style="max-width: 680px;">
                            <option value=""> </option>
                            <?
                            if ($this->id > 0) {
                                $row = new EquipementsBioMedicauxTypes($this->equipement_bio_medicaux_type_id);
                                $rows[] = ['equipement_bio_medicaux_type_id' => $row->id, 'equipement' => $row->nom.' - '.$row->description];
                            } else {
                                $rows = EquipementsBioMedicauxTypes::getListeACommander(array('financement_projet_id' => $financement_projet_id));
                            }
                            foreach($rows as $i => $row)
                            {
                                ?>
                                <option value="<?=$row['equipement_bio_medicaux_type_id']?>"<?=($row['equipement_bio_medicaux_type_id'] == $this->equipement_bio_medicaux_type_id ? ' selected="selected"' : '')?>><?=$row['equipement']?></option>
                                <?php
                            }
                            ?>
                        </select>
                    </div>
	                <?
                }

                for ($i = 1; $i <= 10; $i++) {
                    $champ = 'champ_supplementaire_groupe_' . $i;
                    if ($admin->isChampActif($champ)) {
                        ?>
                        <div style="width: 900px!important;">
                            <label><?= $admin->getLabelForChamp($champ) ?></label>
                            <input type="text" id="<?= $champ ?>" name="<?= $champ ?>" value="<?= $this->$champ ?>" />
                        </div>
                        <?
                    }
                }
                ?>
            </section>
        </fieldset>
        <fieldset class="noMinHeight cotacote">
            <?
            if ($admin->isBlocActif([
                'livraison_estime', 'ao', 'mode_financement', 'portee_ao', 'num_requisition', 'bon_commande', 'fournisseur', 'manufacturier', 'modele'
            ])) {
            ?>
                <section class="dossier_financement">
                    <h5 style="text-decoration: underline; margin: 15px 0;"><?= txt('Dossier de financement') ?></h5>
                    <?
                    if ($admin->isChampActif('livraison_estime')) {
                    ?>
                        <label><?= $admin->getLabelForChamp('livraison_estime') ?></label>
                        <div style="">
                            <input type="text" id="livraison_estime" name="livraison_estime" value="<?=($this->livraison_estime)?>" style="" class="" autocomplete="off" />
                        </div>
                    <?
                    }
                    if ($admin->isChampActif('ao')) {
                    ?>
                        <label><?= $admin->getLabelForChamp('ao') ?></label>
                        <div style="">
                            <input type="text" id="ao" name="ao" value="<?=($this->ao)?>" style="" class="" autocomplete="off" />
                        </div>
                        <?
                    }
                    if ($admin->isChampActif('mode_financement')) {
                    ?>
                        <label><?= $admin->getLabelForChamp('mode_financement') ?></label>
                        <div style="">
                            <input type="text" id="mode_financement" name="mode_financement" value="<?=($this->mode_financement)?>" style="" class="" autocomplete="off" />
                        </div>
                        <?
                    }
                    if ($admin->isChampActif('portee_ao')) {
                    ?>
                        <label><?= $admin->getLabelForChamp('portee_ao') ?></label>
                        <div style="">
                            <input type="text" id="portee_ao" name="portee_ao" value="<?=($this->portee_ao)?>" style="" class="" autocomplete="off" />
                        </div>
                        <?
                    }
                    if ($admin->isChampActif('num_requisition')) {
                    ?>
                        <label><?= $admin->getLabelForChamp('num_requisition') ?></label>
                        <div style="">
                            <input type="text" id="num_requisition" name="num_requisition" value="<?=($this->num_requisition)?>" style="" class="" autocomplete="off" />
                        </div>
                        <?
                    }
                    if ($admin->isChampActif('bon_commande')) {
                    ?>
                        <label><?= $admin->getLabelForChamp('bon_commande') ?></label>
                        <div style="">
                            <input type="text" id="bon_commande" name="bon_commande" value="<?=($this->bon_commande)?>" style="" class="" autocomplete="off" />
                        </div>
                        <?
                    }
                    if ($admin->isChampActif('fournisseur')) {
                    ?>
                        <label><?= $admin->getLabelForChamp('fournisseur') ?></label>
                        <div style="">
                            <input type="text" id="fournisseur" name="fournisseur" value="<?=($this->fournisseur)?>" style="" class="" autocomplete="off" />
                        </div>
                        <?
                    }
                    if ($admin->isChampActif('manufacturier')) {
                    ?>
                        <label><?= $admin->getLabelForChamp('manufacturier') ?></label>
                        <div style="">
                            <input type="text" id="manufacturier" name="manufacturier" value="<?=($this->manufacturier)?>" style="" class="" autocomplete="off" />
                        </div>
                        <?
                    }
                    if ($admin->isChampActif('modele')) {
                    ?>
                        <label><?= $admin->getLabelForChamp('modele') ?></label>
                        <div style="">
                            <input type="text" id="modele" name="modele" value="<?=($this->modele)?>" style="" class="" autocomplete="off" />
                        </div>
                        <?
                    }
                    ?>
                </section>
<?
            }

            if ($admin->isBlocActif([
                'projet_drv', 'avant_taxes', 'code_taxe', 'cout_taxe_net', 'cout_projete', 
            ])) {
            ?>
                <section class="suivi_budgetaire">

                    <h5 style="text-decoration: underline; margin: 15px 0;"><?= txt('Gestion des sources de financement') ?></h5>

                    <?
                    if ($admin->isChampActif('projet_drv')) {
                    ?>
                        <label><?= $admin->getLabelForChamp('projet_drv') ?></label>
                        <div style="">
                                <input type="text" id="projet_drv" name="projet_drv" value="<?=($this->projet_drv)?>" style="" class="" autocomplete="off" />
                        </div>
                        <?
                    }
                    if ($admin->isChampActif('avant_taxes')) {
                    ?>
                        <label><?= $admin->getLabelForChamp('avant_taxes') ?></label>
                        <div style="">
                                <input type="text" id="avant_taxes" name="avant_taxes" value="<?=($this->avant_taxes)?>" style="" class="" autocomplete="off" />
                        </div>
                        <?
                    }
                    if ($admin->isChampActif('code_taxe')) {
                    ?>
                        <label><?= $admin->getLabelForChamp('code_taxe') ?></label>
                        <div style="">
                                <input type="text" id="code_taxe" name="code_taxe" value="<?=($this->code_taxe)?>" style="" class="" autocomplete="off" />
                        </div>
                        <?
                    }
                	if ($admin->isChampActif('cout_taxe_net')) {
                	?>
                    <label><?= $admin->getLabelForChamp('cout_taxe_net') ?></label>
                    <div style="position: relative;">
                        <input type="text" id="cout_taxe_net" name="cout_taxe_net" value="<?=($this->cout_taxe_net)?>" style="" class="" autocomplete="off" />
                        <span style="position: absolute; right: -70px; top: 3px;"></span>
                    </div>
                    <?
                }
            	if ($admin->isChampActif('cout_projete')) {
                ?>
                    <label><?= $admin->getLabelForChamp('cout_projete') ?></label>
                    <div style="position: relative;">
                         <input type="text" id="cout_projete" name="cout_projete" value="<?=($this->cout_projete)?>" style="" class="" autocomplete="off" />
                        <span style="position: absolute; right: -70px; top: 3px;"></span>
                    </div>
                    <?
                }
                    ?>
                </section>
            <?
            }
            if ($admin->isChampActif('commentaires')) {
                ?>
                <section>
                    <label><?= $admin->getLabelForChamp('commentaires') ?></label>
                    <div style="width:597px;height:75px">
                        <textarea id="commentaires" name="commentaires" style="width:597px;height:75px" class="" autocomplete="off"><?=($this->commentaires)?></textarea>
                    </div>
                </section>
            <?
            }
            ?>
        </fieldset>
<?
    if ($admin->isBlocActif([
        'statut_livraison', 'deficience_livraison', 'commentaire_logistique'
    ])) {
?>
        <fieldset class="noMinHeight cotacote">

            <section class="logistique_livraison">

                <h5 style="text-decoration: underline; margin: 15px 0;"><?= txt('Logistique de livraison') ?></h5>
                <?
                
                if ($admin->isChampActif('statut_livraison')) {
                ?>
                    <label><?= $admin->getLabelForChamp('statut_livraison') ?></label>
                    <div style="">
                        <input type="text" id="statut_livraison" name="statut_livraison" value="<?=($this->statut_livraison)?>" style="" class="" autocomplete="off" />
                    </div>
                    <?
                }
                if ($admin->isChampActif('deficience_livraison')) {
                ?>
                    <label><?= $admin->getLabelForChamp('deficience_livraison') ?></label>
                    <div style="">
                        <input type="text" id="deficience_livraison" name="deficience_livraison" value="<?=($this->deficience_livraison)?>" style="" class="" autocomplete="off" />
                    </div>
                    <?
                }
                if ($admin->isChampActif('commentaire_logistique')) {
                ?>
                    <label><?= $admin->getLabelForChamp('commentaire_logistique') ?></label>
                    <div style="">
                        <input type="text" id="commentaire_logistique" name="commentaire_logistique" value="<?=($this->commentaire_logistique)?>" style="" class="" autocomplete="off" />
                    </div>
                    <?
                }
                ?>
            </section>
        </fieldset>
        <?
    }

    if ($admin->isBlocActif([
        'ref_nego_existante', 'debut_contrat', 'expiration_contrat', 'ref_achat'
    ])) {
    ?>
        <fieldset class="noMinHeight cotacote">
            <section class="reference_interne">
                <h5 style="text-decoration: underline; margin: 15px 0;"><?= txt('Références interne') ?></h5>
                <?
                if ($admin->isChampActif('ref_nego_existante')) {
                ?>
                    <label><?= $admin->getLabelForChamp('ref_nego_existante') ?></label>
                    <div style="">
                        <input type="text" id="ref_nego_existante" name="ref_nego_existante" value="<?=($this->ref_nego_existante)?>" style="" class="" autocomplete="off" />
                    </div>
                    <?
                }
                if ($admin->isChampActif('debut_contrat')) {
                ?>
                    <label><?= $admin->getLabelForChamp('debut_contrat') ?></label>
                                    <div style="position: relative;">
                        <input type="text" id="debut_contrat" name="debut_contrat" value="<?=($this->debut_contrat)?>" style="" class="" autocomplete="off" />
                                            <span style="position: absolute; right: -70px; top: 3px;">(aaaa-mm-jj)</span>
                    </div>
                    <?
                }
                if ($admin->isChampActif('expiration_contrat')) {
                ?>
                    <label><?= $admin->getLabelForChamp('expiration_contrat') ?></label>
                                    <div style="position: relative;">
                        <input type="text" id="expiration_contrat" name="expiration_contrat" value="<?=($this->expiration_contrat)?>" style="" class="" autocomplete="off" />
                                            <span style="position: absolute; right: -70px; top: 3px;">(aaaa-mm-jj)</span>
                    </div>
                    <?
                }
                if ($admin->isChampActif('ref_achat')) {
                ?>
                    <label><?= $admin->getLabelForChamp('ref_achat') ?></label>
                    <div style="">
                        <input type="text" id="ref_achat" name="ref_achat" value="<?=($this->ref_achat)?>" style="" class="" autocomplete="off" />
                    </div>
                    <?
                }
                ?>
            </section>
        </fieldset>
		<?php
            }
	}

	public static function delete($id)
	{
		global $db;
		$db->autocommit(false);
		$db->query("DELETE FROM `financements_projets_groupes_equipements` WHERE id = ?", array($id));
		$db->commit();
	}

	public static function validate($data, $id)
	{
		$valide = true;
		foreach($data as $colomnName => $value)
		{
			if($colomnName == 'numero')
			{
				if(dm_strlen($value) == 0)
				{
					setErreur(txt('Le numéro est obligatoire.'));
					$valide = false;
				}
			}
			if($colomnName == 'equipement_bio_medicaux_type_id')
			{
				if(dm_strlen($value) == 0)
				{
					setErreur(txt('L\'équipement est obligatoire.'));
					$valide = false;
				}
			}
		}
		return $valide;
	}

	public function isLocked()
	{
		if(dm_strlen($this->ts_verrouille) > 0)
		{
			return true;
		}
		return false;
	}

	public function isDonneesDenormalisees() {
		if(isset($this->equipement_bio_medicaux_type_id) && (int)$this->equipement_bio_medicaux_type_id > 0)
		{
			return false;
		}
		return true;
    }

    public static function traitementSauvegardeSupplementaire($id, $data, $is_ajout, $objAnciennesValeurs = null)
    {
        global $db;
        if ($is_ajout) {
            $financement_projet = new FinancementsProjets(intval($_POST['financement_projet_id']));
            if (isset($financement_projet->fiches) && count($financement_projet->fiches) > 0) {
                $sql = "select f.id as fiche_id, f.code, sum(qte_requise) as quantite_requise_actuelle, sum(qte_existante) as quantite_existante_actuelle, sum(qte_conservee) as quantite_demenager_actuelle
                        from equipements_bio_medicaux ebm
                        join fiches f on ebm.fiche_id = f.id
                        where projet_id = ? and ebm.fiche_id in (" . dm_implode(", ", array_keys($financement_projet->fiches)) . ") and equipement_bio_medicaux_type_id = ?
                        group by fiche_id, f.code
                        order by f.code";
                $fiches_qte = $db->getAll($sql, array(getProjetId(), $_POST['equipement_bio_medicaux_type_id']));

                foreach ($fiches_qte as $fiche) {
                    $sql = "insert into financements_projets_groupes_equipements_fiches (financement_projet_groupe_equipement_id, fiche_id, quantite_requise, quantite_existante, quantite_demenager) values (?, ?, ?, ?, ?)";
                    $db->query($sql, array($id, $fiche['fiche_id'], $fiche['quantite_requise_actuelle'], $fiche['quantite_existante_actuelle'], $fiche['quantite_demenager_actuelle']));
                }
            }
        }
    }
}

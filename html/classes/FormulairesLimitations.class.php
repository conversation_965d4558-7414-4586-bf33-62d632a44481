<?
class FormulairesLimitations extends DbRow {
	public $id;
	public $projet_id;
	public $formulaire_id;
	public $formulaire;
	public $can_add;
	public $can_delete;
	public $usagers;

	protected function setDataForId($id)
	{
		global $db;

		$sql = "select *,
				(select nom from formulaires where id = formulaire_id) as formulaire
                from formulaires_limitations 
                where id = ?";
		$row = $db->getRow($sql, array($id));

		$sql = "select u.id as usager_id, concat(u.prenom, ' ', u.nom) as usager
                from formulaires_limitations_usagers_exceptions fuer 
                join usagers u on u.id = fuer.usager_id
                where formulaire_limitation_id = ?";
		$rows_usagers = $db->getAll($sql, array($id));

		$usagers = array();
		foreach($rows_usagers as $i => $row_usager)
		{
			$usagers[$row_usager['usager_id']] = $row_usager['usager'];
		}
		$row['usagers'] = $usagers;

		$this->populateThisWithThat($row);
	}

	public static function getListe($filter = array(), $orderBy = 'nom', $all_data = false)
	{
		global $db;

		$where = '';
		$data = array();

		$where .= " and projet_id = ?";
		$data[] = getProjetId();

		$sql = "select id, formulaires_limitations.*,
				(select nom from formulaires where id = formulaire_id) as formulaire
                from formulaires_limitations 
                where true $where
                order by formulaire";
		$rows = $db->getAssoc($sql, $data);

		$formulaire_limitation_ids = array_keys($rows);

		$usagers = array();
		if(count($formulaire_limitation_ids) > 0)
		{
			$sql = "select u.id as usager_id, concat(u.prenom, ' ', u.nom) as usager, formulaire_limitation_id
                    from formulaires_limitations_usagers_exceptions fuer 
                    join usagers u on u.id = fuer.usager_id
                    where formulaire_limitation_id in (".db::placeHolders($formulaire_limitation_ids).")";
			$rows_usagers = $db->getAll($sql, $formulaire_limitation_ids);

			foreach($rows_usagers as $i => $row_usager)
			{
				$usagers[intval($row_usager['formulaire_limitation_id'])][$row_usager['usager_id']] = $row_usager['usager'];
			}
		}

		$liste = array();
		foreach($rows as $i => $row)
		{
			$obj = new self(intval($row['id']), $row);

			$obj->usagers = array();
			if(isset($usagers[$obj->id]))
			{
				$obj->usagers = $usagers[$obj->id];
			}

			$liste[$obj->id] = $obj;
		}

		return $liste;
	}

	public function getEditorHTML($iteration = 0, $only_champs_denormalises = false)
	{
		?>
		<section id="formulaire_liste">
			<label for="formulaire_id_form_limitations"><?=txt('Formulaire')?></label>
			<div>
				<select id="formulaire_id_form_limitations" name="formulaire_id">
					<option value=""> </option>
					<?=Formulaires::getOptions(array('ids_notin_limitations' => (isset($this->formulaire_id) ? $this->formulaire_id : 0)), $this->formulaire_id)?>
				</select>
			</div>
		</section>

		<section>
			<label for="usager_id"><? print txt('Usagers non affectés'); ?></label>
			<div>
				<div>
					<select class="multipleSelect" data-colname="usager_id" id="usager_id_adder">
						<option value=""> </option>
						<?
						Usager::getOptions();
						?>
					</select>
				</div>
				<div id="usager_id">
					<?
					if(isset($this->usagers) && is_array($this->usagers))
					{
						foreach($this->usagers as $usager_id => $usager)
						{
							?>
							<div id="usager_id" class="top_block">
								<a href="javascript: void(0);" class="del_multiple">
									<img src="css/images/icons/dark/trashcan.png" /></a>
								<span><?=$usager?></span>
								<input type="hidden" name="usager_id[]" value="<?=$usager_id?>" />
							</div>
							<?
						}
					}
					?>
				</div>
			</div>
		</section>
		<?
	}

	public static function getDynamicTableTitle()
	{
		?>
		{ "sTitle": "<? print txt('Formulaire'); ?>", "sClass": "dataTableLargetTitle" },
		{ "sTitle": "<? print txt('Usagers non affectés', false); ?>", "sClass": "dataTableLargetTitle" },
		<?
	}

	public function getDynamicTableContent()
	{
		$className = $this->formulaire;
		?>
		"<? print dm_addslashes($className::TITRE); ?>",
		"<? print count($this->usagers) > 0 ? dm_addslashes(dm_implode(', ', $this->usagers)) : ''; ?>"
		<?
	}

	public static function delete($id)
	{
		global $db;
		$db->autocommit(false);
		$db->query("DELETE FROM `formulaires_limitations_usagers_exceptions` WHERE formulaire_limitation_id = ?", array($id));
		$db->query("DELETE FROM `formulaires_limitations` WHERE id = ?", array($id));
		$db->commit();
	}

	public static function validate($data, $id)
	{
		$valide = true;
		foreach($data as $colomnName => $value)
		{
			if($colomnName == 'formulaire_id')
			{
				if(dm_strlen($value) == 0)
				{
					setErreur(txt('Le formulaire est obligatoire.'));
					$valide = false;
				}
			}
		}
		return $valide;
	}

    public static function traitementSauvegardeSupplementaire($id, $data, $is_ajout, $objAnciennesValeurs = null)
    {
        global $db;
        $sql = "update formulaires_limitations set can_add = ?, can_delete = ? where id = ?";
        $db->query($sql, array((isset($data['can_add']) ? 1 : 0), (isset($data['can_delete']) ? 1 : 0), $id));

        $sql = "DELETE FROM formulaires_limitations_usagers_exceptions WHERE formulaire_limitation_id = ?";
        $db->query($sql, array($id));

        if (isset($data['usager_id']) && count($data['usager_id']) > 0) {
            foreach ($data['usager_id'] as $ordre => $usager_id) {
                $sql = "INSERT INTO formulaires_limitations_usagers_exceptions (formulaire_limitation_id, usager_id) 
                            VALUES (?, ?)";
                $db->query($sql, array($id, $usager_id));
            }
        }
    }
}
?>
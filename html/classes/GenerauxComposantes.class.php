<?
class GenerauxComposantes extends FicheFormWrapper {
	private static $CLASS_NAME='GenerauxComposantes';
	const SQL_TABLE_NAME='generaux_composantes';
	const TITRE='Propriétés générales';
	const CAN_DELETE=false;
	public static $AUTO=true;
    public static $TYPE_FORM='UNIQUE';
    public $id;
	public $fiche_id;
	public $marque_id;
	public $utilite;
	public $remarque;
    public $local_id;
    public $localisation;
    public $fonctionnement_id;
    public $element_id;
    public $modele_id;
    public $nomenclature_id;
    public $nomenclature;
	public $numero_lot;
	public $fiche_parent_id;
	public $fiche_parent;
	public $fiche_type_id;
	public $fiche_sous_type_id;
	public $batiment_id;
	public $code_alternatif;
	public $code_alternatif_2;
	public $id_revit;
	public $id_codebook;
	public $code;
    public $batiment_niveau_element_id;
    public $batiment_niveau_element;
	public $fiche_type;
	public $fiche_sous_type;
	public $batiment;
	public $fiche_local_id;
	public $fiche_local;
	public $champ_combine_1;
	public $champ_combine_2;
	public $champ_combine_3;
	public $champ_combine_4;
	public $champ_combine_5;

	public static function getChampsSaisieMetaData()
    {
        global $Langue;
        $return_array = array(
            
            'nomenclature' => array(
                'classe'      	=> 'Nomenclature',
                'table'      	=> 'nomenclatures',
                'champ_id'      => 'nomenclature_id',
                'type'          => 'select',
                'format'        => 'int',
                'titre'         => txt('Nomenclature', false),
                'css_editeur'   => 'xxlarge',
                'sql_for_nom'   => "concat(code, ' - ', nomenclatures.nom_$Langue)"
            ),
            
            'utilite' => array(
                'table'      	=> '',
                'champ_id'      => 'utilite',
                'type'          => 'input',
                'format'        => 'text',
                'titre'         => txt('Utilité', false),
                'css_editeur'   => 'small'
            ),
            
            'fonctionnement' => array(
                'classe'      	=> 'Fonctionnement',
                'table'      	=> 'fonctionnements',
                'champ_id'      => 'fonctionnement_id',
                'type'          => 'select',
                'format'        => 'int',
                'titre'         => txt('Fonctionnement', false),
                'css_editeur'   => 'xxlarge'
            ),
            
            'element' => array(
                'classe'      	=> 'Element',
                'table'      	=> 'elements',
                'champ_id'      => 'element_id',
                'type'          => 'select',
                'format'        => 'int',
                'titre'         => txt('Élément traité', false),
                'css_editeur'   => 'xxlarge'
            ),
            
            'localisation' => array(
                'table'      	=> '',
                'champ_id'      => 'localisation',
                'type'          => 'input',
                'format'        => 'text',
                'titre'         => txt('Localisation', false),
                'css_editeur'   => 'xxlarge'
            ),
            
            'marque' => array(
                'classe'      	=> 'Marque',
                'table'      	=> 'marques',
                'champ_id'      => 'marque_id',
                'type'          => 'select',
                'format'        => 'int',
                'titre'         => txt('Marque', false),
                'css_editeur'   => 'xxlarge',
                'champ_enfant_dependance' => 'modele_id',
            ),
            
            'modele' => array(
                'classe'      	=> 'Modele',
                'table'      	=> 'modeles',
                'champ_id'      => 'modele_id',
                'type'          => 'select',
                'format'        => 'int',
                'titre'         => txt('Modèle', false),
                'css_editeur'   => 'xxlarge',
                'dependance_id' => 'marque_id',
                'dependance_champ_liste' => 'marque_id'
            ),
            
            'remarque' => array(
                'table'      	=> '',
                'champ_id'      => 'remarque',
                'type'          => 'textarea',
                'format'        => 'text',
                'titre'         => txt('Commentaires', false),
                'css_editeur'   => 'xxlarge'
            ),
            
        );
		return parent::parseMetaData($return_array);
    }
	
	public static function getListe($filter = array(), $orderBy = 'id', $extra_data = null)
	{
		return parent::getListe($filter, $orderBy, $extra_data);
	}
    
    public static function getFormHTML($fiche_id, $liste_data = null, $forced_liste = null)
	{
        return parent::getUniqueFormHTML($fiche_id, $liste_data);
    }
    
    public function getEditorHTML($iteration = 0, $only_champs_denormalises = false)
	{
?>
        <style>
            #editables label {
                width: 275px !important;
            }
            .edit_popupContent section > div {
                width: 690px !important;
            }
            #editables input[type=checkbox] {
                position: relative;
                top: 1px;
            }
        </style>
<?
        parent::getEditorHTML($iteration);
    }
    
    public static function getFicheHTML($fiche, $liste = null, $nb_archives = 0, $can_edit = false, $projet_courant_id = null, $gabarit = null)
	{
?>
        <style>
            .blocsFiche section label {
                width: 260px !important;
            }
        </style>
<?
        if(isset($liste) && is_array($liste))
        {
            $liste_tmp = array_values($liste);
            if(isset($liste_tmp[0]))
            {
                $obj = $liste_tmp[0];
            }
            else
            {
                $currentClassName = get_called_class();
                $obj = $currentClassName::getOne($fiche->id);
            }
        }
        else
        {
            $currentClassName = get_called_class();
            $obj = $currentClassName::getOne($fiche->id);
        }
        
        parent::getUniqueFicheHTML($fiche, $obj, $nb_archives, $can_edit, $projet_courant_id);
        
        parent::getAutoFicheHtml($fiche->id, $obj);
    }
    
    public static function getDiffArchiveHTML($fiche_id, $liste, $last_liste, $token_archive, $last_token_archive)
	{
?>
        <style>
            .blocsFiche section label {
                width: 260px !important;
            }
        </style>
<?
        $liste = array_values($liste);
        $last_liste = array_values($last_liste);
        if(isset($liste[0]))
        {
            $obj = $liste[0];
        }
        
        if(isset($last_liste[0]))
        {
            $last_obj = $last_liste[0];
        }
        else
        {
            $last_obj = new self(0);
        }
        
?>
        <h2 class="blocsFicheTitre" style="height: 30px;">
            <?=txt(self::TITRE)?></h2>
<?
        
        parent::getAutoDiffArchiveHtml($fiche_id, $obj, $last_obj, $token_archive, $last_token_archive);
    }
    
    public function getFiltreHTML()
    {
?>
        <fieldset class="noMinHeight">
<?
            $this->getEditorHTML();
?>
        </fieldset>
<?
    }
}
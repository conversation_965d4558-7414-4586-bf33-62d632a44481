<?
class GenerauxInfrastructures extends FicheFormWrapper {
	private static $CLASS_NAME='GenerauxInfrastructures';
	const SQL_TABLE_NAME='generaux_infrastructures';
	const TITRE='Propriétés générales';
	const CAN_DELETE=false;
	public static $AUTO=true;
    public static $TYPE_FORM='UNIQUE';
    public $id;
	public $fiche_id;
	public $utilite;
	public $type_soin_id;
	public $type_soin;
	public $remarque;
	public $niveaux_1_id;
	public $niveaux_2_id;
	public $niveaux_3_id;
	public $niveaux_4_id;
	public $niveaux_5_id;
	public $niveaux_6_id;
	public $zone_id;
	public $bloc_id;
	public $etage_id;
	public $zone;
	public $bloc;
	public $etage;
	public $numero_lot;
	public $fiche_parent_id;
	public $fiche_parent;
	public $fiche_type_id;
	public $fiche_sous_type_id;
	public $batiment_id;
	public $code_alternatif;
	public $code_alternatif_2;
	public $id_revit;
	public $id_codebook;
	public $route_id;
	public $route;
	public $chainage_debut;
	public $chainage_fin;
	public $code;
    public $batiment_niveau_element_id;
    public $batiment_niveau_element;
	public $fiche_type;
	public $fiche_sous_type;
	public $batiment;
	public $fiche_local_id;
	public $fiche_local;
	public $fiche_local_destination_id;
	public $fiche_local_destination;
	public $fiche_local_controleur_id;
	public $fiche_local_controleur;
	public $champ_combine_1;
	public $champ_combine_2;
	public $champ_combine_3;
	public $champ_combine_4;
	public $champ_combine_5;

	public static function getChampsSaisieMetaData()
    {
        $return_array = array(
            /*
            'route' => array(
				'classe'      	=> 'Routes',
				'table'      	=> 'routes',
                'champ_id'      => 'route_id',
                'type'          => 'select',
                'format'        => 'int',
                'titre'         => txt('Route', false),
                'css_editeur'   => 'medium',
                'hidden'        => 1,
            ),
            
            'chainage_debut' => array(
                'classe'      	=> '',
                'table'      	=> '',
                'champ_id'      => 'chainage_debut',
                'type'          => 'input',
                'format'        => 'text',
                'titre'         => txt('Chaînage début', false),
                'css_editeur'   => 'small',
                'hidden'        => 1,
            ),
            
            'chainage_fin' => array(
                'classe'      	=> '',
                'table'      	=> '',
                'champ_id'      => 'chainage_fin',
                'type'          => 'input',
                'format'        => 'text',
                'titre'         => txt('Chaînage fin', false),
                'css_editeur'   => 'small',
                'hidden'        => 1,
            ),
            */
            'code' => array(
                'table'      	=> '',
                'champ_id'      => 'code',
                'type'          => 'input',
                'format'        => 'text',
                'titre'         => txt('Code', false),
                'css_editeur'   => 'small',
                'hidden'        => 1,
            ),
            
            'batiment' => array(
                'classe'      	=> 'Batiment',
                'table'      	=> 'batiments',
                'champ_id'      => 'batiment_id',
                'type'          => 'select',
                'format'        => 'int',
                'titre'         => txt('Bâtiment', false),
                'css_editeur'   => 'xxlarge',
                'hidden'        => 1,
            ),
            
            'fiche_type' => array(
                'classe'      	=> 'FicheType',
                'table'      	=> 'fiches_types',
                'champ_id'      => 'fiche_type_id',
                'type'          => 'select',
                'format'        => 'int',
                'titre'         => txt('Type', false),
                'css_editeur'   => 'xxlarge',
                'hidden'        => 1,
            ),
            
            'fiche_sous_type' => array(
                'classe'      	=> 'FicheSousType',
                'table'      	=> 'fiches_sous_types',
                'champ_id'      => 'fiche_sous_type_id',
                'type'          => 'select',
                'format'        => 'int',
                'titre'         => txt('Sous-Type', false),
                'css_editeur'   => 'xxlarge',
                'hidden'        => 1,
            ),
            
            'numero_lot' => array(
                'table'      	=> '',
                'champ_id'      => 'numero_lot',
                'type'          => 'input',
                'format'        => 'text',
                'titre'         => txt('Numéro de lot', false),
                'css_editeur'   => 'small',
                'hidden'        => 1,
            ),
            
            'code_alternatif' => array(
                'table'      	=> '',
                'champ_id'      => 'code_alternatif',
                'type'          => 'input',
                'format'        => 'text',
                'titre'         => txt('Code alternatif', false),
                'css_editeur'   => 'small',
                'hidden'        => 1,
            ),

	        'code_alternatif_2' => array(
		        'table'      	=> '',
		        'champ_id'      => 'code_alternatif_2',
		        'type'          => 'input',
		        'format'        => 'text',
		        'titre'         => txt('Code alternatif 2', false),
		        'css_editeur'   => 'small',
		        'hidden'        => 1,
	        ),
            
            'id_revit' => array(
                'table'      	=> '',
                'champ_id'      => 'id_revit',
                'type'          => 'input',
                'format'        => 'text',
                'titre'         => txt('ID Externe 1', false),
                'css_editeur'   => 'small',
                'hidden'        => 1,
            ),
            
            'id_codebook' => array(
                'table'      	=> '',
                'champ_id'      => 'id_codebook',
                'type'          => 'input',
                'format'        => 'text',
                'titre'         => txt('ID Externe 2', false),
                'css_editeur'   => 'small',
                'hidden'        => 1,
            ),
            
            'batiment_niveau_element' => array(
                'classe'      	=> 'BatimentsNiveaux',
                'table'      	=> 'batiments_niveaux_elements',
                'champ_id'      => 'batiment_niveau_element_id',
                'type'          => 'select',
                'format'        => 'int',
                'titre'         => txt('Structure hiérarchique du local', false),
                'css_editeur'   => 'xxlarge',
                'hidden'        => 1,
            ),
            
            'fiche_local' => array(
                'classe'      	=> 'Fiches',
                'table'      	=> 'fiches',
                'champ_id'      => 'fiche_local_id',
                'type'          => 'select',
                'format'        => 'int',
                'titre'         => txt('Local maître', false),
                'css_editeur'   => 'xxlarge',
                'sql_for_nom'   => 'code',
                'hidden'        => 1,
            ),
            
            'fiche_local_destination' => array(
                'classe'      	=> 'Fiches',
                'table'      	=> 'fiches',
                'champ_id'      => 'fiche_local_destination_id',
                'type'          => 'select',
                'format'        => 'int',
                'titre'         => txt('Local (vers)', false),
                'css_editeur'   => 'xxlarge',
                'sql_for_nom'   => 'code',
                'hidden'        => 1,
            ),
            
            'fiche_parent' => array(
                'classe'      	=> 'Fiches',
                'table'      	=> 'fiches',
                'champ_id'      => 'fiche_parent_id',
                'type'          => 'select',
                'format'        => 'int',
                'titre'         => txt('Équip./système d\'appartenance (fiche maître)', false),
                'css_editeur'   => 'xxlarge',
                'sql_for_nom'   => 'code',
                'hidden'        => 1,
            ),
            
            'fiche_local_controleur' => array(
                'classe'      	=> 'Fiches',
                'table'      	=> 'fiches',
                'champ_id'      => 'fiche_local_controleur_id',
                'type'          => 'select',
                'format'        => 'int',
                'titre'         => txt('Localisation du contrôleur', false),
                'css_editeur'   => 'xxlarge',
                'sql_for_nom'   => 'code',
                'hidden'        => 1,
            ),
            
            'utilite' => array(
                'table'      	=> '',
                'champ_id'      => 'utilite',
                'type'          => 'input',
                'format'        => 'text',
                'titre'         => txt('Utilité', false),
                'css_editeur'   => 'small'
            ),
            
            'bloc' => array(
				'classe'      	=> 'AppelsLocauxGenerauxBlocs',
				'table'      	=> 'appels_locaux_generaux_blocs',
                'champ_id'      => 'bloc_id',
                'type'          => 'select',
                'format'        => 'int',
                'titre'         => txt('Bloc', false),
                'css_editeur'   => 'medium',
                'from_fiche_id'  => 1
            ),
            
            'zone' => array(
				'classe'      	=> 'AppelsLocauxGenerauxZones',
				'table'      	=> 'appels_locaux_generaux_zones',
                'champ_id'      => 'zone_id',
                'type'          => 'select',
                'format'        => 'int',
                'titre'         => txt('Zone', false),
                'css_editeur'   => 'medium',
                'from_fiche_id'  => 1
            ),
            
            'etage' => array(
				'classe'      	=> 'AppelsLocauxGenerauxEtages',
				'table'      	=> 'appels_locaux_generaux_etages',
                'champ_id'      => 'etage_id',
                'type'          => 'select',
                'format'        => 'int',
                'titre'         => txt('Étage', false),
                'css_editeur'   => 'medium',
                'from_fiche_id'  => 1
            ),
            
            'type_soin' => array(
                'table'      	=> '',
                'champ_id'      => 'type_soin_id',
                'type'          => 'select',
                'format'        => 'int',
                'titre'         => txt('Type de soins (contexte hospitalier)', false),
                'css_editeur'   => 'xxlarge',
                'liste'         => array(
                    1 => array('id' => 1, 'nom' => 'Base'),
                    2 => array('id' => 2, 'nom' => 'Intermédiaire'),
                    3 => array('id' => 3, 'nom' => 'Critiques'),
                )
            ),
            
            'remarque' => array(
                'table'      	=> '',
                'champ_id'      => 'remarque',
                'type'          => 'textarea',
                'format'        => 'text',
                'titre'         => txt('Commentaires', false),
                'css_editeur'   => 'xxlarge'
            ),
            
        );
		return parent::parseMetaData($return_array);
    }
	
	public static function getListe($filter = array(), $orderBy = 'id', $extra_data = null)
	{
		return parent::getListe($filter, $orderBy, $extra_data);
	}
    
    public static function getFormHTML($fiche_id, $liste_data = null, $forced_liste = null)
	{
        return parent::getUniqueFormHTML($fiche_id, $liste_data);
    }
    
    public function getEditorHTML($iteration = 0, $only_champs_denormalises = false)
	{
?>
        <style>
            #editables label {
                width: 275px !important;
            }
            .edit_popupContent section > div {
                width: 690px !important;
            }
            #editables input[type=checkbox] {
                position: relative;
                top: 1px;
            }
        </style>
<?
        parent::getEditorHTML($iteration);
    }
    
    public static function getFicheHTML($fiche, $liste = null, $nb_archives = 0, $can_edit = false, $projet_courant_id = null, $gabarit = null)
	{
?>
        <style>
            .blocsFiche section label {
                width: 260px !important;
            }
        </style>
<?
        if(isset($liste) && is_array($liste))
        {
            $liste_tmp = array_values($liste);
            if(isset($liste_tmp[0]))
            {
                $obj = $liste_tmp[0];
            }
            else
            {
                $currentClassName = get_called_class();
                $obj = $currentClassName::getOne($fiche->id);
            }
        }
        else
        {
            $currentClassName = get_called_class();
            $obj = $currentClassName::getOne($fiche->id);
        }
        
        parent::getUniqueFicheHTML($fiche, $obj, $nb_archives, $can_edit, $projet_courant_id);
        
        parent::getAutoFicheHtml($fiche->id, $obj);
    }
    
    public static function getDiffArchiveHTML($fiche_id, $liste, $last_liste, $token_archive, $last_token_archive)
	{
?>
        <style>
            .blocsFiche section label {
                width: 260px !important;
            }
        </style>
<?
        $liste = array_values($liste);
        $last_liste = array_values($last_liste);
        if(isset($liste[0]))
        {
            $obj = $liste[0];
        }
        
        if(isset($last_liste[0]))
        {
            $last_obj = $last_liste[0];
        }
        else
        {
            $last_obj = new self(0);
        }
        
?>
        <h2 class="blocsFicheTitre" style="height: 30px;">
            <?=txt(self::TITRE)?></h2>
<?
        
        parent::getAutoDiffArchiveHtml($fiche_id, $obj, $last_obj, $token_archive, $last_token_archive);
    }
    
    public function getFiltreHTML()
    {
?>
        <fieldset class="noMinHeight">
<?
            $this->getEditorHTML();
?>
        </fieldset>
<?
    }
    
    public static function validate($data, $fiche_id)
    {
        $valide = true;
        foreach($data as $colomnName => $value)
        {
            if($colomnName == 'raison')
            {
                if(dm_strlen($value) == 0)
                {
                    setErreur(txt('Le motif du changement est obligatoire.'));
                    $valide = false;
                }
            }
        }
        
        $valide_fiche = Fiches::validate($data, $fiche_id);
        
        return !$valide || !$valide_fiche ? false : true;
    }
}
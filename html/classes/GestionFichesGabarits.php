<?php

class GestionFichesGabarits extends Gestionnaire implements GestionnaireInterface
{
    use GestionnairePage;

    const SQL_TABLE_NAME = 'fiches';

    public int $id = 0;
    public $gabarit_id = null;
    public $code_fiche = '';
    public $utilite = '';
    public $code_gabarit = '';
    public $equipements_qtes_fiches = '';
    public $equipements_qtes_gabarits = '';

    public function setDataForId($id): void
    {
        $sql = self::getSql() .
            "where f2.id = ?";
        $row = db::instance()->getRow($sql, [$id]);

        if (isset($row)) {
            $this->populateThisWithThat($row);
        }
    }

    public static function getSql()
    {
        return "with gabarits as (
                        select fiche_id, f.code as code_gabarit, group_concat(distinct concat(ebmt.nom, ': ', coalesce(qte_requise, 0), ' | ', coalesce(qte_existante, 0), ' | ', coalesce(qte_fantome, 0), ' | ', coalesce(qte_conservee, 0)) order by ebmt.nom SEPARATOR '<br>') as equipements_qtes
                        from equipements_bio_medicaux ebm
                        join fiches f on f.id = ebm.fiche_id
                        join equipements_bio_medicaux_types ebmt on ebmt.id = ebm.equipement_bio_medicaux_type_id
                        where is_gabarit = 1
                        group by fiche_id
                     ),
                     fiches_avec_gabarit as (
                        select distinct fiche_id
                        from fiches_gabarits fg
                     ),
                    fiches_avec_gabarit_equipements as (
                        select ebm.fiche_id, group_concat(distinct concat(ebmt.nom, ': ', coalesce(qte_requise, 0), ' | ', coalesce(qte_existante, 0), ' | ', coalesce(qte_fantome, 0), ' | ', coalesce(qte_conservee, 0)) order by ebmt.nom SEPARATOR '<br>') as equipements_qtes
                        from equipements_bio_medicaux ebm
                        join fiches_avec_gabarit fag on fag.fiche_id = ebm.fiche_id
                        join equipements_bio_medicaux_types ebmt on ebmt.id = ebm.equipement_bio_medicaux_type_id
                        group by fiche_id
                    )
                
                select f2.id, fg.gabarit_id, f2.code as code_fiche, utilite, code_gabarit, fage.equipements_qtes as equipements_qtes_fiches, g.equipements_qtes as equipements_qtes_gabarits
                from fiches f2
                join generaux_locaux gl on gl.fiche_id = f2.id
                left join fiches_gabarits fg on f2.id = fg.fiche_id
                left join gabarits g on g.fiche_id = fg.gabarit_id
                left join fiches_avec_gabarit_equipements fage on fage.fiche_id = fg.fiche_id
                where true ";
    }

    public static function getFiltreData($filter): array
    {
        $where = ' and gl.projet_id = ?';
        $data = [getProjetId()];

        if (isset($filter['statut'])) {
            if ($filter['statut'] == 'identique') {
                $where .= " and gabarit_id is not null and fage.equipements_qtes = g.equipements_qtes";
            } else if ($filter['statut'] == 'respecte') {
                $where .= " and gabarit_id is not null and substring(fage.equipements_qtes from 1 for char_length(g.equipements_qtes)) = g.equipements_qtes and fage.equipements_qtes <> g.equipements_qtes";
            } else if ($filter['statut'] == 'non-respecte') {
                $where .= " and gabarit_id is not null and substring(fage.equipements_qtes from 1 for char_length(g.equipements_qtes)) <> g.equipements_qtes";
            }
        }

        if (isset($filter['appartenance'])) {
            if (count($filter['appartenance']) == 1) {
                if ($filter['appartenance'][0] == 'avec') {
                    $where .= " and gabarit_id is not null";
                }
                if ($filter['appartenance'][0] == 'sans') {
                    $where .= " and gabarit_id is null";
                }
            }
        } else {
            $where .= " and false";
        }

        return array($where, $data);
    }

    /**
     * @param $filter
     * @param $orderBy
     * @return GestionFichesGabarits[]
     */
    public static function getListe($filter, $orderBy = 'code_fiche'): array
    {
        list($where, $data) = self::getFiltreData($filter);

        $sql = self::getSql() .
                "$where
                order by $orderBy";
        $rows = db::instance()->getAll($sql, $data);

        $liste = [];
        foreach ($rows as $row) {
            $liste[(int)$row['id']] = new self((int)$row['id'], $row);
        }
        return $liste;
    }

    public function getEditorHTML()
    {

    }

    public static function delete($id): void
    {
    }

    public static function validate($data, $id): bool
    {
        $valide = true;
        return $valide;
    }

    public static function traitementSauvegardeSupplementaire(
        $id,
        $requestData,
        $is_ajout,
        $objAnciennesValeurs = null
    ): void {
    }

    public function traitementApresSubmit($requestData): void
    {
    }

    /**
     * @return GestionnaireInterface
     */
    public static function getClasseContexte(): string
    {
        /** @var GestionnaireInterface $currentClassName */
        $currentClassName = get_called_class();
        return $currentClassName;
    }

    public static function getTitre(): string
    {
        return txt('Gestion des gabarits');
    }

    public static function getListeForDatatable($modeSelection = false): array
    {
        $filtre = &$_SESSION['filtresGestionnairePage_' . self::getClasseContexte()];
        $liste = self::getListe($filtre);

        $listeForDatatable = [];
        foreach ($liste as $row) {
            $listeForDatatable[] = [
                $row->code_fiche . ' - ' . $row->utilite,
                $row->code_gabarit,
                '<span style="color: ' . $row->getStatutCouleur() . '; font-weight: strong;">' . $row->getStatutLabel() . '</span>',
                <<<HTML
                    <a class="affiche-diff" data-fiche_id="$row->id" data-gabarit_id="$row->gabarit_id">
                        <img src="css/images/icons/dark/magnifying_glass.png"></a>
HTML
            ];
        }
        return $listeForDatatable;
    }

    public static function getAutresOptions(): array
    {
        return [
            [
                'typeAffichage' => 'image',
                'classes' => 'exporterFiches',
                'url' => 'javascript: void(0);',
                'image' => 'css/images/icons/dark/DM_export_CSV.png',
            ]
        ];
    }

    public static function getOngletData(): array
    {
        return [];
    }

    public static function getListeFiltres()
    {
        return [
            'statut' => [
                'titre' => txt('Statut', false),
                'nomChamp' => 'statut',
                'type' => 'listeValeur',
                'non-multiple' => true,
                'listeValeur' => [
                    'non-respecte' => txt('Gabarit non-respecté'),
                    'respecte' => txt('Gabarit respecté'),
                    'identique' => txt('Identique au gabarit'),
                ]
            ],
            'appartenance' => [
                'nomChamp' => 'appartenance',
                'type' => 'checkboxes',
                'listeValeur' => [
                    'avec' => txt('Avec gabarit'),
                    'sans' => txt('Sans gabarit'),
                ]
            ],
            'boutons' => [
                'titre' => txt('Filtrer', false),
                'type' => 'bouton',
            ]
        ];
    }

    public static function getListeFiltresAvances(): array
    {
        return [
//            [
//                'titre' => txt('Filtre avancé locaux'),
//                'nomChamp' => 'fiche_ids',
//                'classe' => 'Fiches',
//                'url' => 'index.php?section=filtre_avance&module=filtre_avance&type=' . self::getClasseContexte() . '&smaller=1'
//            ]
        ];
    }

    public static function getListeColonnes($modeSelection = false): array
    {
        return [
            'code_fiche' => [
                'titre' => txt('Local', false),
                'isData' => true
            ],
            'code_gabarit' => [
                'titre' => txt('Gabarit', false),
                'isData' => true
            ],
            'statut' => [
                'titre' => txt('Statut', false),
                'isData' => true
            ],
            'boutons' => [
                'titre' => '',
                'isData' => false
            ],
        ];
    }

    public static function afficheCustomStyle()
    {
        ?>
        <style>
            .div_filtre {
                float: left;
                margin: 5px;
                line-height: 27px;
            }
            .div_filtre > div {
                vertical-align: bottom!important;
            }
            #datatable_filter {
                display: none;
            }
            form {
                margin-bottom: 25px;
            }
            #statut {
                position: relative;
                top: 6px;
            }
        </style>
        <?
    }

    public static function afficheJavascript()
    {
        ?>
        <script>
			$(document).ready(function () {
				setTimeout(() => {
					$('html').css({ overflow: 'auto' });
                }, 100);
				$(document).on('click', '#divFiltresCheckboxes input[type=checkbox]', function () {
                    $('#filtrer').click();
				});
				$(document).on('click', 'a.affiche-diff', function () {
					$('#dialog-diff').remove();
					$('body').append(`<div id="dialog-diff" title="Liste des " style="display: none; margin: 5px 15px;">
                        <p id="diff_bloc" style="margin-top: 10px;"></p>
                    </div>`);

					const dialog = $('#dialog-diff');
					$.get("index.php", { action: 'getDataFicheGabarit', fiche_id: $(this).data('fiche_id'), gabarit_id: $(this).data('gabarit_id') }, function (data) {

						const diffTableau = ``;

						dialog.find("#diff_bloc").html(this.getTableauReadonly());

						dialog.dialog({
							height:550,
							width:1300,
							modal: true,
							buttons: {
								"Fermer": function() {
									$(this).dialog( "close" );
								}
							}
						});
                    }, 'json');
				});
			});
        </script>
        <?php
    }

    private function getStatut()
    {
        if ($this->equipements_qtes_fiches === $this->equipements_qtes_gabarits && isset($this->gabarit_id)) {
            return 'identique';
        } else if (dm_substr($this->equipements_qtes_fiches, 0, dm_strlen($this->equipements_qtes_gabarits)) === $this->equipements_qtes_gabarits) {
            return 'respecte';
        } else if (isset($this->gabarit_id)) {
            return 'non-respecte';
        } else {
            return 'N/A';
        }
    }

    private function getStatutLabel()
    {
        if ($this->getStatut() === 'identique') {
            return txt('Identique au gabarit', false);
        } else if ($this->getStatut() === 'respecte') {
            return txt('Gabarit respecté', false);
        } else if ($this->getStatut() === 'non-respecte') {
            return txt('Gabarit non-respecté', false);
        } else {
            return 'Sans gabarit';
        }
    }

    private function getStatutCouleur()
    {
        if ($this->getStatut() === 'identique') {
            return 'green';
        } else if ($this->getStatut() === 'respecte') {
            return 'yellow';
        } else if ($this->getStatut() === 'non-respecte') {
            return 'red';
        } else {
            return 'black';
        }
    }

    public static function affichagePage(): void
    {
        self::afficheJavascript();
        self::afficheCustomStyle();
        self::afficheTitreEtOnglets();
        $filtre = &$_SESSION['filtresGestionnairePage_' . self::getClasseContexte()];
        if (!isset($filtre['appartenance'])) {
            $filtre['appartenance'] = ['avec', 'sans'];
        }
        self::traiteFiltre();
        self::afficheFiltre(250, true);
        self::afficheTableau();
    }
}
<?php

use JetBrains\PhpStorm\ArrayShape;

class GestionnaireFichesQuincailleries extends GestionnaireFiches implements GestionnaireFichesInterface
{
    protected const FICHE_SOUS_TYPE_ID = 2000013;
    public const LISTE_METAS_GENERAUX = ['description'];

    public static function getTitre(): string
    {
        return txt('Gestion des groupes de quincailleries');
    }

    public static function getListeFiltres(): array
    {
        return [

        ];
    }

    public static function getUrl(): string
    {
        return 'index.php?section=gestionnairePage&module=GestionnaireFichesQuincailleries';
    }

    public static function getFiltre(): array
    {
        return [
            'get_nb_lignesForm' => 'gestions_portes_groupes_quincailleries',
            'get_nb_archives' => 'GenerauxGroupesControles',
            'fiche_sous_type_id' => 2000013,
            'typeGestion' => 'quincailleries'
        ];
    }

    public static function getListeColonnes(): array
    {
        return [
            'code' => [
                'titre' => txt('Code du groupe de quicaillerie', false),
                'isData' => true
            ],
            'description' => [
                'titre' => txt('Description', false),
                'isData' => true
            ],
            'gestions_portes_groupes_quincailleries' => [
                'titre' => '<span class="titreAvecLien">' . txt('Composantes', false) . '&nbsp;&nbsp;</span>
                            <a class="fancy" href="index.php?section=fiche&module=synchronisation&via_formulaire=1&force_fiche_sous_type_id=' . self::FICHE_SOUS_TYPE_ID . '&force_formulaire_id=185">
                                <img src="css/images/icons/dark/incomming.png" /></a>',
                'isData' => false
            ],
            'boutons' => [
                'titre' => '<a href="index.php?section=admin&amp;module=edit&amp;id=0&amp;table=fiches&amp;type=client&amp;is_gabarit=0&amp;fiche_type_id=2000002&amp;fiche_sous_type_id=2000013&amp;from_gestion_portes=1" class="fancy" title="Ajouter un groupe de quincaillerie">
                                <img src="css/images/icons/dark/plus.png"></a>',
                'isData' => false
            ],
        ];
    }

    public static function getClasseGeneraux(): string
    {
        return 'GenerauxGroupesControles';
    }

    public static function getOngletData(): array
    {
        return GestionnaireFichesPortes::getOngletData();
    }

    public static function getAutresOptions()
    {
        return [
            [
                'typeAffichage' => 'image',
                'classes' => 'fancy',
                'url' => 'index.php?section=fiche&module=synchronisation&via_formulaire=1&force_fiche_sous_type_id=' . self::FICHE_SOUS_TYPE_ID . '&force_formulaire_id=110',
                'image' => 'css/images/icons/dark/incomming.png',
            ],
            [
                'typeAffichage' => 'image',
                'classes' => 'exporterFiches',
                'url' => 'javascript: void(0);',
                'image' => 'css/images/icons/dark/DM_export_CSV.png',
            ]
        ];
    }

    public static function getNomFichierExport(): string
    {
        return 'export_groupes_quincailleries.xlsx';
    }

    public static function getOptionsExport(): array
    {
        $listeFiches = Fiches::getListe(['fiche_sous_type_id' => self::FICHE_SOUS_TYPE_ID]);
        return [
            'listeFiches' => array_keys($listeFiches),
            'formulaire_id' => '185',
            'gabarit_export_id' => '',
            'fiche_sous_type_id' => self::FICHE_SOUS_TYPE_ID,
            'inclusions' => ['inclusion_totale'],
            'format' => 'xlsx',
            'generate' => '1'
        ];
    }
}
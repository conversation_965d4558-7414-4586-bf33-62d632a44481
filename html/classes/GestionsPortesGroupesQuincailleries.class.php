<?php

class GestionsPortesGroupesQuincailleries extends FicheFormWrapper {
	private static $CLASS_NAME='GestionsPortesGroupesQuincailleries';
	const SQL_TABLE_NAME='gestions_portes_groupes_quincailleries';
	const TITRE='Groupes de quincaillerie';
	public $id;
	public $fiche_id;
	public $gestion_porte_quincaillerie_type_id;
	public $gestion_porte_quincaillerie_type;
	public $qte_requise;
	public $quantite;
	public $commentaires;
    
    public static function getChampsSaisieMetaData()
    {
        return array(
            
            'gestion_porte_quincaillerie_type' => array(
                'classe'      	=> 'GestionsPortesQuincailleriesTypes',
                'table'      	=> 'gestions_portes_quincailleries_types',
                'champ_id'      => 'gestion_porte_quincaillerie_type_id',
                'type'          => 'select',
                'format'        => 'int',
                'titre'         => txt('Type de quincaillerie', false),
                'css_editeur'   => 'large',
                'obligatoire'	=> 1,
            ),
            
            'qte_requise' => array(
				'classe'      	=> '',
				'table'      	=> '',
				'champ_id'      => 'qte_requise',
				'type'          => 'input',
				'format'        => 'int',
				'titre'         => txt('Quantité', false),
				'css_editeur'   => 'tiny'
			),
            
            'commentaires' => array(
				'table'      	=> '',
				'champ_id'      => 'commentaires',
				'type'          => 'input',
                'format'        => 'text',
                'titre'         => txt('Commentaires', false),
                'css_editeur'   => 'xxlarge'
            ),
            
        );
    }

	public static function getListe($filter = array(), $orderBy = 'ordre', $extra_data = array())
	{
        $extra_data = array(
            'gestion_porte_quincaillerie_type' => array('table' => 'gestions_portes_quincailleries_types'),
        );
        return parent::getListe($filter, $orderBy, $extra_data);
    }
}

<?php

/**
 * Class Inventaires
 */
class Inventaires extends DbRow {
    public $id;
    public $nom;
    public $champs_releve_mobile;
    public $champs_locaux_actifs;

    /**
     * @var  InventairesChamps[]
     */
    public $champs;

    /**
     * @var  InventairesLocauxChamps[]
     */
    public $locauxChamps;

    /**
     * Show the accounts set to a specific project
     *
     * @param int $id - id
     * @throws Exception
     */
    protected function setDataForId($id)
    {
        global $db;

        $sql = "select i.*
                from inventaires i
                where i.id = ?";
        $row = $db->getRow($sql, array($id));

        $this->populateThisWithThat($row);

        $this->champs = InventairesChamps::getListe(['inventaire_id' => $this->id]);
        $this->locauxChamps = InventairesLocauxChamps::getListe(['inventaire_id' => $this->id]);
    }

    private static function getFiltreData($filter)
    {
        global $db;

        $where = ' and projet_id = ?';
        $data = array(getProjetId());

        return array($where, $data);
    }

    /**
     * getCount
     *
     * @param array $filter - Filters
     * @param string $orderBy - Order by
     * @return mixed
     * @throws Exception
     */
    public static function getCount($filter = array(), $orderBy = 'nom')
    {
        global $db;

        list($where, $data) = self::getFiltreData($filter);

        $sql = "select count(*)
                from inventaires i
                where true $where";
//	    printDebug(dm_nl2br(db::interpolateQuery($sql, $data)));
        $nb = $db->getOne($sql, $data);

        return $nb;
    }


    /**
     * Listing function of Acquisitions
     *
     * @param array $filter - Filters
     * @param string $orderBy - Order
     * @return array
     * @throws Exception
     */
    public static function getListe($filter = [], $orderBy = 'id')
    {
        global $db;
        list($where, $data) = self::getFiltreData($filter);

        $sql = "select i.*, id
                from inventaires i
                where true $where
                order by $orderBy";
        //printDebug(dm_nl2br(db::interpolateQuery($sql, $data)));
        $rows = $db->getAssoc($sql, $data);

        $liste = array();
        foreach ($rows as $i => $row) {
            $liste[] = new self(intval($row['id']), $row);
        }
        return $liste;
    }

    public static function getOptions($filter = array(), $selected = 0, $includeEmpty = false)
    {
        if ($includeEmpty) {
            ?>
            <option value=""></option>
            <?php
        }
        $rows = self::getListe($filter);
        foreach ($rows as $i => $row) {
            $sel = $row->id == $selected ? ' selected="selected"' : '';
            ?>
            <option value="<?= $row->id ?>"<?= $sel ?>><?= $row->numero . " - " . $row->description ?></option>
            <?
        }
    }

    public function getEditorHTML($iteration = 0)
    {
        ?>
        <style>
            td {
                padding: 3px !important;
            }

        </style>

        <section>
            <label for="nom_formulaire"><?= txt('Paramètres') ?></label>
            <div>
                <table style="margin-bottom: 10px; width: 500px!important;" class="formFieldsTable" id="table_colonnes">
                    <tbody>
                    <?
                    if (!isset($this->champs) || count($this->champs) == 0) {
                        $this->champs[] = new InventairesChamps(0, ['nom' => '', 'ordre' => 1, 'element_id' => 0]);
                    }
                    ?>
                    <tr>
                        <td style="font-weight: bold; width: 100px; text-align: center!important;">
                            <?= txt('') ?>
                        </td>
                        <td style="font-weight: bold; width: 215px; text-align: center!important;">
                            <?= txt('Titre du paramètre') ?>
                        </td>
                        <td style="font-weight: bold; width: 215px; text-align: center!important;">
                            <?= txt('Inventaires importés') ?>
                        </td>
                        <td style="font-weight: bold; width: 215px; text-align: center!important;">
                            <?= txt('Relevés internes') ?>
                        </td>
                    </tr>

                    <?
                    $champsInventaireImporte = $this->getChampsInventaireImporte();
                    $champsReleveMobile = $this->getChampsReleveMobile();
                    foreach (InventairesLignes::getChamps() as $key => $champ) {
                        ?>
                        <tr class="top_block">
                            <td style="width: 50px;min-width: 50px!important;">
                            </td>
                            <td style=" text-align: left!important;">
                                <?= $champ['titre'] ?>
                            </td>
                            <td style=" text-align: center!important;">
                                <input type="checkbox" name="inventaire_importe[]" value="<?= $key ?>" <?= (in_array($key, $champsInventaireImporte) ? ' checked="checked"' : '') ?> />
                            </td>
                            <td style=" text-align: center!important;">
                                <input type="checkbox" name="releve_mobile[]" value="<?= $key ?>" <?= (in_array($key, $champsReleveMobile) ? ' checked="checked"' : '') ?> />
                            </td>
                        </tr>
                        <?
                    }
                    foreach ($this->champs as $champ) {
                        ?>
                        <tr class="top_block editable">
                            <td style="width: 50px;min-width: 50px!important;">
                                <span class="delSortBtns">
                                    <input type="hidden" name="nothing[]" value=""/>
                                    <a href="javascript: void(0);" class="sort">
                                        <img src="css/images/icons/dark/triangle_up_down.png"/></a>

                                    <a href="javascript: void(0);" class="delete">
                                        <img src="css/images/icons/dark/trashcan.png"/></a>

                                    <input type="hidden" name="element_id[]" value="<?= $champ->id ?>" class="element_id"/>
                                </span>
                            </td>
                            <td style=" text-align: left!important;">
                                <input type="text" name="nom_champ[]" value="<?= ($champ->nom) ?>" style="width: 250px;min-width: 250px!important;"/>
                            </td>
                            <td style=" text-align: center!important; width: 100px;">
                                <input type="checkbox" name="inventaire_importe[]" value="<?= $champ->id ?>" <?= (in_array($champ->id, $champsInventaireImporte) ? ' checked="checked"' : '') ?> />
                            </td>
                            <td style=" text-align: center!important; width: 100px;">
                                <input type="checkbox" name="releve_mobile[]" value="<?= $champ->id ?>" <?= (in_array($champ->id, $champsReleveMobile) ? ' checked="checked"' : '') ?> />
                            </td>
                        </tr>
                        <?
                    }
                    ?>
                    </tbody>
                    <tfoot>
                    <tr class="top_block">
                        <td>
                            <a href="javascript: void(0);" class="add_line" style="">
                                <img src="css/images/icons/dark/plus.png"/></a>
                        </td>
                        <td>
                            &nbsp;
                        </td>
                        <td>
                            &nbsp;
                        </td>
                    </tr>
                    </tfoot>
                </table>
            </div>
        </section>
        <?
    }

    public static function delete($id)
    {
        global $db;
        $db->autocommit(false);
        $db->query("DELETE FROM `inventaires` WHERE id = ?", array($id));
        $db->commit();
    }

    public static function validate($data, $id)
    {
        return true;
    }

    public static function traitementSauvegardeSupplementaire($id, $requestData, $is_ajout)
    {
        global $db;
        $sql = "select id FROM inventaires_champs WHERE inventaire_id = ?";
        $ids_to_delete_tmp = $db->getAll($sql, array($id));

        $ids_to_delete = array();
        foreach ($ids_to_delete_tmp as $row_tmp) {
            $ids_to_delete[intval($row_tmp['id'])] = intval($row_tmp['id']);
        }

        if (isset($requestData['element_id']) && count($requestData['element_id']) > 0) {
            foreach ($requestData['element_id'] as $ordre => $element_id) {
                if (dm_strlen($requestData['nom_champ'][$ordre]) > 0) {
                    if ($element_id > 0) {
                        $sql = "UPDATE inventaires_champs SET nom = ?, ordre = ?
                                        WHERE id = ?";
                        $db->query($sql, array($requestData['nom_champ'][$ordre], $ordre, $element_id));
                        unset($ids_to_delete[$element_id]);
                    } else {
                        $sql = "INSERT INTO inventaires_champs (nom, ordre, inventaire_id) 
                                        VALUES (?, ?, ?)";
                        $db->query($sql, array($requestData['nom_champ'][$ordre], $ordre, $id));
                    }
                }
            }
        }

        if (isset($_POST['releve_mobile'])) {
            $sql = "update inventaires set champs_releve_mobile = ?, champs_inventaire_importe = ? where id = ?";
            db::instance()->query($sql, [json_encode($_POST['releve_mobile']), json_encode($_POST['inventaire_importe']), $id]);
        }

        if (count($ids_to_delete) > 0) {
            $sql = "DELETE FROM inventaires_champs WHERE id in (" . dm_implode(', ', $ids_to_delete) . ")";
            $db->query($sql);
        }
    }

    public static function getInventaireCourant(): Inventaires
    {
        $inventaires = self::getListe();
        $inventaire_id = (isset($inventaires[0]) ? $inventaires[0]->id : 0);
        if ($inventaire_id === 0) {
            $projetCourant = getProjetInfo();
            $sql = "insert into inventaires (projet_id, nom) values (?, ?)";
            db::instance()->query($sql, [getProjetId(), Config::instance()->codeClient . ' - ' . $projetCourant->code]);
            $inventaire_id = db::instance()->lastInsertId();
        }
        return new Inventaires($inventaire_id);
    }

    public function getChampsActifs($is_editable): array
    {
        $champsActifs = [];
        if (in_array($is_editable, [1, ''])) {
            $champsActifs = array_merge($champsActifs, isset($this->champs_releve_mobile) && strlen($this->champs_releve_mobile) > 0 ? json_decode(
                $this->getRaw('champs_releve_mobile'),
                true
            ) : []);
        }
        if (in_array($is_editable, [0, ''])) {
            $champsActifs = array_merge($champsActifs, isset($this->champs_inventaire_importe) && strlen($this->champs_inventaire_importe) > 0 ? json_decode(
                $this->getRaw('champs_inventaire_importe'),
                true
            ) : []);
        }
        return $champsActifs;
    }

    public function getChampsInventaireImporte(): array
    {
        return isset($this->champs_inventaire_importe) && strlen($this->champs_inventaire_importe) > 0 ? json_decode(
            $this->getRaw('champs_inventaire_importe'),
            true
        ) : [];
    }

    public function getChampsReleveMobile(): array
    {
        return isset($this->champs_releve_mobile) && strlen($this->champs_releve_mobile) > 0 ? json_decode(
            $this->getRaw('champs_releve_mobile'),
            true
        ) : [];
    }

    public function getChampsLocauxActifs(): array
    {
        return isset($this->champs_locaux_actifs) && strlen($this->champs_locaux_actifs) > 0 ? json_decode(
            $this->getRaw('champs_locaux_actifs'),
            true
        ) : [];
    }

    public static function isFiltreActif($filtre, $is_editable): bool
    {
        $inventaire = Inventaires::getInventaireCourant();
        $champsActifs = $inventaire->getChampsActifs($is_editable);
        foreach (InventairesLignes::getChamps() as $key => $champ) {
            if (in_array($key, $champsActifs) && isset($filtre[$key]) && count($filtre[$key]) > 0) {
                return true;
            }
        }
        foreach ($inventaire->champs as $champ) {
            if (in_array($champ->id, $champsActifs) && isset($filtre['filtresCustom_' . $champ->id]) && count($filtre['filtresCustom_' . $champ->id]) > 0) {
                return true;
            }
        }
        return false;
    }
}

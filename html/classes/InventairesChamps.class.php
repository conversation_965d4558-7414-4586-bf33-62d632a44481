<?php

/**
 * Class Inventaires
 */
class InventairesChamps extends DbRow {
	public $id;
	public $nom;

	/**
	 * InventairesChamps constructor.
	 *
	 * @param  int $id - ??
	 * @param  null $row - ??
	 * @throws Exception
	 */



	/**
	 * Show the accounts set to a specific project
	 *
	 * @param  int $id - id
	 * @throws Exception
	 */
	protected function setDataForId($id)
	{
		global $db;

		$sql = "select tb.*
                from inventaires_champs tb
                where tb.id = ?";
		$row = $db->getRow($sql, array($id));

		$this->populateThisWithThat($row);


	}

	private static function getFiltreData($filter)
	{

		global $db;

		$where = "";
		$data = [];

		if (isset($filter['inventaire_id']) && intval($filter['inventaire_id']) > 0) {
			$where .= " and inventaire_id = ?";
			$data[] = intval($filter['inventaire_id']);
		}

		return array($where, $data);
	}

	/**
	 * getCount
	 *
	 * @param  array $filter - Filters
	 * @param  string $orderBy - Order by
	 * @return mixed
	 * @throws Exception
	 */
	public static function getCount($filter = array(), $orderBy = 'nom')
	{
		global $db;

		list($where, $data) = self::getFiltreData($filter);

		$sql = "select count(*)
                from inventaires_champs tb
                where true $where";
//	    printDebug(dm_nl2br(db::interpolateQuery($sql, $data)));
		$nb = $db->getOne($sql, $data);

		return $nb;
	}


	/**
	 * Listing function of Acquisitions
	 *
	 * @param  array $filter - Filters
	 * @param  string $orderBy - Order
	 * @return InventairesChamps[]
	 * @throws Exception
	 */
	public static function getListe($filter = [], $orderBy = 'ordre')
	{
		global $db;
		list($where, $data) = self::getFiltreData($filter);

		$sql = "select tb.*, id
                from inventaires_champs tb
                where true $where
                order by $orderBy";
		//printDebug(dm_nl2br(db::interpolateQuery($sql, $data)));
		$rows = $db->getAssoc($sql, $data);

		$liste = array();
		foreach ($rows as $i => $row) {
			$liste[intval($row['id'])] = new self(intval($row['id']), $row);
		}
		return $liste;
	}

	public static function getOptions($filter = array(), $selected = 0, $includeEmpty = false)
	{
		if ($includeEmpty) {
			?>
			<option value=""> </option>
			<?php
		}
		$rows = self::getListe($filter);
		foreach ($rows as $i => $row) {
			$sel = $row->id == $selected ? ' selected="selected"' : '';
			?>
			<option value="<?= $row->id ?>"<?= $sel ?>><?= $row->numero . " - " . $row->description ?></option>
			<?
		}
	}

	public static function delete($id)
	{
		global $db;
		$db->autocommit(false);
		$db->query("DELETE FROM `inventaires_champs` WHERE id = ?", array($id));
		$db->commit();
	}
}

<?php

/**
 * Class Inventaires
 */
class InventairesLignes extends DbRow {
    public $id;
    public $inventaire_id;
    public $numero_inventaire;
    public $code;
    public $description;
    public $statut_externe;
    public $ordre;
    public $is_actif;
    public $est_associe;
    public $local;
    public $equipement;
    public $equipement_externe;
    public $has_historique_non_lu;
    public $is_obsolete;
    public $barcode_nouveau;
    public $fiche_local_id;
    public $inventaire_local_id;
    public $code_norme_id;
    public $equipement_norme;
    public $marque_id;
    public $modele_id;
    public $numero_serie;
    public $date_fabrication;
    public $hauteur;
    public $largeur;
    public $profondeur;
    public $type_installation_id;
    public $condition_vetuste_id;
    public $remarque;
    public $is_editable;
    public $numero_local;
    public $cedule_demenagement_id;
    public $condition_vetuste;
    public $commentaire;
    public $code_fiche_gbm;
    public $fiche_id;
    public $fiche_local_destination_id;
    public $equipement_bio_medicaux_type_id;
    public $inventaire_ligne_id;
    public $qte_existante;
    public $qte_conservee;
    public $qte_associee;

    /**
     * InventairesLignes constructor.
     *
     * @param int $id - ??
     * @param null $row - ??
     * @throws Exception
     */
    public function __construct($id, $row = null)
    {
        if ($id == 0) {
            $sql = "SELECT `COLUMN_NAME`, `DATA_TYPE`
                    FROM `INFORMATION_SCHEMA`.`COLUMNS` 
                    WHERE `TABLE_SCHEMA`='" . Config::instance()->dbName . "' 
                        AND `TABLE_NAME`='inventaires_lignes'
                        order by DATA_TYPE;";
            $cols = db::instance()->getAll($sql, []);

            foreach ($cols as $data) {
                $col_name = $data['COLUMN_NAME'];
                $this->$col_name = ($data['DATA_TYPE'] == 'int' ? 0 : '');
            }
        } else {
            if (isset($row) && (is_object($row) || is_array($row))) {
                $this->populateThisWithThat($row);
            } else {
                $this->setDataForId($id);
            }
        }
    }


    /**
     * Show the accounts set to a specific project
     *
     * @param int $id - id
     * @throws Exception
     */
    protected function setDataForId($id)
    {
        global $db;

        $sql = "select tb.*, f2.code as code_fiche, il.numero_local, ebmt.nom as equipement_norme, ma.nom as marque, mo.numero_modele as modele, it.nom as type_installation, cvt.nom as condition_vetuste, fiche_id
                from inventaires_lignes tb
                left join inventaires_fiches_equipements ife on ife.numero_inventaire = tb.numero_inventaire and ife.inventaire_id = tb.inventaire_id
				left join fiches f1 on ife.fiche_id = f1.id
                left join inventaires_locaux il on il.id = tb.inventaire_local_id
                left join fiches f2 on f2.id = tb.fiche_local_id
                left join equipements_bio_medicaux_types ebmt on ebmt.id = tb.code_norme_id
                left join modeles mo on mo.id = tb.modele_id
                left join marques ma on ma.id = mo.marque_id
                left join installations_types it on it.id = tb.type_installation_id
                left join conditions_vetustes_types cvt on cvt.id = tb.condition_vetuste_id
                where tb.id = ?";
        $row = $db->getRow($sql, array($id));

        $this->populateThisWithThat($row);
    }

    private static function getFiltreData($filter)
    {
        $where = "";
        $data = [];

        if (isset($filter['have_gbm']) && intval($filter['have_gbm']) == 1) {
            $where .= " and exists (select 1 from equipements_bio_medicaux ebm where ebm.equipement_bio_medicaux_type_id = ebmtSource.id and ebm.projet_id = ? limit 1)";
            $data[] = getProjetId();
        }

        if (isset($filter['have_historique'])) {
            $where .= " and exists (select 1 from logs_imports_inventaires_lignes liil join logs_imports_inventaires lii on lii.id = liil.log_import_inventaire_id where liil.numero_inventaire = tb.numero_inventaire and lii.inventaire_id = tb.inventaire_id)";
        }

        if (isset($filter['is_actif']) && dm_strlen($filter['is_actif']) > 0) {
            $where .= " and is_actif = ?";
            $data[] = $filter['is_actif'] == 'visible' ? 1 : 0;
        }

        if (isset($filter['est_associe']) && dm_strlen($filter['est_associe']) > 0) {
            if ($filter['est_associe'] == 'associe') {
                $where .= " and ife.numero_inventaire is not null";
            } else {
                $where .= " and ife.numero_inventaire is null";
            }
        }

        if (isset($filter['is_editable']) && dm_strlen($filter['is_editable']) > 0) {
            $where .= " and is_editable = ?";
            $data[] = (int)$filter['is_editable'];
        }

        if (isset($filter['text_search']) && dm_strlen($filter['text_search']) > 0) {
            $where .= " and (exists (select 1 from inventaires_valeurs where inventaire_ligne_id = tb.id and valeur like ?) or tb.description like ? or tb.code like ?)";
            $data[] = "%" . $filter['text_search'] . "%";
            $data[] = "%" . $filter['text_search'] . "%";
            $data[] = "%" . $filter['text_search'] . "%";
        }

        if (isset($filter['inventaire_ligne_id']) && intval($filter['inventaire_ligne_id']) > 0) {
            $where .= " and tb.id = ?";
            $data[] = intval($filter['inventaire_ligne_id']);
        }

        if (isset($filter['inventaire_id'])) {
            $where .= " and tb.inventaire_id = ?";
            $data[] = intval($filter['inventaire_id']);
        }

        if (isset($filter['numero_inventaire'])) {
            if (is_array($filter['numero_inventaire'])) {
                if (count($filter['numero_inventaire']) > 0) {
                    $where .= " and tb.numero_inventaire in (" . db::placeHolders($filter['numero_inventaire']) . ")";
                    $data = array_merge($data, $filter['numero_inventaire']);
                }
            } else if (dm_strlen($filter['numero_inventaire']) > 0) {
                $where .= " and tb.numero_inventaire = ?";
                $data[] = $filter['numero_inventaire'];
            }
        }

        if (isset($filter['statut_externe']) && dm_strlen($filter['statut_externe']) > 0) {
            $where .= " and statut_externe = ?";
            $data[] = $filter['statut_externe'];
        }

        if (isset($filter['equipement_bio_medicaux_type_id']) && intval(
                $filter['equipement_bio_medicaux_type_id']
            ) > 0 && isset($filter['fiche_id']) && intval($filter['fiche_id']) > 0) {
            $where .= " and exists (select numero_inventaire from inventaires_fiches_equipements ife where equipement_bio_medicaux_type_id = ? and fiche_id = ? and ife.numero_inventaire = tb.numero_inventaire)";
            $data[] = $filter['equipement_bio_medicaux_type_id'];
            $data[] = $filter['fiche_id'];
        }

        if (isset($filter['fiche_id'])) {
            if ($_GET['module'] == 'pagePropositionsAssociations') {
                if (is_array($filter['fiche_id'])) {
                    if (count($filter['fiche_id']) > 0) {
                        $where .= " and f.id in (" . db::placeHolders($filter['fiche_id']) . ")";
                        $data = array_merge($data, $filter['fiche_id']);
                    }
                } else if (intval($filter['fiche_id']) > 0) {
                    $where .= " and f.id = ?";
                    $data[] = $filter['fiche_id'];
                }
            } else {
                if (is_array($filter['fiche_id'])) {
                    if (count($filter['fiche_id']) > 0) {
                        $where .= " and exists (select numero_inventaire from inventaires_fiches_equipements ife where fiche_id in (" . db::placeHolders($filter['fiche_id']) . ") and ife.numero_inventaire = tb.numero_inventaire)";
                        $data = array_merge($data, $filter['fiche_id']);
                    }
                } else if (intval($filter['fiche_id']) > 0) {
                    $where .= " and exists (select numero_inventaire from inventaires_fiches_equipements ife where fiche_id = ? and ife.numero_inventaire = tb.numero_inventaire)";
                    $data[] = $filter['fiche_id'];
                }
            }
        }

        if (isset($filter['fiche_local_id'])) {
            if (is_array($filter['fiche_local_id'])) {
                if (count($filter['fiche_local_id']) > 0) {
                    $where .= " and tb.fiche_local_id in (" . db::placeHolders($filter['fiche_local_id']) . ")";
                    $data = array_merge($data, $filter['fiche_local_id']);
                }
            } else if (intval($filter['fiche_local_id']) > 0) {
                $where .= " and tb.fiche_local_id = ?";
                $data[] = $filter['fiche_local_id'];
            }
        }

        if (isset($filter['inventaire_local_id'])) {
            if (is_array($filter['inventaire_local_id'])) {
                if (count($filter['inventaire_local_id']) > 0) {
                    $where .= " and tb.inventaire_local_id in (" . db::placeHolders($filter['inventaire_local_id']) . ")";
                    $data = array_merge($data, $filter['inventaire_local_id']);
                }
            } else if (intval($filter['inventaire_local_id']) > 0) {
                $where .= " and tb.inventaire_local_id = ?";
                $data[] = $filter['inventaire_local_id'];
            }
        }

        if (isset($filter['equipement_externe'])) {
            if (is_array($filter['equipement_externe'])) {
                if (count($filter['equipement_externe']) > 0) {
                    $where .= " and (tb.code in (" . db::placeHolders($filter['equipement_externe']) . ") or ebmtSource.nom in (" . db::placeHolders($filter['equipement_externe']) . "))";
                    $data = array_merge($data, $filter['equipement_externe'], $filter['equipement_externe']);
                }
            } else if (dm_strlen($filter['equipement_externe']) > 0) {
                $where .= " and ? in (tb.code, ebmtSource.nom)";
                $data[] = $filter['equipement_externe'];
            }
        }

        if (isset($filter['non_cedules']) && dm_strlen($filter['non_cedules']) > 0) {
            $where .= " and not exists (select inventaire_ligne_id from cedules_demenagements_inventaires_lignes where inventaire_ligne_id = tb.id)";
        }

        if (isset($filter['cedule_demenagement_id'])) {
            if (is_array($filter['cedule_demenagement_id'])) {
                if (count($filter['cedule_demenagement_id']) > 0) {
                    $where .= " and exists (select inventaire_ligne_id from cedules_demenagements_inventaires_lignes where cedule_demenagement_id in (" . db::placeHolders($filter['cedule_demenagement_id']) . ") and inventaire_ligne_id = tb.id)";
                    $data = array_merge($data, $filter['cedule_demenagement_id']);
                } else {
                    $where .= " and false";
                }
            } else if (intval($filter['cedule_demenagement_id']) > 0) {
                $where .= " and exists (select inventaire_ligne_id from cedules_demenagements_inventaires_lignes where cedule_demenagement_id = ? and inventaire_ligne_id = tb.id)";
                $data[] = $filter['cedule_demenagement_id'];
            }
        }

        if (isset($filter['ids_to_exclude']) && count($filter['ids_to_exclude']) > 0) {
            $where .= " and tb.id not in (" . db::placeHolders($filter['ids_to_exclude']) . ")";
            $data = array_merge($data, $filter['ids_to_exclude']);
        }

        if (isset($filter['ids'])) {
            if (count($filter['ids']) > 0) {
                $where .= " and tb.id in (" . db::placeHolders($filter['ids']) . ")";
                $data = array_merge($data, $filter['ids']);
            } else {
                $where .= " and false";
            }
        }

        if (isset($filter['barcode_nouveau']) && count($filter['barcode_nouveau']) > 0) {
            $where .= " and tb.barcode_nouveau in (" . db::placeHolders($filter['barcode_nouveau']) . ")";
            $data = array_merge($data, $filter['barcode_nouveau']);
        }

        if (isset($filter['code_norme_id'])) {
            if (is_array($filter['code_norme_id'])) {
                if (count($filter['code_norme_id']) > 0) {
                    $where .= " and tb.code_norme_id in (" . db::placeHolders($filter['code_norme_id']) . ")";
                    $data = array_merge($data, $filter['code_norme_id']);
                }
            } else if (intval($filter['code_norme_id']) > 0) {
                $where .= " and ebmtSource.id = ?";
                $data[] = $filter['code_norme_id'];
            }
        }

        if (isset($filter['marque_id']) && count($filter['marque_id']) > 0) {
            $where .= " and tb.modele_id in (select id from modeles where marque_id in (" . db::placeHolders($filter['marque_id']) . "))";
            $data = array_merge($data, $filter['marque_id']);
        }

        if (isset($filter['modele_id']) && count($filter['modele_id']) > 0) {
            $where .= " and tb.modele_id in (" . db::placeHolders($filter['modele_id']) . ")";
            $data = array_merge($data, $filter['modele_id']);
        }

        if (isset($filter['numero_serie']) && count($filter['numero_serie']) > 0) {
            $where .= " and tb.numero_serie in (" . db::placeHolders($filter['numero_serie']) . ")";
            $data = array_merge($data, $filter['numero_serie']);
        }

        if (isset($filter['type_installation_id']) && count($filter['type_installation_id']) > 0) {
            $where .= " and tb.type_installation_id in (" . db::placeHolders($filter['type_installation_id']) . ")";
            $data = array_merge($data, $filter['type_installation_id']);
        }

        if (isset($filter['condition_vetuste_id']) && count($filter['condition_vetuste_id']) > 0) {
            $where .= " and tb.condition_vetuste_id in (" . db::placeHolders($filter['condition_vetuste_id']) . ")";
            $data = array_merge($data, $filter['condition_vetuste_id']);
        }

        $inventaire = Inventaires::getInventaireCourant();
        foreach ($inventaire->champs as $champ) {
            if (isset($filter['filtresCustom_' . $champ->id]) && is_array($filter['filtresCustom_' . $champ->id])) {
                if (count($filter['filtresCustom_' . $champ->id]) > 0) {
                    $where .= " and exists (select 1 from inventaires_valeurs where inventaire_ligne_id = tb.id and valeur in (" . db::placeHolders($filter['filtresCustom_' . $champ->id]) . ") and inventaire_champ_id = ?)";
                    $data = array_merge($data, $filter['filtresCustom_' . $champ->id], [$champ->id]);
                }
            }
        }
        return array($where, $data);
    }

    /**
     * getCount
     *
     * @param array $filter - Filters
     * @param string $orderBy - Order by
     * @return mixed
     * @throws Exception
     */
    public static function getCount($filter = array(), $orderBy = 'id')
    {
        global $db;

        list($where, $data) = self::getFiltreData($filter);

        $sql = "select count(*)
                from inventaires_lignes tb
                left join equipements_bio_medicaux_types ebmtSourceForCode on is_editable = 0 and ebmtSourceForCode.nom = tb.code
                left join equipements_bio_medicaux_types ebmtSource on ebmtSource.id = case when is_editable = 1 then tb.code_norme_id else ebmtSourceForCode.id end
                left join inventaires_fiches_equipements ife on ife.numero_inventaire = tb.numero_inventaire and ife.inventaire_id = tb.inventaire_id
                where true $where";
//	    printDebug(dm_nl2br(db::interpolateQuery($sql, $data)));
        $nb = $db->getOne($sql, $data);

        return $nb;
    }


    /**
     * Listing function of Acquisitions
     *
     * @param array $filter - Filters
     * @param string $orderBy - Order
     * @param string $sql_limit - Limit Offset
     * @return InventairesLignes[]
     * @throws Exception
     */
    public static function getListe(array $filter = [], string $orderBy = 'tb.id', string $sql_limit = ''): array
    {
        global $db;
        list($where, $data) = self::getFiltreData($filter);

        $sql = "select tb.*, case when f.code_alternatif_2 is not null then coalesce(concat(f.code, concat(' (', f.code_alternatif_2, ')'), ' - ', gl.utilite)) else coalesce(concat(f.code, ' - ', gl.utilite)) end as local, coalesce(concat(ebmt.nom, ' - ', ebmt.description)) as equipement,
       				case when f.id is not null then 1 else 0 end as est_associe, equipement_bio_medicaux_type_id, ife.fiche_id,
       				case when exists (select 1 from logs_imports_inventaires_lignes liil join logs_imports_inventaires lii on liil.log_import_inventaire_id = lii.id where liil.numero_inventaire = tb.numero_inventaire and lii.inventaire_id = tb.inventaire_id limit 1) then 1 else 0 end as has_historique_non_lu,
                    case when ife.numero_inventaire is not null and not exists (SELECT ebm.equipement_bio_medicaux_type_id from equipements_bio_medicaux ebm where ebm.equipement_bio_medicaux_type_id = ife.equipement_bio_medicaux_type_id and ebm.fiche_id = ife.fiche_id) then 1 else 0 end as is_obsolete, 
                    coalesce(concat(ebmtSource.nom, ' - ', ebmtSource.description)) as equipement_norme, il.numero_local,
                    case when is_editable = 1 then ebmtSource.nom else tb.code end as code,
                    case when is_editable = 1 then coalesce(concat(ebmtSource.nom, ' - ', coalesce(ebmtSource.description, ''))) else coalesce(concat(tb.code, ' - ', coalesce(tb.description, ''))) end as equipement_externe,
                    case when is_editable = 1 then ebmtSource.description else tb.description end as description,
                    cdil.cedule_demenagement_id, cvt.nom as condition_vetuste, ife.equipement_bio_medicaux_type_id
                from inventaires_lignes tb
                left join equipements_bio_medicaux_types ebmtSourceForCode on is_editable = 0 and ebmtSourceForCode.nom = tb.code
                left join equipements_bio_medicaux_types ebmtSource on ebmtSource.id = case when is_editable = 1 then tb.code_norme_id else ebmtSourceForCode.id end
                left join inventaires_fiches_equipements ife on ife.numero_inventaire = tb.numero_inventaire and ife.inventaire_id = tb.inventaire_id
				left join fiches f on ife.fiche_id = f.id
                left join generaux_locaux gl on gl.fiche_id = f.id
				left join equipements_bio_medicaux_types ebmt on ife.equipement_bio_medicaux_type_id = ebmt.id
				left join inventaires_locaux il on il.id = tb.inventaire_local_id
                left join cedules_demenagements_inventaires_lignes cdil on tb.id = cdil.inventaire_ligne_id
                left join conditions_vetustes_types cvt on cvt.id = tb.condition_vetuste_id
                where true $where
                order by is_obsolete desc, $orderBy
                $sql_limit";
//        printDebug(dm_nl2br(db::interpolateQuery($sql, $data)));
        $rows = $db->getAll($sql, $data);

        $key = 'id';
        if (isset($filter['key_to_use'])) {
            $key = $filter['key_to_use'];
        }
        $liste = array();
        if (isset($filter['for_json'])) {
            foreach ($rows as $i => $row) {
                if (isset($row[$filter['id_to_use']]) && strlen($row[$filter['id_to_use']]) > 0) {
                    $row["index"] = $i;
                    $liste[$row[$key]] = ['id' => $row[$filter['id_to_use']], 'text' => $row[$filter['text_to_use']]];
                }
            }
        } else {
            foreach ($rows as $i => $row) {
                $row["index"] = $i;
                $liste[$row[$key]] = new self(intval($row['id']), $row);
            }
        }
        return $liste;
    }

    /**
     * Listing function of Acquisitions
     *
     * @param array $filter - Filters
     * @return int
     * @throws Exception
     */
    public static function getCountPropositions(array $filter = []): int
    {
        global $db;
        list($where, $data) = self::getFiltreData($filter);

        $where .= " and not exists (select 1 from inventaires_fiches_equipements ife where ife.numero_inventaire = tb.numero_inventaire and (ife.fiche_id <> f.id or ife.equipement_bio_medicaux_type_id <> ebm.equipement_bio_medicaux_type_id) and ife.inventaire_id = tb.inventaire_id)";

        $sql = "select count(*)
                from inventaires_lignes tb
                left join equipements_bio_medicaux_types ebmtSourceForCode on is_editable = 0 and ebmtSourceForCode.nom = tb.code
                left join equipements_bio_medicaux_types ebmtSource on ebmtSource.id = case when is_editable = 1 then tb.code_norme_id else ebmtSourceForCode.id end
                join equipements_bio_medicaux ebm on ebm.equipement_bio_medicaux_type_id = ebmtSource.id
				left join fiches f on ebm.fiche_id = f.id
                left join generaux_locaux gl on gl.fiche_id = f.id
				left join equipements_bio_medicaux_types ebmt on ebm.equipement_bio_medicaux_type_id = ebmt.id
				left join inventaires_locaux il on il.id = tb.inventaire_local_id
                left join inventaires_fiches_equipements ife on ife.numero_inventaire = tb.numero_inventaire and ife.inventaire_id = tb.inventaire_id and ife.fiche_id = f.id and ife.equipement_bio_medicaux_type_id = ebmt.id
                where true $where";
//        printDebug(dm_nl2br(db::interpolateQuery($sql, $data)));exit;
        return $db->getOne($sql, $data);
    }

    /**
     * Listing function of Acquisitions
     *
     * @param array $filter - Filters
     * @param string $orderBy - Order
     * @param string $sql_limit - Limit Offset
     * @return InventairesLignes[]
     * @throws Exception
     */
    public static function getListePropositions(array $filter = [], string $orderBy = 'tb.id', string $sql_limit = ''): array
    {
        global $db;
        list($where, $data) = self::getFiltreData($filter);

        $where .= " and not exists (select 1 from inventaires_fiches_equipements ife where ife.numero_inventaire = tb.numero_inventaire and (ife.fiche_id <> f.id or ife.equipement_bio_medicaux_type_id <> ebm.equipement_bio_medicaux_type_id) and ife.inventaire_id = tb.inventaire_id)";

        $orderBy = 'est_associe desc, ' . $orderBy;
        $sql = "select tb.*, case when f.code_alternatif_2 is not null then coalesce(concat(f.code, concat(' (', f.code_alternatif_2, ')'), ' - ', gl.utilite)) else coalesce(concat(f.code, ' - ', gl.utilite)) end as local, 
                    coalesce(concat(ebmt.nom, ' - ', coalesce(ebmt.description, ''))) as equipement,
       				case when ife.fiche_id is not null then 1 else 0 end as est_associe, ebm.equipement_bio_medicaux_type_id, ebm.fiche_id,
       				case when exists (select 1 from logs_imports_inventaires_lignes liil join logs_imports_inventaires lii on liil.log_import_inventaire_id = lii.id where liil.numero_inventaire = tb.numero_inventaire and lii.inventaire_id = tb.inventaire_id limit 1) then 1 else 0 end as has_historique_non_lu,
                    ebm.qte_existante, ebm.qte_conservee, f.id as fiche_local_destination_id, tb.id as inventaire_ligne_id,
                    (select count(*) from inventaires_fiches_equipements ife where ife.fiche_id = f.id and ife.equipement_bio_medicaux_type_id = ebm.equipement_bio_medicaux_type_id and ife.inventaire_id = tb.inventaire_id) as qte_associee, ebm.id as ebm_id
                from inventaires_lignes tb
                left join equipements_bio_medicaux_types ebmtSourceForCode on is_editable = 0 and ebmtSourceForCode.nom = tb.code
                left join equipements_bio_medicaux_types ebmtSource on ebmtSource.id = case when is_editable = 1 then tb.code_norme_id else ebmtSourceForCode.id end
                join equipements_bio_medicaux ebm on ebm.equipement_bio_medicaux_type_id = ebmtSource.id
				left join fiches f on ebm.fiche_id = f.id
                left join generaux_locaux gl on gl.fiche_id = f.id
				left join equipements_bio_medicaux_types ebmt on ebm.equipement_bio_medicaux_type_id = ebmt.id
				left join inventaires_locaux il on il.id = tb.inventaire_local_id
                left join inventaires_fiches_equipements ife on ife.numero_inventaire = tb.numero_inventaire and ife.inventaire_id = tb.inventaire_id and ife.fiche_id = f.id and ife.equipement_bio_medicaux_type_id = ebmt.id
                where true $where
                order by $orderBy
                $sql_limit";
//        printDebug(dm_nl2br(db::interpolateQuery($sql, $data)));exit;
        $rows = $db->getAll($sql, $data);

        $key = 'id';
        if (isset($filter['key_to_use'])) {
            $key = $filter['key_to_use'];
        }
        $liste = array();
        if (isset($filter['for_json'])) {
            foreach ($rows as $i => $row) {
                if (isset($row[$filter['id_to_use']]) && strlen($row[$filter['id_to_use']]) > 0) {
                    $row["index"] = $i;
                    $liste[$row[$key]] = ['id' => $row[$filter['id_to_use']], 'text' => $row[$filter['text_to_use']]];
                }
            }
        } else {
            foreach ($rows as $i => $row) {
                $row["index"] = $i;
                $liste[$row[$key]] = new self(intval($row['id']), $row);
            }
        }

        return $liste;
    }

    /**
     * Listing function of Acquisitions
     *
     * @param array $filter - Filters
     * @return int
     * @throws Exception
     */
    public static function getCountPropositionsByInventaire(array $filter = []): int
    {
        global $db;
        list($where, $data) = self::getFiltreData($filter);

        $where .= " and exists (select 1 from equipements_bio_medicaux ebm on ebm.equipement_bio_medicaux_type_id = ebmtSource.id and ebm.projet_id = ? limit 1)";
        $data[] = getProjetId();

        $sql = "select count(*)
                from inventaires_lignes tb
                left join equipements_bio_medicaux_types ebmtSourceForCode on is_editable = 0 and ebmtSourceForCode.nom = tb.code
                left join equipements_bio_medicaux_types ebmtSource on ebmtSource.id = case when is_editable = 1 then tb.code_norme_id else ebmtSourceForCode.id end
				left join fiches f on ebm.fiche_id = f.id
                left join generaux_locaux gl on gl.fiche_id = f.id
				left join inventaires_locaux il on il.id = tb.inventaire_local_id
                where true $where";
//        printDebug(dm_nl2br(db::interpolateQuery($sql, $data)));exit;
        return $db->getOne($sql, $data);
    }

    /**
     * Listing function of Acquisitions
     *
     * @param array $filter - Filters
     * @param string $orderBy - Order
     * @param string $sql_limit - Limit Offset
     * @return InventairesLignes[]
     * @throws Exception
     */
    public static function getListePropositionsByInventaire(array $filter = [], string $orderBy = 'tb.id', string $sql_limit = ''): array
    {
        global $db;
        list($where, $data) = self::getFiltreData($filter);

        $where .= " and exists (select 1 from equipements_bio_medicaux ebm on ebm.equipement_bio_medicaux_type_id = ebmtSource.id and ebm.projet_id = ? limit 1)";
        $data[] = getProjetId();

        $sql = "select tb.*, case when f.code_alternatif_2 is not null then coalesce(concat(f.code, concat(' (', f.code_alternatif_2, ')'), ' - ', gl.utilite)) else coalesce(concat(f.code, ' - ', gl.utilite)) end as local, 
                    coalesce(concat(ebmt.nom, ' - ', coalesce(ebmt.description, ''))) as equipement, il.numero_local,
       				case when ife.fiche_id is not null then 1 else 0 end as est_associe
                from inventaires_lignes tb
                left join equipements_bio_medicaux_types ebmtSourceForCode on is_editable = 0 and ebmtSourceForCode.nom = tb.code
                left join equipements_bio_medicaux_types ebmtSource on ebmtSource.id = case when is_editable = 1 then tb.code_norme_id else ebmtSourceForCode.id end
                left join inventaires_fiches_equipements ife on ife.numero_inventaire = tb.numero_inventaire and ife.inventaire_id = tb.inventaire_id
				left join equipements_bio_medicaux_types ebmt on ife.equipement_bio_medicaux_type_id = ebmt.id
				left join fiches f on f.id = ife.fiche_id
                left join generaux_locaux gl on gl.fiche_id = f.id
				left join inventaires_locaux il on il.id = tb.inventaire_local_id
                where true $where
                order by $orderBy
                $sql_limit";
//        printDebug(dm_nl2br(db::interpolateQuery($sql, $data)));exit;
        $rows = $db->getAll($sql, $data);

        $key = 'id';
        if (isset($filter['key_to_use'])) {
            $key = $filter['key_to_use'];
        }
        $liste = array();
        if (isset($filter['for_json'])) {
            foreach ($rows as $i => $row) {
                if (isset($row[$filter['id_to_use']]) && strlen($row[$filter['id_to_use']]) > 0) {
                    $row["index"] = $i;
                    $liste[$row[$key]] = ['id' => $row[$filter['id_to_use']], 'text' => $row[$filter['text_to_use']]];
                }
            }
        } else {
            foreach ($rows as $i => $row) {
                $row["index"] = $i;
                $liste[$row[$key]] = new self(intval($row['id']), $row);
            }
        }
        return $liste;
    }

    /**
     * Listing function of Acquisitions
     *
     * @param array $filter - Filters
     * @param string $orderBy - Order
     * @param string $sql_limit - Limit Offset
     * @return InventairesLignes[]
     * @throws Exception
     */
    public static function getListeSimple($filter = [], $orderBy = 'tb.id', $sql_limit = '')
    {
        global $db;
        list($where, $data) = self::getFiltreData($filter);

        $sql = "select tb.*
                from inventaires_lignes tb
                where true $where
                order by $orderBy
                $sql_limit";
        //printDebug(dm_nl2br(db::interpolateQuery($sql, $data)));
        $rows = $db->getAll($sql, $data);

        $key = 'id';
        if (isset($filter['key_to_use'])) {
            $key = $filter['key_to_use'];
        }
        $liste = array();
        if (isset($filter['for_json'])) {
            foreach ($rows as $i => $row) {
                $row["index"] = $i;
                $liste[$row[$key]] = ['id' => $row[$filter['id_to_use']], 'text' => $row[$filter['text_to_use']]];
            }
        } else {
            foreach ($rows as $i => $row) {
                $row["index"] = $i;
                $liste[$row[$key]] = new self(intval($row['id']), $row);
            }
        }
        return $liste;
    }

    public static function getListeValeurs($filter = [], $orderBy = 'ligne_id', $raw = false)
    {
        global $db;
        list($where, $data) = self::getFiltreData($filter);

        $sql = "select tb.id as ligne_id, ic.id as champ_id, iv.valeur, tb.numero_inventaire
                from inventaires_lignes tb
                join inventaires_valeurs iv on iv.inventaire_ligne_id = tb.id
                join inventaires_champs ic on iv.inventaire_champ_id = ic.id
                left join equipements_bio_medicaux_types ebmtSourceForCode on is_editable = 0 and ebmtSourceForCode.nom = tb.code
                left join equipements_bio_medicaux_types ebmtSource on ebmtSource.id = case when is_editable = 1 then tb.code_norme_id else ebmtSourceForCode.id end
                left join inventaires_fiches_equipements ife on ife.numero_inventaire = tb.numero_inventaire and ife.inventaire_id = tb.inventaire_id
                where true $where
                order by $orderBy";
//        printDebug(dm_nl2br(db::interpolateQuery($sql, $data)));exit;
        $rows = $db->getAll($sql, $data);

        $key = 'ligne_id';
        if (isset($filter['key_to_use'])) {
            $key = $filter['key_to_use'];
        }

        $liste = array();
        foreach ($rows as $i => $row) {
            $iter = $key <> 'index' ? $row[$key] : $i;
            $liste[$iter][(int)$row['champ_id']] = $raw ? $row->getRaw('valeur') : $row['valeur'];
        }
        return $liste;
    }

    public static function getListeValeursDistinctParChamp(array $filter = [], string $orderBy = 'ligne_id'): array
    {
        list($where, $data) = self::getFiltreData($filter);

        $sql = "select tb.id as ligne_id, ic.id as champ_id, iv.valeur, tb.numero_inventaire
                    from inventaires_lignes tb
                    join inventaires_valeurs iv on iv.inventaire_ligne_id = tb.id
                    join inventaires_champs ic on iv.inventaire_champ_id = ic.id
                    where true $where
                    order by $orderBy";
        $rows = db::instance()->getAll($sql, $data);

        $liste = [];
        foreach ($rows as $row) {
            $liste[(int)$row['champ_id']][$row['valeur']] = $row['valeur'];
        }
        return $liste;
    }

    public static function getResumeToolTip($numero_inventaire, $inventaire_id)
    {
        $inventaire_ligne_id = InventairesLignes::getIdByNumreo($numero_inventaire);
        $ligne = new self($inventaire_ligne_id);
        $valeurs = InventairesLignes::getListeValeurs(['inventaire_ligne_id' => $inventaire_ligne_id]);
        $valeurs = $valeurs[$inventaire_ligne_id] ?? [];
        $inventaire = new Inventaires($inventaire_id);

        $html = '<div style="vertical-align: top">';

        ob_start();
        DocumentsListesElements::editeurPhotos('inventaires_lignes', $inventaire_ligne_id, false);
        $docsHtml = ob_get_clean();
        ob_end_clean();
        if (!empty($docsHtml)) {
            $html .= '<div style="float: left; max-width: 600px;">';
            $html .= $docsHtml;
            $html .= '</div>';
        }

        $html .= '<div style="float: left; margin: 15px; max-width: 600px;">';
        $html .= '<h2 style="margin-bottom: 10px;">' . txt('Équipement') . '</h2>';
        $champsActifs = $inventaire->getChampsActifs($ligne->is_editable);
        foreach (InventairesLignes::getChamps() as $key => $champ) {
            if (in_array($key, $champsActifs)) {
                $nomChamp = $champ['colonneNom'] ?? $key;
                $html .= '<div style="margin-bottom: 5px;">' . $champ['titre'] . ': <b style="color: blue;">' . ($ligne->$nomChamp ?? '-') . "</b></div>";
            }
        }
        foreach ($inventaire->champs as $champ) {
            if (in_array($champ->id, $champsActifs)) {
                $html .= '<div style="margin-bottom: 5px;">' . $champ->nom . ': <b style="color: blue;">' . ($valeurs[$champ->id] ?? '-') . "</b></div>";
            }
        }
        $html .= '</div>';

        if ($ligne->inventaire_local_id > 0) {
            $inventaireLocal = new InventairesLocaux($ligne->inventaire_local_id);
            $html .= '<div style="float: left; margin: 15px; max-width: 600px;">';
            $html .= '<h2 style="margin-bottom: 10px;">' . txt('Local de provenance (inventaire)') . '</h2>';
            $html .= InventairesLocaux::getResumeToolTip($inventaireLocal->numero_local, $inventaire_id);
            $html .= '</div>';
        }

        if ($ligne->fiche_id > 0) {
            $html .= '<div style="float: left; margin: 15px; max-width: 600px;">';
            $html .= '<h2 style="margin-bottom: 10px;">' . txt('Local de destination (DocMatic)') . '</h2>';
            $html .= BatimentsNiveaux::getHierarchieToolTip($ligne->fiche_id);
            $html .= '</div>';
        }
        $html .= '<br style="clear: both" /></div>';

        return $html;
    }

    public static function getIdByNumreo($numero_inventaire)
    {
        global $db;
        $sql = "select id from inventaires_lignes where numero_inventaire = ?";
        return $db->getOne($sql, [$numero_inventaire]);
    }

    public static function delete($id)
    {
        global $db;
        $db->autocommit(false);
        $db->query("DELETE FROM `inventaires_valeurs` WHERE inventaire_ligne_id = ?", array($id));
        $db->query("DELETE FROM `inventaires_lignes` WHERE id = ?", array($id));
        $db->commit();
    }

    /**
     * @param array $filter
     * @param bool $includeEmpty
     * @return false|string
     */
    public static function getJsonOptions($filter = array(), $includeEmpty = false)
    {
        $filter['for_json'] = 1;
        $json_array = [];
        if ($includeEmpty) {
            $json_array[] = ['id' => '', 'text' => ''];
        }
        $json_array = array_merge($json_array, array_values(self::getListe($filter)));
        return json_encode($json_array);
    }

    public function getClassLigne()
    {
        $class = (isset($this->est_associe) && $this->est_associe ? 'associe' : 'dissocie');
        if ($this->is_obsolete == 1) {
            $class .= ' is_obsolete';
        } elseif ($this->est_associe && $this->statut_externe == 'Inactif') {
            $class .= ' associe_inactif_externe';
        } elseif (!$this->est_associe && $this->statut_externe == 'Inactif') {
            $class .= ' non_associe_inactif_externe';
        }
        return $class;
    }

    public static function getFormMobileHTML($inventaire_ligne_id, $is_from_mobile = true)
    {
        ?>
        <fieldset class="noMinHeight">
            <style>
                fieldset {
                    padding: 5px;
                }

                input[type=text], select, textarea {
                    min-width: 250px;
                    max-width: 250px;
                    width: 250px;
                }

                fieldset.cotacote section > div {
                    margin: 0 0 10px 0 !important;
                    padding: 0 0 0 0 !important;
                }
                .edit_popupContent section > div {
                    width: 650px !important;
                }

                #editables label {
                    width: <?= ($is_from_mobile ? 200 : 290) ?>px !important;
                    margin: 0 0 5px 0 !important;
                    padding: 0 0 0 0 !important;
                }

                fieldset.cotacote textarea {
                    height: 50px !important;
                    min-height: 50px !important;
                }

                section {
                    margin: 0 0 0 0 !important;
                    padding: 0 0 0 0 !important;
                }

                .select2-search-choice-close {
                    top: 3px !important;
                }
            </style>
            <fieldset class="noMinHeight cotacote">
                <?php
                $inventaire = Inventaires::getInventaireCourant();
                ?>
                <input type="hidden" name="inventaire_id" value="<?= $inventaire->id ?>" />
                <?php
                if ($is_from_mobile) {
                    ?>
                    <section class="formulaire_general">
                        <label></label>
                        <div style="">
                            <select id="inventaire_ligne_id" name="inventaire_ligne_id"></select>
                        </div>
                    </section>
                    <?php
                } else {
                    ?>
                    <input type="hidden" name="inventaire_ligne_id" value="<?= $inventaire_ligne_id ?>" />
                    <?php
                }
                ?>
                <?
                $inventaire = Inventaires::getInventaireCourant();
                $ligne = new self($inventaire_ligne_id);
                $champsActifs = $inventaire->getChampsActifs($ligne->is_editable);
                foreach (InventairesLignes::getChamps() as $nomChamp => $meta) {
                    if (in_array($nomChamp, $champsActifs)) {
                        ?>
                        <section class="formulaire_general">
                            <label style=""><?= $meta['titre'] ?></label>
                            <div style="display: inline-block; position: relative;">
                                <? self::afficheChampEdition($nomChamp, $meta, $ligne->$nomChamp) ?>
                            </div>
                        </section>
                        <?
                    }
                }

                $valeurs = InventairesLignes::getListeValeurs(['inventaire_ligne_id' => $inventaire_ligne_id]);
                foreach ($inventaire->champs as $champ) {
                    if (in_array($champ->id, $champsActifs)) {
                        ?>
                        <section class="formulaire_general">
                            <label style=""><?= $champ->nom ?></label>
                            <div style="">
                                <? self::afficheChampEdition('inventaires_valeurs[' . $inventaire_ligne_id . '-' . $champ->id . ']', ['type' => 'inputText'], (isset($valeurs[$inventaire_ligne_id]) && isset($valeurs[$inventaire_ligne_id][$champ->id]) ? $valeurs[$inventaire_ligne_id][$champ->id] : '')) ?>
                            </div>
                        </section>
                        <?
                    }
                }
                ?>
                <br style="clear: both;">
            </fieldset>
            <br style="clear: both;">
        </fieldset>
        <?php
        DocumentsListesElements::afficheFormulaireMobile('inventaires_lignes', $inventaire_ligne_id);
        ?>
        <script>
			$(document).ready(function () {

				load_locaux_apartenance();

				$('select.is-select2').select2({width: '250px', allowClear: true});

				$('select.is-select2').on('change', function (evt) {
					$('.select2-focusser').blur();
				});
				$('select.is-select2').on('select2-close', function (evt) {
					setTimeout(() => {
						$('.select2-focusser').blur();
					}, 10);
				});
				$('select.is-select2').on('select2-closing', function (evt) {
					setTimeout(() => {
						$('.select2-focusser').blur();
					}, 10);
				});

				if (<?= ($is_from_mobile ? 'true' : 'false') ?>) {
					$.startObjectLoader($('#fiche_id'), 'ligne_id_loader');
					$.get('index.php', {
						action: 'getInventairesLignes'
					}, function (json_data) {
						loading_dynamic_combobox('inventaire_ligne_id', <?= $inventaire_ligne_id ?>, json_data);
						$.stopObjectLoader('ligne_id_loader');

						$('#inventaire_ligne_id').on('change', function (evt) {
							window.location = `index.php?section=releveMobile&module=inventaire&ligne_id_sel=${this.value}`;
						});
					}, 'json');
				}

				$('#marque_id').on('change', function () {
					reloadModeles(0);
				});

				$('#ajoutImage').click(function () {
					$('#ajoutImageContainer').append('<input type="file" name="images[]" accept="image/gif, image/jpeg, image/png" capture />');
					parent.fancybox_resize();
				});

				setTimeout(() => {
					$('.select2-search input').prop('focus', false);
				}, 100);
			});

			function load_locaux_apartenance() {
				const url = 'index.php?section=fiche&module=index';
				$.get(url, { action: 'getFichesList', term: '', batiment_id: '', fiche_type_id: '', fiche_sous_type_id: 2000002, numero_lot: '', code_alternatif: '', id_revit: '', id_codebook: '', procedure_entretien_id: '', selected: '', disable_fitre_avance: 1 }, function(json_data){
					loading_dynamic_combobox('fiche_local_id', $('#fiche_local_id').val(), json_data);
				}, 'json');
			}

			function reloadMarques(selected_id) {
				const url = 'index.php';
				$.get(url, {action: 'getMarques', selected_id: selected_id}, function (html) {
					$('#marque_id').html(html);
					$('#marque_id').select2('destroy');
					$('#marque_id').select2({
						width: '250px',
						allowClear: true
					});

					$('#marque_id').on('change', function (evt) {
						$('.select2-focusser').blur();
					});
				});
			}

			function reloadModeles(selected_id) {
				const url = 'index.php';
				$.get(url, {
					action: 'getModelesFromMarque',
					marque_id: $('#marque_id').val(),
					selected_id: selected_id
				}, function (html) {
					$('#modele_id').html(html);
					$('#modele_id').select2('destroy');
					$('#modele_id').select2({
						width: '250px',
						allowClear: true
					});

					$('#modele_id').on('change', function (evt) {
						$('.select2-focusser').blur();
					});
				});
			}

			function openMarquePopup() {
				$.fancybox({
					'overlayShow': false,
					'type': 'iframe',
					'modal': true,
					'centerOnScroll': true,
					'width': 1024,
					'href': 'index.php?section=admin&module=edit&table=marques&type=client&id=0&popupMobile=1&callback=reloadMarques',
					'onComplete': function () {
						$('#fancybox-frame').load(function () { // wait for frame to load and then gets it's height
							fancybox_resize(300);
						});
					},
					'onClosed'			:	function(){
						$('html').css({ overflow: 'auto' });
					}
				});
			}

			function openModelePopup() {
				const marque_id = $('#marque_id').val();
				$.fancybox({
					'overlayShow': false,
					'type': 'iframe',
					'modal': true,
					'centerOnScroll': true,
					'width': 1024,
					'href': 'index.php?section=admin&module=edit&table=modeles&type=client&id=0&popupMobile=1&callback=reloadModeles' + (marque_id > 0 ? '&marque_id=' + marque_id : ''),
					'onComplete': function () {
						$('#fancybox-frame').load(function () { // wait for frame to load and then gets it's height
							fancybox_resize(300);
						});
					},
					'onClosed'			:	function(){
						$('html').css({ overflow: 'auto' });
					}
				});
			}
        </script>
        <?php
    }

    public function getEditorHTML($iteration = 0, $only_champs_denormalises = false)
    {
        self::getFormMobileHTML($this->id, false);
    }

    public static function validate($data, $fiche_id)
    {
        return true;
    }

    public static function afficheChampEdition($nomChamp, $meta, $valeur)
    {
        switch ($meta['type']) {
            case 'inputText':
                ?>
                <input type="text" id="<?= $nomChamp ?>" name="<?= $nomChamp ?>" value="<?= $valeur ?>" autocomplete="off" class="<?= ($meta['htmlClass'] ?? '') ?>">
                <?
                break;
            case 'textarea':
                ?>
                <textarea id="<?= $nomChamp ?>" name="<?= $nomChamp ?>" style="min-height: 100px; overflow: hidden; overflow-wrap: break-word; resize: none; height: 50px;" class="<?= ($meta['htmlClass'] ?? '') ?>"><?= $valeur ?></textarea>
                <?
                break;
            case 'select':
                if ($nomChamp == 'inventaire_local_id' && isset($_GET['inventaire_local_id']) && (int)($valeur ?? 0) == 0) {
                    $valeur = (int)$_GET['inventaire_local_id'];
                }
                ?>
                <select id="<?= $nomChamp ?>" name="<?= $nomChamp ?>" class="<?= ($meta['htmlClass'] ?? '') ?>">
                    <option value=""></option>
                    <?
                    if (isset($meta['classe'])) {
                        $classe = $meta['classe'];
                        $classe::getOptions(($meta['filtre'] ?? []), ($valeur ?? 0));
                    }
                    ?>
                </select>
                <?
                break;
            case 'date':
                ?>
                <input type="text" id="<?= $nomChamp ?>" name="<?= $nomChamp ?>"
                       value="<?= $valeur ?>"
                       style="min-width: 75px!important; width: 75px!important; text-align: center;"
                       class="date hasDatepicker <?= ($meta['htmlClass'] ?? '') ?>" autocomplete="off" />
                <?
                break;
            case 'float':
                ?>
                <input type="number" id="<?= $nomChamp ?>" name="<?= $nomChamp ?>" value="<?= $valeur ?>"
                       style="min-width: 75px!important; width: 75px!important; text-align: right;"
                       class="justInteger <?= ($meta['htmlClass'] ?? '') ?>" autocomplete="off" />
                &nbsp;<?= $meta['unite'] ?>
                <?
                break;
        }
        if (isset($meta['htmlExtra'])) {
            print $meta['htmlExtra'];
        }
    }

    public static function afficheChampValeursDistincts($nomChamp, $meta, $selecteds): void
    {
        $inventaire = Inventaires::getInventaireCourant();
        ?>
        <select id="<?= $nomChamp ?>" name="<?= $nomChamp ?>" style="width: 200px;" class="filtre champ_multiple" multiple data-placeholder="- <?= $meta['titre'] ?> -">
            <?
            switch ($meta['type']) {
                case 'inputText':
                    $sql = "select distinct " . db::tableColonneValide($nomChamp) . " from inventaires_lignes where inventaire_id = ? and " . db::tableColonneValide($nomChamp) . " != '' order by " . db::tableColonneValide($nomChamp);
                    $valeurs = db::instance()->getCol($sql, array($inventaire->id));
                    foreach ($valeurs as $valeur) {
                        $sel = in_array($valeur, $selecteds) ? ' selected="selected"' : '';
                        ?>
                        <option value="<?= $valeur ?>"<?= $sel ?>><?= $valeur ?></option>
                        <?
                    }
                    break;
                case 'select':
                    if (isset($meta['classe'])) {
                        $classe = $meta['classe'];
                        $classe::getOptions(($meta['filtre'] ?? []), $selecteds, false);
                    }
                    break;
            }
            ?>
        </select>
        <?
    }

    public static function getChamps()
    {
        return [
            'barcode_nouveau' => [
                'titre' => txt('Numéro étiquette (nouveau)'),
                'type' => 'inputText',
            ],
            'inventaire_local_id' => [
                'titre' => txt('Local de provenance (inventaire)'),
                'type' => 'select',
                'classe' => 'InventairesLocaux',
                'filtre' => ['is_mobile' => 1],
                'htmlClass' => 'is-select2',
                'colonneNom' => 'numero_local'
            ],
            'fiche_local_id' => [
                'titre' => txt('Local de provenance (fiches)'),
                'type' => 'select',
                'classe' => 'Fiches',
                'filtre' => ['fiche_sous_type_id' => 2000002],
                'htmlClass' => 'lazy_loaded_fiches',
                'colonneNom' => 'code_fiche'
            ],
            'code_norme_id' => [
                'titre' => txt('Code normé'),
                'type' => 'select',
                'classe' => 'EquipementsBioMedicauxTypes',
                'htmlClass' => 'is-select2',
                'colonneNom' => 'equipement_norme'
            ],
            'marque_id' => [
                'titre' => txt('Marque'),
                'type' => 'select',
                'classe' => 'Marque',
                'htmlExtra' => '<a href="javascript: openMarquePopup();" style="position: absolute; top: -1px;" class="fancy_small">
                                    <img src="css/images/icons/dark/plus.png"></a>',
                'htmlClass' => 'is-select2',
                'colonneNom' => 'marque'
            ],
            'modele_id' => [
                'titre' => txt('Modèle'),
                'type' => 'select',
                'htmlExtra' => '<a href="javascript: openModelePopup();" style="position: absolute; top: -1px;" class="fancy_small">
                                    <img src="css/images/icons/dark/plus.png"></a>',
                'htmlClass' => 'is-select2',
                'colonneNom' => 'modele'
            ],
            'numero_serie' => [
                'titre' => txt('Numéro de série'),
                'type' => 'inputText',
            ],
            'date_fabrication' => [
                'titre' => txt('Date de fabrication'),
                'type' => 'date',
            ],
//            'hauteur' => [
//                'titre' => txt('Hauteur'),
//                'type' => 'float',
//                'unite' => 'mm',
//            ],
//            'largeur' => [
//                'titre' => txt('Largeur'),
//                'type' => 'float',
//                'unite' => 'mm',
//            ],
//            'profondeur' => [
//                'titre' => txt('Profondeur'),
//                'type' => 'float',
//                'unite' => 'mm',
//            ],
            'type_installation_id' => [
                'titre' => txt('Type d\'installation'),
                'type' => 'select',
                'classe' => 'InstallationsTypes',
                'htmlClass' => 'is-select2',
                'colonneNom' => 'type_installation'
            ],
            'condition_vetuste_id' => [
                'titre' => txt('Condition de l\'équipement'),
                'type' => 'select',
                'classe' => 'ConditionsVetustesTypes',
                'htmlClass' => 'is-select2',
                'colonneNom' => 'condition_vetuste'
            ],
            'remarque' => [
                'titre' => txt('Commentaires'),
                'type' => 'textarea',
            ],
        ];
    }

    public static function traitementSauvegardeSupplementaire($id, $requestData, $is_ajout)
    {
        $valeurs = InventairesLignes::getListeValeurs(['inventaire_ligne_id' => $id]);
        if (isset($requestData['inventaires_valeurs'])) {
            foreach ($requestData['inventaires_valeurs'] as $key => $value) {
                list($inventaire_ligne_id, $inventaire_champ_id) = explode('-', $key);
                if (isset($valeurs[$inventaire_ligne_id]) && isset($valeurs[$inventaire_ligne_id][$inventaire_champ_id])) {
                    $sql = "UPDATE inventaires_valeurs SET valeur = ? WHERE inventaire_ligne_id = ? AND inventaire_champ_id = ?";
                } else {
                    $sql = "INSERT INTO inventaires_valeurs (valeur, inventaire_ligne_id, inventaire_champ_id) VALUES (?, ?, ?)";
                }
                db::instance()->query($sql, [$value, $inventaire_ligne_id, $inventaire_champ_id]);
            }
        }
    }
}

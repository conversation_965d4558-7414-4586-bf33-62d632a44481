<?
class MatricesImportationsListes extends DbRow {
	public $id;
	public $nom;
	public $formulaire_id;
	public $formulaire;
	public $valeurs;
	public $description;
	
	protected function setDataForId($id)
	{
        global $db;
        
		$sql = "select matrices_importations_listes.*, formulaires.nom as formulaire
                from matrices_importations_listes 
                join formulaires on formulaires.id = formulaire_id
                where matrices_importations_listes.id = ?";
		$row = $db->getRow($sql, array($id));
        
		$this->populateThisWithThat($row);
		
		$sql = "select *
                from matrices_importations_listes_valeurs
                where matrice_importation_liste_id = ?
                order by id";
		$valeurs = $db->getAll($sql, array($id));
        
        $this->valeurs = isset($valeurs) ? $valeurs : array();
	}
	
	public static function getListe($filter = array(), $orderBy = 'nom')
	{
        global $db;
        
        $where = '';
        $data = array();
        
		$sql = "select matrices_importations_listes.id, matrices_importations_listes.*, formulaires.nom as formulaire
                from matrices_importations_listes 
                join formulaires on formulaires.id = formulaire_id
                where true $where
                order by $orderBy";
		$rows = $db->getAssoc($sql, $data);
		
        $valeurs = array();
        $matrice_importation_liste_ids = array_keys($rows);
        
        if(count($matrice_importation_liste_ids) > 0)
        {
            $sql = "select *
                    from matrices_importations_listes_valeurs
                    where matrice_importation_liste_id in (".db::placeHolders($matrice_importation_liste_ids).")
                    order by id";
            $valeurs_tmp = $db->getAll($sql, $matrice_importation_liste_ids);

            foreach($valeurs_tmp as $row_col)
            {
                $valeurs[intval($row_col['matrice_importation_liste_id'])][] = $row_col;
            }
        }
        
		$liste = array();
		foreach($rows as $i => $row)
		{
            $row['valeurs'] = isset($valeurs[intval($row['id'])]) ? $valeurs[intval($row['id'])] : array();
			$liste[intval($row['id'])] = new self(intval($row['id']), $row);
		}
		
		return $liste;
	}
    
    public function getLignes($fiche_ids = array())
    {
        global $db;
        
        $valeurs = $this->valeurs;
        
        $where = "";
        $data = array();
        
        $sql_extra_cols = "";
        $sql_extra_join = "";
        
        $tables_already_in_join = array();
        foreach($valeurs as $colonne)
        {
            if(!isset($tables_already_in_join[$colonne['formulaire']]))
            {
                $table_alias = 'alias_'.uniqid();
                $sql_extra_join .= "\nleft join ".$colonne['formulaire']::SQL_TABLE_NAME." as ".$table_alias." on ".$table_alias.".fiche_id = fiches.id";
                $tables_already_in_join[$colonne['formulaire']] = $table_alias;
            }
            
            $metas = $colonne['formulaire']::getFlatChampsSaisieMetaData();
            $meta = $metas[$colonne['nom_champ']];
            
            $table_alias_2 = $tables_already_in_join[$colonne['formulaire']];
            if($meta['type'] == 'select' && !isset($meta['liste']))
            {
                $table_alias = 'alias_'.uniqid();
                
                $nom_champ = $table_alias.".nom";
                if(isset($meta['sql_for_nom']))
                {
                    $nom_champ = FicheFormWrapper::formatSqlForNom($meta['sql_for_nom'], $table_alias);
                }
                
                $sql_extra_cols .= ", ".$nom_champ." as ".$colonne['nom_champ'];//.(isset($colonne['alias']) && dm_strlen($colonne['alias']) > 0 ? $colonne['alias'] : $colonne['nom_champ']);
                $sql_extra_join .= "\nleft join ".$meta['table']." as ".$table_alias." on ".$table_alias.".id = ".$table_alias_2.".".$meta['champ_id']."";
            }
            else if($meta['type'] == 'radio')
            {
            }
            else if($meta['type'] == 'checkbox')
            {
                $sql_extra_cols .= ", case when ".$table_alias_2.".".$meta['champ_id']." = 1 then 'Oui' else '' end as ".$colonne['nom_champ'];
            }
            else if($meta['type'] == 'textarea' || $meta['type'] == 'input')
            {
                if(isset($meta['unite_prefixe']))
                {
                    $sql_extra_cols .= ", concat('".$meta['unite_prefixe']." ', ".$table_alias_2.".".$meta['champ_id'].") as ".$colonne['nom_champ'];
                }
                else if(isset($meta['unite']))
                {
                    $sql_extra_cols .= ", concat(".$table_alias_2.".".$meta['champ_id'].", ' ".$meta['unite']."') as ".$colonne['nom_champ'];
                }
                else
                {
                    $sql_extra_cols .= ", ".$table_alias_2.".".$meta['champ_id']." as ".$colonne['nom_champ'];
                }
            }
        }
        
        if($this->fiche_sous_type_id > 0)
        {
            $where .= " and fiche_sous_type_id = ?";
            $data[] = $this->fiche_sous_type_id;
        }
        
        if(dm_strlen($this->numero_lot) > 0)
        {
            $where .= " and numero_lot in (?)";
            $data[] = dm_implode("', '", dm_explode(';', $this->numero_lot));
        }
        
        $sql = "select fiches.code ".$sql_extra_cols." "
                . "from fiches "
                . $sql_extra_join . " "
                . "where true $where "
                . "order by fiches.code";
        $lignes = $db->getAll($sql, $data);
        
        return $lignes;
    }
	
    public function getEditorHTML($iteration = 0, $only_champs_denormalises = false)
	{        
        $is_ajout = ($this->id == 0);
        $className = $this->formulaire_id > 0 ? $this->formulaire : '';
?>
		<section>
			<label for="nom"><?=txt('Nom')?></label>
			<div>
				<input id="nom" type="text" name="nom" value="<?=($this->nom)?>" />
			</div>
		</section>

        <section>
            <label for="formulaire_id"><?=txt('Formulaire')?></label>
            <div>
                <select id="formulaire_id" name="formulaire_id">
                    <option value=""> </option>
                    <?=Formulaires::getOptions(array('ids' => Formulaires::getFormulairesIdWithQuantite()), $this->formulaire_id)?>
                </select>
            </div>
        </section>

        <section>
            <label for="description"><?=txt('Description')?></label>
            <div>
                <textarea id="description" name="description"><?=($this->description)?></textarea>
            </div>
        </section>

        <section id="valeurs">
            <label for="etapes"><?=txt('Associations')?></label>
            <div>
                
            </div>
            
            <table style="margin-bottom: 10px; width: 700px;" class="formFieldsTable">
                
                <thead id="matrices_importations_listes_titre">
<?
                if(dm_strlen($className) > 0)
                {
                    $className::getImportHTMLLigneTitre();
                }
?>
                </thead>
                
                <tbody id="matrices_importations_listes_valeurs" data-cur_iteration="<?=(count($this->valeurs) + 1)?>">
<?
                if(dm_strlen($className) > 0)
                {
                    if(isset($this->valeurs) && is_array($this->valeurs))
                    {
                        foreach($this->valeurs as $i => $valeur)
                        {
                            if(dm_strlen($className) > 0)
                            {
                                $className::getImportHTMLLigne(($i+1), $valeur['code_valeur_interne'], $valeur['code_valeur_externe']);
                            }
                        }
                    }
                }
?>
                </tbody>
                
                <tfoot id="matrices_importations_listes_ajout_ligne" style="<?=(dm_strlen($className) > 0 ? '' : 'display: none;')?>">
                    <tr class="top_block">
                        <td>
                            <a href="javascript: void(0);" class="add_line" style="">
                                <img src="css/images/icons/dark/plus.png" /></a>
                        </td>
                        <td colspan="20">
                            &nbsp;
                        </td>
                    </tr>
                </tfoot>
                
            </table>
            
        </section>
<?
	}
	
	public static function getDynamicTableTitle()
	{
?>
		{ "sTitle": "<?=txt('Nom')?>" },
		{ "sTitle": "<?=txt('Formulaire')?>" },
		{ "sTitle": "<?=txt('Description')?>" },
<?
	}
	
	public function getDynamicTableContent()
	{
        global $types_import_export;
?>
		"<?=dm_addslashes($this->getRaw('nom'))?>",
		"<?=dm_addslashes($this->getRaw('formulaire'))?>",
		"<?=dm_addslashes($this->getRaw('description'))?>"
<?
	}
	
	public static function getOptions($filter = array(), $selected = 0, $includeEmpty = false)
	{
        if($includeEmpty)
        {
?>
            <option value=""> </option>
<?
        }
        $rows = self::getListe($filter, 'nom');
        foreach($rows as $i => $row)
        {
            $sel = $row->id == $selected ? ' selected="selected"' : '';
?>
            <option value="<?=$row->id?>"<?=$sel?>><?=$row->nom.' ('.$row->formulaire.')'?></option>
<?
        }
	}
    
    public static function delete($id)
	{
        global $db;
        $db->autocommit(false);
        $db->query("DELETE FROM `matrices_importations_listes_valeurs` WHERE matrice_importation_liste_id = ?", array($id));
        $db->query("DELETE FROM `matrices_importations_listes` WHERE id = ?", array($id));
        $db->commit();
	}
    
    public static function validate($data, $id)
	{
        $valide = true;
        foreach($data as $colomnName => $value)
        {
            if($colomnName == 'nom')
            {
                if(dm_strlen($value) == 0)
                {
                    setErreur(txt('Le nom est obligatoire.'));
                    $valide = false;
                }
            }
            else if($colomnName == 'formulaire_id')
            {
                if(dm_strlen($value) == 0)
                {
                    setErreur(txt('Le formulaire est obligatoire.'));
                    $valide = false;
                }
            }
        }
        return $valide;
	}
}
<?php

class MobiliersLaboratoires extends FicheFormWrapper {
	private static $CLASS_NAME='MobiliersLaboratoires';
	const SQL_TABLE_NAME='mobiliers_laboratoires';
	const TITRE='Mobilier de laboratoire';
	public $id;
	public $fiche_id;
	public $no;
	public $mobilier_laboratoire_type_id;
	public $fourni_installe_id;
	public $existant_nouveau_id;
	public $qte_requise;
	public $commentaires;
    
    public static function getChampsSaisieMetaData()
    {
        return array(
            
            'mobilier_laboratoire_type' => array(
                'classe'      	=> 'MobiliersLaboratoiresTypes',
                'table'      	=> 'mobiliers_laboratoires_types',
                'champ_id'      => 'mobilier_laboratoire_type_id',
                'type'          => 'select',
                'format'        => 'int',
                'titre'         => txt('Type de mobilier', false),
                'css_editeur'   => 'xlarge',
                'obligatoire'	=> 1,
            ),
            
            'qte_requise' => array(
				'classe'      	=> '',
				'table'      	=> '',
				'champ_id'      => 'qte_requise',
				'type'          => 'input',
				'format'        => 'int',
				'titre'         => txt('Quantité', false),
				'css_editeur'   => 'tiny'
			),
            
            'fourni_installe' => array(
                'classe'      	=> 'FournisInstalles',
                'table'      	=> 'fournis_installes',
                'champ_id'      => 'fourni_installe_id',
                'type'          => 'select',
                'format'        => 'int',
                'titre'         => txt('Fourni / Installé', false),
                'css_editeur'   => 'average'
            ),
            
            'existant_nouveau' => array(
                'classe'      	=> 'ExistantsNouveaux',
                'table'      	=> 'existants_nouveaux',
                'champ_id'      => 'existant_nouveau_id',
                'type'          => 'select',
                'format'        => 'int',
                'titre'         => txt('Exist. / Nouv.', false),
                'css_editeur'   => 'small'
            ),
            
            'commentaires' => array(
				'table'      	=> '',
				'champ_id'      => 'commentaires',
				'type'          => 'input',
                'format'        => 'text',
                'titre'         => txt('Commentaires', false),
                'css_editeur'   => 'large'
            ),
            
        );
    }

	public static function getListe($filter = array(), $orderBy = 'ordre', $extra_data = array())
	{
        $extra_data = array(
            'mobilier_laboratoire_type' => array('table' => 'mobiliers_laboratoires_types'),
            'fourni_installe' => array('table' => 'fournis_installes'),
            'existant_nouveau' => array('table' => 'existants_nouveaux'),
            
        );
        return parent::getListe($filter, $orderBy, $extra_data);
    }
}
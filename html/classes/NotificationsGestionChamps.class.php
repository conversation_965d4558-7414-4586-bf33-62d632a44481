<?php
class NotificationsGestionChamps extends DbRow {
	public $champ_id;
	
	public static function getListe($filter = array(), $orderBy = 'champ_id')
	{
        global $db;
        
        $where = '';
        $data = array();
        
        if(isset($filter['notification_gestion_id']) && intval($filter['notification_gestion_id']) > 0)
        {
            $where .= ' and notification_gestion_id = ?';
            $data[] = intval($filter['notification_gestion_id']);
        }
        
		$sql = "select *
                from notifications_gestion_champs 
                where true $where
                order by $orderBy";
		$rows = $db->getAll($sql, $data);
		
		$liste = array();
		foreach($rows as $i => $row)
		{
			$liste[$row['champ_id']] = new self(0, $row);
		}
		
		return $liste;
	}
	
	public static function getListeCol($filter = array(), $orderBy = 'code')
	{
        global $db;
        
        $where = '';
        $data = array();
        
        if(isset($filter['notification_gestion_id']) && intval($filter['notification_gestion_id']) > 0)
        {
            $where .= ' and notification_gestion_id = ?';
            $data[] = intval($filter['notification_gestion_id']);
        }
        
		$sql = "select champ_id
                from notifications_gestion_champs 
                where true $where";
		$rows = $db->getCol($sql, $data);
		
		return $rows;
	}
}
?>
<?php

class ParametresArchitectures extends FicheFormWrapper {
	private static $CLASS_NAME='ParametresArchitectures';
	public static $AUTO=true;
	const SQL_TABLE_NAME='parametres_architectures';
	const TITRE='Paramètres architecturaux';
    public static $TYPE_FORM='UNIQUE';
    public $id;
	public $fiche_id;
	public $projet_id;
//Dimensions et occupation
	public $superficie_nette_totale;
	public $largeur_min;
	public $profondeur_min;
	public $hauteur_min;
	public $superficie_nette_dessinee;
	public $superficie_nette_sous_espace;
//Usage
	public $espace_type_id;
	public $espace_type;
	public $frequentation_id;
	public $frequentation;
	public $presence_id;
	public $presence;
	public $nombre_usager_jour;
	public $nombre_usager_soir;
	public $nombre_usager_nuit;
	public $acces_special;
	public $commentaires;
	public $champ_combine_1;
	public $champ_combine_2;
	public $champ_combine_3;
	public $champ_combine_4;
	public $champ_combine_5;
//Ambiance requise
	public $calme;
	public $chaleureuse;
	public $reduction_stimuli_exterieur;
	public $insonorisation;
	public $indice_transmission_son_type_id;
	public $indice_transmission_son_type;
//Protecteurs
	public $revetement_mural_id;
	public $revetement_mural;
	public $pare_choc_haut;
	public $pare_choc_bas;
	public $protecteur_coin;
	public $main_courante;
//Fenestration
	public $fenestration_interieure;
	public $verre_interieur_id;
	public $verre_interieur;
	public $fenestration_exterieure;
	public $verre_exterieur_id;
	public $verre_exterieur;
//Particularités
	public $vibration_id;
	public $vibration;
	public $tolerance_mouvement_id;
	public $tolerance_mouvement;
	public $resistance_feu_id;
	public $resistance_feu;
	public $transport_pneumatique;
	public $niveau_confinement_id;
	public $niveau_confinement;
	public $local_classification_grade_id;
	public $local_classification_grade;
	public $protection_rx;
	public $substance_nucleaire;
	public $blindage_magnetique;
	public $sensibilite_variation_temperature;
	public $milieu_sterile;
	public $matiere_dangeureuse;
	public $milieu_humide;
	public $etancheite;
	public $sante_mentale;
	public $materiau_non_reflechissant;
	public $anti_deflagration;
//Plancher
	public $substrat_plancher_id;
	public $substrat_plancher;
	public $fini_plancher_id;
	public $fini_plancher;
	public $antiderapant;
	public $antistatique;
	public $impermeable;
	public $sureleve;
	public $capacite_portante_id;
	public $capacite_portante;
	public $planeite_id;
	public $planeite;
//Murs et plinthes
	public $substrat_mur_id;
	public $substrat_mur;
	public $fini_mur_id;
	public $fini_mur;
	public $fond_clouage;
	public $type_plinthe_id;
	public $type_plinthe;
	public $fini_plinthe_id;
	public $fini_plinthe;
//Plafonds	
	public $substrat_plafond_id;
	public $substrat_plafond;
	public $fini_plafond_id;
	public $fini_plafond;


	public static function getChampsSaisieMetaData()
    {

        	$return_array = array(
        
            'regroupements' => array(
        		1   => array(
                    'titre' => txt('Dimensions', false),
                    'data'  =>  array(
                        'superficie_nette_totale' => array(
                            'classe'        => '',
                            'table'      	=> '',
                            'champ_id'      => 'superficie_nette_totale',
                            'type'          => 'input',
                            'format'        => 'float',
                            'titre'         => txt('Superficie nette totale', false),
                            'css_editeur'   => 'xxlarge',
                            'unite'         => 'm²'
                        ),
                        'largeur_min' => array(
                            'classe'        => '',
                            'table'      	=> '',
                            'champ_id'      => 'largeur_min',
                            'type'          => 'input',
                            'format'        => 'float',
                            'titre'         => txt('Largeur min.', false),
                            'css_editeur'   => 'xxlarge',
                            'unite'         => 'm'
                        ),
                        'profondeur_min' => array(
                            'classe'        => '',
                            'table'      	=> '',
                            'champ_id'      => 'profondeur_min',
                            'type'          => 'input',
                            'format'        => 'float',
                            'titre'         => txt('Profondeur min.', false),
                            'css_editeur'   => 'xxlarge',
                            'unite'         => 'm'
                        ),
                        'hauteur_min' => array(
                            'classe'        => '',
                            'table'      	=> '',
                            'champ_id'      => 'hauteur_min',
                            'type'          => 'input',
                            'format'        => 'float',
                            'titre'         => txt('Hauteur min.', false),
                            'css_editeur'   => 'xxlarge',
                            'unite'         => 'm'
                        ),
                        'superficie_nette_dessinee' => array(
                            'classe'        => '',
                            'table'      	=> '',
                            'champ_id'      => 'superficie_nette_dessinee',
                            'type'          => 'input',
                            'format'        => 'float',
                            'titre'         => txt('Superficie nette dessinée', false),
                            'css_editeur'   => 'xxlarge',
                            'unite'         => 'm²'
                        ),
                        'superficie_nette_sous_espace' => array(
                            'classe'        => '',
                            'table'      	=> '',
                            'champ_id'      => 'superficie_nette_sous_espace',
                            'type'          => 'input',
                            'format'        => 'float',
                            'titre'         => txt('Superficie nette du sous-espace', false),
                            'css_editeur'   => 'xxlarge',
                            'unite'         => 'm²'
                        ),
                    )
                ),      
        		2   => array(
                    'titre' => txt('Usage', false),
                    'data'  =>  array(
                        'espace_type' => array(
                            'classe'        => 'EspacesTypes',
                            'table'      	=> 'espaces_types',
                            'champ_id'      => 'espace_type_id',
                            'type'          => 'select',
                            'format'        => 'int',
                            'titre'         => txt('Type d\'espace', false),
                            'css_editeur'   => 'xxlarge'
                        ),
                        'frequentation' => array(
                            'classe'        => 'Frequentations',
                            'table'      	=> 'frequentations',
                            'champ_id'      => 'frequentation_id',
                            'type'          => 'select',
                            'format'        => 'int',
                            'titre'         => txt('Type d\'usager', false),
                            'css_editeur'   => 'xxlarge'
                        ),
                        'presence' => array(
                            'classe'        => 'Presences',
                            'table'      	=> 'presences',
                            'champ_id'      => 'presence_id',
                            'type'          => 'select',
                            'format'        => 'int',
                            'titre'         => txt('Horaire normal d\'utilisation', false),
                            'css_editeur'   => 'xxlarge'
                        ),
                        'nombre_usager_jour' => array(
                            'classe'        => '',
                            'table'      	=> '',
                            'champ_id'      => 'nombre_usager_jour',
                            'type'          => 'input',
                            'format'        => 'int',
                            'titre'         => txt('Nombre d\'usagers (jour)', false),
                            'css_editeur'   => 'xxlarge'
                        ),
                        'nombre_usager_soir' => array(
                            'classe'        => '',
                            'table'      	=> '',
                            'champ_id'      => 'nombre_usager_soir',
                            'type'          => 'input',
                            'format'        => 'int',
                            'titre'         => txt('Nombre d\'usagers (soir)', false),
                            'css_editeur'   => 'xxlarge'
                        ),
                        'nombre_usager_nuit' => array(
                            'classe'        => '',
                            'table'      	=> '',
                            'champ_id'      => 'nombre_usager_nuit',
                            'type'          => 'input',
                            'format'        => 'int',
                            'titre'         => txt('Nombre d\'usagers (nuit)', false),
                            'css_editeur'   => 'xxlarge'
                        ),
                        'acces_special_requis' => array(
                            'classe'        => '',
                            'table'      	=> '',
                            'champ_id'      => 'acces_special_requis',
                            'type'          => 'checkbox',
                            'format'        => 'int',
                            'titre'         => txt('Acces spécial requis', false),
                            'css_editeur'   => 'xxlarge'
                        ),
                    )
                ),
				3   => array(
                    'titre' => txt('Ambiance requise', false),
                    'data'  =>  array(
					    'calme' => array(
                            'classe'        => '',
                            'table'      	=> '',
                            'champ_id'      => 'calme',
                            'type'          => 'checkbox',
                            'format'        => 'int',
                            'titre'         => txt('Calme', false),
                            'css_editeur'   => 'xxlarge'
                        ),					    
						'chaleureuse' => array(
                            'classe'        => '',
                            'table'      	=> '',
                            'champ_id'      => 'chaleureuse',
                            'type'          => 'checkbox',
                            'format'        => 'int',
                            'titre'         => txt('Chaleureuse', false),
                            'css_editeur'   => 'xxlarge'
                        ),					    
						'reduction_stimuli_exterieur' => array(
                            'classe'        => '',
                            'table'      	=> '',
                            'champ_id'      => 'reduction_stimuli_exterieur',
                            'type'          => 'checkbox',
                            'format'        => 'int',
                            'titre'         => txt('Réduction des stimulis exterieurs', false),
                            'css_editeur'   => 'xxlarge'
                        ),					    
						'insonorisation' => array(
                            'classe'        => '',
                            'table'      	=> '',
                            'champ_id'      => 'insonorisation',
                            'type'          => 'checkbox',
                            'format'        => 'int',
                            'titre'         => txt('Insonorisation', false),
                            'css_editeur'   => 'xxlarge'
                        ),
                        'indice_transmission_son' => array(
                            'classe'        => 'IndicesTransmissionsSonsTypes',
                            'table'      	=> 'indices_transmissions_sons_types',
                            'champ_id'      => 'indice_transmission_son_id',
                            'type'          => 'select',
                            'format'        => 'int',
                            'titre'         => txt('Indice de transmission du son (ITS)', false),
                            'css_editeur'   => 'xxlarge'
                        ),
                    )
                ),	
                     4  => array(
                    'titre' => txt('Particularités', false),
                    'data'  =>  array(
 
                        'local_classification_grade' => array(
                            'classe'      	=> 'LocauxClassifications',
                            'table'      	=> 'locaux_classifications',
                            'champ_id'      => 'local_classification_grade_id',
                            'type'          => 'select',
                            'format'        => 'int',
                            'titre'         => txt('Classification du local (grade)', false),
                            'css_editeur'   => 'xxlarge',
                        ),
                        'niveau_confinement' => array(
                            'classe'        => 'NiveauxConfinements',
                            'table'      	=> 'niveaux_confinements',
                            'champ_id'      => 'niveau_confinement_id',
                            'type'          => 'select',
                            'format'        => 'int',
                            'titre'         => txt('Niveau de confinement', false),
                            'css_editeur'   => 'xxlarge'
                        ),
                        'protection_rx' => array(
                            'classe'        => '',
                            'table'      	=> '',
                            'champ_id'      => 'protection_rx',
                            'type'          => 'checkbox',
                            'format'        => 'int',
                            'titre'         => txt('Protection RX', false),
                            'css_editeur'   => 'xxlarge'
                        ),
                        'substance_nucleaire' => array(
                            'classe'        => '',
                            'table'      	=> '',
                            'champ_id'      => 'substance_nucleaire',
                            'type'          => 'checkbox',
                            'format'        => 'int',
                            'titre'         => txt('Substances nucléaires', false),
                            'css_editeur'   => 'xxlarge'
                        ),
                        'blindage_magnetique' => array(
                            'classe'        => '',
                            'table'      	=> '',
                            'champ_id'      => 'blindage_magnetique',
                            'type'          => 'checkbox',
                            'format'        => 'int',
                            'titre'         => txt('Blindage magnétique', false),
                            'css_editeur'   => 'xxlarge'
                        ),
                        'sensibilite_variation_temperature' => array(
                            'classe'        => '',
                            'table'      	=> '',
                            'champ_id'      => 'sensibilite_variation_temperature',
                            'type'          => 'checkbox',
                            'format'        => 'int',
                            'titre'         => txt('Sensibilité aux variations de température', false),
                            'css_editeur'   => 'xxlarge'
                        ),
                        'milieu_sterile' => array(
                            'classe'        => '',
                            'table'      	=> '',
                            'champ_id'      => 'milieu_sterile',
                            'type'          => 'checkbox',
                            'format'        => 'int',
                            'titre'         => txt('Milieu stérile ou infectieux', false),
                            'css_editeur'   => 'xxlarge'
                        ),
						'milieu_humide' => array(
                            'classe'        => '',
                            'table'      	=> '',
                            'champ_id'      => 'milieu_humide',
                            'type'          => 'checkbox',
                            'format'        => 'int',
                            'titre'         => txt('Milieu humide', false),
                            'css_editeur'   => 'xxlarge'
                        ),
						'etancheite' => array(
                            'classe'        => '',
                            'table'      	=> '',
                            'champ_id'      => 'etancheite',
                            'type'          => 'checkbox',
                            'format'        => 'int',
                            'titre'         => txt('Étancheité', false),
                            'css_editeur'   => 'xxlarge'
                        ),
						'sante_mentale' => array(
                            'classe'        => '',
                            'table'      	=> '',
                            'champ_id'      => 'sante_mentale',
                            'type'          => 'checkbox',
                            'format'        => 'int',
                            'titre'         => txt('Santé mentale', false),
                            'css_editeur'   => 'xxlarge'
                        ),
						'materiau_non_reflechissant' => array(
                            'classe'        => '',
                            'table'      	=> '',
                            'champ_id'      => 'materiau_non_reflechissant',
                            'type'          => 'checkbox',
                            'format'        => 'int',
                            'titre'         => txt('Matériaux non réfléchissants (laser)', false),
                            'css_editeur'   => 'xxlarge'
                        ),
						'antideflagration' => array(
                            'classe'        => '',
                            'table'      	=> '',
                            'champ_id'      => 'antideflagration',
                            'type'          => 'checkbox',
                            'format'        => 'int',
                            'titre'         => txt('Antidéflagration', false),
                            'css_editeur'   => 'xxlarge'
                        ),
                        'vibration' => array(
                            'classe'        => 'VibrationsTypes',
                            'table'      	=> 'vibrations_types',
                            'champ_id'      => 'vibration_id',
                            'type'          => 'select',
                            'format'        => 'int',
                            'titre'         => txt('Sensibilité aux vibrations', false),
                            'css_editeur'   => 'xxlarge'
                        ),
                        'tolerance_mouvement' => array(
                            'classe'        => 'TolerancesMouvementsTypes',
                            'table'      	=> 'tolerances_mouvements_types',
                            'champ_id'      => 'tolerance_mouvement_id',
                            'type'          => 'select',
                            'format'        => 'int',
                            'titre'         => txt('Tolérance aux mouvements', false),
                            'css_editeur'   => 'xxlarge'
                        ),
                        'transport_pneumatique' => array(
                            'classe'        => '',
                            'table'      	=> '',
                            'champ_id'      => 'transport_pneumatique',
                            'type'          => 'checkbox',
                            'format'        => 'int',
                            'titre'         => txt('Transport pneumatique', false),
                            'css_editeur'   => 'xxlarge'
                        ),
                        'matiere_dangeureuse' => array(
                            'classe'        => '',
                            'table'      	=> '',
                            'champ_id'      => 'matiere_dangeureuse',
                            'type'          => 'checkbox',
                            'format'        => 'int',
                            'titre'         => txt('Matières dangeureuses', false),
                            'css_editeur'   => 'xxlarge'
                        ),
                        'resistance_feu' => array(
                            'classe'      	=> 'LocauxResistanceFeux',
                            'table'      	=> 'locaux_resistance_feux',
                            'champ_id'      => 'resistance_feu_id',
                            'type'          => 'select',
                            'format'        => 'int',
                            'titre'         => txt('Résistance au feu', false),
                            'css_editeur'   => 'xxlarge',
                        ),
					),
                ),
                    5   => array(
                    'titre' => txt('Commentaires', false),
                    'data'  =>  array(
                        'commentaires' => array(
                            'classe'        => '',
                            'table'      	=> '',
                            'champ_id'      => 'commentaires',
                            'type'          => 'textarea',
                            'format'        => 'text',
                            'titre'      	=> txt('Commentaires', false),
                            'css_editeur'   => 'xxlarge',
                            'hide_titre'    => 1
                        ),
                    )),
/*					6   => array(
                    'titre' => txt('Protecteurs', false),
                    'data'  =>  array(
                        'revetement_mural' => array(
                            'classe'        => 'RevetementsMuralsTypes',
                            'table'      	=> 'revetements_murals_types',
                            'champ_id'      => 'revetement_mural_id',
                            'type'          => 'select',
                            'format'        => 'int',
                            'titre'         => txt('Revêtement mural', false),
                            'css_editeur'   => 'xxlarge'
                        ),					    
						'pare_choc_haut' => array(
                            'classe'        => '',
                            'table'      	=> '',
                            'champ_id'      => 'pare_choc_haut',
                            'type'          => 'checkbox',
                            'format'        => 'int',
                            'titre'         => txt('Pare-choc haut', false),
                            'css_editeur'   => 'xxlarge'
                        ),					    
						'pare_choc_bas' => array(
                            'classe'        => '',
                            'table'      	=> '',
                            'champ_id'      => 'pare_choc_bas',
                            'type'          => 'checkbox',
                            'format'        => 'int',
                            'titre'         => txt('Pare-choc bas', false),
                            'css_editeur'   => 'xxlarge'
                        ),					    
						'protecteur_coin' => array(
                            'classe'        => '',
                            'table'      	=> '',
                            'champ_id'      => 'protecteur_coin',
                            'type'          => 'checkbox',
                            'format'        => 'int',
                            'titre'         => txt('Protecteur de coin', false),
                            'css_editeur'   => 'xxlarge'
                        ),					    
						'main_courante' => array(
                            'classe'        => '',
                            'table'      	=> '',
                            'champ_id'      => 'main_courante',
                            'type'          => 'checkbox',
                            'format'        => 'int',
                            'titre'         => txt('Main courante', false),
                            'css_editeur'   => 'xxlarge'
                        ),
                    )
                ),
					7   => array(
                    'titre' => txt('Fenestration', false),
                    'data'  =>  array(
						'fenestration_interieure' => array(
                            'classe'        => '',
                            'table'      	=> '',
                            'champ_id'      => 'fenestration_interieure',
                            'type'          => 'checkbox',
                            'format'        => 'int',
                            'titre'         => txt('Fenestration intérieure', false),
                            'css_editeur'   => 'xxlarge'
                        ),                        
						'verre_interieur' => array(
                            'classe'        => 'VerresInterieursTypes',
                            'table'      	=> 'verres_interieurs_types',
                            'champ_id'      => 'verre_interieur_id',
                            'type'          => 'select',
                            'format'        => 'int',
                            'titre'         => txt('Type de verre intérieur', false),
                            'css_editeur'   => 'xxlarge'
                        ),
						'fenestration_exterieure' => array(
                            'classe'        => '',
                            'table'      	=> '',
                            'champ_id'      => 'fenestration_exterieure',
                            'type'          => 'checkbox',
                            'format'        => 'int',
                            'titre'         => txt('Fenestration extérieure', false),
                            'css_editeur'   => 'xxlarge'
                        ),                        
						'verre_exterieur' => array(
                            'classe'        => 'VerresExterieursTypes',
                            'table'      	=> 'verres_exterieurs_types',
                            'champ_id'      => 'verre_exterieur_id',
                            'type'          => 'select',
                            'format'        => 'int',
                            'titre'         => txt('Type de verre extérieur', false),
                            'css_editeur'   => 'xxlarge'
                        ),							
                    )
                ),		
					8   => array(
                    'titre' => txt('Planchers', false),
                    'data'  =>  array(
                        'substrat_plancher' => array(
                            'classe'      	=> 'LocauxSubstrats',
                            'table'      	=> 'locaux_substrats',
                            'champ_id'      => 'substrat_plancher_id',
                            'type'          => 'select',
                            'format'        => 'int',
                            'titre'         => txt('Substrat de plancher', false),
                            'css_editeur'   => 'xxlarge',
                        ),
                        'fini_plancher' => array(
                            'classe'      	=> 'LocauxFinis',
                            'table'      	=> 'locaux_finis',
                            'champ_id'      => 'fini_plancher_id',
                            'type'          => 'select',
                            'format'        => 'int',
                            'titre'         => txt('Fini de plancher', false),
                            'css_editeur'   => 'xxlarge',
                        ),					    
						'antiderapant' => array(
                            'classe'        => '',
                            'table'      	=> '',
                            'champ_id'      => 'antiderapant',
                            'type'          => 'checkbox',
                            'format'        => 'int',
                            'titre'         => txt('Antidérapant', false),
                            'css_editeur'   => 'xxlarge'
                        ),					    
						'antistatique' => array(
                            'classe'        => '',
                            'table'      	=> '',
                            'champ_id'      => 'antistatique',
                            'type'          => 'checkbox',
                            'format'        => 'int',
                            'titre'         => txt('Antistatique', false),
                            'css_editeur'   => 'xxlarge'
                        ),					    
						'impermeable' => array(
                            'classe'        => '',
                            'table'      	=> '',
                            'champ_id'      => 'impermeable',
                            'type'          => 'checkbox',
                            'format'        => 'int',
                            'titre'         => txt('Imperméable', false),
                            'css_editeur'   => 'xxlarge'
                        ),						    
						'sureleve' => array(
                            'classe'        => '',
                            'table'      	=> '',
                            'champ_id'      => 'sureleve',
                            'type'          => 'checkbox',
                            'format'        => 'int',
                            'titre'         => txt('Surélevé', false),
                            'css_editeur'   => 'xxlarge'
                        ),	
                        'capacite_portante' => array(
                            'classe'        => 'CapacitesPortantesTypes',
                            'table'      	=> 'capacites_portantes_types',
                            'champ_id'      => 'capacite_portante_id',
                            'type'          => 'select',
                            'format'        => 'int',
                            'titre'         => txt('Capacités portantes', false),
                            'css_editeur'   => 'xxlarge'
                        ),
                        'planeite' => array(
                            'classe'        => 'PlaneitesTypes',
                            'table'      	=> 'planeites_types',
                            'champ_id'      => 'planeite_id',
                            'type'          => 'select',
                            'format'        => 'int',
                            'titre'         => txt('Planéité', false),
                            'css_editeur'   => 'xxlarge'
                        ),
                    )
                ),
					9   => array(
                    'titre' => txt('Murs', false),
                    'data'  =>  array(	
                        'substrat_mur' => array(
                            'classe'      	=> 'LocauxSubstrats',
                            'table'      	=> 'locaux_substrats',
                            'champ_id'      => 'substrat_mur_id',
                            'type'          => 'select',
                            'format'        => 'int',
                            'titre'         => txt('Substrat de mur', false),
                            'css_editeur'   => 'xxlarge',
                        ),
                        'fini_mur' => array(
                            'classe'      	=> 'LocauxFinis',
                            'table'      	=> 'locaux_finis',
                            'champ_id'      => 'fini_mur_id',
                            'type'          => 'select',
                            'format'        => 'int',
                            'titre'         => txt('Fini de mur', false),
                            'css_editeur'   => 'xxlarge',
                        ),					    
						'fond_clouage' => array(
                            'classe'        => '',
                            'table'      	=> '',
                            'champ_id'      => 'fond_clouage',
                            'type'          => 'checkbox',
                            'format'        => 'int',
                            'titre'         => txt('Fond de clouage', false),
                            'css_editeur'   => 'xxlarge'
                        ),	
                    )
                ),
					10   => array(
                    'titre' => txt('Plafonds', false),
                    'data'  =>  array(
					    
						'substrat_plafond' => array(
                            'classe'      	=> 'LocauxSubstrats',
                            'table'      	=> 'locaux_substrats',
                            'champ_id'      => 'substrat_plafond_id',
                            'type'          => 'select',
                            'format'        => 'int',
                            'titre'         => txt('Substrat de plafond', false),
                            'css_editeur'   => 'xxlarge',
                        ),
                        'fini_plafond' => array(
                            'classe'      	=> 'LocauxFinis',
                            'table'      	=> 'locaux_finis',
                            'champ_id'      => 'fini_plafond_id',
                            'type'          => 'select',
                            'format'        => 'int',
                            'titre'         => txt('Fini de plafond', false),
                            'css_editeur'   => 'xxlarge',
                        ),							
                    )
                ),	
               		11   => array(
                    'titre' => txt('Plinthes', false),
                    'data'  =>  array(
                        'type_plinthe' => array(
                            'classe'      	=> 'LocauxPlinthes',
                            'table'      	=> 'locaux_plinthes',
                            'champ_id'      => 'type_plinthe_id',
                            'type'          => 'select',
                            'format'        => 'int',
                            'titre'         => txt('Type de plinthe', false),
                            'css_editeur'   => 'xxlarge',
                        ),
                        'fini_plinthe' => array(
                            'classe'      	=> 'LocauxFinis',
                            'table'      	=> 'locaux_finis',
                            'champ_id'      => 'fini_plinthe_id',
                            'type'          => 'select',
                            'format'        => 'int',
                            'titre'         => txt('Fini de plinthe', false),
                            'css_editeur'   => 'xxlarge',
                        ),
                    )
                ),	*/
            ),
        );
		return parent::parseMetaData($return_array);
	}

	public static function getListe($filter = array(), $orderBy = 'id', $extra_data = null)
	{
		return parent::getListe($filter, $orderBy, $extra_data);
	}
    
    public static function getFormHTML($fiche_id, $liste_data = null, $forced_liste = null)
	{
        return parent::getUniqueFormHTML($fiche_id, $liste_data);
    }
    
    public function getEditorHTML($iteration = 0, $only_champs_denormalises = false)
	{
?>
        <style>
            #editables label {
                width: 275px !important;
            }
            .edit_popupContent section > div {
                width: 690px !important;
            }
            #editables input[type=checkbox] {
                position: relative;
                top: 1px;
            }
        </style>
<?
        parent::getEditorHTML($iteration);
    }
    
    public static function getFicheHTML($fiche, $liste = null, $nb_archives = 0, $can_edit = false, $projet_courant_id = null, $gabarit = null)
	{
?>
        <style>
            .blocsFiche section label {
                width: 260px !important;
            }
        </style>
<?
        if(isset($liste) && is_array($liste))
        {
            $liste_tmp = array_values($liste);
            if(isset($liste_tmp[0]))
            {
                $obj = $liste_tmp[0];
            }
            else
            {
                $currentClassName = get_called_class();
                $obj = $currentClassName::getOne($fiche->id);
            }
        }
        else
        {
            $currentClassName = get_called_class();
            $obj = $currentClassName::getOne($fiche->id);
        }
        
        parent::getUniqueFicheHTML($fiche, $obj, $nb_archives, $can_edit, $projet_courant_id);
        
        parent::getAutoFicheHtml($fiche->id, $obj);
    }
    
    public static function getDiffArchiveHTML($fiche_id, $liste, $last_liste, $token_archive, $last_token_archive)
	{
?>
        <style>
            .blocsFiche section label {
                width: 260px !important;
            }
        </style>
<?
        $liste = array_values($liste);
        $last_liste = array_values($last_liste);
        if(isset($liste[0]))
        {
            $obj = $liste[0];
        }
        
        if(isset($last_liste[0]))
        {
            $last_obj = $last_liste[0];
        }
        else
        {
            $last_obj = new self(0);
        }
        
?>
        <h2 class="blocsFicheTitre" style="height: 30px;">
            <?=txt(self::TITRE)?></h2>
<?
        
        parent::getAutoDiffArchiveHtml($fiche_id, $obj, $last_obj, $token_archive, $last_token_archive);
    }
    
    public function getFiltreHTML()
    {
?>
        <fieldset class="noMinHeight">
<?
            $this->getEditorHTML();
?>
        </fieldset>
<?
    }
}

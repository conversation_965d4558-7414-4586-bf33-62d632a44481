<?
class Pavage extends FicheFormWrapper {
    private static $CLASS_NAME='Pavage';
    const SQL_TABLE_NAME='pavage';
    const TITRE='Pavage';
    public static $AUTO=true;
    public $id;
    public $fiche_id;
    public $element_id;     
    public $element; 
    public $radie;     
    public $epaisseur;     
    public $compaction_id;     
    public $compaction; 
    public $date_dinstallation;  
	public $qte_requise;
	public $quantite;    
    public $commentaires;     

    public static function getChampsSaisieMetaData()
    {
        return array(

            'element' => array(

                'classe'      	=> 'PavageMateriaux',
                'table'      	=> 'pavage_materiaux',
                'champ_id'      => 'element_id',
                'type'          => 'select',
                'format'        => 'int',
                'titre'         => txt('Élément', false),
                'css_editeur'   => 'medium',
                'obligatoire'	=> 1,

            ),
            'radie' => array(

                'classe'      	=> '',
                'table'      	=> '',
                'champ_id'      => 'radie',
                'type'          => 'input',
                'format'        => 'float',
                'titre'         => txt('Radié', false),
                'css_editeur'   => 'small',

            ),
            'epaisseur' => array(

                'classe'      	=> '',
                'table'      	=> '',
                'champ_id'      => 'epaisseur',
                'type'          => 'input',
                'format'        => 'float',
                'titre'         => txt('Épaisseur', false),
                'css_editeur'   => 'small',

            ),
            'compaction' => array(

                'classe'      	=> 'CompacitionTypes',
                'table'      	=> 'compacition_types',
                'champ_id'      => 'compaction_id',
                'type'          => 'select',
                'format'        => 'int',
                'titre'         => txt('Compaction', false),
                'css_editeur'   => 'medium',

            ),
            'date_dinstallation' => array(

                'classe'      	=> '',
                'table'      	=> '',
                'champ_id'      => 'date_dinstallation',
                'type'          => 'input',
                'format'        => 'date',
                'titre'         => txt('Date d\'installation', false),
                'css_editeur'   => 'small',

            ),
            
            'qte_requise' => array(
				'classe'      	=> '',
				'table'      	=> '',
				'champ_id'      => 'qte_requise',
				'type'          => 'input',
				'format'        => 'int',
				'titre'         => txt('Quantité', false),
				'css_editeur'   => 'tiny'
			),
			
            'commentaires' => array(

                'classe'      	=> '',
                'table'      	=> '',
                'champ_id'      => 'commentaires',
                'type'          => 'input',
                'format'        => 'text',
                'titre'         => txt('Commentaires', false),
                'css_editeur'   => 'xxlarge',

            ),
        );
    }
}

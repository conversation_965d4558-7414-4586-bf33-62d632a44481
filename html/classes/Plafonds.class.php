<?php

class Plafonds extends FicheFormWrapper {
	private static $CLASS_NAME='Plafonds';
	const SQL_TABLE_NAME='plafonds';
	const TITRE='Plafonds';
	public $id;
	public $fiche_id;
	public $plafond_type_id;
	public $quantite_id;
	public $plafond_type;
	public $quantite;
	public $commentaires;
    
    public static function getChampsSaisieMetaData()
    {
        return array(
            
            'plafond_type' => array(
                'classe'      	=> 'PlafondsTypes',
                'table'      	=> 'plafonds_types',
                'champ_id'      => 'plafond_type_id',
                'type'          => 'select',
                'format'        => 'int',
                'titre'         => txt('Type de plafond', false),
                'css_editeur'   => 'xxlarge',
                'obligatoire'	=> 1,
            ),

            'qte_requise' => array(
                'classe'      	=> '',
                'table'      	=> '',
                'champ_id'      => 'qte_requise',
                'type'          => 'input',
                'format'        => 'int',
                'titre'         => txt('Qté', false),
                'css_editeur'   => 'tiny'
            ),
            
            'commentaires' => array(
				'table'      	=> '',
				'champ_id'      => 'commentaires',
				'type'          => 'input',
                'format'        => 'text',
                'titre'         => txt('Commentaires', false),
                'css_editeur'   => 'xxlarge'
            ),
            
        );
    }

	public static function getListe($filter = array(), $orderBy = 'ordre', $extra_data = array())
	{
        $extra_data = array(
            'plafond_type' => array('table' => 'plafonds_types'),
        );
        return parent::getListe($filter, $orderBy, $extra_data);
    }
}

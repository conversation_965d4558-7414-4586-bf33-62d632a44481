<?
class PlomberiesPompes extends FicheFormWrapper {
    private static $CLASS_NAME='PlomberiesPompes';
    const SQL_TABLE_NAME='plomberies_pompes';
    const TITRE='Plomberie - Pompe';
    public static $AUTO=true;
    public static $TYPE_FORM='UNIQUE';
    public $id;
    public $fiche_id;
    public $type_de_pompe_drop_down_menu_id;     
    public $type_de_pompe_drop_down_menu; 
    public $debit_maximal;     
    public $pression_maximale;     
    public $succion_maximale; 
    public $commentaire;          

    public static function getChampsSaisieMetaData()
    {
        $return_array = array(

            'type_de_pompe_drop_down_menu' => array(

                'classe'      	=> 'PompesTypes',
                'table'      	=> 'pompes_types',
                'champ_id'      => 'type_de_pompe_drop_down_menu_id',
                'type'          => 'select',
                'format'        => 'int',
                'titre'         => txt('Type de pompe', false),
                'css_editeur'   => 'xxlarge',

            ),
            'debit_maximal' => array(

                'classe'      	=> '',
                'table'      	=> '',
                'champ_id'      => 'debit_maximal',
                'type'          => 'input',
                'format'        => 'float',
                'titre'         => txt('Débit maximal', false),
                'css_editeur'   => 'xxlarge',
        'unite'         => 'l/s',

            ),
            'pression_maximale' => array(

                'classe'      	=> '',
                'table'      	=> '',
                'champ_id'      => 'pression_maximale',
                'type'          => 'input',
                'format'        => 'float',
                'titre'         => txt('Pression maximale', false),
                'css_editeur'   => 'xxlarge',
        'unite'         => 'kPa',

            ),
            'succion_maximale' => array(

                'classe'      	=> '',
                'table'      	=> '',
                'champ_id'      => 'succion_maximale',
                'type'          => 'input',
                'format'        => 'float',
                'titre'         => txt('Succion maximale', false),
                'css_editeur'   => 'xxlarge',
        'unite'         => 'kPa',

            ),
            'commentaire' => array(
                'classe'      	=> '',
                'table'      	=> '',
                'champ_id'      => 'commentaire',
                'type'          => 'textarea',
                'format'        => 'text',
                'titre'         => txt('Commentaires', false),
                'css_editeur'   => 'xxlarge'
            ),

		);
		return parent::parseMetaData($return_array);
	}

	public static function getListe($filter = array(), $orderBy = 'id', $extra_data = null)
    {
        return parent::getListe($filter, $orderBy, $extra_data);
    }

    public static function getFormHTML($fiche_id, $liste_data = null, $forced_liste = null)
    {
        return parent::getUniqueFormHTML($fiche_id, $liste_data);
    }

    public function getEditorHTML($iteration = 0, $only_champs_denormalises = false)
    {
?>
        <style>
            #editables label {
                width: 275px !important;
            }
            .edit_popupContent section > div {
                width: 690px !important;
            }
            #editables input[type=checkbox] {
                position: relative;
                top: 1px;
            }
        </style>
<?
        parent::getEditorHTML($iteration);
    }

    public static function getFicheHTML($fiche, $liste = null, $nb_archives = 0, $can_edit = false, $projet_courant_id = null, $gabarit = null)
    {
?>
        <style>
            .blocsFiche section label {
                width: 260px !important;
            }
        </style>
<?
        if(isset($liste) && is_array($liste))
        {
            $liste_tmp = array_values($liste);
            if(isset($liste_tmp[0]))
            {
                $obj = $liste_tmp[0];
            }
            else
            {
                $currentClassName = get_called_class();
                $obj = $currentClassName::getOne($fiche->id);
            }
        }
        else
        {
            $currentClassName = get_called_class();
            $obj = $currentClassName::getOne($fiche->id);
        }

        parent::getUniqueFicheHTML($fiche, $obj, $nb_archives, $can_edit, $projet_courant_id);

        parent::getAutoFicheHtml($fiche->id, $obj);
    }

    public static function getDiffArchiveHTML($fiche_id, $liste, $last_liste, $token_archive, $last_token_archive)
    {
?>
        <style>
            .blocsFiche section label {
                width: 260px !important;
            }
        </style>
<?
        $liste = array_values($liste);
        $last_liste = array_values($last_liste);
        if(isset($liste[0]))
        {
            $obj = $liste[0];
        }

        if(isset($last_liste[0]))
        {
            $last_obj = $last_liste[0];
        }
        else
        {
            $last_obj = new self(0);
        }

?>
        <h2 class="blocsFicheTitre" style="height: 30px;">
            <?=txt(self::TITRE)?></h2>
<?

        parent::getAutoDiffArchiveHtml($fiche_id, $obj, $last_obj, $token_archive, $last_token_archive);
    }

    public function getFiltreHTML()
    {
?>
        <fieldset class="noMinHeight">
<?
            $this->getEditorHTML();
?>
        </fieldset>
<?
    }
}

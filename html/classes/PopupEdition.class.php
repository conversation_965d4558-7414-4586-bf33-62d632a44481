<?php

class PopupEdition extends DbRow {
    public static function traitePostData($id, $table, $className)
    {
        global $db, $ServerUrl, $CodeClient, $TablesCategoriesPermissions, $tables_for_listes, $validColumns, $NodeWebServiceServerUrl;

        $new_password = '';

        $imagesData = [];
        if (isset($_FILES['images']['tmp_name'])) {
            foreach ($_FILES['images']['tmp_name'] as $i => $tmp_name) {
                if (is_uploaded_file($tmp_name)) {
                    $imagesData[] = [
                        'name' => $_FILES['images']['name'][$i],
                        'type' => $_FILES['images']['type'][$i],
                        'tmp_name' => $_FILES['images']['tmp_name'][$i],
                        'error' => $_FILES['images']['error'][$i],
                        'size' => $_FILES['images']['size'][$i],
                    ];
                }
            }
        }
        foreach ($imagesData as $imageData) {
            $violations = DocumentsListesElements::validateFile($imageData);
            if (count($violations) > 0) {
                foreach ($violations as $violation) {
                    setErreur($violation->getMessage());
                }
            }
        }

        if (isset($_GET['mode']) && $_GET['mode'] == 'cloner_fiche') {
            db::instance()->autocommit(false);

            $_POST['fiche_id'] = $id;
            $new_id = Fiches::cloner($_POST);

            db::instance()->commit();

            if (nbErreurs() == 0) {
                if (isset($_POST['projet_id']) && intval($_POST['projet_id']) <> getProjetId()) {
                    ?>
                    <script>
						closePopup();
                    </script>
                    <?php
                } else {
                    ?>
                    <script>
						parent.window.location = '<?=$ServerUrl?>index.php?section=fiche&module=index&id=<?=$new_id?>';
						closePopup();
                    </script>
                    <?php
                }
            }
        } elseif (isset($_GET['formulaire'])) {
            $_POST['formulaire'] = $className;
            $_POST['fiche_id'] = intval($_GET['from_fiche']);

            db::instance()->autocommit(false);

            $className::save($_POST, true);

            self::traiteDocumentsMobiles();

            if (nbErreurs() == 0) {
                $formulaire = new Formulaires(Formulaires::getIdFromNom($className));

                if (isset($_SESSION['is_gabarit']) && $_SESSION['is_gabarit'] == 1) {
                    $gabarit = new Fiches($_POST['fiche_id']);
                    $fiches = $gabarit->getFichesAffectedByGabarit($formulaire->id);
                    foreach ($fiches as $fiche) {
                        $fiche->affecterGabarit($gabarit, $formulaire);
                    }
                }

                db::instance()->commit();

                $code_discipline = 'GEN';
                if (isset($formulaire->discipline_id) && $formulaire->discipline_id > 0) {
                    $discipline = new Discipline($formulaire->discipline_id);
                    $code_discipline = $discipline->code;
                } elseif ($className == 'ProprietesDinstallationEtDutilisation') {
                    $code_discipline = 'ENT';
                }

                if (isset($_GET['fromMobile'])) {
                    if ($_GET['fromMobile'] == 'equipements') {
                        header(
                            'Location: index.php?section=releveMobile&module=equipements&fiche_id_sel=' . intval(
                                $_GET['fiche_id_sel']
                            )
                        );
                    } else if ($_GET['fromMobile'] == 'inventaire') {
                        header(
                            'Location: index.php?section=releveMobile&module=inventaire&ligne_id_sel=' . intval(
                                $_GET['ligne_id_sel']
                            ) . (isset($_GET['inventaire_local_id']) ? '&inventaire_local_id=' . $_GET['inventaire_local_id'] : '')
                        );
                    }
                } elseif (isset($_GET['import_id'])) {
                    header(
                        'Location: index.php?section=fiche&module=comparaison&import_id=' . intval($_GET['import_id'])
                    );
                } else {
                    ?>
                    <script>
						function formulaireFerme(codeClient, usager_id, ouvrage_id, formulaire_id, fiche_id) {
                            <?
                            if (isset($NodeWebServiceServerUrl)) {
                            ?>
							const xhr = new XMLHttpRequest();
							xhr.addEventListener('loadend', () => {
								if (parent.$.fancybox) {
									window.parent.lastOpenedForm = "";
									parent.$.fancybox.close();
								}
							});
							xhr.open('POST', `<?=$NodeWebServiceServerUrl?>formulaire-ferme/${codeClient}/${usager_id}/${ouvrage_id}/${formulaire_id}/${fiche_id}`, true);
							xhr.send();
                            <?
                            } else {
                            ?>
							if (parent.$.fancybox) {
								window.parent.lastOpenedForm = "";
								parent.$.fancybox.close();
							}
                            <?
                            }
                            ?>
						}

						function closePopup() {
							formulaireFerme('<?=$CodeClient?>', <?=getIdUsager()?>, <?=getOuvrageId(
                            )?>, <?=Formulaires::getIdFromNom($_GET['formulaire'])?>, <?=intval($_GET['from_fiche'])?>);
						}

                        <?if (nbAvertissements() > 0) {?>

                        <?if (isset($_GET['from_coordination'])) {?>

                        <?avertissements_alert();?>
						parent.avertir();
						closePopup();

                        <?} else if (isset($_GET['from_gestion_portes'])) {?>

						parent.window.location.reload();

                        <?} else {?>

                        <?avertissements_alert();?>
						parent.reloadFiche(<?=intval($_GET['from_fiche'])?>, '<?=$code_discipline?>');
						closePopup();

                        <?}?>

                        <?} else {?>

                        <?if (isset($_GET['from_coordination']) || isset($_GET['from_gestion_portes'])) {?>

						parent.window.location.reload();

                        <?} elseif (nbAvertissements() > 0) {?>

                        <?avertissements_alert();?>
						parent.reloadFiche(<?=intval($_GET['from_fiche'])?>, '<?=$code_discipline?>');

                        <?} else {?>

						parent.reloadFiche(<?=intval($_GET['from_fiche'])?>, '<?=$code_discipline?>');

                        <?}?>
						closePopup();
                        <?}?>
                    </script>
                    <?php
                }
                exit;
            }
        } else {
            db::instance()->autocommit(false);

            $updateStatement = array();
            $insertStatementCol = array();
            $insertStatementVal = array();
            $data = array();
            if ($table == 'champs_ponctuels' && $_POST['type_saisie'] == 'comm') {
                $_POST['titre_bloc'] = 'Commentaire';
            }

            if ($table == 'procedures_entretiens_planifiees' && (isset($_POST['ignorer']) || isset($_POST['annuler']))) {
                if (isset($_POST['ignorer'])) {
                    $sql = "update procedures_entretiens_planifiees set ts_ignore = current_timestamp, usager_ignore_id = ?, ts_a_planifier = null, usager_a_planifier_id = null where id = ?";
                    db::instance()->query($sql, array(getIdUsager(), $id));
                }
                if (isset($_POST['annuler'])) {
                    $sql = "update procedures_entretiens_planifiees set ts_annule = current_timestamp, usager_annule_id = ? where id = ?";
                    db::instance()->query($sql, array(getIdUsager(), $id));
                }

                db::instance()->commit();
                ?>
                <script>
					parent.window.location.reload();
					parent.$.fancybox.close();
                </script>
                <?php
                exit;
            }

            $objAnciennesValeurs = null;
            if ($id > 0) {
                $objAnciennesValeurs = new $className($id);
            }

            $dataToValidate = $_POST;
            if (isset($_FILES)) {
                $dataToValidate = $dataToValidate + $_FILES;
            }
            $className::validate($dataToValidate, $id);

            foreach ($_POST as $colomnName => $value) {
                if (!db::instance()::tableColonneValide($colomnName)) {
                    continue;
                }

                if (is_array($value)) {
                    if ($colomnName == 'reglementation_niveau_element_id') {
                        foreach ($_POST['reglementation_niveau_element_id'] as $element_id) {
                            if (intval($element_id) > 0) {
                                $value = intval($element_id);
                            }
                        }
                    } else {
                        if (isset($value[0])) {
                            $value = $value[0];
                        } else {
                            continue;
                        }
                    }
                }

                $dataType = '';
                if (isset($validColumns['doc'][$table][$colomnName])) {
                    $dataType = $validColumns['doc'][$table][$colomnName];
                } else {
                    continue;
                }

                $addValue = true;

                if (in_array($table, array('procedures_entretiens', 'procedures_inspections'))) {
                    if ($colomnName == 'nomenclature_id' && !in_array($_POST['type'], array('generale'))) {
                        $addValue = false;
                    }

                    if ($colomnName == 'modele_id' && !in_array($_POST['type'], array('specifique'))) {
                        $addValue = false;
                    }

                    if ($colomnName == 'fiche_id' && !in_array($_POST['type'], array('privee', 'legale'))) {
                        $addValue = false;
                    }
                }

                if ($table == 'usagers') {
                    if ($id == getIdUsager()) {
                        if (!in_array($colomnName, ['password', 'password2'])) {
                            $addValue = false;
                        }
                    } else if (isMaster()) {
                        if (!in_array($colomnName, ['sexe', 'langue', 'prenom', 'nom', 'email'])) {
                            $addValue = false;
                        }
                    } else {
                        $addValue = false;
                    }
                }

                if ($addValue) {
                    if ($colomnName == 'password') {
                        if (isMaster() || ($id == getIdUsager())) {
                            if (dm_strlen($value) > 0) {
                                $random_salt = hash('sha512', uniqid(random_bytes(16), true));
                                $password = hash('sha512', strval($value) . $random_salt);

                                $updateStatement[] = '`password` = ?';
                                $insertStatementCol[] = '`password`';
                                $insertStatementVal[] = '?';
                                $data[] = $password;

                                $updateStatement[] = '`salt` = ?';
                                $insertStatementCol[] = '`salt`';
                                $insertStatementVal[] = '?';
                                $data[] = $random_salt;

                                $updateStatement[] = '`ts_temp_password` = null';
                                $insertStatementCol[] = '`ts_temp_password`';
                                $insertStatementVal[] = 'null';

                                $new_password = strval($value);
                            }
                        }
                    } elseif (dm_strlen($value) == 0) {
                        $updateStatement[] = '`' . $colomnName . '` = null';
                        $insertStatementCol[] = '`' . $colomnName . '`';
                        $insertStatementVal[] = 'null';
                    } else {
                        $updateStatement[] = '`' . $colomnName . '` = ?';
                        $insertStatementCol[] = '`' . $colomnName . '`';
                        $insertStatementVal[] = '?';

                        if ($dataType == 'int') {
                            $data[] = intval($value);
                        } elseif ($dataType == 'float') {
                            $data[] = floatval($value);
                        } elseif ($dataType == 'date') {
                            $data[] = strval($value);
                        } elseif ($dataType == 'varchar') {
                            $data[] = strval($value);
                        } elseif ($dataType == 'timestamp') {
                            $data[] = strval($value);
                        }
                    }
                }
            }

            if (isset($_POST['code_valeur_externe']) && count($_POST['code_valeur_externe']) > 0) {
                $form = new Formulaires(intval($_POST['formulaire_id']));
                $classNameTmp = $form->nom;
                $metas = $classNameTmp::getChampsSaisieMetaData();

                $champ_ids = array();
                foreach ($metas as $key => $meta) {
                    if ($meta['type'] == 'select' && !in_array(
                            $key,
                            array(
                                'quantite_existante',
                                'quantite',
                                'quantite_fantome',
                                'conformite_du_besoin_electricite',
                                'conformite_du_besoin_plomberie',
                                'conformite_du_besoin_mecanique',
                                'conformite_du_besoin_telecom',
                                'conformite_du_besoin_architecture',
                                'conformite_du_besoin_structure',
                                'conformite_du_besoin',
                                'equipement_electricite',
                                'equipement_plomberie'
                            )
                        )) {
                        $champ_ids[] = $meta['champ_id'];
                    }
                }

                foreach ($_POST['code_valeur_externe'] as $ordre => $code_valeur_externe) {
                    $code_valeur_interne = '';
                    foreach ($champ_ids as $i => $champ_id) {
                        $code_valeur_interne .= ($i > 0 ? '_' : '') . (isset($_POST[$champ_id][$ordre]) ? $_POST[$champ_id][$ordre] : 0);
                    }
                    $_POST['valeurs'][$ordre]['code_valeur_externe'] = $code_valeur_externe;
                    $_POST['valeurs'][$ordre]['code_valeur_interne'] = $code_valeur_interne;
                }

                if (isset($_POST['formulaire_id']) && intval(
                        $_POST['formulaire_id']
                    ) > 0 && isset($_POST['element_liste_id'])) {
                    $formulaire_tmp = new Formulaires(intval($_POST['formulaire_id']));
                    $className_tmp = $formulaire_tmp->nom;
                    $metas = $className_tmp::getChampsSaisieMetaData();

                    $champ_ids = array();
                    foreach ($metas as $key => $meta) {
                        if ($meta['type'] == 'select' && !in_array(
                                $key,
                                array(
                                    'quantite_existante',
                                    'quantite',
                                    'quantite_fantome',
                                    'conformite_du_besoin_electricite',
                                    'conformite_du_besoin_plomberie',
                                    'conformite_du_besoin_mecanique',
                                    'conformite_du_besoin_telecom',
                                    'conformite_du_besoin_architecture',
                                    'conformite_du_besoin_structure',
                                    'conformite_du_besoin',
                                    'equipement_electricite',
                                    'equipement_plomberie'
                                )
                            )) {
                            $champ_ids[] = $meta['champ_id'];
                        }
                    }

                    if (isset($_POST['element_liste_id']) && count($_POST['element_liste_id']) > 0) {
                        foreach ($_POST['element_liste_id'] as $ordre => $element_id) {
                            $code_valeur_interne = '';
                            foreach ($champ_ids as $i => $champ_id) {
                                $code_valeur_interne .= ($i > 0 ? '_' : '') . (isset($_POST[$champ_id][$ordre]) ? $_POST[$champ_id][$ordre] : 0);
                            }

                            $_POST['valeurs'][$ordre]['code_valeur_interne'] = $code_valeur_interne;
                            $_POST['valeurs'][$ordre]['id'] = $element_id;
                        }
                    }
                }
            }

            if (nbErreurs() == 0) {
                if (!isset($tables['master'][$table])) {
                    if ($table == 'fiches') {
                        $updateStatement[] = '`ts_modif` = current_timestamp';
                        $insertStatementCol[] = '`ts_ajout`';
                        $insertStatementVal[] = 'current_timestamp';

                        $updateStatement[] = '`usager_modif_id` = ?';
                        $insertStatementCol[] = '`usager_ajout_id`';
                        $insertStatementVal[] = '?';
                        $data[] = getIdUsager();

                        if ($id == 0) {
                            $insertStatementCol[] = '`ouvrage_id`';
                            $insertStatementVal[] = '?';
                            $data[] = getOuvrageId();
                        }
                    }

                    if ($table == 'fiches_statuts') {
                        $updateStatement[] = '`ouvrage_id` = ?';
                        $insertStatementCol[] = '`ouvrage_id`';
                        $insertStatementVal[] = '?';
                        $data[] = getOuvrageId();
                    }

                    if ($table == 'projets') {
                        $updateStatement[] = '`ts_modif` = current_timestamp';
                        $insertStatementCol[] = '`ts_ajout`';
                        $insertStatementVal[] = 'current_timestamp';

                        $updateStatement[] = '`usager_id_modif` = ?';
                        $insertStatementCol[] = '`usager_id_ajout`';
                        $insertStatementVal[] = '?';
                        $data[] = getIdUsager();
                    }

                    if (in_array(
                            $table,
                            array(
                                'inventaires',
                                'uni_reglementations_niveaux',
                                'suivis_budgetaires',
                                'echeanciers',
                                'contrats_projets',
                                'acquisitions_projets',
                                'financements_projets',
                                'questions',
                                'acquisitions_projets_groupes_usagers',
                                'questions_groupes_usagers',
                                'approvisionnements',
                                'imports',
                                'gabarits_exports',
                                'champs_textes_exports',
                                'champs_textes_denormalises',
                                'formulaires_readonly',
                                'formulaires_hidden',
                                'modeles_impressions',
                                'formulaires_limitations',
                                'procedures_inspections_planifiees',
                                'procedures_entretiens_planifiees',
                                'procedures_surveillances_planifiees',
                                'proprietes_dinstallation_et_dutilisation',
                                'notifications_gestion',
                                'admin_liste_colonnes_bio_medicaux',
                                'exemptions_verrouillages',
                                'exports_automatiques',
                                'cedules_demenagements',
                                'procedures_inspections_planifiees_gbm',
                                'procedures_mises_en_service_planifiees_mep',
                                'formulaires_customs',
                                'visualisateur3d_fichiers'
                            )
                        ) && $id == 0) {
                        if (in_array($table, array('procedures_inspections_gbm', 'questions', 'imports', 'notifications_gestion', 'contrats_projets', 'exemptions_verrouillages', 'cedules_demenagements')
                        )) {
                            if ($id == 0) {
                                $updateStatement[] = '`ts_ajout` = current_timestamp';
                                $insertStatementCol[] = '`ts_ajout`';
                                $insertStatementVal[] = 'current_timestamp';

                                $updateStatement[] = '`usager_ajout_id` = ?';
                                $insertStatementCol[] = '`usager_ajout_id`';
                                $insertStatementVal[] = '?';
                                $data[] = getIdUsager();
                            }

                            if ($table == 'questions') {
                                if (isset($_POST['formulaire_id']) && intval(
                                        $_POST['formulaire_id']
                                    ) > 0 && isset($_POST['liste_item_id']) && intval($_POST['liste_item_id']) > 0) {
                                    $formulaire = new Formulaires(intval($_POST['formulaire_id']));

                                    $updateStatement[] = '`classe_formulaire` = ?';
                                    $insertStatementCol[] = '`classe_formulaire`';
                                    $insertStatementVal[] = '?';
                                    $data[] = $formulaire->nom;

                                    $updateStatement[] = '`nom_table` = ?';
                                    $insertStatementCol[] = '`nom_table`';
                                    $insertStatementVal[] = '?';

                                    $metaFirstChamp = $formulaire->getFirstChamp();
                                    $data[] = $metaFirstChamp['table'];
                                }
                            }
                        }

                        $updateStatement[] = '`projet_id` = ?';
                        $insertStatementCol[] = '`projet_id`';
                        $insertStatementVal[] = '?';
                        $data[] = getProjetId();

                        if (in_array($table, $TablesCategoriesPermissions) || in_array(
                                $table,
                                array(
                                    'echeanciers',
                                    'approvisionnements',
                                    'acquisitions_projets',
                                    'financements_projets'
                                )
                            )) {
                            $updateStatement[] = '`ts_modif` = current_timestamp';
                            $insertStatementCol[] = '`ts_ajout`';
                            $insertStatementVal[] = 'current_timestamp';
                            $insertStatementCol[] = '`ts_modif`';
                            $insertStatementVal[] = 'current_timestamp';

                            $updateStatement[] = '`usager_modif_id` = ?';
                            $insertStatementCol[] = '`usager_modif_id`';
                            $insertStatementVal[] = '?';
                            $insertStatementCol[] = '`usager_ajout_id`';
                            $insertStatementVal[] = '?';
                            $data[] = getIdUsager();
                            $data[] = getIdUsager();
                        }

                        if ($table == 'procedures_entretiens_planifiees') {
                            $updateStatement[] = '`code_suivi` = ?';
                            $insertStatementCol[] = '`code_suivi`';
                            $insertStatementVal[] = '?';
                            $data[] = uniqid();
                        }

                        if ($table == 'proprietes_dinstallation_et_dutilisation') {
                            $updateStatement[] = '`fiche_id` = ?';
                            $insertStatementCol[] = '`fiche_id`';
                            $insertStatementVal[] = '?';
                            $data[] = intval($_GET['from_fiche']);
                        }
                    }

                    if ($table == 'champs_textes_exports' && $id == 0) {
                        $insertStatementCol[] = '`nom_champ`';
                        $insertStatementVal[] = '?';
                        $data[] = 'champ_combine_' . ChampsTextesExports::trouverPremierTrou(
                                intval($_POST['formulaire_id'])
                            );
                    }

                    if ($table == 'champs_textes_denormalises' && $id == 0) {
                        $insertStatementCol[] = '`nom_champ`';
                        $insertStatementVal[] = '?';
                        $data[] = 'champ_combine_' . ChampsTextesDenormalises::trouverPremierTrou(
                                intval($_POST['formulaire_id'])
                            );
                    }

                    if ($table == 'procedures_entretiens_planifiees') {
                        $priorites = ProcedureEntretienPlanifiee::getPriorites();
                        $date_echeance = date(
                            'Y-m-d',
                            strtotime($_POST['date_travaux'] . ' + ' . $priorites[$_POST['priorite']]['formated'])
                        );

                        $updateStatement[] = '`date_echeance` = ?';
                        $insertStatementCol[] = '`date_echeance`';
                        $insertStatementVal[] = '?';
                        $data[] = $date_echeance;

                        if (isset($_POST['completer'])) {
                            $updateStatement[] = '`ts_complete` = current_timestamp';
                            $insertStatementCol[] = '`ts_complete`';
                            $insertStatementVal[] = 'current_timestamp';

                            $updateStatement[] = '`usager_complete_id` = ?';
                            $insertStatementCol[] = '`usager_complete_id`';
                            $insertStatementVal[] = '?';
                            $data[] = getIdUsager();
                        } elseif (isset($_POST['annuler'])) {
                            $updateStatement[] = '`ts_annule` = current_timestamp';
                            $insertStatementCol[] = '`ts_annule`';
                            $insertStatementVal[] = 'current_timestamp';

                            $updateStatement[] = '`usager_annule_id` = ?';
                            $insertStatementCol[] = '`usager_annule_id`';
                            $insertStatementVal[] = '?';
                            $data[] = getIdUsager();
                        } elseif (isset($_POST['ignorer'])) {
                            $updateStatement[] = '`ts_ignore` = current_timestamp';
                            $insertStatementCol[] = '`ts_ignore`';
                            $insertStatementVal[] = 'current_timestamp';

                            $updateStatement[] = '`usager_ignore_id` = ?';
                            $insertStatementCol[] = '`usager_ignore_id`';
                            $insertStatementVal[] = '?';
                            $data[] = getIdUsager();
                        }
                    }
                }

                $is_ajout = false;
                if ($table <> 'acquisitions_groupes_equipements') {
                    $table_to_use = "`" . db::tableColonneValide($table) . "`";
                    if ($table == 'formulaires_listes_parametres') {
                        $table_to_use = "`formulaires_listes`";
                    }
                    if ($table == 'formulaires_customs_parametres') {
                        $table_to_use = "`formulaires_customs`";
                    }
                    if (in_array($table, $TablesCategoriesPermissions)) {
                        $table_to_use = "`permissions`";
                    }
                    if ($table == 'evenements_systeme') {
                        $table_to_use = "`master`.`evenements_systeme`";
                    }
                    if ($id > 0) {
                        if (!empty($updateStatement)) {
                            $data[] = $id;
                            $sql = "UPDATE $table_to_use SET  " . dm_implode(", ", $updateStatement) . " WHERE id = ?";
                            db::instance()->query($sql, $data);
                        }
                    } else {
                        if (!in_array($table, ['acquisitions_jalons_modeles', 'financements_jalons_modeles', 'inventaires_locaux_champs'])) {
                            $sql = "INSERT INTO $table_to_use (" . dm_implode(
                                    ", ",
                                    $insertStatementCol
                                ) . ") VALUES (" . dm_implode(
                                    ", ",
                                    $insertStatementVal
                                ) . ")";
                            db::instance()->query($sql, $data);
                        }

                        $id = db::instance()->lastInsertId();

                        $is_ajout = true;

                        if ($table == 'procedures_entretiens_planifiees') {
                            $sql = "update procedures_entretiens_planifiees set no_bon_travail = ? where id = ?";
                            db::instance()->query($sql, array("BT-" . ($id + 7000000), $id));
                        }
                    }
                }

                if (method_exists($className, 'traitementSauvegardeSupplementaire')) {
                    $className::traitementSauvegardeSupplementaire($id, $_REQUEST, $is_ajout, $objAnciennesValeurs);
                }

                if (isset($_FILES['procedure']) && is_uploaded_file($_FILES['procedure']['tmp_name'])) {
                    move_uploaded_file($_FILES['procedure']['tmp_name'], procedurePath($id));
                }

                if (in_array($table, $tables_for_listes) && $is_ajout) {
                    $sql = "insert into `" . db::tableColonneValide($table) . "_projets` (liste_item_id, projet_id) values (?, ?)";
                    db::instance()->query($sql, array($id, getProjetId()));
                }

                if ($table == 'procedures_entretiens_planifiees' && (isset($_POST['completer']) || isset($_POST['ignorer']) || isset($_POST['annuler']))) {
                    $procedure_planifie = new ProcedureEntretienPlanifiee($id);
                    if ($procedure_planifie->mode_repetition == 'periode_completion') {
                        $procedure_planifie->repeter();
                    }
                }

                self::traiteDocumentsMobiles($id);

                db::instance()->commit();

                if ($table == 'pseudos') {
                    init_pseudos(true);
                }

                if ($table == 'usagers' && $id == getIdUsager() && dm_strlen($new_password) > 0) {
                    $usager = new Usager($id);
                    $_SESSION['usager_' . $CodeClient] = $usager;

                    $sql = "select password from usagers where id = ?";
                    $usager_db = db::instance()->getRow($sql, array($id));

                    $user_browser = getIp(true);
                    $_SESSION['login_string'] = hash('sha512', $usager_db['password'] . $user_browser);
                }

                if (isset($element_ids_to_recharge_fiche) && count($element_ids_to_recharge_fiche) > 0) {
                    db::instance()->autocommit(false);

                    $sql = "select distinct batiments_niveaux.ouvrage_id, fiches.batiment_niveau_element_id, fiches_batiment_niveaux_elements.fiche_id
                        from fiches_batiment_niveaux_elements
                        join fiches on fiches.id = fiche_id
                        join batiments_niveaux on batiments_niveaux.id = batiment_niveau_id
                        where fiches_batiment_niveaux_elements.batiment_niveau_element_id in (" . dm_implode(
                            ', ',
                            $element_ids_to_recharge_fiche
                        ) . ")";
                    $rows_fiches = db::instance()->getAll($sql, array());

                    foreach ($rows_fiches as $i => $row_fiche) {
                        Fiches::ajusterHierarchieFiltre(
                            intval($row_fiche['ouvrage_id']),
                            intval($row_fiche['batiment_niveau_element_id']),
                            intval($row_fiche['fiche_id'])
                        );
                    }
                    db::instance()->commit();
                }

                if (isset($_GET['isWiz']) && $table <> 'champs_ponctuels') {
                    $nomOption = $_POST['nom'];
                }

                if (isset($_GET['fromMobile'])) {
                    if ($_GET['fromMobile'] == 'equipements') {
                        header(
                            'Location: index.php?section=releveMobile&module=equipements&fiche_id_sel=' . intval(
                                $_GET['fiche_id_sel']
                            )
                        );
                    } else if ($_GET['fromMobile'] == 'inventaire') {
                        header(
                            'Location: index.php?section=releveMobile&module=inventaire&ligne_id_sel=' . intval(
                                $_GET['ligne_id_sel']
                            ) . (isset($_GET['inventaire_local_id']) ? '&inventaire_local_id=' . $_GET['inventaire_local_id'] : '')
                        );
                    }
                } elseif (isset($_GET['import_id'])) {
                    header(
                        'Location: index.php?section=fiche&module=comparaison' . (isset($_GET['added_from_importation']) ? '&id=' . $id . '&fiche_code=' . $_GET['fiche_code'] . (isset($_GET['ligne_code']) ? '&ligne_code=' . $_GET['ligne_code'] : '') . '&formulaire=' . (isset($_GET['formulaire_for_import']) ? $_GET['formulaire_for_import'] : '') . '&nom_champ=' . $_GET['nom_champ'] . '&import_data_from_id=1' : '') . '&import_id=' . intval(
                            $_GET['import_id']
                        )
                    );
                } else {
                    ?>
                    <script>
                        <?php
                        if (isset($_GET['from_gestion_portes']) || isset($_GET['from_gestionnaire'])) { ?>
						parent.window.location.reload();
                        <? } elseif (isset($_GET['callback'])) { ?>
						parent.<?=validerJsCallBack($_GET['callback'])?>('<?=$id?>');
                        <? } elseif (in_array($table, array('fiches'))) { ?>
						parent.clearFiltre('<?=$id?>', 'GEN');//parent.window.location = '<?//=$ServerUrl?>index.php?section=<?//=(isset($_SESSION['is_gabarit']) && intval($_SESSION['is_gabarit']) == 1 ? 'gabarits' : 'fiche')?>&module=index&id=<?//=$id?>';
                        <? } elseif (in_array($table, array('acquisitions_groupes_equipements')
                        ) && isset($_POST['acquisition_groupe_id'])) { ?>
						parent.window.location = 'index.php?section=acquisition&module=index&acquisition_groupe_id=<?=$_POST['acquisition_groupe_id']?>';
                        <? } elseif (in_array($table, array('acquisitions_projets_groupes_equipements')
                        ) && isset($_GET['acquisition_projet_id'])) { ?>
						parent.window.location = 'index.php?section=acquisition&module=projets&acquisition_projet_id=<?=$_GET['acquisition_projet_id']?>';
                        <? } elseif (in_array($table, array('acquisitions_projets'))) { ?>
						parent.window.location = 'index.php?section=acquisition&module=projets&acquisition_projet_id=<?=$id?>';
                        <? } elseif (in_array($table, array('financements_groupes_equipements')
                        ) && isset($_POST['financement_groupe_id'])) { ?>
						parent.window.location = 'index.php?section=financement&module=index&financement_groupe_id=<?=$_POST['financement_groupe_id']?>';
                        <? } elseif (in_array($table, array('financements_projets_groupes_equipements')
                        ) && isset($_GET['financement_projet_id'])) { ?>
						parent.window.location = 'index.php?section=financement&module=projets&financement_projet_id=<?=$_GET['financement_projet_id']?>';
                        <? } elseif (in_array($table, array('financements_projets'))) { ?>
                        <?
                        if (isset($_SESSION['avertissementsDepotTransferes'])) {
                        ?>
						    parent.$.alert(<?= json_encode($_SESSION['avertissementsDepotTransferes']) ?>);
                        <?
                        } else {
                            ?>
                            parent.window.location = 'index.php?section=financement&module=projets&financement_projet_id=<?=$id?>';
                        <?
                        }
                        ?>
                        <? } elseif (in_array($table, array('inventaires'))) { ?>
						parent.window.location = 'index.php?section=inventaires&module=index';
                        <? } elseif (in_array($table, array('procedures_surveillances_planifiees_fiches')
                        ) && isset($_GET['procedure_surveillance_planifiee_id'])) { ?>
						parent.window.location = 'index.php?section=surveillance&module=historique&procedure_surveillance_planifiee_id=<?=$_GET['procedure_surveillance_planifiee_id']?>';
                        <? } elseif (in_array($table, array('procedures_inspections_planifiees_fiches')
                        ) && isset($_GET['procedure_inspection_planifiee_id'])) { ?>
						parent.window.location = 'index.php?section=inspection&module=historique&procedure_inspection_planifiee_id=<?=$_GET['procedure_inspection_planifiee_id']?>';
                        <? } elseif (isset($_GET['from_coordination'])) { ?>
						parent.window.location = 'index.php?section=coordination&module=general<?=(isset($_GET['page']) ? '&page=' . intval(
                                $_GET['page']
                            ) : '')?>';
                        <? } elseif (isset($_GET['equipement_bio_medicaux_coordination']) || isset($_GET['refressh_after']) || in_array(
                            $table,
                            array(
                                'admin_liste_colonnes_bio_medicaux',
                                'financements_projets_depots',
                                'ouvrages',
                                'evenements_systeme',
                                'financements_projets_administrations',
                                'contrats_projets_administrations',
                                'contrats_projets',
                                'suivis_budgetaires_administrations'
                            )
                        ) || ($table == 'usagers' && $id == getIdUsager()) || isset($_GET['bind_non_reconnus']) || (!isset($_GET['from_fiche']) && in_array(
                                $table,
                                array(
                                    'suivis_budgetaires',
                                    'acquisitions_projets',
                                    'questions',
                                    'procedures_entretiens',
                                    'financements_projets',
                                    'procedures_entretiens_planifiees',
                                    'procedures_surveillances_planifiees',
                                    'procedures_inspections',
                                    'procedures_inspections_planifiees',
                                    'procedures_inspections_planifiees_fiches',
                                    'programmes_entretiens',
                                    'notifications_gestion',
                                    'approvisionnements',
                                    'inventaires_lignes',
                                    'inventaires_locaux',
                                    'exemptions_verrouillages',
                                    'procedures_inspections_planifiees_gbm',
                                    'procedures_inspections_gbm',
                                    'procedures_inspections_planifiees_gbm_equipements',
                                    'procedures_mises_en_service_mep',
                                    'procedures_mises_en_service_planifiees_mep',
                                    'procedures_mises_en_service_planifiees_mep_equipements'
                                )
                            ))) { ?>
						parent.window.location.reload();
                        <? } elseif ($table == 'financements_projets_administrations_unifiees') { ?>
						parent.window.location = "index.php?section=financement&module=projets&mode=global&admin_modele_id=<?=$id?>";
                        <? } elseif (isset($_GET['isWiz']) && $table == 'champs_ponctuels') { ?>
						parent.addChampPonctuel(<?=$id?>);
                        <? } elseif (isset($_GET['isWiz'])) { ?>
						parent.addOption('<?=$_GET['isWiz']?>', <?=$id?>, '<?=dm_addslashes($nomOption)?>');
                        <? } elseif (isset($_GET['from_fiche'])) { ?>
						parent.reloadFiche(<?=intval($_GET['from_fiche'])?>);
                        <? } else { ?>
						if (parent.loadContentOnglet)
							parent.loadContentOnglet('<?=(isset($_GET['table_to_reload']) ? $_GET['table_to_reload'] : $table)?>', false);
                        <? } ?>
						parent.$.fancybox.close();
                    </script>
                    <?php
                }
                die();
            }
        }
    }

    public static function ajustementDataForObj($id, $table)
    {
        global $tables_for_listes, $db;
        $data = isset($_POST) && count($_POST) > 0 ? $_POST : [];
        if (in_array($table, $tables_for_listes) && !in_array(
                $table,
                array('modeles', 'marques', 'nomenclatures', 'fiches_statuts')
            )) {
            $data = Utils::flattenData($data);
        }

        if (isset($_GET['nom'])) {
            $data['nom'] = $_GET['nom'];
        }

        if (isset($_GET['formulaire_id'])) {
            $data['formulaire_id'] = $_GET['formulaire_id'];
        }

        if (isset($_GET['numero_modele'])) {
            $data['numero_modele'] = $_GET['numero_modele'];
        }

        if (isset($_GET['discipline_id'])) {
            $data['discipline_id'] = intval($_GET['discipline_id']);
            $discipline = new Discipline(intval($_GET['discipline_id']));
            $data['discipline'] = $discipline->nom;
        }

        if (isset($_GET['periode_id'])) {
            $data['periode_id'] = intval($_GET['periode_id']);
            $periode = new Periode(intval($_GET['periode_id']));
            $data['periode'] = $periode->nom;
        }

        if (isset($_GET['type_procedure'])) {
            $data['type'] = $_GET['type_procedure'];
        }

        if (isset($_GET['date_travaux'])) {
            $data['date_travaux'] = $_GET['date_travaux'];
            $data['date_travaux_readonly'] = 1;
        }

        if (isset($_GET['fiche_id'])) {
            $data['fiche_id'] = intval($_GET['fiche_id']);
        }

        if (isset($_GET['from_fiche'])) {
            $data['fiche_id'] = intval($_GET['from_fiche']);
        }

        if (isset($_GET['marque_id'])) {
            $data['marque_id'] = intval($_GET['marque_id']);
            $marque = new Marque(intval($_GET['marque_id']));
            $data['marque'] = $marque->nom;
        }

        if (isset($_GET['modele_id'])) {
            $data['modele_id'] = intval($_GET['modele_id']);
            $modele = new Modele(intval($_GET['modele_id']));
            $data['modele'] = $modele->numero_modele;
        }

        if (isset($_GET['nomenclature_id'])) {
            $data['nomenclature_id'] = intval($_GET['nomenclature_id']);
            $nomenclature = new Nomenclature(intval($_GET['nomenclature_id']));
            $data['nomenclature'] = $nomenclature->nom;
        }

        if (isset($_GET['nombre_phases_panneau_id'])) {
            $data['nombre_phases_panneau_id'] = intval($_GET['nombre_phases_panneau_id']);
        }

        if (isset($_GET['nombre_phases'])) {
            $data['nombre_phases'] = intval($_GET['nombre_phases']);
        }

        if (isset($_GET['nombre_phases_id'])) {
            $data['nombre_phases_id'] = intval($_GET['nombre_phases_id']);
        }

        if (isset($_GET['nombre_phase_type_id'])) {
            $data['nombre_phase_type_id'] = intval($_GET['nombre_phase_type_id']);
        }

        if (isset($_GET['phasage_type_id'])) {
            $data['phasage_type_id'] = intval($_GET['phasage_type_id']);
        }

        if (isset($_GET['tension_type_id'])) {
            $data['tension_type_id'] = intval($_GET['tension_type_id']);
        }

        if (isset($_GET['client_id'])) {
            $data['client_id'] = intval($_GET['client_id']);
        }

        if (isset($_GET['id']) && !isset($_GET['formulaire'])) {
            $data['id'] = intval($_GET['id']);
        }

        if (isset($_GET['numero_lot'])) {
            $data['numero_lot'] = $_GET['numero_lot'];
        }

        if ($id == 0 && $table == 'batiments_niveaux') {
            $sql = "select max(ordre) as ordre
                from batiments_niveaux
                where ouvrage_id = ?";
            $row = db::instance()->getRow($sql, array(getOuvrageId()));

            $data['ordre'] = isset($row['ordre']) ? intval($row['ordre']) + 1 : 1;
        }

        if ($id == 0 && $table == 'uni_reglementations_niveaux') {
            $sql = "select max(ordre) as ordre
                from uni_reglementations_niveaux";
            $row = db::instance()->getRow($sql, array());

            $data['ordre'] = isset($row['ordre']) ? intval($row['ordre']) + 1 : 1;
        }

        if (count($_POST) == 0 && isset($_GET['batiment_id']) && intval(
                $_GET['batiment_id']
            ) > 0 && $table == 'fiches') {
            $data['batiment_id'] = intval($_GET['batiment_id']);
        }

        if (count($_POST) == 0 && isset($_GET['code']) && dm_strlen($_GET['code']) > 0 && $table == 'fiches') {
            $data['code'] = $_GET['code'];
        }

        if (count($_POST) == 0 && isset($_GET['code_alternatif']) && dm_strlen(
                $_GET['code_alternatif']
            ) > 0 && $table == 'fiches') {
            $data['code_alternatif'] = $_GET['code_alternatif'];
        }

        if (count($_POST) == 0 && isset($_GET['numero_lot']) && dm_strlen(
                $_GET['numero_lot']
            ) > 0 && $table == 'fiches') {
            $data['numero_lot'] = $_GET['numero_lot'];
        }

        if (count($_POST) == 0 && isset($_GET['fiche_type_id']) && intval(
                $_GET['fiche_type_id']
            ) > 0 && $table == 'fiches') {
            $data['fiche_type_id'] = intval($_GET['fiche_type_id']);
        }

        if (count($_POST) == 0 && isset($_GET['fiche_sous_type_id']) && intval(
                $_GET['fiche_sous_type_id']
            ) > 0 && $table == 'fiches') {
            $data['fiche_sous_type_id'] = intval($_GET['fiche_sous_type_id']);
        }

        if (count($_POST) == 0 && isset($_GET['fiche_parent_id']) && intval(
                $_GET['fiche_parent_id']
            ) > 0 && $table == 'fiches') {
            $data['fiche_parent_id'] = intval($_GET['fiche_parent_id']);
        }

        if (count($_POST) == 0 && isset($_GET['fiche_local_id']) && intval(
                $_GET['fiche_local_id']
            ) > 0 && $table == 'fiches') {
            $data['fiche_local_id'] = intval($_GET['fiche_local_id']);
        }

        if (count($_POST) == 0 && isset($_GET['fiche_local_destination_id']) && intval(
                $_GET['fiche_local_destination_id']
            ) > 0 && $table == 'fiches') {
            $data['fiche_local_destination_id'] = intval($_GET['fiche_local_destination_id']);
        }

        if (count($_POST) == 0 && isset($_GET['fiche_local_controleur_id']) && intval(
                $_GET['fiche_local_controleur_id']
            ) > 0 && $table == 'fiches') {
            $data['fiche_local_controleur_id'] = intval($_GET['fiche_local_controleur_id']);
        }

        if (count($_POST) == 0 && isset($_GET['id_revit']) && dm_strlen($_GET['id_revit']) > 0 && $table == 'fiches') {
            $data['id_revit'] = $_GET['id_revit'];
        }

        if (count($_POST) == 0 && isset($_GET['id_codebook']) && dm_strlen(
                $_GET['id_codebook']
            ) > 0 && $table == 'fiches') {
            $data['id_codebook'] = $_GET['id_codebook'];
        }

        if (count($_POST) == 0 && isset($_GET['is_gabarit']) && intval($_GET['is_gabarit']) > 0 && $table == 'fiches') {
            $data['is_gabarit'] = intval($_GET['is_gabarit']);
        }

        if (isset($_POST) && count($_POST) > 0) {
            if (isset($_POST['fiche_type_id']) && intval($_POST['fiche_type_id']) > 0 && $table == 'fiches') {
                $fiche_type = new FicheType(intval($_POST['fiche_type_id']));
                $data['fiche_type'] = $fiche_type->nom;
            }

            if (isset($_POST['fiche_sous_type_id']) && intval($_POST['fiche_sous_type_id']) > 0 && $table == 'fiches') {
                $fiche_sous_type = new FicheSousType(intval($_POST['fiche_sous_type_id']));
                $data['fiche_sous_type'] = $fiche_sous_type->nom;
            }
        }

        return $data;
    }

    public static function preparationObjAfterSubmit($id, $data, $className, $table)
    {
        if (isset($_GET['source_id'])) {
            $obj = new $className(intval($_GET['source_id']));
            $obj->nom = '';
        } else {
            $obj = new $className($id, $data);
        }

        if (method_exists($className, 'traitementApresSubmit')) {
            $obj->traitementApresSubmit($_REQUEST);
        }
        return $obj;
    }

    public static function afficheFormulaire($id, $obj, $className, $table, $typeTable, $useData, $isValidationBtns)
    {
        global $tables, $CodeClient;

        $titre = isset($_GET['formulaire']) ? txt($className::TITRE) : $tables[$typeTable][$table]['nom'];
        if (isset($_GET['is_gabarit']) && intval($_GET['is_gabarit']) == 1) {
            $titre = txt('Gabarits');
        } elseif (isset($_GET['fiche_sous_type_id']) && intval($_GET['fiche_sous_type_id']) == 2000014) {
            $titre = txt('Ajout d\'un(e) porte / accès');
        } elseif (isset($_GET['fiche_parent_id']) && intval($_GET['fiche_parent_id']) > 0) {
            $titre = txt('Composantes');
        } elseif (isset($_GET['fiche_local_id']) && intval($_GET['fiche_local_id']) > 0) {
            $titre = txt('Sous-local');
        }
        ?>

        <style>
            #analogique_table {
                width: 90%;
            }

            #analogique_table select.unite {
                width: 100%;
            }

            label.actif {
                width: 75px !important;
                text-align: right;
                position: relative;
                top: -6px;
            }

            td {
                border: solid 0px white !important;
                text-align: left !important;
                padding: 0px !important;
            }

            #discrete_table td input[type=text], #analogique_table td input[type=text] {
                width: 75px !important;
            }

            select {
                color: black !important;
                min-width: 225px;
            }

            #editables label {
                font-size: 13px;
                width: 175px !important;
            }

            select option {
                border: solid 0px;
                color: black !important;
            }

            input {
                font-size: 13px;
                color: black !important;
            }

            #discrete_table td, #analogique_table td {
                padding: 3px !important;
            }

            table, table thead tr, table tfoot tr, table th {
                background-image: none;
            }

            #champs_ponctuels_saisies td input {
                width: 150px !important;
            }

            .champs_ponctuels {
                margin-top: 5px;
            }

            input[type="checkbox"], input[type="radio"] {
                position: relative;
                top: 3px;
            }

            <?
            if (isMobile()) {
                if (isIPad()) {
                ?>
            label.titre {
                max-width: 646px !important;
            }

            .edit_popupContent section > div {
                max-width: 250px !important;
            }

            .edit_popupContent select, .edit_popupContent input {
                max-width: 250px !important;
            }

            <?
                } else {
                ?>
            label.titre {
                max-width: 275px !important;
            }

            label {
                margin-left: 0 !important;
            }

            .edit_popupContent section > div {
                max-width: 250px !important;
            }

            .edit_popupContent select, .edit_popupContent input {
                max-width: 250px !important;
            }

            <?
                }
            }
            ?>

        </style>
        <?
        $is_force_read_only = isset($_GET['force_readonly']);
        ?>
        <script>
			var cur_timestamp = '<?=date('Y-m-d H:i:s')?>';
			var cur_user = '<?=getIdUsager()?>';
			var fiche_parent_id_tmp = '<?=(isset($_GET['fiche_parent_id']) ? intval($_GET['fiche_parent_id']) : '')?>';
            <?
                if (isset($_GET['formulaire'])) {
                    $formulaire = new Formulaires(Formulaires::getIdFromNom($className));
                    ?>
                    $(document).ready(function () {
                        parent.fancybox_resize(<?= ($formulaire->format == 'big' ? 1400 : '') ?>);
                    });
                    <?
                }
            ?>
        </script>
        <div id="popupContent" class="edit_popupContent">
            <?
            if (!isset($_GET['document_edition']) && !$is_force_read_only)
            {
            ?>
            <form id="form" autocomplete="off" method="post"
                  action="index.php?section=admin&module=edit<?= (isset($_GET['from_gestionnaire']) ? '&from_gestionnaire=' . $_GET['from_gestionnaire'] : '') ?><?= (isset($_GET['from_gestion_portes']) ? '&from_gestion_portes=' . $_GET['from_gestion_portes'] : '') ?><?= (isset($_GET['mode']) ? '&mode=' . $_GET['mode'] : '') ?><?= (isset($_GET['equipement_bio_medicaux_coordination']) ? '&equipement_bio_medicaux_coordination=' . $_GET['equipement_bio_medicaux_coordination'] : '') ?><?= (isset($_GET['popupMobile']) ? '&popupMobile=' . $_GET['popupMobile'] : '') ?><?= (isset($_GET['only_cat']) ? '&only_cat=' . $_GET['only_cat'] : '') ?><?= (isset($_GET['only_budget']) ? '&only_budget=' . $_GET['only_budget'] : '') ?><?= (isset($_GET['liste_item_id']) ? '&liste_item_id=' . $_GET['liste_item_id'] : '') ?><?= (isset($_GET['source_id']) ? '&source_id=' . $_GET['source_id'] : '') ?><?= (isset($_GET['is_duplication']) ? '&is_duplication=' . $_GET['is_duplication'] : '') ?><?= (isset($_GET['callback']) ? '&callback=' . validerJsCallBack($_GET['callback']) : '') ?><?= (isset($_GET['refressh_after']) ? '&refressh_after=' . $_GET['refressh_after'] : '') ?><?= (isset($_GET['page']) ? '&page=' . $_GET['page'] : '') ?><?= (isset($_GET['acquisition_projet_id']) ? '&acquisition_projet_id=' . $_GET['acquisition_projet_id'] : '') ?><?= (isset($_GET['financement_projet_id']) ? '&financement_projet_id=' . $_GET['financement_projet_id'] : '') ?><?= (isset($_GET['document_edition']) ? '&document_edition=' . $_GET['document_edition'] : '') ?><?= (isset($_GET['table_to_reload']) ? '&table_to_reload=' . $_GET['table_to_reload'] : '') ?>&table=<?= $table ?>&id=<?= $id ?>&type=<?= $typeTable ?><?= (isset($_GET['dependance_champ_liste']) ? '&dependance_champ_liste=' . $_GET['dependance_champ_liste'] : '') ?><?= (isset($_GET['procedure_surveillance_planifiee_id']) ? '&procedure_surveillance_planifiee_id=' . $_GET['procedure_surveillance_planifiee_id'] : '') ?><?= (isset($_GET['procedure_inspection_planifiee_id']) ? '&procedure_inspection_planifiee_id=' . $_GET['procedure_inspection_planifiee_id'] : '') ?><?= (isset($_GET['compare_data']) ? '&compare_data=' . $_GET['compare_data'] : '') ?><?= (isset($_GET['is_projection']) ? '&is_projection=' . $_GET['is_projection'] : '') ?><?= (isset($_GET['date_travaux']) ? '&date_travaux=' . $_GET['date_travaux'] : '') ?><?= (isset($_GET['fiche_parent_id']) ? '&fiche_parent_id=' . $_GET['fiche_parent_id'] : '') ?><?= (isset($_GET['add_sub_room']) ? '&add_sub_room=' . $_GET['add_sub_room'] : '') ?><?= (isset($_GET['add_composante']) ? '&add_composante=' . $_GET['add_composante'] : '') ?><?= (isset($_GET['mode']) ? '&mode=' . $_GET['mode'] : '') ?><?= (isset($_GET['id_ligne']) ? '&id_ligne=' . $_GET['id_ligne'] : '') ?><?= (isset($_GET['import_id']) ? '&import_id=' . $_GET['import_id'] : '') ?><?= (isset($_GET['from_coordination']) ? '&from_coordination=' . $_GET['from_coordination'] : '') ?><?= (isset($_GET['isWiz']) ? '&isWiz=' . $_GET['isWiz'] : '') ?><?= (isset($_GET['from_fiche']) ? '&from_fiche=' . intval($_GET['from_fiche']) : '') ?><?= (isset($_GET['formulaire']) ? '&formulaire=' . $_GET['formulaire'] : '') ?><?= (isset($_GET['added_from_importation']) ? '&added_from_importation=' . $_GET['added_from_importation'] : '') ?><?= (isset($_GET['fiche_code']) ? '&fiche_code=' . $_GET['fiche_code'] : '') ?><?= (isset($_GET['ligne_code']) ? '&ligne_code=' . $_GET['ligne_code'] : '') ?><?= (isset($_GET['formulaire_for_import']) ? '&formulaire_for_import=' . $_GET['formulaire_for_import'] : '') ?><?= (isset($_GET['nom_champ']) ? '&nom_champ=' . $_GET['nom_champ'] : '') ?><?= (isset($_GET['date_du']) ? '&date_du=' . $_GET['date_du'] : '') ?><?= (isset($_GET['bind_non_reconnus']) ? '&bind_non_reconnus=' . $_GET['bind_non_reconnus'] : '') ?>"
                  enctype="multipart/form-data">
                <?
                } elseif (!isset($_GET['document_edition'])) {
                ?>
                <form>
                    <?
                    }
                    if (isset($_GET['formulaire'])) {
                        ?>
                        <input type="hidden" id="fiche_id" value="<?= intval($_GET['from_fiche']) ?>"/>
                        <?
                    } else {
                        ?>
                        <input type="hidden" id="id" name="id" value="<?= $id ?>"/>
                        <input type="hidden" id="typeTable" value="<?= ($typeTable) ?>"/>
                        <input type="hidden" id="table" value="<?= $table ?>"/>
                        <?
                    }
                    ?>
                    <fieldset>
                        <?
                        if (!isset($_GET['document_edition'])) {
                            if (isset($_GET['from_fiche']) && intval($_GET['from_fiche']) > 0) {
                                $fiche = new Fiches(intval($_GET['from_fiche']));
                            }
                            ?>
                            <?
                            $titre_gauche = '';
                            if ($table == 'equipements_bio_medicaux_types' && !isset($_GET['only_cat'])) {
                                $titre_gauche = $obj->nom . ' - ' . $obj->description;
                                if (dm_strlen($titre_gauche) > 80) {
                                    $titre_gauche = dm_substr($titre_gauche, 0, 80) . '...';
                                }
                                $titre = isset($_GET['only_budget']) ? txt('Paramètres budgétaires') : txt(
                                    'Requis techniques'
                                );
                            } elseif (isset($fiche)) {
                                $titre_gauche = 'Fiche: ' . $fiche->code . (dm_strlen(
                                        $fiche->code_alternatif
                                    ) > 0 ? '<span style="margin-left: 30px;  font-size: 16px; font-weight: 100;">(Code alternatif: ' . $fiche->code_alternatif . ')</span>' : '');
                            }
                            ?>
                            <label class="titre"><span
                                        style="float: left; font-size: 16px; font-weight: 700;"><?= $titre_gauche ?></span><?= $titre ?>
                            </label>
                            <br/>
                            <?
                        }
                        ?>
                        <div id="editables">
                            <div id="bloc_erreur" style="margin-left: 20px;">
                                <?
                                erreurs();
                                ?>
                            </div>
                            <?
                            if (isset($_GET['added_from_importation'])) {
                                ?>
                                <b style="font-weight: bold; color: red; margin: 0 0 25px 25px;"><?= txt(
                                        'Attention! La donnée que vous tentez d\'ajouter n\'existe pas dans le registre de DocMatic. Desirez-vous l\'ajouter?'
                                    ) ?></b>
                                <?
                            }

                            if (isset($_GET['equipement_bio_medicaux_coordination'])) {
                                ?>
                                <div style="padding: 10px;">
                                    <div style="border: dashed 1px black; padding: 10px; margin: 10px 0; text-align: left;">
                                        <h4 style="text-decoration: underline;"><?= txt(
                                                'Équipements biomedicaux'
                                            ) ?></h4>
                                        <table id="equipement_bio_medicaux_coordination" class="">
                                            <thead>
                                            <?
                                            $filtre = [
                                                'cols' => [
                                                    'equipement_bio_medicaux_type',
                                                    'quantite',
                                                    'quantite_fantome',
                                                    'qte_total_a_coordonner',
                                                    'qte_coordonnee',
                                                    'qte_restante',
                                                    'qte_liee'
                                                ]
                                            ];
                                            EquipementsBioMedicauxNoCoord::getFicheHTMLTitre(false, $filtre);
                                            ?>
                                            </thead>
                                            <tbody>
                                            <?
                                            // is_coordination_telecom is_coordination_electricite
                                            $isCoordKey = $className == 'PrisesElectriquesCoordinations' ? 'is_coordination_electricite' : 'is_coordination_telecom';
                                            $liste = EquipementsBioMedicauxNoCoord::getListe(
                                                ['fiche_id' => $fiche->id, $isCoordKey => 1]
                                            );
                                            $liste = EquipementsBioMedicauxNoCoord::addDataCoordinationRaccordsForEquipement(
                                                intval($_GET['from_fiche']),
                                                $liste,
                                                $className
                                            );

                                            if (count($liste) > 0) {
                                                foreach ($liste as $iteration => $ligne) {
                                                    $ligne->getFicheHTMLLigne($iteration, null, $filtre);
                                                }
                                            }
                                            ?>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>

                                <div id="dialog-coordination" title="Coordination"
                                     style="display: none; margin: 5px 15px;">
                                    <p style="margin-top: 10px;"></p>
                                </div>
                                <?
                            }

                            if (isset($_GET['import_id'])) {
                                $import = new Imports(intval($_GET['import_id']));
                                if ($import->type == 'listes' && $table <> 'fiches') {
                                    $to_import_data = $import->getDataForFiche(intval($_GET['from_fiche']));
                                    ?>
                                    <div style="padding: 10px;">
                                        <div style="border: dashed 1px black; padding: 10px; margin: 10px 0;">
                                            <h4 style="text-decoration: underline;"><?= txt(
                                                    'Provenant de l\'import'
                                                ) ?></h4>
                                            <table style="margin: 10px 0 0 0;">
                                                <tr>
                                                    <td style="font-weight: bold;">
                                                        <?= txt('Code externe') ?>
                                                    </td>
                                                    <td style="font-weight: bold;">
                                                        <?= txt('Correspondance DocMatic') ?>
                                                    </td>
                                                    <td style="font-weight: bold; text-align: center!important;">
                                                        <?= txt('Qté requise') ?>
                                                    </td>
                                                    <?
                                                    if (in_array($className, ['EquipementsBioMedicauxNoCoord', 'EquipementsBioMedicaux'])) {
                                                        ?>
                                                        <td style="font-weight: bold; text-align: center!important;">
                                                            <?= txt('Qté existante') ?>
                                                        </td>
                                                        <td style="font-weight: bold; text-align: center!important;">
                                                            <?= txt('Qté fantôme') ?>
                                                        </td>
                                                        <td style="font-weight: bold; text-align: center!important;">
                                                            <?= txt('Qté conservée') ?>
                                                        </td>
                                                        <td style="font-weight: bold;">
                                                            <?= txt('Commentaires') ?>
                                                        </td>
                                                        <td style="font-weight: bold;">
                                                            <?= txt('Spécificité de l\'installation	') ?>
                                                        </td>
                                                        <?
                                                        if (isChampRemarques()) {
                                                            ?>
                                                            <td style="font-weight: bold;">
                                                                <?= txt('Remarques') ?>
                                                            </td>
                                                            <?
                                                        }
                                                    }
                                                    ?>
                                                </tr>
                                                <?
                                                foreach ($to_import_data as $i => $row) {
                                                    ?>
                                                    <tr>
                                                        <td>
                                                            <?= $row['code_valeur_externe'] ?>
                                                        </td>
                                                        <td>
                                                            <?= $row['nom_interne'] ?>
                                                        </td>
                                                        <td style="text-align: center!important;">
                                                            <?= $row['quantite_externe'] ?>
                                                        </td>
                                                        <?
                                                        if (in_array($className, ['EquipementsBioMedicauxNoCoord', 'EquipementsBioMedicaux'])) {
                                                            ?>
                                                            <td style="text-align: center!important;">
                                                                <?= $row['quantite_existante_externe'] ?>
                                                            </td>
                                                            <td style="text-align: center!important;">
                                                                <?= $row['quantite_fantome_externe'] ?>
                                                            </td>
                                                            <td style="text-align: center!important;">
                                                                <?= $row['quantite_conservee_externe'] ?>
                                                            </td>
                                                            <td>
                                                                <?= $row['commentaires_externe'] ?>
                                                            </td>
                                                            <td>
                                                                <?= $row['specificite_installation_externe'] ?>
                                                            </td>
                                                            <?
                                                            if (isChampRemarques()) {
                                                                ?>
                                                                <td>
                                                                    <?= $row['remarques_externe'] ?>
                                                                </td>
                                                                <?
                                                            }
                                                        }
                                                        ?>
                                                    </tr>
                                                    <?
                                                }
                                                ?>
                                            </table>
                                        </div>
                                    </div>
                                    <?
                                }
                            }

                            if (isset($_GET['compare_data'])) {
                                $liste_to_compare_to = $className::getListe(
                                    array('forced_bd' => $_SESSION['to_compare'], 'forced_key' => 'nom'),
                                    'nom',
                                    true
                                );
                                ?>
                                <div style="padding: 10px;">
                                    <div style="border: dashed 1px black; padding: 15px; margin: 10px 0;">
                                        <h4 style="text-decoration: underline; margin-bottom: 15px;"><?= txt(
                                                'Provenant de l\'import'
                                            ) ?></h4>
                                        <?
                                        foreach ($liste_to_compare_to as $compare_data => $row) {
                                            if ($compare_data == $_GET['compare_data']) {
                                                $metas = $className::getChampsSaisieMetaData();
                                                foreach ($metas as $key => $meta) {
                                                    ?>
                                                    <div style="margin-left: 30px;">
                                                        <span style="font-weight: bold; display: inline-block; width: 225px;"><?= $meta['titre'] ?></span>
                                                        <span style="display: inline-block;"><?= $row->$key ?></span>
                                                    </div>
                                                    <?
                                                }
                                            }
                                        }
                                        ?>
                                    </div>
                                </div>
                                <?
                            }

                            if (isset($_GET['formulaire'])) {
                                $metas = $className::getFlatChampsSaisieMetaData();
                                $gabaritsExistants = [];
                                foreach ($metas as $key => $meta) {
                                    if ($meta['type'] == 'select' && isset($meta['classe']) && dm_strlen(
                                            $meta['classe']
                                        ) > 0 && !isset($gabaritsExistants[$meta['classe']])) {
                                        ?>
                                        <select id="gabarit_<?= $meta['classe'] ?>" style="display: none;">
                                            <?
                                            if (!in_array($meta['classe'], ['Quantites'])) {
                                                ?>
                                                <option value=""></option>
                                                <?
                                            }
                                            $filtre = array('for_edition' => 1);
                                            if (isset($meta['extra_filtre'])) {
                                                $filtre = $meta['extra_filtre'];
                                            }
                                            if ($meta['table'] == 'Fiches') {
                                                $filtre['no_filtre_global'] = 1;
                                            }
                                            if (in_array(
                                                    $className,
                                                    ['EquipementsBioMedicauxNoCoord', 'EquipementsBioMedicaux']
                                                ) && $meta['table'] == 'equipements_bio_medicaux_types' && isset($_GET['from_fiche'])) {
                                                $filtre['notInDepotVerouille'] = $_GET['from_fiche'];
                                            }
                                            $meta['classe']::getOptions($filtre, 0);
                                            ?>
                                        </select>
                                        <?
                                        $gabaritsExistants[$meta['classe']] = 1;
                                    }
                                }
                                $forcedListe = null;
                                if (isset($_GET['equipement_bio_medicaux_coordination'])) {
                                    $data = $className::getListe(array('fiche_id' => intval($_GET['from_fiche'])));
                                    $forcedListe = $className::addDataCoordinationRaccords(
                                        intval($_GET['from_fiche']),
                                        $data
                                    );
                                }
                                $className::getFormHTML(
                                    intval($_GET['from_fiche']),
                                    ($useData ? ($data ?? null) : null),
                                    $forcedListe
                                );
                            } else {
                                ?>
                                <fieldset>
                                    <?
                                    if (isset($_GET['document_edition'])) {
                                        $obj->getDocumentEditorHTML();
                                    } elseif (isset($_GET['is_duplication'])) {
                                        if (isset($_GET["source_id"])) {
                                            ?>
                                            <input type="hidden" id="source_id" value="<?= $_GET['source_id'] ?>"/>
                                            <?
                                        }
                                        $obj->getEditorHTMLForDuplication();
                                    } else {
                                        $obj->getEditorHTML();
                                    }
                                    ?>
                                </fieldset>
                                <?
                            }
                            ?>
                        </div>

                        <br/>

                        <section>
                            <div style="text-align: right; <?= (isMobile() ? (isIPad(
                            ) ? 'max-width: 646px!important;' : 'max-width: 275px!important;') : 'width: 98%!important;') ?>margin-top: 25px;">
                                <?
                                if ($table == 'procedures_entretiens_planifiees' && $obj->id > 0 && in_array(
                                        $obj->code_statut,
                                        array('a_venir', 'en_cours', 'echu', 'a_planifier')
                                    )) {
                                    ?>
                                    <span style="margin-right: 50px;">
<?
if ($obj->code_statut == 'a_planifier') {
    ?>
    <button class="submit" value="submitbuttonvalue" name="ignorer" style="color: red;"><?= txt(
            'Ignorer la planification pour cet événement'
        ) ?></button>
    <?
} else {
    ?>
    <button class="submit" value="submitbuttonvalue" name="completer" style="color: green;"><?= txt(
            'Completer l\'entretien'
        ) ?></button>
    <button class="submit" value="submitbuttonvalue" name="annuler" style="color: red;"><?= txt(
            'Annuler l\'entretien'
        ) ?></button>
    <?
}
?>
						</span>
                                    <?
                                }

                                if ($table == 'financements_projets_depots' && isset($_GET['mode']) && $_GET['mode'] == 'deverouillage_usagers') {
                                    ?>
                                    <button onclick="window.location = 'index.php?action=unlockUnlockDepot&isLock=1&id=<?= $id ?>';return false"
                                            name="cancel"><?= txt('Verrouiller pour tous') ?></button>
                                    <button class="submit" value="submitbuttonvalue" name="save" id="save"><?= txt(
                                            'Déverrouiller pour ces usagers'
                                        ) ?></button>
                                    <?
                                } elseif ($table == 'procedures_entretiens_planifiees') {
                                    if (!in_array($obj->code_statut, array('complete', 'ignore', 'annule')
                                        ) && havePermission('admin_procedures_entretiens_planifiees')) {
                                        ?>
                                        <button class="submit" value="submitbuttonvalue" name="save" id="save"><?= txt(
                                                'Sauvegarder'
                                            ) ?></button>
                                        <?
                                    }
                                } elseif (!isset($_GET['document_edition']) && !$is_force_read_only) {
                                    if (isset($_GET['from_coordination']) && isset($_GET['formulaire']) && $_GET['formulaire'] == 'EquipementsBioMedicaux') {
                                        ?>
                                        <button class="submit" type="button" value="1" name="repondre_oui"
                                                id="repondre_oui"><?= txt('Tout mettre à OUI') ?></button>
                                        <?
                                    }
                                    if (in_array($table, ['inventaires_lignes', 'inventaires_locaux'])) {
                                        ?>
                                        <button type="button" name="ajoutImage" id="ajoutImage"><?= txt('Ajouter une image') ?></button>
                                        <?
                                    }
                                    ?>
                                    <button class="submit" value="submitbuttonvalue" name="save" id="save"><?= txt(
                                            'Sauvegarder'
                                        ) ?></button>
                                    <?
                                }
                                if (isset($_GET['import_id'])) {
                                    ?>
                                    <button onclick="window.location = 'index.php?section=fiche&module=comparaison&import_id=<?= intval(
                                        $_GET['import_id']
                                    ) ?>';return false" name="cancel"><?= txt('Annuler') ?></button>
                                    <?
                                } elseif (isset($_GET['document_edition']) || $is_force_read_only) {
                                    ?>
                                    <button onclick="closePopup();return false;" name="cancel"><?= txt(
                                            'Fermer'
                                        ) ?></button>
                                    <?
                                } else {
                                    ?>
                                    <button onclick="closePopup();return false;" name="cancel"><?= txt(
                                            'Annuler'
                                        ) ?></button>
                                    <?
                                }
                                if (!isDBMaster() && $table <> 'usagers' && $isValidationBtns && $obj->isValide == 0) {
                                    ?>
                                    <span style="float: right;">
							<button class="submit" value="submitbuttonvalue" name="accept"><?= txt(
                                    'Accepter'
                                ) ?></button>
							<button class="submit" value="submitbuttonvalue" name="refuse"><?= txt(
                                    'Refuser'
                                ) ?></button>
						</span>
                                    <?
                                }
                                ?>
                            </div>
                        </section>
                    </fieldset>
                    <?
                    if (!isset($_GET['document_edition'])) {
                    ?>
                </form>
            <?
            }
            ?>
        </div>
        <script>
            <?
            if ($is_force_read_only)
            {
            ?>
			$(document).find('input').addClass('readonly');
			$(document).find('select').addClass('readonly');
			$(document).find('textarea').addClass('readonly');
			$(document).find('input').attr('placeholder', '');
			$(document).find('a').addClass('readonly').css({opacity: '0', cursor: 'default'});
			$(document).find('select').find('option[value=""]').html('');
			$(document).find('.fas').addClass('readonly').css({opacity: '0', cursor: 'default'});
			$('#listeFiches_to').removeClass('readonly').addClass('readonly_scrollable');
			$(document).find('input[type="checkbox"]').each(function () {
				if (this.checked) {
					$(this).replaceWith('<div style="width: 13px; height: 13px; margin-left: 50px; display: inline-block;">x</div>');
				} else {
					$(this).replaceWith('<div style="width: 13px; height: 13px; margin-left: 50px; display: inline-block;"></div>');
				}
			});
            <?
            }
            ?>
        </script>
        <?
    }

    public static function traiteDocumentsMobiles($id = 0)
    {
        if (isset($_GET['fromMobile'])) {
            $imagesData = [];
            if (isset($_FILES['images']['tmp_name'])) {
                foreach ($_FILES['images']['tmp_name'] as $i => $tmp_name) {
                    if (is_uploaded_file($tmp_name)) {
                        $imagesData[] = [
                            'name' => $_FILES['images']['name'][$i],
                            'type' => $_FILES['images']['type'][$i],
                            'tmp_name' => $_FILES['images']['tmp_name'][$i],
                            'error' => $_FILES['images']['error'][$i],
                            'size' => $_FILES['images']['size'][$i],
                        ];
                    }
                }
            }

            if (count($imagesData)) {
                foreach ($imagesData as $imageData) {
                    if ($_GET['fromMobile'] == 'equipements') {
                        DocumentsListesElements::add('fiches', intval($_POST['fiche_id']), $imageData);
                    } else if ($_GET['fromMobile'] == 'inventaire') {
                        DocumentsListesElements::add('inventaires_lignes', intval($_POST['inventaire_ligne_id']), $imageData);
                    } else if ($_GET['fromMobile'] == 'inventaires_locaux') {
                        DocumentsListesElements::add('inventaires_locaux', $id, $imageData);
                    }
                }
            }
        }
    }
}
<?php
class ProcedureEntretienPlanifieeFiche extends DbRow {
	public $id;
	public $procedure_entretien_planifiee_id;
	public $fiche_id;
	public $code;
	
	protected function setDataForId($id)
	{
        global $db;
        
		$sql = "select *
                from procedures_entretiens_planifiees_fiches 
                join fiches on fiches.id = fiche_id
                where id = ?";
		$row = $db->getRow($sql, array($id));
		
		$this->populateThisWithThat($row);
	}
	
	public static function getListe($filter = array(), $orderBy = 'code')
	{
        global $db;
        
        $where = '';
        $data = array();
        
        if(isset($filter['procedure_entretien_planifiee_id']) && intval($filter['procedure_entretien_planifiee_id']) > 0)
        {
            $where .= ' and procedure_entretien_planifiee_id = ?';
            $data[] = intval($filter['procedure_entretien_planifiee_id']);
        }
        
		$sql = "select *
                from procedures_entretiens_planifiees_fiches 
                join fiches on fiches.id = fiche_id
                where true $where
                order by $orderBy";
		$rows = $db->getAll($sql, $data);
		
		$liste = array();
		foreach($rows as $i => $row)
		{
			$liste[intval($row['fiche_id'])] = new self(intval($row['fiche_id']), $row);
		}
		
		return $liste;
	}
}
?>
<?php
class ProcedureInspectionPlanifieeFiche extends DbRow {
	public $id;
	public $procedure_inspection_planifiee_id;
	public $fiche_id;
	public $code;
	public $ajout_usager_id;
	public $ajout_usager;
	public $modification_usager_id;
	public $modification_usager;
	public $ts_ajout;
	public $ts_modification;
    public $nb_taches_conformes;
    public $reponses_taches;
	
	protected function setDataForId($id)
	{
        global $db;
        
		$sql = "select procedures_inspections_planifiees_fiches.*, (select count(*) from procedures_inspections_planifiees_fiches_taches where procedure_inspection_planifiee_fiche_id = procedures_inspections_planifiees_fiches.id and statut_conformite = 1) as nb_taches_conformes,
                    concat(u1.prenom, ' ', u1.nom) as ajout_usager, concat(u2.prenom, ' ', u2.nom) as modification_usager
                from procedures_inspections_planifiees_fiches 
                join fiches on fiches.id = fiche_id
                left join usagers u1 on u1.id = ajout_usager_id
                left join usagers u2 on u2.id = modification_usager_id
                where procedures_inspections_planifiees_fiches.id = ?";
		$row = $db->getRow($sql, array($id));
		
		$this->populateThisWithThat($row);
        
        $sql = "select * from procedures_inspections_planifiees_fiches_taches where procedure_inspection_planifiee_fiche_id = ?";
        $reponses_taches_tmp = $db->getAll($sql, array($this->id));
        
        $this->reponses_taches = array();
        foreach($reponses_taches_tmp as $row)
        {
            $this->reponses_taches[$row['procedure_inspection_tache_id']] = $row;
        }
	}
	
	public static function getListe($filter = array(), $orderBy = 'code')
	{
        global $db;
        
        $where = '';
        $data = array();
        
        if(isset($filter['procedure_inspection_planifiee_id']) && intval($filter['procedure_inspection_planifiee_id']) > 0)
        {
            $where .= ' and procedure_inspection_planifiee_id = ?';
            $data[] = intval($filter['procedure_inspection_planifiee_id']);
        }
        
		$sql = "select procedures_inspections_planifiees_fiches.*, fiches.code, (select count(*) from procedures_inspections_planifiees_fiches_taches where procedure_inspection_planifiee_fiche_id = procedures_inspections_planifiees_fiches.id and statut_conformite = 1) as nb_taches_conformes,
                    concat(u1.prenom, ' ', u1.nom) as ajout_usager, concat(u2.prenom, ' ', u2.nom) as modification_usager
                from procedures_inspections_planifiees_fiches 
                join fiches on fiches.id = fiche_id
                left join usagers u1 on u1.id = ajout_usager_id
                left join usagers u2 on u2.id = modification_usager_id
                where true $where
                order by $orderBy";
		$rows = $db->getAll($sql, $data);
		
		$liste = array();
		foreach($rows as $i => $row)
		{
			$liste[intval($row['fiche_id'])] = new self(intval($row['fiche_id']), $row);
		}
		
		return $liste;
	}
	
    public function getEditorHTML($iteration = 0, $only_champs_denormalises = false)
	{
        $procedure_planif = new ProcedureInspectionPlanifiee($this->procedure_inspection_planifiee_id);
?>
<style>
    #blocDonnees input {
        position: relative;
        top: 0px!important;
        margin-left: 0px;
    }
    #blocDonnees label {
        width: 45px !important;
        padding: 0;
        display: inline-block;
        background: none;
        font-weight: 400;
        font-size: 16px;
        text-align: left;
    }
    #blocDonnees td {
        padding: 10px!important;
        border-left: 1px;
    }
</style>
        <fieldset>

            <input type="hidden" name="modification_usager_id" value="<?=getIdUsager()?>" />
            <input type="hidden" name="ts_modification" value="<?=date('Y-m-d m:i:s')?>" />

            <div id="blocDonnees" style="padding: 40px 0 0 30px;">
                <table>
                    <tr><th width="200">
                        <?=txt('Tâche')?>
                    </th><th width="300">
                        <?=txt('Commentaire')?>
                    </th><th width="100">
                        <?=txt('Conformité')?>
                    </th></tr>
<?
                    foreach($procedure_planif->taches as $tache)
                    {
                        $reponse_tache = isset($this->reponses_taches[$tache->id]) ? $this->reponses_taches[$tache->id] : array('commentaire' => '', 'statut_conformite' => 0);
?>
                        <tr><td>
                            <input type="hidden" name="procedure_inspection_tache_id[]" value="<?=$tache->id?>" />   
                            <input type="hidden" name="is_new[]" value="<?=$tache->id?>" />   
                            <?=$tache->nom?>
                        </td><td>
                            <textarea name="commentaire[]" style="height: 18px; resize: none; word-wrap: break-word; overflow: hidden; position: relative; top: 2px;" /><?=($reponse_tache['commentaire'])?></textarea>
                        </td><td style="text-align: center!important;">
                            <input type="radio" id="validation_oui_<?=$tache->id?>" value="1" name="statut_conformite_<?=$tache->id?>"<?=($reponse_tache['statut_conformite'] == 1 ? ' checked="checked"' : '')?> /><label for="validation_oui_<?=$tache->id?>">&nbsp;<?=txt('Oui')?></label>
                            <input type="radio" id="validation_non_<?=$tache->id?>" value="-1" name="statut_conformite_<?=$tache->id?>"<?=($reponse_tache['statut_conformite'] == -1 ? ' checked="checked"' : '')?> /><label for="validation_non_<?=$tache->id?>">&nbsp;<?=txt('Non')?></label>
                        </td></tr>
<?
                    }
?>
                </table>
            </div>
                
        </fieldset>
<?php
	}
    
    public static function validate()
    {
        return true;
    }

    public static function traitementSauvegardeSupplementaire($id, $data, $is_ajout, $objAnciennesValeurs = null)
    {
        global $db;
        $sql = "delete FROM procedures_inspections_planifiees_fiches_taches WHERE procedure_inspection_planifiee_fiche_id = ?";
        $db->query($sql, array($id));

        if (isset($data['procedure_inspection_tache_id']) && count($data['procedure_inspection_tache_id']) > 0) {
            foreach ($data['procedure_inspection_tache_id'] as $ordre => $procedure_inspection_tache_id) {
                $sql = "INSERT INTO procedures_inspections_planifiees_fiches_taches (procedure_inspection_tache_id, procedure_inspection_planifiee_fiche_id, commentaire, statut_conformite) 
                                VALUES (?, ?, ?, ?)";
                $db->query($sql, array($procedure_inspection_tache_id, $id, $data['commentaire'][$ordre], (isset($data['statut_conformite_' . $procedure_inspection_tache_id]) ? intval($data['statut_conformite_' . $procedure_inspection_tache_id]) : 0)));
            }
        }
    }
}
?>
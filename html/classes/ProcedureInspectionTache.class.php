<?php
class ProcedureInspectionTache extends DbRow {
	public $id;
	public $procedure_id;
	public $nom;
	public $ordre;
	public $type_saisie;
	
	protected function setDataForId($id)
	{
        global $db;
        
		$sql = "select *
                from procedures_inspections_taches 
                where id = ?";
		$row = $db->getRow($sql, array($id));
		
		$this->populateThisWithThat($row);
	}
	
	public static function getListe($filter = array(), $orderBy = 'ordre')
	{
        global $db;
        
        $where = '';
        $data = array();
        
        if(isset($filter['procedure_id']) && intval($filter['procedure_id']) > 0)
        {
            $where .= ' and procedure_id = ?';
            $data[] = intval($filter['procedure_id']);
        }
        
		$sql = "select *
                from procedures_inspections_taches 
                where true $where
                order by $orderBy";
		$rows = $db->getAll($sql, $data);
		
		$liste = array();
		foreach($rows as $i => $row)
		{
			$liste[intval($row['id'])] = new self(intval($row['id']), $row);
		}
		
		return $liste;
	}
}
?>
<?php
class ProcedureMiseEnServiceMepTache extends DbRow {
	public $id;
	public $procedure_id;
	public $nom;
	public $ordre;
	public $type_saisie;
	
	protected function setDataForId($id)
	{
        global $db;
        
		$sql = "select *
                from procedures_mises_en_service_mep_taches 
                where id = ?";
		$row = $db->getRow($sql, array($id));
		
		$this->populateThisWithThat($row);
	}
	
	public static function getListe($filter = array(), $orderBy = 'ordre')
	{
        global $db;
        
        $where = '';
        $data = array();
        
        if(isset($filter['procedure_id']))
        {
            if (is_array($filter['procedure_id'])) {
                $where .= ' and procedure_id in (' . db::placeHolders($filter['procedure_id']) . ')';
                $data = array_merge($data, $filter['procedure_id']);
            } else if (intval($filter['procedure_id']) > 0) {
                $where .= ' and procedure_id = ?';
                $data[] = intval($filter['procedure_id']);
            } else {
                $where .= ' and false';
            }
        }
        
		$sql = "select *
                from procedures_mises_en_service_mep_taches 
                where true $where
                order by $orderBy";
		$rows = $db->getAll($sql, $data);
		
		$liste = array();
		foreach($rows as $i => $row)
		{
			$liste[intval($row['id'])] = new self(intval($row['id']), $row);
		}
		
		return $liste;
	}
}
?>
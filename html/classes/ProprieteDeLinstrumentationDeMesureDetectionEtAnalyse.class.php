<?
class ProprieteDeLinstrumentationDeMesureDetectionEtAnalyse extends FicheFormWrapper {
    private static $CLASS_NAME='ProprieteDeLinstrumentationDeMesureDetectionEtAnalyse';
    const SQL_TABLE_NAME='propriete_de_linstrumentation_de_mesure_detection_et_analyse';
    const TITRE='Propriétés de l\'instrumentation de mesure, détection et analyse';
    public static $AUTO=true;
    public static $TYPE_FORM='LISTE_COORDINATION';
    public $id;
    public $fiche_id;
    public $fonction_de_linstrument_id;     
    public $fonction_de_linstrument; 
    public $element_traite_id;     
    public $element_traite; 
    public $diametre_nominal_id;     
    public $diametre_nominal; 
    public $type_de_raccord_id;     
    public $type_de_raccord; 
    public $valeur_minimale;     
    public $valeur_maximale;     
    public $unite_id;
    public $unite;     
    public $type_de_signal_id;     
    public $type_de_signal; 
    public $nomenclature_id;     
    public $nomenclature; 
    public $qte_requise;     
    public $quantite; 
    public $conformite_du_besoin;     
    public $instrument_id;     
    public $instrument; 
    public $usager_exigence_id;
    public $ts_exigence;
    public $usager_solution_id;
    public $ts_solution;
    public $usager_solution;
    public $usager_exigence;
    public $conformite_du_besoin_id;
    public $nom_du_raccord;

    public static function getChampsSaisieMetaData()
    {
        return array(
            /*'regroupements' => array(
            1   => array(
                'titre' => '',
                'data'  =>  array(*/

                    'nom_du_raccord' => array(

                        'classe'      	=> '',
                        'table'      	=> '',
                        'champ_id'      => 'nom_du_raccord',
                        'type'          => 'input',
                        'format'        => 'text',
                        'titre'         => txt('Nom du raccord', false),
                        'css_editeur'   => 'medium',

                    ),

                    'fonction_de_linstrument' => array(

                        'classe'      	=> 'InstrumentsFonctions',
                        'table'      	=> 'instruments_fonctions',
                        'champ_id'      => 'fonction_de_linstrument_id',
                        'type'          => 'select',
                        'format'        => 'int',
                        'titre'         => txt('Fonction de l\'instrument (utilité concrète)', false),
                        'css_editeur'   => 'medium',

                    ),
                    'element_traite' => array(

                        'classe'      	=> 'Element',
                        'table'      	=> 'elements',
                        'champ_id'      => 'element_traite_id',
                        'type'          => 'select',
                        'format'        => 'int',
                        'titre'         => txt('Élément traité', false),
                        'css_editeur'   => 'medium',
                        'extra_filtre'  => array('element_type_id' => 10),

                    ),
                    'type_de_raccord' => array(

                        'classe'      	=> 'RaccordsPlomberiesTypes',
                        'table'      	=> 'raccords_plomberies_types',
                        'champ_id'      => 'type_de_raccord_id',
                        'type'          => 'select',
                        'format'        => 'int',
                        'titre'         => txt('Type de raccord (raccord au procédé)', false),
                        'css_editeur'   => 'medium',

                    ),
                    'diametre_nominal' => array(

                        'classe'      	=> 'DiametresConduitsPlomberies',
                        'table'      	=> 'diametres_conduits_plomberies',
                        'champ_id'      => 'diametre_nominal_id',
                        'type'          => 'select',
                        'format'        => 'int',
                        'titre'         => txt('Diamètre nominal (raccord au procédé)', false),
                        'css_editeur'   => 'medium',
                        'sql_for_nom'   => "case when metrique is not null and imperial is not null
                                                then concat(metrique, ' (', imperial, ')')
                                            when metrique is not null and imperial is null
                                                then metrique
                                            when metrique is null and imperial is not null
                                                then imperial
                                            end",

                    ),
                    'valeur_minimale' => array(

                        'classe'      	=> '',
                        'table'      	=> '',
                        'champ_id'      => 'valeur_minimale',
                        'type'          => 'input',
                        'format'        => 'float',
                        'titre'         => txt('Valeur minimale (substance)', false),
                        'css_editeur'   => 'tiny',

                    ),
                    'valeur_maximale' => array(

                        'classe'      	=> '',
                        'table'      	=> '',
                        'champ_id'      => 'valeur_maximale',
                        'type'          => 'input',
                        'format'        => 'float',
                        'titre'         => txt('Valeur maximale (substance)', false),
                        'css_editeur'   => 'tiny',

                    ),
                    'unite' => array(

                        'classe'      	=> 'Unite',
                        'table'      	=> 'unites',
                        'champ_id'      => 'unite_id',
                        'type'          => 'select',
                        'format'        => 'int',
                        'titre'         => txt('Unité de mesure', false),
                        'css_editeur'   => 'tiny',
                        
                    ),
                    'type_de_signal' => array(

                        'classe'      	=> 'SignauxTypes',
                        'table'      	=> 'signaux_types',
                        'champ_id'      => 'type_de_signal_id',
                        'type'          => 'select',
                        'format'        => 'int',
                        'titre'         => txt('Type de signal', false),
                        'css_editeur'   => 'medium',

                    ),
                    'nomenclature' => array(

                        'classe'      	=> 'Nomenclature',
                        'table'      	=> 'nomenclatures',
                        'champ_id'      => 'nomenclature_id',
                        'type'          => 'select',
                        'format'        => 'int',
                        'titre'         => txt('Nomenclature', false),
                        'css_editeur'   => 'medium',
                        'extra_filtre'  => array('discipline_id' => array(11))

                    ),
					'qte_requise' => array(
						'classe'      	=> '',
						'table'      	=> '',
						'champ_id'      => 'qte_requise',
						'type'          => 'input',
						'format'        => 'int',
						'titre'         => txt('Quantité', false),
						'css_editeur'   => 'tiny',
                        'hidden'        => 1,
                        'default_value' => 1,
					),
               /* )
            ),
            2   => array(
                'titre' => '',
                'data'  =>  array(*/

                    'conformite_du_besoin' => array(

                        'classe'      	=> 'OuisNons',
                        'table'      	=> 'ouis_nons',
                        'champ_id'      => 'conformite_du_besoin_id',
                        'type'          => 'select',
                        'format'        => 'int',
                        'titre'         => txt('Conformité du besoin', false),
                        'css_editeur'   => 'tiny',
                        'discipline_can_edit'   => 11,
                        'is_reponse_coord'   => 1,

                    ),
                    'instrument' => array(

                        'classe'      	=> 'Fiches',
                        'table'      	=> 'fiches',
                        'champ_id'      => 'instrument_id',
                        'type'          => 'select',
                        'format'        => 'int',
                        'titre'         => txt('Instrument', false),
                        'css_editeur'   => 'medium',
                        'discipline_can_edit'   => 11,
                        'is_reponse_coord'   => 1,
                        'extra_filtre'  => array('fiche_type_id' => 2000000, 'no_filtre_global' => 1),
                        'sql_for_nom'   => 'code',

                    ),
            
                    'usager_exigence' => array(

                        'classe'      	=> '',
                        'table'      	=> '',
                        'champ_id'      => 'usager_exigence_id',
                        'type'          => 'input',
                        'format'        => 'text',
                        'titre'         => '',
                        'css_editeur'   => 'large',
                        'hidden'        => 1,
                        'is_reponse_coord'   => 1,
                    ),
            
                    'ts_exigence' => array(

                        'classe'      	=> '',
                        'table'      	=> '',
                        'champ_id'      => 'ts_exigence',
                        'type'          => 'input',
                        'format'        => 'text',
                        'titre'         => '',
                        'css_editeur'   => 'large',
                        'hidden'        => 1,
                        'is_reponse_coord'   => 1,
                    ),
            
                    'usager_solution_id' => array(

                        'classe'      	=> '',
                        'table'      	=> '',
                        'champ_id'      => 'usager_solution_id',
                        'type'          => 'input',
                        'format'        => 'text',
                        'titre'         => '',
                        'css_editeur'   => 'large',
                        'hidden'        => 1,
                        'is_reponse_coord'   => 1,
                    ),
            
                    'ts_solution' => array(

                        'classe'      	=> '',
                        'table'      	=> '',
                        'champ_id'      => 'ts_solution',
                        'type'          => 'input',
                        'format'        => 'text',
                        'titre'         => '',
                        'css_editeur'   => 'large',
                        'hidden'        => 1,
                        'is_reponse_coord'   => 1,
                    ),
 
              /*  )
            ))*/
        );
    }
}

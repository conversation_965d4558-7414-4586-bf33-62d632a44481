<?
class ProprietesElectriqueAerothermeElectrique extends FicheFormWrapper {
    private static $CLASS_NAME='ProprietesElectriqueAerothermeElectrique';
    const SQL_TABLE_NAME='proprietes_electrique_aerotherme_electrique';
    const TITRE='Propriétés mécaniques - Aérotherme électrique';
    public static $AUTO=true;
	public static $TYPE_FORM='UNIQUE';
    public $id;
    public $fiche_id;
    public $capacite_calculee;     
    public $capacite_installee;     
    public $longueur_projection;     

    public static function getChampsSaisieMetaData()
    {
        return array(

            'capacite_calculee' => array(

                'classe'      	=> '',
                'table'      	=> '',
                'champ_id'      => 'capacite_calculee',
                'type'          => 'input',
                'format'        => 'float',
                'titre'         => txt('Capacité (calculée)', false),
                'css_editeur'   => 'small',
				'unite'         => 'W',

            ),
            'capacite_installee' => array(

                'classe'      	=> '',
                'table'      	=> '',
                'champ_id'      => 'capacite_installee',
                'type'          => 'input',
                'format'        => 'float',
                'titre'         => txt('Capacité (installée)', false),
                'css_editeur'   => 'small',
				'unite'         => 'W',

            ),
            'longueur_projection' => array(

                'classe'      	=> '',
                'table'      	=> '',
                'champ_id'      => 'longueur_projection',
                'type'          => 'input',
                'format'        => 'float',
                'titre'         => txt('Longueur projection', false),
                'css_editeur'   => 'small',
				'unite'         => 'mm',

            ),
            'commentaire' => array(
				'classe'      	=> '',
				'table'      	=> '',
				'champ_id'      => 'commentaire',
				'type'          => 'textarea',
                'format'        => 'text',
                'titre'         => txt('Commentaires', false),
                'css_editeur'   => 'xxlarge'
            ),

        );
    }
    public static function getListe($filter = array(), $orderBy = 'id', $extra_data = null)
    {
        return parent::getListe($filter, $orderBy, $extra_data);
    }

    public static function getFormHTML($fiche_id, $liste_data = null, $forced_liste = null)
    {
        return parent::getUniqueFormHTML($fiche_id, $liste_data);
    }

    public function getEditorHTML($iteration = 0, $only_champs_denormalises = false)
    {
?>
        <style>
            #editables label {
                width: 275px !important;
            }
            .edit_popupContent section > div {
                width: 690px !important;
            }
            #editables input[type=checkbox] {
                position: relative;
                top: 1px;
            }
        </style>
<?
        parent::getEditorHTML($iteration);
    }

    public static function getFicheHTML($fiche, $liste = null, $nb_archives = 0, $can_edit = false, $projet_courant_id = null, $gabarit = null)
    {
?>
        <style>
            .blocsFiche section label {
                width: 260px !important;
            }
        </style>
<?
        if(isset($liste) && is_array($liste))
        {
            $liste_tmp = array_values($liste);
            if(isset($liste_tmp[0]))
            {
                $obj = $liste_tmp[0];
            }
            else
            {
                $currentClassName = get_called_class();
                $obj = $currentClassName::getOne($fiche->id);
            }
        }
        else
        {
            $currentClassName = get_called_class();
            $obj = $currentClassName::getOne($fiche->id);
        }

        parent::getUniqueFicheHTML($fiche, $obj, $nb_archives, $can_edit, $projet_courant_id);

        parent::getAutoFicheHtml($fiche->id, $obj);
    }

    public static function getDiffArchiveHTML($fiche_id, $liste, $last_liste, $token_archive, $last_token_archive)
    {
?>
        <style>
            .blocsFiche section label {
                width: 260px !important;
            }
        </style>
<?
        $liste = array_values($liste);
        $last_liste = array_values($last_liste);
        if(isset($liste[0]))
        {
            $obj = $liste[0];
        }

        if(isset($last_liste[0]))
        {
            $last_obj = $last_liste[0];
        }
        else
        {
            $last_obj = new self(0);
        }

?>
        <h2 class="blocsFicheTitre" style="height: 30px;">
            <?=txt(self::TITRE)?></h2>
<?

        parent::getAutoDiffArchiveHtml($fiche_id, $obj, $last_obj, $token_archive, $last_token_archive);
    }

    public function getFiltreHTML()
    {
?>
        <fieldset class="noMinHeight">
<?
            $this->getEditorHTML();
?>
        </fieldset>
<?
    }
}

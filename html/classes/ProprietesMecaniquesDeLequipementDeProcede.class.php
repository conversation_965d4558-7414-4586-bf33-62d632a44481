<?
class ProprietesMecaniquesDeLequipementDeProcede extends FicheFormWrapper {
    private static $CLASS_NAME='ProprietesMecaniquesDeLequipementDeProcede';
    const SQL_TABLE_NAME='proprietes_mecaniques_de_lequipement_de_procede';
    const TITRE='Propriétés mécaniques de l\'équipement de procédé';
    public static $AUTO=true;
    public static $TYPE_FORM='UNIQUE';
    public $id;
    public $fiche_id;
    public $vitesse_de_rotation_max_rpm;     
    public $base_de_proprete_on_id;     
    public $base_de_proprete_on; 
    public $cadences_valeur;     
    public $cadences_types_id;     
    public $cadences_types; 
    public $rendement_valeur;     
    public $rendement_type_id;     
    public $rendement_type; 
    public $hauteur_de_convoyeur;     
    public $vitesse_de_lineaire_max_pimin;     
    public $capacite_de_charge_lbs;     
    public $commentaire;      

    public static function getChampsSaisieMetaData()
    {
        $return_array = array(

            'vitesse_de_rotation_max_rpm' => array(

                'classe'      	=> '',
                'table'      	=> '',
                'champ_id'      => 'vitesse_de_rotation_max_rpm',
                'type'          => 'input',
                'format'        => 'float',
                'titre'         => txt('Vitesse de rotation maximale', false),
                'css_editeur'   => 'xxlarge',
        'unite'         => 'tpm',

            ),
            'cadences_valeur' => array(

                'classe'      	=> '',
                'table'      	=> '',
                'champ_id'      => 'cadences_valeur',
                'type'          => 'input',
                'format'        => 'float',
                'titre'         => txt('Cadences / capacités', false),
                'css_editeur'   => 'xxlarge',
                'champ_associe' => 'cadences_types'

            ),
            'cadences_types' => array(

                'classe'      	=> 'CadencesTypes',
                'table'      	=> 'cadences_types',
                'champ_id'      => 'cadences_types_id',
                'type'          => 'select',
                'format'        => 'int',
                'titre'         => txt('Cadence type', false),
                'css_editeur'   => 'xxlarge',

            ),
/*            
            'rendement_valeur' => array(

                'classe'      	=> '',
                'table'      	=> '',
                'champ_id'      => 'rendement_valeur',
                'type'          => 'input',
                'format'        => 'float',
                'titre'         => txt('Rendement valeur', false),
                'css_editeur'   => 'xxlarge',

            ),
            'rendement_type' => array(

                'classe'      	=> 'RendementsTypes',
                'table'      	=> 'rendements_types',
                'champ_id'      => 'rendement_type_id',
                'type'          => 'select',
                'format'        => 'int',
                'titre'         => txt('Rendement type', false),
                'css_editeur'   => 'xxlarge',

            ),
 */
            'hauteur_de_convoyeur' => array(

                'classe'      	=> '',
                'table'      	=> '',
                'champ_id'      => 'hauteur_de_convoyeur',
                'type'          => 'input',
                'format'        => 'float',
                'titre'         => txt('Hauteur de convoyeur', false),
                'css_editeur'   => 'xxlarge',
        'unite'         => 'mm',

            ),
            'vitesse_de_lineaire_max_pimin' => array(

                'classe'      	=> '',
                'table'      	=> '',
                'champ_id'      => 'vitesse_de_lineaire_max_pimin',
                'type'          => 'input',
                'format'        => 'float',
                'titre'         => txt('Vitesse de linéaire maximale', false),
                'css_editeur'   => 'xxlarge',
        'unite'         => 'mm/s',

            ),
            'capacite_de_charge_lbs' => array(

                'classe'      	=> '',
                'table'      	=> '',
                'champ_id'      => 'capacite_de_charge_lbs',
                'type'          => 'input',
                'format'        => 'float',
                'titre'         => txt('Capacité de charge', false),
                'css_editeur'   => 'xxlarge',
        'unite'         => 'Kg',

            ),
            'commentaire' => array(
                'classe'      	=> '',
                'table'      	=> '',
                'champ_id'      => 'commentaire',
                'type'          => 'textarea',
                'format'        => 'text',
                'titre'         => txt('Commentaires', false),
                'css_editeur'   => 'xxlarge'
            ),

		);
		return parent::parseMetaData($return_array);
	}

	public static function getListe($filter = array(), $orderBy = 'id', $extra_data = null)
    {
        return parent::getListe($filter, $orderBy, $extra_data);
    }

    public static function getFormHTML($fiche_id, $liste_data = null, $forced_liste = null)
    {
        return parent::getUniqueFormHTML($fiche_id, $liste_data);
    }

    public function getEditorHTML($iteration = 0, $only_champs_denormalises = false)
    {
?>
        <style>
            #editables label {
                width: 275px !important;
            }
            .edit_popupContent section > div {
                width: 690px !important;
            }
            #editables input[type=checkbox] {
                position: relative;
                top: 1px;
            }
        </style>
<?
        parent::getEditorHTML($iteration);
    }

    public static function getFicheHTML($fiche, $liste = null, $nb_archives = 0, $can_edit = false, $projet_courant_id = null, $gabarit = null)
    {
?>
        <style>
            .blocsFiche section label {
                width: 260px !important;
            }
        </style>
<?
        if(isset($liste) && is_array($liste))
        {
            $liste_tmp = array_values($liste);
            if(isset($liste_tmp[0]))
            {
                $obj = $liste_tmp[0];
            }
            else
            {
                $currentClassName = get_called_class();
                $obj = $currentClassName::getOne($fiche->id);
            }
        }
        else
        {
            $currentClassName = get_called_class();
            $obj = $currentClassName::getOne($fiche->id);
        }

        parent::getUniqueFicheHTML($fiche, $obj, $nb_archives, $can_edit, $projet_courant_id);

        parent::getAutoFicheHtml($fiche->id, $obj);
    }

    public static function getDiffArchiveHTML($fiche_id, $liste, $last_liste, $token_archive, $last_token_archive)
    {
?>
        <style>
            .blocsFiche section label {
                width: 260px !important;
            }
        </style>
<?
        $liste = array_values($liste);
        $last_liste = array_values($last_liste);
        if(isset($liste[0]))
        {
            $obj = $liste[0];
        }

        if(isset($last_liste[0]))
        {
            $last_obj = $last_liste[0];
        }
        else
        {
            $last_obj = new self(0);
        }

?>
        <h2 class="blocsFicheTitre" style="height: 30px;">
            <?=txt(self::TITRE)?></h2>
<?

        parent::getAutoDiffArchiveHtml($fiche_id, $obj, $last_obj, $token_archive, $last_token_archive);
    }

    public function getFiltreHTML()
    {
?>
        <fieldset class="noMinHeight">
<?
            $this->getEditorHTML();
?>
        </fieldset>
<?
    }
}

<?
class ProprietesMecaniquesEchangeurAPlaque extends FicheFormWrapper {
    private static $CLASS_NAME='ProprietesMecaniquesEchangeurAPlaque';
    const SQL_TABLE_NAME='proprietes_mecaniques_echangeur_a_plaque';
    const TITRE='Propriétés mécaniques - Échangeur eau-vapeur';
    public static $AUTO=true;
    public static $TYPE_FORM='UNIQUE';
    public $id;
    public $fiche_id;
    public $heat_exchanged;     
    public $cool_heat_transfer_fluid_steam_kpa;     
    public $cool_heat_transfer_fluid_water_;     
    public $cool_heat_transfer_fluid_ethylene_;     
    public $cool_heat_transfer_fluid_propylene_;     
    public $cool_steam_flow_kgh;     
    public $cool_flow;     
    public $cool_inlet_temperature;     
    public $cool_outlet_temperature;     
    public $cool_pressure_drop;     
    public $hot_heat_transfer_fluid_steam_kpa;     
    public $hot_heat_transfer_fluid_water_;     
    public $hot_heat_transfer_fluid_ethylene_;     
    public $hot_heat_transfer_fluid_propylene_;     
    public $hot_steam_flow_kgh;     
    public $hot_flow;     
    public $hot_inlet_temperature;     
    public $hot_outlet_temperature;     
    public $hot_pressure_drop;     
    public $cool_inlet_diameter_id;     
    public $cool_inlet_diameter; 
    public $hot_inlet_diameter_id;     
    public $hot_inlet_diameter; 
    public $cool_inlet_fitting_type_id;     
    public $cool_inlet_fitting_type; 
    public $hot_inlet_fitting_type_id;     
    public $hot_inlet_fitting_type; 
    public $diametreconnecteurcotefroid;     
    public $diametreconnecteurcote_chaud;     
    public $commentaire;      

    public static function getChampsSaisieMetaData()
    {
        $return_array = array(

            'heat_exchanged' => array(

                'classe'      	=> '',
                'table'      	=> '',
                'champ_id'      => 'heat_exchanged',
                'type'          => 'input',
                'format'        => 'float',
                'titre'         => txt('Chaleure échangée', false),
                'css_editeur'   => 'xxlarge',
        'unite'         => 'kW',

            ),
            'cool_heat_transfer_fluid_steam_kpa' => array(

                'classe'      	=> '',
                'table'      	=> '',
                'champ_id'      => 'cool_heat_transfer_fluid_steam_kpa',
                'type'          => 'input',
                'format'        => 'float',
                'titre'         => txt('Pression de vapeur (froid)', false),
                'css_editeur'   => 'xxlarge',
        'unite'         => 'kPa',

            ),
/*			
            'cool_heat_transfer_fluid_water_' => array(

                'classe'      	=> '',
                'table'      	=> '',
                'champ_id'      => 'cool_heat_transfer_fluid_water_',
                'type'          => 'input',
                'format'        => 'float',
                'titre'         => txt('Pourcentage d\'eau (froid)', false),
                'css_editeur'   => 'xxlarge',
        'unite'         => '%',

            ),
 * 
 */
            'cool_heat_transfer_fluid_ethylene_' => array(

                'classe'      	=> '',
                'table'      	=> '',
                'champ_id'      => 'cool_heat_transfer_fluid_ethylene_',
                'type'          => 'input',
                'format'        => 'float',
                'titre'         => txt('Pourcentage d\'éthylène-glycol (froid)', false),
                'css_editeur'   => 'xxlarge',
        'unite'         => '%',

            ),
            'cool_heat_transfer_fluid_propylene_' => array(

                'classe'      	=> '',
                'table'      	=> '',
                'champ_id'      => 'cool_heat_transfer_fluid_propylene_',
                'type'          => 'input',
                'format'        => 'float',
                'titre'         => txt('Pourcentage de propylène-glycol (froid)', false),
                'css_editeur'   => 'xxlarge',
        'unite'         => '%',

            ),
            'cool_steam_flow_kgh' => array(

                'classe'      	=> '',
                'table'      	=> '',
                'champ_id'      => 'cool_steam_flow_kgh',
                'type'          => 'input',
                'format'        => 'float',
                'titre'         => txt('Débit de vapeur (froid)', false),
                'css_editeur'   => 'xxlarge',
        'unite'         => 'kg/h',

            ),
/*			
            'cool_flow' => array(

                'classe'      	=> '',
                'table'      	=> '',
                'champ_id'      => 'cool_flow',
                'type'          => 'input',
                'format'        => 'float',
                'titre'         => txt('Débit de liquide (froid)', false),
                'css_editeur'   => 'xxlarge',
        'unite'         => 'l/s',

            ),
 * 
 */
            'cool_inlet_temperature' => array(

                'classe'      	=> '',
                'table'      	=> '',
                'champ_id'      => 'cool_inlet_temperature',
                'type'          => 'input',
                'format'        => 'float',
                'titre'         => txt('Température à l\'entrée (froid)', false),
                'css_editeur'   => 'xxlarge',
        'unite'         => '°C',

            ),
            'cool_outlet_temperature' => array(

                'classe'      	=> '',
                'table'      	=> '',
                'champ_id'      => 'cool_outlet_temperature',
                'type'          => 'input',
                'format'        => 'float',
                'titre'         => txt('Température à la sortie (froid)', false),
                'css_editeur'   => 'xxlarge',
        'unite'         => '°C',

            ),
            'cool_pressure_drop' => array(

                'classe'      	=> '',
                'table'      	=> '',
                'champ_id'      => 'cool_pressure_drop',
                'type'          => 'input',
                'format'        => 'float',
                'titre'         => txt('Perte de pression (froid)', false),
                'css_editeur'   => 'xxlarge',
        'unite'         => 'kPa',

            ),
            'hot_heat_transfer_fluid_steam_kpa' => array(

                'classe'      	=> '',
                'table'      	=> '',
                'champ_id'      => 'hot_heat_transfer_fluid_steam_kpa',
                'type'          => 'input',
                'format'        => 'float',
                'titre'         => txt('Pression vapeur (chaud)', false),
                'css_editeur'   => 'xxlarge',
        'unite'         => 'kPa',

            ),
/*			
            'hot_heat_transfer_fluid_water_' => array(

                'classe'      	=> '',
                'table'      	=> '',
                'champ_id'      => 'hot_heat_transfer_fluid_water_',
                'type'          => 'input',
                'format'        => 'float',
                'titre'         => txt('Pourcentage d\'eau (chaud)', false),
                'css_editeur'   => 'xxlarge',
        'unite'         => '%',

            ),
 * 
 */
            'hot_heat_transfer_fluid_ethylene_' => array(

                'classe'      	=> '',
                'table'      	=> '',
                'champ_id'      => 'hot_heat_transfer_fluid_ethylene_',
                'type'          => 'input',
                'format'        => 'float',
                'titre'         => txt('Pourcentage d\'éthylène-glycol (chaud)', false),
                'css_editeur'   => 'xxlarge',
        'unite'         => '%',

            ),
            'hot_heat_transfer_fluid_propylene_' => array(

                'classe'      	=> '',
                'table'      	=> '',
                'champ_id'      => 'hot_heat_transfer_fluid_propylene_',
                'type'          => 'input',
                'format'        => 'float',
                'titre'         => txt('Pourcentage de propylène-glycol (chaud)', false),
                'css_editeur'   => 'xxlarge',
        'unite'         => '%',

            ),
            'hot_steam_flow_kgh' => array(

                'classe'      	=> '',
                'table'      	=> '',
                'champ_id'      => 'hot_steam_flow_kgh',
                'type'          => 'input',
                'format'        => 'float',
                'titre'         => txt('Débit de vapeur (chaud)', false),
                'css_editeur'   => 'xxlarge',
        'unite'         => 'kg/h',

            ),
/*			
            'hot_flow' => array(

                'classe'      	=> '',
                'table'      	=> '',
                'champ_id'      => 'hot_flow',
                'type'          => 'input',
                'format'        => 'float',
                'titre'         => txt('Débit de liquide (chaud)', false),
                'css_editeur'   => 'xxlarge',
        'unite'         => 'l/s',

            ),
 * 
 */
            'hot_inlet_temperature' => array(

                'classe'      	=> '',
                'table'      	=> '',
                'champ_id'      => 'hot_inlet_temperature',
                'type'          => 'input',
                'format'        => 'float',
                'titre'         => txt('Température à l\'entrée (chaud)', false),
                'css_editeur'   => 'xxlarge',
        'unite'         => '°C',

            ),
            'hot_outlet_temperature' => array(

                'classe'      	=> '',
                'table'      	=> '',
                'champ_id'      => 'hot_outlet_temperature',
                'type'          => 'input',
                'format'        => 'float',
                'titre'         => txt('Température à la sortie (chaud)', false),
                'css_editeur'   => 'xxlarge',
        'unite'         => '°C',

            ),
            'hot_pressure_drop' => array(

                'classe'      	=> '',
                'table'      	=> '',
                'champ_id'      => 'hot_pressure_drop',
                'type'          => 'input',
                'format'        => 'float',
                'titre'         => txt('Perte de pression (chaud)', false),
                'css_editeur'   => 'xxlarge',
        'unite'         => 'kPa',

            ),
            'diametreconnecteurcotefroid' => array(

                'classe'      	=> '',
                'table'      	=> '',
                'champ_id'      => 'diametreconnecteurcotefroid',
                'type'          => 'input',
                'format'        => 'float',
                'titre'         => txt('Diamètre connecteur (côté froid)', false),
                'css_editeur'   => 'small',
				'unite'         => 'mm',

            ),
            'diametreconnecteurcote_chaud' => array(

                'classe'      	=> '',
                'table'      	=> '',
                'champ_id'      => 'diametreconnecteurcote_chaud',
                'type'          => 'input',
                'format'        => 'float',
                'titre'         => txt('Diamètre connecteur (côté chaud)', false),
                'css_editeur'   => 'small',
				'unite'         => 'mm',

            ),
            'commentaire' => array(
                'classe'      	=> '',
                'table'      	=> '',
                'champ_id'      => 'commentaire',
                'type'          => 'textarea',
                'format'        => 'text',
                'titre'         => txt('Commentaires', false),
                'css_editeur'   => 'xxlarge'
            ),

		);
		return parent::parseMetaData($return_array);
	}

	public static function getListe($filter = array(), $orderBy = 'id', $extra_data = null)
    {
        return parent::getListe($filter, $orderBy, $extra_data);
    }

    public static function getFormHTML($fiche_id, $liste_data = null, $forced_liste = null)
    {
        return parent::getUniqueFormHTML($fiche_id, $liste_data);
    }

    public function getEditorHTML($iteration = 0, $only_champs_denormalises = false)
    {
?>
        <style>
            #editables label {
                width: 275px !important;
            }
            .edit_popupContent section > div {
                width: 690px !important;
            }
            #editables input[type=checkbox] {
                position: relative;
                top: 1px;
            }
        </style>
<?
        parent::getEditorHTML($iteration);
    }

    public static function getFicheHTML($fiche, $liste = null, $nb_archives = 0, $can_edit = false, $projet_courant_id = null, $gabarit = null)
    {
?>
        <style>
            .blocsFiche section label {
                width: 260px !important;
            }
        </style>
<?
        if(isset($liste) && is_array($liste))
        {
            $liste_tmp = array_values($liste);
            if(isset($liste_tmp[0]))
            {
                $obj = $liste_tmp[0];
            }
            else
            {
                $currentClassName = get_called_class();
                $obj = $currentClassName::getOne($fiche->id);
            }
        }
        else
        {
            $currentClassName = get_called_class();
            $obj = $currentClassName::getOne($fiche->id);
        }

        parent::getUniqueFicheHTML($fiche, $obj, $nb_archives, $can_edit, $projet_courant_id);

        parent::getAutoFicheHtml($fiche->id, $obj);
    }

    public static function getDiffArchiveHTML($fiche_id, $liste, $last_liste, $token_archive, $last_token_archive)
    {
?>
        <style>
            .blocsFiche section label {
                width: 260px !important;
            }
        </style>
<?
        $liste = array_values($liste);
        $last_liste = array_values($last_liste);
        if(isset($liste[0]))
        {
            $obj = $liste[0];
        }

        if(isset($last_liste[0]))
        {
            $last_obj = $last_liste[0];
        }
        else
        {
            $last_obj = new self(0);
        }

?>
        <h2 class="blocsFicheTitre" style="height: 30px;">
            <?=txt(self::TITRE)?></h2>
<?

        parent::getAutoDiffArchiveHtml($fiche_id, $obj, $last_obj, $token_archive, $last_token_archive);
    }

    public function getFiltreHTML()
    {
?>
        <fieldset class="noMinHeight">
<?
            $this->getEditorHTML();
?>
        </fieldset>
<?
    }
}

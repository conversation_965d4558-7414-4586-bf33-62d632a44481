<?
class ProprietesMecaniquesMelangeurDair extends FicheFormWrapper {
    private static $CLASS_NAME='ProprietesMecaniquesMelangeurDair';
    const SQL_TABLE_NAME='proprietes_mecaniques_melangeur_dair';
    const TITRE='Propriétés mécaniques - Mélangeur d\'air';
    public static $AUTO=true;
    public static $TYPE_FORM='UNIQUE';
    public $id;
    public $fiche_id;
    public $airflow;     
    public $airflow_theoretical;     
    public $velocity;     
    public $cell_height;     
    public $cell_width;     
    public $nominal_depth;     
    public $quantity_id;     
    public $quantity; 
    public $nominal_width;     
    public $nominal_height;     
    public $inlet_straigth_length;     
    public $outlet_straigth_length;     
	public $commentaire;

	public static function getChampsSaisieMetaData()
    {
        $return_array = array(

            'airflow_theoretical' => array(

                'classe'      	=> '',
                'table'      	=> '',
                'champ_id'      => 'airflow_theoretical',
                'type'          => 'input',
                'format'        => 'float',
                'titre'         => txt('Débit d\'air (calculé)', false),
                'css_editeur'   => 'xxlarge',
				'unite'         => 'l/s',

            ),
            'airflow' => array(

                'classe'      	=> '',
                'table'      	=> '',
                'champ_id'      => 'airflow',
                'type'          => 'input',
                'format'        => 'float',
                'titre'         => txt('Débit d\'air (installé)', false),
                'css_editeur'   => 'xxlarge',
				'unite'         => 'l/s',

            ),
            'velocity' => array(

                'classe'      	=> '',
                'table'      	=> '',
                'champ_id'      => 'velocity',
                'type'          => 'input',
                'format'        => 'float',
                'titre'         => txt('Vélocité', false),
                'css_editeur'   => 'xxlarge',
        'unite'         => 'm/s',

            ),
            'cell_height' => array(

                'classe'      	=> '',
                'table'      	=> '',
                'champ_id'      => 'cell_height',
                'type'          => 'input',
                'format'        => 'float',
                'titre'         => txt('Hauteur (cellule)', false),
                'css_editeur'   => 'xxlarge',
        'unite'         => 'mm',

            ),
            'cell_width' => array(

                'classe'      	=> '',
                'table'      	=> '',
                'champ_id'      => 'cell_width',
                'type'          => 'input',
                'format'        => 'float',
                'titre'         => txt('Largeur (cellule)', false),
                'css_editeur'   => 'xxlarge',
        'unite'         => 'mm',

            ),
            'nominal_depth' => array(

                'classe'      	=> '',
                'table'      	=> '',
                'champ_id'      => 'nominal_depth',
                'type'          => 'input',
                'format'        => 'float',
                'titre'         => txt('Profondeur nominale (cellule)', false),
                'css_editeur'   => 'xxlarge',
        'unite'         => 'mm',

            ),

            'qte_requise' => array(
                'classe'      	=> '',
                'table'      	=> '',
                'champ_id'      => 'qte_requise',
                'type'          => 'input',
                'format'        => 'int',
                'titre'         => txt('Cellule(s) (Qté)', false),
                'css_editeur'   => 'tiny'
            ),

            'nominal_width' => array(

                'classe'      	=> '',
                'table'      	=> '',
                'champ_id'      => 'nominal_width',
                'type'          => 'input',
                'format'        => 'float',
                'titre'         => txt('Largeur nominale (mélangeur)', false),
                'css_editeur'   => 'xxlarge',
        'unite'         => 'mm',

            ),
            'nominal_height' => array(

                'classe'      	=> '',
                'table'      	=> '',
                'champ_id'      => 'nominal_height',
                'type'          => 'input',
                'format'        => 'float',
                'titre'         => txt('Hauteur nominale (mélangeur)', false),
                'css_editeur'   => 'xxlarge',
        'unite'         => 'mm',

            ),
            'inlet_straigth_length' => array(

                'classe'      	=> '',
                'table'      	=> '',
                'champ_id'      => 'inlet_straigth_length',
                'type'          => 'input',
                'format'        => 'float',
                'titre'         => txt('Longeur droite à l\'entrée', false),
                'css_editeur'   => 'xxlarge',
        'unite'         => 'mm',

            ),
            'outlet_straigth_length' => array(

                'classe'      	=> '',
                'table'      	=> '',
                'champ_id'      => 'outlet_straigth_length',
                'type'          => 'input',
                'format'        => 'float',
                'titre'         => txt('Longueure droite à la sortie', false),
                'css_editeur'   => 'xxlarge',
        'unite'         => 'mm',

            ),
            'commentaire' => array(
                'classe'      	=> '',
                'table'      	=> '',
                'champ_id'      => 'commentaire',
                'type'          => 'textarea',
                'format'        => 'text',
                'titre'         => txt('Commentaires', false),
                'css_editeur'   => 'xxlarge'
            ),

		);
		return parent::parseMetaData($return_array);
	}

	public static function getListe($filter = array(), $orderBy = 'id', $extra_data = null)
    {
        return parent::getListe($filter, $orderBy, $extra_data);
    }

    public static function getFormHTML($fiche_id, $liste_data = null, $forced_liste = null)
    {
        return parent::getUniqueFormHTML($fiche_id, $liste_data);
    }

    public function getEditorHTML($iteration = 0, $only_champs_denormalises = false)
    {
?>
        <style>
            #editables label {
                width: 275px !important;
            }
            .edit_popupContent section > div {
                width: 690px !important;
            }
            #editables input[type=checkbox] {
                position: relative;
                top: 1px;
            }
        </style>
<?
        parent::getEditorHTML($iteration);
    }

    public static function getFicheHTML($fiche, $liste = null, $nb_archives = 0, $can_edit = false, $projet_courant_id = null, $gabarit = null)
    {
?>
        <style>
            .blocsFiche section label {
                width: 260px !important;
            }
        </style>
<?
        if(isset($liste) && is_array($liste))
        {
            $liste_tmp = array_values($liste);
            if(isset($liste_tmp[0]))
            {
                $obj = $liste_tmp[0];
            }
            else
            {
                $currentClassName = get_called_class();
                $obj = $currentClassName::getOne($fiche->id);
            }
        }
        else
        {
            $currentClassName = get_called_class();
            $obj = $currentClassName::getOne($fiche->id);
        }

        parent::getUniqueFicheHTML($fiche, $obj, $nb_archives, $can_edit, $projet_courant_id);

        parent::getAutoFicheHtml($fiche->id, $obj);
    }

    public static function getDiffArchiveHTML($fiche_id, $liste, $last_liste, $token_archive, $last_token_archive)
    {
?>
        <style>
            .blocsFiche section label {
                width: 260px !important;
            }
        </style>
<?
        $liste = array_values($liste);
        $last_liste = array_values($last_liste);
        if(isset($liste[0]))
        {
            $obj = $liste[0];
        }

        if(isset($last_liste[0]))
        {
            $last_obj = $last_liste[0];
        }
        else
        {
            $last_obj = new self(0);
        }

?>
        <h2 class="blocsFicheTitre" style="height: 30px;">
            <?=txt(self::TITRE)?></h2>
<?

        parent::getAutoDiffArchiveHtml($fiche_id, $obj, $last_obj, $token_archive, $last_token_archive);
    }

    public function getFiltreHTML()
    {
?>
        <fieldset class="noMinHeight">
<?
            $this->getEditorHTML();
?>
        </fieldset>
<?
    }
}

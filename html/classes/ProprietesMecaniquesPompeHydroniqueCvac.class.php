<?
class ProprietesMecaniquesPompeHydroniqueCvac extends FicheFormWrapper {
    private static $CLASS_NAME='ProprietesMecaniquesPompeHydroniqueCvac';
    const SQL_TABLE_NAME='proprietes_mecaniques_pompe_hydronique_cvac';
    const TITRE='Propriétés mécaniques - Pompe hydronique CVAC';
    public static $AUTO=true;
    public static $TYPE_FORM='UNIQUE';
    public $id;
    public $fiche_id;
    public $fluid_type_id;     
    public $fluid_type; 
    public $fluid_poucentage;     
    public $operating_temperature;     
    public $design_flow_rate;     
    public $design_head;     
    public $maximum_speed;     
    public $pump_efficiency;     
	public $commentaire;

	public static function getChampsSaisieMetaData()
    {
        $return_array = array(

            'fluid_type' => array(

                'classe'      	=> 'FluidesTypes',
                'table'      	=> 'fluides_types',
                'champ_id'      => 'fluid_type_id',
                'type'          => 'select',
                'format'        => 'int',
                'titre'         => txt('Type de fluide', false),
                'css_editeur'   => 'xxlarge',

            ),
            'fluid_poucentage' => array(

                'classe'      	=> '',
                'table'      	=> '',
                'champ_id'      => 'fluid_poucentage',
                'type'          => 'input',
                'format'        => 'float',
                'titre'         => txt('Pourcentage de liquide', false),
                'css_editeur'   => 'xxlarge',
        'unite'         => '%',

            ),
            'operating_temperature' => array(

                'classe'      	=> '',
                'table'      	=> '',
                'champ_id'      => 'operating_temperature',
                'type'          => 'input',
                'format'        => 'float',
                'titre'         => txt('Température d\'opération', false),
                'css_editeur'   => 'xxlarge',
        'unite'         => '°C',

            ),
            'design_flow_rate' => array(

                'classe'      	=> '',
                'table'      	=> '',
                'champ_id'      => 'design_flow_rate',
                'type'          => 'input',
                'format'        => 'float',
                'titre'         => txt('Débit selon design', false),
                'css_editeur'   => 'xxlarge',
        'unite'         => 'l/s',

            ),
            'design_head' => array(

                'classe'      	=> '',
                'table'      	=> '',
                'champ_id'      => 'design_head',
                'type'          => 'input',
                'format'        => 'float',
                'titre'         => txt('Pression selon design', false),
                'css_editeur'   => 'xxlarge',
        'unite'         => 'kPa',

            ),
            'maximum_speed' => array(

                'classe'      	=> '',
                'table'      	=> '',
                'champ_id'      => 'maximum_speed',
                'type'          => 'input',
                'format'        => 'float',
                'titre'         => txt('Vitesse maximale', false),
                'css_editeur'   => 'xxlarge',
        'unite'         => 'tpm',

            ),
            'pump_efficiency' => array(

                'classe'      	=> '',
                'table'      	=> '',
                'champ_id'      => 'pump_efficiency',
                'type'          => 'input',
                'format'        => 'float',
                'titre'         => txt('Efficacité de la pompe', false),
                'css_editeur'   => 'xxlarge',
        'unite'         => '%',

            ),
            'commentaire' => array(
                'classe'      	=> '',
                'table'      	=> '',
                'champ_id'      => 'commentaire',
                'type'          => 'textarea',
                'format'        => 'text',
                'titre'         => txt('Commentaires', false),
                'css_editeur'   => 'xxlarge'
            ),

		);
		return parent::parseMetaData($return_array);
	}

	public static function getListe($filter = array(), $orderBy = 'id', $extra_data = null)
    {
        return parent::getListe($filter, $orderBy, $extra_data);
    }

    public static function getFormHTML($fiche_id, $liste_data = null, $forced_liste = null)
    {
        return parent::getUniqueFormHTML($fiche_id, $liste_data);
    }

    public function getEditorHTML($iteration = 0, $only_champs_denormalises = false)
    {
?>
        <style>
            #editables label {
                width: 275px !important;
            }
            .edit_popupContent section > div {
                width: 690px !important;
            }
            #editables input[type=checkbox] {
                position: relative;
                top: 1px;
            }
        </style>
<?
        parent::getEditorHTML($iteration);
    }

    public static function getFicheHTML($fiche, $liste = null, $nb_archives = 0, $can_edit = false, $projet_courant_id = null, $gabarit = null)
    {
?>
        <style>
            .blocsFiche section label {
                width: 260px !important;
            }
        </style>
<?
        if(isset($liste) && is_array($liste))
        {
            $liste_tmp = array_values($liste);
            if(isset($liste_tmp[0]))
            {
                $obj = $liste_tmp[0];
            }
            else
            {
                $currentClassName = get_called_class();
                $obj = $currentClassName::getOne($fiche->id);
            }
        }
        else
        {
            $currentClassName = get_called_class();
            $obj = $currentClassName::getOne($fiche->id);
        }

        parent::getUniqueFicheHTML($fiche, $obj, $nb_archives, $can_edit, $projet_courant_id);

        parent::getAutoFicheHtml($fiche->id, $obj);
    }

    public static function getDiffArchiveHTML($fiche_id, $liste, $last_liste, $token_archive, $last_token_archive)
    {
?>
        <style>
            .blocsFiche section label {
                width: 260px !important;
            }
        </style>
<?
        $liste = array_values($liste);
        $last_liste = array_values($last_liste);
        if(isset($liste[0]))
        {
            $obj = $liste[0];
        }

        if(isset($last_liste[0]))
        {
            $last_obj = $last_liste[0];
        }
        else
        {
            $last_obj = new self(0);
        }

?>
        <h2 class="blocsFicheTitre" style="height: 30px;">
            <?=txt(self::TITRE)?></h2>
<?

        parent::getAutoDiffArchiveHtml($fiche_id, $obj, $last_obj, $token_archive, $last_token_archive);
    }

    public function getFiltreHTML()
    {
?>
        <fieldset class="noMinHeight">
<?
            $this->getEditorHTML();
?>
        </fieldset>
<?
    }
}

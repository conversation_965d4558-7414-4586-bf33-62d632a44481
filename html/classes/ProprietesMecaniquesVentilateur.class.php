<?
class ProprietesMecaniquesVentilateur extends FicheFormWrapper {
    private static $CLASS_NAME='ProprietesMecaniquesVentilateur';
    const SQL_TABLE_NAME='proprietes_mecaniques_ventilateur';
    const TITRE='Propriétés mécaniques - Ventilateur';
    public static $AUTO=true;
    public static $TYPE_FORM='UNIQUE';
    public $id;
    public $fiche_id;
    public $classification_du_systeme_id;     
    public $classification_du_systeme; 
    public $fan_wheel_type_id;     
    public $fan_wheel_type; 
    public $fan_wheel_dia;     
    public $fan_rpm;     
    public $airflow;     
    public $airflow_theoretical;     
    public $fan_external_static_pressure;     
    public $fan_total_static_pressure;     
    public $electrical_motor_enclosure_id;     
    public $electrical_motor_enclosure; 
	public $commentaire;

	public static function getChampsSaisieMetaData()
    {
        $return_array = array(

            'classification_du_systeme' => array(

                'classe'      	=> 'UtaClassificationsTypes',
                'table'      	=> 'uta_classifications_types',
                'champ_id'      => 'classification_du_systeme_id',
                'type'          => 'select',
                'format'        => 'int',
                'titre'         => txt('Classification UTA', false),
                'css_editeur'   => 'xxlarge',

            ),
            'fan_wheel_type' => array(

                'classe'      	=> 'RouesVentilateursTypes',
                'table'      	=> 'roues_ventilateurs_types',
                'champ_id'      => 'fan_wheel_type_id',
                'type'          => 'select',
                'format'        => 'int',
                'titre'         => txt('Type de roue (ventilateur)', false),
                'css_editeur'   => 'xxlarge',

            ),
            'fan_wheel_dia' => array(

                'classe'      	=> '',
                'table'      	=> '',
                'champ_id'      => 'fan_wheel_dia',
                'type'          => 'input',
                'format'        => 'float',
                'titre'         => txt('Diamètre de roue (ventilateur)', false),
                'css_editeur'   => 'xxlarge',
        'unite'         => 'mm',

            ),
            'fan_rpm' => array(

                'classe'      	=> '',
                'table'      	=> '',
                'champ_id'      => 'fan_rpm',
                'type'          => 'input',
                'format'        => 'float',
                'titre'         => txt('Vitesse du ventilateur', false),
                'css_editeur'   => 'xxlarge',
        'unite'         => 'tpm',

            ),
            'airflow_theoretical' => array(

                'classe'      	=> '',
                'table'      	=> '',
                'champ_id'      => 'airflow_theoretical',
                'type'          => 'input',
                'format'        => 'float',
                'titre'         => txt('Débit d\'air (calculé)', false),
                'css_editeur'   => 'xxlarge',
				'unite'         => 'l/s',

            ),
            'airflow' => array(

                'classe'      	=> '',
                'table'      	=> '',
                'champ_id'      => 'airflow',
                'type'          => 'input',
                'format'        => 'float',
                'titre'         => txt('Débit d\'air (installé)', false),
                'css_editeur'   => 'xxlarge',
				'unite'         => 'l/s',

            ),
            'fan_external_static_pressure' => array(

                'classe'      	=> '',
                'table'      	=> '',
                'champ_id'      => 'fan_external_static_pressure',
                'type'          => 'input',
                'format'        => 'float',
                'titre'         => txt('Pression static externe (ventilateur)', false),
                'css_editeur'   => 'xxlarge',
        'unite'         => 'Pa',

            ),
            'fan_total_static_pressure' => array(

                'classe'      	=> '',
                'table'      	=> '',
                'champ_id'      => 'fan_total_static_pressure',
                'type'          => 'input',
                'format'        => 'float',
                'titre'         => txt('Pression static totale (ventilateur)', false),
                'css_editeur'   => 'xxlarge',
        'unite'         => 'Pa',

            ),
            'electrical_motor_enclosure' => array(

                'classe'      	=> 'MoteursChassisTypes',
                'table'      	=> 'moteurs_chassis_types',
                'champ_id'      => 'electrical_motor_enclosure_id',
                'type'          => 'select',
                'format'        => 'int',
                'titre'         => txt('Type de chassis (moteur)', false),
                'css_editeur'   => 'xxlarge',

            ),
            'commentaire' => array(
                'classe'      	=> '',
                'table'      	=> '',
                'champ_id'      => 'commentaire',
                'type'          => 'textarea',
                'format'        => 'text',
                'titre'         => txt('Commentaires', false),
                'css_editeur'   => 'xxlarge'
            ),

		);
		return parent::parseMetaData($return_array);
	}

	public static function getListe($filter = array(), $orderBy = 'id', $extra_data = null)
    {
        return parent::getListe($filter, $orderBy, $extra_data);
    }

    public static function getFormHTML($fiche_id, $liste_data = null, $forced_liste = null)
    {
        return parent::getUniqueFormHTML($fiche_id, $liste_data);
    }

    public function getEditorHTML($iteration = 0, $only_champs_denormalises = false)
    {
?>
        <style>
            #editables label {
                width: 275px !important;
            }
            .edit_popupContent section > div {
                width: 690px !important;
            }
            #editables input[type=checkbox] {
                position: relative;
                top: 1px;
            }
        </style>
<?
        parent::getEditorHTML($iteration);
    }

    public static function getFicheHTML($fiche, $liste = null, $nb_archives = 0, $can_edit = false, $projet_courant_id = null, $gabarit = null)
    {
?>
        <style>
            .blocsFiche section label {
                width: 260px !important;
            }
        </style>
<?
        if(isset($liste) && is_array($liste))
        {
            $liste_tmp = array_values($liste);
            if(isset($liste_tmp[0]))
            {
                $obj = $liste_tmp[0];
            }
            else
            {
                $currentClassName = get_called_class();
                $obj = $currentClassName::getOne($fiche->id);
            }
        }
        else
        {
            $currentClassName = get_called_class();
            $obj = $currentClassName::getOne($fiche->id);
        }

        parent::getUniqueFicheHTML($fiche, $obj, $nb_archives, $can_edit, $projet_courant_id);

        parent::getAutoFicheHtml($fiche->id, $obj);
    }

    public static function getDiffArchiveHTML($fiche_id, $liste, $last_liste, $token_archive, $last_token_archive)
    {
?>
        <style>
            .blocsFiche section label {
                width: 260px !important;
            }
        </style>
<?
        $liste = array_values($liste);
        $last_liste = array_values($last_liste);
        if(isset($liste[0]))
        {
            $obj = $liste[0];
        }

        if(isset($last_liste[0]))
        {
            $last_obj = $last_liste[0];
        }
        else
        {
            $last_obj = new self(0);
        }

?>
        <h2 class="blocsFicheTitre" style="height: 30px;">
            <?=txt(self::TITRE)?></h2>
<?

        parent::getAutoDiffArchiveHtml($fiche_id, $obj, $last_obj, $token_archive, $last_token_archive);
    }

    public function getFiltreHTML()
    {
?>
        <fieldset class="noMinHeight">
<?
            $this->getEditorHTML();
?>
        </fieldset>
<?
    }
}

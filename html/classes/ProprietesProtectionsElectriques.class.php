<?
class ProprietesProtectionsElectriques extends FicheFormWrapper {
	private static $CLASS_NAME='ProprietesProtectionsElectriques';
	const SQL_TABLE_NAME='proprietes_protections_electriques';
	const TITRE='Propriétés des protections électriques';
	public $id;
	public $fiche_id;
	public $ajustement_relais;
	public $fusible_nombre;
	public $fusible_capacite;
	public $fusible_action;
	public $fusible_format;
	public $type_protection;
    
    public static function getChampsSaisieMetaData()
    {
        return array(
/*
            'installation_type' => array(
				'classe'      	=> 'InstallationsTypes',
				'table'      	=> 'installations_types',
                'champ_id'      => 'installation_type_id',
                'type'          => 'select',
                'format'        => 'int',
                'titre'         => txt('Installation', false),
                'css_editeur'   => 'xxlarge'
            ),
            
            'qte_requise' => array(
				'classe'      	=> '',
				'table'      	=> '',
				'champ_id'      => 'qte_requise',
				'type'          => 'input',
				'format'        => 'int',
				'titre'         => txt('Quantité', false),
				'css_editeur'   => 'tiny'
			),
            
            'commentaires' => array(
				'table'      	=> '',
				'champ_id'      => 'commentaires',
				'type'          => 'input',
                'format'        => 'text',
                'titre'         => txt('Commentaires', false),
                'css_editeur'   => 'xxlarge'
            ),
*/
        );
    }
	
	public static function getListe($filter = array(), $orderBy = 'id', $extra_data = null)
	{
		return parent::getListe($filter, $orderBy, $extra_data);
	}
	
    public static function getFormHTML($fiche_id, $liste_data = null, $forced_liste = null)
	{
        global $types_protections;
        
        if(isset($liste_data))
        {
            $objs = self::dataToListe($liste_data);
            if(isset($objs[0]))
            {
                $obj = $objs[0];
            }
            else
            {
                $obj = new self(0);
            }
        }
        else
        {
            $obj = self::getOne($fiche_id);
        }
?>			
        <fieldset class="noMinHeight">
            <section>
                <label class="label"><?=txt('Type de protection')?></label>
                <div>
                    <br />
<?
                    $obj->type_protection = dm_strlen($obj->type_protection) > 0 ? $obj->type_protection : 1;
                    foreach($types_protections as $i => $row)
                    {
                        $chk = $row['id'] == $obj->type_protection ? ' checked="checked"' : '';
?>
                        <div>
                            <input type="radio" name="type_protection[]" id="type_protection_<?=$row['id']?>"<?=$chk?> class="type_protection" value="<?=$row['id']?>" /><label for="type_protection_<?=$row['id']?>" style="width: 360px!important; position: relative; top: 3px;"><?=$row['nom']?></label>
                        </div>
<?
                    }
?>
                </div>
            </section>

            <section>
                <label class="label"><?=txt('Nombre')?></label>
                <div>
<?
                    $disabled = in_array($obj->type_protection, array(2)) ? '' : ' disabled="disabled"';
?>
                    <input id="fusible_nombre" type="text" name="fusible_nombre[]" value="<?=($obj->fusible_nombre == 0 ? '' : $obj->fusible_nombre)?>" class="justInteger" style="width: 50%;"<?=$disabled?> />
                </div>
            </section>

            <section>
                <label class="label"><?=txt('Capacité')?></label>
                <div>
<?
                    $disabled = in_array($obj->type_protection, array(2,3)) ? '' : ' disabled="disabled"';
?>
                    <input id="fusible_capacite" type="text" name="fusible_capacite[]" value="<?=($obj->fusible_capacite == 0 ? '' : $obj->fusible_capacite)?>" class="justInteger" style="width: 50%;"<?=$disabled?> /> <?=txt('Ampères')?>
                </div>
            </section>

            <section>
                <label class="label"><?=txt('Action')?></label>
                <div>
<?
                    $disabled = in_array($obj->type_protection, array(2)) ? '' : ' disabled="disabled"';
?>
                    <input id="fusible_action" type="text" name="fusible_action[]" value="<?=($obj->fusible_action)?>" style="width: 50%;"<?=$disabled?> /> <?=txt('Ex: Time delay, Fast action, etc.')?>
                </div>
            </section>

            <section>
                <label class="label"><?=txt('Format ou type')?></label>
                <div>
<?
                    $disabled = in_array($obj->type_protection, array(2, 3)) ? '' : ' disabled="disabled"';
?>
                    <input id="fusible_format" type="text" name="fusible_format[]" value="<?=($obj->fusible_format)?>" style="width: 50%;"<?=$disabled?> /> <?=txt('Ex: 3/4 pouces, Midget, etc.')?>
                </div>
            </section>

            <section>
                <label class="label"><?=txt('Ajustement (relais O/L)')?></label>
                <div>
<?
                    $disabled = in_array($obj->type_protection, array(2,3)) ? '' : ' disabled="disabled"';
?>
                    <input id="ajustement_relais" type="text" name="ajustement_relais[]" value="<?=($obj->ajustement_relais == 0 ? '' : ($obj->ajustement_relais))?>" class="justInteger" style="width: 50%;"<?=$disabled?> /> <?=txt('Ampères')?>
                </div>
            </section>
            
            <section>
            <table style="position: relative; left: 60px; max-width: 800px;">
            
            <tr><td class="title" style="padding: 20px 5px 0 0!important;" valign="top">
                <?=txt('Motif du changement')?>
            </td>
            <td colspan="10" style="padding-top: 20px!important;">
                <select id="motif_changement" data-name="raison">
                    <option value=""> </option>
                    <?=MotifsChangements::getOptions(array(), 1)?>
                </select>
                <div style="color: blue; margin-top: 10px;"><?=txt('SVP, écrire un motif particulier ou choisir parmi la sélection proposée.')?></div>
            </td></tr>
            
            </table>
            </section>
            
        </fieldset>
<?
	}
	
    public static function getFicheHTML($fiche, $liste = null, $nb_archives = 0, $can_edit = false, $projet_courant_id = null, $gabarit = null)
	{
        if(isset($liste) && is_array($liste))
        {
            $liste_tmp = array_values($liste);
            if(isset($liste_tmp[0]))
            {
                $obj = $liste_tmp[0];
            }
            else
            {
                $currentClassName = get_called_class();
                $obj = $currentClassName::getOne($fiche->id);
            }
        }
        else
        {
            $currentClassName = get_called_class();
            $obj = $currentClassName::getOne($fiche->id);
        }
        
        parent::getUniqueFicheHTML($fiche, $obj, $nb_archives, $can_edit, $projet_courant_id);
?>
		<div class="blocsFiche">
        <? if($obj->type_protection == 2) { ?>
			
            <section>
				<label style="text-decoration: underline;"><?=txt('Fusibles')?></label>
                <div>
                    &nbsp;
				</div>
            </section>    
                
            <section>
				<label><?=txt('Nombre')?> : </label>
                <div>
                    <?=$obj->fusible_nombre?>
				</div>
            </section>    
                
            <section>
				<label><?=txt('Capacité')?> : </label>
                <div>
                    <?=$obj->fusible_capacite.' '.txt('Ampères')?>
				</div>
            </section>    
                
            <section>
				<label><?=txt('Action')?> : </label>
                <div>
                    <?=$obj->fusible_action?>
				</div>
            </section>    
                
            <section>
				<label><?=txt('Format')?> : </label>
                <div>
                    <?=$obj->fusible_format?>
				</div>
            </section>    
                
            <section>
				<label><?=txt('Ajustement du relais de surcharge')?> : </label>
                <div>
                    <?=$obj->ajustement_relais.' '.txt('Ampères')?>
				</div>
            </section>
				
	<? } else if($obj->type_protection == 3) { ?>	
			
            <section>
				<label style="text-decoration: underline;"><?=txt('Disjoncteur')?></label>
                <div>
                    &nbsp;
				</div>
            </section>    
                
            <section>
				<label><?=txt('Capacité')?> : </label>
                <div>
                    <?=$obj->fusible_capacite.' '.txt('Ampères')?>
				</div>
            </section>    
                
            <section>
				<label><?=txt('Format')?> : </label>
                <div>
                    <?=$obj->fusible_format?>
				</div>
            </section>
                
            <section>
				<label><?=txt('Ajustement du relais de surcharge')?> : </label>
                <div>
                    <?=$obj->ajustement_relais.' '.txt('Ampères')?>
				</div>
            </section>
                
	<? } else if($obj->type_protection == 1) { ?>	
			
            <section>
				<label style="width: 500px!important;"><?=txt('Aucune protection (intrinsèque à l\'équipement alimenté).')?></label>
                <div>
                    &nbsp;
				</div>
            </section>    
                
	<? } ?>
		</div>
<?
    }
}
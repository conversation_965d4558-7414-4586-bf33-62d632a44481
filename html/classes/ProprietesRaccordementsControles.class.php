<?

class ProprietesRaccordementsControles extends FicheFormWrapper {
	private static $CLASS_NAME='ProprietesRaccordementsControles';
	const SQL_TABLE_NAME='proprietes_raccordements_controles';
	const TITRE='Propriétés de raccordement de contrôle (premier signal)';
    public static $TYPE_FORM='UNIQUE';
	public static $AUTO=true;
	public $id;
	public $fiche_id;
	public $automate_id;
	public $automate;
	public $type_carte_id;
	public $type_carte;
	public $adresse_geio;
	public $borne_1;
	public $borne_2;
	public $numero_fil_1;
	public $numero_fil_2;
	public $rack;
	public $slot;
	public $champ_combine_1;
	public $champ_combine_2;
	public $champ_combine_3;
	public $champ_combine_4;
	public $champ_combine_5;

	public static function getChampsSaisieMetaData()
    {
        $return_array = array(
            
            'automate' => array(
                'classe'      	=> 'Fiches',
                'table'      	=> 'fiches',
                'champ_id'      => 'automate_id',
                'type'          => 'select',
                'format'        => 'int',
                'titre'         => txt('Automate', false),
                'css_editeur'   => 'xxlarge',
                'dependance_id' => 'fiche_sous_type_id',
                'dependance_champ_liste' => 'fiche_sous_type_id',
                'dependance_force_val' => 2000011,
                'sql_for_nom'   => 'code'
            ),
            
            'type_carte' => array(
                'classe'      	=> 'TypeCarte',
                'table'      	=> 'types_cartes',
                'champ_id'      => 'type_carte_id',
                'type'          => 'select',
                'format'        => 'int',
                'titre'         => txt('Carte I/O', false),
                'css_editeur'   => 'small'
            ),
            
            'adresse_geio' => array(
                'classe'      	=> '',
                'table'      	=> '',
                'champ_id'      => 'adresse_geio',
                'type'          => 'input',
                'format'        => 'text',
                'titre'         => txt('Adresse GEIO', false),
                'css_editeur'   => 'xxlarge'
            ),
            
            'borne_1' => array(
                'classe'      	=> '',
                'table'      	=> '',
                'champ_id'      => 'borne_1',
                'type'          => 'input',
                'format'        => 'text',
                'titre'         => txt('No. Entrée/Sortie 1 (+)', false),
                'css_editeur'   => 'xxlarge'
            ),
            
            'borne_2' => array(
                'classe'      	=> '',
                'table'      	=> '',
                'champ_id'      => 'borne_2',
                'type'          => 'input',
                'format'        => 'text',
                'titre'         => txt('No. Entrée/Sortie 2 (-)', false),
                'css_editeur'   => 'xxlarge'
            ),
            
            'numero_fil_1' => array(
                'classe'      	=> '',
                'table'      	=> '',
                'champ_id'      => 'numero_fil_1',
                'type'          => 'input',
                'format'        => 'text',
                'titre'         => txt('No. de fil 1 (+)', false),
                'css_editeur'   => 'xxlarge'
            ),
            
            'numero_fil_2' => array(
                'classe'      	=> '',
                'table'      	=> '',
                'champ_id'      => 'numero_fil_2',
                'type'          => 'input',
                'format'        => 'text',
                'titre'         => txt('No. de fil 2 (-)', false),
                'css_editeur'   => 'xxlarge'
            ),
            
            'rack' => array(
                'classe'      	=> '',
                'table'      	=> '',
                'champ_id'      => 'rack',
                'type'          => 'input',
                'format'        => 'text',
                'titre'         => txt('Rack', false),
                'css_editeur'   => 'xxlarge'
            ),
            
            'slot' => array(
                'classe'      	=> '',
                'table'      	=> '',
                'champ_id'      => 'slot',
                'type'          => 'input',
                'format'        => 'text',
                'titre'         => txt('Slot', false),
                'css_editeur'   => 'xxlarge'
            ),
            
        );
		return parent::parseMetaData($return_array);
    }
	
	public static function getListe($filter = array(), $orderBy = 'id', $extra_data = null)
	{
		return parent::getListe($filter, $orderBy, $extra_data);
	}
	
    public static function getFormHTML($fiche_id, $liste_data = null, $forced_liste = null)
	{
        $currentClassName = get_called_class();
        $metas = $currentClassName::getFlatChampsSaisieMetaData();
        $champs_readonly = $currentClassName::getChampsReadOnly();
        
        if(isset($liste_data))
        {
            $objs = self::dataToListe($liste_data);
            if(isset($objs[0]))
            {
                $obj = $objs[0];
            }
            else
            {
                $obj = new self(0);
            }
        }
        else
        {
            $obj = self::getOne($fiche_id);
        }
        
        $is_motif = ($obj->id > 0);
?>			
        <style>
            .edit_popupContent section > div {
                float: left;
                margin: 5px 0;
                padding: 0;
                width: 706px !important;
            }
            
            #validation_besoin {
                color: blue;
                font-size: 16px;
                width: 900px;
            }
            #validation_besoin input {
                position: relative;
                top: 0px!important;
                margin-left: 0px;
            }
            #validation_besoin label {
                width: 45px !important;
                padding: 0;
                display: inline-block;
                background: none;
                font-weight: 400;
                font-size: 16px;
                text-align: left;
            }
            h2.blocsFicheTitre {
                margin: 5px 20px;
            }
            .blocsFiche {
                margin: 25px 20px 25px 15px;
            }
            .blocsFiche label {
                text-align: left!important;
                margin-left: 0px!important;
                background: none!important;
                font-weight: normal!important;
                min-width: 215px!important;
            }
            h2.blocsFicheTitre {
                font-size: 16px!important;
                line-height: 22px!important;
                font-weight: 100!important;
                color: #777777!important;
                text-shadow: 0 1px 0 #ffffff!important;
                margin: 5px 0px!important;
                text-decoration: underline!important;
                border-bottom: 0px solid #aaa!important;
            }
            
            h2.underlined{
                border-bottom: 1px solid #aaa!important;
            }
            
            .blocsFiche section > div {
                width: 606px !important;
            }
        </style>
        <div style="margin-left: 40px;">
<?
        $fiche = new Fiches($fiche_id);
        $prop_elec = ProprietesControles::getOne($fiche_id);
        if($prop_elec->id > 0)
        {
            ProprietesControles::getFicheHTML($fiche);
        }
?>
        <input type="hidden" id="statut_exigence" name="statut_exigence[]" value="<?=$obj->statut_exigence?>" />
        <input type="hidden" id="statut_solution" name="statut_solution[]" value="<?=$obj->statut_solution?>" />
        <input type="hidden" id="usager_solution_id" name="usager_solution_id[]" value="<?=($obj->usager_solution_id == 0 ? '' : $obj->usager_solution_id)?>" />
        <input type="hidden" id="ts_solution" name="ts_solution[]" value="<?=$obj->ts_solution?>" />
        <br style="clear: both;">
        <br style="clear: both;">
        <br style="clear: both;">
        <div id="validation_besoin">
            <?=txt('Les propriétés de contrôle inscrites ci-dessus permettent-elles de définir le raccord de contrôle requis?')?>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
            <input type="radio" id="validation_oui" value="1" class="validation_oui_non" name="validation_besoin"<?=($obj->statut_exigence == 1 ? ' checked="checked"' : '')?> /><label for="validation_oui">&nbsp;Oui</label>
            <input type="radio" id="validation_non" value="-1" class="validation_oui_non" name="validation_besoin"<?=($obj->statut_exigence == -1 ? ' checked="checked"' : '')?> /><label for="validation_non">&nbsp;Non</label>
	        <?
	        if($prop_elec->fiche_id > 0 && havePermission(array('repondant_qrt_bcf', 'demandeur_qrt_bcf')))
	        {
		        print '<span class="messagerie" style="position: relative; top: 7px;" data-discipline_id="8">';
		        $qrts = Questions::getListe(array('fiche_id' => $prop_elec->fiche_id, 'classe_formulaire' => $currentClassName, 'discipline_id' => 8));
		        if(count($qrts) > 0)
		        {
			        $qrt = array_values($qrts)[0];
			        ?>
                    <a href="javascript: void(0);" class="start_qrt" data-qrt_id="<?=$qrt->id?>">
                        <img src="css/images/icons/dark/speech_bubble.png" /><?=($qrt->nb_notifications_all > 0 ? '<span class="extra">'.$qrt->nb_notifications_all.'</span>' : '')?></a>
			        <?
		        }
		        else
		        {
			        ?>
                    <a href="index.php?section=admin&module=edit&table=questions&type=client&id=0&fiche_id=<?=$prop_elec->fiche_id?>&nom_table=&liste_item_id=&classe_formulaire=<?=$currentClassName?>&discipline_id=8" class="fancy" data-small_fancy="true" style="opacity: 0.5;">
                        <img src="css/images/icons/dark/speech_bubble.png" /></a>
			        <?
		        }
		        print '</span>';
	        }
	        ?>
        </div>

        <h2 class="underlined"></h2>
        
        <fieldset class="noMinHeight">
            <h5 style="text-decoration: underline; margin: 15px 0;"><?=txt(self::TITRE)?></h5>
            <section>
                <label><?=txt('Automate')?></label>
                <div>        
<?
                    $key = 'automate';
                    $meta = $metas[$key];
                    $meta_champ_id = $meta['champ_id'];
                    if(isset($champs_readonly[$key]))
                    {
                        print self::getReadonlyToDisplay($meta, $key, $obj);      
?>
                        <input type="hidden" id="<?=$meta['champ_id']?>" name="<?=$meta['champ_id']?>[]" value="<?=($obj->$meta_champ_id)?>" />
<?
                    }
                    else
                    {
?>
                        <select id="automate_id" name="automate_id[]" class="lazy_loaded_fiches" disabled data-textblank="<?=txt('- Non applicable -')?>">
                            <option value=""><?=txt('- Non applicable -')?></option>
                            <?=Fiches::getOptions(array('ids' => array($obj->automate_id), 'fiche_sous_type_id' => 2000011, 'no_filtre_global' => 1), $obj->automate_id)?>
                        </select>
<?
                    }
?>
                </div>
            </section>

            <section>
                <label><?=txt('Carte I/O')?></label>
                <div>             
<?
                    $key = 'type_carte';
                    $meta = $metas[$key];
                    $meta_champ_id = $meta['champ_id'];
                    if(isset($champs_readonly[$key]))
                    {
                        print self::getReadonlyToDisplay($meta, $key, $obj);      
?>
                        <input type="hidden" id="<?=$meta['champ_id']?>" name="<?=$meta['champ_id']?>[]" value="<?=($obj->$meta_champ_id)?>" />
<?
                    }
                    else
                    {
?>
                        <select id="type_carte_id" name="type_carte_id[]" <?=$obj->automate_id == 0 ? ' disabled="disabled"' : ''?> class="automate_id_dependant">
                            <option value=""> </option>
                            <?=TypeCarte::getOptions(array(), $obj->type_carte_id)?>
                        </select>
<?
                    }
?>
                </div>
            </section>

            <section>
                <label><?=txt('Adresse GEIO')?></label>
                <div>           
<?
                    $key = 'adresse_geio';
                    $meta = $metas[$key];
                    $meta_champ_id = $meta['champ_id'];
                    if(isset($champs_readonly[$key]))
                    {
                        print self::getReadonlyToDisplay($meta, $key, $obj);      
?>
                        <input type="hidden" id="<?=$meta['champ_id']?>" name="<?=$meta['champ_id']?>[]" value="<?=($obj->$meta_champ_id)?>" />
<?
                    }
                    else
                    {
?>
                        <input id="adresse_geio" type="text" name="adresse_geio[]" value="<?=($obj->adresse_geio)?>" style="width: 50%;" <?=$obj->automate_id == 0 ? ' disabled="disabled"' : ''?> class="automate_id_dependant" />
<?
                    }
?>
                </div>
            </section>

            <section>
                <label><?=txt('No. Entrée/Sortie 1 (+)')?></label>
                <div>        
<?
                    $key = 'borne_1';
                    $meta = $metas[$key];
                    $meta_champ_id = $meta['champ_id'];
                    if(isset($champs_readonly[$key]))
                    {
                        print self::getReadonlyToDisplay($meta, $key, $obj);      
?>
                        <input type="hidden" id="<?=$meta['champ_id']?>" name="<?=$meta['champ_id']?>[]" value="<?=($obj->$meta_champ_id)?>" />
<?
                    }
                    else
                    {
?>
                        <input id="borne_1" type="text" name="borne_1[]" value="<?=($obj->borne_1)?>" style="width: 50%;" <?=$obj->automate_id == 0 ? ' disabled="disabled"' : ''?> class="automate_id_dependant" />
<?
                    }
?>
                </div>
            </section>

            <section>
                <label><?=txt('No. Entrée/Sortie 2 (-)')?></label>
                <div>        
<?
                    $key = 'borne_2';
                    $meta = $metas[$key];
                    $meta_champ_id = $meta['champ_id'];
                    if(isset($champs_readonly[$key]))
                    {
                        print self::getReadonlyToDisplay($meta, $key, $obj);      
?>
                        <input type="hidden" id="<?=$meta['champ_id']?>" name="<?=$meta['champ_id']?>[]" value="<?=($obj->$meta_champ_id)?>" />
<?
                    }
                    else
                    {
?>
                        <input id="borne_2" type="text" name="borne_2[]" value="<?=($obj->borne_2)?>" style="width: 50%;" <?=$obj->automate_id == 0 ? ' disabled="disabled"' : ''?> class="automate_id_dependant" />
<?
                    }
?>
                </div>
            </section>

            <section>
                <label><?=txt('No. de fil 1 (+)')?></label>
                <div>          
<?
                    $key = 'numero_fil_1';
                    $meta = $metas[$key];
                    $meta_champ_id = $meta['champ_id'];
                    if(isset($champs_readonly[$key]))
                    {
                        print self::getReadonlyToDisplay($meta, $key, $obj);      
?>
                        <input type="hidden" id="<?=$meta['champ_id']?>" name="<?=$meta['champ_id']?>[]" value="<?=($obj->$meta_champ_id)?>" />
<?
                    }
                    else
                    {
?>
                        <input id="numero_fil_1" type="text" name="numero_fil_1[]" value="<?=($obj->numero_fil_1)?>" style="width: 50%;" <?=$obj->automate_id == 0 ? ' disabled="disabled"' : ''?> class="automate_id_dependant" />
<?
                    }
?>
                </div>
            </section>

            <section>
                <label><?=txt('No. de fil 2 (-)')?></label>
                <div>          
<?
                    $key = 'numero_fil_2';
                    $meta = $metas[$key];
                    $meta_champ_id = $meta['champ_id'];
                    if(isset($champs_readonly[$key]))
                    {
                        print self::getReadonlyToDisplay($meta, $key, $obj);      
?>
                        <input type="hidden" id="<?=$meta['champ_id']?>" name="<?=$meta['champ_id']?>[]" value="<?=($obj->$meta_champ_id)?>" />
<?
                    }
                    else
                    {
?>
                        <input id="numero_fil_2" type="text" name="numero_fil_2[]" value="<?=($obj->numero_fil_2)?>" style="width: 50%;" <?=$obj->automate_id == 0 ? ' disabled="disabled"' : ''?> class="automate_id_dependant" />
<?
                    }
?>
                </div>
            </section>

            <section>
                <label><?=txt('Rack')?></label>
                <div>           
<?
                    $key = 'rack';
                    $meta = $metas[$key];
                    $meta_champ_id = $meta['champ_id'];
                    if(isset($champs_readonly[$key]))
                    {
                        print self::getReadonlyToDisplay($meta, $key, $obj);      
?>
                        <input type="hidden" id="<?=$meta['champ_id']?>" name="<?=$meta['champ_id']?>[]" value="<?=($obj->$meta_champ_id)?>" />
<?
                    }
                    else
                    {
?>
                        <input id="rack" type="text" name="rack[]" value="<?=($obj->rack)?>" style="width: 50%;" <?=$obj->automate_id == 0 ? ' disabled="disabled"' : ''?> class="automate_id_dependant" />
<?
                    }
?>
                </div>
            </section>

            <section>
                <label><?=txt('Slot')?></label>
                <div>          
<?
                    $key = 'slot';
                    $meta = $metas[$key];
                    $meta_champ_id = $meta['champ_id'];
                    if(isset($champs_readonly[$key]))
                    {
                        print self::getReadonlyToDisplay($meta, $key, $obj);      
?>
                        <input type="hidden" id="<?=$meta['champ_id']?>" name="<?=$meta['champ_id']?>[]" value="<?=($obj->$meta_champ_id)?>" />
<?
                    }
                    else
                    {
?>
                        <input id="slot" type="text" name="slot[]" value="<?=($obj->slot)?>" style="width: 50%;" <?=$obj->automate_id == 0 ? ' disabled="disabled"' : ''?> class="automate_id_dependant" />
<?
                    }
?>
                </div>
            </section>
<?
            if(isset($metas['champ_combine_1']) || isset($metas['champ_combine_2']) || isset($metas['champ_combine_3']) || isset($metas['champ_combine_4']) || isset($metas['champ_combine_5']))
            {
?>
                </fieldset>
                
                <fieldset class="noMinHeight">
                    <h5 style="text-decoration: underline; margin: 15px 0;"><?=txt('Champs dénormalisés')?></h5>
<?            
                    $obj->getEditorHTML(0, true);
                    
                if($is_motif)
                {
?>
                    </fieldset>

                    <fieldset class="noMinHeight">
                        <h5 style="text-decoration: underline; margin: 15px 0;">&nbsp;</h5>
<?            
                }
            }
            
            if($is_motif)
            {
?>
                <section>
                   <label for="raison">
                        <?=txt('Motif du changement')?>
                    </label>
                    <div>
                        <select id="motif_changement" data-name="raison">
                            <option value=""> </option>
                            <?=MotifsChangements::getOptions(array(), 1)?>
                        </select>
                        <div style="color: blue; margin-top: 10px;"><?=txt('SVP, écrire un motif particulier ou choisir parmi la sélection proposée.')?></div>
                    </div>
                </section>
<?
            }
            else
            {
?>
                <input type="hidden" name="raison" value="Création du formulaire" />
<?
            }
?>
        </fieldset>
        </div>
<?
	}
	
    public static function getFicheHTML($fiche, $liste = null, $nb_archives = 0, $can_edit = false, $projet_courant_id = null, $gabarit = null)
	{
        if(isset($liste) && is_array($liste))
        {
            $liste_tmp = array_values($liste);
            if(isset($liste_tmp[0]))
            {
                $obj = $liste_tmp[0];
            }
            else
            {
                $currentClassName = get_called_class();
                $obj = $currentClassName::getOne($fiche->id);
            }
        }
        else
        {
            $currentClassName = get_called_class();
            $obj = $currentClassName::getOne($fiche->id);
        }
        
        parent::getUniqueFicheHTML($fiche, $obj, $nb_archives, $can_edit, $projet_courant_id);
        parent::getAutoFicheHtml($fiche->id, $obj);
    }
    
    public static function getDiffArchiveHTML($fiche_id, $liste, $last_liste, $token_archive, $last_token_archive)
	{
?>
        <style>
            .blocsFiche section label {
                width: 260px !important;
            }
        </style>
<?
        $liste = array_values($liste);
        $last_liste = array_values($last_liste);
        if(isset($liste[0]))
        {
            $obj = $liste[0];
        }
        
        if(isset($last_liste[0]))
        {
            $last_obj = $last_liste[0];
        }
        else
        {
            $last_obj = new self(0);
        }
        
?>
        <h2 class="blocsFicheTitre" style="height: 30px;">
            <?=txt(self::TITRE)?></h2>
<?
        
        parent::getAutoDiffArchiveHtml($fiche_id, $obj, $last_obj, $token_archive, $last_token_archive);
    }
    
    public static function getLiens($fiche_id)
    {
        global $db, $Langue;
        
        $sql = "select ".self::SQL_TABLE_NAME.".*, fiches.code as fiche, automate_id as fiche_id, fiches_types.nom_$Langue as fiche_type, fiches_sous_types.nom_$Langue as fiche_sous_type, nomenclatures.nom_$Langue as nomenclature, "
                . "fiches.ts_delete "
                . "from ".self::SQL_TABLE_NAME." "
                . "join fiches on fiches.id = automate_id and fiches.ts_archive is null
                JOIN fiches_types ON fiches_types.id = fiche_type_id
                JOIN fiches_sous_types ON fiches_sous_types.id = fiche_sous_type_id
                left join proprietes_controles on proprietes_controles.fiche_id = ".self::SQL_TABLE_NAME.".fiche_id
                left JOIN nomenclatures ON nomenclatures.id = nomenclature_id
                where ".self::SQL_TABLE_NAME.".fiche_id = ?";
        $liens_1 = $db->getAll($sql, array($fiche_id));
        $liens_1 = isset($liens_1) && is_array($liens_1) ? $liens_1 : array();
        
        $sql = "select ".self::SQL_TABLE_NAME.".*, fiches.code as fiche, ".self::SQL_TABLE_NAME.".fiche_id as fiche_id, fiches_types.nom_$Langue as fiche_type, fiches_sous_types.nom_$Langue as fiche_sous_type, nomenclatures.nom_$Langue as nomenclature, "
                . "fiches.ts_delete "
                . "from ".self::SQL_TABLE_NAME." "
                . "join fiches on fiches.id = ".self::SQL_TABLE_NAME.".fiche_id and fiches.ts_archive is null
                JOIN fiches_types ON fiches_types.id = fiche_type_id
                JOIN fiches_sous_types ON fiches_sous_types.id = fiche_sous_type_id
                left join proprietes_controles on proprietes_controles.fiche_id = ".self::SQL_TABLE_NAME.".fiche_id
                left JOIN nomenclatures ON nomenclatures.id = nomenclature_id
                where automate_id = ?";
        $liens_2 = $db->getAll($sql, array($fiche_id));
        $liens_2 = isset($liens_2) && is_array($liens_2) ? $liens_2 : array();
        
        $liens = array_merge($liens_1, $liens_2);
        
        return $liens;
    }
}
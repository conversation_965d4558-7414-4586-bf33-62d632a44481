<?php
class Questions extends DbRow {
	public $id;
	public $question_categorie_id;
	public $question_categorie;
	public $code;
	public $projet_id;
	public $fiche_id;
	public $fiche_code;
	public $fiche_code_deleted;
	public $sujet;
	public $question;
	public $usager_ajout_id;
	public $demandeur;
    public $courriel_demandeur;
	public $ts_ajout;
	public $question_statut_id;
	public $reponses;
	public $statut;
	public $groupes_usagers_demandeurs;
	public $usagers_demandeurs;
	public $groupes_usagers_repondants;
	public $usagers_repondants;
	public $all_usagers_demandeurs;
	public $all_usagers_repondants;
	public $nb_notifications;
	public $ts_fin_discussion;
	public $is_prioritaire;
	public $nom_table;
	public $liste_item_id;
	public $classe_formulaire;
	public $discipline_id;
	public $is_usager_demandeur;
	public $is_usager_repondant;
	public $is_usager_can_reponse;
	public $can_conclure_qrt;
	public $fiches = array();
	public $reponse_nb_jours_max;
	public $ts_fin_discussion_max;
    public $bcf_guid;

	/**
	 * @param $id
	 */
	protected function setDataForId($id)
	{
		global $db, $Langue;

		$demandeurs = Usager::getListe(array('permission_code' => 'demandeur_qrt_bcf'));

		$sql = "select q.*, qs.nom_$Langue as statut, f.code as fiche_code, qc.nom as question_categorie, case when q.usager_ajout_id = ? then 1 else 0 end as is_usager_demandeur,
                  (select count(distinct coalesce(question_reponse_id, 1)) from notifications_qrt nq join notifications_qrt_usagers nqu on nq.id = nqu.notification_qrt_id where nq.question_id = q.id and nqu.ts_lu is null and nqu.usager_id = ?) as nb_notifications_all, 
                  (select count(distinct coalesce(question_reponse_id, 1)) from notifications_qrt nq join notifications_qrt_usagers nqu on nq.id = nqu.notification_qrt_id where nq.question_id = q.id and nq.question_reponse_id is null and nqu.ts_lu is null and nqu.usager_id = ?) as nb_notifications,
                  case when usager_ajout_str is not null then usager_ajout_str else concat(u.nom, ', ', u.prenom) end as demandeur,
                  concat(ufd.nom, ', ', ufd.prenom) as conclueur, q.fiche_code as fiche_code_deleted
                from questions q
                left join usagers u on u.id = q.usager_ajout_id
                left join usagers ufd on ufd.id = q.usager_fin_discussion_id
                left join questions_statuts qs on qs.id = q.question_statut_id
                left join questions_categories qc on q.question_categorie_id = qc.id
                left join fiches f on f.id = q.fiche_id
                where q.id = ?";
		$row = $db->getRow($sql, array(getIdUsager(), getIdUsager(), getIdUsager(), $id));

		$this->populateThisWithThat($row);

		$this->ts_ajout = dm_substr($this->ts_ajout, 0, 16);
		$this->ts_fin_discussion = dm_substr($this->ts_fin_discussion, 0, 16);

		$sql = "select qr.id as cle, qr.id, question_id, case when qr.usager_repondant_str is not null then qr.usager_repondant_str else concat(u.nom, ', ', u.prenom) end as repondant, qr.ts_ajout, usager_repondant_id, reponse, qr.bcf_guid,
                    (select count(distinct coalesce(question_reponse_id, 1)) from notifications_qrt nq join notifications_qrt_usagers nqu on nq.id = nqu.notification_qrt_id where nq.question_reponse_id = qr.id and nqu.ts_lu is null and nqu.usager_id = ?) as nb_notifications
					from questions q
					join questions_reponses qr on q.id = qr.question_id
					left join usagers u on u.id = qr.usager_repondant_id
					where q.id = ?
					order by qr.ts_ajout";
		$rows_reponses = $db->getAssoc($sql, array(getIdUsager(), $this->id));

		$sql = "select qgu.id as cle, qgu.id, qgu.nom
		        from questions_to_groupes_usagers qtgu
		        join questions_groupes_usagers qgu on qtgu.question_groupe_usager_id = qgu.id
		        where question_id = ? and type_groupe = 'demandeur'
		        order by qgu.nom";
		$groupes_usagers_demandeurs = $db->getAssoc($sql, array($this->id));

		$sql = "select qgu.id as cle, qgu.id, qgu.nom
		        from questions_to_groupes_usagers qtgu
		        join questions_groupes_usagers qgu on qtgu.question_groupe_usager_id = qgu.id
		        where question_id = ? and type_groupe = 'repondant'
		        order by qgu.nom";
		$groupes_usagers_repondants = $db->getAssoc($sql, array($this->id));

		$sql = "select cle, id, nom
                from (
                    select u.id as cle, u.id, concat(u.nom, ', ', u.prenom) as nom
                    from questions_to_usagers qtu
                    join usagers u on qtu.usager_id = u.id
                    where question_id = ? and type_usager = 'demandeur'
                    
                    union 
                    
                    select u.id as cle, u.id, concat(u.nom, ', ', u.prenom) as nom
                    from usagers u
                    where u.id = ?
		        ) as tmp
		        order by nom";
		$usagers_demandeurs = $db->getAssoc($sql, array($this->id, $this->usager_ajout_id));

		$sql = "select u.id as cle, u.id, concat(u.nom, ', ', u.prenom) as nom
		        from questions_to_usagers qtu
		        join usagers u on qtu.usager_id = u.id
		        where question_id = ? and type_usager = 'repondant'
		        order by nom";
		$usagers_repondants = $db->getAssoc($sql, array($this->id));

		$sql = "select u.id as cle, u.id, concat(u.nom, ', ', u.prenom) as nom
		        from questions_to_groupes_usagers qtgu
		        join questions_groupes_usagers qgu on qtgu.question_groupe_usager_id = qgu.id
		        join questions_groupes_usagers_listes qgul on qgu.id = qgul.question_groupe_usager_id
		        join usagers u on u.id = qgul.usager_id
		        where qtgu.question_id = ? and qtgu.type_groupe = 'demandeur'
		        order by qgu.nom";
		$usagers_demandeurs_from_groupes = $db->getAssoc($sql, array($this->id));

		$sql = "select u.id as cle, u.id, concat(u.nom, ', ', u.prenom) as nom
		        from questions_to_groupes_usagers qtgu
		        join questions_groupes_usagers qgu on qtgu.question_groupe_usager_id = qgu.id
		        join questions_groupes_usagers_listes qgul on qgu.id = qgul.question_groupe_usager_id
		        join usagers u on u.id = qgul.usager_id
		        where qtgu.question_id = ? and qtgu.type_groupe = 'repondant'
		        order by qgu.nom";
		$usagers_repondants_from_groupes = $db->getAssoc($sql, array($this->id));

		$this->groupes_usagers_demandeurs = $groupes_usagers_demandeurs;
		$this->usagers_demandeurs = $usagers_demandeurs;
		$this->groupes_usagers_repondants = $groupes_usagers_repondants;
		$this->usagers_repondants = $usagers_repondants;
		$this->all_usagers_demandeurs = ($usagers_demandeurs + $usagers_demandeurs_from_groupes);
		$this->all_usagers_repondants = ($usagers_repondants + $usagers_repondants_from_groupes);
		$this->is_usager_repondant = isset($this->all_usagers_repondants[getIdUsager()]) ? 1 : 0;
		$this->is_usager_can_reponse = havePermission(array('repondant_qrt_bcf')) ? 1 : 0;

		foreach($rows_reponses as $reponse_id => $reponse)
		{
			$type_repondant = 'demandeur';
			if(isset($this->all_usagers_repondants[$reponse['usager_repondant_id']]))
			{
				$type_repondant = 'repondant';
			}
			else if($reponse['usager_repondant_id'] == $this->usager_ajout_id || isset($demandeurs[$reponse['usager_repondant_id']]))
			{
				$type_repondant = 'demandeur';
			}
			$rows_reponses[$reponse_id]['type_repondant'] = $type_repondant;
			$rows_reponses[$reponse_id]['ts_ajout'] = dm_substr($reponse['ts_ajout'], 0, 16);
		}
		$this->reponses = $rows_reponses;

		$this->can_conclure_qrt = (havePermission('conclure_qrt_bcf') ? 1 : 0);

		$sql = "select f.*, f.id
                from questions_fiches qf
                join fiches f on qf.fiche_id = f.id
                where question_id = ?
                order by f.code";
		$fiches = $db->getAssoc($sql, array($id));

		$this->fiches = isset($fiches) ? $fiches : array();
	}

	/**
	 * @param array $filter
	 * @param string $orderBy
	 * @param string $sql_limit
	 * @return Questions[]
	 */
	public static function getListe($filter = array(), $orderBy = 'nb_notifications_all desc, statut, ts_ajout desc', $sql_limit = '')
	{
		global $db, $Langue;
		$where = '';

		// Si la date de conclusion dépasse la date maximun, SET le statut à "En retard"
		// Si un nombre maximun avant réponse est dépassé, SET le statut à "En retard"
		$db->query("CALL qrt_retard(?)", array(getProjetId()));

        $data = [];
        $selection_items = [];
		if (isset($filter['exporter_items']) && $filter['exporter_items'] == 1 && is_array($filter['selection_items']) && count($filter['selection_items']) > 0) {
			foreach($filter['selection_items'] as $item)
				$selection_items[] = intval($item);

			$where .= ' and q.id IN ('.db::placeHolders($selection_items).')';
            $data = $selection_items;
		}

		$where .= ' and q.projet_id = ?';
		$data = array_merge($selection_items, [getIdUsager(), getIdUsager(), getIdUsager(), getProjetId()]);

		if(isset($filter['niveau']) && count($filter['niveau']) > 0)
		{
			foreach($filter['niveau'] as $niveau_id => $niveau_element_id)
			{
				if(is_numeric($niveau_element_id) && intval($niveau_element_id) > 0)
				{
					$where .= " and exists (
					                select question_id from questions_fiches as qf
                                    join fiches_batiment_niveaux_elements as niveau_217 on niveau_217.fiche_id = qf.fiche_id and niveau_217.batiment_niveau_id = ?
                                    join batiments_niveaux_elements as niveau_element_217 on niveau_217.batiment_niveau_element_id = niveau_element_217.id and niveau_element_217.id = ?
                                    where qf.question_id = q.id
                    )";
					$data[] = $niveau_id;
					$data[] = $niveau_element_id;
				}
			}
		}

		if(isset($filter['terme']) && dm_strlen($filter['terme']) > 0)
		{
			$where .= ' and (q.code like ? or q.sujet like ? or q.question like ?)';
			$data[] = '%'.$filter['terme'].'%';
			$data[] = '%'.$filter['terme'].'%';
			$data[] = '%'.$filter['terme'].'%';
		}

		if(isset($filter['question_statut_id']) && $filter['question_statut_id'] > 0)
		{
			$where .= ' and q.question_statut_id = ?';
			$data[] = $filter['question_statut_id'];
		}

		if(isset($filter['question_categorie_id']) && $filter['question_categorie_id'] > 0)
		{
			$where .= ' and q.question_categorie_id = ?';
			$data[] = $filter['question_categorie_id'];
		}

		if(isset($filter['usager_ajout_id']) && $filter['usager_ajout_id'] > 0)
		{
			$where .= ' and q.usager_ajout_id = ?';
			$data[] = $filter['usager_ajout_id'];
		}

		if(isset($filter['usager_demandeur_id']) && $filter['usager_demandeur_id'] > 0)
		{
			$where .= ' and q.usager_ajout_id = ?';
			$data[] = $filter['usager_demandeur_id'];
		}

		if(isset($filter['fiche_id']) && $filter['fiche_id'] > 0)
		{
			$where .= ' and q.fiche_id = ?';
			$data[] = $filter['fiche_id'];
		}

		if(isset($filter['table_name']) && dm_strlen($filter['table_name']) > 0)
		{
			$where .= ' and q.table_name = ?';
			$data[] = $filter['table_name'];
		}

		if(isset($filter['liste_item_id']) && $filter['liste_item_id'] > 0)
		{
			$where .= ' and q.liste_item_id = ?';
			$data[] = $filter['liste_item_id'];
		}

		if(isset($filter['classe_formulaire']) && dm_strlen($filter['classe_formulaire']) > 0)
		{
			$where .= ' and q.classe_formulaire = ?';
			$data[] = $filter['classe_formulaire'];
		}

		if(isset($filter['discipline_id']) && $filter['discipline_id'] > 0)
		{
			$where .= ' and q.discipline_id = ?';
			$data[] = $filter['discipline_id'];
		}

		if(isset($filter['is_prioritaire']) && $filter['is_prioritaire'] > -1)
		{
			$where .= ' and q.is_prioritaire = ?';
			$data[] = $filter['is_prioritaire'];
		}

		if(isset($filter['usager_repondant_id']) && $filter['usager_repondant_id'] > 0)
		{
			$where .= ' and (
			                    exists (select question_id from questions_to_usagers qtu where qtu.question_id = q.id and usager_id = ?) 
			                    or 
			                    exists (select question_id from questions_to_groupes_usagers qtgu join questions_groupes_usagers_listes qgul on qgul.question_groupe_usager_id = qtgu.question_groupe_usager_id where qtgu.question_id = q.id and usager_id = ?) 
			                )';
			$data[] = $filter['usager_repondant_id'];
			$data[] = $filter['usager_repondant_id'];
		}

		if(isset($filter['formulaire_id']) && $filter['formulaire_id'] > 0)
		{
			$form = new Formulaires($filter['formulaire_id']);
			$where .= ' and q.classe_formulaire = ?';
			$data[] = $form->nom;
		}

		if(isset($filter['liste_item_id']) && $filter['liste_item_id'] > 0)
		{
			$where .= ' and q.liste_item_id = ?';
			$data[] = $filter['liste_item_id'];
		}

		$sql = "select q.id, q.*, qs.nom_$Langue as statut, f.code as fiche_code, qc.nom as question_categorie, case when q.usager_ajout_id = ? then 1 else 0 end as is_usager_demandeur, 
                  (select count(distinct coalesce(question_reponse_id, 1)) from notifications_qrt nq join notifications_qrt_usagers nqu on nq.id = nqu.notification_qrt_id where nq.question_id = q.id and nqu.ts_lu is null and nqu.usager_id = ?) as nb_notifications_all, 
                  (select count(distinct coalesce(question_reponse_id, 1)) from notifications_qrt nq join notifications_qrt_usagers nqu on nq.id = nqu.notification_qrt_id where nq.question_id = q.id and nq.question_reponse_id is null and nqu.ts_lu is null and nqu.usager_id = ?) as nb_notifications,
                  case when usager_ajout_str is not null then usager_ajout_str else concat(u.nom, ', ', u.prenom) end as demandeur, u.email as courriel_demandeur,
                  concat(ufd.nom, ', ', ufd.prenom) as conclueur, q.fiche_code as fiche_code_deleted
                from questions q
                left join usagers u on u.id = q.usager_ajout_id
                left join usagers ufd on ufd.id = q.usager_fin_discussion_id
                left join fiches f on q.fiche_id = f.id
                left join questions_statuts qs on qs.id = q.question_statut_id
                left join questions_categories qc on q.question_categorie_id = qc.id
                where true $where
                order by $orderBy, nb_notifications_all desc
                $sql_limit";
		//printDebug(dm_nl2br(db::interpolateQuery($sql, $data)));
		$rows = $db->getAssoc($sql, $data);

		$reponses = array();
		$groupes_usagers_demandeurs = array();
		$groupes_usagers_repondants = array();
		$usagers_demandeurs = array();
		$usagers_repondants = array();
		$all_usagers_demandeurs = array();
		$all_usagers_repondants = array();
		$fiches = array();
		$fiches_deleted = array();
		$questions_ids = array_keys($rows);
		if(count($questions_ids) > 0)
		{
			$sql = "select qr.id, question_id, case when qr.usager_repondant_str is not null then qr.usager_repondant_str else concat(u.nom, ', ', u.prenom) end as repondant, qr.ts_ajout, usager_repondant_id, reponse, u.email as courriel_repondant,
                    (select count(distinct coalesce(question_reponse_id, 1)) from notifications_qrt nq join notifications_qrt_usagers nqu on nq.id = nqu.notification_qrt_id where nq.question_reponse_id = qr.id and nqu.ts_lu is null and nqu.usager_id = ?) as nb_notifications
					from questions q
					join questions_reponses qr on q.id = qr.question_id
					left join usagers u on u.id = qr.usager_repondant_id
					where q.id in (".db::placeHolders($questions_ids).")
					order by qr.ts_ajout";
			$questions_ids = array_merge(array(getIdUsager()), $questions_ids);
			$rows_reponses = $db->getAll($sql, $questions_ids);

			foreach($rows_reponses as $i => $row_reponse)
			{
				$row_reponse['type_repondant'] = $row_reponse['usager_repondant_id'] == $rows[$row_reponse['question_id']]['usager_ajout_id'] || isset($demandeurs[$row_reponse['usager_repondant_id']]) ? 'demandeur' : 'repondant';
				$row_reponse['ts_ajout'] = dm_substr($row_reponse['ts_ajout'], 0, 16);
				$reponses[intval($row_reponse['question_id'])][$row_reponse['id']] = $row_reponse;
			}

			$sql = "select qgu.id, qgu.nom, question_id
		        from questions_to_groupes_usagers qtgu
		        join questions_groupes_usagers qgu on qtgu.question_groupe_usager_id = qgu.id
		        where question_id in (".db::placeHolders($questions_ids).") and type_groupe = 'demandeur'
		        order by qgu.nom";
			$rows_groupes_usagers_demandeurs = $db->getAll($sql, $questions_ids);

			foreach($rows_groupes_usagers_demandeurs as $i => $row)
			{
				$groupes_usagers_demandeurs[intval($row['question_id'])][$row['id']] = $row;
			}

			$sql = "select qgu.id, qgu.nom, question_id
		        from questions_to_groupes_usagers qtgu
		        join questions_groupes_usagers qgu on qtgu.question_groupe_usager_id = qgu.id
		        where question_id in (".db::placeHolders($questions_ids).") and type_groupe = 'repondant'
		        order by qgu.nom";
			$rows_groupes_usagers_repondants = $db->getAll($sql, $questions_ids);

			foreach($rows_groupes_usagers_repondants as $i => $row)
			{
				$groupes_usagers_repondants[intval($row['question_id'])][$row['id']] = $row;
			}

			$sql = "select u.id, concat(u.nom, ', ', u.prenom) as nom, question_id
		        from questions_to_usagers qtu
		        join usagers u on qtu.usager_id = u.id
		        where question_id in (".db::placeHolders($questions_ids).") and type_usager = 'demandeur'
		        order by nom";
			$rows_usagers_demandeurs = $db->getAll($sql, $questions_ids);

			foreach($rows_usagers_demandeurs as $i => $row)
			{
				$usagers_demandeurs[intval($row['question_id'])][$row['id']] = $row;
				$all_usagers_demandeurs[intval($row['question_id'])][$row['id']] = $row;
			}

			$sql = "select u.id, concat(u.nom, ', ', u.prenom) as nom, question_id, u.email as courriel_repondant
		        from questions_to_usagers qtu
		        join usagers u on qtu.usager_id = u.id
		        where question_id in (".db::placeHolders($questions_ids).") and type_usager = 'repondant'
		        order by nom";
			$rows_usagers_repondants = $db->getAll($sql, $questions_ids);

			foreach($rows_usagers_repondants as $i => $row)
			{
				$usagers_repondants[intval($row['question_id'])][$row['id']] = $row;
				$all_usagers_repondants[intval($row['question_id'])][$row['id']] = $row;
			}

			$sql = "select u.id as cle, u.id, concat(u.nom, ', ', u.prenom) as nom, question_id
		        from questions_to_groupes_usagers qtgu
		        join questions_groupes_usagers qgu on qtgu.question_groupe_usager_id = qgu.id
		        join questions_groupes_usagers_listes qgul on qgu.id = qgul.question_groupe_usager_id
		        join usagers u on u.id = qgul.usager_id
		        where qtgu.question_id in (".db::placeHolders($questions_ids).") and qtgu.type_groupe = 'demandeur'
		        order by qgu.nom";
			$rows_usagers_demandeurs_from_groupes = $db->getAssoc($sql, $questions_ids);

			foreach($rows_usagers_demandeurs_from_groupes as $i => $row)
			{
				$all_usagers_demandeurs[intval($row['question_id'])][$row['id']] = $row;
			}

			$sql = "select u.id as cle, u.id, concat(u.nom, ', ', u.prenom) as nom, question_id, u.email as courriel_repondant
		        from questions_to_groupes_usagers qtgu
		        join questions_groupes_usagers qgu on qtgu.question_groupe_usager_id = qgu.id
		        join questions_groupes_usagers_listes qgul on qgu.id = qgul.question_groupe_usager_id
		        join usagers u on u.id = qgul.usager_id
		        where qtgu.question_id in (".db::placeHolders($questions_ids).") and qtgu.type_groupe = 'repondant'
		        order by qgu.nom";
			$rows_usagers_repondants_from_groupes = $db->getAssoc($sql, $questions_ids);

			foreach($rows_usagers_repondants_from_groupes as $i => $row)
			{
				$all_usagers_repondants[intval($row['question_id'])][$row['id']] = $row;
			}

			$sql = "select f.*, question_id
	                from questions_fiches ef
	                join fiches f on ef.fiche_id = f.id
	                where question_id in (".db::placeHolders($questions_ids).")
                    order by f.code";
			$fiches_tmp = $db->getAll($sql, $questions_ids);

			foreach($fiches_tmp as $row_col)
			{
				$fiches[intval($row_col['question_id'])][$row_col['id']] = $row_col;
			}

			$sql = "select ef.*
	                from questions_fiches ef
	                left join fiches f on ef.fiche_id = f.id
	                where question_id in (".db::placeHolders($questions_ids).") and f.id is null
                    order by ef.fiche_code";
			$fiches_deleted_tmp = $db->getAll($sql, $questions_ids);

			foreach($fiches_deleted_tmp as $row_col)
			{
				$fiches_deleted[intval($row_col['question_id'])][$row_col['fiche_code']] = $row_col;
			}

			$sql = "select q.*, q.id as question_id
	                from questions q
	                left join fiches f on q.fiche_id = f.id
	                where q.id in (".db::placeHolders($questions_ids).") and f.id is null
                    order by q.fiche_code";
			$fiches_deleted_tmp_2 = $db->getAll($sql, $questions_ids);

			foreach($fiches_deleted_tmp_2 as $row_col)
			{
				$fiches_deleted[intval($row_col['question_id'])][$row_col['fiche_code']] = $row_col;
			}
		}

		$liste = array();
		foreach($rows as $id => $row)
		{
			$row['fiches'] = isset($fiches[intval($row['id'])]) ? $fiches[intval($row['id'])] : array();
			$row['fiches_deleted'] = isset($fiches_deleted[intval($row['id'])]) ? $fiches_deleted[intval($row['id'])] : array();
			$obj = new self(intval($id), $row);

			$obj->ts_ajout = dm_substr($obj->ts_ajout, 0, 16);
			$obj->ts_fin_discussion = dm_substr($obj->ts_fin_discussion, 0, 16);

			$obj->reponses = array();
			if(isset($reponses[$obj->id]))
			{
				$obj->reponses = $reponses[$obj->id];
			}

			$obj->groupes_usagers_demandeurs = array();
			if(isset($groupes_usagers_demandeurs[$obj->id]))
			{
				$obj->groupes_usagers_demandeurs = $groupes_usagers_demandeurs[$obj->id];
			}

			$obj->groupes_usagers_repondants = array();
			if(isset($groupes_usagers_repondants[$obj->id]))
			{
				$obj->groupes_usagers_repondants = $groupes_usagers_repondants[$obj->id];
			}

			$obj->usagers_demandeurs = array();
			if(isset($usagers_demandeurs[$obj->id]))
			{
				$obj->usagers_demandeurs = $usagers_demandeurs[$obj->id];
			}

			$obj->usagers_repondants = array();
			if(isset($usagers_repondants[$obj->id]))
			{
				$obj->usagers_repondants = $usagers_repondants[$obj->id];
			}

			$obj->all_usagers_demandeurs = array();
			if(isset($all_usagers_demandeurs[$obj->id]))
			{
				$obj->all_usagers_demandeurs = $all_usagers_demandeurs[$obj->id];
			}

			$obj->all_usagers_repondants = array();
			if (isset($all_usagers_repondants[$obj->id])) {
				$obj->all_usagers_repondants = $all_usagers_repondants[$obj->id];
			} else if (isset($obj->usager_repondant_str) && strlen($obj->usager_repondant_str) > 0) {
                $obj->all_usagers_repondants = [
                    ['cle' => -1, 'id' => -1, 'nom' => $obj->usager_repondant_str, 'question_id' => $obj->id, 'courriel_repondant' => '']
                ];
            }
			$obj->is_usager_repondant = isset($obj->all_usagers_repondants[getIdUsager()]) ? 1 : 0;
			$obj->is_usager_can_reponse = havePermission(array('repondant_qrt_bcf')) ? 1 : 0;

			$obj->can_conclure_qrt = (havePermission('conclure_qrt_bcf') ? 1 : 0);

			$liste[$id] = $obj;
		}

		return $liste;
	}

	/**
	 * @param array $filter
	 * @return mixed
	 */
	public static function getCount(array $filter = array())
	{
		global $db;

		$where = ' and q.projet_id = ?';
		$data = array(getProjetId());

		if(isset($filter['niveau']) && count($filter['niveau']) > 0)
		{
			foreach($filter['niveau'] as $niveau_id => $niveau_element_id)
			{
				if(is_numeric($niveau_element_id) && intval($niveau_element_id) > 0)
				{
					$where .= " and exists (
					                select question_id from questions_fiches as qf
                                    join fiches_batiment_niveaux_elements as niveau_217 on niveau_217.fiche_id = qf.fiche_id and niveau_217.batiment_niveau_id = ?
                                    join batiments_niveaux_elements as niveau_element_217 on niveau_217.batiment_niveau_element_id = niveau_element_217.id and niveau_element_217.id = ?
                                    where qf.question_id = q.id
                    )";
					$data[] = $niveau_id;
					$data[] = $niveau_element_id;
				}
			}
		}

		if(isset($filter['terme']) && dm_strlen($filter['terme']) > 0)
		{
			$where .= ' and (q.code like ? or q.sujet like ? or q.question like ?)';
			$data[] = '%'.$filter['terme'].'%';
			$data[] = '%'.$filter['terme'].'%';
			$data[] = '%'.$filter['terme'].'%';
		}

		if(isset($filter['question_statut_id']) && $filter['question_statut_id'] > 0)
		{
			$where .= ' and q.question_statut_id = ?';
			$data[] = $filter['question_statut_id'];
		}

		if(isset($filter['question_categorie_id']) && $filter['question_categorie_id'] > 0)
		{
			$where .= ' and q.question_categorie_id = ?';
			$data[] = $filter['question_categorie_id'];
		}

		if(isset($filter['usager_ajout_id']) && $filter['usager_ajout_id'] > 0)
		{
			$where .= ' and q.usager_ajout_id = ?';
			$data[] = $filter['usager_ajout_id'];
		}

		if(isset($filter['usager_demandeur_id']) && $filter['usager_demandeur_id'] > 0)
		{
			$where .= ' and q.usager_ajout_id = ?';
			$data[] = $filter['usager_demandeur_id'];
		}

		if(isset($filter['fiche_id']) && $filter['fiche_id'] > 0)
		{
			$where .= ' and q.fiche_id = ?';
			$data[] = $filter['fiche_id'];
		}

		if(isset($filter['table_name']) && dm_strlen($filter['table_name']) > 0)
		{
			$where .= ' and q.table_name = ?';
			$data[] = $filter['table_name'];
		}

		if(isset($filter['liste_item_id']) && $filter['liste_item_id'] > 0)
		{
			$where .= ' and q.liste_item_id = ?';
			$data[] = $filter['liste_item_id'];
		}

		if(isset($filter['classe_formulaire']) && dm_strlen($filter['classe_formulaire']) > 0)
		{
			$where .= ' and q.classe_formulaire = ?';
			$data[] = $filter['classe_formulaire'];
		}

		if(isset($filter['discipline_id']) && $filter['discipline_id'] > 0)
		{
			$where .= ' and q.discipline_id = ?';
			$data[] = $filter['discipline_id'];
		}

		if(isset($filter['is_prioritaire']) && $filter['is_prioritaire'] > -1)
		{
			$where .= ' and q.is_prioritaire = ?';
			$data[] = $filter['is_prioritaire'];
		}

		if(isset($filter['usager_repondant_id']) && $filter['usager_repondant_id'] > 0)
		{
			$where .= ' and (
			                    exists (select question_id from questions_to_usagers qtu where qtu.question_id = q.id and usager_id = ?) 
			                    or 
			                    exists (select question_id from questions_to_groupes_usagers qtgu join questions_groupes_usagers_listes qgul on qgul.question_groupe_usager_id = qtgu.question_groupe_usager_id where qtgu.question_id = q.id and usager_id = ?) 
			                )';
			$data[] = $filter['usager_repondant_id'];
			$data[] = $filter['usager_repondant_id'];
		}

		if(isset($filter['formulaire_id']) && $filter['formulaire_id'] > 0)
		{
		    $form = new Formulaires($filter['formulaire_id']);
			$where .= ' and q.classe_formulaire = ?';
			$data[] = $form->nom;
		}

		if(isset($filter['liste_item_id']) && $filter['liste_item_id'] > 0)
		{
			$where .= ' and q.liste_item_id = ?';
			$data[] = $filter['liste_item_id'];
		}

		$sql = "select count(*)
                from questions q
                where true $where";
		//printDebug(dm_nl2br(db::interpolateQuery($sql, $data)));
		$nb = $db->getOne($sql, $data);

		return $nb;
	}

    public static function getExportBCF($filter = array(), $orderBy = 'nb_notifications_all desc, statut, ts_ajout desc')
    {
        global $db, $isDev, $CodeClient;

        $bcf_guids_to_update = [];

        list($slash, $path_tmp) = FiltredCache::initPersonalizedTmpPath();

        $destination = $path_tmp . $slash . "export.zip";
        $zip = new ZipArchive();
        $open = $zip->open($destination, ZipArchive::CREATE | ZIPARCHIVE::OVERWRITE);
        if ($open !== true) {
            throw new Exception('Inccapable de créer le ZipArchive '.getDebug($open));
        }

        $filter['exporter_items'] = 1;
        $questions = self::getListe($filter, $orderBy);

        $zip->addFile('../bcf.version', 'bcf.version');

        foreach ($questions as $question) {
            if (isset($question->bcf_guid) && strlen($question->bcf_guid) > 0) {
                $bcf_guid_question = $question->bcf_guid;
            } else {
                $bcf_guid_question = uuid();
                $bcf_guids_to_update[] = [
                    'table' => 'questions',
                    'id' => $question->id,
                    'bcf_guid' => $bcf_guid_question
                ];
            }

            $markup = new SimpleXMLElement('<Markup/>');

            $topic = $markup->addChild('Topic', '');

            $topic->addAttribute('Guid', $bcf_guid_question);

            $topic->addChild('Title', $question->sujet);
            $topic->addChild('Priority', ($question->is_prioritaire ? 'high' : 'normal'));
            $topic->addChild('CreationDate', str_replace(' ', 'T', $question->ts_ajout . ':00.0000') . '-05:00');
            $topic->addChild('CreationAuthor', $question->demandeur . (!isset($question->courriel_demandeur) || strlen($question->courriel_demandeur) == 0 ? '' : ' (' . $question->courriel_demandeur . ')'));
            if (isset($question->ts_fin_discussion_max) && strlen($question->ts_fin_discussion_max) > 0) {
                $topic->addChild('DueDate', str_replace(' ', 'T', $question->ts_fin_discussion_max) . 'Z');
            }
            $topic->Description[0] = $question->question;

            foreach ($question->all_usagers_repondants as $repondant) {
                $topic->addChild('AssignedTo', $repondant['nom'] . (!isset($repondant['courriel_repondant']) || strlen($repondant['courriel_repondant']) == 0 ? '' : ' (' . $repondant['courriel_repondant'] . ')'));
            }

            foreach ($question->reponses as $reponse) {
                if (isset($reponse['bcf_guid']) && strlen($reponse['bcf_guid']) > 0) {
                    $bcf_guid_reponse = $reponse['bcf_guid'];
                } else {
                    $bcf_guid_reponse = uuid();
                    $bcf_guids_to_update[] = [
                        'table' => 'questions_reponses',
                        'id' => $reponse['id'],
                        'bcf_guid' => $bcf_guid_reponse
                    ];
                }
                $comment = $markup->addChild('Comment', '');
                $comment->addAttribute('Guid', $bcf_guid_reponse);

                $comment->addChild('Date', str_replace(' ', 'T', $reponse['ts_ajout'] . ':00.0000') . '-05:00');
                $comment->addChild('Author', $reponse['repondant'] . (!isset($reponse['courriel_repondant']) || strlen($reponse['courriel_repondant']) == 0 ? '' : ' (' . $reponse['courriel_repondant'] . ')'));
                $comment->Comment[0] = ($reponse['reponse'] ?? '');
            }

            $dom = new DOMDocument('1.0');
            $dom->preserveWhiteSpace = false;
            $dom->formatOutput = true;
            $dom_xml = dom_import_simplexml($markup);
            $dom_xml = $dom->importNode($dom_xml, true);
            $dom_xml = $dom->appendChild($dom_xml);
            $result = $dom->saveXML();

            if (!is_dir($path_tmp . $slash . $bcf_guid_question)) {
                mkdir($path_tmp . $slash . $bcf_guid_question, 0777, true);
            }
            file_put_contents($path_tmp . $slash . $bcf_guid_question . $slash . 'markup.bcf', $result);

            $zip->addEmptyDir($bcf_guid_question);
            $zip->addFile($path_tmp . $slash . $bcf_guid_question . $slash . 'markup.bcf', $bcf_guid_question . '/markup.bcf');
        }

        $zip->close();

        $db->autocommit(false);
        foreach ($bcf_guids_to_update as $data) {
            $sql = "update `{$data['table']}` set bcf_guid = ? where id = ?";
            $db->query($sql, [$data['bcf_guid'], $data['id']]);
        }
        $db->commit();

        header("Pragma: public");
        header("Expires: 0");
        header("Cache-Control: must-revalidate, post-check=0, pre-check=0");
        header("Cache-Control: public");
        header("Content-Description: File Transfer");
        header("Content-type: application/octet-stream");
        header('Content-Disposition: attachment; filename="export.zip"');
        header("Content-Transfer-Encoding: binary");
        header("Content-Length: ".filesize($destination));
        ob_end_flush();
        @readfile($destination);
    }

	public static function getExport($filter = array(), $orderBy = 'nb_notifications_all desc, statut, ts_ajout desc')
    {
        global $tables;

        $filter['exporter_items'] = 1;
        $questions = self::getListe($filter, $orderBy);

	    $col = 0;
	    $dataArray = array();
	    $dataArray[0][$col++] = txt('Numéro QRT-BCF', false);
	    $dataArray[0][$col++] = txt('Catégorie', false);
	    $dataArray[0][$col++] = txt('Fiche(s)', false);
	    $dataArray[0][$col++] = txt('Équipement', false);
	    $dataArray[0][$col++] = txt('Local d\'appartenance', false);
	    $dataArray[0][$col++] = txt('Discipline', false);
	    $dataArray[0][$col++] = txt('Statut', false);
	    $dataArray[0][$col++] = txt('Demandeur', false);
	    $dataArray[0][$col++] = txt('Date/heure question', false);
	    $dataArray[0][$col++] = txt('Sujet', false);
	    $dataArray[0][$col++] = txt('Question', false);
	    $dataArray[0][$col++] = txt('Répondant', false);
	    $dataArray[0][$col++] = txt('Date/heure réponse', false);
	    $dataArray[0][$col++] = txt('Réponse', false);
	    $dataArray[0][$col++] = txt('Date/heure conclusion', false);
	    $dataArray[0][$col++] = txt('Concluant', false);

	    $row = 1;
	    foreach($questions as $i => $question)
	    {
	        if(count($question->reponses) == 0)
            {
	            $col = 0;
	            $dataArray[$row][$col++] = $question->getRaw('code');
	            $dataArray[$row][$col++] = $question->getRaw('question_categorie');
	            if(dm_strlen($question->getRaw('fiche_code')) > 0)
	            {
		            $dataArray[$row][$col++] = $question->getRaw('fiche_code');
	            }
	            else if(isset($question->fiches) && is_array($question->fiches) && count($question->fiches) <= 5)
	            {
		            $fiches = '';
		            $iter = 0;
		            foreach($question->fiches as $i => $fiche)
		            {
			            $fiches .= ($iter == 0 ? '' : ', ').$fiche->getRaw('code');
			            $iter++;
		            }
		            $dataArray[$row][$col++] = $fiches;
	            }
	            else
	            {
		            $dataArray[$row][$col++] = txt('Multiple', false);
	            }

	            if(isset($question->classe_formulaire) && dm_strlen($question->classe_formulaire) > 0)
	            {
		            if($question->classe_formulaire == 'EquipementsBioMedicaux')
		            {
			            $classNameItem = $tables['client'][$question->nom_table]['className'];
			            $item = new $classNameItem($question->liste_item_id);
			            $dataArray[$row][$col++] = $item->getRaw('nom').' - '.$item->getRaw('description');
		            }
		            else if($question->classe_formulaire == 'GenerauxEquipements')
		            {
			            if($question->fiche_id > 0) {
				            $fiche = new Fiches($question->fiche_id);
				            $dataArray[$row][$col++] = $fiche->getRaw('code');
			            }
		            }
	            }
	            else
	            {
		            $dataArray[$row][$col++] = 'N/A';
	            }

	            if(isset($question->classe_formulaire) && dm_strlen($question->classe_formulaire) > 0)
	            {
		            if($question->classe_formulaire == 'EquipementsBioMedicaux')
		            {
		                $fiche_code = '';
			            if($question->fiche_id > 0)
			            {
				            $fiche = new Fiches($question->fiche_id);
				            $fiche_code = $fiche->getRaw('code');
			            }
			            $dataArray[$row][$col++] = $fiche_code;
		            }
		            else if($question->classe_formulaire == 'GenerauxEquipements')
		            {
			            $fiche_code = '';
			            if($question->fiche_id > 0) {
				            $fiche = new Fiches($question->fiche_id);
				            $fiche_parent = null;
				            if($fiche->fiche_local_id > 0) {
					            $fiche_parent = new Fiches($fiche->fiche_local_id);
					            $fiche_code = $fiche_parent->getRaw('code');
				            }
			            }
			            $dataArray[$row][$col++] = $fiche_code;
		            }
		            else
                    {
	                    $dataArray[$row][$col++] = 'N/A';
                    }
	            }
	            else
	            {
		            $dataArray[$row][$col++] = 'N/A';
	            }

	            if(isset($question->classe_formulaire) && dm_strlen($question->classe_formulaire) > 0)
	            {
		            if($question->classe_formulaire == 'EquipementsBioMedicaux')
		            {
		                if ((int)$question->discipline_id > 0) {
			                $discipline = new Discipline($question->discipline_id);
			                $dataArray[$row][$col++] = $discipline->getRaw('nom');
		                } else {
			                $dataArray[$row][$col++] = "";
                        }
		            }
		            else if($question->classe_formulaire == 'GenerauxEquipements')
		            {
			            if ((int)$question->discipline_id > 0) {
                            $discipline = new Discipline($question->discipline_id);
                            $dataArray[$row][$col++] = $discipline->getRaw('nom');
                        } else {
                            $dataArray[$row][$col++] = "";
                        }
		            }
		            else
		            {
			            $dataArray[$row][$col++] = 'N/A';
		            }
	            }
	            else
	            {
		            $dataArray[$row][$col++] = 'N/A';
	            }

	            $dataArray[$row][$col++] = $question->getRaw('statut');
	            $dataArray[$row][$col++] = $question->getRaw('demandeur');
	            $dataArray[$row][$col++] = dm_substr($question->getRaw('ts_ajout'), 0, 16);
	            $dataArray[$row][$col++] = $question->getRaw('sujet');
	            $dataArray[$row][$col++] = $question->getRaw('question');
	            $dataArray[$row][$col++] = '';
	            $dataArray[$row][$col++] = '';
	            $dataArray[$row][$col++] = '';
	            $dataArray[$row][$col++] = dm_substr($question->getRaw('ts_fin_discussion'), 0, 16);
	            $dataArray[$row][$col++] = $question->getRaw('conclueur');
	            $row++;
            }
            else
            {
                foreach ($question->reponses as $reponse)
                {
	                $col = 0;
	                $dataArray[$row][$col++] = $question->getRaw('code');
	                $dataArray[$row][$col++] = $question->getRaw('question_categorie');
	                if(dm_strlen($question->fiche_code) > 0)
	                {
		                $dataArray[$row][$col++] = $question->getRaw('fiche_code');
	                }
	                else if(isset($question->fiches) && is_array($question->fiches) && count($question->fiches) <= 5)
	                {
		                $fiches = '';
		                $iter = 0;
		                foreach($question->fiches as $i => $fiche)
		                {
			                $fiches .= ($iter == 0 ? '' : ', ').$fiche->getRaw('code');
			                $iter++;
		                }
		                $dataArray[$row][$col++] = $fiches;
	                }
	                else
	                {
		                $dataArray[$row][$col++] = txt('Multiple', false);
	                }

	                if(isset($question->classe_formulaire) && dm_strlen($question->classe_formulaire) > 0)
	                {
		                if($question->classe_formulaire == 'EquipementsBioMedicaux')
		                {
			                $classNameItem = $tables['client'][$question->nom_table]['className'];
			                $item = new $classNameItem($question->liste_item_id);
			                $dataArray[$row][$col++] = $item->getRaw('nom').' - '.$item->getRaw('description');
		                }
		                else if($question->classe_formulaire == 'GenerauxEquipements')
		                {
			                $code_fiche = '';
			                if($question->fiche_id > 0) {
				                $fiche = new Fiches($question->fiche_id);
				                $code_fiche = $fiche->getRaw('code');
			                }
			                $dataArray[$row][$col++] = $code_fiche;
		                }
		                else
		                {
			                $dataArray[$row][$col++] = 'N/A';
		                }
	                }
	                else
	                {
		                $dataArray[$row][$col++] = 'N/A';
	                }

	                if(isset($question->classe_formulaire) && dm_strlen($question->classe_formulaire) > 0)
	                {
		                if($question->classe_formulaire == 'EquipementsBioMedicaux')
		                {
			                $code_fiche = '';
			                if($question->fiche_id > 0)
			                {
				                $fiche = new Fiches($question->fiche_id);
				                $code_fiche = $fiche->getRaw('code');
			                }
			                $dataArray[$row][$col++] = $code_fiche;
		                }
		                else if($question->classe_formulaire == 'GenerauxEquipements')
		                {
		                    $code_fiche = '';
			                if($question->fiche_id > 0) {
				                $fiche = new Fiches($question->fiche_id);
				                $fiche_parent = null;
				                if($fiche->fiche_local_id > 0) {
					                $fiche_parent = new Fiches($fiche->fiche_local_id);
					                $code_fiche = $fiche_parent->getRaw('code');
				                }
			                }
			                $dataArray[$row][$col++] = $code_fiche;
		                }
		                else
		                {
			                $dataArray[$row][$col++] = 'N/A';
		                }
	                }
	                else
	                {
		                $dataArray[$row][$col++] = 'N/A';
	                }

	                if(isset($question->classe_formulaire) && dm_strlen($question->classe_formulaire) > 0)
	                {
		                if($question->classe_formulaire == 'EquipementsBioMedicaux')
		                {
			                if ((int)$question->discipline_id > 0) {
				                $discipline = new Discipline($question->discipline_id);
				                $dataArray[$row][$col++] = $discipline->getRaw('nom');
			                } else {
				                $dataArray[$row][$col++] = '';
                            }
		                }
		                else if($question->classe_formulaire == 'GenerauxEquipements')
		                {
			                if ((int)$question->discipline_id > 0) {
                                $discipline = new Discipline($question->discipline_id);
                                $dataArray[$row][$col++] = $discipline->getRaw('nom');
                            } else {
                                $dataArray[$row][$col++] = '';
                            }
		                }
		                else
		                {
			                $dataArray[$row][$col++] = 'N/A';
		                }
	                }
	                else
	                {
		                $dataArray[$row][$col++] = 'N/A';
	                }
	                $dataArray[$row][$col++] = $question->getRaw('statut');
	                $dataArray[$row][$col++] = $question->getRaw('demandeur');
	                $dataArray[$row][$col++] = dm_substr($question->getRaw('ts_ajout'), 0, 16);
	                $dataArray[$row][$col++] = $question->getRaw('sujet');
	                $dataArray[$row][$col++] = $question->getRaw('question');
	                $dataArray[$row][$col++] = $reponse->getRaw('repondant');
	                $dataArray[$row][$col++] = dm_substr($reponse->getRaw('ts_ajout'), 0, 16);
	                $dataArray[$row][$col++] = $reponse->getRaw('reponse');
	                $dataArray[$row][$col++] = dm_substr($question->getRaw('ts_fin_discussion'), 0, 16);
	                $dataArray[$row][$col++] = $question->getRaw('conclueur');
	                $row++;
                }
            }
	    }
	    exportExcelFromDataArray($dataArray, 'QRTs', 'QRTs', 'qrts');
    }

	public function getEditorHTML($iteration = 0, $only_champs_denormalises = false)
	{
		?>
        <script>
            $(document).ready(function() {
				$('#formulaire_id').change(function(){
					if (this.value !== '') {
						$.get('index.php', {
							action: 'get_list_item_ids_from_formulaire_id',
							formulaire_id: this.value
						}, function (options) {
							$('#liste_item_id').html(options);
							$('#liste_item_id').select2({width: '260px'});
							//$('#listeFiches_to').html('');
							//$('#fiches_parent').hide();
						});
					} else {
						$('#liste_item_id').html('');
						$('#liste_item_id').select2({width: '260px'});
						//$('#fiches_parent').show();
					}
				});
				// $('#formulaire_id').trigger('change');
				$('#liste_item_id').select2({width: '260px'});
            });
        </script>
        <style>
            .blocsFiche, #fiche table, #fiche tr, #fiche td, #fiche th, .ui-dialog table, .ui-dialog tr, .ui-dialog td, .ui-dialog th {
                background: none!important;
            }
            .blocsFicheTitre {
                margin-top: 0px!important;
            }
            .span_mise_en_page {
                display: block!important;
            }
            .span_mise_en_page label {
                display: inline-block!important;
                background: none!important;
                position: relative;
                top: -6px;
                margin-bottom: 5px;
                font-size: 13px;
            }
            #blocDonnees select, #blocFiltre select {
                width: 260px!important;
            }
            #blocRepondants select {
                width: 200px!important;
                min-width: 200px!important;
            }
            div.comboselectbox.searchable div.combowrap:first-child ul.comboselect {
                height: 90%!important;
            }
            div.comboselectbox div.combowrap {
                padding: 10px!important;
                width: 175px!important;
            }
            li.used a.add {
                background-image: none!important;
            }
            #blocDonnees input, #blocDonnees textarea {
                width: 370px!important;
                box-sizing: border-box;
            }
            td {
            }
        </style>
        <fieldset>
            <div style="float: left;">
                <div id="blocDonnees" style="padding: 0px 0 0 30px; width: 450px;">
                    <input type="hidden" name="question_statut_id" value="<?=(isset($this->question_statut_id) ? $this->question_statut_id : 2)?>" />
                    <b style="font-weight: bold;"><?= txt('Identification de la QRT-BCF:') ?></b><br/><br/>
                    <table>
                    <?
                    $is_from_coordination = isset($_GET['from_coordination']);
                    if ($is_from_coordination) {
                        $this->fiche_id = intval($_GET['fiche_id']);
                        $this->nom_table = $_GET['nom_table'];
                        $this->liste_item_id = (intval($_GET['liste_item_id']) > 0 ? intval($_GET['liste_item_id']) : '');
                        $this->classe_formulaire = $_GET['classe_formulaire'];
                        $this->discipline_id = intval($_GET['discipline_id']);
                        $this->question_categorie_id = 888888;
                        $this->question_categorie = txt('Coordination');
                    }

                    if (($is_from_coordination && isset($this->classe_formulaire) && dm_strlen($this->classe_formulaire) > 0) || $this->question_categorie_id == 888888) {
                        ?>
                        <tr style="width: 450px;  display: table-row;">
                            <td style="width: 100px;color: red; padding-bottom: 5px!important;"><?php print txt('Catégorie'); ?></td>
                            <td style="padding-bottom: 5px!important;">
	                            <?php
	                            if ($this->question_categorie_id <> 888888 && ($this->id == 0 || ($this->id > 0 && $this->usager_ajout_id == getIdUsager()) || isMaster() || havePermission(array('admin_qrt_bcf')))) {
		                            ?>
                                    <select id="question_categorie_id" name="question_categorie_id">
                                        <option value=""> </option>
			                            <?= QuestionsCategories::getOptions(array(), $this->question_categorie_id) ?>
                                    </select>
		                            <?php
	                            } else {
		                            print $this->question_categorie;
		                            ?><input type="hidden" name="question_categorie_id"
                                             value="<?= $this->question_categorie_id ?>" /><?php
	                            }
	                            ?>
                            </td>
                        </tr>
                        <?
                        ?><input type="hidden" name="fiche_id" value="<?= $this->fiche_id ?>" /><?
                        ?><input type="hidden" name="liste_item_id" value="<?= $this->liste_item_id ?>" /><?
                        ?><input type="hidden" name="discipline_id" value="<?= $this->discipline_id ?>" /><?
                    }
                    else {
                        ?>
                            <tr style="width: 450px;  display: table-row;">
                                <td style="width: 100px;color: red; padding-bottom: 5px!important;"><?php print txt('Catégorie'); ?></td>
                                <td style="padding-bottom: 5px!important;">
                                    <?php
                                    if ($this->id == 0 || ($this->id > 0 && $this->usager_ajout_id == getIdUsager()) || isMaster() || havePermission(array('admin_qrt_bcf'))) {
                                        ?>
                                        <select id="question_categorie_id" name="question_categorie_id">
                                            <option value=""> </option>
                                            <?= QuestionsCategories::getOptions(array('ids_notIn' => array(888888)), $this->question_categorie_id) ?>
                                        </select>
                                        <?php
                                    } else {
                                        print $this->question_categorie;
                                        ?><input type="hidden" name="question_categorie_id"
                                                 value="<?= $this->question_categorie_id ?>" /><?php
                                    }
                                    ?>
                                </td>
                            </tr>
                        <?
                    }
                    ?>

                        <tr style="width: 450px;  display: table-row;">
                            <td style="width: 100px;color: red; padding-bottom: 5px!important;"><?php print txt('Sujet'); ?></td>
                            <td style="color: blue; vertical-align: top; padding-bottom: 5px!important;">
			                    <?php
			                    if($this->id == 0 || ($this->id > 0 && $this->usager_ajout_id == getIdUsager()) || isMaster() || havePermission(array('admin_qrt_bcf')))
			                    {
				                    ?>
                                    <input type="text" name="sujet" value="<?=($this->sujet)?>" />
				                    <?php
			                    }
			                    else
			                    {
				                    print dm_nl2br($this->sujet);
				                    ?><input type="hidden" name="sujet" value="<?=($this->sujet)?>" /><?php
			                    }
			                    ?>
                            </td>
                        </tr>

                        <tr style="width: 450px; margin-bottom: 5px; display: table-row;">
                            <td style="width: 100px; vertical-align: top;color: red; padding-bottom: <?=($this->id > 0 ? '7' : '2')?>px!important;"><?php print txt('Question'); ?></td>
                            <td style="color: blue; vertical-align: top; padding-bottom: <?=($this->id > 0 ? '7' : '2')?>px!important;">
			                    <?php
			                    if($this->id > 0)
			                    {
				                    print dm_nl2br($this->question);
				                    ?><textarea name="question" style="display: none;"><?=($this->question)?></textarea><?php
			                    }
			                    else
			                    {
				                    ?>
                                    <textarea name="question"><?=($this->question)?></textarea>
				                    <?php
			                    }
			                    ?>
                            </td>
                        </tr>

                        <tr style="width: 450px; margin-bottom: 5px; display: table-row;">
                            <td style="width: 100px; vertical-align: top; padding-bottom: <?=($this->id > 0 ? '7' : '5')?>px!important;"><?php print txt('Formulaire'); ?></td>
                            <td style="vertical-align: top; padding-bottom: <?=($this->id > 0 ? '7' : '5')?>px!important;">
			                    <?php
			                    if($this->id > 0 || $this->question_categorie_id == 888888)
			                    {
			                        if (isset($this->classe_formulaire) && dm_strlen($this->classe_formulaire) > 0) {
				                        $classForm = $this->classe_formulaire;
				                        print $classForm::TITRE;
			                        }
			                    }
			                    else
			                    {
                                    $selectedId = isset($this->classe_formulaire) ? Formulaires::getIdFromNom($this->classe_formulaire) : 144;
				                    ?>
                                    <select id="formulaire_id" name="formulaire_id">
                                        <option value=""> </option>
					                    <? Formulaires::getOptions(array('ids' => Formulaires::getFormulairesIdWithQuantite()), $selectedId)?>
                                    </select>
				                    <?php
			                    }
			                    ?>
                            </td>
                        </tr>

                        <tr style="width: 450px; margin-bottom: 5px; display: table-row;">
                            <td style="width: 100px; vertical-align: top; padding-bottom: 5px!important;"><?php print txt('Équipement'); ?></td>
                            <td style="vertical-align: top; padding-bottom: 5px!important;">
			                    <?php
			                    if($this->id > 0 || $this->question_categorie_id == 888888)
			                    {
			                        if (isset($this->classe_formulaire) && dm_strlen($this->classe_formulaire) > 0) {
				                        $classForm = $this->classe_formulaire;
				                        $formulaire = new Formulaires(Formulaires::getIdFromNom($classForm));
				                        $metaFirstChamp = $formulaire->getFirstChamp();
				                        $classItem = $metaFirstChamp['classe'];
				                        $item = new $classItem($this->liste_item_id);
				                        print $item->nom . (isset($item->description) ? ' - ' . $item->description : '');
			                        }
			                    }
			                    else
			                    {
				                    ?>
                                    <select id="liste_item_id" name="liste_item_id">
                                        <?
                                        if (isset($this->classe_formulaire) && dm_strlen($this->classe_formulaire) > 0) {
                                            $classForm = $this->classe_formulaire;
                                            $formulaire = new Formulaires(Formulaires::getIdFromNom($classForm));
                                            $metaFirstChamp = $formulaire->getFirstChamp();
                                            $classItem = $metaFirstChamp['classe'];
                                            $selectedId = $this->liste_item_id ?? -1;
                                            $classItem::getOptions([], $selectedId);
                                        }
                                        ?>
                                    </select>
				                    <?php
			                    }
			                    ?>
                            </td>
                        </tr>

                        <?
                        if (!empty($this->fiche_id)) {
                            ?>
                            <tr style="width: 450px; margin-bottom: 5px; display: table-row;">
                                <td style="width: 100px; vertical-align: top; padding-bottom: <?=($this->id > 0 ? '7' : '5')?>px!important;"><?php print txt('Fiche'); ?></td>
                                <td style="vertical-align: top; padding-bottom: <?=($this->id > 0 ? '7' : '5')?>px!important;">
                                    <?php
                                    $fiche = new Fiches($this->fiche_id);
                                    print $fiche->code
                                    ?>
                                </td>
                            </tr>
                            <?
                        }
                        ?>

                    </table>
                </div>
                <div style="padding: 40px 0 0 30px; width: 450px;">

                    <table style="padding: 40px 0 0 30px; width: 450px;">
                        <tr style="margin-bottom: 5px; display: table-row;">
                            <td style="width: 180px;"><?php print txt('Prioritaire'); ?></td>
                            <td style="height: 26px;">
                                <input type="checkbox" id="is_prioritaire" name="is_prioritaire"<?=($this->is_prioritaire == 1 ? ' checked="checked"' : '')?> />
                            </td>
                        </tr>
                        <tr style="margin-bottom: 5px; display: table-row;">
                            <td style="width: 180px; "><?php print txt('Délai de réponse maximal'); ?></td>
                            <td style="height: 26px;">
                                <input type="text" name="reponse_nb_jours_max" id="reponse_nb_jours_max" min="0" maxlength="3" style="width: 30px; text-align: center;" value="<?= $this->reponse_nb_jours_max ?>" class="justInteger" /> <?=txt('jour(s)')?>
                            </td>
                        </tr>
                        <tr style="margin-bottom: 5px; display: table-row;">
                            <td style="width: 180px;"><?php print txt('Date de conclusion attendue'); ?></td>
                            <td style="height: 26px;">
                                <input type="text" name="ts_fin_discussion_max" id="ts_fin_discussion_max" style="width: 90px; text-align: center;" value="<?= dm_substr($this->ts_fin_discussion_max,0,10) ?>" />
                            </td>
                        </tr>
                    </table>
                </div>
                <div id="blocRepondants" style="padding: 40px 0 0 30px; width: 450px;">
                    <b style="font-weight: bold;"><?= txt('Répondants:') ?></b><br/><br/>
                	<b style="color: red;"><?= txt('Veuillez sélectionner le(s) répondant(s) selon la nature de la question et notamment, la catégorie d\'équipement.') ?></b><br/><br/>
                    <table>
                        <tr>
                            <td width="200" valign="top">
	                            <?php
	                            if($this->id == 0 || ($this->id > 0 && $this->usager_ajout_id == getIdUsager()) || isMaster() || havePermission(array('admin_qrt_bcf'))) {
		                            ?>
                                    <div>
                                        <select class="multipleSelect" data-colname="repondant_groupe_usager_id">
                                            <option value="">- <?=txt('Ajouter un groupe d\'usagers')?> -</option>
				                            <?php
				                            QuestionsGroupesUsagers::getOptions();
				                            ?>
                                        </select>
                                    </div>
		                            <?php
	                            }
	                            ?>
                                <div id="repondant_groupe_usager_id">
		                            <?php
		                            if(isset($this->groupes_usagers_repondants) && is_array($this->groupes_usagers_repondants))
		                            {
			                            foreach($this->groupes_usagers_repondants as $i => $groupe)
			                            {
				                            ?>
                                            <div id="repondant_groupe_usager_id_<?=$groupe['id']?>" class="top_block">
					                            <?php
					                            if($this->id == 0 || ($this->id > 0 && $this->usager_ajout_id == getIdUsager()) || isMaster() || havePermission(array('admin_qrt_bcf'))) {
						                            ?>
                                                    <a href="javascript: void(0);" class="del_multiple">
                                                        <img src="css/images/icons/dark/trashcan.png" /></a>
						                            <?php
					                            }
					                            ?>
                                                <span><?=$groupe['nom']?></span>
                                                <input type="hidden" name="repondant_groupe_usager_id[]" value="<?=$groupe['id']?>" />
                                            </div>
				                            <?php
			                            }
		                            }
		                            ?>
                                </div>
                            </td><td style="color: red; text-align: center!important;" width="50" valign="top">
                                <?=txt('ET/OU')?>
                            </td><td width="200" valign="top" align="right">
		                        <?php
		                        if($this->id == 0 || ($this->id > 0 && $this->usager_ajout_id == getIdUsager()) || isMaster() || havePermission(array('admin_qrt_bcf'))) {
			                        ?>
                                    <div>
                                        <select class="multipleSelect" data-colname="repondant_usager_id">
                                            <option value="">- <?=txt('Ajouter un usager')?> -</option>
					                        <?php
					                        Usager::getOptions(array('permission_code' => 'repondant_qrt_bcf'));
					                        ?>
                                        </select>
                                    </div>
			                        <?php
		                        }
		                        ?>
                                <div id="repondant_usager_id">
			                        <?php
			                        if(isset($this->usagers_repondants) && is_array($this->usagers_repondants))
			                        {
				                        foreach($this->usagers_repondants as $i => $usager)
				                        {
					                        ?>
                                            <div id="repondant_usager_id_<?=$usager['id']?>" class="top_block">
						                        <?php
						                        if($this->id == 0 || ($this->id > 0 && $this->usager_ajout_id == getIdUsager()) || isMaster() || havePermission(array('admin_qrt_bcf'))) {
							                        ?>
                                                    <a href="javascript: void(0);" class="del_multiple">
                                                        <img src="css/images/icons/dark/trashcan.png" /></a>
							                        <?php
						                        }
						                        ?>
                                                <span><?=$usager['nom']?></span>
                                                <input type="hidden" name="repondant_usager_id[]" value="<?=$usager['id']?>" />
                                            </div>
					                        <?php
				                        }
			                        }
			                        ?>
                                </div>
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
<?
            if($this->question_categorie_id <> 888888)
            {
?>
                <div id="fiches_parent" style="margin: 0 0 15px 30px; height: 455px; width: 450px; float: left; position: relative; <?=(!($this->id == 0 || ($this->id > 0 && $this->usager_ajout_id == getIdUsager()) || isMaster() || havePermission(array('admin_qrt_bcf'))) ? 'pointer-events : none;' : '')?>">
                    <?
                    Fiches::getMultiSelector((isset($this->fiches) ? $this->fiches : []), '_QRT');
                    ?>
                </div>
            <?
            }
            ?>

            <br style="clear: both" />

            <div style="width: 450px; float: right;">
                <?php
                $documents = DocumentsListesElements::getListe(['nom_table' => 'questions', 'liste_item_id' => ($this->id > 0 ? $this->id : -1)]);
                if (count($documents) > 0) {
                    foreach ($documents as $i => $document) {
                        ?>
                        <div id="thumb_preview_<?=$document->id?>" class="document" style="text-align: center; margin: 15px; float: left; width: 300px; height: 330px; position: relative;">
                            <a href="index.php?action=download_document&id=<?=$document->id?>" target="_blank" style="display: inline-block; width: 300px; height: 300px;">
                                <img src="index.php?action=download_document&thumbnail=300&id=<?=$document->id?>" style="max-width: 300px; max-height: 300px; transform-origin: center center; transform: <?=(in_array($document->rotation, [90, 270]) ? 'translateY(36px)' : '')?> rotate(<?=$document->rotation?>deg);"></a>
                        </div>
                        <?
                    }
                }
                ?>
            </div>

        </fieldset>
<?php
	}

	/**
	 * @param $id
	 */
	public static function delete($id)
	{
		global $db;
		$db->autocommit(false);
		$db->query("DELETE FROM `notifications_qrt` WHERE question_id = ?", array($id));
		$db->query("DELETE FROM `questions_to_usagers` WHERE question_id = ?", array($id));
		$db->query("DELETE FROM `questions_to_groupes_usagers` WHERE question_id = ?", array($id));
		$db->query("DELETE FROM `questions_reponses` WHERE question_id = ?", array($id));
		$db->query("DELETE FROM `questions_fiches` WHERE question_id = ?", array($id));
		$db->query("DELETE FROM `questions` WHERE id = ?", array($id));
		$db->commit();
	}

	/**
	 * @param $data
	 * @param $id
	 * @return bool
	 */
	public static function validate($data, $id)
	{
		$valide = true;
		foreach($data as $colomnName => $value)
		{
			if($colomnName == 'sujet' && dm_strlen($value) == 0)
			{
				setErreur(txt('Le champ sujet est obligatoire.'));
				$valide = false;
			}
			if($colomnName == 'question' && dm_strlen($value) == 0)
			{
				setErreur(txt('Le champ question est obligatoire.'));
				$valide = false;
			}
			if($colomnName == 'question_categorie_id' && (int)$value == 0)
			{
				setErreur(txt('Le champ catégorie est obligatoire.'));
				$valide = false;
			}
		}

		return $valide;
	}

	/**
	 * @param null $usager_id
	 * @return mixed
	 */
	public static function getNbNotificationsNonLu($usager_id = null)
	{
		global $db;

		$usager_id_to_use = isset($usager_id) ? $usager_id : getIdUsager();
		$sql = "select count(distinct question_reponse_id) as nb 
                from notifications_qrt_usagers 
                join notifications_qrt on notifications_qrt.id = notification_qrt_id
                join questions on questions.id = question_id
                where usager_id = ".$usager_id_to_use." and ts_lu is null and projet_id = ". getProjetId();
		//printDebug(dm_nl2br(db::interpolateQuery($sql, array())));
		$nb = $db->getOne($sql);

		$sql = "select count(distinct question_id) as nb 
                from notifications_qrt_usagers 
                join notifications_qrt on notifications_qrt.id = notification_qrt_id
                join questions on questions.id = question_id
                where usager_id = ".$usager_id_to_use." and ts_lu is null and notifications_qrt.question_reponse_id is null and projet_id = ". getProjetId();
		//printDebug(dm_nl2br(db::interpolateQuery($sql, array())));
		$nb_2 = $db->getOne($sql);
		return $nb + $nb_2;
	}

	/**
	 * @param null $question_id
	 * @return array
	 */
	public static function getNotificationsNonEnvoye($question_id = null)
	{
		global $db;

		$where = "";
		$data = array();

		if(isset($question_id))
        {
	        $where .= " and nq.question_id = ? and q.is_prioritaire = 1";
	        $data[] = $question_id;
        }

		$sql = "select nqu.*, nqu.id as id_notif, q.id as question_id, u.email, concat(d.prenom, ' ', d.nom) as nom_demandeur, p.nom as nom_projet, concat(r.prenom, ' ', r.nom) as nom_repondant
                from notifications_qrt_usagers nqu
                join notifications_qrt nq on nq.id = notification_qrt_id
                join questions q on q.id = nq.question_id
                left join questions_reponses qr on nq.question_reponse_id = qr.id
                join usagers u on u.id = nqu.usager_id
                join usagers d on d.id = q.usager_ajout_id
                left join usagers r on r.id = qr.usager_repondant_id
                join projets p on p.id = q.projet_id
                where ts_lu is null and ts_envoye is null $where";
		//printDebug(dm_nl2br(db::interpolateQuery($sql, array())));
		return $db->getAll($sql, $data);
	}

	/**
	 * @param $question_id
	 * @param string $mode
	 * @param string $mode_envoi
	 */
	public static function sendNotificationEmails($question_id, $mode = 'question', $mode_envoi = 'differe')
    {
        global $CodeClient;
		post_async('http://127.0.0.1/notifications_qrt_envoi.php', array('debug' => 1, 'mode' => $mode, 'question_id' => $question_id, 'CodeClient' => $CodeClient, 'mode_envoi' => $mode_envoi));
    }

    public static function traitementSauvegardeSupplementaire($id, $requestData, $is_ajout)
    {
        global $db;
        $d = DateTime::createFromFormat('Y-m-d', $requestData['ts_fin_discussion_max']);
        $requestData['ts_fin_discussion_max'] = ($d && $d->format('Y-m-d') === $requestData['ts_fin_discussion_max']) ? $requestData['ts_fin_discussion_max'] : null;

        $sql = "update questions set is_prioritaire = ?, ts_fin_discussion_max = ? where id = ?";
        $db->query($sql, array((isset($requestData['is_prioritaire']) ? 1 : 0), $requestData['ts_fin_discussion_max'], $id));

        $sql = "insert into notifications_qrt (question_id) values (?)";
        $db->query($sql, array($id));
        $last_notif_id = $db->lastInsertId();

        $sql = "DELETE FROM questions_to_groupes_usagers WHERE question_id = ?";
        $db->query($sql, array($id));

        if (isset($requestData['demandeur_groupe_usager_id']) && count($requestData['demandeur_groupe_usager_id']) > 0) {
            foreach ($requestData['demandeur_groupe_usager_id'] as $ordre => $groupe_id) {
                $sql = "INSERT INTO questions_to_groupes_usagers (question_id, question_groupe_usager_id, type_groupe) 
                            VALUES (?, ?, 'demandeur')";
                $db->query($sql, array($id, $groupe_id));

                $sql = "select qgul.usager_id
						        from questions_groupes_usagers_listes qgul
						        where question_groupe_usager_id = ?";
                $usager_ids = $db->getCol($sql, array($groupe_id));

                foreach ($usager_ids as $ordre => $usager_id) {
                    if ($usager_id <> getIdUsager()) {
                        $sql = "insert into notifications_qrt_usagers (notification_qrt_id, usager_id, type_notification) values (?, ?, 'demandeur_to_demandeur')";
                        $db->query($sql, array($last_notif_id, $usager_id));
                    }
                }
            }
        }

        if (isset($requestData['repondant_groupe_usager_id']) && count($requestData['repondant_groupe_usager_id']) > 0) {
            foreach ($requestData['repondant_groupe_usager_id'] as $ordre => $groupe_id) {
                $sql = "INSERT INTO questions_to_groupes_usagers (question_id, question_groupe_usager_id, type_groupe) 
                            VALUES (?, ?, 'repondant')";
                $db->query($sql, array($id, $groupe_id));

                $sql = "select qgul.usager_id
						        from questions_groupes_usagers_listes qgul
						        where question_groupe_usager_id = ?";
                $usager_ids = $db->getCol($sql, array($groupe_id));

                foreach ($usager_ids as $ordre => $usager_id) {
                    if ($usager_id <> getIdUsager()) {
                        $sql = "insert into notifications_qrt_usagers (notification_qrt_id, usager_id, type_notification) values (?, ?, 'demandeur_to_repondant')";
                        $db->query($sql, array($last_notif_id, $usager_id));
                    }
                }
            }
        }

        $sql = "DELETE FROM questions_to_usagers WHERE question_id = ?";
        $db->query($sql, array($id));

        if (isset($requestData['demandeur_usager_id']) && count($requestData['demandeur_usager_id']) > 0) {
            foreach ($requestData['demandeur_usager_id'] as $ordre => $usager_id) {
                $sql = "INSERT INTO questions_to_usagers (question_id, usager_id, type_usager) 
                            VALUES (?, ?, 'demandeur')";
                $db->query($sql, array($id, $usager_id));

                if ($usager_id <> getIdUsager()) {
                    $sql = "insert into notifications_qrt_usagers (notification_qrt_id, usager_id, type_notification) values (?, ?, 'demandeur_to_demandeur')";
                    $db->query($sql, array($last_notif_id, $usager_id));
                }
            }
        }

        if (isset($requestData['repondant_usager_id']) && count($requestData['repondant_usager_id']) > 0) {
            foreach ($requestData['repondant_usager_id'] as $ordre => $usager_id) {
                $sql = "INSERT INTO questions_to_usagers (question_id, usager_id, type_usager) 
                            VALUES (?, ?, 'repondant')";
                $db->query($sql, array($id, $usager_id));

                if ($usager_id <> getIdUsager()) {
                    $sql = "insert into notifications_qrt_usagers (notification_qrt_id, usager_id, type_notification) values (?, ?, 'demandeur_to_repondant')";
                    $db->query($sql, array($last_notif_id, $usager_id));
                }
            }
        }

        $sql = "DELETE FROM questions_fiches WHERE question_id = ?";
        $db->query($sql, array($id));

        if (isset($requestData['listeFiches']) && count($requestData['listeFiches']) > 0) {
            foreach ($requestData['listeFiches'] as $ordre => $fiche_id) {
                $sql = "INSERT INTO questions_fiches (question_id, fiche_id) 
                            VALUES (?, ?)";
                $db->query($sql, array($id, $fiche_id));
            }
        }

        if (isset($requestData['is_prioritaire']) && $is_ajout) {
            Questions::sendNotificationEmails($id, 'question', 'immediat');
        }

        if ($is_ajout) {
            $sql = "update questions set code = ? where id = ?";
            $db->query($sql, array('QRT-' . str_pad($id, 7, '0', STR_PAD_LEFT), $id));
        }
    }

    public function traitementApresSubmit($requestData)
    {
        global $db;
        if (isset($requestData['demandeur_groupe_usager_id']) && count($requestData['demandeur_groupe_usager_id']) > 0) {
            $ordre = 0;
            $this->groupes_usagers_demandeurs = array();
            foreach ($requestData['demandeur_groupe_usager_id'] as $ordre => $element_id) {
                $sql = "select nom from questions_groupes_usagers where id = ?";
                $row = $db->getRow($sql, array($element_id));

                $this->groupes_usagers_demandeurs[$ordre] = array(
                    'id' => $element_id,
                    'nom' => $row['nom']
                );
            }
        }
        if (isset($requestData['repondant_groupe_usager_id']) && count($requestData['repondant_groupe_usager_id']) > 0) {
            $ordre = 0;
            $this->groupes_usagers_repondants = array();
            foreach ($requestData['repondant_groupe_usager_id'] as $ordre => $element_id) {
                $sql = "select nom from questions_groupes_usagers where id = ?";
                $row = $db->getRow($sql, array($element_id));

                $this->groupes_usagers_repondants[$ordre] = array(
                    'id' => $element_id,
                    'nom' => $row['nom']
                );
            }
        }
        if (isset($requestData['demandeur_usager_id']) && count($requestData['demandeur_usager_id']) > 0) {
            $ordre = 0;
            $this->usagers_demandeurs = array();
            foreach ($requestData['demandeur_usager_id'] as $ordre => $element_id) {
                $sql = "select concat(u.nom, ', ', u.prenom) as nom from usagers u where id = ?";
                $row = $db->getRow($sql, array($element_id));

                $this->usagers_demandeurs[$ordre] = array(
                    'id' => $element_id,
                    'nom' => $row['nom']
                );
            }
        }
        if (isset($requestData['repondant_usager_id']) && count($requestData['repondant_usager_id']) > 0) {
            $ordre = 0;
            $this->usagers_repondants = array();
            foreach ($requestData['repondant_usager_id'] as $ordre => $element_id) {
                $sql = "select concat(u.nom, ', ', u.prenom) as nom from usagers u where id = ?";
                $row = $db->getRow($sql, array($element_id));

                $this->usagers_repondants[$ordre] = array(
                    'id' => $element_id,
                    'nom' => $row['nom']
                );
            }
        }

        $this->fiches = array();
        if (isset($requestData['listeFiches']) && is_array($requestData['listeFiches'])) {
            if (count($requestData['listeFiches']) > 0) {
                $sql = "select id as cle, id, code from fiches where id in (" . db::placeHolders($requestData['listeFiches']) . ")";
                $fiches = $db->getAssoc($sql, $requestData['listeFiches']);
                foreach ($requestData['listeFiches'] as $fiche_id) {
                    $this->fiches[$fiche_id] = $fiches[$fiche_id];
                }
            }
        }
    }
}
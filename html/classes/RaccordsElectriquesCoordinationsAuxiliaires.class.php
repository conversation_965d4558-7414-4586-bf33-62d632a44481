<?php

class RaccordsElectriquesCoordinationsAuxiliaires extends FicheFormWrapper {
	private static $CLASS_NAME='RaccordsElectriquesCoordinationsAuxiliaires';
	public static $AUTO=true;
	const SQL_TABLE_NAME='raccords_electriques_coordinations_auxiliaires';
	const TITRE='Raccord d\'alimentation électrique (alimentation auxiliaire)';
    public static $TYPE_FORM='UNIQUE';
    
	public $id;
	public $fiche_id;
	public $projet_id;
    public $cable_type_id;
    public $cable_type;
	public $equipement_description_exigence;
	public $equipement_description_solution;
	public $type_raccord_exigence_id;
	public $tension_type_exigence_id;
	public $type_raccord_exigence;
	public $tension_type_exigence;
	public $puissance_exigence;
	public $puissance_exigence_unite_id;
	public $installation_type_exigence_id;
	public $quantite_exigence_id;
	public $puissance_exigence_unite;
	public $installation_type_exigence;
	public $quantite_exigence;
	public $commentaires_exigence;
	public $statut_exigence;
	public $type_raccord_solution_id;
	public $tension_type_solution_id;
	public $type_raccord_solution;
	public $tension_type_solution;
	public $puissance_solution;
	public $puissance_solution_unite_id;
	public $puissance_solution_unite;
    public $disjoncteur_calibre_id;
    public $disjoncteur_calibre;
	public $installation_type_solution_id;
	public $installation_type_solution;
	public $quantite_solution_id;
	public $commentaires_solution;
	public $statut_solution;
	public $source_alimentation_id;
	public $source_alimentation;
	public $numero_casier;
	public $numero_disjoncteur;
	public $type_source;
	public $ajustement_relais;
	public $fusible_nombre;
	public $fusible_capacite;
	public $fusible_action_type_id;
	public $fusible_format_type_id;
	public $fusible_action_type;
	public $fusible_format_type;
	public $type_protection;
    public $disjoncteur_ajustement_amperage_id;
    public $disjoncteur_ajustement_amperage;
    public $disjoncteur_capacite_rupture_id;
    public $disjoncteur_capacite_rupture;
	public $ordre;
	public $statut;
    public $alimentation_electrique_type_id;
    public $usager_exigence_id;
    public $alimentation_electrique_type;
    public $usager_exigence;
    public $ts_exigence;
    public $usager_solution_id;
    public $usager_solution;
    public $ts_solution;
    public $nomenclature;
	public $calibre_fil_alimentation_id;
	public $nombre_phase_type_id;
	public $calibre_fil_alimentation;
	public $nombre_phase_type;
	public $cable_longueur;
	public $quantite_artere_paralele;
	public $conduit_partage;
	public $calibre_fil_malt_id;
	public $conduit_malt_type_id;
	public $calibre_fil_malm_id;
	public $conduit_type_id;
	public $calibre_fil_malt;
	public $conduit_malt_type;
	public $calibre_fil_malm;
	public $conduit_type;
	public $demarreur_type_id;
	public $demarreur_type;
	public $demarreur_composition_type_id;
	public $demarreur_composition_type;
	public $type_protection_text;
    public $disjoncteur_modele_id;
    public $disjoncteur_modele;
	public $champ_combine_1;
	public $champ_combine_2;
	public $champ_combine_3;
	public $champ_combine_4;
	public $champ_combine_5;

	public static function getChampsSaisieMetaData()
    {
        global $types_sources, $types_protections;
        $return_array = array(

            'regroupements' => array(
                
                2 => array(
                    
                    'titre' => txt('Propriétés de la source d\'alimentation', false),
                    'data'  =>  array(
                        
                        'source_alimentation' => array(
                            'classe'      	=> 'Fiches',
                            'table'      	=> 'fiches',
                            'champ_id'      => 'source_alimentation_id',
                            'type'          => 'select',
                            'format'        => 'int',
                            'titre'         => txt('Source d\'alimentation', false),
                            'css_editeur'   => 'xxlarge',
                            'sql_for_nom'   => 'code'
                        ),
                        
                        'demarreur_type' => array(
                            'classe'      	=> 'DemarreursTypes',
                            'table'      	=> 'demarreurs_types',
                            'champ_id'      => 'demarreur_type_id',
                            'type'          => 'select',
                            'format'        => 'int',
                            'titre'         => txt('Type d\'équipement', false),
                            'css_editeur'   => 'xxlarge'
                        ),
                        
                        'demarreur_composition_type' => array(
                            'classe'      	=> 'DemarreursCompositionsTypes',
                            'table'      	=> 'demarreurs_compositions_types',
                            'champ_id'      => 'demarreur_composition_type_id',
                            'type'          => 'select',
                            'format'        => 'int',
                            'titre'         => txt('Composition de l\'équipement', false),
                            'css_editeur'   => 'xxlarge'
                        ),

                        'numero_casier' => array(
                            'classe'        => '',
                            'table'      	=> '',
                            'champ_id'      => 'numero_casier',
                            'type'          => 'input',
                            'format'        => 'text',
                            'titre'         => txt('Numéro de casier', false),
                            'css_editeur'   => 'xxlarge'
                        ),

                        'numero_disjoncteur' => array(
                            'classe'        => '',
                            'table'      	=> '',
                            'champ_id'      => 'numero_disjoncteur',
                            'type'          => 'input',
                            'format'        => 'text',
                            'titre'         => txt('Numéro de circuit(s)', false),
                            'css_editeur'   => 'xxlarge'
                        ),

                    )
                ),
                3 => array(
                    
                    'titre' => txt('Propriétés des protections électriques', false),
                    'data'  =>  array(

                        'type_protection_text' => array(
                            'classe'        => '',
                            'table'      	=> '',
                            'champ_id'      => 'type_protection',
                            'type'          => 'radio',
                            'format'        => 'int',
                            'titre'         => txt('Type de protection', false),
                            'css_editeur'   => 'xxlarge',
                            'liste'         => $types_protections
                        ),

                        'fusible_capacite' => array(
                            'classe'        => '',
                            'table'      	=> '',
                            'champ_id'      => 'fusible_capacite',
                            'type'          => 'input',
                            'format'        => 'text',
                            'titre'         => txt('Calibre du/des fusible(s)', false),
                            'css_editeur'   => 'xxlarge',
                            'unite'         => 'A'
                        ),

                        'disjoncteur_calibre' => array(
                            'classe'        => 'CalibresProtectionsTypes',
                            'table'      	=> 'calibres_protections_types',
                            'champ_id'      => 'disjoncteur_calibre_id',
                            'type'          => 'select',
                            'format'        => 'int',
                            'titre'         => txt('Calibre du disjoncteur', false),
                            'css_editeur'   => 'xxlarge',
                        ),

                        'disjoncteur_ajustement_amperage' => array(
                            'classe'        => 'AmperagesTypes',
                            'table'      	=> 'amperages_types',
                            'champ_id'      => 'disjoncteur_ajustement_amperage_id',
                            'type'          => 'select',
                            'format'        => 'int',
                            'titre'         => txt('Ajustement ampérage (disj.)', false),
                            'css_editeur'   => 'xxlarge',
                        ),

                        'disjoncteur_capacite_rupture' => array(
                            'classe'        => 'CapacitesRupturesTypes',
                            'table'      	=> 'capacites_ruptures_types',
                            'champ_id'      => 'disjoncteur_capacite_rupture_id',
                            'type'          => 'select',
                            'format'        => 'int',
                            'titre'         => txt('Capacité de rupture (disj.)', false),
                            'css_editeur'   => 'xxlarge',
                        ),
                        
                        'disjoncteur_modele' => array(
                            'classe'      	=> 'DisjoncteursModeles',
                            'table'      	=> 'disjoncteurs_modeles',
                            'champ_id'      => 'disjoncteur_modele_id',
                            'type'          => 'select',
                            'format'        => 'int',
                            'titre'         => txt('Modèle de disjoncteur', false),
                            'css_editeur'   => 'xxlarge'
                        ),
                        
                        'fusible_action_type' => array(
                            'classe'        => 'FusiblesActionsTypes',
                            'table'      	=> 'fusibles_actions_types',
                            'champ_id'      => 'fusible_action_type_id',
                            'type'          => 'select',
                            'format'        => 'int',
                            'titre'         => txt('Action du/des fusibles', false),
                            'css_editeur'   => 'large'
                        ),
                        
                        'fusible_format_type' => array(
                            'classe'      	=> 'FusiblesFormatsTypes',
                            'table'      	=> 'fusibles_formats_types',
                            'champ_id'      => 'fusible_format_type_id',
                            'type'          => 'select',
                            'format'        => 'int',
                            'titre'         => txt('Format du/des fusibles', false),
                            'css_editeur'   => 'xxlarge'
                        ),

                        'ajustement_relais' => array(
                            'classe'        => '',
                            'table'      	=> '',
                            'champ_id'      => 'ajustement_relais',
                            'type'          => 'input',
                            'format'        => 'text',
                            'titre'         => txt('Ajustement (relais O/L)', false),
                            'css_editeur'   => 'xxlarge',
                            'unite'         => 'A'
                        )

                    )
                ),
        
                4 => array(
                    
                    'titre' => txt('Propriétés du câblage et des conduits électriques', false),
                    'data'  =>  array(
                        
                        'cable_type' => array(
                            'classe'        => 'CablesTypes',
                            'table'      	=> 'cables_types',
                            'champ_id'      => 'cable_type_id',
                            'type'          => 'select',
                            'format'        => 'int',
                            'titre'         => txt('Type de câblage', false),
                            'css_editeur'   => 'large'
                        ),
                        
                        'calibre_fil_alimentation' => array(
                            'classe'        => 'CablesCalibresTypes',
                            'table'      	=> 'cables_calibres_types',
                            'champ_id'      => 'calibre_fil_alimentation_id',
                            'type'          => 'select',
                            'format'        => 'int',
                            'titre'         => txt('Calibre du filage (Alim.)', false),
                            'css_editeur'   => 'large'
                        ),
                        
                        'calibre_fil_malm' => array(
                            'classe'      	=> 'CablesCalibresTypes',
                            'table'      	=> 'cables_calibres_types',
                            'champ_id'      => 'calibre_fil_malm_id',
                            'type'          => 'select',
                            'format'        => 'int',
                            'titre'         => txt('Calibre du filage (MALM)', false),
                            'css_editeur'   => 'xxlarge',
                            'dependance_id' => 'nombre_phases_id',
                            'dependance_champ_liste' => 'nombre_phases_id',
                            'dependance_force_val' => 'null'
                        ),
                        
                        'conduit_type' => array(
                            'classe'        => 'ConduitsTypes',
                            'table'      	=> 'conduits_types',
                            'champ_id'      => 'conduit_type_id',
                            'type'          => 'select',
                            'format'        => 'int',
                            'titre'         => txt('Type/calibre du conduit', false),
                            'css_editeur'   => 'large'
                        ),

                        'cable_longueur' => array(
                            'classe'        => '',
                            'table'      	=> '',
                            'champ_id'      => 'cable_longueur',
                            'type'          => 'input',
                            'format'        => 'float',
                            'titre'         => txt('Longueur du câblage', false),
                            'css_editeur'   => 'xxlarge',
                            'unite'         => 'm'
                        ),
                        
                        'quantite_artere_paralele' => array(
                            'classe'        => '',
                            'table'      	=> '',
                            'champ_id'      => 'quantite_artere_paralele',
                            'type'          => 'input',
                            'format'        => 'int',
                            'titre'         => txt('Nb. d\'artère(s) parallèle(s)', false),
                            'css_editeur'   => 'large',
                            'default_value' => 1
                        ),
            
                        'conduit_partage' => array(
                            'classe'        => '',
                            'table'      	=> '',
                            'champ_id'      => 'conduit_partage',
                            'type'          => 'checkbox',
                            'format'        => 'int',
                            'titre'         => txt('Conduit partagé', false),
                            'css_editeur'   => 'xxlarge'
                        ),
                        
                        'calibre_fil_malt' => array(
                            'classe'      	=> 'CablesCalibresTypes',
                            'table'      	=> 'cables_calibres_types',
                            'champ_id'      => 'calibre_fil_malt_id',
                            'type'          => 'select',
                            'format'        => 'int',
                            'titre'         => txt('Calibre du filage (MALT)', false),
                            'css_editeur'   => 'xxlarge',
                            'dependance_id' => 'nombre_phases_id',
                            'dependance_champ_liste' => 'nombre_phases_id',
                            'dependance_force_val' => 'null'
                        ),
                        
                        'conduit_malt_type' => array(
                            'classe'        => 'ConduitsTypes',
                            'table'      	=> 'conduits_types',
                            'champ_id'      => 'conduit_malt_type_id',
                            'type'          => 'select',
                            'format'        => 'int',
                            'titre'         => txt('Type/calibre du conduit (MALT)', false),
                            'css_editeur'   => 'large'
                        ),

                    )
                ),       
        
                5   => array(
                    'titre' => txt('Commentaires', false),
                    'data'  =>  array(
                        'commentaires_solution' => array(
                            'table'      	=> '',
                            'champ_id'      => 'commentaires_solution',
                            'type'          => 'textarea',
                            'format'        => 'text',
                            'titre'         => txt('Commentaires', false),
                            'css_editeur'   => 'xxlarge'
                        ),

                    )
                )
            )
        );
		return parent::parseMetaData($return_array);
	}

	public static function getListe($filter = array(), $orderBy = 'id', $extra_data = null)
	{
		return parent::getListe($filter, $orderBy, $extra_data);
	}
    
    public function getLabelBesoin()
    {
        if($this->statut_exigence == 1)
        {
            return txt('Besoin').'<span style="color: green;"> ('.txt('conforme').')</span>';
        }
        else if($this->statut_exigence == -1)
        {
            return txt('Besoin').'<span style="color: red;"> ('.txt('à valider').')</span>';
        }
        else
        {
            return txt('Besoin');
        }
    }
    
    public static function getFormHTML($fiche_id, $liste_data = null, $forced_liste = null)
	{
        global $types_sources, $types_protections;
        
        $currentClassName = get_called_class();
        $metas = $currentClassName::getFlatChampsSaisieMetaData();
        $champs_readonly = $currentClassName::getChampsReadOnly();
        
        if(isset($liste_data))
        {
            $objs = self::dataToListe($liste_data);
            if(isset($objs[0]))
            {
                $obj = $objs[0];
            }
            else
            {
                $obj = new self(0);
            }
        }
        else
        {
            $obj = self::getOne($fiche_id);
        }
        
        $is_motif = ($obj->id > 0);
?>			
        <style>
            .edit_popupContent section > div {
                float: left;
                margin: 5px 0;
                padding: 0;
                width: 706px !important;
            }
            
            #validation_besoin {
                color: blue;
                font-size: 16px;
                width: 900px;
            }
            #validation_besoin input {
                position: relative;
                top: 0px!important;
                margin-left: 0px;
            }
            #validation_besoin label {
                width: 45px !important;
                padding: 0;
                display: inline-block;
                background: none;
                font-weight: 400;
                font-size: 16px;
                text-align: left;
            }
            h2.blocsFicheTitre {
                margin: 5px 20px;
            }
            .blocsFiche {
                margin: 25px 20px 25px 15px;
            }
            .blocsFiche label {
                text-align: left!important;
                margin-left: 0px!important;
                background: none!important;
                font-weight: normal!important;
                min-width: 215px!important;
            }
            h2.blocsFicheTitre {
                font-size: 16px!important;
                line-height: 22px!important;
                font-weight: 100!important;
                color: #777777!important;
                text-shadow: 0 1px 0 #ffffff!important;
                margin: 5px 0px!important;
                text-decoration: underline!important;
                border-bottom: 0px solid #aaa!important;
            }
            
            h2.underlined{
                border-bottom: 1px solid #aaa!important;
            }
            
            .blocsFiche section > div {
                width: 606px !important;
            }
        </style>
        <div style="margin-left: 40px;">
<?
            $nombre_phase_type_id_for_preremplir = 0;
            $fiche = new Fiches($fiche_id);
            $prop_elec = ProprietesElectriquesAuxiliaires::getOne($fiche_id);
            if($prop_elec->id > 0)
            {
                ProprietesElectriquesAuxiliaires::getFicheHTML($fiche);
                $nombre_phase_type_id_for_preremplir = isset($prop_elec->nombre_phase_type_id) ? $prop_elec->nombre_phase_type_id : 0; 
            }
            else
            {
                $prop_elec = EquipementsDistributionsElectriques::getOne($fiche_id);
                if($prop_elec->id > 0)
                {
                    EquipementsDistributionsElectriques::getFicheHTML($fiche);
                    $nombre_phase_type_id_for_preremplir = isset($prop_elec->nombre_phase_type_id) ? $prop_elec->nombre_phase_type_id : 0; 
                }
                else
                {
                    $prop_elec = EquipementsTransformationsElectriques::getOne($fiche_id);
                    if($prop_elec->id > 0)
                    {
                        EquipementsTransformationsElectriques::getFicheHTML($fiche);
                        if(isset($prop_elec->kva_type_id) && $prop_elec->kva_type_id > 0)
                        {
                            $kva = new KvaTypes($prop_elec->kva_type_id);
                            $nombre_phase_type_id_for_preremplir = $kva->nombre_phase_fil_primaire_id;
                        }
                    }
                }
            }
?>
        <input type="hidden" id="statut_exigence" name="statut_exigence[]" value="<?=$obj->statut_exigence?>" />
        <input type="hidden" id="statut_solution" name="statut_solution[]" value="<?=$obj->statut_solution?>" />
        <input type="hidden" id="usager_solution_id" name="usager_solution_id[]" value="<?=($obj->usager_solution_id == 0 ? '' : $obj->usager_solution_id)?>" />
        <input type="hidden" id="ts_solution" name="ts_solution[]" value="<?=$obj->ts_solution?>" />
        <input type="hidden" id="fiche_sous_type_id" value="<?=$fiche->fiche_sous_type_id?>" />
        <input type="hidden" id="kva_type_id_for_recommendation" value="<?=(isset($prop_elec->kva_type_id) ? $prop_elec->kva_type_id : 0)?>" />
        <input type="hidden" id="hp_type_id_for_recommendation" value="<?=(isset($prop_elec->total_hp_id) && isset($prop_elec->charge_type_id) && $prop_elec->charge_type_id == 1 && isset($prop_elec->quantite_id) && $prop_elec->quantite_id == 1 ? $prop_elec->total_hp_id : 0)?>" />
        <input type="hidden" id="nombre_phase_type_id_for_preremplir" value="<?=$nombre_phase_type_id_for_preremplir?>" />
        <br style="clear: both;">
        <br style="clear: both;">
        <br style="clear: both;">
        <div id="validation_besoin">
            <?=txt('Les propriétés électriques inscrites ci-dessus permettent-elles de définir le raccord électrique requis?')?>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
            <input type="radio" id="validation_oui" value="1" class="validation_oui_non" name="validation_besoin"<?=($obj->statut_exigence == 1 ? ' checked="checked"' : '')?> /><label for="validation_oui">&nbsp;Oui</label>
            <input type="radio" id="validation_non" value="-1" class="validation_oui_non" name="validation_besoin"<?=($obj->statut_exigence == -1 ? ' checked="checked"' : '')?> /><label for="validation_non">&nbsp;Non</label>
	        <?
	        if($prop_elec->fiche_id > 0 && havePermission(array('repondant_qrt_bcf', 'demandeur_qrt_bcf')))
	        {
		        print '<span class="messagerie" style="position: relative; top: 7px;" data-discipline_id="1">';
		        $qrts = Questions::getListe(array('fiche_id' => $prop_elec->fiche_id, 'classe_formulaire' => $currentClassName, 'discipline_id' => 1));
		        if(count($qrts) > 0)
		        {
			        $qrt = array_values($qrts)[0];
			        ?>
                    <a href="javascript: void(0);" class="start_qrt" data-qrt_id="<?=$qrt->id?>">
                        <img src="css/images/icons/dark/speech_bubble.png" /><?=($qrt->nb_notifications_all > 0 ? '<span class="extra">'.$qrt->nb_notifications_all.'</span>' : '')?></a>
			        <?
		        }
		        else
		        {
			        ?>
                    <a href="index.php?section=admin&module=edit&table=questions&type=client&id=0&fiche_id=<?=$prop_elec->fiche_id?>&nom_table=&liste_item_id=&classe_formulaire=<?=$currentClassName?>&discipline_id=1" class="fancy" data-small_fancy="true" style="opacity: 0.5;">
                        <img src="css/images/icons/dark/speech_bubble.png" /></a>
			        <?
		        }
		        print '</span>';
	        }
	        ?>
        </div>

        <h2 class="underlined"></h2>
                
        <fieldset class="noMinHeight">
            
            <h5 style="text-decoration: underline; margin: 15px 0;"><?=txt('Propriétés de la source d\'alimentation')?></h5>
                        
            <section>
                <label><?=txt('Source d\'alimentation')?></label>
                <div>
<?
                    $key = 'source_alimentation';
                    $meta = $metas[$key];
                    $meta_champ_id = $meta['champ_id'];
                    if(isset($champs_readonly[$key]))
                    {
                        print self::getReadonlyToDisplay($meta, $key, $obj);      
?>
                        <input type="hidden" id="<?=$meta['champ_id']?>" name="<?=$meta['champ_id']?>[]" value="<?=($obj->$meta_champ_id)?>" />
<?
                    }
                    else
                    {
?>
                        <select id="source_alimentation_id" name="source_alimentation_id[]" class="to_validate_for_statut_solution lazy_loaded_fiches" disabled data-textblank=" ">
                            <option value=""> </option>
                            <?=Fiches::getOptions(array('ids' => array($obj->source_alimentation_id), 'fiche_sous_type_id' => array(2000003, 2000006), 'excluded_fiche_id' => $fiche_id, 'inclure_supprimees' => 1, 'no_filtre_global' => 1), $obj->source_alimentation_id)?>
                        </select>
<?
                    }
?>
                </div>
            </section>

            <section>
                <label><?=txt('Type d\'équipement')?></label>
                <div>
                    
<?
                    $key = 'demarreur_type';
                    $meta = $metas[$key];
                    $meta_champ_id = $meta['champ_id'];
                    if(isset($champs_readonly[$key]))
                    {
                        print self::getReadonlyToDisplay($meta, $key, $obj);      
?>
                        <input type="hidden" id="<?=$meta['champ_id']?>" name="<?=$meta['champ_id']?>[]" value="<?=($obj->$meta_champ_id)?>" />
<?
                    }
                    else
                    {
?>
                        <select id="demarreur_type_id" name="demarreur_type_id[]">
                            <option value=""> </option>
                            <?=DemarreursTypes::getOptions(array(), $obj->demarreur_type_id)?>
                        </select>
<?
                    }
?>
                </div>
            </section>
                        
            <section>
                <label><?=txt('Composition de l\'équipement')?></label>
                <div>
                    
<?
                    $key = 'demarreur_composition_type';
                    $meta = $metas[$key];
                    $meta_champ_id = $meta['champ_id'];
                    if(isset($champs_readonly[$key]))
                    {
                        print self::getReadonlyToDisplay($meta, $key, $obj);      
?>
                        <input type="hidden" id="<?=$meta['champ_id']?>" name="<?=$meta['champ_id']?>[]" value="<?=($obj->$meta_champ_id)?>" />
<?
                    }
                    else
                    {
?>
                        <select id="demarreur_composition_type_id" name="demarreur_composition_type_id[]">
                            <option value=""> </option>
                            <?=DemarreursCompositionsTypes::getOptions(array(), $obj->demarreur_composition_type_id)?>
                        </select>
<?
                    }
?>
                </div>
            </section>

            <section>
                <label><?=txt('Numéro de casier')?></label>
                <div>
        
<?
                    $key = 'numero_casier';
                    $meta = $metas[$key];
                    $meta_champ_id = $meta['champ_id'];
                    if(isset($champs_readonly[$key]))
                    {
                        print self::getReadonlyToDisplay($meta, $key, $obj);      
?>
                        <input type="hidden" id="<?=$meta['champ_id']?>" name="<?=$meta['champ_id']?>[]" value="<?=($obj->$meta_champ_id)?>" />
<?
                    }
                    else
                    {
?>
                        <input id="numero_casier" type="text" name="numero_casier[]" value="<?=($obj->numero_casier)?>" style="width: 15%;" /> Ex. D3, F4, etc.
<?
                    }
?>
                </div>
            </section>

            <section>
                <label><?=txt('Numéro de circuit(s)')?></label>
                <div>
        
<?
                    $key = 'numero_disjoncteur';
                    $meta = $metas[$key];
                    $meta_champ_id = $meta['champ_id'];
                    if(isset($champs_readonly[$key]))
                    {
                        print self::getReadonlyToDisplay($meta, $key, $obj);      
?>
                        <input type="hidden" id="<?=$meta['champ_id']?>" name="<?=$meta['champ_id']?>[]" value="<?=($obj->$meta_champ_id)?>" />
<?
                    }
                    else
                    {
?>
                        <input id="numero_disjoncteur" type="text" name="numero_disjoncteur[]" value="<?=($obj->numero_disjoncteur)?>" style="width: 15%;" /> Ex. 24, 8-10, 1-3-5, etc.
<?
                    }
?>
                </div>
            </section>
            
        </fieldset>

        <fieldset class="noMinHeight">
            <h5 style="text-decoration: underline; margin: 15px 0;"><?=txt('Propriétés des protections électriques')?></h5>
            <section>
                <label class="label"><?=txt('Type de protection')?></label>
                <div>
                    
<?
                    $key = 'type_protection_text';
                    $meta = $metas[$key];
                    $meta_champ_id = $meta['champ_id'];
                    if(isset($champs_readonly[$key]))
                    {
                        print self::getReadonlyToDisplay($meta, $key, $obj);      
?>
                        <input type="hidden" id="<?=$meta['champ_id']?>" name="<?=$meta['champ_id']?>[]" value="<?=($obj->$meta_champ_id)?>" />
<?
                    }
                    else
                    {
?>
                        <br />
<?
                        $obj->type_protection = dm_strlen($obj->type_protection) > 0 ? $obj->type_protection : 1;
                        foreach($types_protections as $i => $row)
                        {
                            $chk = $row['id'] == $obj->type_protection ? ' checked="checked"' : '';
?>
                            <div>
                                <input type="radio" name="type_protection[]" id="type_protection_<?=$row['id']?>"<?=$chk?> class="type_protection" value="<?=$row['id']?>" /><label for="type_protection_<?=$row['id']?>" style="width: 360px!important; position: relative; top: 3px;"><?=$row['nom']?></label>
                            </div>
<?
                        }
                    }
?>
                </div>
            </section>
                        
            <section>
                <label><?=txt('Calibre du disjoncteur')?></label>
                <div>

<?
                    $key = 'disjoncteur_calibre';
                    $meta = $metas[$key];
                    $meta_champ_id = $meta['champ_id'];
                    if(isset($champs_readonly[$key]))
                    {
                        print self::getReadonlyToDisplay($meta, $key, $obj);      
?>
                        <input type="hidden" id="<?=$meta['champ_id']?>" name="<?=$meta['champ_id']?>[]" value="<?=($obj->$meta_champ_id)?>" />
<?
                    }
                    else
                    {
                        $disabled = in_array($obj->type_protection, array(3)) ? '' : ' disabled="disabled"';
?>
                        <select id="disjoncteur_calibre_id" name="disjoncteur_calibre_id[]"<?=$disabled?>>
                            <option value=""> </option>
                            <?=CalibresProtectionsTypes::getOptions(array(), $obj->disjoncteur_calibre_id)?>
                        </select>
<?
                    }
?>
                </div>
            </section>
                        
            <section>
                <label><?=txt('Ajustement ampérage (disj.)')?></label>
                <div>

<?
                    $key = 'disjoncteur_ajustement_amperage';
                    $meta = $metas[$key];
                    $meta_champ_id = $meta['champ_id'];
                    if(isset($champs_readonly[$key]))
                    {
                        print self::getReadonlyToDisplay($meta, $key, $obj);      
?>
                        <input type="hidden" id="<?=$meta['champ_id']?>" name="<?=$meta['champ_id']?>[]" value="<?=($obj->$meta_champ_id)?>" />
<?
                    }
                    else
                    {
                        $disabled = in_array($obj->type_protection, array(3)) ? '' : ' disabled="disabled"';
?>
                        <select id="disjoncteur_ajustement_amperage_id" name="disjoncteur_ajustement_amperage_id[]"<?=$disabled?>>
                            <option value=""> </option>
                            <?=AmperagesTypes::getOptions(array(), $obj->disjoncteur_ajustement_amperage_id)?>
                        </select>
<?
                    }
?>
                </div>
            </section>
                        
            <section>
                <label><?=txt('Capacité de rupture (disj.)')?></label>
                <div>

<?
                    $key = 'disjoncteur_capacite_rupture';
                    $meta = $metas[$key];
                    $meta_champ_id = $meta['champ_id'];
                    if(isset($champs_readonly[$key]))
                    {
                        print self::getReadonlyToDisplay($meta, $key, $obj);      
?>
                        <input type="hidden" id="<?=$meta['champ_id']?>" name="<?=$meta['champ_id']?>[]" value="<?=($obj->$meta_champ_id)?>" />
<?
                    }
                    else
                    {
                        $disabled = in_array($obj->type_protection, array(3)) ? '' : ' disabled="disabled"';
?>
                        <select id="disjoncteur_capacite_rupture_id" name="disjoncteur_capacite_rupture_id[]"<?=$disabled?>>
                            <option value=""> </option>
                            <?=CapacitesRupturesTypes::getOptions(array(), $obj->disjoncteur_capacite_rupture_id)?>
                        </select>
<?
                    }
?>
                </div>
            </section>
                        
            <section>
                <label><?=txt('Modèle de disjoncteur')?></label>
                <div>

<?
                    $key = 'disjoncteur_modele';
                    $meta = $metas[$key];
                    $meta_champ_id = $meta['champ_id'];
                    if(isset($champs_readonly[$key]))
                    {
                        print self::getReadonlyToDisplay($meta, $key, $obj);      
?>
                        <input type="hidden" id="<?=$meta['champ_id']?>" name="<?=$meta['champ_id']?>[]" value="<?=($obj->$meta_champ_id)?>" />
<?
                    }
                    else
                    {
                        $disabled = in_array($obj->type_protection, array(3)) ? '' : ' disabled="disabled"';
?>
                        <select id="disjoncteur_modele_id" name="disjoncteur_modele_id[]"<?=$disabled?>>
                            <option value=""> </option>
                            <?=DisjoncteursModeles::getOptions(array(), $obj->disjoncteur_modele_id)?>
                        </select>
<?
                    }
?>
                </div>
            </section>

            <section>
                <label class="label"><?=txt('Calibre du/des fusible(s)')?></label>
                <div>

<?
                    $key = 'fusible_capacite';
                    $meta = $metas[$key];
                    $meta_champ_id = $meta['champ_id'];
                    if(isset($champs_readonly[$key]))
                    {
                        print self::getReadonlyToDisplay($meta, $key, $obj);      
?>
                        <input type="hidden" id="<?=$meta['champ_id']?>" name="<?=$meta['champ_id']?>[]" value="<?=($obj->$meta_champ_id)?>" />
<?
                    }
                    else
                    {
                        $disabled = in_array($obj->type_protection, array(2,3)) ? '' : ' disabled="disabled"';
?>
                        <input id="fusible_capacite" type="text" name="fusible_capacite[]" value="<?=($obj->fusible_capacite == 0 ? '' : ($obj->fusible_capacite))?>" class="justFloat" style="width: 15%; text-align: right"<?=$disabled?> /> <?=txt('A')?>
<?
                    }
?>
                </div>
            </section>

            <section>
                <label class="label"><?=txt('Action du fusible')?></label>
                <div>

<?
                    $key = 'fusible_action_type';
                    $meta = $metas[$key];
                    $meta_champ_id = $meta['champ_id'];
                    if(isset($champs_readonly[$key]))
                    {
                        print self::getReadonlyToDisplay($meta, $key, $obj);      
?>
                        <input type="hidden" id="<?=$meta['champ_id']?>" name="<?=$meta['champ_id']?>[]" value="<?=($obj->$meta_champ_id)?>" />
<?
                    }
                    else
                    {
                        $disabled = in_array($obj->type_protection, array(2)) ? '' : ' disabled="disabled"';
?>
                        <select id="fusible_action_type_id" name="fusible_action_type_id[]"<?=$disabled?>>
                            <option value=""> </option>
                            <?=  FusiblesActionsTypes::getOptions(array(), $obj->fusible_action_type_id)?>
                        </select>
<?
                    }
?>
                </div>
            </section>

            <section>
                <label class="label"><?=txt('Format du/des fusibles')?></label>
                <div>

<?
                    $key = 'fusible_format_type';
                    $meta = $metas[$key];
                    $meta_champ_id = $meta['champ_id'];
                    if(isset($champs_readonly[$key]))
                    {
                        print self::getReadonlyToDisplay($meta, $key, $obj);      
?>
                        <input type="hidden" id="<?=$meta['champ_id']?>" name="<?=$meta['champ_id']?>[]" value="<?=($obj->$meta_champ_id)?>" />
<?
                    }
                    else
                    {
                        $disabled = in_array($obj->type_protection, array(2, 3)) ? '' : ' disabled="disabled"';
?>
                        <select id="fusible_format_type_id" name="fusible_format_type_id[]"<?=$disabled?>>
                            <option value=""> </option>
                            <?=  FusiblesFormatsTypes::getOptions(array(), $obj->fusible_format_type_id)?>
                        </select>
<?
                    }
?>
                </div>
            </section>

            <section>
                <label class="label"><?=txt('Ajustement (relais O/L)')?></label>
                <div>

<?
                    $key = 'ajustement_relais';
                    $meta = $metas[$key];
                    $meta_champ_id = $meta['champ_id'];
                    if(isset($champs_readonly[$key]))
                    {
                        print self::getReadonlyToDisplay($meta, $key, $obj);      
?>
                        <input type="hidden" id="<?=$meta['champ_id']?>" name="<?=$meta['champ_id']?>[]" value="<?=($obj->$meta_champ_id)?>" />
<?
                    }
                    else
                    {
                        $disabled = in_array($obj->type_protection, array(2,3)) ? '' : ' disabled="disabled"';
?>
                        <input id="ajustement_relais" type="text" name="ajustement_relais[]" value="<?=($obj->ajustement_relais == 0 ? '' : ($obj->ajustement_relais))?>" class="justFloat" style="width: 15%; text-align: right"<?=$disabled?> /> <?=txt('A')?>
<?
                    }
?>
                </div>
            </section>
        </fieldset>
                
        <fieldset class="noMinHeight">
            <h5 style="text-decoration: underline; margin: 15px 0;"><?=txt('Propriétés du câblage et des conduits électriques')?></h5>
<?            
            $obj->getEditorHTML();
?>       
        </fieldset>
<?
        if(isset($metas['champ_combine_1']) || isset($metas['champ_combine_2']) || isset($metas['champ_combine_3']) || isset($metas['champ_combine_4']) || isset($metas['champ_combine_5']))
        {
?>
            <fieldset class="noMinHeight">
                <h5 style="text-decoration: underline; margin: 15px 0;"><?=txt('Champs dénormalisés')?></h5>
<?            
                $obj->getEditorHTML(0, true);
?>       
            </fieldset>
<?
        }
?>       
        <fieldset class="noMinHeight">
            <h5 style="text-decoration: underline; margin: 15px 0;"><?=txt('Commentaires')?></h5>
			<section class="formulaire_general ">
                <div style="width: 100%!important;">
                    <textarea id="commentaires_solution" name="commentaires_solution[]" style="height: 45px;"><?=($obj->commentaires_solution)?></textarea>
                </div>
            </section>
            
        </fieldset>
<?
        
        if($is_motif)
        {
?>
            <fieldset class="noMinHeight">
                <h5 style="text-decoration: underline; margin: 15px 0;">&nbsp;</h5>
            </fieldset>
<?            
        }
?>   
        <fieldset class="noMinHeight">
<?
            if($is_motif)
            {
?>
                <section>
                   <label for="raison">
                        <?=txt('Motif du changement')?>
                    </label>
                    <div>
                        <select id="motif_changement" data-name="raison">
                            <option value=""> </option>
                            <?=MotifsChangements::getOptions(array(), 1)?>
                        </select>
                        <div style="color: blue; margin-top: 10px;"><?=txt('SVP, écrire un motif particulier ou choisir parmi la sélection proposée.')?></div>
                    </div>
                </section>
<?
            }
            else
            {
?>
                <input type="hidden" name="raison" value="Création du formulaire" />
<?
            }
?>
        </fieldset>
        </div>         
<?
	}
    
    public static function getFicheHTML($fiche, $liste = null, $nb_archives = 0, $can_edit = false, $projet_courant_id = null, $gabarit = null)
	{
        global $types_sources;
        
        if(isset($liste) && is_array($liste))
        {
            $liste_tmp = array_values($liste);
            if(isset($liste_tmp[0]))
            {
                $obj = $liste_tmp[0];
            }
            else
            {
                $currentClassName = get_called_class();
                $obj = $currentClassName::getOne($fiche->id);
            }
        }
        else
        {
            $currentClassName = get_called_class();
            $obj = $currentClassName::getOne($fiche->id);
        }
        
        parent::getUniqueFicheHTML($fiche, $obj, $nb_archives, $can_edit, $projet_courant_id);
        
        parent::getAutoFicheHtml($fiche->id, $obj);
    }
    
    public static function getDiffArchiveHTML($fiche_id, $liste, $last_liste, $token_archive, $last_token_archive)
	{
?>
        <style>
            .blocsFiche section label {
                width: 260px !important;
            }
        </style>
<?
        $liste = array_values($liste);
        $last_liste = array_values($last_liste);
        if(isset($liste[0]))
        {
            $obj = $liste[0];
        }
        
        if(isset($last_liste[0]))
        {
            $last_obj = $last_liste[0];
        }
        else
        {
            $last_obj = new self(0);
        }
        
?>
        <h2 class="blocsFicheTitre" style="height: 30px;">
    <?=txt(self::TITRE)?></h2>
<?
        
        parent::getAutoDiffArchiveHtml($fiche_id, $obj, $last_obj, $token_archive, $last_token_archive);
    }
    
    public function getFiltreHTML()
    {
?>
        <fieldset class="noMinHeight">
<?
            $this->getEditorHTML();
?>
        </fieldset>
<?
    }
    
    public static function getSources($fiche_id)
    {
        global $db, $Langue;
        
        $sql = "select ".self::SQL_TABLE_NAME.".*, total_kw, fiches.code as fiche, source_alimentation_id as fiche_id, fiches_types.nom_$Langue as fiche_type, fiches_sous_types.nom_$Langue as fiche_sous_type, "
                . "tensions_types.nom as tension, fiches.ts_delete, 
                case when fiches.fiche_type_id = 2000003 then 
                    coalesce((select nomenclatures.nom_$Langue from generaux_equipements join nomenclatures on nomenclatures.id = nomenclature_id where generaux_equipements.fiche_id = fiches.id), '".txt('À déterminer')."')
                else 
                    coalesce((select nomenclatures.nom_$Langue from generaux_instruments join nomenclatures on nomenclatures.id = nomenclature_id where generaux_instruments.fiche_id = fiches.id), '".txt('À déterminer')."')
                end as nomenclature "
                . "from ".self::SQL_TABLE_NAME." "
                . "join fiches on fiches.id = source_alimentation_id and fiches.ts_archive is null
                left join proprietes_electriques_auxiliaires on proprietes_electriques_auxiliaires.fiche_id = fiches.id
                JOIN fiches_types ON fiches_types.id = fiche_type_id
                JOIN fiches_sous_types ON fiches_sous_types.id = fiche_sous_type_id
                left join unites on unites.id = puissance_exigence_unite_id
                left join tensions_types on tensions_types.id = tension_type_exigence_id "
                . "where ".self::SQL_TABLE_NAME.".fiche_id = ?";
        $liens = $db->getAll($sql, array($fiche_id));
        
        return $liens;
    }
    
    public static function getCharges($fiche_id)
    {
        global $db, $Langue;
        
        $sql = "select ".self::SQL_TABLE_NAME.".*, total_kw, fiches.code as fiche, ".self::SQL_TABLE_NAME.".fiche_id as fiche_id, fiches_types.nom_$Langue as fiche_type, fiches_sous_types.nom_$Langue as fiche_sous_type, "
                . "tensions_types.nom as tension, fiches.ts_delete, 
                case when fiches.fiche_type_id = 2000003 then 
                    coalesce((select nomenclatures.nom_$Langue from generaux_equipements join nomenclatures on nomenclatures.id = nomenclature_id where generaux_equipements.fiche_id = fiches.id), '".txt('À déterminer')."')
                else 
                    coalesce((select nomenclatures.nom_$Langue from generaux_instruments join nomenclatures on nomenclatures.id = nomenclature_id where generaux_instruments.fiche_id = fiches.id), '".txt('À déterminer')."')
                end as nomenclature "
                . "from ".self::SQL_TABLE_NAME." "
                . "join fiches on fiches.id = ".self::SQL_TABLE_NAME.".fiche_id and fiches.ts_archive is null
                left join proprietes_electriques_auxiliaires on proprietes_electriques_auxiliaires.fiche_id = fiches.id
                JOIN fiches_types ON fiches_types.id = fiche_type_id
                JOIN fiches_sous_types ON fiches_sous_types.id = fiche_sous_type_id
                left join tensions_types on tensions_types.id = tension_type_id "
                . "where source_alimentation_id = ?";
        $liens = $db->getAll($sql, array($fiche_id));
        
        return $liens;
    }
}

<?
class ReseauxDeTelecomsSouterrains extends FicheFormWrapper {
    private static $CLASS_NAME='ReseauxDeTelecomsSouterrains';
    const SQL_TABLE_NAME='reseaux_de_telecoms_souterrains';
    const TITRE='Réseaux de télécoms souterrains';
    public static $AUTO=true;
    public $id;
    public $fiche_id;
    public $element_id;     
    public $element; 
    public $radie;     
    public $date_dinstallation;  
	public $qte_requise;
	public $quantite;    
    public $commentaires;     

    public static function getChampsSaisieMetaData()
    {
        return array(

            'element' => array(

                'classe'      	=> 'ReseauxTelecomsComposantes',
                'table'      	=> 'reseaux_telecoms_composantes',
                'champ_id'      => 'element_id',
                'type'          => 'select',
                'format'        => 'int',
                'titre'         => txt('Élément', false),
                'css_editeur'   => 'medium',
                'obligatoire'	=> 1,

            ),
            'radie' => array(

                'classe'      	=> '',
                'table'      	=> '',
                'champ_id'      => 'radie',
                'type'          => 'input',
                'format'        => 'float',
                'titre'         => txt('Radié', false),
                'css_editeur'   => 'small',

            ),
            'date_dinstallation' => array(

                'classe'      	=> '',
                'table'      	=> '',
                'champ_id'      => 'date_dinstallation',
                'type'          => 'input',
                'format'        => 'date',
                'titre'         => txt('Date d\'installation', false),
                'css_editeur'   => 'small',

            ),
            
            'qte_requise' => array(
				'classe'      	=> '',
				'table'      	=> '',
				'champ_id'      => 'qte_requise',
				'type'          => 'input',
				'format'        => 'int',
				'titre'         => txt('Quantité', false),
				'css_editeur'   => 'tiny'
			),
			
            'commentaires' => array(

                'classe'      	=> '',
                'table'      	=> '',
                'champ_id'      => 'commentaires',
                'type'          => 'input',
                'format'        => 'text',
                'titre'         => txt('Commentaires', false),
                'css_editeur'   => 'xxlarge',

            ),
        );
    }
}

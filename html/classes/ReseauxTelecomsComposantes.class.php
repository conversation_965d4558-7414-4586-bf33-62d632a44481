<?

class ReseauxTelecomsComposantes extends ListClassWrapper {
	private static $CLASS_NAME='ReseauxTelecomsComposantes';
	const SQL_TABLE_NAME='reseaux_telecoms_composantes';
	const CAN_BE_BILAN_DISTANCE=true;
	public $id;
	public $nom;
	public $nom_en;

	public static function getChampsSaisieMetaData()
	{
		return array('nom' => array( 'classe' => '', 'table' => '', 'champ_id' => 'nom', 'type' => 'input', 'format' => 'text', 'titre' => txt('Nom', false), 'css_editeur' => 'large'), 'nom_en' => array( 'classe' => '', 'table' => '', 'champ_id' => 'nom_en', 'type' => 'input', 'format' => 'text', 'titre' => txt('Nom anglais', false), 'css_editeur' => 'large'));
	}
}
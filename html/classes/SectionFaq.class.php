<?
class SectionFaq extends DbRow {
	public $id;
	public $question;
	public $question_fr;
	public $question_en;
	public $reponse;
	public $reponse_fr;
	public $reponse_en;
	public $ordre;
	public $is_actif;



	protected function setDataForId($id)
	{
		global $db_user, $db_pswd, $db_host, $Langue;

		$db = new db($db_user, $db_pswd, $db_host, 'master');

		$sql = "select sq.*, sq.question_$Langue as question, sq.reponse_$Langue as reponse
                from section_faq sq
                where id = ?";
		$row = $db->getRow($sql, array($id));

		$this->populateThisWithThat($row);
	}

    /*
     *
     * return SectionFaq[]
     * */
	public static function getListe($filter = [], $orderBy = 'ordre')
	{
		global $db_user, $db_pswd, $db_host, $Langue;

		$db = new db($db_user, $db_pswd, $db_host, 'master');

		$where = '';
		$data = [];

		if (isset($filter['is_actif'])) {
			$where .= " and is_actif = ?";
			$data[] = (int)$filter['is_actif'];
		}

		$sql = "select sq.*, sq.question_$Langue as question, sq.reponse_$Langue as reponse
                from section_faq sq
                where true $where
                order by $orderBy";
		$rows = $db->getAll($sql, $data);

		$liste = [];
		foreach ($rows as $i => $row) {
			$liste[intval($row['id'])] = new self(intval($row['id']), $row);
		}

		return $liste;
	}

	public static function delete($id)
	{
		global $db_user, $db_pswd, $db_host, $Langue;

		$db = new db($db_user, $db_pswd, $db_host, 'master');

		$db->autocommit(false);
		$db->query("DELETE FROM `section_faq` WHERE id = ?", array($id));
		$db->commit();
	}

	public function afficherQuestionReponse()
	{
		?>
		<li class="faq_question">
            <?= dm_nl2br($this->question) ?>
            <div class="faq_reponse" style="display: none;">
                <span class="faq_reponse_titre"><?= txt('Réponse:') ?></span>
                <span class="faq_reponse_texte"><?= dm_nl2br($this->reponse) ?></span>
            </div>
        </li>
		<?
	}
}
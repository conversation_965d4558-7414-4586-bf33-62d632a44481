<?php
class SuivisBudgetairesAdministrations extends DbRow {
	public $id;
	public $projet_id;
	public $configuration;
	public $configuration_data;
	protected $assocMetaData;

	protected function set(int $id)
	{
		global $db;

		$sql = "select *
                from suivis_budgetaires_administrations apa
                where apa.id = ?";
		$row = $db->getRow($sql, array($id));

		$this->populateThisWithThat($row);
		$this->configuration_data = json_decode($this->getRaw('configuration'), true);
	}

	public static function getAdminForProjet()
	{
		global $db;
		$sql = "select * from suivis_budgetaires_administrations where projet_id = ?";
		$row = $db->getRow($sql, [getProjetId()]);

		if (isset($row) && is_object($row)) {
			$admin = new self($row['id'], $row);
			$admin->configuration_data = json_decode($admin->getRaw('configuration'), true);
			return $admin;
		} else {
			return null;
		}
	}

	public function getEditorHTML(int $iteration = 0)
	{
		global $db;
		?>
        <script>
			$(document).ready(() => {
			});
        </script>
        <style>
            h3 {
                color: darkblue;
            }
            fieldset.cotacote {
                margin: 0 0 15px 40px;
                width: 40%;
                /*float: left;*/
                margin-top: 0!important;
            }
            fieldset.cotacote section > div {
                width: 150px !important;
            }
            #editables fieldset.cotacote label {
                width: 150px !important;
            }
            fieldset.cotacote textarea {
                height: 50px !important;
                min-height: 50px !important;
            }
            table {
                background-image: none;
            }
            table td, table th {
                border: 0px solid;
                padding: 2px 0px;
                text-align: left;
            }
            input {
                width: 200px!important;
            }
            input[type=checkbox] {
                position: relative;
                top: 2px;
            }
            .checkbox_readonly {
                pointer-events: none;
            }
        </style>
        <fieldset style="padding: 0 0 0 20px;">
	        <?php
			erreurs();
			messages();
			?>
            <input type="hidden" name="projet_id" value="<?= getProjetId() ?>" />
            <div id="editables" style="padding: 12px 5px 0px 15px; position: relative;">
                <fieldset>
                    <h2><?= txt('Champs des formulaires') ?></h2>
	                <?php
					$dernierEnsembleTitre = '';
					foreach (self::getChampsMetas() as $blocsNiv2) {
						?>
                        <fieldset class="noMinHeight cotacote">
	                        <?php
							foreach ($blocsNiv2 as $bloc) {
                                ?>
                                <section class="responsable_dossier" style="margin-left: 30px;">
                                    <table style="margin-left: 20px;">
                                        <tr style="width: 750px; margin-bottom: 5px; display: table-row;">
                                            <td style="width: 450px; vertical-align: top"><h5 style="text-decoration: underline; margin: 15px 0;"><?= $bloc['titre'] ?></h5></td>
                                            <td style="width: 175px; text-align: center!important;"><h5 style="text-decoration: underline; margin: 15px 0;"><?= txt('Alias') ?></h5></td>
                                            <td style="width: 75px; text-align: center!important;"><h5 style="text-decoration: underline; margin: 15px 0;"><?= txt('Visible') ?></h5></td>
                                        </tr>
                                        <?php
                                        foreach ($bloc['champs'] as $champ) {
                                            $alias = $this->configuration_data['champs'][$champ['nom']]['alias'] ?? '';
                                            $checked = isset($this->configuration_data['champs'][$champ['nom']]['actif']) || (isset($champ['obligatoire']) && $champ['obligatoire'] == 1) ? ' checked="checked"' : '';
                                            $class_checkbox = isset($champ['obligatoire']) && $champ['obligatoire'] == 1 ? 'checkbox_readonly' : '';
                                            ?>
                                            <tr style="width: 750px; margin-bottom: 5px; display: table-row;">
                                                <td style="width: 200px; vertical-align: top"><span<?= isset($champ['descriptif']) && dm_strlen($champ['descriptif']) > 0 ? ' title="' . $champ['descriptif'] . '"' : '' ?>><?= $champ['label'] ?></span></td>
                                                <td style="width: 175px; text-align: center!important;">
                                                    <input type="hidden" id="label_<?= $champ['nom'] ?>" name="champs[<?= $champ['nom'] ?>][label]" value="<?= $champ['label'] ?>"/>
                                                    <input type="text" id="alias_<?= $champ['nom'] ?>" name="champs[<?= $champ['nom'] ?>][alias]" value="<?= $alias ?>"/>
                                                </td>
                                                <td style="text-align: center!important;"><input type="checkbox" id="export_<?= $champ['nom'] ?>" name="champs[<?= $champ['nom'] ?>][actif]" <?= $checked ?> class="<?= $class_checkbox ?>" /></td>
                                            </tr>
                                            <?php
                                        }
                                        ?>
                                    </table>
                                </section>
                                <?php
							}
							?>
                        </fieldset>
						<?php
					}
					?>
            </div>
        </fieldset>
		<?php
	}

	public static function validate($data, $id)
	{
		return true;
	}

	public function getAssocMetaData()
	{
		if (!isset($this->assocMetaData)) {
			foreach (self::getChampsMetas() as $blocsNiv2) {
				foreach ($blocsNiv2 as $bloc) {
					if ($bloc['ensemble'] <> 'globale') {
						foreach ($bloc['champs'] as $champ) {
							$this->assocMetaData[$champ['nom']] = $champ;
						}
					}
				}
			}
		}
		return $this->assocMetaData;
	}

	public function getTooltipHtml(string $champ): string
	{
		$dataAssoc = $this->getAssocMetaData();
		return isset($dataAssoc[$champ]['descriptif']) && dm_strlen($dataAssoc[$champ]['descriptif']) > 0 ? ' title="' . $dataAssoc[$champ]['descriptif'] . '"' : '';
	}

	public function getLabelForChamp(string $champ): string
	{
		return isset($this->configuration_data['champs'][$champ]['alias']) && dm_strlen($this->configuration_data['champs'][$champ]['alias']) > 0 ? $this->configuration_data['champs'][$champ]['alias'] : ($this->configuration_data['champs'][$champ]['label'] ?? 'N/A');
	}

	public function isChampActif(string $champ): bool
	{
		return isset($this->configuration_data['champs'][$champ]['actif']) || !isset($this->configuration_data['champs']);
	}

	public function isBlocActif(array $champs): bool
	{
		foreach ($champs as $champ) {
            if (is_array($champ)) {
	            if (isset($this->configuration_data['champs'][$champ['nom']]['actif'])) {
		            return true;
	            }
            } else {
	            if (isset($this->configuration_data['champs'][$champ]['actif'])) {
		            return true;
	            }
            }
		}
		return false;
	}

// Les information dans la section ci dessous (getColonnesMetas) sont pour l'information qui doit appaitre dans le tableau incluant les entêtes
//quand l'information est préenté dans une colonne, il faut mettre col_ en avant du nom du champs dans le paramètre nom (normalement on utilise col_nom de la colonne dans la bd). //Il faut que la valeur du paramètre nom soit unique. Si on retrouve un champ identique dans les projets et les groupes, il faut attribuer in nom unique à chacun.

/*
	public static function getColonnesMetas(): array
	{
		global $db;

		$metas = [
			[
				[
					'titre' => "Information du projet",
					'ensemble' => "projet",
					'colonnes' => [
						[
							'nom' => 'col_numero_projet',
							'label' => txt('Numéro du projet', false),
							'obligatoire' => 1,
							'col_bd' => 'numero',
							'exportable' => 0,
							'filtrable' => 0,
							'descriptif' => txt("- Numéro servant à l'identification du projet \n - Sert de pivot pour l'importation des données au niveau du projet(lot)", false),
						],
						[
							'nom' => 'col_description_projet',
							'label' => txt('Description du projet', false),
							'col_bd' => 'description',
							'exportable' => 0,
							'filtrable' => 0,
							'descriptif' => txt('', false),
						],
						[
							'nom' => 'col_priorite_acquisition',
							'label' => txt('Priorité', false),
							'col_bd' => 'priorite_acquisition_id',
							//'exportable' => 1,
//							'filtrable' => 1,
							'descriptif' => txt('', false),
						],
						[
							'nom' => 'col_bloquant_type',
							'label' => txt('Bloquant', false),
							'col_bd' => 'bloquant_type_id',
							//'exportable' => 1,
//							'filtrable' => 1,
							'descriptif' => txt("Contrainte empêchant le processus d/'acquisiton de progresser", false),
						],
						[
							'nom' => 'col_sous_volet_responsable_id',
							'label' => txt('Sous-volet responsable', false),
							'col_bd' => 'sous_volet_responsable_id',
							//'exportable' => 1,
//							'filtrable' => 1,
							'descriptif' => txt('', false),
						],
						[
							'nom' => 'col_phase_incidence_contraignante_id',
							'label' => txt('Phase d\'incidence la plus contraignante', false),
							'col_bd' => 'phase_incidence_contraignante_id',
							//'exportable' => 1,
//							'filtrable' => 1,
							'descriptif' => txt('', false),
						],
						[
							'nom' => 'col_priorisation_id',
							'label' => txt('Priorisation', false),
							'col_bd' => 'priorisation_id',
							//'exportable' => 1,
//							'filtrable' => 1,
							'descriptif' => txt('', false),
						],
						[
							'nom' => 'col_mode_sollicitation_id',
							'label' => txt('Mode de sollicitation', false),
							'col_bd' => 'mode_sollicitation_id',
							//'exportable' => 1,
//							'filtrable' => 1,
							'descriptif' => txt('', false),
						],
						[
							'nom' => 'col_mode_adjudication_id',
							'label' => txt('Mode d\'adjudication', false),
							'col_bd' => 'mode_adjudication_id',
							//'exportable' => 1,
//							'filtrable' => 1,
							'descriptif' => txt('', false),
						],
						[
							'nom' => 'col_ts_ajout',
							'label' => txt('Date de création', false),
							'exportable' => 1,
							'descriptif' => txt('', false),
						],
						[
							'nom' => 'col_titre_prochain_jalon',
							'label' => txt('Titre du prochain jalon', false),
							'col_bd' => 'titre_prochain_jalon',
							'exportable' => 1,
							'descriptif' => txt('', false),
						],
						[
							'nom' => 'col_date_prochain_jalon',
							'label' => txt('Échéance du jalon', false),
							'col_bd' => 'date_prochain_jalon',
							'exportable' => 1,
							'format' => 'date',
							'descriptif' => txt('', false),
						],
						[
							'nom' => 'col_cout_total_estimation_projet',
							'label' => txt('Estimation budg. totale (av. taxes)', false),
							'col_bd' => 'cout_total_estimation',
							'exportable' => 1,
							'descriptif' => txt('- Déterminé par la totalisation des quantités à traitées (Requis actuels - Conservés actuels) multiplié par le Coût unitaire (actuel), soit le coût normé provenant de la librairie', false),
						],
						[
							'nom' => 'col_cout_total_projet',
							'label' => txt('Coût total (effectif)', false),
							'col_bd' => 'cout_total',
							'exportable' => 1,
							'descriptif' => txt('', false),
						],
						[
							'nom' => 'col_statut',
							'label' => txt('Statut', false),
							'exportable' => 1,
							'descriptif' => txt('', false),
						],
					]
				],
			],
			[
				[
					'titre' => "Information du groupe",
					'ensemble' => "groupe",
					'colonnes' => [
						[
							'nom' => 'col_numero',
							'label' => txt('Numéro du groupe', false),
							'obligatoire' => 1,
							'col_bd' => 'numero',
							'exportable' => 0,
							'filtrable' => 0,
							'descriptif' => txt("- Numéro servant à l'identification du groupe d'équipement \n - Sert de pivot pour l'importation des données au niveau du groupe d'équipement", false),
						],
						[
							'nom' => 'col_description',
							'label' => txt('Description du groupe', false),
							'col_bd' => 'description',
							'exportable' => 0,
							'filtrable' => 0,
							'descriptif' => txt('', false),					],
						[
							'nom' => 'col_code_equipement',
							'label' => txt('Code de l\'équipement', false),
							'col_bd' => 'code_equipement',
							'exportable' => 0,
							'filtrable' => 0,
							'descriptif' => txt("- Code provenant de la librairie d'équipement", false),
						],
						[
							'nom' => 'col_description_equipement',
							'label' => txt('Description de l\'équipement', false),
							'col_bd' => 'description_equipement',
							'exportable' => 1,
							'descriptif' => txt('', false),
						],
						[
							'nom' => 'col_categorie_equipement',
							'label' => txt('Catégorie de l\'équipement', false),
							'col_bd' => 'categorie_equipement',
							'exportable' => 1,
							'descriptif' => txt('', false),
						],
						[
							'nom' => 'col_phase_incidence_equipement',
							'label' => txt('Phase d\'incidence', false),
							'col_bd' => 'phase_incidence_equipement',
							'exportable' => 1,
							'descriptif' => txt('', false),
						],
						[
							'nom' => 'col_categorie_responsabilite_equipement',
							'label' => txt('Catégorie de responsabilité', false),
							'col_bd' => 'categorie_responsabilite_equipement',
							'exportable' => 1,
							'descriptif' => txt('', false),
						],
						[
							'nom' => 'col_cout_unitaire',
							'label' => txt('Coût unitaire (actuel)', false),
							'col_bd' => 'prix_unitaire_nouveau',
							'exportable' => 1,
							'descriptif' => txt("- Coût provenant de la librairie d'équipement", false),
						],
						[
							'nom' => 'col_qte_a_commander_acheter_lot',
							'label' => txt('Qté totale (à acheter dans le lot)', false),
							'col_bd' => 'quantite_estimation',
							'exportable' => 1,
							'descriptif' => txt("- Somme des quantités calculées selon la formule < Requis actuel (A) > - < Conservés actuel (C) >", false),
						],
						[
							'nom' => 'col_cout_total_groupe_acheter_lot',
							'label' => txt('Coût total (à acheter dans le lot)', false),
							'col_bd' => 'cout_total_estimation',
							'exportable' => 1,
							'descriptif' => txt('', false),
						],
						[
							'nom' => 'cout_unitaire_effectif',
							'label' => txt('Coût unitaire (effectif)', false),
							'col_bd' => 'cout_unitaire_effectif',
							'exportable' => 1,
							'descriptif' => txt("- Coût réel d'achat éditable dans le formulaire du groupe d'équipement", false),
						],
						[
							'nom' => 'col_qte_a_commander_totale',
							'label' => txt('Qté totale (effective)', false),
							'col_bd' => 'quantite',
							'exportable' => 1,
							'descriptif' => txt("- Somme des quantités sélectionnées dans la colonne < À commander >", false),
						],
						[
							'nom' => 'col_cout_total_groupe',
							'label' => txt('Coût total (effectif)', false),
							'col_bd' => 'cout_total',
							'exportable' => 1,
							'descriptif' => txt("- Coût unitaire effectif (réel) multiplié par la somme des quantités sélectionnées dans la colonne < À commander >", false),
						],
						[
							'nom' => 'col_residuel_comm',
							'label' => txt('Résiduel (com.)', false),
							'exportable' => 1,
							'descriptif' => txt('', false),
						],
						[
							'nom' => 'col_total_recu',
							'label' => txt('Total reçu', false),
							'exportable' => 1,
							'descriptif' => txt('', false),
						],
						[
							'nom' => 'col_total_distribue',
							'label' => txt('Total à distribuer', false),
							'exportable' => 1,
							'descriptif' => txt('', false),
						],
						[
							'nom' => 'col_residuel_distribue',
							'label' => txt('Résiduel (dist.)', false),
							'exportable' => 1,
							'descriptif' => txt('', false),
						],
						[
							'nom' => 'col_ts_ajout_groupe',
							'label' => txt('Date de création', false),
							'exportable' => 1,
							'descriptif' => txt('', false),
						],
					]
				],
				[
					'titre' => "Information du bon de commande",
					'ensemble' => "groupe",
					'colonnes' => [
						[
							'nom' => 'col_bon_commande',
							'label' => txt('# Bon de commande', false),
							'col_bd' => 'bon_commande',
							'importable' => 1,
							'exportable' => 1,
							'filtrable' => 1,
							'descriptif' => txt("- Numéro provenant du logiciel de gestion des achats (ex: GRM) \n - Sert de pivot pour l'importation des données complémentaires (ex: Produits non gérés par DocMatic)", false),
						],
						[
							'nom' => 'produit_no',
							'label' => txt('Numéro du produit (GRM)', false),
							'importable' => 1,
							'exportable' => 1,
							'filtrable' => 1,
							'descriptif' => txt("- Numéro provenant du logiciel de gestion des achats (ex: GRM) \n - Permet l'affichage des produits et informations connexes sur la vue des infornations complémentaires (en cliquant sur le numéro de Bon de commande)", false),
						],
						[
							'nom' => 'produit_description',
							'label' => txt('Description du produit (GRM)', false),
							'importable' => 1,
							'exportable' => 1,
							'filtrable' => 1,
							'descriptif' => txt('', false),
						],
						[
							'nom' => 'quantite_disponible',
							'label' => txt('Quantité reçue', false),
							'importable' => 1,
							'exportable' => 1,
							'descriptif' => txt('', false),
						],
						[
							'nom' => 'quantite_commandee',
							'label' => txt('Quantité commandée', false),
							'importable' => 1,
							'exportable' => 1,
							'descriptif' => txt('', false),
						],
						[
							'nom' => 'is_reception_globale',
							'label' => txt('Récepeption globale', false),
							'importable' => 1,
							'exportable' => 1,
							'descriptif' => txt('', false),
						],
					]
				],
			],
			[
				[
					'titre' => "Intrants bruts",
					'ensemble' => "fiche",
					'colonnes' => [
						[
							'nom' => 'col_local',
							'label' => txt('Local', false),
							'col_bd' => 'local',
							'exportable' => 1,
							'descriptif' => txt('', false),
						],
						[
							'nom' => 'col_requis_j1',
							'label' => txt('Requis (J1)', false),
							'col_bd' => 'requise_init',
							'exportable' => 1,
							'descriptif' => txt('', false),
						],
						[
							'nom' => 'col_existante_j1',
							'label' => txt('Existantes (J1)', false),
							'col_bd' => 'existante_init',
							'exportable' => 1,
							'descriptif' => txt('', false),
						],
						[
							'nom' => 'col_conserve_j1',
							'label' => txt('Conservés (J1)', false),
							'col_bd' => 'demenager_init',
							'exportable' => 1,
							'descriptif' => txt('', false),
						],
						[
							'nom' => 'requise_actuelle',
							'label' => txt('Requis actuels (A)', false),
							'reference' => true,
							'exportable' => 1,
							'descriptif' => txt('', false),
						],
						[
							'nom' => 'existante_actuelle',
							'label' => txt('Existants actuels (B)', false),
							'reference' => true,
							'exportable' => 1,
							'descriptif' => txt('', false),
						],
						[
							'nom' => 'demenager_actuelle',
							'label' => txt('Conservés actuels (C)', false),
							'reference' => true,
							'exportable' => 1,
							'descriptif' => txt('', false),
						],
					],
				],
				[
					'titre' => "Intrants calculés",
					'ensemble' => "fiche",
					'colonnes' => [
						[
							'nom' => 'remplacement_actuelle',
							'label' => txt('Remplacements (B-C)', false),
							'reference' => true,
							'exportable' => 1,
							'descriptif' => txt('', false),
						],
						[
							'nom' => 'col_nouveaux',
							'label' => txt('Nouveaux (A-C)', false),
							'reference' => true,
							'exportable' => 1,
							'descriptif' => txt('', false),
						],
						[
							'nom' => 'developpement_actuelle',
							'label' => txt('Développements (A-B)', false),
							'reference' => true,
							'exportable' => 1,
							'descriptif' => txt('', false),
						],
						[
							'nom' => 'col_a_traiter',
							'label' => txt('À traiter', false),
							'col_bd' => 'a_traiter',
							'exportable' => 1,
							'descriptif' => txt('', false),
						],
						[
							'nom' => 'col_deja_traite',
							'label' => txt('Déjà traité', false),
							'col_bd' => 'deja_traite',
							'exportable' => 1,
							'descriptif' => txt('', false),
						],
						[
							'nom' => 'col_residuel_com',
							'label' => txt('Résiduel (com.)', false),
							'col_bd' => 'residuel_com',
							'exportable' => 1,
							'descriptif' => txt('', false),
						],
						[
							'nom' => 'col_a_commander',
							'label' => txt('À commander', false),
							'col_bd' => 'commande',
							'exportable' => 1,
							'descriptif' => txt('', false),
						],
						[
							'nom' => 'col_residuel_dist',
							'label' => txt('Résiduel (dist.)', false),
							'exportable' => 1,
							'descriptif' => txt('', false),
						],
						[
							'nom' => 'col_a_distribuer',
							'label' => txt('À distribuer', false),
							'exportable' => 1,
							'descriptif' => txt('', false),
						],
						[
							'nom' => 'colonne_supplementaire_detail_1',
							'label' => txt('Colonne supplémentaire 1', false),
							'importable' => 1,
							'col_bd' => 'colonne_supplementaire_detail_1',
							'format' => 'float',
							'exportable' => 1,
							'descriptif' => txt('', false),
						],
						[
							'nom' => 'colonne_supplementaire_detail_2',
							'label' => txt('Colonne supplémentaire 2', false),
							'importable' => 1,
							'col_bd' => 'colonne_supplementaire_detail_2',
							'format' => 'float',
							'exportable' => 1,
							'descriptif' => txt('', false),
						],
						[
							'nom' => 'colonne_supplementaire_detail_3',
							'label' => txt('Colonne supplémentaire 3', false),
							'importable' => 1,
							'col_bd' => 'colonne_supplementaire_detail_3',
							'format' => 'float',
							'exportable' => 1,
							'descriptif' => txt('', false),
						],
						[
							'nom' => 'colonne_supplementaire_detail_4',
							'label' => txt('Colonne supplémentaire 4', false),
							'importable' => 1,
							'col_bd' => 'colonne_supplementaire_detail_4',
							'format' => 'float',
							'exportable' => 1,
							'descriptif' => txt('', false),
						],
						[
							'nom' => 'colonne_supplementaire_detail_5',
							'label' => txt('Colonne supplémentaire 5', false),
							'importable' => 1,
							'col_bd' => 'colonne_supplementaire_detail_5',
							'format' => 'float',
							'exportable' => 1,
							'descriptif' => txt('', false),
						]
					]
				],
			]
		];

		$sql = "select * from suivis_budgetaires where projet_id = ?";
		$suivi = $db->getRow($sql, [getProjetId()]);
		if (isset($suivi['id'])) {
			$suiviBudgetaire = new SuivisBudgetaires($suivi['id']);
		}

		if (isset($suiviBudgetaire)) {
			$metas[2][2] = [
				'titre' => "Colonnes des sources de financement (tableau)",
				'ensemble' => "fiche",
				'colonnes' => []
			];
			foreach ($suiviBudgetaire->getPortefeuilles(true) as $portefeuille) {
				$metas[2][2]['colonnes'][] = [
					'nom' => 'qt_portefeuille_' . $portefeuille['portefeuille_numero'],
					'label' => $portefeuille['nom'],
					'reference' => true,
					'exportable' => 1,
					'filtrable' => 1
				];
			}
		}

		return $metas;
	}
*/
// Les information dans la section ci dessous (getChampsMetas) sont pour l'information qui doit appaitre dans les formulaires

	public static function getChampsMetas(): array
	{
		return [
			[
				[
					'titre' => txt("Source de financement 1", false),
					'champs' => [
						[
							'nom' => 'cout_sf1_initial',
							'label' => txt('Coût SF1 initial/demande MSSS J0 (avant tx)', false),
							'exportable' => 1,
							'format' => 'float',
							'editable' => 0,
							'col_bd' => 'cout_total_source1_j0',
							'descriptif' => txt('Ʃéquipements lot @ J0 = coût unitaire librairie x Qté SF1', false),
						],
						[
							'nom' => 'cout_autorise_sf1',
							'label' => txt('Coût autorisé SF1 (avant tx)', false),
							'exportable' => 1,
							'format' => 'float',
							'editable' => 0,
							'col_bd' => 'reponse_cout_total_avant_taxes',
							'descriptif' => txt('Ʃéquipements lot = prix unitaire avant taxes (recommandation MSSS) x Qté SF1(recommandation MSSS)', false),
						],
						[
							'nom' => 'cout_sf1_effectif',
							'label' => txt('Coût SF1 total effectif (avant tx)', false),
							'exportable' => 1,
							'format' => 'float',
							'editable' => 0,
							'col_bd' => 'cout_total_source1',
							'descriptif' => txt('Ʃéquipements lot = coût unitaire librairie actuel x qté requise fiche local actuel', false),
						],
						[
							'nom' => 'difference_cout_recommandation_vs__sf1_effectif',
							'label' => txt('Différence de coût entre recommandation MSSS et SF1 effectif', false),
							'exportable' => 1,
							'format' => 'float',
							'editable' => 0,
							'col_bd' => 'difference_cout_sf1_recommandation_vs_effectif',
							'descriptif' => txt('Coût autorisé SF1 (avant tx) - Coût SF1 total effectif (avant tx)', false),
//Formule pour le trigger "reponse_cout_total_avant_taxes" - "cout_total_source1"
						],
						[
							'nom' => 'pourcentage_recommandation_vs_effectif',
							'label' => txt('Pourcentage de recommandation MSSS VS SF1 ajusté/effectif', false),
							'exportable' => 1,
							'format' => 'pourcent',
							'editable' => 0,
							'col_bd' => 'difference_pourcentage_sf1_recommandation_vs_effectif	',
							'descriptif' => txt('Coût autorisé SF1(avant tx) / Coût SF1 total ajusté/effectif (avant tx)', false),
//Formule pour le trigger "reponse_cout_total_avant_taxes" / "cout_total_source1"
						],
					],
				],
				[
					'titre' => txt("Source de financement 2", false),
					'champs' => [
						[
							'nom' => 'cout_sf2_confirme',
							'label' => txt('Coût SF2 confirmé (avant tx)', false),
							'exportable' => 1,
							'format' => 'float',
							'editable' => 0,
							'col_bd' => 'analyse_source2_cout_total_confirme',
							'descriptif' => txt('Ʃéquipements lot = Qté SF2 confirmé  x coût unitaire SF2 confirmé (suivi demande financement)', false),
						],
						[
							'nom' => 'cout_sf2_effectif',
							'label' => txt('Coût SF2 effectif (avant tx)', false),
							'exportable' => 1,
							'format' => 'float',
							'editable' => 0,
							'col_bd' => 'cout_total_source2',
							'descriptif' => txt('Ʃéquipements lot = Qté SF2 Existante - conservée) fiche local actuel  x coût unitaire librairie actuel', false),
						],
						[
							'nom' => 'difference_cout_sf2_confirme_vs_sf2_effectif',
							'label' => txt('Différence de coût entre SF2 confirmé et SF2 ajusté', false),
							'exportable' => 1,
							'format' => 'float',
							'editable' => 0,
							'col_bd' => 'difference_cout_sf2_confirme_vs_effectif',
							'descriptif' => txt('Coût SF2 confirmé (avant taxes) - Coût SF2 total ajusté/effectif (avant tx)', false),
//Formule pour le trigger "analyse_source2_cout_total_confirme" - "cout_total_source2"
						],
					],
				],
				[
					'titre' => txt("Source de financement 3", false),
					'champs' => [
						[
							'nom' => 'cout_sf3_confirme',
							'label' => txt('Coût SF2 confirmé (avant tx)', false),
							'exportable' => 1,
							'format' => 'float',
							'editable' => 0,
							'col_bd' => 'analyse_source3_cout_total_confirme',
							'descriptif' => txt('Ʃéquipements lot = Quantité SF4confirmée x Coût unitaire SF4 confirmé (avant taxes) (suivi demande de financement)', false),
						],
						[
							'nom' => 'cout_sf3_effectif',
							'label' => txt('Coût SF3 ajusté/effectif (avant tx)', false),
							'exportable' => 1,
							'format' => 'float',
							'editable' => 0,
							'col_bd' => 'cout_total_source3',
							'descriptif' => txt('Ʃéquipements lot = Quantité SF3 effective (suivi demande de financement)  x coût unitaire librairie actuel', false),
						],
						[
							'nom' => 'différence_cout_sf3_confirme_vs_sf3_effectif',
							'label' => txt('Différence de coût SF3 confirmé et SF3 ajusté', false),
							'exportable' => 1,
							'format' => 'float',
							'editable' => 0,
							'col_bd' => 'difference_cout_sf3_confirme_vs_effectif',
							'descriptif' => txt('Coût SF3 confirmé (avant tx) - Coût SF3 ajusté/effectif (avant tx)', false),
//Formule pour le trigger "analyse_source3_cout_total_confirme" - "cout_total_source3"
						],
					],
				],
				[
					'titre' => txt("Source de financement 4", false),
					'champs' => [
						[
							'nom' => 'cout_sf4_confirme',
							'label' => txt('Coût SF4 confirmé (avant tx)', false),
							'exportable' => 1,
							'format' => 'float',
							'editable' => 0,
							'col_bd' => 'analyse_source4_cout_total_confirme',
							'descriptif' => txt('Ʃéquipements lot = Quantité SF4 x Coût unitaire SF4 confirmé (avant taxes) (suivi demande de financement)', false),
						],
						[
							'nom' => 'cout_sf4_effectif',
							'label' => txt('Coût SF4 ajusté/effectif (avant tx)', false),
							'exportable' => 1,
							'format' => 'float',
							'editable' => 0,
							'col_bd' => 'cout_total_source4',
							'descriptif' => txt('Ʃéquipements lot = Quantité SF4 effective (suivi demande de financement)  x coût unitaire librairie actuel', false),
						],
						[
							'nom' => 'différence_cout_sf4_confirme_vs_sf4_effectif',
							'label' => txt('Différence de coût SF4 confirmé et SF4 ajusté', false),
							'exportable' => 1,
							'format' => 'float',
							'editable' => 0,
							'col_bd' => 'difference_cout_sf4_confirme_vs_effectif',
							'descriptif' => txt('Coût SF4 (avant tx) - Coût SF4 ajusté/effectif (avant tx)', false),
//Formule pour le trigger "analyse_source4_cout_total_confirme" - "cout_total_source4"
						],
					],
				],
				[
					'titre' => txt("Source de financement 5", false),
					'champs' => [
						[
							'nom' => 'cout_sf5_confirme',
							'label' => txt('Coût SF5 confirmé (avant tx)', false),
							'exportable' => 1,
							'format' => 'float',
							'editable' => 0,
							'col_bd' => 'analyse_source5_cout_total_confirme',
							'descriptif' => txt('Ʃéquipements lot = Quantité SF5 confirmée x Coût unitaire SF5 confirmé (avant taxes) (suivi demande de financement)', false),
						],
						[
							'nom' => 'cout_sf5_effectif',
							'label' => txt('Coût SF5 ajusté/effectif (avant tx)', false),
							'exportable' => 1,
							'format' => 'float',
							'editable' => 0,
							'col_bd' => 'cout_total_source5',
							'descriptif' => txt('Ʃéquipements lot = Quantité SF5 effective (suivi demande de financement) x coût unitaire librairie actuel', false),
						],
						[
							'nom' => 'différence_cout_sf5_confirme_vs_sf5_effectif',
							'label' => txt('Différence de coût SF5 confirmé et SF5 ajusté', false),
							'exportable' => 1,
							'format' => 'float',
							'editable' => 0,
							'col_bd' => 'difference_cout_sf5_confirme_vs_effectif',
							'descriptif' => txt('Coût SF5 (avant tx) - Coût SF5 ajusté/effectif (avant tx)', false),
//Formule pour le trigger "analyse_source5_cout_total_confirme" - "cout_total_source5"
						],
					],
				],
				[
					'titre' => "Total",
					'champs' => [
						[
							'nom' => 'cout_total_financement_confirme',
							'label' => txt('Coût total financement confirmé (avant taxes)', false),
							'exportable' => 1,
							'format' => 'float',
							'editable' => 0,
							'col_bd' => 'prix_unitaire_nouveau * qte_source1',
							'descriptif' => txt('Ʃéquipements lot = Coût autorisé développement MSSS (avant tx) + Coût remplacement confirmé (avant taxes) + Coût développement autre financement confirmé (avant taxes)', false),
						],
						[
							'nom' => 'cout_total_lot_estime',
							'label' => txt('Coût total estimé du lot (av. tx)', false),
							'exportable' => 1,
							'format' => 'float',
							'editable' => 0,
							'col_bd' => 'prix_unitaire_nouveau * qte_source1',
							'descriptif' => txt('Ʃéquipements lot = (qté requise fiche local - qté conservée fiche local) x coût unitaire librairie actuel', false),
						],
					],
				],
				[
					'titre' => "Adjudication",
					'champs' => [
						[
							'nom' => 'cout_total_contrat_adjuge',
							'label' => txt('Coût total contrat adjugé (avant tx)', false),
							'exportable' => 1,
							'format' => 'float',
							'editable' => 1,
							'col_bd' => 'cout_total_contrat_adjuge',
							'descriptif' => txt('Entrée manuelle', false),
						],
						[
							'nom' => 'cout_partiel_contrat_adjuge_sf1',
							'label' => txt('Coût partiel contrat adjugé / SF1 (avant tx)', false),
							'exportable' => 1,
							'format' => 'float',
							'editable' => 1,
							'col_bd' => 'cout_partiel_contrat_adjuge_sf1',
							'descriptif' => txt('Entrée manuelle', false),
						],
						[
							'nom' => 'cout_partiel_contrat_adjuge_sf2',
							'label' => txt('Coût total contrat adjugé / SF2(avant tx)', false),
							'exportable' => 1,
							'format' => 'float',
							'editable' => 1,
							'col_bd' => 'cout_partiel_contrat_adjuge_sf2',
							'descriptif' => txt('Entrée manuelle', false),
						],
						[
							'nom' => 'cout_partiel_contrat_adjuge_sf3',
							'label' => txt('Coût total contrat adjugé / SF3(avant tx)', false),
							'exportable' => 1,
							'format' => 'float',
							'editable' => 1,
							'col_bd' => 'cout_partiel_contrat_adjuge_sf3',
							'descriptif' => txt('Entrée manuelle', false),
						],
						[
							'nom' => 'cout_partiel_contrat_adjuge_sf4',
							'label' => txt('Coût total contrat adjugé / SF4(avant tx)', false),
							'exportable' => 1,
							'format' => 'float',
							'editable' => 0,
							'col_bd' => 'cout_partiel_contrat_adjuge_sf4',
							'descriptif' => txt('Entrée manuelle', false),
						],
						[
							'nom' => 'cout_partiel_contrat_adjuge_sf5',
							'label' => txt('Coût total contrat adjugé / SF5(avant tx)', false),
							'exportable' => 1,
							'format' => 'float',
							'editable' => 1,
							'col_bd' => 'cout_partiel_contrat_adjuge_sf5',
							'descriptif' => txt('Entrée manuelle', false),
						],
					],
				],
				[
					'titre' => "Modification au contrat",
					'champs' => [
						[
							'nom' => 'cout_total_contrat_revise',
							'label' => txt('Coût total contrat révisé (avant tx)', false),
							'exportable' => 1,
							'format' => 'float',
							'editable' => 1,
							'col_bd' => 'cout_total_contrat_revise',
							'descriptif' => txt('Entrée manuelle', false),
						],
						[
							'nom' => 'cout_partiel_contrat_revise_sf1',
							'label' => txt('Coût partiel contrat révisé / SF1(avant tx)', false),
							'exportable' => 1,
							'format' => 'float',
							'editable' => 1,
							'col_bd' => 'cout_partiel_contrat_revise_sf1',
							'descriptif' => txt('Entrée manuelle', false),
						],
						[
							'nom' => 'cout_partiel_contrat_revise_sf2',
							'label' => txt('Coût total contrat révisé / SF2 (avant tx)', false),
							'exportable' => 1,
							'format' => 'float',
							'editable' => 1,
							'col_bd' => 'cout_partiel_contrat_revise_sf2',
							'descriptif' => txt('Entrée manuelle', false),
						],
						[
							'nom' => 'cout_partiel_contrat_revise_sf3',
							'label' => txt('Coût partiel contrat révisé / SF3 (avant tx)', false),
							'exportable' => 1,
							'format' => 'float',
							'editable' => 1,
							'col_bd' => 'cout_partiel_contrat_revise_sf3',
							'descriptif' => txt('Entrée manuelle', false),
						],
						[
							'nom' => 'cout_partiel_contrat_revise_sf4',
							'label' => txt('Coût partiel contrat révisé / SF4 (avant tx)', false),
							'exportable' => 1,
							'format' => 'float',
							'editable' => 1,
							'col_bd' => 'cout_partiel_contrat_revise_sf4',
							'descriptif' => txt('Entrée manuelle', false),
						],
						[
							'nom' => 'cout_partiel_contrat_revise_sf5',
							'label' => txt('Coût partiel contrat révisé / SF5 (avant tx)', false),
							'exportable' => 1,
							'format' => 'float',
							'editable' => 1,
							'col_bd' => 'cout_partiel_contrat_revise_sf5',
							'descriptif' => txt('Entrée manuelle', false),
						],
						[
							'nom' => 'pourcentage_modification_rapport_cout_initial_contrat',
							'label' => txt('Pourcentage de la modification par rapport au coût initial du contrat', false),
							'exportable' => 1,
							'format' => 'float',
							'editable' => 0,
							'formule' => 'prix_unitaire_nouveau * qte_source1',
							'descriptif' => txt('(Colonne S - Colonne O) / Colonne O', false),
						],
					],
				],
				[
					'titre' => txt("Gestion SF1 (surplus/déficit)", false),
					'champs' => [
						[
							'nom' => 'solde_budgetaire_sf1',
							'label' => txt('Solde budgétaire SF1 (octroi-financement)', false),
							'exportable' => 1,
							'format' => 'float',
							'editable' => 0,
							'formule' => 'prix_unitaire_nouveau * qte_source1',
							'descriptif' => txt('Si Coût partiel contrat révisé / développement MSSS (avant tx) = vide -> Coût partiel contrat octroyé / développement MSSS (avant tx) - Coût autorisé développement MSSS (avant tx). Si Coût partiel contrat révisé / développement MSSS (avant tx) = non vide -> Coût partiel contrat révisé / développement MSSS (avant tx) - Coût autorisé développement MSSS (avant tx)', false),
						],
						[
							'nom' => 'solde_budgetaire_sf1_effectif)',
							'label' => txt('Solde budgétaire SF1 actualisé/effectif (après transfert)', false),
							'exportable' => 1,
							'format' => 'float',
							'editable' => 0,
							'formule' => 'prix_unitaire_nouveau * qte_source1',
							'descriptif' => txt('Solde budgétaire développement MSSS (octroi-financement) - transfert vers enveloppe de surplus (lorsque activé) + transfert provenant de enveloppe de surplus (lorsque activé)', false),
						],
						[
							'nom' => 'provision_requise_enveloppe_sf1_lot',
							'label' => txt('Provision requise enveloppe SF1 dans le lot', false),
							'exportable' => 1,
							'format' => 'float',
							'editable' => 1,
							'col_bd' => 'provision_requise_enveloppe_sf1_lot',
							'descriptif' => txt('Montant à inscrire manuellement (par exemple, max 10 % du contrat octroyé)', false),
						],
						[
							'nom' => 'montant_transfert_solde_vers_enveloppe_surplus',
							'label' => txt('Montant du transfert de solde vers enveloppe de surplus', false),
							'exportable' => 1,
							'format' => 'float',
							'editable' => 1,
							'col_bd' => 'montant_transfert_solde_vers_enveloppe_surplus',
							'descriptif' => txt('Montant à inscrire manuellement (en considérant provision requise)', false),
						],
						[
							'nom' => 'transfert_solde_positif_vers_enveloppe_surplus',
							'label' => txt('Tranfert du solde positif vers enveloppe de surplus', false),
							'exportable' => 1,
							'format' => 'tinyint',
							'editable' => 1,
							'col_bd' => 'transfert_solde_positif_vers_enveloppe_surplus',
							'descriptif' => txt('√ case à cocher qui permet le transfert budgétaire du lot vers enveloppe de surplus et la soustraction de ce montant du Solde budgétaire développement MSSS actualisé/effectif (après transfert)', false),
						],
						[
							'nom' => 'montant_transfert_solde_provenant_enveloppe_surplus',
							'label' => txt('Montant du transfert de solde provenant de enveloppe de surplus', false),
							'exportable' => 1,
							'format' => 'float',
							'editable' => 1,
							'col_bd' => 'montant_transfert_solde_provenant_enveloppe_surplus',
							'descriptif' => txt('Montant à inscrire manuellement (en considérant déficit)', false),
						],
						[
							'nom' => 'transfert_provenant_enveloppe_surplus_pour_solde_negatif',
							'label' => txt('Transfert provenant enveloppe de surplus pour le solde négatif', false),
							'exportable' => 1,
							'format' => 'tinyint',
							'editable' => 1,
							'col_bd' => 'transfert_provenant_enveloppe_surplus_pour_solde_negatif',
							'descriptif' => txt('√ case à cocher qui permet le transfert budgétaire enveloppe de surplus vers le lot et addition de ce montant au Solde budgétaire développement MSSS actualisé/effectif (après transfert)', false),
						],
						[
							'nom' => 'statut_financement_sf1',
							'label' => txt('Statut financement SF1', false),
							'exportable' => 1,
							'format' => 'int',
							'editable' => 1,
							'col_bd' => 'statut_financement_sf1',
							'descriptif' => txt('Menu déroulant personnalisable', false),
						],
					],
				],
				[
					'titre' => txt("Gestion SF2",false),
					'champs' => [
						[
							'nom' => 'solde_budgetaire_sf2',
							'label' => txt('Solde budgétaire SF2 (octroi-financement)', false),
							'exportable' => 1,
							'format' => 'float',
							'editable' => 0,
							'formule' => 'prix_unitaire_nouveau * qte_source1',
							'descriptif' => txt('Si Coût partiel contrat révisé / remplacement (avant tx) = vide -> Coût partiel contrat octroyé / remplacement (avant tx) - Coût remplacement confirmé (avant tx). Si Coût partiel contrat révisé / remplacement (avant tx) = non vide -> Coût partiel contrat révisé / remplacement (avant tx) - Coût remplacement confirmé (avant tx)', false),
						],
						[
							'nom' => 'statut_financement_sf2',
							'label' => txt('Statut financement SF2', false),
							'exportable' => 1,
							'format' => 'int',
							'editable' => 1,
							'col_bd' => 'statut_financement_sf2',
							'descriptif' => txt('Menu déroulant personnalisable', false),
						],
					],
				],
				[
					'titre' => txt("Gestion SF3", false),
					'champs' => [
						[
							'nom' => 'solde_budgetaire_sf3',
							'label' => txt('Solde budgétaire SF3 (octroi-financement)', false),
							'exportable' => 1,
							'format' => 'float',
							'editable' => 0,
							'formule' => 'prix_unitaire_nouveau * qte_source1',
							'descriptif' => txt('Si Coût partiel contrat révisé / développement autre financement (avant tx) = vide -> Coût partiel contrat octroyé / développement autre financement (avant tx) - Coût développement autre financement confirmé (avant tx). Si Coût partiel contrat révisé / développement autre financement (avant tx) = non vide -> Coût partiel contrat révisé / développement autre financement (avant tx) - Coût développement autre financement confirmé (avant tx)', false),
						],
						[
							'nom' => 'statut_financement_sf3',
							'label' => txt('Statut financement SF3', false),
							'exportable' => 1,
							'format' => 'int',
							'editable' => 1,
							'col_bd' => 'statut_financement_sf3',
							'descriptif' => txt('Menu déroulant personnalisable', false),
						],
					],
				],
				[
					'titre' => txt("Gestion SF4", false),
					'champs' => [
						[
							'nom' => 'solde_budgetaire_sf4',
							'label' => txt('Solde budgétaire SF4 (octroi-financement)', false),
							'exportable' => 1,
							'format' => 'float',
							'editable' => 0,
							'formule' => 'prix_unitaire_nouveau * qte_source1',
							'descriptif' => txt('Si Coût partiel contrat révisé / développement autre financement (avant tx) = vide -> Coût partiel contrat octroyé / développement autre financement (avant tx) - Coût développement autre financement confirmé (avant tx). Si Coût partiel contrat révisé / développement autre financement (avant tx) = non vide -> Coût partiel contrat révisé / développement autre financement (avant tx) - Coût développement autre financement confirmé (avant tx)', false),
						],
						[
							'nom' => 'statut_financement_sf4',
							'label' => txt('Statut financement SF4', false),
							'exportable' => 1,
							'format' => 'int',
							'editable' => 1,
							'col_bd' => 'statut_financement_sf4',
							'descriptif' => txt('Menu déroulant personnalisable', false),
						],
					],
				],
				[
					'titre' => txt("Gestion SF5", false),
					'champs' => [
						[
							'nom' => 'solde_budgetaire_sf5',
							'label' => txt('Solde budgétaire SF5 (octroi-financement)', false),
							'exportable' => 1,
							'format' => 'float',
							'editable' => 0,
							'formule' => 'prix_unitaire_nouveau * qte_source1',
							'descriptif' => txt('Si Coût partiel contrat révisé / développement autre financement (avant tx) = vide -> Coût partiel contrat octroyé / développement autre financement (avant tx) - Coût développement autre financement confirmé (avant tx). Si Coût partiel contrat révisé / développement autre financement (avant tx) = non vide -> Coût partiel contrat révisé / développement autre financement (avant tx) - Coût développement autre financement confirmé (avant tx)', false),
						],
						[
							'nom' => 'statut_financement_sf5',
							'label' => txt('Statut financement SF5', false),
							'exportable' => 1,
							'format' => 'int',
							'editable' => 1,
							'col_bd' => 'statut_financement_sf5',
							'descriptif' => txt('Menu déroulant personnalisable', false),
						],
					],
				],
				[
					'titre' => "Projection budgétaire pour le projet/lot",
					'champs' => [
						[
							'nom' => 'cout_projete_total',
							'label' => txt('Coût projeté total', false),
							'exportable' => 1,
							'format' => 'float',
							'editable' => 0,
							'formule' => 'prix_unitaire_nouveau * qte_source1',
							'descriptif' => txt('Si Coût total contrat octroyé (av. tx) est non vide. 1- Si Coût total contrat révisé (av. tx) est vide -> Coût projeté total = Coût total contrat octroyé (av. tx) + Provision requise enveloppe développement MSSS dans le lot', false),
						],
						[
							'nom' => 'cout_projete_sf1',
							'label' => txt('Coût projeté SF1', false),
							'exportable' => 1,
							'format' => 'float',
							'editable' => 0,
							'col_bd' => 'cout_projete_sf1',
							'descriptif' => txt('', false),
						],
						[
							'nom' => 'cout_projete_sf2',
							'label' => txt('Coût projeté SF2', false),
							'exportable' => 1,
							'format' => 'float',
							'editable' => 0,
							'col_bd' => 'cout_projete_sf1',
							'descriptif' => txt('', false),
						],
						[
							'nom' => 'cout_projete_sf3',
							'label' => txt('Coût projeté SF3', false),
							'exportable' => 1,
							'format' => 'float',
							'editable' => 0,
							'col_bd' => 'cout_projete_sf1',
							'descriptif' => txt('', false),
						],
						[
							'nom' => 'cout_projete_sf4',
							'label' => txt('Coût projeté SF4', false),
							'exportable' => 1,
							'format' => 'float',
							'editable' => 0,
							'col_bd' => 'cout_projete_sf1',
							'descriptif' => txt('', false),
						],
						[
							'nom' => 'cout_projete_sf5',
							'label' => txt('Coût projeté SF5', false),
							'exportable' => 1,
							'format' => 'float',
							'editable' => 0,
							'col_bd' => 'cout_projete_sf1',
							'descriptif' => txt('', false),
						],
					]
				],
			],
		];
	}

    public static function traitementSauvegardeSupplementaire($id, $data, $is_ajout, $objAnciennesValeurs = null)
    {
        global $db;
        $sql = "update suivis_budgetaires_administrations set configuration = ? where id = ?";
        $db->query($sql, [json_encode(['champs' => $data['champs']]), $id]);
    }
}
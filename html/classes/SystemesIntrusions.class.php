<?php

class SystemesIntrusions extends FicheFormWrapper {
	private static $CLASS_NAME='SystemesIntrusions';
	const SQL_TABLE_NAME='systemes_intrusions';
	const TITRE='Systèmes d\'intrusion';
	public $id;
	public $fiche_id;
	public $equipement_systeme_intrusion_type_id;
	public $installation_type_id;
	public $qte_requise;
	public $commentaires;
    
    public static function getChampsSaisieMetaData()
    {
        return array(
            
            'equipement_systeme_intrusion_type' => array(
                'classe'      	=> 'EquipementsSystemesIntrusionsTypes',
                'table'      	=> 'equipements_systemes_intrusions_types',
                'champ_id'      => 'equipement_systeme_intrusion_type_id',
                'type'          => 'select',
                'format'        => 'int',
                'titre'         => txt('Type d\'équipement', false),
                'css_editeur'   => 'large',
                'obligatoire'	=> 1,
            ),
            
            'installation_type' => array(
				'classe'      	=> 'InstallationsTypes',
				'table'      	=> 'installations_types',
                'champ_id'      => 'installation_type_id',
                'type'          => 'select',
                'format'        => 'int',
                'titre'         => txt('Installation', false),
                'css_editeur'   => 'average'
            ),
            
            'qte_requise' => array(
				'classe'      	=> '',
				'table'      	=> '',
				'champ_id'      => 'qte_requise',
				'type'          => 'input',
				'format'        => 'int',
				'titre'         => txt('Quantité', false),
				'css_editeur'   => 'tiny'
			),
            
            'commentaires' => array(
				'table'      	=> '',
				'champ_id'      => 'commentaires',
				'type'          => 'input',
                'format'        => 'text',
                'titre'         => txt('Commentaires', false),
                'css_editeur'   => 'xxlarge'
            ),
            
        );
    }

	public static function getListe($filter = array(), $orderBy = 'ordre', $extra_data = array())
	{
        $extra_data = array(
            'equipement_systeme_intrusion_type' => array('table' => 'equipements_systemes_intrusions_types'),
            'installation_type' => array('table' => 'installations_types'),
            
        );
        return parent::getListe($filter, $orderBy, $extra_data);
    }
}

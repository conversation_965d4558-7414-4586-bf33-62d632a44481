<?php

class UniAccessoiresProtectionsIncendies extends FicheForm<PERSON>rapper {
	private static $CLASS_NAME='UniAccessoiresProtectionsIncendies';
	const SQL_TABLE_NAME='uni_accessoires_protections_incendies';
	const TITRE='(D4030) Accessoires de protection incendie';
	public $id;
	public $fiche_id;
	public $accessoire_protection_incendie_type_id;
	public $qte_requise;
	public $accessoire_protection_incendie_type;
	public $quantite;
	public $commentaires;
    
    public static function getChampsSaisieMetaData()
    {
        return array(
            
            'accessoire_protection_incendie_type' => array(
                'classe'      	=> 'UniAccessoiresProtectionsIncendiesTypes',
                'table'      	=> 'uni_accessoires_protections_incendies_types',
                'champ_id'      => 'accessoire_protection_incendie_type_id',
                'type'          => 'select',
                'format'        => 'int',
                'titre'         => txt('Type d\'accessoire de protection incendie', false),
                'css_editeur'   => 'large',
                'obligatoire'	=> 1,
            ),
            
            'qte_requise' => array(
				'classe'      	=> '',
				'table'      	=> '',
				'champ_id'      => 'qte_requise',
				'type'          => 'input',
				'format'        => 'int',
				'titre'         => txt('Quantité', false),
				'css_editeur'   => 'tiny'
			),
            
            'commentaires' => array(
				'table'      	=> '',
				'champ_id'      => 'commentaires',
				'type'          => 'input',
                'format'        => 'text',
                'titre'         => txt('Commentaires', false),
                'css_editeur'   => 'xxlarge'
            ),
            
        );
    }

	public static function getListe($filter = array(), $orderBy = 'ordre', $extra_data = array())
	{
        $extra_data = array(
            'accessoire_protection_incendie_type' => array('table' => 'uni_accessoires_protections_incendies_types'),
            
        );
        return parent::getListe($filter, $orderBy, $extra_data);
    }
}

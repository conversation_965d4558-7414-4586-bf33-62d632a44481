<?php

class UniExigencesFonctionnelles extends FicheFormWrapper {
	private static $CLASS_NAME='UniExigencesFonctionelles';
	const SQL_TABLE_NAME='uni_exigences_fonctionnelles';
	const TITRE='(section 03.02) Exigences fonctionnelles';
	public $id;
	public $fiche_id;
	public $projet_id;
	public $exigence_fonctionnelle_type_id;
	public $qantite_id;
	public $local;
	public $local_id;
	public $commentaires;
	public $ordre;
	public $status;

    
    public static function getChampsSaisieMetaData()
    {
        return array(
            
            'exigence_fonctionnelle_type' => array(
                'classe'      	=> 'UniExigencesFonctionnellesTypes',
                'table'      	=> 'uni_exigences_fonctionnelles_types',
                'champ_id'      => 'exigence_fonctionnelle_type_id',
                'type'          => 'select',
                'format'        => 'int',
                'titre'         => txt('Type d\'exigence', false),
                'css_editeur'   => 'large',
                'obligatoire'	=> 1,
            ),
            
            
            'local' => array(
                'classe'      	=> 'Fiches',
                'table'      	=> 'fiches',
                'champ_id'      => 'local_id',
                'type'          => 'select',
                'format'        => 'int',
                'titre'         => txt('Local', false),
                'css_editeur'   => 'large',
                'sql_for_nom'   => 'code',
            
            ),
            
            'commentaires' => array(
				'table'      	=> '',
				'champ_id'      => 'commentaires',
				'type'          => 'input',
                'format'        => 'text',
                'titre'         => txt('Commentaires', false),
                'css_editeur'   => 'xxlarge'
            ),
            
        );
    }

	public static function getListe($filter = array(), $orderBy = 'ordre', $extra_data = array())
	{
        $extra_data = array(
            'exigence_fonctionnelle_type' => array('table' => 'uni_exigences_fonctionnelles_types'),
            'local' => array('table' => 'fiches'),
        );
        return parent::getListe($filter, $orderBy, $extra_data);
    }
}

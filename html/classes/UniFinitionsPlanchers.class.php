<?php

class UniFinitionsPlanchers extends FicheFormWrapper {
	private static $CLASS_NAME='UniFinitionsPlanchers';
	const SQL_TABLE_NAME='uni_finitions_planchers';
	const TITRE='(C3020) Finitions des planchers';
	public $id;
	public $fiche_id;
	public $finition_plancher_type_id;
	public $pourcentage_id;
	public $finition_plancher_type;
	public $pourcentage;
	public $commentaires;
    
    public static function getChampsSaisieMetaData()
    {
        return array(
            
            'finition_plancher_type' => array(
                'classe'      	=> 'UniFinitionsPlanchersTypes',
                'table'      	=> 'uni_finitions_planchers_types',
                'champ_id'      => 'finition_plancher_type_id',
                'type'          => 'select',
                'format'        => 'int',
                'titre'         => txt('Type de finition de plancher', false),
                'css_editeur'   => 'large',
                'obligatoire'	=> 1,
            ),
            
            'pourcentage' => array(
				'classe'      	=> 'Pourcentages',
				'table'      	=> 'pourcentages',
                'champ_id'      => 'pourcentage_id',
                'type'          => 'select',
                'format'        => 'int',
                'titre'         => txt('Pourcentage (%)', false),
                'css_editeur'   => 'small'
            ),
            
            'commentaires' => array(
				'table'      	=> '',
				'champ_id'      => 'commentaires',
				'type'          => 'input',
                'format'        => 'text',
                'titre'         => txt('Commentaires', false),
                'css_editeur'   => 'xxlarge'
            ),
            
        );
    }

	public static function getListe($filter = array(), $orderBy = 'ordre', $extra_data = array())
	{
        $extra_data = array(
            'finition_plancher_type' => array('table' => 'uni_finitions_planchers_types'),
            'pourcentage' => array('table' => 'pourcentages'),
        );
        return parent::getListe($filter, $orderBy, $extra_data);
    }
}

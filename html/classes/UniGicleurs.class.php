<?php

class UniGicleurs extends FicheFormWrapper {
	private static $CLASS_NAME='UniGicleurs';
	const SQL_TABLE_NAME='uni_gicleurs';
	const TITRE='(D4010) Gicleurs';
	public $id;
	public $fiche_id;
	public $gicleur_type_id;
	public $qte_requise;
	public $gicleur_type;
	public $quantite;
	public $commentaires;
    
    public static function getChampsSaisieMetaData()
    {
        return array(
            
            'gicleur_type' => array(
                'classe'      	=> 'UniGicleursTypes',
                'table'      	=> 'uni_gicleurs_types',
                'champ_id'      => 'gicleur_type_id',
                'type'          => 'select',
                'format'        => 'int',
                'titre'         => txt('Type de gicleurs', false),
                'css_editeur'   => 'large',
                'obligatoire'	=> 1,
            ),
            
            'qte_requise' => array(
				'classe'      	=> '',
				'table'      	=> '',
				'champ_id'      => 'qte_requise',
				'type'          => 'input',
				'format'        => 'int',
				'titre'         => txt('Quantité', false),
				'css_editeur'   => 'tiny'
			),
            
            'commentaires' => array(
				'table'      	=> '',
				'champ_id'      => 'commentaires',
				'type'          => 'input',
                'format'        => 'text',
                'titre'         => txt('Commentaires', false),
                'css_editeur'   => 'xxlarge'
            ),
            
        );
    }

	public static function getListe($filter = array(), $orderBy = 'ordre', $extra_data = array())
	{
        $extra_data = array(
            'gicleur_type' => array('table' => 'uni_gicleurs_types'),
            
        );
        return parent::getListe($filter, $orderBy, $extra_data);
    }
}

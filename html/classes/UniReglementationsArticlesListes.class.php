<?
class UniReglementationsArticlesListes extends DbRow {
	public $id;
	public $reglementation_niveau_element_id;
	public $element;
	public $articles = array();

	protected function setDataForId($id)
	{
		global $db;

		$sql = "select t.*, u.nom as element
                from uni_reglementations_articles_listes t
                left join uni_reglementations_niveaux_elements u on t.reglementation_niveau_element_id = u.id ";
		$row = $db->getRow($sql, array($id));

		$this->populateThisWithThat($row);

		$sql = "select *
                from uni_reglementations_articles
                where uni_reglementation_article_liste_id = ?
                order by ordre";
		$articles = $db->getAll($sql, array($id));

		$this->articles = isset($articles) ? $articles : array();
	}

	public static function getListe($filter = array())
	{
		global $db;

		$sql = "select t.*, t.id, u.nom as element
                from uni_reglementations_articles_listes t
                left join uni_reglementations_niveaux_elements u on t.reglementation_niveau_element_id = u.id ";
		$rows = $db->getAssoc($sql, []);

		$articles = array();
		$ids = array_keys($rows);

		if(count($ids) > 0)
		{
			$sql = "select *
                    from uni_reglementations_articles
                    where uni_reglementation_article_liste_id in (".db::placeHolders($ids).")
                    order by ordre";
			$articles_tmp = $db->getAll($sql, $ids);

			foreach($articles_tmp as $row_col)
			{
				$articles[intval($row_col['uni_reglementation_article_liste_id'])][] = $row_col;
			}
		}

		$liste = array();
		foreach($rows as $i => $row)
		{
			$row['articles'] = isset($articles[intval($row['id'])]) ? $articles[intval($row['id'])] : array();
			$liste[intval($row['id'])] = new self(intval($row['id']), $row);
		}

		return $liste;
	}

	public function getEditorHTML($iteration = 0, $only_champs_denormalises = false)
	{
	    global $db;
		?>
        <script>
            $(document).ready(function(){
				$('#blocFiltre').on('change', 'select.niveaux', function(){

					let _that = this;
					setTimeout(function(){
						changeNiveau(_that);
					}, 100);

				});
            });



			function changeNiveau(obj)
			{
				var niveau_enfant_id = $(obj).data('niveau_enfant_id');

				while($('#niveau_' + niveau_enfant_id).length)
				{
					$('#niveau_' + niveau_enfant_id).val('');
					$('#niveau_' + niveau_enfant_id).find('.all').replaceTagName('span').hide();
					niveau_enfant_id = $('#niveau_' + niveau_enfant_id).data('niveau_enfant_id');
				}

				var parent_element_id = 0;
				$('#blocFiltre').find('select.niveaux').each(function(){

					if(this.value.length)
					{
						parent_element_id = this.value;
					}

				});

				var niveau_enfant_id = $(obj).data('niveau_enfant_id');

				while($('#niveau_' + niveau_enfant_id).length)
				{
					$('#niveau_' + niveau_enfant_id).val('');
					$('#s2id_' + $('#niveau_' + niveau_enfant_id).attr('id')).find('.select2-chosen').html($('#niveau_' + niveau_enfant_id).find('option:first-child').text());

					if(parent_element_id == 0)
					{
						$('#niveau_' + niveau_enfant_id).find('.all').replaceTagName('option').show();
					}
					else
					{
						$('#niveau_' + niveau_enfant_id).find('.parent_niveau_id_' + parent_element_id).replaceTagName('option').show();
					}

					$('#niveau_' + niveau_enfant_id).val('');

					niveau_enfant_id = $('#niveau_' + niveau_enfant_id).data('niveau_enfant_id');
				}
			}
        </script>
		<section>
            <div id="blocFiltre" style="padding: 10px 0 0 20px; width: 650px; float: left; max-height: 800px; overflow-y: auto; overflow-x: hidden;">

                <b style="font-weight: bold;"><?=txt('Filtres par structure hiérarchique:')?></b><br /><br />
				<?php
				$niveaux = array();
				$niveaux_tmp = UniReglementationsNiveaux::getListe(array());

				if(count($niveaux_tmp) > 0)
				{
					$i = 0;
					foreach($niveaux_tmp as $niveau)
					{
						$niveaux[$i++] = $niveau;
						if($i > 3)
						{
							break;
						}
					}
				}

				$nb_niveaux = count($niveaux);
				if($nb_niveaux > 0)
				{
					foreach($niveaux as $i => $niveau)
					{
						?>
                        <div style="width: 650px; margin-bottom: 5px;">
                            <span style="width: 120px; display: inline-block;"><?=$niveau->nom?></span>
                            <select id="niveau_<?=$niveau->id?>" name="reglementation_niveau_element_id[<?=$niveau->id?>]" class="niveaux" data-niveau_id="<?=$niveau->id?>" style="width: 340px;"
                                    data-niveau_parent_id="<?=(isset($niveaux[($i-1)]) ? $niveaux[($i-1)]->id : -1)?>"
                                    data-niveau_enfant_id="<?=(isset($niveaux[($i+1)]) ? $niveaux[($i+1)]->id : -1)?>">
                                <option value=""> </option>
								<?
								foreach($niveau->uni_reglementations_niveaux_elements as $j => $element)
								{
									$sel = ($element['id'] == $this->reglementation_niveau_element_id ? ' selected="selected"' : '');

									$display = '';
									$tag = '';
									$classes = array();
									if(isset($element['reglementation_niveau_element_id']))
									{
										$parent = $niveau->uni_reglementations_niveaux_elements_precedants[$element['reglementation_niveau_element_id']];
										foreach($parent['ids'] as $id)
										{
											$classes[] = 'parent_niveau_id_'.$id;
										}
									}

									$suffix = $i > 0 ? ' ('.$parent['nom'].')' : '';
									?>
                                    <option value="<?=$element['id']?>" data-nom="<?=$element['nom']?>" style="display: <?=$display?>;" class="all <?=$tag?> <?=dm_implode(' ', $classes)?>"<?=$sel?>><?=$element['nom'].$suffix?></option>
									<?
								}
								?>
                            </select>
                        </div>
						<?php
					}
				}
				?>
		</section>

		<table style="margin-bottom: 10px; width: 900px; margin-left: 50px;" class="formFieldsTable" id="table_colonnes">
			<thead>
			<tr id="projets_etapes_titre">
				<td style="font-weight: bold; width: 60px; text-align: center">
					&nbsp;
				</td>
				<td style="font-weight: bold; width: 155px; text-align: center!important;">
					<?=txt('Type d\'article')?>
				</td>
				<td style="font-weight: bold; width: 250px; text-align: center!important;">
					<?=txt('Regroupement')?>
				</td>
				<td style="font-weight: bold; width: 255px; text-align: center!important;">
					<?=txt('Article')?>
				</td>
			</tr>
			</thead>
			<tbody id="uni_reglementations_articles_listes_colonnes" data-cur_iteration="<?=(count($this->articles) + 1)?>">
			<?
			if(isset($this->articles) && is_array($this->articles))
			{
				if(count($this->articles) == 0)
				{
					$this->articles[] = array('id' => '0', 'uni_reglementation_article_liste_id' => 0, 'reglementation_type_id' => 0, 'nom_regrouppement' => '', 'article' => '');
				}
				foreach($this->articles as $i => $article)
				{
					?>
					<tr class="top_block editable">
						<td>

                            <span class="delSortBtns">
                                <input type="hidden" name="nothing[]" value="" />
                                <a href="javascript: void(0);" class="sort">
                                    <img src="css/images/icons/dark/triangle_up_down.png" /></a>

                                <a href="javascript: void(0);" class="delete">
                                    <img src="css/images/icons/dark/trashcan.png" /></a>

                                <input type="hidden" name="element_id[]" value="<?=$article['id']?>" class="element_id" />
                            </span>

						</td>
						<td style=" text-align: center!important;">
							<select name="reglementation_type_id[]" class="reglementation_type_id" style="width: 140px;min-width: 140px!important;">
								<option value=""> </option>
								<?
                                $sql = "select * from uni_reglementations_types order by nom";
                                $types = $db->getAll($sql, []);
                                foreach ($types as $type) {
                                    ?>
                                    <option value="<?=$type['id']?>"<?=($type['id'] == $article['reglementation_type_id'] ? ' selected="selected"' : '')?>><?=$type['nom']?></option>
                                    <?
                                }
                                ?>
							</select>
						</td>
                        <td style=" text-align: center!important;">
                            <input type="text" name="nom_regrouppement[]" class="nom_regrouppement" value="<?=$article['nom_regrouppement']?>" style="width: 200px;min-width: 200px!important;" />
                        </td>
                        <td style=" text-align: center!important;">
                            <textarea name="article[]" class="article" style="width: 400px;min-width: 400px!important; height: 16px; position: relative; top: 1px;"><?=$article['article']?></textarea>
                        </td>
					</tr>
					<?
				}
			}
			?>
			</tbody>
			<tfoot>
			<tr class="top_block">
				<td>
					<a href="javascript: void(0);" class="add_line" style="">
						<img src="css/images/icons/dark/plus.png" /></a>
				</td>
				<td>
					&nbsp;
				</td>
				<td>
					&nbsp;
				</td>
			</tr>
			</tfoot>
		</table>
		<?
	}

	public static function getDynamicTableTitle()
	{
		?>
		{ "sTitle": "<?=txt('Niveau')?>" },
		<?
	}

	public function getDynamicTableContent()
	{
		?>
		"<?=dm_addslashes($this->getRaw('element'))?>"
		<?
	}

	public static function delete($id)
	{
		global $db;
		$db->autocommit(false);
		$db->query("DELETE FROM `uni_reglementations_articles` WHERE uni_reglementation_article_liste_id = ?", array($id));
		$db->query("DELETE FROM `uni_reglementations_articles_listes` WHERE id = ?", array($id));
		$db->commit();
	}

	public static function validate($data, $id)
	{
		$valide = true;
		foreach($data as $colomnName => $value)
		{
			if($colomnName == 'nom')
			{
				if(dm_strlen($value) == 0)
				{
					setErreur(txt('Le nom est obligatoire.'));
					$valide = false;
				}
			}
		}
		return $valide;
	}

    public static function traitementSauvegardeSupplementaire($id, $requestData, $is_ajout)
    {
        global $db;
        $sql = "select id FROM uni_reglementations_articles WHERE uni_reglementation_article_liste_id = ?";
        $ids_to_delete_tmp = $db->getAll($sql, array($id));

        $ids_to_delete = array();
        foreach ($ids_to_delete_tmp as $row_tmp) {
            $ids_to_delete[intval($row_tmp['id'])] = intval($row_tmp['id']);
        }

        if (isset($requestData['element_id']) && count($requestData['element_id']) > 0) {
            $ordre = 1;
            foreach ($requestData['element_id'] as $i => $element_id) {
                if ($element_id > 0) {
                    $sql = "update uni_reglementations_articles set uni_reglementation_article_liste_id = ?, reglementation_type_id = ?, nom_regrouppement = ?, article = ?, ordre = ? where id = ?";
                    $db->query($sql, array($id, $requestData['reglementation_type_id'][$i], $requestData['nom_regrouppement'][$i], $requestData['article'][$i], $ordre, $element_id));
                    unset($ids_to_delete[$element_id]);
                } else {
                    $sql = "INSERT INTO uni_reglementations_articles (uni_reglementation_article_liste_id, reglementation_type_id, nom_regrouppement, article, ordre) 
                                    VALUES (?, ?, ?, ?, ?)";
                    $db->query($sql, array($id, $requestData['reglementation_type_id'][$i], $requestData['nom_regrouppement'][$i], $requestData['article'][$i], $ordre));
                }
                $ordre++;
            }
        }
        if (count($ids_to_delete) > 0 && intval($requestData['do_not_delete']) == 0) {
            $sql = "delete FROM uni_reglementations_articles WHERE id in (" . dm_implode(', ', $ids_to_delete) . ")";
            $db->query($sql, array($id));
        }
    }
}
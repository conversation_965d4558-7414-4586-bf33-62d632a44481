<?php
class Unite extends DbRow {
	public $id;
    public $unite_type_id;
    public $unite_type;
	public $nom;
	public $isValide;
	public $code_client_ajout;
	public $usager_ajout_id;
	public $ts_ajout;
	public $code_ajout;
	public $ts_valide;
	public $note_ajout;
	public $ts_sync;
	public $ts_delete;
	
	protected function setDataForId($id)
	{
        global $db;
        
		$sql = "select unites.*, unites_types.nom as unite_type,
				case when unites.ts_valide is null then 0 else 1 end as is<PERSON>alide
                from unites 
                left join unites_types on unites_types.id = unite_type_id
                where unites.id = ?";
		$row = $db->getRow($sql, array($id));
		
		$this->populateThisWithThat($row);
	}
	
	public static function getListe($filter = array(), $orderBy = 'unites.nom')
	{
        global $db;
        
        $where = '';
        $data = array();        
        
        if(isset($filter['unite_type_id']))
        {
            $where .= " and unite_type_id = ?";
            $data[] = $filter['unite_type_id'];
        }
        
		$sql = "select unites.*, unites_types.nom as unite_type,
				case when unites.ts_valide is null then 0 else 1 end as isValide
                from unites 
                left join unites_types on unites_types.id = unite_type_id
                where true $where
                order by $orderBy";
		$rows = $db->getAll($sql, $data);
		
		$liste = array();
		foreach($rows as $i => $row)
		{
			$liste[intval($row['id'])] = new self(intval($row['id']), $row);
		}
		
		return $liste;
	}
	
    public function getEditorHTML($iteration = 0, $only_champs_denormalises = false)
	{
?>			
		<section>
            <label for="nom"><?php print txt('Nom'); ?></label>
            <div>
                <input id="nom" type="text" name="nom" value="<?php print ($this->nom); ?>" />
            </div>
        </section>
		<section>
            <label for="unite_type_id"><?php print txt('Type'); ?></label>
            <div>
                <select id="unite_type_id" name="unite_type_id">
                    <?=UniteType::getOptions(array(), $this->unite_type_id)?>
                </select>
            </div>
        </section>
<?php
	}
	
	public static function getDynamicTableTitle()
	{
?>
		{ "sTitle": "<?php print txt('Nom'); ?>", "sClass": "dataTableLargetTitle" },
		{ "sTitle": "<?php print txt('Type'); ?>", "sClass": "dataTableLargetTitle" },
<?php
	}
	
	public function getDynamicTableContent()
	{
?>
		"<?php print dm_addslashes($this->getRaw('nom')); ?>",
		"<?php print dm_addslashes($this->getRaw('unite_type')); ?>"
<?php
	}
	
	public static function getOptions($filter = array(), $selected = 0)
	{
        $rows = self::getListe($filter, 'unites.nom');
        foreach($rows as $i => $row)
        {
            $sel = $row->id == $selected ? ' selected="selected"' : '';
?>
            <option value="<?php print $row->id; ?>"<?php print $sel; ?>><?php print $row->nom; ?></option>
<?php
        }
	}
    
    public static function delete($id)
	{
        global $db;
        $db->autocommit(false);
        $db->query("DELETE FROM `unites` WHERE id = ?", array($id));
        $db->commit();
	}
    
    public static function validate($data, $id)
	{
        $valide = true;
        foreach($data as $colomnName => $value)
        {
            if($colomnName == 'nom')
            {
                if(dm_strlen($value) == 0)
                {
                    setErreur(txt('Le nom est obligatoire.'));
                    $valide = false;
                }
            }
        }
        return $valide;
	}
}
?>
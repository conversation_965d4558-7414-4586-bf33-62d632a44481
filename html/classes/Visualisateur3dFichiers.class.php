<?php

class Visualisateur3dFichiers extends Gestionnaire implements GestionnaireInterface
{
    use GestionnairePage;

    const SQL_TABLE_NAME = 'visualisateur3d_fichiers';

    public int $id = 0;
    public string $nom = '';
    public string $nom_fichier = '';
    public string $mime_type = '';
    public int $is_plancher_focus = 1;
    public string $mode_tree = 'containment';
    public string $type_source = 'xkt';
    public int $size = 0;
    public array $fiches = [];

    public static function getChampsSaisieMetaData(): array
    {
        return [
            'nom' => [
                'classe'        => '',
                'table'      	=> '',
                'champ_id'      => 'nom',
                'type'          => 'input',
                'format'        => 'text',
                'titre'         => txt('Nom', false),
                'css_editeur'   => 'large'
            ],
            'nom_fichier' => [
                'classe'        => '',
                'table'      	=> '',
                'champ_id'      => 'nom_fichier',
                'type'          => 'input',
                'format'        => 'text',
                'titre'         => txt('Fichier', false),
            ],
            'mime_type' => [
                'classe'        => '',
                'table'      	=> '',
                'champ_id'      => 'mime_type',
                'type'          => 'input',
                'format'        => 'text',
                'titre'         => txt('Mime Type', false),
                'hidden'        => 1,
            ],
            'size' => [
                'classe'        => '',
                'table'      	=> '',
                'champ_id'      => 'size',
                'type'          => 'input',
                'format'        => 'text',
                'titre'         => txt('Poids', false),
            ],
        ];
    }

    public function setDataForId($id): void
    {
        parent::setDataForId($id);

        $sql = "select f.id, f.*, visualisateur3d_fichier_id, fiche_id
                from visualisateur3d_fichiers_fiches v
                join fiches f on v.fiche_id = f.id
                where v.visualisateur3d_fichier_id = ?";
        $this->fiches = db::instance()->getAll($sql, [$id]);
    }

    public static function getFiltreData($filter): array
    {
        $where = ' and projet_id = ?';
        $data = [getProjetId()];

        if (!empty($filter['fiche_id'])) {
            $where .= ' and exists (select id from visualisateur3d_fichiers_fiches vf where vf.visualisateur3d_fichier_id = v.id and vf.fiche_id = ?)';
            $data[] = $filter['fiche_id'];
        }

        return array($where, $data);
    }

    /**
     * @param $filter
     * @param $orderBy
     * @return Visualisateur3dFichiers[]
     */
    public static function getListe($filter, $orderBy = 'v.id'): array
    {
        list($where, $data) = self::getFiltreData($filter);

        $sql = "select v.id, v.*
                from visualisateur3d_fichiers v
                where true $where
                order by $orderBy";
        $rows = db::instance()->getAssoc($sql, $data);

        $ids = array_keys($rows);

        $liste = [];
        if (!empty($ids)) {
            $sql = "select f.id, f.*, visualisateur3d_fichier_id, fiche_id
                    from visualisateur3d_fichiers_fiches v
                    join fiches f on v.fiche_id = f.id
                    where v.visualisateur3d_fichier_id in (" . db::placeHolders($ids) . ")";
            $fichess_tmp = db::instance()->getAll($sql, $ids);

            $fiches = [];
            foreach ($fichess_tmp as $row_col) {
                $fiches[(int)$row_col['visualisateur3d_fichier_id']][(int)$row_col['fiche_id']] = new Fiches((int)$row_col['fiche_id'], $row_col);
            }

            foreach ($rows as $row) {
                $row['fiches'] = $fiches[(int)$row['id']] ?? [];
                $liste[(int)$row['id']] = new self((int)$row['id'], $row);
            }
        }
        return $liste;
    }

    public function getEditorHTML()
    {
        ?>
        <style>
            .blocsFiche, #fiche table, #fiche tr, #fiche td, #fiche th, .ui-dialog table, .ui-dialog tr, .ui-dialog td, .ui-dialog th {
                background: none!important;
            }
            .blocsFicheTitre {
                margin-top: 0px!important;
            }
            .span_mise_en_page {
                display: block!important;
            }
            .span_mise_en_page label {
                display: inline-block!important;
                background: none!important;
                position: relative;
                top: -6px;
                margin-bottom: 5px;
                font-size: 13px;
            }
            #blocDonnees select, #blocFiltre select {
                width: 260px!important;
            }
            div.comboselectbox.searchable div.combowrap:first-child ul.comboselect {
                height: 90%!important;
            }
            div.comboselectbox div.combowrap {
                padding: 10px!important;
                width: 175px!important;
            }
            li.used a.add {
                background-image: none!important;
            }
            #blocDonnees input, #blocDonnees textarea {
                width: 370px!important;
                box-sizing: border-box;
            }
        </style>
        <fieldset>

            <section>
                <label for="nom"><?=txt('Nom')?></label>
                <div>
                    <input type="text" id="nom" name="nom" value="<?=($this->nom)?>" />
                </div>
            </section>

            <section>
                <label for="is_plancher_focus"><?=txt('Activation des sols')?></label>
                <div>
                    <input type="checkbox" id="is_plancher_focus" name="is_plancher_focus" value="1" <?= $this->is_plancher_focus == 1 ? ' checked="checked"' : '' ?> />
                </div>
            </section>

            <section>
                <label for="nom"><?=txt('Mode de l\'arbre')?></label>
                <div>
                    <?
                    $options = [
                        'aucun' => txt('Aucun'),
                        'containment' => txt('Hiérarchique'),
                        'types' => txt('Par types'),
                        'stories' => txt('Par stories'),
                    ];
                    ?>
                    <select name="mode_tree">
                        <?
                        foreach ($options as $valeur => $texte) {
                            ?>
                            <option value="<?= $valeur ?>"<?= $this->mode_tree == $valeur ? ' selected="selected"' : '' ?>><?= $texte ?></option>
                            <?
                        }
                        ?>
                    </select>
                </div>
            </section>

            <section>
                <label for="nom"><?=txt('Fichier IFC')?></label>
                <div>
                    <input type="file" id="fichier_ifc" name="fichier_ifc" accept=".ifc" />
                </div>
            </section>

            <div id="fiches_parent" style="margin: 0 0 15px 30px; height: 525px; width: 750px; float: left; position: relative;">
                <?php
                $extra_filter = array('fiche_sous_type_id' => 2000002);
                Fiches::getMultiSelector(array_flip(array_column(($this->fiches ?? []), 'id')), '_visualisateur3d_fichiers', $extra_filter, true);
                ?>
            </div>

        </fieldset>
        <?php
    }

    public static function delete($id)
    {
        $sql = "delete from visualisateur3d_fichiers where id = ?";
        db::instance()->query($sql, [$id]);
    }

    public static function validate($data, $id)
    {
        $valide = true;
        foreach ($data as $colomnName => $value) {
            if ($colomnName == 'nom') {
                if (strlen($value) == 0) {
                    setErreur(txt('Le nom est obligatoire'));
                    $valide = false;
                }
            }
        }
        if (empty($data['listeFiches'])) {
            setErreur(txt('Vous devez sélectionner au moins 1 local.'));
            $valide = false;
        }
        return $valide;
    }

    public static function traitementSauvegardeSupplementaire($id, $requestData, $is_ajout, $objAnciennesValeurs = null): void
    {
        $sql = "DELETE FROM visualisateur3d_fichiers_fiches WHERE visualisateur3d_fichier_id = ?";
        db::instance()->query($sql, array($id));

        if (isset($requestData['listeFiches']) && count($requestData['listeFiches']) > 0) {
            foreach ($requestData['listeFiches'] as $fiche_id) {
                $sql = "INSERT INTO visualisateur3d_fichiers_fiches (visualisateur3d_fichier_id, fiche_id) 
                        VALUES (?, ?)";
                db::instance()->query($sql, array($id, $fiche_id));
            }
        }

        if (is_uploaded_file($_FILES['fichier_ifc']['tmp_name'])) {
            $path = '/var/www/temp/' . Config::instance()->codeClient . '_ifcs';
            if (!is_dir($path)) {
                mkdir($path, 0777, true);
            }
            move_uploaded_file($_FILES['fichier_ifc']['tmp_name'], $path . '/' . $id . '.ifc');

            if (Config::instance()->isDev) {
                $cmd = "node C:\\wamp64\\www\\xeokit-convert\\convert2xkt.js -s " . $path . '/' . $id . '.ifc' . " -o " . $path . '/' . $id . '.ifc' . ".xkt -l 2>&1";
            } else {
                $cmd = "node /var/www/xeokit-convert/convert2xkt.js -s " . $path . '/' . $id . '.ifc' . " -o " . $path . '/' . $id . '.ifc' . ".xkt -l 2>&1";
            }
            exec($cmd, $output, $ret);

            $typeSource = 'xkt';
            if ($ret <> 0) {
                setErreur(txt('Le fichier XKT n\'a pu être généré. Le IFC sera utilisé tel quel pour l\'affichage', false));
                $typeSource = 'ifc';
//                throw new Exception('Erreur lors du traitement '.$cmd.getDebug($output));
            }

            $sql = "update visualisateur3d_fichiers set nom_fichier = ?, mime_type = ?, size = ?, type_source = ? where id = ?";
            db::instance()->query($sql, [$_FILES['fichier_ifc']['name'], $_FILES['fichier_ifc']['type'], $_FILES['fichier_ifc']['size'], $typeSource, $id]);
        }

        $sql = "update visualisateur3d_fichiers set is_plancher_focus = ? where id = ?";
        db::instance()->query($sql, [($requestData['is_plancher_focus'] ?? 0), $id]);
    }

    public function traitementApresSubmit($requestData): void
    {
        if (!empty($requestData['fiches']) > 0) {
            $this->fiches = [];
            foreach ($requestData['fiches'] as $fiche_id) {
                $this->fiches[$fiche_id] = new Fiches($fiche_id);
            }
        }
    }

    /**
     * @return GestionnaireInterface
     */
    public static function getClasseContexte()
    {
        /** @var GestionnaireInterface $currentClassName */
        $currentClassName = get_called_class();
        return $currentClassName;
    }

    public static function getTitre()
    {
        return txt('Gestion des fichiers IFC');
    }

    public static function getListeForDatatable($modeSelection = false): array
    {
        $filtre = &$_SESSION['filtresGestionnairePage_' . self::getClasseContexte()];
        $liste = self::getListe($filtre);

        $listeForDatatable = [];
        foreach ($liste as $row) {
            $fiches = count($row->fiches) > 5 ? implode(', ', array_slice(array_column($row->fiches, 'code'), 0, 5)) . '...' : implode(', ', array_column($row->fiches, 'code'));
            $listeForDatatable[] = [
                $row->nom,
                '<a href="index.php?action=download_fichier_ifc&id=' . $row->id . '" target="_blank">
                        ' . $row->nom_fichier . ' (' . FileSizeConvert($row->size) . ')</a>',
                !is_file('/var/www/temp/' . Config::instance()->codeClient . '_ifcs/' . $row->id . '.ifc.xkt') ? '' : '<a href="index.php?action=download_fichier_xkt&id=' . $row->id . '" target="_blank">
                        ' . $row->nom_fichier . '.xkt (' . FileSizeConvert(filesize('/var/www/temp/' . Config::instance()->codeClient . '_ifcs/' . $row->id . '.ifc.xkt')) . ')</a>',
                $fiches,
                <<<HTML
                    <a href="index.php?section=admin&module=edit&table=visualisateur3d_fichiers&type=client&id={$row->id}&from_gestionnaire=1" class="fancy">
                        <img src="css/images/icons/dark/create_write.png"></a>
                    <a class="dle_btn dle_delete_generique" tableName="visualisateur3d_fichiers" idElement="{$row->id}">
                        <img src="css/images/icons/dark/trashcan.png" /></a>
HTML
            ];
        }
        return $listeForDatatable;
    }

    public static function getAutresOptions(): array
    {
        return [];
    }

    public static function getOngletData(): array
    {
        return [];
    }

    public static function getListeFiltres()
    {
        return [];
    }

    public static function getListeColonnes($modeSelection = false): array
    {
        return [
            'nom' => [
                'titre' => txt('Nom', false),
                'isData' => true
            ],
            'nom_fichier_ifc' => [
                'titre' => txt('IFC', false),
                'isData' => true
            ],
            'nom_fichier_xkt' => [
                'titre' => txt('XKT', false),
                'isData' => true
            ],
            'fiches' => [
                'titre' => txt('Locaux', false),
                'isData' => true
            ],
            'boutons' => [
                'titre' => '<a href="index.php?section=admin&module=edit&id=0&table=visualisateur3d_fichiers&type=client&from_gestionnaire=1" class="fancy" title="' . txt('Ajouter un fichier IFC') . '">
                                <img src="css/images/icons/dark/plus.png"></a>',
                'isData' => false
            ],
        ];
    }
}
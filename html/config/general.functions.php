<?php
require_once __DIR__ . '/../../vendor/autoload.php';

use LdapRecord\Connection;
use PhpOffice\PhpSpreadsheet\Calculation\Calculation;
use PhpOffice\PhpSpreadsheet\Cell\Cell;
use PhpOffice\PhpSpreadsheet\Cell\Coordinate;
use PhpOffice\PhpSpreadsheet\Cell\DataType;
use PhpOffice\PhpSpreadsheet\Cell\DefaultValueBinder;
use PhpOffice\PhpSpreadsheet\Cell\IValueBinder;
use PhpOffice\PhpSpreadsheet\IOFactory;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;
use PhpOffice\PhpSpreadsheet\Writer\Csv;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use OpenSpout\Common\Entity\Cell as OpenSpoutCell;
use OpenSpout\Common\Entity\Row;
use Symfony\Component\Validator\Context\ExecutionContextInterface;

/**
 * @param $str
 */
function printDebug($str)
{
	print "<pre>";
	print_r($str);
	print "</pre>";
    print "<script>if ($.fancybox) parent.fancybox_resize();</script>";
}

/**
 * @param string $sql
 * @param array $data
 */
function printSql(string $sql, array $data)
{
    printDebug((db::interpolateQuery($sql, $data)));
}

/**
 * @param string $sql
 * @param array $data
 */
function getSql(string $sql, array $data)
{
    return getDebug((db::interpolateQuery($sql, $data)));
}

//test
/**
 * @param $str
 * @return false|string
 */
function getDebug($str)
{
	ob_start();
	print "<pre>";
	print_r($str);
	$printed = ob_get_contents();
	print "</pre>";
	ob_end_clean();
	
	return $printed;
}

/**
 * @param $dec
 * @return array
 */
function decimalHourToTime($dec)
{
    // start by converting to seconds
    $seconds = ($dec * 3600);
    // we're given hours, so let's get those the easy way
    $hours = floor($dec);
    // since we've "calculated" hours, let's remove them from the seconds variable
    $seconds -= $hours * 3600;
    // calculate minutes left
    $minutes = floor($seconds / 60);
    // remove those from seconds as well
    $seconds -= $minutes * 60;
    // return the time formatted HH:MM:SS
    return array($hours, $minutes, $seconds);
}

/**
 * @param $frenchTxt
 * @param bool $htmled
 * @return string
 */
function txt($frenchTxt, $htmled = true, $replaces = [])
{
	global $Langue, $Textes, $texte_key_cache;

	$cache_key = uniqueKey($frenchTxt);
	if (isset($texte_key_cache[$cache_key])) {
		$key = $texte_key_cache[$cache_key];
	} else {
		$key = text_to_key($frenchTxt);
		$texte_key_cache[$cache_key] = $key;
	}

	$returnTxt = $frenchTxt;

	init_pseudos();

	if (isset($_SESSION['pseudos'][getProjetId()][$key][$Langue])) {
		$returnTxt = $_SESSION['pseudos'][getProjetId()][$key][$Langue];
	} else if ($Langue == 'en') {
		if (isset($Textes[$key])) {
			$returnTxt = $Textes[$key];
		} else {
			//mb_send_mail('<EMAIL>,<EMAIL>', 'Nouveau texte à traduire', '$Textes["'.$key.'"] = "'.$returnTxt.'";');
			$_SESSION['texts'][$key] = $frenchTxt;
		}
	}

    if (!empty($replaces)) {
        $returnTxt = str_replace(array_keys($replaces), array_values($replaces), $returnTxt);
    }

	return $htmled ? dm_htmlentities($returnTxt, ENT_QUOTES, "UTF-8") : $returnTxt;
}

/**
 * @param bool $reset
 */
function init_pseudos($reset = false)
{
	global $db;

	if ($reset) {
		unset($_SESSION['pseudos'][getProjetId()]);
	}

	if (getProjetId() > 0 && !isset($_SESSION['pseudos'][getProjetId()])) {
		$sql = "select * 
                from pseudos 
                join pseudos_projets on liste_item_id = pseudos.id
                where projet_id = ?";
		$pseudos_tmp = db::instance()->getAll($sql, array(getProjetId()));

		foreach ($pseudos_tmp as $i => $pseudo) {
			$_SESSION['pseudos'][getProjetId()][text_to_key($pseudo->getRaw('nom'))]['fr'] = $pseudo["label_fr"];
			$_SESSION['pseudos'][getProjetId()][text_to_key($pseudo->getRaw('nom'))]['en'] = $pseudo["label_en"];
		}
	}
}

/**
 * @param $texte
 * @return string
 */
function text_to_key($texte)
{
    return strtolower(dm_str_replace(' ', '_', dm_trim(dm_substr(dm_preg_replace("/[^a-zA-Z0-9 ]+/", "", transliterateString($texte)), 0, 75))));
}

/**
 * @param $txt
 * @return mixed
 */
function transliterateString($txt)
{
    $transliterationTable = array('á' => 'a', 'Á' => 'A', 'à' => 'a', 'À' => 'A', 'ă' => 'a', 'Ă' => 'A', 'â' => 'a', 'Â' => 'A', 'å' => 'a', 'Å' => 'A', 'ã' => 'a', 'Ã' => 'A', 'ą' => 'a', 'Ą' => 'A', 'ā' => 'a', 'Ā' => 'A', 'ä' => 'ae', 'Ä' => 'AE', 'æ' => 'ae', 'Æ' => 'AE', 'ḃ' => 'b', 'Ḃ' => 'B', 'ć' => 'c', 'Ć' => 'C', 'ĉ' => 'c', 'Ĉ' => 'C', 'č' => 'c', 'Č' => 'C', 'ċ' => 'c', 'Ċ' => 'C', 'ç' => 'c', 'Ç' => 'C', 'ď' => 'd', 'Ď' => 'D', 'ḋ' => 'd', 'Ḋ' => 'D', 'đ' => 'd', 'Đ' => 'D', 'ð' => 'dh', 'Ð' => 'Dh', 'é' => 'e', 'É' => 'E', 'è' => 'e', 'È' => 'E', 'ĕ' => 'e', 'Ĕ' => 'E', 'ê' => 'e', 'Ê' => 'E', 'ě' => 'e', 'Ě' => 'E', 'ë' => 'e', 'Ë' => 'E', 'ė' => 'e', 'Ė' => 'E', 'ę' => 'e', 'Ę' => 'E', 'ē' => 'e', 'Ē' => 'E', 'ḟ' => 'f', 'Ḟ' => 'F', 'ƒ' => 'f', 'Ƒ' => 'F', 'ğ' => 'g', 'Ğ' => 'G', 'ĝ' => 'g', 'Ĝ' => 'G', 'ġ' => 'g', 'Ġ' => 'G', 'ģ' => 'g', 'Ģ' => 'G', 'ĥ' => 'h', 'Ĥ' => 'H', 'ħ' => 'h', 'Ħ' => 'H', 'í' => 'i', 'Í' => 'I', 'ì' => 'i', 'Ì' => 'I', 'î' => 'i', 'Î' => 'I', 'ï' => 'i', 'Ï' => 'I', 'ĩ' => 'i', 'Ĩ' => 'I', 'į' => 'i', 'Į' => 'I', 'ī' => 'i', 'Ī' => 'I', 'ĵ' => 'j', 'Ĵ' => 'J', 'ķ' => 'k', 'Ķ' => 'K', 'ĺ' => 'l', 'Ĺ' => 'L', 'ľ' => 'l', 'Ľ' => 'L', 'ļ' => 'l', 'Ļ' => 'L', 'ł' => 'l', 'Ł' => 'L', 'ṁ' => 'm', 'Ṁ' => 'M', 'ń' => 'n', 'Ń' => 'N', 'ň' => 'n', 'Ň' => 'N', 'ñ' => 'n', 'Ñ' => 'N', 'ņ' => 'n', 'Ņ' => 'N', 'ó' => 'o', 'Ó' => 'O', 'ò' => 'o', 'Ò' => 'O', 'ô' => 'o', 'Ô' => 'O', 'ő' => 'o', 'Ő' => 'O', 'õ' => 'o', 'Õ' => 'O', 'ø' => 'oe', 'Ø' => 'OE', 'ō' => 'o', 'Ō' => 'O', 'ơ' => 'o', 'Ơ' => 'O', 'ö' => 'oe', 'Ö' => 'OE', 'ṗ' => 'p', 'Ṗ' => 'P', 'ŕ' => 'r', 'Ŕ' => 'R', 'ř' => 'r', 'Ř' => 'R', 'ŗ' => 'r', 'Ŗ' => 'R', 'ś' => 's', 'Ś' => 'S', 'ŝ' => 's', 'Ŝ' => 'S', 'š' => 's', 'Š' => 'S', 'ṡ' => 's', 'Ṡ' => 'S', 'ş' => 's', 'Ş' => 'S', 'ș' => 's', 'Ș' => 'S', 'ß' => 'SS', 'ť' => 't', 'Ť' => 'T', 'ṫ' => 't', 'Ṫ' => 'T', 'ţ' => 't', 'Ţ' => 'T', 'ț' => 't', 'Ț' => 'T', 'ŧ' => 't', 'Ŧ' => 'T', 'ú' => 'u', 'Ú' => 'U', 'ù' => 'u', 'Ù' => 'U', 'ŭ' => 'u', 'Ŭ' => 'U', 'û' => 'u', 'Û' => 'U', 'ů' => 'u', 'Ů' => 'U', 'ű' => 'u', 'Ű' => 'U', 'ũ' => 'u', 'Ũ' => 'U', 'ų' => 'u', 'Ų' => 'U', 'ū' => 'u', 'Ū' => 'U', 'ư' => 'u', 'Ư' => 'U', 'ü' => 'ue', 'Ü' => 'UE', 'ẃ' => 'w', 'Ẃ' => 'W', 'ẁ' => 'w', 'Ẁ' => 'W', 'ŵ' => 'w', 'Ŵ' => 'W', 'ẅ' => 'w', 'Ẅ' => 'W', 'ý' => 'y', 'Ý' => 'Y', 'ỳ' => 'y', 'Ỳ' => 'Y', 'ŷ' => 'y', 'Ŷ' => 'Y', 'ÿ' => 'y', 'Ÿ' => 'Y', 'ź' => 'z', 'Ź' => 'Z', 'ž' => 'z', 'Ž' => 'Z', 'ż' => 'z', 'Ż' => 'Z', 'þ' => 'th', 'Þ' => 'Th', 'µ' => 'u', 'а' => 'a', 'А' => 'a', 'б' => 'b', 'Б' => 'b', 'в' => 'v', 'В' => 'v', 'г' => 'g', 'Г' => 'g', 'д' => 'd', 'Д' => 'd', 'е' => 'e', 'Е' => 'E', 'ё' => 'e', 'Ё' => 'E', 'ж' => 'zh', 'Ж' => 'zh', 'з' => 'z', 'З' => 'z', 'и' => 'i', 'И' => 'i', 'й' => 'j', 'Й' => 'j', 'к' => 'k', 'К' => 'k', 'л' => 'l', 'Л' => 'l', 'м' => 'm', 'М' => 'm', 'н' => 'n', 'Н' => 'n', 'о' => 'o', 'О' => 'o', 'п' => 'p', 'П' => 'p', 'р' => 'r', 'Р' => 'r', 'с' => 's', 'С' => 's', 'т' => 't', 'Т' => 't', 'у' => 'u', 'У' => 'u', 'ф' => 'f', 'Ф' => 'f', 'х' => 'h', 'Х' => 'h', 'ц' => 'c', 'Ц' => 'c', 'ч' => 'ch', 'Ч' => 'ch', 'ш' => 'sh', 'Ш' => 'sh', 'щ' => 'sch', 'Щ' => 'sch', 'ъ' => '', 'Ъ' => '', 'ы' => 'y', 'Ы' => 'y', 'ь' => '', 'Ь' => '', 'э' => 'e', 'Э' => 'e', 'ю' => 'ju', 'Ю' => 'ju', 'я' => 'ja', 'Я' => 'ja');
    return dm_str_replace(array_keys($transliterationTable), array_values($transliterationTable), $txt);
}

/**
 *
 */
function erreurs()
{
	if (isset($_SESSION['erreurs']) && count($_SESSION['erreurs']) > 0) {
		print '<b id="erreurs" style="color: red;">';
		$iter = 0;
		foreach ($_SESSION['erreurs'] as $erreur) {
			print ($iter == 0 ? '' : '<br />') . $erreur;
			$iter++;
		}
		print '</b>';
		unset($_SESSION['erreurs']);
	}
}

/**
 *
 */
function erreursPopup()
{
	if (isset($_SESSION['erreurs']) && count($_SESSION['erreurs']) > 0) {
		print '<script>';
		$erreurTxt = '';
		foreach ($_SESSION['erreurs'] as $erreur) {
			$erreurTxt .= $erreur;
		}
		print "alert('$erreurTxt');";
		print '</script>';
		unset($_SESSION['erreurs']);
	}
}

/**
 *
 */
function avertissements()
{
	if (isset($_SESSION['avertissements']) && count($_SESSION['avertissements']) > 0) {
		print '<b style="color: red;">';
		$iter = 0;
		foreach ($_SESSION['avertissements'] as $erreur) {
			print ($iter == 0 ? '' : '<br />') . $erreur;
			$iter++;
		}
		print '</b>';
		unset($_SESSION['avertissements']);
	}
}

/**
 *
 */
function avertissements_alert()
{
	if (isset($_SESSION['avertissements']) && count($_SESSION['avertissements']) > 0) {
		print 'parent.alert_avertissement(\'';
		$iter = 0;
		foreach ($_SESSION['avertissements'] as $erreur) {
			print ($iter == 0 ? '' : "\n") . $erreur;
			$iter++;
		}
		print '\');';
		unset($_SESSION['avertissements']);
	}
}

/**
 *
 */
function messages()
{
	if (isset($_SESSION['messages']) && count($_SESSION['messages']) > 0) {
		print '<b id="messages" style="color: green;">';
		foreach ($_SESSION['messages'] as $message) {
			print '<br />' . $message;
		}
		print '</b>';
		unset($_SESSION['messages']);
	}
}

function showPaginateur($lien, $limit, $nb_total, $nb_pages, $nb_par_page, $page, $floatLeft = false, $isAjax = false, $parentId = "")
{
    if ($isAjax) {
	?>
        <script>
            function goToPage(obj) {
				$.startObjectLoader($('<?= $parentId ?>'), 'loaderrr');
				$.get($(obj).attr('href'), {}, function(html){
					$('<?= $parentId ?>').html(html);
					$.stopObjectLoader('loaderrr');
				});
				return false;
            }
        </script>
        <?php
    }
        ?>
    <div id="datatable_info" class="dataTables_info" style="margin: 10px 5px; width: 200px!important;">Liste <?php print ($limit + 1); ?>
        à <?= ($limit + $nb_par_page <= $nb_total ? $limit + $nb_par_page : $nb_total); ?> de <?php print $nb_total; ?> entrées
    </div>
    <div id="datatable_paginate" class="dataTables_paginate paging_full_numbers" style="margin: 10px 5px;<?= ($floatLeft ? 'float:left!important;' : '') ?>">
        <a href="<?php print $lien; ?>&page=1" <?= ($isAjax ? 'onclick="goToPage(this); return false;"' : '') ?>><span id="datatable_first"
                                            class="first paginate_button <?php print ($page == 1 ? 'paginate_button_disabled' : ''); ?>">Première</span></a>
        <a href="<?php print $lien; ?>&page=<?php print ($page == 1 ? 1 : $page - 1); ?>" <?= ($isAjax ? 'onclick="goToPage(this); return false;"' : '') ?>><span id="datatable_previous"
                                                                              class="previous paginate_button <?php print ($page == 1 ? 'paginate_button_disabled' : ''); ?>">Précédente</span></a>
        <span>
		<?php
		$first_page = ($page - 3) > 0 ? ($page - 3) : 1;
		$last_page = ($first_page + 5 <= $nb_pages ? $first_page + 5 : $nb_pages);
		for ($iter_page = $first_page; $iter_page <= $last_page; $iter_page++) {
			?>
            <a href="<?php print $lien; ?>&page=<?php print $iter_page; ?>" <?= ($isAjax ? 'onclick="goToPage(this); return false;"' : '') ?>><span
                        class="<?php print ($iter_page == $page ? 'paginate_active' : 'paginate_button'); ?>"><?php print $iter_page; ?></span></a>
			<?php
		}
		?>
		</span>
        <a href="<?php print $lien; ?>&page=<?php print ($page == $nb_pages ? $nb_pages : $page + 1); ?>" <?= ($isAjax ? 'onclick="goToPage(this); return false;"' : '') ?>><span id="datatable_next"
                                                                                              class="next paginate_button <?php print ($page == $nb_pages ? 'paginate_button_disabled' : ''); ?>">Suivante</span></a>
        <a href="<?php print $lien; ?>&page=<?php print $nb_pages; ?>" <?= ($isAjax ? 'onclick="goToPage(this); return false;"' : '') ?>><span id="datatable_last"
                                                           class="last paginate_button <?php print ($page == $nb_pages ? 'paginate_button_disabled' : ''); ?>">Dernière</span></a>
    </div>
	<?php
}

/**
 * @param $avertissement
 */
function setAvertissements($avertissement)
{
	$_SESSION['avertissements'][] = $avertissement;
}

/**
 * @param $erreur
 */
function setErreur($erreur)
{
	$_SESSION['erreurs'][] = $erreur;
}

/**
 * @return int
 */
function nbAvertissements()
{
	return isset($_SESSION['avertissements']) ? count($_SESSION['avertissements']) : 0;
}

/**
 * @return int
 */
function nbErreurs()
{
	return isset($_SESSION['erreurs']) ? count($_SESSION['erreurs']) : 0;
}

/**
 * @param $message
 */
function setMessage($message)
{
	$_SESSION['messages'][] = $message;
}

/**
 * @return bool
 */
function isMaster()
{
	global $CodeClient;
	return (isset($_SESSION['usager_'.$CodeClient]->is_master) && intval($_SESSION['usager_'.$CodeClient]->is_master) == 1);
}

/**
 * @return bool
 */
function isMasterOrMario()
{
    global $CodeClient;
    return ((isset($_SESSION['usager_'.$CodeClient]->is_master) && intval($_SESSION['usager_'.$CodeClient]->is_master) == 1) || $_SESSION['usager_'.$CodeClient]->email == '<EMAIL>');
}

/**
 * @return bool
 */
function isPilote()
{
	global $CodeClient;
	return (isset($_SESSION['usager_'.$CodeClient]->is_pilote) && intval($_SESSION['usager_'.$CodeClient]->is_pilote) == 1);
}

/**
 * @return Usager
 */
function getUsager()
{
	global $CodeClient;
	return $_SESSION['usager_' . $CodeClient] ?? null;
}

/**
 * @return int
 */
function getIdUsager()
{
	global $CodeClient;
	return isset($_SESSION['usager_'.$CodeClient]) ? intval($_SESSION['usager_'.$CodeClient]->id) : 0;
}

//test
/**
 * @param $role
 * @return bool
 */
function haveRole($role)
{
	$usager = getUsager();
	if (is_array($role)) {
		return (isset($usager->roles_codes) && is_array($usager->roles_codes) && count(array_intersect($role, $usager->roles_codes)) > 0) || isMaster();
	} else {
		return (isset($usager->roles_codes) && is_array($usager->roles_codes) && in_array($role, $usager->roles_codes)) || isMaster();
	}
}

/**
 * @param $permission
 * @return bool
 */
function havePermission($permission, $exclude_master = false)
{
    $usager = getUsager();
	if (is_array($permission)) {
		return (isset($usager->permissions_codes) && is_array($usager->permissions_codes) && count(array_intersect($permission, $usager->permissions_codes)) > 0) || (!$exclude_master && isMaster());
	} else {
		return (isset($usager->permissions_codes) && is_array($usager->permissions_codes) && in_array($permission, $usager->permissions_codes)) || (!$exclude_master && isMaster());
	}
}

/**
 * @param $permission
 * @param null $projet_id
 * @return bool
 */
function havePermissionForProjet($permission, $projet_id = null, $exclude_master = false)
{
	$usager = getUsager();
    $projet_id = $projet_id ?? getProjetId();
	return (isset($usager->permissions_projets[$projet_id][$permission]) && $usager->permissions_projets[$projet_id][$permission]) || (!$exclude_master && isMaster());
}

/**
 * @param $id
 * @return string
 */
function procedurePath($id)
{
    return 'docs/procedures/'.$id.'.pdf';
}

/**
 * @param $id
 * @return string
 */
function manuelPath($id)
{
    return 'docs/manuels/'.$id.'.pdf';
}

/**
 * @param $row_slave
 * @param $colonnes
 * @return array
 */
function keep_essentials($row_slave, $colonnes)
{
	$essentials = array();
	foreach ($row_slave as $key => $value) {
		if (in_array($key, $colonnes)) {
			$essentials[$key] = $value;
		}
	}
	return $essentials;
}

/**
 * @param $id
 * @param bool $wipeProjetInfo
 */
function setOuvrageInfo($id, $wipeProjetInfo = false)
{
	if ($id > 0) {
		$ouvrage = new Ouvrages($id);

		$usager = getUsager();
		$projets_auth = $usager->getProjetsAutorises();

		if (!isset($projets_auth[$ouvrage->id]) && !isMaster()) {
			disconnect();
		}

		if ($wipeProjetInfo) {
			setProjetInfo(0);
			$_SESSION['fiche_mode'] = 'consultation';
		}

		$_SESSION['ouvrage_info'] = $ouvrage;
		unset($_SESSION['filtre_avance']);
		unset($_SESSION['filtre_top']);
	} else {
		unset($_SESSION['ouvrage_info']);
		unset($_SESSION['fiche_mode']);
	}
}

/**
 * @return int
 */
function getOuvrageId()
{
    $ouvragecInfo = getOuvrageInfo();
	return isset($ouvragecInfo) ? $ouvragecInfo->id : 0;
}

/**
 * @return Ouvrages
 */
function getOuvrageInfo()
{
	return $_SESSION['ouvrage_info'] ?? null;
}

/**
 * @param $id
 * @param string $mode
 */
function setProjetInfo($id, $mode = 'consultation')
{
	if ($id > 0) {
		$projet = new Projets($id);
		if ($projet->getCodeStatut() <> 50) {
			$mode = 'consultation';
		}

		$_SESSION['fiche_mode'] = $mode;

		$usager = getUsager();
		$projets_auth = $usager->getProjetsAutorises();

		if (!isset($projets_auth[$projet->ouvrage_id][$projet->id]) && !isMaster()) {
			disconnect();
		}

		setOuvrageInfo($projet->ouvrage_id);
		$_SESSION['projet_info'] = $projet;
		unset($_SESSION['filtre_avance']);
		unset($_SESSION['filtre_top']);
	} else {
		unset($_SESSION['projet_info']);
		unset($_SESSION['fiche_mode']);
	}
}

/**
 * @param $id
 */
function setProjetInfoForCron($id)
{
	if ($id > 0) {
		$projet = new Projets($id);
		setOuvrageInfoForCron($projet->ouvrage_id);
		$_SESSION['projet_info'] = $projet;
	}
}

/**
 * @param $id
 */
function setOuvrageInfoForCron($id)
{
	if ($id > 0) {
		$ouvrage = new Ouvrages($id);
		$_SESSION['ouvrage_info'] = $ouvrage;
	}
}

/**
 * @return bool
 */
function isFicheReadonly()
{
    return isset($_SESSION['fiche_mode']) && $_SESSION['fiche_mode'] == 'consultation';
}

/**
 * @return int
 */
function getProjetId()
{
    $projetInfo = getProjetInfo();
	return isset($projetInfo) ? $projetInfo->id : 0;
}

/**
 * @return Projets
 */
function getProjetInfo()
{
	return $_SESSION['projet_info'] ?? null;
}

/**
 * @return bool
 */
function isMacroProjet()
{
    $projet = getProjetInfo();
	return isset($projet) && ($projet->type_conception == 1 || $projet->type_conception == 0);
}

/**
 * @return bool
 */
function isMicroProjet()
{
    $projet = getProjetInfo();
	return isset($projet) && ($projet->type_conception == 2);
}

/**
 * @return bool
 */
function isListReadonlyProjet()
{
    $projet = getProjetInfo();
	return isset($projet) && ($projet->is_listes_readonly == 1);
}

/**
 * @return bool
 */
function isAfficheCategorieProjet()
{
	$projet = getProjetInfo();
	return isset($projet) && ($projet->is_affiche_categorie == 1);
}

/**
 * @return bool
 */
function isDBMaster()
{
	return $_SESSION['is_db_master'] ?? false;
}

function ldapAuth($email, $password, $codeClient)
{
	$connection = new Connection([
		'hosts' => [$_ENV[strtoupper($codeClient) . '_LDAP_HOST']],
    ]);

    return $connection->auth()->attempt($email, $password, true);
}

/**
 * @param $email
 * @param $password
 * @param $codeClient
 * @param bool $useMaster
 * @return array
 */
function login($email, $password, $codeClient, $useMaster = false)
{
	global $db_user, $db_pswd, $db_host, $db_name;

	if ($useMaster) {
		$db = new db($db_user, $db_pswd, $db_host, 'master');
	} else {
		$db = new db($db_user, $db_pswd, $db_host, $db_name);
	}

	$sql = "SELECT *
			FROM usagers
			WHERE email = ? and is_actif = 1";
	$usager = $db->getRow($sql, array($email));

//    if (isset($_ENV[strtoupper($codeClient) . '_LDAP_HOST']) && $usager['is_ldap'] == 1) {
//        return ldapAuth($email, $password, $codeClient);
//    }

	// L’utilisation de déclarations empêche les injections SQL
	if ($usager) {
		$usager_id = intval($usager['id']);
		$db_password = $usager['password'];
		$salt = $usager['salt'];

		// Hashe le mot de passe avec le salt unique
		$password = hash('sha512', $password . $salt);

		// Si l’utilisateur existe, le script vérifie qu’il n’est pas verrouillé
		// à cause d’essais de connexion trop répétés

		if (checkbrute($usager_id, $useMaster) == true) {
			// Le compte est verrouillé
			// Envoie un email à l’utilisateur l’informant que son compte est verrouillé
			return ['statut' => false, 'code' => 'checkbrute'];
		} else {
			// Vérifie si les deux mots de passe sont les mêmes
			// Le mot de passe que l’utilisateur a donné.
			if ($db_password == $password) {
				// Le mot de passe est correct!
				// Récupère la chaîne user-agent de l’utilisateur
				$user_browser = getIp(true);
				$_SESSION['login_string'] = hash('sha512', $password . $user_browser);

				$db->query("DELETE FROM usagers_tentatives_logins WHERE usager_id = ?", array($usager_id));
				$db->query("INSERT INTO usagers_logons (usager_id) VALUES (?)", array($usager_id));

				$usager = new Usager($usager_id);
				$_SESSION['usager_' . $codeClient] = $usager;

				// Ouverture de session réussie.
				return ['statut' => true, 'code' => 'reussite'];
			} else {
				// Le mot de passe n’est pas correct
				// Nous enregistrons cet essai dans la base de données
				$db->query("INSERT INTO usagers_tentatives_logins(usager_id)
                                    VALUES (?)", array($usager_id));
				return ['statut' => false, 'code' => 'motPasseEronne'];
			}
		}
	} else {
		// L’utilisateur n’existe pas.
		return ['statut' => false, 'code' => 'inexistant'];
	}
}

/**
 * @param $usager_id
 * @param bool $useMaster
 * @return bool
 */
function checkbrute($usager_id, $useMaster = false)
{
	global $db_user, $db_pswd, $db_host, $db_name;

	if ($useMaster) {
		$db = new db($db_user, $db_pswd, $db_host, 'master');
	} else {
		$db = new db($db_user, $db_pswd, $db_host, $db_name);
	}

	$now_moins_2_heures = date('Y-m-d H:i:s', strtotime(date('Y-m-d H:i:s') . ' - 2 hours'));
	$sql = "select ts_ajout
           from usagers_tentatives_logins
           where usager_id = ? and ts_ajout > ?";
	$tentatives = $db->getAll($sql, array($usager_id, $now_moins_2_heures));

	// S’il y a eu plus de 5 essais de connexion
	if (count($tentatives) > 4) {
		return true;
	} else {
		return false;
	}
}

function valideIp($isScriptCache = false) {
    global $db, $isDev;

//    if (!$isDev) {
	    $ips = IpManagement::getListe();
	    $statut = -10;
        $id = -1;
	    if (isset($ips[$_SERVER['REMOTE_ADDR']])) {
		    $statut = $ips[$_SERVER['REMOTE_ADDR']]->status;
		    $id = $ips[$_SERVER['REMOTE_ADDR']]->id;
	    }

	    if ($statut <> 1) {
		    if ($statut == -10) {
			    $id = IpManagement::insert($_SERVER['REMOTE_ADDR'], 0);
		    }
		    $_POST['password'] = "[MOT DE PASSE]";
		    $textesStatut = [-10 => "Inexistant", 0 => "En attente de confirmation", -1 => "Banni"];
		    $message = "Statut: " . $textesStatut[$statut] . "<br />Adresse IP: " . $_SERVER['REMOTE_ADDR'] . getDebug($_POST) . getDebug($_SERVER);

		    if ($isScriptCache) {
			    envoiCourriel(['<EMAIL>'], 'Tentative d\'accès à un script interne avec IP non-reconnu', $message);
			    erase_session();
			    header('Location: login.php');
		    } else {
                $codeErreur = 1;
                if (isset($_POST['email']) && in_array($statut, [-10, 0])) {
                    $langue = $db->getOne("select langue from usagers where email = ?", [$_POST['email']]);
                    if (dm_strlen($langue) > 0) {
	                    $validationCode = hash('sha512', uniqid(random_bytes(16), TRUE));
	                    IpManagement::updateValidationCode($id, $validationCode);
	                    envoiCourriel($_POST['email'], 'Solutions DocMatic – ' . ($langue == 'en' ? 'Connection Issues' : 'Problèmes de connexion'), IpManagement::getValidationMessage($validationCode, $langue), array(), array(), '<EMAIL>', 'No Reply', ['<EMAIL>']);

	                    $codeErreur = 2;
                    } else {
	                    envoiCourriel(['<EMAIL>'], 'Tentative de connection avec IP non-reconnu et adresse courriel non-reconnue', $message);
                    }
                } else {
	                envoiCourriel(['<EMAIL>'], 'Tentative de connection avec IP non-reconnu', $message);
                }
			    erase_session();
			    header('Location: login.php?err=' . urlencode($codeErreur));
		    }
		    exit;
	    }
//    }
}

/**
 * @return bool
 */
function login_check()
{
	global $db_user, $db_pswd, $db_host, $db_name;

	$usager = getUsager();

	if (isMaster()) {
		$db = new db($db_user, $db_pswd, $db_host, 'master');
	} else if (isset($usager->db_name_check)) {
		$db = new db($db_user, $db_pswd, $db_host, $usager->db_name_check);
	} else {
		if (!isset($db_name)) {
			return false;
		}
		$db = new db($db_user, $db_pswd, $db_host, $db_name);
	}

	// Vérifie que toutes les variables de session sont mises en place
	if ($usager) {
		$sql = "select password from usagers where id = ?";
		$usager_db = $db->getRow($sql, array($usager->id));
		if ($usager_db) {
			$login_string = $_SESSION['login_string'];

			// Récupère la chaîne user-agent de l’utilisateur
			$user_browser = getIp(true);
			$login_check = hash('sha512', $usager_db['password'] . $user_browser);

			if ($login_check === $login_string) {
				// Connecté!!!!
				return true;
			} else {
				// Pas connecté
				return false;
			}
		} else {
			// Pas connecté
			return false;
		}
	} else {
		// Pas connecté
		return false;
	}
}

function getIp($short = false){
	if(!empty($_SERVER['HTTP_CLIENT_IP'])){
		$ip = $_SERVER['HTTP_CLIENT_IP'];
	}elseif(!empty($_SERVER['HTTP_X_FORWARDED_FOR'])){
		$ip = $_SERVER['HTTP_X_FORWARDED_FOR'];
	}else{
		$ip = $_SERVER['REMOTE_ADDR'];
	}
    if ($short) {
        $ipParts = dm_explode('.', $ip);
        if (count($ipParts) == 4) {
            $ip = $ipParts[0] . '.' . $ipParts[1] . '.' . $ipParts[2];
        }
    }

	return $ip;
}

/**
 * @param $usager_id
 * @param $code_client
 * @return string
 */
function create_temp_token($usager_id, $code_client)
{
	global $db_user, $db_pswd, $db_host, $Mapping_DB_Clients;

	$db = new db($db_user, $db_pswd, $db_host, $Mapping_DB_Clients[$code_client]['db']);

	$db->autocommit(false);

	$temp_token = bin2hex(random_bytes(100));
	$temp_token_expiration = date('Y-m-d H:i:s', strtotime(' + 2 seconds'));

	$sql = "update usagers set temp_token = ?, temp_token_expiration = ? where id = ?";
	$db->query($sql, array(hash('sha512', $temp_token), $temp_token_expiration, $usager_id));

	$db->commit();

	return $temp_token;
}

/**
 * @param $temp_token
 * @return bool
 */
function autologon_from_temp_token($temp_token)
{
	global $db, $CodeClient;

	$db->autocommit(false);

	$sql = "select * 
            from usagers
            where temp_token = ? and temp_token_expiration >= ?";
	$usager = $db->getRow($sql, array(hash('sha512', $temp_token), date('Y-m-d H:i:s')));

	if (isset($usager) && isset($usager['id']) && $usager['id'] > 0) {
		$user_browser = getIp(true);
		$_SESSION['login_string'] = hash('sha512', $usager['password'] . $user_browser);

		$db->query("update usagers set temp_token = null, temp_token_expiration = null WHERE id = ?", array($usager['id']));
		$db->query("DELETE FROM usagers_tentatives_logins WHERE usager_id = ?", array($usager['id']));
		$db->query("INSERT INTO usagers_logons (usager_id) VALUES (?)", array($usager['id']));

		$usager = new Usager($usager['id']);
		$_SESSION['usager_' . $CodeClient] = $usager;
		// Ouverture de session réussie.
		return true;
	} else {
		$db->query("update usagers set temp_token = null, temp_token_expiration = null WHERE temp_token = ?", array($temp_token));
	}

	$db->commit();

	return false;
}

/**
 * @param $email
 * @param $password
 * @param $codeClient
 * @return int|null
 */
function find_successfull_login($email, $password, $codeClient)
{
	global $db, $db_user, $db_pswd, $db_host, $Mapping_DB_Clients, $CodeClient;

	$db = new db($db_user, $db_pswd, $db_host, $Mapping_DB_Clients[$codeClient]['db']);

	$sql = "SELECT *
            FROM usagers
            WHERE email = ? and is_actif = 1";
	$usager = $db->getRow($sql, array($email));

	// L’utilisation de déclarations empêche les injections SQL
	if ($usager) {
		$usager_id = intval($usager['id']);
		$db_password = $usager['password'];
		$salt = $usager['salt'];

		// Hashe le mot de passe avec le salt unique
		$password = hash('sha512', $password . $salt);

		// Si l’utilisateur existe, le script vérifie qu’il n’est pas verrouillé
		// à cause d’essais de connexion trop répétés

		$now_moins_2_heures = date('Y-m-d H:i:s', strtotime(date('Y-m-d H:i:s') . ' - 2 hours'));
		$sql = "select ts_ajout
               from usagers_tentatives_logins
               where usager_id = ? and ts_ajout > ?";
		$tentatives = $db->getAll($sql, array($usager_id, $now_moins_2_heures));

		// S’il y a eu plus de 5 essais de connexion
		if (count($tentatives) > 5) {
			// Le compte est verrouillé
			// Envoie un email à l’utilisateur l’informant que son compte est verrouillé
			return null;
		} else {
			// Vérifie si les deux mots de passe sont les mêmes
			// Le mot de passe que l’utilisateur a donné.
			if ($db_password == $password) {
				$db->query("DELETE FROM usagers_tentatives_logins WHERE usager_id = ?", array($usager_id));

				$usager = getUsager();
				if (!isset($usager)) {
					$user_browser = getIp(true);
					$_SESSION['login_string'] = hash('sha512', $password . $user_browser);

					$usager = new Usager($usager_id);
					$usager->db_name_check = $Mapping_DB_Clients[$codeClient]['db'];
					$_SESSION['usager_' . $CodeClient] = $usager;
				}

				// Ouverture de session réussie.
				return $usager_id;
			} else {
				// Le mot de passe n’est pas correct
				// Nous enregistrons cet essai dans la base de données
				$db->query("INSERT INTO usagers_tentatives_logins(usager_id)
                                VALUES (?)", array($usager_id));
				return null;
			}
		}
	} else {
		// L’utilisateur n’existe pas.
		return null;
	}
}

/**
 * @param $email
 * @param $codeClient
 * @return int|null
 */
function find_successfull_login_auto($email, $codeClient)
{
	global $db, $db_user, $db_pswd, $db_host, $Mapping_DB_Clients, $CodeClient;

	$db = new db($db_user, $db_pswd, $db_host, $Mapping_DB_Clients[$codeClient]['db']);

	$sql = "SELECT *
            FROM usagers
            WHERE email = ? and is_actif = 1";
	$usager = $db->getRow($sql, array($email));

	// L’utilisation de déclarations empêche les injections SQL
	if ($usager) {
		$usager_id = intval($usager['id']);
		$db_password = $usager['password'];

        $usager = getUsager();
        if (!isset($usager)) {
            $user_browser = getIp(true);
            $_SESSION['login_string'] = hash('sha512', $db_password . $user_browser);

            $usager = new Usager($usager_id);
            $usager->db_name_check = $Mapping_DB_Clients[$codeClient]['db'];
            $_SESSION['usager_' . $CodeClient] = $usager;
        }

		// Ouverture de session réussie.
		return $usager_id;
	} else {
		// L’utilisateur n’existe pas.
		return null;
	}
}

/**
 *
 */
function erase_session()
{
    // Détruisez les variables de session 
    $_SESSION = array();

    // Retournez les paramètres de session 
    $params = session_get_cookie_params();

    // Effacez le cookie. 
    setcookie(session_name(),
            '', time() - 42000, 
            $params["path"], 
            $params["domain"], 
            $params["secure"], 
            $params["httponly"]);

    // Détruisez la session 
    session_destroy();
}

/**
 *
 */
function disconnect()
{
    unset($_SESSION);
    header('Location: login.php');
    die();
}

/**
 *
 */
function startBuff()
{
	if (ob_get_level() == 0)
	{
		ob_start();
	}
}

/**
 *
 */
function sendBuff()
{
	ob_flush();
	flush();
}

/**
 *
 */
function stopBuff()
{
	//ob_end_flush();
}

/**
 * @param array $files
 * @param string $destination
 * @param bool $overwrite
 * @return bool
 */
function create_zip($files = array(), $destination = '', $overwrite = false)
{
	//if the zip file already exists and overwrite is false, return false
	if (file_exists($destination) && !$overwrite) {
		return false;
	}
	//vars
	$valid_files = array();
	//if files were passed in...
	if (is_array($files)) {
		//cycle through each file
		foreach ($files as $file) {
			//make sure the file exists
			if (file_exists($file)) {
				$valid_files[] = $file;
			}
		}
	}
	//if we have good files...
	if (count($valid_files)) {
		//create the archive
		$zip = new ZipArchive();
		if ($zip->open($destination, $overwrite ? ZIPARCHIVE::OVERWRITE : ZIPARCHIVE::CREATE) !== true) {
			return false;
		}
		//add the files
		foreach ($valid_files as $file) {
			$zip->addFile($file, $file);
		}
		//debug
		//echo 'The zip archive contains ',$zip->numFiles,' files with a status of ',$zip->status;

		//close the zip -- done!
		$zip->close();

		//check to make sure the file exists
		return file_exists($destination);
	} else {
		return false;
	}
}

/**
 * @param $old
 * @param $new
 * @return string
 */
function diff($old, $new)
{

	$return = '';
	if (dm_trim($old) == dm_trim($new)) {
		$return = $new;
	} else if (dm_trim($old) <> dm_trim($new)) {
		$return = "<ins>$new</ins><del>$old</del> ";
	}

	return $return;
}

/**
 * @param $old
 * @param $new
 * @return array
 */
function diffs($old, $new){
    $matrix = array();
    $maxlen = 0;
	$omax = 0;
	$nmax = 0;
    foreach($old as $oindex => $ovalue){
        $nkeys = array_keys($new, $ovalue);
        foreach($nkeys as $nindex){
            $matrix[$oindex][$nindex] = isset($matrix[$oindex - 1][$nindex - 1]) ?
                $matrix[$oindex - 1][$nindex - 1] + 1 : 1;
            if ($matrix[$oindex][$nindex] > $maxlen){
                $maxlen = $matrix[$oindex][$nindex];
                $omax = $oindex + 1 - $maxlen;
                $nmax = $nindex + 1 - $maxlen;
            }
        }   
    }
    if ($maxlen == 0) return array(array('d'=>$old, 'i'=>$new));
    return array_merge(
        diffs(array_slice($old, 0, $omax), array_slice($new, 0, $nmax)),
        array_slice($new, $nmax, $maxlen),
        diffs(array_slice($old, $omax + $maxlen), array_slice($new, $nmax + $maxlen)));
}

/**
 * @param $old
 * @param $new
 * @return string
 */
function diff_by_word($old, $new){
    $ret = '';
    $diff = diffs(dm_preg_split("/[\s]+/u", $old), dm_preg_split("/[\s]+/u", $new));
    foreach($diff as $k){
        if (is_array($k))
            $ret .= (!empty($k['d'])?"<del>".dm_implode(' ',$k['d'])."</del> ":'').
                (!empty($k['i'])?"<ins>".dm_implode(' ',$k['i'])."</ins> ":'');
        else $ret .= $k . ' ';
    }
    return $ret;
}

/**
 * @param $str
 * @param string $charset
 * @return string
 */
function strip_accents($str, $charset='utf-8')
{
    $str = dm_htmlentities($str, ENT_NOQUOTES, $charset);
    
    $str = dm_preg_replace('#&([A-Za-z])(?:acute|cedil|caron|circ|grave|orn|ring|slash|th|tilde|uml);#', '\1', $str);
    $str = dm_preg_replace('#&([A-Za-z]{2})(?:lig);#', '\1', $str); // pour les ligatures e.g. '&oelig;'
    $str = dm_preg_replace('#&[^;]+;#', '', $str); // supprime les autres caractères
    
    return $str;
}

/**
 * @param $fiche_id
 */
function setSelectedGabaritId($fiche_id)
{
    $_SESSION['selected_gabarit_id'] = $fiche_id;
}

/**
 * @return int
 */
function getSelectedGabaritId()
{
    return (isset($_SESSION['selected_gabarit_id']) ? $_SESSION['selected_gabarit_id'] : 0);
}

/**
 * @param $tos
 * @param $sujet
 * @param $message
 * @param array|string $ccs
 * @param array|string $attachments
 * @param string $from
 * @param string $fromName
 */
function envoiCourriel($tos, $sujet, $message, $ccs = array(), $attachments = array(), $from = '<EMAIL>', $fromName = 'Ne pas répondre', $bccs = [])
{
	require_once(Config::instance()->baseDir . 'classes/PHPMailer/PHPMailerAutoload.php');
	require_once(Config::instance()->baseDir . 'classes/PHPMailer/class.phpmailer.php');

	$email = new PHPMailer();
	$email->isSMTP();
	$email->isHTML(true);
	$email->Host = Config::instance()->mailHost;
	$email->CharSet = 'UTF-8';
	$email->From = $from;
	$email->FromName = $fromName;
	$email->Subject = $sujet;
	$email->Body = $message;

	if (is_array($tos)) {
		foreach ($tos as $to) {
			$email->addAddress($to);
		}
	} else {
		$email->addAddress($tos);
	}

	if (is_array($ccs)) {
		foreach ($ccs as $cc) {
			$email->addCC($cc);
		}
	} else {
		$email->addCC($ccs);
	}

	if (is_array($bccs)) {
		foreach ($bccs as $bcc) {
			$email->addBCC($bcc);
		}
	} else {
		$email->addBCC($bccs);
	}

	if (is_array($attachments)) {
		foreach ($attachments as $attachment) {
			$email->addAttachment($attachment, basename($attachment));
		}
	} else {
		$email->addAttachment($attachments, basename($attachments));
	}

	$email->SMTPOptions = array(
		'ssl' => array(
			'verify_peer' => false,
			'verify_peer_name' => false,
			'allow_self_signed' => true
		)
	);
	if (!Config::instance()->isDev && !isset($_GET['debug'])) {
		if (!$email->send()) {
			throw new Exception($email->ErrorInfo);
		}
	} else {
		printDebug($email);
	}
}

/** 
* Converts bytes into human readable file size. 
* 
* @param string $bytes 
* @return string human readable file size (2,87 Мб)
* <AUTHOR> Arseny 
*/ 
function FileSizeConvert($bytes)
{
    $bytes = floatval($bytes);
        $arBytes = array(
            0 => array(
                "UNIT" => "TB",
                "VALUE" => pow(1024, 4)
            ),
            1 => array(
                "UNIT" => "GB",
                "VALUE" => pow(1024, 3)
            ),
            2 => array(
                "UNIT" => "MB",
                "VALUE" => pow(1024, 2)
            ),
            3 => array(
                "UNIT" => "KB",
                "VALUE" => 1024
            ),
            4 => array(
                "UNIT" => "B",
                "VALUE" => 1
            ),
        );

	$result = '';
    foreach($arBytes as $arItem)
    {
        if ($bytes >= $arItem["VALUE"])
        {
            $result = $bytes / $arItem["VALUE"];
            $result = dm_str_replace(".", "," , strval(round($result, 2)))." ".$arItem["UNIT"];
            break;
        }
    }
    return $result;
}

/**
 * @param $a
 * @param $b
 * @return int
 */
function custom_sort($a, $b) {
    return strcmp (sansaccent($a), sansaccent($b));
}

/**
 * @param $a
 * @param $b
 * @return int
 */
function custom_sort_titre($a, $b) {
	return strcmp (sansaccent($a['titre']), sansaccent($b['titre']));
}

/**
 * @param $a
 * @param $b
 * @return int
 */
function custom_sort_name($a, $b) {
	return strcmp (sansaccent($a['Name']), sansaccent($b['Name']));
}

/**
 * @param $a
 * @param $b
 * @return int
 */
function custom_sort_nom($a, $b) {
    return strcmp (sansaccent($a['nom']), sansaccent($b['nom']));
}

/**
 * @param $a
 * @param $b
 * @return int
 */
function custom_sort_libelle($a, $b) {
	return strcmp (sansaccent($a['libelle']), sansaccent($b['libelle']));
}

function custom_sort_tri($a, $b) {
    // Compare les valeurs de "tri" directement
    if ($a['tri'] == $b['tri']) {
        return 0; // Si égalité
    }
    return ($a['tri'] < $b['tri']) ? -1 : 1; // Sinon, on compare les valeurs de "tri"
}

/**
 * @param $str
 * @param bool $utf8
 * @return mixed
 */
function sansaccent($str, $utf8 = true) {
    $str = (string) $str;
    if (is_null($utf8)) {
        if (!function_exists('mb_detect_encoding')) {
            $utf8 = (strtolower(mb_detect_encoding($str)) == 'utf-8');
        } else {
            $length = dm_strlen($str);
            $utf8 = true;
 
            for ($i = 0; $i < $length; $i++) {
                $c = ord($str[$i]);
 
                if ($c < 0x80) $n = 0; // 0bbbbbbb
                elseif (($c & 0xE0) == 0xC0) $n = 1; // 110bbbbb
                elseif (($c & 0xF0) == 0xE0) $n = 2; // 1110bbbb
                elseif (($c & 0xF8) == 0xF0) $n = 3; // 11110bbb
                elseif (($c & 0xFC) == 0xF8) $n = 4; // 111110bb
                elseif (($c & 0xFE) == 0xFC) $n = 5; // 1111110b
                else return false; // Does not match any model
 
                for ($j = 0; $j < $n; $j++) { // n bytes matching 10bbbbbb follow ?
                    if ((++$i == $length) || ((ord($str[$i]) & 0xC0) != 0x80)) {
                        $utf8 = false;
                        break;
                    }
                }
            }
        }
    }
 
    if (!$utf8) {
        $str = utf8_encode($str);
    }
 
    $transliteration = array(
        'Ĳ' => 'I', 'Ö' => 'O', 'Œ' => 'O', 'Ü' => 'U', 'ä' => 'a', 'æ' => 'a',
        'ĳ' => 'i', 'ö' => 'o', 'œ' => 'o', 'ü' => 'u', 'ß' => 's', 'ſ' => 's',
        'À' => 'A', 'Á' => 'A', 'Â' => 'A', 'Ã' => 'A', 'Ä' => 'A', 'Å' => 'A',
        'Æ' => 'A', 'Ā' => 'A', 'Ą' => 'A', 'Ă' => 'A', 'Ç' => 'C', 'Ć' => 'C',
        'Č' => 'C', 'Ĉ' => 'C', 'Ċ' => 'C', 'Ď' => 'D', 'Đ' => 'D', 'È' => 'E',
        'É' => 'E', 'Ê' => 'E', 'Ë' => 'E', 'Ē' => 'E', 'Ę' => 'E', 'Ě' => 'E',
        'Ĕ' => 'E', 'Ė' => 'E', 'Ĝ' => 'G', 'Ğ' => 'G', 'Ġ' => 'G', 'Ģ' => 'G',
        'Ĥ' => 'H', 'Ħ' => 'H', 'Ì' => 'I', 'Í' => 'I', 'Î' => 'I', 'Ï' => 'I',
        'Ī' => 'I', 'Ĩ' => 'I', 'Ĭ' => 'I', 'Į' => 'I', 'İ' => 'I', 'Ĵ' => 'J',
        'Ķ' => 'K', 'Ľ' => 'K', 'Ĺ' => 'K', 'Ļ' => 'K', 'Ŀ' => 'K', 'Ł' => 'L',
        'Ñ' => 'N', 'Ń' => 'N', 'Ň' => 'N', 'Ņ' => 'N', 'Ŋ' => 'N', 'Ò' => 'O',
        'Ó' => 'O', 'Ô' => 'O', 'Õ' => 'O', 'Ø' => 'O', 'Ō' => 'O', 'Ő' => 'O',
        'Ŏ' => 'O', 'Ŕ' => 'R', 'Ř' => 'R', 'Ŗ' => 'R', 'Ś' => 'S', 'Ş' => 'S',
        'Ŝ' => 'S', 'Ș' => 'S', 'Š' => 'S', 'Ť' => 'T', 'Ţ' => 'T', 'Ŧ' => 'T',
        'Ț' => 'T', 'Ù' => 'U', 'Ú' => 'U', 'Û' => 'U', 'Ū' => 'U', 'Ů' => 'U',
        'Ű' => 'U', 'Ŭ' => 'U', 'Ũ' => 'U', 'Ų' => 'U', 'Ŵ' => 'W', 'Ŷ' => 'Y',
        'Ÿ' => 'Y', 'Ý' => 'Y', 'Ź' => 'Z', 'Ż' => 'Z', 'Ž' => 'Z', 'à' => 'a',
        'á' => 'a', 'â' => 'a', 'ã' => 'a', 'ā' => 'a', 'ą' => 'a', 'ă' => 'a',
        'å' => 'a', 'ç' => 'c', 'ć' => 'c', 'č' => 'c', 'ĉ' => 'c', 'ċ' => 'c',
        'ď' => 'd', 'đ' => 'd', 'è' => 'e', 'é' => 'e', 'ê' => 'e', 'ë' => 'e',
        'ē' => 'e', 'ę' => 'e', 'ě' => 'e', 'ĕ' => 'e', 'ė' => 'e', 'ƒ' => 'f',
        'ĝ' => 'g', 'ğ' => 'g', 'ġ' => 'g', 'ģ' => 'g', 'ĥ' => 'h', 'ħ' => 'h',
        'ì' => 'i', 'í' => 'i', 'î' => 'i', 'ï' => 'i', 'ī' => 'i', 'ĩ' => 'i',
        'ĭ' => 'i', 'į' => 'i', 'ı' => 'i', 'ĵ' => 'j', 'ķ' => 'k', 'ĸ' => 'k',
        'ł' => 'l', 'ľ' => 'l', 'ĺ' => 'l', 'ļ' => 'l', 'ŀ' => 'l', 'ñ' => 'n',
        'ń' => 'n', 'ň' => 'n', 'ņ' => 'n', 'ŉ' => 'n', 'ŋ' => 'n', 'ò' => 'o',
        'ó' => 'o', 'ô' => 'o', 'õ' => 'o', 'ø' => 'o', 'ō' => 'o', 'ő' => 'o',
        'ŏ' => 'o', 'ŕ' => 'r', 'ř' => 'r', 'ŗ' => 'r', 'ś' => 's', 'š' => 's',
        'ť' => 't', 'ù' => 'u', 'ú' => 'u', 'û' => 'u', 'ū' => 'u', 'ů' => 'u',
        'ű' => 'u', 'ŭ' => 'u', 'ũ' => 'u', 'ų' => 'u', 'ŵ' => 'w', 'ÿ' => 'y',
        'ý' => 'y', 'ŷ' => 'y', 'ż' => 'z', 'ź' => 'z', 'ž' => 'z', 'Α' => 'A',
        'Ά' => 'A', 'Ἀ' => 'A', 'Ἁ' => 'A', 'Ἂ' => 'A', 'Ἃ' => 'A', 'Ἄ' => 'A',
        'Ἅ' => 'A', 'Ἆ' => 'A', 'Ἇ' => 'A', 'ᾈ' => 'A', 'ᾉ' => 'A', 'ᾊ' => 'A',
        'ᾋ' => 'A', 'ᾌ' => 'A', 'ᾍ' => 'A', 'ᾎ' => 'A', 'ᾏ' => 'A', 'Ᾰ' => 'A',
        'Ᾱ' => 'A', 'Ὰ' => 'A', 'ᾼ' => 'A', 'Β' => 'B', 'Γ' => 'G', 'Δ' => 'D',
        'Ε' => 'E', 'Έ' => 'E', 'Ἐ' => 'E', 'Ἑ' => 'E', 'Ἒ' => 'E', 'Ἓ' => 'E',
        'Ἔ' => 'E', 'Ἕ' => 'E', 'Ὲ' => 'E', 'Ζ' => 'Z', 'Η' => 'I', 'Ή' => 'I',
        'Ἠ' => 'I', 'Ἡ' => 'I', 'Ἢ' => 'I', 'Ἣ' => 'I', 'Ἤ' => 'I', 'Ἥ' => 'I',
        'Ἦ' => 'I', 'Ἧ' => 'I', 'ᾘ' => 'I', 'ᾙ' => 'I', 'ᾚ' => 'I', 'ᾛ' => 'I',
        'ᾜ' => 'I', 'ᾝ' => 'I', 'ᾞ' => 'I', 'ᾟ' => 'I', 'Ὴ' => 'I', 'ῌ' => 'I',
        'Θ' => 'T', 'Ι' => 'I', 'Ί' => 'I', 'Ϊ' => 'I', 'Ἰ' => 'I', 'Ἱ' => 'I',
        'Ἲ' => 'I', 'Ἳ' => 'I', 'Ἴ' => 'I', 'Ἵ' => 'I', 'Ἶ' => 'I', 'Ἷ' => 'I',
        'Ῐ' => 'I', 'Ῑ' => 'I', 'Ὶ' => 'I', 'Κ' => 'K', 'Λ' => 'L', 'Μ' => 'M',
        'Ν' => 'N', 'Ξ' => 'K', 'Ο' => 'O', 'Ό' => 'O', 'Ὀ' => 'O', 'Ὁ' => 'O',
        'Ὂ' => 'O', 'Ὃ' => 'O', 'Ὄ' => 'O', 'Ὅ' => 'O', 'Ὸ' => 'O', 'Π' => 'P',
        'Ρ' => 'R', 'Ῥ' => 'R', 'Σ' => 'S', 'Τ' => 'T', 'Υ' => 'Y', 'Ύ' => 'Y',
        'Ϋ' => 'Y', 'Ὑ' => 'Y', 'Ὓ' => 'Y', 'Ὕ' => 'Y', 'Ὗ' => 'Y', 'Ῠ' => 'Y',
        'Ῡ' => 'Y', 'Ὺ' => 'Y', 'Φ' => 'F', 'Χ' => 'X', 'Ψ' => 'P', 'Ω' => 'O',
        'Ώ' => 'O', 'Ὠ' => 'O', 'Ὡ' => 'O', 'Ὢ' => 'O', 'Ὣ' => 'O', 'Ὤ' => 'O',
        'Ὥ' => 'O', 'Ὦ' => 'O', 'Ὧ' => 'O', 'ᾨ' => 'O', 'ᾩ' => 'O', 'ᾪ' => 'O',
        'ᾫ' => 'O', 'ᾬ' => 'O', 'ᾭ' => 'O', 'ᾮ' => 'O', 'ᾯ' => 'O', 'Ὼ' => 'O',
        'ῼ' => 'O', 'α' => 'a', 'ά' => 'a', 'ἀ' => 'a', 'ἁ' => 'a', 'ἂ' => 'a',
        'ἃ' => 'a', 'ἄ' => 'a', 'ἅ' => 'a', 'ἆ' => 'a', 'ἇ' => 'a', 'ᾀ' => 'a',
        'ᾁ' => 'a', 'ᾂ' => 'a', 'ᾃ' => 'a', 'ᾄ' => 'a', 'ᾅ' => 'a', 'ᾆ' => 'a',
        'ᾇ' => 'a', 'ὰ' => 'a', 'ᾰ' => 'a', 'ᾱ' => 'a', 'ᾲ' => 'a', 'ᾳ' => 'a',
        'ᾴ' => 'a', 'ᾶ' => 'a', 'ᾷ' => 'a', 'β' => 'b', 'γ' => 'g', 'δ' => 'd',
        'ε' => 'e', 'έ' => 'e', 'ἐ' => 'e', 'ἑ' => 'e', 'ἒ' => 'e', 'ἓ' => 'e',
        'ἔ' => 'e', 'ἕ' => 'e', 'ὲ' => 'e', 'ζ' => 'z', 'η' => 'i', 'ή' => 'i',
        'ἠ' => 'i', 'ἡ' => 'i', 'ἢ' => 'i', 'ἣ' => 'i', 'ἤ' => 'i', 'ἥ' => 'i',
        'ἦ' => 'i', 'ἧ' => 'i', 'ᾐ' => 'i', 'ᾑ' => 'i', 'ᾒ' => 'i', 'ᾓ' => 'i',
        'ᾔ' => 'i', 'ᾕ' => 'i', 'ᾖ' => 'i', 'ᾗ' => 'i', 'ὴ' => 'i', 'ῂ' => 'i',
        'ῃ' => 'i', 'ῄ' => 'i', 'ῆ' => 'i', 'ῇ' => 'i', 'θ' => 't', 'ι' => 'i',
        'ί' => 'i', 'ϊ' => 'i', 'ΐ' => 'i', 'ἰ' => 'i', 'ἱ' => 'i', 'ἲ' => 'i',
        'ἳ' => 'i', 'ἴ' => 'i', 'ἵ' => 'i', 'ἶ' => 'i', 'ἷ' => 'i', 'ὶ' => 'i',
        'ῐ' => 'i', 'ῑ' => 'i', 'ῒ' => 'i', 'ῖ' => 'i', 'ῗ' => 'i', 'κ' => 'k',
        'λ' => 'l', 'μ' => 'm', 'ν' => 'n', 'ξ' => 'k', 'ο' => 'o', 'ό' => 'o',
        'ὀ' => 'o', 'ὁ' => 'o', 'ὂ' => 'o', 'ὃ' => 'o', 'ὄ' => 'o', 'ὅ' => 'o',
        'ὸ' => 'o', 'π' => 'p', 'ρ' => 'r', 'ῤ' => 'r', 'ῥ' => 'r', 'σ' => 's',
        'ς' => 's', 'τ' => 't', 'υ' => 'y', 'ύ' => 'y', 'ϋ' => 'y', 'ΰ' => 'y',
        'ὐ' => 'y', 'ὑ' => 'y', 'ὒ' => 'y', 'ὓ' => 'y', 'ὔ' => 'y', 'ὕ' => 'y',
        'ὖ' => 'y', 'ὗ' => 'y', 'ὺ' => 'y', 'ῠ' => 'y', 'ῡ' => 'y', 'ῢ' => 'y',
        'ῦ' => 'y', 'ῧ' => 'y', 'φ' => 'f', 'χ' => 'x', 'ψ' => 'p', 'ω' => 'o',
        'ώ' => 'o', 'ὠ' => 'o', 'ὡ' => 'o', 'ὢ' => 'o', 'ὣ' => 'o', 'ὤ' => 'o',
        'ὥ' => 'o', 'ὦ' => 'o', 'ὧ' => 'o', 'ᾠ' => 'o', 'ᾡ' => 'o', 'ᾢ' => 'o',
        'ᾣ' => 'o', 'ᾤ' => 'o', 'ᾥ' => 'o', 'ᾦ' => 'o', 'ᾧ' => 'o', 'ὼ' => 'o',
        'ῲ' => 'o', 'ῳ' => 'o', 'ῴ' => 'o', 'ῶ' => 'o', 'ῷ' => 'o', 'А' => 'A',
        'Б' => 'B', 'В' => 'V', 'Г' => 'G', 'Д' => 'D', 'Е' => 'E', 'Ё' => 'E',
        'Ж' => 'Z', 'З' => 'Z', 'И' => 'I', 'Й' => 'I', 'К' => 'K', 'Л' => 'L',
        'М' => 'M', 'Н' => 'N', 'О' => 'O', 'П' => 'P', 'Р' => 'R', 'С' => 'S',
        'Т' => 'T', 'У' => 'U', 'Ф' => 'F', 'Х' => 'K', 'Ц' => 'T', 'Ч' => 'C',
        'Ш' => 'S', 'Щ' => 'S', 'Ы' => 'Y', 'Э' => 'E', 'Ю' => 'Y', 'Я' => 'Y',
        'а' => 'A', 'б' => 'B', 'в' => 'V', 'г' => 'G', 'д' => 'D', 'е' => 'E',
        'ё' => 'E', 'ж' => 'Z', 'з' => 'Z', 'и' => 'I', 'й' => 'I', 'к' => 'K',
        'л' => 'L', 'м' => 'M', 'н' => 'N', 'о' => 'O', 'п' => 'P', 'р' => 'R',
        'с' => 'S', 'т' => 'T', 'у' => 'U', 'ф' => 'F', 'х' => 'K', 'ц' => 'T',
        'ч' => 'C', 'ш' => 'S', 'щ' => 'S', 'ы' => 'Y', 'э' => 'E', 'ю' => 'Y',
        'я' => 'Y', 'ð' => 'd', 'Ð' => 'D', 'þ' => 't', 'Þ' => 'T', 'ა' => 'a',
        'ბ' => 'b', 'გ' => 'g', 'დ' => 'd', 'ე' => 'e', 'ვ' => 'v', 'ზ' => 'z',
        'თ' => 't', 'ი' => 'i', 'კ' => 'k', 'ლ' => 'l', 'მ' => 'm', 'ნ' => 'n',
        'ო' => 'o', 'პ' => 'p', 'ჟ' => 'z', 'რ' => 'r', 'ს' => 's', 'ტ' => 't',
        'უ' => 'u', 'ფ' => 'p', 'ქ' => 'k', 'ღ' => 'g', 'ყ' => 'q', 'შ' => 's',
        'ჩ' => 'c', 'ც' => 't', 'ძ' => 'd', 'წ' => 't', 'ჭ' => 'c', 'ხ' => 'k',
        'ჯ' => 'j', 'ჰ' => 'h',
    );
 
    return dm_str_replace(array_keys($transliteration), array_values($transliteration), $str);
}

/**
 * @param $str
 * @return string
 */
function simple_unaccent($str )
{
    $unwanted_array = array(    'Š'=>'S', 'š'=>'s', 'Ž'=>'Z', 'ž'=>'z', 'À'=>'A', 'Á'=>'A', 'Â'=>'A', 'Ã'=>'A', 'Ä'=>'A', 'Å'=>'A', 'Æ'=>'A', 'Ç'=>'C', 'È'=>'E', 'É'=>'E',
                                'Ê'=>'E', 'Ë'=>'E', 'Ì'=>'I', 'Í'=>'I', 'Î'=>'I', 'Ï'=>'I', 'Ñ'=>'N', 'Ò'=>'O', 'Ó'=>'O', 'Ô'=>'O', 'Õ'=>'O', 'Ö'=>'O', 'Ø'=>'O', 'Ù'=>'U',
                                'Ú'=>'U', 'Û'=>'U', 'Ü'=>'U', 'Ý'=>'Y', 'Þ'=>'B', 'ß'=>'Ss', 'à'=>'a', 'á'=>'a', 'â'=>'a', 'ã'=>'a', 'ä'=>'a', 'å'=>'a', 'æ'=>'a', 'ç'=>'c',
                                'è'=>'e', 'é'=>'e', 'ê'=>'e', 'ë'=>'e', 'ì'=>'i', 'í'=>'i', 'î'=>'i', 'ï'=>'i', 'ð'=>'o', 'ñ'=>'n', 'ò'=>'o', 'ó'=>'o', 'ô'=>'o', 'õ'=>'o',
                                'ö'=>'o', 'ø'=>'o', 'ù'=>'u', 'ú'=>'u', 'û'=>'u', 'ý'=>'y', 'þ'=>'b', 'ÿ'=>'y' );
    $str = strtr( $str, $unwanted_array );
    return $str;
}

/**
 * @param $string
 * @return string
 */
function comparableString($string)
{
    return strtolower(dm_str_replace(array(' ', ','), array('', '.'), simple_unaccent($string)));
}

function conserverSeulementNombre($string, int $nb_decimal = 2): float|int
{
    $number = null;
    if (preg_match('/^[\d\.]+/', $string, $matches)) {
        $number = floatval($matches[0]);
    }
    return empty($number) ? -1 : round($number, $nb_decimal);
}

/**
 * @param $string
 * @return string
 */
function conserverSeulementTexte($string)
{
    return dm_preg_replace("/[0-9.,-]/", "", $string);
}

/**
 * @param $string
 * @return string
 */
function extraireNombreMesure($string)
{
    if (is_numeric($string)) {
        return $string;
    } else {
        preg_match('/(\d+(?:\.\d*)?)\s*(\D+)/', $string, $m);
        return trim($m[1] ?? '');
    }
}

/**
 * @param $string
 * @return string
 */
function extraireUniteMesure($string)
{
    preg_match('/(\d+(?:\.\d*)?)\s*(.*)/', $string, $m);
    return trim($m[2] ?? '');
}

/**
 * @param $string
 * @return string
 */
function unitesMesuresEquivalentes($uniteMesure1, $uniteMesure2)
{
    if ($uniteMesure1 == $uniteMesure2) {
        return true;
    } else {
        $equivalences = getEquivalencesUnitesMesure();
        if (isset($equivalences[$uniteMesure1]) && isset($equivalences[$uniteMesure1][$uniteMesure2])) {
            return true;
        }
    }
    return false;
}

function getEquivalencesUnitesMesure()
{
    global $db;
    if (!isset($_SESSION['unites_mesures_equivalentes'])) {
        $sql = "select * from unites_mesures_equivalentes";
        $equivalences = $db->getAll($sql);
        foreach ($equivalences as $row) {
            $_SESSION['unites_mesures_equivalentes'][$row['unite_mesure']][$row['unite_mesure_equivalente']] = $row['unite_mesure_equivalente'];
            $_SESSION['unites_mesures_equivalentes'][$row['unite_mesure_equivalente']][$row['unite_mesure']] = $row['unite_mesure'];
        }
    }
    return $_SESSION['unites_mesures_equivalentes'];
}

/**
 * @param $string
 * @return string
 */
function convertToCheckboxValue($string)
{
	global $accepted_values_for_chekbox;
	$string_tmp = strtoupper(dm_trim($string));
	if (in_array($string_tmp, $accepted_values_for_chekbox['checked'])) {
		return txt('Oui');
	} else if (in_array($string_tmp, $accepted_values_for_chekbox['unchecked'])) {
		return txt('Non');
	} else {
		return $string;
	}
}

/**
 * @param $string
 * @return int|string
 */
function convertToIntCheckboxValue($string)
{
	global $accepted_values_for_chekbox;
	$string_tmp = strtoupper(dm_trim($string));
	if (in_array($string_tmp, $accepted_values_for_chekbox['checked'])) {
		return 1;
	} else if (in_array($string_tmp, $accepted_values_for_chekbox['unchecked'])) {
		return 0;
	} else {
		return $string;
	}
}

/**
 * @param $string
 * @return boolean|string
 */
function convertToBooleanCheckboxValue($string)
{
    global $accepted_values_for_chekbox;
    $string_tmp = strtoupper(dm_trim($string));
    if (in_array($string_tmp, $accepted_values_for_chekbox['checked'])) {
        return true;
    } else if (in_array($string_tmp, $accepted_values_for_chekbox['unchecked'])) {
        return false;
    } else {
        return $string;
    }
}

/**
 * @param $string
 * @return null|string|string[]
 */
function sanitizeString(string $string): string
{
    $res = dm_preg_replace('/\p{C}+/u', "", dm_trim($string));
	return $res ?? '';
}

/**
 * @param $nom
 * @param $url
 * @param $col
 * @param $order_by_col
 * @param $order_by_dir
 * @param bool $is_print
 * @return string
 */
function getOrderByLink($nom, $url, $col, $order_by_col, $order_by_dir, $is_print = false)
{
	$link = '<a href="' . $url . '&order_by_col=' . $col . '&order_by_dir=' . ($order_by_col == $col && $order_by_dir == 'asc' ? 'desc' : 'asc') . '" class="order_link' . ($order_by_col == $col ? ' active' : '') . '">';
	$link .= $nom;

	$img_src = 'triangle_up_down.png';
	if ($order_by_col == $col) {
		if ($order_by_dir == 'asc') {
			$img_src = 'triangle_down.png';
		} else {
			$img_src = 'triangle_up.png';
		}
	}
	if (!$is_print) {
		$link .= '<img src="css/images/icons/dark/' . $img_src . '" />';
	}
	$link .= '</a>';

	return $link;
}

/**
 * @param $nom
 * @param $function
 * @param $col
 * @param $order_by_col
 * @param $order_by_dir
 * @param bool $is_print
 * @return string
 */
function getOrderByFunction($nom, $function, $col, $order_by_col, $order_by_dir, $is_print = false)
{
    $function = str_replace(['[order_by_col]', '[order_by_dir]'], [$col, ($order_by_col == $col && $order_by_dir == 'asc' ? 'desc' : 'asc')], $function);
	$link = '<a onclick="' . $function . '" class="order_link' . ($order_by_col == $col ? ' active' : '') . '">';
	$link .= $nom;

	$img_src = 'triangle_up_down.png';
	if ($order_by_col == $col) {
		if ($order_by_dir == 'asc') {
			$img_src = 'triangle_down.png';
		} else {
			$img_src = 'triangle_up.png';
		}
	}
	if (!$is_print) {
		$link .= '<img src="css/images/icons/dark/' . $img_src . '" />';
	}
	$link .= '</a>';

	return $link;
}

/**
 * @param $field
 * @param $array
 * @param string $direction
 * @return bool
 */
function sortBy($field, &$array, $direction = 'asc', $preserveKey = false)
{
	$temp = [];
	foreach ($array as $key => $value)
		$temp[$value[$field] . "oldkey" . $key] = $value; //concatenate something unique to make sure two equal weights don't overwrite each other
	ksort($temp); // or ksort($temp, SORT_NATURAL); see paragraph above to understand why
	if (!$preserveKey) {
		$array = array_values($temp);
	}
	unset($temp);

	/*uasort($array, create_function('$a, $b', '
		$a = $a["' . $field . '"];
		$b = $b["' . $field . '"];

		if ($a == $b)
		{
			return 0;
		}

		return ($a ' . ($direction == 'desc' ? '>' : '<') .' $b) ? -1 : 1;
	'));*/

	return true;
}

/**
 * @param $liste
 * @param $liste_to_compare_to
 * @return array
 */
function getCompareMeltedList($liste, $liste_to_compare_to)
{
	$liste_return = array();
	$liste_to_upload = array();
	$liste_to_download = array();
	foreach ($liste as $compare_data => $ligne) {
		if (!isset($liste_to_compare_to[$compare_data])) {
			$ligne->statut_comparaison = 0;
			$liste_to_upload[] = $ligne;
		} else {
			$ligne->statut_comparaison = 1;
			unset($liste_to_compare_to[$compare_data]);
			$liste_return[] = $ligne;
		}
	}

	foreach ($liste_to_compare_to as $compare_data => $ligne) {
		$ligne->statut_comparaison = -1;
		$liste_to_download[] = $ligne;
	}

	return array_merge($liste_to_upload, $liste_to_download, $liste_return);
}

/**
 * @param $match
 * @return string
 */
function replace_unicode_escape_sequence($match) {
    return mb_convert_encoding(pack('H*', $match[1]), 'UTF-8', 'UCS-2BE');
}

/**
 * @param $str
 * @return string
 */
function unicode_decode($str) {
    return !isset($str) ? '' : preg_replace_callback('/\\\\u([0-9a-f]{4})/i', 'replace_unicode_escape_sequence', $str);
}

/**
 * @param $dirPath
 */
function viderDir($dirPath) {
    if (! is_dir($dirPath)) {
        throw new InvalidArgumentException("$dirPath must be a directory");
    }
    if (dm_substr($dirPath, dm_strlen($dirPath) - 1, 1) != '/') {
        $dirPath .= '/';
    }
    $files = glob($dirPath . '*', GLOB_MARK);
    foreach ($files as $file) {
        if (is_dir($file)) {
            viderDir($file);
            rmdir($file);
        } else {
            unlink($file);
        }
    }
}

/**
 * @return array
 */
function system_extension_mime_types() {
    # Returns the system MIME type mapping of extensions to MIME types, as defined in /etc/mime.types.
    $out = array();
    $file = fopen('/etc/mime.types', 'r');
    while (($line = fgets($file)) !== false) {
        $line = dm_trim(dm_preg_replace('/#.*/', '', $line));
        if (!$line)
            continue;
        $parts = dm_preg_split('/\s+/', $line);
        if (count($parts) == 1)
            continue;
        $type = array_shift($parts);
        foreach($parts as $part)
            $out[$part] = $type;
    }
    fclose($file);
    return $out;
}

/**
 * @param $file
 * @return mixed|null
 */
function system_extension_mime_type($file) {
    # Returns the system MIME type (as defined in /etc/mime.types) for the filename specified.
    #
    # $file - the filename to examine
    static $types;
    if (!isset($types))
        $types = system_extension_mime_types();
    $ext = pathinfo($file, PATHINFO_EXTENSION);
    if (!$ext)
        $ext = $file;
    $ext = strtolower($ext);
    return $types[$ext] ?? null;
}

/**
 * @return array
 */
function system_mime_type_extensions() {
    # Returns the system MIME type mapping of MIME types to extensions, as defined in /etc/mime.types (considering the first
    # extension listed to be canonical).
    $out = array();
    $file = fopen('/etc/mime.types', 'r');
    while (($line = fgets($file)) !== false) {
        $line = dm_trim(dm_preg_replace('/#.*/', '', $line));
        if (!$line)
            continue;
        $parts = dm_preg_split('/\s+/', $line);
        if (count($parts) == 1)
            continue;
        $type = array_shift($parts);
        if (!isset($out[$type]))
            $out[$type] = array_shift($parts);
    }
    fclose($file);
    return $out;
}

/**
 * @param $type
 * @return mixed|null
 */
function system_mime_type_extension($type) {
    # Returns the canonical file extension for the MIME type specified, as defined in /etc/mime.types (considering the first
    # extension listed to be canonical).
    #
    # $type - the MIME type
    static $exts;
    if (!isset($exts))
        $exts = system_mime_type_extensions();
    return isset($exts[$type]) ? $exts[$type] : null;
}

/**
 * @return string
 */
function generer_password() {
    $alphabet = "abcdefghijklmnopqrstuwxyzABCDEFGHIJKLMNOPQRSTUWXYZ0123456789";
    $pass = array(); //remember to declare $pass as an array
    $alphaLength = dm_strlen($alphabet) - 1; //put the length -1 in cache
    for ($i = 0; $i < 16; $i++) {
        $n = rand(0, $alphaLength);
        $pass[] = $alphabet[$n];
    }
    return dm_implode('', $pass); //turn the array into a string
}

function callNodeWebservice($service, $formulaire_id, $fiche_id) {
    global $CodeClient, $NodeWebServiceServerUrl;
    if (isset($NodeWebServiceServerUrl)) {
        ?>
        <script>
			$.post("<?=$NodeWebServiceServerUrl?><?= $service ?>/<?= $CodeClient . "/" . getIdUsager() . "/" . getOuvrageId() . "/$formulaire_id/$fiche_id" ?>");
        </script>
        <?php
//	    $url = $NodeWebServiceServerUrl . "$service/$CodeClient/" . getIdUsager() . "/" . getOuvrageId() . "/$formulaire_id/$fiche_id";
//	    exec("curl -X POST $url 2>&1", $output, $ret);
//        if (isset($output)) {
//            envoiCourriel('<EMAIL>', 'Debug', $url . '<br><br>' . $ret . '<br><br>' . getDebug($output));
//        }
    }
}

/*
 * Executes a PHP page asynchronously so the current page does not have to wait for it to     finish running.
 *
 */
function post_async($url, array $params)
{
	foreach ($params as $key => &$val) {
		if (is_array($val)) $val = dm_implode(',', $val);
		$post_params[] = $key.'='.urlencode($val);
	}
	$post_string = dm_implode('&', $post_params);

	$parts=parse_url($url);

	$fp = fsockopen($parts['host'],
		isset($parts['port'])?$parts['port']:80,
		$errno, $errstr, 30);

	$out = "POST ".$parts['path']." HTTP/1.1\r\n";
	$out.= "Host: ".$parts['host']."\r\n";
	$out.= "Content-Type: application/x-www-form-urlencoded\r\n";
	$out.= "Content-Length: ".dm_strlen($post_string)."\r\n";
	$out.= "Connection: Close\r\n\r\n";
	if (isset($post_string)) $out.= $post_string;

	fwrite($fp, $out);
	fclose($fp);
}

function printTrace()
{
	$trace = '';
	try {
		throw new Exception();
	}
	catch(Exception $e) {
		$trace = $e->getTraceAsString();
	}
	print $trace;
}

function getAutorizedEquipementsGBM(): array
{
    $sql = "select equipements_bio_medicaux_types.id 
            from equipements_bio_medicaux_types
            join equipements_bio_medicaux_types_categories ebmtc on equipements_bio_medicaux_types.equipement_bio_medicaux_type_categorie_id = ebmtc.id
            join equipements_bio_medicaux_types_categories_usagers u on ebmtc.id = u.equipement_bio_medicaux_type_categorie_id
            where u.usager_id = ?";
    return db::instance()->getCol($sql, [getIdUsager()], true);
}

function getAutorizedFichesForStatut($projet_id = null): array
{
    $where = "";
    $data = [getIdUsager()];
    if ($projet_id != null) {
        $where .= " and f.ouvrage_id in (select ouvrage_id from projets where id = ?)";
        $data[] = $projet_id;
    }
    $sql = "select f.id 
            from fiches f
            left join fiches_statuts_usagers u on u.fiche_statut_id = f.fiche_statut_id and u.usager_id = ?
            where f.fiche_statut_id is null or u.usager_id is not null $where";
    return db::instance()->getCol($sql, $data, true);
}

function isAutorizedFicheForStatut($fiche_id)
{
	global $db;

    $sql = "select count(f.id) 
            from fiches f
            left join fiches_statuts_usagers u on u.fiche_statut_id = f.fiche_statut_id and u.usager_id = ?
            where (f.fiche_statut_id is null or u.usager_id is not null) and f.id = ?";
    $nb = $db->getOne($sql, array(getIdUsager(), $fiche_id));
    return (int)$nb === 1;
}

function getDataArrayFromExcel($fileData) {
    $data = [];
	$spreadsheet = IOFactory::load($fileData['tmp_name']);
	foreach ($spreadsheet->getWorksheetIterator() as $iter_sheet => $worksheet) {
		if ($iter_sheet > 0) {
			break;
		}
		$highestRow = $worksheet->getHighestDataRow(); // e.g. 10
		$highestColumn = $worksheet->getHighestDataColumn(); // e.g 'F'
		$highestColumnIndex = Coordinate::columnIndexFromString($highestColumn);
		$row_titre = 1;
		for ($row = 1; $row <= $highestRow; ++$row) {
			for ($col = 1; $col <= $highestColumnIndex; ++$col) {
				$cell = $worksheet->getCellByColumnAndRow($col, $row);
				$val = sanitizeString((string)$cell->getCalculatedValue());
				if ($row > $row_titre) {
					$data[$row][] = $val;
				}
			}
		}
	}
    return $data;
}

function curl($url) {
	// create curl resource
	$ch = curl_init();
	// set url
	curl_setopt($ch, CURLOPT_URL, $url);

	curl_setopt($ch, CURLOPT_TIMEOUT, 5);
	curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 5);

	//return the transfer as a string
	curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

	// $output contains the output string
	if ( ! $output = curl_exec($ch)) {
		trigger_error(curl_error($ch));
	}

	// close curl resource to free up system resources
	curl_close($ch);

	return $output;
}

function flattenArbo($element)
{
	$flatArbo = array();
	foreach ($element as $key => $node) {
		if (is_array($node) && array_key_exists('items', $node)) {
			$flatArbo = array_merge($flatArbo, flatten($node['items']));
			unset($node['items']);
			$flatArbo[] = $node;
		} else {
			$flatArbo[] = $node;
		}
	}

	return $flatArbo;
}

function getListePageStandard($table, $type) {
    ?>
    <style>
        h1.putLeft {
            display: none;
        }
    </style>
    <script src="js/admin.editSystemData.js?ts=<?=filemtime('js/admin.editSystemData.js')?>"></script>
    <script>
		var ongletToLoad = '<?=$table?>';
		var txt_confDel = '';
		var typeTable = '<?=$type?>';
		$(document).ready(function() {
			txt_confDel = confirmationMessages['<?=$table?>'];
		});
    </script>
    <div id="content" style="padding: 5px!important"></div>
    <?php
}

function isMobile() {
	$useragent = $_SERVER['HTTP_USER_AGENT'] ?? 'no mobile';
	return preg_match('/(android|bb\d+|meego).+mobile|avantgo|bada\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od|ad)|iris|kindle|lge |maemo|midp|mmp|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\.(browser|link)|vodafone|wap|windows (ce|phone)|xda|xiino/i',$useragent)||preg_match('/1207|6310|6590|3gso|4thp|50[1-6]i|770s|802s|a wa|abac|ac(er|oo|s\-)|ai(ko|rn)|al(av|ca|co)|amoi|an(ex|ny|yw)|aptu|ar(ch|go)|as(te|us)|attw|au(di|\-m|r |s )|avan|be(ck|ll|nq)|bi(lb|rd)|bl(ac|az)|br(e|v)w|bumb|bw\-(n|u)|c55\/|capi|ccwa|cdm\-|cell|chtm|cldc|cmd\-|co(mp|nd)|craw|da(it|ll|ng)|dbte|dc\-s|devi|dica|dmob|do(c|p)o|ds(12|\-d)|el(49|ai)|em(l2|ul)|er(ic|k0)|esl8|ez([4-7]0|os|wa|ze)|fetc|fly(\-|_)|g1 u|g560|gene|gf\-5|g\-mo|go(\.w|od)|gr(ad|un)|haie|hcit|hd\-(m|p|t)|hei\-|hi(pt|ta)|hp( i|ip)|hs\-c|ht(c(\-| |_|a|g|p|s|t)|tp)|hu(aw|tc)|i\-(20|go|ma)|i230|iac( |\-|\/)|ibro|idea|ig01|ikom|im1k|inno|ipaq|iris|ja(t|v)a|jbro|jemu|jigs|kddi|keji|kgt( |\/)|klon|kpt |kwc\-|kyo(c|k)|le(no|xi)|lg( g|\/(k|l|u)|50|54|\-[a-w])|libw|lynx|m1\-w|m3ga|m50\/|ma(te|ui|xo)|mc(01|21|ca)|m\-cr|me(rc|ri)|mi(o8|oa|ts)|mmef|mo(01|02|bi|de|do|t(\-| |o|v)|zz)|mt(50|p1|v )|mwbp|mywa|n10[0-2]|n20[2-3]|n30(0|2)|n50(0|2|5)|n7(0(0|1)|10)|ne((c|m)\-|on|tf|wf|wg|wt)|nok(6|i)|nzph|o2im|op(ti|wv)|oran|owg1|p800|pan(a|d|t)|pdxg|pg(13|\-([1-8]|c))|phil|pire|pl(ay|uc)|pn\-2|po(ck|rt|se)|prox|psio|pt\-g|qa\-a|qc(07|12|21|32|60|\-[2-7]|i\-)|qtek|r380|r600|raks|rim9|ro(ve|zo)|s55\/|sa(ge|ma|mm|ms|ny|va)|sc(01|h\-|oo|p\-)|sdk\/|se(c(\-|0|1)|47|mc|nd|ri)|sgh\-|shar|sie(\-|m)|sk\-0|sl(45|id)|sm(al|ar|b3|it|t5)|so(ft|ny)|sp(01|h\-|v\-|v )|sy(01|mb)|t2(18|50)|t6(00|10|18)|ta(gt|lk)|tcl\-|tdg\-|tel(i|m)|tim\-|t\-mo|to(pl|sh)|ts(70|m\-|m3|m5)|tx\-9|up(\.b|g1|si)|utst|v400|v750|veri|vi(rg|te)|vk(40|5[0-3]|\-v)|vm40|voda|vulc|vx(52|53|60|61|70|80|81|83|85|98)|w3c(\-| )|webc|whit|wi(g |nc|nw)|wmlb|wonu|x700|yas\-|your|zeto|zte\-/i',dm_substr($useragent,0,4));
}

function isIPad() {
	$useragent=($_SERVER['HTTP_USER_AGENT']);
	return preg_match('/ipad/i',$useragent);
}

function setJSTableData($className, $table, $type, $liste) {
	global $tables, $TablesCategoriesPermissions, $TablesFancyBig;

    Perf::instance()->addTime('setJSTableData: ' . __LINE__);

	$isMasterTable = isset($tables['master'][$table]) && $table <> 'usagers';
	$sClass = 'dataTableShortTitle';
	if($sClass == 'dataTableShortTitle' && (isMaster() || havePermission('can_edit_documents')))
	{
		$sClass = 'dataTableAvgtTitle';
	}

    Perf::instance()->addTime('setJSTableData: ' . __LINE__);

	$formulaires_listes = array();
	if (method_exists($className, 'getFormulairesListes')
		&& is_callable(array($className, 'getFormulairesListes')))
	{
		$formulaires_listes = $className::getFormulairesListes($className);
	}

	$usagerInfo = getUsager();


    Perf::instance()->addTime('setJSTableData: ' . __LINE__);
	?>
    var aoColumns = [
	<?php $className::getDynamicTableTitle() ?>
    { "sTitle": "<?= ($table == 'notifications_acquisitions_projets_jalons' ? txt('Statut') : '') ?>", "sClass": "<?=$sClass?>" }
    ];

    var aaData = [
	<?php
	foreach($liste as $i => $ligne) {
		$conditionDelete = false;
		if (GestionnaireRoute::instance()->haveAccesRoute("delete-$table")) {
			$conditionDelete = true;
        }
        if (in_array($ligne->id, array(888888, 999999))) {
	        $conditionDelete = false;
        }
        ?>
        [
		<?php
		$ligne->getDynamicTableContent();
		$is_comparaison_actif = isset($ligne->statut_comparaison) && in_array($ligne->statut_comparaison, array(-1, 0));

		if(!isDBMaster() && $isMasterTable && !isMaster())
		{
			?>
            , '<?=($ligne->isValide == 0 ? '<img src="css/images/icons/dark/exclamation.png" class="exclamation" />' : '')?>'
			<?
		}
		else
		{
			?>
            , ''
			<?php
			if(!$is_comparaison_actif)
			{
                if ($table == 'notifications_acquisitions_projets_jalons') {
                    if (!isset($ligne->ts_lu)) {
                        ?>
                        + '<a onclick="parent.window.location = \'index.php?section=acquisition&module=notifications&lu=<?= $ligne->id ?>\'" style="cursor: pointer;" title="<?= txt('Considérer') ?>"><img src="css/images/icons/dark/tick.png"/></a><span style="display: none;">bgColorYellow</span>'
                        <?php
                    } else {
                        ?>
                        + '<a onclick="parent.window.location = \'index.php?section=acquisition&module=notifications&nonlu=<?= $ligne->id ?>\'" style="cursor: pointer;" title="<?= txt('Marquer comme non considérée') ?>"><img src="css/images/icons/dark/bended_arrow_left.png"/></a>'
                        <?php
                    }
                } else {
                    if (!isMaster() && $table == "fiches_statuts" && GestionnaireRoute::instance()->haveAccesRoute('admin-edit-fiches_statuts-edition')) {
                        if ($ligne->id <> 999999 || havePermission('desactivation_fiche')) {
                            ?>
                            + '<a href="index.php?section=admin&module=edit&table=<?=$table?>&id=<?=$ligne->id?>&type=<?=$type?>" class="dle_btn <?= (in_array($table, $TablesFancyBig) ? 'dle_edit_fancy_big' : 'dle_edit') ?>" title="<?=txt('Statuts de fiche')?>"><img src="css/images/icons/dark/create_write.png" /></a>'
                            <?php
                        }
                    } else if (!isMaster() && $table == "equipements_bio_medicaux_types" && GestionnaireRoute::instance()->haveAccesRoute("admin-edit-$table-edition") && in_array($ligne->id, getAutorizedEquipementsGBM()) && ($ligne->is_depot_vrouille == 0 || IsClientModeEtendu())) {
                        ?>
                        + '<a href="index.php?section=admin&module=edit&table=<?=$table?>&id=<?=$ligne->id?>&type=<?=$type?>&only_cat=1" class="dle_btn <?= (in_array($table, $TablesFancyBig) ? 'dle_edit_fancy_big' : 'dle_edit') ?>" title="<?=txt('Paramètres fondamentaux')?>"><img src="css/images/icons/dark/create_write.png" /></a>'
                        <?php
                    } else if(in_array($table, array('formulaires_listes', 'gabarits_exports', 'correlations_champs_externes', 'imports', 'formulaires_readonly', 'champs_textes_denormalises', 'echeanciers'))) {
                        ?>
                        + '<a href="index.php?section=admin&module=edit&table=<?=$table?>&id=<?=$ligne->id?>&type=<?=$type?>&YO=1" class="dle_btn <?= (in_array($table, $TablesFancyBig) ? 'dle_edit_fancy_big' : 'dle_edit') ?>" title="<?=txt('Paramètres fondamentaux')?>"><img src="css/images/icons/dark/create_write.png" /></a>'
                        <?php
                    } else if (GestionnaireRoute::instance()->haveAccesRoute("admin-edit-$table-edition")) {
                        ?>
                        + '<a href="index.php?section=admin&module=edit&table=<?=$table?>&id=<?=$ligne->id?>&type=<?=$type?>" class="dle_btn <?= (in_array($table, $TablesFancyBig) ? 'dle_edit_fancy_big' : 'dle_edit') ?>"><img src="css/images/icons/dark/create_write.png" /></a>'
                        <?php
                    }

                    if(count($formulaires_listes) > 0 && $table == "equipements_bio_medicaux_types" && GestionnaireRoute::instance()->haveAccesRoute("admin-edit-formulaires_listes_parametres-edition") && in_array($ligne->id, getAutorizedEquipementsGBM()))
                    {
                        ?>
                        + '<a href="index.php?section=admin&module=edit&table=formulaires_listes_parametres&id=<?=$formulaires_listes[0]->id?>&type=client&liste_item_id=<?=$ligne->id?>&table_to_reload=<?=$formulaires_listes[0]->nom_table?>" class="dle_btn dle_edit" title="<?=txt('Requis techniques')?>"><img src="css/images/icons/dark/tag.png" /></a>'
                        <?php
                    }

                    if( $table == "equipements_bio_medicaux_types" && GestionnaireRoute::instance()->haveAccesRoute("admin-edit-equipements_bio_medicaux_types-edition-document_edition") && in_array($ligne->id, getAutorizedEquipementsGBM()))
                    {
                        ?>
                        + '<a href="index.php?section=admin&module=edit&table=<?=$table?>&id=<?=$ligne->id?>&type=<?=$type?>&document_edition=1" class="dle_btn dle_edit" style="<?=($ligne->nb_document == 0 ? 'opacity: 0.3;' : '')?>" title="<?=txt('Documents et images')?>"><img src="css/images/icons/dark/folder.png" /></a>'
                        <?php
                    }
                    if (count($formulaires_listes) > 0 && $table == "equipements_bio_medicaux_types" && GestionnaireRoute::instance()->haveAccesRoute("admin-edit-equipements_bio_medicaux_types-edition-only_budget") && in_array($ligne->id, getAutorizedEquipementsGBM()) && ($ligne->is_depot_vrouille == 0 || IsClientModeEtendu())) {
                        ?>
                        + '<a href="index.php?section=admin&module=edit&table=equipements_bio_medicaux_types&table1=formulaires_listes_parametres&id=<?=$ligne->id?>&type=client&only_budget=1&table_to_reload=<?=$formulaires_listes[0]->nom_table?>&mode=fiche" class="dle_btn dle_edit" title="<?=txt('Paramètres budgétaires')?>"><img src="css/images/icons/dark/price_tag.png" /></a>'
                        <?php
                    }
                    if($table == 'procedures_entretiens' && is_file(procedurePath($ligne->id)) && !isDBMaster())
                    {
                        ?>
                        + '<a href="index.php?section=procedure&module=download&id=<?=$ligne->id?>" class="dle_btn"><img src="css/images/icons/dark/pdf_document.png" /></a>'
                        <?php
                    }
                    if( $table == "equipements_bio_medicaux_types" && in_array($ligne->id, getAutorizedEquipementsGBM()) && GestionnaireRoute::instance()->haveAccesRoute("admin-edit-equipements_bio_medicaux_types-edition-is_duplication") && isset($formulaires_listes[0]))
                    {
                        ?>
                        + '<a href="index.php?section=admin&module=edit&table=equipements_bio_medicaux_types&table1=formulaires_listes_parametres&id=0&source_id=<?=$ligne->id?>&type=client&liste_item_id=<?=$ligne->id?>&table_to_reload=<?=$formulaires_listes[0]->nom_table?>&mode=fiche&is_duplication=1" class="dle_btn dle_edit" title="<?=txt('Dupliquer l\'élément')?>"><img src="css/images/icons/dark/duplicate.png" /></a>'
                        <?php
                    }
                    if(!in_array($table, $TablesCategoriesPermissions) && ((($table == 'usagers' && $ligne->id <> $usagerInfo->id) || ($type <> 'master')) && (isMaster() || havePermission('suppression_librairie')) && !isDBMaster()) || in_array($table, array('formulaires_listes', 'imports', 'gabarits_exports', 'correlations_champs_externes', 'champs_textes_denormalises', 'champs_textes_exports', 'formulaires_readonly', 'echeanciers', 'pseudos', 'formulaires_limitations', 'appels_locaux_generaux_blocs', 'appels_locaux_generaux_etages', 'appels_locaux_generaux_zones', 'champs_textes_denormalises', 'correlations_champs_externes', 'gabarits_exports', 'imports', 'champs_textes_exports', 'motifs_changements', 'pseudos', 'reseaux', 'formulaires_readonly', 'acquisitions_projets_groupes_usagers')))
                    {
                        if($conditionDelete)
                        {
                            ?>
                            + '<a href="javascript: void(0);" class="dle_btn dle_delete" tableName="<?=$table?>" idElement="<?=$ligne->id?>" data-nb_appro="<?=(isset($ligne->nb_appro) ? $ligne->nb_appro : 0)?>" title="<?=txt('Supprimer l\'élément')?>"><img src="css/images/icons/dark/trashcan.png" /></a>'
                            <?php
                        }
                        if ($table == 'usagers' && GestionnaireRoute::instance()->haveAccesRoute('personnifier')) {
                            ?>
                            + '<a href="index.php?action=personnifier&usager_id=<?=$ligne->id?>" class="personnifier"><img src="css/images/icons/dark/magnifying_glass.png" /></a>'
                            <?php
                        }
                    }
                    if(!isDBMaster() && $isMasterTable && $ligne->isValide == 0)
                    {
                        ?>
                        + '<img src="css/images/icons/dark/exclamation.png" class="exclamation" />'
                        <?php
                    }
                    if($table == 'imports' && $ligne->type_import == 'excel')
                    {
                        ?>
                        + '<a href="index.php?section=admin&module=import_liste_for_import&id=<?=$ligne->id?>" style="position: relative; top: 0; left: 0;" title="<?=txt('Importer')?>" class="dle_btn dle_edit"><img src="css/images/icons/dark/arrow_down_left.png" /></a>'
                        + '<a href="index.php?action=export_excel_for_import&import_id=<?=$ligne->id?>" style="position: relative; top: 0; left: 0;" title="<?=txt('Exporter')?>" class="exporter"><img src="css/images/icons/dark/arrow_up_right.png" /></a>'
                        <?php
                    }
                    if($table == 'formulaires_listes')
                    {
                        ?>
                        + '<a href="index.php?section=admin&module=edit&table=<?=$table?>&id=<?=$ligne->id?>&type=<?=$type?>&mode=cloner" class="dle_btn <?= (in_array($table, $TablesFancyBig) ? 'dle_edit_fancy_big' : 'dle_edit') ?>" style="position: relative; top: 0; left: 0;" title="<?=txt('Cloner')?>"><img src="css/images/icons/dark/duplicate.png" /></a>'
                        <?php
                    }
                }
            }
            else if($ligne->statut_comparaison == 0 && !$className::COMPARE_MANUALLY)
            {
                ?>
                + '<a href="javascript: void(0);" class="sync_data" data-table_name="<?=$table?>" data-id_element="<?=$ligne->id?>" data-mode="upload"><img src="css/images/icons/dark/bended_arrow_right.png" /></a>'
                <?php
            }
            else if($ligne->statut_comparaison == -1)
            {
                if($className::COMPARE_MANUALLY)
                {
                    ?>
                    + '<a href="index.php?section=admin&amp;module=edit&amp;table=<?=$table?>&amp;type=client&amp;id=0&amp;compare_data=<?=urlencode($ligne->compare_data)?>" class="dle_btn dle_edit"><img src="css/images/icons/dark/bended_arrow_left.png" /></a>'
                    <?php
                }
                else
                {
                    ?>
                    + '<a href="javascript: void(0);" class="sync_data" data-table_name="<?=$table?>" data-id_element="<?=$ligne->id?>" data-mode="download"><img src="css/images/icons/dark/bended_arrow_left.png" /></a>'
                    <?php
                }
            }
            if($table == 'usagers' && GestionnaireRoute::instance()->haveAccesRoute('envoi_courriel_usager'))
            {
                ?>
                + '<a href="index.php?action=envoi_courriel_usager&idUsager=<?=$ligne->id?>" onclick="if(confirm(\'<?=txt('Êtes-vous certain de vouloir envoyer le courriel ouverture de compte?')?>\')) { parent.window.location = this.href; return false; } else { return false; }" title="<?=txt('Envoi du courriel d\'ouverture de compte')?>"><img src="css/images/icons/dark/mail.png" /></a>'
                <?php
            }
            if ($table == 'exports_automatiques') {
                ?>
                + '<a href="<?= $ligne->getLienTelechargement() ?>" target="_blank" id="lienTelechargement" title="<?= txt('Exporter') ?>" style="cursor: pointer; position: relative; top: -3px; margin-left: 5px"><img src="css/images/icons/dark/DM_export_CSV.png" /></a>'
                <?php
            }
		}
		?>
        ],
		<?php
	}
	?>
    ];
	<?php

    Perf::instance()->addTime('setJSTableData: ' . __LINE__);
}

function arrayViderVeleursVides($array): array
{
    return array_filter($array, function($value) { return isset($value) && dm_strlen(dm_trim((string)$value)) > 0; } );
}

function exportSimpleXLSX($sheets_data, $filename)
{
	$spreadsheet = new Spreadsheet();

	foreach ($sheets_data as $sheet_key => $sheet)
	{
		if(!isset($activeSheet))
		{
			$activeSheet = $spreadsheet->getActiveSheet();
		}
		else
		{
			$activeSheet = $spreadsheet->createSheet();
		}

		$activeSheet->setTitle($sheet['titre']);
		$activeSheet->getParent()->getDefaultStyle()->getFont()->setName('Arial');
		$activeSheet->getParent()->getDefaultStyle()->getFont()->setSize(10);

		$l = 0;
		$c = 0;
		foreach($sheet['cols'] as $key => $col)
		{
			$activeSheet->getStyle(cellCoord($l, $c))->getFont()->setBold(true);
			$activeSheet->SetCellValue(cellCoord($l, $c), $col['titre']);
			$c++;
		}

		foreach ($sheet['data'] as $i => $ligne)
		{
			$l++;
			$c = 0;
			foreach($sheet['cols'] as $key => $col)
			{
				if($col['type'] == 'lien')
				{
					$activeSheet->SetCellValue(cellCoord($l, $c), ($ligne[$key]));
				}
				else if($col['type'] == 'string')
				{
					if(dm_strpos($ligne[$key], 'Total') !== false)
					{
						$activeSheet->getStyle(cellCoord($l, $c))->getFont()->setBold(true);
					}
					if (isset($col['multiligne']) && $col['multiligne']) {
						$activeSheet->getStyle(cellCoord($l, $c))->getAlignment()->setWrapText(true);
					}
					$activeSheet->setCellValueExplicit(cellCoord($l, $c), $ligne[$key], DataType::TYPE_STRING);

				}
				else if($col['type'] == 'int')
				{
					$activeSheet->SetCellValue(cellCoord($l, $c), (dm_strlen((string)($ligne[$key])) > 0 ? (int)($ligne[$key]) : ''));
				}
				else if($col['type'] == 'dollar')
				{
					if (is_numeric($ligne[$key])) {
						$activeSheet->getStyle(cellCoord($l, $c))->getNumberFormat()->setFormatCode('# ### ##0.00$');
						$activeSheet->SetCellValue(cellCoord($l, $c), (float)($ligne[$key]));
					} else {
						$activeSheet->setCellValueExplicit(cellCoord($l, $c), $ligne[$key], DataType::TYPE_STRING);
					}
				}
				else if($col['type'] == 'pourcent')
				{
					$activeSheet->getStyle(cellCoord($l, $c))->getNumberFormat()->setFormatCode('# ### ##0.0%');
					$activeSheet->SetCellValue(cellCoord($l, $c), (float)($ligne[$key]));
				}
				else if($col['type'] == 'formula')
				{
					$activeSheet->getStyle(cellCoord($l, $c))->getNumberFormat()->applyFromArray(array('code' => PHPExcel_Style_NumberFormat::FORMAT_PERCENTAGE_00));
					$activeSheet->SetCellValue(cellCoord($l, $c), "=IF(".cellCoord($l, ($c + $col['cols'][0])).">0,".cellCoord($l, ($c + $col['cols'][1]))."/".cellCoord($l, ($c + $col['cols'][0])).",0)");
				}
				else if($col['type'] == 'date')
				{
					$activeSheet->getStyle(cellCoord($l, $c))->getNumberFormat()->setFormatCode(PHPExcel_Style_NumberFormat::FORMAT_DATE_YYYYMMDD2);
					$activeSheet->SetCellValue(cellCoord($l, $c), (dm_strlen($ligne[$key]) > 0 ? PHPExcel_Shared_Date::PHPToExcel( $ligne[$key] ) : ''));
				}

				$c++;
			}
			unset($sheet['data'][$i]);
		}

		$c = 0;
		foreach($sheet['cols'] as $key => $col)
		{
			$activeSheet->getColumnDimension(cellCoord($l, $c, true))->setAutoSize(true);
			$c++;
		}
	}

	//------------------------------------------------------------------------------
	// Output
	//------------------------------------------------------------------------------
	header('Content-Disposition: attachment; filename='.$filename );
	header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
	header('Content-Transfer-Encoding: binary');
	header('Cache-Control: must-revalidate');
	header('Pragma: public');

	$objWriter = new Xlsx($spreadsheet);
	$objWriter->save('php://output');
    $spreadsheet->disconnectWorksheets();
    $spreadsheet->garbageCollect();
    unset($spreadsheet, $objWriter);
}

function cellCoord($row, $col, $returnOnlyCol = false, $returnOnlyRow = false)
{
	$int = (int)($col / 26);
	$frac = $col % 26;
	$chr1 = '';

	if ($int > 0) {
		$chr1 = chr(ord('A') + $int - 1);
	}

	$chr2 = chr(ord('A') + $frac);
	$row++;

	if ($returnOnlyCol)
	{
		return $chr1 . $chr2;
	}
	else if ($returnOnlyRow)
	{
		return $row;
	}
	return $chr1 . $chr2 . $row;
}

function exportDataSelonFormat($dataArray, $sheet_title, $file_name, $formatPourExcel = 'standard', $assoc_col_cols = [], $assoc_col_champ = [], $format = 'xlsx', $librairie = 'PhpSpreadsheet')
{
    global $GlobalDataArray;
    $GlobalDataArray = $dataArray;
	switch ($format) {
		case 'xlsx':
			exportExcelFromDataArray($dataArray, $sheet_title, $file_name, $formatPourExcel, $assoc_col_cols, $assoc_col_champ, false, null, $librairie);
			break;
		case 'json':
			exportDataJson($dataArray, $file_name);
			break;
		case 'csv':
			exportExcelFromDataArray($dataArray, $sheet_title, $file_name, $formatPourExcel, $assoc_col_cols, $assoc_col_champ, true, null, $librairie);
			break;
		case 'xml':
			exportDataXml($dataArray, $file_name);
			break;
	}
}

function exportDataJson($dataArray, $file_name)
{
	header('Content-Type: application/json');
	header('Content-Disposition: attachment;filename="'.$file_name.'.json"');
	header('Cache-Control: max-age=0');
	header('Cache-Control: max-age=1');
	header ('Expires: Mon, 26 Jul 1997 05:00:00 GMT'); // Date in the past
	header ('Last-Modified: '.gmdate('D, d M Y H:i:s').' GMT'); // always modified
	header ('Cache-Control: cache, must-revalidate'); // HTTP/1.1
	header ('Pragma: public'); // HTTP/1.0

	print json_encode($dataArray);
}

function exportDataXml($dataArray, $file_name)
{
	header('Content-Type: application/xml');
	header('Content-Disposition: attachment;filename="'.$file_name.'.xml"');
	header('Cache-Control: max-age=0');
	header('Cache-Control: max-age=1');
	header ('Expires: Mon, 26 Jul 1997 05:00:00 GMT'); // Date in the past
	header ('Last-Modified: '.gmdate('D, d M Y H:i:s').' GMT'); // always modified
	header ('Cache-Control: cache, must-revalidate'); // HTTP/1.1
	header ('Pragma: public'); // HTTP/1.0

	$xmlDoc = new SimpleXMLElement('<root/>');
    foreach ($dataArray as $noLigne => $lignes) {
	    $xmlLigne = $xmlDoc->addChild("ligne_$noLigne");
	    foreach ($lignes as $noColonne => $valeur) {
		    $xmlLigne->addChild("colonne_$noColonne", htmlspecialchars(($valeur??'')));
	    }
    }
    print $xmlDoc->asXML();
}

function arrayToXml($array, $rootElement = null, $xml = null) {
	$_xml = $xml;

	// If there is no Root Element then insert root
	if ($_xml === null) {
		$_xml = new SimpleXMLElement($rootElement !== null ? $rootElement : '<root/>');
	}

	// Visit all key value pair
	foreach ($array as $k => $v) {

		// If there is nested array then
		if (is_array($v)) {

			// Call function for nested array
			arrayToXml($v, 'key_' . $k, $_xml->addChild('key_' . $k));
		}

		else {

			// Simply add child element.
			$_xml->addChild('key_' . $k, $v);
		}
	}

	return $_xml->asXML();
}

function exportExcelFromDataArray($dataArray, $sheet_title, $file_name, $format = 'standard', $assoc_col_cols = [], $assoc_col_champ = [], $isCsv = false, $path_to_save = null, $librairie = 'PhpSpreadsheet')
{
	if ($librairie == 'PhpSpreadsheet') {
        exportWithPhpSpreadsheet($dataArray, $sheet_title, $file_name, $format, $assoc_col_cols, $assoc_col_champ, $isCsv, $path_to_save);
    } else if ($librairie == 'OpenSpout') {
        exportWithOpenSpout($dataArray, $sheet_title, $file_name, $format, $assoc_col_cols, $assoc_col_champ, $isCsv, $path_to_save);
    }
}

function exportWithOpenSpout($dataArray, $sheet_title, $file_name, $format, $assoc_col_cols, $assoc_col_champ, $isCsv, $path_to_save)
{
    if ($isCsv) {
        $writer = new \OpenSpout\Writer\CSV\Writer();
        $options = new \OpenSpout\Writer\CSV\Options();
    } else {
        $writer = new \OpenSpout\Writer\XLSX\Writer();
        $options = new \OpenSpout\Writer\XLSX\Options();
    }

    $sheet_title = dm_str_replace("'", "", $sheet_title);
    $sheet_title = strip_accents(dm_substr($sheet_title, 0, 31));
    $sheet_title = dm_preg_replace("/[^a-zA-Z0-9\-_]+/", "", $sheet_title);

    $file_name = dm_str_replace("'", "", $file_name);
    $file_name = strip_accents($file_name);
    $file_name = dm_preg_replace("/[^a-zA-Z0-9\-_]+/", "", $file_name);

    $fullFileName = $file_name . '.' . ($isCsv ? 'csv' : 'xlsx');
    if (isset($path_to_save)) {
        $writer->openToFile($path_to_save . '/' . $fullFileName);
    } else {
        $writer->openToBrowser($fullFileName);
    }

    $maxLengths = [];
    foreach ($dataArray as $iterLigne => $rowData) {
        $cells = [];
        $rowData = array_values((array)$rowData);
        foreach($rowData as $iterCol => $cellValue) {
            $length = dm_strlen($cellValue);
            if (!isset($maxLengths[$iterCol]) || $length > $maxLengths[$iterCol]) {
                $maxLengths[$iterCol] = $length;
            }
            $cells[] = OpenSpoutCell::fromValue($cellValue);
        }
        $writer->addRow(new Row($cells));
        unset($iterLigne);
    }

    $sheet = $writer->getCurrentSheet();
    $sheet->setName($sheet_title);
    foreach ($maxLengths as $iterCol => $length) {
        $sheet->setColumnWidth(calculateColumnWidth($length), $iterCol+1);
    }

    $writer->close();
    unset($writer);
}

function calculateColumnWidth($length) {
    // This is a rough estimation, adjust the multiplier as needed
    return max($length * 1.1, 10);
}

function exportWithPhpSpreadsheet($dataArray, $sheet_title, $file_name, $format, $assoc_col_cols, $assoc_col_champ, $isCsv, $path_to_save)
{
    $spreadsheet = new Spreadsheet();

    $spreadsheet->setActiveSheetIndex(0);

    $sheet_title = dm_str_replace("'", "", $sheet_title);
    $sheet_title = strip_accents(dm_substr($sheet_title, 0, 31));
    $sheet_title = dm_preg_replace("/[^a-zA-Z0-9\-_]+/", "", $sheet_title);

    $file_name = dm_str_replace("'", "", $file_name);
    $file_name = strip_accents($file_name);
    $file_name = dm_preg_replace("/[^a-zA-Z0-9\-_]+/", "", $file_name);

    $spreadsheet->getActiveSheet()->setTitle($sheet_title);

    Calculation::getInstance()->setCalculationCacheEnabled(FALSE);

    if ($format == 'forceString') {
        class PHPExcel_Cell_MyColumnValueBinder extends DefaultValueBinder implements IValueBinder
        {
            protected $stringColumns = [];

            public function __construct(array $stringColumnList = []) {
                // Accept a list of columns that will always be set as strings
                $this->stringColumns = $stringColumnList;
            }

            public function bindValue(Cell $cell, $value = null): bool
            {
                // If the cell is one of our columns to set as a string...
                if (in_array($cell->getColumn(), $this->stringColumns)) {
                    // ... then we cast it to a string and explicitly set it as a string
                    $cell->setValueExplicit((string) $value, DataType::TYPE_STRING);
                    return true;
                }
                // Otherwise, use the default behaviour
                return parent::bindValue($cell, $value);
            }
        }

        Cell::setValueBinder(new PHPExcel_Cell_MyColumnValueBinder(['A', 'B', 'C', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P']));
    }
    $spreadsheet->getActiveSheet()->fromArray($dataArray, null, 'A1', true);

    formatCols(@$spreadsheet, $dataArray, $format, $assoc_col_cols, $assoc_col_champ);

    if (!isset($path_to_save)) {
// Redirect output to a client\'s web browser (Excel5)
        header('Content-Type: ' . ($isCsv ? 'application/csv' : 'xlsx') . 'application/vnd.ms-excel');
        header('Content-Disposition: attachment;filename="' . $file_name . '.' . ($isCsv ? 'csv' : 'xlsx') . '"');
        header('Cache-Control: max-age=0');
// If you're serving to IE 9, then the following may be needed
        header('Cache-Control: max-age=1');

// If you're serving to IE over SSL, then the following may be needed
        header('Expires: Mon, 26 Jul 1997 05:00:00 GMT'); // Date in the past
        header('Last-Modified: ' . gmdate('D, d M Y H:i:s') . ' GMT'); // always modified
        header('Cache-Control: cache, must-revalidate'); // HTTP/1.1
        header('Pragma: public'); // HTTP/1.0
    }

    if ($isCsv) {
        $objWriter = new Csv($spreadsheet);
    } else {
        $objWriter = new Xlsx($spreadsheet);
    }

    $objWriter->save((isset($path_to_save) ? $path_to_save . '/' . $file_name . '.' . ($isCsv ? 'csv' : 'xlsx') : 'php://output'));
    $spreadsheet->disconnectWorksheets();
    $spreadsheet->garbageCollect();
    unset($spreadsheet, $objWriter);
}

function formatCols($spreadsheet, $dataArray, $format, $assoc_col_cols = [], $assoc_col_champ = [])
{
    $activeSpreadSheet = $spreadsheet->getActiveSheet();

	$assoc_col_lettre = [];
	$nb_cols = count($dataArray[0]);
	$iter_col = 0;
	for ($col = 'A'; $col !== 'ZZ'; $col++) {
		if (in_array($format, ['forceString', 'standard'])) {
			$activeSpreadSheet->getColumnDimension($col)->setAutoSize(true);
		} else if ($format == 'qrts') {
            if (in_array($col, array('K', 'N'))) {
                $activeSpreadSheet->getColumnDimension($col)->setWidth(100);
            } else if (in_array($col, array('C', 'D', 'J'))) {
                $activeSpreadSheet->getColumnDimension($col)->setWidth(60);
            } else {
                $activeSpreadSheet->getColumnDimension($col)->setAutoSize(true);
            }

            if (in_array($col, array('J', 'K', 'N'))) {
                $activeSpreadSheet->getStyle($col . '1:' . $col . $activeSpreadSheet->getHighestRow())->getAlignment()->setWrapText(true);
            }
		} else if ($format == 'notifications') {
            if (in_array($col, array('D', 'F', 'G'))) {
                $activeSpreadSheet->getColumnDimension($col)->setWidth(60);
            } else {
                $activeSpreadSheet->getColumnDimension($col)->setAutoSize(true);
            }
		} else if (in_array($format, ['financement_avance', 'acquisitions_avance'])) {
			$activeSpreadSheet->getColumnDimension($col)->setWidth(35);
		}
		$assoc_col_lettre[$iter_col] = $col;
		if ($iter_col++ > $nb_cols) {
			break;
		}
	}

    if ($format == 'acquisitions_avance') {
	    foreach (AcquisitionsProjetsAdministrations::getColonnesMetas() as $blocsNiv2) {
		    foreach ($blocsNiv2 as $bloc) {
			    foreach ($bloc['colonnes'] as $champ) {
				    if (isset($champs_to_export[$champ['nom']]['export'])) {
					    if ($bloc['ensemble'] <> 'globale') {
						    if (isset($champ['format']) && $champ['format'] == 'date') {
							    $col = $assoc_col_lettre[$assoc_col_cols[$champ['nom']]];
							    $activeSpreadSheet->getStyle($col.'1:'.$col.$activeSpreadSheet->getHighestRow())->getNumberFormat()->setFormatCode(NumberFormat::FORMAT_DATE_YYYYMMDD2);
						    }
					    }
				    }
			    }
		    }
	    }

        // formattage des colonnes
	    foreach (AcquisitionsProjetsAdministrations::getChampsMetas() as $blocsNiv2) {
		    foreach ($blocsNiv2 as $bloc) {
			    foreach ($bloc['champs'] as $champ) {
				    if (isset($champ['format']) && $champ['format'] == 'date' && isset($assoc_col_champ[$champ['nom']])) {
					    $col = $assoc_col_lettre[$assoc_col_champ[$champ['nom']]];
					    $activeSpreadSheet->getStyle($col.'1:'.$col.$activeSpreadSheet->getHighestRow())->getNumberFormat()->setFormatCode(NumberFormat::FORMAT_DATE_YYYYMMDD2);
				    }
			    }
		    }
	    }
    }
}

function isWeekend($date) {
	return (date('N', strtotime($date)) >= 6);
}

function dm_strlen(string|null $string) : int
{
    return !isset($string) ? 0 : strlen($string);
}

function dm_addslashes(string|null $string) : string
{
	return !isset($string) ? '' : addslashes($string);
}

function dm_nl2br(string|null $string, bool $use_xhtml = true): string
{
	return !isset($string) ? '' : nl2br($string, $use_xhtml);
}

function dm_number_format(float|null $num, int $decimals = 0, ?string $decimal_separator = '.', ?string $thousands_separator = ','): string
{
	return !isset($num) ? '' : number_format($num, $decimals, $decimal_separator, $thousands_separator);
}

function dm_htmlspecialchars(string|null $string, int $flags = ENT_QUOTES | ENT_SUBSTITUTE | ENT_HTML401, ?string $encoding = null, bool $double_encode = true) : string
{
	return !isset($string) ? '' : htmlspecialchars($string, $flags, $encoding, $double_encode);
}

function dm_htmlspecialchars_decode(string|null $string, int $flags = ENT_QUOTES | ENT_SUBSTITUTE | ENT_HTML401): string
{
    return !isset($string) ? '' : htmlspecialchars_decode($string, $flags);
}

function dm_substr(string|null $string, int $offset, ?int $length = null): string
{
	return !isset($string) ? 0 : substr($string, $offset, $length);
}

function dm_preg_replace(array|string $pattern, array|string $replacement, array|string|null $subject, int $limit = -1, int|null &$count = null): array|string|null
{
	return !isset($subject) ? 0 : preg_replace($pattern, $replacement, $subject, $limit, $count);
}

function dm_trim(string|null $string, string $characters = " \xC2\xA0\t\n\r\0\x0B"): string
{
	return !isset($string) ? '' : trim($string, $characters);
}

function dm_explode(string $separator, string|null $string, int $limit = PHP_INT_MAX): array|false
{
	return !isset($string) ? [] : explode($separator, $string, $limit);
}

function dm_preg_split(string $pattern, string|null $subject, int $limit = -1, int $flags = 0): array|false
{
    return !isset($subject) ? [] : preg_split($pattern, $subject, $limit, $flags);
}

function dm_htmlentities(string|null $string, int $flags = ENT_QUOTES | ENT_SUBSTITUTE | ENT_HTML401, ?string $encoding = null, bool $double_encode = true): string
{
    return !isset($string) ? '' : htmlentities($string, $flags, $encoding, $double_encode);
}

function dm_str_replace(array|string|null $search, array|string|null $replace, string|array|null $subject, int &$count = null): string|array
{
    return !isset($subject) ? '' : str_replace(($search ?? ''), ($replace ?? ''), $subject, $count);
}

function dm_implode(string $separator, array|null $array): string
{
	return !isset($array) ? '' : implode($separator, $array);
}

function dm_strpos(string|null $haystack, string $needle, int $offset = 0): int|false
{
	return !isset($haystack) ? false : strpos($haystack, $needle, $offset);
}

function dm_strip_tags(string|null $string, array|string|null $allowed_tags = null): string
{
    return !isset($string) ? '' : strip_tags($string, $allowed_tags);
}

function getParsedJsonStream()
{
	include('classes/JsonParser/Listener.php');
	include('classes/JsonParser/Parser.php');
	include('classes/JsonParser/ParsingError.php');
	include('classes/JsonParser/Listener/GeoJsonListener.php');
	include('classes/JsonParser/Listener/IdleListener.php');
	include('classes/JsonParser/Listener/InMemoryListener.php');
	include('classes/JsonParser/Listener/SubsetConsumerListener.php');
	$listener = new \JsonStreamingParser\Listener\InMemoryListener();
	$stream = fopen('php://input', 'r');
	try {
		$parser = new \JsonStreamingParser\Parser($stream, $listener);
		$parser->parse();
		fclose($stream);
	} catch (Exception $e) {
		fclose($stream);
		throw $e;
	}

	return $listener->getJson();
}

function Get_Mapping_DB_Clients()
{
    if (!isset($_SESSION['clientsMaster'])) {
        $_SESSION['clientsMaster'] = ClientsMaster::getListe();
    }
    return $_SESSION['clientsMaster'];
}

function GetDataClientCourant()
{
    global $CodeClient;
    $dataClients = Get_Mapping_DB_Clients();
    return $dataClients[$CodeClient];
}

function IsClientModeEtendu()
{
    global $CodeClient;
    $dataClients = Get_Mapping_DB_Clients();
    return (int)$dataClients[$CodeClient]['is_financement_etendu'] == 1;
}

function getParametresFondamentauxEquipements()
{
    return [
        [
            'titre' => txt('Code', false),
            'nom_col' => 'nom'
        ],
	    [
		    'titre' => txt('Description', false),
		    'nom_col' => 'description'
	    ],
	    [
		    'titre' => txt('Catégorie', false),
		    'nom_col' => 'equipement_bio_medicaux_type_categorie'
	    ],
	    [
		    'titre' => txt('Sous-catégorie', false),
		    'nom_col' => 'equipements_bio_medicaux_types_sous_categorie'
	    ],
	    [
		    'titre' => txt('Catégorie responsabilité', false),
		    'nom_col' => 'equipements_bio_medicaux_types_categorie_responsabilite'
	    ],
	    [
		    'titre' => txt('Budgété par', false),
		    'nom_col' => 'equipements_bio_medicaux_types_responsable_logistique_1'
	    ],
	    [
		    'titre' => txt('Approvisionnement et livraison par', false),
		    'nom_col' => 'equipements_bio_medicaux_types_responsable_logistique_2'
	    ],
	    [
		    'titre' => txt('Pré-installation chantier par', false),
		    'nom_col' => 'equipements_bio_medicaux_types_responsable_logistique_3'
	    ],
	    [
		    'titre' => txt('Réception et installation par', false),
		    'nom_col' => 'equipements_bio_medicaux_types_responsable_logistique_4'
	    ],
	    [
		    'titre' => txt('Raccordement MEP par', false),
		    'nom_col' => 'equipements_bio_medicaux_types_responsable_logistique_5'
	    ],
	    [
		    'titre' => txt('Mise en service par', false),
		    'nom_col' => 'equipements_bio_medicaux_types_responsable_logistique_6'
	    ],
	    [
		    'titre' => txt('Direction responsable', false),
		    'nom_col' => 'equipements_bio_medicaux_types_direction_responsable'
	    ],
	    [
		    'titre' => txt('Phase d\'incidence', false),
		    'nom_col' => 'phase_incidence'
	    ],
    ];
}

function sec_session_start()
{
	global $isDev, $bUrl;

	$session_name = 'sec_session_id';   // Attribue un nom de session
	// Cette variable empêche Javascript d’accéder à l’id de session
	$httponly = true;
	// Force la session à n’utiliser que les cookies
	if (ini_set('session.use_only_cookies', 1) === FALSE) {
		header('Location: ' . $bUrl . 'error.php');
		exit();
	}
	// Récupère les paramètres actuels de cookies
	$cookieParams = session_get_cookie_params();
	session_set_cookie_params([
		'lifetime' => $cookieParams["lifetime"],
		'path' => $cookieParams["path"],
		'domain' => $cookieParams["domain"],
		'secure' => ($isDev ? false : true),
		'httponly' => $httponly,
		'samesite' => 'strict'
	]);
	// Donne à la session le nom configuré plus haut
	session_name($session_name);
	session_start();            // Démarre la session PHP
	session_regenerate_id();    // Génère une nouvelle session et efface la précédente
}

function getStartAndEndDate($week, $year) {
	$dto = new DateTime();
	$dto->setISODate($year, $week);
	$ret['week_start'] = date('Y-m-d', strtotime($dto->format('Y-m-d') . ' -1 day'));
	$dto->modify('+6 days');
	$ret['week_end'] = $dto->format('Y-m-d');
	return $ret;
}

function getListeOfReferencer($table, $id)
{
	global $db, $db_name, $tables_for_listes, $tables;

	$sql = "SELECT TABLE_NAME, COLUMN_NAME 
                FROM information_schema.KEY_COLUMN_USAGE 
                WHERE REFERENCED_TABLE_NAME = ? AND REFERENCED_COLUMN_NAME = 'id' AND TABLE_SCHEMA = ?;";
	$references = $db->getAll($sql, array($table, $db_name));

	$referencers = array();
	foreach ($references as $reference) {
		if (in_array($reference['TABLE_NAME'], $tables_for_listes) && $reference['TABLE_NAME'] <> 'conversions_noms_colonnes') {
			$where = $reference['COLUMN_NAME'] . " = ?";
			$data = array($id);
            $sql = "select nom, id
                    from " . $reference['TABLE_NAME'] . " tbl
                    where $where";
			$rows = $db->getAll($sql, $data);

			foreach ($rows as $i => $row) {
				$referencers[] = html_entity_decode($tables['client'][$reference['TABLE_NAME']]['nom']) . ' - ' . $row['nom'];
			}
		}
	}
	return $referencers;
}

function readfile_chunked($filename,$retbytes=true) {
	$chunksize = 1*(1024*1024); // how many bytes per chunk the user wishes to read
	$buffer = '';
	$cnt =0;
	$handle = fopen($filename, 'rb');
	if ($handle === false) {
		return false;
	}
	while (!feof($handle)) {
		$buffer = fread($handle, $chunksize);
		echo $buffer;
		if ($retbytes) {
			$cnt += strlen($buffer);
		}
	}
	$status = fclose($handle);
	if ($retbytes && $status) {
		return $cnt; // return number of bytes delivered like readfile() does.
	}
	return $status;
}

function getMenuMetaData()
{
	$projet_courant_id = getProjetId();
    $data_tmp = [
        [
            'type' => 'lien',
            'tri' => 'A',
	        'libelle' => txt('Accueil', false),
            'lien' => 'index.php?section=landing&module=index',
            'display' => true,
	        'selected' => false
        ],
        [
            'type' => 'lien',
            'tri' => 'B',
	        'libelle' => txt('Librairie', false),
            'lien' => 'index.php?section=admin&module=index&type=client',
            'display' => GestionnaireRoute::instance()->haveAccesRoute('admin-index'),
	        'selected' => ($_GET['section'] == 'admin')
        ],
        [
            'type' => 'lien',
            'tri' => 'C',
	        'libelle' => txt('Fiches', false),
            'lien' => 'index.php?section=fiche&module=index',
            'display' => true,
	        'selected' => ($_GET['section'] == 'fiche' && $_GET['module'] == 'index')
        ],
        [
            'type' => 'lien',
            'tri' => 'D',
	        'libelle' => txt('Gabarits', false),
            'lien' => 'index.php?section=gabarits&module=index',
            'display' => GestionnaireRoute::instance()->haveAccesRoute('gabarits-index'),
	        'selected' => ($_GET['section'] == 'gabarits')
        ],
	    [
		    'type' => 'liste',
		    'libelle' => txt('Rapports', false),
		    'display' => true,
		    'selected' => (($_GET['section'] == 'fiche' && in_array($_GET['module'], array('logs', 'bilan'))) || ($_GET['section'] == 'coordination' && $_GET['module'] == 'raccords')),
		    'liste' => [
			    [
				    'type' => 'lien',
            		'tri' => 'Acquisitions',
					'libelle' => '<span style="display: flex; align-items: center;"><img src="css/images/icons/dark/Bilans.png" alt="Bilans" style="width: 35px; height: 35px; margin-right: 15px;"><span style="font-size: 16px;">' . txt('Bilans', false) . '</span></span>',
				    'lien' => 'index.php?section=fiche&module=bilan',
                    'display' => GestionnaireRoute::instance()->haveAccesRoute('fiche-bilan'),
			    ],
			    [
    				'type' => 'lien',
            		'tri' => 'Historiques',
    				'libelle' => txt('Historiques', false),
					'libelle' => '<span style="display: flex; align-items: center;"><img src="css/images/icons/dark/Historiques.png" alt="Historiques" style="width: 35px; height: 35px; margin-right: 15px;"><span style="font-size: 16px;">' . txt('Historiques', false) . '</span></span>',
    				'lien' => 'index.php?section=fiche&module=logs',
    				'display' => GestionnaireRoute::instance()->haveAccesRoute('fiche-logs'),
				],
			    [
				    'type' => 'lien',
            		'tri' => 'Coordination',
				    'libelle' => txt('Coordination', false),
					'libelle' => '<span style="display: flex; align-items: center;"><img src="css/images/icons/dark/Coordination.png" alt="Coordination" style="width: 35px; height: 35px; margin-right: 15px;"><span style="font-size: 16px;">' . txt('Coordination', false) . '</span></span>',
				    'lien' => 'index.php?section=coordination&module=general',
                    'display' => GestionnaireRoute::instance()->haveAccesRoute('coordination-general'),
			    ],
			    [
				    'type' => 'lien',
            		'tri' => 'Utilisation',
				    'libelle' => txt('Utilisation', false),
					'libelle' => '<span style="display: flex; align-items: center;"><img src="css/images/icons/dark/Utilisation.png" alt="Utilisation" style="width: 35px; height: 35px; margin-right: 15px;"><span style="font-size: 16px;">' . txt('Utilisation', false) . '</span></span>',
				    'lien' => 'index.php?section=logs&module=graphique',
                    'display' => GestionnaireRoute::instance()->haveAccesRoute('logs-graphique'),
			    ],
			    [
				    'type' => 'lien',
            		'tri' => 'Journeaux',
				    'libelle' => txt('Journeaux', false),
					'libelle' => '<span style="display: flex; align-items: center;"><img src="css/images/icons/dark/Journeaux.png" alt="Journeaux" style="width: 35px; height: 35px; margin-right: 15px;"><span style="font-size: 16px;">' . txt('Journeaux', false) . '</span></span>',
				    'lien' => 'index.php?section=evenements_systeme&module=index',
                    'display' => GestionnaireRoute::instance()->haveAccesRoute('evenements_systeme-index'),
			    ],
            ]
	    ],
        	    [
		    'type' => 'liste',
		    'libelle' => txt('Outils', false),
		    'display' => true,
		    'selected' => (in_array($_GET['section'], array('notifications', 'entretien', 'inspection'))),
		    'liste' => [
            			    [
				    'type' => 'lien',
            		'tri' => 'Appariement des codes',
					'libelle' => '<span style="display: flex; align-items: center;"><img src="css/images/icons/dark/Appariement des codes.png" alt="Appariement des codes" style="width: 35px; height: 35px; margin-right: 15px;"><span style="font-size: 16px;">' . txt('Appariement des codes', false) . '</span></span>',
				    'lien' => 'index.php?section=fiche&module=apparier',
                    'display' => GestionnaireRoute::instance()->haveAccesRoute('fiche-apparier'),
			    ],
			    [
				    'type' => 'lien',
            		'tri' => 'Auto-répartition des locaux',
					'libelle' => '<span style="display: flex; align-items: center;"><img src="css/images/icons/dark/Auto-répartition.png" alt="Auto-répartition des locaux" style="width: 35px; height: 35px; margin-right: 15px;"><span style="font-size: 16px;">' . txt('Auto-répartition des locaux', false) . '</span></span>',
				    'lien' => 'index.php?section=fiche&module=locaux_types',
                    'display' => GestionnaireRoute::instance()->haveAccesRoute('fiche-locaux_types'),
			    ],
			    [
				    'type' => 'lien',
            		'tri' => 'Auto-synthèse des fichiers de distribution',
					'libelle' => '<span style="display: flex; align-items: center;"><img src="css/images/icons/dark/Auto-synthèse.png" alt="Auto-synthèse des fichiers de distribution" style="width: 35px; height: 35px; margin-right: 15px;"><span style="font-size: 16px;">' . txt('Auto-synthèse des fichiers de distribution', false) . '</span></span>',
				    'lien' => 'index.php?section=scripts&module=autoSyntheseFichierDistribution',
                    'display' => GestionnaireRoute::instance()->haveAccesRoute('scripts-autoSyntheseFichierDistribution'),
			    ],
			    [
				    'type' => 'lien',
            		'tri' => 'Entretien (GMAO)',
					'libelle' => '<span style="display: flex; align-items: center;"><img src="css/images/icons/dark/Entretien.png" alt="Entretien (GMAO)" style="width: 35px; height: 35px; margin-right: 15px;"><span style="font-size: 16px;">' . txt('Entretien (GMAO)', false) . '</span></span>',
				    'lien' => 'index.php?section=entretien&module=procedures',
                    'display' => havePermission('admin_procedures_entretiens') && GestionnaireRoute::instance()->haveAccesRoute('entretien-procedures'),
                ],
			    [
				    'type' => 'lien',
            		'tri' => 'Entretien (GMAO)',
					'libelle' => '<span style="display: flex; align-items: center;"><img src="css/images/icons/dark/Entretien.png" alt="Entretien (GMAO)" style="width: 35px; height: 35px; margin-right: 15px;"><span style="font-size: 16px;">' . txt('Entretien (GMAO)', false) . '</span></span>',
				    'lien' => 'index.php?section=entretien&module=historique',
                    'display' => !havePermission('admin_procedures_entretiens') && GestionnaireRoute::instance()->haveAccesRoute('entretien-historique'),
                ],
			    [
				    'type' => 'lien',
            		'tri' => 'Notifications',
					'libelle' => '<span style="display: flex; align-items: center;"><img src="css/images/icons/dark/Gestion des notifications.png" alt="Notifications" style="width: 35px; height: 35px; margin-right: 15px;"><span style="font-size: 16px;">' . txt('Notifications', false) . '</span></span>',
				    'lien' => 'index.php?section=notifications&module=admin',
                    'display' => GestionnaireRoute::instance()->haveAccesRoute('notifications-admin'),
                ],
			    [
				    'type' => 'lien',
            		'tri' => 'Notifications',
					'libelle' => '<span style="display: flex; align-items: center;"><img src="css/images/icons/dark/Gestion des notifications.png" alt="Notifications" style="width: 35px; height: 35px; margin-right: 15px;"><span style="font-size: 16px;">' . txt('Notifications', false) . '</span></span>',
				    'lien' => 'index.php?section=notifications&module=index',
                    'display' => !havePermission(array('admin_notifications')) && GestionnaireRoute::instance()->haveAccesRoute('notifications-index'),
                ],
			    [
				    'type' => 'lien',
            		'tri' => 'QRT-BCF',
					'libelle' => '<span style="display: flex; align-items: center;"><img src="css/images/icons/dark/Communications QRT.png" alt="QRT-BCF" style="width: 35px; height: 35px; margin-right: 15px;"><span style="font-size: 16px;">' . txt('QRT-BCF', false) . '</span></span>',
				    'lien' => 'index.php?section=qrt&module=index',
                    'display' => GestionnaireRoute::instance()->haveAccesRoute('qrt-index'),
                ],
			    [
				    'type' => 'lien',
            		'tri' => 'Tableau des superficies',
					'libelle' => '<span style="display: flex; align-items: center;"><img src="css/images/icons/dark/Tableau des superficies.png" alt="Tableau des superficies" style="width: 35px; height: 35px; margin-right: 15px;"><span style="font-size: 16px;">' . txt('Tableau des superficies', false) . '</span></span>',
				    'lien' => 'index.php?section=superficies&module=index',
                    'display' => GestionnaireRoute::instance()->haveAccesRoute('superficies-index'),
                ],
			    [
				    'type' => 'lien',
            		'tri' => 'Surveillance de travaux',
					'libelle' => '<span style="display: flex; align-items: center;"><img src="css/images/icons/dark/Surveillance de travaux.png" alt="Surveillance de travaux" style="width: 35px; height: 35px; margin-right: 15px;"><span style="font-size: 16px;">' . txt('Surveillance de travaux', false) . '</span></span>',
				    'lien' => 'index.php?section=surveillance&module=historique',
                    'display' => GestionnaireRoute::instance()->haveAccesRoute('surveillance-historique'),
                ],
			    [
				    'type' => 'lien',
            		'tri' => 'Statuts de fiche',
					'libelle' => '<span style="display: flex; align-items: center;"><img src="css/images/icons/dark/Gestion des statuts de fiches.png" alt="Statuts de fiche" style="width: 35px; height: 35px; margin-right: 15px;"><span style="font-size: 16px;">' . txt('Statuts de fiche', false) . '</span></span>',
				    'lien' => 'index.php?section=fiches_statuts&module=index',
                    'display' => GestionnaireRoute::instance()->haveAccesRoute('fiches_statuts-index'),
                ],
			    [
				    'type' => 'lien',
            		'tri' => 'Structure hiérarchique',
					'libelle' => '<span style="display: flex; align-items: center;"><img src="css/images/icons/dark/Structure hiérarchique.png" alt="Structure hiérarchique" style="width: 35px; height: 35px; margin-right: 15px;"><span style="font-size: 16px;">' . txt('Structure hiérarchique', false) . '</span></span>',
				    'lien' => 'index.php?section=structure_hierarchique&module=index',
                    'display' => GestionnaireRoute::instance()->haveAccesRoute('structure_hierarchique-index'),
                ],
			    [
				    'type' => 'lien',
            		'tri' => 'Structure hiérarchique',
					'libelle' => '<span style="display: flex; align-items: center;"><img src="css/images/icons/dark/Structure hiérarchique.png" alt="Structure hiérarchique" style="width: 35px; height: 35px; margin-right: 15px;"><span style="font-size: 16px;">' . txt('Structure hiérarchique', false) . '</span></span>',
				    'lien' => 'index.php?section=structure_hierarchique&module=specifique',
                    'display' => !havePermission(['structure_hierarchique_generale']) && GestionnaireRoute::instance()->haveAccesRoute('structure_hierarchique-specifique'),
                ],
                [
                    'type' => 'lien',
            		'tri' => 'Gestion des portes',
					'libelle' => '<span style="display: flex; align-items: center;"><img src="css/images/icons/dark/Gestion des portes.png" alt="Gestion des portes" style="width: 35px; height: 35px; margin-right: 15px;"><span style="font-size: 16px;">' . txt('Gestion des portes', false) . '</span></span>',
                    'lien' => 'index.php?section=gestionnairePage&module=GestionnaireFichesPortes',
                    'display' => GestionnaireRoute::instance()->haveAccesRoute('gestionnairePage-GestionnaireFichesPortes'),
                ],
                [
                    'type' => 'lien',
            		'tri' => 'Gestion des IFC',
					'libelle' => '<span style="display: flex; align-items: center;"><img src="css/images/icons/dark/Gestion des IFC.png" alt="Gestion des IFC" style="width: 35px; height: 35px; margin-right: 15px;"><span style="font-size: 16px;">' . txt('Gestion des IFC', false) . '</span></span>',
                    'lien' => 'index.php?section=gestionnairePage&module=Visualisateur3dFichiers',
                    'display' => GestionnaireRoute::instance()->haveAccesRoute('gestionnairePage-Visualisateur3dFichiers'),
			    ],
			]
	    ],
	    [
		    'type' => 'liste',
		    'libelle' => txt('Planification', false),
		    'display' => true,
		    'selected' => (in_array($_GET['section'], array('notifications', 'entretien', 'inspection'))),
		    'liste' => [
			    [
				    'type' => 'lien',
            		'tri' => 'Acquisitions',
					'libelle' => '<span style="display: flex; align-items: center;"><img src="css/images/icons/dark/Gestion des acquisitions.png" alt="Acquisitions" style="width: 35px; height: 35px; margin-right: 15px;"><span style="font-size: 16px;">' . txt('Acquisitions', false) . '</span></span>',
				    'lien' => 'index.php?section=acquisition&module=projets',
                    'display' => GestionnaireRoute::instance()->haveAccesRoute('acquisition-projets'),
			    ],
			    [
				    'type' => 'lien',
            		'tri' => 'Dossiers contractuels',
					'libelle' => '<span style="display: flex; align-items: center;"><img src="css/images/icons/dark/Dossiers contractuels.png" alt="Dossiers contractuels" style="width: 35px; height: 35px; margin-right: 15px;"><span style="font-size: 16px;">' . txt('Dossiers contractuels', false) . '</span></span>',
				    'lien' => 'index.php?section=contrats&module=index',
                    'display' => GestionnaireRoute::instance()->haveAccesRoute('contrats-index'),
			    ],
			    [
				    'type' => 'lien',
            		'tri' => 'Échéanciers globaux',
					'libelle' => '<span style="display: flex; align-items: center;"><img src="css/images/icons/dark/Échéanciers globaux.png" alt="Échéanciers globaux" style="width: 35px; height: 35px; margin-right: 15px;"><span style="font-size: 16px;">' . txt('Échéanciers globaux', false) . '</span></span>',
				    'lien' => 'index.php?section=gestionnairePage&module=Echeanciers',
                    'display' => GestionnaireRoute::instance()->haveAccesRoute('gestionnairePage-Echeanciers') && !havePermission(['acces_echeanciers_globaux_generale']),
			    ],
			    [
				    'type' => 'lien',
            		'tri' => 'Échéanciers globaux',
					'libelle' => '<span style="display: flex; align-items: center;"><img src="css/images/icons/dark/Échéanciers globaux.png" alt="Échéanciers globaux" style="width: 35px; height: 35px; margin-right: 15px;"><span style="font-size: 16px;">' . txt('Échéanciers globaux', false) . '</span></span>',
				    'lien' => 'index.php?section=echeancier_global&module=specifique',
                    'display' => GestionnaireRoute::instance()->haveAccesRoute('echeancier_global-specifique'),
			    ],
			    [
				    'type' => 'lien',
            		'tri' => 'Demandes de financement',
					'libelle' => '<span style="display: flex; align-items: center;"><img src="css/images/icons/dark/Demandes de financement.png" alt="Demandes de financement" style="width: 35px; height: 35px; margin-right: 15px;"><span style="font-size: 16px;">' . txt('Demandes de financement', false) . '</span></span>',
					'lien' => 'index.php?section=financement&module=projets&mode=global',
                    'display' => GestionnaireRoute::instance()->haveAccesRoute('financement-projets'),
                ],
			    [
				    'type' => 'lien',
            		'tri' => 'Gestion des équipements',
					'libelle' => '<span style="display: flex; align-items: center;"><img src="css/images/icons/dark/Gestion des équipements.png" alt="Gestion des équipements" style="width: 35px; height: 35px; margin-right: 15px;"><span style="font-size: 16px;">' . txt('Gestion des équipements', false) . '</span></span>',
				    'lien' => 'index.php?section=gestion_equipements&module=index',
                    'display' => GestionnaireRoute::instance()->haveAccesRoute('gestion_equipements-index'),
                ],
			    [
				    'type' => 'lien',
            		'tri' => 'Inventaires et déménagements',
					'libelle' => '<span style="display: flex; align-items: center;"><img src="css/images/icons/dark/Gestion des inventaires.png" alt="Inventaires et déménagements" style="width: 35px; height: 35px; margin-right: 0px;"><img src="css/images/icons/dark/Gestion des déménagements.png" alt="Financements" style="width: 35px; height: 35px; margin-right: 15px;"><span style="font-size: 16px;">' . txt('Inventaires et déménagements', false) . '</span></span>',
				    'lien' => 'index.php?section=inventaires&module=index&onglet=inventaire_importe',
                    'display' => GestionnaireRoute::instance()->haveAccesRoute('inventaires-index'),
                ],
			    [
				    'type' => 'lien',
            		'tri' => 'Mise en service',
					'libelle' => '<span style="display: flex; align-items: center;"><img src="css/images/icons/dark/Mise en service.png" alt="Mise en service" style="width: 35px; height: 35px; margin-right: 15px;"><span style="font-size: 16px;">' . txt('Mise en service', false) . '</span></span>',
					'lien' => 'index.php?section=inspection&module=proceduresGbm',
                    'display' => GestionnaireRoute::instance()->haveAccesRoute('inspection-procedures'),
                ],
			    [
				    'type' => 'lien',
            		'tri' => 'Mise en service',
					'libelle' => '<span style="display: flex; align-items: center;"><img src="css/images/icons/dark/Mise en service.png" alt="Mise en service" style="width: 35px; height: 35px; margin-right: 15px;"><span style="font-size: 16px;">' . txt('Mise en service', false) . '</span></span>',
				    'lien' => 'index.php?section=inspection&module=historique',
                    'display' => !havePermission('admin_procedures_inspections') && GestionnaireRoute::instance()->haveAccesRoute('inspection-historique'),
                ],
			    [
				    'type' => 'lien',
            		'tri' => 'Sources de financement',
					'libelle' => '<span style="display: flex; align-items: center;"><img src="css/images/icons/dark/Gestion budgétaire.png" alt="Sources de financement" style="width: 35px; height: 35px; margin-right: 15px;"><span style="font-size: 16px;">' . txt('Sources de financement', false) . '</span></span>',
				    'lien' => 'index.php?section=suivi_budgetaire&module=index',
                    'display' => GestionnaireRoute::instance()->haveAccesRoute('suivi_budgetaire-index'),
                ],
                [
                    'type' => 'lien',
                    'tri' => 'Gestion des gabarits',
                    'libelle' => '<span style="display: flex; align-items: center;"><img src="css/images/icons/dark/Gestion budgétaire.png" alt="Gestion des gabarits" style="width: 35px; height: 35px; margin-right: 15px;"><span style="font-size: 16px;">' . txt('Gestion des gabarits', false) . '</span></span>',
                    'lien' => 'index.php?section=gestionnairePage&module=GestionFichesGabarits',
                    'display' => isMasterOrMario(),
                ],
            ]
	    ],

    ];

    $data = [];
    foreach ($data_tmp as $dd) {
        if ($dd['type'] == 'liste') {
			uasort($dd['liste'], 'custom_sort_tri');
        }
	    $data[] = $dd;
    }

    return $data;
}



function uuid($data = null) {
    // Generate 16 bytes (128 bits) of random data or use the data passed into the function.
    $data = $data ?? random_bytes(16);
    assert(strlen($data) == 16);

    // Set version to 0100
    $data[6] = chr(ord($data[6]) & 0x0f | 0x40);
    // Set bits 6-7 to 10
    $data[8] = chr(ord($data[8]) & 0x3f | 0x80);

    // Output the 36 character UUID.
    return vsprintf('%s%s-%s-%s-%s-%s%s%s', str_split(bin2hex($data), 4));
}

function updateStringFromArray($array)
{
    $string = "";
    $values = [];
    $iter = 0;
    foreach ($array as $key => $value) {
        if (!is_array($value)) {
            $string .= ($iter++ == 0 ? '' : ', ') . "$key = ?";
            $values[] = $value;
        }
    }
    return [$string, $values];
}

function insertStringFromArray($array)
{
    $stringCols = "";
    $stringVals = "";
    $values = [];
    $iter = 0;
    foreach ($array as $key => $value) {
        if (!is_array($value)) {
            $stringCols .= ($iter == 0 ? '' : ', ') . "$key";
            $stringVals .= ($iter == 0 ? '' : ', ') . "?";
            $values[] = $value;
            $iter++;
        }
    }
    return ["($stringCols) VALUES ($stringVals)", $values];
}

function formatValueFromBcf($dataDM, $value)
{
    if ($dataDM['nom_col'] == 'is_prioritaire') {
        $value = $value == 'high' ? 1 : 0;
    } else if ($dataDM['format'] == 'timestamp') {
        $value = str_replace('T', ' ', substr($value, 0, 19));
    }
    return $value;
}

function getColonneTriEquipementsGBM()
{
    $projet = getProjetInfo();
    return isset($projet) ? $projet->getColonneTriEquipementsGBM() : 'equipement_bio_medicaux_type';
}

function getTempDir()
{
    global $isDev;
    if ($isDev) {
        return 'c:\wamp64\www\docmatic\temp' . '\\';
    }
    return '/var/www/temp/';
}

function formatBytes($size,$unit)
{
    if($unit == "KB")
    {
        return $fileSize = round($size / 1024,4) . 'KB';
    }
    if($unit == "MB")
    {
        return $fileSize = round($size / 1024 / 1024,4) . 'MB';
    }
    if($unit == "GB")
    {
        return $fileSize = round($size / 1024 / 1024 / 1024,4) . 'GB';
    }
}

function getChampNomFromLangue($table): string
{
    global $Langue;
    $tablesSansEn = ['batiments_niveaux_elements', 'fonctionnements', 'elements', 'ouis_nons', 'conversions_unites', 'conversions_noms_colonnes', 'amperages_types', 'fusibles_calibres_types', 'equipements_bio_medicaux_types_categories'];
    return !in_array($table, $tablesSansEn) ? ($Langue == 'en' ? 'nom_en' : 'nom') : 'nom';
}

function sanitizeExcelSheetName(string $sheet_title): string|null
{
    $sheet_title = dm_str_replace("'", "", $sheet_title);
    $sheet_title = strip_accents(dm_substr($sheet_title, 0, 31));
    return dm_preg_replace("/[^a-zA-Z0-9\-_]+/", "", $sheet_title);
}

function basicSanitize(?string $data): string
{
    return dm_htmlspecialchars($data, ENT_QUOTES, 'UTF-8');
}

function recursiveSanitizing($data)
{
    foreach ($data as $key => $value) {
        if (is_array($value)) {
            $data[$key] = recursiveSanitizing($value);
        } else {
            $data[$key] = is_string($value) ? basicSanitize($value) : $value;
        }
    }
    return $data;
}

function validerJsCallBack($callback)
{
    $valideCallBacks = ['filtreAvanceCallback', 'filtre_avance_callback', 'after_insert_projet', 'reloadMarques', 'reloadModeles', 'after_insert_projet', 'callbackAjoutParametreDocmatic'];
    return in_array($callback, $valideCallBacks) ? $callback : '';
}

function uniqueKey(?string $text): string
{
    return hash('sha256', ($text ?? ''));
}

function camelToSnake($camelCase) {
    $result = '';
    for ($i = 0; $i < strlen($camelCase); $i++) {
        $char = $camelCase[$i];
        if (ctype_upper($char)) {
            $result .= '_' . strtolower($char);
        } else {
            $result .= $char;
        }
    }
    return ltrim($result, '_');
}

function getCleFiltreGlobalToUse()
{
    return '_' . $_GET['section'] . '_' . $_GET['module'] . '_' . ($_GET['onglet'] ?? '');
}

function nomFichierRepertoireValide($nom): string
{
    if (!preg_match('/^[a-zA-Z_][a-zA-Z0-9_]*$/', $nom)) {
        throw new Error("Possible tentative d'injection SQL: " . $nom);
    }
    return $nom;
}

function isExportDrofus(): bool
{
    return in_array(Config::instance()->codeClient, ['phvs', 'ciussseimtl']);
}

function couleur_aleatoire() {
    // Génère un nombre compris entre 0 et 255 pour chaque composant de couleur
    $rouge = rand(0, 255);
    $vert = rand(0, 255);
    $bleu = rand(0, 255);

    // Convertit en hexadécimal et assure que chaque composant a deux chiffres
    return sprintf("#%02x%02x%02x", $rouge, $vert, $bleu);
}

function generateGrayShades($shadesCount, $indexMax) {
    $colors = [];
    $minValue = 30; // Valeur minimale de gris
    $maxValue = 225; // Valeur maximale de gris
    $step = ($maxValue - $minValue) / ($shadesCount - 1);

    for ($i = 0; $i < $shadesCount; $i++) {
        $value = (int)round($minValue + $step * $i);
        $hexValue = str_pad(dechex($value), 2, '0', STR_PAD_LEFT);
        $colors[] = "#$hexValue$hexValue$hexValue";
    }

    $indexedColors = [];
    $stepForIndex = (int)(($shadesCount - 1) / ($indexMax - 1));
    for ($i = 0; $i < $indexMax; $i++) {
        $indexedColors[$i + 1] = $colors[$i * $stepForIndex];
    }

    return $indexedColors;
}

function generateBlueShades($shadesCount, $indexMax) {
    $colors = [];
    $minValue = 50; // Valeur minimale de bleu
    $maxValue = 205; // Valeur maximale de bleu
    $step = ($maxValue - $minValue) / ($shadesCount - 1);

    for ($i = 0; $i < $shadesCount; $i++) {
        $value = (int)round($minValue + $step * $i);
        $hexValue = str_pad(dechex($value), 2, '0', STR_PAD_LEFT);
        $colors[] = "#0000$hexValue";
    }

    $indexedColors = [];
    $stepForIndex = (int)(($shadesCount - 1) / ($indexMax - 1));
    for ($i = 0; $i < $indexMax; $i++) {
        $indexedColors[$i + 1] = $colors[$i * $stepForIndex];
    }

    return $indexedColors;
}

function generateGrayBlueShades($shadesCount, $indexMax) {
    $colors = [];
    $minValueGray = 20; // Valeur minimale de gris
    $maxValueGray = 235; // Valeur maximale de gris
    $minValueBlue = 20; // Valeur minimale de bleu
    $maxValueBlue = 235; // Valeur maximale de bleu
    $stepGray = ($maxValueGray - $minValueGray) / ($shadesCount - 1);
    $stepBlue = ($maxValueBlue - $minValueBlue) / ($shadesCount - 1);

    for ($i = 0; $i < $shadesCount; $i++) {
        $valueGray = (int)round($minValueGray + $stepGray * $i);
        $valueBlue = (int)round($minValueBlue + $stepBlue * $i);
        $hexValueGray = str_pad(dechex($valueGray), 2, '0', STR_PAD_LEFT);
        $hexValueBlue = str_pad(dechex($valueBlue), 2, '0', STR_PAD_LEFT);
        $colors[] = "#$hexValueGray$hexValueGray$hexValueBlue";
    }

    $indexedColors = [];
    $stepForIndex = (int)(($shadesCount - 1) / ($indexMax - 1));
    for ($i = 0; $i < $indexMax; $i++) {
        $indexedColors[$i + 1] = $colors[$i * $stepForIndex];
    }

    return $indexedColors;
}

function isValidDate(string $date, string $format = 'Y-m-d H:i:s'): bool
{
    $d = DateTime::createFromFormat($format, $date);
    return $d && $d->format($format) === $date;
}

function getNextSequenceId($table)
{
    db::instance()->query("INSERT INTO `" . db::tableColonneValide($table) . "_sequences` VALUES (null);");
    return db::instance()->getOne("SELECT LAST_INSERT_ID() AS sequence_id;");
}

function getExcelColumnLetter($col) {
    $letter = '';
    while ($col > 0) {
        $col--; // Décrémenter pour passer à l'index 0
        $letter = chr($col % 26 + 65) . $letter; // Convertir le reste en lettre
        $col = intval($col / 26); // Passer à la colonne précédente
    }
    return $letter;
}
function isChampRemarques()
{
    global $CodeClient;
    return in_array($CodeClient, ['sqi', 'phvs', 'cisssca']);
}

function isStringEquals($string1, $string2): bool
{
    $string1 = ($string1 ?? '');
    $string2 = ($string2 ?? '');
    // Échapper la deuxième chaîne pour comparaison
    $escapedStr2 = htmlspecialchars($string2, ENT_QUOTES | ENT_HTML5);

    // Comparer les chaînes après décodage HTML
    return html_entity_decode($string1, ENT_QUOTES | ENT_HTML5) === html_entity_decode($string2, ENT_QUOTES | ENT_HTML5) ||
        $escapedStr2 === $string1 ||
        htmlspecialchars($string1, ENT_QUOTES | ENT_HTML5) === $string2;
}
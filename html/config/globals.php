<?php

$modes = array(
	1 => array('id' => 1, 'nom' 	=> txt('Instruments analogique')),
	2 => array('id' => 2, 'nom' 	=> txt('Instruments discret')),
	4 => array('id' => 4, 'nom' 	=> txt('Instruments sans calibration')),
	8 => array('id' => 8, 'nom' 	=> txt('Équipement')),
	9 => array('id' => 9, 'nom' 	=> txt('Commande d\'équipement')),
	10 => array('id' => 10, 'nom' 	=> txt('Feedback d\'équipement')),

							);

$types_sources = array(
	1 => array('id' => 1, 'nom' 	=> txt('Sectionneur dans un casier de MCC')),
	2 => array('id' => 6, 'nom' 	=> txt('Démarreur magnétique dans un casier de MCC')),
	3 => array('id' => 7, 'nom' 	=> txt('Démarreur progressif dans un casier de MCC')),
	4 => array('id' => 8, 'nom' 	=> txt('Variateur de vitesse dans un casier de MCC')),
	5 => array('id' => 2, 'nom' 	=> txt('Sectionneur dans un boîtier local (à proximité)')),
	6 => array('id' => 9, 'nom' 	=> txt('Démarreur magnétique dans un boîtier local (à proximité)')),
	7 => array('id' => 10, 'nom' 	=> txt('Démarreur progressif dans un boîtier local (à proximité)')),
	8 => array('id' => 11, 'nom' 	=> txt('Variateur de vitesse dans un boîtier local (à proximité)')),
	9 => array('id' => 3, 'nom' 	=> txt('Panneau de distibution dans un casier de MCC')),
	10 => array('id' => 4, 'nom' 	=> txt('Panneau de distribution local (à proximité)')),
	11 => array('id' => 5, 'nom' 	=> txt('Autre (veuillez décrire l\'installation dans le champs "Commentaires")')),

							);

$types_protections = array(
	1 => array('id' => 1, 'nom' 	=> txt('Aucune protection (intrinsèque à l\'équipement alimenté)')),
	2 => array('id' => 2, 'nom' 	=> txt('Fusible (puissance)')),
	3 => array('id' => 3, 'nom' 	=> txt('Disjoncteur (puissance)')),
			
							);

$phasages_types = array(
	1 => array('id' => 1, 'nom' 	=> txt('Monophasé')),
	3 => array('id' => 3, 'nom' 	=> txt('Triphasé')),

							);

$types_import_export = array(
    'listes' 						=> txt('Listes d\'éléments'),
    'objets' 						=> txt('Propriétés spécifiques'),
    'hybrides' 						=> txt('Listes de raccords'),

							);

$libelles_code_pivot = array(
    'code_fiche' 					=> txt('Code de la fiche'),
    'code_alternatif' 				=> txt('Code alternatif'),
    'id_revit' 						=> txt('ID Externe 1'),

							);

$types_assocs = array(
    'listes'    					=> 'LISTE',
    'hybrides'  					=> 'LISTE_COORDINATION',
    'objets'    					=> 'UNIQUE',

							);

$accepted_values_for_chekbox = array(
    'checked' 						=> array('OUI', 'YES', 'TRUE', '1', 'ACTIF', 'X'),
    'unchecked' 					=> array('NON', 'NO', 'FALSE', '0', 'INACTIF'),

							);

$champs_filtres_speciaux = [
	'presence_donnees'    			=> [
		'titre' => txt('[Présence de données]'),
	],
	'absence_donnees'  				=> [
		'titre' => txt('[Absence de données]'),
	],
	'presence_doublons'    			=> [
		'titre' => txt('[Présence de doublons]'),
	],
];

$champs_filtres_speciaux_acquisitions = [
	'%vide%'    			=> [
		'titre' => txt('[Champ vide]'),
	],
	'%non-vide%'  				=> [
		'titre' => txt('[Champ non-vide]'),
	],
];

$champs_filtres_speciaux_equipements_bio_medicaux = [
	'equipement_bio_medicaux_type_categorie' 					=> [
		'titre' => txt('Catégorie'),
		'classe' => 'EquipementsBioMedicauxTypesCategories'
	],
	'classification_budgetaire_type' 							=> [
		'titre' => txt('Classification budgétaire'),
		'classe' => 'ClassificationsBudgetairesTypes'
	],
	'phase_incidence' 											=> [
		'titre' => txt('Phase d\'incidence'),
		'classe' => 'PhasesIncidences'
	],
	'equipements_bio_medicaux_types_sous_categorie' 			=> [
		'titre' => txt('Sous-catégorie'),
		'classe' => 'EquipementsBioMedicauxTypesSousCategories'
	],
	'equipements_bio_medicaux_types_categorie_responsabilite' 	=> [
		'titre' => txt('Catégorie responsabilité'),
		'classe' => 'EquipementsBioMedicauxTypesCategoriesResponsabilites'
	],
	'equipements_bio_medicaux_types_responsable_logistique_1' 	=> [
		'titre' => txt('Budgété par'),
		'classe' => 'EquipementsBioMedicauxTypesResponsablesLogistiques'
	],
	'equipements_bio_medicaux_types_responsable_logistique_2' 	=> [
		'titre' => txt('Approvisionnement et livraison par:'),
		'classe' => 'EquipementsBioMedicauxTypesResponsablesLogistiques'
	],
	'equipements_bio_medicaux_types_responsable_logistique_3' 	=> [
		'titre' => txt('Pré-installation chantier par'),
		'classe' => 'EquipementsBioMedicauxTypesResponsablesLogistiques'
	],
	'equipements_bio_medicaux_types_responsable_logistique_4' 	=> [
		'titre' => txt('Réception et installation par'),
		'classe' => 'EquipementsBioMedicauxTypesResponsablesLogistiques'
	],
	'equipements_bio_medicaux_types_responsable_logistique_5' 	=> [
		'titre' => txt('Raccordement MEP par'),
		'classe' => 'EquipementsBioMedicauxTypesResponsablesLogistiques'
	],
	'equipements_bio_medicaux_types_responsable_logistique_6' 	=> [
		'titre' => txt('Mise en service par'),
		'classe' => 'EquipementsBioMedicauxTypesResponsablesLogistiques'
	],
	'equipements_bio_medicaux_types_direction_responsable' 		=> [
		'titre' => txt('Direction responsable'),
		'classe' => 'EquipementsBioMedicauxTypesDirectionsResponsables'
	]
];

$TablesCategoriesPermissions = ['usagers_acquisitions', 'usagers_financements', 'usagers_raccords', 'usagers_contrats', 'usagers_equipements', 'usagers_qrt', 'usagers_suivis_budgetaires', 'usagers_notifications', 'usagers_entretiens', 'usagers_mises_en_services', 'usagers_surveillances', 'usagers_administrations_bims', 'usagers_rapports', 'usagers_librairies', 'usagers_fiches', 'usagers_structure_hierarchique', 'usagers_echeanciers_globaux', 'usagers_inventaires' ];

$TablesFancyBig = ['echeanciers', 'usagers'];

$AllFormatsExports = ['xlsx' => 'Excel', 'xml' => 'XML', 'csv' => 'CSV', 'json' => 'JSON'];

$masterTables = array(
	'fiches_types',
	'correlations',
	'elements_types',
	'elements',
	'fonctionnements',
	'disciplines',
	'types_cartes',
	'unites_types',
	'unites',

/*
	'marques',
	'modeles',
	'nomenclatures',
	'periodes',
	'procedures_entretiens',
	'roles',
	'permissions',
	'fournisseurs',
    'accessoires_architecturaux_types',
    'accessoires_sanitaires_ensembles_types',
    'accessoires_sanitaires_unites_types',
    'alimentations_electriques_types',
    'appels_locaux_generaux_equipements_types',
    'appels_locaux_generaux_types',
    'appels_locaux_generaux_zones',
    'appels_locaux_generaux_blocs',
    'appels_locaux_generaux_etages',
    'bains_types',
    'douches_types',
    'cameras_securites_types',
    'changements_airs_heures',
    'prises_communications_sorties_types',
    'evacuations_speciales_types',
    'eaux_purifies_types',
    'eclairages_controles_types',
    'eclairages_lumens_types',
    'eclairages_ratios_urgences',
    'eclairages_interieurs_types',
    'eclairages_exterieurs_types',
    'eclairages_stationnements_types',
    'eclairages_routiers_types',
    'eclairages_parcs_types',
    'appels_gardes_types',
    'appels_urgences_types',
    'equipements_medicaux_specialises_types',
    'equipements_multimedias_types',
    'eviers_types',
    'existants_nouveaux',
    'fournis_installes',
    'frequentations',
    'gaz_medicaux_types',
    'horaire_normal_utilisation',
    'horloges_types',
    'humidites_relatives',
    'lavabos_types',
    'mobiliers_integres_types',
    'mobiliers_laboratoires_types',
    'mobiliers_mobiles_types',
    'niveaux_confinements',
    'niveaux_bruits',
    'niveaux_vibrations_toleres',
    'taux_occupations',
    'outillages_types',
    'plomberies_divers_types',
    'pourcentages',
    'presences',
    'pressurisations_types',
    'prises_electriques_grades',
    'prises_electriques_types',
    'quantites',
    'temperatures',
    'toilettes_types',
    'csa_types',
    'soins_types',
    'finis_types',
    'hottes_types',
    'installations_types',
    'equipements_paratonnerres_types',
    'equipements_postes_peages_types',
    'equipements_ups_types',
    'equipements_systemes_intrusions_types',
    'equipements_controles_acces_types',
    'equipements_systemes_ventilations_types',
    'equipements_alarmes_incendies_types'
    'phasages_types'

************************************************************************************************************************* Ajout liste librairie *******************************************************************************************************************************************************************************

Mettre tous les noms des tebles qui composent les librairies

Recette (nom de la table / Nom affiché / Nom de la classe du formulaire d'ajout à la librairie)

*/

);


$tables['client']['usagers_acquisitions']                   		= array('nom' => txt('Acquisitions'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'UsagersAcquisitionsTypes');
$tables['client']['usagers_financements']                   		= array('nom' => txt('Financements'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'UsagersFinancementsTypes');
$tables['client']['usagers_contrats']                   			= array('nom' => txt('Gestion des dossiers contractuels'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'UsagersContratsTypes');
$tables['client']['usagers_equipements']                   		    = array('nom' => txt('Gestion des équipements'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'UsagersEquipementsTypes');
$tables['client']['usagers_qrt']                   					= array('nom' => txt('QRT-BCF'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'UsagersQrtTypes');
$tables['client']['usagers_suivis_budgetaires']      				= array('nom' => txt('Gestion des sources de financement'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'UsagersSuivisBudgetairesTypes');
$tables['client']['usagers_notifications']                   		= array('nom' => txt('Notifications'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'UsagersNotificationsTypes');
$tables['client']['usagers_entretiens']                   			= array('nom' => txt('Entretien'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'UsagersEntretiensTypes');
$tables['client']['usagers_mises_en_services']                   	= array('nom' => txt('Mise en service'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'UsagersMisesEnServicesTypes');
$tables['client']['usagers_surveillances']                   		= array('nom' => txt('Surveillance de travaux'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'UsagersSurveillancesTypes');
$tables['client']['usagers_administrations_bims']                   = array('nom' => txt('Administration BIM'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'UsagersAdministrationsBimsTypes');
$tables['client']['usagers_rapports']                   			= array('nom' => txt('Rapports'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'UsagersRapportsTypes');
$tables['client']['usagers_librairies']                   			= array('nom' => txt('Librairies'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'UsagersLibrairiesTypes');
$tables['client']['usagers_fiches']                   				= array('nom' => txt('Fiches'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'UsagersFichesTypes');
$tables['client']['usagers_structure_hierarchique']                 = array('nom' => txt('Structure hiérarchique'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'UsagersStructureHierarchiqueTypes');
$tables['client']['usagers_echeanciers_globaux']                    = array('nom' => txt('Échéanciers globaux'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'UsagersEcheanciersGlobauxTypes');
$tables['client']['usagers_inventaires']                    		= array('nom' => txt('Inventaires et déménagements'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'UsagersInventairesTypes');
$tables['client']['usagers_raccords']                    			= array('nom' => txt('Gestion des raccords'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'UsagersRaccordsTypes');
$tables['client']['usagers_gestion_portes']                   		= array('nom' => txt('Gestion des portes'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'UsagersGestionsPortesTypes');


$tables['client']['fournisseurs'] 				= array('nom' => txt('Fournisseurs'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer ce fournisseur?', false), 'className' => 'Fournisseur');
$tables['client']['fournisseurs_marques']              = array('nom' => txt('Fournisseurs/Marques'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'FournisseursMarques');
$tables['master']['correlations'] 				= array('nom' => txt('Correlations'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cette correlation?', false), 'className' => 'Correlation');
$tables['master']['elements'] 					= array('nom' => txt('Éléments'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'Element');
$tables['master']['elements_types'] 			= array('nom' => txt('Types d\'éléments'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer ce type d\'élément?', false), 'className' => 'ElementType');
$tables['master']['fonctionnements'] 			= array('nom' => txt('Fonctionnements'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer ce fonctionnement?', false), 'className' => 'Fonctionnement');
$tables['master']['periodes'] 					= array('nom' => txt('Périodes'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cette période?', false), 'className' => 'Periode');
$tables['master']['types_cartes'] 				= array('nom' => txt('Types de cartes'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer ce type de carte?', false), 'className' => 'TypeCarte');
$tables['master']['unites'] 					= array('nom' => txt('Unités'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cette unité?', false), 'className' => 'Unite');
$tables['master']['unites_types'] 				= array('nom' => txt('Types d\'unités'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer ce type d\'unité?', false), 'className' => 'UniteType');
$tables['master']['roles'] 						= array('nom' => txt('Rôles'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer ce rôle?', false), 'className' => 'Role');
$tables['master']['permissions'] 				= array('nom' => txt('Permissions'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cette permission?', false), 'className' => 'Permission');
$tables['master']['disciplines'] 				= array('nom' => txt('Discipline'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cette discipline?', false), 'className' => 'Discipline');
$tables['master']['fiches_types'] 				= array('nom' => txt('Type'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer ce type?', false), 'className' => 'FicheType');
$tables['client']['fiches_sous_types'] 			= array('nom' => txt('Sous-types de fiche'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer ce sous-type?', false), 'className' => 'FicheSousType');
$tables['client']['exports_automatiques'] 		= array('nom' => txt('Exports automatiques'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet export automatique?', false), 'className' => 'ExportsAutomatiques');


$tables['client']['procedures_entretiens']  						= array('nom' => txt('Procédures d\'entretien'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cette procédure d\'entretien?', false), 'className' => 'ProcedureEntretien');
$tables['client']['procedures_entretiens_planifiees']  				= array('nom' => txt('Procédures d\'entretien planifiées'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cette procédure d\'entretien planifiée?', false), 'className' => 'ProcedureEntretienPlanifiee');
$tables['client']['approvisionnements']  							= array('nom' => txt('Approvisionnements'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cette élément?', false), 'className' => 'Approvisionnements');
$tables['client']['procedures_inspections']  						= array('nom' => txt('Procédure de mise en service'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cette procédure de mise en service?', false), 'className' => 'ProcedureInspection');
$tables['client']['procedures_inspections_gbm']  					= array('nom' => txt('Procédure de mise en service'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cette procédure de mise en service?', false), 'className' => 'ProcedureInspectionGbm');
$tables['client']['procedures_mises_en_service_mep']  					= array('nom' => txt('Procédure de mise en service'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cette procédure de mise en service?', false), 'className' => 'ProcedureMiseEnServiceMep');
$tables['client']['inventaires'] 						            = array('nom' => txt('Inventaire'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cette procédure de mise en service?', false), 'className' => 'Inventaires');
$tables['client']['mises_en_services'] 						        = array('nom' => txt('Mise en service'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cette procédure de mise en service?', false), 'className' => 'MisesEnServices');
$tables['client']['equipements_mep']  						        = array('nom' => txt('Équipement MEP'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cette procédure de mise en service?', false), 'className' => 'EquipementsMep');
$tables['client']['inventaires_lignes']  						    = array('nom' => txt('Inventaire'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cette procédure de mise en service?', false), 'className' => 'InventairesLignes');
$tables['client']['inventaires_locaux']  						    = array('nom' => txt('Inventaire'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cette procédure de mise en service?', false), 'className' => 'InventairesLocaux');
$tables['client']['inventaires_locaux_champs']  				    = array('nom' => txt('Inventaire'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cette procédure de mise en service?', false), 'className' => 'InventairesLocauxChamps');
$tables['client']['logs_imports_inventaires_lignes']  				= array('nom' => txt('Inventaire'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cette procédure de mise en service?', false), 'className' => 'LogsImportsInventairesLignes');
$tables['client']['procedures_inspections_planifiees']  			= array('nom' => txt('Planification'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cette mise en service?', false), 'className' => 'ProcedureInspectionPlanifiee');
$tables['client']['procedures_inspections_planifiees_fiches']  		= array('nom' => txt('Tâches de la procédure de mise en service'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cette mise en service?', false), 'className' => 'ProcedureInspectionPlanifieeFiche');
$tables['client']['procedures_inspections_planifiees_gbm']  			= array('nom' => txt('Planification'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cette mise en service?', false), 'className' => 'ProcedureInspectionPlanifieeGbm');
$tables['client']['procedures_inspections_planifiees_gbm_equipements']  		= array('nom' => txt('Tâches de la procédure de mise en service'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cette mise en service?', false), 'className' => 'ProcedureInspectionPlanifieeGbmEquipement');
$tables['client']['procedures_mises_en_service_planifiees_mep']  			= array('nom' => txt('Planification'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cette mise en service?', false), 'className' => 'ProcedureMiseEnServicePlanifieeMep');
$tables['client']['procedures_mises_en_service_planifiees_mep_equipements']  		= array('nom' => txt('Tâches de la procédure de mise en service'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cette mise en service?', false), 'className' => 'ProcedureMiseEnServicePlanifieeMepEquipement');
$tables['client']['procedures_surveillances_planifiees']  			= array('nom' => txt('Gestion des surveillances'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cette surveillance?', false), 'className' => 'ProcedureSurveillancePlanifiee');
$tables['client']['procedures_surveillances_planifiees_fiches']  	= array('nom' => txt('Surveillance'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cette surveillance?', false), 'className' => 'ProcedureSurveillancePlanifieeFiche');
$tables['client']['questions']  									= array('nom' => txt('Gestion des QRT-BCF'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cette surveillance?', false), 'className' => 'Questions');
$tables['client']['formulaires_listes_parametres']  				= array('nom' => '', 'delConf' => txt('Êtes-vous certain de vouloir supprimer cette surveillance?', false), 'className' => 'FormulairesListesParametres');
$tables['client']['formulaires_customs_parametres']  				= array('nom' => '', 'delConf' => txt('Êtes-vous certain de vouloir supprimer cette surveillance?', false), 'className' => 'FormulairesCustomsParametres');
$tables['client']['notifications_gestion']  						= array('nom' => txt('Gestion des notifications'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'NotificationsGestion');
$tables['client']['approvisionnements_etapes_noms']          		= array('nom' => txt('Étapes d\'approvisionnement'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cette étapes d\'approvisionnement?', false), 'className' => 'ApprovisionnementsEtapesNoms');
$tables['client']['motifs_changements']          					= array('nom' => txt('Motifs de changements'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cette nomenclature?', false), 'className' => 'MotifsChangements');
$tables['client']['nomenclatures']          						= array('nom' => txt('Nomenclatures'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cette nomenclature?', false), 'className' => 'Nomenclature');
$tables['client']['amperages_types'] 								= array('nom' => txt('Capacités des barres omnibus'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'AmperagesTypes');
$tables['client']['fusibles_calibres_types'] 						= array('nom' => txt('Fusibles (calibres)'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'FusiblesCalibresTypes');
$tables['client']['appareils_chauffages_types'] 					= array('nom' => txt('Appareils de chauffage (types)'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'AppareilsChauffagesTypes');
$tables['client']['signaux_types'] 									= array('nom' => txt('Signaux (types)'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'SignauxTypes');
$tables['client']['cables_calibres_types'] 							= array('nom' => txt('Calibres des câbles'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'CablesCalibresTypes');
$tables['client']['calibres_protections_types'] 					= array('nom' => txt('Disjoncteurs (calibres)'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'CalibresProtectionsTypes');
$tables['client']['capacites_ruptures_types'] 						= array('nom' => txt('Capacités de rupture'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'CapacitesRupturesTypes');
$tables['client']['disjoncteurs_modeles'] 							= array('nom' => txt('Disjoncteurs (modèles)'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'DisjoncteursModeles');
$tables['client']['installations_disjoncteurs_types'] 				= array('nom' => txt('Disjoncteurs (installations)'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'InstallationsDisjoncteursTypes');
$tables['client']['equipements_electriques_types'] 					= array('nom' => txt('Équipements électriques'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'EquipementsElectriquesTypes');
$tables['client']['fabricants'] 									= array('nom' => txt('Fabricants'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'Fabricants');
$tables['client']['kva_types'] 										= array('nom' => txt('Transformateurs (puissances en kVA)'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'KvaTypes');
$tables['client']['montages_panneaux_types'] 						= array('nom' => txt('Montages de panneaux'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'MontagesPanneauxTypes');
$tables['client']['nombres_espaces_panneaux_types'] 				= array('nom' => txt('Nombre d\'espaces de panneaux'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'NombresEspacesPanneauxTypes');
$tables['client']['nombres_phases_types'] 							= array('nom' => txt('Nombre de phases'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'NombresPhasesTypes');
$tables['client']['tensions_types'] 								= array('nom' => txt('Tensions'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'TensionsTypes');
$tables['client']['transformateurs_types'] 							= array('nom' => txt('Transformateurs (modèles)'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'TransformateursTypes');
$tables['client']['materiaux_types'] 								= array('nom' => txt('Transformateurs (matériaux)'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'MateriauxTypes');
$tables['client']['boitiers_types'] 								= array('nom' => txt('Étanchéité des boîtiers'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'BoitiersTypes');
$tables['client']['conversions_noms_colonnes'] 						= array('nom' => txt('Administration champs booléens'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'ConversionsNomsColonnes');
$tables['client']['conversions_unites'] 							= array('nom' => txt('Conversion des unités'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'ConversionsUnites');
$tables['client']['conversions_recettes'] 							= array('nom' => txt('Matrices de conversion'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'ConversionsRecettes');
$tables['client']['principals_secondaires'] 						= array('nom' => txt('Principal / Secondaire'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'PrincipalsSecondaires');
$tables['client']['batiments_niveaux'] 								= array('nom' => txt('Structure hiérarchique des locaux'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'BatimentsNiveaux');
$tables['client']['uni_reglementations_articles_listes'] 			= array('nom' => txt('Articles'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'UniReglementationsArticlesListes');
$tables['client']['uni_reglementations_niveaux'] 					= array('nom' => txt('Structure articles'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'UniReglementationsNiveaux');
$tables['client']['equipements_bio_medicaux_types_categories'] 		= array('nom' => txt('Catégories d\'équipements GBM'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cette catégorie?', false), 'className' => 'EquipementsBioMedicauxTypesCategories');
$tables['client']['fiches_statuts'] 		                        = array('nom' => txt('Statuts de fiche'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cette catégorie?', false), 'className' => 'FichesStatuts');
$tables['client']['classifications_budgetaires_types'] 				= array('nom' => txt('Classification budgétaire'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'ClassificationsBudgetairesTypes');
$tables['client']['equipements_bio_medicaux_types'] 				= array('nom' => txt('Équipements biomédicaux'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer ce type d\'équipement biomédical?', false), 'className' => 'EquipementsBioMedicauxTypes');
$tables['client']['accessoires_architecturaux_types'] 				= array('nom' => txt('Accessoires architecturaux'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer ce type d\'accessoire architectural?', false), 'className' => 'AccessoiresArchitecturauxTypes');
$tables['client']['accessoires_sanitaires_ensembles_types'] 		= array('nom' => txt('Accessoires sanitaires (Ens.)'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'AccessoiresSanitairesEnsemblesTypes');
$tables['client']['accessoires_sanitaires_unites_types'] 			= array('nom' => txt('Accessoires sanitaires (Unit.)'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'AccessoiresSanitairesUnitesTypes');
$tables['client']['alimentations_electriques_types'] 				= array('nom' => txt('Alimentations électriques'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'AlimentationsElectriquesTypes');
$tables['client']['appels_locaux_generaux_equipements_types'] 		= array('nom' => txt('Appels locaux et généraux (médias)'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'AppelsLocauxGenerauxEquipementsTypes');
$tables['client']['appels_locaux_generaux_types'] 					= array('nom' => txt('Appels locaux et généraux (types)'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'AppelsLocauxGenerauxTypes');
$tables['client']['appels_locaux_generaux_zones'] 					= array('nom' => txt('Zones'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'AppelsLocauxGenerauxZones');
$tables['client']['appels_locaux_generaux_blocs'] 					= array('nom' => txt('Blocs'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'AppelsLocauxGenerauxBlocs');
$tables['client']['appels_locaux_generaux_etages'] 					= array('nom' => txt('Étages'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'AppelsLocauxGenerauxEtages');
$tables['client']['routes_types'] 									= array('nom' => txt('Routes (types)'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'RoutesTypes');
$tables['client']['bains_types'] 									= array('nom' => txt('Bains'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'BainsTypes');
$tables['client']['gestions_portes_types'] 							= array('nom' => txt('Portes (types)'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'GestionsPortesTypes');
$tables['client']['procedures_inspections_planifiees_responsables_types'] = array('nom' => txt('Responsables planifications mises en service'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'ProceduresInspectionsPlanifieesResponsablesTypes');
$tables['client']['gestions_portes_quincailleries_types'] 			= array('nom' => txt('Portes (quincaillerie)'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'GestionsPortesQuincailleriesTypes');
$tables['client']['evacuations_types'] 								= array('nom' => txt('Évacuations (types)'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'EvacuationsTypes');
$tables['client']['habillages_fenetres_types'] 						= array('nom' => txt('Habillage de fenêtre'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'HabillagesFenetresTypes');
$tables['client']['murs_types'] 									= array('nom' => txt('Murs'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'MursTypes');
$tables['client']['planchers_types'] 								= array('nom' => txt('Planchers'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'PlanchersTypes');
$tables['client']['fenetres_types'] 								= array('nom' => txt('Fenêtres'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'FenetresTypes');
$tables['client']['plafonds_types'] 								= array('nom' => txt('Plafonds'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'PlafondsTypes');
$tables['client']['portes_types'] 									= array('nom' => txt('Portes'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'PortesTypes');
$tables['client']['protecteurs_murals_types'] 						= array('nom' => txt('Protecteurs muraux'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'ProtecteursMuralsTypes');
$tables['client']['interieurs_exterieurs_types'] 					= array('nom' => txt('Intérieur/extérieur'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'InterieursExterieursTypes');
$tables['client']['douches_types'] 									= array('nom' => txt('Douches'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'DouchesTypes');
$tables['client']['cameras_securites_types'] 						= array('nom' => txt('Caméras de sécurité'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'CamerasSecuritesTypes');
$tables['client']['changements_airs_heures'] 						= array('nom' => txt('Changements d\'air à l\'heure'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'ChangementsAirsHeures');
$tables['client']['prises_communications_sorties_types'] 			= array('nom' => txt('Prises de communication'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'PrisesCommunicationsSortiesTypes');
$tables['client']['evacuations_speciales_types'] 					= array('nom' => txt('Évacuations spéciales'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'EvacuationsSpecialesTypes');
$tables['client']['eaux_purifies_types'] 							= array('nom' => txt('Eaux purifiées'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'EauxPurifiesTypes');
$tables['client']['eclairages_controles_types'] 					= array('nom' => txt('Contrôles d\'éclairage'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'EclairagesControlesTypes');
$tables['client']['raccords_directs_types']							= array('nom' => txt('Raccords directs (types)'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'RaccordsDirectsTypes');
$tables['client']['eclairages_lumens_types'] 						= array('nom' => txt('Lumens'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'EclairagesLumensTypes');
$tables['client']['eclairages_ratios_urgences'] 					= array('nom' => txt('Ratios d\'éclairage sur l\'urgence'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'EclairagesRatiosUrgences');
$tables['client']['eclairages_interieurs_types'] 					= array('nom' => txt('Éclairage intérieur'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'EclairagesInterieursTypes');
$tables['client']['eclairages_exterieurs_types'] 					= array('nom' => txt('Éclairage extérieur'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'EclairagesExterieursTypes');
$tables['client']['eclairages_stationnements_types'] 				= array('nom' => txt('Éclairage de stationnement'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'EclairagesStationnementsTypes');
$tables['client']['eclairages_routiers_types'] 						= array('nom' => txt('Éclairage routier'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'EclairagesRoutiersTypes');
$tables['client']['eclairages_parcs_types'] 						= array('nom' => txt('Éclairage de parcs'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'EclairagesParcsTypes');
$tables['client']['appels_gardes_types'] 							= array('nom' => txt('Appels de gardes'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'AppelsGardesTypes');
$tables['client']['appels_urgences_types'] 							= array('nom' => txt('Appels d\'urgence'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'AppelsUrgencesTypes');
$tables['client']['equipements_medicaux_specialises_types'] 		= array('nom' => txt('EMS'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'EquipementsMedicauxSpecialisesTypes');
$tables['client']['equipements_multimedias_types'] 					= array('nom' => txt('Multimédias'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'EquipementsMultimediasTypes');
$tables['client']['eviers_types'] 									= array('nom' => txt('Éviers '), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'EviersTypes');
$tables['client']['existants_nouveaux'] 							= array('nom' => txt('Existants ou nouveaux'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'ExistantsNouveaux');
$tables['client']['fournis_installes'] 								= array('nom' => txt('Fournis ou installés'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'FournisInstalles');
$tables['client']['frequentations'] 								= array('nom' => txt('Fréquentations'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'Frequentations');
$tables['client']['indices_transmissions_sons_types'] 				= array('nom' => txt('Indices de transmission du son'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'IndicesTransmissionsSonsTypes');
$tables['client']['verres_interieurs_types'] 						= array('nom' => txt('Verres intérieurs'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'VerresInterieursTypes');
$tables['client']['verres_exterieurs_types'] 						= array('nom' => txt('Verres extérieurs'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'VerresExterieursTypes');
$tables['client']['revetements_murals_types'] 						= array('nom' => txt('Revêtements muraux'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'RevetementsMuralsTypes');
$tables['client']['gaz_medicaux_types'] 							= array('nom' => txt('Gaz médicaux'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'GazMedicauxTypes');
$tables['client']['horaire_normal_utilisation'] 					= array('nom' => txt('Horaires normales'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'HoraireNormalUtilisation');
$tables['client']['horloges_types'] 								= array('nom' => txt('Horloges'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'HorlogesTypes');
$tables['client']['humidites_relatives'] 							= array('nom' => txt('Humidités relatives'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'HumiditesRelatives');
$tables['client']['lavabos_types'] 									= array('nom' => txt('Lavabos'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'LavabosTypes');
$tables['client']['mobiliers_integres_types'] 						= array('nom' => txt('Mobiliers intégrés'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'MobiliersIntegresTypes');
$tables['client']['mobiliers_laboratoires_types'] 					= array('nom' => txt('Mobiliers de laboratoire'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'MobiliersLaboratoiresTypes');
$tables['client']['mobiliers_mobiles_types'] 						= array('nom' => txt('Mobilier mobile'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'MobiliersMobilesTypes');
$tables['client']['niveaux_confinements'] 							= array('nom' => txt('Niveaux de confinement'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'NiveauxConfinements');
$tables['client']['niveaux_bruits'] 								= array('nom' => txt('Niveaux de bruit'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'NiveauxBruits');
$tables['client']['niveaux_vibrations_toleres'] 					= array('nom' => txt('Niveaux de vibration'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'NiveauxVibrationsToleres');
$tables['client']['taux_occupations'] 								= array('nom' => txt('Taux d\'occupations'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'TauxOccupations');
$tables['client']['outillages_types'] 								= array('nom' => txt('Outillage'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'OutillagesTypes');
$tables['client']['plomberies_divers_types'] 						= array('nom' => txt('Plomberie diverse'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'PlomberiesDiversTypes');
$tables['client']['pourcentages'] 									= array('nom' => txt('Pourcentages'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'Pourcentages');
$tables['client']['presences'] 										= array('nom' => txt('Présences'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'Presences');
$tables['client']['pressurisations_types'] 							= array('nom' => txt('Pressurisations'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'PressurisationsTypes');
$tables['client']['prises_electriques_grades'] 						= array('nom' => txt('Grades des prises'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'PrisesElectriquesGrades');
$tables['client']['prises_electriques_types'] 						= array('nom' => txt('Prises électriques'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'PrisesElectriquesTypes');
$tables['client']['quantites'] 										= array('nom' => txt('Quantites'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'Quantites');
$tables['client']['temperatures'] 									= array('nom' => txt('Températures'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'Temperatures');
$tables['client']['toilettes_types'] 								= array('nom' => txt('Toilettes'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'ToilettesTypes');
$tables['client']['csa_types'] 										= array('nom' => txt('CSA'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'CsaTypes');
$tables['client']['csa_autres_types'] 								= array('nom' => txt('CSA (tableau 1)'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'CsaAutresTypes');
$tables['client']['csa_electricites_types'] 						= array('nom' => txt('CSA selon Z32'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'CsaElectricitesTypes');
$tables['client']['redondances_types'] 								= array('nom' => txt('Redondances (types)'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'RedondancesTypes');
$tables['client']['filtrations_types'] 								= array('nom' => txt('Filtrations (types)'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'FiltrationsTypes');
$tables['client']['soins_types'] 									= array('nom' => txt('Types de soins'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'SoinsTypes');
$tables['client']['finis_types'] 									= array('nom' => txt('Finis'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'FinisTypes');
$tables['client']['hottes_types'] 									= array('nom' => txt('Hottes (types)'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'HottesTypes');
$tables['client']['installations_types'] 							= array('nom' => txt('Installations'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'InstallationsTypes');
$tables['client']['questions_categories'] 							= array('nom' => txt('Catégories QRT-BCF'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'QuestionsCategories');
$tables['client']['pseudos'] 										= array('nom' => txt('Pseudonymes'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'Pseudos');
$tables['client']['equipements_paratonnerres_types'] 				= array('nom' => txt('Paratonnerres'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'EquipementsParatonnerresTypes');
$tables['client']['equipements_postes_peages_types']		 		= array('nom' => txt('Postes de péage'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'EquipementsPostesPeagesTypes');
$tables['client']['equipements_ups_types'] 							= array('nom' => txt('UPS'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'EquipementsUpsTypes');
$tables['client']['equipements_systemes_intrusions_types'] 			= array('nom' => txt('Systèmes d\'intrusion'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'EquipementsSystemesIntrusionsTypes');
$tables['client']['equipements_controles_acces_types'] 				= array('nom' => txt('Contrôles d\'accès'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'EquipementsControlesAccesTypes');
$tables['client']['equipements_systemes_ventilations_types'] 		= array('nom' => txt('Systèmes de ventilation'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'EquipementsSystemesVentilationsTypes');
$tables['client']['equipements_alarmes_incendies_types'] 			= array('nom' => txt('Alarme incendie'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'EquipementsAlarmesIncendiesTypes');
$tables['client']['phasages_types'] 								= array('nom' => txt('Phasage'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'PhasagesTypes');
$tables['client']['envois_receptions_types']                      	= array('nom' => txt('Transports pneumatiques (fonctions)'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'EnvoisReceptionsTypes');
$tables['client']['cuves_types']                                   	= array('nom' => txt('Cuves'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'CuvesTypes');
$tables['client']['fontaines_refrigerees_types']                   	= array('nom' => txt('Fontaines réfrigérées'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'FontainesRefrigereesTypes');
$tables['client']['plomberies_securites_urgences_types']          	= array('nom' => txt('Équipements de sécurité/urgence'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'PlomberiesSecuritesUrgencesTypes');
$tables['client']['urinoirs_types']                               	= array('nom' => txt('Urinoirs'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'UrinoirsTypes');
$tables['client']['raccords_electriques_types']                		= array('nom' => txt('Raccords électriques'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'RaccordsElectriquesTypes');
$tables['client']['raccords_plomberies_types']                   	= array('nom' => txt('Raccords de plomberie (types)'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'RaccordsPlomberiesTypes');
$tables['client']['raccords_plomberies_materiaux']               	= array('nom' => txt('Raccords de plomberie (matériaux)'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'RaccordsPlomberiesMateriaux');
$tables['client']['raccords_plomberies_formats_types']           	= array('nom' => txt('Format de raccord'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'RaccordsPlomberiesFormatsTypes');
$tables['client']['fusibles_actions_types']                      	= array('nom' => txt('Fusibles (actions)'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'FusiblesActionsTypes');
$tables['client']['fusibles_formats_types']                       	= array('nom' => txt('Fusibles (formats)'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'FusiblesFormatsTypes');
$tables['client']['hp_types']                                      	= array('nom' => txt('Moteurs (puissances en HP)'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'HpTypes');
$tables['client']['cables_types']                                	= array('nom' => txt('Types de câblage'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'CablesTypes');
$tables['client']['conduits_types']                              	= array('nom' => txt('Conduits (types/calibres)'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'ConduitsTypes');
$tables['client']['diametres_conduits_electricites']              	= array('nom' => txt('Conduits (diamètres standards)'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'DiametresConduitsElectricites');
$tables['client']['diametres_conduits_plomberies']               	= array('nom' => txt('Diamètres des conduits de plomberie'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'DiametresConduitsPlomberies');
$tables['client']['diametres_conduits_canalisations']            	= array('nom' => txt('Diamètres des canalisations'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'DiametresConduitsCanalisations');
$tables['client']['demarreurs_compositions_types']                	= array('nom' => txt('Démarreurs/panneaux (compositions)'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'DemarreursCompositionsTypes');
$tables['client']['dispositifs_electriques_champs_types']       	= array('nom' => txt('Dispositifs de champs'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'DispositifsElectriquesChampsTypes');
$tables['client']['demarreurs_types']                        		= array('nom' => txt('Dispositifs de raccordement'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'DemarreursTypes');
$tables['client']['charges_types']                            		= array('nom' => txt('Charges (types)'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'ChargesTypes');
$tables['client']['marques']                                 		= array('nom' => txt('Marques'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cette marque?', false), 'className' => 'Marque');
$tables['client']['modeles']                               			= array('nom' => txt('Modèles'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer ce modèle?', false), 'className' => 'Modele');
$tables['client']['priorites'] 										= array('nom' => txt('Transports pneumatiques (priorités)'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'Priorites');
$tables['client']['transfo_temperatures']                       	= array('nom' => txt('Transformateurs (températures)'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'TransfoTemperatures');
$tables['client']['transfo_impedances']                   			= array('nom' => txt('Transformateurs (impédances)'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'TransfoImpedances');
$tables['client']['transfo_k_facteurs']                  			= array('nom' => txt('Transformateurs (facteurs k)'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'TransfoFacteurs');
$tables['client']['formulaires_readonly']              				= array('nom' => txt('Verrouillage des champs'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', '', false), 'className' => 'FormulairesReadonly');
$tables['client']['formulaires_hidden']              				= array('nom' => txt('Désactivation des champs'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', '', false), 'className' => 'FormulairesHidden');
$tables['client']['modeles_impressions']              				= array('nom' => txt('Modèles d\'impression'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', '', false), 'className' => 'ModelesImpressions');
$tables['client']['formulaires_limitations']                        = array('nom' => txt('Limitation des formulaires (ajout/retrait interdit)'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', '', false), 'className' => 'FormulairesLimitations');
$tables['client']['questions_groupes_usagers']                 		= array('nom' => txt('Groupes d\'usagers QRT-BCF'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', '', false), 'className' => 'QuestionsGroupesUsagers');
$tables['client']['acquisitions_projets_groupes_usagers']           = array('nom' => txt('Groupes d\'usagers QRT-BCF'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', '', false), 'className' => 'AcquisitionsProjetsGroupesUsagers');
$tables['client']['notifications_acquisitions_projets_jalons']      = array('nom' => txt('Groupes d\'usagers QRT-BCF'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', '', false), 'className' => 'NotificationsAcquisitionsProjetsJalons');
$tables['client']['fluides_types']                         			= array('nom' => txt('Fluides (types)'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'FluidesTypes');
$tables['client']['compresseurs_types']                 			= array('nom' => txt('Compresseurs (types)'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'CompresseursTypes');
$tables['client']['coolants_types']                          		= array('nom' => txt('Réfrigérants (types)'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'CoolantsTypes');
$tables['client']['duplexe_simplex']                       			= array('nom' => txt('Pompe (duplex/simplex)'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'DuplexeSimplex');
$tables['client']['uta_classifications_types']           			= array('nom' => txt('UTA (classifications)'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'UtaClassificationsTypes');
$tables['client']['moteurs_chassis_types']                 			= array('nom' => txt('Moteurs (chassis)'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'MoteursChassisTypes');
$tables['client']['niveaux_criticites']                        		= array('nom' => txt('Niveaux de criticité'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'NiveauxCriticites');
$tables['client']['systemes_types']                             	= array('nom' => txt('Systèmes (types)'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'SystemesTypes');
$tables['client']['roues_ventilateurs_types']                  		= array('nom' => txt('Roues de ventilateurs (types)'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'RouesVentilateursTypes');
$tables['client']['ailettes_serpentins_modeles']              		= array('nom' => txt('Ailettes de serpentins (modèles)'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'AilettesSerpentinsModeles');
$tables['client']['ailettes_serpentins_materiaux']            		= array('nom' => txt('Ailettes de serpentins (matériaux)'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'AilettesSerpentinsMateriaux');
$tables['client']['equipements_installations_methodes']        		= array('nom' => txt('Méthodes d\'installation'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'EquipementsInstallationsMethodes');
$tables['client']['serpentins_fonctions_types']                  	= array('nom' => txt('Serpentins (fonctions)'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'SerpentinsFonctionsTypes');
$tables['client']['grilles_diffuseurs_materiaux_types']         	= array('nom' => txt('Grilles et diffuseurs (matériaux)'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'GrillesDiffuseursMateriauxTypes');
$tables['client']['grilles_diffuseurs_garnitures_types']      		= array('nom' => txt('Grilles et diffuseurs (garnitures)'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'GrillesDiffuseursGarnituresTypes');
$tables['client']['grilles_diffuseurs_finitions_types']          	= array('nom' => txt('Grilles et diffuseurs (finitions)'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'GrillesDiffuseursFinitionsTypes');
$tables['client']['tour_eau_types']                           		= array('nom' => txt('Tours d\'eau (types)'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'TourEauTypes');
$tables['client']['diffuseur_types']                            	= array('nom' => txt('Diffuseurs (types)'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'DiffuseurTypes');
$tables['client']['filtre_efficacites']                           	= array('nom' => txt('Filtres (efficacités)'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'FiltreEfficacites');
$tables['client']['formulaires_listes']                          	= array('nom' => txt('Formulaires intrinsèques d\'éléments'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'FormulairesListes');
$tables['client']['formulaires_customs']                          	= array('nom' => txt('Formulaires personnalisés'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'FormulairesCustoms');
$tables['client']['echeanciers']                                 	= array('nom' => txt('Gestion des échéanciers'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'Echeanciers');
$tables['client']['evenements_systeme']                       	    = array('nom' => txt('Journal d\'événements système'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'EvenementsSysteme');
$tables['client']['admin_liste_colonnes_bio_medicaux']              = array('nom' => txt('Admininstration des colonnes'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'AdminListeColonnesBioMedicaux');
$tables['client']['acquisitions_projets']                       	= array('nom' => txt('Gestion des acquisitions'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'AcquisitionsProjets');
$tables['client']['financements_projets']                       	= array('nom' => txt('Gestion des financements'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'FinancementsProjets');
$tables['client']['contrats_projets']                       	    = array('nom' => txt('Gestion des dossiers contractuels'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'ContratsProjets');
$tables['client']['suivis_budgetaires']                             = array('nom' => txt('Gestion des sources de financement'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'SuivisBudgetaires');
$tables['client']['acquisitions_projets_groupes_equipements']   	= array('nom' => txt('Gestion des groupes d\'équipements'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'AcquisitionsProjetsGroupesEquipements');
$tables['client']['financements_projets_groupes_equipements']   	= array('nom' => txt('Gestion des groupes d\'équipements'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'FinancementsProjetsGroupesEquipements');
$tables['client']['financements_projets_depots']   	                = array('nom' => txt('Gestion des dépôts'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'FinancementsProjetsDepots');
$tables['client']['suivis_budgetaires_administrations']   	    = array('nom' => txt('Administration'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'SuivisBudgetairesAdministrations');
$tables['client']['financements_projets_administrations']   	    = array('nom' => txt('Administration'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'FinancementsProjetsAdministrations');
$tables['client']['financements_projets_administrations_unifiees']  = array('nom' => txt('Administration'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'FinancementsProjetsAdministrationsUnifiees');
$tables['client']['acquisitions_projets_administrations']   	    = array('nom' => txt('Administration'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'AcquisitionsProjetsAdministrations');
$tables['client']['contrats_projets_administrations']   	    = array('nom' => txt('Administration'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'ContratsProjetsAdministrations');
$tables['client']['priorites_acquisitions_types']               	= array('nom' => txt('Priorités d\'acquisition'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'PrioritesAcquisitionsTypes');
$tables['client']['priorites_financements_types']               	= array('nom' => txt('Priorités de financement'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'PrioritesFinancementsTypes');
$tables['client']['modes_acquisitions_types']                    	= array('nom' => txt('Modes d\'acquisition'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'ModesAcquisitionsTypes');
$tables['client']['modes_financements_types']                    	= array('nom' => txt('Modes de financement'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'ModesFinancementsTypes');
$tables['client']['portees_appels_offres_types']                	= array('nom' => txt('Portées d\'appel d\'offre'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'PorteesAppelsOffresTypes');
$tables['client']['acquisitions_jalons_modeles']                	= array('nom' => txt('Jalons modèles'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer ce jalon modèle?', false), 'className' => 'AcquisitionsJalonsModelesTypes');
$tables['client']['financements_jalons_modeles']                	= array('nom' => txt('Jalons modèles'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer ce jalon modèle?', false), 'className' => 'FinancementsJalonsModelesTypes');
$tables['client']['contrats_jalons_modeles']                	    = array('nom' => txt('Jalons modèles'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer ce jalon modèle?', false), 'className' => 'ContratsJalonsModeles');
$tables['client']['espaces_types']                           		= array('nom' => txt('Types d\'espaces'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'EspacesTypes');
$tables['client']['capacites_portantes_types']                 		= array('nom' => txt('Capacités portantes'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'CapacitesPortantesTypes');
$tables['client']['planeites_types']                              	= array('nom' => txt('Planéités'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'PlaneitesTypes');
$tables['client']['vibrations_types']                            	= array('nom' => txt('Vibrations'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'VibrationsTypes');
$tables['client']['tolerances_mouvements_types']            		= array('nom' => txt('Tolérances aux mouvements'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'TolerancesMouvementsTypes');
$tables['client']['uni_cloisons_types'] 							= array('nom' => txt('(C1010) Cloisons intérieures'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'UniCloisonsTypes');
$tables['client']['uni_portes_types']                               = array('nom' => txt('(C1020) Portes intérieures'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'UniPortesTypes');
$tables['client']['uni_exigences_fonctionnelles_types'] 			= array('nom' => txt('(section 03.02) Exigences fonctionnelles'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'UniExigencesFonctionnellesTypes');
$tables['client']['uni_finitions_murs_types']                      	= array('nom' => txt('(C3010) Finitions des murs'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'UniFinitionsMursTypes');
$tables['client']['uni_finitions_planchers_types']               	= array('nom' => txt('(C3020) Finitions des planchers'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'UniFinitionsPlanchersTypes');
$tables['client']['uni_finitions_plinthes_types']              		= array('nom' => txt('(C3020) Finitions des plinthes'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'UniFinitionsPlinthesTypes');
$tables['client']['uni_finitions_plafonds_types']              		= array('nom' => txt('(C3030) Finitions des plafonds'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'UniFinitionsPlafondsTypes');
$tables['client']['uni_accessoires_protections_incendies_types']   	= array('nom' => txt('(D4030) Accessoires de protection incendie'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'UniAccessoiresProtectionsIncendiesTypes');
$tables['client']['uni_accessoires_integres_types']      			= array('nom' => txt('(C1030) Accessoires intégrés'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'UniAccessoiresIntegresTypes');
$tables['client']['uni_ameublements_decorations_types']  			= array('nom' => txt('(E2010/E2020) Ameublement et décoration'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'UniAmeublementsDecorationsTypes');
$tables['client']['uni_appareils_plomberies_types']      			= array('nom' => txt('(D2010) Appareils de plomberie'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'UniAppareilsPlomberiesTypes');
$tables['client']['uni_autres_systemes_plomberies_types']  			= array('nom' => txt('(D2090) Autres systèmes de plomberie'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'UniAutresSystemesPlomberiesTypes');
$tables['client']['uni_communications_informatiques_types']			= array('nom' => txt('(xxxxx) Communication et informatique'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'UniCommunicationsInformatiquesTypes');
$tables['client']['uni_distributions_cvcas_types']        			= array('nom' => txt('(D3040) Distribution de CVCA'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'UniDistributionsCvcasTypes');
$tables['client']['uni_distributions_secondaires_types']   			= array('nom' => txt('(D5020) Distribution secondaire'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'UniDistributionsSecondairesTypes');
$tables['client']['uni_eclairages_types']   						= array('nom' => txt('(D5020) Éclairage'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'UniEclairagesTypes');
$tables['client']['uni_equipements_types']       					= array('nom' => txt('(E1010/E1020/E1030) Équipements'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'UniEquipementsTypes');
$tables['client']['uni_gicleurs_types']       						= array('nom' => txt('(D4010) Gicleurs'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'UniGicleursTypes');
$tables['client']['uni_securites_types']       						= array('nom' => txt('(LCCCC) Sécurité'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'UniSecuritesTypes');
$tables['client']['uni_reglementations_types']       				= array('nom' => txt('Types d\'articles'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'UniReglementationsTypes');
$tables['client']['sites_livraisons_types']       					= array('nom' => txt('Sites de livraison'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'SitesLivraisonsTypes');
$tables['client']['locals_transitoires_types']       				= array('nom' => txt('Locaux transitoires'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'LocalsTransitoiresTypes');
$tables['client']['conversions_unites_types']       				= array('nom' => txt('Unités'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'ConversionsUnitesTypes');
$tables['client']['formulaires_types']       						= array('nom' => txt('Types de formulaires'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'FormulairesTypes');
$tables['client']['phases_incidences'] 								= array('nom' => txt('Phases d\'incidence'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'PhasesIncidences');
$tables['client']['conditions_vetustes_types'] 						= array('nom' => txt('Conditions / vétustés'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'ConditionsVetustesTypes');
$tables['client']['bloquants_types'] 								= array('nom' => txt('Bloquants'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'BloquantsTypes');
$tables['client']['bloquants_financements_types'] 					= array('nom' => txt('Bloquants'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'BloquantsFinancementsTypes');
$tables['client']['equipements_bio_medicaux_types_directions_responsables'] 		= array('nom' => txt('Directions responsables'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'EquipementsBioMedicauxTypesDirectionsResponsables');
$tables['client']['equipements_bio_medicaux_types_responsables_logistiques'] 		= array('nom' => txt('Responsables logistique'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'EquipementsBioMedicauxTypesResponsablesLogistiques');
$tables['client']['equipements_bio_medicaux_types_sous_categories'] 				= array('nom' => txt('Sous-catégories'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'EquipementsBioMedicauxTypesSousCategories');
$tables['client']['equipements_bio_medicaux_types_categories_responsabilite'] 		= array('nom' => txt('Catégories de responsabilités'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'EquipementsBioMedicauxTypesCategoriesResponsabilites');
$tables['client']['acquisitions_sous_volets_responsables_types'] 					= array('nom' => txt('Sous-volets responsables'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'AcquisitionsSousVoletsResponsablesTypes');
$tables['client']['acquisitions_phases_incidences_contraignantes_types'] 			= array('nom' => txt('Phases d\'incidences contaignantes'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'AcquisitionsPhasesIncidencesContraignantesTypes');
$tables['client']['acquisitions_projets_priorisations_types'] 						= array('nom' => txt('Priorisations'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'AcquisitionsProjetsPriorisationsTypes');
$tables['client']['acquisitions_projets_modes_sollicitations_types'] 				= array('nom' => txt('Modes de sollicitation'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'AcquisitionsProjetsModesSollicitationsTypes');
$tables['client']['acquisitions_projets_modes_adjudications_types'] 				= array('nom' => txt('Modes d\'adjudication'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'AcquisitionsProjetsModesAdjudicationsTypes');
$tables['client']['acquisitions_responsables_dossiers_types'] 						= array('nom' => txt('Responsables de dossier (liste partagée)'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'AcquisitionsResponsablesDossiersTypes');
$tables['client']['acquisitions_responsables_dossiers_a_types'] 					= array('nom' => txt('Responsables de dossier A'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'AcquisitionsResponsablesDossiersATypes');
$tables['client']['acquisitions_responsables_dossiers_b_types'] 					= array('nom' => txt('Responsables de dossier B'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'AcquisitionsResponsablesDossiersBTypes');
$tables['client']['acquisitions_responsables_dossiers_c_types'] 					= array('nom' => txt('Responsables de dossier C'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'AcquisitionsResponsablesDossiersCTypes');
$tables['client']['acquisitions_responsables_dossiers_d_types'] 					= array('nom' => txt('Responsables de dossier D'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'AcquisitionsResponsablesDossiersDTypes');
$tables['client']['acquisitions_responsables_dossiers_e_types'] 					= array('nom' => txt('Responsables de dossier E'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'AcquisitionsResponsablesDossiersETypes');
$tables['client']['acquisitions_responsables_dossiers_f_types'] 					= array('nom' => txt('Responsables de dossier F'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'AcquisitionsResponsablesDossiersFTypes');
$tables['client']['acquisitions_responsables_dossiers_g_types'] 					= array('nom' => txt('Responsables de dossier G'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'AcquisitionsResponsablesDossiersGTypes');
$tables['client']['acquisitions_responsables_dossiers_h_types'] 					= array('nom' => txt('Responsables de dossier H'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'AcquisitionsResponsablesDossiersHTypes');
$tables['client']['acquisitions_responsables_dossiers_i_types'] 					= array('nom' => txt('Responsables de dossier I'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'AcquisitionsResponsablesDossiersITypes');
$tables['client']['acquisitions_responsables_dossiers_j_types'] 					= array('nom' => txt('Responsables de dossier J'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'AcquisitionsResponsablesDossiersJTypes');
$tables['client']['acquisitions_menus_supplementaires_a_types'] 					= array('nom' => txt('Menu supplémentaire A'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'AcquisitionsMenusSupplementairesATypes');
$tables['client']['exemptions_verrouillages']                     		            = array('nom' => txt('Gestion des équipements exempts de verrouillage'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'ExemptionsVerrouillages');
$tables['client']['visualisateur3d_fichiers']                     		            = array('nom' => txt('Gestion des fichiers IFC'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'Visualisateur3dFichiers');
$tables['client']['cedules_demenagements']                     		                = array('nom' => txt('Cédule de déménagement'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'CedulesDemenagements');
$tables['client']['acquisitions_responsables_installations_types'] 					= array('nom' => txt("Responsables de l'installation"), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'AcquisitionsResponsablesInstallationsTypes');

$tables['client']['financements_responsables_dossiers_a_types'] 					= array('nom' => txt('Responsables A'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'FinancementsResponsablesDossiersATypes');
$tables['client']['financements_responsables_dossiers_b_types'] 					= array('nom' => txt('Responsables B'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'FinancementsResponsablesDossiersBTypes');
$tables['client']['financements_responsables_dossiers_c_types'] 					= array('nom' => txt('Responsables C'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'FinancementsResponsablesDossiersCTypes');
$tables['client']['financements_responsables_dossiers_d_types'] 					= array('nom' => txt('Responsables D'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'FinancementsResponsablesDossiersDTypes');
$tables['client']['financements_responsables_dossiers_e_types'] 					= array('nom' => txt('Responsables E'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'FinancementsResponsablesDossiersETypes');


$tables['client']['financements_regrouppements_types'] 					= array('nom' => txt('Regroupements'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'FinancementsRegrouppementsTypes');


$tables['client']['contrats_responsables_dossiers_types'] 							= array('nom' => txt('Responsables de dossier contractuel'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'ContratsResponsablesDossiersTypes');
$tables['client']['conges_feries_types'] 											= array('nom' => txt('Congés fériés'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'CongesFeriesTypes');

/*
$tables['client']['composantes_electriques_types']                	= array('nom' => txt('Composante électrique'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'ComposantesElectriquesTypes');
$tables['client']['accessoires_architecturaux']						= array('nom' => txt('Accessoires_architecturaux'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'AccessoiresArchitecturaux');
$tables['client']['accessoires_sanitaires_ensembles'] 				= array('nom' => txt('Ensembleccessoires_sanitaires_ensembles'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'AccessoiresSanitairesEnsembles');
$tables['client']['accessoires_sanitaires_unite'] 					= array('nom' => txt('accessoires_sanitaires_unite'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'AccessoiresSanitairesUnite');
$tables['client']['appels_locaux_generaux'] 						= array('nom' => txt('appels_locaux_generaux'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'AppelsLocauxGeneraux');
$tables['client']['douches'] 										= array('nom' => txt('douches'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'Douches');
$tables['client']['bains']											= array('nom' => txt('bains'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'Bains');
$tables['client']['cameras_securites'] 								= array('nom' => txt('cameras_securites'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'CamerasSecurites');
$tables['client']['evacuations_speciales'] 							= array('nom' => txt('evacuations_speciales'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'EvacuationsSpeciales');
$tables['client']['eaux_purifies']									= array('nom' => txt('eaux_purifies'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'EauxPurifies');
$tables['client']['eclairages_interieurs'] 							= array('nom' => txt('eclairages_interieurs'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'EclairagesInterieurs');
$tables['client']['eclairages_exterieurs'] 							= array('nom' => txt('eclairages_exterieurs'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'EclairagesExterieurs');
$tables['client']['eclairages_stationnements']						= array('nom' => txt('eclairages_stationnements'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'EclairagesStationnements');
$tables['client']['eclairages_routiers'] 							= array('nom' => txt('eclairages_routiers'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'EclairagesRoutiers');
$tables['client']['eclairages_parcs'] 								= array('nom' => txt('eclairages_parcs'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'EclairagesParcs');
$tables['client']['appels_gardes']									= array('nom' => txt('appels_gardes'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'AppelsGardes');
$tables['client']['appels_urgences'] 								= array('nom' => txt('appels_urgences'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'AppelsUrgences');
$tables['client']['equipements_medicaux_specialises'] 				= array('nom' => txt('equipements_medicaux_specialises'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'EquipementsMedicauxSpecialises');
$tables['client']['equipements_multimedias']						= array('nom' => txt('equipements_multimedias'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'EquipementsMultimedias');
$tables['client']['eviers'] 										= array('nom' => txt('eviers'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'Eviers');
$tables['client']['gaz_medicaux']									= array('nom' => txt('gaz_medicaux'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'GazMedicaux');
$tables['client']['horloges'] 										= array('nom' => txt('horloges'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'Horloges');
$tables['client']['hottes']											= array('nom' => txt('hottes'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'Hottes');
$tables['client']['lavabos']			  							= array('nom' => txt('lavabos'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'Lavabos');
$tables['client']['mobiliers_integres']								= array('nom' => txt('mobiliers_integres'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'MobiliersIntegres');
$tables['client']['mobiliers_laboratoires']							= array('nom' => txt('mobiliers_laboratoires'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'MobiliersLaboratoires');
$tables['client']['mobiliers_mobiles']				   				= array('nom' => txt('mobiliers_mobiles'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'MobiliersMobiles');
$tables['client']['outillages']			 							= array('nom' => txt('outillages'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'Outillages');
$tables['client']['plomberies_divers'] 								= array('nom' => txt('plomberies_divers'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'PlomberiesDivers');
$tables['client']['prises_electriques_interieurs'] 					= array('nom' => txt('prises_electriques_interieurs'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'PrisesElectriquesInterieurs');
$tables['client']['prises_electriques_exterieurs'] 					= array('nom' => txt('prises_electriques_exterieurs'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'PrisesElectriquesExterieurs');
$tables['client']['prises_electriques_stationnements'] 				= array('nom' => txt('prises_electriques_stationnements'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'PisesElectriquesStationnements');
$tables['client']['prises_communications'] 							= array('nom' => txt('prises_communications'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'PrisesCommunications');
$tables['client']['toilettes'] 										= array('nom' => txt('toilettes'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'Toilettes');
$tables['client']['transports_pneumatiques'] 						= array('nom' => txt('transports_pneumatiques'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'TransportsPneumatiques');
$tables['client']['parametres_cvac'] 								= array('nom' => txt('parametres_cvac'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'ParametresCvac');
$tables['client']['systemes_ventilations'] 							= array('nom' => txt('systemes_ventilations'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'SystemesVentilations');
$tables['client']['groupes_electrogenes']							= array('nom' => txt('groupes_electrogenes'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'GroupesElectrogenes');
$tables['client']['alarmes_incendies']								= array('nom' => txt('alarmes_incendies'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'AlarmesIncendies');
$tables['client']['controles_acces'] 								= array('nom' => txt('controles_acces'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'ControlesAcces');
$tables['client']['systemes_intrusions']							= array('nom' => txt('systemes_intrusions'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'SystemesIntrusions');
$tables['client']['ups'] 											= array('nom' => txt('ups'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'Ups');
$tables['client']['postes_peages'] 									= array('nom' => txt('postes_peages'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'PostesPeages');
$tables['client']['paratonnerres'] 									= array('nom' => txt('paratonnerres'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'Paratonnerres');
$tables['client']['principals_secondaires'] 						= array('nom' => txt('principals_secondaires'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'PrincipalsSecondaires');
$tables['client']['priorites'] 										= array('nom' => txt('priorites'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'Priorites');
$tables['client']['proprietes_raccordements_controles'] 			= array('nom' => txt('proprietes_raccordements_controles'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'ProprietesRaccordementsControles');
$tables['client']['proprietes_calibrations_discretes'] 				= array('nom' => txt('proprietes_calibrations_discretes'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'ProprietesCalibrationsDiscretes');
$tables['client']['proprietes_calibrations_analogiques'] 			= array('nom' => txt('proprietes_calibrations_analogiques'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'ProprietesCalibrationsAnalogiques');
$tables['client']['proprietes_protections_electriques'] 			= array('nom' => txt('proprietes_protections_electriques'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'ProprietesProtectionsElectriques');
$tables['client']['generaux_locaux'] 								= array('nom' => txt('generaux_locaux'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'GenerauxLocaux');
$tables['client']['generaux_equipements'] 							= array('nom' => txt('generaux_equipements'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'GenerauxEquipements');
$tables['client']['generaux_instruments'] 							= array('nom' => txt('generaux_instruments'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'GenerauxInstruments');
$tables['client']['generaux_vehicules'] 							= array('nom' => txt('generaux_vehicules'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'GenerauxVehicules');
*/

if(isMaster())
{
$tables['client']['formulaires'] 									= array('nom' => txt('Formulaires'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer ce formulaire?', false), 'className' => 'Formulaires');
}
$tables['client']['ouvrages'] 										= array('nom' => txt('Ouvrages'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet ouvrage?', false), 'className' => 'Ouvrages');
$tables['client']['projets'] 										= array('nom' => txt('Gestionnaire de projets'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer ce projet?', false), 'className' => 'Projets');
$tables['client']['clients'] 										= array('nom' => txt('Clients'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer ce client?', false), 'className' => 'Client');
$tables['client']['reseaux']                						= array('nom' => txt('Reseaux'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer ce reseau?', false), 'className' => 'Reseau');
$tables['client']['batiments']              						= array('nom' => txt('Bâtiments'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer ce bâtiment?', false), 'className' => 'Batiment');
$tables['client']['routes']              							= array('nom' => txt('Routes'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cette route?', false), 'className' => 'Routes');
$tables['client']['fonctions']              						= array('nom' => txt('Fonctions des bâtiments'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cette fonction?', false), 'className' => 'Fonction');
$tables['client']['gabarits_exports']              					= array('nom' => txt('Matrices d\'exportation'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cette matrice d\'exportation?', false), 'className' => 'GabaritsExports');
$tables['client']['proprietes_dinstallation_et_dutilisation']      	= array('nom' => txt('Propriétés d\'installation et d\'utilisation'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cette matrice d\'exportation?', false), 'className' => 'ProprietesDinstallationEtDutilisation');
$tables['client']['champs_textes_exports']              			= array('nom' => txt('Matrices de champs combinés'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cette matrice?', false), 'className' => 'ChampsTextesExports');
$tables['client']['champs_textes_denormalises']              		= array('nom' => txt('Champs dénormalisés'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cette matrice?', false), 'className' => 'ChampsTextesDenormalises');
$tables['client']['imports']                						= array('nom' => txt('Matrices d\'importation'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cette matrice d\'importation de paramètres?', false), 'className' => 'Imports');
$tables['client']['programmes_entretiens']  						= array('nom' => txt('Bons de travail'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer ce programme d\'entretien?', false), 'className' => 'ProgrammeEntretien');
$tables['client']['usagers'] 										= array('nom' => txt('Usagers'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet usager?', false), 'className' => 'Usager');
$tables['master']['usagers'] 										= array('nom' => txt('Usagers'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet usager?', false), 'className' => 'Usager');
$tables['client']['fiches'] 										= array('nom' => txt('Fiches'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet fiche?', false), 'className' => 'Fiches');
$tables['client']['locaux_classifications']                       	= array('nom' => txt('Locaux (classifications)'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'LocauxClassifications');
$tables['client']['locaux_resistance_feux']               			= array('nom' => txt('Locaux (résistances au feu)'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'LocauxResistanceFeux');
$tables['client']['locaux_substrats']                     			= array('nom' => txt('Locaux (substrats)'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'LocauxSubstrats');
$tables['client']['locaux_finis']                      				= array('nom' => txt('Locaux (finis)'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'LocauxFinis');
$tables['client']['locaux_plinthes']                  				= array('nom' => txt('Plinthes (matériaux)'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'LocauxPlinthes');
$tables['client']['portes_models']                    				= array('nom' => txt('Portes (modèles)'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'PortesModels');
$tables['client']['portes_materiaux']                  				= array('nom' => txt('Portes (matériaux)'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'PortesMateriaux');
$tables['client']['portes_vitrages']                     			= array('nom' => txt('Portes (vitrages)'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'PortesVitrages');
$tables['client']['portes_pellicules_vitrage']         				= array('nom' => txt('Portes (pellicules vitrage)'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'PortesPelliculesVitrage');
$tables['client']['portes_finis']                        			= array('nom' => txt('Portes (finis)'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'PortesFinis');
$tables['client']['porte_vitrage_cadre']             				= array('nom' => txt('Portes (vitrages cadre)'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'PorteVitrageCadre');
$tables['client']['portes_persiennes']                				= array('nom' => txt('Portes (persiennes)'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'PortesPersiennes');
$tables['client']['ouis_nons']                   					= array('nom' => txt('Sélections (oui/non)'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'OuisNons');
$tables['client']['cadences_types']                					= array('nom' => txt('Cadences / capacités (types)'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'CadencesTypes');
$tables['client']['pompes_types']                    				= array('nom' => txt('Pompes (types)'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'PompesTypes');
$tables['client']['valves_types']                        			= array('nom' => txt('Valves (types)'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'ValvesTypes');
$tables['client']['raccords_plomberies_nature']          			= array('nom' => txt('Raccords de plomberie (natures)'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'RaccordsPlomberiesNature');
$tables['client']['plomberies_substance']                    		= array('nom' => txt('Plomberie (substances)'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'PlomberiesSubstance');
$tables['client']['instruments_fonctions']                  		= array('nom' => txt('Instruments (fonctions)'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'InstrumentsFonctions');
$tables['client']['signal_nature']                       			= array('nom' => txt('Signaux (natures)'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'SignalNature');
$tables['client']['taux_utilisations']                 				= array('nom' => txt('Taux d\'utilisation'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'TauxUtilisations');
$tables['client']['correlations_champs_externes']      				= array('nom' => txt('Corrélation des champs externes'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'CorrelationsChampsExternes');
$tables['client']['infrastructure_materiaux']             			= array('nom' => txt('Infrastructure (matériaux)'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'InfrastructureMateriaux');
$tables['client']['compacition_types']                    			= array('nom' => txt('Compaction (types)'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'CompacitionTypes');
$tables['client']['aqueduc_composantes']                   			= array('nom' => txt('Aqueduc (composantes)'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'AqueducComposantes');
$tables['client']['egout_pluvial_composantes']                 		= array('nom' => txt('Égout pluvial (composantes)'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'EgoutPluvialComposantes');
$tables['client']['egout_sanitaire_composantes']                	= array('nom' => txt('Égout sanitaire (composantes)'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'EgoutSanitaireComposantes');
$tables['client']['reseau_electriques_composantes']        			= array('nom' => txt('Réseau électriques (composantes)'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'ReseauElectriquesComposantes');
$tables['client']['reseau_gas_composantes']                    		= array('nom' => txt('Réseau gas (composantes)'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'ReseauGasComposantes');
$tables['client']['reseaux_telecoms_composantes']                  	= array('nom' => txt('Réseaux télécoms (composantes)'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'ReseauxTelecomsComposantes');
$tables['client']['pavage_materiaux']                       		= array('nom' => txt('Pavage (matériaux)'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'PavageMateriaux');
$tables['client']['acquisitions_groupes']                     		= array('nom' => txt('Gestion des groupes d\'équipements'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'AcquisitionsGroupes');
$tables['client']['financements_groupes']                     		= array('nom' => txt('Gestion des groupes d\'équipements'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'FinancementsGroupes');
$tables['client']['acquisitions_groupes_equipements']      			= array('nom' => txt('Équipements'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'FinancementsGroupesEquipements');
$tables['client']['financements_groupes_equipements']      			= array('nom' => txt('Équipements'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'FinancementsGroupesEquipements');
$tables['client']['financements_statuts_internes_types']      		= array('nom' => txt('Statuts internes'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'FinancementsStatutsInternesTypes');
$tables['client']['financements_statuts_externes_types']      		= array('nom' => txt('Statuts externes'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'FinancementsStatutsExternesTypes');
$tables['client']['prises_communications_coordinations']      		= array('nom' => txt('Prises de communication coordonnées'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'PrisesCommunicationsCoordinations');
$tables['client']['prises_electriques_coordinations']      			= array('nom' => txt('Prises électriques coordonnées'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'PrisesElectriquesCoordinations');
$tables['client']['gestions_portes_groupes_quincailleries']      	= array('nom' => txt('Groupes de quincaillerie'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'GestionsPortesGroupesQuincailleries');

/*

$tables['client']['panneaux']               						= array('nom' => txt('Panneaux'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer ce panneau?', false), 'className' => 'Panneau');
$tables['client']['automates']              						= array('nom' => txt('Automates'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet automate?', false), 'className' => 'Automate');
$tables['client']['rendements_types']                            	= array('nom' => txt('Rendements (éléments)'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'RendementsTypes');
$tables['client']['matrices_importations_listes']              		= array('nom' => txt('Matrices d\'importation de listes'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cette matrice d\'importation de listes?', false), 'className' => 'MatricesImportationsListes');
$tables['client']['champs_ponctuels'] 								= array('nom' => txt('Champs de saisie ponctuels'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer ce champs de saisie ponctuel?', false), 'className' => 'ChampsPonctuel');
$tables['client']['locaux_classifications']                       	= array('nom' => txt('Locaux (classifications)'), 'delConf' => txt('Êtes-vous certain de vouloir supprimer cet élément?', false), 'className' => 'LocauxClassifications');

************************************************************************************************************************* Ajout des tables des formulaires fiches de tous types *******************************************************************************************************************************************************************************


Mettre le nom de toutes les tables qui contiennent les informations dans les formulaires des fiches
ET
Les noms de champs ne sont pas requis pour les tables qui stock les données dans les formulaires de fiches
*/


$validColumns['doc']['proprietes_electrique_aerotherme_electrique'] = array();
$validColumns['doc']['proprietes_electrique_serpentin_electrique'] = array();
$validColumns['doc']['infrastructure_routiere'] = array();
$validColumns['doc']['reseaux_daqueduc'] = array();
$validColumns['doc']['reseaux_degout_pluvial'] = array();
$validColumns['doc']['reseaux_degout_sanitaire'] = array();
$validColumns['doc']['reseaux_electriques_sousterrains'] = array();
$validColumns['doc']['reseaux_de_conduites_de_gas_souterrains'] = array();
$validColumns['doc']['reseaux_de_telecoms_souterrains'] = array();
$validColumns['doc']['pavage'] = array();
$validColumns['doc']['proprietes_architecturales_des_portes_et_acces'] = array();
$validColumns['doc']['groupes_quincailleries_controle_acces'] = array();
$validColumns['doc']['gestions_portes_groupes_quincailleries'] = array();
$validColumns['doc']['proprietes_mecaniques_aerotherme'] = array();
$validColumns['doc']['proprietes_mecaniques_collecteur'] = array();
$validColumns['doc']['proprietes_mecaniques_echangeur_a_plaque'] = array();
$validColumns['doc']['proprietes_mecaniques_pompe_hydronique'] = array();
$validColumns['doc']['proprietes_mecaniques_pompe_hydronique_cvac'] = array();
$validColumns['doc']['proprietes_mecaniques_refroidisseur'] = array();
$validColumns['doc']['proprietes_mecaniques_refroidisseur_hiver'] = array();
$validColumns['doc']['proprietes_mecaniques_unite_de_pressurisation_au_glycol'] = array();
$validColumns['doc']['proprietes_mecaniques_chauffeeau'] = array();
$validColumns['doc']['proprietes_mecaniques_pompe_de_recirculation'] = array();
$validColumns['doc']['proprietes_mecaniques_pompes_submersibles'] = array();
$validColumns['doc']['proprietes_mecaniques_distributeur_de_vapeur'] = array();
$validColumns['doc']['proprietes_mecaniques_generateur_de_vapeur'] = array();
$validColumns['doc']['proprietes_mecaniques_hotte'] = array();
$validColumns['doc']['proprietes_mecaniques_lampes_ultraviolet'] = array();
$validColumns['doc']['proprietes_mecaniques_melangeur_dair'] = array();
$validColumns['doc']['proprietes_mecaniques_persienne'] = array();
$validColumns['doc']['proprietes_mecaniques_plenum'] = array();
$validColumns['doc']['proprietes_mecaniques_rideau_dair'] = array();
$validColumns['doc']['proprietes_mecaniques_roue_entalphique_estivale'] = array();
$validColumns['doc']['proprietes_mecaniques_roue_entalphique_hivernale'] = array();
$validColumns['doc']['proprietes_mecaniques_serpentin_hydronique'] = array();
$validColumns['doc']['proprietes_mecaniques_silencieux'] = array();
$validColumns['doc']['proprietes_mecaniques_station_de_mesure_de_debit'] = array();
$validColumns['doc']['proprietes_mecaniques_unite_de_ventilation'] = array();
$validColumns['doc']['proprietes_mecaniques_unite_de_ventilation_sur_mesure'] = array();
$validColumns['doc']['proprietes_mecaniques_vcf'] = array();
$validColumns['doc']['proprietes_mecaniques_ventilateur'] = array();
$validColumns['doc']['proprietes_mecaniques_ventiloconvecteur'] = array();
$validColumns['doc']['proprietes_mecaniques_volet_motorise'] = array();
$validColumns['doc']['proprietes_mecaniques_boite_de_fin_de_course_double'] = array();
$validColumns['doc']['proprietes_mecaniques_boite_de_fin_de_course_simple'] = array();
$validColumns['doc']['proprietes_mecaniques_filtre'] = array();
$validColumns['doc']['proprietes_mecaniques_grille_de_securite'] = array();
$validColumns['doc']['proprietes_mecaniques_grille_et_diffuseur'] = array();
$validColumns['doc']['proprietes_mecaniques_de_lequipement_de_procede'] = array();
$validColumns['doc']['proprietes_mecaniques_de_la_pompe'] = array();
$validColumns['doc']['proprietes_mecaniques_du_robinet_valve_de_plomberie'] = array();
$validColumns['doc']['propriete_des_raccords_de_plomberie'] = array();
$validColumns['doc']['propriete_des_raccords_de_plomberie_sans_coord'] = array();
$validColumns['doc']['propriete_de_linstrumentation_de_mesure_detection_et_analyse'] = array();
$validColumns['doc']['propriete_des_raccords_de_controle'] = array();
$validColumns['doc']['proprietes_mecaniques_chaudierea_eauchaude'] = array();
$validColumns['doc']['proprietes_mecaniques_chaudierea_vapeur'] = array();
$validColumns['doc']['proprietes_mecaniques_tour_deau'] = array();
$validColumns['doc']['proprietes_mecaniques_recuperateur_de_gaz_de_combustion'] = array();
$validColumns['doc']['proprietes_mecaniques_reservoir_de_condense'] = array();
$validColumns['doc']['proprietes_mecaniques_degazeur'] = array();
$validColumns['doc']['proprietes_mecaniques_reservoir_de_vidange'] = array();
$validColumns['doc']['proprietes_mecaniques_reservoir_dexpansion'] = array();
$validColumns['doc']['proprietes_mecaniques_echangeur_eaueau'] = array();
$validColumns['doc']['diffuseurs'] = array();
$validColumns['doc']['uni_finitions_murs'] = array();
$validColumns['doc']['uni_finitions_planchers'] = array();
$validColumns['doc']['uni_finitions_plinthes'] = array();
$validColumns['doc']['uni_finitions_plafonds'] = array();
$validColumns['doc']['uni_accessoires_protections_incendies'] = array();
$validColumns['doc']['uni_accessoires_integres'] = array();
$validColumns['doc']['uni_ameublements_decorations'] = array();
$validColumns['doc']['uni_appareils_plomberies'] = array();
$validColumns['doc']['uni_autres_systemes_plomberies'] = array();
$validColumns['doc']['uni_communications_informatiques'] = array();
$validColumns['doc']['uni_distributions_cvcas'] = array();
$validColumns['doc']['uni_distributions_secondaires'] = array();
$validColumns['doc']['uni_eclairages'] = array();
$validColumns['doc']['uni_equipements'] = array();
$validColumns['doc']['uni_gicleurs'] = array();
$validColumns['doc']['uni_securites'] = array();
$validColumns['doc']['uni_cloisons'] = array();
$validColumns['doc']['uni_portes'] = array();
$validColumns['doc']['uni_exigences_fonctionnelles'] = array();
$validColumns['doc']['acquisitions_groupes_equipements'] = array();
$validColumns['doc']['financements_groupes_equipements'] = array();
$validColumns['doc']['raccords_electriques_coordinations'] = array();
$validColumns['doc']['raccords_electriques_coordinations_auxiliaires'] = array();
$validColumns['doc']['generaux_groupes_controles'] = array();
$validColumns['doc']['generaux_portes'] = array();
$validColumns['doc']['generaux_routes'] = array();
$validColumns['doc']['generaux_infrastructures'] = array();
$validColumns['doc']['generaux_superstructures'] = array();
$validColumns['doc']['generaux_amenagements_interieurs'] = array();
$validColumns['doc']['generaux_services'] = array();
$validColumns['doc']['generaux_equipements_fixes'] = array();
$validColumns['doc']['generaux_amenagements_emplacements'] = array();
$validColumns['doc']['conversions_unites'] = array();


$validColumns['doc']['douches'] = array(
										//	'fiche_id' 							=> 'int',
										//	'douche_type_id' 					=> 'int',
										//	'quantite_id' 						=> 'int',
										//	'commentaires' 						=> 'varchar',
							);

$validColumns['doc']['bains'] = array(
										//	'fiche_id' 							=> 'int',
										//	'bain_type_id' 						=> 'int',
										//	'quantite_id' 						=> 'int',
										//	'commentaires' 						=> 'varchar',
							);

$validColumns['doc']['murs'] = array(
										//	'fiche_id' 							=> 'int',
										//	'mur_type_id' 						=> 'int',
										//	'quantite_id' 						=> 'int',
										//	'commentaires' 						=> 'varchar',
							);

$validColumns['doc']['planchers'] = array(
										//	'fiche_id' 							=> 'int',
										//	'plancher_type_id' 				=> 'int',
										//	'quantite_id' 						=> 'int',
										//	'commentaires' 						=> 'varchar',
							);

$validColumns['doc']['plafonds'] = array(
										//	'fiche_id' 							=> 'int',
										//	'plafond_type_id' 					=> 'int',
										//	'quantite_id' 						=> 'int',
										//	'commentaires' 						=> 'varchar',
							);

$validColumns['doc']['portes'] = array(
										//	'fiche_id' 							=> 'int',
										//	'porte_type_id' 					=> 'int',
										//	'quantite_id' 						=> 'int',
										//	'commentaires' 						=> 'varchar',
							);

$validColumns['doc']['gestions_portes_quincailleries'] = array(
										//	'fiche_id' 							=> 'int',
										//	'porte_type_id' 					=> 'int',
										//	'quantite_id' 						=> 'int',
										//	'commentaires' 						=> 'varchar',
							);

$validColumns['doc']['fenetres'] = array(
										//	'fiche_id' 							=> 'int',
										//	'fenetre_type_id' 					=> 'int',
										//	'quantite_id' 						=> 'int',
										//	'commentaires' 						=> 'varchar',
							);

$validColumns['doc']['protecteurs_murals'] = array(
										//	'fiche_id' 							=> 'int',
										//	'protecteur_mural_type_id' 		=> 'int',
										//	'quantite_id' 						=> 'int',
										//	'commentaires' 						=> 'varchar',
							);

$validColumns['doc']['habillages_fenetres'] = array(
										//	'fiche_id' 							=> 'int',
										//	'habillage_fenetre_type_id' 		=> 'int',
										//	'quantite_id' 						=> 'int',
										//	'commentaires' 						=> 'varchar',
							);

$validColumns['doc']['cameras_securites'] = array(
										//	'fiche_id' 							=> 'int',
										//	'camera_securite_type_id' 			=> 'int',
										//	'installation_type_id' 				=> 'int',
										//	'quantite_id' 						=> 'int',
										//	'commentaires' 						=> 'varchar',
							);

$validColumns['doc']['evacuations_speciales'] = array(
										//	'fiche_id' 							=> 'int',
										//	'evacuation_speciale_type_id' 		=> 'int',
										//	'quantite_id' 						=> 'int',
										//	'commentaires' 						=> 'varchar',
							);

$validColumns['doc']['eaux_purifies'] = array(
										//	'fiche_id' 							=> 'int',
										//	'eaux_purifie_type_id' 				=> 'int',
										//	'quantite_id' 						=> 'int',
										//	'commentaires' 						=> 'varchar',
							);

$validColumns['doc']['eclairages_interieurs'] = array(
										//	'fiche_id' 							=> 'int',
										//	'eclairage_interieur_type_id' 		=> 'int',
										//	'eclairage_ratio_urgence_id' 		=> 'int',
										//	'vital' 							=> 'int',
										//	'protection_personnes' 				=> 'int',
										//	'installation_type_id' 				=> 'int',
										//	'eclairage_controle_type_id' 		=> 'int',
										//	'eclairage_lumens_type_id' 			=> 'int',
										//	'principal_secondaire_id' 			=> 'int',
										//	'quantite_id'                       => 'int',
										//	'commentaires' 						=> 'varchar',
							);

$validColumns['doc']['eclairages_exterieurs'] = array(
										//	'fiche_id' 							=> 'int',
										//	'eclairage_exterieur_type_id' 		=> 'int',
										//	'eclairage_ratio_urgence_id' 		=> 'int',
										//	'vital' 							=> 'int',
										//	'protection_personnes' 				=> 'int',
										//	'installation_type_id' 				=> 'int',
										//	'eclairage_controle_type_id' 		=> 'int',
										//	'eclairage_lumens_type_id' 			=> 'int',
										//	'principal_secondaire_id' 			=> 'int',
										//	'quantite_id'                       => 'int',
										//	'commentaires' 						=> 'varchar',
							);

$validColumns['doc']['eclairages_stationnements'] = array(
										//	'fiche_id' 							=> 'int',
										//	'eclairage_stationnement_type_id' 	=> 'int',
										//	'eclairage_ratio_urgence_id' 		=> 'int',
										//	'vital' 							=> 'int',
										//	'protection_personnes' 				=> 'int',
										//	'installation_type_id' 				=> 'int',
										//	'eclairage_controle_type_id' 		=> 'int',
										//	'eclairage_lumens_type_id' 			=> 'int',
										//	'principal_secondaire_id' 			=> 'int',
										//	'quantite_id'                       => 'int',
										//	'commentaires' 						=> 'varchar',
							);

$validColumns['doc']['eclairages_routiers'] = array(
										//	'fiche_id' 							=> 'int',
										//	'eclairage_routier_type_id' 		=> 'int',
										//	'eclairage_ratio_urgence_id' 		=> 'int',
										//	'vital' 							=> 'int',
										//	'protection_personnes' 				=> 'int',
										//	'installation_type_id' 				=> 'int',
										//	'eclairage_controle_type_id' 		=> 'int',
										//	'eclairage_lumens_type_id' 			=> 'int',
										//	'principal_secondaire_id' 			=> 'int',
										//	'quantite_id'                       => 'int',
										//	'commentaires' 						=> 'varchar',
							);

$validColumns['doc']['eclairages_parcs'] = array(
										//	'fiche_id' 							=> 'int',
										//	'eclairage_parc_type_id' 			=> 'int',
										//	'eclairage_ratio_urgence_id' 		=> 'int',
										//	'vital' 							=> 'int',
										//	'protection_personnes' 				=> 'int',
										//	'installation_type_id' 				=> 'int',
										//	'eclairage_controle_type_id' 		=> 'int',
										//	'eclairage_lumens_type_id' 			=> 'int',
										//	'principal_secondaire_id' 			=> 'int',
										//	'quantite_id'                       => 'int',
										//	'commentaires' 						=> 'varchar',
							);

$validColumns['doc']['appels_gardes'] = array(
										//	'fiche_id' 							=> 'int',
										//	'appel_garde_type_id' 				=> 'int',
										//	'installation_type_id' 				=> 'int',
										//	'quantite_id' 						=> 'int',
										//	'commentaires' 						=> 'varchar',
							);

$validColumns['doc']['appels_urgences'] = array(
										//	'fiche_id' 							=> 'int',
										//	'appel_urgence_type_id' 			=> 'int',
										//	'installation_type_id' 				=> 'int',
										//	'quantite_id' 						=> 'int',
										//	'commentaires' 						=> 'varchar',
							);

$validColumns['doc']['equipements_medicaux_specialises'] = array(
										//	'fiche_id' 									=> 'int',
										//	'equipement_medicaux_specialise_type_id'	=> 'int',
										//	'quantite_id' 								=> 'int',
										//	'fourni_installe_id' 						=> 'int',
										//	'existant_nouveau_id' 						=> 'int',
										//	'surcharge_plancher' 						=> 'int',
										//	'surcharge_plafond' 						=> 'int',
										//	'niveau_vibration_tolere_id' 				=> 'int',
										//	'canivaux' 									=> 'int',
										//	'operationnel' 								=> 'int',
										//	'commentaires' 								=> 'varchar',

							);

$validColumns['doc']['equipements_multimedias'] = array(
										//	'fiche_id' 							=> 'int',
										//	'equipement_multimedia_type_id' 	=> 'int',
										//	'installation_type_id' 				=> 'int',
										//	'quantite_id' 						=> 'int',
										//	'commentaires' 						=> 'varchar',
							);

$validColumns['doc']['eviers'] = array(
										//	'fiche_id' 							=> 'int',
										//	'evier_type_id' 					=> 'int',
										//	'quantite_id' 						=> 'int',
										//	'commentaires' 						=> 'varchar',
							);

$validColumns['doc']['cuves'] = array(
										//	'fiche_id' 							=> 'int',
										//	'type_id' 							=> 'int',
										//	'quantite_id' 						=> 'int',
										//	'commentaires' 						=> 'varchar',
							);

$validColumns['doc']['fontaines_refrigerees'] = array(
										//	'fiche_id' 							=> 'int',
										//	'type_id' 							=> 'int',
										//	'quantite_id' 						=> 'int',
										//	'commentaires' 						=> 'varchar',
							);

$validColumns['doc']['plomberies_securites_urgences'] = array(
										//	'fiche_id' 							=> 'int',
										//	'type_id' 							=> 'int',
										//	'quantite_id' 						=> 'int',
										//	'commentaires' 						=> 'varchar',
							);

$validColumns['doc']['urinoirs'] = array(
										//	'fiche_id' 							=> 'int',
										//	'type_id' 							=> 'int',
										//	'quantite_id' 						=> 'int',
										//	'commentaires' 						=> 'varchar',
							);

$validColumns['doc']['raccords_plomberies'] = array(
										//	'fiche_id' 							=> 'int',
										//	'type_id' 							=> 'int',
										//	'installation_type_id' 				=> 'int',
										//	'quantite_id' 						=> 'int',
										//	'commentaires' 						=> 'varchar',
							);

$validColumns['doc']['raccords_electriques'] = array(
										//	'fiche_id' 							=> 'int',
										//	'type_id' 							=> 'int',
										//	'tension_type_id' 					=> 'int',
										//	'puissance' 						=> 'float',
										//	'installation_type_id' 				=> 'int',
										//	'quantite_id' 						=> 'int',
										//	'commentaires' 						=> 'varchar',
							);

$validColumns['doc']['gaz_medicaux'] = array(
										//	'fiche_id' 							=> 'int',
										//	'gaz_medicau_type_id' 				=> 'int',
										//	'installation_type_id' 				=> 'int',
										//	'quantite_id' 						=> 'int',
										//	'commentaires' 						=> 'varchar',
							);

$validColumns['doc']['horloges'] = array(
										//	'fiche_id' 							=> 'int',
										//	'horloge_type_id' 					=> 'int',
										//	'installation_type_id' 				=> 'int',
										//	'quantite_id' 						=> 'int',
										//	'commentaires' 						=> 'varchar',
							);

$validColumns['doc']['hottes'] = array(
										//	'fiche_id' 							=> 'int',
										//	'hotte_type_id' 					=> 'int',
										//	'largeur' 							=> 'float',
										//	'raccord_gaz' 						=> 'int',
										//	'raccord_vide' 						=> 'int',
										//	'quantite_id' 						=> 'int',
										//	'commentaires' 						=> 'varchar',
							);

$validColumns['doc']['lavabos'] = array(
										//	'fiche_id' 							=> 'int',
										//	'lavabo_type_id' 					=> 'int',
										//	'quantite_id' 						=> 'int',
										//	'commentaires' 						=> 'varchar',
							);

$validColumns['doc']['mobiliers_integres'] = array(
										//	'fiche_id' 							=> 'int',
										//	'no' 								=> 'varchar',
										//	'mobilier_integre_type_id' 			=> 'int',
										//	'fini_type_id' 						=> 'int',
										//	'quantite_id' 						=> 'int',
										//	'commentaires' 						=> 'varchar',
							);

$validColumns['doc']['mobiliers_laboratoires'] = array(
										//	'fiche_id' 							=> 'int',
										//	'no' 								=> 'varchar',
										//	'mobilier_laboratoire_type_id' 		=> 'int',
										//	'fourni_installe_id' 				=> 'int',
										//	'existant_nouveau_id' 				=> 'int',
										//	'quantite_id' 						=> 'int',
										//	'commentaires' 						=> 'varchar',
							);

$validColumns['doc']['mobiliers_mobiles'] = array(
										//	'fiche_id' 							=> 'int',
										//	'no' 								=> 'varchar',
										//	'mobilier_mobile_type_id' 			=> 'int',
										//	'fourni_installe_id' 				=> 'int',
										//	'existant_nouveau_id' 				=> 'int',
										//	'quantite_id' 						=> 'int',
										//	'commentaires' 						=> 'varchar',
							);

$validColumns['doc']['outillages'] = array(
										//	'fiche_id' 							=> 'int',
										//	'no' 								=> 'varchar',
										//	'outillage_type_id' 				=> 'int',
										//	'quantite_id' 						=> 'int',
										//	'fourni_installe_id' 				=> 'int',
										//	'existant_nouveau_id' 				=> 'int',
										//	'commentaires' 						=> 'varchar',
							);

$validColumns['doc']['plomberies_divers'] = array(
										//	'fiche_id' 							=> 'int',
										//	'plomberie_diver_type_id' 			=> 'int',
										//	'quantite_id' 						=> 'int',
										//	'commentaires' 						=> 'varchar',
							);

$validColumns['doc']['prises_electriques_interieurs'] = array(
										//	'fiche_id' 							=> 'int',
										//	'prise_electrique_type_id' 			=> 'int',
										//	'alimentation_electrique_type_id' 	=> 'int',
										//	'installation_type_id' 				=> 'int',
										//	'prise_electrique_grade_id' 		=> 'int',
										//	'quantite_id' 						=> 'int',
										//	'commentaires' 						=> 'varchar',
							);

$validColumns['doc']['prises_communications_coordinations'] = array(
										//	'dedie' 							=> 'int',
										//	'fiche_id' 							=> 'int',
										//	'prise_electrique_type_id' 			=> 'int',
										//	'alimentation_electrique_type_id' 	=> 'int',
										//	'installation_type_id' 				=> 'int',
										//	'prise_electrique_grade_id' 		=> 'int',
										//	'quantite_id' 						=> 'int',
										//	'commentaires' 						=> 'varchar',
							);

$validColumns['doc']['prises_electriques_coordinations'] = array(
										//	'dedie' 							=> 'int',
										//	'fiche_id' 							=> 'int',
										//	'prise_electrique_type_id' 			=> 'int',
										//	'alimentation_electrique_type_id' 	=> 'int',
										//	'installation_type_id' 				=> 'int',
										//	'prise_electrique_grade_id' 		=> 'int',
										//	'quantite_id' 						=> 'int',
										//	'commentaires' 						=> 'varchar',
							);

$validColumns['doc']['appareils_chauffages'] = array(
										//	'fiche_id' 							=> 'int',
										//	'appareil_chauffage_type_id' 		=> 'int',
										//	'alimentation_electrique_type_id' 	=> 'int',
										//	'installation_type_id' 				=> 'int',
										//	'prise_electrique_grade_id' 		=> 'int',
										//	'quantite_id' 						=> 'int',
										//	'commentaires' 						=> 'varchar',
							);

$validColumns['doc']['prises_electriques_exterieurs'] = array(
										//	'fiche_id' 							=> 'int',
										//	'prise_electrique_type_id' 			=> 'int',
										//	'alimentation_electrique_type_id' 	=> 'int',
										//	'installation_type_id' 				=> 'int',
										//	'prise_electrique_grade_id' 		=> 'int',
										//	'quantite_id' 						=> 'int',
										//	'commentaires' 						=> 'varchar',
							);

$validColumns['doc']['prises_electriques_stationnements'] = array(
										//	'fiche_id' 							=> 'int',
										//	'prise_electrique_type_id' 			=> 'int',
										//	'alimentation_electrique_type_id' 	=> 'int',
										//	'installation_type_id' 				=> 'int',
										//	'prise_electrique_grade_id' 		=> 'int',
										//	'quantite_id' 						=> 'int',
										//	'commentaires' 						=> 'varchar',
							);

$validColumns['doc']['prises_communications'] = array(
										//	'fiche_id' 								=> 'int',
										//	'prise_communication_sortie_type_id' 	=> 'int',
										//	'installation_type_id' 					=> 'int',
										//	'quantite_id' 							=> 'int',
										//	'commentaires' 							=> 'varchar',
							);

$validColumns['doc']['toilettes'] = array(
										//	'fiche_id' 							=> 'int',
										//	'toilette_type_id' 					=> 'int',
										//	'quantite_id' 						=> 'int',
										//	'commentaires' 						=> 'varchar',
							);

$validColumns['doc']['transports_pneumatiques'] = array(
										//	'fiche_id' 							=> 'int',
										//	'envoi_reception_type_id' 			=> 'int',
										//	'presence' 							=> 'int',
										//	'avertisseur_sonore' 				=> 'int',
										//	'avertisseur_lumineux' 				=> 'int',
										//	'priorite_id' 						=> 'int',
										//	'commentaires' 						=> 'varchar',
							);

$validColumns['doc']['parametres_cvac'] = array(
										//	'fiche_id' 							=> 'int',
										//	'csa_type_id' 						=> 'int',
										//	'evacuation'                        => 'int',
										//	'pressurisation_type_id' 			=> 'int',
										//	'indicateur_pression_local' 		=> 'int',
										//	'pourcentage_evacuation_id' 		=> 'int',
										//	'climatisation_urgence' 			=> 'int',
										//	'besoin_eau_glacee' 				=> 'int',
										//	'filtration_hepa' 					=> 'int',
										//	'taux_occupation_id' 				=> 'int',
										//	'changement_air_frais_min_id' 		=> 'int',
										//	'changement_air_total_min_id' 		=> 'int',
										//	'changement_air_evacue_min_id' 		=> 'int',
										//	'temperature_min_id' 				=> 'int',
										//	'temperature_max_id' 				=> 'int',
										//	'humidite_relative_min_id' 			=> 'int',
										//	'humidite_relative_max_id' 			=> 'int',
										//	'niveau_bruit_id' 					=> 'int',
										//	'commentaires' 						=> 'varchar',
							);

$validColumns['doc']['parametres_electricites'] = array(
										//	'fiche_id' 							=> 'int',
										//	'csa_type_id' 						=> 'int',
										//	'commentaires' 						=> 'varchar',
							);


$validColumns['doc']['approvisionnements'] = array(
										//	'nom_table'         => 'varchar',
										//	'liste_item_id'     => 'int',
										//	'quantite'          => 'int',
										//	'commentaire'       => 'varchar',
							);

$validColumns['doc']['proprietes_dinstallation_et_dutilisation'] = array(
										//	'fiche_id'              			=> 'int',
										//	'date_installation' 				=> 'date',
										//	'date_mise_en_fonction' 			=> 'date',
										//	'taux_utilisation_id' 				=> 'int',
										//	'commentaire'           			=> 'varchar',
							);

$validColumns['doc']['alarmes_incendies'] = array(
										//	'fiche_id' 								=> 'int',
										//	'equipement_alarme_incendie_type_id'	=> 'int',
										//	'installation_type_id' 					=> 'int',
										//	'quantite_id' 							=> 'int',
										//	'commentaires' 							=> 'varchar',
							);

$validColumns['doc']['controles_acces'] = array(
										//	'fiche_id' 								=> 'int',
										//	'equipement_controle_acces_type_id'		=> 'int',
										//	'installation_type_id' 					=> 'int',
										//	'quantite_id' 							=> 'int',
										//	'commentaires' 							=> 'varchar',
							);

$validColumns['doc']['systemes_intrusions'] = array(
										//	'fiche_id' 								=> 'int',
										//	'equipement_systeme_intrusion_type_id'	=> 'int',
										//	'installation_type_id' 					=> 'int',
										//	'quantite_id' 							=> 'int',
										//	'commentaires' 							=> 'varchar',
							);

$validColumns['doc']['ups'] = array(
										//	'fiche_id' 							=> 'int',
										//	'equipement_ups_type_id'			=> 'int',
										//	'installation_type_id' 				=> 'int',
										//	'quantite_id' 						=> 'int',
										//	'commentaires' 						=> 'varchar',
							);

$validColumns['doc']['postes_peages'] = array(
										//	'fiche_id' 							=> 'int',
										//	'equipement_poste_peage_type_id'	=> 'int',
										//	'installation_type_id' 				=> 'int',
										//	'quantite_id' 						=> 'int',
										//	'commentaires' 						=> 'varchar',
							);

$validColumns['doc']['eclairages_controles'] = array(
										//	'fiche_id' 							=> 'int',
										//	'eclairage_controle_type_id'		=> 'int',
										//	'installation_type_id' 				=> 'int',
										//	'quantite_id' 						=> 'int',
										//	'commentaires' 						=> 'varchar',
							);

$validColumns['doc']['raccords_directs'] = array(
										//	'fiche_id' 							=> 'int',
										//	'raccord_direct_type_id'			=> 'int',
										//	'installation_type_id' 				=> 'int',
										//	'quantite_id' 						=> 'int',
										//	'commentaires' 						=> 'varchar',
							);

$validColumns['doc']['paratonnerres'] = array(
										//	'fiche_id' 							=> 'int',
										//	'equipement_paratonnerre_type_id'	=> 'int',
										//	'installation_type_id' 				=> 'int',
										//	'quantite_id' 						=> 'int',
										//	'commentaires' 						=> 'varchar',
							);

$validColumns['doc']['proprietes_raccordements_controles'] = array(
										//	'fiche_id' 					=> 'int',
										//	'automate_id'				=> 'int',
										//	'type_carte_id' 			=> 'int',
										//	'adresse_geio' 				=> 'varchar',
										//	'borne_1' 					=> 'varchar',
										//	'borne_2' 					=> 'varchar',
										//	'numero_fil_1' 				=> 'varchar',
										//	'numero_fil_2' 				=> 'varchar',
										//	'rack' 						=> 'varchar',
										//	'slot' 						=> 'varchar',
							);

$validColumns['doc']['proprietes_raccordements_controles_auxiliaires'] = array(
										//	'fiche_id' 					=> 'int',
										//	'automate_id'				=> 'int',
										//	'type_carte_id' 			=> 'int',
										//	'adresse_geio' 				=> 'varchar',
										//	'borne_1' 					=> 'varchar',
										//	'borne_2' 					=> 'varchar',
										//	'numero_fil_1' 				=> 'varchar',
										//	'numero_fil_2' 				=> 'varchar',
										//	'rack' 						=> 'varchar',
										//	'slot' 						=> 'varchar',
							);

$validColumns['doc']['proprietes_calibrations_discretes'] = array(
										//	'fiche_id' 					=> 'int',
										//	'unite_consigne'			=> 'int',
										//	'consigne_croissante_1' 	=> 'float',
										//	'consigne_decroissante_1' 	=> 'float',
										//	'contact_1' 				=> 'varchar',
										//	'consigne_croissante_2' 	=> 'float',
										//	'consigne_decroissante_2' 	=> 'float',
										//	'contact_2' 				=> 'varchar',
										//	'consigne_croissante_3' 	=> 'float',
										//	'consigne_decroissante_3' 	=> 'float',
										//	'contact_3' 				=> 'varchar',
										//	'consigne_croissante_4' 	=> 'float',
										//	'consigne_decroissante_4' 	=> 'float',
										//	'contact_4' 				=> 'varchar',
										//	'consigne_croissante_5' 	=> 'float',
										//	'consigne_decroissante_5' 	=> 'float',
										//	'contact_5' 				=> 'varchar',
										//	'consigne_croissante_6' 	=> 'float',
										//	'consigne_decroissante_6' 	=> 'float',
										//	'contact_6' 				=> 'varchar',
							);

$validColumns['doc']['proprietes_calibrations_analogiques'] = array(
										//	'fiche_id' 					=> 'int',
										//	'unite_entree'				=> 'int',
										//	'entree_0' 					=> 'float',
										//	'entree_25' 				=> 'float',
										//	'entree_50' 				=> 'float',
										//	'entree_75' 				=> 'float',
										//	'entree_100' 				=> 'float',
										//	'unite_sortie'				=> 'int',
										//	'sortie_0' 					=> 'float',
										//	'sortie_25' 				=> 'float',
										//	'sortie_50' 				=> 'float',
										//	'sortie_75' 				=> 'float',
										//	'sortie_100' 				=> 'float',
										//	'type_correlation_a'		=> 'int',
										//	'unite_correlation_a'		=> 'int',
										//	'correlation_a0' 			=> 'float',
										//	'correlation_a25' 			=> 'float',
										//	'correlation_a50' 			=> 'float',
										//	'correlation_a75' 			=> 'float',
										//	'correlation_a100' 			=> 'float',
										//	'type_correlation_b'		=> 'int',
										//	'unite_correlation_b'		=> 'int',
										//	'correlation_b0' 			=> 'float',
										//	'correlation_b25' 			=> 'float',
										//	'correlation_b50' 			=> 'float',
										//	'correlation_b75' 			=> 'float',
										//	'correlation_b100' 			=> 'float',
							);

$validColumns['doc']['panneaux_electriques_details'] = array(
										//	'fiche_id' 						=> 'int',
										//	'calibre_protection_type_id'	=> 'int',
										//	'charge'                    	=> 'varchar',
										//	'puissance'                 	=> 'int',
							);

$validColumns['doc']['equipements_transformations_electriques'] = array(
										//	'fiche_id' 											=> 'int',
										//	'tension_primaire_id'								=> 'int',
										//	'tension_secondaire_id'								=> 'int',
										//	'transformateur_type_id'							=> 'int',
										//	'temperature' 										=> 'int',
										//	'impedance' 										=> 'int',
										//	'facteur_k'											=> 'int',
										//	'materiau_type_id'									=> 'int',
										//	'boitier_type_id'									=> 'int',
										//	'kva_type_id'										=> 'int',
										//	'alimentation_cable_calibre_id'						=> 'int',
										//	'alimentation_cable_longueur'						=> 'int',
										//	'alimentation_circuit_parallele'					=> 'int',
										//	'alimentation_disjoncteur_installation_id'			=> 'int',
										//	'alimentation_disjoncteur_calibre_id'				=> 'int',
										//	'alimentation_disjoncteur_ajustement_amperage_id'	=> 'int',
										//	'alimentation_disjoncteur_capacite_rupture_id'		=> 'int',
										//	'commentaires'										=> 'varchar',
							);

$validColumns['doc']['equipements_distributions_electriques'] = array(
										//	'fiche_id' 											=> 'int',
										//	'equipement_electrique_type_id'						=> 'int',
										//	'tension_type_id'									=> 'int',
										//	'nombre_phase_type_id'								=> 'int',
										//	'nombre_circuits' 									=> 'int',
										//	'nombre_espaces' 									=> 'int',
										//	'nombre_cellules'									=> 'int',
										//	'nombre_tiroirs'									=> 'int',
										//	'capacite_rupture_types_id'							=> 'int',
										//	'barres_amperage_type_id'							=> 'int',
										//	'disjoncteur_principal_calibre_id'					=> 'int',
										//	'disjoncteur_principal_modele_id'					=> 'int',
										//	'boitier_modele'									=> 'int',
										//	'montage_panneau_type_id'							=> 'int',
										//	'malt_isole'										=> 'int',
										//	'neutre_200'										=> 'int',
										//	'alimentation_cable_calibre_id'						=> 'int',
										//	'alimentation_cable_longueur'						=> 'int',
										//	'alimentation_circuit_parallele'					=> 'int',
										//	'alimentation_disjoncteur_installation_id'			=> 'int',
										//	'alimentation_disjoncteur_calibre_id'				=> 'int',
										//	'alimentation_disjoncteur_ajustement_amperage_id'	=> 'int',
										//	'alimentation_disjoncteur_capacite_rupture_id'		=> 'int',
										//	'commentaires'										=> 'varchar',
							);

$validColumns['doc']['generaux_locaux'] = array(
										//	'fiche_id' 					=> 'int',
										//	'utilite'					=> 'varchar',
										//	'type_soin_id'				=> 'int',
										//	'remarque'					=> 'varchar',
										//	'niveaux_1_id'				=> 'int',
										//	'niveaux_2_id'				=> 'int',
										//	'niveaux_3_id'				=> 'int',
										//	'niveaux_4_id'				=> 'int',
										//	'niveaux_5_id'				=> 'int',
										//	'niveaux_6_id'				=> 'int',
							);

$validColumns['doc']['equipements_bio_medicaux'] = array(
										//	'fiche_id' 					=> 'int',
							);

$validColumns['doc']['equipements_bio_medicaux_no_coord'] = array(
										//	'fiche_id' 					=> 'int',
							);

$validColumns['doc']['accessoires_architecturaux'] = array(
										//	'fiche_id' 								=> 'int',
										//	'no' 									=> 'varchar',
										//	'accessoire_architecturaux_type_id' 	=> 'int',
										//	'quantite_id' 							=> 'int',
										//	'commentaires' 							=> 'varchar',
							);

$validColumns['doc']['dispositifs_electriques_champs'] = array(
										//	'fiche_id' 					=> 'int',
							);

$validColumns['doc']['proprietes_electriques'] = array(
										//	'fiche_id' 					=> 'int',
							);

$validColumns['doc']['proprietes_electriques_auxiliaires'] = array(
										//	'fiche_id' 					=> 'int',
							);

$validColumns['doc']['proprietes_controles'] = array(
										//	'fiche_id' 					=> 'int',
							);

$validColumns['doc']['proprietes_controles_auxiliaires'] = array(
										//	'fiche_id' 					=> 'int',
							);

$validColumns['doc']['accessoires_sanitaires_ensembles'] = array(
										//	'fiche_id' 									=> 'int',
										//	'no' 										=> 'varchar',
										//	'accessoire_sanitaire_ensemble_type_id' 	=> 'int',
										//	'quantite_id' 								=> 'int',
										//	'commentaires' 								=> 'varchar',
							);

$validColumns['doc']['accessoires_sanitaires_unite'] = array(
										//	'fiche_id' 								=> 'int',
										//	'no' 									=> 'varchar',
										//	'accessoire_sanitaire_unite_type_id' 	=> 'int',
										//	'quantite_id' 							=> 'int',
										//	'commentaires' 							=> 'varchar',
							);

$validColumns['doc']['appels_locaux_generaux'] = array(
										//	'fiche_id' 									=> 'int',
										//	'appel_locaux_generaux_equipement_type_id' 	=> 'varchar',
										//	'installation_type_id' 						=> 'int',
										//	'appel_locaux_generaux_type_id' 			=> 'int',
										//	'appel_locaux_generaux_bloc_id' 			=> 'int',
										//	'appel_locaux_generaux_etage_id' 			=> 'int',
										//	'appel_locaux_generaux_zone_id' 			=> 'int',
										//	'quantite_id' 								=> 'int',
										//	'commentaires' 								=> 'varchar',
							);

$validColumns['doc']['parametres_architectures'] = array(
										//	'fiche_id' 							=> 'int',
										//	'superficie_net_totale' 			=> 'float',
										//	'superficie_net_sous_espace'       	=> 'float',
										//	'frequentation_id' 					=> 'int',
										//	'presence_id' 						=> 'int',
										//	'milieu_sterile' 					=> 'int',
										//	'transport_pneumatique' 			=> 'int',
										//	'protection_rx' 					=> 'int',
										//	'matiere_dangeureuse' 				=> 'int',
										//	'niveau_bruit_id' 					=> 'int',
										//	'commentaires' 						=> 'varchar',
							);

$validColumns['doc']['systemes_ventilations'] = array(
										//	'fiche_id' 									=> 'int',
										//	'equipement_systeme_ventilation_type_id' 	=> 'int',
										//	'commentaires' 								=> 'varchar',
							);

$validColumns['doc']['groupes_electrogenes'] = array(
										//	'fiche_id' 						=> 'int',
										//	'commentaires' 					=> 'varchar',
							);


/*


************************************************************************************************************************* Ajout formulaires de type listes *******************************************************************************************************************************************************************************


Mettre le nom de toutes les tables qui contiennent les informations de la librairie comme par exemple cloisons_types
ET
Inscrire le nom des champs des tables qui servent à stocker les Types d'éléments (dans les librairies).

*/

// Types mais pas sur ...  ( à tester) ********************************************************************************




$validColumns['doc']['reseaux'] = array(
											'nom' 			=> 'varchar',
							);

$validColumns['doc']['marques'] = array(
											'nom' 			=> 'varchar',
							);

$validColumns['doc']['types_cartes'] = array(
											'nom' 			=> 'varchar',
							);

$validColumns['doc']['conversions_noms_colonnes'] = array(
											'nom' 			=> 'varchar',
							);

$validColumns['doc']['disjoncteurs_modeles'] = array(
											'nom' 			=> 'varchar',
											'nom_en' 		=> 'varchar',
							);

$validColumns['doc']['fabricants'] = array(
											'nom' 			=> 'varchar',
							);

$validColumns['doc']['correlations'] = array(
											'nom' 			=> 'varchar',
							);

$validColumns['doc']['periodes'] = array(
											'nom' 			=> 'varchar',
											'nom_en' 		=> 'varchar',
							);

$validColumns['doc']['fonctionnements'] = array(
											'nom' 			=> 'varchar',
											'nom_en' 		=> 'varchar',
							);

$validColumns['doc']['raccords_plomberies_materiaux'] = array(
											'nom' 			=> 'varchar',
											'nom_en' 		=> 'varchar',
											'is_defaut' 	=> 'int',
							);

$validColumns['doc']['niveaux_confinements'] = array(
											'nom' 			=> 'varchar',
											'nom_en' 		=> 'varchar',
											'is_defaut' 	=> 'int',
							);

$validColumns['doc']['niveaux_bruits'] = array(
											'nom' 			=> 'varchar',
											'nom_en' 		=> 'varchar',
											'is_defaut' 	=> 'int',
							);

$validColumns['doc']['niveaux_vibrations_toleres'] = array(
											'nom' 			=> 'varchar',
											'nom_en' 		=> 'varchar',
											'is_defaut' 	=> 'int',
							);

$validColumns['doc']['taux_occupations'] = array(
											'nom' 			=> 'varchar',
											'is_defaut' 	=> 'int',
							);

$validColumns['doc']['pourcentages'] = array(
											'nom' 			=> 'varchar',
											'is_defaut' 	=> 'int',
							);

$validColumns['doc']['presences'] = array(
											'nom' 			=> 'varchar',
											'nom_en' 		=> 'varchar',
											'is_defaut' 	=> 'int',
							);

$validColumns['doc']['prises_electriques_grades'] = array(
											'nom' 			=> 'varchar',
											'nom_en' 		=> 'varchar',
											'is_defaut' 	=> 'int',
							);

$validColumns['doc']['quantites'] = array(
											'nom' 			=> 'varchar',
											'is_defaut' 	=> 'int',
							);

$validColumns['doc']['temperatures'] = array(
											'nom' 			=> 'varchar',
											'is_defaut' 	=> 'int',
							);

$validColumns['doc']['transports_pneumatiques'] = array(
											'nom' 			=> 'varchar',
											'nom_en' 		=> 'varchar',
											'is_defaut' 	=> 'int',
							);

$validColumns['doc']['principals_secondaires'] = array(
											'nom' 			=> 'varchar',
											'nom_en' 		=> 'varchar',
											'is_defaut' 	=> 'int',
							);

$validColumns['doc']['priorites'] = array(
											'nom' 			=> 'varchar',
											'nom_en' 		=> 'varchar',
											'is_defaut' 	=> 'int',
							);

$validColumns['doc']['existants_nouveaux'] = array(
											'nom' 			=> 'varchar',
											'nom_en' 		=> 'varchar',
											'is_defaut' 	=> 'int',
							);

$validColumns['doc']['fournis_installes'] = array(
											'nom' 			=> 'varchar',
											'nom_en' 		=> 'varchar',
											'is_defaut' 	=> 'int',
							);

$validColumns['doc']['frequentations'] = array(
											'nom' 			=> 'varchar',
											'nom_en' 		=> 'varchar',
											'is_defaut' 	=> 'int',
							);

$validColumns['doc']['horaire_normal_utilisation'] = array(
											'nom' 			=> 'varchar',
											'nom_en' 		=> 'varchar',
											'is_defaut' 	=> 'int',
							);

$validColumns['doc']['humidites_relatives'] = array(
											'nom' 			=> 'varchar',
											'is_defaut' 	=> 'int',
							);

$validColumns['doc']['transfo_temperatures'] = array(
											'nom' 			=> 'varchar',
											'is_defaut' 	=> 'int',
							);

$validColumns['doc']['transfo_impedances'] = array(
											'nom' 			=> 'varchar',
											'is_defaut' 	=> 'int',
							);

$validColumns['doc']['transfo_k_facteurs'] = array(
											'nom' 			=> 'varchar',
											'is_defaut' 	=> 'int',
							);

$validColumns['doc']['questions_categories'] = array(
											'nom' 			=> 'varchar',
											'is_defaut' 	=> 'int',
							);


$validColumns['doc']['phases_incidences'] = array(
											'nom' 			=> 'varchar',
											'is_defaut' 	=> 'int',
							);


$validColumns['doc']['equipements_bio_medicaux_types_categories_responsabilite'] = array(
											'nom' 			=> 'varchar',
											'nom_en' 		=> 'varchar',
											'is_defaut' 	=> 'int',
							);


$validColumns['doc']['equipements_bio_medicaux_types_directions_responsables'] = array(
											'nom' 			=> 'varchar',
											'nom_en' 		=> 'varchar',
											'is_defaut' 	=> 'int',
							);


$validColumns['doc']['equipements_bio_medicaux_types_sous_categories'] = array(
											'nom' 			=> 'varchar',
											'nom_en' 		=> 'varchar',
											'is_defaut' 	=> 'int',
							);


$validColumns['doc']['equipements_bio_medicaux_types_responsables_logistiques'] = array(
											'nom' 			=> 'varchar',
											'nom_en' 		=> 'varchar',
											'is_defaut' 	=> 'int',
                            );

$validColumns['doc']['infrastructure_materiaux'] = array(
											'nom' 			=> 'varchar',
											'nom_en' 		=> 'varchar',
											'is_defaut' 	=> 'int',
                            );

$validColumns['doc']['aqueduc_composantes'] = array(
											'nom' 			=> 'varchar',
											'nom_en' 		=> 'varchar',
											'is_defaut' 	=> 'int',
                            );

$validColumns['doc']['egout_pluvial_composantes'] = array(
											'nom' 			=> 'varchar',
											'nom_en' 		=> 'varchar',
											'is_defaut' 	=> 'int',
                            );

$validColumns['doc']['egout_sanitaire_composantes'] = array(
											'nom' 			=> 'varchar',
											'nom_en' 		=> 'varchar',
											'is_defaut' 	=> 'int',
                            );

$validColumns['doc']['reseau_electriques_composantes'] = array(
											'nom' 			=> 'varchar',
											'nom_en' 		=> 'varchar',
											'is_defaut' 	=> 'int',
                            );

$validColumns['doc']['reseau_gas_composantes'] = array(
											'nom' 			=> 'varchar',
											'nom_en' 		=> 'varchar',
											'is_defaut' 	=> 'int',
                            );

$validColumns['doc']['reseaux_telecoms_composantes'] = array(
											'nom' 			=> 'varchar',
											'nom_en' 		=> 'varchar',
											'is_defaut' 	=> 'int',
                            );

$validColumns['doc']['pavage_materiaux'] = array(
											'nom' 			=> 'varchar',
											'nom_en' 		=> 'varchar',
											'is_defaut' 	=> 'int',
                            );

$validColumns['doc']['taux_utilisations'] = array(
											'nom' 			=> 'varchar',
											'nom_en' 		=> 'varchar',
											'is_defaut' 	=> 'int',
                            );

$validColumns['doc']['raccords_plomberies_nature'] = array(
											'nom' 			=> 'varchar',
											'nom_en' 		=> 'varchar',
											'is_defaut' 	=> 'int',
                            );

$validColumns['doc']['plomberies_substance'] = array(
											'nom' 			=> 'varchar',
											'nom_en' 		=> 'varchar',
											'is_defaut' 	=> 'int',
                            );

$validColumns['doc']['instruments_fonctions'] = array(
											'nom_en' 		=> 'varchar',
											'nom' 			=> 'varchar',
											'is_defaut' 	=> 'int',
                            );

$validColumns['doc']['signal_nature'] = array(
											'nom' 			=> 'varchar',
											'nom_en' 		=> 'varchar',
											'is_defaut' 	=> 'int',
                            );

$validColumns['doc']['ouis_nons'] = array(
											'nom' 			=> 'varchar',
											'nom_en' 		=> 'varchar',
											'is_defaut' 	=> 'int',
                            );

$validColumns['doc']['locaux_classifications'] = array(
											'nom' 			=> 'varchar',
											'nom_en' 		=> 'varchar',
											'is_defaut' 	=> 'int',
                            );

$validColumns['doc']['locaux_resistance_feux'] = array(
											'nom' 			=> 'varchar',
											'nom_en' 		=> 'varchar',
											'is_defaut' 	=> 'int',
                            );

$validColumns['doc']['locaux_substrats'] = array(
											'nom' 			=> 'varchar',
											'nom_en' 		=> 'varchar',
											'is_defaut' 	=> 'int',
                            );

$validColumns['doc']['locaux_finis'] = array(
											'nom' 			=> 'varchar',
											'nom_en' 		=> 'varchar',
											'is_defaut' 	=> 'int',
                            );

$validColumns['doc']['locaux_plinthes'] = array(
											'nom' 			=> 'varchar',
											'nom_en' 		=> 'varchar',
											'is_defaut' 	=> 'int',
                            );

$validColumns['doc']['portes_models'] = array(
											'nom' 			=> 'varchar',
											'nom_en' 		=> 'varchar',
											'is_defaut' 	=> 'int',
                            );

$validColumns['doc']['portes_materiaux'] = array(
											'nom' 			=> 'varchar',
											'nom_en' 		=> 'varchar',
											'is_defaut' 	=> 'int',
                            );

$validColumns['doc']['portes_vitrages'] = array(
											'nom' 			=> 'varchar',
											'nom_en' 		=> 'varchar',
											'is_defaut' 	=> 'int',
                            );

$validColumns['doc']['portes_pellicules_vitrage'] = array(
											'nom' 			=> 'varchar',
											'nom_en' 		=> 'varchar',
											'is_defaut' 	=> 'int',
                            );

$validColumns['doc']['portes_finis'] = array(
											'nom' 			=> 'varchar',
											'nom_en' 		=> 'varchar',
											'is_defaut' 	=> 'int',
                            );

$validColumns['doc']['porte_vitrage_cadre'] = array(
											'nom' 			=> 'varchar',
											'nom_en' 		=> 'varchar',
											'is_defaut' 	=> 'int',
                            );

$validColumns['doc']['portes_persiennes'] = array(
											'nom' 			=> 'varchar',
											'nom_en' 		=> 'varchar',
											'is_defaut' 	=> 'int',
                            );

$validColumns['doc']['niveaux_criticites'] = array(
											'nom' 			=> 'varchar',
											'nom_en' 		=> 'varchar',
											'is_defaut' 	=> 'int',
							);

$validColumns['doc']['duplexe_simplex'] = array(
											'nom' 			=> 'varchar',
											'nom_en' 		=> 'varchar',
											'is_defaut' 	=> 'int',
                            );

$validColumns['doc']['ailettes_serpentins_modeles'] = array(
											'nom' 				=> 'varchar',
											'nom_en' 			=> 'varchar',
											'is_defaut' 		=> 'int',
                            );

$validColumns['doc']['ailettes_serpentins_materiaux'] = array(
											'nom' 				=> 'varchar',
											'nom_en' 			=> 'varchar',
											'is_defaut' 		=> 'int',
                            );

$validColumns['doc']['equipements_installations_methodes'] = array(
											'nom' 				=> 'varchar',
											'nom_en' 			=> 'varchar',
											'is_defaut' 		=> 'int',
                            );

$validColumns['doc']['formulaires'] = array(
											'nom' 				=> 'varchar',
											'discipline_id' 	=> 'int',
											'nom_table_source' 	=> 'varchar',
											'nom_table_cible' 	=> 'varchar',
											'nom_champ_cible' 	=> 'varchar',
											'type_formulaire_id' => 'int',
							);

$validColumns['doc']['formulaires_listes'] = array(
											'projet_id' 		=> 'int',
											'nom_formulaire' 	=> 'varchar',
											'nom_table' 		=> 'varchar',
							);

$validColumns['doc']['formulaires_customs'] = array(
    'projet_id' 		=> 'int',
    'nom_formulaire' 	=> 'varchar',
    'nom_onglet' 		=> 'varchar',
);

$validColumns['doc']['changements_airs_heures'] = array(
											'nom' 				=> 'varchar',
											'nom_en' 			=> 'varchar',
											'is_defaut' 		=> 'int',
							);

$validColumns['doc']['eclairages_ratios_urgences'] = array(
											'nom' 				=> 'varchar',
											'is_defaut' 		=> 'int',
							);

$validColumns['doc']['conversions_unites'] = array(
											'nom' 				=> 'varchar',
											'nom_sortie' 		=> 'varchar',
											'formule' 			=> 'varchar',
							);

$validColumns['doc']['elements'] = array(
											'nom'               => 'varchar',
											'element_type_id' 	=> 'int',
							);

$validColumns['doc']['motifs_changements'] = array(
											'nom_fr' 			=> 'varchar',
											'nom_en' 			=> 'varchar',
							);

$validColumns['doc']['approvisionnements_etapes_noms'] = array(
											'nom_fr' 			=> 'varchar',
											'nom_en'			=> 'varchar',
							);

$validColumns['doc']['disciplines'] = array(
											'nom_fr' 			=> 'varchar',
											'nom_en' 			=> 'varchar',
											'code' 				=> 'varchar',
							);

$validColumns['doc']['unites'] = array(
											'nom' 				=> 'varchar',
											'unite_type_id' 	=> 'int',
							);

$validColumns['doc']['formulaires_listes_parametres'] = array(
											'nom_formulaire' 	=> 'varchar',
											'nom_table' 		=> 'varchar',
							);

$validColumns['doc']['formulaires_customs_parametres'] = array(
    'nom_formulaire' 	=> 'varchar',
    'nom_onglet' 		=> 'varchar',
);

$validColumns['doc']['formulaires_readonly'] = array(
											'projet_id' 		=> 'int',
											'formulaire_id' 	=> 'int',
							);

$validColumns['doc']['formulaires_hidden'] = array(
    'projet_id' 		=> 'int',
    'formulaire_id' 	=> 'int',
);

$validColumns['doc']['modeles_impressions'] = array(
    'projet_id' 		                => 'int',
    'nom' 				                => 'varchar',
    'description' 		                => 'varchar',
    'is_inclure_formulaires_remplis' 	=> 'int',
);


$validColumns['doc']['questions_groupes_usagers'] = array(
											'projet_id' 		=> 'int',
											'nom' 				=> 'varchar',
							);
$validColumns['doc']['notifications_acquisitions_projets_jalons'] = array(
    'acquisition_projet_jalon_id' 		=> 'int',
);

$validColumns['doc']['acquisitions_projets_groupes_usagers'] = array(
	'projet_id' 		=> 'int',
	'nom' 				=> 'varchar',
);

$validColumns['doc']['exemptions_verrouillages'] = array(
    'projet_id' 		=> 'int',
    'date_echeance' 	=> 'varchar',
);

$validColumns['doc']['visualisateur3d_fichiers'] = array(
    'nom' 		=> 'varchar',
    'is_plancher_focus' => 'int',
    'mode_tree' 		=> 'varchar',
);

$validColumns['doc']['cedules_demenagements'] = array(
    'code' 		=> 'varchar',
    'description' => 'varchar',
    'usager_responsable_id' 		=> 'int',
    'statut' 		=> 'int',
    'date_debut' => 'varchar',
    'date_fin' => 'varchar',
    'commentaire' => 'varchar',
);

$validColumns['doc']['roles'] = array(
											'nom' 				=> 'varchar',
											'code' 				=> 'varchar',
							);

$validColumns['doc']['permissions'] = array(
											'nom' 				=> 'varchar',
											'code' 				=> 'varchar',
							);

$validColumns['doc']['automates'] = array(
											'nom' 				=> 'varchar',
											'panneau_id' 		=> 'int',
							);

$validColumns['doc']['echeanciers'] = array(
											'nom' 				=> 'varchar',
											'description' 		=> 'varchar',
											'type' => 'varchar'
							);

$validColumns['doc']['formulaires_limitations'] = array(
											'projet_id' 	=> 'int',
											'formulaire_id' => 'int',
											'can_add' 		=> 'int',
											'can_delete' 	=> 'int',
							);

$validColumns['doc']['diametres_conduits_electricites'] = array(
											'metrique' 		=> 'varchar',
											'imperial' 		=> 'varchar',
											'standard' 		=> 'varchar',
											'is_defaut' 	=> 'int',
							);

$validColumns['doc']['diametres_conduits_plomberies'] = array(
											'metrique' 		=> 'varchar',
											'imperial' 		=> 'varchar',
											'standard' 		=> 'varchar',
											'is_defaut' 	=> 'int',
							);

$validColumns['doc']['diametres_conduits_canalisations'] = array(
											'metrique' 		=> 'varchar',
											'imperial' 		=> 'varchar',
											'standard' 		=> 'varchar',
											'is_defaut' 	=> 'int',
							);

$validColumns['doc']['pseudos'] = array(
											'nom' 			=> 'varchar',
											'label_fr' 		=> 'varchar',
											'label_en' 		=> 'varchar',
							);

$validColumns['doc']['modeles'] = array(
											'numero_modele'		=> 'varchar',
											'marque_id' 		=> 'int',
											'nomenclature_id' 	=> 'int',
							);

$validColumns['doc']['nomenclatures'] = array(
											'code' 				=> 'varchar',
											'nom_fr' 			=> 'varchar',
											'nom_en' 			=> 'varchar',
											'discipline_id' 	=> 'int',
							);

$validColumns['doc']['acquisitions_jalons_modeles'] = array(
											'id'         		=> 'int',
                                            'groupe_jalon'		=> 'varchar',
											'nom_jalon'    		=> 'varchar',
											'duree_jalon'    	=> 'int',
											'order_jalon'    	=> 'int',
							);

$validColumns['doc']['financements_jalons_modeles'] = array(
											'id'         		=> 'int',
                                            'groupe_jalon'		=> 'varchar',
											'nom_jalon'    		=> 'varchar',
											'duree_jalon'    	=> 'int',
											'order_jalon'    	=> 'int',
							);

$validColumns['doc']['contrats_jalons_modeles'] = array(
	'id'         		=> 'int',
	'nom'    		=> 'varchar',
);

$validColumns['doc']['notifications_gestion'] = array(
											'is_all_fiches'   	=> 'int',
											'is_all_champs'   	=> 'int',
											'conserver'       	=> 'int',
							);

$validColumns['doc']['usagers'] = array(
											'langue' 		=> 'varchar',
											'sexe' 			=> 'varchar',
											'nom' 			=> 'varchar',
											'prenom' 		=> 'varchar',
											'email' 		=> 'varchar',
											'password' 		=> 'varchar',
											'is_pilote' 	=> 'int',
							);

$validColumns['doc']['champs_ponctuels'] = array(
											'titre_bloc' 	=> 'varchar',
											'titre_col_1' 	=> 'varchar',
											'titre_col_2' 	=> 'varchar',
											'titre_col_3' 	=> 'varchar',
											'titre_col_4' 	=> 'varchar',
											'type_saisie' 	=> 'varchar',
											'multiple' 		=> 'int',
							);

$validColumns['doc']['batiments'] = array(
											'code' 			=> 'varchar',
											'nom' 			=> 'varchar',
											'ouvrage_id' 	=> 'int',
											'reseau_id' 	=> 'int',
											'priorite'		=> 'int',
											'description'	=> 'varchar',
							);

$validColumns['doc']['routes'] = array(
											'code' 				=> 'varchar',
											'nom' 				=> 'varchar',
											'route_type_id' 	=> 'int',
											'ouvrage_id' 		=> 'int',
											'reseau_id' 		=> 'int',
											'chainage_debut'	=> 'int',
											'chainage_fin'  	=> 'int',
											'precision'     	=> 'int',
											'description'		=> 'varchar',
							);

$validColumns['doc']['correlations_champs_externes'] = array(
											'projet_id' 		=> 'int',
											'code_externe'		=> 'varchar',
											'formulaire'		=> 'varchar',
											'nom_champ'     	=> 'varchar',
							);

$validColumns['doc']['batiments_niveaux'] = array(
											'ouvrage_id' 		=> 'int',
											'nom' 				=> 'varchar',
											'ordre'        	 	=> 'int',
											'description'		=> 'varchar',
							);

$validColumns['doc']['uni_reglementations_articles_listes'] = array(
															'reglementation_niveau_element_id' 	=> 'int',
							);

$validColumns['doc']['uni_reglementations_niveaux'] = array(
													'nom' 			=> 'varchar',
													'ordre'        	=> 'int',
													'description'	=> 'varchar',
							);

$validColumns['doc']['panneaux'] = array(
											'nom' 				=> 'varchar',
											'localisation' 		=> 'varchar',
											'batiment_id' 		=> 'int',
							);

$validColumns['doc']['gabarits_exports'] = array(
											'nom' 					=> 'varchar',
											'description' 			=> 'varchar',
											'fiche_sous_type_id' 	=> 'int',
											'numero_lot' 			=> 'varchar',
											'type'          		=> 'varchar',
											'formulaire_id' 		=> 'int',
							);

$validColumns['doc']['suivis_budgetaires'] = array(
											'renouveller_section' 	=> 'varchar',
											'renouveller_alias' 	=> 'varchar',
											'renouveller_calcul' 	=> 'varchar',
											'acheter_section' 		=> 'varchar',
											'acheter_alias' 		=> 'varchar',
											'acheter_calcul' 		=> 'varchar',
											'demenager_section' 	=> 'varchar',
											'demenager_alias' 		=> 'varchar',
											'demenager_calcul' 		=> 'varchar',
											'developpement_section' => 'varchar',
											'developpement_alias' 	=> 'varchar',
											'developpement_calcul' 	=> 'varchar',
											'code_alias' 			=> 'varchar',
											'cout_alias' 			=> 'varchar',
								);
							
$validColumns['doc']['acquisitions_projets'] = array(
											'numero' 							=> 'varchar',
        									'description' 						=> 'varchar',
    										'priorite_acquisition_id' 			=> 'int',
    										'menu_supplementaire_a_id' 			=> 'int',
    										'conseiller_nch' 					=> 'varchar',
  											'expert_contenu' 					=> 'varchar',
   											'responsable_sda' 					=> 'varchar',
    										'leader' 							=> 'varchar',
    										'responsable_dossier_1_id' 			=> 'int',
    										'responsable_dossier_2_id' 			=> 'int',
    										'responsable_dossier_3_id' 			=> 'int',
    										'responsable_dossier_4_id' 			=> 'int',
    										'responsable_dossier_5_id' 			=> 'int',
    										'responsable_dossier_6_id' 			=> 'int',
    										'responsable_dossier_7_id' 			=> 'int',
    										'responsable_dossier_8_id' 			=> 'int',
											'responsable_dossier_9_id' 			=> 'int',
											'responsable_dossier_10_id' 		=> 'int',
											'responsable_dossier_a_id' 			=> 'int',
											'responsable_dossier_b_id' 			=> 'int',
											'responsable_dossier_c_id' 			=> 'int',
											'responsable_dossier_d_id' 			=> 'int',
											'responsable_dossier_e_id' 			=> 'int',
											'responsable_dossier_f_id' 			=> 'int',
											'responsable_dossier_g_id' 			=> 'int',
											'responsable_dossier_h_id' 			=> 'int',
											'responsable_dossier_i_id' 			=> 'int',
											'responsable_dossier_j_id' 			=> 'int',
											'responsable_dossier_contractuel_id'=> 'int',
  											'ref_nego_existante'				=> 'varchar',
  											'livraison_estime'					=> 'varchar',
  											'debut_contrat'						=> 'varchar',
											'expiration_contrat'				=> 'varchar',
											'ref_achat'							=> 'varchar',
											'ao'								=> 'varchar',
											'mode_acquisition'					=> 'varchar',
											'portee_ao'							=> 'varchar',
											'num_requisition'					=> 'varchar',
											'projet_drv'						=> 'varchar',
											'bon_de_commande'					=> 'varchar',
											'bon_commande'						=> 'varchar',
											'avant_taxes'						=> 'varchar',
											'code_taxe'							=> 'varchar',
											'fournisseur'						=> 'varchar',
											'manufacturier'						=> 'varchar',
											'modele'							=> 'varchar',
											'livraison_planifie'				=> 'varchar',
											'livraison_reserve'					=> 'varchar',
											'site_livraison_id'					=> 'int',
											'local_transitoire_id'				=> 'int',
											'statut_livraison'					=> 'varchar',
											'deficience_livraison'				=> 'varchar',
											'commentaire_logistique'			=> 'varchar',
    										'commentaires' 						=> 'varchar',
    										'jalons_modeles' 					=> 'varchar',
											'bloquant_type_id' 					=> 'varchar',
											'suivi_administratif' 				=> 'varchar',
											'sous_volet_responsable_id'			=> 'int',
											'phase_incidence_contraignante_id'	=> 'int',
											'priorisation_id'					=> 'int',
											'mode_sollicitation_id'				=> 'int',
											'mode_adjudication_id'				=> 'int',
											'champ_supplementaire_projet_1' 	=> 'varchar',
											'champ_supplementaire_projet_2' 	=> 'varchar',
											'champ_supplementaire_projet_3' 	=> 'varchar',
											'champ_supplementaire_projet_4' 	=> 'varchar',
											'champ_supplementaire_projet_5' 	=> 'varchar',
											'champ_supplementaire_projet_6' 	=> 'varchar',
											'champ_supplementaire_projet_7' 	=> 'varchar',
											'champ_supplementaire_projet_8' 	=> 'varchar',
											'champ_supplementaire_projet_9' 	=> 'varchar',
											'champ_supplementaire_projet_10' 	=> 'varchar',
											'avancement_jalon_en_cours' 	    => 'int',
							);

$validColumns['doc']['evenements_systeme'] = array(
	'type_evenement' 						=> 'varchar',
	'module_touche' 						=> 'varchar',
	'titre' 			                    => 'varchar',
	'description' 							=> 'varchar',
	'is_affiche_message_tous' 				=> 'int',
	'message_tous' 							=> 'varchar',
);

$validColumns['doc']['admin_liste_colonnes_bio_medicaux'] = array(
	'projet_id' 						    => 'int',
);

$validColumns['doc']['contrats_projets'] = array(
											'acquisition_projet_id'				=> 'int',
											'numero_appel_offre' 				=> 'varchar',
        									'titre_appel_offre' 				=> 'varchar',
    										'phase_incidence_contraignante' 	=> 'varchar',
											'numero_seao' 				        => 'varchar',
    										'commentaires' 					    => 'varchar',
    										'responsable_dossier_1_id' 			=> 'int',
    										'responsable_dossier_2_id' 			=> 'int',
    										'responsable_dossier_3_id' 			=> 'int',
    										'responsable_dossier_4_id' 			=> 'int',
    										'responsable_dossier_5_id' 			=> 'int',
    										'responsable_dossier_6_id' 			=> 'int',
    										'responsable_dossier_7_id' 			=> 'int',
    										'responsable_dossier_8_id' 			=> 'int',
											'champ_supplementaire_projet_1' 	=> 'varchar',
											'champ_supplementaire_projet_2' 	=> 'varchar',
											'champ_supplementaire_projet_3' 	=> 'varchar',
											'champ_supplementaire_projet_4' 	=> 'varchar',
											'champ_supplementaire_projet_5' 	=> 'varchar',
											'champ_supplementaire_projet_6' 	=> 'varchar',
											'champ_supplementaire_projet_7' 	=> 'varchar',
											'champ_supplementaire_projet_8' 	=> 'varchar',
											'champ_supplementaire_projet_9' 	=> 'varchar',
											'champ_supplementaire_projet_10' 	=> 'varchar',
											'contrat_jalon_modele_id'			=> 'int',
							);

$validColumns['doc']['financements_projets'] = array(
											'numero' 							=> 'varchar',
        									'description' 						=> 'varchar',
    										'priorite_financement_id' 			=> 'int',
    										'intervenant_1' 					=> 'varchar',
    										'intervenant_2' 					=> 'varchar',
    										'intervenant_3' 					=> 'varchar',
    										'intervenant_4' 					=> 'varchar',
											'responsable_dossier_a_id' 			=> 'int',
											'responsable_dossier_b_id' 			=> 'int',
											'responsable_dossier_c_id' 			=> 'int',
											'responsable_dossier_d_id' 			=> 'int',
											'responsable_dossier_e_id' 			=> 'int', 
    										'commentaires' 						=> 'varchar',
    										'jalons_modeles' 					=> 'varchar',
											'bloquant_financement_type_id' 		=> 'varchar',
											'suivi_administratif' 				=> 'varchar',
											'champ_supplementaire_projet_1' 	=> 'varchar',
											'champ_supplementaire_projet_2' 	=> 'varchar',
											'champ_supplementaire_projet_3' 	=> 'varchar',
											'champ_supplementaire_projet_4' 	=> 'varchar',
											'champ_supplementaire_projet_5' 	=> 'varchar',
											'champ_supplementaire_projet_6' 	=> 'varchar',
											'champ_supplementaire_projet_7' 	=> 'varchar',
											'champ_supplementaire_projet_8' 	=> 'varchar',
											'champ_supplementaire_projet_9' 	=> 'varchar',
											'champ_supplementaire_projet_10' 	=> 'varchar',
											'champ_reference' 					=> 'varchar'
							);


$validColumns['doc']['acquisitions_projets_groupes_equipements'] = array(
											'numero' 							=> 'varchar',
											'description' 						=> 'varchar',
											'equipement_bio_medicaux_type_id' 	=> 'int',
											'acquisition_projet_id' 			=> 'int',
											'ref_nego_existante' 				=> 'varchar',
											'livraison_estime' 					=> 'varchar',
											'debut_contrat' 					=> 'varchar',
											'expiration_contrat' 				=> 'varchar',
											'ref_achat' 						=> 'varchar',
											'ao' 								=> 'varchar',
											'mode_acquisition' 					=> 'varchar',
											'portee_ao' 						=> 'varchar',
											'num_requisition' 					=> 'varchar',
											'cout_unitaire_effectif' 			=> 'varchar',
											'projet_drv' 						=> 'varchar',
											'bon_commande' 						=> 'varchar',
											'avant_taxes' 						=> 'varchar',
											'code_taxe' 						=> 'varchar',
											'fournisseur' 						=> 'varchar',
											'manufacturier' 					=> 'varchar',
											'modele' 							=> 'varchar',
    										'commentaires' 						=> 'varchar',
											'livraison_planifie' 				=> 'varchar',
											'livraison_reserve' 				=> 'varchar',
											'site_livraison_id' 				=> 'varchar',
											'local_transitoire_id' 				=> 'varchar',
											'statut_livraison' 					=> 'varchar',
											'deficience_livraison' 				=> 'varchar',
    'commentaire_logistique' 			=> 'varchar',
    'date_installation' 			=> 'date',
    'statut_installation' 			=> 'varchar',
    'acquisition_responsable_installation_id' 			=> 'int',
    'deficience_installation' 			=> 'int',
    'commentaire_installation' 			=> 'varchar',
							);

$validColumns['doc']['financements_projets_groupes_equipements'] = array(
											'numero' 							=> 'varchar',
											'description' 						=> 'varchar',
											'equipement_bio_medicaux_type_id' 	=> 'int',
											'financement_projet_id' 			=> 'int',
											'ref_nego_existante' 				=> 'varchar',
											'livraison_estime' 					=> 'varchar',
											'debut_contrat' 					=> 'varchar',
											'expiration_contrat' 				=> 'varchar',
											'ref_achat' 						=> 'varchar',
											'ao' 								=> 'varchar',
											'mode_financement' 					=> 'varchar',
											'portee_ao' 						=> 'varchar',
											'num_requisition' 					=> 'varchar',
											'projet_drv' 						=> 'varchar',
											'bon_commande' 						=> 'varchar',
											'avant_taxes' 						=> 'varchar',
											'code_taxe' 						=> 'varchar',
											'fournisseur' 						=> 'varchar',
											'manufacturier' 					=> 'varchar',
											'modele' 							=> 'varchar',
    										'commentaires' 						=> 'varchar',
											'cout_taxe_net' 				    => 'varchar',
											'cout_projete' 				        => 'varchar',
											'site_livraison_id' 				=> 'varchar',
											'local_transitoire_id' 				=> 'varchar',
											'statut_livraison' 					=> 'varchar',
											'deficience_livraison' 				=> 'varchar',
											'commentaire_logistique' 			=> 'varchar',
											'champ_supplementaire_groupe_1' 	=> 'varchar',
											'champ_supplementaire_groupe_2' 	=> 'varchar',
											'champ_supplementaire_groupe_3' 	=> 'varchar',
											'champ_supplementaire_groupe_4' 	=> 'varchar',
											'champ_supplementaire_groupe_5' 	=> 'varchar',
											'champ_supplementaire_groupe_6' 	=> 'varchar',
											'champ_supplementaire_groupe_7' 	=> 'varchar',
											'champ_supplementaire_groupe_8' 	=> 'varchar',
											'champ_supplementaire_groupe_9' 	=> 'varchar',
											'champ_supplementaire_groupe_10' 	=> 'varchar',
							);

$validColumns['doc']['financements_projets_depots'] = array(
	'nom' 							    => 'varchar',
	'description' 						=> 'varchar',
	'financement_projet_id' 			=> 'int',
	'source_financement_id' 			=> 'int',
);

$validColumns['doc']['financements_projets_administrations'] = array(
	'projet_id' 					=> 'int',
	'configuration' 				=> 'varchar',
);
$validColumns['doc']['financements_projets_administrations_unifiees'] = array(
	'projet_id' 					=> 'int',
	'configuration' 				=> 'varchar',
);
$validColumns['doc']['suivis_budgetaires_administrations'] = array(
	'projet_id' 					=> 'int',
	'configuration' 				=> 'varchar',
);
$validColumns['doc']['acquisitions_projets_administrations'] = array(
	'projet_id' 					=> 'int',
	'configuration' 				=> 'varchar',
);
$validColumns['doc']['contrats_projets_administrations'] = array(
	'projet_id' 					=> 'int',
	'configuration' 				=> 'varchar',
);
$validColumns['doc']['champs_textes_exports'] = array(
											'nom' 							=> 'varchar',
											'description' 					=> 'varchar',
											'formulaire_id' 				=> 'int',
							);

$validColumns['doc']['champs_textes_denormalises'] = array(
											'nom' 							=> 'varchar',
											'description' 					=> 'varchar',
											'formulaire_id' 				=> 'int',
							);

$validColumns['doc']['imports'] = array(
											'nom' 							=> 'varchar',
											'description' 					=> 'varchar',
											'fiche_sous_type_id' 			=> 'int',
											'numero_lot' 					=> 'varchar',
											'type'          				=> 'varchar',
											'formulaire_id' 				=> 'int',
											'type_import'   				=> 'varchar',
							);

$validColumns['doc']['fonctions'] = array(
											'nom' 							=> 'varchar',
											'nom_en' 						=> 'varchar',
							);

$validColumns['doc']['appels_locaux_generaux_zones'] = array(
											'batiment_id' 				=> 'int',
											'nom' 						=> 'varchar',
											'is_defaut' 				=> 'int',
							);

$validColumns['doc']['appels_locaux_generaux_blocs'] = array(
											'batiment_id' 				=> 'int',
											'nom' 						=> 'varchar',
											'is_defaut' 				=> 'int',
							);

$validColumns['doc']['appels_locaux_generaux_etages'] = array(
											'batiment_id' 				=> 'int',
											'nom' 						=> 'varchar',
											'is_defaut' 				=> 'int',
							);

$validColumns['doc']['conversions_recettes'] = array(
											'formulaire_liste_nom_parametre_id' 	=> 'int',
											'valeur_parametre' 						=> 'varchar',
											'nom_colonne_id' 						=> 'int',
							);

$liste_durees = array(
    										'0.25' 			=> txt('15 minutes'),
    										'0.5' 			=> txt('30 minutes'),
    										'0.75' 			=> txt('45 minutes'),
    										'1' 			=> txt('1 heure'),
    										'1.5' 			=> txt('1.5 heures'),
    										'2' 			=> txt('2 heure'),
   									 		'2.5' 			=> txt('2.5 heures'),
    										'3' 			=> txt('3 heure'),
    										'3.5' 			=> txt('3.5 heures'),
    										'4' 			=> txt('4 heure'),
    										'4.5' 			=> txt('4.5 heures'),
    										'5' 			=> txt('5 heure'),
    										'5.5' 			=> txt('5.5 heures'),
    										'6' 			=> txt('6 heure'),
    										'6.5' 			=> txt('6.5 heures'),
    										'7' 			=> txt('7 heure'),
    										'7.5' 			=> txt('7.5 heures'),
    										'8' 			=> txt('8 heure'),

							);

$validColumns['doc']['clients'] = array(
											'nom' 				=> 'varchar',
											'adresse1' 			=> 'varchar',
											'adresse2' 			=> 'varchar',
											'ville' 			=> 'varchar',
											'province' 			=> 'varchar',
											'pays' 				=> 'varchar',
											'code_postal' 		=> 'varchar',
											'telephone' 		=> 'varchar',
											'fax' 				=> 'varchar',
							);

$validColumns['doc']['ouvrages'] = array(
											'code'          	=> 'varchar',
											'nom'           	=> 'varchar',
											'description' 		=> 'varchar',
							);

$validColumns['doc']['projets'] = array(
											'ouvrage_id' 			=> 'int',
											'client_id' 			=> 'int',
											'code'          		=> 'varchar',
											'nom'           		=> 'varchar',
											'description' 			=> 'varchar',
											'date_debut' 			=> 'date',
											'date_fin'      		=> 'date',
											'usager_id_directeur' 	=> 'int',
											'usager_id_dao' 		=> 'int',
											'type_conception' 		=> 'int',
											'is_listes_readonly' 	=> 'int',
											'is_affiche_categorie' 	=> 'int',
											'is_jalon_etalon' 	=> 'int',
											'ajout_code_structure_hierarchique' => 'int',
											'structure_hierarchique_filtres_principaux' => 'int',
											'is_equipements_tries_par_description' => 'int',
											'is_gestion_unifiee_portes' => 'int',
											'is_acquisition_jalon_reference' => 'int',
							);

$validColumns['doc']['fournisseurs'] = array(
											'nom' 				=> 'varchar',
											'adresse1' 			=> 'varchar',
											'adresse2' 			=> 'varchar',
											'ville' 			=> 'varchar',
											'province' 			=> 'varchar',
											'pays' 				=> 'varchar',
											'code_postal' 		=> 'varchar',
											'telephone' 		=> 'varchar',
											'fax' 				=> 'varchar',
							);
$validColumns['doc']['fournisseurs_marques'] = array(
	'fournisseur_id'                => 'int',
	'marque_id'                     => 'int',
);
$validColumns['doc']['generaux_equipements'] = array(
											'fiche_id' 					=> 'int',
											'utilite'					=> 'varchar',
											'remarque'					=> 'varchar',
											'local_id'					=> 'int',
											'localisation'				=> 'varchar',
											'fonctionnement_id'			=> 'int',
											'element_id'				=> 'int',
											'modele_id'					=> 'int',
											'nomenclature_id'			=> 'int',
							);

$validColumns['doc']['generaux_composantes'] = array(
											'fiche_id' 					=> 'int',
											'utilite'					=> 'varchar',
											'remarque'					=> 'varchar',
											'local_id'					=> 'int',
											'localisation'				=> 'varchar',
											'fonctionnement_id'			=> 'int',
											'element_id'				=> 'int',
											'modele_id'					=> 'int',
											'nomenclature_id'			=> 'int',
							);

$validColumns['doc']['generaux_instruments'] = array(
											'fiche_id' 					=> 'int',
											'utilite'					=> 'varchar',
											'remarque'					=> 'varchar',
											'local_id'					=> 'int',
											'localisation'				=> 'varchar',
											'fonctionnement_id'			=> 'int',
											'element_id'				=> 'int',
											'nomenclature_id'			=> 'int',
							);

$validColumns['doc']['generaux_vehicules'] = array(
											'fiche_id' 					=> 'int',
											'utilite'					=> 'varchar',
											'remarque'					=> 'varchar',
											'local_id'					=> 'int',
											'localisation'				=> 'varchar',
											'nomenclature_id'			=> 'int',
											'utilite_id'				=> 'int',
											'masse_net'					=> 'float',
											'nombre_essieux'			=> 'int',
											'couleur_id'				=> 'int',
											'annee'						=> 'int',
											'numero_serie'				=> 'varchar',
											'immatriculation'			=> 'varchar',
											'no_dossier_saaq'			=> 'varchar',
											'dimension_pneu'			=> 'varchar',
											'carburant_type_id'			=> 'int',
											'utilisateur_responsable'	=> 'varchar',
											'nombre_cylindre'			=> 'int',
											'volume_moteur'				=> 'float',
							);
							
$validColumns['doc']['acquisitions_groupes'] = array(
											'formulaire_id'         => 'int',
											'nom'                   => 'varchar',
											'description'           => 'varchar',
							);
							
$validColumns['doc']['financements_groupes'] = array(
											'formulaire_id'         => 'int',
											'nom'                   => 'varchar',
											'description'           => 'varchar',
							);

$validColumns['doc']['fiches'] = array(
											'fiche_parent_id'     			=> 'int',
											'fiche_type_id'     			=> 'int',
											'fiche_sous_type_id'			=> 'int',
											'batiment_id'       			=> 'int',
											'route_id'       				=> 'int',
											'chainage_debut'       			=> 'int',
											'chainage_fin'       			=> 'int',
											'batiment_niveau_element_id'	=> 'int',
											'code'             				=> 'varchar',
											'code_alternatif'              	=> 'varchar',
											'code_alternatif_2'            	=> 'varchar',
											'id_revit'              		=> 'varchar',
											'id_codebook'              		=> 'varchar',
											'is_gabarit'              		=> 'int',
											'numero_lot'              		=> 'varchar',
											'fiche_local_id'              	=> 'int',
											'fiche_local_destination_id'   	=> 'int',
											'fiche_local_controleur_id'    	=> 'int',
											'fiche_statut_id'    	        => 'int',
											'gestion_porte_type_id'    	    => 'int',
											'description'    	            => 'varchar',
							);

$validColumns['doc']['programmes_entretiens'] = array(
											'nom' 				=> 'varchar',
											'description' 		=> 'varchar',
											'periode_id' 		=> 'int',
											'discipline_id' 	=> 'int',
							);

$validColumns['doc']['procedures_entretiens'] = array(
											'nom'               => 'varchar',
											'description'       => 'varchar',
											'periode_id'        => 'int',
											'discipline_id'     => 'int',
											'type'              => 'varchar',
											'fiche_id'			=> 'int',
											'nomenclature_id' 	=> 'int',
											'modele_id'         => 'int',
											'projet_id'         => 'int',
											'duree_prevue'      => 'float',
							);

$validColumns['doc']['procedures_entretiens_planifiees'] = array(
											'discipline_id'         	=> 'int',
											'procedure_entretien_id'	=> 'int',
											'type_bon_travail'      	=> 'varchar',
											'date_travaux'          	=> 'date',
											'statut'                	=> 'varchar',
											'priorite'              	=> 'int',
											'usager_attribue_id'    	=> 'int',
											'mode_repetition'       	=> 'varchar',
							);

$validColumns['doc']['procedures_inspections'] = array(
											'nom'               => 'varchar',
											'description'       => 'varchar',
											'discipline_id'     => 'int',
											'type'              => 'varchar',
											'fiche_id' 			=> 'int',
											'nomenclature_id' 	=> 'int',
											'modele_id'         => 'int',
											'projet_id'         => 'int',
							);

$validColumns['doc']['procedures_inspections_gbm'] = array(
    'nom'               => 'varchar',
    'description'       => 'varchar',
    'projet_id'         => 'int',
);
$validColumns['doc']['procedures_mises_en_service_mep'] = array(
    'nom'               => 'varchar',
    'description'       => 'varchar',
    'projet_id'         => 'int',
);
$validColumns['doc']['inventaires'] = array(
	'nom'               => 'varchar'
);
$validColumns['doc']['mises_en_services'] = array(
    'nom'               => 'varchar'
);
$validColumns['doc']['equipements_mep'] = array(
    'code'               => 'varchar',
    'description'               => 'varchar',
    'mise_en_service_id'               => 'int',
    'barcode_nouveau'               => 'varchar',
    'marque_id'                     => 'int',
    'modele_id'                     => 'int',
    'numero_serie'                  => 'varchar',
    'date_fabrication'              => 'varchar',
    'hauteur'                       => 'float',
    'largeur'                       => 'float',
    'profondeur'                    => 'float',
    'type_installation_id'          => 'int',
    'condition_vetuste_id'          => 'int',
    'remarque'                      => 'varchar',
);
$validColumns['doc']['inventaires_lignes'] = array(
    'inventaire_id'                => 'int',
    'barcode_nouveau'               => 'varchar',
    'fiche_local_id'                => 'int',
    'inventaire_local_id'           => 'int',
    'code_norme_id'                 => 'int',
    'marque_id'                     => 'int',
    'modele_id'                     => 'int',
    'numero_serie'                  => 'varchar',
    'date_fabrication'              => 'varchar',
    'hauteur'                       => 'float',
    'largeur'                       => 'float',
    'profondeur'                    => 'float',
    'type_installation_id'          => 'int',
    'condition_vetuste_id'          => 'int',
    'remarque'                      => 'varchar',
);
$validColumns['doc']['inventaires_locaux'] = array(
    'inventaire_id'                => 'int',
    'nom_local'               => 'varchar',
    'numero_local'               => 'varchar',
);
$validColumns['doc']['inventaires_locaux_champs'] = array(
    'nom'               => 'varchar',
);
$validColumns['doc']['logs_imports_inventaires_lignes'] = array(
	'nom'               => 'varchar'
);

$validColumns['doc']['procedures_inspections_planifiees'] = array(
											'discipline_id'         	=> 'int',
											'procedure_inspection_id'	=> 'int',
											'usager_attribue_id'   		=> 'int',
							);

$validColumns['doc']['procedures_inspections_planifiees_gbm'] = array(
    'numero'         	=> 'varchar',
    'description'         	=> 'varchar',
    'date_debut'         	=> 'varchar',
    'date_fin'         	=> 'varchar',
    'commentaire'         	=> 'varchar',
    'procedure_inspection_id'	=> 'int',
    'responsable_planification_id'   		=> 'int',
);
$validColumns['doc']['procedures_mises_en_service_planifiees_mep'] = array(
    'numero'         	=> 'varchar',
    'description'         	=> 'varchar',
    'date_debut'         	=> 'varchar',
    'date_fin'         	=> 'varchar',
    'commentaire'         	=> 'varchar',
    'procedure_inspection_id'	=> 'int',
    'responsable_planification_id'   		=> 'int',
);

$validColumns['doc']['procedures_inspections_planifiees_gbm_equipements'] = array(
    'modification_usager_id'    => 'varchar',
    'ts_modification'           => 'varchar',
);

$validColumns['doc']['procedures_mises_en_service_planifiees_mep_equipements'] = array(
    'modification_usager_id'    => 'varchar',
    'ts_modification'           => 'varchar',
);

$validColumns['doc']['procedures_surveillances_planifiees'] = array(
											'discipline_id'         	=> 'int',
											'procedure_inspection_id'	=> 'int',
											'usager_attribue_id'    	=> 'int',
							);

$validColumns['doc']['procedures_inspections_planifiees_fiches'] = array(
											'modification_usager_id'    => 'varchar',
											'ts_modification'           => 'varchar',
							);

$validColumns['doc']['procedures_surveillances_planifiees_fiches'] = array(
											'modification_usager_id'    => 'varchar',
											'ts_modification'           => 'varchar',
							);

$validColumns['doc']['questions'] = array(
											'code'         				=> 'varchar',
											'question_categorie_id'		=> 'int',
											'sujet'    					=> 'varchar',
											'question'    				=> 'varchar',
											'question_statut_id' 		=> 'int',
											'is_prioritaire' 			=> 'int',
//											'nom_table'    				=> 'varchar',
											'liste_item_id' 			=> 'int',
//											'classe_formulaire'    		=> 'varchar',
											'discipline_id'    			=> 'int',
											'fiche_id'    				=> 'int',
											'reponse_nb_jours_max' 		=> 'int',
							);













// Types surs ********************************************************************************






$validColumns['doc']['conditions_vetustes_types'] = array(
											'nom' 			=> 'varchar',
											'nom_en' 		=> 'varchar',
											'is_defaut' 	=> 'int',
							);

$validColumns['doc']['outillages_types'] = array(
											'nom' 			=> 'varchar',
											'nom_en' 		=> 'varchar',
											'is_defaut' 	=> 'int',
							);

$validColumns['doc']['plomberies_divers_types'] = array(
											'nom' 			=> 'varchar',
											'nom_en' 		=> 'varchar',
											'is_defaut' 	=> 'int',
							);

$validColumns['doc']['pressurisations_types'] = array(
											'nom' 			=> 'varchar',
											'nom_en' 		=> 'varchar',
											'is_defaut' 	=> 'int',
							);

$validColumns['doc']['prises_electriques_types'] = array(
											'nom' 			=> 'varchar',
											'nom_en' 		=> 'varchar',
											'is_defaut' 	=> 'int',
							);

$validColumns['doc']['conduits_types'] = array(
											'nom' 								=> 'varchar',
											'diametre_conduit_electricite_id' 	=> 'int',
											'is_defaut' 						=> 'int',
							);

$validColumns['doc']['raccords_plomberies_types'] = array(
											'nom' 			=> 'varchar',
											'nom_en' 		=> 'varchar',
											'is_defaut' 	=> 'int',
							);

$validColumns['doc']['raccords_plomberies_formats_types'] = array(
											'nom' 			=> 'varchar',
											'nom_en' 		=> 'varchar',
											'is_defaut' 	=> 'int',
							);

$validColumns['doc']['raccords_electriques_types'] = array(
											'nom' 			=> 'varchar',
											'nom_en' 		=> 'varchar',
											'is_defaut' 	=> 'int',
							);

$validColumns['doc']['mobiliers_integres_types'] = array(
											'nom' 			=> 'varchar',
											'nom_en' 		=> 'varchar',
											'is_defaut' 	=> 'int',
							);

$validColumns['doc']['mobiliers_laboratoires_types'] = array(
											'nom' 			=> 'varchar',
											'nom_en' 		=> 'varchar',
											'is_defaut' 	=> 'int',
							);

$validColumns['doc']['mobiliers_mobiles_types'] = array(
											'nom' 			=> 'varchar',
											'nom_en' 		=> 'varchar',
											'is_defaut' 	=> 'int',
							);

$validColumns['doc']['gaz_medicaux_types'] = array(
											'nom' 			=> 'varchar',
											'nom_en' 		=> 'varchar',
											'is_defaut' 	=> 'int',
							);
$validColumns['doc']['horloges_types'] = array(
											'nom' 			=> 'varchar',
											'nom_en' 		=> 'varchar',
											'is_defaut' 	=> 'int',
							);

$validColumns['doc']['lavabos_types'] = array(
											'nom' 			=> 'varchar',
											'nom_en' 		=> 'varchar',
											'is_defaut' 	=> 'int',
							);

$validColumns['doc']['cuves_types'] = array(
											'nom' 			=> 'varchar',
											'nom_en' 		=> 'varchar',
											'is_defaut' 	=> 'int',
							);

$validColumns['doc']['fontaines_refrigerees_types'] = array(
											'nom' 			=> 'varchar',
											'nom_en' 		=> 'varchar',
											'is_defaut' 	=> 'int',
							);

$validColumns['doc']['plomberies_securites_urgences_types'] = array(
											'nom' 			=> 'varchar',
											'nom_en' 		=> 'varchar',
											'is_defaut' 	=> 'int',
							);

$validColumns['doc']['urinoirs_types'] = array(
											'nom' 			=> 'varchar',
											'nom_en' 		=> 'varchar',
											'is_defaut' 	=> 'int',
							);

$validColumns['doc']['fusibles_actions_types'] = array(
											'nom' 			=> 'varchar',
											'nom_en' 		=> 'varchar',
											'is_defaut' 	=> 'int',
							);

$validColumns['doc']['fusibles_formats_types'] = array(
											'nom' 			=> 'varchar',
											'nom_en' 		=> 'varchar',
											'is_defaut' 	=> 'int',
							);

$validColumns['doc']['composantes_electriques_types'] = array(
											'nom' 			=> 'varchar',
											'nom_en' 		=> 'varchar',
											'is_defaut' 	=> 'int',
							);

$validColumns['doc']['hp_types'] = array(
											'nom' 										=> 'varchar',
											'kw' 										=> 'float',
											'courant' 									=> 'float',
											'tension_type_id' 							=> 'int',
											'phasage_type_id' 							=> 'int',
											'nombre_phase_type_id' 						=> 'int',
											'calibre_protection_fusible' 				=> 'int',
											'calibre_protection_type_disjoncteur_id' 	=> 'int',
											'calibre_cable_type_cuivre_id' 				=> 'int',
											'calibre_cable_type_aluminium_id' 			=> 'int',
											'calibre_cable_type_malm_cuivre_id' 		=> 'int',
											'calibre_cable_type_malm_aluminium_id' 		=> 'int',
											'conduit_type_cuivre_id' 					=> 'int',
											'conduit_type_aluminium_id' 				=> 'int',
											'ajustement_relais' 						=> 'int',
											'is_defaut' 								=> 'int',
							);

$validColumns['doc']['cables_types'] = array(
											'nom' 			=> 'varchar',
											'is_defaut' 	=> 'int',
							);

$validColumns['doc']['elements_types'] = array(
											'nom' 				=> 'varchar',
											'nom_en' 				=> 'varchar',
							);

$validColumns['doc']['eclairages_interieurs_types'] = array(
											'nom' 					=> 'varchar',
											'nom_en' 				=> 'varchar',
											'is_defaut' 			=> 'int',
							);

$validColumns['doc']['eclairages_exterieurs_types'] = array(
											'nom' 					=> 'varchar',
											'nom_en' 				=> 'varchar',
											'is_defaut' 			=> 'int',
							);

$validColumns['doc']['eclairages_stationnements_types'] = array(
											'nom' 					=> 'varchar',
											'nom_en' 				=> 'varchar',
											'is_defaut' 			=> 'int',
							);

$validColumns['doc']['eclairages_routiers_types'] = array(
											'nom' 					=> 'varchar',
											'nom_en' 				=> 'varchar',
											'is_defaut' 			=> 'int',
							);

$validColumns['doc']['eclairages_parcs_types'] = array(
											'nom' 					=> 'varchar',
											'nom_en' 				=> 'varchar',
											'is_defaut' 			=> 'int',
							);

$validColumns['doc']['raccords_directs_types'] = array(
											'nom' 					=> 'varchar',
											'nom_en' 				=> 'varchar',
											'is_defaut' 			=> 'int',
							);

$validColumns['doc']['equipements_paratonnerres_types'] = array(
											'nom' 			=> 'varchar',
											'nom_en' 		=> 'varchar',
											'is_defaut' 	=> 'int',
							);

$validColumns['doc']['equipements_postes_peages_types'] = array(
											'nom' 				=> 'varchar',
											'nom_en' 			=> 'varchar',
											'is_defaut' 		=> 'int',
							);

$validColumns['doc']['equipements_ups_types'] = array(
											'nom' 				=> 'varchar',
											'nom_en' 			=> 'varchar',
											'is_defaut' 		=> 'int',
							);

$validColumns['doc']['equipements_systemes_intrusions_types'] = array(
											'nom' 				=> 'varchar',
											'nom_en' 			=> 'varchar',
											'is_defaut' 		=> 'int',
							);

$validColumns['doc']['equipements_controles_acces_types'] = array(
											'nom' 				=> 'varchar',
											'nom_en' 			=> 'varchar',
											'is_defaut' 		=> 'int',
							);

$validColumns['doc']['equipements_systemes_ventilations_types'] = array(
											'nom' 				=> 'varchar',
											'nom_en' 			=> 'varchar',
											'is_defaut' 		=> 'int',
							);

$validColumns['doc']['equipements_alarmes_incendies_types'] = array(
											'nom' 				=> 'varchar',
											'nom_en' 			=> 'varchar',
											'is_defaut' 		=> 'int',
							);

$validColumns['doc']['phasages_types'] = array(
											'nom' 				=> 'varchar',
											'nom_en' 			=> 'varchar',
											'is_defaut' 		=> 'int',
							);

$validColumns['doc']['envois_receptions_types'] = array(
											'nom' 				=> 'varchar',
											'nom_en' 			=> 'varchar',
											'is_defaut' 		=> 'int',
							);

$validColumns['doc']['appels_gardes_types'] = array(
											'nom' 					=> 'varchar',
											'nom_en' 				=> 'varchar',
											'is_defaut' 			=> 'int',
							);

$validColumns['doc']['appels_urgences_types'] = array(
											'nom' 					=> 'varchar',
											'nom_en' 				=> 'varchar',
											'is_defaut' 			=> 'int',
							);

$validColumns['doc']['equipements_medicaux_specialises_types'] = array(
											'nom' 							=> 'varchar',
											'nom_en' 						=> 'varchar',
											'is_defaut' 					=> 'int',
							);

$validColumns['doc']['equipements_multimedias_types'] = array(
											'nom' 					=> 'varchar',
											'nom_en' 				=> 'varchar',
											'is_defaut' 			=> 'int',
							);

$validColumns['doc']['eviers_types'] = array(
											'nom' 					=> 'varchar',
											'nom_en' 				=> 'varchar',
											'is_defaut' 			=> 'int',
							);

$validColumns['doc']['bains_types'] = array(
											'nom' 						=> 'varchar',
											'nom_en' 					=> 'varchar',
											'is_defaut' 				=> 'int',
							);
$validColumns['doc']['procedures_inspections_planifiees_responsables_types'] = array(
    'nom' 						=> 'varchar',
    'nom_en' 					=> 'varchar',
    'is_defaut' 				=> 'int',
);

$validColumns['doc']['gestions_portes_types'] = array(
    'nom' 						    => 'varchar',
    'nom_en' 					    => 'varchar',
    'hauteur' 					    => 'float',
    'largeur' 					    => 'float',
    'largeur_porte_a' 			    => 'float',
    'largeur_porte_b' 			    => 'float',
    'porte_materiel_id' 		    => 'int',
    'porte_vitrage_type_id' 	    => 'int',
    'porte_vitrage_pellicule_id' 	=> 'int',
    'fini_cadrage_id' 				=> 'int',
    'vitrage_cadrage_type_id' 		=> 'int',
    'persienne_porte_id' 			=> 'int',
    'is_defaut' 				    => 'int',
							);

$validColumns['doc']['gestions_portes_quincailleries_types'] = array(
											'nom' 						=> 'varchar',
											'nom_en' 					=> 'varchar',
											'is_defaut' 				=> 'int',
							);

$validColumns['doc']['evacuations_types'] = array(
											'nom' 						=> 'varchar',
											'nom_en' 					=> 'varchar',
											'is_defaut' 				=> 'int',
							);

$validColumns['doc']['habillages_fenetres_types'] = array(
											'nom' 						=> 'varchar',
											'nom_en' 					=> 'varchar',
											'is_defaut' 				=> 'int',
							);

$validColumns['doc']['murs_types'] = array(
											'nom' 						=> 'varchar',
											'nom_en' 					=> 'varchar',
											'is_defaut' 				=> 'int',
							);

$validColumns['doc']['planchers_types'] = array(
											'nom' 						=> 'varchar',
											'nom_en' 					=> 'varchar',
											'is_defaut' 				=> 'int',
							);

$validColumns['doc']['fenetres_types'] = array(
											'nom' 						=> 'varchar',
											'nom_en' 					=> 'varchar',
											'is_defaut' 				=> 'int',
							);

$validColumns['doc']['plafonds_types'] = array(
											'nom' 						=> 'varchar',
											'nom_en' 					=> 'varchar',
											'is_defaut' 				=> 'int',
							);

$validColumns['doc']['portes_types'] = array(
											'nom' 						=> 'varchar',
											'nom_en' 					=> 'varchar',
											'is_defaut' 				=> 'int',
							);

$validColumns['doc']['protecteurs_murals_types'] = array(
											'nom' 						=> 'varchar',
											'nom_en' 					=> 'varchar',
											'is_defaut' 				=> 'int',
							);

$validColumns['doc']['interieurs_exterieurs_types'] = array(
											'nom' 						=> 'varchar',
											'nom_en' 					=> 'varchar',
											'is_defaut' 				=> 'int',
							);

$validColumns['doc']['routes_types'] = array(
											'nom' 						=> 'varchar',
											'nom_en' 					=> 'varchar',
											'is_defaut' 				=> 'int',
							);

$validColumns['doc']['douches_types'] = array(
											'nom' 						=> 'varchar',
											'nom_en' 					=> 'varchar',
											'is_defaut' 				=> 'int',
							);

$validColumns['doc']['cameras_securites_types'] = array(
											'nom' 						=> 'varchar',
											'nom_en' 					=> 'varchar',
											'is_defaut' 				=> 'int',
							);

$validColumns['doc']['prises_communications_sorties_types'] = array(
											'nom' 						=> 'varchar',
											'nom_en' 					=> 'varchar',
											'is_defaut' 				=> 'int',
							);

$validColumns['doc']['evacuations_speciales_types'] = array(
											'nom' 						=> 'varchar',
											'nom_en' 					=> 'varchar',
											'is_defaut' 				=> 'int',
							);

$validColumns['doc']['eaux_purifies_types'] = array(
											'nom' 						=> 'varchar',
											'nom_en' 					=> 'varchar',
											'is_defaut' 				=> 'int',
							);

$validColumns['doc']['eclairages_controles_types'] = array(
											'nom' 					=> 'varchar',
											'nom_en' 				=> 'varchar',
											'is_defaut' 			=> 'int',
							);

$validColumns['doc']['eclairages_lumens_types'] = array(
											'nom' 					=> 'varchar',
											'nom_en' 				=> 'varchar',
											'is_defaut' 			=> 'int',
							);

$validColumns['doc']['unites_types'] = array(
											'nom' 				=> 'varchar',
							);

$validColumns['doc']['fiches_types'] = array(
											'nom_fr' 				=> 'varchar',
											'nom_en' 				=> 'varchar',
											'description'       	=> 'varchar',
											'formulaire_general'	=> 'varchar',
							);

$validColumns['doc']['fiches_sous_types'] = array(
											'fiche_type_id'     	=> 'int',
											'nom_fr' 				=> 'varchar',
											'nom_en' 				=> 'varchar',
											'description'       	=> 'varchar',
											'formulaire_general'	=> 'varchar',
											'discipline_id'     	=> 'int',
                                            'is_defaut' 	=> 'int',
							);

$validColumns['doc']['exports_automatiques'] = array(
    'projet_id'     => 'int',
    'nom' 			=> 'varchar',
    'module' 		=> 'varchar',
    'modele_id'     => 'int',
);

$validColumns['doc']['grilles_diffuseurs_materiaux_types'] = array(
											'nom' 			=> 'varchar',
											'nom_en' 		=> 'varchar',
											'is_defaut' 	=> 'int',
                            );

$validColumns['doc']['serpentins_fonctions_types'] = array(
											'nom' 			=> 'varchar',
											'nom_en' 		=> 'varchar',
											'is_defaut' 	=> 'int',
                            );

$validColumns['doc']['grilles_diffuseurs_garnitures_types'] = array(
											'nom' 			=> 'varchar',
											'nom_en' 		=> 'varchar',
											'is_defaut' 	=> 'int',
                            );

$validColumns['doc']['grilles_diffuseurs_finitions_types'] = array(
											'nom' 			=> 'varchar',
											'nom_en' 		=> 'varchar',
											'is_defaut' 	=> 'int',
                            );

$validColumns['doc']['amperages_types'] = array(
											'nom' 			=> 'varchar',
							);

$validColumns['doc']['fusibles_calibres_types'] = array(
											'nom' 			=> 'varchar',
							);

$validColumns['doc']['appareils_chauffages_types'] = array(
											'nom' 			=> 'varchar',
											'nom_en' 		=> 'varchar',
							);

$validColumns['doc']['signaux_types'] = array(
											'nom' 			=> 'varchar',
											'nom_en' 		=> 'varchar',
							);

$validColumns['doc']['cables_calibres_types'] = array(
											'nom' 				=> 'varchar',
											'courant' 			=> 'int',
											'nombre_phases_id' 	=> 'int',
							);

$validColumns['doc']['calibres_protections_types'] = array(
											'nom' 					=> 'varchar',
											'nombre_poles' 			=> 'varchar',
											'nombre_phase_type_id' 	=> 'int',
							);

$validColumns['doc']['capacites_ruptures_types'] = array(
											'nom' 			=> 'varchar',
							);

$validColumns['doc']['fluides_types'] = array(
											'nom' 			=> 'varchar',
											'nom_en' 		=> 'varchar',
											'is_defaut' 	=> 'int',
                            );

$validColumns['doc']['compresseurs_types'] = array(
											'nom' 			=> 'varchar',
											'nom_en' 		=> 'varchar',
											'is_defaut' 	=> 'int',
                            );

$validColumns['doc']['coolants_types'] = array(
											'nom' 			=> 'varchar',
											'nom_en' 		=> 'varchar',
											'is_defaut' 	=> 'int',
                            );

$validColumns['doc']['uta_classifications_types'] = array(
											'nom' 			=> 'varchar',
											'nom_en' 		=> 'varchar',
											'is_defaut' 	=> 'int',
                            );

$validColumns['doc']['moteurs_chassis_types'] = array(
											'nom' 			=> 'varchar',
											'nom_en' 		=> 'varchar',
											'is_defaut' 	=> 'int',
                            );

$validColumns['doc']['systemes_types'] = array(
											'nom' 			=> 'varchar',
											'nom_en' 		=> 'varchar',
											'is_defaut' 	=> 'int',
                            );

$validColumns['doc']['roues_ventilateurs_types'] = array(
											'nom' 			=> 'varchar',
											'nom_en' 		=> 'varchar',
											'is_defaut' 	=> 'int',
                            );

$validColumns['doc']['installations_disjoncteurs_types'] = array(
											'nom' 			=> 'varchar',
											'nom_en' 		=> 'varchar',
							);

$validColumns['doc']['equipements_electriques_types'] = array(
											'nom' 			=> 'varchar',
											'nom_en' 		=> 'varchar',
							);


$validColumns['doc']['kva_types'] = array(
											'nom' 										=> 'varchar',
											'phasage_type_id' 							=> 'int',
											'nombre_phase_fil_primaire_id' 				=> 'int',
											'nombre_phase_fil_secondaire_id' 			=> 'int',
											'tension_type_primaire_id' 					=> 'int',
											'tension_type_secondaire_id' 				=> 'int',
											'calibre_protection_type_disjoncteur_id' 	=> 'int',
                                            'calibre_protection_fusible' 				=> 'varchar',
											'calibre_cable_type_cuivre_id' 				=> 'int',
											'calibre_cable_type_aluminium_id' 			=> 'int',
                                            'calibre_cable_type_malm_cuivre_id' 		=> 'int',
											'calibre_cable_type_malm_aluminium_id' 		=> 'int',
											'calibre_cable_type_malt_cuivre_id' 		=> 'int',
											'calibre_cable_type_malt_aluminium_id' 		=> 'int',
											'conduit_type_cuivre_id' 					=> 'int',
											'conduit_type_aluminium_id' 				=> 'int',
											'conduit_type_malt_cuivre_id' 				=> 'int',
											'conduit_type_malt_aluminium_id' 			=> 'int',
							);

$validColumns['doc']['montages_panneaux_types'] = array(
											'nom' 			=> 'varchar',
											'nom_en' 		=> 'varchar',
							);

$validColumns['doc']['nombres_espaces_panneaux_types'] = array(
											'nom' 			=> 'varchar',
							);

$validColumns['doc']['nombres_phases_types'] = array(
											'nom' 			=> 'varchar',
											'nom_en' 		=> 'varchar',
							);

$validColumns['doc']['tensions_types'] = array(
											'nom' 						=> 'varchar',
											'nombre_phases_panneau_id' 	=> 'int',
							);

$validColumns['doc']['transformateurs_types'] = array(
											'nom' 			=> 'varchar',
											'nom_en' 		=> 'varchar',
							);

$validColumns['doc']['materiaux_types'] = array(
											'nom' 			=> 'varchar',
											'nom_en' 		=> 'varchar',
							);

$validColumns['doc']['boitiers_types'] = array(
											'nom' 			=> 'varchar',
											'nom_en' 		=> 'varchar',
							);

$validColumns['doc']['compacition_types'] = array(
											'nom' 			=> 'varchar',
											'nom_en' 		=> 'varchar',
											'is_defaut' 	=> 'int',
                            );
$validColumns['doc']['cadences_types'] = array(
											'nom' 			=> 'varchar',
											'nom_en' 		=> 'varchar',
											'is_defaut' 	=> 'int',
                            );

$validColumns['doc']['pompes_types'] = array(
											'nom' 			=> 'varchar',
											'nom_en' 		=> 'varchar',
											'is_defaut' 	=> 'int',
                            );

$validColumns['doc']['valves_types'] = array(
											'nom' 			=> 'varchar',
											'nom_en' 		=> 'varchar',
											'is_defaut' 	=> 'int',
                            );

$validColumns['doc']['tour_eau_types'] = array(
											'nom' 			=> 'varchar',
											'nom_en' 		=> 'varchar',
											'is_defaut' 	=> 'int',
                            );

$validColumns['doc']['diffuseur_types'] = array(
											'nom' 			=> 'varchar',
											'nom_en' 		=> 'varchar',
											'is_defaut' 	=> 'int',
                            );

$validColumns['doc']['accessoires_sanitaires_ensembles_types'] = array(
											'nom' 							=> 'varchar',
											'nom_en' 						=> 'varchar',
											'is_defaut' 					=> 'int',
							);

$validColumns['doc']['accessoires_sanitaires_unites_types'] = array(
											'nom' 							=> 'varchar',
											'nom_en' 						=> 'varchar',
											'is_defaut' 					=> 'int',
							);

$validColumns['doc']['alimentations_electriques_types'] = array(
											'nom' 							=> 'varchar',
											'nom_en' 						=> 'varchar',
											'is_defaut' 					=> 'int',
							);

$validColumns['doc']['priorites_acquisitions_types'] = array(
    										'nom' 							=> 'varchar',
											'nom_en' 						=> 'varchar',
											'is_defaut' 					=> 'int',
							);

$validColumns['doc']['priorites_financements_types'] = array(
    										'nom' 							=> 'varchar',
											'nom_en' 						=> 'varchar',
											'is_defaut' 					=> 'int',
							);

$validColumns['doc']['modes_financements_types'] = array(
											'nom' 							=> 'varchar',
											'nom_en' 						=> 'varchar',
											'is_defaut' 					=> 'int',
							);

$validColumns['doc']['modes_acquisitions_types'] = array(
											'nom' 							=> 'varchar',
											'nom_en' 						=> 'varchar',
											'is_defaut' 					=> 'int',
							);

$validColumns['doc']['portees_appels_offres_types'] = array(
											'nom' 							=> 'varchar',
											'nom_en' 						=> 'varchar',
											'is_defaut' 					=> 'int',
							);

$validColumns['doc']['appels_locaux_generaux_equipements_types'] = array(
											'nom' 							=> 'varchar',
											'nom_en' 						=> 'varchar',
											'is_defaut' 					=> 'int',
							);

$validColumns['doc']['appels_locaux_generaux_types'] = array(
											'nom' 						=> 'varchar',
											'nom_en' 					=> 'varchar',
											'is_defaut' 				=> 'int',
							);

$validColumns['doc']['dispositifs_electriques_champs_types'] = array(
											'nom' 				=> 'varchar',
											'nom_en' 			=> 'varchar',
											'is_defaut' 		=> 'int',
							);

$validColumns['doc']['demarreurs_types'] = array(
											'nom' 				=> 'varchar',
											'nom_en' 			=> 'varchar',
											'is_defaut' 		=> 'int',
							);

$validColumns['doc']['charges_types'] = array(
											'nom' 				=> 'varchar',
											'nom_en' 			=> 'varchar',
											'is_defaut' 		=> 'int',
							);

$validColumns['doc']['demarreurs_compositions_types'] = array(
											'nom' 				=> 'varchar',
											'nom_en' 			=> 'varchar',
											'is_defaut' 		=> 'int',
							);

$validColumns['doc']['toilettes_types'] = array(
											'nom' 			=> 'varchar',
											'nom_en' 		=> 'varchar',
											'is_defaut' 	=> 'int',
							);

$validColumns['doc']['verres_interieurs_types'] = array(
											'nom' 			=> 'varchar',
											'nom_en' 		=> 'varchar',
											'is_defaut' 	=> 'int',
							);

$validColumns['doc']['verres_exterieurs_types'] = array(
											'nom' 			=> 'varchar',
											'nom_en' 		=> 'varchar',
											'is_defaut' 	=> 'int',
							);

$validColumns['doc']['revetements_murals_types'] = array(
											'nom' 			=> 'varchar',
											'nom_en' 		=> 'varchar',
											'is_defaut' 	=> 'int',
							);

$validColumns['doc']['indices_transmissions_sons_types'] = array(
											'nom' 			=> 'varchar',
											'nom_en' 		=> 'varchar',
											'is_defaut' 	=> 'int',
							);

$validColumns['doc']['csa_types'] = array(
											'nom' 			=> 'varchar',
											'is_defaut' 	=> 'int',
							);

$validColumns['doc']['csa_autres_types'] = array(
											'nom' 			=> 'varchar',
											'is_defaut' 	=> 'int',
							);

$validColumns['doc']['csa_electricites_types'] = array(
											'nom' 			=> 'varchar',
											'is_defaut' 	=> 'int',
							);

$validColumns['doc']['redondances_types'] = array(
											'nom' 			=> 'varchar',
											'nom_en' 		=> 'varchar',
											'is_defaut' 	=> 'int',
							);

$validColumns['doc']['filtrations_types'] = array(
											'nom' 			=> 'varchar',
											'nom_en' 		=> 'varchar',
											'is_defaut' 	=> 'int',
							);

$validColumns['doc']['soins_types'] = array(
											'nom' 			=> 'varchar',
											'nom_en' 		=> 'varchar',
											'is_defaut' 	=> 'int',
							);

$validColumns['doc']['finis_types'] = array(
											'nom' 			=> 'varchar',
											'nom_en' 		=> 'varchar',
											'is_defaut' 	=> 'int',
							);

$validColumns['doc']['hottes_types'] = array(
											'nom' 			=> 'varchar',
											'nom_en' 		=> 'varchar',
											'is_defaut' 	=> 'int',
							);

$validColumns['doc']['installations_types'] = array(
											'nom' 			=> 'varchar',
											'nom_en' 		=> 'varchar',
											'is_defaut' 	=> 'int',
							);

$validColumns['doc']['equipements_bio_medicaux_types'] = array(
											'nom'                       									=> 'varchar',
											'description'               									=> 'varchar',
											'description_en'               									=> 'varchar',
											'phase_incidence_id' 											=> 'int',
                                            'livraison_date'                                                => 'date',
											'equipement_bio_medicaux_type_categorie_id'						=> 'int',
											'equipements_bio_medicaux_types_categorie_responsabilite_id' 	=> 'int',
											'equipements_bio_medicaux_types_direction_responsable_id' 		=> 'int',
											'equipements_bio_medicaux_types_responsable_logistique_1_id' 	=> 'int',
											'equipements_bio_medicaux_types_responsable_logistique_2_id' 	=> 'int',
											'equipements_bio_medicaux_types_responsable_logistique_3_id' 	=> 'int',
											'equipements_bio_medicaux_types_responsable_logistique_4_id' 	=> 'int',
											'equipements_bio_medicaux_types_responsable_logistique_5_id' 	=> 'int',
											'equipements_bio_medicaux_types_responsable_logistique_6_id' 	=> 'int',
											'equipements_bio_medicaux_types_sous_categorie_id' 				=> 'int',
											'prix_unitaire_nouveau' 						=> 'float',
											'prix_unitaire_nouveau_date' 					=> 'date',
											'prix_unitaire_nouveau_source' 					=> 'varchar',
											'prix_unitaire_precedant' 						=> 'float',
											'prix_unitaire_precedant_date' 					=> 'date',
                                            'prix_unitaire_autre_1'                         => 'float',
											'classification_budgetaire_type_id'        		=> 'int',
											'taxable'        		                    	=> 'int',
											'commentaires_couts'              		 		=> 'varchar',
											'referentiel_code_actif_plus'              		 		=> 'varchar',
											'referentiel_code_equipement'              		 		=> 'varchar',
											'is_defaut'                 					=> 'int',
							);

$validColumns['doc']['classifications_budgetaires_types'] = array(
											'nom' 			=> 'varchar',
							);

$validColumns['doc']['equipements_bio_medicaux_types_categories'] = array(
											'nom'                       	=> 'varchar',
											'is_defaut' 					=> 'int',
							);

$validColumns['doc']['fiches_statuts'] = array(
	'nom'                       	=> 'varchar',
	'is_defaut' 					=> 'int',
);

$validColumns['doc']['accessoires_architecturaux_types'] = array(
											'nom' 							=> 'varchar',
											'nom_en' 						=> 'varchar',
											'is_defaut' 					=> 'int',
							);

$validColumns['doc']['uni_cloisons_types'] = array(
											'nom' 			=> 'varchar',
											'nom_en' 		=> 'varchar',
											'is_defaut' 	=> 'int',
							);

$validColumns['doc']['uni_portes_types'] = array(
											'nom' 			=> 'varchar',
											'nom_en' 		=> 'varchar',
											'is_defaut' 	=> 'int',
							);

$validColumns['doc']['capacites_portantes_types'] = array(
											'nom' 			=> 'varchar',
											'nom_en' 		=> 'varchar',
											'is_defaut' 	=> 'int',
							);

$validColumns['doc']['uni_exigences_fonctionnelles_types'] = array(
											'nom' 			=> 'varchar',
											'nom_en' 		=> 'varchar',
											'is_defaut' 	=> 'int',
							);

$validColumns['doc']['planeites_types'] = array(
											'nom' 			=> 'varchar',
											'nom_en' 		=> 'varchar',
											'is_defaut' 	=> 'int',
							);

$validColumns['doc']['tolerances_mouvements_types'] = array(
											'nom' 			=> 'varchar',
											'nom_en' 		=> 'varchar',
											'is_defaut' 	=> 'int',
							);

$validColumns['doc']['vibrations_types'] = array(
											'nom' 			=> 'varchar',
											'nom_en' 		=> 'varchar',
											'is_defaut' 	=> 'int',
							);

$validColumns['doc']['espaces_types'] = array(
											'nom' 			=> 'varchar',
											'nom_en' 		=> 'varchar',
											'is_defaut' 	=> 'int',
							);

$validColumns['doc']['uni_finitions_murs_types'] = array(
											'nom' 			=> 'varchar',
											'nom_en' 		=> 'varchar',
											'is_defaut' 	=> 'int',
							);

$validColumns['doc']['uni_finitions_planchers_types'] = array(
											'nom' 			=> 'varchar',
											'nom_en' 		=> 'varchar',
											'is_defaut' 	=> 'int',
							);

$validColumns['doc']['uni_finitions_plinthes_types'] = array(
											'nom' 			=> 'varchar',
											'nom_en' 		=> 'varchar',
											'is_defaut' 	=> 'int',
							);

$validColumns['doc']['uni_finitions_plafonds_types'] = array(
											'nom' 			=> 'varchar',
											'nom_en' 		=> 'varchar',
											'is_defaut' 	=> 'int',
							);

$validColumns['doc']['uni_accessoires_protections_incendies_types'] = array(
											'nom' 			=> 'varchar',
											'nom_en' 		=> 'varchar',
											'is_defaut' 	=> 'int',
							);

$validColumns['doc']['uni_accessoires_integres_types'] = array(
											'nom' 			=> 'varchar',
											'nom_en' 		=> 'varchar',
											'is_defaut' 	=> 'int',
							);

$validColumns['doc']['uni_ameublements_decorations_types'] = array(
											'nom' 			=> 'varchar',
											'nom_en' 		=> 'varchar',
											'is_defaut' 	=> 'int',
							);

$validColumns['doc']['uni_appareils_plomberies_types'] = array(
											'nom' 			=> 'varchar',
											'nom_en' 		=> 'varchar',
											'is_defaut' 	=> 'int',
							);

$validColumns['doc']['uni_autres_systemes_plomberies_types'] = array(
											'nom' 			=> 'varchar',
											'nom_en' 		=> 'varchar',
											'is_defaut' 	=> 'int',
							);

$validColumns['doc']['uni_communications_informatiques_types'] = array(
											'nom' 			=> 'varchar',
											'nom_en' 		=> 'varchar',
											'is_defaut' 	=> 'int',
							);

$validColumns['doc']['uni_distributions_cvcas_types'] = array(
											'nom' 			=> 'varchar',
											'nom_en' 		=> 'varchar',
											'is_defaut' 	=> 'int',
							);

$validColumns['doc']['uni_distributions_secondaires_types'] = array(
											'nom' 			=> 'varchar',
											'nom_en' 		=> 'varchar',
											'is_defaut' 	=> 'int',
							);

$validColumns['doc']['uni_eclairages_types'] = array(
											'nom' 			=> 'varchar',
											'nom_en' 		=> 'varchar',
											'is_defaut' 	=> 'int',
							);

$validColumns['doc']['uni_equipements_types'] = array(
											'nom' 			=> 'varchar',
											'nom_en' 		=> 'varchar',
											'is_defaut' 	=> 'int',
							);

$validColumns['doc']['uni_gicleurs_types'] = array(
											'nom' 			=> 'varchar',
											'nom_en' 		=> 'varchar',
											'is_defaut' 	=> 'int',
							);

$validColumns['doc']['uni_securites_types'] = array(
											'nom' 			=> 'varchar',
											'nom_en' 		=> 'varchar',
											'is_defaut' 	=> 'int',
							);

$validColumns['doc']['uni_reglementations_types'] = array(
											'nom' 			=> 'varchar',
											'nom_en' 		=> 'varchar',
											'is_defaut' 	=> 'int',
							);

$validColumns['doc']['sites_livraisons_types'] = array(
											'nom' 			=> 'varchar',
											'nom_en' 		=> 'varchar',
											'is_defaut' 	=> 'int',
							);

$validColumns['doc']['locals_transitoires_types'] = array(
											'nom' 			=> 'varchar',
											'nom_en' 		=> 'varchar',
											'is_defaut' 	=> 'int',
							);

$validColumns['doc']['conversions_unites_types'] = array(
											'nom' 			=> 'varchar',
											'nom_en' 		=> 'varchar',
											'is_defaut' 	=> 'int',
							);

$validColumns['doc']['formulaires_types'] = array(
											'nom' 			=> 'varchar',
											'nom_en' 		=> 'varchar',
											'is_defaut' 	=> 'int',
							);

$validColumns['doc']['bloquants_types'] = array(
											'nom' 			=> 'varchar',
											'nom_en' 		=> 'varchar',
											'is_defaut' 	=> 'int',
							);

$validColumns['doc']['bloquants_financements_types'] = array(
											'nom' 						=> 'varchar',
											'nom_en' 					=> 'varchar',
											'is_defaut' 				=> 'int',
							);

$validColumns['doc']['financements_statuts_internes_types'] = array(
											'nom' 						=> 'varchar',
											'nom_en' 					=> 'varchar',
											'is_defaut' 				=> 'int',
							);

$validColumns['doc']['financements_statuts_externes_types'] = array(
											'nom' 						=> 'varchar',
											'nom_en' 					=> 'varchar',
											'is_defaut' 				=> 'int',
							);

$validColumns['doc']['acquisitions_sous_volets_responsables_types'] = array(
											'nom' 						=> 'varchar',
											'is_defaut' 				=> 'int',
							);

$validColumns['doc']['acquisitions_phases_incidences_contraignantes_types'] = array(
											'nom' 						=> 'varchar',
											'nom_en' 					=> 'varchar',
											'is_defaut' 				=> 'int',
							);

$validColumns['doc']['acquisitions_projets_priorisations_types'] = array(
											'nom' 						=> 'varchar',
											'nom_en' 					=> 'varchar',
											'is_defaut' 				=> 'int',
							);

$validColumns['doc']['acquisitions_projets_modes_sollicitations_types'] = array(
											'nom' 						=> 'varchar',
											'nom_en' 					=> 'varchar',
											'is_defaut' 				=> 'int',
							);

$validColumns['doc']['acquisitions_projets_modes_adjudications_types'] = array(
											'nom' 						=> 'varchar',
											'nom_en' 					=> 'varchar',
											'is_defaut' 				=> 'int',
							);

$validColumns['doc']['acquisitions_responsables_dossiers_types'] = array(
											'nom' 						=> 'varchar',
											'is_defaut' 				=> 'int',
							);

$validColumns['doc']['acquisitions_responsables_dossiers_a_types'] = array(
											'nom' 						=> 'varchar',
											'is_defaut' 				=> 'int',
							);

$validColumns['doc']['acquisitions_responsables_dossiers_b_types'] = array(
											'nom' 						=> 'varchar',
											'is_defaut' 				=> 'int',
							);

$validColumns['doc']['acquisitions_responsables_dossiers_c_types'] = array(
											'nom' 						=> 'varchar',
											'is_defaut' 				=> 'int',
							);

$validColumns['doc']['acquisitions_responsables_dossiers_d_types'] = array(
											'nom' 						=> 'varchar',
											'is_defaut' 				=> 'int',
							);

$validColumns['doc']['acquisitions_responsables_dossiers_e_types'] = array(
											'nom' 						=> 'varchar',
											'is_defaut' 				=> 'int',
							);

$validColumns['doc']['acquisitions_responsables_dossiers_f_types'] = array(
											'nom' 						=> 'varchar',
											'is_defaut' 				=> 'int',
							);

$validColumns['doc']['acquisitions_responsables_dossiers_g_types'] = array(
											'nom' 						=> 'varchar',
											'is_defaut' 				=> 'int',
							);

$validColumns['doc']['acquisitions_responsables_dossiers_h_types'] = array(
											'nom' 						=> 'varchar',
											'is_defaut' 				=> 'int',
							);

$validColumns['doc']['acquisitions_responsables_dossiers_i_types'] = array(
											'nom' 						=> 'varchar',
											'is_defaut' 				=> 'int',
							);

$validColumns['doc']['acquisitions_responsables_dossiers_j_types'] = array(
											'nom' 						=> 'varchar',
											'is_defaut' 				=> 'int',
							);

$validColumns['doc']['acquisitions_responsables_installations_types'] = array(
											'nom' 						=> 'varchar',
											'is_defaut' 				=> 'int',
							);

$validColumns['doc']['acquisitions_menus_supplementaires_a_types'] = array(
											'nom' 						=> 'varchar',
											'is_defaut' 				=> 'int',
							);

$validColumns['doc']['contrats_responsables_dossiers_types'] = array(
											'nom' 						=> 'varchar',
											'is_defaut' 				=> 'int',
							);

$validColumns['doc']['conges_feries_types'] = array(
											'nom' 						=> 'varchar',
											'nom_en' 		=> 'varchar',
											'date_conge' 				=> 'date',
											'is_defaut' 				=> 'int',
							);

$validColumns['doc']['financements_responsables_dossiers_a_types'] = array(
											'nom' 						=> 'varchar',
											'is_defaut' 				=> 'int',
							);

$validColumns['doc']['financements_responsables_dossiers_b_types'] = array(
											'nom' 						=> 'varchar',
											'is_defaut' 				=> 'int',
							);

$validColumns['doc']['financements_responsables_dossiers_c_types'] = array(
											'nom' 						=> 'varchar',
											'is_defaut' 				=> 'int',
							);

$validColumns['doc']['financements_responsables_dossiers_d_types'] = array(
											'nom' 						=> 'varchar',
											'is_defaut' 				=> 'int',
							);

$validColumns['doc']['financements_responsables_dossiers_e_types'] = array(
											'nom' 						=> 'varchar',
											'is_defaut' 				=> 'int',
							);

$validColumns['doc']['financements_regrouppements_types'] = array(
											'nom' 						=> 'varchar',
											'nom_en' 		=> 'varchar',
											'is_defaut' 				=> 'int',
							);


$validColumns['doc']['proprietes_sources_alimentations'] = array(
											'fiche_id' 					=> 'int',
											'source_alimentation'		=> 'int',
											'numero_casier' 			=> 'varchar',
											'numero_disjoncteur' 		=> 'varchar',
											'puissance' 				=> 'varchar',
											'tension' 					=> 'varchar',
							);

$validColumns['doc']['proprietes_protections_electriques'] = array(
											'fiche_id' 					=> 'int',
											'ajustement_relais'			=> 'int',
											'fusible_nombre'			=> 'int',
											'fusible_capacite'			=> 'int',
											'fusible_action' 			=> 'varchar',
											'fusible_format' 			=> 'varchar',
											'type_source'				=> 'int',
											'type_protection'			=> 'int',
							);

/* $validColumns['doc']['matrices_importations_listes'] = array(
											'nom' 							=> 'varchar',
											'formulaire_id' 				=> 'int',
											'description' 					=> 'varchar',
							);
                            

************************************************************************************************************************* Ajout liste librairie *******************************************************************************************************************************************************************************

Mettre tous les noms des tebles qui représente une liste d'option dans un formulaire

*/

$tables_for_listes = array('phases_incidences', 'equipements_bio_medicaux_types_categories_responsabilite', 'equipements_bio_medicaux_types_directions_responsables', 'equipements_bio_medicaux_types_sous_categories', 'equipements_bio_medicaux_types_responsables_logistiques', 'classifications_budgetaires_types', 'equipements_bio_medicaux_types', 'fiches_statuts', 'equipements_bio_medicaux_types_categories', 'accessoires_architecturaux_types', 'accessoires_sanitaires_ensembles_types', 'accessoires_sanitaires_unites_types', 'alimentations_electriques_types', 'appels_locaux_generaux_equipements_types',
    'appels_locaux_generaux_types', 'appels_locaux_generaux_zones', 'appels_locaux_generaux_blocs', 'appels_locaux_generaux_etages', 'bains_types', 'evacuations_types', 'douches_types', 'cameras_securites_types', 'changements_airs_heures',
    'prises_communications_sorties_types', 'evacuations_speciales_types', 'eaux_purifies_types', 'eclairages_controles_types', 'eclairages_lumens_types', 'eclairages_ratios_urgences', 'eclairages_interieurs_types',
    'eclairages_exterieurs_types', 'eclairages_stationnements_types', 'eclairages_routiers_types', 'eclairages_parcs_types', 'appels_gardes_types', 'appels_urgences_types', 'equipements_medicaux_specialises_types',
    'equipements_multimedias_types', 'eviers_types', 'existants_nouveaux', 'fournis_installes', 'frequentations', 'gaz_medicaux_types', 'horaire_normal_utilisation', 'horloges_types', 'humidites_relatives',
    'lavabos_types', 'mobiliers_integres_types', 'mobiliers_laboratoires_types', 'mobiliers_mobiles_types', 'niveaux_confinements', 'niveaux_bruits', 'vibrations_types', 'niveaux_vibrations_toleres', 'taux_occupations', 'outillages_types',
    'plomberies_divers_types', 'pourcentages', 'presences', 'pressurisations_types', 'prises_electriques_grades', 'prises_electriques_types', 'quantites', 'temperatures', 'toilettes_types', 'csa_types', 'csa_autres_types', 'redondances_types', 'filtrations_types',  'soins_types',
    'finis_types', 'hottes_types', 'installations_types', 'questions_categories', 'equipements_paratonnerres_types', 'equipements_postes_peages_types', 'equipements_ups_types', 'equipements_systemes_intrusions_types',
    'equipements_controles_acces_types', 'equipements_systemes_ventilations_types', 'equipements_alarmes_incendies_types', 'phasages_types', 'envois_receptions_types', 'principals_secondaires', 'amperages_types', 'cables_calibres_types', 'signaux_types',
    'calibres_protections_types', 'capacites_ruptures_types', 'disjoncteurs_modeles', 'installations_disjoncteurs_types', 'equipements_electriques_types', 'kva_types', 'montages_panneaux_types',
    'nombres_espaces_panneaux_types', 'nombres_phases_types', 'tensions_types', 'transformateurs_types', 'materiaux_types', 'boitiers_types', 'conversions_recettes', 'conversions_noms_colonnes', 'cuves_types', 'fontaines_refrigerees_types', 'plomberies_securites_urgences_types',
    'priorites', 'raccords_plomberies_types', 'raccords_plomberies_materiaux', 'raccords_plomberies_formats_types', 'urinoirs_types', 'fusibles_actions_types', 'fusibles_formats_types', 'composantes_electriques_types', 'hp_types',
    'cables_types', 'conduits_types', 'diametres_conduits_electricites', 'diametres_conduits_plomberies', 'diametres_conduits_canalisations', 'dispositifs_electriques_champs_types', 'demarreurs_types', 
    'demarreurs_compositions_types', 'charges_types', 'marques', 'modeles', 'transfo_temperatures', 'transfo_impedances', 'transfo_k_facteurs', 'fluides_types', 'compresseurs_types', 'coolants_types', 'duplexe_simplex',
    'uta_classifications_types', 'moteurs_chassis_types', 'niveaux_criticites', 'systemes_types', 'roues_ventilateurs_types', 'ailettes_serpentins_modeles', 'ailettes_serpentins_materiaux', 'equipements_installations_methodes',
    'serpentins_fonctions_types', 'grilles_diffuseurs_materiaux_types', 'grilles_diffuseurs_garnitures_types', 'grilles_diffuseurs_finitions_types', 'locaux_classifications', 'locaux_resistance_feux', 'locaux_substrats',
    'locaux_finis', 'locaux_plinthes', 'portes_models', 'portes_materiaux', 'portes_vitrages', 'portes_pellicules_vitrage', 'portes_finis', 'porte_vitrage_cadre', 'portes_persiennes',
    'ouis_nons', 'cadences_types', 'pompes_types', 'valves_types', 'raccords_plomberies_nature', 'plomberies_substance', 'instruments_fonctions', 'signal_nature', 'taux_utilisations', 'routes_types', 'infrastructure_materiaux',
    'compacition_types', 'aqueduc_composantes', 'egout_pluvial_composantes', 'egout_sanitaire_composantes', 'reseau_electriques_composantes', 'reseau_gas_composantes', 'reseaux_telecoms_composantes', 'pavage_materiaux',
    'appareils_chauffages_types', 'fusibles_calibres_types', 'pseudos', 'nomenclatures', 'tour_eau_types', 'diffuseur_types', 'filtre_efficacites', 'priorites_acquisitions_types', 'modes_acquisitions_types', 'priorites_financements_types', 'modes_financements_types', 'portees_appels_offres_types', 
    'uni_cloisons_types', 'uni_portes_types', 'capacites_portantes_types', 'uni_exigences_fonctionnelles_types', 'planeites_types', 'tolerances_mouvements_types', 'vibrations_types', 'espaces_types', 
    'uni_finitions_murs_types', 'uni_finitions_planchers_types', 'uni_finitions_plinthes_types', 'uni_finitions_plafonds_types', 'uni_accessoires_protections_incendies_types', 'uni_accessoires_integres_types', 'uni_ameublements_decorations_types',
    'uni_appareils_plomberies_types', 'uni_autres_systemes_plomberies_types', 'uni_communications_informatiques_types', 'uni_distributions_cvcas_types', 'uni_distributions_secondaires_types', 'uni_eclairages_types', 'uni_equipements_types',
    'uni_gicleurs_types', 'uni_securites_types', 'uni_reglementations_types', 'sites_livraisons_types', 'locals_transitoires_types', 'conversions_unites_types', 'formulaires_types', "conditions_vetustes_types", "bloquants_types", "bloquants_financements_types",
    'acquisitions_sous_volets_responsables_types','acquisitions_phases_incidences_contraignantes_types','acquisitions_projets_priorisations_types','acquisitions_projets_modes_sollicitations_types','acquisitions_projets_modes_adjudications_types', 'acquisitions_responsables_dossiers_types',
    'acquisitions_responsables_dossiers_a_types', 'acquisitions_responsables_dossiers_b_types', 'acquisitions_responsables_dossiers_c_types', 'acquisitions_responsables_dossiers_d_types', 'acquisitions_responsables_dossiers_e_types', 'acquisitions_responsables_dossiers_f_types',
	'acquisitions_responsables_dossiers_g_types', 'acquisitions_responsables_dossiers_h_types', 'acquisitions_responsables_dossiers_i_types', 'acquisitions_responsables_dossiers_j_types', 'acquisitions_menus_supplementaires_a_types','contrats_responsables_dossiers_types',
    'financements_statuts_internes_types', 'financements_statuts_externes_types', 'approvisionnements_etapes_noms', 'conges_feries_types','financements_responsables_dossiers_a_types', 'financements_responsables_dossiers_b_types','financements_responsables_dossiers_c_types',
    'financements_responsables_dossiers_d_types','financements_responsables_dossiers_e_types','financements_regrouppements_types', 'indices_transmissions_sons_types', 'revetements_murals_types', 'verres_exterieurs_types', 'verres_interieurs_types', 'habillages_fenetres_types',
    'murs_types', 'planchers_types', 'fenetres_types', 'plafonds_types', 'protecteurs_murals_types', 'acquisitions_responsables_installations_types', 'interieurs_exterieurs_types', 'csa_electricites_types', 'portes_types', 'gestions_portes_types', 'gestions_portes_quincailleries_types', 'procedures_inspections_planifiees_responsables_types');



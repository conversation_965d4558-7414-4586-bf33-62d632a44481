<?php
ini_set("memory_limit","10000M");
ini_set('max_execution_time', 30000);

$projets = array();
if (isset($_GET['id']) && intval($_GET['id']) > 0)
	$projets[intval($_GET['id'])] = '';
else {
	$filtre = &$_SESSION['filtre_contrat'];
	$filtre['contrat_projet_id'] = $filtre['contrat_projet_id'] ?? 0;
	$filtre['voir_tous'] = $filtre['voir_tous'] ?? false;

	$projets = ContratsProjets::getListe([$filtre]);
}

$admin = ContratsProjetsAdministrations::getAdminForProjet();

$col = 0;
$dataArray = array();
$dataArray[0][$col++] = 'ID';
foreach (ContratsProjetsAdministrations::getColonnesMetas() as $blocsNiv2) {
	foreach ($blocsNiv2 as $bloc) {
		foreach ($bloc['colonnes'] as $champ) {
			if (isset($champs_to_export[$champ['nom']]['export'])) {
				$dataArray[0][$col++] = isset($champs_to_export[$champ['nom']]['alias']) && dm_strlen(dm_trim($champs_to_export[$champ['nom']]['alias'])) > 0 ? $champs_to_export[$champ['nom']]['alias'] : $admin->getLabelForColonne($champ['nom']);
			}
		}
	}
}

$assoc_col_champ = [];
$row = 1;
foreach ($projets as $projet_id => $projet) {
	$dataArray[$row][$col++] = $projet->id;
	foreach (ContratsProjetsAdministrations::getColonnesMetas() as $blocsNiv2) {
		foreach ($blocsNiv2 as $bloc) {
			foreach ($bloc['colonnes'] as $champ) {
				if (isset($champs_to_export[$champ['nom']]['export'])) {
					$col_bd = $champ['col_bd'] ?? $champ['nom'];
					$val = "";
					if (isset($projet->$col_bd)) {
						$val = $projet->getRaw($col_bd);
					}
					$dataArray[$row][$col++] = $val;
				}
			}
		}
	}
	$row++;
}

$nom_fichier = 'Exportation_globale';
exportExcelFromDataArray($dataArray, $nom_fichier, $nom_fichier, 'contrat_avance');
exit;

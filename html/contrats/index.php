<?
include('config/header.inc.php');
$admin = ContratsProjetsAdministrations::getAdminForProjet();
?>
	<h1 class="ficheTitre" style="margin-bottom: 20px;">
		<?=txt('Gestion des dossiers contractuels')?>
<!--		<img src="css/images/icons/dark/filter.png" style="cursor: pointer; position: relative; top: 4px;" id="filter_btn" />-->

        <a href="index.php?section=contrats&module=export_avance" class="fancy_big" title="Exporter">
            <img src="css/images/icons/dark/DM_export_CSV.png"></a>

		<div style="float: right;">
            <a class="btn actif" href="index.php?section=contrats&module=index&mode=specifique" onclick="return false"><?php echo txt('Gestion spécifique'); ?></a>
            <a class="btn fancy_big" href="index.php?section=admin&module=edit&table=contrats_projets_administrations&type=client&id=<?= (isset($admin) ? $admin->id : 0) ?>"><?php echo txt('Administration'); ?></a>
            <a class="btn" href="index.php?section=contrats&module=calendrier"><?php echo txt('Calendrier'); ?></a>
<!--            <a class="btn fancy" href="index.php?section=contrats&module=import" onclick="return false">--><?//=txt('Importation')?><!--</a>-->
        </div>
	</h1>
<?
$filtre = [];
$contrats = [];
$url = 'index.php?section=contrats&module=index';
$key_filtre = 'filtre_contrat';
include('filtre.inc.php');
if (isset($admin)) {
?>
	<table id="contrats">
		<tr>
            <?
            foreach (ContratsProjetsAdministrations::getColonnesTableau() as $colonne) {
                ?>
                <th class="<?=($filtre['order_by_col'] == $colonne['nom'] ? 'ordered' : '')?>">
	                <?=getOrderByLink($colonne['label'], $url, $colonne['nom'], $filtre['order_by_col'], $filtre['order_by_dir'])?>
                </th>
                <?
            }
            ?>
			<th width="100" style="text-align: center!important;">
                <a href="index.php?section=admin&module=edit&table=contrats_projets&type=client&id=0" class="fancy_big">
                    <img src="css/images/icons/dark/plus.png" /></a>
			</th>
		</tr>
		<?
		foreach ($contrats as $i => $contrat) {
			?>
			<tr>
				<?
				foreach (ContratsProjetsAdministrations::getColonnesTableau() as $colonne) {
                    $nom = $colonne['nom'];
				?>
                    <td class="<?=($filtre['order_by_col'] == $colonne['nom'] ? 'ordered' : '')?>">
                        <?= $contrat->$nom ?? '' ?>
                    </td>
                    <?
				}
				?>
				<td style="height: 26px; text-align: center;">
                    <?
                    if ($contrat->canEdit == 1) {
                    ?>
                        <a href="index.php?section=admin&module=edit&table=contrats_projets&type=client&id=<?=$contrat->id?>" class="fancy_big">
                            <img src="css/images/icons/dark/create_write.png"></a>
                        <a href="javascript: void(0);" class="dle_btn dle_delete_generique" tableName="contrats_projets" idElement="<?=$contrat->id?>">
                            <img src="css/images/icons/dark/trashcan.png" /></a>
                        <?
                    } else {
                        ?>
                        <a href="index.php?section=admin&module=edit&table=contrats_projets&type=client&id=<?=$contrat->id?>&force_readonly=1" class="fancy_big">
                            <img src="css/images/icons/dark/magnifying_glass.png"></a>
                        <?
                    }
                        ?>
				</td>
			</tr>
			<?
		}
		?>
	</table>
<?
}
showPaginateur('index.php?section=contrats&module=index', $limit, $nb_total, $nb_pages, $nb_par_page, $page);
?>
	<iframe id="iframe" name="iframe" style="display: none; width: 100%;"></iframe>
	<br style="clear: both;" />
<?
include('config/footer.inc.php');
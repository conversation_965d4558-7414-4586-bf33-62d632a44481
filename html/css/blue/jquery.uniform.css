/*

Uniform Theme: Aristo
Version: 1.0
By: 280North / Ported by <PERSON>
License: Creative Commons Share Alike
---
For use with the Uniform plugin:
http://pixelmatrixdesign.com/uniform/
---
Generated by Uniform Theme Generator:
http://pixelmatrixdesign.com/uniform/themer.html

*/

/* Global Declaration */

div.selector, 
div.selector span,
div.checker span,
div.radio span,
div.uploader,
div.uploader span.action {
	background-image: url(images/uniform/sprite.png);
	background-repeat: no-repeat;
}

.selector, 
.radio, 
.checker, 
.uploader, 
.selector *, 
.radio *, 
.checker *, 
.uploader *{
	margin: 0;
	padding: 0;
}

/* SPRITES */

/* Select */

div.selector {
	background-position: -483px -160px;
	line-height: 32px;
	height: 32px;
}

div.selector span {
	background-position: right 0px;
	height: 32px;
	line-height: 32px;
}

div.selector select {
	/* change these to adjust positioning of select element */
	top: 0px;
	left: 0px;
}

div.selector:active, 
div.selector.active {
	background-position: -483px -192px;
}

div.selector:active span, 
div.selector.active span {
	background-position: right -32px;
}

div.selector.focus, div.selector.hover, div.selector:hover {
	background-position: -483px -224px;
}

div.selector.focus span, div.selector.hover span, div.selector:hover span {
	background-position: right -64px;
}

div.selector.focus:active,
div.selector.focus.active,
div.selector:hover:active,
div.selector.active:hover {
	background-position: -483px -256px;
}

div.selector.focus:active span,
div.selector:hover:active span,
div.selector.active:hover span,
div.selector.focus.active span {
	background-position: right -96px;
}

div.selector.disabled,
div.selector.disabled:active,
div.selector.disabled.active {
	background-position: -483px -288px;
}

div.selector.disabled span,
div.selector.disabled:active span,
div.selector.disabled.active span {
	background-position: right -128px;
}

/* Checkbox */

div.checker {
	width: 23px;
	height: 23px;
}

div.checker input {
	width: 23px;
	height: 23px;
}

div.checker span {
	background-position: 0px -320px;
	height: 23px;
	width: 23px;
}

div.checker:active span, 
div.checker.active span {
	background-position: -23px -320px;
}

div.checker.focus span,
div.checker:hover span {
	background-position: -46px -320px;
}

div.checker.focus:active span,
div.checker:active:hover span,
div.checker.active:hover span,
div.checker.focus.active span {
	background-position: -69px -320px;
}

div.checker span.checked {
	background-position: -92px -320px;
}

div.checker:active span.checked, 
div.checker.active span.checked {
	background-position: -115px -320px;
}

div.checker.focus span.checked,
div.checker:hover span.checked {
	background-position: -138px -320px;
}

div.checker.focus:active span.checked,
div.checker:hover:active span.checked,
div.checker.active:hover span.checked,
div.checker.active.focus span.checked {
	background-position: -161px -320px;
}

div.checker.disabled span,
div.checker.disabled:active span,
div.checker.disabled.active span {
	background-position: -184px -320px;
}

div.checker.disabled span.checked,
div.checker.disabled:active span.checked,
div.checker.disabled.active span.checked {
	background-position: -207px -320px;
}

/* radio */

div.radio {
	width: 23px;
	height: 23px;
}

div.radio input {
	width: 23px;
	height: 23px;
}

div.radio span {
	height: 23px;
	width: 23px;
	background-position: 0px -343px;
}

div.radio:active span, 
div.radio.active span {
	background-position: -23px -343px;
}

div.radio.focus span, 
div.radio:hover span {
	background-position: -46px -343px;
}

div.radio.focus:active span,
div.radio:active:hover span,
div.radio.active:hover span,
div.radio.active.focus span {
	background-position: -69px -343px;
}

div.radio span.checked {
	background-position: -92px -343px;
}

div.radio:active span.checked,
div.radio.active span.checked {
	background-position: -115px -343px;
}

div.radio.focus span.checked, div.radio:hover span.checked {
	background-position: -138px -343px;
}

div.radio.focus:active span.checked, 
div.radio:hover:active span.checked,
div.radio.focus.active span.checked,
div.radio.active:hover span.checked {
	background-position: -161px -343px;
}

div.radio.disabled span,
div.radio.disabled:active span,
div.radio.disabled.active span {
	background-position: -184px -343px;
}

div.radio.disabled span.checked,
div.radio.disabled:active span.checked,
div.radio.disabled.active span.checked {
	background-position: -207px -343px;
}

/* uploader */

div.uploader {
	background-position: 0px -366px;
	height: 32px;
}

div.uploader span.action {
	background-position: right -494px;
	height: 24px;
	line-height: 24px;
}

div.uploader span.filename {
	height: 24px;
	/* change this line to adjust positioning of filename area */
	margin: 4px 0px 4px 4px;
	line-height: 24px;
}

div.uploader.focus,
div.uploader.hover,
div.uploader:hover {
	background-position: 0px -430px;
}

div.uploader.focus span.action,
div.uploader.hover span.action,
div.uploader:hover span.action {
	background-position: right -526px;
}

div.uploader.active span.action,
div.uploader:active span.action {
	background-position: right -558px;
}

div.uploader.focus.active span.action,
div.uploader:focus.active span.action,
div.uploader.focus:active span.action,
div.uploader:focus:active span.action {
	background-position: right -590px;
}

div.uploader.disabled {
	background-position: 0px -398px;
}

 div.uploader.disabled span.action {
	background-position: right -462px;
}

/* PRESENTATION */

/* Select */
div.selector {
	font-weight: bold;
	color: #464545;
	font-size: 14px;
}

div.selector select {
	font-size: 1em;
	border: solid 1px #fff;
}

div.selector span {
	padding: 0px 25px 0px 2px;
	cursor: pointer;
}

div.selector span {
	color: #666;
	text-shadow: 0 1px 0 #fff;
}

div.selector.disabled span {
	color: #bbb;
}

/* checker */
div.checker {
	margin-right: 10px;
}

/* radio */
div.radio {
	margin-right: 10px;
}

/* uploader */
div.uploader {
	width: 190px;
	margin-bottom: 20px;
	cursor: pointer;
}

div.uploader span.action {
	width: 83px;
	text-align: center;
	text-shadow: rgba(255,255,255,0.5) 0px 1px 0px;
	background-color: #F5F5F5;
	font-weight: bold;
	color: #1c4257;
}

div.uploader span.filename {
	color: #777;
	width: 82px;
	border-right: solid 1px #F5F5F5;
	font-size: 90%;
	font-size: 11px;
}

div.uploader input {
	width: 190px;
}

 div.uploader.disabled span.action {
	color: #aaa;
}

 div.uploader.disabled span.filename {
	border-color: #ddd;
	color: #aaa;
}
/*

CORE FUNCTIONALITY 

Not advised to edit stuff below this line
-----------------------------------------------------
*/

.selector select:focus, .radio input:focus, .checker input:focus, uploader input:focus {
	outline: 0;
}

/* Select */

 div.selector {
	position: relative;
	padding-left: 10px;
	display:inline-block;
	zoom:1;
	*display:inline;
}

 div.selector span {
	display: inline-block;
	float: left;
}

 div.selector select {
	position: absolute;
	opacity: 0;
}

/* checker */

 div.checker {
	position: relative;
	float: left;
}

 div.checker span {
	display: block;
	float: left;
	text-align: center;
}

div.checker input {
	opacity: 0;
	display: inline-block;
}

/* radio */

div.radio {
	position: relative;
	float: left;
}

div.radio span {
	display: block;
	float: left;
	text-align: center;
}

 div.radio input {
	opacity: 0;
	text-align: center;
	display: inline-block;
}

/* uploader */

div.uploader {
	position: relative;
	float: left;
	overflow: hidden;
}

div.uploader span.action {
	float: left;
	display: inline;
	padding: 4px 0px;
	overflow: hidden;
	cursor: pointer;
}

div.uploader span.filename {
	padding: 0px 10px;
	float: left;
	display: block;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

div.uploader input {
	opacity: 0;
	position: absolute;
	top: 0;
	right: 0;
	bottom: 0;
	float: right;
}
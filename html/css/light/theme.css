/*----------------------------------------------------------------------*/
/* colors
/* #f0a8a8 #f0cccc red
/* #a2e8a2 #c5e8c5 green
/* #e8e8a2 #e8e8c5 yellow
/* #a8f0f0 #ccf0f0 blue
/* #f0a8f0 #f0ccf0 pink
/* #aea8f0 #cfccf0 purple
/*
/* contrast color #bada55
/*----------------------------------------------------------------------*/

/*----------------------------------------------------------------------*/
/* Imports
/*----------------------------------------------------------------------*/

@import 'jquery-ui.css';
@import 'jquery.miniColors.css';
@import 'jquery.tipsy.css';
@import 'jquery.uniform.css';
@import 'jquery.wysiwyg.css';
@import 'jquery.elfinder.css';
@import 'jquery.fancybox.css';
@import 'jquery.datatables.css';


/*----------------------------------------------------------------------*/
/* General Section
/*----------------------------------------------------------------------*/

html, body, textarea, input { 
	color:#6f6f6f;
}
html{
	background-color:#ffffff;
	background-image:url(images/paper_02.png);
}
a, a:hover, a:visited, a:link {color:#3f3f3f;}

::-moz-selection{ background:#bada55; color:#ffffff; text-shadow:none;  }
::selection { background:#bada55; color:#ffffff; text-shadow:none; }


nav ul li a span{
	background-image:url(../images/icons/dark/arrow_right.png);
}

h1, h2, h3, h4, h5, h6{
	text-shadow:0 1px 0 #ffffff;
	color:#777777;
}
h1{
	text-shadow:0 2px 0 #ffffff;
}
h1 span{
	text-shadow:0 1px 0 #ffffff;
}
blockquote{
	border-color:#999999;
	color:#999999;
}

hr { 
	border-top-color:#e1e1e1;
	border-bottom-color:#ffffff;
}
/*----------------------------------------------------------------------*/
/* jQuery UI mods
/*----------------------------------------------------------------------*/
.ui-widget-header a, .ui-accordion-header a{
	color:#444444;
	text-shadow:0 0 1px #FFFFFF;
}
.ui-widget-header{
	border-top-color:#FFFFFF !important;
	border-bottom-color:#AAAAAA !important;
	margin:0;
}
.ui-widget-overlay{
	background-image:url(images/Paper_02.png);
	background-color:#333333;
	opacity: 0.5;
}
.ui-state-default a{
	border-top-color:#FFFFFF !important;
}
.ui-slider-range.ui-widget-header{
	background-color:#333333;
	background-image:url(../images/bg/01.png);
}
.ui-slider{
	border-color:#AAAAAA !important;
	background-color:#f6f6f6;
	background-image:url(images/paper_02.png);
	-webkit-box-shadow:inset 0 1px 1px rgba(0, 0, 0, 0.3);
	-moz-box-shadow:inset 0 1px 3px rgba(0, 0, 0, 0.3);
	box-shadow:inset 0 1px 3px rgba(0, 0, 0, 0.3);
}
.ui-slider .ui-slider-handle{
	border-color:#AAAAAA !important;
	background-image:url(images/slider_handler.png);
	-webkit-box-shadow:0 1px 1px rgba(0, 0, 0, 0.1);
	-moz-box-shadow:0 0 3px rgba(0, 0, 0, 0.1);
	box-shadow:0 0 1px rgba(0, 0, 0, 0.1);
}

/*----------------------------------------------------------------------*/
/* Pageoptions
/*----------------------------------------------------------------------*/

#pageoptions h1, #pageoptions h2,#pageoptions h3, #pageoptions h4, #pageoptions h5, #pageoptions h6{
	text-shadow:0 2px 0 #000000;
	color:#f1f1f1;
}
#pageoptions ul li a{
	color:#999999;
}
#pageoptions ul li a:hover, #pageoptions ul li a.active{
	color:#f1f1f1;
	background-color:#2e2e33;
}
#pageoptions > div{
	color:#f1f1f1;
	background-color:#2e2e33;
	-webkit-box-shadow:inset 0 2px 1px rgba(0, 0, 0, 0.5);
	-moz-box-shadow:inset 0 2px 1px rgba(0, 0, 0, 0.5);
	box-shadow:inset 0 2px 1px rgba(0, 0, 0, 0.5);
}
/*----------------------------------------------------------------------*/
/* Tables
/*----------------------------------------------------------------------*/

table{
	background-image:url(images/paper_01.png);
}
table td, table th{
	border-color:#dddddd #e7e7e7 #dddddd #e7e7e7;
}
table thead tr,table tfoot tr, table th{
	background-image:url(images/paper_02.png);
}
table th{
	text-shadow:0 -1px 0 #ffffff;
}


/*----------------------------------------------------------------------*/
/* Header
/*----------------------------------------------------------------------*/


header{
	/*-webkit-box-shadow:3px 0 4px rgba(0, 0, 0, 0.1);
	-moz-box-shadow:3px 0 4px rgba(0, 0, 0, 0.1);
	box-shadow:3px 0 4px rgba(0, 0, 0, 0.1);*/
	border-top-color:#d7d7d7;
	
	background: transparent;
	/* the filter causes an overflow problem on IE9 http://stackoverflow.com/questions/3619383/internet-explorer-css-property-filter-ignores-overflowvisible
	filter:progid:DXImageTransform.Microsoft.gradient(startColorstr='#f6f6f6', endColorstr='#f1f1f1');*/
	/*background:-webkit-gradient(linear, left top, left bottom, from(#f6f6f6), to(#f1f1f1));
	background:-moz-linear-gradient(top,  #f6f6f6,  #f1f1f1);
	background:-o-linear-gradient(top,  #f6f6f6,  #f1f1f1);*/
}
#logo, #logo a{
	text-shadow:0 -1px 0 #999999;
	color:#ffffff;
}
#header ul li ul li{
	border-color:#d7d7d7 #c2c2c2 #c2c2c2 #d7d7d7;
}
#header ul li ul li a{
	color:#353535;
	text-shadow:0px 1px 0 #ffffff;
	border-top-color:#ffffff;
	
	background:#f1f1f1;
	filter:progid:DXImageTransform.Microsoft.gradient(startColorstr='#f1f1f1', endColorstr='#e8e8e8');
	background:-webkit-gradient(linear, left top, left bottom, from(#f1f1f1), to(#e8e8e8));
	background:-moz-linear-gradient(top,  #f1f1f1,  #e8e8e8);
	background:-o-linear-gradient(top,  #f1f1f1,  #e8e8e8);
}
#header ul li ul li a:hover{

	background:#f6f6f6;
	filter:progid:DXImageTransform.Microsoft.gradient(startColorstr='#f6f6f6', endColorstr='#f1f1f1');
	background:-webkit-gradient(linear, left top, left bottom, from(#f6f6f6), to(#f1f1f1));
	background:-moz-linear-gradient(top,  #f6f6f6,  #f1f1f1);
	background:-o-linear-gradient(top,  #f6f6f6,  #f1f1f1);
}
#header ul li ul li a:active, #header ul li ul li a.active{
	-webkit-box-shadow:inset 0 2px 1px rgba(0, 0, 0, 0.3);
	-moz-box-shadow:inset 0 2px 1px rgba(0, 0, 0, 0.3);
	box-shadow:inset 0 2px 1px rgba(0, 0, 0, 0.3);
	
	background:#e8e8e8;
	filter:progid:DXImageTransform.Microsoft.gradient(startColorstr='#e8e8e8', endColorstr='#e3e3e3');
	background:-webkit-gradient(linear, left top, left bottom, from(#e8e8e8), to(#e3e3e3));
	background:-moz-linear-gradient(top,  #e8e8e8,  #e3e3e3);
	background:-o-linear-gradient(top,  #e8e8e8,  #e3e3e3); 
}
#header ul li ul li:active, #header ul li ul li.active{
}
#header ul li ul li ul{
	background-color:#2e2e33;
}
#header ul li ul li span{
	background-color:#f0a8a8;
	color:#ffffff;
	border-color: #bbb;
   	-webkit-box-shadow: inset 0px 2px 2px rgba(255,255,255, 0.4), inset 0px -2px 3px rgba(255,25,25, 0.4);
	-moz-box-shadow: inset 0px 2px 2px rgba(255,255,255, 0.4), inset 0px -2px 3px rgba(255,25,25, 0.4);
	box-shadow: inset 0px 2px 2px rgba(255,255,255, 0.4), inset 0px -2px 3px rgba(255,25,25, 0.4);
}
#header ul li ul li ul li{
	border-color:#dddddd;
}
#searchbox{
	border-color:#d7d7d7 #c2c2c2 #c2c2c2 #d7d7d7;
}
form#searchform input#search{
	color:#353535;
	text-shadow:0px 1px 0 #ffffff;
	border-top-color:#ffffff;
	-webkit-box-shadow:none;
	-moz-box-shadow:none;
	box-shadow:none;
	
	background:#f1f1f1;
	filter:progid:DXImageTransform.Microsoft.gradient(startColorstr='#f1f1f1', endColorstr='#e8e8e8');
	background:-webkit-gradient(linear, left top, left bottom, from(#f1f1f1), to(#e8e8e8));
	background:-moz-linear-gradient(top,  #f1f1f1,  #e8e8e8);
	background:-o-linear-gradient(top,  #f1f1f1,  #e8e8e8);
}
form#searchform input#search:hover{
	background:#f6f6f6;
	filter:progid:DXImageTransform.Microsoft.gradient(startColorstr='#f6f6f6', endColorstr='#f1f1f1');
	background:-webkit-gradient(linear, left top, left bottom, from(#f6f6f6), to(#f1f1f1));
	background:-moz-linear-gradient(top,  #f6f6f6,  #f1f1f1);
	background:-o-linear-gradient(top,  #f6f6f6,  #f1f1f1);
}
form#searchform input#search:focus{
	-webkit-box-shadow:inset 0 2px 1px rgba(0, 0, 0, 0.3);
	-moz-box-shadow:inset 0 2px 1px rgba(0, 0, 0, 0.3);
	box-shadow:inset 0 2px 1px rgba(0, 0, 0, 0.3);
	background:#ffffff;
	filter:none;
}
form#searchform input#search.load{
	background-image:url(images/loading.gif) !important;
}

 /* Live Search
 /*---------------------------------------------------------------------*/

ul#searchboxresult{
	border-top-color:#e1e1e1;
	-webkit-box-shadow:0px 0px 4px rgba(0, 0, 0, 0.1);
	-moz-box-shadow:0px 0px 4px rgba(0, 0, 0, 0.1);
	box-shadow:0px 0px 4px rgba(0, 0, 0, 0.1);
}
ul#searchboxresult li a{
	border-color:#ffffff #f7f7f7 #dddddd #f7f7f7;
	background-color:#f7f7f7;
}
ul#searchboxresult li a:hover{
	background-color:#f1f1f1;
}


/*----------------------------------------------------------------------*/
/* Navigation
/*----------------------------------------------------------------------*/

nav select, nav input{
	/*border-right-color:#e1e1e1 !important;
	border-left-color:#e1e1e1;*/
	-webkit-box-shadow:5px 5px 10px 0 rgba(50, 50, 50, 0.46)!important;
	-moz-box-shadow:5px 5px 10px 0 rgba(50, 50, 50, 0.46)!important;
	box-shadow: 5px 5px 10px 0 rgba(50, 50, 50, 0.46)!important;
}

nav ul{
	border-top-color:#e1e1e1;
	background-color:#2e2e33;
}
nav ul li{
	background-image:none !important;
}
nav ul li a{
	background:#f1f1f1;
	filter:progid:DXImageTransform.Microsoft.gradient(startColorstr='#f1f1f1', endColorstr='#e8e8e8');
	background:-webkit-gradient(linear, left top, left bottom, from(#f1f1f1), to(#e8e8e8));
	background:-moz-linear-gradient(top,  #f1f1f1,  #e8e8e8);
	background:-o-linear-gradient(top,  #f1f1f1,  #e8e8e8);
}
nav ul li a:hover{
	background:#f6f6f6;
	filter:progid:DXImageTransform.Microsoft.gradient(startColorstr='#f6f6f6', endColorstr='#f1f1f1');
	background:-webkit-gradient(linear, left top, left bottom, from(#f6f6f6), to(#f1f1f1));
	background:-moz-linear-gradient(top,  #f6f6f6,  #f1f1f1);
	background:-o-linear-gradient(top,  #f6f6f6,  #f1f1f1);
}
nav ul li a span{
	color:#353535;
	text-shadow:0px 1px 0 #ffffff;
	border-color:#ffffff #f7f7f7 #dddddd #f7f7f7;
}
nav ul li a:active, nav ul li a.active{
	background-image:url(images/nav_active.png);
	background-repeat:repeat-x;
	background-color:#bada55;
	background-position:left center;
	filter:none;
}
nav ul li a:active span, nav ul li a.active span{
	color:#ffffff;
	text-shadow:0px -1px 0 rgba(0,0,0,0.3);
}

nav ul li ul{
	border-bottom-color:#dddddd;
}
nav ul li ul li{
	background-image:none !important;
}
nav ul li ul li a{
}
nav ul li ul li a span{
	border-top-color:#dddddd;
	background-image:none !important;
}
/*----------------------------------------------------------------------*/
/* Content
/*----------------------------------------------------------------------*/

#content{
	/*-webkit-box-shadow:3px 0 4px rgba(0, 0, 0, 0.1);
	-moz-box-shadow:3px 0 4px rgba(0, 0, 0, 0.1);
	box-shadow:3px 0 4px rgba(0, 0, 0, 0.1);*/
	background-color:#eeeeee;
	background-image:url(images/paper_02.png);
	border-color:#ededed;
}

.bgsample{
	border-color:#999999;
	-webkit-box-shadow:inset 0 0 2px rgba(0, 0, 0, 0.5);
	-moz-box-shadow:inset 0 0 2px rgba(0, 0, 0, 0.5);
	box-shadow:inset 0 0 2px rgba(0, 0, 0, 0.5);
	background-color:#ffffff;
}

/*----------------------------------------------------------------------*/
/* Breadcrumb
/*----------------------------------------------------------------------*/


.breadcrumb li a{
	color:#999999;
	text-shadow:0 1px 0 #ffffff;
	border-color:#eeeeee #dddddd #dddddd #dddddd;
	background-image:url(images/breadcrumb.png);
}
.breadcrumb li:first-child a{
	border-left-color:#dddddd;
}
.breadcrumb li:last-child a{
}
.breadcrumb.disabled li a:hover{
	color:#999999;
}
.breadcrumb li a.previous{
	color:#aaaaaa;
}
.breadcrumb.disabled li a.previous:hover{
	color:#aaaaaa;
}
.breadcrumb li a:hover{
	color:#777777;
}
.breadcrumb li a:active, .breadcrumb li a.active, .breadcrumb.disabled li a.active:hover{
	color:#555555;
	text-shadow:0 0 1px #ffffff;
}

/*----------------------------------------------------------------------*/
/* Gallery
/*----------------------------------------------------------------------*/

.gallery{
	border-color:#CCCCCC #E7E7E7 #FFFFFF #E7E7E7;
	background-image:url(images/paper_02.png);
}
.gallery .sortable_placeholder{
	background-color:#f6f6f6;
	background-image:url(images/paper_02.png);
}

.gallery li{
	border-color:#FFFFFF #E7E7E7 #CCCCCC #E7E7E7;
	background-image:url(images/paper_01.png);
}
.gallery li > a{
	background-image:url(images/loading.gif);
}
.gallery li img{
}
.gallery li span{
	background-color:#2e2e33;
	border-top-color:#666666;
	-webkit-box-shadow:0 -1px 0 #2e2e33;
	-moz-box-shadow:0 -1px 0 #2e2e33;
	box-shadow:0 -1px 0 #2e2e33;
}
.gallery li > a span a{
	color:#ffffff;
}
.gallery li a span a.edit{
	background-image:url(../images/icons/light/pencil.png);
}
.gallery li a span a.delete{
	background-image:url(../images/icons/light/cross.png);
}

/*----------------------------------------------------------------------*/
/* Message Box
/*----------------------------------------------------------------------*/

#wl_msg .msg-box, #wl_msg .msg-box-close{
	border-color:#e1e1e1;
	color:#f1f1f1;
	
	background:#202020;
	filter:progid:DXImageTransform.Microsoft.gradient(startColorstr='#202020', endColorstr='#151515');
	background:-webkit-gradient(linear, left top, left bottom, from(#202020), to(#151515));
	background:-moz-linear-gradient(top,  #202020,  #151515);
	background:-o-linear-gradient(top,  #202020,  #151515);
}
#wl_msg .msg-box-close{
}
#wl_msg .msg-box-close:hover{
	background-color:#e1e1e1;
	color:#2e2e33;
}
#wl_msg .msg-box h3, #wl_msg .msg-close{
	color:#f1f1f1;
	text-shadow:0 1px 0 #111111;
	background:#202020;
}
#wl_msg .msg-box h3{
	border-bottom-color:#555555;
}
#wl_msg .msg-close{
	border-left-color:#555555;
	border-bottom-color:#555555;
	background-image:url(../images/icons/light/cross.png);
}
#wl_msg .msg-close:hover{
	background-image:url(../images/icons/dark/cross.png);
	background-color:#e1e1e1;
}
#wl_msg .msg-content{
}
/*----------------------------------------------------------------------*/
/* Alert Boxes
/*----------------------------------------------------------------------*/

div.alert{
	border-color:#c7c7c7 #b2b2b2 #b2b2b2 #c7c7c7;
	text-shadow:0 1px 0 rgba(255,255,255,0.4);
	color:#555;
	background-color:#e6e6e6;
	-webkit-box-shadow:inset 0 1px 0 rgba(255, 255, 255, 0.6), inset 0 2px 5px rgba(255, 255, 255, 0.5), inset 0 -2px 8px rgba(0, 0, 0, 0.1);
	-moz-box-shadow:inset 0 1px 0 rgba(255, 255, 255, 0.6), inset 0 2px 5px rgba(255, 255, 255, 0.5), inset 0 -2px 8px rgba(0, 0, 0, 0.1);
	box-shadow:inset 0 1px 0 rgba(255, 255, 255, 0.5), inset 0 2px 5px rgba(255, 255, 255, 0.1), inset 0 -2px 8px rgba(0, 0, 0, 0.1);
	margin:9px 0;
}
div.alert:hover{
	border-color:#b2b2b2;
}
div.alert a{
	color:#777777;
}
div.alert a:hover{
	color:#444444;
}

div.alert.red, div.alert.warning{
	background-color:#f0a8a8;
}
div.alert.red:hover, div.alert.warning:hover{
	background-color:#f0cccc;
}
div.alert.green, div.alert.success{
	background-color:#a2e8a2;
}
div.alert.green:hover, div.alert.success:hover{
	background-color:#c5e8c5;
}
div.alert.yellow, div.alert.note{
	background-color:#e8e8a2;
}
div.alert.yellow:hover, div.alert.note:hover{
	background-color:#e8e8c5;
}
div.alert.blue, div.alert.info{
	background-color:#a8f0f0;
}
div.alert.blue:hover, div.alert.info:hover{
	background-color:#ccf0f0;
}
div.alert.pink{
	background-color:#f0a8f0;
}
div.alert.pink:hover{
	background-color:#f0ccf0;
}
div.alert.purple{
	background-color:#aea8f0;
}
div.alert.purple:hover{
	background-color:#cfccf0;
}

div.alert.warning{
	background-image:url(../images/icons/dark/alert.png);
}
div.alert.success{
	background-image:url(../images/icons/dark/tick.png);
}
div.alert.note{
	background-image:url(../images/icons/dark/light_bulb.png);
}
div.alert.info{
	background-image:url(../images/icons/dark/information.png);
}
/*----------------------------------------------------------------------*/
/* Form Elements
/*----------------------------------------------------------------------*/

pre{
	-webkit-box-shadow:inset 0 1px 1px rgba(0, 0, 0, 0.1);
	-moz-box-shadow:inset 0 1px 3px rgba(0, 0, 0, 0.1);
	box-shadow:inset 0 1px 3px rgba(0, 0, 0, 0.1);
}
pre, code{
	background-image:url(images/paper_02.png);
	background-color:#f1f1f1;
	border-color:#bbbbbb;
}
pre code{
	background-image:url(images/code_lines.png);
}

input,textarea,select{
	border-color:#bbbbbb;
}
input.placeholder,textarea.placeholder{
	color:#cccccc;
}
input.error{
	color:#c49090;
}
input[type=text], textarea, input:invalid, input:required, textarea:required,select{
	-webkit-box-shadow:inset 0 1px 3px rgba(0, 0, 0, 0.2);
	-moz-box-shadow:inset 0 1px 3px rgba(0, 0, 0, 0.2);
	box-shadow:inset 0 1px 3px rgba(0, 0, 0, 0.2);
}
input[type=text]:hover,input[type=text]:focus,textarea:hover,textarea:focus,select:focus,select:hover{
	-webkit-box-shadow:inset 0 1px 3px rgba(0, 0, 0, 0.2), 0 0 3px rgba(0, 0, 0, 0.2);
	-moz-box-shadow:inset 0 1px 3px rgba(0, 0, 0, 0.2), 0 0 3px rgba(0, 0, 0, 0.2);
	box-shadow:inset 0 1px 3px rgba(0, 0, 0, 0.2), 0 0 3px rgba(0, 0, 0, 0.2);
}

select optgroup, select option, select{
	color:#555555;
}
select option{
	border-color:#ffffff #f7f7f7 #dddddd #f7f7f7;
}
form{
}
form .wl_formstatus{
	color:#990000;
}
form span.required{
	background-image:url(images/required.png);
}

form fieldset{
	border-color:#dddddd #e7e7e7 #bbbbbb #e7e7e7;
}
#popupContent form > fieldset > label, #popupContent fieldset > form > label, #popupContent > form > label{
	border-top-color:#ffffff;
	text-shadow:0 1px 0 #ffffff;

	background:#f1f1f1;
	filter:progid:DXImageTransform.Microsoft.gradient(startColorstr='#f1f1f1', endColorstr='#e8e8e8');
	background:-webkit-gradient(linear, left top, left bottom, from(#f1f1f1), to(#e8e8e8));
	background:-moz-linear-gradient(top,  #f1f1f1,  #e8e8e8);
	background:-o-linear-gradient(top,  #f1f1f1,  #e8e8e8);
}
form fieldset > section{
}
form fieldset > section > div{
	border-left-color:#e7e7e7;
}
form fieldset > section > div span{
}
form fieldset > section.error{
	background:#f0cccc;
	border-bottom-color:#f0cccc;
	filter:none;
}
form fieldset > section.error > label{
	text-shadow:none;
}
form fieldset > section label{
}
div.passwordstrength{
	text-shadow:0 1px 0 #dddddd;
	-webkit-box-shadow:inset 0 0 5px rgba(0, 0, 0, 0.5);
	-moz-box-shadow:inset 0 0 5px rgba(0, 0, 0, 0.5);
	box-shadow:inset 0 0 5px rgba(0, 0, 0, 0.5);
}
div.passwordstrength.s_1{background-color:#f0a8a8;}
div.passwordstrength.s_2{background-color:#f0d2b5;}
div.passwordstrength.s_3{background-color:#e8e8a2;}
div.passwordstrength.s_4{background-color:#a8f0f0;}
div.passwordstrength.s_5{background-color:#a2e8a2;}

/*----------------------------------------------------------------------*/
/* jQuery Checkbox
/*----------------------------------------------------------------------*/

.jquery-checkbox span.checkboxplaceholder { 
	background-image:url(images/checkbox.png);
}

/*----------------------------------------------------------------------*/
/* File Upload
/*----------------------------------------------------------------------*/

div.fileuploadui{
}
div.fileuploadui a{
}
ul.fileuploadpool{
	border-color:#ffffff;
	background-image:url(images/paper_02.png);
	-webkit-box-shadow:inset 0 1px 1px rgba(0, 0, 0, 0.5);
	-moz-box-shadow:inset 0 1px 3px rgba(0, 0, 0, 0.5);
	box-shadow:inset 0 1px 3px rgba(0, 0, 0, 0.5);
}
ul.fileuploadpool.drop{
	background-image:url(images/dragndrop.png),url(images/paper_02.png);
}
ul.fileuploadpool.single{
}
ul.fileuploadpool li{
	border-color:#FFFFFF #E7E7E7 #CCCCCC #E7E7E7;
	background-color:#f6f6f6;
}
ul.fileuploadpool li.error{
	background-color:#f0cccc;
}
ul.fileuploadpool li .name{
}
ul.fileuploadpool li a{
}
ul.fileuploadpool li a:hover{
}
ul.fileuploadpool li a.cancel{
	background-image:url(../images/icons/dark/cross.png);
}
ul.fileuploadpool li a.remove{
	background-image:url(../images/icons/dark/cross.png);
}
ul.fileuploadpool li .progress{
	background-color:#f0a8a8;
	background-image:url(images/upload.gif);
}
ul.fileuploadpool li.success .progress{
	background-color:#a2e8a2;
	background-image:none;
}

/*----------------------------------------------------------------------*/
/* Comboselect
/*----------------------------------------------------------------------*/

div.comboselectbox div.combowrap{
	border-color:#FFFFFF #E7E7E7 #CCCCCC #E7E7E7;
	background-image:url(images/paper_02.png);
	-webkit-box-shadow:inset 0 1px 1px rgba(0, 0, 0, 0.5);
	-moz-box-shadow:inset 0 1px 3px rgba(0, 0, 0, 0.5);
	box-shadow:inset 0 1px 3px rgba(0, 0, 0, 0.5);
}
div.comboselectbox ul.comboselect li{
	border-color:#FFFFFF #E7E7E7 #CCCCCC #E7E7E7;
	background-color:#f6f6f6;
}
div.comboselectbox ul.comboselect li:hover a{
	color:#111;
}
div.comboselectbox ul.comboselect li a.add{
	background-image:url(../images/icons/dark/plus.png);
}
div.comboselectbox ul.comboselect li a.remove{
	background-image:url(../images/icons/dark/cross_2.png);
}
div.comboselectbox ul.comboselect li.used a{
	color:#ccc;
}
div.comboselectbox ul.comboselect li.selected{
	background-color:#efefef;
}
div.comboselectbox div.comboselectbuttons a.add{
	background-image:url(../images/icons/dark/triangle_right.png);
}
div.comboselectbox div.comboselectbuttons a.remove{
	background-image:url(../images/icons/dark/triangle_left.png);
}
div.comboselectbox div.comboselectbuttons a.addall{
	background-image:url(../images/icons/dark/triangle_double_right.png);
}
div.comboselectbox div.comboselectbuttons a.removeall{
	background-image:url(../images/icons/dark/triangle_double_left.png);
}

@media screen and (max-width:700px){
	div.comboselectbox div.comboselectbuttons a.add{
		background-image:url(../images/icons/dark/triangle_down.png);
	}
	div.comboselectbox div.comboselectbuttons a.remove{
		background-image:url(../images/icons/dark/triangle_up.png);
	}
	div.comboselectbox div.comboselectbuttons a.addall{
		background-image:url(../images/icons/dark/triangle_double_down.png);
	}
	div.comboselectbox div.comboselectbuttons a.removeall{
		background-image:url(../images/icons/dark/triangle_double_up.png);
	}
}

/*----------------------------------------------------------------------*/
/* Buttons
/*----------------------------------------------------------------------*/


button, a.btn, .button
.dataTables_paginate span.paginate_button,
.dataTables_paginate span.paginate_active{
	border-color:#c7c7c7 #b2b2b2 #b2b2b2 #c7c7c7;
	text-shadow:0 1px 0 rgba(255,255,255,0.7);
	color:#555555;
	background-color:#e6e6e6;
	-webkit-box-shadow:inset 0 1px 0 rgba(255, 255, 255, 0.6), inset 0 2px 5px rgba(255, 255, 255, 0.5), inset 0 -2px 5px rgba(0, 0, 0, 0.1);
	-moz-box-shadow:inset 0 1px 0 rgba(255, 255, 255, 0.6), inset 0 2px 5px rgba(255, 255, 255, 0.5), inset 0 -2px 5px rgba(0, 0, 0, 0.1);
	box-shadow:inset 0 1px 0 rgba(255, 255, 255, 0.6), inset 0 2px 5px rgba(255, 255, 255, 0.5), inset 0 -2px 5px rgba(0, 0, 0, 0.1);
}
button.icon, a.btn.icon, .button.icon{
}
button{
}
a.btn{
}
button:hover, a.btn:hover, .button:hover,
.dataTables_paginate span.paginate_active:hover{
	background-color:#f1f1f1;
}
button:active, a.btn:active, .button:active,
.dataTables_paginate span.paginate_active,
a.btn.actif{
	background-color:#f1f1f1;
	border-color:#b2b2b2 #c7c7c7 #c7c7c7 #b2b2b2;
	-webkit-box-shadow:inset 0 2px 1px rgba(0, 0, 0, 0.1);
	-moz-box-shadow:inset 0 2px 1px rgba(0, 0, 0, 0.1);
	box-shadow:inset 0 2px 1px rgba(0, 0, 0, 0.1);
}

button.red, a.btn.red, .button.red{
	background-color:#f0a8a8;
}
button.red:hover, a.btn.red:hover, .button.red:hover{
	background-color:#f0cccc;
}
button.green, a.btn.green, .button.green{
	background-color:#a2e8a2;
}
button.green:hover, a.btn.green:hover, .button.green:hover{
	background-color:#c5e8c5;
}
button.yellow, a.btn.yellow, .button.yellow{
	background-color:#e8e8a2;
}
button.yellow:hover, a.btn.yellow:hover, .button.yellow:hover{
	background-color:#e8e8c5;
}
button.blue, a.btn.blue, .button.blue{
	background-color:#a8f0f0;
}
button.blue:hover, a.btn.blue:hover, .button.blue:hover{
	background-color:#ccf0f0;
}
button.pink, a.btn.pink, .button.pink{
	background-color:#f0a8f0;
}
button.pink:hover, a.btn.pink:hover, .button.pink:hover{
	background-color:#f0ccf0;
}
button.purple, a.btn.purple, .button.purple{
	background-color:#aea8f0;
}
button.purple:hover, a.btn.purple:hover, .button.purple:hover{
	background-color:#cfccf0;
}


/*----------------------------------------------------------------------*/
/* Widgets
/*----------------------------------------------------------------------*/

.widget, .widget.loading{
	background-color:#ffffff;
	border-color:#dddddd #efefef #f1f1f1 #efefef;
	background-image:url(images/paper_02.png);
}
.widget > div{
	border-color:#dddddd #e7e7e7 #bbbbbb #e7e7e7;
	background-image:url(images/paper_01.png);
}
.widget > div.ui-widget{
	background-image:none;
}
.widget:hover{
	border-color:#cccccc;
}
.widget.ui-sortable-helper{
	-webkit-box-shadow:0px 5px 6px rgba(0, 0, 0, 0.6);
	-moz-box-shadow:0px 5px 6px rgba(0, 0, 0, 0.6);
	box-shadow:0px 5px 6px rgba(0, 0, 0, 0.6);
}
.widget h3.handle{
	color:#444444;
	text-shadow:0 0 1px #ffffff;
	border-top-color:#ffffff;
	border-bottom-color:#aaaaaa;
	
	background:#f1f1f1;
	filter:progid:DXImageTransform.Microsoft.gradient(startColorstr='#f1f1f1', endColorstr='#e8e8e8');
	background:-webkit-gradient(linear, left top, left bottom, from(#f1f1f1), to(#e8e8e8));
	background:-moz-linear-gradient(top,  #f1f1f1,  #e8e8e8);
	background:-o-linear-gradient(top,  #f1f1f1,  #e8e8e8);
}
.widget h3.handle:hover{
	-webkit-box-shadow:inset 0 -3px 12px rgba(255, 255, 255, 0.5);
	-moz-box-shadow:inset 0 -3px 12px rgba(255, 255, 255, 0.5);
	box-shadow:inset 0 -3px 12px rgba(255, 255, 255, 0.5);
	
	background:#f6f6f6;
	filter:progid:DXImageTransform.Microsoft.gradient(startColorstr='#f6f6f6', endColorstr='#f1f1f1');
	background:-webkit-gradient(linear, left top, left bottom, from(#f6f6f6), to(#f1f1f1));
	background:-moz-linear-gradient(top,  #f6f6f6,  #f1f1f1);
	background:-o-linear-gradient(top,  #f6f6f6,  #f1f1f1);
}

.widget h3.handle .collapse{
	background-image:url(../images/icons/dark/collapse.png);
}
.widget h3.handle .reload{
	background-image:url(../images/icons/dark/refresh_3.png);
}
.widget.collapsed{
	border-bottom-color:#aaaaaa;
}
.widget.collapsed h3.handle .collapse{
	background-image:url(../images/icons/dark/expand.png);
}
.widget.loading h3.handle .reload{
	background-image:url(images/loading.gif);
}
.widget.number-widget > div ul li{
	border-top-color:#555555;
}
.widget.number-widget > div ul li a{
	color:#555555;
	text-shadow:0 1px 0 #ffffff;
}
.widget.number-widget > div ul li a:hover{
	color:#999999;
}
.widget.number-widget ul li a span{
}

.sortable_placeholder{
	border-color:#ffffff;
	background-color:#f6f6f6;
	background-image:url(images/paper_02.png);
	-webkit-box-shadow:inset 0 1px 1px rgba(0, 0, 0, 0.5);
	-moz-box-shadow:inset 0 1px 3px rgba(0, 0, 0, 0.5);
	box-shadow:inset 0 1px 3px rgba(0, 0, 0, 0.5);
}

/*----------------------------------------------------------------------*/
/* Footer
/*----------------------------------------------------------------------*/

footer{
	color:#aaaaaa;
}



/* Move the Headernav to a drop down menu to the right */
/*@media screen and (max-width:960px) {
	#header ul#headernav{
		background-image:url(../images/icons/dark/expand.png);
		border-left-color:#e7e7e7;
	}
	#header ul#headernav li ul li ul{
		background-image:url(../images/bg/04.png);
		background-color:#2e2e33;
	}
}*/

/* iPhone and real small devicces */
@media screen and (max-width:480px) {
	nav ul li a:active span, nav ul li a.active span{
		border-top-color:#ffffff;
	}
	nav{
		border-top-color:#e1e1e1;
	}
	nav ul {
		background:#f1f1f1;
	}
	nav ul li a span{
		border-left-color:#e1e1e1;
	}
}

/*----------------------------------------------------------------------*/
/* Icons
/*----------------------------------------------------------------------*/

nav ul li.i_abacus a span, .i_abacus{ background-image: url(../images/icons/dark/abacus.png);}
nav ul li.i_access_denied a span, .i_access_denied{ background-image: url(../images/icons/dark/access_denied.png);}
nav ul li.i_address_book a span, .i_address_book{ background-image: url(../images/icons/dark/address_book.png);}
nav ul li.i_admin_user a span, .i_admin_user{ background-image: url(../images/icons/dark/admin_user.png);}
nav ul li.i_admin_user_2 a span, .i_admin_user_2{ background-image: url(../images/icons/dark/admin_user_2.png);}
nav ul li.i_airplane a span, .i_airplane{ background-image: url(../images/icons/dark/airplane.png);}
nav ul li.i_alarm a span, .i_alarm{ background-image: url(../images/icons/dark/alarm.png);}
nav ul li.i_alarm_2 a span, .i_alarm_2{ background-image: url(../images/icons/dark/alarm_2.png);}
nav ul li.i_alarm_clock a span, .i_alarm_clock{ background-image: url(../images/icons/dark/alarm_clock.png);}
nav ul li.i_alert a span, .i_alert{ background-image: url(../images/icons/dark/alert.png);}
nav ul li.i_android a span, .i_android{ background-image: url(../images/icons/dark/android.png);}
nav ul li.i_application a span, .i_application{ background-image: url(../images/icons/dark/application.png);}
nav ul li.i_archive a span, .i_archive{ background-image: url(../images/icons/dark/archive.png);}
nav ul li.i_arrow_down a span, .i_arrow_down{ background-image: url(../images/icons/dark/arrow_down.png);}
nav ul li.i_arrow_down_left a span, .i_arrow_down_left{ background-image: url(../images/icons/dark/arrow_down_left.png);}
nav ul li.i_arrow_down_right a span, .i_arrow_down_right{ background-image: url(../images/icons/dark/arrow_down_right.png);}
nav ul li.i_arrow_left a span, .i_arrow_left{ background-image: url(../images/icons/dark/arrow_left.png);}
nav ul li.i_arrow_right a span, .i_arrow_right{ background-image: url(../images/icons/dark/arrow_right.png);}
nav ul li.i_arrow_up a span, .i_arrow_up{ background-image: url(../images/icons/dark/arrow_up.png);}
nav ul li.i_arrow_up_left a span, .i_arrow_up_left{ background-image: url(../images/icons/dark/arrow_up_left.png);}
nav ul li.i_arrow_up_right a span, .i_arrow_up_right{ background-image: url(../images/icons/dark/arrow_up_right.png);}
nav ul li.i_bag a span, .i_bag{ background-image: url(../images/icons/dark/bag.png);}
nav ul li.i_balloons a span, .i_balloons{ background-image: url(../images/icons/dark/balloons.png);}
nav ul li.i_bandaid a span, .i_bandaid{ background-image: url(../images/icons/dark/bandaid.png);}
nav ul li.i_battery a span, .i_battery{ background-image: url(../images/icons/dark/battery.png);}
nav ul li.i_battery_33 a span, .i_battery_33{ background-image: url(../images/icons/dark/battery_33.png);}
nav ul li.i_battery_66 a span, .i_battery_66{ background-image: url(../images/icons/dark/battery_66.png);}
nav ul li.i_battery_empty a span, .i_battery_empty{ background-image: url(../images/icons/dark/battery_empty.png);}
nav ul li.i_battery_full a span, .i_battery_full{ background-image: url(../images/icons/dark/battery_full.png);}
nav ul li.i_bended_arrow_down a span, .i_bended_arrow_down{ background-image: url(../images/icons/dark/bended_arrow_down.png);}
nav ul li.i_bended_arrow_left a span, .i_bended_arrow_left{ background-image: url(../images/icons/dark/bended_arrow_left.png);}
nav ul li.i_bended_arrow_right a span, .i_bended_arrow_right{ background-image: url(../images/icons/dark/bended_arrow_right.png);}
nav ul li.i_bended_arrow_up a span, .i_bended_arrow_up{ background-image: url(../images/icons/dark/bended_arrow_up.png);}
nav ul li.i_big_brush a span, .i_big_brush{ background-image: url(../images/icons/dark/big_brush.png);}
nav ul li.i_blackberry a span, .i_blackberry{ background-image: url(../images/icons/dark/blackberry.png);}
nav ul li.i_blocks_images a span, .i_blocks_images{ background-image: url(../images/icons/dark/blocks_images.png);}
nav ul li.i_blu-ray a span, .i_blu-ray{ background-image: url(../images/icons/dark/blu-ray.png);}
nav ul li.i_bluetooth a span, .i_bluetooth{ background-image: url(../images/icons/dark/bluetooth.png);}
nav ul li.i_bluetooth_2 a span, .i_bluetooth_2{ background-image: url(../images/icons/dark/bluetooth_2.png);}
nav ul li.i_book a span, .i_book{ background-image: url(../images/icons/dark/book.png);}
nav ul li.i_book_large a span, .i_book_large{ background-image: url(../images/icons/dark/book_large.png);}
nav ul li.i_books a span, .i_books{ background-image: url(../images/icons/dark/books.png);}
nav ul li.i_breadcrumb a span, .i_breadcrumb{ background-image: url(../images/icons/dark/breadcrumb.png);}
nav ul li.i_brush a span, .i_brush{ background-image: url(../images/icons/dark/brush.png);}
nav ul li.i_buildings a span, .i_buildings{ background-image: url(../images/icons/dark/buildings.png);}
nav ul li.i_bulls_eye a span, .i_bulls_eye{ background-image: url(../images/icons/dark/bulls_eye.png);}
nav ul li.i_calculator a span, .i_calculator{ background-image: url(../images/icons/dark/calculator.png);}
nav ul li.i_calendar a span, .i_calendar{ background-image: url(../images/icons/dark/calendar.png);}
nav ul li.i_calendar_day a span, .i_calendar_day{ background-image: url(../images/icons/dark/calendar_day.png);}
nav ul li.i_camera a span, .i_camera{ background-image: url(../images/icons/dark/camera.png);}
nav ul li.i_camera_2 a span, .i_camera_2{ background-image: url(../images/icons/dark/camera_2.png);}
nav ul li.i_car a span, .i_car{ background-image: url(../images/icons/dark/car.png);}
nav ul li.i_cash_register a span, .i_cash_register{ background-image: url(../images/icons/dark/cash_register.png);}
nav ul li.i_cassette a span, .i_cassette{ background-image: url(../images/icons/dark/cassette.png);}
nav ul li.i_cat a span, .i_cat{ background-image: url(../images/icons/dark/cat.png);}
nav ul li.i_cd a span, .i_cd{ background-image: url(../images/icons/dark/cd.png);}
nav ul li.i_chair a span, .i_chair{ background-image: url(../images/icons/dark/chair.png);}
nav ul li.i_chart a span, .i_chart{ background-image: url(../images/icons/dark/chart.png);}
nav ul li.i_chart_2 a span, .i_chart_2{ background-image: url(../images/icons/dark/chart_2.png);}
nav ul li.i_chart_3 a span, .i_chart_3{ background-image: url(../images/icons/dark/chart_3.png);}
nav ul li.i_chart_4 a span, .i_chart_4{ background-image: url(../images/icons/dark/chart_4.png);}
nav ul li.i_chart_5 a span, .i_chart_5{ background-image: url(../images/icons/dark/chart_5.png);}
nav ul li.i_chart_6 a span, .i_chart_6{ background-image: url(../images/icons/dark/chart_6.png);}
nav ul li.i_chart_7 a span, .i_chart_7{ background-image: url(../images/icons/dark/chart_7.png);}
nav ul li.i_chart_8 a span, .i_chart_8{ background-image: url(../images/icons/dark/chart_8.png);}
nav ul li.i_chemical a span, .i_chemical{ background-image: url(../images/icons/dark/chemical.png);}
nav ul li.i_chrome a span, .i_chrome{ background-image: url(../images/icons/dark/chrome.png);}
nav ul li.i_clipboard a span, .i_clipboard{ background-image: url(../images/icons/dark/clipboard.png);}
nav ul li.i_clock a span, .i_clock{ background-image: url(../images/icons/dark/clock.png);}
nav ul li.i_cloud a span, .i_cloud{ background-image: url(../images/icons/dark/cloud.png);}
nav ul li.i_cloud_download a span, .i_cloud_download{ background-image: url(../images/icons/dark/cloud_download.png);}
nav ul li.i_cloud_upload a span, .i_cloud_upload{ background-image: url(../images/icons/dark/cloud_upload.png);}
nav ul li.i_cog a span, .i_cog{ background-image: url(../images/icons/dark/cog.png);}
nav ul li.i_cog_2 a span, .i_cog_2{ background-image: url(../images/icons/dark/cog_2.png);}
nav ul li.i_cog_3 a span, .i_cog_3{ background-image: url(../images/icons/dark/cog_3.png);}
nav ul li.i_cog_4 a span, .i_cog_4{ background-image: url(../images/icons/dark/cog_4.png);}
nav ul li.i_collapse a span, .i_collapse{ background-image: url(../images/icons/dark/collapse.png);}
nav ul li.i_companies a span, .i_companies{ background-image: url(../images/icons/dark/companies.png);}
nav ul li.i_compress a span, .i_compress{ background-image: url(../images/icons/dark/compress.png);}
nav ul li.i_copy a span, .i_copy{ background-image: url(../images/icons/dark/copy.png);}
nav ul li.i_coverflow a span, .i_coverflow{ background-image: url(../images/icons/dark/coverflow.png);}
nav ul li.i_create_write a span, .i_create_write{ background-image: url(../images/icons/dark/create_write.png);}
nav ul li.i_cross a span, .i_cross{ background-image: url(../images/icons/dark/cross.png);}
nav ul li.i_cup a span, .i_cup{ background-image: url(../images/icons/dark/cup.png);}
nav ul li.i_cursor a span, .i_cursor{ background-image: url(../images/icons/dark/cursor.png);}
nav ul li.i_delicious a span, .i_delicious{ background-image: url(../images/icons/dark/delicious.png);}
nav ul li.i_desk a span, .i_desk{ background-image: url(../images/icons/dark/desk.png);}
nav ul li.i_digg a span, .i_digg{ background-image: url(../images/icons/dark/digg.png);}
nav ul li.i_digg_2 a span, .i_digg_2{ background-image: url(../images/icons/dark/digg_2.png);}
nav ul li.i_document a span, .i_document{ background-image: url(../images/icons/dark/document.png);}
nav ul li.i_document_add a span, .i_document_add{ background-image: url(../images/icons/dark/document_add.png);}
nav ul li.i_document_delete a span, .i_document_delete{ background-image: url(../images/icons/dark/document_delete.png);}
nav ul li.i_documents a span, .i_documents{ background-image: url(../images/icons/dark/documents.png);}
nav ul li.i_download a span, .i_download{ background-image: url(../images/icons/dark/download.png);}
nav ul li.i_download_to_computer a span, .i_download_to_computer{ background-image: url(../images/icons/dark/download_to_computer.png);}
nav ul li.i_dress a span, .i_dress{ background-image: url(../images/icons/dark/dress.png);}
nav ul li.i_dribbble a span, .i_dribbble{ background-image: url(../images/icons/dark/dribbble.png);}
nav ul li.i_dribbble_2 a span, .i_dribbble_2{ background-image: url(../images/icons/dark/dribbble_2.png);}
nav ul li.i_dropbox a span, .i_dropbox{ background-image: url(../images/icons/dark/dropbox.png);}
nav ul li.i_drupal a span, .i_drupal{ background-image: url(../images/icons/dark/drupal.png);}
nav ul li.i_duplicate a span, .i_duplicate{ background-image: url(../images/icons/dark/duplicate.png);}
nav ul li.i_dvd a span, .i_dvd{ background-image: url(../images/icons/dark/dvd.png);}
nav ul li.i_eject a span, .i_eject{ background-image: url(../images/icons/dark/eject.png);}
nav ul li.i_electricty_input a span, .i_electricty_input{ background-image: url(../images/icons/dark/electricty_input.png);}
nav ul li.i_electricty_plug a span, .i_electricty_plug{ background-image: url(../images/icons/dark/electricty_plug.png);}
nav ul li.i_empty a span, .i_empty{ background-image: url(../images/icons/dark/empty.png);}
nav ul li.i_excel_document a span, .i_excel_document{ background-image: url(../images/icons/dark/excel_document.png);}
nav ul li.i_excel_documents a span, .i_excel_documents{ background-image: url(../images/icons/dark/excel_documents.png);}
nav ul li.i_exclamation a span, .i_exclamation{ background-image: url(../images/icons/dark/exclamation.png);}
nav ul li.i_exit a span, .i_exit{ background-image: url(../images/icons/dark/exit.png);}
nav ul li.i_expand a span, .i_expand{ background-image: url(../images/icons/dark/expand.png);}
nav ul li.i_expose a span, .i_expose{ background-image: url(../images/icons/dark/expose.png);}
nav ul li.i_expression_engine a span, .i_expression_engine{ background-image: url(../images/icons/dark/expression_engine.png);}
nav ul li.i_eyedropper a span, .i_eyedropper{ background-image: url(../images/icons/dark/eyedropper.png);}
nav ul li.i_facebook a span, .i_facebook{ background-image: url(../images/icons/dark/facebook.png);}
nav ul li.i_facebook_like a span, .i_facebook_like{ background-image: url(../images/icons/dark/facebook_like.png);}
nav ul li.i_fax a span, .i_fax{ background-image: url(../images/icons/dark/fax.png);}
nav ul li.i_female a span, .i_female{ background-image: url(../images/icons/dark/female.png);}
nav ul li.i_female_contour a span, .i_female_contour{ background-image: url(../images/icons/dark/female_contour.png);}
nav ul li.i_file_cabinet a span, .i_file_cabinet{ background-image: url(../images/icons/dark/file_cabinet.png);}
nav ul li.i_film a span, .i_film{ background-image: url(../images/icons/dark/film.png);}
nav ul li.i_film_2 a span, .i_film_2{ background-image: url(../images/icons/dark/film_2.png);}
nav ul li.i_filmcamera a span, .i_filmcamera{ background-image: url(../images/icons/dark/filmcamera.png);}
nav ul li.i_finish_flag a span, .i_finish_flag{ background-image: url(../images/icons/dark/finish_flag.png);}
nav ul li.i_firefox a span, .i_firefox{ background-image: url(../images/icons/dark/firefox.png);}
nav ul li.i_flag a span, .i_flag{ background-image: url(../images/icons/dark/flag.png);}
nav ul li.i_flag_2 a span, .i_flag_2{ background-image: url(../images/icons/dark/flag_2.png);}
nav ul li.i_folder a span, .i_folder{ background-image: url(../images/icons/dark/folder.png);}
nav ul li.i_folder_add a span, .i_folder_add{ background-image: url(../images/icons/dark/folder_add.png);}
nav ul li.i_folder_closed a span, .i_folder_closed{ background-image: url(../images/icons/dark/folder_closed.png);}
nav ul li.i_folder_delete a span, .i_folder_delete{ background-image: url(../images/icons/dark/folder_delete.png);}
nav ul li.i_folder_download a span, .i_folder_download{ background-image: url(../images/icons/dark/folder_download.png);}
nav ul li.i_folder_lock a span, .i_folder_lock{ background-image: url(../images/icons/dark/folder_lock.png);}
nav ul li.i_folder_love a span, .i_folder_love{ background-image: url(../images/icons/dark/folder_love.png);}
nav ul li.i_folder_upload a span, .i_folder_upload{ background-image: url(../images/icons/dark/folder_upload.png);}
nav ul li.i_footprint a span, .i_footprint{ background-image: url(../images/icons/dark/footprint.png);}
nav ul li.i_forward a span, .i_forward{ background-image: url(../images/icons/dark/forward.png);}
nav ul li.i_fountain_pen a span, .i_fountain_pen{ background-image: url(../images/icons/dark/fountain_pen.png);}
nav ul li.i_frames a span, .i_frames{ background-image: url(../images/icons/dark/frames.png);}
nav ul li.i_fullscreen a span, .i_fullscreen{ background-image: url(../images/icons/dark/fullscreen.png);}
nav ul li.i_globe a span, .i_globe{ background-image: url(../images/icons/dark/globe.png);}
nav ul li.i_globe_2 a span, .i_globe_2{ background-image: url(../images/icons/dark/globe_2.png);}
nav ul li.i_google_buzz a span, .i_google_buzz{ background-image: url(../images/icons/dark/google_buzz.png);}
nav ul li.i_google_maps a span, .i_google_maps{ background-image: url(../images/icons/dark/google_maps.png);}
nav ul li.i_graph a span, .i_graph{ background-image: url(../images/icons/dark/graph.png);}
nav ul li.i_grid a span, .i_grid{ background-image: url(../images/icons/dark/grid.png);}
nav ul li.i_hd a span, .i_hd{ background-image: url(../images/icons/dark/hd.png);}
nav ul li.i_hd_2 a span, .i_hd_2{ background-image: url(../images/icons/dark/hd_2.png);}
nav ul li.i_hd_3 a span, .i_hd_3{ background-image: url(../images/icons/dark/hd_3.png);}
nav ul li.i_headphones a span, .i_headphones{ background-image: url(../images/icons/dark/headphones.png);}
nav ul li.i_help a span, .i_help{ background-image: url(../images/icons/dark/help.png);}
nav ul li.i_house a span, .i_house{ background-image: url(../images/icons/dark/house.png);}
nav ul li.i_house_2 a span, .i_house_2{ background-image: url(../images/icons/dark/house_2.png);}
nav ul li.i_ice_cream a span, .i_ice_cream{ background-image: url(../images/icons/dark/ice_cream.png);}
nav ul li.i_ice_cream_2 a span, .i_ice_cream_2{ background-image: url(../images/icons/dark/ice_cream_2.png);}
nav ul li.i_ichat a span, .i_ichat{ background-image: url(../images/icons/dark/ichat.png);}
nav ul li.i_imac a span, .i_imac{ background-image: url(../images/icons/dark/imac.png);}
nav ul li.i_image a span, .i_image{ background-image: url(../images/icons/dark/image.png);}
nav ul li.i_image_2 a span, .i_image_2{ background-image: url(../images/icons/dark/image_2.png);}
nav ul li.i_images a span, .i_images{ background-image: url(../images/icons/dark/images.png);}
nav ul li.i_images_2 a span, .i_images_2{ background-image: url(../images/icons/dark/images_2.png);}
nav ul li.i_inbox a span, .i_inbox{ background-image: url(../images/icons/dark/inbox.png);}
nav ul li.i_incomming a span, .i_incomming{ background-image: url(../images/icons/dark/incomming.png);}
nav ul li.i_information a span, .i_information{ background-image: url(../images/icons/dark/information.png);}
nav ul li.i_ipad a span, .i_ipad{ background-image: url(../images/icons/dark/ipad.png);}
nav ul li.i_iphone_3g a span, .i_iphone_3g{ background-image: url(../images/icons/dark/iphone_3g.png);}
nav ul li.i_iphone_4 a span, .i_iphone_4{ background-image: url(../images/icons/dark/iphone_4.png);}
nav ul li.i_ipod a span, .i_ipod{ background-image: url(../images/icons/dark/ipod.png);}
nav ul li.i_ipod_nano a span, .i_ipod_nano{ background-image: url(../images/icons/dark/ipod_nano.png);}
nav ul li.i_joomla a span, .i_joomla{ background-image: url(../images/icons/dark/joomla.png);}
nav ul li.i_key a span, .i_key{ background-image: url(../images/icons/dark/key.png);}
nav ul li.i_key_2 a span, .i_key_2{ background-image: url(../images/icons/dark/key_2.png);}
nav ul li.i_ladys_purse a span, .i_ladys_purse{ background-image: url(../images/icons/dark/ladys_purse.png);}
nav ul li.i_lamp a span, .i_lamp{ background-image: url(../images/icons/dark/lamp.png);}
nav ul li.i_laptop a span, .i_laptop{ background-image: url(../images/icons/dark/laptop.png);}
nav ul li.i_lastfm a span, .i_lastfm{ background-image: url(../images/icons/dark/lastfm.png);}
nav ul li.i_lemonade_stand a span, .i_lemonade_stand{ background-image: url(../images/icons/dark/lemonade_stand.png);}
nav ul li.i_light_bulb a span, .i_light_bulb{ background-image: url(../images/icons/dark/light_bulb.png);}
nav ul li.i_link a span, .i_link{ background-image: url(../images/icons/dark/link.png);}
nav ul li.i_link_2 a span, .i_link_2{ background-image: url(../images/icons/dark/link_2.png);}
nav ul li.i_linux a span, .i_linux{ background-image: url(../images/icons/dark/linux.png);}
nav ul li.i_list a span, .i_list{ background-image: url(../images/icons/dark/list.png);}
nav ul li.i_list_image a span, .i_list_image{ background-image: url(../images/icons/dark/list_image.png);}
nav ul li.i_list_images a span, .i_list_images{ background-image: url(../images/icons/dark/list_images.png);}
nav ul li.i_loading_bar a span, .i_loading_bar{ background-image: url(../images/icons/dark/loading_bar.png);}
nav ul li.i_locked a span, .i_locked{ background-image: url(../images/icons/dark/locked.png);}
nav ul li.i_locked_2 a span, .i_locked_2{ background-image: url(../images/icons/dark/locked_2.png);}
nav ul li.i_macos a span, .i_macos{ background-image: url(../images/icons/dark/macos.png);}
nav ul li.i_magic_mouse a span, .i_magic_mouse{ background-image: url(../images/icons/dark/magic_mouse.png);}
nav ul li.i_magnifying_glass a span, .i_magnifying_glass{ background-image: url(../images/icons/dark/magnifying_glass.png);}
nav ul li.i_mail a span, .i_mail{ background-image: url(../images/icons/dark/mail.png);}
nav ul li.i_male a span, .i_male{ background-image: url(../images/icons/dark/male.png);}
nav ul li.i_male_contour a span, .i_male_contour{ background-image: url(../images/icons/dark/male_contour.png);}
nav ul li.i_map a span, .i_map{ background-image: url(../images/icons/dark/map.png);}
nav ul li.i_marker a span, .i_marker{ background-image: url(../images/icons/dark/marker.png);}
nav ul li.i_maximize a span, .i_maximize{ background-image: url(../images/icons/dark/maximize.png);}
nav ul li.i_medical_case a span, .i_medical_case{ background-image: url(../images/icons/dark/medical_case.png);}
nav ul li.i_megaphone a span, .i_megaphone{ background-image: url(../images/icons/dark/megaphone.png);}
nav ul li.i_microphone a span, .i_microphone{ background-image: url(../images/icons/dark/microphone.png);}
nav ul li.i_mighty_mouse a span, .i_mighty_mouse{ background-image: url(../images/icons/dark/mighty_mouse.png);}
nav ul li.i_minimize a span, .i_minimize{ background-image: url(../images/icons/dark/minimize.png);}
nav ul li.i_minus a span, .i_minus{ background-image: url(../images/icons/dark/minus.png);}
nav ul li.i_mobile_phone a span, .i_mobile_phone{ background-image: url(../images/icons/dark/mobile_phone.png);}
nav ul li.i_mobypicture a span, .i_mobypicture{ background-image: url(../images/icons/dark/mobypicture.png);}
nav ul li.i_money a span, .i_money{ background-image: url(../images/icons/dark/money.png);}
nav ul li.i_money_2 a span, .i_money_2{ background-image: url(../images/icons/dark/money_2.png);}
nav ul li.i_monitor a span, .i_monitor{ background-image: url(../images/icons/dark/monitor.png);}
nav ul li.i_mouse a span, .i_mouse{ background-image: url(../images/icons/dark/mouse.png);}
nav ul li.i_myspace a span, .i_myspace{ background-image: url(../images/icons/dark/myspace.png);}
nav ul li.i_next a span, .i_next{ background-image: url(../images/icons/dark/next.png);}
nav ul li.i_note_book a span, .i_note_book{ background-image: url(../images/icons/dark/note_book.png);}
nav ul li.i_outgoing a span, .i_outgoing{ background-image: url(../images/icons/dark/outgoing.png);}
nav ul li.i_pacman a span, .i_pacman{ background-image: url(../images/icons/dark/pacman.png);}
nav ul li.i_pacman_ghost a span, .i_pacman_ghost{ background-image: url(../images/icons/dark/pacman_ghost.png);}
nav ul li.i_paint_brush a span, .i_paint_brush{ background-image: url(../images/icons/dark/paint_brush.png);}
nav ul li.i_pants a span, .i_pants{ background-image: url(../images/icons/dark/pants.png);}
nav ul li.i_paperclip a span, .i_paperclip{ background-image: url(../images/icons/dark/paperclip.png);}
nav ul li.i_paste a span, .i_paste{ background-image: url(../images/icons/dark/paste.png);}
nav ul li.i_pause a span, .i_pause{ background-image: url(../images/icons/dark/pause.png);}
nav ul li.i_paypal a span, .i_paypal{ background-image: url(../images/icons/dark/paypal.png);}
nav ul li.i_paypal_2 a span, .i_paypal_2{ background-image: url(../images/icons/dark/paypal_2.png);}
nav ul li.i_paypal_3 a span, .i_paypal_3{ background-image: url(../images/icons/dark/paypal_3.png);}
nav ul li.i_pdf_document a span, .i_pdf_document{ background-image: url(../images/icons/dark/pdf_document.png);}
nav ul li.i_pdf_documents a span, .i_pdf_documents{ background-image: url(../images/icons/dark/pdf_documents.png);}
nav ul li.i_pencil a span, .i_pencil{ background-image: url(../images/icons/dark/pencil.png);}
nav ul li.i_phone a span, .i_phone{ background-image: url(../images/icons/dark/phone.png);}
nav ul li.i_phone_2 a span, .i_phone_2{ background-image: url(../images/icons/dark/phone_2.png);}
nav ul li.i_phone_hook a span, .i_phone_hook{ background-image: url(../images/icons/dark/phone_hook.png);}
nav ul li.i_piggy_bank a span, .i_piggy_bank{ background-image: url(../images/icons/dark/piggy_bank.png);}
nav ul li.i_plane_suitecase a span, .i_plane_suitecase{ background-image: url(../images/icons/dark/plane_suitecase.png);}
nav ul li.i_play a span, .i_play{ background-image: url(../images/icons/dark/play.png);}
nav ul li.i_plixi a span, .i_plixi{ background-image: url(../images/icons/dark/plixi.png);}
nav ul li.i_plus a span, .i_plus{ background-image: url(../images/icons/dark/plus.png);}
nav ul li.i_post_card a span, .i_post_card{ background-image: url(../images/icons/dark/post_card.png);}
nav ul li.i_power a span, .i_power{ background-image: url(../images/icons/dark/power.png);}
nav ul li.i_powerpoint_document a span, .i_powerpoint_document{ background-image: url(../images/icons/dark/powerpoint_document.png);}
nav ul li.i_powerpoint_documents a span, .i_powerpoint_documents{ background-image: url(../images/icons/dark/powerpoint_documents.png);}
nav ul li.i_presentation a span, .i_presentation{ background-image: url(../images/icons/dark/presentation.png);}
nav ul li.i_prev a span, .i_prev{ background-image: url(../images/icons/dark/prev.png);}
nav ul li.i_preview a span, .i_preview{ background-image: url(../images/icons/dark/preview.png);}
nav ul li.i_price_tag a span, .i_price_tag{ background-image: url(../images/icons/dark/price_tag.png);}
nav ul li.i_price_tags a span, .i_price_tags{ background-image: url(../images/icons/dark/price_tags.png);}
nav ul li.i_printer a span, .i_printer{ background-image: url(../images/icons/dark/printer.png);}
nav ul li.i_question a span, .i_question{ background-image: url(../images/icons/dark/question.png);}
nav ul li.i_radio a span, .i_radio{ background-image: url(../images/icons/dark/radio.png);}
nav ul li.i_record a span, .i_record{ background-image: url(../images/icons/dark/record.png);}
nav ul li.i_recycle a span, .i_recycle{ background-image: url(../images/icons/dark/recycle.png);}
nav ul li.i_refresh a span, .i_refresh{ background-image: url(../images/icons/dark/refresh.png);}
nav ul li.i_refresh_2 a span, .i_refresh_2{ background-image: url(../images/icons/dark/refresh_2.png);}
nav ul li.i_refresh_3 a span, .i_refresh_3{ background-image: url(../images/icons/dark/refresh_3.png);}
nav ul li.i_refresh_4 a span, .i_refresh_4{ background-image: url(../images/icons/dark/refresh_4.png);}
nav ul li.i_repeat a span, .i_repeat{ background-image: url(../images/icons/dark/repeat.png);}
nav ul li.i_rewind a span, .i_rewind{ background-image: url(../images/icons/dark/rewind.png);}
nav ul li.i_robot a span, .i_robot{ background-image: url(../images/icons/dark/robot.png);}
nav ul li.i_rss a span, .i_rss{ background-image: url(../images/icons/dark/rss.png);}
nav ul li.i_ruler a span, .i_ruler{ background-image: url(../images/icons/dark/ruler.png);}
nav ul li.i_ruler_2 a span, .i_ruler_2{ background-image: url(../images/icons/dark/ruler_2.png);}
nav ul li.i_running_man a span, .i_running_man{ background-image: url(../images/icons/dark/running_man.png);}
nav ul li.i_safari a span, .i_safari{ background-image: url(../images/icons/dark/safari.png);}
nav ul li.i_scan_label a span, .i_scan_label{ background-image: url(../images/icons/dark/scan_label.png);}
nav ul li.i_scissors a span, .i_scissors{ background-image: url(../images/icons/dark/scissors.png);}
nav ul li.i_sd a span, .i_sd{ background-image: url(../images/icons/dark/sd.png);}
nav ul li.i_sd_2 a span, .i_sd_2{ background-image: url(../images/icons/dark/sd_2.png);}
nav ul li.i_sd_3 a span, .i_sd_3{ background-image: url(../images/icons/dark/sd_3.png);}
nav ul li.i_settings a span, .i_settings{ background-image: url(../images/icons/dark/settings.png);}
nav ul li.i_settings_2 a span, .i_settings_2{ background-image: url(../images/icons/dark/settings_2.png);}
nav ul li.i_shopping_bag a span, .i_shopping_bag{ background-image: url(../images/icons/dark/shopping_bag.png);}
nav ul li.i_shopping_basket a span, .i_shopping_basket{ background-image: url(../images/icons/dark/shopping_basket.png);}
nav ul li.i_shopping_basket_2 a span, .i_shopping_basket_2{ background-image: url(../images/icons/dark/shopping_basket_2.png);}
nav ul li.i_shopping_cart a span, .i_shopping_cart{ background-image: url(../images/icons/dark/shopping_cart.png);}
nav ul li.i_shopping_cart_2 a span, .i_shopping_cart_2{ background-image: url(../images/icons/dark/shopping_cart_2.png);}
nav ul li.i_shopping_cart_3 a span, .i_shopping_cart_3{ background-image: url(../images/icons/dark/shopping_cart_3.png);}
nav ul li.i_shopping_cart_4 a span, .i_shopping_cart_4{ background-image: url(../images/icons/dark/shopping_cart_4.png);}
nav ul li.i_shuffle a span, .i_shuffle{ background-image: url(../images/icons/dark/shuffle.png);}
nav ul li.i_sign_post a span, .i_sign_post{ background-image: url(../images/icons/dark/sign_post.png);}
nav ul li.i_skype a span, .i_skype{ background-image: url(../images/icons/dark/skype.png);}
nav ul li.i_sleeveless_shirt a span, .i_sleeveless_shirt{ background-image: url(../images/icons/dark/sleeveless_shirt.png);}
nav ul li.i_socks a span, .i_socks{ background-image: url(../images/icons/dark/socks.png);}
nav ul li.i_sound a span, .i_sound{ background-image: url(../images/icons/dark/sound.png);}
nav ul li.i_speech_bubble a span, .i_speech_bubble{ background-image: url(../images/icons/dark/speech_bubble.png);}
nav ul li.i_speech_bubble_2 a span, .i_speech_bubble_2{ background-image: url(../images/icons/dark/speech_bubble_2.png);}
nav ul li.i_speech_bubbles a span, .i_speech_bubbles{ background-image: url(../images/icons/dark/speech_bubbles.png);}
nav ul li.i_speech_bubbles_2 a span, .i_speech_bubbles_2{ background-image: url(../images/icons/dark/speech_bubbles_2.png);}
nav ul li.i_sport_shirt a span, .i_sport_shirt{ background-image: url(../images/icons/dark/sport_shirt.png);}
nav ul li.i_stop a span, .i_stop{ background-image: url(../images/icons/dark/stop.png);}
nav ul li.i_stop_watch a span, .i_stop_watch{ background-image: url(../images/icons/dark/stop_watch.png);}
nav ul li.i_strategy a span, .i_strategy{ background-image: url(../images/icons/dark/strategy.png);}
nav ul li.i_strategy_2 a span, .i_strategy_2{ background-image: url(../images/icons/dark/strategy_2.png);}
nav ul li.i_stubleupon a span, .i_stubleupon{ background-image: url(../images/icons/dark/stubleupon.png);}
nav ul li.i_suitecase a span, .i_suitecase{ background-image: url(../images/icons/dark/suitecase.png);}
nav ul li.i_sweater a span, .i_sweater{ background-image: url(../images/icons/dark/sweater.png);}
nav ul li.i_t-shirt a span, .i_t-shirt{ background-image: url(../images/icons/dark/t-shirt.png);}
nav ul li.i_table a span, .i_table{ background-image: url(../images/icons/dark/table.png);}
nav ul li.i_tag a span, .i_tag{ background-image: url(../images/icons/dark/tag.png);}
nav ul li.i_tags a span, .i_tags{ background-image: url(../images/icons/dark/tags.png);}
nav ul li.i_television a span, .i_television{ background-image: url(../images/icons/dark/television.png);}
nav ul li.i_tick a span, .i_tick{ background-image: url(../images/icons/dark/tick.png);}
nav ul li.i_timer a span, .i_timer{ background-image: url(../images/icons/dark/timer.png);}
nav ul li.i_trashcan a span, .i_trashcan{ background-image: url(../images/icons/dark/trashcan.png);}
nav ul li.i_trashcan_2 a span, .i_trashcan_2{ background-image: url(../images/icons/dark/trashcan_2.png);}
nav ul li.i_travel_suitecase a span, .i_travel_suitecase{ background-image: url(../images/icons/dark/travel_suitecase.png);}
nav ul li.i_tree a span, .i_tree{ background-image: url(../images/icons/dark/tree.png);}
nav ul li.i_triangle_double_down a span, .i_triangle_double_down{ background-image: url(../images/icons/dark/triangle_double_down.png);}
nav ul li.i_triangle_double_left a span, .i_triangle_double_left{ background-image: url(../images/icons/dark/triangle_double_left.png);}
nav ul li.i_triangle_double_right a span, .i_triangle_double_right{ background-image: url(../images/icons/dark/triangle_double_right.png);}
nav ul li.i_triangle_double_up a span, .i_triangle_double_up{ background-image: url(../images/icons/dark/triangle_double_up.png);}
nav ul li.i_triangle_down a span, .i_triangle_down{ background-image: url(../images/icons/dark/triangle_down.png);}
nav ul li.i_triangle_down_left a span, .i_triangle_down_left{ background-image: url(../images/icons/dark/triangle_down_left.png);}
nav ul li.i_triangle_down_right a span, .i_triangle_down_right{ background-image: url(../images/icons/dark/triangle_down_right.png);}
nav ul li.i_triangle_left a span, .i_triangle_left{ background-image: url(../images/icons/dark/triangle_left.png);}
nav ul li.i_triangle_left_right a span, .i_triangle_left_right{ background-image: url(../images/icons/dark/triangle_left_right.png);}
nav ul li.i_triangle_right a span, .i_triangle_right{ background-image: url(../images/icons/dark/triangle_right.png);}
nav ul li.i_triangle_up a span, .i_triangle_up{ background-image: url(../images/icons/dark/triangle_up.png);}
nav ul li.i_triangle_up_down a span, .i_triangle_up_down{ background-image: url(../images/icons/dark/triangle_up_down.png);}
nav ul li.i_triangle_up_left a span, .i_triangle_up_left{ background-image: url(../images/icons/dark/triangle_up_left.png);}
nav ul li.i_triangle_up_right a span, .i_triangle_up_right{ background-image: url(../images/icons/dark/triangle_up_right.png);}
nav ul li.i_trolly a span, .i_trolly{ background-image: url(../images/icons/dark/trolly.png);}
nav ul li.i_truck a span, .i_truck{ background-image: url(../images/icons/dark/truck.png);}
nav ul li.i_tumbler a span, .i_tumbler{ background-image: url(../images/icons/dark/tumbler.png);}
nav ul li.i_twitter a span, .i_twitter{ background-image: url(../images/icons/dark/twitter.png);}
nav ul li.i_twitter_2 a span, .i_twitter_2{ background-image: url(../images/icons/dark/twitter_2.png);}
nav ul li.i_typo a span, .i_typo{ background-image: url(../images/icons/dark/typo.png);}
nav ul li.i_umbrella a span, .i_umbrella{ background-image: url(../images/icons/dark/umbrella.png);}
nav ul li.i_under_construction a span, .i_under_construction{ background-image: url(../images/icons/dark/under_construction.png);}
nav ul li.i_unlocked a span, .i_unlocked{ background-image: url(../images/icons/dark/unlocked.png);}
nav ul li.i_upload a span, .i_upload{ background-image: url(../images/icons/dark/upload.png);}
nav ul li.i_user a span, .i_user{ background-image: url(../images/icons/dark/user.png);}
nav ul li.i_user_2 a span, .i_user_2{ background-image: url(../images/icons/dark/user_2.png);}
nav ul li.i_user_comment a span, .i_user_comment{ background-image: url(../images/icons/dark/user_comment.png);}
nav ul li.i_users a span, .i_users{ background-image: url(../images/icons/dark/users.png);}
nav ul li.i_users_2 a span, .i_users_2{ background-image: url(../images/icons/dark/users_2.png);}
nav ul li.i_v-card a span, .i_v-card{ background-image: url(../images/icons/dark/v-card.png);}
nav ul li.i_v-card_2 a span, .i_v-card_2{ background-image: url(../images/icons/dark/v-card_2.png);}
nav ul li.i_vault a span, .i_vault{ background-image: url(../images/icons/dark/vault.png);}
nav ul li.i_vimeo a span, .i_vimeo{ background-image: url(../images/icons/dark/vimeo.png);}
nav ul li.i_vimeo_2 a span, .i_vimeo_2{ background-image: url(../images/icons/dark/vimeo_2.png);}
nav ul li.i_walking_man a span, .i_walking_man{ background-image: url(../images/icons/dark/walking_man.png);}
nav ul li.i_wifi_signal a span, .i_wifi_signal{ background-image: url(../images/icons/dark/wifi_signal.png);}
nav ul li.i_wifi_signal_2 a span, .i_wifi_signal_2{ background-image: url(../images/icons/dark/wifi_signal_2.png);}
nav ul li.i_windows a span, .i_windows{ background-image: url(../images/icons/dark/windows.png);}
nav ul li.i_winner_podium a span, .i_winner_podium{ background-image: url(../images/icons/dark/winner_podium.png);}
nav ul li.i_wizard a span, .i_wizard{ background-image: url(../images/icons/dark/wizard.png);}
nav ul li.i_word_document a span, .i_word_document{ background-image: url(../images/icons/dark/word_document.png);}
nav ul li.i_word_documents a span, .i_word_documents{ background-image: url(../images/icons/dark/word_documents.png);}
nav ul li.i_wordpress a span, .i_wordpress{ background-image: url(../images/icons/dark/wordpress.png);}
nav ul li.i_wordpress_2 a span, .i_wordpress_2{ background-image: url(../images/icons/dark/wordpress_2.png);}
nav ul li.i_youtube a span, .i_youtube{ background-image: url(../images/icons/dark/youtube.png);}
nav ul li.i_youtube_2 a span, .i_youtube_2{ background-image: url(../images/icons/dark/youtube_2.png);}
nav ul li.i_zip_file a span, .i_zip_file{ background-image: url(../images/icons/dark/zip_file.png);}
nav ul li.i_zip_files a span, .i_zip_files{ background-image: url(../images/icons/dark/zip_files.png);}

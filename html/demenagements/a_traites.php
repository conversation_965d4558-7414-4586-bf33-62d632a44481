<?php
$inventaire = Inventaires::getInventaireCourant();
$inventaire_id = $inventaire->id;
$url = 'index.php?section=demenagements&module=a_traites';
$key_filtre = 'filtre_inventaires_liste_demenagements_a_traites';
$doNotDisplayFiltre = true;

$sel_inventaire_ligne_ids = $_SESSION[$key_filtre]['sel_inventaire_ligne_ids'];

$ids_to_exclude = [];
$lignes_assocs = InventairesLignes::getListe(['ids' => $sel_inventaire_ligne_ids]);
foreach ($lignes_assocs as $i => $ligne) {
    $ids_to_exclude[] = $ligne->id;
}

include('inventaires/filtre.inc.php');

$liste_lignes = array_merge($lignes_assocs, $lignes);

$formulaires_listes = EquipementsBioMedicauxTypes::getFormulairesListes('EquipementsBioMedicauxTypes');
$lignesForTabledata = [];
foreach($liste_lignes as $ligne) {
    $checked = in_array($ligne->id, $sel_inventaire_ligne_ids) ? ' checked="checked"' : '';
    $lignesForTabledata[] = [
        '<div class="inventaire_ligne_tooltip" data-numero_inventaire="' . $ligne->numero_inventaire . '" data-inventaire_id="' . $ligne->inventaire_id . '">'
                    . $ligne->numero_inventaire . '</div>',
        $ligne->code,
        $ligne->description,
       '<div class="inventaire_locaux_tooltip" data-numero_local="' . $ligne->numero_local . '" data-inventaire_id="' . $ligne->inventaire_id . '">'
                . $ligne->numero_local . '</div>',
       '<div class="tooltip_struct" data-fiche_id="' . $ligne->fiche_id . '">'
                . $ligne->local . '</div>',
        '<a href="index.php?section=admin&module=popup_listes_param&fiche_id=' . $ligne->fiche_id . '&formulaire_liste_id=' . $formulaires_listes[0]->id . '&liste_item_id=' . $ligne->equipement_bio_medicaux_type_id . '" class="fancy add">'
                . $ligne->equipement . '</a>',
        ($ligne->is_actif ? txt('Visible') :txt( 'Non-visible')),
        '<input type="checkbox" name="inventaires_lignes[]" id="check_' . $ligne->id . '" ' . $checked . ' class="checks" value="' . $ligne->id . '" />'
    ];
}
print json_encode(['data' => $lignesForTabledata]);
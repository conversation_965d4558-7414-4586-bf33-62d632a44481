<?
$is_print = isset($_GET['print']);
if ($is_print) {
    $noHeader = 1;
    $isPDF = true;
    include('config/header.inc.php');
    ?>
    <script>
		window.onload = function () {
			window.print();
		}
    </script>
    <style>
        * {
            background-color: white!important;
        }
        #procedure td, #taches td {
            text-align: left;
            border: none !important;
        }

        #procedure th {
            font-weight: bold;
            text-align: left;
            border: none !important;
        }
        table td, table th {
            padding: 3px 3px;
        }
    </style>
    <?
}

if (!$is_print) {
    include('config/header.inc.php');
}

$cedule = new CedulesDemenagements(intval($_GET['id'] ?? 0));
?>
    <div style="padding: 25px;">
        <table id="procedure">
            <tr>
                <td width="150px">
                    <img src="Docmatic.png" width="105"
                         style="position: relative; top: -1px; left: 3px; display: inline-block;"/>
                </td>
                <td style="text-align: right!important;" valign="top">
                    <h1 style="margin-top: 0px!important;"><?= $cedule->code ?></h1>
                </td>
            </tr>
        </table>

        <table id="procedure" cellspacing="0" cellpadding="0" border="0" style="margin: 25px 0 5px 0px!important; page-break-inside:avoid;">
            <tr>
                <td style="font-weight: bold;" width="175">
                    <?= txt('Description:') ?>
                </td>
                <td style="">
                    <?= $cedule->description ?>
                </td>
            </tr>
            <tr>
                <td style="font-weight: bold;" width="175">
                    <?= txt('Date début:') ?>
                </td>
                <td style="">
                    <?= $cedule->date_debut ?>
                </td>
            </tr>
            <tr>
                <td style="font-weight: bold;" width="175">
                    <?= txt('Date fin:') ?>
                </td>
                <td style="">
                    <?= $cedule->date_fin ?>
                </td>
            </tr>
            <tr>
                <td style="font-weight: bold;" width="175">
                    <?= txt('Commentaire:') ?>
                </td>
                <td style="">
                    <?= $cedule->commentaire ?>
                </td>
            </tr>
        </table>

        <div style="page-break-inside:avoid;">
            <h2 class="blocsFicheTitre" style="height: 30px; margin: 25px 20px 25px 0px!important;">
                <?= txt('Instructions') ?>
            </h2>

            <table id="procedure" width="100%">
                <?
                foreach ($cedule->instructions as $instruction) {
                    ?>
                    <tr>
                        <td style="padding-left: 15px;">
                            <li><?= $instruction->instruction ?></li>
                        </td>
                    </tr>
                    <?
                }
                ?>
            </table>
        </div>

        <div style="page-break-inside:avoid;">
            <h2 class="blocsFicheTitre" style="height: 30px; margin: 25px 20px 25px 0px!important;">
                <?= txt('Équipements à déménager') ?>
            </h2>

            <table id="taches" width="100%">
                <?
                $nb = count($cedule->inventaires_lignes);
                $iter = 1;
                foreach ($cedule->inventaires_lignes as $ligne) {
                    ?>
                    <tr>
                        <td width="100px">
                            <?= txt('Équipement:') ?>
                        </td>
                        <td colspan="2">
                            <?= $ligne->equipement_externe ?>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <?= txt('Provenance:') ?>
                        </td>
                        <td colspan="2">
                            <?= $ligne->numero_local ?>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <?= txt('Destination:') ?>
                        </td>
                        <td colspan="2">
                            <?= $ligne->local ?>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <?= txt('Commentaire:') ?>
                        </td>
                        <td style="border-bottom: solid 1px!important;">
                            &nbsp;
                        </td>
                        <td style="text-align: center!important;" width="20%">
                            <input type="radio"/><label>&nbsp;<?= txt('Oui') ?></label>
                            <input type="radio"/><label>&nbsp;<?= txt('Non') ?></label>
                        </td>
                    </tr>
                    <?
                    if ($iter++ < $nb) {
                        ?>
                        <tr>
                            <td colspan="3">
                                &nbsp;
                            </td>
                        </tr>
                        <?
                    }
                }
                ?>
            </table>
        </div>

        <div style="page-break-inside:avoid;">
            <h2 class="blocsFicheTitre" style="height: 30px; margin: 25px 20px 100px 0px!important;">
                <?= txt('Commentaires ') ?><span style="font-size: 12px!important;"><?= txt(
                        '(veuillez inscrire toute observation pertinente)'
                    ) ?></span>
            </h2>
        </div>

        <div style="page-break-inside:avoid;">
            <h2 class="blocsFicheTitre" style="height: 30px; margin: 55px 20px 25px 0px!important;">
                <?= txt('Répondant') ?>
            </h2>

            <table id="procedure" style="margin: 0 0 0 20px;">
                <tr>
                    <td style="font-weight: bold; padding-top: 5px; height: 25px;">
                        <?= txt('Nom (lettres moulées):') ?>
                    </td>
                    <td style="padding-top: 5px; height: 15px;">
                    </td>
                </tr>
                <tr>
                    <td style="font-weight: bold; padding-top: 5px; height: 25px;">
                        <?= txt('Signature:') ?>
                    </td>
                    <td style="padding-top: 5px;">
                    </td>
                </tr>
                <tr>
                    <td style="font-weight: bold; padding-top: 5px; height: 25px;">
                        <?= txt('Date de fin de l\'inspection:') ?>
                    </td>
                    <td style="padding-top: 5px;">
                    </td>
                </tr>
            </table>
        </div>

    </div>
<?
include('config/footer.inc.php');
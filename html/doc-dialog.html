<!doctype html>
<html lang="en-us">
<head>
	<meta charset="utf-8">
	
	<title>White Label - a full featured Admin Skin</title>
	
	<meta name="description" content="">
	<meta name="author" content="revaxarts.com">
	
	
	<!-- Google Font and style definitions -->
	<link rel="stylesheet" href="http://fonts.googleapis.com/css?family=PT+Sans:regular,bold">
	<link rel="stylesheet" href="css/style.css">
	
	<!-- include the skins (change to dark if you like) -->
	<link rel="stylesheet" href="css/light/theme.css" id="themestyle">
	<!-- <link rel="stylesheet" href="css/dark/theme.css" id="themestyle"> -->
	
	<!--[if lt IE 9]>
	<script src="http://html5shim.googlecode.com/svn/trunk/html5.js"></script>
	<link rel="stylesheet" href="css/ie.css">
	<![endif]-->
	
	<!-- Apple iOS and Android stuff -->
	<meta name="apple-mobile-web-app-capable" content="no">
	<meta name="apple-mobile-web-app-status-bar-style" content="black">
	<link rel="apple-touch-icon-precomposed" href="apple-touch-icon-precomposed.png">
	
	<!-- Apple iOS and Android stuff - don't remove! -->
	<meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=no,maximum-scale=1">
	
	<!-- Use Google CDN for jQuery and jQuery UI -->
	<script src="https://ajax.googleapis.com/ajax/libs/jquery/1.7.1/jquery.min.js"></script>
	<script src="https://ajax.googleapis.com/ajax/libs/jqueryui/1.8.12/jquery-ui.min.js"></script>
	
	<!-- Loading JS Files this way is not recommended! Merge them but keep their order -->
	
	<!-- some basic functions -->
	<script src="js/functions.js"></script>
		
	<!-- all Third Party Plugins and Whitelabel Plugins -->
	<script src="js/plugins.js"></script>
	<script src="js/editor.js"></script>
	<script src="js/calendar.js"></script>
	<script src="js/flot.js"></script>
	<script src="js/elfinder.js"></script>
	<script src="js/datatables.js"></script>
	<script src="js/wl_Alert.js"></script>
	<script src="js/wl_Autocomplete.js"></script>
	<script src="js/wl_Breadcrumb.js"></script>
	<script src="js/wl_Calendar.js"></script>
	<script src="js/wl_Chart.js"></script>
	<script src="js/wl_Color.js"></script>
	<script src="js/wl_Date.js"></script>
	<script src="js/wl_Editor.js"></script>
	<script src="js/wl_File.js"></script>
	<script src="js/wl_Dialog.js"></script>
	<script src="js/wl_Fileexplorer.js"></script>
	<script src="js/wl_Form.js"></script>
	<script src="js/wl_Gallery.js"></script>
	<script src="js/wl_Multiselect.js"></script>
	<script src="js/wl_Number.js"></script>
	<script src="js/wl_Password.js"></script>
	<script src="js/wl_Slider.js"></script>
	<script src="js/wl_Store.js"></script>
	<script src="js/wl_Time.js"></script>
	<script src="js/wl_Valid.js"></script>
	<script src="js/wl_Widget.js"></script>
	
	<!-- configuration to overwrite settings -->
	<script src="js/config.js"></script>
		
	<!-- the script which handles all the access to plugins etc... -->
	<script src="js/script.js"></script>
	
	
</head>
<body>
				<div id="pageoptions">
			<ul>
				<li><a href="login.html">Logout</a></li>
				<li><a href="#" id="wl_config">Configuration</a></li>
				<li><a href="#">Settings</a></li>
			</ul>
			<div>
						<h3>Place for some configs</h3>
						<p>Li Europan lingues es membres del sam familie. Lor separat existentie es un myth. Por scientie, musica, sport etc, litot Europa usa li sam vocabular. Li lingues differe solmen in li grammatica, li pronunciation e li plu commun vocabules. Omnicos directe al desirabilite de un nov lingua franca: On refusa continuar payar custosi traductores.</p>
			</div>
		</div>

			<header>
		<div id="logo">
			<a href="dashboard.html">Logo Here</a>
		</div>
		<div id="header">
			<ul id="headernav">
				<li><ul>
					<li><a href="icons.html">Icons</a><span>300+</span></li>
					<li><a href="#">Submenu</a><span>4</span>
						<ul>
							<li><a href="#">Just</a></li>
							<li><a href="#">another</a></li>
							<li><a href="#">Dropdown</a></li>
							<li><a href="#">Menu</a></li>
						</ul>
					</li>
					<li><a href="login.html">Login</a></li>
					<li><a href="wizard.html">Wizard</a><span>Bonus</span></li>
					<li><a href="#">Errorpage</a><span>new</span>
						<ul>
							<li><a href="error-403.html">403</a></li>
							<li><a href="error-404.html">404</a></li>
							<li><a href="error-405.html">405</a></li>
							<li><a href="error-500.html">500</a></li>
							<li><a href="error-503.html">503</a></li>
						</ul>
					</li>
				</ul></li>
			</ul>
			<div id="searchbox">
				<form id="searchform" autocomplete="off">
					<input type="search" name="query" id="search" placeholder="Search">
				</form>
			</div>
			<ul id="searchboxresult">
			</ul>
		</div>
	</header>

				<nav>
			<ul id="nav">
				<li class="i_house"><a href="dashboard.html"><span>Dashboard</span></a></li>
				<li class="i_book"><a><span>Documentation</span></a>
					<ul>
						<li><a href="doc-alert.html"><span>Alert Boxes</span></a></li>
						<li><a href="doc-breadcrumb.html"><span>Breadcrumb</span></a></li>
						<li><a href="doc-calendar.html"><span>Calendar</span></a></li>
						<li><a href="doc-charts.html"><span>Charts</span></a></li>
						<li><a href="doc-dialog.html"><span>Dialog</span></a></li>
						<li><a href="doc-editor.html"><span>Editor</span></a></li>
						<li><a href="doc-file.html"><span>File</span></a></li>
						<li><a href="doc-fileexplorer.html"><span>Fileexplorer</span></a></li>
						<li><a href="doc-form.html"><span>Form</span></a></li>
						<li><a href="doc-gallery.html"><span>Gallery</span></a></li>
						<li><a href="doc-inputfields.html"><span>Inputfields</span></a></li>
						<li><a href="doc-slider.html"><span>Slider</span></a></li>
						<li><a href="doc-store.html"><span>Store</span></a></li>
						<li><a href="doc-widget.html"><span>Widget</span></a></li>
					</ul>
				</li>
				<li class="i_create_write"><a href="form.html"><span>Form</span></a></li>
				<li class="i_graph"><a href="charts.html"><span>Charts</span></a></li>
				<li class="i_images"><a href="gallery.html"><span>Gallery</span></a></li>
				<li class="i_blocks_images"><a href="widgets.html"><span>Widgets</span></a></li>
				<li class="i_breadcrumb"><a href="breadcrumb.html"><span>Breadcrumb</span></a></li>
				<li class="i_file_cabinet"><a href="fileexplorer.html"><span>Fileexplorer</span></a></li>
				<li class="i_calendar_day"><a href="calendar.html"><span>Calendar</span></a></li>
				<li class="i_speech_bubbles_2"><a href="dialogs_and_buttons.html"><span>Dialogs &amp; Buttons</span></a></li>
				<li class="i_table"><a href="datatable.html"><span>Table</span></a></li>
				<li class="i_typo"><a href="typo.html"><span>Typo</span></a></li>
				<li class="i_grid"><a href="grid.html"><span>Grid</span></a></li>
			</ul>
		</nav>
		
			
		<section id="content">
		
			<div class="g12">
			
			<h1>Dialog <span title="current version: 1.1">v 1.1</span></h1>
			<p>Replaces the nativ Dialogs, Alertboxes Confirmations and Promptboxes with the jQuery UI dialog</p>
			
			<h3>Options</h3>
			<table class="documentation">
			<thead>
				<tr><th>option</th><th>default</th><th>possible values</th><th>description</th><th>since</th><th>info</th></tr>
			</thead>
			<tbody>
			<tr><th>nativ</th><td>false</td><td>true | false</td>
			<td>Use nativ dialogs instead of jQuery</td><td>1.0</td>
			</tr>
			<tr><th>text</th><td>texts</td><td>object</td>
			<td>all used text. Header, OK button, Cancel Button</td><td>1.0</td>
			</tr>
			<tr><td colspan="6">Check out the <a href="http://jqueryui.com/demos/dialog/">jQuery dialog options</a> for the all options</td>
			</tr>
			</tbody>
			</table>
			
			<h3>Methods</h3>
			<p>Methods are not available on native dialogs!</p>
			<table class="documentation">
			<thead>
				<tr><th>method name</th><th>default</th><th>arguments</th><th>description</th><th>since</th><th>info</th></tr>
			</thead>
			<tbody>
			<tr><th>setHeader</th><td>''</td><td>String</td>
			<td>Sets the Header of the message</td><td>1.1</td>
			</tr>
			<tr><th>setBody</th><td>''</td><td>String</td>
			<td>Sets the Body of the message</td><td>1.1</td>
			</tr>
			<tr><th>close</th><td>null</td><td>function</td>
			<td>closes the messagebox. Argument can be a callback function which get'S trigger after the message is closed</td><td>1.1</td>
			</tr>
			<tr><th>closeAll</th><td>null</td><td>function</td>
			<td>closes all open messagebox. Argument can be a callback function which get'S trigger after the message is closed</td><td>1.1</td>
			</tr>
			</tbody>
			</table>
			
			<h3>Usage</h3>
<pre>$.alert('This is a simple Message');</pre>
<pre>$.prompt('text', 'inputvalue', [callback [,cancelcallback]);</pre>
<pre>$.confirm('text', [callback [,cancelcallback]);</pre>
			<h3>Usage with public methods</h3>
<pre>var dialog = $.alert('This message can be accessed via public methods');
dialog.setHeader('Set a header');
dialog.setBody('Set a Body');
dialog.close(function(){ // callback function });
</pre>
			<h3>Examples</h3>
			<button id="dialog_switch">switch to nativ dialogs</button>
			<hr>

			<button id="dialog">Dialog (alert)</button>
			<button id="dialog_confirm">Confirm Dialog (confirm)</button>
			<button id="dialog_methods">Public Methods</button>
			<button id="dialog_prompt">Modal Prompt (prompt)</button>

			<div class="alert info">use <code>nativ = (window.navigator.standalone === undefined) ? true : false</code> to use nativ dialogs only on iOS</div>
			
			<hr>
			
			<h1>Messages <span title="current version: 1.1">v 1.1</span></h1>
			<p>Displays a message on the top right corner</p>
			
			<h3>Options</h3>
			<table class="documentation">
			<thead>
				<tr><th>option</th><th>default</th><th>possible values</th><th>description</th><th>since</th><th>info</th></tr>
			</thead>
			<tbody>
			<tr><th>header</th><td>null</td><td>String</td>
			<td>Defines the Header of the message</td><td>1.0</td>
			</tr>
			<tr><th>live</th><td>5000</td><td>integer</td>
			<td>Defines the lifetime in milliseconds</td><td>1.0</td>
			</tr>
			<tr><th>topoffset</th><td>90</td><td>integer</td>
			<td>Defines the top offset of the boxes if the window isn't scrolled</td><td>1.0</td>
			</tr>
			<tr><th>fadeTime</th><td>500</td><td>integer</td>
			<td>Defines the time to fade out the boxes in milliseconds</td><td>1.0</td>
			</tr>
			<tr><th>sticky</th><td>false</td><td>true | false</td>
			<td>ignores the lifetime. A click on the close button is required</td><td>1.0</td>
			</tr>
			</tr>
			</tbody>
			</table>
			
			<h3>Methods</h3>
			<table class="documentation">
			<thead>
				<tr><th>method name</th><th>default</th><th>arguments</th><th>description</th><th>since</th><th>info</th></tr>
			</thead>
			<tbody>
			<tr><th>setHeader</th><td>''</td><td>String</td>
			<td>Sets the Header of the message</td><td>1.1</td>
			</tr>
			<tr><th>setBody</th><td>''</td><td>String</td>
			<td>Sets the Body of the message</td><td>1.1</td>
			</tr>
			<tr><th>close</th><td>null</td><td>function</td>
			<td>closes the messagebox. Argument can be a callback function which get'S trigger after the message is closed</td><td>1.1</td>
			</tr>
			<tr><th>closeAll</th><td>null</td><td>function</td>
			<td>closes all open messagebox. Argument can be a callback function which get'S trigger after the message is closed</td><td>1.1</td>
			</tr>
			</tbody>
			</table>
	
			
			<h3>Usage</h3>
<pre>$.msg('This is a simple Message');
$.msg("This Message will stay until you click the cross",{sticky:true});
$.msg("This is with a custom Header",{header:'Custom Header'});
$.msg("This stays exactly 10 seconds",{live:10000});
</pre>
			<h3>Usage with public methods</h3>
<pre>var message = $.msg('This message can be accessed via public methods');
message.setHeader('Set a header');
message.setBody('Set a Body');
message.close(function(){ // callback function });
</pre>

			<h3>Examples</h3>
			<button id="message">Simple Message</button>
			<button id="message_sticky">Sticky Message</button>
			<button id="message_header">Custom Header</button>
			<button id="message_delay">Different Timeout</button>
			<button id="message_methods">Public Methods</button>
			
			</div>
		</section>
		<footer>Copyright by revaxarts.com 2012</footer>
</body>
</html>
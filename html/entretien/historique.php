<?
$is_print = isset($_GET['print']);
if($is_print)
{
    $noHeader = 1;
    $isPDF = true;
    include('config/header.inc.php');
?>
    <script>
        window.onload = function() { window.print(); }
    </script>
    <style>
    #logs td {
        text-align: left;
        padding: 0px 5px;
    }
    #logs th {
        text-weight: bold;
        text-align: left;
    }
    </style>    
<?
}

if(!$is_print)
{
include('config/header.inc.php');
?>
    <script>
        $(document).ready(function(){

            $(document).find('span.datepicker').datetimepicker({
                timepicker:false,
                inline:true,
                format:'Y-m-d',
                lang:'fr',
				scrollMonth : false,
				scrollInput : false,
                onSelectDate: function(ct,$i){

                    $i.parent().parent().find('select.sel_date').append('<option value="' + ct.dateFormat('Y-m-d') + '" selected="selected" class="date_fixe">' + ct.dateFormat('Y-m-d') + '</option>');
                    $i.parent().hide();

                }
            });
    
            $(document).on('change', '#marque_id', function(){
                
                var url = 'index.php?section=fiche&module=wizard';
                $.get(url, { action: 'getModelesFromMarque', marque_id: this.value }, function(html){
                    $('#modele_id').html(html);
                });

            });
            
            $(document).on('click', '#auto_planif', function(){
                
                var url = $(this).attr('href');
                $.get(url, {  }, function(text){
                    alert(text);
                    window.location = window.location;
                });
                return false;
                
            });
            
            $(document).on('click', '#filter_btn', function(){
                
                $('#historique_form').toggle(400);
                
            });
        
            load_fiches();

        });

        function load_fiches()
        {
            var fiche_id_sel = $('#fiche_id').val();
            var url = 'index.php?section=fiche&module=index';
            $.get(url, { action: 'getFichesList', term: '', batiment_id: '', fiche_type_id: '', fiche_sous_type_id: '', numero_lot: '', code_alternatif: '', id_revit: '', id_codebook: '', procedure_entretien_id: '', selected: '', disable_fitre_avance: 1 }, function(json_data){

                loading_dynamic_combobox('fiche_id', fiche_id_sel, json_data);

            }, 'json');
        }
    </script>
    <style>
    #historique td {
        text-align: left;
        padding: 0px 5px;
    }
    #historique th {
        text-weight: bold;
        text-align: left;
    }
    select {
        width: 210px;
    }
    </style>    
<?
}

$default_usager_attribue_id = havePermission('entretien_completion') && !isMaster() ? getIdUsager() : 0;

$filtre = &$_SESSION['filtre_historique'];
$filtre['date_debut'] = isset($filtre['date_debut']) ? $filtre['date_debut'] : '';
$filtre['date_fin'] = isset($filtre['date_fin']) ? $filtre['date_fin'] : '';
$filtre['discipline_id'] = isset($filtre['discipline_id']) ? $filtre['discipline_id'] : 0;
$filtre['periode_id'] = isset($filtre['periode_id']) ? $filtre['periode_id'] : 0;
$filtre['type'] = isset($filtre['type']) ? $filtre['type'] : '';
$filtre['fiche_id'] = isset($filtre['fiche_id']) ? $filtre['fiche_id'] : 0;
$filtre['marque_id'] = isset($filtre['marque_id']) ? $filtre['marque_id'] : 0;
$filtre['modele_id'] = isset($filtre['modele_id']) ? $filtre['modele_id'] : 0;
$filtre['nomenclature_id'] = isset($filtre['nomenclature_id']) ? $filtre['nomenclature_id'] : 0;
$filtre['order_by_col'] = isset($filtre['order_by_col']) ? $filtre['order_by_col'] : 'date_travaux';
$filtre['order_by_dir'] = isset($filtre['order_by_dir']) ? $filtre['order_by_dir'] : 'desc';
$filtre['usager_attribue_id'] = isset($filtre['usager_attribue_id']) ? $filtre['usager_attribue_id'] : $default_usager_attribue_id;
$filtre['no_bon_travail'] = isset($filtre['no_bon_travail']) ? $filtre['no_bon_travail'] : '';

$page = 1;
if(isset($_GET['page']) && intval($_GET['page']) > 0)
{
    $page = intval($_GET['page']);
}

if(isset($_POST['date_debut']))
{
    $filtre['date_debut'] = $_POST['date_debut'];
    $page = 1;
}

if(isset($_POST['date_fin']))
{
    $filtre['date_fin'] = $_POST['date_fin'];
    $page = 1;
}

if(isset($_POST['discipline_id']))
{
    $filtre['discipline_id'] = intval($_POST['discipline_id']);
    $page = 1;
}

if(isset($_POST['periode_id']))
{
    $filtre['periode_id'] = intval($_POST['periode_id']);
    $page = 1;
}

if(isset($_POST['type']))
{
    $filtre['type'] = $_POST['type'];
    $page = 1;
}

if(isset($_POST['fiche_id']))
{
    $filtre['fiche_id'] = intval($_POST['fiche_id']);
    $page = 1;
}

if(isset($_POST['marque_id']))
{
    $filtre['marque_id'] = intval($_POST['marque_id']);
    $page = 1;
}

if(isset($_POST['modele_id']))
{
    $filtre['modele_id'] = intval($_POST['modele_id']);
    $page = 1;
}

if(isset($_POST['nomenclature_id']))
{
    $filtre['nomenclature_id'] = intval($_POST['nomenclature_id']);
    $page = 1;
}

if(isset($_POST['usager_attribue_id']))
{
    $filtre['usager_attribue_id'] = intval($_POST['usager_attribue_id']);
    $page = 1;
}

if(isset($_POST['no_bon_travail']))
{
    $filtre['no_bon_travail'] = $_POST['no_bon_travail'];
    $page = 1;
}

if(isset($_GET['order_by_col']) && isset($_GET['order_by_dir']))
{
    $filtre['order_by_col'] = $_GET['order_by_col'];
    $filtre['order_by_dir'] = $_GET['order_by_dir'];
    $page = 1;
}

$filtre_applique = false;
if(dm_strlen($filtre['date_debut']) > 0)
{
    $filtre_applique = true;
}
if(dm_strlen($filtre['date_fin']) > 0)
{
    $filtre_applique = true;
}
if(intval($filtre['discipline_id']) > 0)
{
    $filtre_applique = true;
}
if(intval($filtre['periode_id']) > 0)
{
    $filtre_applique = true;
}
if(dm_strlen($filtre['type']) > 0)
{
    $filtre_applique = true;
}
if(intval($filtre['fiche_id']) > 0)
{
    $filtre_applique = true;
}
if(intval($filtre['marque_id']) > 0)
{
    $filtre_applique = true;
}
if(intval($filtre['modele_id']) > 0)
{
    $filtre_applique = true;
}
if(intval($filtre['nomenclature_id']) > 0)
{
    $filtre_applique = true;
}
if(dm_strlen($filtre['no_bon_travail']) > 0)
{
    $filtre_applique = true;
}
if(havePermission('admin_procedures_entretiens_planifiees'))
{
    if(intval($filtre['usager_attribue_id']) > 0)
    {
        $filtre_applique = true;
    }
}

$nb_par_page = 20;
$nb_total = ProcedureEntretienPlanifiee::getCount($filtre);

$nb_pages = ceil($nb_total / $nb_par_page);
$limit = $page == 1 ? 0 : ($page-1) * $nb_par_page;

$sql_limit = $is_print ? '' : "LIMIT $limit, $nb_par_page";

if(isset($_GET['is_projection']))
{
    $procedures = ProcedureEntretienPlanifiee::getListeProjections(date('Y-m', strtotime('- 1 month')).'-01', date('Y-m', strtotime('+ 2 years')).'-01');
}
else
{
    $procedures = ProcedureEntretienPlanifiee::getListe($filtre, $filtre['order_by_col'].' '.$filtre['order_by_dir'], $sql_limit);
}

$types = ProcedureEntretien::getTypes();
if(!$is_print)
{
?>

<h1 class="ficheTitre" style="margin-bottom: 20px;">
    
    <?=(havePermission('admin_procedures_ent retiens_planifiees') ? txt('Planification de l\'entretien') : txt('Bons de travail'))?>
    <img src="css/images/icons/dark/filter.png" style="cursor: pointer; position: relative; top: 4px;" id="filter_btn" />
    
    <a href="index.php?section=entretien&module=historique&print=1" style="position: relative; top: 4px;" title="<?=txt("Imprimer")?>" target="iframe">
        <img src="css/images/icons/dark/printer.png" /></a>
<?
    if(havePermission('admin_procedures_entretiens_planifiees'))
    {
?>
        <a href="generer_tache_planification_auto.php" style="position: relative; top: 4px;" title="<?=txt("Autogénérer la planification")?>" id="auto_planif">
            <img src="css/images/icons/dark/wizard.png" /></a>
<?
    }
?>
    
    <div style="float: right;">
<?
        if(GestionnaireRoute::instance()->haveAccesRoute('entretien-procedures'))
        {
?>
            <a class="btn" href="index.php?section=entretien&module=procedures"><?=txt('Procédures')?></a>
<?
        }

        if (GestionnaireRoute::instance()->haveAccesRoute('entretien-historique')) {
?>
            <a class="btn actif" href="index.php?section=entretien&module=historique"><?=txt('Planification')?></a>
            <?
        }
            ?>
        <a class="btn" href="index.php?section=entretien&module=index"><?=txt('Calendrier')?></a>
    </div>
    
</h1>
<form action="index.php?section=entretien&module=historique" method="post" style="margin: 0 20px 20px 0; <?=($filtre_applique ? '' : 'display: none;')?>" id="historique_form">

    <?=txt('Entre')?>&nbsp;

    <input id="date_debut" type="text" name="date_debut" value="<?=$filtre['date_debut']?>" class="date hasDatepicker" style="width: 75px!important; text-align: center;" maxlength="10" />&nbsp;

    <?=txt('et')?>&nbsp;

    <input id="date_fin" type="text" name="date_fin" value="<?=$filtre['date_fin']?>" class="date hasDatepicker" style="width: 75px!important; text-align: center;" maxlength="10" />&nbsp;

    <br><br>
    
    <span style=" height: 25px;">
        <select name="type">
            <option value=""><?=txt('- Type -')?></option>
<?
            foreach($types as $code => $type)
            {
                $sel = $code == $filtre['type'] ? ' selected="selected"' : '';
?>
                <option value="<?=$code?>"<?=$sel?>><?=$type['nom'].' '.$type['desc']?></option>
<?
            }
?>
        </select>
    </span>
    
    <span style="margin-left: 20px; height: 25px;">
        <select name="discipline_id">
            <option value=""><?=txt('- Discipline -')?></option>
<?
            Discipline::getOptions(array(), $filtre['discipline_id']);
?>
        </select>
    </span>
    
    <span style="margin-left: 20px; height: 25px;">
        <select name="periode_id">
            <option value=""><?=txt('- Période -')?></option>
<?
            Periode::getOptions(array(), $filtre['periode_id']);
?>
        </select>
    </span>
    
    <span style="margin-left: 20px; height: 25px;">
        <select name="nomenclature_id">
            <option value=""><?=txt('- Nomenclature -')?></option>
<?
            Nomenclature::getOptions(array(), $filtre['nomenclature_id']);
?>
        </select>
    </span>
    
    <span style="margin-left: 20px; height: 25px;">
        <input type="text" id="no_bon_travail" name="no_bon_travail" value="<?=$filtre['no_bon_travail']?>" placeholder="<?=txt('Numéro')?>" style="width: 210px!important;" />
    </span>
    
    <br><br>
    
    <span style="height: 25px;">
        <select id="marque_id" name="marque_id">
            <option value=""><?=txt('- Marque -')?></option>
<?
            Marque::getOptions(array(), $filtre['marque_id']);
?>
        </select>
    </span>
    
    <span style="margin-left: 20px; height: 25px;">
        <select id="modele_id" name="modele_id">
            <option value=""><?=txt('- Modele -')?></option>
<?
            Modele::getOptions(array('marque_id' => $filtre['marque_id']), $filtre['modele_id']);
?>
        </select>
    </span>
    
    <span style="margin-left: 20px; height: 25px;">
        <select id="fiche_id" name="fiche_id" class="lazy_loaded_fiches" disabled data-textblank="<?=txt('- Fiche -')?>">
            <option value=""><?=txt('- Fiche -')?></option>
<?
            Fiches::getOptions(array('ids' => array($filtre['fiche_id']), 'no_filtre_global' => 1), $filtre['fiche_id']);
?>
        </select>
        
    </span>
<?
    if(havePermission('admin_procedures_entretiens_planifiees'))
    {
?>
        <span style="margin-left: 20px; height: 25px;">
            <select name="usager_attribue_id">
                <option value=""><?=txt('- Responsable -')?></option>
<?
                Usager::getOptions(array('permission_code' => 'entretien_completion', 'discipline_id' => ($filtre['discipline_id'] == 0 ? null : $filtre['discipline_id'])), $filtre['usager_attribue_id']);
?>
            </select>
        </span>
<?
    }
?>
    <span style="margin-left: 20px; text-align: right; width: 250px;">
        
        <input type="submit" class="filtrer" name="filtrer" value="<?=txt('Filtrer')?>" style="min-height: 20px!important; padding: 0 20px;" />
        
    </span>
    
</form>
<?
}

$status = ProcedureEntretienPlanifiee::getStatuts();
if($is_print)
{
?>
    <h6><?=(havePermission('admin_procedures_entretiens_planifiees') ? txt('Planification de l\'entretien') : txt('Bons de travail'))?></h6>
<?
}
?>

<table id="historique">
<tr><th width="50" class="<?=($filtre['order_by_col'] == 'no_bon_travail' ? 'ordered' : '')?>">
    <?=getOrderByLink(txt('Numéro'), 'index.php?section=entretien&module=historique', 'no_bon_travail', $filtre['order_by_col'], $filtre['order_by_dir'], $is_print)?>
</th><th width="150" class="<?=($filtre['order_by_col'] == 'procedure_entretien' ? 'ordered' : '')?>">
    <?=getOrderByLink(txt('Procédure'), 'index.php?section=entretien&module=historique', 'procedure_entretien', $filtre['order_by_col'], $filtre['order_by_dir'], $is_print)?>
</th><th width="75" class="<?=($filtre['order_by_col'] == 'discipline' ? 'ordered' : '')?>">
    <?=getOrderByLink(txt('Discipline'), 'index.php?section=entretien&module=historique', 'discipline', $filtre['order_by_col'], $filtre['order_by_dir'], $is_print)?>
</th><th width="225">
	<?=txt('Fiche(s) liée(s)')?>
</th><th width="100" class="<?=($filtre['order_by_col'] == 'date_travaux' ? 'ordered' : '')?>">
    <?=getOrderByLink(txt('Date des travaux'), 'index.php?section=entretien&module=historique', 'date_travaux', $filtre['order_by_col'], $filtre['order_by_dir'], $is_print)?>
</th><th width="100" class="<?=($filtre['order_by_col'] == 'date_echeance' ? 'ordered' : '')?>">
    <?=getOrderByLink(txt('Date d\'échéance'), 'index.php?section=entretien&module=historique', 'date_echeance', $filtre['order_by_col'], $filtre['order_by_dir'], $is_print)?>
</th>
<?
if(havePermission('admin_procedures_entretiens_planifiees'))
{
?>
    <th width="125" class="<?=($filtre['order_by_col'] == 'usager_attribue' ? 'ordered' : '')?>">
        <?=getOrderByLink(txt('Responsable'), 'index.php?section=entretien&module=historique', 'usager_attribue', $filtre['order_by_col'], $filtre['order_by_dir'], $is_print)?>
    </th>
<?
}
?>
<th width="75" class="<?=($filtre['order_by_col'] == 'statut' ? 'ordered' : '')?>">
    <?=getOrderByLink(txt('Statut'), 'index.php?section=entretien&module=historique', 'statut', $filtre['order_by_col'], $filtre['order_by_dir'], $is_print)?>
</th>
<?
if(!$is_print)
{
?>
    <th width="77" style="text-align: center!important;">
<?
    if(GestionnaireRoute::instance()->haveAccesRoute('admin-edit-procedures_entretiens_planifiees-ajout'))
    {
?>
        <a href="index.php?section=admin&module=edit&table=procedures_entretiens_planifiees&type=client&id=0" class="fancy">
            <img src="css/images/icons/dark/plus.png" /></a>
<?
    }
?>
    </th>
<?
}
?>
</tr>
<?
$priorites = ProcedureEntretienPlanifiee::getPriorites();
$statuts = ProcedureEntretienPlanifiee::getStatuts();
foreach($procedures as $i => $procedure)
{
	?>
	<tr><td class="<?=($filtre['order_by_col'] == 'no_bon_travail' ? 'ordered' : '')?>">
		<?=(isset($procedure->date_travaux_projection) ? (dm_strlen($procedure->no_bon_travail) > 0 ? '<span style="color: gainsboro;">['.$procedure->no_bon_travail.']</span>' : '&nbsp;') : $procedure->no_bon_travail)?>
	</td><td class="<?=($filtre['order_by_col'] == 'procedure_entretien' ? 'ordered' : '')?>">
		<?=$procedure->procedure_entretien?>
	</td><td class="<?=($filtre['order_by_col'] == 'discipline' ? 'ordered' : '')?>">
		<?=$procedure->discipline?>
	</td><td>
<?
        if(isset($procedure->fiches) && is_array($procedure->fiches) && count($procedure->fiches) > 0)
        {
            $iter = 0;
            foreach($procedure->fiches as $i => $fiche)
            {
                print ($iter == 0 ? '' : ', ').$fiche->code;
                if($iter == 2)
                {
                    break;
                }
                $iter++;
            }
            if(count($procedure->fiches) > 3)
            {
                print '... <span style="font-weight: bold; color: blue;">('.count($procedure->fiches).')</span>';
            }
        }
        $date_travaux = (isset($procedure->date_travaux_projection) ? $procedure->date_travaux_projection : $procedure->date_travaux);
        $date_echeance = isset($priorites[$procedure->priorite]) ? date('Y-m-d', strtotime($date_travaux.' '.$priorites[$procedure->priorite]['formated'])) : '&nbsp;';
?>
	</td><td class="<?=($filtre['order_by_col'] == 'date_travaux' ? 'ordered' : '')?>">
		<?=$date_travaux?>
	</td><td class="<?=($filtre['order_by_col'] == 'date_echeance' ? 'ordered' : '')?>">
		<?=$date_echeance?>
	</td>
<?
    if(havePermission('admin_procedures_entretiens_planifiees'))
    {
?>
        <td class="<?=($filtre['order_by_col'] == 'usager_attribue' ? 'ordered' : '')?>">
            <?=(isset($procedure->usager_attribue) ? $procedure->usager_attribue : txt('À déterminer'))?>
        </td>
<?
    }
    
    $color = (isset($procedure->date_travaux_projection) ? $statuts['projection']['color'] : (isset($status[$procedure->code_statut]) ? $status[$procedure->code_statut]['color'] : ''));
    $nom_statut = (isset($procedure->date_travaux_projection) ? $statuts['projection']['nom'] : (isset($status[$procedure->code_statut]) ? $status[$procedure->code_statut]['nom'] : '&nbsp;'));
?>
    <td style="color: <?=$color?>; font-weight: bold;" class="<?=($filtre['order_by_col'] == 'statut' ? 'ordered' : '')?>">
		<?=$nom_statut?>
	</td>
<?
        if(!$is_print)
        {
?>
            <td style="height: 26px; text-align: center!important;">
<?
            if(GestionnaireRoute::instance()->haveAccesRoute('acquisition-locaux'))
            {
?>
                <a href="index.php?section=admin&module=edit&table=procedures_entretiens_planifiees&type=client&id=<?=$procedure->id?>" class="fancy">
                    <img src="css/images/icons/dark/create_write.png"></a>
                <a href="javascript: void(0);" class="dle_btn dle_delete_generique" tableName="procedures_entretiens_planifiees" idElement="<?=$procedure->id?>">
                    <img src="css/images/icons/dark/trashcan.png" /></a>
<?
            }
            else
            {
?>
                <a href="index.php?section=admin&module=edit&table=procedures_entretiens_planifiees&type=client&id=<?=$procedure->id?>" class="fancy">
                    <img src="css/images/icons/dark/magnifying_glass.png"></a>
<?
            }
?>
                <a href="index.php?section=entretien&module=procedure&print=1&procedure_entretien_planifiee_id=<?=$procedure->id?>" title="<?=txt("Imprimer")?>" target="iframe">
                    <img src="css/images/icons/dark/printer.png" /></a>
                
            </td>
<?
        }
?>
	</tr>
	<?
}
?>
</table>
<?
if(!$is_print)
{
	showPaginateur('index.php?section=entretien&module=historique', $limit, $nb_total, $nb_pages, $nb_par_page, $page);
}
?>
<iframe id="iframe" name="iframe" style="display: none; width: 100%;"></iframe>
<br style="clear: both;" />
<?
include('config/footer.inc.php');
?>
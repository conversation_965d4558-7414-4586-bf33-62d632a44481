<?
$is_print = isset($_GET['print']);
if($is_print)
{
    $noHeader = 1;
    $isPDF = true;
    include('config/header.inc.php');
?>
    <script>
        window.onload = function() { window.print(); }
    </script>
    <style>
    #logs td {
        text-align: left;
        padding: 0px 5px;
    }
    #logs th {
        text-weight: bold;
        text-align: left;
    }
    </style>    
<?
}

if(!$is_print)
{
include('config/header.inc.php');
?>
    <script>
    $(document).ready(function(){

        load_fiches();

        $(document).find('span.datepicker').datetimepicker({
            timepicker:false,
            inline:true,
            format:'Y-m-d',
            lang:'fr',
			scrollMonth : false,
			scrollInput : false,
            onSelectDate: function(ct,$i){

                $i.parent().parent().find('select.sel_date').append('<option value="' + ct.dateFormat('Y-m-d') + '" selected="selected" class="date_fixe">' + ct.dateFormat('Y-m-d') + '</option>');
                $i.parent().hide();

            }
        });


        $(document).on('change', '#marque_id', function(){

            var url = 'index.php?section=fiche&module=wizard';
            $.get(url, { action: 'getModelesFromMarque', marque_id: this.value }, function(html){
                $('#modele_id').html(html);
            });

        });

        $(document).on('click', '#filter_btn', function(){

            $('#procedures_form').toggle(400);

        });

    });
    
    function load_fiches()
    {
        var fiche_id_sel = $('#fiche_id').val();
        var url = 'index.php?section=fiche&module=index';
        $.get(url, { action: 'getFichesList', term: '', batiment_id: '', fiche_type_id: '', fiche_sous_type_id: '', numero_lot: '', code_alternatif: '', id_revit: '', id_codebook: '', procedure_entretien_id: '', selected: '', disable_fitre_avance: 1 }, function(json_data){

            loading_dynamic_combobox('fiche_id', fiche_id_sel, json_data);
            
        }, 'json');
    }
    </script>
    <style>
    #procedures td {
        text-align: left;
        padding: 0px 5px;
    }
    #procedures th {
        text-weight: bold;
        text-align: left;
    }
    select {
        width: 210px;
    }
    </style>    
<?
}
$filtre = &$_SESSION['filtre_procedures'];
$filtre['date_debut'] = isset($filtre['date_debut']) ? $filtre['date_debut'] : '';
$filtre['date_fin'] = isset($filtre['date_fin']) ? $filtre['date_fin'] : '';
$filtre['discipline_id'] = isset($filtre['discipline_id']) ? $filtre['discipline_id'] : 0;
$filtre['periode_id'] = isset($filtre['periode_id']) ? $filtre['periode_id'] : 0;
$filtre['type'] = isset($filtre['type']) ? $filtre['type'] : '';
$filtre['fiche_id'] = isset($filtre['fiche_id']) ? $filtre['fiche_id'] : 0;
$filtre['marque_id'] = isset($filtre['marque_id']) ? $filtre['marque_id'] : 0;
$filtre['modele_id'] = isset($filtre['modele_id']) ? $filtre['modele_id'] : 0;
$filtre['nomenclature_id'] = isset($filtre['nomenclature_id']) ? $filtre['nomenclature_id'] : 0;
$filtre['order_by_col'] = isset($filtre['order_by_col']) ? $filtre['order_by_col'] : 'nom';
$filtre['order_by_dir'] = isset($filtre['order_by_dir']) ? $filtre['order_by_dir'] : 'asc';

$page = 1;
if(isset($_GET['page']) && intval($_GET['page']) > 0)
{
    $page = intval($_GET['page']);
}

if(isset($_POST['discipline_id']))
{
    $filtre['discipline_id'] = intval($_POST['discipline_id']);
    $page = 1;
}

if(isset($_POST['periode_id']))
{
    $filtre['periode_id'] = intval($_POST['periode_id']);
    $page = 1;
}

if(isset($_POST['type']))
{
    $filtre['type'] = $_POST['type'];
    $page = 1;
}

if(isset($_POST['fiche_id']))
{
    $filtre['fiche_id'] = intval($_POST['fiche_id']);
    $page = 1;
}

if(isset($_POST['marque_id']))
{
    $filtre['marque_id'] = intval($_POST['marque_id']);
    $page = 1;
}

if(isset($_POST['modele_id']))
{
    $filtre['modele_id'] = intval($_POST['modele_id']);
    $page = 1;
}

if(isset($_POST['nomenclature_id']))
{
    $filtre['nomenclature_id'] = intval($_POST['nomenclature_id']);
    $page = 1;
}

if(isset($_GET['order_by_col']) && isset($_GET['order_by_dir']))
{
    $filtre['order_by_col'] = $_GET['order_by_col'];
    $filtre['order_by_dir'] = $_GET['order_by_dir'];
    $page = 1;
}

$filtre_applique = false;
if(intval($filtre['discipline_id']) > 0)
{
    $filtre_applique = true;
}
if(intval($filtre['periode_id']) > 0)
{
    $filtre_applique = true;
}
if(dm_strlen($filtre['type']) > 0)
{
    $filtre_applique = true;
}
if(intval($filtre['fiche_id']) > 0)
{
    $filtre_applique = true;
}
if(intval($filtre['marque_id']) > 0)
{
    $filtre_applique = true;
}
if(intval($filtre['modele_id']) > 0)
{
    $filtre_applique = true;
}
if(intval($filtre['nomenclature_id']) > 0)
{
    $filtre_applique = true;
}

$nb_par_page = 20;
$nb_total = ProcedureEntretien::getCount($filtre);

$nb_pages = ceil($nb_total / $nb_par_page);
$limit = $page == 1 ? 0 : ($page-1) * $nb_par_page;

$sql_limit = $is_print ? '' : "LIMIT $limit, $nb_par_page";

$procedures = ProcedureEntretien::getListe($filtre, $filtre['order_by_col'].' '.$filtre['order_by_dir'], $sql_limit);

$types = ProcedureEntretien::getTypes();
if(!$is_print)
{
?>
    
<h1 class="ficheTitre" style="margin-bottom: 20px;">
    <?=txt('Tableau des procédures')?>
    <img src="css/images/icons/dark/filter.png" style="cursor: pointer; position: relative; top: 4px;" id="filter_btn" />
    
    <a href="index.php?section=entretien&module=procedures&print=1" style="position: relative; top: 4px;" title="<?=txt("Imprimer")?>" target="iframe">
        <img src="css/images/icons/dark/printer.png" /></a>

    <div style="float: right;">
<?
        if(GestionnaireRoute::instance()->haveAccesRoute('entretien-procedures'))
        {
?>
            <a class="btn actif" href="index.php?section=entretien&module=procedures"><?=txt('Procédures')?></a>
<?
        }
        if (GestionnaireRoute::instance()->haveAccesRoute('entretien-historique')) {
?>
            <a class="btn" href="index.php?section=entretien&module=historique"><?=txt('Planification')?></a>
            <?
        }
            ?>
        <a class="btn" href="index.php?section=entretien&module=index"><?=txt('Calendrier')?></a>
    </div>
</h1>
<form action="index.php?section=entretien&module=procedures" method="post" style="margin: 0 20px 20px 0; <?=($filtre_applique ? '' : 'display: none;')?>" id="procedures_form">

    <span style="">
        <select name="type">
            <option value=""><?=txt('- Type -')?></option>
<?
            foreach($types as $code => $type)
            {
                $sel = $code == $filtre['type'] ? ' selected="selected"' : '';
?>
                <option value="<?=$code?>"<?=$sel?>><?=$type['nom'].' '.$type['desc']?></option>
<?
            }
?>
        </select>
    </span>
    
    <span style="margin-left: 20px;">
        <select name="discipline_id">
            <option value=""><?=txt('- Discipline -')?></option>
<?
            Discipline::getOptions(array(), $filtre['discipline_id']);
?>
        </select>
    </span>
    
    <span style="margin-left: 20px;">
        <select name="periode_id">
            <option value=""><?=txt('- Période -')?></option>
<?
            Periode::getOptions(array(), $filtre['periode_id']);
?>
        </select>
    </span>
    
    <span style="margin-left: 20px;">
        <select name="nomenclature_id">
            <option value=""><?=txt('- Nomenclature -')?></option>
<?
            Nomenclature::getOptions(array(), $filtre['nomenclature_id']);
?>
        </select>
    </span>
    
    <br><br>
    
    <span>
        <select id="marque_id" name="marque_id">
            <option value=""><?=txt('- Marque -')?></option>
<?
            Marque::getOptions(array(), $filtre['marque_id']);
?>
        </select>
    </span>
    
    <span style="margin-left: 20px;">
        <select id="modele_id" name="modele_id">
            <option value=""><?=txt('- Modele -')?></option>
<?
            Modele::getOptions(array('marque_id' => $filtre['marque_id']), $filtre['modele_id']);
?>
        </select>
    </span>
    
    <span style="margin-left: 20px;">
        <select id="fiche_id" name="fiche_id" class="lazy_loaded_fiches" disabled data-textblank="<?=txt('- Fiche -')?>">
            <option value=""><?=txt('- Fiche -')?></option>
<?
            Fiches::getOptions(array('ids' => array($filtre['fiche_id']), 'no_filtre_global' => 1), $filtre['fiche_id']);
?>
        </select>
    </span>
    
    <span style="margin-left: 20px; text-align: right; width: 250px;">
        
        <input type="submit" class="filtrer" name="filtrer" value="<?=txt('Filtrer')?>" style="min-height: 20px!important; padding: 0 20px;" />
        
    </span>
    
</form>
<?
}

if($is_print)
{
?>
    <h6><?=txt('Procédures')?></h6>
<?
}
?>

<table id="procedures">
<tr><th class="<?=($filtre['order_by_col'] == 'nom' ? 'ordered' : '')?>">
    <?=getOrderByLink(txt('Code'), 'index.php?section=entretien&module=procedures', 'nom', $filtre['order_by_col'], $filtre['order_by_dir'])?>
</th><th width="375" class="<?=($filtre['order_by_col'] == 'type' ? 'ordered' : '')?>">
    <?=getOrderByLink(txt('Type'), 'index.php?section=entretien&module=procedures', 'type', $filtre['order_by_col'], $filtre['order_by_dir'])?>
</th><th width="175" class="<?=($filtre['order_by_col'] == 'discipline' ? 'ordered' : '')?>">
    <?=getOrderByLink(txt('Discipline'), 'index.php?section=entretien&module=procedures', 'discipline', $filtre['order_by_col'], $filtre['order_by_dir'])?>
</th><th width="175" class="<?=($filtre['order_by_col'] == 'periodes.ordre' ? 'ordered' : '')?>">
    <?=getOrderByLink(txt('Période'), 'index.php?section=entretien&module=procedures', 'periodes.ordre', $filtre['order_by_col'], $filtre['order_by_dir'])?>
</th>
<?
if(!$is_print)
{
?>
    <th width="125" style="text-align: center!important;">
        <a href="index.php?section=admin&module=edit&table=procedures_entretiens&type=client&id=0" class="fancy">
            <img src="css/images/icons/dark/plus.png" /></a>
    </th>
<?
}
?>
</tr>
<?
foreach($procedures as $i => $procedure)
{
	?>
	<tr><td class="<?=($filtre['order_by_col'] == 'nom' ? 'ordered' : '')?>">
		<?=$procedure->nom?>
	</td><td class="<?=($filtre['order_by_col'] == 'type' ? 'ordered' : '')?>">
		<?=$types[$procedure->type]['nom']. ' '.$types[$procedure->type]['desc']?>
	</td><td class="<?=($filtre['order_by_col'] == 'discipline' ? 'ordered' : '')?>">
		<?=$procedure->discipline?>
	</td><td class="<?=($filtre['order_by_col'] == 'periodes.ordre' ? 'ordered' : '')?>">
		<?=$procedure->periode?>
	</td>
<?
        if(!$is_print)
        {
?>
            <td style="height: 26px; text-align: center;">
                <a href="index.php?section=admin&module=edit&table=procedures_entretiens&type=client&id=<?=$procedure->id?>" class="fancy">
                    <img src="css/images/icons/dark/create_write.png"></a>
                <a href="javascript: void(0);" class="dle_btn dle_delete_generique" tableName="procedures_entretiens" idElement="<?=$procedure->id?>">
                    <img src="css/images/icons/dark/trashcan.png" /></a>
                <a href="index.php?section=entretien&module=procedure&print=1&id=<?=$procedure->id?>" title="<?=txt("Imprimer")?>" target="iframe">
                    <img src="css/images/icons/dark/printer.png" /></a>
            </td>
<?
        }
?>
	</tr>
	<?
}
?>
</table>
<?
if(!$is_print)
{
	showPaginateur('index.php?section=entretien&module=procedures', $limit, $nb_total, $nb_pages, $nb_par_page, $page);
}
?>
<iframe id="iframe" name="iframe" style="display: none; width: 100%;"></iframe>
<br style="clear: both;" />
<?
include('config/footer.inc.php');
?>
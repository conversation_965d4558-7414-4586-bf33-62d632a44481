<?
require_once __DIR__ . '/../../vendor/autoload.php';

use PhpOffice\PhpSpreadsheet\Cell\Coordinate;
use PhpOffice\PhpSpreadsheet\IOFactory;

ini_set("memory_limit","10000M");
ini_set('max_execution_time', 30000);

$all_cols_statique = EquipementsMep::getChamps();

$mise_en_service = new MisesEnServices(intval($_GET['id']));

$etape = 'valider';

if(isset($_GET['export_doublons'])) {
	$lignes_to_import = $_SESSION['synchro_lignes_doublons'];
	unset($_SESSION['synchro_lignes_doublons']);

	$col = 0;
	$dataArray = array();
	$dataArray[0][$col++] = txt('Code de l\'équipement', false);
	foreach ($lignes_to_import as $numero_inventaire => $elements_data) {
		foreach ($elements_data as $iter => $data_to_import) {
			foreach ($data_to_import as $nom_parametre => $valeur) {
				$dataArray[0][$col++] = $nom_parametre;
			}
			break;
		}
		break;
	}

	$noLigne = 1;
	foreach ($lignes_to_import as $numero_inventaire => $elements_data) {
		foreach ($elements_data as $iter => $data_to_import) {
			$col = 0;
			$dataArray[$noLigne][$col++] = $numero_inventaire;
			foreach ($data_to_import as $nom_parametre => $valeur) {
				$dataArray[$noLigne][$col++] = $valeur['new_valeur'];
			}
			$noLigne++;
		}
		unset($lignes_to_import[$numero_inventaire]);
	}

	exportExcelFromDataArray($dataArray, 'doublons_import_inventaires', 'doublons_import_inventaires');
	exit;
} else if(isset($_POST['synchroniser'])) {
	$_SESSION['messageErreurCustom'] = 'L\'information fournie ne permet pas d\'effectuer l\'importation. Veuillez svp confirmer que votre fichier respecte les exigences minimales suivantes;

•	Le titre de la colonne Pivot est correctement orthographié « Code de l\'équipement »
•	La colonne Pivot (Code de l\'équipement) est la « Colonne A »
•	Le titre de chaque colonne est sur la « Ligne 1 »
•	Il n\'y a pas de ligne vides dans le bloc de données à importer
•	Il n\'y a pas de donnée orpheline (ex : totalisation d\'une colonne sur sa dernière ligne)
•	L\'information à importer est dans le premier onglet du fichier
•	Il n\'y a que des données brutes dans le fichier (aucune fonction de calcul)
•	Le type de fichier est .xlsx (les anciennes versions d\'Excel ne sont pas importables)

Si tout vous semble conforme et que la fonction d\'importation ne parvient toujours pas à traiter votre fichier, veuillez svp <NAME_EMAIL> pour obtenir une assistance.';

	$all_cols = $_SESSION['synchro_data']['all_cols'];
	$champs = $_SESSION['synchro_data']['champs'];
	$lignes = $_SESSION['synchro_data']['lignes_a_importer'];

    db::instance()->autocommit(false);

	if (nbErreurs() > 0) {
		$etape = 'synchroniser';
	} else {
		$champs_static_to_import = array();
		$champs_to_import = array();

		foreach ($all_cols_statique as $key => $parametre) {
			if (isset($_POST['alias_' . $key]) && dm_strlen($_POST['alias_' . $key]) > 0) {
				$parametre['key'] = $key;
				$champs_static_to_import[$_POST['alias_' . $key]] = $parametre;
			}
		}

		foreach ($all_cols as $i => $champ) {
			if (isset($_POST['alias_' . uniqueKey($champ->nom)]) && dm_strlen($_POST['alias_' . uniqueKey($champ->nom)]) > 0) {
				$champs_to_import[$_POST['alias_' . uniqueKey($champ->nom)]] = $champ;
			}
		}

		$lignes_to_import = array();
		foreach ($lignes as $col_iter => $champs) {
            $code_pivot_externe = dm_htmlspecialchars_decode($_POST['code_pivot_externe']);
			$code_equipement = $champs[$code_pivot_externe];
			$iter = isset($lignes_to_import[$code_equipement]) ? count($lignes_to_import[$code_equipement]) + 1 : 1;

            $last_marque_id = -1;
			foreach ($champs_static_to_import as $alias => $parametre) {
                $alias = dm_htmlspecialchars_decode($alias);
                $new_valeur = trim($champs[$alias] ?? '');
                if ($parametre['type'] == 'select') {
                    $where = "`" . db::tableColonneValide($parametre['colonneCompare']) . "` = ?";
                    $data = [$new_valeur];
                    if ($parametre['table'] == 'modeles') {
                        if (empty($last_marque_id)) {
                            continue;
                        }
                        $where .= " and marque_id = ?";
                        $data[] = $last_marque_id;
                    }
                    $sql = "select id 
                            from `" . db::tableColonneValide($parametre['table']) . "` 
                            where $where";
                    $idTmp = db::instance()->getOne($sql, $data);
                    if ((int)$idTmp > 0) {
                        $new_valeur = (int)$idTmp;
                    } else if (dm_strlen(dm_trim($new_valeur)) > 0) {
                        if ($parametre['table'] == 'modeles') {
                            $sql = "insert into `" . db::tableColonneValide($parametre['table']) . "` 
                                (`" . db::tableColonneValide($parametre['colonneCompare']) . "`, marque_id) values (?, ?)";
                            db::instance()->query($sql, [$new_valeur, $last_marque_id]);
                        } else {
                            $sql = "insert into `" . db::tableColonneValide($parametre['table']) . "` 
                                (`" . db::tableColonneValide($parametre['colonneCompare']) . "`) values (?)";
                            db::instance()->query($sql, [$new_valeur]);
                        }
                        $new_valeur = db::instance()->lastInsertId();

                        $sql = "insert into `" . db::tableColonneValide($parametre['table']) . "_projets` 
                                (liste_item_id, projet_id) values (?, ?)";
                        db::instance()->query($sql, [$new_valeur, getProjetId()]);
                    }
                    if ($parametre['table'] == 'marques') {
                        $last_marque_id = $new_valeur;
                    }
                } else if ($parametre['type'] == 'float') {
                    $new_valeur = is_numeric($new_valeur) ? (float)$new_valeur : '';
                } else if ($parametre['type'] == 'date') {
                    $new_valeur = isValidDate($new_valeur, 'Y-m-d') ? $new_valeur : '';
                }
                $lignes_to_import[$code_equipement][$iter][$alias] = array(
                    'parametre_key' => $parametre['key'],
                    'new_valeur' => dm_trim($new_valeur),
                    'type' => 'static'
                );
			}
			foreach ($champs_to_import as $alias => $champ) {
                $alias = dm_htmlspecialchars_decode($alias);
				$new_valeur = $champs[$alias] ?? '';
				$lignes_to_import[$code_equipement][$iter][$champ->nom] = array(
					'champ_id' => $champ->id,
					'new_valeur' => dm_trim($new_valeur),
					'type' => 'dynamic'
				);
			}
			unset($lignes[$col_iter]);
		}

		$equipements_mep = EquipementsMep::getListe([ 'mise_en_service_id' => $mise_en_service->id, 'key_to_use' => 'code' ]);
		$valeurs = EquipementsMep::getListeValeurs([ 'mise_en_service_id' => $mise_en_service->id ]);

		$ordre = 1;
		foreach ($lignes_to_import as $code_equipement => $elements_data) {
			$champs_a_notifier = array();
			$data_to_import = array();
			if (count($elements_data) > 1) {
				$occurences = array();
				foreach ($elements_data as $iter => $data_to_import_tmp) {
					$occurences[uniqueKey(serialize($data_to_import_tmp))] = $data_to_import_tmp;
				}

				if (count($occurences) == 1) {
					$occurences = array_values($occurences);
					$data_to_import = $occurences[0];
					unset($lignes_to_import[$code_equipement]);
				}
			} else {
				$data_to_import = $elements_data[1];
				unset($lignes_to_import[$code_equipement]);
			}

            if (count($data_to_import) > 0) {
                $statut_existant = 'Existant';
                $est_associe = false;
                if (isset($equipements_mep[$code_equipement])) {
                    $equipement_mep = $equipements_mep[$code_equipement];
                    $equipement_mep_id = $equipement_mep->id;
                } else {
                    $sql = "insert into `equipements_mep` (mise_en_service_id, code, ordre) values (?, ?, ?)";
                    db::instance()->query($sql, array($mise_en_service->id, $code_equipement, $ordre));
                    $equipement_mep_id = db::instance()->lastInsertId();
                    $statut_existant = 'Nouveau';
                }

                foreach ($data_to_import as $nom_champ => $valeur) {
                    if ($valeur['type'] == 'dynamic') {
                        $champ = $mise_en_service->champs[$valeur['champ_id']];
                        $new_valeur = dm_trim($valeur['new_valeur']);
                        $champ_id = $valeur['champ_id'];

                        $cle_rapport = uniqueKey($code_equipement . $nom_champ);
                        if ((!isset($valeurs[$equipement_mep_id]) || !isset($valeurs[$equipement_mep_id][$champ_id])) && dm_strlen($new_valeur) > 0) {
                            $sql = "insert into equipements_mep_valeurs (equipement_mep_id, equipement_mep_champ_id, valeur) values (?, ?, ?)";
                            db::instance()->query($sql, array($equipement_mep_id, $champ_id, $new_valeur));
                        } else {
                            $old_valeur = isset($valeurs[$equipement_mep_id]) && isset($valeurs[$equipement_mep_id][$champ_id]) ? dm_trim($valeurs[$equipement_mep_id][$champ_id]) : '';
                            if ($old_valeur <> $new_valeur) {
                                $sql = "update equipements_mep_valeurs set valeur = ? where equipement_mep_id = ? and equipement_mep_champ_id = ?";
                                db::instance()->query($sql, array($new_valeur, $equipement_mep_id, $champ_id));
                            }
                        }
                    } else {
                        $new_valeur = $valeur['new_valeur'];
                        $key = $valeur['parametre_key'];

                        $cle_rapport = uniqueKey($code_equipement . $nom_champ);
                        $old_valeur = isset($equipement_mep) ? $equipement_mep->$key : '';
                        if ($old_valeur <> $new_valeur) {
                            $sql = "update `equipements_mep` set `" . db::tableColonneValide($key) . "` = ? where id = ?";
                            db::instance()->query($sql, array($new_valeur, $equipement_mep_id));
                        }
                    }
                }
                $ordre++;
            }
		}

        unset($_SESSION['synchro_data']);

		if (count($lignes_to_import)) {
			$_SESSION['synchro_lignes_doublons'] = $lignes_to_import;
			?>
            <script>
				alert('<?=dm_addslashes(txt('Le rapport suivant comporte des éléments non-importables (doublons), ainsi que la liste des items d\'inventaire manquants.', false))?>');
				parent.loading_beforeunload = false;
				parent.window.location = 'index.php?section=inventaires&module=import&export_doublons=1&id=<?=$mise_en_service->id?>';
				parent.$.fancybox.close();
            </script>
			<?
		} else {
			?>
            <script>
				parent.window.location.reload();
				parent.$.fancybox.close();
            </script>
			<?
		}

        db::instance()->commit();
        unset($_SESSION['messageErreurCustom']);
        ?>
        <script>
            parent.window.location.reload();
            parent.$.fancybox.close();
        </script>
        <?
        die();
	}
} else if(isset($_POST['valider'])) {
	$_SESSION['synchro_data']['lignes_a_importer'] = array();
	$alias = array();
	$champs = array();

	if (!isset($_FILES['fichier']) || !is_uploaded_file($_FILES['fichier']['tmp_name'])) {
		setErreur(txt('Vous devez sélectionner le fichier à importer.'));
	} else {
		$all_cols = $mise_en_service->champs;

		$all_alias = array();
		foreach ($all_cols as $champ) {
			$all_alias[$champ->nom] = $champ->nom;
		}

		$spreadsheet = IOFactory::load($_FILES['fichier']['tmp_name']);

		foreach ($spreadsheet->getWorksheetIterator() as $iter_sheet => $worksheet) {
			if ($iter_sheet > 0) {
				break;
			}
			$highestRow = $worksheet->getHighestDataRow(); // e.g. 10
			$highestColumn = $worksheet->getHighestDataColumn(); // e.g 'F'
			$highestColumnIndex = Coordinate::columnIndexFromString($highestColumn);
			$nrColumns = ord($highestColumn) - 64;

			$nb_actif = 0;
			$row_titre = 0;
			for ($row = 1; $row <= $highestRow; ++$row) {
				$nb = 0;
				for ($col = 1; $col <= $highestColumnIndex; ++$col) {
					$cell = $worksheet->getCellByColumnAndRow($col, $row);
					$val = sanitizeString((string)$cell->getCalculatedValue());

					if (isset($all_alias[$val])) {
						$nb++;
					}
				}

				if ($nb > $nb_actif) {
					$row_titre = $row;
                    break;
				}
			}

			if ($nb == 0) {
				$row_titre = 1;
			}

			for ($row = 1; $row <= $highestRow; ++$row) {
				for ($col = 1; $col <= $highestColumnIndex; ++$col) {
					$cell = $worksheet->getCellByColumnAndRow($col, $row);
					$val = sanitizeString((string)$cell->getCalculatedValue());

					$val = dm_trim($val) == '.' ? '' : $val;
                    $val = dm_trim(dm_str_replace("’", "'", $val));
					if ($row == $row_titre && dm_strlen($val) > 0) {
						$val = dm_trim(dm_preg_replace("/\r|\n/", "", $val));
						$alias[$col] = $val;
						$champs[$val] = $val;
					} else if ($row > $row_titre && isset($alias[$col])) {
                        $aliasCol = dm_trim(dm_preg_replace("/\r|\n/", "", $alias[$col]));
						$_SESSION['synchro_data']['lignes_a_importer'][$row][$aliasCol] = $val;
					}
				}
			}
		}
		$spreadsheet->disconnectWorksheets();
		unset($spreadsheet);

		$_SESSION['synchro_data']['champs'] = $champs;
		$_SESSION['synchro_data']['all_cols'] = $all_cols;
		$_SESSION['synchro_data']['type_import'] = 'excel';
	}

	$etape = 'synchroniser';
}
$noHeader = true;
include('config/header.inc.php');
?>
<script>
	var $document = $(document);
	$document.ready(function(){

		$document.on('change', 'select.uniques', function(){

			var valeur = this.value;
			var id_select = this.id;

			$document.find('select.uniques').each(function(){

				if(this.value == valeur && this.id != id_select)
				{
					this.value = '';
				}

			});

		});

	});
</script>
<style>
	.blocsFiche, #fiche table, #fiche tr, #fiche td, #fiche th, .ui-dialog table, .ui-dialog tr, .ui-dialog td, .ui-dialog th {
		background: none!important;
	}
	.blocsFicheTitre {
		margin-top: 0px!important;
	}
	.readonly {
		text-align: center;
	}
</style>

<div id="popupContent">

	<form id="form" autocomplete="off" method="post" action="index.php?section=equipements_mep&module=import&id=<?=intval($_GET['id'])?>" enctype="multipart/form-data">
		<label><?=txt('Mise-à-jour des données')?></label>
		<fieldset style="min-height: 175px; padding: 10px 0 0 50px;">
			<?
			if (!isset($all_cols)) {
				?>
				<div id="editables">
					<?
					erreurs();
					?>
					<section style="margin: 10px 0 10px 0;">
						<span for="fichier"><?php print txt('Fichier MS Excel à importer'); ?></span>
						<span>
                            <input type="file" id="fichier" name="fichier" style="border: solid 0px!important;"/>
                        </span>
					</section>

				</div>
				<?
			} else {
				?>
				<div id="editables">
					<?
					messages();
					erreurs();
					?>
					<section style="margin: 10px 0 50px 0;">
						<table style="width: 95%!important; margin: 10px 0 0 0;">
							<tr>
								<th>
									<?= txt('Champs d\'inventaire') ?>
								</th>
								<th>
									<?= txt('Colonnes détectées dans le fichier importé') ?>
								</th>
							</tr>
							<tr style="background-color: #e2ffe2">
								<td style="border-right: solid 1px transparent!important;">
									<?= txt('Code de l\'équipement') ?>
								</td>
								<td style="border-left: solid 1px transparent!important; position: relative;">
									<div
										style="position: absolute; left: -29px; top: 10px; color: red; font-weight: bold;">
										<-- <?= txt('Pivot') ?> -->
									</div>
									<?= txt('Code de l\'équipement') ?>
									<input type="hidden" name="code_pivot_externe" value="<?= txt('Code de l\'équipement', false) ?>" />
                                    <span style="width: 24px; height: 24px; display: inline-block;">&nbsp;</span>
                                </td>
							</tr>
							<?
							foreach($all_cols_statique as $key => $parametre)
							{
                                if ($key == 'code') {
                                    continue;
                                }
								?>
                                <tr class="champs"><td>
                                        <span><?=$parametre['titre']?></span>
									</td><td>
										<select id="alias_<?=$key?>" name="alias_<?=$key?>" class="uniques colonnes" style="max-width: 250px!important;">
											<option value=""> </option>
											<?
											foreach($champs as $i => $alias)
											{
												$sel = sansaccent($alias) == sansaccent($parametre['titre'])  ? ' selected="selected"' : '';
												?>
												<option value="<?=$alias?>"<?=$sel?>><?=$alias?></option>
												<?
											}
											?>
										</select>
                                        <img src="css/images/icons/dark/cross_3.png" class="resetAssoc" />
                                    </td></tr>
								<?
							}

							foreach ($all_cols as $champ) {
								?>
                                <tr class="champs"><td>
									<td>
                                        <span><?= $champ->nom ?></span>
									</td>
									<td>
										<select id="alias_<?= uniqueKey($champ->nom) ?>" name="alias_<?= uniqueKey($champ->nom) ?>"
										        class="uniques colonnes" style="max-width: 250px!important;">
											<option value=""> </option>
											<?
											foreach ($champs as $i => $alias) {
												$sel = $alias == $champ->nom ? ' selected="selected"' : '';
												?>
												<option value="<?= $alias ?>"<?= $sel ?>><?= $alias ?></option>
												<?
											}
											?>
										</select>
                                        <img src="css/images/icons/dark/cross_3.png" class="resetAssoc" />
                                    </td>
								</tr>
								<?
							}
							?>
						</table>
					</section>
				</div>
				<?
			}
			?>
		</fieldset>

		<div style="text-align: right; width: 98%!important;">
            <?
            if ($etape == 'synchroniser') {
                ?>
                <button class="assocAll all" style="color: green;"><?= txt('Tout associer') ?></button>
                <button class="resetAll all" style="color: red;"><?= txt('Tout dissocier') ?></button>
                <?
            }
            ?>
			<button class="submit" value="submitbuttonvalue" name="<?=$etape?>"><?=txt($etape)?></button>
			<button onclick="parent.$.fancybox.close();return false;" name="close"><?=txt('Annuler')?></button>

		</div>
	</form>
</div>
<?
include('config/footer.inc.php');
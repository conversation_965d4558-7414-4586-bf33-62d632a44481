<?
$is_print = isset($_GET['print']);
if($is_print)
{
    $noHeader = 1;
    $isPDF = true;
    include('config/header.inc.php');
    ?>
    <script>
		window.onload = function() { window.print(); }
    </script>
    <style>
        #logs td {
            text-align: left;
            padding: 0px 5px;
        }
        #logs th {
            text-weight: bold;
            text-align: left;
        }
    </style>
    <?
}

if(!$is_print)
{
    include('config/header.inc.php');
    ?>
    <script>
		$(document).ready(function(){

			$(document).find('span.datepicker').datetimepicker({
				timepicker:false,
				inline:true,
				format:'Y-m-d',
				lang:'fr',
				scrollMonth : false,
				scrollInput : false,
				onSelectDate: function(ct,$i){

					$i.parent().parent().find('select.sel_date').append('<option value="' + ct.dateFormat('Y-m-d') + '" selected="selected" class="date_fixe">' + ct.dateFormat('Y-m-d') + '</option>');
					$i.parent().hide();

				}
			});


			$(document).on('change', '#marque_id', function(){

				var url = 'index.php?section=fiche&module=wizard';
				$.get(url, { action: 'getModelesFromMarque', marque_id: this.value }, function(html){
					$('#modele_id').html(html);
				});

			});

			$(document).on('click', '#auto_planif', function(){

				var url = $(this).attr('href');
				$.get(url, {  }, function(text){
					alert(text);
					window.location = window.location;
				});
				return false;

			});

			$(document).on('click', '#filter_btn', function(){

				$('#historique_form').toggle(400);

			});

			load_fiches();

		});

		function load_fiches()
		{
			var fiche_id_sel = $('#fiche_id').val();
			var url = 'index.php?section=fiche&module=index';
			$.get(url, { action: 'getFichesList', term: '', batiment_id: '', fiche_type_id: '', fiche_sous_type_id: '', numero_lot: '', code_alternatif: '', id_revit: '', id_codebook: '', procedure_entretien_id: '', selected: '', disable_fitre_avance: 1 }, function(json_data){

				loading_dynamic_combobox('fiche_id', fiche_id_sel, json_data);

			}, 'json');
		}
    </script>
    <style>
        #historique td {
            text-align: left;
            padding: 0px 5px;
        }
        #historique th {
            text-weight: bold;
            text-align: left;
        }
        select {
            width: 210px;
        }
    </style>
    <?
}

$default_usager_attribue_id = havePermission('inspection_completion') && !isMaster() ? getIdUsager() : 0;

$filtre = &$_SESSION['filtre_historique_mise_en_service_mep'];
$filtre['discipline_id'] = isset($filtre['discipline_id']) ? $filtre['discipline_id'] : 0;
$filtre['type'] = isset($filtre['type']) ? $filtre['type'] : '';
$filtre['fiche_id'] = isset($filtre['fiche_id']) ? $filtre['fiche_id'] : 0;
$filtre['marque_id'] = isset($filtre['marque_id']) ? $filtre['marque_id'] : 0;
$filtre['modele_id'] = isset($filtre['modele_id']) ? $filtre['modele_id'] : 0;
$filtre['nomenclature_id'] = isset($filtre['nomenclature_id']) ? $filtre['nomenclature_id'] : 0;
$filtre['order_by_col'] = isset($filtre['order_by_col']) ? $filtre['order_by_col'] : 'id';
$filtre['order_by_dir'] = isset($filtre['order_by_dir']) ? $filtre['order_by_dir'] : 'desc';
$filtre['usager_attribue_id'] = isset($filtre['usager_attribue_id']) ? $filtre['usager_attribue_id'] : $default_usager_attribue_id;

$_SESSION['miseEnServiceMode'] = $_SESSION['miseEnServiceMode'] ?? 'fiches';
if (isset($_GET['mode'])) {
    $_SESSION['miseEnServiceMode'] = $_GET['mode'];
}

$page = 1;
if(isset($_GET['page']) && intval($_GET['page']) > 0)
{
    $page = intval($_GET['page']);
}

if(isset($_POST['discipline_id']))
{
    $filtre['discipline_id'] = intval($_POST['discipline_id']);
    $page = 1;
}

if(isset($_POST['type']))
{
    $filtre['type'] = $_POST['type'];
    $page = 1;
}

if(isset($_POST['fiche_id']))
{
    $filtre['fiche_id'] = intval($_POST['fiche_id']);
    $page = 1;
}

if(isset($_POST['marque_id']))
{
    $filtre['marque_id'] = intval($_POST['marque_id']);
    $page = 1;
}

if(isset($_POST['modele_id']))
{
    $filtre['modele_id'] = intval($_POST['modele_id']);
    $page = 1;
}

if(isset($_POST['nomenclature_id']))
{
    $filtre['nomenclature_id'] = intval($_POST['nomenclature_id']);
    $page = 1;
}

if(isset($_POST['usager_attribue_id']))
{
    $filtre['usager_attribue_id'] = intval($_POST['usager_attribue_id']);
    $page = 1;
}

if(isset($_GET['order_by_col']) && isset($_GET['order_by_dir']))
{
    $filtre['order_by_col'] = $_GET['order_by_col'];
    $filtre['order_by_dir'] = $_GET['order_by_dir'];
    $page = 1;
}

$filtre_applique = false;
if(intval($filtre['discipline_id']) > 0)
{
    $filtre_applique = true;
}
if(dm_strlen($filtre['type']) > 0)
{
    $filtre_applique = true;
}
if(intval($filtre['fiche_id']) > 0)
{
    $filtre_applique = true;
}
if(intval($filtre['marque_id']) > 0)
{
    $filtre_applique = true;
}
if(intval($filtre['modele_id']) > 0)
{
    $filtre_applique = true;
}
if(intval($filtre['nomenclature_id']) > 0)
{
    $filtre_applique = true;
}
if(havePermission('admin_procedures_inspections_planifiees'))
{
    if(intval($filtre['usager_attribue_id']) > 0)
    {
        $filtre_applique = true;
    }
}

$nb_par_page = 20;
$nb_total = ProcedureMiseEnServicePlanifieeMep::getCount($filtre);

$nb_pages = ceil($nb_total / $nb_par_page);
$limit = $page == 1 ? 0 : ($page-1) * $nb_par_page;

$sql_limit = $is_print ? '' : "LIMIT $limit, $nb_par_page";

$procedures = ProcedureMiseEnServicePlanifieeMep::getListe($filtre, $filtre['order_by_col'].' '.$filtre['order_by_dir'], $sql_limit);

if(!$is_print)
{
    ?>
    <h1 class="ficheTitre" style="position: relative;">

        <?= txt('Mise en service - Planification') ?>

        <a href="index.php?section=equipements_mep&module=planification&print=1" style="position: relative; top: 4px;" title="<?=txt("Imprimer")?>" target="iframe">
            <img src="css/images/icons/dark/printer.png" /></a>

        <div style="display: inline-block; position: absolute; right: 10px; top: 12px;  z-index: 1;">
            <?
            include('inspection/onglets.inc.php');
            ?>
        </div>

    </h1>
    <div id="ongletContent" style="position: relative;">
        <?php
        EquipementsMep::afficheTitreEtOnglets();
        ?>
        <?
        }

        if($is_print)
        {
            ?>
            <h6><?= txt('Mise en service - Planification') ?></h6>
            <?
        }
        ?>

        <table id="historique" style="margin-top: 50px;">
            <tr>
                <th width="75" class="<?=($filtre['order_by_col'] == 'numero' ? 'ordered' : '')?>">
                    <?=getOrderByLink(txt('Numéro'), 'index.php?section=equipements_mep&module=planification', 'numero', $filtre['order_by_col'], $filtre['order_by_dir'], $is_print)?>
                <th width="150" class="<?=($filtre['order_by_col'] == 'description' ? 'ordered' : '')?>">
                    <?=getOrderByLink(txt('Description'), 'index.php?section=equipements_mep&module=planification', 'description', $filtre['order_by_col'], $filtre['order_by_dir'], $is_print)?>
                </th><th width="75" class="<?=($filtre['order_by_col'] == 'date_debut' ? 'ordered' : '')?>">
                    <?=getOrderByLink(txt('Date début'), 'index.php?section=equipements_mep&module=planification', 'date_debut', $filtre['order_by_col'], $filtre['order_by_dir'], $is_print)?>
                </th><th width="75" class="<?=($filtre['order_by_col'] == 'date_debut' ? 'ordered' : '')?>">
                    <?=getOrderByLink(txt('Date fin'), 'index.php?section=equipements_mep&module=planification', 'date_fin', $filtre['order_by_col'], $filtre['order_by_dir'], $is_print)?>
                </th>
                <?
                if(havePermission('admin_procedures_inspections_planifiees'))
                {
                    ?>
                    <th width="125" class="<?=($filtre['order_by_col'] == 'responsable_planification' ? 'ordered' : '')?>">
                        <?=getOrderByLink(txt('Responsable'), 'index.php?section=equipements_mep&module=planification', 'responsable_planification', $filtre['order_by_col'], $filtre['order_by_dir'], $is_print)?>
                    </th>
                    <?
                }
                ?>
                <th width="115">
                    <?= txt('Statut de conformité') ?>
                </th>
                <?
                if(!$is_print)
                {
                    ?>
                    <th width="77" style="text-align: center!important;">
                        <?
                        if(GestionnaireRoute::instance()->haveAccesRoute('admin-edit-procedures_inspections_planifiees_gbm'))
                        {
                            ?>
                            <a href="index.php?section=admin&module=edit&table=procedures_mises_en_service_planifiees_mep&type=client&id=0" class="fancy_big">
                                <img src="css/images/icons/dark/plus.png" /></a>
                            <?
                        }
                        ?>
                    </th>
                    <?
                }
                ?>
            </tr>
            <?
            foreach($procedures as $i => $procedure)
            {
                ?>
                <tr>
                    <td class="<?=($filtre['order_by_col'] == 'numero' ? 'ordered' : '')?>">
                        <?=$procedure->numero?>
                    </td>
                    <td class="<?=($filtre['order_by_col'] == 'description' ? 'ordered' : '')?>">
                        <?=$procedure->description?>
                    </td>
                    <td class="<?=($filtre['order_by_col'] == 'date_debut' ? 'ordered' : '')?>">
                        <?=$procedure->date_debut?>
                    </td>
                    <td class="<?=($filtre['order_by_col'] == 'date_fin' ? 'ordered' : '')?>">
                        <?=$procedure->date_fin?>
                    </td>
                        <?
                        $nb_mises_en_service_non_conforme = 0;
                        $nbEquipementSansProcedure = 0;
                        if (isset($procedure->equipements) && is_array($procedure->equipements) && count($procedure->equipements) > 0) {
                            $iter = 0;
                            foreach ($procedure->equipements as $i => $equipement) {
                                if ($equipement->nb_taches_conformes <> $equipement->nb_taches || empty($equipement->procedure_id)) {
                                    $nb_mises_en_service_non_conforme++;
                                }
                                if (empty($equipement->procedure_id)) {
                                    $nbEquipementSansProcedure++;
                                }
                                $iter++;
                            }
                            if (count($procedure->equipements) > 3) {
//                                print '... <span style="font-weight: bold; color: blue">('.count($procedure->equipements).')</span>';
                            }
                        }
                        ?>
                    <?
                    if(havePermission('admin_procedures_inspections_planifiees'))
                    {
                        ?>
                        <td class="<?=($filtre['order_by_col'] == 'responsable_planification' ? 'ordered' : '')?>">
                            <?=(isset($procedure->responsable_planification) ? $procedure->responsable_planification : txt('À déterminer'))?>
                        </td>
                        <?
                    }

                    $is_all_mises_en_service_conformes = $nb_mises_en_service_non_conforme == 0;
                    $nom_statut = $is_all_mises_en_service_conformes ? txt('Conforme') : $nb_mises_en_service_non_conforme.' '.txt('élément(s) non-conforme(s)');
                    $nom_statut2 = $nbEquipementSansProcedure > 0 ? '<br />' . $nbEquipementSansProcedure . ' ' . txt('équipement(s) sans procédure') : '';
                    $color = $is_all_mises_en_service_conformes ? 'green' : 'red';
                    ?>
                    <td>
                        <a href="javascript: void(0);" style="font-weight: bold; color: <?=$color?>!important;" onclick="$('#mises_en_service_<?=$procedure->id?>').slideToggle(400)"><?= $nom_statut . $nom_statut2 ?></a>
                    </td>
                    <?
                    if(!$is_print)
                    {
                        ?>
                        <td style="height: 26px; text-align: center!important;">
                            <?
                            if(havePermission('admin_procedures_inspections_planifiees'))
                            {
                                ?>
                                <a href="index.php?section=admin&module=edit&table=procedures_mises_en_service_planifiees_mep&type=client&id=<?=$procedure->id?>" class="fancy_big">
                                    <img src="css/images/icons/dark/create_write.png"></a>
                                <a href="javascript: void(0);" class="dle_btn dle_delete_generique" tableName="procedures_mises_en_service_planifiees_mep" idElement="<?=$procedure->id?>">
                                    <img src="css/images/icons/dark/trashcan.png" /></a>
                                <?
                            }
                            else
                            {
                                ?>
                                <a href="index.php?section=admin&module=edit&table=procedures_mises_en_service_planifiees_mep&type=client&id=<?=$procedure->id?>" class="fancy_big">
                                    <img src="css/images/icons/dark/magnifying_glass.png"></a>
                                <?
                            }
                            ?>
                            <a href="index.php?section=equipements_mep&module=procedure&print=1&procedure_mise_en_service_planifiee_mep_id=<?=$procedure->id?>" title="<?=txt("Imprimer")?>" target="iframe">
                                <img src="css/images/icons/dark/printer.png" /></a>

                        </td>
                        <?
                    }
                    ?>
                </tr>
                <tr>
                    <td colspan="8" style="padding: 0px!important; background-color: grey!important;">

                        <div id="mises_en_service_<?=$procedure->id?>" style=" display: <?=(isset($_GET['procedure_mise_en_service_planifiee_mep_id']) && $_GET['procedure_mise_en_service_planifiee_mep_id'] == $procedure->id && count($procedure->fiches) > 0 ? '' : 'none')?>; margin: 25px; background-color: grey!important;">
                            <table style="width: 85%!important;float: right;" style="">
                                <tr><th width="200">
                                        <?=txt('Équipement')?>
                                    </th><th width="325">
                                        <?=txt('Ajout')?>
                                    </th><th width="325">
                                        <?=txt('Dernière édition')?>
                                    </th><th>
                                        <?=txt('Statut de conformité')?>
                                    </th>
                                    <?
                                    if(!$is_print)
                                    {
                                        ?>
                                        <th width="22">
                                            &nbsp;
                                        </th>
                                        <?
                                    }
                                    ?>
                                </>
                                <?
                                foreach($procedure->equipements as $i => $equipement)
                                {
                                    ?>
                                    <tr><td>
                                            <?=$equipement->equipement?>
                                        </td><td>
                                            <?=dm_substr($equipement->ts_ajout, 0, 16).' '.txt('par').' '.$equipement->ajout_usager?>
                                        </td><td>
                                            <?=(dm_strlen($equipement->ts_modification) > 0 ? dm_substr($equipement->ts_modification, 0, 16).' '.txt('par').' '.$equipement->modification_usager : '&nbsp;')?>
                                            <?
                                            if (empty($equipement->procedure)) {
                                                $nom_statut = txt('Aucune procédure');
                                                $color = 'red';
                                            } else {
                                                $is_all_taches_conformes = $equipement->nb_taches == $equipement->nb_taches_conformes;
                                                $nb_taches_non_conforme = $equipement->nb_taches - $equipement->nb_taches_conformes;
                                                $nom_statut = $is_all_taches_conformes ? txt('Conforme') : $nb_taches_non_conforme.' '.txt('tâche(s) non-conforme(s)');
                                                $color = $is_all_taches_conformes ? 'green' : 'red';
                                            }
                                            ?>
                                        </td><td style="color: <?=$color?>!important; font-weight: bold;">
                                            <?=$nom_statut?>
                                        </td>
                                        <?
                                        if(!$is_print)
                                        {
                                            ?>
                                            <td style="height: 26px;">
                                                <? if (!empty($equipement->procedure)) { ?>
                                                <a class="fancy" style="position: relative; top: 3px;" href="index.php?section=admin&module=edit&table=procedures_mises_en_service_planifiees_mep_equipements&procedure_mise_en_service_planifiee_mep_id=<?=$procedure->id?>&type=client&id=<?=$equipement->id?>">
                                                    <img src="css/images/icons/dark/create_write.png"></a>
                                                <? } ?>
                                            </td>
                                            <?
                                        }
                                        ?>
                                    </tr>
                                    <?
                                }
                                ?>
                            </table>
                            <br style="clear: both;" />
                        </div>

                    </td>
                </tr>
                <?
            }
            ?>
        </table>
        <?
        if(!$is_print)
        {
            showPaginateur('index.php?section=equipements_mep&module=planification', $limit, $nb_total, $nb_pages, $nb_par_page, $page);
        }
        ?>
    </div>
    <iframe id="iframe" name="iframe" style="display: none; width: 100%;"></iframe>
    <br style="clear: both;" />
<?
include('config/footer.inc.php');
?>
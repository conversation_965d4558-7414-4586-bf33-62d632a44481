<?php
chdir(dirname(__file__));

$doNotCheckLogin=1;
$doNotValidIp = 1;
ini_set("memory_limit","10000M");
ini_set('max_execution_time', 30000);
$db_name_override = $argv[1];
$export_id = (int)$argv[2];
include('config/inc.php');

$CodeClient = $db_name_override;
$dbNameOverrideGlobal = $db_name_override;
$db = db::instance($db_name_override);

$export = new ExportsAutomatiques($export_id);
$champs_to_export = $export->getChampsToExport();

setProjetInfoForCron($export->projet_id);

$isCron = true;
if ($export->module == 'acquisition') {
    include("acquisition/exporter_avancer.php");
} else if ($export->module == 'financement') {
    include("financement/exporter_avancer_financements.php");
}
<?
ini_set("memory_limit","8000M");
ini_set('max_execution_time', 300000);

if (isset($_POST['affecter'])) {
    if (!isset($_POST['listeFiches']) || count($_POST['listeFiches']) == 0) {
        setErreur(txt('Vous devez sélectionner au moins 1 fiche sur laquelle affecter le gabarit.'));
    }
    if (!isset($_POST['formulaire_id']) || count($_POST['formulaire_id']) == 0) {
	    setErreur(txt('Vous devez sélectionner au moins 1 formulaire.'));
    }
    
    if (nbErreurs() == 0) {
        $db->autocommit(false);
        
        $gabarit_id = intval($_POST['gabarit_id']);
        $gabarit = new Fiches($gabarit_id);
        
        $fiches = Fiches::getListe(array('ids' => $_POST['listeFiches'], 'no_filtre_global' => 1));

        $formulaires = $gabarit->getFormulaires();
        foreach($fiches as $fiche) {
            $sql = "delete FROM fiches_gabarits_formulaires WHERE fiche_id = ?";
            $db->query($sql, array($fiche->id));

	        foreach ($formulaires as $i => $formulaire) {
                if (in_array($formulaire->id, $_POST['formulaire_id'])) {
	                if (isset($formulaire->discipline_id) && $formulaire->discipline_id > 0) {
		                $sql = "select count(*) as nb from fiches_disciplines where fiche_id = ? and discipline_id = ?";
		                $row_nb = $db->getRow($sql, array($fiche->id, $formulaire->discipline_id));

		                if ($row_nb['nb'] == 0) {
			                $sql = "insert into fiches_disciplines (fiche_id, discipline_id) values(?, ?)";
			                $db->query($sql, array($fiche->id, $formulaire->discipline_id));
		                }
	                }

	                $fiche->affecterGabarit($gabarit, $formulaire, isset($_POST['remplacer_donnees']));

                    $sql = "INSERT INTO fiches_gabarits_formulaires (fiche_id, gabarit_id, formulaire_id) 
                            VALUES (?, ?, ?)";
                    $db->query($sql, array($fiche->id, $gabarit->id, $formulaire->id));
                }
	        }

	        $sql = "delete FROM fiches_gabarits WHERE fiche_id = ?";
	        $db->query($sql, array($fiche->id));

            $sql = "INSERT INTO fiches_gabarits (fiche_id, gabarit_id) 
                    VALUES (?, ?)";
            $db->query($sql, array($fiche->id, $gabarit->id));
        }
            
        $db->commit();
?>
        <script>
            parent.$.fancybox.close();
        </script>
<?
        exit;
    }
}

$codeFiltreAvance = '_applicateur_gabarit';
$isFiltreActif = false;
if (isset($_SESSION["filtre_top$codeFiltreAvance"]) && isset($_SESSION["filtre_avance$codeFiltreAvance"])) {
    $isFiltreActif = Fiches::isFiltreActif($_SESSION["filtre_top$codeFiltreAvance"], $_SESSION["filtre_avance$codeFiltreAvance"]);
}

$noHeader = true;
include('config/header.inc.php');
?>
<script>
var term = '';
var timout = setTimeout(function(){}, 1);
let filtreAvance = null;
$(document).ready(function(){

	filtreAvance = new FiltreAvance('getFichesListForAppliqueGabarit', $('#filtreAvance'), 'Recherche de fiches', <?=$isFiltreActif ? 'true' : 'false'?>, 'listeFiches', '<?=$codeFiltreAvance?>', true, false, function(){}, false);
	filtreAvanceCallback = (isFiltreActif) => {
		filtreAvance.callback(isFiltreActif);
	};

	$('#listeFiches').change(function(){
		changeNbs($(this));
	});

	$('#blocFiltre').on('change', '#gabarit_id', function(){
		filtreAvance.appliqueFiltre();
	});

    $('#gabarit_id').select2({
		'width': $('#gabarit_id').width() + 'px'
	});

	$('#gabarit_id').change(function(){
		appliqueSelectionGabarit();
    });
	if ($('#gabarit_id').val() !== '') {
		appliqueSelectionGabarit();
    }
});

function changeNbs($sel_obj)
{
	$('#nb_selection').html($sel_obj.find(':selected').length);
	$('#nb_total').html($sel_obj.find('option').length);
}

function appliqueSelectionGabarit()
{
	$.get('index.php', { action: 'getFormulairesDuGabarit', gabarit_id: $('#gabarit_id').val() }, function(html) {
		$('#formulaire_id').html(html);
		$('#formulaire_id').select2('destroy');
		$('#formulaire_id').select2({
			'width': $('#formulaire_id').width() + 'px'
		});
	});
}
</script>
<style>
    .blocsFiche, #fiche table, #fiche tr, #fiche td, #fiche th, .ui-dialog table, .ui-dialog tr, .ui-dialog td, .ui-dialog th {
        background: none!important;
    }
    .blocsFicheTitre {
        margin-top: 0!important;
    }

    .select2-choices {
        min-height: auto!important;
    }

    .select2-choices input {
        padding: 0!important;
        margin: 0 0 0 5px!important;
    }
</style>

<div id="popupContent">

    <form id="form" autocomplete="off" method="post" action="index.php?section=fiche&module=applicateur_gabarit">
        <fieldset>
            
            <label><?=txt('Propagation de gabarit(s)')?></label>
	        <?
	        erreurs();
	        ?>

            <div id="blocFiltre" style="padding: 30px 0 0 40px; width: 550px; float: left;">

                <div style="width: 450px; margin-bottom: 5px;">
                    <span style="width: 100px; display: inline-block;"><?=txt('Gabarit:')?></span>
                    <select id="gabarit_id" name="gabarit_id" style="width: 550px;">
                        <option value=""> </option>
                        <?
                        $formulaire_qte_ids = Formulaires::getFormulairesIdWithQuantite();
                        $sql = "select f.id as gabarit_id, f.code as code_fiche, o.nom as ouvrage, coalesce(ge.utilite, gl.utilite) as utilite
                                from fiches f 
                                left join generaux_equipements ge on f.id = ge.fiche_id
                                left join generaux_locaux gl on f.id = gl.fiche_id
                                join ouvrages o on f.ouvrage_id = o.id
                                where f.ts_delete is null and f.ts_archive is null and f.is_gabarit = 1 and exists (select fiche_id from fiches_formulaires ff where ff.fiche_id = f.id and formulaire_id in (" . implode(', ', $formulaire_qte_ids) . "))
                                order by ouvrage, code_fiche";
                        $gabarits = $db->getAll($sql);
                        foreach ($gabarits as $i => $gabarit) {
                            ?>
                            <option value="<?= $gabarit['gabarit_id'] ?>"<?= ($gabarit['gabarit_id'] == getSelectedGabaritId() ? ' selected="selected"' : '') ?>><?= $gabarit['ouvrage'] . ' | ' . $gabarit['code_fiche'] . (isset($gabarit['utilite']) && strlen($gabarit['utilite']) > 0 ? ' - ' . $gabarit['utilite'] : '') ?></option>
                            <?
                        }
                        ?>
                    </select>
                </div>

                <div style="width: 450px; height: 50px; margin-bottom: 5px;">
                    <span style="width: 100px; display: inline-block;"><?=txt('Formulaires:')?></span>
                    <select id="formulaire_id" name="formulaire_id[]" multiple style="width: 450px;">
                    </select>
                </div>

                <div style="color: red; margin: 15px 0 0 0; width: 550px;">
                    <?=txt('Attention! La propagation d\'un gabarit peut "Ajouter des données aux informations existantes" OU "Remplacer les données existantes" (cocher l\'option ci-dessous).')?>
                </div>

                <div style="width: 450px; margin: 15px 0 0 0!important;">
                    <label style="font-weight: bold;"><input type="checkbox" name="remplacer_donnees" value="1" style="position: relative; top: 2px;" />&nbsp;<?=txt('Remplacer les données existantes')?></label>
                </div>
            </div>

            <div style="padding: 10px 0 15px 40px; width: 360px; float: left;">

                <div id="filtreAvance" style="margin-bottom: 5px; min-height: 30px;"></div>
                <div style="display: inline-block; position: relative; top: 0px;"><input type="text" id="codeFiche" style="width: 320px;" /></div>
                <ul id="listeFiches" style="min-height: 460px!important; height: 460px!important; width: 350px;"></ul>

            </div>

            <div id="blocFiltre" style="width: 250px; float: left;">
                
            </div>

        </fieldset>

        <div style="text-align: right; width: 98%!important;">
            <h6 style="font-weight: bold; width: 316px; margin: 17px 0; float: right;">
                <?=txt('Fiche(s) sélectionnée(s): ')?><span id="nb_selection" style="font-weight: bold;">0</span>&nbsp;<?=txt('sur')?>&nbsp;<span id="nb_total" style="font-weight: bold;">0</span>
            </h6>
            <br style="clear: both;" />
            <button class="submit" value="submitbuttonvalue" name="affecter"><?=txt('Affecter')?></button>
            <button onclick="parent.$.fancybox.close();return false;" name="close"><?=txt('Annuler')?></button>
        </div>
    </form>
</div>    
<?
include('config/footer.inc.php');
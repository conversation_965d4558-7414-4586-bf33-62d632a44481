<?
$fiche_id = intval($_GET['fiche_id']);
$fiche = new Fiches($fiche_id);

if(isset($_GET['formulaire']))
{
    $formulaire = $_GET['formulaire'];
    $formulaire_id = Formulaires::getIdFromNom($formulaire);
}

if(isset($_GET['token_archive_a_retablir']))
{
    if(!isset($_POST['raison']) || dm_strlen(dm_trim($_POST['raison'])) == 0)
    {
        setErreur(txt('Le motif du changement est obligatoire.'));
    }
    else 
    {
        if ($formulaire == 'EquipementsBioMedicaux') {
	        $formulaire = 'EquipementsBioMedicauxNoCoord';
        }
        $listeArchive = $formulaire::getListe(array('fiche_id' => $fiche_id, 'token_archive' => $_GET['token_archive_a_retablir']));

        $commentaire = $formulaire::getCommentaireFormulaire($fiche_id, $_GET['token_archive_a_retablir']);
        
        $db->autocommit(false);
        
        if(isset($listeArchive) && count($listeArchive) > 0)
        {
            $sql = "SELECT `COLUMN_NAME`, `DATA_TYPE`
                    FROM `INFORMATION_SCHEMA`.`COLUMNS` 
                    WHERE `TABLE_SCHEMA` = '$db_name_archive' 
                        AND `TABLE_NAME` = '" . $formulaire::SQL_TABLE_NAME . "'
                        AND `COLUMN_NAME` not in ('id', 'fiche_id', 'no', 'ordre', 'statut', 'token_archive', 'ts_ajout', 'usager_ajout_id')
                        order by DATA_TYPE;";
            $cols = $db->getAll($sql, array());

            $data_to_save = array();

            $iter = 0;
            foreach($listeArchive as $i => $obj)
            {
                foreach($cols as $j => $col)
                {
                    $col_name = $col['COLUMN_NAME'];
                    $data_to_save[$col['COLUMN_NAME']][$iter] = isset($obj->$col_name) ? $obj->$col_name : '';
                }
	            $data_to_save['id_ligne'][$iter] = $obj->id;
                $iter++;
            }
            $data_to_save['fiche_id'] = $fiche_id;
            $data_to_save['formulaire'] = $formulaire;
            $data_to_save['raison'] = $_POST['raison'];
            $data_to_save['fiche_formulaire_commentaire'] = $commentaire;
            
            $formulaire::save($data_to_save, true);
        }
        
        if($fiche->is_gabarit == 1)
        {
            $formulaire = new Formulaires($formulaire_id);
            $gabarit = $fiche;
            
            $fiches = $gabarit->getFichesAffectedByGabarit($formulaire->id);
            foreach($fiches as $fiche_tmp)
            {
                $fiche_tmp->affecterGabarit($gabarit, $formulaire);
            }
        }
        
        $db->commit();
        
        if(isset($_GET['notification_gestion_id']))
        {
?>
            <script>
                parent.window.location = 'index.php?section=notifications&module=index&notification_gestion_id=<?=intval($_GET['notification_gestion_id'])?>';
                parent.$.fancybox.close();
            </script>
<?
        }
        else if(isset($_GET['from_histo']) || isset($_GET['from_gestion_portes']))
        {
?>
            <script>
                parent.window.location.reload();
                parent.$.fancybox.close();
            </script>
<?
        }
        else
        {
?>
            <script>
                parent.reloadFiche(<?=$fiche_id?>);
                parent.$.fancybox.close();
            </script>
<?
        }
        exit;
    }
}

$logs = $formulaire::getArchivesForFiche($fiche_id);

if(isset($_GET['token_archive']))
{
    $token_archive = dm_strlen($_GET['token_archive']) > 0 ? $_GET['token_archive'] : $logs[0]['token_archive'];
}
else
{
    $token_archive = isset($_POST['token_archive']) ? $_POST['token_archive'] : $logs[0]['token_archive'];
}

$noHeader = true;
include('config/header.inc.php');

$isPDF = 1;
?>
<script>
    $(document).ready(function(){
        
        $("#motif_changement").combobox();
        
    });
</script>
<style>
    .blocsFiche, #fiche table, #fiche tr, #fiche td, #fiche th, .ui-dialog table, .ui-dialog tr, .ui-dialog td, .ui-dialog th {
        background: none!important;
    }
    .blocsFicheTitre {
        margin-top: 0px!important;
    }
    .ui-autocomplete-input {
        width: 75%!important;
    }
    .custom-combobox {
        position: relative;
        right: -25px;
    }
</style>
<div id="popupContent">
	<form id="form" autocomplete="off" method="post" action="index.php?section=fiche&module=form_logs&fiche_id=<?=$fiche_id?>&formulaire=<?=$formulaire?><?=(isset($_GET['from_histo']) ? '&from_histo='.$_GET['from_histo'] : '')?><?=(isset($_GET['from_gestion_portes']) ? '&from_gestion_portes='.$_GET['from_gestion_portes'] : '')?>&can_edit_form=<?=(isset($_GET['can_edit_form']) ? intval($_GET['can_edit_form']) : 0)?><?=(isset($_GET['notification_gestion_id']) ? '&notification_gestion_id='.$_GET['notification_gestion_id'] : '')?><?=(isset($_GET['fiche_log_id']) ? '&fiche_log_id='.$_GET['fiche_log_id'] : '')?>">
        <fieldset>
            
            <label><?=$fiche->batiment?></label>
            
            <h3 style="float: right; width: 100%; text-align: right; margin: 15px 25px 0 0"><?=$fiche->fiche_type.($fiche->fiche_sous_type <> 'N/A' ? ' - '.$fiche->fiche_sous_type : '').'<br />'.$fiche->code?></h3>
            
            <div style="float: left; width: 100%; margin: 0px 20px;">
                
                <select name="token_archive" onchange="$('#form').submit();">
<?
                    $raison = '';
                    $i_last = count($logs) - 1;
                    $last_token_archive = '';
                    foreach($logs as $i => $log)
                    {
                        $sel = $log['token_archive'] == $token_archive ? ' selected="selected"' : '';
                        if($log['token_archive'] == $token_archive)
                        {
                            $raison = $log['raison'];
                            $last_token_archive = isset($logs[($i+1)]) ? $logs[($i+1)]['token_archive'] : '';
                        }
                        $str_ts = '';
                        if(dm_strlen($log['token_archive']) == 0)
                        {
                            $str_ts = txt('Version actuelle du ').dm_substr($log['ts_ajout'], 0, 10).txt(' à ').dm_substr($log['ts_ajout'], 10, 9);
                        }
                        else if($i == $i_last)
                        {
                            $str_ts = txt('Version originale du ').dm_substr($log['ts_ajout'], 0, 10).txt(' à ').dm_substr($log['ts_ajout'], 10, 9);
                        }
                        else
                        {
                            $str_ts = txt('Version du ').dm_substr($log['ts_ajout'], 0, 10).txt(' à ').dm_substr($log['ts_ajout'], 10, 9);
                        }
?>
                        <option value="<?=$log['token_archive']?>"<?=$sel?>><?=$str_ts.txt(' par ').$log['usager'].txt(' dans le cadre du projet ').($log['projet_id'] == getProjetId() ? txt('actuel') : $log['code'].' - '.$log['nom'])?></option>
<?
                    }
?>
                </select>
            </div>
            
        </fieldset>
    </form>
                
    <div id="fiche" class="fichePDF" style="position: relative; background: none!important; max-height: 465px; overflow-y: auto; overflow-x: hidden;">
        <!--<span style="position: absolute; right: 10px;">
            <input type="checkbox" id="switch_versions" value="1" /><label for="switch_versions" style="position: relative; top: -7px;">&nbsp;<?//=txt('Afficher les différences avec la version précédente')?></label>
        </span><br />-->
<?        
        $form = new Formulaires($formulaire_id);
        $first_champ = $form->getFirstChamp();
        $forms_with_qte = Formulaires::getFormulairesIdWithQuantite();
        $is_qte_form = in_array($formulaire_id, $forms_with_qte);
        
        $orderBy = $is_qte_form ? $first_champ['champ_id'].', qte_requise' : 'ordre';


        $formulaireForDisplay = $formulaire == 'EquipementsBioMedicaux' ? 'EquipementsBioMedicauxNoCoord' : $formulaire;

        $liste = $formulaireForDisplay::getListe(array('fiche_id' => $fiche->id, 'is_for_archive' => 1, 'token_archive' => (isset($token_archive) ? $token_archive : '')), $orderBy);
        unset($buffer["liste_$formulaireForDisplay"]);
        $last_liste = dm_strlen($last_token_archive) > 0 ? $formulaireForDisplay::getListe(array('fiche_id' => $fiche->id, 'token_archive' => $last_token_archive), $orderBy): array();

        $formulaireForDisplay::getDiffArchiveHTML($fiche->id, $liste, $last_liste, $token_archive, $last_token_archive);
?>
        <table style="position: relative; left: 25px;">
        <tr><td style="font-weight: bold; color: red; width: 125px;">
            <?=txt('Motif du changement')?>
        </td>
        <td style="color: red;">
            <div style="max-width: 480px;">
                <?=dm_nl2br($raison)?>
            </div>
        </td></tr>
        </table>
        <br style="clear: both;" />
        <br style="clear: both;" />
        <br style="clear: both;" />
    </div>
    <form id="form_raison" autocomplete="off" method="post" action="index.php?section=fiche&module=form_logs&fiche_id=<?=$fiche_id?>&token_archive=<?=$token_archive?><?=(isset($_GET['from_histo']) ? '&from_histo='.$_GET['from_histo'] : '')?>&formulaire=<?=$formulaire?>&token_archive_a_retablir=<?=$token_archive?>&can_edit_form=<?=(isset($_GET['can_edit_form']) ? intval($_GET['can_edit_form']) : 0)?><?=(isset($_GET['notification_gestion_id']) ? '&notification_gestion_id='.$_GET['notification_gestion_id'] : '')?><?=(isset($_GET['fiche_log_id']) ? '&fiche_log_id='.$_GET['fiche_log_id'] : '')?>">
<?
        $fiches_verrouillees_ou_j0 = Fiches::getFicheIdsDepotsVerouilles();
        $canRetablir = havePermission('edition_projet') && !in_array($fiche_id, $fiches_verrouillees_ou_j0) && isAutorizedFicheForStatut($fiche->id) && $formulaire::canAddDeleteMove() && !isset($_GET['notification_gestion_id']) && ($token_archive <> $logs[0]['token_archive'] || dm_strlen($logs[0]['token_archive']) > 0) && isset($_GET['can_edit_form']) && intval($_GET['can_edit_form']) == 1 && !$fiche->isFormulaireAffectedByGabarit($formulaire_id);
        if($canRetablir)
        {
?>
            <div style="position: relative;">
                <div style="position: absolute; right: 20px; top: -40px; text-align: right;">
<?
                    if(nbErreurs() > 0)
                    {
                        erreurs();
                    }
                    else 
                    {
?>
                        <div style="color: blue; margin-top: 10px;"><?=txt('SVP, écrire un motif particulier ou choisir parmi la sélection proposée.')?></div>
<?
                    }
?>
                    <select id="motif_changement" data-name="raison">
                        <option value=""> </option>
                        <?=MotifsChangements::getOptions()?>
                    </select>
                </div>
                <br style="clear: both;" />
                <br style="clear: both;" />
            </div>
<?
        }
?>
        <div style="text-align: right; width: 98%!important; position: relative; top: -10px;">
<?
            if($canRetablir)
            {
?>
                <button onclick="$('#form_raison').submit();"><?=txt('Rétablir la version sélectionnée')?></button>
<?
            }
            
            if(isset($_GET['notification_gestion_id']) && isset($_GET['fiche_log_id']))
            {
?>
                <button onclick="parent.window.location = 'index.php?section=notifications&module=index&notification_gestion_id=<?=intval($_GET['notification_gestion_id'])?>'; parent.$.fancybox.close();" name="close"><?=txt('Fermer')?></button>
<?
            }
            else
            {
?>
                <button onclick="parent.$.fancybox.close();return false;" name="close"><?=txt('Fermer')?></button>
<?
            }
?>
        </div>
    </form>
</div>
<?
$isPDF = 0;
include('config/footer.inc.php');

if(isset($_GET['notification_gestion_id']) && isset($_GET['fiche_log_id']))
{
    $db->autocommit(false);
    $sql = "update fiches_logs_notifications set ts_lu = current_timestamp where notification_gestion_id = ? and fiche_log_id = ? and usager_id = ?";
    $db->query($sql, array(intval($_GET['notification_gestion_id']), intval($_GET['fiche_log_id']), getIdUsager()));
    $db->commit();
}
<?
if (isset($_GET['pushToWebService'])) {
	$projet = new Projets(getProjetId());
	$result = $projet->pushToWebService(true);
}
include('config/header.inc.php');
?>
    <script>
    $(document).ready(function(){
        
        initFancy();

        $('#type_import').change(function(){
        	$('#importations_admin_form').submit();
        });

        $(document).on('click', 'img.alert', function(){
            var $this = $(this);
            $( "#dialog-alert" ).dialog({
                height:550,
                width:550,
                modal: true,
                open: function(ev, ui){
                    $('#message_dans_dialog').html($('#message_' + $this.data('import_id')).html());
                    initFancy();
                },
                buttons: {
                    "Fermer": function() {
                        $( this ).dialog( "close" );

                    }
                }
            });
        });
            
    });
    
    function initFancy()
    {
        $(document).find("a.fancy").fancybox({
            'overlayShow'		:	false,
            'type'				:	'iframe',
            'modal'				:	true,
            'centerOnScroll'	:	true,
            'width'             :   1024,
            'onComplete'		:	function(){
                $('#fancybox-frame').load(function() { // wait for frame to load and then gets it's height
                    fancybox_resize();
                });
            },
			'onClosed'			:	function(){
				$('html').css({ overflow: 'auto' });
			}
        });
    }
    </script>
    <style>
    #besoins td {
        text-align: left;
        padding: 0px 5px;
    }
    #besoins th {
        text-weight: bold;
        text-align: center;
    }
    </style>    
<?
$filtre = &$_SESSION['importation_ws2'];
$filtre['type_import'] = $filtre['type_import'] ?? 'local';

if(isset($_POST['type_import']))
{
    $filtre['type_import'] = $_POST['type_import'];
}

$filtre_applique = false;
if(dm_strlen($filtre['type_import']) > 0)
{
    $filtre_applique = true;
}

if (in_array($filtre['type_import'], ['local', 'equipement'])) {
    $usager = getUsager();
    $disciplines_auth = $usager->getDisciplinesAutorisees();
    $disciplines_autorises_ids = array_keys($disciplines_auth);
    $disciplines_autorises_ids[] = -1;

	$sql =
		"SELECT 
            distinct 
            fst.id AS id, 
            fst.nom AS nom,
            fst.id AS type_id,
            rc.classification       
        FROM rvt_relations_parametres rrp
        JOIN rvt_parametres rp ON rrp.rvt_parametre_id = rp.id 
        JOIN rvt_relations_familles rrf ON rrf.rvt_famille_id = rp.rvt_famille_id
        JOIN rvt_familles rf on rp.rvt_famille_id = rf.id
        JOIN master.rvt_categories rc on rf.rvt_categorie_id = rc.id
        JOIN fiches_sous_types fst ON fst.id = rrf.dm_fiche_sous_type_id
        WHERE rc.classification = ? and fst.discipline_id in (" . db::placeHolders($disciplines_autorises_ids) . ")";
    $result = $db->getAll($sql, array_merge([$filtre['type_import']], $disciplines_autorises_ids));
} else if (in_array($filtre['type_import'], ['installation'])) {
    $projectGuid = Projets::getProjectGuidStatic($CodeClient, getProjetId());
    $formulairesEditables = Formulaires::getListeFormulairesEditables();
    $formulaires_editables_ids = array_keys($formulairesEditables);
    $formulaires_editables_ids[] = -1;

	$sql = "SELECT 
            distinct 
            f.id AS id, 
            f.nom AS nom,
            fst.id AS type_id,
            rc.classification                 
		FROM rvt_relations_installations rri
		JOIN rvt_types rt on rri.rvt_type_id = rt.id
		JOIN rvt_familles rf ON rt.rvt_famille_id = rf.id
		JOIN rvt_relations_familles rrf ON rf.id = rrf.rvt_famille_id
        JOIN master.rvt_categories rc on rf.rvt_categorie_id = rc.id
        JOIN fiches_sous_types fst ON fst.id = rrf.dm_fiche_sous_type_id
        JOIN formulaires f on rri.dm_formulaire_id = f.id
        WHERE rc.classification = ? and f.id in (" . db::placeHolders($formulaires_editables_ids) . ")";
	$result = $db->getAll($sql, array_merge([$filtre['type_import']], $formulaires_editables_ids));
}

$titre = txt('Importation via LinkMatic');
include('fiche/titre_importation.inc.php');
$typesImport = [
    'local' => txt('Locaux (Revit rooms)'),
	'equipement' => txt('Équipements (Revit equipments)'),
	'installation' => txt('Liste d\'élément (Revit installations)'),
];
?>
<form action="index.php?section=fiche&module=importation_ws" method="post" style="margin: 0 20px 20px 0;" id="importations_admin_form">
    
    <span style="height: 25px;">
        <select id="type_import" name="type_import">
            <?
            foreach ($typesImport as $type_import => $nom) {
                ?>
                <option value="<?= $type_import ?>"<?= ($type_import == $filtre['type_import'] ? ' selected="selected"' : '') ?>><?= $nom ?></option>
                <?
            }
            ?>
        </select>
    </span>
    
</form>

<?
messages();
erreurs();
$projet = getProjetInfo();
if (GestionnaireRoute::instance()->haveAccesRoute('pushProjectToWebService')) {
    ?>
    <a href="index.php?action=pushProjectToWebService&projet_id=<?=$projet->id?>" style="float:right; color: blue;"><?= txt('Administration des associations') ?></a><br />
    <?php
}
?>
<table style="margin: 20px 0;">
<tr><th width="125">
	<?=txt('Nom')?>
</th>
<th width="65">
    &nbsp;
</th>
</tr>
<?
foreach($result as $i => $row)
{
	?>
	<tr><td>
        <?= ($row['classification'] == 'installation' ? ($row['nom'])::TITRE : $row['nom']) ?>
	</td>
    <td align="center" style="text-align: center!important;">
        <a class="fancy_big" style="position: relative; top: 3px;" href="index.php?section=fiche&module=synchronisation&valider_data_from_webservice=1&type_import_ws=<?= $filtre['type_import'] ?>&element_id=<?= intval($row['id']) ?>&type_id=<?= intval($row['type_id']) ?>">
            <img src="css/images/icons/dark/magnifying_glass.png"></a>
    </td>
	</tr>
	<?
}
?>
</table>
<br style="clear: both;" />
<div id="dialog-alert" title="Attention!!">
  <p id="message_dans_dialog"></p>
</div>
<?
include('config/footer.inc.php');
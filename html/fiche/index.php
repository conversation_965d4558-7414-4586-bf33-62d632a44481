<?
include('config/header.inc.php');

$sel_batiment_id = 0;
$sel_fiche_type_id = 0;
$sel_fiche_sous_type_id = 0;

$forced_fiche_id = isset($_GET['id']) ? intval($_GET['id']) : 0;
if ($forced_fiche_id > 0 && !isset($_GET['filtre_global'])) {
	unset($_SESSION['filtre_appliquefiches']);
	unset($_SESSION['filtre_avancefiches']);
	unset($_SESSION['filtre_topfiches']);
}
$inclure_supprimees = havePermission('effacement') ? 1 : 0;
$fiches = Fiches::getListe(array('term' => '', 'extract_is_orphelin' => 1, 'force_ordre' => 'code_ordre', 'batiment_id' => $sel_batiment_id, 'fiche_type_id' => $sel_fiche_type_id, 'fiche_sous_type_id' => $sel_fiche_sous_type_id));
if($forced_fiche_id == 0 && count($fiches) > 0)
{
    $fiche_tmp = array_values($fiches)[0];
	$fiche = new Fiches($fiche_tmp->id);
	$forced_fiche_id = $fiche->id;
}
?>
<script>
$(document).ready(function(){

    $(document).on('click', '#filtre_niveau_btn', function(){

        $('#filtre_niveau').toggle(400);

    });
    appliqueFiltre(<?=$forced_fiche_id?>, 'GEN', <?= (!isset($_GET['filtre_global']) ? 1 : 0) ?>);
	
});

var term = '';
var timout = setTimeout(function(){}, 1);

var infos = new Array();
infos['batiment'] = '';
infos['marque'] = '';
infos['modele'] = '';
</script>
<?
    $filtre_top = &$_SESSION['filtre_topfiches'];
    $filtre_top['code_fiche_search'] = isset($filtre_top['code_fiche_search']) ? $filtre_top['code_fiche_search'] : '';
    $filtre_top['code_fiche_search_autocomplete'] = isset($filtre_top['code_fiche_search_autocomplete']) ? $filtre_top['code_fiche_search_autocomplete'] : '';
    $filtre_top['batiment_id'] = isset($filtre_top['batiment_id']) ? $filtre_top['batiment_id'] : '';
    $filtre_top['fiche_type_id'] = isset($filtre_top['fiche_type_id']) ? $filtre_top['fiche_type_id'] : '';
    $filtre_top['fiche_sous_type_id'] = isset($filtre_top['fiche_sous_type_id']) ? $filtre_top['fiche_sous_type_id'] : '';
    $filtre_top['numero_lot'] = isset($filtre_top['numero_lot']) ? $filtre_top['numero_lot'] : '';
    $filtre_top['code_alternatif'] = isset($filtre_top['code_alternatif']) ? $filtre_top['code_alternatif'] : '';
    $filtre_top['id_revit'] = isset($filtre_top['id_revit']) ? $filtre_top['id_revit'] : '';
    $filtre_top['id_codebook'] = isset($filtre_top['id_codebook']) ? $filtre_top['id_codebook'] : '';
    $filtre_top['niveau'] = isset($filtre_top['niveau']) ? $filtre_top['niveau'] : array();

    $niveaux = BatimentsNiveaux::getListeReel(4, true);
?>

<div style="margin: 20px 0 0 0;">
<div id="content">

	<div id="blocFiltre" style="padding: 12px 5px 0px 15px; position: relative;">
<?
            ob_start();
            $options_differees = array();
            $nb_niveaux = count($niveaux);
            if($nb_niveaux > 0)
            {
                $niveau_parent_id = -1;
                foreach($niveaux as $i => $niveau)
                {
?>
                    <div style="float: left; width: 160px; margin-left: <?=($i == 0 ? '0px' : '10px')?>;">
                        <select id="niveau_<?=$niveau->id?>" name="niveau[<?=$niveau->id?>]" class="niveaux" style="width: 160px;" data-niveau_id="<?=$niveau->id?>" 
                            data-niveau_parent_id="<?=(isset($niveaux[($i-1)]) ? $niveaux[($i-1)]->id : -1)?>"
                            data-niveau_enfant_id="<?=(isset($niveaux[($i+1)]) ? $niveaux[($i+1)]->id : -1)?>"> 
                            <option value=""><?=('- '.$niveau->nom.' -')?></option>
<?
                            foreach($niveau->batiments_niveaux_elements as $j => $element)
                            {
                                $sel = (isset($filtre_top['niveau'][$niveau->id]) && $filtre_top['niveau'][$niveau->id] == $element['id'] ? ' selected="selected"' : '');

                                $display = '';
                                $tag = '';
                                $classes = array();
                                if(isset($element['batiment_niveau_element_id'])) {
                                    $parent = $niveau->batiments_niveaux_elements_precedants[$element['batiment_niveau_element_id']];
                                    foreach ($parent['ids'] as $id) {
                                        $classes[] = 'parent_niveau_id_' . $id;
                                    }
                                }

                                $suffix = $i > 0 ? ' ('.$parent['nom'].')' : '';
	                            if ($i < 3) {
		                            ?>
                                    <option value="<?=$element['id']?>" data-nom="<?=$element['nom']?>" style="display: <?=$display?>;" class="all <?=$tag?> <?=dm_implode(' ', $classes)?>"<?=$sel?>><?=$element['nom'].$suffix?></option>
		                            <?php
	                            } else {
		                            $options_differees['niveau_'.$niveau->id][] = '<option value="'.$element['id'].'" data-nom="'.$element['nom'].'" style="display: '.$display.';" class="all '.$tag.' '.dm_implode(' ', $classes).'"'.$sel.'>'.$element['nom'].$suffix.'</option>';
	                            }
                            }
?>
                        </select>
                    </div>
<?
                    $niveau_parent_id = $niveau->id;
                }
            }
            $filtre_niveaux = ob_get_clean();
            
            ob_start();
?>
            <div style="float: left; width: 160px; margin: 0 10px 10px 0;">
                <select id="batiment_id" name="batiment_id" style="width: 160px;">
                    <option value="">- <?=txt('Bâtiment')?> -</option>
<?        
                    Batiment::getOptions(array('ouvrage_id' => getOuvrageId()), $filtre_top['batiment_id']);
?>
                </select>
			</div>
			<div style="float: left; width: 160px; margin: 0 10px 10px 0;">
                <select id="fiche_type_id" name="fiche_type_id" style="width: 160px;">
                    <option value="">- <?=txt('Type')?> -</option>
<?
                    FicheType::getOptions(array(), $filtre_top['fiche_type_id']);
?>
                </select>
			</div>
			<div style="float: left; width: 160px; margin: 0 10px 10px 0;">
                <select id="fiche_sous_type_id" name="fiche_sous_type_id" style="width: 160px;">
                    <option value="">- <?=txt('Sous-type')?> -</option>
<?
                    $filtre_tmp = isset($filtre_top['fiche_type_id']) && $filtre_top['fiche_type_id'] > 0 ? array('fiche_type_id' => $filtre_top['fiche_type_id']) : array();
                    FicheSousType::getOptions($filtre_tmp, $filtre_top['fiche_sous_type_id']);
?>
                </select>
			</div>
			<div style="float: left; width: 160px; margin: 0 10px 10px 0;">
                <select id="numero_lot" name="numero_lot" style="width: 160px;">
                    <option value="">- <?=txt('Numéro de lot')?> -</option>
<?
                    $numeros_lots = Fiches::getAllNumerosLots();
                    foreach($numeros_lots as $i => $numero_lot)
                    {
                        $sel = $filtre_top['numero_lot'] == $numero_lot ? ' selected="selected"' : '';
?>
                        <option value="<?=$numero_lot?>"<?=$sel?>><?=$numero_lot?></option>
<?
                    }
?>
                </select>
			</div>
			<div class="filtre_optionnel" style="float: left; width: 160px; margin: 0 10px 10px 0; display: none;">
                <input type="text" id="code_alternatif" value="<?=($filtre_top['code_alternatif'])?>" maxlength="200" style="width: 153px; padding-left: 5px;" placeholder="<?=txt('Code alternatif')?>" />
			</div>
			<div class="filtre_optionnel" style="float: left; width: 160px; margin: 0 10px 10px 0; display: none;">
                <input type="text" id="id_revit" value="<?=($filtre_top['id_revit'])?>" maxlength="200" style="width: 153px; padding-left: 5px;" placeholder="<?=txt('ID Externe 1')?>" />
			</div>
			<div style="float: left; width: 160px; margin: 0 10px 10px 0; display: none;">
                <input type="text" id="id_codebook" value="<?=($filtre_top['id_codebook'])?>" maxlength="200" style="width: 153px; padding-left: 5px;" placeholder="<?=txt('ID Externe 2')?>" />
			</div>
			<div class="filtre_optionnel" style="float: left; width: 100px; margin: 0 10px 10px 0; display: none;">
                <input class="filtrer" name="filtrer" id="filtrer_rechercher" value="<?=txt('Rechercher')?>" style="min-height: 20px!important; padding: 0 20px;" type="submit">
			</div>
<?
            $filtre_batiments = ob_get_clean();
            $condition_affichage_bloc_top = false;
            $projet = getProjetInfo();
            if($projet->isStructureHierarchiqueFiltresPrincipaux())
            {
                $condition_affichage_bloc_top = ((isset($filtre_top['batiment_id']) && intval($filtre_top['batiment_id']) > 0) 
                                                    || (isset($filtre_top['fiche_type_id']) && intval($filtre_top['fiche_type_id']) > 0)
                                                    || (isset($filtre_top['fiche_sous_type_id']) && intval($filtre_top['fiche_sous_type_id']) > 0)
                                                    || (isset($filtre_top['numero_lot']) && intval($filtre_top['numero_lot']) > 0));
            }
            else
            {
                $condition_affichage_bloc_top = isset($filtre_top['niveau']) && is_array($filtre_top['niveau']) && count($filtre_top['niveau']) > 0;
            }
?>
        <div id="filtre_niveau" style="position: absolute; top: -19px; margin: 0 10px 10px 0; width: 100%;<?=($condition_affichage_bloc_top ? '' : 'display: none;')?>">
            <?=($projet->isStructureHierarchiqueFiltresPrincipaux() ? $filtre_batiments : $filtre_niveaux)?>
        </div>
		<div style="width: 100%; float: left;">
			<?=($projet->isStructureHierarchiqueFiltresPrincipaux() ? $filtre_niveaux : $filtre_batiments)?>
    
            <div style="float: left; width: 110px; margin: 0 10px 10px 0;">
                <a href="javascript: void(0);" id="clear_filtre" style="display: inline!important;" title="<?=txt('Libérer tous les filtres')?>">
                    <img src="css/images/icons/dark/bended_arrow_left.png" /></a>
                <a href="index.php?section=filtre_avance&module=filtre_avance&type=fiches" class="fancy" style="display: inline!important;" title="<?=txt('Filtres avancés')?>">
                    <img src="css/images/icons/dark/filter.png" /></a>
                <a href="" id="refresh" style="display: inline!important;" title="<?=txt('Rafraîchir la page')?>">
                    <img src="css/images/icons/dark/refresh_3.png" /></a>
<?
                if(count($niveaux) > 0)
                {
?>
                    <a href="javascript: void(0);" id="filtre_niveau_btn" style="display: inline!important;" title="<?=txt('Afficher les filtres supplémentaires')?>">
                        <img src="css/images/icons/dark/triangle_up_down.png" /></a>
<?
                }
?>
			</div>
		</div>
		
		<br style="clear: both;" />
		
	</div>
        
    <div style="position: relative;">
        <span style="position: absolute; top: 8px; right: 15px; font-size: 14px; color: red;"><span id="decompte"><?=count($fiches)?></span>&nbsp;<?=($_GET['section'] == 'gabarits' ? txt('gabarit(s)') : txt('fiche(s)'))?></span>
	</div>
    
    <div id="ficheFiltre" style="position: relative; z-index: 1; margin-top: 7px!important; margin-top: 8px!important; display: none;">
<?
        include('fiche/filtre.php');
?>
    </div>
    
	<div id="ficheContent" style="position: relative; z-index: 3; margin-top: 7px!important; margin-top: 8px!important;">
<?
        if(count($fiches) == 0 && !isFicheReadonly())
        {
?>
            <a href="index.php?section=admin&module=edit&id=0&table=fiches&type=client&is_gabarit=<?=(isset($_GET['section']) && $_GET['section'] == 'gabarits' ? 1 : 0)?>" id="ajout_fiche" class="add">
                <img src="css/images/icons/dark/plus.png" /></a>
<?
        }
        
        if(isset($fiches) && count($fiches) > 0)
        {
            if($forced_fiche_id > 0)
            {
                $_GET['id'] = $forced_fiche_id;
                $_GET['f_section'] = 'GEN';
                include('fiche/fiche.php');
            }
        }
?>
        
	</div>
</div>
</div>
<nav>
    <div style="display: inline-block;">
        <input type="text" id="codeFiche" style="width: 257px!important;" value="<?=((isset($filtre_top['code_fiche_search_autocomplete']) && dm_strlen($filtre_top['code_fiche_search_autocomplete']) > 0 ? $filtre_top['code_fiche_search_autocomplete'] : $filtre_top['code_fiche_search']))?>" />
        <a style="position: relative; top: 5px; right: -7px;" href="index.php?section=fiche&module=export_codes&filtre_global_to_use=fiches" onclick="loading_beforeunload = false;" title="<?=txt('Exporter les codes')?>">
            <img src="css/images/icons/dark/DM_export_CSV.png"></a>
    </div>

    <!--<div id="message_res" style="color: red; height: 15px; position: absolute; top: 2px; left: 160px; width: 16px;">
<?
    //if(count($fiches) >= 1000)
    //{
    //print '<img src="css/images/icons/dark/alert_red.png" width="18" title="'.txt('Avis: Le résultat de recherche correspondant au critères de filtration actuels comporte un très grand nombre de fiches. Seuls les 1000 premiers résultats sont disponibles dans la liste ci-dessous. Astuce! Veuillez raffiner vos critères de filtration pour mieux cibler le résultat souhaité.').'"/>';
    //}
    ?>
</div>-->
    <ul id="listeFiches" style="position: relative; top: 16px; min-height: 670px; height: 670px;">
    </ul>
    <script>
		const listeFiches = document.getElementById('listeFiches');

		// Gestion de l'événement de clic sur les éléments de la liste
		listeFiches.addEventListener('click', function(event) {
			const item = event.target;

			// Déselectionne les éléments sélectionnés
			const selectedItems = document.querySelectorAll('#listeFiches .selected');
			selectedItems.forEach(selectedItem => {
				if (selectedItem !== item) {
					selectedItem.classList.remove('selected');
				}
			});

			// Sélectionne l'élément cliqué
			if (item.tagName === 'LI') {
				const container = listeFiches.getBoundingClientRect();
				const itemRect = item.getBoundingClientRect();

				if (!item.classList.contains('selected')) {
					item.classList.add('selected');
				} else {
					item.classList.remove('selected'); // Deselectionne l'élément
				}

				// Affiche l'élément sélectionné dans la console
				const selectedItem = listeFiches.querySelector('.selected');
				if (selectedItem) {
					reloadFiche(selectedItem.getAttribute('value'));

					// Fait défiler la liste pour que l'élément sélectionné soit visible
					if (itemRect.top < container.top) {
						listeFiches.scrollTop -= container.top - itemRect.top;
					} else if (itemRect.bottom > container.bottom) {
						listeFiches.scrollTop += itemRect.bottom - container.bottom;
					}
				}
			}
		});

		// Gestion de l'événement de clavier pour naviguer à l'intérieur de la sélection
		document.addEventListener('keydown', function(event) {
			const key = event.key;
			if (key === 'ArrowUp' || key === 'ArrowDown') {
				event.preventDefault();

				const selected = document.querySelector('#listeFiches .selected');
				if (selected) {
					const direction = key === 'ArrowUp' ? 'previousElementSibling' : 'nextElementSibling';
					const sibling = selected[direction];

					if (sibling && sibling.tagName === 'LI') {
						selected.classList.remove('selected');
						sibling.classList.add('selected');

						const container = listeFiches.getBoundingClientRect();
						const siblingRect = sibling.getBoundingClientRect();

						// Fait défiler la liste pour que l'élément sélectionné soit visible
						if (siblingRect.top < container.top) {
							listeFiches.scrollTop -= container.top - siblingRect.top;
						} else if (siblingRect.bottom > container.bottom) {
							listeFiches.scrollTop += siblingRect.bottom - container.bottom;
						}

						reloadFiche(sibling.getAttribute('value'));
					}
				}
			}
		});
    </script>
</nav>
<?
if(isset($options_differees) && count($options_differees) > 0) {
	?>
    <options_differees style="display: none;">
		<?php
		foreach($options_differees as $key => $options)
		{
			?>
            <span data-idselect="<?=$key?>">
                <?php
                foreach($options as $i => $option)
                {
                    print $option."\n";
                }
                ?>
            </span>
			<?php
		}
		?>
    </options_differees>
	<?php
}
$includeFiche = 1;
include('config/footer.inc.php');
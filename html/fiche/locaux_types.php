<?
require_once __DIR__ . '/../../vendor/autoload.php';

use PhpOffice\PhpSpreadsheet\Cell\Coordinate;
use PhpOffice\PhpSpreadsheet\IOFactory;

if(isset($_POST['maj']))
{
    $lignes = array();
    $alias = array();
    $champs = array();

    if(!isset($_FILES['fichier']) || !is_uploaded_file($_FILES['fichier']['tmp_name']))
    {
        setErreur(txt('Vous devez sélectionner le fichier à apparier.'));
    }
    else
    {
        $sheet_title = "";
	    $spreadsheet = IOFactory::load($_FILES['fichier']['tmp_name']);
        foreach ($spreadsheet->getWorksheetIterator() as $iter_sheet => $worksheet)
        {
            if($iter_sheet > 0)
            {
                break;
            }
            
            $sheet_title        = $worksheet->getTitle();
            $highestRow         = $worksheet->getHighestDataRow(); // e.g. 10
            $highestColumn      = $worksheet->getHighestDataColumn(); // e.g 'F'
            $highestColumnIndex = Coordinate::columnIndexFromString($highestColumn);

            $row_titre = 1;
            $ligne_titre = array();
            $ids_internes = array();
            $codes_local_type = array();
            for ($row = 1; $row <= $highestRow; ++ $row) 
            {
                for ($col = 1; $col <= $highestColumnIndex; ++ $col)
                {
                    $cell = $worksheet->getCellByColumnAndRow($col, $row);
                    $val = sanitizeString((string)$cell->getCalculatedValue());

                    if($row == $row_titre)
                    {
                        if(dm_strlen($val) == 0)
                        {
                            break;
                        }
                        $ligne_titre[$col] = $val;
                    }
                    else if($col == 0)
                    {
                        $codes_local_type[$row] = $val;
                    }
                }
            }
        }
        
        if(count($codes_local_type) > 0)
        {
            $local_type_nombres = Fiches::getLocauxTypesNumbers();
            
            $dataArray = array();
            $dataArray[0] = $ligne_titre;

            foreach($codes_local_type as $row => $code_local_type)
            {
                $row_iter = ($row-1);
                $dataArray[$row_iter][0] = htmlspecialchars_decode($code_local_type);
                $dataArray[$row_iter][1] = $local_type_nombres[$code_local_type]['nb'] ?? "";
            }

	        exportExcelFromDataArray($dataArray, $sheet_title, $_FILES['fichier']['name']);
            exit;
        }
    }
}

$noHeader = true;
include('config/header.inc.php');
?>
<div id="popupContent">

    <form id="form" autocomplete="off" method="post" action="index.php?section=fiche&module=locaux_types" enctype="multipart/form-data">
        <label><?=txt('Auto-répartition des locaux')?></label>
        <fieldset style="min-height: 175px; padding: 10px 0 0 50px; width: 920px;">    
            <div id="editables">
<?
                erreurs();
?>
                <section style="margin: 10px 0 30px 0;">
                    <span for="fichier"><?php print txt('Fichier MS Excel à mettre à jour'); ?></span><br /><br />
                    <span>
                        <input type="file" id="fichier" name="fichier" style="border: solid 0px!important; max-width: 300px!important;" />
                    </span>
                </section>
                
                <section style="margin: 10px 0 30px 0;">
                    <div style="font-weight: bold; margin-bottom: 5px;"><?=txt('Voici la structure que doit avoir votre fichier:')?></div>
                    <div><?=txt('Le titre des colonnes n\'a aucune importance mais les données qu\'elles contiennent doivent être selon les spécifications inscrites ci-dessous. Les données de la deuxième colonne seront fournies/mise à jour par DocMatic lors de la mise-à-jour du fichier.')?></div>
                </section>
                
                <section style="margin: 10px 0 30px 0;">
                    <table style="margin-top: 5px;"><tr>
                    <th>
                        [<?=txt('Code de local type')?>]
                    </th><th>
                        [<?=txt('Quanitité de locaux de ce type dans DocMatic')?>]
                    </th>
                    </tr><tr>
                    <td>
                        <?=txt('1410.01.06.05.')?>
                    </td><td>
                        [<?=txt('Quanitité 1 (')?><span style="text-decoration: underline;"><?=txt('mise à jour par DocMatic')?></span>)]
                    </td>
                    </tr><tr>
                    <td>
                        <?=txt('1410.01.20.50.')?>
                    </td><td>
                        [<?=txt('Quanitité 2 (')?><span style="text-decoration: underline;"><?=txt('mise à jour par DocMatic')?></span>)]
                    </td>
                    </tr><tr>
                    <td>
                        <?=txt('1450.02.19.01.')?>
                    </td><td>
                        [<?=txt('Quanitité 3 (')?><span style="text-decoration: underline;"><?=txt('mise à jour par DocMatic')?></span>)]
                    </td>
                    </tr><tr>
                    <td>
                        ...
                    </td><td>
                        ...
                    </td>
                    </tr></table>
                </section>

            </div>
        </fieldset>
        
        <div style="text-align: left; width: 715px!important; float: left; margin-left: 55px; margin-bottom: 10px;">
        
            <span style="font-weight: bold; color: blue;"><?=txt('DocMatic effectura l\'appariement de toutes les données qui trouveront un correspondance dans le registre des fiches.<br />Une fois mise-à-jour, votre fichier sera populé des quantités de locaux de chaque type et vous sera retourné automatiquement.', false)?></span>
        
        </div>
        <div style="text-align: right; width: 250px!important; float: left; vertical-align: bottom;">
            
            <button class="submit" value="submitbuttonvalue" name="maj" onclick="loading_beforeunload = false;"><?=txt('Mettre à jour le fichier')?></button>
            <button onclick="parent.$.fancybox.close();return false;" name="close"><?=txt('Annuler')?></button>
            
        </div>
        <br style="clear: both;" />
    </form>
</div>    
<?
include('config/footer.inc.php');
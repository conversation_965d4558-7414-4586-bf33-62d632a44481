<?

ini_set("memory_limit", "10000M");
ini_set('max_execution_time', 30000);

$champs = Config::instance()->getChampsFiltresSpeciauxEquipementsBioMedicaux(true);

$filtre = &$_SESSION['filtre_ExportationLogsDetailles'];
$filtre['fiche_sous_type_id'] = $filtre['fiche_sous_type_id'] ?? 0;
$filtre['formulaire_id'] = $filtre['formulaire_id'] ?? 0;
$filtre['champ_nom'] = $filtre['champ_nom'] ?? [];
$filtre['filtre_global'] = $filtre['filtre_global'] ?? 0;
$filtre['date_debut'] = $filtre['date_debut'] ?? date('Y-m-d', strtotime(date('Y-m-d') . ' - 3 months'));
$filtre['date_fin'] = $filtre['date_fin'] ?? date('Y-m-d');

$filtre['filtre_global'] = (isset($_SESSION['filtre_avance_logs_detailles']) && count(
        $_SESSION['filtre_avance_logs_detailles']
    ) > 0) || (isset($_SESSION['filtre_top_logs_detailles']) && count(
        $_SESSION['filtre_top_logs_detailles']
    ) > 0) ? $filtre['filtre_global'] : false;

$forms_with_qte = Formulaires::getFormulairesIdWithQuantite();
$is_intrinseque = false;
foreach ($filtre['champ_nom'] as $champ_nom) {
    $exploded_champ_nom = dm_explode('__', $champ_nom);
    if ($exploded_champ_nom[0] == 'intrinseque' || isset($champs[$champ_nom])) {
        $is_intrinseque = true;
    }
}

if (isset($_GET['export'])) {
    $filtredCache = new FiltredCache('ExportationLogsDetailles', $filtre);
    $logs = $filtredCache->getFilteredData();

    $dataArray = array();
    $ligne = 0;
    $dataArray[$ligne][] = txt('Date / Heure', false);
    $dataArray[$ligne][] = txt('Usager', false);
    $dataArray[$ligne][] = txt('Fiche', false);
    $dataArray[$ligne][] = txt('Formulaire', false);
    $dataArray[$ligne][] = txt('Champ', false);
    if (in_array($filtre['formulaire_id'], $forms_with_qte) || $is_intrinseque) {
        $dataArray[$ligne][] = txt('Type d\'équipement', false);
    }
    $dataArray[$ligne][] = txt('Valeur avant', false);
    $dataArray[$ligne][] = txt('Valeur après', false);
    $ligne++;
    foreach ($logs as $i => $log) {
        $dataArray[$ligne][] = dm_substr(dm_htmlspecialchars_decode($log['ts_ajout']), 0, 19);
        $dataArray[$ligne][] = dm_htmlspecialchars_decode($log['prenom_usager'] . ' ' . $log['nom_usager']);
        $dataArray[$ligne][] = dm_htmlspecialchars_decode($log['code_fiche']);
        $dataArray[$ligne][] = (isset($className) && dm_strlen($className) > 0 ? txt($className::TITRE, false) : '');
        $dataArray[$ligne][] = dm_htmlspecialchars_decode($log['titre_champ']);
        if (in_array($filtre['formulaire_id'], $forms_with_qte) || $is_intrinseque) {
            $dataArray[$ligne][] = dm_htmlspecialchars_decode($log['equipement']);
        }
        $dataArray[$ligne][] = strval(
            isset($log['valeur_avant_excel']) ? str_replace('=', '', dm_htmlspecialchars_decode($log['valeur_avant_excel'])) : ''
        );
        $dataArray[$ligne][] = strval(
            isset($log['valeur_apres_excel']) ? str_replace('=', '', dm_htmlspecialchars_decode($log['valeur_apres_excel'])) : ''
        );
        $ligne++;
    }

    $nom_fichier = 'logs_detailles_' . date('Y-m-d');
    exportDataSelonFormat($dataArray, $nom_fichier, $nom_fichier, 'forceString', [], [], $_GET['format']);
    exit;
}

if (isset($_GET['filtre_global'])) {
    $filtre['filtre_global'] = intval($_GET['filtre_global']);
    $page = 1;
}

$page = 1;
if (isset($_GET['page']) && intval($_GET['page']) > 0) {
    $page = intval($_GET['page']);
}

if (isset($_POST['fiche_sous_type_id'])) {
    $filtre['fiche_sous_type_id'] = intval($_POST['fiche_sous_type_id']);
    $page = 1;
}

if (isset($_POST['formulaire_id'])) {
    $filtre['formulaire_id'] = intval($_POST['formulaire_id']);
    $page = 1;
}

if (isset($_POST['champ_nom'])) {
    $filtre['champ_nom'] = $_POST['champ_nom'];
    $page = 1;
} elseif (isset($_POST['formulaire_id'])) {
    $filtre['champ_nom'] = [];
    $page = 1;
}

if (isset($_POST['date_debut'])) {
    $filtre['date_debut'] = $_POST['date_debut'];
    $filtre['page'] = 1;
}

if (isset($_POST['date_fin'])) {
    $filtre['date_fin'] = $_POST['date_fin'];
    $filtre['page'] = 1;
}

$where = ' and projet_id = ?';
$data = array(getProjetId());

$can_fetch = false;
if (isset($filtre['fiche_sous_type_id']) && intval($filtre['fiche_sous_type_id']) > 0) {
    $where .= ' and fiches.fiche_sous_type_id = ?';
    $data[] = intval($filtre['fiche_sous_type_id']);
}

if (isset($filtre['formulaire_id']) && intval($filtre['formulaire_id']) > 0) {
    $where .= ' and formulaire_id = ?';
    $data[] = intval($filtre['formulaire_id']);
}

if (isset($filtre['date_debut']) && dm_strlen($filtre['date_debut']) > 0) {
    $where .= ' and lip.ts_ajout >= ?';
    $data[] = $filtre['date_debut'] . ' 00:00:00';
}

if (isset($filtre['date_fin']) && dm_strlen($filtre['date_fin']) > 0) {
    $where .= ' and lip.ts_ajout <= ?';
    $data[] = $filtre['date_fin'] . ' 23:59:59';
}

if (isset($filtre['champ_nom']) && count($filtre['champ_nom']) > 0 && isset($filtre['formulaire_id']) && intval(
        $filtre['formulaire_id']
    ) > 0) {
    $can_fetch = true;
}

if ($filtre['filtre_global']) {
    $fiches = Fiches::getListe(array('filtre_global_to_use' => '_logs_detailles'));
    $filtre['fiche_ids'] = array_keys($fiches);
} else {
    $filtre['fiche_ids'] = null;
}

if (isset($filtre['fiche_ids'])) {
    if (count($filtre['fiche_ids']) > 0) {
        $where .= " and fiches.id in ('" . dm_implode("', '", $filtre['fiche_ids']) . "')";
    } else {
        $where .= " and false";
    }
}

//$logger = new ScriptLogger();
$disable_sql_log = 1;
$logs = [];
$filtredCache = new FiltredCache('ExportationLogsDetailles', $filtre);
if (!isset($_POST['filtrer']) && $filtredCache->isExistData()) {
    $logs = $filtredCache->getFilteredData();
    $className = Formulaires::getNomFromId($filtre['formulaire_id']);
} elseif ($can_fetch) {
    $className = Formulaires::getNomFromId($filtre['formulaire_id']);
    $form = new Formulaires($filtre['formulaire_id']);
    $first_champ = $form->getFirstChamp();
    $metas = $className::getFlatChampsSaisieMetaData();
    $is_qte_form = in_array($filtre['formulaire_id'], $forms_with_qte);

    $fetch_fiches_logs = false;
    foreach ($filtre['champ_nom'] as $champ_nom) {
        $exploded_champ_nom = dm_explode('__', $champ_nom);
        if ($exploded_champ_nom[0] <> 'intrinseque' && dm_strlen($champ_nom) > 0) {
            $fetch_fiches_logs = true;
        }
    }
    $fiche_logs = [];
    if ($fetch_fiches_logs) {
        $orderBy = $is_qte_form ? $first_champ['champ_id'] . ', quantite_id' : 'ordre';
        $sql = "select distinct lip.id, lip.ts_ajout, usagers.nom as nom_usager, usagers.prenom as prenom_usager, 
                    fiches.code as code_fiche, formulaires.nom as formulaire, token_archive, raison, fiche_id,
                    concat(usagers.prenom, ' ', usagers.nom) as usager
            from fiches_logs lip
            join usagers on usagers.id = usager_ajout_id
            join fiches on fiches.id = fiche_id
            left join formulaires on formulaires.id = formulaire_id
            where true $where
            order by lip.ts_ajout desc";
//            printDebug(dm_nl2br(db::interpolateQuery($sql, $data)));
        $fiche_logs = $db->getAll($sql, $data);

        // $logger->log('After $fiche_logs = $db->getAll($sql, $data);');

        // $logger->initIteration();
        // $logger->setTotalIterations(count($fiche_logs));
        foreach ($fiche_logs as $i => $log) {
//            // $logger->log('foreach ($fiche_logs as $i => $log) { 1', 'debut');
            $classNameForDisplay = $className == 'EquipementsBioMedicaux' ? 'EquipementsBioMedicauxNoCoord' : $className;

            $liste = $classNameForDisplay::getListe(
                array(
                    'fiche_id' => $log['fiche_id'],
                    'is_for_archive' => 1,
                    'token_archive' => (isset($log['token_archive']) && dm_strlen(
                        $log['token_archive']
                    ) > 0 ? $log['token_archive'] : '')
                ),
                $orderBy
            );
            unset($buffer["liste_$classNameForDisplay"]);

            $last_token_archive = Fiches::getLastTokenArchive(
                $log['fiche_id'],
                $filtre['formulaire_id'],
                $log['token_archive']
            );

            $last_liste = dm_strlen($last_token_archive) > 0 ? $classNameForDisplay::getListe(
                array('fiche_id' => $log['fiche_id'], 'token_archive' => $last_token_archive),
                $orderBy
            ) : array();

            $liste_diffs = [];
            foreach ($liste as $id => &$ligne) {
                $ligne_diff = $last_liste[$ligne->id] ?? null;
                foreach ($filtre['champ_nom'] as $champ_nom) {
                    if (!isset($metas[$champ_nom])) {
                        continue;
                    }

                    $meta = $metas[$champ_nom];
                    $is_ligne_champ_set = isset($ligne->$champ_nom);
                    $is_ligne_diff_champ_set = isset($ligne_diff->$champ_nom);

                    if ($is_ligne_champ_set) {
                        $trimmed_len_ligne_champ = dm_strlen(dm_trim($ligne->$champ_nom));
                    }

                    if (($is_ligne_champ_set && !$is_ligne_diff_champ_set && $trimmed_len_ligne_champ > 0)
                        || ($is_ligne_diff_champ_set && $ligne->$champ_nom <> $ligne_diff->$champ_nom))
                    {
                        $valeur_avant = $is_ligne_diff_champ_set ? $ligne_diff->$champ_nom : '';

                        $diff = [
                            'valeur_avant' => $valeur_avant,
                            'valeur_apres' => $ligne->$champ_nom,
                            'valeur_avant_excel' => $valeur_avant,
                            'valeur_apres_excel' => $ligne->$champ_nom,
                            'titre_champ' => $meta['titre'],
                            'nom_champ' => $champ_nom,
                            'id' => $ligne->id,
                        ];

                        if ($is_qte_form) {
                            $first_champ_nom = $first_champ['nom_champ'];
                            $first_champ_id = $first_champ['champ_id'];
                            $diff['equipement'] = $ligne->$first_champ_nom;
                            $diff['equipement_id'] = $ligne->$first_champ_id;
                        }

                        $liste_diffs[] = $diff;
                    }
                }
                unset($liste[$id]);
                if (isset($last_liste[$id])) {
                    unset($last_liste[$id]);
                }
            }

            if ($liste_diffs) {
                $fiche_logs[$i]['liste_diffs'] = $liste_diffs;
            } else {
                unset($fiche_logs[$i]);
            }
//            // $logger->log('- DONE', 'fin');
        }
    }
    // $logger->stopIteration();

    $class = $first_champ['classe'];
    $formulaires_listes = $class::getFormulairesListes($class);
    $metasType = $class::getFlatChampsSaisieMetaData();

    $ids = [];
    $nom_parametres = [];

    foreach ($filtre['champ_nom'] as $champ_nom) {
        $exploded_champ_nom = dm_explode('__', $champ_nom);
        if ($exploded_champ_nom[0] == 'intrinseque' || isset($champs[$champ_nom])) {
            if (isset($champs[$champ_nom])) {
                if (isset($metasType[$champ_nom])) {
                    $nom_parametres[$metasType[$champ_nom]['titre']] = $metasType[$champ_nom]['titre'];
                } else {
                    continue;
                }
            } else {
                $ids[$exploded_champ_nom[2]] = $exploded_champ_nom[2];
            }
        }
    }

    // $logger->log('After foreach ($filtre[\'champ_nom\'] as $champ_nom) {');

    if ($className == 'EquipementsBioMedicaux') {
        $sql = "select 0 as fiche_id, lipl.nom_parametre as titre_champ, 'test' as code_fiche, lip.ts_ajout, lipl.element_name as equipement,
                      lipl.nouvelle_valeur as valeur_apres, lipl.ancienne_valeur as valeur_avant, u.nom as nom_usager, u.prenom as prenom_usager,
                        lipl.nouvelle_valeur as valeur_apres_excel, lipl.ancienne_valeur as valeur_avant_excel, 1 as is_intrinseque, 
                        ebmt.id as equipement_bio_medicaux_type_id,
                        concat(u.prenom, ' ', u.nom) as usager
                    from logs_imports_parametres_lignes lipl
                    join logs_imports_parametres lip on lip.id = lipl.log_import_parametre_id
                    left join formulaires_listes_parametres flp on flp.nom_parametre = lipl.nom_parametre
                    left join formulaires_listes fl on fl.id = flp.formulaire_liste_id
                    join equipements_bio_medicaux_types ebmt on ebmt.nom = lipl.element_name
                    join usagers u on u.id = lip.usager_ajout_id
                    ";

        $where = "";
        $data = [];
        if (count($ids) > 0) {
            $where .= " and fl.id = ?";
            $data[] = $formulaires_listes[0]->id;

            $where .= " and flp.id in (" . db::placeHolders($ids) . ")";
            $data = array_merge($data, $ids);
        } elseif (count($nom_parametres) > 0) {
            $where .= " and lipl.nom_parametre in (" . db::placeHolders($nom_parametres) . ")";
//        $data = array_merge($data, array_values($nom_parametres));
            $data = [...$data, ...array_values($nom_parametres)];
        } else {
            $where .= "and false";
        }

        if (isset($filtre['date_debut']) && dm_strlen($filtre['date_debut']) > 0) {
            $where .= ' and lip.ts_ajout >= ?';
            $data[] = $filtre['date_debut'] . ' 00:00:00';
        }

        if (isset($filtre['date_fin']) && dm_strlen($filtre['date_fin']) > 0) {
            $where .= ' and lip.ts_ajout <= ?';
            $data[] = $filtre['date_fin'] . ' 23:59:59';
        }

        $sqlToUse = $sql . "
                    where true $where and coalesce(ancienne_valeur, '') <> coalesce(nouvelle_valeur, '')
                    order by equipement, code_fiche, lip.ts_ajout desc";
        $logs_tmp = $db->getAll($sqlToUse, $data);

        // $logger->log('After $logs_tmp = $db->getAll($sqlToUse, $data);');

        $equipement_bio_medicaux_type_ids = count($logs_tmp) == 0 ? [0] : array_column(
            $logs_tmp,
            'equipement_bio_medicaux_type_id'
        );

        $sql = "select ebm.equipement_bio_medicaux_type_id, f.id as fiche_id, f.code as code_fiche
            from equipements_bio_medicaux ebm
            join fiches f on ebm.fiche_id = f.id
            where equipement_bio_medicaux_type_id in (" . db::placeHolders($equipement_bio_medicaux_type_ids) . ")
                and projet_id = ?";
        $equipements_fiches_tmp = $db->getAll($sql, [...$equipement_bio_medicaux_type_ids, ...[getProjetId()]]);

        // $logger->log('After $equipements_fiches_tmp = $db->getAll($sql, [...$equipement_bio_medicaux_type_ids, ...[getProjetId()]]);');

        unset($equipement_bio_medicaux_type_ids);

        $equipements_fiches = [];
        foreach ($equipements_fiches_tmp as $row) {
            $equipements_fiches[$row['equipement_bio_medicaux_type_id']][$row['fiche_id']] = $row;
        }

        $logs = [];

        foreach ($logs_tmp as $it => $row) {
            if (isset($equipements_fiches[$row['equipement_bio_medicaux_type_id']])) {
                foreach ($equipements_fiches[$row['equipement_bio_medicaux_type_id']] as $rowFiche) {
                    $rowE = clone($row);
                    $rowE['fiche_id'] = $rowFiche['fiche_id'];
                    $rowE['code_fiche'] = $rowFiche['code_fiche'];
                    $logs[] = $rowE;
                }
            }
            unset($logs_tmp[$it]);
        }
        unset($equipements_fiches);
    }
    // $logger->log('After foreach ($logs_tmp as $it => $row) {');

    // $logger->initIteration();
    // $logger->setTotalIterations(count($fiche_logs));
    foreach ($fiche_logs as $i => $log) {
//        // $logger->log('foreach ($fiche_logs as $i => $log) { 2', 'debut');
        $liste_diffs = $log['liste_diffs'];
        foreach ($liste_diffs as $id => $diff) {
            $exploded_champ_nom = dm_explode('__', $diff['nom_champ']);
            if ($exploded_champ_nom[0] == 'intrinseque' || isset($champs[$diff['nom_champ']])) {
                $is_intrinseque = true;
            } else {
                $key = $log['fiche_id'] . '_' . $filtre['formulaire_id'] . '_' . $diff['nom_champ'] . '_' . $diff['id'] . '_' . $log['token_archive'];

                $logs[$key]['ts_ajout'] = dm_substr($log['ts_ajout'], 0, 19);
                $logs[$key]['prenom_usager'] = $log['prenom_usager'];
                $logs[$key]['nom_usager'] = $log['nom_usager'];
                $logs[$key]['code_fiche'] = $log['code_fiche'];
                $logs[$key]['fiche_id'] = $log['fiche_id'];
                if ($is_qte_form) {
                    $logs[$key]['equipement'] = $diff['equipement'];
                    $logs[$key]['equipement_id'] = $diff['equipement_id'];
                }
                $logs[$key]['valeur_avant'] = $diff['valeur_avant'];
                $logs[$key]['valeur_apres'] = $diff['valeur_apres'];
                $logs[$key]['valeur_avant_excel'] = $diff['valeur_avant_excel'];
                $logs[$key]['valeur_apres_excel'] = $diff['valeur_apres_excel'];
                $logs[$key]['token_archive'] = $log['token_archive'];
                $logs[$key]['titre_champ'] = $diff['titre_champ'];
                $logs[$key]['usager'] = $log['usager'];
                $logs[$key]['formulaire'] = $className::TITRE;
                $logs[$key]['is_intrinseque'] = 0;
            }
        }
//        // $logger->log('- DONE', 'fin');
        unset($fiche_logs[$i]);
    }
    // $logger->stopIteration();
    $logs = array_values($logs);

    $nb_par_page = 15;
    $nb_total = count($logs);
    $nb_pages = ceil($nb_total / $nb_par_page);
    $limit = $page == 1 ? 0 : ($page - 1) * $nb_par_page;

    $filtredCache->saveFilteredData($logs);
    // $logger->log('After $filtredCache->saveFilteredData($logs);');
}

// $logger->finDuLog();

$is_print = isset($_GET['print']);
if ($is_print) {
    $noHeader = 1;
    $isPDF = true;
    include('config/header.inc.php');
    ?>
    <script>
		window.onload = function () {
			window.print();
		}
    </script>
    <style>
        #logs td {
            text-align: left;
            padding: 0 5px;
        }

        #logs th {
            font-weight: bold;
            text-align: left;
        }
    </style>
    <?
}

if (!$is_print) {
    include('config/header.inc.php');
    ?>
    <script>
		$(document).ready(function () {

			$(document).find('select.sel_date').change(function () {

				if (this.value == -1) {
					$(this).parent().find('span.datepicker_parent').show();
				} else {
					$(this).parent().find('span.datepicker_parent').hide();
				}

				$(this).find('option.date_fixe').remove();

			});

			$(document).on('change', '#formulaire_id', function () {

				loadChampNoms(this.value, []);

			});

			$('#table_filtre').on('change', '#fiche_sous_type_id', function () {

				var url = 'index.php?section=fiche&module=index';
				$.get(url, {action: 'getFormulaireForSousType', fiche_sous_type_id: $(this).val()}, function (html) {

					$('#formulaire_id').html(html);
					$('#champ_nom').html('');
					DmMultiSelector.addNew('Paramètre(s)', 'champ_nom', '#champ_nom', []);

				});

			});

			if ($('#formulaire_id').val() !== '') {
				loadChampNoms($('#formulaire_id').val(), <?= json_encode($filtre['champ_nom']) ?>);
			} else {
				DmMultiSelector.addNew('Paramètre(s)', 'champ_nom', '#champ_nom', []);
			}

			$(document).on('click', '#exporter', function () {
				$("#dialog-options_export").dialog({
					height: 250,
					width: 650,
					modal: true,
					buttons: {
						"Exporter": function () {
							$.startObjectLoader($('body'), 'export_loader');

							fetch(`index.php?section=fiche&module=logs_detailles&export=1&format=${$('#format').val()}`, {
								method: 'GET'
							})
								.then(response => response.blob())
								.then(blob => {
									var url = window.URL.createObjectURL(blob);
									var a = document.createElement('a');
									a.href = url;
									a.download = `Historique détaillé.${$('#format').val()}`;
									document.body.appendChild(a); // we need to append the element to the dom -> otherwise it will not work in firefox
									a.click();
									a.remove();  //afterwards we remove the element again
									$.stopObjectLoader('export_loader');
								});

							$(this).dialog("close");

							return false;
						},
						"Annuler": function () {
							$(this).dialog("close");
						}
					}
				});
			});


		});

		function loadChampNoms(formulaire_id, champ_nom_value) {
			var url = 'index.php?section=fiche&module=index';
			$.get(url, {
				action: 'getChampsFromFormulaireId',
				formulaire_id: formulaire_id,
				champ_nom_value: champ_nom_value,
				with_qte: 1,
				ignorerChampsCustom: 1,
				sansOptionVide: 1,
				addExtraFiltre: 1,
                format: 'json'
			}, function (data) {
				DmMultiSelector.addNew('Paramètre(s)', 'champ_nom', '#champ_nom', (data ?? []));
			}, 'json');
		}
    </script>
    <style>
        #logs td {
            text-align: left;
            padding: 0 5px;
        }

        #logs th {
            font-weight: bold;
            text-align: left;
        }

        #table_filtre td {
            border: 0 solid;
            padding: 0 0;
            text-align: left !important;
            vertical-align: top;
        }

        #table_filtre {
            background-image: none !important;
            width: auto !important;
        }

        #table_filtre td {
            padding: 5px;
        }

        #s2id_champ_nom {
            position: relative;
            top: 1px;
            left: 12px;
        }

        .select2-choices {
            min-height: auto !important;
        }

        .select2-choices input {
            padding: 0 !important;
            margin: 0 0 0 5px !important;
        }
    </style>
    <?
}

if (!$is_print) {
    ?>

    <h1 class="ficheTitre">
        <?= txt('Historiques détaillés') ?>
        <?= ($filtre['filtre_global'] ?
            '<span style="color: blue; font-size: 14px; font-weight: bold; position: relative; top: -2px;">(' . txt(
                'Filtres avancés appliqués'
            ) . ')</span>
		<input type="button" class="filtrer" value="' . txt(
                'Désaffecter'
            ) . '" style="min-height: 22px!important; padding: 0 20px; position: relative; top: -4px;" onclick="window.location = \'index.php?section=fiche&module=logs_detailles&filtre_global=0\'" />'
            : '') ?>

        <div style="float: right;">
            <a class="btn" href="index.php?section=fiche&module=logs"><?php echo txt('Historiques généraux'); ?></a>
            <a class="btn actif" href="index.php?section=fiche&module=logs_detailles"><?php echo txt('Historiques détaillés'); ?></a>
            <a class="btn" href="index.php?section=fiche&module=historique_comparatif"><?php echo txt('Historiques comparatifs'); ?></a>
        </div>
    </h1>

    <?
    if (count($logs) > 300000) {
        ?>
        <div style="padding: 20px 20px 0 20px">
            <p style="color: red;">
                <?= txt('Attention ! Le résultat de la recherche ne peut être affiché car il comporte plus de 300 000 lignes de données (dépasse les capacités du navigateur). Il reste toutefois disponible en version Ms Excel.') ?>
            </p>
            <p style="color: red;">
                <?= txt('Cliquez sur l\'icône Ms Excel pour obtenir le rapport.') ?>
            </p>
        </div>
        <?
    }
    ?>

    <form action="index.php?section=fiche&module=logs_detailles" method="post" style="margin: 20px 0 0 0;"
          id="logs_form">
        <table id="table_filtre" style="width: ;">
            <tr>

                <td style="margin-left: 20px;">
                    <select id="formulaire_id" name="formulaire_id" style="max-width: 430px;">
                        <option value=""><?= txt('- Formulaire -') ?></option>
                        <?
                        Formulaires::getOptions(array(), $filtre['formulaire_id']);
                        ?>
                    </select>
                </td>

                <td style="margin-left: 20px;">
                    <select id="champ_nom" name="champ_nom[]" data-value="<?= dm_implode('||', $filtre['champ_nom']) ?>"
                            style="width: 450px;" multiple="multiple">
                    </select>
                </td>

                <td style="padding-left: 20px;">
                    <?= txt('Période entre ') ?><input type="text" id="date_debut" name="date_debut"
                                                       class="date hasDatepicker filtre"
                                                       value="<?= (isset($filtre['date_debut']) ? $filtre['date_debut'] : '') ?>">
                    <?= txt('et') ?> <input type="text" id="date_fin" name="date_fin" class="date hasDatepicker filtre"
                                            value="<?= (isset($filtre['date_fin']) ? $filtre['date_fin'] : '') ?>">
                </td>

                <td style="width: 100px;">
                    <input type="submit" class="filtrer" name="filtrer" value="<?= txt('Filtrer') ?>"
                           style="height: 20px!important;min-height: 20px!important; padding: 0 20px 1px 20px; position: relative; top: 2px;"/>
                </td>

                <td style="width: 20px;">
                    <?
                    if ($filtre['formulaire_id'] == 0) {
                        print '<img src="css/images/icons/dark/DM_export_CSV.png" style="opacity: 0.5; position: relative; top: 4px;" title="' . txt('Vous devez filtrer sur un formulaire avant de pouvoir faire l\'exportation.') . '" />';
                    } else {
                        ?>
                        <a href="index.php?section=exportation&module=ExportationLogsDetailles&formulaire_id=<?= $filtre['formulaire_id'] ?>" class="fancy_big" title="<?= txt("Exporter") ?>" style="position: relative; top: 4px;">
                            <img src="css/images/icons/dark/DM_export_CSV.png"/></a>
                        <?
                    }
                    ?>
                </td>

                <td style="width: 100px;">
                    <a href="index.php?section=filtre_avance&module=filtre_avance&type=_logs_detailles" class="fancy"
                       title="<?= txt('Filtres avancés') ?>">
                        <img src="css/images/icons/dark/filter.png"/></a>
                </td>
            </tr>
        </table>
    </form>
    <?
}

if (count($logs) <= 300000) {
    if ($is_print) {
        ?>
        <h6><?= txt('Historique de changements pour la période se situant entre ') . $filtre['date_debut'] . ' ' . txt(
                'et'
            ) . ' ' . $filtre['date_fin'] ?></h6>
        <?
    }
    ?>
    <div id="theWrapper">
        <table id="logs" class="display">
            <thead>
            <tr>
                <th width="125"><?= txt('Date / Heure') ?></th>
                <th width="125"><?= txt('Usager') ?></th>
                <th width="125"><?= txt('Fiche') ?></th>
                <th width="175"><?= txt('Formulaire') ?></th>
                <th width="125"><?= txt('Champ') ?></th>
                <?
                if (in_array($filtre['formulaire_id'], $forms_with_qte) || $is_intrinseque) {
                    ?>
                    <th width="125"><?= txt('Type d\'équipement') ?></th>
                    <?
                }
                ?>
                <th><?= txt('Valeur avant') ?></th>
                <th><?= txt('Valeur après') ?></th>
                <?
                if (!$is_print) {
                    ?>
                    <th width="22">&nbsp;</th>
                    <?
                }
                ?>
            </tr>
            </thead>
        </table>
    </div>
    <?php
    $lignes = [];
    foreach ($logs as $it => $row) {
        $lien_fiche = 'index.php?section=fiche&module=index&id=' . $row['fiche_id'];
        $ligne = [
            dm_substr($row['ts_ajout'], 0, 19),
            $row['prenom_usager'] . ' ' . $row['nom_usager'],
            '<a href="' . $lien_fiche . '" style="text-decoration: underline;">' . $row['code_fiche'] . '</a>',
            (isset($className) && dm_strlen($className) > 0 ? txt($className::TITRE) : ''),
            $row['titre_champ']
        ];
        if (in_array($filtre['formulaire_id'], $forms_with_qte) || $is_intrinseque) {
            $ligne[] = $row['equipement'];
        }
        $ligne[] = $row['valeur_avant'];
        $ligne[] = $row['valeur_apres'];
        if (!$is_print) {
            if (dm_strlen($className) > 0 && $row['is_intrinseque'] == 0) {
                $can_edit_form = havePermission('edition_projet') ? 1 : 0;
                $fiche_id = $row['fiche_id'];
                $ligne[] = '<a class="fancy" title="Consulter l\'historique des modifications" style="position: relative; top: 3px;" href="index.php?from_histo=1&can_edit_form=' . $can_edit_form . '&section=fiche&module=form_logs&fiche_id=' . $fiche_id . '&formulaire=' . $className . '&token_archive=' . $row['token_archive'] . '"> <img src="css/images/icons/dark/magnifying_glass.png"></a>';
            } elseif ($row['is_intrinseque'] == 1) {
                $ligne[] = '';
            }
        }
        $lignes[] = $ligne;
        unset($logs[$it]);
    }
    ?>
    <script>
        $(document).ready(function () {
			DmSimpleDatatable.addNew('#logs', <?= json_encode($lignes) ?>);
        });
    </script>
    <iframe id="iframe" name="iframe" style="display: none; width: 100%;"></iframe>
    <br style="clear: both;"/>
    <?
}
include('config/footer.inc.php');
?>
<div id="dialog-options_export" title="<?= txt('Export') ?>" style="display: none; margin: 5px 15px;">
    <p class="message" style="margin-top: 45px;">
        <?= txt('Veuillez sélectionner le format du fichier à exporter:') ?>&nbsp;&nbsp;
        <select id="format" name="format" style="margin-right: 20px; min-width: 120px;">
            <?
            foreach ($AllFormatsExports as $key => $format) {
                ?>
                <option value="<?= $key ?>"><?= $format ?></option>
                <?
            }
            ?>
        </select>
    </p>
</div>
<?
$id = intval($_GET['id']);
$discpline_id = intval($_GET['discipline_id']);

if(isset($_POST) && count($_POST) > 0)
{
	$db->autocommit(false);
	
	$updateStatement = array();
	$insertStatementCol = array();
	$insertStatementVal = array();
	$data = array();
	
	foreach($_POST as $colomnName => $value)
	{
		$dataType = '';
		if(isset($validColumns['doc']['fiches'][$colomnName]))
		{
			$dataType = $validColumns['doc']['fiches'][$colomnName];
		}
		else
		{
			continue;
		}
        
        if($colomnName == 'fiche_type_id' || is_array($value))
        {
            continue;
        }
        
		$addValue = true;
        if($colomnName == 'code')
		{
			if(dm_strlen($value) == 0)
			{
				setErreur(txt('Le code est obligatoire.'));
				$addValue = false;
			}
            else
            {
                $sql = "select count(*) as nb from fiches where code = ? and id <> ?";
                $row = $db->getRow($sql, array($value, $id));
                
                if($row['nb'] > 0)
                {
                    setErreur(txt('Ce code d\'appareil existe déjà.'));
                    $addValue = false;
                }
            }
            
            $value = strtoupper($value);
		}
		
        if($colomnName == 'nomenclature_id')
		{
			if(intval($value) == 0)
			{
				setErreur(txt('La nomenclature est obligatoire.'));
				$addValue = false;
			}
		}
		
        if($colomnName == 'batiment_id')
		{
			if(intval($value) == 0)
			{
				setErreur(txt('Le batiment est obligatoire.'));
				$addValue = false;
			}
		}
		
        if($addValue)
        {
            if(dm_strlen($value) > 0)
            {
                $updateStatement[] = '`'.$colomnName.'` = ?';
                $insertStatementCol[] = '`'.$colomnName.'`';
                $insertStatementVal[] = '?';

                if($dataType == 'int')
                {
                    $data[] = intval($value);
                }
                else if($dataType == 'float')
                {
                    $data[] = floatval($value);
                }
                else if($dataType == 'date')
                {
                    $data[] = strval($value);
                }
                else if($dataType == 'varchar')
                {
                    $data[] = strval($value);
                }
                else if($dataType == 'timestamp')
                {
                    $data[] = strval($value);
                }
            }
            else
            {
                $updateStatement[] = '`'.$colomnName.'` = null';
                $insertStatementCol[] = '`'.$colomnName.'`';
                $insertStatementVal[] = 'null';
            }
        }
	}
    
    if(nbErreurs() == 0)
	{
        if($id > 0)
		{
			$data[] = $id;
			$sql = "UPDATE `fiches` SET  ".dm_implode(", ", $updateStatement)." WHERE id = ?";
			$db->query($sql, $data);
		}
		else
		{
			$sql = "INSERT INTO `fiches` (".dm_implode(", ", $insertStatementCol).") VALUES (".dm_implode(", ", $insertStatementVal).")";
			$db->query($sql, $data);
			$id = $db->lastInsertId();
		}

        $sql = "DELETE FROM liens_internes WHERE fiche_1_id = ? OR fiche_2_id = ?";
        $db->query($sql, array($id, $id));

        if(isset($_POST['fiche_id']) && count($_POST['fiche_id']) > 0)
        {
            foreach($_POST['fiche_id'] as $i => $fiche_id)
            {
                $sql = "INSERT INTO liens_internes (fiche_1_id, fiche_2_id) VALUES (?, ?)";
                $db->query($sql, array($id, intval($fiche_id)));
            }
        }
        
        if($id > 0 && $discpline_id > 0)
        {
            $sql = "delete from fiches_disciplines where fiche_id = ? and discipline_id = ?";
            $db->query($sql, array($id, $discpline_id));
            
            $sql = "insert into fiches_disciplines (fiche_id, discipline_id) values (?, ?)";
            $db->query($sql, array($id, $discpline_id));
            
            $sql = "DELETE FROM champs_ponctuels_saisie WHERE fiche_id = ? and discipline_id = ?";
            $db->query($sql, array($id, $discpline_id));

            if(isset($_POST['ordre']))
            {
                foreach($_POST['ordre'] as $ordre => $champ_ponctuel_id)
                {
                    $type_saisie = $_POST['type_saisie'][$ordre];
                    $val_titre_bloc = $_POST['titre_bloc'][$ordre];
                    $val_col_1_arr = $_POST['champ_ponctuel_col1'][$ordre];
                    $val_col_2_arr = $_POST['champ_ponctuel_col2'][$ordre];
                    $val_col_3_arr = $_POST['champ_ponctuel_col3'][$ordre];
                    $val_col_4_arr = $_POST['champ_ponctuel_col4'][$ordre];
                    
                    if(!is_array($val_col_1_arr))
                    {
                        $val_col_1_arr = array($val_col_1_arr);
                        
                        if(isset($val_col_2_arr) && dm_strlen($val_col_2_arr) > 0)
                        {
                            $val_col_2_arr = array($val_col_2_arr);
                        }
                        
                        if(isset($val_col_3_arr) && dm_strlen($val_col_3_arr) > 0)
                        {
                            $val_col_3_arr = array($val_col_3_arr);
                        }
                        
                        if(isset($val_col_4_arr) && dm_strlen($val_col_4_arr) > 0)
                        {
                            $val_col_4_arr = array($val_col_4_arr);
                        }
                    }
                    
                    foreach($val_col_1_arr as $iteration => $val_col_1)
                    {
                        $insertStatementCol = array();
                        $insertStatementVal = array();
                        $data = array();
                        
                        $insertStatementCol[] = '`champ_ponctuel_id`';
                        $insertStatementVal[] = '?';
                        $data[] = $champ_ponctuel_id;
                        
                        $insertStatementCol[] = '`fiche_id`';
                        $insertStatementVal[] = '?';
                        $data[] = $id;
                        
                        $insertStatementCol[] = '`discipline_id`';
                        $insertStatementVal[] = '?';
                        $data[] = $discpline_id;
                        
                        $insertStatementCol[] = '`ordre`';
                        $insertStatementVal[] = '?';
                        $data[] = $ordre;
                        
                        $insertStatementCol[] = '`iteration`';
                        $insertStatementVal[] = '?';
                        $data[] = $iteration;
                        
                        $insertStatementCol[] = '`val_col_1`';
                        $insertStatementVal[] = '?';
                        $data[] = $val_col_1;
                        
                        $insertStatementCol[] = '`val_titre_bloc`';
                        $insertStatementVal[] = '?';
                        $data[] = $val_titre_bloc;
                        
                        if(isset($val_col_2_arr[$iteration]) && dm_strlen($val_col_2_arr[$iteration]) > 0)
                        {
                            $insertStatementCol[] = '`val_col_2`';
                            $insertStatementVal[] = '?';
                            $data[] = $val_col_2_arr[$iteration];
                        }
                        
                        if(isset($val_col_3_arr[$iteration]) && dm_strlen($val_col_3_arr[$iteration]) > 0)
                        {
                            $insertStatementCol[] = '`val_col_3`';
                            $insertStatementVal[] = '?';
                            $data[] = $val_col_3_arr[$iteration];
                        }
                        
                        if(isset($val_col_4_arr[$iteration]) && dm_strlen($val_col_4_arr[$iteration]) > 0)
                        {
                            $insertStatementCol[] = '`val_col_4`';
                            $insertStatementVal[] = '?';
                            $data[] = $val_col_4_arr[$iteration];
                        }
                        
                        $sql = "INSERT INTO `champs_ponctuels_saisie` (".dm_implode(", ", $insertStatementCol).") VALUES (".dm_implode(", ", $insertStatementVal).")";
                        $db->query($sql, $data);
                    }
                }
            }
        }
        
        $db->commit();
	
        header('Location: index.php?section=fiche&module=index');
        exit;
    }
}

if($discpline_id > 0)
{
    $sql = "select code from disciplines where id = ?";
    $rowDisc = $db->getRow($sql, array($discpline_id));
    $codeDiscipline = $rowDisc['code'];
}
else
{
    $codes = array(-1 => 'GEN', -2 => 'DOC');
    $codeDiscipline = $codes[$discpline_id];
}

$liens_internes = array();
if($id == 0 || (isset($_POST) && count($_POST) > 0))
{
	$fiche = array();
	foreach($validColumns['doc']['fiches'] as $columnName => $type)
	{
        if(isset($_POST[$columnName]))
        {
            $fiche[$columnName] = $_POST[$columnName];
        }
        else
        {
            $fiche[$columnName] = '';
        }
	}
}
else
{
	$sql = "SELECT fiches.*, fiche_type_id
			FROM fiches
            JOIN fiches_sous_types ON fiche_sous_type_id = fiches_sous_types.id
            JOIN fiches_types ON fiche_type_id = fiches_types.id
			where fiches.id = ?";
	$fiche = $db->getRow($sql, array($id));
	
	if(isset($_GET['mode']) && $_GET['mode'] == 'cloneFrom')
	{
		$id = 0;
		$fiche['code'] = '';
		$fiche['batiment_id'] = 0;
		$fiche['description'] = '';
	}
	else
	{
		$sql = "SELECT nomenclature_id, fiches.code as fiche, fiche_2_id as fiche_id
				FROM  `liens_internes` 
				JOIN fiches ON fiches.id = fiche_2_id
				WHERE fiche_1_id = ?";
		$liens_internes1 = $db->getAll($sql, array($id));

		$sql = "SELECT nomenclature_id, fiches.code as fiche, fiche_1_id as fiche_id
				FROM  `liens_internes` 
				JOIN fiches ON fiches.id = fiche_1_id
				WHERE fiche_2_id = ?";
		$liens_internes2 = $db->getAll($sql, array($id));

		$liens_internes = array_merge($liens_internes1, $liens_internes2);
	}
}

if($fiche['modele_id'] > 0)
{
	$sql = "SELECT marque_id
			FROM modeles
			where id = ?";
	$marque_id = $db->getRow($sql, array($fiche['modele_id']));
	$fiche['marque_id'] = $marque_id['marque_id'];
}

$sql = "SELECT *
		FROM unites
		WHERE ts_valide is not null
		ORDER BY nom";
$unites = $db->getAll($sql);

$sql = "SELECT *
		FROM nomenclatures
		WHERE ts_valide is not null";
$resNoms = $db->getAll($sql);

$nomenclatures = array();
foreach($resNoms as $i => $row)
{
	$nomenclatures[$row['id']] = $row;
}
$sql = "SELECT fiches.id, fiches.code, nomenclature_id, nomenclatures.code as nomenclature_code, nomenclatures.nom as nomenclature_nom, batiment_id
		FROM fiches
		JOIN nomenclatures ON nomenclatures.id = nomenclature_id
		WHERE fiches.id <> ?
		ORDER BY fiches.code";
$resInsts = $db->getAll($sql, array($id));

$page = 'wizard';
include('config/header.inc.php');
?>
<script src="js/wizard.js"></script>
<script>
var codes = new Array();
<?php
foreach($resInsts as $i => $row)
{
	print "codes[".$i."] = new Array();"."\n";
	print "codes[".$i."]['id'] = '".$row['id']."'"."\n";
	print "codes[".$i."]['code'] = '".$row['code']."'"."\n";
	print "codes[".$i."]['nomenclature_id'] = '".$row['nomenclature_id']."'"."\n";
	print "codes[".$i."]['nomenclature_code'] = '".$row['nomenclature_code']."'"."\n";
	print "codes[".$i."]['nomenclature_nom'] = '".dm_addslashes($row['nomenclature_nom'])."'"."\n";
	print "codes[".$i."]['batiment_id'] = '".$row['batiment_id']."'"."\n";
}
?>

var nomenclatures = new Array();
<?php
foreach($nomenclatures as $id_nom => $row)
{
	print "nomenclatures['".$id_nom."'] = '".dm_addslashes($row['code'].' - '.$row['nom'])."'"."\n";
}
?>

codesLength = codes.length;
term = '';
timout = setTimeout(function(){}, 1);

$(document).ready(function(){
	
	$(document).on('change, keyup', '#codeInspection', function(){
		term = this.value;
		clearTimeout(timout);
		timout = setTimeout(function(){
			$('#listeFiches').html('');
			for (var i = 0; i < codesLength; i++)
			{
				id = codes[i]['id'];
				code = codes[i]['code'];
				nomenclature_code = codes[i]['nomenclature_code'];
				nomenclature_nom = codes[i]['nomenclature_nom'];
				nomenclature_id = codes[i]['nomenclature_id'];
				batiment_id = codes[i]['batiment_id'];
				
				if(code.indexOf(term.toUpperCase()) != -1 && batiment_id == $('#batiment_id').val())
				{
					$('#listeFiches').append('<option id="opt_inst_' + id + '" value="' + id + '" data-nomenclature_id="' + nomenclature_id + '">' + code + ' (' + nomenclature_code + ' - ' + nomenclature_nom + ')' + '</option>');
				}
			}
		},1);
	});
	
	$(document).on('click', '#ajout_lien_interne', function(){
		$('#codeInspection').keyup();
		$('#listeFiches').scrollTop(0);
		$( "#dialog-confirm-lien_interne" ).dialog({
			resizable: false,
			height:350,
			width:500,
			modal: true,
			buttons: {
				"Ajouter": function() {
					idInst = $('#listeFiches').val();
					if(!$('#lien_interne_' + idInst).length)
					{
						codeInst = $('#opt_inst_' + idInst).html();
                        nomenclature_id = $('#opt_inst_' + idInst).data('nomenclature_id');
						nomenclatureInst = nomenclatures[nomenclature_id];
						$('#liens_internes').append('<tr id="lien_interne_' + idInst + '"><td align="right" style="padding-left: 20px;"><a href="javascript: void(0);" class="del_lien_interne" data-fiche_id="' + idInst + '" style="position: relative; top: 6px;"><img src="css/images/icons/dark/trashcan.png" /></a>&nbsp;&nbsp;<input type="hidden" name="fiche_id[]" value="' + idInst + '" />' + nomenclatureInst + ': ' + codeInst + '</td></tr>');
					}
					
					$( this ).dialog( "close" );
				},
				"Annuler": function() {
					$( this ).dialog( "close" );
				}
			}
		});
	});
	
	$(document).on('click', 'a.del_lien_interne', function(){
		$('#lien_interne_' + $(this).data('fiche_id')).remove();
	});
	
	$(document).find("a.add_element").fancybox({
		'overlayShow'		:	false,
		'type'				:	'iframe',
		'modal'				:	true,
		'centerOnScroll'	:	true,
		'onComplete'		:	function(){
			$('#fancybox-frame').load(function() { // wait for frame to load and then gets it's height
				fancybox_resize();
			});
		},
		'onClosed'			:	function(){
			$('html').css({ overflow: 'auto' });
		}
	});
    
    $('#champ_ponctuel').change(function(){
        
         addChampPonctuel($(this).val());
        
    });
    
    $("#champ_ponctuel_add").fancybox({
		'overlayShow'		:	false,
		'type'				:	'iframe',
		'modal'				:	true,
		'centerOnScroll'	:	true,
		'onComplete'		:	function(){
			$('#fancybox-frame').load(function() { // wait for frame to load and then gets it's height
				fancybox_resize();
			});
		},
		'onClosed'			:	function(){
			$('html').css({ overflow: 'auto' });
		}
	});
    
    $('#champs_ponctuels_saisies').on('mouseover', 'section.champs_ponctuels', function(){
        $(this).find('span.delSortBtns').css( { opacity: '1' } );
    });
    
    $('#champs_ponctuels_saisies').on('mouseout', 'section.champs_ponctuels', function(){
        $(this).find('span.delSortBtns').css( { opacity: '0' } );
    });
    
    $('#champs_ponctuels_saisies').on('click', 'a.delete', function(){
        $(this).parent().parent().remove();
    });
    
    $('#champs_ponctuels_saisies').sortable({
        handle: 'a.sort',
        items: 'section.champs_ponctuels',
        axis: 'y'
    });
	
	$('#breadcrumbcontent').show();
    /*
    $('#wizard').on('mouseover', 'table.formFieldsTable tr', function(){
        $(this).find('span.delSortBtns').css( { opacity: '1' } );
    });
    
    $('#wizard').on('mouseout', 'table.formFieldsTable tr', function(){
        $(this).find('span.delSortBtns').css( { opacity: '0' } );
    });
    */
});

function addChampPonctuel(champ_ponctuel_id)
{
     url = 'index.php?section=fiche&module=wizard';
    $.get(url, { action: 'getChampPonctuel', champ_ponctuel_id: champ_ponctuel_id }, function(data){
        $('#champs_ponctuels_saisies').append(data);
        $('#champ_ponctuel').val('');
    });
}

function addOption(idElement, idOption, nomOption)
{
	$('#' + idElement).append('<option value="' + idOption + '">' + nomOption + '</option>');
	$('#' + idElement).val(idOption);
}
</script>
<style>
    #analogique_table {
        width: 90%;
    }    
    #analogique_table select.unite {
        width: 100%;
    }    
    label.actif {
        width: 75px!important;
        text-align: right; 
        position: relative; 
        top: -6px;
    }
    td {
        border: solid 0px white!important;
        text-align: left!important;
        padding: 0px!important;
    }
    #discrete_table td input[type=text], #analogique_table td input[type=text] {
        width: 75px!important;
    }
    select {
        color: black!important;
        min-width: 225px;
    }
    .label, label {
        font-size: 13px;
        width: 225px!important;
    }
    select option {
        border: solid 0px;
        color: black!important;
    }
    input {
        font-size: 13px;
        color: black!important;
    }
    div.label{
        width: 400px!important;
    }
    #discrete_table td, #analogique_table td{
        padding: 3px!important;
    }
    table thead tr, table tfoot tr, table th {
        background-image: none;
    }

    #champs_ponctuels_saisies td input {
        width: 150px!important;
    }

    .champs_ponctuels {
        margin-top: 5px;
    }
</style>

<div id="dialog-confirm-lien_interne" title="<?php print txt('Veuillez rechercher la fiche à lier.'); ?>" style="display: none;">
	<fieldset>
		
		<input type="text" id="codeInspection" /><br />
		<select id="listeFiches" multiple="multiple" style="min-height: 190px!important; width: 425px;">
		</select>
		
	</fieldset>
</div>

<div id="content">
	<ul class="breadcrumb" data-connect="breadcrumbcontent">
	
    <? if($codeDiscipline == 'GEN') { ?>
		<li><a href="#"><?php print txt('Générales'); ?></a></li>
		<li><a href="#"><?php print txt('Informations complémentaires'); ?></a></li>
    <? } ?>
        
    <? if($codeDiscipline == 'INST') { ?>
        <li><a href="#"><?php print txt('Propriétés analogiques'); ?></a></li>
        <li><a href="#"><?php print txt('Propriétés discrètes'); ?></a></li>
        <li><a href="#"><?php print txt('Raccordement de contrôle'); ?></a></li>
    <? } ?>
        
    <? if($codeDiscipline == 'ELE') { ?>
        <li><a href="#"><?php print txt('Source d\'alimentation'); ?></a></li>
        <li><a href="#"><?php print txt('Protections'); ?></a></li>
    <? } ?>
        
    <? if($codeDiscipline == 'AUTO') { ?>
        <li><a href="#"><?php print txt('Raccordement de contrôle'); ?></a></li>
    <? } ?>
        
    <? if(!in_array($codeDiscipline, array('GEN', 'DOC'))) { ?>
        <li><a href="#"><?php print txt('Autres'); ?></a></li>
    <? } ?>
		
	</ul>
	<form action="index.php?section=fiche&module=wizard&id=<?php print $id; ?>&discipline_id=<?=$discpline_id?>" method="post" id="formWiz">
        <?php erreurs(); ?>
		<input type="hidden" id="idfiche" value="<?php print $id; ?>" />
		<div id="breadcrumbcontent" style="display: none;">
<?
            if($codeDiscipline == 'GEN')
            { 
				include_once('wizard_prop_generale.php');
				include_once('wizard_info_complementaire.php');
            }
            
            if($codeDiscipline == 'INST')
            {
				include_once('wizard_prop_analogique.php');
				include_once('wizard_prop_discrete.php');
				include_once('wizard_prop_raccordement.php');
                $labelPrecedantAutre = txt('Raccordement de contrôle');
            }
            
            if($codeDiscipline == 'ELE')
            {
				include_once('wizard_prop_source_alimentation.php');
				include_once('wizard_prop_protections.php');
                $labelPrecedantAutre = txt('Protections');
            }
            
            if($codeDiscipline == 'AUTO')
            {
				include_once('wizard_prop_raccordement.php');
                $labelPrecedantAutre = txt('Raccordement de contrôle');
            }
            
            if(!in_array($codeDiscipline, array('GEN', 'DOC')))
            {
                include_once('wizard_autre.php');
            }
?>
            
		</div>
	</form>
    <br style="clear: both;" />
</div>
<?php
include('config/footer.inc.php');
?>
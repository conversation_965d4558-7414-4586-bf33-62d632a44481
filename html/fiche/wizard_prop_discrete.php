<div>
	<h2 class="blocsFicheTitre"><?php print txt('Propriétés discrètes'); ?></h2>
	<fieldset>
		<section>
			<span class="label"><?php print txt('Unité de consigne'); ?></span>
			<div>
				<select id="unite_consigne" name="unite_consigne">
					<option value=""> </option>
<?php						
					foreach($unites as $i => $row)
					{
						$sel = $row['id'] == $instrument['unite_consigne'] ? ' selected="selected"' : '';
?>
						<option value="<?php print $row['id']; ?>"<?php print $sel; ?>><?php print $row['nom']; ?></option>
<?php
					}
?>
				</select>
			</div>
		</section>
		
		<section>
			<table id="discrete_table">

				<tr><td style="font-weight: bold; height: 30px;" colspan="7" align="left">
					&nbsp;
				</td></tr>
			
				<tr><td>
					&nbsp;
				</td><td style="text-align: center!important;">
					1
				</td><td style="text-align: center!important;">
					2
				</td><td style="text-align: center!important;">
					3
				</td><td style="text-align: center!important;">
					4
				</td><td style="text-align: center!important;">
					5
				</td><td style="text-align: center!important;">
					6
				</td></tr>
				
				<tr><td>
					<?php print txt('Consigne croissante ou ON'); ?>
				</td><td style="text-align: center!important;">
					<input type="text" name="consigne_croissante_1" value="<?php print ($instrument['consigne_croissante_1']); ?>" />
				</td><td style="text-align: center!important;">
					<input type="text" name="consigne_croissante_2" value="<?php print ($instrument['consigne_croissante_2']); ?>" />
				</td><td style="text-align: center!important;">
					<input type="text" name="consigne_croissante_3" value="<?php print ($instrument['consigne_croissante_3']); ?>" />
				</td><td style="text-align: center!important;">
					<input type="text" name="consigne_croissante_4" value="<?php print ($instrument['consigne_croissante_4']); ?>" />
				</td><td style="text-align: center!important;">
					<input type="text" name="consigne_croissante_5" value="<?php print ($instrument['consigne_croissante_5']); ?>" />
				</td><td style="text-align: center!important;">
					<input type="text" name="consigne_croissante_6" value="<?php print ($instrument['consigne_croissante_6']); ?>" />
				</td></tr>
				
				<tr><td>
					<?php print txt('Consigne décroissante ou OFF'); ?>
				</td><td style="text-align: center!important;">
					<input type="text" name="consigne_decroissante_1" value="<?php print ($instrument['consigne_decroissante_1']); ?>" />
				</td><td style="text-align: center!important;">
					<input type="text" name="consigne_decroissante_2" value="<?php print ($instrument['consigne_decroissante_2']); ?>" />
				</td><td style="text-align: center!important;">
					<input type="text" name="consigne_decroissante_3" value="<?php print ($instrument['consigne_decroissante_3']); ?>" />
				</td><td style="text-align: center!important;">
					<input type="text" name="consigne_decroissante_4" value="<?php print ($instrument['consigne_decroissante_4']); ?>" />
				</td><td style="text-align: center!important;">
					<input type="text" name="consigne_decroissante_5" value="<?php print ($instrument['consigne_decroissante_5']); ?>" />
				</td><td style="text-align: center!important;">
					<input type="text" name="consigne_decroissante_6" value="<?php print ($instrument['consigne_decroissante_6']); ?>" />
				</td></tr>
				
				<tr><td>
					<?php print txt('Contact'); ?>
				</td><td style="text-align: center!important;">
					<select name="contact_1" style="width: 75px!important; min-width: 75px!important;">
						<option value=""> </option>
						<?php $sel = $instrument['contact_1'] == 'N.O' ? ' selected="selected"' : ''; ?>
						<option value="N.O"<?php print $sel; ?>>N.O</option>
						<?php $sel = $instrument['contact_1'] == 'N.F' ? ' selected="selected"' : ''; ?>
						<option value="N.F"<?php print $sel; ?>>N.F</option>
					</select>
				</td><td style="text-align: center!important;">
					<select name="contact_2" style="width: 75px!important; min-width: 75px!important;">
						<option value=""> </option>
						<?php $sel = $instrument['contact_2'] == 'N.O' ? ' selected="selected"' : ''; ?>
						<option value="N.O"<?php print $sel; ?>>N.O</option>
						<?php $sel = $instrument['contact_2'] == 'N.F' ? ' selected="selected"' : ''; ?>
						<option value="N.F"<?php print $sel; ?>>N.F</option>
					</select>
				</td><td style="text-align: center!important;">
					<select name="contact_3" style="width: 75px!important; min-width: 75px!important;">
						<option value=""> </option>
						<?php $sel = $instrument['contact_3'] == 'N.O' ? ' selected="selected"' : ''; ?>
						<option value="N.O"<?php print $sel; ?>>N.O</option>
						<?php $sel = $instrument['contact_3'] == 'N.F' ? ' selected="selected"' : ''; ?>
						<option value="N.F"<?php print $sel; ?>>N.F</option>
					</select>
				</td><td style="text-align: center!important;">
					<select name="contact_4" style="width: 75px!important; min-width: 75px!important;">
						<option value=""> </option>
						<?php $sel = $instrument['contact_4'] == 'N.O' ? ' selected="selected"' : ''; ?>
						<option value="N.O"<?php print $sel; ?>>N.O</option>
						<?php $sel = $instrument['contact_4'] == 'N.F' ? ' selected="selected"' : ''; ?>
						<option value="N.F"<?php print $sel; ?>>N.F</option>
					</select>
				</td><td style="text-align: center!important;">
					<select name="contact_5" style="width: 75px!important; min-width: 75px!important;">
						<option value=""> </option>
						<?php $sel = $instrument['contact_5'] == 'N.O' ? ' selected="selected"' : ''; ?>
						<option value="N.O"<?php print $sel; ?>>N.O</option>
						<?php $sel = $instrument['contact_5'] == 'N.F' ? ' selected="selected"' : ''; ?>
						<option value="N.F"<?php print $sel; ?>>N.F</option>
					</select>
				</td><td style="text-align: center!important;">
					<select name="contact_6" style="width: 75px!important; min-width: 75px!important;">
						<option value=""> </option>
						<?php $sel = $instrument['contact_6'] == 'N.O' ? ' selected="selected"' : ''; ?>
						<option value="N.O"<?php print $sel; ?>>N.O</option>
						<?php $sel = $instrument['contact_6'] == 'N.F' ? ' selected="selected"' : ''; ?>
						<option value="N.F"<?php print $sel; ?>>N.F</option>
					</select>
				</td></tr>
				
			</table>
		</section>
	</fieldset>
	<div class="fr" style="margin-top: 25px;">
		<a href="javascript: ;" class="btn small" onclick="$.confirm('<?php print txt('Les changements ne seront pas conservés. Êtes-vous certain de vouloir annuler?'); ?>', function(){ window.location = 'index.php?section=fiche&module=index'; });"><?php print txt('Annuler'); ?></a>
		<?php if($id > 0) { ?><a href="javascript: ;" class="btn small" onclick="$('#formWiz').submit();return false;"><?php print txt('Sauvegarder'); ?></a><?php } ?>
		&nbsp;&nbsp;&nbsp;&nbsp;
		<a class="btn small prev icon i_arrow_left"><?php print txt('Propriétés analogiques'); ?></a>
		<a class="btn small next icon i_arrow_right"><?php print txt('Raccordement de contrôle'); ?></a>
	</div>
</div>
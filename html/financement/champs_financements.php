<?
$champs_financements =
[
	[
		[
			'titre' => "Information du projet",
			'ensemble' => "projet",
			'champs' => [
				[
					'nom' => 'numero_projet',
					'label' => txt('Numéro du projet', false),
					'obligatoire' => 1
				],
				[
					'nom' => 'description_projet',
					'label' => txt('Description du projet', false),
					//'obligatoire' => 1
				],
				[
					'nom' => 'commentaires',
					'label' => txt('Commentaires du projet', false),
					//'obligatoire' => 1
				],
            	[
					'nom' => 'suivi_administratif',
					'label' => txt('Suivi administratif', false),
		            'format' => 'texteMultiligne'
					//'obligatoire' => 1
				],
			]
		],
		[
			'titre' => "Responsables du dossier",
			'ensemble' => "projet",
			'champs' => [
				[
					'nom' => 'intervenant_1',
					'label' => txt('Intervenant 1', false)
				],
				[
					'nom' => 'intervenant_2',
					'label' => txt('Intervenant 2', false)
				],
				[
					'nom' => 'intervenant_3',
					'label' => txt('Intervenant 3', false)
				],
				[
					'nom' => 'intervenant_4',
					'label' => txt('Intervenant 4', false)
				],
				[
					'nom' => 'priorite_financement',
					'label' => txt('Priorité', false)
				],
				[
					'nom' => 'bloquant_type',
					'label' => txt('Bloquant', false),
				],
			]
		],
		[
			'titre' => "Information du groupe",
			'ensemble' => "groupe",
			'champs' => [
				[
					'nom' => 'numero',
					'label' => txt('Numéro du groupe', false),
					//'obligatoire' => 1
				],
				[
					'nom' => 'description',
					'label' => txt('Description du groupe', false),
					//'obligatoire' => 1
				],
				[
					'nom' => 'code_equipement',
					'label' => txt('Code de l\'équipement', false),
					//'obligatoire' => 1
				],
				[
					'nom' => 'description_equipement',
					'label' => txt('Description de l\'équipement', false),
					//'obligatoire' => 1
				],
				[
					'nom' => 'quantite_a_commander',
					'label' => txt('Quantité soumise au MSSS', false),
					//'obligatoire' => 1
				],
				[
					'nom' => 'quantite_disponible',
					'label' => txt('Quantité reçue', false),
					//'obligatoire' => 1
				],
			]
		],
	],
	[
		[
			'titre' => "Gestion des sources de financement",
			'ensemble' => "groupe",
			'champs' => [
				[
					'nom' => 'projet_drv',
					'label' => txt('# Projet DRF', false)
				],
				[
					'nom' => 'avant_taxes',
					'label' => txt('Montant avant taxes', false)
				],
				[
					'nom' => 'code_taxe',
					'label' => txt('Code de taxe', false)
				],
            	[
					'nom' => 'cout_taxe_net',
					'label' => txt('Coût après taxes nettes', false)
				],
            	[
					'nom' => 'cout_projete',
					'label' => txt('Doût projeté', false)
				],
			]
		],
		[
			'titre' => "Dossier de financement",
			'ensemble' => "groupe",
			'champs' => [
				[
					'nom' => 'livraison_estime',
					'label' => txt('Délais de livraison estimé(jour)', false)
				],
				[
					'nom' => 'ao',
					'label' => txt('#AO', false)
				],
				[
					'nom' => 'mode_financement',
					'label' => txt('Mode de financement', false)
				],
				[
					'nom' => 'portee_ao',
					'label' => txt('Portée AO (CHU, GAC)', false)
				],
				[
					'nom' => 'num_requisition',
					'label' => txt('# Réquisition', false)
				],
				[
					'nom' => 'bon_commande',
					'label' => txt('# Bon de commande', false)
				],
				[
					'nom' => 'fournisseur',
					'label' => txt('Fournisseur', false)
				],
				[
					'nom' => 'manufacturier',
					'label' => txt('Manufacturier', false)
				],
				[
					'nom' => 'modele',
					'label' => txt('Modèle', false)
				],
			]
		],
	],
	[
		[
			'titre' => "Logistique de livraison",
			'ensemble' => "groupe",
			'champs' => [
				[
					'nom' => 'site_livraison',
					'label' => txt('Site de livraison', false)
				],
				[
					'nom' => 'local_transitoire',
					'label' => txt('Local transitoire', false)
				],
				[
					'nom' => 'statut_livraison',
					'label' => txt('Statut de livraison', false)
				],
				[
					'nom' => 'deficience_livraison',
					'label' => txt('Déficience livraison', false)
				],
				[
					'nom' => 'commentaire_logistique',
					'label' => txt('Commentaire logistique', false)
				],
			]
		],
		[
			'titre' => "Références interne",
			'ensemble' => "groupe",
			'champs' => [
				[
					'nom' => 'ref_nego_existante',
					'label' => txt('#Ref négociation existante', false)
				],
				[
					'nom' => 'debut_contrat',
					'label' => txt('Début du contrat', false)
				],
				[
					'nom' => 'expiration_contrat',
					'label' => txt('Expiration du contrat', false)
				],
				[
					'nom' => 'ref_achat',
					'label' => txt('Réflérence d\'achat', false)
				],
			]
		],
		[
			'titre' => "Informations connexes",
			'ensemble' => "globale",
			'champs' => [
				[
					'nom' => 'etat_avancement',
					'label' => txt('État d\'avancement (nom et échéance du jalon en cours)', false)
				],
				[
					'nom' => 'liste_jalon',
					'label' => txt('Liste complète des jalons', false)
				],
				[
					'nom' => 'quantites_complementaires',
					'label' => txt('Qté complémentaires: Résiduels (com. et dist.) / Tot. dist.', false)
				],
				[
					'nom' => 'details_groupes',
					'label' => txt('Détails des groupes (locaux, quantitées com. et dist.)', false)
				],
			]
		]
	],
];
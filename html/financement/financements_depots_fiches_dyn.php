<?
ini_set('max_execution_time', 30000);
$projet = new FinancementsProjets(intval($_GET['projet_id']));
$adminUni = new FinancementsProjetsAdministrationsUnifiees((int)$_GET['admin_modele_id']);
$admin = FinancementsProjetsAdministrations::getAdminForProjet();
$infosLocauxEquipements = $projet->getInfoLocauxEquipements();
if (count($infosLocauxEquipements) == 0) {
    exit;
}

//if ($isDev) {
//	$infosLocauxEquipements_tmp = $infosLocauxEquipements;
//	for ($i = 0; $i <= 75; $i++) {
//		$infosLocauxEquipements = array_merge($infosLocauxEquipements, $infosLocauxEquipements_tmp);
//	}
//}

$batiments_niveaux = BatimentsNiveaux::getListe();
$batiments_niveaux_all = BatimentsNiveaux::getAllLevelsData(true);

$structIsSticky = false;
$listeChampsSticky = [];
$listeChamps = [];
$listeRegroupements = [];
$regroupementSticky = ['titre' => '', 'nombreDeColonnes' => 0];
foreach (FinancementsProjetsAdministrationsUnifiees::getColonnesMetas(true) as $blocsNiv2) {
	foreach ($blocsNiv2 as $bloc) {
		if ($bloc['ensemble'] == 'fiche') {
            $regroupement = ['titre' => $bloc['titre'], 'nombreDeColonnes' => 0, 'color' => ($bloc['color'] ?? 'light-grey')];
			foreach ($bloc['colonnes'] as $champ) {
                if ($adminUni->isColonneVisible($champ['nom'])) {
                    if ($adminUni->isColonneFixe($champ['nom'])) {
                        $listeChampsSticky[] = $champ;
                        if ($champ['nom'] == 'structure_hierarchique') {
                            $structIsSticky = true;
                        }
                        $regroupementSticky['nombreDeColonnes']++;
                    } else {
                        $listeChamps[] = $champ;
                        if ($champ['nom'] == 'structure_hierarchique') {
                            $regroupement['nombreDeColonnes'] += count($batiments_niveaux);
                        } else {
                            $regroupement['nombreDeColonnes']++;
                        }
                    }
                }
			}
            if ($regroupement['nombreDeColonnes'] > 0) {
                $listeRegroupements[] = $regroupement;
            }
		}
	}
}

$nbColonnesFixes = count($listeChampsSticky);
if ($structIsSticky) {
    $nbColonnesFixes += (count($batiments_niveaux)-1);
}

if (!empty($listeChampsSticky)) {
    $listeRegroupements = array_merge([['titre' => '', 'nombreDeColonnes' => $nbColonnesFixes, 'color' => 'light-grey']], $listeRegroupements);
}

$listeChamps = array_merge($listeChampsSticky, $listeChamps);

$coloredCols = ['depot_source1', 'depot_source2', 'depot_source3', 'depot_source4', 'depot_source5'];
?>
<div style="height: 75px;">
    <h2 style="position: fixed; left: 20px;"><?= $projet->numero . '-' . $projet->description ?></h2>
    <a id="boutonFermeture" onclick="toggleAcq(<?= $projet->id ?>,'<?= $projet->numero ?>',1);"><?= txt('Fermer la fenêtre') ?></a>
</div>
<table id="theTable-<?= $projet->id ?>" class="display nowrap"><thead>
    <tr>
        <?
        foreach ($listeRegroupements as $regroupement) {
            ?>
            <th colspan="<?= $regroupement['nombreDeColonnes'] ?>" style="background-color: <?= $regroupement['color'] ?>!important; background-image: none!important">
                <?= $regroupement['titre'] ?>
            </th>
            <?
        }
        ?>
    </tr>
    <tr>
        <?
        foreach ($listeChamps as $champ) {
            if ($champ['nom'] == 'structure_hierarchique') {
	            foreach ($batiments_niveaux as $iter => $batiment_niveau) {
		            ?>
                    <th width="300" style="<?= (isset($champ['color']) ? 'background-color: '.$champ['color'].'!important;' : '') ?>text-align: center;border-bottom: none!important; border-color: #dddddd #e7e7e7 #dddddd #e7e7e7; font-weight: 700; padding: 12px 5px;" data-format="varchar" class="varchar">
			            <?= $batiment_niveau->nom ?>
                    </th>
		            <?
	            }
            } else {
                $unitaire = isset($champ['unitaire']) ? ' unitaire' : '';
                ?>
                <th width="300" style="<?= (isset($champ['color']) ? 'background-color: '.$champ['color'].'!important;' : '') ?>text-align: center;border-bottom: none!important; border-color: #dddddd #e7e7e7 #dddddd #e7e7e7; font-weight: 700; padding: 12px 5px;" data-format="<?= ($champ['format'] ?? 'varchar') ?>" class="<?= ($champ['format'] ?? 'varchar') . (isset($champ['editable']) && $champ['editable'] == 1 ? ' editable' : '') . $unitaire ?>">
                    <span<?= $adminUni->getTooltipHtml($champ['nom']) ?>><?= $admin->getLabelForColonneMultiLigne($champ['nom']) ?></span>
                </th>
                <?
            }
        }
        ?>
    </tr>
    </thead>
</table>
<style>
    .dataTable td {
        padding: 4px 20px 4px 20px!important;
    }
    thead th {
        padding-right: 30px!important;
    }
    thead th.int, thead th.float, thead th.varchar {
        text-align: right!important;
        padding-right: 5px!important;
    }
    thead th.editable {
        text-align: center!important;
        padding-right: 0px!important;
    }
    td.alignRight {
        text-align: right !important;
    }
    .top, .bottom {
        padding: 0!important;
        background-color: inherit!important;
        border: 0 solid #CCCCCC!important;
    }
    .dataTables_info {
        width: 100%;
        left: 20px;
        top: 51px;
        position: fixed;
        clear: both;
    }
    .dataTables_paginate {
        width: 100%;
        right: 0;
        top: 42px;
        position: fixed;
        clear: both;
    }
    .fin {
        position: absolute;
        margin: 10px;
        padding: 10px 5px;
        background-image: url(css/light/images/paper_02.png);
        top: 0;
        z-index: 1500;
        left: 0;
        height: 100%;
    }
    .cb-dropdown-wrap {
        max-height: 500px; /* At most, around 3/4 visible items. */
        position: relative;
        height: 17px;
        width: 98%!important;
    }

    .cb-dropdown,
    .cb-dropdown li {
        margin: 0;
        padding: 0;
        list-style: none;
    }

    .cb-dropdown {
        position: absolute;
        z-index: 1;
        width: 100%;
        height: 100%;
        overflow: hidden;
        background: #fff;
        border: 1px solid #888;
    }

    /* For selected filter. */
    .active .cb-dropdown {
        background: white;
    }

    .cb-dropdown-expended {
        height: 400px;
        overflow: auto;
        transition: 0.2s height ease-in-out;
    }

    /* For selected items. */
    .cb-dropdown li.active {
        background-color: #edebeb;
    }

    .cb-dropdown li label {
        display: block;
        position: relative;
        cursor: pointer;
        line-height: 19px; /* Match height of .cb-dropdown-wrap */

    }

    .cb-dropdown li label > input {
        position: absolute;
        left: 5px;
        top: 2px;
        width: 16px;

    }

    .cb-dropdown li label > span {
        display: block;
        margin-left: 25px;
        margin-right: 5px; /* At least, width of the checkbox. */
        font-family: sans-serif;
        font-size: 0.8em;
        font-weight: normal;
        text-align: left;

    }

    .cb-dropdown-wrap .icone_fermeture {
        display: none;
        cursor: pointer;
        position: absolute;
        right: -3px;
        z-index: 100;
        top: -2px;
    }

    .cb-dropdown-wrap .decompte {
        display: none;
        position: absolute;
        right: 17px;
        z-index: 100;
        top: 2px;
        color: blue;
        font-size: 10px;
    }

    thead th a {
        text-decoration: underline!important;
    }
    div.dtfh-floatingparenthead {
        z-index:  102 !important;
        overflow: inherit !important;
    }

    #boutonFermeture {
        position: fixed;
        right: 10px;
        top: 10px;
        z-index:  111 !important;
    }

    th.dtfc-fixed-left {
        position: sticky;
        z-index: 1111;
    }

    tr.odd td {
        background-color: #f6f6f6;
    }

    tr.even td {
        background-color: #ffffff;
    }
    thead th.sorting {
        padding-right: 30px!important;
    }
    .ui-dialog {
        z-index: 1600!important;
    }
    tr.highlight > td
    {
        background-color: rgb(222, 252, 235)!important;
    }
</style>

<script>
    var dataForDatatable = [];
	var columnsDataForDatatable = [];
	<?
	$columnDataLigne = [];
	foreach ($listeChamps as $champ) {
        if ($champ['nom'] == 'structure_hierarchique') {
            foreach ($batiments_niveaux as $i => $b_niveau) {
	            $columnDataLigne[] = '{}';
            }
        } else {
            if (isset($champ['format']) && $champ['format'] == 'int' && (!isset($champ['editable']) || $champ['editable'] == 0)) {
                $columnDataLigne[] = '{ className: "alignRight ' . (isset($champ['transposable']) && $champ['transposable'] == 1 ? 'transposable' : '') . '", type: "num",
                     render: function (data, type, row, display) {
                          if ([\'filter\', \'sort\'].includes(type)) {
                            const reg = /value="(.*)" \/>/;
                            const matches = data.match(reg);
                            return parseInt(matches[1]);
                          }
                          return data;
                        }
                     }';
            } else if (isset($champ['format']) && $champ['format'] == 'float' && (!isset($champ['editable']) || $champ['editable'] == 0)) {
                $columnDataLigne[] = '{ className: "alignRight ' . (isset($champ['transposable']) && $champ['transposable'] == 1 ? 'transposable' : '') . '", type: "num",
                     render: function (data, type, row, display) {
                          if ([\'filter\', \'sort\'].includes(type)) {
                            const reg = /value="(.*)" \/>/;
                            const matches = data.match(reg);
                            return parseFloat(matches[1]);
                          }
                          return data;
                        }
                     }';
            } else if (isset($champ['editable']) && $champ['editable'] == 1) {
                if (isset($champ['format']) && $champ['format'] == 'select') {
                    $columnDataLigne[] = '{ className: "editable ' . (isset($champ['transposable']) && $champ['transposable'] == 1 ? 'transposable' : '') . '",
                     render: function (data, type, row, display) {
                          if ([\'filter\', \'sort\'].includes(type)) {
                            return $(data).find("option[selected=selected]").text();
                          }
                          return data;
                        }
                     }';
                } else {
                    $type = '';
                    if (isset($champ['format']) && $champ['format'] == 'int') {
                        $type = ', type: "num",
                     render: function (data, type, row, display) {
                          if ([\'filter\', \'sort\'].includes(type)) {
                            const reg = /value="(.*)" \/>/;
                            const matches = data.match(reg);
                            return parseInt(matches[1]);
                          }
                          return data;
                        }';
                    } else if (isset($champ['format']) && $champ['format'] == 'float') {
                        $type = ', type: "num",
                     render: function (data, type, row, display) {
                          if ([\'filter\', \'sort\'].includes(type)) {
                            const reg = /value="(.*)" \/>/;
                            const matches = data.match(reg);
                            return parseFloat(matches[1]);
                          }
                          return data;
                        }';
                    }
                    $columnDataLigne[] = '{ className: "editable ' . (isset($champ['transposable']) && $champ['transposable'] == 1 ? 'transposable' : '') . '"' . $type . ' }';
                }
            } else {
                $columnDataLigne[] = '{}';
            }
        }
	}
    ?>
	columnsDataForDatatable = [ <?= implode(", ", $columnDataLigne) ?> ];
    <?
    foreach ($infosLocauxEquipements as $ligne) {
        $dataLigne = [];
        foreach ($listeChamps as $champ) {
            if ($champ['nom'] == 'structure_hierarchique') {
                $iter_struct = 0;
                $struct_data = $batiments_niveaux_all[$ligne['fiche_id']] ?? array();
                foreach ($batiments_niveaux as $i => $b_niveau) {
                    $niveau = $struct_data['local_niveaux'][$iter_struct] ?? null;
	                $dataLigne[] = addcslashes((isset($niveau) ? $niveau['nom'] : ''), "'");
	                $iter_struct++;
                }
            } else {
                $editable = (isset($champ['editable']) && $champ['editable'] == 1);
                if (isset($ligne['isVerrouilleDepot']) && $ligne['isVerrouilleDepot'] == 1 && isset($champ['depot_source']) && $champ['depot_source'] == $ligne['source_financement_id']) {
                    $editable = false;
                }
	            $col_bd = $champ['col_bd'] ?? $champ['nom'];
	            $valeur = '';
                $valeurBrute = '';
                if (isset($champ['format']) && in_array($champ['format'], ['select', 'int']) == 'int') {
                    $valeurBrute = (int)$ligne[$col_bd];
		            $valeur = '<input type="hidden" class="champValeurBrute" data-id="'.$ligne['id'].'" value="' . $valeurBrute . '" />' . $valeurBrute;
	            } else if (isset($champ['format']) && $champ['format'] == 'float') {
                    $valeurBrute = dm_number_format((float)$ligne[$col_bd], 2, '.', '');
                    $cout = dm_number_format((float)$ligne[$col_bd], 2, ',', ' ') . '$';
		            $valeur = '<input type="hidden" class="champValeurBrute" data-id="'.$ligne['id'].'" value="' . $valeurBrute . '" />' . $cout;
	            } else {
		            if ($col_bd == 'taxable') {
			            $valeur = $ligne[$col_bd] == 1 ? txt('Oui') : txt('Non');
		            } else {
			            $valeur = $ligne[$col_bd];
		            }
                    $valeurBrute = $valeur;
	            }

                if ($editable) {
	                $format = $champ['format'] ?? '';
	                if ($format == 'select') {
                        $select = '';
		                $select .= '<select class="editable champValeurBrute" data-id="' . $ligne['id'] . '" data-champ="' . $col_bd . '" data-format="' . $format . '" data-format="' . $format . '">';
                        if (!isset($champ['noBlankOption'])) {
                            $select .= '<option value=""> </option>';
                        }
                        foreach ($champ['liste'] as $row) {
                            $row = (array)$row;
                            $sel = $row['id'] == $valeurBrute ? ' selected="selected"' : '';
                            $select .= '<option value="' . $row['id'] . '" id="' . ($champ['classe_source'] ?? '') . '-' . $row['id'] . '"' . $sel . '>' . $row['nom'] . '</option>';
                        }
                        $select .= '</select>';

                        $valeur = $select;
	                } else {
                        $classFormat = $format == 'int' ? 'justInteger' : ($format == 'float' ? 'justFloat' : '');
                        $texteAlign = in_array($format, ['int', 'float']) ? 'text-align: right;' : '';
                        $valeur = '<input type="text" class="editable champValeurBrute '. $classFormat .'" data-id="'.$ligne['id'].'" data-champ="'.$col_bd.'" value="'.$valeurBrute.'" data-format="'.$format.'" style="'.$texteAlign.'" />';
                    }
                }
	            $valeur = $valeur ?? '';
                $valeur = str_replace("\n", '', $valeur);
	            $dataLigne[] = addcslashes($valeur, "'");
            }
        }
        ?>
	    dataForDatatable.push( [ '<?= implode("', '", $dataLigne) ?>' ] );
    <?
    }
    ?>

	function getChampsEditionEnLot(api, colIdx) {
		let champs = [];
		api.column(colIdx, {filter: 'applied'}).every(function() {
			this.data().each(function(leChamp){
				champs.push($(leChamp));
            });
		});
		return champs;
    }

	function getIdFiltred(api, colIdx)
	{
        let ids = [];
		const champs = getChampsEditionEnLot(api, colIdx, true);
		for (const champ of champs) {
			ids.push($(champ).data('id'));
        }
		return ids;
    }

	function getIdsValeursFiltred(api, colIdx, curColIdx)
	{
		let valeurs = [];
		const champs = getChampsEditionEnLot(api, colIdx);
		for (const champ of champs) {
			valeurs.push({ id: $(champ).data('id'), valeur: $(champ).val() });
		}
		return valeurs;
	}

    function eLotButton(column, colIdx) {
		const lien = $('<a>', {
			'id': `editLot-${colIdx}`,
			'class': 'el-button'
		});
		lien.text('<?= txt('Éditer en lot', false) ?>');
		column.css('text-align', 'center!important');
		lien.appendTo(column);

		return lien;
    }

	function cbDropdown(column, colIdx) {
        const ul = $('<ul>', {
            'id': `filter-${colIdx}`,
            'class': 'cb-dropdown'
        });

		const boutonFermeture = $('<img>', {
			src: 'css/images/icons/dark/cross_3.png',
            class: 'icone_fermeture'
        });

		const libelleDecompte = $('<span>', {
			class: 'decompte'
		});

		const marginTop = column.hasClass('select') ? '5px' : '0';
		const div = $('<div>', {
			'class': 'cb-dropdown-wrap',
            'style': `margin-top: ${marginTop};`
		});

		libelleDecompte.appendTo(div);

		boutonFermeture.appendTo(div);

		ul.appendTo(div.appendTo(column));

		return ul;
	}

	function faireLeDecompte(ddmenu) {
		let decompte = 0;
		ddmenu.find('input[type=checkbox]').each(function(){
			if (this.checked) {
				decompte++;
            }
        });

		if (decompte > 0) {
			ddmenu.parent().find('span.decompte').text(`${ decompte } sélectionné(s)`);
			ddmenu.parent().find('span.decompte').show();
		} else {
			ddmenu.parent().find('span.decompte').hide();
		}
    }

    if (dataForDatatable.length > 0) {
		const tr = $('#theTable-<?= $projet->id ?> thead tr:last-child');
		const tr2 = tr.clone(true);
		tr.addClass('sorting');
		tr2.addClass('filters')
			.appendTo('#theTable-<?= $projet->id ?> thead');

		tr.find('th').removeClass('int').removeClass('float').removeClass('varchar');
		tr2.find('th').html('');

		var floatVal = function (i) {
			return parseFloat(typeof i === 'string' ? i.replace(/[\$ ]/g, '').replace(',', '.') : typeof i === 'number' ? i : 0);
		};

	    $(document).on('click', '.icone_fermeture', function(){
			$(this).hide();
			$(this).parent().find('ul.cb-dropdown').removeClass('cb-dropdown-expended');
			$(this).parent().find('input.searchbox').val('');
        });

		var datatable = $('#theTable-<?= $projet->id ?>').DataTable({
			language: {
				url: 'config/dataTables.french.json'
			},
			deferRender: true,
			data: dataForDatatable,
			columns: columnsDataForDatatable,
			dom: '<"top"ip>t<"bottom"ip>',
			pageLength: 300,
			orderCellsTop: true,
			fixedHeader: true,
			fixedColumns:   {
				left: <?= $nbColonnesFixes ?>
			},
			initComplete: function () {
				var api = this.api();

				let left = 0;
				api.columns().eq(0)
					.each(function (colIdx) {
						let column = api.column(colIdx);
						const cell = $('#theTable-<?= $projet->id ?>').find('.filters th').eq(
							colIdx
						);

						const cellSorting = $('#theTable-<?= $projet->id ?>').find('.sorting th').eq(
							colIdx
						);

						if (cellSorting.hasClass('dtfc-fixed-left')) {
							cell.addClass('dtfc-fixed-left');
							cell.css('left', left + 'px');
							left += cell.outerWidth();
						}

						const width = cell.width();
						cell.css('width', `${width}px!important`);
						cell.css('background-color', '#ffffe9');
						cell.css('background-image', 'none');

                        if (cell.hasClass('editable')) {
							eLotButton(cell, colIdx)
                                .on('click', function(){
									$("#dialog-editionEnLot").dialog({
										resizable: false,
										height: 305,
										width: 450,
										modal: true,
										open: function (ev, ui) {
											const champsEditables = getChampsEditionEnLot(api, colIdx);
											const champEditable = champsEditables[0].clone(true);
											$(champEditable).removeClass('editable');
											$(champEditable).removeClass('champEditable');
											$(champEditable).addClass('editableLot');
											$(champEditable).val('');
											$("#dialog-editionEnLot").find('p.first').html(champEditable);

											const select = $('<select class="colonnes" style="width: 99%;"></select>');
											let options = '';
											api.columns().eq(0).each(function (colIdxCur) {
                                                // Set the header cell to contain the input element
                                                var cellCur = $('#theTable-<?= $projet->id ?>').find('tr.sorting th').eq(
													colIdxCur
                                                );

												if (
													colIdxCur !== colIdx
                                                    && cellCur.hasClass('transposable')
                                                    && cell.hasClass(cellCur.data('format'))
                                                    && ['int', 'float'].includes(cellCur.data('format'))
                                                ) {
													options += `<option value="${colIdxCur}">${cellCur.text()}</option>`;
												}
                                            });

											if (options.length) {
												select.html('<option></option>' + options);
												$("#dialog-editionEnLot").find('p.second').html(select);
												$("#dialog-editionEnLot").find('.transposable').show();
												$("#dialog-editionEnLot").css('height', '188px');
											} else {
												$("#dialog-editionEnLot").find('.transposable').hide();
												$("#dialog-editionEnLot").css('height', '78px');
                                            }
										},
										buttons: {
											"Sauvegarder": function () {
												const _this = this;
												let messageConfirmation = '<?= txt('Êtes-vous certain de vouloir poursuivre?', false) ?>';
												const champSelect = $("#dialog-editionEnLot").find('.colonnes');
												const valeurSelect = $(champSelect).val();

                                                const champValeurBrute = $("#dialog-editionEnLot").find('.editableLot');
                                                let valeur = $(champValeurBrute).val();
												let ids = getIdFiltred(api, colIdx);
												if (champSelect.length && valeurSelect.length > 0) {
													ids = [];
													valeur = [];
													const valeurs = getIdsValeursFiltred(api, valeurSelect, colIdx);
													Object.keys(valeurs).forEach(key => {
														const laValeur = valeurs[key];
														ids.push(laValeur.id);
														valeur.push(laValeur.valeur);
                                                    });
												} else if (valeur.length === 0) {
                                                    messageConfirmation = '<?= txt('Attention!!\n\nToutes les données de cette colonne seront supprimées\n\nÊtes-vous certain de vouloir poursuivre?', false) ?>';
                                                }
                                                if (confirm(messageConfirmation)) {
                                                    $.startObjectLoader($("#dialog-editionEnLot"), `loading-dialog-editionEnLot`);
                                                    $.post(`index.php?action=saveStraightDataEquipementBioMedicaux`, {
														ids: ids,
                                                        valeur: valeur,
                                                        champ: $(champValeurBrute).data('champ'),
                                                        format: $(champValeurBrute).data('format')
                                                    }, function () {
                                                        $(_this).dialog("close");
                                                        $.stopObjectLoader(`loading-dialog-editionEnLot`);
														$('#acquisition_<?= $projet->id ?>').html('');

														show('loading', true);
														charger_projet(<?= $projet->id ?>,'<?= $projet->numero ?>',1);
                                                    });
                                                }
											},
											"Annuler": function () {
												$(this).dialog("close");
											}
										}
									});
                                });
                        }
						if ((cell.hasClass('varchar') && !cell.hasClass('editable')) || cell.hasClass('select')) {
							let ddmenu = cbDropdown(cell, colIdx)
								.on('change', ':checkbox', function () {
									if ($(this).hasClass('select_all')) {
										ddmenu.find('input[type=checkbox]').prop('checked', true);
									} else if ($(this).hasClass('unselect_all')) {
										let lastThis;
										ddmenu.find('input[type=checkbox]:checked').each(function(){
											$(this).prop('checked', false);
											$(this).closest('li').removeClass('active');
											if (!$(this).hasClass('unselect_all')) {
												lastThis = this;
                                            }
                                        });
										if (lastThis) {
											$(lastThis).trigger('change');
										}
                                    } else {
										var active;
										var vals = $(':checked', ddmenu).map(function (index, element) {
											active = true;
											return $.fn.dataTable.util.escapeRegex($(element).val());
										}).toArray().join('|');

										column
											.search(vals.length > 0 ? '^(' + vals + ')$' : '', true, false)
											.draw();

										// Highlight the current item if selected.
										if (this.checked) {
											$(this).closest('li').addClass('active');
										} else {
											$(this).closest('li').removeClass('active');
										}

										// Highlight the current filter if selected.
										var active2 = ddmenu.parent().is('.active');
										if (active && !active2) {
											ddmenu.parent().addClass('active');
										} else if (!active && active2) {
											ddmenu.parent().removeClass('active');
										}

										faireLeDecompte(ddmenu);
									}
								});
							let
								$label = $('<label>', {
									class: 'doNotHide'
								}),
                                $text = $('<span>', {
									text: '-',
								}),
								$champtexte = $('<input>', {
									class: 'searchbox',
									type: 'text',
									style: `border: solid 1px white!important; width: 95%;height: 15px!important;box-shadow: none!important;top: -1px!important;`
								});
							$text.appendTo($label);
							$champtexte.appendTo($label);
							ddmenu.append($('<li>', {
								class: 'doNotHide'
							}).append($label));

							let
								$label2 = $('<label>'),
								$text2 = $('<span>', {
									text: 'Désélectionner tout'
								}),
								$cb2 = $('<input>', {
									class: 'unselect_all',
									type: 'checkbox',
									value: 1
								});

							$text2.appendTo($label2);
							$cb2.appendTo($label2);

							ddmenu.append($('<li>', {
								class: 'doNotHide'
							}).append($label2));

							$champtexte
                                .on('keyup', function() {
                                    const valeur = this.value;
                                    const leMmenu = $(this).parent().parent().parent();
                                    if (valeur.length > 0) {
                                        let menu = leMmenu[0];
                                        let spans_to_show = contains(menu, 'span', valeur, true);
                                        spans_to_show.forEach(function(span) {
                                            const li = $(span).parent().parent();
                                            if (li.css('display') === 'none') {
                                                li.css('display', '');
                                            }
                                        });

                                        let spans_to_hide = contains(menu, 'span', valeur, false);
                                        spans_to_hide.forEach(function(span) {
                                            const li = $(span).parent().parent();
                                            if (li.css('display') !== 'none' && !li.hasClass('doNotHide') && !li.find('input[type=checkbox]').prop('checked')) {
                                                li.css('display', 'none');
                                            }
                                        });

                                    } else {
                                        leMmenu.find('li').css('display', '');
                                    }
								})
								.on('focus', function() {
                                    const leMmenu = $(this).parent().parent().parent();
									leMmenu.addClass('cb-dropdown-expended');
									leMmenu.parent().find('.icone_fermeture').show();
                                })
							;

							let alreadyListed = [];
							column.data().unique().sort().each(function (d, j) {
								let text = d;
								if (/<select/.test(text)) {
									const reg = /<option [^>]+ selected="selected">(.*?)<\/option>/;
									const matches = d.match(reg);
									if (matches && matches[1]) {
										text = matches[1];
                                    } else {
										text = '';
                                    }
                                }

								if (text !== '' && !alreadyListed.includes(text)) {
									alreadyListed.push(text);
									var // wrapped
										$label = $('<label>'),
										$text = $('<span>', {
											text: text
										}),
										$cb = $('<input>', {
											type: 'checkbox',
											value: text
										});

									$text.appendTo($label);
									$cb.appendTo($label);

									ddmenu.append($('<li>').append($label));
								}
							});
							alreadyListed = [];
						}
					}
				);
				show('loading', false);
			},
			"fnDrawCallback": function ( row, data, start, end, display ) {
				var api = this.api(), data;

				api.columns()
					.eq(0)
					.each(function (colIdx) {
						// Set the header cell to contain the input element
						var cell = $('#theTable-<?= $projet->id ?>').find('.filters th').eq(
							colIdx
						);

						if (!cell.hasClass('editable')) {
							if (cell.hasClass('int') && !cell.hasClass('unitaire')) {
								api.column(colIdx, {filter: 'applied'}).every(function () {
									var sum = this
										.data()
										.reduce(function (a, b) {
											const reg = /value="(.*)" \/>/;
											const matches = b.match(reg);
											const bb = matches[1];
											var x = parseInt(a) || 0;
											var y = parseInt(bb) || 0;
											return x + y;
										}, 0);
									$(cell).html(sum);
								});
							} else if (cell.hasClass('float') && !cell.hasClass('unitaire')) {
								api.column(colIdx, {filter: 'applied'}).every(function () {
									var sum = this
										.data()
										.reduce(function (a, b) {
											const reg = /value="(.*)" \/>/;
											const matches = b.match(reg);
											const bb = matches[1];
											var x = floatVal(a) || 0;
											var y = floatVal(bb) || 0;
											return x + y;
										}, 0);
									$(cell).html(formatNumberMoney(sum) + '$');
								});
							}
						}
					});
			}
		});

		datatable.on('click', 'td', function () {
			const rowIdx = datatable.cell(this).index().row;

			datatable
				.row(rowIdx)
				.nodes()
				.each((el) => {
					if (el.classList.contains("highlight")) {
						el.classList.remove('highlight');
                    } else {
						el.classList.add('highlight');
                    }
				});
		});
	}
</script>
<div id="dialog-editionEnLot" title="Édition en lot" style="display: none; margin: 5px 15px;">
    <span style="margin-top: 5px;"><?= txt('Copier cette valeur partout:') ?></span><br />
    <p class="first" style="margin-bottom: 0; margin-top: 5px;"></p>
    <span style="margin-top: 5px; color: blue;">* <?= txt('Laisser le champ libre pour supprimer.') ?></span><br /><br />

    <div class="transposable">
        <span style="margin-top: 5px;"><?= txt('OU') ?></span><br /><br />
        <span style="margin-top: 5px;"><?= txt('Transposer les valeurs de la colonne suivante:') ?></span><br />
        <p class="second" style="margin-top: 10px;"></p>
    </div>
</div>
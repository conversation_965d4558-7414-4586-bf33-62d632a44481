<?php
$projet = new FinancementsProjets(intval($_GET['projet_id']));

$sql = "select * from suivis_budgetaires where projet_id = ?";
$suivi = $db->getRow($sql, [getProjetId()]);
if (isset($suivi['id'])) {
	$suiviBudgetaire = new SuivisBudgetaires($suivi['id']);
}

$formulaires_listes = EquipementsBioMedicauxTypes::getFormulairesListes('EquipementsBioMedicauxTypes');

$fiches_ids = array();

$fiches = $projet->getGroupesFiches();

$totaux = [];
$totaux['requise_init'] = 0;
$totaux['existante_init'] = 0;
$totaux['demenager_init'] = 0;
$totaux['requise_actuelle'] = 0;
$totaux['existante_actuelle'] = 0;
$totaux['demenager_actuelle'] = 0;
$totaux['remplacement_actuelle'] = 0;
$totaux['col_nouveaux'] = 0;
$totaux['developpement_actuelle'] = 0;
if (isset($suiviBudgetaire)) {
	foreach ($suiviBudgetaire->getPortefeuilles(true) as $portefeuille) {
		$totaux['qt_portefeuille_' . $portefeuille['portefeuille_numero']] = 0;
	}
}
$totaux['deja_traite'] = 0;
$totaux['residuel_com'] = 0;
$totaux['commande'] = 0;
$totaux['cout_norme'] = 0;
$totaux['cout_taxe_net'] = 0;
$totaux['cout_projete'] = 0;
$totaux['quantite_autorise'] = 0;
$totaux['prix_unitaire_autorise'] = 0;
for ($ii = 1; $ii <= 5; $ii++) {
	if (!isset($totaux['cout_supplementaire_' . $ii])) {
		$totaux['cout_supplementaire_' . $ii] = 0.0;
	}
}
for ($ii = 1; $ii <= 10; $ii++) {
	if (!isset($totaux['colonne_supplementaire_detail_' . $ii])) {
		$totaux['colonne_supplementaire_detail_' . $ii] = 0;
	}
}

$totaux['distribue'] = 0;
foreach ($fiches as $key => $fiche) {
	$fiche['remplacement_actuelle'] = (int)$fiche['existante_actuelle'] - (int)$fiche['demenager_actuelle'];
	$fiche['col_nouveaux'] = (int)$fiche['requise_actuelle'] - (int)$fiche['demenager_actuelle'];
	$fiche['developpement_actuelle'] = (int)$fiche['requise_actuelle'] - (int)$fiche['existante_actuelle'];
	$fiche['a_traiter'] = $fiche[$projet->champ_reference];
	$fiche['residuel_com'] = ($fiche['a_traiter'] - $fiche['deja_traite']);

	$fiches[$key]['remplacement_actuelle'] = $fiche['remplacement_actuelle'];
	$fiches[$key]['col_nouveaux'] = $fiche['col_nouveaux'];
	$fiches[$key]['developpement_actuelle'] = $fiche['developpement_actuelle'];
	$fiches[$key]['a_traiter'] = $fiche['a_traiter'];
	$fiches[$key]['residuel_com'] = $fiche['residuel_com'];

    $fiches_ids[$fiche['fiche_id']] = $fiche['fiche_id'];

	$totaux['requise_init'] += $fiche['requise_init'];
	$totaux['existante_init'] += $fiche['existante_init'];
	$totaux['demenager_init'] += $fiche['demenager_init'];
	$totaux['requise_actuelle'] += $fiche['requise_actuelle'];
	$totaux['existante_actuelle'] += $fiche['existante_actuelle'];
	$totaux['demenager_actuelle'] += $fiche['demenager_actuelle'];
	$totaux['remplacement_actuelle'] += $fiche['remplacement_actuelle'];
	$totaux['col_nouveaux'] += $fiche['col_nouveaux'];
	$totaux['developpement_actuelle'] += $fiche['developpement_actuelle'];
	if (isset($suiviBudgetaire)) {
		foreach ($suiviBudgetaire->getPortefeuilles(true) as $portefeuille) {
			$totaux['qt_portefeuille_' . $portefeuille['portefeuille_numero']] += $fiche['qt_portefeuille_' . $portefeuille['portefeuille_numero']];
		}
	}
	$totaux['deja_traite'] += $fiche['deja_traite'];
	$totaux['residuel_com'] += ($fiche['a_traiter'] - $fiche['deja_traite']);
	$totaux['commande'] += $fiche['commande'];
	$totaux['cout_norme'] += ((int)$fiche['commande'] * $fiche['cout_norme']);
	$totaux['cout_taxe_net'] += $fiche['cout_taxe_net'];
	$totaux['cout_projete'] += $fiche['cout_projete'];
	$totaux['quantite_autorise'] += $fiche['quantite_autorise'];
	$totaux['prix_unitaire_autorise'] += $fiche['prix_unitaire_autorise'];

	for ($ii = 1; $ii <= 5; $ii++) {
		$totaux['cout_supplementaire_' . $ii] += (float)$fiche['cout_supplementaire_' . $ii];
	}

	for ($ii = 1; $ii <= 10; $ii++) {
		if ($totaux['colonne_supplementaire_detail_' . $ii] <> -99999) {
			if (is_numeric($fiche['colonne_supplementaire_detail_' . $ii]) || $fiche['colonne_supplementaire_detail_' . $ii] == '') {
				$totaux['colonne_supplementaire_detail_' . $ii] += $fiche['colonne_supplementaire_detail_' . $ii];
			} else {
				$totaux['colonne_supplementaire_detail_' . $ii] = -99999;
			}
		}
	}
	$totaux['distribue'] += $fiche['distribue'];
}

$qte_a_commander_totale = 0;
foreach($fiches as $i => $fiche) {
	$qte_a_commander_totale += $fiche['commande'];
}

$qte_distribuee_totale = 0;
$qte_residuel_dist_total = 0;
foreach($fiches as $i => $fiche) {
	$qte_distribuee_totale += isset($fiche['distribue']) && dm_strlen($fiche['distribue']) > 0 ? (int)$fiche['distribue'] : 0;
}
//$qte_residuel_dist_total = $groupe->quantite_disponible - $qte_distribuee_totale;

$admin = FinancementsProjetsAdministrations::getAdminForProjet();
?>
<div style="padding: 10px; text-align: right; background-image: url(css/light/images/paper_02.png); border-color: #dddddd #e7e7e7 #dddddd #e7e7e7;">
    <?= $admin->getLabelForColonne('col_qte_a_commander_totale') ?>: <span style="color: blue; font-weight: bold"><?= $qte_a_commander_totale ?></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
    <?= $admin->getLabelForColonne('col_residuel_comm') ?>: <span style="color: blue; font-weight: bold"><?= $totaux['residuel_com'] ?></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
<!--    --><?//= $admin->getLabelForColonne('col_total_recu') ?><!--: <span style="color: blue; font-weight: bold">--><?//= (int)$groupe->quantite_disponible ?><!--</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;-->
    <?= $admin->getLabelForColonne('col_total_distribue') ?>: <span style="color: blue; font-weight: bold"><?= $qte_distribuee_totale ?></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
<!--    --><?//= $admin->getLabelForColonne('col_residuel_distribue') ?><!--: <span style="color: blue; font-weight: bold">--><?//= $qte_residuel_dist_total ?><!--</span>-->
</div>
<?
$fiches_infos = Fiches::getListe(['ids' => $fiches_ids]);
?>
<table>
    <tr>
        <td width="300" style="display: <?= ($admin->isColonneVisible('col_local') ? '' : 'none') ?>; border-bottom: none!important; text-shadow: 0 -1px 0 #ffffff; background-image: url(css/light/images/paper_02.png); border-color: #dddddd #e7e7e7 #dddddd #e7e7e7; font-weight: 700; padding: 12px 5px;">
		    <?= $admin->getLabelForColonne('col_local') ?>
        </td><td width="140" style="text-align: center; display: <?= ($admin->isColonneVisible('col_code_equipement') ? '' : 'none') ?>; border-bottom: none!important; text-shadow: 0 -1px 0 #ffffff; background-image: url(css/light/images/paper_02.png); border-color: #dddddd #e7e7e7 #dddddd #e7e7e7; font-weight: 700; padding: 12px 5px;">
		    <?= $admin->getLabelForColonne('col_code_equipement') ?>
        </td><td width="110" style="text-align: center; display: <?= ($admin->isColonneVisible('col_description_equipement') ? '' : 'none') ?>; border-bottom: none!important; text-shadow: 0 -1px 0 #ffffff; background-image: url(css/light/images/paper_02.png); border-color: #dddddd #e7e7e7 #dddddd #e7e7e7; font-weight: 700; padding: 12px 5px;">
		    <?= $admin->getLabelForColonne('col_description_equipement') ?>
        </td><td width="75" style="display: <?= ($admin->isColonneVisible('col_requis_j1') ? '' : 'none') ?>;text-align: center; border-bottom: none!important; text-shadow: 0 -1px 0 #ffffff; background-image: url(css/light/images/paper_02.png); border-color: #dddddd #e7e7e7 #dddddd #e7e7e7; font-weight: 700; padding: 12px 5px;">
		    <?= $admin->getLabelForColonne('col_requis_j1') ?>
        </td><td width="75" style="display: <?= ($admin->isColonneVisible('col_existante_j1') ? '' : 'none') ?>;text-align: center; border-bottom: none!important; text-shadow: 0 -1px 0 #ffffff; background-image: url(css/light/images/paper_02.png); border-color: #dddddd #e7e7e7 #dddddd #e7e7e7; font-weight: 700; padding: 12px 5px;">
            <?= $admin->getLabelForColonne('col_existante_j1') ?>
        </td><td width="75" style="display: <?= ($admin->isColonneVisible('col_conserve_j1') ? '' : 'none') ?>;text-align: center; border-bottom: none!important; text-shadow: 0 -1px 0 #ffffff; background-image: url(css/light/images/paper_02.png); border-color: #dddddd #e7e7e7 #dddddd #e7e7e7; font-weight: 700; padding: 12px 5px;">
            <?= $admin->getLabelForColonne('col_conserve_j1') ?>
        </td><td width="75" style="<?= ($projet->champ_reference == 'requise_actuelle' ? 'background-color: lightcyan;' : 'background-image: url(css/light/images/paper_02.png);') ?> display: <?= ($admin->isColonneVisible('requise_actuelle') ? '' : 'none') ?>;text-align: center; border-bottom: none!important; text-shadow: 0 -1px 0 #ffffff; border-color: #dddddd #e7e7e7 #dddddd #e7e7e7; font-weight: 700; padding: 12px 5px;">
            <?= $admin->getLabelForColonne('requise_actuelle') ?>
        </td><td width="75" style="<?= ($projet->champ_reference == 'existante_actuelle' ? 'background-color: lightcyan;' : 'background-image: url(css/light/images/paper_02.png);') ?> display: <?= ($admin->isColonneVisible('existante_actuelle') ? '' : 'none') ?>;text-align: center; border-bottom: none!important; text-shadow: 0 -1px 0 #ffffff; border-color: #dddddd #e7e7e7 #dddddd #e7e7e7; font-weight: 700; padding: 12px 5px;">
            <?= $admin->getLabelForColonne('existante_actuelle') ?>
        </td><td width="75" style="<?= ($projet->champ_reference == 'demenager_actuelle' ? 'background-color: lightcyan;' : 'background-image: url(css/light/images/paper_02.png);') ?> display: <?= ($admin->isColonneVisible('demenager_actuelle') ? '' : 'none') ?>;text-align: center; border-bottom: none!important; text-shadow: 0 -1px 0 #ffffff; border-color: #dddddd #e7e7e7 #dddddd #e7e7e7; font-weight: 700; padding: 12px 5px;">
            <?= $admin->getLabelForColonne('demenager_actuelle') ?>
        </td><td width="100" style="<?= ($projet->champ_reference == 'remplacement_actuelle' ? 'background-color: lightcyan;' : 'background-image: url(css/light/images/paper_02.png);') ?> display: <?= ($admin->isColonneVisible('remplacement_actuelle') ? '' : 'none') ?>;text-align: center; border-bottom: none!important; text-shadow: 0 -1px 0 #ffffff; border-color: #dddddd #e7e7e7 #dddddd #e7e7e7; font-weight: 700; padding: 12px 5px;">
            <?= $admin->getLabelForColonne('remplacement_actuelle') ?>
        </td><td width="100" style="<?= ($projet->champ_reference == 'col_nouveaux' ? 'background-color: lightcyan;' : 'background-image: url(css/light/images/paper_02.png);') ?> display: <?= ($admin->isColonneVisible('col_nouveaux') ? '' : 'none') ?>;text-align: center; border-bottom: none!important; text-shadow: 0 -1px 0 #ffffff; border-color: #dddddd #e7e7e7 #dddddd #e7e7e7; font-weight: 700; padding: 12px 5px;">
            <?= $admin->getLabelForColonne('col_nouveaux') ?>
        </td><td width="100" style="<?= ($projet->champ_reference == 'developpement_actuelle' ? 'background-color: lightcyan;' : 'background-image: url(css/light/images/paper_02.png);') ?> display: <?= ($admin->isColonneVisible('developpement_actuelle') ? '' : 'none') ?>;text-align: center; border-bottom: none!important; text-shadow: 0 -1px 0 #ffffff; border-color: #dddddd #e7e7e7 #dddddd #e7e7e7; font-weight: 700; padding: 12px 5px;">
            <?= $admin->getLabelForColonne('developpement_actuelle') ?>
        </td>
        <?
        if (isset($suiviBudgetaire)) {
            foreach ($suiviBudgetaire->getPortefeuilles(true) as $portefeuille) {
                ?>
                <td width="75" style="<?= ($projet->champ_reference == 'qt_portefeuille_' . $portefeuille['portefeuille_numero'] ? 'background-color: lightcyan;' : 'background-image: url(css/light/images/paper_02.png);') ?> display: <?= ($admin->isColonneVisible('qt_portefeuille_' . $portefeuille['portefeuille_numero']) ? '' : 'none') ?>;text-align: center; border-bottom: none!important; text-shadow: 0 -1px 0 #ffffff; border-color: #dddddd #e7e7e7 #dddddd #e7e7e7; font-weight: 700; padding: 12px 5px;">
                    <?= $admin->getLabelForColonne('qt_portefeuille_' . $portefeuille['portefeuille_numero']) ?>
                </td>
                <?
            }
        }
        ?>

        <td width="75" style="display: <?= ($admin->isColonneVisible('col_deja_traite') ? '' : 'none') ?>;text-align: center; border-bottom: none!important; text-shadow: 0 -1px 0 #ffffff; background-image: url(css/light/images/paper_02.png); border-color: #dddddd #e7e7e7 #dddddd #e7e7e7; font-weight: 700; padding: 12px 5px;">
            <?= $admin->getLabelForColonne('col_deja_traite') ?>
        </td>

        <td width="100" style="display: <?= ($admin->isColonneVisible('col_residuel_com') ? '' : 'none') ?>;text-align: center; border-bottom: none!important; text-shadow: 0 -1px 0 #ffffff; background-image: url(css/light/images/paper_02.png); border-color: #dddddd #e7e7e7 #dddddd #e7e7e7; font-weight: 700; padding: 12px 5px;">
            <?= $admin->getLabelForColonne('col_residuel_com') ?>
        </td>


        <td width="200" style="display: <?= ($admin->isColonneVisible('col_a_commander') ? '' : 'none') ?>;text-align: center; border-bottom: none!important; text-shadow: 0 -1px 0 #ffffff; background-image: url(css/light/images/paper_02.png); border-color: #dddddd #e7e7e7 #dddddd #e7e7e7; font-weight: 700; padding: 12px 5px;">
            <?= $admin->getLabelForColonne('col_a_commander') ?>
            <?php if (havePermission(array('administration_quantite_financer'))){ ?>
<!--                <input type="button" class="tout_associer" value="--><?//=txt('Tout soumettre')?><!--" data-parent_id="groupe_--><?//=$groupe->id?><!--" data-projet_numero="--><?//=$projet->numero?><!--" data-projet_id="--><?//=$projet->id?><!--" style="padding: 0 5px; margin-left: 5px;" />-->
        <?php } ?>
        </td>

        <td width="100" style="display: <?= ($admin->isColonneVisible('cout_norme') ? '' : 'none') ?>;text-align: center; border-bottom: none!important; text-shadow: 0 -1px 0 #ffffff; background-image: url(css/light/images/paper_02.png); border-color: #dddddd #e7e7e7 #dddddd #e7e7e7; font-weight: 700; padding: 12px 5px;">
            <?= $admin->getLabelForColonne('cout_norme') ?>
        </td>
        
    	<td width="100" style="display: <?= ($admin->isColonneVisible('cout_taxe_net') ? '' : 'none') ?>;text-align: center; border-bottom: none!important; text-shadow: 0 -1px 0 #ffffff; background-image: url(css/light/images/paper_02.png); border-color: #dddddd #e7e7e7 #dddddd #e7e7e7; font-weight: 700; padding: 12px 5px;">
            <?= $admin->getLabelForColonne('cout_taxe_net') ?>
        </td>
    
        <td width="100" style="display: <?= ($admin->isColonneVisible('cout_projete') ? '' : 'none') ?>;text-align: center; border-bottom: none!important; text-shadow: 0 -1px 0 #ffffff; background-image: url(css/light/images/paper_02.png); border-color: #dddddd #e7e7e7 #dddddd #e7e7e7; font-weight: 700; padding: 12px 5px;">
            <?= $admin->getLabelForColonne('cout_projete') ?>
        </td>

        <td width="100" style="display: <?= ($admin->isColonneVisible('quantite_autorise') ? '' : 'none') ?>;text-align: center; border-bottom: none!important; text-shadow: 0 -1px 0 #ffffff; background-image: url(css/light/images/paper_02.png); border-color: #dddddd #e7e7e7 #dddddd #e7e7e7; font-weight: 700; padding: 12px 5px;">
            <?= $admin->getLabelForColonne('quantite_autorise') ?>
        </td>

        <td width="100" style="display: <?= ($admin->isColonneVisible('prix_unitaire_autorise') ? '' : 'none') ?>;text-align: center; border-bottom: none!important; text-shadow: 0 -1px 0 #ffffff; background-image: url(css/light/images/paper_02.png); border-color: #dddddd #e7e7e7 #dddddd #e7e7e7; font-weight: 700; padding: 12px 5px;">
            <?= $admin->getLabelForColonne('prix_unitaire_autorise') ?>
        </td>
        <td width="100" style="display: <?= ($admin->isColonneVisible('cout_supplementaire_1') ? '' : 'none') ?>;text-align: center; border-bottom: none!important; text-shadow: 0 -1px 0 #ffffff; background-image: url(css/light/images/paper_02.png); border-color: #dddddd #e7e7e7 #dddddd #e7e7e7; font-weight: 700; padding: 12px 5px;">
		    <?= $admin->getLabelForColonne('cout_supplementaire_1') ?>
        </td>
        <td width="100" style="display: <?= ($admin->isColonneVisible('cout_supplementaire_2') ? '' : 'none') ?>;text-align: center; border-bottom: none!important; text-shadow: 0 -1px 0 #ffffff; background-image: url(css/light/images/paper_02.png); border-color: #dddddd #e7e7e7 #dddddd #e7e7e7; font-weight: 700; padding: 12px 5px;">
		    <?= $admin->getLabelForColonne('cout_supplementaire_2') ?>
        </td>
        <td width="100" style="display: <?= ($admin->isColonneVisible('cout_supplementaire_3') ? '' : 'none') ?>;text-align: center; border-bottom: none!important; text-shadow: 0 -1px 0 #ffffff; background-image: url(css/light/images/paper_02.png); border-color: #dddddd #e7e7e7 #dddddd #e7e7e7; font-weight: 700; padding: 12px 5px;">
		    <?= $admin->getLabelForColonne('cout_supplementaire_3') ?>
        </td>
        <td width="100" style="display: <?= ($admin->isColonneVisible('cout_supplementaire_4') ? '' : 'none') ?>;text-align: center; border-bottom: none!important; text-shadow: 0 -1px 0 #ffffff; background-image: url(css/light/images/paper_02.png); border-color: #dddddd #e7e7e7 #dddddd #e7e7e7; font-weight: 700; padding: 12px 5px;">
		    <?= $admin->getLabelForColonne('cout_supplementaire_4') ?>
        </td>
        <td width="100" style="display: <?= ($admin->isColonneVisible('cout_supplementaire_5') ? '' : 'none') ?>;text-align: center; border-bottom: none!important; text-shadow: 0 -1px 0 #ffffff; background-image: url(css/light/images/paper_02.png); border-color: #dddddd #e7e7e7 #dddddd #e7e7e7; font-weight: 700; padding: 12px 5px;">
		    <?= $admin->getLabelForColonne('cout_supplementaire_5') ?>
        </td>
        <td width="100" style="display: <?= ($admin->isColonneVisible('colonne_supplementaire_detail_1') ? '' : 'none') ?>;text-align: center; border-bottom: none!important; text-shadow: 0 -1px 0 #ffffff; background-image: url(css/light/images/paper_02.png); border-color: #dddddd #e7e7e7 #dddddd #e7e7e7; font-weight: 700; padding: 12px 5px;">
            <?= $admin->getLabelForColonne('colonne_supplementaire_detail_1') ?>
        </td>
        <td width="100" style="display: <?= ($admin->isColonneVisible('colonne_supplementaire_detail_2') ? '' : 'none') ?>;text-align: center; border-bottom: none!important; text-shadow: 0 -1px 0 #ffffff; background-image: url(css/light/images/paper_02.png); border-color: #dddddd #e7e7e7 #dddddd #e7e7e7; font-weight: 700; padding: 12px 5px;">
            <?= $admin->getLabelForColonne('colonne_supplementaire_detail_2') ?>
        </td>
        <td width="100" style="display: <?= ($admin->isColonneVisible('colonne_supplementaire_detail_3') ? '' : 'none') ?>;text-align: center; border-bottom: none!important; text-shadow: 0 -1px 0 #ffffff; background-image: url(css/light/images/paper_02.png); border-color: #dddddd #e7e7e7 #dddddd #e7e7e7; font-weight: 700; padding: 12px 5px;">
            <?= $admin->getLabelForColonne('colonne_supplementaire_detail_3') ?>
        </td>
        <td width="100" style="display: <?= ($admin->isColonneVisible('colonne_supplementaire_detail_4') ? '' : 'none') ?>;text-align: center; border-bottom: none!important; text-shadow: 0 -1px 0 #ffffff; background-image: url(css/light/images/paper_02.png); border-color: #dddddd #e7e7e7 #dddddd #e7e7e7; font-weight: 700; padding: 12px 5px;">
            <?= $admin->getLabelForColonne('colonne_supplementaire_detail_4') ?>
        </td>
        <td width="100" style="display: <?= ($admin->isColonneVisible('colonne_supplementaire_detail_5') ? '' : 'none') ?>;text-align: center; border-bottom: none!important; text-shadow: 0 -1px 0 #ffffff; background-image: url(css/light/images/paper_02.png); border-color: #dddddd #e7e7e7 #dddddd #e7e7e7; font-weight: 700; padding: 12px 5px;">
            <?= $admin->getLabelForColonne('colonne_supplementaire_detail_5') ?>
        </td>
        <td width="100" style="display: <?= ($admin->isColonneVisible('colonne_supplementaire_detail_6') ? '' : 'none') ?>;text-align: center; border-bottom: none!important; text-shadow: 0 -1px 0 #ffffff; background-image: url(css/light/images/paper_02.png); border-color: #dddddd #e7e7e7 #dddddd #e7e7e7; font-weight: 700; padding: 12px 5px;">
            <?= $admin->getLabelForColonne('colonne_supplementaire_detail_6') ?>
        </td>
        <td width="100" style="display: <?= ($admin->isColonneVisible('colonne_supplementaire_detail_7') ? '' : 'none') ?>;text-align: center; border-bottom: none!important; text-shadow: 0 -1px 0 #ffffff; background-image: url(css/light/images/paper_02.png); border-color: #dddddd #e7e7e7 #dddddd #e7e7e7; font-weight: 700; padding: 12px 5px;">
            <?= $admin->getLabelForColonne('colonne_supplementaire_detail_7') ?>
        </td>
        <td width="100" style="display: <?= ($admin->isColonneVisible('colonne_supplementaire_detail_8') ? '' : 'none') ?>;text-align: center; border-bottom: none!important; text-shadow: 0 -1px 0 #ffffff; background-image: url(css/light/images/paper_02.png); border-color: #dddddd #e7e7e7 #dddddd #e7e7e7; font-weight: 700; padding: 12px 5px;">
            <?= $admin->getLabelForColonne('colonne_supplementaire_detail_8') ?>
        </td>
        <td width="100" style="display: <?= ($admin->isColonneVisible('colonne_supplementaire_detail_9') ? '' : 'none') ?>;text-align: center; border-bottom: none!important; text-shadow: 0 -1px 0 #ffffff; background-image: url(css/light/images/paper_02.png); border-color: #dddddd #e7e7e7 #dddddd #e7e7e7; font-weight: 700; padding: 12px 5px;">
            <?= $admin->getLabelForColonne('colonne_supplementaire_detail_9') ?>
        </td>
        <td width="100" style="display: <?= ($admin->isColonneVisible('colonne_supplementaire_detail_10') ? '' : 'none') ?>;text-align: center; border-bottom: none!important; text-shadow: 0 -1px 0 #ffffff; background-image: url(css/light/images/paper_02.png); border-color: #dddddd #e7e7e7 #dddddd #e7e7e7; font-weight: 700; padding: 12px 5px;">
            <?= $admin->getLabelForColonne('colonne_supplementaire_detail_10') ?>
        </td>

        <td width="195" style="display: <?= ($admin->isColonneVisible('col_a_distribuer') ? '' : 'none') ?>;text-align: center; border-bottom: none!important; text-shadow: 0 -1px 0 #ffffff; background-image: url(css/light/images/paper_02.png); border-color: #dddddd #e7e7e7 #dddddd #e7e7e7; font-weight: 700; padding: 12px 5px;">
            <?= $admin->getLabelForColonne('col_a_distribuer') ?>
            <?php if (havePermission(array('administration_quantite_approuvees'))) { ?>
<!--                <input type="button" class="tout_distribuer" value="--><?//=txt('Tout accepter')?><!--" data-parent_id="groupe_--><?//=$groupe->id?><!--" style="padding: 0 5px; margin-left: 5px;" data-projet_numero="--><?//=$projet->numero?><!--" data-projet_id="--><?//=$projet->id?><!--" />-->
            <?php } ?>
        </td>

        <td width="100" style="display: <?= ($admin->isColonneVisible('col_residuel_dist') ? '' : 'none') ?>;text-align: center; border-bottom: none!important; text-shadow: 0 -1px 0 #ffffff; background-image: url(css/light/images/paper_02.png); border-color: #dddddd #e7e7e7 #dddddd #e7e7e7; font-weight: 700; padding: 12px 5px;">
            <?= $admin->getLabelForColonne('col_residuel_dist') ?>
        </td>

        <td style="width: 50px; text-align: center; vertical-align: middle; border-bottom: none!important; text-shadow: 0 -1px 0 #ffffff; background-image: url(css/light/images/paper_02.png); border-color: #dddddd #e7e7e7 #dddddd #e7e7e7; font-weight: 700; padding: 12px 5px;">

        </td>

    </tr>
    <tr style="background-color: #ffffe9;">
        <td width="300" height="35" colspan="<?= ($admin->isColonneVisible('col_description_equipement') ? 3 : 2) ?>"
            style="text-align: right;padding: 0px 4px!important;">
			<?= txt('Totaux') ?>:
        </td>
        <td style="display: <?= ($admin->isColonneVisible('col_requis_j1') ? '' : 'none') ?>;text-align: center; padding: 0px 4px!important;"
            width="75">
			<?= $totaux['requise_init'] ?>
        </td>
        <td style="display: <?= ($admin->isColonneVisible('col_existante_j1') ? '' : 'none') ?>;text-align: center; padding: 0px 4px!important;"
            width="75">
			<?= $totaux['existante_init'] ?>
        </td>
        <td style="display: <?= ($admin->isColonneVisible('col_conserve_j1') ? '' : 'none') ?>;text-align: center; padding: 0px 4px!important;"
            width="75">
			<?= $totaux['demenager_init'] ?>
        </td>
        <td class="<?= ($totaux['requise_init'] <> $totaux['requise_actuelle'] ? 'isalert' : '') ?>"
            style="<?= ($projet->champ_reference == 'demenager_actuelle' ? 'background-color: lightcyan;' : '') ?> display: <?= ($admin->isColonneVisible('requise_actuelle') ? '' : 'none') ?>;text-align: center;"
            width="75">
			<?= $totaux['requise_actuelle'] ?>
        </td>
        <td class=""
            style="<?= ($projet->champ_reference == 'demenager_actuelle' ? 'background-color: lightcyan;' : '') ?> display: <?= ($admin->isColonneVisible('existante_actuelle') ? '' : 'none') ?>;text-align: center;"
            width="75">
			<?= $totaux['existante_actuelle'] ?>
        </td>
        <td class="<?= ($totaux['demenager_init'] <> $totaux['demenager_actuelle'] ? 'isalert' : '') ?>"
            style="<?= ($projet->champ_reference == 'demenager_actuelle' ? 'background-color: lightcyan;' : '') ?> display: <?= ($admin->isColonneVisible('demenager_actuelle') ? '' : 'none') ?>;text-align: center;"
            width="75">
			<?= $totaux['demenager_actuelle'] ?>
        </td>
        <td class="<?= ($totaux['remplacement_actuelle'] < 0 ? 'isalert' : '') ?>"
            style="<?= ($projet->champ_reference == 'remplacement_actuelle' ? 'background-color: lightcyan;' : '') ?> display: <?= ($admin->isColonneVisible('remplacement_actuelle') ? '' : 'none') ?>;text-align: center;"
            width="100">
			<?= $totaux['remplacement_actuelle'] ?>
        </td>
        <td class="<?= ($totaux['col_nouveaux'] < 0 ? 'isalert' : '') ?>"
            style="<?= ($projet->champ_reference == 'col_nouveaux' ? 'background-color: lightcyan;' : '') ?> display: <?= ($admin->isColonneVisible('col_nouveaux') ? '' : 'none') ?>;text-align: center;"
            width="100">
			<?= $totaux['col_nouveaux'] ?>
        </td>
        <td class="<?= ($totaux['developpement_actuelle'] < 0 ? 'isalert' : '') ?>"
            style="<?= ($projet->champ_reference == 'developpement_actuelle' ? 'background-color: lightcyan;' : '') ?> display: <?= ($admin->isColonneVisible('developpement_actuelle') ? '' : 'none') ?>;text-align: center;"
            width="100">
			<?= $totaux['developpement_actuelle'] ?>
        </td>
		<?
		if (isset($suiviBudgetaire)) {
			foreach ($suiviBudgetaire->getPortefeuilles(true) as $portefeuille) {
				?>
                <td class=""
                    style="<?= ($projet->champ_reference == 'qt_portefeuille_' . $portefeuille['portefeuille_numero'] ? 'background-color: lightcyan;' : '') ?> display: <?= ($admin->isColonneVisible('qt_portefeuille_' . $portefeuille['portefeuille_numero']) ? '' : 'none') ?>;text-align: center;"
                    width="75">
					<?= $totaux['qt_portefeuille_' . $portefeuille['portefeuille_numero']] ?>
                </td>
				<?
			}
		}
		?>
        <td
            style="display: <?= ($admin->isColonneVisible('col_deja_traite') ? '' : 'none') ?>;text-align: center;"
            width="75">
			<?= $totaux['deja_traite'] ?>
        </td>
		<?
		$class = $totaux['residuel_com'] == 0 ? '' : 'isalert';
		$class = $totaux['residuel_com'] < 0 ? 'isalertNegatif' : $class;
		?>
        <td style="display: <?= ($admin->isColonneVisible('col_residuel_com') ? '' : 'none') ?>;text-align: center;"
            width="100" class="residuel_com <?= $class ?>">
			<?= $totaux['residuel_com'] ?>
        </td>
        <td style="display: <?= ($admin->isColonneVisible('col_a_commander') ? '' : 'none') ?>;text-align: center;"
            width="200">
				<?= $totaux['commande'] ?>
        </td>
        <td style="display: <?= ($admin->isColonneVisible('cout_norme') ? '' : 'none') ?>;text-align: right; white-space: nowrap;"
            width="100">
			<?= dm_number_format($totaux['cout_norme'], 2, ',', ' ') ?>$
        </td>
        <td style="display: <?= ($admin->isColonneVisible('cout_taxe_net') ? '' : 'none') ?>;text-align: right; white-space: nowrap;"
            width="100">
			<?= dm_number_format($totaux['cout_taxe_net'], 2, ',', ' ') ?>$
        </td>
        <td style="display: <?= ($admin->isColonneVisible('cout_projete') ? '' : 'none') ?>;text-align: right; white-space: nowrap;"
            width="100">
			<?= dm_number_format($totaux['cout_projete'], 2, ',', ' ') ?>$
        </td>
        <td style="display: <?= ($admin->isColonneVisible('quantite_autorise') ? '' : 'none') ?>;text-align: center;"
            width="100">
			<?= $totaux['quantite_autorise'] ?>
        </td>
        <td style="display: <?= ($admin->isColonneVisible('prix_unitaire_autorise') ? '' : 'none') ?>;text-align: right; white-space: nowrap;"
            width="100">
			<?= (dm_strlen($totaux['prix_unitaire_autorise']) > 0 ? dm_number_format($totaux['prix_unitaire_autorise'], 2, ',', ' ') . '$' : '') ?>
        </td>
        <?
        for ($ii = 1; $ii <= 5; $ii++) {
	        ?>
            <td style="display: <?= ($admin->isColonneVisible('cout_supplementaire_' . $ii) ? '' : 'none') ?>;text-align: center;"
                width="100">
	            <?= dm_number_format($totaux['cout_supplementaire_' . $ii], 2, ',', ' ') ?>$
            </td>
	        <?
        }
        ?>
        <td style="display: <?= ($admin->isColonneVisible('colonne_supplementaire_detail_1') ? '' : 'none') ?>;text-align: center;"
            width="100">
			<?= $totaux['colonne_supplementaire_detail_1'] == -99999 ? 'N/A' : $totaux['colonne_supplementaire_detail_1'] ?>
        </td>
        <td style="display: <?= ($admin->isColonneVisible('colonne_supplementaire_detail_2') ? '' : 'none') ?>;text-align: center;"
            width="100">
			<?= $totaux['colonne_supplementaire_detail_2'] == -99999 ? 'N/A' : $totaux['colonne_supplementaire_detail_2'] ?>
        </td>
        <td style="display: <?= ($admin->isColonneVisible('colonne_supplementaire_detail_3') ? '' : 'none') ?>;text-align: center;"
            width="100">
			<?= $totaux['colonne_supplementaire_detail_3'] == -99999 ? 'N/A' : $totaux['colonne_supplementaire_detail_3'] ?>
        </td>
        <td style="display: <?= ($admin->isColonneVisible('colonne_supplementaire_detail_4') ? '' : 'none') ?>;text-align: center;"
            width="100">
			<?= $totaux['colonne_supplementaire_detail_4'] == -99999 ? 'N/A' : $totaux['colonne_supplementaire_detail_4'] ?>
        </td>
        <td style="display: <?= ($admin->isColonneVisible('colonne_supplementaire_detail_5') ? '' : 'none') ?>;text-align: center;"
            width="100">
			<?= $totaux['colonne_supplementaire_detail_5'] == -99999 ? 'N/A' : $totaux['colonne_supplementaire_detail_5'] ?>
        </td>
        <td style="display: <?= ($admin->isColonneVisible('colonne_supplementaire_detail_6') ? '' : 'none') ?>;text-align: center;"
            width="100">
			<?= $totaux['colonne_supplementaire_detail_6'] == -99999 ? 'N/A' : $totaux['colonne_supplementaire_detail_6'] ?>
        </td>
        <td style="display: <?= ($admin->isColonneVisible('colonne_supplementaire_detail_7') ? '' : 'none') ?>;text-align: center;"
            width="100">
			<?= $totaux['colonne_supplementaire_detail_7'] == -99999 ? 'N/A' : $totaux['colonne_supplementaire_detail_7'] ?>
        </td>
        <td style="display: <?= ($admin->isColonneVisible('colonne_supplementaire_detail_8') ? '' : 'none') ?>;text-align: center;"
            width="100">
			<?= $totaux['colonne_supplementaire_detail_8'] == -99999 ? 'N/A' : $totaux['colonne_supplementaire_detail_8'] ?>
        </td>
        <td style="display: <?= ($admin->isColonneVisible('colonne_supplementaire_detail_9') ? '' : 'none') ?>;text-align: center;"
            width="100">
			<?= $totaux['colonne_supplementaire_detail_9'] == -99999 ? 'N/A' : $totaux['colonne_supplementaire_detail_9'] ?>
        </td>
        <td style="display: <?= ($admin->isColonneVisible('colonne_supplementaire_detail_10') ? '' : 'none') ?>;text-align: center;"
            width="100">
			<?= $totaux['colonne_supplementaire_detail_10'] == -99999 ? 'N/A' : $totaux['colonne_supplementaire_detail_10'] ?>
        </td>
        <td style="display: <?= ($admin->isColonneVisible('col_a_distribuer') ? '' : 'none') ?>;text-align: center;"
            width="195">
			<?= $totaux['distribue'] ?>
        </td>
		<?
		$residuel_dist = $totaux['commande'] - $totaux['distribue'];
		$class = $residuel_dist == 0 ? '' : 'isalert';
		$class = $residuel_dist < 0 ? 'isalertNegatif' : $class;
		?>
        <td style="display: <?= ($admin->isColonneVisible('col_residuel_dist') ? '' : 'none') ?>;text-align: center;"
            width="100" class="residuel_dist <?= $class ?>">
			<?= $residuel_dist ?>
        </td>
        <td style="width: 20px;">
        </td>
    </tr>
    <?
    $iter_fiches = 0;
    foreach ($fiches as $fiche) {
        $groupe = $projet->groupes[$fiche['groupe_id']];
	    ?>
        <tr id="tr_gr_<?php echo $groupe->id . "_" . $fiche['fiche_id']; ?>">
            <td width="300" class="struct" fiche="<?= $fiche['fiche_id'] ?>"
                style="display: <?= ($admin->isColonneVisible('col_local') ? '' : 'none') ?>;padding: 0px 4px!important;">
			    <?
			    if (isset($fiches_infos[$fiche['fiche_id']])) {
				    $fiche_info = $fiches_infos[$fiche['fiche_id']];
				    $txt_supp = '';
				    if (dm_strlen($fiche_info->code_alternatif_2) > 0) {
					    $txt_supp = ' (' . $fiche_info->code_alternatif_2 . ')';
				    }

				    if (isset($fiche_info->batiment_niveau_element)) {
					    $txt_supp .= '<br />';
					    $bat_niv_split = dm_explode(' - ', $fiche_info->batiment_niveau_element);
					    foreach ($bat_niv_split as $iter => $bat_niv_spl) {
						    if ($iter > 0) {
							    $txt_supp .= ($iter > 1 ? ' - ' : '') . $bat_niv_spl;
						    }
					    }
				    }
				    print $fiche['local'] . $txt_supp;
			    } else {
				    print '<span style="color: red;">' . $fiche['local'] . txt(' (Supprimé)') . '</span>';
			    }
			    ?>
            </td>
            <td style="text-align: center; display: <?= ($admin->isColonneVisible('col_code_equipement') ? '' : 'none') ?>;">
		        <?
		        if (isset($formulaires_listes[0])) {
			        if (isset($groupe->code_equipement)) {
				        ?>
                        <a href="index.php?section=admin&amp;module=popup_listes_param&amp;fiche_id=3750&amp;formulaire_liste_id=<?=$formulaires_listes[0]->id?>&amp;liste_item_id=<?= $groupe->equipement_bio_medicaux_type_id ?>"
                           class="fancy" style="font-weight: bold; color: blue; text-decoration: underline">
					        <?= $groupe->code_equipement ?></a>
				        <?
			        } else {
				        print $groupe->produit_no;
			        }
		        } else {
			        ?>
			        <?= (isset($groupe->code_equipement) ? $groupe->code_equipement : $groupe->produit_no) ?>
			        <?
		        }
		        ?>
            </td><td style="display: <?= ($admin->isColonneVisible('col_description_equipement') ? '' : 'none') ?>;">
		        <?= (isset($groupe->description_equipement) ? $groupe->description_equipement : $groupe->produit_description) ?>
            </td>
            <td style="display: <?= ($admin->isColonneVisible('col_requis_j1') ? '' : 'none') ?>;text-align: center; padding: 0px 4px!important;"
                width="75">
			    <?= $fiche['requise_init'] ?>
            </td>
            <td style="display: <?= ($admin->isColonneVisible('col_existante_j1') ? '' : 'none') ?>;text-align: center; padding: 0px 4px!important;"
                width="75">
			    <?= $fiche['existante_init'] ?>
            </td>
            <td style="display: <?= ($admin->isColonneVisible('col_conserve_j1') ? '' : 'none') ?>;text-align: center; padding: 0px 4px!important;"
                width="75">
			    <?= $fiche['demenager_init'] ?>
            </td>
            <td class="<?= ($fiche['requise_init'] <> $fiche['requise_actuelle'] ? 'isalert' : '') ?>"
                style="<?= ($projet->champ_reference == 'demenager_actuelle' ? 'background-color: lightcyan;' : '') ?> display: <?= ($admin->isColonneVisible('requise_actuelle') ? '' : 'none') ?>;text-align: center;"
                width="75">
			    <?= $fiche['requise_actuelle'] ?>
            </td>
            <td class=""
                style="<?= ($projet->champ_reference == 'demenager_actuelle' ? 'background-color: lightcyan;' : '') ?> display: <?= ($admin->isColonneVisible('existante_actuelle') ? '' : 'none') ?>;text-align: center;"
                width="75">
			    <?= $fiche['existante_actuelle'] ?>
            </td>
            <td class="<?= ($fiche['demenager_init'] <> $fiche['demenager_actuelle'] ? 'isalert' : '') ?>"
                style="<?= ($projet->champ_reference == 'demenager_actuelle' ? 'background-color: lightcyan;' : '') ?> display: <?= ($admin->isColonneVisible('demenager_actuelle') ? '' : 'none') ?>;text-align: center;"
                width="75">
			    <?= $fiche['demenager_actuelle'] ?>
            </td>
            <td class="<?= ($fiche['remplacement_actuelle'] < 0 ? 'isalert' : '') ?>"
                style="<?= ($projet->champ_reference == 'remplacement_actuelle' ? 'background-color: lightcyan;' : '') ?> display: <?= ($admin->isColonneVisible('remplacement_actuelle') ? '' : 'none') ?>;text-align: center;"
                width="100">
			    <?= $fiche['remplacement_actuelle'] ?>
            </td>
            <td class="<?= ($fiche['col_nouveaux'] < 0 ? 'isalert' : '') ?>"
                style="<?= ($projet->champ_reference == 'col_nouveaux' ? 'background-color: lightcyan;' : '') ?> display: <?= ($admin->isColonneVisible('col_nouveaux') ? '' : 'none') ?>;text-align: center;"
                width="100">
			    <?= $fiche['col_nouveaux'] ?>
            </td>
            <td class="<?= ($fiche['developpement_actuelle'] < 0 ? 'isalert' : '') ?>"
                style="<?= ($projet->champ_reference == 'developpement_actuelle' ? 'background-color: lightcyan;' : '') ?> display: <?= ($admin->isColonneVisible('developpement_actuelle') ? '' : 'none') ?>;text-align: center;"
                width="100">
			    <?= $fiche['developpement_actuelle'] ?>
            </td>
		    <?
		    if (isset($suiviBudgetaire)) {
			    foreach ($suiviBudgetaire->getPortefeuilles(true) as $portefeuille) {
				    ?>
                    <td class=""
                        style="<?= ($projet->champ_reference == 'qt_portefeuille_' . $portefeuille['portefeuille_numero'] ? 'background-color: lightcyan;' : '') ?> display: <?= ($admin->isColonneVisible('qt_portefeuille_' . $portefeuille['portefeuille_numero']) ? '' : 'none') ?>;text-align: center;"
                        width="75">
					    <?= $fiche['qt_portefeuille_' . $portefeuille['portefeuille_numero']] ?>
                    </td>
				    <?
			    }
		    }
		    ?>
            <td id="deja_<?= $groupe->id . 'x' . $fiche['fiche_id'] ?>"
                style="display: <?= ($admin->isColonneVisible('col_deja_traite') ? '' : 'none') ?>;text-align: center;"
                width="75"
                class="deja_<?= $projet->id ?>_<?= $groupe->equipement_bio_medicaux_type_id ?>_<?= $fiche['fiche_id'] ?>">
			    <?= $fiche['deja_traite'] ?>
            </td>
		    <?
		    $class = $fiche['residuel_com'] == 0 ? '' : 'isalert';
		    $class = $fiche['residuel_com'] < 0 ? 'isalertNegatif' : $class;
		    ?>
            <td style="display: <?= ($admin->isColonneVisible('col_residuel_com') ? '' : 'none') ?>;text-align: center;"
                width="100" class="residuel_com <?= $class ?>">
			    <?= $fiche['residuel_com'] ?>
            </td>
            <td style="display: <?= ($admin->isColonneVisible('col_a_commander') ? '' : 'none') ?>;text-align: center;"
                width="200">
			    <?php if (havePermission(array('administration_quantite_financer'))) { ?>
                    <select id="quantite_groupe_<?= $fiche['fiche_id'] ?>_<?= $groupe->id ?>"
                            data-parent_id="groupe_<?= $groupe->id ?>"
                            data-equipement_id="<?= $groupe->equipement_bio_medicaux_type_id ?>"
                            class="quantite_groupe quantite_groupe_<?= $groupe->equipement_bio_medicaux_type_id ?>_<?= $fiche['fiche_id'] ?>"
                            style="width: 50px!important;" data-fiche_id="<?= $fiche['fiche_id'] ?>"
                            data-financement_projet_groupe_equipement_id="<?= $groupe->id ?>"
                            data-quantite_a_traiter="<?= $fiche['a_traiter'] ?>"
                            data-projet_numero="<?= $projet->numero ?>" data-projet_id="<?= $projet->id ?>">
					    <?php
					    $selected = false;
					    for ($quantite = 0; $quantite <= $fiche['a_traiter']; $quantite++) {
						    $sel = '';
						    if ($quantite == $fiche['commande']) {
							    $sel = ' selected="selected"';
							    $selected = true;
						    }
						    print '<option value="' . $quantite . '"' . $sel . '>' . $quantite . '</option>';
					    }
					    if (!$selected && dm_strlen($fiche['commande']) > 0) {
						    print '<option value="' . $fiche['commande'] . '" selected="selected">' . $fiche['commande'] . '</option>';
					    }
					    ?>
                    </select>
			    <?php } else {
				    echo $fiche['commande'];
			    } ?>
            </td>
            <td style="display: <?= ($admin->isColonneVisible('cout_norme') ? '' : 'none') ?>;text-align: right; white-space: nowrap;"
                width="100">
			    <?= dm_number_format(((int)$fiche['commande'] * $groupe->cout_norme), 2, ',', ' ') ?>$
            </td>
            <td style="display: <?= ($admin->isColonneVisible('cout_taxe_net') ? '' : 'none') ?>;text-align: right; white-space: nowrap;"
                width="100">
			    <?= dm_number_format($groupe->cout_taxe_net, 2, ',', ' ') ?>$
            </td>
            <td style="display: <?= ($admin->isColonneVisible('cout_projete') ? '' : 'none') ?>;text-align: right; white-space: nowrap;"
                width="100">
			    <?= dm_number_format($groupe->cout_projete, 2, ',', ' ') ?>$
            </td>
            <td style="display: <?= ($admin->isColonneVisible('quantite_autorise') ? '' : 'none') ?>;text-align: center;"
                width="100">
			    <?= $fiche['quantite_autorise'] ?>
            </td>
            <td style="display: <?= ($admin->isColonneVisible('prix_unitaire_autorise') ? '' : 'none') ?>;text-align: right; white-space: nowrap;"
                width="100">
			    <?= (dm_strlen($fiche['prix_unitaire_autorise']) > 0 ? dm_number_format($fiche['prix_unitaire_autorise'], 2, ',', ' ') . '$' : '') ?>
            </td>
	        <?
	        for ($ii = 1; $ii <= 5; $ii++) {
		        ?>
                <td style="display: <?= ($admin->isColonneVisible('cout_supplementaire_' . $ii) ? '' : 'none') ?>;text-align: center;"
                    width="100">
	                <?= dm_number_format($fiche['cout_supplementaire_' . $ii], 2, ',', ' ') ?>$
                </td>
		        <?
	        }
	        ?>
            <td style="display: <?= ($admin->isColonneVisible('colonne_supplementaire_detail_1') ? '' : 'none') ?>;text-align: center;"
                width="100">
	            <?= $fiche['colonne_supplementaire_detail_1'] ?>
            </td>
            <td style="display: <?= ($admin->isColonneVisible('colonne_supplementaire_detail_2') ? '' : 'none') ?>;text-align: center;"
                width="100">
			    <?= $fiche['colonne_supplementaire_detail_2'] ?>
            </td>
            <td style="display: <?= ($admin->isColonneVisible('colonne_supplementaire_detail_3') ? '' : 'none') ?>;text-align: center;"
                width="100">
			    <?= $fiche['colonne_supplementaire_detail_3'] ?>
            </td>
            <td style="display: <?= ($admin->isColonneVisible('colonne_supplementaire_detail_4') ? '' : 'none') ?>;text-align: center;"
                width="100">
			    <?= $fiche['colonne_supplementaire_detail_4'] ?>
            </td>
            <td style="display: <?= ($admin->isColonneVisible('colonne_supplementaire_detail_5') ? '' : 'none') ?>;text-align: center;"
                width="100">
			    <?= $fiche['colonne_supplementaire_detail_5'] ?>
            </td>
            <td style="display: <?= ($admin->isColonneVisible('colonne_supplementaire_detail_6') ? '' : 'none') ?>;text-align: center;"
                width="100">
			    <?= $fiche['colonne_supplementaire_detail_6'] ?>
            </td>
            <td style="display: <?= ($admin->isColonneVisible('colonne_supplementaire_detail_7') ? '' : 'none') ?>;text-align: center;"
                width="100">
			    <?= $fiche['colonne_supplementaire_detail_7'] ?>
            </td>
            <td style="display: <?= ($admin->isColonneVisible('colonne_supplementaire_detail_8') ? '' : 'none') ?>;text-align: center;"
                width="100">
			    <?= $fiche['colonne_supplementaire_detail_8'] ?>
            </td>
            <td style="display: <?= ($admin->isColonneVisible('colonne_supplementaire_detail_9') ? '' : 'none') ?>;text-align: center;"
                width="100">
			    <?= $fiche['colonne_supplementaire_detail_9'] ?>
            </td>
            <td style="display: <?= ($admin->isColonneVisible('colonne_supplementaire_detail_10') ? '' : 'none') ?>;text-align: center;"
                width="100">
			    <?= $fiche['colonne_supplementaire_detail_10'] ?>
            </td>
            <td style="display: <?= ($admin->isColonneVisible('col_a_distribuer') ? '' : 'none') ?>;text-align: center;"
                width="195">
			    <?php if (havePermission(array('administration_quantite_approuvees'))) { ?>
                    <select id="distribuee_groupe_<?= $fiche['fiche_id'] ?>_<?= $groupe->id ?>"
                            class="quantite_distribuee" style="width: 50px!important;"
                            data-parent_id="groupe_<?= $groupe->id ?>" data-fiche_id="<?= $fiche['fiche_id'] ?>"
                            data-financement_projet_groupe_equipement_id="<?= $groupe->id ?>"
                            data-quantite_a_commander="<?= $fiche['commande'] ?>"
                            data-projet_numero="<?= $projet->numero ?>" data-projet_id="<?= $projet->id ?>">
					    <?php
					    $selected = false;
					    for ($quantite = 0; $quantite <= $fiche['commande']; $quantite++) {
						    $sel = '';
						    if ($quantite == $fiche['distribue']) {
							    $sel = ' selected="selected"';
							    $selected = true;
						    }
						    print '<option value="' . $quantite . '"' . $sel . '>' . $quantite . '</option>';
					    }
					    if (!$selected && dm_strlen($fiche['distribue']) > 0) {
						    print '<option value="' . $fiche['distribue'] . '" selected="selected">' . $fiche['distribue'] . '</option>';
					    }
					    ?>
                    </select>
			    <?php } else {
				    echo $fiche['distribue'];
			    } ?>
            </td>
		    <?
		    $residuel_dist = $fiche['commande'] - $fiche['distribue'];
		    $class = $residuel_dist == 0 ? '' : 'isalert';
		    $class = $residuel_dist < 0 ? 'isalertNegatif' : $class;
		    ?>
            <td style="display: <?= ($admin->isColonneVisible('col_residuel_dist') ? '' : 'none') ?>;text-align: center;"
                width="100" class="residuel_dist <?= $class ?>">
			    <?= $residuel_dist ?>
            </td>
            <td style="width: 20px;">
                <a href="index.php?section=fiche&module=form_logs&fiche_id=<?php echo $fiche['fiche_id']; ?>&formulaire=EquipementsBioMedicaux"
                   class="fancy" style="position: relative; top: 2px;"
                   title="<?= txt('Consulter l\'historique des modifications') ?>">
                    <img src="css/images/icons/dark/clock.png"/></a>
            </td>
        </tr>
	    <?
        if ($iter_fiches++ > 1000) {
            break;
        }
    }
    ?>
    </table>

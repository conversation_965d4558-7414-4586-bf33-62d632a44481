<?php
if ( isset($_GET['projet_id']) && isset($_GET['projet_numero']) && intval($_GET['projet_id']) > 0 ) {
	$projet_id = intval($_GET['projet_id']);
	$projet_numero = dm_str_replace("'", "''", $_GET['projet_numero']);
	$usager = getUsager();
	$projet = new FinancementsProjets($projet_id);
	$projet_listes = FinancementsProjets::getListeParProjet( $projet_id );
	$open_groupe_id = isset($_GET['open_groupe_id']) ? intval($_GET['open_groupe_id']) : 0;
//	print_r($projet['groupes'][0]);
}
else
	exit;

$sql = "select * from suivis_budgetaires where projet_id = ?";
$suivi = $db->getRow($sql, [getProjetId()]);
if (isset($suivi['id'])) {
	$suiviBudgetaire = new SuivisBudgetaires($suivi['id']);
}

$admin = FinancementsProjetsAdministrations::getAdminForProjet();
?>
<table style="width: 100%!important;float: right;" style="">
    <tr><th width="75" style="display: <?= ($admin->isColonneVisible('col_bon_commande') ? '' : 'none') ?>;">
			<?= $admin->getLabelForColonne('col_bon_commande') ?>
        </th><th width="75" style="display: <?= ($admin->isColonneVisible('col_numero') ? '' : 'none') ?>;">
		    <?= $admin->getLabelForColonne('col_numero') ?>
        </th><th width="170" style="display: <?= ($admin->isColonneVisible('col_description') ? '' : 'none') ?>;">
		    <?= $admin->getLabelForColonne('col_description') ?>
        </th><th width="140" style="text-align: center; display: <?= ($admin->isColonneVisible('col_code_equipement') ? '' : 'none') ?>;">
		    <?= $admin->getLabelForColonne('col_code_equipement') ?>
        </th><th width="110" style="text-align: center; display: <?= ($admin->isColonneVisible('col_description_equipement') ? '' : 'none') ?>;">
		    <?= $admin->getLabelForColonne('col_description_equipement') ?>
        </th><th width="70" style="display: <?= ($admin->isColonneVisible('col_ts_ajout_groupe') ? '' : 'none') ?>;">
		    <?= $admin->getLabelForColonne('col_ts_ajout_groupe') ?>
        </th>
        <th width="100" style=" text-align: center;">
            <?php if (havePermission(array('creation_groupe_equipements')) && !$projet->isLocked()) { ?>
            &nbsp;<a href="index.php?section=admin&module=edit&table=financements_projets_groupes_equipements&type=client&id=0&financement_projet_id=<?=$projet_id?>" class="fancy">
                <img src="css/images/icons/dark/plus.png" /></a>
            <?php } ?>
        </th>
    </tr>
	<?php

	foreach ($projet_listes['groupes'] as $groupe)
	{
	?>
    <tr id="tr_<?= $groupe->id ?>" class="groupes" data-groupe_id="<?= $groupe->id ?>"
        data-projet_id="<?= $projet_id ?>" data-equipement_id="<?= $groupe->equipement_bio_medicaux_type_id ?>" style="<?= ($groupe->isDonneesDenormalisees() ? 'background-color: #f2f2f2;' : '') ?>">
        <td style="display: none;">
			<?php echo '<pre>' . print_r($groupe, TRUE) . '</pre>'; ?>
        </td>
        <td style="display: <?= ($admin->isColonneVisible('col_bon_commande') ? '' : 'none') ?>;">
            <a href="javascript: void(0);" style="font-weight: bold; color: blue; text-decoration: underline"
               onclick="toggleCom(<?= $projet_id ?>, '<?= $groupe->bon_commande ?>', <?= $groupe->id ?>)"><?= $groupe->bon_commande ?></a>
        </td>
        <td style="display: <?= ($admin->isColonneVisible('col_numero') ? '' : 'none') ?>;">
            <a href="javascript: void(0);" style="font-weight: bold; color: blue; text-decoration: underline"
               onclick="toggleGr(<?= $groupe->id ?>)"><?= $groupe->numero ?></a>
        </td>
        <td style="display: <?= ($admin->isColonneVisible('col_description') ? '' : 'none') ?>;">
			<?= $groupe->description ?>
        </td>
        <td style="text-align: center; display: <?= ($admin->isColonneVisible('col_code_equipement') ? '' : 'none') ?>;">
			<?
			$formulaires_listes = EquipementsBioMedicauxTypes::getFormulairesListes('EquipementsBioMedicauxTypes');
			if (isset($formulaires_listes[0])) {
			    if (isset($groupe->code_equipement)) {
				    ?>
                    <a href="index.php?section=admin&amp;module=popup_listes_param&amp;fiche_id=3750&amp;formulaire_liste_id=<?=$formulaires_listes[0]->id?>&amp;liste_item_id=<?= $groupe->equipement_bio_medicaux_type_id ?>"
                       class="fancy" style="font-weight: bold; color: blue; text-decoration: underline">
					    <?= $groupe->code_equipement ?></a>
				    <?
                } else {
			        print $groupe->produit_no;
                }
			} else {
			?>
				<?= (isset($groupe->code_equipement) ? $groupe->code_equipement : $groupe->produit_no) ?>
			<?
			}
                ?>
            </td><td style="display: <?= ($admin->isColonneVisible('col_description_equipement') ? '' : 'none') ?>;">
				<?= (isset($groupe->description_equipement) ? $groupe->description_equipement : $groupe->produit_description) ?>
            </td><td style="display: <?= ($admin->isColonneVisible('col_ts_ajout_groupe') ? '' : 'none') ?>;">
                <?=dm_substr($groupe->ts_ajout, 0, 10)?>
            </td>
                <td style="height: 26px; text-align: center; min-height: 28px;">
                    <?
                    if ($groupe->isDonneesDenormalisees()) {
	                    ?>
                        <a href="index.php?section=admin&module=edit&table=financements_projets_groupes_equipements&type=client&id=<?=$groupe->id?>&force_readonly=1" class="fancy" title="<?=txt('Visualiser le groupe')?>">
                            <img src="css/images/icons/dark/magnifying_glass.png"></a>
	                    <?
                    } else if(!$groupe->isLocked()) {
                        if (havePermission(array('edition_groupe_equipements'))) {
                    ?>
                        <a href="index.php?section=admin&module=edit&table=financements_projets_groupes_equipements&type=client&id=<?=$groupe->id?>&financement_projet_id=<?=$projet_id?>&page=1" class="fancy">
                            <img src="css/images/icons/dark/create_write.png"></a>
                            <a href="javascript: void(0);" class="duplicate" idElement="<?= $groupe->id ?>" IdProject="<?=$projet_id?>" tableName="financements_projets_groupes_equipements">
                                <img src="css/images/icons/dark/duplicate.png">
                            </a>
                        <?php }
                        else
                        {
                            ?>
                            <a href="index.php?section=admin&module=edit&table=financements_projets_groupes_equipements&type=client&id=<?=$groupe->id?>&force_readonly=1" class="fancy" title="<?=txt('Visualiser le groupe')?>">
                                <img src="css/images/icons/dark/magnifying_glass.png"></a>
                            <?
                        }
                        if (havePermission(array('effacement_groupe_equipements'))) {
                            ?>
                            <a href="javascript: void(0);" class="dle_btn dle_delete_generique"
                               tableName="financements_projets_groupes_equipements"
                               idElement="<?= $groupe->id ?>" data-projet_numero="<?=$projet_numero?>" data-projet_id="<?=$projet_id?>">
                                <img src="css/images/icons/dark/trashcan.png"/></a>
                            <?
                        }
                        if (havePermission(array('verrouillage_projets_financement'))) { ?>

                            <a href="index.php?section=financement&module=projets&lock_groupe=1&id=<?= $groupe->id ?>&page=1&projet_id=<?=$projet_id?>"
                               title="<?= txt('Vérrouiller le groupe') ?>">
                                <img src="css/images/icons/dark/unlocked.png"/></a>

                            <?
                        }
                    }
                    else
                    {
                        ?>
                        <a href="index.php?section=admin&module=edit&table=financements_projets_groupes_equipements&type=client&id=<?=$groupe->id?>&force_readonly=1" class="fancy" title="<?=txt('Visualiser le groupe')?>">
                            <img src="css/images/icons/dark/magnifying_glass.png"></a>
                        <?
                        if (havePermission(array('deverrouillage_projets_financements')) && $groupe->isLocked()) { ?>
                            <a href="index.php?section=financement&module=projets&lock_groupe=0&id=<?=$groupe->id?>&projet_id=<?=$projet_id?>" title="<?=txt('Dévérrouiller le groupe')?>">
                                <img src="css/images/icons/dark/locked.png" /></a>
                        <?php }
                        else{
                            ?>
                            <img src="css/images/icons/dark/locked.png" />
                            <?
                        }
                        if (havePermission(array('edition_groupe_equipements'))) { ?>
                            <a href="javascript: void(0);" class="duplicate" idElement="<?= $groupe->id ?>" IdProject="<?=$projet_id?>" tableName="financements_projets_groupes_equipements">
                                <img src="css/images/icons/dark/duplicate.png">
                            </a>
                        <?php }
                    }
                    ?>
                </td>
        </tr>
        <tr>
            <td colspan="20" style="padding: 0px!important; background-color: #aaa!important;">
                <div class="gr" id="groupe_<?=$groupe->id?>" data-groupe_id="<?=$groupe->id?>" style=" display: <?=($groupe->id == $open_groupe_id ? '' : 'none')?>; margin: 10px; background-color: grey!important;">
                </div>
                <div class="co" id="com_<?= $groupe->id ?>" style=" display: none; margin: 10px; background-color: #aaa!important;"></div>
            </td>
        </tr>
		<?
	}
	?>
</table>
<br style="clear: both;" />

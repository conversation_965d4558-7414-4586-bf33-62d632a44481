<?php
if ( isset($_GET['projet_id']) && intval($_GET['projet_id']) > 0 ) {
	$projet_id = intval($_GET['projet_id']);
	$page = intval($_GET['page']);
	chdir($_SERVER['DOCUMENT_ROOT']);
	include 'config/inc.php';
	$usager = getUsager();
	$liste_tmp = FinancementsProjets::ganttGetListe($projet_id);
	if (isset($liste_tmp[$projet_id])) {
		echo '{"data":[';
		FinancementsProjets::ganttShowProjectGlobal($liste_tmp[$projet_id]);
		echo ',';
		FinancementsProjets::ganttProjetJSON($liste_tmp[$projet_id]);
		echo ']}';
	}
}
else
	exit;
?>
<?
require_once __DIR__ . '/../../vendor/autoload.php';

use PhpOffice\PhpSpreadsheet\Cell\Coordinate;
use PhpOffice\PhpSpreadsheet\IOFactory;

ini_set("memory_limit","10000M");
ini_set('max_execution_time', 30000);

$pivots = ['id', 'code_equipement_A', 'fiche_code'];
$admin = FinancementsProjetsAdministrations::getAdminForProjet();

$etape = 'valider';

if(isset($_POST['synchroniser'])) {
	$sql = "select concat(f.code, '-', ebmt.nom), ebm.id 
            from equipements_bio_medicaux ebm
            join fiches f on ebm.fiche_id = f.id
            join equipements_bio_medicaux_types ebmt on ebm.equipement_bio_medicaux_type_id = ebmt.id
            where ouvrage_id = ?";
	$assocFiches = $db->getAssoc($sql, [getOuvrageId()]);

    $sql = "select distinct ebm.id 
            from equipements_bio_medicaux ebm
            join fiches f on ebm.fiche_id = f.id
            join equipements_bio_medicaux_types ebmt on ebm.equipement_bio_medicaux_type_id = ebmt.id
            where ouvrage_id = ?";
    $listeIds = $db->getCol($sql, [getOuvrageId()]);

	$all_cols = $_SESSION['synchro_data']['all_cols'];
	$champs = $_SESSION['synchro_data']['champs'];
	$lignes = $_SESSION['synchro_data']['lignes_a_importer'];

    $pivotsOk = true;
    if (dm_strlen($_POST["alias_id"]) == 0) {
	    if (dm_strlen($_POST["alias_code_equipement_A"]) == 0 || dm_strlen($_POST["alias_fiche_code"]) == 0) {
		    $pivotsOk = false;
	    }
    }

	if (!$pivotsOk) {
		setErreur(txt('Le pivot est obligatoire. (ID seulement ou la combinaison Code le local et d\'équipement)'));
	}

	$idNotExists = [];
	$idNotRempli = [];
	$ficheNotExists = [];
	$ficheNotRempli = [];
	foreach ($lignes as $row => $champs) {
		if (dm_strlen($_POST["alias_id"]) > 0) {
			if (dm_strlen(dm_trim($champs[$_POST['alias_id']])) == 0) {
				$idNotRempli[] = $row;
			} else if (!in_array($champs[$_POST['alias_id']], $assocFiches)) {
				$idNotExists[] = $champs[$_POST['alias_id']];
			}
		}

		if (dm_strlen($_POST["alias_fiche_code"]) > 0 && dm_strlen($_POST["alias_code_equipement_A"]) > 0) {
			if (dm_strlen(dm_trim($champs[$_POST['alias_fiche_code']])) == 0 || dm_strlen(dm_trim($champs[$_POST['alias_code_equipement_A']])) == 0) {
				$ficheNotRempli[] = $row;
			} else if (!isset($assocFiches[$champs[$_POST['alias_fiche_code']] . '-' . $champs[$_POST['alias_code_equipement_A']]])) {
				$ficheNotExists[] = $champs[$_POST['alias_fiche_code']];
			}
        }
	}

	if (count($idNotRempli) > 0) {
		setErreur(txt('Le id est obligatoire. Voici les lignes pour lesquelles nous n\'avons pas l\'information: ') . dm_implode(', ', $idNotRempli));
	}

	if (count($ficheNotRempli) > 0) {
		setErreur(txt('Le local et l\'équipement sont obligatoires. Voici les lignes pour lesquelles nous n\'avons pas l\'information: ') . dm_implode(', ', $ficheNotRempli));
	}

	if (nbErreurs() > 0) {
		$etape = 'synchroniser';
	} else {
		$champs_to_import = array();
		foreach ($all_cols as $key => $champ) {
			if (isset($_POST['alias_' . $key]) && dm_strlen($_POST['alias_' . $key]) > 0) {
				$champs_to_import[$_POST['alias_' . $key]] = $champ;
			}
		}

        $lignes_non_importables = [];
		foreach ($lignes as $row => $champs) {
			$id = 0;
            if (isset($champs[$_POST['alias_id']]) && (int)$champs[$_POST['alias_id']] > 0 && in_array((int)$champs[$_POST['alias_id']], $listeIds)) {
	            $id = $champs[$_POST['alias_id']];
            } else if (isset($champs[$_POST['alias_fiche_code']]) && isset($champs[$_POST['alias_code_equipement_A']]) && isset($assocFiches[$champs[$_POST['alias_fiche_code']] . '-' . $champs[$_POST['alias_code_equipement_A']]])) {
                $id = $assocFiches[$champs[$_POST['alias_fiche_code']] . '-' . $champs[$_POST['alias_code_equipement_A']]]['id'];
            } else {
	            $lignes_non_importables[] = $champs;
            }

            if ($id > 0) {
	            foreach ($champs_to_import as $alias => $champ) {
		            $new_valeur = isset($champs[$alias]) ? $champs[$alias] : '';
		            if ($champ['format'] == 'int') {
			            $new_valeur = (int)$new_valeur;
		            } else if ($champ['format'] == 'float') {
			            $new_valeur = (float)$new_valeur;
		            }

		            $setNull = true;
		            if (dm_strlen(strval($new_valeur)) > 0) {
			            $setNull = false;
		            }

		            $col_bd = $champ['col_bd'] ?? $champ['nom'];
		            if (!$setNull) {
                        if (in_array($col_bd, ['analyse_source1_statut_interne_id', 'analyse_source1_statut_externe_id', 'analyse_source2_statut_interne_id',
                                'analyse_source3_statut_interne_id', 'analyse_source4_statut_interne_id', 'analyse_source5_statut_interne_id'])) {

                            $tableAssoc = [
                                'analyse_source1_statut_interne_id' => 'financements_statuts_internes_types',
                                'analyse_source1_statut_externe_id' => 'financements_statuts_externes_types',
                                'analyse_source2_statut_interne_id' => 'financements_statuts_internes_types',
                                'analyse_source3_statut_interne_id' => 'financements_statuts_internes_types',
                                'analyse_source4_statut_interne_id' => 'financements_statuts_internes_types',
                                'analyse_source5_statut_interne_id' => 'financements_statuts_internes_types',
                            ];

                            $sql = "select id from " . $tableAssoc[$col_bd] . " where nom = ?";
                            $idTmp = $db->getOne($sql, [$new_valeur]);
                            if ((int)$idTmp > 0) {
                                $new_valeur = (int)$idTmp;
                            } else {
                                $sql = "insert into " . $tableAssoc[$col_bd] . " (nom) values (?)";
                                $db->query($sql, [$new_valeur]);
                                $new_valeur = $db->lastInsertId();

                                $sql = "insert into " . $tableAssoc[$col_bd] . "_projets (liste_item_id, projet_id) values (?, ?)";
                                $db->query($sql, [$new_valeur, getProjetId()]);
                            }
                        } else if ($col_bd == 'financement_complete') {
                            $new_valeur = convertToIntCheckboxValue($new_valeur);
                        }
		            }

		            if ($setNull) {
			            $sql = "update equipements_bio_medicaux set `" . db::tableColonneValide($col_bd) . "` = null where id = ?";
			            $db->query($sql, [$id]);
		            } else {
			            $sql = "update equipements_bio_medicaux set `" . db::tableColonneValide($col_bd) . "` = ? where id = ?";
			            $db->query($sql, [$new_valeur, $id]);
		            }
	            }
            }
			unset($lignes[$row]);
		}
		if (count($lignes_non_importables) > 0) {
			$data_titre = [];
            foreach ($_SESSION['synchro_data']['champs'] as $champ) {
	            $data_titre[] = $champ;
            }

			$sql = "insert into financements_imports_erreurs (data_titre, projet_id) values (?, ?)";
			$db->query($sql, [json_encode($data_titre), getProjetId()]);
			$id = $db->lastInsertId();

            foreach ($lignes_non_importables as $data_ligne) {
                $data = [];
	            foreach ($_SESSION['synchro_data']['champs'] as $champ) {
		            $data[] = $data_ligne[$champ];
	            }
	            $sql = "insert into financements_imports_erreurs_lignes (financement_import_erreur_id, data_ligne) values (?, ?)";
	            $db->query($sql, [$id, json_encode($data)]);
            }

            unset($all_cols);
			setErreur(txt('Attention! Le fichier que vous venez d’importer comportait des lignes de données pour lesquelles aucune correspondance « Id / Équipement / Local » n’a été trouvée dans DocMatic. Les lignes de données non importées ont été cumulées dans un registre permettant de générer un rapport Excel sur demande. Vous pouvez télécharger ce rapport ou tout autre rapport d’évènement antérieur en cliquant sur le lien qui vous intéresse dans la liste ci-dessous.', false));
        }

        unset($_SESSION['synchro_data']);
        $db->commit();

		if (count($lignes_non_importables) == 0) {
            ?>
            <script>
                parent.window.location.reload();
                parent.$.fancybox.close();
            </script>
            <?
            die();
		}
	}
} else if(isset($_POST['valider'])) {
	$_SESSION['synchro_data']['lignes_a_importer'] = array();
	$alias = array();
	$champs = array();

	if (!isset($_FILES['fichier']) || !is_uploaded_file($_FILES['fichier']['tmp_name'])) {
		setErreur(txt('Vous devez sélectionner le fichier à importer.'));
	} else {
		$all_cols = FinancementsProjetsAdministrationsUnifiees::getChampsColonnesImportables();

		$all_alias = array();
		foreach ($all_cols as $key => $champ) {
			$all_alias[$champ['label']] = $champ['label'];
		}

		$spreadsheet = IOFactory::load($_FILES['fichier']['tmp_name']);

		foreach ($spreadsheet->getWorksheetIterator() as $iter_sheet => $worksheet) {
			if ($iter_sheet > 0) {
				break;
			}
			$highestRow = $worksheet->getHighestDataRow(); // e.g. 10
			$highestColumn = $worksheet->getHighestDataColumn(); // e.g 'F'
			$highestColumnIndex = Coordinate::columnIndexFromString($highestColumn);
			$nrColumns = ord($highestColumn) - 64;

			$row_titre = 1;
			for ($row = 1; $row <= $highestRow; ++$row) {
				for ($col = 1; $col <= $highestColumnIndex; ++$col) {
					$cell = $worksheet->getCellByColumnAndRow($col, $row);
					$val = sanitizeString((string)$cell->getCalculatedValue());

					$val = dm_trim($val) == '.' ? '' : $val;
					if ($row == $row_titre && dm_strlen($val) > 0) {
						$val = dm_trim(dm_preg_replace("/\r|\n/", "", $val));
						$alias[$col] = $val;
						$champs[$val] = $val;
					} else if ($row > $row_titre && isset($alias[$col])) {
						$_SESSION['synchro_data']['lignes_a_importer'][$row][$alias[$col]] = $val;
					}
				}
			}
		}

		$spreadsheet->disconnectWorksheets();
		unset($spreadsheet);

		$_SESSION['synchro_data']['champs'] = $champs;

		$_SESSION['synchro_data']['all_cols'] = $all_cols;

		$_SESSION['synchro_data']['type_import'] = 'excel';
	}

	$etape = 'synchroniser';
}
$noHeader = true;
include('config/header.inc.php');
?>
<script>
	var $document = $(document);
	$document.ready(function(){

		$document.on('change', 'select.uniques', function(){

			var valeur = this.value;
			var id_select = this.id;

			$document.find('select.uniques').each(function(){

				if(this.value == valeur && this.id != id_select)
				{
					this.value = '';
				}

			});

		});

		$document.on('click', '.download', function () {
			$.startObjectLoader($('body'), 'export_loader');
			fetch(`index.php?action=download_excel_erreurs&id=${ $(this).data('id') }`, {
				method: 'GET'
			})
                .then(response => response.blob())
                .then(blob => {
                    var url = window.URL.createObjectURL(blob);
                    var a = document.createElement('a');
                    a.href = url;
                    a.download = "Erreurs Import Financement.xlsx";
                    document.body.appendChild(a); // we need to append the element to the dom -> otherwise it will not work in firefox
                    a.click();
                    a.remove();  //afterwards we remove the element again
                    $.stopObjectLoader('export_loader');
                });
		});

		$document.on('click', '.delete', function () {
			$.get(`index.php?action=delete_excel_erreurs&id=${ $(this).data('id') }`);
			$(this).parent().remove();
        });

	});
</script>
<style>
	.blocsFiche, #fiche table, #fiche tr, #fiche td, #fiche th, .ui-dialog table, .ui-dialog tr, .ui-dialog td, .ui-dialog th {
		background: none!important;
	}
	.blocsFicheTitre {
		margin-top: 0px!important;
	}
	.readonly {
		text-align: center;
	}
</style>

<div id="popupContent">
	<form id="form" autocomplete="off" method="post" action="index.php?section=financement&module=import" enctype="multipart/form-data">
		<label><?=txt('Mise-à-jour des données')?></label>
		<fieldset style="min-height: 175px; padding: 10px 0 0 50px;">
			<?
			if (!isset($all_cols)) {
				?>
				<div id="editables">
					<?
					erreurs();
					?>
					<section style="margin: 10px 0 10px 0;">
						<span for="fichier"><?php print txt('Fichier MS Excel à importer'); ?></span><br/><br/>
						<span>
                            <input type="file" id="fichier" name="fichier" style="border: solid 0px!important;"/>
                        </span>
					</section>
                <?
                $listeErreurs = FinancementsProjets::getListeErreursImport();
                if (count($listeErreurs) > 0) {
                    ?>
                    <section style="margin: 20px 0 10px 0; color: black;">
                        <span style="font-weight: bold"><?php print txt('Dernières erreurs'); ?></span><br/><br/>
                        <span>
                    <?
                    foreach ($listeErreurs as $erreur) {
                        ?>
                        <div style="margin-top: -5px">
                        
                            <a class="download" data-id="<?= $erreur['id'] ?>" style="color: blue; position: relative; top: -8px;"><?= txt('Rapport du ').substr($erreur['ts_insert'], 0, 16) ?></a>
                            <a class="delete" data-id="<?= $erreur['id'] ?>"><img src="css/images/icons/dark/trashcan.png" /></a>
                        </div>
                        <?
                    }
                    ?>
                        </span>
                    </section>
                    <?
                }
                ?>
				</div>
				<?
			} else {
				?>
				<div id="editables">
					<?
					messages();
					erreurs();
					?>
					<section style="margin: 10px 0 50px 0;">
						<table style="width: 95%!important; margin: 10px 0 0 0;">
							<tr>
								<th>
									<?= txt('Champs') ?>
								</th>
								<th>
									<?= txt('Colonnes détectées dans le fichier importé') ?>
								</th>
							</tr>
							<?
                            foreach ($pivots as $pivot) {
                                ?>
                                <tr style="background-color: #e2ffe2">
                                    <td style="border-right: solid 1px transparent!important;">
			                            <?= ($pivot == 'id' ? 'ID' : $admin->getLabelForColonne($pivot)) ?>
                                    </td>
                                    <td style="border-left: solid 1px transparent!important; position: relative;">
                                        <div style="position: absolute; left: -29px; top: 10px; color: red; font-weight: bold;">
                                            <-- <?= txt('Pivot') ?> -->
                                        </div>
			                            <? affichageSelect($pivot, ($pivot == 'id' ? 'ID' : $admin->getLabelForColonne($pivot)), true); ?>
                                        <span style="width: 24px; height: 24px; display: inline-block;">&nbsp;</span>
                                    </td>
                                </tr>
                                <?
                            }
							foreach ($all_cols as $key => $champ) {
								?>
								<tr class="champs"><td>
                                        <span><?= $champ['label'] ?></span>
									</td><td>
										<? affichageSelect($key, $champ['label']) ?>
                                        <img src="css/images/icons/dark/cross_3.png" class="resetAssoc" />
									</td></tr>
								<?
                            }
							?>
						</table>
					</section>
				</div>
				<?
            }
			?>
		</fieldset>

		<div style="text-align: right; width: 98%!important;">
            <?
            if ($etape == 'synchroniser') {
                ?>
                <button class="assocAll all" style="color: green;"><?= txt('Tout associer') ?></button>
                <button class="resetAll all" style="color: red;"><?= txt('Tout dissocier') ?></button>
                <?
            }
            ?>
			<button class="submit" value="submitbuttonvalue" name="<?=$etape?>"><?=txt($etape)?></button>
			<button onclick="parent.$.fancybox.close();return false;" name="close"><?=txt('Annuler')?></button>

		</div>
	</form>
</div>
<?
include('config/footer.inc.php');

function affichageSelect($key, $selected, $isPivot = false)
{
    global $champs;
    ?>
    <select id="alias_<?=$key?>" name="alias_<?=$key?>" class="uniques <?= ($isPivot ? '' : 'colonnes') ?>" style="max-width: 250px!important;">
        <option value=""> </option>
		<?
		foreach($champs as $i => $alias)
		{
			$sel = sansaccent($alias) == sansaccent($selected)  ? ' selected="selected"' : '';
			?>
            <option value="<?=$alias?>"<?=$sel?>><?=$alias?></option>
			<?
		}
		?>
    </select>
    <?
}
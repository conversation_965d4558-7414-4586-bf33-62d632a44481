<?php
//		date, durée, actif, complet
$data = $_POST['table'];
$fonc = $_POST['fonc'];
$item = $_POST['item'];
$statut = 'OK';

function avant($item) {
	global $data,$statut;
	$item--;
	$premier = $item;
	
	if ( $premier == 0 || $data[$premier][2] == 0 ) $statut = 'RIEN FAIRE';
	
	for ($i = $item-1; $i > -1; $i--) {
		if ($data[$i][2] == 0) continue;
		if ($data[$premier][2] == 1 && $data[$premier][0] == '') {
			$statut = 'ERREUR';
			break;
		}
		$d_date = new DateTime($data[$premier][0]);
		$d_duree = $data[$premier][1];

		if ( $data[$i][2] == 1 ) {	// actif
			if ( $data[$i][3] == 0 ) {	// pas complet
				$data[$i][0] = $d_date->sub(new DateInterval('P'.$d_duree.'D'))->format("Y-m-d");
			
				$premier = $i;
			}
			else {
				$statut = 'ERREUR';
				break;
			}
		}
		
		if ( $data[$i][3] == 1 ) {
			$statut = 'ERREUR';
			break;
		}
	}
}

function apres($item) {
	global $data,$statut;
	$item--;
	$premier = $item;
	
	if ( $premier == count($data) || $data[$premier][2] == 0 ) $statut = 'RIEN FAIRE';
	
	for ($i = $item+1; $i < count($data); $i++) {
		if ($data[$premier][2] == 1 && $data[$premier][0] == '') {
			$statut = 'ERREUR';
			break;
		}
		$d_date = new DateTime($data[$premier][0]);
		$i_duree = $data[$i][1];
		
		if ( $data[$i][2] == 1 ) {	// actif
			if ( $data[$i][3] == 0 ) {	// pas complet
				$data[$i][0] = $d_date->add(new DateInterval('P'.$i_duree.'D'))->format("Y-m-d");
				
				$premier = $i;
			}
			else {
				$statut = 'ERREUR';
				break;
			}
		}
	}
}

function bi($item) {
	avant($item);
	apres($item);
}


$fonc($item);

echo json_encode( array('statut' => $statut, 'table' => $data) );


<?php
/**
 * TODO: Description
 */

$filtre_global_to_use = '_financements';

$isFiltreActifFiches = Fiches::isFiltreActif(($_SESSION['filtre_top' . $filtre_global_to_use] ?? []), ($_SESSION['filtre_avance' . $filtre_global_to_use] ?? []));
$titleFiches = txt('Aucun filtre actif');
if ($isFiltreActifFiches) {
    $titleFiches = txt('Filtre(s) actif(s)');
}
$isFiltreActifEquipement = EquipementsBioMedicauxTypes::isFiltreActif(($_SESSION['filtre_avance_equipement' . $filtre_global_to_use] ?? []));
$titleEqu = txt('Aucun filtre actif');
if ($isFiltreActifEquipement) {
    $titleEqu = txt('Filtre(s) actif(s)');
}

if (isset($_GET['desafecte_filtre_avance'])) {
	$_SESSION['filtre_avance_financements_projets_liste'] = array();
	header("Location: index.php?section=financement&module=projets&mode=global");
	die();
}

if (isset($_GET['lock_groupe'])) {
	$lock = intval($_GET['lock_groupe']);
	$id = intval($_GET['id']);
	$projet_id = intval($_GET['projet_id']);
	if ($lock == 1) {
		$sql = "update financements_projets_groupes_equipements set ts_verrouille = now(), usager_verrouille_id = ? where id = ?";
		$db->query($sql, array(getIdUsager(), $id));
	} else {
		$sql = "update financements_projets_groupes_equipements set ts_verrouille = null, usager_verrouille_id = null where id = ?";
		$db->query($sql, array($id));
	}

	header('Location: index.php?section=financement&module=projets&mode=global&financement_projet_id='.$projet_id);
	die();
} else if (isset($_GET['lock_projet'])) {
    $lock = intval($_GET['lock_projet']);
    $id = intval($_GET['id']);
    $page = intval($_GET['page']);
    if ($lock == 1) {
        $sql = "update financements_projets set ts_verrouille = now(), usager_verrouille_id = ? where id = ?";
        $db->query($sql, array(getIdUsager(), $id));
	    $sql = "update financements_projets_groupes_equipements set ts_verrouille = now(), usager_verrouille_id = ? where financement_projet_id = ?";
	    $db->query($sql, array(getIdUsager(), $id));
    } else {
        $sql = "update financements_projets set ts_verrouille = null, usager_verrouille_id = null where id = ?";
        $db->query($sql, array($id));
    }

    header('Location: index.php?section=financement&module=projets&mode=global');
    die();
} else if (isset($_GET['delete_admin_modele_id'])) {
    $sql = "delete from financements_projets_administrations_unifiees where id = ?";
    $db->query($sql, [intval($_GET['delete_admin_modele_id'])]);

	header('Location: index.php?section=financement&module=projets&mode=global&admin_modele_id=' . FinancementsProjetsAdministrationsUnifiees::getDefaultId());
	die();
}

$is_print = isset($_GET['print']);

if ($is_print) {
    $noHeader = 1;
    $isPDF = true;
    include "config/header.inc.php";
    ?>

    <script>
        window.onload = function() { window.print(); }
    </script>
    <style>
        #logs td {
            text-align: left;
            padding: 0px 5px;
        }
        #logs th {
            text-weight: bold;
            text-align: left;
        }
        fieldset.cotacote {
            margin: 0 0 17px 30px;
            width: 42%;
            float: left;
        }
        fieldset.cotacote section > div {
            width: 150px !important;
        }
        #editables fieldset.cotacote label {
            width: 240px !important;
        }
        fieldset.cotacote input[type=text], fieldset.cotacote select {
            min-width: 150px !important;
            width: 150px !important;
        }
        fieldset.cotacote textarea {
            height: 50px !important;
            min-height: 50px !important;
        }

    </style>

    <?php
}

if (!$is_print) {
    include "config/header.inc.php";
    ?>
    <script>
        $(document).ready(function(){
			$('#modeleAdmin').change(function(){
				window.location = `index.php?section=financement&module=projets&mode=global&admin_modele_id=${this.value}`;
            });
			$('#delModeleAdmin').click(function(){
				if (confirm('<?= txt('Êtes-vous certain de vouloir supprimer ce modèle?') ?>')) {
					window.location = `index.php?section=financement&module=projets&mode=global&delete_admin_modele_id=${$(this).data('id')}`;
				}
			});
        });
    </script>
    <style>
        .isalert {
            background-color: #fef2d5;
        }
        .isalertNegatif {
            background-color: #fe5152;
            color: white;
            font-weight: bold;
        }
        #financements_projets td {
            text-align: left;
            padding: 0px 5px;
        }
        #financements_projets th {
            text-weight: bold;
            text-align: left;
        }
        select {
            width: 210px;
        }

        .vue-projet { color: blue; }
        .vue { margin-top:6px; margin-left:10px; }
        ul.vue { clear: both; padding-top:3px; padding-left:5px; }
        .vue li { list-style-type:disc!important; margin-left:20px; display:list-item; clear:both; }
        .vue ol { list-style-type:decimal!important; display:list-item; padding-left:6px; margin:6px auto; clear:both; border-top:#EEE 1px solid; }
        .vue ol:first-child { border-top:none; }
        .vue div { float: left; margin-right: 5px; min-width:80px; }
        .vue div.droite { float: right; margin-right: 5px; }
        .vue div.droite2 { float: right; margin-right: 5px; width:130px; }
        .vue div.denorm { margin-left: 85px; }
        .vue div.desc { width: 375px; }
        .vue div label { float:left; margin-right: 4px; color:blue; height:1.5em; }

        .select2-choices {
            min-height: auto!important;
        }

        .select2-choices input {
            padding: 0!important;
            margin: 0 0 0 5px!important;
        }

        .div_tr {
            display: table-row;
            line-height: 40px;
        }

        .div_td {
            display: table-cell;
            border-right: solid transparent 10px;
        }
    </style>
<?php
}
$filtre = &$_SESSION['filtre_financement'];
$filtre['projet_id'] = $filtre['projet_id'] ?? [];
$filtre['equipement_bio_medicaux_type_id_via_depot'] = $filtre['equipement_bio_medicaux_type_id_via_depot'] ?? [];
$filtre['fiche_ids'] = $filtre['fiche_ids'] ?? [];
$filtre['echeancier'] = $filtre['echeancier'] ?? [];
$filtre['order_by_col'] = $filtre['order_by_col'] ?? 'numero';
$filtre['order_by_dir'] = $filtre['order_by_dir'] ?? 'asc';
$filtre['page'] = $filtre['page'] ?? 1;
$filtre['mode'] = $filtre['mode'] ?? 'specifique';
$filtre['admin_modele_id'] = $filtre['admin_modele_id'] ?? FinancementsProjetsAdministrationsUnifiees::getDefaultId();

if (isset($_GET['clear_filtre'])) {
	$filtre['projet_id'] = [];
	$filtre['equipement_bio_medicaux_type_id_via_depot'] = [];
    $filtre['fiche_ids'] = [];
    $filtre['echeancier'] = [];
}

if (isset($_GET['page']) && intval($_GET['page']) > 0) {
	$filtre['page'] = intval($_GET['page']);
}

if (isset($_GET['mode']) && dm_strlen($_GET['mode']) > 0) {
	$filtre['mode'] = $_GET['mode'];
}

if (isset($_GET['admin_modele_id']) && intval($_GET['admin_modele_id']) > 0) {
	$filtre['admin_modele_id'] = intval($_GET['admin_modele_id']);
}

if (isset($_POST['projet_id'])) {
    $filtre['projet_id'] = $_POST['projet_id'];
    $filtre['page'] = 1;
} else if (isset($_POST['filtrer'])) {
    $filtre['projet_id'] = [];
}

if (isset($_POST['equipement_bio_medicaux_type_id'])) {
    $filtre['equipement_bio_medicaux_type_id_via_depot'] = $_POST['equipement_bio_medicaux_type_id'];
    $filtre['page'] = 1;
} else if (isset($_POST['filtrer'])) {
    $filtre['equipement_bio_medicaux_type_id_via_depot'] = [];
}

if (isset($_POST['fiche_ids'])) {
    $filtre['fiche_ids'] = $_POST['fiche_ids'];
    $filtre['page'] = 1;
} else if (isset($_POST['filtrer'])) {
    $filtre['fiche_ids'] = [];
}

if (isset($_POST['echeancier'])) {
    $filtre['echeancier'] = $_POST['echeancier'];
    $filtre['page'] = 1;
} else if (isset($_POST['filtrer'])) {
    $filtre['echeancier'] = [];
}

if(isset($_GET['order_by_col']) && isset($_GET['order_by_dir']))
{
	$filtre['order_by_col'] = $_GET['order_by_col'];
	$filtre['order_by_dir'] = $_GET['order_by_dir'];
	$filtre['page'] = 1;
}

$isFiltreActif = false;
$title = txt('Aucun filtre actif');
$filtre['filtre_avance'] = array();
if (FinancementsProjets::isFiltreAvanceActif()) {
	$title = txt('Filtre(s) actif(s)');
	$isFiltreActif = true;
	$filtre['filtre_avance'] = $_SESSION['filtre_avance_financements_projets_liste'];
}

$nb_par_page = 15;
if (isset($_SESSION['nb_total_pages']) && $filtre['page'] > 1 && !isset($_GET['clear_filtre'])) {
	$nb_total = $_SESSION['nb_total_pages'];
}
else {
	$nb_total = FinancementsProjets::getCount($filtre);
	$_SESSION['nb_total_pages'] = $nb_total;
}

$nb_pages = ceil($nb_total / $nb_par_page);
$limit = $filtre['page'] == 1 ? 0 : ($filtre['page']-1) * $nb_par_page;
$sql_limit = $is_print ? '' : "LIMIT $limit, $nb_par_page";

if($isFiltreActifFiches) {
    $fiches = Fiches::getListe(array('filtre_global_to_use' => $filtre_global_to_use));
    $filtre['fiche_ids'] = array_keys($fiches);
}

if ($isFiltreActifEquipement) {
    $equips = EquipementsBioMedicauxTypes::getListe( [ 'filtre_global_to_use' => $filtre_global_to_use ] );
    $filtre['equipement_bio_medicaux_type_id_via_depot'] = array_keys($equips);
}

$financements_projets = FinancementsProjets::getListeProjets($filtre, $filtre['order_by_col'].' '.$filtre['order_by_dir'], $sql_limit);

if (!$is_print) {
    ?>
    <h1 class="ficheTitre" style="margin-bottom: 20px;">
        <?php echo txt('Financement - Gestion spécifique'); ?>

<!--        <a href="index.php?section=financement&module=projets&mode=global&print=1" style="position: relative; top: 4px;" title="--><?php //echo txt("Imprimer"); ?><!--" target="iframe">-->
<!--            <img src="css/images/icons/dark/printer.png" /></a>-->

        <?
        if ($CodeClient <> 'phvs') {
        ?>
            <a href="index.php?section=financement&module=filtre_avance_financements" class="fancy_big" style="<?= ($isFiltreActif ? '' : 'opacity: 0.5;') ?> position: relative; top: 3px;" title="<?= $title ?>">
                <img src="css/images/icons/dark/filter.png"/></a>
<?
        }
?>
        <a href="index.php?section=financement&module=export_avance_financements" class="fancy_big" title="Exporter">
            <img src="css/images/icons/dark/DM_export_CSV.png"></a>

	    <?=($isFiltreActif ?
		    '<span style="color: blue; font-size: 14px; font-weight: bold; position: relative; top: -2px;">('.txt('Filtres avancés appliqués').')</span>
		    <input type="button" class="filtrer" value="'.txt('Désaffecter').'" style="min-height: 22px!important; padding: 0 20px; position: relative; top: -4px;" onclick="window.location = \'index.php?section=financement&module=projets&mode=global&desafecte_filtre_avance=1\'" />'
		    : '')?>
        <?
        $admin = FinancementsProjetsAdministrations::getAdminForProjet();
        $adminUni = new FinancementsProjetsAdministrationsUnifiees($filtre['admin_modele_id']);
        ?>
        <div style="float: right;">
<!--            <a class="btn" href="index.php?section=financement&module=equipements_non_traites">--><?php //echo txt('Gestion générale'); ?><!--</a>-->

            <a class="btn <?= ($filtre['mode'] == 'global' ? 'actif' : '') ?>" href="index.php?section=financement&module=projets&mode=global"><?php echo txt('Gestion spécifique'); ?></a>
            <?
            if (havePermission('admin_financements') /*&& $CodeClient <> 'phvs'*/)  {
                ?>
                <a class="btn fancy_big" href="index.php?section=admin&module=edit&table=financements_projets_administrations&type=client&id=<?= (isset($admin) ? $admin->id : 0) ?>"><?php echo txt('Administration'); ?></a>
                <?
            }

	        if (FinancementsProjets::isGestionDuClendrier()) {
                ?>
                <a class="btn" href="index.php?section=financement&module=calendrier_financements"><?php echo txt('Calendrier'); ?></a>
                <?
	        }

            if (havePermission('gestion_exemptions_verrouillages')) {
                ?>
                <a class="btn" href="index.php?section=gestionnairePage&module=ExemptionsVerrouillages"><?php echo txt('Exemptions de verrouillages'); ?></a>
                <?
            }

	        if (havePermission('synchronisation_donnes_financements')) {
		        ?>
                <a class="btn fancy" href="index.php?section=financement&module=import"><?=txt('Importation')?></a>
		        <?
	        }
	        ?>
        </div>
    </h1>

<!--    <div id="blocFiltre" style="padding: 12px 5px 0px 15px; position: relative;">-->
<!---->
<!--        <div style="float: left; width: 600px; margin: 0 10px 10px 0;">-->
<!--            --><?//=txt('Fin de projet entre ')?><!--<input type="text" id="date_debut" name="date_debut" class="date hasDatepicker filtre" value="--><?//=$filtre['date_debut']?><!--">-->
<!--            --><?//=txt('et')?><!-- <input type="text" id="date_fin" name="date_fin" class="date hasDatepicker filtre" value="--><?//=$filtre['date_fin']?><!--">-->
<!---->
<!--        </div>-->
<!--    </div>-->
<!--        <br style="clear: both;" />-->
<!--        <br style="clear: both;" />-->

    <form action="index.php?section=financement&module=projets&mode=global" method="post">
    <div style="display: table">
        <div class="div_tr">
            <div class="div_td" style="width: 310px;">
                <a href="index.php?section=filtre_avance&module=filtre_avance&type=<?= $filtre_global_to_use ?>&smaller=1"
                   class="fancy"
                   style="<?= ($isFiltreActifFiches ? '' : 'opacity: 0.5;') ?> position: relative; top: 6px;"
                   title="<?= $titleFiches ?>">
                    <img src="css/images/icons/dark/filter.png"/><span style="position: relative; top: -7px;">&nbsp;<?= txt('Filtre avancé locaux') ?></span></a>

                <?=($isFiltreActifFiches ?
                    '<span style="color: blue; font-size: 12px; font-weight: bold; position: relative; top: -2px;">('.txt('Filtres appliqués').')</span>
                         <a href="javascript: void(0);" id="clear_filtre" style="display: inline!important; position: relative; top: 6px;" title="Libérer tous les filtres">
                            <img src="css/images/icons/dark/bended_arrow_left.png"></a>'
                    : '')?>
            </div>
            <div class="div_td" style="width: 310px;">
                <a href="index.php?section=financement&module=filtre_avance_equipement&type=<?= $filtre_global_to_use ?>"
                   class="fancy"
                   style="<?= ($isFiltreActifEquipement ? '' : 'opacity: 0.5;') ?> position: relative; top: 6px;"
                   title="<?= $titleEqu ?>">
                    <img src="css/images/icons/dark/filter.png"/><span style="position: relative; top: -7px;">&nbsp;<?= txt('Filtre avancé équipements') ?></span></a>

                <?=($isFiltreActifEquipement ?
                    '<span style="color: blue; font-size: 12px; font-weight: bold; position: relative; top: -2px;">('.txt('Filtres appliqués').')</span>
                         <a href="javascript: void(0);" id="clear_filtre_equipement" style="display: inline!important; position: relative; top: 6px;" title="Libérer tous les filtres">
                            <img src="css/images/icons/dark/bended_arrow_left.png"></a>'
                    : '')?>
            </div>
        </div>
        <div class="div_tr">

            <div class="div_td" style="<?= ($isFiltreActifFiches ? 'opacity: 0.5; pointer-events: none;' : '') ?>">
                <?php
                $lisetSelecteds = $isFiltreActifFiches ? [] : $filtre['fiche_ids'];
                ?>
                <select id="fiche_ids" name="fiche_ids" style="width: 300px;" class="filtre champ_multiple" multiple data-placeholder="- <?= txt('Locaux') ?> -">
                    <? Fiches::getOptions(['fiche_sous_type_id' => 2000002], $lisetSelecteds); ?>
                </select>
                <?
                ?>
            </div>

            <div class="div_td" style="<?= ($isFiltreActifEquipement ? 'opacity: 0.5; pointer-events: none;' : '') ?>">
                <?php
                $lisetSelecteds = $isFiltreActifEquipement ? [] : $filtre['equipement_bio_medicaux_type_id_via_depot'];
                FinancementsProjets::selectBoxEquipements($lisetSelecteds, true);
                ?>
            </div>

            <div class="div_td">
                <?php
                FinancementsProjets::selectBoxProjects($filtre['projet_id'], true);
                ?>
            </div>

            <div class="div_td">
                <?php
                FinancementsProjets::selectBoxEcheancier($filtre['echeancier'], true); ?>
            </div>

            <div class="div_td">
                <input type="submit" id="filtrer" class="filtrer" name="filtrer" value="<?=txt('Filtrer')?>" />
                <a href="index.php?section=financement&module=projets&mode=global&clear_filtre=1" id="clear_filtre" style="display: inline!important; position: relative; top: 10px;"
                   title="<?php echo txt('Libérer tous les filtres'); ?>">
                    <img src="css/images/icons/dark/bended_arrow_left.png" /></a>
            </div>
        </div>
    </div>
    </form>
    <br style="clear: both" />
    <br style="clear: both" />
<?php
}

if ($is_print) {
?>
    <h6><?php echo txt('Financement - Gestion spécifique'); ?></h6>
<?php
}

if (isset($adminUni) && isset($admin)) {
?>
    <div style="float: left; margin: 5px 15px;position: relative; top: -10px">
        <label for="modeleAdmin"><?= txt('Modèles de gestion ') ?></label>
        <select id="modeleAdmin" style="width: 250px;">
        <?
        $modeles = FinancementsProjetsAdministrationsUnifiees::getListe(['projet_id' => getProjetId()], 'nom');
        foreach ($modeles as $i => $modele) {
            ?>
            <option value="<?= $modele->id ?>"<?= ($modele->id == $filtre['admin_modele_id'] ? ' selected="selected"' : '') ?>><?= $modele->nom ?></option>
            <?
        }
        ?>
        </select>
        <span style="position: relative; top: 10px">
            <a href="index.php?section=admin&module=edit&table=financements_projets_administrations_unifiees&type=client&id=<?= $filtre['admin_modele_id'] ?>" class="fancy_big_no_exp_table" title="<?=txt('Éditer le projet')?>">
                <img src="css/images/icons/dark/create_write.png"></a>
            <a href="index.php?section=admin&module=edit&table=financements_projets_administrations_unifiees&type=client&id=0" class="fancy_big">
                <img src="css/images/icons/dark/plus.png" /></a>
            <a id="delModeleAdmin" data-id="<?= $filtre['admin_modele_id'] ?>">
                <img src="css/images/icons/dark/trashcan.png"/></a>
        </span>
    </div>
    <div style="float: right; margin: 5px 0px;">
<!--        <span style="margin-right: 30px; ">-->
<!--            --><?//=txt('Eléments à réviser')?>
<!--            <select id="elements_a_revise">-->
<!--            	<option value="0">- --><?//= txt('Faire un choix') ?><!-- -</option>-->
<!--            	<option value="1" --><?php //if ($filtre['elements_a_revise'] == 1) echo ' selected'; ?><?//= txt('Quantités soumises à réviser') ?><!--</option>-->
<!--            	<option value="2" --><?php //if ($filtre['elements_a_revise'] == 2) echo ' selected'; ?><?//= txt('Quantités effectives à réviser') ?><!--</option>-->
<!--            	<option value="3" --><?php //if ($filtre['elements_a_revise'] == 3) echo ' selected'; ?><?//= txt('Associations de locaux à réviser') ?><!--</option>-->
<!--            </select>-->
<!--        </span>-->
    </div>

    <table id="financements_projets">
        <tr>
            <th width="120" class="<?=($filtre['order_by_col'] == 'numero' ? 'ordered' : '')?>" style="display: <?= ($adminUni->isColonneVisible('col_numero_projet') ? '' : 'none') ?>;"<?= $adminUni->getTooltipHtml('col_numero_projet') ?>>
                <?=getOrderByLink($admin->getLabelForColonne('col_numero_projet'), 'index.php?section=financement&module=projets&mode=global', 'numero', $filtre['order_by_col'], $filtre['order_by_dir'])?>
            </th>
            <th class="<?=($filtre['order_by_col'] == 'description' ? 'ordered' : '')?>" style="display: <?= ($adminUni->isColonneVisible('col_description_projet') ? '' : 'none') ?>;"<?= $adminUni->getTooltipHtml('col_description_projet') ?>>
                <?=getOrderByLink($admin->getLabelForColonne('col_description_projet'), 'index.php?section=financement&module=projets&mode=global', 'description', $filtre['order_by_col'], $filtre['order_by_dir'])?>
            </th>
            <th width="120" class="<?=($filtre['order_by_col'] == 'ts_ajout' ? 'ordered' : '')?>" style="display: <?= ($adminUni->isColonneVisible('col_ts_ajout') ? '' : 'none') ?>;"<?= $adminUni->getTooltipHtml('col_ts_ajout') ?>>
                <?=getOrderByLink($admin->getLabelForColonne('col_ts_ajout'), 'index.php?section=financement&module=projets&mode=global', 'ts_ajout', $filtre['order_by_col'], $filtre['order_by_dir'])?>
            </th>
            <th width="300" class="<?=($filtre['order_by_col'] == 'titre_prochain_jalon' ? 'ordered' : '')?>" style="display: <?= ($adminUni->isColonneVisible('col_titre_prochain_jalon') ? '' : 'none') ?>;"<?= $adminUni->getTooltipHtml('col_titre_prochain_jalon') ?>>
                <?=getOrderByLink($admin->getLabelForColonne('col_titre_prochain_jalon'), 'index.php?section=financement&module=projets&mode=global', 'titre_prochain_jalon', $filtre['order_by_col'], $filtre['order_by_dir'])?>
            </th>
            <th width="140" class="<?=($filtre['order_by_col'] == 'date_prochain_jalon' ? 'ordered' : '')?>" style="display: <?= ($adminUni->isColonneVisible('col_date_prochain_jalon') ? '' : 'none') ?>;"<?= $adminUni->getTooltipHtml('col_date_prochain_jalon') ?>>
                <?=getOrderByLink($admin->getLabelForColonne('col_date_prochain_jalon'), 'index.php?section=financement&module=projets&mode=global', 'date_prochain_jalon', $filtre['order_by_col'], $filtre['order_by_dir'])?>
            </th>
            <th width="75" class="<?=($filtre['order_by_col'] == 'statut' ? 'ordered' : '')?>" style="display: <?= ($adminUni->isColonneVisible('col_statut') ? '' : 'none') ?>;"<?= $adminUni->getTooltipHtml('col_statut') ?>>
                <?=getOrderByLink($admin->getLabelForColonne('col_statut'), 'index.php?section=financement&module=projets&mode=global', 'statut', $filtre['order_by_col'], $filtre['order_by_dir'])?>
            </th>
            <?
			if(!$is_print)
			{
				?>
				<th width="150" style="text-align: center!important;">
<?php if (havePermission(array('creation_projet_financement'))) { ?>
					<a href="index.php?section=admin&module=edit&table=financements_projets&type=client&id=0&mode=global" class="fancy_big">
						<img src="css/images/icons/dark/plus.png" /></a>
<?php } ?>
				</th>
				<?
			}
			?>
		</tr>
		<?php
		$com_id = 0;
		foreach ($financements_projets as $i => $financement)
		{
			$projet_numero = $financement->numero;
			?>
			<tr id="ligne_projet_<?=$financement->id?>" class="ligne_projet" data-projet_id="<?=$financement->id?>">
                <td class="<?=($filtre['order_by_col'] == 'numero' ? 'ordered' : '')?>" style="display: <?= ($adminUni->isColonneVisible('col_numero_projet') ? '' : 'none') ?>;">
                    <a href="javascript: void(0);" style="font-weight: bold; color: blue; text-decoration: underline" onclick="toggleAcq(<?=$financement->id?>,'<?=$financement->numero?>',<?=$filtre['page']?>);"><?=$financement->numero?></a>
				</td><td class="<?=($filtre['order_by_col'] == 'description' ? 'ordered' : '')?>" style="display: <?= ($adminUni->isColonneVisible('col_description_projet') ? '' : 'none') ?>;">
					<?=$financement->description?>
				</td><td class="<?=($filtre['order_by_col'] == 'ts_ajout' ? 'ordered' : '')?>" style="display: <?= ($adminUni->isColonneVisible('col_ts_ajout') ? '' : 'none') ?>;">
					<?=dm_substr($financement->ts_ajout, 0, 10)?>
				</td><td class="<?=($filtre['order_by_col'] == 'titre_prochain_jalon' ? 'ordered' : '')?>" style="display: <?= ($adminUni->isColonneVisible('col_titre_prochain_jalon') ? '' : 'none') ?>;">
					<?=$financement->titre_prochain_jalon?>
				</td><td class="<?=($filtre['order_by_col'] == 'date_prochain_jalon' ? 'ordered' : '')?>" style="display: <?= ($adminUni->isColonneVisible('col_date_prochain_jalon') ? '' : 'none') ?>;">
					<?=$financement->date_prochain_jalon?>
                </td><td class="<?=($filtre['order_by_col'] == 'statut' ? 'ordered' : '')?>" style="color: <?=$financement->statutColor()?>; font-weight: bold; display: <?= ($adminUni->isColonneVisible('col_statut') ? '' : 'none') ?>;">
					<?=$financement->statutTexte()?>
				</td>
				<?
				if(!$is_print)
				{
					?>
					<td style="height: 26px; text-align: center;">
                        <?
                        if(!$financement->isLocked())
                        {
                            if (havePermission(array('edition_projet_financement'))) { ?>
                                <a href="index.php?section=admin&module=edit&table=financements_projets&type=client&id=<?=$financement->id?>&mode=global" class="fancy_big"  title="<?=txt('Éditer le projet')?>">
                                    <img src="css/images/icons/dark/create_write.png"></a>

                            <?php }
                            else{
	                            ?>
                                <a href="index.php?section=admin&module=edit&table=financements_projets&type=client&id=<?=$financement->id?>&force_readonly=1" class="fancy_big" title="<?=txt('Éditer le projet')?>">
                                    <img src="css/images/icons/dark/magnifying_glass.png"></a>
	                            <?
                            }
                            if (havePermission(array('effacement_projet_financement'))) {
                                if ($financement->nbDepots > 0) {
	                                ?>
                                    <a href="javascript: void(0);" onclick="alert('<?= txt('Pour procéder à la suppression de cette demande de financement, veuillez d’abord supprimer tous les dépôts qu’elle contient.') ?>')"
                                       title="<?= txt('Supprimer le projet') ?>">
                                        <img src="css/images/icons/dark/trashcan.png"/></a>
	                                <?php
                                } else {
                                    ?>
                                    <a href="javascript: void(0);" class="dle_btn dle_delete_generique"
                                       tableName="financements_projets" idElement="<?= $financement->id ?>"
                                       title="<?= txt('Supprimer le projet') ?>">
                                        <img src="css/images/icons/dark/trashcan.png"/></a>
                                    <?php
                                }
                            }
	                        ?>
                            <a href="index.php?section=financement&module=admin_depot&financement_projet_id=<?= $financement->id ?>" title="<?= txt('Gérer les dépôts') ?>" class="fancy_big">
                                <img src="css/images/icons/dark/list_images.png"/></a>
	                        <?
                        }
                        else
                        {
	                        if (havePermission(array('edition_projet_financement'))) { ?>
                                <a href="index.php?section=admin&module=edit&table=financements_projets&type=client&id=<?=$financement->id?>&mode=global" class="fancy_big" title="<?=txt('Éditer le projet')?>">
                                    <img src="css/images/icons/dark/create_write.png"></a>

	                        <?php }
	                        else
                            {
	                            ?>
                                <a href="index.php?section=admin&module=edit&table=financements_projets&type=client&id=<?=$financement->id?>&force_readonly=1" class="fancy_big" title="<?=txt('Éditer le projet')?>">
                                    <img src="css/images/icons/dark/magnifying_glass.png"></a>
	                            <?
                            }
                            if (havePermission(array('synchronisation_donnes_financements'))) { ?>
                            <a href="index.php?section=financement&module=projets&mode=global&export_projet=1&id=<?=$financement->id?>" onclick="loading_beforeunload = false;" title="<?=txt('Exporter')?>">
                                <img src="css/images/icons/dark/arrow_up_right.png" /></a>
                            <a href="index.php?section=financement&module=import&id=<?=$financement->id?>" class="fancy" title="<?=txt('Importer')?>">
                                <img src="css/images/icons/dark/arrow_down_left.png" /></a>
                            <?php }
	                        ?>
                            <a href="index.php?section=financement&module=admin_depot&financement_projet_id=<?= $financement->id ?>" title="<?= txt('Gérer les dépôts') ?>" class="fancy_big">
                                <img src="css/images/icons/dark/list_images.png"/></a>
	                        <?
                        }
                        ?>
                        <img src="css/images/icons/dark/alert_red.png" class="projet_alert" id="projet_alert_<?=$financement->id?>" style="display: none;" />
					</td>
					<?
				}
				?>
			</tr>

            <tr>
                <td colspan="8" style="padding: 0px!important; background-color: #aaa!important;">
                    <div class="fin" id="financement_<?=$financement->id?>" data-projet_id="<?=$financement->id?>" style="display: none; margin: 10px; padding: 10px 5px; background-image: url('css/light/images/paper_02.png');"></div>
                </td>
            </tr>
			<?
		}
		?>
	</table>
<?
}
    ?>
<script>
	const mode = 'global';
<?php include 'projets.js_financements.php'; ?>
</script>
<?
if(!$is_print)
{
	showPaginateur("index.php?section=financement&module=projets&mode=global", $limit, $nb_total, $nb_pages, $nb_par_page, $filtre['page']);
}
?>
	<iframe id="iframe" name="iframe" style="display: none; width: 100%;"></iframe>
	<br style="clear: both;" />
<?
include('config/footer.inc.php');
?>
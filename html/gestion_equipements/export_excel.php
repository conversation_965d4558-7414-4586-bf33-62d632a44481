<?
ini_set("memory_limit","10000M");
ini_set('max_execution_time', 30000);

$isIncluded = 1;
$_GET['ouvrage_id'] = getOuvrageId();
require_once 'gestion_equipements/generer_arbo.php';
$arbreData = $arbre;

$is_equipements = [];
for ($iter = 1; $iter <=3; $iter++) {
	if (isset($_GET["equipement_type_id_$iter"]) && $_GET["equipement_type_id_$iter"] > 0) {
		$equipement = getEquipement($_GET["formulaire_id_$iter"], $_GET["equipement_type_id_$iter"]);
		$is_equipements[$iter] = $equipement->getRaw('nom');
	}
}


$ligne = 0;
$dataArray = [];
$dataArray[$ligne][] = 'Locaux';
foreach ($is_equipements as $iter => $equip) {
	$dataArray[$ligne][] = "Requis ($equip)";
	$dataArray[$ligne][] = "Existant ($equip)";
	$dataArray[$ligne][] = "Fantôme ($equip)";
}
$ligne++;

if (isset($arbreData) && is_array($arbreData)) {
	foreach ($arbreData as $it1 => $data_niv_1) {
		traiteNiveau($dataArray, $ligne, $data_niv_1, $is_equipements);
		if (isset($data_niv_1['items'])) {
			foreach ($data_niv_1['items'] as $it2 => $data_niv_2) {
				traiteNiveau($dataArray, $ligne, $data_niv_2, $is_equipements);
				if (isset($data_niv_2['items']) && !(isset($data_niv_2["equipement1"]) || isset($data_niv_2["equipement2"]) || isset($data_niv_2["equipement3"]))) {
					foreach ($data_niv_2['items'] as $it3 => $data_niv_3) {
						traiteNiveau($dataArray, $ligne, $data_niv_3, $is_equipements);
						if (isset($data_niv_3['items']) && !(isset($data_niv_3["equipement1"]) || isset($data_niv_3["equipement2"]) || isset($data_niv_3["equipement3"]))) {
							foreach ($data_niv_3['items'] as $it4 => $data_niv_4) {
								traiteNiveau($dataArray, $ligne, $data_niv_4, $is_equipements);
								if (isset($data_niv_4['items']) && !(isset($data_niv_4["equipement1"]) || isset($data_niv_4["equipement2"]) || isset($data_niv_4["equipement3"]))) {
									foreach ($data_niv_4['items'] as $it5 => $data_niv_5) {
										traiteNiveau($dataArray, $ligne, $data_niv_5, $is_equipements);
										if (isset($data_niv_5['items']) && !(isset($data_niv_5["equipement1"]) || isset($data_niv_5["equipement2"]) || isset($data_niv_5["equipement3"]))) {
											foreach ($data_niv_5['items'] as $it6 => $data_niv_6) {
												traiteNiveau($dataArray, $ligne, $data_niv_6, $is_equipements);
												if (isset($data_niv_6['items']) && !(isset($data_niv_6["equipement1"]) || isset($data_niv_6["equipement2"]) || isset($data_niv_6["equipement3"]))) {
													foreach ($data_niv_6['items'] as $it6 => $data_niv_7) {
														traiteNiveau($dataArray, $ligne, $data_niv_7, $is_equipements);
													}
												}
											}
										}
									}
								}
							}
						}
					}
				}
			}
		}
	}
}

function traiteNiveau(&$dataArray, &$ligne, &$data_niv, $is_equipements) {
	if (isset($data_niv["equipement1"]) || isset($data_niv["equipement2"]) || isset($data_niv["equipement3"])) {
		$dataArray[$ligne][] = $data_niv['niv'];
		foreach ($is_equipements as $iter => $equip) {
			$quantite = 0;
			$quantite_existante = 0;
			$quantite_fantome = 0;
			if (isset($data_niv["equipement$iter"])) {
				foreach ($data_niv["equipement$iter"] as $equipement) {
					$quantite += (int)$equipement["quantite"];
					$quantite_existante += (int)$equipement["quantite_existante"];
					$quantite_fantome += (int)$equipement["quantite_fantome"];
				}
			}
			$dataArray[$ligne][] = $quantite;
			$dataArray[$ligne][] = $quantite_existante;
			$dataArray[$ligne][] = $quantite_fantome;
		}
		$ligne++;
	}
}

function getEquipement($formulaire_id = 0, $equipement_type_id = 0) {

	$form = new Formulaires($formulaire_id);
	$first_champ = $form->getFirstChamp();
	$class_first_champ = $first_champ['classe'];
	return new $class_first_champ($equipement_type_id);
}

exportExcelFromDataArray($dataArray, txt('Export'), txt('Export gestion équipements '.date('Y-m-d'), false));
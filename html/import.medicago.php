<?
include('config/inc.php');

$locaux = array(
	"01.A.101" => array(16, "Employee Entrance", 0, "Secondary entrance with security (card reader)"),
    "01.A.102" => array(16, "Infirmary", 0, "First aid equipments"),
    "01.A.103" => array(16, "Vestibule - Employee entrance", 0, ""),
    "01.A.201" => array(16, "Cloak room (visitors)", 0, "Small wardrobe"),
    "01.A.202" => array(16, "Reception Lobby", 0, "Reception desk with check-in for visitors"),
    "01.A.203" => array(16, "Reception / security desk", 0, "Security central "),
    "01.A.204" => array(16, "Security office", 0, "Securtiy central"),
    "01.A.205" => array(16, "Vestibule - Main entrance", 0, ""),
    "01.A.206" => array(16, "Waiting Area", 0, ""),
    "01.B.101" => array(19, "Executive Reception", 1, ""),
    "01.B.102" => array(19, "Office - Director/Manager", 2, "2016.06.30 : R.H, Finance Together : locked with open shelving (300 sf) could be at another level than the 3rd floor"),
    "01.B.103" => array(19, "Office - Director/Manager", 2, "2016.06.30 : R.H, Finance Together : locked with open shelving (300 sf) could be at another level than the 3rd floor"),
    "01.B.104" => array(19, "Office - VP/Executive", 2, "2016.06.30 : R.H, Finance Together : locked with open shelving (300 sf) could be at another level than the 3rd floor"),
    "01.B.105" => array(19, "Office - VP/Executive", 2, "2016.06.30 : R.H, Finance Together : locked with open shelving (300 sf) could be at another level than the 3rd floor"),
    "01.B.106-1" => array(19, "Work Stations - Supervisor, Sr Specialist Scientist", 3, "2016.06.30 : R.H, Finance Together : locked with open shelving (300 sf) could be at another level than the 3rd floor"),
    "01.B.106-2" => array(19, "Work Stations -Specialist & Associate", 5, "Open offices - desking style"),
    "01.B.106-3" => array(19, "Work Stations -Administrative Assistant, Clerk", 1, "Open offices - desking style"),
    "01.B.201" => array(16, "Office - Director/Manager Office", 1, "Closed office"),
    "01.B.202" => array(16, "Work Stations -Specialist & Associate", 11, "Open offices - desking style"),
    "01.B.301-1" => array(16, "Work Stations - Supervisor, Sr Specialist Scientist", 2, "2016.06.30 J.R. : includes 1 logistics"),
    "01.B.301-2" => array(16, "Work Stations -Specialist & Associate", 3, "Open offices - desking style"),
    "01.B.301-3" => array(-1, "Work Stations -Administrative Assistant, Clerk", 0, "Open offices - desking style"),
    "01.C.101" => array(16, "Office - Director/Manager", 4, "Closed office"),
    "01.C.102" => array(16, "Office - Director/Manager", 4, "Closed office"),
    "01.C.103" => array(16, "Office - Director/Manager", 4, "Closed office"),
    "01.C.104" => array(16, "Office - Director/Manager", 4, "Closed office"),
    "01.C.105" => array(-1, "Office Greenhouse", 1, "Closed office"),
    "01.C.106-1" => array(16, "Work Stations - Supervisor, Sr Specialist Scientist", 2, "Open offices - desking style"),
    "01.C.106-2" => array(16, "Work Stations -Specialist & Associate", 30, "2016.06.30 M.M. : 10 desks shared by 3 people, because 2 shifts/day"),
    "01.D.101" => array(17, "Office - Director/Manager", 6, "Closed office"),
    "01.D.102" => array(17, "Office - Director/Manager", 6, "Closed office"),
    "01.D.103" => array(17, "Office - Director/Manager", 6, "Closed office"),
    "01.D.104" => array(17, "Office - Director/Manager", 6, "Closed office"),
    "01.D.105" => array(17, "Office - Director/Manager", 6, "Closed office"),
    "01.D.106" => array(17, "Office - Director/Manager", 6, "Closed office"),
    "01.D.107" => array(19, "Office - VP/Executive", 1, "Closed office"),
    "01.D.108-1" => array(17, "Open Office Work Station - Supervisor, Sr Specialist Scientist", 12, "2016.06.30 : + 4 shared work station in progress"),
    "01.D.108-2" => array(17, "Work Stations -Specialist & Associate", 73, "2016.06.30 M.M. : 10 desks shared by 3 people, because 2 shifts/day"),
    "01.D.108-3" => array(17, "Work Stations -Administrative Assistant, Clerk", 5, "Open offices - desking style"),
    "01.E.101" => array(18, "Office -Director/ Manager", 4, "Closed office"),
    "01.E.102" => array(18, "Office -Director/ Manager", 4, "Closed office"),
    "01.E.103" => array(18, "Office -Director/ Manager", 4, "Closed office"),
    "01.E.104" => array(18, "Office -Director/ Manager", 4, "Closed office"),
    "01.E.105" => array(19, "Office - VP /Executive", 1, "Closed office"),
    "01.E.106-1" => array(18, "Open Office Work Station - Supervisor, Sr Specialist Scientist", 13, "Open offices - desking style"),
    "01.E.106-2" => array(18, "Work Stations -Specialist & Associate", 22, "2016.06.30 M.M. : 7 desks shared by 3 people, because 2 shifts/day"),
    "01.E.106-3" => array(18, "Work Stations -Administrative Assistant, Clerk", 3, "Open offices - desking style"),
    "01.F.101" => array(18, "Office - Director/Manager", 5, "2016.06.30 : fire proof room (GMP compliant) with Mobilex or fire cabinets 400 sf (à valider avec Jean) close to QA in charge of documentation (ground floor or 2nd floor)"),
    "01.F.102" => array(18, "Office - Director/Manager", 5, "2016.06.30 : fire proof room (GMP compliant) with Mobilex or fire cabinets 400 sf (à valider avec Jean) close to QA in charge of documentation (ground floor or 2nd floor)"),
    "01.F.103" => array(18, "Office - Director/Manager", 5, "2016.06.30 : fire proof room (GMP compliant) with Mobilex or fire cabinets 400 sf (à valider avec Jean) close to QA in charge of documentation (ground floor or 2nd floor)"),
    "01.F.104" => array(18, "Office - Director/Manager", 5, "2016.06.30 : fire proof room (GMP compliant) with Mobilex or fire cabinets 400 sf (à valider avec Jean) close to QA in charge of documentation (ground floor or 2nd floor)"),
    "01.F.105" => array(18, "Office - Director/Manager", 5, "2016.06.30 : fire proof room (GMP compliant) with Mobilex or fire cabinets 400 sf (à valider avec Jean) close to QA in charge of documentation (ground floor or 2nd floor)"),
    "01.F.106" => array(19, "Office - VP/Executive", 1, "2016.06.30 : fire proof room (GMP compliant) with Mobilex or fire cabinets 400 sf (à valider avec Jean) close to QA in charge of documentation (ground floor or 2nd floor)"),
    "01.F.107-1" => array(19, "Work Stations - Supervisor, Sr Specialist Scientist", 12, "2016.06.30 M.M. : need to be distributed among : G.H. (2), Process (4), QC (3), Ingenering (1), Pilot (1), Warehouse (1)"),
    "01.F.107-2" => array(19, "Work Stations -Specialist & Associate", 19, "2016.06.30 M.M. : need to be distributed among other team: G.H. (2), Process (4), QC (4), Ingenering (4), Pilot (2), Warehouse (3)"),
    "01.F.107-3" => array(19, "Work Stations -Administrative Assistant, Clerk", 2, "Open offices - desking style"),
    "01.G.101" => array(-1, "Enclosed - Director/Manager Office", 9, "Closed office"),
    "01.G.102" => array(-1, "Enclosed - Director/Manager Office", 9, "Closed office"),
    "01.G.103" => array(-1, "Enclosed - Director/Manager Office", 9, "Closed office"),
    "01.G.104" => array(-1, "Enclosed - Director/Manager Office", 9, "Closed office"),
    "01.G.105" => array(-1, "Enclosed - Director/Manager Office", 9, "Closed office"),
    "01.G.106" => array(-1, "Enclosed - Director/Manager Office", 9, "Closed office"),
    "01.G.107" => array(-1, "Enclosed - Director/Manager Office", 9, "Closed office"),
    "01.G.108" => array(-1, "Enclosed - Director/Manager Office", 9, "Closed office"),
    "01.G.109" => array(-1, "Enclosed - Director/Manager Office", 9, "Closed office"),
    "01.G.110-1" => array(-1, "Open Office Work Station - Supervisor, Sr Specialist Scientist", 14, "Open offices - desking style"),
    "01.G.110-2" => array(-1, "Open Office Work Station -Specialist & Associate", 16, "2016.06.30 M.M. : 8 desks shared by 2 people, because need to fill in maintenance report, Comm book, etc."),
    "01.G.110-3" => array(-1, "Open Office Work Station -Administrative Assistant, Clerk", 3, "Open offices - desking style"),
    "01.H.101" => array(16, "Office - Director/Manager", 1, "Closed office"),
    "01.H.102-1" => array(16, "Work Stations - Supervisor, Sr Specialist Scientist", 3, "Open offices - desking style"),
    "01.H.102-2" => array(16, "Work Stations -Specialist & Associate", 14, "2016.06.30 M.M. : 5 offices shared by 2 because of Protocoles, etc. To be confirmed"),
    "01.H.102-3" => array(16, "Work Stations -Administrative Assistant, Clerk", 2, "Open offices - desking style"),
    "01.I.101" => array(19, "Office - Director/Manager", 4, "2016.06.30 J.R. : 2 projects manager are included but could be on 2nd floor"),
    "01.I.102" => array(19, "Office - Director/Manager", 4, "2016.06.30 J.R. : 2 projects manager are included but could be on 2nd floor"),
    "01.I.103" => array(19, "Office - Director/Manager", 4, "2016.06.30 J.R. : 2 projects manager are included but could be on 2nd floor"),
    "01.I.104" => array(19, "Office - Director/Manager", 4, "2016.06.30 J.R. : 2 projects manager are included but could be on 2nd floor"),
    "01.I.105" => array(19, "Office - VP/Executive", 1, "Closed office"),
    "01.I.106" => array(19, "Open Office Work Station - Supervisor, Sr Specialist Scientist", 5, "Open offices - desking style"),
    "01.J.101" => array(19, "Office -Director/Manager", 1, "Closed office"),
    "01.J.102" => array(19, "Office - VP/Executive", 3, "Closed office"),
    "01.J.103" => array(19, "Open Office Work Station -Specialist & Associate", 5, "Open offices - desking style"),
    "01.K.101" => array(18, "Office - Director/Manager", 9, "Closed office"),
    "01.K.102" => array(18, "Office - Director/Manager", 9, "Closed office"),
    "01.K.103" => array(18, "Office - Director/Manager", 9, "Closed office"),
    "01.K.104" => array(18, "Office - Director/Manager", 9, "Closed office"),
    "01.K.105" => array(18, "Office - Director/Manager", 9, "Closed office"),
    "01.K.106" => array(18, "Office - Director/Manager", 9, "Closed office"),
    "01.K.107" => array(18, "Office - Director/Manager", 9, "Closed office"),
    "01.K.108" => array(18, "Office - Director/Manager", 9, "Closed office"),
    "01.K.109" => array(18, "Office - Director/Manager", 9, "Closed office"),
    "01.K.110" => array(19, "Office - VP/Executive", 3, "Closed office"),
    "01.K.111" => array(19, "Office - VP/Executive", 3, "Closed office"),
    "01.K.112" => array(19, "Office - VP/Executive", 3, "Closed office"),
    "01.K.113-1" => array(18, "Open Office Work Station - Supervisor, Sr Specialist Scientist", 22, "Open offices - desking style"),
    "01.K.113-2" => array(18, "Open Office Work Station -Specialist & Associate", 14, "2016.06.30. M.M. : 7 desks shared bye 2 people because of protocoles autoring and reports."),
    "01.L.101" => array(18, "Office - Director/Manager", 2, "Closed office"),
    "01.L.102" => array(18, "Office - Director/Manager", 2, "Closed office"),
    "01.L.103-1" => array(18, "Work Stations - Supervisor, Sr Specialist Scientist", 11, "Open offices - desking style"),
    "01.L.103-2" => array(18, "Work Stations -Specialist & Associate", 10, "2016.06.30 M.M. : 5 desks shared by 2, because of protocole autoring and reports."),
    "01.M.101" => array(18, "Office - Director/Manager", 3, "Closed office"),
    "01.M.102" => array(18, "Office - Director/Manager", 3, "Closed office"),
    "01.M.103" => array(18, "Office - Director/Manager", 3, "Closed office"),
    "01.M.104-1" => array(18, "Open Office Work Station - Supervisor, Sr Specialist Scientist", 9, "Open offices - desking style"),
    "01.M.104-2" => array(18, "Open Office Work Station -Specialist & Associate", 7, "2016.06.30 : M.M. : 4 desks shared by 2 because of protocoles autoring and reports"),
    "01.M.105" => array(19, "Office - VP/Executive", 1, "Closed office"),
    "01.N.101" => array(-1, "Copy / fax rooms", 0, "2016.08.03 : Number of copy room should be defined. Is one per floor enough?"),
    "01.N.102" => array(-1, "Copy / fax rooms", 0, "2016.08.03 : Number of copy room should be defined. Is one per floor enough?"),
    "01.N.103" => array(-1, "Copy / fax rooms", 0, "2016.08.03 : Number of copy room should be defined. Is one per floor enough?"),
    "01.N.104" => array(-1, "Copy / fax rooms", 0, "2016.08.03 : Number of copy room should be defined. Is one per floor enough?"),
    "01.N.201" => array(19, "Commissioning Doc Storage", 0, "2016.08.03 : How many Commissioning Doc storage are required?"),
    "01.N.202" => array(-1, "Documentation rooms", 0, "2016.08.03: Is one each floor is sufficient?"),
    "01.N.203" => array(-1, "Documentation rooms", 0, "2016.08.03: Is one each floor is sufficient?"),
    "01.N.204" => array(-1, "Documentation rooms", 0, "2016.08.03: Is one each floor is sufficient?"),
    "01.N.205" => array(-1, "Documentation rooms", 0, "2016.08.03: Is one each floor is sufficient?"),
    "01.N.206" => array(19, "Engineering Document Storage", 0, "2014-08-29
1) next to Maintenance Offices"),
    "01.N.207" => array(19, "Engineering Document Storage", 0, "2014-08-29
1) next to Maintenance Offices"),
    "01.N.208" => array(19, "QA Document Storage", 0, "2016.08.03 : How many QA Doc storage are required?"),
    "01.N.301" => array(-1, "Supply rooms", 0, "2016.08.03 : How many supply rooms do we need? Where should they be located?"),
    "01.N.302" => array(-1, "Supply rooms", 0, "2016.08.03 : How many supply rooms do we need? Where should they be located?"),
    "01.N.303" => array(-1, "Supply rooms", 0, "2016.08.03 : How many supply rooms do we need? Where should they be located?"),
    "02.A.101" => array(16, "Cafeteria seating area", 0, "2016-08-03:
Assumed 1.6sq.m./pers (1.6x200 = 320sq.m)
Actual area = 323sq.m."),
    "02.A.201" => array(16, "Dish return", 0, "Assumed layout - to be determined"),
    "02.A.202" => array(16, "Servery / cashiers", 0, "Assumed layout - to be determined"),
    "02.A.301" => array(16, "Dishwash", 0, "Assumed layout - to be determined"),
    "02.A.302" => array(16, "Freezer", 0, "Assumed layout - to be determined"),
    "02.A.303" => array(16, "Kitchen", 0, "Assumed layout - to be determined"),
    "02.A.304" => array(16, "Kitchen Office", 0, "Assumed layout - to be determined"),
    "02.A.305" => array(16, "Preparation area", 0, "Assumed layout - to be determined"),
    "02.A.306" => array(16, "Receiving", 0, "Assumed layout - to be determined"),
    "02.A.307" => array(16, "Refrigerator", 0, "Assumed layout - to be determined"),
    "02.A.308" => array(16, "Staff Area", 0, "Assumed layout - to be determined"),
    "02.A.309" => array(16, "Storage", 0, "Assumed layout - to be determined"),
    "02.A.401" => array(16, "Lunch storage", 0, "Refrigerators and plastic laminated built in furniture for dishwashing and integration of waste bins"),
    "02.B.101" => array(16, "Staff Cloakroom (unisex) / Lockers room", 0, "2016.08.03 : Medicago Staff Population : 421                  12' lin. = 24 persons                                                   We now have 167' lin. (334 persons) - which is in line with the maximum population at the same time when we take in consideration the shift"),
    "02.C.101" => array(-1, "Closed Bubble", 0, "22 bubbles of 4-6 seats"),
    "02.C.102" => array(-1, "Closed Bubble", 0, "22 bubbles of 4-6 seats"),
    "02.C.103" => array(-1, "Closed Bubble", 0, "22 bubbles of 4-6 seats"),
    "02.C.104" => array(-1, "Closed Bubble", 0, "22 bubbles of 4-6 seats"),
    "02.C.105" => array(-1, "Closed Bubble", 0, "22 bubbles of 4-6 seats"),
    "02.C.106" => array(-1, "Closed Bubble", 0, "22 bubbles of 4-6 seats"),
    "02.C.107" => array(-1, "Closed Bubble", 0, "22 bubbles of 4-6 seats"),
    "02.C.108" => array(-1, "Closed Bubble", 0, "22 bubbles of 4-6 seats"),
    "02.C.109" => array(-1, "Closed Bubble", 0, "22 bubbles of 4-6 seats"),
    "02.C.110" => array(-1, "Closed Bubble", 0, "22 bubbles of 4-6 seats"),
    "02.C.111" => array(-1, "Closed Bubble", 0, "22 bubbles of 4-6 seats"),
    "02.C.112" => array(-1, "Closed Bubble", 0, "22 bubbles of 4-6 seats"),
    "02.C.113" => array(-1, "Closed Bubble", 0, "22 bubbles of 4-6 seats"),
    "02.C.114" => array(-1, "Closed Bubble", 0, "22 bubbles of 4-6 seats"),
    "02.C.115" => array(-1, "Closed Bubble", 0, "22 bubbles of 4-6 seats"),
    "02.C.116" => array(-1, "Closed Bubble", 0, "22 bubbles of 4-6 seats"),
    "02.C.117" => array(-1, "Closed Bubble", 0, "22 bubbles of 4-6 seats"),
    "02.C.118" => array(-1, "Closed Bubble", 0, "22 bubbles of 4-6 seats"),
    "02.C.119" => array(-1, "Closed Bubble", 0, "22 bubbles of 4-6 seats"),
    "02.C.120" => array(-1, "Closed Bubble", 0, "22 bubbles of 4-6 seats"),
    "02.C.130" => array(-1, "Conference room (Type I - Medium)", 0, "2016-06-30 : 1 on each floor (15 seats)"),
    "02.C.131" => array(-1, "Conference room (Type I - Medium)", 0, "2016-06-30 : 1 on each floor (15 seats)"),
    "02.C.132" => array(-1, "Conference room (Type I - Medium)", 0, "2016-06-30 : 1 on each floor (15 seats)"),
    "02.C.133" => array(-1, "Conference room (Type I - Medium)", 0, "2016-06-30 : 1 on each floor (15 seats)"),
    "02.C.134" => array(-1, "Conference room (Type I - Medium)", 0, ""),
    "02.C.135" => array(-1, "Conference room (Type I - Medium)", 0, ""),
    "02.C.136" => array(-1, "Conference room (Type I - Medium)", 0, ""),
    "02.C.137" => array(-1, "Conference room (Type I - Medium)", 0, ""),
    "02.C.138" => array(-1, "Conference room (Type I - Medium)", 0, ""),
    "02.C.139" => array(-1, "Conference room (Type I - Medium)", 0, ""),
    "02.C.140" => array(-1, "Conference room (Type I - Medium)", 0, ""),
    "02.C.141" => array(-1, "Conference room (Type I - Medium)", 0, ""),
    "02.C.142" => array(-1, "Conference room (Type I - Medium)", 0, ""),
    "02.C.143" => array(-1, "Conference room (Type I - Medium)", 0, ""),
    "02.C.150" => array(19, "Executive Conference Room", 0, "2016-06-30 : 2 on 3rd floor (14 and 10 seats)"),
    "02.C.151" => array(19, "Executive Conference Room", 0, "2016-06-30 : 2 on 3rd floor (14 and 10 seats)"),
    "02.C.160" => array(16, "Meeting Rooms", 0, "2016.06.30 : Need 2 meeting rooms (6-8p.) & (4-6p.) for interviews and suppliers/visitors in proximity to reception area"),
    "02.C.161" => array(16, "Meeting Rooms", 0, "2016.06.30 : Need 2 meeting rooms (6-8p.) & (4-6p.) for interviews and suppliers/visitors in proximity to reception area"),
    "02.C.201" => array(-1, "Creative Area", 0, "Open area with loose furniture by owner"),
    "02.C.202" => array(-1, "Creative Area", 0, "Open area with loose furniture by owner"),
    "02.C.203" => array(-1, "Creative Area", 0, "Open area with loose furniture by owner"),
    "02.C.204" => array(-1, "Creative Area", 0, "Open area with loose furniture by owner"),
    "02.C.205" => array(-1, "Creative Area", 0, "Open area with loose furniture by owner"),
    "02.C.206" => array(-1, "Creative Area", 0, "Open area with loose furniture by owner"),
    "02.C.207" => array(16, "Landscape", 0, "Interior plants"),
    "02.C.208" => array(-1, "Open meeting areas", 0, "2016-06-30 : 14 open meeting area (4 per floor + 2 on mezz) including 5 creative area (1 on mezz) + (2 on 2nd and 3rd floor). In each creative area add a stand up computer workstation."),
    "02.C.209" => array(-1, "Open meeting areas", 0, "2016-06-30 : 14 open meeting area (4 per floor + 2 on mezz) including 5 creative area (1 on mezz) + (2 on 2nd and 3rd floor). In each creative area add a stand up computer workstation."),
    "02.C.210" => array(-1, "Open meeting areas", 0, "2016-06-30 : 14 open meeting area (4 per floor + 2 on mezz) including 5 creative area (1 on mezz) + (2 on 2nd and 3rd floor). In each creative area add a stand up computer workstation."),
    "02.C.211" => array(-1, "Open meeting areas", 0, "2016-06-30 : 14 open meeting area (4 per floor + 2 on mezz) including 5 creative area (1 on mezz) + (2 on 2nd and 3rd floor). In each creative area add a stand up computer workstation."),
    "02.C.212" => array(-1, "Open meeting areas", 0, "2016-06-30 : 14 open meeting area (4 per floor + 2 on mezz) including 5 creative area (1 on mezz) + (2 on 2nd and 3rd floor). In each creative area add a stand up computer workstation."),
    "02.C.213" => array(-1, "Open meeting areas", 0, "2016-06-30 : 14 open meeting area (4 per floor + 2 on mezz) including 5 creative area (1 on mezz) + (2 on 2nd and 3rd floor). In each creative area add a stand up computer workstation."),
    "02.C.214" => array(-1, "Open meeting areas", 0, "2016-06-30 : 14 open meeting area (4 per floor + 2 on mezz) including 5 creative area (1 on mezz) + (2 on 2nd and 3rd floor). In each creative area add a stand up computer workstation."),
    "02.C.215" => array(-1, "Open meeting areas", 0, "2016-06-30 : 14 open meeting area (4 per floor + 2 on mezz) including 5 creative area (1 on mezz) + (2 on 2nd and 3rd floor). In each creative area add a stand up computer workstation."),
    "02.C.216" => array(-1, "Open meeting areas", 0, "2016-06-30 : 14 open meeting area (4 per floor + 2 on mezz) including 5 creative area (1 on mezz) + (2 on 2nd and 3rd floor). In each creative area add a stand up computer workstation."),
    "02.C.217" => array(-1, "Open meeting areas", 0, "2016-06-30 : 14 open meeting area (4 per floor + 2 on mezz) including 5 creative area (1 on mezz) + (2 on 2nd and 3rd floor). In each creative area add a stand up computer workstation."),
    "02.C.218" => array(-1, "Open meeting areas", 0, "2016-06-30 : 14 open meeting area (4 per floor + 2 on mezz) including 5 creative area (1 on mezz) + (2 on 2nd and 3rd floor). In each creative area add a stand up computer workstation."),
    "02.C.219" => array(-1, "Open meeting areas", 0, "2016-06-30 : 14 open meeting area (4 per floor + 2 on mezz) including 5 creative area (1 on mezz) + (2 on 2nd and 3rd floor). In each creative area add a stand up computer workstation."),
    "02.C.220" => array(-1, "Open meeting stairs A4", 0, ""),
    "02.D.101" => array(16, "Break Room / Coffee", 0, ""),
    "02.D.102" => array(16, "Break Room / Coffee", 0, ""),
    "02.D.103" => array(16, "Break Room / Coffee", 0, ""),
    "02.D.104" => array(16, "Break Room / Coffee", 0, ""),
    "02.D.105" => array(16, "Break Room / Coffee", 0, ""),
    "02.D.106" => array(16, "Break Room / Coffee", 0, ""),
    "02.D.107" => array(16, "Break Room / Coffee", 0, ""),
    "02.D.108" => array(16, "Break Room / Coffee", 0, ""),
    "02.E.101" => array(17, "Training room", 0, "2016-06-30 : 2 training rooms (25 seats with a retractable wall in between) on mezz"),
    "02.E.102" => array(17, "Training room", 0, "2016-06-30 : 2 training rooms (25 seats with a retractable wall in between) on mezz"),
    "02.F.101" => array(16, "Gym", 0, "2016.08.03 : 100 sq. m. = capacity of 10 persons."),
    "02.F.102" => array(16, "Lockers - Female", 0, "Minimal requirement In terms of lockes and washroom will be provided"),
    "02.F.103" => array(16, "Lockers - Male", 0, "Minimal requirement In terms of lockes and washroom will be provided"),
    "03.A.101" => array(16, "Future germination 2", 0, "Prefabricated room - process equipment"),
    "03.A.102" => array(16, "Future germination 2", 0, "Prefabricated room - process equipment"),
    "03.A.103" => array(16, "Future germination 2", 0, "Prefabricated room - process equipment"),
    "03.A.104" => array(16, "Germination 1", 0, "Prefabricated room - process equipment"),
    "03.A.105" => array(16, "Germination 1", 0, "Prefabricated room - process equipment"),
    "03.A.106" => array(16, "Germination 2", 0, "Prefabricated room - process equipment"),
    "03.A.107" => array(16, "Germination 2", 0, "Prefabricated room - process equipment"),
    "03.A.108" => array(16, "Plant inspection ", 0, ""),
    "03.A.109" => array(16, "Seeding", 0, "Room by architecutre - precise equipment needed to be determined"),
    "03.B.101" => array(16, "Plant in gutters to greenhouse", 0, "Process eqpm - architecure to provide a basically finished area"),
    "03.B.102" => array(16, "Plant inspection", 0, "Process eqpm - architecure to provide a basically finished area"),
    "03.B.103" => array(16, "Plant transfer to gutters towards production", 0, "Process eqpm - architecure to provide a basically finished area"),
    "03.B.104" => array(16, "Plant transfer to towards pilot plant", 0, "Process eqpm - architecure to provide a basically finished area"),
    "03.C.102" => array(16, "Control Room", 0, "To be coordonated with process eqpm"),
    "03.C.103" => array(16, "Headhouse Circulation", 0, ""),
    "03.C.104" => array(16, "Janitor", 0, "Equipment to be coordonated with cleaning procedures"),
    "03.C.105" => array(16, "Lift to transfer (pilot plant)", 0, ""),
    "03.C.106" => array(16, "Washing Area", 0, "Equipment to be coordonated with cleaning procedures"),
    "03.D.101" => array(16, "Exit Vestibule Female", 0, ""),
    "03.D.102" => array(16, "Exit Vestibule Male", 0, ""),
    "03.D.103" => array(16, "Gown distribution Female", 0, ""),
    "03.D.104" => array(16, "Gown distribution Male", 0, ""),
    "03.D.105" => array(16, "Gowning In/Out Female", 0, ""),
    "03.D.106" => array(16, "Gowning In/Out Male", 0, ""),
    "03.D.107" => array(16, "Janitor", 0, ""),
    "03.D.108" => array(16, "Lockers (25) Female", 0, "Numbers of lockers to be confirmed"),
    "03.D.109" => array(16, "Lockers (25) Male", 0, "Numbers of lockers to be confirmed"),
    "03.D.110" => array(16, "Showers Female", 0, ""),
    "03.D.111" => array(16, "Showers Male", 0, ""),
    "03.D.112" => array(16, "Vestibule", 0, ""),
    "03.D.113" => array(16, "Vestibule", 0, ""),
    "04.A.101" => array(16, "Cell expansion production", 0, "Separated room - eqpm and bench space to be refined"),
    "04.A.102" => array(16, "Cold Room", 0, "2016-08-03
Included in inoculum preparation in a separeted room "),
    "04.A.103" => array(16, "Inoculum preparation", 0, "2016: Includes a weighing zone of 325 sf."),
    "04.A.201" => array(14, "Corridor CNC", 0, ""),
    "04.A.202" => array(14, "Infiltration solution preparation Line 1 & 2", 0, "Detailled cooridnation with process equipemnt to be done"),
    "04.A.203" => array(14, "Infiltration solution preparation Line 1 & 2", 0, "Detailled cooridnation with process equipemnt to be done"),
    "04.A.204" => array(14, "Plant infiltration Line 1 & 2", 0, "Additionnal requirement for eqpm to be discussed "),
    "04.A.205" => array(14, "Plant infiltration Line 1 & 2", 0, "Additionnal requirement for eqpm to be discussed "),
    "04.A.206" => array(14, "SAS Infiltration Line 1 & 2", 0, ""),
    "04.A.207" => array(14, "SAS Infiltration Line 1 & 2", 0, ""),
    "04.A.208" => array(14, "Shaft to infiltration CNC", 0, "Finishes of shaft to be explored"),
    "04.A.209" => array(14, "Washing area Line 1 & 2", 0, "Required eqpm to be defined as well as walls protection"),
    "04.A.210" => array(14, "Washing area Line 1 & 2", 0, "Required eqpm to be defined as well as walls protection"),
    "04.A.301" => array(14, "AGV Retour Corridor", 0, ""),
    "04.A.302" => array(14, "AGV Supply Corridor", 0, ""),
    "04.A.303" => array(14, "Gutter ciruclation shaft", 0, "Finishes of shaft to be explored"),
    "04.A.304" => array(14, "Plant Incubator", 0, "Space for a total of 15 rooms (+/-4m X 20m each)     "),
    "04.A.305" => array(14, "Plant Incubator", 0, "Space for a total of 15 rooms (+/-4m X 20m each)     "),
    "04.A.306" => array(14, "Plant Incubator", 0, "Space for a total of 15 rooms (+/-4m X 20m each)     "),
    "04.A.307" => array(14, "Plant Incubator", 0, "Space for a total of 15 rooms (+/-4m X 20m each)     "),
    "04.A.308" => array(14, "Plant Incubator", 0, "Space for a total of 15 rooms (+/-4m X 20m each)     "),
    "04.A.309" => array(14, "Plant Incubator", 0, "Space for a total of 15 rooms (+/-4m X 20m each)     "),
    "04.A.310" => array(14, "Plant Incubator", 0, "Space for a total of 15 rooms (+/-4m X 20m each)     "),
    "04.A.311" => array(14, "Plant Incubator", 0, "Space for a total of 15 rooms (+/-4m X 20m each)     "),
    "04.A.312" => array(14, "Plant Incubator", 0, "Space for a total of 15 rooms (+/-4m X 20m each)     "),
    "04.A.313" => array(14, "Plant Incubator", 0, "Space for a total of 15 rooms (+/-4m X 20m each)     "),
    "04.A.314" => array(14, "Plant Incubator", 0, "Space for a total of 15 rooms (+/-4m X 20m each)     "),
    "04.A.315" => array(14, "Plant Incubator", 0, "Space for a total of 15 rooms (+/-4m X 20m each)     "),
    "04.A.316" => array(14, "Plant Incubator", 0, "Space for a total of 15 rooms (+/-4m X 20m each)     "),
    "04.A.317" => array(14, "Plant Incubator", 0, "Space for a total of 15 rooms (+/-4m X 20m each)     "),
    "04.A.318" => array(14, "Plant Incubator", 0, "Space for a total of 15 rooms (+/-4m X 20m each)     "),
    "04.A.401" => array(16, "Harvest Line 1 & 2", 0, "Total for 2 parallel lines with trimmer, chopper, shaker"),
    "04.A.402" => array(16, "Harvest Line 1 & 2", 0, "Total for 2 parallel lines with trimmer, chopper, shaker"),
    "04.B.101" => array(-1, "Corridor CNC", 0, ""),
    "04.B.102" => array(14, "Gutter circulation shaft", 0, "Finishes of shaft to be explored"),
    "04.B.103" => array(14, "Gutter circulation shaft", 0, "Finishes of shaft to be explored"),
    "04.B.104" => array(16, "Office corridor", 0, ""),
    "04.B.201" => array(16, "Airlock", 0, ""),
    "04.B.202" => array(16, "Airlock CNC", 0, ""),
    "04.B.203" => array(16, "Airlock CNC", 0, ""),
    "04.B.204" => array(16, "Airlock CNC", 0, ""),
    "04.B.205" => array(16, "Airlock CNC", 0, ""),
    "04.B.206" => array(14, "AGV control room", 0, "Assumed as required - to be analysed in detailled design"),
    "04.B.207" => array(14, "AGV repair work shop", 0, "Assumed as required - to be analysed in detailled design"),
    "04.B.208" => array(16, "Autoclave", 0, ""),
    "04.B.209" => array(16, "Autoclave", 0, ""),
    "04.B.210" => array(14, "MAL IN / OUT ", 0, "See drawings for proposed equipment"),
    "04.B.211" => array(-1, "MAL IN / OUT CNC", 0, "See drawings for proposed equipment"),
    "04.B.212" => array(-1, "MAL IN / OUT CNC", 0, "See drawings for proposed equipment"),
    "04.B.213" => array(-1, "MAL IN / OUT CNC", 0, "See drawings for proposed equipment"),
    "04.B.214" => array(-1, "MAL IN / OUT CNC", 0, "See drawings for proposed equipment"),
    "04.B.215" => array(-1, "MAL IN / OUT CNC", 0, "See drawings for proposed equipment"),
    "04.B.216" => array(-1, "MAL IN / OUT CNC", 0, "See drawings for proposed equipment"),
    "04.B.217" => array(14, "MAL OUT CNC ", 0, "See drawings for proposed equipment"),
    "04.B.218" => array(14, "MAL OUT CNC ", 0, "See drawings for proposed equipment"),
    "04.B.219" => array(14, "PAL IN CNC", 0, "See drawings for proposed equipment"),
    "04.B.220" => array(14, "PAL IN CNC", 0, "See drawings for proposed equipment"),
    "04.B.221" => array(14, "PAL IN CNC", 0, "See drawings for proposed equipment"),
    "04.B.222" => array(14, "PAL IN CNC", 0, "See drawings for proposed equipment"),
    "04.B.223" => array(14, "PAL IN CNC", 0, "See drawings for proposed equipment"),
    "04.B.224" => array(14, "PAL IN/OUT", 0, "See drawings for proposed equipment"),
    "04.B.225" => array(14, "PAL IN/OUT CNC", 0, "See drawings for proposed equipment"),
    "04.B.226" => array(-1, "PAL OUT CNC", 0, "See drawings for proposed equipment"),
    "04.B.227" => array(-1, "PAL OUT CNC", 0, "See drawings for proposed equipment"),
    "04.B.228" => array(-1, "PAL OUT CNC", 0, "See drawings for proposed equipment"),
    "04.B.229" => array(-1, "PAL OUT CNC", 0, "See drawings for proposed equipment"),
    "04.B.230" => array(-1, "PAL OUT CNC", 0, "See drawings for proposed equipment"),
    "04.B.231" => array(14, "PAL/MAL IN CNC", 0, "See drawings for proposed equipment"),
    "04.B.232" => array(-1, "PAL/MAL IN/OUT CNC", 0, "See drawings for proposed equipment"),
    "04.B.233" => array(-1, "PAL/MAL IN/OUT CNC", 0, "See drawings for proposed equipment"),
    "04.B.234" => array(14, "PAL/MAL OUT CNC", 0, "See drawings for proposed equipment"),
    "04.B.301" => array(16, "Meeting Room", 0, "Inside production area - finishes as CNC corridor for cleanability"),
    "04.B.302" => array(16, "Office space", 0, "Inside production area - finishes as CNC corridor for cleanability"),
    "04.B.401" => array(14, "Gutters washing ", 0, "Size of room coordinated with process - additionnal equipment to be discussed in detailled design"),
    "04.B.402" => array(16, "Soiled wash area D", 0, "Specific fix equipment to be added in detailled design to accomodate procedures"),
    "04.B.403" => array(16, "Sterile material holding C", 0, "Specific fix equipment to be added in detailled design to accomodate procedures"),
    "04.B.404" => array(16, "Sterile material holding C", 0, "Specific fix equipment to be added in detailled design to accomodate procedures"),
    "04.B.405" => array(16, "Washer", 0, "Specific fix equipment to be added in detailled design to accomodate procedures"),
    "04.B.406" => array(16, "Washer", 0, "Specific fix equipment to be added in detailled design to accomodate procedures"),
    "04.B.501" => array(16, "Clean gutters staging", 0, "Assumed to accomodate two batches of gutters - 1m x 1.5m x 3.5m of size each"),
    "04.B.502" => array(16, "Clean Storage/Wrapping C", 0, "2016                                                                   1)includes prepation area           "),
    "04.B.503" => array(16, "Clean Storage/Wrapping C", 0, "2016                                                                   1)includes prepation area           "),
    "04.C.101" => array(16, "Enzyme Digestion room A Line  1 & 2", 0, "Two digesters per lline, in separated rooms, accesible from a common gray corridor.  SST bench space added after user meeting. Higher ceilings required."),
    "04.C.102" => array(16, "Enzyme Digestion room A Line  1 & 2", 0, "Two digesters per lline, in separated rooms, accesible from a common gray corridor.  SST bench space added after user meeting. Higher ceilings required."),
    "04.C.103" => array(16, "Enzyme Digestion room B Line 1 & 2", 0, "Two digesters per lline, in separated rooms, accesible from a common gray corridor.  SST bench space added after user meeting. Higher ceilings required."),
    "04.C.104" => array(16, "Enzyme Digestion room B Line 1 & 2", 0, "Two digesters per lline, in separated rooms, accesible from a common gray corridor.  SST bench space added after user meeting. Higher ceilings required."),
    "04.C.105" => array(16, "Supply & retour Digestion", 0, "Two digesters per lline, in separated rooms, accesible from a common gray corridor.  SST bench space added after user meeting. Higher ceilings required."),
    "04.C.106" => array(16, "Supply & retour Digestion", 0, "Two digesters per lline, in separated rooms, accesible from a common gray corridor.  SST bench space added after user meeting. Higher ceilings required."),
    "04.C.201" => array(16, "Centrifugation/Clarification Line 1 & 2", 0, "High eqpm required high ceilling"),
    "04.C.202" => array(16, "Centrifugation/Clarification Line 1 & 2", 0, "High eqpm required high ceilling"),
    "04.C.301" => array(16, "Column packing room Line 1 & 2", 0, "Separated rooms added after user meeting - assumed to be for pre-viral only."),
    "04.C.302" => array(16, "Column packing room Line 1 & 2", 0, "Separated rooms added after user meeting - assumed to be for pre-viral only."),
    "04.C.303" => array(16, "Purification Line 1 & 2", 0, "Previral steps, TFF 1, Caption and UV
Separation between Purification 1 and 2"),
    "04.C.304" => array(16, "Purification Line 1 & 2", 0, "Previral steps, TFF 1, Caption and UV
Separation between Purification 1 and 2"),
    "04.C.401" => array(16, "Purification Line 1 & 2 ", 0, "Post Viral steps, Anion & TFF 2 / 
Added extra space for TFF-2 Seasonal (SNC_2014-08-08)"),
    "04.C.402" => array(16, "Purification Line 1 & 2 ", 0, "Post Viral steps, Anion & TFF 2 / 
Added extra space for TFF-2 Seasonal (SNC_2014-08-08)"),
    "04.C.501" => array(16, "Sterile bulk filtration", 0, "1 Room shared by both trains (SNC_2014-08-08)"),
    "04.D.101" => array(16, "Controled production corridor D", 0, "Production corridor to achieve unidirectionnal flow"),
    "04.D.102" => array(16, "Corridor material D", 0, "Production corridor to achieve unidirectionnal flow"),
    "04.D.103" => array(16, "Return corridor C", 0, "Production corridor to achieve unidirectionnal flow"),
    "04.D.104" => array(16, "Supply corridor C", 0, "Production corridor to achieve unidirectionnal flow"),
    "04.D.105" => array(16, "Supply corridor C", 0, "Production corridor to achieve unidirectionnal flow"),
    "04.D.201" => array(16, "MAL IN/OUT C", 0, "See drawings for proposed equipment"),
    "04.D.202" => array(16, "MAL IN/OUT C", 0, "See drawings for proposed equipment"),
    "04.D.203" => array(16, "MAL IN/OUT C", 0, "See drawings for proposed equipment"),
    "04.D.204" => array(16, "MAL IN/OUT D", 0, "See drawings for proposed equipment"),
    "04.D.205" => array(16, "MAL OUT C", 0, "See drawings for proposed equipment"),
    "04.D.206" => array(16, "PAL IN C", 0, "See drawings for proposed equipment"),
    "04.D.207" => array(16, "PAL IN C", 0, "See drawings for proposed equipment"),
    "04.D.208" => array(16, "PAL IN D", 0, "See drawings for proposed equipment"),
    "04.D.209" => array(16, "PAL IN D", 0, "See drawings for proposed equipment"),
    "04.D.210" => array(16, "PAL IN D", 0, "See drawings for proposed equipment"),
    "04.D.211" => array(16, "PAL/MAL OUT C ", 0, "See drawings for proposed equipment"),
    "04.D.212" => array(16, "PAL/MAL OUT D", 0, "See drawings for proposed equipment"),
    "04.D.301" => array(16, "Office", 0, "2016-05-20 New rooms added"),
    "04.D.302" => array(16, "Office", 0, "2016-05-20 New rooms added"),
    "04.D.401" => array(16, "Autoclave", 0, "Specific fix equipment to be added in detailled design to accomodate procedures.  Protection on wall and techinques of washing to be discussed in the detailled phase.  Classification of these room to be discussed in detailled design - CNC or C and D."),
    "04.D.402" => array(16, "Clean Wrapping C", 0, "Specific fix equipment to be added in detailled design to accomodate procedures.  Protection on wall and techinques of washing to be discussed in the detailled phase.  Classification of these room to be discussed in detailled design - CNC or C and D."),
    "04.D.403" => array(16, "Janitor", 0, "Specific fix equipment to be added in detailled design to accomodate procedures.  Protection on wall and techinques of washing to be discussed in the detailled phase.  Classification of these room to be discussed in detailled design - CNC or C and D."),
    "04.D.404" => array(16, "Janitor", 0, "Specific fix equipment to be added in detailled design to accomodate procedures.  Protection on wall and techinques of washing to be discussed in the detailled phase.  Classification of these room to be discussed in detailled design - CNC or C and D."),
    "04.D.405" => array(16, "Soiled wash area D", 0, "Specific fix equipment to be added in detailled design to accomodate procedures.  Protection on wall and techinques of washing to be discussed in the detailled phase.  Classification of these room to be discussed in detailled design - CNC or C and D."),
    "04.D.406" => array(16, "Sterile material holding C", 0, "Specific fix equipment to be added in detailled design to accomodate procedures.  Protection on wall and techinques of washing to be discussed in the detailled phase.  Classification of these room to be discussed in detailled design - CNC or C and D."),
    "04.D.407" => array(16, "Washer", 0, "Specific fix equipment to be added in detailled design to accomodate procedures.  Protection on wall and techinques of washing to be discussed in the detailled phase.  Classification of these room to be discussed in detailled design - CNC or C and D."),
    "04.E.101" => array(18, "Buffer Hold Tanks", 0, "High ceiling required as coordinated with SNC"),
    "04.E.102" => array(18, "Buffer Solution Preparation", 0, "High ceiling required as coordinated with SNC"),
    "04.E.103" => array(18, "Clean Component Storage", 0, ""),
    "04.E.104" => array(18, "Media Preparation ", 0, "No unidirectional flow needed as discussed in user meeting.  Layout modified in order to minimize C classified rooms.  Final adjustments on room size to be discussed in detailled design phase."),
    "04.E.105" => array(18, "Preweighed staging", 0, "No unidirectional flow needed as discussed in user meeting.  Layout modified in order to minimize C classified rooms.  Final adjustments on room size to be discussed in detailled design phase."),
    "04.E.106" => array(18, "Raw material storage", 0, "No unidirectional flow needed as discussed in user meeting.  Layout modified in order to minimize C classified rooms.  Final adjustments on room size to be discussed in detailled design phase."),
    "04.E.107" => array(18, "Storage Prepared Buffer/Media Bags", 0, "No unidirectional flow needed as discussed in user meeting.  Layout modified in order to minimize C classified rooms.  Final adjustments on room size to be discussed in detailled design phase."),
    "04.E.108" => array(18, "Weighing", 0, "No unidirectional flow needed as discussed in user meeting.  Layout modified in order to minimize C classified rooms.  Final adjustments on room size to be discussed in detailled design phase."),
    "04.F.101" => array(18, "Controlled General Plant Corridor ", 0, ""),
    "04.F.102" => array(18, "Corridor C", 0, ""),
    "04.F.103" => array(18, "Supply corridor C", 0, "Supply corridor proposed to minimize the quantity of air lock at each room"),
    "04.F.104" => array(18, "Supply Corridor D", 0, "Supply corridor proposed to minimize the quantity of air lock at each room"),
    "04.F.201" => array(18, "MAL D ", 0, "See drawings for proposed equipment"),
    "04.F.202" => array(18, "MAL IN D", 0, "See drawings for proposed equipment"),
    "04.F.203" => array(18, "MAL IN D", 0, "See drawings for proposed equipment"),
    "04.F.204" => array(18, "PAL/MAL IN/OUT C", 0, "See drawings for proposed equipment"),
    "04.F.205" => array(18, "PAL C", 0, "See drawings for proposed equipment"),
    "04.F.206" => array(18, "PAL D", 0, "See drawings for proposed equipment"),
    "04.F.207" => array(18, "PAL D", 0, "See drawings for proposed equipment"),
    "04.F.208" => array(18, "PAL IN/OUT D", 0, "See drawings for proposed equipment"),
    "04.F.209" => array(18, "PAL/MAL IN/OUT D", 0, "See drawings for proposed equipment"),
    "04.F.301" => array(18, "Buffer offices", 0, "2016-05-20 New room added                                                      "),
    "04.F.401" => array(18, "Autoclave", 0, "Specific fix equipment to be added in detailled design to accomodate procedures.  Protection on wall and techinques of washing to be discussed in the detailled phase.  Completely separated from washing area of pilot plant buffer preparation area"),
    "04.F.402" => array(18, "Sterile material holding", 0, "Specific fix equipment to be added in detailled design to accomodate procedures.  Protection on wall and techinques of washing to be discussed in the detailled phase.  Completely separated from washing area of pilot plant buffer preparation area"),
    "04.F.403" => array(18, "Wash area", 0, "Specific fix equipment to be added in detailled design to accomodate procedures.  Protection on wall and techinques of washing to be discussed in the detailled phase.  Completely separated from washing area of pilot plant buffer preparation area"),
    "04.F.404" => array(18, "Washing area clean", 0, "Specific fix equipment to be added in detailled design to accomodate procedures.  Protection on wall and techinques of washing to be discussed in the detailled phase.  Completely separated from washing area of pilot plant buffer preparation area"),
    "04.F.405" => array(18, "Wash area soiled", 0, "Specific fix equipment to be added in detailled design to accomodate procedures.  Protection on wall and techinques of washing to be discussed in the detailled phase.  Completely separated from washing area of pilot plant buffer preparation area"),
    "04.F.406" => array(18, "Washer", 0, "Specific fix equipment to be added in detailled design to accomodate procedures.  Protection on wall and techinques of washing to be discussed in the detailled phase.  Completely separated from washing area of pilot plant buffer preparation area"),
    "04.F.407" => array(18, "Washer", 0, "Specific fix equipment to be added in detailled design to accomodate procedures.  Protection on wall and techinques of washing to be discussed in the detailled phase.  Completely separated from washing area of pilot plant buffer preparation area"),
    "04.G.101" => array(18, "Buffer Hold Tanks", 0, "Completely separated from washing area of pilot plant buffer preparation area as required in user meeting discussion                                                 "),
    "04.G.102" => array(18, "Buffer Solution Preparation", 0, "Completely separated from washing area of pilot plant buffer preparation area as required in user meeting discussion                                                 "),
    "04.G.103" => array(18, "Clean Component Storage", 0, "Completely separated from washing area of pilot plant buffer preparation area as required in user meeting discussion                                                 "),
    "04.G.104" => array(18, "Pre-weighed", 0, "Completely separated from washing area of pilot plant buffer preparation area as required in user meeting discussion                                                 "),
    "04.G.105" => array(18, "Raw material storage", 0, "Completely separated from washing area of pilot plant buffer preparation area as required in user meeting discussion                                                 "),
    "04.G.106" => array(18, "Weighing", 0, "Completely separated from washing area of pilot plant buffer preparation area as required in user meeting discussion                                                 "),
    "04.H.101" => array(18, "Supply Corridor", 0, ""),
    "04.H.201" => array(18, "MAL C", 0, "2016-05-20 New room added                                                      "),
    "04.H.202" => array(18, "PAL C", 0, "2016-05-20 New room added                                                      "),
    "04.H.301" => array(18, "Wash area", 0, ""),
    "04.I.101" => array(18, "Cell Bank C", 0, "Centralized cell bank for production and pilot plant"),
    "04.J.101" => array(18, "PAL/MAL in C", 0, "2016/06/06 new room after user meeting on 2016/06/03 Combined PAL/MAL"),
    "04.J.102" => array(18, "PAL/MAL in D", 0, "2016/06/06 new room after user meeting on 2016/06/03 Combined PAL/MAL"),
    "04.J.103" => array(18, "PAL/MAL out C ", 0, "2016/06/06 new room after user meeting on 2016/06/03 Combined PAL/MAL"),
    "04.J.104" => array(18, "PAL/MAL out  D", 0, "2016/06/06 new room after user meeting on 2016/06/03 Combined PAL/MAL"),
    "04.J.201" => array(18, "Decon. Autoclave", 0, ""),
    "04.J.202" => array(18, "Decontaminated material holding ", 0, ""),
    "04.J.203" => array(18, "Sterile material holding", 0, "2016-05-20 Room added "),
    "04.J.204" => array(18, "Sterilization autoclave", 0, "2016-05-20 Room added "),
    "04.J.205" => array(18, "Wash area Soiled", 0, ""),
    "04.J.206" => array(18, "Wash area Clean/wrapping", 0, ""),
    "04.K.101" => array(16, "Clean Gown Storage - Female", 0, "See drawings for proposed organization"),
    "04.K.102" => array(16, "Clean Gown Storage - Male", 0, "See drawings for proposed organization"),
    "04.K.103" => array(16, "Gown-in Distribution - Female", 0, "See drawings for proposed organization"),
    "04.K.104" => array(16, "Gown-in Distribution - Male", 0, "See drawings for proposed organization"),
    "04.K.105" => array(16, "Gown-out - Female", 0, "See drawings for proposed organization"),
    "04.K.106" => array(16, "Gown-out - Male", 0, "See drawings for proposed organization"),
    "04.K.107" => array(16, "Janitor - Female", 0, "See drawings for proposed organization"),
    "04.K.108" => array(16, "Janitor - Male", 0, "See drawings for proposed organization"),
    "04.K.109" => array(16, "Janitor", 0, ""),
    "04.K.110" => array(16, "Janitor", 0, ""),
    "04.K.111" => array(16, "Lockers room - Female", 0, "2016-08-03 : Is 72 lockers enough?"),
    "04.K.112" => array(16, "Lockers room - Male", 0, "2016.08.03 : Is 85 lockers enough?"),
    "04.K.113" => array(16, "PAL IN contaminated - Female", 0, "2016-08-03 : room added"),
    "04.K.114" => array(16, "PAL IN contaminated - Male", 0, "2016.08-03 : room added"),
    "04.K.115" => array(16, "PAL OUT - Female", 0, ""),
    "04.K.116" => array(16, "PAL OUT - Male", 0, ""),
    "04.K.117" => array(16, "Shower - Female", 0, "Equipment to be discussed in detailled design"),
    "04.K.118" => array(16, "Shower - Male", 0, "Equipment to be discussed in detailled design"),
    "04.K.119" => array(16, "Soiled gown - Female", 0, ""),
    "04.K.120" => array(16, "Soiled gown - Male", 0, ""),
    "04.K.121" => array(16, "Vestibule - Female", 0, ""),
    "04.K.122" => array(16, "Vestibule - Male", 0, ""),
    "04.L.102" => array(16, "Controled production corridor CNC", 0, ""),
    "04.L.108" => array(16, "Return Process Corridor CNC", 0, ""),
    "04.L.109" => array(16, "Supply Process Corridor CNC", 0, ""),
    "04.L.201" => array(16, "A/L", 0, ""),
    "04.L.202" => array(16, "A/L", 0, ""),
    "04.L.203" => array(16, "A/L", 0, ""),
    "04.L.204" => array(16, "A/L", 0, ""),
    "04.L.205" => array(16, "Janitor", 0, ""),
    "04.L.206" => array(16, "MAL IN CNC", 0, "See drawings for proposed equipment"),
    "04.L.207" => array(16, "MAL IN CNC", 0, "See drawings for proposed equipment"),
    "04.L.208" => array(16, "MAL IN CNC", 0, "See drawings for proposed equipment"),
    "04.L.209" => array(16, "MAL IN CNC", 0, "See drawings for proposed equipment"),
    "04.L.210" => array(16, "MAL OUT CNC ", 0, "See drawings for proposed equipment"),
    "04.L.211" => array(16, "MAL OUT CNC ", 0, "See drawings for proposed equipment"),
    "04.L.212" => array(16, "PAL IN CNC", 0, "See drawings for proposed equipment"),
    "04.L.213" => array(16, "PAL IN CNC", 0, "See drawings for proposed equipment"),
    "04.L.214" => array(16, "PAL OUT CNC", 0, "See drawings for proposed equipment"),
    "04.L.301" => array(16, "Vertical circulation sas", 0, "To provide control on pressurization of room"),
    "04.L.303" => array(16, "Vertical circulation sas", 0, "To provide control on pressurization of room"),
    "05.A.101" => array(18, "Sample Reception", 0, "Type of pass thru and size to be discussed in DD"),
    "05.B.101" => array(18, "Biochemistry Laboratory", 0, "1) Physiochemistry, Virology and Immunology Labs were combined together to become Biochemistry Lab
2) Need dedicated area for Weighning stations and instrument area for equipment and HPLCs within room"),
    "05.B.102" => array(18, "Biomolecular Laboratory", 0, "Services and euipment requirement to be discussed in the detailled phase.  Built in furniture layout as discussed in users meetings"),
    "05.B.103" => array(18, "Dark Room", 0, "Services and euipment requirement to be discussed in the detailled phase.  Built in furniture layout as discussed in users meetings"),
    "05.B.104" => array(18, "Equipment room", 0, "Services and euipment requirement to be discussed in the detailled phase.  Built in furniture layout as discussed in users meetings"),
    "05.B.105" => array(18, "Weighing station", 0, "Services and euipment requirement to be discussed in the detailled phase.  Built in furniture layout as discussed in users meetings"),
    "05.B.201" => array(18, "Preparation (buffers, solution)", 0, ""),
    "05.B.301" => array(18, "Instrument room", 0, "Services and euipment requirement to be discussed in the detailled phase.Built in furniture layout as discussed in users meetings"),
    "05.B.302" => array(18, "Microbiology Laboratory ", 0, "Services and euipment requirement to be discussed in the detailled phase.Built in furniture layout as discussed in users meetings"),
    "05.C.101" => array(18, "QC corridor", 0, "Separated from the facility with PAL and MAL"),
    "05.C.201" => array(-1, "Air lock", 0, ""),
    "05.C.202" => array(-1, "Air lock", 0, ""),
    "05.C.203" => array(-1, "Air lock", 0, ""),
    "05.C.204" => array(18, "Janitor closet", 0, ""),
    "05.C.205" => array(18, "MAL IN", 0, "See drawings for proposed equipment"),
    "05.C.206" => array(18, "PAL IN / OUT ", 0, "See drawings for proposed equipment"),
    "05.C.301" => array(18, "Decontamination autoclave", 0, "Capacity required to be further discussed in detailled design phase"),
    "05.C.302" => array(18, "Contaminated material holding area", 0, "Capacity required to be further discussed in detailled design phase"),
    "05.C.303" => array(18, "Decontamination material holding area (Out of containment area)", 0, "Capacity required to be further discussed in detailled design phase"),
    "05.C.304" => array(18, "Waste", 0, "Capacity required to be further discussed in detailled design phase"),
    "05.B.401" => array(18, "MAL IN/OUT C", 0, "See drawings for proposed equipment"),
    "05.C.401" => array(18, "Cold room Stability Testing ", 0, "2014-08-12 Between 2 and 8° Celsius"),
    "05.B.402" => array(18, "MAL IN/OUT D", 0, "See drawings for proposed equipment"),
    "05.C.402" => array(18, "Storage cold room", 0, "2014-08-12 Between 2 and 8° Celsius"),
    "05.B.403" => array(18, "PAL IN/OUT C", 0, "See drawings for proposed equipment"),
    "05.C.403" => array(18, "Storage ambient temp.", 0, "2014-08-12 At 22° Celsius"),
    "05.B.404" => array(18, "PAL IN/OUT D", 0, "See drawings for proposed equipment"),
    "05.C.404" => array(18, "Storage incubation", 0, "1 room at 25° Celsius and 1 room at 30° Celsius                                                                Combine 2 incubation storage following comments from Medicago - Separate incubators in same room"),
    "05.B.405" => array(18, "Sterility testing laboratory", 0, "C laboratory with class A under laminar hood"),
    "05.C.501" => array(18, "Autoclave", 0, "Specific fix equipment to be added in detailled design to accomodate procedures.  Protection on wall and techinques of washing to be discussed in the detailled phase."),
    "05.C.502" => array(18, "Sterile material holding", 0, "Specific fix equipment to be added in detailled design to accomodate procedures.  Protection on wall and techinques of washing to be discussed in the detailled phase."),
    "05.C.503" => array(18, "Wash area - Clean / Wrapping", 0, "Specific fix equipment to be added in detailled design to accomodate procedures.  Protection on wall and techinques of washing to be discussed in the detailled phase."),
    "05.C.504" => array(18, "Wash area - Soiled", 0, "Specific fix equipment to be added in detailled design to accomodate procedures.  Protection on wall and techinques of washing to be discussed in the detailled phase."),
    "05.C.505" => array(18, "Washer", 0, "Specific fix equipment to be added in detailled design to accomodate procedures.  Protection on wall and techinques of washing to be discussed in the detailled phase."),
    "06.A.101" => array(16, "Air Lock", 0, ""),
    "06.A.102" => array(16, "Charging Station", 0, "Exact needs to be discussed in DD"),
    "06.A.103" => array(16, "Expedition Preparation area", 0, "Floor space for pallets and eqpm TBD"),
    "06.A.104" => array(16, "Mail room ", 0, "Loose furniture by owner"),
    "06.A.105" => array(16, "Receiving/Staging", 0, "Proposed racking to accomodate 8 pallets"),
    "06.A.106" => array(16, "Shipping/Receiving Reception office", 0, "Loose furniture by owner"),
    "06.A.107" => array(16, "Vestibule (Drivers)", 0, "Interphone to contact Reception office"),
    "06.A.108" => array(16, "Warehouse Reception Office", 0, "Common space with 6.106"),
    "06.A.109" => array(16, "Warehouse Shipping/Receiving Dock", 0, "Free space for circulation of pallets"),
    "06.B.101" => array(16, "Alcohol Dilution Preparation", 0, "To be further developped according to codes and needs of Medicago"),
    "06.B.102" => array(16, "Bulk corrosive material storage ", 0, "16 pallets capacity - to confirm in DD"),
    "06.B.103" => array(16, "Bulk Flammable material storage", 0, "Accomodate drum storage for alcohol - floor space"),
    "06.B.104" => array(16, "Cold room raw materials", 0, "16 pallets capacity - to confirm i nDD"),
    "06.B.105" => array(16, "Material Staging", 0, "Floor space prior to delivery in departments"),
    "06.B.106" => array(16, "Raw materials holding - Quarantine", 0, "12 pallets capacity - procedure to confirm"),
    "06.B.107" => array(16, "Raw materials storage - Released", 0, "400 pallets - Racking an eqpm by owner"),
    "06.B.108" => array(16, "Storage of dispensed alcohol", 0, ""),
    "06.B.109" => array(16, "A/L", 0, ""),
    "06.B.201" => array(16, "Air shower", 0, ""),
    "06.B.202" => array(16, "MAL IN D", 0, "See drawings for proposed equipment"),
    "06.B.203" => array(16, "MAL IN/OUT D", 0, "See drawings for proposed equipment"),
    "06.B.204" => array(16, "MAL OUT D", 0, "See drawings for proposed equipment"),
    "06.B.205" => array(16, "PAL IN/OUT C", 0, "See drawings for proposed equipment"),
    "06.B.206" => array(16, "PAL IN/OUT D", 0, "See drawings for proposed equipment"),
    "06.B.207" => array(16, "Pallet deposit", 0, "See drawings for proposed equipment"),
    "06.B.208" => array(16, "Quality Control Sampling C", 0, "See drawings for proposed equipment"),
    "06.B.301" => array(16, "Air Lock", 0, ""),
    "06.B.302" => array(16, "Janitor", 0, ""),
    "06.B.303" => array(16, "MAL IN CNC", 0, "See drawings for proposed equipment"),
    "06.B.304" => array(16, "MAL IN/OUT C", 0, "See drawings for proposed equipment"),
    "06.B.305" => array(16, "MAL OUT CNC", 0, "See drawings for proposed equipment"),
    "06.B.306" => array(16, "MAL OUT D", 0, "See drawings for proposed equipment"),
    "06.B.307" => array(16, "PAL IN/OUT CNC (access to production)", 0, "See drawings for proposed equipment"),
    "06.B.308" => array(16, "Warehouse General Circulation", 0, ""),
    "06.C.101" => array(16, "Bulk Packaged Cold Room", 0, "Large cold room with full redundancy - to be coordination at Detailled desing phase"),
    "06.C.102" => array(16, "Bulk Shipping dock (finish product shipping)", 0, "Dedicated shipping dock"),
    "06.C.103" => array(16, "Sterile Bulk Cold Storage (finish product shipping)", 0, "Storage of 300 containers x 40L per year - to be reviewed with new 2016 capacity"),
    "06.C.104" => array(16, "Sterile Bulk Packaging", 0, "In a cold room - redundancy to be discussed in DD"),
    "06.D.101" => array(16, "A/L", 0, ""),
    "06.D.102" => array(16, "A/L", 0, ""),
    "06.D.103" => array(16, "A/L", 0, ""),
    "06.D.104" => array(16, "A/L", 0, ""),
    "06.D.105" => array(14, "PAL/MAL IN/OUT", 0, ""),
    "06.D.110" => array(-1, "Waste Catwalk", 0, ""),
    "06.D.111" => array(-1, "Waste Catwalk", 0, ""),
    "06.D.112" => array(-1, "Waste Dumbwaiters", 0, "To be further detailled in Detailled design phase"),
    "06.D.113" => array(-1, "Waste Dumbwaiters", 0, "To be further detailled in Detailled design phase"),
    "06.D.114" => array(-1, "Waste Dumbwaiters", 0, "To be further detailled in Detailled design phase"),
    "06.D.115" => array(-1, "Waste Dumbwaiters", 0, "To be further detailled in Detailled design phase"),
    "06.D.116" => array(-1, "Waste Dumbwaiters", 0, "To be further detailled in Detailled design phase"),
    "06.D.117" => array(-1, "Waste Dumbwaiters", 0, "To be further detailled in Detailled design phase"),
    "06.D.118" => array(-1, "Waste Dumbwaiters", 0, "To be further detailled in Detailled design phase"),
    "06.D.119" => array(-1, "Waste Dumbwaiters", 0, "To be further detailled in Detailled design phase"),
    "06.D.201" => array(14, "Corrosive Waste", 0, "To be discussed in detailled phase for type of waste and volume to be handled"),
    "06.D.202" => array(14, "Flammable Waste", 0, "To be discussed in detailled phase for type of waste and volume to be handled"),
    "06.D.203" => array(14, "Recycling Waste", 0, "To be discussed in detailled phase for type of waste and volume to be handled"),
    "06.D.204" => array(14, "Regular waste", 0, "Exterior Compactor "),
    "06.D.205" => array(14, "Waste Shipping Area", 0, "Maybe waste dock at GF could be reduced"),
    "06.D.206" => array(14, "Waste shipping dock (produciton waste)", 0, ""),
    "06.E.101" => array(14, "Contaminated waste holding", 0, ""),
    "06.E.102" => array(14, "Waste decontamination - Continuous Autoclave", 0, "2 Autoclaves"),
    "06.E.103" => array(14, "Decontaminated waste holding area", 0, "Volume to be determined in Detailled Design"),
    "06.E.104" => array(14, "MAL IN/OUT", 0, "See drawings for proposed equipment"),
    "06.E.105" => array(14, "PAL IN/OUT", 0, "See drawings for proposed equipment"),
    "06.E.106" => array(14, "PAL/MAL IN/OUT", 0, "See drawings for proposed equipment"),
    "06.E.107" => array(14, "Waste shipping dock", 0, "1 doors - Includes space for dumpster inside"),
    "06.F.101" => array(16, "Exit Vestibule - Female", 0, "Capacity required to be further analysed in detailled Design"),
    "06.F.102" => array(16, "Exit Vestibule - Male", 0, "Capacity required to be further analysed in detailled Design"),
    "06.F.103" => array(16, "Janitor", 0, "Capacity required to be further analysed in detailled Design"),
    "06.F.104" => array(16, "Janitor", 0, "Capacity required to be further analysed in detailled Design"),
    "06.F.105" => array(16, "Lockers Female", 0, "Capacity required to be further analysed in detailled Design"),
    "06.F.106" => array(16, "Lockers Male", 0, "Capacity required to be further analysed in detailled Design"),
    "06.F.107" => array(16, "Shower Female", 0, "Capacity required to be further analysed in detailled Design"),
    "06.F.108" => array(16, "Shower Male", 0, "Capacity required to be further analysed in detailled Design"),
    "06.F.109" => array(16, "Vestibule", 0, ""),
    "06.F.110" => array(16, "Vestibule", 0, ""),
    "06.G.101" => array(16, "A/L", 0, ""),
    "06.G.102" => array(16, "Greenhouse Shipping/Receiving Dock", 0, ""),
    "06.G.103" => array(16, "Office", 0, ""),
    "06.G.104" => array(16, "Plant Growth Cold Warehouse", 0, ""),
    "06.G.105" => array(16, "Plant growth warehouse air lock", 0, ""),
    "06.G.106" => array(16, "Plant growth warehouse pallets", 0, "24 palettes au sol de 2,400mm de hauteur (rangée double au sol) + 12 pallettes au-dessus pour matériel (rangée simple au-dessus)"),
    "06.G.107" => array(16, "Vestibule (drivers)", 0, ""),
    "06.G.108" => array(16, "Waste Shipping", 0, ""),
    "07.A.101" => array(16, "Inoculum prep pilot", 0, "1) shared between GMP Stream and R&D Stream
2) area to be confirmed, hypothesis : half the area of Inoculum Prep room #147 in North Carolina "),
    "07.B.101" => array(16, "Harvesting - R&D", 0, "Eqpm to be used to be analysed in detailled phase"),
    "07.B.102" => array(16, "Incubation - R&D", 0, "Eqpm to be used to be analysed in detailled phase"),
    "07.B.103" => array(16, "Incubation - R&D", 0, "Eqpm to be used to be analysed in detailled phase"),
    "07.B.104" => array(16, "Infiltration - R&D", 0, "Eqpm to be used to be analysed in detailled phase"),
    "07.B.105" => array(16, "Staging / Work Area / Harvesting R&D", 0, ""),
    "07.B.106" => array(16, "Sample Circulation", 0, ""),
    "07.B.107" => array(16, "Wash area ", 0, ""),
    "07.C.101" => array(16, "Clarification / Filtration", 0, "CNC"),
    "07.C.102" => array(16, "Clean Gutter Storage", 0, "Changed from Clean Storage when decided to use gutters in PP   "),
    "07.C.103" => array(16, "Gutter Washing", 0, "Changed from Washing Suite when decided to use gutters in PP                  "),
    "07.C.104" => array(16, "Harvesting / Dicing / Extraction-Digestion", 0, "Layout form Medicago and SNC Process team"),
    "07.C.105" => array(16, "Incubation GMP", 0, "Layout form Medicago and SNC Process team"),
    "07.C.106" => array(16, "Incubation GMP", 0, "Layout form Medicago and SNC Process team"),
    "07.C.107" => array(16, "Infiltration GMP", 0, "Layout form Medicago and SNC Process team"),
    "07.C.108" => array(16, "Staging / Work Area / Hall", 0, ""),
    "07.D.102" => array(16, "Controlled Corridor CNC", 0, ""),
    "07.D.201" => array(16, "Carts Holding Area", 0, ""),
    "07.D.202" => array(16, "Plant Corridor to Pilot Plant ", 0, ""),
    "07.D.203" => array(16, "Plant reception", 0, "To be coordonated in Detailled Design with Process"),
    "07.D.204" => array(16, "Plant transfer towards Pilot Plant", 0, ""),
    "07.D.301" => array(16, "Air lock CNC", 0, ""),
    "07.D.302" => array(16, "Air lock CNC", 0, ""),
    "07.D.303" => array(16, "Janitor CNC", 0, ""),
    "07.D.304" => array(16, "MAL IN/OUT Upstream", 0, ""),
    "07.D.305" => array(16, "PAL IN CNC", 0, ""),
    "07.D.306" => array(16, "PAL OUT CNC", 0, ""),
    "07.D.401" => array(16, "Sterile material holding CNC", 0, "Specific fix equipment to be added in detailled design to accomodate procedures.  Protection on wall and techinques of washing to be discussed in the detailled phase. "),
    "07.D.402" => array(16, "Sterilizer", 0, ""),
    "07.D.403" => array(16, "Washing Area - clean (wrapping) CNC", 0, ""),
    "07.D.404" => array(16, "Washing Area - soiled CNC", 0, ""),
    "07.D.405" => array(16, "Washer", 0, ""),
    "07.E.101" => array(16, "Formulation, Formulation & Filling B", 0, "B classified rooms. Gowning procedures to be analysed in Detailled Desing phase"),
    "07.E.102" => array(16, "POSTVIRAL Purification 2 (Chromatro 2 + TFF2)", 0, "Suply and return corridor for these rooms in order to keep segregation "),
    "07.E.103" => array(16, "PREVIRAL Purification 1 (Chromatro 1 + UVC)", 0, "Suply and return corridor for these rooms in order to keep segregation "),
    "07.F.101" => array(16, "Corridor CNC", 0, ""),
    "07.F.102" => array(16, "Corridor D", 0, ""),
    "07.F.103" => array(16, "Return corridor C", 0, "In order to provide unidirectionnal flow into the B classified area"),
    "07.F.104" => array(16, "Supply corridor C", 0, "In order to provide unidirectionnal flow into the B classified area"),
    "07.F.201" => array(16, "Packing Material Storage D", 0, ""),
    "07.F.202" => array(16, "Released Product Cold Room D", 0, ""),
    "07.F.203" => array(16, "WIP Cold Room B", 0, ""),
    "07.F.204" => array(16, "WIP Cold Room D", 0, ""),
    "07.F.301" => array(16, "Anteroom B", 0, ""),
    "07.F.302" => array(16, "Emergency A/L", 0, ""),
    "07.F.303" => array(16, "Janitor ", 0, ""),
    "07.F.304" => array(16, "Janitor ", 0, ""),
    "07.F.305" => array(16, "MAL IN B", 0, "See drawings for proposed equipment"),
    "07.F.306" => array(16, "MAL IN/OUT DOWNSTREAM D", 0, "See drawings for proposed equipment"),
    "07.F.307" => array(16, "MAL OUT C", 0, "See drawings for proposed equipment"),
    "07.F.308" => array(16, "PAL IN B", 0, "See drawings for proposed equipment"),
    "07.F.309" => array(16, "PAL IN B", 0, "See drawings for proposed equipment"),
    "07.F.310" => array(16, "PAL IN C", 0, "See drawings for proposed equipment"),
    "07.F.311" => array(16, "PAL IN D", 0, "See drawings for proposed equipment"),
    "07.F.312" => array(16, "PAL OUT C", 0, "See drawings for proposed equipment"),
    "07.F.313" => array(16, "PAL OUT D", 0, "See drawings for proposed equipment"),
    "07.F.314" => array(16, "PAL/MAL IN C", 0, "See drawings for proposed equipment"),
    "07.F.315" => array(16, "PAL/MAL IN C", 0, "See drawings for proposed equipment"),
    "07.F.316" => array(16, "PAL/MAL OUT C", 0, "See drawings for proposed equipment"),
    "07.F.317" => array(16, "PAL/MAL OUT C", 0, "See drawings for proposed equipment"),
    "07.F.318" => array(16, "Reagent Laboratory", 0, ""),
    "07.F.319" => array(16, "Visual Inspection / Packaging D", 0, ""),
    "07.F.401" => array(16, "Sterilizer", 0, "Specific fix equipment to be added in detailled design to accomodate procedures.  Protection on wall and techinques of washing to be discussed in the detailled phase. "),
    "07.F.402" => array(16, "Washing Area - clean (wrapping)", 0, "Specific fix equipment to be added in detailled design to accomodate procedures.  Protection on wall and techinques of washing to be discussed in the detailled phase. "),
    "07.F.403" => array(16, "Washing Area - soiled", 0, "Specific fix equipment to be added in detailled design to accomodate procedures.  Protection on wall and techinques of washing to be discussed in the detailled phase. "),
    "07.F.404" => array(16, "Washer", 0, "Specific fix equipment to be added in detailled design to accomodate procedures.  Protection on wall and techinques of washing to be discussed in the detailled phase. "),
    "07.G.101" => array(16, "Gowning Distribution - Female", 0, ""),
    "07.G.102" => array(16, "Gowning Distribution - Male", 0, ""),
    "07.G.103" => array(16, "Lockers room - Female", 0, ""),
    "07.G.104" => array(16, "Lockers room - Male", 0, ""),
    "07.G.105" => array(16, "PAL IN Female CNC", 0, "See drawings for proposed equipment"),
    "07.G.106" => array(16, "PAL IN Male CNC", 0, "See drawings for proposed equipment"),
    "07.G.107" => array(16, "PAL OUT Female CNC", 0, ""),
    "07.G.108" => array(16, "PAL OUT Male CNC", 0, ""),
    "07.G.109" => array(16, "Shower - Female", 0, ""),
    "07.G.110" => array(16, "Shower - Male", 0, ""),
    "07.G.111" => array(16, "Vestibule - Female", 0, ""),
    "07.G.112" => array(16, "Vestibule - Male", 0, ""),
    "07.H.101" => array(16, "Air lock ", 0, ""),
    "07.H.102" => array(16, "Pilot plant Maintenance", 0, "Room added"),
    "07.H.103" => array(16, "MAL IN/OUT D", 0, ""),
    "07.H.104" => array(16, "Vestibule", 0, ""),
    "08.A.101" => array(18, "Cell culture", 0, "28° Celsius"),
    "08.A.102" => array(18, "Freezer room", 0, "Provide 5 freezers -80'C
Provide 3 freezers -20'C
Provide 4 refrigerators 4'C
Accessible by principle laboratory R&I"),
    "08.A.103" => array(18, "Research & Innovation laboratory", 0, "Provide 2 biosafety cabinets
Provide 1 chemical fume hood"),
    "08.A.104" => array(18, "Storage", 0, ""),
    "08.B.101" => array(18, "Downstream Laboratory", 0, "Access by corridor "),
    "08.B.102" => array(18, "Extraction / Clarification C", 0, "Dedicated to noisy equipment and wash area"),
    "08.B.103" => array(18, "Extraction / Clarification A", 0, ""),
    "08.B.104" => array(18, "Extraction / Clarification B", 0, ""),
    "08.B.105" => array(18, "Solution preparation", 0, "Located close to Extraction / Clarification"),
    "08.B.106" => array(18, "Solution storage", 0, "18288 Linear metres of shelving +  1830mm multi purpose counter"),
    "08.B.107" => array(18, "Storage", 0, "12694 Linear metres of shelving (10x4' shelves) fixed to walls"),
    "08.B.108" => array(18, "Temperature controlled room", 0, "Temperature range: 12-30 deg celsius"),
    "08.C.101" => array(18, "Analytical Development Laboratory", 0, "Laboratory as discussed in user meetings. Services and specifie equipment to be accomodated needs to be discussed in the detailled design phase"),
    "08.C.102" => array(18, "Balance ", 0, "Laboratory as discussed in user meetings. Services and specifie equipment to be accomodated needs to be discussed in the detailled design phase"),
    "08.C.103" => array(18, "Cell culture ", 0, "Laboratory as discussed in user meetings. Services and specifie equipment to be accomodated needs to be discussed in the detailled design phase"),
    "08.C.104" => array(18, "Freezer room", 0, "Laboratory as discussed in user meetings. Services and specifie equipment to be accomodated needs to be discussed in the detailled design phase"),
    "08.C.105" => array(18, "MS & HPLC Room", 0, "Laboratory as discussed in user meetings. Services and specifie equipment to be accomodated needs to be discussed in the detailled design phase"),
    "08.C.106" => array(18, "Serology Lab", 0, "Laboratory as discussed in user meetings. Services and specifie equipment to be accomodated needs to be discussed in the detailled design phase"),
    "08.C.107" => array(18, "Storage ", 0, "Laboratory as discussed in user meetings. Services and specifie equipment to be accomodated needs to be discussed in the detailled design phase"),
    "08.D.101" => array(18, "Corridor R&D", 0, ""),
    "08.D.201" => array(18, "Back-up Fridge and Freezer Room", 0, ""),
    "08.D.202" => array(18, "Clean Component Storage", 0, ""),
    "08.D.203" => array(18, "Dark Room", 0, ""),
    "08.D.204" => array(18, "Multiuse Cold room", 0, "for plant materials
between 2 and 8° Celsius
next to reception"),
    "08.D.301" => array(18, "A/L", 0, ""),
    "08.D.302" => array(18, "A/L", 0, ""),
    "08.D.303" => array(18, "Centrifugation", 0, "Must be located between DP & RI
Provide 5 ultracentrifuge
Provide 3 floor centrifuge
Shared by 3 departments : AD, RI and PD"),
    "08.D.304" => array(18, "Chemical waste", 0, ""),
    "08.D.305" => array(18, "Emergency A/L ", 0, ""),
    "08.D.306" => array(18, "Gas cylinder closet", 0, ""),
    "08.D.307" => array(18, "Janitorial closet", 0, ""),
    "08.D.308" => array(18, "MAL IN/OUT & Reception", 0, "See drawings for proposed equipment"),
    "08.D.309" => array(18, "MilliQ (niche)", 0, ""),
    "08.D.310" => array(18, "MilliQ (niche)", 0, ""),
    "08.D.311" => array(18, "PAL IN/OUT", 0, "See drawings for proposed equipment"),
    "08.D.312" => array(18, "PAL/MAL IN/OUT", 0, "See drawings for proposed equipment"),
    "08.D.313" => array(18, "Waste dumbwaiter", 0, ""),
    "08.D.401" => array(18, "Sterile Material Holding C", 0, "Specific fix equipment to be added in detailled design to accomodate procedures.  Protection on wall and techinques of washing to be discussed in the detailled phase. "),
    "08.D.402" => array(18, "Sterile Material Storage C", 0, "Specific fix equipment to be added in detailled design to accomodate procedures.  Protection on wall and techinques of washing to be discussed in the detailled phase. "),
    "08.D.403" => array(18, "Sterilization Autoclave", 0, "Specific fix equipment to be added in detailled design to accomodate procedures.  Protection on wall and techinques of washing to be discussed in the detailled phase. "),
    "08.D.404" => array(18, "Wash area", 0, "Specific fix equipment to be added in detailled design to accomodate procedures.  Protection on wall and techinques of washing to be discussed in the detailled phase. "),
    "08.D.405" => array(18, "Wash Area Clean C", 0, "Specific fix equipment to be added in detailled design to accomodate procedures.  Protection on wall and techinques of washing to be discussed in the detailled phase. "),
    "08.D.406" => array(18, "Washer", 0, "Specific fix equipment to be added in detailled design to accomodate procedures.  Protection on wall and techinques of washing to be discussed in the detailled phase. "),
    "08.D.407" => array(18, "Washer", 0, "Specific fix equipment to be added in detailled design to accomodate procedures.  Protection on wall and techinques of washing to be discussed in the detailled phase. "),
    "09.A.101" => array(14, "AVG Repair Workshop CNC", 0, ""),
    "09.A.102" => array(18, "Calibration Laboratory", 0, ""),
    "09.A.103" => array(14, "Critical parts", 0, ""),
    "09.A.104" => array(14, "Elevator Mechanical Room", 0, ""),
    "09.A.105" => array(14, "Elevator Mechanical Room", 0, ""),
    "09.A.106" => array(14, "Elevator Mechanical Room", 0, ""),
    "09.A.107" => array(14, "Elevator Mechanical Room", 0, ""),
    "09.A.108" => array(14, "Elevator Mechanical Room", 0, ""),
    "09.A.109" => array(14, "Elevator Mechanical Room", 0, ""),
    "09.A.110" => array(16, "Engeneering Doc Storage (documentation room)", 0, ""),
    "09.A.111" => array(-1, "Janitor", 0, "8 Janitor rooms in the Administration department"),
    "09.A.112" => array(-1, "Janitor", 0, "8 Janitor rooms in the Administration department"),
    "09.A.113" => array(-1, "Janitor", 0, "8 Janitor rooms in the Administration department"),
    "09.A.114" => array(-1, "Janitor", 0, "8 Janitor rooms in the Administration department"),
    "09.A.115" => array(-1, "Janitor", 0, "8 Janitor rooms in the Administration department"),
    "09.A.116" => array(-1, "Janitor", 0, "8 Janitor rooms in the Administration department"),
    "09.A.117" => array(-1, "Janitor", 0, "8 Janitor rooms in the Administration department"),
    "09.A.118" => array(-1, "Janitor", 0, "8 Janitor rooms in the Administration department"),
    "09.A.119" => array(-1, "Janitor", 0, "8 Janitor rooms in the Administration department"),
    "09.A.120 (doublon)" => array(-1, "Janitor", 0, "8 Janitor rooms in the Administration department"),
    "09.A.120" => array(14, "Mechanical Room", 0, ""),
    "09.A.121" => array(14, "Process Control Room", 0, ""),
    "09.B.101" => array(14, "Mechanical Receiving / Shipping", 0, ""),
    "09.C.101" => array(14, "Boiler room", 0, ""),
    "09.C.102" => array(14, "Boiler room", 0, ""),
    "09.C.103" => array(14, "Boiler room Supervisor", 0, ""),
    "09.C.104" => array(14, "Chiller room", 0, ""),
    "09.C.105" => array(14, "Clean utilities", 0, ""),
    "09.C.106" => array(14, "Effluent waste treatment", 0, ""),
    "09.C.107" => array(14, "General Mechanical room ", 0, ""),
    "09.C.108" => array(16, "Mech.", 0, ""),
    "09.C.109" => array(18, "Mecahnical room : HVAC", 0, ""),
    "09.C.110" => array(16, "Mechanical/Electrical Room", 0, ""),
    "09.C.110 (doublon)" => array(18, "Mecahnical room : HVAC", 0, ""),
    "09.C.111" => array(14, "Neutralization Tanks", 0, ""),
    "09.C.112" => array(14, "Pre-Treatment", 0, ""),
    "09.C.113" => array(14, "Pumps Room", 0, ""),
    "09.C.114" => array(14, "Water entrance", 0, ""),
    "09.C.115" => array(-1, "Purification system ", 0, ""),
    "09.C.201" => array(14, "MAL IN/OUT", 0, "See drawings for proposed equipment"),
    "09.C.202" => array(14, "MAL IN/OUT", 0, "See drawings for proposed equipment"),
    "09.C.203" => array(14, "PAL IN/OUT", 0, "See drawings for proposed equipment"),
    "09.C.204" => array(14, "PAL IN/OUT", 0, "See drawings for proposed equipment"),
    "09.D.101" => array(14, "Electrical distribution rooms", 0, ""),
    "09.D.102" => array(-1, "Electrical Room", 0, ""),
    "09.D.103" => array(-1, "Electrical Room", 0, ""),
    "09.D.104" => array(-1, "Electrical Room", 0, ""),
    "09.D.105" => array(-1, "Electrical Room", 0, ""),
    "09.D.106" => array(-1, "Electrical Room", 0, ""),
    "09.D.107" => array(-1, "Electrical Room", 0, ""),
    "09.D.108" => array(14, "Emergency generator room", 0, ""),
    "09.D.109" => array(14, "Main electrical room", 0, ""),
    "09.D.110" => array(-1, "UPS", 0, ""),
    "09.E.101" => array(14, "Documentation room", 0, ""),
    "09.E.102" => array(-1, "Equipment storage (IT)", 0, ""),
    "09.E.103" => array(14, "Main telecom room", 0, ""),
    "09.E.104" => array(-1, "Telecom rooms", 0, ""),
    "09.E.105" => array(-1, "Telecom rooms", 0, ""),
    "09.E.106" => array(-1, "Telecom rooms", 0, ""),
    "09.E.107" => array(-1, "Telecom rooms", 0, ""),
    "09.E.108" => array(-1, "Telecom rooms", 0, ""),
    "09.E.109" => array(-1, "Telecom rooms", 0, ""),
    "09.E.110" => array(-1, "Telecom rooms", 0, ""),
    "09.E.111" => array(-1, "Information Technology server room (IT)", 0, ""),
    "09.F.101" => array(14, "Building Automation Control Room", 0, ""),
    "09.F.102" => array(16, "Server Room", 0, ""),
    "09.G.101" => array(14, "Acid and base storage", 0, ""),
    "09.G.102" => array(14, "Greenhouse Services (feritgation)", 0, ""),
    "09.G.103" => array(15, "Maintenance catwalk", 0, ""),
    "09.G.104" => array(15, "Maintenance catwalk", 0, ""),
    "10.A.101" => array(16, "WC (Infirmary)", 0, ""),
    "10.A.102" => array(16, "WC (Security)", 0, ""),
    "10.A.103" => array(16, "WC (Visitors)", 0, ""),
    "10.A.201" => array(-1, "WC - Female ", 0, ""),
    "10.A.202" => array(-1, "WC - Female ", 0, ""),
    "10.A.203" => array(-1, "WC - Female ", 0, ""),
    "10.A.204" => array(-1, "WC - Female ", 0, ""),
    "10.A.205" => array(-1, "WC - Female ", 0, ""),
    "10.A.206" => array(-1, "WC - Female ", 0, ""),
    "10.A.207" => array(-1, "WC - Female ", 0, ""),
    "10.A.208" => array(-1, "WC - Female ", 0, ""),
    "10.A.209" => array(-1, "WC - Male", 0, ""),
    "10.A.210" => array(-1, "WC - Male", 0, ""),
    "10.A.211" => array(-1, "WC - Male", 0, ""),
    "10.A.212" => array(-1, "WC - Male", 0, ""),
    "10.A.213" => array(-1, "WC - Male", 0, ""),
    "10.A.214" => array(-1, "WC - Male", 0, ""),
    "10.A.215" => array(-1, "WC - Male", 0, ""),
    "10.A.216" => array(-1, "WC - Male", 0, ""),
    "10.B.101" => array(16, "WC (Drivers)", 0, ""),
    "10.B.102" => array(16, "WC - Female ", 0, ""),
    "10.B.103" => array(16, "WC - Male", 0, ""),
    "10.C.101" => array(16, "WC - Female ", 0, ""),
    "10.C.102" => array(16, "WC - Male", 0, ""),
    "10.D.101" => array(18, "WC - Female ", 0, ""),
    "10.D.102" => array(18, "WC - Male", 0, ""),
    "10.E.101" => array(16, "WC (Drivers)", 0, ""),
    "10.E.102" => array(16, "WC - Female ", 0, ""),
    "10.E.103" => array(16, "WC - Male", 0, ""),
    "10.F.101" => array(16, "WC - Female ", 0, ""),
    "10.F.102" => array(16, "WC - Male", 0, ""),
    "10.G.101" => array(18, "WC - Female ", 0, ""),
    "10.G.102" => array(18, "WC - Male", 0, ""),
    "10.H.101" => array(14, "WC - Female ", 0, ""),
    "10.H.102" => array(14, "WC - Male", 0, ""),
    "12.A.101" => array(16, "Corridor", 0, ""),
    "12.A.102" => array(16, "Exit Corridor", 0, ""),
    "12.A.103" => array(16, "Exit Corridor", 0, ""),
    "12.A.104" => array(16, "Exit Corridor", 0, ""),
    "12.A.105" => array(16, "Exit Corridor", 0, ""),
    "12.A.201" => array(-1, "Atrium", 0, ""),
    "12.A.202" => array(-1, "Atrium", 0, ""),
    "12.A.203" => array(-1, "Atrium", 0, ""),
    "12.A.204" => array(-1, "Circulation", 0, ""),
    "12.A.205" => array(-1, "Circulation", 0, ""),
    "12.A.301" => array(-1, "Interior Parking", 0, ""),
    "13.A.101" => array(16, "Material general circulation", 0, ""),
    "13.A.102" => array(16, "Material general circulation", 0, ""),
    "13.B.101" => array(18, "Corridor", 0, ""),
    "13.C.101" => array(14, "Clean gutters staging", 0, ""),
    "16.A.101" => array(16, "Greenhouse ", 0, ""),
    "16.A.102" => array(16, "Propagation", 0, ""),
    "16.B.101" => array(16, "Greenhouse plenum", 0, ""),
    "16.B.102" => array(-1, "Gutter handling system", 0, ""),
    "16.C.101" => array(16, "Greenhouse expansion", 0, ""),
    "18.A.101" => array(15, "Parking space", 0, "2016.08.03 : 258 interior parking lots"),
);

$db->autocommit(false);

foreach($locaux as $code_fiche => $local)
{
	$niveau = $local[0] > -1 ? $local[0] : 'null';
	$utilite = dm_str_replace("'", "''", $local[1]);
	$nb_occupants = $local[2] > 0 ? $local[2] : 'null';
	$commentaire = dm_str_replace("'", "''", $local[3]);
	
	$sql = "INSERT INTO `fiches` (`fiche_parent_id`, `fiche_type_id`, `fiche_sous_type_id`, `batiment_id`, `batiment_niveau_element_id`, `code`, `code_alternatif`, `id_revit`, `id_codebook`, `ts_ajout`, `usager_ajout_id`, `ts_modif`, `usager_modif_id`, `ts_delete`, `usager_delete_id`, `is_gabarit`, `numero_lot`, `fiche_local_id`, `description`, `commentaire`) "
            . " VALUES (NULL, '2000002', '2000002', '17', NULL, '".$code_fiche."', NULL, NULL, NULL, CURRENT_TIMESTAMP, 25, NULL, NULL, NULL, NULL, '0', NULL, NULL, NULL, NULL);";
	$db->query($sql, array());
	$fiche_id = $db->lastInsertId();
	
	$sql = "INSERT INTO `generaux_locaux` (`fiche_id`, `projet_id`, `ordre`, `utilite`, `type_soin_id`, `remarque`, `zone_id`, `bloc_id`, `etage_id`, `code`) VALUES ($fiche_id, 13, 1, '$utilite', null, '$commentaire', null, null, $niveau, '".$code_fiche."');";
	$db->query($sql, array());
	$id = $db->lastInsertId();

	$sql = "INSERT INTO `snclavalin_archive`.`generaux_locaux` (`fiche_id`, `projet_id`, `ordre`, `utilite`, `type_soin_id`, `remarque`, `zone_id`, `bloc_id`, `etage_id`, `ts_ajout`, `usager_ajout_id`, `id`) VALUES ($fiche_id, 13, 1, '$utilite', null, '$commentaire', null, null, $niveau, current_timestamp, 25, $id);

	insert into `fiches_logs` (fiche_id, formulaire_id, projet_id, ts_ajout, usager_ajout_id, code, nb_lignes, raison) values ($fiche_id, 48, 13, current_timestamp, 25, 'updateFicheForm', 1, 'Création du formulaire');

	UPDATE `fiches` SET ts_modif = current_timestamp, usager_modif_id = 25 WHERE id = $fiche_id;

	INSERT INTO `snclavalin_archive`.fiches_formulaires (fiche_id, formulaire_id, commentaire, ts_ajout, usager_ajout_id) 
							VALUES ($fiche_id, 48, null, current_timestamp, 25);

	INSERT INTO fiches_formulaires (fiche_id, formulaire_id, commentaire) 
							VALUES ($fiche_id, 48, null);";
	$db->query($sql, array());


	$sql = "INSERT INTO `parametres_cvac` (`fiche_id`, `projet_id`, `ordre`, `csa_type_id`, `evacuation`, `pressurisation_type_id`, `indicateur_pression_local`, `pourcentage_evacuation_id`, `climatisation_urgence`, `besoin_eau_glacee`, `filtration_hepa`, `taux_occupation_id`, `changement_air_frais_min_id`, `changement_air_total_min_id`, `changement_air_evacue_min_id`, `changement_air_retour_min_id`, `temperature_min_occupe_id`, `temperature_max_occupe_id`, `temperature_min_inoccupe_id`, `temperature_max_inoccupe_id`, `humidite_relative_min_id`, `humidite_relative_max_id`, `niveau_bruit_id`, `commentaires`) VALUES ($fiche_id, 13, 1, null, 0, null, 0, null, 0, 0, 0, $nb_occupants, null, null, null, null, null, null, null, null, null, null, null, null);";
	$db->query($sql, array());
	$id = $db->lastInsertId();

	$sql = "INSERT INTO `snclavalin_archive`.`parametres_cvac` (`fiche_id`, `projet_id`, `ordre`, `csa_type_id`, `evacuation`, `pressurisation_type_id`, `indicateur_pression_local`, `pourcentage_evacuation_id`, `climatisation_urgence`, `besoin_eau_glacee`, `filtration_hepa`, `taux_occupation_id`, `changement_air_frais_min_id`, `changement_air_total_min_id`, `changement_air_evacue_min_id`, `changement_air_retour_min_id`, `temperature_min_occupe_id`, `temperature_max_occupe_id`, `temperature_min_inoccupe_id`, `temperature_max_inoccupe_id`, `humidite_relative_min_id`, `humidite_relative_max_id`, `niveau_bruit_id`, `commentaires`, `ts_ajout`, `usager_ajout_id`, `id`) VALUES ($fiche_id, 13, 1, null, 0, null, 0, null, 0, 0, 0, $nb_occupants, null, null, null, null, null, null, null, null, null, null, null, null, current_timestamp, 25, $id);

	insert into `fiches_logs` (fiche_id, formulaire_id, projet_id, ts_ajout, usager_ajout_id, code, nb_lignes, raison) values ($fiche_id, 31, 13, current_timestamp, 25, 'updateFicheForm', 1, 'Création du formulaire');

	UPDATE `fiches` SET ts_modif = current_timestamp, usager_modif_id = 25 WHERE id = $fiche_id;

	INSERT INTO `snclavalin_archive`.fiches_formulaires (fiche_id, formulaire_id, commentaire, ts_ajout, usager_ajout_id) 
							VALUES ($fiche_id, 31, null, current_timestamp, 25);

	INSERT INTO fiches_formulaires (fiche_id, formulaire_id, commentaire) 
							VALUES ($fiche_id, 31, null);

	insert into fiches_disciplines (fiche_id, discipline_id) values($fiche_id, 3);";
	$db->query($sql, array());
}

$db->commit();
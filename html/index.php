<?php

use Aws\S3\MultipartUploader;

ini_set("memory_limit", "10000M");
$ts_debut_all = microtime(true);
$debut_script = date('Y-m-d H:i:s');

include('config/inc.php');

GestionnaireRoute::instance()->redirectAndLogoutOrNotPage();

if (isset($_GET['table'])) {
    $sql = "SELECT table_name FROM information_schema.tables WHERE table_schema = ?;";
    $allTables = db::instance()->getCol($sql, [Config::instance()->codeClient]);
    $allTables = array_flip((array_merge($allTables, $TablesCategoriesPermissions, ['equipements_bio_medicaux_no_coord'])));
    if (!isset($allTables[$_GET['table']])) {
        mail('<EMAIL>', 'Table non-autorisée', $_GET['table']);
        die();
    }
}

$usager = getUsager();
if ($usager->is_accepte_condition == 0) {
    if (isset($_GET['is_accepte_condition']) && intval($_GET['is_accepte_condition']) == 1) {
        $db->autocommit(false);
        $sql = "update usagers set is_accepte_condition = 1 where id = ?";
        $db->query($sql, array($usager->id));
        $db->commit();

        $usager = new Usager($usager->id);
        $_SESSION['usager_'.$CodeClient] = $usager;

        header('Location: index.php?section=landing&module=index');
        die();
    } else {
        include_once(nomFichierRepertoireValide('conditions_'.$Langue).'.php');
    }
} elseif (isset($_GET['action'])) {
	if ($_GET['action'] == 'getFormulairesDuGabarit') {
		$gabarit_id = (int)$_GET['gabarit_id'];
		$gabarit = new Fiches($gabarit_id);

		$formulaires = $gabarit->getFormulaires();
        $formulaire_gabarit_ids = array_keys($formulaires);
		$formulaire_qte_ids = Formulaires::getFormulairesIdWithQuantite();

        Formulaires::getOptions([ 'ids' => array_intersect($formulaire_gabarit_ids, $formulaire_qte_ids) ]);
		exit;
	} elseif ($_GET['action'] == 'getContratsJalons') {
		$contrat_jalon_modele_id = (int)$_GET['contrat_jalon_modele_id'];
        $jalons = ContratsJalons::getListe([ 'contrat_jalon_modele_id' => $contrat_jalon_modele_id, 'is_actif_defaut' => 1, 'mode_no_data' => 1 ]);
        $isFirst = true;
        foreach ($jalons as $jalon) {
            $jalon->displayLigne($isFirst);
            if ($jalon->type_jalon == 'jalon') {
	            $isFirst = false;
            }
        }
		exit;
	} elseif ($_GET['action'] == 'save_quantite_groupe') {
		$sql = "select count(*) from acquisitions_projets_groupes_equipements_fiches where fiche_id = ? and acquisition_projet_groupe_equipement_id = ?";
		$nb = $db->getOne($sql, [intval($_GET['fiche_id']), intval($_GET['acquisition_projet_groupe_equipement_id'])]);
		if ($nb > 0) {
			$sql = "update acquisitions_projets_groupes_equipements_fiches set quantite = ? where fiche_id = ? and acquisition_projet_groupe_equipement_id = ?";
			$db->query($sql, array(intval($_GET['quantite']), intval($_GET['fiche_id']), intval($_GET['acquisition_projet_groupe_equipement_id'])));
		} else {
			$sql = "insert into acquisitions_projets_groupes_equipements_fiches (quantite, quantite_distribuee, quantite_existante, quantite_requise, quantite_demenager, fiche_id, acquisition_projet_groupe_equipement_id) values (?, ?, ?, ?, ?, ?, ?)";
			$db->query($sql, array(intval($_GET['quantite']), 0, 0, 0, 0, intval($_GET['fiche_id']), intval($_GET['acquisition_projet_groupe_equipement_id'])));
		}
		exit;
	} elseif ($_GET['action'] == 'save_quantite_groupe_financement') {
        $sql = "select count(*) from financements_projets_groupes_equipements_fiches where fiche_id = ? and financement_projet_groupe_equipement_id = ?";
        $nb = $db->getOne($sql, [intval($_GET['fiche_id']), intval($_GET['financement_projet_groupe_equipement_id'])]);
        if ($nb > 0) {
	        $sql = "update financements_projets_groupes_equipements_fiches set quantite = ? where fiche_id = ? and financement_projet_groupe_equipement_id = ?";
	        $db->query($sql, array(intval($_GET['quantite']), intval($_GET['fiche_id']), intval($_GET['financement_projet_groupe_equipement_id'])));
        } else {
            $sql = "insert into financements_projets_groupes_equipements_fiches (quantite, quantite_existante, quantite_requise, quantite_demenager, fiche_id, financement_projet_groupe_equipement_id) values (?, ?, ?, ?, ?, ?)";
	        $db->query($sql, array(intval($_GET['quantite']), 0, 0, 0, intval($_GET['fiche_id']), intval($_GET['financement_projet_groupe_equipement_id'])));
        }
        exit;
    } elseif ($_GET['action'] == "duplicate_groupe_equipement") {
        /* Duplicate the equipment group in the same project */


        if ($_GET['id']) {
            $sql_groupe = "SELECT * FROM acquisitions_projets_groupes_equipements WHERE id = ?";
            $groupes = $db->getAll($sql_groupe, array($_GET['id']));

            $sql_numero = "SELECT numero FROM acquisitions_projets_groupes_equipements ORDER BY numero DESC LIMIT 0,1";
            $last_numero = $db->getOne($sql_numero);

            $nouveau_numero = str_pad($last_numero + 1, 5, '0', STR_PAD_LEFT);

            foreach ($groupes as $groupe) {
                $sql_fiches = "SELECT * FROM acquisitions_projets_groupes_equipements_fiches WHERE acquisition_projet_groupe_equipement_id = ?";
                $fiches = $db->getAll($sql_fiches, array($groupe['id']));

                $sql_insertion_groupe = "INSERT INTO acquisitions_projets_groupes_equipements
                      (numero, description, equipement_bio_medicaux_type_id, acquisition_projet_id, quantite_disponible)
                      VALUES(?, ?, ?, ?, ?)";

                $db->query(
                    $sql_insertion_groupe,
                    array(
                        $nouveau_numero,
                        $groupe['description'],
                        $groupe['equipement_bio_medicaux_type_id'],
                        $groupe['acquisition_projet_id'],
                        '0'
                    )
                );

                $id_groupe = $db->lastInsertId();

                foreach ($fiches as $fiche) {
                    $sql_insertion_groupe = "INSERT INTO acquisitions_projets_groupes_equipements_fiches
                      (acquisition_projet_groupe_equipement_id, fiche_id, quantite, quantite_requise, quantite_existante, quantite_demenager)
                      VALUES(?, ?, '0', '0', '0', '0')";

                    $db->query($sql_insertion_groupe, array($id_groupe, $fiche['fiche_id']));
                    //printDebug(dm_nl2br(db::interpolateQuery($sql_insertion_groupe, array($id_groupe, $fiche['fiche_id'], '', ''))));


                }
            }
        }
        exit;
    } elseif ($_GET['action'] == "update_procedures_inspections_gbm_non_traites") {
        if (isset($_POST['listeEquipementsTypes'])) {
            $procedure_id = intval($_POST['procedure_id']);
            foreach ($_POST['listeEquipementsTypes'] as $equipement_bio_medicaux_type_id) {
                $sql = "INSERT INTO procedures_inspections_gbm_equipements (procedure_id, equipement_bio_medicaux_type_id) 
                        VALUES (?, ?)";
                db::instance()->query($sql, array($procedure_id, $equipement_bio_medicaux_type_id));
            }
        }
        print 'ok';
    } elseif ($_GET['action'] == "update_procedures_mises_en_service_mep_non_traites") {
        if (isset($_POST['listeEquipements'])) {
            $procedure_id = intval($_POST['procedure_id']);
            foreach ($_POST['listeEquipements'] as $equipement_mep_id) {
                $sql = "INSERT INTO procedures_mises_en_service_mep_equipements (procedure_id, equipement_mep_id) 
                        VALUES (?, ?)";
                db::instance()->query($sql, array($procedure_id, $equipement_mep_id));
            }
        }
        print 'ok';
    } elseif ($_GET['action'] == "update_planifications_mises_en_service_mep_non_traites") {
        if (isset($_POST['listeEquipements'])) {
            $planification_id = intval($_POST['planification_id']);
            foreach ($_POST['listeEquipements'] as $equipement_mep_id) {
                $sql = "INSERT INTO procedures_mises_en_service_planifiees_mep_equipements (procedure_mise_en_service_planifiee_mep_id, equipement_mep_id) 
                        VALUES (?, ?)";
                db::instance()->query($sql, array($planification_id, $equipement_mep_id));
            }
        }
        print 'ok';
    } elseif ($_GET['action'] == "update_planifications_inspections_gbm_non_traites") {
        if (isset($_POST['listeEquipements'])) {
            $planification_id = intval($_POST['planification_id']);
            foreach ($_POST['listeEquipements'] as $equipement_bio_medicaux_id) {
                $sql = "INSERT INTO procedures_inspections_planifiees_gbm_equipements (procedure_inspection_planifiee_gbm_id, equipement_bio_medical_id, ajout_usager_id) 
                        VALUES (?, ?, ?)";
                db::instance()->query($sql, array($planification_id, $equipement_bio_medicaux_id, getIdUsager()));
            }
        }
        print 'ok';
    } elseif ($_GET['action'] == "update_equipement_non_traites") {
		// Updating the 'Equipement non traités'

		// If it's for an existing project
		if (isset($_POST['c']) && isset($_POST['projet_id']) && $_POST['projet_id'] != "0") {
			$id_project = (int)$_POST['projet_id'];

			$equipementsFiches = [];
			foreach ($_POST['c'] as $equipement) {
				$equipementFicheInfo = dm_explode('_', $equipement);
				$fiche_id = $equipementFicheInfo[0];
				$equipement_id = $equipementFicheInfo[1];
				$quantite = $equipementFicheInfo[2];

				if (!isset($equipementsFiches[$equipement_id][$fiche_id])) {
					$equipementsFiches[$equipement_id][$fiche_id] = 0;
				}
				$equipementsFiches[$equipement_id][$fiche_id] += (int)$quantite;
			}

			$data = [$id_project];
			$data = array_merge($data, array_keys($equipementsFiches));

			$sql = "select equipement_bio_medicaux_type_id, id from acquisitions_projets_groupes_equipements where acquisition_projet_id = ? and equipement_bio_medicaux_type_id in (" . db::placeHolders(array_keys($equipementsFiches)) . ")";
			$assocs = $db->getAssoc($sql, $data);

			foreach ($equipementsFiches as $equipement_id => $fiches) {
				if (isset($assocs[$equipement_id])) {
					$id_groupe = $assocs[$equipement_id]['id'];
				} else {
					// Setting the equipment group number to the last one + 1
					$select_last_numero = "SELECT numero FROM acquisitions_projets_groupes_equipements ORDER BY numero DESC LIMIT 0,1";
					$last_numero = $db->getOne($select_last_numero);
					$numero_groupe = str_pad( $last_numero + 1,  5, '0', STR_PAD_LEFT);

					$equipement = new EquipementsBioMedicauxTypes($equipement_id);
					$insertion_project = "INSERT INTO acquisitions_projets_groupes_equipements (numero, description, equipement_bio_medicaux_type_id, acquisition_projet_id) 
                                            VALUES(?, ?, ?, ?)";

					$db->query($insertion_project, [$numero_groupe, $equipement->nom, $equipement_id, $id_project]);

					$id_groupe = $db->lastInsertId();
				}

				foreach ($fiches as $fiche_id => $quantite) {
					$insertion_fiche[$fiche_id] = "INSERT INTO acquisitions_projets_groupes_equipements_fiches
                        (acquisition_projet_groupe_equipement_id, fiche_id, quantite, quantite_requise, quantite_demenager) 
                        VALUES (?, ?, '0', ?, '0')";
					$db->query($insertion_fiche[$fiche_id], [$id_groupe, $fiche_id, $quantite]);
				}

			}
            print 'ok';
		}
    } else if ($_GET['action'] == "get_planifications_for_equipement_non_traites") {
        $sql = "select ap.id
                    from acquisitions_projets ap
                    where projet_id = ?";
        $projet_ids = $db->getCol($sql, array(getProjetId()));

        $planification_locaux_ids = [];
        foreach ($_POST['listeEquipements'] as $equipement_id) {
            $planification_locaux_ids[$_POST['planification_locaux_ids_assocs'][$equipement_id]] = $_POST['planification_locaux_ids_assocs'][$equipement_id];
        }

        print '<option value="">- ' . txt('Sélectionnez une planification') . ' -</option>';
        if (!empty($planification_locaux_ids)) {
            $sql = "select procedure_inspection_planifiee_gbm_id, count(*) as nb
                    from procedures_inspections_planifiees_gbm_locaux
                    where fiche_id in (" . db::placeHolders($planification_locaux_ids) . ")
                    group by procedure_inspection_planifiee_gbm_id";
            $planification_ids_tmp = $db->getAssoc($sql, $planification_locaux_ids);

            $nbLocaux = count($planification_locaux_ids);
            $planification_ids = [];
            foreach ($planification_ids_tmp as $planification_id => $row) {
                if ($row['nb'] == $nbLocaux) {
                    $planification_ids[] = $planification_id;
                }
            }

            if (!empty($planification_ids)) {
                $planifications = ProcedureInspectionPlanifieeGbm::getListe(['ids' => $planification_ids]);
                foreach ($planifications as $planification) {
                    print '<option value="' . $planification->id . '">' . $planification->numero . ' - ' . $planification->description . '</option>';
                }
            }
        }
    } else if ($_GET['action'] == "get_projets_for_equipement_non_traites") {
		$sql = "select ap.id
                    from acquisitions_projets ap
                    where projet_id = ?";
		$projet_ids = $db->getCol($sql, array(getProjetId()));
		foreach ($_POST['c'] as $equipement) {
			$equipementFicheInfo = dm_explode('_', $equipement);
			$fiche_id = $equipementFicheInfo[0];
			$equipement_id = $equipementFicheInfo[1];
			$quantite = $equipementFicheInfo[2];

			$sql = "select distinct ap.id
                    from acquisitions_projets ap
                    join acquisitions_projets_fiches agf on agf.acquisition_projet_id = ap.id
                    left join acquisitions_projets_groupes_equipements apge on apge.acquisition_projet_id = ap.id and apge.equipement_bio_medicaux_type_id = ?
                    left join acquisitions_projets_groupes_equipements_fiches apgef on apgef.fiche_id = agf.fiche_id and apgef.acquisition_projet_groupe_equipement_id = apge.id
                    where apgef.fiche_id is null and agf.fiche_id = ?";
			$projet_ids_tmp = $db->getCol($sql, [$equipement_id, $fiche_id]);
			$projet_ids = array_intersect($projet_ids, $projet_ids_tmp);
		}

        if (count($projet_ids) > 0) {
	        $sql = "Select ap.id, concat(ap.numero, ' - ', ap.description) as projet 
					from acquisitions_projets ap 
					where ap.id in (" . db::placeHolders($projet_ids) . ") 
					order by projet;";
	        $projets = $db->getAll($sql, array_values($projet_ids));

	        $sql = "select *, e.nom as nom_echeancier
                from echeanciers e
                join acquisitions_projets_echeanciers a on e.id = a.echeancier_id
                join acquisitions_projets a2 on a.acquisition_projet_id = a2.id
                where a2.projet_id = ?";
	        $echeanciers_projets_tmp = $db->getAll($sql, [getProjetId()]);

	        $echeanciers_projets = [];
	        foreach ($echeanciers_projets_tmp as $i => $echeancier_projet) {
		        $echeanciers_projets[$echeancier_projet['acquisition_projet_id']][] = $echeancier_projet['nom_echeancier'];
	        }
            print '<option value="">- ' . txt('Sélectionnez un projet') . ' -</option>';
	        foreach ($projets as $projet) {
		        $extra = '';
		        if (isset($echeanciers_projets[$projet['id']])) {
			        $extra = ' (';
			        foreach ($echeanciers_projets[$projet['id']] as $i => $nom_echeancier) {
				        $extra .= ($i == 0 ? '' : ', ') . $nom_echeancier;
			        }
			        $extra .= ')';
		        }
		        print '<option value="' . $projet['id'] . '">' . $projet['projet'] . $extra . '</option>';
	        }
        }
    } elseif ($_GET['action'] == "update_equipement_non_traites_financements") {
        // Updating the 'Equipement non traités'

        // If it's for an existing project
        if (isset($_GET['c']) && isset($_GET['projet_id']) && $_GET['projet_id'] != "0") {
            $id_project = (int)$_GET['projet_id'];

            $equipementsFiches = [];
            foreach ($_GET['c'] as $equipement) {
                $equipementFicheInfo = dm_explode('_', $equipement);
	            $fiche_id = $equipementFicheInfo[0];
	            $equipement_id = $equipementFicheInfo[1];
	            $quantite = $equipementFicheInfo[2];

	            if (!isset($equipementsFiches[$equipement_id][$fiche_id])) {
		            $equipementsFiches[$equipement_id][$fiche_id] = 0;
                }
	            $equipementsFiches[$equipement_id][$fiche_id] += (int)$quantite;
            }

            $data = [$id_project];
	        $data = array_merge($data, array_keys($equipementsFiches));

            $sql = "select equipement_bio_medicaux_type_id, id from financements_projets_groupes_equipements where financement_projet_id = ? and equipement_bio_medicaux_type_id in (" . db::placeHolders(array_keys($equipementsFiches)) . ")";
            $assocs = $db->getAssoc($sql, $data);

            foreach ($equipementsFiches as $equipement_id => $fiches) {
                if (isset($assocs[$equipement_id])) {
                    $id_groupe = $assocs[$equipement_id]['id'];
                } else {
                    // Setting the equipment group number to the last one + 1
                    $select_last_numero = "SELECT numero FROM financements_projets_groupes_equipements ORDER BY numero DESC LIMIT 0,1";
                    $last_numero = $db->getOne($select_last_numero);
                    $numero_groupe = str_pad( $last_numero + 1,  5, '0', STR_PAD_LEFT);

                    $equipement = new EquipementsBioMedicauxTypes($equipement_id);
                    $insertion_project = "INSERT INTO financements_projets_groupes_equipements (numero, description, equipement_bio_medicaux_type_id, financement_projet_id) 
                        VALUES(?, ?, ?, ?)";

                    $db->query($insertion_project, [$numero_groupe, $equipement->nom, $equipement_id, $id_project]);

                    $id_groupe = $db->lastInsertId();
                }

                foreach ($fiches as $fiche_id => $quantite) {
                    $insertion_fiche[$fiche_id] = "INSERT INTO financements_projets_groupes_equipements_fiches
                        (financement_projet_groupe_equipement_id, fiche_id, quantite, quantite_requise, quantite_demenager) 
                        VALUES (?, ?, '0', ?, '0')";
                    $db->query($insertion_fiche[$fiche_id], [$id_groupe, $fiche_id, $quantite]);
                }

            }
            header('Location: index.php?section=financement&module=equipements_non_traites&apres_update_equipement_non_traites=1');
            die();
        }
    }
	else if($_GET['action'] == 'save_quantite_groupe_all')
	{
	    $db->autocommit(false);
	    if(isset($_POST['quantites']) && is_array($_POST['quantites']))
        {
            foreach($_POST['quantites'] as $iter => $quantites)
            {
	            $sql = "select count(*) from acquisitions_projets_groupes_equipements_fiches where fiche_id = ? and acquisition_projet_groupe_equipement_id = ?";
	            $nb = $db->getOne($sql, [intval($_POST['fiche_ids'][$iter]), intval($_POST['acquisition_projet_groupe_equipement_ids'][$iter])]);
	            if ($nb > 0) {
		            $sql = "update acquisitions_projets_groupes_equipements_fiches set quantite = ? where fiche_id = ? and acquisition_projet_groupe_equipement_id = ?";
		            $db->query($sql, array(intval($_POST['quantites'][$iter]), intval($_POST['fiche_ids'][$iter]), intval($_POST['acquisition_projet_groupe_equipement_ids'][$iter])));
	            } else {
		            $sql = "insert into acquisitions_projets_groupes_equipements_fiches (quantite, quantite_distribuee, quantite_existante, quantite_requise, quantite_demenager, fiche_id, acquisition_projet_groupe_equipement_id) values (?, ?, ?, ?, ?, ?, ?)";
		            $db->query($sql, array(intval($_POST['quantites'][$iter]), 0, 0, 0, 0, intval($_POST['fiche_ids'][$iter]), intval($_POST['acquisition_projet_groupe_equipement_ids'][$iter])));
	            }
            }
        }
        $db->commit();
		exit;
	}
	else if($_GET['action'] == 'getEquipementSource')
	{
		$projetFinancement = new FinancementsProjets((int)$_GET['financement_projet_id']);
        $filtre = [
            'source_financement_id_not' => (int)$_GET['source_financement_id'],
            'fincancement_depot_id' => (int)$_GET['fincancement_depot_id'],
            'fiches_ids' => array_keys($projetFinancement->fiches)
        ];
        if (isset($_GET['filtre_global_to_use'])) {
	        if (isset($_GET['disable_fitre_avance']) && (int)$_GET['disable_fitre_avance'] == 1) {
		        unset($_SESSION['filtre_avance_equipement' . $_GET['filtre_global_to_use']]);
	        } else {
		        $filtre['filtre_global_to_use'] = $_GET['filtre_global_to_use'];
            }
        }
		$equipements = EquipementsBioMedicauxTypes::getListe($filtre);
        ?>
        <select id="listeEquipements" multiple="multiple" style="min-height: 200px!important; width: 416px;" name="listeEquipements[]">
        <?
		foreach ($equipements as $i => $equipement) {
			?>
            <option value="<?=$equipement->id?>"><?= $equipement->nom . ' - ' . $equipement->description ?></option>
			<?php
		}
        ?>
        </select>
        <?
		exit;
	}
	else if($_GET['action'] == 'save_quantite_distribuee')
	{
		$sql = "select count(*) from acquisitions_projets_groupes_equipements_fiches where fiche_id = ? and acquisition_projet_groupe_equipement_id = ?";
		$nb = $db->getOne($sql, [intval($_GET['fiche_id']), intval($_GET['acquisition_projet_groupe_equipement_id'])]);
		if ($nb > 0) {
			$sql = "update acquisitions_projets_groupes_equipements_fiches set quantite_distribuee = ? where fiche_id = ? and acquisition_projet_groupe_equipement_id = ?";
			$db->query($sql, array(intval($_GET['quantite']), intval($_GET['fiche_id']), intval($_GET['acquisition_projet_groupe_equipement_id'])));
		} else {
			$sql = "insert into acquisitions_projets_groupes_equipements_fiches (quantite_distribuee, quantite, quantite_existante, quantite_requise, quantite_demenager, fiche_id, acquisition_projet_groupe_equipement_id) values (?, ?, ?, ?, ?, ?, ?)";
			$db->query($sql, array(intval($_GET['quantite']), 0, 0, 0, 0, intval($_GET['fiche_id']), intval($_GET['acquisition_projet_groupe_equipement_id'])));
		}
		exit;
	}
	else if($_GET['action'] == 'save_quantite_distribuee_financement')
	{
		$sql = "select count(*) from financements_projets_groupes_equipements_fiches where fiche_id = ? and financement_projet_groupe_equipement_id = ?";
		$nb = $db->getOne($sql, [intval($_GET['fiche_id']), intval($_GET['financement_projet_groupe_equipement_id'])]);
		if ($nb > 0) {
			$sql = "update financements_projets_groupes_equipements_fiches set quantite_distribuee = ? where fiche_id = ? and financement_projet_groupe_equipement_id = ?";
			$db->query($sql, array(intval($_GET['quantite']), intval($_GET['fiche_id']), intval($_GET['financement_projet_groupe_equipement_id'])));
		} else {
			$sql = "insert into financements_projets_groupes_equipements_fiches (quantite_distribuee, quantite, quantite_existante, quantite_requise, quantite_demenager, fiche_id, financement_projet_groupe_equipement_id) values (?, ?, ?, ?, ?, ?, ?)";
			$db->query($sql, array(intval($_GET['quantite']), 0, 0, 0, 0, intval($_GET['fiche_id']), intval($_GET['financement_projet_groupe_equipement_id'])));
		}
		exit;
	}
	else if($_GET['action'] == 'save_quantite_distribuee_all')
	{
		$db->autocommit(false);
		if(isset($_POST['quantites']) && is_array($_POST['quantites']))
		{
			foreach($_POST['quantites'] as $iter => $quantites)
			{
				$sql = "select count(*) from acquisitions_projets_groupes_equipements_fiches where fiche_id = ? and acquisition_projet_groupe_equipement_id = ?";
				$nb = $db->getOne($sql, [intval($_POST['fiche_ids'][$iter]), intval($_POST['acquisition_projet_groupe_equipement_ids'][$iter])]);
				if ($nb > 0) {
					$sql = "update acquisitions_projets_groupes_equipements_fiches set quantite_distribuee = ? where fiche_id = ? and acquisition_projet_groupe_equipement_id = ?";
					$db->query($sql, array(intval($_POST['quantites'][$iter]), intval($_POST['fiche_ids'][$iter]), intval($_POST['acquisition_projet_groupe_equipement_ids'][$iter])));
				} else {
					$sql = "insert into acquisitions_projets_groupes_equipements_fiches (quantite_distribuee, quantite, quantite_existante, quantite_requise, quantite_demenager, fiche_id, acquisition_projet_groupe_equipement_id) values (?, ?, ?, ?, ?, ?, ?)";
					$db->query($sql, array(intval($_POST['quantites'][$iter]), 0, 0, 0, 0, intval($_POST['fiche_ids'][$iter]), intval($_POST['acquisition_projet_groupe_equipement_ids'][$iter])));
				}
			}
		}
		$db->commit();
		exit;
	}
	else if($_GET['action'] == 'unlockUnlockDepot')
	{
		$db->autocommit(false);
        $isLock = (int)$_GET['isLock'];
        $id = (int)$_GET['id'];
        $depot = new FinancementsProjetsDepots($id);
        $projet = new FinancementsProjets($depot->financement_projet_id);

        $sql = "update financements_projets_depots set ts_verrouille = " . ($isLock ? 'now()' : 'null') . ", usager_verrouille_id = ? where id = ?";
        $db->query($sql, [getIdUsager(), $id]);

        if ($isLock) {
	        $sql = "delete from financements_projets_depots_usagers where financement_projet_depot_id = ?";
	        $db->query($sql, [$id]);
        }

		$db->commit();

		if ($isLock) {
            ?>
            <script>
				parent.window.location.reload();
            </script>
            <?
		} else {
			header('Location: index.php?section=financement&module=admin_depot&financement_projet_id=' . $projet->id);
		}
		exit;
	}
	else if($_GET['action'] == 'resetDepot')
	{
		$db->autocommit(false);

		$id = (int)$_GET['id'];
		$depot = new FinancementsProjetsDepots($id);
		$projet = new FinancementsProjets($depot->financement_projet_id);

		$sql = "update financements_projets_depots set ts_photo_last = null, usager_photo_last_id = null, ts_photo_j0 = null, usager_photo_j0_id = null where id = ?";
		$db->query($sql, [$id]);

		$sql = "update equipements_bio_medicaux set 
                        " . $depot->getSqlToNullDepot() . "
                    where projet_id = ? and depot_source" . $depot->source_financement_id . "_id = ? and fiche_id in (" . db::placeHolders($projet->fiches) . ")";
		$db->query($sql, array_merge([getProjetId(), $id], array_keys($projet->fiches)));

		$db->commit();

		header('Location: index.php?section=financement&module=admin_depot&financement_projet_id=' . $projet->id);
		exit;
	}
	else if($_GET['action'] == 'rapportDifferences') {
        $depot = new FinancementsProjetsDepots((int)$_GET['id']);
        $depot->exportRapportDifference($_GET['type']);
        exit;
	}
	else if($_GET['action'] == 'photoDepot') {
		$db->autocommit(false);

		$id = (int)$_GET['id'];
		$type = $_GET['type'];
		$depot = new FinancementsProjetsDepots($id);
		$projet = new FinancementsProjets($depot->financement_projet_id);

		$trigger = -1;
        $strColTsJ0 = "";
        if ($type == 'last') {
	        $trigger = isset($_GET['alsoJ0']) ? $depot->source_financement_id : ($depot->source_financement_id * 10);
        } else if ($type == 'j0') {
	        $trigger = $depot->source_financement_id;
	        $strColTsJ0 = ", ts_j0_source" . $depot->source_financement_id . " = now()";
        }

        if ($trigger > 0 && count($depot->equipements) > 0) {
	        $sql = "update equipements_bio_medicaux set trigger_maj = ? $strColTsJ0 where equipement_bio_medicaux_type_id in (" . db::placeHolders(array_keys($depot->equipements)) . ") and depot_source" . $depot->source_financement_id . "_id = ? 
	        and fiche_id in (" . db::placeHolders(array_keys($projet->fiches)) . ")";
	        $data = array_merge([$trigger], array_keys($depot->equipements), [$id], array_keys($projet->fiches));
	        $db->query($sql, $data);

	        $sql = "update equipements_bio_medicaux set trigger_maj = 0 where equipement_bio_medicaux_type_id in (" . db::placeHolders(array_keys($depot->equipements)) . ") and depot_source" . $depot->source_financement_id . "_id = ? 
	        and fiche_id in (" . db::placeHolders(array_keys($projet->fiches)) . ")";
	        $data = array_merge(array_keys($depot->equipements), [$id], array_keys($projet->fiches));
	        $db->query($sql, $data);

	        if ($trigger > 9) {
		        $sql = "update financements_projets_depots set ts_photo_last = now(), usager_photo_last_id = ? where id = ?";
		        $db->query($sql, [getIdUsager(), $id]);
	        } else {
		        $sql = "update financements_projets_depots set ts_photo_last = now(), usager_photo_last_id = ?, ts_photo_j0 = now(), usager_photo_j0_id = ? where id = ?";
		        $db->query($sql, [getIdUsager(), getIdUsager(), $id]);
            }
        }

		$db->commit();

		header('Location: index.php?section=financement&module=admin_depot&financement_projet_id=' . $projet->id);
		exit;
    }
    else if($_GET['action'] == 'update_projet')
    {

        if (isset($_GET['project'])) {
            echo "test";
        } else {
            echo "test1";
        }
       // exit;
        /*$sql = "update acquisitions_projets_groupes_equipements_fiches set quantite = ? where fiche_id = ? and acquisition_projet_groupe_equipement_id = ?";
        $db->query($sql, array(intval($_GET['quantite']), intval($_GET['fiche_id']), intval($_GET['acquisition_projet_groupe_equipement_id'])));
        exit;*/
    }
    else if($_GET['action'] == 'envoi_courriel_usager')
    {
        $idUsager = intval($_GET['idUsager']);
        $usager = new Usager($idUsager);

        $usager->envoiCourrielOuverture();

        header('Location: index.php?section=admin&module=index&type=client');
        die();
    }
    else if($_GET['action'] == 'reset_all_passwords')
    {
	    $usagers = Usager::getListe();
        foreach ($usagers as $usager) {
	        $usager->envoiCourrielResetMPs();
        }
	    print 'DONE!';
	    die();
    }
    else if($_GET['action'] == 'majNotifications')
    {
        $nb = NotificationsGestion::getNbNotificationsNonLu();
        if($nb > 0)
        {
?>
                <a href="index.php?section=notifications&module=index" style=" font-weight: bold; color: red; margin-right: 15px;">
                    <?=$nb.txt(' notification(s) non lu(s)')?></a>
<?php
        }
    }
    else if($_GET['action'] == 'pushProjectToWebService')
	{
		$projet = new Projets(intval($_GET['projet_id']));
		$result = $projet->pushToWebService();

		if(dm_trim($result) <> 'OK')
		{
			setErreur(txt('Une erreur s\'est produite lors de l\'échange des données.'));
			if(!$isDev)
			{
				envoiCourriel("<EMAIL>", "Erreur: docmatic (pushProjectToWebService)", $result);
			}
		}
		header('Location: index.php?section=relations_externes&module=FamilyView&projet_id='.intval($_GET['projet_id']));
		die();
	}
    else if($_GET['action'] == 'delete_formulaire')
    {
        $className = $_GET['formulaire'];
        $className::deleteFormulaire(intval($_GET['fiche_id']));
    }
    else if($_GET['action'] == 'api_key_generation')
    {
		if(havePermission('api_key_access') || isMaster())
		{
			$api_key = uniqueKey(uniqid());
            $api_key_expiration = date('Y-m-d H:i:s', strtotime('+ 4 hours'));
            $api_key_expiration_all = date('Y-m-d H:i:s', strtotime('+ 8 hours'));

			$sql = "update usagers set api_key = ?, api_key_expiration = ?, api_key_expiration_all = ?, finger_print = null where id = ?";
			$db->query($sql, array($api_key, $api_key_expiration, $api_key_expiration_all, getIdUsager()));

            $_SESSION['usager_'.$CodeClient]->api_key = $api_key;
            $_SESSION['usager_'.$CodeClient]->api_key_expiration = $api_key_expiration;
            $_SESSION['usager_'.$CodeClient]->api_key_expiration_all = $api_key_expiration_all;

			print $api_key;
		}
    }
    else if($_GET['action'] == 'api_key_excel_generation')
    {
		if(havePermission('api_key_access_filematic') || isMaster())
		{
			$api_key_excel = uniqueKey(uniqid());
            $api_key_excel_expiration = date('Y-m-d H:i:s', strtotime('+ 1 month'));
            $api_key_excel_nb_activation = $_SESSION['usager_'.$CodeClient]->api_key_excel_nb_activation + 1;

			$sql = "update usagers set api_key_excel = ?, api_key_excel_expiration = ?, api_key_excel_nb_activation = ?, finger_print = null where id = ?";
			$db->query($sql, array($api_key_excel, $api_key_excel_expiration, $api_key_excel_nb_activation, getIdUsager()));

            $_SESSION['usager_'.$CodeClient]->api_key_excel = $api_key_excel;
            $_SESSION['usager_'.$CodeClient]->api_key_excel_expiration = $api_key_excel_expiration;
            $_SESSION['usager_'.$CodeClient]->api_key_excel_nb_activation = $api_key_excel_nb_activation;

			print $api_key_excel;
		}
    }
    else if($_GET['action'] == 'getTensionTypeFromPhase')
    {
        TensionsTypes::getOptions(array('nombre_phase_type_id' => intval($_GET['nombre_phase_type_id'])), 0, true);
    }
    else if($_GET['action'] == 'getRecommendationFromChampTexteDenormalise')
    {
        $row = new ChampsTextesDenormalises(intval($_GET['champ_texte_denormalise_id']));
        print $row->genererTextesStatique($_GET);
    }
    else if($_GET['action'] == 'getRecommendationFromChampCombine')
    {
        $row = new ChampsTextesDenormalises(intval($_POST['champ_texte_denormalise_id']));
        print json_encode($row->getRecommendationFromTexte($_POST['texte']));
    }
    else if($_GET['action'] == 'getCalibreProtectionTypeFromPhase')
    {
        CalibresProtectionsTypes::getOptions(array('nombre_phase_fil_primaire_id' => intval($_GET['nombre_phase_type_id'])), 0, true);
    }
    else if ($_GET['action'] == 'get_list_item_ids_from_formulaire_id') {
	    $formulaire_id = intval($_GET['formulaire_id']);
	    $form = new Formulaires($formulaire_id);
	    $first_champ = $form->getFirstChamp();
	    $class_first_champ = $first_champ['classe'];
        $filter = [];
        if (isset($_GET['exclureEquipementsNonEditable'])) {
	        $filter = [ 'exclureEquipementsNonEditable' => 1 ];
        }
	    ?>
        <option value="">- <?=$first_champ['titre']?> -</option>
        <?
	    $class_first_champ::getOptions($filter);
	    exit;
    }
    else if ($_GET['action'] == 'getInfoFormulaireFiche') {
		$formulaire_id = intval($_GET['formulaire_id']);
	    $fiche_id = intval($_GET['fiche_id']);
	    $fiche = new Fiches($fiche_id);
		$form = new Formulaires($formulaire_id);
		$className = $form->nom;
		print json_encode([
		    'form' => $className::TITRE,
            'fiche' => $fiche->code
        ]);
		exit;
	}
    else if($_GET['action'] == 'getFichesEquipementsFromListeItem')
    {
        $acquisition_groupe_id = intval($_GET['acquisition_groupe_id']);
        $liste_item_id = intval($_GET['liste_item_id']);
        $fiches_sel = isset($_GET['fiches_sel']) && is_array($_GET['fiches_sel']) ? $_GET['fiches_sel'] : array();

        $acquisition_groupe = new AcquisitionsGroupes($acquisition_groupe_id);
        $form = new Formulaires($acquisition_groupe->formulaire_id);
        $first_champ = $form->getFirstChamp();
        $formClassName = $form->nom;
        $class_first_champ = $first_champ['classe'];

        $where = "";
        $data = array();

        if($liste_item_id > 0)
        {
            $where .= " and tbl.id = ?";
            $data[] = $liste_item_id;
        }

        if(count($fiches_sel) > 0)
        {
            $where .= " and f.id in (" . db::placeHolders($fiches_sel) . ")";
            $data = array_merge($data, $fiches_sel);
        }

        $sql = "select f.id as fiche_id, f.code, tbl.id as liste_item_id,
                    sum(qte_requise - qte_existante) as quantite,
                    case when tbl.description".($Langue == 'en' ? '_en' : '')." is not null and tbl.description".($Langue == 'en' ? '_en' : '')." <> '' then concat(tbl.nom, ' - ', tbl.description".($Langue == 'en' ? '_en' : '').") else tbl.nom end as equipement
                from fiches f
                join `".$formClassName::SQL_TABLE_NAME."` tb2 on tb2.fiche_id = f.id
                join `".$class_first_champ::SQL_TABLE_NAME."` tbl on tbl.id = tb2.`".$first_champ['champ_id']."`
                where true $where
                group by f.id, f.code, liste_item_id, equipement
                order by f.code";
        //printDebug(dm_nl2br(db::interpolateQuery($sql, $data)));
        $rows = $db->getAll($sql, $data);

        foreach($rows as $i => $row)
        {
?>
            <option value="<?=$row['fiche_id'].'-'.$row['liste_item_id']?>"><?=$row['code']." || ".dm_substr($row['equipement'], 0, 25).'... ('.$row['quantite'].')'?></option>
<?php
        }
    }
    else if($_GET['action'] == 'getCalibreCableTypeFromPhaseAlu')
    {
        CablesCalibresTypes::getOptions(array('nombre_phase_fil_primaire_id' => intval($_GET['nombre_phase_type_id']), 'nomlike' => '%AL%'), 0, true);
    }
    else if($_GET['action'] == 'getCalibreCableTypeFromPhaseCu')
    {
        CablesCalibresTypes::getOptions(array('nombre_phase_fil_primaire_id' => intval($_GET['nombre_phase_type_id']), 'nomlike' => '%CU%'), 0, true);
    }
    else if (isset($_GET['action']) && $_GET['action'] == 'display_detail_notification') {
	    $notification_gestion_id = intval($_GET['notification_gestion_id']);
	    $notification_gestion = new NotificationsGestion($notification_gestion_id);

	    $data = $notification_gestion->getLogsDataArray($_GET['anmois']);
	    ?>
        <table>
		    <?
		    foreach ($data as $i => $lignes) {
			    ?><tr><?
			    foreach ($lignes as $j => $col_data) {
				    $type_col = $i == 0 ? 'th' : 'td';
				    ?><<?=$type_col?> style="min-width: 75px;"><?=$col_data?></<?=$type_col?>><?
			    }
			    ?></tr><?
		    }
		    ?>
        </table>
	    <?

	    $db->autocommit(0);
	    $sql = "update fiches_logs_notifications set ts_lu = current_timestamp where notification_gestion_id = ? and fiches_logs_notifications.usager_id = ?";
	    $db->query($sql, array($notification_gestion_id, getIdUsager()));
	    $db->commit();
    }
    else if($_GET['action'] == 'export_excel_for_import')
    {
        $import_id = intval($_GET['import_id']);
        $import = new Imports($import_id);
        $import->exportExcel();
    }
    else if($_GET['action'] == 'reset_data_import')
	{
		$import_id = intval($_GET['import_id']);
		$import = new Imports($import_id);
		$import->resetData();

        header('Location: index.php?section=fiche&module=importation');
        die();
	}
    else if($_GET['action'] == 'getHPFromTensionType')
    {
        HpTypes::getOptions(array('tension_type_id' => intval($_GET['tension_type_id'])), 0, true);
    }
    else if($_GET['action'] == 'getAutomatesFromBatiment')
    {
        Automate::getOptions(array('batiment_id' => intval($_GET['batiment_id'])), 0, true);
    }
    else if($_GET['action'] == 'getFormulaireFromChampsExternes')
    {
        if(isset($_GET['correlation_champ_externe_id']) && intval($_GET['correlation_champ_externe_id']) == 0 && isset($_GET['type_import']) && $_GET['type_import'] <> 'hybrides')
        {
?>
            <option value=""> </option>
<?php
            if(isset($_GET['is_export']) && intval($_GET['is_export']) == 1)
            {
?>
                <option value="ChampsTextesExports"><?=txt('Matrices de champs combinés')?></option>
<?php
            }
        }
        if(isset($_GET['type_import']))
        {
            $ids = isset($_GET['formulaire_id']) && intval($_GET['formulaire_id']) > 0 ? array(intval($_GET['formulaire_id'])) : Formulaires::getFormulairesIdForTypeForm($types_assocs[$_GET['type_import']]);
            Formulaires::getOptionsByNom(array('fiche_sous_type_id' => intval($_GET['fiche_sous_type_id']), 'ids' => $ids, 'correlation_champ_externe_id' => (isset($_GET['correlation_champ_externe_id']) ? intval($_GET['correlation_champ_externe_id']) : 0)));
        }
    }
    else if($_GET['action'] == 'getFormulaireForTypeImport')
    {
?>
        <option value=""> </option>
<?php
        if(isset($_GET['type_import']))
        {
            $filtre = array('ids' => Formulaires::getFormulairesIdForTypeForm($types_assocs[$_GET['type_import']]));
            if(isset($_GET['fiche_sous_type_id']))
            {
                $filtre = array('fiche_sous_type_id' => intval($_GET['fiche_sous_type_id']), 'ids' => Formulaires::getFormulairesIdForTypeForm($types_assocs[$_GET['type_import']]));
            }
            Formulaires::getOptions($filtre);
        }
    }
    else if($_GET['action'] == 'getFormulaireForSousType')
    {
?>
        <option value=""><?=txt(' - Sélectionnez un formulaire - ')?></option>
<?php
        $forms = Formulaires::getListeFormulairesEditables(intval($_GET['fiche_sous_type_id']));
        foreach ($forms as $id => $form) {
            ?>
            <option value="<?= $id ?>"><?= $form['nom'] ?></option>
            <?php
        }
    }
    else if($_GET['action'] == 'getFormulaireForType')
    {
?>
        <option value=""><?=txt(' - Sélectionnez un formulaire - ')?></option>
<?php
        $filtre = array();
        if(isset($_GET['fiche_type_id']) && intval($_GET['fiche_type_id']) > 0)
        {
            $filtre = array('fiche_type_id' => intval($_GET['fiche_type_id']));
        }
        Formulaires::getOptions($filtre);
    }
    else if($_GET['action'] == 'getChampsFromFormulaire')
    {
        if(isset($_GET['formulaire']) && dm_strlen($_GET['formulaire']) > 0 && class_exists($_GET['formulaire']))
        {
            $formulaire = $_GET['formulaire'];

            if($formulaire == 'ChampsTextesExports')
            {
                $exports_champs = ChampsTextesExports::getListe(array('fiche_sous_type_id' => intval($_GET['fiche_sous_type_id'])));
?>
                <option value=""> </option>
<?php
                foreach($exports_champs as $champ_texte_export_id => $export_champ)
                {
?>
                    <option value="<?=$champ_texte_export_id?>"><?=$export_champ->nom?></option>
<?php
                }
            }
            else if(isset($_GET['correlation_champ_externe_id']) && intval($_GET['correlation_champ_externe_id']) > 0)
            {
                $metas = $formulaire::getFlatChampsSaisieMetaData();
                $liste = CorrelationsChampsExternes::getListe(array('formulaire' => $_GET['formulaire'], 'id' => intval($_GET['correlation_champ_externe_id'])));
                foreach ($liste as $id => $val)
                {
?>
                    <option value="<?=$val->nom_champ?>"><?=$metas[$val->nom_champ]['titre']?></option>
<?php
                }
            }
            else
            {
?>
                <option value=""> </option>
<?php
                $metas = $formulaire::getFlatChampsSaisieMetaData();
                foreach($metas as $key => $meta)
                {
                    if((isset($meta['titre']) && !isset($meta['liste']) && !isset($meta['source_externe']) && dm_strlen($meta['titre']) > 0 && !in_array($key, array('quantite_existante', 'quantite', 'quantite_fantome', 'conformite_du_besoin_electricite', 'conformite_du_besoin_plomberie', 'conformite_du_besoin_mecanique', 'conformite_du_besoin_telecom', 'conformite_du_besoin_architecture', 'conformite_du_besoin_structure', 'conformite_du_besoin'))) || ($key == 'quantite' && in_array($formulaire, array('ProprietesElectriques', 'ProprietesElectriquesAuxiliaires'))))
                    {
?>
                        <option value="<?=$key?>"><?=$meta['titre']?></option>
<?php
                    }
                }
            }
        }
        else
        {
?>
            <option value=""> </option>
<?php
        }
    }
    else if($_GET['action'] == 'getChampsFromFiltreEquipement') {
	    $typeChamps = $_GET['typeChamps'];
	    $formulaire = '';
	    $metas = [];
	    if ($typeChamps == 'param_projets_acquisitions') {
		    $admin = AcquisitionsProjetsAdministrations::getAdminForProjet();
		    foreach (AcquisitionsProjetsAdministrations::getMetasForExportation() as $blocsNiv2) {
                foreach ($blocsNiv2 as $bloc) {
                    if (in_array($bloc['ensemble'], ['projet', 'groupe']) && $admin->isBlocFiltrable($bloc['champs'])) {
                        foreach ($bloc['champs'] as $champ) {
                            if (isset($champ['filtrable']) && $champ['filtrable'] == 1 && $admin->isChampActifOrColonneVisible($champ['nom'])) {
                                ?>
                                <option value="<?= $champ['nom'] ?>" id="champ_nom_options_<?=$champ['nom']?>"><?= $admin->getLabelForChampColonne($champ['nom']) ?></option>
                                <?
                            }
                        }
                    }
                }
		    }
        } else if ($typeChamps == '144') {
	        $formulaire = 'EquipementsBioMedicauxNoCoord';
	        $metas = $formulaire::getFlatChampsSaisieMetaData();

            $formulaires_liste = null;
            $key_param_intrinseque = '';
            $className_intrinseque = '';

            if(isset($metas) && is_array($metas))
            {
                foreach($metas as $key => $meta)
                {
                    if(isset($meta['classe']) && dm_strlen($meta['classe']) > 0)
                    {
                        $className = $meta['classe'];
                        $formulaires_listes = array();
                        if (method_exists($className, 'getFormulairesListes')
                            && is_callable(array($className, 'getFormulairesListes')))
                        {
                            $formulaires_listes = $className::getFormulairesListes($className);
                        }
                        if(count($formulaires_listes) > 0)
                        {
                            $key_param_intrinseque = $key;
                            $formulaires_liste = $formulaires_listes[0];
                            $className_intrinseque = $className;
                            break;
                        }
                    }
                }
            }
            ?>
            <option value=""> </option>
            <?
            if ($formulaire == 'EquipementsBioMedicauxNoCoord') {
                $champs_filtres_speciaux = array_merge($champs_filtres_speciaux, $champs_filtres_speciaux_equipements_bio_medicaux);
            }
            foreach ($champs_filtres_speciaux as $key => $meta) {
                ?>
                <option value="<?=$key?>" id="champ_nom_options_<?=$key?>"><?=$meta['titre']?></option>
                <?
            }

            if(dm_strlen($formulaire) > 0)
            {
                if(dm_strlen($key_param_intrinseque) > 0 && isset($formulaires_liste) && isset($formulaires_liste->listes_parametres))
                {
                    foreach($formulaires_liste->listes_parametres as $id => $param)
                    {
                        $key = 'intrinseque__'.$key_param_intrinseque.'__'.$param['id'];
                        ?>
                        <option value="intrinseque__<?=$key_param_intrinseque?>__<?=$param['id']?>" id="champ_nom_options_intrinseque__<?=$key_param_intrinseque?>__<?=$param['id']?>" data-classe="<?=$className_intrinseque?>"><?=$param['nom_parametre'].txt(' [Paramètres intrinsèques]')?></option>
                        <?php
                    }
                }
            }
        }
	}
    else if($_GET['action'] == 'getChampsFromFormulaireId')
    {
        $with_qte = isset($_GET['with_qte']) ? (int)$_GET['with_qte'] : 0;
        $isFormatJson = isset($_GET['format']) && $_GET['format'] == 'json';
        $champ_nom_value = $_GET['champ_nom_value'] ?? [];
        $data = [];
        if(empty($_GET['formulaire_id']))
        {
            if ($isFormatJson) {
                print json_encode([]);
            } else {
                ?>
                <option value=""><?=(isset($_GET['champ_nom_value']) ? txt('- Champ -') : '')?></option>
                <?php
            }
            exit;
        }
        if (str_starts_with($_GET['formulaire_id'], 'custom_')) {
            list($custom, $formulaire_custom_id) = explode('_', $_GET['formulaire_id']);
            $formulaireCustom = new FormulairesCustomsParametres($formulaire_custom_id);
        } else {
            $formulaire_id = (int)$_GET['formulaire_id'];

            $formulaire = Formulaires::getNomFromId($formulaire_id);

            $metas = $formulaire::getFlatChampsSaisieMetaData();
            $formulaires_liste = null;
            $key_param_intrinseque = '';
            $className_intrinseque = '';

            if(isset($metas) && is_array($metas))
            {
                foreach($metas as $key => $meta)
                {
                    if(isset($meta['classe']) && dm_strlen($meta['classe']) > 0)
                    {
                        $className = $meta['classe'];
                        $formulaires_listes = array();
                        if (method_exists($className, 'getFormulairesListes')
                            && is_callable(array($className, 'getFormulairesListes')))
                        {
                            $formulaires_listes = $className::getFormulairesListes($className);
                        }
                        if(count($formulaires_listes) > 0)
                        {
                            $key_param_intrinseque = $key;
                            $formulaires_liste = $formulaires_listes[0];
                            $className_intrinseque = $className;
                            break;
                        }
                    }
                }
            }
        }
        if (!isset($_GET['sansOptionVide'])) {
            if ($isFormatJson) {
                $data[] = ['id' => '', 'nom' => (isset($_GET['champ_nom_value']) ? txt('- Champ -') : '')];
            } else {
?>
                <option value=""><?=(isset($_GET['champ_nom_value']) ? txt('- Champ -') : '')?></option>
<?
            }
        }
        if (!isset($_GET['ignorerChampsCustom'])) {
            foreach ($champs_filtres_speciaux as $key => $meta) {
                if (in_array($key, ['presence_donnees', 'absence_donnees']) || !isset($formulaireCustom)) {
                    if ($isFormatJson) {
                        $data[] = ['id' => $key, 'nom' => html_entity_decode($meta['titre']), 'checked' => in_array($key, $champ_nom_value)];
                    } else {
    ?>
                        <option value="<?=$key?>" id="champ_nom_options_<?=$key?>" <?=(in_array($key, $champ_nom_value) ? ' selected="selected"' : '')?>><?=$meta['titre']?></option>
    <?
                    }
                }
            }
        }

        if (isset($formulaireCustom)) {
            foreach ($formulaireCustom->listes_parametres as $key => $parametre)
            {
                if($parametre->format <> 'section')
                {
                    if ($isFormatJson) {
                        $data[] = ['id' => $key, 'nom' => html_entity_decode($parametre->getRaw('nom_parametre')), 'checked' => in_array($key, $champ_nom_value)];
                    } else {
                        $sel = in_array($key, $champ_nom_value) ? ' selected="selected"' : '';
                        ?>
                        <option value="<?=$key?>" id="champ_nom_options_<?=$key?>"<?=$sel?>><?=$parametre->getRaw('nom_parametre')?></option>
                        <?php
                    }
                }
            }
        } else if(isset($formulaire) && dm_strlen($formulaire) > 0) {
            $metas = $formulaire::getFlatChampsSaisieMetaData();
            $array_champs_exclus = array('conformite_du_besoin_electricite', 'conformite_du_besoin_plomberie', 'conformite_du_besoin_mecanique', 'conformite_du_besoin_telecom', 'conformite_du_besoin_architecture', 'conformite_du_besoin_structure', 'commentaires_electricite', 'commentaires_plomberie', 'commentaires_mecanique', 'commentaires_telecom', 'commentaires_architecture', 'commentaires_structure', 'conformite_du_besoin');
            if($with_qte == 0)
            {
	            $array_champs_exclus[] = 'quantite_fantome';
	            $array_champs_exclus[] = 'quantite_existante';
	            $array_champs_exclus[] = 'quantite';
	            $array_champs_exclus[] = 'quantite_demenager';
            }
			foreach($metas as $key => $meta)
			{
				if((isset($meta['titre']) && !isset($meta['liste']) && !isset($meta['source_externe']) && dm_strlen($meta['titre']) > 0 && !in_array($key, $array_champs_exclus)) || ($key == 'quantite' && in_array($formulaire, array('ProprietesElectriques', 'ProprietesElectriquesAuxiliaires'))))
				{
                    if ($isFormatJson) {
                        $data[] = ['id' => $key, 'nom' => html_entity_decode($meta['titre']), 'checked' => in_array($key, $champ_nom_value)];
                    } else {
                        $sel = in_array($key, $champ_nom_value) ? ' selected="selected"' : '';
    ?>
                        <option value="<?=$key?>" id="champ_nom_options_<?=$key?>" data-classe="<?=(isset($meta['classe']) ? $meta['classe'] : '')?>"<?=$sel?>><?=$meta['titre']?></option>
    <?php
                    }
				}
			}

            if ($formulaire == 'EquipementsBioMedicaux') {
                $champs = Config::instance()->getChampsFiltresSpeciauxEquipementsBioMedicaux(isset($_GET['addExtraFiltre']));
                foreach ($champs as $key => $meta) {
                    if ($isFormatJson) {
                        $data[] = ['id' => $key, 'nom' => html_entity_decode($meta['titre']), 'checked' => in_array($key, $champ_nom_value)];
                    } else {
                        ?>
                        <option value="<?=$key?>" id="champ_nom_options_<?=$key?>" <?=(in_array($key, $champ_nom_value) ? ' selected="selected"' : '')?>><?=$meta['titre']?></option>
                        <?
                    }
                }
            }

            if(dm_strlen($key_param_intrinseque) > 0 && isset($formulaires_liste) && isset($formulaires_liste->listes_parametres) && !isset($_GET['ignorerChampsIntrinseques']))
            {
                foreach($formulaires_liste->listes_parametres as $id => $param)
                {
                    $key = 'intrinseque__'.$key_param_intrinseque.'__'.$param['id'];
                    if ($isFormatJson) {
                        $data[] = ['id' => $key, 'nom' => $param['nom_parametre'].txt(' [Paramètres intrinsèques]', false), 'checked' => in_array($key, $champ_nom_value)];
                    } else {
                        $sel = in_array($key, $champ_nom_value) ? ' selected="selected"' : '';
    ?>
                        <option value="intrinseque__<?=$key_param_intrinseque?>__<?=$param['id']?>" id="champ_nom_options_intrinseque__<?=$key_param_intrinseque?>__<?=$param['id']?>" data-classe="<?=$className_intrinseque?>"<?=$sel?>><?=$param['nom_parametre'].txt(' [Paramètres intrinsèques]')?></option>
    <?php
                    }
                }
            }
        } else {

        }
        if ($isFormatJson) {
            print json_encode($data);
        }
    }
    else if($_GET['action'] == 'getValeursFromChampNom') {
        if ($_GET['formulaire_id'] == 'param_projets_acquisitions') {
	        $champ_nom = $_GET['champ_nom'];
            $metaIsId = [
                'responsable_dossier_1_id' => 'AcquisitionsResponsablesDossiersTypes',
                'responsable_dossier_2_id' => 'AcquisitionsResponsablesDossiersTypes',
                'responsable_dossier_3_id' => 'AcquisitionsResponsablesDossiersTypes',
                'responsable_dossier_4_id' => 'AcquisitionsResponsablesDossiersTypes',
                'responsable_dossier_5_id' => 'AcquisitionsResponsablesDossiersTypes',
                'responsable_dossier_6_id' => 'AcquisitionsResponsablesDossiersTypes',
                'responsable_dossier_7_id' => 'AcquisitionsResponsablesDossiersTypes',
                'responsable_dossier_8_id' => 'AcquisitionsResponsablesDossiersTypes',
	            'responsable_dossier_9_id' => 'AcquisitionsResponsablesDossiersTypes',
	            'responsable_dossier_10_id' => 'AcquisitionsResponsablesDossiersTypes',
	            'responsable_dossier_a_id' => 'AcquisitionsResponsablesDossiersATypes',
	            'responsable_dossier_b_id' => 'AcquisitionsResponsablesDossiersBTypes',
	            'responsable_dossier_c_id' => 'AcquisitionsResponsablesDossiersCTypes',
	            'responsable_dossier_d_id' => 'AcquisitionsResponsablesDossiersDTypes',
	            'responsable_dossier_e_id' => 'AcquisitionsResponsablesDossiersETypes',
	            'responsable_dossier_f_id' => 'AcquisitionsResponsablesDossiersFTypes',
	            'responsable_dossier_g_id' => 'AcquisitionsResponsablesDossiersGTypes',
	            'responsable_dossier_h_id' => 'AcquisitionsResponsablesDossiersHTypes',
	            'responsable_dossier_i_id' => 'AcquisitionsResponsablesDossiersITypes',
	            'responsable_dossier_j_id' => 'AcquisitionsResponsablesDossiersJTypes',
	            'menu_supplementaire_a_id' => 'AcquisitionsMenusSupplementairesATypes',
	            'responsable_dossier_contractuel_id' => 'Usager',
                'priorite_acquisition_id' => 'PrioritesAcquisitionsTypes',
                'bloquant_type_id' => 'BloquantsTypes',
                'site_livraison_id' => 'SitesLivraisonsTypes',
                'local_transitoire_id' => 'LocalsTransitoiresTypes',
                'sous_volet_responsable_id' => 'AcquisitionsSousVoletsResponsablesTypes',
                'phase_incidence_contraignante_id' => 'AcquisitionsPhasesIncidencesContraignantesTypes',
                'priorisation_id' => 'AcquisitionsProjetsPriorisationsTypes',
                'mode_sollicitation_id' => 'AcquisitionsProjetsModesSollicitationsTypes',
            ];
	        $admin = AcquisitionsProjetsAdministrations::getAdminForProjet();
            $champsBd = AcquisitionsProjetsAdministrations::getChampsColonnesExtraData();
            foreach (AcquisitionsProjetsAdministrations::getMetasForExportation() as $blocsNiv2) {
                foreach ($blocsNiv2 as $bloc) {
                    if (in_array($bloc['ensemble'], ['projet', 'groupe']) && $admin->isBlocFiltrable($bloc['champs'])) {
                        foreach ($bloc['champs'] as $champ) {
                            if (isset($champ['filtrable']) && $champ['filtrable'] == 1 && $admin->isChampActifOrColonneVisible($champ['nom'])) {
                                if ($champ['nom'] == $champ_nom) {
	                                $col_bd = $champsBd[$champ['nom']]['col_bd'];
                                    ?>
                                    <select id="valeur" style="width: 340px; height: auto!important;">
                                        <?
                                        if (isset($metaIsId[$col_bd])) {
                                            ?>
                                            <option value=""> </option>
                                            <option value="%vide%"<?=("%vide%" == (isset($filtre[$champ['nom']]) ? $filtre[$champ['nom']] : '') ? ' selected="selected"' : '')?>><?=txt('[Champ vide]')?></option>
                                            <option value="%non-vide%"<?=("%non-vide%" == (isset($filtre[$champ['nom']]) ? $filtre[$champ['nom']] : '') ? ' selected="selected"' : '')?>><?=txt('[Champ non-vide]')?></option>
                                            <?
                                            $class = $metaIsId[$col_bd];
                                            $class::getOptions(array(), (isset($filtre[$champ['nom']]) ? $filtre[$champ['nom']] : ''));
                                        } else {
                                            AcquisitionsProjets::getValeursForFiltreAvance($bloc['ensemble'], $col_bd, (isset($filtre[$champ['nom']]) ? $filtre[$champ['nom']] : ''));
                                        }
                                    ?>
                                    </select>
                                    <?
                                }
                            }
                        }
                    }
                }
            }
        } else {
            if (str_starts_with($_GET['formulaire_id'], 'custom_')) {
                list($custom, $formulaire_custom_id) = explode('_', $_GET['formulaire_id']);
                $formulaireCustom = new FormulairesCustomsParametres($formulaire_custom_id);
                $champ_nom = $_GET['champ_nom'];

                if (isset($champs_filtres_speciaux[$champ_nom])) {
                    ?>
                    <input type="hidden" id="valeur" value=""/>
                    <?php
                } else {
                    $sql = "select distinct valeur
                            from formulaires_customs_parametres_valeurs flpv
                            where formulaire_custom_parametre_id = ?
                            order by valeur";
                    $valeurs = db::instance()->getAll($sql, array($champ_nom));
                    ?>
                    <select id="valeur" style="width: 340px; height: auto!important;">
                        <option value=""> </option>
                        <option value="-1">[<?= txt('Aucun') ?>]</option>
                        <?php
                        foreach ($champs_filtres_speciaux as $key => $metaSpecial) {
                            if ($key == 'presence_donnees' ) {
                                ?>
                                <option value="<?=$key?>" id="champ_nom_options_<?=$key?>"><?=$metaSpecial['titre']?></option>
                                <?
                            }
                        }
                        foreach ($valeurs as $i => $valeur) {
                            if (dm_strlen(dm_trim($valeur['valeur'])) > 0) {
                                ?>
                                <option value="<?= $valeur['valeur'] ?>"><?= $valeur['valeur'] ?></option>
                                <?php
                            }
                        }
                        ?>
                    </select>
                    <?php
                }
            } else {
                $formulaire_id = intval($_GET['formulaire_id']);
                $formulaire = Formulaires::getNomFromId($formulaire_id);
                $champ_nom = $_GET['champ_nom'];

                $is_param_intrinseque = false;
                $exploded_champ_nom = dm_explode('__', $champ_nom);
                if ($exploded_champ_nom[0] == 'intrinseque') {
                    $champ_nom_intrinseque = $exploded_champ_nom[1];
                    $valeur_id_intrinseque = $exploded_champ_nom[2];
                    $is_param_intrinseque = true;
                }

                if ($is_param_intrinseque) {
                    $sql = "select distinct valeur
                            from formulaires_listes_parametres_valeurs flpv
                            join formulaires_listes_parametres flp on flp.id = formulaire_liste_parametre_id
                            where formulaire_liste_parametre_id = ?
                            order by valeur";
                    $valeurs = $db->getAll($sql, array($valeur_id_intrinseque));
                    ?>
                    <select id="valeur" style="width: 340px; height: auto!important;">
                        <option value=""> </option>
                        <?php
                        foreach ($valeurs as $i => $valeur) {
                            if (dm_strlen(dm_trim($valeur['valeur'])) > 0) {
                                ?>
                                <option value="<?= $valeur['valeur'] ?>"><?= $valeur['valeur'] ?></option>
                                <?php
                            }
                        }
                        ?>
                    </select>
                    <?php
                } else {
                    if (isset($champs_filtres_speciaux[$champ_nom])) {
                        ?>
                        <input type="hidden" id="valeur" value=""/>
                        <?php
                    } else if (isset($champs_filtres_speciaux_equipements_bio_medicaux[$champ_nom])) {
                        ?>
                        <select id="valeur" style="width: 340px; height: auto!important;">
                            <option value=""> </option>
                            <option value="-1">[<?= txt('Aucun') ?>]</option>
                            <?
                            $classe = $champs_filtres_speciaux_equipements_bio_medicaux[$champ_nom]['classe'];
                            $classe::getOptions();
                            ?>
                        </select>
                        <?php
                    } else {
                        $metas = $formulaire::getFlatChampsSaisieMetaData();
                        $meta = $metas[$champ_nom];
                        if ($meta['type'] == 'select') {
                            ?>
                            <select id="valeur" style="width: 340px; height: auto!important;">
                                <option value=""> </option>
                                <option value="-1">[<?= txt('Aucun') ?>]</option>
                                <?php
                                foreach ($champs_filtres_speciaux as $key => $metaSpecial) {
                                    if ($key == 'presence_donnees' ) {
                                    ?>
                                        <option value="<?=$key?>" id="champ_nom_options_<?=$key?>"><?=$metaSpecial['titre']?></option>
                                    <?
                                    }
                                }
                                $class = $meta['classe'];
                                $class::getOptions();
                                ?>
                            </select>
                            <?php
                        } else {
                            ?>
                            <input type="text" id="valeur" value="" maxlength="200" style="width: 333px; padding-left: 5px;"/>
                            <?php
                        }
                    }
                }
            }
        }
    }
    else if($_GET['action'] == 'getProjetsFromClientMaster') {
        $client_master_db = $_GET['client_master_db'];
        $clientMaster = new ClientsMaster($client_master_db);
        $sql = "select projets.*, ouvrages.nom as ouvrage, concat(ouvrages.nom, ' - ', projets.code) as nom
                from `" . $clientMaster->db . "`.projets
                left join ouvrages on ouvrages.id = ouvrage_id";
        $projets = db::instance()->getAll($sql);
        ?>
        <option value=""></option>
        <?
        foreach ($projets as $i => $projet) {
            ?>
            <option value="<?= $projet->id ?>"><?= $projet->nom ?></option>
            <?php
        }
    } else if($_GET['action'] == 'getChampsFromFormulaireIdForNotifications')
    {
        $formulaire_id = $_GET['formulaire_id'];
        if (str_starts_with($formulaire_id, 'custom_')) {
            list($custom, $formulaire_custom_id) = explode('_', $formulaire_id);
            $formulaireCustom = new FormulairesCustomsParametres($formulaire_custom_id);
        } else {
            $formulaire = Formulaires::getNomFromId($formulaire_id);
            $metas = $formulaire::getFlatChampsSaisieMetaData();
            $formulaires_liste = null;
            $key_param_intrinseque = '';
            $className_intrinseque = '';

            if(isset($metas) && is_array($metas))
            {
                foreach($metas as $key => $meta)
                {
                    if(isset($meta['classe']) && dm_strlen($meta['classe']) > 0)
                    {
                        $className = $meta['classe'];
                        $formulaires_listes = array();
                        if (method_exists($className, 'getFormulairesListes')
                            && is_callable(array($className, 'getFormulairesListes')))
                        {
                            $formulaires_listes = $className::getFormulairesListes($className);
                        }
                        if(count($formulaires_listes) > 0)
                        {
                            $key_param_intrinseque = $key;
                            $formulaires_liste = $formulaires_listes[0];
                            $className_intrinseque = $className;
                            break;
                        }
                    }
                }
            }
        }

        if (isset($formulaireCustom)) {
            foreach ($formulaireCustom->listes_parametres as $key => $parametre)
            {
                if($parametre->format <> 'section')
                {
                    ?>
                    <option value="<?=$key?>" id="champ_nom_options_<?=$key?>"><?=$parametre->getRaw('nom_parametre')?></option>
                    <?php
                }
            }
        } else if(isset($formulaire) && dm_strlen($formulaire) > 0) {
            $metas = $formulaire::getFlatChampsSaisieMetaData();
            foreach($metas as $key => $meta)
            {
                if(isset($meta['titre']) && !isset($meta['source_externe']) && dm_strlen($meta['titre']) > 0)
                {
                    ?>
                    <option value="<?=$meta['champ_id']?>"><?=$meta['titre']?></option>
                    <?php
                }
            }
            if ($formulaire == 'EquipementsBioMedicaux') {
                $champs = Config::instance()->getChampsFiltresSpeciauxEquipementsBioMedicaux(true);
                foreach ($champs as $key => $meta) {
                    ?>
                    <option value="<?=$key?>" id="champ_nom_options_<?=$key?>"><?=$meta['titre']?></option>
                    <?
                }
            }
            if(dm_strlen($key_param_intrinseque) > 0 && isset($formulaires_liste) && isset($formulaires_liste->listes_parametres))
            {
                foreach($formulaires_liste->listes_parametres as $id => $param)
                {
                    ?>
                    <option value="intrinseque__<?=$key_param_intrinseque?>__<?=$param['id']?>" id="champ_nom_options_intrinseque__<?=$key_param_intrinseque?>__<?=$param['id']?>" data-classe="<?=$className_intrinseque?>"><?=$param['nom_parametre'].txt(' [Paramètres intrinsèques]')?></option>
                    <?php
                }
            }
        }
    }
    else if($_GET['action'] == 'getNomenclaturesFromFicheDiscipline')
    {
        $fiche = new Fiches(intval($_GET['fiche_id']));
        if(isset($fiche))
        {
            $fiche_sous_type = new FicheSousType($fiche->fiche_sous_type_id);
            if(isset($fiche_sous_type))
            {
?>
                <option value=""> </option>
<?php
                $filtre = array();
                if(isset($fiche_sous_type->discipline_id) && $fiche_sous_type->discipline_id > 0)
                {
	                $filtre = array('discipline_id' => $fiche_sous_type->discipline_id);
                }
                Nomenclature::getOptions($filtre, intval($_GET['nomenclature_id']), true);
            }
        }
    }
    else if($_GET['action'] == 'getNbPhaseFilInfoForRaccord')
    {
        ob_start();
        CablesCalibresTypes::getOptions(array('nombre_phase_type_id' => intval($_GET['nombre_phase_type_id'])), intval($_GET['calibre_fil_alimentation_id']), true);
        $cables_calibres = ob_get_clean();
        ob_end_clean();

        ob_start();
        CalibresProtectionsTypes::getOptions(array('nombre_phase_type_id' => intval($_GET['nombre_phase_type_id'])), intval($_GET['disjoncteur_calibre_id']), true);
        $calibres_protections = ob_get_clean();
        ob_end_clean();

        $info = array();
        $info['disjoncteur_calibre_id_options'] = $calibres_protections;
        $info['calibre_fil_alimentation_id_options'] = $cables_calibres;

        print json_encode($info);
    }
    else if($_GET['action'] == 'getTensionsFromKva')
    {
        $kva = new KvaTypes(intval($_GET['kva_type_id']));

        $info = array();
        $info['tension_primaire_id'] = (dm_strlen($kva->tension_type_primaire_id) > 0 ? $kva->tension_type_primaire_id : '');
        $info['tension_secondaire_id'] = (dm_strlen($kva->tension_type_secondaire_id) > 0 ? $kva->tension_type_secondaire_id : '');

        print json_encode($info);
    }
    else if($_GET['action'] == 'exportIfc')
    {
        ini_set('max_execution_time', 30000);
        $projet = getProjetInfo();

        if ($isDev) {
            $cmdPython = "python";
            $path_tmp = 'c:\wamp64\www\docmatic\file_converters\\';
            $path_converter = 'c:\wamp64\www\docmatic\file_converters';
            $ifcFileName = 'export';
        } else {
            $cmdPython = "python3";
            $path_tmp = '/var/www/temp/' . uniqueKey($CodeClient . getIdUsager()) . '/';
            $path_converter = '/var/www/file_converters';
            $ifcFileName = conserverSeulementTexte(str_replace(' ', '-', $projet->nom));
        }

        if (!is_dir($path_tmp)) {
            mkdir($path_tmp, 0777, true);
        }

//        printDebug(json_encode($projet->getIfcData(), JSON_PRETTY_PRINT));
        file_put_contents($path_tmp . 'export.json', json_encode($projet->getIfcData(), JSON_PRETTY_PRINT));

        $output = null;
        chdir($path_converter);
        $cmd = "$cmdPython json2ifc.py -i $path_tmp"."export.json -o $path_tmp"."export.ifc 2>&1";
        exec($cmd, $output, $result);

        $clientS3 = DocumentsListesElements::setRegisterStreamWrapper();

        $uploader = new MultipartUploader($clientS3, "$path_tmp"."export.ifc", [
            'Bucket' => $S3_BUCKET,
            'Key' => "bucket1/$CodeClient/downloads/" . $ifcFileName . '.ifc',
        ]);

        $uploader->upload();

        $clientS3->putObjectAcl([
            'Bucket' => $S3_BUCKET,
            'Key' => "bucket1/$CodeClient/downloads/" . $ifcFileName . '.ifc',
            'ACL' => 'public-read'
        ]);

        $link = "https://docmatic.os-qc1.cirrusproject.ca/bucket1/$CodeClient/downloads/$ifcFileName".".ifc";
        $sujet = "Solutions DocMatic - Document prêt pour téléchargement";
        $usager = getUsager();
        $message = "Bonjour ".($usager->sexe == 'm' ? 'M.' : 'Mme')." ".$usager->nom.',
<br /><br />
Votre document .ifc est prêt pour téléchargement à partir du lien ci-dessous:
<br /><br />
<a href="' . $link . '">' . $ifcFileName . '.ifc</a>
<br /><br /><table width="687" border="0" cellspacing="0" cellpadding="0" style="border-collapse:collapse;width:412.5pt;">
<tbody><tr>
<td width="687" style="width:412.5pt;padding:0;">
<div style="margin:0;"><a href="http://www.docmatic.ca" target="_blank"><img src="https://www.docmatic.ca/img/Docmatic_real.png" width="200" /></a></div>
</td>
</tr>
<tr>
<td width="687" style="width:412.5pt;padding:0;">
<div style="margin:0;"><font face="Calibri,sans-serif" size="2"><span style="font-size:11pt;"><font face="Arial,sans-serif" size="2" color="#003DA6"><span style="font-size:10.5pt;" lang="en-CA"><br>

MESSAGERIE AUTOMATISÉE</span></font><font face="Arial,sans-serif" size="2" color="#003DA6"><span style="font-size:10.5pt;" lang="en-CA"><br>

Service de support aux utilisateurs</span></font><font face="Arial,sans-serif" size="2" color="#003DA6"><span style="font-size:10.5pt;" lang="en-CA"><br>

</span></font><a href="mailto:<EMAIL>" target="_blank" rel="noopener noreferrer" data-auth="NotApplicable"><font face="Arial,sans-serif" size="2"><span style="font-size:10.5pt;"><i><font color="#003DA6"><span lang="en-CA"><EMAIL></span></font></i></span></font></a></span></font></div>
</td>
</tr>
<tr>
<td width="687" style="width:412.5pt;padding:13.5pt 0 0 0;">
<div style="margin:0;"><font face="Calibri,sans-serif" size="2"><span style="font-size:11pt;"><font face="Arial,sans-serif" size="2" color="#00A1DF"><span style="font-size:10.5pt;" lang="en-CA">T. 581 986.1783</span></font><font face="Arial,sans-serif" size="2" color="#00A1DF"></font><font face="Arial,sans-serif" size="2" color="#00A1DF"><span style="font-size:10.5pt;" lang="en-CA"><br>

</span></font><a href="https://www.docmatic.ca"><font face="Arial,sans-serif" size="2"><span style="font-size:10.5pt;"><font face="Arial Black,sans-serif" color="#00A1DF"><span lang="en-CA">www.docmatic.ca</span></font></span></font></a></span></font></div>
</td>
</tr>
</tbody></table>';
        envoiCourriel($usager->email, $sujet, $message, array(), array(), '<EMAIL>', 'No Reply');

        unlink("$path_tmp"."export.ifc");

//        header('Content-Description: File Transfer');
//        header('Content-Disposition: attachment; filename=export.ifc');
//        header('Content-Type: application/octet-stream');
//        header('Content-Disposition: attachment; filename=' . basename($path_tmp . 'export.ifc'));
//        header('Content-Transfer-Encoding: binary');
//        header('Expires: 0');
//        header('Cache-Control: must-revalidate');
//        header('Pragma: public');
//        header('Content-Length: ' . filesize($path_tmp . 'export.ifc'));
//        print file_get_contents($path_tmp . 'export.ifc');
    }
    else if($_GET['action'] == 'getKvaInfoForRaccord')
    {
        $kva = new KvaTypes(intval($_GET['kva_type_id']));

        $info = array();
        $info['fusible_capacite_info'] = (dm_strlen($kva->calibre_protection_fusible) > 0 ? txt('Recommandation: ').$kva->calibre_protection_fusible.' A' : '');
        $info['disjoncteur_calibre_id_info'] = (dm_strlen($kva->calibre_protection_type_disjoncteur) > 0 ? txt('Recommandation: ').$kva->calibre_protection_type_disjoncteur : '');

        $info['calibre_fil_alimentation_id_info'] = '';
        if(dm_strlen($kva->calibre_cable_type_cuivre) > 0 || dm_strlen($kva->calibre_cable_type_aluminium) > 0)
        {
            $info['calibre_fil_alimentation_id_info'] .= txt('Recommandation: ');
            if(dm_strlen($kva->calibre_cable_type_cuivre) > 0)
            {
                $info['calibre_fil_alimentation_id_info'] .= $kva->calibre_cable_type_cuivre;
                if(dm_strlen($kva->calibre_cable_type_aluminium) > 0)
                {
                    $info['calibre_fil_alimentation_id_info']  .= ' ou ';
                }
            }
            $info['calibre_fil_alimentation_id_info'] .= (dm_strlen($kva->calibre_cable_type_aluminium) > 0 ? $kva->calibre_cable_type_aluminium : '');
        }

        $info['calibre_fil_malm_id_info'] = '';
        if(dm_strlen($kva->calibre_cable_type_malm_cuivre) > 0 || dm_strlen($kva->calibre_cable_type_malm_aluminium) > 0)
        {
            $info['calibre_fil_malm_id_info'] .= txt('Recommandation: ');
            if(dm_strlen($kva->calibre_cable_type_malm_cuivre) > 0)
            {
                $info['calibre_fil_malm_id_info'] .= $kva->calibre_cable_type_malm_cuivre;
                if(dm_strlen($kva->calibre_cable_type_malm_aluminium) > 0)
                {
                    $info['calibre_fil_malm_id_info']  .= ' ou ';
                }
            }
            $info['calibre_fil_malm_id_info'] .= (dm_strlen($kva->calibre_cable_type_malm_aluminium) > 0 ? $kva->calibre_cable_type_malm_aluminium : '');
        }

        $info['conduit_type_id_info'] = '';
        if(dm_strlen($kva->conduit_type_cuivre) > 0 || dm_strlen($kva->conduit_type_aluminium) > 0)
        {
            $info['conduit_type_id_info'] .= txt('Recommandation: ');
            if(dm_strlen($kva->conduit_type_cuivre) > 0)
            {
                $info['conduit_type_id_info'] .= txt('Si cuivre: ').$kva->conduit_type_cuivre;
                if(dm_strlen($kva->conduit_type_aluminium) > 0)
                {
                    $info['conduit_type_id_info']  .= ' ou ';
                }
            }
            $info['conduit_type_id_info'] .= (dm_strlen($kva->conduit_type_aluminium) > 0 ? txt('si aluminium: ').$kva->conduit_type_aluminium : '');
        }

        $info['calibre_fil_malt_id_info'] = '';
        if(dm_strlen($kva->calibre_cable_type_malt_cuivre) > 0 || dm_strlen($kva->calibre_cable_type_malt_aluminium) > 0)
        {
            $info['calibre_fil_malt_id_info'] .= txt('Recommandation: ');
            if(dm_strlen($kva->calibre_cable_type_malt_cuivre) > 0)
            {
                $info['calibre_fil_malt_id_info'] .= $kva->calibre_cable_type_malt_cuivre;
                if(dm_strlen($kva->calibre_cable_type_malt_aluminium) > 0)
                {
                    $info['calibre_fil_malt_id_info']  .= ' ou ';
                }
            }
            $info['calibre_fil_malt_id_info'] .= (dm_strlen($kva->calibre_cable_type_malt_aluminium) > 0 ? $kva->calibre_cable_type_malt_aluminium : '');
        }

        $info['conduit_malt_type_id_info'] = '';
        if(dm_strlen($kva->conduit_type_malt_cuivre) > 0 || dm_strlen($kva->conduit_type_malt_aluminium) > 0)
        {
            $info['conduit_malt_type_id_info'] .= txt('Recommandation: ');
            if(dm_strlen($kva->conduit_type_malt_cuivre) > 0)
            {
                $info['conduit_malt_type_id_info'] .= txt('Si cuivre: '). $kva->conduit_type_malt_cuivre;
                if(dm_strlen($kva->conduit_type_malt_aluminium) > 0)
                {
                    $info['conduit_malt_type_id_info']  .= ' ou ';
                }
            }
            $info['conduit_malt_type_id_info'] .= (dm_strlen($kva->conduit_type_malt_aluminium) > 0 ? txt('si aluminium: ').$kva->conduit_type_malt_aluminium : '');
        }

        print json_encode($info);
    }
    else if($_GET['action'] == 'getHpInfoForRaccord')
    {
        $hp = new HpTypes(intval($_GET['hp_type_id']));

        $info = array();
        $info['fusible_capacite_info'] = (dm_strlen($hp->calibre_protection_fusible) > 0 ? txt('Recommandation: ').$hp->calibre_protection_fusible.' A' : '');
        $info['disjoncteur_calibre_id_info'] = (dm_strlen($hp->calibre_protection_type_disjoncteur) > 0 ? txt('Recommandation: ').$hp->calibre_protection_type_disjoncteur : '');

        $info['calibre_fil_alimentation_id_info'] = '';
        if(dm_strlen($hp->calibre_cable_type_cuivre) > 0 || dm_strlen($hp->calibre_cable_type_aluminium) > 0)
        {
            $info['calibre_fil_alimentation_id_info'] .= txt('Recommandation: ');
            if(dm_strlen($hp->calibre_cable_type_cuivre) > 0)
            {
                $info['calibre_fil_alimentation_id_info'] .= $hp->calibre_cable_type_cuivre;
                if(dm_strlen($hp->calibre_cable_type_aluminium) > 0)
                {
                    $info['calibre_fil_alimentation_id_info']  .= ' ou ';
                }
            }
            $info['calibre_fil_alimentation_id_info'] .= (dm_strlen($hp->calibre_cable_type_aluminium) > 0 ? $hp->calibre_cable_type_aluminium : '');
        }

        $info['calibre_fil_malm_id_info'] = '';
        if(dm_strlen($hp->calibre_cable_type_malm_cuivre) > 0 || dm_strlen($hp->calibre_cable_type_malm_aluminium) > 0)
        {
            $info['calibre_fil_malm_id_info'] .= txt('Recommandation: ');
            if(dm_strlen($hp->calibre_cable_type_malm_cuivre) > 0)
            {
                $info['calibre_fil_malm_id_info'] .= $hp->calibre_cable_type_malm_cuivre;
                if(dm_strlen($hp->calibre_cable_type_malm_aluminium) > 0)
                {
                    $info['calibre_fil_malm_id_info']  .= ' ou ';
                }
            }
            $info['calibre_fil_malm_id_info'] .= (dm_strlen($hp->calibre_cable_type_malm_aluminium) > 0 ? $hp->calibre_cable_type_malm_aluminium : '');
        }

        $info['conduit_type_id_info'] = '';
        if(dm_strlen($hp->conduit_type_cuivre) > 0 || dm_strlen($hp->conduit_type_aluminium) > 0)
        {
            $info['conduit_type_id_info'] .= txt('Recommandation: ');
            if(dm_strlen($hp->conduit_type_cuivre) > 0)
            {
                $info['conduit_type_id_info'] .= txt('Si cuivre: ').$hp->conduit_type_cuivre;
                if(dm_strlen($hp->conduit_type_aluminium) > 0)
                {
                    $info['conduit_type_id_info']  .= ' ou ';
                }
            }
            $info['conduit_type_id_info'] .= (dm_strlen($hp->conduit_type_aluminium) > 0 ? txt('si aluminium: ').$hp->conduit_type_aluminium : '');
        }

        $info['ajustement_relais_info'] = '';
        if(dm_strlen($hp->ajustement_relais) > 0)
        {
            $info['ajustement_relais_info'] .= txt('Recommandation: ').$hp->ajustement_relais.' A';
        }

        print json_encode($info);
    }
    else if($_GET['action'] == 'getFichesListForCloneForm')
    {
        $suffix_filtres_globaux = ($_GET['filtre_global_to_use'] ?? '');
        $key_filtre_top = 'filtre_top'.$suffix_filtres_globaux;
        $key_filtre_avance = 'filtre_avance'.$suffix_filtres_globaux;
        $fiche_id = intval($_GET['fiche_id']);
        $formulaire = $_GET['formulaire'];
        $projet_id = intval($_GET['projet_id']);

        $fiche = new Fiches($fiche_id);

        $filter = array(
            'term' => $_GET['term'],
            'excluded_fiche_id' => $fiche->id,
            'fiche_type_id' => $fiche->fiche_type_id,
            'fiche_sous_type_id' => $fiche->fiche_sous_type_id,
            'projet_formulaire' => array(
                'projet_id' => $projet_id,
                'formulaire_id' => Formulaires::getIdFromNom($formulaire)
            ),
            'filtre_global_to_use' => $suffix_filtres_globaux,
        );

	    if(isset($_GET['exclureFichesStatutNonEditable']) && intval($_GET['exclureFichesStatutNonEditable']) == 1)
	    {
		    $filter['exclureFichesStatutNonEditable'] = 1;
	    }

	    if(isset($_GET['exclureFichesFromDepotsVerouilles']) && intval($_GET['exclureFichesFromDepotsVerouilles']) == 1)
	    {
		    $filter['exclureFichesFromDepotsVerouilles'] = 1;
	    }

        if(isset($_GET['projet_id_destination']) && intval($_GET['projet_id_destination']) > 0)
        {
            $filter['projet_id_destination'] = intval($_GET['projet_id_destination']);
            $filter['projet_formulaire']['projet_id'] = intval($_GET['projet_id_destination']);
        }
        
        if (isset($_GET['disable_fitre_avance'])) {
            $_SESSION[$key_filtre_top] = array();
            $_SESSION[$key_filtre_avance] = array();
        }

        header('Content-Type: application/json');
        print Fiches::getJsonOptions($filter);
    }
    else if($_GET['action'] == 'getFichesListForCloneFormCustom')
    {
        $suffix_filtres_globaux = ($_GET['filtre_global_to_use'] ?? '');
        $key_filtre_top = 'filtre_top'.$suffix_filtres_globaux;
        $key_filtre_avance = 'filtre_avance'.$suffix_filtres_globaux;
        $fiche_id = intval($_GET['fiche_id']);

        $fiche = new Fiches($fiche_id);

        $filter = array(
            'term' => $_GET['term'],
            'excluded_fiche_id' => $fiche->id,
            'fiche_type_id' => $fiche->fiche_type_id,
            'fiche_sous_type_id' => $fiche->fiche_sous_type_id,
            'filtre_global_to_use' => $suffix_filtres_globaux,
        );

        if(isset($_GET['exclureFichesStatutNonEditable']) && intval($_GET['exclureFichesStatutNonEditable']) == 1)
        {
            $filter['exclureFichesStatutNonEditable'] = 1;
        }

        if(isset($_GET['exclureFichesFromDepotsVerouilles']) && intval($_GET['exclureFichesFromDepotsVerouilles']) == 1)
        {
            $filter['exclureFichesFromDepotsVerouilles'] = 1;
        }

        if (isset($_GET['disable_fitre_avance'])) {
            $_SESSION[$key_filtre_top] = array();
            $_SESSION[$key_filtre_avance] = array();
        }

        header('Content-Type: application/json');
        print Fiches::getJsonOptions($filter);
    }
    else if($_GET['action'] == 'getFichesListForAppliqueGabarit')
    {
        $gabarit_id = intval($_GET['gabarit_id']);

        $suffix_filtres_globaux = ($_GET['filtre_global_to_use'] ?? '');

        $filter = array(
            'term' => $_GET['term'],
            'is_gabarit' => 0,
            'filtre_global_to_use' => $suffix_filtres_globaux,
        );

        if ($gabarit_id > 0) {
            $gabarit = new Fiches($gabarit_id);
            $filter['fiche_type_id'] = $gabarit->fiche_type_id;
            $filter['fiche_sous_type_id'] = $gabarit->fiche_sous_type_id;
            $filter['gabarit_id_to_affecte'] = $gabarit_id;
        }

        header('Content-Type: application/json');
        print Fiches::getJsonOptions($filter);
    }
    else if($_GET['action'] == 'getFichesListForDissocierGabarit')
    {
        $gabarit_id = intval($_GET['gabarit_id']);

        $suffix_filtres_globaux = ($_GET['filtre_global_to_use'] ?? '');

        $filter = array(
            'term' => $_GET['term'],
            'is_gabarit' => 0,
            'filtre_global_to_use' => $suffix_filtres_globaux,
        );

        if ($gabarit_id > 0) {
            $gabarit = new Fiches($gabarit_id);
            $filter['fiche_type_id'] = $gabarit->fiche_type_id;
            $filter['fiche_sous_type_id'] = $gabarit->fiche_sous_type_id;
            $filter['gabarit_id_to_dissocier'] = $gabarit_id;
        }

        header('Content-Type: application/json');
        print Fiches::getJsonOptions($filter);
    }
    else if($_GET['action'] == 'getFichesListForDestruction')
    {
	    $suffix_filtres_globaux = (isset($_GET['filtre_global_to_use']) ? $_GET['filtre_global_to_use'] : '');
	    $key_filtre_top = 'filtre_top'.$suffix_filtres_globaux;
	    $key_filtre_avance = 'filtre_avance'.$suffix_filtres_globaux;

	    if(isset($_GET['disable_fitre_avance']))
	    {
		    $_SESSION[$key_filtre_top] = array();
		    $_SESSION[$key_filtre_avance] = array();
	    }

	    $filter = array(
		    'term' => $_GET['term'],
		    'just_deleted' => 1,
            'filtre_global_to_use' => $suffix_filtres_globaux
	    );

        header('Content-Type: application/json');
        print Fiches::getJsonOptions($filter);
    }
    else if($_GET['action'] == 'personnifier')
    {
	    if (isMaster()) {
		    $usager = new Usager(intval($_GET['usager_id']));
		    $user_browser = getIp(true);
		    $sql = "select password from usagers where id = ?";
		    $password = $db->getOne($sql, [intval($_GET['usager_id'])]);
		    $_SESSION['login_string'] = hash('sha512', $password . $user_browser);
		    $_SESSION['usager_'.$CodeClient] = $usager;
		    header('Location: index.php?section=landing&module=index');
		    die();
        }
    }
    else if($_GET['action'] == 'forceFiltre')
    {
	    $filtre_global_to_use = isset($_GET['filtre_global_to_use']) ? $_GET['filtre_global_to_use'] : '';
        $_SESSION['filtre_top'.$filtre_global_to_use] = array();
        $_SESSION['filtre_avance'.$filtre_global_to_use] = array();
        if(isset($_GET['champ_valeur']))
        {
            $champs_noms = dm_explode('-', $_GET['champ_nom']);
            $champs_valeurs = dm_explode('_', $_GET['champ_valeur']);

            if(isset($champs_noms[0]) && dm_strlen($champs_noms[0]) > 0 && isset($champs_valeurs[0]) && dm_strlen($champs_valeurs[0]) > 0)
            {
                $_SESSION['filtre_avance'.$filtre_global_to_use][intval($_GET['formulaire_id'])][$champs_noms[0]] = $champs_valeurs[0];
            }

            if(isset($champs_noms[1]) && dm_strlen($champs_noms[1]) > 0 && isset($champs_valeurs[1]) && dm_strlen($champs_valeurs[1]) > 0)
            {
                $_SESSION['filtre_avance'.$filtre_global_to_use][intval($_GET['formulaire_id'])][$champs_noms[1]] = $champs_valeurs[1];
            }
        }
        if(isset($_GET['fiches_ids']))
        {
            $_SESSION['filtre_avance'.$filtre_global_to_use]['ids'] = $_GET['fiches_ids'];
        }
        header('Location: index.php?section=fiche&module=index');
	    die();
    }
    else if($_GET['action'] == 'get_hierarchie_tooltip')
    {
        print BatimentsNiveaux::getHierarchieToolTip(intval($_GET['fiche_id']));
    }
    else if($_GET['action'] == 'get_resume_inventaire')
	{
        if (!empty($_GET['numero_inventaire']) && !empty($_GET['inventaire_id'])) {
		    print InventairesLignes::getResumeToolTip($_GET['numero_inventaire'], intval($_GET['inventaire_id']));
        }
	}
    else if($_GET['action'] == 'desactiverInutilises')
    {
        $sql = "SELECT DISTINCT TABLE_NAME, COLUMN_NAME 
                FROM information_schema.KEY_COLUMN_USAGE 
                WHERE REFERENCED_TABLE_NAME = ? AND REFERENCED_COLUMN_NAME = 'id' AND TABLE_SCHEMA = ? AND TABLE_NAME <> ?";
        $fkDatas = db::instance()->getAll($sql, [$_GET['table'], Config::instance()->codeClient, $_GET['table'] . '_projets']);

        $nom_table_source = $_GET['table'];
        foreach ($fkDatas as $fkData) {
            $sql = "delete from `" . db::tableColonneValide($nom_table_source) . "_projets`
                    where liste_item_id in (
                        select distinct id
                        from `" . db::tableColonneValide($nom_table_source) . "` tbs
                        where not exists (
                            select 1
                            from `" . db::tableColonneValide($fkData['TABLE_NAME']) . "`
                            where `" . db::tableColonneValide($fkData['COLUMN_NAME']) . "` = tbs.id
                                and projet_id = ?
                        )
                    )
                        and projet_id = ?";
            db::instance()->query($sql, [getProjetId(), getProjetId()]);
        }
        die('OK');
    }
    else if($_GET['action'] == 'download_fichier_ifc')
    {
        $id = (int)$_GET['id'];
        $fichierIfc = new Visualisateur3dFichiers($id);

        ini_set("memory_limit","16000M");
        ini_set('max_execution_time', 500);

        header($_SERVER['SERVER_PROTOCOL'].' 200 OK');
        header("Content-Type: text/plain");
        header("Content-Transfer-Encoding: Binary");
        header("Content-Length: ".filesize('/var/www/temp/' . Config::instance()->codeClient . '_ifcs/' . $id . '.ifc'));
        header("Content-Disposition: attachment; filename=\"" . $fichierIfc->nom_fichier . "\"");
        print readfile_chunked('/var/www/temp/' . Config::instance()->codeClient . '_ifcs/' . $id . '.ifc');
        die();
    }
    else if($_GET['action'] == 'download_fichier_xkt')
    {
        $id = (int)$_GET['id'];
        $fichierIfc = new Visualisateur3dFichiers($id);

        ini_set("memory_limit","16000M");
        ini_set('max_execution_time', 500);

        header($_SERVER['SERVER_PROTOCOL'].' 200 OK');
        header("Content-Type: text/plain");
        header("Content-Transfer-Encoding: Binary");
        header("Content-Length: ".filesize('/var/www/temp/' . Config::instance()->codeClient . '_ifcs/' . $id . '.ifc.xkt'));
        header("Content-Disposition: attachment; filename=\"" . $fichierIfc->nom_fichier . ".xkt\"");
        print readfile_chunked('/var/www/temp/' . Config::instance()->codeClient . '_ifcs/' . $id . '.ifc.xkt');
        die();
    }
    else if($_GET['action'] == 'get_resume_local')
    {
        print InventairesLocaux::getResumeToolTip($_GET['numero_local'], intval($_GET['inventaire_id']));
    }
    else if($_GET['action'] == 'inventaire_set_actif_interne')
	{
	    $sql = "update inventaires_lignes set is_actif = ? where numero_inventaire = ? and inventaire_id = ?";
		$db->query($sql, [intval($_GET['is_actif']), $_GET['numero_inventaire'], intval($_GET['inventaire_id'])]);
	}
    else if($_GET['action'] == 'rotateThumbnail')
	{
        $document_id = intval($_GET['document_id']);
        $document = new DocumentsListesElements($document_id);
		$rotation = $document->rotation == 270 ? 0 : ($document->rotation + 90);

		$sql = "update documents_listes_elements set rotation = ? where id = ?";
		$db->query($sql, [$rotation, $document_id]);

		print $rotation;
		exit;
	}
    else if($_GET['action'] == 'download_document')
    {
        $document = new DocumentsListesElements(intval($_GET['id']));
        if (isset($_GET['thumbnail'])) {
	        $document->download($_GET['thumbnail']);
        } else {
	        $document->download();
        }
    }
    else if($_GET['action'] == 'display_document')
    {
        $document = new DocumentsListesElements(intval($_GET['id']));
        $document->display();
    }
    else if($_GET['action'] == 'download_documents')
    {
	    ini_set("memory_limit","16000M");
	    ini_set('max_execution_time', 500);

	    header($_SERVER['SERVER_PROTOCOL'].' 200 OK');
	    header("Content-Type: application/zip");
	    header("Content-Transfer-Encoding: Binary");
	    header("Content-Length: ".filesize('/var/www/temp/'.$CodeClient.'_documents.zip'));
	    header("Content-Disposition: attachment; filename=\"" . $CodeClient.'_documents.zip' . "\"");
	    print readfile_chunked('/var/www/temp/'.$CodeClient.'_documents.zip');
        die();
    }
    else if ($_GET['action'] == 'download_excel_erreurs')
    {
        $id = (int)$_GET['id'];
        list($data_titre, $data_lignes) = FinancementsProjets::getErreurImport($id);

	    $dataArray = [ json_decode($data_titre->getRaw('data_titre'), true) ];
        foreach ($data_lignes as $data_ligne) {
            $dataArray[] = json_decode($data_ligne->getRaw('data_ligne'), true);
        }

	    $nom_fichier = 'Erreurs Import Financement (' . substr($data_titre->getRaw('ts_insert'), 0, 16) . ')';
	    exportExcelFromDataArray($dataArray, 'Erreurs', $nom_fichier);
	    die();
    }
    else if ($_GET['action'] == 'delete_excel_erreurs')
	{
		$id = (int)$_GET['id'];
		$db->query("delete from financements_imports_erreurs where id = ?", [$id]);
		die();
	}
	else if($_GET['action'] == 'delete_document')
	{
		$document = new DocumentsListesElements(intval($_GET['id']));
		$document->delete();

		if (!isset($_GET['ajax'])) {
			header('Location: index.php?section=admin&module=edit&table=' . $document->nom_table . '&id=' . $document->liste_item_id . '&type=client&document_edition=1');
		}
		die();
	}
	else if($_GET['action'] == 'getJsonList')
	{
		$listToGet = $_POST['listToGet'];
		if (in_array($listToGet, ['InventairesLignes', 'Fiches', 'InventairesLignesEquipements', 'EquipementsBioMedicauxTypes', 'ContratsProjets', 'InventairesLocaux'])) {
			header('Content-Type: application/json');
			if ($listToGet == 'InventairesLignesEquipements') {
				$_POST['filtre']['key_to_use'] = 'code';
				print InventairesLignes::getJsonOptions($_POST['filtre']);
            } else if ($listToGet == 'InventairesLocaux') {
                $_POST['filtre']['key_to_use'] = 'numero_local';
                print InventairesLocaux::getJsonOptions($_POST['filtre']);
            } else {
				print $listToGet::getJsonOptions($_POST['filtre'], (in_array($listToGet, ['InventairesLignes', 'EquipementsBioMedicauxTypes'])));
			}
        }
		die();
	}
    else if ($_GET['action'] == 'getEquipementsFichesInventaires') {
        $lignesTmp = InventairesLignes::getListePropositions(['inventaire_ligne_id' => (int)$_GET['inventaire_ligne_id'], 'key_to_use' => 'ebm_id']);
        $formulaires_listes = EquipementsBioMedicauxTypes::getFormulairesListes('EquipementsBioMedicauxTypes');
        $lignesDatatable = [];
        foreach ($lignesTmp as $ligne) {
            $association = (isset($ligne->est_associe) && $ligne->est_associe ? 'associe_contexte' : 'dissocie');
            $button = '';
            if ($ligne->is_actif == 1 && havePermission('gestion_associations_inventaires')) {
                ob_start();
                ?>
                    <button class="association"
                        data-association="<?= (isset($ligne->est_associe) && $ligne->est_associe && $association == 'associe_contexte' ? 'dissocier' : 'associer') ?>"
                        data-inventaire_id="<?= $ligne->inventaire_id ?>"
                        data-numero_inventaire="<?= $ligne->numero_inventaire ?>"
                        data-fiche_id="<?= $ligne->fiche_id ?>"
                        data-equipement_bio_medicaux_type_id="<?= $ligne->equipement_bio_medicaux_type_id ?>"
                        data-inventaire_ligne_id="<?= $ligne->inventaire_ligne_id ?>"
                    >
                        <?= txt(isset($ligne->est_associe) && $ligne->est_associe && $association == 'associe_contexte' ? 'Dissocier' : 'Associer') ?>
                    </button>
                <?
                $button = ob_get_clean();
            }
            $class = (isset($ligne->est_associe) && $ligne->est_associe ? 'associe_contexte' : 'dissocie');
            $class .= ($ligne->est_associe && $ligne->statut_externe == 'Inactif' ? ' associe_inactif_externe' : '');
            $class .= (!$ligne->est_associe && $ligne->statut_externe == 'Inactif' ? ' non_associe_inactif_externe' : '');
            $lignesDatatable[] = [
                '<div class="' . $class . '"><div class="inventaire_ligne_tooltip" data-numero_inventaire="' . $ligne->numero_inventaire . '" data-inventaire_id="' . $ligne->inventaire_id . '">
                    ' . $ligne->numero_inventaire . '
                </div></div>',
                '<div class="' . $class . '"><div class="tooltip_struct" data-fiche_id="' . $ligne->fiche_local_destination_id . '">
                    ' . $ligne->local . '
                </div></div>',
                '<div class="' . $class . '"><a href="index.php?section=admin&module=popup_listes_param&fiche_id=' . $ligne->fiche_id . '&formulaire_liste_id=' . $formulaires_listes[0]->id . '&liste_item_id=' . $ligne->equipement_bio_medicaux_type_id . '" class="fancy add">
                     ' . $ligne->equipement . '
                 </a></div>',
                '<div class="' . $class . '">' . $ligne->qte_existante . '</div>',
                '<div class="' . $class . '">' . $ligne->qte_conservee . '</div>',
                '<div class="' . $class . '">' . $ligne->qte_associee . '</div>',
                '<div class="' . $class . '">' . ($ligne->is_actif ? txt('Visible') : txt( 'Non-visible')) . '</div>',
                '<div class="' . $class . '">' . $button . '</div>'
            ];
        }
        print json_encode($lignesDatatable);
    }
	else if($_GET['action'] == 'delete_document_all')
    {
        $nom_table = $_GET['nom_table'];
        $liste_item_id = (int)$_GET['liste_item_id'];
        $is_image_principale = (int)$_GET['is_image_principale'];

		$documents = DocumentsListesElements::getListe(array('nom_table' => $nom_table, 'liste_item_id' => $liste_item_id, 'is_image_principale' => $is_image_principale));
		if(count($documents) > 0)
		{
		    foreach ($documents as $document)
            {
			    $document->delete();
			}
		}

		setMessage(txt('Suppression effectuée avec succès.'));
		if($liste_item_id == 0)
        {
			header('Location: index.php?section=admin&module=import_documents_lot&nom_table='.$nom_table);
        }
        else
        {
            header('Location: index.php?section=admin&module=edit&table='.$nom_table.'&id='.$liste_item_id.'&type=client&document_edition=1');
		}
	    die();
    }
    else if($_GET['action'] == 'upload_document')
    {
        if (isset($_FILES['pdf']) && is_uploaded_file($_FILES['pdf']['tmp_name'])) {
            $violations = DocumentsListesElements::validateFile($_FILES['pdf']);
            if (count($violations) > 0) {
                foreach ($violations as $violation) {
                    setErreur($violation->getMessage());
                }
            }
        }

        if (isset($_FILES['image'])) {
            $violations = DocumentsListesElements::validateFile($_FILES['image']);
            if (count($violations) > 0) {
                foreach ($violations as $violation) {
                    setErreur($violation->getMessage());
                }
            } else {
                DocumentsListesElements::add($_GET['nom_table'], intval($_GET['liste_item_id']), $_FILES['image'], 1);
            }
        }

        if (isset($_FILES['file'])) {
            $violations = DocumentsListesElements::validateFile($_FILES['file']);
            if (count($violations) > 0) {
                foreach ($violations as $violation) {
                    setErreur($violation->getMessage());
                }
            } else {
                DocumentsListesElements::add($_GET['nom_table'], intval($_GET['liste_item_id']), $_FILES['file']);
            }
        }

        header('Location: index.php?section=admin&module=edit&table='.$_GET['nom_table'].'&id='.intval($_GET['liste_item_id']).'&type=client&document_edition=1');
	    die();
    }
    else if($_GET['action'] == 'update_document_is_public')
    {
	    $document_id = (int)$_GET['document_id'];
	    $is_public = (int)$_GET['is_public'];

	    $db->autocommit(false);

	    $sql = "update documents_listes_elements set is_public = ? where id = ?";
	    $db->query($sql, array($is_public, $document_id));

	    $db->commit();
    }
    else if($_GET['action'] == 'update_document_is_public_fiche')
    {
	    $document_id = (int)$_GET['document_id'];
	    $fiche_id = (int)$_GET['fiche_id'];
	    $is_public_fiche = (int)$_GET['is_public_fiche'];

	    $db->autocommit(false);

	    $sql = "delete from documents_listes_elements_fiches where document_liste_element_id = ? and fiche_id = ?";
	    $db->query($sql, array($document_id, $fiche_id));
	    if($is_public_fiche <> -1)
        {
            $sql = "insert into documents_listes_elements_fiches (document_liste_element_id, fiche_id, is_public_fiche) values (?, ?, ?)";
            $db->query($sql, array($document_id, $fiche_id, $is_public_fiche));
        }

	    $db->commit();
    }
    else if($_GET['action'] == 'get_formulaire_liste_params')
    {
        //print FormulairesListesParametres::getValuesToolTip(intval($_GET['formulaire_liste_id']), intval($_GET['liste_item_id']));
    }
    else if($_GET['action'] == 'set_code_fiche_search')
    {
        $_SESSION['filtre_top']['code_fiche_search_autocomplete'] = $_GET['code_fiche_search'];
    }
    else if($_GET['action'] == 'get_qrts')
    {
	    $filtre = array();
	    if(isset($_GET['fiche_id']) && intval($_GET['fiche_id']) > 0)
        {
	        $filtre['fiche_id'] = intval($_GET['fiche_id']);
        }
        else if(isset($_GET['nom_table']) && dm_strlen($_GET['nom_table']) > 0)
        {
	        $filtre['nom_table'] = $_GET['nom_table'];
        }
        else if(isset($_GET['liste_item_id']) && intval($_GET['liste_item_id']) > 0)
        {
	        $filtre['liste_item_id'] = intval($_GET['liste_item_id']);
        }
        else if(isset($_GET['classe_formulaire']) && dm_strlen($_GET['classe_formulaire']) > 0)
        {
	        $filtre['classe_formulaire'] = $_GET['classe_formulaire'];
        }
        else if(isset($_GET['discipline_id']) && intval($_GET['discipline_id']) > 0)
        {
	        $filtre['discipline_id'] = intval($_GET['discipline_id']);
        }
        $qrts = Questions::getListe($filtre);
	    print json_encode($qrts);
    } else if($_GET['action'] == 'getInventairesLignes') {
        $lignes = InventairesLignes::getListeSimple(['is_editable' => 1, 'inventaire_id' => Inventaires::getInventaireCourant()->id, 'for_json' => 1, 'id_to_use' => 'id', 'text_to_use' => 'numero_inventaire']);
        header('Content-Type: application/json');
        print json_encode(array_values($lignes));
    }
    else if($_GET['action'] == 'getFichesList')
    {
	    $suffix_filtres_globaux = ($_GET['filtre_global_to_use'] ?? '');
	    $key_filtre_top = 'filtre_top'.$suffix_filtres_globaux;
	    $key_filtre_avance = 'filtre_avance'.$suffix_filtres_globaux;

        $excluded_fiche_id = isset($_GET['excluded_fiche_id']) ? intval($_GET['excluded_fiche_id']) : 0;
        $inclure_supprimees = isset($_GET['inclure_supprimees']) && havePermission('effacement') ? intval($_GET['inclure_supprimees']) : 0;
        if(isset($_GET[$key_filtre_avance]))
        {
            if(dm_strlen($_GET['champ_valeur']) == 0)
            {
                unset($_SESSION[$key_filtre_avance][intval($_GET['formulaire_id'])][($_GET['niveau_id'] ?? $_GET['champ_nom'])]);
            }
            else
            {
                $_SESSION[$key_filtre_avance][intval($_GET['formulaire_id'])][($_GET['niveau_id'] ?? $_GET['champ_nom'])] = $_GET['champ_valeur'];
            }
        }

        if(isset($_GET['type_fiche']) && intval($_GET['fiche_sous_type_id']) == 0)
        {
			$_GET['fiche_sous_type_id'] = $_GET['type_fiche'] == 'local' ? array(2000000, 2000001, 2000003, 2000006, 2000007, 2000008, 2000009, 2000011, 2000012, 2000016) : array(2000002);
        }

        $ids = null;
        if(isset($_GET['procedure_entretien_id']) && intval($_GET['procedure_entretien_id']) > 0)
        {
            $procedure = new ProcedureEntretien(intval($_GET['procedure_entretien_id']));
            $fiches = $procedure->getListeFiches();
            $ids = array_keys($fiches);
        }
        if(isset($_GET['procedure_inspection_id']) && intval($_GET['procedure_inspection_id']) > 0)
        {
            $procedure = new ProcedureInspection(intval($_GET['procedure_inspection_id']));
            $fiches = $procedure->getListeFiches();
            $ids = array_keys($fiches);
        }

        if(isset($_GET['disable_fitre_avance']))
        {
            $_SESSION[$key_filtre_top] = array();
            $_SESSION[$key_filtre_avance] = array();
        }

        $filtre = array();
        $filtre['ids'] = (isset($ids) ? $ids : null);
        $filtre['excluded_fiche_id'] = $excluded_fiche_id;
        $filtre['no_filtre_global'] = (isset($_GET['no_filtre_global']) ? isset($_GET['no_filtre_global']) : 0);
        $filtre['inclure_supprimees'] = $inclure_supprimees;
	    $filtre['filtre_global_to_use'] = $suffix_filtres_globaux;

        if(isset($_GET['exclureFichesStatutNonEditable']) && intval($_GET['exclureFichesStatutNonEditable']) == 1)
        {
	        $filtre['exclureFichesStatutNonEditable'] = 1;
        }

	    if(isset($_GET['exclureFichesFromDepotsVerouilles']) && intval($_GET['exclureFichesFromDepotsVerouilles']) == 1)
	    {
		    $filtre['exclureFichesFromDepotsVerouilles'] = 1;
	    }

        if(isset($_GET['identifierFichesFromDepotsVerouillesPourEquipement']) && (int)$_GET['liste_item_id'] > 1) {
            $filtre['equipementAVerifierVerouille'] = (int)$_GET['liste_item_id'];
        }

        if(isset($_GET['term']))
        {
            //$_SESSION[$key_filtre_top]['code_fiche_search'] = $_GET['term'];
            $filtre['term'] = $_GET['term'];
        }
        if(isset($_GET['batiment_id']))
        {
            $_SESSION[$key_filtre_top]['batiment_id'] = intval($_GET['batiment_id']);
            $filtre['batiment_id'] = $_GET['batiment_id'];
        }
        if(isset($_GET['fiche_type_id']))
        {
            $_SESSION[$key_filtre_top]['fiche_type_id'] = intval($_GET['fiche_type_id']);
            $filtre['fiche_type_id'] = $_GET['fiche_type_id'];
        }
        if(isset($_GET['fiche_sous_type_id']))
        {
            if(is_array($_GET['fiche_sous_type_id']))
            {
                $filtre['fiche_sous_type_id'] = $_GET['fiche_sous_type_id'];
            }
            else
            {
                $_SESSION[$key_filtre_top]['fiche_sous_type_id'] = intval($_GET['fiche_sous_type_id']);
                $filtre['fiche_sous_type_id'] = (is_array($_GET['fiche_sous_type_id']) ? $_GET['fiche_sous_type_id'] : intval($_GET['fiche_sous_type_id']));
            }
        }
        if(isset($_GET['numero_lot']))
        {
            $_SESSION[$key_filtre_top]['numero_lot'] = $_GET['numero_lot'];
            $filtre['numero_lot'] = $_GET['numero_lot'];
        }
        if(isset($_GET['code_alternatif']))
        {
            $_SESSION[$key_filtre_top]['code_alternatif'] = $_GET['code_alternatif'];
            $filtre['code_alternatif'] = $_GET['code_alternatif'];
        }
        if(isset($_GET['id_revit']))
        {
            $_SESSION[$key_filtre_top]['id_revit'] = $_GET['id_revit'];
            $filtre['id_revit'] = $_GET['id_revit'];
        }
        if(isset($_GET['id_codebook']))
        {
            $_SESSION[$key_filtre_top]['id_codebook'] = $_GET['id_codebook'];
            $filtre['id_codebook'] = $_GET['id_codebook'];
        }

        if(isset($_GET['niveau']) && is_array($_GET['niveau'])) {
	        $filtre_niveau = array();
	        $niveauPopule = false;
	        foreach ($_GET['niveau'] as $key => $val) {
                $filtre_niveau[intval($key)] = $val;
                $niveauPopule = true;
	        }
            if ($niveauPopule) {
	            $_SESSION[$key_filtre_top]['niveau'] = $filtre_niveau;
            }
        }

        if(isset($_GET['force_ordre']))
        {
	        $filtre['force_ordre'] = $_GET['force_ordre'];
        }

	    if(isset($_GET['extract_is_orphelin']))
	    {
		    $filtre['extract_is_orphelin'] = intval($_GET['extract_is_orphelin']);
	    }

	    if(isset($_POST['fiches_to_preserve']) && dm_strlen($_POST['fiches_to_preserve']) > 0 && $_POST['fiches_to_preserve'] <> '[]')
        {
	        $filtre['fiches_to_preserve'] = json_decode($_POST['fiches_to_preserve']);
        }

	    if(isset($_GET['echeancier_id']))
	    {
		    $filtre['echeancier_id'] = $_GET['echeancier_id'];
	    }

	    if(isset($_GET['echeancier_ids']) && count($_GET['echeancier_ids']) > 0)
	    {
		    $filtre['echeancier_ids'] = $_GET['echeancier_ids'];
	    }

        $selected = isset($_GET['selected']) ? intval($_GET['selected']) : 0;
        header('Content-Type: application/json');
        print Fiches::getJsonOptions($filtre);
    }
    else if($_GET['action'] == 'clear_filtre')
    {
        $filtre_global_to_use = isset($_GET['filtre_global_to_use']) ? $_GET['filtre_global_to_use'] : '';
        $_SESSION['filtre_avance'.$filtre_global_to_use] = array();
        $_SESSION['filtre_top'.$filtre_global_to_use] = array();
    }
    else if($_GET['action'] == 'associationInventaire')
    {
        if ($_GET['association'] == 'dissocier') {
            $sql = "delete from inventaires_fiches_equipements where inventaire_id = ? and equipement_bio_medicaux_type_id = ? and fiche_id = ? and numero_inventaire = ?";
        } else {
            $sql = "insert into inventaires_fiches_equipements(inventaire_id, equipement_bio_medicaux_type_id, fiche_id, numero_inventaire) values (?, ?, ?, ?)";
        }
        $db->query($sql, [(int)$_GET['inventaire_id'], (int)$_GET['equipement_bio_medicaux_type_id'], (int)$_GET['fiche_id'], $_GET['numero_inventaire']]);
    }
    else if($_GET['action'] == 'clear_filtre_InvLocaux')
    {
        $filtre_global_to_use = isset($_GET['filtre_global_to_use']) ? $_GET['filtre_global_to_use'] : '';
        $_SESSION['filtre_invLocaux'.$filtre_global_to_use] = array();
    }
    else if($_GET['action'] == 'clear_filtre_inv')
    {
        $filtre_global_to_use = isset($_GET['filtre_global_to_use']) ? $_GET['filtre_global_to_use'] : '';
        $_SESSION['filtre_inv'.$filtre_global_to_use] = array();
    }
    else if($_GET['action'] == 'clear_filtre_equipement')
    {
	    $filtre_global_to_use = isset($_GET['filtre_global_to_use']) ? $_GET['filtre_global_to_use'] : '';
	    $_SESSION['filtre_avance_equipement'.$filtre_global_to_use] = array();
    }
    else if($_GET['action'] == 'getSuperficiesList')
    {
	    $niveaux = BatimentsNiveaux::getListeReel();
	    $nb = count($niveaux);
	    include_once('superficies/generer'.$nb.'.php');
    }
    else if($_GET['action'] == 'validationEquipementsDepots')
    {
        $projetFinancement = new FinancementsProjets((int)$_POST['financement_projet_id']);
        $id = (int)$_POST['id'];
        $sql = "select ebm.id, f.code as fiche, concat(ebmt.nom, coalesce(concat(' - ', ebmt.description), '')) as equipement, fp.numero as projet, fpd.nom as depot
                from equipements_bio_medicaux ebm
                join equipements_bio_medicaux_types ebmt on ebm.equipement_bio_medicaux_type_id = ebmt.id
                join fiches f on ebm.fiche_id = f.id
                join financements_projets_depots fpd on ebm.depot_source" . (int)$_POST['source_financement_id'] . "_id = fpd.id
                join financements_projets fp on fpd.financement_projet_id = fp.id
                where depot_source" . (int)$_POST['source_financement_id'] . "_id is not null 
                    and depot_source" . (int)$_POST['source_financement_id'] . "_id <> ? 
                    and ebm.projet_id = ? 
                    and equipement_bio_medicaux_type_id in (" . db::placeHolders($_POST['listeEquipements']) . ") 
                    and fiche_id in (" . db::placeHolders($projetFinancement->fiches) . ")";
        $liste = db::instance()->getAll($sql, array_merge([$id, getProjetId()], $_POST['listeEquipements'], array_keys($projetFinancement->fiches)));
        print json_encode([
            'statut' => 'OK',
            'resultat' => $liste
        ]);
    }
    else if($_GET['action'] == 'getGestionEquipementList')
    {
	    $niveaux = BatimentsNiveaux::getListeReel();
	    $nb = count($niveaux);
	    include_once('gestion_equipements/generer'.$nb.'.php');
    }
    else if($_GET['action'] == 'getSuiviBudgetaireList')
    {
	    include_once('suivi_budgetaire/generer.php');
    }
    else if($_GET['action'] == 'getSuiviBudgetaireListAssociation')
    {
        include_once('inventaires/generer.php');
    }
    else if($_GET['action'] == 'updateNbInventaires')
	{

	    $sql = "select count(*) as nb
                from inventaires_lignes il
                join inventaires_fiches_equipements ife on ife.numero_inventaire = il.numero_inventaire
                where il.inventaire_id = ? and fiche_id = ? and ife.equipement_bio_medicaux_type_id = ?";
		$nb = $db->getOne($sql, [(int)$_GET['inventaire_id'], (int)$_GET['fiche_id'], (int)$_GET['equipement_bio_medicaux_type_id']]);

		print (int)$nb;
	}
    else if($_GET['action'] == 'transposition')
    {
        $equipement_bio_medicaux_type_id = (int)$_GET['equipement_bio_medicaux_type_id'];
        $transposition_source = $_GET['transposition_source'];
        $transposition_cible = $_GET['transposition_cible'];
        $seulement_si_debalancement = (int)$_GET['seulement_si_debalancement'];

        $calculs = [
            'qte_remplacement' => [],
            'qte_nouveau' => [],
            'qte_developpement' => [],
        ];

        $suiviBudgetaire = SuivisBudgetaires::getForProjet();
        foreach ($suiviBudgetaire->getPortefeuilles(true) as $portefeuille) {
            if ($portefeuille['is_section_a'] == 1) {
                $calculs['qte_remplacement'][] = "coalesce(qte_source" . $portefeuille['portefeuille_numero'] . ", 0)";
            }
            if ($portefeuille['is_section_b'] == 1) {
                $calculs['qte_nouveau'][] = "coalesce(qte_source" . $portefeuille['portefeuille_numero'] . ", 0)";
            }
            if ($portefeuille['is_section_c'] == 1) {
                $calculs['qte_developpement'][] = "coalesce(qte_source" . $portefeuille['portefeuille_numero'] . ", 0)";
            }
        }

        $where = "";
        if ($seulement_si_debalancement == 1) {
            $calcul = implode(' + ', $calculs[$transposition_source]);
            if (!empty($calcul)) {
                $where .= " and coalesce({$transposition_source}, 0) <> ($calcul)";
            } else {
                $where .= " and false";
            }
            $where .= " and coalesce({$transposition_source}, 0) >= 0";
        }

        $equipements_tmp = [];
        $extra_cols = ', case when depot1.ts_verrouille is not null then 1 else 0 end as isVerrouilleDepot1, case when depot2.ts_verrouille is not null then 1 else 0 end as isVerrouilleDepot2, case when depot3.ts_verrouille is not null then 1 else 0 end as isVerrouilleDepot3, case when depot4.ts_verrouille is not null then 1 else 0 end as isVerrouilleDepot4, case when depot5.ts_verrouille is not null then 1 else 0 end as isVerrouilleDepot5';
        $extra_cols .= ', case when depot1.ts_photo_j0 is not null then depot1.id else 0 end as isPhotoJ0Depot1, case when depot2.ts_photo_j0 is not null then depot2.id else 0 end as isPhotoJ0Depot2, case when depot3.ts_photo_j0 is not null then depot3.id else 0 end as isPhotoJ0Depot3, case when depot4.ts_photo_j0 is not null then depot4.id else 0 end as isPhotoJ0Depot4, case when depot5.ts_photo_j0 is not null then depot5.id else 0 end as isPhotoJ0Depot5';
        $extra_cols .= ', depot1.id as depot_id1, depot2.id as depot_id2, depot3.id as depot_id3, depot4.id as depot_id4, depot5.id as depot_id5';
        $extra_joins = [];
        for ($iterDepot = 1; $iterDepot <= 5; $iterDepot++) {
            $extra_joins[] = 'left join financements_projets_depots as depot' . $iterDepot . ' on depot' . $iterDepot . '.id = tb.depot_source' . $iterDepot . '_id';
        }
        $sql = "select tb.* $extra_cols
                from equipements_bio_medicaux tb
                " . dm_implode("\n", $extra_joins) . "
                where equipement_bio_medicaux_type_id = ? and projet_id = ?";
        $equipements_tmp = db::instance()->getAll($sql, [$equipement_bio_medicaux_type_id, getProjetId()]);

        $equipement_bio_med_ids = [0];
        foreach ($equipements_tmp as $row) {
            list($canInactivate, $equipement_non_editable, $canDelete) = EquipementsBioMedicaux::getDroitEditionEquipementStatic($row, (int)$row['equipement_bio_medicaux_type_id']);
            if (!$equipement_non_editable) {
                $equipement_bio_med_ids[] = $row['id'];
            }
        }

        $sql = "update equipements_bio_medicaux 
                set `" . db::tableColonneValide($transposition_cible) . "` = `" . db::tableColonneValide($transposition_source) . "` 
                where id in (" . db::placeHolders($equipement_bio_med_ids) . ")
                    $where";
        db::instance()->query($sql, $equipement_bio_med_ids);

        print 'OK';
    }
    else if($_GET['action'] == 'getSuiviBudgetaireListGeneral')
	{
		include_once('suivi_budgetaire/generer_generale.php');
	}
    else if($_GET['action'] == 'saveQuantiteEquipementBioMedicauxGenerale')
	{
		$profil_budgetaire_id = intval($_GET['profil_budgetaire_id']);
		$batiment_niveau_element_id = intval($_GET['batiment_niveau_element_id']);
		$equipement_bio_medicaux_type_id = intval($_GET['equipement_bio_medicaux_type_id']);
		$champ = $_GET['champ'];
		$quantite = intval($_GET['quantite']);

		$db->autocommit(false);

		$sql = "select * from suivis_budgetaires_gestions_generales where profil_budgetaire_id = ? and equipement_bio_medicaux_type_id = ? and batiment_niveau_element_id = ?";
		$suiviB = $db->getRow($sql, [$profil_budgetaire_id, $equipement_bio_medicaux_type_id, $batiment_niveau_element_id]);

		if (isset($suiviB['equipement_bio_medicaux_type_id'])) {
			$sql = "update suivis_budgetaires_gestions_generales set `" . db::tableColonneValide($champ) . "`= ? where profil_budgetaire_id = ? and equipement_bio_medicaux_type_id = ? and batiment_niveau_element_id = ?";
			$db->query($sql, [$quantite, $profil_budgetaire_id, $equipement_bio_medicaux_type_id, $batiment_niveau_element_id]);
		} else {
		    $sql = "select nom from batiments_niveaux_elements where id = ?";
		    $nom = $db->getOne($sql, [$batiment_niveau_element_id]);

			$sql = "insert into suivis_budgetaires_gestions_generales (`" . db::tableColonneValide($champ) . "`, profil_budgetaire_id, equipement_bio_medicaux_type_id, batiment_niveau_element_id, batiment_niveau_nom)
                    values (?, ?, ?, ?, ?)";
			$db->query($sql, [$quantite, $profil_budgetaire_id, $equipement_bio_medicaux_type_id, $batiment_niveau_element_id, $nom]);
        }

		$db->commit();
	}
    else if($_GET['action'] == 'saveQuantiteEquipementBioMedicaux')
	{
        $formulaire_equipement_id = intval($_GET['formulaire_equipement_id']);
		$fiche_id = intval($_GET['fiche_id']);
		$equipement_bio_medicaux_type_id = intval($_GET['equipement_bio_medicaux_type_id']);
		$champ = $_GET['champ'];
		$outil = $_GET['outil'];
		$valeur = intval($_GET['valeur']);

		$db->autocommit(false);
		if (!in_array($champ, ['qte_requise', 'qte_existante', 'qte_fantome', 'qte_conservee'])) {
		    if ($formulaire_equipement_id > 0) {
			    $sql = "update equipements_bio_medicaux set `" . db::tableColonneValide($champ) . "`= ?, equipement_bio_medicaux_type_id = equipement_bio_medicaux_type_id where id = ?";
			    $db->query($sql, [$valeur, $formulaire_equipement_id]);
            } else {
		        $sql = "select max(ordre) ordre from equipements_bio_medicaux where projet_id = ? and equipement_bio_medicaux_type_id = ? and fiche_id = ?";
		        $ordre = $db->getOne($sql, [getProjetId(), $equipement_bio_medicaux_type_id, $fiche_id]);

		        $sql = "insert into equipements_bio_medicaux (projet_id, equipement_bio_medicaux_type_id, fiche_id, `" . db::tableColonneValide($champ) . "`, ordre) values (?, ?, ?, ?, ?)";
		        $db->query($sql, [getProjetId(), $equipement_bio_medicaux_type_id, $fiche_id, $valeur, ($ordre+1)]);
		    }

        } else {
			$liste = EquipementsBioMedicaux::getListe(array('fiche_id' => $fiche_id));

			$ajouter = true;
			$data_to_save = array();
			$iter = 0;
			if (isset($liste) && count($liste) > 0) {
				$sql = "SELECT `COLUMN_NAME`, `DATA_TYPE`
                    FROM `INFORMATION_SCHEMA`.`COLUMNS` 
                    WHERE `TABLE_SCHEMA`='$db_name' 
                        AND `TABLE_NAME`='" . EquipementsBioMedicaux::SQL_TABLE_NAME . "'
                        AND `COLUMN_NAME` not in ('id', 'fiche_id', 'no', 'ordre', 'statut', 'token_archive', 'ts_ajout', 'usager_ajout_id')
                        order by DATA_TYPE;";
				$cols = $db->getAll($sql, array());

				foreach ($liste as $i => $obj) {
					foreach ($cols as $j => $col) {
						$col_name = $col['COLUMN_NAME'];
						$data_to_save[$col['COLUMN_NAME']][$iter] = isset($obj->$col_name) ? $obj->$col_name : '';
					}
					$data_to_save['id_ligne'][$iter] = $obj->id;
					if ($obj->id == $formulaire_equipement_id || ($formulaire_equipement_id == 0 && $obj->fiche_id == $fiche_id && $obj->equipement_bio_medicaux_type_id == $equipement_bio_medicaux_type_id)) {
						$data_to_save[$champ][$iter] = $valeur;
						$ajouter = false;
					}
					$iter++;
				}
			}
			if ($ajouter) {
				$data_to_save['equipement_bio_medicaux_type_id'][$iter] = $equipement_bio_medicaux_type_id;
				$data_to_save[$champ][$iter] = $valeur;
			}

			$fiche_formulaire_commentaire = EquipementsBioMedicaux::getCommentaireFormulaire($fiche_id);
			$fiche_formulaire_commentaire = isset($fiche_formulaire_commentaire) ? $fiche_formulaire_commentaire : '';

			$data_to_save['fiche_formulaire_commentaire'] = $fiche_formulaire_commentaire;

			$data_to_save['fiche_id'] = $fiche_id;
			$data_to_save['formulaire'] = 'EquipementsBioMedicaux';
			if ($outil == 'suivi_budgetaire') {
				$data_to_save['raison'] = txt('Modification de quantités via l\'outil de gestion des sources de financement');
			} else if ($outil == 'gestion_equipements') {
				$data_to_save['raison'] = txt('Modification de quantités via l\'outil de gestion des équipements');
			}
			EquipementsBioMedicaux::save($data_to_save, true);
		}
		$db->commit();
	}
    else if ($_GET['action'] == 'getDataFicheGabarit') {
        $dataFiches = EquipementsBioMedicaux::getListe([ 'fiche_id' => (int)$_GET['fiche_id'] ]);

        print json_encode([
            'dataFiche' => EquipementsBioMedicaux::getListe([ 'fiche_id' => (int)$_GET['fiche_id'] ]),
            'dataGabarit' => EquipementsBioMedicaux::getListe([ 'fiche_id' => (int)$_GET['gabarit_id'] ]),
        ]);
    }
    else if($_GET['action'] == 'sync_data')
    {
        $id = intval($_GET['id']);
        $table = $_GET['table'];
        $mode = $_GET['mode'];

        $db->autocommit(false);

        if($mode == 'upload')
        {
            $bd_from = $db_name;
            $bd_to = $_SESSION['to_compare'];
        }
        else
        {
            $bd_from = $_SESSION['to_compare'];
            $bd_to = $db_name;
        }

        $cols = $validColumns['doc'][$table];

        $sql = "insert into `" . db::tableColonneValide($bd_to) . "`.`" . db::tableColonneValide($table) . "` (`".dm_implode("`, `", array_keys($cols))."`)
                select `".dm_implode("`, `", array_keys($cols))."`
                from `" . db::tableColonneValide($bd_from) . "`.`" . db::tableColonneValide($table) . "`
                where `id` = ?";
        $db->query($sql, array($id));

        $db->commit();

        print 'OK';
    }
    else if ($_GET['action'] == 'setIsMiseEnService') {
        $id = intval($_GET['id']);
        $is_mise_en_service = intval($_GET['is_mise_en_service']);

        $sql = "update equipements_bio_medicaux_types set is_mise_en_service = ? where id = ?";
        db::instance()->query($sql, [$is_mise_en_service, $id]);

        print 'OK';
    } else if($_GET['action'] == 'setActifForProjet')
    {
        $id = intval($_GET['id']);
        $table = $_GET['table'];
        $is_actif = intval($_GET['is_actif']);
        $projet_id = getProjetId();

        $id_col = $table == 'formulaires' ? 'formulaire_id' : 'liste_item_id';

        if($id > 0 && $projet_id > 0) {
	        $db->autocommit(false);

	        $referencers = [];
	        $codes_utilises = [];
	        if ($is_actif == 0 && !in_array($table, array('formulaires', 'fonctions_batiments'))) {
		        $codes_utilises = Fiches::getCodesFichesReferenced($table, $id, $projet_id);
		        $referencers = getListeOfReferencer($table, $id);
	        }

	        if (count($codes_utilises) == 0 && count($referencers) == 0) {
		        $sql = "delete from `" . db::tableColonneValide($table) . "_projets` where $id_col = ? and projet_id = ?";
		        $db->query($sql, array($id, $projet_id));

		        if ($is_actif == 1) {
			        $sql = "insert into `" . db::tableColonneValide($table) . "_projets` ($id_col, projet_id) values (?, ?)";
			        $db->query($sql, array($id, $projet_id));
		        }
	        }

	        $db->commit();

	        if ($table == 'pseudos') {
		        init_pseudos(true);
	        }
        }

        if (count($referencers) > 0) {
	        print txt('Il est impossible de désactiver cette donnée parce que les éléments de librairie suivants y font référence:', false)."\n\n".dm_implode("\n", $referencers);
        } else if(count($codes_utilises) > 0) {
            print txt('Il est impossible de désactiver cette donnée (dans le cadre de ce projet) parce que les fiches suivantes la contienne:', false)."\n\n".dm_implode("\n", $codes_utilises);
        } else {
            print 'OK';
        }
    }
    else if($_GET['action'] == 'update_cedule')
    {
        if (isset($_POST['c'])) {
            db::instance()->autocommit(false);
            $cedule_demenagement_id = (int)$_POST['cedule_demenagement_id'];
            $inventaire_ligne_ids = $_POST['c'];
            foreach ($inventaire_ligne_ids as $inventaire_ligne_id) {
                $sql = "insert into cedules_demenagements_inventaires_lignes (cedule_demenagement_id, inventaire_ligne_id) values (?, ?)";
                db::instance()->query($sql, [$cedule_demenagement_id, $inventaire_ligne_id]);
            }
            db::instance()->commit();
        }
        print 'ok';
    }
    else if($_GET['action'] == 'setActifAllForProjet')
	{
		$table = $_GET['table'];
		$is_actif = intval($_GET['is_actif']);
		$projet_id = getProjetId();

		$id_col = $table == 'formulaires' ? 'formulaire_id' : 'liste_item_id';

		if($projet_id > 0)
		{
			$db->autocommit(false);

			if ($is_actif == 0) {
				$sql = "delete from `" . db::tableColonneValide($table) . "_projets` where projet_id = ?";
				$db->query($sql, array($projet_id));
			}

            if ($is_actif == 1) {
                $sql = "insert into `" . db::tableColonneValide($table) . "_projets` ($id_col, projet_id) 
                        select id, $projet_id 
                        from `" . db::tableColonneValide($table) . "` tb
                        where not exists (select projet_id from `" . db::tableColonneValide($table) . "_projets` where projet_id = $projet_id and $id_col = tb.id)";
                $db->query($sql, array());
            }

			$db->commit();

			if($table == 'pseudos')
			{
				init_pseudos(true);
			}
		}

		print 'OK';
	}
    else if($_GET['action'] == 'setCoordinationForProjet')
    {
        $id = intval($_GET['id']);
        $table = $_GET['table'];
        $is_coordination = intval($_GET['is_coordination']);
        $projet_id = getProjetId();
        $type_coordination = $_GET['type_coordination'];

//        if(!isset($_SESSION['rows_constraints']) && !isset($_SESSION['rows_constraints'][$table]))
//        {
//            $sql = "SELECT TABLE_NAME,COLUMN_NAME,CONSTRAINT_NAME, REFERENCED_TABLE_NAME,REFERENCED_COLUMN_NAME
//                    FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE
//                    WHERE REFERENCED_TABLE_SCHEMA = '".$db_name."' AND REFERENCED_TABLE_NAME = '$table' AND REFERENCED_COLUMN_NAME = 'id' and TABLE_NAME <> '$table"."_projets'";
//            $_SESSION['rows_constraints'][$table] = $db->getAll($sql);
//        }
//        $rows_constraints = $_SESSION['rows_constraints'][$table];
//
//        $codes_utilises = array();
//        foreach($rows_constraints as $i => $row_constraint)
//        {
//            if(!in_array($row_constraint['TABLE_NAME'], ['inventaires_fiches_equipements', 'financements_projets_groupes_equipements', 'acquisitions_projets_groupes_equipements', 'suivis_budgetaires_gestions_generales']))
//            {
//	            $sql = "select fiches.code
//                        from `".$row_constraint['TABLE_NAME']."`
//                        join fiches on fiches.id = fiche_id
//                        where `".$row_constraint['COLUMN_NAME']."` = ? and projet_id = ?";
//	            $codes_utilises_tmp = $db->getCol($sql, array($id, $projet_id));
//	            $codes_utilises = array_merge($codes_utilises, $codes_utilises_tmp);
//            }
//        }
//
//        if(count($codes_utilises) > 0)
//        {
//            print txt('Il est impossible de faire cette action car cette donnée est déjà utilisée dans le cadre '.(count($codes_utilises) == 1 ? 'de la fiche suivante' : 'des fiches suivantes').':', false)."\n\n".dm_implode("\n", $codes_utilises);
//            die();
//        }

        $id_col = 'liste_item_id';
        if($id > 0 && $projet_id > 0) {
	        $db->autocommit(false);

	        $sql = "select count(*) as nb from `" . db::tableColonneValide($table) . "_projets` where $id_col = ? and projet_id = ?";
	        $nb = $db->getOne($sql, array($id, $projet_id));

	        if ($nb == 1) {
		        $sql = "update `" . db::tableColonneValide($table) . "_projets` set is_coordination_" . $type_coordination . " = ? where $id_col = ? and projet_id = ?";
		        $db->query($sql, array($is_coordination, $id, $projet_id));
	        } else {
		        $sql = "insert into `" . db::tableColonneValide($table) . "_projets` ($id_col, projet_id, is_coordination_" . $type_coordination . ") values (?, ?, ?)";
		        $db->query($sql, array($id, $projet_id, $is_coordination));
	        }

	        $db->commit();
        }

        print 'OK';
    }
    else if($_GET['action'] == 'setBilanDistanceForProjet')
    {
        $id = intval($_GET['id']);
        $table = $_GET['table'];
        $is_bilan_distance = intval($_GET['is_bilan_distance']);
        $projet_id = getProjetId();

        $id_col = 'liste_item_id';

        if($id > 0 && $projet_id > 0)
        {
            $db->autocommit(false);

            $sql = "select count(*) as nb from `" . db::tableColonneValide($table) . "_projets` where $id_col = ? and projet_id = ?";
            $nb = $db->getOne($sql, array($id, $projet_id));

            if($nb == 1)
            {
                $sql = "update `" . db::tableColonneValide($table) . "_projets` set is_bilan_distance = ? where $id_col = ? and projet_id = ?";
                $db->query($sql, array($is_bilan_distance, $id, $projet_id));
            }
            else
            {
                $sql = "insert into `" . db::tableColonneValide($table) . "_projets` ($id_col, projet_id, is_bilan_distance) values (?, ?, ?)";
                $db->query($sql, array($id, $projet_id, $is_bilan_distance));
            }

            $db->commit();
        }

        print 'OK';
    }
    else if($_GET['action'] == 'setDefaut')
    {
        $id = intval($_GET['id']);
        $table = $_GET['table'];
        $is_defaut = intval($_GET['is_defaut']);

        if($id > 0)
        {
            $db->autocommit(false);

            $sql = "update `" . db::tableColonneValide($table) . "` set is_defaut = ? where id = ?";
            $db->query($sql, array($is_defaut, $id));

            $db->commit();
        }

        print 'OK';
    }
    else if($_GET['action'] == 'getProcedureFromDiscipline')
    {
?>
        <option value=""> </option>
<?
        ProcedureEntretien::getOptions(array('discipline_id' => intval($_GET['discipline_id'])), 0, true);
    }
    else if($_GET['action'] == 'getProcedureInspectionFromDiscipline')
    {
?>
        <option value=""> </option>
<?
        ProcedureInspection::getOptions(array('discipline_id' => intval($_GET['discipline_id'])), 0, true);
    }
    else if($_GET['action'] == 'getUsagersAttribuesFromDiscipline')
    {
?>
        <option value=""> </option>
<?
        Usager::getOptions(array('permission_code' => 'entretien_completion', 'discipline_id' => intval($_GET['discipline_id'])), 0);
    }
    else if($_GET['action'] == 'getUsagersAttribuesInspectionsFromDiscipline')
    {
?>
        <option value=""> </option>
<?
        Usager::getOptions(array('permission_code' => 'inspection_completion', 'discipline_id' => intval($_GET['discipline_id'])), 0);
    }
    else if($_GET['action'] == 'getUsagersAttribuesSurveillancesFromDiscipline')
    {
?>
        <option value=""> </option>
<?
        Usager::getOptions(array('permission_code' => 'surveillance_completion', 'discipline_id' => intval($_GET['discipline_id'])), 0);
    }
    else if($_GET['action'] == 'getBatimentFromProjet')
    {
        Batiment::getOptions(array('projet_id' => intval($_GET['projet_id'])), 0, true);
    }
    else if($_GET['action'] == 'getPanneauxFromBatiment')
    {
        Panneau::getOptions(array('batiment_id' => intval($_GET['batiment_id'])), 0, true);
    }
    else if($_GET['action'] == 'getSousTypesForType')
    {
        print FicheSousType::getOptions(array('fiche_type_id' => intval($_GET['fiche_type_id'])), 0, true);
    }
    else if($_GET['action'] == 'getKvaTypeForPhasage')
    {
        print KvaTypes::getOptions(array('phasage_type_id' => intval($_GET['phasage_type_id'])), 0, true);
    }
    else if($_GET['action'] == 'getFichesLocauxParents')
    {
        $this_fiche_id = intval($_GET['this_fiche_id']);
        $fiche_type_id = intval($_GET['fiche_type_id']);
        $fiche_sous_type_id = intval($_GET['fiche_sous_type_id']);

        if($fiche_sous_type_id == 2000013)
        {
?>
            <?=Fiches::getJsonOptions(array('fiche_sous_type_id' => 2000002, 'fiche_local_id_null' => 1, 'excluded_fiche_id' => $this_fiche_id, 'no_filtre_global' => 1), true) ?>
<?
        }
        else
        {
            $sous_locaux = Fiches::getListe(array('fiche_local_id' => $this_fiche_id, 'fiche_sous_type_id' => 2000002, 'no_filtre_global' => 1));
            if(count($sous_locaux) == 0)
            {
?>
                <?=Fiches::getJsonOptions(array('fiche_sous_type_id' => 2000002, 'fiche_local_id_null' => 1, 'excluded_fiche_id' => $this_fiche_id, 'no_filtre_global' => 1), true) ?>
<?
            }
        }
    }
    else if($_GET['action'] == 'getFichesLocauxDestinations')
    {
        $this_fiche_id = intval($_GET['this_fiche_id']);
        $fiche_type_id = intval($_GET['fiche_type_id']);
        $fiche_sous_type_id = intval($_GET['fiche_sous_type_id']);
?>
        <?=Fiches::getJsonOptions(array('fiche_sous_type_id' => 2000002, 'fiche_local_id_null' => 1, 'excluded_fiche_id' => $this_fiche_id, 'no_filtre_global' => 1), 0, true)?>
<?
    }
    else if($_GET['action'] == 'getFichesParents')
    {
        $this_fiche_id = intval($_GET['this_fiche_id']);
        $fiche_type_id = intval($_GET['fiche_type_id']);
        $fiche_sous_type_id = intval($_GET['fiche_sous_type_id']);

        if(!in_array($fiche_type_id, array(2000002)))
        {
?>
            <?=Fiches::getJsonOptions(array('fiche_type_id_exclude' => 2000002, 'fiche_parent_id_null' => 1, 'no_filtre_global' => 1), true)?>
<?
        }
        else if(in_array($fiche_sous_type_id, array(2000014)))
        {
?>
            <?=Fiches::getJsonOptions(array('fiche_sous_type_id' => 2000013, 'no_filtre_global' => 1), true)?>
<?
        }
    }
    else if($_GET['action'] == 'getFormulaireGeneral')
    {
        if(isset($_GET['fiche_type_id']) && intval($_GET['fiche_type_id']) > 0)
        {
            $fiche_type = new FicheType(intval($_GET['fiche_type_id']));
            if(dm_strlen($fiche_type->formulaire_general) > 0)
            {
                $className = $fiche_type->formulaire_general;
                $formulaire_general = $className::getOne(0);
                $formulaire_general->getEditorHtml();
            }
        }
        else if(isset($_GET['fiche_sous_type_id']) && intval($_GET['fiche_sous_type_id']) > 0)
        {
            $fiche_sous_type = new FicheSousType(intval($_GET['fiche_sous_type_id']));
            if(dm_strlen($fiche_sous_type->formulaire_general) > 0)
            {
                $className = $fiche_sous_type->formulaire_general;
                $formulaire_general = $className::getOne(0);
                $formulaire_general->getEditorHtml();
            }
        }
    }
    else if($_GET['action'] == 'getChainagesOptions')
    {
        if(intval($_GET['route_id']) > 0)
        {
            $route = new Routes(intval($_GET['route_id']));
            $route->getOptionsChainage(0, 0, (isset($_GET['exlude_empty']) ? false : true));
        }
    }
    else if($_GET['action'] == 'getChainagesOptionsSingle')
    {
        if(intval($_GET['route_id']) > 0)
        {
            $route = new Routes(intval($_GET['route_id']));
            $route->getOptionsChainageSingle(0, (isset($_GET['exlude_empty']) ? false : true));
        }
    }
    else if($_GET['action'] == 'addDisciplineToFiche')
    {
        $db->autocommit(false);

        $sql = "delete from fiches_disciplines where fiche_id = ? and discipline_id = ?";
        $db->query($sql, array(intval($_GET['id']), intval($_GET['discipline_id'])));

        $sql = "insert into fiches_disciplines (fiche_id, discipline_id) values (?, ?)";
        $db->query($sql, array(intval($_GET['id']), intval($_GET['discipline_id'])));

        $db->commit();

        print 'OK';
    }
    else if($_GET['action'] == 'getEditableFormulaire')
    {
        $classeName = $_GET['formulaire'];
        $classeName::getFormHTML(intval($_GET['fiche_id']));
    }
    else if($_GET['action'] == 'getChampPonctuel')
    {
        $champ_ponctuel = new ChampsPonctuel(intval($_GET['champ_ponctuel_id']));
        $champ_ponctuel->getWizardHTML();
    }
    else if($_GET['action'] == 'getFormulairesOptionsForFicheSousType')
    {
?>
        <option value=""> </option>
        <option value="ChampsTextesExports"><?=txt('Matrice de champs combinés')?></option>
<?
        Formulaires::getOptionsByNom(array('fiche_sous_type_id' => intval($_GET['fiche_sous_type_id']), 'ids' => Formulaires::getFormulairesIdWithQuantite(true)));
    }
    else if($_GET['action'] == 'getChampsExternesOptionsForFicheSousType')
    {
?>
        <option value=""> </option>
<?
        CorrelationsChampsExternes::getOptions(array('fiche_sous_type_id' => intval($_GET['fiche_sous_type_id']), 'formulaire_id' => (isset($_GET['formulaire_id']) ? intval($_GET['formulaire_id']) : 0), 'ids' => Formulaires::getFormulairesIdWithQuantite(true)));
    }
    else if($_GET['action'] == 'getMarques')
    {
        $selected_id = isset($_GET['selected_id']) ? (int)$_GET['selected_id'] : 0;
	    Marque::getOptions([], $selected_id, true);
    }
    else if($_GET['action'] == 'getModelesFromMarque')
    {
	    $selected_id = isset($_GET['selected_id']) ? (int)$_GET['selected_id'] : 0;
        Modele::getOptions(array('marque_id' => intval($_GET['marque_id'])), $selected_id, true);
    }
    else if($_GET['action'] == 'getAutomatesFromBatiment')
    {
        Automate::getOptions(array('batiment_id' => intval($_GET['batiment_id'])), 0, true);
    }
    else if($_GET['action'] == 'getPanneauxFromBatiment')
    {
        Panneau::getOptions(array('batiment_id' => intval($_GET['batiment_id'])), 0, true);
    }
    else if($_GET['action'] == 'getTitreForMatriceImportationListe')
    {
        $formulaire = new Formulaires(intval($_GET['formulaire_id']));
        $className = $formulaire->nom;
        $className::getImportHTMLLigneTitre();
    }
    else if($_GET['action'] == 'getListeItemsFromTableName')
    {
        $nom_table = $_GET['nom_table'];
        $className = $tables['client'][$nom_table]['className'];
        if (isset($_GET['getJson'])) {
            $filter = ['with_description' => 1];
            if (isset($_GET['filtre_global_to_use'])) {
                if (isset($_GET['disable_fitre_avance']) && (int)$_GET['disable_fitre_avance'] == 1) {
                    unset($_SESSION['filtre_avance_equipement' . $_GET['filtre_global_to_use']]);
                } else {
                    $filter['filtre_global_to_use'] = $_GET['filtre_global_to_use'];
                }
            }
            echo $className::getJsonOptions($filter);
        } else {
            ?>
            <option value=""> </option>
            <?
            $className::getOptions(array());
        }
    }
    else if($_GET['action'] == 'getChampsForMatriceImportationListe')
    {
        if(isset($_GET['formulaire_id']) && intval($_GET['formulaire_id']) > 0)
        {
            $formulaire = new Formulaires(intval($_GET['formulaire_id']));
            $className = $formulaire->nom;
            $className::getImportHTMLLigne(1, '0_0_0_0_0_0_0_0_0_0_0_0_0_0_0_0_0_0_0_0_0_0_0_0_0_0_0_0_0_0_0', '', 0);
        }
    }
    else if($_GET['action'] == 'getInfoEquipement')
    {
	    $equipement_bio_medicaux_type_id = intval($_GET['equipement_bio_medicaux_type_id']);
	    $equipement = new EquipementsBioMedicauxTypes($equipement_bio_medicaux_type_id);
	    print json_encode([
	        'code' => $equipement->nom,
		    'description' => $equipement->description,
		    'classificationBudgetaire' => $equipement->classification_budgetaire_type,
		    'coutUnitaire' => $equipement->prix_unitaire_nouveau,
            'taxable' => $equipement->taxable
        ]);
    }
    else if($_GET['action'] == 'getEmplacementFromPanneau')
    {
        $sql = "SELECT *
                FROM `localisations_panneaux`
                WHERE id in (select localisation_panneau_id from panneaux where id = ?)";
        $row = $db->getRow($sql, array(intval($_GET['source_alimentation'])));
        print isset($row['nom']) ? $row['nom'] : '';
    }
    else if($_GET['action'] == 'delete')
    {
	    $db->autocommit(false);

        $id = intval($_GET['id']);
        $table = isset($_GET['table']) ? $_GET['table'] : '';

        $typeTable = $_GET['type'];

        $className = $tables[$typeTable][$table]['className'];

        if(!isset($validColumns['doc'][$table]))
        {
            print "table : $table invalide";
            exit;
        }

        if($table == 'champs_textes_denormalises')
		{
			$champ_texte_denormalise = new ChampsTextesDenormalises($id);
			$formulaire = $champ_texte_denormalise->formulaire;
			$table = $formulaire::SQL_TABLE_NAME;

			$sql = "select distinct fiches.code
                    FROM `" . db::tableColonneValide($table) . "`
                    join fiches on fiches.id = fiche_id
                    WHERE `" . db::tableColonneValide($champ_texte_denormalise->nom_champ) . "` is not null and `" . db::tableColonneValide($champ_texte_denormalise->nom_champ) . "` <> '' and `" . db::tableColonneValide($table) . "`.projet_id = ?";
			$codes_utilises = $db->getCol($sql, array(getProjetId()));

			if(count($codes_utilises) > 0)
			{
				print txt('Il est impossible de supprimer ce champ dénormalisé parce que les fiches suivantes contiennent des valeurs qui lui sont associées;'."\n\n".dm_implode("\n", $codes_utilises), false);
				die;
			}
		}
        else if (in_array($table, ['classifications_budgetaires_types', 'equipements_bio_medicaux_types_categories', 'equipements_bio_medicaux_types_sous_categories', 'equipements_bio_medicaux_types_categories_responsabilite', 'equipements_bio_medicaux_types_responsables_logistiques', 'equipements_bio_medicaux_types_directions_responsables', 'phases_incidences'])) {
            $metas = EquipementsBioMedicauxTypes::getFlatChampsSaisieMetaData();
	        $champ_ids = [];
            foreach ($metas as $key => $data) {
                if ($data['table'] == $table) {
	                $champ_ids[] = $data['champ_id'];
                }
            }
            if (count($champ_ids) > 0) {
	            $equipements_utilises = [];
                foreach ($champ_ids as $champ_id) {
	                $sql = "select nom from equipements_bio_medicaux_types where `" . db::tableColonneValide($champ_id) . "` = ?";
	                $equipements_utilises_tmp = $db->getCol($sql, [$id]);
	                $equipements_utilises = array_merge($equipements_utilises, $equipements_utilises_tmp);
                }

                if (count($equipements_utilises) > 0) {
	                if (isMaster()) {
		                print txt('Les équipements suivants contiennent cette donnée;' . "\n\n" . 'Est-ce que vous désirez le supprimer quand même?' . "\n\n" . dm_implode("\n", $equipements_utilises), false);
	                } else {
		                print txt('Il est impossible de supprimer cette donnée parce que les équipements suivantes la contienne;' . "\n\n" . dm_implode("\n", $equipements_utilises), false);
	                }
	                die;
                }

	            $sql = "delete from " . $table . '_projets where liste_item_id = ?';
	            $db->query($sql, array($id));
            }
        } else if (in_array($table, ['acquisitions_responsables_dossiers_a_types', 'acquisitions_responsables_dossiers_b_types', 'acquisitions_responsables_dossiers_c_types', 'acquisitions_responsables_dossiers_d_types', 'acquisitions_responsables_dossiers_e_types', 'acquisitions_responsables_dossiers_f_types', 'acquisitions_responsables_dossiers_g_types', 'acquisitions_responsables_dossiers_h_types', 'acquisitions_responsables_dossiers_i_types', 'acquisitions_responsables_dossiers_j_types', 'acquisitions_menus_supplementaires_a_types', 'acquisitions_responsables_dossiers_types', 'acquisitions_sous_volets_responsables_types' ,'acquisitions_projets_priorisations_types' ,'acquisitions_phases_incidences_contraignantes_types', 'acquisitions_projets_modes_sollicitations_types', 'bloquants_types', 'locals_transitoires_types', 'priorites_acquisitions_types', 'sites_livraisons_types', 'bloquants_financements_types', 'financements_jalons_modeles', 'priorites_financements_types'])) {
            $metaSuppression = [
                'bloquants_types' => [[
                    'table' => "acquisitions_projets",
	                'colonne' => "bloquant_type_id",
                ]],
		        'locals_transitoires_types' => [[
			        'table' => "acquisitions_projets_groupes_equipements",
			        'colonne' => "local_transitoire_id",
		        ]],
		        'priorites_acquisitions_types' => [[
			        'table' => "acquisitions_projets",
			        'colonne' => "priorite_acquisition_id",
		        ]],
		        'sites_livraisons_types' => [
			        [
				        'table' => "acquisitions_projets",
				        'colonne' => "site_livraison_id",
			        ],
			        [
				        'table' => "acquisitions_projets_groupes_equipements",
				        'colonne' => "site_livraison_id",
			        ],
		        ],
		        'bloquants_financements_types' => [[
			        'table' => "financements_projets",
			        'colonne' => "bloquant_type_id",
		        ]],
		        'priorites_financements_types' => [[
			        'table' => "financements_projets",
			        'colonne' => "priorite_financement_id",
		        ]],
	            'acquisitions_sous_volets_responsables_types' => [[
		            'table' => "acquisitions_projets",
		            'colonne' => "sous_volet_responsable_id",
	            ]],
	            'acquisitions_phases_incidences_contraignantes_types' => [[
		            'table' => "acquisitions_projets",
		            'colonne' => "phase_incidence_contraignante_id",
	            ]],
	            'acquisitions_projets_priorisations_types' => [[
		            'table' => "acquisitions_projets",
		            'colonne' => "priorisation_id",
	            ]],
	            'acquisitions_projets_modes_sollicitations_types' => [[
		            'table' => "acquisitions_projets",
		            'colonne' => "mode_sollicitation_id",
	            ]],
	            'acquisitions_responsables_dossiers_types' => [
                    [
	                    'table' => "acquisitions_projets",
	                    'colonne' => "responsable_dossier_1_id",
                    ],
		            [
			            'table' => "acquisitions_projets",
			            'colonne' => "responsable_dossier_2_id",
		            ],
		            [
			            'table' => "acquisitions_projets",
			            'colonne' => "responsable_dossier_3_id",
		            ],
		            [
			            'table' => "acquisitions_projets",
			            'colonne' => "responsable_dossier_4_id",
		            ],
		            [
			            'table' => "acquisitions_projets",
			            'colonne' => "responsable_dossier_5_id",
		            ],
		            [
			            'table' => "acquisitions_projets",
			            'colonne' => "responsable_dossier_6_id",
		            ],
		            [
			            'table' => "acquisitions_projets",
			            'colonne' => "responsable_dossier_7_id",
		            ],
		            [
			            'table' => "acquisitions_projets",
			            'colonne' => "responsable_dossier_8_id",
		            ],
		            [
			            'table' => "acquisitions_projets",
			            'colonne' => "responsable_dossier_9_id",
		            ],
		            [
			            'table' => "acquisitions_projets",
			            'colonne' => "responsable_dossier_10_id",
		            ],
	            ],
	            'acquisitions_responsables_dossiers_a_types' => [[
                    'table' => "acquisitions_projets",
                    'colonne' => "responsable_dossier_a_id",
                ]],
	            'acquisitions_responsables_dossiers_b_types' => [[
		            'table' => "acquisitions_projets",
		            'colonne' => "responsable_dossier_b_id",
	            ]],
	            'acquisitions_responsables_dossiers_c_types' => [[
		            'table' => "acquisitions_projets",
		            'colonne' => "responsable_dossier_c_id",
	            ]],
	            'acquisitions_responsables_dossiers_d_types' => [[
		            'table' => "acquisitions_projets",
		            'colonne' => "responsable_dossier_d_id",
	            ]],
	            'acquisitions_responsables_dossiers_e_types' => [[
		            'table' => "acquisitions_projets",
		            'colonne' => "responsable_dossier_e_id",
	            ]],
	            'acquisitions_responsables_dossiers_f_types' => [[
		            'table' => "acquisitions_projets",
		            'colonne' => "responsable_dossier_f_id",
	            ]],
	            'acquisitions_responsables_dossiers_g_types' => [[
		            'table' => "acquisitions_projets",
		            'colonne' => "responsable_dossier_g_id",
	            ]],
	            'acquisitions_responsables_dossiers_h_types' => [[
		            'table' => "acquisitions_projets",
		            'colonne' => "responsable_dossier_h_id",
	            ]],
	            'acquisitions_responsables_dossiers_i_types' => [[
		            'table' => "acquisitions_projets",
		            'colonne' => "responsable_dossier_i_id",
	            ]],
	            'acquisitions_responsables_dossiers_j_types' => [[
		            'table' => "acquisitions_projets",
		            'colonne' => "responsable_dossier_j_id",
	            ]],
	            'acquisitions_menus_supplementaires_a_types' => [[
		            'table' => "acquisitions_projets",
		            'colonne' => "menu_supplementaire_a_id",
	            ]],
	        ];
            $tableGlobal = '';
	        $numeros = [];
	        foreach ($metaSuppression[$table] as $meta) {
		        $sql = "select numero from `" . $meta['table'] . "` where `" . $meta['colonne'] . "` = ?";
		        $numeros = array_merge($numeros, $db->getCol($sql, [$id]));
		        $tableGlobal = $meta['table'];
	        }

            if (count($numeros) > 0) {
	            $msg = txt('Impossible de supprimer cet élément car il est actuellement utilisé dans un/des projet(s) ', false);
                $msg .= $tableGlobal == 'financements_projets' ? txt('de financement', false) : txt('d\'acquisition', false);
                $msg .= txt(' dans un/des projets DocMatic.') . "\n\n";
	            $msg .= txt('Pour le supprimer, vous devrez d’abord retracer et éliminer l’ensemble des occurrences de cet élément dans les projets ', false);
	            $msg .= $tableGlobal == 'financements_projets' ? txt('de financement', false) : txt('d\'acquisition', false);
	            $msg .= txt(' où il apparaît:', false) . "\n\n";
                $msg .= dm_implode("\n", $numeros);
                print $msg;
	            die;
            }

	        $sql = "delete from " . $table . '_projets where liste_item_id = ?';
	        $db->query($sql, array($id));

        } else if ($table == 'questions_categories') {
            $sql = "select code from questions q where question_categorie_id = ? and projet_id = ?";
	        $codes_utilises = $db->getCol($sql, [$id, getProjetId()]);
            if (count($codes_utilises) > 0) {
	            print txt('Il est impossible de supprimer cette catégorie car elle est utilisée dans la/les QRT-BCF suivantes;' . "\n\n" . dm_implode("\n", $codes_utilises), false);
                die();
            }
        } else if(in_array($table, $tables_for_listes)) {
	        $sql = "delete from rvt_relations_installations
                    where dm_formulaire_id in (select id from formulaires where nom_table_source = ?) and dm_liste_item_id = ?";
	        $db->query($sql, [$table, $id]);

            $codes_utilises = Fiches::getCodesFichesReferenced($table, $id);
            if (count($codes_utilises) > 0) {
                if (isMaster()) {
                    print txt('Les fiches suivantes contiennent cette donnée (multi-projets);' . "\n\n" . 'Est-ce que vous désirez la supprimer quand même?' . "\n\n" . dm_implode("\n", $codes_utilises), false);
                } else {
                    print txt('Il est impossible de supprimer cette donnée (multi-projets) parce que les fiches suivantes la contienne;' . "\n\n" . dm_implode("\n", $codes_utilises), false);
                }
                die;
            }

            $sql = "delete from " . $table . '_projets where liste_item_id = ?';
            $db->query($sql, array($id));

        } else if ($table == 'financements_projets_depots') {
//            $depot = new FinancementsProjetsDepots($id);
//	        $projetFinancement = new FinancementsProjets($depot->financement_projet_id);
//
//            if (count($projetFinancement->fiches) > 0) {
//	            $sql = "update equipements_bio_medicaux set
//                        depot_source" . $depot->source_financement_id . "_id = null,
//                        " . $depot->getSqlToNullDepot() . "
//                    where projet_id = ? and depot_source" . $depot->source_financement_id . "_id = ? and fiche_id in (" . db::placeHolders($projetFinancement->fiches) . ")";
//	            $db->query($sql, array_merge([getProjetId(), $id], array_keys($projetFinancement->fiches)));
//            }
        }
        
        // patch à cause de serge
        if($table == 'acquisitions_jalons_modeles')
        {
            $sql = "select groupe_jalon from acquisitions_jalons_modeles jm where jm.id = ?";
	        $groupe_jalon = $db->getOne($sql, array($id));

            $sql = "delete from acquisitions_jalons_modeles where groupe_jalon = ?";
	        $db->query($sql, array($groupe_jalon));
        }
        else
        {
            $className::delete($id);
        }
        print 'OK';
	    $db->commit();
    }
    else if($_GET['action'] == 'calculerLaDate')
	{
        $dateDebut = $_GET['dateDebut'];
		$delai = $_GET['delai'];
		$exclusion_weekend = $_GET['exclusion_weekend'];
		$exclusion_feries = $_GET['exclusion_feries'];
		$exclusion_tombe_weekend = $_GET['exclusion_tombe_weekend'];

		$count5WD = 0;
		$temp = strtotime($dateDebut);
		$joursAExclure = ($exclusion_feries == 1 ? CongesFeriesTypes::getListeDateConge() : []);
        $strAddDay = ($exclusion_weekend == 1 ? 'weekday' : 'day');

		while($count5WD < $delai){
			$next1WD = strtotime("+1 $strAddDay", $temp);
			$next1WDDate = date('Y-m-d', $next1WD);
			if(!in_array($next1WDDate, $joursAExclure)){
				$count5WD++;
			}
			$temp = $next1WD;
		}

		$next5WD = date("Y-m-d", $temp);

        if (isWeekend($next5WD) && $exclusion_tombe_weekend == 1) {
            $dayToAdd = 0;
            if (date('N', strtotime($next5WD)) == 6) {
	            $dayToAdd = 2;
            } else if (date('N', strtotime($next5WD)) == 7) {
	            $dayToAdd = 1;
            }
	        $next5WD = date("Y-m-d", strtotime("$next5WD + $dayToAdd days"));
        }

		print json_encode(['statut' => 'OK', 'dateFin' => $next5WD]);
	}
    else if($_GET['action'] == 'force_delete')
    {
	    $db->autocommit(false);

	    $db_archive = db_archive();
	    $id = intval($_GET['id']);
	    $table = isset($_GET['table']) ? $_GET['table'] : '';
	    $typeTable = $_GET['type'];
	    $className = $tables[$typeTable][$table]['className'];
	    if (in_array($table, ['classifications_budgetaires_types', 'equipements_bio_medicaux_types_categories', 'equipements_bio_medicaux_types_sous_categories', 'equipements_bio_medicaux_types_categories_responsabilite', 'equipements_bio_medicaux_types_responsables_logistiques', 'equipements_bio_medicaux_types_directions_responsables', 'phases_incidences'])) {
		    $metas = EquipementsBioMedicauxTypes::getFlatChampsSaisieMetaData();
		    $champ_ids = [];
		    foreach ($metas as $key => $data) {
			    if ($data['table'] == $table) {
				    $champ_ids[] = $data['champ_id'];
			    }
		    }
		    if (count($champ_ids) > 0) {
                foreach ($champ_ids as $champ_id) {
	                $sql = "update equipements_bio_medicaux_types set `" . db::tableColonneValide($champ_id) . "` = " . ($table == 'equipements_bio_medicaux_types_categories' ? 999999 : 'null') . " where `" . db::tableColonneValide($champ_id) . "` = ?";
	                $db->query($sql, array($id));
                }

			    $sql = "delete from ".$table.'_projets where liste_item_id = ?';
			    $db->query($sql, array($id));

			    $className::delete($id);
		    }
	    } else if(in_array($table, $tables_for_listes)) {
		    $codes_utilises = Fiches::getCodesFichesReferenced($table, $id);
		    $tables_colonnes = Fiches::getTablesColonnesReferenced($table, $id);

		    $fiche_ids = array_keys($codes_utilises);

		    if (count($fiche_ids) > 0) {
			    foreach ($tables_colonnes as $table_ref => $colonne_ref) {
				    $sql = "delete from `" . db::tableColonneValide($table_ref) . "` where fiche_id in (" . db::placeHolders($fiche_ids) . ") and `" . db::tableColonneValide($colonne_ref) . "` = ?";
				    $db->query($sql, array_merge($fiche_ids, [$id]));

				    $sql = "delete from `" . db::tableColonneValide($db_name_archive) . "`.`" . db::tableColonneValide($table_ref) . "` where `" . db::tableColonneValide($colonne_ref) . "` = ?";
				    $db->query($sql, array($id));
			    }
		    }

		    $sql = "delete from ".$table.'_projets where liste_item_id = ?';
		    $db->query($sql, array($id));

		    $className::delete($id);
	    }
	    $db->commit();
    }
    else if($_GET['action'] == 'fiche_procedure_id_reload')
    {
        $programme_id = intval($_GET['id']);
        $periode_id = intval($_GET['periode_id']);
        $discipline_id = intval($_GET['discipline_id']);

        $procedures = ProcedureEntretien::getListe(array('periode_id' => $periode_id, 'discipline_id' => $discipline_id));

        $fiches_procedures = array();
        if($programme_id > 0)
        {
            $programme = new ProgrammeEntretien($programme_id);

            $procedures_ids = array_keys($procedures);
            $fiches_procedures = $programme->getProceduresFiches($procedures_ids);
        }

        $batiments_ids = array();
        $all_fiches_procedures = array();
        foreach($procedures as $procedure_id => $procedure)
        {
            $fiches = $procedure->getListeFiches();
            foreach($fiches as $i => $fiche)
            {
                $all_fiches_procedures[] = array('fiche' => $fiche, 'procedure' => $procedure);
                $batiments_ids[$fiche['batiment_id']] = $fiche['batiment_id'];
            }
        }

        $batiments = Batiment::getListe(array('ids' => $batiments_ids));

        $types = ProcedureEntretien::getTypes();

        if(count($all_fiches_procedures) > 0)
        {
?>
            <div id="filtre_procedures" style="font-size: 13px!important; margin: 0px 0 10px 20px;">
            <?=txt('Bâtiment:')?>
                <select class="batiment_id" style="width: 150px;">
                    <option value="0"></option>
                <? foreach ($batiments as $i => $batiment) { ?>
                        <option value="<?=$batiment->id?>"><?=$batiment->nom?></option>
                <? } ?>
                </select>
            </div>

            <div id="liste_procedures" style="max-height: 200px; overflow-y: scroll; overflow-x: hidden; padding-left: 10px; width: 100%; box-sizing: border-box">
<?
            foreach ($all_fiches_procedures as $i => $data) {
                $fiche = $data['fiche'];
                $procedure = $data['procedure'];
?>
                <div class="fiche_procedure_id batiment_<?=$fiche['batiment_id']?>" style="margin: 3px; width: 97%!important; box-sizing: border-box; float: left; border: solid 1px grey; padding: 15px 0 3px 0;">
                    <label for="fiche_procedure_id_<?=$fiche['fiche_id'].'_'.$procedure->id?>" style="width: 100%!important;">
<?
                        $chk = isset($fiches_procedures[$fiche['fiche_id']][$procedure->id]) ? ' checked="checked"' : '';
?>
                        <div style="float: left; width: 30px;">
                            <input type="checkbox" name="fiche_procedure_id[]" id="fiche_procedure_id_<?=$fiche['fiche_id'].'_'.$procedure->id?>" value="<?=$fiche['fiche_id'].'_'.$procedure->id?>"<?=$chk;?> />
                        </div>
                        <div style="font-size: 11px; float: left; width: 600px;">
                            <b style="font-weight: bold;"><?=txt('Fiche').':</b> '.$fiche['code']?>
                            <br /><b style="font-weight: bold;"><?=(isset($types[$procedure->type]) ? ' '.$types[$procedure->type]['nom'] : '').':</b> '.$procedure->nom.' '.$procedure->description?>
                            <br /><b style="font-weight: bold;"><?=txt('Durée').':</b> '.$liste_durees[(string)$procedure->duree_prevue]?>
                        </div>
                    </label>
                </div>
<?
            }
?>
            </div>
            <script>
                $('#filtre_procedures').find('select').change(function(){
                    filtrons();
                });

                function filtrons()
                {
                    batiment_id = $('#filtre_procedures').find('select.batiment_id').val();

                    $('#liste_procedures').find('div').hide();

                    filtre = '';
                    if(batiment_id > 0)
                    {
                        filtre = filtre + '.batiment_' + batiment_id;
                    }

                    $('#liste_procedures').find('div' + filtre).show();
                }
            </script>
<?
        }
    }
    else if($_GET['action'] == 'changeTimes')
    {
        $db->autocommit(false);

        $sql = "update programmes_planifiees "
                . "set date_du = ?, date_fin = ? "
                . "where id = ?";
        $db->query($sql, array($_GET['date_du'], $_GET['date_fin'], intval($_GET['id'])));

        $db->commit();
    }
    else if($_GET['action'] == 'changeDateTravaux')
    {
        $db->autocommit(false);

        $sql = "update procedures_entretiens_planifiees "
                . "set date_travaux = ? "
                . "where id = ?";
        $db->query($sql, array($_GET['date_travaux'], intval($_GET['id'])));

        $db->commit();
    }
    else if($_GET['action'] == 'getPlanification')
    {
        $sql = "select programmes_planifiees.id, nom as title, date_du as `start`, date_fin as end, programme_entretien_id "
                . "from programmes_planifiees "
                . "join programmes_entretiens on programmes_entretiens.id = programme_entretien_id "
                . "where date_du between ? and ? "
                . "order by date_du";
        $liste_tmp = $db->getAll($sql, array($_GET['start'].' 00:00:00', $_GET['end'].' 00:00:00'));

        $liste = array();
        foreach ($liste_tmp as $val)
        {
            $val['durationEditable'] = false;
            $liste[] = $val;
        }

        print json_encode($liste);
    }
    else if($_GET['action'] == 'getPlanificationCalendrier')
    {
        $priorites = ProcedureEntretienPlanifiee::getPriorites();
        $statuts = ProcedureEntretienPlanifiee::getStatuts();
        $liste_tmp = ProcedureEntretienPlanifiee::getListeProjections($_GET['start'], $_GET['end']);

        $liste = array();
        foreach ($liste_tmp as $val_tmp)
        {
            $val = array(
                'programme_entretien_planifiee_id' => $val_tmp->id,
                'title' => $val_tmp->procedure_entretien.' - '.(isset($priorites[$val_tmp->priorite]['nom']) ? $priorites[$val_tmp->priorite]['nom'] : txt('Non-planifié', false)).(isset($val_tmp->date_travaux_projection) ? ' ('.txt('Projection').')' : ''),
                'start' => (isset($val_tmp->date_travaux_projection) ? $val_tmp->date_travaux_projection : $val_tmp->date_travaux),
                'end' => (isset($val_tmp->date_travaux_projection) ? $val_tmp->date_travaux_projection : $val_tmp->date_travaux),
                'date_travaux' => (isset($val_tmp->date_travaux_projection) ? $val_tmp->date_travaux_projection : $val_tmp->date_travaux),
                'color' => (isset($val_tmp->date_travaux_projection) ? $statuts['projection']['color'] : $statuts[$val_tmp->code_statut]['color']),
                'code_statut' => (isset($val_tmp->date_travaux_projection) ? 'projection' : $val_tmp->code_statut),
                'is_projection' => (isset($val_tmp->date_travaux_projection) ? 1 : 0),
            );
            $liste[] = $val;
        }
        print json_encode($liste);
    }
    else if($_GET['action'] == 'getPlanificationCalendrierGbm')
    {
        $liste_tmp = ProcedureInspectionPlanifieeGbm::getListe(['dateDebut' => $_GET['start'], 'dateFin' => $_GET['end']]);

        $grayShades = generateGrayBlueShades(10000, 500);
        $liste = array();
        $iter = 350;
        foreach ($liste_tmp as $val_tmp) {
            $liste[] = [
                'planification_id' => $val_tmp->id,
                'title' => $val_tmp->numero . ' - ' . $val_tmp->description . ' (' . $val_tmp->responsable_planification . ')',
                'start' => $val_tmp->date_debut,
                'end' => $val_tmp->date_fin,
                'date_travaux' => $val_tmp->date_debut,
                'color' => $grayShades[$iter],
            ];
            $iter -= 15;
        }

        print json_encode($liste);
    }
    else if($_GET['action'] == 'getPlanificationCalendrierMep')
    {
        $liste_tmp = ProcedureMiseEnServicePlanifieeMep::getListe(['dateDebut' => $_GET['start'], 'dateFin' => $_GET['end']]);

        $grayShades = generateGrayBlueShades(10000, 500);
        $liste = array();
        $iter = 350;
        foreach ($liste_tmp as $val_tmp) {
            $liste[] = [
                'planification_id' => $val_tmp->id,
                'title' => $val_tmp->numero . ' - ' . $val_tmp->description . ' (' . $val_tmp->responsable_planification . ')',
                'start' => $val_tmp->date_debut,
                'end' => $val_tmp->date_fin,
                'date_travaux' => $val_tmp->date_debut,
                'color' => $grayShades[$iter],
            ];
            $iter -= 15;
        }

        print json_encode($liste);
    }
    else if ($_GET['action'] == 'getGanttData') {

            if (isset($_GET['id']) && $_GET['start_date'] && $_GET['end_date']) {

                if (isset($_GET['start_date'])) {
                    $start_date = $_GET['start_date'];
                    $datetime1 = new DateTime($_GET['start_date']);
                }

                if (isset($_GET['end_date'])) {
                    $end_date = $_GET['end_date'];
                    $datetime2 = new DateTime($end_date);
                }

                if (isset($_GET['id'])) {
                    $id_projet = $_GET['id'];
                }

                $difference = $datetime1->diff($datetime2);

                if (isset($end_date) && isset($id_projet) && isset($difference->d)) {
                    $data = [];
                    if (dm_substr( $id_projet, 0, 2 ) === "eg") {
                        $id_of_jalon_global = dm_str_replace("eg", "", $id_projet);
                        $get_real_id_jalon = dm_explode("-", $id_of_jalon_global);
                        if (isset($get_real_id_jalon['3'])) {
                            $sql = "UPDATE echeanciers_jalons 
                                    SET date_debut = ?, date_fin = ? 
                                    WHERE id = ?";
                            $data = [$start_date, $end_date, $get_real_id_jalon['3']];
                        }

                    } else {
                        $sql = "UPDATE acquisitions_projets_jalons 
                                SET date_jalon = ?, nb_jours_jalon = ? 
                                WHERE id = ?";
                        $data = [$end_date, $difference->d, $id_projet];
                    }
                    $db->query($sql, $data);
                }
            }
    }
    else if ($_GET['action'] == 'saveStructureHierarchique') {
	    $db->autocommit(false);
        $fiche_id = intval($_GET['fiche_id']);
	    $niveau_ids = $_GET['niveau_ids'];

	    $niveaux = BatimentsNiveaux::getListeReel(count($niveau_ids));
	    $parent_id = 0;
	    foreach ($niveau_ids as $niv => $niveau_id) {
            if (!is_numeric($niveau_id) && isset($niveaux[$niv])) {
                list($code, $nom) = dm_explode('___', $niveau_id);
	            $niveau = $niveaux[$niv];
	            $ordreMax = $db->getOne("select max(ordre) from batiments_niveaux_elements where batiment_niveau_id = ?", [$niveau->id]);
	            $data = array($niveau->id, $nom, $code, ($ordreMax+1));
	            $sql = "insert into batiments_niveaux_elements (batiment_niveau_id, batiment_niveau_element_id, nom, code, ordre) 
                                values (?, " . ($parent_id > 0 ? "'" . $parent_id . "'" : 'null') . ", ?, ?, ?)";
	            $db->query($sql, $data);
	            $niveau_id = $db->lastInsertId();
            }

            $parent_id = $niveau_id;
        }

	    $last_niveau_id = $parent_id;

	    $sql = "UPDATE fiches SET batiment_niveau_element_id = ? WHERE id = ?";
	    $db->query($sql, array($last_niveau_id, $fiche_id));

	    $sql = "UPDATE generaux_locaux SET batiment_niveau_element_id = ? WHERE fiche_id = ?";
	    $db->query($sql, array($last_niveau_id, $fiche_id));

	    Fiches::ajusterHierarchieFiltre(getOuvrageId(), $last_niveau_id, $fiche_id);

	    $db->commit();
	    print 'OK';
    }
    else if($_GET['action'] == 'getPlanificationCalendrierFinancements')
	{
		$priorites = ProcedureEntretienPlanifiee::getPriorites();
		$statuts = FinancementsProjets::getStatuts();

		$project_id_requested = '';
		if (isset($_GET['projet_id']) && $_GET['projet_id'] != '') {
			$project_id_requested = $_GET['projet_id'];
			$filtre['financement_projet_id'] = intval($project_id_requested);
		}
		$jalon_id_requested = isset($_GET['jalon_id']) && $_GET['jalon_id'] <> 0 ? $_GET['jalon_id'] : '';

		if (isset($_GET['type_groupe']) && $_GET['type_groupe'] != '') {
			$filtre['type_groupe'] = $_GET['type_groupe'];
		}
		if (isset($_GET['numero_groupe']) && $_GET['numero_groupe'] != '' && $_GET['numero_groupe'] != 0) {
			$filtre['numero_groupe'] = $_GET['numero_groupe'];
		}
		if (isset($_GET['echeancier']) && count($_GET['echeancier']) > 0) {
			$filtre['echeancier'] = $_GET['echeancier'];
		}

		$filtre['filtre_avance'] = array();
		if (FinancementsProjets::isFiltreAvanceActif()) {
			$filtre['filtre_avance'] = $_SESSION['filtre_avance_financements_projets_liste'];
		}

		$statut_requested = isset($_GET['statut']) ? $_GET['statut'] : '';

		$liste_tmp = FinancementsProjets::getListe($filtre);

		$liste = array();
		foreach ($liste_tmp as $val_tmp)
		{
			$jalons = FinancementsProjets::getJalons($val_tmp->id, $jalon_id_requested, $statut_requested, $project_id_requested, (isset($_GET['date_debut']) && $_GET['date_debut'] != '' ? $_GET['date_debut'] : ''), (isset($_GET['date_fin']) && $_GET['date_fin'] != '' ? $_GET['date_fin'] : ''));

			foreach ($jalons as $jalon) {

				if ($val_tmp->id == $jalon['financement_projet_id']) {
					$date = new DateTime($jalon['date_jalon']);
					$now = new DateTime();

					if ($jalon['ts_complete'] == null || $jalon['ts_complete'] == null) {
						if ($date < $now) {
							$code_statut = "en_retard";
							$color = "red";
						} else {
							$code_statut = "en_cours";
							$color = "green";
						}
					} else {
						$code_statut = "complete";
						$color = "black";
					}

					$liste[] = [
						'financement_id' => $val_tmp->id,
						'title' => $val_tmp->numero . " - ".$jalon['nom_jalon'],
						'description_projet' => $val_tmp->description_projet,
						'start' => $jalon['date_jalon'],
						'end' => $jalon['date_jalon'],
						'date_travaux' => '',
						'color' => $color,
						'code_statut' => $code_statut,
						'is_projection' => false,
					];

				}
			}

		}
		if (isset($_GET['exportExcel'])) {
			$dataArray = [
				[
					txt('Date du jalon', false),
					txt('Numéro de projet', false),
					txt('Description du projet', false),
					txt('Nom du jalon', false),
					txt('Statut du jalon', false),
				]
			];
			$statuts = FinancementsProjets::getStatuts();
			foreach ($liste as $jalon) {
				list($numero, $nom_jalon) = dm_explode(' - ', $jalon->getRaw('title'));
				$dataArray[] = [
					$jalon->getRaw('start'),
					$numero,
					$jalon->getRaw('description_projet'),
					$nom_jalon,
					$statuts[$jalon->getRaw('code_statut')]['nom'],
				];
			}

			$nom_fichier = 'export_jalons';
			exportExcelFromDataArray($dataArray, $nom_fichier, $nom_fichier);
		} else {
			print json_encode($liste);
		}
	}
    else if($_GET['action'] == 'getInfoAcquisition')
    {
        $id = (int)$_GET['id'];
	    $sql = "select ap.*,
                  (select nom_jalon from acquisitions_projets_jalons where acquisition_projet_id = ap.id and is_actif = 1 order by date_jalon limit 1) as titre_dernier_jalon,
                  (select max(date_jalon) from acquisitions_projets_jalons where acquisition_projet_id = ap.id and is_actif = 1) as date_dernier_jalon
                from acquisitions_projets ap
                left join priorites_acquisitions_types pat on ap.priorite_acquisition_id = pat.id
                left join contrats_responsables_dossiers_types ardtco on ardtco.id = responsable_dossier_contractuel_id 
                where ap.id = ?";
	    $row = $db->getRow($sql, array($id));
        $projetAcquisition = new AcquisitionsProjets($id, $row);
        print json_encode($projetAcquisition);
    }
    else if($_GET['action'] == 'getPlanificationCalendrierContrats')
    {
        $_GET['contrat_projet_id_cant_be_null'] = 1;
	    $liste = ContratsJalons::getListeCalandrier($_GET);

	    if (isset($_GET['exportExcel'])) {
		    $dataArray = [
			    [
				    txt('Date du jalon', false),
				    txt('Dossier', false),
				    txt('Nom du jalon', false),
				    txt('Statut du jalon', false),
			    ]
		    ];
		    $statuts = AcquisitionsProjets::getStatuts();
		    foreach ($liste as $jalon) {
			    list($dossier, $nom_jalon) = dm_explode(' - ', $jalon->getRaw('title'));
			    $dataArray[] = [
				    $jalon->getRaw('start'),
				    $dossier,
				    $nom_jalon,
				    $statuts[$jalon->getRaw('code_statut')]['nom'],
			    ];
		    }

		    $nom_fichier = 'export_jalons';
		    exportExcelFromDataArray($dataArray, $nom_fichier, $nom_fichier);
	    } else {
		    print json_encode($liste);
	    }
    }
    else if($_GET['action'] == 'getPlanificationCalendrierAcquisitions')
    {
        $priorites = ProcedureEntretienPlanifiee::getPriorites();
        $statuts = AcquisitionsProjets::getStatuts();

	    $project_id_requested = '';
        if (isset($_GET['projet_id']) && $_GET['projet_id'] != '') {
            $project_id_requested = $_GET['projet_id'];
            $filtre['acquisition_projet_id'] = intval($project_id_requested);
        }
        $jalon_id_requested = isset($_GET['jalon_id']) && $_GET['jalon_id'] <> 0 ? $_GET['jalon_id'] : '';

        if (isset($_GET['type_groupe']) && $_GET['type_groupe'] != '') {
            $filtre['type_groupe'] = $_GET['type_groupe'];
        }
        if (isset($_GET['numero_groupe']) && $_GET['numero_groupe'] != '' && $_GET['numero_groupe'] != 0) {
            $filtre['numero_groupe'] = $_GET['numero_groupe'];
        }
	    if (isset($_GET['echeancier']) && count($_GET['echeancier']) > 0) {
		    $filtre['echeancier'] = $_GET['echeancier'];
	    }

	    $filtre['filtre_avance'] = array();
	    if (AcquisitionsProjets::isFiltreAvanceActif()) {
		    $filtre['filtre_avance'] = $_SESSION['filtre_avance_acquisitions_projets_2'];
	    }

        $statut_requested = isset($_GET['statut']) ? $_GET['statut'] : '';

        $liste_tmp = AcquisitionsProjets::getListeProjets_simple($filtre);

	    $liste = array();
        $listeExcel = array();
        foreach ($liste_tmp as $val_tmp)
        {
            $jalons = AcquisitionsProjets::getJalons($val_tmp->id, $jalon_id_requested, $statut_requested, $project_id_requested, (isset($_GET['date_debut']) && $_GET['date_debut'] != '' ? $_GET['date_debut'] : ''), (isset($_GET['date_fin']) && $_GET['date_fin'] != '' ? $_GET['date_fin'] : ''));

            foreach ($jalons as $jalon) {

                if ($val_tmp->id == $jalon['acquisition_projet_id']) {
                    $date = new DateTime($jalon['date_jalon']);
                    $now = new DateTime();

                    if ($jalon['ts_complete'] == null || $jalon['ts_complete'] == null) {
                        if ($date < $now) {
                            $code_statut = "en_retard";
                            $color = "red";
                        } else {
	                        $code_statut = "en_cours";
                            $color = "green";
                        }
                    } else {
                        $code_statut = "complete";
                        $color = "black";
                    }

                    $liste[] = [
                        'acquisition_id' => $val_tmp->id,
                        'title' => $val_tmp->numero . " - ".$jalon['nom_jalon'],
	                    'description_projet' => $val_tmp->description_projet,
                        'start' => $jalon['date_jalon'],
                        'end' => $jalon['date_jalon'],
                        'date_travaux' => '',
                        'color' => $color,
                        'code_statut' => $code_statut,
                        'is_projection' => false,
                        'nb_jours_jalon' => $jalon['nb_jours_jalon'],
                    ];

                    $listeExcel[] = [
                        'acquisition_id' => $val_tmp->id,
                        'title' => $val_tmp->numero . " - ".$jalon->getRaw('nom_jalon'),
                        'description_projet' => $val_tmp->description_projet,
                        'start' => $jalon->getRaw('date_jalon'),
                        'end' => $jalon->getRaw('date_jalon'),
                        'date_travaux' => '',
                        'color' => $color,
                        'code_statut' => $code_statut,
                        'is_projection' => false,
                        'nb_jours_jalon' => $jalon['nb_jours_jalon'],
                    ];

                }
            }

        }
        if (isset($_GET['exportExcel'])) {
	        $dataArray = [
	            [
		            txt('Date du jalon', false),
	                txt('Numéro de projet', false),
		            txt('Description du projet', false),
		            txt('Nom du jalon', false),
		            txt('Statut du jalon', false),
		            txt('Durée du jalon', false),
                ]
            ];
	        $statuts = AcquisitionsProjets::getStatuts();
	        foreach ($listeExcel as $jalon) {
	            list($numero, $nom_jalon) = dm_explode(' - ', $jalon['title']);
		        $dataArray[] = [
			        $jalon['start'],
			        $numero,
			        $jalon['description_projet'],
			        $nom_jalon,
			        $statuts[$jalon['code_statut']]['nom'],
			        $jalon['nb_jours_jalon'],
                ];
            }

	        $nom_fichier = 'export_jalons';
	        exportExcelFromDataArray($dataArray, $nom_fichier, $nom_fichier);
        } else {
	        print json_encode($liste);
        }
    }
    else if($_GET['action'] == 'planification')
    {
        $db->autocommit(false);

        $sql = "insert into programmes_planifiees (programme_entretien_id, date_du, date_fin) "
                . "values (?, ?, ?)";
        $db->query($sql, array(intval($_GET['programme_id']), $_GET['date_du'], $_GET['date_fin']));
        print json_encode(array('id' => $db->lastInsertId()));

        $db->commit();
    } else if ($_GET['action'] == 'saveStraightDataEquipementBioMedicaux') {
        $valeur = $_POST['valeur'];
        $champ = $_POST['champ'];
        if (is_array($valeur)) {
            $ids = $_POST['ids'];
            $valeurs = $valeur;
            if (count($ids) > 0) {
                foreach ($ids as $iter => $id) {
                    $laValeur = $valeurs[$iter];
                    if ($_POST['format'] == 'int') {
                        $laValeur = (int)$valeurs[$iter];
                    } elseif ($_POST['format'] == 'float') {
                        $laValeur = (float)$valeurs[$iter];
                    }
                    $sql = "update equipements_bio_medicaux set `" . db::tableColonneValide($champ) . "` = ? where id = ?";
                    $db->query($sql, array($laValeur, $id));
                }
            }
        } else {
            if ($_POST['format'] == 'int') {
                $valeur = (int)$valeur;
            } elseif ($_POST['format'] == 'float') {
                $valeur = (float)$valeur;
            }
            if (strlen($valeur) > 0) {
                if (isset($_POST['ids'])) {
                    $ids = $_POST['ids'];
                    if (count($ids) > 0) {
                        $sql = "update equipements_bio_medicaux set `" . db::tableColonneValide($champ) . "` = ? where id in (" . db::placeHolders($ids) . ")";
                        $db->query($sql, array_merge([$valeur], $ids));
                    }
                } else {
                    $id = (int)$_POST['id'];
                    $sql = "update equipements_bio_medicaux set `" . db::tableColonneValide($champ) . "` = ? where id = ?";
                    $db->query($sql, [$valeur, $id]);
                }
            } else {
                if (isset($_POST['ids'])) {
                    $ids = $_POST['ids'];
                    if (count($ids) > 0) {
                        $sql = "update equipements_bio_medicaux set `" . db::tableColonneValide($champ) . "` = null where id in (" . db::placeHolders($ids) . ")";
                        $db->query($sql, $ids);
                    }
                } else {
                    $id = (int)$_POST['id'];
                    $sql = "update equipements_bio_medicaux set `" . db::tableColonneValide($champ) . "` = null where id = ?";
                    $db->query($sql, [$id]);
                }
            }
        }
    } else if($_GET['action'] == 'traitementLocauxATraiter') {
        $mode = $_POST['mode'];
        $fiche_projet_ids = [];
        if (isset($_POST['fiche_projet_ids'])) {
            $fiche_projet_ids = $_POST['fiche_projet_ids'];
        } else {
            $fiche_id = (int)$_POST['fiche_id'];
            $acquisition_projet_id = (int)$_POST['acquisition_projet_id'];
            $fiche_projet_ids = [$fiche_id . '-' . $acquisition_projet_id];
        }
        foreach ($fiche_projet_ids as $fiche_projet_id) {
            list($fiche_id, $acquisition_projet_id) = explode('-', $fiche_projet_id);
            $sql = '';
            if ($mode == 'ajouter') {
                $sql = "INSERT INTO acquisitions_projets_fiches (acquisition_projet_id, fiche_id) VALUES (?, ?)";
            } else if ($mode == 'retirer') {
                $sql = "DELETE FROM acquisitions_projets_fiches WHERE acquisition_projet_id = ? AND fiche_id = ?";
            }
            db::instance()->query($sql, [$acquisition_projet_id, $fiche_id]);
        }
    }
} else if(isset($_GET['section']) && isset($_GET['module'])) {
    if ($_GET['section'] == 'exportation') {
        $classe = $_GET['module'];
        $formulaire_id = (int)$_GET['formulaire_id'];
        $exportation = new $classe($formulaire_id);

        if (isset($_POST['delete']) && intval($_POST['update_filtre']) > 0) {
            $modelesExport = new ModelesExports(intval($_POST['update_filtre']));
            $modelesExport->delete();

            $exportation->champsToExport = array();
        } else if (isset($_POST['modele_selectionne'])) {
            if (intval($_POST['modele_selectionne']) > 0) {
                $exportation->modeleSelectionne = new ModelesExports(intval($_POST['modele_selectionne']));
                $exportation->champsToExport = json_decode($exportation->modeleSelectionne->getRaw('json_data'), true);
                $isModeleSel = true;
            } else {
                $exportation->champsToExport = array();
            }
        } else if (isset($_GET['export']) || isset($_GET['exporter']) || isset($_POST['nom_sauvegarde'])) {
            $exportation->champsToExport = $_POST['champs_to_export'];
            $is_erreur = false;
            if (isset($_GET['exporter'])) {
                $exportation->champsToExport = $_POST['champs_to_export'];
                $filtre = &$_SESSION['filtre_' . $classe];
                $filtredCache = new FiltredCache($classe, $filtre);
                $logs = $filtredCache->getFilteredData();

                $generauxLocaux = [];
                $equipements = [];
                $valeursIntrinseques = [];
                if ($exportation->formulaire == 'EquipementsBioMedicaux') {
                    $generauxLocaux = GenerauxLocaux::getListe(['code_pivot_ligne_for_key' => 'fiche_id']);
                    $equipements = EquipementsBioMedicauxTypes::getListe();
                    $formulaires_listes = EquipementsBioMedicauxTypes::getFormulairesListes(
                        'EquipementsBioMedicaux'
                    );
                    if (isset($formulaires_listes[0])) {
                        $formulaire_liste = $formulaires_listes[0];
                        $valeursIntrinseques = FormulairesListes::getValeursByElements($formulaire_liste->id);
                    }
                }

                $dataArray = [];
                $ligne = 0;

                $allChamps = $exportation->getAllChamps();
                foreach ($exportation->champsToExport as $nom => $data) {
                    if (isset($data['export'])) {
                        $dataChamp = $allChamps[$nom];
                        $dataArray[$ligne][] = !empty($data['alias']) ? $data['alias'] : $dataChamp['label'];
                    }
                }
                $ligne++;
                foreach ($logs as $i => $leData) {
                    $lesSousLogs = [$leData];
                    if (isset($leData['parametres'])) {
                        $lesSousLogs = $leData['parametres'];
                    }
                    foreach ($lesSousLogs as $log) {
                        foreach ($exportation->champsToExport as $nom => $data) {
                            if (isset($data['export'])) {
                                $dataChamp = $allChamps[$nom];
                                $valeur = '';
                                if ($dataChamp['section'] == 'donneesDeBase') {
                                    $valeur = $log[$nom] ?? '';
                                } else if ($dataChamp['section'] == 'parametresGenerauxLocaux') {
                                    $fiche = $generauxLocaux[$log['fiche_id']] ?? [];
                                    $valeur = $fiche[$nom] ?? '';
                                } else if ($dataChamp['section'] == 'parametresEquipementsBioMedicauxTypes') {
                                    $equipement_id = $log['equipement_id'] ?? $log['equipement_bio_medicaux_type_id'];
                                    $equipement = $equipements[$equipement_id] ?? [];
                                    $valeur = $equipement[$nom] ?? '';
                                } else if ($dataChamp['section'] == 'parametresEquipementsBioMedicauxTypesIntrinseques') {
                                    $equipement_id = $log['equipement_id'] ?? $log['equipement_bio_medicaux_type_id'];
                                    $valeurIntrinsequeData = $valeursIntrinseques[$equipement_id] ?? [];
                                    list($intran, $param_id) = explode('__', $nom);
                                    $valeur = $valeurIntrinsequeData[$param_id]['valeur'] ?? '';
                                }

                                $dataArray[$ligne][] = dm_htmlspecialchars_decode($valeur);
                            }
                        }
                        $ligne++;
                    }
                }

                $nom_fichier = 'logs_detailles_' . date('Y-m-d');
                exportDataSelonFormat($dataArray, $nom_fichier, $nom_fichier, 'forceString');
                exit;
            } else if (isset($_POST['update_filtre'])) {
                db::instance()->autocommit(false);
                $sql = "update modeles_exports set json_data = ?, nom = ? where id = ?";
                db::instance()->query($sql, array(json_encode($exportation->champsToExport), $_POST['nom_sauvegarde'], intval($_POST['update_filtre'])));
                db::instance()->commit();

                $exportation->modeleSelectionne = new ModelesExports(intval($_POST['update_filtre']));
                $isModeleSel = true;
            } else if (isset($_POST['nom_sauvegarde'])) {
                if (dm_strlen($_POST['nom_sauvegarde']) == 0) {
                    setErreur(txt('Le modèle ne sera pas sauvegardé. Vous devez entrer un nom pour ce modèle.'));
                    $is_erreur = true;
                }

                if (!$is_erreur) {
                    db::instance()->autocommit(false);
                    $sql = "insert into modeles_exports (projet_id, nom, usager_id, json_data, module, formulaire) values (?, ?, ?, ?, ?, ?)";
                    db::instance()->query($sql, array(getProjetId(), $_POST['nom_sauvegarde'], getIdUsager(), json_encode($exportation->champsToExport), $classe, $exportation->formulaire));
                    $id_added = db::instance()->lastInsertId();

                    $sql = "select * from acquisitions_projets_exports_modeles where id = ?";
                    $modele_export_sel = db::instance()->getRow($sql, array($id_added));

                    $exportation->modeleSelectionne = new ModelesExports($id_added);

                    db::instance()->commit();
                    $isModeleSel = true;
                }
            }
        }

        $noHeader = true;
        include('config/header.inc.php');
        $exportation->afficheFormulaireExportation();
        include('config/footer.inc.php');
    } else if ($_GET['section'] == 'fiche' && $_GET['module'] == 'clone_formulaire_custom') {
        $noHeader = true;
        include('config/header.inc.php');
        $id = (int)$_GET['id'];
        $fiche_id = (int)$_GET['fiche_id'];
        $formulaireCustom = new FormulairesCustoms($id);
        $formulaireCustom->afficheFormulaireClone($fiche_id);

        $includeAdminIndex=1;
        include('config/footer.inc.php');
    } else if ($_GET['section'] == 'fiche' && $_GET['module'] == 'cloner_formulaire_custom') {
        $id = (int)$_GET['id'];
        $fiche_id = (int)$_GET['fiche_id'];
        $formulaireCustom = new FormulairesCustoms($id);
        $formulaireCustom->cloner($fiche_id, $_POST['listeFiches'], $_POST['champs']);
        ?>
        <script>
			parent.reloadFiche(<?=$fiche_id?>);
			parent.$.fancybox.close();
        </script>
        <?
    } else if ($_GET['section'] == 'gestionnairePage') {
        $classe = $_GET['module'];
        include('config/header.inc.php');
        $classe::affichagePage();

        $includeAdminIndex=1;
        include('config/footer.inc.php');
    } else if ($_GET['section'] == 'importLibrairie') {
        $classe = $_GET['module'];
        $etape = 'valider';
        $entetesColonnes = [];
        if(isset($_POST['synchroniser'])) {
            db::instance()->autocommit(false);
            $classe::synchroniserImport();
            db::instance()->commit();
            ?>
            <script>
				parent.window.location.reload();
				parent.$.fancybox.close();
            </script>
            <?
            die();
        } else if (isset($_POST['valider'])) {
            $entetesColonnes = $classe::validerImport();
            $etape = 'synchroniser';
        }
        $noHeader = true;
        include('config/header.inc.php');
        $classe::affichagePageImport($etape, $entetesColonnes);
        include('config/footer.inc.php');
    } else if ($_GET['section'] == 'exportLibrairie') {
        $classe = $_GET['module'];
        $classe::exporter();
	} else if ($_GET['section'] == 'landing' && $_GET['module'] == 'selOuvrage') {
		setOuvrageInfo(intval($_GET['id']), true);
		header('Location: index.php?section=fiche&module=index');
	} else if ($_GET['section'] == 'landing' && $_GET['module'] == 'selProjet') {
		setProjetInfo(intval($_GET['id']), $_GET['mode']);
		if (isMobile() && in_array(ClientsMaster::instance()->releve_mobile, ['equipements', 'inventaire'])) {
			header('Location: index.php?section=releveMobile&module=' . ClientsMaster::instance()->releve_mobile);
		} else {
            $usager = getUsager();
            if ($usager->is_mobile_only == 1) {
                if (in_array(ClientsMaster::instance()->releve_mobile, ['equipements', 'inventaire'])) {
                    FiltredCache::initPersonalizedTmpPath(true);
                    header('Location: index.php?section=releveMobile&module=' . ClientsMaster::instance()->releve_mobile);
                    die();
                }
                erase_session();
                header('Location: login.php');
                die();
            } else {
                if (isset($_GET['goto_notif'])) {
                    header('Location: index.php?section=notifications&module=index');
                } else {
                    header('Location: index.php?section=fiche&module=index');
                }
            }
		}
		die();
	} else if ($_GET['section'] == 'financement' && $_GET['module'] == 'projets') {
        if (isset($_GET['mode']) && $_GET['mode'] == 'specifique') {
	        include_once('financement/projets_financements.php');
        } else if (!isset($_GET['mode']) || $_GET['mode'] == 'global') {
	        include_once('financement/projets_financements_unifiees.php');
        } else {
	        envoiCourriel('<EMAIL>', 'Debug', getDebug($_SERVER));
	        include_once('financement/projets_financements.php');
        }
	} else if ($_GET['section'] == 'financement' && $_GET['module'] == 'groupes_a_reviser') {
		include_once('financement/groupes_a_reviser_financements.php');
	} else if ($_GET['section'] == 'financement' && $_GET['module'] == 'equipements_non_traites') {
		include_once('financement/equipements_non_traites_financements.php');
	} else if ($_GET['section'] == 'financement' && $_GET['module'] == 'projet_groupe_tout_associer') {
		include_once('financement/projet_groupe_tout_associer_financements.php');
	} else if ($_GET['section'] == 'financement' && $_GET['module'] == 'projet_groupe_tout_distribuer') {
		include_once('financement/projet_groupe_tout_distribuer_financements.php');
	} else if ($_GET['section'] == 'financement' && $_GET['module'] == 'projets_vue') {
		include_once('financement/projets_vue_financements.php');
	} else if ($_GET['section'] == 'financement' && $_GET['module'] == 'affiche_groupe') {
		include_once('financement/financements_groupe.php');
	} else if ($_GET['section'] == 'financement' && $_GET['module'] == 'affiche_groupes_fiches') {
		include_once('financement/financements_groupes_fiches.php');
	} else if ($_GET['section'] == 'financement' && $_GET['module'] == 'affiche_depots_fiches') {
		include_once('financement/financements_depots_fiches_dyn.php');
	} else if ($_GET['section'] == 'admin' && $_GET['module'] == 'logs_listes_param') {
		include_once('admin/logs_listes_parametres.php');
	} else if ($_GET['section'] == 'admin' && $_GET['module'] == 'popup_listes_param') {
		include_once('admin/get_form_liste_params.php');
	} else if ($_GET['section'] == 'fiche' && $_GET['module'] == 'importation_ws') {
	    if (isset($_GET['projet_id'])) {
	        setProjetInfo(intval($_GET['projet_id']), 'edition');
        }
		include_once('fiche/importation_ws.php');
	} else if ($_GET['section'] == 'fiche' && $_GET['module'] == 'export_codes') {
        $filtre_global_to_use = $_GET['filtre_global_to_use'] ?? '';
		$filtre = ['by_code' => 1, 'filtre_global_to_use' => $filtre_global_to_use];
		if (isset($_GET['just_deleted'])) {
			$filtre = ['by_code' => 1, 'filtre_global_to_use' => $filtre_global_to_use, 'just_deleted' => 1];
		}
		$batiments_niveaux = BatimentsNiveaux::getListe();
		$batiments_niveaux_all = BatimentsNiveaux::getAllLevelsData(true, 0, true);
		$fiches = Fiches::getListe($filtre);

		$dataArray = array();
		$iter = 0;
		$noCol = 0;
		$dataArray[$iter][$noCol++] = txt('Code');
		foreach ($batiments_niveaux as $i => $batiment_niveau) {
			$dataArray[$iter][$noCol++] = $batiment_niveau->getRaw('nom');
		}
		$iter++;
		foreach ($fiches as $code => $fiche) {
			$noCol = 0;
			$dataArray[$iter][$noCol++] = $fiche->getRaw('code');

			$iter_struct = 0;
			$struct_data = $batiments_niveaux_all[$fiche->id] ?? array();
			foreach ($batiments_niveaux as $i => $b_niveau) {
				$niveau = $struct_data['local_niveaux'][$iter_struct] ?? null;
				$dataArray[$iter][$noCol++] = isset($niveau) ? $niveau['nom'] : '';
				$iter_struct++;
			}
			$iter++;
		}

		$nom_fichier = 'codes_' . date('Y-m-d');
		exportExcelFromDataArray($dataArray, $nom_fichier, $nom_fichier);

	} else if ($_GET['section'] == 'fiche' && $_GET['module'] == 'delete') {
		$db->autocommit(false);

		$sql = "UPDATE `fiches` set ts_delete = CURRENT_TIMESTAMP, usager_delete_id = ? WHERE id = ?;";
		$db->query($sql, array(getIdUsager(), intval($_GET['id'])));

		$sql = "UPDATE `fiches` set ts_delete = CURRENT_TIMESTAMP, usager_delete_id = ? WHERE fiche_parent_id = ?;";
		$db->query($sql, array(getIdUsager(), intval($_GET['id'])));

		$sql = "UPDATE `fiches` set fiche_local_id = null WHERE fiche_local_id = ?;";
		$db->query($sql, array(intval($_GET['id'])));

		$sql = "UPDATE `fiches` set fiche_local_destination_id = null WHERE fiche_local_destination_id = ?;";
		$db->query($sql, array(intval($_GET['id'])));

		$sql = "UPDATE `fiches` set fiche_local_controleur_id = null WHERE fiche_local_controleur_id = ?;";
		$db->query($sql, array(intval($_GET['id'])));

		$sql = "insert into `fiches_logs` (fiche_id, projet_id, ts_ajout, usager_ajout_id, code, nb_lignes, raison) values (?, ?, current_timestamp, ?, ?, ?, ?)";
		$db->query($sql, array(intval($_GET['id']), getProjetId(), getIdUsager(), 'deleteFiche', 0, 'Suppression de la fiche'));

		$db->commit();

        if (isset($_GET['from_gestion_portes'])) {
            header('Location: index.php?section=portes&module=index');
        } else {
		    header('Location: index.php?section=' . (isset($_GET['is_gabarit']) && intval($_GET['is_gabarit']) == 1 ? 'gabarits' : 'fiche') . '&module=index');
        }
		exit;
	} else if ($_GET['section'] == 'fiche' && $_GET['module'] == 'recuperation') {
		$db->autocommit(false);

		Fiches::recuperation(intval($_GET['id']));

		$db->commit();

		header('Location: index.php?section=' . (isset($_GET['is_gabarit']) && intval($_GET['is_gabarit']) == 1 ? 'gabarits' : 'fiche') . '&module=index&id=' . intval($_GET['id']));
		exit;
	} else if ($_GET['section'] == 'fiche' && $_GET['module'] == 'clone_formulaire') {
		include_once('fiche/clone_formulaire.php');
	} else if ($_GET['section'] == 'fiche' && $_GET['module'] == 'form_logs') {
		include_once('fiche/form_logs.php');
	} else if ($_GET['section'] == 'fiche' && $_GET['module'] == 'bilan') {
		include_once('fiche/bilan.php');
	} else if ($_GET['section'] == 'fiche' && $_GET['module'] == 'apparier') {
		include_once('fiche/apparier.php');
	} else if ($_GET['section'] == 'fiche' && $_GET['module'] == 'locaux_types') {
		include_once('fiche/locaux_types.php');
	} else if ($_GET['section'] == 'fiche' && $_GET['module'] == 'editeur_lot') {
		include_once('fiche/editeur_lot.php');
	} else if ($_GET['section'] == 'admin' && $_GET['module'] == 'editSystemData') {
		include_once('admin/editSystemData.php');
	} else if ($_GET['section'] == 'admin' && $_GET['module'] == 'edit') {
		include_once('admin/edit.php');
	} else if ($_GET['section'] == 'ouvrage' && $_GET['module'] == 'index') {
		include_once('ouvrage/index.php');
	} else if ($_GET['section'] == 'ouvrage' && $_GET['module'] == 'editSystemData') {
		include_once('ouvrage/editSystemData.php');
	} else if ($_GET['section'] == 'ouvrage' && $_GET['module'] == 'edit') {
		include_once('ouvrage/edit.php');
	} else if ($_GET['section'] == 'dle' && $_GET['module'] == 'dle') {
		include_once('dle/dle.php');
	} else if ($_GET['section'] == 'filtre_avance' && $_GET['module'] == 'filtre_avance') {
		include_once('filtre_avance/filtre_avance.php');
	} else if ($_GET['section'] == 'fiche' && $_GET['module'] == 'index') {

        if (!isset($_SESSION['projet_info']) && !isset($_SESSION['ouvrage_info'])) {
            header('Location: index.php?section=landing&module=index');
            die();
        }

		$_SESSION['is_gabarit'] = 0;
		if (isset($_GET['clear_filtre'])) {
			$_SESSION['filtre_avance'] = array();
			$_SESSION['filtre_top'] = array();
		}

		if (isset($_GET['fiche_code'])) {
			$_GET['id'] = Fiches::getIdByCode($_GET['fiche_code']);
		}

		include_once('fiche/index.php');
	} else if ($_GET['section'] == 'gabarits' && $_GET['module'] == 'index') {
		$_SESSION['is_gabarit'] = 1;
		if (isset($_GET['clear_filtre'])) {
			$_SESSION['filtre_avance'] = array();
			$_SESSION['filtre_top'] = array();
		}
		include_once('fiche/index.php');
	} else if ($_GET['section'] == 'coordination' && $_GET['module'] == 'general') {
		include_once('coordination/rapport_coordination.php');
	} else if ($_GET['section'] == 'fiche' && $_GET['module'] == 'filtre_formulaire') {
		include_once('fiche/filtre_formulaires.php');
	} else if ($_GET['section'] == 'fiche' && $_GET['module'] == 'logs') {
		include_once('fiche/logs.php');
	} else if ($_GET['section'] == 'fiche' && $_GET['module'] == 'logs_detailles') {
		include_once('fiche/logs_detailles.php');
	} else if ($_GET['section'] == 'fiche' && $_GET['module'] == 'fiche') {
		include_once('fiche/fiche.php');
	} else if ($_GET['section'] == 'elfinder' && $_GET['module'] == 'connector') {
		include_once('elfinder/php/connector.php');
	} else if ($_GET['section'] == 'synchro' && $_GET['module'] == 'rename_manuels_path') {
		include_once('synchro/rename_manuels_path.php');
	} else if ($_GET['section'] == 'ouvrage' && $_GET['module'] == 'index') {
		include_once('ouvrage/index.php');
	} else if ($_GET['section'] == 'ouvrage' && $_GET['module'] == 'wizard') {
		include_once('ouvrage/wizard.php');
	} else if ($_GET['section'] == 'notifications' && $_GET['module'] == 'admin') {
		include_once('notifications/admin.php');
	} else if ($_GET['section'] == 'notifications' && $_GET['module'] == 'export') {
		include_once('notifications/export.php');
	} else if ($_GET['section'] == 'fiche' && $_GET['module'] == 'wizard') {
		include_once('fiche/wizard.php');
	} else if ($_GET['section'] == 'entretien' && $_GET['module'] == 'index') {
		include_once('entretien/index.php');
	} else if ($_GET['section'] == 'entretien' && $_GET['module'] == 'calendrier') {
		include_once('entretien/calendrier.php');
	} else if ($_GET['section'] == 'notifications' && $_GET['module'] == 'index') {
		include_once('notifications/index.php');
	} else if ($_GET['section'] == 'synchro' && $_GET['module'] == 'updateMasterData') {
		include_once('synchro/update.master.data.php');
	} else if ($_GET['section'] == 'superficies' && $_GET['module'] == 'beta') {
		include_once('superficies/beta.php');
	} else if ($_GET['section'] == 'synchro' && $_GET['module'] == 'validateData') {
		$db->autocommit(false);

		if (!in_array($_GET['table'], $masterTables))
			die('table non valide');

		$sql = "UPDATE `" . db::tableColonneValide($_GET['table']) . "` set ts_valide = CURRENT_TIMESTAMP WHERE code_ajout = ?;";
		$db->query($sql, array($_GET['token']));
		$db->commit();
		?>
        <b style="color: green;"><?php print txt('Validation réussi avec succès!'); ?> <a
                    href="index.php?section=synchro&module=updateMasterData"><?php print txt('Cliquez ici pour lancer la synchronisation tout de suite.'); ?></a></b>
		<?php
	} else if ($_GET['section'] == 'procedure' && $_GET['module'] == 'download') {
		$id = intval($_GET['id']);

		$sql = "select nom from procedures_entretiens where id = ?";
		$procedure = $db->getRow($sql, array($id));

		$nom = dm_preg_replace("/[^a-zA-Z0-9_\-]+/", "", $procedure['nom']);

		header("Content-Type: application/force-download; name=\"" . $nom . ".pdf\"");
		header("Content-Transfer-Encoding: binary");
		header("Content-Disposition: inline; filename=\"" . $nom . ".pdf\";");
		header('Expires: Sat, 26 Jul 1997 05:00:00 GMT');
		header('Last-Modified: ' . gmdate('D, d M Y H:i:s') . ' GMT');
		header('Cache-Control: no-store, no-cache, must-revalidate');
		header('Cache-Control: post-check=0, pre-check=0', false);
		header('Pragma: no-cache');

		print file_get_contents(procedurePath($id));
	} else if ($_GET['section'] == 'manuel' && $_GET['module'] == 'download') {
		$id = intval($_GET['id']);

		$sql = "select numero_modele from modeles where id = (select modele_id from manuels where id = ?)";
		$manuel = $db->getRow($sql, array($id));

		$numero_modele = dm_preg_replace("/[^a-zA-Z0-9_\-]+/", "", $manuel['numero_modele']);

		header("Content-Type: application/force-download; name=\"" . $numero_modele . ".pdf\"");
		header("Content-Transfer-Encoding: binary");
		header("Content-Disposition: inline; filename=\"" . $numero_modele . ".pdf\";");
		header('Expires: Sat, 26 Jul 1997 05:00:00 GMT');
		header('Last-Modified: ' . gmdate('D, d M Y H:i:s') . ' GMT');
		header('Cache-Control: no-store, no-cache, must-revalidate');
		header('Cache-Control: post-check=0, pre-check=0', false);
		header('Pragma: no-cache');

		print file_get_contents(manuelPath($id));
	} else if ($_GET['section'] == 'fiche' && $_GET['module'] == 'download') {
		/*
				// get the HTML
				ob_start();
				$_GET['pdf'] = 1;
				include('fiche/fiche.php');
				$content = ob_get_clean();

				// convert in PDF
				require_once('html2pdf_v4.03/html2pdf.class.php');
				try
				{
					$html2pdf = new HTML2PDF('P', 'A4', 'fr');
			//      $html2pdf->setModeDebug();
					$html2pdf->setDefaultFont('Arial');
					$html2pdf->writeHTML($content, isset($_GET['vuehtml']));
					$html2pdf->Output('fiche.pdf');
				}
				catch(HTML2PDF_exception $e) {
					echo $e;
					exit;
				}
		*/
		/*        ob_start();*/
		$_GET['pdf'] = 1;
		include('fiche/fiche.php');/*
        $html = ob_get_clean();
        ob_end_clean();

        $dompdf = new Dompdf();

        $dompdf->loadHtml($html);
        $dompdf->render();

        $dompdf->stream("test.pdf", array("Attachment" => false));*/
	} else if ($_GET['section'] == 'superficies' && $_GET['module'] == 'index') {
        include_once('superficies/index.php');
    } else if (is_file(nomFichierRepertoireValide($_GET['section']) . "/" . nomFichierRepertoireValide($_GET['module']) . ".php")) {
        include_once(nomFichierRepertoireValide($_GET['section']) . "/" . nomFichierRepertoireValide($_GET['module']) . ".php");
    } else {
		disconnect();
	}
}
else {
    if (!$isDev && !$isRemoteDev) {
	    envoiCourriel('<EMAIL>', 'Debug disconnect()', getDebug($_SERVER));
    }
	disconnect();
}

if(!$IsLoginEnv && !$IsRevitEnv && !isset($do_not_log_perfo)) {
    try {
        if (isMasterOrMario()) {
            $pathDebug = getTempDir() . 'sqls_' . date('Y-m-d') . '.log';
            $file = new SplFileObject($pathDebug, 'a');
            foreach ($sql_logs as $sql_log) {
                if (floatval($sql_log['duration']) > 3) {
                    $file->fwrite("\n" . '-------------------------------------------------------------------------');
                    $file->fwrite("\n" . 'ANALYZE ' . $sql_log['query']);
                }
            }
        }

        $ts_fin = microtime(true);
        $fin_script = date('Y-m-d H:i:s');
        $usager = getUsager();

        $total_duration = $ts_fin - $ts_debut_all;
        $sql_duration = 0;
        if (isset($sql_logs)) {
            foreach ($sql_logs as $data) {
                $sql_duration += floatval($data['duration']);
            }
        }
        $php_duration = $total_duration - $sql_duration;

        $data = [];
        $fields = [];

        $data[] = $debut_script;
        $fields['ts_debut'] = '?';

        $data[] = $fin_script;
        $fields['ts_fin'] = '?';

        if (isset($usager)) {
            $data[] = $usager->id;
            $fields['usager_id'] = '?';

            $data[] = session_id();
            $fields['session_id'] = '?';
        }

        $data[] = $_SERVER['HTTP_USER_AGENT'] ?? '';
        $fields['user_agent'] = '?';

        $data[] = $_SERVER['QUERY_STRING'] ?? '';
        $fields['query_string'] = '?';

        $data[] = round($total_duration, 8);
        $fields['duree_ms'] = '?';

        $data[] = round($sql_duration, 8);
        $fields['duree_sql'] = '?';

        $data[] = round($php_duration, 8);
        $fields['duree_php'] = '?';

        if (isset($_GET['section'])) {
            $data[] = $_GET['section'];
            $fields['section'] = '?';
        }

        if (isset($_GET['module'])) {
            $data[] = $_GET['module'];
            $fields['module'] = '?';
        }

        if (isset($_GET['action'])) {
            $data[] = $_GET['action'];
            $fields['action'] = '?';
        }

        if (isset($_SERVER['REQUEST_METHOD'])) {
            $data[] = $_SERVER['REQUEST_METHOD'];
            $fields['methode'] = '?';
        }

        $data[] = $_SERVER['REMOTE_ADDR'];
        $fields['adresse_ip'] = '?';

        $data[] = isset($noHeader) && $noHeader ? 1 : 0;
        $fields['in_popup'] = '?';

        $projet = getProjetInfo();
        if (isset($projet)) {
            $data[] = $projet->id;
            $fields['projet_id'] = '?';
        }

        $sql = db::prepareQuery('insert', 'logs_performances', $fields);
        $db->query($sql, $data);

        $tempsTotal = round($total_duration, 8);
        if ($tempsTotal > 10 && $tempsTotal < 100000 && isset($sql_logs)) {
            $sujet = 'Page lente ' . round($total_duration, 4) .
                ' (SQL: ' . round($sql_duration, 4) .
                ' -- PHP: ' . round($php_duration, 4) . ')';
            $message = ($_SERVER['QUERY_STRING'] ?? '') . "\n\n<br /><br />" . getDebug($sql_logs);
            if (Config::instance()->isDev) {
//                print $sujet . '<br /><br />' . $message;
            } else {
                envoiCourriel('<EMAIL>', $sujet, $message);
            }
        }
    } catch (Exception $e) {
        envoiCourriel('<EMAIL>', 'Erreur log performance', $e->getMessage());
    }
}
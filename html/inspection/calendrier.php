<?
$_SESSION['selected_onglet'] = isset($_SESSION['selected_onglet']) ? $_SESSION['selected_onglet'] : 'correlations';

include('config/header.inc.php');
?>
    <script>
		$(document).ready(function(){

			$('#calendar').fullCalendar({
				header: {
					left: '',
					center: 'title',
					right: 'prev,next today'
				},
				lang: '<?=($Langue == 'fr' ? 'fr-ca' : 'en-ca')?>',
				events: 'index.php?section=entretien&module=index&action=getPlanificationCalendrier',
				droppable: true,
				defaultView: 'month',
				dayClick: function(date, jsEvent, view) {

					var theMoment = moment(date);

					var date_travaux = theMoment.format('YYYY-MM-DD');

					$.fancybox({
						'overlayShow'		:	false,
						'type'				:	'iframe',
						'modal'				:	true,
						'centerOnScroll'	:	true,
						'width'             :   1024,
						'href'              :	'index.php?section=admin&module=edit&table=procedures_entretiens_planifiees&type=client&id=0&date_travaux=' + date_travaux,
						'onComplete'		:	function(){
							$('#fancybox-frame').load(function() { // wait for frame to load and then gets it's height
								fancybox_resize();
							});
						},
						'onClosed'			:	function(){
							$('html').css({ overflow: 'auto' });
						}
					});

				},
				eventClick: function(calEvent, jsEvent, view) {

					var programme_entretien_planifiee_id = calEvent.programme_entretien_planifiee_id;
					var date_travaux = calEvent.date_travaux;
					var is_projection = calEvent.is_projection;

					$.fancybox({
						'overlayShow'		:	false,
						'type'				:	'iframe',
						'modal'				:	true,
						'centerOnScroll'	:	true,
						'width'             :   1024,
						'href'              :	'index.php?section=admin&module=edit&table=procedures_entretiens_planifiees&type=client&id=' + programme_entretien_planifiee_id + '&date_travaux=' + date_travaux + (is_projection == 1 ? '&is_projection=1' : ''),
						'onComplete'		:	function(){
							$('#fancybox-frame').load(function() { // wait for frame to load and then gets it's height
								fancybox_resize();
							});
						},
						'onClosed'			:	function(){
							$('html').css({ overflow: 'auto' });
						}
					});

				},
				drop: function(date) {

					var originalEventObject = $(this).data('eventObject');
					var copiedEventObject = $.extend({}, originalEventObject);

					var theMoment = moment(date);

					var date_travaux = theMoment.format('YYYY-MM-DD');

					dropped = $(this);
					url = 'index.php?section=entretien&module=index';
					$.get(url, { action: 'changeDateTravaux', date_travaux: date_travaux,
						programme_id: dropped.data('programme_id') }, function(data){

						copiedEventObject.id = data.id;
						$('#calendar').fullCalendar('renderEvent', copiedEventObject, true);
						dropped.remove();

					}, 'json');

				}

				, eventDrop: function(event, delta, revertFunc) {
                    <?
                    if(!havePermission('admin_procedures_entretiens_planifiees'))
                    {
                    ?>
					revertFunc();
					return false;
                    <?
                    }
                    ?>
					if(event.code_statut == 'complete' || event.code_statut == 'ignore' || event.code_statut == 'annule')
					{
						alert('<?=txt('Impossible de déplacer cette tâche car elle a été complétée, annulée ou ignorée.', false)?>');
						revertFunc();
						return false;
					}

					date_travaux = event.start.format('YYYY-MM-DD');
					url = 'index.php?section=entretien&module=index';
					$.get(url, {action: 'changeDateTravaux', date_travaux: date_travaux, id: event.programme_entretien_planifiee_id});

				}
			});

			parent.fit_iframe('calendar_iframe');
		});
    </script>

    <style>
        #programmes div.programme {
            cursor: pointer;
            border-radius: 5px;
            padding: 3px;
            margin-bottom: 2px;
        }

        .fc-content .fc-title, .fc-content .fc-time {
            color: white!important;
        }

        .fc-row .fc-content-skeleton td, .fc-row .fc-helper-skeleton td {
            border-color: #ddd!important;
        }

        .fc-unthemed .fc-today {
            background: #fcf8e3!important;
        }

        table {
            background-image: none!important;
        }
    </style>
    <style>
        #content {
            background-color: #F1F1F1!important;
            background-image: none!important;
            border-color: #ededed;
        }
    </style>
    <h1 class="ficheTitre" style="position: relative;">
        <?=txt('Mise en service - Calendrier')?>
        <img src="css/images/icons/dark/filter.png" style="cursor: pointer; position: relative; top: 4px;" id="filter_btn" />

        <a href="index.php?section=inspection&module=procedures&print=1" style="position: relative; top: 4px;" title="<?=txt("Imprimer")?>" target="iframe">
            <img src="css/images/icons/dark/printer.png" /></a>

        <div style="display: inline-block; position: absolute; right: 10px; top: 12px;  z-index: 1;">
            <?
            include('onglets.inc.php');
            ?>
        </div>
    </h1>
    <div id="ongletContent" style="position: relative;">
        <div style="float: right; margin-left: 40px; position: relative; top: -3px;">
            <?
            $inventaire_id = db::instance()->lastInsertId();
            if ($_SESSION['miseEnServiceMode'] == 'equipementsMEP') {
                ?>
                <a class="btn" href="index.php?section=inventaires&module=index&onglet=inventaire_importe"><?=txt('Équipements MEP importés')?></a>
                <?
            } else {
                ?>
                <a class="btn" href="index.php?section=inventaires&module=index&onglet=inventaire_importe"><?=txt('Non-traités')?></a>
                <?
            }
            if(GestionnaireRoute::instance()->haveAccesRoute('inspection-procedures'))
            {
                ?>
                <a class="btn" href="index.php?section=inspection&module=<?= $_SESSION['miseEnServiceMode'] == 'equipementsGBM' ? 'proceduresGbm' : 'procedures' ?>"><?=txt('Procédures')?></a>
                <?
            }
            if (GestionnaireRoute::instance()->haveAccesRoute('inspection-historique')) {
                ?>
                <a class="btn" href="index.php?section=inspection&module=<?= $_SESSION['miseEnServiceMode'] == 'equipementsGBM' ? 'planificationGbm' : 'historique' ?>"><?=txt('Planification')?></a>
                <?
            }
            if (GestionnaireRoute::instance()->haveAccesRoute('inspection-historique')) {
                ?>
                <a class="btn actif" href="index.php?section=inspection&module=calendrier"><?=txt('Calendrier')?></a>
                <?
            }
            if ($_SESSION['miseEnServiceMode'] == 'equipementsMEP') {
                ?>
                <a class="btn fancy" href="index.php?section=admin&module=edit&table=inventaires&type=client&id=<?= $inventaire_id ?>"><?=txt('Gestion des champs')?></a>
                <a class="btn fancy" href="index.php?section=inventaires_locaux&module=import&id=<?= $inventaire_id ?>"><?=txt('Importation')?></a>
                <?
            }
            ?>
        </div>

        <?=ProcedureEntretienPlanifiee::getLegendeStatuts()?>
        <div>
            <div id="calendar"></div>
        </div>
    </div>
    <iframe id="iframe" name="iframe" style="display: none; width: 100%;"></iframe>

<?
include('config/footer.inc.php');
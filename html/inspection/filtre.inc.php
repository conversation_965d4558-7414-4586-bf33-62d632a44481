<?php
$filtre = &$_SESSION[$key_filtre];
$filtre['fiche_id'] = $filtre['fiche_id'] ?? [];
$filtre['equipement_bio_medicaux_type_id'] = $filtre['equipement_bio_medicaux_type_id'] ?? [];

if (isset($_GET['clear_filtre'])) {
    $sel_inventaire_ligne_ids = $filtre['sel_inventaire_ligne_ids'] ?? [];
    $filtre = [
        'statut_externe' => '',
        'is_actif' => '',
        'est_associe' => '',
        'numero_inventaire' => '',
        'fiche_id' => [],
        'is_editable' => in_array($key_filtre, ['filtre_inventaires_liste_inventaire_importe', 'filtre_inventaires_associations']) ? 0 : 1,
        'inventaire_local_id' => [],
        'equipement_externe' => [],
        'text_search' => '',
        'inventaire_id' => $inventaire_id,
        'sel_inventaire_ligne_ids' => $sel_inventaire_ligne_ids,
        'order_by_col' => $filtre['order_by_col'],
        'order_by_dir' => $filtre['order_by_dir'],
    ];
    $page = 1;
}

if (is_array($filtre['numero_inventaire'])) {
    $filtre['numero_inventaire'] = '';
}

if (in_array($key_filtre, ['filtre_inventaires_liste_demenagements_non_traites', 'filtre_inventaires_liste_demenagements_a_traites'])) {
    $filtre['non_cedules'] = 1;
}

if (isset($_POST['statut_externe'])) {
    $filtre['statut_externe'] = $_POST['statut_externe'];
    $page = 1;
}

if (isset($_POST['is_actif'])) {
    $filtre['is_actif'] = $_POST['is_actif'];
    $page = 1;
}

if (isset($_POST['est_associe'])) {
    $filtre['est_associe'] = $_POST['est_associe'];
    $page = 1;
}

if (isset($_POST['numero_inventaire'])) {
    $filtre['numero_inventaire'] = $_POST['numero_inventaire'];
    $page = 1;
}

if (isset($_POST['fiche_id'])) {
    $filtre['fiche_id'] = $_POST['fiche_id'];
    $page = 1;
} else if (isset($_POST['filtrer'])) {
    $filtre['fiche_id'] = [];
    $page = 1;
}

if (isset($_POST['inventaire_local_id'])) {
    $filtre['inventaire_local_id'] = $_POST['inventaire_local_id'];
    $page = 1;
} else if (isset($_POST['filtrer'])) {
    $filtre['inventaire_local_id'] = [];
    $page = 1;
}

if (isset($_POST['equipement_externe'])) {
    $filtre['equipement_externe'] = $_POST['equipement_externe'];
    $page = 1;
} else if (isset($_POST['filtrer'])) {
    $filtre['equipement_externe'] = [];
    $page = 1;
}

if (isset($_POST['text_search'])) {
    $filtre['text_search'] = $_POST['text_search'];
    $page = 1;
}

if (isset($_GET['order_by_col']) && isset($_GET['order_by_dir'])) {
    $filtre['order_by_col'] = $_GET['order_by_col'];
    $filtre['order_by_dir'] = $_GET['order_by_dir'];
    $page = 1;
}

if (isset($ids_to_exclude)) {
    $filtre['ids_to_exclude'] = $ids_to_exclude;
}

$filtre['key_to_use'] = 'index';

if (!isset($doNotDisplayFiltre) || !$doNotDisplayFiltre) {
    ?>
    <style>
        #inventaires td {
            text-align: left;
            padding: 0px 5px;
        }

        #inventaires th {
            text-weight: bold;
            text-align: left;
        }

        select {
            width: 130px;
        }

        tr.associe_contexte td {
            background-color: aquamarine;
        }

        tr.associe td {
            background-color: lightcyan;
        }

        tr.non_associe_inactif_externe td {
            background-color: lightgoldenrodyellow;
        }

        tr.is_obsolete td {
            background-color: #fe94174f !important;
        }

        tr.associe_inactif_externe td {
            background-color: pink;
        }

        .select2-container {
            position: relative;
            top: -2px;
        }

        .select2-search-choice-close {
            position: relative;
            top: -2px;
        }
    </style>
    <script>
        $(document).ready(function () {
			$('#fiche_id').dynamicDmMultiSelect({
				listToGet: 'Fiches',
				selectedIds: <?= json_encode($filtre['fiche_id']) ?>,
				filtre: { fiche_sous_type_id: 2000002 },
				placeholder: <?= json_encode(txt('- Locaux -', false)) ?>
			});

			$('#equipement_bio_medicaux_type_id').dynamicDmMultiSelect({
				listToGet: 'EquipementsBioMedicauxTypes',
				selectedIds: <?= json_encode($filtre['equipement_bio_medicaux_type_id']) ?>,
				filtre: { with_description: 1 },
				placeholder: '<?= txt('- Équipements -') ?>'
			});
        });
    </script>
    <form action="<?= $url ?>" method="post" id="form">

        <span style="margin-left: 10px;">
            <select id="fiche_id" name="fiche_id" style="width: 200px;">
            </select>
        </span>

        <span style="margin-left: 10px;">
            <select id="equipement_bio_medicaux_type_id" name="equipement_bio_medicaux_type_id" style="width: 200px;">
            </select>
        </span>

        <span style="margin-left: 20px; text-align: right; width: 200px;">
            <input type="submit" id="filtrer" class="filtrer" name="filtrer" value="<?= txt('Filtrer') ?>" style="min-height: 20px!important; padding: 0 20px;"/>
            <a href="<?= $url ?>&clear_filtre=1" id="clear_filtre"
               style="position: relative; top: 8px; padding: 0 10px;"
               title="<?php
               echo txt('Libérer tous les filtres'); ?>">
                <img src="css/images/icons/dark/bended_arrow_left.png"/></a>
        </span>

    </form>
    <?php
}
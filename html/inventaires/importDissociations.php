<?php

$etape = 'valider';
if (isset($_POST['valider'])) {
    $etape = 'synchroniser';
} else if (isset($_POST['synchroniser'])) {
    $etape = 'importer';
}

$noHeader = 1;
include('config/header.inc.php');

$inventaire = new Inventaires(intval($_GET['id']));
$importation = new ImportationInventairesDissociations($etape, $inventaire);
if ($etape == 'synchroniser') {
    $importation->traiteFichierExcel();
} else if($etape == 'importer') {
    $lignes_to_import = $importation->getDonneesImportation();
    $importation->importation($lignes_to_import);
}
$importation->afficheFormulaire();

include('config/footer.inc.php');
<?
if (isset($_POST['save'])) {
    PopupEdition::traiteDocumentsMobiles();
}
$noHeader = 1;
include('config/header.inc.php');
?>
    <script>
		$(document).ready(function(){
			$('#ajoutImage').click(function(){
				$('#ajoutImageContainer').append('<input type="file" name="images[]" accept="image/gif, image/jpeg, image/png" capture />');
				window.parent.fancybox_resize();
			});
        });
    </script>
    <div id="popupContent" class="edit_popupContent">
        <form action="index.php?section=inventaires&module=photos&fromMobile=inventaire&id=<?= (int)$_GET['id'] ?>" method="post" enctype="multipart/form-data">
            <label><?= txt('Gestion des photos') ?></label>
            <fieldset style="padding: 0 0 0 20px;">
                <div id="editables" style="padding: 12px 5px 0px 15px; position: relative;">
                    <fieldset>
                        <?
                        DocumentsListesElements::editeurPhotos('inventaires_lignes', (int)$_GET['id']);
                        ?>
                    </fieldset>
                    <input type="hidden" name="inventaire_ligne_id" value="<?= (int)$_GET['id'] ?>" />
                    <div id="ajoutImageContainer" style="max-width: 300px;">
                    </div>
                </div>
                <div style="text-align: right; width: 98%!important;">
                    <button type="button" name="ajoutImage" id="ajoutImage"><?= txt('Ajouter une image') ?></button>
                    <button class="submit" value="submitbuttonvalue" name="save" id="save"><?= txt('Sauvegarder') ?></button>
                    <button onclick="$('#popupContent').css( 'min-width', '600px' ); parent.$.fancybox.close();return false;" name="close"><?=txt('Fermer')?></button>
                </div>
            </fieldset>
        </form>
    </div>
<?
include('config/footer.inc.php');
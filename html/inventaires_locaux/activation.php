<?
if (isset($_POST['save'])) {
    $inventaire = Inventaires::getInventaireCourant();
    $sql = "update inventaires_locaux set is_mobile = 0 where inventaire_id = ?";
    db::instance()->query($sql, [$inventaire->id]);
    if (!empty($_POST['inventaire_local_id'])) {
        $sql = "update inventaires_locaux set is_mobile = 1 where inventaire_id = ? and id in (" . db::placeHolders($_POST['inventaire_local_id']) . ")";
        db::instance()->query($sql, array_merge([$inventaire->id], $_POST['inventaire_local_id']));
    }
    ?>
    <script>
		parent.window.location.reload();
    </script>
    <?php
    die();
}
$noHeader = 1;
include('config/header.inc.php');

$key_filtre = 'filtre_inventaires_locaux_activation';
$filtre = &$_SESSION[$key_filtre];

$inventaire = Inventaires::getInventaireCourant();
$champsLocaux = $inventaire->getChampsLocauxActifs();
foreach ($inventaire->locauxChamps as $champ) {
    if (in_array($champ->id, $champsLocaux)) {
        $filtre['filtresCustom_' . $champ->id] = $filtre['filtresCustom_' . $champ->id] ?? [];
    } else {
        unset($filtre['filtresCustom_' . $champ->id]);
    }
}

if (isset($_GET['clear_filtre'])) {
    foreach ($inventaire->locauxChamps as $champ) {
        if (in_array($champ->id, $champsLocaux)) {
            $filtre['filtresCustom_' . $champ->id] = [];
        }
    }
}

foreach ($inventaire->locauxChamps as $champ) {
    if (in_array($champ->id, $champsLocaux)) {
        if (isset($_POST['filtresCustom_' . $champ->id])) {
            $filtre['filtresCustom_' . $champ->id] = $_POST['filtresCustom_' . $champ->id];
        } else if (isset($_POST['filtrer'])) {
            $filtre['filtresCustom_' . $champ->id] = [];
        }
    }
}
?>

    <style>
        table, table thead tr, table tfoot tr, table th {
            background-image: none;
        }
        td {
            border: solid 0px white!important;
            text-align: left!important;
            padding: 0px!important;
        }
    </style>
    <script>
        $(document).ready(function(){
			$('select.champ_multiple').each(function(){
				DmMultiSelector.initFromSelect(`#${this.id}`);
			});
        });
    </script>
    <style>
        div.combowrap:first-child ul.comboselect {
            height: 85%!important;
        }
        div.comboselectbox div.combowrap {
            width: 350px!important;
        }
        .edit_popupContent section > div {
            width: 889px!important;
            float: left;
            margin: 4px 0 5px 0;
            padding: 0;
        }
        div.comboselectbox ul.comboselect {
            overflow-x: hidden;
        }
    </style>
    <div id="popupContent" class="edit_popupContent">
        <form action="index.php?section=inventaires_locaux&module=activation" id="formFiltre" method="post">
            <label><?= txt('Activation des locaux d\'inventaire') ?></label>
            <div style="display: table; padding: 25px 25px 0 40px">
                <div class="div_tr">
                    <?
                    $valeurs = InventairesLocaux::getListeValeursDistinctParChamp([ 'inventaire_id' => $inventaire->id ]);
                    $iter = 1;
                    foreach ($inventaire->locauxChamps as $champ) {
                        if (in_array($champ->id, $champsLocaux)) {
                            ?>
                            <div class="div_td">
                                <select id="filtresCustom_<?= $champ->id ?>" name="filtresCustom_<?= $champ->id ?>" style="width: 225px;" class="filtre champ_multiple" multiple data-placeholder="- <?= $champ->nom ?> -">
                                    <?
                                    if (isset($valeurs[$champ->id])) {
                                        foreach ($valeurs[$champ->id] as $valeur) {
                                            $sel = in_array($valeur, $filtre['filtresCustom_' . $champ->id]) ? ' selected="selected"' : '';
                                            ?>
                                            <option value="<?= $valeur ?>"<?= $sel ?>><?= $valeur ?></option>
                                            <?
                                        }
                                    }
                                    ?>
                                </select>
                            </div>
                            <?
                            if ($iter++ % 4 == 0) {
                            ?>
                                </div>
                                <div class="div_tr">
                            <?
                            }
                        }
                    }
                    ?>

                    <div class="div_td">
                        <input type="submit" id="filtrer" class="filtrer" name="filtrer" value="<?=txt('Filtrer')?>" />
                        <a href="index.php?section=inventaires_locaux&module=activation&clear_filtre=1" id="clear_filtre" style="display: inline!important; position: relative; top: 10px;"
                           title="<?php echo txt('Libérer tous les filtres'); ?>">
                            <img src="css/images/icons/dark/bended_arrow_left.png" /></a>
                    </div>
                </div>
            </div>
        </form>
        <form action="index.php?section=inventaires_locaux&module=activation" method="post" enctype="multipart/form-data">
            <fieldset style="padding: 0 0 0 20px;">
                <div id="editables" style="padding: 12px 5px 0px 15px; position: relative;">
                    <fieldset>
                        <div id="sidesSelector" style="width: 600px"></div>
                    </fieldset>
                </div>
                <div style="text-align: right; width: 98%!important;">
                    <button class="submit" value="submitbuttonvalue" name="save" id="save"><?= txt('Sauvegarder') ?></button>
                    <button onclick="$('#popupContent').css( 'min-width', '600px' ); parent.$.fancybox.close();return false;" name="close"><?=txt('Fermer')?></button>
                </div>
            </fieldset>
        </form>
    </div>

    <script>
		$(document).ready(function(){
			<?
            $filtre['orIsMobile'] = 1;
            $locaux = InventairesLocaux::getListe($filtre);
            $allLocaux = [];
            $locauxActifs = [];
            foreach ($locaux as $local) {
                $allLocaux[] = [
                    'id' => $local->id,
                    'text' => $local->numero_local,
                ];
                if ($local->is_mobile == 1) {
                    $locauxActifs[] = $local->id;
                }
            }
            ?>
			DmSidesSelector.addNew('#sidesSelector', 'inventaire_local_id', '<?= txt('Locaux disponibles') ?>', '<?= txt('Locaux actifs') ?>', <?= json_encode($allLocaux) ?>, <?= json_encode($locauxActifs) ?>);
		});
    </script>
<?
include('config/footer.inc.php');
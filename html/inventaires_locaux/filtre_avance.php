<?
$key_filtre = $_GET['key_filtre_avance'];
$key_filtre_applique = $key_filtre . '_applique';

$inventaire = Inventaires::getInventaireCourant();
$inventaire_id = $inventaire->id;

$filtre = &$_SESSION[$key_filtre];
$filtre['inventaire_id'] = $inventaire_id;

$champsLocaux = $inventaire->getChampsLocauxActifs();
foreach ($inventaire->locauxChamps as $champ) {
    if (in_array($champ->id, $champsLocaux)) {
        $filtre['filtresCustom_' . $champ->id] = $filtre['filtresCustom_' . $champ->id] ?? [];
    } else {
        unset($filtre['filtresCustom_' . $champ->id]);
    }
}

if (!isset($_POST['liberer']) && !isset($_POST['modele_selectionne']) && !isset($_POST['delete'])) {
    if (isset($_SESSION[$key_filtre_applique]) && $_SESSION[$key_filtre_applique] > 0) {
        $sql = "select * from filtres_sauvegardes where id = ?";
        $filtre_sauvegarde_sel = db::instance()->getRow($sql, array(intval($_SESSION[$key_filtre_applique])));

        $filtre = json_decode($filtre_sauvegarde_sel->getRaw('json_data'), true);
    }
}

if (isset($_POST['delete']) && intval($_POST['update_filtre']) > 0) {
    $sql = "delete from filtres_sauvegardes where id = ?";
    db::instance()->query($sql, array(intval($_POST['update_filtre'])));

    $filtre = [];
} else if (isset($_POST['modele_selectionne'])) {
    if (intval($_POST['modele_selectionne']) > 0) {
        $sql = "select * from filtres_sauvegardes where id = ?";
        $filtre_sauvegarde_sel = db::instance()->getRow($sql, array(intval($_POST['modele_selectionne'])));
        $filtre = json_decode($filtre_sauvegarde_sel->getRaw('json_data'), true);
    } else {
        $filtre = [];
    }
} else if (isset($_POST['liberer'])) {
    $filtre = [
        'inventaire_id' => $inventaire_id,
    ];

    foreach ($inventaire->locauxChamps as $champ) {
        if (in_array($champ->id, $champsLocaux)) {
            $filtre['filtresCustom_' . $champ->id] = [];
        }
    }

    unset($_SESSION[$key_filtre_applique]);
} else if (isset($_POST['appliquer']) || isset($_POST['nom_sauvegarde'])) {
    foreach ($inventaire->locauxChamps as $champ) {
        if (in_array($champ->id, $champsLocaux)) {
            if (isset($_POST['filtresCustom_' . $champ->id])) {
                $filtre['filtresCustom_' . $champ->id] = $_POST['filtresCustom_' . $champ->id];
            } else if (isset($_POST['filtrer'])) {
                $filtre['filtresCustom_' . $champ->id] = [];
            }
        }
    }

    $filtre_global = 1;
    $is_exit_popup = true;
    $is_erreur = false;
    if (isset($_POST['update_filtre'])) {
        $is_exit_popup = false;
        if (!InventairesLocaux::isFiltreActif($filtre)) {
            setErreur(txt('Le modèle ne sera pas sauvegardé. Aucun filtre n\'a été sélectionné.'));
            $is_erreur = true;
        }

        if (!$is_erreur) {
            $filtres_sauvegardes = $filtre;

            db::instance()->autocommit(false);
            $sql = "update filtres_sauvegardes set json_data = ?, ts_modif = current_timestamp, usager_modif_id = ?, nom = ? where id = ?";
            db::instance()->query($sql, array(json_encode($filtres_sauvegardes), getIdUsager(), $_POST['nom_sauvegarde'], intval($_POST['update_filtre'])));
            db::instance()->commit();

            $sql = "select * from filtres_sauvegardes where id = ?";
            $filtre_sauvegarde_sel = db::instance()->getRow($sql, array(intval($_POST['update_filtre'])));

            $_SESSION[$key_filtre_applique] = isset($_POST['update_filtre']) ? intval($_POST['update_filtre']) : 0;

            $is_exit_popup = true;
        }
    } else if (isset($_POST['nom_sauvegarde'])) {
        $is_exit_popup = false;
        if (dm_strlen($_POST['nom_sauvegarde']) == 0) {
            setErreur(txt('Le modèle ne sera pas sauvegardé. Vous devez entrer un nom pour ce modèle.'));
            $is_erreur = true;
        }

        if (!InventairesLocaux::isFiltreActif($filtre)) {
            setErreur(txt('Le modèle ne sera pas sauvegardé. Aucun filtre n\'a été sélectionné.'));
            $is_erreur = true;
        }

        if (!$is_erreur) {
            $filtres_sauvegardes = $filtre;

            db::instance()->autocommit(false);
            $sql = "insert into filtres_sauvegardes (ouvrage_id, nom, json_data, ts_ajout, usager_ajout_id, type) values (?, ?, ?, current_timestamp, ?, 'inventaires_locaux')";
            db::instance()->query($sql, array(getOuvrageId(), $_POST['nom_sauvegarde'], json_encode($filtres_sauvegardes), getIdUsager()));
            $id_added = db::instance()->lastInsertId();
            db::instance()->commit();

            $_SESSION[$key_filtre_applique] = $id_added;

            $is_exit_popup = true;
        }
    }

    if (!$is_erreur && $is_exit_popup) {
        ?>
        <script src="js/general.functions.js?ts=<?=filemtime('js/general.functions.js')?>"></script>
        <script>
            var url = updateUrlParameter(parent.window.location.href, 'page', 1);
            url = updateUrlParameter(url, 'filtre_global', <?=$filtre_global?>);
            parent.$.fancybox.close();
            parent.window.location = url;
        </script>
        <?
        exit;
    }
}

$noHeader = true;
include('config/header.inc.php');
?>
    <script>
		$(document).ready(function(){
			$('select[multiple]').each(function(){
				DmMultiSelector.initFromSelect(`#${this.id}`);
			});

			$(document).on('click', '#save', function () {
                $.prompt('<?=txt('Inscrivez le nom de ce nouveau modèle:')?>', '', function (value) {
                    $('#formFiltre').append('<input type="hidden" id="nom_sauvegarde" name="nom_sauvegarde" value="' + value + '" />');
                    $('#formFiltre').submit();
                });
			});

			$(document).on('change', '#selectionner_modele', function () {
				$('#formFiltre').append('<input type="hidden" name="modele_selectionne" value="' + this.value + '" />');
				$('#formFiltre').submit();
			});
		});
    </script>
    <div id="popupContent">
        <form action="index.php?section=inventaires_locaux&module=filtre_avance&key_filtre_avance=<?= $_GET['key_filtre_avance'] ?>" id="formFiltre" method="post" style="margin-bottom: 25px;">
            <label><?= txt('Filtres avancés') ?></label>
            <fieldset style="min-height: 175px; padding: 10px 0 0 10px;">
                <?
                erreurs();
                messages();
                ?>
                <div id="editables" style="padding: 12px 5px 0px 15px; position: relative; min-height: 350px">
                    <?
                    if (isset($filtre_sauvegarde_sel)) {
                        ?>
                        <h4 style="margin-bottom: 10px;">
                            <?= txt('Nom du modèle') ?>:<br/>
                            <input type="text" name="nom_sauvegarde"
                                   value="<?= ($filtre_sauvegarde_sel['nom']) ?>"
                                   style="width: 50%!important;"/>
                        </h4>
                        <?
                    }
                    ?>
                    <div style="display: table">
                        <div class="div_tr">
                            <?
                            $valeurs = InventairesLocaux::getListeValeursDistinctParChamp([ 'inventaire_id' => $inventaire->id ]);
                            $iter = 1;
                            foreach ($inventaire->locauxChamps as $champ) {
                                if (in_array($champ->id, $champsLocaux)) {
                                    ?>
                                    <div class="div_td">
                                        <select id="filtresCustom_<?= $champ->id ?>" name="filtresCustom_<?= $champ->id ?>" style="width: 200px;" class="filtre champ_multiple" multiple data-placeholder="- <?= $champ->nom ?> -">
                                            <?
                                            if (isset($valeurs[$champ->id])) {
                                                foreach ($valeurs[$champ->id] as $valeur) {
                                                    $sel = in_array($valeur, $filtre['filtresCustom_' . $champ->id]) ? ' selected="selected"' : '';
                                                    ?>
                                                    <option value="<?= $valeur ?>"<?= $sel ?>><?= $valeur ?></option>
                                                    <?
                                                }
                                            }
                                            ?>
                                        </select>
                                    </div>
                                    <?
                                    if ($iter++ % 4 == 0) {
                                    ?>
                                        </div>
                                        <div class="div_tr">
                                    <?
                                    }
                                }
                            }
                            ?>
                        </div>
                    </div>
                </div>

                <div style="text-align: right; width: 98%!important;margin-bottom: 10px; margin-top: 25px;">
                    <select id="selectionner_modele" name="selectionner_modele" style="float: left; margin-left: 15px;">
                        <option value=""><?= txt('- Sélectionner un modèle à appliquer -') ?></option>
                        <?
                        $sql = "select * from filtres_sauvegardes where ouvrage_id = ? and type = 'inventaires_locaux' order by nom";
                        $filtres_sauvegardes = db::instance()->getAll($sql, array(getOuvrageId()));

                        foreach ($filtres_sauvegardes as $filtre_sauvegarde) {
                            $sel = isset($filtre_sauvegarde_sel['id']) && $filtre_sauvegarde['id'] == $filtre_sauvegarde_sel['id'] ? ' selected="selected"' : '';
                            ?>
                            <option value="<?= $filtre_sauvegarde['id'] ?>"<?= $sel ?>><?= $filtre_sauvegarde['nom'] ?></option>
                            <?
                        }
                        ?>
                    </select>
                    <?
                    if (isset($filtre_sauvegarde_sel)) {
                        ?>
                        <button name="save" class="appliquer_filtre" type="submit"
                                style="color: green;"><?= txt('Enregistrer et appliquer le modèle') ?></button>
                        <button name="appliquer" class="appliquer_filtre" type="submit"
                                style="color: blue;"><?= txt('Appliquer') ?></button>
                        <button id="delete" name="delete" type="submit" style="color: red;"
                                onclick="return confirm('<?= txt('Êtes-vous certain de vouloir supprimer ce modèle?') ?>');"><?= txt('Supprimer le modèle') ?></button>
                        <input type="hidden" name="update_filtre" value="<?= $filtre_sauvegarde_sel['id'] ?>"/>
                        <?
                    } else {
                        ?>
                        <button id="save" name="save" type="button"
                                style="color: green;"><?= txt('Enregistrer et appliquer le modèle') ?></button>
                        <button name="appliquer" class="appliquer_filtre" type="submit"
                                style="color: blue;"><?= txt('Appliquer') ?></button>
                        <?
                    }
                    ?>
                    <button name="liberer" type="submit" style="color: orange;"><?= txt('Remettre à zéro') ?></button>
                    <button onclick="parent.$.fancybox.close();return false;" name="cancel"
                            style="color: red;"><?= txt('Annuler') ?></button>
                </div>
            </fieldset>
        </form>
    </div>
<?
include('config/footer.inc.php');
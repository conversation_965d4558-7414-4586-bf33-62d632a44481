class DmSidesSelector {
	constructor(options) {
		this.selector = options.selector;
		this.name = options.name;
		this.libelleGauche = options.libelleGauche;
		this.libelleDroit = options.libelleDroit;
		this.list = options.list;
		this.selected = options.selected;
	}

	static addNew(selector, name, libelleGauche, libelleDroit, list, selected) {
		const multi = new DmSidesSelector({ selector, name, libelleGauche, libelleDroit, list, selected });
		multi.init();
	}

	init() {
		const _that = this;
		document.querySelector(_that.selector).innerHTML = `
			<table>
				<tr>
					<td style="width: 43%; vertical-align: top!important;">
						<b style="font-weight: bold; display: inline-block; width: 100%; margin-bottom: 10px;">&nbsp;
							<span id="nbGauche" style="color: blue;font-weight: bold;"></span>&nbsp;${_that.libelleGauche}
						</b>
						<input type="text" id="codeFiche" value="" autocomplete="off" style="min-width: 100%!important;" placeholder="Recherche..."/><br />
						<select name="from" id="liste" class="form-control" size="8" multiple="multiple" style="min-height: 330px!important; width: 100%; min-width: auto!important;"></select>
					</td>
					<td style="width: 14%; text-align: center!important; vertical-align: top; padding-top: 70px!important;">
						<span class="fas fa-angle-double-right" id="liste_rightAll"
						  	style="font-size: 20px; cursor: pointer; opacity: 0.7; width: 40px; margin-bottom: 10px;"></span><br/>
						<span class="fas fa-angle-right" id="liste_rightSelected"
							style="font-size: 20px; cursor: pointer; opacity: 0.7; width: 40px; margin-bottom: 10px;"></span><br/>
						<span class="fas fa-angle-left" id="liste_leftSelected"
							style="font-size: 20px; cursor: pointer; opacity: 0.7; width: 40px; margin-bottom: 10px;"></span><br/>
						<span class="fas fa-angle-double-left" id="liste_leftAll"
							style="font-size: 20px; cursor: pointer; opacity: 0.7; width: 40px; margin-bottom: 10px;"></span>
					</td>
					<td style="width: 43%; vertical-align: top!important; position: relative">
						<b style="font-weight: bold; display: inline-block; width: 100%; margin-bottom: 10px;">
							<span id="nbDroite" style="color: blue;font-weight: bold;"></span>&nbsp;${_that.libelleDroit}
						</b>
						<div style="height: 30px;">
							&nbsp;
						</div>
						<select id="liste_to" class="form-control" size="8" multiple="multiple" name="${_that.name}[]" style="min-height: 330px!important; font-size: 11px; width: 100%; min-width: auto!important;"></select>
					</td>
				</tr>
			</table>`;

		let bufferLeft = '';
		let bufferRight = '';
		$.each(_that.list, function (i, data) {
			if (_that.selected.includes(data.id)) {
				bufferRight += `<option value="${data.id}">${data.text}</option>`;
			} else {
				bufferLeft += `<option value="${data.id}">${data.text}</option>`;
			}
		});
		$('#liste').html(bufferLeft);
		$('#liste_to').html(bufferRight);

		setTimeout(function () {
			_that.recomptage();
			$('#liste').multiselect({
				submitAllLeft: false,
				submitAllRight: true,
				beforeMoveToRight: function ($left, $right, $options) {
					if ($options.length > 100) {
						return confirm('Êtes-vous certain de vouloir associer tous ces éléments?\n\nL\'opération prendra plusieurs secondes à se compléter si le volume est important.');
					} else if ($options.length == 0) {
						alert('Veuillez d\'abord sélectionner un ou plusieurs éléments à associer.');
						return false;
					}
					return true;
				},
				beforeMoveToLeft: function ($left, $right, $options) {
					if ($options.length > 100) {
						return confirm('Êtes-vous certain de vouloir dissocier tous ces éléments?\n\nL\'opération prendra plusieurs secondes à se compléter si le volume est important.');
					} else if ($options.length == 0) {
						alert('Veuillez d\'abord sélectionner un ou plusieurs éléments à dissocier.');
						return false;
					}
					return true;
				},
				afterMoveToLeft: function ($left, $right, $options) {
					_that.recomptage();
				},
				afterMoveToRight: function ($left, $right, $options) {
					_that.recomptage();
				}
			});
		}, 100);
	}

	recomptage() {
		$('#nbGauche').html($('#liste').find('option').length);
		$('#nbDroite').html($('#liste_to').find('option').length);
	}
}
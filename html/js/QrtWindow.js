class QrtWindow {
	constructor(fiche_id, nom_table, liste_item_id, classe_formulaire, discipline_id) {

		let _that = this;
		_that.fiche_id = fiche_id;
		_that.nom_table = nom_table;
		_that.liste_item_id = liste_item_id;
		_that.classe_formulaire = classe_formulaire;
		_that.discipline_id = discipline_id;
		_that.qrts = [];
		_that.init();
	}

	init() {

		let _that = this;
		_that.fenetre_discussion = $('<fenetre_discussion><header></header><discussion></discussion><footer></footer></fenetre_discussion>');

		$.get('index.php', {
			action: 'get_qrts',
			fiche_id: _that.fiche_id,
			nom_table: _that.nom_table,
			liste_item_id: _that.liste_item_id,
			classe_formulaire: _that.classe_formulaire,
			discipline_id: _that.discipline_id
		}, function(qrts){
			_that.qrts = qrts;
			_that._bindDomEvents();
		}, 'json');
	}


}
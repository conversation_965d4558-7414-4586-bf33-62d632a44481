$(document).ready(function () {

	/*----------------------------------------------------------------------*/
	/* Parse the data from an data-attribute of DOM Elements
	/*----------------------------------------------------------------------*/


	$.parseData = function (data, returnArray) {
		if (/^\[(.*)\]$/.test(data)) { //array
			data = data.substr(1, data.length - 2).split(',');
		}
		if (returnArray && !$.isArray(data) && data != null) {
			data = Array(data);
		}
		return data;
	};
	
	/*----------------------------------------------------------------------*/
	/* Image Preloader
	/* http://engineeredweb.com/blog/09/12/preloading-images-jquery-and-javascript
	/*----------------------------------------------------------------------*/
	


	// Arguments are image paths relative to the current page.
	$.preload = function() {
		var cache = [],
			args_len = arguments.length;
		for (var i = args_len; i--;) {
			var cacheImage = document.createElement('img');
			cacheImage.src = arguments[i];
			cache.push(cacheImage);
		}
	};
	
	
	/*----------------------------------------------------------------------*/
	/* fadeInSlide by revaxarts.com
	/* Fades out a box and slide it up before it will get removed
	/*----------------------------------------------------------------------*/


	$.fn.fadeInSlide = function (speed, callback) {
		if ($.isFunction(speed)) callback = speed;
		if (!speed) speed = 200;
		if (!callback) callback = function () {};
		this.each(function () {

			var $this = $(this);
			$this.fadeTo(speed / 2, 1).slideDown(speed / 2, function () {
				callback();
			});
		});
		return this;
	};
	
    $.fn.replaceTagName = function(replaceWith) {
        var tags = [],
            i    = this.length;
        while (i--) {
            var newElement = document.createElement(replaceWith),
                thisi      = this[i],
                thisia     = thisi.attributes;
            for (var a = thisia.length - 1; a >= 0; a--) {
                var attrib = thisia[a];
                newElement.setAttribute(attrib.name, attrib.value);
            };
            newElement.innerHTML = thisi.innerHTML;
            //$(thisi).after(newElement).remove();
			thisi.parentNode.replaceChild(newElement, thisi);
            tags[i] = newElement;
        }
        return $(tags);
    };
	
	/*----------------------------------------------------------------------*/
	/* fadeOutSlide by revaxarts.com
	/* Fades out a box and slide it up before it will get removed
	/*----------------------------------------------------------------------*/


	$.fn.fadeOutSlide = function (speed, callback) {
		if ($.isFunction(speed)) callback = speed;
		if (!speed) speed = 200;
		if (!callback) callback = function () {};
		this.each(function () {

			var $this = $(this);
			$this.fadeTo(speed / 2, 0).slideUp(speed / 2, function () {
				$this.remove();
				callback();
			});
		});
		return this;
	};
	
	/*----------------------------------------------------------------------*/
	/* textFadeOut by revaxarts.com
	/* Fades out a box and slide it up before it will get removed
	/*----------------------------------------------------------------------*/


	$.fn.textFadeOut = function (text, delay, callback) {
		if (!text) return false;
		if ($.isFunction(delay)) callback = delay;
		if (!delay) delay = 2000;
		if (!callback) callback = function () {};
		this.each(function () {

			var $this = $(this);
			$this.stop().text(text).show().delay(delay).fadeOut(1000,function(){
				$this.text('').show();
				callback();
			})
		});
		return this;
	};
	
	/*----------------------------------------------------------------------*/
	/* leadingZero by revaxarts.com
	/* adds a leding zero if necessary
	/*----------------------------------------------------------------------*/
	
	
	$.leadingZero = function (value) {
		value = parseInt(value, 10);
		if(!isNaN(value)) {
			(value < 10) ? value = '0' + value : value;
		}
		return value;
	};
    
	$.fn.forceInteger = function () {
		return this.each(function () {

			$(document).on('keypress', 'input.justInteger', function() {
                
				if(event.which == 8 || event.which == 0){
					return true;
				}
                else if(event.which == 45 && this.selectionStart == 0){
                    return true;
                }
				if(event.which < 47 || event.which > 59) {
					event.preventDefault();
					//event.preventDefault();
				} // prevent if not number/dot
                
			});
		});
	};

	$.fn.forceQuantity = function () {
		return this.each(function () {
			$(document).on('keypress', 'input.justQuantity', function() {
				if(event.which == 8 || event.which == 0){
					return true;
				}
				if(event.which < 47 || event.which > 59) {
					event.preventDefault();
				}
			});
		});
	};

	$.fn.forceFloat = function () {
    return this.each(function () {
        $(document).on('keypress', 'input.justFloat', function(event) {
            const maxDecimals = $(this).data('maxdecimals') ? parseInt($(this).data('maxdecimals')) : Infinity;
            const value = $(this).val();
            const decimalCount = value.split('.')[1] ? value.split('.')[1].length : 0;

            if(event.which == 8 || event.which == 0){
                return true;
            }
            else if(event.which == 45 && this.selectionStart == 0){
                return true;
            }
            if(event.which < 46 || event.which > 59 || event.which == 47) {
                event.preventDefault();
            }
            if(event.which == 46 && value.indexOf('.') != -1) {
                event.preventDefault();
            }
            if(event.which >= 48 && event.which <= 57 && decimalCount >= maxDecimals) {
                event.preventDefault();
            }
        });
    });
};

	$.fn.optionShow = function() {
		this.each(function(index, option) {
			// Remove <span> to make it compatible with IE
			if($(option).parent().is('span')) {
				$(option).parent().replaceWith(option);
			}

			$(option).show();
		});

		return this;
	};

	$.fn.optionHide = function() {
		this.each(function(index, option) {
			// Wrap with <span> to make it compatible with IE
			if(!$(option).parent().is('span')) {
				$(option).wrap('<span>').hide();
			}
		});

		return this;
	};

	$.fn.limitTextarea = function(limit) {
		this.each(function(index, option) {
			const countDiv = document.createElement('div');
			$(this).parent().append(countDiv);

			const setCountDiv = (obj) => {
				const nbTabs = parseInt(obj.value.split(/\t/).length) == 1 ? 0 : (parseInt(obj.value.split(/\t/).length)-1);
				const nbLignes = parseInt(obj.value.split(/\n/).length) == 1 ? 0 : (parseInt(obj.value.split(/\n/).length)-1);
				const length =  nbTabs + nbLignes + parseInt(obj.value.length) - 1;
				countDiv.style.color = length > limit ? 'red' : 'black';
				countDiv.style.fontWeight = 'bold';
				countDiv.style.float = 'right';
				countDiv.style.margin = '0 40px 0 0';
				countDiv.innerHTML = `${ length } / ${ limit }`;
				obj.style.border = length > limit ? 'solid 1px red' : '';
			};

			setCountDiv(this);
			this.onkeyup = (e) => {
				setCountDiv(e.target);
			};
		});

		return this;
	};

	jQuery.expr[':'].contains = function(a, i, m) {
	  return jQuery(a).text().toUpperCase()
		  .indexOf(m[3].toUpperCase()) >= 0;
	};
});

var delay = (function(){
	var timer = 0;
	return function(callback, ms){
		clearTimeout (timer);
		timer = setTimeout(callback, ms);
	};
})();

$.startObjectLoader = function($obj, obj_id, grosseur_image = '') {

	if($obj.length && !$('#' + obj_id).length && $obj.is(":visible")) {
		var pos = $obj.offset();
		$('body').append('<div id="' + obj_id + '" class="loaderObj' + grosseur_image + '" style="top: ' + pos.top + 'px; left: ' + pos.left + 'px; width: ' + $obj.outerWidth() + 'px; height: ' + $obj.outerHeight() + 'px; z-index: 302;"></div>');
	}

};

$.stopObjectLoader = function(obj_id) {

	$('#' + obj_id).remove();

};

function inIframe () {
    try {
        return window.self !== window.top;
    } catch (e) {
        return true;
    }
}

function fancybox_resize(width_hardocded, do_not_exp_tables = false)
{
    if(inIframe())
    {
        $( "#fancybox-frame" ).contents().find('#popupContent').css( 'min-width', '775px' );
		if (!do_not_exp_tables) {
			$( "#fancybox-frame" ).contents().find('#editables table').css( 'width', '100%' );
			$( "#fancybox-frame" ).contents().find('#editables table').css( 'margin', 'auto' );
		}
    }
    
    if(typeof(width_hardocded) !== 'undefined' && width_hardocded)
    {
        $( "#fancybox-frame" ).contents().find('#popupContent').css( 'min-width', width_hardocded + 'px' );
		if (!do_not_exp_tables) {
			$( "#fancybox-frame" ).contents().find('#editables table').css( 'width', '100%' );
			$( "#fancybox-frame" ).contents().find('#editables table').css( 'margin', 'auto' );
		}
    }
    
    var iframeHeight = $( "#fancybox-frame" ).contents().find('#popupContent').outerHeight(true);
    var iframeWidth = $( "#fancybox-frame" ).contents().find('#popupContent').outerWidth(true);

    var windowHeight = GB_getParentHeight();

    if(iframeHeight > windowHeight)
    {
        height = windowHeight - 100;
        width = iframeWidth;
    }
    else
    {
        height = iframeHeight + 10;
        width = iframeWidth;
    }

    if(height < 130)
        height = 130;
    else if(height > windowHeight)
        height = windowHeight;
    
    $('#fancybox-content').height(height);
    $('#fancybox-outer').width(width);
    $('#fancybox-wrap').width(width);
    $('#fancybox-content').width(width);
    $('.fancybox-inner').width(width);

    // center the thing - vertically and horizontally
    $.fancybox.center();
    $.fancybox.resize();
	$('html').css({ overflow: 'hidden' });
}

function fit_iframe(id)
{                
    var iframeHeight = $('#' + id).contents().find('#popupContent').outerHeight(true);
    $('#' + id).height(iframeHeight + 100);
}

function GB_getParentHeight()
{
	var hauteurFenetre = 0;

	if( typeof(window.innerHeight ) === 'number')
		hauteurFenetre = window.innerHeight;
	else if(window.document.documentElement && window.document.documentElement.clientHeight)
		hauteurFenetre = window.document.documentElement.clientHeight;
	else if(window.document.body && window.document.body.clientHeight)
		hauteurFenetre = window.document.body.clientHeight;

	return hauteurFenetre;
}

function trim (myString)
{
	return myString.replace(/^\s+/g,'').replace(/\s+$/g,'')
}

function utf8_encode (argString) {
  // http://kevin.vanzonneveld.net
  // +   original by: Webtoolkit.info (http://www.webtoolkit.info/)
  // +   improved by: Kevin van Zonneveld (http://kevin.vanzonneveld.net)
  // +   improved by: sowberry
  // +    tweaked by: Jack
  // +   bugfixed by: Onno Marsman
  // +   improved by: Yves Sucaet
  // +   bugfixed by: Onno Marsman
  // +   bugfixed by: Ulrich
  // +   bugfixed by: Rafal Kukawski
  // +   improved by: kirilloid
  // +   bugfixed by: kirilloid
  // *     example 1: utf8_encode('Kevin van Zonneveld');
  // *     returns 1: 'Kevin van Zonneveld'

  if (argString === null || typeof argString === "undefined") {
    return "";
  }

  var string = (argString + ''); // .replace(/\r\n/g, "\n").replace(/\r/g, "\n");
  var utftext = '',
    start, end, stringl = 0;

  start = end = 0;
  stringl = string.length;
  for (var n = 0; n < stringl; n++) {
    var c1 = string.charCodeAt(n);
    var enc = null;

    if (c1 < 128) {
      end++;
    } else if (c1 > 127 && c1 < 2048) {
      enc = String.fromCharCode(
         (c1 >> 6)        | 192,
        ( c1        & 63) | 128
      );
    } else if (c1 & 0xF800 != 0xD800) {
      enc = String.fromCharCode(
         (c1 >> 12)       | 224,
        ((c1 >> 6)  & 63) | 128,
        ( c1        & 63) | 128
      );
    } else { // surrogate pairs
      if (c1 & 0xFC00 != 0xD800) { throw new RangeError("Unmatched trail surrogate at " + n); }
      var c2 = string.charCodeAt(++n);
      if (c2 & 0xFC00 != 0xDC00) { throw new RangeError("Unmatched lead surrogate at " + (n-1)); }
      c1 = ((c1 & 0x3FF) << 10) + (c2 & 0x3FF) + 0x10000;
      enc = String.fromCharCode(
         (c1 >> 18)       | 240,
        ((c1 >> 12) & 63) | 128,
        ((c1 >> 6)  & 63) | 128,
        ( c1        & 63) | 128
      );
    }
    if (enc !== null) {
      if (end > start) {
        utftext += string.slice(start, end);
      }
      utftext += enc;
      start = end = n + 1;
    }
  }

  if (end > start) {
    utftext += string.slice(start, stringl);
  }

  return utftext;
}

function addslashes(str) {
  //  discuss at: http://phpjs.org/functions/addslashes/
  // original by: Kevin van Zonneveld (http://kevin.vanzonneveld.net)
  // improved by: Ates Goral (http://magnetiq.com)
  // improved by: marrtins
  // improved by: Nate
  // improved by: Onno Marsman
  // improved by: Brett Zamir (http://brett-zamir.me)
  // improved by: Oskar Larsson Högfeldt (http://oskar-lh.name/)
  //    input by: Denny Wardhana
  //   example 1: addslashes("kevin's birthday");
  //   returns 1: "kevin\\'s birthday"

  return (str + '')
    .replace(/[\\"']/g, '\\$&')
    .replace(/\u0000/g, '\\0');
}

var compteur_pageSize = 0;
function loading_dynamic_combobox(element_id, fiche_id_sel, json_data)
{
	if($('#' + element_id).hasClass('readonly'))
    {
        return true;
    }
    if($('#s2id_' + element_id).length)
	{
		$('#s2id_' + element_id).remove();
	}
    $('#' + element_id).select2('destroy');
    
    //var width = $('#' + element_id).width();
    
    $('#' + element_id).attr('id', element_id + '_tmp').hide();
    $('#' + element_id + '_tmp').parent().append('<input type="hidden" name="' + $('#' + element_id + '_tmp').attr('name') + '" id="' + element_id + '" class="' + $('#' + element_id + '_tmp').attr('class') + '" />');
    $('#' + element_id).val(fiche_id_sel);


    var initSel;
    var data = [];
    if(typeof($('#' + element_id + '_tmp').data('textblank')) !== 'undefined' && $('#' + element_id + '_tmp').data('textblank').length > 0)
    {
        data.push({id: "", text: ""+$('#' + element_id + '_tmp').data('textblank')});
        if(fiche_id_sel.length == 0)
        {
            initSel = {id: "", text: ""+$('#' + element_id + '_tmp').data('textblank')};
        }
    }
    $.each(json_data, function( i, data_tmp ) {
        if(data_tmp.id == fiche_id_sel)
        {
            initSel = {id: ""+data_tmp.id, text: ""+data_tmp.text};
        }
        data.push({id: ""+data_tmp.id, text: ""+data_tmp.text});
    });

    $('#' + element_id).select2({
        initSelection: function(element, callback) {
            callback(initSel);
        },
		allowClear: true,
        width: '250px',
        height: '20px',
        escapeMarkup: function(markup) {
          return markup;
        },
		// minimumInputLength: 3,
        templateResult: function (d) { 
            return $(d.text); 
        },
        templateSelection: function (d) { 
            return $(d.text); 
        },
        query: function(options){
                     
          var default_pageSize = 500;
          var pageSize = compteur_pageSize === 0 ? default_pageSize : 10000;
          
          var startIndex  = (options.page - 1) * default_pageSize;
          var filteredData = data;
          var stripDiacritics = window.Select2.util.stripDiacritics;

          if( options.term && options.term.length > 0 ){
            if( !options.context ){
              var term = stripDiacritics(options.term.toLowerCase());

              options.context = data.filter( function(metric){
                //since data is very big... save the stripDiacritics result for later
                //to speed up next search.
                //this does modify the original array!

                if ( !metric.stripped_text ) {
                  metric.stripped_text = stripDiacritics(metric.text.toLowerCase());
                }
                return (metric.stripped_text.indexOf(term) !== -1);
              });
            }
            filteredData = options.context;
          }

          options.callback({
            context: filteredData,
            results: filteredData.slice(startIndex, startIndex + pageSize),
            more: (startIndex + pageSize) < filteredData.length
          });
                    
          compteur_pageSize++;
        }
    });

	$('#' + element_id).on('change', function (evt) {
		$('.select2-focusser').blur();
	});
    
    $('#' + element_id).on('select2-opening', function (evt) {
        compteur_pageSize = 0;
    });

	const clearBtn = $('<a style="position: absolute; top: -1px;"><img src="css/images/icons/dark/cross_3.png" /></a>');
	clearBtn.click(function(){
		$('#' + element_id).val('');
		$('#' + element_id + '_tmp').html('<option value="" selected="selected"></option>');
		$('#' + element_id + '_tmp').parent().find('.select2-chosen').html('');
	});
	$('#' + element_id).parent().css('position', 'relative');
	$('#' + element_id).parent().append(clearBtn);
}

function copyToClipboard(element) {
  var $temp = $("<input>");
  $(element).parent().append($temp);
  $temp.val($(element).text()).select();
  document.execCommand("copy");
  $temp.remove();
}
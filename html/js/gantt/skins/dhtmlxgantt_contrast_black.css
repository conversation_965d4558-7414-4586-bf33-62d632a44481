/*
@license

dhtmlxGantt v.6.1.0 Professional
This software is covered by DHTMLX Enterprise License. Usage without proper license is prohibited.

(c) Dinamenta, UAB.

*/
.buttonBg {
  background: #000;
}
/* colors for items with inline styles assigned (task.color, link.color)*/
.gridHoverStyle {
  background-color: #42425e;
}
.gridSelection {
  background-color: #42425e;
}
.timelineSelection {
  background-color: #42425e;
}
.header_text_style {
  text-transform: uppercase;
  font-weight: bold;
}
.gantt_grid_scale .gantt_grid_head_cell {
  color: #c1c1c1;
  text-transform: uppercase;
  font-weight: bold;
  border-top: none !important;
  border-right: none !important;
}
.gantt_grid_data .gantt_cell {
  border-right: none;
  color: #fff;
}
/*
	Tasks
*/
.gantt_task_link .gantt_link_arrow_right {
  border-width: 6px 6px 6px 6px;
  margin-top: -3px;
}
.gantt_task_link .gantt_link_arrow_left {
  border-width: 6px 6px 6px 6px;
  margin-left: -6px;
  margin-top: -3px;
}
.gantt_task_link .gantt_link_arrow_up {
  border-width: 6px 6px 6px 6px;
}
.gantt_task_link .gantt_link_arrow_down {
  border-width: 6px 6px 6px 6px;
}
.gantt_task_line .gantt_task_progress_drag {
  bottom: -4px;
  height: 10px;
  margin-left: -8px;
  width: 16px;
}
.chartHeaderBg {
  background-color: #000;
}
.gantt_task .gantt_task_scale .gantt_scale_cell {
  color: #c1c1c1;
  text-transform: uppercase;
  font-weight: bold;
  border-right: 1px solid #7e7e7e;
}
/*
	project highlight
*/
.gantt_row.gantt_project,
.gantt_row.odd.gantt_project {
  background-color: #edffee;
}
.gantt_task_row.gantt_project,
.gantt_task_row.odd.gantt_project {
  background-color: #f5fff5;
}
.gantt_task_line.gantt_project {
  background-color: #006803;
  border: 1px solid #006803;
}
.gantt_task_line.gantt_project .gantt_task_progress {
  background-color: #003b01;
}
/*
	milestone
*/
/*
	lightbox
*/
.modalBorder {
  border-color: #fff;
  -webkit-box-shadow: inset 0px 0px 0px 4px #0505ff;
  -moz-box-shadow: inset 0px 0px 0px 4px #0505ff;
  box-shadow: inset 0px 0px 0px 4px #0505ff;
  padding: 4px;
}
.gantt_popup_shadow {
  border-color: #fff;
  -webkit-box-shadow: inset 0px 0px 0px 4px #0505ff;
  -moz-box-shadow: inset 0px 0px 0px 4px #0505ff;
  box-shadow: inset 0px 0px 0px 4px #0505ff;
  padding: 4px;
}
.buttonBg {
  background: #000;
}
.gantt_cal_larea input,
.gantt_cal_larea select,
.gantt_cal_larea textarea {
  background: #000;
  color: #fff;
}
.gantt_cal_larea .gantt_section_time {
  background: #000;
}
.gantt_cal_larea .gantt_cal_lsection {
  color: #c1c1c1;
  font-size: 14px;
}
.gantt_cal_light .gantt_btn_set {
  margin: 5px 10px;
}
.gantt_btn_set.gantt_cancel_btn_set {
  border-style: solid;
  border-width: 2px;
  border-color: #fff;
  background: #000;
  color: #fff;
}
.gantt_btn_set.gantt_save_btn_set {
  border-style: solid;
  border-width: 2px;
  border-color: #76ff03;
  background: #000;
  color: #76ff03;
}
.gantt_btn_set.gantt_delete_btn_set {
  border-style: solid;
  border-width: 2px;
  border-color: #ffc400;
  background: #000;
  color: #ffc400;
}
.gantt_delete_btn {
  margin-top: 2px;
  width: 20px;
}
.gantt_cal_light_wide {
  padding-left: 0 !important;
  padding-right: 0 !important;
}
.gantt_cal_light_wide .gantt_cal_larea {
  border-left: none !important;
  border-right: none !important;
}
/*
	Message
*/
.gantt_popup_button.gantt_ok_button {
  font-weight: bold;
  border-style: solid;
  border-width: 2px;
  border-color: #76ff03;
  background: #000;
  color: #76ff03;
}
.gantt_popup_button.gantt_cancel_button {
  font-weight: bold;
  color: #454544;
  border-style: solid;
  border-width: 2px;
  border-color: #fff;
  background: #000;
  color: #fff;
}
.gantt_popup_title {
  color: #fff;
}
/*
	Quick info
*/
.gantt_qi_big_icon {
  border-style: solid;
  border-width: 2px;
  border-color: #fff;
  background: #000;
  color: #fff;
}
.gantt_qi_big_icon.icon_edit {
  border-style: solid;
  border-width: 2px;
  border-color: #fff;
  background: #000;
  color: #fff;
}
.gantt_qi_big_icon.icon_delete {
  border-style: solid;
  border-width: 2px;
  border-color: #ffc400;
  background: #000;
  color: #ffc400;
}
/*links dnd*/
.gantt_tooltip {
  font-size: 14px;
  color: #fff;
  background: #000;
  border: 1px solid #FFFF00;
}
.gantt_container {
  background-color: #000;
  font-family: "arial";
  font-size: 14px;
  border: 1px solid #FFFF00;
  position: relative;
  white-space: nowrap;
  overflow-x: hidden;
  overflow-y: hidden;
}
.gantt_task_scroll {
  overflow-x: scroll;
}
.gantt_task,
.gantt_grid {
  position: relative;
  overflow-x: hidden;
  overflow-y: hidden;
  display: inline-block;
  vertical-align: top;
}
.gantt_grid_scale,
.gantt_task_scale {
  color: #ffffff;
  font-size: 14px;
  border-bottom: 1px solid #FFFF00;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}
.gantt_grid_scale {
  background-color: #000;
}
.gantt_task_scale {
  background-color: #000;
}
.gantt_task_vscroll {
  background-color: #000;
}
.gantt_scale_line {
  box-sizing: border-box;
  -moz-box-sizing: border-box;
  border-top: 1px solid #FFFF00;
}
.gantt_scale_line:first-child {
  border-top: none;
}
.gantt_grid_head_cell {
  display: inline-block;
  vertical-align: top;
  border-right: 1px solid #FFFF00;
  text-align: center;
  position: relative;
  cursor: default;
  height: 100%;
  box-sizing: border-box;
  -moz-box-sizing: border-box;
  line-height: 33px;
  -moz-user-select: -moz-none;
  -webkit-user-select: none;
  user-select: none;
  overflow: hidden;
}
.gantt_scale_line {
  clear: both;
}
.gantt_grid_data {
  width: 100%;
  overflow: hidden;
  position: relative;
}
.gantt_row {
  position: relative;
  -webkit-user-select: none;
  -moz-user-select: none;
  -moz-user-select: -moz-none;
}
.gantt_add,
.gantt_grid_head_add {
  width: 100%;
  height: 100%;
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyhpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuNi1jMTMyIDc5LjE1OTI4NCwgMjAxNi8wNC8xOS0xMzoxMzo0MCAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZVJlZiMiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENDIDIwMTUuNSAoV2luZG93cykiIHhtcE1NOkluc3RhbmNlSUQ9InhtcC5paWQ6RTk4MTk1QzM2ODc4MTFFNkFCNkFDOUZDNzZFNjgxRkIiIHhtcE1NOkRvY3VtZW50SUQ9InhtcC5kaWQ6RTk4MTk1QzQ2ODc4MTFFNkFCNkFDOUZDNzZFNjgxRkIiPiA8eG1wTU06RGVyaXZlZEZyb20gc3RSZWY6aW5zdGFuY2VJRD0ieG1wLmlpZDpFOTgxOTVDMTY4NzgxMUU2QUI2QUM5RkM3NkU2ODFGQiIgc3RSZWY6ZG9jdW1lbnRJRD0ieG1wLmRpZDpFOTgxOTVDMjY4NzgxMUU2QUI2QUM5RkM3NkU2ODFGQiIvPiA8L3JkZjpEZXNjcmlwdGlvbj4gPC9yZGY6UkRGPiA8L3g6eG1wbWV0YT4gPD94cGFja2V0IGVuZD0iciI/Psrbi8sAAABYSURBVHjaYiz7z8xACWDCI/cBiP9D8QdyDODHwSbaAIq9QF8DkAMMhtEBuvwHZAP4ybCcn6pe+EiGXrAeFihHAIefkQHjME8HOAOJUCCz4DFAgBgXAAQYACAZFHJhx3hLAAAAAElFTkSuQmCC);
  background-position: center center;
  background-repeat: no-repeat;
  cursor: pointer;
  position: relative;
  -moz-opacity: 0.3;
  opacity: 0.3;
}
.gantt_grid_head_cell.gantt_grid_head_add {
  -moz-opacity: 0.6;
  opacity: 0.6;
  top: 0;
}
.gantt_grid_head_cell.gantt_grid_head_add:hover {
  -moz-opacity: 1;
  opacity: 1;
}
.gantt_grid_data .gantt_row:hover,
.gantt_grid_data .gantt_row.odd:hover {
  background-color: #42425e;
}
.gantt_grid_data .gantt_row:hover .gantt_add,
.gantt_grid_data .gantt_row.odd:hover .gantt_add {
  -moz-opacity: 1;
  opacity: 1;
}
.gantt_task_row,
.gantt_row {
  border-bottom: 1px solid #7e7e7e;
}
.gantt_row,
.gantt_task_row {
  background-color: #000;
}
.gantt_row.odd,
.gantt_task_row.odd {
  background-color: #000;
}
.gantt_row,
.gantt_cell,
.gantt_task_row,
.gantt_task_cell,
.gantt_grid_head_cell,
.gantt_scale_cell {
  box-sizing: border-box;
  -moz-box-sizing: border-box;
}
.gantt_grid_head_cell,
.gantt_scale_cell {
  line-height: inherit;
}
.gantt_grid_scale .gantt_grid_column_resize_wrap {
  cursor: col-resize;
  position: absolute;
  width: 13px;
  margin-left: -7px;
}
.gantt_grid_column_resize_wrap .gantt_grid_column_resize {
  background-color: #FFFF00;
  height: 100%;
  width: 1px;
  margin: 0 auto;
}
.gantt_drag_marker.gantt_grid_resize_area {
  background-color: rgba(231, 231, 231, 0.5);
  border-left: 1px solid #FFFF00;
  border-right: 1px solid #FFFF00;
  height: 100%;
  width: 100%;
  -moz-box-sizing: border-box;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}
.gantt_cell {
  display: inline-block;
  vertical-align: top;
  border-right: 1px solid #7e7e7e;
  padding-left: 6px;
  padding-right: 6px;
  height: 100%;
  overflow: hidden;
  white-space: nowrap;
  font-size: 14px;
}
.gantt_grid_scale .gantt_last_cell,
.gantt_grid_data .gantt_last_cell,
.gantt_task .gantt_task_scale .gantt_scale_cell.gantt_last_cell,
.gantt_task_bg .gantt_last_cell {
  border-right-width: 0px;
}
.gantt_task .gantt_task_scale .gantt_scale_cell.gantt_last_cell {
  border-right-width: 1px;
}
.gantt_task_bg {
  overflow: hidden;
}
.gantt_scale_cell {
  display: inline-block;
  white-space: nowrap;
  overflow: hidden;
  border-right: 1px solid #FFFF00;
  text-align: center;
  height: 100%;
}
.gantt_task_cell {
  display: inline-block;
  height: 100%;
  border-right: 1px solid #7e7e7e;
}
.gantt_layout_cell.gantt_ver_scroll {
  width: 0px;
  background-color: transparent;
  height: 1px;
  overflow-x: hidden;
  overflow-y: scroll;
  position: absolute;
  right: 0px;
  z-index: 1;
}
.gantt_ver_scroll > div {
  width: 1px;
  height: 1px;
}
.gantt_hor_scroll {
  height: 0px;
  background-color: transparent;
  width: 100%;
  clear: both;
  overflow-x: scroll;
  overflow-y: hidden;
}
.gantt_layout_cell .gantt_hor_scroll {
  position: absolute;
}
.gantt_hor_scroll > div {
  width: 5000px;
  height: 1px;
}
.gantt_tree_indent {
  width: 15px;
  height: 100%;
  display: inline-block;
}
.gantt_tree_content,
.gantt_tree_icon {
  vertical-align: top;
}
.gantt_tree_icon {
  width: 28px;
  height: 100%;
  display: inline-block;
  background-repeat: no-repeat;
  background-position: center center;
}
.gantt_tree_content {
  height: 100%;
  display: inline-block;
}
.gantt_tree_icon.gantt_open {
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyhpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuNi1jMTMyIDc5LjE1OTI4NCwgMjAxNi8wNC8xOS0xMzoxMzo0MCAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZVJlZiMiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENDIDIwMTUuNSAoV2luZG93cykiIHhtcE1NOkluc3RhbmNlSUQ9InhtcC5paWQ6QjRFQzM4RjY2ODc3MTFFNjlGNUVCQUVCQzgyNkQ5RUUiIHhtcE1NOkRvY3VtZW50SUQ9InhtcC5kaWQ6QjRFQzM4Rjc2ODc3MTFFNjlGNUVCQUVCQzgyNkQ5RUUiPiA8eG1wTU06RGVyaXZlZEZyb20gc3RSZWY6aW5zdGFuY2VJRD0ieG1wLmlpZDpCNEVDMzhGNDY4NzcxMUU2OUY1RUJBRUJDODI2RDlFRSIgc3RSZWY6ZG9jdW1lbnRJRD0ieG1wLmRpZDpCNEVDMzhGNTY4NzcxMUU2OUY1RUJBRUJDODI2RDlFRSIvPiA8L3JkZjpEZXNjcmlwdGlvbj4gPC9yZGY6UkRGPiA8L3g6eG1wbWV0YT4gPD94cGFja2V0IGVuZD0iciI/PpdfxE8AAABySURBVHjaYjx48CADucDOzo6BiYFCwILE/k+iXkZ0A+CCRAC4ZXi9sNXO6T8IE+sFkAZcBqHwvQ/tYyDKBaQGIr7A/I8rnGjiAkYsNjMSZQBy4CAHHro4zb2A7CJGUg34T4kLGMnxAuP///8pCgOAAAMAxcMbX5R+AnUAAAAASUVORK5CYII=);
  width: 18px;
  cursor: pointer;
}
.gantt_tree_icon.gantt_close {
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyhpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuNi1jMTMyIDc5LjE1OTI4NCwgMjAxNi8wNC8xOS0xMzoxMzo0MCAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZVJlZiMiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENDIDIwMTUuNSAoV2luZG93cykiIHhtcE1NOkluc3RhbmNlSUQ9InhtcC5paWQ6Qjg3ODU2QTg2ODc3MTFFNkE4MDlDMDMyQkRGNDgyQTAiIHhtcE1NOkRvY3VtZW50SUQ9InhtcC5kaWQ6Qjg3ODU2QTk2ODc3MTFFNkE4MDlDMDMyQkRGNDgyQTAiPiA8eG1wTU06RGVyaXZlZEZyb20gc3RSZWY6aW5zdGFuY2VJRD0ieG1wLmlpZDpCODc4NTZBNjY4NzcxMUU2QTgwOUMwMzJCREY0ODJBMCIgc3RSZWY6ZG9jdW1lbnRJRD0ieG1wLmRpZDpCODc4NTZBNzY4NzcxMUU2QTgwOUMwMzJCREY0ODJBMCIvPiA8L3JkZjpEZXNjcmlwdGlvbj4gPC9yZGY6UkRGPiA8L3g6eG1wbWV0YT4gPD94cGFja2V0IGVuZD0iciI/PqbUJTYAAABdSURBVHjaYjx48CADucDOzo6BiYFCwILE/k+iXkZ0A+CCRAC4ZVT1AsNWOyeiNHkf2sdAGxcQGZgo4UQTFzCSbQBy4BALKPYC1cPgPyUGMJLjAsb///9T5AWAAAMAJ4wO9WM7P60AAAAASUVORK5CYII=);
  width: 18px;
  cursor: pointer;
}
.gantt_tree_icon.gantt_blank {
  width: 18px;
}
.gantt_tree_icon.gantt_folder_open {
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAASCAYAAABWzo5XAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyhpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuNi1jMTMyIDc5LjE1OTI4NCwgMjAxNi8wNC8xOS0xMzoxMzo0MCAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZVJlZiMiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENDIDIwMTUuNSAoV2luZG93cykiIHhtcE1NOkluc3RhbmNlSUQ9InhtcC5paWQ6RkM2MUMyMzQ2ODc3MTFFNjhFMjE5QzYzREY2ODEzREUiIHhtcE1NOkRvY3VtZW50SUQ9InhtcC5kaWQ6RkM2MUMyMzU2ODc3MTFFNjhFMjE5QzYzREY2ODEzREUiPiA8eG1wTU06RGVyaXZlZEZyb20gc3RSZWY6aW5zdGFuY2VJRD0ieG1wLmlpZDpGQzYxQzIzMjY4NzcxMUU2OEUyMTlDNjNERjY4MTNERSIgc3RSZWY6ZG9jdW1lbnRJRD0ieG1wLmRpZDpGQzYxQzIzMzY4NzcxMUU2OEUyMTlDNjNERjY4MTNERSIvPiA8L3JkZjpEZXNjcmlwdGlvbj4gPC9yZGY6UkRGPiA8L3g6eG1wbWV0YT4gPD94cGFja2V0IGVuZD0iciI/PlC39FYAAADWSURBVHjaYty4fi0DNQATA5UAskGeQPwMiP9jwfWkGDQXiCVxqGsgZBiyQTBDGNFwI5Jh2FwL8oUnMWHUgGQYNgBywFxiA7sBi0thGGwYVWOtFYi/IIn9JxI7oBtUBMTcZDiiHt2gE1B2C55wQMZiQPwZ6iIHZINgMZIKxFxEuOQ1EE9GdxXIoANQLA7E2UR6qweIP6K7iAHJVaVAzEuEQe+BeBqyAAuUBrloBxB7APEnMgL+BXI6ygfiR2QY8hSIk1mQBG4BsTy5CZLx////VEnZAAEGAO3yNxaCOyWPAAAAAElFTkSuQmCC);
}
.gantt_tree_icon.gantt_folder_closed {
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAASCAYAAABWzo5XAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyhpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuNi1jMTMyIDc5LjE1OTI4NCwgMjAxNi8wNC8xOS0xMzoxMzo0MCAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZVJlZiMiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENDIDIwMTUuNSAoV2luZG93cykiIHhtcE1NOkluc3RhbmNlSUQ9InhtcC5paWQ6RkZDMUZDMkI2ODc3MTFFNkI5QTdFOTQyM0UxNUEzRjEiIHhtcE1NOkRvY3VtZW50SUQ9InhtcC5kaWQ6RkZDMUZDMkM2ODc3MTFFNkI5QTdFOTQyM0UxNUEzRjEiPiA8eG1wTU06RGVyaXZlZEZyb20gc3RSZWY6aW5zdGFuY2VJRD0ieG1wLmlpZDpGRkMxRkMyOTY4NzcxMUU2QjlBN0U5NDIzRTE1QTNGMSIgc3RSZWY6ZG9jdW1lbnRJRD0ieG1wLmRpZDpGRkMxRkMyQTY4NzcxMUU2QjlBN0U5NDIzRTE1QTNGMSIvPiA8L3JkZjpEZXNjcmlwdGlvbj4gPC9yZGY6UkRGPiA8L3g6eG1wbWV0YT4gPD94cGFja2V0IGVuZD0iciI/PvnU3ycAAAB9SURBVHjaYty4fi0DNQATA5UAskGeQPwMiP9jwfWkGDQXiCVxqGsgZBiyQTBDGNFwI5Jh2FwL8oUnMWHUgGQYNgBywFxiA7sBi0thGGwYTWJt1KChaNBzKP2fRAwCL5ANSgEJkOGYp0CczIIksA1PpiUIGP///0+VMAIIMAC11ShRpw6rxAAAAABJRU5ErkJggg==);
}
.gantt_tree_icon.gantt_file {
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAASCAYAAABWzo5XAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyhpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuNi1jMTMyIDc5LjE1OTI4NCwgMjAxNi8wNC8xOS0xMzoxMzo0MCAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZVJlZiMiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENDIDIwMTUuNSAoV2luZG93cykiIHhtcE1NOkluc3RhbmNlSUQ9InhtcC5paWQ6MDQwMTVBMUU2ODc4MTFFNkFBNDVCQkYzMzdERTM1MzYiIHhtcE1NOkRvY3VtZW50SUQ9InhtcC5kaWQ6MDQwMTVBMUY2ODc4MTFFNkFBNDVCQkYzMzdERTM1MzYiPiA8eG1wTU06RGVyaXZlZEZyb20gc3RSZWY6aW5zdGFuY2VJRD0ieG1wLmlpZDowNDAxNUExQzY4NzgxMUU2QUE0NUJCRjMzN0RFMzUzNiIgc3RSZWY6ZG9jdW1lbnRJRD0ieG1wLmRpZDowNDAxNUExRDY4NzgxMUU2QUE0NUJCRjMzN0RFMzUzNiIvPiA8L3JkZjpEZXNjcmlwdGlvbj4gPC9yZGY6UkRGPiA8L3g6eG1wbWV0YT4gPD94cGFja2V0IGVuZD0iciI/PsHnBgAAAAB8SURBVHjaYvz//z/Dpg3rGCgBfgFBDExoYp5A/AyI/+PB9dgMQzdoLhBLEnBAAzbDWND4MEMYsRjwH80wEGjE5SJSAIrLmBgoAw3UMghnGOEDjHjCjHouGjVoOBj0HCmNEMIg8AKXQSnIkgTAUyBOxpWytxFRjGAFAAEGAOcUI0XiA1ItAAAAAElFTkSuQmCC);
}
.gantt_grid_head_cell .gantt_sort {
  position: absolute;
  right: 5px;
  top: 8px;
  width: 7px;
  height: 13px;
  background-repeat: no-repeat;
  background-position: center center;
}
.gantt_grid_head_cell .gantt_sort.gantt_asc {
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAcAAAANCAYAAABlyXS1AAAARUlEQVR4nGNgQAKGxib/GbABkIS7b8B/DAUwCRiGK0CXwFBAb1DfP/U/LszwHwi2X7qFgUEArBtdAVwCBmAKMCSQFSDzAWXXaOHsXeqkAAAAAElFTkSuQmCC);
}
.gantt_grid_head_cell .gantt_sort.gantt_desc {
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAcAAAANCAYAAABlyXS1AAAARUlEQVR42mNgQAL1/VP/M2ADIIntF2/9x1AAlrh0C47hCmA60DFYwX88gIFGwNDY5D8uDFbg7hvwHx2jmIBTAlkB0e4BAEjlaNtBWJPnAAAAAElFTkSuQmCC);
}
.gantt_inserted,
.gantt_updated {
  font-weight: bold;
}
.gantt_deleted {
  text-decoration: line-through;
}
.gantt_invalid {
  background-color: #FFE0E0;
}
.gantt_error {
  color: red;
}
.gantt_status {
  right: 1px;
  padding: 5px 10px;
  background: rgba(155, 155, 155, 0.1);
  position: absolute;
  top: 1px;
  -webkit-transition: opacity 0.2s;
  transition: opacity 0.2s;
  opacity: 0;
}
.gantt_status.gantt_status_visible {
  opacity: 1;
}
#gantt_ajax_dots span {
  -webkit-transition: opacity 0.2s;
  transition: opacity 0.2s;
  background-repeat: no-repeat;
  opacity: 0;
}
#gantt_ajax_dots span.gantt_dot_visible {
  opacity: 1;
}
.gantt_message_area {
  position: fixed;
  right: 5px;
  width: 250px;
  z-index: 1000;
}
.gantt-info {
  min-width: 120px;
  padding: 4px 4px 4px 20px;
  font-family: "arial";
  z-index: 10000;
  margin: 5px;
  margin-bottom: 10px;
  -webkit-transition: all .5s ease;
  -moz-transition: all .5s ease;
  -o-transition: all .5s ease;
  transition: all .5s ease;
}
.gantt-info.hidden {
  height: 0px;
  padding: 0px;
  border-width: 0px;
  margin: 0px;
  overflow: hidden;
}
.gantt_modal_box {
  overflow: hidden;
  display: inline-block;
  min-width: 250px;
  width: 250px;
  text-align: center;
  position: fixed;
  z-index: 20000;
  border-color: #fff;
  -webkit-box-shadow: inset 0px 0px 0px 4px #0505ff;
  -moz-box-shadow: inset 0px 0px 0px 4px #0505ff;
  box-shadow: inset 0px 0px 0px 4px #0505ff;
  padding: 4px;
  font-family: "arial";
  border-radius: 6px;
  border: 1px solid #FFFF00;
  background: #000;
}
.gantt_popup_title {
  border-top-left-radius: 6px;
  border-top-right-radius: 6px;
  border-width: 0px;
}
.gantt_button,
.gantt_popup_button {
  border: 1px solid #FFFF00;
  height: 30px;
  line-height: 30px;
  display: inline-block;
  margin: 0 5px;
  border-radius: 4px;
  background: #000;
}
.gantt-info,
.gantt_popup_button,
.gantt_button {
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: -moz-none;
  cursor: pointer;
}
.gantt_popup_text {
  overflow: hidden;
}
.gantt_popup_controls {
  border-radius: 6px;
  padding: 10px;
}
.gantt_popup_button {
  min-width: 100px;
}
div.dhx_modal_cover {
  background-color: #000;
  cursor: default;
  filter: progid:DXImageTransform.Microsoft.Alpha(opacity=20);
  opacity: 0.2;
  position: fixed;
  z-index: 19999;
  left: 0px;
  top: 0px;
  width: 100%;
  height: 100%;
  border: none;
  zoom: 1;
}
.gantt-info img,
.gantt_modal_box img {
  float: left;
  margin-right: 20px;
}
.gantt-alert-error,
.gantt-confirm-error {
  border: 1px solid #ff0000;
}
/*Skin section*/
.gantt_button input,
.gantt_popup_button div {
  border-radius: 4px;
  font-size: 16px;
  -moz-box-sizing: content-box;
  box-sizing: content-box;
  padding: 0px;
  margin: 0px;
  vertical-align: top;
}
.gantt_popup_title {
  border-bottom: 1px solid #c1c1c1;
  height: 40px;
  line-height: 40px;
  font-size: 20px;
}
.gantt_popup_text {
  margin: 15px 15px 5px 15px;
  font-size: 16px;
  color: #fff;
  min-height: 30px;
  border-radius: 6px;
}
.gantt-info,
.gantt-error {
  font-size: 16px;
  color: #000;
  box-shadow: 3px 3px 3px rgba(0, 0, 0, 0.07);
  padding: 0px;
  background-color: #FFFFFF;
  border-radius: 3px;
  border: 1px solid #FFFFFF;
}
.gantt-info div {
  padding: 5px 10px 5px 10px;
  background-color: #FFFFFF;
  border-radius: 3px;
  border: 1px solid #FFFF00;
}
.gantt-error {
  background-color: #B60000;
  border: 1px solid #FFFF00;
}
.gantt-error div {
  background-color: #B60000;
  border: 1px solid transparent;
  color: #FFFFFF;
}
.gantt-warning {
  background-color: #EF7F00;
  border: 1px solid #FFFF00;
}
.gantt-warning div {
  background-color: #EF7F00;
  border: 1px solid transparent;
  color: #FFFFFF;
}
.gantt_grid div,
.gantt_data_area div {
  -ms-touch-action: none;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}
.gantt_data_area {
  position: relative;
  overflow-x: hidden;
  overflow-y: hidden;
  -moz-user-select: -moz-none;
  -webkit-user-select: none;
  user-select: none;
}
.gantt_links_area {
  position: absolute;
  left: 0px;
  top: 0px;
}
.gantt_task_content,
.gantt_task_progress,
.gantt_side_content {
  line-height: inherit;
  overflow: hidden;
  height: 100%;
}
.gantt_task_content {
  font-size: 14px;
  color: #fff;
  width: 100%;
  top: 0;
  cursor: pointer;
  position: absolute;
  white-space: nowrap;
  text-align: center;
}
.gantt_task_progress {
  text-align: center;
  z-index: 0;
  background: #0e2d7d;
}
.gantt_task_progress_wrapper {
  border-radius: inherit;
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
}
.gantt_task_line {
  -webkit-border-radius: 2px;
  -moz-border-radius: 2px;
  border-radius: 2px;
  position: absolute;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  background-color: #0042e9;
  border: 1px solid #0042e9;
  -webkit-user-select: none;
  -moz-user-select: none;
  -moz-user-select: -moz-none;
}
.gantt_task_line.gantt_drag_move div {
  cursor: move;
}
.gantt_touch_move,
.gantt_touch_progress .gantt_touch_resize {
  -moz-transform: scale(1.02, 1.1);
  -o-transform: scale(1.02, 1.1);
  -webkit-transform: scale(1.02, 1.1);
  transform: scale(1.02, 1.1);
  -moz-transform-origin: 50%;
  -o-transform-origin: 50%;
  -webkit-transform-origin: 50%;
  transform-origin: 50%;
}
.gantt_touch_progress .gantt_task_progress_drag,
.gantt_touch_resize .gantt_task_drag {
  -moz-transform: scaleY(1.3);
  -o-transform: scaleY(1.3);
  -webkit-transform: scaleY(1.3);
  transform: scaleY(1.3);
  -moz-transform-origin: 50%;
  -o-transform-origin: 50%;
  -webkit-transform-origin: 50%;
  transform-origin: 50%;
}
.gantt_side_content {
  position: absolute;
  white-space: nowrap;
  color: #fff;
  top: 0;
  font-size: 11px;
  font-size: 13px;
}
.gantt_side_content.gantt_left {
  right: 100%;
  padding-right: 20px;
}
.gantt_side_content.gantt_right {
  left: 100%;
  padding-left: 20px;
}
.gantt_side_content.gantt_link_crossing {
  bottom: 8.75px;
  top: auto;
}
.gantt_task_link .gantt_line_wrapper,
.gantt_link_arrow {
  position: absolute;
  cursor: pointer;
}
.gantt_line_wrapper div {
  background-color: #18ffff;
}
.gantt_task_link:hover .gantt_line_wrapper div {
  box-shadow: 0 0 5px 0px #18ffff;
}
.gantt_task_link div.gantt_link_arrow {
  background-color: transparent;
  border-style: solid;
  width: 0px;
  height: 0px;
}
.gantt_link_control {
  position: absolute;
  width: 20px;
  top: 0px;
}
.gantt_link_control div {
  display: none;
  cursor: pointer;
  box-sizing: border-box;
  position: relative;
  top: 50%;
  margin-top: -7.5px;
  vertical-align: middle;
  border: 1px solid #929292;
  -webkit-border-radius: 6.5px;
  -moz-border-radius: 6.5px;
  border-radius: 6.5px;
  height: 13px;
  width: 13px;
  background-color: #f0f0f0;
}
.gantt_link_control.task_right div.gantt_link_point {
  margin-left: 7px;
}
.gantt_link_control div:hover {
  background-color: #FFF;
}
.gantt_link_control.task_left {
  left: -20px;
}
.gantt_link_control.task_right {
  right: -20px;
}
.gantt_task_line.gantt_selected .gantt_link_control div,
.gantt_task_line:hover .gantt_link_control div,
.gantt_task_line.gantt_drag_progress .gantt_link_control div,
.gantt_task_line.gantt_drag_move .gantt_link_control div,
.gantt_task_line.gantt_drag_resize .gantt_link_control div,
.gantt_task_line.gantt_selected .gantt_task_progress_drag,
.gantt_task_line:hover .gantt_task_progress_drag,
.gantt_task_line.gantt_drag_progress .gantt_task_progress_drag,
.gantt_task_line.gantt_drag_move .gantt_task_progress_drag,
.gantt_task_line.gantt_drag_resize .gantt_task_progress_drag,
.gantt_task_line.gantt_selected .gantt_task_drag,
.gantt_task_line:hover .gantt_task_drag,
.gantt_task_line.gantt_drag_progress .gantt_task_drag,
.gantt_task_line.gantt_drag_move .gantt_task_drag,
.gantt_task_line.gantt_drag_resize .gantt_task_drag {
  display: block;
}
.gantt_link_target .gantt_link_control div {
  display: block;
}
.gantt_link_source,
.gantt_link_target {
  box-shadow: 0px 0px 3px #0042e9;
}
.gantt_link_target.link_start_allow,
.gantt_link_target.link_finish_allow {
  box-shadow: 0px 0px 3px #65ffff;
}
.gantt_link_target.link_start_deny,
.gantt_link_target.link_finish_deny {
  box-shadow: 0px 0px 3px #e87e7b;
}
.link_start_allow .gantt_link_control.task_start_date div,
.link_finish_allow .gantt_link_control.task_end_date div {
  background-color: #65ffff;
  border-color: #18ffff;
}
.link_start_deny .gantt_link_control.task_start_date div,
.link_finish_deny .gantt_link_control.task_end_date div {
  background-color: #e87e7b;
  border-color: #dd3e3a;
}
.gantt_link_arrow_right {
  border-width: 4px 0 4px 6px;
  border-top-color: transparent !important;
  border-right-color: transparent !important;
  border-bottom-color: transparent !important;
  border-left-color: #18ffff;
  margin-top: -1px;
}
.gantt_link_arrow_left {
  border-width: 4px 6px 4px 0;
  margin-top: -1px;
  border-top-color: transparent !important;
  border-right-color: #18ffff;
  border-bottom-color: transparent !important;
  border-left-color: transparent !important;
}
.gantt_link_arrow_up {
  border-width: 0 4px 6px 4px;
  border-color: transparent transparent #18ffff transparent;
  border-top-color: transparent !important;
  border-right-color: transparent !important;
  border-bottom-color: #18ffff;
  border-left-color: transparent !important;
}
.gantt_link_arrow_down {
  border-width: 4px 6px 0 4px;
  border-top-color: #18ffff;
  border-right-color: transparent !important;
  border-bottom-color: transparent !important;
  border-left-color: transparent !important;
}
.gantt_task_drag,
.gantt_task_progress_drag {
  cursor: ew-resize;
  display: none;
  position: absolute;
}
.gantt_task_drag.task_right {
  cursor: e-resize;
}
.gantt_task_drag.task_left {
  cursor: w-resize;
}
.gantt_task_drag {
  height: 100%;
  width: 8px;
  z-index: 1;
  top: -1px;
}
.gantt_task_drag.task_left {
  left: -7px;
}
.gantt_task_drag.task_right {
  right: -7px;
}
.gantt_task_progress_drag {
  height: 8px;
  width: 8px;
  bottom: -4px;
  margin-left: -4px;
  background-position: bottom;
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAALCAYAAAB24g05AAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyhpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuNi1jMTMyIDc5LjE1OTI4NCwgMjAxNi8wNC8xOS0xMzoxMzo0MCAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZVJlZiMiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENDIDIwMTUuNSAoV2luZG93cykiIHhtcE1NOkluc3RhbmNlSUQ9InhtcC5paWQ6RDQ2MjFDRjU2ODc4MTFFNjk2MEVEQjgxRDU3RThDRjQiIHhtcE1NOkRvY3VtZW50SUQ9InhtcC5kaWQ6RDQ2MjFDRjY2ODc4MTFFNjk2MEVEQjgxRDU3RThDRjQiPiA8eG1wTU06RGVyaXZlZEZyb20gc3RSZWY6aW5zdGFuY2VJRD0ieG1wLmlpZDpENDYyMUNGMzY4NzgxMUU2OTYwRURCODFENTdFOENGNCIgc3RSZWY6ZG9jdW1lbnRJRD0ieG1wLmRpZDpENDYyMUNGNDY4NzgxMUU2OTYwRURCODFENTdFOENGNCIvPiA8L3JkZjpEZXNjcmlwdGlvbj4gPC9yZGY6UkRGPiA8L3g6eG1wbWV0YT4gPD94cGFja2V0IGVuZD0iciI/PkyX4qoAAADrSURBVHjaYiz7z8yADXQx/mUCUr1QbjFQ3T9s6phwaOYEUqvkHBkLQBjIXg0VI2wAUKEIkNqrFc0YHLqDiQGEtWMYg0BiUDncBgAVqACpY5bVjJY+i5kYmNkYwNh7ERMDSAwkB1WDaQBQwoKJheGY+ywmVdsWoDAjkiogGyTmMZtJFajmOEgtigFAgSA2HoZ9wZuZRPVTGRlwAb0URgagGhGQWpAeqNkM+TySDL3BW5mYxQ1xa0YGL8//Z1jr8+/fl2cMRSAd/zMeMjPwyTGQBD49YmCYIf8X4gVSNSPrYWKgEIC9QIkBAAEGAKWdQQSo7CbHAAAAAElFTkSuQmCC);
  background-repeat: no-repeat;
  z-index: 1;
}
.gantt_task_progress_drag:hover {
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAALCAYAAAB24g05AAAABmJLR0QA/wD/AP+gvaeTAAAACXBIWXMAAAsTAAALEwEAmpwYAAAA+UlEQVQoz6WPP0tCURiHn9+5d7huEd5VKhIEwaEQlOtQS0HjbXBy6ANUkuEo4njAz9Bee5tu2bfoCwTRVsu5p6E7GHoJ8dneP7+H99XQB6zDyhlgmpeDoQ+ydXumIFwCHuOG+nFDfeAp7/0vsHJlYFY50WVnIjoTUTlVCszyWbHAyh0Ci1pX7ea9MCGYEJoDUeuqDSzynVWBlWspYHF0rWq9p5VT6z1xfKOqAl6tXOuPwMqlYcQ8GSnePxdF7J2JZKRyGDG3cimAgNtol2kyVrBzUBxe5vPN8zL22fcHdwL8xYOhFLMRX+/wfJX9vrBpeDlj2BIBfhvBD+rwPMTN8+ghAAAAAElFTkSuQmCC);
}
.gantt_link_tooltip {
  box-shadow: 3px 3px 3px #888888;
  background-color: #fff;
  border-left: 1px dotted #cecece;
  border-top: 1px dotted #cecece;
  font-family: Tahoma;
  font-size: 8pt;
  color: #444;
  padding: 6px;
  line-height: 20px;
}
.gantt_link_direction {
  height: 0px;
  border: 0px none #18ffff;
  border-bottom-style: dashed;
  border-bottom-width: 2px;
  transform-origin: 0% 0%;
  -ms-transform-origin: 0% 0%;
  -webkit-transform-origin: 0% 0%;
  z-index: 2;
  margin-left: 1px;
  position: absolute;
}
.gantt_grid_data .gantt_row.gantt_selected,
.gantt_grid_data .gantt_row.odd.gantt_selected {
  background-color: #42425e;
}
.gantt_task_row.gantt_selected {
  background-color: #42425e;
}
.gantt_task_row.gantt_selected .gantt_task_cell {
  border-right-color: #2d2d40;
}
.gantt_task_line.gantt_selected {
  box-shadow: 0 0 5px #0e2d7d;
}
.gantt_task_line.gantt_project.gantt_selected {
  box-shadow: 0 0 5px #006803;
}
.gantt_task_line.gantt_milestone {
  visibility: hidden;
  background-color: #c06fce;
  border: 0px solid #ef8eff;
  box-sizing: content-box;
  -moz-box-sizing: content-box;
}
.gantt_task_line.gantt_milestone div {
  visibility: visible;
}
.gantt_task_line.gantt_milestone .gantt_task_content {
  background: inherit;
  border: inherit;
  border-width: 1px;
  border-radius: inherit;
  box-sizing: border-box;
  -moz-box-sizing: border-box;
  -webkit-transform: rotate(45deg);
  -moz-transform: rotate(45deg);
  -ms-transform: rotate(45deg);
  -o-transform: rotate(45deg);
  transform: rotate(45deg);
}
.gantt_task_line.gantt_task_inline_color {
  border-color: #999999;
}
.gantt_task_line.gantt_task_inline_color .gantt_task_progress {
  background-color: #363636;
  opacity: 0.2;
}
.gantt_task_line.gantt_task_inline_color.gantt_selected {
  box-shadow: 0 0 5px #999999;
}
.gantt_task_line.gantt_task_inline_color.gantt_project.gantt_selected {
  box-shadow: 0 0 5px #999999;
}
.gantt_task_link.gantt_link_inline_color:hover .gantt_line_wrapper div {
  box-shadow: 0 0 5px 0px #999999;
}
.gantt_critical_task {
  background-color: #e63030;
  border-color: #9d3a3a;
}
.gantt_critical_task .gantt_task_progress {
  background-color: rgba(0, 0, 0, 0.4);
}
.gantt_critical_link .gantt_line_wrapper > div {
  background-color: #e63030;
}
.gantt_critical_link .gantt_link_arrow {
  border-color: #e63030;
}
.gantt_row:focus,
.gantt_cell:focus,
.gantt_btn_set:focus,
.gantt_qi_big_icon:focus,
.gantt_popup_button:focus,
.gantt_grid_head_cell:focus {
  -moz-box-shadow: inset 0px 0px 1px 1px #4d90fe;
  -webkit-box-shadow: inset 0px 0px 1px 1px #4d90fe;
  box-shadow: inset 0px 0px 1px 1px #4d90fe;
}
.gantt_split_parent {
  opacity: 0.1;
  pointer-events: none;
}
.gantt_unselectable,
.gantt_unselectable div {
  -webkit-user-select: none;
  -moz-user-select: none;
  -moz-user-select: -moz-none;
}
.gantt_cal_light {
  -webkit-tap-highlight-color: transparent;
  background: #000;
  border-radius: 6px;
  font-family: "arial";
  border: 1px solid #c1c1c1;
  color: #ffffff;
  font-size: 14px;
  position: absolute;
  z-index: 10001;
  width: 550px;
  height: 250px;
  border-color: #fff;
  -webkit-box-shadow: inset 0px 0px 0px 4px #0505ff;
  -moz-box-shadow: inset 0px 0px 0px 4px #0505ff;
  box-shadow: inset 0px 0px 0px 4px #0505ff;
  padding: 4px;
}
.gantt_cal_light_wide {
  width: 650px;
}
.gantt_cal_light select {
  font-family: "arial";
  border: 1px solid #c1c1c1;
  font-size: 14px;
  padding: 2px;
  margin: 0px;
}
.gantt_cal_ltitle {
  padding: 7px 10px;
  overflow: hidden;
  white-space: nowrap;
  -webkit-border-top-left-radius: 6px;
  -webkit-border-bottom-left-radius: 0;
  -webkit-border-top-right-radius: 6px;
  -webkit-border-bottom-right-radius: 0;
  -moz-border-radius-topleft: 6px;
  -moz-border-radius-bottomleft: 0;
  -moz-border-radius-topright: 6px;
  -moz-border-radius-bottomright: 0;
  border-top-left-radius: 6px;
  border-bottom-left-radius: 0;
  border-top-right-radius: 6px;
  border-bottom-right-radius: 0;
}
.gantt_cal_ltitle span {
  white-space: nowrap;
}
.gantt_cal_lsection {
  color: #727272;
  font-weight: bold;
  padding: 12px 0px 5px 10px;
}
.gantt_cal_lsection .gantt_fullday {
  float: right;
  margin-right: 5px;
  font-size: 12px;
  font-weight: normal;
  line-height: 20px;
  vertical-align: top;
  cursor: pointer;
}
.gantt_cal_lsection {
  font-size: 13px;
}
.gantt_cal_ltext {
  padding: 2px	10px;
  overflow: hidden;
}
.gantt_cal_ltext textarea {
  overflow-y: auto;
  overflow-x: hidden;
  font-family: "arial";
  font-size: 14px;
  -moz-box-sizing: border-box;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  border: 1px solid #c1c1c1;
  height: 100%;
  width: 100%;
  outline: none !important;
  resize: none;
}
.gantt_section_constraint [data-constraint-time-select] {
  margin-left: 20px;
}
.gantt_time {
  font-weight: bold;
}
.gantt_cal_light .gantt_title {
  padding-left: 10px;
}
.gantt_cal_larea {
  border: 1px solid #c1c1c1;
  border-left: none;
  border-right: none;
  background-color: #000;
  overflow: hidden;
  height: 1px;
}
.gantt_btn_set {
  margin: 10px 7px 5px 10px;
  padding: 5px 15px 5px 10px;
  float: left;
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  border-radius: 4px;
  border-width: 2px;
  border-color: #c1c1c1;
  border-style: solid;
  height: 32px;
  font-weight: bold;
  background: #000;
  -moz-box-sizing: border-box;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  cursor: pointer;
}
.gantt_hidden {
  display: none;
}
.gantt_btn_set div {
  float: left;
  font-size: 13px;
  height: 18px;
  line-height: 18px;
  background-repeat: no-repeat;
  vertical-align: middle;
}
.gantt_save_btn {
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyhpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuNi1jMTMyIDc5LjE1OTI4NCwgMjAxNi8wNC8xOS0xMzoxMzo0MCAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZVJlZiMiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENDIDIwMTUuNSAoV2luZG93cykiIHhtcE1NOkluc3RhbmNlSUQ9InhtcC5paWQ6MEEyMTFDOUM2ODc5MTFFNjlDOTRDREYyNUQ4NENENTEiIHhtcE1NOkRvY3VtZW50SUQ9InhtcC5kaWQ6MEEyMTFDOUQ2ODc5MTFFNjlDOTRDREYyNUQ4NENENTEiPiA8eG1wTU06RGVyaXZlZEZyb20gc3RSZWY6aW5zdGFuY2VJRD0ieG1wLmlpZDowQTIxMUM5QTY4NzkxMUU2OUM5NENERjI1RDg0Q0Q1MSIgc3RSZWY6ZG9jdW1lbnRJRD0ieG1wLmRpZDowQTIxMUM5QjY4NzkxMUU2OUM5NENERjI1RDg0Q0Q1MSIvPiA8L3JkZjpEZXNjcmlwdGlvbj4gPC9yZGY6UkRGPiA8L3g6eG1wbWV0YT4gPD94cGFja2V0IGVuZD0iciI/PhVZXooAAAD2SURBVHjaxNO/a8JAFMBxUx1KQXAQcWj9sdhBCDg52I6FgjgIIi7duunsIl0dxNmxW3EUHaS0tFj1DxJRKiX1/AZeIYTGNMnQg8927727e++0tgqHgqyTUMDlN8E55tAjPoIzeEcWPa8nyEllM3iJupcEeXzgAm+4xdpMoGOB9JHgAmZIYooKtj+P2MWVHO3yl+Ci3DmOEar4tHahIRtSkqRgCb7GK2IYmnfGl72NG5QxRkKSlXCDZ0TxiDsYTnOwQw1PUu0FE5xhgHt8uw2SIVUGEniKPlpQTq9rHyRzYxMr7PHg1lunSez8dTg0pdT//saDAAMAj/Mt6qJaGUkAAAAASUVORK5CYII=);
  margin-top: 2px;
  width: 21px;
}
.gantt_cancel_btn {
  margin-top: 2px;
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyhpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuNi1jMTMyIDc5LjE1OTI4NCwgMjAxNi8wNC8xOS0xMzoxMzo0MCAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZVJlZiMiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENDIDIwMTUuNSAoV2luZG93cykiIHhtcE1NOkluc3RhbmNlSUQ9InhtcC5paWQ6MERGODM2RkI2ODc5MTFFNkFCOTRCOTNEMkMwODJCQkUiIHhtcE1NOkRvY3VtZW50SUQ9InhtcC5kaWQ6MERGODM2RkM2ODc5MTFFNkFCOTRCOTNEMkMwODJCQkUiPiA8eG1wTU06RGVyaXZlZEZyb20gc3RSZWY6aW5zdGFuY2VJRD0ieG1wLmlpZDowREY4MzZGOTY4NzkxMUU2QUI5NEI5M0QyQzA4MkJCRSIgc3RSZWY6ZG9jdW1lbnRJRD0ieG1wLmRpZDowREY4MzZGQTY4NzkxMUU2QUI5NEI5M0QyQzA4MkJCRSIvPiA8L3JkZjpEZXNjcmlwdGlvbj4gPC9yZGY6UkRGPiA8L3g6eG1wbWV0YT4gPD94cGFja2V0IGVuZD0iciI/Ppyd3J4AAAFPSURBVHjajJO5SkNRFEVfnlEbU9qrjVMwYiMBEbSK4g8IllFQK/EvLCwEEYdS/AOHIiJYWinEqREtbLQKCGpQcW3YgUuI8R1YvOTdvU9uzpCqVCpRXQxAEaag2+8e4AT24CYUp4IEbbAOixBHjePHSVbhLQqEMh/CMnzBBuQhY/J+p7MFOIaO8AabsATPMAOXDX69E86hz9+35FGCQT5cwTeMNjGfgbT30AVpyMUuWAtsJzBfwzjs2FNUgoKFBwnMk/ASaAv6C58uYjtUE5gjaz+kT//RrnrzBLwG561+VmMPiWIkoVmR9fMp9oQpZhOaa1pFSTXIuvoakkfo/cc8BBdu47BuUIZdF0bmuybmHBxZq7aXa5OY8cGYO6Ep24dbG/thznuijp3CtLThMinJmme92TLptivwXr+NtVAB5z1gPb6RalPyJpZD8a8AAwBoDGzHPr0K4wAAAABJRU5ErkJggg==);
  width: 20px;
}
.gantt_delete_btn {
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyhpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuNi1jMTMyIDc5LjE1OTI4NCwgMjAxNi8wNC8xOS0xMzoxMzo0MCAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZVJlZiMiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENDIDIwMTUuNSAoV2luZG93cykiIHhtcE1NOkluc3RhbmNlSUQ9InhtcC5paWQ6MTZBMjlFNkU2ODc5MTFFNjkxOTRGRTZGMDkwMENCMUIiIHhtcE1NOkRvY3VtZW50SUQ9InhtcC5kaWQ6MTZBMjlFNkY2ODc5MTFFNjkxOTRGRTZGMDkwMENCMUIiPiA8eG1wTU06RGVyaXZlZEZyb20gc3RSZWY6aW5zdGFuY2VJRD0ieG1wLmlpZDoxNkEyOUU2QzY4NzkxMUU2OTE5NEZFNkYwOTAwQ0IxQiIgc3RSZWY6ZG9jdW1lbnRJRD0ieG1wLmRpZDoxNkEyOUU2RDY4NzkxMUU2OTE5NEZFNkYwOTAwQ0IxQiIvPiA8L3JkZjpEZXNjcmlwdGlvbj4gPC9yZGY6UkRGPiA8L3g6eG1wbWV0YT4gPD94cGFja2V0IGVuZD0iciI/PpeemjIAAABMSURBVHjaYvx/hAEbqAfiBjQxEL8RXSETkZphBtSjCzJCXfCfgTzAyMRAIWCBmUSpATBArFcY8QUiSWDUgFEDsBlwhAg9R5E5AAEGAFNACzFr4MENAAAAAElFTkSuQmCC);
  margin-top: 2px;
  width: 20px;
}
.gantt_cal_cover {
  width: 100%;
  height: 100%;
  position: absolute;
  z-index: 10000;
  top: 0px;
  left: 0px;
  background-color: black;
  opacity: 0.1;
  filter: progid:DXImageTransform.Microsoft.Alpha(opacity=10);
}
.gantt_custom_button {
  padding: 0px 3px 0px 3px;
  font-family: "arial";
  font-size: 14px;
  font-weight: normal;
  margin-right: 10px;
  margin-top: -5px;
  cursor: pointer;
  float: right;
  height: 21px;
  width: 90px;
  border: 1px solid #CECECE;
  text-align: center;
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  -ms-border-radius: 4px;
  -o-border-radius: 4px;
  border-radius: 4px;
}
.gantt_custom_button div {
  cursor: pointer;
  float: none;
  height: 21px;
  line-height: 21px;
  vertical-align: middle;
}
.gantt_custom_button div:first-child {
  display: none;
}
.gantt_cal_light_wide {
  width: 580px;
  padding: 2px 4px;
}
.gantt_cal_light_wide .gantt_cal_larea {
  -moz-box-sizing: border-box;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  border: 1px solid #c1c1c1;
}
.gantt_cal_light_wide .gantt_cal_lsection {
  border: 0px;
  float: left;
  text-align: right;
  width: 80px;
  height: 20px;
  padding: 5px 10px 0px 0px;
}
.gantt_cal_light_wide .gantt_wrap_section {
  position: relative;
  padding: 10px 0;
  overflow: hidden;
  border-bottom: 1px solid #7e7e7e;
}
.gantt_cal_light_wide .gantt_section_time {
  overflow: hidden;
  padding-top: 2px !important;
  padding-right: 0px;
  height: 20px !important;
}
.gantt_cal_light_wide .gantt_cal_ltext {
  padding-right: 0px;
}
.gantt_cal_light_wide .gantt_cal_larea {
  padding: 0 10px;
  width: 100%;
}
.gantt_cal_light_wide .gantt_section_time {
  background: transparent;
}
.gantt_cal_light_wide .gantt_cal_checkbox label {
  padding-left: 0px;
}
.gantt_cal_light_wide .gantt_cal_lsection .gantt_fullday {
  float: none;
  margin-right: 0px;
  font-weight: bold;
  cursor: pointer;
}
.gantt_cal_light_wide .gantt_custom_button {
  position: absolute;
  top: 0;
  right: 0;
  margin-top: 2px;
}
.gantt_cal_light_wide .gantt_repeat_right {
  margin-right: 55px;
}
.gantt_cal_light_wide.gantt_cal_light_full {
  width: 738px;
}
.gantt_cal_wide_checkbox input {
  margin-top: 8px;
  margin-left: 14px;
}
.gantt_cal_light input {
  font-size: 14px;
}
.gantt_section_time {
  background-color: white;
  white-space: nowrap;
  padding: 2px 10px 5px;
  padding-top: 2px !important;
}
.gantt_section_time .gantt_time_selects {
  float: left;
  height: 25px;
}
.gantt_section_time .gantt_time_selects select {
  height: 23px;
  padding: 2px;
  border: 1px solid #c1c1c1;
}
.gantt_duration {
  width: 100px;
  height: 23px;
  float: left;
  white-space: nowrap;
  margin-left: 20px;
  line-height: 23px;
}
.gantt_duration .gantt_duration_value,
.gantt_duration .gantt_duration_dec,
.gantt_duration .gantt_duration_inc {
  -moz-box-sizing: border-box;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  text-align: center;
  vertical-align: top;
  height: 100%;
  border: 1px solid #c1c1c1;
}
.gantt_duration .gantt_duration_value {
  width: 40px;
  padding: 3px 4px;
  border-left-width: 0;
  border-right-width: 0;
}
.gantt_duration .gantt_duration_dec,
.gantt_duration .gantt_duration_inc {
  width: 20px;
  padding: 1px;
  padding-bottom: 1px;
  background: #000;
}
.gantt_duration .gantt_duration_dec {
  -moz-border-top-left-radius: 4px;
  -moz-border-bottom-left-radius: 4px;
  -webkit-border-top-left-radius: 4px;
  -webkit-border-bottom-left-radius: 4px;
  border-top-left-radius: 4px;
  border-bottom-left-radius: 4px;
}
.gantt_duration .gantt_duration_inc {
  margin-right: 4px;
  -moz-border-top-right-radius: 4px;
  -moz-border-bottom-right-radius: 4px;
  -webkit-border-top-right-radius: 4px;
  -webkit-border-bottom-right-radius: 4px;
  border-top-right-radius: 4px;
  border-bottom-right-radius: 4px;
}
.gantt_resources {
  max-height: 150px;
  height: auto;
  overflow-y: auto;
}
.gantt_resource_row {
  display: block;
  padding: 10px 0;
  border-bottom: 1px solid #7e7e7e;
  cursor: pointer;
}
.gantt_resource_row input[type=checkbox]:not(:checked),
.gantt_resource_row input[type=checkbox]:not(:checked) ~ div {
  opacity: 0.5;
}
.gantt_resource_toggle {
  vertical-align: middle;
}
.gantt_resources_filter .gantt_resources_filter_input {
  padding: 1px 2px 1px 2px;
  box-sizing: border-box;
}
.gantt_resources_filter .switch_unsetted {
  vertical-align: middle;
}
.gantt_resource_cell {
  display: inline-block;
}
.gantt_resource_cell.gantt_resource_cell_checkbox {
  width: 24px;
  max-width: 24px;
  min-width: 24px;
  vertical-align: middle;
}
.gantt_resource_cell.gantt_resource_cell_label {
  width: 40%;
  max-width: 40%;
  vertical-align: middle;
}
.gantt_resource_cell.gantt_resource_cell_value {
  width: 30%;
  max-width: 30%;
  vertical-align: middle;
}
.gantt_resource_cell.gantt_resource_cell_value input,
.gantt_resource_cell.gantt_resource_cell_value select {
  width: 80%;
  vertical-align: middle;
  padding: 1px 2px 1px 2px;
  box-sizing: border-box;
}
.gantt_resource_cell.gantt_resource_cell_unit {
  width: 10%;
  max-width: 10%;
  vertical-align: middle;
}
.gantt_resource_early_value {
  opacity: 0.8;
  font-size: 0.9em;
}
/* Quick info */
.gantt_cal_quick_info {
  border: 1px solid #FFFF00;
  border-radius: 6px;
  position: absolute;
  z-index: 300;
  border-color: #fff;
  -webkit-box-shadow: inset 0px 0px 0px 4px #0505ff;
  -moz-box-shadow: inset 0px 0px 0px 4px #0505ff;
  box-shadow: inset 0px 0px 0px 4px #0505ff;
  padding: 4px;
  background-color: #000;
  width: 300px;
  transition: left 0.5s ease, right 0.5s;
  -moz-transition: left 0.5s ease, right 0.5s;
  -webkit-transition: left 0.5s ease, right 0.5s;
  -o-transition: left 0.5s ease, right 0.5s;
}
.gantt_no_animate {
  transition: none;
  -moz-transition: none;
  -webkit-transition: none;
  -o-transition: none;
}
.gantt_cal_quick_info.gantt_qi_left .gantt_qi_big_icon {
  float: right;
}
.gantt_cal_qi_title {
  -webkit-border-top-left-radius: 6px;
  -webkit-border-bottom-left-radius: 0;
  -webkit-border-top-right-radius: 6px;
  -webkit-border-bottom-right-radius: 0;
  -moz-border-radius-topleft: 6px;
  -moz-border-radius-bottomleft: 0;
  -moz-border-radius-topright: 6px;
  -moz-border-radius-bottomright: 0;
  border-top-left-radius: 6px;
  border-bottom-left-radius: 0;
  border-top-right-radius: 6px;
  border-bottom-right-radius: 0;
  padding: 5px 0px 8px 12px;
  color: #fff;
  background-color: #000;
  border-bottom: 1px solid #FFFF00;
}
.gantt_cal_qi_tdate {
  font-size: 14px;
  font-weight: bold;
}
.gantt_cal_qi_tcontent {
  font-size: 14px;
}
.gantt_cal_qi_content {
  padding: 16px 8px;
  font-size: 14px;
  color: #fff;
  overflow: hidden;
}
.gantt_cal_qi_controls {
  -webkit-border-top-left-radius: 0;
  -webkit-border-bottom-left-radius: 6px;
  -webkit-border-top-right-radius: 0;
  -webkit-border-bottom-right-radius: 6px;
  -moz-border-radius-topleft: 0;
  -moz-border-radius-bottomleft: 6px;
  -moz-border-radius-topright: 0;
  -moz-border-radius-bottomright: 6px;
  border-top-left-radius: 0;
  border-bottom-left-radius: 6px;
  border-top-right-radius: 0;
  border-bottom-right-radius: 6px;
  padding-left: 7px;
}
.gantt_cal_qi_controls .gantt_menu_icon {
  margin-top: 6px;
  background-repeat: no-repeat;
}
.gantt_cal_qi_controls .gantt_menu_icon.icon_edit {
  width: 20px;
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAACNiR0NAAAABmJLR0QA/wD/AP+gvaeTAAAACXBIWXMAAA7EAAAOxAGVKw4bAAAAB3RJTUUH3QYFCjI5ZQj5bAAAAFNJREFUOMvt0zEOACAIA0DkwTymH8bJTRTKZGJXyaWEKPKTCQAH4Ls37cItcDUzsxHNDLZNhCq7Gt1wh9ErV7EjyGAhyGLphlnsClWuS32rn0czAV+vNGrM/LBtAAAAAElFTkSuQmCC);
}
.gantt_cal_qi_controls .gantt_menu_icon.icon_delete {
  width: 20px;
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyhpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuNi1jMTMyIDc5LjE1OTI4NCwgMjAxNi8wNC8xOS0xMzoxMzo0MCAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZVJlZiMiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENDIDIwMTUuNSAoV2luZG93cykiIHhtcE1NOkluc3RhbmNlSUQ9InhtcC5paWQ6MTZBMjlFNkU2ODc5MTFFNjkxOTRGRTZGMDkwMENCMUIiIHhtcE1NOkRvY3VtZW50SUQ9InhtcC5kaWQ6MTZBMjlFNkY2ODc5MTFFNjkxOTRGRTZGMDkwMENCMUIiPiA8eG1wTU06RGVyaXZlZEZyb20gc3RSZWY6aW5zdGFuY2VJRD0ieG1wLmlpZDoxNkEyOUU2QzY4NzkxMUU2OTE5NEZFNkYwOTAwQ0IxQiIgc3RSZWY6ZG9jdW1lbnRJRD0ieG1wLmRpZDoxNkEyOUU2RDY4NzkxMUU2OTE5NEZFNkYwOTAwQ0IxQiIvPiA8L3JkZjpEZXNjcmlwdGlvbj4gPC9yZGY6UkRGPiA8L3g6eG1wbWV0YT4gPD94cGFja2V0IGVuZD0iciI/PpeemjIAAABMSURBVHjaYvx/hAEbqAfiBjQxEL8RXSETkZphBtSjCzJCXfCfgTzAyMRAIWCBmUSpATBArFcY8QUiSWDUgFEDsBlwhAg9R5E5AAEGAFNACzFr4MENAAAAAElFTkSuQmCC);
}
.gantt_qi_big_icon {
  font-size: 13px;
  border-radius: 4px;
  font-weight: bold;
  background: #000;
  margin: 5px 9px 8px 0px;
  min-width: 60px;
  line-height: 32px;
  vertical-align: middle;
  padding: 0px 10px 0px 5px;
  cursor: pointer;
  border: 1px solid #FFFF00;
}
.gantt_cal_qi_controls div {
  float: left;
  height: 32px;
  text-align: center;
  line-height: 32px;
}
.gantt_tooltip {
  padding: 10px;
  position: absolute;
  z-index: 50;
  white-space: nowrap;
}
.gantt_resource_marker {
  position: absolute;
  text-align: center;
  font-size: 14px;
  color: #FFF;
}
.gantt_resource_marker_ok {
  background: rgba(78, 208, 134, 0.75);
}
.gantt_resource_marker_overtime {
  background: rgba(255, 134, 134, 0.69);
}
.gantt_histogram_label {
  width: 100%;
  height: 100%;
  position: absolute;
  z-index: 1;
  font-weight: bold;
  font-size: 14px;
}
.gantt_histogram_fill {
  background-color: rgba(41, 157, 180, 0.2);
  width: 100%;
  position: absolute;
  bottom: 0;
}
.gantt_histogram_hor_bar {
  height: 1px;
  position: absolute;
  background: #299DB4;
  margin-top: -1px;
  margin-left: -1px;
}
.gantt_histogram_vert_bar {
  width: 1px;
  position: absolute;
  background: #299DB4;
  margin-left: -1px;
}
.gantt_histogram_cell {
  position: absolute;
  text-align: center;
  font-size: 14px;
  color: #000000;
}
.gantt_marker {
  height: 100%;
  width: 2px;
  top: 0;
  position: absolute;
  text-align: center;
  background-color: rgba(255, 0, 0, 0.4);
  -moz-box-sizing: border-box;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}
.gantt_marker .gantt_marker_content {
  padding: 5px;
  background: inherit;
  color: white;
  position: absolute;
  font-size: 12px;
  line-height: 12px;
  opacity: 0.8;
}
.gantt_marker_area {
  position: absolute;
  top: 0;
  left: 0;
}
.gantt_grid_editor_placeholder {
  position: absolute;
}
.gantt_grid_editor_placeholder > div,
.gantt_grid_editor_placeholder input,
.gantt_grid_editor_placeholder select {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
}
.gantt_row_placeholder div {
  opacity: 0.5;
}
.gantt_row_placeholder .gantt_file,
.gantt_row_placeholder .gantt_add {
  display: none;
}
.gantt_drag_marker.gantt_grid_dnd_marker {
  background-color: transparent;
  transition: all 0.1s ease ;
}
.gantt_grid_dnd_marker_line {
  height: 4px;
  width: 100%;
  background-color: #3498db;
}
.gantt_grid_dnd_marker_line::before {
  background: #fff;
  width: 12px;
  height: 12px;
  box-sizing: border-box;
  border: 3px solid #3498db;
  border-radius: 6px;
  content: "";
  line-height: 1px;
  display: block;
  position: absolute;
  margin-left: -11px;
  margin-top: -4px;
  pointer-events: none;
}
.gantt_grid_dnd_marker_folder {
  height: 100%;
  width: 100%;
  position: absolute;
  pointer-events: none;
  box-sizing: border-box;
  box-shadow: 0 0 0px 2px #3f98db inset;
  background: transparent;
}
.gantt_overlay_area {
  position: absolute;
  height: inherit;
  width: inherit;
  top: 0;
  left: 0;
  display: none;
}
.gantt_overlay {
  position: absolute;
  left: 0;
  top: 0;
  height: inherit;
  width: inherit;
}
.gantt_layout_content {
  width: 100%;
  overflow: auto;
  box-sizing: border-box;
}
.gantt_layout_cell {
  position: relative;
  box-sizing: border-box;
}
.gantt_layout_cell > .gantt_layout_header {
  background: #33aae8;
  color: white;
  font-size: 17px;
  padding: 5px 10px;
  box-sizing: border-box;
}
.gantt_layout_header.collapsed_x {
  background: #a9a9a9;
}
.gantt_layout_header.collapsed_x .gantt_header_arrow:before {
  content: "\21E7";
}
.gantt_layout_header.collapsed_y {
  background: #a9a9a9;
}
.gantt_layout_header.collapsed_y .gantt_header_arrow:before {
  content: "\21E9";
}
.gantt_layout_header {
  cursor: pointer;
}
.gantt_layout_header .gantt_header_arrow {
  float: right;
  text-align: right;
}
.gantt_layout_header .gantt_header_arrow:before {
  content: "\21E6";
}
.gantt_layout_header.vertical .gantt_header_arrow:before {
  content: "\21E7";
}
.gantt_layout_outer_scroll_vertical .gantt_layout_content {
  overflow-y: hidden;
}
.gantt_layout_outer_scroll_horizontal .gantt_layout_content {
  overflow-x: hidden;
}
.gantt_layout_x > .gantt_layout_cell {
  display: inline-block;
  vertical-align: top;
}
.gantt_layout_x {
  white-space: nowrap;
}
.gantt_resizing {
  opacity: 0.7;
  background: #f2f2f2;
}
.gantt_layout_cell_border_right.gantt_resizer {
  overflow: visible;
  border-right: 0;
}
.gantt_resizer {
  cursor: e-resize;
  position: relative;
}
.gantt_resizer_y {
  cursor: n-resize;
}
.gantt_resizer_stick {
  background: #33aae8;
  z-index: 9999;
  position: absolute;
  top: 0;
  width: 100%;
}
.gantt_resizer_x .gantt_resizer_x {
  position: absolute;
  width: 20px;
  height: 100%;
  margin-left: -10px;
  top: 0;
  left: 0;
  z-index: 1;
}
.gantt_resizer_y .gantt_resizer_y {
  position: absolute;
  height: 20px;
  width: 100%;
  top: -10px;
  left: 0;
  z-index: 1;
}
.gantt_resizer_error {
  background: indianred!important;
}
.gantt_noselect {
  -webkit-user-select: none;
  user-select: none;
}
.gantt_layout_cell_border_left {
  border-left: 1px solid #FFFF00;
}
.gantt_layout_cell_border_right {
  border-right: 1px solid #FFFF00;
}
.gantt_layout_cell_border_top {
  border-top: 1px solid #FFFF00;
}
.gantt_layout_cell_border_bottom {
  border-bottom: 1px solid #FFFF00;
}
.gantt_layout_cell_border_transparent {
  border-color: transparent;
}
.gantt_window {
  position: absolute;
  top: 50%;
  left: 50%;
  z-index: 999999999;
  background: white;
}
.gantt_window_content {
  position: relative;
}
.gantt_window_content_header {
  background: #39c;
  color: #ffffff;
  height: 33px;
  padding: 10px 10px 0 10px;
  border-bottom: solid 2px #ffffff;
  position: relative;
}
.gantt_window_content_header_text {
  padding-left: 10%;
}
.gantt_window_content_header_buttons {
  position: absolute;
  top: 10px;
  right: 10px;
}
.gantt_window_content_header_buttons:hover {
  color: #000000;
  cursor: pointer;
}
.gantt_window_content_resizer {
  position: absolute;
  width: 15px;
  height: 15px;
  bottom: 0;
  line-height: 15px;
  right: -1px;
  text-align: center;
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA8AAAAPCAMAAAAMCGV4AAAABlBMVEUAAAAAAAClZ7nPAAAAAXRSTlMAQObYZgAAABZJREFUeAFjIAUwUshlpJDLSIhLGAAACQ4AFk79JaMAAAAASUVORK5CYII=);
  cursor: nw-resize;
  z-index: 999;
}
.gantt_window_content_frame {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.1);
  z-index: 9999;
}
.gantt_window_drag {
  cursor: pointer!important;
}
.gantt_window_resizing {
  overflow: visible;
}
.gantt_window_resizing_body {
  overflow: hidden!important;
}
.gantt_window_modal {
  background: rgba(0, 0, 0, 0.1);
  z-index: 9999;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  position: fixed;
}
.gantt_container,
.gantt_cal_light,
.gantt_message_area,
.gantt_modal_box,
.gantt_cal_quick_info,
.gantt_tooltip {
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.gantt_noselect {
  -moz-user-select: -moz-none;
  -webkit-user-select: none;
  -ms-user-select: none;
  user-select: none;
}
.gantt_drag_marker {
  position: absolute;
  top: -1000px;
  left: -1000px;
  font-family: "arial";
  font-size: 14px;
}
.gantt_drag_marker .gantt_tree_indent,
.gantt_drag_marker .gantt_tree_icon.gantt_blank,
.gantt_drag_marker .gantt_tree_icon.gantt_open,
.gantt_drag_marker .gantt_tree_icon.gantt_close {
  display: none;
}
.gantt_drag_marker,
.gantt_drag_marker .gantt_row.odd {
  background-color: #000;
}
.gantt_drag_marker .gantt_row {
  border-left: 1px solid #656565;
  border-top: 1px solid #656565;
}
.gantt_drag_marker .gantt_cell {
  border-color: #656565;
}
.gantt_row.gantt_over,
.gantt_task_row.gantt_over {
  background-color: #0070fe;
}
.gantt_row.gantt_transparent .gantt_cell {
  opacity: 0.7;
}
.gantt_task_row.gantt_transparent {
  background-color: #d0ddff;
}
.gantt_popup_button.gantt_delete_button {
  font-weight: bold;
  border-style: solid;
  border-width: 2px;
  border-color: #76ff03;
  background: #000;
  color: #76ff03;
}
.gantt_container_resize_watcher {
  background: transparent;
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0px;
  left: 0px;
  z-index: -1;
  pointer-events: none;
  border: 0;
  box-sizing: border-box;
  opacity: 0;
}


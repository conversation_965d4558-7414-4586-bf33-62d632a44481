{"name": "cookie", "version": "1.4.0", "title": "<PERSON><PERSON><PERSON><PERSON>", "description": "A simple, lightweight jQuery plugin for reading, writing and deleting cookies.", "author": {"name": "<PERSON>", "url": "https://github.com/carhartl"}, "maintainers": [{"name": "<PERSON>", "url": "https://github.com/carhartl"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/FagnerMartinsBrack"}], "licenses": [{"type": "MIT", "url": "https://raw.github.com/carhartl/jquery-cookie/master/MIT-LICENSE.txt"}], "dependencies": {"jquery": ">=1.2"}, "bugs": "https://github.com/carhartl/jquery-cookie/issues", "homepage": "https://github.com/carhartl/jquery-cookie", "docs": "https://github.com/carhartl/jquery-cookie#readme"}
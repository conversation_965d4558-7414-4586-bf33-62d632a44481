$(document).ready(function(){
	$('#clear_filtre').click(function() {
		$('#qrt_filter_form select').prop("selectedIndex",0);
		$('#qrt_filter_form input[type=text]').val('');
		$('#qrt_filter_form').submit();
	});

	$('#qrt_filter_form select').each(function () {
		if ($(this).prop("selectedIndex") > 0)
			$(this).css("background-color", "#f0d2b5");
	});

	$('#qrt_filter_form input[type=text]').each(function () {
		if ($(this).val() != '')
			$(this).css("background-color", "#f0d2b5");
	});

	$('#qrt_filter_form select').change(function () {
		if ($(this).prop("selectedIndex") > 0)
			$(this).css("background-color", "#f0d2b5");
		else
			$(this).css("background-color", "#ffffff");
	});

	$('#qrt_filter_form input[type=text]').change(function () {
		if ($(this).val() != '')
			$(this).css("background-color", "#f0d2b5");
		else
			$(this).css("background-color", "#ffffff");
	});

	$('#selectionner_tout').change(function() {
		if ($(this).prop("checked"))
			$('.selection_items').prop("checked", true);
		else
			$('.selection_items').prop("checked", false);
	});

	// Calendrier pour QRT
	$("#ts_fin_discussion_max").datetimepicker({
		timepicker:false,
		format:'Y-m-d',
		scrollMonth : false,
		scrollInput : false
	});
});

class Qrt {
	constructor(qrt_id, btn, context) {
		this.qrt_id = qrt_id;
		this.btn = btn
		this.context = context
		this.init();
	}

	init(){
		const _that = this;

		if(_that.context !== undefined) {
			_that.fil_discussion = $('<div class="fil_discussion_inline newclearfix">');
		} else {
			_that.fil_discussion = $('<div class="fil_discussion">');
		}
		$.post('index.php?section=qrt&module=traitement', {
			get_qrt: 1,
			id: _that.qrt_id
		}, function(qrt){
			_that._renderFilDiscussion(qrt);
			if(parent.document)
			{
				if(_that.context !== undefined) {
					$(parent.document).find('div.fil_discussion_inline').remove();
					$(parent.document).find('div.qrt').each(function(){
						if(this.id !== _that.context)
						{
							$(this).hide();
						}
					});
					$('br').remove();
					$(parent.document).find('#' + _that.context).append(_that.fil_discussion).append('<br style="clear: both;" />').slideToggle(400);
				} else {
					$(parent.document).find('div.fil_discussion').remove();
					$(parent.document).find('body').append(_that.fil_discussion);
				}

			}
			else
			{
				if(_that.context !== undefined) {
					$('div.fil_discussion_inline').remove();
					$('div.qrt').each(function(){
						if(this.id !== _that.context)
						{
							$(this).hide();
						}
					});
					$('br').remove();
					$('#' + _that.context).append(_that.fil_discussion).append('<br style="clear: both;" />').slideToggle(400);
				} else {
					$('div.fil_discussion').remove();
					$('body').append(_that.fil_discussion);
				}
			}
			_that._bindDomEvents();
		}, 'json');
	}

	_bindDomEvents(){
		const _that = this;
		_that.fil_discussion.on('click', 'button.close_btn', function(){
			if(_that.context !== undefined) {
				$('#' + _that.context).slideToggle(400);
			} else {
				_that.fil_discussion.remove();
			}
			return false;
		});

		_that.fil_discussion.on('click', 'button.repondre', function(){
			let url = "index.php?section=qrt&module=traitement";
			$.post(url, { nouvelle_reponse: _that.fil_discussion.find('textarea.nouvelle_reponse').val(), question_id: _that.qrt_id }, function(qrt){
				_that._renderFilDiscussion(qrt);
			}, 'json');
			return false;
		});

		_that.fil_discussion.on('click', 'button.conclure_discussion', function(){

			if(confirm('Êtes-vous certain de vouloir conclure la discussion?'))
			{
				let url = "index.php?section=qrt&module=traitement";
				$.post(url, { conclure_discussion: 1, question_id: _that.qrt_id }, function(qrt){
					_that._renderFilDiscussion(qrt);
				}, 'json');
			}
			return false;
		});

		_that.fil_discussion.on('click', 'button.reactiver_discussion', function(){

			if(confirm('Êtes-vous certain de vouloir réactiver la discussion?'))
			{
				let url = "index.php?section=qrt&module=traitement";
				$.post(url, { reactiver_discussion: 1, question_id: _that.qrt_id }, function(qrt){
					_that._renderFilDiscussion(qrt);
				}, 'json');
			}
			return false;
		});

		_that.fil_discussion.on('click', 'button.aknowledge', function(){
			let url = "index.php?section=qrt&module=traitement";
			$.post(url, { aknowledge: 1, question_id: _that.qrt_id }, function(qrt){
				_that._renderFilDiscussion(qrt);
			}, 'json');
			return false;
		});
	}

	_renderFilDiscussion(qrt){
		const _that = this;

		let html =	'<h4 style="text-align: right; font-weight: bold;">' + qrt.code + '</h4>' +
			'<div class="reponses_bloc">' +
				'<div class="question_bloc">' +
					'<div class="reponse">' + qrt.question + '</div>' +
					'<div class="repondant">' + qrt.ts_ajout + ' par ' + qrt.demandeur + '</div>';
					if(qrt.nb_notifications > 0) {
						html = html + '<div class="notification"><img src="css/images/icons/dark/alert_red.png" /></div>';
					}

			html +=
				'</div>' +
				'<div class="messages"></div>' +
				'<div class="reponses">';
				$.each(qrt.reponses, function( i, reponse ) {
					html = html +
						'<div class="reponse_bloc ' + (reponse.type_repondant) + '">' +
							'<div class="reponse">' + reponse.reponse + '</div>' +
							'<div class="repondant">' + reponse.ts_ajout + ' par ' + reponse.repondant + '</div>';
							if(reponse.nb_notifications > 0) {
								html = html + '<div class="notification"><img src="css/images/icons/dark/alert_red.png" /></div>';
							}
					html +=
						'</div>';
				});
			html +=
				'</div>';
		if(qrt.question_statut_id == 1)
		{
			html +=
				'<div class="question_bloc">' +
					'<div class="reponse">Fin de la discussion</div>' +
					'<div class="repondant">' + qrt.ts_fin_discussion + ' par ' + qrt.conclueur + '</div>' +
				'</div>';
		}
		let disabled_class = ((qrt.question_statut_id !== 1 && qrt.is_usager_can_reponse == 1) || qrt.is_usager_demandeur == 1) && (qrt.question_statut_id == 2 || qrt.question_statut_id == 3) ? '' : ' disabled';
		html +=
			'<div class="repondre">' +
			'<textarea class="nouvelle_reponse ' + disabled_class + '"></textarea>' +
			'<button class="repondre ' + disabled_class + '">Envoyer</button>';
		if((qrt.question_statut_id == 2 || qrt.question_statut_id == 3) && qrt.can_conclure_qrt == 1) {
			html = html + '<button class="conclure_discussion">Conclure</button>';
		} else if (qrt.can_conclure_qrt == 1) {
			html = html + '<button class="reactiver_discussion">Réactiver</button>';
		}
		html +=
			'<button class="aknowledge ' + (qrt.nb_notifications_all > 0 ? '' : 'disabled') + '">Lu</button>' +
			'<button class="close_btn">Sortir</button>' +
			'</div>';
		html +=
			'</div><br style="clear: both;" />';

		$(_that.btn).find('span.extra').remove();
		if(qrt.nb_notifications_all > 0)
		{
			$(_that.btn).append('<span class="extra">' + qrt.nb_notifications_all + '</span>');
		}

		/*let is_init = _that.fil_discussion.html().length === 0;
		let nb_reponses_blocs_avant = _that.fil_discussion.find('reponse_bloc').length;*/

		_that.fil_discussion.html(html);

		/*let nb_reponses_blocs_apres = _that.fil_discussion.find('reponse_bloc').length;

		let nb_nouvelles_reponses = nb_reponses_blocs_apres - nb_reponses_blocs_avant;
		if(!is_init && nb_nouvelles_reponses > 0)
		{
			_that.fil_discussion.find('messages').html(nb_nouvelles_reponses + ' nouveau' + (nb_nouvelles_reponses > 1 ? 'x' : '') + 'message' + (nb_nouvelles_reponses > 1 ? 's' : ''));
		}*/

		_that.fil_discussion.find('div.reponses').animate({scrollTop: 1000000}, 10);
	}
}
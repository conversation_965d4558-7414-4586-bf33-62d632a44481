<?php
$doNotValidIp = 1;
$doNotCheckLogin = 1;
include('config/inc.php');

$success = false;
if (isset($_GET['validationCode'])) {
    if (IpManagement::validateValidationCode($_SERVER['REMOTE_ADDR'], $_GET['validationCode'])) {
	    IpManagement::whiteListIp($_SERVER['REMOTE_ADDR']);
	    erase_session();
	    header('Location: login.php?mess=1');
    } else {
	    erase_session();
	    header('Location: login.php');
    }
	exit;
} else if(isset($_POST['email']) && isset($_POST['password'])) {
	valideIp();

	$isDBMaster = isDBMaster();

	$dbs_can_access = array();
	if (!$IsLoginEnv) {
		$success = login($_POST['email'], $_POST['password'], $CodeClient, true);
	}
	if (!$success['statut']) {
		if ($IsLoginEnv) {
			unset($Mapping_DB_Clients['master']);
			foreach ($Mapping_DB_Clients as $codeClient_tmp => $datadb) {
				$db_name = $datadb['db'];
				$usager_id = find_successfull_login($_POST['email'], $_POST['password'], $codeClient_tmp);
				if (isset($usager_id)) {
					$dbs_can_access[] = array('usager_id' => $usager_id, 'code_client' => $codeClient_tmp);
					$success = ['statut' => true, 'code' => 'reussite'];
				}
			}
		} else {
			$success = login($_POST['email'], $_POST['password'], $CodeClient, false);
		}
	}

	if (!$success['statut']) {
		$_POST['password'] = "[MOT DE PASSE]";
	    $message = "Adresse IP à surveiller: ".$_SERVER['REMOTE_ADDR'].getDebug($_POST).getDebug($_SERVER);
	    envoiCourriel(['<EMAIL>'], 'Échec de connection', $message);
		erase_session();

        $err = $success['code'] == 'checkbrute' ? 3 : 1;
		header('Location: login.php?err=' . $err);
		exit;
	} else if (count($dbs_can_access) == 1) {
		$data = $dbs_can_access[0];
		$temp_token = create_temp_token($data['usager_id'], $data['code_client']);

		erase_session();
		if ($isDev) {
			header('Location: http://' . ($data['code_client'] == '192' ? '************' : 'localhost') . '/docmatic/temp.php?token=' . $temp_token);
			exit;
		} else {
			header('Location: https://' . $data['code_client'] . '.docmatic.ca/temp.php?token=' . $temp_token);
			exit;
		}
	} else if (count($dbs_can_access) > 1) {
		$_SESSION['dbs_can_access'] = $dbs_can_access;
		header('Location: index.php?section=landing_env&module=index');
		exit;
	} else {
		if (isMaster() && $isDBMaster) {
            FiltredCache::initPersonalizedTmpPath(true);
			header('Location: index.php?section=admin&module=index&type=master');
			die();
		} else {
		    if (isMobile() && in_array(ClientsMaster::instance()->releve_mobile, ['equipements', 'inventaire'])) {
                FiltredCache::initPersonalizedTmpPath(true);
			    header('Location: index.php?section=landing&module=mobile');
			    die();
            } else {
                $usager = getUsager();
                if ($usager->is_mobile_only == 1) {
                    if (in_array(ClientsMaster::instance()->releve_mobile, ['equipements', 'inventaire'])) {
                        FiltredCache::initPersonalizedTmpPath(true);
                        header('Location: index.php?section=landing&module=mobile');
                        die();
                    }
                    erase_session();
                    header('Location: login.php');
                    die();
                } else {
                    FiltredCache::initPersonalizedTmpPath(true);
                    header('Location: index.php?section=landing&module=index');
                    die();
                }
            }
		}
	}
}

erase_session();
$page = 'login';
include('config/header.inc.php');

$langue = $_COOKIE['lang'] ?? 'fr_en';

$texte_email = 'Courriel / Email';
$texte_password = 'Mot de passe / Password';
$texte_bouton = "Réinitialiser / Reset";
$texte_bouton_cancel = "Annuler / Cancel";
$texte_msg = 'Un courriel vous sera envoyé afin de créer un nouveau mot de passe.<br />
              We will send you a message in order to create a new password.';
$texte_oublie = 'Oublié? / Forgotten?';
$texte_entrer_email_associe = "Veuillez entrer l'adresse courriel associée au compte usager pour lequel vous désirez récupérer l'accès.<br /><br />Please enter your email address used to recover the access.";
$texte_retour_login = "Access / Login";
$texte_retour_perdu = "Recommencer / Retry";
$texte_email_envoye = "- Un courriel vous a été envoyé.<br />- An email has been sent.";
$texte_no_match = "L'adresse courriel que vous avez entrée ne correspond à aucun compte usager. Veuillez vérifier vos informations de connexion ou contacter le support DocMatic à l'adresse <EMAIL><br /><br />Email you entered doesn't match with any account. Please verify your email or contact DocMatic support <NAME_EMAIL>";

if ($langue == 'fr') {
	$texte_email = 'Courriel';
	$texte_password = 'Mot de passe';
	$texte_bouton = "Ré-initialiser";
	$texte_oublie = "Oublié?";
	$text_bouton_cancel = "Annuler";
	$texte_entrer_email_associe = "Veuillez entrer l'adresse courriel associée au compte usager pour lequel vous désirez récupérer l'accès.";
	$texte_retour_login = "Access";
	$texte_retour_perdu = "Recommencer";
	$texte_email_envoye = "Un courriel vous a été envoyé. Si vous ne le trouvez pas dans votre boîte courriel, veuillez vérifier vos informations de connexion ou contacter le support DocMatic à l'adresse <EMAIL>.";
	$texte_no_match = "L'adresse courriel que vous avez entrée ne correspond à aucun compte usager. Veuillez vérifier vos informations de connexion ou contacter le support DocMatic à l'adresse <EMAIL>";
} else if ($langue == 'en') {
	$texte_email = 'Email';
	$texte_password = 'Password';
	$texte_bouton = "Reset";
	$text_bouton_cancel = "Cancel";
	$texte_oublie = "Forgotten?";
	$texte_entrer_email_associe = "Please enter your email address used to recover the access.";
	$texte_retour_login = "Login";
	$texte_retour_perdu = "Retry";
	$texte_email_envoye = "An email has been sent. If you can't find it in your inbox, please verify your email or contact DocMatic support <NAME_EMAIL>";
	$texte_no_match = "Email you entered doesn't match with any account. Please verify your email or contact DocMatic support <NAME_EMAIL>";
}

?>
<script>
$(document).ready(function(){
	$('#email').focus();

  $("#btn_oublie").on('click', function(e) {
  	<?
      if ($IsLoginEnv) {
      ?>
          alert('<?=txt('La récupération du mot de passe n’est pas autorisée à partir la présente page de connexion « multi-environnements » (login.docmatic.ca).\n\nCette fonction n’est autorisée qu’à partir de la page de connexion spécifique à l’environnement auquel vous désirez accéder (ex : votre_environnement.docmatic.ca).', false)?>');
      <?
	  } else {
	  ?>
          e.preventDefault();
          $("#content").hide();
          $("#resetpw").show();
	  <?
	  }
      ?>
  });
  $("#fermerreset").on('click', function(e) {
    e.preventDefault();
    $("#resetpw").hide();
    $("#content").show();

  });
  $("#retouremailperdu").on('click', function(e) {
    e.preventDefault();
    $("#afteremailsent").hide();
    $("#content").hide();
    $("#resetpw").show();

  });
    $("#retourlogin").on('click', function(e) {
    e.preventDefault();
    $("#afteremailsent").hide();
    $("#resetpw").hide();
    $("#content").show();

  });


});
</script>
<style>
    html{
        background-color:#000757;
        background-image:url(css/light/images/ImageAccueil_HR.jpg);
        background-position: bottom; /*Positioning*/
        background-repeat: no-repeat;
    }
    #content{
        background-color: transparent!important;
        background-image: none!important;
    }
    <?
    if (!isMobile()) {
        ?>
        .logo {
            position: relative;
            top: -150px!important;
            left: -140px!important;
        }
    <?
    }
    ?>
    .logo img {
        width: 325px;
        content:url("Docmatic_inverse.png");
    }
  #btn_oublie {
      color: #FFB848!important;
      text-decoration: none;
    }
    #fermerreset{
      background: #e6e6e6;
      color : #555;
      border-color: #c7c7c7 #b2b2b2 #b2b2b2 #c7c7c7
      border : 1px solid;
      border-radius: 2px 4px;
      /* margin-right: 10px; */
    }
    <?
    if (!isMobile()) {
        ?>
        footer div {
            width: 60%!important;
            color: white;
        }
    <?
    } else {
        ?>
        footer div {
            width: 100%!important;
            color: white;
        }
    <?
    }
    ?>
    label {
        text-shadow: none!important;
        color: white;
    }
</style><br />
<div id="content">
      <form action="login.php" id="loginform" method="post" <?= (isMobile() ? 'style="margin-top: 200px;"' : '') ?>>
        <fieldset>
            <?
            $errs = [
                1 => txt('Courriel et/ou mot de passe incorrect(s).'),
                2 => txt('Connection impossible.<br />Consultez vos courriels pour plus d\'informations.', false),
                3 => txt('Vous avez atteint la limite de tentatives échouées.') . '<br /><br />' .
                    txt('Votre compte est verrouillé.') . '<br /><br />' .
                    txt('Veuillez contacter l’équipe de support aux <NAME_EMAIL>.')
            ];
            ?>
            <?=(isset($_GET['err']) && isset($errs[(int)$_GET['err']]) ? '<br /><b style="color: red; font-weight: bold; font-size: 15px">'.$errs[(int)$_GET['err']].'</b><br /><br />' : '')?>
	        <?=(isset($_GET['mess']) ? '<br /><b style="color: green;">'.txt('Opération exécutée avec succès.').'</b>' : '')?>
            <?php
                if (!isset($_GET['err']) || !in_array((int)$_GET['err'], [2, 3])) {
            ?>
                    <section><label for="email">&nbsp;&nbsp;<?=$texte_email?></label>
                        <div><input type="text" id="email" name="email" autofocus tabindex="1"></div>
                    </section>
                    <section>
                        <label for="password">&nbsp;&nbsp;<?=$texte_password?><a href="#" id="btn_oublie"><?=$texte_oublie?></a></label>
                        <div><input type="password" id="password" name="password" tabindex="2"></div>
                    </section>
                    <section>
                        <div><button class="fr submit" tabindex="3">Connexion</button></div>
                    </section>
            <?php
                }
            ?>
		</fieldset>
	</form>
</div>
<div id="resetpw" style="display: none;">
<form <?= (isMobile() ? 'style="margin-top: 250px;"' : '') ?>>
  <fieldset>
    <section>
      <label style="text-align: justify; padding:3% 4% 1%;"><?=$texte_msg?></label>

      <label for="emailperdu">&nbsp;&nbsp;<?=$texte_email?></label>
      <div><input type="text" id="emailperdu" autofocus tabindex="1" required></div>
    </section>
    <section>
      <div>
        <button id="validercourriel" class="fr submit" tabindex="3"><?=$texte_bouton?></button>
        <button id="fermerreset" class="fr submit" tabindex="2"><?=$texte_bouton_cancel?></button>
      </div>

    </section>
  </fieldset>
</form>
</div>
<div id="afteremailsent" style="display: none;">
<form <?= (isMobile() ? 'style="margin-top: 250px;"' : '') ?>>
  <fieldset>
    <section style="padding-bottom: 25px;">
      <label id="msgaftersent" style="padding: 0;">Email a été envoyé</label>
    </section>
    <section style="text-align: center;">
      <div class="txtOublie"><button id="retourlogin" class="fr submit" tabindex="2"><?=$texte_retour_login?></button></div>
      <div class="txtOublie"><button id="retouremailperdu" class="fr submit" tabindex="3" style="display: none;"><?=$texte_retour_perdu?></button></div>
    </section>
  </fieldset>
</form>

</div>
<script>
$("#validercourriel").on('click', function(e) {
  var valid_email = 0;
  e.preventDefault();

  $('#msgaftersent').empty();
  $("#resetpw").hide();

  if ($("#emailperdu").val() == '') {
    $('#msgaftersent').append("<?=$texte_entrer_email_associe?>");
    $("#retourlogin").hide();
    $("#retouremailperdu").show();
  } else {

    $.post("valid_email.php", {
      email : $("#emailperdu").val()
    },
    function(data, status) {
      if(data=="1"){
        $('#msgaftersent').append("<?=$texte_email_envoye?>");
        $("#retouremailperdu").hide();
        $("#retourlogin").show();
      } else {
        $('#msgaftersent').append("<?=$texte_email_envoye?>");
        $("#retourlogin").hide();
        $("#retouremailperdu").show();
      }
    });
  }
  // $("#content").hide();
  $("#afteremailsent").show();

});
</script>
<?
include('config/footer.inc.php');

if (isset($_GET['err']) && !isset($errs[(int)$_GET['err']])) {
	$message = "Adresse IP: " . $_SERVER['REMOTE_ADDR'] . '<br><br>$_GET:<br>' . getDebug($_GET) . '<br><br>$_POST:<br>' . getDebug($_POST) . '<br><br>$_SERVER:<br>' . getDebug($_SERVER);
	envoiCourriel(['<EMAIL>', '<EMAIL>'], 'Tentative de XXS', $message);
}

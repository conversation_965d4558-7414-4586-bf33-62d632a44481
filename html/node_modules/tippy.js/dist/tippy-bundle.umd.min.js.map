{"version": 3, "file": "tippy-bundle.umd.min.js", "sources": ["../src/browser.ts", "../src/constants.ts", "../src/utils.ts", "../src/dom-utils.ts", "../src/bindGlobalEventListeners.ts", "../src/props.ts", "../src/template.ts", "../src/createTippy.ts", "../src/index.ts", "../src/addons/createSingleton.ts", "../src/addons/delegate.ts", "../src/plugins/animateFill.ts", "../src/plugins/followCursor.ts", "../src/plugins/inlinePositioning.ts", "../src/plugins/sticky.ts", "../build/bundle-umd.js", "../src/css.ts"], "sourcesContent": ["export const isBrowser =\n  typeof window !== 'undefined' && typeof document !== 'undefined';\n\nexport const isIE11 = isBrowser\n  ? // @ts-ignore\n    !!window.msCrypto\n  : false;\n", "export const ROUND_ARROW =\n  '<svg width=\"16\" height=\"6\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M0 6s1.796-.013 4.67-3.615C5.851.9 6.93.006 8 0c1.07-.006 2.148.887 3.343 2.385C14.233 6.005 16 6 16 6H0z\"></svg>';\n\nexport const BOX_CLASS = `__NAMESPACE_PREFIX__-box`;\nexport const CONTENT_CLASS = `__NAMESPACE_PREFIX__-content`;\nexport const BACKDROP_CLASS = `__NAMESPACE_PREFIX__-backdrop`;\nexport const ARROW_CLASS = `__NAMESPACE_PREFIX__-arrow`;\nexport const SVG_ARROW_CLASS = `__NAMESPACE_PREFIX__-svg-arrow`;\n\nexport const TOUCH_OPTIONS = {passive: true, capture: true};\n\nexport const TIPPY_DEFAULT_APPEND_TO = () => document.body;\n", "import {BasePlacement, Placement} from './types';\n\nexport function hasOwnProperty(\n  obj: Record<string, unknown>,\n  key: string\n): boolean {\n  return {}.hasOwnProperty.call(obj, key);\n}\n\nexport function getValueAtIndexOrReturn<T>(\n  value: T | [T | null, T | null],\n  index: number,\n  defaultValue: T | [T, T]\n): T {\n  if (Array.isArray(value)) {\n    const v = value[index];\n    return v == null\n      ? Array.isArray(defaultValue)\n        ? defaultValue[index]\n        : defaultValue\n      : v;\n  }\n\n  return value;\n}\n\nexport function isType(value: any, type: string): boolean {\n  const str = {}.toString.call(value);\n  return str.indexOf('[object') === 0 && str.indexOf(`${type}]`) > -1;\n}\n\nexport function invokeWithArgsOrReturn(value: any, args: any[]): any {\n  return typeof value === 'function' ? value(...args) : value;\n}\n\nexport function debounce<T>(\n  fn: (arg: T) => void,\n  ms: number\n): (arg: T) => void {\n  // Avoid wrapping in `setTimeout` if ms is 0 anyway\n  if (ms === 0) {\n    return fn;\n  }\n\n  let timeout: any;\n\n  return (arg): void => {\n    clearTimeout(timeout);\n    timeout = setTimeout(() => {\n      fn(arg);\n    }, ms);\n  };\n}\n\nexport function removeProperties<T>(obj: T, keys: string[]): Partial<T> {\n  const clone = {...obj};\n  keys.forEach((key) => {\n    delete (clone as any)[key];\n  });\n  return clone;\n}\n\nexport function splitBySpaces(value: string): string[] {\n  return value.split(/\\s+/).filter(Boolean);\n}\n\nexport function normalizeToArray<T>(value: T | T[]): T[] {\n  return ([] as T[]).concat(value);\n}\n\nexport function pushIfUnique<T>(arr: T[], value: T): void {\n  if (arr.indexOf(value) === -1) {\n    arr.push(value);\n  }\n}\n\nexport function appendPxIfNumber(value: string | number): string {\n  return typeof value === 'number' ? `${value}px` : value;\n}\n\nexport function unique<T>(arr: T[]): T[] {\n  return arr.filter((item, index) => arr.indexOf(item) === index);\n}\n\nexport function getNumber(value: string | number): number {\n  return typeof value === 'number' ? value : parseFloat(value);\n}\n\nexport function getBasePlacement(placement: Placement): BasePlacement {\n  return placement.split('-')[0] as BasePlacement;\n}\n\nexport function arrayFrom(value: ArrayLike<any>): any[] {\n  return [].slice.call(value);\n}\n\nexport function removeUndefinedProps(\n  obj: Record<string, unknown>\n): Partial<Record<string, unknown>> {\n  return Object.keys(obj).reduce((acc, key) => {\n    if (obj[key] !== undefined) {\n      (acc as any)[key] = obj[key];\n    }\n\n    return acc;\n  }, {});\n}\n", "import {ReferenceElement, Targets} from './types';\nimport {PopperTreeData} from './types-internal';\nimport {arrayFrom, isType, normalizeToArray, getBasePlacement} from './utils';\n\nexport function div(): HTMLDivElement {\n  return document.createElement('div');\n}\n\nexport function isElement(value: unknown): value is Element | DocumentFragment {\n  return ['Element', 'Fragment'].some((type) => isType(value, type));\n}\n\nexport function isNodeList(value: unknown): value is NodeList {\n  return isType(value, 'NodeList');\n}\n\nexport function isMouseEvent(value: unknown): value is MouseEvent {\n  return isType(value, 'MouseEvent');\n}\n\nexport function isReferenceElement(value: any): value is ReferenceElement {\n  return !!(value && value._tippy && value._tippy.reference === value);\n}\n\nexport function getArrayOfElements(value: Targets): Element[] {\n  if (isElement(value)) {\n    return [value];\n  }\n\n  if (isNodeList(value)) {\n    return arrayFrom(value);\n  }\n\n  if (Array.isArray(value)) {\n    return value;\n  }\n\n  return arrayFrom(document.querySelectorAll(value));\n}\n\nexport function setTransitionDuration(\n  els: (HTMLDivElement | null)[],\n  value: number\n): void {\n  els.forEach((el) => {\n    if (el) {\n      el.style.transitionDuration = `${value}ms`;\n    }\n  });\n}\n\nexport function setVisibilityState(\n  els: (HTMLDivElement | null)[],\n  state: 'visible' | 'hidden'\n): void {\n  els.forEach((el) => {\n    if (el) {\n      el.setAttribute('data-state', state);\n    }\n  });\n}\n\nexport function getOwnerDocument(\n  elementOrElements: Element | Element[]\n): Document {\n  const [element] = normalizeToArray(elementOrElements);\n\n  // Elements created via a <template> have an ownerDocument with no reference to the body\n  return element?.ownerDocument?.body ? element.ownerDocument : document;\n}\n\nexport function isCursorOutsideInteractiveBorder(\n  popperTreeData: PopperTreeData[],\n  event: MouseEvent\n): boolean {\n  const {clientX, clientY} = event;\n\n  return popperTreeData.every(({popperRect, popperState, props}) => {\n    const {interactiveBorder} = props;\n    const basePlacement = getBasePlacement(popperState.placement);\n    const offsetData = popperState.modifiersData.offset;\n\n    if (!offsetData) {\n      return true;\n    }\n\n    const topDistance = basePlacement === 'bottom' ? offsetData.top!.y : 0;\n    const bottomDistance = basePlacement === 'top' ? offsetData.bottom!.y : 0;\n    const leftDistance = basePlacement === 'right' ? offsetData.left!.x : 0;\n    const rightDistance = basePlacement === 'left' ? offsetData.right!.x : 0;\n\n    const exceedsTop =\n      popperRect.top - clientY + topDistance > interactiveBorder;\n    const exceedsBottom =\n      clientY - popperRect.bottom - bottomDistance > interactiveBorder;\n    const exceedsLeft =\n      popperRect.left - clientX + leftDistance > interactiveBorder;\n    const exceedsRight =\n      clientX - popperRect.right - rightDistance > interactiveBorder;\n\n    return exceedsTop || exceedsBottom || exceedsLeft || exceedsRight;\n  });\n}\n\nexport function updateTransitionEndListener(\n  box: HTMLDivElement,\n  action: 'add' | 'remove',\n  listener: (event: TransitionEvent) => void\n): void {\n  const method = `${action}EventListener` as\n    | 'addEventListener'\n    | 'removeEventListener';\n\n  // some browsers apparently support `transition` (unprefixed) but only fire\n  // `webkitTransitionEnd`...\n  ['transitionend', 'webkitTransitionEnd'].forEach((event) => {\n    box[method](event, listener as EventListener);\n  });\n}\n\n/**\n * Compared to xxx.contains, this function works for dom structures with shadow\n * dom\n */\nexport function actualContains(parent: Element, child: Element): boolean {\n  let target = child;\n  while (target) {\n    if (parent.contains(target)) {\n      return true;\n    }\n    target = (target.getRootNode?.() as any)?.host;\n  }\n  return false;\n}\n", "import {TOUCH_OPTIONS} from './constants';\nimport {isReferenceElement} from './dom-utils';\n\nexport const currentInput = {isTouch: false};\nlet lastMouseMoveTime = 0;\n\n/**\n * When a `touchstart` event is fired, it's assumed the user is using touch\n * input. We'll bind a `mousemove` event listener to listen for mouse input in\n * the future. This way, the `isTouch` property is fully dynamic and will handle\n * hybrid devices that use a mix of touch + mouse input.\n */\nexport function onDocumentTouchStart(): void {\n  if (currentInput.isTouch) {\n    return;\n  }\n\n  currentInput.isTouch = true;\n\n  if (window.performance) {\n    document.addEventListener('mousemove', onDocumentMouseMove);\n  }\n}\n\n/**\n * When two `mousemove` event are fired consecutively within 20ms, it's assumed\n * the user is using mouse input again. `mousemove` can fire on touch devices as\n * well, but very rarely that quickly.\n */\nexport function onDocumentMouseMove(): void {\n  const now = performance.now();\n\n  if (now - lastMouseMoveTime < 20) {\n    currentInput.isTouch = false;\n\n    document.removeEventListener('mousemove', onDocumentMouseMove);\n  }\n\n  lastMouseMoveTime = now;\n}\n\n/**\n * When an element is in focus and has a tippy, leaving the tab/window and\n * returning causes it to show again. For mouse users this is unexpected, but\n * for keyboard use it makes sense.\n * TODO: find a better technique to solve this problem\n */\nexport function onWindowBlur(): void {\n  const activeElement = document.activeElement as HTMLElement | null;\n\n  if (isReferenceElement(activeElement)) {\n    const instance = activeElement._tippy!;\n\n    if (activeElement.blur && !instance.state.isVisible) {\n      activeElement.blur();\n    }\n  }\n}\n\nexport default function bindGlobalEventListeners(): void {\n  document.addEventListener('touchstart', onDocumentTouchStart, TOUCH_OPTIONS);\n  window.addEventListener('blur', onWindowBlur);\n}\n", "import {DefaultProps, Plugin, Props, ReferenceElement, Tippy} from './types';\nimport {\n  hasOwnProperty,\n  removeProperties,\n  invokeWithArgsOrReturn,\n} from './utils';\nimport {warnWhen} from './validation';\nimport {TIPPY_DEFAULT_APPEND_TO} from './constants';\n\nconst pluginProps = {\n  animateFill: false,\n  followCursor: false,\n  inlinePositioning: false,\n  sticky: false,\n};\n\nconst renderProps = {\n  allowHTML: false,\n  animation: 'fade',\n  arrow: true,\n  content: '',\n  inertia: false,\n  maxWidth: 350,\n  role: 'tooltip',\n  theme: '',\n  zIndex: 9999,\n};\n\nexport const defaultProps: DefaultProps = {\n  appendTo: TIPPY_DEFAULT_APPEND_TO,\n  aria: {\n    content: 'auto',\n    expanded: 'auto',\n  },\n  delay: 0,\n  duration: [300, 250],\n  getReferenceClientRect: null,\n  hideOnClick: true,\n  ignoreAttributes: false,\n  interactive: false,\n  interactiveBorder: 2,\n  interactiveDebounce: 0,\n  moveTransition: '',\n  offset: [0, 10],\n  onAfterUpdate() {},\n  onBeforeUpdate() {},\n  onCreate() {},\n  onDestroy() {},\n  onHidden() {},\n  onHide() {},\n  onMount() {},\n  onShow() {},\n  onShown() {},\n  onTrigger() {},\n  onUntrigger() {},\n  onClickOutside() {},\n  placement: 'top',\n  plugins: [],\n  popperOptions: {},\n  render: null,\n  showOnCreate: false,\n  touch: true,\n  trigger: 'mouseenter focus',\n  triggerTarget: null,\n  ...pluginProps,\n  ...renderProps,\n};\n\nconst defaultKeys = Object.keys(defaultProps);\n\nexport const setDefaultProps: Tippy['setDefaultProps'] = (partialProps) => {\n  /* istanbul ignore else */\n  if (__DEV__) {\n    validateProps(partialProps, []);\n  }\n\n  const keys = Object.keys(partialProps) as Array<keyof DefaultProps>;\n  keys.forEach((key) => {\n    (defaultProps as any)[key] = partialProps[key];\n  });\n};\n\nexport function getExtendedPassedProps(\n  passedProps: Partial<Props> & Record<string, unknown>\n): Partial<Props> {\n  const plugins = passedProps.plugins || [];\n  const pluginProps = plugins.reduce<Record<string, unknown>>((acc, plugin) => {\n    const {name, defaultValue} = plugin;\n\n    if (name) {\n      acc[name] =\n        passedProps[name] !== undefined\n          ? passedProps[name]\n          : (defaultProps as any)[name] ?? defaultValue;\n    }\n\n    return acc;\n  }, {});\n\n  return {\n    ...passedProps,\n    ...pluginProps,\n  };\n}\n\nexport function getDataAttributeProps(\n  reference: ReferenceElement,\n  plugins: Plugin[]\n): Record<string, unknown> {\n  const propKeys = plugins\n    ? Object.keys(getExtendedPassedProps({...defaultProps, plugins}))\n    : defaultKeys;\n\n  const props = propKeys.reduce(\n    (acc: Partial<Props> & Record<string, unknown>, key) => {\n      const valueAsString = (\n        reference.getAttribute(`data-tippy-${key}`) || ''\n      ).trim();\n\n      if (!valueAsString) {\n        return acc;\n      }\n\n      if (key === 'content') {\n        acc[key] = valueAsString;\n      } else {\n        try {\n          acc[key] = JSON.parse(valueAsString);\n        } catch (e) {\n          acc[key] = valueAsString;\n        }\n      }\n\n      return acc;\n    },\n    {}\n  );\n\n  return props;\n}\n\nexport function evaluateProps(\n  reference: ReferenceElement,\n  props: Props\n): Props {\n  const out = {\n    ...props,\n    content: invokeWithArgsOrReturn(props.content, [reference]),\n    ...(props.ignoreAttributes\n      ? {}\n      : getDataAttributeProps(reference, props.plugins)),\n  };\n\n  out.aria = {\n    ...defaultProps.aria,\n    ...out.aria,\n  };\n\n  out.aria = {\n    expanded:\n      out.aria.expanded === 'auto' ? props.interactive : out.aria.expanded,\n    content:\n      out.aria.content === 'auto'\n        ? props.interactive\n          ? null\n          : 'describedby'\n        : out.aria.content,\n  };\n\n  return out;\n}\n\nexport function validateProps(\n  partialProps: Partial<Props> = {},\n  plugins: Plugin[] = []\n): void {\n  const keys = Object.keys(partialProps) as Array<keyof Props>;\n  keys.forEach((prop) => {\n    const nonPluginProps = removeProperties(\n      defaultProps,\n      Object.keys(pluginProps)\n    );\n\n    let didPassUnknownProp = !hasOwnProperty(nonPluginProps, prop);\n\n    // Check if the prop exists in `plugins`\n    if (didPassUnknownProp) {\n      didPassUnknownProp =\n        plugins.filter((plugin) => plugin.name === prop).length === 0;\n    }\n\n    warnWhen(\n      didPassUnknownProp,\n      [\n        `\\`${prop}\\``,\n        \"is not a valid prop. You may have spelled it incorrectly, or if it's\",\n        'a plugin, forgot to pass it in an array as props.plugins.',\n        '\\n\\n',\n        'All props: https://atomiks.github.io/tippyjs/v6/all-props/\\n',\n        'Plugins: https://atomiks.github.io/tippyjs/v6/plugins/',\n      ].join(' ')\n    );\n  });\n}\n", "import {\n  ARROW_CLASS,\n  BACKDROP_CLASS,\n  BOX_CLASS,\n  CONTENT_CLASS,\n  SVG_ARROW_CLASS,\n} from './constants';\nimport {div, isElement} from './dom-utils';\nimport {Instance, PopperElement, Props} from './types';\nimport {PopperChildren} from './types-internal';\nimport {arrayFrom} from './utils';\n\n// Firefox extensions don't allow .innerHTML = \"...\" property. This tricks it.\nconst innerHTML = (): 'innerHTML' => 'innerHTML';\n\nfunction dangerouslySetInnerHTML(element: Element, html: string): void {\n  element[innerHTML()] = html;\n}\n\nfunction createArrowElement(value: Props['arrow']): HTMLDivElement {\n  const arrow = div();\n\n  if (value === true) {\n    arrow.className = ARROW_CLASS;\n  } else {\n    arrow.className = SVG_ARROW_CLASS;\n\n    if (isElement(value)) {\n      arrow.appendChild(value);\n    } else {\n      dangerouslySetInnerHTML(arrow, value as string);\n    }\n  }\n\n  return arrow;\n}\n\nexport function setContent(content: HTMLDivElement, props: Props): void {\n  if (isElement(props.content)) {\n    dangerouslySetInnerHTML(content, '');\n    content.appendChild(props.content);\n  } else if (typeof props.content !== 'function') {\n    if (props.allowHTML) {\n      dangerouslySetInnerHTML(content, props.content);\n    } else {\n      content.textContent = props.content;\n    }\n  }\n}\n\nexport function getChildren(popper: PopperElement): PopperChildren {\n  const box = popper.firstElementChild as HTMLDivElement;\n  const boxChildren = arrayFrom(box.children);\n\n  return {\n    box,\n    content: boxChildren.find((node) => node.classList.contains(CONTENT_CLASS)),\n    arrow: boxChildren.find(\n      (node) =>\n        node.classList.contains(ARROW_CLASS) ||\n        node.classList.contains(SVG_ARROW_CLASS)\n    ),\n    backdrop: boxChildren.find((node) =>\n      node.classList.contains(BACKDROP_CLASS)\n    ),\n  };\n}\n\nexport function render(\n  instance: Instance\n): {\n  popper: PopperElement;\n  onUpdate?: (prevProps: Props, nextProps: Props) => void;\n} {\n  const popper = div();\n\n  const box = div();\n  box.className = BOX_CLASS;\n  box.setAttribute('data-state', 'hidden');\n  box.setAttribute('tabindex', '-1');\n\n  const content = div();\n  content.className = CONTENT_CLASS;\n  content.setAttribute('data-state', 'hidden');\n\n  setContent(content, instance.props);\n\n  popper.appendChild(box);\n  box.appendChild(content);\n\n  onUpdate(instance.props, instance.props);\n\n  function onUpdate(prevProps: Props, nextProps: Props): void {\n    const {box, content, arrow} = getChildren(popper);\n\n    if (nextProps.theme) {\n      box.setAttribute('data-theme', nextProps.theme);\n    } else {\n      box.removeAttribute('data-theme');\n    }\n\n    if (typeof nextProps.animation === 'string') {\n      box.setAttribute('data-animation', nextProps.animation);\n    } else {\n      box.removeAttribute('data-animation');\n    }\n\n    if (nextProps.inertia) {\n      box.setAttribute('data-inertia', '');\n    } else {\n      box.removeAttribute('data-inertia');\n    }\n\n    box.style.maxWidth =\n      typeof nextProps.maxWidth === 'number'\n        ? `${nextProps.maxWidth}px`\n        : nextProps.maxWidth;\n\n    if (nextProps.role) {\n      box.setAttribute('role', nextProps.role);\n    } else {\n      box.removeAttribute('role');\n    }\n\n    if (\n      prevProps.content !== nextProps.content ||\n      prevProps.allowHTML !== nextProps.allowHTML\n    ) {\n      setContent(content, instance.props);\n    }\n\n    if (nextProps.arrow) {\n      if (!arrow) {\n        box.appendChild(createArrowElement(nextProps.arrow));\n      } else if (prevProps.arrow !== nextProps.arrow) {\n        box.removeChild(arrow);\n        box.appendChild(createArrowElement(nextProps.arrow));\n      }\n    } else if (arrow) {\n      box.removeChild(arrow!);\n    }\n  }\n\n  return {\n    popper,\n    onUpdate,\n  };\n}\n\n// Runtime check to identify if the render function is the default one; this\n// way we can apply default CSS transitions logic and it can be tree-shaken away\nrender.$$tippy = true;\n", "import {createPopper, StrictModifiers, Modifier} from '@popperjs/core';\nimport {currentInput} from './bindGlobalEventListeners';\nimport {isIE11} from './browser';\nimport {TIPPY_DEFAULT_APPEND_TO, TOUCH_OPTIONS} from './constants';\nimport {\n  actualContains,\n  div,\n  getOwnerDocument,\n  isCursorOutsideInteractiveBorder,\n  isMouseEvent,\n  setTransitionDuration,\n  setVisibilityState,\n  updateTransitionEndListener,\n} from './dom-utils';\nimport {defaultProps, evaluateProps, getExtendedPassedProps} from './props';\nimport {getChildren} from './template';\nimport {\n  Content,\n  Instance,\n  LifecycleHooks,\n  PopperElement,\n  Props,\n  ReferenceElement,\n} from './types';\nimport {ListenerObject, PopperTreeData, PopperChildren} from './types-internal';\nimport {\n  arrayFrom,\n  debounce,\n  getValueAtIndexOrReturn,\n  invokeWithArgsOrReturn,\n  normalizeToArray,\n  pushIfUnique,\n  splitBySpaces,\n  unique,\n  removeUndefinedProps,\n} from './utils';\nimport {createMemoryLeakWarning, errorWhen, warnWhen} from './validation';\n\nlet idCounter = 1;\nlet mouseMoveListeners: ((event: MouseEvent) => void)[] = [];\n\n// Used by `hideAll()`\nexport let mountedInstances: Instance[] = [];\n\nexport default function createTippy(\n  reference: ReferenceElement,\n  passedProps: Partial<Props>\n): Instance {\n  const props = evaluateProps(reference, {\n    ...defaultProps,\n    ...getExtendedPassedProps(removeUndefinedProps(passedProps)),\n  });\n\n  // ===========================================================================\n  // 🔒 Private members\n  // ===========================================================================\n  let showTimeout: any;\n  let hideTimeout: any;\n  let scheduleHideAnimationFrame: number;\n  let isVisibleFromClick = false;\n  let didHideDueToDocumentMouseDown = false;\n  let didTouchMove = false;\n  let ignoreOnFirstUpdate = false;\n  let lastTriggerEvent: Event | undefined;\n  let currentTransitionEndListener: (event: TransitionEvent) => void;\n  let onFirstUpdate: () => void;\n  let listeners: ListenerObject[] = [];\n  let debouncedOnMouseMove = debounce(onMouseMove, props.interactiveDebounce);\n  let currentTarget: Element;\n\n  // ===========================================================================\n  // 🔑 Public members\n  // ===========================================================================\n  const id = idCounter++;\n  const popperInstance = null;\n  const plugins = unique(props.plugins);\n\n  const state = {\n    // Is the instance currently enabled?\n    isEnabled: true,\n    // Is the tippy currently showing and not transitioning out?\n    isVisible: false,\n    // Has the instance been destroyed?\n    isDestroyed: false,\n    // Is the tippy currently mounted to the DOM?\n    isMounted: false,\n    // Has the tippy finished transitioning in?\n    isShown: false,\n  };\n\n  const instance: Instance = {\n    // properties\n    id,\n    reference,\n    popper: div(),\n    popperInstance,\n    props,\n    state,\n    plugins,\n    // methods\n    clearDelayTimeouts,\n    setProps,\n    setContent,\n    show,\n    hide,\n    hideWithInteractivity,\n    enable,\n    disable,\n    unmount,\n    destroy,\n  };\n\n  // TODO: Investigate why this early return causes a TDZ error in the tests —\n  // it doesn't seem to happen in the browser\n  /* istanbul ignore if */\n  if (!props.render) {\n    if (__DEV__) {\n      errorWhen(true, 'render() function has not been supplied.');\n    }\n\n    return instance;\n  }\n\n  // ===========================================================================\n  // Initial mutations\n  // ===========================================================================\n  const {popper, onUpdate} = props.render(instance);\n\n  popper.setAttribute('data-__NAMESPACE_PREFIX__-root', '');\n  popper.id = `__NAMESPACE_PREFIX__-${instance.id}`;\n\n  instance.popper = popper;\n  reference._tippy = instance;\n  popper._tippy = instance;\n\n  const pluginsHooks = plugins.map((plugin) => plugin.fn(instance));\n  const hasAriaExpanded = reference.hasAttribute('aria-expanded');\n\n  addListeners();\n  handleAriaExpandedAttribute();\n  handleStyles();\n\n  invokeHook('onCreate', [instance]);\n\n  if (props.showOnCreate) {\n    scheduleShow();\n  }\n\n  // Prevent a tippy with a delay from hiding if the cursor left then returned\n  // before it started hiding\n  popper.addEventListener('mouseenter', () => {\n    if (instance.props.interactive && instance.state.isVisible) {\n      instance.clearDelayTimeouts();\n    }\n  });\n\n  popper.addEventListener('mouseleave', () => {\n    if (\n      instance.props.interactive &&\n      instance.props.trigger.indexOf('mouseenter') >= 0\n    ) {\n      getDocument().addEventListener('mousemove', debouncedOnMouseMove);\n    }\n  });\n\n  return instance;\n\n  // ===========================================================================\n  // 🔒 Private methods\n  // ===========================================================================\n  function getNormalizedTouchSettings(): [string | boolean, number] {\n    const {touch} = instance.props;\n    return Array.isArray(touch) ? touch : [touch, 0];\n  }\n\n  function getIsCustomTouchBehavior(): boolean {\n    return getNormalizedTouchSettings()[0] === 'hold';\n  }\n\n  function getIsDefaultRenderFn(): boolean {\n    // @ts-ignore\n    return !!instance.props.render?.$$tippy;\n  }\n\n  function getCurrentTarget(): Element {\n    return currentTarget || reference;\n  }\n\n  function getDocument(): Document {\n    const parent = getCurrentTarget().parentNode as Element;\n    return parent ? getOwnerDocument(parent) : document;\n  }\n\n  function getDefaultTemplateChildren(): PopperChildren {\n    return getChildren(popper);\n  }\n\n  function getDelay(isShow: boolean): number {\n    // For touch or keyboard input, force `0` delay for UX reasons\n    // Also if the instance is mounted but not visible (transitioning out),\n    // ignore delay\n    if (\n      (instance.state.isMounted && !instance.state.isVisible) ||\n      currentInput.isTouch ||\n      (lastTriggerEvent && lastTriggerEvent.type === 'focus')\n    ) {\n      return 0;\n    }\n\n    return getValueAtIndexOrReturn(\n      instance.props.delay,\n      isShow ? 0 : 1,\n      defaultProps.delay\n    );\n  }\n\n  function handleStyles(fromHide = false): void {\n    popper.style.pointerEvents =\n      instance.props.interactive && !fromHide ? '' : 'none';\n    popper.style.zIndex = `${instance.props.zIndex}`;\n  }\n\n  function invokeHook(\n    hook: keyof LifecycleHooks,\n    args: [Instance, any?],\n    shouldInvokePropsHook = true\n  ): void {\n    pluginsHooks.forEach((pluginHooks) => {\n      if (pluginHooks[hook]) {\n        pluginHooks[hook]!(...args);\n      }\n    });\n\n    if (shouldInvokePropsHook) {\n      instance.props[hook](...args);\n    }\n  }\n\n  function handleAriaContentAttribute(): void {\n    const {aria} = instance.props;\n\n    if (!aria.content) {\n      return;\n    }\n\n    const attr = `aria-${aria.content}`;\n    const id = popper.id;\n    const nodes = normalizeToArray(instance.props.triggerTarget || reference);\n\n    nodes.forEach((node) => {\n      const currentValue = node.getAttribute(attr);\n\n      if (instance.state.isVisible) {\n        node.setAttribute(attr, currentValue ? `${currentValue} ${id}` : id);\n      } else {\n        const nextValue = currentValue && currentValue.replace(id, '').trim();\n\n        if (nextValue) {\n          node.setAttribute(attr, nextValue);\n        } else {\n          node.removeAttribute(attr);\n        }\n      }\n    });\n  }\n\n  function handleAriaExpandedAttribute(): void {\n    if (hasAriaExpanded || !instance.props.aria.expanded) {\n      return;\n    }\n\n    const nodes = normalizeToArray(instance.props.triggerTarget || reference);\n\n    nodes.forEach((node) => {\n      if (instance.props.interactive) {\n        node.setAttribute(\n          'aria-expanded',\n          instance.state.isVisible && node === getCurrentTarget()\n            ? 'true'\n            : 'false'\n        );\n      } else {\n        node.removeAttribute('aria-expanded');\n      }\n    });\n  }\n\n  function cleanupInteractiveMouseListeners(): void {\n    getDocument().removeEventListener('mousemove', debouncedOnMouseMove);\n    mouseMoveListeners = mouseMoveListeners.filter(\n      (listener) => listener !== debouncedOnMouseMove\n    );\n  }\n\n  function onDocumentPress(event: MouseEvent | TouchEvent): void {\n    // Moved finger to scroll instead of an intentional tap outside\n    if (currentInput.isTouch) {\n      if (didTouchMove || event.type === 'mousedown') {\n        return;\n      }\n    }\n\n    const actualTarget =\n      (event.composedPath && event.composedPath()[0]) || event.target;\n\n    // Clicked on interactive popper\n    if (\n      instance.props.interactive &&\n      actualContains(popper, actualTarget as Element)\n    ) {\n      return;\n    }\n\n    // Clicked on the event listeners target\n    if (\n      normalizeToArray(instance.props.triggerTarget || reference).some((el) =>\n        actualContains(el, actualTarget as Element)\n      )\n    ) {\n      if (currentInput.isTouch) {\n        return;\n      }\n\n      if (\n        instance.state.isVisible &&\n        instance.props.trigger.indexOf('click') >= 0\n      ) {\n        return;\n      }\n    } else {\n      invokeHook('onClickOutside', [instance, event]);\n    }\n\n    if (instance.props.hideOnClick === true) {\n      instance.clearDelayTimeouts();\n      instance.hide();\n\n      // `mousedown` event is fired right before `focus` if pressing the\n      // currentTarget. This lets a tippy with `focus` trigger know that it\n      // should not show\n      didHideDueToDocumentMouseDown = true;\n      setTimeout(() => {\n        didHideDueToDocumentMouseDown = false;\n      });\n\n      // The listener gets added in `scheduleShow()`, but this may be hiding it\n      // before it shows, and hide()'s early bail-out behavior can prevent it\n      // from being cleaned up\n      if (!instance.state.isMounted) {\n        removeDocumentPress();\n      }\n    }\n  }\n\n  function onTouchMove(): void {\n    didTouchMove = true;\n  }\n\n  function onTouchStart(): void {\n    didTouchMove = false;\n  }\n\n  function addDocumentPress(): void {\n    const doc = getDocument();\n    doc.addEventListener('mousedown', onDocumentPress, true);\n    doc.addEventListener('touchend', onDocumentPress, TOUCH_OPTIONS);\n    doc.addEventListener('touchstart', onTouchStart, TOUCH_OPTIONS);\n    doc.addEventListener('touchmove', onTouchMove, TOUCH_OPTIONS);\n  }\n\n  function removeDocumentPress(): void {\n    const doc = getDocument();\n    doc.removeEventListener('mousedown', onDocumentPress, true);\n    doc.removeEventListener('touchend', onDocumentPress, TOUCH_OPTIONS);\n    doc.removeEventListener('touchstart', onTouchStart, TOUCH_OPTIONS);\n    doc.removeEventListener('touchmove', onTouchMove, TOUCH_OPTIONS);\n  }\n\n  function onTransitionedOut(duration: number, callback: () => void): void {\n    onTransitionEnd(duration, () => {\n      if (\n        !instance.state.isVisible &&\n        popper.parentNode &&\n        popper.parentNode.contains(popper)\n      ) {\n        callback();\n      }\n    });\n  }\n\n  function onTransitionedIn(duration: number, callback: () => void): void {\n    onTransitionEnd(duration, callback);\n  }\n\n  function onTransitionEnd(duration: number, callback: () => void): void {\n    const box = getDefaultTemplateChildren().box;\n\n    function listener(event: TransitionEvent): void {\n      if (event.target === box) {\n        updateTransitionEndListener(box, 'remove', listener);\n        callback();\n      }\n    }\n\n    // Make callback synchronous if duration is 0\n    // `transitionend` won't fire otherwise\n    if (duration === 0) {\n      return callback();\n    }\n\n    updateTransitionEndListener(box, 'remove', currentTransitionEndListener);\n    updateTransitionEndListener(box, 'add', listener);\n\n    currentTransitionEndListener = listener;\n  }\n\n  function on(\n    eventType: string,\n    handler: EventListener,\n    options: boolean | Record<string, unknown> = false\n  ): void {\n    const nodes = normalizeToArray(instance.props.triggerTarget || reference);\n    nodes.forEach((node) => {\n      node.addEventListener(eventType, handler, options);\n      listeners.push({node, eventType, handler, options});\n    });\n  }\n\n  function addListeners(): void {\n    if (getIsCustomTouchBehavior()) {\n      on('touchstart', onTrigger, {passive: true});\n      on('touchend', onMouseLeave as EventListener, {passive: true});\n    }\n\n    splitBySpaces(instance.props.trigger).forEach((eventType) => {\n      if (eventType === 'manual') {\n        return;\n      }\n\n      on(eventType, onTrigger);\n\n      switch (eventType) {\n        case 'mouseenter':\n          on('mouseleave', onMouseLeave as EventListener);\n          break;\n        case 'focus':\n          on(isIE11 ? 'focusout' : 'blur', onBlurOrFocusOut as EventListener);\n          break;\n        case 'focusin':\n          on('focusout', onBlurOrFocusOut as EventListener);\n          break;\n      }\n    });\n  }\n\n  function removeListeners(): void {\n    listeners.forEach(({node, eventType, handler, options}: ListenerObject) => {\n      node.removeEventListener(eventType, handler, options);\n    });\n    listeners = [];\n  }\n\n  function onTrigger(event: Event): void {\n    let shouldScheduleClickHide = false;\n\n    if (\n      !instance.state.isEnabled ||\n      isEventListenerStopped(event) ||\n      didHideDueToDocumentMouseDown\n    ) {\n      return;\n    }\n\n    const wasFocused = lastTriggerEvent?.type === 'focus';\n\n    lastTriggerEvent = event;\n    currentTarget = event.currentTarget as Element;\n\n    handleAriaExpandedAttribute();\n\n    if (!instance.state.isVisible && isMouseEvent(event)) {\n      // If scrolling, `mouseenter` events can be fired if the cursor lands\n      // over a new target, but `mousemove` events don't get fired. This\n      // causes interactive tooltips to get stuck open until the cursor is\n      // moved\n      mouseMoveListeners.forEach((listener) => listener(event));\n    }\n\n    // Toggle show/hide when clicking click-triggered tooltips\n    if (\n      event.type === 'click' &&\n      (instance.props.trigger.indexOf('mouseenter') < 0 ||\n        isVisibleFromClick) &&\n      instance.props.hideOnClick !== false &&\n      instance.state.isVisible\n    ) {\n      shouldScheduleClickHide = true;\n    } else {\n      scheduleShow(event);\n    }\n\n    if (event.type === 'click') {\n      isVisibleFromClick = !shouldScheduleClickHide;\n    }\n\n    if (shouldScheduleClickHide && !wasFocused) {\n      scheduleHide(event);\n    }\n  }\n\n  function onMouseMove(event: MouseEvent): void {\n    const target = event.target as Node;\n    const isCursorOverReferenceOrPopper =\n      getCurrentTarget().contains(target) || popper.contains(target);\n\n    if (event.type === 'mousemove' && isCursorOverReferenceOrPopper) {\n      return;\n    }\n\n    const popperTreeData = getNestedPopperTree()\n      .concat(popper)\n      .map((popper) => {\n        const instance = popper._tippy!;\n        const state = instance.popperInstance?.state;\n\n        if (state) {\n          return {\n            popperRect: popper.getBoundingClientRect(),\n            popperState: state,\n            props,\n          };\n        }\n\n        return null;\n      })\n      .filter(Boolean) as PopperTreeData[];\n\n    if (isCursorOutsideInteractiveBorder(popperTreeData, event)) {\n      cleanupInteractiveMouseListeners();\n      scheduleHide(event);\n    }\n  }\n\n  function onMouseLeave(event: MouseEvent): void {\n    const shouldBail =\n      isEventListenerStopped(event) ||\n      (instance.props.trigger.indexOf('click') >= 0 && isVisibleFromClick);\n\n    if (shouldBail) {\n      return;\n    }\n\n    if (instance.props.interactive) {\n      instance.hideWithInteractivity(event);\n      return;\n    }\n\n    scheduleHide(event);\n  }\n\n  function onBlurOrFocusOut(event: FocusEvent): void {\n    if (\n      instance.props.trigger.indexOf('focusin') < 0 &&\n      event.target !== getCurrentTarget()\n    ) {\n      return;\n    }\n\n    // If focus was moved to within the popper\n    if (\n      instance.props.interactive &&\n      event.relatedTarget &&\n      popper.contains(event.relatedTarget as Element)\n    ) {\n      return;\n    }\n\n    scheduleHide(event);\n  }\n\n  function isEventListenerStopped(event: Event): boolean {\n    return currentInput.isTouch\n      ? getIsCustomTouchBehavior() !== event.type.indexOf('touch') >= 0\n      : false;\n  }\n\n  function createPopperInstance(): void {\n    destroyPopperInstance();\n\n    const {\n      popperOptions,\n      placement,\n      offset,\n      getReferenceClientRect,\n      moveTransition,\n    } = instance.props;\n\n    const arrow = getIsDefaultRenderFn() ? getChildren(popper).arrow : null;\n\n    const computedReference = getReferenceClientRect\n      ? {\n          getBoundingClientRect: getReferenceClientRect,\n          contextElement:\n            getReferenceClientRect.contextElement || getCurrentTarget(),\n        }\n      : reference;\n\n    const tippyModifier: Modifier<'$$tippy', Record<string, unknown>> = {\n      name: '$$tippy',\n      enabled: true,\n      phase: 'beforeWrite',\n      requires: ['computeStyles'],\n      fn({state}) {\n        if (getIsDefaultRenderFn()) {\n          const {box} = getDefaultTemplateChildren();\n\n          ['placement', 'reference-hidden', 'escaped'].forEach((attr) => {\n            if (attr === 'placement') {\n              box.setAttribute('data-placement', state.placement);\n            } else {\n              if (state.attributes.popper[`data-popper-${attr}`]) {\n                box.setAttribute(`data-${attr}`, '');\n              } else {\n                box.removeAttribute(`data-${attr}`);\n              }\n            }\n          });\n\n          state.attributes.popper = {};\n        }\n      },\n    };\n\n    type TippyModifier = Modifier<'$$tippy', Record<string, unknown>>;\n    type ExtendedModifiers = StrictModifiers | Partial<TippyModifier>;\n\n    const modifiers: Array<ExtendedModifiers> = [\n      {\n        name: 'offset',\n        options: {\n          offset,\n        },\n      },\n      {\n        name: 'preventOverflow',\n        options: {\n          padding: {\n            top: 2,\n            bottom: 2,\n            left: 5,\n            right: 5,\n          },\n        },\n      },\n      {\n        name: 'flip',\n        options: {\n          padding: 5,\n        },\n      },\n      {\n        name: 'computeStyles',\n        options: {\n          adaptive: !moveTransition,\n        },\n      },\n      tippyModifier,\n    ];\n\n    if (getIsDefaultRenderFn() && arrow) {\n      modifiers.push({\n        name: 'arrow',\n        options: {\n          element: arrow,\n          padding: 3,\n        },\n      });\n    }\n\n    modifiers.push(...(popperOptions?.modifiers || []));\n\n    instance.popperInstance = createPopper<ExtendedModifiers>(\n      computedReference,\n      popper,\n      {\n        ...popperOptions,\n        placement,\n        onFirstUpdate,\n        modifiers,\n      }\n    );\n  }\n\n  function destroyPopperInstance(): void {\n    if (instance.popperInstance) {\n      instance.popperInstance.destroy();\n      instance.popperInstance = null;\n    }\n  }\n\n  function mount(): void {\n    const {appendTo} = instance.props;\n\n    let parentNode: any;\n\n    // By default, we'll append the popper to the triggerTargets's parentNode so\n    // it's directly after the reference element so the elements inside the\n    // tippy can be tabbed to\n    // If there are clipping issues, the user can specify a different appendTo\n    // and ensure focus management is handled correctly manually\n    const node = getCurrentTarget();\n\n    if (\n      (instance.props.interactive && appendTo === TIPPY_DEFAULT_APPEND_TO) ||\n      appendTo === 'parent'\n    ) {\n      parentNode = node.parentNode;\n    } else {\n      parentNode = invokeWithArgsOrReturn(appendTo, [node]);\n    }\n\n    // The popper element needs to exist on the DOM before its position can be\n    // updated as Popper needs to read its dimensions\n    if (!parentNode.contains(popper)) {\n      parentNode.appendChild(popper);\n    }\n\n    instance.state.isMounted = true;\n\n    createPopperInstance();\n\n    /* istanbul ignore else */\n    if (__DEV__) {\n      // Accessibility check\n      warnWhen(\n        instance.props.interactive &&\n          appendTo === defaultProps.appendTo &&\n          node.nextElementSibling !== popper,\n        [\n          'Interactive tippy element may not be accessible via keyboard',\n          'navigation because it is not directly after the reference element',\n          'in the DOM source order.',\n          '\\n\\n',\n          'Using a wrapper <div> or <span> tag around the reference element',\n          'solves this by creating a new parentNode context.',\n          '\\n\\n',\n          'Specifying `appendTo: document.body` silences this warning, but it',\n          'assumes you are using a focus management solution to handle',\n          'keyboard navigation.',\n          '\\n\\n',\n          'See: https://atomiks.github.io/tippyjs/v6/accessibility/#interactivity',\n        ].join(' ')\n      );\n    }\n  }\n\n  function getNestedPopperTree(): PopperElement[] {\n    return arrayFrom(\n      popper.querySelectorAll('[data-__NAMESPACE_PREFIX__-root]')\n    );\n  }\n\n  function scheduleShow(event?: Event): void {\n    instance.clearDelayTimeouts();\n\n    if (event) {\n      invokeHook('onTrigger', [instance, event]);\n    }\n\n    addDocumentPress();\n\n    let delay = getDelay(true);\n    const [touchValue, touchDelay] = getNormalizedTouchSettings();\n\n    if (currentInput.isTouch && touchValue === 'hold' && touchDelay) {\n      delay = touchDelay;\n    }\n\n    if (delay) {\n      showTimeout = setTimeout(() => {\n        instance.show();\n      }, delay);\n    } else {\n      instance.show();\n    }\n  }\n\n  function scheduleHide(event: Event): void {\n    instance.clearDelayTimeouts();\n\n    invokeHook('onUntrigger', [instance, event]);\n\n    if (!instance.state.isVisible) {\n      removeDocumentPress();\n\n      return;\n    }\n\n    // For interactive tippies, scheduleHide is added to a document.body handler\n    // from onMouseLeave so must intercept scheduled hides from mousemove/leave\n    // events when trigger contains mouseenter and click, and the tip is\n    // currently shown as a result of a click.\n    if (\n      instance.props.trigger.indexOf('mouseenter') >= 0 &&\n      instance.props.trigger.indexOf('click') >= 0 &&\n      ['mouseleave', 'mousemove'].indexOf(event.type) >= 0 &&\n      isVisibleFromClick\n    ) {\n      return;\n    }\n\n    const delay = getDelay(false);\n\n    if (delay) {\n      hideTimeout = setTimeout(() => {\n        if (instance.state.isVisible) {\n          instance.hide();\n        }\n      }, delay);\n    } else {\n      // Fixes a `transitionend` problem when it fires 1 frame too\n      // late sometimes, we don't want hide() to be called.\n      scheduleHideAnimationFrame = requestAnimationFrame(() => {\n        instance.hide();\n      });\n    }\n  }\n\n  // ===========================================================================\n  // 🔑 Public methods\n  // ===========================================================================\n  function enable(): void {\n    instance.state.isEnabled = true;\n  }\n\n  function disable(): void {\n    // Disabling the instance should also hide it\n    // https://github.com/atomiks/tippy.js-react/issues/106\n    instance.hide();\n    instance.state.isEnabled = false;\n  }\n\n  function clearDelayTimeouts(): void {\n    clearTimeout(showTimeout);\n    clearTimeout(hideTimeout);\n    cancelAnimationFrame(scheduleHideAnimationFrame);\n  }\n\n  function setProps(partialProps: Partial<Props>): void {\n    /* istanbul ignore else */\n    if (__DEV__) {\n      warnWhen(instance.state.isDestroyed, createMemoryLeakWarning('setProps'));\n    }\n\n    if (instance.state.isDestroyed) {\n      return;\n    }\n\n    invokeHook('onBeforeUpdate', [instance, partialProps]);\n\n    removeListeners();\n\n    const prevProps = instance.props;\n    const nextProps = evaluateProps(reference, {\n      ...prevProps,\n      ...removeUndefinedProps(partialProps),\n      ignoreAttributes: true,\n    });\n\n    instance.props = nextProps;\n\n    addListeners();\n\n    if (prevProps.interactiveDebounce !== nextProps.interactiveDebounce) {\n      cleanupInteractiveMouseListeners();\n      debouncedOnMouseMove = debounce(\n        onMouseMove,\n        nextProps.interactiveDebounce\n      );\n    }\n\n    // Ensure stale aria-expanded attributes are removed\n    if (prevProps.triggerTarget && !nextProps.triggerTarget) {\n      normalizeToArray(prevProps.triggerTarget).forEach((node) => {\n        node.removeAttribute('aria-expanded');\n      });\n    } else if (nextProps.triggerTarget) {\n      reference.removeAttribute('aria-expanded');\n    }\n\n    handleAriaExpandedAttribute();\n    handleStyles();\n\n    if (onUpdate) {\n      onUpdate(prevProps, nextProps);\n    }\n\n    if (instance.popperInstance) {\n      createPopperInstance();\n\n      // Fixes an issue with nested tippies if they are all getting re-rendered,\n      // and the nested ones get re-rendered first.\n      // https://github.com/atomiks/tippyjs-react/issues/177\n      // TODO: find a cleaner / more efficient solution(!)\n      getNestedPopperTree().forEach((nestedPopper) => {\n        // React (and other UI libs likely) requires a rAF wrapper as it flushes\n        // its work in one\n        requestAnimationFrame(nestedPopper._tippy!.popperInstance!.forceUpdate);\n      });\n    }\n\n    invokeHook('onAfterUpdate', [instance, partialProps]);\n  }\n\n  function setContent(content: Content): void {\n    instance.setProps({content});\n  }\n\n  function show(): void {\n    /* istanbul ignore else */\n    if (__DEV__) {\n      warnWhen(instance.state.isDestroyed, createMemoryLeakWarning('show'));\n    }\n\n    // Early bail-out\n    const isAlreadyVisible = instance.state.isVisible;\n    const isDestroyed = instance.state.isDestroyed;\n    const isDisabled = !instance.state.isEnabled;\n    const isTouchAndTouchDisabled =\n      currentInput.isTouch && !instance.props.touch;\n    const duration = getValueAtIndexOrReturn(\n      instance.props.duration,\n      0,\n      defaultProps.duration\n    );\n\n    if (\n      isAlreadyVisible ||\n      isDestroyed ||\n      isDisabled ||\n      isTouchAndTouchDisabled\n    ) {\n      return;\n    }\n\n    // Normalize `disabled` behavior across browsers.\n    // Firefox allows events on disabled elements, but Chrome doesn't.\n    // Using a wrapper element (i.e. <span>) is recommended.\n    if (getCurrentTarget().hasAttribute('disabled')) {\n      return;\n    }\n\n    invokeHook('onShow', [instance], false);\n    if (instance.props.onShow(instance) === false) {\n      return;\n    }\n\n    instance.state.isVisible = true;\n\n    if (getIsDefaultRenderFn()) {\n      popper.style.visibility = 'visible';\n    }\n\n    handleStyles();\n    addDocumentPress();\n\n    if (!instance.state.isMounted) {\n      popper.style.transition = 'none';\n    }\n\n    // If flipping to the opposite side after hiding at least once, the\n    // animation will use the wrong placement without resetting the duration\n    if (getIsDefaultRenderFn()) {\n      const {box, content} = getDefaultTemplateChildren();\n      setTransitionDuration([box, content], 0);\n    }\n\n    onFirstUpdate = (): void => {\n      if (!instance.state.isVisible || ignoreOnFirstUpdate) {\n        return;\n      }\n\n      ignoreOnFirstUpdate = true;\n\n      // reflow\n      void popper.offsetHeight;\n\n      popper.style.transition = instance.props.moveTransition;\n\n      if (getIsDefaultRenderFn() && instance.props.animation) {\n        const {box, content} = getDefaultTemplateChildren();\n        setTransitionDuration([box, content], duration);\n        setVisibilityState([box, content], 'visible');\n      }\n\n      handleAriaContentAttribute();\n      handleAriaExpandedAttribute();\n\n      pushIfUnique(mountedInstances, instance);\n\n      // certain modifiers (e.g. `maxSize`) require a second update after the\n      // popper has been positioned for the first time\n      instance.popperInstance?.forceUpdate();\n\n      invokeHook('onMount', [instance]);\n\n      if (instance.props.animation && getIsDefaultRenderFn()) {\n        onTransitionedIn(duration, () => {\n          instance.state.isShown = true;\n          invokeHook('onShown', [instance]);\n        });\n      }\n    };\n\n    mount();\n  }\n\n  function hide(): void {\n    /* istanbul ignore else */\n    if (__DEV__) {\n      warnWhen(instance.state.isDestroyed, createMemoryLeakWarning('hide'));\n    }\n\n    // Early bail-out\n    const isAlreadyHidden = !instance.state.isVisible;\n    const isDestroyed = instance.state.isDestroyed;\n    const isDisabled = !instance.state.isEnabled;\n    const duration = getValueAtIndexOrReturn(\n      instance.props.duration,\n      1,\n      defaultProps.duration\n    );\n\n    if (isAlreadyHidden || isDestroyed || isDisabled) {\n      return;\n    }\n\n    invokeHook('onHide', [instance], false);\n    if (instance.props.onHide(instance) === false) {\n      return;\n    }\n\n    instance.state.isVisible = false;\n    instance.state.isShown = false;\n    ignoreOnFirstUpdate = false;\n    isVisibleFromClick = false;\n\n    if (getIsDefaultRenderFn()) {\n      popper.style.visibility = 'hidden';\n    }\n\n    cleanupInteractiveMouseListeners();\n    removeDocumentPress();\n    handleStyles(true);\n\n    if (getIsDefaultRenderFn()) {\n      const {box, content} = getDefaultTemplateChildren();\n\n      if (instance.props.animation) {\n        setTransitionDuration([box, content], duration);\n        setVisibilityState([box, content], 'hidden');\n      }\n    }\n\n    handleAriaContentAttribute();\n    handleAriaExpandedAttribute();\n\n    if (instance.props.animation) {\n      if (getIsDefaultRenderFn()) {\n        onTransitionedOut(duration, instance.unmount);\n      }\n    } else {\n      instance.unmount();\n    }\n  }\n\n  function hideWithInteractivity(event: MouseEvent): void {\n    /* istanbul ignore else */\n    if (__DEV__) {\n      warnWhen(\n        instance.state.isDestroyed,\n        createMemoryLeakWarning('hideWithInteractivity')\n      );\n    }\n\n    getDocument().addEventListener('mousemove', debouncedOnMouseMove);\n    pushIfUnique(mouseMoveListeners, debouncedOnMouseMove);\n    debouncedOnMouseMove(event);\n  }\n\n  function unmount(): void {\n    /* istanbul ignore else */\n    if (__DEV__) {\n      warnWhen(instance.state.isDestroyed, createMemoryLeakWarning('unmount'));\n    }\n\n    if (instance.state.isVisible) {\n      instance.hide();\n    }\n\n    if (!instance.state.isMounted) {\n      return;\n    }\n\n    destroyPopperInstance();\n\n    // If a popper is not interactive, it will be appended outside the popper\n    // tree by default. This seems mainly for interactive tippies, but we should\n    // find a workaround if possible\n    getNestedPopperTree().forEach((nestedPopper) => {\n      nestedPopper._tippy!.unmount();\n    });\n\n    if (popper.parentNode) {\n      popper.parentNode.removeChild(popper);\n    }\n\n    mountedInstances = mountedInstances.filter((i) => i !== instance);\n\n    instance.state.isMounted = false;\n    invokeHook('onHidden', [instance]);\n  }\n\n  function destroy(): void {\n    /* istanbul ignore else */\n    if (__DEV__) {\n      warnWhen(instance.state.isDestroyed, createMemoryLeakWarning('destroy'));\n    }\n\n    if (instance.state.isDestroyed) {\n      return;\n    }\n\n    instance.clearDelayTimeouts();\n    instance.unmount();\n\n    removeListeners();\n\n    delete reference._tippy;\n\n    instance.state.isDestroyed = true;\n\n    invokeHook('onDestroy', [instance]);\n  }\n}\n", "import bindGlobalEventListeners, {\n  currentInput,\n} from './bindGlobalEventListeners';\nimport createTippy, {mountedInstances} from './createTippy';\nimport {getArrayOfElements, isElement, isReferenceElement} from './dom-utils';\nimport {defaultProps, setDefaultProps, validateProps} from './props';\nimport {HideAll, HideAllOptions, Instance, Props, Targets} from './types';\nimport {validateTargets, warnWhen} from './validation';\n\nfunction tippy(\n  targets: Targets,\n  optionalProps: Partial<Props> = {}\n): Instance | Instance[] {\n  const plugins = defaultProps.plugins.concat(optionalProps.plugins || []);\n\n  /* istanbul ignore else */\n  if (__DEV__) {\n    validateTargets(targets);\n    validateProps(optionalProps, plugins);\n  }\n\n  bindGlobalEventListeners();\n\n  const passedProps: Partial<Props> = {...optionalProps, plugins};\n\n  const elements = getArrayOfElements(targets);\n\n  /* istanbul ignore else */\n  if (__DEV__) {\n    const isSingleContentElement = isElement(passedProps.content);\n    const isMoreThanOneReferenceElement = elements.length > 1;\n    warnWhen(\n      isSingleContentElement && isMoreThanOneReferenceElement,\n      [\n        'tippy() was passed an Element as the `content` prop, but more than',\n        'one tippy instance was created by this invocation. This means the',\n        'content element will only be appended to the last tippy instance.',\n        '\\n\\n',\n        'Instead, pass the .innerHTML of the element, or use a function that',\n        'returns a cloned version of the element instead.',\n        '\\n\\n',\n        '1) content: element.innerHTML\\n',\n        '2) content: () => element.cloneNode(true)',\n      ].join(' ')\n    );\n  }\n\n  const instances = elements.reduce<Instance[]>(\n    (acc, reference): Instance[] => {\n      const instance = reference && createTippy(reference, passedProps);\n\n      if (instance) {\n        acc.push(instance);\n      }\n\n      return acc;\n    },\n    []\n  );\n\n  return isElement(targets) ? instances[0] : instances;\n}\n\ntippy.defaultProps = defaultProps;\ntippy.setDefaultProps = setDefaultProps;\ntippy.currentInput = currentInput;\n\nexport default tippy;\n\nexport const hideAll: HideAll = ({\n  exclude: excludedReferenceOrInstance,\n  duration,\n}: HideAllOptions = {}) => {\n  mountedInstances.forEach((instance) => {\n    let isExcluded = false;\n\n    if (excludedReferenceOrInstance) {\n      isExcluded = isReferenceElement(excludedReferenceOrInstance)\n        ? instance.reference === excludedReferenceOrInstance\n        : instance.popper === (excludedReferenceOrInstance as Instance).popper;\n    }\n\n    if (!isExcluded) {\n      const originalDuration = instance.props.duration;\n\n      instance.setProps({duration});\n      instance.hide();\n\n      if (!instance.state.isDestroyed) {\n        instance.setProps({duration: originalDuration});\n      }\n    }\n  });\n};\n", "import tippy from '..';\nimport {div} from '../dom-utils';\nimport {\n  C<PERSON><PERSON><PERSON><PERSON>,\n  <PERSON><PERSON><PERSON>,\n  CreateSingletonProps,\n  ReferenceElement,\n  CreateSingletonInstance,\n  Instance,\n  Props,\n} from '../types';\nimport {normalizeToArray, removeProperties} from '../utils';\nimport {errorWhen} from '../validation';\nimport {applyStyles, Modifier} from '@popperjs/core';\n\n// The default `applyStyles` modifier has a cleanup function that gets called\n// every time the popper is destroyed (i.e. a new target), removing the styles\n// and causing transitions to break for singletons when the console is open, but\n// most notably for non-transform styles being used, `gpuAcceleration: false`.\nconst applyStylesModifier: Modifier<'applyStyles', Record<string, unknown>> = {\n  ...applyStyles,\n  effect({state}) {\n    const initialStyles = {\n      popper: {\n        position: state.options.strategy,\n        left: '0',\n        top: '0',\n        margin: '0',\n      },\n      arrow: {\n        position: 'absolute',\n      },\n      reference: {},\n    };\n\n    Object.assign(state.elements.popper.style, initialStyles.popper);\n    state.styles = initialStyles;\n\n    if (state.elements.arrow) {\n      Object.assign(state.elements.arrow.style, initialStyles.arrow);\n    }\n\n    // intentionally return no cleanup function\n    // return () => { ... }\n  },\n};\n\nconst createSingleton: CreateSingleton = (\n  tippyInstances,\n  optionalProps = {}\n) => {\n  /* istanbul ignore else */\n  if (__DEV__) {\n    errorWhen(\n      !Array.isArray(tippyInstances),\n      [\n        'The first argument passed to createSingleton() must be an array of',\n        'tippy instances. The passed value was',\n        String(tippyInstances),\n      ].join(' ')\n    );\n  }\n\n  let individualInstances = tippyInstances;\n  let references: Array<ReferenceElement> = [];\n  let triggerTargets: Array<Element> = [];\n  let currentTarget: Element | null;\n  let overrides = optionalProps.overrides;\n  let interceptSetPropsCleanups: Array<() => void> = [];\n  let shownOnCreate = false;\n\n  function setTriggerTargets(): void {\n    triggerTargets = individualInstances\n      .map((instance) =>\n        normalizeToArray(instance.props.triggerTarget || instance.reference)\n      )\n      .reduce((acc, item) => acc.concat(item), []);\n  }\n\n  function setReferences(): void {\n    references = individualInstances.map((instance) => instance.reference);\n  }\n\n  function enableInstances(isEnabled: boolean): void {\n    individualInstances.forEach((instance) => {\n      if (isEnabled) {\n        instance.enable();\n      } else {\n        instance.disable();\n      }\n    });\n  }\n\n  function interceptSetProps(singleton: Instance): Array<() => void> {\n    return individualInstances.map((instance) => {\n      const originalSetProps = instance.setProps;\n\n      instance.setProps = (props): void => {\n        originalSetProps(props);\n\n        if (instance.reference === currentTarget) {\n          singleton.setProps(props);\n        }\n      };\n\n      return (): void => {\n        instance.setProps = originalSetProps;\n      };\n    });\n  }\n\n  // have to pass singleton, as it maybe undefined on first call\n  function prepareInstance(\n    singleton: Instance,\n    target: ReferenceElement\n  ): void {\n    const index = triggerTargets.indexOf(target);\n\n    // bail-out\n    if (target === currentTarget) {\n      return;\n    }\n\n    currentTarget = target;\n\n    const overrideProps: Partial<Props> = (overrides || [])\n      .concat('content')\n      .reduce((acc, prop) => {\n        (acc as any)[prop] = individualInstances[index].props[prop];\n        return acc;\n      }, {});\n\n    singleton.setProps({\n      ...overrideProps,\n      getReferenceClientRect:\n        typeof overrideProps.getReferenceClientRect === 'function'\n          ? overrideProps.getReferenceClientRect\n          : (): ClientRect => references[index]?.getBoundingClientRect(),\n    });\n  }\n\n  enableInstances(false);\n  setReferences();\n  setTriggerTargets();\n\n  const plugin: Plugin = {\n    fn() {\n      return {\n        onDestroy(): void {\n          enableInstances(true);\n        },\n        onHidden(): void {\n          currentTarget = null;\n        },\n        onClickOutside(instance): void {\n          if (instance.props.showOnCreate && !shownOnCreate) {\n            shownOnCreate = true;\n            currentTarget = null;\n          }\n        },\n        onShow(instance): void {\n          if (instance.props.showOnCreate && !shownOnCreate) {\n            shownOnCreate = true;\n            prepareInstance(instance, references[0]);\n          }\n        },\n        onTrigger(instance, event): void {\n          prepareInstance(instance, event.currentTarget as Element);\n        },\n      };\n    },\n  };\n\n  const singleton = tippy(div(), {\n    ...removeProperties(optionalProps, ['overrides']),\n    plugins: [plugin, ...(optionalProps.plugins || [])],\n    triggerTarget: triggerTargets,\n    popperOptions: {\n      ...optionalProps.popperOptions,\n      modifiers: [\n        ...(optionalProps.popperOptions?.modifiers || []),\n        applyStylesModifier,\n      ],\n    },\n  }) as CreateSingletonInstance<CreateSingletonProps>;\n\n  const originalShow = singleton.show;\n\n  singleton.show = (target?: ReferenceElement | Instance | number): void => {\n    originalShow();\n\n    // first time, showOnCreate or programmatic call with no params\n    // default to showing first instance\n    if (!currentTarget && target == null) {\n      return prepareInstance(singleton, references[0]);\n    }\n\n    // triggered from event (do nothing as prepareInstance already called by onTrigger)\n    // programmatic call with no params when already visible (do nothing again)\n    if (currentTarget && target == null) {\n      return;\n    }\n\n    // target is index of instance\n    if (typeof target === 'number') {\n      return (\n        references[target] && prepareInstance(singleton, references[target])\n      );\n    }\n\n    // target is a child tippy instance\n    if (individualInstances.indexOf(target as Instance) >= 0) {\n      const ref = (target as Instance).reference;\n      return prepareInstance(singleton, ref);\n    }\n\n    // target is a ReferenceElement\n    if (references.indexOf(target as ReferenceElement) >= 0) {\n      return prepareInstance(singleton, target as ReferenceElement);\n    }\n  };\n\n  singleton.showNext = (): void => {\n    const first = references[0];\n    if (!currentTarget) {\n      return singleton.show(0);\n    }\n    const index = references.indexOf(currentTarget);\n    singleton.show(references[index + 1] || first);\n  };\n\n  singleton.showPrevious = (): void => {\n    const last = references[references.length - 1];\n    if (!currentTarget) {\n      return singleton.show(last);\n    }\n    const index = references.indexOf(currentTarget);\n    const target = references[index - 1] || last;\n    singleton.show(target);\n  };\n\n  const originalSetProps = singleton.setProps;\n\n  singleton.setProps = (props): void => {\n    overrides = props.overrides || overrides;\n    originalSetProps(props);\n  };\n\n  singleton.setInstances = (nextInstances): void => {\n    enableInstances(true);\n    interceptSetPropsCleanups.forEach((fn) => fn());\n\n    individualInstances = nextInstances;\n\n    enableInstances(false);\n    setReferences();\n    setTriggerTargets();\n    interceptSetPropsCleanups = interceptSetProps(singleton);\n\n    singleton.setProps({triggerTarget: triggerTargets});\n  };\n\n  interceptSetPropsCleanups = interceptSetProps(singleton);\n\n  return singleton;\n};\n\nexport default createSingleton;\n", "import tippy from '..';\nimport {TOUCH_OPTIONS} from '../constants';\nimport {defaultProps} from '../props';\nimport {Instance, Props, Targets} from '../types';\nimport {ListenerObject} from '../types-internal';\nimport {normalizeToArray, removeProperties} from '../utils';\nimport {errorWhen} from '../validation';\n\nconst BUBBLING_EVENTS_MAP = {\n  mouseover: 'mouseenter',\n  focusin: 'focus',\n  click: 'click',\n};\n\n/**\n * Creates a delegate instance that controls the creation of tippy instances\n * for child elements (`target` CSS selector).\n */\nfunction delegate(\n  targets: Targets,\n  props: Partial<Props> & {target: string}\n): Instance | Instance[] {\n  /* istanbul ignore else */\n  if (__DEV__) {\n    errorWhen(\n      !(props && props.target),\n      [\n        'You must specity a `target` prop indicating a CSS selector string matching',\n        'the target elements that should receive a tippy.',\n      ].join(' ')\n    );\n  }\n\n  let listeners: ListenerObject[] = [];\n  let childTippyInstances: Instance[] = [];\n  let disabled = false;\n\n  const {target} = props;\n\n  const nativeProps = removeProperties(props, ['target']);\n  const parentProps = {...nativeProps, trigger: 'manual', touch: false};\n  const childProps = {\n    touch: defaultProps.touch,\n    ...nativeProps,\n    showOnCreate: true,\n  };\n\n  const returnValue = tippy(targets, parentProps);\n  const normalizedReturnValue = normalizeToArray(returnValue);\n\n  function onTrigger(event: Event): void {\n    if (!event.target || disabled) {\n      return;\n    }\n\n    const targetNode = (event.target as Element).closest(target);\n\n    if (!targetNode) {\n      return;\n    }\n\n    // Get relevant trigger with fallbacks:\n    // 1. Check `data-tippy-trigger` attribute on target node\n    // 2. Fallback to `trigger` passed to `delegate()`\n    // 3. Fallback to `defaultProps.trigger`\n    const trigger =\n      targetNode.getAttribute('data-tippy-trigger') ||\n      props.trigger ||\n      defaultProps.trigger;\n\n    // @ts-ignore\n    if (targetNode._tippy) {\n      return;\n    }\n\n    if (event.type === 'touchstart' && typeof childProps.touch === 'boolean') {\n      return;\n    }\n\n    if (\n      event.type !== 'touchstart' &&\n      trigger.indexOf((BUBBLING_EVENTS_MAP as any)[event.type]) < 0\n    ) {\n      return;\n    }\n\n    const instance = tippy(targetNode, childProps);\n\n    if (instance) {\n      childTippyInstances = childTippyInstances.concat(instance);\n    }\n  }\n\n  function on(\n    node: Element,\n    eventType: string,\n    handler: EventListener,\n    options: boolean | Record<string, unknown> = false\n  ): void {\n    node.addEventListener(eventType, handler, options);\n    listeners.push({node, eventType, handler, options});\n  }\n\n  function addEventListeners(instance: Instance): void {\n    const {reference} = instance;\n\n    on(reference, 'touchstart', onTrigger, TOUCH_OPTIONS);\n    on(reference, 'mouseover', onTrigger);\n    on(reference, 'focusin', onTrigger);\n    on(reference, 'click', onTrigger);\n  }\n\n  function removeEventListeners(): void {\n    listeners.forEach(({node, eventType, handler, options}: ListenerObject) => {\n      node.removeEventListener(eventType, handler, options);\n    });\n    listeners = [];\n  }\n\n  function applyMutations(instance: Instance): void {\n    const originalDestroy = instance.destroy;\n    const originalEnable = instance.enable;\n    const originalDisable = instance.disable;\n\n    instance.destroy = (shouldDestroyChildInstances = true): void => {\n      if (shouldDestroyChildInstances) {\n        childTippyInstances.forEach((instance) => {\n          instance.destroy();\n        });\n      }\n\n      childTippyInstances = [];\n\n      removeEventListeners();\n      originalDestroy();\n    };\n\n    instance.enable = (): void => {\n      originalEnable();\n      childTippyInstances.forEach((instance) => instance.enable());\n      disabled = false;\n    };\n\n    instance.disable = (): void => {\n      originalDisable();\n      childTippyInstances.forEach((instance) => instance.disable());\n      disabled = true;\n    };\n\n    addEventListeners(instance);\n  }\n\n  normalizedReturnValue.forEach(applyMutations);\n\n  return returnValue;\n}\n\nexport default delegate;\n", "import {BACKDROP_CLASS} from '../constants';\nimport {div, setVisibilityState} from '../dom-utils';\nimport {getChildren} from '../template';\nimport {AnimateFill} from '../types';\nimport {errorWhen} from '../validation';\n\nconst animateFill: AnimateFill = {\n  name: 'animateFill',\n  defaultValue: false,\n  fn(instance) {\n    // @ts-ignore\n    if (!instance.props.render?.$$tippy) {\n      if (__DEV__) {\n        errorWhen(\n          instance.props.animateFill,\n          'The `animateFill` plugin requires the default render function.'\n        );\n      }\n\n      return {};\n    }\n\n    const {box, content} = getChildren(instance.popper);\n\n    const backdrop = instance.props.animateFill\n      ? createBackdropElement()\n      : null;\n\n    return {\n      onCreate(): void {\n        if (backdrop) {\n          box.insertBefore(backdrop, box.firstElementChild!);\n          box.setAttribute('data-animatefill', '');\n          box.style.overflow = 'hidden';\n\n          instance.setProps({arrow: false, animation: 'shift-away'});\n        }\n      },\n      onMount(): void {\n        if (backdrop) {\n          const {transitionDuration} = box.style;\n          const duration = Number(transitionDuration.replace('ms', ''));\n\n          // The content should fade in after the backdrop has mostly filled the\n          // tooltip element. `clip-path` is the other alternative but is not\n          // well-supported and is buggy on some devices.\n          content.style.transitionDelay = `${Math.round(duration / 10)}ms`;\n\n          backdrop.style.transitionDuration = transitionDuration;\n          setVisibilityState([backdrop], 'visible');\n        }\n      },\n      onShow(): void {\n        if (backdrop) {\n          backdrop.style.transitionDuration = '0ms';\n        }\n      },\n      onHide(): void {\n        if (backdrop) {\n          setVisibilityState([backdrop], 'hidden');\n        }\n      },\n    };\n  },\n};\n\nexport default animateFill;\n\nfunction createBackdropElement(): HTMLDivElement {\n  const backdrop = div();\n  backdrop.className = BACKDROP_CLASS;\n  setVisibilityState([backdrop], 'hidden');\n  return backdrop;\n}\n", "import {getOwnerDocument, isMouseEvent} from '../dom-utils';\nimport {FollowCursor, Instance} from '../types';\n\nlet mouseCoords = {clientX: 0, clientY: 0};\nlet activeInstances: Array<{instance: Instance; doc: Document}> = [];\n\nfunction storeMouseCoords({clientX, clientY}: MouseEvent): void {\n  mouseCoords = {clientX, clientY};\n}\n\nfunction addMouseCoordsListener(doc: Document): void {\n  doc.addEventListener('mousemove', storeMouseCoords);\n}\n\nfunction removeMouseCoordsListener(doc: Document): void {\n  doc.removeEventListener('mousemove', storeMouseCoords);\n}\n\nconst followCursor: FollowCursor = {\n  name: 'followCursor',\n  defaultValue: false,\n  fn(instance) {\n    const reference = instance.reference;\n    const doc = getOwnerDocument(instance.props.triggerTarget || reference);\n\n    let isInternalUpdate = false;\n    let wasFocusEvent = false;\n    let isUnmounted = true;\n    let prevProps = instance.props;\n\n    function getIsInitialBehavior(): boolean {\n      return (\n        instance.props.followCursor === 'initial' && instance.state.isVisible\n      );\n    }\n\n    function addListener(): void {\n      doc.addEventListener('mousemove', onMouseMove);\n    }\n\n    function removeListener(): void {\n      doc.removeEventListener('mousemove', onMouseMove);\n    }\n\n    function unsetGetReferenceClientRect(): void {\n      isInternalUpdate = true;\n      instance.setProps({getReferenceClientRect: null});\n      isInternalUpdate = false;\n    }\n\n    function onMouseMove(event: MouseEvent): void {\n      // If the instance is interactive, avoid updating the position unless it's\n      // over the reference element\n      const isCursorOverReference = event.target\n        ? reference.contains(event.target as Node)\n        : true;\n      const {followCursor} = instance.props;\n      const {clientX, clientY} = event;\n\n      const rect = reference.getBoundingClientRect();\n      const relativeX = clientX - rect.left;\n      const relativeY = clientY - rect.top;\n\n      if (isCursorOverReference || !instance.props.interactive) {\n        instance.setProps({\n          // @ts-ignore - unneeded DOMRect properties\n          getReferenceClientRect() {\n            const rect = reference.getBoundingClientRect();\n\n            let x = clientX;\n            let y = clientY;\n\n            if (followCursor === 'initial') {\n              x = rect.left + relativeX;\n              y = rect.top + relativeY;\n            }\n\n            const top = followCursor === 'horizontal' ? rect.top : y;\n            const right = followCursor === 'vertical' ? rect.right : x;\n            const bottom = followCursor === 'horizontal' ? rect.bottom : y;\n            const left = followCursor === 'vertical' ? rect.left : x;\n\n            return {\n              width: right - left,\n              height: bottom - top,\n              top,\n              right,\n              bottom,\n              left,\n            };\n          },\n        });\n      }\n    }\n\n    function create(): void {\n      if (instance.props.followCursor) {\n        activeInstances.push({instance, doc});\n        addMouseCoordsListener(doc);\n      }\n    }\n\n    function destroy(): void {\n      activeInstances = activeInstances.filter(\n        (data) => data.instance !== instance\n      );\n\n      if (activeInstances.filter((data) => data.doc === doc).length === 0) {\n        removeMouseCoordsListener(doc);\n      }\n    }\n\n    return {\n      onCreate: create,\n      onDestroy: destroy,\n      onBeforeUpdate(): void {\n        prevProps = instance.props;\n      },\n      onAfterUpdate(_, {followCursor}): void {\n        if (isInternalUpdate) {\n          return;\n        }\n\n        if (\n          followCursor !== undefined &&\n          prevProps.followCursor !== followCursor\n        ) {\n          destroy();\n\n          if (followCursor) {\n            create();\n\n            if (\n              instance.state.isMounted &&\n              !wasFocusEvent &&\n              !getIsInitialBehavior()\n            ) {\n              addListener();\n            }\n          } else {\n            removeListener();\n            unsetGetReferenceClientRect();\n          }\n        }\n      },\n      onMount(): void {\n        if (instance.props.followCursor && !wasFocusEvent) {\n          if (isUnmounted) {\n            onMouseMove(mouseCoords as MouseEvent);\n            isUnmounted = false;\n          }\n\n          if (!getIsInitialBehavior()) {\n            addListener();\n          }\n        }\n      },\n      onTrigger(_, event): void {\n        if (isMouseEvent(event)) {\n          mouseCoords = {clientX: event.clientX, clientY: event.clientY};\n        }\n        wasFocusEvent = event.type === 'focus';\n      },\n      onHidden(): void {\n        if (instance.props.followCursor) {\n          unsetGetReferenceClientRect();\n          removeListener();\n          isUnmounted = true;\n        }\n      },\n    };\n  },\n};\n\nexport default followCursor;\n", "import {Modifier, Placement} from '@popperjs/core';\nimport {isMouseEvent} from '../dom-utils';\nimport {BasePlacement, InlinePositioning, Props} from '../types';\nimport {arrayFrom, getBasePlacement} from '../utils';\n\nfunction getProps(props: Props, modifier: Modifier<any, any>): Partial<Props> {\n  return {\n    popperOptions: {\n      ...props.popperOptions,\n      modifiers: [\n        ...(props.popperOptions?.modifiers || []).filter(\n          ({name}) => name !== modifier.name\n        ),\n        modifier,\n      ],\n    },\n  };\n}\n\nconst inlinePositioning: InlinePositioning = {\n  name: 'inlinePositioning',\n  defaultValue: false,\n  fn(instance) {\n    const {reference} = instance;\n\n    function isEnabled(): boolean {\n      return !!instance.props.inlinePositioning;\n    }\n\n    let placement: Placement;\n    let cursorRectIndex = -1;\n    let isInternalUpdate = false;\n    let triedPlacements: Array<string> = [];\n\n    const modifier: Modifier<\n      'tippyInlinePositioning',\n      Record<string, unknown>\n    > = {\n      name: 'tippyInlinePositioning',\n      enabled: true,\n      phase: 'afterWrite',\n      fn({state}) {\n        if (isEnabled()) {\n          if (triedPlacements.indexOf(state.placement) !== -1) {\n            triedPlacements = [];\n          }\n\n          if (\n            placement !== state.placement &&\n            triedPlacements.indexOf(state.placement) === -1\n          ) {\n            triedPlacements.push(state.placement);\n            instance.setProps({\n              // @ts-ignore - unneeded DOMRect properties\n              getReferenceClientRect: () =>\n                getReferenceClientRect(state.placement),\n            });\n          }\n\n          placement = state.placement;\n        }\n      },\n    };\n\n    function getReferenceClientRect(placement: Placement): Partial<DOMRect> {\n      return getInlineBoundingClientRect(\n        getBasePlacement(placement),\n        reference.getBoundingClientRect(),\n        arrayFrom(reference.getClientRects()),\n        cursorRectIndex\n      );\n    }\n\n    function setInternalProps(partialProps: Partial<Props>): void {\n      isInternalUpdate = true;\n      instance.setProps(partialProps);\n      isInternalUpdate = false;\n    }\n\n    function addModifier(): void {\n      if (!isInternalUpdate) {\n        setInternalProps(getProps(instance.props, modifier));\n      }\n    }\n\n    return {\n      onCreate: addModifier,\n      onAfterUpdate: addModifier,\n      onTrigger(_, event): void {\n        if (isMouseEvent(event)) {\n          const rects = arrayFrom(instance.reference.getClientRects());\n          const cursorRect = rects.find(\n            (rect) =>\n              rect.left - 2 <= event.clientX &&\n              rect.right + 2 >= event.clientX &&\n              rect.top - 2 <= event.clientY &&\n              rect.bottom + 2 >= event.clientY\n          );\n          const index = rects.indexOf(cursorRect);\n          cursorRectIndex = index > -1 ? index : cursorRectIndex;\n        }\n      },\n      onHidden(): void {\n        cursorRectIndex = -1;\n      },\n    };\n  },\n};\n\nexport default inlinePositioning;\n\nexport function getInlineBoundingClientRect(\n  currentBasePlacement: BasePlacement | null,\n  boundingRect: DOMRect,\n  clientRects: DOMRect[],\n  cursorRectIndex: number\n): {\n  top: number;\n  bottom: number;\n  left: number;\n  right: number;\n  width: number;\n  height: number;\n} {\n  // Not an inline element, or placement is not yet known\n  if (clientRects.length < 2 || currentBasePlacement === null) {\n    return boundingRect;\n  }\n\n  // There are two rects and they are disjoined\n  if (\n    clientRects.length === 2 &&\n    cursorRectIndex >= 0 &&\n    clientRects[0].left > clientRects[1].right\n  ) {\n    return clientRects[cursorRectIndex] || boundingRect;\n  }\n\n  switch (currentBasePlacement) {\n    case 'top':\n    case 'bottom': {\n      const firstRect = clientRects[0];\n      const lastRect = clientRects[clientRects.length - 1];\n      const isTop = currentBasePlacement === 'top';\n\n      const top = firstRect.top;\n      const bottom = lastRect.bottom;\n      const left = isTop ? firstRect.left : lastRect.left;\n      const right = isTop ? firstRect.right : lastRect.right;\n      const width = right - left;\n      const height = bottom - top;\n\n      return {top, bottom, left, right, width, height};\n    }\n    case 'left':\n    case 'right': {\n      const minLeft = Math.min(...clientRects.map((rects) => rects.left));\n      const maxRight = Math.max(...clientRects.map((rects) => rects.right));\n      const measureRects = clientRects.filter((rect) =>\n        currentBasePlacement === 'left'\n          ? rect.left === minLeft\n          : rect.right === maxRight\n      );\n\n      const top = measureRects[0].top;\n      const bottom = measureRects[measureRects.length - 1].bottom;\n      const left = minLeft;\n      const right = maxRight;\n      const width = right - left;\n      const height = bottom - top;\n\n      return {top, bottom, left, right, width, height};\n    }\n    default: {\n      return boundingRect;\n    }\n  }\n}\n", "import {VirtualElement} from '@popperjs/core';\nimport {ReferenceElement, Sticky} from '../types';\n\nconst sticky: Sticky = {\n  name: 'sticky',\n  defaultValue: false,\n  fn(instance) {\n    const {reference, popper} = instance;\n\n    function getReference(): ReferenceElement | VirtualElement {\n      return instance.popperInstance\n        ? instance.popperInstance.state.elements.reference\n        : reference;\n    }\n\n    function shouldCheck(value: 'reference' | 'popper'): boolean {\n      return instance.props.sticky === true || instance.props.sticky === value;\n    }\n\n    let prevRefRect: ClientRect | null = null;\n    let prevPopRect: ClientRect | null = null;\n\n    function updatePosition(): void {\n      const currentRefRect = shouldCheck('reference')\n        ? getReference().getBoundingClientRect()\n        : null;\n      const currentPopRect = shouldCheck('popper')\n        ? popper.getBoundingClientRect()\n        : null;\n\n      if (\n        (currentRefRect && areRectsDifferent(prevRefRect, currentRefRect)) ||\n        (currentPopRect && areRectsDifferent(prevPopRect, currentPopRect))\n      ) {\n        if (instance.popperInstance) {\n          instance.popperInstance.update();\n        }\n      }\n\n      prevRefRect = currentRefRect;\n      prevPopRect = currentPopRect;\n\n      if (instance.state.isMounted) {\n        requestAnimationFrame(updatePosition);\n      }\n    }\n\n    return {\n      onMount(): void {\n        if (instance.props.sticky) {\n          updatePosition();\n        }\n      },\n    };\n  },\n};\n\nexport default sticky;\n\nfunction areRectsDifferent(\n  rectA: ClientRect | null,\n  rectB: ClientRect | null\n): boolean {\n  if (rectA && rectB) {\n    return (\n      rectA.top !== rectB.top ||\n      rectA.right !== rectB.right ||\n      rectA.bottom !== rectB.bottom ||\n      rectA.left !== rectB.left\n    );\n  }\n\n  return true;\n}\n", "import css from '../dist/tippy.css';\nimport {injectCSS} from '../src/css';\nimport {isBrowser} from '../src/browser';\nimport tippy, {hideAll} from '../src';\nimport createSingleton from '../src/addons/createSingleton';\nimport delegate from '../src/addons/delegate';\nimport animateFill from '../src/plugins/animateFill';\nimport followCursor from '../src/plugins/followCursor';\nimport inlinePositioning from '../src/plugins/inlinePositioning';\nimport sticky from '../src/plugins/sticky';\nimport {ROUND_ARROW} from '../src/constants';\nimport {render} from '../src/template';\n\nif (isBrowser) {\n  injectCSS(css);\n}\n\ntippy.setDefaultProps({\n  plugins: [animateFill, followCursor, inlinePositioning, sticky],\n  render,\n});\n\ntippy.createSingleton = createSingleton;\ntippy.delegate = delegate;\ntippy.hideAll = hideAll;\ntippy.roundArrow = ROUND_ARROW;\n\nexport default tippy;\n", "export function injectCSS(css: string): void {\n  const style = document.createElement('style');\n  style.textContent = css;\n  style.setAttribute('data-__NAMESPACE_PREFIX__-stylesheet', '');\n  const head = document.head;\n  const firstStyleOrLinkTag = document.querySelector('head>style,head>link');\n\n  if (firstStyleOrLinkTag) {\n    head.insertBefore(style, firstStyleOrLinkTag);\n  } else {\n    head.appendChild(style);\n  }\n}\n"], "names": ["<PERSON><PERSON><PERSON><PERSON>", "window", "document", "isIE11", "msCrypto", "TOUCH_OPTIONS", "passive", "capture", "TIPPY_DEFAULT_APPEND_TO", "body", "getValueAtIndexOrReturn", "value", "index", "defaultValue", "Array", "isArray", "v", "isType", "type", "str", "toString", "call", "indexOf", "invokeWithArgsOrReturn", "args", "debounce", "fn", "ms", "arg", "clearTimeout", "timeout", "setTimeout", "removeProperties", "obj", "keys", "clone", "for<PERSON>ach", "key", "normalizeToArray", "concat", "pushIfUnique", "arr", "push", "getBasePlacement", "placement", "split", "arrayFrom", "slice", "removeUndefinedProps", "Object", "reduce", "acc", "undefined", "div", "createElement", "isElement", "some", "isMouseEvent", "isReferenceElement", "_tippy", "reference", "getArrayOfElements", "isNodeList", "querySelectorAll", "setTransitionDuration", "els", "el", "style", "transitionDuration", "setVisibilityState", "state", "setAttribute", "getOwnerDocument", "elementOrElements", "element", "ownerDocument", "updateTransitionEndListener", "box", "action", "listener", "method", "event", "actualContains", "parent", "child", "target", "contains", "getRootNode", "_target$getRootNode", "host", "currentInput", "is<PERSON><PERSON>ch", "lastMouseMoveTime", "onDocumentTouchStart", "performance", "addEventListener", "onDocumentMouseMove", "now", "removeEventListener", "onWindowBlur", "activeElement", "instance", "blur", "isVisible", "defaultProps", "appendTo", "aria", "content", "expanded", "delay", "duration", "getReferenceClientRect", "hideOnClick", "ignoreAttributes", "interactive", "interactiveBorder", "interactiveDebounce", "moveTransition", "offset", "onAfterUpdate", "onBeforeUpdate", "onCreate", "onDestroy", "onHidden", "onHide", "onMount", "onShow", "onShown", "onTrigger", "onUntrigger", "onClickOutside", "plugins", "popperOptions", "render", "showOnCreate", "touch", "trigger", "triggerTarget", "animateFill", "followCursor", "inlinePositioning", "sticky", "allowHTML", "animation", "arrow", "inertia", "max<PERSON><PERSON><PERSON>", "role", "theme", "zIndex", "defaultKeys", "getExtendedPassedProps", "passedProps", "pluginProps", "plugin", "name", "evaluateProps", "props", "out", "valueAsString", "getAttribute", "trim", "JSON", "parse", "e", "getDataAttributeProps", "dangerouslySetInnerHTML", "html", "createArrowElement", "className", "append<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "textContent", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "popper", "first<PERSON><PERSON><PERSON><PERSON><PERSON>", "boxChildren", "children", "find", "node", "classList", "backdrop", "onUpdate", "prevProps", "nextProps", "removeAttribute", "<PERSON><PERSON><PERSON><PERSON>", "$$tippy", "idCounter", "mouseMoveListeners", "mountedInstances", "createTippy", "showTimeout", "hideTimeout", "scheduleHideAnimationFrame", "lastTriggerEvent", "currentTransitionEndListener", "onFirstUpdate", "currentTarget", "isVisibleFromClick", "didHideDueToDocumentMouseDown", "didTouchMove", "ignoreOnFirstUpdate", "listeners", "debouncedOnMouseMove", "onMouseMove", "id", "filter", "item", "popperInstance", "isEnabled", "isDestroyed", "isMounted", "isShown", "clearDelayTimeouts", "cancelAnimationFrame", "setProps", "partialProps", "invokeHook", "removeListeners", "addListeners", "cleanupInteractiveMouseListeners", "handleAriaExpandedAttribute", "handleStyles", "createPopperInstance", "getNestedPopperTree", "nestedPopper", "requestAnimationFrame", "forceUpdate", "show", "isAlreadyVisible", "isDisabled", "isTouchAndTouchDisabled", "getC<PERSON>rentTarget", "hasAttribute", "getIsDefaultRenderFn", "visibility", "addDocumentPress", "transition", "getDefaultTemplateChildren", "offsetHeight", "handleAriaContentAttribute", "callback", "onTransitionEnd", "onTransitionedIn", "parentNode", "mount", "hide", "isAlreadyHidden", "removeDocumentPress", "onTransitionedOut", "unmount", "hideWithInteractivity", "getDocument", "enable", "disable", "destroyPopperInstance", "i", "destroy", "pluginsHooks", "map", "hasAriaExpanded", "scheduleShow", "getNormalizedTouchSettings", "getIsCustomTouchBehavior", "_instance$props$rende", "get<PERSON>elay", "isShow", "fromHide", "pointerEvents", "hook", "shouldInvokePropsHook", "pluginHooks", "attr", "currentValue", "nextValue", "replace", "onDocumentPress", "actual<PERSON>arget", "<PERSON><PERSON><PERSON>", "onTouchMove", "onTouchStart", "doc", "on", "eventType", "handler", "options", "onMouseLeave", "Boolean", "onBlurOrFocusOut", "shouldScheduleClickHide", "isEventListenerStopped", "wasFocused", "scheduleHide", "isCursorOverReferenceOrPopper", "popperTreeData", "clientX", "clientY", "every", "popperRect", "popperState", "basePlacement", "offsetData", "modifiersData", "topDistance", "top", "y", "bottomDistance", "bottom", "leftDistance", "left", "x", "rightDistance", "right", "exceedsTop", "exceedsBottom", "exceedsLeft", "exceedsRight", "isCursorOutsideInteractiveBorder", "_instance$popperInsta", "getBoundingClientRect", "relatedTarget", "computedReference", "contextElement", "modifiers", "padding", "adaptive", "enabled", "phase", "requires", "attributes", "createPopper", "touchValue", "touchDelay", "tippy", "targets", "optionalProps", "instances", "setDefaultProps", "applyStylesModifier", "applyStyles", "effect", "initialStyles", "position", "strategy", "margin", "assign", "elements", "styles", "BUBBLING_EVENTS_MAP", "mouseover", "focusin", "click", "createBackdropElement", "insertBefore", "overflow", "Number", "transitionDelay", "Math", "round", "mouseCoords", "activeInstances", "storeMouseCoords", "isInternalUpdate", "wasFocusEvent", "isUnmounted", "getIsInitialBehavior", "addListener", "removeListener", "unsetGetReferenceClientRect", "isCursorOverReference", "rect", "relativeX", "relativeY", "width", "height", "create", "addMouseCoordsListener", "data", "length", "removeMouseCoordsListener", "_", "cursorRectIndex", "triedPlacements", "modifier", "currentBasePlacement", "boundingRect", "clientRects", "firstRect", "lastRect", "isTop", "minLeft", "min", "rects", "maxRight", "max", "measureRects", "getInlineBoundingClientRect", "getClientRects", "addModifier", "getProps", "cursorRect", "<PERSON><PERSON><PERSON><PERSON>", "prevRefRect", "prevPopRect", "updatePosition", "currentRefRect", "currentPopRect", "areRectsDifferent", "update", "rectA", "rectB", "css", "head", "firstStyleOrLinkTag", "querySelector", "injectCSS", "createSingleton", "tippyInstances", "individualInstances", "references", "triggerTargets", "overrides", "interceptSetPropsCleanups", "shownOnCreate", "setTriggerTargets", "setReferences", "enableInstances", "interceptSetProps", "singleton", "originalSetProps", "prepareInstance", "overrideProps", "prop", "_references$index", "originalShow", "ref", "showNext", "first", "showPrevious", "last", "setInstances", "nextInstances", "delegate", "childTippyInstances", "disabled", "nativeProps", "parentProps", "childProps", "returnValue", "targetNode", "closest", "original<PERSON><PERSON>roy", "originalEnable", "originalDisable", "shouldDestroyChildInstances", "addEventListeners", "hide<PERSON>ll", "excludedReferenceOrInstance", "exclude", "isExcluded", "originalDuration", "roundArrow"], "mappings": "iPAAO,IAAMA,EACO,oBAAXC,QAA8C,oBAAbC,SAE7BC,IAASH,KAEhBC,OAAOG,SCIAC,EAAgB,CAACC,SAAS,EAAMC,SAAS,GAEzCC,EAA0B,kBAAMN,SAASO,eCFtCC,EACdC,EACAC,EACAC,MAEIC,MAAMC,QAAQJ,GAAQ,KAClBK,EAAIL,EAAMC,UACJ,MAALI,EACHF,MAAMC,QAAQF,GACZA,EAAaD,GACbC,EACFG,SAGCL,EAGF,SAASM,EAAON,EAAYO,OAC3BC,EAAM,GAAGC,SAASC,KAAKV,UACK,IAA3BQ,EAAIG,QAAQ,YAAoBH,EAAIG,QAAWJ,QAAY,EAG7D,SAASK,EAAuBZ,EAAYa,SACzB,mBAAVb,EAAuBA,eAASa,GAAQb,EAGjD,SAASc,EACdC,EACAC,UAGW,IAAPA,EACKD,EAKF,SAACE,GACNC,aAAaC,GACbA,EAAUC,YAAW,WACnBL,EAAGE,KACFD,QANDG,EAUC,SAASE,EAAoBC,EAAQC,OACpCC,mBAAYF,UAClBC,EAAKE,SAAQ,SAACC,UACJF,EAAcE,MAEjBF,EAOF,SAASG,EAAoB3B,SAC1B,GAAW4B,OAAO5B,GAGrB,SAAS6B,EAAgBC,EAAU9B,IACZ,IAAxB8B,EAAInB,QAAQX,IACd8B,EAAIC,KAAK/B,GAgBN,SAASgC,EAAiBC,UACxBA,EAAUC,MAAM,KAAK,GAGvB,SAASC,EAAUnC,SACjB,GAAGoC,MAAM1B,KAAKV,GAGhB,SAASqC,EACdf,UAEOgB,OAAOf,KAAKD,GAAKiB,QAAO,SAACC,EAAKd,eAClBe,IAAbnB,EAAII,KACLc,EAAYd,GAAOJ,EAAII,IAGnBc,IACN,aCrGWE,WACPnD,SAASoD,cAAc,OAGzB,SAASC,EAAU5C,SACjB,CAAC,UAAW,YAAY6C,MAAK,SAACtC,UAASD,EAAON,EAAOO,MAOvD,SAASuC,EAAa9C,UACpBM,EAAON,EAAO,cAGhB,SAAS+C,EAAmB/C,YACvBA,IAASA,EAAMgD,QAAUhD,EAAMgD,OAAOC,YAAcjD,GAGzD,SAASkD,EAAmBlD,UAC7B4C,EAAU5C,GACL,CAACA,GAdL,SAAoBA,UAClBM,EAAON,EAAO,YAgBjBmD,CAAWnD,GACNmC,EAAUnC,GAGfG,MAAMC,QAAQJ,GACTA,EAGFmC,EAAU5C,SAAS6D,iBAAiBpD,IAGtC,SAASqD,EACdC,EACAtD,GAEAsD,EAAI7B,SAAQ,SAAC8B,GACPA,IACFA,EAAGC,MAAMC,mBAAwBzD,WAKhC,SAAS0D,EACdJ,EACAK,GAEAL,EAAI7B,SAAQ,SAAC8B,GACPA,GACFA,EAAGK,aAAa,aAAcD,MAK7B,SAASE,EACdC,SAEOC,EAAWpC,EAAiBmC,mBAG5BC,YAAAA,EAASC,kBAAelE,KAAOiE,EAAQC,cAAgBzE,SAoCzD,SAAS0E,EACdC,EACAC,EACAC,OAEMC,EAAYF,mBAMjB,gBAAiB,uBAAuB1C,SAAQ,SAAC6C,GAChDJ,EAAIG,GAAQC,EAAOF,MAQhB,SAASG,EAAeC,EAAiBC,WAC1CC,EAASD,EACNC,GAAQ,UACTF,EAAOG,SAASD,UACX,EAETA,QAAUA,EAAOE,sBAAPF,EAAOE,sBAARC,EAAiCC,YAErC,MCjIIC,EAAe,CAACC,SAAS,GAClCC,EAAoB,EAQjB,SAASC,IACVH,EAAaC,UAIjBD,EAAaC,SAAU,EAEnB1F,OAAO6F,aACT5F,SAAS6F,iBAAiB,YAAaC,IASpC,SAASA,QACRC,EAAMH,YAAYG,MAEpBA,EAAML,EAAoB,KAC5BF,EAAaC,SAAU,EAEvBzF,SAASgG,oBAAoB,YAAaF,IAG5CJ,EAAoBK,EASf,SAASE,QACRC,EAAgBlG,SAASkG,iBAE3B1C,EAAmB0C,GAAgB,KAC/BC,EAAWD,EAAczC,OAE3ByC,EAAcE,OAASD,EAAS/B,MAAMiC,WACxCH,EAAcE,YC1BPE,iBACXC,SAAUjG,EACVkG,KAAM,CACJC,QAAS,OACTC,SAAU,QAEZC,MAAO,EACPC,SAAU,CAAC,IAAK,KAChBC,uBAAwB,KACxBC,aAAa,EACbC,kBAAkB,EAClBC,aAAa,EACbC,kBAAmB,EACnBC,oBAAqB,EACrBC,eAAgB,GAChBC,OAAQ,CAAC,EAAG,IACZC,2BACAC,4BACAC,sBACAC,uBACAC,sBACAC,oBACAC,qBACAC,oBACAC,qBACAC,uBACAC,yBACAC,4BACAtF,UAAW,MACXuF,QAAS,GACTC,cAAe,GACfC,OAAQ,KACRC,cAAc,EACdC,OAAO,EACPC,QAAS,mBACTC,cAAe,MAtDG,CAClBC,aAAa,EACbC,cAAc,EACdC,mBAAmB,EACnBC,QAAQ,GAGU,CAClBC,WAAW,EACXC,UAAW,OACXC,OAAO,EACPrC,QAAS,GACTsC,SAAS,EACTC,SAAU,IACVC,KAAM,UACNC,MAAO,GACPC,OAAQ,OA2CJC,EAAcrG,OAAOf,KAAKsE,GAczB,SAAS+C,EACdC,OAGMC,GADUD,EAAYrB,SAAW,IACXjF,QAAgC,SAACC,EAAKuG,SACzDC,EAAsBD,EAAtBC,KAAM9I,EAAgB6I,EAAhB7I,aAET8I,IACFxG,EAAIwG,QACoBvG,IAAtBoG,EAAYG,GACRH,EAAYG,YACXnD,EAAqBmD,MAAS9I,UAGhCsC,IACN,4BAGEqG,EACAC,GAwCA,SAASG,EACdhG,EACAiG,OAEMC,mBACDD,GACHlD,QAASpF,EAAuBsI,EAAMlD,QAAS,CAAC/C,KAC5CiG,EAAM5C,iBACN,GA5CD,SACLrD,EACAuE,UAEiBA,EACblF,OAAOf,KAAKqH,mBAA2B/C,GAAc2B,QAAAA,MACrDmB,GAEmBpG,QACrB,SAACC,EAA+Cd,OACxC0H,GACJnG,EAAUoG,2BAA2B3H,IAAU,IAC/C4H,WAEGF,SACI5G,KAGG,YAARd,EACFc,EAAId,GAAO0H,WAGT5G,EAAId,GAAO6H,KAAKC,MAAMJ,GACtB,MAAOK,GACPjH,EAAId,GAAO0H,SAIR5G,IAET,IAeIkH,CAAsBzG,EAAWiG,EAAM1B,iBAG7C2B,EAAIpD,sBACCF,EAAaE,KACboD,EAAIpD,MAGToD,EAAIpD,KAAO,CACTE,SACwB,SAAtBkD,EAAIpD,KAAKE,SAAsBiD,EAAM3C,YAAc4C,EAAIpD,KAAKE,SAC9DD,QACuB,SAArBmD,EAAIpD,KAAKC,QACLkD,EAAM3C,YACJ,KACA,cACF4C,EAAIpD,KAAKC,SAGVmD,EC1JT,SAASQ,EAAwB5F,EAAkB6F,GACjD7F,EAAO,UAAgB6F,EAGzB,SAASC,EAAmB7J,OACpBqI,EAAQ3F,WAEA,IAAV1C,EACFqI,EAAMyB,yBAENzB,EAAMyB,4BAEFlH,EAAU5C,GACZqI,EAAM0B,YAAY/J,GAElB2J,EAAwBtB,EAAOrI,IAI5BqI,EAGF,SAAS2B,EAAWhE,EAAyBkD,GAC9CtG,EAAUsG,EAAMlD,UAClB2D,EAAwB3D,EAAS,IACjCA,EAAQ+D,YAAYb,EAAMlD,UACQ,mBAAlBkD,EAAMlD,UAClBkD,EAAMf,UACRwB,EAAwB3D,EAASkD,EAAMlD,SAEvCA,EAAQiE,YAAcf,EAAMlD,SAK3B,SAASkE,EAAYC,OACpBjG,EAAMiG,EAAOC,kBACbC,EAAclI,EAAU+B,EAAIoG,gBAE3B,CACLpG,IAAAA,EACA8B,QAASqE,EAAYE,MAAK,SAACC,UAASA,EAAKC,UAAU9F,6BACnD0D,MAAOgC,EAAYE,MACjB,SAACC,UACCA,EAAKC,UAAU9F,yBACf6F,EAAKC,UAAU9F,+BAEnB+F,SAAUL,EAAYE,MAAK,SAACC,UAC1BA,EAAKC,UAAU9F,+BAKd,SAAS+C,EACdhC,OAKMyE,EAASzH,IAETwB,EAAMxB,IACZwB,EAAI4F,sBACJ5F,EAAIN,aAAa,aAAc,UAC/BM,EAAIN,aAAa,WAAY,UAEvBoC,EAAUtD,aAWPiI,EAASC,EAAkBC,SACJX,EAAYC,GAAnCjG,IAAAA,IAAK8B,IAAAA,QAASqC,IAAAA,MAEjBwC,EAAUpC,MACZvE,EAAIN,aAAa,aAAciH,EAAUpC,OAEzCvE,EAAI4G,gBAAgB,cAGa,iBAAxBD,EAAUzC,UACnBlE,EAAIN,aAAa,iBAAkBiH,EAAUzC,WAE7ClE,EAAI4G,gBAAgB,kBAGlBD,EAAUvC,QACZpE,EAAIN,aAAa,eAAgB,IAEjCM,EAAI4G,gBAAgB,gBAGtB5G,EAAIV,MAAM+E,SACsB,iBAAvBsC,EAAUtC,SACVsC,EAAUtC,cACbsC,EAAUtC,SAEZsC,EAAUrC,KACZtE,EAAIN,aAAa,OAAQiH,EAAUrC,MAEnCtE,EAAI4G,gBAAgB,QAIpBF,EAAU5E,UAAY6E,EAAU7E,SAChC4E,EAAUzC,YAAc0C,EAAU1C,WAElC6B,EAAWhE,EAASN,EAASwD,OAG3B2B,EAAUxC,MACPA,EAEMuC,EAAUvC,QAAUwC,EAAUxC,QACvCnE,EAAI6G,YAAY1C,GAChBnE,EAAI6F,YAAYF,EAAmBgB,EAAUxC,SAH7CnE,EAAI6F,YAAYF,EAAmBgB,EAAUxC,QAKtCA,GACTnE,EAAI6G,YAAY1C,UAzDpBrC,EAAQ8D,0BACR9D,EAAQpC,aAAa,aAAc,UAEnCoG,EAAWhE,EAASN,EAASwD,OAE7BiB,EAAOJ,YAAY7F,GACnBA,EAAI6F,YAAY/D,GAEhB2E,EAASjF,EAASwD,MAAOxD,EAASwD,OAqD3B,CACLiB,OAAAA,EACAQ,SAAAA,GAMJjD,EAAOsD,SAAU,ECjHjB,IAAIC,EAAY,EACZC,EAAsD,GAG/CC,EAA+B,GAE3B,SAASC,EACtBnI,EACA4F,OAUIwC,EACAC,EACAC,EAKAC,EACAC,EACAC,EAGAC,ELYoB7J,EKhClBoH,EAAQD,EAAchG,mBACvB4C,EACA+C,EAAuBvG,EAAqBwG,MAS7C+C,GAAqB,EACrBC,GAAgC,EAChCC,GAAe,EACfC,GAAsB,EAItBC,EAA8B,GAC9BC,EAAuBnL,EAASoL,GAAahD,EAAMzC,qBAMjD0F,EAAKlB,IAELzD,GLKkB1F,EKLDoH,EAAM1B,SLMlB4E,QAAO,SAACC,EAAMpM,UAAU6B,EAAInB,QAAQ0L,KAAUpM,KKSnDyF,EAAqB,CAEzByG,GAAAA,EACAlJ,UAAAA,EACAkH,OAAQzH,IACR4J,eArBqB,KAsBrBpD,MAAAA,EACAvF,MApBY,CAEZ4I,WAAW,EAEX3G,WAAW,EAEX4G,aAAa,EAEbC,WAAW,EAEXC,SAAS,GAWTlF,QAAAA,EAEAmF,8BAuuBAzL,aAAamK,GACbnK,aAAaoK,GACbsB,qBAAqBrB,IAxuBrBsB,kBA2uBgBC,MAMZpH,EAAS/B,MAAM6I,mBAInBO,GAAW,iBAAkB,CAACrH,EAAUoH,IAExCE,SAEMpC,EAAYlF,EAASwD,MACrB2B,EAAY5B,EAAchG,mBAC3B2H,EACAvI,EAAqByK,IACxBxG,kBAAkB,KAGpBZ,EAASwD,MAAQ2B,EAEjBoC,KAEIrC,EAAUnE,sBAAwBoE,EAAUpE,sBAC9CyG,KACAjB,EAAuBnL,EACrBoL,GACArB,EAAUpE,sBAKVmE,EAAU9C,gBAAkB+C,EAAU/C,cACxCnG,EAAiBiJ,EAAU9C,eAAerG,SAAQ,SAAC+I,GACjDA,EAAKM,gBAAgB,oBAEdD,EAAU/C,eACnB7E,EAAU6H,gBAAgB,iBAG5BqC,KACAC,KAEIzC,GACFA,EAASC,EAAWC,GAGlBnF,EAAS4G,iBACXe,KAMAC,KAAsB7L,SAAQ,SAAC8L,GAG7BC,sBAAsBD,EAAavK,OAAQsJ,eAAgBmB,iBAI/DV,GAAW,gBAAiB,CAACrH,EAAUoH,KAzyBvC9C,oBA4yBkBhE,GAClBN,EAASmH,SAAS,CAAC7G,QAAAA,KA5yBnB0H,oBAszBMC,EAAmBjI,EAAS/B,MAAMiC,UAClC4G,EAAc9G,EAAS/B,MAAM6I,YAC7BoB,GAAclI,EAAS/B,MAAM4I,UAC7BsB,EACJ9I,EAAaC,UAAYU,EAASwD,MAAMtB,MACpCzB,EAAWpG,EACf2F,EAASwD,MAAM/C,SACf,EACAN,EAAaM,aAIbwH,GACAnB,GACAoB,GACAC,YAQEC,KAAmBC,aAAa,sBAIpChB,GAAW,SAAU,CAACrH,IAAW,IACO,IAApCA,EAASwD,MAAM/B,OAAOzB,UAI1BA,EAAS/B,MAAMiC,WAAY,EAEvBoI,OACF7D,EAAO3G,MAAMyK,WAAa,WAG5Bb,KACAc,KAEKxI,EAAS/B,MAAM8I,YAClBtC,EAAO3G,MAAM2K,WAAa,WAKxBH,KAAwB,OACHI,KAAhBlK,IAAAA,IAAK8B,IAAAA,QACZ3C,EAAsB,CAACa,EAAK8B,GAAU,GAGxC0F,EAAgB,oBACThG,EAAS/B,MAAMiC,YAAamG,MAIjCA,GAAsB,EAGjB5B,EAAOkE,aAEZlE,EAAO3G,MAAM2K,WAAazI,EAASwD,MAAMxC,eAErCsH,MAA0BtI,EAASwD,MAAMd,UAAW,OAC/BgG,KAAhBlK,IAAAA,IAAK8B,IAAAA,QACZ3C,EAAsB,CAACa,EAAK8B,GAAUG,GACtCzC,EAAmB,CAACQ,EAAK8B,GAAU,WAGrCsI,KACAnB,KAEAtL,EAAasJ,EAAkBzF,YAI/BA,EAAS4G,mBAAgBmB,cAEzBV,GAAW,UAAW,CAACrH,IAEnBA,EAASwD,MAAMd,WAAa4F,eAxmBV7H,EAAkBoI,GAC1CC,GAAgBrI,EAAUoI,GAwmBtBE,CAAiBtI,GAAU,WACzBT,EAAS/B,MAAM+I,SAAU,EACzBK,GAAW,UAAW,CAACrH,wBAlTzBgJ,EAFG5I,EAAYJ,EAASwD,MAArBpD,SASD0E,EAAOsD,KAMXY,EAHChJ,EAASwD,MAAM3C,aAAeT,IAAajG,GAC/B,WAAbiG,EAEa0E,EAAKkE,WAEL9N,EAAuBkF,EAAU,CAAC0E,IAK5CkE,EAAW/J,SAASwF,IACvBuE,EAAW3E,YAAYI,GAGzBzE,EAAS/B,MAAM8I,WAAY,EAE3BY,KA6RAsB,IA94BAC,oBAw5BMC,GAAmBnJ,EAAS/B,MAAMiC,UAClC4G,EAAc9G,EAAS/B,MAAM6I,YAC7BoB,GAAclI,EAAS/B,MAAM4I,UAC7BpG,EAAWpG,EACf2F,EAASwD,MAAM/C,SACf,EACAN,EAAaM,aAGX0I,GAAmBrC,GAAeoB,YAItCb,GAAW,SAAU,CAACrH,IAAW,IACO,IAApCA,EAASwD,MAAMjC,OAAOvB,UAI1BA,EAAS/B,MAAMiC,WAAY,EAC3BF,EAAS/B,MAAM+I,SAAU,EACzBX,GAAsB,EACtBH,GAAqB,EAEjBoC,OACF7D,EAAO3G,MAAMyK,WAAa,aAG5Bf,KACA4B,KACA1B,IAAa,GAETY,KAAwB,OACHI,KAAhBlK,IAAAA,IAAK8B,IAAAA,QAERN,EAASwD,MAAMd,YACjB/E,EAAsB,CAACa,EAAK8B,GAAUG,GACtCzC,EAAmB,CAACQ,EAAK8B,GAAU,WAIvCsI,KACAnB,KAEIzH,EAASwD,MAAMd,UACb4F,eAlrBmB7H,EAAkBoI,GAC3CC,GAAgBrI,GAAU,YAErBT,EAAS/B,MAAMiC,WAChBuE,EAAOuE,YACPvE,EAAOuE,WAAW/J,SAASwF,IAE3BoE,OA4qBAQ,CAAkB5I,EAAUT,EAASsJ,SAGvCtJ,EAASsJ,WAv8BXC,+BA28B6B3K,GAS7B4K,KAAc9J,iBAAiB,YAAa6G,GAC5CpK,EAAaqJ,EAAoBe,GACjCA,EAAqB3H,IAr9BrB6K,kBAstBAzJ,EAAS/B,MAAM4I,WAAY,GArtB3B6C,mBA2tBA1J,EAASkJ,OACTlJ,EAAS/B,MAAM4I,WAAY,GA3tB3ByC,mBA49BItJ,EAAS/B,MAAMiC,WACjBF,EAASkJ,WAGNlJ,EAAS/B,MAAM8I,iBAIpB4C,KAKA/B,KAAsB7L,SAAQ,SAAC8L,GAC7BA,EAAavK,OAAQgM,aAGnB7E,EAAOuE,YACTvE,EAAOuE,WAAW3D,YAAYZ,GAGhCgB,EAAmBA,EAAiBiB,QAAO,SAACkD,UAAMA,IAAM5J,KAExDA,EAAS/B,MAAM8I,WAAY,EAC3BM,GAAW,WAAY,CAACrH,KAn/BxB6J,sBA4/BI7J,EAAS/B,MAAM6I,mBAInB9G,EAASiH,qBACTjH,EAASsJ,UAEThC,YAEO/J,EAAUD,OAEjB0C,EAAS/B,MAAM6I,aAAc,EAE7BO,GAAW,YAAa,CAACrH,UAngCtBwD,EAAMxB,cAKFhC,QAMkBwD,EAAMxB,OAAOhC,GAAjCyE,IAAAA,OAAQQ,IAAAA,SAEfR,EAAOvG,aAAa,kBAAkC,IACtDuG,EAAOgC,YAA6BzG,EAASyG,GAE7CzG,EAASyE,OAASA,EAClBlH,EAAUD,OAAS0C,EACnByE,EAAOnH,OAAS0C,MAEV8J,EAAehI,EAAQiI,KAAI,SAAC1G,UAAWA,EAAOhI,GAAG2E,MACjDgK,EAAkBzM,EAAU8K,aAAa,wBAE/Cd,KACAE,KACAC,KAEAL,GAAW,WAAY,CAACrH,IAEpBwD,EAAMvB,cACRgI,KAKFxF,EAAO/E,iBAAiB,cAAc,WAChCM,EAASwD,MAAM3C,aAAeb,EAAS/B,MAAMiC,WAC/CF,EAASiH,wBAIbxC,EAAO/E,iBAAiB,cAAc,WAElCM,EAASwD,MAAM3C,aACfb,EAASwD,MAAMrB,QAAQlH,QAAQ,eAAiB,GAEhDuO,KAAc9J,iBAAiB,YAAa6G,MAIzCvG,WAKEkK,QACAhI,EAASlC,EAASwD,MAAlBtB,aACAzH,MAAMC,QAAQwH,GAASA,EAAQ,CAACA,EAAO,YAGvCiI,UACoC,SAApCD,IAA6B,YAG7B5B,4BAEEtI,EAASwD,MAAMxB,UAAfoI,EAAuB9E,kBAGzB8C,YACAnC,GAAiB1I,WAGjBiM,SACD1K,EAASsJ,KAAmBY,kBAC3BlK,EAASX,EAAiBW,GAAUjF,kBAGpC6O,YACAlE,EAAYC,YAGZ4F,GAASC,UAKbtK,EAAS/B,MAAM8I,YAAc/G,EAAS/B,MAAMiC,WAC7Cb,EAAaC,SACZwG,GAA8C,UAA1BA,EAAiBjL,KAE/B,EAGFR,EACL2F,EAASwD,MAAMhD,MACf8J,EAAS,EAAI,EACbnK,EAAaK,gBAIRkH,GAAa6C,YAAAA,IAAAA,GAAW,GAC/B9F,EAAO3G,MAAM0M,cACXxK,EAASwD,MAAM3C,cAAgB0J,EAAW,GAAK,OACjD9F,EAAO3G,MAAMkF,UAAYhD,EAASwD,MAAMR,gBAGjCqE,GACPoD,EACAtP,EACAuP,mBAAAA,IAAAA,GAAwB,GAExBZ,EAAa/N,SAAQ,SAAC4O,GAChBA,EAAYF,IACdE,EAAYF,SAAZE,EAAsBxP,MAItBuP,OACF1K,EAASwD,OAAMiH,WAAStP,YAInByN,SACAvI,EAAQL,EAASwD,MAAjBnD,QAEFA,EAAKC,aAIJsK,UAAevK,EAAKC,QACpBmG,EAAKhC,EAAOgC,GACJxK,EAAiB+D,EAASwD,MAAMpB,eAAiB7E,GAEzDxB,SAAQ,SAAC+I,OACP+F,EAAe/F,EAAKnB,aAAaiH,MAEnC5K,EAAS/B,MAAMiC,UACjB4E,EAAK5G,aAAa0M,EAAMC,EAAkBA,MAAgBpE,EAAOA,OAC5D,KACCqE,EAAYD,GAAgBA,EAAaE,QAAQtE,EAAI,IAAI7C,OAE3DkH,EACFhG,EAAK5G,aAAa0M,EAAME,GAExBhG,EAAKM,gBAAgBwF,iBAMpBnD,MACHuC,GAAoBhK,EAASwD,MAAMnD,KAAKE,UAI9BtE,EAAiB+D,EAASwD,MAAMpB,eAAiB7E,GAEzDxB,SAAQ,SAAC+I,GACT9E,EAASwD,MAAM3C,YACjBiE,EAAK5G,aACH,gBACA8B,EAAS/B,MAAMiC,WAAa4E,IAASsD,KACjC,OACA,SAGNtD,EAAKM,gBAAgB,6BAKlBoC,KACPgC,KAAc3J,oBAAoB,YAAa0G,GAC/Cf,EAAqBA,EAAmBkB,QACtC,SAAChI,UAAaA,IAAa6H,cAItByE,GAAgBpM,OAEnBS,EAAaC,UACX8G,GAA+B,cAAfxH,EAAM/D,UAKtBoQ,EACHrM,EAAMsM,cAAgBtM,EAAMsM,eAAe,IAAOtM,EAAMI,WAIzDgB,EAASwD,MAAM3C,cACfhC,EAAe4F,EAAQwG,OAOvBhP,EAAiB+D,EAASwD,MAAMpB,eAAiB7E,GAAWJ,MAAK,SAACU,UAChEgB,EAAehB,EAAIoN,MAErB,IACI5L,EAAaC,kBAKfU,EAAS/B,MAAMiC,WACfF,EAASwD,MAAMrB,QAAQlH,QAAQ,UAAY,cAK7CoM,GAAW,iBAAkB,CAACrH,EAAUpB,KAGP,IAA/BoB,EAASwD,MAAM7C,cACjBX,EAASiH,qBACTjH,EAASkJ,OAKT/C,GAAgC,EAChCzK,YAAW,WACTyK,GAAgC,KAM7BnG,EAAS/B,MAAM8I,WAClBqC,iBAKG+B,KACP/E,GAAe,WAGRgF,KACPhF,GAAe,WAGRoC,SACD6C,EAAM7B,KACZ6B,EAAI3L,iBAAiB,YAAasL,IAAiB,GACnDK,EAAI3L,iBAAiB,WAAYsL,GAAiBhR,GAClDqR,EAAI3L,iBAAiB,aAAc0L,GAAcpR,GACjDqR,EAAI3L,iBAAiB,YAAayL,GAAanR,YAGxCoP,SACDiC,EAAM7B,KACZ6B,EAAIxL,oBAAoB,YAAamL,IAAiB,GACtDK,EAAIxL,oBAAoB,WAAYmL,GAAiBhR,GACrDqR,EAAIxL,oBAAoB,aAAcuL,GAAcpR,GACpDqR,EAAIxL,oBAAoB,YAAasL,GAAanR,YAmB3C8O,GAAgBrI,EAAkBoI,OACnCrK,EAAMkK,KAA6BlK,aAEhCE,EAASE,GACZA,EAAMI,SAAWR,IACnBD,EAA4BC,EAAK,SAAUE,GAC3CmK,QAMa,IAAbpI,SACKoI,IAGTtK,EAA4BC,EAAK,SAAUuH,GAC3CxH,EAA4BC,EAAK,MAAOE,GAExCqH,EAA+BrH,WAGxB4M,GACPC,EACAC,EACAC,YAAAA,IAAAA,GAA6C,GAE/BxP,EAAiB+D,EAASwD,MAAMpB,eAAiB7E,GACzDxB,SAAQ,SAAC+I,GACbA,EAAKpF,iBAAiB6L,EAAWC,EAASC,GAC1CnF,EAAUjK,KAAK,CAACyI,KAAAA,EAAMyG,UAAAA,EAAWC,QAAAA,EAASC,QAAAA,gBAIrClE,KL9WJ,IAAuBjN,EK+WtB6P,MACFmB,GAAG,aAAc3J,GAAW,CAAC1H,SAAS,IACtCqR,GAAG,WAAYI,GAA+B,CAACzR,SAAS,MLjXhCK,EKoXZ0F,EAASwD,MAAMrB,QLnXxB7H,EAAMkC,MAAM,OAAOkK,OAAOiF,UKmXO5P,SAAQ,SAACwP,MAC3B,WAAdA,SAIJD,GAAGC,EAAW5J,IAEN4J,OACD,aACHD,GAAG,aAAcI,cAEd,QACHJ,GAAGxR,EAAS,WAAa,OAAQ8R,cAE9B,UACHN,GAAG,WAAYM,iBAMdtE,KACPhB,EAAUvK,SAAQ,gBAAE+I,IAAAA,KAAMyG,IAAAA,UAAWC,IAAAA,QAASC,IAAAA,QAC5C3G,EAAKjF,oBAAoB0L,EAAWC,EAASC,MAE/CnF,EAAY,YAGL3E,GAAU/C,SACbiN,GAA0B,KAG3B7L,EAAS/B,MAAM4I,YAChBiF,GAAuBlN,KACvBuH,OAKI4F,EAAwC,oBAA3BjG,YAAkBjL,MAErCiL,EAAmBlH,EACnBqH,EAAgBrH,EAAMqH,cAEtBwB,MAEKzH,EAAS/B,MAAMiC,WAAa9C,EAAawB,IAK5C4G,EAAmBzJ,SAAQ,SAAC2C,UAAaA,EAASE,MAKnC,UAAfA,EAAM/D,OACLmF,EAASwD,MAAMrB,QAAQlH,QAAQ,cAAgB,GAC9CiL,KAC6B,IAA/BlG,EAASwD,MAAM7C,aACfX,EAAS/B,MAAMiC,UAEf2L,GAA0B,EAE1B5B,GAAarL,GAGI,UAAfA,EAAM/D,OACRqL,GAAsB2F,GAGpBA,IAA4BE,GAC9BC,GAAapN,aAIR4H,GAAY5H,OACbI,EAASJ,EAAMI,OACfiN,EACJ7D,KAAmBnJ,SAASD,IAAWyF,EAAOxF,SAASD,GAEtC,cAAfJ,EAAM/D,MAAwBoR,GJ5b/B,SACLC,EACAtN,OAEOuN,EAAoBvN,EAApBuN,QAASC,EAAWxN,EAAXwN,eAETF,EAAeG,OAAM,gBAAEC,IAAAA,WAAYC,IAAAA,YACjCzL,IAD8C0C,MAC9C1C,kBACD0L,EAAgBlQ,EAAiBiQ,EAAYhQ,WAC7CkQ,EAAaF,EAAYG,cAAczL,WAExCwL,SACI,MAGHE,EAAgC,WAAlBH,EAA6BC,EAAWG,IAAKC,EAAI,EAC/DC,EAAmC,QAAlBN,EAA0BC,EAAWM,OAAQF,EAAI,EAClEG,EAAiC,UAAlBR,EAA4BC,EAAWQ,KAAMC,EAAI,EAChEC,EAAkC,SAAlBX,EAA2BC,EAAWW,MAAOF,EAAI,EAEjEG,EACJf,EAAWM,IAAMR,EAAUO,EAAc7L,EACrCwM,EACJlB,EAAUE,EAAWS,OAASD,EAAiBhM,EAC3CyM,EACJjB,EAAWW,KAAOd,EAAUa,EAAelM,EACvC0M,EACJrB,EAAUG,EAAWc,MAAQD,EAAgBrM,SAExCuM,GAAcC,GAAiBC,GAAeC,KIqbjDC,CAlBmB7F,KACpB1L,OAAOuI,GACPsF,KAAI,SAACtF,SAEExG,WADWwG,EAAOnH,OACDsJ,uBAAT8G,EAAyBzP,aAEnCA,EACK,CACLqO,WAAY7H,EAAOkJ,wBACnBpB,YAAatO,EACbuF,MAAAA,GAIG,QAERkD,OAAOiF,SAE2C/M,KACnD4I,KACAwE,GAAapN,aAIR8M,GAAa9M,GAElBkN,GAAuBlN,IACtBoB,EAASwD,MAAMrB,QAAQlH,QAAQ,UAAY,GAAKiL,IAM/ClG,EAASwD,MAAM3C,YACjBb,EAASuJ,sBAAsB3K,GAIjCoN,GAAapN,aAGNgN,GAAiBhN,GAEtBoB,EAASwD,MAAMrB,QAAQlH,QAAQ,WAAa,GAC5C2D,EAAMI,SAAWoJ,MAOjBpI,EAASwD,MAAM3C,aACfjC,EAAMgP,eACNnJ,EAAOxF,SAASL,EAAMgP,gBAKxB5B,GAAapN,YAGNkN,GAAuBlN,WACvBS,EAAaC,SAChB6K,MAA+BvL,EAAM/D,KAAKI,QAAQ,UAAY,WAI3D0M,KACPgC,WAQI3J,EAASwD,MALXzB,IAAAA,cACAxF,IAAAA,UACA0E,IAAAA,OACAP,IAAAA,uBACAM,IAAAA,eAGI2B,EAAQ2F,KAAyB9D,EAAYC,GAAQ9B,MAAQ,KAE7DkL,EAAoBnN,EACtB,CACEiN,sBAAuBjN,EACvBoN,eACEpN,EAAuBoN,gBAAkB1F,MAE7C7K,EA+BEwQ,EAAsC,CAC1C,CACEzK,KAAM,SACNmI,QAAS,CACPxK,OAAAA,IAGJ,CACEqC,KAAM,kBACNmI,QAAS,CACPuC,QAAS,CACPpB,IAAK,EACLG,OAAQ,EACRE,KAAM,EACNG,MAAO,KAIb,CACE9J,KAAM,OACNmI,QAAS,CACPuC,QAAS,IAGb,CACE1K,KAAM,gBACNmI,QAAS,CACPwC,UAAWjN,IAxDmD,CAClEsC,KAAM,UACN4K,SAAS,EACTC,MAAO,cACPC,SAAU,CAAC,iBACX/S,mBAAI4C,IAAAA,SACEqK,KAAwB,KACnB9J,EAAOkK,KAAPlK,KAEN,YAAa,mBAAoB,WAAWzC,SAAQ,SAAC6O,GACvC,cAATA,EACFpM,EAAIN,aAAa,iBAAkBD,EAAM1B,WAErC0B,EAAMoQ,WAAW5J,sBAAsBmG,GACzCpM,EAAIN,qBAAqB0M,EAAQ,IAEjCpM,EAAI4G,wBAAwBwF,MAKlC3M,EAAMoQ,WAAW5J,OAAS,OAyC5B6D,MAA0B3F,GAC5BoL,EAAU1R,KAAK,CACbiH,KAAM,QACNmI,QAAS,CACPpN,QAASsE,EACTqL,QAAS,KAKfD,EAAU1R,WAAV0R,SAAmBhM,SAAAA,EAAegM,YAAa,IAE/C/N,EAAS4G,eAAiB0H,eACxBT,EACApJ,mBAEK1C,GACHxF,UAAAA,EACAyJ,cAAAA,EACA+H,UAAAA,cAKGpE,KACH3J,EAAS4G,iBACX5G,EAAS4G,eAAeiD,UACxB7J,EAAS4G,eAAiB,eA4DrBgB,YACAnL,EACLgI,EAAO/G,iBAAiB,+BAInBuM,GAAarL,GACpBoB,EAASiH,qBAELrI,GACFyI,GAAW,YAAa,CAACrH,EAAUpB,IAGrC4J,SAEIhI,EAAQ6J,IAAS,KACYH,IAA1BqE,OAAYC,OAEfnP,EAAaC,SAA0B,SAAfiP,GAAyBC,IACnDhO,EAAQgO,GAGNhO,EACFmF,EAAcjK,YAAW,WACvBsE,EAASgI,SACRxH,GAEHR,EAASgI,gBAIJgE,GAAapN,MACpBoB,EAASiH,qBAETI,GAAW,cAAe,CAACrH,EAAUpB,IAEhCoB,EAAS/B,MAAMiC,gBAWlBF,EAASwD,MAAMrB,QAAQlH,QAAQ,eAAiB,GAChD+E,EAASwD,MAAMrB,QAAQlH,QAAQ,UAAY,GAC3C,CAAC,aAAc,aAAaA,QAAQ2D,EAAM/D,OAAS,GACnDqL,QAKI1F,EAAQ6J,IAAS,GAEnB7J,EACFoF,EAAclK,YAAW,WACnBsE,EAAS/B,MAAMiC,WACjBF,EAASkJ,SAEV1I,GAIHqF,EAA6BiC,uBAAsB,WACjD9H,EAASkJ,gBA9BXE,MChxBN,SAASqF,EACPC,EACAC,YAAAA,IAAAA,EAAgC,QAE1B7M,EAAU3B,EAAa2B,QAAQ5F,OAAOyS,EAAc7M,SAAW,IJ+CrEjI,SAAS6F,iBAAiB,aAAcF,EAAsBxF,GAC9DJ,OAAO8F,iBAAiB,OAAQI,OItC1BqD,mBAAkCwL,GAAe7M,QAAAA,IAwBjD8M,EAtBWpR,EAAmBkR,GAsBT7R,QACzB,SAACC,EAAKS,OACEyC,EAAWzC,GAAamI,EAAYnI,EAAW4F,UAEjDnD,GACFlD,EAAIT,KAAK2D,GAGJlD,IAET,WAGKI,EAAUwR,GAAWE,EAAU,GAAKA,EAG7CH,EAAMtO,aAAeA,EACrBsO,EAAMI,gBHMmD,SAACzH,GAM3CxK,OAAOf,KAAKuL,GACpBrL,SAAQ,SAACC,GACXmE,EAAqBnE,GAAOoL,EAAapL,OGb9CyS,EAAMpP,aAAeA,EAId,IClDDyP,mBACDC,eACHC,uBAAQ/Q,IAAAA,MACAgR,EAAgB,CACpBxK,OAAQ,CACNyK,SAAUjR,EAAMwN,QAAQ0D,SACxBlC,KAAM,IACNL,IAAK,IACLwC,OAAQ,KAEVzM,MAAO,CACLuM,SAAU,YAEZ3R,UAAW,IAGbX,OAAOyS,OAAOpR,EAAMqR,SAAS7K,OAAO3G,MAAOmR,EAAcxK,QACzDxG,EAAMsR,OAASN,EAEXhR,EAAMqR,SAAS3M,OACjB/F,OAAOyS,OAAOpR,EAAMqR,SAAS3M,MAAM7E,MAAOmR,EAActM,UC/BxD6M,EAAsB,CAC1BC,UAAW,aACXC,QAAS,QACTC,MAAO,SCLT,IAAMtN,EAA2B,CAC/BiB,KAAM,cACN9I,cAAc,EACda,YAAG2E,qBAEIA,EAASwD,MAAMxB,UAAfoI,EAAuB9E,cAQnB,SAGcd,EAAYxE,EAASyE,QAArCjG,IAAAA,IAAK8B,IAAAA,QAEN0E,EAAWhF,EAASwD,MAAMnB,YA4CpC,eACQ2C,EAAWhI,WACjBgI,EAASZ,2BACTpG,EAAmB,CAACgH,GAAW,UACxBA,EA/CD4K,GACA,WAEG,CACLxO,oBACM4D,IACFxG,EAAIqR,aAAa7K,EAAUxG,EAAIkG,mBAC/BlG,EAAIN,aAAa,mBAAoB,IACrCM,EAAIV,MAAMgS,SAAW,SAErB9P,EAASmH,SAAS,CAACxE,OAAO,EAAOD,UAAW,iBAGhDlB,sBACMwD,EAAU,KACLjH,EAAsBS,EAAIV,MAA1BC,mBACD0C,EAAWsP,OAAOhS,EAAmBgN,QAAQ,KAAM,KAKzDzK,EAAQxC,MAAMkS,gBAAqBC,KAAKC,MAAMzP,EAAW,SAEzDuE,EAASlH,MAAMC,mBAAqBA,EACpCC,EAAmB,CAACgH,GAAW,aAGnCvD,kBACMuD,IACFA,EAASlH,MAAMC,mBAAqB,QAGxCwD,kBACMyD,GACFhH,EAAmB,CAACgH,GAAW,kBCxDrCmL,EAAc,CAAChE,QAAS,EAAGC,QAAS,GACpCgE,EAA8D,GAElE,SAASC,SAAkBlE,IAAAA,QAASC,IAAAA,QAClC+D,EAAc,CAAChE,QAAAA,EAASC,QAAAA,GAW1B,IAAM9J,EAA6B,CACjCgB,KAAM,eACN9I,cAAc,EACda,YAAG2E,OACKzC,EAAYyC,EAASzC,UACrB8N,EAAMlN,EAAiB6B,EAASwD,MAAMpB,eAAiB7E,GAEzD+S,GAAmB,EACnBC,GAAgB,EAChBC,GAAc,EACdtL,EAAYlF,EAASwD,eAEhBiN,UAE2B,YAAhCzQ,EAASwD,MAAMlB,cAA8BtC,EAAS/B,MAAMiC,mBAIvDwQ,IACPrF,EAAI3L,iBAAiB,YAAa8G,YAG3BmK,IACPtF,EAAIxL,oBAAoB,YAAa2G,YAG9BoK,IACPN,GAAmB,EACnBtQ,EAASmH,SAAS,CAACzG,uBAAwB,OAC3C4P,GAAmB,WAGZ9J,EAAY5H,OAGbiS,GAAwBjS,EAAMI,QAChCzB,EAAU0B,SAASL,EAAMI,QAEtBsD,EAAgBtC,EAASwD,MAAzBlB,aACA6J,EAAoBvN,EAApBuN,QAASC,EAAWxN,EAAXwN,QAEV0E,EAAOvT,EAAUoQ,wBACjBoD,EAAY5E,EAAU2E,EAAK7D,KAC3B+D,EAAY5E,EAAU0E,EAAKlE,KAE7BiE,GAA0B7Q,EAASwD,MAAM3C,aAC3Cb,EAASmH,SAAS,CAEhBzG,sCACQoQ,EAAOvT,EAAUoQ,wBAEnBT,EAAIf,EACJU,EAAIT,EAEa,YAAjB9J,IACF4K,EAAI4D,EAAK7D,KAAO8D,EAChBlE,EAAIiE,EAAKlE,IAAMoE,OAGXpE,EAAuB,eAAjBtK,EAAgCwO,EAAKlE,IAAMC,EACjDO,EAAyB,aAAjB9K,EAA8BwO,EAAK1D,MAAQF,EACnDH,EAA0B,eAAjBzK,EAAgCwO,EAAK/D,OAASF,EACvDI,EAAwB,aAAjB3K,EAA8BwO,EAAK7D,KAAOC,QAEhD,CACL+D,MAAO7D,EAAQH,EACfiE,OAAQnE,EAASH,EACjBA,IAAAA,EACAQ,MAAAA,EACAL,OAAAA,EACAE,KAAAA,eAODkE,IACHnR,EAASwD,MAAMlB,eACjB8N,EAAgB/T,KAAK,CAAC2D,SAAAA,EAAUqL,IAAAA,IAvFxC,SAAgCA,GAC9BA,EAAI3L,iBAAiB,YAAa2Q,GAuF5Be,CAAuB/F,aAIlBxB,IAK2D,KAJlEuG,EAAkBA,EAAgB1J,QAChC,SAAC2K,UAASA,EAAKrR,WAAaA,MAGV0G,QAAO,SAAC2K,UAASA,EAAKhG,MAAQA,KAAKiG,QA7F7D,SAAmCjG,GACjCA,EAAIxL,oBAAoB,YAAawQ,GA6F/BkB,CAA0BlG,SAIvB,CACLjK,SAAU+P,EACV9P,UAAWwI,EACX1I,0BACE+D,EAAYlF,EAASwD,OAEvBtC,uBAAcsQ,SAAIlP,IAAAA,aACZgO,QAKevT,IAAjBuF,GACA4C,EAAU5C,eAAiBA,IAE3BuH,IAEIvH,GACF6O,KAGEnR,EAAS/B,MAAM8I,WACdwJ,GACAE,KAEDC,MAGFC,IACAC,OAINpP,mBACMxB,EAASwD,MAAMlB,eAAiBiO,IAC9BC,IACFhK,EAAY2J,GACZK,GAAc,GAGXC,KACHC,MAIN/O,mBAAU6P,EAAG5S,GACPxB,EAAawB,KACfuR,EAAc,CAAChE,QAASvN,EAAMuN,QAASC,QAASxN,EAAMwN,UAExDmE,EAA+B,UAAf3R,EAAM/D,MAExByG,oBACMtB,EAASwD,MAAMlB,eACjBsO,IACAD,IACAH,GAAc,OCpJxB,IAAMjO,EAAuC,CAC3Ce,KAAM,oBACN9I,cAAc,EACda,YAAG2E,OAOGzD,EANGgB,EAAayC,EAAbzC,cAOHkU,GAAmB,EACnBnB,GAAmB,EACnBoB,EAAiC,GAE/BC,EAGF,CACFrO,KAAM,yBACN4K,SAAS,EACTC,MAAO,aACP9S,mBAAI4C,IAAAA,MAfK+B,EAASwD,MAAMjB,qBAiB8B,IAA9CmP,EAAgBzW,QAAQgD,EAAM1B,aAChCmV,EAAkB,IAIlBnV,IAAc0B,EAAM1B,YAC0B,IAA9CmV,EAAgBzW,QAAQgD,EAAM1B,aAE9BmV,EAAgBrV,KAAK4B,EAAM1B,WAC3ByD,EAASmH,SAAS,CAEhBzG,uBAAwB,2BAUFnE,UA+C7B,SACLqV,EACAC,EACAC,EACAL,MAUIK,EAAYR,OAAS,GAA8B,OAAzBM,SACrBC,KAKgB,IAAvBC,EAAYR,QACZG,GAAmB,GACnBK,EAAY,GAAG7E,KAAO6E,EAAY,GAAG1E,aAE9B0E,EAAYL,IAAoBI,SAGjCD,OACD,UACA,aACGG,EAAYD,EAAY,GACxBE,EAAWF,EAAYA,EAAYR,OAAS,GAC5CW,EAAiC,QAAzBL,EAERhF,EAAMmF,EAAUnF,IAChBG,EAASiF,EAASjF,OAClBE,EAAOgF,EAAQF,EAAU9E,KAAO+E,EAAS/E,KACzCG,EAAQ6E,EAAQF,EAAU3E,MAAQ4E,EAAS5E,YAI1C,CAACR,IAAAA,EAAKG,OAAAA,EAAQE,KAAAA,EAAMG,MAAAA,EAAO6D,MAHpB7D,EAAQH,EAGmBiE,OAF1BnE,EAASH,OAIrB,WACA,YACGsF,EAAUjC,KAAKkC,UAALlC,KAAY6B,EAAY/H,KAAI,SAACqI,UAAUA,EAAMnF,SACvDoF,EAAWpC,KAAKqC,UAALrC,KAAY6B,EAAY/H,KAAI,SAACqI,UAAUA,EAAMhF,UACxDmF,EAAeT,EAAYpL,QAAO,SAACoK,SACd,SAAzBc,EACId,EAAK7D,OAASiF,EACdpB,EAAK1D,QAAUiF,KAGfzF,EAAM2F,EAAa,GAAG3F,IACtBG,EAASwF,EAAaA,EAAajB,OAAS,GAAGvE,aAM9C,CAACH,IAAAA,EAAKG,OAAAA,EAAQE,KALRiF,EAKc9E,MAJbiF,EAIoBpB,MAJpBoB,EADDH,EAK4BhB,OAF1BnE,EAASH,kBAKjBiF,GA7GAW,CACLlW,EAAiBC,GACjBgB,EAAUoQ,wBACVlR,EAAUc,EAAUkV,kBACpBhB,GAdQ/Q,CAAuBzC,EAAM1B,eAInCA,EAAY0B,EAAM1B,sBAoBfmW,QANiBtL,EAOnBkJ,IAPmBlJ,EApE9B,SAAkB5D,EAAcmO,eACvB,CACL5P,+BACKyB,EAAMzB,eACTgM,+BACMvK,EAAMzB,wBAAegM,YAAa,IAAIrH,QACxC,qBAAEpD,OAAmBqO,EAASrO,SAEhCqO,OAoEiBgB,CAAS3S,EAASwD,MAAOmO,GAP5CrB,GAAmB,EACnBtQ,EAASmH,SAASC,GAClBkJ,GAAmB,SASd,CACLlP,SAAUsR,EACVxR,cAAewR,EACf/Q,mBAAU6P,EAAG5S,MACPxB,EAAawB,GAAQ,KACjBwT,EAAQ3V,EAAUuD,EAASzC,UAAUkV,kBACrCG,EAAaR,EAAMvN,MACvB,SAACiM,UACCA,EAAK7D,KAAO,GAAKrO,EAAMuN,SACvB2E,EAAK1D,MAAQ,GAAKxO,EAAMuN,SACxB2E,EAAKlE,IAAM,GAAKhO,EAAMwN,SACtB0E,EAAK/D,OAAS,GAAKnO,EAAMwN,WAEvB7R,EAAQ6X,EAAMnX,QAAQ2X,GAC5BnB,EAAkBlX,GAAS,EAAIA,EAAQkX,IAG3CnQ,oBACEmQ,GAAmB,MCpG3B,IAAMjP,EAAiB,CACrBc,KAAM,SACN9I,cAAc,EACda,YAAG2E,OACMzC,EAAqByC,EAArBzC,UAAWkH,EAAUzE,EAAVyE,gBAQToO,EAAYvY,UACc,IAA1B0F,EAASwD,MAAMhB,QAAmBxC,EAASwD,MAAMhB,SAAWlI,MAGjEwY,EAAiC,KACjCC,EAAiC,cAE5BC,QACDC,EAAiBJ,EAAY,cAb5B7S,EAAS4G,eACZ5G,EAAS4G,eAAe3I,MAAMqR,SAAS/R,UACvCA,GAYeoQ,wBACf,KACEuF,EAAiBL,EAAY,UAC/BpO,EAAOkJ,wBACP,MAGDsF,GAAkBE,EAAkBL,EAAaG,IACjDC,GAAkBC,EAAkBJ,EAAaG,KAE9ClT,EAAS4G,gBACX5G,EAAS4G,eAAewM,SAI5BN,EAAcG,EACdF,EAAcG,EAEVlT,EAAS/B,MAAM8I,WACjBe,sBAAsBkL,SAInB,CACLxR,mBACMxB,EAASwD,MAAMhB,QACjBwQ,QASV,SAASG,EACPE,EACAC,UAEID,IAASC,IAETD,EAAMzG,MAAQ0G,EAAM1G,KACpByG,EAAMjG,QAAUkG,EAAMlG,OACtBiG,EAAMtG,SAAWuG,EAAMvG,QACvBsG,EAAMpG,OAASqG,EAAMrG,aCvDvBtT,GCbG,SAAmB4Z,OAClBzV,EAAQjE,SAASoD,cAAc,SACrCa,EAAMyG,YAAcgP,EACpBzV,EAAMI,aAAa,wBAAwC,QACrDsV,EAAO3Z,SAAS2Z,KAChBC,EAAsB5Z,SAAS6Z,cAAc,wBAE/CD,EACFD,EAAK3D,aAAa/R,EAAO2V,GAEzBD,EAAKnP,YAAYvG,GDInB6V,s4CAGFlF,EAAMI,gBAAgB,CACpB/M,QAAS,CAACO,EAAaC,EAAcC,EAAmBC,GACxDR,OAAAA,IAGFyM,EAAMmF,gBNyBmC,SACvCC,EACAlF,kBAAAA,IAAAA,EAAgB,QAiBZ1I,EAHA6N,EAAsBD,EACtBE,EAAsC,GACtCC,EAAiC,GAEjCC,EAAYtF,EAAcsF,UAC1BC,EAA+C,GAC/CC,GAAgB,WAEXC,IACPJ,EAAiBF,EACd/J,KAAI,SAAC/J,UACJ/D,EAAiB+D,EAASwD,MAAMpB,eAAiBpC,EAASzC,cAE3DV,QAAO,SAACC,EAAK6J,UAAS7J,EAAIZ,OAAOyK,KAAO,aAGpC0N,IACPN,EAAaD,EAAoB/J,KAAI,SAAC/J,UAAaA,EAASzC,sBAGrD+W,EAAgBzN,GACvBiN,EAAoB/X,SAAQ,SAACiE,GACvB6G,EACF7G,EAASyJ,SAETzJ,EAAS0J,sBAKN6K,EAAkBC,UAClBV,EAAoB/J,KAAI,SAAC/J,OACxByU,EAAmBzU,EAASmH,gBAElCnH,EAASmH,SAAW,SAAC3D,GACnBiR,EAAiBjR,GAEbxD,EAASzC,YAAc0I,GACzBuO,EAAUrN,SAAS3D,IAIhB,WACLxD,EAASmH,SAAWsN,eAMjBC,EACPF,EACAxV,OAEMzE,EAAQyZ,EAAe/Y,QAAQ+D,MAGjCA,IAAWiH,GAIfA,EAAgBjH,MAEV2V,GAAiCV,GAAa,IACjD/X,OAAO,WACPW,QAAO,SAACC,EAAK8X,UACX9X,EAAY8X,GAAQd,EAAoBvZ,GAAOiJ,MAAMoR,GAC/C9X,IACN,IAEL0X,EAAUrN,0BACLwN,GACHjU,uBACkD,mBAAzCiU,EAAcjU,uBACjBiU,EAAcjU,uBACd,iCAAkBqT,EAAWxZ,WAAXsa,EAAmBlH,6BAI/C2G,GAAgB,GAChBD,IACAD,QAEM/Q,EAAiB,CACrBhI,oBACS,CACLgG,qBACEiT,GAAgB,IAElBhT,oBACE2E,EAAgB,MAElBpE,wBAAe7B,GACTA,EAASwD,MAAMvB,eAAiBkS,IAClCA,GAAgB,EAChBlO,EAAgB,OAGpBxE,gBAAOzB,GACDA,EAASwD,MAAMvB,eAAiBkS,IAClCA,GAAgB,EAChBO,EAAgB1U,EAAU+T,EAAW,MAGzCpS,mBAAU3B,EAAUpB,GAClB8V,EAAgB1U,EAAUpB,EAAMqH,mBAMlCuO,EAAY/F,EAAMzR,qBACnBrB,EAAiBgT,EAAe,CAAC,eACpC7M,SAAUuB,UAAYsL,EAAc7M,SAAW,IAC/CM,cAAe4R,EACfjS,+BACK4M,EAAc5M,eACjBgM,8BACMY,EAAc5M,wBAAegM,YAAa,IAC9Ce,SAKAgG,EAAeN,EAAUxM,KAE/BwM,EAAUxM,KAAO,SAAChJ,MAChB8V,KAIK7O,GAA2B,MAAVjH,SACb0V,EAAgBF,EAAWT,EAAW,QAK3C9N,GAA2B,MAAVjH,MAKC,iBAAXA,SAEP+U,EAAW/U,IAAW0V,EAAgBF,EAAWT,EAAW/U,OAK5D8U,EAAoB7Y,QAAQ+D,IAAuB,EAAG,KAClD+V,EAAO/V,EAAoBzB,iBAC1BmX,EAAgBF,EAAWO,UAIhChB,EAAW9Y,QAAQ+D,IAA+B,EAC7C0V,EAAgBF,EAAWxV,YAItCwV,EAAUQ,SAAW,eACbC,EAAQlB,EAAW,OACpB9N,SACIuO,EAAUxM,KAAK,OAElBzN,EAAQwZ,EAAW9Y,QAAQgL,GACjCuO,EAAUxM,KAAK+L,EAAWxZ,EAAQ,IAAM0a,IAG1CT,EAAUU,aAAe,eACjBC,EAAOpB,EAAWA,EAAWzC,OAAS,OACvCrL,SACIuO,EAAUxM,KAAKmN,OAElB5a,EAAQwZ,EAAW9Y,QAAQgL,GAC3BjH,EAAS+U,EAAWxZ,EAAQ,IAAM4a,EACxCX,EAAUxM,KAAKhJ,QAGXyV,EAAmBD,EAAUrN,gBAEnCqN,EAAUrN,SAAW,SAAC3D,GACpByQ,EAAYzQ,EAAMyQ,WAAaA,EAC/BQ,EAAiBjR,IAGnBgR,EAAUY,aAAe,SAACC,GACxBf,GAAgB,GAChBJ,EAA0BnY,SAAQ,SAACV,UAAOA,OAE1CyY,EAAsBuB,EAEtBf,GAAgB,GAChBD,IACAD,IACAF,EAA4BK,EAAkBC,GAE9CA,EAAUrN,SAAS,CAAC/E,cAAe4R,KAGrCE,EAA4BK,EAAkBC,GAEvCA,GMjPT/F,EAAM6G,SLLN,SACE5G,EACAlL,OAaI8C,EAA8B,GAC9BiP,EAAkC,GAClCC,GAAW,EAERxW,EAAUwE,EAAVxE,OAEDyW,EAAc9Z,EAAiB6H,EAAO,CAAC,WACvCkS,mBAAkBD,GAAatT,QAAS,SAAUD,OAAO,IACzDyT,iBACJzT,MAAO/B,EAAa+B,OACjBuT,GACHxT,cAAc,IAGV2T,EAAcnH,EAAMC,EAASgH,YAG1B/T,EAAU/C,MACZA,EAAMI,SAAUwW,OAIfK,EAAcjX,EAAMI,OAAmB8W,QAAQ9W,MAEhD6W,OAQC1T,EACJ0T,EAAWlS,aAAa,uBACxBH,EAAMrB,SACNhC,EAAagC,YAGX0T,EAAWvY,UAII,eAAfsB,EAAM/D,MAAqD,kBAArB8a,EAAWzT,OAKpC,eAAftD,EAAM/D,MACNsH,EAAQlH,QAASuU,EAA4B5Q,EAAM/D,OAAS,QAKxDmF,EAAWyO,EAAMoH,EAAYF,GAE/B3V,IACFuV,EAAsBA,EAAoBrZ,OAAO8D,gBAI5CsL,EACPxG,EACAyG,EACAC,EACAC,YAAAA,IAAAA,GAA6C,GAE7C3G,EAAKpF,iBAAiB6L,EAAWC,EAASC,GAC1CnF,EAAUjK,KAAK,CAACyI,KAAAA,EAAMyG,UAAAA,EAAWC,QAAAA,EAASC,QAAAA,WApDdxP,EAAiB2Z,GAwGzB7Z,kBAjCEiE,OAChB+V,EAAkB/V,EAAS6J,QAC3BmM,EAAiBhW,EAASyJ,OAC1BwM,EAAkBjW,EAAS0J,QAEjC1J,EAAS6J,QAAU,SAACqM,YAAAA,IAAAA,GAA8B,GAC5CA,GACFX,EAAoBxZ,SAAQ,SAACiE,GAC3BA,EAAS6J,aAIb0L,EAAsB,GAlBxBjP,EAAUvK,SAAQ,gBAAE+I,IAAAA,KAAMyG,IAAAA,UAAWC,IAAAA,QAASC,IAAAA,QAC5C3G,EAAKjF,oBAAoB0L,EAAWC,EAASC,MAE/CnF,EAAY,GAkBVyP,KAGF/V,EAASyJ,OAAS,WAChBuM,IACAT,EAAoBxZ,SAAQ,SAACiE,UAAaA,EAASyJ,YACnD+L,GAAW,GAGbxV,EAAS0J,QAAU,WACjBuM,IACAV,EAAoBxZ,SAAQ,SAACiE,UAAaA,EAAS0J,aACnD8L,GAAW,YA3CYxV,OAClBzC,EAAayC,EAAbzC,UAEP+N,EAAG/N,EAAW,aAAcoE,EAAW3H,GACvCsR,EAAG/N,EAAW,YAAaoE,GAC3B2J,EAAG/N,EAAW,UAAWoE,GACzB2J,EAAG/N,EAAW,QAASoE,GAwCvBwU,CAAkBnW,MAKb4V,GKlITnH,EAAM2H,QP6C0B,6BAGZ,KAFTC,IAATC,QACA7V,IAAAA,SAEAgF,EAAiB1J,SAAQ,SAACiE,OACpBuW,GAAa,KAEbF,IACFE,EAAalZ,EAAmBgZ,GAC5BrW,EAASzC,YAAc8Y,EACvBrW,EAASyE,SAAY4R,EAAyC5R,SAG/D8R,EAAY,KACTC,EAAmBxW,EAASwD,MAAM/C,SAExCT,EAASmH,SAAS,CAAC1G,SAAAA,IACnBT,EAASkJ,OAEJlJ,EAAS/B,MAAM6I,aAClB9G,EAASmH,SAAS,CAAC1G,SAAU+V,SOhErC/H,EAAMgI,WdxBJ"}
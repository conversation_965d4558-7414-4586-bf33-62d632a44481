{"version": 3, "file": "tippy-headless.umd.js", "sources": ["../../src/constants.ts", "../../src/utils.ts", "../../src/dom-utils.ts", "../../src/bindGlobalEventListeners.ts", "../../src/browser.ts", "../../src/validation.ts", "../../src/props.ts", "../../src/template.ts", "../../src/createTippy.ts", "../../src/index.ts", "../../src/addons/createSingleton.ts", "../../src/addons/delegate.ts", "../../src/plugins/animateFill.ts", "../../src/plugins/followCursor.ts", "../../src/plugins/inlinePositioning.ts", "../../src/plugins/sticky.ts", "../../build/headless-umd.js"], "sourcesContent": ["export const ROUND_ARROW =\n  '<svg width=\"16\" height=\"6\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M0 6s1.796-.013 4.67-3.615C5.851.9 6.93.006 8 0c1.07-.006 2.148.887 3.343 2.385C14.233 6.005 16 6 16 6H0z\"></svg>';\n\nexport const BOX_CLASS = `__NAMESPACE_PREFIX__-box`;\nexport const CONTENT_CLASS = `__NAMESPACE_PREFIX__-content`;\nexport const BACKDROP_CLASS = `__NAMESPACE_PREFIX__-backdrop`;\nexport const ARROW_CLASS = `__NAMESPACE_PREFIX__-arrow`;\nexport const SVG_ARROW_CLASS = `__NAMESPACE_PREFIX__-svg-arrow`;\n\nexport const TOUCH_OPTIONS = {passive: true, capture: true};\n\nexport const TIPPY_DEFAULT_APPEND_TO = () => document.body;\n", "import {BasePlacement, Placement} from './types';\n\nexport function hasOwnProperty(\n  obj: Record<string, unknown>,\n  key: string\n): boolean {\n  return {}.hasOwnProperty.call(obj, key);\n}\n\nexport function getValueAtIndexOrReturn<T>(\n  value: T | [T | null, T | null],\n  index: number,\n  defaultValue: T | [T, T]\n): T {\n  if (Array.isArray(value)) {\n    const v = value[index];\n    return v == null\n      ? Array.isArray(defaultValue)\n        ? defaultValue[index]\n        : defaultValue\n      : v;\n  }\n\n  return value;\n}\n\nexport function isType(value: any, type: string): boolean {\n  const str = {}.toString.call(value);\n  return str.indexOf('[object') === 0 && str.indexOf(`${type}]`) > -1;\n}\n\nexport function invokeWithArgsOrReturn(value: any, args: any[]): any {\n  return typeof value === 'function' ? value(...args) : value;\n}\n\nexport function debounce<T>(\n  fn: (arg: T) => void,\n  ms: number\n): (arg: T) => void {\n  // Avoid wrapping in `setTimeout` if ms is 0 anyway\n  if (ms === 0) {\n    return fn;\n  }\n\n  let timeout: any;\n\n  return (arg): void => {\n    clearTimeout(timeout);\n    timeout = setTimeout(() => {\n      fn(arg);\n    }, ms);\n  };\n}\n\nexport function removeProperties<T>(obj: T, keys: string[]): Partial<T> {\n  const clone = {...obj};\n  keys.forEach((key) => {\n    delete (clone as any)[key];\n  });\n  return clone;\n}\n\nexport function splitBySpaces(value: string): string[] {\n  return value.split(/\\s+/).filter(Boolean);\n}\n\nexport function normalizeToArray<T>(value: T | T[]): T[] {\n  return ([] as T[]).concat(value);\n}\n\nexport function pushIfUnique<T>(arr: T[], value: T): void {\n  if (arr.indexOf(value) === -1) {\n    arr.push(value);\n  }\n}\n\nexport function appendPxIfNumber(value: string | number): string {\n  return typeof value === 'number' ? `${value}px` : value;\n}\n\nexport function unique<T>(arr: T[]): T[] {\n  return arr.filter((item, index) => arr.indexOf(item) === index);\n}\n\nexport function getNumber(value: string | number): number {\n  return typeof value === 'number' ? value : parseFloat(value);\n}\n\nexport function getBasePlacement(placement: Placement): BasePlacement {\n  return placement.split('-')[0] as BasePlacement;\n}\n\nexport function arrayFrom(value: ArrayLike<any>): any[] {\n  return [].slice.call(value);\n}\n\nexport function removeUndefinedProps(\n  obj: Record<string, unknown>\n): Partial<Record<string, unknown>> {\n  return Object.keys(obj).reduce((acc, key) => {\n    if (obj[key] !== undefined) {\n      (acc as any)[key] = obj[key];\n    }\n\n    return acc;\n  }, {});\n}\n", "import {ReferenceElement, Targets} from './types';\nimport {PopperTreeData} from './types-internal';\nimport {arrayFrom, isType, normalizeToArray, getBasePlacement} from './utils';\n\nexport function div(): HTMLDivElement {\n  return document.createElement('div');\n}\n\nexport function isElement(value: unknown): value is Element | DocumentFragment {\n  return ['Element', 'Fragment'].some((type) => isType(value, type));\n}\n\nexport function isNodeList(value: unknown): value is NodeList {\n  return isType(value, 'NodeList');\n}\n\nexport function isMouseEvent(value: unknown): value is MouseEvent {\n  return isType(value, 'MouseEvent');\n}\n\nexport function isReferenceElement(value: any): value is ReferenceElement {\n  return !!(value && value._tippy && value._tippy.reference === value);\n}\n\nexport function getArrayOfElements(value: Targets): Element[] {\n  if (isElement(value)) {\n    return [value];\n  }\n\n  if (isNodeList(value)) {\n    return arrayFrom(value);\n  }\n\n  if (Array.isArray(value)) {\n    return value;\n  }\n\n  return arrayFrom(document.querySelectorAll(value));\n}\n\nexport function setTransitionDuration(\n  els: (HTMLDivElement | null)[],\n  value: number\n): void {\n  els.forEach((el) => {\n    if (el) {\n      el.style.transitionDuration = `${value}ms`;\n    }\n  });\n}\n\nexport function setVisibilityState(\n  els: (HTMLDivElement | null)[],\n  state: 'visible' | 'hidden'\n): void {\n  els.forEach((el) => {\n    if (el) {\n      el.setAttribute('data-state', state);\n    }\n  });\n}\n\nexport function getOwnerDocument(\n  elementOrElements: Element | Element[]\n): Document {\n  const [element] = normalizeToArray(elementOrElements);\n\n  // Elements created via a <template> have an ownerDocument with no reference to the body\n  return element?.ownerDocument?.body ? element.ownerDocument : document;\n}\n\nexport function isCursorOutsideInteractiveBorder(\n  popperTreeData: PopperTreeData[],\n  event: MouseEvent\n): boolean {\n  const {clientX, clientY} = event;\n\n  return popperTreeData.every(({popperRect, popperState, props}) => {\n    const {interactiveBorder} = props;\n    const basePlacement = getBasePlacement(popperState.placement);\n    const offsetData = popperState.modifiersData.offset;\n\n    if (!offsetData) {\n      return true;\n    }\n\n    const topDistance = basePlacement === 'bottom' ? offsetData.top!.y : 0;\n    const bottomDistance = basePlacement === 'top' ? offsetData.bottom!.y : 0;\n    const leftDistance = basePlacement === 'right' ? offsetData.left!.x : 0;\n    const rightDistance = basePlacement === 'left' ? offsetData.right!.x : 0;\n\n    const exceedsTop =\n      popperRect.top - clientY + topDistance > interactiveBorder;\n    const exceedsBottom =\n      clientY - popperRect.bottom - bottomDistance > interactiveBorder;\n    const exceedsLeft =\n      popperRect.left - clientX + leftDistance > interactiveBorder;\n    const exceedsRight =\n      clientX - popperRect.right - rightDistance > interactiveBorder;\n\n    return exceedsTop || exceedsBottom || exceedsLeft || exceedsRight;\n  });\n}\n\nexport function updateTransitionEndListener(\n  box: HTMLDivElement,\n  action: 'add' | 'remove',\n  listener: (event: TransitionEvent) => void\n): void {\n  const method = `${action}EventListener` as\n    | 'addEventListener'\n    | 'removeEventListener';\n\n  // some browsers apparently support `transition` (unprefixed) but only fire\n  // `webkitTransitionEnd`...\n  ['transitionend', 'webkitTransitionEnd'].forEach((event) => {\n    box[method](event, listener as EventListener);\n  });\n}\n\n/**\n * Compared to xxx.contains, this function works for dom structures with shadow\n * dom\n */\nexport function actualContains(parent: Element, child: Element): boolean {\n  let target = child;\n  while (target) {\n    if (parent.contains(target)) {\n      return true;\n    }\n    target = (target.getRootNode?.() as any)?.host;\n  }\n  return false;\n}\n", "import {TOUCH_OPTIONS} from './constants';\nimport {isReferenceElement} from './dom-utils';\n\nexport const currentInput = {isTouch: false};\nlet lastMouseMoveTime = 0;\n\n/**\n * When a `touchstart` event is fired, it's assumed the user is using touch\n * input. We'll bind a `mousemove` event listener to listen for mouse input in\n * the future. This way, the `isTouch` property is fully dynamic and will handle\n * hybrid devices that use a mix of touch + mouse input.\n */\nexport function onDocumentTouchStart(): void {\n  if (currentInput.isTouch) {\n    return;\n  }\n\n  currentInput.isTouch = true;\n\n  if (window.performance) {\n    document.addEventListener('mousemove', onDocumentMouseMove);\n  }\n}\n\n/**\n * When two `mousemove` event are fired consecutively within 20ms, it's assumed\n * the user is using mouse input again. `mousemove` can fire on touch devices as\n * well, but very rarely that quickly.\n */\nexport function onDocumentMouseMove(): void {\n  const now = performance.now();\n\n  if (now - lastMouseMoveTime < 20) {\n    currentInput.isTouch = false;\n\n    document.removeEventListener('mousemove', onDocumentMouseMove);\n  }\n\n  lastMouseMoveTime = now;\n}\n\n/**\n * When an element is in focus and has a tippy, leaving the tab/window and\n * returning causes it to show again. For mouse users this is unexpected, but\n * for keyboard use it makes sense.\n * TODO: find a better technique to solve this problem\n */\nexport function onWindowBlur(): void {\n  const activeElement = document.activeElement as HTMLElement | null;\n\n  if (isReferenceElement(activeElement)) {\n    const instance = activeElement._tippy!;\n\n    if (activeElement.blur && !instance.state.isVisible) {\n      activeElement.blur();\n    }\n  }\n}\n\nexport default function bindGlobalEventListeners(): void {\n  document.addEventListener('touchstart', onDocumentTouchStart, TOUCH_OPTIONS);\n  window.addEventListener('blur', onWindowBlur);\n}\n", "export const isBrowser =\n  typeof window !== 'undefined' && typeof document !== 'undefined';\n\nexport const isIE11 = isBrowser\n  ? // @ts-ignore\n    !!window.msCrypto\n  : false;\n", "import {Targets} from './types';\n\nexport function createMemoryLeakWarning(method: string): string {\n  const txt = method === 'destroy' ? 'n already-' : ' ';\n\n  return [\n    `${method}() was called on a${txt}destroyed instance. This is a no-op but`,\n    'indicates a potential memory leak.',\n  ].join(' ');\n}\n\nexport function clean(value: string): string {\n  const spacesAndTabs = /[ \\t]{2,}/g;\n  const lineStartWithSpaces = /^[ \\t]*/gm;\n\n  return value\n    .replace(spacesAndTabs, ' ')\n    .replace(lineStartWithSpaces, '')\n    .trim();\n}\n\nfunction getDevMessage(message: string): string {\n  return clean(`\n  %ctippy.js\n\n  %c${clean(message)}\n\n  %c👷‍ This is a development-only message. It will be removed in production.\n  `);\n}\n\nexport function getFormattedMessage(message: string): string[] {\n  return [\n    getDevMessage(message),\n    // title\n    'color: #00C584; font-size: 1.3em; font-weight: bold;',\n    // message\n    'line-height: 1.5',\n    // footer\n    'color: #a6a095;',\n  ];\n}\n\n// Assume warnings and errors never have the same message\nlet visitedMessages: Set<string>;\nif (__DEV__) {\n  resetVisitedMessages();\n}\n\nexport function resetVisitedMessages(): void {\n  visitedMessages = new Set();\n}\n\nexport function warnWhen(condition: boolean, message: string): void {\n  if (condition && !visitedMessages.has(message)) {\n    visitedMessages.add(message);\n    console.warn(...getFormattedMessage(message));\n  }\n}\n\nexport function errorWhen(condition: boolean, message: string): void {\n  if (condition && !visitedMessages.has(message)) {\n    visitedMessages.add(message);\n    console.error(...getFormattedMessage(message));\n  }\n}\n\nexport function validateTargets(targets: Targets): void {\n  const didPassFalsyValue = !targets;\n  const didPassPlainObject =\n    Object.prototype.toString.call(targets) === '[object Object]' &&\n    !(targets as any).addEventListener;\n\n  errorWhen(\n    didPassFalsyValue,\n    [\n      'tippy() was passed',\n      '`' + String(targets) + '`',\n      'as its targets (first) argument. Valid types are: String, Element,',\n      'Element[], or NodeList.',\n    ].join(' ')\n  );\n\n  errorWhen(\n    didPassPlainObject,\n    [\n      'tippy() was passed a plain object which is not supported as an argument',\n      'for virtual positioning. Use props.getReferenceClientRect instead.',\n    ].join(' ')\n  );\n}\n", "import {DefaultProps, Plugin, Props, ReferenceElement, Tippy} from './types';\nimport {\n  hasOwnProperty,\n  removeProperties,\n  invokeWithArgsOrReturn,\n} from './utils';\nimport {warnWhen} from './validation';\nimport {TIPPY_DEFAULT_APPEND_TO} from './constants';\n\nconst pluginProps = {\n  animateFill: false,\n  followCursor: false,\n  inlinePositioning: false,\n  sticky: false,\n};\n\nconst renderProps = {\n  allowHTML: false,\n  animation: 'fade',\n  arrow: true,\n  content: '',\n  inertia: false,\n  maxWidth: 350,\n  role: 'tooltip',\n  theme: '',\n  zIndex: 9999,\n};\n\nexport const defaultProps: DefaultProps = {\n  appendTo: TIPPY_DEFAULT_APPEND_TO,\n  aria: {\n    content: 'auto',\n    expanded: 'auto',\n  },\n  delay: 0,\n  duration: [300, 250],\n  getReferenceClientRect: null,\n  hideOnClick: true,\n  ignoreAttributes: false,\n  interactive: false,\n  interactiveBorder: 2,\n  interactiveDebounce: 0,\n  moveTransition: '',\n  offset: [0, 10],\n  onAfterUpdate() {},\n  onBeforeUpdate() {},\n  onCreate() {},\n  onDestroy() {},\n  onHidden() {},\n  onHide() {},\n  onMount() {},\n  onShow() {},\n  onShown() {},\n  onTrigger() {},\n  onUntrigger() {},\n  onClickOutside() {},\n  placement: 'top',\n  plugins: [],\n  popperOptions: {},\n  render: null,\n  showOnCreate: false,\n  touch: true,\n  trigger: 'mouseenter focus',\n  triggerTarget: null,\n  ...pluginProps,\n  ...renderProps,\n};\n\nconst defaultKeys = Object.keys(defaultProps);\n\nexport const setDefaultProps: Tippy['setDefaultProps'] = (partialProps) => {\n  /* istanbul ignore else */\n  if (__DEV__) {\n    validateProps(partialProps, []);\n  }\n\n  const keys = Object.keys(partialProps) as Array<keyof DefaultProps>;\n  keys.forEach((key) => {\n    (defaultProps as any)[key] = partialProps[key];\n  });\n};\n\nexport function getExtendedPassedProps(\n  passedProps: Partial<Props> & Record<string, unknown>\n): Partial<Props> {\n  const plugins = passedProps.plugins || [];\n  const pluginProps = plugins.reduce<Record<string, unknown>>((acc, plugin) => {\n    const {name, defaultValue} = plugin;\n\n    if (name) {\n      acc[name] =\n        passedProps[name] !== undefined\n          ? passedProps[name]\n          : (defaultProps as any)[name] ?? defaultValue;\n    }\n\n    return acc;\n  }, {});\n\n  return {\n    ...passedProps,\n    ...pluginProps,\n  };\n}\n\nexport function getDataAttributeProps(\n  reference: ReferenceElement,\n  plugins: Plugin[]\n): Record<string, unknown> {\n  const propKeys = plugins\n    ? Object.keys(getExtendedPassedProps({...defaultProps, plugins}))\n    : defaultKeys;\n\n  const props = propKeys.reduce(\n    (acc: Partial<Props> & Record<string, unknown>, key) => {\n      const valueAsString = (\n        reference.getAttribute(`data-tippy-${key}`) || ''\n      ).trim();\n\n      if (!valueAsString) {\n        return acc;\n      }\n\n      if (key === 'content') {\n        acc[key] = valueAsString;\n      } else {\n        try {\n          acc[key] = JSON.parse(valueAsString);\n        } catch (e) {\n          acc[key] = valueAsString;\n        }\n      }\n\n      return acc;\n    },\n    {}\n  );\n\n  return props;\n}\n\nexport function evaluateProps(\n  reference: ReferenceElement,\n  props: Props\n): Props {\n  const out = {\n    ...props,\n    content: invokeWithArgsOrReturn(props.content, [reference]),\n    ...(props.ignoreAttributes\n      ? {}\n      : getDataAttributeProps(reference, props.plugins)),\n  };\n\n  out.aria = {\n    ...defaultProps.aria,\n    ...out.aria,\n  };\n\n  out.aria = {\n    expanded:\n      out.aria.expanded === 'auto' ? props.interactive : out.aria.expanded,\n    content:\n      out.aria.content === 'auto'\n        ? props.interactive\n          ? null\n          : 'describedby'\n        : out.aria.content,\n  };\n\n  return out;\n}\n\nexport function validateProps(\n  partialProps: Partial<Props> = {},\n  plugins: Plugin[] = []\n): void {\n  const keys = Object.keys(partialProps) as Array<keyof Props>;\n  keys.forEach((prop) => {\n    const nonPluginProps = removeProperties(\n      defaultProps,\n      Object.keys(pluginProps)\n    );\n\n    let didPassUnknownProp = !hasOwnProperty(nonPluginProps, prop);\n\n    // Check if the prop exists in `plugins`\n    if (didPassUnknownProp) {\n      didPassUnknownProp =\n        plugins.filter((plugin) => plugin.name === prop).length === 0;\n    }\n\n    warnWhen(\n      didPassUnknownProp,\n      [\n        `\\`${prop}\\``,\n        \"is not a valid prop. You may have spelled it incorrectly, or if it's\",\n        'a plugin, forgot to pass it in an array as props.plugins.',\n        '\\n\\n',\n        'All props: https://atomiks.github.io/tippyjs/v6/all-props/\\n',\n        'Plugins: https://atomiks.github.io/tippyjs/v6/plugins/',\n      ].join(' ')\n    );\n  });\n}\n", "import {\n  ARROW_CLASS,\n  BACKDROP_CLASS,\n  BOX_CLASS,\n  CONTENT_CLASS,\n  SVG_ARROW_CLASS,\n} from './constants';\nimport {div, isElement} from './dom-utils';\nimport {Instance, PopperElement, Props} from './types';\nimport {PopperChildren} from './types-internal';\nimport {arrayFrom} from './utils';\n\n// Firefox extensions don't allow .innerHTML = \"...\" property. This tricks it.\nconst innerHTML = (): 'innerHTML' => 'innerHTML';\n\nfunction dangerouslySetInnerHTML(element: Element, html: string): void {\n  element[innerHTML()] = html;\n}\n\nfunction createArrowElement(value: Props['arrow']): HTMLDivElement {\n  const arrow = div();\n\n  if (value === true) {\n    arrow.className = ARROW_CLASS;\n  } else {\n    arrow.className = SVG_ARROW_CLASS;\n\n    if (isElement(value)) {\n      arrow.appendChild(value);\n    } else {\n      dangerouslySetInnerHTML(arrow, value as string);\n    }\n  }\n\n  return arrow;\n}\n\nexport function setContent(content: HTMLDivElement, props: Props): void {\n  if (isElement(props.content)) {\n    dangerouslySetInnerHTML(content, '');\n    content.appendChild(props.content);\n  } else if (typeof props.content !== 'function') {\n    if (props.allowHTML) {\n      dangerouslySetInnerHTML(content, props.content);\n    } else {\n      content.textContent = props.content;\n    }\n  }\n}\n\nexport function getChildren(popper: PopperElement): PopperChildren {\n  const box = popper.firstElementChild as HTMLDivElement;\n  const boxChildren = arrayFrom(box.children);\n\n  return {\n    box,\n    content: boxChildren.find((node) => node.classList.contains(CONTENT_CLASS)),\n    arrow: boxChildren.find(\n      (node) =>\n        node.classList.contains(ARROW_CLASS) ||\n        node.classList.contains(SVG_ARROW_CLASS)\n    ),\n    backdrop: boxChildren.find((node) =>\n      node.classList.contains(BACKDROP_CLASS)\n    ),\n  };\n}\n\nexport function render(\n  instance: Instance\n): {\n  popper: PopperElement;\n  onUpdate?: (prevProps: Props, nextProps: Props) => void;\n} {\n  const popper = div();\n\n  const box = div();\n  box.className = BOX_CLASS;\n  box.setAttribute('data-state', 'hidden');\n  box.setAttribute('tabindex', '-1');\n\n  const content = div();\n  content.className = CONTENT_CLASS;\n  content.setAttribute('data-state', 'hidden');\n\n  setContent(content, instance.props);\n\n  popper.appendChild(box);\n  box.appendChild(content);\n\n  onUpdate(instance.props, instance.props);\n\n  function onUpdate(prevProps: Props, nextProps: Props): void {\n    const {box, content, arrow} = getChildren(popper);\n\n    if (nextProps.theme) {\n      box.setAttribute('data-theme', nextProps.theme);\n    } else {\n      box.removeAttribute('data-theme');\n    }\n\n    if (typeof nextProps.animation === 'string') {\n      box.setAttribute('data-animation', nextProps.animation);\n    } else {\n      box.removeAttribute('data-animation');\n    }\n\n    if (nextProps.inertia) {\n      box.setAttribute('data-inertia', '');\n    } else {\n      box.removeAttribute('data-inertia');\n    }\n\n    box.style.maxWidth =\n      typeof nextProps.maxWidth === 'number'\n        ? `${nextProps.maxWidth}px`\n        : nextProps.maxWidth;\n\n    if (nextProps.role) {\n      box.setAttribute('role', nextProps.role);\n    } else {\n      box.removeAttribute('role');\n    }\n\n    if (\n      prevProps.content !== nextProps.content ||\n      prevProps.allowHTML !== nextProps.allowHTML\n    ) {\n      setContent(content, instance.props);\n    }\n\n    if (nextProps.arrow) {\n      if (!arrow) {\n        box.appendChild(createArrowElement(nextProps.arrow));\n      } else if (prevProps.arrow !== nextProps.arrow) {\n        box.removeChild(arrow);\n        box.appendChild(createArrowElement(nextProps.arrow));\n      }\n    } else if (arrow) {\n      box.removeChild(arrow!);\n    }\n  }\n\n  return {\n    popper,\n    onUpdate,\n  };\n}\n\n// Runtime check to identify if the render function is the default one; this\n// way we can apply default CSS transitions logic and it can be tree-shaken away\nrender.$$tippy = true;\n", "import {createPopper, StrictModifiers, Modifier} from '@popperjs/core';\nimport {currentInput} from './bindGlobalEventListeners';\nimport {isIE11} from './browser';\nimport {TIPPY_DEFAULT_APPEND_TO, TOUCH_OPTIONS} from './constants';\nimport {\n  actualContains,\n  div,\n  getOwnerDocument,\n  isCursorOutsideInteractiveBorder,\n  isMouseEvent,\n  setTransitionDuration,\n  setVisibilityState,\n  updateTransitionEndListener,\n} from './dom-utils';\nimport {defaultProps, evaluateProps, getExtendedPassedProps} from './props';\nimport {getChildren} from './template';\nimport {\n  Content,\n  Instance,\n  LifecycleHooks,\n  PopperElement,\n  Props,\n  ReferenceElement,\n} from './types';\nimport {ListenerObject, PopperTreeData, PopperChildren} from './types-internal';\nimport {\n  arrayFrom,\n  debounce,\n  getValueAtIndexOrReturn,\n  invokeWithArgsOrReturn,\n  normalizeToArray,\n  pushIfUnique,\n  splitBySpaces,\n  unique,\n  removeUndefinedProps,\n} from './utils';\nimport {createMemoryLeakWarning, errorWhen, warnWhen} from './validation';\n\nlet idCounter = 1;\nlet mouseMoveListeners: ((event: MouseEvent) => void)[] = [];\n\n// Used by `hideAll()`\nexport let mountedInstances: Instance[] = [];\n\nexport default function createTippy(\n  reference: ReferenceElement,\n  passedProps: Partial<Props>\n): Instance {\n  const props = evaluateProps(reference, {\n    ...defaultProps,\n    ...getExtendedPassedProps(removeUndefinedProps(passedProps)),\n  });\n\n  // ===========================================================================\n  // 🔒 Private members\n  // ===========================================================================\n  let showTimeout: any;\n  let hideTimeout: any;\n  let scheduleHideAnimationFrame: number;\n  let isVisibleFromClick = false;\n  let didHideDueToDocumentMouseDown = false;\n  let didTouchMove = false;\n  let ignoreOnFirstUpdate = false;\n  let lastTriggerEvent: Event | undefined;\n  let currentTransitionEndListener: (event: TransitionEvent) => void;\n  let onFirstUpdate: () => void;\n  let listeners: ListenerObject[] = [];\n  let debouncedOnMouseMove = debounce(onMouseMove, props.interactiveDebounce);\n  let currentTarget: Element;\n\n  // ===========================================================================\n  // 🔑 Public members\n  // ===========================================================================\n  const id = idCounter++;\n  const popperInstance = null;\n  const plugins = unique(props.plugins);\n\n  const state = {\n    // Is the instance currently enabled?\n    isEnabled: true,\n    // Is the tippy currently showing and not transitioning out?\n    isVisible: false,\n    // Has the instance been destroyed?\n    isDestroyed: false,\n    // Is the tippy currently mounted to the DOM?\n    isMounted: false,\n    // Has the tippy finished transitioning in?\n    isShown: false,\n  };\n\n  const instance: Instance = {\n    // properties\n    id,\n    reference,\n    popper: div(),\n    popperInstance,\n    props,\n    state,\n    plugins,\n    // methods\n    clearDelayTimeouts,\n    setProps,\n    setContent,\n    show,\n    hide,\n    hideWithInteractivity,\n    enable,\n    disable,\n    unmount,\n    destroy,\n  };\n\n  // TODO: Investigate why this early return causes a TDZ error in the tests —\n  // it doesn't seem to happen in the browser\n  /* istanbul ignore if */\n  if (!props.render) {\n    if (__DEV__) {\n      errorWhen(true, 'render() function has not been supplied.');\n    }\n\n    return instance;\n  }\n\n  // ===========================================================================\n  // Initial mutations\n  // ===========================================================================\n  const {popper, onUpdate} = props.render(instance);\n\n  popper.setAttribute('data-__NAMESPACE_PREFIX__-root', '');\n  popper.id = `__NAMESPACE_PREFIX__-${instance.id}`;\n\n  instance.popper = popper;\n  reference._tippy = instance;\n  popper._tippy = instance;\n\n  const pluginsHooks = plugins.map((plugin) => plugin.fn(instance));\n  const hasAriaExpanded = reference.hasAttribute('aria-expanded');\n\n  addListeners();\n  handleAriaExpandedAttribute();\n  handleStyles();\n\n  invokeHook('onCreate', [instance]);\n\n  if (props.showOnCreate) {\n    scheduleShow();\n  }\n\n  // Prevent a tippy with a delay from hiding if the cursor left then returned\n  // before it started hiding\n  popper.addEventListener('mouseenter', () => {\n    if (instance.props.interactive && instance.state.isVisible) {\n      instance.clearDelayTimeouts();\n    }\n  });\n\n  popper.addEventListener('mouseleave', () => {\n    if (\n      instance.props.interactive &&\n      instance.props.trigger.indexOf('mouseenter') >= 0\n    ) {\n      getDocument().addEventListener('mousemove', debouncedOnMouseMove);\n    }\n  });\n\n  return instance;\n\n  // ===========================================================================\n  // 🔒 Private methods\n  // ===========================================================================\n  function getNormalizedTouchSettings(): [string | boolean, number] {\n    const {touch} = instance.props;\n    return Array.isArray(touch) ? touch : [touch, 0];\n  }\n\n  function getIsCustomTouchBehavior(): boolean {\n    return getNormalizedTouchSettings()[0] === 'hold';\n  }\n\n  function getIsDefaultRenderFn(): boolean {\n    // @ts-ignore\n    return !!instance.props.render?.$$tippy;\n  }\n\n  function getCurrentTarget(): Element {\n    return currentTarget || reference;\n  }\n\n  function getDocument(): Document {\n    const parent = getCurrentTarget().parentNode as Element;\n    return parent ? getOwnerDocument(parent) : document;\n  }\n\n  function getDefaultTemplateChildren(): PopperChildren {\n    return getChildren(popper);\n  }\n\n  function getDelay(isShow: boolean): number {\n    // For touch or keyboard input, force `0` delay for UX reasons\n    // Also if the instance is mounted but not visible (transitioning out),\n    // ignore delay\n    if (\n      (instance.state.isMounted && !instance.state.isVisible) ||\n      currentInput.isTouch ||\n      (lastTriggerEvent && lastTriggerEvent.type === 'focus')\n    ) {\n      return 0;\n    }\n\n    return getValueAtIndexOrReturn(\n      instance.props.delay,\n      isShow ? 0 : 1,\n      defaultProps.delay\n    );\n  }\n\n  function handleStyles(fromHide = false): void {\n    popper.style.pointerEvents =\n      instance.props.interactive && !fromHide ? '' : 'none';\n    popper.style.zIndex = `${instance.props.zIndex}`;\n  }\n\n  function invokeHook(\n    hook: keyof LifecycleHooks,\n    args: [Instance, any?],\n    shouldInvokePropsHook = true\n  ): void {\n    pluginsHooks.forEach((pluginHooks) => {\n      if (pluginHooks[hook]) {\n        pluginHooks[hook]!(...args);\n      }\n    });\n\n    if (shouldInvokePropsHook) {\n      instance.props[hook](...args);\n    }\n  }\n\n  function handleAriaContentAttribute(): void {\n    const {aria} = instance.props;\n\n    if (!aria.content) {\n      return;\n    }\n\n    const attr = `aria-${aria.content}`;\n    const id = popper.id;\n    const nodes = normalizeToArray(instance.props.triggerTarget || reference);\n\n    nodes.forEach((node) => {\n      const currentValue = node.getAttribute(attr);\n\n      if (instance.state.isVisible) {\n        node.setAttribute(attr, currentValue ? `${currentValue} ${id}` : id);\n      } else {\n        const nextValue = currentValue && currentValue.replace(id, '').trim();\n\n        if (nextValue) {\n          node.setAttribute(attr, nextValue);\n        } else {\n          node.removeAttribute(attr);\n        }\n      }\n    });\n  }\n\n  function handleAriaExpandedAttribute(): void {\n    if (hasAriaExpanded || !instance.props.aria.expanded) {\n      return;\n    }\n\n    const nodes = normalizeToArray(instance.props.triggerTarget || reference);\n\n    nodes.forEach((node) => {\n      if (instance.props.interactive) {\n        node.setAttribute(\n          'aria-expanded',\n          instance.state.isVisible && node === getCurrentTarget()\n            ? 'true'\n            : 'false'\n        );\n      } else {\n        node.removeAttribute('aria-expanded');\n      }\n    });\n  }\n\n  function cleanupInteractiveMouseListeners(): void {\n    getDocument().removeEventListener('mousemove', debouncedOnMouseMove);\n    mouseMoveListeners = mouseMoveListeners.filter(\n      (listener) => listener !== debouncedOnMouseMove\n    );\n  }\n\n  function onDocumentPress(event: MouseEvent | TouchEvent): void {\n    // Moved finger to scroll instead of an intentional tap outside\n    if (currentInput.isTouch) {\n      if (didTouchMove || event.type === 'mousedown') {\n        return;\n      }\n    }\n\n    const actualTarget =\n      (event.composedPath && event.composedPath()[0]) || event.target;\n\n    // Clicked on interactive popper\n    if (\n      instance.props.interactive &&\n      actualContains(popper, actualTarget as Element)\n    ) {\n      return;\n    }\n\n    // Clicked on the event listeners target\n    if (\n      normalizeToArray(instance.props.triggerTarget || reference).some((el) =>\n        actualContains(el, actualTarget as Element)\n      )\n    ) {\n      if (currentInput.isTouch) {\n        return;\n      }\n\n      if (\n        instance.state.isVisible &&\n        instance.props.trigger.indexOf('click') >= 0\n      ) {\n        return;\n      }\n    } else {\n      invokeHook('onClickOutside', [instance, event]);\n    }\n\n    if (instance.props.hideOnClick === true) {\n      instance.clearDelayTimeouts();\n      instance.hide();\n\n      // `mousedown` event is fired right before `focus` if pressing the\n      // currentTarget. This lets a tippy with `focus` trigger know that it\n      // should not show\n      didHideDueToDocumentMouseDown = true;\n      setTimeout(() => {\n        didHideDueToDocumentMouseDown = false;\n      });\n\n      // The listener gets added in `scheduleShow()`, but this may be hiding it\n      // before it shows, and hide()'s early bail-out behavior can prevent it\n      // from being cleaned up\n      if (!instance.state.isMounted) {\n        removeDocumentPress();\n      }\n    }\n  }\n\n  function onTouchMove(): void {\n    didTouchMove = true;\n  }\n\n  function onTouchStart(): void {\n    didTouchMove = false;\n  }\n\n  function addDocumentPress(): void {\n    const doc = getDocument();\n    doc.addEventListener('mousedown', onDocumentPress, true);\n    doc.addEventListener('touchend', onDocumentPress, TOUCH_OPTIONS);\n    doc.addEventListener('touchstart', onTouchStart, TOUCH_OPTIONS);\n    doc.addEventListener('touchmove', onTouchMove, TOUCH_OPTIONS);\n  }\n\n  function removeDocumentPress(): void {\n    const doc = getDocument();\n    doc.removeEventListener('mousedown', onDocumentPress, true);\n    doc.removeEventListener('touchend', onDocumentPress, TOUCH_OPTIONS);\n    doc.removeEventListener('touchstart', onTouchStart, TOUCH_OPTIONS);\n    doc.removeEventListener('touchmove', onTouchMove, TOUCH_OPTIONS);\n  }\n\n  function onTransitionedOut(duration: number, callback: () => void): void {\n    onTransitionEnd(duration, () => {\n      if (\n        !instance.state.isVisible &&\n        popper.parentNode &&\n        popper.parentNode.contains(popper)\n      ) {\n        callback();\n      }\n    });\n  }\n\n  function onTransitionedIn(duration: number, callback: () => void): void {\n    onTransitionEnd(duration, callback);\n  }\n\n  function onTransitionEnd(duration: number, callback: () => void): void {\n    const box = getDefaultTemplateChildren().box;\n\n    function listener(event: TransitionEvent): void {\n      if (event.target === box) {\n        updateTransitionEndListener(box, 'remove', listener);\n        callback();\n      }\n    }\n\n    // Make callback synchronous if duration is 0\n    // `transitionend` won't fire otherwise\n    if (duration === 0) {\n      return callback();\n    }\n\n    updateTransitionEndListener(box, 'remove', currentTransitionEndListener);\n    updateTransitionEndListener(box, 'add', listener);\n\n    currentTransitionEndListener = listener;\n  }\n\n  function on(\n    eventType: string,\n    handler: EventListener,\n    options: boolean | Record<string, unknown> = false\n  ): void {\n    const nodes = normalizeToArray(instance.props.triggerTarget || reference);\n    nodes.forEach((node) => {\n      node.addEventListener(eventType, handler, options);\n      listeners.push({node, eventType, handler, options});\n    });\n  }\n\n  function addListeners(): void {\n    if (getIsCustomTouchBehavior()) {\n      on('touchstart', onTrigger, {passive: true});\n      on('touchend', onMouseLeave as EventListener, {passive: true});\n    }\n\n    splitBySpaces(instance.props.trigger).forEach((eventType) => {\n      if (eventType === 'manual') {\n        return;\n      }\n\n      on(eventType, onTrigger);\n\n      switch (eventType) {\n        case 'mouseenter':\n          on('mouseleave', onMouseLeave as EventListener);\n          break;\n        case 'focus':\n          on(isIE11 ? 'focusout' : 'blur', onBlurOrFocusOut as EventListener);\n          break;\n        case 'focusin':\n          on('focusout', onBlurOrFocusOut as EventListener);\n          break;\n      }\n    });\n  }\n\n  function removeListeners(): void {\n    listeners.forEach(({node, eventType, handler, options}: ListenerObject) => {\n      node.removeEventListener(eventType, handler, options);\n    });\n    listeners = [];\n  }\n\n  function onTrigger(event: Event): void {\n    let shouldScheduleClickHide = false;\n\n    if (\n      !instance.state.isEnabled ||\n      isEventListenerStopped(event) ||\n      didHideDueToDocumentMouseDown\n    ) {\n      return;\n    }\n\n    const wasFocused = lastTriggerEvent?.type === 'focus';\n\n    lastTriggerEvent = event;\n    currentTarget = event.currentTarget as Element;\n\n    handleAriaExpandedAttribute();\n\n    if (!instance.state.isVisible && isMouseEvent(event)) {\n      // If scrolling, `mouseenter` events can be fired if the cursor lands\n      // over a new target, but `mousemove` events don't get fired. This\n      // causes interactive tooltips to get stuck open until the cursor is\n      // moved\n      mouseMoveListeners.forEach((listener) => listener(event));\n    }\n\n    // Toggle show/hide when clicking click-triggered tooltips\n    if (\n      event.type === 'click' &&\n      (instance.props.trigger.indexOf('mouseenter') < 0 ||\n        isVisibleFromClick) &&\n      instance.props.hideOnClick !== false &&\n      instance.state.isVisible\n    ) {\n      shouldScheduleClickHide = true;\n    } else {\n      scheduleShow(event);\n    }\n\n    if (event.type === 'click') {\n      isVisibleFromClick = !shouldScheduleClickHide;\n    }\n\n    if (shouldScheduleClickHide && !wasFocused) {\n      scheduleHide(event);\n    }\n  }\n\n  function onMouseMove(event: MouseEvent): void {\n    const target = event.target as Node;\n    const isCursorOverReferenceOrPopper =\n      getCurrentTarget().contains(target) || popper.contains(target);\n\n    if (event.type === 'mousemove' && isCursorOverReferenceOrPopper) {\n      return;\n    }\n\n    const popperTreeData = getNestedPopperTree()\n      .concat(popper)\n      .map((popper) => {\n        const instance = popper._tippy!;\n        const state = instance.popperInstance?.state;\n\n        if (state) {\n          return {\n            popperRect: popper.getBoundingClientRect(),\n            popperState: state,\n            props,\n          };\n        }\n\n        return null;\n      })\n      .filter(Boolean) as PopperTreeData[];\n\n    if (isCursorOutsideInteractiveBorder(popperTreeData, event)) {\n      cleanupInteractiveMouseListeners();\n      scheduleHide(event);\n    }\n  }\n\n  function onMouseLeave(event: MouseEvent): void {\n    const shouldBail =\n      isEventListenerStopped(event) ||\n      (instance.props.trigger.indexOf('click') >= 0 && isVisibleFromClick);\n\n    if (shouldBail) {\n      return;\n    }\n\n    if (instance.props.interactive) {\n      instance.hideWithInteractivity(event);\n      return;\n    }\n\n    scheduleHide(event);\n  }\n\n  function onBlurOrFocusOut(event: FocusEvent): void {\n    if (\n      instance.props.trigger.indexOf('focusin') < 0 &&\n      event.target !== getCurrentTarget()\n    ) {\n      return;\n    }\n\n    // If focus was moved to within the popper\n    if (\n      instance.props.interactive &&\n      event.relatedTarget &&\n      popper.contains(event.relatedTarget as Element)\n    ) {\n      return;\n    }\n\n    scheduleHide(event);\n  }\n\n  function isEventListenerStopped(event: Event): boolean {\n    return currentInput.isTouch\n      ? getIsCustomTouchBehavior() !== event.type.indexOf('touch') >= 0\n      : false;\n  }\n\n  function createPopperInstance(): void {\n    destroyPopperInstance();\n\n    const {\n      popperOptions,\n      placement,\n      offset,\n      getReferenceClientRect,\n      moveTransition,\n    } = instance.props;\n\n    const arrow = getIsDefaultRenderFn() ? getChildren(popper).arrow : null;\n\n    const computedReference = getReferenceClientRect\n      ? {\n          getBoundingClientRect: getReferenceClientRect,\n          contextElement:\n            getReferenceClientRect.contextElement || getCurrentTarget(),\n        }\n      : reference;\n\n    const tippyModifier: Modifier<'$$tippy', Record<string, unknown>> = {\n      name: '$$tippy',\n      enabled: true,\n      phase: 'beforeWrite',\n      requires: ['computeStyles'],\n      fn({state}) {\n        if (getIsDefaultRenderFn()) {\n          const {box} = getDefaultTemplateChildren();\n\n          ['placement', 'reference-hidden', 'escaped'].forEach((attr) => {\n            if (attr === 'placement') {\n              box.setAttribute('data-placement', state.placement);\n            } else {\n              if (state.attributes.popper[`data-popper-${attr}`]) {\n                box.setAttribute(`data-${attr}`, '');\n              } else {\n                box.removeAttribute(`data-${attr}`);\n              }\n            }\n          });\n\n          state.attributes.popper = {};\n        }\n      },\n    };\n\n    type TippyModifier = Modifier<'$$tippy', Record<string, unknown>>;\n    type ExtendedModifiers = StrictModifiers | Partial<TippyModifier>;\n\n    const modifiers: Array<ExtendedModifiers> = [\n      {\n        name: 'offset',\n        options: {\n          offset,\n        },\n      },\n      {\n        name: 'preventOverflow',\n        options: {\n          padding: {\n            top: 2,\n            bottom: 2,\n            left: 5,\n            right: 5,\n          },\n        },\n      },\n      {\n        name: 'flip',\n        options: {\n          padding: 5,\n        },\n      },\n      {\n        name: 'computeStyles',\n        options: {\n          adaptive: !moveTransition,\n        },\n      },\n      tippyModifier,\n    ];\n\n    if (getIsDefaultRenderFn() && arrow) {\n      modifiers.push({\n        name: 'arrow',\n        options: {\n          element: arrow,\n          padding: 3,\n        },\n      });\n    }\n\n    modifiers.push(...(popperOptions?.modifiers || []));\n\n    instance.popperInstance = createPopper<ExtendedModifiers>(\n      computedReference,\n      popper,\n      {\n        ...popperOptions,\n        placement,\n        onFirstUpdate,\n        modifiers,\n      }\n    );\n  }\n\n  function destroyPopperInstance(): void {\n    if (instance.popperInstance) {\n      instance.popperInstance.destroy();\n      instance.popperInstance = null;\n    }\n  }\n\n  function mount(): void {\n    const {appendTo} = instance.props;\n\n    let parentNode: any;\n\n    // By default, we'll append the popper to the triggerTargets's parentNode so\n    // it's directly after the reference element so the elements inside the\n    // tippy can be tabbed to\n    // If there are clipping issues, the user can specify a different appendTo\n    // and ensure focus management is handled correctly manually\n    const node = getCurrentTarget();\n\n    if (\n      (instance.props.interactive && appendTo === TIPPY_DEFAULT_APPEND_TO) ||\n      appendTo === 'parent'\n    ) {\n      parentNode = node.parentNode;\n    } else {\n      parentNode = invokeWithArgsOrReturn(appendTo, [node]);\n    }\n\n    // The popper element needs to exist on the DOM before its position can be\n    // updated as Popper needs to read its dimensions\n    if (!parentNode.contains(popper)) {\n      parentNode.appendChild(popper);\n    }\n\n    instance.state.isMounted = true;\n\n    createPopperInstance();\n\n    /* istanbul ignore else */\n    if (__DEV__) {\n      // Accessibility check\n      warnWhen(\n        instance.props.interactive &&\n          appendTo === defaultProps.appendTo &&\n          node.nextElementSibling !== popper,\n        [\n          'Interactive tippy element may not be accessible via keyboard',\n          'navigation because it is not directly after the reference element',\n          'in the DOM source order.',\n          '\\n\\n',\n          'Using a wrapper <div> or <span> tag around the reference element',\n          'solves this by creating a new parentNode context.',\n          '\\n\\n',\n          'Specifying `appendTo: document.body` silences this warning, but it',\n          'assumes you are using a focus management solution to handle',\n          'keyboard navigation.',\n          '\\n\\n',\n          'See: https://atomiks.github.io/tippyjs/v6/accessibility/#interactivity',\n        ].join(' ')\n      );\n    }\n  }\n\n  function getNestedPopperTree(): PopperElement[] {\n    return arrayFrom(\n      popper.querySelectorAll('[data-__NAMESPACE_PREFIX__-root]')\n    );\n  }\n\n  function scheduleShow(event?: Event): void {\n    instance.clearDelayTimeouts();\n\n    if (event) {\n      invokeHook('onTrigger', [instance, event]);\n    }\n\n    addDocumentPress();\n\n    let delay = getDelay(true);\n    const [touchValue, touchDelay] = getNormalizedTouchSettings();\n\n    if (currentInput.isTouch && touchValue === 'hold' && touchDelay) {\n      delay = touchDelay;\n    }\n\n    if (delay) {\n      showTimeout = setTimeout(() => {\n        instance.show();\n      }, delay);\n    } else {\n      instance.show();\n    }\n  }\n\n  function scheduleHide(event: Event): void {\n    instance.clearDelayTimeouts();\n\n    invokeHook('onUntrigger', [instance, event]);\n\n    if (!instance.state.isVisible) {\n      removeDocumentPress();\n\n      return;\n    }\n\n    // For interactive tippies, scheduleHide is added to a document.body handler\n    // from onMouseLeave so must intercept scheduled hides from mousemove/leave\n    // events when trigger contains mouseenter and click, and the tip is\n    // currently shown as a result of a click.\n    if (\n      instance.props.trigger.indexOf('mouseenter') >= 0 &&\n      instance.props.trigger.indexOf('click') >= 0 &&\n      ['mouseleave', 'mousemove'].indexOf(event.type) >= 0 &&\n      isVisibleFromClick\n    ) {\n      return;\n    }\n\n    const delay = getDelay(false);\n\n    if (delay) {\n      hideTimeout = setTimeout(() => {\n        if (instance.state.isVisible) {\n          instance.hide();\n        }\n      }, delay);\n    } else {\n      // Fixes a `transitionend` problem when it fires 1 frame too\n      // late sometimes, we don't want hide() to be called.\n      scheduleHideAnimationFrame = requestAnimationFrame(() => {\n        instance.hide();\n      });\n    }\n  }\n\n  // ===========================================================================\n  // 🔑 Public methods\n  // ===========================================================================\n  function enable(): void {\n    instance.state.isEnabled = true;\n  }\n\n  function disable(): void {\n    // Disabling the instance should also hide it\n    // https://github.com/atomiks/tippy.js-react/issues/106\n    instance.hide();\n    instance.state.isEnabled = false;\n  }\n\n  function clearDelayTimeouts(): void {\n    clearTimeout(showTimeout);\n    clearTimeout(hideTimeout);\n    cancelAnimationFrame(scheduleHideAnimationFrame);\n  }\n\n  function setProps(partialProps: Partial<Props>): void {\n    /* istanbul ignore else */\n    if (__DEV__) {\n      warnWhen(instance.state.isDestroyed, createMemoryLeakWarning('setProps'));\n    }\n\n    if (instance.state.isDestroyed) {\n      return;\n    }\n\n    invokeHook('onBeforeUpdate', [instance, partialProps]);\n\n    removeListeners();\n\n    const prevProps = instance.props;\n    const nextProps = evaluateProps(reference, {\n      ...prevProps,\n      ...removeUndefinedProps(partialProps),\n      ignoreAttributes: true,\n    });\n\n    instance.props = nextProps;\n\n    addListeners();\n\n    if (prevProps.interactiveDebounce !== nextProps.interactiveDebounce) {\n      cleanupInteractiveMouseListeners();\n      debouncedOnMouseMove = debounce(\n        onMouseMove,\n        nextProps.interactiveDebounce\n      );\n    }\n\n    // Ensure stale aria-expanded attributes are removed\n    if (prevProps.triggerTarget && !nextProps.triggerTarget) {\n      normalizeToArray(prevProps.triggerTarget).forEach((node) => {\n        node.removeAttribute('aria-expanded');\n      });\n    } else if (nextProps.triggerTarget) {\n      reference.removeAttribute('aria-expanded');\n    }\n\n    handleAriaExpandedAttribute();\n    handleStyles();\n\n    if (onUpdate) {\n      onUpdate(prevProps, nextProps);\n    }\n\n    if (instance.popperInstance) {\n      createPopperInstance();\n\n      // Fixes an issue with nested tippies if they are all getting re-rendered,\n      // and the nested ones get re-rendered first.\n      // https://github.com/atomiks/tippyjs-react/issues/177\n      // TODO: find a cleaner / more efficient solution(!)\n      getNestedPopperTree().forEach((nestedPopper) => {\n        // React (and other UI libs likely) requires a rAF wrapper as it flushes\n        // its work in one\n        requestAnimationFrame(nestedPopper._tippy!.popperInstance!.forceUpdate);\n      });\n    }\n\n    invokeHook('onAfterUpdate', [instance, partialProps]);\n  }\n\n  function setContent(content: Content): void {\n    instance.setProps({content});\n  }\n\n  function show(): void {\n    /* istanbul ignore else */\n    if (__DEV__) {\n      warnWhen(instance.state.isDestroyed, createMemoryLeakWarning('show'));\n    }\n\n    // Early bail-out\n    const isAlreadyVisible = instance.state.isVisible;\n    const isDestroyed = instance.state.isDestroyed;\n    const isDisabled = !instance.state.isEnabled;\n    const isTouchAndTouchDisabled =\n      currentInput.isTouch && !instance.props.touch;\n    const duration = getValueAtIndexOrReturn(\n      instance.props.duration,\n      0,\n      defaultProps.duration\n    );\n\n    if (\n      isAlreadyVisible ||\n      isDestroyed ||\n      isDisabled ||\n      isTouchAndTouchDisabled\n    ) {\n      return;\n    }\n\n    // Normalize `disabled` behavior across browsers.\n    // Firefox allows events on disabled elements, but Chrome doesn't.\n    // Using a wrapper element (i.e. <span>) is recommended.\n    if (getCurrentTarget().hasAttribute('disabled')) {\n      return;\n    }\n\n    invokeHook('onShow', [instance], false);\n    if (instance.props.onShow(instance) === false) {\n      return;\n    }\n\n    instance.state.isVisible = true;\n\n    if (getIsDefaultRenderFn()) {\n      popper.style.visibility = 'visible';\n    }\n\n    handleStyles();\n    addDocumentPress();\n\n    if (!instance.state.isMounted) {\n      popper.style.transition = 'none';\n    }\n\n    // If flipping to the opposite side after hiding at least once, the\n    // animation will use the wrong placement without resetting the duration\n    if (getIsDefaultRenderFn()) {\n      const {box, content} = getDefaultTemplateChildren();\n      setTransitionDuration([box, content], 0);\n    }\n\n    onFirstUpdate = (): void => {\n      if (!instance.state.isVisible || ignoreOnFirstUpdate) {\n        return;\n      }\n\n      ignoreOnFirstUpdate = true;\n\n      // reflow\n      void popper.offsetHeight;\n\n      popper.style.transition = instance.props.moveTransition;\n\n      if (getIsDefaultRenderFn() && instance.props.animation) {\n        const {box, content} = getDefaultTemplateChildren();\n        setTransitionDuration([box, content], duration);\n        setVisibilityState([box, content], 'visible');\n      }\n\n      handleAriaContentAttribute();\n      handleAriaExpandedAttribute();\n\n      pushIfUnique(mountedInstances, instance);\n\n      // certain modifiers (e.g. `maxSize`) require a second update after the\n      // popper has been positioned for the first time\n      instance.popperInstance?.forceUpdate();\n\n      invokeHook('onMount', [instance]);\n\n      if (instance.props.animation && getIsDefaultRenderFn()) {\n        onTransitionedIn(duration, () => {\n          instance.state.isShown = true;\n          invokeHook('onShown', [instance]);\n        });\n      }\n    };\n\n    mount();\n  }\n\n  function hide(): void {\n    /* istanbul ignore else */\n    if (__DEV__) {\n      warnWhen(instance.state.isDestroyed, createMemoryLeakWarning('hide'));\n    }\n\n    // Early bail-out\n    const isAlreadyHidden = !instance.state.isVisible;\n    const isDestroyed = instance.state.isDestroyed;\n    const isDisabled = !instance.state.isEnabled;\n    const duration = getValueAtIndexOrReturn(\n      instance.props.duration,\n      1,\n      defaultProps.duration\n    );\n\n    if (isAlreadyHidden || isDestroyed || isDisabled) {\n      return;\n    }\n\n    invokeHook('onHide', [instance], false);\n    if (instance.props.onHide(instance) === false) {\n      return;\n    }\n\n    instance.state.isVisible = false;\n    instance.state.isShown = false;\n    ignoreOnFirstUpdate = false;\n    isVisibleFromClick = false;\n\n    if (getIsDefaultRenderFn()) {\n      popper.style.visibility = 'hidden';\n    }\n\n    cleanupInteractiveMouseListeners();\n    removeDocumentPress();\n    handleStyles(true);\n\n    if (getIsDefaultRenderFn()) {\n      const {box, content} = getDefaultTemplateChildren();\n\n      if (instance.props.animation) {\n        setTransitionDuration([box, content], duration);\n        setVisibilityState([box, content], 'hidden');\n      }\n    }\n\n    handleAriaContentAttribute();\n    handleAriaExpandedAttribute();\n\n    if (instance.props.animation) {\n      if (getIsDefaultRenderFn()) {\n        onTransitionedOut(duration, instance.unmount);\n      }\n    } else {\n      instance.unmount();\n    }\n  }\n\n  function hideWithInteractivity(event: MouseEvent): void {\n    /* istanbul ignore else */\n    if (__DEV__) {\n      warnWhen(\n        instance.state.isDestroyed,\n        createMemoryLeakWarning('hideWithInteractivity')\n      );\n    }\n\n    getDocument().addEventListener('mousemove', debouncedOnMouseMove);\n    pushIfUnique(mouseMoveListeners, debouncedOnMouseMove);\n    debouncedOnMouseMove(event);\n  }\n\n  function unmount(): void {\n    /* istanbul ignore else */\n    if (__DEV__) {\n      warnWhen(instance.state.isDestroyed, createMemoryLeakWarning('unmount'));\n    }\n\n    if (instance.state.isVisible) {\n      instance.hide();\n    }\n\n    if (!instance.state.isMounted) {\n      return;\n    }\n\n    destroyPopperInstance();\n\n    // If a popper is not interactive, it will be appended outside the popper\n    // tree by default. This seems mainly for interactive tippies, but we should\n    // find a workaround if possible\n    getNestedPopperTree().forEach((nestedPopper) => {\n      nestedPopper._tippy!.unmount();\n    });\n\n    if (popper.parentNode) {\n      popper.parentNode.removeChild(popper);\n    }\n\n    mountedInstances = mountedInstances.filter((i) => i !== instance);\n\n    instance.state.isMounted = false;\n    invokeHook('onHidden', [instance]);\n  }\n\n  function destroy(): void {\n    /* istanbul ignore else */\n    if (__DEV__) {\n      warnWhen(instance.state.isDestroyed, createMemoryLeakWarning('destroy'));\n    }\n\n    if (instance.state.isDestroyed) {\n      return;\n    }\n\n    instance.clearDelayTimeouts();\n    instance.unmount();\n\n    removeListeners();\n\n    delete reference._tippy;\n\n    instance.state.isDestroyed = true;\n\n    invokeHook('onDestroy', [instance]);\n  }\n}\n", "import bindGlobalEventListeners, {\n  currentInput,\n} from './bindGlobalEventListeners';\nimport createTippy, {mountedInstances} from './createTippy';\nimport {getArrayOfElements, isElement, isReferenceElement} from './dom-utils';\nimport {defaultProps, setDefaultProps, validateProps} from './props';\nimport {HideAll, HideAllOptions, Instance, Props, Targets} from './types';\nimport {validateTargets, warnWhen} from './validation';\n\nfunction tippy(\n  targets: Targets,\n  optionalProps: Partial<Props> = {}\n): Instance | Instance[] {\n  const plugins = defaultProps.plugins.concat(optionalProps.plugins || []);\n\n  /* istanbul ignore else */\n  if (__DEV__) {\n    validateTargets(targets);\n    validateProps(optionalProps, plugins);\n  }\n\n  bindGlobalEventListeners();\n\n  const passedProps: Partial<Props> = {...optionalProps, plugins};\n\n  const elements = getArrayOfElements(targets);\n\n  /* istanbul ignore else */\n  if (__DEV__) {\n    const isSingleContentElement = isElement(passedProps.content);\n    const isMoreThanOneReferenceElement = elements.length > 1;\n    warnWhen(\n      isSingleContentElement && isMoreThanOneReferenceElement,\n      [\n        'tippy() was passed an Element as the `content` prop, but more than',\n        'one tippy instance was created by this invocation. This means the',\n        'content element will only be appended to the last tippy instance.',\n        '\\n\\n',\n        'Instead, pass the .innerHTML of the element, or use a function that',\n        'returns a cloned version of the element instead.',\n        '\\n\\n',\n        '1) content: element.innerHTML\\n',\n        '2) content: () => element.cloneNode(true)',\n      ].join(' ')\n    );\n  }\n\n  const instances = elements.reduce<Instance[]>(\n    (acc, reference): Instance[] => {\n      const instance = reference && createTippy(reference, passedProps);\n\n      if (instance) {\n        acc.push(instance);\n      }\n\n      return acc;\n    },\n    []\n  );\n\n  return isElement(targets) ? instances[0] : instances;\n}\n\ntippy.defaultProps = defaultProps;\ntippy.setDefaultProps = setDefaultProps;\ntippy.currentInput = currentInput;\n\nexport default tippy;\n\nexport const hideAll: HideAll = ({\n  exclude: excludedReferenceOrInstance,\n  duration,\n}: HideAllOptions = {}) => {\n  mountedInstances.forEach((instance) => {\n    let isExcluded = false;\n\n    if (excludedReferenceOrInstance) {\n      isExcluded = isReferenceElement(excludedReferenceOrInstance)\n        ? instance.reference === excludedReferenceOrInstance\n        : instance.popper === (excludedReferenceOrInstance as Instance).popper;\n    }\n\n    if (!isExcluded) {\n      const originalDuration = instance.props.duration;\n\n      instance.setProps({duration});\n      instance.hide();\n\n      if (!instance.state.isDestroyed) {\n        instance.setProps({duration: originalDuration});\n      }\n    }\n  });\n};\n", "import tippy from '..';\nimport {div} from '../dom-utils';\nimport {\n  C<PERSON><PERSON><PERSON><PERSON>,\n  <PERSON><PERSON><PERSON>,\n  CreateSingletonProps,\n  ReferenceElement,\n  CreateSingletonInstance,\n  Instance,\n  Props,\n} from '../types';\nimport {normalizeToArray, removeProperties} from '../utils';\nimport {errorWhen} from '../validation';\nimport {applyStyles, Modifier} from '@popperjs/core';\n\n// The default `applyStyles` modifier has a cleanup function that gets called\n// every time the popper is destroyed (i.e. a new target), removing the styles\n// and causing transitions to break for singletons when the console is open, but\n// most notably for non-transform styles being used, `gpuAcceleration: false`.\nconst applyStylesModifier: Modifier<'applyStyles', Record<string, unknown>> = {\n  ...applyStyles,\n  effect({state}) {\n    const initialStyles = {\n      popper: {\n        position: state.options.strategy,\n        left: '0',\n        top: '0',\n        margin: '0',\n      },\n      arrow: {\n        position: 'absolute',\n      },\n      reference: {},\n    };\n\n    Object.assign(state.elements.popper.style, initialStyles.popper);\n    state.styles = initialStyles;\n\n    if (state.elements.arrow) {\n      Object.assign(state.elements.arrow.style, initialStyles.arrow);\n    }\n\n    // intentionally return no cleanup function\n    // return () => { ... }\n  },\n};\n\nconst createSingleton: CreateSingleton = (\n  tippyInstances,\n  optionalProps = {}\n) => {\n  /* istanbul ignore else */\n  if (__DEV__) {\n    errorWhen(\n      !Array.isArray(tippyInstances),\n      [\n        'The first argument passed to createSingleton() must be an array of',\n        'tippy instances. The passed value was',\n        String(tippyInstances),\n      ].join(' ')\n    );\n  }\n\n  let individualInstances = tippyInstances;\n  let references: Array<ReferenceElement> = [];\n  let triggerTargets: Array<Element> = [];\n  let currentTarget: Element | null;\n  let overrides = optionalProps.overrides;\n  let interceptSetPropsCleanups: Array<() => void> = [];\n  let shownOnCreate = false;\n\n  function setTriggerTargets(): void {\n    triggerTargets = individualInstances\n      .map((instance) =>\n        normalizeToArray(instance.props.triggerTarget || instance.reference)\n      )\n      .reduce((acc, item) => acc.concat(item), []);\n  }\n\n  function setReferences(): void {\n    references = individualInstances.map((instance) => instance.reference);\n  }\n\n  function enableInstances(isEnabled: boolean): void {\n    individualInstances.forEach((instance) => {\n      if (isEnabled) {\n        instance.enable();\n      } else {\n        instance.disable();\n      }\n    });\n  }\n\n  function interceptSetProps(singleton: Instance): Array<() => void> {\n    return individualInstances.map((instance) => {\n      const originalSetProps = instance.setProps;\n\n      instance.setProps = (props): void => {\n        originalSetProps(props);\n\n        if (instance.reference === currentTarget) {\n          singleton.setProps(props);\n        }\n      };\n\n      return (): void => {\n        instance.setProps = originalSetProps;\n      };\n    });\n  }\n\n  // have to pass singleton, as it maybe undefined on first call\n  function prepareInstance(\n    singleton: Instance,\n    target: ReferenceElement\n  ): void {\n    const index = triggerTargets.indexOf(target);\n\n    // bail-out\n    if (target === currentTarget) {\n      return;\n    }\n\n    currentTarget = target;\n\n    const overrideProps: Partial<Props> = (overrides || [])\n      .concat('content')\n      .reduce((acc, prop) => {\n        (acc as any)[prop] = individualInstances[index].props[prop];\n        return acc;\n      }, {});\n\n    singleton.setProps({\n      ...overrideProps,\n      getReferenceClientRect:\n        typeof overrideProps.getReferenceClientRect === 'function'\n          ? overrideProps.getReferenceClientRect\n          : (): ClientRect => references[index]?.getBoundingClientRect(),\n    });\n  }\n\n  enableInstances(false);\n  setReferences();\n  setTriggerTargets();\n\n  const plugin: Plugin = {\n    fn() {\n      return {\n        onDestroy(): void {\n          enableInstances(true);\n        },\n        onHidden(): void {\n          currentTarget = null;\n        },\n        onClickOutside(instance): void {\n          if (instance.props.showOnCreate && !shownOnCreate) {\n            shownOnCreate = true;\n            currentTarget = null;\n          }\n        },\n        onShow(instance): void {\n          if (instance.props.showOnCreate && !shownOnCreate) {\n            shownOnCreate = true;\n            prepareInstance(instance, references[0]);\n          }\n        },\n        onTrigger(instance, event): void {\n          prepareInstance(instance, event.currentTarget as Element);\n        },\n      };\n    },\n  };\n\n  const singleton = tippy(div(), {\n    ...removeProperties(optionalProps, ['overrides']),\n    plugins: [plugin, ...(optionalProps.plugins || [])],\n    triggerTarget: triggerTargets,\n    popperOptions: {\n      ...optionalProps.popperOptions,\n      modifiers: [\n        ...(optionalProps.popperOptions?.modifiers || []),\n        applyStylesModifier,\n      ],\n    },\n  }) as CreateSingletonInstance<CreateSingletonProps>;\n\n  const originalShow = singleton.show;\n\n  singleton.show = (target?: ReferenceElement | Instance | number): void => {\n    originalShow();\n\n    // first time, showOnCreate or programmatic call with no params\n    // default to showing first instance\n    if (!currentTarget && target == null) {\n      return prepareInstance(singleton, references[0]);\n    }\n\n    // triggered from event (do nothing as prepareInstance already called by onTrigger)\n    // programmatic call with no params when already visible (do nothing again)\n    if (currentTarget && target == null) {\n      return;\n    }\n\n    // target is index of instance\n    if (typeof target === 'number') {\n      return (\n        references[target] && prepareInstance(singleton, references[target])\n      );\n    }\n\n    // target is a child tippy instance\n    if (individualInstances.indexOf(target as Instance) >= 0) {\n      const ref = (target as Instance).reference;\n      return prepareInstance(singleton, ref);\n    }\n\n    // target is a ReferenceElement\n    if (references.indexOf(target as ReferenceElement) >= 0) {\n      return prepareInstance(singleton, target as ReferenceElement);\n    }\n  };\n\n  singleton.showNext = (): void => {\n    const first = references[0];\n    if (!currentTarget) {\n      return singleton.show(0);\n    }\n    const index = references.indexOf(currentTarget);\n    singleton.show(references[index + 1] || first);\n  };\n\n  singleton.showPrevious = (): void => {\n    const last = references[references.length - 1];\n    if (!currentTarget) {\n      return singleton.show(last);\n    }\n    const index = references.indexOf(currentTarget);\n    const target = references[index - 1] || last;\n    singleton.show(target);\n  };\n\n  const originalSetProps = singleton.setProps;\n\n  singleton.setProps = (props): void => {\n    overrides = props.overrides || overrides;\n    originalSetProps(props);\n  };\n\n  singleton.setInstances = (nextInstances): void => {\n    enableInstances(true);\n    interceptSetPropsCleanups.forEach((fn) => fn());\n\n    individualInstances = nextInstances;\n\n    enableInstances(false);\n    setReferences();\n    setTriggerTargets();\n    interceptSetPropsCleanups = interceptSetProps(singleton);\n\n    singleton.setProps({triggerTarget: triggerTargets});\n  };\n\n  interceptSetPropsCleanups = interceptSetProps(singleton);\n\n  return singleton;\n};\n\nexport default createSingleton;\n", "import tippy from '..';\nimport {TOUCH_OPTIONS} from '../constants';\nimport {defaultProps} from '../props';\nimport {Instance, Props, Targets} from '../types';\nimport {ListenerObject} from '../types-internal';\nimport {normalizeToArray, removeProperties} from '../utils';\nimport {errorWhen} from '../validation';\n\nconst BUBBLING_EVENTS_MAP = {\n  mouseover: 'mouseenter',\n  focusin: 'focus',\n  click: 'click',\n};\n\n/**\n * Creates a delegate instance that controls the creation of tippy instances\n * for child elements (`target` CSS selector).\n */\nfunction delegate(\n  targets: Targets,\n  props: Partial<Props> & {target: string}\n): Instance | Instance[] {\n  /* istanbul ignore else */\n  if (__DEV__) {\n    errorWhen(\n      !(props && props.target),\n      [\n        'You must specity a `target` prop indicating a CSS selector string matching',\n        'the target elements that should receive a tippy.',\n      ].join(' ')\n    );\n  }\n\n  let listeners: ListenerObject[] = [];\n  let childTippyInstances: Instance[] = [];\n  let disabled = false;\n\n  const {target} = props;\n\n  const nativeProps = removeProperties(props, ['target']);\n  const parentProps = {...nativeProps, trigger: 'manual', touch: false};\n  const childProps = {\n    touch: defaultProps.touch,\n    ...nativeProps,\n    showOnCreate: true,\n  };\n\n  const returnValue = tippy(targets, parentProps);\n  const normalizedReturnValue = normalizeToArray(returnValue);\n\n  function onTrigger(event: Event): void {\n    if (!event.target || disabled) {\n      return;\n    }\n\n    const targetNode = (event.target as Element).closest(target);\n\n    if (!targetNode) {\n      return;\n    }\n\n    // Get relevant trigger with fallbacks:\n    // 1. Check `data-tippy-trigger` attribute on target node\n    // 2. Fallback to `trigger` passed to `delegate()`\n    // 3. Fallback to `defaultProps.trigger`\n    const trigger =\n      targetNode.getAttribute('data-tippy-trigger') ||\n      props.trigger ||\n      defaultProps.trigger;\n\n    // @ts-ignore\n    if (targetNode._tippy) {\n      return;\n    }\n\n    if (event.type === 'touchstart' && typeof childProps.touch === 'boolean') {\n      return;\n    }\n\n    if (\n      event.type !== 'touchstart' &&\n      trigger.indexOf((BUBBLING_EVENTS_MAP as any)[event.type]) < 0\n    ) {\n      return;\n    }\n\n    const instance = tippy(targetNode, childProps);\n\n    if (instance) {\n      childTippyInstances = childTippyInstances.concat(instance);\n    }\n  }\n\n  function on(\n    node: Element,\n    eventType: string,\n    handler: EventListener,\n    options: boolean | Record<string, unknown> = false\n  ): void {\n    node.addEventListener(eventType, handler, options);\n    listeners.push({node, eventType, handler, options});\n  }\n\n  function addEventListeners(instance: Instance): void {\n    const {reference} = instance;\n\n    on(reference, 'touchstart', onTrigger, TOUCH_OPTIONS);\n    on(reference, 'mouseover', onTrigger);\n    on(reference, 'focusin', onTrigger);\n    on(reference, 'click', onTrigger);\n  }\n\n  function removeEventListeners(): void {\n    listeners.forEach(({node, eventType, handler, options}: ListenerObject) => {\n      node.removeEventListener(eventType, handler, options);\n    });\n    listeners = [];\n  }\n\n  function applyMutations(instance: Instance): void {\n    const originalDestroy = instance.destroy;\n    const originalEnable = instance.enable;\n    const originalDisable = instance.disable;\n\n    instance.destroy = (shouldDestroyChildInstances = true): void => {\n      if (shouldDestroyChildInstances) {\n        childTippyInstances.forEach((instance) => {\n          instance.destroy();\n        });\n      }\n\n      childTippyInstances = [];\n\n      removeEventListeners();\n      originalDestroy();\n    };\n\n    instance.enable = (): void => {\n      originalEnable();\n      childTippyInstances.forEach((instance) => instance.enable());\n      disabled = false;\n    };\n\n    instance.disable = (): void => {\n      originalDisable();\n      childTippyInstances.forEach((instance) => instance.disable());\n      disabled = true;\n    };\n\n    addEventListeners(instance);\n  }\n\n  normalizedReturnValue.forEach(applyMutations);\n\n  return returnValue;\n}\n\nexport default delegate;\n", "import {BACKDROP_CLASS} from '../constants';\nimport {div, setVisibilityState} from '../dom-utils';\nimport {getChildren} from '../template';\nimport {AnimateFill} from '../types';\nimport {errorWhen} from '../validation';\n\nconst animateFill: AnimateFill = {\n  name: 'animateFill',\n  defaultValue: false,\n  fn(instance) {\n    // @ts-ignore\n    if (!instance.props.render?.$$tippy) {\n      if (__DEV__) {\n        errorWhen(\n          instance.props.animateFill,\n          'The `animateFill` plugin requires the default render function.'\n        );\n      }\n\n      return {};\n    }\n\n    const {box, content} = getChildren(instance.popper);\n\n    const backdrop = instance.props.animateFill\n      ? createBackdropElement()\n      : null;\n\n    return {\n      onCreate(): void {\n        if (backdrop) {\n          box.insertBefore(backdrop, box.firstElementChild!);\n          box.setAttribute('data-animatefill', '');\n          box.style.overflow = 'hidden';\n\n          instance.setProps({arrow: false, animation: 'shift-away'});\n        }\n      },\n      onMount(): void {\n        if (backdrop) {\n          const {transitionDuration} = box.style;\n          const duration = Number(transitionDuration.replace('ms', ''));\n\n          // The content should fade in after the backdrop has mostly filled the\n          // tooltip element. `clip-path` is the other alternative but is not\n          // well-supported and is buggy on some devices.\n          content.style.transitionDelay = `${Math.round(duration / 10)}ms`;\n\n          backdrop.style.transitionDuration = transitionDuration;\n          setVisibilityState([backdrop], 'visible');\n        }\n      },\n      onShow(): void {\n        if (backdrop) {\n          backdrop.style.transitionDuration = '0ms';\n        }\n      },\n      onHide(): void {\n        if (backdrop) {\n          setVisibilityState([backdrop], 'hidden');\n        }\n      },\n    };\n  },\n};\n\nexport default animateFill;\n\nfunction createBackdropElement(): HTMLDivElement {\n  const backdrop = div();\n  backdrop.className = BACKDROP_CLASS;\n  setVisibilityState([backdrop], 'hidden');\n  return backdrop;\n}\n", "import {getOwnerDocument, isMouseEvent} from '../dom-utils';\nimport {FollowCursor, Instance} from '../types';\n\nlet mouseCoords = {clientX: 0, clientY: 0};\nlet activeInstances: Array<{instance: Instance; doc: Document}> = [];\n\nfunction storeMouseCoords({clientX, clientY}: MouseEvent): void {\n  mouseCoords = {clientX, clientY};\n}\n\nfunction addMouseCoordsListener(doc: Document): void {\n  doc.addEventListener('mousemove', storeMouseCoords);\n}\n\nfunction removeMouseCoordsListener(doc: Document): void {\n  doc.removeEventListener('mousemove', storeMouseCoords);\n}\n\nconst followCursor: FollowCursor = {\n  name: 'followCursor',\n  defaultValue: false,\n  fn(instance) {\n    const reference = instance.reference;\n    const doc = getOwnerDocument(instance.props.triggerTarget || reference);\n\n    let isInternalUpdate = false;\n    let wasFocusEvent = false;\n    let isUnmounted = true;\n    let prevProps = instance.props;\n\n    function getIsInitialBehavior(): boolean {\n      return (\n        instance.props.followCursor === 'initial' && instance.state.isVisible\n      );\n    }\n\n    function addListener(): void {\n      doc.addEventListener('mousemove', onMouseMove);\n    }\n\n    function removeListener(): void {\n      doc.removeEventListener('mousemove', onMouseMove);\n    }\n\n    function unsetGetReferenceClientRect(): void {\n      isInternalUpdate = true;\n      instance.setProps({getReferenceClientRect: null});\n      isInternalUpdate = false;\n    }\n\n    function onMouseMove(event: MouseEvent): void {\n      // If the instance is interactive, avoid updating the position unless it's\n      // over the reference element\n      const isCursorOverReference = event.target\n        ? reference.contains(event.target as Node)\n        : true;\n      const {followCursor} = instance.props;\n      const {clientX, clientY} = event;\n\n      const rect = reference.getBoundingClientRect();\n      const relativeX = clientX - rect.left;\n      const relativeY = clientY - rect.top;\n\n      if (isCursorOverReference || !instance.props.interactive) {\n        instance.setProps({\n          // @ts-ignore - unneeded DOMRect properties\n          getReferenceClientRect() {\n            const rect = reference.getBoundingClientRect();\n\n            let x = clientX;\n            let y = clientY;\n\n            if (followCursor === 'initial') {\n              x = rect.left + relativeX;\n              y = rect.top + relativeY;\n            }\n\n            const top = followCursor === 'horizontal' ? rect.top : y;\n            const right = followCursor === 'vertical' ? rect.right : x;\n            const bottom = followCursor === 'horizontal' ? rect.bottom : y;\n            const left = followCursor === 'vertical' ? rect.left : x;\n\n            return {\n              width: right - left,\n              height: bottom - top,\n              top,\n              right,\n              bottom,\n              left,\n            };\n          },\n        });\n      }\n    }\n\n    function create(): void {\n      if (instance.props.followCursor) {\n        activeInstances.push({instance, doc});\n        addMouseCoordsListener(doc);\n      }\n    }\n\n    function destroy(): void {\n      activeInstances = activeInstances.filter(\n        (data) => data.instance !== instance\n      );\n\n      if (activeInstances.filter((data) => data.doc === doc).length === 0) {\n        removeMouseCoordsListener(doc);\n      }\n    }\n\n    return {\n      onCreate: create,\n      onDestroy: destroy,\n      onBeforeUpdate(): void {\n        prevProps = instance.props;\n      },\n      onAfterUpdate(_, {followCursor}): void {\n        if (isInternalUpdate) {\n          return;\n        }\n\n        if (\n          followCursor !== undefined &&\n          prevProps.followCursor !== followCursor\n        ) {\n          destroy();\n\n          if (followCursor) {\n            create();\n\n            if (\n              instance.state.isMounted &&\n              !wasFocusEvent &&\n              !getIsInitialBehavior()\n            ) {\n              addListener();\n            }\n          } else {\n            removeListener();\n            unsetGetReferenceClientRect();\n          }\n        }\n      },\n      onMount(): void {\n        if (instance.props.followCursor && !wasFocusEvent) {\n          if (isUnmounted) {\n            onMouseMove(mouseCoords as MouseEvent);\n            isUnmounted = false;\n          }\n\n          if (!getIsInitialBehavior()) {\n            addListener();\n          }\n        }\n      },\n      onTrigger(_, event): void {\n        if (isMouseEvent(event)) {\n          mouseCoords = {clientX: event.clientX, clientY: event.clientY};\n        }\n        wasFocusEvent = event.type === 'focus';\n      },\n      onHidden(): void {\n        if (instance.props.followCursor) {\n          unsetGetReferenceClientRect();\n          removeListener();\n          isUnmounted = true;\n        }\n      },\n    };\n  },\n};\n\nexport default followCursor;\n", "import {Modifier, Placement} from '@popperjs/core';\nimport {isMouseEvent} from '../dom-utils';\nimport {BasePlacement, InlinePositioning, Props} from '../types';\nimport {arrayFrom, getBasePlacement} from '../utils';\n\nfunction getProps(props: Props, modifier: Modifier<any, any>): Partial<Props> {\n  return {\n    popperOptions: {\n      ...props.popperOptions,\n      modifiers: [\n        ...(props.popperOptions?.modifiers || []).filter(\n          ({name}) => name !== modifier.name\n        ),\n        modifier,\n      ],\n    },\n  };\n}\n\nconst inlinePositioning: InlinePositioning = {\n  name: 'inlinePositioning',\n  defaultValue: false,\n  fn(instance) {\n    const {reference} = instance;\n\n    function isEnabled(): boolean {\n      return !!instance.props.inlinePositioning;\n    }\n\n    let placement: Placement;\n    let cursorRectIndex = -1;\n    let isInternalUpdate = false;\n    let triedPlacements: Array<string> = [];\n\n    const modifier: Modifier<\n      'tippyInlinePositioning',\n      Record<string, unknown>\n    > = {\n      name: 'tippyInlinePositioning',\n      enabled: true,\n      phase: 'afterWrite',\n      fn({state}) {\n        if (isEnabled()) {\n          if (triedPlacements.indexOf(state.placement) !== -1) {\n            triedPlacements = [];\n          }\n\n          if (\n            placement !== state.placement &&\n            triedPlacements.indexOf(state.placement) === -1\n          ) {\n            triedPlacements.push(state.placement);\n            instance.setProps({\n              // @ts-ignore - unneeded DOMRect properties\n              getReferenceClientRect: () =>\n                getReferenceClientRect(state.placement),\n            });\n          }\n\n          placement = state.placement;\n        }\n      },\n    };\n\n    function getReferenceClientRect(placement: Placement): Partial<DOMRect> {\n      return getInlineBoundingClientRect(\n        getBasePlacement(placement),\n        reference.getBoundingClientRect(),\n        arrayFrom(reference.getClientRects()),\n        cursorRectIndex\n      );\n    }\n\n    function setInternalProps(partialProps: Partial<Props>): void {\n      isInternalUpdate = true;\n      instance.setProps(partialProps);\n      isInternalUpdate = false;\n    }\n\n    function addModifier(): void {\n      if (!isInternalUpdate) {\n        setInternalProps(getProps(instance.props, modifier));\n      }\n    }\n\n    return {\n      onCreate: addModifier,\n      onAfterUpdate: addModifier,\n      onTrigger(_, event): void {\n        if (isMouseEvent(event)) {\n          const rects = arrayFrom(instance.reference.getClientRects());\n          const cursorRect = rects.find(\n            (rect) =>\n              rect.left - 2 <= event.clientX &&\n              rect.right + 2 >= event.clientX &&\n              rect.top - 2 <= event.clientY &&\n              rect.bottom + 2 >= event.clientY\n          );\n          const index = rects.indexOf(cursorRect);\n          cursorRectIndex = index > -1 ? index : cursorRectIndex;\n        }\n      },\n      onHidden(): void {\n        cursorRectIndex = -1;\n      },\n    };\n  },\n};\n\nexport default inlinePositioning;\n\nexport function getInlineBoundingClientRect(\n  currentBasePlacement: BasePlacement | null,\n  boundingRect: DOMRect,\n  clientRects: DOMRect[],\n  cursorRectIndex: number\n): {\n  top: number;\n  bottom: number;\n  left: number;\n  right: number;\n  width: number;\n  height: number;\n} {\n  // Not an inline element, or placement is not yet known\n  if (clientRects.length < 2 || currentBasePlacement === null) {\n    return boundingRect;\n  }\n\n  // There are two rects and they are disjoined\n  if (\n    clientRects.length === 2 &&\n    cursorRectIndex >= 0 &&\n    clientRects[0].left > clientRects[1].right\n  ) {\n    return clientRects[cursorRectIndex] || boundingRect;\n  }\n\n  switch (currentBasePlacement) {\n    case 'top':\n    case 'bottom': {\n      const firstRect = clientRects[0];\n      const lastRect = clientRects[clientRects.length - 1];\n      const isTop = currentBasePlacement === 'top';\n\n      const top = firstRect.top;\n      const bottom = lastRect.bottom;\n      const left = isTop ? firstRect.left : lastRect.left;\n      const right = isTop ? firstRect.right : lastRect.right;\n      const width = right - left;\n      const height = bottom - top;\n\n      return {top, bottom, left, right, width, height};\n    }\n    case 'left':\n    case 'right': {\n      const minLeft = Math.min(...clientRects.map((rects) => rects.left));\n      const maxRight = Math.max(...clientRects.map((rects) => rects.right));\n      const measureRects = clientRects.filter((rect) =>\n        currentBasePlacement === 'left'\n          ? rect.left === minLeft\n          : rect.right === maxRight\n      );\n\n      const top = measureRects[0].top;\n      const bottom = measureRects[measureRects.length - 1].bottom;\n      const left = minLeft;\n      const right = maxRight;\n      const width = right - left;\n      const height = bottom - top;\n\n      return {top, bottom, left, right, width, height};\n    }\n    default: {\n      return boundingRect;\n    }\n  }\n}\n", "import {VirtualElement} from '@popperjs/core';\nimport {ReferenceElement, Sticky} from '../types';\n\nconst sticky: Sticky = {\n  name: 'sticky',\n  defaultValue: false,\n  fn(instance) {\n    const {reference, popper} = instance;\n\n    function getReference(): ReferenceElement | VirtualElement {\n      return instance.popperInstance\n        ? instance.popperInstance.state.elements.reference\n        : reference;\n    }\n\n    function shouldCheck(value: 'reference' | 'popper'): boolean {\n      return instance.props.sticky === true || instance.props.sticky === value;\n    }\n\n    let prevRefRect: ClientRect | null = null;\n    let prevPopRect: ClientRect | null = null;\n\n    function updatePosition(): void {\n      const currentRefRect = shouldCheck('reference')\n        ? getReference().getBoundingClientRect()\n        : null;\n      const currentPopRect = shouldCheck('popper')\n        ? popper.getBoundingClientRect()\n        : null;\n\n      if (\n        (currentRefRect && areRectsDifferent(prevRefRect, currentRefRect)) ||\n        (currentPopRect && areRectsDifferent(prevPopRect, currentPopRect))\n      ) {\n        if (instance.popperInstance) {\n          instance.popperInstance.update();\n        }\n      }\n\n      prevRefRect = currentRefRect;\n      prevPopRect = currentPopRect;\n\n      if (instance.state.isMounted) {\n        requestAnimationFrame(updatePosition);\n      }\n    }\n\n    return {\n      onMount(): void {\n        if (instance.props.sticky) {\n          updatePosition();\n        }\n      },\n    };\n  },\n};\n\nexport default sticky;\n\nfunction areRectsDifferent(\n  rectA: ClientRect | null,\n  rectB: ClientRect | null\n): boolean {\n  if (rectA && rectB) {\n    return (\n      rectA.top !== rectB.top ||\n      rectA.right !== rectB.right ||\n      rectA.bottom !== rectB.bottom ||\n      rectA.left !== rectB.left\n    );\n  }\n\n  return true;\n}\n", "import tippy, {hideAll} from '../src';\nimport createSingleton from '../src/addons/createSingleton';\nimport delegate from '../src/addons/delegate';\nimport animateFill from '../src/plugins/animateFill';\nimport followCursor from '../src/plugins/followCursor';\nimport inlinePositioning from '../src/plugins/inlinePositioning';\nimport sticky from '../src/plugins/sticky';\nimport {ROUND_ARROW} from '../src/constants';\n\ntippy.setDefaultProps({\n  plugins: [animateFill, followCursor, inlinePositioning, sticky],\n  animation: false,\n});\n\ntippy.createSingleton = createSingleton;\ntippy.delegate = delegate;\ntippy.hideAll = hideAll;\ntippy.roundArrow = ROUND_ARROW;\n\nexport default tippy;\n"], "names": ["ROUND_ARROW", "CONTENT_CLASS", "BACKDROP_CLASS", "ARROW_CLASS", "SVG_ARROW_CLASS", "TOUCH_OPTIONS", "passive", "capture", "TIPPY_DEFAULT_APPEND_TO", "document", "body", "hasOwnProperty", "obj", "key", "call", "getValueAtIndexOrReturn", "value", "index", "defaultValue", "Array", "isArray", "v", "isType", "type", "str", "toString", "indexOf", "invokeWithArgsOrReturn", "args", "debounce", "fn", "ms", "timeout", "arg", "clearTimeout", "setTimeout", "removeProperties", "keys", "clone", "for<PERSON>ach", "splitBySpaces", "split", "filter", "Boolean", "normalizeToArray", "concat", "pushIfUnique", "arr", "push", "unique", "item", "getBasePlacement", "placement", "arrayFrom", "slice", "removeUndefinedProps", "Object", "reduce", "acc", "undefined", "div", "createElement", "isElement", "some", "isNodeList", "isMouseEvent", "isReferenceElement", "_tippy", "reference", "getArrayOfElements", "querySelectorAll", "setTransitionDuration", "els", "el", "style", "transitionDuration", "setVisibilityState", "state", "setAttribute", "getOwnerDocument", "elementOrElements", "element", "ownerDocument", "isCursorOutsideInteractiveBorder", "popperTreeData", "event", "clientX", "clientY", "every", "popperRect", "popperState", "props", "interactiveBorder", "basePlacement", "offsetData", "modifiersData", "offset", "topDistance", "top", "y", "bottomDistance", "bottom", "leftDistance", "left", "x", "rightDistance", "right", "exceedsTop", "exceedsBottom", "exceedsLeft", "exceedsRight", "updateTransitionEndListener", "box", "action", "listener", "method", "actualContains", "parent", "child", "target", "contains", "getRootNode", "host", "currentInput", "is<PERSON><PERSON>ch", "lastMouseMoveTime", "onDocumentTouchStart", "window", "performance", "addEventListener", "onDocumentMouseMove", "now", "removeEventListener", "onWindowBlur", "activeElement", "instance", "blur", "isVisible", "bindGlobalEventListeners", "<PERSON><PERSON><PERSON><PERSON>", "isIE11", "msCrypto", "createMemoryLeakWarning", "txt", "join", "clean", "spacesAndTabs", "lineStartWithSpaces", "replace", "trim", "getDevMessage", "message", "getFormattedMessage", "visitedMessages", "resetVisitedMessages", "Set", "warn<PERSON><PERSON>", "condition", "has", "add", "console", "warn", "<PERSON><PERSON><PERSON>", "error", "validateTargets", "targets", "didPassFalsyValue", "didPassPlainObject", "prototype", "String", "pluginProps", "animateFill", "followCursor", "inlinePositioning", "sticky", "renderProps", "allowHTML", "animation", "arrow", "content", "inertia", "max<PERSON><PERSON><PERSON>", "role", "theme", "zIndex", "defaultProps", "appendTo", "aria", "expanded", "delay", "duration", "getReferenceClientRect", "hideOnClick", "ignoreAttributes", "interactive", "interactiveDebounce", "moveTransition", "onAfterUpdate", "onBeforeUpdate", "onCreate", "onDestroy", "onHidden", "onHide", "onMount", "onShow", "onShown", "onTrigger", "onUntrigger", "onClickOutside", "plugins", "popperOptions", "render", "showOnCreate", "touch", "trigger", "triggerTarget", "defaultKeys", "setDefaultProps", "partialProps", "validateProps", "getExtendedPassedProps", "passedProps", "plugin", "name", "getDataAttributeProps", "propKeys", "valueAsString", "getAttribute", "JSON", "parse", "e", "evaluateProps", "out", "prop", "nonPluginProps", "didPassUnknownProp", "length", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "popper", "first<PERSON><PERSON><PERSON><PERSON><PERSON>", "boxChildren", "children", "find", "node", "classList", "backdrop", "idCounter", "mouseMoveListeners", "mountedInstances", "createTippy", "showTimeout", "hideTimeout", "scheduleHideAnimationFrame", "isVisibleFromClick", "didHideDueToDocumentMouseDown", "didTouchMove", "ignoreOnFirstUpdate", "lastTriggerEvent", "currentTransitionEndListener", "onFirstUpdate", "listeners", "debouncedOnMouseMove", "onMouseMove", "currentTarget", "id", "popperInstance", "isEnabled", "isDestroyed", "isMounted", "isShown", "clearDelayTimeouts", "setProps", "<PERSON><PERSON><PERSON><PERSON>", "show", "hide", "hideWithInteractivity", "enable", "disable", "unmount", "destroy", "onUpdate", "pluginsHooks", "map", "hasAriaExpanded", "hasAttribute", "addListeners", "handleAriaExpandedAttribute", "handleStyles", "invokeHook", "scheduleShow", "getDocument", "getNormalizedTouchSettings", "getIsCustomTouchBehavior", "getIsDefaultRenderFn", "$$tippy", "getC<PERSON>rentTarget", "parentNode", "getDefaultTemplateChildren", "get<PERSON>elay", "isShow", "fromHide", "pointerEvents", "hook", "shouldInvokePropsHook", "pluginHooks", "handleAriaContentAttribute", "attr", "nodes", "currentValue", "nextValue", "removeAttribute", "cleanupInteractiveMouseListeners", "onDocumentPress", "actual<PERSON>arget", "<PERSON><PERSON><PERSON>", "removeDocumentPress", "onTouchMove", "onTouchStart", "addDocumentPress", "doc", "onTransitionedOut", "callback", "onTransitionEnd", "onTransitionedIn", "on", "eventType", "handler", "options", "onMouseLeave", "onBlurOrFocusOut", "removeListeners", "shouldScheduleClickHide", "isEventListenerStopped", "wasFocused", "scheduleHide", "isCursorOverReferenceOrPopper", "getNestedPopperTree", "getBoundingClientRect", "shouldBail", "relatedTarget", "createPopperInstance", "destroyPopperInstance", "computedReference", "contextElement", "tippyModifier", "enabled", "phase", "requires", "attributes", "modifiers", "padding", "adaptive", "createPopper", "mount", "append<PERSON><PERSON><PERSON>", "nextElement<PERSON><PERSON>ling", "touchValue", "touchDelay", "requestAnimationFrame", "cancelAnimationFrame", "prevProps", "nextProps", "nestedPopper", "forceUpdate", "isAlreadyVisible", "isDisabled", "isTouchAndTouchDisabled", "visibility", "transition", "offsetHeight", "isAlreadyHidden", "<PERSON><PERSON><PERSON><PERSON>", "i", "tippy", "optionalProps", "elements", "isSingleContentElement", "isMoreThanOneReferenceElement", "instances", "hide<PERSON>ll", "excludedReferenceOrInstance", "exclude", "isExcluded", "originalDuration", "applyStylesModifier", "applyStyles", "effect", "initialStyles", "position", "strategy", "margin", "assign", "styles", "createSingleton", "tippyInstances", "individualInstances", "references", "triggerTargets", "overrides", "interceptSetPropsCleanups", "shownOnCreate", "setTriggerTargets", "setReferences", "enableInstances", "interceptSetProps", "singleton", "originalSetProps", "prepareInstance", "overrideProps", "originalShow", "ref", "showNext", "first", "showPrevious", "last", "setInstances", "nextInstances", "BUBBLING_EVENTS_MAP", "mouseover", "focusin", "click", "delegate", "childTippyInstances", "disabled", "nativeProps", "parentProps", "childProps", "returnValue", "normalizedReturnValue", "targetNode", "closest", "addEventListeners", "removeEventListeners", "applyMutations", "original<PERSON><PERSON>roy", "originalEnable", "originalDisable", "shouldDestroyChildInstances", "createBackdropElement", "insertBefore", "overflow", "Number", "transitionDelay", "Math", "round", "className", "mouseCoords", "activeInstances", "storeMouseCoords", "addMouseCoordsListener", "removeMouseCoordsListener", "isInternalUpdate", "wasFocusEvent", "isUnmounted", "getIsInitialBehavior", "addListener", "removeListener", "unsetGetReferenceClientRect", "isCursorOverReference", "rect", "relativeX", "relativeY", "width", "height", "create", "data", "_", "getProps", "modifier", "cursorRectIndex", "triedPlacements", "getInlineBoundingClientRect", "getClientRects", "setInternalProps", "addModifier", "rects", "cursorRect", "currentBasePlacement", "boundingRect", "clientRects", "firstRect", "lastRect", "isTop", "minLeft", "min", "maxRight", "max", "measureRects", "getReference", "<PERSON><PERSON><PERSON><PERSON>", "prevRefRect", "prevPopRect", "updatePosition", "currentRefRect", "currentPopRect", "areRectsDifferent", "update", "rectA", "rectB", "roundArrow"], "mappings": ";;;;;;EAAO,IAAMA,WAAW,GACtB,0LADK;EAIA,IAAMC,aAAa,kBAAnB;EACA,IAAMC,cAAc,mBAApB;EACA,IAAMC,WAAW,gBAAjB;EACA,IAAMC,eAAe,oBAArB;EAEA,IAAMC,aAAa,GAAG;EAACC,EAAAA,OAAO,EAAE,IAAV;EAAgBC,EAAAA,OAAO,EAAE;EAAzB,CAAtB;EAEA,IAAMC,uBAAuB,GAAG,SAA1BA,uBAA0B;EAAA,SAAMC,QAAQ,CAACC,IAAf;EAAA,CAAhC;;ECTA,SAASC,cAAT,CACLC,GADK,EAELC,GAFK,EAGI;EACT,SAAO,GAAGF,cAAH,CAAkBG,IAAlB,CAAuBF,GAAvB,EAA4BC,GAA5B,CAAP;EACD;AAED,EAAO,SAASE,uBAAT,CACLC,KADK,EAELC,KAFK,EAGLC,YAHK,EAIF;EACH,MAAIC,KAAK,CAACC,OAAN,CAAcJ,KAAd,CAAJ,EAA0B;EACxB,QAAMK,CAAC,GAAGL,KAAK,CAACC,KAAD,CAAf;EACA,WAAOI,CAAC,IAAI,IAAL,GACHF,KAAK,CAACC,OAAN,CAAcF,YAAd,IACEA,YAAY,CAACD,KAAD,CADd,GAEEC,YAHC,GAIHG,CAJJ;EAKD;;EAED,SAAOL,KAAP;EACD;AAED,EAAO,SAASM,MAAT,CAAgBN,KAAhB,EAA4BO,IAA5B,EAAmD;EACxD,MAAMC,GAAG,GAAG,GAAGC,QAAH,CAAYX,IAAZ,CAAiBE,KAAjB,CAAZ;EACA,SAAOQ,GAAG,CAACE,OAAJ,CAAY,SAAZ,MAA2B,CAA3B,IAAgCF,GAAG,CAACE,OAAJ,CAAeH,IAAf,UAA0B,CAAC,CAAlE;EACD;AAED,EAAO,SAASI,sBAAT,CAAgCX,KAAhC,EAA4CY,IAA5C,EAA8D;EACnE,SAAO,OAAOZ,KAAP,KAAiB,UAAjB,GAA8BA,KAAK,MAAL,SAASY,IAAT,CAA9B,GAA+CZ,KAAtD;EACD;AAED,EAAO,SAASa,QAAT,CACLC,EADK,EAELC,EAFK,EAGa;EAClB;EACA,MAAIA,EAAE,KAAK,CAAX,EAAc;EACZ,WAAOD,EAAP;EACD;;EAED,MAAIE,OAAJ;EAEA,SAAO,UAACC,GAAD,EAAe;EACpBC,IAAAA,YAAY,CAACF,OAAD,CAAZ;EACAA,IAAAA,OAAO,GAAGG,UAAU,CAAC,YAAM;EACzBL,MAAAA,EAAE,CAACG,GAAD,CAAF;EACD,KAFmB,EAEjBF,EAFiB,CAApB;EAGD,GALD;EAMD;AAED,EAAO,SAASK,gBAAT,CAA6BxB,GAA7B,EAAqCyB,IAArC,EAAiE;EACtE,MAAMC,KAAK,qBAAO1B,GAAP,CAAX;EACAyB,EAAAA,IAAI,CAACE,OAAL,CAAa,UAAC1B,GAAD,EAAS;EACpB,WAAQyB,KAAD,CAAezB,GAAf,CAAP;EACD,GAFD;EAGA,SAAOyB,KAAP;EACD;AAED,EAAO,SAASE,aAAT,CAAuBxB,KAAvB,EAAgD;EACrD,SAAOA,KAAK,CAACyB,KAAN,CAAY,KAAZ,EAAmBC,MAAnB,CAA0BC,OAA1B,CAAP;EACD;AAED,EAAO,SAASC,gBAAT,CAA6B5B,KAA7B,EAAkD;EACvD,SAAQ,EAAD,CAAY6B,MAAZ,CAAmB7B,KAAnB,CAAP;EACD;AAED,EAAO,SAAS8B,YAAT,CAAyBC,GAAzB,EAAmC/B,KAAnC,EAAmD;EACxD,MAAI+B,GAAG,CAACrB,OAAJ,CAAYV,KAAZ,MAAuB,CAAC,CAA5B,EAA+B;EAC7B+B,IAAAA,GAAG,CAACC,IAAJ,CAAShC,KAAT;EACD;EACF;AAED,EAIO,SAASiC,MAAT,CAAmBF,GAAnB,EAAkC;EACvC,SAAOA,GAAG,CAACL,MAAJ,CAAW,UAACQ,IAAD,EAAOjC,KAAP;EAAA,WAAiB8B,GAAG,CAACrB,OAAJ,CAAYwB,IAAZ,MAAsBjC,KAAvC;EAAA,GAAX,CAAP;EACD;AAED,EAIO,SAASkC,gBAAT,CAA0BC,SAA1B,EAA+D;EACpE,SAAOA,SAAS,CAACX,KAAV,CAAgB,GAAhB,EAAqB,CAArB,CAAP;EACD;AAED,EAAO,SAASY,SAAT,CAAmBrC,KAAnB,EAAiD;EACtD,SAAO,GAAGsC,KAAH,CAASxC,IAAT,CAAcE,KAAd,CAAP;EACD;AAED,EAAO,SAASuC,oBAAT,CACL3C,GADK,EAE6B;EAClC,SAAO4C,MAAM,CAACnB,IAAP,CAAYzB,GAAZ,EAAiB6C,MAAjB,CAAwB,UAACC,GAAD,EAAM7C,GAAN,EAAc;EAC3C,QAAID,GAAG,CAACC,GAAD,CAAH,KAAa8C,SAAjB,EAA4B;EACzBD,MAAAA,GAAD,CAAa7C,GAAb,IAAoBD,GAAG,CAACC,GAAD,CAAvB;EACD;;EAED,WAAO6C,GAAP;EACD,GANM,EAMJ,EANI,CAAP;EAOD;;ECtGM,SAASE,GAAT,GAA+B;EACpC,SAAOnD,QAAQ,CAACoD,aAAT,CAAuB,KAAvB,CAAP;EACD;AAED,EAAO,SAASC,SAAT,CAAmB9C,KAAnB,EAAwE;EAC7E,SAAO,CAAC,SAAD,EAAY,UAAZ,EAAwB+C,IAAxB,CAA6B,UAACxC,IAAD;EAAA,WAAUD,MAAM,CAACN,KAAD,EAAQO,IAAR,CAAhB;EAAA,GAA7B,CAAP;EACD;AAED,EAAO,SAASyC,UAAT,CAAoBhD,KAApB,EAAuD;EAC5D,SAAOM,MAAM,CAACN,KAAD,EAAQ,UAAR,CAAb;EACD;AAED,EAAO,SAASiD,YAAT,CAAsBjD,KAAtB,EAA2D;EAChE,SAAOM,MAAM,CAACN,KAAD,EAAQ,YAAR,CAAb;EACD;AAED,EAAO,SAASkD,kBAAT,CAA4BlD,KAA5B,EAAmE;EACxE,SAAO,CAAC,EAAEA,KAAK,IAAIA,KAAK,CAACmD,MAAf,IAAyBnD,KAAK,CAACmD,MAAN,CAAaC,SAAb,KAA2BpD,KAAtD,CAAR;EACD;AAED,EAAO,SAASqD,kBAAT,CAA4BrD,KAA5B,EAAuD;EAC5D,MAAI8C,SAAS,CAAC9C,KAAD,CAAb,EAAsB;EACpB,WAAO,CAACA,KAAD,CAAP;EACD;;EAED,MAAIgD,UAAU,CAAChD,KAAD,CAAd,EAAuB;EACrB,WAAOqC,SAAS,CAACrC,KAAD,CAAhB;EACD;;EAED,MAAIG,KAAK,CAACC,OAAN,CAAcJ,KAAd,CAAJ,EAA0B;EACxB,WAAOA,KAAP;EACD;;EAED,SAAOqC,SAAS,CAAC5C,QAAQ,CAAC6D,gBAAT,CAA0BtD,KAA1B,CAAD,CAAhB;EACD;AAED,EAAO,SAASuD,qBAAT,CACLC,GADK,EAELxD,KAFK,EAGC;EACNwD,EAAAA,GAAG,CAACjC,OAAJ,CAAY,UAACkC,EAAD,EAAQ;EAClB,QAAIA,EAAJ,EAAQ;EACNA,MAAAA,EAAE,CAACC,KAAH,CAASC,kBAAT,GAAiC3D,KAAjC;EACD;EACF,GAJD;EAKD;AAED,EAAO,SAAS4D,kBAAT,CACLJ,GADK,EAELK,KAFK,EAGC;EACNL,EAAAA,GAAG,CAACjC,OAAJ,CAAY,UAACkC,EAAD,EAAQ;EAClB,QAAIA,EAAJ,EAAQ;EACNA,MAAAA,EAAE,CAACK,YAAH,CAAgB,YAAhB,EAA8BD,KAA9B;EACD;EACF,GAJD;EAKD;AAED,EAAO,SAASE,gBAAT,CACLC,iBADK,EAEK;EAAA;;EACV,0BAAkBpC,gBAAgB,CAACoC,iBAAD,CAAlC;EAAA,MAAOC,OAAP,wBADU;;;EAIV,SAAOA,OAAO,QAAP,6BAAAA,OAAO,CAAEC,aAAT,mCAAwBxE,IAAxB,GAA+BuE,OAAO,CAACC,aAAvC,GAAuDzE,QAA9D;EACD;AAED,EAAO,SAAS0E,gCAAT,CACLC,cADK,EAELC,KAFK,EAGI;EACT,MAAOC,OAAP,GAA2BD,KAA3B,CAAOC,OAAP;EAAA,MAAgBC,OAAhB,GAA2BF,KAA3B,CAAgBE,OAAhB;EAEA,SAAOH,cAAc,CAACI,KAAf,CAAqB,gBAAsC;EAAA,QAApCC,UAAoC,QAApCA,UAAoC;EAAA,QAAxBC,WAAwB,QAAxBA,WAAwB;EAAA,QAAXC,KAAW,QAAXA,KAAW;EAChE,QAAOC,iBAAP,GAA4BD,KAA5B,CAAOC,iBAAP;EACA,QAAMC,aAAa,GAAG1C,gBAAgB,CAACuC,WAAW,CAACtC,SAAb,CAAtC;EACA,QAAM0C,UAAU,GAAGJ,WAAW,CAACK,aAAZ,CAA0BC,MAA7C;;EAEA,QAAI,CAACF,UAAL,EAAiB;EACf,aAAO,IAAP;EACD;;EAED,QAAMG,WAAW,GAAGJ,aAAa,KAAK,QAAlB,GAA6BC,UAAU,CAACI,GAAX,CAAgBC,CAA7C,GAAiD,CAArE;EACA,QAAMC,cAAc,GAAGP,aAAa,KAAK,KAAlB,GAA0BC,UAAU,CAACO,MAAX,CAAmBF,CAA7C,GAAiD,CAAxE;EACA,QAAMG,YAAY,GAAGT,aAAa,KAAK,OAAlB,GAA4BC,UAAU,CAACS,IAAX,CAAiBC,CAA7C,GAAiD,CAAtE;EACA,QAAMC,aAAa,GAAGZ,aAAa,KAAK,MAAlB,GAA2BC,UAAU,CAACY,KAAX,CAAkBF,CAA7C,GAAiD,CAAvE;EAEA,QAAMG,UAAU,GACdlB,UAAU,CAACS,GAAX,GAAiBX,OAAjB,GAA2BU,WAA3B,GAAyCL,iBAD3C;EAEA,QAAMgB,aAAa,GACjBrB,OAAO,GAAGE,UAAU,CAACY,MAArB,GAA8BD,cAA9B,GAA+CR,iBADjD;EAEA,QAAMiB,WAAW,GACfpB,UAAU,CAACc,IAAX,GAAkBjB,OAAlB,GAA4BgB,YAA5B,GAA2CV,iBAD7C;EAEA,QAAMkB,YAAY,GAChBxB,OAAO,GAAGG,UAAU,CAACiB,KAArB,GAA6BD,aAA7B,GAA6Cb,iBAD/C;EAGA,WAAOe,UAAU,IAAIC,aAAd,IAA+BC,WAA/B,IAA8CC,YAArD;EACD,GAxBM,CAAP;EAyBD;AAED,EAAO,SAASC,2BAAT,CACLC,GADK,EAELC,MAFK,EAGLC,QAHK,EAIC;EACN,MAAMC,MAAM,GAAMF,MAAN,kBAAZ,CADM;EAMN;;EACA,GAAC,eAAD,EAAkB,qBAAlB,EAAyC1E,OAAzC,CAAiD,UAAC8C,KAAD,EAAW;EAC1D2B,IAAAA,GAAG,CAACG,MAAD,CAAH,CAAY9B,KAAZ,EAAmB6B,QAAnB;EACD,GAFD;EAGD;EAED;EACA;EACA;EACA;;AACA,EAAO,SAASE,cAAT,CAAwBC,MAAxB,EAAyCC,KAAzC,EAAkE;EACvE,MAAIC,MAAM,GAAGD,KAAb;;EACA,SAAOC,MAAP,EAAe;EAAA;;EACb,QAAIF,MAAM,CAACG,QAAP,CAAgBD,MAAhB,CAAJ,EAA6B;EAC3B,aAAO,IAAP;EACD;;EACDA,IAAAA,MAAM,GAAIA,MAAM,CAACE,WAAX,2CAAIF,MAAM,CAACE,WAAP,EAAJ,qBAAG,oBAAiCC,IAA1C;EACD;;EACD,SAAO,KAAP;EACD;;EClIM,IAAMC,YAAY,GAAG;EAACC,EAAAA,OAAO,EAAE;EAAV,CAArB;EACP,IAAIC,iBAAiB,GAAG,CAAxB;EAEA;EACA;EACA;EACA;EACA;EACA;;AACA,EAAO,SAASC,oBAAT,GAAsC;EAC3C,MAAIH,YAAY,CAACC,OAAjB,EAA0B;EACxB;EACD;;EAEDD,EAAAA,YAAY,CAACC,OAAb,GAAuB,IAAvB;;EAEA,MAAIG,MAAM,CAACC,WAAX,EAAwB;EACtBvH,IAAAA,QAAQ,CAACwH,gBAAT,CAA0B,WAA1B,EAAuCC,mBAAvC;EACD;EACF;EAED;EACA;EACA;EACA;EACA;;AACA,EAAO,SAASA,mBAAT,GAAqC;EAC1C,MAAMC,GAAG,GAAGH,WAAW,CAACG,GAAZ,EAAZ;;EAEA,MAAIA,GAAG,GAAGN,iBAAN,GAA0B,EAA9B,EAAkC;EAChCF,IAAAA,YAAY,CAACC,OAAb,GAAuB,KAAvB;EAEAnH,IAAAA,QAAQ,CAAC2H,mBAAT,CAA6B,WAA7B,EAA0CF,mBAA1C;EACD;;EAEDL,EAAAA,iBAAiB,GAAGM,GAApB;EACD;EAED;EACA;EACA;EACA;EACA;EACA;;AACA,EAAO,SAASE,YAAT,GAA8B;EACnC,MAAMC,aAAa,GAAG7H,QAAQ,CAAC6H,aAA/B;;EAEA,MAAIpE,kBAAkB,CAACoE,aAAD,CAAtB,EAAuC;EACrC,QAAMC,QAAQ,GAAGD,aAAa,CAACnE,MAA/B;;EAEA,QAAImE,aAAa,CAACE,IAAd,IAAsB,CAACD,QAAQ,CAAC1D,KAAT,CAAe4D,SAA1C,EAAqD;EACnDH,MAAAA,aAAa,CAACE,IAAd;EACD;EACF;EACF;AAED,EAAe,SAASE,wBAAT,GAA0C;EACvDjI,EAAAA,QAAQ,CAACwH,gBAAT,CAA0B,YAA1B,EAAwCH,oBAAxC,EAA8DzH,aAA9D;EACA0H,EAAAA,MAAM,CAACE,gBAAP,CAAwB,MAAxB,EAAgCI,YAAhC;EACD;;EC9DM,IAAMM,SAAS,GACpB,OAAOZ,MAAP,KAAkB,WAAlB,IAAiC,OAAOtH,QAAP,KAAoB,WADhD;AAGP,EAAO,IAAMmI,MAAM,GAAGD,SAAS;EAE3B,CAAC,CAACZ,MAAM,CAACc,QAFkB,GAG3B,KAHG;;ECDA,SAASC,uBAAT,CAAiC3B,MAAjC,EAAyD;EAC9D,MAAM4B,GAAG,GAAG5B,MAAM,KAAK,SAAX,GAAuB,YAAvB,GAAsC,GAAlD;EAEA,SAAO,CACFA,MADE,0BACyB4B,GADzB,8CAEL,oCAFK,EAGLC,IAHK,CAGA,GAHA,CAAP;EAID;AAED,EAAO,SAASC,KAAT,CAAejI,KAAf,EAAsC;EAC3C,MAAMkI,aAAa,GAAG,YAAtB;EACA,MAAMC,mBAAmB,GAAG,WAA5B;EAEA,SAAOnI,KAAK,CACToI,OADI,CACIF,aADJ,EACmB,GADnB,EAEJE,OAFI,CAEID,mBAFJ,EAEyB,EAFzB,EAGJE,IAHI,EAAP;EAID;;EAED,SAASC,aAAT,CAAuBC,OAAvB,EAAgD;EAC9C,SAAON,KAAK,4BAGRA,KAAK,CAACM,OAAD,CAHG,0GAAZ;EAOD;;AAED,EAAO,SAASC,mBAAT,CAA6BD,OAA7B,EAAwD;EAC7D,SAAO,CACLD,aAAa,CAACC,OAAD,CADR;EAGL,wDAHK;EAKL,oBALK;EAOL,mBAPK,CAAP;EASD;;EAGD,IAAIE,eAAJ;;AACA,EAAa;EACXC,EAAAA,oBAAoB;EACrB;;AAED,EAAO,SAASA,oBAAT,GAAsC;EAC3CD,EAAAA,eAAe,GAAG,IAAIE,GAAJ,EAAlB;EACD;AAED,EAAO,SAASC,QAAT,CAAkBC,SAAlB,EAAsCN,OAAtC,EAA6D;EAClE,MAAIM,SAAS,IAAI,CAACJ,eAAe,CAACK,GAAhB,CAAoBP,OAApB,CAAlB,EAAgD;EAAA;;EAC9CE,IAAAA,eAAe,CAACM,GAAhB,CAAoBR,OAApB;;EACA,gBAAAS,OAAO,EAACC,IAAR,iBAAgBT,mBAAmB,CAACD,OAAD,CAAnC;EACD;EACF;AAED,EAAO,SAASW,SAAT,CAAmBL,SAAnB,EAAuCN,OAAvC,EAA8D;EACnE,MAAIM,SAAS,IAAI,CAACJ,eAAe,CAACK,GAAhB,CAAoBP,OAApB,CAAlB,EAAgD;EAAA;;EAC9CE,IAAAA,eAAe,CAACM,GAAhB,CAAoBR,OAApB;;EACA,iBAAAS,OAAO,EAACG,KAAR,kBAAiBX,mBAAmB,CAACD,OAAD,CAApC;EACD;EACF;AAED,EAAO,SAASa,eAAT,CAAyBC,OAAzB,EAAiD;EACtD,MAAMC,iBAAiB,GAAG,CAACD,OAA3B;EACA,MAAME,kBAAkB,GACtB/G,MAAM,CAACgH,SAAP,CAAiB/I,QAAjB,CAA0BX,IAA1B,CAA+BuJ,OAA/B,MAA4C,iBAA5C,IACA,CAAEA,OAAD,CAAiBpC,gBAFpB;EAIAiC,EAAAA,SAAS,CACPI,iBADO,EAEP,CACE,oBADF,EAEE,MAAMG,MAAM,CAACJ,OAAD,CAAZ,GAAwB,GAF1B,EAGE,oEAHF,EAIE,yBAJF,EAKErB,IALF,CAKO,GALP,CAFO,CAAT;EAUAkB,EAAAA,SAAS,CACPK,kBADO,EAEP,CACE,yEADF,EAEE,oEAFF,EAGEvB,IAHF,CAGO,GAHP,CAFO,CAAT;EAOD;;ECjFD,IAAM0B,WAAW,GAAG;EAClBC,EAAAA,WAAW,EAAE,KADK;EAElBC,EAAAA,YAAY,EAAE,KAFI;EAGlBC,EAAAA,iBAAiB,EAAE,KAHD;EAIlBC,EAAAA,MAAM,EAAE;EAJU,CAApB;EAOA,IAAMC,WAAW,GAAG;EAClBC,EAAAA,SAAS,EAAE,KADO;EAElBC,EAAAA,SAAS,EAAE,MAFO;EAGlBC,EAAAA,KAAK,EAAE,IAHW;EAIlBC,EAAAA,OAAO,EAAE,EAJS;EAKlBC,EAAAA,OAAO,EAAE,KALS;EAMlBC,EAAAA,QAAQ,EAAE,GANQ;EAOlBC,EAAAA,IAAI,EAAE,SAPY;EAQlBC,EAAAA,KAAK,EAAE,EARW;EASlBC,EAAAA,MAAM,EAAE;EATU,CAApB;AAYA,EAAO,IAAMC,YAA0B;EACrCC,EAAAA,QAAQ,EAAElL,uBAD2B;EAErCmL,EAAAA,IAAI,EAAE;EACJR,IAAAA,OAAO,EAAE,MADL;EAEJS,IAAAA,QAAQ,EAAE;EAFN,GAF+B;EAMrCC,EAAAA,KAAK,EAAE,CAN8B;EAOrCC,EAAAA,QAAQ,EAAE,CAAC,GAAD,EAAM,GAAN,CAP2B;EAQrCC,EAAAA,sBAAsB,EAAE,IARa;EASrCC,EAAAA,WAAW,EAAE,IATwB;EAUrCC,EAAAA,gBAAgB,EAAE,KAVmB;EAWrCC,EAAAA,WAAW,EAAE,KAXwB;EAYrCtG,EAAAA,iBAAiB,EAAE,CAZkB;EAarCuG,EAAAA,mBAAmB,EAAE,CAbgB;EAcrCC,EAAAA,cAAc,EAAE,EAdqB;EAerCpG,EAAAA,MAAM,EAAE,CAAC,CAAD,EAAI,EAAJ,CAf6B;EAgBrCqG,EAAAA,aAhBqC,2BAgBrB,EAhBqB;EAiBrCC,EAAAA,cAjBqC,4BAiBpB,EAjBoB;EAkBrCC,EAAAA,QAlBqC,sBAkB1B,EAlB0B;EAmBrCC,EAAAA,SAnBqC,uBAmBzB,EAnByB;EAoBrCC,EAAAA,QApBqC,sBAoB1B,EApB0B;EAqBrCC,EAAAA,MArBqC,oBAqB5B,EArB4B;EAsBrCC,EAAAA,OAtBqC,qBAsB3B,EAtB2B;EAuBrCC,EAAAA,MAvBqC,oBAuB5B,EAvB4B;EAwBrCC,EAAAA,OAxBqC,qBAwB3B,EAxB2B;EAyBrCC,EAAAA,SAzBqC,uBAyBzB,EAzByB;EA0BrCC,EAAAA,WA1BqC,yBA0BvB,EA1BuB;EA2BrCC,EAAAA,cA3BqC,4BA2BpB,EA3BoB;EA4BrC5J,EAAAA,SAAS,EAAE,KA5B0B;EA6BrC6J,EAAAA,OAAO,EAAE,EA7B4B;EA8BrCC,EAAAA,aAAa,EAAE,EA9BsB;EA+BrCC,EAAAA,MAAM,EAAE,IA/B6B;EAgCrCC,EAAAA,YAAY,EAAE,KAhCuB;EAiCrCC,EAAAA,KAAK,EAAE,IAjC8B;EAkCrCC,EAAAA,OAAO,EAAE,kBAlC4B;EAmCrCC,EAAAA,aAAa,EAAE;EAnCsB,GAoClC7C,WApCkC,EAqClCK,WArCkC,CAAhC;EAwCP,IAAMyC,WAAW,GAAGhK,MAAM,CAACnB,IAAP,CAAYoJ,YAAZ,CAApB;AAEA,EAAO,IAAMgC,eAAyC,GAAG,SAA5CA,eAA4C,CAACC,YAAD,EAAkB;EACzE;EACA,EAAa;EACXC,IAAAA,aAAa,CAACD,YAAD,EAAe,EAAf,CAAb;EACD;;EAED,MAAMrL,IAAI,GAAGmB,MAAM,CAACnB,IAAP,CAAYqL,YAAZ,CAAb;EACArL,EAAAA,IAAI,CAACE,OAAL,CAAa,UAAC1B,GAAD,EAAS;EACnB4K,IAAAA,YAAD,CAAsB5K,GAAtB,IAA6B6M,YAAY,CAAC7M,GAAD,CAAzC;EACD,GAFD;EAGD,CAVM;AAYP,EAAO,SAAS+M,sBAAT,CACLC,WADK,EAEW;EAChB,MAAMZ,OAAO,GAAGY,WAAW,CAACZ,OAAZ,IAAuB,EAAvC;EACA,MAAMvC,WAAW,GAAGuC,OAAO,CAACxJ,MAAR,CAAwC,UAACC,GAAD,EAAMoK,MAAN,EAAiB;EAC3E,QAAOC,IAAP,GAA6BD,MAA7B,CAAOC,IAAP;EAAA,QAAa7M,YAAb,GAA6B4M,MAA7B,CAAa5M,YAAb;;EAEA,QAAI6M,IAAJ,EAAU;EAAA;;EACRrK,MAAAA,GAAG,CAACqK,IAAD,CAAH,GACEF,WAAW,CAACE,IAAD,CAAX,KAAsBpK,SAAtB,GACIkK,WAAW,CAACE,IAAD,CADf,YAEKtC,YAAD,CAAsBsC,IAAtB,CAFJ,oBAEmC7M,YAHrC;EAID;;EAED,WAAOwC,GAAP;EACD,GAXmB,EAWjB,EAXiB,CAApB;EAaA,2BACKmK,WADL,EAEKnD,WAFL;EAID;AAED,EAAO,SAASsD,qBAAT,CACL5J,SADK,EAEL6I,OAFK,EAGoB;EACzB,MAAMgB,QAAQ,GAAGhB,OAAO,GACpBzJ,MAAM,CAACnB,IAAP,CAAYuL,sBAAsB,mBAAKnC,YAAL;EAAmBwB,IAAAA,OAAO,EAAPA;EAAnB,KAAlC,CADoB,GAEpBO,WAFJ;EAIA,MAAM7H,KAAK,GAAGsI,QAAQ,CAACxK,MAAT,CACZ,UAACC,GAAD,EAAgD7C,GAAhD,EAAwD;EACtD,QAAMqN,aAAa,GAAG,CACpB9J,SAAS,CAAC+J,YAAV,iBAAqCtN,GAArC,KAA+C,EAD3B,EAEpBwI,IAFoB,EAAtB;;EAIA,QAAI,CAAC6E,aAAL,EAAoB;EAClB,aAAOxK,GAAP;EACD;;EAED,QAAI7C,GAAG,KAAK,SAAZ,EAAuB;EACrB6C,MAAAA,GAAG,CAAC7C,GAAD,CAAH,GAAWqN,aAAX;EACD,KAFD,MAEO;EACL,UAAI;EACFxK,QAAAA,GAAG,CAAC7C,GAAD,CAAH,GAAWuN,IAAI,CAACC,KAAL,CAAWH,aAAX,CAAX;EACD,OAFD,CAEE,OAAOI,CAAP,EAAU;EACV5K,QAAAA,GAAG,CAAC7C,GAAD,CAAH,GAAWqN,aAAX;EACD;EACF;;EAED,WAAOxK,GAAP;EACD,GArBW,EAsBZ,EAtBY,CAAd;EAyBA,SAAOiC,KAAP;EACD;AAED,EAAO,SAAS4I,aAAT,CACLnK,SADK,EAELuB,KAFK,EAGE;EACP,MAAM6I,GAAG,qBACJ7I,KADI;EAEPwF,IAAAA,OAAO,EAAExJ,sBAAsB,CAACgE,KAAK,CAACwF,OAAP,EAAgB,CAAC/G,SAAD,CAAhB;EAFxB,KAGHuB,KAAK,CAACsG,gBAAN,GACA,EADA,GAEA+B,qBAAqB,CAAC5J,SAAD,EAAYuB,KAAK,CAACsH,OAAlB,CALlB,CAAT;EAQAuB,EAAAA,GAAG,CAAC7C,IAAJ,qBACKF,YAAY,CAACE,IADlB,EAEK6C,GAAG,CAAC7C,IAFT;EAKA6C,EAAAA,GAAG,CAAC7C,IAAJ,GAAW;EACTC,IAAAA,QAAQ,EACN4C,GAAG,CAAC7C,IAAJ,CAASC,QAAT,KAAsB,MAAtB,GAA+BjG,KAAK,CAACuG,WAArC,GAAmDsC,GAAG,CAAC7C,IAAJ,CAASC,QAFrD;EAGTT,IAAAA,OAAO,EACLqD,GAAG,CAAC7C,IAAJ,CAASR,OAAT,KAAqB,MAArB,GACIxF,KAAK,CAACuG,WAAN,GACE,IADF,GAEE,aAHN,GAIIsC,GAAG,CAAC7C,IAAJ,CAASR;EARN,GAAX;EAWA,SAAOqD,GAAP;EACD;AAED,EAAO,SAASb,aAAT,CACLD,YADK,EAELT,OAFK,EAGC;EAAA,MAFNS,YAEM;EAFNA,IAAAA,YAEM,GAFyB,EAEzB;EAAA;;EAAA,MADNT,OACM;EADNA,IAAAA,OACM,GADc,EACd;EAAA;;EACN,MAAM5K,IAAI,GAAGmB,MAAM,CAACnB,IAAP,CAAYqL,YAAZ,CAAb;EACArL,EAAAA,IAAI,CAACE,OAAL,CAAa,UAACkM,IAAD,EAAU;EACrB,QAAMC,cAAc,GAAGtM,gBAAgB,CACrCqJ,YADqC,EAErCjI,MAAM,CAACnB,IAAP,CAAYqI,WAAZ,CAFqC,CAAvC;EAKA,QAAIiE,kBAAkB,GAAG,CAAChO,cAAc,CAAC+N,cAAD,EAAiBD,IAAjB,CAAxC,CANqB;;EASrB,QAAIE,kBAAJ,EAAwB;EACtBA,MAAAA,kBAAkB,GAChB1B,OAAO,CAACvK,MAAR,CAAe,UAACoL,MAAD;EAAA,eAAYA,MAAM,CAACC,IAAP,KAAgBU,IAA5B;EAAA,OAAf,EAAiDG,MAAjD,KAA4D,CAD9D;EAED;;EAEDhF,IAAAA,QAAQ,CACN+E,kBADM,EAEN,OACOF,IADP,QAEE,sEAFF,EAGE,2DAHF,EAIE,MAJF,EAKE,8DALF,EAME,wDANF,EAOEzF,IAPF,CAOO,GAPP,CAFM,CAAR;EAWD,GAzBD;EA0BD;;ECzJM,SAAS6F,WAAT,CAAqBC,MAArB,EAA4D;EACjE,MAAM9H,GAAG,GAAG8H,MAAM,CAACC,iBAAnB;EACA,MAAMC,WAAW,GAAG3L,SAAS,CAAC2D,GAAG,CAACiI,QAAL,CAA7B;EAEA,SAAO;EACLjI,IAAAA,GAAG,EAAHA,GADK;EAELmE,IAAAA,OAAO,EAAE6D,WAAW,CAACE,IAAZ,CAAiB,UAACC,IAAD;EAAA,aAAUA,IAAI,CAACC,SAAL,CAAe5H,QAAf,CAAwBvH,aAAxB,CAAV;EAAA,KAAjB,CAFJ;EAGLiL,IAAAA,KAAK,EAAE8D,WAAW,CAACE,IAAZ,CACL,UAACC,IAAD;EAAA,aACEA,IAAI,CAACC,SAAL,CAAe5H,QAAf,CAAwBrH,WAAxB,KACAgP,IAAI,CAACC,SAAL,CAAe5H,QAAf,CAAwBpH,eAAxB,CAFF;EAAA,KADK,CAHF;EAQLiP,IAAAA,QAAQ,EAAEL,WAAW,CAACE,IAAZ,CAAiB,UAACC,IAAD;EAAA,aACzBA,IAAI,CAACC,SAAL,CAAe5H,QAAf,CAAwBtH,cAAxB,CADyB;EAAA,KAAjB;EARL,GAAP;EAYD;;EC5BD,IAAIoP,SAAS,GAAG,CAAhB;EACA,IAAIC,kBAAmD,GAAG,EAA1D;;AAGA,EAAO,IAAIC,gBAA4B,GAAG,EAAnC;AAEP,EAAe,SAASC,WAAT,CACbrL,SADa,EAEbyJ,WAFa,EAGH;EACV,MAAMlI,KAAK,GAAG4I,aAAa,CAACnK,SAAD,oBACtBqH,YADsB,EAEtBmC,sBAAsB,CAACrK,oBAAoB,CAACsK,WAAD,CAArB,CAFA,EAA3B,CADU;EAOV;EACA;;EACA,MAAI6B,WAAJ;EACA,MAAIC,WAAJ;EACA,MAAIC,0BAAJ;EACA,MAAIC,kBAAkB,GAAG,KAAzB;EACA,MAAIC,6BAA6B,GAAG,KAApC;EACA,MAAIC,YAAY,GAAG,KAAnB;EACA,MAAIC,mBAAmB,GAAG,KAA1B;EACA,MAAIC,gBAAJ;EACA,MAAIC,4BAAJ;EACA,MAAIC,aAAJ;EACA,MAAIC,SAA2B,GAAG,EAAlC;EACA,MAAIC,oBAAoB,GAAGxO,QAAQ,CAACyO,WAAD,EAAc3K,KAAK,CAACwG,mBAApB,CAAnC;EACA,MAAIoE,aAAJ,CArBU;EAwBV;EACA;;EACA,MAAMC,EAAE,GAAGlB,SAAS,EAApB;EACA,MAAMmB,cAAc,GAAG,IAAvB;EACA,MAAMxD,OAAO,GAAGhK,MAAM,CAAC0C,KAAK,CAACsH,OAAP,CAAtB;EAEA,MAAMpI,KAAK,GAAG;EACZ;EACA6L,IAAAA,SAAS,EAAE,IAFC;EAGZ;EACAjI,IAAAA,SAAS,EAAE,KAJC;EAKZ;EACAkI,IAAAA,WAAW,EAAE,KAND;EAOZ;EACAC,IAAAA,SAAS,EAAE,KARC;EASZ;EACAC,IAAAA,OAAO,EAAE;EAVG,GAAd;EAaA,MAAMtI,QAAkB,GAAG;EACzB;EACAiI,IAAAA,EAAE,EAAFA,EAFyB;EAGzBpM,IAAAA,SAAS,EAATA,SAHyB;EAIzB0K,IAAAA,MAAM,EAAElL,GAAG,EAJc;EAKzB6M,IAAAA,cAAc,EAAdA,cALyB;EAMzB9K,IAAAA,KAAK,EAALA,KANyB;EAOzBd,IAAAA,KAAK,EAALA,KAPyB;EAQzBoI,IAAAA,OAAO,EAAPA,OARyB;EASzB;EACA6D,IAAAA,kBAAkB,EAAlBA,kBAVyB;EAWzBC,IAAAA,QAAQ,EAARA,QAXyB;EAYzBC,IAAAA,UAAU,EAAVA,UAZyB;EAazBC,IAAAA,IAAI,EAAJA,IAbyB;EAczBC,IAAAA,IAAI,EAAJA,IAdyB;EAezBC,IAAAA,qBAAqB,EAArBA,qBAfyB;EAgBzBC,IAAAA,MAAM,EAANA,MAhByB;EAiBzBC,IAAAA,OAAO,EAAPA,OAjByB;EAkBzBC,IAAAA,OAAO,EAAPA,OAlByB;EAmBzBC,IAAAA,OAAO,EAAPA;EAnByB,GAA3B,CA3CU;EAkEV;;EACA;;EACA,MAAI,CAAC5L,KAAK,CAACwH,MAAX,EAAmB;EACjB,IAAa;EACXjD,MAAAA,SAAS,CAAC,IAAD,EAAO,0CAAP,CAAT;EACD;;EAED,WAAO3B,QAAP;EACD,GA1ES;EA6EV;EACA;;;EACA,sBAA2B5C,KAAK,CAACwH,MAAN,CAAa5E,QAAb,CAA3B;EAAA,MAAOuG,MAAP,iBAAOA,MAAP;EAAA,MAAe0C,QAAf,iBAAeA,QAAf;;EAEA1C,EAAAA,MAAM,CAAChK,YAAP,CAAoB,iBAApB,EAAsD,EAAtD;EACAgK,EAAAA,MAAM,CAAC0B,EAAP,cAAoCjI,QAAQ,CAACiI,EAA7C;EAEAjI,EAAAA,QAAQ,CAACuG,MAAT,GAAkBA,MAAlB;EACA1K,EAAAA,SAAS,CAACD,MAAV,GAAmBoE,QAAnB;EACAuG,EAAAA,MAAM,CAAC3K,MAAP,GAAgBoE,QAAhB;EAEA,MAAMkJ,YAAY,GAAGxE,OAAO,CAACyE,GAAR,CAAY,UAAC5D,MAAD;EAAA,WAAYA,MAAM,CAAChM,EAAP,CAAUyG,QAAV,CAAZ;EAAA,GAAZ,CAArB;EACA,MAAMoJ,eAAe,GAAGvN,SAAS,CAACwN,YAAV,CAAuB,eAAvB,CAAxB;EAEAC,EAAAA,YAAY;EACZC,EAAAA,2BAA2B;EAC3BC,EAAAA,YAAY;EAEZC,EAAAA,UAAU,CAAC,UAAD,EAAa,CAACzJ,QAAD,CAAb,CAAV;;EAEA,MAAI5C,KAAK,CAACyH,YAAV,EAAwB;EACtB6E,IAAAA,YAAY;EACb,GAnGS;EAsGV;;;EACAnD,EAAAA,MAAM,CAAC7G,gBAAP,CAAwB,YAAxB,EAAsC,YAAM;EAC1C,QAAIM,QAAQ,CAAC5C,KAAT,CAAeuG,WAAf,IAA8B3D,QAAQ,CAAC1D,KAAT,CAAe4D,SAAjD,EAA4D;EAC1DF,MAAAA,QAAQ,CAACuI,kBAAT;EACD;EACF,GAJD;EAMAhC,EAAAA,MAAM,CAAC7G,gBAAP,CAAwB,YAAxB,EAAsC,YAAM;EAC1C,QACEM,QAAQ,CAAC5C,KAAT,CAAeuG,WAAf,IACA3D,QAAQ,CAAC5C,KAAT,CAAe2H,OAAf,CAAuB5L,OAAvB,CAA+B,YAA/B,KAAgD,CAFlD,EAGE;EACAwQ,MAAAA,WAAW,GAAGjK,gBAAd,CAA+B,WAA/B,EAA4CoI,oBAA5C;EACD;EACF,GAPD;EASA,SAAO9H,QAAP,CAtHU;EAyHV;EACA;;EACA,WAAS4J,0BAAT,GAAkE;EAChE,QAAO9E,KAAP,GAAgB9E,QAAQ,CAAC5C,KAAzB,CAAO0H,KAAP;EACA,WAAOlM,KAAK,CAACC,OAAN,CAAciM,KAAd,IAAuBA,KAAvB,GAA+B,CAACA,KAAD,EAAQ,CAAR,CAAtC;EACD;;EAED,WAAS+E,wBAAT,GAA6C;EAC3C,WAAOD,0BAA0B,GAAG,CAAH,CAA1B,KAAoC,MAA3C;EACD;;EAED,WAASE,oBAAT,GAAyC;EAAA;;EACvC;EACA,WAAO,CAAC,2BAAC9J,QAAQ,CAAC5C,KAAT,CAAewH,MAAhB,aAAC,sBAAuBmF,OAAxB,CAAR;EACD;;EAED,WAASC,gBAAT,GAAqC;EACnC,WAAOhC,aAAa,IAAInM,SAAxB;EACD;;EAED,WAAS8N,WAAT,GAAiC;EAC/B,QAAM7K,MAAM,GAAGkL,gBAAgB,GAAGC,UAAlC;EACA,WAAOnL,MAAM,GAAGtC,gBAAgB,CAACsC,MAAD,CAAnB,GAA8B5G,QAA3C;EACD;;EAED,WAASgS,0BAAT,GAAsD;EACpD,WAAO5D,WAAW,CAACC,MAAD,CAAlB;EACD;;EAED,WAAS4D,QAAT,CAAkBC,MAAlB,EAA2C;EACzC;EACA;EACA;EACA,QACGpK,QAAQ,CAAC1D,KAAT,CAAe+L,SAAf,IAA4B,CAACrI,QAAQ,CAAC1D,KAAT,CAAe4D,SAA7C,IACAd,YAAY,CAACC,OADb,IAECqI,gBAAgB,IAAIA,gBAAgB,CAAC1O,IAAjB,KAA0B,OAHjD,EAIE;EACA,aAAO,CAAP;EACD;;EAED,WAAOR,uBAAuB,CAC5BwH,QAAQ,CAAC5C,KAAT,CAAekG,KADa,EAE5B8G,MAAM,GAAG,CAAH,GAAO,CAFe,EAG5BlH,YAAY,CAACI,KAHe,CAA9B;EAKD;;EAED,WAASkG,YAAT,CAAsBa,QAAtB,EAA8C;EAAA,QAAxBA,QAAwB;EAAxBA,MAAAA,QAAwB,GAAb,KAAa;EAAA;;EAC5C9D,IAAAA,MAAM,CAACpK,KAAP,CAAamO,aAAb,GACEtK,QAAQ,CAAC5C,KAAT,CAAeuG,WAAf,IAA8B,CAAC0G,QAA/B,GAA0C,EAA1C,GAA+C,MADjD;EAEA9D,IAAAA,MAAM,CAACpK,KAAP,CAAa8G,MAAb,QAAyBjD,QAAQ,CAAC5C,KAAT,CAAe6F,MAAxC;EACD;;EAED,WAASwG,UAAT,CACEc,IADF,EAEElR,IAFF,EAGEmR,qBAHF,EAIQ;EAAA,QADNA,qBACM;EADNA,MAAAA,qBACM,GADkB,IAClB;EAAA;;EACNtB,IAAAA,YAAY,CAAClP,OAAb,CAAqB,UAACyQ,WAAD,EAAiB;EACpC,UAAIA,WAAW,CAACF,IAAD,CAAf,EAAuB;EACrBE,QAAAA,WAAW,CAACF,IAAD,CAAX,OAAAE,WAAW,EAAWpR,IAAX,CAAX;EACD;EACF,KAJD;;EAMA,QAAImR,qBAAJ,EAA2B;EAAA;;EACzB,yBAAAxK,QAAQ,CAAC5C,KAAT,EAAemN,IAAf,yBAAwBlR,IAAxB;EACD;EACF;;EAED,WAASqR,0BAAT,GAA4C;EAC1C,QAAOtH,IAAP,GAAepD,QAAQ,CAAC5C,KAAxB,CAAOgG,IAAP;;EAEA,QAAI,CAACA,IAAI,CAACR,OAAV,EAAmB;EACjB;EACD;;EAED,QAAM+H,IAAI,aAAWvH,IAAI,CAACR,OAA1B;EACA,QAAMqF,EAAE,GAAG1B,MAAM,CAAC0B,EAAlB;EACA,QAAM2C,KAAK,GAAGvQ,gBAAgB,CAAC2F,QAAQ,CAAC5C,KAAT,CAAe4H,aAAf,IAAgCnJ,SAAjC,CAA9B;EAEA+O,IAAAA,KAAK,CAAC5Q,OAAN,CAAc,UAAC4M,IAAD,EAAU;EACtB,UAAMiE,YAAY,GAAGjE,IAAI,CAAChB,YAAL,CAAkB+E,IAAlB,CAArB;;EAEA,UAAI3K,QAAQ,CAAC1D,KAAT,CAAe4D,SAAnB,EAA8B;EAC5B0G,QAAAA,IAAI,CAACrK,YAAL,CAAkBoO,IAAlB,EAAwBE,YAAY,GAAMA,YAAN,SAAsB5C,EAAtB,GAA6BA,EAAjE;EACD,OAFD,MAEO;EACL,YAAM6C,SAAS,GAAGD,YAAY,IAAIA,YAAY,CAAChK,OAAb,CAAqBoH,EAArB,EAAyB,EAAzB,EAA6BnH,IAA7B,EAAlC;;EAEA,YAAIgK,SAAJ,EAAe;EACblE,UAAAA,IAAI,CAACrK,YAAL,CAAkBoO,IAAlB,EAAwBG,SAAxB;EACD,SAFD,MAEO;EACLlE,UAAAA,IAAI,CAACmE,eAAL,CAAqBJ,IAArB;EACD;EACF;EACF,KAdD;EAeD;;EAED,WAASpB,2BAAT,GAA6C;EAC3C,QAAIH,eAAe,IAAI,CAACpJ,QAAQ,CAAC5C,KAAT,CAAegG,IAAf,CAAoBC,QAA5C,EAAsD;EACpD;EACD;;EAED,QAAMuH,KAAK,GAAGvQ,gBAAgB,CAAC2F,QAAQ,CAAC5C,KAAT,CAAe4H,aAAf,IAAgCnJ,SAAjC,CAA9B;EAEA+O,IAAAA,KAAK,CAAC5Q,OAAN,CAAc,UAAC4M,IAAD,EAAU;EACtB,UAAI5G,QAAQ,CAAC5C,KAAT,CAAeuG,WAAnB,EAAgC;EAC9BiD,QAAAA,IAAI,CAACrK,YAAL,CACE,eADF,EAEEyD,QAAQ,CAAC1D,KAAT,CAAe4D,SAAf,IAA4B0G,IAAI,KAAKoD,gBAAgB,EAArD,GACI,MADJ,GAEI,OAJN;EAMD,OAPD,MAOO;EACLpD,QAAAA,IAAI,CAACmE,eAAL,CAAqB,eAArB;EACD;EACF,KAXD;EAYD;;EAED,WAASC,gCAAT,GAAkD;EAChDrB,IAAAA,WAAW,GAAG9J,mBAAd,CAAkC,WAAlC,EAA+CiI,oBAA/C;EACAd,IAAAA,kBAAkB,GAAGA,kBAAkB,CAAC7M,MAAnB,CACnB,UAACwE,QAAD;EAAA,aAAcA,QAAQ,KAAKmJ,oBAA3B;EAAA,KADmB,CAArB;EAGD;;EAED,WAASmD,eAAT,CAAyBnO,KAAzB,EAA+D;EAC7D;EACA,QAAIsC,YAAY,CAACC,OAAjB,EAA0B;EACxB,UAAImI,YAAY,IAAI1K,KAAK,CAAC9D,IAAN,KAAe,WAAnC,EAAgD;EAC9C;EACD;EACF;;EAED,QAAMkS,YAAY,GACfpO,KAAK,CAACqO,YAAN,IAAsBrO,KAAK,CAACqO,YAAN,GAAqB,CAArB,CAAvB,IAAmDrO,KAAK,CAACkC,MAD3D,CAR6D;;EAY7D,QACEgB,QAAQ,CAAC5C,KAAT,CAAeuG,WAAf,IACA9E,cAAc,CAAC0H,MAAD,EAAS2E,YAAT,CAFhB,EAGE;EACA;EACD,KAjB4D;;;EAoB7D,QACE7Q,gBAAgB,CAAC2F,QAAQ,CAAC5C,KAAT,CAAe4H,aAAf,IAAgCnJ,SAAjC,CAAhB,CAA4DL,IAA5D,CAAiE,UAACU,EAAD;EAAA,aAC/D2C,cAAc,CAAC3C,EAAD,EAAKgP,YAAL,CADiD;EAAA,KAAjE,CADF,EAIE;EACA,UAAI9L,YAAY,CAACC,OAAjB,EAA0B;EACxB;EACD;;EAED,UACEW,QAAQ,CAAC1D,KAAT,CAAe4D,SAAf,IACAF,QAAQ,CAAC5C,KAAT,CAAe2H,OAAf,CAAuB5L,OAAvB,CAA+B,OAA/B,KAA2C,CAF7C,EAGE;EACA;EACD;EACF,KAfD,MAeO;EACLsQ,MAAAA,UAAU,CAAC,gBAAD,EAAmB,CAACzJ,QAAD,EAAWlD,KAAX,CAAnB,CAAV;EACD;;EAED,QAAIkD,QAAQ,CAAC5C,KAAT,CAAeqG,WAAf,KAA+B,IAAnC,EAAyC;EACvCzD,MAAAA,QAAQ,CAACuI,kBAAT;EACAvI,MAAAA,QAAQ,CAAC2I,IAAT,GAFuC;EAKvC;EACA;;EACApB,MAAAA,6BAA6B,GAAG,IAAhC;EACA3N,MAAAA,UAAU,CAAC,YAAM;EACf2N,QAAAA,6BAA6B,GAAG,KAAhC;EACD,OAFS,CAAV,CARuC;EAavC;EACA;;EACA,UAAI,CAACvH,QAAQ,CAAC1D,KAAT,CAAe+L,SAApB,EAA+B;EAC7B+C,QAAAA,mBAAmB;EACpB;EACF;EACF;;EAED,WAASC,WAAT,GAA6B;EAC3B7D,IAAAA,YAAY,GAAG,IAAf;EACD;;EAED,WAAS8D,YAAT,GAA8B;EAC5B9D,IAAAA,YAAY,GAAG,KAAf;EACD;;EAED,WAAS+D,gBAAT,GAAkC;EAChC,QAAMC,GAAG,GAAG7B,WAAW,EAAvB;EACA6B,IAAAA,GAAG,CAAC9L,gBAAJ,CAAqB,WAArB,EAAkCuL,eAAlC,EAAmD,IAAnD;EACAO,IAAAA,GAAG,CAAC9L,gBAAJ,CAAqB,UAArB,EAAiCuL,eAAjC,EAAkDnT,aAAlD;EACA0T,IAAAA,GAAG,CAAC9L,gBAAJ,CAAqB,YAArB,EAAmC4L,YAAnC,EAAiDxT,aAAjD;EACA0T,IAAAA,GAAG,CAAC9L,gBAAJ,CAAqB,WAArB,EAAkC2L,WAAlC,EAA+CvT,aAA/C;EACD;;EAED,WAASsT,mBAAT,GAAqC;EACnC,QAAMI,GAAG,GAAG7B,WAAW,EAAvB;EACA6B,IAAAA,GAAG,CAAC3L,mBAAJ,CAAwB,WAAxB,EAAqCoL,eAArC,EAAsD,IAAtD;EACAO,IAAAA,GAAG,CAAC3L,mBAAJ,CAAwB,UAAxB,EAAoCoL,eAApC,EAAqDnT,aAArD;EACA0T,IAAAA,GAAG,CAAC3L,mBAAJ,CAAwB,YAAxB,EAAsCyL,YAAtC,EAAoDxT,aAApD;EACA0T,IAAAA,GAAG,CAAC3L,mBAAJ,CAAwB,WAAxB,EAAqCwL,WAArC,EAAkDvT,aAAlD;EACD;;EAED,WAAS2T,iBAAT,CAA2BlI,QAA3B,EAA6CmI,QAA7C,EAAyE;EACvEC,IAAAA,eAAe,CAACpI,QAAD,EAAW,YAAM;EAC9B,UACE,CAACvD,QAAQ,CAAC1D,KAAT,CAAe4D,SAAhB,IACAqG,MAAM,CAAC0D,UADP,IAEA1D,MAAM,CAAC0D,UAAP,CAAkBhL,QAAlB,CAA2BsH,MAA3B,CAHF,EAIE;EACAmF,QAAAA,QAAQ;EACT;EACF,KARc,CAAf;EASD;;EAED,WAASE,gBAAT,CAA0BrI,QAA1B,EAA4CmI,QAA5C,EAAwE;EACtEC,IAAAA,eAAe,CAACpI,QAAD,EAAWmI,QAAX,CAAf;EACD;;EAED,WAASC,eAAT,CAAyBpI,QAAzB,EAA2CmI,QAA3C,EAAuE;EACrE,QAAMjN,GAAG,GAAGyL,0BAA0B,GAAGzL,GAAzC;;EAEA,aAASE,QAAT,CAAkB7B,KAAlB,EAAgD;EAC9C,UAAIA,KAAK,CAACkC,MAAN,KAAiBP,GAArB,EAA0B;EACxBD,QAAAA,2BAA2B,CAACC,GAAD,EAAM,QAAN,EAAgBE,QAAhB,CAA3B;EACA+M,QAAAA,QAAQ;EACT;EACF,KARoE;EAWrE;;;EACA,QAAInI,QAAQ,KAAK,CAAjB,EAAoB;EAClB,aAAOmI,QAAQ,EAAf;EACD;;EAEDlN,IAAAA,2BAA2B,CAACC,GAAD,EAAM,QAAN,EAAgBkJ,4BAAhB,CAA3B;EACAnJ,IAAAA,2BAA2B,CAACC,GAAD,EAAM,KAAN,EAAaE,QAAb,CAA3B;EAEAgJ,IAAAA,4BAA4B,GAAGhJ,QAA/B;EACD;;EAED,WAASkN,EAAT,CACEC,SADF,EAEEC,OAFF,EAGEC,OAHF,EAIQ;EAAA,QADNA,OACM;EADNA,MAAAA,OACM,GADuC,KACvC;EAAA;;EACN,QAAMpB,KAAK,GAAGvQ,gBAAgB,CAAC2F,QAAQ,CAAC5C,KAAT,CAAe4H,aAAf,IAAgCnJ,SAAjC,CAA9B;EACA+O,IAAAA,KAAK,CAAC5Q,OAAN,CAAc,UAAC4M,IAAD,EAAU;EACtBA,MAAAA,IAAI,CAAClH,gBAAL,CAAsBoM,SAAtB,EAAiCC,OAAjC,EAA0CC,OAA1C;EACAnE,MAAAA,SAAS,CAACpN,IAAV,CAAe;EAACmM,QAAAA,IAAI,EAAJA,IAAD;EAAOkF,QAAAA,SAAS,EAATA,SAAP;EAAkBC,QAAAA,OAAO,EAAPA,OAAlB;EAA2BC,QAAAA,OAAO,EAAPA;EAA3B,OAAf;EACD,KAHD;EAID;;EAED,WAAS1C,YAAT,GAA8B;EAC5B,QAAIO,wBAAwB,EAA5B,EAAgC;EAC9BgC,MAAAA,EAAE,CAAC,YAAD,EAAetH,SAAf,EAA0B;EAACxM,QAAAA,OAAO,EAAE;EAAV,OAA1B,CAAF;EACA8T,MAAAA,EAAE,CAAC,UAAD,EAAaI,YAAb,EAA4C;EAAClU,QAAAA,OAAO,EAAE;EAAV,OAA5C,CAAF;EACD;;EAEDkC,IAAAA,aAAa,CAAC+F,QAAQ,CAAC5C,KAAT,CAAe2H,OAAhB,CAAb,CAAsC/K,OAAtC,CAA8C,UAAC8R,SAAD,EAAe;EAC3D,UAAIA,SAAS,KAAK,QAAlB,EAA4B;EAC1B;EACD;;EAEDD,MAAAA,EAAE,CAACC,SAAD,EAAYvH,SAAZ,CAAF;;EAEA,cAAQuH,SAAR;EACE,aAAK,YAAL;EACED,UAAAA,EAAE,CAAC,YAAD,EAAeI,YAAf,CAAF;EACA;;EACF,aAAK,OAAL;EACEJ,UAAAA,EAAE,CAACxL,MAAM,GAAG,UAAH,GAAgB,MAAvB,EAA+B6L,gBAA/B,CAAF;EACA;;EACF,aAAK,SAAL;EACEL,UAAAA,EAAE,CAAC,UAAD,EAAaK,gBAAb,CAAF;EACA;EATJ;EAWD,KAlBD;EAmBD;;EAED,WAASC,eAAT,GAAiC;EAC/BtE,IAAAA,SAAS,CAAC7N,OAAV,CAAkB,gBAAyD;EAAA,UAAvD4M,IAAuD,QAAvDA,IAAuD;EAAA,UAAjDkF,SAAiD,QAAjDA,SAAiD;EAAA,UAAtCC,OAAsC,QAAtCA,OAAsC;EAAA,UAA7BC,OAA6B,QAA7BA,OAA6B;EACzEpF,MAAAA,IAAI,CAAC/G,mBAAL,CAAyBiM,SAAzB,EAAoCC,OAApC,EAA6CC,OAA7C;EACD,KAFD;EAGAnE,IAAAA,SAAS,GAAG,EAAZ;EACD;;EAED,WAAStD,SAAT,CAAmBzH,KAAnB,EAAuC;EAAA;;EACrC,QAAIsP,uBAAuB,GAAG,KAA9B;;EAEA,QACE,CAACpM,QAAQ,CAAC1D,KAAT,CAAe6L,SAAhB,IACAkE,sBAAsB,CAACvP,KAAD,CADtB,IAEAyK,6BAHF,EAIE;EACA;EACD;;EAED,QAAM+E,UAAU,GAAG,sBAAA5E,gBAAgB,SAAhB,8BAAkB1O,IAAlB,MAA2B,OAA9C;EAEA0O,IAAAA,gBAAgB,GAAG5K,KAAnB;EACAkL,IAAAA,aAAa,GAAGlL,KAAK,CAACkL,aAAtB;EAEAuB,IAAAA,2BAA2B;;EAE3B,QAAI,CAACvJ,QAAQ,CAAC1D,KAAT,CAAe4D,SAAhB,IAA6BxE,YAAY,CAACoB,KAAD,CAA7C,EAAsD;EACpD;EACA;EACA;EACA;EACAkK,MAAAA,kBAAkB,CAAChN,OAAnB,CAA2B,UAAC2E,QAAD;EAAA,eAAcA,QAAQ,CAAC7B,KAAD,CAAtB;EAAA,OAA3B;EACD,KAxBoC;;;EA2BrC,QACEA,KAAK,CAAC9D,IAAN,KAAe,OAAf,KACCgH,QAAQ,CAAC5C,KAAT,CAAe2H,OAAf,CAAuB5L,OAAvB,CAA+B,YAA/B,IAA+C,CAA/C,IACCmO,kBAFF,KAGAtH,QAAQ,CAAC5C,KAAT,CAAeqG,WAAf,KAA+B,KAH/B,IAIAzD,QAAQ,CAAC1D,KAAT,CAAe4D,SALjB,EAME;EACAkM,MAAAA,uBAAuB,GAAG,IAA1B;EACD,KARD,MAQO;EACL1C,MAAAA,YAAY,CAAC5M,KAAD,CAAZ;EACD;;EAED,QAAIA,KAAK,CAAC9D,IAAN,KAAe,OAAnB,EAA4B;EAC1BsO,MAAAA,kBAAkB,GAAG,CAAC8E,uBAAtB;EACD;;EAED,QAAIA,uBAAuB,IAAI,CAACE,UAAhC,EAA4C;EAC1CC,MAAAA,YAAY,CAACzP,KAAD,CAAZ;EACD;EACF;;EAED,WAASiL,WAAT,CAAqBjL,KAArB,EAA8C;EAC5C,QAAMkC,MAAM,GAAGlC,KAAK,CAACkC,MAArB;EACA,QAAMwN,6BAA6B,GACjCxC,gBAAgB,GAAG/K,QAAnB,CAA4BD,MAA5B,KAAuCuH,MAAM,CAACtH,QAAP,CAAgBD,MAAhB,CADzC;;EAGA,QAAIlC,KAAK,CAAC9D,IAAN,KAAe,WAAf,IAA8BwT,6BAAlC,EAAiE;EAC/D;EACD;;EAED,QAAM3P,cAAc,GAAG4P,mBAAmB,GACvCnS,MADoB,CACbiM,MADa,EAEpB4C,GAFoB,CAEhB,UAAC5C,MAAD,EAAY;EAAA;;EACf,UAAMvG,QAAQ,GAAGuG,MAAM,CAAC3K,MAAxB;EACA,UAAMU,KAAK,4BAAG0D,QAAQ,CAACkI,cAAZ,qBAAG,sBAAyB5L,KAAvC;;EAEA,UAAIA,KAAJ,EAAW;EACT,eAAO;EACLY,UAAAA,UAAU,EAAEqJ,MAAM,CAACmG,qBAAP,EADP;EAELvP,UAAAA,WAAW,EAAEb,KAFR;EAGLc,UAAAA,KAAK,EAALA;EAHK,SAAP;EAKD;;EAED,aAAO,IAAP;EACD,KAfoB,EAgBpBjD,MAhBoB,CAgBbC,OAhBa,CAAvB;;EAkBA,QAAIwC,gCAAgC,CAACC,cAAD,EAAiBC,KAAjB,CAApC,EAA6D;EAC3DkO,MAAAA,gCAAgC;EAChCuB,MAAAA,YAAY,CAACzP,KAAD,CAAZ;EACD;EACF;;EAED,WAASmP,YAAT,CAAsBnP,KAAtB,EAA+C;EAC7C,QAAM6P,UAAU,GACdN,sBAAsB,CAACvP,KAAD,CAAtB,IACCkD,QAAQ,CAAC5C,KAAT,CAAe2H,OAAf,CAAuB5L,OAAvB,CAA+B,OAA/B,KAA2C,CAA3C,IAAgDmO,kBAFnD;;EAIA,QAAIqF,UAAJ,EAAgB;EACd;EACD;;EAED,QAAI3M,QAAQ,CAAC5C,KAAT,CAAeuG,WAAnB,EAAgC;EAC9B3D,MAAAA,QAAQ,CAAC4I,qBAAT,CAA+B9L,KAA/B;EACA;EACD;;EAEDyP,IAAAA,YAAY,CAACzP,KAAD,CAAZ;EACD;;EAED,WAASoP,gBAAT,CAA0BpP,KAA1B,EAAmD;EACjD,QACEkD,QAAQ,CAAC5C,KAAT,CAAe2H,OAAf,CAAuB5L,OAAvB,CAA+B,SAA/B,IAA4C,CAA5C,IACA2D,KAAK,CAACkC,MAAN,KAAiBgL,gBAAgB,EAFnC,EAGE;EACA;EACD,KANgD;;;EASjD,QACEhK,QAAQ,CAAC5C,KAAT,CAAeuG,WAAf,IACA7G,KAAK,CAAC8P,aADN,IAEArG,MAAM,CAACtH,QAAP,CAAgBnC,KAAK,CAAC8P,aAAtB,CAHF,EAIE;EACA;EACD;;EAEDL,IAAAA,YAAY,CAACzP,KAAD,CAAZ;EACD;;EAED,WAASuP,sBAAT,CAAgCvP,KAAhC,EAAuD;EACrD,WAAOsC,YAAY,CAACC,OAAb,GACHwK,wBAAwB,OAAO/M,KAAK,CAAC9D,IAAN,CAAWG,OAAX,CAAmB,OAAnB,KAA+B,CAD3D,GAEH,KAFJ;EAGD;;EAED,WAAS0T,oBAAT,GAAsC;EACpCC,IAAAA,qBAAqB;EAErB,2BAMI9M,QAAQ,CAAC5C,KANb;EAAA,QACEuH,aADF,oBACEA,aADF;EAAA,QAEE9J,SAFF,oBAEEA,SAFF;EAAA,QAGE4C,MAHF,oBAGEA,MAHF;EAAA,QAIE+F,sBAJF,oBAIEA,sBAJF;EAAA,QAKEK,cALF,oBAKEA,cALF;EAQA,QAAMlB,KAAK,GAAGmH,oBAAoB,KAAKxD,WAAW,CAACC,MAAD,CAAX,CAAoB5D,KAAzB,GAAiC,IAAnE;EAEA,QAAMoK,iBAAiB,GAAGvJ,sBAAsB,GAC5C;EACEkJ,MAAAA,qBAAqB,EAAElJ,sBADzB;EAEEwJ,MAAAA,cAAc,EACZxJ,sBAAsB,CAACwJ,cAAvB,IAAyChD,gBAAgB;EAH7D,KAD4C,GAM5CnO,SANJ;EAQA,QAAMoR,aAA2D,GAAG;EAClEzH,MAAAA,IAAI,EAAE,SAD4D;EAElE0H,MAAAA,OAAO,EAAE,IAFyD;EAGlEC,MAAAA,KAAK,EAAE,aAH2D;EAIlEC,MAAAA,QAAQ,EAAE,CAAC,eAAD,CAJwD;EAKlE7T,MAAAA,EALkE,qBAKtD;EAAA,YAAR+C,KAAQ,SAARA,KAAQ;;EACV,YAAIwN,oBAAoB,EAAxB,EAA4B;EAC1B,sCAAcI,0BAA0B,EAAxC;EAAA,cAAOzL,GAAP,yBAAOA,GAAP;;EAEA,WAAC,WAAD,EAAc,kBAAd,EAAkC,SAAlC,EAA6CzE,OAA7C,CAAqD,UAAC2Q,IAAD,EAAU;EAC7D,gBAAIA,IAAI,KAAK,WAAb,EAA0B;EACxBlM,cAAAA,GAAG,CAAClC,YAAJ,CAAiB,gBAAjB,EAAmCD,KAAK,CAACzB,SAAzC;EACD,aAFD,MAEO;EACL,kBAAIyB,KAAK,CAAC+Q,UAAN,CAAiB9G,MAAjB,kBAAuCoE,IAAvC,CAAJ,EAAoD;EAClDlM,gBAAAA,GAAG,CAAClC,YAAJ,WAAyBoO,IAAzB,EAAiC,EAAjC;EACD,eAFD,MAEO;EACLlM,gBAAAA,GAAG,CAACsM,eAAJ,WAA4BJ,IAA5B;EACD;EACF;EACF,WAVD;EAYArO,UAAAA,KAAK,CAAC+Q,UAAN,CAAiB9G,MAAjB,GAA0B,EAA1B;EACD;EACF;EAvBiE,KAApE;EA6BA,QAAM+G,SAAmC,GAAG,CAC1C;EACE9H,MAAAA,IAAI,EAAE,QADR;EAEEwG,MAAAA,OAAO,EAAE;EACPvO,QAAAA,MAAM,EAANA;EADO;EAFX,KAD0C,EAO1C;EACE+H,MAAAA,IAAI,EAAE,iBADR;EAEEwG,MAAAA,OAAO,EAAE;EACPuB,QAAAA,OAAO,EAAE;EACP5P,UAAAA,GAAG,EAAE,CADE;EAEPG,UAAAA,MAAM,EAAE,CAFD;EAGPE,UAAAA,IAAI,EAAE,CAHC;EAIPG,UAAAA,KAAK,EAAE;EAJA;EADF;EAFX,KAP0C,EAkB1C;EACEqH,MAAAA,IAAI,EAAE,MADR;EAEEwG,MAAAA,OAAO,EAAE;EACPuB,QAAAA,OAAO,EAAE;EADF;EAFX,KAlB0C,EAwB1C;EACE/H,MAAAA,IAAI,EAAE,eADR;EAEEwG,MAAAA,OAAO,EAAE;EACPwB,QAAAA,QAAQ,EAAE,CAAC3J;EADJ;EAFX,KAxB0C,EA8B1CoJ,aA9B0C,CAA5C;;EAiCA,QAAInD,oBAAoB,MAAMnH,KAA9B,EAAqC;EACnC2K,MAAAA,SAAS,CAAC7S,IAAV,CAAe;EACb+K,QAAAA,IAAI,EAAE,OADO;EAEbwG,QAAAA,OAAO,EAAE;EACPtP,UAAAA,OAAO,EAAEiG,KADF;EAEP4K,UAAAA,OAAO,EAAE;EAFF;EAFI,OAAf;EAOD;;EAEDD,IAAAA,SAAS,CAAC7S,IAAV,OAAA6S,SAAS,EAAU,CAAA3I,aAAa,QAAb,YAAAA,aAAa,CAAE2I,SAAf,KAA4B,EAAtC,CAAT;EAEAtN,IAAAA,QAAQ,CAACkI,cAAT,GAA0BuF,iBAAY,CACpCV,iBADoC,EAEpCxG,MAFoC,oBAI/B5B,aAJ+B;EAKlC9J,MAAAA,SAAS,EAATA,SALkC;EAMlC+M,MAAAA,aAAa,EAAbA,aANkC;EAOlC0F,MAAAA,SAAS,EAATA;EAPkC,OAAtC;EAUD;;EAED,WAASR,qBAAT,GAAuC;EACrC,QAAI9M,QAAQ,CAACkI,cAAb,EAA6B;EAC3BlI,MAAAA,QAAQ,CAACkI,cAAT,CAAwBc,OAAxB;EACAhJ,MAAAA,QAAQ,CAACkI,cAAT,GAA0B,IAA1B;EACD;EACF;;EAED,WAASwF,KAAT,GAAuB;EACrB,QAAOvK,QAAP,GAAmBnD,QAAQ,CAAC5C,KAA5B,CAAO+F,QAAP;EAEA,QAAI8G,UAAJ,CAHqB;EAMrB;EACA;EACA;EACA;;EACA,QAAMrD,IAAI,GAAGoD,gBAAgB,EAA7B;;EAEA,QACGhK,QAAQ,CAAC5C,KAAT,CAAeuG,WAAf,IAA8BR,QAAQ,KAAKlL,uBAA5C,IACAkL,QAAQ,KAAK,QAFf,EAGE;EACA8G,MAAAA,UAAU,GAAGrD,IAAI,CAACqD,UAAlB;EACD,KALD,MAKO;EACLA,MAAAA,UAAU,GAAG7Q,sBAAsB,CAAC+J,QAAD,EAAW,CAACyD,IAAD,CAAX,CAAnC;EACD,KAnBoB;EAsBrB;;;EACA,QAAI,CAACqD,UAAU,CAAChL,QAAX,CAAoBsH,MAApB,CAAL,EAAkC;EAChC0D,MAAAA,UAAU,CAAC0D,WAAX,CAAuBpH,MAAvB;EACD;;EAEDvG,IAAAA,QAAQ,CAAC1D,KAAT,CAAe+L,SAAf,GAA2B,IAA3B;EAEAwE,IAAAA,oBAAoB;EAEpB;;EACA,IAAa;EACX;EACAxL,MAAAA,QAAQ,CACNrB,QAAQ,CAAC5C,KAAT,CAAeuG,WAAf,IACER,QAAQ,KAAKD,YAAY,CAACC,QAD5B,IAEEyD,IAAI,CAACgH,kBAAL,KAA4BrH,MAHxB,EAIN,CACE,8DADF,EAEE,mEAFF,EAGE,0BAHF,EAIE,MAJF,EAKE,kEALF,EAME,mDANF,EAOE,MAPF,EAQE,oEARF,EASE,6DATF,EAUE,sBAVF,EAWE,MAXF,EAYE,wEAZF,EAaE9F,IAbF,CAaO,GAbP,CAJM,CAAR;EAmBD;EACF;;EAED,WAASgM,mBAAT,GAAgD;EAC9C,WAAO3R,SAAS,CACdyL,MAAM,CAACxK,gBAAP,CAAwB,mBAAxB,CADc,CAAhB;EAGD;;EAED,WAAS2N,YAAT,CAAsB5M,KAAtB,EAA2C;EACzCkD,IAAAA,QAAQ,CAACuI,kBAAT;;EAEA,QAAIzL,KAAJ,EAAW;EACT2M,MAAAA,UAAU,CAAC,WAAD,EAAc,CAACzJ,QAAD,EAAWlD,KAAX,CAAd,CAAV;EACD;;EAEDyO,IAAAA,gBAAgB;EAEhB,QAAIjI,KAAK,GAAG6G,QAAQ,CAAC,IAAD,CAApB;;EACA,gCAAiCP,0BAA0B,EAA3D;EAAA,QAAOiE,UAAP;EAAA,QAAmBC,UAAnB;;EAEA,QAAI1O,YAAY,CAACC,OAAb,IAAwBwO,UAAU,KAAK,MAAvC,IAAiDC,UAArD,EAAiE;EAC/DxK,MAAAA,KAAK,GAAGwK,UAAR;EACD;;EAED,QAAIxK,KAAJ,EAAW;EACT6D,MAAAA,WAAW,GAAGvN,UAAU,CAAC,YAAM;EAC7BoG,QAAAA,QAAQ,CAAC0I,IAAT;EACD,OAFuB,EAErBpF,KAFqB,CAAxB;EAGD,KAJD,MAIO;EACLtD,MAAAA,QAAQ,CAAC0I,IAAT;EACD;EACF;;EAED,WAAS6D,YAAT,CAAsBzP,KAAtB,EAA0C;EACxCkD,IAAAA,QAAQ,CAACuI,kBAAT;EAEAkB,IAAAA,UAAU,CAAC,aAAD,EAAgB,CAACzJ,QAAD,EAAWlD,KAAX,CAAhB,CAAV;;EAEA,QAAI,CAACkD,QAAQ,CAAC1D,KAAT,CAAe4D,SAApB,EAA+B;EAC7BkL,MAAAA,mBAAmB;EAEnB;EACD,KATuC;EAYxC;EACA;EACA;;;EACA,QACEpL,QAAQ,CAAC5C,KAAT,CAAe2H,OAAf,CAAuB5L,OAAvB,CAA+B,YAA/B,KAAgD,CAAhD,IACA6G,QAAQ,CAAC5C,KAAT,CAAe2H,OAAf,CAAuB5L,OAAvB,CAA+B,OAA/B,KAA2C,CAD3C,IAEA,CAAC,YAAD,EAAe,WAAf,EAA4BA,OAA5B,CAAoC2D,KAAK,CAAC9D,IAA1C,KAAmD,CAFnD,IAGAsO,kBAJF,EAKE;EACA;EACD;;EAED,QAAMhE,KAAK,GAAG6G,QAAQ,CAAC,KAAD,CAAtB;;EAEA,QAAI7G,KAAJ,EAAW;EACT8D,MAAAA,WAAW,GAAGxN,UAAU,CAAC,YAAM;EAC7B,YAAIoG,QAAQ,CAAC1D,KAAT,CAAe4D,SAAnB,EAA8B;EAC5BF,UAAAA,QAAQ,CAAC2I,IAAT;EACD;EACF,OAJuB,EAIrBrF,KAJqB,CAAxB;EAKD,KAND,MAMO;EACL;EACA;EACA+D,MAAAA,0BAA0B,GAAG0G,qBAAqB,CAAC,YAAM;EACvD/N,QAAAA,QAAQ,CAAC2I,IAAT;EACD,OAFiD,CAAlD;EAGD;EACF,GA3wBS;EA8wBV;EACA;;;EACA,WAASE,MAAT,GAAwB;EACtB7I,IAAAA,QAAQ,CAAC1D,KAAT,CAAe6L,SAAf,GAA2B,IAA3B;EACD;;EAED,WAASW,OAAT,GAAyB;EACvB;EACA;EACA9I,IAAAA,QAAQ,CAAC2I,IAAT;EACA3I,IAAAA,QAAQ,CAAC1D,KAAT,CAAe6L,SAAf,GAA2B,KAA3B;EACD;;EAED,WAASI,kBAAT,GAAoC;EAClC5O,IAAAA,YAAY,CAACwN,WAAD,CAAZ;EACAxN,IAAAA,YAAY,CAACyN,WAAD,CAAZ;EACA4G,IAAAA,oBAAoB,CAAC3G,0BAAD,CAApB;EACD;;EAED,WAASmB,QAAT,CAAkBrD,YAAlB,EAAsD;EACpD;EACA,IAAa;EACX9D,MAAAA,QAAQ,CAACrB,QAAQ,CAAC1D,KAAT,CAAe8L,WAAhB,EAA6B7H,uBAAuB,CAAC,UAAD,CAApD,CAAR;EACD;;EAED,QAAIP,QAAQ,CAAC1D,KAAT,CAAe8L,WAAnB,EAAgC;EAC9B;EACD;;EAEDqB,IAAAA,UAAU,CAAC,gBAAD,EAAmB,CAACzJ,QAAD,EAAWmF,YAAX,CAAnB,CAAV;EAEAgH,IAAAA,eAAe;EAEf,QAAM8B,SAAS,GAAGjO,QAAQ,CAAC5C,KAA3B;EACA,QAAM8Q,SAAS,GAAGlI,aAAa,CAACnK,SAAD,oBAC1BoS,SAD0B,EAE1BjT,oBAAoB,CAACmK,YAAD,CAFM;EAG7BzB,MAAAA,gBAAgB,EAAE;EAHW,OAA/B;EAMA1D,IAAAA,QAAQ,CAAC5C,KAAT,GAAiB8Q,SAAjB;EAEA5E,IAAAA,YAAY;;EAEZ,QAAI2E,SAAS,CAACrK,mBAAV,KAAkCsK,SAAS,CAACtK,mBAAhD,EAAqE;EACnEoH,MAAAA,gCAAgC;EAChClD,MAAAA,oBAAoB,GAAGxO,QAAQ,CAC7ByO,WAD6B,EAE7BmG,SAAS,CAACtK,mBAFmB,CAA/B;EAID,KA/BmD;;;EAkCpD,QAAIqK,SAAS,CAACjJ,aAAV,IAA2B,CAACkJ,SAAS,CAAClJ,aAA1C,EAAyD;EACvD3K,MAAAA,gBAAgB,CAAC4T,SAAS,CAACjJ,aAAX,CAAhB,CAA0ChL,OAA1C,CAAkD,UAAC4M,IAAD,EAAU;EAC1DA,QAAAA,IAAI,CAACmE,eAAL,CAAqB,eAArB;EACD,OAFD;EAGD,KAJD,MAIO,IAAImD,SAAS,CAAClJ,aAAd,EAA6B;EAClCnJ,MAAAA,SAAS,CAACkP,eAAV,CAA0B,eAA1B;EACD;;EAEDxB,IAAAA,2BAA2B;EAC3BC,IAAAA,YAAY;;EAEZ,QAAIP,QAAJ,EAAc;EACZA,MAAAA,QAAQ,CAACgF,SAAD,EAAYC,SAAZ,CAAR;EACD;;EAED,QAAIlO,QAAQ,CAACkI,cAAb,EAA6B;EAC3B2E,MAAAA,oBAAoB,GADO;EAI3B;EACA;EACA;;EACAJ,MAAAA,mBAAmB,GAAGzS,OAAtB,CAA8B,UAACmU,YAAD,EAAkB;EAC9C;EACA;EACAJ,QAAAA,qBAAqB,CAACI,YAAY,CAACvS,MAAb,CAAqBsM,cAArB,CAAqCkG,WAAtC,CAArB;EACD,OAJD;EAKD;;EAED3E,IAAAA,UAAU,CAAC,eAAD,EAAkB,CAACzJ,QAAD,EAAWmF,YAAX,CAAlB,CAAV;EACD;;EAED,WAASsD,UAAT,CAAoB7F,OAApB,EAA4C;EAC1C5C,IAAAA,QAAQ,CAACwI,QAAT,CAAkB;EAAC5F,MAAAA,OAAO,EAAPA;EAAD,KAAlB;EACD;;EAED,WAAS8F,IAAT,GAAsB;EACpB;EACA,IAAa;EACXrH,MAAAA,QAAQ,CAACrB,QAAQ,CAAC1D,KAAT,CAAe8L,WAAhB,EAA6B7H,uBAAuB,CAAC,MAAD,CAApD,CAAR;EACD,KAJmB;;;EAOpB,QAAM8N,gBAAgB,GAAGrO,QAAQ,CAAC1D,KAAT,CAAe4D,SAAxC;EACA,QAAMkI,WAAW,GAAGpI,QAAQ,CAAC1D,KAAT,CAAe8L,WAAnC;EACA,QAAMkG,UAAU,GAAG,CAACtO,QAAQ,CAAC1D,KAAT,CAAe6L,SAAnC;EACA,QAAMoG,uBAAuB,GAC3BnP,YAAY,CAACC,OAAb,IAAwB,CAACW,QAAQ,CAAC5C,KAAT,CAAe0H,KAD1C;EAEA,QAAMvB,QAAQ,GAAG/K,uBAAuB,CACtCwH,QAAQ,CAAC5C,KAAT,CAAemG,QADuB,EAEtC,CAFsC,EAGtCL,YAAY,CAACK,QAHyB,CAAxC;;EAMA,QACE8K,gBAAgB,IAChBjG,WADA,IAEAkG,UAFA,IAGAC,uBAJF,EAKE;EACA;EACD,KAzBmB;EA4BpB;EACA;;;EACA,QAAIvE,gBAAgB,GAAGX,YAAnB,CAAgC,UAAhC,CAAJ,EAAiD;EAC/C;EACD;;EAEDI,IAAAA,UAAU,CAAC,QAAD,EAAW,CAACzJ,QAAD,CAAX,EAAuB,KAAvB,CAAV;;EACA,QAAIA,QAAQ,CAAC5C,KAAT,CAAeiH,MAAf,CAAsBrE,QAAtB,MAAoC,KAAxC,EAA+C;EAC7C;EACD;;EAEDA,IAAAA,QAAQ,CAAC1D,KAAT,CAAe4D,SAAf,GAA2B,IAA3B;;EAEA,QAAI4J,oBAAoB,EAAxB,EAA4B;EAC1BvD,MAAAA,MAAM,CAACpK,KAAP,CAAaqS,UAAb,GAA0B,SAA1B;EACD;;EAEDhF,IAAAA,YAAY;EACZ+B,IAAAA,gBAAgB;;EAEhB,QAAI,CAACvL,QAAQ,CAAC1D,KAAT,CAAe+L,SAApB,EAA+B;EAC7B9B,MAAAA,MAAM,CAACpK,KAAP,CAAasS,UAAb,GAA0B,MAA1B;EACD,KAlDmB;EAqDpB;;;EACA,QAAI3E,oBAAoB,EAAxB,EAA4B;EAC1B,mCAAuBI,0BAA0B,EAAjD;EAAA,UAAOzL,GAAP,0BAAOA,GAAP;EAAA,UAAYmE,OAAZ,0BAAYA,OAAZ;;EACA5G,MAAAA,qBAAqB,CAAC,CAACyC,GAAD,EAAMmE,OAAN,CAAD,EAAiB,CAAjB,CAArB;EACD;;EAEDgF,IAAAA,aAAa,GAAG,yBAAY;EAAA;;EAC1B,UAAI,CAAC5H,QAAQ,CAAC1D,KAAT,CAAe4D,SAAhB,IAA6BuH,mBAAjC,EAAsD;EACpD;EACD;;EAEDA,MAAAA,mBAAmB,GAAG,IAAtB,CAL0B;;EAQ1B,WAAKlB,MAAM,CAACmI,YAAZ;EAEAnI,MAAAA,MAAM,CAACpK,KAAP,CAAasS,UAAb,GAA0BzO,QAAQ,CAAC5C,KAAT,CAAeyG,cAAzC;;EAEA,UAAIiG,oBAAoB,MAAM9J,QAAQ,CAAC5C,KAAT,CAAesF,SAA7C,EAAwD;EACtD,qCAAuBwH,0BAA0B,EAAjD;EAAA,YAAOzL,IAAP,0BAAOA,GAAP;EAAA,YAAYmE,QAAZ,0BAAYA,OAAZ;;EACA5G,QAAAA,qBAAqB,CAAC,CAACyC,IAAD,EAAMmE,QAAN,CAAD,EAAiBW,QAAjB,CAArB;EACAlH,QAAAA,kBAAkB,CAAC,CAACoC,IAAD,EAAMmE,QAAN,CAAD,EAAiB,SAAjB,CAAlB;EACD;;EAED8H,MAAAA,0BAA0B;EAC1BnB,MAAAA,2BAA2B;EAE3BhP,MAAAA,YAAY,CAAC0M,gBAAD,EAAmBjH,QAAnB,CAAZ,CArB0B;EAwB1B;;EACA,gCAAAA,QAAQ,CAACkI,cAAT,4CAAyBkG,WAAzB;EAEA3E,MAAAA,UAAU,CAAC,SAAD,EAAY,CAACzJ,QAAD,CAAZ,CAAV;;EAEA,UAAIA,QAAQ,CAAC5C,KAAT,CAAesF,SAAf,IAA4BoH,oBAAoB,EAApD,EAAwD;EACtD8B,QAAAA,gBAAgB,CAACrI,QAAD,EAAW,YAAM;EAC/BvD,UAAAA,QAAQ,CAAC1D,KAAT,CAAegM,OAAf,GAAyB,IAAzB;EACAmB,UAAAA,UAAU,CAAC,SAAD,EAAY,CAACzJ,QAAD,CAAZ,CAAV;EACD,SAHe,CAAhB;EAID;EACF,KAnCD;;EAqCA0N,IAAAA,KAAK;EACN;;EAED,WAAS/E,IAAT,GAAsB;EACpB;EACA,IAAa;EACXtH,MAAAA,QAAQ,CAACrB,QAAQ,CAAC1D,KAAT,CAAe8L,WAAhB,EAA6B7H,uBAAuB,CAAC,MAAD,CAApD,CAAR;EACD,KAJmB;;;EAOpB,QAAMoO,eAAe,GAAG,CAAC3O,QAAQ,CAAC1D,KAAT,CAAe4D,SAAxC;EACA,QAAMkI,WAAW,GAAGpI,QAAQ,CAAC1D,KAAT,CAAe8L,WAAnC;EACA,QAAMkG,UAAU,GAAG,CAACtO,QAAQ,CAAC1D,KAAT,CAAe6L,SAAnC;EACA,QAAM5E,QAAQ,GAAG/K,uBAAuB,CACtCwH,QAAQ,CAAC5C,KAAT,CAAemG,QADuB,EAEtC,CAFsC,EAGtCL,YAAY,CAACK,QAHyB,CAAxC;;EAMA,QAAIoL,eAAe,IAAIvG,WAAnB,IAAkCkG,UAAtC,EAAkD;EAChD;EACD;;EAED7E,IAAAA,UAAU,CAAC,QAAD,EAAW,CAACzJ,QAAD,CAAX,EAAuB,KAAvB,CAAV;;EACA,QAAIA,QAAQ,CAAC5C,KAAT,CAAe+G,MAAf,CAAsBnE,QAAtB,MAAoC,KAAxC,EAA+C;EAC7C;EACD;;EAEDA,IAAAA,QAAQ,CAAC1D,KAAT,CAAe4D,SAAf,GAA2B,KAA3B;EACAF,IAAAA,QAAQ,CAAC1D,KAAT,CAAegM,OAAf,GAAyB,KAAzB;EACAb,IAAAA,mBAAmB,GAAG,KAAtB;EACAH,IAAAA,kBAAkB,GAAG,KAArB;;EAEA,QAAIwC,oBAAoB,EAAxB,EAA4B;EAC1BvD,MAAAA,MAAM,CAACpK,KAAP,CAAaqS,UAAb,GAA0B,QAA1B;EACD;;EAEDxD,IAAAA,gCAAgC;EAChCI,IAAAA,mBAAmB;EACnB5B,IAAAA,YAAY,CAAC,IAAD,CAAZ;;EAEA,QAAIM,oBAAoB,EAAxB,EAA4B;EAC1B,mCAAuBI,0BAA0B,EAAjD;EAAA,UAAOzL,GAAP,0BAAOA,GAAP;EAAA,UAAYmE,OAAZ,0BAAYA,OAAZ;;EAEA,UAAI5C,QAAQ,CAAC5C,KAAT,CAAesF,SAAnB,EAA8B;EAC5B1G,QAAAA,qBAAqB,CAAC,CAACyC,GAAD,EAAMmE,OAAN,CAAD,EAAiBW,QAAjB,CAArB;EACAlH,QAAAA,kBAAkB,CAAC,CAACoC,GAAD,EAAMmE,OAAN,CAAD,EAAiB,QAAjB,CAAlB;EACD;EACF;;EAED8H,IAAAA,0BAA0B;EAC1BnB,IAAAA,2BAA2B;;EAE3B,QAAIvJ,QAAQ,CAAC5C,KAAT,CAAesF,SAAnB,EAA8B;EAC5B,UAAIoH,oBAAoB,EAAxB,EAA4B;EAC1B2B,QAAAA,iBAAiB,CAAClI,QAAD,EAAWvD,QAAQ,CAAC+I,OAApB,CAAjB;EACD;EACF,KAJD,MAIO;EACL/I,MAAAA,QAAQ,CAAC+I,OAAT;EACD;EACF;;EAED,WAASH,qBAAT,CAA+B9L,KAA/B,EAAwD;EACtD;EACA,IAAa;EACXuE,MAAAA,QAAQ,CACNrB,QAAQ,CAAC1D,KAAT,CAAe8L,WADT,EAEN7H,uBAAuB,CAAC,uBAAD,CAFjB,CAAR;EAID;;EAEDoJ,IAAAA,WAAW,GAAGjK,gBAAd,CAA+B,WAA/B,EAA4CoI,oBAA5C;EACAvN,IAAAA,YAAY,CAACyM,kBAAD,EAAqBc,oBAArB,CAAZ;EACAA,IAAAA,oBAAoB,CAAChL,KAAD,CAApB;EACD;;EAED,WAASiM,OAAT,GAAyB;EACvB;EACA,IAAa;EACX1H,MAAAA,QAAQ,CAACrB,QAAQ,CAAC1D,KAAT,CAAe8L,WAAhB,EAA6B7H,uBAAuB,CAAC,SAAD,CAApD,CAAR;EACD;;EAED,QAAIP,QAAQ,CAAC1D,KAAT,CAAe4D,SAAnB,EAA8B;EAC5BF,MAAAA,QAAQ,CAAC2I,IAAT;EACD;;EAED,QAAI,CAAC3I,QAAQ,CAAC1D,KAAT,CAAe+L,SAApB,EAA+B;EAC7B;EACD;;EAEDyE,IAAAA,qBAAqB,GAdE;EAiBvB;EACA;;EACAL,IAAAA,mBAAmB,GAAGzS,OAAtB,CAA8B,UAACmU,YAAD,EAAkB;EAC9CA,MAAAA,YAAY,CAACvS,MAAb,CAAqBmN,OAArB;EACD,KAFD;;EAIA,QAAIxC,MAAM,CAAC0D,UAAX,EAAuB;EACrB1D,MAAAA,MAAM,CAAC0D,UAAP,CAAkB2E,WAAlB,CAA8BrI,MAA9B;EACD;;EAEDU,IAAAA,gBAAgB,GAAGA,gBAAgB,CAAC9M,MAAjB,CAAwB,UAAC0U,CAAD;EAAA,aAAOA,CAAC,KAAK7O,QAAb;EAAA,KAAxB,CAAnB;EAEAA,IAAAA,QAAQ,CAAC1D,KAAT,CAAe+L,SAAf,GAA2B,KAA3B;EACAoB,IAAAA,UAAU,CAAC,UAAD,EAAa,CAACzJ,QAAD,CAAb,CAAV;EACD;;EAED,WAASgJ,OAAT,GAAyB;EACvB;EACA,IAAa;EACX3H,MAAAA,QAAQ,CAACrB,QAAQ,CAAC1D,KAAT,CAAe8L,WAAhB,EAA6B7H,uBAAuB,CAAC,SAAD,CAApD,CAAR;EACD;;EAED,QAAIP,QAAQ,CAAC1D,KAAT,CAAe8L,WAAnB,EAAgC;EAC9B;EACD;;EAEDpI,IAAAA,QAAQ,CAACuI,kBAAT;EACAvI,IAAAA,QAAQ,CAAC+I,OAAT;EAEAoD,IAAAA,eAAe;EAEf,WAAOtQ,SAAS,CAACD,MAAjB;EAEAoE,IAAAA,QAAQ,CAAC1D,KAAT,CAAe8L,WAAf,GAA6B,IAA7B;EAEAqB,IAAAA,UAAU,CAAC,WAAD,EAAc,CAACzJ,QAAD,CAAd,CAAV;EACD;EACF;;EC/mCD,SAAS8O,KAAT,CACEhN,OADF,EAEEiN,aAFF,EAGyB;EAAA,MADvBA,aACuB;EADvBA,IAAAA,aACuB,GADS,EACT;EAAA;;EACvB,MAAMrK,OAAO,GAAGxB,YAAY,CAACwB,OAAb,CAAqBpK,MAArB,CAA4ByU,aAAa,CAACrK,OAAd,IAAyB,EAArD,CAAhB;EAEA;;EACA,EAAa;EACX7C,IAAAA,eAAe,CAACC,OAAD,CAAf;EACAsD,IAAAA,aAAa,CAAC2J,aAAD,EAAgBrK,OAAhB,CAAb;EACD;;EAEDvE,EAAAA,wBAAwB;EAExB,MAAMmF,WAA2B,qBAAOyJ,aAAP;EAAsBrK,IAAAA,OAAO,EAAPA;EAAtB,IAAjC;EAEA,MAAMsK,QAAQ,GAAGlT,kBAAkB,CAACgG,OAAD,CAAnC;EAEA;;EACA,EAAa;EACX,QAAMmN,sBAAsB,GAAG1T,SAAS,CAAC+J,WAAW,CAAC1C,OAAb,CAAxC;EACA,QAAMsM,6BAA6B,GAAGF,QAAQ,CAAC3I,MAAT,GAAkB,CAAxD;EACAhF,IAAAA,QAAQ,CACN4N,sBAAsB,IAAIC,6BADpB,EAEN,CACE,oEADF,EAEE,mEAFF,EAGE,mEAHF,EAIE,MAJF,EAKE,qEALF,EAME,kDANF,EAOE,MAPF,EAQE,iCARF,EASE,2CATF,EAUEzO,IAVF,CAUO,GAVP,CAFM,CAAR;EAcD;;EAED,MAAM0O,SAAS,GAAGH,QAAQ,CAAC9T,MAAT,CAChB,UAACC,GAAD,EAAMU,SAAN,EAAgC;EAC9B,QAAMmE,QAAQ,GAAGnE,SAAS,IAAIqL,WAAW,CAACrL,SAAD,EAAYyJ,WAAZ,CAAzC;;EAEA,QAAItF,QAAJ,EAAc;EACZ7E,MAAAA,GAAG,CAACV,IAAJ,CAASuF,QAAT;EACD;;EAED,WAAO7E,GAAP;EACD,GATe,EAUhB,EAVgB,CAAlB;EAaA,SAAOI,SAAS,CAACuG,OAAD,CAAT,GAAqBqN,SAAS,CAAC,CAAD,CAA9B,GAAoCA,SAA3C;EACD;;EAEDL,KAAK,CAAC5L,YAAN,GAAqBA,YAArB;EACA4L,KAAK,CAAC5J,eAAN,GAAwBA,eAAxB;EACA4J,KAAK,CAAC1P,YAAN,GAAqBA,YAArB;AAEA,EAEO,IAAMgQ,OAAgB,GAAG,SAAnBA,OAAmB,QAGL;EAAA,gCAAP,EAAO;EAAA,MAFhBC,2BAEgB,QAFzBC,OAEyB;EAAA,MADzB/L,QACyB,QADzBA,QACyB;;EACzB0D,EAAAA,gBAAgB,CAACjN,OAAjB,CAAyB,UAACgG,QAAD,EAAc;EACrC,QAAIuP,UAAU,GAAG,KAAjB;;EAEA,QAAIF,2BAAJ,EAAiC;EAC/BE,MAAAA,UAAU,GAAG5T,kBAAkB,CAAC0T,2BAAD,CAAlB,GACTrP,QAAQ,CAACnE,SAAT,KAAuBwT,2BADd,GAETrP,QAAQ,CAACuG,MAAT,KAAqB8I,2BAAD,CAA0C9I,MAFlE;EAGD;;EAED,QAAI,CAACgJ,UAAL,EAAiB;EACf,UAAMC,gBAAgB,GAAGxP,QAAQ,CAAC5C,KAAT,CAAemG,QAAxC;EAEAvD,MAAAA,QAAQ,CAACwI,QAAT,CAAkB;EAACjF,QAAAA,QAAQ,EAARA;EAAD,OAAlB;EACAvD,MAAAA,QAAQ,CAAC2I,IAAT;;EAEA,UAAI,CAAC3I,QAAQ,CAAC1D,KAAT,CAAe8L,WAApB,EAAiC;EAC/BpI,QAAAA,QAAQ,CAACwI,QAAT,CAAkB;EAACjF,UAAAA,QAAQ,EAAEiM;EAAX,SAAlB;EACD;EACF;EACF,GAnBD;EAoBD,CAxBM;;ECrDP;EACA;EACA;;EACA,IAAMC,mBAAqE,qBACtEC,gBADsE;EAEzEC,EAAAA,MAFyE,wBAEzD;EAAA,QAARrT,KAAQ,QAARA,KAAQ;EACd,QAAMsT,aAAa,GAAG;EACpBrJ,MAAAA,MAAM,EAAE;EACNsJ,QAAAA,QAAQ,EAAEvT,KAAK,CAAC0P,OAAN,CAAc8D,QADlB;EAEN9R,QAAAA,IAAI,EAAE,GAFA;EAGNL,QAAAA,GAAG,EAAE,GAHC;EAINoS,QAAAA,MAAM,EAAE;EAJF,OADY;EAOpBpN,MAAAA,KAAK,EAAE;EACLkN,QAAAA,QAAQ,EAAE;EADL,OAPa;EAUpBhU,MAAAA,SAAS,EAAE;EAVS,KAAtB;EAaAZ,IAAAA,MAAM,CAAC+U,MAAP,CAAc1T,KAAK,CAAC0S,QAAN,CAAezI,MAAf,CAAsBpK,KAApC,EAA2CyT,aAAa,CAACrJ,MAAzD;EACAjK,IAAAA,KAAK,CAAC2T,MAAN,GAAeL,aAAf;;EAEA,QAAItT,KAAK,CAAC0S,QAAN,CAAerM,KAAnB,EAA0B;EACxB1H,MAAAA,MAAM,CAAC+U,MAAP,CAAc1T,KAAK,CAAC0S,QAAN,CAAerM,KAAf,CAAqBxG,KAAnC,EAA0CyT,aAAa,CAACjN,KAAxD;EACD,KAnBa;EAsBd;;EACD;EAzBwE,EAA3E;;EA4BA,IAAMuN,eAAgC,GAAG,SAAnCA,eAAmC,CACvCC,cADuC,EAEvCpB,aAFuC,EAGpC;EAAA;;EAAA,MADHA,aACG;EADHA,IAAAA,aACG,GADa,EACb;EAAA;;EACH;EACA,EAAa;EACXpN,IAAAA,SAAS,CACP,CAAC/I,KAAK,CAACC,OAAN,CAAcsX,cAAd,CADM,EAEP,CACE,oEADF,EAEE,uCAFF,EAGEjO,MAAM,CAACiO,cAAD,CAHR,EAIE1P,IAJF,CAIO,GAJP,CAFO,CAAT;EAQD;;EAED,MAAI2P,mBAAmB,GAAGD,cAA1B;EACA,MAAIE,UAAmC,GAAG,EAA1C;EACA,MAAIC,cAA8B,GAAG,EAArC;EACA,MAAItI,aAAJ;EACA,MAAIuI,SAAS,GAAGxB,aAAa,CAACwB,SAA9B;EACA,MAAIC,yBAA4C,GAAG,EAAnD;EACA,MAAIC,aAAa,GAAG,KAApB;;EAEA,WAASC,iBAAT,GAAmC;EACjCJ,IAAAA,cAAc,GAAGF,mBAAmB,CACjCjH,GADc,CACV,UAACnJ,QAAD;EAAA,aACH3F,gBAAgB,CAAC2F,QAAQ,CAAC5C,KAAT,CAAe4H,aAAf,IAAgChF,QAAQ,CAACnE,SAA1C,CADb;EAAA,KADU,EAIdX,MAJc,CAIP,UAACC,GAAD,EAAMR,IAAN;EAAA,aAAeQ,GAAG,CAACb,MAAJ,CAAWK,IAAX,CAAf;EAAA,KAJO,EAI0B,EAJ1B,CAAjB;EAKD;;EAED,WAASgW,aAAT,GAA+B;EAC7BN,IAAAA,UAAU,GAAGD,mBAAmB,CAACjH,GAApB,CAAwB,UAACnJ,QAAD;EAAA,aAAcA,QAAQ,CAACnE,SAAvB;EAAA,KAAxB,CAAb;EACD;;EAED,WAAS+U,eAAT,CAAyBzI,SAAzB,EAAmD;EACjDiI,IAAAA,mBAAmB,CAACpW,OAApB,CAA4B,UAACgG,QAAD,EAAc;EACxC,UAAImI,SAAJ,EAAe;EACbnI,QAAAA,QAAQ,CAAC6I,MAAT;EACD,OAFD,MAEO;EACL7I,QAAAA,QAAQ,CAAC8I,OAAT;EACD;EACF,KAND;EAOD;;EAED,WAAS+H,iBAAT,CAA2BC,SAA3B,EAAmE;EACjE,WAAOV,mBAAmB,CAACjH,GAApB,CAAwB,UAACnJ,QAAD,EAAc;EAC3C,UAAM+Q,gBAAgB,GAAG/Q,QAAQ,CAACwI,QAAlC;;EAEAxI,MAAAA,QAAQ,CAACwI,QAAT,GAAoB,UAACpL,KAAD,EAAiB;EACnC2T,QAAAA,gBAAgB,CAAC3T,KAAD,CAAhB;;EAEA,YAAI4C,QAAQ,CAACnE,SAAT,KAAuBmM,aAA3B,EAA0C;EACxC8I,UAAAA,SAAS,CAACtI,QAAV,CAAmBpL,KAAnB;EACD;EACF,OAND;;EAQA,aAAO,YAAY;EACjB4C,QAAAA,QAAQ,CAACwI,QAAT,GAAoBuI,gBAApB;EACD,OAFD;EAGD,KAdM,CAAP;EAeD,GA3DE;;;EA8DH,WAASC,eAAT,CACEF,SADF,EAEE9R,MAFF,EAGQ;EACN,QAAMtG,KAAK,GAAG4X,cAAc,CAACnX,OAAf,CAAuB6F,MAAvB,CAAd,CADM;;EAIN,QAAIA,MAAM,KAAKgJ,aAAf,EAA8B;EAC5B;EACD;;EAEDA,IAAAA,aAAa,GAAGhJ,MAAhB;EAEA,QAAMiS,aAA6B,GAAG,CAACV,SAAS,IAAI,EAAd,EACnCjW,MADmC,CAC5B,SAD4B,EAEnCY,MAFmC,CAE5B,UAACC,GAAD,EAAM+K,IAAN,EAAe;EACpB/K,MAAAA,GAAD,CAAa+K,IAAb,IAAqBkK,mBAAmB,CAAC1X,KAAD,CAAnB,CAA2B0E,KAA3B,CAAiC8I,IAAjC,CAArB;EACA,aAAO/K,GAAP;EACD,KALmC,EAKjC,EALiC,CAAtC;EAOA2V,IAAAA,SAAS,CAACtI,QAAV,mBACKyI,aADL;EAEEzN,MAAAA,sBAAsB,EACpB,OAAOyN,aAAa,CAACzN,sBAArB,KAAgD,UAAhD,GACIyN,aAAa,CAACzN,sBADlB,GAEI;EAAA;;EAAA,oCAAkB6M,UAAU,CAAC3X,KAAD,CAA5B,qBAAkB,kBAAmBgU,qBAAnB,EAAlB;EAAA;EALR;EAOD;;EAEDkE,EAAAA,eAAe,CAAC,KAAD,CAAf;EACAD,EAAAA,aAAa;EACbD,EAAAA,iBAAiB;EAEjB,MAAMnL,MAAc,GAAG;EACrBhM,IAAAA,EADqB,gBAChB;EACH,aAAO;EACL0K,QAAAA,SADK,uBACa;EAChB2M,UAAAA,eAAe,CAAC,IAAD,CAAf;EACD,SAHI;EAIL1M,QAAAA,QAJK,sBAIY;EACf8D,UAAAA,aAAa,GAAG,IAAhB;EACD,SANI;EAOLvD,QAAAA,cAPK,0BAOUzE,QAPV,EAO0B;EAC7B,cAAIA,QAAQ,CAAC5C,KAAT,CAAeyH,YAAf,IAA+B,CAAC4L,aAApC,EAAmD;EACjDA,YAAAA,aAAa,GAAG,IAAhB;EACAzI,YAAAA,aAAa,GAAG,IAAhB;EACD;EACF,SAZI;EAaL3D,QAAAA,MAbK,kBAaErE,QAbF,EAakB;EACrB,cAAIA,QAAQ,CAAC5C,KAAT,CAAeyH,YAAf,IAA+B,CAAC4L,aAApC,EAAmD;EACjDA,YAAAA,aAAa,GAAG,IAAhB;EACAO,YAAAA,eAAe,CAAChR,QAAD,EAAWqQ,UAAU,CAAC,CAAD,CAArB,CAAf;EACD;EACF,SAlBI;EAmBL9L,QAAAA,SAnBK,qBAmBKvE,QAnBL,EAmBelD,KAnBf,EAmB4B;EAC/BkU,UAAAA,eAAe,CAAChR,QAAD,EAAWlD,KAAK,CAACkL,aAAjB,CAAf;EACD;EArBI,OAAP;EAuBD;EAzBoB,GAAvB;EA4BA,MAAM8I,SAAS,GAAGhC,KAAK,CAACzT,GAAG,EAAJ,oBAClBxB,gBAAgB,CAACkV,aAAD,EAAgB,CAAC,WAAD,CAAhB,CADE;EAErBrK,IAAAA,OAAO,GAAGa,MAAH,SAAewJ,aAAa,CAACrK,OAAd,IAAyB,EAAxC,CAFc;EAGrBM,IAAAA,aAAa,EAAEsL,cAHM;EAIrB3L,IAAAA,aAAa,oBACRoK,aAAa,CAACpK,aADN;EAEX2I,MAAAA,SAAS,YACH,0BAAAyB,aAAa,CAACpK,aAAd,2CAA6B2I,SAA7B,KAA0C,EADvC,GAEPmC,mBAFO;EAFE;EAJQ,KAAvB;EAaA,MAAMyB,YAAY,GAAGJ,SAAS,CAACpI,IAA/B;;EAEAoI,EAAAA,SAAS,CAACpI,IAAV,GAAiB,UAAC1J,MAAD,EAAyD;EACxEkS,IAAAA,YAAY,GAD4D;EAIxE;;EACA,QAAI,CAAClJ,aAAD,IAAkBhJ,MAAM,IAAI,IAAhC,EAAsC;EACpC,aAAOgS,eAAe,CAACF,SAAD,EAAYT,UAAU,CAAC,CAAD,CAAtB,CAAtB;EACD,KAPuE;EAUxE;;;EACA,QAAIrI,aAAa,IAAIhJ,MAAM,IAAI,IAA/B,EAAqC;EACnC;EACD,KAbuE;;;EAgBxE,QAAI,OAAOA,MAAP,KAAkB,QAAtB,EAAgC;EAC9B,aACEqR,UAAU,CAACrR,MAAD,CAAV,IAAsBgS,eAAe,CAACF,SAAD,EAAYT,UAAU,CAACrR,MAAD,CAAtB,CADvC;EAGD,KApBuE;;;EAuBxE,QAAIoR,mBAAmB,CAACjX,OAApB,CAA4B6F,MAA5B,KAAmD,CAAvD,EAA0D;EACxD,UAAMmS,GAAG,GAAInS,MAAD,CAAqBnD,SAAjC;EACA,aAAOmV,eAAe,CAACF,SAAD,EAAYK,GAAZ,CAAtB;EACD,KA1BuE;;;EA6BxE,QAAId,UAAU,CAAClX,OAAX,CAAmB6F,MAAnB,KAAkD,CAAtD,EAAyD;EACvD,aAAOgS,eAAe,CAACF,SAAD,EAAY9R,MAAZ,CAAtB;EACD;EACF,GAhCD;;EAkCA8R,EAAAA,SAAS,CAACM,QAAV,GAAqB,YAAY;EAC/B,QAAMC,KAAK,GAAGhB,UAAU,CAAC,CAAD,CAAxB;;EACA,QAAI,CAACrI,aAAL,EAAoB;EAClB,aAAO8I,SAAS,CAACpI,IAAV,CAAe,CAAf,CAAP;EACD;;EACD,QAAMhQ,KAAK,GAAG2X,UAAU,CAAClX,OAAX,CAAmB6O,aAAnB,CAAd;EACA8I,IAAAA,SAAS,CAACpI,IAAV,CAAe2H,UAAU,CAAC3X,KAAK,GAAG,CAAT,CAAV,IAAyB2Y,KAAxC;EACD,GAPD;;EASAP,EAAAA,SAAS,CAACQ,YAAV,GAAyB,YAAY;EACnC,QAAMC,IAAI,GAAGlB,UAAU,CAACA,UAAU,CAAChK,MAAX,GAAoB,CAArB,CAAvB;;EACA,QAAI,CAAC2B,aAAL,EAAoB;EAClB,aAAO8I,SAAS,CAACpI,IAAV,CAAe6I,IAAf,CAAP;EACD;;EACD,QAAM7Y,KAAK,GAAG2X,UAAU,CAAClX,OAAX,CAAmB6O,aAAnB,CAAd;EACA,QAAMhJ,MAAM,GAAGqR,UAAU,CAAC3X,KAAK,GAAG,CAAT,CAAV,IAAyB6Y,IAAxC;EACAT,IAAAA,SAAS,CAACpI,IAAV,CAAe1J,MAAf;EACD,GARD;;EAUA,MAAM+R,gBAAgB,GAAGD,SAAS,CAACtI,QAAnC;;EAEAsI,EAAAA,SAAS,CAACtI,QAAV,GAAqB,UAACpL,KAAD,EAAiB;EACpCmT,IAAAA,SAAS,GAAGnT,KAAK,CAACmT,SAAN,IAAmBA,SAA/B;EACAQ,IAAAA,gBAAgB,CAAC3T,KAAD,CAAhB;EACD,GAHD;;EAKA0T,EAAAA,SAAS,CAACU,YAAV,GAAyB,UAACC,aAAD,EAAyB;EAChDb,IAAAA,eAAe,CAAC,IAAD,CAAf;EACAJ,IAAAA,yBAAyB,CAACxW,OAA1B,CAAkC,UAACT,EAAD;EAAA,aAAQA,EAAE,EAAV;EAAA,KAAlC;EAEA6W,IAAAA,mBAAmB,GAAGqB,aAAtB;EAEAb,IAAAA,eAAe,CAAC,KAAD,CAAf;EACAD,IAAAA,aAAa;EACbD,IAAAA,iBAAiB;EACjBF,IAAAA,yBAAyB,GAAGK,iBAAiB,CAACC,SAAD,CAA7C;EAEAA,IAAAA,SAAS,CAACtI,QAAV,CAAmB;EAACxD,MAAAA,aAAa,EAAEsL;EAAhB,KAAnB;EACD,GAZD;;EAcAE,EAAAA,yBAAyB,GAAGK,iBAAiB,CAACC,SAAD,CAA7C;EAEA,SAAOA,SAAP;EACD,CA1ND;;ECvCA,IAAMY,mBAAmB,GAAG;EAC1BC,EAAAA,SAAS,EAAE,YADe;EAE1BC,EAAAA,OAAO,EAAE,OAFiB;EAG1BC,EAAAA,KAAK,EAAE;EAHmB,CAA5B;EAMA;EACA;EACA;EACA;;EACA,SAASC,QAAT,CACEhQ,OADF,EAEE1E,KAFF,EAGyB;EACvB;EACA,EAAa;EACXuE,IAAAA,SAAS,CACP,EAAEvE,KAAK,IAAIA,KAAK,CAAC4B,MAAjB,CADO,EAEP,CACE,4EADF,EAEE,kDAFF,EAGEyB,IAHF,CAGO,GAHP,CAFO,CAAT;EAOD;;EAED,MAAIoH,SAA2B,GAAG,EAAlC;EACA,MAAIkK,mBAA+B,GAAG,EAAtC;EACA,MAAIC,QAAQ,GAAG,KAAf;EAEA,MAAOhT,MAAP,GAAiB5B,KAAjB,CAAO4B,MAAP;EAEA,MAAMiT,WAAW,GAAGpY,gBAAgB,CAACuD,KAAD,EAAQ,CAAC,QAAD,CAAR,CAApC;EACA,MAAM8U,WAAW,qBAAOD,WAAP;EAAoBlN,IAAAA,OAAO,EAAE,QAA7B;EAAuCD,IAAAA,KAAK,EAAE;EAA9C,IAAjB;EACA,MAAMqN,UAAU;EACdrN,IAAAA,KAAK,EAAE5B,YAAY,CAAC4B;EADN,KAEXmN,WAFW;EAGdpN,IAAAA,YAAY,EAAE;EAHA,IAAhB;EAMA,MAAMuN,WAAW,GAAGtD,KAAK,CAAChN,OAAD,EAAUoQ,WAAV,CAAzB;EACA,MAAMG,qBAAqB,GAAGhY,gBAAgB,CAAC+X,WAAD,CAA9C;;EAEA,WAAS7N,SAAT,CAAmBzH,KAAnB,EAAuC;EACrC,QAAI,CAACA,KAAK,CAACkC,MAAP,IAAiBgT,QAArB,EAA+B;EAC7B;EACD;;EAED,QAAMM,UAAU,GAAIxV,KAAK,CAACkC,MAAP,CAA0BuT,OAA1B,CAAkCvT,MAAlC,CAAnB;;EAEA,QAAI,CAACsT,UAAL,EAAiB;EACf;EACD,KAToC;EAYrC;EACA;EACA;;;EACA,QAAMvN,OAAO,GACXuN,UAAU,CAAC1M,YAAX,CAAwB,oBAAxB,KACAxI,KAAK,CAAC2H,OADN,IAEA7B,YAAY,CAAC6B,OAHf,CAfqC;;EAqBrC,QAAIuN,UAAU,CAAC1W,MAAf,EAAuB;EACrB;EACD;;EAED,QAAIkB,KAAK,CAAC9D,IAAN,KAAe,YAAf,IAA+B,OAAOmZ,UAAU,CAACrN,KAAlB,KAA4B,SAA/D,EAA0E;EACxE;EACD;;EAED,QACEhI,KAAK,CAAC9D,IAAN,KAAe,YAAf,IACA+L,OAAO,CAAC5L,OAAR,CAAiBuY,mBAAD,CAA6B5U,KAAK,CAAC9D,IAAnC,CAAhB,IAA4D,CAF9D,EAGE;EACA;EACD;;EAED,QAAMgH,QAAQ,GAAG8O,KAAK,CAACwD,UAAD,EAAaH,UAAb,CAAtB;;EAEA,QAAInS,QAAJ,EAAc;EACZ+R,MAAAA,mBAAmB,GAAGA,mBAAmB,CAACzX,MAApB,CAA2B0F,QAA3B,CAAtB;EACD;EACF;;EAED,WAAS6L,EAAT,CACEjF,IADF,EAEEkF,SAFF,EAGEC,OAHF,EAIEC,OAJF,EAKQ;EAAA,QADNA,OACM;EADNA,MAAAA,OACM,GADuC,KACvC;EAAA;;EACNpF,IAAAA,IAAI,CAAClH,gBAAL,CAAsBoM,SAAtB,EAAiCC,OAAjC,EAA0CC,OAA1C;EACAnE,IAAAA,SAAS,CAACpN,IAAV,CAAe;EAACmM,MAAAA,IAAI,EAAJA,IAAD;EAAOkF,MAAAA,SAAS,EAATA,SAAP;EAAkBC,MAAAA,OAAO,EAAPA,OAAlB;EAA2BC,MAAAA,OAAO,EAAPA;EAA3B,KAAf;EACD;;EAED,WAASwG,iBAAT,CAA2BxS,QAA3B,EAAqD;EACnD,QAAOnE,SAAP,GAAoBmE,QAApB,CAAOnE,SAAP;EAEAgQ,IAAAA,EAAE,CAAChQ,SAAD,EAAY,YAAZ,EAA0B0I,SAA1B,EAAqCzM,aAArC,CAAF;EACA+T,IAAAA,EAAE,CAAChQ,SAAD,EAAY,WAAZ,EAAyB0I,SAAzB,CAAF;EACAsH,IAAAA,EAAE,CAAChQ,SAAD,EAAY,SAAZ,EAAuB0I,SAAvB,CAAF;EACAsH,IAAAA,EAAE,CAAChQ,SAAD,EAAY,OAAZ,EAAqB0I,SAArB,CAAF;EACD;;EAED,WAASkO,oBAAT,GAAsC;EACpC5K,IAAAA,SAAS,CAAC7N,OAAV,CAAkB,gBAAyD;EAAA,UAAvD4M,IAAuD,QAAvDA,IAAuD;EAAA,UAAjDkF,SAAiD,QAAjDA,SAAiD;EAAA,UAAtCC,OAAsC,QAAtCA,OAAsC;EAAA,UAA7BC,OAA6B,QAA7BA,OAA6B;EACzEpF,MAAAA,IAAI,CAAC/G,mBAAL,CAAyBiM,SAAzB,EAAoCC,OAApC,EAA6CC,OAA7C;EACD,KAFD;EAGAnE,IAAAA,SAAS,GAAG,EAAZ;EACD;;EAED,WAAS6K,cAAT,CAAwB1S,QAAxB,EAAkD;EAChD,QAAM2S,eAAe,GAAG3S,QAAQ,CAACgJ,OAAjC;EACA,QAAM4J,cAAc,GAAG5S,QAAQ,CAAC6I,MAAhC;EACA,QAAMgK,eAAe,GAAG7S,QAAQ,CAAC8I,OAAjC;;EAEA9I,IAAAA,QAAQ,CAACgJ,OAAT,GAAmB,UAAC8J,2BAAD,EAA8C;EAAA,UAA7CA,2BAA6C;EAA7CA,QAAAA,2BAA6C,GAAf,IAAe;EAAA;;EAC/D,UAAIA,2BAAJ,EAAiC;EAC/Bf,QAAAA,mBAAmB,CAAC/X,OAApB,CAA4B,UAACgG,QAAD,EAAc;EACxCA,UAAAA,QAAQ,CAACgJ,OAAT;EACD,SAFD;EAGD;;EAED+I,MAAAA,mBAAmB,GAAG,EAAtB;EAEAU,MAAAA,oBAAoB;EACpBE,MAAAA,eAAe;EAChB,KAXD;;EAaA3S,IAAAA,QAAQ,CAAC6I,MAAT,GAAkB,YAAY;EAC5B+J,MAAAA,cAAc;EACdb,MAAAA,mBAAmB,CAAC/X,OAApB,CAA4B,UAACgG,QAAD;EAAA,eAAcA,QAAQ,CAAC6I,MAAT,EAAd;EAAA,OAA5B;EACAmJ,MAAAA,QAAQ,GAAG,KAAX;EACD,KAJD;;EAMAhS,IAAAA,QAAQ,CAAC8I,OAAT,GAAmB,YAAY;EAC7B+J,MAAAA,eAAe;EACfd,MAAAA,mBAAmB,CAAC/X,OAApB,CAA4B,UAACgG,QAAD;EAAA,eAAcA,QAAQ,CAAC8I,OAAT,EAAd;EAAA,OAA5B;EACAkJ,MAAAA,QAAQ,GAAG,IAAX;EACD,KAJD;;EAMAQ,IAAAA,iBAAiB,CAACxS,QAAD,CAAjB;EACD;;EAEDqS,EAAAA,qBAAqB,CAACrY,OAAtB,CAA8B0Y,cAA9B;EAEA,SAAON,WAAP;EACD;;ECrJD,IAAMhQ,WAAwB,GAAG;EAC/BoD,EAAAA,IAAI,EAAE,aADyB;EAE/B7M,EAAAA,YAAY,EAAE,KAFiB;EAG/BY,EAAAA,EAH+B,cAG5ByG,QAH4B,EAGlB;EAAA;;EACX;EACA,QAAI,2BAACA,QAAQ,CAAC5C,KAAT,CAAewH,MAAhB,aAAC,sBAAuBmF,OAAxB,CAAJ,EAAqC;EACnC,MAAa;EACXpI,QAAAA,SAAS,CACP3B,QAAQ,CAAC5C,KAAT,CAAegF,WADR,EAEP,gEAFO,CAAT;EAID;;EAED,aAAO,EAAP;EACD;;EAED,uBAAuBkE,WAAW,CAACtG,QAAQ,CAACuG,MAAV,CAAlC;EAAA,QAAO9H,GAAP,gBAAOA,GAAP;EAAA,QAAYmE,OAAZ,gBAAYA,OAAZ;;EAEA,QAAMkE,QAAQ,GAAG9G,QAAQ,CAAC5C,KAAT,CAAegF,WAAf,GACb2Q,qBAAqB,EADR,GAEb,IAFJ;EAIA,WAAO;EACL/O,MAAAA,QADK,sBACY;EACf,YAAI8C,QAAJ,EAAc;EACZrI,UAAAA,GAAG,CAACuU,YAAJ,CAAiBlM,QAAjB,EAA2BrI,GAAG,CAAC+H,iBAA/B;EACA/H,UAAAA,GAAG,CAAClC,YAAJ,CAAiB,kBAAjB,EAAqC,EAArC;EACAkC,UAAAA,GAAG,CAACtC,KAAJ,CAAU8W,QAAV,GAAqB,QAArB;EAEAjT,UAAAA,QAAQ,CAACwI,QAAT,CAAkB;EAAC7F,YAAAA,KAAK,EAAE,KAAR;EAAeD,YAAAA,SAAS,EAAE;EAA1B,WAAlB;EACD;EACF,OATI;EAUL0B,MAAAA,OAVK,qBAUW;EACd,YAAI0C,QAAJ,EAAc;EACZ,cAAO1K,kBAAP,GAA6BqC,GAAG,CAACtC,KAAjC,CAAOC,kBAAP;EACA,cAAMmH,QAAQ,GAAG2P,MAAM,CAAC9W,kBAAkB,CAACyE,OAAnB,CAA2B,IAA3B,EAAiC,EAAjC,CAAD,CAAvB,CAFY;EAKZ;EACA;;EACA+B,UAAAA,OAAO,CAACzG,KAAR,CAAcgX,eAAd,GAAmCC,IAAI,CAACC,KAAL,CAAW9P,QAAQ,GAAG,EAAtB,CAAnC;EAEAuD,UAAAA,QAAQ,CAAC3K,KAAT,CAAeC,kBAAf,GAAoCA,kBAApC;EACAC,UAAAA,kBAAkB,CAAC,CAACyK,QAAD,CAAD,EAAa,SAAb,CAAlB;EACD;EACF,OAvBI;EAwBLzC,MAAAA,MAxBK,oBAwBU;EACb,YAAIyC,QAAJ,EAAc;EACZA,UAAAA,QAAQ,CAAC3K,KAAT,CAAeC,kBAAf,GAAoC,KAApC;EACD;EACF,OA5BI;EA6BL+H,MAAAA,MA7BK,oBA6BU;EACb,YAAI2C,QAAJ,EAAc;EACZzK,UAAAA,kBAAkB,CAAC,CAACyK,QAAD,CAAD,EAAa,QAAb,CAAlB;EACD;EACF;EAjCI,KAAP;EAmCD;EAzD8B,CAAjC;AA4DA;EAEA,SAASiM,qBAAT,GAAiD;EAC/C,MAAMjM,QAAQ,GAAGzL,GAAG,EAApB;EACAyL,EAAAA,QAAQ,CAACwM,SAAT,GAAqB3b,cAArB;EACA0E,EAAAA,kBAAkB,CAAC,CAACyK,QAAD,CAAD,EAAa,QAAb,CAAlB;EACA,SAAOA,QAAP;EACD;;ECtED,IAAIyM,WAAW,GAAG;EAACxW,EAAAA,OAAO,EAAE,CAAV;EAAaC,EAAAA,OAAO,EAAE;EAAtB,CAAlB;EACA,IAAIwW,eAA2D,GAAG,EAAlE;;EAEA,SAASC,gBAAT,OAAgE;EAAA,MAArC1W,OAAqC,QAArCA,OAAqC;EAAA,MAA5BC,OAA4B,QAA5BA,OAA4B;EAC9DuW,EAAAA,WAAW,GAAG;EAACxW,IAAAA,OAAO,EAAPA,OAAD;EAAUC,IAAAA,OAAO,EAAPA;EAAV,GAAd;EACD;;EAED,SAAS0W,sBAAT,CAAgClI,GAAhC,EAAqD;EACnDA,EAAAA,GAAG,CAAC9L,gBAAJ,CAAqB,WAArB,EAAkC+T,gBAAlC;EACD;;EAED,SAASE,yBAAT,CAAmCnI,GAAnC,EAAwD;EACtDA,EAAAA,GAAG,CAAC3L,mBAAJ,CAAwB,WAAxB,EAAqC4T,gBAArC;EACD;;EAED,IAAMpR,YAA0B,GAAG;EACjCmD,EAAAA,IAAI,EAAE,cAD2B;EAEjC7M,EAAAA,YAAY,EAAE,KAFmB;EAGjCY,EAAAA,EAHiC,cAG9ByG,QAH8B,EAGpB;EACX,QAAMnE,SAAS,GAAGmE,QAAQ,CAACnE,SAA3B;EACA,QAAM2P,GAAG,GAAGhP,gBAAgB,CAACwD,QAAQ,CAAC5C,KAAT,CAAe4H,aAAf,IAAgCnJ,SAAjC,CAA5B;EAEA,QAAI+X,gBAAgB,GAAG,KAAvB;EACA,QAAIC,aAAa,GAAG,KAApB;EACA,QAAIC,WAAW,GAAG,IAAlB;EACA,QAAI7F,SAAS,GAAGjO,QAAQ,CAAC5C,KAAzB;;EAEA,aAAS2W,oBAAT,GAAyC;EACvC,aACE/T,QAAQ,CAAC5C,KAAT,CAAeiF,YAAf,KAAgC,SAAhC,IAA6CrC,QAAQ,CAAC1D,KAAT,CAAe4D,SAD9D;EAGD;;EAED,aAAS8T,WAAT,GAA6B;EAC3BxI,MAAAA,GAAG,CAAC9L,gBAAJ,CAAqB,WAArB,EAAkCqI,WAAlC;EACD;;EAED,aAASkM,cAAT,GAAgC;EAC9BzI,MAAAA,GAAG,CAAC3L,mBAAJ,CAAwB,WAAxB,EAAqCkI,WAArC;EACD;;EAED,aAASmM,2BAAT,GAA6C;EAC3CN,MAAAA,gBAAgB,GAAG,IAAnB;EACA5T,MAAAA,QAAQ,CAACwI,QAAT,CAAkB;EAAChF,QAAAA,sBAAsB,EAAE;EAAzB,OAAlB;EACAoQ,MAAAA,gBAAgB,GAAG,KAAnB;EACD;;EAED,aAAS7L,WAAT,CAAqBjL,KAArB,EAA8C;EAC5C;EACA;EACA,UAAMqX,qBAAqB,GAAGrX,KAAK,CAACkC,MAAN,GAC1BnD,SAAS,CAACoD,QAAV,CAAmBnC,KAAK,CAACkC,MAAzB,CAD0B,GAE1B,IAFJ;EAGA,UAAOqD,YAAP,GAAuBrC,QAAQ,CAAC5C,KAAhC,CAAOiF,YAAP;EACA,UAAOtF,OAAP,GAA2BD,KAA3B,CAAOC,OAAP;EAAA,UAAgBC,OAAhB,GAA2BF,KAA3B,CAAgBE,OAAhB;EAEA,UAAMoX,IAAI,GAAGvY,SAAS,CAAC6Q,qBAAV,EAAb;EACA,UAAM2H,SAAS,GAAGtX,OAAO,GAAGqX,IAAI,CAACpW,IAAjC;EACA,UAAMsW,SAAS,GAAGtX,OAAO,GAAGoX,IAAI,CAACzW,GAAjC;;EAEA,UAAIwW,qBAAqB,IAAI,CAACnU,QAAQ,CAAC5C,KAAT,CAAeuG,WAA7C,EAA0D;EACxD3D,QAAAA,QAAQ,CAACwI,QAAT,CAAkB;EAChB;EACAhF,UAAAA,sBAFgB,oCAES;EACvB,gBAAM4Q,IAAI,GAAGvY,SAAS,CAAC6Q,qBAAV,EAAb;EAEA,gBAAIzO,CAAC,GAAGlB,OAAR;EACA,gBAAIa,CAAC,GAAGZ,OAAR;;EAEA,gBAAIqF,YAAY,KAAK,SAArB,EAAgC;EAC9BpE,cAAAA,CAAC,GAAGmW,IAAI,CAACpW,IAAL,GAAYqW,SAAhB;EACAzW,cAAAA,CAAC,GAAGwW,IAAI,CAACzW,GAAL,GAAW2W,SAAf;EACD;;EAED,gBAAM3W,GAAG,GAAG0E,YAAY,KAAK,YAAjB,GAAgC+R,IAAI,CAACzW,GAArC,GAA2CC,CAAvD;EACA,gBAAMO,KAAK,GAAGkE,YAAY,KAAK,UAAjB,GAA8B+R,IAAI,CAACjW,KAAnC,GAA2CF,CAAzD;EACA,gBAAMH,MAAM,GAAGuE,YAAY,KAAK,YAAjB,GAAgC+R,IAAI,CAACtW,MAArC,GAA8CF,CAA7D;EACA,gBAAMI,IAAI,GAAGqE,YAAY,KAAK,UAAjB,GAA8B+R,IAAI,CAACpW,IAAnC,GAA0CC,CAAvD;EAEA,mBAAO;EACLsW,cAAAA,KAAK,EAAEpW,KAAK,GAAGH,IADV;EAELwW,cAAAA,MAAM,EAAE1W,MAAM,GAAGH,GAFZ;EAGLA,cAAAA,GAAG,EAAHA,GAHK;EAILQ,cAAAA,KAAK,EAALA,KAJK;EAKLL,cAAAA,MAAM,EAANA,MALK;EAMLE,cAAAA,IAAI,EAAJA;EANK,aAAP;EAQD;EA1Be,SAAlB;EA4BD;EACF;;EAED,aAASyW,MAAT,GAAwB;EACtB,UAAIzU,QAAQ,CAAC5C,KAAT,CAAeiF,YAAnB,EAAiC;EAC/BmR,QAAAA,eAAe,CAAC/Y,IAAhB,CAAqB;EAACuF,UAAAA,QAAQ,EAARA,QAAD;EAAWwL,UAAAA,GAAG,EAAHA;EAAX,SAArB;EACAkI,QAAAA,sBAAsB,CAAClI,GAAD,CAAtB;EACD;EACF;;EAED,aAASxC,OAAT,GAAyB;EACvBwK,MAAAA,eAAe,GAAGA,eAAe,CAACrZ,MAAhB,CAChB,UAACua,IAAD;EAAA,eAAUA,IAAI,CAAC1U,QAAL,KAAkBA,QAA5B;EAAA,OADgB,CAAlB;;EAIA,UAAIwT,eAAe,CAACrZ,MAAhB,CAAuB,UAACua,IAAD;EAAA,eAAUA,IAAI,CAAClJ,GAAL,KAAaA,GAAvB;EAAA,OAAvB,EAAmDnF,MAAnD,KAA8D,CAAlE,EAAqE;EACnEsN,QAAAA,yBAAyB,CAACnI,GAAD,CAAzB;EACD;EACF;;EAED,WAAO;EACLxH,MAAAA,QAAQ,EAAEyQ,MADL;EAELxQ,MAAAA,SAAS,EAAE+E,OAFN;EAGLjF,MAAAA,cAHK,4BAGkB;EACrBkK,QAAAA,SAAS,GAAGjO,QAAQ,CAAC5C,KAArB;EACD,OALI;EAML0G,MAAAA,aANK,yBAMS6Q,CANT,SAMkC;EAAA,YAArBtS,YAAqB,SAArBA,YAAqB;;EACrC,YAAIuR,gBAAJ,EAAsB;EACpB;EACD;;EAED,YACEvR,YAAY,KAAKjH,SAAjB,IACA6S,SAAS,CAAC5L,YAAV,KAA2BA,YAF7B,EAGE;EACA2G,UAAAA,OAAO;;EAEP,cAAI3G,YAAJ,EAAkB;EAChBoS,YAAAA,MAAM;;EAEN,gBACEzU,QAAQ,CAAC1D,KAAT,CAAe+L,SAAf,IACA,CAACwL,aADD,IAEA,CAACE,oBAAoB,EAHvB,EAIE;EACAC,cAAAA,WAAW;EACZ;EACF,WAVD,MAUO;EACLC,YAAAA,cAAc;EACdC,YAAAA,2BAA2B;EAC5B;EACF;EACF,OAhCI;EAiCL9P,MAAAA,OAjCK,qBAiCW;EACd,YAAIpE,QAAQ,CAAC5C,KAAT,CAAeiF,YAAf,IAA+B,CAACwR,aAApC,EAAmD;EACjD,cAAIC,WAAJ,EAAiB;EACf/L,YAAAA,WAAW,CAACwL,WAAD,CAAX;EACAO,YAAAA,WAAW,GAAG,KAAd;EACD;;EAED,cAAI,CAACC,oBAAoB,EAAzB,EAA6B;EAC3BC,YAAAA,WAAW;EACZ;EACF;EACF,OA5CI;EA6CLzP,MAAAA,SA7CK,qBA6CKoQ,CA7CL,EA6CQ7X,KA7CR,EA6CqB;EACxB,YAAIpB,YAAY,CAACoB,KAAD,CAAhB,EAAyB;EACvByW,UAAAA,WAAW,GAAG;EAACxW,YAAAA,OAAO,EAAED,KAAK,CAACC,OAAhB;EAAyBC,YAAAA,OAAO,EAAEF,KAAK,CAACE;EAAxC,WAAd;EACD;;EACD6W,QAAAA,aAAa,GAAG/W,KAAK,CAAC9D,IAAN,KAAe,OAA/B;EACD,OAlDI;EAmDLkL,MAAAA,QAnDK,sBAmDY;EACf,YAAIlE,QAAQ,CAAC5C,KAAT,CAAeiF,YAAnB,EAAiC;EAC/B6R,UAAAA,2BAA2B;EAC3BD,UAAAA,cAAc;EACdH,UAAAA,WAAW,GAAG,IAAd;EACD;EACF;EAzDI,KAAP;EA2DD;EAzJgC,CAAnC;;ECbA,SAASc,QAAT,CAAkBxX,KAAlB,EAAgCyX,QAAhC,EAA8E;EAAA;;EAC5E,SAAO;EACLlQ,IAAAA,aAAa,oBACRvH,KAAK,CAACuH,aADE;EAEX2I,MAAAA,SAAS,YACJ,CAAC,yBAAAlQ,KAAK,CAACuH,aAAN,0CAAqB2I,SAArB,KAAkC,EAAnC,EAAuCnT,MAAvC,CACD;EAAA,YAAEqL,IAAF,QAAEA,IAAF;EAAA,eAAYA,IAAI,KAAKqP,QAAQ,CAACrP,IAA9B;EAAA,OADC,CADI,GAIPqP,QAJO;EAFE;EADR,GAAP;EAWD;;EAED,IAAMvS,iBAAoC,GAAG;EAC3CkD,EAAAA,IAAI,EAAE,mBADqC;EAE3C7M,EAAAA,YAAY,EAAE,KAF6B;EAG3CY,EAAAA,EAH2C,cAGxCyG,QAHwC,EAG9B;EACX,QAAOnE,SAAP,GAAoBmE,QAApB,CAAOnE,SAAP;;EAEA,aAASsM,SAAT,GAA8B;EAC5B,aAAO,CAAC,CAACnI,QAAQ,CAAC5C,KAAT,CAAekF,iBAAxB;EACD;;EAED,QAAIzH,SAAJ;EACA,QAAIia,eAAe,GAAG,CAAC,CAAvB;EACA,QAAIlB,gBAAgB,GAAG,KAAvB;EACA,QAAImB,eAA8B,GAAG,EAArC;EAEA,QAAMF,QAGL,GAAG;EACFrP,MAAAA,IAAI,EAAE,wBADJ;EAEF0H,MAAAA,OAAO,EAAE,IAFP;EAGFC,MAAAA,KAAK,EAAE,YAHL;EAIF5T,MAAAA,EAJE,qBAIU;EAAA,YAAR+C,KAAQ,SAARA,KAAQ;;EACV,YAAI6L,SAAS,EAAb,EAAiB;EACf,cAAI4M,eAAe,CAAC5b,OAAhB,CAAwBmD,KAAK,CAACzB,SAA9B,MAA6C,CAAC,CAAlD,EAAqD;EACnDka,YAAAA,eAAe,GAAG,EAAlB;EACD;;EAED,cACEla,SAAS,KAAKyB,KAAK,CAACzB,SAApB,IACAka,eAAe,CAAC5b,OAAhB,CAAwBmD,KAAK,CAACzB,SAA9B,MAA6C,CAAC,CAFhD,EAGE;EACAka,YAAAA,eAAe,CAACta,IAAhB,CAAqB6B,KAAK,CAACzB,SAA3B;EACAmF,YAAAA,QAAQ,CAACwI,QAAT,CAAkB;EAChB;EACAhF,cAAAA,sBAAsB,EAAE;EAAA,uBACtBA,uBAAsB,CAAClH,KAAK,CAACzB,SAAP,CADA;EAAA;EAFR,aAAlB;EAKD;;EAEDA,UAAAA,SAAS,GAAGyB,KAAK,CAACzB,SAAlB;EACD;EACF;EAxBC,KAHJ;;EA8BA,aAAS2I,uBAAT,CAAgC3I,SAAhC,EAAwE;EACtE,aAAOma,2BAA2B,CAChCpa,gBAAgB,CAACC,SAAD,CADgB,EAEhCgB,SAAS,CAAC6Q,qBAAV,EAFgC,EAGhC5R,SAAS,CAACe,SAAS,CAACoZ,cAAV,EAAD,CAHuB,EAIhCH,eAJgC,CAAlC;EAMD;;EAED,aAASI,gBAAT,CAA0B/P,YAA1B,EAA8D;EAC5DyO,MAAAA,gBAAgB,GAAG,IAAnB;EACA5T,MAAAA,QAAQ,CAACwI,QAAT,CAAkBrD,YAAlB;EACAyO,MAAAA,gBAAgB,GAAG,KAAnB;EACD;;EAED,aAASuB,WAAT,GAA6B;EAC3B,UAAI,CAACvB,gBAAL,EAAuB;EACrBsB,QAAAA,gBAAgB,CAACN,QAAQ,CAAC5U,QAAQ,CAAC5C,KAAV,EAAiByX,QAAjB,CAAT,CAAhB;EACD;EACF;;EAED,WAAO;EACL7Q,MAAAA,QAAQ,EAAEmR,WADL;EAELrR,MAAAA,aAAa,EAAEqR,WAFV;EAGL5Q,MAAAA,SAHK,qBAGKoQ,CAHL,EAGQ7X,KAHR,EAGqB;EACxB,YAAIpB,YAAY,CAACoB,KAAD,CAAhB,EAAyB;EACvB,cAAMsY,KAAK,GAAGta,SAAS,CAACkF,QAAQ,CAACnE,SAAT,CAAmBoZ,cAAnB,EAAD,CAAvB;EACA,cAAMI,UAAU,GAAGD,KAAK,CAACzO,IAAN,CACjB,UAACyN,IAAD;EAAA,mBACEA,IAAI,CAACpW,IAAL,GAAY,CAAZ,IAAiBlB,KAAK,CAACC,OAAvB,IACAqX,IAAI,CAACjW,KAAL,GAAa,CAAb,IAAkBrB,KAAK,CAACC,OADxB,IAEAqX,IAAI,CAACzW,GAAL,GAAW,CAAX,IAAgBb,KAAK,CAACE,OAFtB,IAGAoX,IAAI,CAACtW,MAAL,GAAc,CAAd,IAAmBhB,KAAK,CAACE,OAJ3B;EAAA,WADiB,CAAnB;EAOA,cAAMtE,KAAK,GAAG0c,KAAK,CAACjc,OAAN,CAAckc,UAAd,CAAd;EACAP,UAAAA,eAAe,GAAGpc,KAAK,GAAG,CAAC,CAAT,GAAaA,KAAb,GAAqBoc,eAAvC;EACD;EACF,OAhBI;EAiBL5Q,MAAAA,QAjBK,sBAiBY;EACf4Q,QAAAA,eAAe,GAAG,CAAC,CAAnB;EACD;EAnBI,KAAP;EAqBD;EAvF0C,CAA7C;AA0FA,EAEO,SAASE,2BAAT,CACLM,oBADK,EAELC,YAFK,EAGLC,WAHK,EAILV,eAJK,EAYL;EACA;EACA,MAAIU,WAAW,CAACnP,MAAZ,GAAqB,CAArB,IAA0BiP,oBAAoB,KAAK,IAAvD,EAA6D;EAC3D,WAAOC,YAAP;EACD,GAJD;;;EAOA,MACEC,WAAW,CAACnP,MAAZ,KAAuB,CAAvB,IACAyO,eAAe,IAAI,CADnB,IAEAU,WAAW,CAAC,CAAD,CAAX,CAAexX,IAAf,GAAsBwX,WAAW,CAAC,CAAD,CAAX,CAAerX,KAHvC,EAIE;EACA,WAAOqX,WAAW,CAACV,eAAD,CAAX,IAAgCS,YAAvC;EACD;;EAED,UAAQD,oBAAR;EACE,SAAK,KAAL;EACA,SAAK,QAAL;EAAe;EACb,YAAMG,SAAS,GAAGD,WAAW,CAAC,CAAD,CAA7B;EACA,YAAME,QAAQ,GAAGF,WAAW,CAACA,WAAW,CAACnP,MAAZ,GAAqB,CAAtB,CAA5B;EACA,YAAMsP,KAAK,GAAGL,oBAAoB,KAAK,KAAvC;EAEA,YAAM3X,GAAG,GAAG8X,SAAS,CAAC9X,GAAtB;EACA,YAAMG,MAAM,GAAG4X,QAAQ,CAAC5X,MAAxB;EACA,YAAME,IAAI,GAAG2X,KAAK,GAAGF,SAAS,CAACzX,IAAb,GAAoB0X,QAAQ,CAAC1X,IAA/C;EACA,YAAMG,KAAK,GAAGwX,KAAK,GAAGF,SAAS,CAACtX,KAAb,GAAqBuX,QAAQ,CAACvX,KAAjD;EACA,YAAMoW,KAAK,GAAGpW,KAAK,GAAGH,IAAtB;EACA,YAAMwW,MAAM,GAAG1W,MAAM,GAAGH,GAAxB;EAEA,eAAO;EAACA,UAAAA,GAAG,EAAHA,GAAD;EAAMG,UAAAA,MAAM,EAANA,MAAN;EAAcE,UAAAA,IAAI,EAAJA,IAAd;EAAoBG,UAAAA,KAAK,EAALA,KAApB;EAA2BoW,UAAAA,KAAK,EAALA,KAA3B;EAAkCC,UAAAA,MAAM,EAANA;EAAlC,SAAP;EACD;;EACD,SAAK,MAAL;EACA,SAAK,OAAL;EAAc;EACZ,YAAMoB,OAAO,GAAGxC,IAAI,CAACyC,GAAL,OAAAzC,IAAI,EAAQoC,WAAW,CAACrM,GAAZ,CAAgB,UAACiM,KAAD;EAAA,iBAAWA,KAAK,CAACpX,IAAjB;EAAA,SAAhB,CAAR,CAApB;EACA,YAAM8X,QAAQ,GAAG1C,IAAI,CAAC2C,GAAL,OAAA3C,IAAI,EAAQoC,WAAW,CAACrM,GAAZ,CAAgB,UAACiM,KAAD;EAAA,iBAAWA,KAAK,CAACjX,KAAjB;EAAA,SAAhB,CAAR,CAArB;EACA,YAAM6X,YAAY,GAAGR,WAAW,CAACrb,MAAZ,CAAmB,UAACia,IAAD;EAAA,iBACtCkB,oBAAoB,KAAK,MAAzB,GACIlB,IAAI,CAACpW,IAAL,KAAc4X,OADlB,GAEIxB,IAAI,CAACjW,KAAL,KAAe2X,QAHmB;EAAA,SAAnB,CAArB;EAMA,YAAMnY,IAAG,GAAGqY,YAAY,CAAC,CAAD,CAAZ,CAAgBrY,GAA5B;EACA,YAAMG,OAAM,GAAGkY,YAAY,CAACA,YAAY,CAAC3P,MAAb,GAAsB,CAAvB,CAAZ,CAAsCvI,MAArD;EACA,YAAME,KAAI,GAAG4X,OAAb;EACA,YAAMzX,MAAK,GAAG2X,QAAd;;EACA,YAAMvB,MAAK,GAAGpW,MAAK,GAAGH,KAAtB;;EACA,YAAMwW,OAAM,GAAG1W,OAAM,GAAGH,IAAxB;;EAEA,eAAO;EAACA,UAAAA,GAAG,EAAHA,IAAD;EAAMG,UAAAA,MAAM,EAANA,OAAN;EAAcE,UAAAA,IAAI,EAAJA,KAAd;EAAoBG,UAAAA,KAAK,EAALA,MAApB;EAA2BoW,UAAAA,KAAK,EAALA,MAA3B;EAAkCC,UAAAA,MAAM,EAANA;EAAlC,SAAP;EACD;;EACD;EAAS;EACP,eAAOe,YAAP;EACD;EArCH;EAuCD;;EC9KD,IAAMhT,MAAc,GAAG;EACrBiD,EAAAA,IAAI,EAAE,QADe;EAErB7M,EAAAA,YAAY,EAAE,KAFO;EAGrBY,EAAAA,EAHqB,cAGlByG,QAHkB,EAGR;EACX,QAAOnE,SAAP,GAA4BmE,QAA5B,CAAOnE,SAAP;EAAA,QAAkB0K,MAAlB,GAA4BvG,QAA5B,CAAkBuG,MAAlB;;EAEA,aAAS0P,YAAT,GAA2D;EACzD,aAAOjW,QAAQ,CAACkI,cAAT,GACHlI,QAAQ,CAACkI,cAAT,CAAwB5L,KAAxB,CAA8B0S,QAA9B,CAAuCnT,SADpC,GAEHA,SAFJ;EAGD;;EAED,aAASqa,WAAT,CAAqBzd,KAArB,EAA6D;EAC3D,aAAOuH,QAAQ,CAAC5C,KAAT,CAAemF,MAAf,KAA0B,IAA1B,IAAkCvC,QAAQ,CAAC5C,KAAT,CAAemF,MAAf,KAA0B9J,KAAnE;EACD;;EAED,QAAI0d,WAA8B,GAAG,IAArC;EACA,QAAIC,WAA8B,GAAG,IAArC;;EAEA,aAASC,cAAT,GAAgC;EAC9B,UAAMC,cAAc,GAAGJ,WAAW,CAAC,WAAD,CAAX,GACnBD,YAAY,GAAGvJ,qBAAf,EADmB,GAEnB,IAFJ;EAGA,UAAM6J,cAAc,GAAGL,WAAW,CAAC,QAAD,CAAX,GACnB3P,MAAM,CAACmG,qBAAP,EADmB,GAEnB,IAFJ;;EAIA,UACG4J,cAAc,IAAIE,iBAAiB,CAACL,WAAD,EAAcG,cAAd,CAApC,IACCC,cAAc,IAAIC,iBAAiB,CAACJ,WAAD,EAAcG,cAAd,CAFtC,EAGE;EACA,YAAIvW,QAAQ,CAACkI,cAAb,EAA6B;EAC3BlI,UAAAA,QAAQ,CAACkI,cAAT,CAAwBuO,MAAxB;EACD;EACF;;EAEDN,MAAAA,WAAW,GAAGG,cAAd;EACAF,MAAAA,WAAW,GAAGG,cAAd;;EAEA,UAAIvW,QAAQ,CAAC1D,KAAT,CAAe+L,SAAnB,EAA8B;EAC5B0F,QAAAA,qBAAqB,CAACsI,cAAD,CAArB;EACD;EACF;;EAED,WAAO;EACLjS,MAAAA,OADK,qBACW;EACd,YAAIpE,QAAQ,CAAC5C,KAAT,CAAemF,MAAnB,EAA2B;EACzB8T,UAAAA,cAAc;EACf;EACF;EALI,KAAP;EAOD;EAnDoB,CAAvB;AAsDA;EAEA,SAASG,iBAAT,CACEE,KADF,EAEEC,KAFF,EAGW;EACT,MAAID,KAAK,IAAIC,KAAb,EAAoB;EAClB,WACED,KAAK,CAAC/Y,GAAN,KAAcgZ,KAAK,CAAChZ,GAApB,IACA+Y,KAAK,CAACvY,KAAN,KAAgBwY,KAAK,CAACxY,KADtB,IAEAuY,KAAK,CAAC5Y,MAAN,KAAiB6Y,KAAK,CAAC7Y,MAFvB,IAGA4Y,KAAK,CAAC1Y,IAAN,KAAe2Y,KAAK,CAAC3Y,IAJvB;EAMD;;EAED,SAAO,IAAP;EACD;;EChED8Q,KAAK,CAAC5J,eAAN,CAAsB;EACpBR,EAAAA,OAAO,EAAE,CAACtC,WAAD,EAAcC,YAAd,EAA4BC,iBAA5B,EAA+CC,MAA/C,CADW;EAEpBG,EAAAA,SAAS,EAAE;EAFS,CAAtB;EAKAoM,KAAK,CAACoB,eAAN,GAAwBA,eAAxB;EACApB,KAAK,CAACgD,QAAN,GAAiBA,QAAjB;EACAhD,KAAK,CAACM,OAAN,GAAgBA,OAAhB;EACAN,KAAK,CAAC8H,UAAN,GAAmBnf,WAAnB;;;;;;;;"}
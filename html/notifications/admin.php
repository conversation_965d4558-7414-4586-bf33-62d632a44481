<?
$is_print = isset($_GET['print']);
if($is_print)
{
    $noHeader = 1;
    $isPDF = true;
    include('config/header.inc.php');
?>
    <script>
        window.onload = function() { window.print(); }
    </script>
    <style>
    #logs td {
        text-align: left;
        padding: 0px 5px;
    }
    #logs th {
        text-weight: bold;
        text-align: left;
    }
    </style>    
<?
}

if(!$is_print)
{
include('config/header.inc.php');
?>
    <script>
        $(document).ready(function(){
            
            load_fiches();
            
            $(document).on('click', '#filter_btn', function(){
                $('#notifications_admin_form').toggle(400);
            });

            $("#btn_gestion").click(function () {
            	location.replace("index.php?section=notifications&module=admin");
            });
            $("#btn_consultation").click(function () {
            	location.replace("index.php?section=notifications&module=index");
            });
        });

        function load_fiches()
        {
            var fiche_id_sel = $('#fiche_id').val();
            var url = 'index.php?section=fiche&module=index';
            $.get(url, { action: 'getFichesList', term: '', batiment_id: '', fiche_type_id: '', fiche_sous_type_id: '', numero_lot: '', code_alternatif: '', id_revit: '', id_codebook: '', procedure_entretien_id: '', selected: '', disable_fitre_avance: 1 }, function(json_data){

                loading_dynamic_combobox('fiche_id', fiche_id_sel, json_data);

            }, 'json');
        }
    </script>
    <style>
    #notifications_gestion td {
        text-align: left;
        padding: 0px 5px;
    }
    #notifications_gestion th {
        text-weight: bold;
        text-align: left;
    }
    select {
        width: 210px;
    }
    </style>    
<?
}

$filtre = &$_SESSION['notifications_gestion'];
$filtre['fiche_id'] = isset($filtre['fiche_id']) ? $filtre['fiche_id'] : 0;
$filtre['formulaire_id'] = isset($filtre['formulaire_id']) ? $filtre['formulaire_id'] : 0;
$filtre['champ_id'] = isset($filtre['champ_id']) ? $filtre['champ_id'] : '';
$filtre['usager_id'] = isset($filtre['usager_id']) ? $filtre['usager_id'] : 0;
$filtre['order_by_col'] = isset($filtre['order_by_col']) ? $filtre['order_by_col'] : 'ts_ajout';
$filtre['order_by_dir'] = isset($filtre['order_by_dir']) ? $filtre['order_by_dir'] : 'desc';

$page = 1;
if(isset($_GET['page']) && intval($_GET['page']) > 0)
{
    $page = intval($_GET['page']);
}

if(isset($_POST['fiche_id']))
{
    $filtre['fiche_id'] = intval($_POST['fiche_id']);
    $page = 1;
}

if(isset($_POST['formulaire_id']))
{
    $filtre['formulaire_id'] = intval($_POST['formulaire_id']);
    $page = 1;
}

if(isset($_POST['champ_id']))
{
    $filtre['champ_id'] = $_POST['champ_id'];
    $page = 1;
}

if(isset($_POST['usager_id']))
{
    $filtre['usager_id'] = intval($_POST['usager_id']);
    $page = 1;
}

if(isset($_GET['order_by_col']) && isset($_GET['order_by_dir']))
{
    $filtre['order_by_col'] = $_GET['order_by_col'];
    $filtre['order_by_dir'] = $_GET['order_by_dir'];
    $page = 1;
}

$filtre_applique = false;
if(intval($filtre['fiche_id']) > 0 || intval($filtre['formulaire_id']) > 0 || dm_strlen($filtre['champ_id']) > 0 || intval($filtre['usager_id']) > 0)
{
    $filtre_applique = true;
}

$nb_par_page = 20;
$nb_total = NotificationsGestion::getCount($filtre);

$nb_pages = ceil($nb_total / $nb_par_page);
$limit = $page == 1 ? 0 : ($page-1) * $nb_par_page;

$sql_limit = $is_print ? '' : "LIMIT $limit, $nb_par_page";

$notifications_gestion = NotificationsGestion::getListe($filtre, $filtre['order_by_col'].' '.$filtre['order_by_dir'], $sql_limit);

if(!$is_print)
{
?>

<h1 class="ficheTitre" style="margin-bottom: 20px;">
    <?=txt('Gestion des notifications')?>
    <img src="css/images/icons/dark/filter.png" style="cursor: pointer; position: relative; top: 4px;" id="filter_btn" />
    
    <a href="index.php?section=notifications&module=admin&print=1" style="position: relative; top: 4px;" title="<?=txt("Imprimer")?>" target="iframe">
        <img src="css/images/icons/dark/printer.png" /></a>
	<div style="float: right;">
		<?
		if(GestionnaireRoute::instance()->haveAccesRoute('notifications-admin'))
		{
			?>
            <a class="btn actif" id="btn_gestion">Gestion</a>
            <?
        }
        if (GestionnaireRoute::instance()->haveAccesRoute('notifications-index')) {
            ?>
            <a class="btn" id="btn_consultation">Consultation</a>
			<?
		}
		?>
	</div>
</h1>
<form action="index.php?section=notifications&module=admin" method="post" style="margin: 0 20px 20px 0; <?=($filtre_applique ? '' : 'display: none;')?>" id="notifications_admin_form">

    <span style="margin-left: 32px;">
        <select id="fiche_id" name="fiche_id" class="lazy_loaded_fiches" disabled data-textblank="<?=txt('- Fiche -')?>">
            <option value=""><?=txt('- Fiche -')?></option>
<?
            Fiches::getOptions(array('ids' => array($filtre['fiche_id']), 'projet_id' => getProjetId(), 'no_filtre_global' => 1), $filtre['fiche_id']);
?>
        </select>
    </span>
    
    <span style="margin-left: 20px;">
        <select name="formulaire_id" style="max-width: 225px;">
            <option value=""><?=txt('- Formulaire -')?></option>
<?
        Formulaires::getOptions(array(), $filtre['formulaire_id']);
?>
        </select>
    </span>
    
    <span style="margin-left: 20px; height: 25px;">
        <select name="usager_id">
            <option value=""><?=txt('- Usager -')?></option>
<?
            Usager::getOptions(array('permission_code' => 'consultation_notifications'), $filtre['usager_id']);
?>
        </select>
    </span>
    
    <span style="margin-left: 20px; text-align: right; width: 250px;">
        
        <input type="submit" class="filtrer" name="filtrer" value="<?=txt('Filtrer')?>" style="min-height: 20px!important; padding: 0 20px;" />
        
    </span>
    
</form>
<?
}

if($is_print)
{
?>
    <h6><?=txt('Gestion des notifications')?></h6>
<?
}
?>

<table id="notifications_gestion">
<tr><th width="75" class="<?=($filtre['order_by_col'] == 'id' ? 'ordered' : '')?>">
    <?=getOrderByLink(txt('Numéro'), 'index.php?section=notifications&module=admin', 'id', $filtre['order_by_col'], $filtre['order_by_dir'], $is_print)?>
</th><th width="75" class="<?=($filtre['order_by_col'] == 'ts_ajout' ? 'ordered' : '')?>">
    <?=getOrderByLink(txt('Date ajout'), 'index.php?section=notifications&module=admin', 'ts_ajout', $filtre['order_by_col'], $filtre['order_by_dir'], $is_print)?>
</th><th width="125" class="<?=($filtre['order_by_col'] == 'usager_ajout' ? 'ordered' : '')?>">
    <?=getOrderByLink(txt('Usager ajout'), 'index.php?section=notifications&module=admin', 'usager_ajout', $filtre['order_by_col'], $filtre['order_by_dir'], $is_print)?>
</th><th width="75" class="<?=($filtre['order_by_col'] == 'formulaire' ? 'ordered' : '')?>">
    <?=getOrderByLink(txt('Formulaire'), 'index.php?section=notifications&module=admin', 'formulaire', $filtre['order_by_col'], $filtre['order_by_dir'], $is_print)?>
</th><th width="225">
	<?=txt('Champs(s) liée(s)')?>
</th><th width="225">
	<?=txt('Fiche(s) liée(s)')?>
</th><th width="225">
	<?=txt('Usager(s) à notifier')?>
</th>
<?
if(!$is_print)
{
?>
    <th width="77" style="text-align: center!important;">
        <a href="index.php?section=admin&module=edit&table=notifications_gestion&type=client&id=0" class="fancy">
            <img src="css/images/icons/dark/plus.png" /></a>
    </th>
<?
}

$parametres_intrinseques = FormulairesListesParametres::getListe(array('use_id_as_key' => 1));
?>
</tr>
<?
$champsFiltresSpeciauxEquipementsBioMedicaux = Config::instance()->getChampsFiltresSpeciauxEquipementsBioMedicaux(true);
foreach($notifications_gestion as $i => $notif)
{
    if (!empty($notif->formulaire_id)) {
        $formulaire = $notif->formulaire;
        $metas = $formulaire::getFlatChampsSaisieMetaData();
    } else {
        $formulaireCustom = new FormulairesCustoms($notif->formulaire_custom_id);
        $metas = $formulaireCustom->getMetas();
    }
	?>
	<tr><td class="<?=($filtre['order_by_col'] == 'id' ? 'ordered' : '')?>">
		<?=$notif->getCodeNotification()?>
	</td><td class="<?=($filtre['order_by_col'] == 'ts_ajout' ? 'ordered' : '')?>">
        <?=dm_substr($notif->ts_ajout, 0, 10)?>
    </td><td class="<?=($filtre['order_by_col'] == 'usager_ajout' ? 'ordered' : '')?>">
		<?=$notif->usager_ajout?>
	</td><td class="<?=($filtre['order_by_col'] == 'formulaire' ? 'ordered' : '')?>">
		<?= (isset($formulaire) ? $formulaire::TITRE : $formulaireCustom->nom_formulaire) ?>
	</td><td>
<?
        if(isset($notif->champs) && is_array($notif->champs) && count($notif->champs) > 0)
        {
            $iter = 0;
            foreach($notif->champs as $i => $champ)
            {
	            $is_param_intrinseque = false;
	            $exploded_champ_nom = dm_explode('__', $champ->champ_id);
	            if($exploded_champ_nom[0] == 'intrinseque')
	            {
		            $champ_nom_intrinseque = $exploded_champ_nom[1];
		            $valeur_id_intrinseque = $exploded_champ_nom[2];
		            $is_param_intrinseque = true;
	            }
                if (isset($champsFiltresSpeciauxEquipementsBioMedicaux[$champ->champ_id])) {
                    $titre_champ = $champsFiltresSpeciauxEquipementsBioMedicaux[$champ->champ_id]['titre'];
                }elseif ($is_param_intrinseque) {
	                $titre_champ = $parametres_intrinseques[$valeur_id_intrinseque]->nom_parametre.txt(' [Paramètres intrinsèques]');
                } else {
                    $key = dm_str_replace('_id', '', $champ->champ_id);
                    $titre_champ = isset($metas[$key]) ? $metas[$key]['titre'] : '';
	            }
                print ($iter == 0 ? '' : ', ').$titre_champ;
                if($iter == 2)
                {
                    break;
                }
                $iter++;
            }
            if(count($notif->champs) > 3)
            {
                print '... <span style="font-weight: bold; color: blue;">('.count($notif->champs).')</span>';
            }
        }
?>
	</td><td>
<?
        if(isset($notif->fiches) && is_array($notif->fiches) && count($notif->fiches) > 0)
        {
            $iter = 0;
            foreach($notif->fiches as $i => $fiche)
            {
                print ($iter == 0 ? '' : ', ').$fiche->code;
                if($iter == 2)
                {
                    break;
                }
                $iter++;
            }
            if(count($notif->fiches) > 3)
            {
                print '... <span style="font-weight: bold; color: blue;">('.count($notif->fiches).')</span>';
            }
        }
        else
        {
            print txt('Toutes');
        }
?>
	</td><td>
<?
        if(isset($notif->usagers) && is_array($notif->usagers) && count($notif->usagers) > 0)
        {
            $iter = 0;
            foreach($notif->usagers as $i => $usager)
            {
                print ($iter == 0 ? '' : ', ').$usager->usager;
                $iter++;
            }
        }
?>
	</td>
<?
        if(!$is_print)
        {
?>
            <td style="height: 26px; text-align: center!important;">
                
                <a href="index.php?section=admin&module=edit&table=notifications_gestion&type=client&id=<?=$notif->id?>" class="fancy">
                    <img src="css/images/icons/dark/create_write.png"></a>
                <a href="javascript: void(0);" class="dle_btn dle_delete_generique" tableName="notifications_gestion" idElement="<?=$notif->id?>">
                    <img src="css/images/icons/dark/trashcan.png" /></a>
                
            </td>
<?
        }
?>
	</tr>
	<?
}
?>
</table>
<?
if(!$is_print)
{
	showPaginateur('index.php?section=notifications&module=admin', $limit, $nb_total, $nb_pages, $nb_par_page, $page);
}
?>
<iframe id="iframe" name="iframe" style="display: none; width: 100%;"></iframe>
<br style="clear: both;" />
<?
include('config/footer.inc.php');
?>
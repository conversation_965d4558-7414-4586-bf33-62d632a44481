<?
include('config/inc.php');
require_once 'dompdf/autoload.inc.php';

// reference the Dompdf namespace
use Dompdf\Dompdf;

// instantiate and use the dompdf class
$dompdf = new Dompdf();

ob_start();

$iter = 0;
$fiches = Fiches::getListe(array('no_filtre_global' => 1));
foreach($fiches as $fiche)
{
    if($iter == 10)
        break;
    
    $_GET['multi'] = 1;
    $_GET['id'] = $fiche->id;
    $_GET['pdf'] = 1;
    include('fiche/fiche.php');
    $iter++;
}

$html = ob_get_clean();
ob_end_clean();

$dompdf->loadHtml($html);

// (Optional) Setup the paper size and orientation
$dompdf->setPaper('A4', 'portrait');

// Render the HTML as PDF
$dompdf->render();

// Output the generated PDF to Browser
$dompdf->stream();
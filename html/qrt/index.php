<?
$is_export = isset($_GET['export']);
$is_exportBCF = isset($_GET['exportBCF']);
$is_print = isset($_GET['print']);

$defaultOrderBy = 'nb_notifications_all desc, statut, ts_ajout desc';
$filtre = &$_SESSION['filtre_qrts'];
$filtre['terme'] = isset($filtre['terme']) ? $filtre['terme'] : '';
$filtre['question_statut_id'] = isset($filtre['question_statut_id']) ? $filtre['question_statut_id'] : 0;
$filtre['usager_demandeur_id'] = isset($filtre['usager_demandeur_id']) ? $filtre['usager_demandeur_id'] : 0;
$filtre['usager_repondant_id'] = isset($filtre['usager_repondant_id']) ? $filtre['usager_repondant_id'] : 0;
$filtre['niveau'] = isset($filtre['niveau']) ? $filtre['niveau'] : array();
$filtre['question_categorie_id'] = isset($filtre['question_categorie_id']) ? $filtre['question_categorie_id'] : 0;
$filtre['formulaire_id'] = isset($filtre['formulaire_id']) ? $filtre['formulaire_id'] : 0;
$filtre['liste_item_id'] = isset($filtre['liste_item_id']) ? $filtre['liste_item_id'] : 0;
$filtre['is_prioritaire'] = isset($filtre['is_prioritaire']) ? $filtre['is_prioritaire'] : -1;
$filtre['order_by_col'] = isset($filtre['order_by_col']) ? $filtre['order_by_col'] : $defaultOrderBy;
$filtre['order_by_dir'] = isset($filtre['order_by_dir']) ? $filtre['order_by_dir'] : '';

$filtre['selection_items'] = isset($_POST['selection_items']) ? $_POST['selection_items'] : '';
$page = 1;
if(isset($_GET['page']) && intval($_GET['page']) > 0)
{
	$page = intval($_GET['page']);
}

if(isset($_POST['terme']))
{
	$filtre['terme'] = $_POST['terme'];
	$page = 1;
}

if(isset($_POST['niveau']))
{
	$filtre['niveau'] = $_POST['niveau'];
	$page = 1;
}

if(isset($_POST['question_statut_id']))
{
	$filtre['question_statut_id'] = intval($_POST['question_statut_id']);
	$page = 1;
}

if(isset($_POST['usager_demandeur_id']))
{
	$filtre['usager_demandeur_id'] = intval($_POST['usager_demandeur_id']);
	$page = 1;
}

if(isset($_POST['usager_repondant_id']))
{
	$filtre['usager_repondant_id'] = intval($_POST['usager_repondant_id']);
	$page = 1;
}

if(isset($_POST['question_categorie_id']))
{
	$filtre['question_categorie_id'] = intval($_POST['question_categorie_id']);
	$page = 1;
}

if(isset($_POST['is_prioritaire']))
{
	$filtre['is_prioritaire'] = intval($_POST['is_prioritaire']);
	$page = 1;
}

if(isset($_POST['formulaire_id']))
{
	$filtre['formulaire_id'] = intval($_POST['formulaire_id']);
	$page = 1;
}

if(isset($_POST['liste_item_id']))
{
	$filtre['liste_item_id'] = intval($_POST['liste_item_id']);
	$page = 1;
}

if(isset($_GET['order_by_col']) && isset($_GET['order_by_dir']))
{
	$filtre['order_by_col'] = $_GET['order_by_col'];
	$filtre['order_by_dir'] = $_GET['order_by_dir'];
	$page = 1;
}

$filtre_applique = false;
if(dm_strlen($filtre['terme']) > 0)
{
	$filtre_applique = true;
}
if(count($filtre['niveau']) > 0)
{
	foreach($filtre['niveau'] as $niveau)
	{
		if((int)$niveau > 0)
		{
			$filtre_applique = true;
		}
	}
}
if(intval($filtre['question_statut_id']) > 0)
{
	$filtre_applique = true;
}
if(intval($filtre['usager_demandeur_id']) > 0)
{
	$filtre_applique = true;
}
if(intval($filtre['question_categorie_id']) > 0)
{
	$filtre_applique = true;
}
if(intval($filtre['is_prioritaire']) > -1)
{
	$filtre_applique = true;
}
if(intval($filtre['formulaire_id']) > 0)
{
	$filtre_applique = true;
}
if(intval($filtre['liste_item_id']) > 0)
{
	$filtre_applique = true;
}
if(havePermission('demandeur_qrt_bcf'))
{
	if(intval($filtre['usager_repondant_id']) > 0)
	{
		$filtre_applique = true;
	}
}

$niveaux = BatimentsNiveaux::getListeReel(3);
if($is_exportBCF) {
    Questions::getExportBCF($filtre);
    exit;
} elseif($is_export) {
	Questions::getExport($filtre);
	exit;
}
else {
	$nb_par_page = 20;
	$nb_total = Questions::getCount($filtre);

	$nb_pages = ceil($nb_total / $nb_par_page);
	$limit = $page == 1 ? 0 : ($page - 1) * $nb_par_page;

	$sql_limit = $is_print ? '' : "LIMIT $limit, $nb_par_page";

	$questions = Questions::getListe($filtre, $filtre['order_by_col'] . ' ' . $filtre['order_by_dir'], $sql_limit);
}

if($is_print)
{
	$noHeader = 1;
	$isPDF = true;
	include('config/header.inc.php');
	?>
	<script>
        window.onload = function() { window.print(); }
	</script>
	<style>
		#logs td {
			text-align: left;
			padding: 0px 5px;
		}
		#logs th {
			text-weight: bold;
			text-align: left;
		}
	</style>
	<?
}

if(!$is_print)
{
	include('config/header.inc.php');
	?>
	<script>
        $(document).ready(function(){

            $(document).on('click', '#filter_btn', function(){

                $('#qrt_filter_form_bloc').toggle(400);

            });

			$('#qrt_filter_form').find('select.niveaux').find('option.to_be_span').replaceTagName('span');

			$('#qrt_filter_form').on('change', 'select.niveaux', function(){

				let _that = this;
				setTimeout(function(){
					changeNiveau(_that);
				}, 100);

			});

			$('#formulaire_id').change(function(){
				if (this.value !== '') {
					$.get('index.php', {
						action: 'get_list_item_ids_from_formulaire_id',
						formulaire_id: this.value
					}, function (options) {
						$('#liste_item_id').html(options);
						$('#liste_item_id').select2({width: '210px'});
					});
				} else {
					$('#liste_item_id').html('');
					$('#liste_item_id').select2({width: '210px'});
				}
			});
			$('#liste_item_id').select2({width: '210px'});

			$(document).on('click', '#exporter', function () {
				$.startObjectLoader($('body'), 'export_loader');

				fetch('index.php?section=qrt&module=index&export=1', {
					method: 'GET'
				})
					.then(response => response.blob())
					.then(blob => {
						var url = window.URL.createObjectURL(blob);
						var a = document.createElement('a');
						a.href = url;
						a.download = "Qrt.xlsx";
						document.body.appendChild(a); // we need to append the element to the dom -> otherwise it will not work in firefox
						a.click();
						a.remove();  //afterwards we remove the element again
						$.stopObjectLoader('export_loader');
					});

				return false;
			});

			$(document).on('click', '#exporterBCF', function () {
				$.startObjectLoader($('body'), 'export_loader');

				fetch('index.php?section=qrt&module=index&exportBCF=1', {
					method: 'GET'
				})
					.then(response => response.blob())
					.then(blob => {
						var url = window.URL.createObjectURL(blob);
						var a = document.createElement('a');
						a.href = url;
						a.download = "Qrt.bcf";
						document.body.appendChild(a); // we need to append the element to the dom -> otherwise it will not work in firefox
						a.click();
						a.remove();  //afterwards we remove the element again
						$.stopObjectLoader('export_loader');
					});

				return false;
			});
        });

		function changeNiveau(obj)
		{
			var niveau_enfant_id = $(obj).data('niveau_enfant_id');

			while($('#niveau_' + niveau_enfant_id).length)
			{
				$('#niveau_' + niveau_enfant_id).val('');
				$('#niveau_' + niveau_enfant_id).find('.all').replaceTagName('span').hide();
				niveau_enfant_id = $('#niveau_' + niveau_enfant_id).data('niveau_enfant_id');
			}

			var parent_element_id = 0;
			$('#qrt_filter_form').find('select.niveaux').each(function(){

				if(this.value.length)
				{
					parent_element_id = this.value;
				}

			});

			var niveau_enfant_id = $(obj).data('niveau_enfant_id');

			while($('#niveau_' + niveau_enfant_id).length)
			{
				$('#niveau_' + niveau_enfant_id).val('');
				$('#s2id_' + $('#niveau_' + niveau_enfant_id).attr('id')).find('.select2-chosen').html($('#niveau_' + niveau_enfant_id).find('option:first-child').text());

				if(parent_element_id == 0)
				{
					$('#niveau_' + niveau_enfant_id).find('.all').replaceTagName('option').show();
				}
				else
				{
					$('#niveau_' + niveau_enfant_id).find('.parent_niveau_id_' + parent_element_id).replaceTagName('option').show();
				}

				$('#niveau_' + niveau_enfant_id).val('');

				niveau_enfant_id = $('#niveau_' + niveau_enfant_id).data('niveau_enfant_id');
			}
		}
	</script>
	<style>
		#table_principale td {
			text-align: left;
			padding: 0px 5px;
		}
		#table_principale th {
			text-weight: bold;
			text-align: left;
		}
		td.crochet {
			padding-top: 6px!important;
			text-align: center!important;
		}
		th.crochet {
			padding-top: 12px;
			text-align: center!important;
		}
		select, input {
			width: 210px;
            box-sizing: border-box;
		}
	</style>
	<?
}

if(!$is_print)
{
	?>

	<h1 class="ficheTitre" style="margin-bottom: 20px;">

		<?=txt('Gestion des QRT-BCF')?>
		<img src="css/images/icons/dark/filter.png" style="cursor: pointer; position: relative; top: 4px;" id="filter_btn" />

        <a href="index.php?section=qrt&module=index&print=1" style="position: relative; top: 4px;" title="<?=txt("Imprimer")?>" target="iframe">
            <img src="css/images/icons/dark/printer.png" /></a>

        <!--a href="index.php?section=qrt&module=index&export=1" style="position: relative; top: 1px; left: 6px;" title="<?=txt("Exporter")?>" target="iframe"-->
        <a style="position: relative; top: 1px; left: 6px;" id="exporter" title="<?=txt("Exporter")?>">
            <img src="css/images/icons/dark/DM_export_CSV.png" /></a>

        <a style="position: relative; top: 1px; left: 16px;" id="exporterBCF" title="<?=txt("BCF")?>">
            <img src="css/images/icons/dark/BCF.png" width="18" /></a>

        <?
        QuestionsGroupesUsagers::afficheOnglets();
        ?>

	</h1>
	<form action="index.php?section=qrt&module=index" method="post" id="qrt_filter_form">
		<div style="margin: 0 20px 20px 0; <?=($filtre_applique ? '' : 'display: none;')?>" id="qrt_filter_form_bloc">
    <div style="height: 25px;">

<?
if(count($niveaux) > 0)
{
?>
    <div style="margin-left:;">
<?
        $niveau_parent_id = -1;
        foreach($niveaux as $i => $niveau)
        {
?>
            <span style="margin-left: <?=($i == 0 ? '' : '20px')?>;">
                <select id="niveau_<?=$niveau->id?>" name="niveau[<?=$niveau->id?>]" class="niveaux" data-niveau_id="<?=$niveau->id?>"
                    data-niveau_enfant_id="<?=(isset($niveaux[($i+1)]) ? $niveaux[($i+1)]->id : -1)?>">
                    <option value=""><?=('- '.$niveau->nom.' -')?></option>
<?
                    foreach($niveau->batiments_niveaux_elements as $element)
                    {
                        $sel = (isset($filtre['niveau'][$niveau->id]) && $filtre['niveau'][$niveau->id] == $element['id'] ? ' selected="selected"' : '');
                        $display = 'none';
                        if($niveau_parent_id == -1 || isset($filtre['niveau'][$niveau_parent_id]) && $filtre['niveau'][$niveau_parent_id] == $element['batiment_niveau_element_id'])
                        {
                            $display = '';
                        }

	                    if ($i < 3 || dm_strlen($sel) > 0) {
		                    ?>
                            <option value="<?=$element['id']?>"
                                    style="display: <?=$display?>;"
                                    class="all parent_niveau_id_<?=(isset($element['batiment_niveau_element_id']) ? $element['batiment_niveau_element_id'] : '')?>"<?=$sel?>>
                                <?=$element['nom']?></option>
		                    <?php
	                    } else {
		                    $options_differees['niveau_' . $niveau->id][] = '<option value="'.$element['id'].'"
                            style="display: '.$display.';"
                            class="all parent_niveau_id_'.(isset($element['batiment_niveau_element_id']) ? $element['batiment_niveau_element_id'] : '').'"'.$sel.'>
                            '.$element['nom'].'</option>';
	                    }
                    }
?>
                </select>
            </span>
<?
            $niveau_parent_id = $niveau->id;
        }
?>
        <span style="margin-left: 20px; height: 25px;">
            <select name="is_prioritaire">
                <option value="-1"<?=($filtre['is_prioritaire'] == -1 ? ' selected="selected"' : '')?>><?=txt('- Priorité -')?></option>
                <option value="1"<?=($filtre['is_prioritaire'] == 1 ? ' selected="selected"' : '')?>><?=txt('Prioritaires seulement')?></option>
                <option value="0"<?=($filtre['is_prioritaire'] == 0 ? ' selected="selected"' : '')?>><?=txt('Non-prioritaires seulement')?></option>
            </select>
        </span>
    </div>
<?
}
?>
        </div>
		<br style="clear: both;">
        <input type="text" name="terme" value="<?=($filtre['terme'])?>" style="width: 210px; height: 23px!important;" placeholder="Recherche" />

        <span style="margin-left: 20px; height: 25px;">
            <select name="question_statut_id">
                <option value=""><?=txt('- Statut -')?></option>
                <?
                QuestionsStatuts::getOptions(array(), $filtre['question_statut_id']);
                ?>
            </select>
        </span>
		<?
		//if(isMaster() || havePermission('demandeur_qrt'))
		{
			?>
			<span style="margin-left: 20px; height: 25px;">
                <select name="usager_demandeur_id">
                    <option value=""><?=txt('- Demandeur -')?></option>
                    <?
                    Usager::getOptions([], $filtre['usager_demandeur_id']);
                    ?>
                </select>
            </span>
			<?
		}
		//if(isMaster() || havePermission('demandeur_qrt'))
		{
			?>
			<span style="margin-left: 20px; height: 25px;">
                <select name="usager_repondant_id">
                    <option value=""><?=txt('- Répondant -')?></option>
                    <?
                    Usager::getOptions([], $filtre['usager_repondant_id']);
                    ?>
                </select>
            </span>
			<?
		}
		?>
        <span style="margin-left: 20px; height: 25px;">
            <select name="question_categorie_id">
                <option value=""><?=txt('- Catégorie -')?></option>
                <?
                QuestionsCategories::getOptions(array(), $filtre['question_categorie_id']);
                ?>
            </select>
        </span>

        <br style="clear: both;">
        <br style="clear: both;">

        <span style="height: 25px; position: relative; top: 3px;">
            <select id="formulaire_id" name="formulaire_id">
                <option value="">- <?=txt('Formulaire')?> -</option>
	            <?=Formulaires::getOptions(array('ids' => Formulaires::getFormulairesIdWithQuantite()), $filtre['formulaire_id'])?>
            </select>
        </span>

        <span style="margin-left: 20px; position: relative; top: 1px;">
            <select id="liste_item_id" name="liste_item_id">
                <?
                if ($filtre['formulaire_id'] > 0) {
	                $form = new Formulaires($filtre['formulaire_id']);
	                $first_champ = $form->getFirstChamp();
	                $class_first_champ = $first_champ['classe'];
	                ?>
                    <option value="">- <?=$first_champ['titre']?> -</option>
	                <?
	                $class_first_champ::getOptions([], $filtre['liste_item_id']);
                }
                ?>
            </select>
        </span>

        <span style="margin-left: 20px; text-align: right; width: 250px;">
            <input type="submit" class="filtrer" name="filtrer" value="<?=txt('Filtrer')?>" style="min-height: 20px!important; padding: 0 20px;" />

            <a href="javascript: void(0);" id="clear_filtre" title="<?=txt('Libérer tous les filtres')?>">
                <img src="css/images/icons/dark/bended_arrow_left.png" style="vertical-align:bottom;" /></a>
            <a href="" id="refresh" title="<?=txt('Rafraîchir la page')?>">
                <img src="css/images/icons/dark/refresh_3.png" style="vertical-align:bottom;" /></a>
        </span>
            <br style="clear: both;">
	</div>
	<!--/form-->
    <br>
	<?
}

if($is_print)
{
	?>
	<h6><?=txt('Gestion des QRT-BCF')?></h6>
	<?
}
if ($filtre['order_by_col'] <> $defaultOrderBy) {
	?>
    <a href="index.php?section=qrt&module=index&order_by_col=<?= urlencode($defaultOrderBy) ?>&order_by_dir=" style="float: right; margin: 0 10px 10px 0; text-decoration: underline"><?= txt('Remettre les non-lus en premier') ?></a>
	<?
}
?>
	<!--form id="form_items"-->
	<table id="table_principale">
		<tr><th class="crochet"><input type="checkbox" id="selectionner_tout" name="selectionner_tout" value="1" />
			</th><th class="<?=($filtre['order_by_col'] == 'id' ? 'ordered' : '')?>" style="width: 90px;">
				<?=getOrderByLink(txt('Numéro QRT-BCF'), 'index.php?section=qrt&module=index', 'code', $filtre['order_by_col'], $filtre['order_by_dir'])?>
			</th><th class="<?=($filtre['order_by_col'] == 'question_categorie' ? 'ordered' : '')?>" style="width: 100px;">
				<?=txt('Catégorie')?>
            </th><th class="<?=($filtre['order_by_col'] == 'fiche_code' ? 'ordered' : '')?>">
				<?=txt('Fiche(s)')?>
			</th>
            </th><th>
				<?=txt('Référence(s) antécédente(s)')?>
            </th>
			<th>
				<?=txt('Équipement')?>
			</th>
            <th>
				<?=txt('Local d\'appartenance')?>
            </th>
            <th>
				<?=txt('Discipline')?>
            </th>
            <th class="<?=($filtre['order_by_col'] == 'sujet' ? 'ordered' : '')?>">
				<?=txt('Sujet')?>
            </th>
			<th class="<?=($filtre['order_by_col'] == 'demandeur' ? 'ordered' : '')?>" style="width: 90px;">
				<?=getOrderByLink(txt('Demandeur'), 'index.php?section=qrt&module=index', 'demandeur', $filtre['order_by_col'], $filtre['order_by_dir'])?>
			</th>
			<th>
				<?=txt('Messages')?>
			</th>
			<th class="<?=($filtre['order_by_col'] == 'statut' ? 'ordered' : '')?>" style="width: 60px;">
				<?=getOrderByLink(txt('Statut'), 'index.php?section=qrt&module=index', 'statut', $filtre['order_by_col'], $filtre['order_by_dir'])?>
			</th>
			<?
			if(!$is_print)
			{
				?>
				<th width="85" style="text-align: center!important;">
					<?
					if(havePermission('demandeur_qrt_bcf'))
					{
						?>
						<a href="index.php?section=admin&module=edit&table=questions&type=client&id=0" class="fancy">
							<img src="css/images/icons/dark/plus.png" /></a>
						<?
					}
					?>
				</th>
				<?
			}
			?>
		</tr>
		<?
		foreach($questions as $i => $question)
		{
			?>
			<tr<? if ($question->is_prioritaire == 1) echo ' style="background-color:#fef2d5;"' ?>><td class="crochet">
					<input type="checkbox" class="selection_items" name="selection_items[]" value="<?=$question->id?>" />
				</td><td class="<?=($filtre['order_by_col'] == 'code' ? 'ordered' : '')?>">
					<?=$question->code?>
				</td><td class="<?=($filtre['order_by_col'] == 'question_categorie' ? 'ordered' : '')?>">
					<?=$question->question_categorie?>
                </td><td class="<?=($filtre['order_by_col'] == 'fiche_code' ? 'ordered' : '')?>">
					<?
					if(dm_strlen($question->fiche_code) > 0)
                    {
                        print '<div class="tooltip_struct" data-fiche_id="'.$question->fiche_id.'"><a href="index.php?section=fiche&module=index&id='.$question->fiche_id.'">'.$question->fiche_code.'</a></div>';
                    }
                    else if(isset($question->fiches) && is_array($question->fiches) && count($question->fiches) <= 5)
                    {
                        $iter = 0;
                        foreach($question->fiches as $i => $fiche)
                        {
                            print '<div class="tooltip_struct" data-fiche_id="'.$fiche['id'].'"><a href="index.php?section=fiche&module=index&id='.$fiche['id'].'">'.$fiche['code'].'</a></div>';
                            $iter++;
                        }
                    }
                    else
                    {
	                    print txt('Multiple');
                    }
                    ?>
				</td>
                </td><td>
					<?
					if(dm_strlen($question->fiche_code_deleted) > 0)
					{
						print $question->fiche_code_deleted;
					}
					else if(isset($question->fiches_deleted) && is_array($question->fiches))
					{
						$iter = 0;
						foreach($question->fiches_deleted as $i => $fiche)
						{
							print ($iter == 0 ? '' : ', ').$fiche['fiche_code'];
							$iter++;
						}
					}
					?>
                </td>
                <td>
	                <?
	                if(isset($question->classe_formulaire) && dm_strlen($question->classe_formulaire) > 0)
	                {
		                if($question->classe_formulaire == 'EquipementsBioMedicaux')
		                {
			                $classNameItem = $tables['client'][$question->nom_table]['className'];
			                $item = new $classNameItem($question->liste_item_id);
			                print $item->nom.' - '.$item->description;
		                }
		                else if($question->classe_formulaire == 'GenerauxEquipements')
		                {
			                if($question->fiche_id > 0) {
				                $fiche = new Fiches($question->fiche_id);
				                print $fiche->code;
			                }
		                }
	                }
	                else
	                {
		                print 'N/A';
	                }
	                ?>
                </td>
                <td>
	                <?
	                if(isset($question->classe_formulaire) && dm_strlen($question->classe_formulaire) > 0)
	                {
		                if($question->classe_formulaire == 'EquipementsBioMedicaux')
		                {
		                    if($question->fiche_id > 0)
                            {
                                $fiche = new Fiches($question->fiche_id);
                                print $fiche->code;
                            }
		                }
		                else if($question->classe_formulaire == 'GenerauxEquipements')
		                {
			                if($question->fiche_id > 0) {
                                $fiche = new Fiches($question->fiche_id);
                                $fiche_parent = null;
                                if($fiche->fiche_local_id > 0) {
                                    $fiche_parent = new Fiches($fiche->fiche_local_id);
                                    print $fiche_parent->code;
                                }
			                }
		                }
	                }
	                else
	                {
		                print 'N/A';
	                }
	                ?>
                </td>
                <td>
	                <?
	                if(isset($question->classe_formulaire) && dm_strlen($question->classe_formulaire) > 0)
	                {
		                if($question->classe_formulaire == 'EquipementsBioMedicaux' && $question->discipline_id > 0)
		                {
			                $discipline = new Discipline($question->discipline_id);
			                print $discipline->nom;
		                }
		                else if($question->classe_formulaire == 'GenerauxEquipements')
		                {
			                $discipline = new Discipline($question->discipline_id);
			                print $discipline->nom;
		                }
	                }
	                else
	                {
		                print 'N/A';
	                }
	                ?>
                </td>
				<td class="<?=($filtre['order_by_col'] == 'sujet' ? 'ordered' : '')?>">
					<?=dm_nl2br($question->sujet)?>
				</td>
				<td class="<?=($filtre['order_by_col'] == 'demandeur' ? 'ordered' : '')?>">
					<?=$question->demandeur?>
				</td>
				<td>
					<span id="nb_reponse_<?=$question->id?>"><?=count($question->reponses).'</span> '.txt('messages')?>
				</td>
				<td class="<?=($filtre['order_by_col'] == 'statut' ? 'ordered' : '')?>" style="<? if ($question->statut == txt('En retard')) echo 'color:#FF0000;' ?>">
					<?=$question->statut?>
				</td>
				<?

				if(!$is_print)
				{
					?>
					<td style="height: 26px; text-align: center!important; position: relative;">
						<?
						if(havePermission(array('demandeur_qrt_bcf', 'admin_qrt_bcf')))
						{
						    $image = $question->usager_ajout_id == getIdUsager() || isMaster() || havePermission(array('admin_qrt_bcf')) ? 'create_write' : 'magnifying_glass';
							?>
							<a href="index.php?section=admin&module=edit&table=questions&type=client&id=<?=$question->id?>" class="fancy">
								<img src="css/images/icons/dark/<?=$image?>.png"></a>
<?
                            if((count($question->reponses) == 0 && $question->usager_ajout_id == getIdUsager()) || isMaster())
                            {
?>
                                <a href="javascript: void(0);" class="dle_btn dle_delete_generique" tableName="questions" idElement="<?=$question->id?>">
                                    <img src="css/images/icons/dark/trashcan.png" /></a>
<?
                            }
						}
						else
						{
							?>
							<a href="index.php?section=admin&module=edit&table=questions&type=client&id=<?=$question->id?>" class="fancy">
								<img src="css/images/icons/dark/magnifying_glass.png"></a>
							<?
						}

						?><a href="javascript: void(0);" style="position: relative;" class="start_qrt" data-qrt_id="<?=$question->id?>" data-context="qrt_<?=$question->id?>">
                            <img src="css/images/icons/dark/speech_bubble.png" /><?=($question->nb_notifications_all > 0 ? '<span class="extra">'.$question->nb_notifications_all.'</span>' : '')?></a><?

                            $documents = DocumentsListesElements::getListe(['nom_table' => 'questions', 'liste_item_id' => $question->id]);
                            if (count($documents) > 0) {
                                foreach ($documents as $i => $document) {
                                    ?>
                                    <a href="index.php?action=download_document&id=<?=$document->id?>" target="_blank">
                                        <img src="css/images/icons/dark/image.png" /></a>
                                    <?
                                    break;
                                }
                            }
                        ?>
					</td>
					<?
				}
				?>
			</tr>
            <tr>
                <td colspan="12" style="padding: 0px!important; background-color: grey!important;">

                    <div id="qrt_<?=$question->id?>" class="qrt" style=" display: none; margin: 25px; position: relative;">

                    </div>

                </td>
            </tr>
			<?
		}
		?>
	</table>
	</form>
<?
if(!$is_print)
{
	showPaginateur('index.php?section=qrt&module=index', $limit, $nb_total, $nb_pages, $nb_par_page, $page);
}
?>
	<iframe id="iframe" name="iframe" style="display: none; width: 100%;"></iframe>
	<br style="clear: both;" />
<?
if(isset($options_differees) && count($options_differees) > 0) {
	?>
    <options_differees style="display: none;">
		<?php
		foreach ($options_differees as $key => $options) {
			?>
            <span data-idselect="<?= $key ?>">
                    <?php
                    foreach ($options as $i => $option) {
	                    print $option . "\n";
                    }
                    ?>
                </span>
			<?php
		}
		?>
    </options_differees>
	<?php
}
include('config/footer.inc.php');
?>
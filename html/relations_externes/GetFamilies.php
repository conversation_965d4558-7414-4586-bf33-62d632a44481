<?
	chdir('../');
	include('config/inc.php');
	
	header('Content-Type: application/json; charset=utf-8');

	$projet_id = (int)$_GET['projet_id'];
	$source = $_GET['source'];
	
	if ($source == 'Revit2') {
		$sql = "SELECT rf2.id as Id, rf2.nom as Name 
				FROM rvt_familles rf2 
				WHERE is_associable = 1 and 
					exists (
						select 1 
						from rvt_relations_familles 
						where rvt_relations_familles.rvt_famille_id = rf2.id and dm_fiche_sous_type_id <> -1
					)
				ORDER BY Name";
		$result = $db->getAll($sql, []);

		$families = array();
		foreach ($result as $row) {
			array_push($families, $row);
		}

		echo(json_encode($families));
	} else if ($source == 'Revit') {
		$sql = "SELECT rf2.id as Id, rf2.nom as Name 
				FROM rvt_familles rf2 
				WHERE is_associable = 1 and 
					exists (
						select 1 
						from rvt_relations_familles 
						where rvt_relations_familles.rvt_famille_id = rf2.id and dm_fiche_sous_type_id <> -1
					) 
                    and 
                    exists (
                        SELECT ret.Id
                        FROM rvt_types rt
                        JOIN rvt_installations ret on ret.rvt_type_id = rt.id
                        WHERE rt.rvt_famille_id = rf2.id AND ret.dm_projet_id = ? AND 
                            NOT EXISTS (
                                SELECT 1 
                                FROM rvt_relations_installations  rri
                                WHERE rri.rvt_type_id = rt.id
                            )
                        limit 1
                    )
				ORDER BY Name";
		$result = $db->getAll($sql, array($projet_id));

		$families = array();
		foreach ($result as $row) {
			array_push($families, $row);
		}

		echo(json_encode($families));
	} else if ($source == 'DocMatic') {
		$without = isset($_GET['wq']) && intval($_GET['wq']) == 1 ? false : true;
		$form_ids_wq = Formulaires::getFormulairesIdWithQuantite($without, intval($_GET['projet_id']));
		$justLists = isset($_GET['justLists']);
		$liste_types = $justLists ? [3, 5, 6] : [1, 2];
		if (isset($_GET['dm_fiche_sous_type_id'])) {
			$sql = "SELECT distinct f.id as Id, f.nom as classeForm 
					FROM formulaires f
					join formulaires_fiches_sous_types ffst on ffst.formulaire_id = f.id
					join rvt_relations_familles tfr on tfr.dm_fiche_sous_type_id = ffst.fiche_sous_type_id
					WHERE ffst.fiche_sous_type_id = ? and f.type_formulaire_id in (" . dm_implode(', ', $liste_types) . ")";
			$result = $db->getAll($sql, array(intval($_GET['dm_fiche_sous_type_id'])));

			$families = array();
			foreach ($result as $row) {
				if (in_array($row['Id'], $form_ids_wq)) {
					$class = $row['classeForm'];
					$nomForm = $class::TITRE;
					$row['Name'] = $nomForm;
					array_push($families, $row);
				}
			}
			uasort ($families, 'custom_sort_name');
			$families = array_values($families);

			echo(json_encode($families));
		}
	}
<?
	chdir('../');
	include('config/inc.php');
	
	if(isset($_POST['dm_fiche_sous_type_id']) && isset($_POST['rvt_famille_id'])) {
		$projet_id = $_GET['projet_id'];
		$dm_fiche_sous_type_id = intval($_POST['dm_fiche_sous_type_id']);
		$rvt_famille_id = $_POST['rvt_famille_id'];

		$db->autocommit(false);

		$db->query("INSERT INTO rvt_relations_familles (rvt_famille_id, dm_fiche_sous_type_id) 
							VALUES (?, ?) 
							ON DUPLICATE KEY UPDATE dm_fiche_sous_type_id = ?",
			array($rvt_famille_id, $dm_fiche_sous_type_id, $dm_fiche_sous_type_id));

		$sql = "select distinct rp.id, dm_parametre_nom, dm_champ_texte_denormalise_id, dm_formulaire_id
				from rvt_relations_parametres pr
				join rvt_parametres rp on pr.rvt_parametre_id = rp.id
				where rp.rvt_famille_id = ?";
		$params = $db->getAll($sql, array($rvt_famille_id));

		foreach ($params as $param) {
			$sql = "INSERT INTO rvt_relations_parametres (rvt_parametre_id, dm_parametre_nom, dm_champ_texte_denormalise_id, dm_formulaire_id) 
						values (?, ?, ?, ?)
					ON DUPLICATE KEY UPDATE dm_formulaire_id = ?, dm_parametre_nom = ?, dm_champ_texte_denormalise_id = ?";
			$db->query($sql, array($param['id'], $param['dm_parametre_nom'], $param['dm_champ_texte_denormalise_id'], $param['dm_formulaire_id'], $param['dm_formulaire_id'],
				$param['dm_parametre_nom'], $param['dm_champ_texte_denormalise_id']));
		}

		$db->commit();
	}
<!DOCTYPE html>
<?
$projet_id = intval($_GET['projet_id']);
setProjetInfo($projet_id, 'edition');
?>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <title>Parameters</title>
    <!-- Google Font and style definitions -->
    <link rel="stylesheet" href="//cdn.datatables.net/1.10.16/css/jquery.dataTables.min.css">
    <link rel="stylesheet" href="https://code.jquery.com/ui/1.12.1/themes/smoothness/jquery-ui.css">
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.7/css/bootstrap.min.css" integrity="sha384-BVYiiSIFeK1dGmJRAkycuHAHRg32OmUcww7on3RYdg4Va+PmSTsz/K68vbdEjh4u" crossorigin="anonymous">
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.7/css/bootstrap-theme.min.css" integrity="sha384-rHyoN1iRsVXV4nD0JutlnGaslCJuC7uwjduW9SVrLvRYooPp2bWYgmgJQIXwl/Sp" crossorigin="anonymous">
    <link rel="stylesheet" href="https://code.jquery.com/ui/1.12.1/themes/smoothness/jquery-ui.css">
    <link rel="stylesheet" href="css/dark/jquery.fancybox.css">

    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.2.1/jquery.min.js"></script>
    <script src="//cdn.datatables.net/1.10.16/js/jquery.dataTables.min.js"></script>
    <script src="https://code.jquery.com/jquery-migrate-1.4.1.js"></script>
    <script src="https://ajax.googleapis.com/ajax/libs/jqueryui/1.12.1/jquery-ui.min.js"></script>
    <script src="js/functions.js?ts=<?=filemtime('js/functions.js')?>"></script>
    <script src="js/plugins.js?ts=<?=filemtime('js/plugins.js')?>"></script>

    <script src="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.7/js/bootstrap.min.js" integrity="sha384-Tc5IQib027qvyjSMfHjOMaLkfuWVxZxUPnCJA7l2mCWNIpG9mGCD8wGNIcPD7Txa" crossorigin="anonymous"></script>
</head>

<body>
<div id="loading"></div>
<div class="container">
    <div class="row" style="margin-top: 2em;">

        <div class="logo" style="margin-bottom: 20px;">
            <a href="index.php?section=landing&module=index"><img src="Docmatic.png" width="155" /></a>
        </div>

		<?include('menu.inc.php');?>

        <br style="clear: both;" />

        <div class="col-md-9">
            <form id="linkOptions">

                <div style="margin-bottom: 1em;">
                    <label for="RevitSelectParameter"><?= txt('Paramètre de locaux Revit') ?></label><br/>
                    <select id="RevitSelectParameter" name="rvt_parametre_id">
                    </select>
                    <input type="button"  value="<?= txt('Ignorer') ?>" onclick="setNonAssociable($('#RevitSelectParameter').val(), 'parameter');" style="margin-left: 10px; color: red;"/>
                </div>

                <div style="margin-bottom: 1em; color: blue; font-weight: bold;">
						<?= txt('LIÉ À') ?>
                </div>

                <div style="margin-bottom: 1em; display: none;">
                    <label for="DocMaticSelectType"><?= txt('DocMatic datasheet type (***** NE PLUS AFFICHER MAIS FAIRE UNE SÉLECTION SELON LA SÉLECTION FAITE DANS REVIT PARAMETER CAR PRÉSENTEMENT L INVERSE *****)') ?></label><br/>
                    <input id="DocMaticSelectType" name="dm_fiche_sous_type_id" type="hidden" value="2000002" />
                </div>

                <div style="margin-bottom: 1em;">
                    <label for="DocMaticSelectForm"><?= txt('Formulaire DocMatic') ?></label><br/>
                    <select id="DocMaticSelectForm" name="docmaticFormId">
                    </select>
                </div>

                <div style="margin-bottom: 1em;">
                    <label for="DocMaticSelectParameter"><?= txt('Champ du formulaire DocMatic') ?></label><br/>
                    <select id="DocMaticSelectParameter" name="docmaticParameterId">
                    </select>
                    <a href="javascript: void(0);" class="fancy"><img src="css/images/icons/dark/plus.png" /></a>
                </div>

            </form>
            <input type="button" value="<?= txt('Associer') ?>" onclick="linkParameters();"/>
        </div>
        <div class="col-md-7"></div>
    </div>

    <div class="row">
        <div class="col-md-12" style="margin-top: 2em;">
            <h3><?= txt('Paramètres de locaux Revit liés') ?></h3>
            <table id="Linked" class="display with-button" cellspacing="0">
                <thead>
                <tr style="text-align: left;">
                    <th><?= txt('Paramètre Revit (id)') ?></th>
                    <th><?= txt('Famille Revit') ?></th>
                    <th><?= txt('Paramètre Revit') ?></th>
                    <th><?= txt('Formulaire DocMatic (id)') ?></th>
                    <th><?= txt('Formulaire DocMatic') ?></th>
                    <th><?= txt('Champ DocMatic (id)') ?></th>
                    <th><?= txt('Champ DocMatic') ?></th>
                    <th>
                </tr>
                </thead>
            </table>
        </div>
    </div>

    <div class="row" style="margin-top: 2em;">
        <div class="col-md-12">
            <h3><?= txt('Paramètres de locaux Revit non liés') ?></h3>
            <table id="Revit" class="display" cellspacing="0">
                <thead>
                <tr style="text-align: left;">
                    <th><?= txt('Paramètre Revit (id)') ?></th>
                    <th><?= txt('Famille Revit') ?></th>
                    <th><?= txt('Paramètre Revit') ?></th>
                </tr>
                </thead>
            </table>
        </div>
    </div>
    <div class="row" style="margin-top: 2em;">
        <div class="col-md-12">
            <h3><?= txt('Champs de formulaire DocMatic non liés') ?></h3>
            <table id="DocMatic" class="display" cellspacing="0">
                <thead>
                <tr style="text-align: left;">
                    <th><?= txt('Champ DocMatic (id)') ?></th>
                    <th><?= txt('Formulaire DocMatic (id)') ?></th>
                    <th><?= txt('Formulaire DocMatic') ?></th>
                    <th><?= txt('Champ DocMatic') ?></th>
                </tr>
                </thead>
            </table>
        </div>
    </div>

    <div class="row">
        <div class="col-md-12" style="margin-top: 2em;">
            <h3><?= txt('Paramètres de locaux Revit ignorés') ?></h3>
            <table id="Skipped" class="display with-button" cellspacing="0">
                <thead>
                <tr style="text-align: left;">
                    <th><?= txt('Paramètre Revit (id)') ?></th>
                    <th><?= txt('Famille Revit') ?></th>
                    <th><?= txt('Paramètre Revit') ?></th>
                    <th></th>
                </tr>
                </thead>
            </table>
        </div>
    </div>

</div>
</body>

<script>
	var docMaticUnlinkedParameters = null;
	var revitUnlinkedParameters = null;
	var linkedParameters = null;
	var skippedParameters = null;

	$(document).ready(function() {

		$(document).find("a.fancy").click(function() {
			if ($('#RevitSelectParameter').find('option:selected').length) {
				let RevitSelectParameter = $('#RevitSelectParameter').find('option:selected').text();
				RevitSelectParameter = RevitSelectParameter.split(' | ')[1].split(' (')[0];
				$.fancybox({
					'overlayShow': false,
					'type': 'iframe',
					'modal': true,
					'centerOnScroll': true,
					'width': 1024,
					'href': `index.php?section=admin&module=edit&table=champs_textes_denormalises&type=client&id=0&nom=${RevitSelectParameter}&formulaire_id=${$('#DocMaticSelectForm').val()}&callback=callbackAjoutParametreDocmatic`,
					'onComplete': function () {
						$('#fancybox-frame').load(function () { // wait for frame to load and then gets it's height
							fancybox_resize();
						});
					},
					'onClosed'			:	function(){
						$('html').css({ overflow: 'auto' });
					}
				});
			}
		});

		$("#DocMaticSelectType").change(function() {
			$("#DocMaticSelectType option:selected" ).each(function() {
				updateSelectDocMaticForm($( this ).val());
			});
		}).trigger( "change" );

		$("#RevitSelectParameter").change(function() {
			$("#RevitSelectParameter option:selected" ).each(function() {
				$("#DocMaticSelectType").val($(this).attr('dm_fiche_sous_type_id'));
				updateSelectDocMaticForm($(this).attr('dm_fiche_sous_type_id'));
			});
		}).trigger( "change" );

		$("#DocMaticSelectForm").change(function() {
			$("#DocMaticSelectForm option:selected" ).each(function() {
				updateSelectDocMaticParameter($( this ).val());
			});
		}).trigger( "change" );

		updateSelectDocMaticType();
		updateSelectDocMaticForm(document.getElementById('DocMaticSelectType').value);
		updateUnlinkedRevitParameters();
		updateUnlinkedDocMaticParameters();
		updateLinkedParameters();
		updateSkippedParameters();
	} );

	function callbackAjoutParametreDocmatic(id_added) {
		let existed_values = [];
		$('#DocMaticSelectParameter').find('option').each(function(){
			existed_values.push(this.value);
		});

		$.get('index.php?action=pushProjectToWebService&projet_id=<?=intval($_GET['projet_id'])?>', {}, function(){
			updateSelectDocMaticParameter($('#DocMaticSelectForm').val(), () => {
				let value_added = '';
				$('#DocMaticSelectParameter').find('option').each(function(){
					if (existed_values.indexOf(this.value) === -1) {
						value_added = this.value;
					}
				});
				$('#DocMaticSelectParameter').val(value_added);
			});
		});
	}

	function show(id, value) {
		document.getElementById(id).style.display = value ? 'block' : 'none';
	}

	function updateSelectDocMaticType()
	{
		$.post( "relations_externes/GetTypes.php?projet_id=<?= $projet_id ?>&source=DocMatic&just_associeted=1", function( data ) {
			var arr = $.map(data, function(el) { return el });
			$('#DocMaticSelectType').html("");
			for (i = 0; i < arr.length; i++)
			{
				$('#DocMaticSelectType').append($('<option>', {
					id: "docmatic-form-option-".concat(i),
					value: arr[i]['Id'],
					html: "".concat(arr[i]['Name'])
				}));

				if (i == 0)
					$('#docmatic-form-option-0').attr("selected","selected");
			}

			$("#DocMaticSelectType option:selected" ).each(function() {
				updateSelectRevitParameter($( this ).val());
			});
		});
	}

	function updateSkippedParameters()
	{
		skippedParameters = $('#Skipped').DataTable( {
			language: {
				url: 'config/dataTables.french.json'
			},
			"ajax": 'relations_externes/GetParameters.php?projet_id=<?= $projet_id ?>&skipped=1&forRooms=1',
			"columnDefs": [
				{
                    "targets": -1,
                    "data": null,
                    "defaultContent": "<button><?= txt('Rétablir') ?></button>"
                },
				{
					"targets": [ 0 ],
					"visible": false
				}
			]
		} );

		$('#Skipped tbody').on( 'click', 'button', function () {
			var data = skippedParameters.row( $(this).parents('tr') ).data();

			$.post("relations_externes/SetNonAssociable.php?projet_id=<?= $projet_id ?>&unskip=1", { Id: data[0], Type: 'parameter' }, function(data) {
				skippedParameters.ajax.reload( null, false );
				updateSelectRevitParameter($( '#DocMaticSelectType' ).val());
			});
		} );
	}

	function updateUnlinkedRevitParameters()
	{
		revitUnlinkedParameters = $('#Revit').DataTable( {
			language: {
				url: 'config/dataTables.french.json'
			},
			"ajax": 'relations_externes/GetParameters.php?projet_id=<?= $projet_id ?>&source=Revit&forRooms=1',
			"columnDefs": [
				{
					"targets": [ 0 ],
					"visible": false
				}
			]
		} );
	}

	function updateUnlinkedDocMaticParameters()
	{
		docMaticUnlinkedParameters = $('#DocMatic').DataTable(
			{
				language: {
					url: 'config/dataTables.french.json'
				},
                "ajax": 'relations_externes/GetParameters.php?projet_id=<?= $projet_id ?>&source=DocMatic&forRooms=1',
                "columnDefs": [
                    {
                        "targets": [ 0, 1 ],
                        "visible": false
                    }
                ]
            }
        );
	}

	function updateLinkedParameters()
	{
		linkedParameters = $('#Linked').DataTable( {
			language: {
				url: 'config/dataTables.french.json'
			},
			"ajax": 'relations_externes/GetParameters.php?projet_id=<?= $projet_id ?>&forRooms=1',
			"columnDefs": [
				{
                    "targets": -1,
                    "data": null,
                    "defaultContent": "<button><?= txt('Dissocier') ?></button>"
                },
				{
					"targets": [ 0, 3, 5 ],
					"visible": false
				}
			]
		} );

		$('#Linked tbody').on( 'click', 'button', function () {
			var data = linkedParameters.row( $(this).parents('tr') ).data();

			$.post("relations_externes/UnLinkParameters.php?projet_id=<?= $projet_id ?>", {
				"docmaticFormId":data[3],
				"docmaticParameterId":data[5],
				"rvt_parametre_id":data[0],
				"rvt_famille_id":data[7]
			}, function(data) {
				revitUnlinkedParameters.ajax.reload( null, false );
				docMaticUnlinkedParameters.ajax.reload( null, false );
				linkedParameters.ajax.reload( null, false );
				updateSelectDocMaticType();
			});
		} );
	}

	function getrvt_famille_id()
	{
		return $("#RevitSelectParameter option:selected" ).attr('rvt_famille_id');
	}

	function updateSelectDocMaticForm(dm_fiche_sous_type_id)
	{
		$.post( "relations_externes/GetFamilies.php?projet_id=<?= $projet_id ?>&source=DocMatic&dm_fiche_sous_type_id=".concat(dm_fiche_sous_type_id)+'&wq=0&projet_id=<?=intval($_GET['projet_id'])?>', function( data ) {
			var arr = $.map(data, function(el) { return el });
			$('#DocMaticSelectForm').html("");
			for (i = 0; i < arr.length; i++)
			{
				$('#DocMaticSelectForm').append($('<option>', {
					id: "docmatic-form-option-".concat(i),
					value: arr[i]['Id'],
					html: "".concat(arr[i]['Name'])
				}));

				if (i == 0)
					$('#docmatic-form-option-0').attr("selected","selected");
			}

			$("#DocMaticSelectForm option:selected" ).each(function() {
				updateSelectDocMaticParameter($( this ).val());
			});
		});
	}


	function updateSelectRevitParameter()
	{
		$.post( "relations_externes/GetParameters.php?projet_id=<?= $projet_id ?>&forRooms=1&source=Revit", function( data ) {
			var arr = $.map(data, function(el) { return el });
			$('#RevitSelectParameter').html("");
			for (i = 0; i < arr.length; i++)
			{
				$('#RevitSelectParameter').append($('<option>', {
					id: "revit-parameter-option-".concat(i),
					value: arr[i][0],
					html: "".concat(arr[i][1], ' | ', arr[i][2], " (", arr[i][0], ")"),
					dm_fiche_sous_type_id: arr[i][3],
					rvt_famille_id: arr[i][4]
				}));

				if (i == 0)
					$('#revit-parameter-option-0').attr("selected","selected");
			}
			$("#RevitSelectParameter option:selected" ).each(function() {
				$("#DocMaticSelectType").val($(this).attr('dm_fiche_sous_type_id'));
				// updateSelectDocMaticForm($(this).attr('dm_fiche_sous_type_id'));
			});
		});
	}

	function updateSelectDocMaticParameter(formId, callback)
	{
		$.post( "relations_externes/GetParameters.php?projet_id=<?= $projet_id ?>&forRooms=1&source=DocMatic&formId=".concat(formId), function( data ) {
			var arr = $.map(data, function(el) { return el });
			$('#DocMaticSelectParameter').html("");
			for (i = 0; i < arr.length; i++)
			{
				$('#DocMaticSelectParameter').append($('<option>', {
					id: "docmatic-parameter-option-".concat(i),
					value: arr[i][0],
					html: "".concat(arr[i][3])
				}));

				if (i == 0)
					$('#docmatic-parameter-option-0').attr("selected","selected");

				if (callback) {
					callback();
                }
			}
		});
	}

	function linkParameters()
	{
		let data = {};
		$("#linkOptions").find("select, input").each(function() {
			data[this.name] = $(this).val();
		});
		data.rvt_famille_id = getrvt_famille_id();
		$.post("relations_externes/LinkParameters.php?projet_id=<?= $projet_id ?>", data, function(data) {
			revitUnlinkedParameters.ajax.reload( null, false );
			docMaticUnlinkedParameters.ajax.reload( null, false );
			linkedParameters.ajax.reload( null, false );
			updateSelectDocMaticType();
		});
	}

	function setNonAssociable(Id, Type)
	{
		$.post("relations_externes/SetNonAssociable.php?projet_id=<?= $projet_id ?>", { Id: Id, Type: Type }, function(data) {
			skippedParameters.ajax.reload( null, false );
			updateSelectRevitParameter($( '#DocMaticSelectType' ).val());
		});
	}
</script>

</html>
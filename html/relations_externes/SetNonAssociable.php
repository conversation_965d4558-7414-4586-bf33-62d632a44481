<?
	chdir('../');
	include('config/inc.php');
    
	$db->autocommit(false);
    
    $table = '';
    switch ($_POST['Type']) {
        case 'family':
            $table = "rvt_familles";
            break;
        case 'parameter':
            $table = "rvt_parametres";
            break;
        case 'type':
            $table = "rvt_types";
            break;
    }
    
    $value = isset($_GET['unskip']) ? 1 : 0;
    
    $sql = "update $table set is_associable = ? where id = ?";
    $db->query($sql, array($value, $_POST['Id']));
    
    $db->commit();
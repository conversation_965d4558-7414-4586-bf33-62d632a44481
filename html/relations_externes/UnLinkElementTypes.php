<?
	chdir('../');
	include('config/inc.php');

$projet_id = $_GET['projet_id'];
	$docMaticFormId = $_POST['docmaticFormId'];
	$rvt_famille_id = $_POST['rvt_famille_id'];
	$revitFamilyTypeId = $_POST['revitFamilyTypeId'];
	$docmaticParameterId = $_POST['docmaticParameterId'];

	$db->autocommit(false);

	$data = [$revitFamilyTypeId, $docMaticFormId];
	if (isset($docmaticParameterId) && dm_strlen($docmaticParameterId) > 0) {
		$data[] = $docmaticParameterId;
	}

	$sql = "DELETE FROM rvt_relations_installations WHERE 
				rvt_type_id = ? AND 
				dm_formulaire_id = ? AND 
				dm_liste_item_id " . (isset($docmaticParameterId) && dm_strlen($docmaticParameterId) > 0 ? " = ?" : " is null");
	$db->query($sql, $data);
	
	$db->commit();
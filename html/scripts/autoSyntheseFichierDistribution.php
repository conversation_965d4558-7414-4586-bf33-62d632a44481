<?
require_once __DIR__ . '/../../vendor/autoload.php';

use PhpOffice\PhpSpreadsheet\Cell\Coordinate;
use PhpOffice\PhpSpreadsheet\IOFactory;

if (isset($_POST['synthetiser'])) {
	$lignes = array();
	$alias = array();
	$champs = array();

	if(!isset($_FILES['fichier']) || !is_uploaded_file($_FILES['fichier']['tmp_name']))
	{
		setErreur(txt('Vous devez sélectionner le fichier à importer.'));
	}
	else
	{
		$row_titre = 1;
		$col_assoc = [
			0 => 'codeFiche',
			1 => 'categorie',
			2 => 'codeEquipement',
			3 => 'description',
			4 => 'quantite',
			5 => 'cout',
			6 => 'commentaire',
		];
		$titres = array();
		$all_datas = array();
		$spreadsheet = IOFactory::load($_FILES['fichier']['tmp_name']);
		foreach ($spreadsheet->getWorksheetIterator() as $iter_sheet => $worksheet)
		{
			if($iter_sheet > 0)
			{
				break;
			}
			$highestRow         = $worksheet->getHighestDataRow(); // e.g. 10
			$highestColumn      = $worksheet->getHighestDataColumn(); // e.g 'F'
			$highestColumnIndex = Coordinate::columnIndexFromString($highestColumn);
			$nrColumns = ord($highestColumn) - 64;

			for ($row = 1; $row <= $highestRow; ++ $row)
			{
				for ($col = 1; $col <= $highestColumnIndex; ++ $col)
				{
					$cell = $worksheet->getCellByColumnAndRow($col, $row);
					$val = sanitizeString((string)$cell->getCalculatedValue());

					if($row == $row_titre && dm_strlen($val) > 0)
					{
						$titres[$col] = $val;
					}
					else if($row > $row_titre)
					{
						$all_datas[$row][$col_assoc[$col]] = $val;
					}
				}
			}

			$commentaire_already = [];
			$local_code_data = [];
			foreach ($all_datas as $iter_row => $data) {
				if (!isset($local_code_data[$data['codeFiche'].'_'.$data['codeEquipement']])) {
					$local_code_data[$data['codeFiche'].'_'.$data['codeEquipement']] = $data;
					$commentaire_already[$data['codeFiche'].'_'.$data['codeEquipement']][$data['commentaire']] = $data['commentaire'];
				} else {
					$local_code_data[$data['codeFiche'].'_'.$data['codeEquipement']]['codeFiche'] = $data['codeFiche'];
					$local_code_data[$data['codeFiche'].'_'.$data['codeEquipement']]['categorie'] = $data['categorie'];
					$local_code_data[$data['codeFiche'].'_'.$data['codeEquipement']]['codeEquipement'] = $data['codeEquipement'];
					$local_code_data[$data['codeFiche'].'_'.$data['codeEquipement']]['description'] = $data['description'];
					$quantite = intval($data['quantite']);
					// $local_code_data[$data['codeFiche'].'_'.$data['codeEquipement']]['quantite'] = intval($data['quantite']) > $quantite ? $data['quantite'] : $quantite;
					$local_code_data[$data['codeFiche'].'_'.$data['codeEquipement']]['quantite'] += $quantite;
					$local_code_data[$data['codeFiche'].'_'.$data['codeEquipement']]['cout'] = $data['cout'];
					if (!isset($commentaire_already[$data['codeFiche'].'_'.$data['codeEquipement']][$data['commentaire']])) {
						$isVide = dm_strlen($local_code_data[$data['codeFiche'].'_'.$data['codeEquipement']]['commentaire']) == 0;
						$local_code_data[$data['codeFiche'].'_'.$data['codeEquipement']]['commentaire'] .= dm_strlen($data['commentaire']) > 0 ? ($isVide ? "" : " / ").$data['commentaire'] : '';
						$commentaire_already[$data['codeFiche'].'_'.$data['codeEquipement']][$data['commentaire']] = $data['commentaire'];
					}
				}
			}

			$row = 1;
			$dataArray = array();
			foreach ($titres as $col => $titre) {
				$dataArray[$row][$col] = $titre;
			}
			$row++;
			foreach ($local_code_data as $key => $data) {
				$col = 0;
				foreach($data as $champ => $val) {
					$dataArray[$row][$col] = $val;
					$col++;
				}
				$row++;
			}
			exportExcelFromDataArray($dataArray, 'synthese', 'synthese');
			exit;
		}
	}

	$etape = 'synchroniser';
}
$noHeader = true;
include('config/header.inc.php');
?>
<style>
	ul {
		list-style-type: disc;
		list-style-position: inside;
		margin-left: 35px;
	}
	ol {
		list-style-type: decimal;
		list-style-position: inside;
	}
	ul ul, ol ul {
		list-style-type: circle;
		list-style-position: inside;
		margin-left: 15px;
	}
	ol ol, ul ol {
		list-style-type: lower-latin;
		list-style-position: inside;
		margin-left: 15px;
	}
	li {
		margin-bottom: 15px;
	}
</style>
<div id="popupContent">

	<form id="form" autocomplete="off" method="post" action="index.php?section=scripts&module=autoSyntheseFichierDistribution" enctype="multipart/form-data">
		<label><?=txt('Auto-synthèse des fichiers de distribution')?></label>
		<fieldset style="min-height: 175px; padding: 10px 0 0 50px;">
			<div id="editables">
				<?
				erreurs();
				?>
				<section style="margin: 10px 0 10px 0;">
					<span for="fichier"><?php print txt('Fichier MS Excel à synthétiser'); ?></span>
					<span>
                        <input type="file" id="fichier" name="fichier" style="border: solid 0px!important;" />
                    </span>
				</section>

				<section style="color: blue;">
					<p>
					Cet outil à pour fonction de traiter les fichiers de distribution (avant import) dans le but de détecter et d’éliminer tous les doublons qu’ils contiennent.
					</p><p>
					DocMatic effectuera donc la synthèse de tous les doublons « Local / Équipement » qui seront détectés dans le fichier qui lui sera soumis.
					</p><p>
					Le fichier doit obligatoirement comporter TOUTES les informations suivantes;
					</p><div style="margin-left: 35px;"><p>
					Colonne 1 : Code de local (donnée obligatoire)
					</p><p>
					Colonne 2 : Catégorie d’équipement (créer une colonne « Catégorie » et laisser les champs vides en-dessous si l’information n’est pas disponible)
					</p><p>
					Colonne 3 : Code d’équipement (donnée obligatoire)
					</p><p>
					Colonne 4 : Description d’équipement (créer une colonne « Description » et laisser les champs vides en-dessous si l’information n’est pas disponible)
                    </p><p>
                            Colonne 5 : Quantité d’équipement (donnée obligatoire)
					</p><p>
					Colonne 6 : Coût de l’équipement (créer une colonne « Coût » et laisser les champs vides en-dessous si l’information n’est pas disponible)
					</p><p>
					Colonne 7 : Commentaire (créer une colonne « Commentaires » et laisser les champs vides en-dessous si l’information n’est pas disponible)
					</p></div><p>
					Le titre des colonnes n’a pas d‘importance, mais l’ordre dans lequel l’information se trouve doit respecter la séquence susmentionnée.
					</p><p>
					L’élimination des doublons se fera de la manière suivante;
					</p><p>
					<ul>
						<li>
							Conservation d’une seul occurrence « Local type / Code »
						</li><li>
							Conservation de la dernière valeur détectée pour les champs « Catégorie », « Description » et « Coût »
						</li><li>
							Conservation de la valeur « Quantité » la plus élevée parmi celles détectées
						</li><li>
							Concaténation de tous les « Commentaires » avec l’insertion d’une barre oblique « / » entre chaque commentaire. Les commentaires identiques seront ignorés, ainsi que les champs vides.
						</li>
					</ul>
					</p><p>
					Une fois que tous les doublons auront été éliminés, un fichier de synthèse sera produit par le système et vous sera transmis directement dans votre navigateur.
					</p>
				</section>

			</div>
		</fieldset>

		<div style="text-align: right; width: 98%!important;">

			<button class="submit" value="submitbuttonvalue" name="synthetiser" onclick="loading_beforeunload = false;"><?=txt('Synthétiser')?></button>
			<button onclick="parent.$.fancybox.close();return false;" name="close"><?=txt('Annuler')?></button>

		</div>
	</form>
</div>
<?
include('config/footer.inc.php');

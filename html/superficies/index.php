<?php
$is_print = isset($_GET['print']);
$sel_batiment_id = 0;
$sel_fiche_type_id = 0;
$sel_fiche_sous_type_id = 0;

$forced_fiche_id = isset($_GET['id']) ? intval($_GET['id']) : 0;
$inclure_supprimees = havePermission('effacement') ? 1 : 0;
$fiches = Fiches::getListe(array('term' => '', 'extract_is_orphelin' => 1, 'force_ordre' => 'code_ordre', 'batiment_id' => $sel_batiment_id, 'fiche_type_id' => $sel_fiche_type_id, 'fiche_sous_type_id' => $sel_fiche_sous_type_id));
if($forced_fiche_id == 0 && count($fiches) > 0)
{
    $fiche_tmp = array_values($fiches)[0];
	$fiche = new Fiches($fiche_tmp->id);
	$forced_fiche_id = $fiche->id;
}

$niveaux = BatimentsNiveaux::getListeReel();

if($is_print)
{
	$noHeader = 1;
	$isPDF = true;
	include('config/header.inc.php');
?>
	<script>
        window.onload = function() { window.print(); }
	</script>
	<style>
		#logs td {
			text-align: left;
			padding: 0px 5px;
		}
		#logs th {
			text-weight: bold;
			text-align: left;
		}
	</style>
<?php
}

if(!$is_print)
{
	include('config/header.inc.php');
?>
<script>
var timout = setTimeout(function(){}, 1);
var entete = '';
var niveaux = [];

$(document).ready(function(){
	$('#sup_filter_form').submit(function(e){
		e.preventDefault();
	});

	$('#sup_blocFiltre select.niveaux[data-niveau_parent_id=-1]').each(function(){
		let item = $(this);
		let items = [];
		items.push(item.data("niveau_id"));
		while (item.data("niveau_enfant_id") != -1) {
			let enfant = item.data("niveau_enfant_id");
			item = $('select.niveaux[data-niveau_id='+ enfant +']');
			items.push(item.data("niveau_id"));
		}
		for (var i=0; i < items.length; i++) {
			niveaux[niveaux.length] = $('select.niveaux[data-niveau_id='+ items[i] +']').children();
		}
	});

	evenementsSurFiltres();
	
	$('#btn_generer').click(function(){
		sup_appliqueFiltre();
	});

	$('#filtre_vider').click(function(){
		$('#sup_blocFiltre select.niveaux').off();
		for (i=0; i < niveaux.length; i++) {
			$('select.niveaux[data-niv='+ i +']').html(niveaux[i]);
		}

		$('#sup_blocFiltre select.niveaux').prop("selectedIndex", 0);
		evenementsSurFiltres();

		$('#sup_blocFiltre').find('select.niveaux').each(function() {
			$('#s2id_' + this.id).find('.select2-chosen').html($(this).find('option:first-child').text());
		});
	});

	entete = $(".laTable").html();

	$("#est_orphelin").click(function () {
		if ( $(this).prop("checked") )
			$('#filtre_vider').click();
	})	
});

function evenementsSurFiltres() {
	$('#sup_filter_form select').change(function () {
		sup_changeNiveau(this);
	});

	$('#sup_filter_form select').each(function () {
		if ($(this).prop("selectedIndex") > 0)
			$(this).css("background-color", "#f0d2b5");
		else
			$(this).css("background-color", "");
	});
}

function sup_changeNiveau(obj)
{
	$('#sup_blocFiltre select.niveaux').off();

	var niveau_enfant_id = $(obj).data('niveau_enfant_id');
	while($('#niveau_' + niveau_enfant_id).length)
	{
		$('#niveau_' + niveau_enfant_id).html('');
		$('#s2id_' + $('#niveau_' + niveau_enfant_id).attr('id')).find('.select2-chosen').html($('#niveau_' + niveau_enfant_id).find('option:first-child').text());
		niveau_enfant_id = $('#niveau_' + niveau_enfant_id).data('niveau_enfant_id');
	}

	let item = $(obj);
	for (niv = $(obj).data('niv')+1; niv < 5; niv++) {
		if ($('select.niveaux[data-niv='+ (niv-1) +']')[0].selectedIndex > 0 && niveaux[niv]) {
			for (k=0; k < niveaux[niv].length; k++) {
				if (niveaux[niv][k].className == '' || niveaux[niv][k].className.indexOf("parent_niveau_id_"+item.val()) > -1)
					$('select.niveaux[data-niv='+ niv +']').append(niveaux[niv][k]);
			}
			$('select.niveaux[data-niv='+ niv +']')[0].selectedIndex = 0;
		}
		item = $('select.niveaux[data-niv='+ niv +']');
	}

	evenementsSurFiltres();
}

function sup_appliqueFiltre()
{
    var niveau = {};

    $('#sup_blocFiltre').find('select.niveaux').each(function(){
		niveau[$(this).data('niveau_id')] = $(this).val();
    });

    $(".laTable").html('');
    show('loading', true);
    var params = {
    		action: 'getSuperficiesList',
    		force_ordre: 'code_ordre',
    		terme: $('#terme').val()
    	};
    if ( $("#est_orphelin").prop("checked") )
    	params.est_orphelin = 1;
    else
    	params.niveau = niveau;

    $.get('index.php', params, function(json_data){
	   if (json_data.length > 0) {
	    	$(".laTable").html(entete);
			if ( $("#est_orphelin").prop("checked") )
				generer_orphelins(json_data);
			else
				generer<?=count($niveaux)?>(json_data, 1, [0,0,0,0,0]);
		}
		else {
			$(".laTable").html('<tr><td style="text-align:center; color:#FF0000; padding:10px; font-size:1.4em;">Aucun résultat</td>/tr>');
		}
		show('loading', false);
    }, 'json');
}

function generer3(liste, niv, dim) {
	var total = liste.length;
	var niv_enfant = niv+1;
	dim[niv] = total;

	for (var i=0; i < total; i++) {
		let ligne1 = (dim[1] > 0)? '<div class="ligne3"></div>' : '';
		let ligne2 = (dim[2] > 0)? '<div class="ligne3"></div>' : '';
		let ligne3 = (dim[3] > 0)? '<div class="ligne3"></div>' : '';
		let ligne4 = (i < total-1)? '<div class="ligne4"></div>' : '<div class="ligne2"></div>';
		let ligne5 = (i < total-1)? '<div class="ligne4"></div>' : '<div class="ligne2"></div>';

		if (typeof liste[i].niv == 'undefined') liste[i].niv = '-- Indéterminé --';

		if (niv == 1) {
			dim[1]--;
			d = '<tr><td colspan="6"></td></tr>';
			d += '<tr><td colspan="4">'+liste[i].niv+' &nbsp;<div class="ligne"></div></td><td class="droit b3">'+liste[i].sup_prog.toFixed(2)+'</td><td class="droit b3">'+liste[i].sup_dess.toFixed(2)+'</td></tr>';
		}
		else if (niv == 2) {
			dim[2]--;
			d = '<tr><td>'+ligne5+'</td><td colspan="3">&nbsp; '+liste[i].niv+' &nbsp;<div class="ligne"></div></td><td class="droit b2">'+liste[i].sup_prog.toFixed(2)+'</td><td class="droit b2">'+liste[i].sup_dess.toFixed(2)+'</td></tr>';
		}
		else if (niv == 3) {
			dim[3]--;
			rouge = (liste[i].items[2] != liste[i].items[3])?' rouge':'';
			sup_prog = (liste[i].items[2] > 0)? liste[i].items[2].toFixed(2):'---';
			sup_dess = (liste[i].items[3] > 0)? liste[i].items[3].toFixed(2):'---';
			d = '<tr><td>'+ligne2+'</td><td>'+ligne5+'</td><td>'+liste[i].niv+'</td><td><a href="/index.php?section=fiche&module=index&id='+liste[i].items[0]+'" target="_blank">'+liste[i].items[1]+'</a></td><td class="droit'+rouge+'">'+sup_prog+'</td><td class="droit'+rouge+'">'+sup_dess+'</td></tr>';
		}

		$(".laTable").append(d);
		if (niv_enfant < 4) {
			generer3(liste[i].items, niv_enfant, dim);
		}
	}
}

function generer4(liste, niv, dim) {
	var total = liste.length;
	var niv_enfant = niv+1;
	dim[niv] = total;

	for (var i=0; i < total; i++) {
		let ligne1 = (dim[1] > 0)? '<div class="ligne3"></div>' : '';
		let ligne2 = (dim[2] > 0)? '<div class="ligne3"></div>' : '';
		let ligne3 = (dim[3] > 0)? '<div class="ligne3"></div>' : '';
		let ligne4 = (i < total-1)? '<div class="ligne4"></div>' : '<div class="ligne2"></div>';
		let ligne5 = (i < total-1)? '<div class="ligne4"></div>' : '<div class="ligne2"></div>';

		if (typeof liste[i].niv == 'undefined') liste[i].niv = '-- Indéterminé --';

		if (niv == 1) {
			dim[1]--;
			d = '<tr><td colspan="7"></td></tr>';
			d += '<tr><td colspan="5">'+liste[i].niv+' &nbsp;<div class="ligne"></div></td><td class="droit b3">'+liste[i].sup_prog.toFixed(2)+'</td><td class="droit b3">'+liste[i].sup_dess.toFixed(2)+'</td></tr>';
		}
		else if (niv == 2) {
			dim[2]--;
			d = '<tr><td>'+ligne4+'</td><td colspan="4">&nbsp; '+liste[i].niv+' &nbsp;<div class="ligne"></div></td><td class="droit b2">'+liste[i].sup_prog.toFixed(2)+'</td><td class="droit b2">'+liste[i].sup_dess.toFixed(2)+'</td></tr>';
		}
		else if (niv == 3) {
			dim[3]--;
			d = '<tr><td>'+ligne2+'</td><td>'+ligne5+'</td><td colspan="3">&nbsp; '+liste[i].niv+' &nbsp;<div class="ligne"></div></td><td class="droit b">'+liste[i].sup_prog.toFixed(2)+'</td><td class="droit b">'+liste[i].sup_dess.toFixed(2)+'</td></tr>';
		}
		else if (niv == 4) {
			dim[4]--;
			rouge = (liste[i].items[2] != liste[i].items[3])?' rouge':'';
			sup_prog = (liste[i].items[2] > 0)? liste[i].items[2].toFixed(2):'---';
			sup_dess = (liste[i].items[3] > 0)? liste[i].items[3].toFixed(2):'---';
			d = '<tr><td>'+ligne2+'</td><td>'+ligne3+'</td><td></td><td>'+liste[i].niv+'</td><td><a href="/index.php?section=fiche&module=index&id='+liste[i].items[0]+'" target="_blank">'+liste[i].items[1]+'</a></td><td class="droit'+rouge+'">'+sup_prog+'</td><td class="droit'+rouge+'">'+sup_dess+'</td></tr>';
		}

		$(".laTable").append(d);
		if (niv_enfant < 5) {
			generer4(liste[i].items, niv_enfant, dim);
		}
	}
}

function generer5(liste, niv, dim) {
	var total = liste.length;
	var niv_enfant = niv+1;
	dim[niv] = total;

	for (var i=0; i < total; i++) {
		let ligne1 = (dim[1] > 0)? '<div class="ligne3"></div>' : '';
		let ligne2 = (dim[2] > 0)? '<div class="ligne3"></div>' : '';
		let ligne3 = (dim[3] > 0)? '<div class="ligne3"></div>' : '';
		let ligne4 = (i < total-1)? '<div class="ligne4"></div>' : '<div class="ligne2"></div>';
		let ligne5 = (i < total-1)? '<div class="ligne4"></div>' : '<div class="ligne2"></div>';

		if (typeof liste[i].niv == 'undefined') liste[i].niv = '-- Indéterminé --';

		if (niv == 1) {
			dim[1]--;
			d = '<tr><td colspan="8"></td></tr>';
			d += '<tr><td colspan="6">'+liste[i].niv+' &nbsp;<div class="ligne"></div></td><td class="droit b3">'+liste[i].sup_prog.toFixed(2)+'</td><td class="droit b3">'+liste[i].sup_dess.toFixed(2)+'</td></tr>';
		}
		else if (niv == 2) {
			dim[2]--;
			d = '<tr><td>'+ligne4+'</td><td colspan="5">&nbsp; '+liste[i].niv+' &nbsp;<div class="ligne"></div></td><td class="droit b2">'+liste[i].sup_prog.toFixed(2)+'</td><td class="droit b2">'+liste[i].sup_dess.toFixed(2)+'</td></tr>';
		}
		else if (niv == 3) {
			dim[3]--;
			d = '<tr><td>'+ligne2+'</td><td>'+ligne5+'</td><td colspan="4">&nbsp; '+liste[i].niv+' &nbsp;<div class="ligne"></div></td><td class="droit b">'+liste[i].sup_prog.toFixed(2)+'</td><td class="droit b">'+liste[i].sup_dess.toFixed(2)+'</td></tr>';
		}
		else if (niv == 4) {
			dim[4]--;
			d = '<tr><td>'+ligne2+'</td><td>'+ligne3+'</td><td>'+ligne5+'</td><td colspan="3">&nbsp; '+liste[i].niv+' &nbsp;<div class="ligne"></div></td><td class="droit b">'+liste[i].sup_prog.toFixed(2)+'</td><td class="droit b">'+liste[i].sup_dess.toFixed(2)+'</td></tr>';
		}
		else if (niv == 5) {
			dim[5]--;
			rouge = (liste[i].items[2] != liste[i].items[3])?' rouge':'';
			sup_prog = (liste[i].items[2] > 0)? liste[i].items[2].toFixed(2):'---';
			sup_dess = (liste[i].items[3] > 0)? liste[i].items[3].toFixed(2):'---';
			d = '<tr><td>'+ligne2+'</td><td>'+ligne3+'</td><td>'+ligne3+'</td><td>'+ligne4+'</td><td>'+liste[i].niv+'</td><td><a href="/index.php?section=fiche&module=index&id='+liste[i].items[0]+'" target="_blank">'+liste[i].items[1]+'</a></td><td class="droit'+rouge+'">'+sup_prog+'</td><td class="droit'+rouge+'">'+sup_dess+'</td></tr>';
		}

		$(".laTable").append(d);
		if (niv_enfant < 6) {
			generer5(liste[i].items, niv_enfant, dim);
		}
	}
}

function generer_orphelins(liste) {
	var total = liste.length;

	for (var i=0; i < total; i++) {
		rouge = (liste[i].sup_prog != liste[i].sup_dess)?' rouge':'';
		sup_prog = (liste[i].sup_prog > 0)? liste[i].sup_prog.toFixed(2):'---';
		sup_dess = (liste[i].sup_dess > 0)? liste[i].sup_dess.toFixed(2):'---';
		d = '<tr><td></td><td></td><td></td><td></td><td><a href="/index.php?section=fiche&module=index&id='+liste[i].id+'" target="_blank">'+liste[i].code+'</a></td><td class="droit'+rouge+'">'+sup_prog+'</td><td class="droit'+rouge+'">'+sup_dess+'</td></tr>';
		$(".laTable").append(d);
	}
}
</script>
	<style>
		#table_principale td {
			text-align: left;
			padding: 0px 5px;
		}
		#table_principale th {
			text-weight: bold;
			text-align: left;
		}
		td.crochet {
			padding-top: 6px!important;
			text-align: center!important;
		}
		th.crochet {
			padding-top: 12px;
			text-align: center!important;
		}
		select, input {
			width: 210px;
            box-sizing: border-box;
		}
	.t-table		{display: table; border:#ccc 1px solid; width: 100%;}
	.laTable, .laTable tr, .laTable td
		{border:none!important; background: none; text-align: left; overflow: hidden; white-space: nowrap;}
	.laTable td		{margin: 0; padding: 0; line-height: 24px; vertical-align: middle;}
	.laTable th		{text-align: left;}
	.laTable .droit	{text-align: right;}
	.ligne			{display:inline-block; width: 100%; height:1px; margin-bottom:3px; border-top: black 1px solid;}
	.ligne2			{width: 90%; height:11px; margin: 0 0 12px 10%; border-bottom: black 1px solid; border-left: black 1px solid;}
	.ligne4			{width: 90%; height:11px; margin: 0 0 12px 10%; border-bottom: black 1px solid; border-left: black 1px solid;}
	.ligne4::after	{position:relative; height:14px; left: -1px; top:8px; border-left: black 1px solid; content:" "}
	.ligne3			{width: 1px; height: 24px; margin: 0 0 0 10%; border-left: black 1px solid;}
	.b				{font-weight: bold; color: #9999ff;}
	.b2				{font-weight: bold; color: #5555ff;}
	.b3				{font-weight: bold; color: blue;}
	.rouge			{color: #FF0000;}
	</style>

	<h1 class="ficheTitre" style="margin-bottom: 20px;">

		<?php echo txt('Tableau des superficies')?>
        <a href="index.php?section=superficies&module=index&print=1" style="position: relative; top: 4px;" title="<?php echo txt("Imprimer")?>" target="iframe">
            <img src="css/images/icons/dark/printer.png" /></a>
        <!--a href="javascript:exporter();" style="position: relative; top: 1px; left: 6px;" title="<?php echo txt("Exporter")?>">
            <img src="css/images/icons/dark/DM_export_CSV.png" /></a-->

	</h1>
	<form action="" method="post" id="sup_filter_form">
		<div style="margin: 0 20px 20px 0;" id="sup_blocFiltre">
<?php
$options_differees = array();
$nb_niveaux = count($niveaux);
if($nb_niveaux > 0)
{
    $niv = 0;
    foreach($niveaux as $i => $niveau)
    {
?>
        <div style="float: left; width: 160px; margin-left: <?=($i == 0 ? '0px' : '10px')?>;">
            <select id="niveau_<?=$niveau->id?>" name="niveau[<?=$niveau->id?>]" class="niveaux" style="width: 160px;"
            	data-niv="<?=$niv?>"
            	data-niveau_id="<?=$niveau->id?>" 
                data-niveau_parent_id="<?=(isset($niveaux[($i-1)]) ? $niveaux[($i-1)]->id : -1)?>"
                data-niveau_enfant_id="<?=(isset($niveaux[($i+1)]) ? $niveaux[($i+1)]->id : -1)?>"> 
                <option value=""><?=('- '.$niveau->nom.' -')?></option>
<?php
                foreach($niveau->batiments_niveaux_elements as $element)
                {
                    $classes = array();
                    if(isset($element['batiment_niveau_element_id']))
                    {
                        $parent = $niveau->batiments_niveaux_elements_precedants[$element['batiment_niveau_element_id']];
                        foreach($parent['ids'] as $id)
                        {
                            $classes[] = 'parent_niveau_id_'.$id;
                        }
                    }

                    $suffix = $i > 0 ? ' ('.$parent['nom'].')' : '';
                    echo '<option value="'.$element['id'].'" data-nom="'.$element['nom'].'" class="'.dm_implode(' ', $classes).'">'.$element['nom'].$suffix.'</option>';
                }
?>
            </select>
        </div>
<?
    	$niv++;
    }
}
?>
        <div style="float:left; margin-left: 8px; top: -4px;">
        	<input type="text" name="terme" id="terme" value="" style="width: 210px;" placeholder="Recherche" />
		</div>
		
		<div style="float:left; position:relative; margin-left: 8px; top: -4px;">
            <input type="button" class="filtrer" id="btn_generer" value="<?php echo txt('Générer')?>" style="min-height: 20px!important; padding: 0 20px;" />
            <a href="javascript: void(0);" id="filtre_vider" title="<?php echo txt('Libérer tous les filtres')?>">
                <img src="css/images/icons/dark/bended_arrow_left.png" style="vertical-align:bottom;" /></a>
            <a href="" id="refresh" title="<?php echo txt('Rafraîchir la page')?>">
                <img src="css/images/icons/dark/refresh_3.png" style="vertical-align:bottom;" /></a>
        </div>
	</div>
	<div style="float: right;">
		<input type="checkbox" name="est_orphelin" id="est_orphelin" value="1" />
		<?php echo txt('Orphelins.')?>
	</div>
	<div style="clear: both; height: 1px;"></div>
	<!--/form-->
    <br>
	<?php
}

if($is_print)
{
	?>
	<h6><?php echo txt('Tableau des superficies')?></h6>
	<?php
}
$sql = "select count(*) as nbTotal, count(case when revit_unique_id is not null then id else null end) as nbModelises
        from fiches f 
        where ts_delete is null and ts_archive is null and fiche_sous_type_id = 2000002";
$rowNbs = $db->getRow($sql);

$pourcentage = round(($rowNbs['nbTotal'] > 0 ? ($rowNbs['nbModelises'] / $rowNbs['nbTotal']) * 100 : 0), 2) . '%';
?>
	<!-- --- Contenu --- -->
        <span style="color: blue; float: right"><?= $rowNbs['nbTotal'] ?></span><span style="color: black; float: right"><?= txt('Locaux programmés: ') . '&nbsp' ?></span></br>
		<span style="color: blue; float: right"><?= $rowNbs['nbModelises'] . ' (' . $pourcentage . ')' ?></span></span><span style="color: black; float: right"><?= txt('Locaux modélisés: ') . '&nbsp' ?></span></br></br>
	<table class="laTable" cellpadding="0" cellspacing="0">
		<tr>
            <?
            foreach ($niveaux as $niveau) {
            ?>
                <th width="15%"><?=$niveau->nom?></th>
	            <?
            }
            ?>
			<th width="15%">Code</th>
			<th width="70" class="droit">Programmé</th>
			<th width="70" class="droit">Modélisé</th>
		</tr>
	</table>
    <!--select id="listeFiches" size="100" style="position: relative; top: 16px;">
    </select-->

	</form>
	<iframe id="iframe" name="iframe" style="display: none; width: 100%;"></iframe>
	<br style="clear: both;" />
<?php
if(isset($options_differees) && count($options_differees) > 0) {
	?>
    <options_differees style="display: none;">
		<?php
		foreach($options_differees as $key => $options)
		{
			?>
            <span data-idselect="<?=$key?>">
                <?php
                foreach($options as $i => $option)
                {
                    print $option."\n";
                }
                ?>
            </span>
			<?php
		}
		?>
    </options_differees>
	<?php
}
$includeFiche = 1;

include('config/footer.inc.php');
?>
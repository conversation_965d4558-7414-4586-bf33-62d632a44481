<?php
$colonnes = array_keys($validColumns['doc'][$table]);

$sql = "SELECT id, ".dm_implode(', ', $colonnes).", code_client_ajout, usager_ajout_id, ts_ajout, code_ajout, note_ajout
		FROM `" . db::tableColonneValide($table) . "`
		WHERE ts_valide IS NOT NULL
		ORDER BY id";
$rows_master = $db->getAssoc($sql);

$sql = "SELECT id, ".dm_implode(', ', $colonnes).", code_client_ajout, usager_ajout_id, ts_ajout, code_ajout, note_ajout
		FROM `" . db::tableColonneValide($table) . "`
		WHERE ts_delete IS NOT NULL
		ORDER BY id";
$rows_master_to_delete = $db->getAssoc($sql);

$sql = "SELECT id, ".dm_implode(', ', $colonnes).", code_client_ajout, usager_ajout_id, ts_ajout, code_ajout, (select CONCAT(usagers.prenom, ' ', usagers.nom) from usagers where usagers.id = usager_ajout_id) as nom_usager, '".$table."' as table_updated, ts_sync
		FROM `" . db::tableColonneValide($table) . "`
		ORDER BY `" . db::tableColonneValide($table) . "`.id";
$rows_slave = $db_slave->getAssoc($sql);

foreach($rows_master_to_delete as $id => $row_master)
{
	if($table == 'procedures_entretiens')
	{
		$sql = "DELETE FROM procedures_entretiens_taches WHERE procedure_id = ?";
		$db->query($sql, array($id));
		
		$sql = "DELETE FROM procedures_entretiens_taches WHERE procedure_id = (select id from procedures_entretiens where code_ajout = ?)";
		$db_slave->query($sql, array($row_master['code_ajout']));
	}

	$sql = "DELETE FROM `" . db::tableColonneValide($table) . "` WHERE id = ?";
	$db->query($sql, array($id));
	
	$sql = "DELETE FROM `" . db::tableColonneValide($table) . "` WHERE code_ajout = ?";
	$db_slave->query($sql, array($row_master['code_ajout']));
	
	$notifications['delete'][$table][$id] = $row_master;
}

foreach($rows_master as $id => $row_master)
{
	if(isset($rows_slave[$id]))
	{
		$row_slave = $rows_slave[$id];
		
		$row_slave_tmp = keep_essentials($row_slave, $colonnes);
		$row_master_tmp = keep_essentials($row_master, $colonnes);
		
		if(uniqueKey(serialize($row_slave_tmp)) <> uniqueKey(serialize($row_master_tmp)))
		{
			$data = array();
			$fields = array();
		
			foreach($colonnes as $iter => $colonne)
			{
				$data[] = $row_master[$colonne];
				$fields[$colonne] = '?';
			}
			
			$data[] = $id;
			
			$sql = db::prepareQuery('update', $table, $fields, 'id = ?');
			$db_slave->query($sql, $data);
            
            $sql = "update $table set ts_sync = CURRENT_TIMESTAMP where id = ?";
			$db->query($sql, array($id));
			
			if($table == 'procedures_entretiens')
			{
				$sql = "select * from procedures_entretiens_taches where procedure_id = ?";
				$taches = $db->getAll($sql, array($id));
				
				$sql = "delete from procedures_entretiens_taches where procedure_id = ?";
				$db_slave->query($sql, array($id));
				
				foreach($taches as $i => $row)
				{
					$sql = "INSERT INTO procedures_entretiens_taches (nom, ordre, procedure_id, type_saisie) 
							VALUES (?, ?, ?, 'texte')";
					$db_slave->query($sql, array($row['nom'], $row['ordre'], $id));
				}
			}
			
			$notifications['update'][$table][$id] = $row_master;
		}
	}
	else
	{
		if(dm_strlen($row_master['code_ajout']) > 0)
		{
			$sql = "select count(*) as nb from `" . db::tableColonneValide($table) . "` WHERE code_ajout = ?";
			$row = $db_slave->getRow($sql, array($row_master['code_ajout']));
			
			if(intval($row['nb']) > 0)
			{
				if($table == 'procedures_entretiens')
				{					
					$sql = "DELETE FROM procedures_entretiens_taches WHERE procedure_id = (select id from procedures_entretiens where code_ajout = ?)";
					$db_slave->query($sql, array($row_master['code_ajout']));
				}
				
				$sql = "DELETE FROM `" . db::tableColonneValide($table) . "` WHERE code_ajout = ?";
				$db_slave->query($sql, array($row_master['code_ajout']));
				
				$notifications_client[$codeClient]['insert'][$table][$id] = $row_master;
			}
		}
		
		$data = array();
		$fields = array();
		
		$data[] = $id;
		$fields['id'] = '?';
		
		foreach($colonnes as $iter => $colonne)
		{
			$data[] = $row_master[$colonne];
			$fields[$colonne] = '?';
		}
		
		$data[] = date('Y-m-d h:i:s');
		$fields['ts_valide'] = '?';
		
		$sql = db::prepareQuery('insert', $table, $fields);
		$db_slave->query($sql, $data);
        
        $sql = "update $table set ts_sync = CURRENT_TIMESTAMP where id = ?";
        $db->query($sql, array($id));
			
		$notifications['insert'][$table][$id] = $row_master;
			
		if($table == 'procedures_entretiens')
		{
			$sql = "select * from procedures_entretiens_taches where procedure_id = ?";
			$taches = $db->getAll($sql, array($id));
			
			foreach($taches as $i => $row)
			{
				$sql = "INSERT INTO procedures_entretiens_taches (nom, ordre, procedure_id, type_saisie) 
						VALUES (?, ?, ?, 'texte')";
				$db_slave->query($sql, array($row['nom'], $row['ordre'], $id));
			}
		}
	}
	unset($rows_slave[$id]);
}

foreach($rows_slave as $id => $row_slave)
{
	if(isset($row_slave['code_ajout']))
	{
		$sql = "SELECT code_ajout FROM `" . db::tableColonneValide($table) . "` WHERE code_ajout = ?";
		$row_tmp = $db->getRow($sql, array($row_slave['code_ajout']));
		
		if(!isset($row_tmp['code_ajout']))
		{
			if(isset($row_slave['ts_sync']))
			{
				$sql = "delete from `" . db::tableColonneValide($table) . "` where id = ?";
				$db_slave->query($sql, array($id));
					
				$notifications_client[$codeClient]['delete'][$table][$id] = $row_slave;
			}
			else
			{
				$data = array();
				$fields = array();
				
				foreach($colonnes as $iter => $colonne)
				{
					$data[] = $row_slave[$colonne];
					$fields[$colonne] = '?';
				}
				
				$data[] = $row_slave['code_client_ajout'];
				$fields['code_client_ajout'] = '?';
				
				$data[] = $row_slave['usager_ajout_id'];
				$fields['usager_ajout_id'] = '?';
				
				$data[] = $row_slave['ts_ajout'];
				$fields['ts_ajout'] = '?';
				
				$data[] = $row_slave['code_ajout'];
				$fields['code_ajout'] = '?';
				
				$sql = db::prepareQuery('insert', $table, $fields);
				$db->query($sql, $data);
				$id_ajout = $db->lastInsertId();
        
                $sql = "update $table set ts_sync = CURRENT_TIMESTAMP where id = ?";
                $db_slave->query($sql, array($id));
				
				$requisitions[] = $row_slave;
				
				if($table == 'procedures_entretiens')
				{
					$sql = "select * from procedures_entretiens_taches where procedure_id = ?";
					$taches = $db_slave->getAll($sql, array($id));
					
					foreach($taches as $i => $row)
					{
						$sql = "INSERT INTO procedures_entretiens_taches (nom, ordre, procedure_id, type_saisie) 
								VALUES (?, ?, ?, 'texte')";
						$db->query($sql, array($row['nom'], $row['ordre'], $id_ajout));
					}
				}
			}
		}
	}
	else
	{
		$sql = "delete from `" . db::tableColonneValide($table) . "` where id = ?";
		$db_slave->query($sql, array($id));
			
		$notifications['delete'][$table][$id] = $row_slave;
	}
}
?>
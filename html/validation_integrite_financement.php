<?php

chdir(dirname(__file__));
$doNotValidIp = 1;
$doNotCheckLogin = 1;
ini_set("memory_limit","10000M");
ini_set('max_execution_time', 30000);
include('config/inc.php');

class Validation {
    private $db;
    private $validationRules = array();
    private $validationNames = array();
    private $numRules = 19;
    private $testMode = false;
    private $emailTo = '<EMAIL>';
    private $emailFrom = 'admin@localhost';
    
    public function __construct($db_name, $testMode = false) {
        $this->testMode = $testMode;
        if (!$testMode) {
            global $db_user, $db_pswd, $db_host;
            try {
                $this->db = new db($db_user, $db_pswd, $db_host, $db_name);
            } catch(Exception $e) {
                throw new Exception("Connection failed: " . $e->getMessage());
            }
        }
        $this->initializeValidationRules();
    }

    private function initializeValidationRules() {
        $rules = [
            // Règle 1
            ["SELECT CASE WHEN qte_requise >= qte_existante THEN 1 ELSE 0 END as valid 
              FROM equipements_bio_medicaux WHERE id = :id",
             "Conformité de la quantité requise vs existante"],
            
            // Règle 2
            ["SELECT CASE WHEN qte_existante >= qte_conservee THEN 1 ELSE 0 END as valid 
              FROM equipements_bio_medicaux WHERE id = :id",
             "Conformité de la quantité existante vs conservée"],
            
            // Règle 3
            ["SELECT CASE WHEN qte_requise >= qte_existante AND qte_existante >= qte_conservee THEN 1 ELSE 0 END as valid 
              FROM equipements_bio_medicaux WHERE id = :id",
             "Conformité des quantités dans la fiche"],
            
            // Règle 4
            ["SELECT CASE WHEN qte_developpement = qte_source1 THEN 1 ELSE 0 END as valid 
              FROM equipements_bio_medicaux WHERE id = :id",
             "Conformité du balancement des sources de financement (développement)"],
            
            // Règle 5
            ["SELECT CASE WHEN qte_remplacement = qte_source2 THEN 1 ELSE 0 END as valid 
              FROM equipements_bio_medicaux WHERE id = :id",
             "Conformité du balancement des sources de financement (remplacement)"],
            
            // Règle 6
            ["SELECT CASE WHEN qte_developpement = qte_source1 AND qte_remplacement = qte_source2 THEN 1 ELSE 0 END as valid 
              FROM equipements_bio_medicaux WHERE id = :id",
             "Conformité du balancement des sources de financement (développement et remplacement)"],
            
            // Règles 7-11
            ["SELECT CASE WHEN analyse_source1_qte_en_attente = 0 THEN 1 ELSE 0 END as valid 
              FROM equipements_bio_medicaux WHERE id = :id",
             "Conformité de suffisance des quantités approuvées (SF1)"],
            
            ["SELECT CASE WHEN analyse_source2_qte_en_attente = 0 THEN 1 ELSE 0 END as valid 
              FROM equipements_bio_medicaux WHERE id = :id",
             "Conformité de suffisance des quantités approuvées (SF2)"],
            
            ["SELECT CASE WHEN analyse_source3_qte_en_attente = 0 THEN 1 ELSE 0 END as valid 
              FROM equipements_bio_medicaux WHERE id = :id",
             "Conformité de suffisance des quantités approuvées (SF3)"],
            
            ["SELECT CASE WHEN analyse_source4_qte_en_attente = 0 THEN 1 ELSE 0 END as valid 
              FROM equipements_bio_medicaux WHERE id = :id",
             "Conformité de suffisance des quantités approuvées (SF4)"],
            
            ["SELECT CASE WHEN analyse_source5_qte_en_attente = 0 THEN 1 ELSE 0 END as valid 
              FROM equipements_bio_medicaux WHERE id = :id",
             "Conformité de suffisance des quantités approuvées (SF5)"],
            
            // Règle 12
            ["SELECT CASE WHEN analyse_source1_qte_en_attente = 0 
                       AND analyse_source2_qte_en_attente = 0 
                       AND analyse_source3_qte_en_attente = 0 
                       AND analyse_source4_qte_en_attente = 0 
                       AND analyse_source5_qte_en_attente = 0 THEN 1 ELSE 0 END as valid 
              FROM equipements_bio_medicaux WHERE id = :id",
             "Conformité de suffisance des quantités approuvées pour l'ensemble des sources de financement"],
            
            // Règles 13-17
            ["SELECT CASE WHEN qte_budgete_source1 = qte_source1 THEN 1 ELSE 0 END as valid 
              FROM equipements_bio_medicaux WHERE id = :id",
             "Conformité du balancement des quantités budgétés (SF1)"],
            
            ["SELECT CASE WHEN qte_budgete_source2 = qte_source2 THEN 1 ELSE 0 END as valid 
              FROM equipements_bio_medicaux WHERE id = :id",
             "Conformité du balancement des quantités budgétés (SF2)"],
            
            ["SELECT CASE WHEN qte_budgete_source3 = qte_source3 THEN 1 ELSE 0 END as valid 
              FROM equipements_bio_medicaux WHERE id = :id",
             "Conformité du balancement des quantités budgétés (SF3)"],
            
            ["SELECT CASE WHEN qte_budgete_source4 = qte_source4 THEN 1 ELSE 0 END as valid 
              FROM equipements_bio_medicaux WHERE id = :id",
             "Conformité du balancement des quantités budgétés (SF4)"],
            
            ["SELECT CASE WHEN qte_budgete_source5 = qte_source5 THEN 1 ELSE 0 END as valid 
              FROM equipements_bio_medicaux WHERE id = :id",
             "Conformité du balancement des quantités budgétés (SF5)"],
            
            // Règle 18 - Vérification des règles 13-17
            ["SELECT CASE WHEN 
                (SELECT CASE WHEN qte_budgete_source1 = qte_source1 THEN 1 ELSE 0 END) +
                (SELECT CASE WHEN qte_budgete_source2 = qte_source2 THEN 1 ELSE 0 END) +
                (SELECT CASE WHEN qte_budgete_source3 = qte_source3 THEN 1 ELSE 0 END) +
                (SELECT CASE WHEN qte_budgete_source4 = qte_source4 THEN 1 ELSE 0 END) +
                (SELECT CASE WHEN qte_budgete_source5 = qte_source5 THEN 1 ELSE 0 END) = 5 
                THEN 1 ELSE 0 END as valid 
              FROM equipements_bio_medicaux WHERE id = :id",
             "Conformité des quantités budgétés pour l'ensemble des sources de financement"],
            
            // Règle 19 - Validation finale
            ["SELECT CASE WHEN (
                SELECT COUNT(*) 
                FROM (
                    SELECT CASE WHEN qte_requise >= qte_existante THEN 1 ELSE 0 END +
                    CASE WHEN qte_existante >= qte_conservee THEN 1 ELSE 0 END +
                    CASE WHEN qte_requise >= qte_existante AND qte_existante >= qte_conservee THEN 1 ELSE 0 END +
                    CASE WHEN qte_developpement = qte_source1 THEN 1 ELSE 0 END +
                    CASE WHEN qte_remplacement = qte_source2 THEN 1 ELSE 0 END +
                    CASE WHEN qte_developpement = qte_source1 AND qte_remplacement = qte_source2 THEN 1 ELSE 0 END +
                    CASE WHEN analyse_source1_qte_en_attente = 0 THEN 1 ELSE 0 END +
                    CASE WHEN analyse_source2_qte_en_attente = 0 THEN 1 ELSE 0 END +
                    CASE WHEN analyse_source3_qte_en_attente = 0 THEN 1 ELSE 0 END +
                    CASE WHEN analyse_source4_qte_en_attente = 0 THEN 1 ELSE 0 END +
                    CASE WHEN analyse_source5_qte_en_attente = 0 THEN 1 ELSE 0 END +
                    CASE WHEN analyse_source1_qte_en_attente = 0 AND analyse_source2_qte_en_attente = 0 
                         AND analyse_source3_qte_en_attente = 0 AND analyse_source4_qte_en_attente = 0 
                         AND analyse_source5_qte_en_attente = 0 THEN 1 ELSE 0 END +
                    CASE WHEN qte_budgete_source1 = qte_source1 THEN 1 ELSE 0 END +
                    CASE WHEN qte_budgete_source2 = qte_source2 THEN 1 ELSE 0 END +
                    CASE WHEN qte_budgete_source3 = qte_source3 THEN 1 ELSE 0 END +
                    CASE WHEN qte_budgete_source4 = qte_source4 THEN 1 ELSE 0 END +
                    CASE WHEN qte_budgete_source5 = qte_source5 THEN 1 ELSE 0 END +
                    CASE WHEN (
                        CASE WHEN qte_budgete_source1 = qte_source1 THEN 1 ELSE 0 END +
                        CASE WHEN qte_budgete_source2 = qte_source2 THEN 1 ELSE 0 END +
                        CASE WHEN qte_budgete_source3 = qte_source3 THEN 1 ELSE 0 END +
                        CASE WHEN qte_budgete_source4 = qte_source4 THEN 1 ELSE 0 END +
                        CASE WHEN qte_budgete_source5 = qte_source5 THEN 1 ELSE 0 END
                    ) = 5 THEN 1 ELSE 0 END as total_valid
                    FROM equipements_bio_medicaux 
                    WHERE id = :id
                ) v
                WHERE v.total_valid = 18
            ) = 1 THEN 1 ELSE 0 END as valid 
            FROM equipements_bio_medicaux 
            WHERE id = :id",
             "Validité de la ligne"]
        ];

        foreach ($rules as $rule) {
            $this->validationRules[] = $rule[0];
            $this->validationNames[] = $rule[1];
        }
    }
    
    // Validate a single record
    private function validateRecord($recordId) {
        $validationString = '';
        
        if ($this->testMode) {
            // Mode test : génération de résultats aléatoires
            for ($i = 0; $i < $this->numRules; $i++) {
                $validationString .= rand(0, 1);
            }
        } else {
            foreach ($this->validationRules as $rule) {
                try {
                    $result = $this->db->getOne($rule, [':id' => $recordId]);
                    $validationString .= ($result && $result['valid'] == true) ? '1' : '0';
                } catch(Exception $e) {
                    error_log("Validation error for rule: " . $e->getMessage());
                    $validationString .= '0';
                }
            }
        }
        
        return $validationString;
    }
    
    // Process all records
    public function processAllRecords() {
        $reportContent = "Rapport de validation - " . date('Y-m-d H:i:s') . "\n\n";
        
        if ($this->testMode) {
            // En mode test, on simule 5 enregistrements
            $reportContent .= "MODE TEST - Simulation de 5 enregistrements:\n";
            for ($id = 1; $id <= 5; $id++) {
                $validationString = $this->validateRecord($id);
                $reportContent .= "\nEnregistrement #$id: $validationString\n";
                
                // Détail des validations
                $reportContent .= "Validations détaillées:\n";
                for ($i = 0; $i < strlen($validationString); $i++) {
                    $status = $validationString[$i] == '1' ? 'VALIDE' : 'NON VALIDE';
                    $reportContent .= "  " . ($i + 1) . ". " . $this->validationNames[$i] . ": $status\n";
                }
                $reportContent .= "\n";
            }
        } else {
            try {
                $results = $this->db->getAll("SELECT id FROM equipements_bio_medicaux", []);
                
                foreach ($results as $row) {
                    $validationString = $this->validateRecord($row['id']);
                    $reportContent .= "\nEnregistrement #" . $row['id'] . ": $validationString\n";
                    
                    // Détail des validations
                    $reportContent .= "Validations détaillées:\n";
                    for ($i = 0; $i < strlen($validationString); $i++) {
                        $status = $validationString[$i] == '1' ? 'VALIDE' : 'NON VALIDE';
                        $reportContent .= "  " . ($i + 1) . ". " . $this->validationNames[$i] . ": $status\n";
                    }
                }
                
            } catch(Exception $e) {
                throw new Exception("Processing failed: " . $e->getMessage());
            }
        }
        
        // Sauvegarder le rapport dans un fichier
        $filename = 'validation_report_' . date('Y-m-d_His') . '.txt';
        file_put_contents($filename, $reportContent);
        
        // Envoyer le rapport par email
        $headers = 'From: ' . $this->emailFrom . "\r\n" .
                  'Reply-To: ' . $this->emailFrom . "\r\n" .
                  'X-Mailer: PHP/' . phpversion();
        
        $subject = 'Rapport de validation - ' . date('Y-m-d H:i:s');
        
        if (mail($this->emailTo, $subject, $reportContent, $headers, '-f' . $this->emailFrom)) {
            echo "Rapport envoyé avec succès à " . $this->emailTo . "\n";
            echo "Une copie du rapport a été sauvegardée dans : $filename\n";
        } else {
            throw new Exception("Échec de l'envoi du rapport par email");
        }
        
        return true;
    }
    
    // Get the current validation rules
    public function getValidationRules() {
        return $this->validationRules;
    }
    
    // Get the current validation rule names
    public function getValidationRuleNames() {
        return $this->validationNames;
    }
    
    // Display validation rules with their names
    public function displayValidationRules() {
        echo "\nValidation Rules:\n";
        foreach ($this->validationNames as $index => $name) {
            echo ($index + 1) . ". " . $name . "\n";
        }
    }
    
    // Main method to run the validation
    public static function main() {
        try {
            global $Mapping_DB_Clients;
            foreach($Mapping_DB_Clients as $key => $datadb) {
                if ($key != 'master') {
                    $db_name = $datadb['db'];
                    $validation = new self($db_name, false);
                    $validation->processAllRecords();
                }
            }
        } catch(Exception $e) {
            file_put_contents(
                __DIR__ . '/validation_error.log',
                date('Y-m-d H:i:s') . " - Error: " . $e->getMessage() . "\n",
                FILE_APPEND
            );
            throw $e;
        }
    }
}

// Run the validation if this file is executed directly
if (basename(__FILE__) == basename($_SERVER['PHP_SELF'])) {
    Validation::main();
}

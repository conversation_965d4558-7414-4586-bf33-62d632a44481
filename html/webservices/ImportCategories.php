<?
header('Content-Type: application/json; charset=utf-8');

ini_set("memory_limit","10000M");
ini_set('max_execution_time', 300000);

$disable_sql_log = 1;
chdir('../');
$doNotCheckLogin=1;
include('config/inc.php');

if (!$isDev) {
	$headers = ApiUtils::getRequestHeaders();
	$api_key = $headers['API-KEY'] ?? 'no_api_key';
	$finger_print = $headers['FINGER-PRINT'] ?? 'no_finger_print';
	$droit_acces = ApiUtils::canAccessFromApiKey($api_key, $finger_print);
}

if($isDev || $droit_acces['acces']['can_access']) {
	if ($isDev) {
		$pathFile = '/var/www/temp/' . basename(__FILE__) . '.json';
		if (is_file($pathFile)) {
			$parsedJson = json_decode(file_get_contents($pathFile), true);
		}
		$Source = 'Revit';
	} else {
		$parsedJson = getParsedJsonStream();
		file_put_contents('/var/www/temp/' . basename(__FILE__) . '.json', json_encode($parsedJson));
		$ProjectGuid = strtolower($parsedJson['ProjectGuid']);
		$Source = $_GET['source'];
	}
	$Categories = $parsedJson['Categories'];
	unset($parsedJson);

	$db->autocommit(false);

	if ($Source == "Revit") {
		foreach ($Categories as $key => $Category) {
			$UniqueId = $Category['Id'] ?? '';
			$db->query("INSERT INTO master.rvt_categories (id, nom) VALUES (?, ?) 
								ON DUPLICATE KEY UPDATE nom = ?", array($UniqueId, unicode_decode($Category['Name']), unicode_decode($Category['Name'])));
			unset($Categories[$key]);
		}
	}

	$db->commit();
}

if (!$isDev) {
	print json_encode($droit_acces['acces']);
}

$ts_fin = microtime(true);
$total_duration = round($ts_fin - $ts_debut_all, 8);
file_put_contents('/var/www/temp/' . basename(__FILE__) . '.ts', $total_duration . 's');

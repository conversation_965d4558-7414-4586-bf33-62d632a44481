<?
header('Content-Type: application/json; charset=utf-8');

chdir('../');
$doNotCheckLogin=1;
include('config/inc.php');

$headers = ApiUtils::getRequestHeaders();
$api_key = $headers['API-KEY'] ?? 'no_api_key';
$finger_print = $headers['FINGER-PRINT'] ?? 'no_finger_print';

$droit_acces = ApiUtils::canAccessFromApiKey($api_key, $finger_print);

//mail('<EMAIL>', 'ExportDocmaticParameters.php', getDebug($droit_acces));
//mail('<EMAIL>', 'ExportDocmaticParameters.php', getDebug($droit_acces));

if($droit_acces['acces']['can_access'])
{
	$json = file_get_contents('php://input');

//	$json = '{
//				"ProjectGuid": "FAD29501458C2FF07BAD39963F82666C8D7E4BEB",
//				"Installations": [
//					{
//						"RevitUniqueId": "48617e39-5768-4ee3-8cfa-b0cf9331fc71-0010e444",
//						"TypeUniqueId": "17d64ad0-d78d-4987-a236-e3b1011abdd3-000f374a",
//						"Quantite": 11
//					},
//					{
//						"RevitUniqueId": "6b947320-bbcc-4f13-964d-b6403cf5dd5c-000ef796",
//						"TypeUniqueId": "88b03b05-80d3-484f-8022-471f61604c8d-00033c04",
//						"Quantite": 22
//					}
//				]
//			}';

	$parsedJson = json_decode($json, true);

	$ProjectGuid = strtolower($parsedJson['ProjectGuid']);

//	$ProjectGuid = 'a27c37281342fd16cccc124b40053b750558ca6f';

	$projet = Projets::getProjetFromProjectGuid($ProjectGuid);
	$_SESSION['projet_info'] = $projet;

	$ouvrage = new Ouvrages($projet->ouvrage_id);
	$_SESSION['ouvrage_info'] = $ouvrage;

    $usager = new Usager($droit_acces['usager_id']);
    $_SESSION['usager_' . Config::instance()->codeClient] = $usager;

	file_put_contents('/var/www/temp/' . basename(__FILE__) . '.json', json_encode($parsedJson));

	$typesAssocs = $db->getAssoc('select rvt_type_id, dm_formulaire_id, dm_liste_item_id from rvt_relations_installations', [], false, true);

	$db->autocommit(false);

	if (isset($parsedJson['Parameters']) && count($parsedJson['Parameters']) > 0) {
		foreach ($parsedJson['Parameters'] as $parameter) {
			if (!isset($typesAssocs[$parameter['TypeUniqueId']])) {
                $droit_acces['acces']['erreurs'][] = array(
                    'message' => txt('La mise à jour de la fiche ne peut être faite pour ce type d’installation car celle-ci n’a été associée à aucun élément de la librairie DocMatic. Veuillez d’abords effectuer l’association via le service WEB ou contacter l’administrateur BIM.', false)
                );
            } else {
				$typesAssoc = $typesAssocs[$parameter['TypeUniqueId']];
                $fiche_local_id = $db->getOne("select id from fiches where revit_unique_id = ? and ouvrage_id = ?", [$parameter['RevitUniqueId'], $ouvrage->id]);

                if ((int)$fiche_local_id === 0) {
                    if (!havePermission('creation')) {
                        $droit_acces['acces']['erreurs'][] = [
                            'message' => txt('Le local est inexistant dans DocMatic et vous n\'avez pas les droits de création de locaux dans ce projet.', false)
                        ];
                    } else {
                        $sql = "INSERT INTO `fiches` 
                        (`fiche_type_id`, `fiche_sous_type_id`, `code`, `revit_unique_id`, `ts_ajout`, `ouvrage_id`) 
                        VALUES (?, ?, ?, ?, current_timestamp, ?)";
                        $db->query($sql, [2000002, 2000002, $parameter['DocmaticRoomNumber'], $parameter['RevitUniqueId'], $ouvrage->id]);
                        $fiche_local_id = $db->lastInsertId();

                        $data_to_add = array();
                        $metas = GenerauxLocaux::getFlatChampsSaisieMetaData();
                        foreach($metas as $key => $meta) {
                            $data_to_add[$meta['champ_id']][0] = '';
                        }
                        $data_to_add['formulaire'] = 'GenerauxLocaux';
                        $data_to_add['fiche_id'] = $fiche_local_id;
                        $data_to_add['code'][0] = $parameter['DocmaticRoomNumber'];
                        $data_to_add['revit_unique_id'][0] = $parameter['RevitUniqueId'];
                        $data_to_add['fiche_type_id'][0] = 2000002;
                        $data_to_add['fiche_sous_type_id'][0] = 2000002;
                        $data_to_add['utilite'][0] = $parameter['DocmaticRoomName'] ?? '';
                        $data_to_add['is_importation'] = 1;
                        $data_to_add['raison'] = 'Synchronisation directe avec le modèle Revit.';
                        GenerauxLocaux::save($data_to_add, true);
                    }
                }

                $form = new Formulaires($typesAssoc['dm_formulaire_id']);
                $classForm = $form->nom;
                $first_champ = $form->getFirstChamp();

                if (!havePermission('importation')) {
                    $droit_acces['acces']['erreurs'][] = [
                        'message' => txt('Vous n\'avez pas les droits d\'importation dans ce projet.', false)
                    ];
                }

                $fiche = $fiche_local_id > 0 ? new Fiches($fiche_local_id) : null;
                if (isset($fiche) && dm_strlen($fiche->ts_delete) > 0) {
                    $droit_acces['acces']['erreurs'][] = [
                        'message' => txt('Ce local est dans la corbeille. Si vous désirez l\'éditer il faut le réactiver.', false)
                    ];
                }

                if (isset($fiche) && !isAutorizedFicheForStatut($fiche_local_id)) {
                    $droit_acces['acces']['erreurs'][] = [
                        'message' => txt('Vous n\'êtes pas autorisé à éditer les fiches de locaux ayant le statut ' . $fiche->fiche_statut, false)
                    ];
                }

                if (isListReadonlyProjet()) {
                    $droit_acces['acces']['erreurs'][] = [
                        'message' => txt('Ce projet est présentement en mode de conception contrôlé. Veuillez désactiver ce mode si vous désirez pouvoir importer les données directement de Revit.', false)
                    ];
                }

                $disciplines_auth = $usager->getDisciplinesAutorisees();
                if (!isset($disciplines_auth[$form->discipline_id])) {
                    $discipline = new Discipline($form->discipline_id);
                    $droit_acces['acces']['erreurs'][] = [
                        'message' => txt('Vous n\'êtes pas autorisé à éditer les formulaires de la discipline ' . $discipline->nom, false)
                    ];
                }

                if (!havePermission('edition_projet')) {
                    $droit_acces['acces']['erreurs'][] = [
                        'message' => txt('Vous n\'êtes pas autorisé à éditer des fiches de locaux dans ce projet.', false)
                    ];
                }

                $champs_readonly = $classForm::getChampsReadOnly();
                if (isset($champs_readonly['qte_requise'])) {
                    $droit_acces['acces']['erreurs'][] = [
                        'message' => txt('Le champ quantité est verrouillé pour ce formulaire.', false)
                    ];
                }

                if (count($droit_acces['acces']['erreurs']) == 0) {
                    $champ_id_name = $first_champ['champ_id'];
                    $champ_id_valeur = (int)$typesAssoc['dm_liste_item_id'];
                    $liste = $classForm::getListe(array('fiche_id' => $fiche_local_id));

                    $qte_requise = (int)$parameter['Quantite'];

                    if ($form->discipline_id > 0) {
                        $sql = "select count(*) as nb from fiches_disciplines where fiche_id = ? and discipline_id = ?";
                        $row_nb = $db->getRow($sql, array($fiche_local_id, $form->discipline_id));

                        if ($row_nb['nb'] == 0) {
                            $sql = "insert into fiches_disciplines (fiche_id, discipline_id) values(?, ?)";
                            $db->query($sql, array($fiche_local_id, $form->discipline_id));
                        }
                    }

                    $ajouter = true;
                    $data_to_save = array();
                    $iter = 0;
                    if (isset($liste) && count($liste) > 0) {
                        $sql = "SELECT `COLUMN_NAME`, `DATA_TYPE`
                            FROM `INFORMATION_SCHEMA`.`COLUMNS` 
                            WHERE `TABLE_SCHEMA`='$db_name' 
                                AND `TABLE_NAME`='" . $classForm::SQL_TABLE_NAME . "'
                                AND `COLUMN_NAME` not in ('id', 'fiche_id', 'no', 'ordre', 'statut', 'token_archive', 'ts_ajout', 'usager_ajout_id')
                                order by DATA_TYPE;";
                        $cols = $db->getAll($sql, array(), false, true);

                        foreach ($liste as $i => $obj) {
                            foreach ($cols as $j => $col) {
                                $col_name = $col['COLUMN_NAME'];
                                $data_to_save[$col['COLUMN_NAME']][$iter] = $obj->$col_name ?? '';
                            }
                            $data_to_save['id_ligne'][$iter] = $obj->id;
                            if ($obj->fiche_id == $fiche_local_id && $obj->$champ_id_name == $champ_id_valeur) {
                                if ($classForm == 'EquipementsBioMedicaux') {
                                    if (!in_array($champ_id_valeur, getAutorizedEquipementsGBM())) {
                                        $droit_acces['acces']['erreurs'][] = [
                                            'message' => txt('Vous n\'êtes pas autorisé à éditer cet équipements dans ce local car il appartient à une catégorie d\'équipement que vous n\'êtes pas autorisé à éditer.', false)
                                        ];
                                    }
                                    list($canInactivate, $equipement_non_editable, $canDelete) = EquipementsBioMedicaux::getDroitEditionEquipementStatic($obj, $champ_id_valeur, false);
                                    if ($equipement_non_editable) {
                                        $droit_acces['acces']['erreurs'][] = [
                                            'message' => txt('Vous n\'êtes pas autorisé à éditer cet équipements dans ce local car il appartient à un dépôt verrouillé.', false)
                                        ];
                                    }
                                }
                                $data_to_save['qte_requise'][$iter] = $qte_requise;
                                $ajouter = false;
                            }
                            $iter++;
                        }
                    }

                    if ($ajouter) {
                        if (!$classForm::canAddDeleteMove()) {
                            $droit_acces['acces']['erreurs'][] = [
                                'message' => txt('Vous n\'êtes pas autorisé à ajouter des équipements dans ce formulaire. (Module de limitation des formulaires)', false)
                            ];
                        } else {
                            $data_to_save[$champ_id_name][$iter] = $champ_id_valeur;
                            $data_to_save['qte_requise'][$iter] = $qte_requise;
                        }
                    }

                    if (count($droit_acces['acces']['erreurs']) == 0) {
                        $fiche_formulaire_commentaire = $classForm::getCommentaireFormulaire($fiche_local_id);
                        $fiche_formulaire_commentaire = $fiche_formulaire_commentaire ?? '';

                        $data_to_save['fiche_formulaire_commentaire'] = $fiche_formulaire_commentaire;

                        $data_to_save['fiche_id'] = $fiche_local_id;
                        $data_to_save['formulaire'] = $classForm;
                        $data_to_save['raison'] = 'Mise-à-jour des données via LinkMatic';
                        $classForm::save($data_to_save, true);
                    }
                }
			}
		}
	}
	$db->commit();

	print json_encode($droit_acces['acces']);

    file_put_contents('/var/www/temp/' . basename(__FILE__) . '_returned.json', json_encode($droit_acces['acces']));
}

$ts_fin = microtime(true);
$total_duration = round($ts_fin - $ts_debut_all, 8);
file_put_contents('/var/www/temp/' . basename(__FILE__) . '.ts', $total_duration . 's');

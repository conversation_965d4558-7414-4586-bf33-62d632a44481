<?
header('Content-Type: application/json; charset=utf-8');

ini_set("memory_limit","10000M");
ini_set('max_execution_time', 300000);

$disable_sql_log = 1;
chdir('../');
$doNotCheckLogin=1;
include('config/inc.php');

if (!$isDev) {
	$headers = ApiUtils::getRequestHeaders();
	$api_key = $headers['API-KEY'] ?? 'no_api_key';
	$finger_print = $headers['FINGER-PRINT'] ?? 'no_finger_print';
	$droit_acces = ApiUtils::canAccessFromApiKey($api_key, $finger_print);
}

if($isDev || $droit_acces['acces']['can_access']) {
	if ($isDev) {
		$pathFile = '/var/www/temp/' . basename(__FILE__) . '.json';
		if (is_file($pathFile)) {
			$parsedJson = json_decode(file_get_contents($pathFile), true);
		}
		$Source = 'Revit';
	} else {
		$parsedJson = getParsedJsonStream();
		file_put_contents('/var/www/temp/' . basename(__FILE__) . '.json', json_encode($parsedJson));
		$ProjectGuid = strtolower($parsedJson['ProjectGuid']);
		$Source = $_GET['source'];
	}

	$projet = Projets::getProjetFromProjectGuid($ProjectGuid);
	$Rooms = $parsedJson['Rooms'];
    unset($parsedJson);

	$db->autocommit(false);

	if ($Source == "Revit") {
		$ids_parametres_existants = $db->getAssoc("select id, nom FROM rvt_parametres", array(), false, true);
		$ids_existants = $db->getAssoc("select id, nom FROM rvt_locaux WHERE dm_projet_id = ?", array($projet->id), false, true);
		foreach ($Rooms as $key => $Room) {
			$Name = unicode_decode($Room['Name']);
			$Number = $Room['Number'];
			$Area = $Room['Area'];
			$Volume = $Room['Volume'];
			$Level = $Room['Level'];
			$UniqueId = $Room['UniqueId'] ?? '';

			$db->query(
				"INSERT INTO rvt_locaux (id, dm_projet_id, nom, numero, superficie, volume, niveau) VALUES (?, ?, ?, ?, ?, ?, ?)
						ON DUPLICATE KEY UPDATE nom = ?, numero = ?, superficie = ?, volume = ?, niveau = ?",
						array($UniqueId, $projet->id, $Name, $Number, $Area, $Volume, $Level, $Name, $Number, $Area, $Volume, $Level)
			);

			if (isset($Room['ParameterValues']) && is_array($Room['ParameterValues']) && count($Room['ParameterValues']) > 0) {
				foreach ($Room['ParameterValues'] as $ParameterValue) {
					$ParameterId = $ParameterValue['Id'];
					if (isset($ids_parametres_existants[$ParameterId])) {
						$Value = unicode_decode($ParameterValue['Value']);

						if (isset($Value)) {
							$db->query(
								"INSERT INTO rvt_valeurs_paremetres_locaux (rvt_parametre_id, rvt_local_id, valeur) VALUES (?, ?, ?) 
										ON DUPLICATE KEY UPDATE valeur = ?",
								array($ParameterId, $UniqueId, $Value, $Value));
						}
					}
				}
			}
			unset($Rooms[$key]);
			unset($ids_existants[$UniqueId]);
		}
		if (count($ids_existants) > 0) {
			$db->query("DELETE FROM rvt_locaux WHERE id in (" . db::placeHolders($ids_existants) . ")", array_keys($ids_existants));
		}
	}

	$db->commit();
}

if (!$isDev) {
	print json_encode($droit_acces['acces']);
}

$ts_fin = microtime(true);
$total_duration = round($ts_fin - $ts_debut_all, 8);
file_put_contents('/var/www/temp/' . basename(__FILE__) . '.ts', $total_duration . 's');
